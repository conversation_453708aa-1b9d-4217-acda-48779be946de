/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-20 13:34:07
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-09 18:47:30
 */
import { BatchReturnOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import {
  BatchRefundTradeStatusEnum,
  BatchRefundTradeTypeEnum,
  BatchRefundTradeWayEnum
} from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
import { BatchReturnOrderRecordVo } from './BatchReturnOrderRecordVo'
import UserModule from '@api/service/management/user/UserModule'
import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
import StudentTrialListenCourseScene from '@api/service/customer/learning/scene/StudentTrialListenCourseScene'
class BatchRefoundDetailVo {
  /**
   * 姓名
   */
  name: string
  /**
   * 账号
   */
  idCard: string
  /**
   * 退款人数
   */
  refoundCount: number
  /**
   * 交易流水号
   */
  flowNo: string
  /**
   * 批次单号
   */
  batchOrderNo: string
  /**
   * 批次退货单状态
   */
  refoundStatus: number
  /**
   * 退款类型
   */
  refoundType: BatchRefundTradeTypeEnum
  /**
   * 退款单号
   */
  refoundNo: string
  /**
   * 退款金额
   */
  refoundAmount: number
  /**
   * 退款方式
   */
  refoundWay: BatchRefundTradeWayEnum
  /**
   * 退款原因
   */
  refoundReason: string
  /**
   * 退款说明
   */
  refoundExplain: string
  /**
   * 退款申请时间
   */
  refoundDate: string
  /**
   * 审批时间
   */
  approvalDate: string
  /**
   * 退款成功时间
   */
  refoundSuccessDate: string
  /**
   * 操作记录
   */
  refundRecord: BatchReturnOrderRecordVo[] = []
  /**
   * UI使用
   */
  UIReturnOrderStatue: BatchRefundTradeStatusEnum
  /**
   * 谁拒绝的 （（1：卖家取消 2：卖家拒绝退货 3：买家取消 4：确认失败取消））
   */
  closeType: number
  /**
   * 申请人的ID
   */
  applyUserId: string
  /**
   * 审核人
   */
  approveUserId: string
  /**
   * 审核结果
   */
  approveResult: number
  /**
   * 审核意见
   */
  approveComment: string
  /**
   * 取消人
   */
  cancelUserId: string
  /**
   * 取消原因
   */
  cancelReason: string
  /**
   * 取消时间
   */
  cancelDate: string
  /**
   * 退货完成时间
   */
  returnedDate: string
  /**
   * 退款完成时间
   */
  refundedDate: string
  /**
   * 退货退款完成时间
   */
  returnedAndRefundedDate: string
  static from(batchReturnOrderResponse: BatchReturnOrderResponse) {
    const batchRefoundDetailVo = new BatchRefoundDetailVo()
    batchRefoundDetailVo.batchOrderNo = batchReturnOrderResponse.batchOrderInfo?.batchOrderNo
    batchRefoundDetailVo.refoundStatus = batchReturnOrderResponse.basicData?.batchReturnOrderStatus
    batchRefoundDetailVo.refoundNo = batchReturnOrderResponse.batchReturnOrderNo
    batchRefoundDetailVo.refoundAmount = batchReturnOrderResponse.basicData?.refundAmount
    batchRefoundDetailVo.refoundWay = batchReturnOrderResponse.refundInfo?.refundOrderType
    batchRefoundDetailVo.refoundReason = batchReturnOrderResponse.basicData?.applyInfo?.reasonContent
    batchRefoundDetailVo.refoundExplain = batchReturnOrderResponse.basicData?.applyInfo?.description
    batchRefoundDetailVo.refoundDate = batchReturnOrderResponse.basicData?.batchReturnOrderStatusChangeTime?.confirmed
    batchRefoundDetailVo.cancelDate = batchReturnOrderResponse.basicData?.batchReturnOrderStatusChangeTime?.closed
    batchRefoundDetailVo.returnedDate = batchReturnOrderResponse.basicData?.batchReturnOrderStatusChangeTime?.returned
    batchRefoundDetailVo.refundedDate = batchReturnOrderResponse.basicData?.batchReturnOrderStatusChangeTime?.refunded
    batchRefoundDetailVo.returnedAndRefundedDate =
      batchReturnOrderResponse.basicData?.batchReturnOrderStatusChangeTime?.returnedAndRefunded
    batchRefoundDetailVo.approvalDate = batchReturnOrderResponse.approvalInfo?.approveTime
    batchRefoundDetailVo.approveResult = batchReturnOrderResponse.approvalInfo?.approveResult
    batchRefoundDetailVo.approveComment = batchReturnOrderResponse.approvalInfo?.approveComment
    batchRefoundDetailVo.refoundType = batchReturnOrderResponse.basicData?.batchReturnOrderType
    batchRefoundDetailVo.refoundSuccessDate =
      batchReturnOrderResponse.basicData?.batchReturnOrderStatusChangeTime?.returnCompleted
    batchRefoundDetailVo.closeType = batchReturnOrderResponse.basicData?.closeReason?.closeType
    batchRefoundDetailVo.applyUserId = batchReturnOrderResponse.basicData?.applyInfo?.applyUser.userId
    batchRefoundDetailVo.cancelUserId = batchReturnOrderResponse.basicData?.closeReason?.cancelUser?.userId
    batchRefoundDetailVo.cancelReason = batchReturnOrderResponse.basicData?.closeReason?.cancelReason
    batchRefoundDetailVo.approveUserId = batchReturnOrderResponse.approvalInfo?.approveUser?.userId
    batchRefoundDetailVo.refoundCount = batchReturnOrderResponse.basicData?.returnOrderCount
    batchRefoundDetailVo.flowNo = batchReturnOrderResponse.batchOrderInfo?.paymentInfo?.flowNo
    // 借鉴锦哥状态转换
    const approveStates = [0, 1, 2],
      handlingStates = [3, 4, 5, 6, 7],
      successStatues = [9, 10, 11]
    if (approveStates.indexOf(batchRefoundDetailVo.refoundStatus) != -1) {
      batchRefoundDetailVo.UIReturnOrderStatue = BatchRefundTradeStatusEnum.REFUNDING
    } else if (handlingStates.indexOf(batchRefoundDetailVo.refoundStatus) != -1) {
      batchRefoundDetailVo.UIReturnOrderStatue = BatchRefundTradeStatusEnum.REFUNDDISPOSE
    } else if (successStatues.indexOf(batchRefoundDetailVo.refoundStatus) != -1) {
      batchRefoundDetailVo.UIReturnOrderStatue = BatchRefundTradeStatusEnum.REFUNDSUCCESS
    } else if (batchRefoundDetailVo.refoundStatus == 12 && batchRefoundDetailVo.closeType == 2) {
      batchRefoundDetailVo.UIReturnOrderStatue = BatchRefundTradeStatusEnum.REFUSEDREFUND
    } else if (batchRefoundDetailVo.refoundStatus == 12 && [1, 3, 4].indexOf(batchRefoundDetailVo.closeType) != -1) {
      batchRefoundDetailVo.UIReturnOrderStatue = BatchRefundTradeStatusEnum.CANCELREFUND
    } else if (batchRefoundDetailVo.refoundStatus == 8) {
      batchRefoundDetailVo.UIReturnOrderStatue = BatchRefundTradeStatusEnum.REFUNDFAIL
    }
    return batchRefoundDetailVo
  }
  async fillRecords() {
    /**
     * 4种情况
     * 只发起退款
     * 发起退款 - 同意退款 - 退款成功
     * 发起退款 - 拒绝退款
     * 发起退款 - 取消退款
     */
    if (
      this.UIReturnOrderStatue == BatchRefundTradeStatusEnum.REFUNDING ||
      this.UIReturnOrderStatue == BatchRefundTradeStatusEnum.REFUNDDISPOSE
    ) {
      // 发起退款
      await this.addHandleRecord()
      if (this.approveResult === 1) {
        await this.addApprovalRecord()
      }
    } else if (this.UIReturnOrderStatue == BatchRefundTradeStatusEnum.REFUSEDREFUND) {
      // 拒绝退款
      await this.addHandleRecord()
      await this.addRefuseRecord()
    } else if (this.UIReturnOrderStatue == BatchRefundTradeStatusEnum.CANCELREFUND) {
      //取消退款
      await this.addHandleRecord()
      await this.addCancelRecord()
    } else if (this.UIReturnOrderStatue == BatchRefundTradeStatusEnum.REFUNDSUCCESS) {
      // 退款成功

      await this.addHandleRecord()
      await this.addApprovalRecord()
      await this.addSuccessRecord()
    }
  }
  /**
   * 发起退款记录
   */
  private async addHandleRecord() {
    const record = new BatchReturnOrderRecordVo()
    const user = await this.getUserDetail(this.applyUserId)
    record.name = user.userName
    record.time = this.refoundDate
    record.UIReturnOrderStatue = BatchRefundTradeStatusEnum.REFUNDING
    record.tipMsg = this.refoundExplain
    record.money = this.refoundAmount
    this.refundRecord.push(record)
  }
  /**
   * 取消退款
   */
  private async addCancelRecord() {
    const record = new BatchReturnOrderRecordVo()
    const user = await this.getUserDetail(this.cancelUserId)
    record.name = user.userName
    record.time = this.cancelDate
    record.UIReturnOrderStatue = BatchRefundTradeStatusEnum.CANCELREFUND
    record.tipMsg = this.cancelReason
    this.refundRecord.push(record)
  }
  /**
   * 审批记录
   */
  private async addApprovalRecord() {
    const record = new BatchReturnOrderRecordVo()
    const user = await this.getUserDetail(this.approveUserId)
    record.name = user.userName
    record.time = this.approvalDate
    record.UIReturnOrderStatue = BatchRefundTradeStatusEnum.REFUNDDISPOSE
    record.tipMsg = this.approveComment
    this.refundRecord.push(record)
  }
  /**
   * 拒绝退款
   */
  private async addRefuseRecord() {
    //   因为用户模块还没有。所以这里暂时用用户id代替名字
    const record = new BatchReturnOrderRecordVo()
    const user = await this.getUserDetail(this.approveUserId)
    record.name = user.userName
    record.time = this.approvalDate
    record.UIReturnOrderStatue = BatchRefundTradeStatusEnum.REFUSEDREFUND
    record.tipMsg = this.approveComment
    this.refundRecord.push(record)
  }
  /**
   * 同意退款
   */
  private async addSuccessRecord() {
    const record = new BatchReturnOrderRecordVo()
    const user = await this.getUserDetail(this.approveUserId)
    record.name = user.userName
    const map = {
      '1': this.returnedDate,
      '2': this.refundedDate,
      '3': this.returnedAndRefundedDate
    }
    record.time = map[this.refoundType]
    record.UIReturnOrderStatue = BatchRefundTradeStatusEnum.REFUNDSUCCESS
    this.refundRecord.push(record)
  }
  private async getUserDetail(userId: string) {
    const queryUser = UserModule.queryUserFactory.queryStudentDetail(userId)
    const data = await queryUser.queryDetail()
    if (data.status.isSuccess()) {
      return data.data
    }
    return new UserDetailVo()
  }
}

export default BatchRefoundDetailVo
