<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <!--条件查询-->
      <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
      <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
      <hb-search-wrapper :model="deliveryInvoiceParam" :responsiveProps="{ sm: 14, md: 10, xl: 6 }" @reset="restQuery">
        <!-- <el-form :inline="true" label-width="auto"> -->
        <el-form-item label="配送状态">
          <el-select v-model="deliveryInvoiceParam.deliveryStatus" filterable placeholder="请选择">
            <el-option
              v-for="item in deliveryStatusList"
              :label="item.name"
              :value="item.value"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="登录账号" v-if="queryShowLoginAccount.isShowLoginAccount">
          <el-input v-model="deliveryInvoiceParam.loginAccount" clearable placeholder="请输入省平台id" />
        </el-form-item>
        <el-form-item label="收件人姓名">
          <el-input v-model="deliveryInvoiceParam.name" clearable placeholder="请输入收件人姓名" />
        </el-form-item>
        <el-form-item label="证件号">
          <el-input v-model="deliveryInvoiceParam.idCard" clearable placeholder="请输入证件号" />
        </el-form-item>
        <el-form-item label="订单号">
          <el-input v-model="deliveryInvoiceParam.invoiceNo" clearable placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="状态更新时间">
          <double-date-picker
            :begin-create-time.sync="deliveryInvoiceParam.startDate"
            :end-create-time.sync="deliveryInvoiceParam.endDate"
            begin-time-placeholder="起始时间"
            end-time-placeholder="结束时间"
          ></double-date-picker>
        </el-form-item>
        <el-form-item label="配送方式">
          <el-select v-model="deliveryInvoiceParam.deliveryWay" clearable filterable placeholder="请选择">
            <el-option
              v-for="item in deliveryWayList"
              :label="item.name"
              :value="item.value"
              :key="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="运单号">
          <el-input v-model="deliveryInvoiceParam.theAwb" clearable placeholder="请输入运单号" />
        </el-form-item>
        <el-form-item label="领取人">
          <el-input v-model="deliveryInvoiceParam.recipient" clearable placeholder="请输入领取人姓名" />
        </el-form-item>
        <el-form-item label="发票冻结状态">
          <el-select v-model="deliveryInvoiceParam.frozenState" clearable filterable placeholder="请选择">
            <el-option label="是" :value="true"></el-option>
            <el-option label="否" :value="false"></el-option>
          </el-select>
        </el-form-item>
        <template slot="actions">
          <el-button type="primary" @click="initRequest">查询</el-button>
          <el-button v-loading="exportLoading" @click="exportFile">导出包裹信息</el-button>
          <el-button @click="importFile" v-if="!isZtlogin">导入包裹运单</el-button>
        </template>
        <!-- </el-form> -->
      </hb-search-wrapper>
      <!--表格-->
      <el-table stripe :data="tableData" max-height="500px" class="m-table" v-loading="loading">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="订单号" min-width="220" fixed="left">
          <template slot-scope="scope"
            >{{ scope.row.associationId }}
            <p>
              <el-tag type="warning" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.distribution"
                >分销推广
              </el-tag>
            </p>
          </template>
        </el-table-column>
        <el-table-column label="发票号" min-width="120">
          <template slot-scope="scope">{{ scope.row.invoiceNo }}</template>
        </el-table-column>
        <el-table-column label="收件信息" min-width="400">
          <template slot-scope="scope">
            <p class="f-flex" v-if="scope.row.deliveryInfo.shippingMethod === 2">
              <span>收货地址：</span>
              <span class="f-flex-sub">{{
                scope.row.deliveryInfo.deliveryAddress ? getRegionName(scope.row) : ''
              }}</span>
            </p>
            <p v-if="scope.row.deliveryInfo.shippingMethod === 2">
              收货人：{{
                scope.row.deliveryInfo.deliveryAddress ? scope.row.deliveryInfo.deliveryAddress.consignee : ''
              }}
            </p>
            <p v-if="scope.row.deliveryInfo.shippingMethod === 1">购买人：{{ scope.row.name }}</p>
            <p v-if="queryShowLoginAccount.isShowLoginAccount">登录账号：{{ scope.row.loginAccount }}</p>
            <p v-if="scope.row.deliveryInfo.shippingMethod === 1">身份证号：{{ scope.row.idCard }}</p>
            <p>
              手机号: {{ scope.row.deliveryInfo.deliveryAddress ? scope.row.deliveryInfo.deliveryAddress.phone : '' }}
            </p>
            <p v-if="scope.row.deliveryInfo.shippingMethod === 1">
              自取地址: {{ scope.row.deliveryInfo.takePoint ? scope.row.deliveryInfo.takePoint.pickupLocation : '' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="配送方式" width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.deliveryInfo.shippingMethod === 0">-</div>
            <div v-if="scope.row.deliveryInfo.shippingMethod === 1">自取</div>
            <div v-if="scope.row.deliveryInfo.shippingMethod === 2">邮寄</div>
          </template>
        </el-table-column>
        <el-table-column label="配送状态" min-width="130">
          <template slot-scope="scope">
            <div v-if="scope.row.deliveryInfo.deliveryStatus === 1">
              <el-badge is-dot type="primary" class="badge-status">就绪</el-badge>
            </div>
            <div v-if="scope.row.deliveryInfo.deliveryStatus === 0">
              <el-badge is-dot type="info" class="badge-status">未就绪</el-badge>
            </div>
            <div v-if="scope.row.deliveryInfo.deliveryStatus === 3 || scope.row.deliveryInfo.deliveryStatus === 2">
              <el-badge is-dot type="success" class="badge-status">已配送</el-badge>
            </div>
            <!-- <div v-if="scope.row.deliveryInfo.deliveryStatus === 2">
              <el-badge is-dot type="success" class="badge-status">已自取</el-badge>
            </div> -->
          </template>
        </el-table-column>
        <el-table-column label="配送信息 / 取件信息" min-width="260">
          <template slot-scope="scope">
            <div v-if="scope.row.deliveryInfo.shippingMethod === 1 && scope.row.deliveryInfo.deliveryStatus === 2">
              <p>领取人: {{ scope.row.deliveryInfo.takeResult.takePerson }}</p>
              <p>手机号: {{ scope.row.deliveryInfo.takeResult.phone }}</p>
              <p>取货时间: {{ scope.row.taken }}</p>
            </div>
            <div v-if="scope.row.deliveryInfo.shippingMethod === 2 && scope.row.deliveryInfo.deliveryStatus === 3">
              <p>
                快递公司: {{ scope.row.deliveryInfo.express ? scope.row.deliveryInfo.express.expressCompanyName : '' }}
              </p>
              <p>运单号: {{ scope.row.deliveryInfo.express ? scope.row.deliveryInfo.express.expressNo : '' }}</p>
              <p>发货时间: {{ scope.row.shipped }}</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="是否冻结" min-width="120">
          <template slot-scope="scope">{{ scope.row.invoiceFreezeStatus ? '是' : '否' }}</template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              v-if="
                scope.row.deliveryInfo.shippingMethod === 1 &&
                !scope.row.invoiceFreezeStatus &&
                scope.row.deliveryInfo.deliveryStatus != 0 &&
                scope.row.deliveryInfo.deliveryStatus != 2 &&
                !isZtlogin
              "
              @click="isShowDeliveryDialog(scope.row, 1)"
              >确认自取</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-if="
                scope.row.deliveryInfo.shippingMethod === 2 &&
                !scope.row.invoiceFreezeStatus &&
                scope.row.deliveryInfo.deliveryStatus != 0 &&
                scope.row.deliveryInfo.deliveryStatus != 3 &&
                !isZtlogin
              "
              @click="isShowDeliveryDialog(scope.row, 2)"
              >确认配送</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-if="scope.row.invoiceFreezeStatus || scope.row.deliveryInfo.deliveryStatus === 0"
              >-</el-button
            >
            <el-button
              type="text"
              size="mini"
              class="fontColor"
              v-if="scope.row.deliveryInfo.deliveryStatus === 3 && !scope.row.invoiceFreezeStatus"
              >已配送</el-button
            >
            <el-button
              type="text"
              size="mini"
              class="fontColor"
              v-if="scope.row.deliveryInfo.deliveryStatus === 2 && !scope.row.invoiceFreezeStatus"
              >已自取</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-if="
                (scope.row.deliveryInfo.deliveryStatus === 2 || scope.row.deliveryInfo.deliveryStatus === 3) &&
                !scope.row.invoiceFreezeStatus &&
                !isZtlogin
              "
              @click="deliveryLogClick(scope.row)"
              >配送记录</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </el-card>
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务管理-个人报名增值税电子普通发票（自动开票）</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="importSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导入成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导入任务查看-个人报名增值税电子普通发票（线下开票）</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="importSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goImportDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
    <delivery-dialog
      :dialogType="dialogType"
      @confirmOrder="confirmOrder"
      @confirmDeliveryOrder="confirmDeliveryOrder"
      ref="deliveryDialog"
    ></delivery-dialog>
    <import-parcel-waybill
      ref="importParcelWaybill"
      :import-dialog.sync="importInvoiceVisible"
      @importSuccess="importSuccessVisible = true"
    ></import-parcel-waybill>
    <delivery-log ref="deliveryLog" :deliveryRecordList="deliveryRecordList"></delivery-log>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { UiPage } from '@hbfe/common'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import DeliveryInvoiceParamVo from '@api/service/management/trade/single/invoice/query/vo/DeliveryInvoiceParam'
  import {
    DeliveryStatusEnum,
    DeliveryWayEnum
  } from '@api/service/management/trade/single/invoice/enum/DeliveryInvoiceEnum'
  import DeliveryDialog from '@hbfe/jxjy-admin-components/src/trade/personal/delivery-dialog.vue'
  import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import ImportParcelWaybill from '@hbfe/jxjy-admin-components/src/trade/personal/import-parcel-waybill.vue'
  import DeliveryLog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/delivery-log.vue'
  import { OfflineDeliveryRecord } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import QueryDeliveryRegion from '@api/service/common/basic-data-dictionary/query/QueryDeliveryRegion'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import { RegionResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryDeliveryInvoiceInTrainingChannel from '@api/service/management/trade/single/invoice/query/QueryDeliveryInvoiceInTrainingChannel'
  @Component({
    components: { DoubleDatePicker, DeliveryDialog, ImportParcelWaybill, DeliveryLog }
  })
  export default class extends Vue {
    @Ref('deliveryDialog') deliveryDialog: DeliveryDialog
    @Ref('importParcelWaybill') importParcelWaybill: ImportParcelWaybill
    @Ref('deliveryLog') deliveryLog: DeliveryLog
    select = [{}]
    // 初始地区树
    regionOptions: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    // 发票配送查询实例
    queryDeliveryInvoiceVo = TradeModule.singleTradeBatchFactor.invoiceFactor.queryDeliveryInvoice
    // 发票配送业务实例
    mutationDeliveryInvoiceVo = TradeModule.singleTradeBatchFactor.invoiceFactor.mutationDeliveryInvoice
    loading = false
    deliveryInvoiceParam = new DeliveryInvoiceParamVo()
    // 弹窗类型
    dialogType = 0
    // 选中的发票详情
    checkedItemDetail = new OffLinePageInvoiceVo()
    //导出成功弹窗
    exportSuccessVisible = false
    //导入弹窗
    importInvoiceVisible = false
    //导入成功弹窗
    importSuccessVisible = false
    // 配送状态
    deliveryStatusList = [
      { name: '未就绪', value: DeliveryStatusEnum.NOTREADY },
      { name: '就绪', value: DeliveryStatusEnum.READY },
      { name: '已配送', value: DeliveryStatusEnum.DELIVERED }
    ]
    // 配送状态
    deliveryWayList = [
      { name: '快递', value: DeliveryWayEnum.COURIER },
      { name: '自取', value: DeliveryWayEnum.SELFFETCHED }
    ]
    deliveryWay = DeliveryWayEnum
    tableData = new Array<OffLinePageInvoiceVo>()
    // 发票配送记录
    deliveryRecordList = new Array<OfflineDeliveryRecord>()
    // 导出loading
    exportLoading = false
    regionList: Array<RegionResponse> = new Array<RegionResponse>()
    SaleChannelEnum = SaleChannelEnum
    page: UiPage
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    queryZtInvoice = new QueryDeliveryInvoiceInTrainingChannel()
    constructor() {
      super()
      if (this.isZtlogin) {
        this.page = new UiPage(this.doQueryPageZt, this.doQueryPageZt)
      } else {
        this.page = new UiPage(this.doQueryPage, this.doQueryPage)
      }
    }
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    getRegionName(item: OffLinePageInvoiceVo) {
      if (item.deliveryInfo?.deliveryAddress?.region) {
        const regionNameList = new Array<string>()
        item.deliveryInfo?.deliveryAddress?.region?.split('/').map((ite) => {
          const regionName = this.regionList?.find((it) => it.code === ite)
          if (regionName) regionNameList.push(regionName.name)
        })
        return regionNameList.join('') + item.deliveryInfo?.deliveryAddress?.address
      }
      return item.deliveryInfo?.deliveryAddress?.address || ''
    }
    // 页面初始化调用的请求
    async initRequest() {
      this.page.pageNo = 1
      if (this.isZtlogin) {
        await this.doQueryPageZt()
      } else {
        await this.doQueryPage()
      }
    }
    // 列表请求
    async doQueryPage() {
      try {
        this.loading = true
        this.tableData = await this.queryDeliveryInvoiceVo.queryPageDeliveryInvoice(
          this.page,
          this.deliveryInvoiceParam
        )
        const regionIdList = new Array<string>()
        this.tableData.map((item) => {
          if (item?.deliveryInfo?.deliveryAddress?.region)
            regionIdList.push(...item.deliveryInfo.deliveryAddress?.region.split('/').filter((val) => val))
        })

        this.regionList =
          (await QueryDeliveryRegion.querRegionDetil([...new Set(regionIdList)])) || new Array<RegionResponse>()
        console.log(this.tableData, this.regionList, 'this.tableData')
      } catch (e) {
        this.$message.error('请求列表失败！')
        console.log(e)
      } finally {
        this.loading = false
      }
    }
    // 列表请求
    async doQueryPageZt() {
      try {
        this.loading = true
        this.tableData = await this.queryZtInvoice.queryPageDeliveryInvoice(this.page, this.deliveryInvoiceParam)
        const regionIdList = new Array<string>()
        this.tableData.map((item) => {
          if (item?.deliveryInfo?.deliveryAddress?.region)
            regionIdList.push(...item.deliveryInfo.deliveryAddress?.region.split('/').filter((val) => val))
        })

        this.regionList =
          (await QueryDeliveryRegion.querRegionDetil([...new Set(regionIdList)])) || new Array<RegionResponse>()
        console.log(this.tableData, this.regionList, 'this.tableData')
      } catch (e) {
        this.$message.error('请求列表失败！')
        console.log(e)
      } finally {
        this.loading = false
      }
    }
    // 重置条件
    async restQuery() {
      this.deliveryInvoiceParam = new DeliveryInvoiceParamVo()
      this.initRequest()
    }

    // 是否显示弹窗
    isShowDeliveryDialog(item: any, type: number) {
      this.checkedItemDetail = item
      this.dialogType = type
      this.deliveryDialog.isShowDialog()
    }

    // 确认自取
    async confirmOrder(item: any) {
      console.log(item, 'item')
      item.offlineInvoiceId = this.checkedItemDetail.invoiceId
      const res = await this.mutationDeliveryInvoiceVo.confirmStatus(item, this.deliveryWay.SELFFETCHED)
      if (res.isSuccess()) {
        this.$message.success('确认自取成功')
        this.deliveryDialog.isShowDialog()
        this.initRequest()
      } else {
        this.$message.warning('请求失败！')
      }
    }

    // 确认配送
    async confirmDeliveryOrder(item: any) {
      console.log(item, 'item')
      item.offlineInvoiceId = this.checkedItemDetail.invoiceId
      const res = await this.mutationDeliveryInvoiceVo.confirmStatus(item, this.deliveryWay.COURIER)
      if (res.isSuccess()) {
        this.$message.success('确认配送成功')
        this.deliveryDialog.isShowDialog()
        this.initRequest()
      } else {
        this.$message.warning('请求失败！')
      }
    }

    async created() {
      this.deliveryInvoiceParam.deliveryStatus = DeliveryStatusEnum.READY
      if (this.isZtlogin) {
        await this.doQueryPageZt()
      } else {
        await this.doQueryPage()
      }
    }

    // 前往下载
    goDownloadPage() {
      this.$router.push({ path: '/training/task/exporttask', query: { type: 'exportInvoiceDelivery' } })
      this.exportSuccessVisible = false
    }

    // 导出信息
    async exportFile() {
      this.exportLoading = true
      let res
      if (this.isZtlogin) {
        res = await this.queryZtInvoice.exportPageDeliveryInvoice(this.deliveryInvoiceParam)
      } else {
        res = await this.queryDeliveryInvoiceVo.exportPageDeliveryInvoice(this.deliveryInvoiceParam)
      }
      if (res) {
        this.$message.success('导出成功')
        this.exportSuccessVisible = true
      } else {
        this.$message.warning('导出失败')
      }
      this.exportLoading = false
    }

    // 导入
    importFile() {
      this.importInvoiceVisible = true
    }

    // 下载导入数据
    goImportDownloadPage() {
      this.importSuccessVisible = false
      this.$router.push({ path: '/training/task/importtask', query: { type: '个人报名发票配送' } })
    }

    // 配送记录
    async deliveryLogClick(item: OffLinePageInvoiceVo) {
      this.deliveryRecordList = item.deliveryRecordList
      this.deliveryLog.isShowDialog()
    }

    // 获取收货地址
    getAddress(itemRegion: string, itemAddress: string) {
      let region = ''
      const reg = new RegExp('/', 'g')
      if (itemRegion.indexOf('/') != -1) {
        region = itemRegion.replace(reg, '')
        console.log(region, 'region')
      }
      return region + itemAddress
    }
  }
</script>
<style scoped>
  /* ::v-deep .advanced-item {
    height: 51px;
  } */
  .fontColor {
    color: black;
  }
</style>
