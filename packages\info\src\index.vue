<route-meta>
{
"isMenu": true,
"title": "资讯管理",
"sort": 2,
"icon": "icon-zixun"
}
</route-meta>
<template>
  <el-main>
    <div class="f-p15" v-if="$hasPermission('query')" desc="查询" actions="doQueryPage">
      <div class="f-mb15">
        <template v-if="$hasPermission('create')" desc="新建" actions="@hbfe/jxjy-admin-info/src/create.vue">
          <el-button type="primary" icon="el-icon-plus" @click="addInfo">新建资讯</el-button>
        </template>
        <template
          v-if="$hasPermission('category1')"
          desc="资讯类别管理"
          actions="@hbfe/jxjy-admin-platform/src/basic-info/index.vue"
        >
          <el-button type="primary" plain @click="goToColumnSetup">资讯类别管理</el-button>
        </template>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!-- <el-row :gutter="16" class="m-query is-border-bottom"> -->
        <hb-search-wrapper @reset="reset" class="m-query is-border-bottom">
          <el-form-item label="资讯标题">
            <el-input
              clearable
              placeholder="请输入资讯标题"
              v-model="noticeParam.title"
              @keydown.native.enter="search"
            />
          </el-form-item>

          <el-form-item label="资讯分类">
            <information-classification
              ref="classificationRef"
              v-model="noticeParam.categoryType"
              :checkStrictly="false"
              :value="noticeParam.categoryType"
            ></information-classification>
          </el-form-item>

          <el-form-item label="是否弹窗">
            <el-select v-model="noticeParam.isPopup" clearable placeholder="请选择是否弹窗">
              <el-option :value="true" label="是"></el-option>
              <el-option :value="false" label="否"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="资讯状态">
            <!-- <biz-info-status v-model="noticeParam.status"></biz-info-status> -->
            <el-select v-model="noticeParam.status" clearable placeholder="请选择资讯状态">
              <el-option :value="-1" label="全部"></el-option>
              <el-option :value="0" label="草稿"></el-option>
              <el-option :value="1" label="发布"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="发布地区">
            <biz-region-cascader
              :check-strictly="true"
              placeholder="请选择地区"
              v-model="noticeParam.areaCodeList"
            ></biz-region-cascader>
          </el-form-item> -->

          <template slot="actions">
            <el-button type="primary" @click="search">查询</el-button>
          </template>
        </hb-search-wrapper>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table" v-loading="loading">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column width="115" align="center">
            <template slot-scope="scope">
              <el-tag type="primary" size="small" v-if="scope.row.isPopup">弹窗公告</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="资讯标题" min-width="400">
            <template slot-scope="scope">
              <div>
                <p>{{ scope.row.title }}</p>
                <p class="f-c9 f-f13 f-mt5">
                  <el-tag type="danger" size="small" class="f-mr10" v-if="scope.row.isTop">置顶</el-tag>
                  <span class="f-mr20">发布时间：{{ scope.row.date }}</span
                  >发布人：{{ scope.row.issuer || unknow }}
                </p>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="发布地区" min-width="120">
            <template slot-scope="scope">{{ scope.row.areaCodeName ? scope.row.areaCodeName : '' }}</template>
          </el-table-column> -->
          <el-table-column label="资讯分类" min-width="120">
            <template slot-scope="scope">{{ scope.row.categoryType }}</template>
          </el-table-column>
          <el-table-column label="状态" min-width="120">
            <template slot-scope="scope">
              <div v-if="scope.row.status == 0">
                <el-badge is-dot type="warning" class="badge-status">草稿</el-badge>
              </div>
              <div v-if="scope.row.status == 1">
                <el-badge is-dot type="success" class="badge-status">发布</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" align="center">
            <template slot-scope="scope">
              <template v-if="$hasPermission('copy')" desc="复制" actions="@hbfe/jxjy-admin-info/src/create.vue">
                <el-button type="text" size="mini" @click="copyNotice(scope.row.id)">复制</el-button>
              </template>
              <template v-if="$hasPermission('publish')" desc="发布" actions="publishNoticeOpen">
                <hb-popconfirm
                  placement="top"
                  title="确定发布该资讯？"
                  @confirm="publishNoticeOpen(scope.row.id)"
                  v-if="scope.row.status == 0"
                >
                  <el-button slot="reference" type="text" size="mini">发布</el-button>
                </hb-popconfirm>
              </template>
              <el-button
                type="text"
                size="mini"
                v-if="$hasPermission('modify')"
                desc="修改"
                actions="@hbfe/jxjy-admin-info/src/modify.vue"
                @click="editInfo(scope.row.id)"
              >
                修改
              </el-button>
              <template v-if="$hasPermission('remove')" desc="删除" actions="removeItem">
                <hb-popconfirm
                  placement="top"
                  title="删除后该资讯需要重新创建，是否确认删除？"
                  @confirm="removeItem(scope.row.id)"
                >
                  <el-button slot="reference" type="text" size="mini">删除</el-button>
                </hb-popconfirm>
                <!-- <el-button type="text" size="mini" @click="removeItem(scope.row.id)">删除</el-button> -->
              </template>
              <hb-popconfirm
                placement="top"
                title="置为草稿则门户不可见，是否确认置为草稿"
                @confirm="draftOpen(scope.row.id)"
                v-if="$hasPermission('draft') && scope.row.status == 1"
                desc="置为草稿"
                actions="draftOpen"
              >
                <el-button slot="reference" type="text" size="mini">置为草稿</el-button>
              </hb-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Watch, Ref } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import BizInfoStatus from '@hbfe/jxjy-admin-components/src/biz/biz-info-status.vue'
  import NewsListVo from '@api/service/management/news/query/query-news-list/vo/NewsListVo'
  import NewsListParamVo from '@api/service/management/news/query/query-news-list/vo/NewsListParamVo'
  import InformationClassification from '@hbfe/jxjy-admin-components/src/information-classification.vue'
  import MutationNewsFactory from '@api/service/management/news/mutation/MutationNewsFactory'
  import QueryNewsList from '@api/service/management/news/query/query-news-list/QueryNewsList'
  import bizRegionCascader from '@hbfe/jxjy-admin-components/src/biz/biz-region-cascader.vue'
  import { bind, debounce } from 'lodash-decorators'
  @Component({
    components: { BizInfoStatus, InformationClassification, bizRegionCascader }
  })
  export default class extends Vue {
    /**
     * 资讯分类组件ref
     */
    @Ref('classificationRef')
    classificationRef: InformationClassification
    // 搜索参数
    noticeParam = new NewsListParamVo()
    queryNewsObj = new QueryNewsList()
    mutationNewsObj = new MutationNewsFactory()
    tableData = new Array<NewsListVo>()
    loading = false
    unknow = '未知'
    page: UiPage
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    @Watch('noticeParam.isPopup')
    watchIsPopup(val: any) {
      if (val !== true && val !== false) this.noticeParam.isPopup = undefined
    }
    // 请求列表
    async doQueryPage() {
      try {
        this.loading = true
        this.tableData = await this.queryNewsObj.queryNewsList(this.page, this.noticeParam)
        console.log(this.tableData, '=-=this.tableData=-=')
      } catch (e) {
        this.$message.error('请求失败')
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    async activated() {
      await this.doQueryPage()
      this.$nextTick(() => {
        ;(this.$refs['classificationRef'] as InformationClassification).getInformationClassificationStair().then(() => {
          ;(this.$refs['classificationRef'] as InformationClassification).echo()
        })
      })
    }

    // 新建
    addInfo() {
      this.$router.push('/basic-data/info/create')
    }

    // 修改
    editInfo(id: string) {
      this.$router.push('/basic-data/info/modify/' + id)
    }

    // 详情
    goDetail(id: string) {
      this.$router.push('/basic-data/info/detail/' + id)
    }

    // 去栏目管理
    goToColumnSetup() {
      this.$router.push('/basic-data/platform/basic-info?&column=' + 'column')
    }

    // 复制
    async copyNotice(id: string) {
      this.$router.push('/basic-data/info/create?id=' + id)
    }

    // 删除
    @bind
    @debounce(200)
    async removeItem(id: string) {
      const res = await this.mutationNewsObj.mutationNewsDelete().doDeleteNews(id)
      if (res.isSuccess()) {
        this.$message.success('删除成功')
        await this.doQueryPage()
      } else {
        this.$message.warning('删除失败')
      }
    }

    // 草稿弹窗
    async draftOpen(id: string) {
      console.log('111')
      await this.toDraft(id)
    }

    // 置为草稿
    @bind
    @debounce(200)
    async toDraft(id: string) {
      const mutationNewsChangeStatusVo = this.mutationNewsObj.mutationNewsChangeStatus(id)
      const res = await mutationNewsChangeStatusVo.doDraftNews()
      if (res.isSuccess()) {
        this.$message.success('置为草稿成功')
        await this.doQueryPage()
      } else {
        this.$message.warning('置为草稿失败')
      }
    }

    // 发布弹窗
    async publishNoticeOpen(id: string) {
      console.log('111')
      await this.publishNotice(id)
    }

    // 发布
    @bind
    @debounce(200)
    async publishNotice(id: string) {
      const mutationNewsChangeStatusVo = this.mutationNewsObj.mutationNewsChangeStatus(id)
      const res = await mutationNewsChangeStatusVo.doPublishNews()
      if (res.isSuccess()) {
        this.$message.success('发布成功')
        await this.doQueryPage()
      } else {
        this.$message({
          showClose: true,
          duration: 1500,
          type: 'warning',
          message: res.getMessage()
        })
        setTimeout(() => {
          this.$router.push('/basic-data/info/modify/' + id)
        }, 1500)
      }
    }

    // 查询
    async search() {
      this.page.pageNo = 1
      await this.doQueryPage()
    }

    // 重置
    async reset() {
      this.noticeParam = new NewsListParamVo()
      await this.doQueryPage()
    }
  }
</script>

<style scoped></style>
