import AuthModule from '@api/service/common/auth/AuthModule'
import Authentication from '@api/service/common/authentication/Authentication'
import { ElForm } from 'element-ui/types/form'
import { Component, Ref, Vue } from 'vue-property-decorator'
import { CaptchaApplyRequest, SmsCodeApplyRequest } from '@hbfe-ms/ms-basicdata-domain-gateway'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import { BusinessTypeEnum } from '@hbfe-biz/biz-authentication/dist/enums/BusinessTypeEnum'
import AccountType from '@hbfe-biz/biz-authentication/dist/enums/AccountType'
import LoginParams from '@hbfe-biz/biz-authentication/dist/models/LoginParams'
import { SendMessageParams } from '@api/service/common/authentication/interfaces/LoginParams'
import { IdentityType } from '@hbfe-biz/biz-authentication/dist/enums/IdentityType'

export class LoginResult {
  code: number
  message: string

  constructor(code: number, message: string) {
    this.code = code
    this.message = message
  }

  isSuccess(): boolean {
    return this.code === 200
  }
}

export enum RoleType {
  ADMIN = 1,
  SERVICEPROVIDER = 2,
  REGIONADMINISTRATOR = 3
}

export enum SendSmsType {
  STUDENT = 'student',
  ADMIN = 'admin',
  PROVIDER = 'provider',
  REGION = 'region'
}

const countDownTime = 60

@Component
class LoginCore extends Vue {
  @Ref('loginForm') loginForm: ElForm
  @Ref('loginFormSec') loginFormSec: ElForm
  success = 5
  dialogVisible3 = false
  $authentication: Authentication
  validateCodePic = ''
  passwordLogin = false
  phoneNumbe = ''
  // 验证码发送
  loginSending = true
  // 预计倒计时时间
  count = 60
  // 发送后倒计时计时
  countTime = 60
  dialog1 = false
  // 实为布尔值，此处赋值帐号值。用来解决记住帐号和密码后，图形验证码无法输入问题
  isPhoneNumberValid = false
  getMessageCoding = false
  // 发送后倒计时计时
  countDownTime = countDownTime
  applyingCaptcha = false
  rememberPassword = false
  loginIng = false
  loginResult: LoginResult = new LoginResult(200, '')
  formData = {
    roleType: RoleType.ADMIN,
    account: '',
    password: '',
    accountType: 2,
    extAttributes: '',
    // 图形验证码
    captcha: '',
    phoneCaptcha: ''
  }
  timer = 0

  intervalTimer: any = null

  isCaptchaValid = false
  rules = {}

  sureTime = 5
  @Ref('changePhoneRef')
  changePhoneRef: any
  changePhoneLoading = false
  isPhoneCaptchaValid = true
  phoneCaptchaVaildFlag = false
  changePhoneFrom = {
    phone: '',
    smsCode: '',
    captcha: ''
  }
  // 验证码发送
  sending = true
  validatePic = ''

  stopTimerCount() {
    window.clearInterval(this.timer)
  }

  stopIntervalTimer() {
    if (this.intervalTimer) {
      clearTimeout(this.intervalTimer)
      this.intervalTimer = null
    }
  }

  countDown(time: number) {
    this.countTime = time

    console.log('countStart')
    this.intervalTimer = setInterval(() => {
      if (this.countTime === 0) {
        this.loginSending = true
        clearInterval(this.intervalTimer)
        this.countTime = this.count
      } else {
        this.countTime = this.countTime - 1
      }
    }, 1000)
  }

  async sendMessageWithoutCaptcha() {
    this.loginSending = false
    this.applyingCaptcha = true
    const params = new SmsCodeApplyRequest()
    if (this.formData.roleType === RoleType.SERVICEPROVIDER) {
      //   params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.coursewareSupplierPhoneNumLoginToken)
    } else if (this.formData.roleType === RoleType.REGIONADMINISTRATOR) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionLoginToken)
    } else {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superPhoneNumLoginToken)
    }
    params.businessType = BusinessTypeEnum.login_account
    params.phone = this.formData.account
    try {
      const res = await this.$authentication.verify.msSendSmsCode(params)
      if (res.data.code === 409) {
        this.$message.error('一个手机号一天只能接收4条短信，请使用帐号登录。')
      } else if (res.data.code === 410) {
        this.$message.error('发送验证码失败')
      } else if (res.data.code === 701) {
        this.$message.error('请重新验证图形验证码后，再次获取短信验证码')
      } else if (res.data.code === 515) {
        this.$message.error('不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)')
      } else if (res.data.code === 400) {
        this.$message.error('token无效')
      } else if (res.data.code === 509) {
        this.$message.error('未绑定手机号')
      } else if (res.data.code === 514) {
        this.$message.error('token中未携带手机号')
      }
      if (res.status.code === 200) {
        this.countDown(this.count)
        return
      } else {
        this.$message.error('发送验证码失败')
        this.loginSending = true
      }
    } catch (e) {
      console.log(e)
      const message = e?.message || '获取验证码失败，'
      this.$message.error(message)
    } finally {
      this.applyingCaptcha = false
    }
  }
  async submit() {
    this.changePhoneLoading = true
    try {
      await this.changePhoneRef.validate()
      const res = await this.$authentication.verify.msValidSmsCode(
        this.changePhoneFrom.smsCode,
        this.changePhoneFrom.phone
      )
      if (res.status.code === 200) {
        const changeRes = await this.$authentication.account.bindPhone(
          this.$authentication.verify.shortMessageCaptchaToken
        )
        if (changeRes.status.code === 200) {
          if (changeRes?.data?.code === '200') {
            this.dialogVisible3 = true
            this.sureTimeDown(this.sureTime)
            this.changePhoneLoading = false
          } else {
            this.$message.error(changeRes?.data?.message)
            await this.refreshValidateCodePicPhone()
            this.changePhoneLoading = false
          }
        } else {
          this.$message.error('绑定失败')
          await this.refreshValidateCodePicPhone()
          this.changePhoneLoading = false
        }
      } else {
        this.$message.error('短信验证码错误')
        await this.refreshValidateCodePicPhone()
        this.changePhoneLoading = false
      }
    } catch (e) {
      await this.refreshValidateCodePicPhone()
      this.$message.error('绑定失败')
      this.changePhoneLoading = false
    }
  }
  /**
   * 获取图片验证码
   */
  async refreshValidateCodePicPhone() {
    this.isPhoneCaptchaValid = true
    this.phoneCaptchaVaildFlag = false
    const params = new CaptchaApplyRequest()
    if (this.formData.roleType === RoleType.SERVICEPROVIDER) {
      // params.token = ConfigCenterModule.getFrontendApplication(
      //   frontendApplication.coursewareSupplierChangePhoneNumToken
      // )
    } else if (this.formData.roleType === RoleType.REGIONADMINISTRATOR) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionChangePhoneNumToken)
    } else {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superChangePhoneNumToken)
    }
    params.businessType = BusinessTypeEnum.change_binding_phone
    const res = await this.$authentication.verify.applyCaptcha(params)
    if (res.status.code == 200) {
      this.validatePic = `data:image/jpeg;base64,${res?.data?.captcha}`
    }
  }

  // 验证码倒计时
  sureTimeDown(time: number) {
    let timer = time
    const sureTimeDown = setInterval(() => {
      if (timer === 0) {
        clearInterval(sureTimeDown)
        this.dialogVisible3 = false
        this.dialog1 = false
        this.sureTime = 5
        window.location.replace(this.$router.resolve('/home').href)
        window.location.reload()
      } else {
        timer--
        this.sureTime = timer
      }
    }, 1000)
  }
  // 获取短信验证码
  async getPhoneCaptcha() {
    this.loginSending = false
    this.countDown(this.count)

    const valStatus = await AuthModule.preValidServicerLogin(this.formData.account)
    if (!valStatus.isSuccess()) {
      const errcode = valStatus.errors[0].code
      //30407 该账号不存在
      // 30408 该帐号无合作机构
      if (errcode == 30407) {
        return this.$message.error('帐号不存在，请确认是否已申请合作')
      } else if (errcode == 30408) {
        return this.$message.error('该帐号已中止合作，请联系合作的培训机构确认')
      } else {
        return this.$message.error(valStatus.getMessage())
      }
    }
    this.applyingCaptcha = true
    const sendMessage = new SendMessageParams()
    sendMessage.phoneNumber = this.formData.account
    sendMessage.accountType = AccountType.admin
    try {
      // this.$authentication.captchaToken = ''
      const result = await this.$authentication.verify.sendMessage(sendMessage)
      this.getMessageCoding = true
      if (result.status.code != 200) {
        this.loginResult = result.status
        return
      }
      this.countDown(this.count)
    } catch (e) {
      console.log(e)
      const message = e?.message || '获取验证码失败，'
      this.$message.warning(message)
    } finally {
      this.applyingCaptcha = false
    }
  }

  async refreshValidateCodePic() {
    const params = new CaptchaApplyRequest()
    if (this.formData.roleType === RoleType.SERVICEPROVIDER) {
      // res = await this.$authentication.applyProviderCaptcha()
    } else if (this.formData.roleType === RoleType.REGIONADMINISTRATOR) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionAdminLoginToken)
    } else {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.sunProjectSuperLoginToken)
    }
    params.businessType = BusinessTypeEnum.login_account
    const res = await this.$authentication.verify.applyCaptcha(params)
    this.validateCodePic = `data:image/jpeg;base64,${res?.data?.captcha}`

    this.isCaptchaValid = false
  }

  async doPasswordLogin() {
    const loginParams = new LoginParams()
    loginParams.identity = this.formData.account
    loginParams.password = this.formData.password
    loginParams.longTerm = this.rememberPassword
    loginParams.captchaValue = this.formData.captcha
    loginParams.accountType = AccountType.admin
    loginParams.token = this.$authentication.verify.captchaToken
    const response = await this.$authentication.doLogin(IdentityType.account_pwd_captcha, loginParams)
    this.$authentication.account.doRememberLoginInfo(loginParams)
    return response
  }

  async shortMessageLogin() {
    const shortMessageLoginParams = new LoginParams()
    shortMessageLoginParams.phoneNumber = this.formData.account
    shortMessageLoginParams.smsCode = this.formData.phoneCaptcha
    shortMessageLoginParams.longTerm = this.rememberPassword
    shortMessageLoginParams.token = this.$authentication.verify.shortMessageCaptchaToken
    return await this.$authentication.doLogin(IdentityType.sms_code, shortMessageLoginParams)
  }

  async captchaValid(rule: any, value: any, callback: (error?: Error) => void) {
    if (rule.regexp.test(value)) {
      try {
        if (!this.isCaptchaValid) {
          await this.$authentication.verify.msValidateCaptcha(value)
          this.isCaptchaValid = true
        }
        callback()
      } catch (e) {
        await this.refreshValidateCodePic()
        callback(new Error('验证失败'))
      }
    } else {
      this.isCaptchaValid = false
      callback(new Error('验证码为4位数'))
    }
  }

  public async created() {
    this.formData.account = this.$authentication.account.rememberLoginInfo.account
    this.formData.password = this.$authentication.account.rememberLoginInfo.password
    this.rememberPassword = this.$authentication.account.rememberLoginInfo.longTerm
    this.rules = {
      roleType: [
        {
          required: true,
          message: '请选择登录帐号类型',
          trigger: ['blur', 'change']
        }
      ],
      account: [
        {
          required: true,
          validator: async (rules: any, value: any, callback: (message?: any) => {}) => {
            const message = this.passwordLogin ? '账号' : '手机号'
            if (!value) {
              this.isPhoneNumberValid = false
              return callback(new Error(`请输入${message}`))
            } else {
              if (!this.passwordLogin) {
                // 宽松匹配
                if (!/^(?:(?:\+|00)86)?1\d{10}$/.test(value)) {
                  this.isPhoneNumberValid = false
                  return callback(new Error('手机号格式不正确'))
                }
              }
            }
            this.isPhoneNumberValid = true
            callback()
          },
          trigger: ['blur', 'change']
        }
      ],
      password: [
        {
          required: true,
          message: '请输入密码',
          trigger: 'blur'
        }
      ],
      captcha: [
        {
          regexp: /[a-zA-z0-9]{4}/,
          validator: this.captchaValid,
          required: true,
          trigger: 'change'
        }
      ]
    }
    // 获取登录票
    try {
      // await this.$authentication.getTicket()
      await this.refreshValidateCodePic()
    } catch (e) {
      console.log(e)
    }
  }

  // 加载钉钉二维码
  ddQRCodeLoading = true

  mounted() {
    // this.doRefreshDdQRCode()
  }

  async loginSuccess() {
    // 123
    console.log('dengls')
  }
  dialogVisible() {
    this.dialogVisible3 = true
    this.successTime()
  }
  successTime() {
    let timer = 5
    const timers = setInterval(() => {
      if (timer === 0) {
        clearInterval(timers)
        this.success = 5
        this.dialogVisible3 = false
        this.dialog1 = false
        this.doLogin()
      } else {
        timer--
        this.success = timer
      }
    }, 1000)
  }
  loginFirst() {
    this.dialog1 = true
  }

  async roleTypeChange() {
    await this.refreshValidateCodePic()
  }

  async doLogin() {
    try {
      await this.loginForm.validate()
      this.loginIng = true
      let result: any
      if (!this.passwordLogin) {
        result = await this.shortMessageLogin()
      } else {
        result = await this.doPasswordLogin()
      }
      if (result.status.isSuccess() && result.data.code === '200') {
        try {
          let response
          if (!this.passwordLogin) {
            response = (await this.$authentication.ssoAuth()) as any
          } else {
            response = (await this.$authentication.ssoAuth()) as any
          }
          if (response.code !== 200 || !response.data?.access_token) {
            await this.refreshValidateCodePic()
            this.$authentication.removeToken()
            return
          }
          this.$emit('login-result', true)
          const messageContent = {
            message: 'login-success',
            type: 'admin'
          }
          this.$util.channel.postMessage(messageContent)
          await this.loginSuccess()
        } catch (e) {
          await this.refreshValidateCodePic()
          this.$authentication.removeToken()
          this.loginResult = new LoginResult(e.code, e.message)
        } finally {
          this.loginIng = false
        }
      } else {
        this.loginIng = false
        this.loginResult = new LoginResult(result.data.code, result.data.message)
        result.code == 4201 && (result.message = '验证码不正确，请重新输入')
        if (result.data.message) {
          this.$message.error(result.data.message)
        }
        this.$authentication.removeToken()
        await this.refreshValidateCodePic()
      }
    } catch (e) {
      this.$authentication.removeToken()
      this.loginIng = false
      this.loginResult = new LoginResult(e.code, e.message)
      e.code == 4201 && (e.message = '验证码不正确，请重新输入')
      if (e.message) {
        this.$message.error(e.message)
      }

      await this.refreshValidateCodePic()
    }
  }

  async smsCodeLogin() {
    this.loginIng = true
    const params = new LoginParams()
    params.phoneNumber = this.formData.account
    params.smsCode = this.formData.phoneCaptcha

    try {
      await this.loginFormSec.validate()

      const codeVaildRes = await this.$authentication.verify.msValidSmsCode(params.smsCode, params.phoneNumber)
      if (codeVaildRes?.status?.code !== 200) {
        this.$message.error('验证码未知错误')
        this.loginIng = false
        return
      } else if (codeVaildRes?.data?.code !== 200) {
        if (codeVaildRes?.data?.code === 500) {
          this.$message.error('请输入正确验证码')
        } else if (codeVaildRes?.data?.code === 408) {
          this.$message.error('验证码超时')
        } else {
          this.$message.error('验证码未知错误')
        }
        this.loginIng = false
        return
      }

      params.token = this.$authentication.verify.shortMessageCaptchaToken
      const res = await this.$authentication.doLogin(IdentityType.sms_code, params)
      if (res?.status?.isSuccess() && res.data.code === '200') {
        const response = (await this.$authentication.ssoAuth()) as any
        if (response.code !== 200 || !response.data?.access_token) {
          this.$message.error(res?.data?.message)
          this.loginIng = false
          return
        }
        this.$emit('login-result', true)
        this.loginIng = false
        await this.loginSuccess()
      } else {
        this.$message.error(res?.data?.message)
        this.loginIng = false
      }
    } catch (e) {
      console.log(e, 'rejecte')
      if (e.data?.code === '4001') {
        this.$message.error('账号不存在')
      } else {
        const message = e.data?.message || '登录失败'
        if (message) {
          this.$message.error(message)
        }
      }
      this.loginIng = false
    }
  }

  beforeDestroy() {
    this.loginIng = false
  }

  /**
   * 更新加载钉钉二维码
   */
  async doRefreshDdQRCode() {
    // this.ddQRCodeLoading = true
    // try {
    //   let url = `${location.protocol}//${location.host}/#/binding-dd`
    //   if (process.env.NODE_ENV === 'production') {
    //     url = `${location.protocol}//${location.host}/admin/#/binding-dd`
    //   }
    //   console.log('applyDingDingLoginQRCodeGoto.url,', url)
    //   await this.$authentication.applyDingDingLoginQRCodeGoto(url)
    // } catch (err) {
    //   console.error(err)
    // }
    // const param = this.$authentication.getDingDingLoginGotoParam()
    // if (!param) {
    //   this.ddQRCodeLoading = false
    //   return
    // }
    // ;(window as any).DDLogin({
    //   id: 'dd_login_container',
    //   goto: param, //请参考注释里的方式
    //   style: 'border:none;background-color:#FFFFFF;margin-top:-30px;',
    //   width: '230',
    //   height: '290'
    // })
    // this.ddQRCodeLoading = false
  }

  async ddLoginWithToken(token: string) {
    try {
      this.loginIng = true
      const result = await this.$authentication.ddLoginWithToken(token)
      if (result.status.code === 200) {
        try {
          await this.$authentication.auth()
          this.$emit('login-result', true)
        } catch (e) {
          this.loginResult = new LoginResult(e.code, e.message)
        } finally {
          this.loginIng = false
        }
      } else {
        // todo
        // this.loginResult = result
      }
      await this.loginSuccess()
    } catch (e) {
      this.loginResult = new LoginResult(e.code, e.message)
    }
  }
}

export default LoginCore
