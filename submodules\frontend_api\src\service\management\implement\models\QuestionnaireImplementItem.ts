import QuestionnaireItem from '@api/service/management/resource/question-naire/models/QuestionnaireItem'
import {
  QuestionnaireInLearningSchemeResponse,
  SurveyInformationResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'

export default class QuestionnaireImplementItem extends QuestionnaireItem {
  /**
   * 提交人数
   */
  submitNum: number = undefined

  /**
   * 二维码地址（给UI使用，状态层不填充该字段）
   */
  qrCode: string = undefined

  /**
   * 期别id（报告内查询教师选项使用）
   */
  periodId: string = undefined

  /**
   * 学习方式id
   */
  learningId: string = undefined

  /**
   * 应用范围
   */
  usedRange: number = undefined
  static fromPeriod(dto: QuestionnaireInLearningSchemeResponse) {
    const vo = new QuestionnaireImplementItem()
    const { surveyInformationResponse, num } = dto
    vo.submitNum = num
    if (surveyInformationResponse) {
      vo.id = surveyInformationResponse.questionnaireId
      vo.name = surveyInformationResponse.questionnaireName
      vo.createTime = surveyInformationResponse.createTime
      vo.type = surveyInformationResponse.type
      vo.status = surveyInformationResponse.status
      vo.schemeUseStatus = surveyInformationResponse.isReferenced
      vo.periodId = surveyInformationResponse.usedRange == 2 ? surveyInformationResponse.ownerId : ''
      vo.learningId = surveyInformationResponse.learningId
      vo.usedRange = surveyInformationResponse.usedRange
    }

    return vo
  }

  /**
   * pageIssueQuestionnaireInServicer
   * @param dto
   */
  static fromScanning(dto: SurveyInformationResponse) {
    const vo = new QuestionnaireImplementItem()
    vo.id = dto.questionnaireId
    vo.name = dto.questionnaireName
    vo.type = dto.type
    vo.status = dto.status
    vo.createTime = dto.createTime
    vo.schemeUseStatus = dto.isReferenced
    vo.learningId = dto.learningId
    return vo
  }
}
