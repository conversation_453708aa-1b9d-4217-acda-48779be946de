import { Page, Response } from '@hbfe/common'
import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import ReturnOrderResponseVo from '@api/service/management/trade/single/order/query/vo/ReturnOrderResponseVo'
import { ReturnSortRequest as ReturnSortRequestv2 } from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import { ReturnOrderStatisticResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

export default abstract class QueryRefundListBase {
  totalSize = 0
  //统计数据
  returnOrderStatisic = new ReturnOrderStatisticResponse()

  /**
   * 获取退款单列表
   */
  abstract queryRefundOrderList(page: Page, request: ReturnOrderRequestVo): Promise<Array<ReturnOrderResponseVo>>

  /**
   * 查询退款统计
   */
  abstract queryStatisticReturnOrder(request: ReturnOrderRequestVo): Promise<void>

  /**
   * 导出个人退货单
   */
  abstract exportReturnOrderExcelInServicer(
    request: ReturnOrderRequestVo,
    sort?: Array<ReturnSortRequestv2>
  ): Promise<false | Response<boolean>>
}
