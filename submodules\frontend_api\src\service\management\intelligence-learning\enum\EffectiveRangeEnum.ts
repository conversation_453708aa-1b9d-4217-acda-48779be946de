import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum EffectiveRangeEnum {
  /**
   * 新用户
   */
  new_user = 1,
  /**
   * 全部用户
   */
  all_user = 2
}
export default class EffectiveRangeType extends AbstractEnum<EffectiveRangeEnum> {
  static enum = EffectiveRangeEnum
  constructor(status?: EffectiveRangeEnum) {
    super()
    this.current = status
    this.map.set(EffectiveRangeEnum.new_user, '仅新用户')
    this.map.set(EffectiveRangeEnum.all_user, '全部用户（含已注册）')
  }
}
