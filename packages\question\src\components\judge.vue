<!--创建试题-判断题-->
<template>
  <div>
    <el-form-item label="试题题目" required>
      <!-- <hb-tiny-mce-editor v-model="createQuestion.topic" placeholder="请输入试题题目" v-if="show"></hb-tiny-mce-editor> -->
      <el-input type="textarea" :rows="6" v-model="createQuestion.topic" v-if="show" placeholder="请输入试题题目" />
    </el-form-item>
    <el-form-item label="试题选项">
      <!-- <div>真项:A</div>
      <div class="text-options">
        <el-input type="textarea" :rows="5" placeholder="对" v-model="createQuestion.correctAnswerText"></el-input>
      </div> -->
      <div class="m-option-btn f-mb20">
        <div class="f-flex f-align-center">
          <p class="f-cb">真项 A</p>
          <el-input v-model="createQuestion.correctAnswerText" clearable class="f-flex-sub f-ml10" placeholder="对" />
        </div>
      </div>
      <div class="m-option-btn f-mb20">
        <div class="f-flex f-align-center">
          <p class="f-cb">假项 B</p>
          <el-input v-model="createQuestion.incorrectAnswerText" clearable class="f-flex-sub f-ml10" placeholder="错" />
        </div>
      </div>
      <!-- <div>假项:B</div>
      <div class="text-options">
        <el-input type="textarea" :rows="5" placeholder="错" v-model="createQuestion.incorrectAnswerText"></el-input>
      </div> -->
    </el-form-item>
    <el-form-item label="正确答案" required>
      <el-radio-group v-model="createQuestion.correctAnswer" size="medium">
        <el-radio v-for="(item, index) in judgeOptionList" :key="index" :label="item.value">{{ item.label }} </el-radio>
      </el-radio-group>
    </el-form-item>
    <el-form-item label="题目题析">
      <div class="rich-text">
        <el-input
          type="textarea"
          v-if="show"
          :rows="6"
          v-model="createQuestion.description"
          placeholder="请输入试题题目"
        />
      </div>
      <!-- <hb-tiny-mce-editor
        v-model="createQuestion.description"
        placeholder="请输入题目题析"
        v-if="show"
      ></hb-tiny-mce-editor> -->
    </el-form-item>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Vue, PropSync, Watch } from 'vue-property-decorator'
  import HbTinyMceEditor from '@hbfe/jxjy-admin-components/src/tinymce-editor/index.vue'
  import { isEmpty } from 'lodash'
  import { ChooseAnswerOptionVo } from '@api/service/management/resource/question/mutation/vo/ChooseAnswerOptionVo'
  import CreateOpinionQuestionDto from '@api/service/management/resource/question/mutation/vo/create/CreateOpinionQuestionVo'

  @Component({
    components: {
      HbTinyMceEditor
    }
  })
  export default class extends Vue {
    @PropSync('createQuestionInfo', {
      type: Object
    })
    createQuestion: CreateOpinionQuestionDto

    @Watch('createQuestionInfo', { deep: true })
    changeCreateQuestionInfo(val: any) {
      console.log(val, '444')
    }

    @Prop({
      required: true,
      type: String
    })
    parentComponentQuestionType: number
    @Prop({
      default: false
    })
    isUpdate: boolean
    /**
     * 判断题答案
     */
    correctAnswer = true
    /**
     * 正确文本
     */
    correctText = ''
    /**
     * 错误文本
     */
    incorrectText = ''

    /**
     * 判断题选项
     */
    judgeOptionList = [
      {
        value: true,
        label: '真项:A',
        title: '对'
      },
      {
        value: false,
        label: '假项:B',
        title: '错'
      }
    ]
    show = false

    /**
     * 父组件调用
     * 提交最终数据
     */
    commit() {
      // this.questionCommon.questionContent.correctAnswer = this.correctAnswer
      // this.questionCommon.questionContent.correctText = this.correctText || '对'
      // this.questionCommon.questionContent.incorrectText = this.incorrectText || '错'
    }

    /**
     * 初始化加载
     */
    created() {
      this.deploy()
    }

    deploy() {
      // if (!this.isUpdate) {
      //   this.questionCommon.questionContent = new Judgement()
      // } else {
      //   this.correctAnswer = this.questionCommon.questionContent.correctAnswer
      //   this.correctText = this.questionCommon.questionContent.correctText || '对'
      //   this.incorrectText = this.questionCommon.questionContent.incorrectText || '错'
      // }
      // this.show = false
      // setTimeout(() => {
      //   this.show = true
      // }, 100)
      this.show = true
    }
    productChoiceItem(index: number) {
      const choice = new ChooseAnswerOptionVo()
      choice.id = index + ''
      return choice
    }
    /**
     * 基本表单校验
     */
    validForm() {
      if (isEmpty(this.createQuestion.topic)) {
        this.$message.warning('请配置试题题目。')
        return false
      }
      if (!this.createQuestion.correctAnswerText) {
        this.createQuestion.correctAnswerText = '对'
      }
      if (!this.createQuestion.incorrectAnswerText) {
        this.createQuestion.incorrectAnswerText = '错'
      }
      return true
    }
  }
</script>
