import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum AnswerTypeEnum {
  attachments = 1,
  online_editing
}

class AnswerType extends AbstractEnum<AnswerTypeEnum> {
  constructor(status?: AnswerTypeEnum) {
    super()
    this.current = status
    this.map.set(AnswerTypeEnum.attachments, '提交附件')
    this.map.set(AnswerTypeEnum.online_editing, '在线编辑')
  }
}

export default AnswerType
