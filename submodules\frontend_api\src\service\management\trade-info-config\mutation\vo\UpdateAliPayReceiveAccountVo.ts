import { ReceiveAccountExtProperty, UpdateReceiveAccountRequest } from '@api/ms-gateway/ms-trade-configuration-v1'
import {
  AlipayEncryptionKeyDataResponse,
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import UpdateReceiveAccountVo from './UpdateReceiveAccountVo'

class UpdateAliPayReceiveAccountVo extends UpdateReceiveAccountVo {
  /**
   * 支付宝密钥 -- 原型给的支付宝密钥不需要了
   */

  /**
   * 合作者身份ID
   */
  partner = ''
  /**
   * 支付宝应用私钥
   */
  privateKey = ''
  /**
   * 支付宝公钥
   */
  publicKey = ''
  /**
   * 支付宝应用id
   */
  appId = ''

  from(res: ReceiveAccountConfigResponse) {
    this.id = res.id
    this.accountType = res.accountType
    this.accountNo = res.accountNo
    this.accountName = res.name
    this.taxPayerId = res.taxPayerId
    this.refundWay = res.returnType
    this.paymentChannelId = res.paymentChannelId
    this.qrScanPrompt = res.qrScanPrompt
    if (res.encryptionKeyData.encryptionKeyType === 'Alipay') {
      const temp = res.encryptionKeyData as AlipayEncryptionKeyDataResponse
      this.partner = temp.partner
      this.privateKey = temp.privateKey
      this.publicKey = temp.publicKey
      this.appId = temp.appId
    }
  }

  to() {
    const updateReceiveAccountRequest = new UpdateReceiveAccountRequest()
    updateReceiveAccountRequest.receiveAccountId = this.id
    updateReceiveAccountRequest.name = this.accountName
    updateReceiveAccountRequest.refundWay = this.refundWay
    updateReceiveAccountRequest.taxPayerId = this.taxPayerId
    updateReceiveAccountRequest.qrScanPrompt = this.qrScanPrompt
    updateReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    this.updateProperties('appId', this.appId, updateReceiveAccountRequest.properties)
    this.updateProperties('publicKey', this.publicKey, updateReceiveAccountRequest.properties)
    this.updateProperties('privateKey', this.privateKey, updateReceiveAccountRequest.properties)
    this.updateProperties('partner', this.partner, updateReceiveAccountRequest.properties)
    return updateReceiveAccountRequest
  }
  private updateProperties(propertyName: string, propertyValue: string, properties: Array<ReceiveAccountExtProperty>) {
    const property = properties?.find((item) => item.name === propertyName)
    if (property) {
      property.value = propertyValue
    } else {
      const item = new ReceiveAccountExtProperty()
      item.name = propertyName
      item.value = propertyValue
      properties.push(item)
    }
    return properties
  }
}
export default UpdateAliPayReceiveAccountVo
