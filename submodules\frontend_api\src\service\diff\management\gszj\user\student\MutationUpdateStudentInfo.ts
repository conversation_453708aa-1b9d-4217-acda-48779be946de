import { TokenResponse } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { Response } from '@hbfe/common'
// import UpdateStudentRequestVo from './vo/UpdateStudentRequestVo'
import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'
import MutationUpdateStudentInfo from '@api/service/management/user/mutation/student/MutationUpdateStudentInfo'
import PlatformTrainingChannelUser from '@api/platform-gateway/platform-training-channel-user-v1'
import UpdateUserDetailVo from '@api/service/diff/management/gszj/user/student/model/UpdateUserDetailVo'

/**
 * 修改学员信息
 */
class MutationUpdateStudentInfoDiff extends MutationUpdateStudentInfo {
  /**
   * 修改学员信息金昌
   */
  async doUpdateStudentInfoDiff(params: UpdateUserDetailVo): Promise<Response<TokenResponse>> {
    // 获取注册加密值
    const res = await ServicerSeriesV1Gateway.getStudentRegisterFormConstraint()
    params.encrypt = res?.data?.token
    return await PlatformTrainingChannelUser.updateStudentByAdmin(params.to())
  }
}

export default MutationUpdateStudentInfoDiff
