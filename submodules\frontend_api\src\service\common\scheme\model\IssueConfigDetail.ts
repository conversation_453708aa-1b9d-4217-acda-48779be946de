import { IssueAssessTypeEnum, TeachPlanAssessTypeEnum } from '@api/service/common/scheme/enum/AssessType'
import { AttendanceAssessRequireTypeEnum } from '@api/service/common/scheme/enum/AttendanceAssessRequireType'
import { BeginTimeTypeEnum } from '@api/service/common/scheme/enum/BeginTimeType'
import { EndTimeTypeEnum } from '@api/service/common/scheme/enum/EndTimeType'
import { RegisteredNumDisplayTypeEnum } from '@api/service/common/scheme/enum/RegisteredNumDisplayType'
import DateRange from '@api/service/common/scheme/model/DateRange'
import IssueCourseDetail from '@api/service/common/scheme/model/IssueCourseDetail'
import MultipleAssessLearningTypeInfo from '@api/service/common/scheme/model/MultipleAssessLearningTypeInfo'
import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
import Mockjs from 'mockjs'
import { IssueTrainingDateTypeEnum } from '@api/service/common/scheme/enum/IssueTrainingDateType'
import TeachingPlanItemsGroup from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/teaching-plan-items-groups/TeachingPlanItemsGroup'

/**
 * @description 期别配置详情
 */
class IssueConfigDetail extends MultipleAssessLearningTypeInfo<IssueAssessTypeEnum> {
  /**
   * 培训期别-通用培训须知
   * @description 这里的须知是每个期别都共享的，期别内须知见notice字段
   */
  commonNotice = ''
  /**
   * 期别配置id
   */
  trainingConfigConfigureId = ''
  /**
   * 关联的教学计划学习方式
   */
  relateTeachPlanLearning = new MultipleAssessLearningTypeInfo<TeachPlanAssessTypeEnum>()
  /**
   * 全期别问卷json备份Map
   * {
   *   key: 唯一标识uniqueKey值。规则：resourceId.UUID$uk001
   *   value: 期别配置
   * }
   */
  perQuestionnaireBackupMap = new Map<string, QuestionnaireConfigDetail>()

  /**
   * 教学计划教学模式 1-线下教学计划  2-线上教学计划  3-直播教学计划  10-混合教学计划
   */
  planMode = 1
  /**
   * 期别Id
   */
  id = ''
  /**
   * 期别名称
   */
  issueName = ''
  /**
   * 期别编号
   */
  issueNo = ''
  /**
   * 培训报到时段
   */
  checkDateRange = new DateRange()
  /**
   * 期别已报名人数
   * @description 门户期别商品展示的实际已报名人数
   */
  registeredCount = 0
  /**
   * 期别剩余可报名人数
   * @description 展示的是实际剩余可报名人数
   */
  remainingRegisterNumber = 0
  /**
   * 开放报名人数
   * @description 期别配置的计划报名人数
   */
  openRegistrationNum: number = null
  /**
   * 已报名人数展示类型
   */
  registeredNumDisplayType: RegisteredNumDisplayTypeEnum = RegisteredNumDisplayTypeEnum.read_enrollment
  /**
   * 已报名人数-固定显示数值
   */
  fixedRegisteredNum: number = null
  /**
   * 期别培训时段类型
   */
  issueTrainingDateType: IssueTrainingDateTypeEnum = IssueTrainingDateTypeEnum.custom
  /**
   * 培训时段
   */
  trainingDateRange = new DateRange()
  /**
   * 培训点适用范围 1公共
   */
  trainingPointNature = 1
  /**
   * 培训点配置id
   * @description 与UI无关，仅做业务流转
   */
  trainingPointConfigId = ''
  /**
   * 培训点Id
   */
  trainingPointId = ''
  /**
   * 培训点-经度
   */
  trainingPointLng = ''
  /**
   * 培训点-纬度
   */
  trainingPointLat = ''
  /**
   * 培训地点名称
   */
  trainingPointName = ''
  /**
   * 是否展示在门户，true-是，false-否
   */
  isShowInPortal = true
  /**
   *可见的购买渠道，1：用户自主购买，2：管理员
   */
  visibleChannelList: number[] = [1, 2]
  /**
   * 是否允许学员报名，true-允许，false-不允许
   */
  isEnableStudentEnroll = true
  /**
   * 期别开启报名时间类型
   */
  registerBeginTimeType: BeginTimeTypeEnum = BeginTimeTypeEnum.open_now
  /**
   * 期别开启报名时间
   */
  registerBeginTime = ''
  /**
   * 期别关闭报名时间类型
   */
  registerEndTimeType: EndTimeTypeEnum = EndTimeTypeEnum.no_end
  /**
   * 期别关闭报名时间
   */
  registerEndTime = ''
  /**
   * 是否开启报到
   */
  isOpenCheck = true
  /**
   * 是否开启住宿信息采集
   */
  isOpenAccommodationInfoCollect = true
  /**
   * 住宿信息采集须知
   */
  accommodationInfoCollectNotice = ''
  /**
   * 是否开启结业测试
   */
  isOpenGraduationTest = true
  /**
   * 是否开启考勤
   */
  isOpenAttendance = false
  /**
   * 考勤考核要求类型
   */
  attendanceAssessRequireType: AttendanceAssessRequireTypeEnum = null
  /**
   * 期别要求考勤率
   */
  attendanceRate: number = null
  /**
   * 班主任姓名
   */
  headTeacherName = ''
  /**
   * 班主任联系电话
   */
  headTeacherPhone = ''
  /**
   * 酒店联系人姓名
   */
  hotelContactsName = ''
  /**
   * 酒店联系人电话
   */
  hotelContactsPhone = ''
  /**
   * 学员须知
   */
  notice = ''
  /**
   * 教学计划组备份数组
   */
  teachingPlanItemsGroupsBackup = [] as TeachingPlanItemsGroup[]
  /**
   * 期别课程列表
   */
  issueCourseList: IssueCourseDetail[] = []
  /**
   * 问卷配置列表，仅查看
   */
  questionnaireList: QuestionnaireConfigDetail[] = []

  /**
   * 期别学时成果id
   */
  periodCreditId = ''
  /**
   * 期别总学时
   */
  periods = 0
  /**
   * 期别下课程总学时
   */
  get issueCourserPeriodTotal() {
    return (
      this.issueCourseList.reduce((prev, cur) => {
        return prev + cur.coursePeriod
      }, 0) || 0
    )
  }

  constructor() {
    super()
    this.id = Mockjs.Random.guid()
  }
}

export default IssueConfigDetail
