import MsAuthenticationSecureConfig, {
  CredentialRequest,
  FailedAuthConfigResponse,
  SmsCodeAuthConfigResponse
} from '@api/ms-gateway/ms-identity-authentication-secure-config-v1'
import ServicerSeriesV1Gateway, {
  StudentForceModifyInitialPasswordConfigRequest,
  ValidateMethodConfigRequest
} from '@api/ms-gateway/ms-servicer-series-v1'
import { GrantType } from '@api/service/common/authentication'
import Authentication from '@api/service/common/authentication/Authentication'
import QueryIdCardType from '@api/service/common/basic-data-dictionary/query/QueryIdCardType'
import IdRegisterCardTypeVo from '@api/service/common/basic-data-dictionary/query/vo/IdRegisterCardTypeVo'
import { VerifyTypeEnum } from '@api/service/management/online-school-config/functionality-setting/enum/VerifyEnum'
import UnitRegisterVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/registerAndLogin/UnitRegisterVo'
import { Response, ResponseStatus } from '@hbfe/common'
import AgreementSettingVo from './vo/registerAndLogin/AgreementSettingVo'
import GeneralLoginSettingVo from './vo/registerAndLogin/GeneralLoginSettingVo'
import LoginSettingVo from './vo/registerAndLogin/LoginSetingVo'
import RegisterSettingVo from './vo/registerAndLogin/RegisterSettingVo'
export enum RegisterSettingEnum {
  'PERSON' = 1,
  'CONSTRUCT' = 2,
  'HYGIENE' = 3,
  'DILIGENCE' = 4,
  'TEACHER' = 5,
  'PHARMACIST' = 6
}

class MutationRegisterAndLogin {
  loginSetting = new LoginSettingVo()
  registerSetting = new RegisterSettingVo()
  $authentication: Authentication

  /**
   * 查询注册设置
   * @returns ResponseStatus
   */
  async queryRegisterSetting(): Promise<ResponseStatus> {
    const res = await ServicerSeriesV1Gateway.getStudentResisterFormConstraintForConfig()
    if (res.status.isSuccess()) {
      this.registerSetting.enable = res.data.enabled
      this.registerSetting.from(res.data.fieldConstraints)
    }
    const idCardRes = await this.queryRegisterSettingFormIdCardTypeList()
    this.registerSetting.studentRegister.idCardType = idCardRes.data
    const unitRes = await ServicerSeriesV1Gateway.getDockingTycAndQcc()
    if (unitRes.status.isSuccess()) {
      this.registerSetting.unitRegister = UnitRegisterVo.from(unitRes.data)
    }
    if (!res.status.isSuccess()) {
      return res.status
    } else if (!idCardRes.status.isSuccess()) {
      return idCardRes.status
    } else {
      return new ResponseStatus(200)
    }
  }

  /**
   * 查询证件类型注册设置
   */
  async queryRegisterSettingFormIdCardTypeList() {
    const result = new Response<IdRegisterCardTypeVo[]>()
    const idCardRes = await ServicerSeriesV1Gateway.getStudentRegisterFormIdCardTypeList()
    result.status = idCardRes.status
    if (result.status.isSuccess()) {
      const data = await QueryIdCardType.queryIdCardType()
      result.data = idCardRes.data?.responseList?.map(item => IdRegisterCardTypeVo.fromRegister(item, data)) || []
    }
    return result
  }
  /**
   * 查询登录设置
   * @returns ResponseStatus
   */
  async queryLoginSetting(): Promise<ResponseStatus> {
    const res = await ServicerSeriesV1Gateway.getWechatLoginConfig()
    if (res.status.isSuccess()) {
      this.loginSetting.from(res.data)
    }
    return res.status
  }

  /**
   * 保存注册设置
   * @returns ResponseStatus
   * @param type 保存行业类型 1.人社 2.建社 3.人社建设
   */
  async saveRegisterSetting(type?: RegisterSettingEnum[]): Promise<ResponseStatus> {
    let result = new ResponseStatus(200)
    const res = await ServicerSeriesV1Gateway.saveStudentRegisterFormConstraintConfig(this.registerSetting.to(type))
    if (res.status.isSuccess()) {
      const responseList = this.registerSetting.studentRegister.idCardType.map(IdRegisterCardTypeVo.toRegister)
      const IdCardRes = await ServicerSeriesV1Gateway.saveStudentRegisterFormIdCardTypeList({
        responseList
      })
      if (IdCardRes.status.isSuccess()) {
        result = IdCardRes.status
      } else {
        result = IdCardRes.status
      }
    } else {
      result = res.status
      return result
    }

    // 更新工作单位配置
    const unitSettingRequest = this.registerSetting.unitRegister.toRequest()
    const unitSettingRes = await ServicerSeriesV1Gateway.saveDockingTycAndQccConfig(unitSettingRequest)

    if (!unitSettingRes.status.isSuccess()) {
      result = unitSettingRes.status
    }
    return result
  }

  /**
   * 保存登录设置
   * @returns ResponseStatus
   */
  async saveLoginSetting(): Promise<ResponseStatus> {
    const res = await ServicerSeriesV1Gateway.saveWechatLoginConfig(this.loginSetting.to())
    return res.status
  }

  /**
   * 保存通用登录规则
   */
  async saveCommonLoginSetting(): Promise<ResponseStatus> {
    const param = new StudentForceModifyInitialPasswordConfigRequest()
    param.enabledModifyInitPassword = this.loginSetting.generalLogin.forcePwdChange
    const res = await ServicerSeriesV1Gateway.saveForceModifyInitialPassword(param)
    return res.status
  }
  /**
   * 获取登录强制修改密码配置
   */
  async getLoginForceModifyPassword(): Promise<ResponseStatus> {
    const res = await ServicerSeriesV1Gateway.getForceModifyInitialPassword()
    if (res.status.isSuccess()) {
      if (res.data?.enabledModifyInitPassword) {
        this.loginSetting.generalLogin.forcePwdChange = res.data.enabledModifyInitPassword
      } else {
        this.loginSetting.generalLogin.forcePwdChange = false
      }
    }
    return res.status
  }

  /**
   * 保存验证码认证方式
   */
  async saveVerificationCodeSetting(type: VerifyTypeEnum) {
    const request = new ValidateMethodConfigRequest()
    if (type === VerifyTypeEnum.slide) {
      request.enabledCaptcha = false
      request.enabledSlideCircus = true
    } else if (type === VerifyTypeEnum.captcha) {
      request.enabledCaptcha = true
      request.enabledSlideCircus = false
    } else {
      request.enabledCaptcha = false
      request.enabledSlideCircus = false
    }
    const res = await ServicerSeriesV1Gateway.saveValidateMethodConfig(request)
    return res.status
  }

  /**
   * 保存单点安全配置
   */
  async saveSinglePointSecurityConfig() {
    const generalLogin = this.loginSetting.generalLogin
    const request = GeneralLoginSettingVo.to(generalLogin)
    const res = await MsAuthenticationSecureConfig.saveSSOSecureConfig(request)
    if (res.status.isSuccess() && res.data?.code != '200') {
      return new ResponseStatus(500, res.data?.message)
    }
    return res.status
  }

  /**
   * 获取单点安全配置
   */
  async getSinglePointSecurityConfig() {
    const res = await MsAuthenticationSecureConfig.getSSOSecureConfig()

    if (res.status.isSuccess()) {
      const temp = this.loginSetting.generalLogin.forcePwdChange
      this.loginSetting.generalLogin = GeneralLoginSettingVo.from(res.data)
      if (temp) {
        this.loginSetting.generalLogin.forcePwdChange = temp
      }
    }
    return res.status
  }
  /**
   * 获取短信认证配置
   * @param token 初始化令牌
   * @param grantType  调用类型 默认为身份凭证Token(默认授权类型)
   */
  async getSmsCodeAuthConfig(token: string, grantType?: GrantType) {
    const request = new CredentialRequest()
    request.token = token
    request.grantType = grantType
    const res = await MsAuthenticationSecureConfig.getSmsCodeAuthConfig(request)
    if (res.status.isSuccess()) {
      return res.data
    } else {
      return new SmsCodeAuthConfigResponse()
    }
  }
  /**
   * 获取失败认证配置
   * @param token 初始化令牌
   * @param grantType  调用类型 默认为身份凭证Token(默认授权类型)
   */
  async getFailedAuthConfig(token: string, grantType?: GrantType) {
    const request = new CredentialRequest()
    request.token = token
    request.grantType = grantType
    const res = await MsAuthenticationSecureConfig.getFailedAuthConfig(request)
    if (res.status.isSuccess()) {
      return res.data
    } else {
      return new FailedAuthConfigResponse()
    }
  }

  /**
   * 查询网校协议设置
   */
  async getOnlineSchoolAgreementSetting() {
    const res = await ServicerSeriesV1Gateway.getOnlineSchoolProtolConfig()
    if (res.status.isSuccess() && res.data) {
      const detail = AgreementSettingVo.from(res.data)
      return detail
    } else {
      return new AgreementSettingVo()
    }
  }
  /**
   * 保存网校协议设置
   */
  async saveOnlineSchoolAgreementSetting(params: AgreementSettingVo) {
    const request = AgreementSettingVo.to(params)
    const res = await ServicerSeriesV1Gateway.saveOnlineSchoolProtocolConfig(request)
    return res.status
  }
}
export default MutationRegisterAndLogin
