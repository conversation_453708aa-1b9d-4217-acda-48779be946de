<template>
  <el-card
    shadow="never"
    class="m-card f-mb15"
    v-if="$hasPermission('autoInvoice,autoInvoiceZt')"
    desc="autoInvoice:电子普通发票（自动开票）,autoInvoiceZt:电子普通发票（专题）"
    actions="autoInvoice:created#autoInvoiceZt:created"
  >
    <!--条件查询-->
    <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
    <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
    <!--    <el-row :gutter="16" class="m-query is-border-bottom">
      <el-form :inline="true" label-width="auto">
        <el-col :sm="12" :md="8" :xl="6">-->
    <hb-search-wrapper :model="pageQueryParam" @reset="resetCondition">
      <el-form-item label="培训方案" v-if="!isZtlogin">
        <learning-scheme-select-diff v-model="hasSelectSchemeMode"></learning-scheme-select-diff>
      </el-form-item>
      <el-form-item label="培训方案" v-if="isZtlogin">
        <zt-learning-scheme-select v-model="hasSelectSchemeMode"></zt-learning-scheme-select>
      </el-form-item>
      <el-form-item label="期别名称" v-if="showPeriodName">
        <biz-period-select :scheme-id="hasSelectSchemeMode[0].id" v-model="pageQueryParam.periodId" />
      </el-form-item>
      <!--        </el-col>
        <el-col :sm="12" :md="8" :xl="6">-->
      <el-form-item label="订单号">
        <el-input v-model="pageQueryParam.orderNoList" clearable placeholder="请输入订单号" />
      </el-form-item>
      <!--        </el-col>
        <el-col :sm="12" :md="8" :xl="6">-->
      <el-form-item label="姓名">
        <el-input v-model="pageQueryParam.userName" clearable placeholder="请输入购买人姓名" />
      </el-form-item>
      <!--        </el-col>
        <el-col :sm="12" :md="8" :xl="6">-->
      <el-form-item label="登录账号" v-if="queryShowLoginAccount.isShowLoginAccount">
        <el-input v-model="pageQueryParam.loginAccount" clearable placeholder="请输入省平台id" />
      </el-form-item>
      <el-form-item label="证件号">
        <el-input v-model="pageQueryParam.idCard" clearable placeholder="请输入证件号" />
      </el-form-item>
      <!--        </el-col>
        <el-col :sm="12" :md="8" :xl="6">-->
      <el-form-item label="手机号">
        <el-input v-model="pageQueryParam.phone" clearable placeholder="请输入购买人手机号" />
      </el-form-item>
      <!--        </el-col>
        <el-col :sm="12" :md="8" :xl="6">-->
      <el-form-item label="发票状态">
        <el-select v-model="pageQueryParam.blueInvoiceItemBillStatusList" clearable placeholder="请选择发票状态">
          <el-option v-for="item in blueInvoiceStatus" :label="item.name" :value="item.value" :key="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <!--        </el-col>
        <el-col :sm="12" :md="8" :xl="6">-->
      <el-form-item label="冻结状态">
        <el-select v-model="pageQueryParam.invoiceFreezeStatus" clearable filterable placeholder="请选择冻结状态">
          <el-option label="正常" :value="false"></el-option>
          <el-option label="冻结" :value="true"></el-option>
        </el-select>
      </el-form-item>
      <!--        </el-col>
        <el-col :sm="12" :md="8" :xl="6">-->
      <el-form-item label="发票号">
        <el-input v-model="pageQueryParam.blueBillNo" clearable placeholder="请输入发票号" />
      </el-form-item>
      <!--        </el-col>
        <el-col :sm="12" :md="8" :xl="6">-->
      <el-form-item label="是否冲红">
        <el-select
          v-model="pageQueryParam.flushed"
          @change="changeRedStatus"
          clearable
          filterable
          placeholder="是否冲红"
        >
          <el-option label="是" :value="true"></el-option>
          <el-option label="否" :value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="冲红状态" v-show="pageQueryParam.flushed">
        <el-select v-model="pageQueryParam.redInvoiceItemBillStatusList" clearable placeholder="请选择冲红状态">
          <el-option
            v-for="item in redInvoiceStatus"
            :label="item.name"
            :value="item.value"
            :key="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <!--        </el-col>
        <el-col :sm="12" :md="8" :xl="6">-->

      <el-form-item label="销售渠道" v-if="!isZtlogin">
        <el-select v-model="pageQueryParam.saleSource" clearable filterable placeholder="请选择销售渠道">
          <el-option v-for="item in saleChannelList" :key="item.code" :value="item.code" :label="item.desc"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="专题名称" v-if="topPicNameFilterShow">
        <el-input v-model="pageQueryParam.specialSubjectName" clearable placeholder="请输入专题进行查询" />
      </el-form-item>
      <el-form-item label="申请开票时间">
        <double-date-picker
          :begin-create-time.sync="pageQueryParam.blueBillStatusChangeTime.billing.begin"
          :end-create-time.sync="pageQueryParam.blueBillStatusChangeTime.billing.end"
          begin-time-placeholder="请选择申请开票时间开始时间"
          end-time-placeholder="请选择申请开票时间结束时间"
        ></double-date-picker>
      </el-form-item>

      <el-form-item label="开票时间">
        <double-date-picker
          :begin-create-time.sync="pageQueryParam.blueBillStatusChangeTime.success.begin"
          :end-create-time.sync="pageQueryParam.blueBillStatusChangeTime.success.end"
          begin-time-placeholder="请选择开票开始时间"
          end-time-placeholder="请选择开票开始时间"
        ></double-date-picker>
      </el-form-item>

      <!--        </el-col>
        <el-col :sm="12" :md="8" :xl="6" class="f-fr">-->
      <template slot="actions">
        <el-button type="primary" @click="doQuery()">查询</el-button>
        <template
          v-if="$hasPermission('autoInvoiceExport,autoInvoiceExportZt')"
          desc="autoInvoiceExport:电子普通发票（自动开票）-导出数据,autoInvoiceExportZt:电子普通发票（专题）-导出数据"
          actions="autoInvoiceExport:exportSpecialInvoice#autoInvoiceExportZt:exportSpecialInvoice"
        >
          <el-button @click="exportSpecialInvoice">导出列表数据</el-button>
        </template>
        <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
      </template>
    </hb-search-wrapper>
    <!--        </el-col>
      </el-form>
    </el-row>-->
    <!--操作栏-->
    <div class="f-mt20">
      <template v-if="$hasPermission('autoInvoiceBatch')" desc="批量开票" actions="doBatchInvoice">
        <el-button v-if="!isZtlogin" type="primary" @click="sureBatchInvoice">批量开票</el-button>
      </template>
      <template v-if="$hasPermission('reBatchOpenInvoice')" desc="重新批量开通发票" actions="reBatchOpenInvoice">
        <el-tooltip
          effect="dark"
          content="重试开票仅针对因发票数量不足，开票状态是“开票中”的情况。请确认已补票，并勾选开票中的发票，操作重试发票。"
          placement="top"
        >
          <el-button type="primary" :disabled="isRe" v-if="!isZtlogin" @click="reBatchOpenInvoice(1)"
            >重试开票</el-button
          >
        </el-tooltip>
      </template>
      <template v-if="$hasPermission('autoInvoiceTime')" desc="设置自动开票时间" actions="@SetInvoiceDayDialog">
        <el-button type="primary" plain v-if="!isZtlogin" @click="openAutoInvoiceDialog">设置自动开票时间</el-button>
      </template>
      <span>
        <span v-if="isAutoInvoice" class="f-ml15 f-cr">注：订单索要发票 {{ autoInvoiceDay }} 天后将自动开票。</span>
        <span class="f-ml15 f-cr" v-else>注：请设置自动开票时间，否则无法开票。</span>
      </span>
    </div>
    <div class="f-mt20">
      <el-alert type="warning" :closable="false" class="m-alert">
        <div class="f-c6">
          搜索结果合计：开票总金额
          <span class="f-fb f-co">¥ {{ totalAmount }}</span>
          ，开票税额总金额
          <span class="f-fb f-co">¥ {{ totalTax }}</span>
        </div>
      </el-alert>
    </div>
    <!--表格-->
    <el-table
      stripe
      :data="pageData"
      @selection-change="tableSelect"
      class="m-table f-mt10"
      v-loading="query.loading"
      ref="autoInvoiceTable"
    >
      <el-table-column
        type="selection"
        v-if="!isZtlogin"
        width="55"
        align="center"
        fixed="left"
        :selectable="getSelectAble"
      ></el-table-column>
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="订单号" min-width="230" fixed="left">
        <template #default="scope">
          {{ scope.row.orderNo }}
          <el-tag type="primary" size="small" v-if="scope.row.spilt">拆票</el-tag>
          <el-tag type="success" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag>
          <el-tag type="warning" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.distribution"
            >分销推广
          </el-tag>
          <el-tag type="danger" size="small" v-if="scope.row.thirdPartyPlatform"
            >{{ scope.row.thirdPartyPlatform }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="退款状态" min-width="130">
        <template slot-scope="scope">
          <el-badge
            is-dot
            :type="refundStatusMapType[scope.row.orderReturnStatus]"
            class="badge-status"
            v-if="refundStatusMapName[scope.row.orderReturnStatus]"
          >
            {{ refundStatusMapName[scope.row.orderReturnStatus] }}</el-badge
          >

          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="付款金额(元)" width="140" align="right" prop="payAmount">
        <template #default="scope">
          {{ scope.row.payAmount || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="开票金额(元)" width="140" align="right" prop="blueTotalAmount">
        <template #default="scope">
          {{ scope.row.blueTotalAmount || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="税额(元)" width="140" align="right" prop="totalTax">
        <template #default="scope">
          {{ scope.row.totalTax || 0 }}
        </template>
      </el-table-column>
      <el-table-column label="购买人信息" min-width="240">
        <template #default="scope">
          <p>姓名：{{ scope.row.name || '-' }}</p>
          <p>证件号：{{ scope.row.idCard || '-' }}</p>
          <p>手机号：{{ scope.row.phone || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="发票抬头" min-width="300">
        <template #default="scope">【{{ invoiceTitleMapType[scope.row.titleType] }}】{{ scope.row.title }}</template>
      </el-table-column>
      <el-table-column label="统一社会信用代码" min-width="180" prop="taxpayerNo">
        <template #default="scope">
          {{ scope.row.taxpayerNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="申请开票时间" min-width="180" prop="applyForDate">
        <template #default="scope">
          {{ scope.row.applyForDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票状态" min-width="130">
        <template #default="scope">
          <el-tooltip
            class="item"
            effect="dark"
            :content="scope.row.blueInvoiceExceptionMsg ? `异常说明：${scope.row.blueInvoiceExceptionMsg}` : '暂无数据'"
            placement="top"
            v-if="scope.row.invoiceStatus === InvoiceStatusEnum.OPENING"
          >
            <el-badge is-dot :type="invoiceStatusMapType[scope.row.invoiceStatus]" class="badge-status">
              {{ invoiceStatusMapName[scope.row.invoiceStatus] }}
            </el-badge>
          </el-tooltip>
          <el-badge
            is-dot
            :type="invoiceStatusMapType[scope.row.invoiceStatus]"
            class="badge-status"
            v-else-if="invoiceStatusMapName[scope.row.invoiceStatus]"
          >
            {{ invoiceStatusMapName[scope.row.invoiceStatus] }}
          </el-badge>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="开票时间" min-width="180" prop="invoiceDate">
        <template #default="scope">
          {{ scope.row.invoiceDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票号" min-width="120" prop="blueInvoiceNo">
        <template #default="scope">
          {{ scope.row.blueInvoiceNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="是否冻结" min-width="120">
        <template #default="scope">{{ scope.row.invoiceFreezeStatus ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template #default="scope">
          <template v-if="$hasPermission('editBill')" desc="修改电子普通发票信息" actions="@EditInvoiceDialog">
            <el-button
              type="text"
              size="mini"
              v-if="(scope.row.invoiceStatus == 0 || scope.row.invoiceStatus == 3) && !scope.row.useless && !isZtlogin"
              @click="editInvoice(scope.row)"
              >修改发票信息
            </el-button>
          </template>
          <template v-if="$hasPermission('reOpenInvoice')" desc="重新开通发票" actions="reOpenInvoice">
            <el-button
              type="text"
              size="mini"
              v-if="scope.row.invoiceStatus == 2 && scope.row.redStatus == 0 && !scope.row.useless && !isZtlogin"
              @click="reOpenInvoice(scope.row)"
              >重新开票
            </el-button>
          </template>
          <el-button type="text" size="mini" v-if="scope.row.invoiceStatus == 2" @click="downloadInvoice(scope.row)"
            >下载发票</el-button
          >
          <el-button type="text" size="mini" v-if="scope.row.redStatus == 2" @click="redInvoice(scope.row.redFilePath)"
            >冲红发票</el-button
          >
          <el-button
            type="text"
            size="mini"
            v-if="$hasPermission('operationRecord')"
            desc="操作记录"
            actions="operationRecord"
            @click="operationRecord(scope.row)"
            >记录
          </el-button>
          <!--暂时隐藏发票操作记录
                    <el-button type="text" size="mini" @click="invoiceLog">记录</el-button>-->
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"></hb-pagination>
    <el-dialog title="提示" :visible.sync="batchDialog" width="450px" class="m-dialog">
      <div>请选择开票对象</div>
      <div slot="footer">
        <el-button type="primary" @click="batchDialog = false">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="sureBatchDialog" width="450px" class="m-dialog">
      <div>确定要批量开票所选发票吗？</div>
      <div slot="footer">
        <el-button @click="sureBatchDialog = false">取 消</el-button>
        <el-button type="primary" @click="doBatchInvoice">确 定</el-button>
      </div>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务管理-个人报名增值税电子普通发票（自动开票）</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
    <set-invoice-day-dialog
      :auto-invoice-dialog.sync="autoInvoiceDialog"
      :invoice-day="invoiceDay"
      @callBack="getAutoInvoiceDay"
    ></set-invoice-day-dialog>
    <invoice-log-dialog :log-dialog.sync="logDialog"></invoice-log-dialog>
    <edit-invoice-dialog
      :dialogType="editInvoiceDialogTitleType"
      :dialog-ctrl.sync="editInvoiceVisible"
      :invoice-id="invoiceId"
      @callBack="doSearch"
    ></edit-invoice-dialog>

    <!-- 操作记录弹窗 -->
    <el-drawer title="操作记录" :visible.sync="operationRecordDialog" size="800px" custom-class="m-drawer">
      <div class="drawer-bd">
        <div class="f-mt20 f-mlr40" v-show="operationRecordData.length > 0">
          <el-timeline>
            <el-timeline-item v-for="item in operationRecordData" :key="item.operatorUserId">
              {{ item.operationStr }}
            </el-timeline-item>
          </el-timeline>
        </div>
        <el-alert type="info" v-show="operationRecordData.length <= 0" :closable="false" class="m-alert">
          <div class="f-ptb30 f-tc">该发票暂时还没有操作记录！</div>
        </el-alert>
      </div>
    </el-drawer>
    <invoice-dialog
      ref="InvoiceDialog"
      :paperPdfUrl="curItem.blueFilePath"
      :ofdUrl="curItem.blueFileOfdPath"
      :xmlUrl="curItem.blueFileXmlPath"
    ></invoice-dialog>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, Watch, Ref } from 'vue-property-decorator'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import { Query, UiPage } from '@hbfe/common'
  import EditInvoiceDialog from '@hbfe/jxjy-admin-components/src/trade/personal/edit-invoice-dialog.vue'
  import SetInvoiceDayDialog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/set-invoice-day-dialog.vue'
  import InvoiceLogDialog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/invoice-log-dialog.vue'
  import QueryPageInvoiceParam from '@api/service/management/trade/single/invoice/query/dto/QueryPageInvoiceParam'
  import InvoiceListResponse from '@api/service/diff/management/qztg/trade/invoice/model/InvoiceListResponse'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import QueryOperationInvoiceLog from '@api/service/management/trade/single/invoice/query/QueryOperationInvoiceLog'
  import OperationLogItem from '@api/service/management/trade/single/invoice/mutation/vo/OperationLogItem'
  import InvoiceDialog from '@hbfe/jxjy-admin-trade/src/order/personal/components/invoice-dialog.vue'
  import {
    InvoiceStatusEnum,
    TitleTypeEnum,
    OrderReturnStatusEnum
  } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import {
    BillStatusChangeTimeRequest,
    DateScopeRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/qztg/trade/enums/SaleChannelType'
  import FileModule from '@api/service/common/file/FileModule'
  import QueryInvoice from '@api/service/diff/management/qztg/trade/invoice/QueryInvoice'
  import LearningSchemeSelectDiff from '@hbfe/jxjy-admin-trade/src/diff/qztg/order/personal/components/learning-scheme-select-diff.vue'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryInvoiceInTrainingChannel from '@api/service/diff/management/qztg/trade/invoice/QueryQztgInvoiceInTrainingChannel'
  import { bind, debounce } from 'lodash-decorators'
  import OrderRefundStatus from '@api/service/common/return-order/enums/OrderRefundStatus'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import ZtLearningSchemeSelect from '@hbfe/jxjy-admin-trade/src/order/personal/components/zt-learning-scheme-select.vue'
  @Component({
    components: {
      InvoiceLogDialog,
      DoubleDatePicker,
      EditInvoiceDialog,
      SetInvoiceDayDialog,
      LearningSchemeSelectDiff,
      InvoiceDialog,
      ZtLearningSchemeSelect
    }
  })
  export default class extends Vue {
    @Ref('InvoiceDialog') InvoiceDialog: InvoiceDialog
    pageQueryParam: QueryPageInvoiceParam = new QueryPageInvoiceParam()
    exportQueryParam: QueryPageInvoiceParam = new QueryPageInvoiceParam()
    //操作记录弹窗是否显示
    operationRecordDialog = false
    //操作记录数组
    operationRecordData: Array<OperationLogItem> = new Array<OperationLogItem>()
    input = ''
    invoiceId = ''
    hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
    page: UiPage
    query: Query = new Query()
    pageData: Array<InvoiceListResponse> = new Array<InvoiceListResponse>()
    selectInvoiceList: Array<InvoiceListResponse> = new Array<InvoiceListResponse>()
    SaleChannelEnum = SaleChannelEnum
    InvoiceStatusEnum = InvoiceStatusEnum
    /**
     * 实例化
     */
    queryInvoice = new QueryInvoice()

    //批量开票提示弹窗
    batchDialog = false
    //确定批量开票提示弹窗
    sureBatchDialog = false
    //自动开票天数设置弹窗
    autoInvoiceDialog = false
    //修改发票弹窗
    editInvoiceVisible = false
    //导出成功弹窗
    exportSuccessVisible = false
    //操作日志弹窗
    logDialog = false
    //默认开票天数
    invoiceDay = 7
    //自动开票天数
    autoInvoiceDay = 0
    //开票总金额
    totalAmount = 0
    //批量重新开票是否可以使用
    isRe = true
    //发票总税额
    totalTax = 0
    /**
     * 是否配置自动开票
     */
    isAutoInvoice = false
    // 当前项
    curItem = new InvoiceListResponse()
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    //专题查询请求
    queryZtInvoice = new QueryInvoiceInTrainingChannel()
    //退货状态
    OrderRefundStatus = OrderRefundStatus

    //发票状态
    blueInvoiceStatus = [
      {
        name: '待开票',
        value: InvoiceStatusEnum.NOTPTOOPEN
      },
      {
        name: '开票中',
        value: InvoiceStatusEnum.OPENING
      },
      {
        name: '开票成功',
        value: InvoiceStatusEnum.OPEMSUCCESS
      },
      {
        name: '开票失败',
        value: InvoiceStatusEnum.OPENERROR
      }
    ]
    //修改发票/重新开票弹窗状态 0：修改发票 1：重新开票
    editInvoiceDialogTitleType = 0
    redInvoiceStatus = [
      {
        name: '请选择冲红状态',
        value: null
      },
      {
        name: '冲红中',
        value: InvoiceStatusEnum.OPENING
      },
      {
        name: '冲红失败',
        value: InvoiceStatusEnum.OPENERROR
      },
      {
        name: '冲红成功',
        value: InvoiceStatusEnum.OPEMSUCCESS
      }
    ]

    invoiceStatusMapName = {
      [InvoiceStatusEnum.NOTPTOOPEN]: '待开票',
      [InvoiceStatusEnum.OPENING]: '开票中',
      [InvoiceStatusEnum.OPENERROR]: '开票失败',
      [InvoiceStatusEnum.OPEMSUCCESS]: '开票成功'
    }

    invoiceStatusMapType = {
      [InvoiceStatusEnum.NOTPTOOPEN]: 'info',
      [InvoiceStatusEnum.OPENING]: 'primary',
      [InvoiceStatusEnum.OPENERROR]: 'danger',
      [InvoiceStatusEnum.OPEMSUCCESS]: 'success'
    }

    refundStatusMapName = {
      [OrderReturnStatusEnum.RETURNSING]: '退款处理中',
      [OrderReturnStatusEnum.RETURNSUCCESS]: '退款成功'
    }

    refundStatusMapType = {
      [OrderReturnStatusEnum.RETURNSING]: 'primary',
      [OrderReturnStatusEnum.RETURNSUCCESS]: 'success'
    }

    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }

    // 获取销售渠道列表
    saleChannelList = SaleChannelType.list()
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    TrainingModeEnum = TrainingModeEnum
    // 专题名称筛选显示
    get topPicNameFilterShow() {
      return (
        this.pageQueryParam.saleSource === SaleChannelEnum.topic ||
        (!this.pageQueryParam.saleSource && this.pageQueryParam.saleSource !== SaleChannelEnum.self)
      )
    }

    constructor() {
      super()
      this.pageQueryParam.blueBillStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.blueBillStatusChangeTime.billing = new DateScopeRequest()
      this.pageQueryParam.blueBillStatusChangeTime.success = new DateScopeRequest()
      if (this.isZtlogin) {
        this.page = new UiPage(this.doSearchZt, this.doSearchZt)
      } else {
        this.page = new UiPage(this.doSearch, this.doSearch)
      }
    }
    get showPeriodName() {
      return [this.TrainingModeEnum.mixed, this.TrainingModeEnum.offline].includes(
        this.hasSelectSchemeMode[0]?.trainingMode?.skuPropertyValueId
      )
    }
    // 培训方案入参
    @Watch('hasSelectSchemeMode', {
      deep: true
    })
    changeScheme(val: Array<HasSelectSchemeMode>) {
      this.pageQueryParam.periodId = ''
    }
    @Watch('selectInvoiceList', { deep: true })
    getSelectInvoiceList() {
      // console.log(this.selectInvoiceList)
      if (this.selectInvoiceList.length <= 0) {
        this.isRe = true
        return
      }
      this.isRe = false
      this.selectInvoiceList.forEach((item) => {
        if (item.invoiceStatus !== 1) {
          // console.log(this.isRe)
          this.isRe = true
          return
        }
      })
    }

    //修改发票信息
    editInvoice(item: InvoiceListResponse) {
      if (item.invoiceFreezeStatus) {
        this.$message.warning('发票已冻结，无法修改！')
        return
      }
      this.editInvoiceDialogTitleType = 0
      this.invoiceId = item.invoiceId
      this.editInvoiceVisible = true
    }
    @bind
    @debounce(200)
    async doQuery() {
      this.page.pageNo = 1
      if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }

    async doSearch() {
      this.query.loading = true
      if (this.hasSelectSchemeMode.length) {
        this.pageQueryParam.commoditySkuIdList = this.hasSelectSchemeMode[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }
      try {
        this.pageData = await this.queryInvoice.onLinePageInvoiceInServicer(this.page, this.pageQueryParam)
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['autoInvoiceTable'] as any)?.doLayout()
        this.totalAmount = this.queryInvoice.totalAmount
        this.totalTax = this.queryInvoice.totalTax
        this.query.loading = false
      }
    }
    async doSearchZt() {
      this.query.loading = true
      if (this.hasSelectSchemeMode.length) {
        this.pageQueryParam.commoditySkuIdList = this.hasSelectSchemeMode[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }
      try {
        this.pageData = await this.onLinePageInvoiceInZtServicer()
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['autoInvoiceTable'] as any)?.doLayout()
        this.totalAmount = this.queryZtInvoice.totalAmount
        this.totalTax = this.queryZtInvoice.totalTax
        this.query.loading = false
      }
    }
    /**
     * 分页查询发票
     */
    async onLinePageInvoiceInZtServicer() {
      return await this.queryZtInvoice.onLineQztgPageInvoiceInServicer(this.page, this.pageQueryParam)
    }
    tableSelect(selection: any) {
      this.selectInvoiceList = selection
    }

    getSelectAble(val: InvoiceListResponse) {
      //退款中、退款成功状态的不支持勾选
      if (
        val.orderReturnStatus == 1 ||
        val.orderReturnStatus == 2 ||
        val.invoiceFreezeStatus ||
        val.invoiceStatus == 2 ||
        val.useless
      ) {
        return false
      } else {
        return true
      }
    }
    /**
     * 重新开票
     * @param
     */
    reOpenInvoice(val: InvoiceListResponse) {
      this.editInvoiceDialogTitleType = 1
      this.invoiceId = val.invoiceId
      this.editInvoiceVisible = true
      //重新开票
    }

    /**
     * 重新批量开票
     * @param type 1：蓝票 2：红票
     */
    async reBatchOpenInvoice(type: number) {
      const invoiceBatchList = this.selectInvoiceList.map((item) => {
        return {
          invoiceId: item.invoiceId,
          billType: type
        }
      })
      this.$alert('确定要批量对所选发票进行重试开票吗？', '提示', {
        showCancelButton: true
      }).then(() => {
        TradeModule.singleTradeBatchFactor.invoiceFactor.mutationInvoice
          .batchRetryInvoice(invoiceBatchList)
          .then((res) => {
            if (res.status.isSuccess()) {
              this.$alert('已通知票务系统重试开票，请耐心等待。', '提示', {
                confirmButtonText: '我知道了'
              })
            } else {
              this.$message.error(res.status?.errors[0]?.message || '批量重试开票失败')
            }
            this.doSearch()
          })
      })

      //重新批量开票
    }
    changeRedStatus() {
      if (!this.pageQueryParam.flushed) {
        this.pageQueryParam.redInvoiceItemBillStatusList = null
      }
    }

    sureBatchInvoice() {
      if (!this.selectInvoiceList || this.selectInvoiceList.length <= 0) {
        this.batchDialog = true
        return
      }
      this.sureBatchDialog = true
    }

    //批量开票
    async doBatchInvoice() {
      this.sureBatchDialog = false
      const selectInvoiceIdList = new Array<string>()
      this.selectInvoiceList.forEach((item) => {
        selectInvoiceIdList.push(item.invoiceId)
      })
      const res = await TradeModule.singleTradeBatchFactor.invoiceFactor.mutationInvoice.issueElectronicInvoice(
        selectInvoiceIdList
      )
      if (!res.length) {
        this.$message.success('批量开票成功')
      } else {
        this.$message.error('批量开票失败')
      }
      await this.doSearch()
    }

    async getAutoInvoiceDay() {
      // 获取自动开票时间
      const res = await this.queryInvoice.getInvoiceAutoBillPolicyInServicer()
      this.isAutoInvoice = res?.id ? true : false
      this.autoInvoiceDay = res?.intervalHours / 24
    }

    /**
     * 操作记录
     * @param val
     */
    async operationRecord(val: InvoiceListResponse) {
      this.operationRecordData = await new QueryOperationInvoiceLog().queryOperationLog(val.invoiceId)
      console.log(this.operationRecordData)
      this.operationRecordDialog = true
      //操作记录
    }

    openAutoInvoiceDialog() {
      //默认7天后自动开票
      this.invoiceDay = 7
      this.autoInvoiceDialog = true
    }

    async resetCondition() {
      this.page.pageNo = 1
      this.pageQueryParam = new QueryPageInvoiceParam()
      this.pageQueryParam.blueBillStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.blueBillStatusChangeTime.billing = new DateScopeRequest()
      this.pageQueryParam.blueBillStatusChangeTime.success = new DateScopeRequest()
      //this.pageQueryParam.commoditySkuIdList = undefined
      this.hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
      /*this.pageQueryParam.orderNoList = undefined
      this.pageQueryParam.userName = undefined
      this.pageQueryParam.idCard = undefined
      this.pageQueryParam.phone = undefined
      this.pageQueryParam.blueInvoiceItemBillStatusList = undefined
      this.pageQueryParam.invoiceFreezeStatus = undefined
      this.pageQueryParam.blueBillNo = undefined
      this.pageQueryParam.flushed = undefined
      this.pageQueryParam.redInvoiceItemBillStatusList = undefined
      this.pageQueryParam.blueBillStatusChangeTime.billing.begin = undefined
      this.pageQueryParam.blueBillStatusChangeTime.billing.end = undefined
      this.pageQueryParam.blueBillStatusChangeTime.success.begin = undefined
      this.pageQueryParam.blueBillStatusChangeTime.success.end = undefined*/
      if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }

    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportVatSpecialOnlineInvoice' }
      })
    }

    // 导出列表数据
    async exportSpecialInvoice() {
      this.exportQueryParam = Object.assign(new QueryPageInvoiceParam(), this.pageQueryParam)

      try {
        let res
        if (this.isZtlogin) {
          res = await this.onLinePageInvoiceInZtExport()
        } else {
          res = await this.queryInvoice.onLinePageInvoiceInExport(this.exportQueryParam)
        }
        if (res) {
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }

    /**
     * 线上发票导出
     */
    async onLinePageInvoiceInZtExport() {
      return await this.queryZtInvoice.onLinePageInvoiceInExport(this.exportQueryParam)
    }
    //下载发票
    async downloadInvoice(item: InvoiceListResponse) {
      await FileModule.applyResourceAccessToken()
      let url = ''
      let baseUrl = ''
      const pathArr: string[] = []
      if (item.blueFilePath) pathArr.push(item.blueFilePath)
      if (item.blueFileOfdPath) pathArr.push(item.blueFileOfdPath)
      if (item.blueFileXmlPath) pathArr.push(item.blueFileXmlPath)
      if (pathArr.length == 0) return this.$message.warning('暂无发票')
      if (pathArr.length == 1) {
        baseUrl = pathArr[0]
        if (baseUrl && baseUrl.substring(0, 4) !== '/mfs') {
          url = this.$util.imgUrlWithToken('/mfs' + baseUrl)
        } else {
          url = this.$util.imgUrlWithToken(baseUrl)
        }
        window.open(url, '_blank')
      } else {
        this.curItem = item
        this.InvoiceDialog.isShowDialog()
      }
    }

    async redInvoice(baseUrl: string) {
      // window.open('/mfs' + url, '_blank')
      await FileModule.applyResourceAccessToken()
      let url = ''
      if (baseUrl && baseUrl.substring(0, 4) !== '/mfs') {
        url = this.$util.imgUrlWithToken('/mfs' + baseUrl)
      } else {
        url = this.$util.imgUrlWithToken(baseUrl)
      }
      window.open(url, '_blank')
      /*const res = await TradeModule.singleTradeBatchFactor.invoiceFactor.mutationInvoice.flushElectronicInvoice(id)
      if (res.success) {
        this.$message.success('冲红成功')
      } else {
        this.$message.error(res.message || '冲红失败')
      }*/
    }
    //发票操作记录
    invoiceLog() {
      this.logDialog = true
    }

    async created() {
      if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
      await this.getAutoInvoiceDay()
    }
  }
</script>
