import CoursewareTransformStatus from '@api/service/management/resource/courseware/enum/CoursewareTransformStatus'

class CoursewareDetailVo {
  id: string

  /*
   课件名称
   */
  name: string

  /*
   课件状态
   */
  enable: boolean

  /*
   课件供应商名称
   */
  providerName: string

  /*
   * 转换状态
   */
  exchangeStatus: CoursewareTransformStatus

  /*
   时长
   */
  duration: number

  /*
   创建人
   */
  creatorName: string

  /*
   创建时间
   */
  createTime: string

  canDelete() {
    return false
  }

  /**
   * 只有课件转换成功的才允许预览
   */
  canPreview() {
    return false
  }

  /**
   * 刷新转码状态
   */
  async refreshExchangeStatus(): Promise<boolean> {
    // this.exchangeStatus = CoursewareStatus.enum.success
    return true
  }
}

export default CoursewareDetailVo
