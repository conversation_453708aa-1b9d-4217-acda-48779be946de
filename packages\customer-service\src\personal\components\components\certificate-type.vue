<!--
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-08-27 16:31:35
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-08-27 20:02:32
 * @Description:
-->
<template>
  <div>
    <el-select
      v-model="selected"
      :placeholder="placeholder"
      @clear="selected = undefined"
      class="el-input"
      filterable
      clearable
    >
      <el-option
        v-for="item in placeChannelOptions"
        :label="item.name"
        :value="item.propertyId"
        :key="item.propertyId"
      ></el-option>
    </el-select>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'
  @Component
  export default class extends Vue {
    // 职称数组
    // placeChannelOptions: Array<LeaderPositionLevelVo> = new Array<LeaderPositionLevelVo>()
    placeChannelOptions: Array<any> = []

    // 选择项
    selected = ''

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Prop({
      type: String,
      default: '请选择证书类型'
    })
    placeholder: string

    //行业Id
    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }

    async created() {
      await this.queryCertificateTypeList()
    }

    // 获取证书类别
    async queryCertificateTypeList() {
      if (this.industryId) {
        const res = await QueryPersonIndustry.getCertificatesType(this.industryId)
        this.placeChannelOptions = res
      }
    }
  }
</script>
