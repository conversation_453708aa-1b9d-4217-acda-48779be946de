import { ResponseStatus } from '@hbfe/common'
import IntelligenceLearningModel from '@api/service/management/intelligence-learning/model/IntelligenceLearningModel'
import MsAutolearning from '@api/ms-gateway/ms-autolearning-v1'
import MsAutolearningSmartSerivice from '@api/ms-gateway/ms-autolearning-online-school-smart-learning-service-v1'
import MSAutoLearningTask, {
  StudentAutoLearningTaskResultQueryPageRequest
} from '@api/ms-gateway/ms-autolearning-student-auto-learning-task-result-v1'
import Context from '@api/service/common/context/Context'
import DailyTiming from '@api/service/common/utils/DailyTiming'
export default class IntelligenceLearningModule {
  /**
   * 智能学习配置
   */
  IntelligenceLearningModel = new IntelligenceLearningModel()

  /**
   * 智能学习webfunny上报方法
   */
  dailyTiming = DailyTiming

  /**
   * 查询智能学习规则 网校配置了智能学习增值服务的情况下才能查到
   */
  async doQuery(servicerId?: string) {
    const res = await MsAutolearningSmartSerivice.queryOnlineSchoolSmartLearningServiceConfig(servicerId)
    if (res.status.isSuccess()) {
      return this.IntelligenceLearningModel.from(res.data)
    } else {
      return new IntelligenceLearningModel()
    }
  }

  /**
   * 保存智能学习规则
   */
  // async doSave() {
  //   const res = await MsAutolearning.updateOnlineSchoolSmartLearningServiceConfig(this.IntelligenceLearningModel.to())
  //   return res.status
  // }

  /**
   * 校验学习方案是否存在自动学习 单个
   * @param schemeId
   */
  async doCheck(schemeId: string) {
    let res
    try {
      res = await MsAutolearning.validLearningSchemeExistAutoLearning({ learningSchemeIdList: [schemeId] })
    } catch (error) {
      console.log(error, 'error 校验学习方案是否存在自动学习')
    }
    const resultMap = res?.data?.resultMap
    return resultMap ? resultMap[schemeId] === 'true' || false : false
  }

  /**
   * 校验学习方案是否存在自动学习 批量
   * @param schemeIdList
   */
  async doBatchCheck(schemeIdList: string[]) {
    let res
    try {
      res = await MsAutolearning.validLearningSchemeExistAutoLearning({ learningSchemeIdList: schemeIdList })
    } catch (error) {
      console.log(error, 'error 校验学习方案是否存在自动学习')
    }

    return res?.data?.resultMap || new Map<string, string>()
  }

  /**
   * 网校内 无缓存
   * 查询网校智能学习服务配置(查询是否开通)
   * 返回结果：未开启0 正常1 失效2
   */
  async doQueryServiceConfig() {
    const servicerId = Context.servicerInfo.id
    const res = await MsAutolearningSmartSerivice.queryOnlineSchoolSmartLearningServiceConfigByServicerId(servicerId)
    return res?.data || 0
  }

  /**
   * 网校内 有缓存 用于开过就一直显示的
   * 查询网校智能学习服务配置(查询是否开通)
   * 返回结果：未开启0 正常1 失效2
   */
  async doQueryServiceConfigWithCache() {
    const res = await MsAutolearningSmartSerivice.queryOnlineSchoolSmartLearningServiceConfigExist()
    return res?.data || 0
  }

  /**
   * 运营域 用服务商Id
   * 查询网校智能学习服务配置(查询是否开通)
   * 返回结果：未开启0 正常1 失效2
   */
  async doQueryServiceConfigByServicerId(servicerId: string) {
    const res = await MsAutolearningSmartSerivice.queryOnlineSchoolSmartLearningServiceConfigByServicerId(servicerId)
    return res?.data || 0
  }

  /**
   * 查询学员智能学习任务信息（查询学员学习执行中任务）
   * @param qualificationIdList
   */
  async doQuerylearningStatus(qualificationId: string) {
    const res = await MSAutoLearningTask.queryStudentNormalAutoLearningTaskResult(qualificationId)
    if (res.status.isSuccess()) {
      if ([0, 2, 4].includes(res.data?.result)) {
        // 上报webfunny
        this.dailyTiming.recordDate()
        return true
      }
    } else {
      return false
    }
    // const request = new StudentAutoLearningTaskResultQueryPageRequest()
    // request.pageNo = 1
    // request.pageSize = 1
    // // 2 执行中
    // request.resultList = [2]
    // request.qualificationIdList = [qualificationId]
    // const res2 = await MSAutoLearningTask.queryByPage(request)
    // if (res2.status.isSuccess()) {
    //   // 是否存在执行中任务
    //   return (res2?.data?.currentPageData || []).length > 0
    // } else {
    //   return false
    // }
  }
}
