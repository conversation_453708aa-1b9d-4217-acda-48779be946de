import { ResponseStatus } from '@hbfe/common'
import mockjs from 'mockjs'
import MsCourseResource, { BatchImportCoursewareCreateRequest } from '@api/ms-gateway/ms-course-resource-v1'

class ImportCourseware {
  /**
   * 模板名称
   */
  modelName = ''
  /**
   * 模板地址
   */
  modelUrl = ''
  /**
   * 查询模板地址
   */
  async queryImportTemplatePath() {
    // 后端没空给接口 先写死
    this.modelUrl =
      '/mfs/ms-file/public/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/外链课件批量导入模板.xlsx'
    this.modelName = '外链课件批量导入模板'
    return this.modelUrl
  }
  /**
   * 选中文件执行导入课件
   * @param filePath excel文件路径
   * @param fileName 文件名称 (需要带后缀名)
   */
  async doImport(filePath: string, fileName: string) {
    const request = new BatchImportCoursewareCreateRequest()
    request.excelFileName = fileName
    request.excelFilePath = filePath
    const res = await MsCourseResource.batchCreateCourseware(request)
    return res
  }
}
export default ImportCourseware
