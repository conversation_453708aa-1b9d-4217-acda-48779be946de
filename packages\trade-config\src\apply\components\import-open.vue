<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header">
        <span class="tit-txt">销售渠道配置</span>
      </div>
      <div class="f-p20 f-tc">
        <div class="m-no-date">
          <img class="img" src="@design/admin/assets/images/no-data-news.png" alt="" />
          <p class="txt">导入开通为系统默认帐号无需配置~</p>
        </div>
      </div>
    </el-card>
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header" class="f-flex">
        <span class="tit-txt f-flex-sub">渠道发票配置</span>
      </div>
      <div class="f-p30">
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form">
              <el-form-item label="是否提供发票：" required>
                <el-radio-group v-model="importOpenDetail.invoiceConfig.allowAskFor">
                  <el-radio :label="false">不提供发票</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import MutationUpdatePurchaseChannel from '@api/service/management/trade-info-config/mutation/MutationUpdatePurchaseChannel'
  import PurchaseChannelTypeVo from '@api/service/management/trade-info-config/mutation/vo/PurchaseChannelTypeVo'
  import TradeInfoConfigModule from '@api/service/management/trade-info-config/TradeInfoConfigModule'
  @Component
  export default class extends Vue {
    @Prop({ type: Object, default: [] }) importOpenDetail: PurchaseChannelTypeVo
    @Watch('importOpenDetail', { deep: true })
    async purchaseChannelListWatch(newVal: PurchaseChannelTypeVo) {
      if (newVal) {
        console.log(newVal, 'newval')
        this.importOpenDetail = newVal
        this.id = this.importOpenDetail.id
        this.updatePurchaseChannelVo = TradeInfoConfigModule.mutationTradeInfoConfigFactory.getUpdatePurchaseChannel(
          this.id,
          1
        )
        await this.requestDetail()
      }
    }
    id = ''
    updatePurchaseChannelVo = new MutationUpdatePurchaseChannel()
    preparePurchaseChannelVo = TradeInfoConfigModule.mutationTradeInfoConfigFactory.getPreparePurchaseChannel()
    form = {
      resource: '1'
    }

    async requestDetail() {
      // await this.preparePurchaseChannelVo.preparePurchaseChannel()
      // const res = await this.updatePurchaseChannelVo.getupdatePurchaseChannelParam()
      // console.log(res, 'res')
      // console.log(this.importOpenDetail, 'this.purchaseChannelList')
      // this.mutationUpdatePurchaseChannel = new MutationUpdatePurchaseChannel(this.importOpenDetail[0].id)
      // const res = await this.mutationUpdatePurchaseChannel.getupdatePurchaseChannelParam()
      // console.log(res, '111')
    }
  }
</script>
