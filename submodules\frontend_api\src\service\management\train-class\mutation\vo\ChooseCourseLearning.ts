import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'
import ChooseCourseRule from '@api/service/management/train-class/mutation/vo/ChooseCourseRule'

/**
 * 选课规则类型的课程学习
 */
class ChooseCourseLearning extends CourseLearningLearningType {
  // region properties

  /**
   *必修课要求学时，类型为number
   */
  compulsoryRequirePeriod = 0
  /**
   *选修课要求学时，类型为number
   */
  electiveRequirePeriod = 0
  /**
   *选课规则，类型为ChooseCourseRule
   */
  chooseCourseRule = new ChooseCourseRule()
  // endregion
  // region methods

  // endregion
}
export default ChooseCourseLearning
