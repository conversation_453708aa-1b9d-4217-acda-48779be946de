/**
 * 课件分类业务对象
 */
import { ResponseStatus } from '@hbfe/common'
import MsCourseResourceV1 from '@api/ms-gateway/ms-course-resource-v1'

class MutationBizCoursewareCategory {
  private readonly id: string

  constructor(id: string) {
    this.id = id
  }

  async doRemove(): Promise<ResponseStatus> {
    const { status } = await MsCourseResourceV1.removeCoursewareCategory(this.id)
    return new ResponseStatus(status.code, status.getMessage())
  }
}

export default MutationBizCoursewareCategory
