<template>
  <el-card shadow="never" class="m-card f-mb15">
    <!--条件查询-->
    <el-row :gutter="16" class="m-query">
      <el-form :inline="true" label-width="auto">
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="配送状态">
            <el-select v-model="deliveryInvoiceParam.deliveryStatus" filterable placeholder="请选择">
              <el-option
                v-for="item in deliveryStatusList"
                :label="item.name"
                :value="item.value"
                :key="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="集体报名批次号">
            <el-input v-model="deliveryInvoiceParam.invoiceNo" clearable placeholder="请输入订单号" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="开票状态">
            <el-select v-model="deliveryInvoiceParam.frozenState" clearable filterable placeholder="请选择">
              <el-option label="是" :value="true"></el-option>
              <el-option label="否" :value="false"></el-option>
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item>
            <el-button type="primary" @click="doQueryPage">查询</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <!--表格-->
    <el-table stripe :data="tableData" ref="distributionRef" v-loading="loading" max-height="500px" class="m-table">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="集体报名批次号" min-width="220" fixed="left">
        <template slot-scope="scope">{{ scope.row.associationId }}</template>
      </el-table-column>
      <el-table-column label="发票号" min-width="120">
        <template slot-scope="scope">{{ scope.row.invoiceNo }}</template>
      </el-table-column>
      <el-table-column label="收件信息" min-width="400">
        <template slot-scope="scope">
          <p class="f-flex" v-if="scope.row.deliveryInfo.shippingMethod === 2">
            <span>收货地址：</span>
            <span class="f-flex-sub">{{
              scope.row.deliveryInfo.deliveryAddress
                ? getAddress(
                    scope.row.deliveryInfo.deliveryAddress.region,
                    scope.row.deliveryInfo.deliveryAddress.address
                  )
                : ''
            }}</span>
          </p>
          <p v-if="scope.row.deliveryInfo.shippingMethod === 2">
            收货人：{{ scope.row.deliveryInfo.deliveryAddress ? scope.row.deliveryInfo.deliveryAddress.consignee : '' }}
          </p>
          <p v-if="scope.row.deliveryInfo.shippingMethod === 1">购买人：{{ scope.row.name }}</p>
          <p v-if="scope.row.deliveryInfo.shippingMethod === 1">身份证号：{{ scope.row.idCard }}</p>
          <p>
            手机号: {{ scope.row.deliveryInfo.deliveryAddress ? scope.row.deliveryInfo.deliveryAddress.phone : '' }}
          </p>
          <p v-if="scope.row.deliveryInfo.shippingMethod === 1">
            自取地址: {{ scope.row.deliveryInfo.takePoint ? scope.row.deliveryInfo.takePoint.pickupLocation : '' }}
          </p>
        </template>
      </el-table-column>
      <el-table-column label="配送方式" width="120">
        <template slot-scope="scope">
          <div v-if="scope.row.deliveryInfo.shippingMethod === 0">-</div>
          <div v-if="scope.row.deliveryInfo.shippingMethod === 1">自取</div>
          <div v-if="scope.row.deliveryInfo.shippingMethod === 2">邮寄</div>
        </template>
      </el-table-column>
      <el-table-column label="配送方式" width="120">
        <template slot-scope="scope">
          <div v-if="scope.$index === 0">自取</div>
          <div v-else>邮寄</div>
        </template>
      </el-table-column>
      <el-table-column label="配送状态" min-width="130">
        <template slot-scope="scope">
          <div v-if="scope.row.deliveryInfo.deliveryStatus === 1">
            <el-badge is-dot type="primary" class="badge-status">就绪</el-badge>
          </div>
          <div v-if="scope.row.deliveryInfo.deliveryStatus === 0">
            <el-badge is-dot type="info" class="badge-status">未就绪</el-badge>
          </div>
          <div v-if="scope.row.deliveryInfo.deliveryStatus === 3 || scope.row.deliveryInfo.deliveryStatus === 2">
            <el-badge is-dot type="success" class="badge-status">已配送</el-badge>
          </div>
          <!-- <div v-if="scope.row.deliveryInfo.deliveryStatus === 2">
              <el-badge is-dot type="success" class="badge-status">已自取</el-badge>
            </div> -->
        </template>
      </el-table-column>
      <el-table-column label="配送信息 / 取件信息" min-width="260">
        <template slot-scope="scope">
          <div v-if="scope.row.deliveryInfo.shippingMethod === 1 && scope.row.deliveryInfo.deliveryStatus === 2">
            <p>领取人: {{ scope.row.deliveryInfo.takeResult.takePerson }}</p>
            <p>手机号: {{ scope.row.deliveryInfo.takeResult.phone }}</p>
            <p>取货时间: {{ scope.row.taken }}</p>
          </div>
          <div v-if="scope.row.deliveryInfo.shippingMethod === 2 && scope.row.deliveryInfo.deliveryStatus === 3">
            <p>快递公司: {{ scope.row.deliveryInfo.express.expressCompanyName }}</p>
            <p>运单号: {{ scope.row.deliveryInfo.express.expressNo }}</p>
            <p>发货时间: {{ scope.row.shipped }}</p>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="是否冻结" min-width="120">
        <template slot-scope="scope">{{ scope.row.invoiceFreezeStatus ? '是' : '否' }}</template>
      </el-table-column>
      <el-table-column label="操作" width="140" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            v-if="
              scope.row.deliveryInfo.shippingMethod === 1 &&
              !scope.row.invoiceFreezeStatus &&
              scope.row.deliveryInfo.deliveryStatus != 0 &&
              scope.row.deliveryInfo.deliveryStatus != 2
            "
            @click="isShowDeliveryDialog(scope.row, 1)"
            >确认自取
          </el-button>
          <el-button
            type="text"
            size="mini"
            v-if="
              scope.row.deliveryInfo.shippingMethod === 2 &&
              !scope.row.invoiceFreezeStatus &&
              scope.row.deliveryInfo.deliveryStatus != 0 &&
              scope.row.deliveryInfo.deliveryStatus != 3
            "
            @click="isShowDeliveryDialog(scope.row, 2)"
            >确认配送
          </el-button>
          <el-button
            type="text"
            size="mini"
            v-if="scope.row.invoiceFreezeStatus || scope.row.deliveryInfo.deliveryStatus === 0"
            >-
          </el-button>
          <el-button
            type="text"
            size="mini"
            class="fontColor"
            v-if="scope.row.deliveryInfo.deliveryStatus === 3 && !scope.row.invoiceFreezeStatus"
            >已配送
          </el-button>
          <el-button
            type="text"
            size="mini"
            class="fontColor"
            v-if="scope.row.deliveryInfo.deliveryStatus === 2 && !scope.row.invoiceFreezeStatus"
            >已自取
          </el-button>
          <el-button
            type="text"
            size="mini"
            class="fontColor"
            v-if="
              (scope.row.deliveryInfo.deliveryStatus === 2 || scope.row.deliveryInfo.deliveryStatus === 3) &&
              !scope.row.invoiceFreezeStatus
            "
            @click="deliveryLogClick(scope.row)"
            >配送记录
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务管理-个人报名增值税电子普通发票（自动开票）</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
    <el-dialog title="提示" :visible.sync="importSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导入成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导入任务查看-个人报名增值税电子普通发票（线下开票）</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="importSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goImportDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
    <delivery-dialog
      :dialogType="dialogType"
      @confirmOrder="confirmOrder"
      @confirmDeliveryOrder="confirmDeliveryOrder"
      ref="deliveryDialog"
    ></delivery-dialog>
    <import-parcel-waybill
      ref="importParcelWaybill"
      :import-dialog.sync="importInvoiceVisible"
      @importSuccess="importSuccessVisible = true"
    ></import-parcel-waybill>
    <delivery-log ref="deliveryLog" :deliveryRecordList="deliveryRecordList"></delivery-log>
  </el-card>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Prop, Watch } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import DeliveryInvoiceParam from '@api/service/management/trade/single/invoice/query/vo/DeliveryInvoiceParam'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import DeliveryDialog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/delivery-dialog.vue'
  import {
    DeliveryStatusEnum,
    DeliveryWayEnum
  } from '@api/service/management/trade/single/invoice/enum/DeliveryInvoiceEnum'
  import OffLinePageInvoiceVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import DeliveryInvoiceParamVo from '@api/service/management/trade/batch/invoice/query/vo/DeliveryInvoiceParam'
  import ImportParcelWaybill from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/import-parcel-waybill.vue'
  import { bind, debounce } from 'lodash-decorators'
  import QueryDeliveryInvoice from '@api/service/management/trade/batch/invoice/query/QueryDeliveryInvoice'
  import MutationDeliveryInvoice from '@api/service/management/trade/batch/invoice/mutation/MutationDeliveryInvoice'
  import { OfflineDeliveryRecord } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import DeliveryLog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/delivery-log.vue'

  @Component({
    components: { DoubleDatePicker, DeliveryDialog, ImportParcelWaybill, DeliveryLog }
  })
  export default class extends Vue {
    @Ref('distributionRef') distributionRef: any
    @Ref('deliveryDialog') deliveryDialog: any
    @Ref('importParcelWaybill') importParcelWaybill: ImportParcelWaybill
    @Ref('deliveryLog') deliveryLog: DeliveryLog

    @Prop({
      type: String,
      default: ''
    })
    userId: string

    @Watch('userId')
    async userIdChange(id: string) {
      await this.doQueryPage()
    }

    // TODO
    // 发票配送查询实例
    queryDeliveryInvoiceVo = new QueryDeliveryInvoice()
    // // 发票配送业务实例
    mutationDeliveryInvoiceVo = new MutationDeliveryInvoice()
    // 列表筛选参数
    deliveryInvoiceParam = new DeliveryInvoiceParamVo()
    loading = false
    // 弹窗类型
    dialogType = 0
    // 选中的发票详情
    checkedItemDetail = new OffLinePageInvoiceVo()
    //导出成功弹窗
    exportSuccessVisible = false
    //导入弹窗
    importInvoiceVisible = false
    //导入成功弹窗
    importSuccessVisible = false
    // 配送状态
    deliveryStatusList = [
      { name: '未就绪', value: DeliveryStatusEnum.NOTREADY },
      { name: '就绪', value: DeliveryStatusEnum.READY },
      { name: '已配送', value: DeliveryStatusEnum.DELIVERED }
    ]
    // 配送状态
    deliveryWayList = [
      { name: '快递', value: DeliveryWayEnum.COURIER },
      { name: '自取', value: DeliveryWayEnum.SELFFETCHED }
    ]
    // 发票配送记录
    deliveryRecordList = new Array<OfflineDeliveryRecord>()
    deliveryWay = DeliveryWayEnum
    tableData = new Array<OffLinePageInvoiceVo>()
    page: UiPage

    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }

    // 列表请求
    async doQueryPage() {
      if (!this.userId) {
        this.tableData = []
        return
      }
      try {
        this.loading = true
        this.deliveryInvoiceParam.createUserId = this.userId
        const result = await this.queryDeliveryInvoiceVo.queryPageDeliveryInvoice(this.page, this.deliveryInvoiceParam)
        this.tableData = result
      } catch (e) {
        this.$message.error('请求列表失败！')
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    async pollingQuery() {
      if (!this.userId) {
        this.tableData = []
        return
      }
      try {
        this.loading = true
        const origin = this.tableData.slice()
        this.deliveryInvoiceParam.createUserId = this.userId
        const result = await this.queryDeliveryInvoiceVo.queryPageDeliveryInvoice(this.page, this.deliveryInvoiceParam)
        this.tableData = [...origin, ...result]
      } catch (e) {
        this.$message.error('请求列表失败！')
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    // 重置条件
    async restQuery() {
      this.deliveryInvoiceParam = new DeliveryInvoiceParamVo()
      this.doQueryPage()
    }

    // 是否显示弹窗
    isShowDeliveryDialog(item: any, type: number) {
      this.checkedItemDetail = item
      this.dialogType = type
      this.deliveryDialog.isShowDialog()
    }

    // 确认自取
    async confirmOrder(item: any) {
      console.log(item, 'item')
      item.offlineInvoiceId = this.checkedItemDetail.invoiceId
      const res = await this.mutationDeliveryInvoiceVo.confirmStatus(item, this.deliveryWay.SELFFETCHED)
      if (res.isSuccess()) {
        this.$message.success('确认自取成功')
        this.deliveryDialog.isShowDialog()
        this.doQueryPage()
      } else {
        this.$message.warning('请求失败！')
      }
    }

    // 确认配送
    async confirmDeliveryOrder(item: any) {
      console.log(item, 'item')
      item.offlineInvoiceId = this.checkedItemDetail.invoiceId
      const res = await this.mutationDeliveryInvoiceVo.confirmStatus(item, this.deliveryWay.COURIER)
      if (res.isSuccess()) {
        this.$message.success('确认配送成功')
        this.deliveryDialog.isShowDialog()
        this.doQueryPage()
      } else {
        this.$message.warning('请求失败！')
      }
    }

    async created() {
      this.deliveryInvoiceParam.deliveryStatus = DeliveryStatusEnum.READY
      await this.$nextTick(async () => {
        const element = this.distributionRef.bodyWrapper
        element.addEventListener('scroll', this.infiniteScroll)
      })
      await this.doQueryPage()
    }

    /**
     * 【换班记录】无限加载
     */
    @bind
    @debounce(200)
    async infiniteScroll() {
      const element = this.distributionRef.bodyWrapper
      const scrollDistance = element.scrollHeight - element.scrollTop - element.clientHeight
      if (scrollDistance <= 0) {
        if (this.tableData.length >= this.page.totalSize) {
          // this.$message.warning('没有更多数据')
        } else {
          this.page.pageNo++
          await this.pollingQuery()
        }
      }
    }

    // 前往下载
    goDownloadPage() {
      this.$router.push({ path: '/training/task/export', query: { type: 'exportBatchInvoiceDelivery' } })
      this.exportSuccessVisible = false
    }

    // 配送记录
    async deliveryLogClick(item: OffLinePageInvoiceVo) {
      this.deliveryRecordList = item.deliveryRecordList
      this.deliveryLog.isShowDialog()
    }

    // 导出信息
    async exportFile() {
      const res = await this.queryDeliveryInvoiceVo.exportPageDeliveryInvoice(this.deliveryInvoiceParam)
      if (res) {
        this.$message.success('导出成功')
        this.exportSuccessVisible = true
      } else {
        this.$message.warning('导出失败')
      }
    }

    // 导入
    importFile() {
      this.importInvoiceVisible = true
    }

    // 下载导入数据
    goImportDownloadPage() {
      this.importSuccessVisible = false
      this.$router.push('/training/task/import')
    }

    // 获取收货地址
    getAddress(itemRegion: string, itemAddress: string) {
      let region = ''
      const reg = new RegExp('/', 'g')
      if (itemRegion.indexOf('/') != -1) {
        region = itemRegion.replace(reg, '')
        console.log(region, 'region')
      }
      return region + itemAddress
    }
  }
</script>
