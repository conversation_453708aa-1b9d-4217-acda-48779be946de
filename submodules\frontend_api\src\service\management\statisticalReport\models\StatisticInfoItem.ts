import {
  ChannelInfoDto,
  DistributionSellStatisticResponse,
  ReportNumStatisticResponse,
  TradeSummaryInfoResponse
} from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import { AccountTypeEunm } from '@api/service/common/enums/trade-configuration/AccountType'
import { PurchaseChannelEnums } from '@api/service/management/statisticalReport/DistributionGoodsOpeningStatistics/model/DistributionGoodsOpeningStatisticsItem'

export default class StatisticInfoItem {
  /**
   * 开通
   */
  open = 0
  /**
   * 退班
   */
  return = 0
  /**
   *  换班（换出）
   */
  swapOutOfClass = 0
  /**
   *  换班（换入）
   */
  swapIntoAClass = 0
  /**
   * 净开通
   */
  count = 0
  /**
   * 分销金额
   */
  distributionAmount = 0

  static toStatisticInfoItem(dto: DistributionSellStatisticResponse) {
    const vo = new StatisticInfoItem()
    vo.open = dto.tradeSuccessCount
    vo.return = dto.returnCount
    vo.count = dto.netTradeSuccessCount
    vo.distributionAmount = dto.netTradeSuccessAmount
    return vo
  }

  static reportNumStatisticResponseToStatisticInfoItem(dto: ReportNumStatisticResponse) {
    const vo = new StatisticInfoItem()
    vo.open = dto.tradeSuccessCount
    vo.return = dto.returnCount
    vo.swapOutOfClass = dto.exchangeOutCount
    vo.swapIntoAClass = dto.exchangeInCount
    vo.count = dto.netTradeSuccessCount
    return vo
  }

  static returnPayTypeData(data: ChannelInfoDto[], type: PurchaseChannelEnums, accountTypeEunm: AccountTypeEunm) {
    const vo = new StatisticInfoItem()
    const dto = data
      .find(item => item.purchaseChannel === type)
      ?.payTypeStatisticResponseList.find(res => res.payType === accountTypeEunm)?.statisticInfo
    if (dto) {
      vo.open = dto.tradeSuccessCount
      vo.return = dto.returnCount
      vo.swapIntoAClass = dto.exchangeInCount
      vo.swapOutOfClass = dto.exchangeOutCount
      vo.count = dto.netTradeSuccessCount
    }
    return vo
  }

  static toSummaryData(dto: TradeSummaryInfoResponse) {
    const vo = new StatisticInfoItem()
    vo.open = dto.totalTradeCompleteNum
    vo.return = dto.totalReturnCompleteNum
    vo.swapOutOfClass = dto.totalExchangeOutNum
    vo.swapIntoAClass = dto.totalExchangeInNum
    vo.count = dto.totalTradeCompleteNetNum
    return vo
  }
}
