import MSAutoLearningTask, {
  StudentAutoLearningTaskResultQueryPageRequest
} from '@api/ms-gateway/ms-autolearning-student-auto-learning-task-result-v1'
export default class IntelligenceAbnormalParams {
  /**
   * 姓名
   */
  name = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 培训方案id
   */
  schemeName = ''

  static to(vo: IntelligenceAbnormalParams) {
    const dto = new StudentAutoLearningTaskResultQueryPageRequest()
    dto.name = vo.name
    dto.phone = vo.phone
    dto.idCard = vo.idCard
    dto.learningSchemeName = vo.schemeName
    dto.resultList = [4]
    return dto
  }
}
