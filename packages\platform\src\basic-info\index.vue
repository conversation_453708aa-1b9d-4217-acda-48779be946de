<route-meta>
{
"isMenu": true,
"title": "基础信息配置",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main>
    <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb15">
      <div class="f-fl">
        <span v-show="!webAccess || !h5Access" style="margin-right: 50px">
          温馨提示：当前网校未开启
          <span v-show="!webAccess">web</span>
          <span v-show="!webAccess && !h5Access">、</span>
          <span v-show="!webAccess && !h5Access"></span>
          <span v-show="!h5Access">h5</span>访问。请完成网校配置后开启对外访问。
        </span>
      </div>
      <template v-if="$hasPermission('setAccess')" desc="访问设置（编辑）" actions="editAccessConfig">
        <div class="f-flex f-ml50 f-fl" style="margin-left: 0px">
          <div class="f-mr50">
            <i class="f-vm f-c6">Web 访问</i>
            <el-switch v-model="webAccess" @change="openDialog(1)" class="f-ml10"></el-switch>
          </div>
          <div class="f-mr50">
            <i class="f-vm f-c6">H5 访问</i>
            <el-switch v-model="h5Access" @change="openDialog(2)" class="f-ml10"></el-switch>
          </div>
        </div>
      </template>
    </el-alert>
    <el-tabs v-model="activeTabName" class="m-tab-top is-sticky">
      <el-tab-pane label="web端" name="web" v-if="$hasPermission('webSet')" desc="web端" actions="@Web">
        <web @callBack="changeWebAccess" :TemplateModuleObj="TemplateModuleObj"></web>
      </el-tab-pane>
      <el-tab-pane label="移动端" name="mobile" v-if="$hasPermission('H5Set')" desc="移动端" actions="@Mobile">
        <mobile :H5TemplateModuleObj="H5TemplateModuleObj"></mobile>
      </el-tab-pane>
    </el-tabs>

    <el-dialog
      title="提示"
      :visible.sync="switchDialog"
      width="450px"
      class="m-dialog"
      :before-close="closeDialog"
      :append-to-body="true"
    >
      <div class="dialog-alert">
        <!--警告-->
        <i class="icon el-icon-warning warning"></i>
        <span class="txt">{{ switchDialogText }}</span>
      </div>
      <div slot="footer">
        <el-button @click="closeDialog">取 消</el-button>
        <el-button type="primary" @click="editAccessConfig(changeType)">确 定</el-button>
      </div>
    </el-dialog>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import Web from '@hbfe/jxjy-admin-platform/src/components/web.vue'
  import Mobile from '@hbfe/jxjy-admin-platform/src/components/mobile.vue'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import TemplateModule from '@api/service/common/template-school/TemplateModule'
  import TemplateItem from '@api/service/common/template-school/TemplateItem'
  @Component({
    components: { Web, Mobile }
  })
  export default class extends Vue {
    activeTabName = 'web'
    webAccess = false
    h5Access = false
    switchDialog = false
    switchDialogText = ''
    changeType = 1
    TemplateModuleObj = new TemplateItem()
    H5TemplateModuleObj = new TemplateItem()

    async getAccessConfig() {
      const res = await OnlineSchoolConfigModule.queryPortal.queryDetail()
      if (res.isSuccess()) {
        this.webAccess =
          OnlineSchoolConfigModule.queryPortal.webPortalInfo.domainName &&
          OnlineSchoolConfigModule.queryPortal.webAccess
            ? true
            : false
        this.h5Access =
          OnlineSchoolConfigModule.queryPortal.h5PortalInfo.domainName && OnlineSchoolConfigModule.queryPortal.h5Access
            ? true
            : false
      }
    }

    openDialog(type: number) {
      if (type === 1) {
        this.webAccess = !this.webAccess
      } else if (type === 2) {
        this.h5Access = !this.h5Access
      }

      this.changeType = type
      const flag = type === 1 ? this.webAccess : this.h5Access
      this.switchDialogText = flag
        ? '关闭后学员无法通过网校域名访问网校平台。\n' + '\n' + '确定关闭网校对外访问？'
        : '\n' + '请确保网校的门户配置已完成再设置为开启。\n' + '\n' + '确定开启网校对外访问？'
      this.switchDialog = true
    }

    closeDialog() {
      this.switchDialog = false
      this.changeType = -1
      this.switchDialogText = ''
    }

    async editAccessConfig(type: number) {
      const access = type === 1 ? !this.webAccess : !this.h5Access
      if (type === 1 && !OnlineSchoolConfigModule.queryPortal.webPortalInfo.domainName) {
        this.switchDialog = false
        return this.$message.error('该网校未提供web，不允许开启访问。')
      }
      if (type === 2 && !OnlineSchoolConfigModule.queryPortal.h5PortalInfo.domainName) {
        this.switchDialog = false
        return this.$message.error('该网校未提供H5，不允许开启访问。')
      }
      const res = await OnlineSchoolConfigModule.mutationPortal.handleAccess(type, access)
      if (res.isSuccess()) {
        this.switchDialog = false
        this.$message.success('修改成功')
        if (type === 1) {
          this.webAccess = !this.webAccess
          OnlineSchoolConfigModule.queryPortal.webAccess = this.webAccess
        } else if (type === 2) {
          this.h5Access = !this.h5Access
          OnlineSchoolConfigModule.queryPortal.h5Access = this.h5Access
        }
      } else {
        this.switchDialog = false
        this.$message.error('修改失败')
        await this.getAccessConfig()
      }
    }

    changeWebAccess() {
      this.webAccess = true
    }
    // 获取网校模板配置
    async getNetSchoolTemplateConfig() {
      const webPortalTemplateId = OnlineSchoolConfigModule.queryPortal.webPortalInfo.webTemplateId
      if (!OnlineSchoolConfigModule.queryPortal.h5PortalInfo.webTemplateId) {
        await OnlineSchoolConfigModule.queryPortal.queryDetail()
      }
      const h5PortalTemplateId = OnlineSchoolConfigModule.queryPortal.h5PortalInfo.webTemplateId
      // PC
      this.TemplateModuleObj = TemplateModule.getTemplate(webPortalTemplateId)
      // H5
      this.H5TemplateModuleObj = TemplateModule.getTemplate(
        h5PortalTemplateId === 'TestTemplateId-1' ? 'TestH5TemplateId-1' : h5PortalTemplateId
      )
    }
    async created() {
      await this.getAccessConfig()
      //   this.webAccess = OnlineSchoolConfigModule.queryPortal.webAccess
      //   this.h5Access = OnlineSchoolConfigModule.queryPortal.h5Access
      this.getNetSchoolTemplateConfig()
    }
  }
</script>
