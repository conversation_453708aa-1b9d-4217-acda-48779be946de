/*
 * @Description: 列表转换数据
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-29 16:34:50
 */

import { ReturnOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import RefundCheckAccountList from '@api/service/management/trade/single/checkAccount/query/vo/RefundCheckAccountListResponse'
import QueryPlatform from '@api/service/diff/common/zztt/dictionary/QueryPlatform'
import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/zztt/trade/enums/SaleChannelType'

export default class RefundCheckAccountListResponse extends RefundCheckAccountList {
  /**
   * 第三方平台
   */
  thirdPartyPlatform = ''
  static from(returnOrderResponse: ReturnOrderResponse) {
    const refundCheckAccountListResponse = Object.assign(
      new RefundCheckAccountListResponse(),
      RefundCheckAccountList.from(returnOrderResponse)
    )
    if (returnOrderResponse.subOrderInfo.saleChannel == SaleChannelEnum.huayi) {
      refundCheckAccountListResponse.thirdPartyPlatform = SaleChannelType.map.get(
        returnOrderResponse.subOrderInfo.saleChannel
      )
    } else if (returnOrderResponse.returnCommodity?.commoditySku?.tppTypeId) {
      refundCheckAccountListResponse.thirdPartyPlatform = QueryPlatform.map.get(
        returnOrderResponse.returnCommodity.commoditySku.tppTypeId
      )?.name
    }
    return refundCheckAccountListResponse
  }
}
