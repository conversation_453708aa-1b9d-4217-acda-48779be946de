import FxnlQueryFront, { TradeSummaryStatisticResponse } from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import StatisticInfoItem from '../../models/StatisticInfoItem'
export default class SummaryInfo {
  /**
   * 合计类型 | 1、合计 2、个人缴费-线上支付 3、集体缴费-线上支付 4、集体缴费线下支付 5、导入开通-线下支付
   */
  summaryType: number = null
  /**
   * 合计统计信息
   */
  summaryInfo: StatisticInfoItem = new StatisticInfoItem()
}
