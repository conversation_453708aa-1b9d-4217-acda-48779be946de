import DateScope from '@api/service/common/models/DateScope'
import { ExaminationAnswerPaperResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'

/**
 * @description 考试记录
 */

class ExamRecordVo extends ExaminationAnswerPaperResponse {
  /**
   * 考试id
   */
  studentExamId = ''

  /**
   * 答卷id
   */
  answerPaperId = ''

  /**
   * 考试时间
   */
  examTime: DateScope = new DateScope()

  /**
   * 考试成绩
   */
  examScore = 0

  static from(response: ExaminationAnswerPaperResponse): ExamRecordVo {
    const detail = new ExamRecordVo()
    Object.assign(detail, response)
    detail.studentExamId = detail.answerPaperBasicInfo?.sceneId ?? null
    detail.examTime.begin = detail.answerPaperTimeInfo?.answeringTime ?? ''
    detail.examTime.end = detail.answerPaperTimeInfo?.handingTime ?? ''
    detail.examScore = detail.answerPaperMarkInfo?.score ?? null
    return detail
  }
}

export default ExamRecordVo
