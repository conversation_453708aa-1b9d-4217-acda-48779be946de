import ConfigCenterModule from '@api/service/common/config-center/ConfigCenter'

class AntiUtils {
  interceptionPath = '/web/cheat/interception'
  checkCodeStatePath = '/web/cheat/codestate'
  pushPath = '/web/cheat/push'
  applyLearningCommitTokenPath = '/web/cheat/applyLearningCommitToken'

  getServiceUrl(path: string): string {
    return ConfigCenterModule.getIngressByName('ingress.anticheat') + path
  }

  /**
   * 获取防作弊拦截服务地址
   */
  getInterceptionServiceUrl(): string {
    return this.getServiceUrl(this.interceptionPath)
  }

  /**
   * 获取验证短码状态服务地址
   */
  getCheckCodeStateServiceUrl(): string {
    return this.getServiceUrl(this.checkCodeStatePath)
  }

  /**
   * 获取提交防作弊照片服务地址
   */
  getPushServiceUrl(): string {
    return this.getServiceUrl(this.pushPath)
  }

  /**
   * 获取防作弊下的课程学习进度提交token地址
   */
  getApplyLearningCommitTokenPath(): string {
    return this.getServiceUrl(this.applyLearningCommitTokenPath)
  }

  /**
   * 生成请求参数
   * @param data 请求数据
   */
  generateRequestParameter(data: any): any {
    return {
      head: { appVersion: '1.0', osPlatform: 'web' },
      data: data
    }
  }
}

export default new AntiUtils()
