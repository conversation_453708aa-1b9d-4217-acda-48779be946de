import { BatchOrderTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
import {
  BatchOrderBasicDataRequest,
  BatchOrderRequest,
  BatchOrderStatusChangeTimeRequest,
  BigDecimalScopeRequest,
  DateScopeRequest,
  OrderPayInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import DataResolve from '@api/service/common/utils/DataResolve'
import BatchOrderUtils from '@api/service/management/trade/batch/order/query/utils/BatchOrderUtils'
import SaleChannelType, { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { PaymentMethodEnum } from '@api/service/management/trade/batch/order/enum/PaymentMethod'

/**
 * @description 【集体报名订单列表】查询参数
 */
class QueryBatchOrderListVo {
  /**
   * 收款账号id集合
   */
  receiveAccountIdList: string[] = []

  /**
   * 批次号
   */
  batchOrderNo = ''

  /**
   * 交易流水号
   */
  flowNo = ''

  /**
   * 购买人id
   */
  buyerId = ''

  /**
   * 购买人账号
   */
  buyerAccount = ''

  /**
   * 购买人姓名
   */
  buyerName = ''

  /**
   * 交易状态 1:待下单、2:下单中、3:待付款、4:支付中、5:开通中、6:交易成功、7:交易关闭中、8:交易关闭
   */
  orderStatus: BatchOrderTradeStatusEnum = null

  /**
   * 批次提交时间
   */
  applyTime: string[] = null

  /**
   * 交易成功时间
   */
  paymentCompleteTime: string[] = null

  /**
   * 是否剔除0元单
   */
  isRemoveZeroOrder = false

  /**
   * 创建人id
   */
  createUserId: Array<string> = new Array<string>()
  /**
   * 专题名称
   */
  specialSubjectName = ''

  /**
   * 是否来源专题
   */
  isFromSpecialSubject: boolean = undefined

  /**
   * 销售渠道
   */
  saleSource: SaleChannelEnum = null

  /**
   * 分销商id
   */
  distributorId = ''

  /**
   * 推广门户id
   */
  promotionPortalId = ''
  /**
   * 缴费方式
   */
  paymentMethod: PaymentMethodEnum = null
  /**
   * 转换远端查询模型
   */
  async to(): Promise<BatchOrderRequest> {
    const to = new BatchOrderRequest()
    to.batchOrderNoList = this.batchOrderNo ? [this.batchOrderNo] : undefined
    to.payInfo = new OrderPayInfoRequest()
    // 收款账号
    if (DataResolve.isWeightyArr(this.receiveAccountIdList)) {
      to.payInfo.receiveAccountIdList = this.receiveAccountIdList
    } else {
      to.payInfo.receiveAccountIdList = undefined
    }
    // 交易流水号
    if (this.flowNo) {
      to.payInfo = new OrderPayInfoRequest()
      to.payInfo.flowNoList = [this.flowNo]
    } else {
      to.payInfo.flowNoList = undefined
    }
    if (this.createUserId.length) {
      to.creatorIdList = this.createUserId
    } else {
      to.creatorIdList = await BatchOrderUtils.getBuyerIdList(this)
    }
    to.basicData = new BatchOrderBasicDataRequest()
    to.basicData.batchOrderStatusChangeTime = new BatchOrderStatusChangeTimeRequest()
    // 交易状态 1:待下单、2:下单中、3:待付款、4:支付中、5:开通中、6:交易成功、7:交易关闭中、8:交易关闭
    if ((this.orderStatus ?? null) !== null) {
      if (this.orderStatus === BatchOrderTradeStatusEnum.Wait_Place_Order) {
        // 待下单
        to.basicData.batchOrderStatusList = [0]
        to.basicData.batchOrderPaymentStatusList = undefined
      }
      if (this.orderStatus === BatchOrderTradeStatusEnum.Placing_Order) {
        // 下单中
        to.basicData.batchOrderStatusList = [4]
        to.basicData.batchOrderPaymentStatusList = undefined
      }
      if (this.orderStatus === BatchOrderTradeStatusEnum.Wait_Pay) {
        // 待付款
        to.basicData.batchOrderStatusList = [1]
        to.basicData.batchOrderPaymentStatusList = [0]
      }
      if (this.orderStatus === BatchOrderTradeStatusEnum.Paying) {
        // 支付中
        to.basicData.batchOrderStatusList = [1]
        to.basicData.batchOrderPaymentStatusList = [1]
      }
      if (this.orderStatus === BatchOrderTradeStatusEnum.Opening) {
        // 开通中
        to.basicData.batchOrderStatusList = [1]
        to.basicData.batchOrderPaymentStatusList = [2]
      }
      if (this.orderStatus === BatchOrderTradeStatusEnum.Pay_Success) {
        // 交易成功
        to.basicData.batchOrderStatusList = [2]
        to.basicData.batchOrderPaymentStatusList = undefined
      }
      if (this.orderStatus === BatchOrderTradeStatusEnum.Closing_Pay) {
        // 交易关闭中
        to.basicData.batchOrderStatusList = [5]
        to.basicData.batchOrderPaymentStatusList = undefined
      }
      if (this.orderStatus === BatchOrderTradeStatusEnum.Close_Pay) {
        // 交易关闭
        to.basicData.batchOrderStatusList = [3]
        to.basicData.batchOrderPaymentStatusList = undefined
      }
    } else {
      to.basicData.batchOrderStatusList = undefined
      to.basicData.batchOrderPaymentStatusList = undefined
    }
    // 批次提交时间
    if (DataResolve.isWeightyArr(this.applyTime)) {
      to.basicData.batchOrderStatusChangeTime.committing = new DateScopeRequest()
      to.basicData.batchOrderStatusChangeTime.committing.begin = this.applyTime[0] || undefined
      to.basicData.batchOrderStatusChangeTime.committing.end = this.applyTime[1] || undefined
    } else {
      to.basicData.batchOrderStatusChangeTime.committing = undefined
    }
    // 交易成功时间
    if (DataResolve.isWeightyArr(this.paymentCompleteTime)) {
      to.basicData.batchOrderStatusChangeTime.completed = new DateScopeRequest()
      to.basicData.batchOrderStatusChangeTime.completed.begin = this.paymentCompleteTime[0] || undefined
      to.basicData.batchOrderStatusChangeTime.completed.end = this.paymentCompleteTime[1] || undefined
    }
    // 是否剔除0元单
    if (this.isRemoveZeroOrder) {
      to.basicData.batchOrderAmountScope = new BigDecimalScopeRequest()
      to.basicData.batchOrderAmountScope.begin = 0.01
    } else {
      to.basicData.batchOrderAmountScope = undefined
    }
    // 专题入参
    if (this.saleSource || this.saleSource === SaleChannelEnum.self) {
      to.basicData.saleChannels = [this.saleSource]
    } else {
      to.basicData.saleChannels = [SaleChannelEnum.self, SaleChannelEnum.distribution, SaleChannelEnum.topic]
    }
    // to.basicData.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(this.isFromSpecialSubject)
    to.basicData.saleChannelName = to.basicData.saleChannels.includes(SaleChannelEnum.topic)
      ? this.specialSubjectName
      : ''

    if (this.distributorId) {
      to.distributorId = this.distributorId
    }
    if (this.promotionPortalId) {
      to.portalId = this.promotionPortalId
    }
    if (this.paymentMethod) {
      to.payInfo.paymentOrderTypeList = [this.paymentMethod]
    } else {
      to.payInfo.paymentOrderTypeList = undefined
    }
    return to
  }
}

export default QueryBatchOrderListVo
