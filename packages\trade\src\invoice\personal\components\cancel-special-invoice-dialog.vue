<template>
  <div>
    <el-dialog title="作废发票" :visible.sync="cancelVisible" width="450px" class="m-dialog">
      <div>
        <el-form ref="form" label-width="auto" class="m-form f-mt20">
          <el-form-item label="发票号码：" class="is-text">{{ cancelItem.invoiceNo || '-' }}</el-form-item>
        </el-form>
      </div>
      <div slot="footer">
        <el-button @click="cancelVisible = false">取 消</el-button>
        <el-button type="primary" @click="cancelInvoice">作废发票</el-button>
      </div>
    </el-dialog>
    <el-drawer title="作废发票" :visible.sync="sureCancelVisible" size="600px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          作废后需要重新打印发票，请确认发票是否作废？
        </el-alert>
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form ref="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="作废说明：">
                <el-input
                  type="textarea"
                  :rows="6"
                  v-model="cancelReason"
                  placeholder="请输入作废说明，选填项，未填写默认无"
                />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="sureCancelVisible = false">取消</el-button>
                <el-button type="primary" @click="sureCancelInvoice">确定</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import { TitleTypeEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import MutationOffLineInvoice from '@api/service/management/trade/single/invoice/mutation/MutationOffLineInvoice'
  import TradeModule from '@api/service/management/trade/TradeModule'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    cancelDialog: boolean

    @Prop({
      type: Object
    })
    cancelItem: OffLinePageInvoiceVo

    mutationOffLineInvoice: MutationOffLineInvoice =
      TradeModule.singleTradeBatchFactor.invoiceFactor.mutationOffLineInvoice

    cancelVisible = false

    //作废理由
    cancelReason = ''

    //确定作废发票弹窗
    sureCancelVisible = false

    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }

    @Watch('cancelDialog')
    changeDialogCtrl() {
      this.cancelVisible = this.cancelDialog
    }

    @Watch('cancelVisible')
    changeDialogVisible() {
      this.$emit('update:cancelDialog', this.cancelVisible)
    }

    //作废发票
    cancelInvoice() {
      this.cancelReason = ''
      this.sureCancelVisible = true
    }

    async sureCancelInvoice() {
      if (!this.cancelReason) {
        this.cancelReason = '无'
      }
      const res = await this.mutationOffLineInvoice.invalidInvoice(this.cancelItem.invoiceId, this.cancelReason)
      if (res.isSuccess()) {
        this.$message.success('作废发票成功')
        this.cancelVisible = false
        this.sureCancelVisible = false
        this.$emit('cancelSuccess')
      } else {
        this.$message.error('作废发票失败')
      }
    }

    created() {
      this.cancelVisible = this.cancelDialog
    }
  }
</script>
