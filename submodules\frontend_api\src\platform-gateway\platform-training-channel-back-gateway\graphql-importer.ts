import getOfflineCollectiveByTrainingChannelIdInSubject from './queries/getOfflineCollectiveByTrainingChannelIdInSubject.graphql'
import getOnlineCollectiveByTrainingChannelIdInSubject from './queries/getOnlineCollectiveByTrainingChannelIdInSubject.graphql'
import getTrainingChannelDetailById from './queries/getTrainingChannelDetailById.graphql'
import listTrainingChannelCount from './queries/listTrainingChannelCount.graphql'
import pageOnlineSchoolAdminInfoInServicer from './queries/pageOnlineSchoolAdminInfoInServicer.graphql'
import pageTrainingChannelInfo from './queries/pageTrainingChannelInfo.graphql'
import pageTrainingChannelInfoInTrainingChannelAdmin from './queries/pageTrainingChannelInfoInTrainingChannelAdmin.graphql'
import pageTrainingChannelSelectCourseInSubject from './queries/pageTrainingChannelSelectCourseInSubject.graphql'
import pageTrainingChannelSimpleNewsInTrainingChannelAdmin from './queries/pageTrainingChannelSimpleNewsInTrainingChannelAdmin.graphql'

export {
  getOfflineCollectiveByTrainingChannelIdInSubject,
  getOnlineCollectiveByTrainingChannelIdInSubject,
  getTrainingChannelDetailById,
  listTrainingChannelCount,
  pageOnlineSchoolAdminInfoInServicer,
  pageTrainingChannelInfo,
  pageTrainingChannelInfoInTrainingChannelAdmin,
  pageTrainingChannelSelectCourseInSubject,
  pageTrainingChannelSimpleNewsInTrainingChannelAdmin
}
