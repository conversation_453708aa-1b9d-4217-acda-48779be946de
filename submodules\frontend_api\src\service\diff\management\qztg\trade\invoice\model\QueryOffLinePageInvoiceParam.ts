import { OfflineInvoiceRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

import QueryOffLinePageInvoiceParam from '@api/service/management/trade/single/invoice/query/vo/QueryOffLinePageInvoiceParam'
import { SaleChannelEnum } from '@api/service/diff/management/qztg/trade/enums/SaleChannelType'

export default class QueryPageInvoiceParam extends QueryOffLinePageInvoiceParam {
  static to(queryPageInvoiceParam: QueryPageInvoiceParam) {
    const offlineInvoiceRequest = Object.assign(
      new OfflineInvoiceRequest(),
      QueryOffLinePageInvoiceParam.to(queryPageInvoiceParam)
    )
    if (queryPageInvoiceParam.saleSource || queryPageInvoiceParam.saleSource === SaleChannelEnum.self) {
      offlineInvoiceRequest.associationInfo.saleChannels = [queryPageInvoiceParam.saleSource]
    } else {
      offlineInvoiceRequest.associationInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic,
        SaleChannelEnum.huayi
      ]
    }
    return offlineInvoiceRequest
  }
}
