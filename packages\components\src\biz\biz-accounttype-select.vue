<route-meta>
{
"title": "科目类型选择器"
}
</route-meta>
<template>
  <el-select v-model="selected" :placeholder="placeholder" @clear="clear" class="el-input" filterable clearable>
    <el-option
      v-for="item in accountTypeOptions"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import SubjectTypeVo from '@api/service/common/basic-data-dictionary/query/vo/SubjectTypeVo'
  import QuerySubjectType from '@api/service/common/basic-data-dictionary/query/QuerySubjectType'

  @Component
  export default class extends Vue {
    selected = ''
    accountTypeOptions: Array<SubjectTypeVo> = new Array<SubjectTypeVo>()

    @Prop({
      type: String,
      default: '请选择科目类型'
    })
    placeholder: string

    //  行业属性id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    // 行业id
    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    //行业属性分类id
    @Prop({
      type: String,
      default: ''
    })
    categoryCode: string
    @Watch('value')
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }

    @Watch('industryPropertyId', {
      immediate: true,
      deep: true
    })
    async industryPropertyIdChange(val: any) {
      if (val) {
        await this.getAccountTypeOptions()
      }
    }

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: SubjectTypeVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    // 专业科目类型Id
    professionalSubjectTypeId = ''
    // 公需+专业科目类型Id
    mixedSubjectTypeId = ''
    /**
     * 获取科目类型
     */
    async getAccountTypeOptions() {
      const res = await QuerySubjectType.querySubjectTypeList(this.industryPropertyId, this.industryId)
      this.accountTypeOptions = res.isSuccess() ? QuerySubjectType.subjectTypeList : ([] as SubjectTypeVo[])
      // TODO 很粗糙的判断方式，待优化
      this.accountTypeOptions?.forEach((el: SubjectTypeVo) => {
        if (el.name === '专业') {
          this.professionalSubjectTypeId = el.propertyId
        }
        if (el.name === '公需+专业') {
          this.mixedSubjectTypeId = el.propertyId
        }
        this.$emit('subjectTypeInfos', {
          professionalSubjectTypeId: this.professionalSubjectTypeId,
          mixedSubjectTypeId: this.mixedSubjectTypeId
        })
      })
    }

    clear() {
      this.selected = undefined
    }
  }
</script>
