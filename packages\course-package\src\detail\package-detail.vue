<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">基础信息</span>
      </div>
      <el-row type="flex" justify="center" class="width-limit f-p20">
        <el-col :md="14" :lg="13" :xl="10">
          <el-form ref="form" label-width="140px" class="m-form">
            <el-form-item label="课程包名称：" class="is-text">{{ coursePackageDetailVo.name }}</el-form-item>
            <el-form-item label="展示名称：" class="is-text">{{ coursePackageDetailVo.showName || '-' }}</el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
    <el-card shadow="never" class="m-card f-mb15">
      <div slot="header" class="f-clear">
        <span class="tit-txt f-fl"
          >已选待确认课程（共 {{ chooseCourseLength }} 门 ， {{ chooseCoursePeriod }} 学时）</span
        >
      </div>
      <div slot="header" class="f-clear">
        <!-- <span class="tit-txt f-fl"
          >已选课程（共 {{ mutationUpdateCoursePackage.updateCoursePackageVo.addedList.length }} 门）</span
        > -->
      </div>
      <el-table stripe :data="courseInCoursePackage" max-height="500px" class="m-table" v-loading="loading">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column prop="name" label="课程名称" min-width="300"> </el-table-column>
        <el-table-column prop="physicsPeriod" label="物理学时" min-width="100" align="center"> </el-table-column>
        <el-table-column prop="period" label="学习学时" min-width="100" align="center"> </el-table-column>
        <el-table-column prop="createTime" label="创建时间" min-width="180"> </el-table-column>
        <el-table-column label="操作" width="140" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="previewCourse(scope.row.id)">预览</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <hb-pagination
        :total-size="totalSize"
        :page="page"
        class="mt10"
        @size-change="pageSizeChange"
        @current-change="currentPageChange"
      >
      </hb-pagination> -->
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch, Mixins } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import MutationUpdateCoursePackage from '@api/service/management/resource/course-package/mutation/MutationUpdateCoursePackage'
  import CreateCoursePackageVo from '@api/service/management/resource/course-package/mutation/vo/CreateCoursePackageVo'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import CoursePackageDetailVo from '@api/service/management/resource/course-package/query/vo/CoursePackageDetailVo'
  import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
  import PreviewCourseMixins from '@hbfe/jxjy-admin-common/src/mixins/PreviewCourseMixins'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'

  @Component
  export default class extends Mixins(PreviewCourseMixins) {
    poolId = ''
    loading = false
    id = ''
    // 查询参数
    page: UiPage
    chooseCourseLength = 0
    chooseCoursePeriod = 0

    mutationUpdateCoursePackage: MutationUpdateCoursePackage
    coursePackageDetailVo: CoursePackageDetailVo = new CoursePackageDetailVo()
    courseInCoursePackage: Array<CourseInCoursePackage> = new Array<CourseInCoursePackage>()
    constructor() {
      super()
      // this.mutationUpdateCoursePackage = new MutationUpdateCoursePackage()
      // this.mutationUpdateCoursePackage.updateCoursePackageVo = new UpdateCoursePackageVo()
    }
    // get courseList() {
    //   return CoursePoolModule.getCourseInPools(this.coursePoolDetail.id)
    // }
    // get courseInPoolsCache() {
    //   return CoursePoolModule.courseInPoolsCache
    // }

    // get totalSize() {
    //   return this.courseInPoolsCache[0]?.totalSize
    // }
    @Prop({
      type: Object,
      // required: true,
      default: () => new CreateCoursePackageVo()
    })
    createCoursePackage: CreateCoursePackageVo
    @Watch('createCoursePackage', {
      immediate: false,
      deep: true
    })
    createCoursePackageChange(val: any) {
      if (val) {
        console.log(val, 'createCoursePackage')
      }
    }
    pageSizeChange(val: number) {
      this.page.pageSize = val
      this.page.pageNo = 1
      this.doQueryPage()
    }

    currentPageChange(val: number) {
      this.page.pageNo = val
      this.doQueryPage()
    }
    async doQueryPage() {
      // this.loading = true

      // const res = await CoursePoolModule.loadCourseInPools({
      //   page: true,
      //   pageNo: this.page.pageNo,
      //   pageSize: this.page.pageSize,
      //   poolId: this.poolId
      // })
      // if (res.code !== 200) {
      //   this.$message.error('加载失败')
      // }
      this.loading = true

      // this.mutationUpdateCoursePackage = await ResourceModule.coursePackageFactory.getUpdateCoursePackage(this.id)
      this.coursePackageDetailVo = await ResourceModule.coursePackageFactory.queryCoursePackage.queryCoursePackageById(
        this.id
      )
      this.courseInCoursePackage =
        await ResourceModule.coursePackageFactory.queryCoursePackage.queryCourseListInCoursePackage(this.id)
      this.courseInCoursePackage.sort((a, b) => {
        return a.sort - b.sort
      })

      this.chooseCoursePeriod = 0
      this.courseInCoursePackage.forEach((item: any) => {
        console.log(item.period, 'item.period')
        this.chooseCoursePeriod = CalculatorObj.add(this.chooseCoursePeriod, item.period)
      })
      this.chooseCourseLength = this.courseInCoursePackage.length

      // this.mutationUpdateCoursePackage.updateCoursePackageVo.addedList
      this.loading = false
    }

    async activated() {
      this.id = this.$route.params.id

      //   this.poolId = this.$route.params.id
      this.doQueryPage()
      //   await CoursePoolModule.loadCoursePoolDetail(this.poolId)
      //   const coursePoolList = CoursePoolModule.coursePoolList
      //   const detail = coursePoolList.find(el => {
      //     return el.id === this.poolId
      //   })
      //   this.coursePoolDetail = detail
    }
  }
</script>
