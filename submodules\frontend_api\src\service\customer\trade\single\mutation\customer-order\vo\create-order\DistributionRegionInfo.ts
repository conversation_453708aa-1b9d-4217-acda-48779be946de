/**
 * @description 分销地区信息
 */
class DistributionRegionInfo {
  /**
   * 地区码
   * @description
   * 形如：鼓楼区 => ['350000', '350100', '350102']
   */
  regionCode: string[] = []
  /**
   * 地区名称
   * @description
   * 形如：鼓楼区 => ['福建省', '福州市', '鼓楼区']
   */
  regionName: string[] = []
  /**
   * 地区全路径列表，形如：'/350000/350100/350102'
   */
  regionPath = ''
  /**
   * 地区等级，省-1、市-2、区县-3
   */
  regionLevel = 0
  /**
   * 下辖分销地区列表
   */
  children: DistributionRegionInfo[] = []
}

export default DistributionRegionInfo
