import AuthModule from '@api/service/common/auth/AuthModule'
import Authentication from '@api/service/common/authentication/Authentication'
import { ElForm } from 'element-ui/types/form'
import { Component, Ref, Vue } from 'vue-property-decorator'
import { CaptchaApplyRequest, SmsCodeApplyRequest } from '@hbfe-ms/ms-basicdata-domain-gateway'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import { BusinessTypeEnum } from '@hbfe-biz/biz-authentication/dist/enums/BusinessTypeEnum'
import { AccountType, SendMessageParams } from '@api/service/common/authentication/interfaces/LoginParams'
import LoginParams from '@hbfe-biz/biz-authentication/dist/models/LoginParams'
import { IdentityType } from '@hbfe-biz/biz-authentication/dist/enums/IdentityType'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
import UnitDialog from '@hbfe/jxjy-admin-authentication/src/login-sign/components/unit-dialog.vue'
import RootModule from '@/store/RootModule'
import OnlineSchoolModule from '@api/service/training-institution/online-school/OnlineSchoolModule'
import { ImageCaptchaTrack } from '@api/ms-gateway/ms-identity-authentication-v1'
import BizSlider from '@hbfe/jxjy-admin-components/src/biz/biz-slider.vue'

export class LoginResult {
  code: number
  message: string

  constructor(code: number, message: string) {
    this.code = code
    this.message = message
  }

  isSuccess(): boolean {
    return this.code === 200
  }
}

export class Incorrect {
  account: string
  frequency: number
  time: number
}

export enum RoleType {
  ADMIN = 1,
  SERVICEPROVIDER = 2,
  REGIONADMINISTRATOR = 3,
  DISTRIBUTOR = 4
}

export enum SendSmsType {
  STUDENT = 'student',
  ADMIN = 'admin',
  PROVIDER = 'provider',
  REGION = 'region'
}

const countDownTime = 60

@Component
class LoginCore extends Vue {
  @Ref('loginForm') loginForm: ElForm
  @Ref('loginFormSec') loginFormSec: ElForm
  success = 5
  dialogVisible3 = false
  $authentication: Authentication
  // 判断当前登录角色是否是分销商
  isFxlogin = QueryManagerDetail?.hasCategory(CategoryEnums.fxs)
  validateCodePic = ''
  passwordLogin = false
  phoneNumbe = ''
  // 验证码发送
  loginSending = true
  // 预计倒计时时间
  count = 60
  // 发送后倒计时计时
  countTime = 60
  dialog1 = false
  // 实为布尔值，此处赋值帐号值。用来解决记住帐号和密码后，图形验证码无法输入问题
  isPhoneNumberValid = false
  getMessageCoding = false
  // 发送后倒计时计时
  countDownTime = countDownTime
  applyingCaptcha = false
  rememberPassword = false
  loginIng = false
  loginResult: LoginResult = new LoginResult(200, '')
  formData = {
    roleType: RoleType.ADMIN,
    account: '',
    password: '',
    accountType: 2,
    extAttributes: '',
    // 图形验证码
    captcha: '',
    phoneCaptcha: ''
  }
  timer = 0

  intervalTimer: any = null

  rules = {}
  // 分销单位组件
  isShow = false

  sureTime = 5
  @Ref('changePhoneRef')
  changePhoneRef: any
  @Ref('unitDialogRef')
  unitDialogRef: UnitDialog
  // 应用方类型
  applicationMemberType: number = null
  // 应用方id
  applicationMemberId = ''

  changePhoneLoading = false
  isPhoneCaptchaValid = true
  phoneCaptchaVaildFlag = false
  changePhoneFrom = {
    phone: '',
    smsCode: '',
    captcha: ''
  }
  // 验证码发送
  sending = true
  validatePic = ''

  /**
   * 验证返回的token
   */
  sliderCaptchToken = ''

  /**
   * 验证返回的data
   */
  sliderCaptchData = new ImageCaptchaTrack()

  /**
   * 获取是否为滑块验证
   */
  onlineSchoolModule = new OnlineSchoolModule()

  localToke = ''

  /**
   * 是否登录
   */
  isUseSlider = false

  // 多次错误账号信息
  incorrectArr = new Array<Incorrect>()
  // 支持连续错误次数
  continuousWrong = 5
  // 分钟
  minute = 60
  dialogVisibleFourth = false
  deadlineFourth = Date.now() + 1000 * 60 * 30
  // 显示短信验证码登录
  showSmsModal = false
  continueLoading = false
  smsToken = ''
  phoneNumber = ''
  stopTimerCount() {
    window.clearInterval(this.timer)
  }

  stopIntervalTimer() {
    if (this.intervalTimer) {
      clearTimeout(this.intervalTimer)
      this.intervalTimer = null
    }
  }

  countDown(time: number) {
    this.countTime = time

    console.log('countStart')
    this.intervalTimer = setInterval(() => {
      if (this.countTime === 0) {
        this.loginSending = true
        clearInterval(this.intervalTimer)
        this.countTime = this.count
      } else {
        this.countTime = this.countTime - 1
      }
    }, 1000)
  }

  async sendMessageWithoutCaptcha() {
    this.loginSending = false
    this.applyingCaptcha = true
    const params = new SmsCodeApplyRequest()
    if (this.formData.roleType === RoleType.SERVICEPROVIDER) {
      //   params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.coursewareSupplierPhoneNumLoginToken)
    } else if (this.formData.roleType === RoleType.REGIONADMINISTRATOR) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionLoginToken)
    } else if (this.formData.roleType === RoleType.DISTRIBUTOR) {
      params.token = ConfigCenterModule.getFrontendApplication(
        frontendApplication.distributionAdministratorPhoneLoginToken
      )
    } else {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superPhoneNumLoginToken)
    }
    params.businessType = BusinessTypeEnum.login_account
    params.phone = this.formData.account
    try {
      const res = await this.$authentication.verify.msSendSmsCode(params)
      if (res.data.code === 409) {
        this.$message.error('一个手机号一天只能接收4条短信，请使用帐号登录。')
      } else if (res.data.code === 410) {
        this.$message.error('发送验证码失败')
      } else if (res.data.code === 701) {
        this.$message.error('请重新验证图形验证码后，再次获取短信验证码')
      } else if (res.data.code === 515) {
        this.$message.error('不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)')
      } else if (res.data.code === 400) {
        this.$message.error('token无效')
      } else if (res.data.code === 509) {
        this.$message.error('未绑定手机号')
      } else if (res.data.code === 514) {
        this.$message.error('token中未携带手机号')
      }
      if (res.status.code === 200) {
        this.countDown(this.count)
        return
      } else {
        this.$message.error('发送验证码失败')
        this.loginSending = true
      }
    } catch (e) {
      console.log(e)
      const message = e?.message || '获取验证码失败，'
      this.$message.error(message)
    } finally {
      this.applyingCaptcha = false
    }
  }
  async submit() {
    this.changePhoneLoading = true
    try {
      await this.changePhoneRef.validate()
      const res = await this.$authentication.verify.msValidSmsCode(
        this.changePhoneFrom.smsCode,
        this.changePhoneFrom.phone
      )
      if (res.status.code === 200) {
        const changeRes = await this.$authentication.account.bindPhone(
          this.$authentication.verify.shortMessageCaptchaToken
        )
        if (changeRes.status.code === 200) {
          if (changeRes?.data?.code === '200') {
            this.dialogVisible3 = true
            this.sureTimeDown(this.sureTime)
            this.changePhoneLoading = false
          } else {
            this.$message.error(changeRes?.data?.message)
            await this.refreshValidateCodePicPhone()
            this.changePhoneLoading = false
          }
        } else {
          this.$message.error('绑定失败')
          await this.refreshValidateCodePicPhone()
          this.changePhoneLoading = false
        }
      } else {
        this.$message.error('短信验证码错误')
        await this.refreshValidateCodePicPhone()
        this.changePhoneLoading = false
      }
    } catch (e) {
      await this.refreshValidateCodePicPhone()
      this.$message.error('绑定失败')
      this.changePhoneLoading = false
    }
  }
  /**
   * 获取图片验证码
   */
  async refreshValidateCodePicPhone() {
    this.isPhoneCaptchaValid = true
    this.phoneCaptchaVaildFlag = false
    const params = new CaptchaApplyRequest()
    if (this.formData.roleType === RoleType.SERVICEPROVIDER) {
      // params.token = ConfigCenterModule.getFrontendApplication(
      //   frontendApplication.coursewareSupplierChangePhoneNumToken
      // )
    } else if (this.formData.roleType === RoleType.REGIONADMINISTRATOR) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionChangePhoneNumToken)
    } else if (this.formData.roleType === RoleType.DISTRIBUTOR) {
      params.token = ConfigCenterModule.getFrontendApplication(
        frontendApplication.distributionAdministratorPwdLoginToken
      )
    } else {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superChangePhoneNumToken)
    }
    params.businessType = BusinessTypeEnum.change_binding_phone
    const res = await this.$authentication.verify.applyCaptcha(params)
    if (res.status.code == 200) {
      this.validatePic = `data:image/jpeg;base64,${res?.data?.captcha}`
    }
  }

  // 验证码倒计时
  sureTimeDown(time: number) {
    let timer = time
    const sureTimeDown = setInterval(() => {
      if (timer === 0) {
        clearInterval(sureTimeDown)
        this.dialogVisible3 = false
        this.dialog1 = false
        this.sureTime = 5
        window.location.replace(this.$router.resolve('/home').href)
        window.location.reload()
      } else {
        timer--
        this.sureTime = timer
      }
    }, 1000)
  }
  // 获取短信验证码
  async getPhoneCaptcha() {
    this.loginSending = false
    this.countDown(this.count)

    const valStatus = await AuthModule.preValidServicerLogin(this.formData.account)
    if (!valStatus.isSuccess()) {
      const errcode = valStatus.errors[0].code
      //30407 该账号不存在
      // 30408 该帐号无合作机构
      if (errcode == 30407) {
        return this.$message.error('帐号不存在，请确认是否已申请合作')
      } else if (errcode == 30408) {
        return this.$message.error('该帐号已中止合作，请联系合作的培训机构确认')
      } else {
        return this.$message.error(valStatus.getMessage())
      }
    }
    this.applyingCaptcha = true
    const sendMessage = new SendMessageParams()
    sendMessage.phoneNumber = this.formData.account
    sendMessage.accountType = AccountType.admin
    try {
      // this.$authentication.captchaToken = ''
      const result = await this.$authentication.verify.sendMessage(sendMessage)
      this.getMessageCoding = true
      if (result.status.code != 200) {
        this.loginResult = result.status
        return
      }
      this.countDown(this.count)
    } catch (e) {
      console.log(e)
      const message = e?.message || '获取验证码失败，'
      this.$message.warning(message)
    } finally {
      this.applyingCaptcha = false
    }
  }

  async refreshValidateCodePic() {
    const params = new CaptchaApplyRequest()
    if (this.formData.roleType === RoleType.SERVICEPROVIDER) {
      // res = await this.$authentication.applyProviderCaptcha()
    } else if (this.formData.roleType === RoleType.REGIONADMINISTRATOR) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionAdminLoginToken)
    } else if (this.formData.roleType === RoleType.DISTRIBUTOR) {
      params.token = ConfigCenterModule.getFrontendApplication(
        frontendApplication.distributionAdministratorPwdLoginToken
      )
    } else {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superLoginToken)
    }
    params.businessType = BusinessTypeEnum.login_account
    const res = await this.$authentication.verify.applyCaptcha(params)
    this.validateCodePic = `data:image/jpeg;base64,${res?.data?.captcha}`
  }

  async doPasswordLogin() {
    const loginParams = new LoginParams()
    loginParams.identity = this.formData.account
    loginParams.password = this.formData.password
    loginParams.longTerm = this.rememberPassword
    loginParams.captchaValue = this.formData.captcha
    loginParams.sliderCaptchaValue = this.sliderCaptchData
    loginParams.accountType = AccountType.admin
    if (this.isUseSlider && this.formData.roleType != 4) {
      loginParams.token = this.sliderCaptchToken
    } else {
      loginParams.token = this.$authentication.verify.captchaToken
    }

    if (this.formData.roleType == RoleType.DISTRIBUTOR) {
      const response = await this.$authentication.doFxLogin(IdentityType.account_pwd_captcha, loginParams)
      this.$authentication.account.doRememberLoginInfo(loginParams)
      return response
    } else {
      // const response = await this.$authentication.doLogin(IdentityType.account_pwd_captcha, loginParams)
      const response = await this.$authentication.doAuthentication(loginParams)
      this.$authentication.account.doRememberLoginInfo(loginParams)
      return response
    }
  }

  async shortMessageLogin() {
    const shortMessageLoginParams = new LoginParams()
    shortMessageLoginParams.phoneNumber = this.formData.account
    shortMessageLoginParams.smsCode = this.formData.phoneCaptcha
    shortMessageLoginParams.longTerm = this.rememberPassword
    shortMessageLoginParams.token = this.$authentication.verify.shortMessageCaptchaToken
    return await this.$authentication.doLogin(IdentityType.sms_code, shortMessageLoginParams)
  }

  async captchaValid(rule: any, value: any, callback: (error?: Error) => void) {
    if (rule.regexp.test(value)) {
      try {
        const res = await this.$authentication.verify.msValidateCaptcha(value)
        if (!res.status.isSuccess() || res.data.code !== 200) {
          callback(new Error('验证失败'))
        }
        callback()
      } catch (e) {
        // await this.refreshValidateCodePic()
        callback(new Error('验证失败'))
      }
    } else {
      callback(new Error('验证码为4位数'))
    }
  }

  public async created() {
    this.formData.account = this.$authentication.account.rememberLoginInfo.account
    this.formData.password = this.$authentication.account.rememberLoginInfo.password
    this.rememberPassword = this.$authentication.account.rememberLoginInfo.longTerm
    this.rules = {
      roleType: [
        {
          required: true,
          message: '请选择登录帐号类型',
          trigger: ['blur', 'change']
        }
      ],
      account: [
        {
          required: true,
          validator: async (rules: any, value: any, callback: (message?: any) => {}) => {
            const message = this.passwordLogin ? '账号' : '手机号'
            if (!value) {
              this.isPhoneNumberValid = false
              return callback(new Error(`请输入${message}`))
            } else {
              if (!this.passwordLogin) {
                // 宽松匹配
                if (!/^(?:(?:\+|00)86)?1\d{10}$/.test(value)) {
                  this.isPhoneNumberValid = false
                  return callback(new Error('手机号格式不正确'))
                }
              }
            }
            this.isPhoneNumberValid = true
            callback()
          },
          trigger: ['blur', 'change']
        }
      ],
      password: [
        {
          required: true,
          message: '请输入密码',
          trigger: 'blur'
        }
      ],
      captcha: [
        {
          regexp: /[a-zA-z0-9]{4}/,
          validator: this.captchaValid,
          required: true,
          trigger: 'none'
        }
      ]
    }
    const res = await this.onlineSchoolModule.getOnlineSchoolConfig()
    if (res.status.isSuccess()) {
      this.isUseSlider = res.data.validateMethodConfig.enabledSlideCircus
    }
    // 获取登录票
    try {
      await this.refreshValidateCodePic()
    } catch (e) {
      console.log(e)
    }
    // this.unitDialogRef.isShow = true
  }

  // 加载钉钉二维码
  ddQRCodeLoading = true

  async mounted() {
    // this.doRefreshDdQRCode()
  }

  async loginSuccess() {
    // 123
    console.log('dengls')
  }
  dialogVisible() {
    this.dialogVisible3 = true
    this.successTime()
  }
  successTime() {
    let timer = 5
    const timers = setInterval(() => {
      if (timer === 0) {
        clearInterval(timers)
        this.success = 5
        this.dialogVisible3 = false
        this.dialog1 = false
        this.doLogin()
      } else {
        timer--
        this.success = timer
      }
    }, 1000)
  }
  loginFirst() {
    this.dialog1 = true
  }

  async roleTypeChange() {
    await this.refreshValidateCodePic()
  }

  /**
   * 滑块校验结果
   */
  async validCaptcha(options: { id: string; data: any }) {
    this.sliderCaptchData = options.data
    this.sliderCaptchToken = options.id
    if (this.$refs.sliderRef) {
      // await (this.$refs.sliderRef as BizSlider).destroyWindow()
      this.loginIng = false
    }
    await this.doLogin()
  }

  /**
   * 登入前是否有滑块验证
   */
  async beforeLogin() {
    if (!this.judge()) {
      return false
    }
    await this.loginForm.validate()
    this.$authentication.removeToken()
    if (this.isUseSlider && this.formData.roleType != 4) {
      if (this.formData.roleType === RoleType.SERVICEPROVIDER) {
        // res = await this.$authentication.applyProviderCaptcha()
      } else if (this.formData.roleType === RoleType.REGIONADMINISTRATOR) {
        this.localToke = ConfigCenterModule.getFrontendApplication(frontendApplication.regionAdminLoginToken)
      } else if (this.formData.roleType === RoleType.DISTRIBUTOR) {
        this.localToke = ConfigCenterModule.getFrontendApplication(
          frontendApplication.distributionAdministratorPwdLoginToken
        )
      } else {
        this.localToke = ConfigCenterModule.getFrontendApplication(frontendApplication.superLoginToken)
      }
      if (this.$refs.sliderRef) {
        await (this.$refs.sliderRef as BizSlider).init()
      }
    } else {
      this.sliderCaptchData = new ImageCaptchaTrack()
      this.sliderCaptchToken = ''
      await this.doLogin()
    }
  }

  judge() {
    const sixtyMinutesInSeconds = 60 * this.minute
    // 获取 localStorage 中的数据
    const incorrect = localStorage.getItem('admin.Incorrect')
    let flag = true
    // 如果 localStorage 中有数据
    if (incorrect) {
      this.incorrectArr = JSON.parse(incorrect)
      // 查找与当前账户匹配的记录的下标
      const findIndex = this.incorrectArr.findIndex((item) => item.account === this.formData.account)
      // 如果找到了匹配的记录
      if (findIndex !== -1 && this.incorrectArr[findIndex].frequency === this.continuousWrong) {
        const findParam = this.incorrectArr[findIndex]
        const currentTimestamp = Math.floor(Date.now() / 1000) // 当前时间的时间戳（秒）
        const timeDifference = currentTimestamp - findParam.time // 时间差（秒）
        if (timeDifference > sixtyMinutesInSeconds) {
          // 如果时间差大于 60 分钟，重置频率和时间
          // findParam.frequency = 0;
          // findParam.time = 0;
          this.incorrectArr.splice(findIndex, 1)
        } else {
          // 否则，计算剩余时间并设置倒计时
          // const remainingSeconds = sixtyMinutesInSeconds - timeDifference;
          // this.deadlineFourth = Date.now() + remainingSeconds * 1000;
          // this.dialogVisibleFourth = true;
          this.$confirm(`由于您的账号或密码多次输入错误，账号被暂时锁定，请联系管理人员。`, '提示', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            showClose: false
          })
          flag = false
        }
      }
    } else {
      // 如果 localStorage 中没有数据，初始化 incorrectArr
      this.incorrectArr = []
    }
    console.log(this.incorrectArr, 'this.incorrectArr')
    return flag
  }

  async doLogin() {
    try {
      // await this.loginForm.validate()
      this.loginIng = true
      let result: any
      if (!this.passwordLogin) {
        result = await this.shortMessageLogin()
      } else {
        result = await this.doPasswordLogin()
      }
      console.log('result111', result)
      if (result.status.isSuccess() && result.data.code === '200') {
        // 分销商登录
        if (this.formData.roleType == RoleType.DISTRIBUTOR) {
          this.operationSuccessful()
        } else {
          const tokenMetadata = result.data.tokenMetadata
          if (tokenMetadata.phone) {
            this.phoneNumber = tokenMetadata.phone
            this.smsToken = result.data.identityAuthenticationToken
            this.showSmsModal = true
          } else {
            await this.refreshValidateCodePic()
            if (this.$refs.sliderRef) {
              await (this.$refs.sliderRef as BizSlider).destroyWindow()
              this.loginIng = false
            }
            this.loginIng = false
            this.$confirm('当前账号尚未绑定手机号码，请联系管理员进行绑定。', '提示', {
              confirmButtonText: '确定',
              showCancelButton: false,
              type: 'warning'
            })
          }
        }
      } else {
        this.loginIng = false
        this.$authentication.removeToken()

        if (result.data?.code === '40006') {
          const incorrectItem = this.incorrectArr.find((el) => el.account === this.formData.account)
          if (incorrectItem) {
            incorrectItem.frequency += 1
            incorrectItem.time = Math.floor(Date.now() / 1000)
          } else {
            this.incorrectArr.push({
              account: this.formData.account,
              frequency: 1,
              time: Math.floor(Date.now() / 1000)
            })
          }
          const num = incorrectItem?.frequency || 1
          const remainingAttempts = this.continuousWrong - num
          let message = ''
          if (remainingAttempts > 0) {
            message = `请输入正确的账号或密码，连续输错${this.continuousWrong}次后，账号将被锁定，您还可重试${remainingAttempts}次！`
          } else {
            message = `您多次账号或密码输入错误，账号被暂时锁定，请${this.minute}分钟后再尝试或联系管理人员。`
          }
          this.$confirm(message, '提示', {
            confirmButtonText: '我知道了',
            showCancelButton: false,
            showClose: false
          })
          this.loginResult = new LoginResult(result.data.code, message)
          localStorage.setItem('admin.Incorrect', JSON.stringify(this.incorrectArr))
          console.log(this.incorrectArr, 'incorrectArr')
        } else if (
          this.isUseSlider &&
          this.formData.roleType != 4 &&
          (result.data.code == '70002' || result.data.code == '70003' || result.data.code == '70004')
        ) {
          this.$message.error('验证失败')
          if (this.isUseSlider && this.formData.roleType != 4) {
            if (this.$refs.sliderRef) {
              await (this.$refs.sliderRef as BizSlider).reloadCaptcha()
              this.loginIng = false
            }
          }
          return
        } else {
          this.loginResult = new LoginResult(result.data.code, result.data.message)
        }
        if (this.isUseSlider && this.formData.roleType != 4) {
          if (this.$refs.sliderRef) {
            await (this.$refs.sliderRef as BizSlider).destroyWindow()
            this.loginIng = false
          }
        } else {
          await this.refreshValidateCodePic()
        }
        result.code == 4201 && (result.message = '验证码不正确，请重新输入')
        if (result.data?.message && result.data?.code != '40006') {
          this.$message.error(result.data.message)
        } else if (result.data.code === '40006') {
          // this.$message.error('账号密码不匹配，请检查输入是否有误。请确认是否已注册。')
        }
      }
    } catch (e) {
      this.$authentication.removeToken()
      this.loginIng = false
      if (e.data.code === '40006') {
        this.loginResult = new LoginResult(e.data.code, '账号密码不匹配，请检查输入是否有误。请确认是否已注册。')
      } else {
        this.loginResult = new LoginResult(e.code, e.message)
      }
      e.code == 4201 && (e.message = '验证码不正确，请重新输入')
      if (e.message && e.data?.code != '40006') {
        this.$message.error(e.message)
      } else if (e.data?.code === '40006') {
        this.$message.error('账号密码不匹配，请检查输入是否有误。请确认是否已注册。')
      }

      await this.refreshValidateCodePic()
    }
  }

  // 去登录
  async operationSuccessful() {
    try {
      this.continueLoading = true
      const findIndex = this.incorrectArr.findIndex((item) => item.account === this.formData.account)
      if (findIndex !== -1) {
        this.incorrectArr.splice(findIndex, 1)
      }
      localStorage.setItem('admin.Incorrect', JSON.stringify(this.incorrectArr))
      let response
      if (!this.passwordLogin) {
        response = (await this.$authentication.ssoAuth()) as any
      } else {
        response = (await this.$authentication.ssoAuth()) as any
      }
      console.log(response, 'response111')
      if (response.code !== 200 || !response.data?.access_token) {
        await this.refreshValidateCodePic()
        this.$authentication.removeToken()
        return
      }
      // 分销角色登录成功时请求单位列表
      if (this.formData.roleType === RoleType.DISTRIBUTOR) {
        await this.isFxLoginStep()
        return
      }

      this.$emit('login-result', true)
      const messageContent = {
        message: 'login-success',
        type: 'admin'
      }
      if (this.$util?.channel) {
        this.$util.channel.postMessage(messageContent)
      }
      await this.loginSuccess()
    } catch (e) {
      if (this.isUseSlider && this.formData.roleType != 4) {
        if (this.$refs.sliderRef) {
          await (this.$refs.sliderRef as BizSlider).reloadCaptcha()
          this.loginIng = false
        }
      }
      await this.refreshValidateCodePic()
      this.$authentication.removeToken()
      this.loginResult = new LoginResult(e.code, e.message)
    } finally {
      if (this.isUseSlider && this.formData.roleType != 4) {
        if (this.$refs.sliderRef) {
          await (this.$refs.sliderRef as BizSlider).destroyWindow()
          this.loginIng = false
        }
      }
      this.loginIng = false
      this.continueLoading = false
    }
  }

  async smsCodeLogin() {
    this.loginIng = true
    const params = new LoginParams()
    params.phoneNumber = this.formData.account
    params.smsCode = this.formData.phoneCaptcha

    try {
      await this.loginFormSec.validate()

      const codeVaildRes = await this.$authentication.verify.msValidSmsCode(params.smsCode, params.phoneNumber)
      if (codeVaildRes?.status?.code !== 200) {
        this.$message.error('验证码未知错误')
        this.loginIng = false
        return
      } else if (codeVaildRes?.data?.code !== 200) {
        if (codeVaildRes?.data?.code === 500) {
          this.$message.error('请输入正确验证码')
        } else if (codeVaildRes?.data?.code === 408) {
          this.$message.error('验证码超时')
        } else {
          this.$message.error('验证码未知错误')
        }
        this.loginIng = false
        return
      }

      params.token = this.$authentication.verify.shortMessageCaptchaToken
      // todo
      let res
      if (this.formData.roleType == RoleType.DISTRIBUTOR) {
        res = await this.$authentication.doFxLogin(IdentityType.sms_code, params)
      } else {
        res = await this.$authentication.doLogin(IdentityType.sms_code, params)
      }
      if (res?.status?.code === 200) {
        const response = (await this.$authentication.ssoAuth()) as any
        if (response.code !== 200 || !response.data?.access_token) {
          this.$message.error(res?.data?.message)
          this.loginIng = false
          return
        }

        // 分销角色登录成功时请求单位列表
        if (this.formData.roleType === RoleType.DISTRIBUTOR) {
          await this.isFxLoginStep()
        }

        this.$emit('login-result', true)
        this.loginIng = false
        await this.loginSuccess()
      } else {
        this.$message.error(res?.data?.message)
        this.loginIng = false
      }
    } catch (e) {
      console.log(e, 'rejecte')
      if (e?.code === 4001) {
        this.$message.error('账号不存在')
      } else {
        const message = e?.message || '登录失败'
        if (message) {
          this.$message.error(message)
        }
      }
      this.loginIng = false
    }
  }

  async isFxLoginStep() {
    // 只有一个单位时直接登录跳转,否则走弹窗选择单位
    if ((await this.queryUnitList()) == 'stopLogin') {
      await this.$authentication.ssoLogout()
    } else if (this.unitDialogRef.unitModel?.length > 1) {
      // （多个单位）请求列表（登录成功时）
      RootModule.SET_MENU_LIST([])
      await QueryManagerDetail.queryManagerDetail()
      location.hash = '/welcome'
    } else {
      // 单个单位直接赋值并登录
      await this.unitDialogRef.getUnitToken(this.unitDialogRef.unitModel[0])
      location.hash = '/fx/distribution/promotion-gateway/check'
      location.reload()
    }
  }

  // fx请求单位列表
  async queryUnitList() {
    const res = await QueryManagerDetail.changeAuthorizationUnitInfoList()
    if (res?.data?.code == '200') {
      this.unitDialogRef.unitModel = QueryManagerDetail.distributionUnitInformationList
    } else if (res?.data?.code == '517') {
      this.$message.error('非本网校分销商账号')
      return 'stopLogin'
    } else {
      this.$message.error('账号或密码有误')
      return 'stopLogin'
    }
  }

  beforeDestroy() {
    this.loginIng = false
  }

  /**
   * 更新加载钉钉二维码
   */
  async doRefreshDdQRCode() {
    // this.ddQRCodeLoading = true
    // try {
    //   let url = `${location.protocol}//${location.host}/#/binding-dd`
    //   if (process.env.NODE_ENV === 'production') {
    //     url = `${location.protocol}//${location.host}/admin/#/binding-dd`
    //   }
    //   console.log('applyDingDingLoginQRCodeGoto.url,', url)
    //   await this.$authentication.applyDingDingLoginQRCodeGoto(url)
    // } catch (err) {
    //   console.error(err)
    // }
    // const param = this.$authentication.getDingDingLoginGotoParam()
    // if (!param) {
    //   this.ddQRCodeLoading = false
    //   return
    // }
    // ;(window as any).DDLogin({
    //   id: 'dd_login_container',
    //   goto: param, //请参考注释里的方式
    //   style: 'border:none;background-color:#FFFFFF;margin-top:-30px;',
    //   width: '230',
    //   height: '290'
    // })
    // this.ddQRCodeLoading = false
  }

  async ddLoginWithToken(token: string) {
    try {
      this.loginIng = true
      const result = await this.$authentication.ddLoginWithToken(token)
      if (result.status.code === 200) {
        try {
          await this.$authentication.auth()
          this.$emit('login-result', true)
        } catch (e) {
          this.loginResult = new LoginResult(e.code, e.message)
        } finally {
          this.loginIng = false
        }
      } else {
        // todo
        // this.loginResult = result
      }
      await this.loginSuccess()
    } catch (e) {
      this.loginResult = new LoginResult(e.code, e.message)
    }
  }
}

export default LoginCore
