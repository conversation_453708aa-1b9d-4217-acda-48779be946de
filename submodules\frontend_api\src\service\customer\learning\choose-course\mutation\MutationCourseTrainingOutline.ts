import { ResponseStatus, Response } from '@hbfe/common'
import MutationWaitChooseCourse from '@api/service/customer/learning/choose-course/mutation/MutationWaitChooseCourse'
import WaitChooseCourseTreeCacheVo from '@api/service/customer/learning/choose-course/mutation/vo/WaitChooseCourseTreeCacheVo'
import QueryCourseModule from '@api/service/customer/course/query/QueryCourseModule'
import TrainingCourseOutline from '@api/service/customer/course/query/vo/TrainingCourseOutline'
import CategoryTypeEnum from '@api/service/customer/course/query/enum/CategoryTypeEnum'
import { cloneDeep } from 'lodash'

export class SecondElectiveMaxPeriod {
  /**
   * 学时要求
   */
  electiveMaxPeriod: string
  /**
   * 选修分类二ID
   */
  id: string
  /**
   * 选修分类名称
   */
  name: string
  /**
   * 操作
   */
  operation: number
}
/**
 * @description 课程大纲数数据持久化类
 */
class MutationCourseTrainingOutline {
  /**
   * 【H5】 二级分类全部id
   */
  static SECOND_ALL_ID = 'secondLevelId'
  /**
   * 【H5】 三级分类全部id
   */
  static THIRD_ALL_ID = 'thirdLevelId'

  /**
   * 选修要求
   */
  secondElectiveMaxPeriod: SecondElectiveMaxPeriod[] = []
  /**
   * 获取web测的方案的课程学习大纲（各节点下无课程）
   * @param {string} schemeId - 方案Id
   * @return {ResponseStatus}
   */
  async queryWebCourseTrainingOutline(
    studentNo: string,
    schemeId: string,
    workJobName?: string
  ): Promise<Response<Array<TrainingCourseOutline>>> {
    // 前置条件 - 清空可选课程列表树
    // 从微服务接口请求获取整棵树（是否是叶子节点已知）
    // 默认第一个叶子节点并加载对应待选可选课程列表- LWF
    MutationWaitChooseCourse.selectedPeriod = 0
    await QueryCourseModule.queryCourse.queryCanChooseTrainingSchemeCourseOutlineTree(
      studentNo,
      schemeId,
      CategoryTypeEnum.elective
    )
    this.secondElectiveMaxPeriod = QueryCourseModule.queryCourse.secondElectiveMaxPeriod
    // 将请求回来的大纲赋值到courseTrainingOutline上
    MutationWaitChooseCourse.waitChooseCourseTreeCache = new Array<WaitChooseCourseTreeCacheVo>()

    const tempOutlineTree = cloneDeep(QueryCourseModule.queryCourse.courseOutlineTreeList)

    if (workJobName) {
      // 重建树，添加更多节点
      tempOutlineTree.map((firstNode: TrainingCourseOutline) => {
        firstNode.children.map((secondNode: TrainingCourseOutline) => {
          if (secondNode?.children?.length) {
            const findWorkNode = secondNode.children.find(it => it.name === workJobName)
            let key = ''
            let firstInsert = false
            secondNode.children.map((thirdNode: TrainingCourseOutline) => {
              if (!findWorkNode) {
                if (!firstInsert) {
                  firstInsert = true
                  key += thirdNode.id
                } else {
                  key += '##' + thirdNode.id
                }
              } else {
                if (findWorkNode.id !== thirdNode.id) {
                  if (!firstInsert) {
                    firstInsert = true
                    key += thirdNode.id
                  } else {
                    key += '##' + thirdNode.id
                  }
                }
              }
            })

            const moreNode = new TrainingCourseOutline()
            moreNode.id = key
            moreNode.name = '更多课程'
            moreNode.parentId = secondNode.id
            moreNode.children = []

            const newChildrenList = new Array<TrainingCourseOutline>()
            if (findWorkNode) {
              moreNode.excludeOutlineIds = [findWorkNode.id]
              newChildrenList.push(findWorkNode)
            }
            if (key !== '') {
              newChildrenList.push(moreNode)
            }
            secondNode.children = newChildrenList
          }
        })
      })
    }

    tempOutlineTree?.forEach((outline: TrainingCourseOutline) => {
      MutationWaitChooseCourse.waitChooseCourseTreeCache.push(WaitChooseCourseTreeCacheVo.from(outline))
    })
    this.handleOutlineTreeList(tempOutlineTree)
    return Promise.resolve({
      // todo 待优化
      data: this.handleOutlineTreeList(tempOutlineTree),
      status: new ResponseStatus(200, '')
    })
  }

  /**
   * 获取H5测的方案的课程学习大纲（各节点下无课程）
   * @param {string} schemeId - 方案Id
   * @return {ResponseStatus}
   */
  async queryH5CourseTrainingOutline(
    studentNo: string,
    schemeId: string
  ): Promise<Response<Array<TrainingCourseOutline>>> {
    //  前置条件 - 清空可选课程列表树
    //  从微服务接口请求获取整棵树（是否是叶子节点已知）
    //  遍历整棵树并插入“全部”分类
    //  上述操作后返回整棵课程学习大纲树给UI页面
    //  默认选中“全部”加载对应待选可选课程列表- LWF
    MutationWaitChooseCourse.waitChooseCourseTreeCache = new Array<WaitChooseCourseTreeCacheVo>()
    MutationWaitChooseCourse.selectedPeriod = 0
    await QueryCourseModule.queryCourse.queryCanChooseTrainingSchemeCourseOutlineTree(studentNo, schemeId)
    const outlineTree = QueryCourseModule.queryCourse.courseOutlineTreeList
    // 插入二级“全部”
    if (outlineTree[0]?.parentId === '-1') {
      const trainingCourseOutline = new TrainingCourseOutline()
      trainingCourseOutline.parentId = '-1'
      trainingCourseOutline.id = MutationCourseTrainingOutline.SECOND_ALL_ID
      trainingCourseOutline.name = '全部'
      outlineTree.unshift(trainingCourseOutline)
    }
    if (outlineTree) {
      // 插入三级“全部”
      outlineTree?.forEach(item => {
        // 过滤调二级分类的全部以及没有三级的
        if (item.id !== MutationCourseTrainingOutline.SECOND_ALL_ID && item.children.length) {
          const trainingCourseOutline = new TrainingCourseOutline()
          trainingCourseOutline.parentId = item.parentId
          trainingCourseOutline.id = MutationCourseTrainingOutline.THIRD_ALL_ID
          trainingCourseOutline.name = '全部'
          item?.children.unshift(trainingCourseOutline)
        }
      })
    }
    return Promise.resolve({
      data: outlineTree,
      status: new ResponseStatus(200, '')
    })
  }

  private handleOutlineTreeList(responseList: Array<TrainingCourseOutline>) {
    return responseList[0]?.children.length ? responseList[0].children : responseList[0] ? [responseList[0]] : []
  }
}

export default MutationCourseTrainingOutline
