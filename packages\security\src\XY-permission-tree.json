{"XY": {"name": "学员", "graphql": ["platform-jxjypxtypt-ahzj-school.mutation.enterIndex:{\"authorizationRequired\":false}", "platform-jxjypxtypt-hljysxh-school.mutation.enterIndexResponse:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-ahzjzh-scys-student.mutation.studentLogin:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "ms-account-v1.mutation.loadBasicValidationData:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.bindPhone:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-account-v1.mutation.loginAndBindOpenPlatform:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.loadRetrievePasswordBasicValidationData:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.resetPasswordWithToken:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.studentIdentify:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.collectiveRegistrationAdminIdentify:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.platformManagementAdminIdentify:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.loadBasicValidationDataWithBindPhone:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-account-v1.mutation.sendSmsCodeByRegister:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.sendSmsCodeByUpdatePhone:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.sendSmsCodeByUpdatePhoneWithSmsValidToken:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.sendSmsCodeByUpdatePwd:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.validSmsCode:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.validCaptcha:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.bindPhoneByAdmin:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-account-v1.mutation.changePasswordByCurrent:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getStudentInfoInMyself:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormIdCardTypeList:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.getLastStudyQualificationInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.pageSchemeLearningInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getSchemeLearningInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getSchemeLearningDetailInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.listStudentReportRecordInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getMySchemeConfig:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCourseOfAutonomousCourseLearningSceneInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-studentlearning-v1.mutation.validStudentAllowLearning:{\"serviceName\":\"ms-studentlearning-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getStudentResisterFormConstraintForConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getDockingTycAndQcc:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getForceModifyInitialPassword:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "platform-support-fore-gateway.query.judgeCurrentUserPasswordUpdateForce:{\"authorizationRequired\":true}", "platform-training-channel-v1.mutation.compareTrainingChannelUnitWithStudentUnit:{\"authorizationRequired\":true}", "ms-config-v1.query.getCurrentFrontendConfig:{\"serviceName\":\"ms-config-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pagePlanItemAttendanceInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-studentlearning-v1.mutation.applyStudentLearningTokenInIssue:{\"serviceName\":\"ms-studentlearning-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.statisticsIssueByDateInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-teachingplan-attendance-sds-v1.query.getServerTime:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getStudentPlanSignRecordCountInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-teachingplan-studentattendance-sds-v1.mutation.clockIn:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":true}", "ms-teachingplan-studentattendance-sds-v1.mutation.clockOut:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.statisticsCourseInSchemeInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.getSchemeLearningRule:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-forestage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listBusinessRegionListById:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-forestage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegionInDistribution:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.pageStudentSchemeConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.listSchemeSkuForStudentInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentUser:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.changeNewPhone:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.forgetPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.changePhone:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.loginAndBindOpenPlatform:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.loginAndBindOpenPlatformV2:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applyBindWeChatOpenPlatformLoginAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.changeCollectiveRegisterPhone:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.changeNewPhoneForFxs:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applyLoginByOpenId:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applyBindWeChatOpenPlatformAndValidLogin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwd:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptcha:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountId:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCode:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyChangeIdentityAuthentication:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticate:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyProxyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticateBasic:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.query.getBoundAccountsForReAuthenticate:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.query.getBoundProxyBusinessDomains:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyProxyIdentityUnifiedSubject:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getWechatLoginConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-shortcode-v1.mutation.applyShortLink:{\"serviceName\":\"ms-shortcode-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getTrainingInstitutionPortalInfo:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getFriendLinkListByPortalType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getBannerListByPortalType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-identityAuth-service-v1.mutation.applyDistributorReAuthenticate:{\"authorizationRequired\":false}", "ms-identity-authentication-v1.query.getAccessTokenValue:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptchaForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCodeForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-shortcode-v1.query.getShortCode:{\"serviceName\":\"ms-shortcode-v1\",\"authorizationRequired\":false}", "ms-shortcode-v1.query.getUrlByShortCode:{\"serviceName\":\"ms-shortcode-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOfflineCollectiveRegisterConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOnlineCollectiveRegisterConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.listTeacherInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applyCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validSmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validIdentity:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validIsBindWeChatOpenPlatform:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applySecureCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applyOpenIdByServicerId:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validIdentityForFxs:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCodeForLoginDistributor:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCodeForLoginDistributorNeedLogin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applyScanCodeOpenIdByServicerId:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "platform-student-v1.query.StudentInfoQuery:{\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.query.getDistributionService:{\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getCurrentServicerInfo:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.ifOnlyPorttalInDustributor:{\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.getCurrentPortalIdInDustributor:{\"authorizationRequired\":false}", "platform-jxjy-distributor-admin-v1.query.whetherBelongDistributor:{\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForServiceByDomainName:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.query.getOnlineSchoolServicerProviderByHeader:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "platform-training-channel-fore-gateway.query.getCurrentTrainingChannelInfo:{\"authorizationRequired\":false}", "platform-jxjy-distributor-admin-v1.query.findInfoByDomainName:{\"authorizationRequired\":false}", "platform-information-gateway.query.listInformationByFuzzyInPublic:{\"authorizationRequired\":false}", "platform-jxjy-obsfile-v1.query.generateUploadToken:{\"authorizationRequired\":false}", "ms-examquestion-v1.mutation.createTagQuestion:{\"serviceName\":\"ms-examquestion-v1\",\"authorizationRequired\":true}", "ms-examquestion-v1.mutation.updateTagQuestion:{\"serviceName\":\"ms-examquestion-v1\",\"authorizationRequired\":true}", "ms-examquestion-v1.mutation.removeTagQuestion:{\"serviceName\":\"ms-examquestion-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-QuestionnaireQueryBackStage.query.pageQuestionnaireAnswerContentInServicer:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "diff-data-export-gateway-backstage.query.exportAnswerAskQuestionExcelInServicer:{\"authorizationRequired\":true}", "platform-information-gateway.query.getCurrentTime:{\"authorizationRequired\":false}", "ms-autonomouscourselearningscene-v1.mutation.applyCourseLearning:{\"serviceName\":\"ms-autonomouscourselearningscene-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.applyCourseLearning:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.applyChooseCourse:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}", "ms-interestcourselearningscene-v1.mutation.applyCourseLearning:{\"serviceName\":\"ms-interestcourselearningscene-v1\",\"authorizationRequired\":true}", "ms-studentlearning-v1.mutation.applyStudentLearningToken:{\"serviceName\":\"ms-studentlearning-v1\",\"authorizationRequired\":true}", "ms-studentlearning-v1.mutation.applyStudentLearningTokenInterruptAutoStudy:{\"serviceName\":\"ms-studentlearning-v1\",\"authorizationRequired\":true}", "platform-short-url-v1.mutation.getShortUrl:{\"authorizationRequired\":false}", "ms-order-v1.mutation.createOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.onlinePayOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.applyCancelOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-QuestionnaireQueryForeStage.query.getQuestionnaireAnswerStaitsticsInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "diff-exam-query-front-gateway-QuestionnaireQueryForestage.query.getCourseTeachersWithPeriodAndOnlineInMyself:{\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryForeStage.query.pageQuestionInMySelf:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-QuestionnaireQueryForeStage.query.pageQuestionnaireIssueInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-QuestionnaireQueryForeStage.query.pageQuestionnaireSchemeInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-studentlearning-v1.mutation.applyQuestionnaire:{\"serviceName\":\"ms-studentlearning-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-QuestionnaireQueryForeStage.query.getQuestionnaireDetailInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-answer-v1.mutation.handedQuestionnaireAnswerPaper:{\"serviceName\":\"ms-exam-answer-v1\",\"authorizationRequired\":true}", "ms-questionnaire-v1.mutation.applyQuestionnaireVerify:{\"serviceName\":\"ms-questionnaire-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-QuestionnaireQueryForeStage.query.getQuestionnaireTemplateDetailInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getRecentTrainingQualificationInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageTrainingPointInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-teachingplan-attendance-sds-v1.query.getServerTimeWithStudentNo:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":true}", "ms-teachingplan-report-sds-v1.mutation.studentReportCheckInWithStudentNo:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.listSkuPropertyCustomerPurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.listBatchRegistrationSources:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-training-channel-fore-gateway.query.getPageTrainingChannelInfo:{\"authorizationRequired\":false}", "platform-training-channel-v1.mutation.getTrainingChannelSetting:{\"authorizationRequired\":false}", "platform-training-channel-back-gateway.query.getOnlineCollectiveByTrainingChannelIdInSubject:{\"authorizationRequired\":false}", "platform-training-channel-back-gateway.query.getOfflineCollectiveByTrainingChannelIdInSubject:{\"authorizationRequired\":false}", "platform-training-channel-fore-gateway.query.getTrainingChannelDetailById:{\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-certificate-v1.mutation.batchPrintCertificates:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.screeningBatchPrintCertificates:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.printCertificate:{\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageIssueCommoditySkuCollectivePurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-certificate-v1.query.queryExportTaskResponsePage:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "fxnl-data-export-gateway-forestage.query.pageExportTaskInfoInMyself:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.collectivelyAdministratorExportCertificateFailedData:{\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageCommoditySkuTrainingChannelCollectiveInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.listSkuPropertyTrainingChannelCollectiveInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageCommoditySkuCustomerCollectivePurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.listSkuPropertyCollectivePurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.pageStudentSchemeLearningInCollective:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.listSchemeSkuBatchedInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-data-export-forestage-v1.query.exportStudentSchemeLearningExcelInCollective:{\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.pageStatisticsStudentSchemeLearningInCollective:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-account-v1.mutation.updateUser:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-dictionary-v1.mutation.updateShowName:{\"serviceName\":\"ms-dictionary-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.pageBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageCoursewareSupplierGRPCInSubject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.pageJobCategoryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getJobCategoryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "platform-jxjy-distributor-admin-v1.query.isOnlineSchoolContractExpired:{\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.isOnlineSchoolContractExpired:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-shortmessage-v1.query.verifyAuthConfigEnable:{\"serviceName\":\"ms-shortmessage-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getExcellentCourses:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listCourseCategoryInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-offlineinvoice-v1.query.queryShippingMethodsForSchool:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.batchApplyInvoiceDirectly:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.batchInvoiceChange:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-order-v1.mutation.batchApplyInvoiceValidate:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-studentcourselearning-v1.mutation.applyCourseAppraisal:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}", "ms-studentcourse-appraisal-v1.mutation.appraisalCourse:{\"serviceName\":\"ms-studentcourse-appraisal-v1\",\"authorizationRequired\":true}", "ms-studentcourse-appraisal-v1.mutation.deleteAppraisalCourse:{\"serviceName\":\"ms-studentcourse-appraisal-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.getCourseAppraiseInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.getCourseAppraiseStatisticsInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-data-export-gateway-backstage.query.pageExportTaskInfoInMyself:{\"authorizationRequired\":true}", "jxjy-collectivesign-v1.query.queryForTask:{\"authorizationRequired\":true}", "jxjy-collectivesign-v1.query.exportFail:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.listCanChooseCourseTrainingOutlineOfChooseCourseLearningSceneInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCanChooseCourseOfChooseCourseLearningSceneInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCourseInSchemeInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageStudentCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageStudentCourseOfChooseCourseLearningSceneV2InMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.getCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.getLastStudentCourseLearningInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.statisticsCourseLearningSceneChooseCourseByOutlineOfInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageStudentCourseOfChooseCourseLearningSceneInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageStudentCourseOfInterestCourseLearningSceneInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.query.queryUnCompleteLearningExperienceTopic:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageLearningExperienceParticipatedInStudent:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageLearningExperienceNotParticipatedInStudent:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageLearningExperienceInStudent:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-studentcourselearning-v1.mutation.applyCourseQuizLearning:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}", "ms-studentlearning-v1.mutation.applyExam:{\"serviceName\":\"ms-studentlearning-v1\",\"authorizationRequired\":true}", "ms-exam-answer-v1.mutation.applyAnswer:{\"serviceName\":\"ms-exam-answer-v1\",\"authorizationRequired\":true}", "ms-exam-answer-v1.mutation.getRemainingAnswerTime:{\"serviceName\":\"ms-exam-answer-v1\",\"authorizationRequired\":true}", "ms-practice-v1.mutation.applyPractice:{\"serviceName\":\"ms-practice-v1\",\"authorizationRequired\":true}", "ms-exam-answer-v1.query.getQuestionToAnswerByQuestionIds:{\"serviceName\":\"ms-exam-answer-v1\",\"authorizationRequired\":true}", "ms-examextraction-v1.mutation.previewAnswerPaper:{\"serviceName\":\"ms-examextraction-v1\",\"authorizationRequired\":false}", "ms-exam-answer-v1.mutation.preSubmitAnswer:{\"serviceName\":\"ms-exam-answer-v1\",\"authorizationRequired\":true}", "ms-exam-answer-v1.mutation.applyHanding:{\"serviceName\":\"ms-exam-answer-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryForeStage.query.pageMyExaminationRecordInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryForeStage.query.getAnswerPaperRecordInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryForeStage.query.pageMyPracticeRecordInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryForeStage.query.pageMyCourseQuizRecordInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-media-resource-learning-v1.mutation.prepareCourseLearningTimingBeToken:{\"serviceName\":\"ms-media-resource-learning-v1\",\"authorizationRequired\":true}", "ms-media-resource-learning-v1.mutation.keepAliveHeartbeat:{\"serviceName\":\"ms-media-resource-learning-v1\",\"authorizationRequired\":true}", "ms-course-play-resource-v1.mutation.applyCoursePlayResource:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCoursewareMediaPlayAntiTheftChainResource:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-media-resource-learning-v1.mutation.applyCoursewareLearningTokenByToken:{\"serviceName\":\"ms-media-resource-learning-v1\",\"authorizationRequired\":true}", "ms-media-resource-learning-v1.mutation.applyMediaLearningTimingWithAnti:{\"serviceName\":\"ms-media-resource-learning-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.getCourseLearnStatistics:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCoursewareMediaPreviewAntiTheftChainResource:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCoursewareMediaPreviewResource:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-media-resource-learning-v1.mutation.commitMediaLearningTimingWithAnti:{\"serviceName\":\"ms-media-resource-learning-v1\",\"authorizationRequired\":true}", "ms-course-play-resource-v1.mutation.applyCourseAudition:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-news-v1.mutation.browseNews:{\"serviceName\":\"ms-news-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOnlineSchoolProtolConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.listCourseCategoryInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "platform-training-channel-back-gateway.query.pageTrainingChannelSelectCourseInSubject:{\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOnlineSchoolConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "platform-certificate-v1.mutation.studentPrintCertificate:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.dataMigrationPrintCertificate:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.dataMigrationCertificatePrintWithOutLogin:{\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getCertificateTemplate:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-student-scheme-learning-query-front-gateway.query.pageHistoryStudentTrainingInfoResponseInMyself:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.getCertificateSnapshot:{\"authorizationRequired\":false}", "platform-student-scheme-learning-query-front-gateway.query.getHistoryStudentTrainingInfoResponseByIdInMyself:{\"authorizationRequired\":false}", "ms-certificate-v1.mutation.scanQrCodeGenerateCertificate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":false}", "ms-certificate-v1.mutation.getCertificateSnapShot:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.getCommoditySkuCustomerPurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-commodity-v1.query.validateCommodity:{\"serviceName\":\"ms-commodity-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.reservingSchemeValidate:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageOrderInMyself:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageReturnOrderInMyself:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.relearnForStudent:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.pageSchemeIssueConfigListOptionalLoginInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageIssueCommoditySkuCustomerPurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageIssueCommoditySkuTrainingChannelInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getSchemeLearningBySubOrderInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-examination-v1.query.getServiceTime:{\"serviceName\":\"ms-examination-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageCommoditySkuTrainingChannelCustomerInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.listSkuPropertyTrainingChannelCustomerInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pagePortalCommoditySkuCustomerPurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.listPortalCommoditySkuPropertyCustomerPurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.getCommoditySkuTrainingChannelInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.getPortalCommoditySkuCustomerPurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-forestage.query.getDistributorCommodityInDistributor:{\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.changePasswordByForceModifyInitPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-account-v1.mutation.immediateResetPassword:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.registerCollectiveRegisterAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.registerStudent:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.registerStudent:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.registerStudentV2:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.createOnlineSchoolStudent:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.changePasswordByCurrent:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.changePasswordByCurrentWithResponse:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.updateCollectiveRegister:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.updateStudent:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.query.findUserIdentificationPhotoByUserId:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.mutation.uploadPhoto:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.query.determineCertificateTemplateIsNeedPhoto:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseInSchemeInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.getLearningExperienceContentInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listLearningExperienceTopic:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.mutation.saveLearningExperienceTopicContent:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.mutation.submitLearningExperience:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.mutation.cancelStudentLearningExperience:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.mutation.applyLearningExperience:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.mutation.saveLearningExperience:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.mutation.updateLearningExperienceTopicContent:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.mutation.check:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.mutation.verifyExists:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfig:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfigByServicerId:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfigExist:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryStudentNormalAutoLearningTaskResult:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-choose-course-v1.mutation.chooseCourse:{\"serviceName\":\"ms-choose-course-v1\",\"authorizationRequired\":true}", "ms-choose-course-v1.mutation.prepareChooseCourse:{\"serviceName\":\"ms-choose-course-v1\",\"authorizationRequired\":true}", "ms-studentcourselearning-v1.mutation.applyCourseLearningPlay:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}", "ms-course-play-resource-v1.mutation.applyCoursePreview:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCourseAuditionWithoutValidate:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listReviewTopNews:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listRootNewsCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listTrainingChannelReviewTopNews:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getCommonNewsDetailWithPreviousAndNext:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getTrainingChannelCommonNewsDetailWithPreviousAndNext:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listPopUpsNews:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listChildNewsCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listTrainingChannelPopUpsNews:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listTopNewsCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.pageSimpleNewsByPublishAndAreaCodePath:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.pageCommonSimpleNewsByPublish:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.pageTrainingChannelCommonSimpleNewsByPublish:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getMenusByPortalType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageExchangeOrderInMySelf:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-payment-v1.query.prepareRepay:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.getOrderInMyself:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.preparePay:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-payment-v1.mutation.timeoutPayFlow:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-payment-v1.mutation.getPreParePayResult:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.mutation.reservingSchemeIssueValidate:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-hljysxh-school.query.getStudentPayStatus:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-student-learning.mutation.applyStudentLearningToken:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-student-learning.mutation.applyStudentLearningToken:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-ahzj-school.mutation.getTrainSupervisionForAHZJ:{\"authorizationRequired\":true}", "platform-jxjypxtypt-ahzj-school.mutation.getVerificationCodeForAHZJ:{\"authorizationRequired\":true}", "platform-jxjypxtypt-ahzj-school.mutation.sendCodeAnswerForAHZJ:{\"authorizationRequired\":true}", "platform-jxjy-marketing-order-v1.mutation.createMarketingOrder:{\"authorizationRequired\":true}", "ms-order-marketing-v1.mutation.calculateOrderPrice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "platform-jxjy-marketing-order-v1.mutation.verifyMarketingOrder:{\"authorizationRequired\":true}", "ms-order-marketing-v1.mutation.applyEnterSaleChannel:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "platform-jxjy-marketing-order-v1.mutation.applyEnterSaleChannelByPortalId:{\"authorizationRequired\":true}", "platform-jxjypxtypt-ahzj-school.mutation.createOrder:{\"authorizationRequired\":true}", "platform-jxjypxtypt-ahzj-school.mutation.validAllowToCreateOrder:{\"authorizationRequired\":true}", "platform-jxjypxtypt-ahzj-school.mutation.validAllowToLearning:{\"authorizationRequired\":true}", "platform-jxjypxtypt-ahzj-school.query.listSignUpNumberInfo:{\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-school.mutation.createHYWOrder:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-order-v1.mutation.preparePlaceOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-school.query.validStudentInfo:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-school.query.encryptedContent:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-school.mutation.syncStudentSignUpForApp:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-school.mutation.syncStudentSignUpUnregistered:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-school.mutation.createStudentAndPlaceOrder:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-fjzj-student-learning.mutation.validAllowToLearning:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-school.query.pageSchemeLearningInMyself:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageCommoditySkuTrainingChannelInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.listSkuPropertyTrainingChannelInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getAreaTrainingChannelStudentInfoInMyself:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-hljysxh-school.mutation.createOrder:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-user-auth.query.checkUserAuthInfo:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-school.mutation.invalidStudentCourses:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-course-learning-gateway-forestage.query.stopCourseLearning:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-school.mutation.verifyPermissionsToCourse:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-school.mutation.verifyStudyStatus:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-course-learning-gateway-forestage.query.getJXGXStudentScheme:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-school.mutation.verifyPersonZcInfo:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-school.mutation.getStudyCourseNum:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-school.mutation.enterIndex:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-qztg-school.query.encryptedContent:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-qztg-school.mutation.syncStudentSignUpForApp:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-qztg-school.mutation.syncStudentSignUpUnregistered:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-qztg-school.mutation.createStudentAndPlaceOrder:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "qztg-trade-query-front-gateway-forestage.query.getCommoditySkuTrainingChannelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "qztg-trade-query-front-gateway-forestage.query.getPortalCommoditySkuCustomerPurchaseInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-learningscheme-v1.mutation.studentLearningResourcePushIsCompleted:{\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-school.mutation.createHYWOrder:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-school.query.validStudentInfo:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-school.query.encryptedContent:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-school.mutation.syncStudentSignUpForApp:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-school.mutation.syncStudentSignUpUnregistered:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-school.mutation.createStudentAndPlaceOrder:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-xmlg-student-learning.mutation.validAllowToLearning:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-school.query.pageSchemeLearningInMyself:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-school.mutation.createHYWOrder:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-school.query.validStudentInfo:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-school.query.encryptedContent:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-school.mutation.syncStudentSignUpForApp:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-school.mutation.syncStudentSignUpUnregistered:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-school.mutation.createStudentAndPlaceOrder:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-zztt-student-learning.mutation.validAllowToLearning:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-student-learning.mutation.applyStudentLearningToken:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-school.query.pageSchemeLearningInMyself:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-student-learning-backstage.query.rePushStudentTrainingResultToGatewayInServicerV2:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInTrainingChannel:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInTrainingChannelV2:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicerV2:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-byzj-school.query.exportStudentCourseLearningQuotationInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-byzj-school.query.pageCourseSubjectInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-byzj-school.query.importCourseSubjectByExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-byzj-school.query.deleteCourseSubjectByIdInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fjzj-trade-query-front-gateway-TradeQueryBackstage.query.pageCombinationOpenReportFormsInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-trade-query-front-gateway-TradeQueryBackstage.query.getCombinationCommodityReportSummaryInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportCombinationCommodityOpenReportFormsInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeLearningReportSummeryInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningTotalStatisticsInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportSchemeLearningReportFormsDetailExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedSchemeLearningReportFormsDetailExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceSchemeLearningReportFormsDetailExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportSchemeLearningReportFormsExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedSchemeLearningReportFormsExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceSchemeLearningReportFormsExcelInServicer:{\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.listSchemeLearningReportFormsInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningStatisticsInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-teachingplan-v1.query.hasSignRecord:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":true}", "ms-teachingplan-v1.mutation.checkPlanItem:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":true}", "platform-jxjy-questionnaire-v1.mutation.questionnaireDeleteVerify:{\"authorizationRequired\":true}", "ms-learningscheme-enrollment-v1.mutation.validateIssueSignUpData:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-learningscheme-enrollment-v1.mutation.validateIssueSignUpNum:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "sds-platform-jxjy-learningscheme-v1.mutation.judgeIssueNumExist:{\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.specialUpdateLearningScheme:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "platform-learningscheme-v1.mutation.asyncUpdateLearningScheme:{\"authorizationRequired\":true}", "platform-learningscheme-v1.mutation.asyncCreateLearningScheme:{\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.getPaperPublishConfigureInServicer:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageLibraryInServicer:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningExperienceParticipatedInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageStudentCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareLearningRecordInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryNewestTaskResultByQualificationIds:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInformationInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fjzj-data-export-gateway-backstage.query.exportCommoditySkuInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gstyb-school.query.exportStudentCourseLearningQuotationInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-gstyb-school.query.importCourseSubjectByExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gstyb-school.query.pageCourseSubjectInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gstyb-school.query.deleteCourseSubjectByIdInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gszj-school.query.exportStudentCourseLearningQuotationInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-gszj-school.query.importCourseSubjectByExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gszj-school.query.pageCourseSubjectInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gszj-school.query.deleteCourseSubjectByIdInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.batchImportStudentList:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.checkImportPrintListTheUploadProgress:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.findUploadSuccessFailureQuantity:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.returnImportedData:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.printTheListRemoveAllStudent:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.exportFailedData:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.learnerImportBatchPrintCertificates:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInTrainingChannel:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInTrainingChannel:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInTrainingChannel:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInDistributor:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInDistributor:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInDistributor:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInServicer:{\"authorizationRequired\":false}", "jxgx-data-export-gateway-backstage.query.exportStudentSchemeLearningExcelInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicerManageRegion:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInServicerManageRegion:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportReturnReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportReturnReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportReturnOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportReturnOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-trade-query-front-gateway-backstage.query.getCommoditySkuInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-trade-query-front-gateway-backstage.query.isReferenced:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.query.isProcessedByTransaction:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportCommoditySkuInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-trade-query-front-gateway-TradeQueryBackstage.query.pageCombinationOpenReportFormsInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-trade-query-front-gateway-TradeQueryBackstage.query.getCombinationCommodityReportSummaryInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportCombinationCommodityOpenReportFormsInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportCommoditySkuInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "yzzj-data-export-gateway-backstage.query.exportStudentSchemeLearningIntegrationDataExcelInServicerManageRegion:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "yzzj-data-export-gateway-backstage.query.exportStudentSchemeLearningIntegrationDataExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "zjzj-data-export-gateway-backstage.query.exportStudentSchemeLearningIntegrationDataExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "zztt-trade-query-front-gateway-TradeQueryBackstage.query.pageCombinationOpenReportFormsInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-trade-query-front-gateway-TradeQueryBackstage.query.getCombinationCommodityReportSummaryInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportCombinationCommodityOpenReportFormsInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportCommoditySkuInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-order-v1.mutation.batchApplyInvoice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findCollectiveSignupMetaSchema:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findCommitCompleteAndSuccessSubTuskSuccessDataByPage:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findCommitCompleteAndFailSubTuskFailDataByPage:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.collectiveSignupDataProcess:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.findBatchOrderBatchPayStatus:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findCountGroupByKey:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.signupByOriginalCollectiveSignup:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.cancelCollectiveSignup:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.deleteCollectiveSignup:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.prepareBatchOrderPay:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-payment-v1.query.prepareBatchOrderRepay:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.onlinePayBatchOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.offlinePayBatchOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.updateBatchOfflinePaymentVouchers:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.onlinePayBatchOrderAndApplyInvoice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.offlinePayBatchOrderAndApplyInvoice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.importCollectiveSignup:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.importCollectiveSignupForVerify:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.collectiveSignupDataAnalysis:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findLastRecordByBatch:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.clearFailureData:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.exportCollectiveSignupImportFailExcel:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "jxjy-collectivesign-v1.query.asyncExportCollectiveSignupExcuteFailExcel:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-forestage.query.exportBatchOrderDetailInMyself:{\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findImportCollectiveSignupCompleteSuccessDataByPage:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findImportCollectiveSignupFailDataByPage:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.updateSignupData:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.deleteSignupData:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.commitCollectiveSignup:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.getBatchOrderInMySelf:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageBatchReturnOrderInMySelf:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.statisticOrderInMyself:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.statisticReturnOrderInMyself:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageBatchOrderInMySelf:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-choose-course-v1.mutation.smartChooseCourse:{\"serviceName\":\"ms-choose-course-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.listCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.applyInvoiceValidate:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.prepareApplyInvoice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.applyInvoice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zzkd-school.query.pageStudentSchemeLearningInCollectiveV2:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zzkd-data-export-gateway-forestage.query.exportStudentSchemeLearningExcelInCollective:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-fjzj-trade.query.queryHymSchemeRefundInfo:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-certificate.mutation.studentPrintCertificate:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-trade.query.queryHymSchemeRefundInfo:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-trade.query.queryHymSchemeRefundInfo:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-training-channel-v1.query.pageUpdateSchemePortalResultTaskInfo:{\"authorizationRequired\":true}", "platform-training-channel-v1.query.exportFailUpdateSchemeShowResult:{\"authorizationRequired\":true}", "platform-training-channel-v1.query.exportAllUpdateSchemeShowResult:{\"authorizationRequired\":true}", "platform-jxjy-learning-result-v1.mutation.exportTaskExcelFailedResultWithName:{\"authorizationRequired\":true}", "platform-jxjy-learning-result-v1.mutation.exportTaskExcelAllResultWithName:{\"authorizationRequired\":true}", "platform-jxjy-learning-result-v1.query.pageImportGradeResultTaskInfo:{\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.batchUpdateLearningSchemeExportFail:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.batchUpdateLearningSchemeExport:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.query.pageImportLearningSchemeImportTask:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.findImportPrintFailData:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.findImportPrintData:{\"authorizationRequired\":true}", "ms-certificate-v1.query.findImportPrintTaskExecuteResponsePage:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.getImportFailedData:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.getAllImportData:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportBatchPaySpecialInvoiceDelivery:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportBatchPaySpecialPaperOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportBatchPayOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-course-resource-v1.mutation.exportCoursewareImportResult:{\"serviceName\":\"ms-course-resource-v1\",\"authorizationRequired\":true}", "ms-course-resource-v1.query.pageCoursewareImportTask:{\"serviceName\":\"ms-course-resource-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.commonQueryOfflineInvoiceImportResult:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportOfflineSpecialElectronicInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportSpecialInvoiceDeliveryWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportSpecialPaperOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-course-resource-v1.query.exportErrorCoursePackageImportResult:{\"serviceName\":\"ms-course-resource-v1\",\"authorizationRequired\":true}", "ms-course-resource-v1.query.exportAllCoursePackageImportResult:{\"serviceName\":\"ms-course-resource-v1\",\"authorizationRequired\":true}", "ms-course-resource-v1.query.pageCoursePackageImportTask:{\"serviceName\":\"ms-course-resource-v1\",\"authorizationRequired\":true}", "ms-importopen-v1.query.exportExcel:{\"serviceName\":\"ms-importopen-v1\",\"authorizationRequired\":true}", "ms-importopen-v1.query.exportExcelAllData:{\"serviceName\":\"ms-importopen-v1\",\"authorizationRequired\":true}", "ms-importopen-v1.query.findTaskExecuteWithSelfResponseByPage:{\"serviceName\":\"ms-importopen-v1\",\"authorizationRequired\":true}", "ms-importopen-v1.query.findTaskExecuteWithServicerResponseByPage:{\"serviceName\":\"ms-importopen-v1\",\"authorizationRequired\":true}", "platform-account-v1.query.exportErrorStudentResult:{\"authorizationRequired\":true}", "platform-account-v1.query.exportAllStudentResult:{\"authorizationRequired\":true}", "ms-account-v1.query.pageStudentImportTask:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-examquestion-v1.mutation.batchExportFailChooseQuestionData:{\"serviceName\":\"ms-examquestion-v1\",\"authorizationRequired\":true}", "ms-examquestion-v1.mutation.batchExportAllChooseQuestionData:{\"serviceName\":\"ms-examquestion-v1\",\"authorizationRequired\":true}", "ms-examquestion-v1.query.findBatchImportQuestionByPage:{\"serviceName\":\"ms-examquestion-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-byzj-school.query.batchExportFailChooseDataInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-byzj-school.query.batchExportAllChooseDataInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-byzj-school.query.findBatchImportByPageInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInServicerManageRegion:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInServicerManageRegion:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInServicerManageRegion:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageCommoditySkuDistributorOpenReportInSupplier:{\"authorizationRequired\":true}", "fxnl-data-export-gateway-backstage.query.exportCommodityOpenStatisticsDetailExcelInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.statisticTradeRecordInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageCommoditySkuDistributorOpenReportInDistributor:{\"authorizationRequired\":true}", "fxnl-data-export-gateway-backstage.query.exportCommodityDistributorOpenReportInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.statisticTradeRecordInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.statisticTradeSummaryInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageDistributorSellStatisticInSupplier:{\"authorizationRequired\":true}", "fxnl-data-export-gateway-backstage.query.exportDistributorSalesStatisticsExcelInSupplier:{\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-school.query.pageStudentSchemeLearningInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-school.query.pageStudentSchemeLearningInServicerV2:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOnlineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getInvoiceAutoBillPolicyInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fjzj-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fjzj-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportReturnReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportReturnReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportReturnOrderExcelInTrainingChannel:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportReturnOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportReturnOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-data-export-gateway-backstage.query.exportStudentExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gstyb-school.query.batchExportFailChooseDataInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gstyb-school.query.batchExportAllChooseDataInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gstyb-school.query.findBatchImportByPageInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gszj-school.query.batchExportFailChooseDataInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gszj-school.query.batchExportAllChooseDataInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-gszj-school.query.findBatchImportByPageInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.deleteCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.addStudentCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "platform-training-channel-user-v1.mutation.updateStudentByAdmin:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listAreaTrainingChannelStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-certificate.mutation.singlePrintCertificate:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.chooseTemplatePrintCertificate:{\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-certificate.mutation.batchPrintCertificate:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-certificate-v1.mutation.printCertificateTemplate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportStudentExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportStudentLearningDetailInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportReturnReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportReturnReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportReturnOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportReturnOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportCentralFinancialDataInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-qztg-school.query.updateMergeCommodityRelation:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "qztg-data-export-gateway-backstage.query.exportStudentExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-school.query.pageStudentSchemeLearningInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-school.query.pageStudentSchemeLearningInServicerV2:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportReturnReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportReturnReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportReturnOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportReturnOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-data-export-gateway-backstage.query.exportStudentExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zzkd-school.query.exportStudentSchemeLearningExcelInTrainingChannelV2:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-zzkd-school.query.pageStudentSchemeLearningInTrainingChannelV2:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zzkd-school.query.exportStudentSchemeLearningExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-zzkd-school.query.pageStudentSchemeLearningInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zzkd-school.query.exportStudentSchemeLearningExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-zzkd-school.query.pageStudentSchemeLearningInServicerV2:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zzkd-school.query.exportStudentSchemeLearningExcelInServicerManageRegion:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}", "platform-jxjypxtypt-zzkd-school.query.pageStudentSchemeLearningInServicerManageRegion:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-school.query.pageStudentSchemeLearningInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-school.query.pageStudentSchemeLearningInServicerV2:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportReturnReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportReturnReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportReturnOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportReturnOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-data-export-gateway-backstage.query.exportStudentExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-trade-query-front-gateway-TradeQueryForestage.query.pageReturnOrderInMyself:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-training-channel-user-v1.mutation.registerStudent:{\"authorizationRequired\":false}", "platform-training-channel-user-v1.mutation.registerStudentV2:{\"authorizationRequired\":false}", "platform-training-channel-user-v1.mutation.updateStudent:{\"authorizationRequired\":true}", "xmlg-trade-query-front-gateway-TradeQueryForestage.query.pageReturnOrderInMyself:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-trade-query-front-gateway-TradeQueryForestage.query.pageReturnOrderInMyself:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-trade.mutation.applyOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fjzj-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInTrainingChannel:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-trade.mutation.applyOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "xmlg-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInTrainingChannel:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-trade.mutation.applyOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "zztt-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInTrainingChannel:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.getQuestionCountInServicer:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.confirmBatchRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.agreeReturnBatchApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.rejectReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.retryRecycleResource:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.retryRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.confirmRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.sellerCancelReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.agreeReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-trade.mutation.agreeOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-trade.mutation.cancelOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-fjzj-trade.mutation.refuseOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-trade.mutation.agreeOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-trade.mutation.cancelOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-xmlg-trade.mutation.refuseOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-trade.mutation.agreeOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-trade.mutation.cancelOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-jxjypxtypt-zztt-trade.mutation.refuseOrderReturn:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-qztg-school.query.createOrder:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getAdminInfoInMyself:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-account-v1.mutation.changeAuthorizationUnitInfoList:{\"authorizationRequired\":true}", "ms-inspection-v1.mutation.getAntiInspectionInfo:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-inspection-v1.mutation.refreshAntiInspection:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-inspection-v1.mutation.newExecuteAntiInspection:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.applyUserDatumCode:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.query.getUserDatumCodeInfo:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-inspection-v1.mutation.detectPhotoFace:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-inspection-v1.mutation.tryComparePhoto:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.query.findAntiResultByToken:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-inspection-v1.mutation.executeAntiInspection:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiBasicConfigDetailInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Forestage.query.getAntiBasicConfigDetailInScheme:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.createAntiConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":false}", "ms-anticheat-v1.mutation.createAntiConfigWithCode:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.updateAntiConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.updateAntiConfigWithCode:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.pageAntiRulesInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.disableAntiConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.enableAntiConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.removeAntiConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiBasicConfigInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiModuleConfigurationInfoInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiBasicConfigInDistributor:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Forestage.query.getAntiBasicConfigByServicerId:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.createOnlineSchoolAntiModuleConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.createDatumConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.enableAntiModuleConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.disableAntiModuleConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.updateDatumConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.batchEnableDisableAntiConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.disableDatumConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.enableDatumConfig:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Forestage.query.getUserReferencePhotoResponseInMyself:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.createUserDatum:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.updateUserDatum:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.doDeleteUserDatum:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.changeAllowUpdateCount:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.pageLearningSupervisionBehaviorInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.pageSupervisionCourseDetailsInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.listLearningSupervisionBehaviorInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-secure-config-v1.query.getSmsCodeAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getFailedAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getSSOSecureConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-secure-config-v1.mutation.saveSSOSecureConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-news-v1.mutation.unPublishNews:{\"serviceName\":\"ms-news-v1\",\"authorizationRequired\":true}", "ms-news-v1.mutation.publishNews:{\"serviceName\":\"ms-news-v1\",\"authorizationRequired\":true}", "ms-news-v1.mutation.createNews:{\"serviceName\":\"ms-news-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getNewsDetail:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-news-v1.mutation.deleteNews:{\"serviceName\":\"ms-news-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.pageSimpleNewsByPublishAndAreaCodePathInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.pageCommonSimpleNewsByPublishInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getRootNewsCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageSimpleNews:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-news-v1.mutation.updateNews:{\"serviceName\":\"ms-news-v1\",\"authorizationRequired\":true}", "ms-payment-v1.query.queryPayFlowStatus:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-payment-v1.mutation.getBatchPreParePayResult:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryQrScanPromptByPayFlowNo:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.deliveredOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":true}", "ms-assess-v1.mutation.batchRePushSchemeIndicator:{\"serviceName\":\"ms-assess-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.createJxjyCommonMenu:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.temporaryOrderUpdateOrderInfoInSubject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.enableOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForService:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-anticheat-v1.mutation.updateUserDatumCodePhoto:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.refreshConfigAndUpdate:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.executeAntiInspection:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":false}", "platform-account-v1.mutation.compensatingStudentAccount:{\"serviceName\":\"platform-account-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.mutation.openDistributionService:{\"authorizationRequired\":false}", "ms-order-repair-data-v1.mutation.orderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-repair-data-v1.mutation.batchOrderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.distributionAuthBatchProduct:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.createPricePlan:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.pricePlanSetSaleChannel:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissReturnOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissExchangeOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.clearStudentLearningRule:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-teachingplan-studentattendance-sds-v1.mutation.oneClickClock:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":false}", "platform-information-gateway.query.listInformationByFuzzyInDistributor:{\"authorizationRequired\":false}", "fxnl-query-front-gateway-forestage.query.pageDistributorCommodityInDistributor:{\"authorizationRequired\":false}", "fxnl-query-front-gateway-forestage.query.checkDistributorCommodityRelationValid:{\"authorizationRequired\":false}", "fxnl-query-front-gateway-forestage.query.getDistributorCommoditySkuPropertyCollectionInDistributor:{\"authorizationRequired\":false}", "fxnl-query-front-gateway-forestage.query.pagePortalDistributorIssueCommodityInDistributor:{\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-forestage.query.getTrainingInstitutionPortalInfoByPortalId:{\"authorizationRequired\":false}", "ms-distribution-v1.mutation.verifySupplyDistributionContractRelation:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":false}", "platform-jxjy-distributor-admin-v1.query.findPublishedInfoByIdentifier:{\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.getOfflineRegistrationConfigByPortalId:{\"authorizationRequired\":false}", "platform-jxjy-distributor-admin-v1.query.validDistributorPortal:{\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.pageCommonSimpleNewsByPublishByPortalId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listNewsCategoryTree:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getCommonNewsDetailWithPreviousAndNextInDistributor:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listPopUpsNewsByPortalId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"]}}