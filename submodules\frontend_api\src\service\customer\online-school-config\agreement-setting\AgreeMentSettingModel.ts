import AgreementSettingVo from './model/AgreementSettingVo'
import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'
/**
 * 学员-协议详情
 */
export default class AgreeMentSettingModel {
  /**
   * 协议详情
   */
  AgreementDetail: AgreementSettingVo = new AgreementSettingVo()

  /**
   * 查询协议配置
   */
  async doQuery() {
    const res = await ServicerSeriesV1Gateway.getOnlineSchoolProtolConfig()
    if (res.status.isSuccess() && res.data) {
      const detail = AgreementSettingVo.from(res.data)
      return detail
    } else {
      return new AgreementSettingVo()
    }
  }
}
