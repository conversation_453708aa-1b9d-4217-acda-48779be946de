<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">添加大题</el-button>
        <el-drawer
          title="添加大题"
          :visible.sync="dialog1"
          :direction="direction"
          size="1000px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="所属节点：" required>
                    <el-select v-model="form.region" clearable placeholder="请选择所属节点" class="form-m">
                      <el-option label="帮助中心" value="shanghai"></el-option>
                      <el-option label="消息" value="beijing"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="大题标题：" required>
                    <el-input
                      v-model="form.name"
                      clearable
                      placeholder="请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。"
                    />
                  </el-form-item>
                  <el-form-item label="题型：" required>
                    <el-radio-group v-model="form.resource">
                      <el-radio label="单选题"></el-radio>
                      <el-radio label="多选题"></el-radio>
                      <el-radio label="判断题"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="抽题规则：" required>
                    <el-radio-group v-model="form.resource">
                      <el-radio label="智能抽题"></el-radio>
                      <el-radio label="按照题库指定数量"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="本大题总分：" required>
                    <el-input v-model="form.name" class="input-num" /> <span class="f-ml5">分</span>
                  </el-form-item>
                  <el-form-item label="本大题题数：" required>
                    <el-input v-model="form.name" class="input-num" /> <span class="f-ml5">题</span>
                  </el-form-item>
                  <el-form-item label="题库1抽题数：" required>
                    <el-input v-model="form.name" class="input-num" /> <span class="f-ml5">题</span>
                  </el-form-item>
                  <el-form-item label="题库2抽题数：" required>
                    <el-input v-model="form.name" class="input-num" /> <span class="f-ml5">题</span>
                  </el-form-item>
                  <el-form-item class="is-text">
                    <span class="f-co">注：试卷为智能组卷，配置的大题数量，需先核实是否有足够的试题满足抽取。</span>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">保存</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
