import closeDistributionDiscountApply from './mutates/closeDistributionDiscountApply.graphql'
import createDistributionDiscountApply from './mutates/createDistributionDiscountApply.graphql'
import openDistributionDiscountApply from './mutates/openDistributionDiscountApply.graphql'
import operateDistributionDiscountApply from './mutates/operateDistributionDiscountApply.graphql'

export {
  closeDistributionDiscountApply,
  createDistributionDiscountApply,
  openDistributionDiscountApply,
  operateDistributionDiscountApply
}
