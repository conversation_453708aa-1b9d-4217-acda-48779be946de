<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="first">
        <div class="f-p15">详见 2201_个人发票管理_电子票（自动开票）.vue</div>
      </el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（线下开票）" name="second">
        <div class="f-p15">详见 2202_个人发票管理_电子票（线下开票） .vue</div>
      </el-tab-pane>
      <el-tab-pane label="增值税专用发票" name="third">
        <div class="f-p15">详见 2203_个人发票管理_专票.vue</div>
      </el-tab-pane>
      <el-tab-pane label="发票配送" name="four">
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15">
            <!--条件查询-->
            <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
            <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="配送状态">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="收件人姓名">
                    <el-input v-model="input" clearable placeholder="请输入收件人姓名" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="证件号">
                    <el-input v-model="input" clearable placeholder="请输入证件号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="订单号">
                    <el-input v-model="input" clearable placeholder="请输入订单号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="状态更新时间">
                    <el-date-picker
                      v-model="form.date1"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="起始时间"
                      end-placeholder="结束时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="配送方式">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="运单号">
                    <el-input v-model="input" clearable placeholder="请输入运单号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="领取人">
                    <el-input v-model="input" clearable placeholder="请输入领取人姓名" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="发票冻结状态">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="供应商">
                    <el-select v-model="select" clearable filterable placeholder="请输入或选择供应商">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="9" :xl="7" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>导出包裹信息</el-button>
                    <el-button>导入包裹运单</el-button>
                    <el-button>重置</el-button>
                    <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                    <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="订单号" min-width="220" fixed="left">
                <template>2112071509467489926</template>
              </el-table-column>
              <el-table-column label="发票号" min-width="120">
                <template>26541122</template>
              </el-table-column>
              <el-table-column label="收件信息" min-width="400">
                <template>
                  <p class="f-flex">
                    <span>收货地址：</span>
                    <span class="f-flex-sub">福建省南平市光泽县创世达802号 963010</span>
                  </p>
                  <p>收货人：刘毅</p>
                  <p>手机号: 18760422526</p>
                </template>
              </el-table-column>
              <el-table-column label="配送方式" width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">自取</div>
                  <div v-else>邮寄</div>
                </template>
              </el-table-column>
              <el-table-column label="配送状态" min-width="130">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="primary" class="badge-status">就绪</el-badge>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <el-badge is-dot type="info" class="badge-status">未就绪</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">已配送</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="配送信息 / 取件信息" min-width="260">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <p>领取人: 伍赠和</p>
                    <p>手机号: 13859552508</p>
                    <p>取货时间: 2020-03-03 11:41:06</p>
                  </div>
                  <div v-else>
                    <p>快递公司: 中国邮政</p>
                    <p>运单号: 1155194294875</p>
                    <p>发货时间: 2020-02-27 14:22:32</p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="是否冻结" min-width="120">
                <template>否</template>
              </el-table-column>
              <el-table-column label="操作" width="160" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text" size="mini">配送记录</el-button>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <el-button type="text" size="mini">确认自取</el-button>
                    <el-button type="text" size="mini">配送记录</el-button>
                  </div>
                  <div v-else-if="scope.$index === 2">
                    <el-button type="text" size="mini">确认配送</el-button>
                    <el-button type="text" size="mini">配送记录</el-button>
                  </div>
                  <div v-else-if="scope.$index === 3">
                    <el-button type="text" size="mini" disabled>已配送</el-button>
                    <el-button type="text" size="mini">配送记录</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text" size="mini" disabled>已自取</el-button>
                    <el-button type="text" size="mini">配送记录</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'four',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
