import MsAccountGateway from '@api/ms-gateway/ms-account-v1'

import { ResponseStatus } from '@hbfe/common'
import UpdateRegionManagerCreateRequestVo from './vo/UpdateRegionManagerCreateRequestVo'

/**
 * 修改地区管理员
 */
class UpdateRegionManager {
  updateRegionManagerParams = new UpdateRegionManagerCreateRequestVo()

  /**
   * @description: 修改地区管理员
   * @param {*}
   * @return {*}
   */
  async doUpdateRegionManager(): Promise<ResponseStatus> {
    const { status } = await MsAccountGateway.updateAreaAdmin(this.updateRegionManagerParams)
    return status
  }
}

export default UpdateRegionManager
