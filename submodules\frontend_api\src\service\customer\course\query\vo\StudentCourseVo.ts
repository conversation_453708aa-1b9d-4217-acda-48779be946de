import { StudentCourseLearningResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class StudentCourseVo {
  id: string
  // 课程名称
  name: string
  // 课程学时
  period: number
  // 课程学习取得结果时间
  learningResultTime: string

  static from(response: StudentCourseLearningResponse) {
    const studentCourse = new StudentCourseVo()
    studentCourse.id = response.course.courseId
    studentCourse.period = response.courseOfCourseTrainingOutline.period
    studentCourse.learningResultTime = response.studentCourse?.learningResultTime
    return studentCourse
  }
}
export default StudentCourseVo
