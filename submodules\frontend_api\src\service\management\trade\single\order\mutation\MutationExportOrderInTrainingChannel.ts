import QueryOrderListVo from '@api/service/management/trade/single/order/query/vo/QueryOrderListVo'
import { Response } from '@hbfe/common'
import MsDataExportBackStage from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import MutationExportOrderBase from '@api/service/management/trade/single/order/mutation/MutationExportOrderBase'

export default class MutationExportOrderInTrainingChannel extends MutationExportOrderBase {
  /**
   * 导出订单列表数据
   */
  async exportOrder(exportParams: QueryOrderListVo): Promise<Response<boolean>> {
    const result = new Response<boolean>()
    const request = await exportParams.to(false)
    const response = await MsDataExportBackStage.exportOrderExcelInTrainingChannel({
      request
    })
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
}
