import { Response } from '@hbfe/common'
import ExamMutationGateway from '@api/ms-gateway/ms-examquestion-v1'
import CreateLibraryDto from './vo/CreateLibraryVo'
class CreateQuestionLibrary {
  createLibraryParams = new CreateLibraryDto()
  /**
   * @description: 创建题库
   * @param {*}
   * @return {*}
   */
  async doCreateQuestionLibrary(): Promise<Response<string>> {
    return await ExamMutationGateway.createLibrary(this.createLibraryParams)
  }
}

export default CreateQuestionLibrary
