"""独立部署的微服务,K8S服务名:ms-shortcode-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取短码信息
		@param shortCode
		@return
	"""
	getShortCode(shortCode:String):ShortCodeResponse @optionalLogin
	"""根据短码获取重定向的url
		@param shortCode
		@return
	"""
	getUrlByShortCode(shortCode:String):String @optionalLogin
}
type Mutation {
	"""申请短链
		@param applyShortLinkRequest
		@return
	"""
	applyShortLink(applyShortLinkRequest:ApplyShortLinkRequest):ApplyShortLinkResultResponse @optionalLogin
	"""更新短码短链
		@param applyShortLinkRequest
		@return
	"""
	updateShortLink(applyShortLinkRequest:UpdateShortCodeRequest):Void
}
"""申请短链请求
	<AUTHOR> create 2022/7/9 9:32
"""
input ApplyShortLinkRequest @type(value:"com.fjhb.ms.shortcode.v1.kernel.gateway.graphql.request.ApplyShortLinkRequest") {
	"""跳转URL"""
	url:String
	"""元数据"""
	metadata:Map
	"""失效时的跳转URL"""
	invalidUrl:String
	"""失效时的元数据"""
	invalidMetadata:Map
	"""失效时间，null为不自动失效"""
	autoInvalidTime:DateTime
}
"""更新短码短链"""
input UpdateShortCodeRequest @type(value:"com.fjhb.ms.shortcode.v1.kernel.gateway.graphql.request.UpdateShortCodeRequest") {
	"""短码"""
	shortCode:String
	"""跳转URL"""
	url:String
}
"""申请短链结果
	<AUTHOR> create 2022/7/9 9:33
"""
type ApplyShortLinkResultResponse @type(value:"com.fjhb.ms.shortcode.v1.kernel.gateway.graphql.response.ApplyShortLinkResultResponse") {
	"""状态码。
		200 = 成功
	"""
	statusCode:String
	"""信息"""
	message:String
	"""短码"""
	shortCode:String
	"""短链"""
	shortLink:String
}
"""短码响应
	<AUTHOR> create 2022/7/9 9:11
"""
type ShortCodeResponse @type(value:"com.fjhb.ms.shortcode.v1.kernel.gateway.graphql.response.ShortCodeResponse") {
	"""跳转URL，当短码失效后返回 invalidUrl"""
	url:String
	"""元数据,当短码失效后返回 invalidMetadata"""
	metadata:Map
}

scalar List
