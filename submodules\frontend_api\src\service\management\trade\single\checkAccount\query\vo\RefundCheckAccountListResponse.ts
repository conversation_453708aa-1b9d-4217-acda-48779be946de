/*
 * @Description: 列表转换数据
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-29 16:34:50
 */

import { ReturnOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { OrderRefundTypeEnum } from '@api/service/common/return-order/enums/OrderRefundType'

export default class RefundCheckAccountListResponse {
  /**
   * 订单号
   */
  orderId?: string
  /**
   * 交易流水号
   */
  batchId?: string
  /**
   * 退款单号
   */
  refundId?: string
  /**
   * 退款成功时间
   */
  startDate?: string
  /**
   * 购买人信息 - ID
   */
  userId?: string
  /**
   * 购买人信息 - 购买人
   */
  name?: string
  /**
   * 购买人信息-登录账号
   */
  loginAccount?: string
  /**
   * 购买人信息 - 证件号
   */
  idCard?: string
  /**
   * 购买人信息 - 手机号
   */
  phone?: string
  /**
   * 实付金额
   */
  money?: number
  /**
   * 销售渠道
   */
  saleChannel: number

  /**
   * 退款类型
   */
  refundType: OrderRefundTypeEnum = undefined

  static from(returnOrderResponse: ReturnOrderResponse) {
    const {
      returnOrderNo,
      subOrderInfo: {
        saleChannel,
        orderInfo: {
          orderNo,
          orderPaymentInfo: { flowNo },
          buyer: { userId }
        }
      },
      basicData: {
        refundAmount,
        returnOrderStatusChangeTime: { returnedAndRefunded },
        returnOrderType
      }
    } = returnOrderResponse
    const refundCheckAccountListResponse = new RefundCheckAccountListResponse()
    refundCheckAccountListResponse.batchId = flowNo
    refundCheckAccountListResponse.orderId = orderNo
    refundCheckAccountListResponse.refundId = returnOrderNo
    refundCheckAccountListResponse.startDate = returnedAndRefunded
    refundCheckAccountListResponse.userId = userId
    refundCheckAccountListResponse.money = refundAmount
    refundCheckAccountListResponse.saleChannel = saleChannel
    refundCheckAccountListResponse.refundType = returnOrderType as OrderRefundTypeEnum

    return refundCheckAccountListResponse
  }
  setUserInfo(idCard: string, name: string, phone: string, loginAccount: string) {
    this.idCard = idCard
    this.name = name
    this.phone = phone
    this.loginAccount = loginAccount
  }
}
