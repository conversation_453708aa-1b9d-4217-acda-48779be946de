import { cloneDeep } from 'lodash'
import ExamPublishPattern from './ExamPublishPattern'
import { QuestionScopeSettingTypes } from '@api/service/management/resource/exam-paper/enum/ExamScopeSettingTypes'
import ExamLibrary from './ExamLibrary'
import QuestionExtract from './QuestionExtract'
import { PublishPatternTypes } from '../../../enum/ExamPaperPublishPatternTypes'
import {
  AutomaticPublishPattern,
  LibraryFixedQuestionScopeSetting,
  LibraryQuestionScopeSetting,
  QuestionExtractSetting,
  QuestionMapScoreSetting,
  QuestionScopeSetting,
  QuestionScoreSetting,
  ScoreEvaluatePattern,
  UserCourseScopeSetting
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { EvaluatePatternTypes } from '../../../enum/EvaluatePatternTypes'
import {
  AutomaticPublishPatternRequest,
  LibraryFixedQuestionScopeSettingRequest,
  LibraryQuestionScopeSettingRequest,
  QuestionExtractRuleRequest,
  QuestionExtractSettingRequest,
  QuestionScopeSettingRequest,
  QuestionScoreSettingRequest,
  ScoreEvaluatePatternRequest,
  UserCourseScopeSettingRequest
} from '@api/ms-gateway/ms-examextraction-v1'
import { MultipleQuestionMissScorePatterns } from '../../../enum/MultipleQuestionMissScorePatterns'

class AutomaticExamPaperVo extends ExamPublishPattern {
  /**
   * 建议作答时长【单位：分钟】
   * 【后端接口是秒，toDto需要转换为秒】
   */
  suggestionTimeLengthByMin = 0

  /**
   * 出卷模式类型
   * 【这里是智能组卷】
   */
  type = PublishPatternTypes.AutomaticPublishPattern

  /**
   * 出题方式(试卷级别只能选择按题库和用户课程id)
   * 【默认按题库出题】
   */
  questionScopes: QuestionScopeSettingTypes = QuestionScopeSettingTypes.LibraryQuestionScopeSetting

  /**
   * 题库选择集合
   */
  libraryIds: ExamLibrary[] = []

  /**
   * 大题
   */
  questionExtracts: QuestionExtract[] = new Array<QuestionExtract>()

  /**
   * 要求的组卷信息key.
     当{@link #userCourseSource} &#x3D; 用户课程题库时需要指定
      @see ExtractionMessageKeys
   */
  requireKeys?: Array<string> = new Array<string>()

  /**
   * 课程来源
   * 1 考试和测验用 [默认为1]
    @see UserCourseSources
   */
  private userCourseSource = 1

  /* 
    创建人id集合
  */
  private createUserIdList: Array<string> = []

  /* 【模型转换为dto】 */
  // region
  toDto(pattern: AutomaticPublishPatternRequest) {
    pattern.suggestionTimeLength = this.suggestionTimeLengthByMin * 60
    pattern.type = this.type
    /* 抽题规则 */
    pattern.questionExtractRule = new QuestionExtractRuleRequest()
    this.parseExtractQuestionPatternDto(pattern.questionExtractRule)
    /* 评定方式 */
    // 这是按分数评定规则，后续扩展需要根据情况调用
    pattern.evaluatePattern = new ScoreEvaluatePatternRequest()
    this.parseScoreEvaluatePatternDto(pattern.evaluatePattern as ScoreEvaluatePatternRequest)

    return pattern
  }

  /* 转换抽题规则 */
  private parseExtractQuestionPatternDto(qExtractRule: QuestionExtractRuleRequest) {
    // 【出题范围】
    qExtractRule.questionCount = this.getQuestionTotal()
    if (this.questionScopes === QuestionScopeSettingTypes.LibraryQuestionScopeSetting) {
      /* 按题库出题 */
      const libraryQuestion = new LibraryQuestionScopeSettingRequest()
      libraryQuestion.libraryIds = this.libraryIds?.map(item => {
        return item.id
      })
      libraryQuestion.type = this.questionScopes
      const libraryQuestionTemp = new Array<QuestionScopeSettingRequest>()
      libraryQuestionTemp.push(libraryQuestion)
      qExtractRule.questionScopes = libraryQuestionTemp
    } else {
      /* 按学员课程id出题 */
      const userCourseIdScope = new UserCourseScopeSettingRequest()
      userCourseIdScope.type = this.questionScopes
      userCourseIdScope.userCourseSource = this.userCourseSource
      const key = 'exam.extraction.userCourses.schemeId'
      userCourseIdScope.requireKeys = [key]
      const userCourseTemp = new Array<QuestionScopeSettingRequest>()
      userCourseTemp.push(userCourseIdScope)
      qExtractRule.questionScopes = userCourseTemp
    }
    // 【出题描述】
    const extractsTemp = new Array<QuestionExtractSettingRequest>()
    this.questionExtracts?.forEach(extract => {
      const questionExtractRule = new QuestionExtractSettingRequest()
      questionExtractRule.groupName = extract.groupName
      questionExtractRule.questionCount = extract.questionCount
      questionExtractRule.sequence = extract.sequence
      questionExtractRule.questionType = extract.questionType
      this.parsePublishPatternForQuestionDto(extract, questionExtractRule)
      extractsTemp.push(questionExtractRule)
    })
    qExtractRule.questionExtracts = extractsTemp
  }

  /* 
    出题范围 【试题】
    按题库抽题(智能抽题)，按题库指定数量抽题，按学员id出题
  */
  private parsePublishPatternForQuestionDto(
    extract: QuestionExtract,
    questionExtractRule: QuestionExtractSettingRequest
  ) {
    // 大题的出题范围
    if (extract.questionScopesTypes === QuestionScopeSettingTypes.LibraryQuestionScopeSetting) {
      // 按题库出题 【智能抽题】
      const libraryQuestion = new LibraryQuestionScopeSettingRequest()
      libraryQuestion.libraryIds = extract.libraryMapQuestionNumSettings?.map(el => {
        return el.id
      })
      libraryQuestion.type = extract.questionScopesTypes

      const libraryTemp = new Array<QuestionScopeSettingRequest>()
      libraryTemp.push(libraryQuestion)
      questionExtractRule.questionScopes = libraryTemp
    } else if (extract.questionScopesTypes === QuestionScopeSettingTypes.LibraryFixedQuestionExtractSetting) {
      // 按题库指定数量抽题
      const libraryFixedSetting = new LibraryFixedQuestionScopeSettingRequest()
      libraryFixedSetting.libraryMapQuestionNumSettings = extract.libraryMapQuestionNumSettings?.map(el => {
        return {
          libraryId: el.id,
          questionNum: el.count
        }
      })
      libraryFixedSetting.type = extract.questionScopesTypes
      const libraryFixedTemp = new Array<QuestionScopeSettingRequest>()
      libraryFixedTemp.push(libraryFixedSetting)
      questionExtractRule.questionScopes = libraryFixedTemp
    } else {
      // 按学员id出题
      const userCourseSetting = new UserCourseScopeSettingRequest()
      userCourseSetting.type = extract.questionScopesTypes
      userCourseSetting.userCourseSource = extract.userCourseSource
      const userCourseTemp = new Array<QuestionScopeSettingRequest>()
      userCourseTemp.push(userCourseSetting)
      questionExtractRule.questionScopes = userCourseTemp
    }
  }

  /**
   * 评定方式 【按分数评定】
   */
  private parseScoreEvaluatePatternDto(ePattern: ScoreEvaluatePatternRequest) {
    ePattern.totalScore = this.totalScore
    ePattern.qualifiedScore = this.qualifiedScore
    // 多选题得分
    ePattern.multipleMissScorePattern = MultipleQuestionMissScorePatterns.NoScore
    ePattern.type = EvaluatePatternTypes.ScoreEvaluate

    // 【默认按分数评定】
    const temp = new Array<QuestionScoreSettingRequest>()
    this.questionExtracts?.forEach((item: QuestionExtract) => {
      // 评分规则
      const scoreSetting = new QuestionScoreSettingRequest()
      scoreSetting.sequence = item.sequence
      scoreSetting.questionType = item.questionType
      scoreSetting.eachQuestionScore = item.totalScore / item.questionCount
      // 具体试题分数数组 目前不需要
      scoreSetting.questionScores = item.questionScores?.map(question => {
        return {
          questionId: question.questionId,
          score: question.score
        }
      })
      temp.push(scoreSetting)
    })
    ePattern.questionScores = temp
  }
  // endregion

  /* 【模型转换为Vo】 */
  // region
  from(pattern: AutomaticPublishPattern, list?: Array<ExamLibrary>) {
    this.suggestionTimeLengthByMin = pattern.suggestionTimeLength / 60
    this.type = pattern.type
    // 出题类型 【默认按题库出题】
    if (pattern.questionExtractRule?.questionScopes?.length) {
      this.questionScopes = pattern.questionExtractRule?.questionScopes[0]?.type
    }

    // 转换抽题规则
    this.fromQuestionExtractRule(pattern, list)

    // 转换评定方式
    this.fromEvaluatePattern(pattern)
  }

  // 抽题规则
  private fromQuestionExtractRule(pattern: AutomaticPublishPattern, list: Array<ExamLibrary>) {
    // 出题范围
    if (this.questionScopes === QuestionScopeSettingTypes.LibraryQuestionScopeSetting) {
      // 按题库出题
      this.libraryIds = list
    } else {
      // 按学员id出题
      const userQuestionList = pattern?.questionExtractRule?.questionScopes as Array<UserCourseScopeSetting>
      const key = 'exam.extraction.userCourses.schemeId'
      this.requireKeys = [key]
      if (userQuestionList?.length) {
        this.userCourseSource = userQuestionList[0]?.userCourseSource
      } else {
        this.userCourseSource = 1
      }
    }
    // 出题描述
    pattern.questionExtractRule?.questionExtracts?.forEach((extractDto: QuestionExtractSetting) => {
      const temp = new QuestionExtract()
      temp.groupName = extractDto.groupName
      temp.sequence = extractDto.sequence
      temp.questionCount = extractDto.questionCount
      temp.questionType = extractDto.questionType
      temp.questionScopesTypes = extractDto.questionScopes[0]?.type || 1
      this.parsePublishPatternForQuestion(temp, extractDto?.questionScopes)
      this.questionExtracts.push(temp)
    })
  }

  // 转换出题范围【试题】
  private parsePublishPatternForQuestion(subVo: QuestionExtract, extractList: Array<QuestionScopeSetting>) {
    if (!extractList.length) {
      subVo.libraryMapQuestionNumSettings = []
      subVo.userCourseSource = 1
    } else {
      // 出题类型
      const questionType = extractList[0]?.type
      if (questionType === QuestionScopeSettingTypes.LibraryFixedQuestionExtractSetting) {
        // 按题库指定数量
        const libraryList = extractList[0] as LibraryFixedQuestionScopeSetting
        libraryList.libraryMapQuestionNumSettings?.forEach(item => {
          const libraryMapTemp = new ExamLibrary()
          libraryMapTemp.id = item.libraryId
          libraryMapTemp.count = item.questionNum
          subVo.libraryMapQuestionNumSettings.push(libraryMapTemp)
        })
      } else if (questionType === QuestionScopeSettingTypes.LibraryQuestionScopeSetting) {
        // 按题库数量出题 【智能抽题】
        const libraryFixedList = extractList[0] as LibraryQuestionScopeSetting
        libraryFixedList.libraryIds?.forEach(sub => {
          const libraryTemp = new ExamLibrary()
          libraryTemp.id = sub
          subVo.libraryMapQuestionNumSettings.push(libraryTemp)
        })
      } else {
        // 按学员id出题
        const userIdList = extractList[0] as UserCourseScopeSetting
        subVo.userCourseSource = userIdList.userCourseSource
      }
    }
  }

  // 评定方式
  private fromEvaluatePattern(pattern: AutomaticPublishPattern) {
    if (pattern.evaluatePattern?.type === EvaluatePatternTypes.ScoreEvaluate) {
      // 按分数评定
      this.parseScoreEvaluatePattern(pattern.evaluatePattern as ScoreEvaluatePattern)
    }
    this.questionExtracts?.forEach((UiItem: QuestionExtract) => {
      UiItem.totalScore = UiItem.eachQuestionScore * UiItem.questionCount
    })
  }

  /* 按分数评定方式*/
  private parseScoreEvaluatePattern(pattern: ScoreEvaluatePattern) {
    this.questionExtracts?.forEach((item: QuestionExtract) => {
      pattern.questionScores?.forEach((sub: QuestionScoreSetting) => {
        if (item.sequence === sub.sequence) {
          item.eachQuestionScore = sub.eachQuestionScore
          item.questionType = sub.questionType
          // 具体试题分数
          item.questionScores = sub.questionScores?.map((el: QuestionMapScoreSetting) => {
            return {
              questionId: el.questionId,
              score: el.score
            }
          })
        }
      })
    })
    this.totalScore = pattern.totalScore
    this.qualifiedScore = pattern.qualifiedScore
  }

  // endregion vo

  /**
   * @description: 添加试题
   * @param {*}
   * @return {*}
   */
  addQuestion(): void {
    const questionExtract = new QuestionExtract()
    // 按题库指定数量
    if (this.questionScopes === QuestionScopeSettingTypes.LibraryFixedQuestionExtractSetting) {
      questionExtract.libraryMapQuestionNumSettings = cloneDeep(this.libraryIds)
    }
    this.questionExtracts.push(questionExtract)
  }

  /**
   * @description: 取消编辑大题/删除大题
   * @param {number} index 当前项的下标
   * @return {*}
   */
  removeQuestion(index: number): void {
    this.questionExtracts.splice(index, 1)
  }

  /**
   * @description: 获取试题总数
   * @return {number}
   */
  private getQuestionTotal(): number {
    let total = 0
    this.questionExtracts?.forEach(item => {
      if (item.questionCount) {
        total += item.questionCount
      }
    })
    return total
  }
}
export default AutomaticExamPaperVo
