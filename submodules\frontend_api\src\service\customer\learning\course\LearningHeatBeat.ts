import MsMediaResourceLearningV1 from '@api/ms-gateway/ms-media-resource-learning-v1'
import TimerTask, { Events } from '@api/service/common/timer/TimerTask'
import LearningHeatBeatResult from '@api/service/customer/learning/course/learning-heat-beat/LearningHeatBeatResult'
import LearningEventStore from '@api/service/customer/learning/LearningEventStore'
import LearningEventEnum from '@api/service/customer/learning/LearningEventEnum'
import { ResponseStatus } from '@hbfe/common'

/**
 * 学习心跳
 *  为了保持在线，默认保持3分钟与心跳服务做交互
 */
class LearningHeatBeat {
  // 计时凭证
  private readonly timingToken: string
  // 3 分钟
  timeout = 18000
  timerTask: TimerTask

  constructor(token: string) {
    this.timingToken = token
    this.timerTask = new TimerTask(this.timeout)
  }

  /**
   * 定义保持在线的功能
   */
  async keepalive() {
    this.timerTask.stop()
    this.timerTask.on(Events.timeupdate, async () => {
      const result = await MsMediaResourceLearningV1.keepAliveHeartbeat(this.timingToken)
      const heatBeatResult = LearningHeatBeatResult.from(result.data)
      if (!heatBeatResult.isSuccess()) {
        LearningEventStore.emit(LearningEventEnum.heatBeatError, heatBeatResult)
        this.timerTask.stop()
      }
    })
    this.timerTask.start()
  }

  /**
   * 断线
   */
  broken() {
    this.timerTask.stop()
  }
}

export default LearningHeatBeat
