import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-learning-constraint-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-learning-constraint-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 创建学习规则请求
<AUTHOR>
@since
 */
export class StudyConstraintRuleCreateRequest {
  /**
   * 适用行业范围
   */
  suitIndustryRangeList?: Array<SuitIndustryRangeDto>
  /**
   * 新增非适用方案
   */
  addNotSuitSchemeList?: Array<NotSuitSchemeDto>
  /**
   * 规则类型
1&#x3D;时长
2&#x3D;课时
@see com.fjhb.ms.studyconstraint.v1.api.consts.OnlineStudyConstraintRuleTypes
   */
  ruleType: number
  /**
   * 最大学习限制值
   */
  maxStudyTimeLength: number
  /**
   * 学习时长限制方式
0 &#x3D; 按日限制(默认)
@see TimeLengthLimitWays
   */
  timeLengthLimitWay: number
  /**
   * 限制值(默认1) 默认限制窗口大小为一天
   */
  timeLengthLimitValue: number
  /**
   * 指定方案设置规则
   */
  specifySchemeRuleList?: Array<SpecifySchemeRuleDto>
}

/**
 * 状态切换请求
<AUTHOR>
@since
 */
export class StudyConstraintRuleStatusRequest {
  /**
   * 规则id
   */
  ruleId?: string
}

/**
 * 创建学习规则请求
<AUTHOR>
@since
 */
export class StudyConstraintRuleUpdateRequest {
  /**
   * 规则id
   */
  ruleId?: string
  /**
   * 适用行业范围
   */
  suitIndustryRangeList?: Array<SuitIndustryRangeDto>
  /**
   * 新增非适用方案
   */
  addNotSuitSchemeList?: Array<NotSuitSchemeDto>
  /**
   * 规则类型
1&#x3D;时长
2&#x3D;课时
@see com.fjhb.ms.studyconstraint.v1.api.consts.OnlineStudyConstraintRuleTypes
   */
  ruleType: number
  /**
   * 最大学习限制值
   */
  maxStudyTimeLength: number
  /**
   * 学习时长限制方式
0 &#x3D; 按日限制(默认)
@see TimeLengthLimitWays
   */
  timeLengthLimitWay: number
  /**
   * 限制值(默认1) 默认限制窗口大小为一天
   */
  timeLengthLimitValue: number
  /**
   * 移除非适用方案
   */
  removeSchemeIds?: Array<string>
  /**
   * 指定方案设置规则(添加)
   */
  addSpecifySchemeRuleList?: Array<SpecifySchemeRuleDto>
  /**
   * 指定方案设置规则(更新)
   */
  updateSpecifySchemeRuleList?: Array<SpecifySchemeRuleDto>
  /**
   * 指定方案设置规则(需要删除的指定方案规则id)
   */
  removeSpecifySchemeRuleList?: Array<string>
}

/**
 * 非适用方案
<AUTHOR>
@since
 */
export class NotSuitSchemeDto {
  /**
   * 学习方案id
   */
  schemeId?: string
}

/**
 * <AUTHOR>
@since 2024-12-19
 */
export class SpecifySchemeRuleDto {
  /**
   * 规则id(为创建时不传)
   */
  specifySchemeRuleId?: string
  /**
   * 学习方案id集合
   */
  schemeIdList?: Array<string>
  /**
   * 最大学习限制值
   */
  maxStudyTimeLength: number
  /**
   * 学习时长限制方式
0 &#x3D; 按日限制(默认)
@see TimeLengthLimitWays
   */
  timeLengthLimitWay: number
  /**
   * 学习时间限制值(按日时 为天数 当前默认1天)
   */
  timeLengthLimitValue: number
  /**
   * 规则类型
1&#x3D;时长
2&#x3D;课时
@see com.fjhb.ms.studyconstraint.v1.api.consts.OnlineStudyConstraintRuleTypes
   */
  ruleType: number
}

/**
 * 适用行业范围
<AUTHOR>
@since
 */
export class SuitIndustryRangeDto {
  /**
   * 行业id
   */
  industryId?: string
  /**
   * 附加范围
year 年度
subjectType 科目类型
trainingCategory 培训类别
trainingObject 培训对象
discipline 学科
positionCategory 岗位类别
jobLevel 技术等级
learningPhase 学段
trainingProfessional 培训专业
@see AdditionalRangeTypes
   */
  additionalRange?: Map<string, string>
}

/**
 * <AUTHOR>
@since
 */
export class ConflictAttribute {
  /**
   * 类型
   */
  type: string
  /**
   * 当前值
   */
  value: Array<string>
  /**
   * 冲突值
   */
  conflictValue: Array<string>
}

/**
 * <AUTHOR>
@since
 */
export class VerifyResult {
  /**
   * 规则id
   */
  ruleId: string
  /**
   * 行业id
   */
  industryId: string
  /**
   * 行业名称
   */
  industryName: string
  /**
   * 冲突列表
   */
  conflictAttributes: Array<ConflictAttribute>
}

/**
 * 通用响应
400 一天学习时长不能超过1440分钟
401 一天学习时长不能超过32学时
410 适用行业与指定方案设置规则间存在不符合的方案
411 指定方案设置规则不同特殊规则间存在重复方案
412 不包含的方案、指定方案设置规则存在重复方案
300  规则冲突
500 操作错误提示
<AUTHOR>
@since
 */
export class CommonResponse {
  /**
   * code 成功&#x3D;200
   */
  code: number
  /**
   * 错误信息
   */
  errMsg: string
  /**
   * json string
   */
  data: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 无作用返回json 结构
   * CommonResponse code=300  data字段的json 结构
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async overlap(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.overlap,
    operation?: string
  ): Promise<Response<VerifyResult>> {
    return commonRequestApi<VerifyResult>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建规则
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createStudyConstraintRule(
    request: StudyConstraintRuleCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createStudyConstraintRule,
    operation?: string
  ): Promise<Response<CommonResponse>> {
    return commonRequestApi<CommonResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 禁用规则
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableStudyConstraintRule(
    request: StudyConstraintRuleStatusRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableStudyConstraintRule,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用规则
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableStudyConstraintRule(
    request: StudyConstraintRuleStatusRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableStudyConstraintRule,
    operation?: string
  ): Promise<Response<CommonResponse>> {
    return commonRequestApi<CommonResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新规则
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateStudyConstraintRule(
    request: StudyConstraintRuleUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateStudyConstraintRule,
    operation?: string
  ): Promise<Response<CommonResponse>> {
    return commonRequestApi<CommonResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
