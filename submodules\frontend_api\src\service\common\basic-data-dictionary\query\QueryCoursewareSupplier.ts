import Backstage, {
  BusinessDataDictionaryCodeRequest,
  BusinessDataDictionaryRequest,
  BusinessDataDictionaryResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { Page } from '@hbfe/common'
import DataGateway from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
class QueryCoursewareSupplier {
  /**
   * 供应商字典缓存(全量)
   */
  private allSupplierMap = new Array<BusinessDataDictionaryResponse>()

  /**
   * code查询字典缓存
   */
  private supplierCodeMap = new Map<number, BusinessDataDictionaryResponse>()

  /**
   * 查询课件供应商字典（全量）
   */
  async listQueryCoursewareSupplier(): Promise<Array<BusinessDataDictionaryResponse>> {
    if (!this.allSupplierMap.length) {
      const rq = new BusinessDataDictionaryRequest()
      rq.businessDataDictionaryType = 'COURSEWARE_SUPPLIER'

      const { data } = await Backstage.listBusinessDataDictionaryInSubProject(rq)
      if (data || data?.length) {
        this.allSupplierMap.push(...data)
      }
    }

    return this.allSupplierMap || []
  }

  /**
   * 查询课件供应商字典（分页）
   * @param page
   */
  async pageQueryCoursewareSupplier(page: Page): Promise<Array<BusinessDataDictionaryResponse>> {
    const rq = new BusinessDataDictionaryRequest()
    rq.businessDataDictionaryType = 'COURSEWARE_SUPPLIER'

    const res = await Backstage.pageBusinessDataDictionaryInSubProject({ page, request: rq })
    page.totalPageSize = res?.data?.totalPageSize
    page.totalSize = res?.data?.totalSize

    return (res?.data?.currentPageData?.length && res?.data?.currentPageData) || []
  }

  /**
   * 查询课件供应商字典（分页）新口
   * @param page
   */
  async pageQueryCoursewareSupplierNew() {
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    const res = await DataGateway.pageCoursewareSupplierGRPCInSubject(page)

    return (res?.data?.currentPageData?.length && res?.data?.currentPageData) || []
  }

  /**
   * 查询课件供应商字典 by code
   * @param page
   */
  async getCoursewareSupplier(code: number): Promise<BusinessDataDictionaryResponse> {
    if (!this.supplierCodeMap.get(code)) {
      const rq = new BusinessDataDictionaryCodeRequest()
      rq.businessDataDictionaryType = 'COURSEWARE_SUPPLIER'
      rq.code = code

      const { data } = await Backstage.getBusinessDataDictionaryInSubProject(rq)
      this.supplierCodeMap.set(code, data)
    }

    return this.supplierCodeMap.get(code)
  }
}

export default new QueryCoursewareSupplier()
