import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import UserDatumModel from '@api/service/common/models/anticheat/UserDatumModel'
import { ResponseStatus } from '@api/Response'
import UserDatumGateway from '@api/gateway/AntiCheat-default'
import PlatformAntiCheat from '@api/gateway/PlatformAntiCheat'
import UserModule from '@api/service/customer/user/query-user/UserModule'
import { RandomIdInfo } from '@api/service/customer/anti-cheat/models/RandomIdInfo'
import AntiCheatGateway from '@api/gateway/AntiCheat-default'
import { Role, RoleType } from '@api/Secure'

/**
 * 本地状态数据
 */
interface UserDatumState {
  /**
   * 用户基准照信息
   */
  userDatum: UserDatumModel
  /**
   * 是否加载报名的期数
   */
  isLoadUserDatum: boolean
}

/**
 * 照片校验代码
 */
export enum VerifyCode {
  /**
   * 成功
   */
  SUCCESS = '200',
  /**
   * 没有人脸
   */
  NO_FACE = '901',
  /**
   * 非活体
   */
  NOT_LIVENESS = '902',
  /**
   * 对比失败
   */
  COMPARE_FAILURE = '903',
  /**
   * 未处理
   */
  UNPROCESSED = '500'
}

/**
 * 校验结果
 */
export class VerifyResult {
  /**
   * 代码
   */
  code = VerifyCode.UNPROCESSED
  /**
   * 消息
   */
  message = '未处理'
}

/**
 * 我的基准照信息模块，注意：防作弊协议信息需要防作弊模块获取
 */
@Module({ namespaced: true, dynamic: true, name: 'CustomerUserDatumModule', store })
class UserDatumModule extends VuexModule implements UserDatumState {
  /**
   * 用户基准照信息
   */
  public userDatum: UserDatumModel = new UserDatumModel()
  /**
   * 是否加载报名的期数
   */
  public isLoadUserDatum = false
  /**
   * 尝试对比结果
   */
  private tryCompareResult: VerifyResult = new VerifyResult()
  /**
   * 活体验证结果
   */
  private livenessResult: VerifyResult = new VerifyResult()
  /**
   * 非人脸验证结果
   */
  private noFaceResult: VerifyResult = new VerifyResult()
  /**
   * 用户上传的基准照地址
   */
  public randomIdInfo: RandomIdInfo = new RandomIdInfo()

  /**
   * 初始化用户基准照信息
   * <pre>
   * 如果用户没有基准照，状态层中依然没有用户基准照数据
   * 所有需要通过exists方法判断
   * </pre>
   * @see exists
   */
  @Action
  @Role([RoleType.user])
  public async init(): Promise<ResponseStatus> {
    if (!this.isLoadUserDatum) {
      const { status, data } = await UserDatumGateway.findUserDatum(UserModule.userInfo.userId)
      if (status.isSuccess()) {
        const datum: UserDatumModel = new UserDatumModel()
        Object.assign(datum, data)
        datum.photoPaths = datum.photoPaths.map(e => (e ? '/mfs/' + e : ''))
        this.SET_USER_DATUM(datum)
      } else {
        return Promise.reject(status)
      }
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 重新加载用户基准照信息
   */
  @Action
  @Role([RoleType.user])
  public async doReloadUserDatum(): Promise<ResponseStatus> {
    this.SET_IS_LOAD_USER_DATUM(false)
    return this.init()
  }

  /**
   * 保存用户基准照
   * @param params 参数：paths - 基准照相对路径列表，不包含mfs;allowUpdateCount - 允许更新次数
   */
  @Action
  @Role([RoleType.user])
  public async doSave(params: { paths: Array<string>; allowUpdateCount?: number }): Promise<ResponseStatus> {
    const currentAllowUpdateCount = params.allowUpdateCount ? params.allowUpdateCount : 0
    const { status } = await UserDatumGateway.createUserDatum({
      photoPaths: params.paths,
      allowUpdateCount: currentAllowUpdateCount
    })
    if (status.isSuccess()) {
      return await this.init()
    } else {
      return Promise.reject(status)
    }
  }

  /**
   * 更新用户基准照，内部自动计算已更新次数
   * @param params 参数：paths - 基准照路径，采用完成覆盖的操作
   */
  @Action
  @Role([RoleType.user])
  public async doUpdate(params: { paths: Array<string> }): Promise<ResponseStatus> {
    const { status } = await UserDatumGateway.updatePhotoPaths(params.paths)
    if (status.isSuccess()) {
      return await this.init()
    } else {
      return Promise.reject(status)
    }
  }

  /**
   * 尝试使用新的照片与已存在的基准照进行对比
   * @see verifyTryCompareResult
   * @param params 参数：newPhotoBase64 - 新照片的Base64字符串
   */
  @Action
  @Role([RoleType.user])
  public async doTryCompareFromDatum(params: { newPhotoBase64: string }): Promise<ResponseStatus> {
    const result: VerifyResult = new VerifyResult()
    let response = await UserDatumGateway.detectPhotoFace(params.newPhotoBase64)
    if (response.status.isSuccess()) {
      if (!response.data) {
        result.code = VerifyCode.NO_FACE
        result.message = '新照片没有人脸'
        this.SET_TRY_COMPARE_RESULT(result)
        return Promise.resolve(response.status)
      }
    } else {
      return Promise.reject(response.status)
    }
    response = await UserDatumGateway.verifyPhotoLiveness(params.newPhotoBase64)
    if (response.status.isSuccess()) {
      if (!response.data) {
        result.code = VerifyCode.NOT_LIVENESS
        result.message = '新照片不是真人拍照'
        this.SET_TRY_COMPARE_RESULT(result)
        return Promise.resolve(response.status)
      }
    } else {
      return Promise.reject(response.status)
    }
    const r = await AntiCheatGateway.findSupervisionConfigBySubProject()
    let maxSimilarity = -1
    if (r.status.isSuccess()) {
      if (r.data.loginConfigSetting && r.data.loginConfigSetting.enable)
        maxSimilarity = r.data.loginConfigSetting.shapeMode.similarity
      if (r.data.examinationConfigSetting && r.data.examinationConfigSetting.enable) {
        maxSimilarity =
          r.data.examinationConfigSetting.shapeMode.similarity > maxSimilarity
            ? r.data.examinationConfigSetting.shapeMode.similarity
            : maxSimilarity
      }
      if (r.data.learningConfigSetting && r.data.learningConfigSetting.enable) {
        maxSimilarity =
          r.data.learningConfigSetting.shapeMode.similarity > maxSimilarity
            ? r.data.learningConfigSetting.shapeMode.similarity
            : maxSimilarity
      }
    }
    if (maxSimilarity == -1) return Promise.resolve(new ResponseStatus(500, '未启动防作弊'))

    const { status, data } = await UserDatumGateway.comparePhotoFromDatum(params.newPhotoBase64)
    if (status.isSuccess()) {
      if (data.result && data.similar >= maxSimilarity) {
        result.code = VerifyCode.SUCCESS
        result.message = '对比成功'
      } else {
        result.code = VerifyCode.COMPARE_FAILURE
        result.message = '对比失败'
      }
      this.SET_TRY_COMPARE_RESULT(result)
    } else {
      return Promise.reject(status)
    }

    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 尝试对比照片，通过verifyTryCompareResult获取结果
   * @see verifyTryCompareResult
   * @param params 参数：
   * photo1Base64 - 第一次拍照照片Base64字符串；
   * photo2Base64 - 需要对比的照片Base64字符串
   * similar - 相似度（从防作弊配置中获取）
   */
  @Action
  @Role([RoleType.user])
  public async doTryCompare(params: {
    photo1Base64: string
    photo2Base64: string
    similar: number
  }): Promise<ResponseStatus> {
    const result: VerifyResult = new VerifyResult()
    //region 验证照片是否人脸
    let response = await UserDatumGateway.detectPhotoFace(params.photo1Base64)
    if (response.status.isSuccess()) {
      if (!response.data) {
        result.code = VerifyCode.NO_FACE
        result.message = '第一张照片没有人脸'
        this.SET_TRY_COMPARE_RESULT(result)
        return Promise.resolve(response.status)
      }
    } else {
      return Promise.reject(response.status)
    }
    response = await UserDatumGateway.detectPhotoFace(params.photo2Base64)
    if (response.status.isSuccess()) {
      if (!response.data) {
        result.code = VerifyCode.NO_FACE
        result.message = '第二张照片没有人脸'
        this.SET_TRY_COMPARE_RESULT(result)
        return Promise.resolve(response.status)
      }
    } else {
      return Promise.reject(response.status)
    }
    //endregion

    //region 验证照片是否活体
    response = await UserDatumGateway.verifyPhotoLiveness(params.photo1Base64)
    if (response.status.isSuccess()) {
      if (!response.data) {
        result.code = VerifyCode.NOT_LIVENESS
        result.message = '第一张照片不是真人拍照'
        this.SET_TRY_COMPARE_RESULT(result)
        return Promise.resolve(response.status)
      }
    } else {
      return Promise.reject(response.status)
    }
    response = await UserDatumGateway.verifyPhotoLiveness(params.photo2Base64)
    if (response.status.isSuccess()) {
      if (!response.data) {
        result.code = VerifyCode.NOT_LIVENESS
        result.message = '第二张照片不是真人拍照'
        this.SET_TRY_COMPARE_RESULT(result)
        return Promise.resolve(response.status)
      }
    } else {
      return Promise.reject(response.status)
    }
    //endregion

    const { status, data } = await UserDatumGateway.comparePhoto({
      photo1: params.photo1Base64,
      photo2: params.photo2Base64
    })
    if (status.isSuccess()) {
      if (data.result && data.similar >= params.similar) {
        result.code = VerifyCode.SUCCESS
        result.message = '对比成功'
      } else {
        result.code = VerifyCode.COMPARE_FAILURE
        result.message = '对比失败'
      }
      this.SET_TRY_COMPARE_RESULT(result)
    } else {
      return Promise.reject(status)
    }

    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 验证照片是否是人脸，通过verifyNoFaceResult获取结果
   * @see verifyNoFaceResult
   * @param params 参数：photoBase64 - 照片的Base64字符串
   */
  @Action
  @Role([RoleType.user])
  public async doVerifyNoFace(params: { photoBase64: string }): Promise<ResponseStatus> {
    const result: VerifyResult = new VerifyResult()
    const { status, data } = await UserDatumGateway.detectPhotoFace(params.photoBase64)
    if (status.isSuccess()) {
      if (data) {
        result.code = VerifyCode.SUCCESS
      } else {
        result.code = VerifyCode.NO_FACE
        result.message = '没有人脸'
      }
      this.SET_NO_FACE_RESULT(result)
    } else {
      return Promise.reject(status)
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 验证照片是否是活体，通过verifyLivenessResult获取结果
   * @see verifyLivenessResult
   * @param params 参数：photoBase64 - 照片的Base64字符串
   */
  @Action
  @Role([RoleType.user])
  public async doVerifyLiveness(params: { photoBase64: string }): Promise<ResponseStatus> {
    const result: VerifyResult = new VerifyResult()
    const { status, data } = await UserDatumGateway.verifyPhotoLiveness(params.photoBase64)
    if (status.isSuccess()) {
      if (data) {
        result.code = VerifyCode.SUCCESS
      } else {
        result.code = VerifyCode.NOT_LIVENESS
        result.message = '不是真人拍照'
      }
      this.SET_LIVENESS_RESULT(result)
    } else {
      return Promise.reject(status)
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 注册基准照二维码的随机码
   * @param params
   */
  @Action
  @Role([RoleType.user])
  public async doApplyDatumPhotoRandom(): Promise<ResponseStatus> {
    const response = await PlatformAntiCheat.applyDatumPhotoRandom()
    if (response.status.isSuccess()) {
      this.SET_RANDOM_ID(response.data)
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 获取基准照的地址
   * @param params
   */
  @Action
  @Role([RoleType.user])
  public async getRandomIdInfo(): Promise<ResponseStatus> {
    const { status, data } = await PlatformAntiCheat.getRandomIdInfo(this.randomIdInfo.randomId)
    if (status.isSuccess()) {
      this.SET_RANDOM_INFO(data)
    } else {
      return Promise.reject(status)
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 设置用户基准照
   * @param params
   * @constructor
   */
  @Mutation
  private SET_USER_DATUM(params: UserDatumModel) {
    this.userDatum = params
    this.isLoadUserDatum = true
  }

  /**
   * 设置是否已加载用户基准照配置
   * @param isLoad 是否加载
   * @constructor
   */
  @Mutation
  private SET_IS_LOAD_USER_DATUM(isLoad: boolean) {
    this.isLoadUserDatum = isLoad
  }

  /**
   * 设置上传基准照结果
   * @param result 结果
   * @constructor
   */
  @Mutation
  private SET_TRY_COMPARE_RESULT(result: VerifyResult) {
    this.tryCompareResult = result
  }

  /**
   * 设置活体检测结果
   * @param result 结果
   */
  @Mutation
  private SET_LIVENESS_RESULT(result: VerifyResult) {
    this.livenessResult = result
  }

  /**
   * 设置非人脸验证结果
   * @param result 结果
   */
  @Mutation
  private SET_NO_FACE_RESULT(result: VerifyResult) {
    this.noFaceResult = result
  }

  /**
   * 用户上传的基准照地址
   * @param info
   * @constructor
   */
  @Mutation
  private SET_RANDOM_INFO(info: { imgUrl: string; expire: boolean }) {
    this.randomIdInfo.imgUrl = info.imgUrl
    this.randomIdInfo.expire = info.expire
  }

  /**
   * 用户上传的基准照地址
   * @param id
   * @constructor
   */
  @Mutation
  private SET_RANDOM_ID(id: string) {
    this.randomIdInfo.randomId = id
  }

  /**
   * 是否存在基准照
   */
  get exists(): boolean {
    return !!this.userDatum?.userId
  }

  /**
   * 获取我的基准照列表，已包含mfs
   */
  get photos(): Array<string> {
    return this.exists ? this.userDatum.photoPaths : []
  }

  /**
   * 获取尝试对比结果，必须先调用doTryCompare方法
   * @see doTryCompare
   * @see doTryCompareFromDatum
   */
  get verifyTryCompareResult(): VerifyResult {
    return this.tryCompareResult
  }

  /**
   * 获取非人脸验证结果，必须先调用doVerifyNoFace方法
   * @see doVerifyNoFace
   */
  get verifyNoFaceResult(): VerifyResult {
    return this.noFaceResult
  }

  /**
   * 获取活体检测结果，必须先调用doVerifyLiveness方法
   * @see doVerifyLiveness
   */
  get verifyLivenessResult(): VerifyResult {
    return this.livenessResult
  }
}

export default getModule(UserDatumModule)
