import { LoginAuthenticationDTO } from '@api/gateway/PlatformUser'

export class LoginAccountInfo {
  /**
   * 登陆账号类型
   *
   *  * 帐号密码认证方式
   * int ACCOUNT_PASSWORD = 1;
   *  * 【第三方】微信认证方式
   * int WEB_CHAT = 2;
   *  * 【第三方】补贴管理系统
   * int BT_SYSTEM = 10;
   */
  loginType: number
  /**
   * 登陆账号
   */
  loginInput: string
  /**
   * 密码
   */
  password?: string

  static from(dto: LoginAuthenticationDTO): LoginAccountInfo {
    const info = new LoginAccountInfo()
    info.loginType = dto.identityType
    if (dto.identity) {
      info.loginInput = dto.identity
    }
    return info
  }

  toLoginAccountDTO(): LoginAuthenticationDTO {
    const dto = new LoginAuthenticationDTO()
    dto.identityType = this.loginType
    dto.identity = this.loginInput
    return dto
  }

  mergeFrom(dto: LoginAuthenticationDTO) {
    this.loginType = dto.identityType
    if (dto.identity) {
      this.loginInput = dto.identity
    }
  }
}
