import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-training-channel-admin-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-training-channel-admin-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 创建专题管理员请求
 */
export class CreateTrainingChannelAdminRequest {
  /**
   * 登录账户【必填】
   */
  account: string
  /**
   * 姓名【必填】
   */
  name: string
  /**
   * 手机
   */
  phone?: string
  /**
   * 启用状态  正常: 1 禁用: 2【必填】
   */
  status: number
  /**
   * 添加的角色id集合【必填】
   */
  addRoleIds: Array<string>
  /**
   * 管理的专题id集合
   */
  trainingChannelIds: Array<string>
}

/**
 * @Description 修改专题管理员请求
<AUTHOR>
@Date 2024/6/5 16:42
 */
export class UpdateTrainingChannelAdminRequset {
  /**
   * 被修改的管理员账户ID【必填】
   */
  accountId: string
  /**
   * 姓名【为null，表示不更新】
   */
  name?: string
  /**
   * 手机【为null，表示不更新】
   */
  phone?: string
  /**
   * 启用状态  正常: 1 禁用: 2【必填】
   */
  status: number
  /**
   * 添加的角色id集合
   */
  addRoleIds?: Array<string>
  /**
   * 移除的角色id集合
   */
  removeRoleIds?: Array<string>
  /**
   * 添加的专题ID集合
   */
  addTrainingChannelIds?: Array<string>
  /**
   * 移除的专题Id集合
   */
  removeTrainingChannelIds?: Array<string>
}

/**
 * <AUTHOR> [2023/7/11 20:54]
 */
export class GenernalResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建专题管理员
   * @param request 创建专题管理员请求
   * @return {code,message}
   * 40001, "指定角色不存在"
   * 40002, "当前角色不为专题管理员"
   * 2001：缺失数据
   * 3001：账号已被使用
   * 3002：手机号已被使用
   * 3100：网校未开启专题配置
   * 3200：专题不属于指定网校
   * 30000, "请输入11位真实有效手机号"
   * 30010, "账号和手机号一致"
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createTrainingChannelAdministrator(
    request: CreateTrainingChannelAdminRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createTrainingChannelAdministrator,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改专题管理员
   * @param request 修改专题管理员请求
   * @return {code,message}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingChannelAdministrator(
    request: UpdateTrainingChannelAdminRequset,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingChannelAdministrator,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
