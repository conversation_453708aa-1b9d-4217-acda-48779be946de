/*
 * @Author: chenwei<PERSON> chenweinian
 * @Date: 2024-11-14 08:57:51
 * @LastEditors: chenweinian chenweinian
 * @LastEditTime: 2024-12-12 16:42:27
 * @FilePath: \ahjspxpt_frontend_uniapp\submodules\frontend_api\src\service\common\scheme\model\RegionTreeProperty.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
/**
 * @description 地区树属性
 */
class RegionTreeProperty {
  /**
   * 地区id
   * @description 比如：鼓楼区 => 350101
   */
  id = ''
  /**
   * 地区名称
   */
  name = ''
  /**
   * 父级地区id
   * @description 比如：鼓楼区 => 350100（福州市）
   */
  parentId = ''
  /**
   * 地区完整路径
   * @description 比如：鼓楼区 => '/350000/350100/350101'
   */
  regionPath = ''
  /**
   * 地区完整路径数组
   * @description 比如：鼓楼区 => ['350000','350100','350101']
   */
  regionPathCode: string[] = []
  /**
   * 子地区节点
   */
  children: RegionTreeProperty[] = []
}

export default RegionTreeProperty
