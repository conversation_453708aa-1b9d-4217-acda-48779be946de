import UserPlayCourse from '@api/service/customer/learning/course/UserPlayCourse'
import { bind, debounce } from 'lodash-decorators'

/**
 * 试听课程
 */
class UserPreviewCourse extends UserPlayCourse {
  async init(coursePlayToken: string) {
    await super.init(coursePlayToken)
    await this.changeCurrentPlayMedia({ chapterIndex: 0, coursewareIndex: 0 })
  }

  @bind
  @debounce(200)
  async changeCurrentPlayMedia(event: { chapterIndex: number; coursewareIndex: number }): Promise<boolean> {
    return super.changeCurrentPlayMedia(event)
  }
}

export default UserPreviewCourse
