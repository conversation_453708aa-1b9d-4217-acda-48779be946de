import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-basicdata-domain-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-basicdata-domain-gateway-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * @author: xucenhao
@time: 2024-10-09
@description:
 */
export class ImageCaptchaTrack {
  /**
   * 背景图片宽度.
   */
  bgImageWidth?: number
  /**
   * 背景图片高度.
   */
  bgImageHeight?: number
  /**
   * 滑块图片宽度.
   */
  sliderImageWidth?: number
  /**
   * 滑块图片高度.
   */
  sliderImageHeight?: number
  /**
   * 滑动开始时间.
   */
  startSlidingTime: number
  /**
   * 滑动结束时间.
   */
  endSlidingTime: number
  /**
   * 滑动的轨迹.
   */
  trackList?: Array<Track>
}

export class Track {
  /**
   * x.
   */
  x?: number
  /**
   * y.
   */
  y?: number
  /**
   * 时间.
   */
  t?: number
  /**
   * 类型.
   */
  type?: string
}

/**
 * @author: linxiquan
@Date: 2023/11/3 17:23
@Description: 教师行业专门字段： 用户学段、学科信息
 */
export class SectionAndSubjects {
  /**
   * 学段
   */
  section?: number
  /**
   * 学科
   */
  subjects?: number
}

/**
 * 获取当前登录手机号短信请求信息
<AUTHOR> 2022/6/27 19:28
 */
export class ApplyCurrentPhoneSmsRequest {
  /**
   * 获取当前登录手机号短信token
   */
  token?: string
  /**
   * 业务类型
登录  10000
帐户注册 20000
忘记密码  30000
换绑手机号 40000
   */
  businessType?: number
}

/**
 * <AUTHOR>
@description:【服务商】给当前登陆账户绑定手机号 - 免登录
@date 2024/12/19 19:49
 */
export class BindPhoneForCurrentAccountRequest {
  /**
   * 链式token-账户Id
   */
  token?: string
  /**
   * 绑定的手机号
   */
  phone?: string
}

/**
 * 图形验证码申请信息
<AUTHOR>
 */
export class CaptchaApplyRequest {
  /**
   * 【必填】图形验证码业务Token
   */
  token?: string
  /**
   * 【必填】图形验证码业务类型
@see ValidationBusinessTypes
   */
  businessType: number
}

/**
 * 图形验证码验证信息
<AUTHOR>
 */
export class CaptchaValidRequest {
  /**
   * 【必填】申请图形验证码获得的token
   */
  token?: string
  /**
   * 【必填】图形验证码
   */
  captchaCode?: string
}

/**
 * 管理员更改手机号
<AUTHOR> [2023/4/17 18:46]
 */
export class ChangePhoneByAdminRequest {
  /**
   * 修改手机号账户id[必填]
   */
  userId?: string
  /**
   * 新手机号【必填】
   */
  newPhone?: string
  /**
   * 旧手机号【必填】
   */
  oldPhone?: string
}

/**
 * 换绑手机号请求信息
<AUTHOR>
 */
export class ChangePhoneRequest {
  /**
   * 【必填】旧手机号token
   */
  oldPhoneToken?: string
  /**
   * 【必填】新手机号token
   */
  newPhoneToken?: string
}

/**
 * 创建内置角色请求
<AUTHOR>
 */
export class CoerceCreateRoleRequest {
  id?: string
  /**
   * 应用方类型（不传则取上下文数据）【创建内置角色需赋值】
@see SystemMemberTypes
   */
  applicationMemberType?: number
  /**
   * 应用方类别
@see com.fjhb.domain.basicdata.api.consts.ApplicationMemberCategories
   */
  applicationMemberCategory: number
  /**
   * 角色类别
@see RoleCategories
   */
  category?: number
  /**
   * 所属方类型（不传则取上下文数据）【创建内置角色需赋值】
@see SystemMemberTypes
   */
  ownerMemberType?: number
  /**
   * 所属方id（不传则取上下文数据）【创建内置角色需赋值】
   */
  ownerMemberId?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 应用类型，标明当前角色用于什么客户域（人社/用工企业）
@see BusinessDomainIdentity#applicationType
@see UnitBusinessTypes
@see SubProjectBusinessTypes
   */
  applicationType?: number
  /**
   * 角色描述
   */
  description?: string
  /**
   * 角色code
   */
  code?: string
  /**
   * 安全对象id
   */
  securityObjectIds?: Array<string>
}

export class CollectiveRegisterChangePhoneRequest {
  /**
   * 【必填】新手机号码
   */
  newPhone: string
  /**
   * 【必填】短信验证码
   */
  smsCode: string
  /**
   * 【必填】申请短信返回的token
   */
  token: string
}

/**
 * 创建超级管理员账户请求信息
<AUTHOR> 2022/7/18 11:26
 */
export class CreateAdministratorAccountRequest {
  /**
   * 管理员姓名【必填】
   */
  name?: string
  /**
   * 登录账号【必填】
   */
  accountName?: string
  /**
   * 手机号【必填】
   */
  phone?: string
  /**
   * 角色列表【必填】
   */
  roleIds?: Array<string>
}

/**
 * 角色响应类
<AUTHOR>
 */
export class CreateRoleByAdminTypeRequest {
  /**
   * 名称
   */
  name?: string
  /**
   * 角色描述
   */
  description?: string
  /**
   * 角色code
   */
  code?: string
  /**
   * 角色分类
1-学员  2-集体报名管理员  3-管理员  4-人设管理员  5-企业管理员  6-人社审批管理员  7-企业经办  8-企业法人  9-企业超管  10-人社超管
11-人社职建处  12-人社就业局  13-企业超管  14-合同供应商  15-专家
   */
  category?: number
  /**
   * 功能权限id集合
   */
  functionalAuthorityIds?: Array<string>
}

/**
 * 角色响应类
<AUTHOR>
 */
export class CreateRoleRequest {
  /**
   * 名称
   */
  name?: string
  /**
   * 角色描述
   */
  description?: string
  /**
   * 角色code
   */
  code?: string
  /**
   * 角色分类
1-学员  2-集体报名管理员  3-管理员  4-人设管理员  5-企业管理员  6-人社审批管理员  7-企业经办  8-企业法人  9-企业超管  10-人社超管
11-人社职建处  12-人社就业局  13-企业超管  14-合同供应商  15-专家
   */
  category?: number
  /**
   * 功能权限id集合
   */
  functionalAuthorityIds?: Array<string>
}

/**
 * 创建服务提供商请求
<AUTHOR> 2022/7/19
 */
export class CreateServiceProviderAccountRequest {
  /**
   * 姓名【必填】
   */
  name?: string
  /**
   * 用户名【必填】
   */
  userName?: string
  /**
   * 手机号【必填】
   */
  phone?: string
  /**
   * 密码 【必填】
   */
  password?: string
  /**
   * 顶级账户Id 【超管创建时必填】
   */
  rootAccountId?: string
}

export class CreateSubProjectAdministratorRequest {
  /**
   * 平台名称，做为企业
   */
  platformName: string
  /**
   * 平台管理员名称【非必填】
   */
  platformManagerName?: string
  /**
   * 平台管理员角色名
   */
  platformManagerRoleId: string
  /**
   * 平台管理员账号
   */
  platformManagerLoginAccount: string
  /**
   * 平台管理员密码
   */
  platformManagerLoginPassword: string
  /**
   * 手机号【非必填】
   */
  phone?: string
}

/**
 * 创建内置角色请求
<AUTHOR>
 */
export class CreateSystemInternalRoleRequest {
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  unitId?: string
  servicerId?: string
  coerceCreateRoleRequests?: Array<CoerceCreateRoleRequest>
}

/**
 * 修改当前账户密码请求(用户端忘记密码)
<AUTHOR>
 */
export class CurrentAccountChangePasswordCauseForgetRequest {
  /**
   * 认证token
   */
  token?: string
  /**
   * 新密码
   */
  password?: string
}

/**
 * 变更当前登录帐户的密码信息
<AUTHOR>
 */
export class CurrentAccountChangePasswordRequest {
  /**
   * 【必填】原始密码
   */
  originalPassword?: string
  /**
   * 【必填】新密码
   */
  newPassword?: string
}

/**
 * 用工企业创建信息
<AUTHOR>
 */
export class EnterpriseCreateRequest {
  /**
   * 【必填】加密的元数据
   */
  metadata?: string
  /**
   * 【必填】企业名称
   */
  name?: string
  /**
   * 【必填】统一社会信用代码
   */
  code?: string
  /**
   * 【必填】单位类型
   */
  type?: string
  /**
   * 【必填】所属行业
   */
  industry?: string
  /**
   * 【必填】注册地区
   */
  area?: string
  /**
   * 【必填】联系地址
   */
  address?: string
  /**
   * 注册地址
   */
  registerAddress?: string
  /**
   * 【必填】邮编
   */
  postcode?: string
  /**
   * 【必填】注册资金
   */
  registeredCapital?: string
  /**
   * 【必填】注册登记机关
   */
  registeredOrgan?: string
  /**
   * 营业期限起始日期
   */
  operatingBeginDate?: string
  /**
   * 营业期限截止日期
   */
  operatingEndDate?: string
  /**
   * 【必填】成立日期
   */
  foundedDate?: string
  /**
   * 法定代表人/负责人
   */
  legalPerson?: string
  /**
   * 【必填】法定代表人证件类型
   */
  idCardType: number
  /**
   * 【必填】法定代表人证件号
   */
  idCard?: string
}

/**
 * 用工企业经办创建信息
<AUTHOR>
 */
export class EnterpriseManagerCreateRequest {
  /**
   * 【必填】姓名
   */
  name?: string
  /**
   * 【必填】身份证号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 要授权的角色的ID集合
   */
  roleIds?: Array<string>
}

/**
 * 用工企业经办通过闽政通注册信息
<AUTHOR>
 */
export class EnterpriseManagerRegisterForMztRequest {
  /**
   * 【必填】登录路口标识token
   */
  token?: string
  /**
   * 【必填】认证中心闽政通登录后返回的token
   */
  mztToken?: string
}

/**
 * 用工企业经办注册信息
<AUTHOR>
 */
export class EnterpriseManagerRegisterRequest {
  /**
   * 【必填】手机短信验证码验证token
   */
  token?: string
  /**
   * 【必填】手机号码
   */
  phone?: string
  /**
   * 【必填】密码
   */
  password?: string
}

/**
 * 用工企业经办创建信息
<AUTHOR>
 */
export class EnterpriseManagerUpdateRequest {
  /**
   * 【必填】账户ID
   */
  accountId?: string
  /**
   * 【必填】姓名
   */
  name?: string
  /**
   * 【必填】身份证号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 要新增授权的角色的ID集合
   */
  addRoleIds?: Array<string>
  /**
   * 要移除授权的角色的ID集合
   */
  removeRoleIds?: Array<string>
}

/**
 * 申请企业元数据请求
<AUTHOR>
 */
export class EnterpriseMetadataApplyRequest {
  /**
   * 加密的元数据
   */
  metadata?: string
}

/**
 * 用工企业更新信息
<AUTHOR>
 */
export class EnterpriseUpdateRequest {
  /**
   * 【必填】企业id
   */
  id?: string
  /**
   * 【必填】企业名称
   */
  name?: string
  /**
   * 【必填】统一社会信用代码
   */
  code?: string
  /**
   * 【必填】单位类型
   */
  type?: string
  /**
   * 【必填】所属行业
   */
  industry?: string
  /**
   * 【必填】注册地区
   */
  area?: string
  /**
   * 【必填】联系地址
   */
  address?: string
  /**
   * 【必填】注册地址
   */
  registerAddress?: string
  /**
   * 【必填】邮编
   */
  postcode?: string
  /**
   * 【必填】注册资金
   */
  registeredCapital?: string
  /**
   * 【必填】注册登记机关
   */
  registeredOrgan?: string
  /**
   * 营业期限起始日期
   */
  operatingBeginDate?: string
  /**
   * 营业期限截止日期
   */
  operatingEndDate?: string
  /**
   * 【必填】成立日期
   */
  foundedDate?: string
  /**
   * 法定代表人/负责人
   */
  legalPerson?: string
  /**
   * 【必填】法定代表人证件类型
   */
  idCardType: number
  /**
   * 【必填】法定代表人证件号
   */
  idCard?: string
}

/**
 * @BelongsProject: fjhb-microservice-basicdata-domain-gateway
@BelongsPackage: com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.request
@Author: xucenhao
@CreateTime: 2024-06-13  14:59
@Description:
 */
export class ExistUserRequest {
  /**
   * 认证标识
   */
  identity?: string
  /**
   * 认证标识类型
@see AuthenticationIdentityTypes
   */
  identityType?: number
}

export class FindBuildInRoleRequest {
  /**
   * 默认网校管理员，查询出网校管理员和专题管理员角色
角色code
com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.consts.RoleCodeConstans
   */
  code?: string
}

export class FindCurrentRoleListByArgsRequest {
  /**
   * 应用类型数组
不传的话默认从上下文中去应用类型
7 专题管理员
1002 网校
   */
  applicationTypes?: Array<number>
}

/**
 * @author: xucenhao
@time: 2024-09-05
@description:
 */
export class FindCurrentRoleListPlusRequest {
  /**
   * 应用方id
   */
  applicationMemberId?: string
}

/**
 * 查询所属方角色请求（包含内置角色）
<AUTHOR>
 */
export class FindRoleByOwnerRequest {
  /**
   * 所属方类型【必填】
4-子项目  5-单位  6-服务商
   */
  ownerMemberType?: number
  /**
   * 所属方id【必填】
   */
  ownerMemberId?: string
}

/**
 * 强制修改初始密码请求
 */
export class ForceModifyInitPasswordRequest {
  /**
   * 【必填】新密码
   */
  newPassword?: string
}

/**
 * 立即重置密码
<AUTHOR>
 */
export class ImmediateResetPasswordRequest {
  /**
   * 【必填】帐户ID
   */
  accountId?: string
  /**
   * 【必填】要重置密码的业务类型
人社管理员-MOHRSS  系统管理员-SYSTEM  管理员-SUPER_ADMINISTRATOR  供应商-SERVICE_PROVIDER
课件供应商-COURSEWARE_SUPPLIER,网校(培训机构)-TRAINING_INSTITUTION,学员-STUDENT,地区管理员-REGION_ADMINISTRATOR
集体报名管理员-COLLECTIVE_REGISTER
   */
  businessType?: string
}

/**
 * 人社管理员创建信息
<AUTHOR>
 */
export class MOHRSSAdminCreateRequest {
  /**
   * 【必填】姓名
   */
  name?: string
  /**
   * 【必填】身份证号
   */
  idCard?: string
  /**
   * 登录账号
   */
  userName?: string
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】所在处/科室
   */
  belongDivision?: string
  /**
   * 【必填】要授权的角色的ID集合
   */
  roleIds?: Array<string>
}

/**
 * 人社管理员创建信息
<AUTHOR>
 */
export class MOHRSSAdminUpdateRequest {
  /**
   * 【必填】管理员账户ID
   */
  accountId?: string
  /**
   * 【必填】姓名
   */
  name?: string
  /**
   * 登录账号
   */
  userName?: string
  /**
   * 【必填】身份证号
   */
  idCard?: string
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】所在处/科室
   */
  belongDivision?: string
  /**
   * 要新增授权的角色的ID集合
   */
  addRoleIds?: Array<string>
  /**
   * 要移除授权的角色的ID集合
   */
  removeRoleIds?: Array<string>
}

/**
 * 下属人社管理员创建信息
<AUTHOR>
 */
export class MOHRSSSubordinateAdminCreateRequest {
  /**
   * 【必填】下属人社单位ID
   */
  unitId?: string
  /**
   * 【必填】姓名
   */
  name?: string
  /**
   * 【必填】身份证号
   */
  idCard?: string
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 所在处/科室
   */
  belongDivision?: string
  /**
   * 【必填】角色Id列表
   */
  roles?: Array<string>
}

/**
 * 下属人社管理员更新信息
<AUTHOR>
 */
export class MOHRSSSubordinateAdminUpdateRequest {
  /**
   * 【必填】管理员账户ID
   */
  accountId?: string
  /**
   * 【必填】下属人社单位ID
   */
  unitId?: string
  /**
   * 【必填】姓名
   */
  name?: string
  /**
   * 【必填】身份证号
   */
  idCard?: string
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 所在处/科室
   */
  belongDivision?: string
  /**
   * 添加的角色Id列表
   */
  addRoles?: Array<string>
  /**
   * 移除的角色Id列表
   */
  removeRoles?: Array<string>
}

export class RegisterCollectiveRegisterAdminRequest {
  /**
   * 【必填】姓名
   */
  name: string
  /**
   * 【必填】手机号码
   */
  phone: string
  /**
   * 【必填】用户输入的图片验证码
   */
  captcha?: string
  /**
   * 【必填】短信验证码
   */
  smsCode: string
  /**
   * 【必填】密码
   */
  password: string
  /**
   * 【必填】申请短信返回的token
   */
  token: string
}

/**
 * 创建企业账户请求信息
<AUTHOR> 2022/6/27 11:26
 */
export class RegisterEnterpriseAccountRequest {
  /**
   * 单位名称
   */
  name?: string
  /**
   * 统一社会信用代码
   */
  code?: string
  /**
   * 注册地址
   */
  registerAddress?: string
  /**
   * 登录账号
   */
  accountName?: string
  /**
   * 单位姓名
   */
  userName?: string
  /**
   * 登录密码
   */
  password?: string
  /**
   * 手机号验证token
   */
  token?: string
}

/**
 * 删除内置角色请求
<AUTHOR>
 */
export class RemoveSystemInternalRoleRequest {
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  unitId?: string
  servicerId?: string
  /**
   * 角色id
   */
  ids?: Array<string>
}

/**
 * <AUTHOR>
@description:重置Admin账户密码请求参数（跨域）
@date 2024/12/23 16:17
 */
export class ResetAdminPasswordRequest {
  /**
   * 【必填】帐户ID
   */
  accountId?: string
  /**
   * 【必填】前端传入的密码
   */
  password?: string
}

/**
 * <AUTHOR> [2023/5/24 15:29]
 */
export class ResetPasswordRequest {
  /**
   * 【必填】帐户ID
   */
  accountId?: string
  /**
   * 【必填】前端传入的密码
   */
  password?: string
}

/**
 * @author: xucenhao
@time: 2024-10-08
@description: 安全的验证码申请请求
 */
export class SecureCaptchaApplyRequest {
  /**
   * 【必填】验证码业务Token
   */
  token?: string
  /**
   * 【必填】验证码业务类型
@see ValidationBusinessTypes
   */
  businessType: number
  /**
   * 【必填】验证码类型
@see CaptchaTypeConstant
   */
  captchaType?: string
}

/**
 * 手机短信验证码申请信息
<AUTHOR>
 */
export class SmsCodeApplyRequest {
  /**
   * 【必填】申请token
   */
  token?: string
  /**
   * 【必填】手机短信验证码业务类型
@see ValidationBusinessTypes
   */
  businessType: number
  /**
   * 手机号
   */
  phone?: string
  /**
   * 验证验证码数据
   */
  verifyCaptchaData?: ImageCaptchaTrack
}

/**
 * 手机短信验证码申请信息
<AUTHOR>
 */
export class SmsCodeForChangingPhoneApplyRequest {
  /**
   * 【必填】申请token
   */
  token?: string
  /**
   * 【必填】手机短信验证码业务类型
@see ValidationBusinessTypes
   */
  businessType: number
}

/**
 * 手机短信验证码
<AUTHOR>
 */
export class SmsCodeValidRequest {
  /**
   * 【必填】申请手机短信验证码的Token
   */
  token?: string
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】手机短信验证码
   */
  smsCode?: string
}

/**
 * 解绑手机-给当前登陆用户
<AUTHOR>
 */
export class UnbindPhoneForCurrentUserRequest {
  /**
   * 原手机号【必填】
   */
  phone: string
}

/**
 * 解绑手机
<AUTHOR>
 */
export class UnbindPhoneRequest {
  /**
   * 用户ID【必填】
   */
  userId: string
  /**
   * 原手机号【必填】
   */
  phone: string
}

/**
 * <AUTHOR> 2022/6/16 16:37
 */
export class UnitCreateRequest {
  id?: string
  /**
   * 单位名称
   */
  name?: string
  /**
   * 所辖地区
   */
  theirArea?: string
  /**
   * 用户姓名
   */
  userName?: string
  /**
   * 【必填】认证标识
   */
  identity?: string
  /**
   * 【必填】密码
   */
  password?: string
  /**
   * 授予角色id（企业账户）
   */
  addEnterpriseRoleIds?: Array<string>
  /**
   * 授予角色id（企业个人账户）
   */
  addEnterprisePersonRoleIds?: Array<string>
  /**
   * 下级单位
   */
  subUnitInfoList?: Array<UnitCreateRequest>
}

/**
 * 修改超级管理员账户请求信息
<AUTHOR> 2022/7/18 11:26
 */
export class UpdateAdministratorAccountRequest {
  /**
   * 账户id【必填】
   */
  accountId?: string
  /**
   * 管理员姓名 【必填】
   */
  name?: string
  /**
   * 登录账号
   */
  accountName?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 新增角色列表
   */
  addRoleIds?: Array<string>
  /**
   * 删除角色列表
   */
  removeRoleIds?: Array<string>
  /**
   * 是否更新全部
   */
  updateAll: boolean
}

export class UpdateCollectiveRegisterAccountInfoRequest {
  /**
   * 【必填】用户ID
   */
  userId?: string
  /**
   * 【必填】认证标识类型 1用户名，2手机，3身份证，4电子邮箱
@see com.fjhb.domain.basicdata.api.account.consts.AuthenticationIdentityTypes
   */
  identityType: number
  /**
   * 【必填】认证标识：帐号
   */
  identity?: string
}

export class UpdateCollectiveRegisterRequest {
  /**
   * 【必填】姓名
   */
  name?: string
}

/**
 * 修改超级管理员账户请求信息
<AUTHOR> 2022/7/18 11:26
 */
export class UpdateOnlineAdministratorAccountRequest {
  /**
   * 账户id【必填】
   */
  accountId?: string
  /**
   * 管理员姓名 【必填】
   */
  name?: string
  /**
   * 登录账号【必填】
   */
  accountName?: string
  /**
   * 服务商id【必填】
   */
  serviceId?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 是否更新全部
   */
  updateAll: boolean
}

/**
 * 修改角色请求
<AUTHOR>
 */
export class UpdateRoleByAdminTypeRequest {
  id?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 角色来源,来源与哪个角色
   */
  source?: string
  /**
   * 角色描述
   */
  description?: string
  /**
   * 功能权限id集合
   */
  functionalAuthorityIds?: Array<string>
}

/**
 * 修改角色请求
<AUTHOR>
 */
export class UpdateRoleRequest {
  id?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 角色描述
   */
  description?: string
  /**
   * 功能权限id集合
   */
  functionalAuthorityIds?: Array<string>
}

/**
 * 更新服务提供商请求
<AUTHOR> 2022/7/19
 */
export class UpdateServiceProviderAccountRequest {
  /**
   * 账号【必填】
   */
  accountId?: string
  /**
   * 姓名 【必填】
   */
  name?: string
  /**
   * 用户名【必填】
   */
  userName?: string
  /**
   * 手机号【必填】
   */
  phone?: string
  /**
   * 顶级账户Id 【超管修改时必填】
   */
  rootAccountId?: string
}

/**
 * 修改内置角色请求
<AUTHOR>
 */
export class UpdateSystemInternalRoleRequest {
  id?: string
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  unitId?: string
  servicerId?: string
  /**
   * 应用方类型（不传则取上下文数据）【创建内置角色需赋值】
@see SystemMemberTypes
   */
  applicationMemberType?: number
  /**
   * 应用方类别
@see com.fjhb.domain.basicdata.api.consts.ApplicationMemberCategories
   */
  applicationMemberCategory: number
  /**
   * 角色类别
@see RoleCategories
   */
  category?: number
  /**
   * 所属方类型（不传则取上下文数据）【创建内置角色需赋值】
@see SystemMemberTypes
   */
  ownerMemberType?: number
  /**
   * 所属方id（不传则取上下文数据）【创建内置角色需赋值】
   */
  ownerMemberId?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 应用类型，标明当前角色用于什么客户域（人社/用工企业）
@see BusinessDomainIdentity#applicationType
@see UnitBusinessTypes
@see SubProjectBusinessTypes
   */
  applicationType?: number
  /**
   * 角色描述
   */
  description?: string
  /**
   * 角色code
   */
  code?: string
}

/**
 * 验证账号/手机号是否存在请求
<AUTHOR>
 */
export class ValidIdentityFxsRequest {
  /**
   * 姓名
   */
  name?: string
  /**
   * 【必填】账号/手机号
   */
  identity?: string
  /**
   * 【必填】图形验证码验证通过生成的token 或 验证身份的初始token
   */
  token?: string
  /**
   * 【必填】业务类型
@see ValidationBusinessTypes
忘记密码:30000
   */
  businessType: number
}

/**
 * 验证账号/手机号是否存在请求
<AUTHOR>
 */
export class ValidIdentityRequest {
  /**
   * 姓名
   */
  name?: string
  /**
   * 【必填】账号/手机号
   */
  identity?: string
  /**
   * 【必填】图形验证码验证通过生成的token 或 验证身份的初始token
   */
  token?: string
  /**
   * 【必填】业务类型
@see ValidationBusinessTypes
忘记密码:30000
   */
  businessType: number
}

/**
 * 申请校验人脸核身认证V1
<AUTHOR>
 */
export class ApplyValidFacialRecognitionV1Request {
  /**
   * 登录路口标识token【必填】
   */
  token: string
  /**
   * 姓名【必填】
   */
  name: string
  /**
   * 身份证号【必填】
   */
  idCardNumber: string
}

/**
 * 申请校验人脸核身认证V1 Web端请求
<AUTHOR>
 */
export class ApplyValidFacialRecognitionV1WebRequest {
  /**
   * 登录路口标识token【必填】
   */
  token: string
  /**
   * 登陆随机码唯一标识【必填】
   */
  loginRandomCode: string
  /**
   * 姓名【必填】
   */
  name: string
  /**
   * 身份证号【必填】
   */
  idCardNumber: string
}

/**
 * 申请校验人脸核身认证结果请求
<AUTHOR>
 */
export class ApplyValidFacialRecognitionVerifyResultExtendRequest {
  /**
   * 登录路口标识token【必填】
   */
  token: string
  /**
   * 姓名【必填】
   */
  name: string
  /**
   * 身份证号【必填】
   */
  idCardNumber: string
  /**
   * 本次认证结果凭据【选填】，若填写则会进行人脸二次核验
   */
  verifyResult?: string
  /**
   * openId/unionId
   */
  openId: string
}

/**
 * 申请校验人脸核身认证结果请求
<AUTHOR>
 */
export class ApplyValidFacialRecognitionVerifyResultRequest {
  /**
   * 登录路口标识token【必填】
   */
  token: string
  /**
   * 姓名【必填】
   */
  name: string
  /**
   * 身份证号【必填】
   */
  idCardNumber: string
  /**
   * 本次认证结果凭据，第三方可以选择根据这个凭据获取相关信息【必填】
   */
  verifyResult: string
}

/**
 * @author: linxiquan
@Date: 2023/7/10 17:52
@Description: 求职者请求
 */
export class JobSeekerBindPlatformAccountRequest {
  /**
   * 微信开放平台  openId  【必填】
   */
  openId?: string
  /**
   * 昵称 nickname 【必填】
   */
  nickname?: string
  /**
   * 是否换绑  为 true 则走换绑  false 则走绑定
   */
  changeBind?: boolean
}

/**
 * 创建网校子管理员请求
<AUTHOR>
 */
export class CreateOnlineSchoolSubAdminByTokenRequest {
  /**
   * 初始token【必传】
   */
  token: string
  /**
   * 登录账户【必填】
   */
  identity: string
  /**
   * 姓名【必填】
   */
  name: string
  /**
   * 性别  女: 0 男:1【必填】
   */
  gender: number
  /**
   * 手机
   */
  phone?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 启用状态  正常: 1 禁用: 2【必填】
   */
  status: number
  /**
   * 密码【必填】
   */
  password: string
  /**
   * 添加的角色id集合【必填】
   */
  addRoleIds: Array<string>
}

/**
 * 创建网校子管理员请求
<AUTHOR>
 */
export class CreateOnlineSchoolSubAdminRequest {
  /**
   * 登录账户【必填】
   */
  identity: string
  /**
   * 姓名【必填】
   */
  name: string
  /**
   * 性别  女: 0 男:1【必填】
   */
  gender: number
  /**
   * 手机
   */
  phone?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 启用状态  正常: 1 禁用: 2【必填】
   */
  status: number
  /**
   * 密码【必填】
   */
  password: string
  /**
   * 添加的角色id集合【必填】
   */
  addRoleIds: Array<string>
}

/**
 * 更新网校子管理员请求
<AUTHOR>
 */
export class UpdateOnlineSchoolSubAdminByTokenRequest {
  /**
   * 初始token【必传】
   */
  token: string
  /**
   * 被修改的管理员账户ID【必填】
   */
  accountId: string
  /**
   * 登录账户【为null，表示不更新】
   */
  identity?: string
  /**
   * 姓名【为null，表示不更新】
   */
  name?: string
  /**
   * 性别【为null，表示不更新】
   */
  gender?: number
  /**
   * 手机【为null，表示不更新】
   */
  phone?: string
  /**
   * 邮箱【为null，表示不更新】
   */
  email?: string
  /**
   * 启用状态  正常: 1 禁用: 2【必填】
   */
  status: number
  /**
   * 添加的角色id集合
   */
  addRoleIds?: Array<string>
  /**
   * 移除的角色id集合
   */
  removeRoleIds?: Array<string>
}

/**
 * 更新网校子管理员请求
<AUTHOR>
 */
export class UpdateOnlineSchoolSubAdminRequest {
  /**
   * 被修改的管理员账户ID【必填】
   */
  accountId: string
  /**
   * 登录账户【为null，表示不更新】
   */
  identity?: string
  /**
   * 姓名【为null，表示不更新】
   */
  name?: string
  /**
   * 性别【为null，表示不更新】
   */
  gender?: number
  /**
   * 手机【为null，表示不更新】
   */
  phone?: string
  /**
   * 邮箱【为null，表示不更新】
   */
  email?: string
  /**
   * 启用状态  正常: 1 禁用: 2【必填】
   */
  status: number
  /**
   * 添加的角色id集合
   */
  addRoleIds?: Array<string>
  /**
   * 移除的角色id集合
   */
  removeRoleIds?: Array<string>
}

/**
 * 申请绑定微信开放平台并返回账户ID登陆token
<AUTHOR>
 */
export class ApplyBindAndLoginRequest {
  /**
   * 初始token【必填】
   */
  token: string
  /**
   * 角色类别
@see com.fjhb.domain.basicdata.api.role.consts.RoleCategories
1:学员
   */
  roleCategory: number
  /**
   * 登陆账号【必填】
   */
  account: string
  /**
   * 登陆密码【必填】
   */
  password: string
  /**
   * OpenId【必填】
   */
  openId: string
  /**
   * 微信用户nickname
   */
  nickname?: string
}

export class ApplyBindWeChatOpenPlatformLoginAccountRequest {
  /**
   * 【必填】token(初始化token)
   */
  token: string
  /**
   * unionId【必填】
   */
  unionId: string
  /**
   * 微信用户nickname
   */
  nickname?: string
  /**
   * 【必填】是否换绑  为 true 则走换绑  false 则走绑定
   */
  changeBind: boolean
}

/**
 * 申请登陆微信开放平台
<AUTHOR>
 */
export class ApplyLoginByOpenIdRequest {
  /**
   * 初始token【必填】
   */
  token: string
  /**
   * 角色类别
@see com.fjhb.domain.basicdata.api.role.consts.RoleCategories
1:学员
   */
  roleCategory: number
  /**
   * OpenId【必填】
   */
  openId: string
}

/**
 * <AUTHOR>
 */
export class ApplyOpenIdRequest {
  /**
   * 授权临时票据code【必填】
   */
  code: string
  /**
   * 服务商ID【必填】
   */
  servicerId: string
}

/**
 * @author: linxiquan
@Date: 2024/6/18 11:58
@Description: 当前登录账户绑定微信开放平台
 */
export class BindLoginAccountOpenPlatformRequest {
  /**
   * 【必填】token(初始化token)
   */
  token: string
  /**
   * 【必填】单点登录Token
   */
  loginToken: string
  /**
   * 【必填】是否换绑  为 true 则走换绑  false 则走绑定
   */
  changeBind: boolean
}

/**
 * 校验是否绑定微信开放平台
<AUTHOR>
 */
export class ValidIsBindWeChatOpenPlatformRequest {
  /**
   * 初始token【必填】
   */
  token: string
  /**
   * 角色类别
@see com.fjhb.domain.basicdata.api.role.consts.RoleCategories
1:学员
   */
  roleCategory: number
  /**
   * OpenId【必填】
   */
  openId: string
}

/**
 * 绑定平台账号【微信】的请求
<AUTHOR>
 */
export class BindPlatformAccountRequest {
  /**
   * 【必填】认证标识(证件号或手机号)
   */
  identity: string
  /**
   * 【必填】密码
   */
  password: string
  /**
   * 用户输入的图片验证码
   */
  captcha?: string
  /**
   * token(图片验证码校验成功生成的)
   */
  token?: string
  /**
   * 【必填】单点登录Token
   */
  loginToken: string
  /**
   * 验证验证码数据
   */
  verifyCaptchaData?: ImageCaptchaTrack
}

export class CreateOnlineSchoolStudentRequest {
  /**
   * 【必填】证件号码
   */
  idCard: string
  /**
   * 【必填】证件类型
   */
  idCardType: number
  /**
   * 【必填】密码
   */
  password: string
  /**
   * 【必填】短信验证码
   */
  smsCode: string
  /**
   * 【必填】用户输入的图片验证码
   */
  captcha: string
  /**
   * 【必填】申请短信返回的token
   */
  token: string
  /**
   * 单点登录Token
   */
  loginToken?: string
  /**
   * 网校服务商id
   */
  onlineSchoolServicerId?: string
  /**
   * 【必填】用户名称
   */
  name?: string
  /**
   * 性别
@see com.fjhb.domain.basicdata.api.user.consts.Genders
   */
  gender?: number
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】所属区域
   */
  area?: string
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * 统一社会信用码
   */
  companyCode?: string
  /**
   * [必填]加密值
   */
  encrypt?: string
  /**
   * 行业信息
   */
  userIndustryInfos?: Array<CreateUserIndustryRequest>
  /**
   * 工作单位所在地区
   */
  companyRegionCode?: string
  /**
   * 【必填-前端校验】邮箱地址
   */
  email?: string
}

/**
 * @author: linxiquan
@Date: 2023/6/8 10:41
@Description:新增证书信息
 */
export class CreateStudentCertificateRequest {
  /**
   * 【必填】用户id
   */
  userId?: string
  /**
   * 【必填】行业id
   */
  industryId?: string
  /**
   * 【必填】证书编号
   */
  certificateNo?: string
  /**
   * 【必填】证书类别
   */
  certificateCategory?: string
  /**
   * 【必填】注册专业
   */
  registerProfessional?: string
  /**
   * 【必填】发证日期（起）
   */
  releaseStartTime?: string
  /**
   * 【必填】证书有效期（止）
   */
  certificateEndTime?: string
  /**
   * 【必填】证书附件
   */
  certificateAttachments?: Array<CertificateAttachment>
}

/**
 * <AUTHOR>
 */
export class CreateStudentRequest {
  /**
   * 【必填】证件号码
   */
  idCard: string
  /**
   * 【必填】证件类型
   */
  idCardType: number
  /**
   * 【必填】密码
   */
  password: string
  /**
   * 【必填】短信验证码
   */
  smsCode: string
  /**
   * 【必填】用户输入的图片验证码
   */
  captcha: string
  /**
   * 【必填】申请短信返回的token
   */
  token: string
  /**
   * 单点登录Token
   */
  loginToken?: string
  /**
   * 【必填】用户名称
   */
  name?: string
  /**
   * 性别
@see com.fjhb.domain.basicdata.api.user.consts.Genders
   */
  gender?: number
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】所属区域
   */
  area?: string
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * 统一社会信用码
   */
  companyCode?: string
  /**
   * [必填]加密值
   */
  encrypt?: string
  /**
   * 行业信息
   */
  userIndustryInfos?: Array<CreateUserIndustryRequest>
  /**
   * 工作单位所在地区
   */
  companyRegionCode?: string
  /**
   * 【必填-前端校验】邮箱地址
   */
  email?: string
}

/**
 * @author: zhengp 2022/1/24 15:27
 */
export class CreateUserIndustryRequest {
  /**
   * 所属行业
   */
  industryId?: string
  /**
   * 一级专业类别
   */
  firstProfessionalCategory?: string
  /**
   * 二级专业类别
   */
  secondProfessionalCategory?: string
  /**
   * 职称等级
   */
  professionalQualification?: string
  /**
   * 证书信息
   */
  certificateInfos?: Array<CertificateInfo>
  /**
   * 人员类别（职业卫生行业）
   */
  personnelCategory?: string
  /**
   * 岗位类别（职业卫生行业）
   */
  positionCategory?: string
  /**
   * 技术等级（工勤行业）
   */
  professionalLevel?: string
  /**
   * 工种（工勤行业）
   */
  jobCategoryId?: string
  /**
   * 教师行业 学段、学科信息 ，例：[{&quot;学段1&quot;:&quot;学科1&quot;}, {&quot;学段1&quot;:&quot;学科2&quot;}]
   */
  sectionAndSubjects?: Array<SectionAndSubjects>
  /**
   * 证书类型（药师行业）
   */
  certificatesType?: string
  /**
   * 执证类别（药师行业）
   */
  practitionerCategory?: string
}

export class CertificateAttachment {
  certificateUrl?: string
  name?: string
}

export class CertificateInfo {
  /**
   * 更新时必填
   */
  certificateId?: string
  /**
   * 证书编号
   */
  certificateNo?: string
  /**
   * 证书类别
   */
  certificateCategory?: string
  /**
   * 注册专业
   */
  registerProfessional?: string
  /**
   * 发证日期（起）
   */
  releaseStartTime?: string
  /**
   * 证书有效期（止）
   */
  certificateEndTime?: string
  /**
   * 证书附件
   */
  certificateAttachments?: Array<CertificateAttachment>
}

/**
 * @author: linxiquan
@Date: 2023/6/8 10:46
@Description:删除证书信息
 */
export class DeleteStudentCertificateRequest {
  /**
   * 【必填】证书id
   */
  certificateId?: string
  /**
   * 【必填】用户id
   */
  userId?: string
  /**
   * 【必填】行业id
   */
  industryId?: string
}

export class UpdateStudentBasicInfoRequest {
  /**
   * 姓名
   */
  name?: string
  /**
   * 性别
   */
  gender?: number
}

/**
 * @author: linxiquan
@Date: 2023/6/8 10:46
@Description:更新证书信息
 */
export class UpdateStudentCertificateRequest {
  /**
   * 【必填】证书id
   */
  certificateId?: string
  /**
   * 【必填】用户id
   */
  userId?: string
  /**
   * 【必填】行业id
   */
  industryId?: string
  /**
   * 【必填】证书编号
   */
  certificateNo?: string
  /**
   * 【必填】证书类别
   */
  certificateCategory?: string
  /**
   * 【必填】注册专业
   */
  registerProfessional?: string
  /**
   * 【必填】发证日期（起）
   */
  releaseStartTime?: string
  /**
   * 【必填】证书有效期（止）
   */
  certificateEndTime?: string
  /**
   * 【必填】证书附件
   */
  certificateAttachments?: Array<CertificateAttachment>
}

/**
 * <AUTHOR>
 */
export class UpdateStudentRequest {
  /**
   * 证件类别
   */
  idCardType?: number
  /**
   * 证件号码
   */
  idCard?: string
  /**
   * 短信验证码
   */
  smsCode?: string
  /**
   * 用户输入的图片验证码
   */
  captcha?: string
  /**
   * 申请短信返回的token
   */
  token?: string
  /**
   * 【必填】默认为普通更新false，强制更新true则会携带短信验证码和token
   */
  forcedUpdate?: boolean
  /**
   * 【必填】用户名称
   */
  name?: string
  /**
   * 性别
@see com.fjhb.domain.basicdata.api.user.consts.Genders
   */
  gender?: number
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】所属区域
   */
  area?: string
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * 统一社会信用码
   */
  companyCode?: string
  /**
   * [必填]加密值
   */
  encrypt?: string
  /**
   * 行业信息
   */
  userIndustryInfos?: Array<CreateUserIndustryRequest>
  /**
   * 工作单位所在地区
   */
  companyRegionCode?: string
  /**
   * 【必填-前端校验】邮箱地址
   */
  email?: string
}

/**
 * <AUTHOR>
 */
export class UpdateStudentSystemRequest {
  /**
   * 用户id
   */
  userId?: string
  /**
   * 【必填】证件号码
   */
  idCard?: string
  /**
   * 【必填】证件类别
   */
  idCardType?: number
  /**
   * 【必填】用户名称
   */
  name?: string
  /**
   * 性别
@see com.fjhb.domain.basicdata.api.user.consts.Genders
   */
  gender?: number
  /**
   * 【必填】手机号
   */
  phone?: string
  /**
   * 【必填】所属区域
   */
  area?: string
  /**
   * 工作单位
   */
  companyName?: string
  /**
   * 统一社会信用码
   */
  companyCode?: string
  /**
   * [必填]加密值
   */
  encrypt?: string
  /**
   * 行业信息
   */
  userIndustryInfos?: Array<CreateUserIndustryRequest>
  /**
   * 工作单位所在地区
   */
  companyRegionCode?: string
  /**
   * 【必填-前端校验】邮箱地址
   */
  email?: string
}

/**
 * @author: xucenhao
@time: 2024-10-08
@description:
 */
export class ImageCaptchaVO {
  /**
   * 验证码类型.
   */
  type: string
  /**
   * 背景图.
   */
  backgroundImage: string
  /**
   * 移动图.
   */
  templateImage: string
  /**
   * 背景图片所属标签.
   */
  backgroundImageTag: string
  /**
   * 模板图片所属标签.
   */
  templateImageTag: string
  /**
   * 背景图片宽度.
   */
  backgroundImageWidth: number
  /**
   * 背景图片高度.
   */
  backgroundImageHeight: number
  /**
   * 滑动图片宽度.
   */
  templateImageWidth: number
  /**
   * 滑动图片高度.
   */
  templateImageHeight: number
}

/**
 * <AUTHOR>
 */
export class ApplyOpenIdResponse {
  /**
   * code为200时，openId才有值
   */
  openId: string
  /**
   * 昵称
   */
  nickname: string
  unionId: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 图形验证码响应信息
<AUTHOR>
 */
export class CaptchaResponse {
  /**
   * 图形验证码
   */
  captcha: string
  /**
   * 请求结果状态码
   */
  code: number
  /**
   * token
   */
  token: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 切换企业token响应信息
<AUTHOR>
 */
export class ChangeEnterpriseTokenResponse {
  /**
   * 企业单位ID
   */
  unitId: string
  /**
   * 企业名称
   */
  name: string
  /**
   * 企业统一社会信用代码
   */
  code: string
  /**
   * 是否已认证
   */
  identityVerification: boolean
  /**
   * 管理员与企业关系状态 1为正常可用  0为冻结不可用
@see PersonUnitRelationshipStatus
   */
  status: number
  /**
   * 切换企业上下文的token
   */
  token: string
}

/**
 * 切换企业管理员单位token响应信息
<AUTHOR>
 */
export class ChangePolicyActorUnitAuthorizeTokenResponse {
  changeTokenInfos: Array<ChangeEnterpriseAdminUnitInfo>
  /**
   * 请求结果状态码
   */
  code: number
  /**
   * token
   */
  token: string
  /**
   * 响应消息
   */
  message: string
}

export class ChangeEnterpriseAdminUnitInfo {
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 统一社会信用代码
   */
  unitCode: string
  /**
   * 单位状态 1正常,2冻结
@see com.fjhb.domain.basicdata.api.unit.consts.UnitStatus
   */
  unitStatus: number
  /**
   * 管理员与单位关系状态 1为正常可用  0为冻结不可用
@see com.fjhb.domain.basicdata.api.unit.consts.PersonUnitRelationshipStatus
   */
  personUnitRelationStatus: number
  /**
   * 账户状态
   */
  accountStatus: number
  /**
   * 账户类型
   */
  accountType: number
  /**
   * 切换单位上下文的token(认证授权用)
   */
  token: string
}

/**
 * 企业账户注册信息
<AUTHOR>
 */
export class EnterpriseAccountRegisterResponse {
  /**
   * 请求结果状态码
   */
  code: number
  /**
   * token
   */
  token: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 用工企业经办注册信息
<AUTHOR>
 */
export class EnterpriseManagerRegisterResponse {
  /**
   * 第三方原数据
   */
  metadata: string
  /**
   * 请求结果状态码
   */
  code: number
  /**
   * token
   */
  token: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 申请企业元数据响应
<AUTHOR>
 */
export class EnterpriseMetadataApplyResponse {
  /**
   * 企业名称
   */
  name: string
  /**
   * 企业统一社会信用代码
   */
  code: string
}

/**
 * @BelongsProject: fjhb-microservice-basicdata-domain-gateway
@BelongsPackage: com.fjhb.ms.basicdata.domain.gateway.v1.kernel.graphql.response
@Author: xucenhao
@CreateTime: 2024-06-13  15:12
@Description:
 */
export class ExistUserResponse {
  code: number
  message: string
  userId: string
  accountId: string
}

export class FindFunctionalAuthorityByRoleIdNewResponse {
  /**
   * 安全对象模型列表
   */
  securityObjectResponses: Array<SecurityObjectNewResponse>
}

export class FindLoginUserOpenIdResponse {
  openId: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * <AUTHOR>
 */
export class GenernalResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * <AUTHOR>
 */
export class GetCurrentUserServicerResponse {
  /**
   * 当前登录用户可用的被授权的服务商id列表
   */
  servicerIdList: Array<string>
}

/**
 * 验证账号/手机号是否存在响应
<AUTHOR>
 */
export class IdentityValidResponse {
  /**
   * 当前账户已绑定的数据脱敏的手机号
   */
  boundDesensitizationPhone: string
  /**
   * 请求结果状态码
   */
  code: number
  /**
   * token
   */
  token: string
  /**
   * 响应消息
   */
  message: string
}

export class RegisterStudentResponse {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 请求结果状态码
   */
  code: number
  /**
   * token
   */
  token: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 角色响应类
<AUTHOR>
 */
export class RoleResponse {
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * 应用方类型
4-子项目  5-单位  6-服务商
   */
  applicationMemberType: number
  /**
   * 角色类别
1-学员  2-集体报名管理员  3-管理员  4-人设管理员  5-企业管理员  6-人社审批管理员  7-企业经办  8-企业法人  9-企业超管  10-人社超管
11-人社职建处  12-人社就业局  13-企业超管  14-合同供应商  15-专家
   */
  category: number
  /**
   * 所属方类型
4-子项目  5-单位  6-服务商
   */
  ownerMemberType: number
  /**
   * 所属方id
   */
  ownerMemberId: string
  /**
   * 所属角色名称
   */
  belongRoleName: string
  /**
   * 角色性质
1-系统内置  2-自定义
   */
  nature: number
  /**
   * 角色描述
   */
  description: string
  /**
   * 角色code
   */
  code: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 修改时间
   */
  updatedTime: string
}

/**
 * 角色响应类
<AUTHOR>
 */
export class RoleResponseForPersonAccount {
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * 应用方类型
4-子项目  5-单位  6-服务商
   */
  applicationMemberType: number
  /**
   * 应用方id
   */
  applicationMemberId: string
  /**
   * 角色类别
1-学员  2-集体报名管理员  3-管理员  4-人设管理员  5-企业管理员  6-人社审批管理员  7-企业经办  8-企业法人  9-企业超管  10-人社超管
11-人社职建处  12-人社就业局  13-企业超管  14-合同供应商  15-专家
   */
  category: number
  /**
   * 所属方类型
4-子项目  5-单位  6-服务商
   */
  ownerMemberType: number
  /**
   * 所属方id
   */
  ownerMemberId: string
  /**
   * 角色性质
1-系统内置  2-自定义
   */
  nature: number
  /**
   * 角色描述
   */
  description: string
  /**
   * 角色code
   */
  code: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 修改时间
   */
  updatedTime: string
}

/**
 * 角色信息对应账户数量响应
<AUTHOR>
 */
export class RoleToAccountCountResponse {
  /**
   * 角色信息
   */
  roleResponse: RoleResponse
  /**
   * 账户数量
   */
  accountCount: number
}

/**
 * 菜单响应类
<AUTHOR>
 */
export class RoleToFunctionalAuthorityNewResponse {
  /**
   * 角色id
   */
  roleId: string
  /**
   * 功能权限对象集合
   */
  functionalAuthorityList: Array<SecurityObjectNewResponse>
}

/**
 * 菜单响应类
<AUTHOR>
 */
export class RoleToFunctionalAuthorityResponse {
  /**
   * 角色id
   */
  roleId: string
  /**
   * 功能权限对象集合
   */
  functionalAuthorityList: Array<SecurityObjectResponse>
}

/**
 * 安全性高的验证码响应信息
<AUTHOR>
 */
export class SecureCaptchaResponse {
  /**
   * token
   */
  token: string
  /**
   * 验证码展示数据
   */
  displayCaptchaData: ImageCaptchaVO
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 安全对象模型
<AUTHOR>
 */
export class SecurityObjectNewResponse {
  /**
   * 安全对象ID
   */
  id: string
  /**
   * 安全对象授权id【定制角色才有值】
当前角色为定制角色|勾选定制角色创建的自定义角色 返回授权ID
   */
  securityAuthorizationId: string
  /**
   * 能力服务版本id【弃用】
   */
  avmId: string
  /**
   * 安全对象类型
0-所有  1-url  2-方法  3-安全对象组
   */
  type: string
  /**
   * 是否为菜单
   */
  isMenu: boolean
  /**
   * 安全对象名称
   */
  name: string
  /**
   * 安全对象名称 用于UI显示的名称
   */
  viewName: string
  /**
   * URL内容
   */
  urlContent: string
  /**
   * URL唯一标识
   */
  urlMark: string
  /**
   * 安全对象内容
   */
  content: string
  /**
   * 安全对象内容的CRC32
   */
  contentCRC32: number
  /**
   * 安全对象描述
   */
  description: string
  /**
   * 安全对象扩展信息
   */
  ext: string
  /**
   * 父级安全对象Id
   */
  parentId: string
  /**
   * 业务对象ID
   */
  objectId: string
  /**
   * 排序号
   */
  sortNo: number
  /**
   * 创建人员ID
   */
  creatorId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 安全对象动作类型
   */
  actionType: number
}

/**
 * 安全对象模型
<AUTHOR>
 */
export class SecurityObjectResponse {
  /**
   * 安全对象ID
   */
  id: string
  /**
   * 能力服务版本id【弃用】
   */
  avmId: string
  /**
   * 安全对象类型
0-所有  1-url  2-方法  3-安全对象组
   */
  type: string
  /**
   * 是否为菜单
   */
  isMenu: boolean
  /**
   * 安全对象名称
   */
  name: string
  /**
   * 安全对象名称 用于UI显示的名称
   */
  viewName: string
  /**
   * URL内容
   */
  urlContent: string
  /**
   * URL唯一标识
   */
  urlMark: string
  /**
   * 安全对象内容
   */
  content: string
  /**
   * 安全对象内容的CRC32
   */
  contentCRC32: number
  /**
   * 安全对象描述
   */
  description: string
  /**
   * 安全对象扩展信息
   */
  ext: string
  /**
   * 父级安全对象Id
   */
  parentId: string
  /**
   * 业务对象ID
   */
  objectId: string
  /**
   * 排序号
   */
  sortNo: number
  /**
   * 创建人员ID
   */
  creatorId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 安全对象动作类型
   */
  actionType: number
}

/**
 * <AUTHOR>
 */
export class TokenExtendResponse {
  token: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * Token 响应对象
<AUTHOR>
 */
export class TokenResponse {
  /**
   * 请求结果状态码
   */
  code: number
  /**
   * token
   */
  token: string
  /**
   * 响应消息
   */
  message: string
}

export class ApplyBindWeChatOpenPlatformLoginAccountResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

export class BindLoginAccountOpenPlatformResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 校验是否啊绑定开放平台响应
<AUTHOR>
 */
export class ValidIsBindOpenPlatformResponse {
  /**
   * 是否绑定
   */
  isBind: boolean
  /**
   * 所属用户ID
   */
  userId: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询内置性质的角色
   * 目前不按照登录账户角色进行过滤，由前端进行控制，只要能看到新建角色管理能进行角色添加这个菜单栏的都查询出网校管理员和专题管理员
   * 根据Apollo配置获取展示的角色
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findBuildInRole(
    request: FindBuildInRoleRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findBuildInRole,
    operation?: string
  ): Promise<Response<Array<RoleResponse>>> {
    return commonRequestApi<Array<RoleResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取当前可分配角色列表
   * @return 可分配角色列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCurrentAssignableRoleList(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findCurrentAssignableRoleList,
    operation?: string
  ): Promise<Response<Array<RoleToAccountCountResponse>>> {
    return commonRequestApi<Array<RoleToAccountCountResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询当前所有功能权限
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCurrentFunctionalAuthorityList(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findCurrentFunctionalAuthorityList,
    operation?: string
  ): Promise<Response<Array<SecurityObjectResponse>>> {
    return commonRequestApi<Array<SecurityObjectResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询当前所有角色列表
   * @return 角色对应账户数量列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCurrentRoleList(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findCurrentRoleList,
    operation?: string
  ): Promise<Response<Array<RoleToAccountCountResponse>>> {
    return commonRequestApi<Array<RoleToAccountCountResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询当前登录应用域下的所有角色列表
   * 可根据传参指定应用类型来获取角色列表，传参未给值时，默认取上下文中的应用类型进行查询
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCurrentRoleListByArgs(
    request: FindCurrentRoleListByArgsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findCurrentRoleListByArgs,
    operation?: string
  ): Promise<Response<Array<RoleToAccountCountResponse>>> {
    return commonRequestApi<Array<RoleToAccountCountResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询当前所有角色列表 new
   * 调用仓储联表查时去除多余无用联表操作
   * @return 角色对应账户数量列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCurrentRoleListNew(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findCurrentRoleListNew,
    operation?: string
  ): Promise<Response<Array<RoleToAccountCountResponse>>> {
    return commonRequestApi<Array<RoleToAccountCountResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findCurrentRoleListPlus(
    request: FindCurrentRoleListPlusRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findCurrentRoleListPlus,
    operation?: string
  ): Promise<Response<Array<RoleToAccountCountResponse>>> {
    return commonRequestApi<Array<RoleToAccountCountResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 角色id查询功能权限
   * @param roleId 角色id
   * @return 功能权限对象列表
   * @param query 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findFunctionalAuthorityByRoleId(
    roleId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findFunctionalAuthorityByRoleId,
    operation?: string
  ): Promise<Response<Array<SecurityObjectResponse>>> {
    return commonRequestApi<Array<SecurityObjectResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { roleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 角色id查询功能权限【返回来源id】
   * @param roleId
   * @return
   * @param query 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findFunctionalAuthorityByRoleIdNew(
    roleId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findFunctionalAuthorityByRoleIdNew,
    operation?: string
  ): Promise<Response<FindFunctionalAuthorityByRoleIdNewResponse>> {
    return commonRequestApi<FindFunctionalAuthorityByRoleIdNewResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { roleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 角色id集合查询功能权限
   * @param roleIds 角色id集合
   * @return 角色对应功能权限集合
   * @param query 查询 graphql 语法文档
   * @param roleIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findFunctionalAuthorityByRoleIds(
    roleIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findFunctionalAuthorityByRoleIds,
    operation?: string
  ): Promise<Response<Array<RoleToFunctionalAuthorityResponse>>> {
    return commonRequestApi<Array<RoleToFunctionalAuthorityResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { roleIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 角色id集合查询功能权限
   * @param roleIds 角色id集合
   * @return 角色对应功能权限集合【来源id】
   * @param query 查询 graphql 语法文档
   * @param roleIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findFunctionalAuthorityByRoleIdsNew(
    roleIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findFunctionalAuthorityByRoleIdsNew,
    operation?: string
  ): Promise<Response<Array<RoleToFunctionalAuthorityNewResponse>>> {
    return commonRequestApi<Array<RoleToFunctionalAuthorityNewResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { roleIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询当前登录用户的openId
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findLoginUserOpenId(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findLoginUserOpenId,
    operation?: string
  ): Promise<Response<FindLoginUserOpenIdResponse>> {
    return commonRequestApi<FindLoginUserOpenIdResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 账户id查询角色详情
   * @return 角色响应类
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findRoleByAccountId(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findRoleByAccountId,
    operation?: string
  ): Promise<Response<Array<RoleResponse>>> {
    return commonRequestApi<Array<RoleResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询当前账户的角色权限 仅供个人账户使用
   * @param unitId 单位id 单位id未传则查询当前账户下的所有角色权限
   * @return
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findRoleByAccountIdOrUnitIdForPersonAccount(
    unitId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findRoleByAccountIdOrUnitIdForPersonAccount,
    operation?: string
  ): Promise<Response<Array<RoleResponseForPersonAccount>>> {
    return commonRequestApi<Array<RoleResponseForPersonAccount>>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据业务域的单位ID查询角色
   * @return {@link RoleResponse}
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findRoleByBusinessDomainUnitId(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findRoleByBusinessDomainUnitId,
    operation?: string
  ): Promise<Response<Array<RoleResponse>>> {
    return commonRequestApi<Array<RoleResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 角色分类查询角色列表
   * @param category
   * @return
   * @param query 查询 graphql 语法文档
   * @param category 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findRoleByCategory(
    category: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findRoleByCategory,
    operation?: string
  ): Promise<Response<Array<RoleResponse>>> {
    return commonRequestApi<Array<RoleResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { category },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * id查询角色详情
   * @param roleId 角色id【必填】
   * @return 角色响应类
   * @param query 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findRoleById(
    roleId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findRoleById,
    operation?: string
  ): Promise<Response<RoleResponse>> {
    return commonRequestApi<RoleResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { roleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询所属方角色（包含内置角色）
   * @param request 查询所属方角色请求（包含内置角色）
   * @return 角色列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findRoleByOwner(
    request: FindRoleByOwnerRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findRoleByOwner,
    operation?: string
  ): Promise<Response<Array<RoleResponse>>> {
    return commonRequestApi<Array<RoleResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取当前登录用户服务商列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCurrentUserServicer(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCurrentUserServicer,
    operation?: string
  ): Promise<Response<GetCurrentUserServicerResponse>> {
    return commonRequestApi<GetCurrentUserServicerResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 管理员换绑学员手机号
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async ChangePhoneByAdmin(
    request: ChangePhoneByAdminRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.ChangePhoneByAdmin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重置网校管理员密码-兼容跨域问题
   * @param request 重置密码，密码由前端传入
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async ResetAdminPasswordWithResponse(
    request: ResetAdminPasswordRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.ResetAdminPasswordWithResponse,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重置密码 - 没有返回值作废
   * @param request 重置密码，密码由前端传入
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async ResetPassword(
    request: ResetPasswordRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.ResetPassword,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重置密码
   * @param request 重置密码，密码由前端传入
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async ResetPasswordWithResponse(
    request: ResetPasswordRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.ResetPasswordWithResponse,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 新增学员证书信息（单条）
   * @param mutate 查询 graphql 语法文档
   * @param createStudentCertificateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addStudentCertificateInfo(
    createStudentCertificateRequest: CreateStudentCertificateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.addStudentCertificateInfo,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createStudentCertificateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 绑定/换绑微信
   * 响应码
   * 200:成功
   * 400：错误的请求 通常是token错误或者入参错误
   * 60005:该openId已被其他账号绑定
   * 60004：当前用户已绑定微信开放平台
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyBindAndReBindWeChatOpenPlatform(
    request: JobSeekerBindPlatformAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyBindAndReBindWeChatOpenPlatform,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请绑定微信开放平台并返回账户ID登陆token
   * @param request {@link ApplyBindAndLoginRequest},{token,roleCategory,account,password,openId}
   * @return {code,message,token}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyBindWeChatOpenPlatformAndValidLogin(
    request: ApplyBindAndLoginRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyBindWeChatOpenPlatformAndValidLogin,
    operation?: string
  ): Promise<Response<TokenExtendResponse>> {
    return commonRequestApi<TokenExtendResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 当前登录用户绑定微信开放平台
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyBindWeChatOpenPlatformLoginAccount(
    request: ApplyBindWeChatOpenPlatformLoginAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyBindWeChatOpenPlatformLoginAccount,
    operation?: string
  ): Promise<Response<ApplyBindWeChatOpenPlatformLoginAccountResponse>> {
    return commonRequestApi<ApplyBindWeChatOpenPlatformLoginAccountResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请图形验证码
   * @param applyInfo 申请信息
   * @return 图形验证码信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyCaptcha(
    applyInfo: CaptchaApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCaptcha,
    operation?: string
  ): Promise<Response<CaptchaResponse>> {
    return commonRequestApi<CaptchaResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请切换用工企业Token列表
   * @return 切换用工企业Token列表
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyChangeEnterpriseTokenList(
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyChangeEnterpriseTokenList,
    operation?: string
  ): Promise<Response<Array<ChangeEnterpriseTokenResponse>>> {
    return commonRequestApi<Array<ChangeEnterpriseTokenResponse>>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请切换政策参与者关联单位Token列表
   * @return {@link ChangePolicyActorUnitAuthorizeTokenResponse}
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyChangePolicyActorUnitAuthorizeTokenList(
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyChangePolicyActorUnitAuthorizeTokenList,
    operation?: string
  ): Promise<Response<ChangePolicyActorUnitAuthorizeTokenResponse>> {
    return commonRequestApi<ChangePolicyActorUnitAuthorizeTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取登录帐户手机短信验证码
   * @param request 请求参数
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCurrentPhoneSmsCode(
    request: ApplyCurrentPhoneSmsRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCurrentPhoneSmsCode,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请用工企业Metadata
   * @param request 申请企业元数据请求
   * @return {name,code}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyEnterpriseMetadata(
    request: EnterpriseMetadataApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyEnterpriseMetadata,
    operation?: string
  ): Promise<Response<EnterpriseMetadataApplyResponse>> {
    return commonRequestApi<EnterpriseMetadataApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请微信开放平台账户ID登陆token
   * @param request {@link ApplyLoginByOpenIdRequest},{token,roleCategory,openId}
   * @return {code,message,token}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyLoginByOpenId(
    request: ApplyLoginByOpenIdRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyLoginByOpenId,
    operation?: string
  ): Promise<Response<TokenExtendResponse>> {
    return commonRequestApi<TokenExtendResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请获取H5授权登录openId-服务商
   * @param request {code,servicerId}
   * @return {code,message,openId}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyOpenIdByServicerId(
    request: ApplyOpenIdRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyOpenIdByServicerId,
    operation?: string
  ): Promise<Response<ApplyOpenIdResponse>> {
    return commonRequestApi<ApplyOpenIdResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请获取web扫码登录openId-服务商
   * @param request {code,servicerId}
   * @return {code,message,openId}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyScanCodeOpenIdByServicerId(
    request: ApplyOpenIdRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyScanCodeOpenIdByServicerId,
    operation?: string
  ): Promise<Response<ApplyOpenIdResponse>> {
    return commonRequestApi<ApplyOpenIdResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请安全性高的验证码
   * @param applyInfo 申请信息
   * @return 图形验证码信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applySecureCaptcha(
    applyInfo: SecureCaptchaApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applySecureCaptcha,
    operation?: string
  ): Promise<Response<SecureCaptchaResponse>> {
    return commonRequestApi<SecureCaptchaResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请手机短信验证码
   * @param applyInfo 申请信息
   * @return 手机短信验证码信息
   * 异常状态码
   * 400:token无效
   * 409:短信验证码获取次数超过限制
   * 410:短信验证码发送异常
   * 509:未绑定手机号
   * 514:token中未携带手机号
   * 515:不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)
   * 701:已经处理,请勿重复提交
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applySmsCode(
    applyInfo: SmsCodeApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applySmsCode,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请手机短信验证码-----换绑手机号使用
   * @param applyInfo 申请信息
   * @return 手机短信验证码信息
   * 异常状态码
   * 400:token无效
   * 409:短信验证码获取次数超过限制
   * 410:短信验证码发送异常
   * 509:未绑定手机号
   * 514:token中未携带手机号
   * 515:不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)
   * 701:已经处理,请勿重复提交
   * 702:用户不存在手机号,无法换绑
   * 703:业务类型有误或用户未登入
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applySmsCodeForChangingPhone(
    applyInfo: SmsCodeForChangingPhoneApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applySmsCodeForChangingPhone,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请手机短信验证码【未登录使用】
   * 原因：由于网关插件改版，登录后的分销商取的servicerProvider中的服务商id为分销商id，发送短信时，取的是对应网校服务商id的模板
   * @param applyInfo 申请信息
   * @return 手机短信验证码信息
   * 异常状态码
   * 400:token无效
   * 409:短信验证码获取次数超过限制
   * 410:短信验证码发送异常
   * 509:未绑定手机号
   * 514:token中未携带手机号
   * 515:不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)
   * 701:已经处理,请勿重复提交
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applySmsCodeForLoginDistributor(
    applyInfo: SmsCodeApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applySmsCodeForLoginDistributor,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请手机短信验证码【已登录使用】
   * 原因：由于网关插件改版，登录后的分销商取的servicerProvider中的服务商id为分销商id，发送短信时，取的是对应网校服务商id的模板
   * @param applyInfo 申请信息
   * @return 手机短信验证码信息
   * 异常状态码
   * 400:token无效
   * 409:短信验证码获取次数超过限制
   * 410:短信验证码发送异常
   * 509:未绑定手机号
   * 514:token中未携带手机号
   * 515:不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)
   * 701:已经处理,请勿重复提交
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applySmsCodeForLoginDistributorNeedLogin(
    applyInfo: SmsCodeApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applySmsCodeForLoginDistributorNeedLogin,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请校验人脸核身认证V1 (目标账户为单账户)
   * @param request 申请校验人脸核身认证请求
   * @return 登陆token
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyValidFacialRecognitionV1(
    request: ApplyValidFacialRecognitionV1Request,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyValidFacialRecognitionV1,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请校验人脸核身认证V1 WEB端
   * @param request 申请校验人脸核身认证V1 Web端请求
   * @return {code,message}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyValidFacialRecognitionV1Web(
    request: ApplyValidFacialRecognitionV1WebRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyValidFacialRecognitionV1Web,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请校验人脸核身认证结果(目标账户为单账户)
   * @param request 申请校验人脸核身认证结果请求
   * @return 登陆token
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyValidFacialRecognitionVerifyResult(
    request: ApplyValidFacialRecognitionVerifyResultRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyValidFacialRecognitionVerifyResult,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请校验人脸核身认证结果(目标账户为单账户)并绑定微信开放平台
   * 1.人脸结果二次核验(可选)
   * 2.身份证、姓名验证
   * 3.验证openId对应账户与身份证对应账户是否相同
   * 4.若openId不存在对应账户且身份证账户未绑定微信平台则进行绑定操作
   * @param request {token,idCardNumber,name,verifyResult,openId}
   * @return 登陆token
   * 状态码说明：
   * 505:账户不存在
   * 506:可用账户不唯一
   * 701:账户已绑定openId，但与当前授权进入的微信用户不是同一个人
   * 702:openId关联的账户与当前身份证对应的账户不是同一个
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyValidFacialRecognitionVerifyResultAndBindWeChatOpenPlatform(
    request: ApplyValidFacialRecognitionVerifyResultExtendRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyValidFacialRecognitionVerifyResultAndBindWeChatOpenPlatform,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 申请校验人脸核身认证结果(目标账户为多账户) - 移除
   * @param request 申请校验人脸核身认证结果请求
   * @return {code,切换的token列表}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyValidFacialRecognitionVerifyResultTokenList(
    request: ApplyValidFacialRecognitionVerifyResultRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyValidFacialRecognitionVerifyResultTokenList,
    operation?: string
  ): Promise<Response<ChangePolicyActorUnitAuthorizeTokenResponse>> {
    return commonRequestApi<ChangePolicyActorUnitAuthorizeTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 当前登录用户绑定微信开发平台
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async bindLoginAccountOpenPlatform(
    request: BindLoginAccountOpenPlatformRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.bindLoginAccountOpenPlatform,
    operation?: string
  ): Promise<Response<BindLoginAccountOpenPlatformResponse>> {
    return commonRequestApi<BindLoginAccountOpenPlatformResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 【服务商】给当前登陆账户绑定手机号 - 免登录
   * @param
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async bindPhoneForCurrentAccount(
    request: BindPhoneForCurrentAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.bindPhoneForCurrentAccount,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 给当前登陆用户绑定手机号
   * @param token token
   * @return {code,message}
   * @param mutate 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async bindPhoneForCurrentUser(
    token: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.bindPhoneForCurrentUser,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { token },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 管理中心 - 集体管理员个人帐号管理 - 换绑手机
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changeCollectiveRegisterPhone(
    request: CollectiveRegisterChangePhoneRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.changeCollectiveRegisterPhone,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 【服务商】换绑手机号
   * @param newPhoneToken 新手机号token
   * @param mutate 查询 graphql 语法文档
   * @param newPhoneToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changeNewPhone(
    newPhoneToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.changeNewPhone,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newPhoneToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 【服务商】换绑手机号
   * 用于分销商
   * @param newPhoneToken 新手机号token
   * @param mutate 查询 graphql 语法文档
   * @param newPhoneToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changeNewPhoneForFxs(
    newPhoneToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.changeNewPhoneForFxs,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newPhoneToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 【服务商】换绑手机号
   * @param newPhoneToken 新手机号token
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param newPhoneToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changeNewPhoneWithResponse(
    newPhoneToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.changeNewPhoneWithResponse,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newPhoneToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 变更当前登录帐户的密码 - 无返回值已废弃
   * @param changeInfo 更新信息
   * @param mutate 查询 graphql 语法文档
   * @param changeInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changePasswordByCurrent(
    changeInfo: CurrentAccountChangePasswordRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.changePasswordByCurrent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { changeInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 变更当前登录帐户的密码
   * @param changeInfo
   * @param mutate 查询 graphql 语法文档
   * @param changeInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changePasswordByCurrentWithResponse(
    changeInfo: CurrentAccountChangePasswordRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.changePasswordByCurrentWithResponse,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { changeInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 登陆时强制修改初始密码
   * @param changeInfo 入参新密码
   * @param mutate 查询 graphql 语法文档
   * @param changeInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changePasswordByForceModifyInitPassword(
    changeInfo: ForceModifyInitPasswordRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.changePasswordByForceModifyInitPassword,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { changeInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 换绑手机号
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changePhone(
    applyInfo: ChangePhoneRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.changePhone,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param unitName 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkExistUnitByName(
    unitName: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.checkExistUnitByName,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { unitName },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkOnlineAdminAuthentication(
    params: {
      identity?: string
      applicationMemberType?: number
      currentUserId?: string
      categoryTypeList?: Array<number>
      identityType?: number
      serviceId?: string
    },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.checkOnlineAdminAuthentication,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkUserAuthentication(
    params: {
      identity?: string
      applicationMemberType?: number
      currentUserId?: string
      categoryTypeList?: Array<number>
      identityType?: number
    },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.checkUserAuthentication,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建管理员账户
   * @param request 请求信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createAdministratorAccount(
    request: CreateAdministratorAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createAdministratorAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建用工企业
   * @param createInfo 创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createEnterprise(
    createInfo: EnterpriseCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createEnterprise,
    operation?: string
  ): Promise<Response<ChangeEnterpriseTokenResponse>> {
    return commonRequestApi<ChangeEnterpriseTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建用工企业经办
   * @param createInfo 创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createEnterpriseManager(
    createInfo: EnterpriseManagerCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createEnterpriseManager,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建人社管理员
   * @param createInfo 创建信息
   * 手机号已存在-100001
   * 身份证已存在-100002
   * 登录名称已存在-100003
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createMOHRSSAdmin(
    createInfo: MOHRSSAdminCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createMOHRSSAdmin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建下属人社管理员
   * @param createInfo 创建信息
   * 手机号已存在-100001
   * 身份证已存在-100002
   * 登录名称已存在-100003
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createMOHRSSSubordinateAdmin(
    createInfo: MOHRSSSubordinateAdminCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createMOHRSSSubordinateAdmin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 当前登录人为分销平台学员
   * 创建网校学员帐户并绑定成员资格领域
   * 200 成功
   * 402 核验异常
   * 400 token解析异常
   * 408 验证码过期，请重新获取验证码
   * 500 验证码错误，请重新输入
   * 100002 身份证已存在
   * 100001 手机号已存在
   * 200001 源账户已被其他账户绑定
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOnlineSchoolStudent(
    request: CreateOnlineSchoolStudentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createOnlineSchoolStudent,
    operation?: string
  ): Promise<Response<RegisterStudentResponse>> {
    return commonRequestApi<RegisterStudentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建网校子管理员
   * @param request 创建网校子管理员请求
   * @return {code,message}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOnlineSchoolSubAdmin(
    request: CreateOnlineSchoolSubAdminRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createOnlineSchoolSubAdmin,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建网校子管理员（使用初始token）
   * @param request {token,...}
   * @return {code,message}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOnlineSchoolSubAdminByToken(
    request: CreateOnlineSchoolSubAdminByTokenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createOnlineSchoolSubAdminByToken,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建角色请求
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createRole(
    request: CreateRoleRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createRole,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createRoleByAdminType(
    request: CreateRoleByAdminTypeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createRoleByAdminType,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建角色请求(新)
   * @param request 创建角色请求
   * @return 角色ID
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createRoleNew(
    request: CreateRoleRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createRoleNew,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建服务提供商账户
   * @param request 请求信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createServiceProviderAccount(
    request: CreateServiceProviderAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createServiceProviderAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 新增子项目管理内置接口
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createSubProjectAdministrator(
    request: CreateSubProjectAdministratorRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createSubProjectAdministrator,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建内置角色请求
   * @param request JSON.toJSONString(CreateRoleRequestCommand... command)
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createSystemInternalRole(
    request: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createSystemInternalRole,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建内置角色
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createSystemInternalRoles(
    request: CreateSystemInternalRoleRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createSystemInternalRoles,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 删除学员证书信息（单条）
   * @param mutate 查询 graphql 语法文档
   * @param deleteStudentCertificateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteCertificateInfo(
    deleteStudentCertificateRequest: DeleteStudentCertificateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteCertificateInfo,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { deleteStudentCertificateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 禁用用工企业经办
   * @param accountId 帐户ID
   * @param mutate 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableEnterpriseManager(
    accountId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableEnterpriseManager,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用用工企业经办
   * @param accountId 帐户ID
   * @param mutate 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableEnterpriseManager(
    accountId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableEnterpriseManager,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 依据认证标识判断是否已经存在学员
   * 509:未绑定手机
   * 402:identityType、identity为空，格式不正确
   * 512：手机号已存在
   * @param request 入参
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async existUser(
    request: ExistUserRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.existUser,
    operation?: string
  ): Promise<Response<ExistUserResponse>> {
    return commonRequestApi<ExistUserResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 修改当前账户密码(用户端忘记密码)
   * @param request 修改当前账户密码请求
   * @return Token响应对象
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async forgetPassword(
    request: CurrentAccountChangePasswordCauseForgetRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.forgetPassword,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 冻结帐户
   * @param accountId 【必填】帐户ID
   * @param mutate 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async freezeAccount(
    accountId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.freezeAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 立即重置密码
   * @param resetInfo 重置密码信息
   * @param mutate 查询 graphql 语法文档
   * @param resetInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async immediateResetPassword(
    resetInfo: ImmediateResetPasswordRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.immediateResetPassword,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { resetInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 初始化单位树
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async initUnitTree(
    request: UnitCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.initUnitTree,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 登录并绑定平台账号【微信】
   * @param request {identity,password,captcha,token,loginToken}
   * @return {code,message,token}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async loginAndBindOpenPlatform(
    request: BindPlatformAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.loginAndBindOpenPlatform,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 登录并绑定平台账号【微信】（2024.02.20 ·新）
   * @param request {identity,password,captcha,token,loginToken}
   * @return {code,message,token}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async loginAndBindOpenPlatformV2(
    request: BindPlatformAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.loginAndBindOpenPlatformV2,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 集体报名管理员 注册
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async registerCollectiveRegisterAdmin(
    request: RegisterCollectiveRegisterAdminRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.registerCollectiveRegisterAdmin,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 通过闽政通注册用工企业经办
   * @param registerInfo 注册信息
   * @param mutate 查询 graphql 语法文档
   * @param registerInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async registerEnterPriseManagerForMZT(
    registerInfo: EnterpriseManagerRegisterForMztRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.registerEnterPriseManagerForMZT,
    operation?: string
  ): Promise<Response<EnterpriseManagerRegisterResponse>> {
    return commonRequestApi<EnterpriseManagerRegisterResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { registerInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建企业账户
   * @param request 请求信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async registerEnterpriseAccount(
    request: RegisterEnterpriseAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.registerEnterpriseAccount,
    operation?: string
  ): Promise<Response<EnterpriseAccountRegisterResponse>> {
    return commonRequestApi<EnterpriseAccountRegisterResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 注册用工企业经办
   * @param registerInfo 注册信息
   * @param mutate 查询 graphql 语法文档
   * @param registerInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async registerEnterpriseManager(
    registerInfo: EnterpriseManagerRegisterRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.registerEnterpriseManager,
    operation?: string
  ): Promise<Response<EnterpriseManagerRegisterResponse>> {
    return commonRequestApi<EnterpriseManagerRegisterResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { registerInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 注册学员账号
   * @param createInfo 注册学员账号信息 {输入的短信验证码、输入手机号、输入的图片验证码、token(申请短信返回的token)}
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async registerStudent(
    createInfo: CreateStudentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.registerStudent,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 注册学员账号（2024.02.20 ·新）
   * @param createInfo 注册学员账号信息 {输入的短信验证码、输入手机号、输入的图片验证码、token(申请短信返回的token)}
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async registerStudentV2(
    createInfo: CreateStudentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.registerStudentV2,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeRoleWithAdminTypeById(
    roleId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeRoleWithAdminTypeById,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { roleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除角色(没有角色账户授权关系则删除)
   * @param roleId 角色id
   * @param mutate 查询 graphql 语法文档
   * @param roleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeRoleWithNoAuthRelById(
    roleId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeRoleWithNoAuthRelById,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { roleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除内置角色请求
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async removeSystemInternalRole(
    request: RemoveSystemInternalRoleRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeSystemInternalRole,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 恢复冻结的帐户
   * @param accountId 【必填】帐户ID
   * @param mutate 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resumeAccount(
    accountId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.resumeAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { accountId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 解绑手机号
   * @param request 解绑请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unbindPhone(
    request: UnbindPhoneRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.unbindPhone,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 解绑手机号(当前登陆用户)
   * @param request 解绑请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unbindPhoneForCurrentUser(
    request: UnbindPhoneForCurrentUserRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.unbindPhoneForCurrentUser,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 解绑用户微信开放平台
   * @param userId 用户ID
   * @param mutate 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unbindWeChatOpenPlatform(
    userId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.unbindWeChatOpenPlatform,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { userId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改管理员账户
   * @param request 请求信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateAdministratorAccount(
    request: UpdateAdministratorAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateAdministratorAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 管理中心 - 集体管理员个人帐号管理 - 更新个人资料
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCollectiveRegister(
    request: UpdateCollectiveRegisterRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateCollectiveRegister,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 管理域 -培训管理 - 集体报名咨询 - 修改登录账号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCollectiveRegisterAccountInfo(
    request: UpdateCollectiveRegisterAccountInfoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateCollectiveRegisterAccountInfo,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新用工企业
   * @param updateInfo 更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateEnterprise(
    updateInfo: EnterpriseUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateEnterprise,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新用工企业经办
   * @param updateInfo 更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateEnterpriseManager(
    updateInfo: EnterpriseManagerUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateEnterpriseManager,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新人社管理员信息
   * @param updateInfo 更新信息
   * 手机号已存在-100001
   * 身份证已存在-100002
   * 登录名称已存在-100003
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateMOHRSSAdmin(
    updateInfo: MOHRSSAdminUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateMOHRSSAdmin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新下属人社管理员信息
   * @param updateInfo 更新信息
   * 手机号已存在-100001
   * 身份证已存在-100002
   * 登录名称已存在-100003
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateMOHRSSSubordinateAdmin(
    updateInfo: MOHRSSSubordinateAdminUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateMOHRSSSubordinateAdmin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 运营域 修改网校管理员账户
   * @param request 请求信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateOnlineAdministratorAccount(
    request: UpdateOnlineAdministratorAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOnlineAdministratorAccount,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改网校子管理员
   * @param request 创建网校子管理员请求
   * @return {code,message}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateOnlineSchoolSubAdmin(
    request: UpdateOnlineSchoolSubAdminRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOnlineSchoolSubAdmin,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改网校子管理员（使用初始token）
   * @param request {token,...}
   * @return {code,message}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateOnlineSchoolSubAdminByToken(
    request: UpdateOnlineSchoolSubAdminByTokenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOnlineSchoolSubAdminByToken,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改角色请求
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateRole(
    request: UpdateRoleRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateRole,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateRoleByAdminType(
    request: UpdateRoleByAdminTypeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateRoleByAdminType,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改服务提供商账户
   * @param request 请求信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateServiceProviderAccount(
    request: UpdateServiceProviderAccountRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateServiceProviderAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新学员账号信息（学员端）
   * @param updateInfo 更新学员账号信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateStudent(
    updateInfo: UpdateStudentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateStudent,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新当前登录学员的基础信息
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateStudentBasicInfo(
    request: UpdateStudentBasicInfoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateStudentBasicInfo,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新学员账号信息（管理端）
   * @param updateInfo 更新学员账号信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateStudentByAdmin(
    updateInfo: UpdateStudentSystemRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateStudentByAdmin,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新学员证书信息（单条）
   * @param mutate 查询 graphql 语法文档
   * @param updateStudentCertificateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateStudentCertificateInfo(
    updateStudentCertificateRequest: UpdateStudentCertificateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateStudentCertificateInfo,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateStudentCertificateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改内置角色请求
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async updateSystemInternalRole(
    request: UpdateSystemInternalRoleRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateSystemInternalRole,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 验证图形验证码
   * @param validInfo 验证信息
   * @return 验证结果
   * @param mutate 查询 graphql 语法文档
   * @param validInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async validCaptcha(
    validInfo: CaptchaValidRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validCaptcha,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { validInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 验证账号/手机号
   * @param validInfo 验证账号/手机号是否存在请求
   * @return 验证结果
   * @param mutate 查询 graphql 语法文档
   * @param validInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async validIdentity(
    validInfo: ValidIdentityRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validIdentity,
    operation?: string
  ): Promise<Response<IdentityValidResponse>> {
    return commonRequestApi<IdentityValidResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { validInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 验证账号/手机号
   * 用于分销商忘记密码验证
   * @param validInfo 验证账号/手机号是否存在请求
   * @return 验证结果
   * @param mutate 查询 graphql 语法文档
   * @param validInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async validIdentityForFxs(
    validInfo: ValidIdentityFxsRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validIdentityForFxs,
    operation?: string
  ): Promise<Response<IdentityValidResponse>> {
    return commonRequestApi<IdentityValidResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { validInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 验证账号/手机号
   * 分销平台中，一个账户存在多种不同应用方类型的角色时，会查询出重复的账户信息（只有角色信息不一致）
   * 添加新口去除重复账户id
   * @param validInfo 验证账号/手机号是否存在请求
   * @return 验证结果
   * @param mutate 查询 graphql 语法文档
   * @param validInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async validIdentityRemoveDuplicates(
    validInfo: ValidIdentityRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validIdentityRemoveDuplicates,
    operation?: string
  ): Promise<Response<IdentityValidResponse>> {
    return commonRequestApi<IdentityValidResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { validInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 校验是否绑定微信开放平台
   * @param request {@link ValidIsBindWeChatOpenPlatformRequest},{token,roleCategory,openId}
   * @return {code,message,isBind[true:false]}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async validIsBindWeChatOpenPlatform(
    request: ValidIsBindWeChatOpenPlatformRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validIsBindWeChatOpenPlatform,
    operation?: string
  ): Promise<Response<ValidIsBindOpenPlatformResponse>> {
    return commonRequestApi<ValidIsBindOpenPlatformResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 验证手机短信验证码
   * @param validInfo 验证信息
   * @return 验证结果
   * @param mutate 查询 graphql 语法文档
   * @param validInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async validSmsCode(
    validInfo: SmsCodeValidRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validSmsCode,
    operation?: string
  ): Promise<Response<TokenResponse>> {
    return commonRequestApi<TokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { validInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
