import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
import AssessSetting from '@api/service/common/scheme/model/schemeDto/common/course-training-outlines/assess-setting/AssessSetting'
import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
import { SchemeAssessSettingTypeEnum } from '@api/service/common/scheme/enum/SchemeAssessSettingType'
import TemplateNameManager from '@api/service/management/train-class/mutation/dto/TemplateNameManager'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
import IssueConfigureAssessSetting from '@api/service/common/scheme/model/schemeDto/issue-configures/assess-setting/IssueConfigureAssessSetting'
import {
  IssueAssessTypeEnum,
  QuestionnaireAssessTypeEnum,
  TeachPlanAssessTypeEnum
} from '@api/service/common/scheme/enum/AssessType'
import IssueConfigure from '@api/service/common/scheme/model/schemeDto/issue-configures/IssueConfigure'
import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
import { QuestionnaireStatusEnum } from '@api/service/common/scheme/enum/QuestionnaireStatus'
import { QuestionnairePreconditionTypeEnum } from '@api/service/common/scheme/enum/QuestionnairePreconditionType'
import { OperationEnum } from '@api/service/management/train-class/mutation/Enum/OperationEnum'
import OutlineRequire from '@api/service/common/scheme/model/schemeDto/common/outline-requires/OutlineRequire'
import QuestionnaireLearningPrecondition from '@api/service/common/scheme/model/schemeDto/common/questionnaire-learning/precondition/QuestionnaireLearningPrecondition'
import ExtendProperty from '@api/service/common/scheme/model/schemeDto/common/extend-properties/ExtendProperty'
import TrainClassConfigJsonManager from '@api/service/management/train-class/Utils/TrainClassConfigJsonManager'
import { QuestionnaireAppliedRangeTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
import QuestionnaireLearningAssessSetting from '@api/service/common/scheme/model/schemeDto/common/questionnaire-learning/assess-setting/QuestionnaireLearningAssessSetting'
import AssessSettingInfo from '@api/service/common/scheme/model/AssessSettingInfo'
import QuestionnaireLearning from '@api/service/common/scheme/model/schemeDto/common/questionnaire-learning/QuestionnaireLearning'
import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
import QuestionnaireLearningConfig from '@api/service/common/scheme/model/schemeDto/common/questionnaire-learning/config/QuestionnaireLearningConfig'
import Address from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/address/Address'
import TeachingPlanItem from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/teaching-plan-items-groups/teaching-plan-items/TeachingPlanItem'
import TeachingPlanItemTeacher from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/teaching-plan-items-groups/teaching-plan-items/teachers/TeachingPlanItemTeacher'
import TeachPlanLearning from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/TeachPlanLearning'
import TeachPlanLearningConfig from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/TeachPlanLearningConfig'
import moment from 'moment'
import TeachPlanLearningAssessSetting from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/assess-setting/TeachPlanLearningAssessSetting'
import DataResolve from '@api/service/common/utils/DataResolve'
import IssueTrainingConfigConfigure from '@api/service/common/scheme/model/schemeDto/issue-configures/training-config-configure/IssueTrainingConfigConfigure'
import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
import { cloneDeep } from 'lodash'
import LearningResult from '@api/service/common/scheme/model/schemeDto/common/learning-results/LearningResult'
import { IssueTrainingDateTypeEnum } from '@api/service/common/scheme/enum/IssueTrainingDateType'
import TeachingPlanItemsGroup from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/teaching-plan-items-groups/TeachingPlanItemsGroup'
import issueCourseDetail from '@api/service/common/scheme/model/IssueCourseDetail'
import IssueCourseDetail from '@api/service/common/scheme/model/IssueCourseDetail'
import TeachPlanLearningAssessSettingLearningResult from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/assess-setting/learning-results/TeachPlanLearningAssessSettingLearningResult'

/**
 * Json工具类
 */
class OfflinePartJsonUtil {
  /**
   * 生成id
   * @param prefix  前缀
   * @param counter 计数器
   * @param digits  位数
   * @private
   */
  private generateId(prefix: string, counter: number, digits: number): string {
    const id = prefix + TrainClassConfigJsonManager.pad(counter, digits)
    return id
  }

  /**
   * 比较期别信息
   * @param type 类型，issue：期别，questionnaire：问卷
   * @param createCommodity 创建方案模型
   * @param learningTypeModelCopy 备份方案模型
   */
  compareIssue(type: string, createCommodity: MutationCreateTrainClassCommodity, learningTypeModelCopy: LearningType) {
    // 处理期别
    const issue = createCommodity.learningTypeModel.issue
    const issueConfigList = issue.issueConfigList
    const issueCopy = learningTypeModelCopy.issue
    const issueCopyConfigList = issueCopy.issueConfigList
    if (type === 'issue') {
      issueCopyConfigList.forEach((issueItem) => {
        const issueFindIdx = issueConfigList.findIndex((el) => el.id === issueItem.id)
        if (issueFindIdx > -1) {
          // 找到该项，则该项是修改，需要处理教学计划
          const targetIssue = issueConfigList[issueFindIdx]
          const courseList = targetIssue.issueCourseList
          const courseCopyList = issueItem.issueCourseList
          courseCopyList.forEach((courseItem) => {
            const courseFindIdx = courseList.findIndex((el) => el.id === courseItem.id)
            if (courseFindIdx > -1) {
              // 找到该项课程，则该项是修改，不处理
            } else {
              const removeCourseItem = cloneDeep(courseItem)
              removeCourseItem.operationType = OperationTypeEnum.remove
              courseList.push(removeCourseItem)
            }
          })
        } else {
          // 找不到该项，则该项是删除，需要从备份中向列表中添加且标记删除
          const removeItem = cloneDeep(issueItem)
          removeItem.operationType = OperationTypeEnum.remove
          removeItem.issueCourseList.forEach((courseItm: any) => {
            courseItm.operationType = OperationTypeEnum.remove
          })
          issueConfigList.push(removeItem)
        }
      })
    }
  }

  /**
   * 比较问卷信息
   * @param type 类型，issue：期别，questionnaire：问卷
   * @param createCommodity 创建方案模型
   * @param learningTypeModelCopy 备份方案模型
   */
  compareQuestionnaire(
    type: string,
    createCommodity: MutationCreateTrainClassCommodity,
    learningTypeModelCopy: LearningType
  ) {
    // 处理问卷
    const questionnaire = createCommodity.learningTypeModel.questionnaire
    const questionnaireConfigList = questionnaire.questionnaireConfigList
    const questionnaireCopy = learningTypeModelCopy.questionnaire
    const questionnaireCopyConfigList = questionnaireCopy.questionnaireConfigList
    if (type === 'questionnaire') {
      questionnaireCopyConfigList.forEach((questionnaire) => {
        const findIdx = questionnaireConfigList.findIndex((el) => el.id === questionnaire.id)
        if (findIdx > -1) {
          // 找到该项，则不处理
        } else {
          const removeItem = cloneDeep(questionnaire)
          removeItem.operationType = OperationTypeEnum.remove
          questionnaireConfigList.push(removeItem)
        }
      })
    }
  }

  /**
   * 转换面网授班新增配置
   * @param jsonObj 方案json对象
   * @param createCommodity 创建方案模型
   */
  convertOfflinePart(
    jsonObj: Scheme,
    createCommodity: MutationCreateTrainClassCommodity,
    learningTypeModelCopy?: LearningType
  ) {
    const isUpdate = !!learningTypeModelCopy
    // 期别配置&调研问卷
    this.convertIssueAndQuestionnaire(jsonObj, createCommodity)

    const issue = createCommodity.learningTypeModel.issue
    // 培训形式
    const trainingMode = createCommodity.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId
    // 培训类型
    ;(jsonObj as Scheme).trainingType = TrainingMode.getTrainingTypeByTrainingMode(trainingMode)

    // 考核对象类型
    const schemeAssessSettingType = issue.isSelected
      ? SchemeAssessSettingTypeEnum.arr
      : SchemeAssessSettingTypeEnum.object

    // 配置学习成果
    if (schemeAssessSettingType === SchemeAssessSettingTypeEnum.object) {
      // 对象类型考核项
      jsonObj.assessSetting = new AssessSetting()
      const jsonObjAssessSetting = jsonObj.assessSetting
      jsonObjAssessSetting.id = createCommodity.trainClassBaseInfo.assessSettingId
        ? createCommodity.trainClassBaseInfo.assessSettingId
        : TemplateNameManager.schemeAssessSettingId
      jsonObjAssessSetting.name = createCommodity.trainClassBaseInfo.assessSettingName
        ? createCommodity.trainClassBaseInfo.assessSettingName
        : TemplateNameManager.schemeAssessSettingName
      const learningTypeModel = createCommodity.learningTypeModel
      jsonObjAssessSetting.relateAssessIds = []
      if (learningTypeModel.courseLearning.isSelected) {
        const learningKey =
          TrainClassConfigJsonManager.schemeType == 1 ? 'chooseCourseLearning' : 'autonomousCourseLearning'
        if (!learningTypeModelCopy || jsonObj[learningKey].config.operation !== 3)
          jsonObjAssessSetting.relateAssessIds.push(jsonObj[learningKey].assessSetting.id)
      }
      if (
        learningTypeModel.learningExperience.isSelected &&
        (!learningTypeModelCopy || jsonObj.learningExperienceLearning.config.operation !== 3) &&
        learningTypeModel.learningExperience.isExamine
      ) {
        jsonObjAssessSetting.relateAssessIds.push(jsonObj.learningExperienceLearning.assessSetting.id)
      }
      // 考试纳入考核
      if (
        learningTypeModel.exam.isSelected &&
        (!learningTypeModelCopy || jsonObj.examLearning.config.operation !== 3) &&
        learningTypeModel.exam.isExamAssessed
      ) {
        jsonObjAssessSetting.relateAssessIds.push(jsonObj.examLearning.assessSetting.id)
      }
      // 心得
      // todo
      jsonObjAssessSetting.learningResults = []
      if (createCommodity.trainClassBaseInfo.period) {
        jsonObjAssessSetting.learningResults.push({
          id: createCommodity.trainClassBaseInfo.creditId
            ? createCommodity.trainClassBaseInfo.creditId
            : TemplateNameManager.schemeAssessSettingLearningResultsCreditId,
          gradeType: 'CREDIT',
          grade: createCommodity.trainClassBaseInfo.period,
          type: 1
        })
      }
      if (createCommodity.trainClassBaseInfo.hasLearningResult) {
        jsonObjAssessSetting.learningResults.push({
          id: createCommodity.trainClassBaseInfo.learningResultAchievementsId
            ? createCommodity.trainClassBaseInfo.learningResultAchievementsId
            : TemplateNameManager.schemeAssessSettingLearningResultsTemplateId,
          provideCert: true,
          openPrintTemplate: createCommodity.trainClassBaseInfo.openPrintTemplate,
          certificateTemplateId: createCommodity.trainClassBaseInfo.learningResultId,
          type: 2
        })
      }
      if (!createCommodity.trainClassBaseInfo.hasLearningResult && isUpdate && createCommodity?.certificate?.id) {
        // 删除标记
        jsonObjAssessSetting.learningResults.push({
          id: createCommodity.certificate.id,
          provideCert: createCommodity.certificate.provideCert,
          openPrintTemplate: createCommodity.certificate.openPrintTemplate,
          certificateTemplateId: createCommodity.certificate.certificateTemplateId,
          type: 2,
          operation: 3
        })
      }
    }
    if (schemeAssessSettingType === SchemeAssessSettingTypeEnum.arr) {
      // 数组类型考核项
      jsonObj.assessSetting = [] as AssessSetting[]
      // 方案级别考核方式
      const schemeAssessItem = new AssessSetting()
      schemeAssessItem.id = createCommodity.trainClassBaseInfo.assessSettingId
        ? createCommodity.trainClassBaseInfo.assessSettingId
        : this.generateId(TemplateNameManager.assessSettingId, TrainClassConfigJsonManager.loopCount.assessIdSCASS, 3)
      TrainClassConfigJsonManager.loopCount.assessIdSCASS++
      schemeAssessItem.name = createCommodity.trainClassBaseInfo.assessSettingName
        ? createCommodity.trainClassBaseInfo.assessSettingName
        : TemplateNameManager.schemeAssessSettingName
      const learningTypeModel = createCommodity.learningTypeModel
      schemeAssessItem.relateAssessIds = []
      if (learningTypeModel.courseLearning.isSelected) {
        const learningKey =
          TrainClassConfigJsonManager.schemeType == 1 ? 'chooseCourseLearning' : 'autonomousCourseLearning'
        if (!learningTypeModelCopy || jsonObj[learningKey].config.operation !== 3)
          schemeAssessItem.relateAssessIds.push(jsonObj[learningKey].assessSetting.id)
      }
      if (
        learningTypeModel.learningExperience.isSelected &&
        (!learningTypeModelCopy || jsonObj.learningExperienceLearning.config.operation !== 3) &&
        learningTypeModel.learningExperience.isExamine
      ) {
        schemeAssessItem.relateAssessIds.push(jsonObj.learningExperienceLearning.assessSetting.id)
      }
      // 考试纳入考核
      if (
        learningTypeModel.exam.isSelected &&
        (!learningTypeModelCopy || jsonObj.examLearning.config.operation !== 3) &&
        learningTypeModel.exam.isExamAssessed
      ) {
        schemeAssessItem.relateAssessIds.push(jsonObj.examLearning.assessSetting.id)
      }
      schemeAssessItem.learningResults = []
      if (createCommodity.trainClassBaseInfo.period) {
        schemeAssessItem.learningResults.push({
          id: createCommodity.trainClassBaseInfo.creditId
            ? createCommodity.trainClassBaseInfo.creditId
            : TemplateNameManager.schemeAssessSettingLearningResultsCreditId,
          gradeType: 'CREDIT',
          grade: createCommodity.trainClassBaseInfo.period,
          type: 1
        })
      }
      if (createCommodity.trainClassBaseInfo.hasLearningResult) {
        schemeAssessItem.learningResults.push({
          id: createCommodity.trainClassBaseInfo.learningResultAchievementsId
            ? createCommodity.trainClassBaseInfo.learningResultAchievementsId
            : TemplateNameManager.schemeAssessSettingLearningResultsTemplateId,
          provideCert: true,
          openPrintTemplate: createCommodity.trainClassBaseInfo.openPrintTemplate,
          certificateTemplateId: createCommodity.trainClassBaseInfo.learningResultId,
          type: 2
        })
      }
      if (!createCommodity.trainClassBaseInfo.hasLearningResult && isUpdate && createCommodity?.certificate?.id) {
        // 删除标记
        schemeAssessItem.learningResults.push({
          id: createCommodity.certificate.id,
          provideCert: createCommodity.certificate.provideCert,
          openPrintTemplate: createCommodity.certificate.openPrintTemplate,
          certificateTemplateId: createCommodity.certificate.certificateTemplateId,
          type: 2,
          operation: 3
        })
      }
      jsonObj.assessSetting.push(schemeAssessItem)

      const containOfflineTrainingModes = [TrainingModeEnum.mixed, TrainingModeEnum.offline]
      const isContainOfflineTrainingMode = containOfflineTrainingModes.includes(
        createCommodity.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId
      )
      // 处理期别的考核项
      if (issue.isSelected && isContainOfflineTrainingMode) {
        const issueAssessItem = new AssessSetting()
        issueAssessItem.id = issue.assessId
          ? issue.assessId
          : this.generateId(TemplateNameManager.assessSettingId, TrainClassConfigJsonManager.loopCount.assessIdSCASS, 3)
        TrainClassConfigJsonManager.loopCount.assessIdSCASS++
        issueAssessItem.name = issue.assessName ? issue.assessName : TemplateNameManager.notRootAssessAssessSettingName
        jsonObj.assessSetting.push(issueAssessItem)
        const { schemeAssessItem } = Scheme.parseTrainClassAssess(jsonObj)
        if (schemeAssessItem) {
          schemeAssessItem.relateAssessIds.push(issueAssessItem.id)
        }
      }
    }

    // 处理方案级别问卷考核
    this.convertSchemeQuestionnaireAssess(jsonObj, createCommodity)

    // 培训形式
    const skuProperty = createCommodity.trainClassBaseInfo.skuProperty
    if (skuProperty.trainingMode.skuPropertyValueId) {
      jsonObj.extendProperties.push({
        name: 'trainingWay',
        value: skuProperty.trainingMode.skuPropertyValueId
      })
    }
  }

  /**
   * 转换方案级别问卷考核
   * @param jsonObj json对象
   * @param createCommodity 课程商品对象
   */
  convertSchemeQuestionnaireAssess(jsonObj: Scheme, createCommodity: MutationCreateTrainClassCommodity) {
    const questionnaire = createCommodity.learningTypeModel.questionnaire
    if (questionnaire.isSelected) {
      const baseFilter: QuestionnaireLearning[] = jsonObj.questionnaireLearning.filter(
        (el: QuestionnaireLearning) => el.config.needAssessment && el.config.status === QuestionnaireStatusEnum.enabled
      )
      if (baseFilter.length) {
        const { schemeAssessItem: assessSetting } = Scheme.parseTrainClassAssess(jsonObj)
        if (!assessSetting.relateAssessIds) {
          assessSetting.relateAssessIds = []
        }
        const addItemFilter = baseFilter.filter((el) => el.config.operation !== OperationTypeEnum.remove)
        const removeItemFilter = baseFilter.filter((el) => el.config.operation === OperationTypeEnum.remove)
        if (addItemFilter.length) {
          let addAssessIds = [] as string[]
          addItemFilter.forEach((el) =>
            el.assessSetting.forEach((subEl) => {
              addAssessIds.push(subEl.id)
            })
          )
          addAssessIds = DataResolve.unique(addAssessIds)
          addAssessIds.forEach((assessId) => {
            const findIdx = assessSetting.relateAssessIds.findIndex((el) => el === assessId)
            if (findIdx > -1) {
              // 找到有效索引说明是已添加的，不操作
            } else {
              // 没有的项新增
              assessSetting.relateAssessIds.push(assessId)
            }
          })
        }
        if (removeItemFilter.length) {
          let removeAssessIds = [] as string[]
          removeItemFilter.forEach((el) => {
            el.assessSetting.forEach((subEl) => {
              removeAssessIds.push(subEl.id)
            })
          })
          removeAssessIds = DataResolve.unique(removeAssessIds)
          removeAssessIds.forEach((assessId) => {
            const findIdx = assessSetting.relateAssessIds.findIndex((el) => el === assessId)
            if (findIdx > -1) {
              // 找到有效索引说明是已添加的，删除
              assessSetting.relateAssessIds.splice(findIdx, 1)
            }
          })
        }
      }
    }
  }

  /**
   * 配置期别和问卷
   * @param obj json对象
   * @param createCommodity 方案
   */
  convertIssueAndQuestionnaire(obj: any, createCommodity: MutationCreateTrainClassCommodity) {
    const jsonObj = obj as Scheme
    const basInfo = createCommodity.trainClassBaseInfo
    const issue = createCommodity.learningTypeModel.issue
    const questionnaire = createCommodity.learningTypeModel.questionnaire

    jsonObj.issueConfigures = [] as IssueConfigure[]
    issue.issueConfigList.forEach((item) => {
      const opt = new IssueConfigure()
      opt.operation = item.operationType
      if (item.operationType === OperationTypeEnum.create) {
        // 新建
        opt.id = this.generateId(TemplateNameManager.issueConfigureId, TrainClassConfigJsonManager.loopCount.issueId, 3)
        TrainClassConfigJsonManager.loopCount.issueId++
      } else {
        // 修改&删除
        opt.id = item.id
      }
      opt.trainingPeriodType = item.issueTrainingDateType
      /** 教学计划学习方式，只有有课表时才有该学习方式 */

      // 根据具体场景分析是否要转换教学计划学习方式
      if (opt.operation === OperationTypeEnum.create) {
        if (opt.trainingPeriodType === IssueTrainingDateTypeEnum.by_issue_courses) {
          // 如果是新建期别 & 现在有期别课表，则新建教学计划学习方式
          this.convertTeachPlan(opt, item, OperationTypeEnum.create)
        }
      } else {
        const hasTeachPlanLearning = item.relateTeachPlanLearning.configId
        if (opt.operation === OperationTypeEnum.update) {
          if (opt.trainingPeriodType === IssueTrainingDateTypeEnum.by_issue_courses) {
            // 如果是修改期别 & 现在有期别课表，之前有教学计划学习方式，则修改教学计划学习方式；如果之前没有教学计划学习方式，则新建教学计划学习方式
            if (hasTeachPlanLearning) {
              this.convertTeachPlan(opt, item, OperationTypeEnum.update)
            } else {
              this.convertTeachPlan(opt, item, OperationTypeEnum.create)
            }
          }
          if (opt.trainingPeriodType === IssueTrainingDateTypeEnum.custom) {
            // 如果是修改期别 & 现在无期别课表，之前有教学计划学习方式，则删除教学计划学习方式；如果之前没有教学计划学习方式，则不处理
            if (hasTeachPlanLearning) {
              this.convertTeachPlan(opt, item, OperationTypeEnum.remove)
            }
          }
        }
        if (opt.operation === OperationTypeEnum.remove) {
          if (hasTeachPlanLearning) {
            // 如果是删除期别 & 之前有教学计划学习方式，不管现在是否有课表，则删除教学计划学习方式
            this.convertTeachPlan(opt, item, OperationTypeEnum.remove)
          }
        }
      }
      TrainClassConfigJsonManager.issueIdMap.set(item.id, opt.id)
      opt.learningSchemeId = basInfo.id ? basInfo.id : TemplateNameManager.schemeID
      opt.issueNum = item.issueNo

      opt.issueName = item.issueName
      opt.startReportTime = item.checkDateRange.startDate
      opt.endReportTime = item.checkDateRange.endDate
      opt.startSignUpTime = item.registerBeginTime
      opt.endSignUpTime = item.registerEndTime
      if (opt.trainingPeriodType === IssueTrainingDateTypeEnum.by_issue_courses) {
        opt.startTrainingTime = opt.teachPlanLearning.config.startTime
        opt.endTrainingTime = opt.teachPlanLearning.config.endTime
      }
      if (opt.trainingPeriodType === IssueTrainingDateTypeEnum.custom) {
        opt.startTrainingTime = item.trainingDateRange.formatStartDate
        opt.endTrainingTime = item.trainingDateRange.formatEndDate
      }

      opt.signUpNumRevealType = item.registeredNumDisplayType
      opt.allowSignUpNum = item.openRegistrationNum
      if (item.fixedRegisteredNum) {
        opt.fixedSignUpRevealNum = item.fixedRegisteredNum
      }
      opt.portalDisplay = item.isShowInPortal
      if (opt.portalDisplay) {
        opt.portalDisplayScope = item.visibleChannelList ? item.visibleChannelList : ([] as number[])
      } else {
        opt.portalDisplayScope = [] as number[]
      }
      opt.openSignUp = item.isEnableStudentEnroll
      opt.trainingNotice = item.notice
      // 培训配置信息
      opt.trainingConfigConfigure = new IssueTrainingConfigConfigure()
      opt.trainingConfigConfigure.id = item.trainingConfigConfigureId
        ? item.trainingConfigConfigureId
        : this.generateId(
            TemplateNameManager.issueConfigureTrainingConfigConfigureId,
            TrainClassConfigJsonManager.loopCount.configIdTCC,
            3
          )
      TrainClassConfigJsonManager.loopCount.configIdTCC++
      opt.trainingConfigConfigure.openAttendance = item.isOpenAttendance
      opt.trainingConfigConfigure.openReport = item.isOpenCheck
      opt.trainingConfigConfigure.minAttendanceRate = this.convertPercentageToDecimal(item.attendanceRate)
      opt.trainingConfigConfigure.openCompletionTest = item.isOpenGraduationTest
      opt.trainingConfigConfigure.openStayInfoCollect = item.isOpenAccommodationInfoCollect
      opt.trainingConfigConfigure.stayNotice = item.accommodationInfoCollectNotice
      opt.questionnaireLearning = [] as QuestionnaireLearning[]
      // 扩展属性
      opt.extendProperties = [] as ExtendProperty[]
      opt.extendProperties.push(
        {
          name: TemplateNameManager.teachingPlanHeadTeacherName,
          value: item.headTeacherName
        }, // 班主任姓名
        {
          name: TemplateNameManager.teachingPlanHeadTeacherPhone,
          value: item.headTeacherPhone
        }, // 班主任电话
        {
          name: TemplateNameManager.teachingPlanHotelLiaisonName,
          value: item.hotelContactsName
        }, // 酒店联系人姓名
        {
          name: TemplateNameManager.teachingPlanHotelLiaisonPhone,
          value: item.hotelContactsPhone
        }, // 酒店联系人电话
        {
          name: TemplateNameManager.teachingPlanAddressTrainingPointId,
          value: item.trainingPointId
        }
      )
      jsonObj.issueConfigures.push(opt)
    })
    // 调研问卷
    // 过滤方案级别问卷
    const schemeFilter = questionnaire.questionnaireConfigList.filter((el) =>
      TemplateNameManager.schemeQuestionnaireRange.includes(el.appliedRangeType)
    )
    // 过滤期别级别问卷
    const issueFilter = questionnaire.questionnaireConfigList.filter((el) =>
      TemplateNameManager.issueQuestionnaireRange.includes(el.appliedRangeType)
    )
    jsonObj.questionnaireLearning = [] as QuestionnaireLearning[]
    if (schemeFilter.length) {
      schemeFilter.forEach((item) => {
        const opt = this.convertToQuestionnaireItemReq(item, jsonObj, createCommodity, {})
        jsonObj.questionnaireLearning.push(opt)
      })
    }
    if (issueFilter.length) {
      const assignIssueQnFilter = issueFilter.filter(
        (el) => el.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.assign_issue
      )
      const perIssueQnFilter = issueFilter.filter(
        (el) => el.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.per_issue
      )
      if (assignIssueQnFilter && assignIssueQnFilter.length) {
        assignIssueQnFilter.forEach((item) => {
          const issueConfigureDto = jsonObj.issueConfigures.find(
            // 前端问卷上记录的是期别id，创建时由前端自动生成Id，需要通过Map找到对应的期别id
            (el: IssueConfigure) =>
              el.id === item.curIssueId || el.id === TrainClassConfigJsonManager.issueIdMap.get(item.curIssueId)
          )
          if (issueConfigureDto) {
            const opt = this.convertToQuestionnaireItemReq(item, jsonObj, createCommodity, {
              issueConfig: issueConfigureDto
            })
            issueConfigureDto.questionnaireLearning.push(opt)
          }
        })
      }
      if (perIssueQnFilter && perIssueQnFilter.length) {
        const voIds = perIssueQnFilter.map((el) => el.uniqueKey)
        const uniqueIdMap = new Map<string, string>()
        voIds.forEach((id) => {
          const isFeGenerated = id.startsWith(QuestionnaireConfigDetail.feUniquekeyPrefix)
          const placeholderId = isFeGenerated
            ? this.generateId(
                TemplateNameManager.questionnaireExtendPropertyUniqueKeyValue,
                TrainClassConfigJsonManager.loopCount.resourceIdUK,
                3
              )
            : id
          TrainClassConfigJsonManager.loopCount.resourceIdUK++
          uniqueIdMap.set(id, placeholderId)
        })
        issue.issueConfigList.forEach((issueConfigVo) => {
          const perQnBackupMap = issueConfigVo.perQuestionnaireBackupMap
          const perQnIds = Array.from(perQnBackupMap.keys())
          const { both, onlyInA } = DataResolve.arrayComparison(voIds, perQnIds)
          const issueConfigureDto = jsonObj.issueConfigures.find(
            // 前端问卷上记录的是期别id，创建时由前端自动生成Id，需要通过Map找到对应的期别id
            (el: IssueConfigure) =>
              el.id === issueConfigVo.id || el.id === TrainClassConfigJsonManager.issueIdMap.get(issueConfigVo.id)
          )
          if (both.length) {
            // 修改/删除问卷配置项
            both.forEach((uniqueKey: string) => {
              const updateQnVo = perQnBackupMap.get(uniqueKey)
              // 读取UI传值并更新
              const curQnVo = perIssueQnFilter.find((el) => el.uniqueKey === uniqueKey)
              if (curQnVo.operationType === OperationTypeEnum.update) {
                // 该问卷需要修改 - 读取备份并与现有合并、转换后端模型
                const updateObj: Partial<QuestionnaireConfigDetail> = {
                  allowCount: curQnVo.allowCount,
                  status: curQnVo.status,
                  templateId: curQnVo.templateId,
                  templateName: curQnVo.templateName,
                  questionnaireName: curQnVo.questionnaireName,
                  openDateType: curQnVo.openDateType,
                  openDateRange: curQnVo.openDateRange,
                  isOpenStatistic: curQnVo.isOpenStatistic,
                  appliedRangeType: QuestionnaireAppliedRangeTypeEnum.per_issue,
                  preconditionType: curQnVo.preconditionType,
                  isAssessed: curQnVo.isAssessed,
                  isForced: curQnVo.isForced,
                  triggerType: curQnVo.triggerType
                }
                const targetQnVo = Object.assign(new QuestionnaireConfigDetail(), updateQnVo, updateObj)
                targetQnVo.operationType = OperationTypeEnum.update
                const opt = this.convertToQuestionnaireItemReq(targetQnVo, jsonObj, createCommodity, {
                  issueConfig: issueConfigureDto,
                  uniqueKey: updateQnVo.uniqueKey
                })
                issueConfigureDto.questionnaireLearning.push(opt)
              }
              if (curQnVo.operationType === OperationTypeEnum.remove) {
                // 该问卷需要删除 - 读取备份、转换后端模型
                updateQnVo.operationType = OperationTypeEnum.remove
                const opt = this.convertToQuestionnaireItemReq(updateQnVo, jsonObj, createCommodity, {
                  issueConfig: issueConfigureDto,
                  uniqueKey: updateQnVo.uniqueKey
                })
                issueConfigureDto.questionnaireLearning.push(opt)
              }
            })
          }
          if (onlyInA.length) {
            // 新增问卷配置项 - 读取UI传值并创建期别下新问卷
            onlyInA.forEach((uniqueKey: string) => {
              const newQnObj: Partial<QuestionnaireConfigDetail> = {
                id: '',
                configId: '',
                preconditionId: '',
                assessSettings: [] as AssessSettingInfo<QuestionnaireAssessTypeEnum>[]
              }
              const newQnVo = perIssueQnFilter.find((el) => el.uniqueKey === uniqueKey)
              newQnVo.operationType = OperationTypeEnum.create
              const targetQnVo = Object.assign(new QuestionnaireConfigDetail(), newQnVo, newQnObj)
              const opt = this.convertToQuestionnaireItemReq(targetQnVo, jsonObj, createCommodity, {
                issueConfig: issueConfigureDto,
                uniqueKey: uniqueIdMap.get(uniqueKey)
              })
              issueConfigureDto.questionnaireLearning.push(opt)
            })
          }
        })
      }
    }
    // 转换期别考核配置，要关联期别下教学计划学习方式和问卷学习方式
    this.convertIssueAssess(jsonObj.issueConfigures, issue.issueConfigList)
  }

  /**
   * 转换期别下教学计划学习方式
   * @param opt 后端期别dto
   * @param issue 前端期别模型
   * @param operationType 操作类型，用于标记教学计划学习方式的实际操作类型，不一定与期别的操作类型一致
   */
  convertTeachPlan(opt: IssueConfigure, issue: IssueConfigDetail, operationType: OperationTypeEnum) {
    const teachPlanLearning = new TeachPlanLearning()
    teachPlanLearning.operation = operationType
    teachPlanLearning.config = new TeachPlanLearningConfig()
    teachPlanLearning.config.extendProperties = [] as ExtendProperty[]
    teachPlanLearning.config.teachingPlanItemsGroups = [] as TeachingPlanItemsGroup[]
    const teachPlanLearningAssessSetting = new TeachPlanLearningAssessSetting()
    // 是否开启考勤
    const isOpenAttendance = issue.isOpenAttendance
    // 上次是否存在考核项，仅在修改或删除时使用
    let isHaveAssessLastTime = false
    // 教学计划配置Id
    let planId = ''
    if (operationType === OperationTypeEnum.create) {
      // 新建
      teachPlanLearning.id = this.generateId(
        TemplateNameManager.teachPlanLearningId,
        TrainClassConfigJsonManager.loopCount.learningIdTPL,
        3
      )
      TrainClassConfigJsonManager.loopCount.learningIdTPL++
      planId = this.generateId(
        TemplateNameManager.teachPlanResourceId,
        TrainClassConfigJsonManager.loopCount.resourceIdTP,
        3
      )
      TrainClassConfigJsonManager.loopCount.resourceIdTP++
      teachPlanLearning.config.id = planId

      teachPlanLearningAssessSetting.id = this.generateId(
        TemplateNameManager.teachPlanLearningAssessSettingId,
        TrainClassConfigJsonManager.loopCount.assessIdTPLA,
        3
      )
      TrainClassConfigJsonManager.loopCount.assessIdTPLA++
      teachPlanLearningAssessSetting.name = TemplateNameManager.teachPlanLearningAssessSettingName
    } else {
      // 修改&删除
      teachPlanLearning.id = issue.relateTeachPlanLearning.id
      planId = issue.relateTeachPlanLearning.configId
      teachPlanLearning.config.id = planId
      const teachPlanAssessItem = issue.relateTeachPlanLearning.assessSettings.find(
        (el) => el.type === TeachPlanAssessTypeEnum.plan
      )
      if (teachPlanAssessItem) {
        isHaveAssessLastTime = true
        teachPlanLearningAssessSetting.id = teachPlanAssessItem.id
        teachPlanLearningAssessSetting.name = teachPlanAssessItem.name
      } else {
        teachPlanLearningAssessSetting.id = this.generateId(
          TemplateNameManager.teachPlanLearningAssessSettingId,
          TrainClassConfigJsonManager.loopCount.assessIdTPLA,
          3
        )
        TrainClassConfigJsonManager.loopCount.assessIdTPLA++
        teachPlanLearningAssessSetting.name = TemplateNameManager.teachPlanLearningAssessSettingName
      }
    }
    teachPlanLearningAssessSetting.signRate = this.convertPercentageToDecimal(issue.attendanceRate)
    if (isOpenAttendance) {
      // 开启考勤，需配置考核项
      teachPlanLearning.assessSetting = teachPlanLearningAssessSetting
    } else {
      // 之前存在考核项，但未开启考勤，标记删除
      if (isHaveAssessLastTime) {
        teachPlanLearningAssessSetting.operation = OperationTypeEnum.remove
        teachPlanLearning.assessSetting = teachPlanLearningAssessSetting
      }
    }
    const config = teachPlanLearning.config
    config.planMode = issue.planMode
    const courseListTmp = issue.issueCourseList.sort((prev, cur) => {
      const prevTime = moment(prev.fullBeginDate, TemplateNameManager.momentFullTimeFormat).valueOf()
      const curTime = moment(cur.fullBeginDate, TemplateNameManager.momentFullTimeFormat).valueOf()
      return prevTime - curTime
    })
    // 这里要区分是整个期别都被移除还是只移除部分课程
    // -> 移除整个期别，期别下所有课程都会被打上删除标记
    // -> 移除部分课程，期别下仅被移除的课程会被打上删除标记
    const courseListTmpFilter =
      issue.operationType === OperationTypeEnum.remove
        ? courseListTmp
        : courseListTmp.filter((el) => el.operationType !== OperationTypeEnum.remove)
    if (courseListTmpFilter.length && issue.issueTrainingDateType === IssueTrainingDateTypeEnum.by_issue_courses) {
      // 当培训时段为按课程时，需要根据有效课表的第一门开始时间和最后一门的结束时间来确定培训开始时间和结束时间
      const startTime = this.getFormatTime(courseListTmpFilter[0].fullBeginDate)
      const endTime = this.getFormatTime(courseListTmpFilter[courseListTmpFilter.length - 1].fullEndDate)
      config.startTime = startTime
      config.endTime = endTime
    }
    if (issue.issueTrainingDateType === IssueTrainingDateTypeEnum.custom) {
      // 当培训时段为自定义时，需要根据自定义的开始时间和结束时间来确定培训开始时间和结束时间
      config.startTime = this.getFormatTime(issue.trainingDateRange.startDate)
      config.endTime = this.getFormatTime(issue.trainingDateRange.endDate)
    }
    /** 教学计划组 */
    config.teachingPlanItemsGroups = this.convertTeachPlanItemGroup(issue, planId)
    /** 培训点信息 */
    config.address = new Address()
    config.address.id = issue.trainingPointConfigId
      ? issue.trainingPointConfigId
      : this.generateId(
          TemplateNameManager.teachPlanLearningResourceId,
          TrainClassConfigJsonManager.loopCount.resourceIdTPR,
          3
        )
    TrainClassConfigJsonManager.loopCount.resourceIdTPR++
    config.address.trainingPointId = issue.trainingPointId
    config.address.nature = issue.trainingPointNature
    config.address.lng = issue.trainingPointLng
    config.address.lat = issue.trainingPointLat
    /** 配置-扩展属性 */
    config.extendProperties = [] as ExtendProperty[]
    config.extendProperties.push(
      {
        name: TemplateNameManager.teachingPlanHeadTeacherName,
        value: issue.headTeacherName
      }, // 班主任姓名
      {
        name: TemplateNameManager.teachingPlanHeadTeacherPhone,
        value: issue.headTeacherPhone
      }, // 班主任电话
      {
        name: TemplateNameManager.teachingPlanHotelLiaisonName,
        value: issue.hotelContactsName
      }, // 酒店联系人姓名
      {
        name: TemplateNameManager.teachingPlanHotelLiaisonPhone,
        value: issue.hotelContactsPhone
      } // 酒店联系人电话
    )
    opt.teachPlanLearning = teachPlanLearning
  }

  /**
   * 转换期别下的教学计划组
   * @param issue 前端期别模型
   * @param planId 教学计划配置ID
   */
  convertTeachPlanItemGroup(issue: IssueConfigDetail, planId: string): TeachingPlanItemsGroup[] {
    /** 添加删除标记 */
    const addFlag = function (groupsBackup: TeachingPlanItemsGroup[], operationType: OperationTypeEnum) {
      return groupsBackup.map((group) => {
        group.operation = operationType
        group.teachingPlanItems?.forEach((teachingPlanItem) => {
          teachingPlanItem.operation = operationType
        })
        return group
      })
    }

    // 新教学计划组
    let groups = [] as TeachingPlanItemsGroup[]
    // 上一次的教学计划组备份
    let groupsBackup = issue.teachingPlanItemsGroupsBackup
    // 总期别课程列表
    const issueCourseList = issue.issueCourseList
    // 时间范围分组
    const timeScopeGroup: {
      teachingDate: string // 培训日期
      isAfternoon: boolean // 是否是下午组
      items: issueCourseDetail[] // 期别课程列表
    }[] = []

    /** 获取操作类型 */
    const getOperationType = function () {
      let operationType: 'add' | 'update' | 'remove' | 'none' = 'none'
      if (issue.operationType === OperationTypeEnum.create) {
        // 新建
        if (issueCourseList.length) {
          // 期别下有课程，走新增教学计划组的逻辑
          operationType = 'add'
        }
      }
      if (issue.operationType === OperationTypeEnum.update) {
        // 修改
        if (issueCourseList.length) {
          if (groupsBackup.length) {
            // 期别下有课程，且存在教学计划组备份，走修改教学计划组的逻辑
            operationType = 'update'
          } else {
            // 期别下有课程，但不存在教学计划组备份，走新增教学计划组的逻辑
            operationType = 'add'
          }
        } else {
          if (groupsBackup.length) {
            // 期别下无课程，但存在教学计划组备份，走删除教学计划组的逻辑
            operationType = 'remove'
          } else {
            // 期别下无课程，且不存在教学计划组备份，不做任何处理
          }
        }
      }
      if (issue.operationType === OperationTypeEnum.remove) {
        // 删除
        if (groupsBackup.length) {
          // 若期别下存在教学计划组备份，则仅在教学计划组和教学计划项上添加删除标记
          operationType = 'remove'
        }
      }
      return operationType
    }

    // 操作类型 add-新增、update-修改、remove-删除、none-无操作
    const operationType = getOperationType()

    if (operationType === 'add') {
      // 新增逻辑
      issueCourseList.forEach((issueCourse) => {
        const teachingDate = issueCourse.teachingDate
        const isAfternoon = issueCourse.isAfternoon
        if (isAfternoon || isAfternoon === false) {
          const index = timeScopeGroup.findIndex(
            (el) => el.teachingDate === teachingDate && el.isAfternoon === isAfternoon
          )
          if (index > -1) {
            timeScopeGroup[index].items.push(issueCourse)
          } else {
            timeScopeGroup.push({
              teachingDate,
              isAfternoon,
              items: [issueCourse]
            })
          }
        }
      })

      groups = timeScopeGroup.map((timeGroup) => {
        return this.createNewTeachPlanGroup(timeGroup.items, { isAfternoon: timeGroup.isAfternoon, planId })
      })
    }

    if (operationType === 'update') {
      groupsBackup = addFlag(groupsBackup, OperationTypeEnum.update)
      const added = issueCourseList.filter((el) => el.operationType === OperationTypeEnum.create)
      const updated = issueCourseList.filter((el) => el.operationType === OperationTypeEnum.update)
      const deleted = issueCourseList.filter((el) => el.operationType === OperationTypeEnum.remove)
      deleted.forEach((issueCourse: IssueCourseDetail) => {
        const targetGroup = groupsBackup.find((tmpGroup) => tmpGroup.id === issueCourse.belongGroupInfo.groupId)
        if (targetGroup) {
          const targetItem = targetGroup.teachingPlanItems.find((el) => el.id === issueCourse.id)
          if (targetItem) {
            targetItem.operation = OperationTypeEnum.remove
          }
        }
      })
      added.forEach((issueCourse: IssueCourseDetail) => {
        const targetGroup = groupsBackup.find((tmpGroup) => tmpGroup.groupTypeData === issueCourse.teachingDate)
        if (targetGroup) {
          // 找到对应的教学计划组，说明该教学计划项是新增的，需要新增教学计划项
          issueCourse.operationType = OperationTypeEnum.create
          const newItem = this.convertTeachPlanItem(issueCourse, { planId, groupId: targetGroup.id })
          targetGroup.teachingPlanItems.push(newItem)
        } else {
          // 找不到对应的教学计划组，说明该课程、该教学计划组是新增的，需要新增教学计划组再新增教学计划项
          const newGroup: TeachingPlanItemsGroup = this.createNewTeachPlanGroup([issueCourse], {
            isAfternoon: issueCourse.isAfternoon,
            planId
          })
          groupsBackup.push(newGroup)
        }
      })
      updated.forEach((issueCourse: IssueCourseDetail) => {
        const targetGroup = groupsBackup.find((tmpGroup) => tmpGroup.id === issueCourse.belongGroupInfo.groupId)
        if (targetGroup) {
          const isAfternoon = targetGroup.groupTypeCode === 'PM'
          const idx = targetGroup.teachingPlanItems.findIndex((el) => el.id === issueCourse.id)
          if (idx || idx === 0) {
            const targetItem = targetGroup.teachingPlanItems[idx]
            if (targetItem) {
              // 找到对应的教学计划项
              if (targetGroup.groupTypeData === issueCourse.teachingDate && isAfternoon === issueCourse.isAfternoon) {
                // 说明是同组（教学日期、组类型一致）
                issueCourse.operationType = OperationTypeEnum.update
                const newItem = this.convertTeachPlanItem(issueCourse, { planId, groupId: targetGroup.id })
                Object.assign(targetItem, newItem)
              } else {
                targetGroup.teachingPlanItems.splice(idx, 1)
                // 说明不同组，先找对应组再新增对应项
                const findGroup = groupsBackup.find((el) => {
                  const isAfternoon = el.groupTypeCode === 'PM'
                  return el.groupTypeData === issueCourse.teachingDate && isAfternoon === issueCourse.isAfternoon
                })
                if (findGroup) {
                  // 找到组，教学计划项移到对应组
                  issueCourse.operationType = OperationTypeEnum.update
                  const newItem = this.convertTeachPlanItem(issueCourse, { planId, groupId: findGroup.id })
                  findGroup.teachingPlanItems.push(newItem)
                } else {
                  // 没找到组，说明是新增组
                  issueCourse.operationType = OperationTypeEnum.update
                  const newGroup = this.createNewTeachPlanGroup([issueCourse], {
                    isAfternoon: issueCourse.isAfternoon,
                    planId
                  })
                  groupsBackup.push(newGroup)
                }
              }
            }
          }
        }
      })
      // 教学计划组、教学计划项重新排序；标记教学计划组是否删除
      // -> 一层排序逻辑：按照教学计划组的最早开始时间排序
      groups = groupsBackup.sort((prev, cur) => {
        const prevTime = new Date(prev.teachingPlanItems[0]?.startTime || '').getTime()
        const curTime = new Date(cur.teachingPlanItems[0]?.startTime || '').getTime()
        return prevTime - curTime
      })
      // -> 二层排序逻辑：按照教学计划项的开始时间排序
      groups.forEach((group) => {
        if (group.teachingPlanItems && group.teachingPlanItems.length) {
          group.teachingPlanItems = group.teachingPlanItems.sort((prev, cur) => {
            const prevTime = new Date(prev.startTime).getTime()
            const curTime = new Date(cur.startTime).getTime()
            return prevTime - curTime
          })
        }
      })
    }

    if (operationType === 'remove') {
      // 删除逻辑
      groups = addFlag(groupsBackup, OperationTypeEnum.remove)
    }
    return groups
  }

  /**
   * 创建新教学计划组
   * @param courseList 期别课程列表
   * @param ext.isAfternoon 是否为下午组
   * @param ext.planId 教学计划配置ID
   */
  createNewTeachPlanGroup(
    courseList: issueCourseDetail[],
    ext: { isAfternoon: boolean; planId: string }
  ): TeachingPlanItemsGroup {
    const group = new TeachingPlanItemsGroup()
    group.operation = OperationTypeEnum.create
    group.planId = ext.planId
    group.id = this.generateId(
      TemplateNameManager.teachingPlanItemsGroupId,
      TrainClassConfigJsonManager.loopCount.planIdPG,
      3
    )
    TrainClassConfigJsonManager.loopCount.planIdPG++
    group.groupType = 1
    group.groupTypeCode = ext.isAfternoon ? 'PM' : 'AM'
    group.name = ext.isAfternoon ? '下午组' : '上午组'
    if (courseList && courseList.length) {
      group.groupTypeData = courseList[0].teachingDate
      group.teachingPlanItems = courseList.map((course) => {
        return this.convertTeachPlanItem(course, { planId: ext.planId, groupId: group.id })
      })
    }
    return group
  }

  /**
   * 转换教学计划项
   * @param issueCourse 期别课程信息
   * @param ext.planId 教学计划配置ID
   * @param ext.groupId 教学计划组ID
   */
  convertTeachPlanItem(issueCourse: IssueCourseDetail, ext: { planId: string; groupId: string }): TeachingPlanItem {
    const teachPlanItem = new TeachingPlanItem()
    teachPlanItem.operation = issueCourse.operationType
    // 授课教师信息
    const teacher = new TeachingPlanItemTeacher()
    if (issueCourse.operationType === OperationTypeEnum.create) {
      teachPlanItem.id = this.generateId(
        TemplateNameManager.teachingPlanItemId,
        TrainClassConfigJsonManager.loopCount.resourceIdTPI,
        3
      )
      TrainClassConfigJsonManager.loopCount.resourceIdTPI++
      teacher.id = this.generateId(
        TemplateNameManager.teachPlanLearningResourceId,
        TrainClassConfigJsonManager.loopCount.resourceIdTPR,
        3
      )
      TrainClassConfigJsonManager.loopCount.resourceIdTPR++
    } else {
      teachPlanItem.id = issueCourse.id
      teacher.id = issueCourse.teacherId
    }
    teachPlanItem.planId = ext.planId
    teachPlanItem.planGroupId = ext.groupId
    teachPlanItem.name = issueCourse.courseName
    teachPlanItem.planMode = issueCourse.planMode
    teachPlanItem.period = Number(issueCourse.coursePeriod)
    teachPlanItem.startTime = this.getFormatTime(issueCourse.fullBeginDate)
    teachPlanItem.endTime = this.getFormatTime(issueCourse.fullEndDate)
    teachPlanItem.teachers = [] as TeachingPlanItemTeacher[]
    teacher.nature = issueCourse.teacherNature
    teacher.teacherName = issueCourse.teacherName
    teacher.extendProperties = [] as ExtendProperty[]
    // 教师职称
    const professionalTitleProperty = new ExtendProperty()
    professionalTitleProperty.name = TemplateNameManager.teachingPlanItemTeacherProfessionalTitle
    professionalTitleProperty.value = issueCourse.teacherPositionTile
    // 教师单位名称
    const teacherUnitNameProperty = new ExtendProperty()
    teacherUnitNameProperty.name = TemplateNameManager.teachingPlanItemTeacherUnitName
    teacherUnitNameProperty.value = issueCourse.teacherUnitName
    teacher.extendProperties.push(professionalTitleProperty, teacherUnitNameProperty)
    teachPlanItem.teachers.push(teacher)
    return teachPlanItem
  }

  /**
   * 处理问卷项
   * @param item 前端问卷配置项
   * @param jsonObj 后端JSON对象，
   * @param createCommodity 前端方案对象
   * @param optional 可选项
   * {
   *   issueConfig 问卷关联期别Dto配置，指定期别问卷必传
   *   uniqueKey 全期别问卷必传
   * }
   */
  convertToQuestionnaireItemReq(
    item: QuestionnaireConfigDetail,
    jsonObj: Scheme,
    createCommodity: MutationCreateTrainClassCommodity,
    optional: { issueConfig?: IssueConfigure; uniqueKey?: string }
  ): QuestionnaireLearning {
    const opt = new QuestionnaireLearning()
    // 问卷停用，等价于不纳入考核
    const qnDisable = item.status !== QuestionnaireStatusEnum.enabled
    opt.config = new QuestionnaireLearningConfig()
    opt.config.needAssessment = item.isAssessed
    opt.operation = item.operationType
    if (item.operationType === OperationTypeEnum.create) {
      // 新建 -> 生成占位符
      opt.id = this.generateId(
        TemplateNameManager.questionnaireLearningId,
        TrainClassConfigJsonManager.loopCount.learningIdQL,
        3
      )
      TrainClassConfigJsonManager.loopCount.learningIdQL++
      if (item.isAssessed && !qnDisable) {
        opt.assessSetting = [] as QuestionnaireLearningAssessSetting[]
        const assessSettingItem = new QuestionnaireLearningAssessSetting()
        assessSettingItem.id = this.generateId(
          TemplateNameManager.questionnaireLearningAssessSettingId,
          TrainClassConfigJsonManager.loopCount.assessIdQLA,
          3
        )
        TrainClassConfigJsonManager.loopCount.assessIdQLA++
        assessSettingItem.name = TemplateNameManager.questionnaireLearningAssessSettingName
        opt.assessSetting.push(assessSettingItem)
      }
      opt.config.id = this.generateId(
        TemplateNameManager.questionnaireLearningConfigId,
        TrainClassConfigJsonManager.loopCount.resourceIdQLR,
        3
      )
      TrainClassConfigJsonManager.loopCount.resourceIdQLR++
      if (item.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.per_issue) {
        opt.extendProperties = [] as ExtendProperty[]
        opt.extendProperties.push({
          name: TemplateNameManager.questionnaireExtendPropertyUniqueKeyName,
          value: optional.uniqueKey
        })
      }
    } else {
      // 更新、删除 -> 使用状态层的id
      opt.id = item.id
      if (item.assessSettings && item.assessSettings.length) {
        const questionnaireAssess = item.assessSettings.find(
          (el: AssessSettingInfo<QuestionnaireAssessTypeEnum>) => el.type === QuestionnaireAssessTypeEnum.questionary
        )
        if (questionnaireAssess) {
          opt.assessSetting = [] as QuestionnaireLearningAssessSetting[]
          const assessSettingItem = new QuestionnaireLearningAssessSetting()
          assessSettingItem.id = questionnaireAssess.id
          assessSettingItem.name = questionnaireAssess.name
          opt.assessSetting.push(assessSettingItem)
        }
      }
      opt.config.id = item.configId
      if (item.operationType === OperationTypeEnum.update) {
        // 更新但是纳入考核的，要考虑
        // 1-之前没有考核项，需要新增考核项的情况
        const needAssessment = opt.config.needAssessment
        const noAssessSettings = !opt.assessSetting || !opt.assessSetting.length
        if (needAssessment && noAssessSettings) {
          opt.assessSetting = [] as QuestionnaireLearningAssessSetting[]
          const assessSettingItem = new QuestionnaireLearningAssessSetting()
          assessSettingItem.id = this.generateId(
            TemplateNameManager.questionnaireLearningAssessSettingId,
            TrainClassConfigJsonManager.loopCount.assessIdQLA,
            3
          )
          TrainClassConfigJsonManager.loopCount.assessIdQLA++
          assessSettingItem.name = TemplateNameManager.questionnaireLearningAssessSettingName
          opt.assessSetting.push(assessSettingItem)
        }
        // 2-之前有考核项 || 问卷停用 ，需要移除考核项的情况
        if ((!needAssessment || qnDisable) && !noAssessSettings) {
          const targetAssessItem = opt.assessSetting.find(
            (el: QuestionnaireLearningAssessSetting) =>
              el.name === TemplateNameManager.questionnaireLearningAssessSettingName
          )
          if (targetAssessItem) {
            targetAssessItem.operation = OperationTypeEnum.remove
          }
        }
      }
      if (item.operationType === OperationTypeEnum.remove) {
        // 删除但是纳入考核的，要考虑如果之前有考核项，需要移除考核项，这里先打上标记，后面用统一转换考核指标再进行移除
        if (opt.assessSetting && opt.assessSetting.length) {
          const targetAssessItem = opt.assessSetting.find(
            (el: QuestionnaireLearningAssessSetting) =>
              el.name === TemplateNameManager.questionnaireLearningAssessSettingName
          )
          if (targetAssessItem) {
            targetAssessItem.operation = OperationTypeEnum.remove
          }
        }
      }
      if (item.appliedRangeType === QuestionnaireAppliedRangeTypeEnum.per_issue) {
        opt.extendProperties = [] as ExtendProperty[]
        opt.extendProperties.push({
          name: TemplateNameManager.questionnaireExtendPropertyUniqueKeyName,
          value: item.uniqueKey
        })
      }
    }
    opt.config.name = item.questionnaireName
    opt.config.description = ''
    opt.config.allowCount = item.allowCount
    opt.config.paperPublishConfigureId = item.templateId
    opt.config.allowStartTime = item.openDateRange.startDate
    opt.config.allowEndTime = item.openDateRange.endDate
    opt.config.openResults = item.isOpenStatistic
    opt.config.forceQuestionnaire = item.isForced
    opt.config.status = item.status
    opt.config.useRange = item.appliedRangeType
    opt.config.questionnaireTriggerLink = item.triggerType
    opt.config.operation = item.operationType
    if (TemplateNameManager.issueQuestionnaireRange.includes(item.appliedRangeType)) {
      // 问卷应用范围：指定培训期别 || （全）培训期别
      const precondition = new QuestionnaireLearningPrecondition()
      precondition.id = item.preconditionId
        ? item.preconditionId
        : this.generateId(
            TemplateNameManager.questionnaireLearningPreconditionId,
            TrainClassConfigJsonManager.loopCount.preconditionIdQLP,
            3
          )
      TrainClassConfigJsonManager.loopCount.preconditionIdQLP++
      precondition.name = item.preconditionName
        ? item.preconditionName
        : TemplateNameManager.questionnaireIssueLearningPreconditionName
      precondition.referLearningId = opt.id
      precondition.issueId = optional.issueConfig?.id
      // 2025-06-05 由于全期别问卷前置条件受具体期别下有无课表影响，故根据具体期别有无课表，重新计算前置条件类型
      let preconditionType = item.preconditionType
      if (optional.issueConfig.trainingPeriodType === IssueTrainingDateTypeEnum.custom) {
        // 期别无课表，视同无前置条件
        preconditionType = QuestionnairePreconditionTypeEnum.none
      }
      if (preconditionType === QuestionnairePreconditionTypeEnum.pass_issue_assess) {
        opt.precondition = precondition
      }
      if (preconditionType === QuestionnairePreconditionTypeEnum.none && item.preconditionId) {
        // 有前置条件ID，但是没有前置条件类型，移除前置条件，需要打上删除标记
        precondition.operation = OperationTypeEnum.remove
        opt.precondition = precondition
      }
    }
    if (TemplateNameManager.schemeQuestionnaireRange.includes(item.appliedRangeType)) {
      // 问卷应用范围：方案 || 线上课程 && 问卷前置条件：通过线上课程考核
      const precondition = new QuestionnaireLearningPrecondition()
      precondition.id = item.preconditionId
        ? item.preconditionId
        : this.generateId(
            TemplateNameManager.questionnaireLearningPreconditionId,
            TrainClassConfigJsonManager.loopCount.preconditionIdQLP,
            3
          )
      TrainClassConfigJsonManager.loopCount.preconditionIdQLP++
      let defaultName = ''
      if (TrainClassConfigJsonManager.schemeType === 1) {
        // 选课规则
        defaultName = TemplateNameManager.questionnaireChooseCoursePreconditionName
        const chooseCourseLearning = jsonObj.chooseCourseLearning
        if (chooseCourseLearning) {
          precondition.referLearningId = chooseCourseLearning.id
          precondition.compulsoryRequirePeriod = chooseCourseLearning.assessSetting.compulsoryRequirePeriod
          precondition.electiveRequirePeriod = chooseCourseLearning.assessSetting.electiveRequirePeriod
        }
      }
      if (TrainClassConfigJsonManager.schemeType === 2) {
        // 自主选课
        defaultName = TemplateNameManager.questionnaireAutonomousCourseLearningPreconditionName
        const autonomousCourseLearningDto = jsonObj.autonomousCourseLearning
        if (autonomousCourseLearningDto) {
          precondition.referLearningId = autonomousCourseLearningDto.id
          precondition.requirePeriod = autonomousCourseLearningDto.assessSetting.requirePeriod
          const courseLearning = createCommodity.learningTypeModel.courseLearning
          const classification = courseLearning.classification
          const childOutlines = classification.childOutlines?.filter((item) => {
            return item.operation !== OperationEnum.REMOVE
          })
          precondition.compulsoryRequirePeriod = childOutlines?.length
            ? childOutlines?.reduce((prev, cur) => {
                return CalculatorObj.add(cur.compulsoryCoursePeriodTotal || 0, prev)
              }, 0) || 0
            : classification.compulsoryCoursePeriodTotal || 0
          precondition.outlineRequires = [] as OutlineRequire[]
          const courseTrainingOutlinesDto = autonomousCourseLearningDto.config.courseTrainingOutlines
          courseTrainingOutlinesDto.forEach((outlineDto) => {
            if (
              outlineDto.assessSetting &&
              outlineDto.assessSetting.requirePeriod &&
              outlineDto.operation !== OperationEnum.REMOVE
            ) {
              precondition.outlineRequires.push({
                outLineId: outlineDto.id,
                requirePeriod: outlineDto.assessSetting.requirePeriod
              })
            }
          })
        }
      }
      precondition.name = item.preconditionName ? item.preconditionName : defaultName
      precondition.operation = item.operationType
      if (item.preconditionType === QuestionnairePreconditionTypeEnum.pass_online_course) {
        opt.precondition = precondition
      }
      if (item.preconditionType === QuestionnairePreconditionTypeEnum.none && item.preconditionId) {
        // 有前置条件ID，但是没有前置条件类型，移除前置条件，需要打上删除标记
        precondition.operation = OperationTypeEnum.remove
        opt.precondition = precondition
      }
    }
    return opt
  }

  /**
   * 转换期别下考核配置
   * @param issueDtos 后端期别dto列表
   * @param issues 前端期别列表
   */
  convertIssueAssess(issueDtos: IssueConfigure[], issues: IssueConfigDetail[]) {
    issueDtos.forEach((issueDto) => {
      issueDto.assessSetting = [] as IssueConfigureAssessSetting[]
      console.log(TrainClassConfigJsonManager.issueIdMap, 'xxxx')
      const targetIssue = issues.find(
        (el) => el.id && issueDto.id === TrainClassConfigJsonManager.issueIdMap.get(el.id)
      )
      // 是否需要期别考试（结业测试）考核
      const issueExamAssessItemDto = this.getIssueExamAssessItem(issueDto, targetIssue)

      const issueTimeAssessItemDto = this.getIssueTimeAssessItem(issueDto, targetIssue)

      const issueIssueAssessItemDto = new IssueConfigureAssessSetting()
      if (issueDto.operation === OperationTypeEnum.create) {
        // 新建
        // 期别-期别考核配置
        issueIssueAssessItemDto.id = this.generateId(
          TemplateNameManager.issueConfigureAssessSettingId,
          TrainClassConfigJsonManager.loopCount.assessIdIA,
          3
        )
        TrainClassConfigJsonManager.loopCount.assessIdIA++
        issueIssueAssessItemDto.name = TemplateNameManager.issueConfigureIssueAssessSettingName

        // 只有期别学时大于0时，才添加期别学时成果
        const issuePeriod = this.getIssuePeriod(targetIssue)
        if (issuePeriod > 0) {
          issueIssueAssessItemDto.learningResults = [] as LearningResult[]
          const issuePeriodCredit = new LearningResult()
          issuePeriodCredit.gradeType = 'CREDIT'
          issuePeriodCredit.type = 1
          issuePeriodCredit.grade = issuePeriod
          issuePeriodCredit.id = this.generateId(
            TemplateNameManager.issueAssessSettingLearningResultsPeriodId,
            TrainClassConfigJsonManager.loopCount.resultIdP,
            3
          )
          TrainClassConfigJsonManager.loopCount.resultIdP++
          issueIssueAssessItemDto.learningResults.push(issuePeriodCredit)
        }
      }
      if (issueDto.operation === OperationTypeEnum.update || issueDto.operation === OperationTypeEnum.remove) {
        // 更新
        const issueIssueAssessItem = targetIssue.assessSettings.find((el) => el.type === IssueAssessTypeEnum.issue)
        // 期别-期别考核配置
        issueIssueAssessItemDto.id = issueIssueAssessItem.id
        issueIssueAssessItemDto.name = issueIssueAssessItem.name
        if (issueDto.operation === OperationTypeEnum.remove) {
          // 移除项不需要重新计算关联的考核Id
          issueIssueAssessItemDto.relateAssessIds = issueIssueAssessItem.relateAssessIds
        }
        // 只有期别学时大于0时，才添加期别学时成果
        // 如果之前有关联的期别学时成果，若修改后的期别学时为0，则移除期别学时成果
        // 如果之前没有关联的期别学时成果，若修改后的期别学时大于0，则添加期别学时成果
        const issuePeriod = this.getIssuePeriod(targetIssue)
        if (targetIssue.periodCreditId) {
          issueIssueAssessItemDto.learningResults = [] as LearningResult[]
          const issuePeriodCredit = new LearningResult()
          issuePeriodCredit.gradeType = 'CREDIT'
          issuePeriodCredit.type = 1
          issuePeriodCredit.grade = issuePeriod
          if (issuePeriod == 0) {
            issuePeriodCredit.operation = OperationTypeEnum.remove
          }
          issuePeriodCredit.id = targetIssue.periodCreditId
          issueIssueAssessItemDto.learningResults.push(issuePeriodCredit)
        } else {
          if (issuePeriod > 0) {
            issueIssueAssessItemDto.learningResults = [] as LearningResult[]
            const issuePeriodCredit = new LearningResult()
            issuePeriodCredit.gradeType = 'CREDIT'
            issuePeriodCredit.type = 1
            issuePeriodCredit.grade = issuePeriod
            issuePeriodCredit.id = this.generateId(
              TemplateNameManager.issueAssessSettingLearningResultsPeriodId,
              TrainClassConfigJsonManager.loopCount.resultIdP,
              3
            )
            TrainClassConfigJsonManager.loopCount.resultIdP++
            issueIssueAssessItemDto.learningResults.push(issuePeriodCredit)
          }
        }
      }
      if (issueDto.operation === OperationTypeEnum.create || issueDto.operation === OperationTypeEnum.update) {
        issueIssueAssessItemDto.relateAssessIds = [] as string[]
        if (issueTimeAssessItemDto && issueTimeAssessItemDto.id) {
          issueIssueAssessItemDto.relateAssessIds.push(issueTimeAssessItemDto.id)
        }
        if (issueExamAssessItemDto && issueExamAssessItemDto.operation !== OperationTypeEnum.remove) {
          issueIssueAssessItemDto.relateAssessIds.push(issueExamAssessItemDto.id)
        }
        // 过滤纳入考核的问卷考核Id
        const assessedQuestionnaireFilter = issueDto.questionnaireLearning.filter(
          (el) => el.config.needAssessment && el.config.status === QuestionnaireStatusEnum.enabled
        )
        const ids = [] as string[]
        if (assessedQuestionnaireFilter.length) {
          assessedQuestionnaireFilter.forEach((el) => {
            if (el.assessSetting && el.assessSetting.length) {
              const targetAssessItem = el.assessSetting.find(
                (el) => el.name === TemplateNameManager.questionnaireLearningAssessSettingName
              )
              if (targetAssessItem.id && targetAssessItem.operation !== OperationTypeEnum.remove) {
                ids.push(targetAssessItem.id)
              }
            }
          })
          issueIssueAssessItemDto.relateAssessIds.push(...ids)
        }
        // 添加教学计划考核Id 目前一个期别对应一个教学计划
        // 只有当存在教学计划学习方式 且 教学计划考核指标存在 时，才添加教学计划考核Id
        if (issueDto.teachPlanLearning) {
          // 是否存在教学计划学习方式
          const isTeachPlanLearningValid = issueDto.teachPlanLearning.operation !== OperationTypeEnum.remove
          // 是否存在教学计划考核指标
          const isTeachPlanAssessValid =
            issueDto.teachPlanLearning.assessSetting &&
            issueDto.teachPlanLearning.assessSetting.id &&
            issueDto.teachPlanLearning.assessSetting.operation !== OperationTypeEnum.remove
          if (isTeachPlanLearningValid && isTeachPlanAssessValid) {
            issueIssueAssessItemDto.relateAssessIds.push(issueDto.teachPlanLearning.assessSetting.id)
          }
        }
      }
      /** 配置期别考核项 **/
      if (issueExamAssessItemDto) {
        issueDto.assessSetting.push(issueExamAssessItemDto)
      }
      issueDto.assessSetting.push(issueIssueAssessItemDto, issueTimeAssessItemDto)
    })
  }

  /**
   * 获取期别考核项(当前时间>=期别培训结束时间)
   * @param issueDto 期别dto
   * @param issue 前端期别模型
   * @private
   */
  private getIssueTimeAssessItem(issueDto: IssueConfigure, issue: IssueConfigDetail) {
    const result = new IssueConfigureAssessSetting()
    if (issueDto.operation === OperationTypeEnum.create) {
      result.name = TemplateNameManager.issueConfigureIssueAssessSettingTime
      result.id = this.generateId(
        TemplateNameManager.issueConfigureAssessSettingId,
        TrainClassConfigJsonManager.loopCount.assessIdIA,
        3
      )
      TrainClassConfigJsonManager.loopCount.assessIdIA++
    }
    if (issueDto.operation === OperationTypeEnum.update || issueDto.operation === OperationTypeEnum.remove) {
      const issueTimeAssessItem = issue.assessSettings.find((el) => el.type === IssueAssessTypeEnum.issue_time)
      if (issueTimeAssessItem) {
        result.id = issueTimeAssessItem?.id || ''
        result.name = issueTimeAssessItem?.name || ''
      } else {
        result.name = TemplateNameManager.issueConfigureIssueAssessSettingTime
        result.id = this.generateId(
          TemplateNameManager.issueConfigureAssessSettingId,
          TrainClassConfigJsonManager.loopCount.assessIdIA,
          3
        )
        TrainClassConfigJsonManager.loopCount.assessIdIA++
      }
    }
    return result
  }

  /**
   * 获取期别考试（结业测试）考核项
   * @param issueDto 期别dto
   * @param issue 前端期别模型
   * @private
   */
  private getIssueExamAssessItem(issueDto: IssueConfigure, issue: IssueConfigDetail): IssueConfigureAssessSetting {
    let result: IssueConfigureAssessSetting
    // 是否需要期别考试（结业测试）考核项
    const needIssueExamAssess = issueDto.trainingConfigConfigure.openCompletionTest
    // 前端模型上的期别考试（结业测试）考核项
    const issueExamAssessVo = issue?.assessSettings?.find((el: any) => el.type === IssueAssessTypeEnum.exam)
    if (needIssueExamAssess) {
      // 有结业测试
      result = new IssueConfigureAssessSetting()
      if (issueExamAssessVo) {
        // 有历史考核项
        result.id = issueExamAssessVo.id
        result.name = issueExamAssessVo.name
      } else {
        // 没有历史考核项
        result.id = this.generateId(
          TemplateNameManager.issueConfigureAssessSettingId,
          TrainClassConfigJsonManager.loopCount.assessIdIA,
          3
        )
        TrainClassConfigJsonManager.loopCount.assessIdIA++
        result.name = TemplateNameManager.issueConfigureExamAssessSettingName
      }
    } else {
      if (issueExamAssessVo) {
        // 无结业测试
        result = new IssueConfigureAssessSetting()
        // 有历史考核项
        result.id = issueExamAssessVo.id
        result.name = issueExamAssessVo.name
        result.operation = OperationTypeEnum.remove
      }
    }
    return result
  }

  /**
   * 将百分比转换为小数
   * @param percentage 百分比值
   * @description 比如1% = 0.01，100% = 1
   */
  convertPercentageToDecimal(percentage: number) {
    if (percentage > 0) {
      return CalculatorObj.divide(percentage, 100)
    } else {
      return 0
    }
  }

  /**
   * 获取期别学时
   * @param issue 期别信息
   * @private
   */
  private getIssuePeriod(issue: IssueConfigDetail): number {
    let periods = 0
    if (issue.issueTrainingDateType === IssueTrainingDateTypeEnum.by_issue_courses) {
      periods = issue.issueCourserPeriodTotal
    }
    if (issue.issueTrainingDateType === IssueTrainingDateTypeEnum.custom) {
      periods = issue.periods
    }
    return periods
  }

  /**
   * 获取格式化时间字符串
   * @param timeStr 时间字符串
   * @description 比如2024/7/1 00:00:00 -> 2024-01-01 00:00:00
   * @private
   */
  private getFormatTime(timeStr: string) {
    return timeStr ? moment(timeStr).format(TemplateNameManager.momentFullTimeFormat) : ''
  }
}

export default new OfflinePartJsonUtil()
