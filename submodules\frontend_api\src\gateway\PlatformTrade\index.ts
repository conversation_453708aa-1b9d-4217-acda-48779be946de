import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformTrade'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformTrade'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举

// 类

/**
 * 延迟查询批次单的查询参数
<AUTHOR> create 2020/3/11 11:27
 */
export class BatchOrderQueryParam {
  /**
   * 集体缴费批次号
   */
  batchNo?: string
  /**
   * 集体缴费单位id
   */
  batchUnitId?: string
  /**
   * 收款账号id
   */
  receiveAccountId?: string
  /**
   * 银行交易流水号
   */
  payFlowNo?: string
  /**
   * 交易状态
   */
  status?: Array<number>
  /**
   * 退款状态 1.已申请   2.退款成功 3.退款失败
-1标识为未退款状态，其他值则都无效
与底层状态不同，只关心最终状态。
如果是拒绝退款就直接清空退款的数据了
   */
  refundStatus?: Array<number>
  /**
   * 是否为0元批次单
   */
  zeroOrder?: boolean
  /**
   * 用户id
   */
  userIds?: Array<string>
  /**
   * 手机号码(后台根据这个获取用户id)
   */
  buyerPhoneNumber?: string
  /**
   * 证件号(后台根据这个获取用户id)
   */
  buyerIdentity?: string
  /**
   * 用户名
   */
  buyerName?: string
  /**
   * 买家登录账户
   */
  buyerLoginInput?: string
  /**
   * 批次单创建开始时间 >
   */
  createStartTime?: string
  /**
   * 批次单创建结束时间 <&#x3D;
   */
  createEndTime?: string
  /**
   * 批次单提交开始时间 >
   */
  commitStartTime?: string
  /**
   * 批次单提交结束时间 <&#x3D;
   */
  commitEndTime?: string
  /**
   * 批次单完成开始时间 >
   */
  completeStartTime?: string
  /**
   * 批次单创建结束时间 <&#x3D;
   */
  completeEndTime?: string
  /**
   * 是否是测试数据
   */
  test?: boolean
}

/**
 * 退款查询参数
<AUTHOR> create 2020/4/14 11:40
 */
export class BatchRefundOrderParam {
  /**
   * 集体缴费批次号
   */
  batchNo?: string
  /**
   * 集体缴费单位id
   */
  batchUnitId?: string
  /**
   * 银行交易流水号
   */
  payFlowNo?: string
  /**
   * 退款批次单状态
   */
  refundStatus?: Array<number>
  /**
   * 批次退款类型
@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderTypeConst
   */
  refundType?: number
  /**
   * 退款方式 1.线上 2.线下
   */
  refundWay?: number
  /**
   * 退款模式 1：普通退款 2：强制退款
   */
  refundMode?: number
  /**
   * 收款账号id
   */
  receiveAccountId?: string
  /**
   * 退款申请时间
   */
  applyStartTime?: string
  /**
   * 退款申请时间
   */
  applyEndTime?: string
  /**
   * 审核时间查询-开始时间
   */
  auditStartTime?: string
  /**
   * 审核时间查询-结束时间
   */
  auditEndTime?: string
  /**
   * 退款完成时间
   */
  refundCompleteStartTime?: string
  /**
   * 退款完成时间
   */
  refundCompleteEndTime?: string
  /**
   * 是否是测试数据
   */
  test?: boolean
  /**
   * 用户id
   */
  userIds?: Array<string>
  /**
   * 手机号码(后台根据这个获取用户id)
   */
  buyerPhoneNumber?: string
  /**
   * 证件号(后台根据这个获取用户id)
   */
  buyerIdentity?: string
  /**
   * 用户名
   */
  buyerName?: string
  /**
   * 买家登录账户
   */
  buyerLoginInput?: string
}

/**
 * 我的订单查询参数对象
 */
export class MyOrderQueryDTO {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 订单号或方案名称
   */
  orderNoOrSchemeName?: string
  /**
   * 订单状态
@see com.fjhb.btpx.support.constants.trade.OrderStatus
&quot;等待付款&quot;, 1
&quot;等待卖家确认款项&quot;, 2
&quot;支付中&quot;, 8
&quot;支付成功&quot;, 3
&quot;发货中&quot;, 4
&quot;发货部分失败&quot;, 9
&quot;发货失败&quot;,10
&quot;发货已完成&quot;, 5
&quot;交易成功&quot;, 6
&quot;交易关闭&quot;, 7
   */
  states?: Array<number>
  /**
   * 订单创建时间
   */
  createTime?: TimeRegionRequest
  /**
   * 订单方案
   */
  schemeId?: string
  /**
   * 订单购买的商品名称
   */
  schemeName?: string
}

/**
 * 延迟查询订单的查询参数
<AUTHOR> create 2020/3/11 11:27
 */
export class OrderQueryDTO {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 订单号
   */
  orderNoList?: Array<string>
  /**
   * 机构ID
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 机构名称
   */
  trainingInstitutionName?: string
  /**
   * 课件供应商id
   */
  coursewareSupplierIdList?: Array<string>
  /**
   * 参训单位名称
   */
  participatingUnitName?: string
  /**
   * 参训单位ID
   */
  participatingUnitIdList?: Array<string>
  /**
   * 渠道商id
   */
  channelVendorIdList?: Array<string>
  /**
   * 交易状态
&quot;等待付款&quot;, 1
&quot;等待卖家确认款项&quot;, 2
&quot;支付中&quot;, 8
&quot;支付成功&quot;, 3
&quot;发货中&quot;, 4
&quot;发货部分失败&quot;, 9
&quot;发货失败&quot;,10
&quot;发货已完成&quot;, 5
&quot;交易成功&quot;, 6
&quot;交易关闭&quot;, 7
   */
  status?: Array<number>
  /**
   * 退款状态 1.已申请   2.退款成功 3.退款失败
-1标识为未退款状态，其他值则都无效
与底层状态不同，只关心最终状态。
如果是拒绝退款就直接清空退款的数据了
   */
  refundStatus?: Array<number>
  /**
   * 购买渠道code
自主报名、集体缴费、导入开通
@see PurchaseChannelTypes
用户自主购买  1;
集体缴费  2;
管理员导入  3;
补贴系统 4;
   */
  purchaseChannelCodeList?: Array<number>
  /**
   * 报名渠道（终端）code
web、小程序、公众号
@see PurchaseChannelTerminalCodes
Web端 &quot;Web&quot;;
IOS端 &quot;IOS&quot;;
安卓端  &quot;Android&quot;;
微信小程序  &quot;WechatMini&quot;;
微信公众号  &quot;WechatOfficial&quot;;
补贴管理系统 &quot;ExternalSystemManage&quot;;
   */
  placeChannelCodeList?: Array<string>
  /**
   * 订单创建时间
   */
  createTime?: TimeRegionRequest
  /**
   * 订单交易完成时间
   */
  completeTime?: TimeRegionRequest
  /**
   * 是否为补考
   */
  makeUpExam?: boolean
  /**
   * 是否为0元订单
   */
  zeroOrder?: boolean
  /**
   * 集体缴费批次号
   */
  batchNo?: string
  /**
   * 集体缴费单位id
   */
  batchUnitId?: string
  /**
   * 收款账号id
   */
  receiveAccountId?: string
  /**
   * 交易流水号
   */
  payFlowNo?: string
  /**
   * 发券机构（属地人社局）（培训券的信息）
   */
  publishOrgNameList?: Array<string>
  /**
   * 用户id
   */
  buyerId?: string
  /**
   * 用户id
   */
  buyerIds?: Array<string>
  /**
   * 用户名称
   */
  buyerName?: string
  /**
   * 手机号码(后台根据这个获取用户id)
   */
  buyerPhoneNumber?: string
  /**
   * 证件号(后台根据这个获取用户id)
   */
  buyerIdentity?: string
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 订单购买的方案名称
   */
  schemeName?: string
  /**
   * 订单号或方案
   */
  orderNoOrSchemeName?: string
  /**
   * 方案id
   */
  schemeIdList?: Array<string>
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeIdList?: Array<string>
  /**
   * path:培训工种类别/培训工种id
用于培训类别联合工种多条件查询
   */
  workTypeIdPathList?: Array<string>
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 适用人群
   */
  suitableCrowNames?: Array<string>
  /**
   * 是否是测试数据
   */
  test?: boolean
  /**
   * 年度
   */
  year?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId?: string
}

/**
 * 上架方案查询参数对象
 */
export class PreExamLSQueryDTO {
  /**
   * 年度id
   */
  yearCode?: number
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 方案id集合
   */
  schemeIds?: Array<string>
  /**
   * 年度
   */
  year?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId?: string
}

/**
 * 退款查询参数
<AUTHOR> create 2020/4/14 11:40
 */
export class RefundOrderParam {
  /**
   * 退款对应的主订单号
   */
  orderNo?: string
  /**
   * 退款对应的子订单号
   */
  subOrderNo?: string
  /**
   * 集体缴费批次号
   */
  batchNo?: string
  /**
   * 集体缴费单位id
   */
  batchUnitId?: string
  /**
   * 收款账号id
   */
  receiveAccountId?: string
  /**
   * 银行交易流水号
   */
  bankTrasactionNumber?: string
  /**
   * 所属培训方案id
   */
  schemeId?: string
  /**
   * 所属期别id
   */
  issueId?: string
  /**
   * 交易渠道信息   web、小程序、公众号
@see PaymentChannelTypeEnum
   */
  placeChannel?: Array<string>
  /**
   * 单位id （所属培训机构id）
   */
  unitId?: string
  /**
   * 退款对应的订单是否补考
   */
  makeUpExam?: boolean
  /**
   * 退款订单状态
   */
  refundStatus?: Array<number>
  /**
   * 退款模式 1：普通退款 2：强制退款
   */
  refundMode?: number
  /**
   * 用户id
   */
  userIds?: Array<string>
  /**
   * 手机号码(后台根据这个获取用户id)
   */
  buyerPhoneNumber?: string
  /**
   * 证件号(后台根据这个获取用户id)
   */
  buyerIdentity?: string
  /**
   * 买家登录账户
   */
  buyerLoginInput?: string
  /**
   * 退款申请时间
   */
  applyStartTime?: string
  /**
   * 退款申请时间
   */
  applyEndTime?: string
  /**
   * 审核时间查询-开始时间
   */
  auditStartTime?: string
  /**
   * 审核时间查询-结束时间
   */
  auditEndTime?: string
  /**
   * 退款完成时间
   */
  refundCompleteStartTime?: string
  /**
   * 退款完成时间
   */
  refundCompleteEndTime?: string
  /**
   * 是否是测试数据
   */
  test?: boolean
}

/**
 * 时间范围
<AUTHOR>
@date 2020/5/3116:42
 */
export class TimeRegionRequest {
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 结束时间
   */
  endTime?: string
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 商品信息
<AUTHOR> create 2020/3/5 14:28
 */
export class CommodityInfoDTO1 {
  /**
   * 商品skuid
   */
  commoditySkuId: string
  /**
   * 期数ID  (前端需要取这个id做期别id)
   */
  issueId: string
  /**
   * 期别ID(暂时没用)
   */
  stageId: string
  /**
   * 期别标题（期别名称）
   */
  title: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 培训方案名称
   */
  schemeName: string
  /**
   * 方案推荐指数
产品要求为前端写死，该字段暂时无用
   */
  recommendIndex: number
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 销售价格
   */
  price: number
  /**
   * 班级图片
   */
  picture: string
  /**
   * 商品学时
   */
  period: number
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * 方案内展示课程讲师树状对象
<AUTHOR> create 2020/3/5 13:53
 */
export class PreExamLSCourseDTO {
  /**
   * 课程ID
   */
  id: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 权重,表示学时,学分等
   */
  period: number
  /**
   * 课程简介
   */
  abouts: string
  /**
   * 所属课程包编号
   */
  poolId: string
  /**
   * 所属课程类型
1:必修课；2：选修；
   */
  courseType: number
  /**
   * 课程包名称
   */
  poolName: string
  /**
   * 课程内讲师
   */
  teacherList: Array<TeacherSimpleDTO>
}

/**
 * 课程考核信息
@author: eleven
@date: 2020/6/5
 */
export class CourseLearningAssessSettingResponse {
  allSelectedComplete: boolean
  schedule: number
}

/**
 * @author: eleven
@date: 2020/6/6
 */
export class CourseLearningResponse {
  /**
   * 学习方案要求的最少学时
   */
  minTotalPeriod: number
  /**
   * 课程考核要求
   */
  assessSetting: CourseLearningAssessSettingResponse
  learningId: string
  enabled: boolean
  compulsoryPackages: Array<PackageRuleSettingDto>
  optionalPackages: Array<PackageRuleSettingDto>
}

/**
 * @author: eleven
@date: 2020/6/5
 */
export class ExamAssessSettingResponse {
  score: number
}

/**
 * @author: eleven
@date: 2020/6/6
 */
export class ExamLearningResponse {
  /**
   * 考试考核，null表示不设置考核
   */
  assessSetting: ExamAssessSettingResponse
  learningId: string
  enabled: boolean
  name: string
  examPaperId: string
  examTimeLength: number
  examCount: number
  configExamTime: boolean
  beginTime: string
  endTime: string
  passScore: number
  openResolvedExam: boolean
  minSubmitTimeLength: number
  less: boolean
  missScorePattern: number
}

/**
 * @author: eleven
@date: 2020/6/5
 */
export class IssueClassLSAchieveSettingResponse {
  enabledAssess: boolean
  grade: number
  templateId: string
}

export class TeacherSimpleDTO {
  /**
   * 教师ID
   */
  id: string
  /**
   * 教师名称
   */
  name: string
  /**
   * 教师头像
   */
  photo: string
  /**
   * 教师简介
   */
  abouts: string
}

/**
 * 年度对象
 */
export class YearDTO {
  /**
   * 年度id
   */
  id: string
  /**
   * 年度code
   */
  yearCode: number
}

/**
 * lazy批次单的分页对象
<AUTHOR> create 2020/3/17 14:08
 */
export class BatchOrderBillDTO {
  batchNo: string
  /**
   * 主键 发票ID
   */
  billId: string
  /**
   * 发票号
   */
  billNo: string
  /**
   * 发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   */
  invoiceType: number
  /**
   * 是否电子票
   */
  electron: boolean
  /**
   * 发票抬头
   */
  title: string
  /**
   * 统一社会信用代码
   */
  taxpayerNo: string
  /**
   * 开户行
   */
  billBankName: string
  /**
   * 账号
   */
  billAccount: string
  /**
   * 地址
   */
  billAddress: string
  /**
   * 电话
   */
  billPhone: string
  /**
   * 是否非税发票
   */
  noTaxBill: boolean
  /**
   * 发票状态
   */
  billBlueStatus: number
  /**
   * 红票状态
   */
  billRedStatus: number
}

/**
 * lazy批次单的分页对象
<AUTHOR> create 2020/3/17 14:08
 */
export class BatchOrderDTO {
  batchNo: string
  /**
   * 批次单所属单位ID
   */
  unitId: string
  /**
   * 集体缴费单位id
   */
  batchUnitId: string
  /**
   * 集体缴费单位名称
   */
  batchUnitName: string
  /**
   * 批次单总金额
   */
  totalAmount: number
  /**
   * 批次单待下单总金额
   */
  pendingOrderTotalAmount: number
  /**
   * 收款账号id
   */
  receiveAccountId: string
  /**
   * 批次单状态
   */
  status: number
  /**
   * 是否是测试数据
   */
  test: boolean
  /**
   * 批次单应用类型 1:普通批次单,2:换货生成的新批次单
   */
  applyType: number
  /**
   * 支付方式  支付方式 1：线上支付 2：线下支付,-1:表示未支付
   */
  payType: number
  /**
   * 微信支付。支付宝支付
   */
  tradeChannelCode: string
  /**
   * 微信支付。支付宝支付
   */
  tradeChannelCodeName: string
  /**
   * 批次单实付总价
   */
  batchTotalPayAmount: number
  /**
   * 银行交易流水号
   */
  payFlowNo: string
  /**
   * 缴费人次
   */
  people: number
  /**
   * 批次提交成功人次
   */
  successPeople: number
  /**
   * 培训总学时
   */
  trainingHours: string
  /**
   * 批次单创建时间, yyyy-mm-dd 24hh:MM:dd
   */
  createTime: string
  /**
   * 批次单提交时间
   */
  commitTime: string
  /**
   * 发货成功时间 yyyy-mm-dd 24hh:MM:dd
   */
  deliverySuccessTime: string
  /**
   * 完成交易的时间, yyyy-mm-dd 24hh:MM:dd
   */
  completeTime: string
  /**
   * 批次单自动关闭时间
   */
  autoCloseTime: string
  /**
   * 付款时间, yyyy-mm-dd 24hh:MM:dd
   */
  payTime: string
  /**
   * 付款完成时间, yyyy-mm-dd 24hh:MM:dd
   */
  payFinishTime: string
  /**
   * 线下汇款时间
   */
  remittanceCertificateDate: string
  /**
   * 线下汇款凭证图片地址
   */
  remittanceCertificateImagePaths: Array<string>
  /**
   * 买家id
   */
  buyerId: string
  /**
   * 买家姓名
   */
  buyerName: string
  /**
   * 买家电话号码
   */
  buyerPhoneNumber: string
  /**
   * 买家身份证号码
   */
  buyerIdentity: string
  /**
   * 买家登录账户
   */
  buyerLoginInput: string
  /**
   * 退款批次单号
   */
  batchRefundNo: string
  /**
   * 退款类型
   */
  refundType: string
  /**
   * 退款申请人id
   */
  refundApplyUserId: string
  /**
   * 退款申请时间
   */
  refundApplyTime: string
  /**
   * 退款总金额
   */
  refundTotalAmount: number
  /**
   * 退款方式 1.线上 2.线下
   */
  refundWay: number
  /**
   * 退款状态 1.已申请   2.退款成功 3.退款失败
与底层状态不同，只关心最终状态。
如果是拒绝退款就直接清空退款的数据了
   */
  refundStatus: number
  /**
   * 退款完成时间
   */
  refundCompleteTime: string
  /**
   * 是否需要发票
   */
  needBill: boolean
  billList: Array<BatchOrderBillDTO>
  /**
   * 本条记录的创建时间
   */
  recordCreateTime: string
  /**
   * 本条记录的最后更新时间
   */
  recordLastUpdateTime: string
}

/**
 * 退款分页信息对象
<AUTHOR> create 2020/4/14 11:45
 */
export class BatchRefundOrderDTO {
  /**
   * 退款批次单号
   */
  batchRefundNo: string
  /**
   * 退款对应的批次单号
   */
  batchNo: string
  /**
   * 集体缴费单位id
   */
  batchUnitId: string
  /**
   * 集体缴费单位名称
   */
  batchUnitName: string
  /**
   * 交易渠道信息   web、小程序、公众号
   */
  placeChannel: string
  /**
   * 批次单总金额
   */
  totalAmount: number
  /**
   * 批次单实付总价
   */
  batchTotalPayAmount: number
  /**
   * 缴费人次
   */
  people: number
  /**
   * 银行交易流水号
   */
  payFlowNo: string
  /**
   * 支付方式  支付方式 1：线上支付 2：线下支付,-1:表示未支付
   */
  payType: number
  /**
   * 退款总金额
   */
  refundTotalAmount: number
  /**
   * 退款批次单状态
   */
  refundStatus: number
  /**
   * 创建方式
1:系统创建
2:用户创建
3:管理员创建
4:历史迁移
5:外部接口
   */
  createType: number
  /**
   * 退款方式 1.线上 2.线下
   */
  refundWay: number
  /**
   * 批次退款类型
@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderTypeConst
   */
  refundType: number
  /**
   * 退款模式 1：普通退款 2：强制退款
   */
  refundMode: number
  /**
   * 申请人id
   */
  applyUserId: string
  /**
   * 申请人姓名
   */
  applyUserName: string
  /**
   * 退款申请时间
   */
  applyTime: string
  /**
   * 退款申请审核人
   */
  auditUserId: string
  /**
   * 审核人姓名
   */
  auditUserName: string
  /**
   * 退款申请审核时间
   */
  auditTime: string
  /**
   * 取消退款人id
   */
  cancelUserId: string
  /**
   * 取消退款人姓名
   */
  cancelUserName: string
  /**
   * 取消退款时间
   */
  cancelTime: string
  /**
   * 资源回收时间
   */
  recycledTime: string
  /**
   * 批次退款完成时间
   */
  refundCompleteTime: string
  /**
   * 财务退款审核人id
   */
  financeAuditUserId: string
  /**
   * 财务退款审核人名称
   */
  financeAuditUserName: string
  /**
   * 财务审核同意/拒绝放款时间
   */
  financeAuditTime: string
  /**
   * 买家id
   */
  buyerId: string
  /**
   * 买家姓名
   */
  buyerName: string
  /**
   * 买家电话号码
   */
  buyerPhoneNumber: string
  /**
   * 买家身份证号码
   */
  buyerIdentity: string
  /**
   * 买家登录账户
   */
  buyerLoginInput: string
}

/**
 * 退款详细
<AUTHOR> create 2020/4/14 14:07
 */
export class BatchRefundOrderDetailsDTO {
  /**
   * 退款原因id
   */
  refundReasonId: string
  /**
   * 退款原因
   */
  refundReason: string
  /**
   * 申请退款描述
   */
  refundDescription: string
  /**
   * 拒绝申请原因描述
   */
  refuseApplyDesc: string
  /**
   * 拒绝退款原因描述
   */
  refuseRefundDesc: string
  /**
   * 批次退款取消原因
   */
  cancelReason: string
  /**
   * 退款批次单号
   */
  batchRefundNo: string
  /**
   * 退款对应的批次单号
   */
  batchNo: string
  /**
   * 集体缴费单位id
   */
  batchUnitId: string
  /**
   * 集体缴费单位名称
   */
  batchUnitName: string
  /**
   * 交易渠道信息   web、小程序、公众号
   */
  placeChannel: string
  /**
   * 批次单总金额
   */
  totalAmount: number
  /**
   * 批次单实付总价
   */
  batchTotalPayAmount: number
  /**
   * 缴费人次
   */
  people: number
  /**
   * 银行交易流水号
   */
  payFlowNo: string
  /**
   * 支付方式  支付方式 1：线上支付 2：线下支付,-1:表示未支付
   */
  payType: number
  /**
   * 退款总金额
   */
  refundTotalAmount: number
  /**
   * 退款批次单状态
   */
  refundStatus: number
  /**
   * 创建方式
1:系统创建
2:用户创建
3:管理员创建
4:历史迁移
5:外部接口
   */
  createType: number
  /**
   * 退款方式 1.线上 2.线下
   */
  refundWay: number
  /**
   * 批次退款类型
@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderTypeConst
   */
  refundType: number
  /**
   * 退款模式 1：普通退款 2：强制退款
   */
  refundMode: number
  /**
   * 申请人id
   */
  applyUserId: string
  /**
   * 申请人姓名
   */
  applyUserName: string
  /**
   * 退款申请时间
   */
  applyTime: string
  /**
   * 退款申请审核人
   */
  auditUserId: string
  /**
   * 审核人姓名
   */
  auditUserName: string
  /**
   * 退款申请审核时间
   */
  auditTime: string
  /**
   * 取消退款人id
   */
  cancelUserId: string
  /**
   * 取消退款人姓名
   */
  cancelUserName: string
  /**
   * 取消退款时间
   */
  cancelTime: string
  /**
   * 资源回收时间
   */
  recycledTime: string
  /**
   * 批次退款完成时间
   */
  refundCompleteTime: string
  /**
   * 财务退款审核人id
   */
  financeAuditUserId: string
  /**
   * 财务退款审核人名称
   */
  financeAuditUserName: string
  /**
   * 财务审核同意/拒绝放款时间
   */
  financeAuditTime: string
  /**
   * 买家id
   */
  buyerId: string
  /**
   * 买家姓名
   */
  buyerName: string
  /**
   * 买家电话号码
   */
  buyerPhoneNumber: string
  /**
   * 买家身份证号码
   */
  buyerIdentity: string
  /**
   * 买家登录账户
   */
  buyerLoginInput: string
}

/**
 * 类说明
<AUTHOR> create 2020/4/14 11:49
 */
export class CommodityInfoDTO {
  /**
   * 商品id
   */
  commodityId: string
  /**
   * 所属培训方案名称
   */
  schemeName: string
  /**
   * 所属培训方案id
   */
  schemeId: string
  /**
   * 所属期别(期数)名称
   */
  issueTitle: string
  /**
   * 所属期别id
   */
  stageId: string
  /**
   * 所属期数id
   */
  issueId: string
  /**
   * 子订单商品年度 属性key
   */
  yearPropertyId: string
  /**
   * 子订单商品年度
   */
  year: string
  /**
   * 子订单商品年度 年度名称
   */
  yearName: string
  /**
   * 子订单商品专业id
   */
  professionId: string
  /**
   * 子订单商品专业id 属性key
   */
  professionPropertyId: string
  /**
   * 子订单商品专业名称
   */
  professionName: string
  /**
   * 子订单商品行业id 属性key
   */
  professionTypePropertyId: string
  /**
   * 子订单商品行业id
   */
  professionTypeId: string
  /**
   * 子订单商品类别、 行业名称
   */
  professionTypeName: string
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训类别名称
   */
  trainingTypeName: string
  /**
   * 培训对象
   */
  traineesId: string
  /**
   * 培训对象名称
   */
  traineesName: string
  /**
   * 岗位类别
   */
  jobCategoryId: string
  /**
   * 岗位类别名称
   */
  jobCategoryName: string
  /**
   * 单位类别
   */
  unitCategoryId: string
  /**
   * 单位类别名称
   */
  unitCategoryName: string
  /**
   * 培训学时
   */
  trainingHours: string
}

/**
 * 期数信息
对应前端的期别（原来叫期别，后面期别隐藏了，后端叫期数）
 */
export class IssueDTO {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 期数ID（对应前端期别）
   */
  issueId: string
  /**
   * 商品SkuId
   */
  commoditySkuId: string
  /**
   * 标题
   */
  title: string
  /**
   * 学习开始时间
   */
  startTime: string
  /**
   * 学习结束时间
   */
  endTime: string
  /**
   * 销售价格
   */
  price: number
  /**
   * 开放报名开始时间
   */
  upPlainTime: string
  /**
   * 开放报名结束时间
   */
  downPlainTime: string
}

/**
 * 退款统计信息
<AUTHOR> create 2020/4/14 14:14
 */
export class LazyRefunOrderStatisticsDTO {
  /**
   * 退款总笔数
   */
  refunOrderCount: number
  /**
   * 退款总额
   */
  totalAmountCount: number
  /**
   * 补考总笔数
   */
  totalMakeUpCount: number
  /**
   * 补考总额
   */
  totalMakeUpAmountCount: number
}

/**
 * lazy订单的分页对象
<AUTHOR> create 2020/3/17 14:08
 */
export class OrderDTO {
  /**
   * 订单所属平台ID
   */
  platformId: string
  /**
   * 订单所属平台版本ID
   */
  platformVersionId: string
  /**
   * 订单所属项目ID
   */
  projectId: string
  /**
   * 订单所属子项目ID
   */
  subProjectId: string
  /**
   * 订单所属组织机构ID
   */
  organizationId: string
  /**
   * 机构ID
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 课件供应商id
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称
   */
  coursewareSupplierName: string
  /**
   * 渠道商id
   */
  channelVendorId: string
  /**
   * 渠道商名称
   */
  channelVendorName: string
  /**
   * 参训单位ID
   */
  participatingUnitId: string
  /**
   * 参训单位名称
   */
  participatingUnitName: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 子订单对象
   */
  subOrderList: Array<SubOrderDTO>
  /**
   * 购买渠道code
自主报名、集体缴费、导入开通
@see PurchaseChannelTypes
用户自主购买  1;
集体缴费  2;
管理员导入  3;
   */
  purchaseChannelCode: number
  /**
   * 报名渠道（终端）code
web、小程序、公众号
@see PurchaseChannelTerminalCodes
/**
Web端 &quot;Web&quot;;
IOS端 &quot;IOS&quot;;
安卓端  &quot;Android&quot;;
微信小程序  &quot;WechatMini&quot;;
微信公众号  &quot;WechatOfficial&quot;;
   */
  placeChannelCode: string
  /**
   * 订单总金额
   */
  totalAmount: number
  /**
   * 主订单状态
&quot;等待付款&quot;, 1
&quot;等待卖家确认款项&quot;, 2
&quot;支付中&quot;, 8
&quot;支付成功&quot;, 3
&quot;发货中&quot;, 4
&quot;发货部分失败&quot;, 9
&quot;发货失败&quot;,10
&quot;发货已完成&quot;, 5
&quot;交易成功&quot;, 6
&quot;交易关闭&quot;, 7
   */
  status: number
  /**
   * 订单应用类型
@see OrderTypes
1:普通订单,2:批次单
   */
  applyType: number
  /**
   * 交易关闭类型
@see OrderClosedTypes
未关闭 0;
买家取消 1;
买家取消 2;
超时取消 3;
批次关联取消 4;
   */
  closedType: number
  /**
   * 交易关闭原因ID
   */
  reasonId: string
  /**
   * 交易关闭原因说明
   */
  reason: string
  /**
   * 失败信息
   */
  failMessage: string
  /**
   * 订单创建时间, yyyy-mm-dd 24hh:MM:dd
   */
  createTime: string
  /**
   * 订单发货时间 yyyy-mm-dd 24hh:MM:dd
   */
  deliverTime: string
  /**
   * 订单发货完成时间 yyyy-mm-dd 24hh:MM:dd
   */
  deliveredTime: string
  /**
   * 完成交易的时间, yyyy-mm-dd 24hh:MM:dd
   */
  completeTime: string
  /**
   * 是否为补考
   */
  makeUpExam: boolean
  /**
   * 批次单号
   */
  batchNo: string
  /**
   * 集体缴费单位id
   */
  batchUnitId: string
  /**
   * 集体缴费单位名称
   */
  batchUnitName: string
  /**
   * 交易流水号
   */
  payFlowNo: string
  /**
   * 收款账号id
   */
  receiveAccountId: string
  /**
   * 支付方式
支付方式 1：线上支付 2：线下支付,-1:表示未支付
@see PayType
   */
  payType: number
  /**
   * 支付渠道Id 微信支付、支付宝支付
   */
  payChannelId: string
  /**
   * 支付渠道名称 微信支付、支付宝支付
   */
  payChannelName: string
  /**
   * 发券机构（属地人社局）（培训券的信息）
   */
  publishOrgName: string
  /**
   * 培训券号
   */
  couponCode: string
  /**
   * 主订单实付总价
   */
  orderTotalPayAmount: number
  /**
   * 付款时间, yyyy-mm-dd 24hh:MM:dd
   */
  payTime: string
  /**
   * 付款完成时间, yyyy-mm-dd 24hh:MM:dd
   */
  payFinishTime: string
  /**
   * 卖家id
   */
  sellerId: string
  /**
   * 卖家名称
   */
  sellerName: string
  /**
   * 买家id
   */
  buyerId: string
  /**
   * 买家姓名
   */
  buyerName: string
  /**
   * 买家电话号码
   */
  buyerPhoneNumber: string
  /**
   * 买家身份证号码
   */
  buyerIdentity: string
  /**
   * 买家登录账户
   */
  buyerLoginInput: string
  /**
   * 买家所属单位id
   */
  buyerBelongUnitId: string
  /**
   * 买家所属单位名称
   */
  buyerBelongUnitName: string
  /**
   * 退款订单号
   */
  refundOrderNo: string
  /**
   * 退款类型
   */
  refundType: string
  /**
   * 退款申请人id
   */
  refundApplyUserId: string
  /**
   * 退款申请时间
   */
  refundApplyTime: string
  /**
   * 退款原因id
   */
  refundReasonId: string
  /**
   * 退款总金额
   */
  refundTotalAmount: number
  /**
   * 退款方式 1.线上 2.线下
   */
  refundWay: number
  /**
   * 退款状态 1.已申请   2.退款成功 3.退款失败
与底层状态不同，只关心最终状态。
如果是拒绝退款就直接清空退款的数据了
   */
  refundStatus: number
  /**
   * 退款完成时间
   */
  refundCompleteTime: string
  /**
   * 是否需要发票
   */
  needBill: boolean
  /**
   * 主键 发票ID
   */
  billId: string
  /**
   * 发票号
   */
  billNo: string
  /**
   * 是否电子票
   */
  electron: boolean
  /**
   * 发票抬头
   */
  title: string
  /**
   * 统一社会信用代码
   */
  taxpayerNo: string
  /**
   * 开户行
   */
  billBankName: string
  /**
   * 账号
   */
  billAccount: string
  /**
   * 地址
   */
  billAddress: string
  /**
   * 电话
   */
  billPhone: string
  /**
   * 是否非税发票
   */
  noTaxBill: boolean
  /**
   * 发票状态
   */
  billStatus: number
  /**
   * 红票状态
   */
  redBillStatus: number
  /**
   * 本条记录的创建时间
   */
  recordCreateTime: string
  /**
   * 本条记录的最后更新时间
   */
  recordLastUpdateTime: string
  /**
   * 是否是测试数据
   */
  test: boolean
}

/**
 * 订单统计信息对象
<AUTHOR> create 2020/3/13 9:27
 */
export class OrderStatisticsDTO {
  /**
   * 订单成交笔数
   */
  orderCount: number
  /**
   * 订单成交总额
   */
  totalAmount: number
  /**
   * 补考成交总额
   */
  makeUpExamAmount: number
  /**
   * 补考成交总额
   */
  makeUpExamCount: number
}

/**
 * 方案详情对象
 */
export class PreExamLSInfoDTO {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 培训方案名称
   */
  name: string
  /**
   * 封面图片地址
   */
  picture: string
  /**
   * 培训方案状态
@see TrainingConfigStatusConst
   */
  status: number
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 创建人ID
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 方案推荐指数
   */
  recommendIndex: number
  /**
   * 课程学习方式信息
分页接口不返回
   */
  courseLearning: CourseLearningResponse
  /**
   * 考试学习方式信息
分页接口不返回
   */
  examLearning: ExamLearningResponse
  /**
   * 试题练习学习方式信息
分页接口不返回
   */
  questionPracticeLearning: QuestionPracticeLearningDto
  /**
   * 培训班成果设置
分页接口不返回
   */
  achieveSetting: IssueClassLSAchieveSettingResponse
  /**
   * 方案列表的讲师信息 一般是方案上外挂的讲师。
详情接口暂时不返回
   */
  teachers: Array<TeacherSimpleDTO>
  /**
   * 销售价格
   */
  price: number
  /**
   * 商品skuid
   */
  commoditySkuId: string
  /**
   * 唯一的期数的id
   */
  issueId: string
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * 退款分页信息对象
<AUTHOR> create 2020/4/14 11:45
 */
export class RefundOrderDTO {
  /**
   * 退款订单号
   */
  refundOrderNo: string
  /**
   * 退款对应的主订单号
   */
  orderNo: string
  /**
   * 退款对应的子订单号
   */
  subOrderNo: string
  /**
   * 集体缴费批次号
   */
  batchNo: string
  /**
   * 集体缴费单位id
   */
  batchUnitId: string
  /**
   * 集体缴费单位名称
   */
  batchUnitName: string
  /**
   * 交易渠道信息   web、小程序、公众号
   */
  placeChannel: string
  /**
   * 订单总金额
   */
  totalAmount: number
  /**
   * 主订单实付总价
   */
  orderTotalPayAmount: number
  /**
   * 培训学时
   */
  trainingHours: string
  /**
   * 卖家id
   */
  sellerId: string
  /**
   * 卖家名称
   */
  sellerName: string
  /**
   * 买家id
   */
  buyerId: string
  /**
   * 买家姓名
   */
  buyerName: string
  /**
   * 买家电话号码
   */
  buyerPhoneNumber: string
  /**
   * 买家身份证号码
   */
  buyerIdentity: string
  /**
   * 买家登录账户
   */
  buyerLoginInput: string
  /**
   * 买家所属单位id
   */
  buyerBelongUnitId: string
  /**
   * 买家所属单位名称
   */
  buyerBelongUnitName: string
  /**
   * 退款商品信息
   */
  commodityInfoDTO: CommodityInfoDTO
  /**
   * 创建方式
1:系统创建
2:用户创建
3:管理员创建
4:历史迁移
5:外部接口
   */
  createType: number
  /**
   * 商品标价
   */
  subOrderLabelPrice: number
  /**
   * 商品购买数量
   */
  purchaseQuantity: number
  /**
   * 银行交易流水号
   */
  bankTrasactionNumber: string
  /**
   * 退款单是否补考
   */
  makeUpExam: boolean
  /**
   * 退款对应订单的单位id
   */
  orderUnitId: string
  /**
   * 退款总金额
   */
  refundTotalAmount: number
  /**
   * 退款订单状态
   */
  refundStatus: number
  /**
   * 退款方式 1.线上 2.线下
   */
  refundWay: number
  /**
   * 退款模式 1：普通退款 2：强制退款
   */
  refundMode: number
  /**
   * 退款类型
   */
  refundType: number
  /**
   * 申请人id
   */
  applyUserId: string
  /**
   * 申请人姓名
   */
  applyUserName: string
  /**
   * 退款原因id
   */
  refundReasonId: string
  /**
   * 退款申请时间
   */
  applyTime: string
  /**
   * 退款申请审核人
   */
  auditUserId: string
  /**
   * 审核人姓名
   */
  auditUserName: string
  /**
   * 退款申请审核时间
   */
  auditTime: string
  /**
   * 取消退款人id
   */
  cancelUserId: string
  /**
   * 取消退款人姓名
   */
  cancelUserName: string
  /**
   * 取消退款时间
   */
  cancelTime: string
  /**
   * 资源回收时间
   */
  recycledTime: string
  /**
   * 财务退款审核人id
   */
  financeAuditUserId: string
  /**
   * 财务退款审核人名称
   */
  financeAuditUserName: string
  /**
   * 财务审核同意/拒绝放款时间
   */
  financeAuditTime: string
  /**
   * 退款完成时间
   */
  refundCompleteTime: string
  /**
   * 是否是测试数据
   */
  test: boolean
}

/**
 * 退款详细
<AUTHOR> create 2020/4/14 14:07
 */
export class RefundOrderDetailsDTO {
  /**
   * 退款原因
   */
  reason: string
  /**
   * 申请退款描述
   */
  description: string
  /**
   * 拒绝申请原因描述
   */
  refuseApplyDesc: string
  /**
   * 拒绝退款原因描述
   */
  refuseRefundDesc: string
  /**
   * 退款订单号
   */
  refundOrderNo: string
  /**
   * 退款对应的主订单号
   */
  orderNo: string
  /**
   * 退款对应的子订单号
   */
  subOrderNo: string
  /**
   * 集体缴费批次号
   */
  batchNo: string
  /**
   * 集体缴费单位id
   */
  batchUnitId: string
  /**
   * 集体缴费单位名称
   */
  batchUnitName: string
  /**
   * 交易渠道信息   web、小程序、公众号
   */
  placeChannel: string
  /**
   * 订单总金额
   */
  totalAmount: number
  /**
   * 主订单实付总价
   */
  orderTotalPayAmount: number
  /**
   * 培训学时
   */
  trainingHours: string
  /**
   * 卖家id
   */
  sellerId: string
  /**
   * 卖家名称
   */
  sellerName: string
  /**
   * 买家id
   */
  buyerId: string
  /**
   * 买家姓名
   */
  buyerName: string
  /**
   * 买家电话号码
   */
  buyerPhoneNumber: string
  /**
   * 买家身份证号码
   */
  buyerIdentity: string
  /**
   * 买家登录账户
   */
  buyerLoginInput: string
  /**
   * 买家所属单位id
   */
  buyerBelongUnitId: string
  /**
   * 买家所属单位名称
   */
  buyerBelongUnitName: string
  /**
   * 退款商品信息
   */
  commodityInfoDTO: CommodityInfoDTO
  /**
   * 创建方式
1:系统创建
2:用户创建
3:管理员创建
4:历史迁移
5:外部接口
   */
  createType: number
  /**
   * 商品标价
   */
  subOrderLabelPrice: number
  /**
   * 商品购买数量
   */
  purchaseQuantity: number
  /**
   * 银行交易流水号
   */
  bankTrasactionNumber: string
  /**
   * 退款单是否补考
   */
  makeUpExam: boolean
  /**
   * 退款对应订单的单位id
   */
  orderUnitId: string
  /**
   * 退款总金额
   */
  refundTotalAmount: number
  /**
   * 退款订单状态
   */
  refundStatus: number
  /**
   * 退款方式 1.线上 2.线下
   */
  refundWay: number
  /**
   * 退款模式 1：普通退款 2：强制退款
   */
  refundMode: number
  /**
   * 退款类型
   */
  refundType: number
  /**
   * 申请人id
   */
  applyUserId: string
  /**
   * 申请人姓名
   */
  applyUserName: string
  /**
   * 退款原因id
   */
  refundReasonId: string
  /**
   * 退款申请时间
   */
  applyTime: string
  /**
   * 退款申请审核人
   */
  auditUserId: string
  /**
   * 审核人姓名
   */
  auditUserName: string
  /**
   * 退款申请审核时间
   */
  auditTime: string
  /**
   * 取消退款人id
   */
  cancelUserId: string
  /**
   * 取消退款人姓名
   */
  cancelUserName: string
  /**
   * 取消退款时间
   */
  cancelTime: string
  /**
   * 资源回收时间
   */
  recycledTime: string
  /**
   * 财务退款审核人id
   */
  financeAuditUserId: string
  /**
   * 财务退款审核人名称
   */
  financeAuditUserName: string
  /**
   * 财务审核同意/拒绝放款时间
   */
  financeAuditTime: string
  /**
   * 退款完成时间
   */
  refundCompleteTime: string
  /**
   * 是否是测试数据
   */
  test: boolean
}

/**
 * 子订单对象
<AUTHOR> create 2020/3/17 14:09
 */
export class SubOrderDTO {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 成交单价
   */
  subOrderDealPrice: number
  /**
   * 子订单实付总价
   */
  subOrderTotalPayAmount: number
  /**
   * 购买数量
   */
  subOrderPurchaseQuantity: number
  /**
   * 子订单状态
1 - 待付款
2 - 未发货
3 - 发货中
4 - 已发货
5 - 买家已签收
6 - 已换货
7 - 退货中
8 - 已退货
9 - 已取消
@see SubOrderStatus
   */
  subOrderStatus: number
  /**
   * 子订单发货的时间, yyyy-mm-dd 24hh:MM:dd
   */
  subOrderDeliverTime: string
  /**
   * 子订单发货完成时间, yyyy-mm-dd 24hh:MM:dd
   */
  subOrderDeliveredTime: string
  /**
   * 完成交易的时间, yyyy-mm-dd 24hh:MM:dd
   */
  subOrderCompleteTime: string
  /**
   * 子订单最后一次状态变更的时间
   */
  lastUpdateTime: string
  /**
   * 商品id
   */
  productId: string
  /**
   * 商品标价
   */
  subOrderLabelPrice: number
  /**
   * 所属培训方案id
   */
  schemeId: string
  /**
   * 方案展示图片
   */
  schemePicture: string
  /**
   * 所属培训方案名称
   */
  schemeName: string
  /**
   * 所属期别(期数)名称
   */
  issueTitle: string
  /**
   * 培训学时
   */
  trainingHours: string
  /**
   * 商品创建人id
   */
  productCreateUserId: string
  /**
   * 所属期别id
   */
  stageId: string
  /**
   * 所属期数id
   */
  issueId: string
  /**
   * 期数开始时间
   */
  issueStartTime: string
  /**
   * 期数结束时间
   */
  issueEndTime: string
  /**
   * 期数线下考试时间
   */
  issueOfflineExamTime: string
  /**
   * 适用人群
   */
  suitableCrowNames: Array<string>
  /**
   * 培训类别Id path
   */
  trainingTypeIdPath: string
  /**
   * 培训类别
   */
  trainingTypeName: string
  /**
   * 培训类别名称/name1/name2/name3
   */
  trainingTypeNamePath: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeName: string
  /**
   * 培训工种类别/培训工种id path
用于培训类别联合工种多条件查询
   */
  workTypeIdPath: string
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

export class LibraryWaySettingDto {
  libraryIds: Array<string>
  recursive: boolean
}

export class PackageRuleSettingDto {
  packageId: string
  limit: boolean
  maxPeriod: number
}

export class QuestionPracticeLearningDto {
  learningId: string
  enabled: boolean
  fetchWay: number
  libraryWaySetting: LibraryWaySettingDto
  tagsWaySetting: TagWaySettingDto
}

export class TagWaySettingDto {
  tagIds: Array<string>
}

export class ChapterDTO {
  relationId: string
  parentRelationId: string
  sort: number
  id: string
  type: string
  name: string
  code: string
  enabled: boolean
}

export class IndustryDTO {
  relationId: string
  majorModelList: Array<MajorDTO>
  id: string
  type: string
  name: string
  code: string
  enabled: boolean
}

export class MajorDTO {
  relationId: string
  sort: number
  id: string
  type: string
  name: string
  code: string
  enabled: boolean
}

export class MajorMapChapterListDTO {
  majorRelationId: string
  chapterList: Array<ChapterDTO>
}

export class BatchOrderDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BatchOrderDTO>
}

export class BatchRefundOrderDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BatchRefundOrderDTO>
}

export class OrderDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OrderDTO>
}

export class PreExamLSInfoDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PreExamLSInfoDTO>
}

export class RefundOrderDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<RefundOrderDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param requestParams 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchOrder(
    requestParams: BatchOrderQueryParam,
    query: DocumentNode = GraphqlImporter.exportBatchOrder,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { requestParams },
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param requestParams 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchRefundOrder(
    requestParams: BatchRefundOrderParam,
    query: DocumentNode = GraphqlImporter.exportBatchRefundOrder,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { requestParams },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出订单管理
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrder(
    param: OrderQueryDTO,
    query: DocumentNode = GraphqlImporter.exportOrder,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出订单管理
   * 为渠道商提供接口
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrderForChannelVendor(
    param: OrderQueryDTO,
    query: DocumentNode = GraphqlImporter.exportOrderForChannelVendor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出订单管理
   * 为参训单位提供接口
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrderForParticipatingUnit(
    param: OrderQueryDTO,
    query: DocumentNode = GraphqlImporter.exportOrderForParticipatingUnit,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出订单管理
   * 为机构提供接口
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrderForTrainingInstitution(
    param: OrderQueryDTO,
    query: DocumentNode = GraphqlImporter.exportOrderForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出对账管理
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliation(
    param: OrderQueryDTO,
    query: DocumentNode = GraphqlImporter.exportReconciliation,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出对账管理
   * 为渠道商提供接口
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliationForChannelVendor(
    param: OrderQueryDTO,
    query: DocumentNode = GraphqlImporter.exportReconciliationForChannelVendor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出对账管理
   * 为机构提供接口
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliationForTrainingInstitution(
    param: OrderQueryDTO,
    query: DocumentNode = GraphqlImporter.exportReconciliationForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param requestParams 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportRefundOrder(
    requestParams: RefundOrderParam,
    query: DocumentNode = GraphqlImporter.exportRefundOrder,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { requestParams },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取某个专业关系节点下一级章节子节点
   * @param majorRelationId 专业关系子节点
   * @return 章节列表
   * @param query 查询 graphql 语法文档
   * @param majorRelationId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllChildChapterByMajor(
    majorRelationId: string,
    query: DocumentNode = GraphqlImporter.findAllChildChapterByMajor,
    operation?: string
  ): Promise<Response<Array<ChapterDTO>>> {
    return commonRequestApi<Array<ChapterDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { majorRelationId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取指定多个专业关系下章节节点及子节点
   * @param majorRelationIdList 专业关系节点列表
   * @return 专业关系节点对应章节列表
   * @param query 查询 graphql 语法文档
   * @param majorRelationIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllChildChapterByMajorList(
    majorRelationIdList: Array<string>,
    query: DocumentNode = GraphqlImporter.findAllChildChapterByMajorList,
    operation?: string
  ): Promise<Response<Array<MajorMapChapterListDTO>>> {
    return commonRequestApi<Array<MajorMapChapterListDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { majorRelationIdList },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取所有行业及专业
   * @return 行业专业关系树
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllIndustryRelationList(
    query: DocumentNode = GraphqlImporter.findAllIndustryRelationList,
    operation?: string
  ): Promise<Response<Array<IndustryDTO>>> {
    return commonRequestApi<Array<IndustryDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 延迟查询退款订单统计信息
   * @param queryParam
   * @return
   * @param query 查询 graphql 语法文档
   * @param queryParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBatchRefundStatistics(
    queryParam: BatchRefundOrderParam,
    query: DocumentNode = GraphqlImporter.getBatchRefundStatistics,
    operation?: string
  ): Promise<Response<LazyRefunOrderStatisticsDTO>> {
    return commonRequestApi<LazyRefunOrderStatisticsDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { queryParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据期别商品id查询期别商品信息
   * @param query 查询 graphql 语法文档
   * @param commoditySkuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getIssueByCommoditySkuId(
    commoditySkuId: string,
    query: DocumentNode = GraphqlImporter.getIssueByCommoditySkuId,
    operation?: string
  ): Promise<Response<CommodityInfoDTO1>> {
    return commonRequestApi<CommodityInfoDTO1>(
      SERVER_URL,
      {
        query: query,
        variables: { commoditySkuId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询我的订单
   * @param query 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getMyOrder(
    orderNo: string,
    query: DocumentNode = GraphqlImporter.getMyOrder,
    operation?: string
  ): Promise<Response<OrderDTO>> {
    return commonRequestApi<OrderDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { orderNo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * lazy查询订单
   * 查询二次清洗表，一般用在管理后台
   * @param query 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOrder(
    orderNo: string,
    query: DocumentNode = GraphqlImporter.getOrder,
    operation?: string
  ): Promise<Response<OrderDTO>> {
    return commonRequestApi<OrderDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { orderNo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据方案id查询方案信息
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPreExamLSById(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.getPreExamLSById,
    operation?: string
  ): Promise<Response<PreExamLSInfoDTO>> {
    return commonRequestApi<PreExamLSInfoDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 延迟查询退款订单详情
   * @param refunOrderNo
   * @return
   * @param query 查询 graphql 语法文档
   * @param refunOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRefundOrder(
    refunOrderNo: string,
    query: DocumentNode = GraphqlImporter.getRefundOrder,
    operation?: string
  ): Promise<Response<RefundOrderDetailsDTO>> {
    return commonRequestApi<RefundOrderDetailsDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { refunOrderNo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * lazy查询订单
   * 查询二次清洗表，一般用在管理后台
   * @param query 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async lazyBatchGetOrder(
    batchNo: string,
    query: DocumentNode = GraphqlImporter.lazyBatchGetOrder,
    operation?: string
  ): Promise<Response<BatchOrderDTO>> {
    return commonRequestApi<BatchOrderDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { batchNo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 延迟查询退款订单详情
   * @param refunOrderNo
   * @return
   * @param query 查询 graphql 语法文档
   * @param refunOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async lazyBatchGetRefundOrder(
    refunOrderNo: string,
    query: DocumentNode = GraphqlImporter.lazyBatchGetRefundOrder,
    operation?: string
  ): Promise<Response<BatchRefundOrderDetailsDTO>> {
    return commonRequestApi<BatchRefundOrderDetailsDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { refunOrderNo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * lazy查询订单统计
   * 查询二次清洗表，一般用在管理后台
   * @param query 查询 graphql 语法文档
   * @param queryParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async lazyBatchOrderStatistics(
    queryParam: BatchOrderQueryParam,
    query: DocumentNode = GraphqlImporter.lazyBatchOrderStatistics,
    operation?: string
  ): Promise<Response<OrderStatisticsDTO>> {
    return commonRequestApi<OrderStatisticsDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { queryParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * lazy查询订单分页
   * 查询二次清洗表，一般用在管理后台
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async lazyBatchPageOrder(
    params: { page?: Page; queryParam?: BatchOrderQueryParam },
    query: DocumentNode = GraphqlImporter.lazyBatchPageOrder,
    operation?: string
  ): Promise<Response<BatchOrderDTOPage>> {
    return commonRequestApi<BatchOrderDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 延迟查询退款订单分页
   * @param page
   * @param queryParam
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async lazyBatchPageRefundOrder(
    params: { page?: Page; queryParam?: BatchRefundOrderParam },
    query: DocumentNode = GraphqlImporter.lazyBatchPageRefundOrder,
    operation?: string
  ): Promise<Response<BatchRefundOrderDTOPage>> {
    return commonRequestApi<BatchRefundOrderDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据期别商品id集合查询期别商品信息
   * @return
   * @param query 查询 graphql 语法文档
   * @param commoditySkuIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listIssueByCommoditySkuIds(
    commoditySkuIds: Array<string>,
    query: DocumentNode = GraphqlImporter.listIssueByCommoditySkuIds,
    operation?: string
  ): Promise<Response<Array<CommodityInfoDTO1>>> {
    return commonRequestApi<Array<CommodityInfoDTO1>>(
      SERVER_URL,
      {
        query: query,
        variables: { commoditySkuIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据方案id查询期别商品信息
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listIssueBySchemeId(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.listIssueBySchemeId,
    operation?: string
  ): Promise<Response<Array<IssueDTO>>> {
    return commonRequestApi<Array<IssueDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据方案id 获取 方案内课程以及讲师
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listPreExamLSCourse(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.listPreExamLSCourse,
    operation?: string
  ): Promise<Response<Array<PreExamLSCourseDTO>>> {
    return commonRequestApi<Array<PreExamLSCourseDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取所有商品的年度
   * @deprecated 目前补贴未用到，暂未提供
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listYear(
    query: DocumentNode = GraphqlImporter.listYear,
    operation?: string
  ): Promise<Response<Array<YearDTO>>> {
    return commonRequestApi<Array<YearDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询订单分页
   * 查询二次清洗表，一般用在管理后台
   * 为渠道商提供接口
   * @param page
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageForChannelVendor(
    params: { page?: Page; param?: OrderQueryDTO },
    query: DocumentNode = GraphqlImporter.pageForChannelVendor,
    operation?: string
  ): Promise<Response<OrderDTOPage>> {
    return commonRequestApi<OrderDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询订单分页
   * 查询二次清洗表，一般用在管理后台
   * 为参训单位提供接口
   * @param page
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageForParticipatingUnit(
    params: { page?: Page; param?: OrderQueryDTO },
    query: DocumentNode = GraphqlImporter.pageForParticipatingUnit,
    operation?: string
  ): Promise<Response<OrderDTOPage>> {
    return commonRequestApi<OrderDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询订单分页
   * 查询二次清洗表，一般用在管理后台
   * 为机构提供接口
   * @param page
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageForTrainingInstitution(
    params: { page?: Page; param?: OrderQueryDTO },
    query: DocumentNode = GraphqlImporter.pageForTrainingInstitution,
    operation?: string
  ): Promise<Response<OrderDTOPage>> {
    return commonRequestApi<OrderDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询我的订单分页(多用于学员侧查询，只会查询出自己的订单)
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageMyOrder(
    params: { page?: Page; param?: MyOrderQueryDTO },
    query: DocumentNode = GraphqlImporter.pageMyOrder,
    operation?: string
  ): Promise<Response<OrderDTOPage>> {
    return commonRequestApi<OrderDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询订单分页
   * 查询二次清洗表，一般用在管理后台
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOrder(
    params: { page?: Page; param?: OrderQueryDTO },
    query: DocumentNode = GraphqlImporter.pageOrder,
    operation?: string
  ): Promise<Response<OrderDTOPage>> {
    return commonRequestApi<OrderDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询已发布的培训方案分页(附带教师信息)
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pagePutawayPreExamLS(
    params: { page?: Page; query?: PreExamLSQueryDTO },
    query: DocumentNode = GraphqlImporter.pagePutawayPreExamLS,
    operation?: string
  ): Promise<Response<PreExamLSInfoDTOPage>> {
    return commonRequestApi<PreExamLSInfoDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 延迟查询退款订单分页
   * @param page
   * @param queryParam
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageRefundOrder(
    params: { page?: Page; queryParam?: RefundOrderParam },
    query: DocumentNode = GraphqlImporter.pageRefundOrder,
    operation?: string
  ): Promise<Response<RefundOrderDTOPage>> {
    return commonRequestApi<RefundOrderDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * lazy查询订单统计
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticsOrder(
    param: OrderQueryDTO,
    query: DocumentNode = GraphqlImporter.statisticsOrder,
    operation?: string
  ): Promise<Response<OrderStatisticsDTO>> {
    return commonRequestApi<OrderStatisticsDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * lazy查询订单统计
   * 为渠道商提供接口
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticsOrderForChannelVendor(
    param: OrderQueryDTO,
    query: DocumentNode = GraphqlImporter.statisticsOrderForChannelVendor,
    operation?: string
  ): Promise<Response<OrderStatisticsDTO>> {
    return commonRequestApi<OrderStatisticsDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * lazy查询订单统计
   * 为参训单位提供接口
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticsOrderForParticipatingUnit(
    param: OrderQueryDTO,
    query: DocumentNode = GraphqlImporter.statisticsOrderForParticipatingUnit,
    operation?: string
  ): Promise<Response<OrderStatisticsDTO>> {
    return commonRequestApi<OrderStatisticsDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * lazy查询订单统计
   * 为机构提供接口
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticsOrderForTrainingInstitution(
    param: OrderQueryDTO,
    query: DocumentNode = GraphqlImporter.statisticsOrderForTrainingInstitution,
    operation?: string
  ): Promise<Response<OrderStatisticsDTO>> {
    return commonRequestApi<OrderStatisticsDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 延迟查询退款订单统计信息
   * @param queryParam
   * @return
   * @param query 查询 graphql 语法文档
   * @param queryParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticsRefundOrder(
    queryParam: RefundOrderParam,
    query: DocumentNode = GraphqlImporter.statisticsRefundOrder,
    operation?: string
  ): Promise<Response<LazyRefunOrderStatisticsDTO>> {
    return commonRequestApi<LazyRefunOrderStatisticsDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { queryParam },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
