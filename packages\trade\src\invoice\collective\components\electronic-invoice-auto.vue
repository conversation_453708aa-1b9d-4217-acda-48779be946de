<template>
  <div class="f-p15">
    <el-tabs v-model="activeName2" type="card" class="m-tab-card">
      <template
        v-if="$hasPermission('autoInvoice,autoInvoiceZt')"
        desc="autoInvoice:电子普通发票（自动开票）,autoInvoiceZt:电子普通发票（自动开票）(专题)"
        actions="autoInvoice:@AutoInvoice#autoInvoiceZt:@AutoInvoice"
      >
        <el-tab-pane label="增值税电子普通发票（自动开票）" name="auto-invoice">
          <auto-invoice ref="autoInvoice"></auto-invoice>
        </el-tab-pane> </template
      ><template
        v-if="$hasPermission('redInvoice,redInvoiceZt')"
        desc="redInvoice:冲红发票,redInvoiceZt:冲红发票(专题)"
        actions="redInvoice:@RedInvoice#redInvoiceZt:@RedInvoice"
      >
        <el-tab-pane label="冲红发票" name="red-invoice">
          <red-invoice ref="redInvoice"></red-invoice>
        </el-tab-pane>
      </template>
    </el-tabs>
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import AutoInvoice from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/auto-invoice.vue'
  import RedInvoice from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/red-invoice.vue'
  @Component({
    components: { AutoInvoice, RedInvoice }
  })
  export default class extends Vue {
    @Ref('autoInvoice') autoInvoice: AutoInvoice
    @Ref('redInvoice') redInvoice: RedInvoice
    activeName2 = 'auto-invoice'
    init() {
      switch (this.activeName2) {
        case 'auto-invoice':
          this.autoInvoice?.page?.currentChange(1)
          break
        case 'red-invoice':
          this.redInvoice?.page?.currentChange(1)
          break
        default:
          break
      }
    }
  }
</script>
