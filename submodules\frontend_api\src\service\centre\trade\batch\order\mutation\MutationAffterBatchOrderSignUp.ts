import Collectivesign, {
  CollectiveSignQueryRequest,
  FindCountGroupByKeyRequest,
  MetaProperty,
  SignupDataProcessResponse
} from '@api/ms-gateway/ms-collectivesign-v1'
import MsOrder from '@api/ms-gateway/ms-order-v1'
import MsSchemeQueryFrontGatewayCourseLearningBackstage, {
  SchemeRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import MsTradeQueryFrontGatewayCourseLearningBackstage, {
  CommoditySkuBackstageResponse,
  CommoditySkuRequest,
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  SchemeResourceResponse,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import msTradeQuery, {
  CommoditySkuForestageResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
import { Page } from '@hbfe/common'
export interface TabItem {
  [key: string]: string | number
}
class MutationAffterBatchOrderSignUp {
  collectiveSignupNo = '' // 集体报名编号

  /**
   *培训班商品列表，类型为TrainClassCommodityVo[]
   */
  trainClassCommodityList: TrainClassCommodityVo[] = []
  //   percentage = 0 // 提交后报名数据执行进度
  constructor(collectiveSignupNo: string) {
    this.collectiveSignupNo = collectiveSignupNo
  }
  /**
   * 查询表头数据
   * @returns
   */
  async findCollectiveSignupMetaSchema() {
    const response = await Collectivesign.findCollectiveSignupMetaSchema()
    return response.data.metaSchemas
  }
  /**
   * 查询提交集体报名后的成功的数据列表
   */
  async findCommitCompleteAndSuccessSubTuskSuccessDataByPage(
    page: Page,
    metaPropertyList?: Array<MetaProperty>,
    trainingChannelId?: string
  ): Promise<Array<TabItem>> {
    const request = new CollectiveSignQueryRequest()
    request.collectiveSignupNo = this.collectiveSignupNo
    request.metaPropertyList = metaPropertyList
    const response = await Collectivesign.findCommitCompleteAndSuccessSubTuskSuccessDataByPage({
      request,
      page
    })
    page.totalSize = response.data.totalSize
    page.totalPageSize = response.data.totalPageSize
    const data = response.data.currentPageData.map((item) => {
      const obj: TabItem = {}
      item.row.forEach((res) => {
        obj[res.key] = res.value
      })
      item.runtimeProperties?.forEach((res) => {
        obj[res.key] = res.value
      })
      return obj
    })
    const queryRequest = new CommoditySkuRequest()
    queryRequest.schemeRequest = new SchemeRequest()
    // 修复bug  JXJYPXTYPT-18163
    queryRequest.isShowAll = true
    queryRequest.schemeRequest.schemeIdList = [
      ...new Set(data.filter((item) => item.SCHEME_ID).map((item) => item.SCHEME_ID) as string[])
    ]
    if (queryRequest.schemeRequest.schemeIdList.length == 0) {
      return data
    }
    let response1
    if (trainingChannelId) {
      queryRequest.trainingChannelIds = [trainingChannelId]
      response1 = await msTradeQuery.pageCommoditySkuTrainingChannelInServicer({
        page: {
          pageNo: 1,
          pageSize: 200
        },
        queryRequest
      })
    } else {
      response1 = await msTradeQuery.pageCommoditySkuCustomerCollectivePurchaseInServicer({
        page: {
          pageNo: 1,
          pageSize: 200
        },
        queryRequest
      })
    }
    const classInfoMap = new Map<string, CommoditySkuForestageResponse>()
    response1.data.currentPageData.forEach((item) => {
      classInfoMap.set((item.resource as any).schemeId, item)
    })
    data.forEach((item) => {
      if (classInfoMap.get(item.SCHEME_ID as string)) {
        const info = classInfoMap.get(item.SCHEME_ID as string)
        // item.technology_ID = info.skuProperty.technicalGrade.skuPropertyValueId
        // item.technology = info.skuProperty.technicalGrade.skuPropertyValueName
        item.region_code =
          info.skuProperty.province.skuPropertyValueId +
          (info.skuProperty.city.skuPropertyValueId ? '/' + info.skuProperty.city.skuPropertyValueId : '') +
          (info.skuProperty.county.skuPropertyValueId ? '/' + info.skuProperty.county.skuPropertyValueId : '')
        item.region =
          info.skuProperty.province.skuPropertyValueName +
          (info.skuProperty.city.skuPropertyValueName ? '/' + info.skuProperty.city.skuPropertyValueName : '') +
          (info.skuProperty.county.skuPropertyValueName ? '/' + info.skuProperty.county.skuPropertyValueName : '')
        item.classHourse = (info.resource as any).period
        item.price = info.commodityBasicData.price
      }
    })
    return data
  }
  /**
   * 查询提交集体报名后的失败的数据列表
   */
  async findCommitCompleteAndFailSubTuskFailDataByPage(
    page: Page,
    metaPropertyList?: Array<MetaProperty>
  ): Promise<Array<TabItem>> {
    const request = new CollectiveSignQueryRequest()
    request.collectiveSignupNo = this.collectiveSignupNo
    request.metaPropertyList = metaPropertyList
    const response = await Collectivesign.findCommitCompleteAndFailSubTuskFailDataByPage({
      request,
      page
    })
    page.totalSize = response.data.totalSize
    page.totalPageSize = response.data.totalPageSize
    const data = response.data.currentPageData.map((item) => {
      const obj: TabItem = {}
      item.row.forEach((res) => {
        obj[res.key] = res.value
        obj['errorMessage'] = item.errorMessage
        item.runtimeProperties?.forEach((res) => {
          obj[res.key] = res.value
        })
      })
      return obj
    })
    const queryRequest = new CommoditySkuRequest()
    queryRequest.schemeRequest = new SchemeRequest()
    queryRequest.schemeRequest.schemeIdList = [
      ...new Set(data.filter((item) => item.SCHEME_ID).map((item) => item.SCHEME_ID) as string[])
    ]
    if (queryRequest.schemeRequest.schemeIdList.length == 0) {
      return data
    }
    const response1 = await MsTradeQueryFrontGatewayCourseLearningBackstage.pageCommoditySkuInServicer({
      page: {
        pageNo: 1,
        pageSize: 200
      },
      queryRequest
    })
    const classInfoMap = new Map<string, CommoditySkuBackstageResponse>()
    response1.data.currentPageData.forEach((item) => {
      classInfoMap.set((item.resource as any).schemeId, item)
    })
    data.forEach((item) => {
      if (classInfoMap.get(item.SCHEME_ID as string)) {
        const info = classInfoMap.get(item.SCHEME_ID as string)
        // item.technology_ID = info.skuProperty.technicalGrade.skuPropertyValueId
        // item.technology = info.skuProperty.technicalGrade.skuPropertyValueName
        item.region_code =
          info.skuProperty.province.skuPropertyValueId +
          (info.skuProperty.city.skuPropertyValueId ? '/' + info.skuProperty.city.skuPropertyValueId : '') +
          (info.skuProperty.county.skuPropertyValueId ? '/' + info.skuProperty.county.skuPropertyValueId : '')
        item.region =
          info.skuProperty.province.skuPropertyValueName +
          (info.skuProperty.city.skuPropertyValueName ? '/' + info.skuProperty.city.skuPropertyValueName : '') +
          (info.skuProperty.county.skuPropertyValueName ? '/' + info.skuProperty.county.skuPropertyValueName : '')
        item.classHourse = (info.resource as any).period
        item.price = info.commodityBasicData.price
      }
    })
    return data
  }
  /**
   * 查询批量上传报名列表的进度
   * @returns
   */
  async queryImportProcess(): Promise<SignupDataProcessResponse> {
    const response = await Collectivesign.collectiveSignupDataProcess(this.collectiveSignupNo)
    // this.percentage = Math.floor(response.data.executedCount / response.data.needExecuteCount) * 100
    return response.data
  }

  /**   获取集体报名状态 --- 校验
   * 1 - 正常
   * 2 - 交易完成
   * 3 - 交易关闭
   * 4 - 提交处理中
   * 5 - 取消处理中
   * 当状态码是1或者4时，不走提交集体报名
   */
  async findCollectiveSignupStatus(): Promise<number> {
    const { data } = await MsOrder.findBatchOrderBatchPayStatus(this.collectiveSignupNo)
    return data
  }
  /**
   * 根据表头获取培训班ID
   */
  async findCountGroupByKey(type: number) {
    const findCountGroupByKeyRequest = new FindCountGroupByKeyRequest()
    findCountGroupByKeyRequest.collectiveSignupNo = this.collectiveSignupNo
    findCountGroupByKeyRequest.keyName = 'signUp_schemeName'
    findCountGroupByKeyRequest.type = type
    const response = await Collectivesign.findCountGroupByKey(findCountGroupByKeyRequest)
    const list = new Array<string>()
    response.data.forEach((className) => {
      list.push(className.key)
    })
    return list
  }
  /**
   * 获取培训班列表
   */
  async queryTrainClassCommodityList(
    page: Page,
    filterCommodity: CommoditySkuRequest,
    sortRequest?: Array<CommoditySkuSortRequest>
  ): Promise<Array<TrainClassCommodityVo>> {
    // let filter = new CommoditySkuRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    const sortRequestM = new CommoditySkuSortRequest()
    sortRequestM.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequestM.policy = SortPolicy.DESC
    !sortRequest && (sortRequest = [])
    sortRequest.push(sortRequestM)
    const res = await MsTradeQueryFrontGatewayCourseLearningBackstage.pageCommoditySkuInServicer({
      page: { pageNo: page.pageNo, pageSize: page.pageSize },
      queryRequest: filterCommodity,
      sortRequest: sortRequest || []
    })
    if (res.status.isSuccess()) {
      const tmpArr = res.data.currentPageData as TrainClassCommodityVo[]
      try {
        for (let i = 0; i < tmpArr.length; i++) {
          const tmpItem = tmpArr[i]
          tmpItem.schemeId = (tmpItem.resource as SchemeResourceResponse).schemeId
          //   tmpItem.skuValueNameProperty = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
          //     tmpItem.skuProperty
          //   )
          // tmpItem.skuValueNameProperty = SkuPropertyConvertUtils.filterSku(tmpItem.skuProperty, this.skuProperties)
          const configJson = await this.queryConfig(tmpItem.schemeId)
          if (configJson) {
            tmpItem.registerBeginDate = configJson.registerBeginDate
            tmpItem.registerEndDate = configJson.registerEndDate
            tmpItem.trainingBeginDate = configJson.trainingBeginDate
            tmpItem.trainingEndDate = configJson.trainingEndDate
            tmpItem.schemeType = configJson.type == 'chooseCourseLearning' ? 1 : 2
            const creditResult = configJson.assessSetting.learningResults.find((item: any) => {
              return item.type == 1
            })
            tmpItem.period = creditResult.grade
          }
        }
        this.trainClassCommodityList = tmpArr
      } catch (e) {
        console.log(e)
      }
      page.totalPageSize = res.data.totalPageSize
      page.totalSize = res.data.totalSize
    }
    //pageCommoditySkuCustomerPurchaseInServicer
    return this.trainClassCommodityList
  }

  /**
   * 获取培训班属性
   */
  async queryConfig(schemeId: string): Promise<any> {
    //获取培训班配置模板jsonString
    const res = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
      schemeId: schemeId,
      needField: [
        'registerBeginDate',
        'registerEndDate',
        'trainingBeginDate',
        'trainingEndDate',
        'type',
        'assessSetting.learningResults'
      ]
    })
    let jsonObj
    try {
      jsonObj = JSON.parse(res.data.schemeConfig)
    } catch (e) {
      return ''
    }
    return jsonObj
  }
}
export default MutationAffterBatchOrderSignUp
