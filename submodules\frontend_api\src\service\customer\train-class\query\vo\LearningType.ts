import CourseLearningLearningType from '@api/service/customer/train-class/query/vo/CourseLearningLearningType'
import ExamLearningType from '@api/service/customer/train-class/query/vo/ExamLearningType'
import PracticeLearningType from '@api/service/customer/train-class/query/vo/PracticeLearningType'
import InterestCourseType from '@api/service/customer/train-class/query/vo/InterestCourseType'
import LearningExperienceType from '@api/service/customer/train-class/query/vo/LearningExperienceType'
import IssueLearningType from '@api/service/common/scheme/model/IssueLearningType'
import QuestionnaireLearningType from '@api/service/common/scheme/model/QuestionnaireLearningType'

/**
 * 培训班学习方式聚合类
 */
class LearningType {
  // region properties

  /**
   *课程学习
   */
  courseLearning = new CourseLearningLearningType()
  /**
   *考试，类型为ExamLearningType
   */
  exam = new ExamLearningType()
  /**
   *练习，类型为PracticeLearningType
   */
  practiceLearning = new PracticeLearningType()
  /**
   *兴趣课，类型为InterestCourseType
   */
  interestCourse = new InterestCourseType()
  /**
   *学习心得，类型为InterestCourseType
   */
  learningExperience = new LearningExperienceType()
  // endregion
  // region methods

  /**
   * 期别学习方式
   */
  issue = new IssueLearningType()
  /**
   * 调研问卷学习方式
   */
  questionnaire = new QuestionnaireLearningType()

  // endregion
}
export default LearningType
