import {
  CourseOfCourseTrainingOutlineRequest,
  StudentCourseLearningRequest,
  StudentCourseRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

export enum LearningStatus {
  learning = 0,
  not_learning = 1,
  complete = 2
}

/**
 * 查询我的兴趣课的查询参数
 */
class QueryMyInterestCoursePageParam {
  // 学号
  studentNo: string
  // 课程学习大纲id列表 适配搜索 '全部'
  outlineIdList: Array<string>
  // 学习状态
  learningStatus?: LearningStatus

  to(): StudentCourseLearningRequest {
    const request = new StudentCourseLearningRequest()
    request.studentNo = this.studentNo
    request.studentCourse = new StudentCourseRequest()
    request.studentCourse.courseLearningStatus = this.learningStatus >= 0 ? [this.learningStatus] : undefined
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.courseOfCourseTrainingOutline.outlineIds = this.outlineIdList?.length ? this.outlineIdList : undefined
    // request.courseOfCourseTrainingOutline.courseType = 3
    return request
  }
}

export default QueryMyInterestCoursePageParam
