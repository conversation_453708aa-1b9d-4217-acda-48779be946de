/**
 * 学员学习课程场景
 */
import MsMediaResourceLearningV1, {
  LearningResultResponse,
  TimingTokenResponse
} from '@api/ms-gateway/ms-media-resource-learning-v1'
import TimerTask from '@api/service/common/timer/TimerTask'
import LearningHeatBeat from '@api/service/customer/learning/course/LearningHeatBeat'
import UserLearningCourse from '@api/service/customer/learning/course/UserLearningCourse'
import ApplyLearningCoursewareToken from '@api/service/customer/learning/course/token-provider/ApplyLearningCoursewareToken'
import ApplyMediaLearningTimingToken from '@api/service/customer/learning/course/token-provider/ApplyMediaLearningTimingToken'
import { Response, ResponseStatus } from '@hbfe/common'
import { EventEmitter } from 'events'
import { Description, upMyLog } from '@hbfe/jxjy-customer-common/src/monitor/WebfunnyUpMyLog'
import { PlayerSkipReportModel } from '@api/service/common/webfunny/models/StudyPlayerModel'
import { CommitReportEnum } from '@api/service/common/webfunny/enums/StudyPlayerEnum'
import WebfunnyReport from '@api/service/common/webfunny/WefunnyReport'

class StudentLearningCourseScene extends EventEmitter {
  courseLearningToken: string
  userLearningCourse: UserLearningCourse

  constructor(courseLearningToken: string, userLearningCourse: UserLearningCourse) {
    super()
    this.courseLearningToken = courseLearningToken
    this.userLearningCourse = userLearningCourse
  }

  // 定时器
  timerTask: TimerTask
  // 申请学习课件的 token
  applyLearningCoursewareToken: ApplyLearningCoursewareToken
  // 申请学习媒体的 token
  applyMediaLearningTimingToken: ApplyMediaLearningTimingToken
  // 学习心跳类
  leaningHeatBeat: LearningHeatBeat
  // 上一次提交学习的进度结果
  prevLearningResult: TimingTokenResponse
  // 最后一次的提交进度学习结果
  lastLearningResult: TimingTokenResponse
  /**
   * 提交进度返回结果
   */
  commitResult = new Response<LearningResultResponse>()

  /**
   * 进入上一次的场景
   */
  async enterPrevScene(coursewareId: string, multiMediaId: string): Promise<ResponseStatus> {
    try {
      this.applyLearningCoursewareToken = new ApplyLearningCoursewareToken(
        this.courseLearningToken,
        coursewareId,
        multiMediaId
      )
      // 如果是学习，就得启动心跳
      if (this.leaningHeatBeat) {
        this.leaningHeatBeat.broken()
      }
      // 申请课件学习 token
      await this.applyLearningCoursewareToken.apply()
      this.applyMediaLearningTimingToken = new ApplyMediaLearningTimingToken(this.applyLearningCoursewareToken.token)
      // 申请媒体学习 token
      const res = await this.applyMediaLearningTimingToken.apply()
      if (res?.code === 10006) {
        return Promise.reject(new ResponseStatus(10006, '系统繁忙'))
      }
      this.lastLearningResult = this.applyMediaLearningTimingToken.learningTokenConfig

      this.leaningHeatBeat = new LearningHeatBeat(this.applyMediaLearningTimingToken.token)
      if (this.userLearningCourse.flag) {
        await this.leaningHeatBeat.keepalive()
      }
    } catch (e) {
      return Promise.reject(new ResponseStatus(500, '进入场景失败'))
    }
    return Promise.resolve(new ResponseStatus(200, '进入成功'))
  }

  /**
   * 关闭学习
   */
  destroy() {
    this.leaningHeatBeat.broken()
    this.leaningHeatBeat = new LearningHeatBeat('')
  }

  /**
   * 记录当前的场景信息
   * @param currentMediaTimeLength
   */
  async recordRecentScene(currentMediaTimeLength = 0) {
    // 埋点参数
    const description = new Description()
    const commitTime = Math.ceil(Number(currentMediaTimeLength.toFixed(2)))
    const { data, status } = await MsMediaResourceLearningV1.commitMediaLearningTimingWithAnti({
      token: this.applyMediaLearningTimingToken.token,
      currentMediaTimeLength: commitTime
    })
    this.commitResult = { data, status }
    if (status.isSuccess() && data?.timingToken) {
      this.prevLearningResult = this.lastLearningResult
      this.lastLearningResult = data.timingToken
      this.userLearningCourse.setCurrentPlayCoursewareSchedule(data.timingToken.coursewareSchedule)
      this.userLearningCourse.courseDetail.schedule = data.timingToken.courseSchedule
    } else {
      try {
        const commitError = new PlayerSkipReportModel()
        commitError.currentScale = commitTime
        commitError.scene = CommitReportEnum.commitFail
        commitError.abnormalMessage = '提交进度异常 api'
        commitError.requestBody = JSON.stringify({
          token: this.applyMediaLearningTimingToken.token,
          currentMediaTimeLength: commitTime
        })
        commitError.response = JSON.stringify(this.commitResult)
        WebfunnyReport.upCommonEvent('通用--课程播放', commitError)
      } catch (e) {
        console.log(e)
      }

      // 埋点信息
      description.response = data
      description.params = commitTime
      upMyLog(description, 'recordRecentScene')
    }
  }

  /**
   * 判断是否是第一次完成该课程的 100 的进度
   * 判断方式：如果上一次的课程提交进度是 !== 100 但是这次的提交进度是百分百，则视为第一次
   */
  isFirstComplete() {
    if (!this.prevLearningResult || !this.lastLearningResult) {
      return false
    }
    const prevIsNot_100 = this.prevLearningResult.courseSchedule !== 100
    const lastIs_100 = this.lastLearningResult.courseSchedule === 100
    return prevIsNot_100 && lastIs_100
  }

  /**
   * 返回总学习时长
   */
  getLearningTotalLength() {
    return this.lastLearningResult.mediaLearningTimeLength
  }

  /**
   * 返回最后一次提交的播放刻度
   */
  getLastCommitPlayScale() {
    return this.lastLearningResult.mediaLearningTimeLength
  }
}

export default StudentLearningCourseScene
