<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--删除基准照-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog1" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">【陈龙】在【2020-12-08 15:59:59】将审批结果为<span class="f-ci">不通过</span></span>
          </div>
          <div slot="footer">
            <el-button type="primary">确 定</el-button>
          </div>
        </el-dialog>
        <!--一键合格-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20 f-mb20">一键合格</el-button>
        <el-drawer title="一键合格" :visible.sync="dialog2" :direction="direction" size="560px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt10">
              <el-form-item label="合格时间配置：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="指定合格时间"></el-radio>
                  <el-radio label="按系统当前操作成功时间"></el-radio>
                </el-radio-group>
                <div class="f-mt5">
                  <el-time-select
                    placeholder="选择指定合格时间"
                    v-model="startTime"
                    class="f-wf"
                    :picker-options="{
                      start: '08:30',
                      step: '00:15',
                      end: '18:30'
                    }"
                  >
                  </el-time-select>
                </div>
              </el-form-item>
              <el-form-item label="考试合格分：" required>
                <el-input v-model="input" placeholder="100" class="f-input-num f-mr5"></el-input>分
                <span class="f-co f-ml30">及格/满分：60/100分</span>
              </el-form-item>
              <el-form-item label="课程测验合格分：" required>
                <el-input v-model="input" placeholder="100" class="f-input-num f-mr5"></el-input>分
                <span class="f-co f-ml30">及格/满分：60/100分</span>
              </el-form-item>
              <el-form-item label=" " class="is-text">
                <span class="f-co">注：请设置合格成绩介于合格分和满分之间。</span>
              </el-form-item>
              <el-alert type="warning" :closable="false" center class="f-mb20">
                <div class="f-f14">一键合格的班级，无学习监拍日志，请谨慎操作！</div>
              </el-alert>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">确认</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames: ['1'],
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleChange(val) {
        console.log(val)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
