import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-exam-answer-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-exam-answer-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class QuestionGroup {
  sequence: number
  questionType: number
  groupName: string
  eachQuestionScore: number
}

/**
 * <AUTHOR>
@date 2021/6/8 17:44
@Description:
 */
export class AnswerRequest {
  /**
   * 【必填】试题ID
   */
  questionId?: string
  /**
   * 【必填】 题型
1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
   */
  questionType: number
}

/**
 * 简单题作答信息
 */
export class AskQuestionAnswer implements AnswerRequest {
  /**
   * 【必填】答案
   */
  answer?: string
  /**
   * 【必填】试题ID
   */
  questionId?: string
  /**
   * 【必填】 题型
1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
   */
  questionType: number
}

/**
 * 填空题作答信息
 */
export class FillQuestionAnswer implements AnswerRequest {
  /**
   * 【必填】答案
   */
  answer?: Array<MapDto1>
  /**
   * 【必填】试题ID
   */
  questionId?: string
  /**
   * 【必填】 题型
1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
   */
  questionType: number
}

/**
 * 多选题作答信息
 */
export class MultipleQuestionAnswer implements AnswerRequest {
  /**
   * 【必填】答案
   */
  answer?: Array<string>
  /**
   * 填空内容 key是答案id，value是填空的内容
   */
  fillContentMap?: Map<string, string>
  /**
   * 【必填】试题ID
   */
  questionId?: string
  /**
   * 【必填】 题型
1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
   */
  questionType: number
}

/**
 * 判断题作答信息
 */
export class OpinionQuestionAnswer implements AnswerRequest {
  /**
   * 【必填】答案
   */
  answer: boolean
  /**
   * 【必填】试题ID
   */
  questionId?: string
  /**
   * 【必填】 题型
1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
   */
  questionType: number
}

/**
 * 单选题作答信息
 */
export class RadioQuestionAnswer implements AnswerRequest {
  /**
   * 【必填】答案
   */
  answer?: string
  /**
   * 填空内容
   */
  fillContent?: string
  /**
   * 【必填】试题ID
   */
  questionId?: string
  /**
   * 【必填】 题型
1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
   */
  questionType: number
}

/**
 * 量表题作答信息
 */
export class ScaleQuestionAnswer implements AnswerRequest {
  /**
   * 【必填】答案
   */
  answer?: number
  /**
   * 【必填】试题ID
   */
  questionId?: string
  /**
   * 【必填】 题型
1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
   */
  questionType: number
}

/**
 * 提交问卷答卷
<AUTHOR>
@date 2025/4/24 17:36
 */
export class HandedQuestionnaireAnswerPaperRequest {
  /**
   * 作答token
   */
  token?: string
  /**
   * 答卷信息
   */
  answerPaperViewRequest?: PreviewPaperPublishConfigureResponse
  /**
   * 出卷时间
   */
  answerExtractionTime?: string
}

/**
 * @author: zhengp
@since 2021/8/27 10:35
 */
export class MapDto1 {
  key?: number
  answer?: string
}

export class PreviewPaperPublishConfigureResponse {
  name?: string
  description?: string
  timeLength: number
  totalScore: number
  totalQuestionCount: number
  groups?: Array<QuestionGroup>
  paperType: number
  questions?: Array<BaseQuestionResponse>
}

export class BaseFillAnswerResponse {
  type: number
}

export class ChildItemResponse {
  no: number
  questionId?: string
}

export class ChooseAnswerOptionResponse {
  id?: string
  content?: string
  enableFillContent?: boolean
  mustFillContent?: boolean
}

export class DisarrayFillAnswerResponse implements BaseFillAnswerResponse {
  disarrayCorrectAnswers?: Array<string>
  type: number
}

export class FillCorrectAnswersResponse {
  blankNo: number
  answers?: Array<string>
}

export class SequenceFillAnswerResponse implements BaseFillAnswerResponse {
  sequenceCorrectAnswers?: Array<FillCorrectAnswersResponse>
  type: number
}

export class SequenceRateFillAnswerResponse implements BaseFillAnswerResponse {
  sequenceRateCorrectAnswers?: Array<SequenceFillAnswerResponse>
  type: number
}

export class AskQuestionResponse1 implements BaseQuestionResponse {
  askAnswer?: string
  id?: string
  groupSequence: number
  questionType: number
  score: number
  topic?: string
  isChildQuestion: boolean
  parentQuestionId?: string
  dissects?: string
  relateCourseId?: Array<string>
  questionDifficulty: number
  answerRequired?: boolean
  answered: boolean
  labelCodeList?: Array<string>
}

export class BaseQuestionResponse {
  id?: string
  groupSequence: number
  questionType: number
  score: number
  topic?: string
  isChildQuestion: boolean
  parentQuestionId?: string
  dissects?: string
  relateCourseId?: Array<string>
  questionDifficulty: number
  answerRequired?: boolean
  answered: boolean
  labelCodeList?: Array<string>
}

export class FatherQuestionResponse1 implements BaseQuestionResponse {
  childQuestions?: Array<ChildItemResponse>
  id?: string
  groupSequence: number
  questionType: number
  score: number
  topic?: string
  isChildQuestion: boolean
  parentQuestionId?: string
  dissects?: string
  relateCourseId?: Array<string>
  questionDifficulty: number
  answerRequired?: boolean
  answered: boolean
  labelCodeList?: Array<string>
}

export class FillQuestionResponse1 implements BaseQuestionResponse {
  fillCount: number
  fillQuestionCorrectAnswer?: BaseFillAnswerResponse
  id?: string
  groupSequence: number
  questionType: number
  score: number
  topic?: string
  isChildQuestion: boolean
  parentQuestionId?: string
  dissects?: string
  relateCourseId?: Array<string>
  questionDifficulty: number
  answerRequired?: boolean
  answered: boolean
  labelCodeList?: Array<string>
}

export class MultipleQuestionResponse1 implements BaseQuestionResponse {
  answerOptions?: Array<ChooseAnswerOptionResponse>
  multipleQuestionCorrectAnswerIds?: Array<string>
  multipleAnswer?: Array<string>
  fillContentMap?: Map<string, string>
  id?: string
  groupSequence: number
  questionType: number
  score: number
  topic?: string
  isChildQuestion: boolean
  parentQuestionId?: string
  dissects?: string
  relateCourseId?: Array<string>
  questionDifficulty: number
  answerRequired?: boolean
  answered: boolean
  labelCodeList?: Array<string>
}

export class OpinionQuestionResponse1 implements BaseQuestionResponse {
  correctAnswerText?: string
  incorrectAnswerText?: string
  opinionQuestionCorrectAnswer?: boolean
  id?: string
  groupSequence: number
  questionType: number
  score: number
  topic?: string
  isChildQuestion: boolean
  parentQuestionId?: string
  dissects?: string
  relateCourseId?: Array<string>
  questionDifficulty: number
  answerRequired?: boolean
  answered: boolean
  labelCodeList?: Array<string>
}

export class RadioQuestionResponse1 implements BaseQuestionResponse {
  answerOptions?: Array<ChooseAnswerOptionResponse>
  radioQuestionCorrectAnswerId?: string
  radioAnswer?: string
  fillContent?: string
  id?: string
  groupSequence: number
  questionType: number
  score: number
  topic?: string
  isChildQuestion: boolean
  parentQuestionId?: string
  dissects?: string
  relateCourseId?: Array<string>
  questionDifficulty: number
  answerRequired?: boolean
  answered: boolean
  labelCodeList?: Array<string>
}

export class ScaleQuestionResponse1 implements BaseQuestionResponse {
  scaleType: number
  startDegree?: string
  endDegree?: string
  series: number
  initialValue: number
  scaleAnswer?: number
  id?: string
  groupSequence: number
  questionType: number
  score: number
  topic?: string
  isChildQuestion: boolean
  parentQuestionId?: string
  dissects?: string
  relateCourseId?: Array<string>
  questionDifficulty: number
  answerRequired?: boolean
  answered: boolean
  labelCodeList?: Array<string>
}

/**
 * 选择题答案选项
<AUTHOR>
 */
export class ChooseAnswerOption {
  /**
   * 答案ID
   */
  id: string
  /**
   * 答案内容
   */
  content: string
  /**
   * 建议分值
   */
  suggestionScore: number
  /**
   * 是否允许填空
   */
  enableFillContent: boolean
  /**
   * 填空是否必填
   */
  mustFillContent: boolean
}

/**
 * 答卷作答控制视图
<AUTHOR>
 */
export class AnswerPaperControlViewResponse {
  /**
   * 是否有时长控制
   */
  timeLengthRestrict: boolean
  /**
   * 试题提交答案后是否可以重答
   */
  questionAgainAnswer: boolean
}

/**
 * 答卷UI呈现控制视图
<AUTHOR>
 */
export class AnswerPaperUIDisplayViewResponse {
  /**
   * 试题呈现方式
1 表示整卷
2 表示大题
3 表示单题
   */
  questionDisplay: number
  /**
   * 是否展示试题解析
   */
  dissectsShow: boolean
  /**
   * 是否显示分数
   */
  scoreShow: boolean
  /**
   * 是否开放作答结果
   */
  openAnswerResults: boolean
  /**
   * 是否提供标签
   */
  provideLabel: boolean
}

/**
 * 答卷信息
<AUTHOR>
 */
export class AnswerPaperViewResponse {
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 作答时长
   */
  timeLength: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 剩余作答时长
   */
  remainderTimeLength: number
  /**
   * 大题集合，为空集合表示本答卷没有使用大题展示方式
   */
  groups: Array<QuestionGroupViewResponse>
  /**
   * 试题集合
   */
  questions: Array<QuestionResponse>
  /**
   * 作答控制
   */
  control: AnswerPaperControlViewResponse
  /**
   * UI呈现方式
   */
  display: AnswerPaperUIDisplayViewResponse
}

/**
 * 申请作答结果
<AUTHOR>
 */
export class ApplyPaperAnswerResponse {
  /**
   * 答卷状态
<p>
说明：
0 表示正在出卷中，需要前端主动间隔多次进行申请作答，直到成功获取答卷
1 表示成功获取答卷，answerPaper将返回结构
2 表示本次作答Token中描述的答卷已交卷无法继续作答
</p>
   */
  state: number
  /**
   * 结果文本信息
   */
  message: string
  /**
   * 答卷信息
   */
  answerPaper: AnswerPaperViewResponse
}

/**
 * 提交问卷答卷返回值
<AUTHOR>
@date 2025/4/30 10:32
 */
export class HandedQuestionnaireAnswerPaperResponse {
  code: string
  message: string
}

/**
 * 试题答案响应
<AUTHOR>
 */
export class QuestionAnswerResponse {
  /**
   * 试题答案集合
   */
  questionToAnswers: Array<BaseQuestionToAnswer>
}

/**
 * 试卷大题信息
<AUTHOR>
 */
export class QuestionGroupViewResponse {
  /**
   * 大题序号
   */
  sequence: number
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
</p>
   */
  questionType: number
  /**
   * 大题名称
   */
  groupName: string
  /**
   * 试题数
   */
  questionCount: number
  /**
   * 每题平均分数
   */
  eachQuestionScore: number
}

/**
 * <AUTHOR>
@date 2021/6/8 15:06
@Description: 答卷响应抽象类
 */
export interface QuestionResponse {
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 试题对应答案基类
<AUTHOR>
 */
export interface BaseQuestionToAnswer {
  /**
   * 试题id
   */
  questionId: string
  /**
   * 试题类型
1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
简答题父子题无答案
   */
  type: number
}

/**
 * 填空题
<AUTHOR>
 */
export class FillQuestionToAnswer implements BaseQuestionToAnswer {
  /**
   * 填空题答案
   */
  fillCorrectAnswer: FillAnswer
  /**
   * 填空数
   */
  fillCount: number
  /**
   * 试题id
   */
  questionId: string
  /**
   * 试题类型
1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
简答题父子题无答案
   */
  type: number
}

/**
 * 多选题
<AUTHOR>
 */
export class MultipleQuestionToAnswer implements BaseQuestionToAnswer {
  /**
   * 正确答案集合
   */
  multipleCorrectAnswers: Array<string>
  /**
   * 试题id
   */
  questionId: string
  /**
   * 试题类型
1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
简答题父子题无答案
   */
  type: number
}

/**
 * 判断题
<AUTHOR>
 */
export class OpinionQuestionToAnswer implements BaseQuestionToAnswer {
  /**
   * 正确答案
   */
  opinionCorrectAnswer: boolean
  /**
   * 试题id
   */
  questionId: string
  /**
   * 试题类型
1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
简答题父子题无答案
   */
  type: number
}

/**
 * 单选
<AUTHOR>
 */
export class RadioQuestionToAnswer implements BaseQuestionToAnswer {
  /**
   * 正确答案id
   */
  radioCorrectAnswer: string
  /**
   * 试题id
   */
  questionId: string
  /**
   * 试题类型
1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
简答题父子题无答案
   */
  type: number
}

/**
 * 散乱无序填空题答案实体
<pre>
每空有多种匹配答案，空格答案不存在顺序关系： 比如：
试题题目：请写出中国四大银行__________、__________、__________、__________。
每空备选答案：
1/中国建设银行 建设银行 建行
2/中国银行 中行
3/中国工商银行 工商银行 工行
4/中国农业银行 农业银行 农行
学员答题答案：农行、工行、中行、建行；评卷为正确并得分；
</pre>
<AUTHOR>
 */
export class DisarrayFillAnswer implements FillAnswer {
  /**
   * 正确答案集合
   */
  disarrayFillCorrectAnswers: Array<string>
  /**
   * 填空题答案类型
@see FillQuestionFillAnswerTypes
1-散乱无序填空题答案实体    2-按序填空题答案实体     3-按序关联填空题答案实体
   */
  type: number
}

/**
 * 填空题答案基类
<AUTHOR>
 */
export interface FillAnswer {
  /**
   * 填空题答案类型
@see FillQuestionFillAnswerTypes
1-散乱无序填空题答案实体    2-按序填空题答案实体     3-按序关联填空题答案实体
   */
  type: number
}

/**
 * 按序填空题答案实体
<pre>
每空有多种匹配答案且空格答案存在顺序关系： 比如：
试题题目：中国的政治中心是__________；中国的经济中心是__________。
试题答案： 1/北京北京市、2/上海上海市
学员答题答案：北京市、上海；评卷为正确并得分；
</pre>
<AUTHOR>
 */
export class SequenceFillAnswer implements FillAnswer {
  /**
   * 每个填空数答案
<p>
KEY:填空序号
VALUE:填空的答案可匹配列表
</p>
   */
  sequenceFillCorrectAnswers: Map<string, string>
  /**
   * 填空题答案类型
@see FillQuestionFillAnswerTypes
1-散乱无序填空题答案实体    2-按序填空题答案实体     3-按序关联填空题答案实体
   */
  type: number
}

/**
 * 按序关联填空题答案实体
<pre>
每空答案精确匹配： 适用于前后空格的答案是有关联的。比如：
试题题目：请写出中国四大名著之一是__________；作者是__________。
试题答案： 1/红楼梦曹雪芹、2/西游记吴承恩
学员答题答案：红楼梦、曹雪芹；评卷为正确并得分；
红楼梦、吴承恩；评卷则给第一个空得分；
</pre>
<AUTHOR>
 */
export class SequenceRelateFillAnswer implements FillAnswer {
  /**
   * 正确答案集合
   */
  sequenceRelateFillCorrectAnswers: Array<SequenceFillAnswer>
  /**
   * 填空题答案类型
@see FillQuestionFillAnswerTypes
1-散乱无序填空题答案实体    2-按序填空题答案实体     3-按序关联填空题答案实体
   */
  type: number
}

/**
 * @author: zhengp
@since 2021/8/27 10:35
 */
export class MapDto {
  key: number
  answer: string
}

/**
 * 简单题
@author: zhengp
@since 2021/8/26 14:53
 */
export class AskQuestionResponse implements QuestionResponse {
  /**
   * 已作答答案
   */
  askAnswer: string
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 父子题
@author: zhengp
@since 2021/8/26 14:53
 */
export class FatherQuestionResponse implements QuestionResponse {
  /**
   * 子题集合
   */
  childQuestions: Array<QuestionResponse>
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 填空题
@author: zhengp
@since 2021/8/26 14:53
 */
export class FillQuestionResponse implements QuestionResponse {
  /**
   * 填空数
   */
  fillCount: number
  /**
   * 已作答答案
   */
  fillAnswer: Array<MapDto>
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 多选题
@author: zhengp
@since 2021/8/26 14:53
 */
export class MultipleQuestionResponse implements QuestionResponse {
  /**
   * 可选答案列表
   */
  answerOptions: Array<ChooseAnswerOption>
  /**
   * 已答答案
   */
  multipleAnswer: Array<string>
  /**
   * 填空内容 key是答案id，value是填空的内容
   */
  fillContentMap: Map<string, string>
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 判断题
@author: zhengp
@since 2021/8/26 14:53
 */
export class OpinionQuestionResponse implements QuestionResponse {
  /**
   * 正确文本
   */
  correctAnswerText: string
  /**
   * 错误文本
   */
  incorrectAnswerText: string
  /**
   * 用户答案
   */
  opinionAnswer: boolean
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 判断题
@author: zhengp
@since 2021/8/26 14:53
 */
export class RadioQuestionResponse implements QuestionResponse {
  /**
   * 可选答案列表
   */
  answerOptions: Array<ChooseAnswerOption>
  /**
   * 已作答答案
   */
  radioAnswer: string
  /**
   * 填空内容
   */
  fillContent: string
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * @Author: chenzeyu
@CreateTime: 2024-08-01  19:42
@Description: 量表题
 */
export class ScaleQuestionResponse implements QuestionResponse {
  /**
   * 量表类型
@see ScaleTypes
   */
  scaleType: number
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  startDegree: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  endDegree: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
  /**
   * 已作答答案
   */
  scaleAnswer: number
  /**
   * 试题ID
   */
  id: string
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题分数
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 试题id集合获取试题答案(不包含父子题及)
   * @param encryptQuestionIds 试题id
   * @param answerToken        答卷id
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getQuestionToAnswerByQuestionIds(
    params: { encryptQuestionIds?: Array<string>; answerToken?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getQuestionToAnswerByQuestionIds,
    operation?: string
  ): Promise<Response<QuestionAnswerResponse>> {
    return commonRequestApi<QuestionAnswerResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询答卷是否可用
   * @param token:
   * @return {@link Boolean}
   * <AUTHOR> By Cb
   * @since 2025/1/20 15:44
   * @param query 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryAnswerPaperAvailable(
    token: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryAnswerPaperAvailable,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { token },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请试卷作答
   * @param token 作答Token
   * @return 申请作答结果
   * @param mutate 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyAnswer(
    token: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyAnswer,
    operation?: string
  ): Promise<Response<ApplyPaperAnswerResponse>> {
    return commonRequestApi<ApplyPaperAnswerResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { token },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请交卷
   * @param token 作答token
   * @param mutate 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyHanding(
    token: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyHanding,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { token },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * @param answerPaperId
   * @return int
   * @title:获取答卷剩余作答时长（单位秒，-1代表无时长限制）
   * @description:
   * @author: chenzeyu
   * @date: 2024/9/25 9:32
   * @param mutate 查询 graphql 语法文档
   * @param answerPaperId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRemainingAnswerTime(
    answerPaperId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.getRemainingAnswerTime,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: mutate,
        variables: { answerPaperId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 提交问卷答卷
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async handedQuestionnaireAnswerPaper(
    request: HandedQuestionnaireAnswerPaperRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.handedQuestionnaireAnswerPaper,
    operation?: string
  ): Promise<Response<HandedQuestionnaireAnswerPaperResponse>> {
    return commonRequestApi<HandedQuestionnaireAnswerPaperResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 预提交作答
   * @param token   作答Token
   * @param answers 试题答案集合
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async preSubmitAnswer(
    params: { token?: string; answers?: Array<AnswerRequest> },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.preSubmitAnswer,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
