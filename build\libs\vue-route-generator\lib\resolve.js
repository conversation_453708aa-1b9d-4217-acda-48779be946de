'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const vueTemplateCompiler = require('vue-template-compiler')
const nestedMap = require('./nested-map')
const routeMetaName = 'route-meta'
const routeParamsName = 'route-params'
const analyseVue = require('../analyse-vue')
let _pages = ''
let _withSecure = false

function resolveRoutePaths(paths, importPrefix, nested, readFile, pages, withSecure) {
  _pages = pages
  _withSecure = withSecure
  const map = {}
  const splitted = paths.map(p => p.split('/'))
  splitted.forEach(path => {
    nestedMap.setToMap(map, pathToMapPath(path), path)
  })
  return pathMapToMeta(map, importPrefix, nested, readFile)
}

exports.resolveRoutePaths = resolveRoutePaths

function pathMapToMeta(map, importPrefix, nested, readFile, parentDepth = 0) {
  if (map.value) {
    const path = map.value
    const meta = {
      name: pathToName(path),
      specifier: pathToSpecifier(path),
      path: pathToRoute(path, parentDepth, nested),
      deepPath: {},
      pathSegments: toActualPath(path),
      component: importPrefix + path.join('/'),
      permissionMap: {}
    }
    const content = readFile(path.join('/'))
    const parsed = vueTemplateCompiler.parseComponent(content, {
      pad: 'space'
    })
    const resultPath = map.value.filter(item => {
      return !/\.vue/.test(item)
    })

    const routeMetaBlock = parsed.customBlocks.find(b => b.type === routeMetaName)
    const routeParam = parsed.customBlocks.find(b => b.type === routeParamsName)
    const routeGroup = parsed.customBlocks.filter(i => i.type === 'route-group')
    if (routeParam && routeParam.attrs.content) {
      meta.path += routeParam.attrs.content
    }
    if (routeMetaBlock) {
      try {
        let content = ''
        content = routeMetaBlock.content
        meta.routeMeta = JSON.parse(content)
      } catch (err) {
        const joinedPath = path.join('/')
        const wrapped = new Error(`Invalid json format of <route-meta> content in ${ joinedPath }\n` + err.message)
        // Store file path to provide useful information to downstream tools
        // like friendly-errors-webpack-plugin
        wrapped.file = joinedPath
        throw wrapped
      }
    }

    if (!meta.routeMeta) {
      meta.routeMeta = {}
    }

    meta.routeMeta.permissionMap = {}
    meta.routeMeta.ownerGroup = []
    if (_withSecure) {
      analyseVue(parsed, meta.routeMeta.permissionMap, `${ _pages }\\${ resultPath.join('\\') }`, meta.pathSegments)
    }
    if (routeGroup.length) {
      const groups = new Set()
      routeGroup.forEach(group => {
        groups.add(group.attrs.key)
      })

      meta.routeMeta.ownerGroup = Array.from(groups.keys())
    }
    meta.routeMeta.group = meta.pathSegments.join('.')
    if (map.children) {
      meta.children = pathMapChildrenToMeta(map.children, importPrefix, nested, readFile, path.length)
    }
    return [meta]
  }
  return map.children ? pathMapChildrenToMeta(map.children, importPrefix, nested, readFile, parentDepth) : []
}

function routePathComparator(a, b) {
  const a0 = a[0]
  const b0 = b[0]
  if (!a0 || !b0) {
    return a.length - b.length
  }
  const aOrder = isDynamicRoute(a0) ? 1 : 0
  const bOrder = isDynamicRoute(b0) ? 1 : 0
  const order = aOrder - bOrder
  return order !== 0 ? order : routePathComparator(a.slice(1), b.slice(1))
}

function pathMapChildrenToMeta(children, importPrefix, nested, readFile, parentDepth) {
  return Array.from(children.values())
    .reduce((acc, value) => {
      return acc.concat(pathMapToMeta(value, importPrefix, nested, readFile, parentDepth))
    }, [])
    .sort((a, b) => {
      // Prioritize static routes than dynamic routes
      return routePathComparator(a.pathSegments, b.pathSegments)
    })
}

function isDynamicRoute(segment) {
  return segment[0] === ':'
}

function isOmittable(segment) {
  return segment === 'index'
}

/**
 * - Remove `.vue` from the last path
 * - Omit if the last segument is `index`
 * - Convert dynamic route to `:param` format
 */
function toActualPath(segments) {
  const lastIndex = segments.length - 1
  const last = basename(segments[lastIndex])
  if (isOmittable(last)) {
    segments = segments.slice(0, -1)
  } else {
    segments = segments.slice(0, -1).concat(last)
  }
  return segments.map((s, i) => {
    if (s[0] === '_') {
      const suffix = lastIndex === i ? '?' : ''
      return ':' + s.slice(1) + suffix
    } else {
      return s
    }
  })
}

function pathToMapPath(segments) {
  const last = segments[segments.length - 1]
  return segments.slice(0, -1).concat(basename(last))
}

function pathToName(segments) {
  const last = segments[segments.length - 1]
  segments = segments.slice(0, -1).concat(basename(last))
  return segments
    .map(s => {
      return s[0] === '_' ? s.slice(1) : s
    })
    .join('-')
}

function pathToSpecifier(segments) {
  const name = pathToName(segments)
  const replaced = name
    .replace(/(^|[^a-zA-Z])([a-zA-Z])/g, (_, a, b) => {
      return a + b.toUpperCase()
    })
    .replace(/[^a-zA-Z0-9]/g, '')
  return /^\d/.test(replaced) ? '_' + replaced : replaced
}

function pathToRoute(segments, parentDepth, nested) {
  const prefix = nested || parentDepth > 0 ? '' : '/'
  return (
    prefix +
    toActualPath(segments)
      .slice(parentDepth)
      .join('/')
  )
}

function basename(filename) {
  return filename.replace(/\.[^.]+$/g, '')
}
