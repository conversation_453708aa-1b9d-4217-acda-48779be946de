<route-meta>
  {
  "isMenu": true,
  "title": "选课统计",
  "sort": 8,
  "icon": "icon-ribaotongji"
  }
</route-meta>

<script lang="ts">
  import CourseSelectionStatistic from '@hbfe/jxjy-admin-courseSelectionStatistic/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    query: [],
    export: [],
    detail: [],
    batchexport: [],
    batchExportGys: []
  })
  export default class extends CourseSelectionStatistic {}
</script>
