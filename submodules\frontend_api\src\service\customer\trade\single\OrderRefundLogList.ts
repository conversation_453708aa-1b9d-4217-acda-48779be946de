import RefundRecordItem from '@api/service/common/return-order/models/RefundRecordItem'
import tradeQueryGateway, {
  ReturnOrderRequest,
  SubOrderInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { Page } from '@hbfe/common'

export default class OrderRefundLogList {
  /**
   * 子订单号
   */
  subOrderNo = ''

  /**
   * 退款记录列表
   */
  records: Array<RefundRecordItem> = new Array<RefundRecordItem>()

  /**
   * 查询子订单退款记录
   */
  async queryReturnLogDetail() {
    const request = new ReturnOrderRequest()
    request.subOrderInfo = new SubOrderInfoRequest()
    request.subOrderInfo.subOrderNoList = [this.subOrderNo]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200

    const res = await tradeQueryGateway.pageReturnOrderInMyself({
      page,
      request
    })

    if (res?.data?.currentPageData?.length) {
      this.records = res.data.currentPageData.map(RefundRecordItem.fromCustomer)
    } else {
      this.records = new Array<RefundRecordItem>()
    }
  }
}
