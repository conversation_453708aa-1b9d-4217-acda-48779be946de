import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum TeacherEvaluationQuestionTypeEnum {
  /**
   * 教师评价题
   */
  TEACHER_EVALUATION_QUESTION = '9a9a9a8196768d780196768d78190000',
  /**
   * 线上教师评价题
   */
  TEACHER_ONLINE_EVALUATION_QUESTION = '9a9a9a8196768d780196768d78190001',
  /**
   * 线下教师评价题
   */
  TEACHER_OFFLINE_EVALUATION_QUESTION = '9a9a9a8196768d780196768d78190002'
}
class TeacherEvaluationQuestionType extends AbstractEnum<TeacherEvaluationQuestionTypeEnum> {
  static enum = TeacherEvaluationQuestionTypeEnum

  constructor(status?: TeacherEvaluationQuestionTypeEnum) {
    super()
    this.current = status
    this.map.set(TeacherEvaluationQuestionTypeEnum.TEACHER_EVALUATION_QUESTION, '教师评价题')
    this.map.set(TeacherEvaluationQuestionTypeEnum.TEACHER_ONLINE_EVALUATION_QUESTION, '线上教师评价题')
    this.map.set(TeacherEvaluationQuestionTypeEnum.TEACHER_OFFLINE_EVALUATION_QUESTION, '线下教师评价题')
  }
}
export default TeacherEvaluationQuestionType
