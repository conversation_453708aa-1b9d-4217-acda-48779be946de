<!--查看期别-->
<template>
  <el-drawer title="查看期别" :visible.sync="visible" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <!--条件查询-->
      <el-row :gutter="0" class="m-query">
        <el-form :inline="true" label-width="auto">
          <el-col :span="10">
            <el-form-item label="期别名称">
              <!--              <el-input v-model="params.issueName" clearable placeholder="请输入期别名称" />-->
              <biz-issue-auto-select v-model="params.issueName" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="报名时间">
              <double-date-picker
                :begin-create-time.sync="params.registerDateRange.startDate"
                :end-create-time.sync="params.registerDateRange.endDate"
                begin-time-placeholder="开始时间"
                end-time-placeholder="结束时间"
              ></double-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-button type="primary" @click="queryIssueList" :loading="loading">查询</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table stripe :data="issueList" class="m-table" max-height="600px" v-loading="loading">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="期别信息" min-width="240">
          <template slot-scope="scope">{{ scope.row.issueName || '-' }}/{{ scope.row.issueNo || '-' }}</template>
        </el-table-column>
        <el-table-column label="开放报名时间" min-width="240">
          <template slot-scope="scope">
            <template v-if="scope.row.isEnableStudentEnroll">
              <p><el-tag type="info" size="mini">开始</el-tag>{{ scope.row.registerBeginTime }}</p>
              <p v-if="scope.row.registerEndTimeType == EndTimeTypeEnum.assign">
                <el-tag type="info" size="mini">结束</el-tag>{{ scope.row.registerEndTime }}
              </p>
              <p v-else><el-tag type="info" size="mini">结束</el-tag>无关闭报名时间</p>
            </template>
            <p v-else>不开放报名</p>
          </template>
        </el-table-column>
        <el-table-column label="剩余报名人数" min-width="180">
          <template slot-scope="scope">{{ scope.row.remainingRegisterNumber }}</template>
        </el-table-column>
        <el-table-column label="操作" min-width="100" v-if="showAction">
          <template slot-scope="scope">
            <el-button type="text" @click="$util.copy(scope.row.issueName, $event)">复制期别名称</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="drawer-ft m-btn-bar">
      <!--      <el-button type="primary">确 定</el-button>-->
      <el-button @click="cancel">取 消</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Vue, Component, Prop } from 'vue-property-decorator'
  import QueryTrainClassIssue, {
    QueryTrainClassIssueListParam
  } from '@api/service/management/train-class/offlinePart/QueryTrainClassIssue'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { EndTimeTypeEnum } from '@api/service/common/scheme/enum/EndTimeType'
  import { bind, debounce } from 'lodash-decorators'
  import BizIssueAutoSelect from '@hbfe/jxjy-admin-components/src/biz/biz-issue-auto-select.vue'

  @Component({
    components: { BizIssueAutoSelect, DoubleDatePicker }
  })
  export default class extends Vue {
    @Prop({ type: Boolean, default: false }) showAction: boolean
    /**
     * 显隐控制
     */
    visible = false

    /**
     * 期别列表
     */
    issueList = new Array<IssueConfigDetail>()

    /**
     * 查询方法
     */
    queryTrainClassIssue = new QueryTrainClassIssue()

    /**
     * 期别关闭类型
     */
    EndTimeTypeEnum = EndTimeTypeEnum

    /**
     * 加载状态
     */
    loading = false

    /**
     * 查询参数
     */
    params = new QueryTrainClassIssueListParam()

    /**
     * 打开弹框
     */
    async open(commodityId: string) {
      if (!commodityId) {
        return
      }
      this.visible = true
      this.params = new QueryTrainClassIssueListParam()
      this.params.commoditySkuId = commodityId
      await this.queryIssueList()
    }

    /**
     * 查询期别列表
     */
    @bind
    @debounce(200)
    async queryIssueList() {
      try {
        this.loading = true
        this.issueList = await this.queryTrainClassIssue.queryTrainClassIssueList(this.params)
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 取消
     */
    cancel() {
      this.visible = false
    }
  }
</script>
