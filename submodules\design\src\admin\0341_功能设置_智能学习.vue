<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="注册登录" name="first">详见 0301_功能设置_注册登录.vue</el-tab-pane>
      <el-tab-pane label="集体报名" name="second">详见 0303_功能设置_集体报名.vue</el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="third">详见 0304_功能设置_增值税发票.vue</el-tab-pane>
      <el-tab-pane label="培训证明" name="fourth">详见 0305_功能设置_培训证明.vue</el-tab-pane>
      <el-tab-pane label="门户精品课程" name="five">详见 0307_功能设置_门户精品课程.vue</el-tab-pane>
      <el-tab-pane label="学习规则" name="sixth">详见 0321_功能设置_学习规则.vue</el-tab-pane>
      <el-tab-pane label="智能学习" name="seventh">
        <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb15 is-border-bottom">
          <div class="f-flex f-align-center">
            <div class="f-flex-sub">
              支持设置全网校智能学习的课程学习规则、考试规则。系统默认设置一套规则，若需要调整配置，修改后保存生效，新建智能学习任务的学员会按照保存的规则计算。
            </div>
            <el-button type="primary" size="small">使用默认配置</el-button>
          </div>
        </el-alert>
        <div class="f-p15">
          <el-card shadow="never" class="m-card is-header">
            <div slot="header" class="">
              <span class="tit-txt">课程学习规则</span>
            </div>
            <el-row type="flex" justify="center" class="width-limit">
              <el-col :md="20" :lg="16" :xl="13">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
                  <el-form-item>
                    <div slot="label">
                      <span class="f-cr f-mr5">*</span>
                      <span class="f-vm">每天学习时间</span>
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                        <div slot="content">
                          学习时间是指触发重新计算学习记录时，每门课程的学习时间会在设置的时间段内，时间段外的时间不会学习
                        </div>
                      </el-tooltip>
                      <span>：</span>
                    </div>
                    <div class="time-item form-l">
                      <div class="time">00:00:00 至 23:59:59</div>
                    </div>
                  </el-form-item>
                  <el-form-item label="每天不学习时间：">
                    <el-button type="primary" icon="el-icon-plus" plain>添加时间段</el-button>
                    <div class="time-item form-l f-mt15">
                      <div class="time">00:00:00 至 23:59:59</div>
                      <i class="f-link f-cb el-icon-delete f-f18"></i>
                    </div>
                    <div class="time-item form-l f-mt15">
                      <div class="time">00:00:00 至 23:59:59</div>
                      <i class="f-link f-cb el-icon-delete f-f18"></i>
                    </div>
                  </el-form-item>
                  <el-form-item label="首次开始学习时间：" required>
                    开通班级的
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <el-input placeholder="" class="input-num f-mlr5" />
                      <div slot="content">
                        请输入正整数
                      </div>
                    </el-tooltip>
                    <i class="f-mlr5">~</i>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <el-input placeholder="" class="input-num f-mlr5" />
                      <div slot="content">
                        请输入正整数
                      </div>
                    </el-tooltip>
                    天内随机开始学习。
                  </el-form-item>
                  <el-form-item label="每天最多学习时长：" required>
                    <el-radio-group v-model="form.resource">
                      <el-radio label="按课程学习学时"></el-radio>
                      <el-radio label="按课程物理时长"></el-radio>
                    </el-radio-group>
                    <div class="f-mt15">
                      每天课程学习最多
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <el-input placeholder="" class="input-num f-mlr5" />
                        <div slot="content">
                          请输入正整数
                        </div>
                      </el-tooltip>
                      学时且每次学习时长达到
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <el-input placeholder="" class="input-num f-mlr5" />
                        <div slot="content">
                          请输入正整数
                        </div>
                      </el-tooltip>
                      学时，随机休息 60~180 分钟。
                    </div>
                    <div class="f-mt15">
                      每天课程学习最多
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <el-input placeholder="" class="input-num f-mlr5" />
                        <div slot="content">
                          请输入正整数
                        </div>
                      </el-tooltip>
                      小时且每次学习时长达到
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <el-input placeholder="" class="input-num f-mlr5" />
                        <div slot="content">
                          请输入正整数
                        </div>
                      </el-tooltip>
                      小时，随机休息 60~180 分钟。
                    </div>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
            <!--测验规则-->
            <div class="m-tit is-border-bottom">
              <span class="tit-txt">测验规则</span>
            </div>
            <el-row type="flex" justify="center" class="width-limit">
              <el-col :md="20" :lg="16" :xl="13">
                <div class="f-mtb30 f-flex f-align-center">
                  <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
                  <span class="f-flex-sub"
                    >课程学习结束后进入对应的课程测验，测验开始时间和结束时间随机间隔分钟数15-60分钟。</span
                  >
                </div>
              </el-col>
            </el-row>

            <!--考试规则-->
            <div class="m-tit is-border-bottom">
              <span class="tit-txt">考试规则</span>
            </div>
            <el-row type="flex" justify="center" class="width-limit">
              <el-col :md="20" :lg="16" :xl="13">
                <div class="f-mt30 f-flex f-align-center">
                  <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
                  <span class="f-flex-sub"
                    >课程学习结束后进入考试，考试开始时间和结束时间随机间隔分钟数，最少间隔总考试时长的三分之一的时间。</span
                  >
                </div>
                <el-alert type="info" :closable="false" class="m-alert f-mt10 f-mb30">
                  例：考试总时长60分钟，开始和结束时间至少间隔20分钟。
                </el-alert>
              </el-col>
            </el-row>

            <!--问卷规则-->
            <div class="m-tit is-border-bottom">
              <span class="tit-txt">问卷规则</span>
            </div>
            <el-row type="flex" justify="center" class="width-limit">
              <el-col :md="20" :lg="16" :xl="13">
                <div class="f-mtb30 f-flex f-align-center">
                  <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
                  <span class="f-flex-sub"
                    >课程学习结束后进入问卷，问卷开始时间和结束时间随机间隔分钟数15-60分钟。</span
                  >
                </div>
              </el-col>
            </el-row>
          </el-card>
          <div class="m-btn-bar f-tc is-sticky-1">
            <el-button>取消</el-button>
            <el-button type="primary">保存</el-button>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'seventh',
        activeName1: 'first',
        activeName2: 'first',
        radio: 3,
        radio1: 6,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
