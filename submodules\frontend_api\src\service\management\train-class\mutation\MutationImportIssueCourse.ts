import IssueCourseDetail from '@api/service/common/scheme/model/IssueCourseDetail'
import { Response, ResponseStatus } from '@hbfe/common'
import FileInfo from '@api/service/common/file/FileInfo'
import { ImportStatusEnum } from '@api/service/management/train-class/offlinePart/enum/ImportStatus'
import SdsPlatformJxjyLearningschemeV1, {
  ImportCourseBatchRequest
} from '@api/platform-gateway/sds-platform-jxjy-learningscheme-v1'
import PlatformJxjyCommonImportQueryV1, {
  QueryImportTemplateRequest
} from '@api/platform-gateway/platform-jxjy-common-import-query-v1'
import moment from 'moment/moment'

/**
 * @description 导入期别课程
 */
class MutationImportIssueCourse {
  // region 请求入参

  /**
   * 任务ID
   * @private
   */
  private _taskId = ''

  /**
   * 文件名称
   */
  fileName = ''
  /**
   * 文件路径
   */
  filePath = ''

  // endregion

  // region 导入结果

  /**
   * 期别课程列表
   */
  issueCourseList: IssueCourseDetail[]
  /**
   * 导入状态
   */
  importStatus: ImportStatusEnum = ImportStatusEnum.not_yet
  /**
   * 导入失败文件信息
   * @description 仅导入失败后有值
   */
  importFailFileInfo = new FileInfo()

  // endregion

  /**
   * 查询批量导入课程模板
   */
  async queryBatchImportIssueCourseTemplate(): Promise<Response<string>> {
    const request = new QueryImportTemplateRequest()
    request.taskCategory = 'COURSE_IMPORT'
    return await PlatformJxjyCommonImportQueryV1.queryImportTemplate(request)
  }

  /**
   * 导入期别课程
   * @description
   * 500-接口异常
   * 3003-表头校验失败
   * 3004-excel表最大长度校验失败
   */
  async doImportIssueCourse(): Promise<ResponseStatus> {
    this.importStatus = ImportStatusEnum.importing
    let result = new ResponseStatus(200)
    const request = new ImportCourseBatchRequest()
    request.filePath = this.filePath
    const { status, data } = await SdsPlatformJxjyLearningschemeV1.batchImportCourse(request)
    result = status
    if (status && status.isSuccess() && data) {
      result = new ResponseStatus(Number(data.code), data.message)
      const { successCourseInfos, failureCourseInfos, excelFilePath } = data
      if (successCourseInfos && successCourseInfos.length) {
        if (!failureCourseInfos || !failureCourseInfos.length) {
          // 仅导入成功
          this.importStatus = ImportStatusEnum.success
        }
        this.issueCourseList = successCourseInfos.map((el) => {
          const opt = new IssueCourseDetail()
          opt.courseName = el.courseName
          opt.teacherName = el.teacherName
          opt.teacherPositionTile = el.teacherTitle
          opt.teacherUnitName = el.teacherUnit
          opt.coursePeriod = Number(el.period)
          opt.teachingDate = moment(el.teachingDate).format('YYYY-MM-DD')
          opt.courseBeginTime = el.teachingStartTime
          opt.courseEndTime = el.teachingEndTime
          return opt
        })
      }
      if (excelFilePath || !result.isSuccess()) {
        this.importStatus = ImportStatusEnum.fail
        this.importFailFileInfo.url = excelFilePath
      }
    } else {
      this.importStatus = ImportStatusEnum.fail
    }
    return result
  }
}

export default MutationImportIssueCourse
