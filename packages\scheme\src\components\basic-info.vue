<template>
  <el-card shadow="never" class="m-card" v-loading="uiConfig.loading.pageLoading">
    <div slot="header" class="">
      <span class="tit-txt">基础信息</span>
    </div>
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form
          ref="basicInfoForm"
          :model="form"
          :rules="rules"
          label-width="150px"
          class="m-form"
          @submit.native.prevent
        >
          <el-form-item label="培训形式" required>
            <el-radio-group v-model="form.schemeForm" :disabled="createMode === 3" @change="handleSchemeForm">
              <el-radio :label="TrainingModeEnum.online">
                {{ TrainingMode.map.get(TrainingModeEnum.online) }}
              </el-radio>
              <el-radio :label="TrainingModeEnum.offline" v-if="isOnlineClassSupport">
                {{ TrainingMode.map.get(TrainingModeEnum.offline) }}
              </el-radio>
              <el-radio :label="TrainingModeEnum.mixed" v-if="isOnlineClassSupport">
                {{ TrainingMode.map.get(TrainingModeEnum.mixed) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item
            label="方案类型："
            prop="schemeType"
            v-if="!(createMode === 3 && (recalculating || isIntelligenceLearning))"
          >
            <el-radio-group :value="defaultSchemeType" @input="changeClassType" :disabled="createMode === 3">
              <el-radio label="train_class">培训班</el-radio>
              <el-radio label="train_cooperate" v-if="isDockThirdParty && isOnline">{{
                modelSchemeType.getSchemeType(schemeTypeEnum.trainingCooperation)
              }}</el-radio>
            </el-radio-group>
            <el-tooltip effect="dark" placement="top" popper-class="m-tooltip" v-if="isDockThirdParty && isOnline">
              <i class="el-icon-question m-tooltip-icon f-c9 f-ml20"></i>
              <div slot="content">
                {{ contextTitle }}<br />
                {{ context }}
              </div>
            </el-tooltip>
          </el-form-item>
          <el-form-item
            label="网授选课方式"
            v-if="
              defaultSchemeType === 'train_class' &&
              !(createMode === 3 && (recalculating || isIntelligenceLearning)) &&
              form.schemeForm !== TrainingModeEnum.offline
            "
            prop="schemeType"
          >
            <el-radio-group v-model="form.schemeType" :disabled="createMode === 3" @change="handleSchemeType">
              <el-radio :label="schemeTypeEnum.chooseCourseLearning">
                {{ modelSchemeType.getSchemeType(schemeTypeEnum.chooseCourseLearning) }}
              </el-radio>
              <el-radio :label="schemeTypeEnum.autonomousCourseLearning">
                {{ modelSchemeType.getSchemeType(schemeTypeEnum.autonomousCourseLearning) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="方案封面：" prop="picture">
            <!--有新增或者修改的添加 <i class="is-tag"></i>-->
            <i class="is-tag" v-if="createMode === 3 && initForm.picture != form.picture"></i>
            <cover-image-upload
              :dialogStyleOpation="{
                width: '500px',
                height: '300px'
              }"
              :ratioArr="['420:237']"
              :initWidth="400"
              v-model="form.picture"
              :hasPreview="false"
              mode="400px 225px"
            ></cover-image-upload>
          </el-form-item>
          <el-form-item label="方案名称：" prop="name">
            <!--有新增或者修改的添加 <i class="is-tag"></i>-->
            <i class="is-tag" v-if="createMode === 3 && initForm.name != form.name"></i>
            <el-input v-model="form.name" maxlength="128" clearable placeholder="请输入方案名称" />
          </el-form-item>
          <sku-properties-linkage
            ref="skuRef"
            v-show="!(createMode === 3 && (recalculating || isIntelligenceLearning))"
            @setCommodityBasicInfo="setCommodityBasicInfo"
          />
          <el-form-item label="培训须知：">
            <el-input
              type="textarea"
              :rows="6"
              v-model="form.notice"
              maxlength="200"
              show-word-limit
              placeholder="培训须知会在培训班详情内展示"
              @input="changeInput"
            />
            <el-checkbox v-model="form.showNoticeDialog" :disabled="!form.notice">进班时弹窗提示培训须知</el-checkbox>
          </el-form-item>
          <el-form-item label="培训方案简介：">
            <hb-tinymce-editor v-model="form.introContent"></hb-tinymce-editor>
          </el-form-item>
        </el-form>
        <el-form
          v-if="schoolConfigFlag && form.schemeForm === TrainingModeEnum.online"
          label-width="150px"
          class="m-form"
        >
          <el-form-item label="成果是否同步：" prop="needDataSync">
            <el-radio-group v-model="form.needDataSync" :disabled="createMode === 3">
              <el-radio :label="true">同步</el-radio>
              <el-radio :label="false">不同步</el-radio>
            </el-radio-group>
            <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
              <i class="el-icon-question m-tooltip-icon f-c9 f-ml20"></i>
              <div slot="content">
                成果是否同步配置说明：<br />
                本网校默认开放学员培训成果对接第三方管理系统功能。
                如无需对接请选择不同步。请注意培训方案一经过发布该配置 不支持修改。
              </div>
            </el-tooltip>
          </el-form-item>
          <el-form-item
            v-if="form.needDataSync && schoolConfigFlag"
            label="成果开始时间规则："
            prop="achievementExhibition"
          >
            <el-radio-group v-model="form.achievementExhibition">
              <el-radio v-for="item in showAchievementExhibitionTypeList" :label="item.code" :key="item.code">{{
                item.desc
              }}</el-radio>
            </el-radio-group>
            <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
              <i class="el-icon-question m-tooltip-icon f-c9 f-ml20"></i>
              <div slot="content">培训成果数据中培训开始时间将根据此处配置自动回填，且配置仅对当前培训方案生效。</div>
            </el-tooltip>
          </el-form-item>
        </el-form>
        <slot name="thirdPlatformId" :form="form" :createMode="createMode"></slot>
      </el-col>
    </el-row>
  </el-card>
</template>

<script lang="ts">
  import { Component, Ref, Vue, PropSync, Prop } from 'vue-property-decorator'
  import CoverImageUpload from '@hbfe/jxjy-admin-scheme/src/components/functional-components/cover-image-upload.vue'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import SkuPropertiesLinkage from '@hbfe/jxjy-admin-scheme/src/components/functional-components/sku-properties-linkage.vue'
  import CreateSchemeUIModule from '@/store/modules-ui/scheme/CreateSchemeUIModule'
  import { cloneDeep, isBoolean } from 'lodash'
  import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
  import getServicerIsDocking from '@api/service/management/online-school-config/portal/query/QueryPortal'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import Context from '@api/service/common/context/Context'
  import SchemeType, { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import AchievementExhibition, {
    AchievementExhibitionEnum
  } from '@api/service/common/enums/train-class/AchievementExhibitionEnum'
  import QueryShowOffline from '@api/service/common/config/QueryShowOffline'

  /**
   * 基础数据表单项
   */
  export class BasicInfoForm {
    // 培训方案类型
    schemeType: number
    // 培训形式
    schemeForm: TrainingModeEnum
    // 方案封面
    picture: string
    // 方案名称
    name: string
    // 成果是否同步
    needDataSync: boolean
    // 培训须知
    notice: string
    // 培训方案简介
    introContent: string
    // 是否展示须知弹窗
    showNoticeDialog: boolean
    // 成果展示规则（培训开始时间）
    achievementExhibition: AchievementExhibitionEnum
  }

  @Component({
    components: {
      SkuPropertiesLinkage,
      CoverImageUpload
    }
  })
  export default class extends Vue {
    /**
     * 基础信息配置 - 双向绑定
     */
    @PropSync('basicInfo', { type: TrainClassBaseModel }) commodityBasicInfo!: TrainClassBaseModel
    /**
     * 培训班类型默认选中“培训班”
     */
    @Prop({
      type: String,
      default: 'train_class'
    })
    defaultSchemeType!: 'train_class' | 'train_cooperate'

    /**
     * 方案类型提示标题
     */
    @Prop({ type: String, default: '培训合作配置说明：' }) contextTitle: string
    /**
     * 方案类型提示文字
     */
    @Prop({ type: String, default: '方案如需开启指定平台的培训合作，请选择此方案类型。' }) context: string

    /**
     * 学习内容 - 双向绑定
     */
    @Prop({ type: LearningType }) learningType: LearningType

    @Ref('skuRef') skuRef: SkuPropertiesLinkage

    // 基础信息表单
    @Ref('basicInfoForm') basicInfoForm: any
    /**
     * 是否重算中
     */
    @Prop({ type: Boolean, default: false })
    recalculating: boolean
    /**
     * 是否智能学习中
     */
    @Prop({ type: Boolean, default: false })
    isIntelligenceLearning: boolean

    SchemeTypeList = [
      { label: '选课规则', value: 1 },
      { label: '自主选课', value: 2 }
    ]

    /**
     *  ui相关的变量控制
     */
    uiConfig = {
      // 元素是否加载动画
      loading: {
        // 页面元素 - 基础信息
        pageLoading: false
      }
    }

    /**
     * 表单验证页面绑定数据
     */
    form: BasicInfoForm = {
      schemeType: 1,
      schemeForm: TrainingModeEnum.online,
      picture: '',
      name: '',
      needDataSync: null,
      notice: '',
      introContent: '',
      showNoticeDialog: false,
      achievementExhibition: AchievementExhibitionEnum.signUpTime
    } as BasicInfoForm

    initForm: BasicInfoForm = {
      schemeType: 1,
      schemeForm: TrainingModeEnum.online,
      picture: '',
      name: '',
      needDataSync: null,
      notice: '',
      introContent: '',
      showNoticeDialog: false,
      achievementExhibition: AchievementExhibitionEnum.signUpTime
    } as BasicInfoForm
    // 表单校验规则
    rules = {
      // 方案类型
      schemeType: [{ required: true, message: '请选择方案类型', trigger: ['blur', 'change'] }],
      // 方案封面
      picture: [{ required: true, message: '请选择方案封面', trigger: ['blur', 'change'] }],
      // 方案名称
      name: [{ required: true, message: '请输入方案名称', trigger: ['blur', 'change'] }]
    }
    // 网校对接与否
    schoolConfigFlag = true
    /**
     * 是否对接第三方平台
     */
    isDockThirdParty = false

    /**
     * 上一个选中的
     */
    oldSelected = TrainingModeEnum.online
    /**
     * 创建模式，1-创建，2-复制，3-编辑，默认：0
     */
    get createMode() {
      return CreateSchemeUIModule.createMode
    }

    /**
     * 是否展示面网授
     */
    get isOnlineClassSupport() {
      const show = QueryShowOffline.getShowOfflineApolloConfig()
      return !show
    }

    /**
     * 是否网授
     */
    get isOnline() {
      return this.form.schemeForm === TrainingModeEnum.online
    }

    /**
     * 获取展示成果类别
     */
    get showAchievementExhibitionTypeList() {
      console.log(AchievementExhibition.list(), 'listxxxx')
      return AchievementExhibition.list()
    }

    changeClassType(value: 'train_class' | 'train_cooperate') {
      if (value === 'train_class' && this.commodityBasicInfo.schemeType === this.schemeTypeEnum.trainingCooperation) {
        this.handleSchemeType(this.schemeTypeEnum.chooseCourseLearning)
      }
      this.$emit('update:defaultSchemeType', value)
    }
    /**
     * 状态层定义方案类型
     */
    modelSchemeType = SchemeType
    /**
     * 状态层定义方案枚举
     */
    schemeTypeEnum = SchemeTypeEnum
    TrainingModeEnum = TrainingModeEnum
    TrainingMode = TrainingMode

    changeInput() {
      if (!this.form.notice) {
        this.form.showNoticeDialog = false
      }
    }

    /**
     * 页面初始化
     */
    async created() {
      // 是否对接第三方平台
      const dockThirdPartyServicerIds = CommonConfigCenter.getFrontendApplication(
        frontendApplication.dockThirdPartyServicerIds
      )
      if (dockThirdPartyServicerIds) {
        this.isDockThirdParty = dockThirdPartyServicerIds.includes(Context.servicerInfo.id)
      }

      this.uiConfig.loading.pageLoading = true
      this.schoolConfigFlag = await getServicerIsDocking.getServicerIsDocking()
      // await this.queryInstitutionList()
      await this.initData()
      this.uiConfig.loading.pageLoading = false
    }

    // async queryInstitutionList() {
    //   this.institutionList = await QueryInstitutionInfo.queryInstitutionListByNoPage()
    // }

    /**
     * 数据初始化
     */
    async initData() {
      // 创建
      if (this.createMode == 1) {
        this.commodityBasicInfo.schemeType = 1
        CreateSchemeUIModule.setSchemeType(1)
        this.form.schemeType = this.commodityBasicInfo.schemeType
        this.form.schemeForm = TrainingModeEnum.online
        this.handleSchemeForm(TrainingModeEnum.online, false)
        this.form.needDataSync = true
        this.$nextTick(async () => {
          await this.skuRef.initBase()
          this.skuRef.setDefaultIndustry()
        })
      } else {
        // 编辑或者复制
        const pictureReg = new RegExp(/^\/mfs\/.*/)
        //因为培训合作与方案类型共用同一字段，所以回显时，排除掉培训合作的情况
        if (this.commodityBasicInfo.schemeType !== 3) {
          this.form.schemeType = this.commodityBasicInfo.schemeType
          this.form.schemeForm = this.commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId
        }
        this.form.needDataSync = isBoolean(this.commodityBasicInfo.needDataSync)
          ? this.commodityBasicInfo.needDataSync
          : true
        this.form.achievementExhibition = this.commodityBasicInfo.achievementExhibition
        this.form.picture = pictureReg.test(this.commodityBasicInfo.picture)
          ? this.commodityBasicInfo.picture.replace(pictureReg, '')
          : this.commodityBasicInfo.picture
        this.form.name = this.createMode == 2 ? this.commodityBasicInfo.name + '_复制' : this.commodityBasicInfo.name
        this.form.notice = this.commodityBasicInfo.notice
        this.form.showNoticeDialog = this.commodityBasicInfo.showNoticeDialog
        this.form.introContent = this.commodityBasicInfo.introContent
        this.form.achievementExhibition = this.commodityBasicInfo.achievementExhibition
        this.initForm =
          this.createMode == 3
            ? JSON.parse(JSON.stringify(this.form))
            : ({
                schemeType: 1,
                schemeForm: TrainingModeEnum.online,
                picture: '',
                name: '',
                needDataSync: null,
                notice: '',
                introContent: '',
                showNoticeDialog: false,
                achievementExhibition: AchievementExhibitionEnum.signUpTime
              } as BasicInfoForm)
        console.log(this.initForm, this.form)
        // 编辑、复制 - 支持回显
        await this.$nextTick(async () => {
          // 回显培训班类型，培训合作
          this.commodityBasicInfo.schemeType === 3 && this.changeClassType('train_cooperate')
          this.form.schemeForm = this.commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId
          this.skuRef.skuProperty = cloneDeep(this.commodityBasicInfo.skuProperty)
          await this.skuRef.initBase()
          this.skuRef.skuProperties.industry = this.commodityBasicInfo.skuProperty.industry.skuPropertyValueId
          this.skuRef.skuProperties.year = this.commodityBasicInfo.skuProperty.year.skuPropertyValueId
          this.skuRef.skuProperties.region = this.getRegion(
            this.commodityBasicInfo.skuProperty.region.skuPropertyValueId
          )
          await this.skuRef.getIndustryDifference()
          if (this.skuRef.isSocietyIndustry || this.skuRef.isConstructionIndustry) {
            await this.skuRef.getSubjectTypeOptions()
            this.skuRef.skuProperties.subjectType = this.commodityBasicInfo.skuProperty.subjectType.skuPropertyValueId
          }
          if (this.skuRef.isSocietyIndustry) {
            await this.skuRef.getSocietyTrainingMajorOptionsV2()
            this.skuRef.skuProperties.societyTrainingMajor = this.getSocietyTrainingMajor(
              this.commodityBasicInfo.skuProperty.trainingMajor.skuPropertyValueId
            )
            await this.skuRef.societyTrainingMajorEcho(
              this.commodityBasicInfo.skuProperty.trainingMajor.skuPropertyValueId
            )
          }
          if (this.skuRef.isConstructionIndustry || this.skuRef.isOccupationalHealthIndustry) {
            await this.skuRef.getTrainingCategoryOptions()
            this.skuRef.skuProperties.trainingCategory =
              this.commodityBasicInfo.skuProperty.trainingCategory.skuPropertyValueId
          }
          if (this.skuRef.isConstructionIndustry) {
            await this.skuRef.getConstructionTrainingMajorOptions()
            this.skuRef.skuProperties.constructionTrainingMajor = this.getConstructionTrainingMajor(
              this.commodityBasicInfo.skuProperty.trainingMajor.skuPropertyValueId
            )
          }
          if (this.skuRef.isOccupationalHealthIndustry) {
            await this.skuRef.getTrainingObjectByOptions()
            this.skuRef.skuProperties.trainingObject =
              this.commodityBasicInfo.skuProperty.trainingObject.skuPropertyValueId
            await this.skuRef.getPositionCategoryOptions()
            this.skuRef.skuProperties.positionCategory =
              this.commodityBasicInfo.skuProperty.positionCategory.skuPropertyValueId
          }
          if (this.skuRef.isworkServiceIndustry) {
            await this.skuRef.getTechnologyLevelOptions()
            this.skuRef.skuProperties.jobLevel = this.commodityBasicInfo.skuProperty.jobLevel.skuPropertyValueId
          }
          if (this.skuRef.isTeacherIndustry) {
            await this.skuRef.getSubjectOptions(this.commodityBasicInfo.skuProperty.learningPhase.skuPropertyValueId)
            this.skuRef.skuProperties.studyPeriod = this.commodityBasicInfo.skuProperty.learningPhase.skuPropertyValueId
            this.skuRef.skuProperties.subject = this.commodityBasicInfo.skuProperty.discipline.skuPropertyValueId
          }
          if (this.skuRef.isPharmacistIndustry) {
            await this.skuRef.getSubjectTypeOptions()
            this.skuRef.skuProperties.subjectType = this.commodityBasicInfo.skuProperty.subjectType.skuPropertyValueId
            await this.skuRef.getPractCategory()

            this.skuRef.skuProperties.occupational[1] =
              this.commodityBasicInfo.skuProperty.practitionerCategory.skuPropertyValueId
            this.skuRef.skuProperties.occupational[0] =
              this.commodityBasicInfo.skuProperty.certificatesType.skuPropertyValueId
          }
        })
      }
    }

    /**
     * 获取表单绑定的地区路径 - 复制培训方案
     */
    getFormRegion(path: string): Promise<string[]> {
      if (!path) return
      const result = path.split('/') || ([] as string[])
      const res = result.length > 3 ? result.slice(1) : result
      return new Promise((resolve) => {
        resolve(res)
      })
    }

    getRegion(path: string): string[] {
      if (!path) return [] as string[]
      const result = path.split('/') || ([] as string[])
      return result
    }

    getSocietyTrainingMajor(path: string): string[] {
      if (!path) return [] as string[]
      const result = path.split('/') || ([] as string[])
      return result
    }

    getConstructionTrainingMajor(path: string): string {
      if (!path) return ''
      const result = path.split('/') || ([] as string[])
      return result.length >= 1 ? result[result.length - 1] : ''
    }
    /**
     * 用于保存最后一级选项
     */
    setValue(value: Array<string>): string {
      if (!value || !value.length) {
        return ''
      } else {
        return value[value.length - 1]
      }
    }

    /**
     * 设置业务状态层的培训专业值
     */
    // setBusinessTrainingMajorValue(form: BasicInfoForm): string {
    //   if (!form.industry) return ''
    //   // 人社行业
    //   if (
    //     this.envConfig.societyIndustryId &&
    //     form.industry === this.envConfig.societyIndustryId &&
    //     form.societyTrainingMajor &&
    //     form.societyTrainingMajor.length
    //   ) {
    //     const item = form.societyTrainingMajor
    //     return item[item.length - 1]
    //   }
    //   // 建设行业
    //   if (
    //     this.envConfig.constructionIndustryId &&
    //     form.industry === this.envConfig.constructionIndustryId &&
    //     form.constructionTrainingMajor
    //   ) {
    //     return form.constructionTrainingMajor
    //   }
    //   return ''
    // }

    /**
     * 设置数据
     */
    setCommodityBasicInfo() {
      const mfsHeadReg = /^\/mfs\/\.*/
      let picture = this.form.picture
      if (mfsHeadReg.test(this.form.picture)) {
        picture = picture.replace(mfsHeadReg, '')
      }
      this.commodityBasicInfo.schemeType = this.form.schemeType
      // 发布时，如果是培训合作，那么将培训方案值设置为3
      this.defaultSchemeType === 'train_cooperate' && (this.commodityBasicInfo.schemeType = 3)
      this.commodityBasicInfo.picture = picture
      this.commodityBasicInfo.name = this.form.name
      this.commodityBasicInfo.needDataSync = this.form.needDataSync
      this.commodityBasicInfo.notice = this.form.notice
      this.commodityBasicInfo.showNoticeDialog = this.form.showNoticeDialog
      this.commodityBasicInfo.introContent = this.form.introContent
      this.commodityBasicInfo.achievementExhibition = this.form.achievementExhibition
      // 以下属于sku
      this.commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId = this.form.schemeForm
      this.commodityBasicInfo.skuProperty.year.skuPropertyValueId = this.skuRef.skuProperties.year
      this.commodityBasicInfo.skuProperty.region.skuPropertyValueId = this.setValue(this.skuRef.skuProperties.region)
      this.commodityBasicInfo.skuProperty.industry.skuPropertyValueId = this.skuRef.skuProperties.industry
      this.commodityBasicInfo.skuProperty.subjectType.skuPropertyValueId = this.skuRef.skuProperties.subjectType
      this.commodityBasicInfo.skuProperty.jobLevel.skuPropertyValueId = this.skuRef.skuProperties.jobLevel
      this.commodityBasicInfo.skuProperty.trainingCategory.skuPropertyValueId =
        this.skuRef.skuProperties.trainingCategory
      if (this.skuRef.isConstructionIndustry) {
        this.commodityBasicInfo.skuProperty.trainingMajor.skuPropertyValueId =
          this.skuRef.skuProperties.constructionTrainingMajor
      } else if (this.skuRef.isSocietyIndustry) {
        this.commodityBasicInfo.skuProperty.trainingMajor.skuPropertyValueId = this.skuRef.skuProperties
          .societyTrainingMajor?.length
          ? this.setValue(this.skuRef.skuProperties.societyTrainingMajor)
          : ''
      } else {
        this.commodityBasicInfo.skuProperty.trainingMajor.skuPropertyValueId = ''
      }
      this.commodityBasicInfo.skuProperty.trainingObject.skuPropertyValueId = this.skuRef.skuProperties.trainingObject
      this.commodityBasicInfo.skuProperty.positionCategory.skuPropertyValueId =
        this.skuRef.skuProperties.positionCategory
      this.commodityBasicInfo.skuProperty.learningPhase.skuPropertyValueId = this.skuRef.skuProperties.studyPeriod
      this.commodityBasicInfo.skuProperty.discipline.skuPropertyValueId = this.skuRef.skuProperties.subject
      this.commodityBasicInfo.skuProperty.certificatesType.skuPropertyValueId =
        this.skuRef.skuProperties.occupational[0]
      this.commodityBasicInfo.skuProperty.practitionerCategory.skuPropertyValueId =
        this.skuRef.skuProperties.occupational[1]

      console.log(this.commodityBasicInfo, 'this.commodityBasicInfo')
    }

    /**
     * 切换培训方案类型
     */
    handleSchemeType(val: number) {
      CreateSchemeUIModule.setSchemeType(val)
      this.learningType.learningExperience.removeAllOutlineCourse()
      this.$emit('schemeType', this.form.schemeType)
    }

    /**
     * 切换培训方式
     */
    handleSchemeForm(val: TrainingModeEnum, noInit = true) {
      if (val !== TrainingModeEnum.online) {
        this.changeClassType('train_class')
        this.form.needDataSync = false
      }
      if (val === TrainingModeEnum.online && noInit) {
        return this.$confirm('当前操作将清空培训期别配置信息，包括期别引用的问卷，是否确定继续操作？', {
          confirmButtonText: '继续操作',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId = val
            this.oldSelected = val
            this.$emit('schemeFormChange', val)
          })
          .catch(() => {
            this.form.schemeForm = this.oldSelected
          })
      } else if (val === TrainingModeEnum.offline && noInit) {
        return this.$confirm('当前操作将清空网授部分学习方式配置信息，是否确定继续操作？', {
          confirmButtonText: '继续操作',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId = val
            this.oldSelected = val
            this.$emit('schemeFormChange', val)
          })
          .catch(() => {
            this.form.schemeForm = this.oldSelected
          })
      } else {
        this.commodityBasicInfo.skuProperty.trainingMode.skuPropertyValueId = val
        this.oldSelected = val
        this.$emit('schemeFormChange', val)
      }
    }

    /**
     * 表单校验 - 基础信息
     */
    validateForm() {
      let isValid = false
      this.setCommodityBasicInfo()
      this.basicInfoForm.validate((valid: number) => {
        if (valid) {
          isValid = true
        } else {
          isValid = false
        }
      })
      const result = this.skuRef.validateForm()
      if (!result) {
        isValid = false
      }
      return isValid
    }
  }
</script>
