<!--
  lxq
  html注释内容
  一：为修改学员信息组件   当前版本信息全部从第三方管理系统引入
  如后续需要修改个人信息  将注释取消即可
  注：技术等级、技术工种、所属工考办未完成除外
  二：行业信息 江苏工勤没有人社和建设行业、按原型需求将行业信息改为暂无数据
-->

<template>
  <div class="f-p15 web" v-if="$hasPermission('basicData')" desc="学员信息" actions="userIdChange,created">
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header" class="f-flex f-justify-between f-align-center">
        <span class="tit-txt">基础信息</span>
        <a
          class="f-cb"
          href="javascript:void(0);"
          v-if="$hasPermission('editCompany')"
          desc="编辑基础信息"
          actions="saveEditVal"
          @click="editBaseInfoOpen()"
          ><span class="el-icon-edit-outline edit-icon f-mr5"></span>编辑</a
        >
      </div>
      <div class="f-plr45 f-pt20 f-pb10" v-loading="uiStatus.query.loadBaseInfoDetail">
        <el-row :gutter="16">
          <el-form :inline="true" label-width="auto" class="m-text-form is-edit">
            <el-col :sm="12" :md="8">
              <el-form-item label="姓名：" class="is-editing">
                {{ studentParams.userName || '-' }}
                <!-- <template v-if="$hasPermission('editName')" desc="编辑姓名" actions="saveEditVal">
                  <basic-data-edit-value
                    v-if="editable"
                    v-model="studentUpdateObj.updateStudentParams.name"
                    placeholder="请输入姓名"
                    @saveEdit="saveEditVal"
                  ></basic-data-edit-value>
                </template> -->
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8">
              <el-form-item label="登录账号：" class="is-editing">
                {{ studentParams.loginAccount || '-' }}
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8">
              <!--点击编辑时 添加 is-editing-->
              <el-form-item label="证件号：" class="is-editing">
                {{ studentParams.idCard || '-' }}
                <!-- <template v-if="$hasPermission('editIdCard')" desc="编辑证件号" actions="saveEditVal">
                  <basic-data-edit-value
                    v-if="editable"
                    v-model="studentUpdateObj.updateStudentParams.idCard"
                    @saveEdit="saveEditVal"
                  ></basic-data-edit-value>
                </template> -->
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8">
              <el-form-item label="性别：" class="is-editing">
                {{ studentParams.genderName || '-' }}
                <!-- <template v-if="$hasPermission('editGender')" desc="编辑性别" actions="saveEditVal">
                  <basic-data-edit-value-radio
                    v-if="editable"
                    v-model="studentUpdateObj.updateStudentParams.gender"
                    placeholder="请选择性别"
                    @saveEdit="saveEditVal"
                  ></basic-data-edit-value-radio>
                </template> -->
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8">
              <!-- <el-form-item label="手机号：" class="is-editing">
                {{ studentParams.phone }} -->
              <!-- <basic-data-edit-value
                  v-model="studentParams.phone"
                  placeholder="请输入手机号"
                  @saveEdit="saveEditVal"
                ></basic-data-edit-value> -->
              <!-- </el-form-item> -->
              <!--点击编辑时 添加 is-editing-->
              <el-form-item label="手机号：" class="is-editing">
                {{ studentParams.phone || '-' }}
                <!-- <template v-if="$hasPermission('editPhone')" desc="编辑手机号" actions="saveEditVal">
                  <basic-data-edit-value
                    v-if="editable"
                    v-model="studentUpdateObj.updateStudentParams.phone"
                    placeholder="请输入手机号"
                    @saveEdit="saveEditVal"
                  ></basic-data-edit-value>
                </template> -->

                <!-- <template v-if="$hasPermission('editPhone') && editable" desc="编辑手机号" actions="savePhone">
                  <div class="edit-box" v-if="isRedact">
                    <el-input v-model="phonenNum" clearable placeholder="请输入手机号" class="f-flex-sub" />
                    <div class="op">
                      <el-tooltip class="item" effect="dark" placement="top">
                        <span class="el-icon-circle-check f-cb edit-icon" @click="savePhone()"></span>
                        <div slot="content">保存</div>
                      </el-tooltip>
                      <el-tooltip class="item" effect="dark" placement="top">
                        <span class="el-icon-circle-close f-c9 edit-icon" @click="unedit"></span>
                        <div slot="content">取消</div>
                      </el-tooltip>
                    </div>
                  </div>
                </template> -->
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8">
              <el-form-item label="单位地区：" class="is-editing">
                {{ transfromRegion(studentParams.companyRegion) }}
                <!-- <template v-if="$hasPermission('editRegionCode')" desc="编辑单位地区" actions="saveEditVal">
                  <basic-data-edit-value-cascader
                    v-if="editable"
                    v-model="companyRegionCodeList"
                    :regionOptions="regionOptions"
                    @saveEdit="saveEditVal"
                  ></basic-data-edit-value-cascader>
                </template> -->
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8">
              <el-form-item label="工作单位：" class="is-editing">
                {{ studentParams.companyName || '-' }}
                <!-- <template v-if="$hasPermission('editCompanyName')" desc="编辑工作单位" actions="saveEditVal">
                  <basic-data-edit-value
                    v-if="editable"
                    v-model="studentUpdateObj.updateStudentParams.companyName"
                    placeholder="请输入工作单位"
                    @saveEdit="saveEditVal"
                  ></basic-data-edit-value>
                </template> -->
              </el-form-item>
            </el-col>

            <!--            <el-col :sm="12" :md="8">-->
            <!--              <el-form-item label="技术等级："-->
            <!--                >{{ studentParams.professionalLevelName }}-->
            <!--                <basic-data-edit-value-->
            <!--                  v-model="studentParams.companyName"-->
            <!--                  placeholder="请输入技术等级"-->
            <!--                  @saveEdit="saveEditVal"-->
            <!--                ></basic-data-edit-value>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <!--            <el-col :sm="12" :md="8">-->
            <!--              <el-form-item label="技术工种："-->
            <!--                >{{ studentParams.jobCategoryName }}-->
            <!--                <basic-data-edit-value-->
            <!--                  v-model="studentParams.companyName"-->
            <!--                  placeholder="请输入技术工种"-->
            <!--                  @saveEdit="saveEditVal"-->
            <!--                ></basic-data-edit-value>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <!--            <el-col :sm="12" :md="8">-->
            <!--              <el-form-item label="所属工考办：">-->
            <!--                {{ transfromRegion(studentParams.managementUnitRegion) }}-->
            <!--                <basic-data-edit-value-->
            <!--                  v-model="studentParams.companyName"-->
            <!--                  placeholder="请输入所属工考办"-->
            <!--                  @saveEdit="saveEditVal"-->
            <!--                ></basic-data-edit-value>-->
            <!--              </el-form-item>-->
            <!--            </el-col>-->
            <el-col :sm="12" :md="8">
              <el-form-item label="注册时间：">{{ studentParams.createTime || '-' }}</el-form-item>
            </el-col>
            <el-col :sm="12" :md="8">
              <el-form-item label="注册来源：">{{ getSourceTypeName(studentParams.sourceType) }}</el-form-item>
            </el-col>
            <el-col :sm="12" :md="8">
              <el-form-item label="证件类型：">{{ idTypeName(studentParams.idCardType) }}</el-form-item>
            </el-col>
          </el-form>
        </el-row>
      </div>
    </el-card>
    <!-- 密码相关 -->
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">密码相关</span>
      </div>
      <div class="f-plr30 f-pt15 f-pb20">
        <el-button
          type="primary"
          size="small"
          class="f-ml10"
          @click="uiStatus.isShow.resetPwdDialog = true"
          :disabled="!editable"
          >重置密码</el-button
        >
        <span class="f-ml10 f-co">注：密码默认重置为abc123</span>
      </div>
      <el-dialog title="重置密码" :visible.sync="uiStatus.isShow.resetPwdDialog" width="380px" class="m-dialog">
        <div>
          你正在重置【<span class="f-cr">{{ studentParams.userName }}</span
          >】学员的密码为：<span class="f-cr">abc123</span>，重置后密码会同步生效，是否确认重置？
        </div>
        <div slot="footer">
          <el-button @click="uiStatus.isShow.resetPwdDialog = false">取 消</el-button>
          <template v-if="$hasPermission('confirmResetPwd')" desc="重置密码" actions="confirmResetPwd">
            <el-button type="primary" @click="confirmResetPwd">确 定</el-button>
          </template>
        </div>
      </el-dialog>
    </el-card>
    <!-- 行业信息 -->
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">行业信息</span>
      </div>
      <div class="f-pb20 f-pt10">
        <template v-if="rsIndustry || jsIndustry || wsIndustry || gqIndustry || teacherIndustry || ysIndustry">
          <!-- 人社行业 -->
          <template v-if="rsIndustry">
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">人社行业</span>
            </div>
            <div class="f-plr40" v-loading="uiStatus.query.loadBaseInfoDetail">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                  <el-col :sm="12" :md="8">
                    <el-form-item label="专业类别：" class="is-editing">
                      {{
                        studentParams.rsStudentIndustryInfo
                          ? studentParams.rsStudentIndustryInfo.firstProfessionalCategoryName
                            ? studentParams.rsStudentIndustryInfo.firstProfessionalCategoryName
                            : '-'
                          : '-'
                      }}
                      <span
                        v-if="
                          studentParams.rsStudentIndustryInfo &&
                          studentParams.rsStudentIndustryInfo.secondProfessionalCategoryName
                        "
                      >
                        > {{ studentParams.rsStudentIndustryInfo.secondProfessionalCategoryName }}
                      </span>
                      <template
                        v-if="$hasPermission('editProfessionalCategoryName')"
                        desc="编辑人社专业类别"
                        actions="saveEditVal"
                      >
                        <basic-data-edit-value-major-select
                          v-if="editable"
                          v-model="rsProfessionalCategory"
                          :industryPropertyId="rsIndustryPropertyId"
                          :industryId="industryId"
                          @saveEdit="saveEditVal"
                        ></basic-data-edit-value-major-select>
                      </template>
                    </el-form-item>
                  </el-col>

                  <el-col :sm="12" :md="8">
                    <el-form-item label="职称等级：" class="is-editing">
                      {{
                        studentParams.rsStudentIndustryInfo &&
                        studentParams.rsStudentIndustryInfo.professionalQualification
                          ? getPositionLevelNameById(studentParams.rsStudentIndustryInfo.professionalQualification)
                          : '-'
                      }}
                      <template
                        v-if="$hasPermission('editProfessionalQualification')"
                        desc="编辑人社职称等级"
                        actions="saveEditVal"
                      >
                        <basic-data-edit-value-professional-level
                          v-if="editable"
                          v-model="studentUpdateObj.updateStudentParams.rsUserIndustryInfo.professionalQualification"
                          @saveEdit="saveEditVal"
                        ></basic-data-edit-value-professional-level>
                      </template>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </template>
          <!-- 建设行业 -->
          <template v-if="jsIndustry">
            <div class="m-sub-tit is-border-bottom f-mt10">
              <span class="tit-txt">建设行业</span>
            </div>
            <div class="f-plr40">
              <el-button type="primary" icon="el-icon-plus" size="small" class="f-mt20" @click="openCertificationDrawer"
                >新增证书</el-button
              >
              <template
                v-if="studentParams.jsStudentIndustryInfo && studentParams.jsStudentIndustryInfo.userCertificateList"
              >
                <div v-for="(item, i) in studentParams.jsStudentIndustryInfo.userCertificateList" :key="i">
                  <p class="f-cb f-mt20 f-f15">{{ item.certificateCategoryName || '-' }}</p>
                  <el-table stripe max-height="500px" class="m-table f-mt10" :data="[item]">
                    <el-table-column label="专业" min-width="200" fixed="left">
                      <template slot-scope="scope">
                        {{ scope.row.registerProfessionalName || '-' }}
                      </template>
                    </el-table-column>
                    <el-table-column label="证书编号" width="200">
                      <template slot-scope="scope">{{ scope.row.certificateCode || '-' }}</template>
                    </el-table-column>
                    <el-table-column label="证书发放日期-证书有效期" min-width="200">
                      <template slot-scope="scope"
                        >{{ scope.row.releaseStartTime || '-' }}<span class="f-plr10"> - </span
                        >{{ scope.row.certificateEndTime || '-' }}</template
                      >
                    </el-table-column>
                    <el-table-column label="附件" width="400">
                      <template slot-scope="scope">
                        <ul
                          class="m-certificate-img f-clear"
                          v-if="scope.row.attachmentInfoList && scope.row.attachmentInfoList.length"
                        >
                          <li v-for="sub in scope.row.attachmentInfoList" :key="sub.name">
                            <el-image :src="sub.url" :preview-src-list="[sub.url]" class="img"> </el-image>
                          </li>
                        </ul>
                      </template>
                    </el-table-column>
                    <el-table-column label="编辑" min-width="100" align="center" fixed="right">
                      <template>
                        <el-button type="text" size="mini" @click="modifyCertification(item)">编辑</el-button>
                        <template
                          v-if="$hasPermission('deleteAttachmentInfoItem')"
                          desc="删除行业证书"
                          actions="deleteCertification"
                        >
                          <el-button type="text" size="mini" @click="deleteCertification(item)">删除</el-button>
                        </template>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </template>
            </div>
          </template>
          <!-- 卫生行业 -->
          <template v-if="wsIndustry">
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">职业卫生行业</span>
            </div>
            <div class="f-plr40" v-loading="uiStatus.query.loadBaseInfoDetail">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                  <el-col :sm="12" :md="8">
                    <el-form-item label="人员类别：" class="is-editing">
                      {{
                        studentParams.wsStudentIndustryInfo.personnelCategoryName
                          ? studentParams.wsStudentIndustryInfo.personnelCategoryName
                          : '-'
                      }}
                      <template
                        v-if="$hasPermission('editPersonnelCategoryName')"
                        desc="编辑卫生人员类别"
                        actions="saveEditVal"
                      >
                        <basic-data-edit-value-person-select
                          v-if="editable"
                          v-model="studentUpdateObj.updateStudentParams.wsUserIndustryInfo.personnelCategory"
                          :industry-property-id="wsIndustryPropertyId"
                          :industryId="wsIndustryId"
                          @saveEdit="saveEditVal"
                          @input="saveNewValue"
                        ></basic-data-edit-value-person-select>
                      </template>
                    </el-form-item>
                  </el-col>

                  <el-col :sm="12" :md="8">
                    <el-form-item label="岗位类别：" class="is-editing">
                      {{
                        studentParams.wsStudentIndustryInfo.positionCategoryName
                          ? studentParams.wsStudentIndustryInfo.positionCategoryName
                          : '-'
                      }}
                      <template
                        v-if="$hasPermission('editPositionCategoryName')"
                        desc="编辑卫生岗位类别"
                        actions="saveEditVal"
                      >
                        <basic-data-edit-value-job-category-select
                          v-if="this.studentParams.wsStudentIndustryInfo.personnelCategory"
                          v-model="studentUpdateObj.updateStudentParams.wsUserIndustryInfo.positionCategory"
                          :industry-property-id="wsIndustryPropertyId"
                          :industryId="wsIndustryId"
                          :trainingObject="trainingObject"
                          @saveEdit="saveEditVal"
                        ></basic-data-edit-value-job-category-select>
                      </template>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </template>
          <!-- 工勤行业 -->
          <template v-if="gqIndustry">
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">工勤行业</span>
            </div>
            <div class="f-plr40" v-loading="uiStatus.query.loadBaseInfoDetail">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                  <el-col :sm="12" :md="8">
                    <el-form-item label="技术等级：" class="is-editing">
                      {{
                        studentParams.gqStudentIndustryInfo.professionalLevelName
                          ? studentParams.gqStudentIndustryInfo.professionalLevelName
                          : '-'
                      }}
                      <template
                        v-if="$hasPermission('editProfessionalLevelName')"
                        desc="编辑工勤技术等级"
                        actions="saveEditVal"
                      >
                        <basic-data-edit-value-technical-grade
                          v-model="studentUpdateObj.updateStudentParams.gqUserIndustryInfo.professionalLevel"
                          :industryPropertyId="gqIndustryPropertyId"
                          :industryId="gqIndustryId"
                          @saveEdit="saveEditVal"
                        ></basic-data-edit-value-technical-grade>
                      </template>
                    </el-form-item>
                  </el-col>

                  <el-col :sm="12" :md="8">
                    <el-form-item label="工种：" class="is-editing">
                      {{
                        studentParams.gqStudentIndustryInfo.jobCategoryName
                          ? studentParams.gqStudentIndustryInfo.jobCategoryName
                          : '-'
                      }}
                      <template
                        v-if="$hasPermission('editJobCategoryIdName')"
                        desc="编辑工勤工种"
                        actions="saveEditVal"
                      >
                        <basic-data-edit-value-type-work
                          v-model="studentUpdateObj.updateStudentParams.gqUserIndustryInfo.jobCategoryId"
                          :industryPropertyId="gqIndustryPropertyId"
                          :industryId="gqIndustryId"
                          @saveEdit="saveEditVal"
                        ></basic-data-edit-value-type-work>
                      </template>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </template>
          <!-- 教师行业 -->
          <template v-if="teacherIndustry">
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">教师行业</span>
            </div>
            <div class="f-plr40">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                  <el-col :sm="12" :md="8">
                    <el-form-item label="学段学科：" class="is-editing">
                      <span
                        v-if="
                          studentParams.lsStudentIndustryInfo.sectionAndSubjectsName.length &&
                          studentParams.lsStudentIndustryInfo.sectionAndSubjectsName[0].section
                        "
                        >{{ studentParams.lsStudentIndustryInfo.sectionAndSubjectsName[0].section
                        }}<span v-if="studentParams.lsStudentIndustryInfo.sectionAndSubjectsName[0].subjects"
                          >-{{ studentParams.lsStudentIndustryInfo.sectionAndSubjectsName[0].subjects }}</span
                        ></span
                      ><span v-else>-</span>
                      <template
                        v-if="$hasPermission('editStudyperiodSubjectName')"
                        desc="编辑学段学科"
                        actions="saveEditVal"
                      >
                        <BasicDataEditValuePeriod
                          v-model="teacherParams"
                          :learningPhase.sync="studentUpdateObj.updateStudentParams.lsUserIndustryInfo.grade"
                          :disciplin.sync="studentUpdateObj.updateStudentParams.lsUserIndustryInfo.subject"
                          :industryId="teacherIndustryId"
                          @saveEdit="saveEditVal"
                        ></BasicDataEditValuePeriod>
                      </template>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </template>

          <!-- 药师行业 -->
          <template v-if="ysIndustry">
            <div class="m-sub-tit is-border-bottom">
              <span class="tit-txt">药师行业</span>
            </div>
            <div class="f-plr40" v-loading="uiStatus.query.loadBaseInfoDetail">
              <el-row :gutter="16">
                <el-form :inline="true" label-width="auto" class="m-text-form is-edit f-mt20">
                  <el-col :sm="12" :md="8">
                    <el-form-item label="证书类型：" class="is-editing">
                      {{ studentParams.ysStudentIndustryInfo.certificatesTypeName || '-' }}
                      <template
                        v-if="$hasPermission('editProfessionalQualification')"
                        desc="编辑证书类型"
                        actions="saveEditVal"
                      >
                        <basic-data-edit-value-certificate-type
                          :industryId="ysIndustryId"
                          v-model="studentUpdateObj.updateStudentParams.ysUserIndustryInfo.certificatesType"
                          @saveEdit="saveEditVal"
                        ></basic-data-edit-value-certificate-type>
                      </template>
                    </el-form-item>
                  </el-col>

                  <el-col :sm="12" :md="8">
                    <el-form-item label="执业类别：" class="is-editing">
                      {{ studentParams.ysStudentIndustryInfo.practitionerCategoryName || '-' }}
                      <template
                        v-if="$hasPermission('editProfessionalQualification')"
                        desc="编辑执业类别"
                        actions="saveEditVal"
                      >
                        <basic-data-edit-value-practice-category
                          :industryId="studentUpdateObj.updateStudentParams.ysUserIndustryInfo.certificatesType"
                          v-model="studentUpdateObj.updateStudentParams.ysUserIndustryInfo.practitionerCategory"
                          @saveEdit="saveEditVal"
                        ></basic-data-edit-value-practice-category>
                      </template>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </template>
        </template>
        <!-- no data -->
        <div v-else class="m-no-date f-ptb30">
          <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
          <div class="date-bd">
            <p class="f-f15 f-c9">暂无数据</p>
          </div>
        </div>
      </div>
      <!-- 新增/编辑证书抽屉 -->
      <template
        v-if="$hasPermission('updateAndEditCertifition')"
        desc="新增/编辑证书抽屉"
        actions="handleAddCertifition,handleUpdateCertifition"
      >
        <add-certification-drawer
          ref="AddCertificationDrawerRef"
          @confirmAddCertifition="handleAddCertifition"
          @confirmUpdateCertifition="handleUpdateCertifition"
        ></add-certification-drawer>
      </template>
    </el-card>
    <!-- 微信相关 -->
    <el-card shadow="never" class="m-card is-header f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">微信相关</span>
      </div>
      <div class="f-pt10">
        <div class="f-plr40">
          <el-row :gutter="16">
            <el-form :inline="true" label-width="auto" class="m-form f-mt10">
              <el-col :sm="12" :md="8">
                <el-form-item label="绑定微信：" v-if="editable">{{
                  getBindWXStatus(studentParams.bindWX)
                }}</el-form-item>
                <el-form-item label="绑定微信：" v-else>-</el-form-item>
              </el-col>
              <el-col :sm="12" :md="8">
                <el-form-item label="微信名称：">
                  {{ studentParams.nickNameByWX || '-' }}
                  <el-button type="primary" size="small" class="f-ml10" @click="wxUnBindDialogShow()" v-if="editable"
                    >微信解绑</el-button
                  >
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </div>
      </div>
      <el-dialog title="解绑微信" :visible.sync="uiStatus.isShow.wxUnBindDialog" width="380px" class="m-dialog">
        <div>
          你正在对【<span class="f-cr">{{ studentParams.userName }}</span
          >】学员帐号解绑微信，解绑后需重新绑定微信，是否确认解绑？
        </div>
        <div slot="footer">
          <el-button @click="uiStatus.isShow.wxUnBindDialog = false">取 消</el-button>
          <template v-if="$hasPermission('unbundle')" desc="微信解绑" actions="confirmUnbindwx">
            <el-button type="primary" @click="confirmUnbindwx">确 定</el-button>
          </template>
        </div>
      </el-dialog>
    </el-card>

    <!-- 人脸识别基准照 -->
    <el-row :gutter="15" class="is-height">
      <el-col :md="8">
        <el-card shadow="never" class="m-card is-header f-mb15">
          <div slot="header" class="">
            <span class="tit-txt">人脸识别基准照</span>
          </div>
          <div class="f-p20 f-tc">
            <div class="u-benchmark-photos-big">
              <img v-if="uiImage" class="photo" :src="[uiImage]" alt="" />
              <img v-else class="photo" src="@design/admin/assets/images/face-pic.jpg" alt="" />

              <template v-if="$hasPermission('revisePhotoTime')" desc="调整基准照次数" actions="@ReferencePhoto">
                <el-button
                  type="danger"
                  class="f-mt10"
                  @click="referencePhotoTime"
                  :disabled="!editable || !userDatum.userDatum.haveCurrentDatum"
                  >调整基准照片修改次数</el-button
                >
              </template>
              <template v-if="$hasPermission('deletePhoto')" desc="删除基准照" actions="doRemove">
                <el-button
                  type="primary"
                  class="f-mt10"
                  @click="RemoveReferencePhoto"
                  :disabled="!editable || !userDatum.userDatum.haveCurrentDatum"
                  >删除基准照</el-button
                >
              </template>
              <!-- {{ userDatum.userDatum.haveCurrentDatum || '********' }} -->
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :md="16">
        <el-card shadow="never" class="m-card is-header f-mb15">
          <div slot="header" class="">
            <span class="tit-txt">基准照片调整记录</span>
          </div>
          <!-- 有历史基准照记录的 -->
          <div class="f-p20" v-if="historyReferencePhoto">
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="操作时间" min-width="200">
                <template slot-scope="scope">{{ scope.row.createdTime }}</template>
              </el-table-column>
              <el-table-column label="剩余次数" min-width="150">
                <template slot-scope="scope">{{ scope.row.residueUpdateCount }}</template>
              </el-table-column>
              <el-table-column label="基准照片" min-width="150" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-image
                    style="width: 70px; height: 100px"
                    :src="historyAdjustPhoto(scope.row.photoPaths[0])"
                    :preview-src-list="[historyAdjustPhoto(scope.row.photoPaths[0])]"
                  ></el-image>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!--用戶沒有基准照的時候-展示空数据-->
          <div class="m-no-date f-mt50" v-else>
            <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
            <div class="date-bd">
              <p class="f-f15 f-c9">暂无数据~</p>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 删除弹框 -->
    <el-dialog title="提示" :visible.sync="deleteDialog" width="450px" class="m-dialog">
      <div class="dialog-alert">
        <span class="txt">删除学员基准照后，学员需重新采集基准照，确认要删除？</span>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="doRemove" :loading="loading">确 定</el-button>
        <el-button @click="deleteDialog = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 调整基准照片修改次数 - 组件-->
    <reference-photo :user-id="userId" :dialog-ctrl.sync="referencePhotoDialog"></reference-photo>
    <editBaseInfoDialog
      ref="EditBaseInfoDialogRef"
      :unitConfig="unitConfig"
      :studentParams="studentParams"
      @updateUserInfo="updateUserInfo"
    >
    </editBaseInfoDialog>
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Watch, Prop, Mixins } from 'vue-property-decorator'
  import BasicDataEditValue from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/basic-data-edit-value.vue'
  import BasicDataEditValuePeriod from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/base-data-edit-value-period.vue'
  import BasicDataEditValueCertificateType from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/basic-data-edit-value-certificate-type.vue'
  import BasicDataEditValuePracticeCategory from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/basic-data-edit-value-practice-category.vue'
  import AddCertificationImages from '@hbfe/jxjy-admin-components/src/upload-images.vue'
  import UserModule from '@api/service/management/user/UserModule'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
  import ProfessionalLevel from '@hbfe/jxjy-admin-customerService/src/personal/components/components/professional-level.vue'
  import BasicDataEditValueProfessionalLevel from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/basic-data-edit-value-professional-level.vue'
  import BasicDataEditValueJobCategorySelect from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/basic-data-edit-value-job-category-select.vue'
  import BasicDataEditValueTypeWork from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/basic-data-edit-value-type-work.vue'
  import BasicDataEditValueTechnicalGrade from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/basic-data-edit-value-technical-grade.vue'
  import BasicDataEditValueMajorSelect from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/basic-data-edit-value-major-select.vue'
  import BasicDataEditValuePersonSelect from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/basic-data-edit-value-person-select.vue'
  import BasicDataEditValueRadio from '@hbfe/jxjy-admin-customerService/src/personal/personal-components/basic-data-edit-value-radio.vue'
  import AddCertificationDrawer, {
    CertificationUrl,
    DrawerForm
  } from '@hbfe/jxjy-admin-customerService/src/personal/components/components/add-certification-drawer.vue'
  import PersonalBaseData from '@hbfe/jxjy-admin-customerService/src/diff/jxgx/personal/PersonalBaseData'
  import CertificateInfoVo from '@api/service/management/user/mutation/student/vo/CertificateInfoVo'
  import UserIndustryJsInfo from '@api/service/management/user/mutation/student/vo/UserIndustryJsInfo'
  import UserIndustryRsInfo from '@api/service/management/user/mutation/student/vo/UserIndustryRsInfo'
  import editBaseInfoDialog from '@hbfe/jxjy-admin-customerService/src/diff/jxgx/personal/__components__/components/editBaseInfoDialog.vue'
  import {
    CertificateInfo,
    CertificateAttachment,
    DeleteStudentCertificateRequest
  } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1/index'
  import {
    StudentCertificateResponse,
    AttachmentInfoResponse
  } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage/index'
  import StudentCertificateInfoVo from '@api/service/management/user/query/student/vo/StudentCertificateInfoVo'
  import { RegionModel } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import MutationUpdateStudentInfo from '@api/service/management/user/mutation/student/MutationUpdateStudentInfo'
  import {
    CreateStudentCertificateRequest,
    UpdateStudentCertificateRequest
  } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryIndustry'
  import { cloneDeep } from 'lodash'
  import ReferencePhoto from '@hbfe/jxjy-admin-customerService/src/personal/components/components/reference-photo.vue'
  import UserDatum from '@hbfe-biz/biz-anticheat/dist/datum/UserDatum'
  import UserDatumDetali from '@hbfe-biz/biz-anticheat/dist/datum/models/UserDatumModel'
  import SingleDatumModel from '@hbfe-biz/biz-anticheat/dist/datum/base-models/SingleDatumModel'
  import FileModule from '@api/service/common/file/FileModule'
  import QueryTrainingCategory from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryTrainingCategory'
  import MajorParam from '@api/service/common/basic-data-dictionary/query/vo/majorParam'
  import QueryTrainingMajor from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
  import QueryIdCardType from '@api/service/common/basic-data-dictionary/query/QueryIdCardType'
  import IdCardTypeVo from '@api/service/common/basic-data-dictionary/query/vo/IdCardTypeVo'
  import QueryPortal from '@api/service/management/online-school-config/portal/query/QueryPortal'
  import UnitConfig from '@api/service/management/online-school-config/portal/models/UnitConfig'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'
  @Component({
    components: {
      BasicDataEditValue,
      AddCertificationImages,
      ProfessionalLevel,
      BasicDataEditValueProfessionalLevel,
      BasicDataEditValueJobCategorySelect,
      BasicDataEditValueTypeWork,
      BasicDataEditValueTechnicalGrade,
      BasicDataEditValueMajorSelect,
      BasicDataEditValuePersonSelect,
      BasicDataEditValueRadio,
      AddCertificationDrawer,
      ReferencePhoto,
      BasicDataEditValuePeriod,
      BasicDataEditValueCertificateType,
      BasicDataEditValuePracticeCategory,
      editBaseInfoDialog
    }
  })
  export default class extends Mixins(PersonalBaseData) {
    @Ref('EditBaseInfoDialogRef')
    EditBaseInfoDialogRef: editBaseInfoDialog
    @Ref('AddCertificationDrawerRef')
    AddCertificationDrawerRef: AddCertificationDrawer
    // 用户基准照
    userDatum = new UserDatum()
    // 用戶基准照信息
    UserDatumDetali = new UserDatumDetali()
    // 学员详情实例
    studentDetailObj = UserModule.queryUserFactory.queryStudentDetail
    // 学员修改实例
    studentUpdateObj = UserModule.mutationUserFactory.mutationUpdateStudentInfo
    // 修改入参
    studentParams = new UserDetailVo()
    // 重置密码实例
    resetPwdObj = UserModule.mutationUserFactory.resetPwdBussiness
    // 用户业务工厂
    mutationUserFactory = UserModule.mutationUserFactory
    // 建设行业证书操作
    mutationUpdateStudentInfo = new MutationUpdateStudentInfo()
    // 是否可编辑
    editable = false

    uiImage = ''

    // 学员详情
    stuDetail = new UserDetailVo()
    drawerTitle = '新增证书'
    // 修改手机号入口显隐
    isRedact = false
    // 手机号
    phonenNum = ''

    // 加载loaidng效果
    loading = false

    trainingObject = ''

    // 人社专业类别id
    rsProfessionalCategory: Array<string> = []

    // 卫生人员类别id
    wsProfessionalCategory: Array<string> = []

    // 页面显隐状态控制
    uiStatus = {
      isShow: {
        // 微信解绑弹窗
        wxUnBindDialog: false,
        // 重置密码弹窗
        resetPwdDialog: false
      },
      query: {
        loadBaseInfoDetail: false
      }
    }

    // PictureUrl = 'https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg'

    // srcList = ['https://fuss10.elemecdn.com/8/27/f01c15bb73e1ef3793e64e6b7bbccjpeg.jpeg']
    test: Array<string> = []

    tableData = new Array<SingleDatumModel>()

    // 延时刷新表单时间
    refreshTime = 1500
    // 查询所属行业
    queryIndustry = QueryIndustry

    // 是否人社行业
    rsIndustry = false
    // 是否建设行业
    jsIndustry = false
    // 是否卫生行业
    wsIndustry = false
    // 是否工勤行业
    gqIndustry = false
    // 是否教师行业
    teacherIndustry = false
    // 是否药师行业
    ysIndustry = false
    // 单位配置
    unitConfig = new UnitConfig()

    // 字典口获取人设行业id
    rsIndustryId: string = null
    // 字典口获取建设行业id
    jsIndustryId: string = null
    // 字典口获取职业卫生行业id
    wsIndustryId: string = null
    // 字典口获取工勤行业id
    gqIndustryId: string = null
    // 字典口获取工勤行业id
    teacherIndustryId: string = null
    // 字典口获取药师行业id
    ysIndustryId: string = null
    teacherParams: Array<number> = []
    ysParams: Array<string> = []

    // 删除弹窗
    deleteDialog = false

    // 弹出监管基准照抽屉
    referencePhotoDialog = false
    /**
     * 证件类型数组
     */
    idCardTypeList = new Array<IdCardTypeVo>()

    // 学员id 由主文件ref传入
    userId = ''

    /**
     * 获取证件类型
     */
    get idTypeName() {
      return (code: number) => {
        const item = this.idCardTypeList.find((item) => {
          return item.code == Number(code)
        })
        return item ? item.name : ''
      }
    }

    saveNewValue(value: string) {
      this.trainingObject = value
    }
    async saveEditVal() {
      // 单位地区code转换

      // 人社行业专业类别

      if (this.rsProfessionalCategory.length == 1) {
        this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.firstProfessionalCategory =
          this.rsProfessionalCategory[0]
        this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.industryId = this.industryId
        console.log(
          QueryTrainingCategory.trainingCategoryList,
          this.rsProfessionalCategory[0],
          'QueryTrainingCategory.trainingCategoryList'
        )
        const firstProfessionalCategoryItem = cloneDeep(
          await QueryPersonIndustry.getOperationTraining(this.industryId)
        ).filter((item) => item.propertyId == this.rsProfessionalCategory[0])[0]
        console.log(firstProfessionalCategoryItem, '===============asdasdasdasdasd')
        const firstProfessionalCategoryName = firstProfessionalCategoryItem.name
        this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.firstProfessionalCategoryName =
          firstProfessionalCategoryName
        this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.secondProfessionalCategory = ''
      }
      if (this.rsProfessionalCategory.length == 2) {
        this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.firstProfessionalCategory =
          this.rsProfessionalCategory[0]
        this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.secondProfessionalCategory =
          this.rsProfessionalCategory[1]
        this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.industryId = this.industryId
        // const res = await QueryTrainingCategory.queryTrainingCategory(this.rsIndustryPropertyId, this.industryId)
        // if (res.isSuccess) {
        const firstProfessionalCategoryItem = cloneDeep(
          await QueryPersonIndustry.getOperationTraining(this.industryId)
        ).filter((item) => item.propertyId == this.rsProfessionalCategory[0])[0]
        const firstProfessionalCategoryName = firstProfessionalCategoryItem.name
        this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.firstProfessionalCategoryName =
          firstProfessionalCategoryName
        const firstProfessionalCategoryId = firstProfessionalCategoryItem.propertyId
        const params = new MajorParam()
        params.industryPropertyId = this.rsIndustryPropertyId
        params.parentPropertyId = firstProfessionalCategoryId
        // const res = await QueryTrainingMajor.queryTrainingMajor(params)
        // if (res.isSuccess()) {
        // console.log(
        //   await QueryPersonIndustry.getIndustryDetail(firstProfessionalCategoryId),
        //   'await QueryPersonIndustry.getIndustryDetail(this.industryId)'
        // )
        const secondProfessionalCategoryName = cloneDeep(
          await QueryPersonIndustry.getIndustryDetail(firstProfessionalCategoryId)
        ).filter((item) => item.propertyId == this.rsProfessionalCategory[1])[0].name

        this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo.secondProfessionalCategoryName =
          secondProfessionalCategoryName
        // }
      }
      // 卫生行业
      if (
        this.wsIndustryId &&
        (this.studentUpdateObj.updateStudentParams.wsUserIndustryInfo.personnelCategory ||
          this.studentUpdateObj.updateStudentParams.wsUserIndustryInfo.positionCategory)
      ) {
        this.studentUpdateObj.updateStudentParams.wsUserIndustryInfo.industryId = this.wsIndustryId
        if (this.trainingObject !== this.studentParams.wsStudentIndustryInfo.personnelCategory) {
          this.studentUpdateObj.updateStudentParams.wsUserIndustryInfo.positionCategory = ''
        }
      }
      // 工勤行业
      if (
        this.gqIndustryId &&
        (this.studentUpdateObj.updateStudentParams.gqUserIndustryInfo.professionalLevel ||
          this.studentUpdateObj.updateStudentParams.gqUserIndustryInfo.jobCategoryId)
      ) {
        this.studentUpdateObj.updateStudentParams.gqUserIndustryInfo.industryId = this.gqIndustryId
      }
      // 教师行业
      if (this.teacherIndustryId && this.teacherParams.length > 0) {
        this.studentUpdateObj.updateStudentParams.lsUserIndustryInfo.industryId = this.teacherIndustryId
      }

      const { certificatesType, practitionerCategory } = this.studentUpdateObj.updateStudentParams.ysUserIndustryInfo
      // 药师行业
      if (this.ysIndustryId && (certificatesType || practitionerCategory)) {
        this.studentUpdateObj.updateStudentParams.ysUserIndustryInfo.industryId = this.ysIndustryId
        if (
          this.studentUpdateObj.updateStudentParams.ysUserIndustryInfo.certificatesType !==
          this.studentParams.ysStudentIndustryInfo.certificatesType
        ) {
          this.studentUpdateObj.updateStudentParams.ysUserIndustryInfo.practitionerCategory = ''
        }
        // this.studentUpdateObj.updateStudentParams.ysUserIndustryInfo.practitionerCategory = this.ysParams[1]
      }

      const res = await this.studentUpdateObj.doUpdateStudentInfo()
      if (!res?.status.isSuccess()) {
        return this.$message.error('修改请求失败！')
      } else {
        if (res?.data?.code == 200) {
          this.$message.success('修改请求成功！')
        }
        if (res?.data?.code == 100001) {
          this.$message.warning('手机号码重复！')
        }
        if (res?.data?.code == 100002) {
          this.$message.warning('身份证号码重复！')
        }
      }
      this.rsProfessionalCategory = new Array<string>()
      this.delayToRefreshTable(() => {
        this.queryStuDetailById()
      })
    }

    // 监听修改信息数据源，同步至更新数据参数
    @Watch('studentParams', { deep: true })
    updateUpdateParams(newVal: UserDetailVo) {
      this.rsIndustry = false
      this.jsIndustry = false
      this.wsIndustry = false
      this.gqIndustry = false
      this.teacherIndustry = false
      this.ysIndustry = false
      this.studentUpdateObj.updateStudentParams.userId = newVal?.userId || undefined
      this.studentUpdateObj.updateStudentParams.idCard = newVal?.idCard || undefined
      this.studentUpdateObj.updateStudentParams.gender = newVal?.gender || 0
      this.studentUpdateObj.updateStudentParams.name = newVal?.userName || undefined
      this.studentUpdateObj.updateStudentParams.phone = newVal?.phone || undefined
      this.studentUpdateObj.updateStudentParams.idCardType = newVal?.idCardType || undefined
      this.studentUpdateObj.updateStudentParams.companyName = newVal?.companyName || undefined
      this.studentUpdateObj.updateStudentParams.companyCode = newVal?.companyCode || undefined
      this.studentUpdateObj.updateStudentParams.companyRegionCode = newVal?.companyRegion?.regionId || undefined
      this.trainingObject = this.studentParams.wsStudentIndustryInfo.personnelCategory
      // 行业信息 -- 人社
      const rsUserIndustryInfo = new UserIndustryRsInfo()
      rsUserIndustryInfo.industryId = newVal.rsStudentIndustryInfo?.industryId || undefined
      rsUserIndustryInfo.firstProfessionalCategory =
        newVal?.rsStudentIndustryInfo?.firstProfessionalCategory || undefined
      rsUserIndustryInfo.secondProfessionalCategory =
        newVal?.rsStudentIndustryInfo?.secondProfessionalCategory || undefined
      rsUserIndustryInfo.firstProfessionalCategoryName =
        newVal?.rsStudentIndustryInfo?.firstProfessionalCategoryName || undefined
      rsUserIndustryInfo.secondProfessionalCategoryName =
        newVal?.rsStudentIndustryInfo?.secondProfessionalCategoryName || undefined
      rsUserIndustryInfo.professionalQualification =
        newVal?.rsStudentIndustryInfo?.professionalQualification || undefined
      // extends
      // 证书信息
      const rsCertificateInfos = new Array<CertificateInfo>()
      newVal?.rsStudentIndustryInfo?.userCertificateList?.map((item: StudentCertificateResponse) => {
        const certificateInfo = new CertificateInfo()
        certificateInfo.certificateId = item?.certificateId || undefined
        certificateInfo.certificateNo = item?.certificateNo || undefined
        certificateInfo.certificateCategory = item?.certificateCategory || undefined
        certificateInfo.registerProfessional = item?.registerProfessional || undefined
        certificateInfo.releaseStartTime = item?.releaseStartTime || undefined
        certificateInfo.certificateEndTime = item?.certificateEndTime || undefined
        // 证书附件
        const certificateAttachments = new Array<CertificateAttachment>()
        item?.attachmentList?.map((item: AttachmentInfoResponse) => {
          const certificateAttachment = new CertificateAttachment()
          certificateAttachment.certificateUrl = item?.url || undefined
          certificateAttachment.name = item?.name || undefined
          certificateAttachments.push(certificateAttachment)
        })
        certificateInfo.certificateAttachments = certificateAttachments
        rsCertificateInfos.push(certificateInfo)
      })
      rsUserIndustryInfo.certificateInfos = rsCertificateInfos
      this.studentUpdateObj.updateStudentParams.rsUserIndustryInfo = rsUserIndustryInfo
      // 行业信息 -- 建设
      const jsUserIndustryInfo = new UserIndustryJsInfo()
      jsUserIndustryInfo.industryId = newVal?.jsStudentIndustryInfo?.industryId || undefined
      jsUserIndustryInfo.firstProfessionalCategory =
        newVal?.jsStudentIndustryInfo?.firstProfessionalCategory || undefined
      jsUserIndustryInfo.secondProfessionalCategory =
        newVal?.jsStudentIndustryInfo?.secondProfessionalCategory || undefined
      jsUserIndustryInfo.professionalQualification =
        newVal?.jsStudentIndustryInfo?.professionalQualification || undefined
      // 证书信息
      const jsCertificateInfos = new Array<CertificateInfoVo>()
      newVal?.jsStudentIndustryInfo?.userCertificateList?.map((item: StudentCertificateInfoVo) => {
        const certificateInfo = new CertificateInfoVo()
        certificateInfo.certificateId = item?.certificateId || undefined
        certificateInfo.certificateNo = item?.certificateNo || undefined
        certificateInfo.certificateCategory = item?.certificateCategory || undefined
        certificateInfo.registerProfessional = item?.registerProfessional || undefined
        certificateInfo.releaseStartTime = item?.releaseStartTime || undefined
        certificateInfo.certificateEndTime = item?.certificateEndTime || undefined
        certificateInfo.certificateCategoryName = item?.certificateCategoryName || undefined
        certificateInfo.registerProfessionalName = item?.registerProfessionalName || undefined
        // 证书附件
        const certificateAttachments = Array<CertificateAttachment>()
        item?.attachmentInfoList?.map((item: AttachmentInfoResponse) => {
          const certificateAttachment = new CertificateAttachment()
          certificateAttachment.certificateUrl = item?.url || undefined
          certificateAttachment.name = item?.name || undefined
          certificateAttachments.push(certificateAttachment)
        })
        certificateInfo.certificateAttachments = certificateAttachments
        jsCertificateInfos.push(certificateInfo)
      })
      jsUserIndustryInfo.certificateInfos = jsCertificateInfos
      this.studentUpdateObj.updateStudentParams.jsUserIndustryInfo = jsUserIndustryInfo
      // 判断学员行业信息
      this.getStudentIndustryStatus()
    }

    async historyPhotoList(userId: string) {
      this.userDatum = new UserDatum(userId)
      const res = await this.userDatum.queryDetail()

      const uiImage = this.userDatum.userDatum.currentDatum.photoPaths[0] || ''
      await FileModule.applyResourceAccessToken()

      //   this.uiImage = uiImage + `?token=${FileModule.resourceAccessToken}`
      if (uiImage) {
        this.uiImage = window.location.origin + '/mfs' + uiImage + `?token=${FileModule.resourceAccessToken}`
      } else {
        this.uiImage = ''
      }

      //   const res = await this.userDatum.queryDetail()
      //   this.tableData = res.historyDatums
      //   const res = await this.userDatum.queryDetail()
      this.tableData = res.historyDatums
    }

    async getStudentIndustryStatus() {
      const sId = this.studentParams?.userId
      if (!sId) return //学员信息不存在
      const res = await this.queryIndustry.queryIndustry()
      if (res.isSuccess()) {
        const industryList = cloneDeep(this.queryIndustry.industryList)

        if (industryList.length) {
          const rsIndustryId = industryList.find((industry) => industry.name.indexOf('人') != -1)?.id
          const jsIndustryId = industryList.find((industry) => industry.name.indexOf('建设') != -1)?.id
          const wsIndustryId = industryList.find((industry) => industry.name.indexOf('卫生') != -1)?.id
          const gqIndustryId = industryList.find((industry) => industry.name.indexOf('工勤') != -1)?.id
          const teacherIndustry = industryList.find((industry) => industry.name.indexOf('教师') != -1)?.id
          const ysIndustryId = industryList.find((industry) => industry.name.indexOf('药师') != -1)?.id

          this.rsIndustry = rsIndustryId ? true : false
          this.jsIndustry = jsIndustryId ? true : false
          this.wsIndustry = wsIndustryId ? true : false
          this.gqIndustry = gqIndustryId ? true : false
          this.teacherIndustry = teacherIndustry ? true : false
          this.ysIndustry = ysIndustryId ? true : false

          // 保存字典口获取的行业id
          this.jsIndustryId = jsIndustryId ? jsIndustryId : null
          this.rsIndustryId = rsIndustryId ? rsIndustryId : null
        }
      }
    }

    // 接收新增证书的参数
    async handleAddCertifition(val: DrawerForm) {
      if (!val) return this.$message.error('证书新增失败！')
      this.toDtoCertification(val, 'add')
    }

    // 更新证书
    async handleUpdateCertifition(val: DrawerForm) {
      if (!val) return this.$message.error('证书更新失败！')
      this.toDtoCertification(val, 'edit')
    }
    /**
     * 解绑微信弹窗
     */
    wxUnBindDialogShow() {
      // this.studentParams.userName
      this.uiStatus.isShow.wxUnBindDialog = true
    }
    // // 根据用户id查详情
    // @Watch('userId')
    async userIdChange(id: string) {
      if (id) {
        this.teacherParams = []
        await this.queryStuDetailById()
        await this.queryStudentFieldVerifyToken()
        await this.historyPhotoList(id)
      } else {
        this.studentParams = new UserDetailVo()
      }
    }

    //   @Watch('userId')
    //   userIdStatus(newVal: string) {
    //     this.editable = newVal ? true : false
    //   }

    // 查询学员详情
    async queryStuDetailById() {
      try {
        this.uiStatus.query.loadBaseInfoDetail = true
        this.editable = false
        const res = await this.studentDetailObj(this.userId).queryDetail()
        this.editable = true
        // 基础信息赋值
        this.fromBasicData(res?.data)
      } catch (e) {
        this.$message.error('获取学员详情失败！')
        console.log(e)
      } finally {
        this.uiStatus.query.loadBaseInfoDetail = false
      }
    }

    // 基础信息赋值
    async fromBasicData(item: UserDetailVo) {
      for (const key in item) {
        this.studentParams[key] = item[key]
      }
      if (this.studentParams.lsStudentIndustryInfo.sectionAndSubjectsName.length) {
        this.studentUpdateObj.updateStudentParams.lsUserIndustryInfo.grade =
          this.studentParams.lsStudentIndustryInfo.sectionAndSubjects[0].section
        this.studentUpdateObj.updateStudentParams.lsUserIndustryInfo.subject =
          this.studentParams.lsStudentIndustryInfo.sectionAndSubjects[0].subjects
        this.teacherParams = this.studentUpdateObj.updateStudentParams.lsUserIndustryInfo.subject
          ? [
              this.studentUpdateObj.updateStudentParams.lsUserIndustryInfo.grade,
              this.studentUpdateObj.updateStudentParams.lsUserIndustryInfo.subject
            ]
          : [this.studentUpdateObj.updateStudentParams.lsUserIndustryInfo.grade]
      }
      this.studentUpdateObj.updateStudentParams.wsUserIndustryInfo.personnelCategory =
        this.studentParams?.wsStudentIndustryInfo?.personnelCategory
      this.studentUpdateObj.updateStudentParams.wsUserIndustryInfo.positionCategory =
        this.studentParams?.wsStudentIndustryInfo?.positionCategory

      this.studentUpdateObj.updateStudentParams.ysUserIndustryInfo.certificatesType =
        this.studentParams?.ysStudentIndustryInfo?.certificatesType
      this.studentUpdateObj.updateStudentParams.ysUserIndustryInfo.practitionerCategory =
        this.studentParams?.ysStudentIndustryInfo?.practitionerCategory
      this.idCardTypeList = await QueryIdCardType.queryIdCardType()
    }
    // 转换单位地区
    transfromRegion(region: RegionModel) {
      const temp = new Array<string>()
      if (region?.provinceName) {
        temp.push(region.provinceName)
      }
      if (region?.cityName) {
        temp.push(region.cityName)
      }
      if (region?.countyName) {
        temp.push(region.countyName)
      }
      return temp.join('-') || '-'
    }
    /*
      新增证书数据；转换为后端入参
      certificateId 存在值则为修改操作
    */
    async toDtoCertification(val: DrawerForm, option: string) {
      if (option == 'add') {
        // todo
        const param = new CreateStudentCertificateRequest()
        param.userId = this.userId
        // 如果传入的建设行业id为空，则获取字典口查出的建设行业id
        param.industryId = this.studentParams.jsStudentIndustryInfo?.industryId || this.jsIndustryId
        param.certificateNo = val.certificationNo
        param.certificateCategory = val.certificationType
        param.registerProfessional = val.specialty || undefined
        param.releaseStartTime = val.certificationStartTime
        param.certificateEndTime = val.certificationEndTime
        param.certificateAttachments = val.certificationList?.map((item: CertificationUrl) => {
          return {
            certificateUrl: item.url,
            name: item.name
          }
        })
        param.certificateAttachments && param.certificateAttachments.length
          ? ''
          : (param.certificateAttachments = new Array<CertificationUrl>())

        try {
          const res = await this.mutationUpdateStudentInfo.addStudentCertificateInfo(param)
          if (res.status.isSuccess()) {
            this.$message.success('证书创建成功！')
            this.delayToRefreshTable(() => {
              this.queryStuDetailById()
            })
          } else {
            this.$message.error('证书创建失败！')
            throw new Error('证书创建失败')
          }
        } catch (error) {
          console.log(error)
        }
      }
      if (option == 'edit') {
        // todo
        const params = new UpdateStudentCertificateRequest()
        params.certificateId = val.certificateId
        params.userId = this.userId
        // 如果传入的建设行业id为空，则获取字典口查出的建设行业id
        params.industryId = this.studentParams.jsStudentIndustryInfo.industryId || this.jsIndustryId
        params.certificateNo = val.certificationNo
        params.certificateCategory = val.certificationType
        params.registerProfessional = val.specialty || undefined
        params.releaseStartTime = val.certificationStartTime
        params.certificateEndTime = val.certificationEndTime
        params.certificateAttachments = val.certificationList?.map((item: CertificationUrl) => {
          return {
            certificateUrl: item.url,
            name: item.name
          }
        })
        params.certificateAttachments && params.certificateAttachments.length
          ? ''
          : (params.certificateAttachments = new Array<CertificationUrl>())

        try {
          const res = await this.mutationUpdateStudentInfo.doUpdateStudentCertificateInfo(params)
          if (res.status.isSuccess()) {
            this.$message.success('证书修改成功！')
            this.delayToRefreshTable(() => {
              this.queryStuDetailById()
            })
          } else {
            this.$message.error('证书修改失败！')
            throw new Error('证书修改失败')
          }
        } catch (error) {
          console.log(error)
        }
      }
      //   if (val?.certificateId) {
      //     // 修改证书
      //     // this.studentParams.jsUserIndustryInfo.certificateInfos?.forEach(sub => {
      //     //   if (val.certificateId === sub.certificateId) {
      //     //     sub.certificateId = val.certificateId
      //     //     sub.certificateNo = val.certificationNo
      //     //     sub.certificateCategory = val.certificationType
      //     //     sub.registerProfessional = val.specialty || undefined
      //     //     sub.releaseStartTime = val.certificationStartTime
      //     //     sub.certificateEndTime = val.certificationEndTime
      //     //     sub.certificateCategoryName = undefined
      //     //     sub.registerProfessionalName = undefined
      //     //     sub.certificateAttachments = val.certificationList?.map((item: CertificationUrl) => {
      //     //       return {
      //     //         certificateUrl: item.url,
      //     //         name: item.name
      //     //       }
      //     //     })
      //     //   }
      //     // })
      //   } else {
      //     // 新增证书
      //     const temp = new CertificateInfoVo()
      //     temp.certificateId = val.certificateId
      //     temp.certificateNo = val.certificationNo
      //     temp.certificateCategory = val.certificationType
      //     temp.registerProfessional = val.specialty || undefined
      //     temp.releaseStartTime = val.certificationStartTime
      //     temp.certificateEndTime = val.certificationEndTime
      //     temp.certificateAttachments = val.certificationList?.map((item: CertificationUrl) => {
      //       return {
      //         certificateUrl: item.url,
      //         name: item.name
      //       }
      //     })
      //     // this.studentParams.jsUserIndustryInfo.certificateInfos.push(temp)
      //   }
    }

    // 确认解绑微信
    async confirmUnbindwx() {
      const sId = this.studentParams?.userId
      if (!sId) {
        this.$message.error('学员id不能为空！')
        return
      }
      try {
        const res = await this.mutationUserFactory.studentBusinessAction(sId).doUnBindStudentWx()
        if (res.code == 200) {
          this.$message.success('解绑成功！')
        }
        await this.queryStuDetailById()
        this.uiStatus.isShow.wxUnBindDialog = false
      } catch (e) {
        console.log(e)
        this.$message.error('解绑微信请求失败！')
      }
    }

    // 确认重置密码
    async confirmResetPwd() {
      try {
        const res = await this.resetPwdObj.doResetStudentPwd(this.studentParams.accountId, 'abc123')
        if (res.status.code == 200) {
          this.$message.success('重置成功！')
        }
        await this.queryStuDetailById()
        this.uiStatus.isShow.resetPwdDialog = false
      } catch (e) {
        this.$message.error('重置密码请求失败！')
        console.error(e)
      }
    }

    // 删除证书
    deleteCertification(item: CertificateInfoVo) {
      const params = new DeleteStudentCertificateRequest()
      params.certificateId = item.certificateId
      params.userId = this.userId
      // 如果传入的建设行业id为空，则获取字典口查出的建设行业id
      params.industryId = this.studentParams.jsStudentIndustryInfo?.industryId || this.jsIndustryId
      this.$confirm('确定要删除？', '提示', {
        confirmButtonText: '确认',
        cancelButtonText: '取消'
      }).then(async () => {
        // this.studentParams?.jsUserIndustryInfo?.certificateInfos?.splice(idx, 1)
        const res = await this.mutationUpdateStudentInfo.deleteCertificateInfoById(params)
        if (!res?.status.isSuccess()) {
          return this.$message.error('删除请求失败！')
        }
        this.delayToRefreshTable(() => {
          this.queryStuDetailById()
        })
        this.$message.success('删除成功！')
      })
    }

    // 打开新增证书抽屉
    openCertificationDrawer() {
      this.AddCertificationDrawerRef.drawerTitle = '新增证书'
      this.AddCertificationDrawerRef.transformData(new StudentCertificateInfoVo()) //新增清除表单数据
      this.AddCertificationDrawerRef.uiStatus.isOpenCertificationDrawer = true
    }
    // 修改证书信息
    modifyCertification(item: StudentCertificateInfoVo) {
      this.AddCertificationDrawerRef.drawerTitle = '修改证书'
      this.AddCertificationDrawerRef.transformData(item)
      this.AddCertificationDrawerRef.uiStatus.isOpenCertificationDrawer = true
    }

    //延时刷新表单
    delayToRefreshTable(f: Function) {
      setTimeout(() => {
        f()
      }, this.refreshTime)
    }

    // 调整基准照片修改次数
    referencePhotoTime() {
      //调整基准照片修改次数弹框展示
      this.referencePhotoDialog = true
    }

    // 删除基准照
    RemoveReferencePhoto() {
      this.deleteDialog = true
    }

    async doRemove() {
      // 加载loading
      this.loading = true
      try {
        const res = await this.userDatum.deleteCurrentDatumByAdmin()
        if (res.code == 200) {
          this.$message.success('删除成功')

          this.delayToRefreshTable(() => {
            // this.userDatum.queryDetail()
            this.historyPhotoList(this.userId)
          })
        } else {
          return this.$message.error('删除失败')
        }
      } catch (error) {
        // this.$message.error()
      } finally {
        setTimeout(() => {
          this.loading = false
          this.deleteDialog = false
        }, 2000)
      }
    }

    // 读取当前基准照片
    get faceReferencePhoto() {
      //判断当前基准照照片，如果没有基准照照片就读取美工提供的图片
      if (
        this.userDatum.userDatum.currentDatum.photoPaths.length &&
        this.userDatum.userDatum.currentDatum.photoPaths[0]
      ) {
        return (
          window.location.origin +
          '/mfs' +
          this.userDatum.userDatum.currentDatum.photoPaths[0] +
          `?token=${FileModule.resourceAccessToken}`
        )
      }
      return ''
    }

    // 历史基准照照片
    get historyAdjustPhoto() {
      return (url: string) => {
        return window.location.origin + '/mfs' + url + `?token=${FileModule.resourceAccessToken}`
      }
    }

    // 判断是否有历史调整记录
    get historyReferencePhoto() {
      // 判断状态层返回的历史基准照数组的长度
      if (this.userDatum.userDatum.historyDatums.length) {
        return true
      }
      return false
    }

    /**
     * 修改基本信息弹窗开启
     */
    editBaseInfoOpen() {
      if (!this.studentParams.loginAccount) {
        return
      }
      this.queryStuDetailById()
      this.EditBaseInfoDialogRef.openDialog()
    }
    /**
     * 更新用户信息
     */
    updateUserInfo() {
      this.rsProfessionalCategory = new Array<string>()
      this.delayToRefreshTable(() => {
        this.queryStuDetailById()
      })
      //
    }
    /**
     * 页面激活时触发清除上次选中学段学科
     */
    async activated() {
      this.teacherParams = []
    }

    /**
     * 请求单位配置位置优化
     * 只在页面初始化发起请求
     */
    async created() {
      await QueryPortal.getUnitConfig()
      this.unitConfig = QueryPortal.unitConfig
    }
  }
</script>
