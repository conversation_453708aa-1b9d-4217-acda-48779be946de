import QueryDeliveryInvoiceBase from '@api/service/management/trade/batch/invoice/query/vo/QueryDeliveryInvoiceBase'
import { Page, UiPage } from '@hbfe/common'
import DeliveryInvoiceParamVo from '@api/service/management/trade/batch/invoice/query/vo/DeliveryInvoiceParam'
import OffLinePageInvoiceVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'
import QueryOffLinePageInvoiceParam from '@api/service/management/trade/batch/invoice/query/vo/QueryOffLinePageInvoiceParam'
import QueryRegion from '@api/service/common/basic-data-dictionary/query/QueryRegion'
import UserModule from '@api/service/management/user/UserModule'
import ExportGateWay from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import QueryOffLineInvoiceInTrainingChannel from '@api/service/management/trade/batch/invoice/query/QueryOffLineInvoiceInTrainingChannel'

export default class QueryDeliveryInvoiceInTrainingChannel extends QueryDeliveryInvoiceBase {
  /**
   * 分页查询发票配送
   * @param page 页数
   * @returns  Array<OffLinePageInvoiceVo>
   */
  async queryPageDeliveryInvoice(
    page: Page,
    deliveryInvoiceParamVo?: DeliveryInvoiceParamVo
  ): Promise<Array<OffLinePageInvoiceVo>> {
    const offlineInvoiceRequest = new QueryOffLinePageInvoiceParam()
    offlineInvoiceRequest.takePerson = deliveryInvoiceParamVo.recipient
    offlineInvoiceRequest.idCard = deliveryInvoiceParamVo.idCard
    offlineInvoiceRequest.deliveryStatusList = deliveryInvoiceParamVo.deliveryStatus
    offlineInvoiceRequest.orderNoList = deliveryInvoiceParamVo.invoiceNo
    offlineInvoiceRequest.deliveryStartTime = deliveryInvoiceParamVo.startDate
    offlineInvoiceRequest.deliveryEndTime = deliveryInvoiceParamVo.endDate
    offlineInvoiceRequest.shippingMethodList = deliveryInvoiceParamVo.deliveryWay
    offlineInvoiceRequest.expressNo = deliveryInvoiceParamVo.theAwb
    offlineInvoiceRequest.consignee = deliveryInvoiceParamVo.name
    offlineInvoiceRequest.invoiceFreezeStatus = deliveryInvoiceParamVo.frozenState
    offlineInvoiceRequest.invoiceStatusList = 2
    offlineInvoiceRequest.userId = deliveryInvoiceParamVo.createUserId
    const queryOffLineInvoice = new QueryOffLineInvoiceInTrainingChannel()

    const listData = await queryOffLineInvoice.offLinePageVatspecialplaInvoiceInServicer(page, offlineInvoiceRequest)
    if (listData.length === 0) return listData
    const regionS = listData
      .map((item) => {
        if (item.deliveryInfo.deliveryAddress) {
          return item.deliveryInfo.deliveryAddress?.region
        }
      })
      .filter((item) => item)

    if (regionS?.length) {
      const map = await QueryRegion.querRegionDetilAll(regionS)
      listData.forEach((item) => {
        if (item.deliveryInfo?.deliveryAddress && item.deliveryInfo.deliveryAddress?.region) {
          if (item.deliveryInfo.deliveryAddress.region[0] === '/') {
            item.deliveryInfo.deliveryAddress.region = map.get(item.deliveryInfo.deliveryAddress.region)
              ? map.get(item.deliveryInfo.deliveryAddress.region)
              : item.deliveryInfo.deliveryAddress.region
          } else {
            item.deliveryInfo.deliveryAddress.region = map.get('/' + item.deliveryInfo.deliveryAddress.region)
              ? map.get('/' + item.deliveryInfo.deliveryAddress.region)
              : item.deliveryInfo.deliveryAddress.region
          }
        }
      })
    }
    return listData
  }
  /**
   * 导出发票配送
   */
  async exportPageDeliveryInvoice(deliveryInvoiceParamVo: DeliveryInvoiceParamVo): Promise<boolean> {
    const offlineInvoiceRequest = new QueryOffLinePageInvoiceParam()
    offlineInvoiceRequest.takePerson = deliveryInvoiceParamVo.name
    offlineInvoiceRequest.idCard = deliveryInvoiceParamVo.idCard
    offlineInvoiceRequest.deliveryStatusList = deliveryInvoiceParamVo.deliveryStatus
    offlineInvoiceRequest.orderNoList = deliveryInvoiceParamVo.invoiceNo
    offlineInvoiceRequest.deliveryStartTime = deliveryInvoiceParamVo.startDate
    offlineInvoiceRequest.deliveryEndTime = deliveryInvoiceParamVo.endDate
    offlineInvoiceRequest.shippingMethodList = deliveryInvoiceParamVo.deliveryWay
    offlineInvoiceRequest.expressNo = deliveryInvoiceParamVo.theAwb
    offlineInvoiceRequest.takePerson = deliveryInvoiceParamVo.recipient
    offlineInvoiceRequest.invoiceFreezeStatus = deliveryInvoiceParamVo.frozenState

    const data = await this.offLinePageVatspecialplaInvoiceInExport(offlineInvoiceRequest)
    return data
  }

  /**
   * 集体线下发票导出 - 专票
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  async offLinePageVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 2
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const idList = await queryStudentIdList.queryPageCollectiveList(page, {
        name: queryOffLinePageInvoiceParam.userName ? queryOffLinePageInvoiceParam.userName : undefined,
        idCard: queryOffLinePageInvoiceParam.idCard ? queryOffLinePageInvoiceParam.idCard : undefined,
        phone: queryOffLinePageInvoiceParam.phone ? queryOffLinePageInvoiceParam.phone : undefined
      })
      if (idList.length === 0) {
        request.associationInfo.buyerIdList = ['-1']
      } else {
        request.associationInfo.buyerIdList = idList.map((item) => item.userInfo.userId)
      }
    }
    const result = await ExportGateWay.exportInvoiceDeliveryInTrainingChannel(request)
    return result.data
  }
}
