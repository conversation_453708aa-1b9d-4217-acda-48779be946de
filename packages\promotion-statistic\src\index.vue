<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <hb-search-wrapper class="m-query is-border-bottom" @reset="resetSearch">
        <el-form-item label="分销商品">
          <el-input
            v-model="supplierDistributorSalesStatisticsData.param.distributedTradeName"
            clearable
            placeholder="请输入分销商品名称"
          />
        </el-form-item>
        <el-form-item label="分销商">
          <el-input
            v-model="supplierDistributorSalesStatisticsData.param.distributorName"
            clearable
            placeholder="请输入分销商名称"
          />
        </el-form-item>
        <el-form-item label="推广门户简称">
          <el-input
            v-model="supplierDistributorSalesStatisticsData.param.portalPromoteTheName"
            clearable
            placeholder="请输入分销商推广门户简称"
          />
        </el-form-item>
        <el-form-item label="报名时间">
          <el-date-picker
            v-model="supplierDistributorSalesStatisticsData.param.registrationPeriod"
            type="datetimerange"
            range-separator="~"
            start-placeholder="报名成功时间"
            end-placeholder="报名成功时间"
          >
          </el-date-picker>
        </el-form-item>
        <template slot="actions">
          <el-button type="primary" @click="exportData">导出</el-button>
          <el-button type="primary" @click="search">查询</el-button>
        </template>
      </hb-search-wrapper>
      <!--操作栏-->
      <div class="f-mt20">
        <el-alert type="warning" :closable="false" class="m-alert f-clear">
          <div class="f-c6 f-fl">
            搜索结果合计：当前分销推广净开通
            <span class="f-fb f-co">{{ supplierDistributorSalesStatisticsData.staticData.netTurnOn }}</span>
            人次，成交总额
            <span class="f-fb f-co">¥ {{ supplierDistributorSalesStatisticsData.staticData.transactionTotal }}</span>
          </div>
          <div class="f-fr f-csp f-flex f-align-center" @click="statisticalCaliberDeclarationDrawer.openDrawer()">
            <i class="el-icon-info f-f16 f-mr5"></i>统计说明
          </div>
        </el-alert>
      </div>
      <!--表格-->
      <el-table
        stripe
        :data="supplierDistributorSalesStatisticsData.list"
        show-summary
        max-height="600"
        class="m-table f-mt10"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="分销商" min-width="180" fixed="left">
          <template slot-scope="{ row }">{{ row.distributorName }}</template>
        </el-table-column>
        <el-table-column label="合计" header-align="center">
          <el-table-column label="开通" min-width="90" align="right">
            <template slot-scope="{ row }">{{ row.total.open }}</template>
          </el-table-column>
          <el-table-column label="退班" min-width="90" align="right">
            <template slot-scope="{ row }">{{ row.total.return }}</template>
          </el-table-column>
          <el-table-column label="净开通" min-width="90" align="right">
            <template slot-scope="{ row }">{{ row.total.count }}</template>
          </el-table-column>
          <el-table-column label="分销总额" min-width="100" align="right">
            <template slot-scope="{ row }">{{ row.total.distributionAmount }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="个人报名" header-align="center">
          <el-table-column label="线上支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.individualOnline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.individualOnline.return }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.individualOnline.count }}</template>
            </el-table-column>
            <el-table-column label="分销金额" min-width="100" align="right">
              <template slot-scope="{ row }">{{ row.individualOnline.distributionAmount }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="集体报名" header-align="center">
          <el-table-column label="线上支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOnline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOnline.return }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOnline.count }}</template>
            </el-table-column>
            <el-table-column label="分销金额" min-width="100" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOnline.distributionAmount }}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="线下支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOffline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOffline.return }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOffline.count }}</template>
            </el-table-column>
            <el-table-column label="分销金额" min-width="100" align="right">
              <template slot-scope="{ row }">{{ row.collectivelyOffline.distributionAmount }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="导入开通" header-align="center">
          <el-table-column label="线下支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.importOffline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.importOffline.return }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template slot-scope="{ row }">{{ row.importOffline.count }}</template>
            </el-table-column>
            <el-table-column label="分销金额" min-width="100" align="right">
              <template slot-scope="{ row }">{{ row.importOffline.distributionAmount }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
      <hb-pagination :page="page" v-bind="page" />
    </el-card>
    <statistical-caliber-declaration-drawer
      ref="statisticalCaliberDeclarationDrawer"
      :search-data="supplierDistributorSalesStatisticsSearchData"
      :field-data="supplierDistributorSalesStatisticsFieldData"
    />
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import StatisticalCaliberDeclarationDrawer from '@hbfe/jxjy-admin-components/src/statistical-caliber-declaration-drawer.vue'
  import {
    supplierDistributorSalesStatisticsSearchData,
    supplierDistributorSalesStatisticsFieldData
  } from '@hbfe/jxjy-admin-components/src/models/statisticalExplanatoryData'

  import SupplierDistributorSalesStatisticsData from '@api/service/management/statisticalReport/DistributorSalesStatistics/SupplierDistributorSalesStatistics'
  import SupplierDistributorSalesStatisticsParams from '@api/service/management/statisticalReport/DistributorSalesStatistics/model/SupplierDistributorSalesStatisticsParams'

  @Component({
    components: { StatisticalCaliberDeclarationDrawer }
  })
  export default class SupplierDistributorSalesStatistics extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.search, this.search)
    }
    // 统计口径说明抽屉ref
    @Ref('statisticalCaliberDeclarationDrawer') statisticalCaliberDeclarationDrawer: StatisticalCaliberDeclarationDrawer
    // 供应商分销商销售统计——搜索字段说明
    get supplierDistributorSalesStatisticsSearchData() {
      return supplierDistributorSalesStatisticsSearchData
    }
    // 供应商分销商销售统计——列表字段说明
    get supplierDistributorSalesStatisticsFieldData() {
      return supplierDistributorSalesStatisticsFieldData
    }
    page: UiPage // 分页
    // 供应商——分销商销售统计模型
    supplierDistributorSalesStatisticsData = new SupplierDistributorSalesStatisticsData()

    created() {
      this.init()
    }

    /**
     * 初始化
     */
    init() {
      this.page.currentChange(1)
    }

    /**
     * 搜索列表
     */
    async search() {
      this.supplierDistributorSalesStatisticsData.queryList(this.page)
      this.supplierDistributorSalesStatisticsData.queryTotalStatic()
    }

    /**
     * 导出数据
     */
    async exportData() {
      await this.supplierDistributorSalesStatisticsData.exportList()
    }

    /**
     * 重置
     */
    async resetSearch() {
      this.supplierDistributorSalesStatisticsData.param = new SupplierDistributorSalesStatisticsParams()
      await this.page.currentChange(1)
    }
  }
</script>
