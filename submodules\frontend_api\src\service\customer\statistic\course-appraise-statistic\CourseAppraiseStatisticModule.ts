import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import {
  SchemeCourseAppraiseStatistic,
  SchemeCourseAppraiseStatisticPage
} from '@api/service/common/models/statistic/course-appraise-statistic/SchemeCourseAppraiseStatisticPage'
import { ResponseStatus } from '@api/Response'
import {
  CourseAppraiseStatistic,
  CourseAppraiseStatisticPage
} from '@api/service/common/models/statistic/course-appraise-statistic/CourseAppraiseStatisticPage'
import PlatformStatisticReportQueryGateway from '@api/gateway/PlatformStatisticReportQuery'
import { UnAuthorize } from '@api/Secure'
import {
  UnitCourseAppraiseStatistic,
  UnitCourseAppraiseStatisticPage
} from '@api/service/common/models/statistic/course-appraise-statistic/UnitCourseAppraiseStatisticPage'
import lodash from 'lodash'

export interface ICourseAppraiseStatistic {
  // 方案维度课程评价统计
  schemeCourseAppraiseList: Array<SchemeCourseAppraiseStatistic>
  // 课程维度的综合评价
  courseAppraiseList: Array<CourseAppraiseStatistic>
  // 机构维度课程评价统计
  unitCourseAppraiseDetailList: Array<UnitCourseAppraiseStatistic>
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerCourseAppraiseStatisticModule', store })
class CourseAppraiseStatisticModule extends VuexModule implements ICourseAppraiseStatistic {
  schemeCourseAppraiseList: Array<SchemeCourseAppraiseStatistic> = new Array<SchemeCourseAppraiseStatistic>()
  courseAppraiseList: Array<CourseAppraiseStatistic> = new Array<CourseAppraiseStatistic>()
  unitCourseAppraiseDetailList: Array<UnitCourseAppraiseStatistic> = new Array<UnitCourseAppraiseStatistic>()

  /**
   * 加载某些方案的方案维度评价统计
   * @param schemeIds
   */
  @Action
  @UnAuthorize
  async loadSchemeCourseAppraiseStatistic(schemeIds: Array<string>) {
    schemeIds = schemeIds.filter(id => this.schemeCourseAppraiseList?.find(s => s.schemeId === id) === undefined)
    if (schemeIds.length === 0) {
      return new ResponseStatus(200, '')
    }
    const res = await PlatformStatisticReportQueryGateway.pageSchemeCourseAppraiseStatistic({
      page: {
        pageNo: 1,
        pageSize: schemeIds.length
      },
      request: {
        schemeIds: schemeIds
      }
    })
    if (res.status.isSuccess()) {
      this.PUSH_SCHEME_COURSE_APPRAISE_STATISTIC(SchemeCourseAppraiseStatisticPage.fromRemote(res.data.currentPageData))
    }
    return res.status
  }

  /**
   * 加载某些课程的课程维度评价统计
   * @param courseIds
   */
  @Action
  @UnAuthorize
  async loadCourseAppraiseStatistic(courseIds: Array<string>) {
    courseIds = courseIds.filter(id => this.courseAppraiseList?.find(s => s.courseId === id) === undefined)
    if (courseIds.length === 0) {
      return new ResponseStatus(200, '')
    }
    const res = await PlatformStatisticReportQueryGateway.pageCourseAppraiseStatistic({
      page: {
        pageNo: 1,
        pageSize: courseIds.length
      },
      request: {
        courseIds: courseIds
      }
    })
    if (res.status.isSuccess()) {
      this.PUSH_COURSE_APPRAISE_STATISTIC(
        CourseAppraiseStatisticPage.fromRemote(res.data.currentPageData, new Array<any>())
      )
    }
    return res.status
  }

  /**
   * 加载单个单位的单位维度评价
   * @param unitId
   */
  @Action
  @UnAuthorize
  async singleUnitCourseAppraiseStatistic(unitId: string) {
    // 加入加载判断
    if (this.unitCourseAppraiseDetailList.find(u => u.unitId === unitId)) {
      return new ResponseStatus(200, '')
    }
    const res = await PlatformStatisticReportQueryGateway.pageTrainingInstitutionCourseAppraiseStatistic({
      page: {
        pageNo: 1,
        pageSize: 1
      },
      request: {
        trainingInstitutionIdList: [unitId]
      }
    })
    if (!res.status.isSuccess()) {
      return res.status
    }
    const learningStatistic = await PlatformStatisticReportQueryGateway.pageTrainingInstitutionLearningStatistic({
      page: {
        pageNo: 1,
        pageSize: 1
      },
      request: {
        trainingInstitutionIdList: [unitId]
      }
    })
    if (!learningStatistic.status.isSuccess()) {
      return learningStatistic.status
    }
    this.PUSH_UNIT_COURSE_APPRAISE_STATISTIC_DETAIL(
      UnitCourseAppraiseStatisticPage.fromRemote(res.data.currentPageData, learningStatistic.data.currentPageData)
    )
    return res.status
  }

  /**
   * push方案维度评价统计
   * @param list
   * @constructor
   */
  @Mutation
  private PUSH_SCHEME_COURSE_APPRAISE_STATISTIC(list: Array<SchemeCourseAppraiseStatistic>) {
    this.schemeCourseAppraiseList.push(...list)
  }

  /**
   * push课程维度评价统计
   * @param list
   * @constructor
   */
  @Mutation
  private PUSH_COURSE_APPRAISE_STATISTIC(list: Array<CourseAppraiseStatistic>) {
    this.courseAppraiseList.push(...list)
  }

  /**
   * 加入机构维度详情状态
   * @param details
   */
  @Mutation
  private PUSH_UNIT_COURSE_APPRAISE_STATISTIC_DETAIL(details: Array<UnitCourseAppraiseStatistic>) {
    details.map(d => {
      const findIndex = this.unitCourseAppraiseDetailList.findIndex(c => c.unitId === d.unitId)
      if (findIndex === -1) {
        this.unitCourseAppraiseDetailList.push(d)
      } else {
        this.unitCourseAppraiseDetailList.splice(findIndex, 1)
        this.unitCourseAppraiseDetailList.push(d)
      }
    })
  }

  /**
   * 获取某个方案的评价数据
   * @param schemeId 方案id
   * @return SchemeCourseAppraiseStatistic
   */
  get getSchemeCourseAppraiseStatistic() {
    return (schemeId: string) => {
      return this.schemeCourseAppraiseList?.find(s => s.schemeId === schemeId)
    }
  }

  /**
   * 获取某个课程的评价数据
   * @param courseId 方案id
   * @return CourseAppraiseStatistic
   */
  get getCourseAppraiseStatistic() {
    return (courseId: string) => {
      return this.courseAppraiseList?.find(s => s.courseId === courseId)
    }
  }

  /**
   * 从详情据里获取具体某一个单位的评价数据
   */
  get getSingleUnitCourseAppraise() {
    return (unitId: string) => {
      return this.unitCourseAppraiseDetailList.find(u => u.unitId === unitId)
    }
  }
}

export default getModule(CourseAppraiseStatisticModule)
