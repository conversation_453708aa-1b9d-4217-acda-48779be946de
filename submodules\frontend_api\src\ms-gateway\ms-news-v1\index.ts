import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-news-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-news-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class ChildNewsCategoryListRequest {
  /**
   * 资讯分类编号
   */
  newsCategoryId?: string
  /**
   * 是否启用
   */
  enable: number
}

/**
 * 新建草稿资讯
 */
export class NewsCategoryCreateRequest {
  /**
   * 分类名称
   */
  name?: string
  /**
   * 分类代码
   */
  code?: string
  /**
   * 上级分类编号
   */
  parentId?: string
}

/**
 * 新建草稿资讯
 */
export class NewsCreateRequest {
  /**
   * 资讯分类编号
   */
  newCategoryId?: string
  /**
   * 资讯标题
   */
  title?: string
  /**
   * 摘要信息
   */
  summary?: string
  /**
   * 封面图片
   */
  coverPath?: string
  /**
   * 内容
   */
  content?: string
  /**
   * 来源
   */
  source?: string
  /**
   * 发布时间
   */
  publishTime?: string
  /**
   * 是否弹窗公告
   */
  popUps: boolean
  /**
   * 发布地区编码
   */
  areaCodePath?: string
  /**
   * 是否置顶
   */
  top: boolean
  /**
   * 弹窗起始时间
   */
  popupBeginTime?: string
  /**
   * 弹窗截止时间
   */
  popupEndTime?: string
  /**
   * 是否需要校验弹窗时间段重叠，不填默认是会校验
   */
  verifyPopUps?: boolean
}

/**
 * 资讯更新信息
 */
export class NewsUpdateRequest {
  /**
   * 资讯编号
   */
  newsId?: string
  /**
   * 资讯分类编号，null不更新
   */
  newsCategoryId?: string
  /**
   * 标题，null不更新
   */
  title?: string
  /**
   * 摘要，null不更新
   */
  summary?: string
  /**
   * 封面图片，null不更新
   */
  coverPath?: string
  /**
   * 内容，null不更新
   */
  content?: string
  /**
   * 来源，null不更新
   */
  source?: string
  /**
   * 发布地区编码，null不更新
   */
  areaCodePath?: string
  /**
   * 发布时间，null不更新
   */
  publishTime?: string
  /**
   * 是否弹窗公告，null不更新
   */
  popUps?: boolean
  /**
   * 是否置顶，null不更新
   */
  top?: boolean
  /**
   * 弹窗起始时间
   */
  popupBeginTime?: string
  /**
   * 弹窗截止时间
   */
  popupEndTime?: string
  /**
   * 是否需要校验弹窗时间段重叠，不填默认是会校验
   */
  verifyPopUps?: boolean
}

export class SimpleNewsPageListRequest {
  /**
   * 资讯分类编号
   */
  newsCategoryId?: string
  /**
   * 标题
   */
  title?: string
  /**
   * 是否弹窗公告
   */
  popUps?: boolean
  /**
   * 资讯状态
   */
  status: number
}

/**
 * @Author: Czy
@CreateTime: 2024-03-22  17:01
@Description: 专题资讯草稿创建请求
 */
export class SpecialSubjectNewsCreateRequest {
  /**
   * 资讯分类编号
   */
  newCategoryId?: string
  /**
   * 专题信息集合
   */
  specialSubjectInfoList?: Array<SpecialSubjectInfo>
  /**
   * 资讯标题
   */
  title?: string
  /**
   * 摘要信息
   */
  summary?: string
  /**
   * 封面图片
   */
  coverPath?: string
  /**
   * 内容
   */
  content?: string
  /**
   * 来源
   */
  source?: string
  /**
   * 发布时间
   */
  publishTime?: string
  /**
   * 是否弹窗公告
   */
  popUps: boolean
  /**
   * 发布地区编码
   */
  areaCodePath?: string
  /**
   * 是否置顶
   */
  top: boolean
  /**
   * 弹窗起始时间
   */
  popupBeginTime?: string
  /**
   * 弹窗截止时间
   */
  popupEndTime?: string
  /**
   * 是否需要校验弹窗时间段重叠，不填默认是会校验
   */
  verifyPopUps?: boolean
}

/**
 * @Author: Czy
@CreateTime: 2024-03-22  17:22
@Description: 专题资讯更新请求
 */
export class SpecialSubjectNewsUpdateRequest {
  /**
   * 资讯编号
   */
  newsId?: string
  /**
   * 专题id
   */
  specialSubjectInfo?: SpecialSubjectInfo
  /**
   * 资讯分类编号，null不更新
   */
  newsCategoryId?: string
  /**
   * 标题，null不更新
   */
  title?: string
  /**
   * 摘要，null不更新
   */
  summary?: string
  /**
   * 封面图片，null不更新
   */
  coverPath?: string
  /**
   * 内容，null不更新
   */
  content?: string
  /**
   * 来源，null不更新
   */
  source?: string
  /**
   * 发布地区编码，null不更新
   */
  areaCodePath?: string
  /**
   * 发布时间，null不更新
   */
  publishTime?: string
  /**
   * 是否弹窗公告，null不更新
   */
  popUps?: boolean
  /**
   * 是否置顶，null不更新
   */
  top?: boolean
  /**
   * 弹窗起始时间
   */
  popupBeginTime?: string
  /**
   * 弹窗截止时间
   */
  popupEndTime?: string
  /**
   * 是否需要校验弹窗时间段重叠，不填默认是会校验
   */
  verifyPopUps?: boolean
}

export class TopNewsCategoryListRequest {
  /**
   * 排除的资讯分类编号
   */
  newsCategoryId?: string
  /**
   * top数量
   */
  top: number
}

/**
 * @Author: Czy
@CreateTime: 2024-04-08  19:05
@Description: 专题信息
 */
export class SpecialSubjectInfo {
  /**
   * 专题id
   */
  specialSubjectId?: string
  /**
   * 专题名称
   */
  specialSubjectName?: string
}

export class News {
  id: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  servicerId: string
  necId: string
  title: string
  summary: string
  content: string
  coverPath: string
  source: string
  areaCodePath: string
  isPopUps: boolean
  isTop: boolean
  status: number
  publishUserId: string
  publishTime: string
  createTime: string
  updateTime: string
  reviewCount: number
  popupBeginTime: string
  popupEndTime: string
  specialSubjectId: string
}

/**
 * 咨询通用返回结果类
<AUTHOR> By lincong
@date 2023/09/11 12:15
 */
export class NewsCommonResult {
  /**
   * 操作状态码
   */
  code: string
  /**
   * 操作结果描述
   */
  message: string
  /**
   * 咨询id
   */
  newsId: string
}

export class NewsCategoryListResponse {
  /**
   * 资讯分类编号，null不更新
   */
  newsCategoryId: string
  /**
   * 分类名称
   */
  name: string
}

export class NewsDetailWithPreviousAndNextResponse {
  /**
   * 资讯编号
   */
  newsId: string
  /**
   * 平台编号
   */
  platformId: string
  /**
   * 平台版本编号
   */
  platformVersionId: string
  /**
   * 项目编号
   */
  projectId: string
  /**
   * 子项目编号
   */
  subProjectId: string
  /**
   * 单位编号
   */
  unitId: string
  /**
   * 服务商编号
   */
  servicerId: string
  /**
   * 资讯分类编号
   */
  newCategoryId: string
  /**
   * 资讯标题
   */
  title: string
  /**
   * 摘要信息
   */
  summary: string
  /**
   * 内容
   */
  content: string
  /**
   * 封面图片
   */
  coverPath: string
  /**
   * 来源
   */
  source: string
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 资讯状态
   */
  status: number
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 浏览数量
   */
  reviewCount: number
  /**
   * 弹窗起始时间
   */
  popupBeginTime: string
  /**
   * 弹窗截止时间
   */
  popupEndTime: string
  /**
   * 上一条咨询id
   */
  previousNewsId: string
  /**
   * 下一条咨询id
   */
  nextNewsId: string
}

export class NewsListResponse {
  /**
   * 资讯编号
   */
  newsId: string
  /**
   * 资讯标题
   */
  title: string
  /**
   * 发布时间
   */
  publishTime: string
}

export class SimpleNewsByPublishPageListResponse {
  /**
   * 资讯编号
   */
  newsId: string
  /**
   * 资讯标题
   */
  title: string
  /**
   * 是否置顶
   */
  top: boolean
  /**
   * 是否弹窗公告
   */
  popUps: boolean
  /**
   * 摘要信息
   */
  summary: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 发布人编号
   */
  publishUserId: string
}

export class SimpleNewsPageListResponse {
  /**
   * 资讯编号
   */
  newsId: string
  /**
   * 资讯标题
   */
  title: string
  /**
   * 是否置顶
   */
  top: boolean
  /**
   * 是否弹窗公告
   */
  popUps: boolean
  /**
   * 摘要信息
   */
  summary: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 发布状态
   */
  publishStatus: number
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询资讯子分类列表
   * @param childNewsCategoryListRequest 资讯分类编号，启用状态
   * @return 资讯分类信息
   * @param query 查询 graphql 语法文档
   * @param childNewsCategoryListRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findChildNewsCategoryList(
    childNewsCategoryListRequest: ChildNewsCategoryListRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findChildNewsCategoryList,
    operation?: string
  ): Promise<Response<Array<NewsCategoryListResponse>>> {
    return commonRequestApi<Array<NewsCategoryListResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { childNewsCategoryListRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询资讯详细
   * @param newsId 资讯编号
   * @return 资讯分类信息
   * @param query 查询 graphql 语法文档
   * @param newsId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findNewsDetail(
    newsId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findNewsDetail,
    operation?: string
  ): Promise<Response<Array<News>>> {
    return commonRequestApi<Array<News>>(
      SERVER_URL,
      {
        query: query,
        variables: { newsId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询资讯展示详细
   * @param newsParentIdCategoryId 资讯分类编号
   * @return 展示详细资讯
   * @param query 查询 graphql 语法文档
   * @param newsParentIdCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findNewsDetailWithPreviousAndNext(
    newsParentIdCategoryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findNewsDetailWithPreviousAndNext,
    operation?: string
  ): Promise<Response<NewsDetailWithPreviousAndNextResponse>> {
    return commonRequestApi<NewsDetailWithPreviousAndNextResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { newsParentIdCategoryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询弹窗公告列表
   * @param top 返回数量
   * @return 资讯列表
   * @param query 查询 graphql 语法文档
   * @param top 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findPopUpsList(
    top: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findPopUpsList,
    operation?: string
  ): Promise<Response<Array<NewsListResponse>>> {
    return commonRequestApi<Array<NewsListResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { top },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询浏览数最多的资讯列表
   * @param top 返回数量
   * @return 资讯列表
   * @param query 查询 graphql 语法文档
   * @param top 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findReviewTopNews(
    top: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findReviewTopNews,
    operation?: string
  ): Promise<Response<Array<NewsListResponse>>> {
    return commonRequestApi<Array<NewsListResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { top },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询资讯分类
   * @param enable 是否启用
   * @return 资讯分类信息
   * @param query 查询 graphql 语法文档
   * @param enable 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findRootNewsCategoryList(
    enable: number,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findRootNewsCategoryList,
    operation?: string
  ): Promise<Response<Array<NewsCategoryListResponse>>> {
    return commonRequestApi<Array<NewsCategoryListResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { enable },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询资讯列表
   * @param newsParentIdCategoryId 资讯分类编号
   * @return 资讯简略资讯列表
   * @param query 查询 graphql 语法文档
   * @param newsParentIdCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findSimpleNewsByPublishPageList(
    newsParentIdCategoryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findSimpleNewsByPublishPageList,
    operation?: string
  ): Promise<Response<Array<SimpleNewsByPublishPageListResponse>>> {
    return commonRequestApi<Array<SimpleNewsByPublishPageListResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { newsParentIdCategoryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询简略资讯列表
   * @param simpleNewsPageListRequest 资讯搜索条件
   * @return 资讯简略资讯列表
   * @param query 查询 graphql 语法文档
   * @param simpleNewsPageListRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findSimpleNewsPageList(
    simpleNewsPageListRequest: SimpleNewsPageListRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findSimpleNewsPageList,
    operation?: string
  ): Promise<Response<Array<SimpleNewsPageListResponse>>> {
    return commonRequestApi<Array<SimpleNewsPageListResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { simpleNewsPageListRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询最多资讯的资讯分类列表
   * @param topNewsCategoryListRequest 排出的资讯分类编号，top数量
   * @return 资讯分类信息
   * @param query 查询 graphql 语法文档
   * @param topNewsCategoryListRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTopNewsCategoryList(
    topNewsCategoryListRequest: TopNewsCategoryListRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findTopNewsCategoryList,
    operation?: string
  ): Promise<Response<Array<NewsCategoryListResponse>>> {
    return commonRequestApi<Array<NewsCategoryListResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { topNewsCategoryListRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 浏览资讯
   * @param newsId 资讯编号
   * @param mutate 查询 graphql 语法文档
   * @param newsId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async browseNews(
    newsId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.browseNews,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newsId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 新建草稿资讯
   * @param newsCreateRequest 草稿资讯信息
   * @return 资讯编号
   * @param mutate 查询 graphql 语法文档
   * @param newsCreateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createNews(
    newsCreateRequest: NewsCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createNews,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newsCreateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 新建资讯分类
   * @param newsCategoryCreateRequest 资讯分类信息
   * @return 资讯编号
   * @param mutate 查询 graphql 语法文档
   * @param newsCategoryCreateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createNewsCategory(
    newsCategoryCreateRequest: NewsCategoryCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createNewsCategory,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newsCategoryCreateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 新建专题资讯草稿
   * @param request
   * @return 资讯编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createSpecialSubjectNews(
    request: SpecialSubjectNewsCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createSpecialSubjectNews,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除资讯
   * @param newsId 资讯编号
   * @param mutate 查询 graphql 语法文档
   * @param newsId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteNews(
    newsId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteNews,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newsId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除资讯分类
   * @param id 分类id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteNewsCategory(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteNewsCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 禁用资讯分类
   * @param id 分类id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableNewsCategory(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableNewsCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用资讯分类
   * @param id 分类id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableNewsCategory(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableNewsCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 发布资讯
   * @param newsId 资讯编号
   * @param mutate 查询 graphql 语法文档
   * @param newsId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async publishNews(
    newsId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.publishNews,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newsId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 发布咨询(智慧就业平台专用)
   * 200为发布成功，500为发布失败
   * @param newsId
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param newsId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pulishNewsByZhjyPlatform(
    newsId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.pulishNewsByZhjyPlatform,
    operation?: string
  ): Promise<Response<NewsCommonResult>> {
    return commonRequestApi<NewsCommonResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newsId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 置为草稿
   * @param newsId 资讯编号
   * @param mutate 查询 graphql 语法文档
   * @param newsId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unPublishNews(
    newsId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.unPublishNews,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newsId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改资讯
   * @param request 修改的资讯信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateNews(
    request: NewsUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateNews,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改资讯分类
   * @param id 分类id
   * @param name 分类名称
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateNewsCategory(
    params: { id?: string; name?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateNewsCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改专题资讯
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateSpecialSubjectNews(
    request: SpecialSubjectNewsUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateSpecialSubjectNews,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
