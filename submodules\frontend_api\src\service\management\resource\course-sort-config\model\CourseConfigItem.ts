import CourseLearningBackstage, {
  BaseResponse,
  CourseSortRuleConfigResponse,
  EffectiveScopeEnum,
  UpdateCourseSortRuleConfigRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { Response } from '@hbfe/common'
import UseRangeEnum from '@api/service/management/resource/course-sort-config/enums/UseRangeEnum'

export default class CourseConfigItem {
  /**
   * 规则id
   */
  ruleId: string = undefined

  /**
   * 规则名称
   */
  ruleName: string = undefined

  /**
   * 乱序
   */
  unSort = false

  /**
   * 周期
   */
  period: string = undefined

  /**
   * 使用范围
   */
  useRange: number | string

  /**
   * 状态
   */
  status = -1

  /**
   * 更改启停用状态
   */
  async changeStatus(): Promise<Response<BaseResponse>> {
    const rq = new UpdateCourseSortRuleConfigRequest()
    rq.configId = this.ruleId
    rq.status = this.status ? 0 : 1
    rq.updateSortCourseNum = false

    const res = await CourseLearningBackstage.updateCourseSortRuleConfigInServicer(rq)

    return res
  }

  /**
   * 模型转化
   * @param dto 后端数据模型
   */
  static from(dto: CourseSortRuleConfigResponse): CourseConfigItem {
    const vo = new CourseConfigItem()
    vo.ruleId = dto.configId
    vo.ruleName = dto.name
    vo.unSort = dto.isShuffle
    vo.period = dto.cronDescribe
    vo.useRange = new UseRangeEnum(dto.effectiveScope).toString()
    vo.status = dto.status
    return vo
  }
}
