import { Property } from '@api/diff-gateway/platform-jxjypxtypt-hljysxh-school'
import CreateOrderParamsCommon from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/CreateOrderParams'
export default class CreateOrderParams extends CreateOrderParamsCommon {
  /**
   * 身份证
   */
  idCard?: string
  /**
   * 用户token
   */
  userToken?: string
  /**
   * 订单拓展属性
   */
  properties?: Array<Property>

  toGeneralCreateOrderRequest() {
    const createOrderParams = new CreateOrderParams()
    const data = super.toGeneralCreateOrderRequest()
    Object.assign(createOrderParams, data)
    createOrderParams.idCard = this.idCard
    createOrderParams.userToken = this.userToken
    createOrderParams.properties = this.properties
    return createOrderParams
  }
}
