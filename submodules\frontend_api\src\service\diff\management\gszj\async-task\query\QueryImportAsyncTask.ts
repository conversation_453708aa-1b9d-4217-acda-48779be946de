import { Page } from '@hbfe/common'
import QueryImportAsyncTask from '@api/service/management/async-task/query/QueryImportAsyncTask'
import PlatformJxjypxtyptSchool, {
  BatchImportQueryRequest,
  FindBatchImportByPageResponse
} from '@api/diff-gateway/platform-jxjypxtypt-gszj-school'
/**
 * 查询导入任务
 */

class QueryImportAsyncTaskDiff extends QueryImportAsyncTask {
  /**
   * @description: 查询导入公需课查看失败数据
   * @param {string} mainTaskId 主任务id
   */
  async batchExportFailChooseDataInServicer(mainTaskId: string): Promise<string> {
    const res = await PlatformJxjypxtyptSchool.batchExportFailChooseDataInServicer(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('查询导入公需课查看失败数据请求失败！')
      return null
    }
    return res.data
  }

  /**
   * @description: 查询导入公需课下载全部数据
   * @param {string} mainTaskId 主任务id
   */
  async batchExportAllChooseDataInServicer(mainTaskId: string): Promise<string> {
    const res = await PlatformJxjypxtyptSchool.batchExportAllChooseDataInServicer(mainTaskId)
    if (!res?.status?.isSuccess()) {
      console.error('查询导入公需课下载全部数据请求失败！')
      return null
    }
    return res.data
  }

  /*
   * @description: 查询导入批量导入公需课列表
   */
  async findBatchImportByPageInServicer(
    page: Page,
    params: BatchImportQueryRequest
  ): Promise<Array<FindBatchImportByPageResponse>> {
    const res = await PlatformJxjypxtyptSchool.findBatchImportByPageInServicer({
      page: page,
      request: params
    })
    if (!res?.status?.isSuccess()) {
      console.error('获取导入批量导入公需课列表请求失败！')
      return new Array<FindBatchImportByPageResponse>()
    }
    page.totalPageSize = res.data?.totalPageSize
    page.totalSize = res.data?.totalSize
    return res.data?.currentPageData || []
  }
}

export default QueryImportAsyncTaskDiff
