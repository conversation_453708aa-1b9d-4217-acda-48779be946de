import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/Trade-default'

// 枚举
export enum RefundMeansEnum {
  NORMAL_REFUND = 'NORMAL_REFUND',
  FORCIBLY_REFUND = 'FORCIBLY_REFUND'
}

// 类

/**
 * 确认批次线上退款信息
 */
export class AffirmOnlineBatchRefundRequest {
  /**
   * 批次退款单号
   */
  batchRefundOrderNo: string
  /**
   * 备注
   */
  remark?: string
}

/**
 * 同意批次退款信息
 */
export class AgreeBatchRefundApplyRequest {
  /**
   * 批次退款单号
   */
  batchRefundOrderNo: string
  /**
   * 备注
   */
  remark?: string
}

/**
 * 批次退款申请信息
 */
export class ApplyBatchRefundRequest {
  /**
   * 批次单号
   */
  batchNo: string
  /**
   * 批次退款单退款方式,空值表示按原来批次单支付方式退款
1:线上退款
2:线下退款
   */
  mode?: number
  /**
   * 申请的退款类型，默认3
1:退货不退款
2:退款不退货
3:退货并退款
   */
  type: number
  /**
   * 退款原因ID
   */
  reasonId?: string
  /**
   * 退款描述
   */
  reason?: string
  /**
   * 退款方式
普通退款、强制退款
默认普通退款
   */
  refundMeans?: RefundMeansEnum
}

/**
 * 申请退款信息
 */
export class ApplyRefundRequest {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 子订单号
   */
  subOrderNo?: string
  /**
   * 退款原因ID
   */
  reasonId?: string
  /**
   * 退款原因
   */
  reason?: string
  refundMeans?: RefundMeansEnum
}

/**
 * 批次单取消信息
<AUTHOR>
@date 2020/8/18
@since 1.1.0
 */
export class BatchOrderCancelRequest {
  /**
   * 批次单号
   */
  batchNo: string
  /**
   * 取消原因ID
   */
  reasonId: string
  /**
   * 取消原因描述
   */
  reasonDescription: string
}

/**
 * 批次单提交信息
<AUTHOR>
@date 2020/8/18
@since 1.1.0
 */
export class BatchOrderCommitRequest {
  /**
   * 批次单号
   */
  batchNo?: string
  /**
   * 批次单总金额
如果有设置金额则批次单以此金额不自动计算,否则会自动计算批次的价格(将批次中所有的订单金额累加)
   */
  totalMoney?: number
}

/**
 * 确认线下支付信息
<AUTHOR>
@date 2020/8/18
@since 1.1.0
 */
export class BatchOrderConfirmPaymentRequest {
  /**
   * 批次单号
   */
  batchNo?: string
  /**
   * 是否成功
   */
  success: boolean
  /**
   * 失败原因
   */
  errMsg?: string
  /**
   * 真正支付完成的收款账户id
   */
  receiveAccountId?: string
}

/**
 * 批次单创建信息
<AUTHOR>
@date 2020/8/18
@since 1.1.0
 */
export class BatchOrderCreateRequest {
  /**
   * 平台ID
   */
  platformId?: string
  /**
   * 平台版本ID
   */
  platformVersionId?: string
  /**
   * 项目ID
   */
  projectId?: string
  /**
   * 子项目ID
   */
  subProjectId?: string
  /**
   * 单位ID
   */
  unitId?: string
  /**
   * 组织机构ID
   */
  organizationId?: string
  /**
   * 集体缴费单位
   */
  collectiveUnitId?: string
  /**
   * 发票索要信息
   */
  invoiceInfo?: InvoiceRequest1
}

/**
 * 批次单更新发票信息
<AUTHOR>
@date 2020/8/18
@since 1.1.0
 */
export class BatchOrderInvoiceUpdateRequest {
  /**
   * 批次单号
   */
  batchOrderNo?: string
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceRequest1
}

/**
 * 线下支付信息
<AUTHOR>
@date 2020/8/18
@since 1.1.0
 */
export class BatchOrderOfflinePayRequest {
  /**
   * 要支付的批次号
   */
  batchNo?: string
  /**
   * 支付金额
   */
  payMoney?: number
  /**
   * 付款人id
   */
  payerId?: string
  /**
   * 收款人账户id
   */
  receiveAccountId?: string
  /**
   * 备注
   */
  remark?: string
  /**
   * 线下付款凭证文件路径集合
   */
  paths?: Array<string>
}

/**
 * 批次单下订单创建信息
<AUTHOR>
@date 2020/8/18
@since 1.1.0
 */
export class BatchOrderOrderCreateRequest {
  /**
   * 买家ID
   */
  buyerId?: string
  /**
   * 销售渠道ID
   */
  marketingChannelId?: string
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceRequest1
  /**
   * 子订单集合
   */
  subOrderList?: Array<SubOrderItemCreateRequest>
}

/**
 * 批次单线上支付信息
<AUTHOR>
@date 2020/8/18
@since 1.1.0
 */
export class BatchOrderPayRequest {
  /**
   * 批次单号
   */
  batchNo?: string
  /**
   * 描述
   */
  description?: string
  /**
   * 收款账号编号
   */
  receiveAccountId?: string
  /**
   * 支付页面地址
   */
  pageUrl?: string
  /**
   * 扩展信息
   */
  extParams?: Map<string, string>
}

/**
 * 取消批次退款信息
 */
export class CancelBatchRefundRequest {
  /**
   * 批次退款单号
   */
  batchRefundOrderNo: string
  /**
   * 取消原因
   */
  reason?: string
}

/**
 * 索要发票信息
 */
export class InvoiceRequest1 {
  /**
   * [必填]发票抬头
   */
  title?: string
  /**
   * [必填]发票抬头类型
@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTitleTypeConst
   */
  titleType: number
  /**
   * 发票类型
@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTypeConst
   */
  type: number
  /**
   * 纳税人识别号，发票抬头类型为企业时必填
   */
  taxpayerNo?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bankName?: string
  /**
   * 账号
   */
  account?: string
  /**
   * 发票邮箱
   */
  email?: string
  /**
   * 发票备注
   */
  remark?: string
  /**
   * 发票的object信息集合
   */
  objectList?: Array<InvoiceObj1>
  /**
   * 是否电子发票
   */
  electron: boolean
  /**
   * 是否非税发票
   */
  noTaxBill: boolean
}

export class InvoiceObj1 {
  objectType?: string
  objectId?: string
}

/**
 * 订单创建信息
 */
export class OrderCreateRequest {
  /**
   * 销售渠道ID
   */
  marketingChannelId?: string
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceRequest1
  /**
   * 子订单集合
   */
  subOrderList?: Array<SubOrderItemCreateRequest>
}

/**
 * 订单发票更新信息
 */
export class OrderInvoiceUpdateRequest {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceRequest1
}

export class OrderPayInfoRequest {
  orderNo?: string
  receiveAccountId?: string
  pageUrl?: string
  extParams?: Map<string, string>
}

/**
 * 拒绝批次退款申请信息
 */
export class RejectBatchRefundApplyRequest {
  /**
   * 批次退款单号
   */
  batchRefundOrderNo: string
  /**
   * 拒绝原因
   */
  reason: string
}

/**
 * 拒绝批次退款信息
 */
export class RejectBatchRefundRequest {
  /**
   * 批次退款单号
   */
  batchRefundOrderNo: string
  /**
   * 拒绝原因
   */
  reason: string
}

export class SubOrderItemCreateRequest {
  /**
   * 商品ID
   */
  commodityId?: string
  /**
   * 是否需要发票
   */
  needBill: boolean
  /**
   * 购买数量
   */
  purchaseQuantity: number
}

/**
 * 索要发票信息
 */
export class InvoiceRequest {
  /**
   * [必填]发票抬头
   */
  title: string
  /**
   * [必填]发票抬头类型
@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTitleTypeConst
   */
  titleType: number
  /**
   * 发票类型
@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTypeConst
   */
  type: number
  /**
   * 纳税人识别号，发票抬头类型为企业时必填
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账号
   */
  account: string
  /**
   * 发票邮箱
   */
  email: string
  /**
   * 发票备注
   */
  remark: string
  /**
   * 发票的object信息集合
   */
  objectList: Array<InvoiceObj>
  /**
   * 是否电子发票
   */
  electron: boolean
  /**
   * 是否非税发票
   */
  noTaxBill: boolean
}

export class InvoiceObj {
  objectType: string
  objectId: string
}

/**
 * 退款校验结果返回
<AUTHOR> create 2020/10/9 14:46
 */
export class ApplyRefundCheckResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 订单校验结果
如果该校验不通过，则不会进行后面几项校验
40001 订单不存在
   */
  subOrderRefundResult: SaleReturnCheckResponse
  /**
   * 发票校验结果
30001 发票蓝票已开具
   */
  invoiceRefundCheckResult: SaleReturnCheckResponse
  /**
   * 商品校验结果
10001  该已售商品无法进行部分退货
10002  该已售商品已失效，无法进行退货
20001  方案考核已通过
   */
  saleReturnCheckResultList: Array<SaleReturnCheckResponse>
}

/**
 * 批次退款校验结果返回值
<AUTHOR> create 2020/10/10 13:50
 */
export class BatchApplyRefundCheckResponse {
  /**
   * 发票校验结果
30001 发票蓝票已开具
   */
  invoiceRefundCheckResult: SaleReturnCheckResponse
  /**
   * 每一笔订单的校验结果
   */
  applyRefundCheckResultList: Array<ApplyRefundCheckResponse>
  /**
   * 批次订单校验结果
如果该校验不通过，则不会进行后面几项校验
40001 订单不存在
@see com.fjhb.platform.core.v1.order.api.constants.SubOrderRefundConstant
   */
  orderRefundResult: SaleReturnCheckResponse
}

/**
 * 批次单信息
<AUTHOR>
@date 2020/8/18
@since 1.1.0
 */
export class BatchOrderResponse {
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 批次单号
   */
  no: string
  /**
   * 批次单状态
0:起始状态
1:添加关联订单中
2:已提交，此时不能在对该批次进行追加关联订单
3:支付中
4:交易关闭中
5:支付失败
6:支付成功
7:发货中
8:发货成功过
9:交易成功
10:交易关闭
   */
  status: number
  /**
   * 批次单总金额(该字段会在批次单提交后计算总金额)
   */
  totalMoney: number
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票索要信息
   */
  invoiceInfo: InvoiceRequest
  /**
   * 收款账户ID
   */
  receiveAccountId: string
  /**
   * 提交时间
   */
  commitTime: string
  /**
   * 交易完成时间,如果最终是交易成功就是交易成功时间,如果最终是交易关闭就是交易关闭时间
   */
  tradeFinishTime: string
  /**
   * 创建类型
1:系统创建
2:用户创建
3:管理员创建
4:历史迁移
5:外部接口
   */
  createType: number
  /**
   * 创建人id
   */
  creatorId: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 批次退款单信息
 */
export class BatchRefundOrderResponse {
  /**
   * 退款单所属平台ID
   */
  platformId: string
  /**
   * 退款单所属平台版本ID
   */
  platformVersionId: string
  /**
   * 退款单所属项目ID
   */
  projectId: string
  /**
   * 退款单所属子项目ID
   */
  subProjectId: string
  /**
   * 退款单所属单位ID
   */
  unitId: string
  /**
   * 退款单所属组织机构ID
   */
  organizationId: string
  /**
   * 退款单号
   */
  no: string
  /**
   * 批次号
   */
  batchNo: string
  /**
   * 退款原因ID
   */
  reasonId: string
  /**
   * 退款金额
   */
  refundAmount: number
  /**
   * 申请退款描述
   */
  reason: string
  /**
   * 批次退款单状态
@see com.fjhb.platform.core.v1.order.api.constants.BatchRefundOrderStatusConst
   */
  refundStatus: number
  /**
   * 申请人ID
   */
  applyUserId: string
  /**
   * 申请时间
   */
  applyTime: string
  /**
   * 拒绝申请原因描述
   */
  refuseApplyDesc: string
  /**
   * 拒绝退款原因描述
   */
  refuseRefundDesc: string
  /**
   * 创建人ID
   */
  creatorId: string
  /**
   * 审核时间
   */
  auditTime: string
  /**
   * 确认退款时间(即点击放款的时间)
   */
  affirmRefundTime: string
  /**
   * 退款成功或退款失败的时间(即退款单处于最终退款结果的时间)
   */
  finishTime: string
  /**
   * 创建方式
@see com.fjhb.platform.core.v1.order.api.constants.BatchRefundOrderCreateTypeConst
   */
  createType: number
  /**
   * 退货单类型,默认退货并退款
@see com.fjhb.platform.core.v1.order.api.constants.BatchRefundOrderTypeConst
   */
  type: number
  /**
   * 退款模式
@see com.fjhb.platform.core.v1.order.api.constants.BatchRefundOrderModeConst
   */
  mode: number
  /**
   * 取消退款原因
   */
  cancelReason: string
  /**
   * 备注
   */
  remark: string
}

/**
 * 订单
 */
export class OrderResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 买家ID
   */
  buyerId: string
  /**
   * 买家名称
   */
  buyerName: string
  /**
   * 卖家ID
   */
  sellerId: string
  /**
   * 卖家名称
   */
  sellerName: string
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票索要信息
   */
  invoiceInfo: InvoiceRequest
  /**
   * 子订单列表
   */
  subOrderList: Array<SubOrderItemResponse>
  /**
   * 订单状态
@see com.fjhb.platform.core.v1.order.api.constants.OrderStatusConst
   */
  orderStatus: number
  /**
   * 订单总金额
   */
  totalAmount: number
  /**
   * 创建方式
@see com.fjhb.platform.core.v1.order.api.constants.OrderCreateTypeConst
   */
  createType: number
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 退款单拓展信息
 */
export class RefundOrderExtResponse {
  /**
   * 业务自定义的objectType
   */
  objectType: string
  /**
   * 业务自定义的objectId
   */
  objectId: string
  /**
   * 备注
   */
  remark: string
}

/**
 * 退款单
 */
export class RefundOrderResponse {
  /**
   * 退款单号
   */
  refundServiceNo: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 主订单号
   */
  refundMasterOrderNo: string
  /**
   * 子订单号
   */
  refundSubOrderNo: string
  /**
   * 是否为虚拟物品
   */
  refundProductVirtual: string
  /**
   * 退款金额
   */
  refundAmount: number
  /**
   * 退款状态
@see com.fjhb.platform.core.v1.order.api.constants.RefundStatusConst
   */
  refundStatus: number
  /**
   * 退款申请人ID
   */
  applyUserId: string
  /**
   * 退款申请时间
   */
  applyTime: string
  /**
   * 退款原因ID
   */
  reasonId: string
  /**
   * 退款原因描述
   */
  reason: string
  /**
   * 审核时间
   */
  auditTime: string
  /**
   * 确认退款时间(即点击放款的时间)
   */
  affirmRefundTime: string
  /**
   * 退款成功或退款失败的时间(即退款单处于最终退款结果的时间)
   */
  finishTime: string
  /**
   * 创建类型
@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderCreateTypeConst
   */
  createType: number
  /**
   * 退款单类型
@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderTypeConst
   */
  type: number
  /**
   * 退款方式
@see com.fjhb.platform.core.v1.order.api.constants.RefundModeConst
   */
  mode: number
  /**
   * 取消退款原因描述
   */
  cancelReason: string
  /**
   * 取消退款申请时间
   */
  cancelApplyDate: string
  /**
   * 拒绝申请原因描述
   */
  refuseApplyDesc: string
  /**
   * 拒绝退款原因描述
   */
  refuseRefundDesc: string
  /**
   * 备注
   */
  remark: string
  /**
   * 操作员ID
   */
  operatorId: string
  /**
   * 拓展信息集合
   */
  extList: Array<RefundOrderExtResponse>
}

/**
 * 校验结果返回类
<AUTHOR> create 2020/10/13 15:18
 */
export class SaleReturnCheckResponse {
  /**
   * 是否允退货
   */
  allow: boolean
  /**
   * 不允许退货原因
   */
  disallowMessage: string
  /**
   * 校验结果返回code
<p>
商品校验结果code 10开头 10001
物品校验结果code 20开头 20001
发票校验结果code 30开头 30001
订单校验结果code 40开头 40001
   */
  code: string
}

/**
 * 子订单的外链信息
 */
export class SubOrderItemLinkResponse {
  /**
   * 子订单外链资源的objectType
   */
  objectType: string
  /**
   * 子订单外链资源的objectId
   */
  objectId: string
  /**
   * 备注
   */
  remark: string
}

/**
 * 子订单
 */
export class SubOrderItemResponse {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 商品溯源码
   */
  commodityTraceCode: string
  /**
   * 商品ID
   */
  commodityId: string
  /**
   * 商品名称
   */
  commodityName: string
  /**
   * 商品图片地址
   */
  photoPath: string
  /**
   * 商品规格
   */
  specification: string
  /**
   * 是否为虚拟物品
   */
  virtualGoods: boolean
  /**
   * 商品标价
   */
  labelPrice: number
  /**
   * 成交单价
   */
  dealPrice: number
  /**
   * 购买数量
   */
  purchaseQuantity: number
  /**
   * 实付总价
   */
  totalAmount: number
  /**
   * 子订单状态
@see com.fjhb.platform.core.v1.order.api.constants.SubOrderStatusConst
   */
  orderStatus: number
  /**
   * 是否需要发票
   */
  needBill: boolean
  /**
   * 子订单的外链资源
   */
  subOrderLinks: Array<SubOrderItemLinkResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 确认线下退款(此操作是在同意退款申请后，管理员通过线下退款后，用于确认已退款)
   * @param refundOrderNo 退款单号
   * @param remark        备注
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async affirmOfflineOrderRefund(
    params: { refundOrderNo?: string; remark?: string },
    mutate: DocumentNode = GraphqlImporter.affirmOfflineOrderRefund,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param affirmInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async affirmOfflineRefund(
    affirmInfo: AffirmOnlineBatchRefundRequest,
    mutate: DocumentNode = GraphqlImporter.affirmOfflineRefund,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { affirmInfo },
      operation: operation
    })
  }

  /**   * 同意退款
   * @param batchRefundNo 批次退款单
   * @param mutate 查询 graphql 语法文档
   * @param batchRefundNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeBatchRefund(
    batchRefundNo: string,
    mutate: DocumentNode = GraphqlImporter.agreeBatchRefund,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { batchRefundNo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param agreeInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeBatchRefundApply(
    agreeInfo: AgreeBatchRefundApplyRequest,
    mutate: DocumentNode = GraphqlImporter.agreeBatchRefundApply,
    operation?: string
  ): Promise<Response<BatchRefundOrderResponse>> {
    return commonRequestApi<BatchRefundOrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { agreeInfo },
      operation: operation
    })
  }

  /**   * 同意退款(此操作是在同意了退款申请后同意进行退款)
   * @param refundOrderNo 退款单号
   * @param mutate 查询 graphql 语法文档
   * @param refundOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeRefund(
    refundOrderNo: string,
    mutate: DocumentNode = GraphqlImporter.agreeRefund,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { refundOrderNo },
      operation: operation
    })
  }

  /**   * 同意退款申请
   * @param refundOrderNo 退款单号
   * @param remark        备注
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeRefundApply(
    params: { refundOrderNo?: string; remark?: string },
    mutate: DocumentNode = GraphqlImporter.agreeRefundApply,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyBatchRefund(
    applyInfo: ApplyBatchRefundRequest,
    mutate: DocumentNode = GraphqlImporter.applyBatchRefund,
    operation?: string
  ): Promise<Response<BatchRefundOrderResponse>> {
    return commonRequestApi<BatchRefundOrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyBatchRefundCheck(
    applyInfo: ApplyBatchRefundRequest,
    mutate: DocumentNode = GraphqlImporter.applyBatchRefundCheck,
    operation?: string
  ): Promise<Response<BatchApplyRefundCheckResponse>> {
    return commonRequestApi<BatchApplyRefundCheckResponse>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyRefund(
    applyInfo: ApplyRefundRequest,
    mutate: DocumentNode = GraphqlImporter.applyRefund,
    operation?: string
  ): Promise<Response<RefundOrderResponse>> {
    return commonRequestApi<RefundOrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyRefundCheck(
    applyInfo: ApplyRefundRequest,
    mutate: DocumentNode = GraphqlImporter.applyRefundCheck,
    operation?: string
  ): Promise<Response<ApplyRefundCheckResponse>> {
    return commonRequestApi<ApplyRefundCheckResponse>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param cancelInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelBatchOrder(
    cancelInfo: BatchOrderCancelRequest,
    mutate: DocumentNode = GraphqlImporter.cancelBatchOrder,
    operation?: string
  ): Promise<Response<BatchOrderResponse>> {
    return commonRequestApi<BatchOrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { cancelInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param cancelInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelBatchRefund(
    cancelInfo: CancelBatchRefundRequest,
    mutate: DocumentNode = GraphqlImporter.cancelBatchRefund,
    operation?: string
  ): Promise<Response<BatchRefundOrderResponse>> {
    return commonRequestApi<BatchRefundOrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { cancelInfo },
      operation: operation
    })
  }

  /**   * 取消订单
   * @param orderNo  订单号
   * @param reasonId 取消原因ID
   * @param remark   取消备注
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelOrder(
    params: { orderNo?: string; reasonId?: string; remark?: string },
    mutate: DocumentNode = GraphqlImporter.cancelOrder,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 取消退款申请
   * @param refundOrderNo 退款单号
   * @param reason        取消退款申请原因，可为空
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelRefundApply(
    params: { refundOrderNo?: string; reason?: string },
    mutate: DocumentNode = GraphqlImporter.cancelRefundApply,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param commitInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async commitBatchOrder(
    commitInfo: BatchOrderCommitRequest,
    mutate: DocumentNode = GraphqlImporter.commitBatchOrder,
    operation?: string
  ): Promise<Response<BatchOrderResponse>> {
    return commonRequestApi<BatchOrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { commitInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param paymentResult 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async confirmBatchOrderOfflinePay(
    paymentResult: BatchOrderConfirmPaymentRequest,
    mutate: DocumentNode = GraphqlImporter.confirmBatchOrderOfflinePay,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { paymentResult },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createBatchOrder(
    createInfo: BatchOrderCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createBatchOrder,
    operation?: string
  ): Promise<Response<BatchOrderResponse>> {
    return commonRequestApi<BatchOrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { createInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createBatchOrderAndOrder(
    params: { batchNo?: string; createInfo?: BatchOrderOrderCreateRequest },
    mutate: DocumentNode = GraphqlImporter.createBatchOrderAndOrder,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOrder(
    createInfo: OrderCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createOrder,
    operation?: string
  ): Promise<Response<OrderResponse>> {
    return commonRequestApi<OrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { createInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param cancelInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async forceCancelBatchOrder(
    cancelInfo: BatchOrderCancelRequest,
    mutate: DocumentNode = GraphqlImporter.forceCancelBatchOrder,
    operation?: string
  ): Promise<Response<BatchOrderResponse>> {
    return commonRequestApi<BatchOrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { cancelInfo },
      operation: operation
    })
  }

  /**   * 强制关闭订单
   * @param orderNo  订单号
   * @param reasonId 关闭原因ID
   * @param remark   关闭备注
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async forceCloseOrder(
    params: { orderNo?: string; reasonId?: string; remark?: string },
    mutate: DocumentNode = GraphqlImporter.forceCloseOrder,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 隐藏用户订单
   * @param orderNo 订单号
   * @param mutate 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async hideOrder(
    orderNo: string,
    mutate: DocumentNode = GraphqlImporter.hideOrder,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { orderNo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param batchOrderOfflinePayInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async offlinePayBatchOrder(
    batchOrderOfflinePayInfo: BatchOrderOfflinePayRequest,
    mutate: DocumentNode = GraphqlImporter.offlinePayBatchOrder,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { batchOrderOfflinePayInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param batchOrderPayInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async onlinePayBatchOrder(
    batchOrderPayInfo: BatchOrderPayRequest,
    mutate: DocumentNode = GraphqlImporter.onlinePayBatchOrder,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { batchOrderPayInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param orderPayInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async payOrder(
    orderPayInfo: OrderPayInfoRequest,
    mutate: DocumentNode = GraphqlImporter.payOrder,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { orderPayInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param rejectInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rejectBatchRefund(
    rejectInfo: RejectBatchRefundRequest,
    mutate: DocumentNode = GraphqlImporter.rejectBatchRefund,
    operation?: string
  ): Promise<Response<BatchRefundOrderResponse>> {
    return commonRequestApi<BatchRefundOrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { rejectInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param rejectInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rejectBatchRefundApply(
    rejectInfo: RejectBatchRefundApplyRequest,
    mutate: DocumentNode = GraphqlImporter.rejectBatchRefundApply,
    operation?: string
  ): Promise<Response<BatchRefundOrderResponse>> {
    return commonRequestApi<BatchRefundOrderResponse>(SERVER_URL, {
      query: mutate,
      variables: { rejectInfo },
      operation: operation
    })
  }

  /**   * 拒绝退款(此操作是在同意了退款申请后在放款给买家时拒绝的)
   * @param refundOrderNo 退款单号
   * @param reason        拒绝退款原因
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rejectRefund(
    params: { refundOrderNo?: string; reason?: string },
    mutate: DocumentNode = GraphqlImporter.rejectRefund,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 拒绝退款申请
   * @param refundOrderNo 退款单号
   * @param reason        拒绝退款申请原因
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rejectRefundApply(
    params: { refundOrderNo?: string; reason?: string },
    mutate: DocumentNode = GraphqlImporter.rejectRefundApply,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 删除批次单，只有起始状态的批次单可以删除
   * @param batchNo 批次单号
   * @param mutate 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeBatchOrderInvoice(
    batchNo: string,
    mutate: DocumentNode = GraphqlImporter.removeBatchOrderInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { batchNo },
      operation: operation
    })
  }

  /**   * 重试线上退款
   * @param batchRefundNo 批次退款单
   * @param mutate 查询 graphql 语法文档
   * @param batchRefundNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async retryOnlineRefund(
    batchRefundNo: string,
    mutate: DocumentNode = GraphqlImporter.retryOnlineRefund,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { batchRefundNo },
      operation: operation
    })
  }

  /**   * 重试批次退款单下订单退款单回收物品
   * @param subOrderNo 子订单号
   * @param mutate 查询 graphql 语法文档
   * @param subOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async retrySubOrderRecycleProduct(
    subOrderNo: string,
    mutate: DocumentNode = GraphqlImporter.retrySubOrderRecycleProduct,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { subOrderNo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateBatchOrderInvoice(
    updateInfo: BatchOrderInvoiceUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateBatchOrderInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { updateInfo },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateOrderInvoice(
    updateInfo: OrderInvoiceUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateOrderInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { updateInfo },
      operation: operation
    })
  }
}

export default new DataGateway()
