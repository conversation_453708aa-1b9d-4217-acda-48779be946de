import SupplierDistributorSalesStatisticsParams from '@api/service/diff/management/xmlg/statistical-report/DistributorSalesStatistics/model/SupplierDistributorSalesStatisticsParams'
import SupplierDistributorSalesStatistics from '@api/service/management/statisticalReport/DistributorSalesStatistics/SupplierDistributorSalesStatistics'
import { Page } from '@hbfe/common'
import FxnlQueryFront from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import SupplierDistributorSalesStatisticsInfo from '@api/service/management/statisticalReport/DistributorSalesStatistics/model/SupplierDistributorSalesStatisticsInfo'
import FxnlDataExport from '@api/platform-gateway/fxnl-data-export-gateway-backstage'

/**
 * 供应商——分销商销售统计
 */
export default class SupplierDistributorSalesStatisticsData extends SupplierDistributorSalesStatistics {
  /**
   * 查询参数
   */
  param = new SupplierDistributorSalesStatisticsParams()

  /**
   * 查询分销商销售统计列表
   */
  async queryList(page: Page) {
    const request = SupplierDistributorSalesStatisticsParams.toStatisticTradeRecordRequestDiff(this.param)
    const response = await FxnlQueryFront.pageDistributorSellStatisticInSupplier({
      page: page,
      request: request
    })
    if (!response.status.isSuccess()) {
      console.error('获取分销商统计列表报错')
      return response
    }
    page.totalPageSize = response.data.totalPageSize
    page.totalSize = response.data.totalSize
    this.list = response.data.currentPageData.map((res) =>
      SupplierDistributorSalesStatisticsInfo.fromDistributorSellStatisticIncludedPurchaseResponse(res)
    )
    return response
  }

  /**
   * 导出列表方法
   * 执行分销产品统计列表导出的逻辑
   */
  async exportList() {
    const request = SupplierDistributorSalesStatisticsParams.toStatisticTradeRecordRequestExportDiff(this.param)
    return FxnlDataExport.exportDistributorSalesStatisticsExcelInSupplier(request)
  }
}
