import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjypxtypt-system-admin-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-system-admin-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class CreateSystemAdministratorRequest {
  /**
   * 姓名【必填】
   */
  name: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 添加的角色id集合【必填】
   */
  addRoleIds: Array<string>
  /**
   * 管理员类型
注：
创建系统管理员时，  传 4
创建供应商管理员时，传 6
创建分销商管理员时，传 7
   */
  adminType: number
}

export class UpdateSystemAdministratorRequest {
  /**
   * 账户id【必填】
   */
  accountId: string
  /**
   * 姓名【必填】
   */
  name: string
  /**
   * 添加的角色id集合
   */
  addRoleIds?: Array<string>
  /**
   * 移除的角色id集合
   */
  removeRoleIds?: Array<string>
}

/**
 * <AUTHOR> [2023/7/11 20:54]
 */
export class GeneralResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 运营管理 创建管理员
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createSystemAdministrator(
    request: CreateSystemAdministratorRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createSystemAdministrator,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新系统管理员
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateSystemAdministrator(
    request: UpdateSystemAdministratorRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateSystemAdministrator,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
