import QueryInvoiceInTrainingChannel from '@api//service/management/trade/single/invoice/query/QueryInvoiceInTrainingChannel'
import { Page } from '@hbfe/common'
import QueryPageInvoiceParam from '@api/service/management/trade/single/invoice/query/dto/QueryPageInvoiceParam'
import {
  OnlineInvoiceSortField,
  OnlineInvoiceSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import InvoiceListResponse from '@api/service/diff/management/fjzj/trade/invoice/model/InvoiceListResponse'

/**
 * 线上发票 - 专题管理员
 */
export default class QueryFjzjInvoiceInTrainingChannel extends QueryInvoiceInTrainingChannel {
  /**
   * 分页查询发票
   * @param page 页数
   * @param queryPageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<InvoiceListResponse>
   */
  async onLineFjzjPageInvoiceInServicer(
    page: Page,
    queryPageInvoiceParam: QueryPageInvoiceParam,
    sort: Array<OnlineInvoiceSortRequest> = [
      { field: OnlineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy.DESC }
    ]
  ): Promise<Array<InvoiceListResponse>> {
    const data = await this.onLinePageInvoiceInServicer(page, queryPageInvoiceParam, sort)
    return data.map((item) => {
      const invoiceListResponse = new InvoiceListResponse()
      Object.assign(invoiceListResponse, item)
      return invoiceListResponse
    })
  }
}
