import TotalStatic from '@api/service/management/statisticalReport/models/TotalStatic'
import DistributorSalesStatisticsParams from '@api/service/management/statisticalReport/DistributorSalesStatistics/model/DistributorSalesStatisticsParams'
import DistributorSalesStatisticsInfo from '@api/service/management/statisticalReport/DistributorSalesStatistics/model/DistributorSalesStatisticsInfo'
import { Page } from '@hbfe/common'

/**
 * 分销商——分销商销售统计
 */
export default class DistributorSalesStatistics {
  /**
   * 查询参数
   */
  params: DistributorSalesStatisticsParams = new DistributorSalesStatisticsParams()
  /**
   * 我的分销
   */
  myDistribution: DistributorSalesStatisticsInfo = new DistributorSalesStatisticsInfo()
  /**
   * 下级分销
   */
  secondDistribution: DistributorSalesStatisticsInfo = new DistributorSalesStatisticsInfo()
  /**
   * 分销统计
   */
  totalStatic: TotalStatic = new TotalStatic()

  /**
   * 查询我的分销
   * @param page
   */
  async queryMyDistribution(page: Page) {
    console.log(1)
  }

  /**
   * 查询下级分销
   * @param page
   */
  async querySecondDistribution(page: Page) {
    console.log(1)
  }

  /**
   * 查询数据统计
   */
  async queryTotalStatic() {
    console.log(1)
  }

  /**
   * 导出数据
   */
  async exportData() {
    console.log(1)
  }
}
