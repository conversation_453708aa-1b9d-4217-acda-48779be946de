import { CreCourseSubjectRequest } from '@api/diff-gateway/platform-jxjypxtypt-gszj-school'
import CourseMaintenanceQueryVoMain from '@api/service/diff/management/byzj/train-class/model/CourseMaintenanceQueryVo'

class CourseMaintenanceQueryVo extends CourseMaintenanceQueryVoMain {
  toDto() {
    const data = super.toDto()
    let params = new CreCourseSubjectRequest()
    params = data
    return params
  }
}

export default CourseMaintenanceQueryVo
