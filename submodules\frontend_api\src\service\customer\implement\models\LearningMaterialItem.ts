import { TeachResourceResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import LearningResourceJsonDto from '@api/service/common/implement/json-model/LearningResourceJsonDto'

export default class LearningMaterialItem {
  /**
   * 附件名称
   */
  name: string = undefined

  /**
   * 文件路径
   */
  path: string = undefined

  /**
   * 文件格式
   */
  format: string = undefined

  /**
   * 展开样式【UI使用】
   */
  unfoldStyle = false

  static from(dto: TeachResourceResponse) {
    const vo = new LearningMaterialItem()
    vo.name = dto.resourceName
    // 这边学习资料是挂方案身上的，所以后端只能给json
    const resource = JSON.parse(dto.resourceContent) as LearningResourceJsonDto
    vo.format = resource.type
    vo.path = resource.path
    return vo
  }
}
