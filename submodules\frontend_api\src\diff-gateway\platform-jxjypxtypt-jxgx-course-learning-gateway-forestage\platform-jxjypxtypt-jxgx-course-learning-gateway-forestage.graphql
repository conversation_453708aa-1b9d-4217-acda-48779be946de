"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
type Query {
	"""江西工信管理系统课程编号换取网校方案信息"""
	getJXGXStudentScheme(JxgxCourseIdentify:String):String
	"""江西工信管理系统终止学习"""
	stopCourseLearning(qualificationId:String):String
}

scalar List
