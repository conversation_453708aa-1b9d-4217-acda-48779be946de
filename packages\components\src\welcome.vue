<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="m-no-date is-row f-ptb50 f-mtb50">
          <img class="img" src="@design/admin/assets/images/no-data-login.png" alt="" />
          <div class="login-txt">
            <p class="f-cb txt-1">恭喜你！</p>
            <p class="txt-2">成功登录{{ webPortalInfo.title }}</p>
          </div>
        </div>
      </el-card>
    </div>
    <template
      v-if="$hasPermission('queryUnitInfoList')"
      desc="内置分销商首页"
      actions="@UnitDialog,changeAuthorizationUnitInfoList"
    >
    </template>
    <unitDialog ref="unitDialogRef"></unitDialog>
  </el-main>
</template>

<style lang="scss" scoped>
  .content {
    line-height: 100px;
    text-align: center;
    font-size: 30px;
    font-weight: bold;
    margin-top: 150px;

    img {
      vertical-align: middle;
    }
  }
</style>

<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import PortalVo from '@api/service/common/online-school-config/vo/PortalVo'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import UnitDialog from '@hbfe/jxjy-admin-authentication/src/login/components/unit-dialog.vue'

  @Component({
    components: {
      UnitDialog
    }
  })
  export default class extends Vue {
    @Ref('unitDialogRef') unitDialogRef: UnitDialog
    webPortalInfo: PortalVo = OnlineSchoolConfigModule.queryPortal.webPortalInfo
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)

    async created() {
      await this.changeAuthorizationUnitInfoList()
    }

    async changeAuthorizationUnitInfoList() {
      if (this.isFxlogin) {
        const currentUnitId = localStorage.getItem('currentUnitId')
        try {
          await QueryManagerDetail.changeAuthorizationUnitInfoList()
          this.unitDialogRef.unitModel = QueryManagerDetail.distributionUnitInformationList
          // 登录成功后保存的分销单位(判斷是否是切換單位操作)
          if (this.unitDialogRef.unitModel.find((res) => res.unitId === currentUnitId)) {
            return
          }
          // fx判断是否展示单位弹框逻辑
          this.unitDialogRef.isShow = this.unitDialogRef.unitModel?.length > 1
        } catch (e) {
          console.log(e)
          return this.$message('获取单位列表失败')
        }
      }
    }
  }
</script>
