import CourseParticipateRequired from '@api/service/common/scheme/model/schemeDto/learning-experience-learning/config/course-participate-required/CourseParticipateRequired'
import SchemeLearningExperienceTopic from '@api/service/common/scheme/model/schemeDto/learning-experience-learning/config/scheme-learning-experience-topics/SchemeLearningExperienceTopic'
import SchemeParticipateRequired from '@api/service/common/scheme/model/schemeDto/learning-experience-learning/config/scheme-participate-required/SchemeParticipateRequired'
import CourseLearningExperienceTopic from '@api/service/common/scheme/model/schemeDto/learning-experience-learning/config/course-learning-experience-topics/CourseLearningExperienceTopic'

/**
 * @description 学习心得学习方式配置
 */
class LearningExperienceLearningConfig {
  /**
   * 展示名称
   */
  showName: string
  /**
   * 配置id
   */
  id: string
  /**
   * 方案参与要求
   */
  schemeParticipateRequired: SchemeParticipateRequired
  /**
   * 方案学习心得列表
   */
  schemeLearningExperienceTopics: SchemeLearningExperienceTopic[]
  /**
   * 课程参与要求
   */
  courseParticipateRequired: CourseParticipateRequired
  /**
   * 课程学习心得列表
   */
  courseLearningExperienceTopics: CourseLearningExperienceTopic[]
  /**
   * 操作类型
   */
  operation: number
}

export default LearningExperienceLearningConfig
