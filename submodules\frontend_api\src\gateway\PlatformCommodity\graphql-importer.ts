import countCommodityChannelVendorOpenNumber from './queries/countCommodityChannelVendorOpenNumber.graphql'
import countCommodityNumber from './queries/countCommodityNumber.graphql'
import countCommodityNumberForChannelVendor from './queries/countCommodityNumberForChannelVendor.graphql'
import countCommodityNumberForCoursewareSupplier from './queries/countCommodityNumberForCoursewareSupplier.graphql'
import countCommodityNumberForTrainingInstitution from './queries/countCommodityNumberForTrainingInstitution.graphql'
import countWorkTypeHasCommodityNumber from './queries/countWorkTypeHasCommodityNumber.graphql'
import getLazyCommodityById from './queries/getLazyCommodityById.graphql'
import getSkuWhichUsedByCommodity from './queries/getSkuWhichUsedByCommodity.graphql'
import getSkuWhichUsedByCommodityQuery from './queries/getSkuWhichUsedByCommodityQuery.graphql'
import listCommodityWithAppraise from './queries/listCommodityWithAppraise.graphql'
import listIssueCommodityInfo from './queries/listIssueCommodityInfo.graphql'
import listWorkTypeCommodityChannelVendorOpenNumber from './queries/listWorkTypeCommodityChannelVendorOpenNumber.graphql'
import listWorkTypeCommodityOpenNumber from './queries/listWorkTypeCommodityOpenNumber.graphql'
import pageCommodityWithAppraise from './queries/pageCommodityWithAppraise.graphql'
import pageLazyCommodity from './queries/pageLazyCommodity.graphql'
import pageLazyCommodityForChannelVendor from './queries/pageLazyCommodityForChannelVendor.graphql'
import pageLazyCommodityForCoursewareSupplier from './queries/pageLazyCommodityForCoursewareSupplier.graphql'
import pageLazyCommodityForParticipatingUnit from './queries/pageLazyCommodityForParticipatingUnit.graphql'
import pageLazyCommodityForTrainingInstitution from './queries/pageLazyCommodityForTrainingInstitution.graphql'

export {
  countCommodityChannelVendorOpenNumber,
  countCommodityNumber,
  countCommodityNumberForChannelVendor,
  countCommodityNumberForCoursewareSupplier,
  countCommodityNumberForTrainingInstitution,
  countWorkTypeHasCommodityNumber,
  getLazyCommodityById,
  getSkuWhichUsedByCommodity,
  getSkuWhichUsedByCommodityQuery,
  listCommodityWithAppraise,
  listIssueCommodityInfo,
  listWorkTypeCommodityChannelVendorOpenNumber,
  listWorkTypeCommodityOpenNumber,
  pageCommodityWithAppraise,
  pageLazyCommodity,
  pageLazyCommodityForChannelVendor,
  pageLazyCommodityForCoursewareSupplier,
  pageLazyCommodityForParticipatingUnit,
  pageLazyCommodityForTrainingInstitution
}
