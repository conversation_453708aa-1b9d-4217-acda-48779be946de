import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import platformUserGateway, { LoginType } from '@api/gateway/PlatformUser'
import { Role, RoleType, UnAuthorize } from '@api/Secure'
import { ResponseStatus } from '@api/Response'

export class CommonUserInfo {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 用户所属平台ID
   */
  platformId: string
  /**
   * 用户所属平台版本ID
   */
  platformVersionId: string
  /**
   * 用户所属项目ID
   */
  projectId: string
  /**
   * 用户所属子项目ID
   */
  subProjectId: string
  /**
   * 用户所属单位ID
   */
  unitId: string
  /**
   * 用户所属组织机构ID
   */
  organizationId: string
  /**
   * 用户域类型
   @see UserdataNorthConstant#USER_REGION_TYPE_CUSTOMER
   @see UserdataNorthConstant#USER_REGION_TYPE_OPERATOR
   @see UserdataNorthConstant#USER_REGION_TYPE_MAINTAINER
   */
  userType: number
  /**
   * 用户昵称
   */
  nickname: string
  /**
   * 用户头像访问地址
   */
  displayPhotoUrl: string
  /**
   * 用户的状态
   @see UserdataNorthConstant#USER_STATUS_NORMAL
   @see UserdataNorthConstant#USER_STATUS_FROZEN
   @see UserdataNorthConstant#USER_STATUS_DELETE
   */
  status: number
  /**
   * 是否已激活
   */
  activated: boolean
  /**
   * 用户信息是否完善
   */
  perfect: boolean
  /**
   * 是否是测试
   */
  test: boolean
  /**
   * 用户唯一性标识类型
   */
  uniqueType: number
  /**
   * 用户唯一性标识值
   */
  uniqueData: string
}

export interface IState {
  commonUserInfo: CommonUserInfo
  userCount: number
}

@Module({ namespaced: true, dynamic: true, name: 'CommonUserModule', store })
class CommonUserModule extends VuexModule implements IState {
  commonUserInfo = new CommonUserInfo()
  userCount = 0
  loadUserCount = true

  /**
   * 校验登录账号是否存在
   * @param param
   */
  @Role([RoleType.user])
  @Action
  async validateLoginAccount(param: { loginType?: LoginType; loginInput?: string; exceptUserId?: string }) {
    const response = await platformUserGateway.identityExists({
      identityType: param.loginType,
      identity: param.loginInput,
      exceptUserId: param.exceptUserId
    })
    if (response.status.isSuccess() && response.data === false) {
      return response.status
    }
    return new ResponseStatus(500, '账号已存在')
  }

  /**
   * 校验用户信息是否存在
   * @param params
   */
  @Role([RoleType.user])
  @Action
  async validateUserExistWithUniqueDataAndName(params: { uniqueData: string; name: string }) {
    const response = await platformUserGateway.userExistsWithIdentityAndName({
      identity: params.uniqueData,
      name: params.name
    })
    if (response.status.isSuccess() && response.data === true) {
      return response.status
    }
    return new ResponseStatus(500, '用户信息不存在')
  }

  // todo 目前没有学员端重置密码，后端未提供
  /**
   * 使用唯一性值和姓名重置用户密码
   */
  @Role([RoleType.user])
  @Action
  async resetPasswordWithUniqueDataAndName(params: { uniqueData: string; name: string; newPassword: string }) {
    //   //账户调整:使用微服务接口
    //   const response = await msAccountGateway.immediateResetPassword(params)
    //   return response.status
    return new ResponseStatus(500)
  }

  // todo 目前没有使用手机验证码token重置用户密码，后端未提供
  /**
   * 使用手机验证码token重置用户密码
   */
  @Role([RoleType.user])
  @Action
  async resetCustomerPasswordWithPhoneCaptchaToken(params: { captchaToken?: string; newPassword?: string }) {
    //   //账户调整:使用微服务接口
    //   const response = await msAccountGateway.resetCustomerPasswordWithPhoneCaptchaToken(params)
    //   return response.status
    return new ResponseStatus(500)
  }

  /**
   * 手机号是否被学员使用，可以排除指定用户
   */
  @Action
  @Role([RoleType.user])
  async isPhoneNumberExists(params: { phoneNumber: string; exceptUserId?: string }) {
    return platformUserGateway.isPhoneNumberExists(params)
  }

  // todo 目前没有使用，后端不提供
  // /**
  //  * 使用账号和密码获取用户通用信息
  //  * @param param
  //  */
  // @Action
  // async loadUserCommonInfoByAccountAndPassword(param: { userType: number; loginInput?: string; password?: string }) {
  //   const response = await platformUser.getUserIdByLoginAccount(param)
  //   if (response.status.isSuccess()) {
  //     this.setUserCommonInfo(response.data)
  //   }
  //   return response.status
  // }

  /**
   * 获取用户数
   */
  @Action
  @UnAuthorize
  async findStudentCount(): Promise<ResponseStatus> {
    if (!this.loadUserCount) {
      return new ResponseStatus(200)
    }
    const response = await platformUserGateway.getStudentCount()
    if (response.status.isSuccess()) {
      this.SET_STUDENT_COUNT(response.data)
      this.SET_LOAD_STUDENT_COUNT(false)
    }
    return response.status
  }

  @Mutation
  setUserCommonInfo(payload: CommonUserInfo) {
    this.commonUserInfo = payload
  }

  @Mutation
  private SET_STUDENT_COUNT(count: number) {
    this.userCount = count
  }

  @Mutation
  private SET_LOAD_STUDENT_COUNT(boo: boolean) {
    this.loadUserCount = boo
  }
}

export default getModule(CommonUserModule)
