import DistributionRegionInfo from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/DistributionRegionInfo'
import { SaleUnitResponse } from '@api/platform-gateway/platform-jxjy-marketing-order-v1'

export default class ScopeRangeItem {
  /**
   * 范围id
   */
  scopeId: string = undefined

  /**
   * 地区范围
   */
  regionList: Array<DistributionRegionInfo> = new Array<DistributionRegionInfo>()

  /**
   * 单位范围
   */
  saleUnitList: Array<SaleUnitResponse> = new Array<SaleUnitResponse>()
}
