<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">专题管理</el-breadcrumb-item>
      <el-breadcrumb-item>编辑专题</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-tabs v-model="activeName" type="card" class="m-tab-card">
        <div class="tab-right">
          <el-button type="primary" size="small" class="f-mr5"
            ><i class="el-icon-picture f-f14 f-vm f-mr5"></i>查看专题示例</el-button
          >
          <el-button type="primary" size="small" class="f-mr5">
            <i class="el-icon-link f-f14 f-vm f-mr5"></i>访问专题web
          </el-button>
          <el-button type="primary" size="small" class="f-mr15">
            <i class="el-icon-link f-f14 f-vm f-mr5"></i>访问专题h5
          </el-button>
        </div>
        <el-tab-pane label="基础信息" name="first">
          见1739_专题管理_编辑专题_基础信息.vue
        </el-tab-pane>
        <el-tab-pane label="门户信息" name="second">
          1740_专题管理_编辑专题_门户信息.vue
        </el-tab-pane>
        <el-tab-pane label="培训方案" name="third">
          1741_专题管理_编辑专题_设置培训信息.vue
        </el-tab-pane>
        <el-tab-pane label="精品课程" name="fourth">
          <!--无分类-->
          <el-card shadow="never" class="m-card f-mb15">
            <div class="f-mt10">
              <span class="f-mr10">请选择精品课程包展示方式：</span>
              <el-radio-group v-model="radio">
                <el-radio :label="3">
                  无分类
                  <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9"></i>
                    <div slot="content">
                      支持不按照分类添加课程，若无精品课程配置，专题门户将不展示精品课程模块。
                    </div>
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
            </div>
            <el-divider class="m-divider"></el-divider>
            <el-button type="primary" icon="el-icon-plus" class="f-mb20">添加精品课程</el-button>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="排序" min-width="70" align="center">
                <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
              </el-table-column>
              <el-table-column label="课程名称" min-width="300">
                <template>课程名称课程名称课程名称课程名称课程名称</template>
              </el-table-column>
              <el-table-column label="课程分类" min-width="180">
                <template>课程分类课程分类</template>
              </el-table-column>
              <el-table-column label="操作时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">取消展示</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="线上集体报名" name="fifth">
          1743_专题管理_编辑专题_线上集体报名.vue
        </el-tab-pane>
        <el-tab-pane label="线下集体报名" name="sixth">
          1744_专题管理_编辑专题_线下集体报名.vue
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'fourth',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
