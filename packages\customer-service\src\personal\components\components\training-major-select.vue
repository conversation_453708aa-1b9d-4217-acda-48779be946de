<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option
      v-for="item in trainingMajorOptions"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import TrainingMajorVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingMajorVo'
  import QueryTrainingMajor from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
  import MajorParam from '@api/service/common/basic-data-dictionary/query/vo/majorParam'

  @Component
  export default class extends Vue {
    selected = ''
    // 选项
    trainingMajorOptions = new Array<TrainingMajorVo>()

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Prop({
      type: String,
      default: '请选培训专业'
    })
    placeholder: string

    // 行业id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string //行业属性id
    // 培训类别id
    @Prop({
      type: String,
      default: ''
    })
    parentPropertyId: string

    /**
     * 查询参数
     */
    @Prop({
      type: Object,
      default: () => {
        return new MajorParam()
      }
    })
    queryParams: MajorParam

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }

    // 查询参数
    @Watch('queryParams', {
      deep: true
    })
    async queryParamsChange(val: MajorParam) {
      if (val?.industryPropertyId && val?.parentPropertyId) {
        await this.queryTrainingMajorOptions()
      } else {
        this.trainingMajorOptions = new Array<TrainingMajorVo>()
        this.selected = ''
      }
    }

    // 获取展示名称
    get showLabel() {
      return (item: TrainingMajorVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    /**
     * 获取培训专业数据
     */
    async queryTrainingMajorOptions() {
      const res = await QueryTrainingMajor.queryTrainingMajor(this.queryParams)
      this.trainingMajorOptions = res.isSuccess() ? QueryTrainingMajor.trainingMajorList : ([] as TrainingMajorVo[])
      // console.log(QueryTrainingMajor.trainingMajorList)
    }
  }
</script>
