import MsTradeQueryFrontGatewayCourseLearningForestage, {
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  Page,
  PortalCommoditySkuPropertyRequest,
  SchemeResourceResponse,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import ThematicMap from '@api/service/customer/thematic-config/ThematicMap'
import SkuPropertyConvertUtils, {
  ComplexSkuPropertyResponse,
  SchemeSkuInfo
} from '@api/service/customer/train-class/Utils/SkuPropertyConvertUtils'
import { SourceEnum } from '@api/service/customer/train-class/query/Enum/SourceEnum'
import { PortalCommoditySkuRequest } from '@api/service/customer/train-class/query/vo/CommodityRequestVo'
import SkuPropertyVo from '@api/service/customer/train-class/query/vo/SkuPropertyVo'
import SkuVo from '@api/service/customer/train-class/query/vo/SkuVo'
import { ResponseStatus } from '@hbfe/common'
import TrainClassCommodityVo from '@api/service/diff/customer/zztt/train-class/model/TrainClassCommodityVo'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import QueryPlatformForestage from '@api/service/diff/common/zztt/dictionary/QueryPlatformForestage'
import ThirdPartyItem from '@api/service/diff/customer/zztt/train-class/model/ThirdPartyItem'
import SchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import QueryTrainClassCommodityListMain from '@api/service/customer/train-class/query/QueryTrainClassCommodityList'
/**
 * 用户域获取培训班商品列表
 */
class QueryTrainClassCommodityList extends QueryTrainClassCommodityListMain {
  /**
   *总数目，类型为number
   */
  totalSize = 0
  /**
   *培训班商品列表，类型为TrainClassCommodityVo[]
   */
  trainClassCommodityList: TrainClassCommodityVo[] = []
  /**
   *刷选条件数组，类型为SkuPropertyVo
   */
  skuProperties = new SkuPropertyVo()
  //当前选中的行业id
  currentInId = ''

  /**
   * 获取培训班列表
   */
  async queryTrainClassCommodityList(
    page: Page,
    filterCommodity: PortalCommoditySkuRequest
  ): Promise<Array<TrainClassCommodityVo>> {
    // const filter = new CommoditySkuRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    const sortRequest = new CommoditySkuSortRequest()
    sortRequest.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequest.policy = SortPolicy.DESC
    filterCommodity.portalCommoditySkuSourceType =
      filterCommodity.portalCommoditySkuPropertyRequest?.portalCommoditySkuSourceType
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pagePortalCommoditySkuCustomerPurchaseInServicer({
      page,
      queryRequest: filterCommodity,
      sortRequest: [sortRequest]
    })
    await QueryPlatformForestage.queryList()
    if (res.status.isSuccess()) {
      // this.trainClassCommodityList = res.data.currentPageData as TrainClassCommodityVo[]
      const tmpArr = []
      const specialIds: Set<string> = new Set()
      for (const item of res.data.currentPageData) {
        const tmpItem = new TrainClassCommodityVo()
        Object.assign(tmpItem, item.originCommodityInfo)
        if (item.portalCommoditySkuForestageResponse) {
          Object.assign(tmpItem.skuProperty, item.portalCommoditySkuForestageResponse.skuProperty)
          tmpItem.portalCommoditySkuId = item.portalCommoditySkuForestageResponse.portalCommoditySkuId
          tmpItem.portalCommoditySkuSourceType = item.portalCommoditySkuForestageResponse.portalCommoditySkuSourceType
          tmpItem.portalCommoditySkuSourceId = item.portalCommoditySkuForestageResponse.portalCommoditySkuSourceId
          if (item.portalCommoditySkuForestageResponse.portalCommoditySkuSourceType === SourceEnum.topic) {
            specialIds.add(item.portalCommoditySkuForestageResponse.portalCommoditySkuSourceId)
          }
        }
        tmpItem.schemeId = (tmpItem.resource as SchemeResourceResponse).schemeId
        tmpItem.schemeType = (tmpItem.resource as SchemeResourceResponse).schemeType
        const tppTypeId = tmpItem.tppTypeId
        if (tmpItem.schemeType == SchemeTypeEnum[SchemeTypeEnum.trainingCooperation] && tppTypeId) {
          tmpItem.thirdPartyInfo = new ThirdPartyItem()
          tmpItem.thirdPartyInfo.id = tppTypeId
          tmpItem.thirdPartyInfo.name = QueryPlatformForestage.map.get(tppTypeId)?.name
        }
        tmpArr.push(tmpItem)
      }
      if (specialIds.size) {
        await ThematicMap.getThematicMap([...specialIds])
      }
      const skuRequest: SchemeSkuInfo[] = []
      tmpArr?.forEach((item) => {
        skuRequest.push(
          new SchemeSkuInfo(item.schemeId, item.skuProperty as ComplexSkuPropertyResponse, item.portalCommoditySkuId)
        )
      })
      const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(skuRequest)
      tmpArr?.forEach((item) => {
        const skuInfo = skuInfos.find(
          (el) => el.id === item.schemeId && el.portalCommodityId == item.portalCommoditySkuId
        )
        if (skuInfo) item.skuValueNameProperty = skuInfo.skuName
        if (item.portalCommoditySkuSourceType === SourceEnum.topic) {
          item.specialType = ThematicMap.map.get(item.portalCommoditySkuSourceId)?.type
          item.specialIndustry = ThematicMap.map.get(item.portalCommoditySkuSourceId)?.industry
          item.specialUnit = ThematicMap.map.get(item.portalCommoditySkuSourceId)?.unitName
        }
      })
      this.trainClassCommodityList = tmpArr
      this.totalSize = res.data.totalSize
    }
    return this.trainClassCommodityList
  }
  switchTrainCate(cate: SkuVo) {
    // if (cate.skuPropertyValueId) {
    this.skuProperties.filterTrainMajor(cate)
    // }
  }
  /**
   * 获取培训班属性
   */
  async querySku(
    sku: PortalCommoditySkuPropertyRequest = new PortalCommoditySkuPropertyRequest(),
    isWeb?: boolean
  ): Promise<ResponseStatus> {
    if (sku.industry && sku.industry.length) {
      this.currentInId = sku.industry[0]
    }
    const sortRequest = new CommoditySkuSortRequest()
    sortRequest.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequest.policy = SortPolicy.DESC
    const res =
      await MsTradeQueryFrontGatewayCourseLearningForestage.listPortalCommoditySkuPropertyCustomerPurchaseInServicer(
        sku
      )
    if (res.status.isSuccess() && res.data) {
      // 优化点：如果只有一个行业，就直接赋值给currentInId
      const { industry } = res.data
      if (industry && industry.length === 1 && industry[0].skuPropertyValueId) {
        this.currentInId = res.data.industry[0].skuPropertyValueId
      }
      try {
        // 获取sku选项
        this.skuProperties = await SkuPropertyConvertUtils.convertToSkuPropertyVo(res.data, this.currentInId, isWeb)
      } catch (e) {
        console.log(e)
      }
    }
    return res.status
  }

  static async getSchemeConfig(schemeIds: string[]) {
    const schemeRes = await SchemeLearningQueryForestage.pageSchemeConfigInServicer({
      page: {
        pageNo: 1,
        pageSize: schemeIds.length
      },
      schemeIds,
      needField: ['extendProperties', 'notice']
    })
    const schemeMap = new Map<string, { notice: string; showNoticeDialog: boolean }>()
    schemeRes.data.currentPageData.forEach((item) => {
      try {
        const schemeConfig = JSON.parse(item.schemeConfig)
        const getExtendProperties = (key: string) =>
          schemeConfig.extendProperties.find((item: any) => item.name == key)?.value
        schemeMap.set(item.schemeId, {
          notice: schemeConfig.notice,
          showNoticeDialog: getExtendProperties('showNoticeDialog')
        })
      } catch (e) {
        console.log(e)
      }
    })
    return schemeMap
  }
  // endregion
}
export default QueryTrainClassCommodityList
