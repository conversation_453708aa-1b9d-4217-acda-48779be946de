import MsAccountGateway from '@api/ms-gateway/ms-account-v1'
import { ResponseStatus } from '@hbfe/common'
import CreateStudentRequestVo from './vo/create/CreateStudentRequestVo'
import basicDataDomain, {
  CreateOnlineSchoolStudentRequest,
  CreateStudentRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import Context from '@api/service/common/context/Context'

/**
 * 学员账号注册
 */
class MutationRegisterUser {
  createStudentParams = new CreateStudentRequestVo()

  async doRegister(): Promise<ResponseStatus> {
    const { status } = await MsAccountGateway.registerStudent(this.createStudentParams)
    return status
  }

  /**
   * 注册
   * 200  成功
   * 402  核验异常
   * 400 token解析异常
   * 408 验证码过期，请重新获取验证码
   * 500 验证码错误，请重新输入
   * 100001（手机号码）、100002（身份证）  该证件号/手机号码已注册学员账号，请直接登录
   * @param params
   */
  async newRegistry(params: CreateStudentRequest) {
    const response = await basicDataDomain.registerStudent(params)

    return response
  }

  async registryV2(params: CreateStudentRequest) {
    return basicDataDomain.registerStudentV2(params)
  }

  /**
   * 分销注册绑定学员账号
   * @param params
   * * 200 成功
   * * 402 核验异常
   * * 400 token解析异常
   * * 408 验证码过期，请重新获取验证码
   * * 500 验证码错误，请重新输入
   * * 100002 身份证已存在
   * * 100001 手机号已存在
   * * 10003 当前分销学员已绑定与继续教育网校中的其他学员进行绑定
   * * 10005 当前分销学员已绑定该网校中的其他学员
   */
  async createOnlineSchoolStudent(params: CreateOnlineSchoolStudentRequest) {
    params.onlineSchoolServicerId = Context.businessEnvironment?.serviceToken?.tokenMeta?.servicerId
    return basicDataDomain.createOnlineSchoolStudent(params)
  }
}
export default MutationRegisterUser
