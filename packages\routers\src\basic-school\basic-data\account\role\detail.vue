<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON> yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: <PERSON> yt
 * @LastEditTime: 2022-06-10 09:54:19
-->
<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON> yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: Lin yt
 * @LastEditTime: 2022-06-08 17:09:57
-->
<route-meta>{"title": "角色详情"}</route-meta>
<route-params content="/:id"></route-params>
<script lang="ts">
  import Detaile from '@hbfe/jxjy-admin-account/src/role/detail.vue'
  export default class extends Detaile {}
</script>
