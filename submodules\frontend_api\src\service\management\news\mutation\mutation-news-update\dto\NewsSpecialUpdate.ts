/*
 * @Description: 创建资讯草稿DTO
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-17 13:37:51
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-18 14:25:02
 */
import { NewsUpdateRequest, SpecialSubjectInfo, SpecialSubjectNewsUpdateRequest } from '@api/ms-gateway/ms-news-v1'
import NewsDetailVo from '@api/service/management/news/query/query-news-detail/vo/NewsDetail'

class NewsSpecialUpdate extends NewsUpdateRequest {
  static from(createDraftNewsVo: NewsDetailVo) {
    const request = new SpecialSubjectNewsUpdateRequest()
    request.newsId = createDraftNewsVo.newId
    request.title = createDraftNewsVo.title
    request.summary = createDraftNewsVo.abstract
    request.coverPath = createDraftNewsVo.bgImage
    request.content = createDraftNewsVo.content
    request.source = createDraftNewsVo.source
    request.publishTime = createDraftNewsVo.time
    request.popUps = createDraftNewsVo.isPopup
    request.top = createDraftNewsVo.top
    request.popupBeginTime = createDraftNewsVo.popupBeginTime
    request.popupEndTime = createDraftNewsVo.popupEndTime
    request.newsCategoryId = createDraftNewsVo.categoryType[createDraftNewsVo.categoryType.length - 1]
    request.areaCodePath =
      createDraftNewsVo?.areaCodeList && createDraftNewsVo?.areaCodeList.length
        ? '/' + createDraftNewsVo.areaCodeList.join('/')
        : undefined
    request.specialSubjectInfo = createDraftNewsVo.specialList.length
      ? createDraftNewsVo.specialList[0]
      : new SpecialSubjectInfo()
    request.verifyPopUps = createDraftNewsVo.verifyPopUps
    return request
  }
}
export default NewsSpecialUpdate
