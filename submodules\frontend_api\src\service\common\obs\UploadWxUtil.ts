import axios from 'axios'
import { UpLoadBase64 } from './UploadModule'
import Env from '@api/service/common/utils/Env'
/*
 * @Author: <PERSON><PERSON>ong
 * @Date: 2022-12-12 08:43:05
 * @LastEditors: ZhuSong
 * @LastEditTime: 2022-12-15 15:06:48
 * @Description:
 */
const MAX_FILE_SIZE = 200 * 1024 * 1024
function checkDomain() {
  //验证是否是域名
  const doname =
    /^([\w-]+\.)+((com)|(net)|(org)|(gov\.cn)|(info)|(cc)|(com\.cn)|(net\.cn)|(org\.cn)|(name)|(biz)|(tv)|(cn)|(mobi)|(name)|(sh)|(ac)|(io)|(tw)|(com\.tw)|(hk)|(com\.hk)|(ws)|(travel)|(us)|(tm)|(la)|(me\.uk)|(org\.uk)|(ltd\.uk)|(plc\.uk)|(in)|(eu)|(it)|(jp))$/
  const flag_domain = doname.test(document?.domain)
  if (!document) {
    // 移动端（小程序、H5）默认用域名
    return true
  }
  if (!flag_domain) {
    //错误的域名
    return false
  } else {
    return true
  }
}

export default class UploadUtil {
  /**
   * 是否为公共的
   */
  isPublic = false
  /**
   * 上传百分比
   */
  progress = 0
  /**
   * axiox 取消请求实例
   */
  private source = axios.CancelToken.source()
  /**
   * 图片OR文件
   */
  private isImageRequest = true
  /**
   * 传文件使用
   */
  private MD5 = ''

  // 上传图片
  uploadBase64(data: UpLoadBase64, authentication: any) {
    this.isImageRequest = true
    const config = this.init()
    return authentication.options.request.post(config.action, data, { header: config.headers })
  }
  // 上传文件
  uploadFileBlock(data: string, authentication: any): any {
    this.isImageRequest = false
    const config = this.init()
    return authentication.options.request.post(config.action, data, { header: config.headers })
  }

  // 初始化请求配置
  private init() {
    const env = Env.proxyEnvStr
    const port = Env.proxyPortStr
    let action
    const headers = new Object()
    console.log(env, 'env')
    console.log(process.env.VUE_APP_HOST, 'host')
    if (checkDomain()) {
      if (Env.isProxyInnerNetworkEnv) {
        // 内网环境
        action = `${['https://api', env, '59iedu.com', port].join('')}${'/web/ms-obsfile-v1'}`
      } else {
        // 生产环境
        action = `https://api.59iedu.com${'/web/ms-obsfile-v1'}`
      }
    } else {
      // IP访问
      if (process.env.NODE_ENV != 'production') {
        // 开发模式
        action = `http://************:1457/web/ms-obsfile-v1`
      } else {
        // 部署模式
        action = `${location.protocol}//${location.host}/web/ms-obsfile-v1`
        // console.log(config.url)
      }
    }

    if (this.isPublic) {
      if (this.isImageRequest) {
        action += '/web/uploadPublicBase64'
      } else {
        action += '/web/uploadPublic'
      }
    } else {
      if (this.isImageRequest) {
        action += '/web/uploadProtectedBase64'
      } else {
        action += '/web/uploadMedia'
      }
    }
    console.log(action, 'action')
    // 设置额外请求头
    headers['Service-Name'] = 'ms-obsfile-v1'

    if (this.isImageRequest) {
      headers['Graphql-SchemaName'] = 'ms-obsfile-v1'
    } else {
      headers['content-type'] = 'multipart/form-data; boundary=XXX'
    }
    return {
      action,
      headers
    }
  }
}
