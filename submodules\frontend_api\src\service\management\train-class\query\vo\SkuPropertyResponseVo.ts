import SkuVo from '@api/service/management/train-class/query/vo/SkuVo'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * 班级上的sku属性
 */
class SkuPropertyResponseVo {
  // region properties

  /**
   *年度，类型为SkuVo
   */
  year = new SkuVo()
  /**
   *地区，类型为SkuVo
   */
  region = new SkuVo()
  /**
   *行业，类型为SkuVo
   */
  industry = new SkuVo()
  /**
   *科目类型，类型为SkuVo
   */
  subjectType = new SkuVo()
  /**
   * 技术等级
   */
  technicalGrade = new SkuVo()
  /**
   *培训类别，类型为SkuVo
   */
  trainingCategory = new SkuVo()
  /**
   *培训专业，类型为SkuVo
   */
  trainingMajor = new SkuVo()
  /**
   * 培训对象，类型为SkuVo
   */
  trainingObject = new SkuVo()
  /**
   * 岗位类别，类型为SkuVo
   */
  positionCategory = new SkuVo()
  /**
   * 技术等级-新
   */
  jobLevel = new SkuVo()
  /**
   * 学段
   */
  learningPhase = new SkuVo()
  /**
   * 学科
   */
  discipline = new SkuVo()
  /**
   * 证书类型
   */
  certificatesType = new SkuVo()
  /**
   * 执业类别
   */
  practitionerCategory = new SkuVo()
  /**
   * 培训形式
   */
  trainingMode = new SkuVo<TrainingModeEnum>()
  // endregion
  // region methods

  // endregion
}
export default SkuPropertyResponseVo
