/*
  试题异步任务查询
*/

import MsImportopenGateway, { TaskExecuteParamRequest } from '@api/ms-gateway/ms-importopen-v1'
import { Page } from '@hbfe/common'
import QueryImportTaskVo from './vo/QueryImportTaskVo'
class QueryQuestionAsynTask {
  /**
   * @description: 查询指定批次号下的导入试题列表
   * @param {string} collectiveSignupNo 批次号
   */
  async queryQueryImportList(params: TaskExecuteParamRequest, page: Page): Promise<Array<QueryImportTaskVo>> {
    const res = await MsImportopenGateway.findTaskExecuteWithServicerResponseByPage({
      param: params,
      page
    })
    if (!res?.status?.isSuccess()) {
      console.error('请求导入试题列表失败！')
      return new Array<QueryImportTaskVo>()
    }
    const arr = Array<QueryImportTaskVo>()
    res?.data?.currentPageData?.forEach((item) => {
      const temp = new QueryImportTaskVo()
      temp.from(item)
      arr.push(temp)
    })
    page.totalPageSize = res.data?.totalPageSize
    page.totalSize = res.data?.totalSize
    const result = arr?.length ? arr : new Array<QueryImportTaskVo>()
    return result
  }

  /**
   * @description: 专题管理员-查询指定批次号下的导入试题列表
   * @param {string} collectiveSignupNo 批次号
   */
  async queryQueryImportListByThemeManeger(
    params: TaskExecuteParamRequest,
    page: Page
  ): Promise<Array<QueryImportTaskVo>> {
    const res = await MsImportopenGateway.findTaskExecuteWithSelfResponseByPage({
      param: params,
      page
    })
    if (!res?.status?.isSuccess()) {
      console.error('请求导入试题列表失败！')
      return new Array<QueryImportTaskVo>()
    }
    const arr = Array<QueryImportTaskVo>()
    res?.data?.currentPageData?.forEach((item) => {
      const temp = new QueryImportTaskVo()
      temp.from(item)
      arr.push(temp)
    })
    page.totalPageSize = res.data?.totalPageSize
    page.totalSize = res.data?.totalSize
    const result = arr?.length ? arr : new Array<QueryImportTaskVo>()
    return result
  }
}
export default QueryQuestionAsynTask
