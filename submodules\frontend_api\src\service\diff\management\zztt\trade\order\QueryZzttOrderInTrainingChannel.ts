import { Page } from '@hbfe/common'
import QueryOrderListVo from '@api/service/management/trade/single/order/query/vo/QueryOrderListVo'
import OrderDetailVo from '@api/service/diff/management/zztt/trade/order/model/OrderDetailVo'
import QueryOrderInTrainingChannel from '@api/service/management/trade/single/order/query/QueryOrderInTrainingChannel'

/**
 * 专题管理员查询订单
 */
export default class QueryZzttOrderInTrainingChannel extends QueryOrderInTrainingChannel {
  /**
   * 查询订单商品分页
   * @param {Page} page - 分页参数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryzzttOrderList(
    page: Page,
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderDetailVo[]> {
    const data = await this.queryOrderList(page, queryParams, isBusinessConsult)
    return data.map((item) => {
      const orderDetailVo = new OrderDetailVo()
      Object.assign(orderDetailVo, item)
      return orderDetailVo
    })
  }
}
