"""独立部署的微服务,K8S服务名:ms-dictionary-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""新增行业属性
		@param industryAdd 新增参数
		@return id
	"""
	addIndustryProperty(industryAdd:IndustryPropertyAddRequest):String
	"""移除行业属性
		@param propertyId 属性id
	"""
	removeIndustryProperty(propertyId:String):Void
	"""修改行业属性
		@param updateDto 更新参数
	"""
	updateIndustryProperty(updateDto:IndustryPropertyUpdateRequest):Void
	"""修改网校名称
		@param request 参数信息
	"""
	updateShowName(request:IndustryTrainingPropertyUpdateRequest):Void
	"""验证属性名称是否存在
		@param propertyId   属性id
		@param propertyName 属性名称
		@return 属性名称是否存在
	"""
	verifyNameExists(propertyId:String,propertyName:String):Boolean!
}
"""行业新增"""
input IndustryPropertyAddRequest @type(value:"com.fjhb.ms.dictionary.v1.kernel.gateway.graphql.request.IndustryPropertyAddRequest") {
	"""所属行业id"""
	bisId:String
	"""行业属性名称"""
	propertyName:String
	"""行业培训属性新增对象集合"""
	trainingPropertyList:[IndustryPropertyCategoryAddRequest]
	"""序号"""
	sort:Int!
}
"""行业属性类别新增对象"""
input IndustryPropertyCategoryAddRequest @type(value:"com.fjhb.ms.dictionary.v1.kernel.gateway.graphql.request.IndustryPropertyCategoryAddRequest") {
	"""行业属性分类code代码
		@see DictionaryConstant.IndustryPropertyCode
	"""
	industryPropertyCategoryCode:String
	"""序号"""
	sort:Int!
	"""行业培训属性新增对象"""
	propertyList:[IndustryTrainingPropertyAddRequest]
}
"""行业属性类别新增对象"""
input IndustryPropertyCategoryUpdateRequest @type(value:"com.fjhb.ms.dictionary.v1.kernel.gateway.graphql.request.IndustryPropertyCategoryUpdateRequest") {
	"""行业属性分类code代码
		@see DictionaryConstant.IndustryPropertyCode
	"""
	industryPropertyCategoryCode:String
	"""序号"""
	sort:Int!
	"""是否启用"""
	enable:Boolean!
	"""新增的行业培训属性id集合
		新增先执行
	"""
	addPropertyIdList:[String]
	"""移除的行业培训属性id集合
		删除后执行
	"""
	removePropertyIdList:[String]
}
input IndustryPropertyUpdateRequest @type(value:"com.fjhb.ms.dictionary.v1.kernel.gateway.graphql.request.IndustryPropertyUpdateRequest") {
	"""行业属性id"""
	industryPropertyId:String
	"""所属行业id"""
	bisId:String
	"""行业属性名称"""
	propertyName:String
	"""行业培训属性新增对象集合"""
	trainingPropertyList:[IndustryPropertyCategoryUpdateRequest]
	"""序号"""
	sort:Int!
}
"""行业培训属性新增对象"""
input IndustryTrainingPropertyAddRequest @type(value:"com.fjhb.ms.dictionary.v1.kernel.gateway.graphql.request.IndustryTrainingPropertyAddRequest") {
	"""行业培训属性id"""
	propertyId:String
}
input IndustryTrainingPropertyUpdateRequest @type(value:"com.fjhb.ms.dictionary.v1.kernel.gateway.graphql.request.IndustryTrainingPropertyUpdateRequest") {
	"""属性id"""
	propertyId:String
	"""所属行业id"""
	bisId:String
	"""行业属性名称"""
	showName:String
	"""网校id"""
	schoolId:String
}

scalar List
