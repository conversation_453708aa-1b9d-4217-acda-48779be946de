export default class BillConfigInfo {
  /**
   * 发票配置 Id
   */
  id: string
  /**
   * 是否提供发票|1：不提供 2：提供&#x27;
   */
  isProvide: number
  /**
   * 发票提供类型|0：无 1：学员自选是否需要发票 2：强制提供&#x27;
   */
  provideType: number
  /**
   * 支持开票的发票类型 (将会是com.fjhb.courseSupermarket.service.bill.dto.InvoiceTypeEnum里面值的组合或者单个值，例如：COMMON_VAT&amp;-&amp;COMMON_ELECTRON)
   */
  supportInvoiceTypes: string
  /**
   * 支持开票的发票抬头 (将会是com.fjhb.courseSupermarket.service.bill.dto.InvoiceTitleEnum里面值的组合或者单个值，例如：PERSONAL&amp;-&amp;UNIT)
   @see InvoiceTitleEnum
   */
  supportInvoiceTitles: string
  /**
   * 是否选择增值税普通发票(发票类型)
   */
  selectCommonVAT: boolean
  /**
   * 是否选择普通电子发票(发票类型)
   */
  selectCommonElectron: boolean
  /**
   * 是否选择增值税专用发票(发票类型)
   */
  selectVATOnly: boolean
  /**
   * 是否选择非税务发票(发票类型)
   */
  selectNonTax: boolean
  /**
   * 是否选择了个人(发票抬头)
   */
  selectPersonal: boolean
  /**
   * 是否选择了单位(发票抬头)
   */
  selectUnit: boolean
}
