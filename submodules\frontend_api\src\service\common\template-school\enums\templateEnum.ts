export enum WebTemplateEnum {
  /**
   * 人社web模板
   */
  RSDEFAULT = 'TestTemplateId-1',
  RSPC001 = 'rs-pc-001',
  RSPC002 = 'rs-pc-002',
  RSPC003 = 'rs-pc-003',
  /**
   * 建设模板
   */
  JSDEFAULT = 'TestTemplateId-1',
  JSPC001 = 'js-pc-001',
  JSPC002 = 'js-pc-002',
  JSPC003 = 'js-pc-003',
  JSPC004 = 'js-pc-004',
  JSPC005 = 'js-pc-005'
}

export enum H5TemplateEnum {
  /**
   * 人社H5模板
   */
  RSH5DEFAULT = 'TestH5TemplateId-1',
  RSH5001 = 'rs-h5-001',
  /**
   * 建设H5模板
   */
  JSH5DEFAULT = 'TestH5TemplateId-1',
  JSH5001 = 'js-h5-001'
}
