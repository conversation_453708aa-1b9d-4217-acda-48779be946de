<template>
  <div>
    <!-- 新增证书抽屉 -->
    <el-drawer
      :title="drawerTitle"
      size="800px"
      custom-class="m-drawer"
      :visible.sync="uiStatus.isOpenCertificationDrawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <div class="drawer-bd">
        <el-form
          ref="drawerFormRef"
          :model="drawerForm"
          :rules="drawerFormRules"
          label-width="auto"
          class="m-form f-mt20"
        >
          <el-form-item label="证书类别：" prop="certificationType">
            <training-category-select
              :industryPropertyId="jsIndustryPropertyId"
              :industryId="industryId"
              v-model="drawerForm.certificationType"
            ></training-category-select>
          </el-form-item>
          <el-form-item label="选择专业：" v-if="uiStatus.isShowTrainingMajor" prop="specialty">
            <el-select v-model="drawerForm.specialty" placeholder="选择专业" clearable>
              <el-option
                v-for="item in trainingMajorList"
                :key="item.propertyId"
                :label="item.name"
                :value="item.propertyId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="证书编号：" prop="certificationNo">
            <el-input v-model="drawerForm.certificationNo" clearable placeholder="请输入证书编号" class="form-l" />
          </el-form-item>
          <el-form-item label="证书发证日期：" prop="certificationStartTime">
            <el-date-picker
              v-model="drawerForm.certificationStartTime"
              :picker-options="pickerStartOptions"
              type="datetime"
              placeholder="请输入证书发证日期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="证书有效期：" prop="certificationEndTime">
            <el-date-picker
              v-model="drawerForm.certificationEndTime"
              :picker-options="pickerEndOptions"
              type="datetime"
              placeholder="请输入证书有效期"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="证书附件：">
            <add-certification-images v-model="drawerForm.certificationList"></add-certification-images>
          </el-form-item>

          <el-form-item class="m-btn-bar">
            <el-button @click="cancelAddCertifition">取消</el-button>
            <el-button type="primary" @click="addCertifition">保存</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Watch, Prop } from 'vue-property-decorator'
  import { ElForm } from 'element-ui/types/form'
  import AddCertificationImages from '@hbfe/jxjy-admin-components/src/upload-images.vue'
  import TrainingCategorySelect from '@hbfe/jxjy-admin-customerService/src/personal/components/components/training-category-select.vue'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import MajorParam from '@api/service/common/basic-data-dictionary/query/vo/majorParam'
  import QueryTrainingMajor from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
  import TrainingMajorVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingMajorVo'
  import { cloneDeep } from 'lodash'
  import CertificateInfoVo from '@api/service/management/user/mutation/student/vo/CertificateInfoVo'
  import StudentCertificateInfoVo from '@api/service/management/user/query/student/vo/StudentCertificateInfoVo'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'

  export class DrawerForm {
    // industryId: string
    certificateId: string
    certificationType: string
    specialty: string
    certificationNo: string
    certificationStartTime: string
    certificationEndTime: string
    // 图片附件数组
    certificationList: Array<CertificationUrl>
    constructor() {
      // ;(this.industryId = ''),
      ;(this.certificateId = ''),
        (this.certificationType = ''),
        (this.specialty = ''),
        (this.certificationNo = ''),
        (this.certificationStartTime = ''),
        (this.certificationEndTime = ''),
        // 图片附件数组
        (this.certificationList = new Array<CertificationUrl>())
    }
  }

  export class CertificationUrl {
    name: string
    url: string
  }

  @Component({
    components: {
      AddCertificationImages,
      TrainingCategorySelect
    }
  })
  export default class extends Vue {
    drawerForm: DrawerForm

    // 新增证书的表单项
    constructor() {
      super()
      this.drawerForm = new DrawerForm()
      this.drawerForm.certificationList = new Array<CertificationUrl>()
    }

    @Ref('drawerFormRef')
    drawerFormRef: ElForm

    uiStatus = {
      // 培训专业
      isShowTrainingMajor: false,
      // 新增证书抽屉
      isOpenCertificationDrawer: false
    }

    // 新增证书必填校验
    drawerFormRules = {
      certificationType: [{ required: true, message: '证书类别不能为空！', trigger: 'change' }],
      specialty: [{ required: true, message: '专业类别不能为空！', trigger: 'change' }],
      certificationNo: [{ required: true, message: '证书编号不能为空！', trigger: 'change' }],
      certificationStartTime: [{ required: true, message: '证书发证日期不能为空！', trigger: 'change' }],
      certificationEndTime: [{ required: true, message: '证书有效期不能为空！', trigger: 'change' }]
    }
    drawerTitle = '新增证书'
    // 行业id
    jsIndustryPropertyId = ''
    // industryId
    industryId = ''
    // 临时存储专业列表
    trainingMajorList = new Array<TrainingMajorVo>()

    // 开始时间禁用区间
    pickerStartOptions = {
      disabledDate: (time: Date) => {
        const endTime = this.drawerForm.certificationEndTime
        const myDate = Date.now()
        let compareTime: number = myDate
        // 已经选择结束时间
        if (endTime) {
          compareTime = new Date(endTime).getTime()
          return time.getTime() >= myDate || time.getTime() > compareTime
        } else {
          return time.getTime() > myDate
        }
      }
    }
    // 结束时间禁用区间
    pickerEndOptions = {
      disabledDate: (time: Date) => {
        const startTime = this.drawerForm.certificationStartTime
        // 当前日期
        // 已经选择开始时间
        if (startTime) {
          const nowTime = new Date(startTime).getTime()
          return time.getTime() <= nowTime
        }
      }
    }

    // 证书类别变化
    @Watch('drawerForm.certificationType')
    async certificationTypeChange(id: string) {
      if (id && this.jsIndustryPropertyId) {
        const param = new MajorParam()
        param.industryPropertyId = this.jsIndustryPropertyId
        param.parentPropertyId = id
        const hasMajor: boolean = await this.queryTrainingMajor(param)
        // 修改操作传入的专业id
        if (hasMajor && this.drawerForm?.specialty) {
          // console.log('培训类别有专业')
          this.uiStatus.isShowTrainingMajor = true
        } else if (hasMajor && !this.drawerForm?.specialty) {
          this.uiStatus.isShowTrainingMajor = true
          this.drawerForm.specialty = ''
        } else {
          // console.log('没有专业的情况')
          this.uiStatus.isShowTrainingMajor = false
          this.drawerForm.specialty = ''
        }
      } else {
        // console.log('砂石款激烈的哈市')
        this.uiStatus.isShowTrainingMajor = false
        this.drawerForm.specialty = ''
      }
    }

    async created() {
      await this.queryConstructPropertyId()
    }

    // 根据父组件传入的值给表单赋值
    transformData(item: StudentCertificateInfoVo) {
      if (item) {
        this.drawerForm.certificateId = item.certificateId
        this.drawerForm.certificationType = item.certificateCategory
        this.drawerForm.specialty = item.registerProfessional || undefined
        this.drawerForm.certificationNo = item.certificateNo
        this.drawerForm.certificationStartTime = item.releaseStartTime
        this.drawerForm.certificationEndTime = item.certificateEndTime
        this.drawerForm.certificationList = item.attachmentInfoList?.map((val) => {
          return {
            name: val.name,
            url: val.url
          }
        })
        this.drawerForm.certificationList && this.drawerForm.certificationList.length
          ? ''
          : (this.drawerForm.certificationList = Array<CertificationUrl>())
      }
    }

    // 新增证书
    addCertifition() {
      this.drawerFormRef.validate((val: boolean) => {
        if (val) {
          // this.drawerForm.industryId = this.jsIndustryPropertyId
          if (this.drawerTitle == '新增证书') {
            this.$emit('confirmAddCertifition', this.drawerForm)
          } else {
            // 修改证书
            this.$emit('confirmUpdateCertifition', this.drawerForm)
          }
          this.uiStatus.isOpenCertificationDrawer = false
        }
      })
    }

    // 取消新增证书
    cancelAddCertifition() {
      this.uiStatus.isOpenCertificationDrawer = false
      this.drawerForm = new DrawerForm()
      this.drawerForm.certificationList = new Array<CertificationUrl>()
    }

    // 判断培训类别下是否有专业
    async queryTrainingMajor(params: MajorParam) {
      const trainingMajorOptions = await QueryPersonIndustry.getIndustryDetail(params.parentPropertyId)
      this.trainingMajorList = cloneDeep(trainingMajorOptions)
      return trainingMajorOptions?.length > 0
    }

    // 获取建设行业属性id
    async queryConstructPropertyId() {
      const res = await QueryIndustry.queryIndustry()
      const industryOptions = res.isSuccess() ? QueryIndustry.industryList : ([] as IndustryVo[])
      const obj = industryOptions?.find((el: IndustryVo) => {
        return el.name === '建设行业'
      })
      this.jsIndustryPropertyId = obj?.propertyId || ''
      this.industryId = obj?.id || ''
    }

    // 清空有效期
    @Watch('drawerForm.certificationStartTime', { deep: true })
    clearEndDate(newVal: any, oldVal: any) {
      // 没有初始值不做清除
      if (!oldVal) return
      if (newVal != oldVal) {
        this.drawerForm.certificationEndTime = null
      }
    }
  }
</script>
