/**
 * 查询栏目
 */
import TypeEnum from '@api/service/management/online-school-config/column/query/enum/TypeEnum'
import Column from '@api/service/customer/online-school-config/column/query/vo/Column'
import MsBasicdataQueryFrontGatewayBasicDataQueryForestage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

/**
 * 查询栏目信息
 */
class QueryColumn {
  treeList: Array<Column> = new Array<Column>()
  list: Array<Column> = new Array<Column>()

  /**
   * 查询列表数据
   */
  async queryList(): Promise<Array<Column>> {
    if (this.list.length > 0) {
      return this.treeList
    }
    const { data, status } = await MsBasicdataQueryFrontGatewayBasicDataQueryForestage.getMenusByPortalType(1)
    if (status.isSuccess()) {
      const list: Array<Column> = new Array<Column>()
      this.list = data.menuInfos.map(Column.from)
      data.menuInfos.forEach(menuInfo => {
        if (menuInfo.parentId === '-1' || menuInfo.parentId === null) {
          const column = Column.from(menuInfo)
          column.children = data.menuInfos.filter(menu => menu.parentId === column.id).map(Column.from)
          list.push(column)
        }
      })
      list.sort((a, b) => {
        return a.sort - b.sort
      })
      this.treeList = list
      return list
    }
  }

  private findCategory(id: string): Column {
    const category = this.list.find(category => category.id === id)
    if (category && (category.parentId === '-1' || category.parentId === null)) {
      return category
    }
    return this.findCategory(category.parentId)
  }

  get getTopColumnById() {
    return (categoryId: string) => {
      return this.findCategory(categoryId)
    }
  }

  /**
   * 根据关联 id 查询栏目
   */
  get getTopColumnByReferenceId() {
    return (id: string) => {
      const category = this.list.find(category => category.referenceId === id)
      if (category) {
        return this.findCategory(category.id)
      }
      return undefined
    }
  }

  get queryByCode() {
    return (code: string) => {
      return this.treeList.find(column => column.code === code)
    }
  }

  get informationColumnList() {
    return this.treeList.filter(column => column.type === TypeEnum.information)
  }

  get menuColumnList() {
    return this.treeList.filter(column => column.type === TypeEnum.menu)
  }
}

export default new QueryColumn()
