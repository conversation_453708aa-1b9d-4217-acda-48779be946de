<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="智能学习编排结果" name="first">
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训方案">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="姓名">
                    <el-input v-model="input" clearable placeholder="请输入学员姓名" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="证件号">
                    <el-input v-model="input" clearable placeholder="请输入学员证件号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="手机号">
                    <el-input v-model="input" clearable placeholder="请输入手机号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="导入任务">
                    <el-input v-model="input" clearable placeholder="请选择" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="开通状态">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="智能选课/学习编排状态">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="导入时间">
                    <el-date-picker
                      v-model="form.date1"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="起始时间"
                      end-placeholder="结束时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="学员帐号信息" min-width="240" fixed="left">
                <template>
                  <p>姓名：张依依</p>
                  <p>证件号：354875965412365896</p>
                  <!--              <p>手机号：15847412365</p>-->
                </template>
              </el-table-column>
              <el-table-column label="培训方案名称" min-width="300">
                <template>方案名称方案名称方案名称方案名称方案名称方案名称</template>
              </el-table-column>
              <el-table-column label="订单号" min-width="220">
                <template>2112071509467489926</template>
              </el-table-column>
              <el-table-column label="班级开通状态" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 2">
                    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                      <el-badge is-dot type="danger" class="badge-status">开通失败</el-badge>
                      <div slot="content">失败原因：这里读取失败原因</div>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">已开通</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="智能选课/学习编排状态" min-width="180">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 2">
                    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                      <el-badge is-dot type="danger" class="badge-status">开通失败</el-badge>
                      <div slot="content">失败原因：这里读取失败原因</div>
                    </el-tooltip>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">已开通</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="创建时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column min-width="240" align="center">
                <template slot="header"
                  >是否更新密码/基础信息<el-tooltip
                    class="item"
                    effect="dark"
                    placement="top"
                    popper-class="m-tooltip is-small"
                  >
                    <span class="el-icon-warning f-co f-ml5 f-f16"></span>
                    <div slot="content">
                      仅针对平台已存在的用户去标记，本次的导入是否<br />更新密码、是否更新基础信息。如果是新用户则对<br />应的说明项显示
                      “-”。
                    </div>
                  </el-tooltip></template
                >
                <template>是/否</template>
              </el-table-column>
              <el-table-column label="完成时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="智能学习执行情况" name="second">
        详见 5003_智能学习_智能学习任务跟踪_智能学习执行情况.vue
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
