<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button @click="$router.go(-1)" type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/scheme/manage' }">培训方案管理</el-breadcrumb-item>
      <el-breadcrumb-item>实施管理</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="m-tab-center">
      <div class="content">
        <template
          v-if="$hasPermission('preTrainingImplementation') && isShowPreTrainingImplementation"
          desc="训前实施设置"
          actions="created,@PreTrainingImplementation"
        >
          <div
            class="item"
            :class="{ 'z-cur': activeTabName == 'preTrainingImplementation' }"
            @click="changeActiveTab('preTrainingImplementation')"
          >
            训前实施设置
          </div>
        </template>

        <template v-if="$hasPermission('trainingProcess')" desc="培训过程管理" actions="created,@TrainingProcess">
          <div
            class="item"
            :class="{ 'z-cur': activeTabName == 'trainingProcess' }"
            @click="changeActiveTab('trainingProcess')"
          >
            培训过程管理
          </div>
        </template>

        <template v-if="$hasPermission('trainingResults')" desc="培训成果管理" actions="created,@TrainingResults">
          <div
            class="item"
            :class="{ 'z-cur': activeTabName == 'trainingResults' }"
            @click="changeActiveTab('trainingResults')"
          >
            培训成果管理
          </div>
        </template>
      </div>
    </div>
    <PreTrainingImplementation
      v-if="activeTabName == 'preTrainingImplementation'"
      :schemeId="schemeId"
    ></PreTrainingImplementation>
    <!--<PreTrainingImplementation-->
    <!--  v-if="isShowPreTrainingImplementation"-->
    <!--&gt;</PreTrainingImplementation>-->
    <TrainingProcess
      :schemeId="schemeId"
      :trainingMode="trainingMode"
      v-if="activeTabName == 'trainingProcess'"
    ></TrainingProcess>
    <TrainingResults v-if="activeTabName == 'trainingResults'" :schemeId="schemeId"></TrainingResults>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import PreTrainingImplementation from './preTrainingImplementation.vue'
  import TrainingProcess from './trainingProcess.vue'
  import TrainingResults from './trainingResults.vue'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import PeriodImplementBase from '@api/service/management/implement/models/PeriodImplementBase'

  @Component({
    components: {
      PreTrainingImplementation,
      TrainingProcess,
      TrainingResults
    }
  })
  export default class extends Vue {
    // 激活项
    activeTabName = ''
    // 培训形式枚举复制
    TrainingModeEnum = TrainingModeEnum
    // 培训形式
    trainingMode = ''
    // 方案id
    schemeId = ''

    changeActiveTab(activeTab: string) {
      this.activeTabName = activeTab
    }

    // 是否显示训前实施
    get isShowPreTrainingImplementation() {
      return this.trainingMode === TrainingModeEnum.mixed || this.trainingMode === TrainingModeEnum.offline
    }

    async created() {
      this.schemeId = this.$route.params.schemeId
      const res = await PeriodImplementBase.getSchemeType(this.$route.params.schemeId)
      this.trainingMode = res.value
      if (!this.isShowPreTrainingImplementation) {
        this.activeTabName = 'trainingProcess'
      } else {
        this.activeTabName = 'preTrainingImplementation'
      }
    }
  }
</script>
