import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformWorkTypeCategory'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformWorkTypeCategory'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举

// 类

export class WorkTypeCategoryCreateDTO {
  name: string
  parentId?: string
  sort: number
}

export class WorkTypeCategoryUpdateDTO {
  id: string
  name: string
  parentId?: string
  sort: number
}

/**
 * 工种分类
<AUTHOR>
 */
export class WorkTypeCategoryResponse {
  id: string
  name: string
  parentId: string
  sort: number
  creatorId: string
  createTime: string
  lastUpdateTime: string
}

export class WorkTypeRelationCategoryResponse {
  workTypeId: string
  categoryList: Array<WorkTypeCategoryResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取所有工种分类
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllCategoryList(
    query: DocumentNode = GraphqlImporter.getAllCategoryList,
    operation?: string
  ): Promise<Response<Array<WorkTypeCategoryResponse>>> {
    return commonRequestApi<Array<WorkTypeCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取所有第一级分类
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllTopCategoryList(
    query: DocumentNode = GraphqlImporter.getAllTopCategoryList,
    operation?: string
  ): Promise<Response<Array<WorkTypeCategoryResponse>>> {
    return commonRequestApi<Array<WorkTypeCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取指定工种分类以及父类
   * @param level 递归到第几级,<=0则一直取到没有父类为止
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCategoryAndParents(
    params: { id?: string; level: number },
    query: DocumentNode = GraphqlImporter.getCategoryAndParents,
    operation?: string
  ): Promise<Response<Array<WorkTypeCategoryResponse>>> {
    return commonRequestApi<Array<WorkTypeCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据工种id集合获取每个工种id对应所属的工种分类信息
   * @param containsParent是否取出对应分类的父分类
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCategoryListByWorkTypeId(
    params: { workTypeId?: string; containsParent: boolean },
    query: DocumentNode = GraphqlImporter.getCategoryListByWorkTypeId,
    operation?: string
  ): Promise<Response<Array<WorkTypeCategoryResponse>>> {
    return commonRequestApi<Array<WorkTypeCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据工种id集合获取每个工种id对应所属的工种分类信息
   * @param containsParent是否取出对应分类的父分类
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCategoryListByWorkTypeIds(
    params: { workTypeIds?: Array<string>; containsParent: boolean },
    query: DocumentNode = GraphqlImporter.getCategoryListByWorkTypeIds,
    operation?: string
  ): Promise<Response<Array<WorkTypeRelationCategoryResponse>>> {
    return commonRequestApi<Array<WorkTypeRelationCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取指定工种分类的所有子分类
   * @param level 递归到第几级,<=0则一直取到没有子类为止
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getChildren(
    params: { id?: string; level: number },
    query: DocumentNode = GraphqlImporter.getChildren,
    operation?: string
  ): Promise<Response<Array<WorkTypeCategoryResponse>>> {
    return commonRequestApi<Array<WorkTypeCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 通过Id获取工种分类信息
   * @param query 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getWorkTypeCategoryList(
    idList: Array<string>,
    query: DocumentNode = GraphqlImporter.getWorkTypeCategoryList,
    operation?: string
  ): Promise<Response<Array<WorkTypeCategoryResponse>>> {
    return commonRequestApi<Array<WorkTypeCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { idList },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建工种分类
   * @param mutate 查询 graphql 语法文档
   * @param createDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createWorkTypeCategory(
    createDTO: WorkTypeCategoryCreateDTO,
    mutate: DocumentNode = GraphqlImporter.createWorkTypeCategory,
    operation?: string
  ): Promise<Response<WorkTypeCategoryResponse>> {
    return commonRequestApi<WorkTypeCategoryResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 工种分类下移指定偏移量
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async moveDownCategory(
    params: { id?: string; offset: number },
    mutate: DocumentNode = GraphqlImporter.moveDownCategory,
    operation?: string
  ): Promise<Response<WorkTypeCategoryResponse>> {
    return commonRequestApi<WorkTypeCategoryResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 工种分类上移指定偏移量
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async moveUpCategory(
    params: { id?: string; offset: number },
    mutate: DocumentNode = GraphqlImporter.moveUpCategory,
    operation?: string
  ): Promise<Response<WorkTypeCategoryResponse>> {
    return commonRequestApi<WorkTypeCategoryResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeWorkTypeCategory(
    id: string,
    mutate: DocumentNode = GraphqlImporter.removeWorkTypeCategory,
    operation?: string
  ): Promise<Response<WorkTypeCategoryResponse>> {
    return commonRequestApi<WorkTypeCategoryResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新工种分类信息
   * @param mutate 查询 graphql 语法文档
   * @param updateDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateWorkTypeCategory(
    updateDTO: WorkTypeCategoryUpdateDTO,
    mutate: DocumentNode = GraphqlImporter.updateWorkTypeCategory,
    operation?: string
  ): Promise<Response<WorkTypeCategoryResponse>> {
    return commonRequestApi<WorkTypeCategoryResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新工种分类与工种的关系
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateWorkTypeCategoryRelations(
    params: { categoryId?: string; workTypeIds?: Array<string> },
    mutate: DocumentNode = GraphqlImporter.updateWorkTypeCategoryRelations,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
