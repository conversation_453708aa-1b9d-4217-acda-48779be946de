import { AutoLearningExecuteLogResponse } from '@api/ms-gateway/ms-autolearning-log-v1'
import ExecutionStatusType from '@api/service/management/intelligence-learning/enum/ExecutionStatusEnum'

export default class ExecutionLogItem {
  /**
   * 日志id
   */
  logId = ''
  /**
   * 任务id
   */
  taskId = ''
  /**
   * 执行状态更新时间
   */
  updateTime = ''
  /**
   * 执行状态更新原因
   */
  updateReason = ''
  /**
   * 执行状态
   */
  status: ExecutionStatusType = null
  /**
   * 智能学习操作类型
   */
  operationType = ''

  static from(dto: AutoLearningExecuteLogResponse) {
    const vo = new ExecutionLogItem()
    vo.logId = dto.logId
    vo.taskId = dto.mainTaskId
    vo.updateTime = dto.createTime
    vo.updateReason = dto.message
    vo.status = new ExecutionStatusType(dto.status)
    switch (dto.type) {
      case 0:
        vo.operationType = '导入智能学习'
        break
      case 1:
        vo.operationType = `重启智能学习`
        break
    }
    return vo
  }
}
