<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div class="f-plr20">
      <!-- 创建/复制 -->
      <template v-if="isCreateMode">
        <edit-choose-course-outline
          ref="editChooseCourseOutlineRef"
          :outlineTree.sync="courseLearningInfo.classification"
          :init-mode="1"
          v-if="uiSchemeType === 1"
          :enable-edit="enableCourseLearning"
          :learningExperience="learningExperience"
          @updateLearningRequire="updateLearningRequire"
          @towElectiveRequire="towElectiveRequire"
          @deleteTowElectiveRequire="deleteTowElectiveRequire"
        />
      </template>
      <!-- 修改 -->
      <template v-if="isModifyMode">
        <modify-choose-course-outline
          ref="modifyChooseCourseOutlineRef"
          :trainSchemeDetail.sync="schemeDetail"
          :outlineTree.sync="courseLearningInfo.classification"
          :init-mode="1"
          v-if="uiSchemeType === 1"
          :enable-edit="enableCourseLearning"
          :learningExperience="learningExperience"
          @updateLearningRequire="updateLearningRequire"
          @towElectiveRequire="towElectiveRequire"
          @deleteTowElectiveRequire="deleteTowElectiveRequire"
        />
      </template>
      <template v-if="isAutoCreateMode">
        <edit-auto-choose-outline
          ref="editAutoChooseOutlineRef"
          :outlineTree.sync="courseLearningInfo.classification"
          :init-mode="1"
          v-if="uiSchemeType === 2"
          :enable-edit="enableCourseLearning"
          :learningExperience="learningExperience"
          @updateLearningRequire="updateLearningRequire"
      /></template>
      <template v-if="isAutoModifyMode">
        <modify-auto-choose-outline
          ref="modifyAutoChooseOutlineRef"
          :outlineTree.sync="courseLearningInfo.classification"
          :trainSchemeDetail.sync="schemeDetail"
          :init-mode="1"
          v-if="uiSchemeType === 2"
          :enable-edit="enableCourseLearning"
          :learningExperience="learningExperience"
          @updateLearningRequire="updateLearningRequire"
      /></template>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="m-tit is-small bg-gray is-border-bottom f-align-center">
          <span class="tit-txt">课程测验</span>
          <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
            <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
            <div slot="content">
              课程测验的组卷范围为已选择课程关联的试题。请先添加课程后，再添加测验。课程测验可选配。
            </div>
          </el-tooltip>
        </div>
        <div class="f-p20">
          <el-button
            type="primary"
            icon="el-icon-plus"
            class="f-mb15"
            @click="addCourseQuiz"
            :disabled="Boolean(courseQuizList.length)"
          >
            添加课程测验
          </el-button>
          <el-table stripe :data="courseQuizList" max-height="500px" class="m-table">
            <el-table-column width="30" align="center" fixed="left">
              <!--              <template slot-scope="scope">
                &lt;!&ndash;有新增或者修改的添加 <i class="is-tag"></i>&ndash;&gt;
                <div v-if="scope.$index === 0"><i class="is-tag"></i></div>
                <div v-else></div>
              </template>-->
            </el-table-column>
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="总分/及格分" min-width="140" align="center" fixed="left">
              <template slot-scope="scope">100 / {{ scope.row.quizPassScore }}</template>
            </el-table-column>
            <el-table-column label="组卷方式" min-width="120" align="center">
              <template>智能组卷</template>
            </el-table-column>
            <el-table-column :label="radioValue === 0 ? '测验题数' : '每学时题数'" min-width="120" align="center">
              <template slot-scope="scope">{{ scope.row.questionNum }}</template>
            </el-table-column>
            <el-table-column label="作答次数" min-width="120" align="center">
              <template>不限次</template>
            </el-table-column>
            <el-table-column label="操作" width="120" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" size="mini" @click="editQuiz(scope.row)">编辑</el-button>
                <el-popconfirm
                  title="移除课程测验后需重新添加，确认要移除？"
                  icon="el-icon-info"
                  icon-color="red"
                  confirm-button-text="确定移除"
                  cancel-button-text="取消"
                  @confirm="removeQuiz(scope.$index)"
                >
                  <el-button type="text" size="mini" slot="reference">移除</el-button>
                </el-popconfirm>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="m-tit is-small bg-gray is-border-bottom">
          <span class="tit-txt">学习要求</span>
        </div>
        <div class="f-p20">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form
                ref="learningRequireFormRef"
                :model="courseLearningInfo"
                :rules="rules"
                label-width="150px"
                class="m-form f-mt10"
              >
                <el-form-item
                  label="课程学习整体要求："
                  :required="true"
                  v-show="uiSchemeType == 2"
                  prop="requirePeriod"
                  :rules="
                    requirePeriodTotal
                      ? [
                          {
                            type: 'number',
                            validator: (rules, value, callback) => {
                              return validatePeriod(rules, value, callback, requirePeriodTotal)
                            },
                            trigger: ['blur', 'change']
                          }
                        ]
                      : [{ require: true, type: 'number', validator: validatePeriod, trigger: ['blur', 'change'] }]
                  "
                >
                  需 ≥
                  <el-input-number
                    v-model.number="courseLearningInfo.requirePeriod"
                    :disabled="!courseLearningInfo.isSelected"
                    size="small"
                    :min="0"
                    class="f-mlr5"
                  />
                  学时，必学课程
                  {{ compulsoryCoursePeriodTotal }} 学时。
                  <p v-show="isTopLevelWeighty">
                    其中
                    <span v-for="(item, index) in topLevelPeriodInfoList" :key="index">
                      {{ item.name }}<i class="f-cr">{{ item.requirePeriod }}</i
                      >学时（必学课程<i class="f-cr">{{ item.compulsoryCoursePeriodTotal }}</i> 学时）<span
                        v-if="index < topLevelPeriodInfoList.length - 1"
                        >、</span
                      >
                    </span>
                  </p>
                </el-form-item>
                <el-form-item
                  label="选修课选修要求："
                  :required="canEditElectiveRequirePeriod"
                  v-show="uiSchemeType == 1"
                >
                  <el-input-number
                    v-model.number="courseLearningInfo.electiveRequirePeriod"
                    :disabled="!canEditElectiveRequirePeriod || isModifyMode"
                    size="small"
                    :min="0"
                    @change="handleElectiveRequirePeriod"
                  />
                  <span class="f-ml10">学时</span>
                  <div class="f-mt10 bg-gray f-plr20 f-ptb10" v-if="isTowElectiveRequire">
                    <el-checkbox
                      v-model="TowElectiveListShow"
                      @change="onChangeTowElectiveRequire"
                      :disabled="isModifyMode"
                      >二级分类存在选课要求</el-checkbox
                    >
                    <div class="f-pb10" v-if="TowElectiveListShow">
                      <div
                        class="f-mt10"
                        v-for="item in courseLearningInfo.chooseCourseRule.secondElectiveMaxPeriod"
                        :key="item.id"
                      >
                        <i class="f-cb">{{ outTowElectiveName(item.id) }}</i
                        ><i class="f-mr10">选课要求</i>
                        <el-input-number
                          v-model="item.electiveMaxPeriod"
                          :disabled="!canEditElectiveRequirePeriod || isModifyMode"
                          size="small"
                          :min="0"
                        /><span class="f-ml10">学时</span>
                      </div>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item label="课程测验纳入考核：" :required="hasConfigureCourseQuiz">
                  <el-radio-group
                    v-model="courseLearningInfo.courseQuizPagerStandard"
                    :disabled="!hasConfigureCourseQuiz"
                  >
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="开放课程评价：" :required="hasConfigureCourse">
                  <el-radio-group
                    v-model="courseLearningInfo.enableAppraisal"
                    :disabled="!hasConfigureCourse"
                    @change="handleEnableAppraisalChange"
                  >
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="评价条件："
                  v-show="courseLearningInfo.enableAppraisal"
                  :required="courseLearningInfo.enableAppraisal"
                >
                  每门课程学习进度达
                  <el-input
                    v-model.number="courseLearningInfo.preconditionCourseSchedule"
                    size="small"
                    class="input-num f-mlr5"
                  />
                  %可以进行课程评价
                </el-form-item>
                <el-form-item label="是否强制学员评价：" v-show="courseLearningInfo.enableAppraisal">
                  <el-radio-group v-model="courseLearningInfo.enableCompulsoryAppraisal">
                    <el-radio :label="true">是</el-radio>
                    <el-radio :label="false">否</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="考核要求：">
                  <!--选课规则-->
                  <p v-if="uiSchemeType == 1">
                    1. 必修 <i class="f-cr">{{ compulsoryTotalPeriod }}</i> 学时， 选修
                    <i class="f-cr">{{ courseLearningInfo.electiveRequirePeriod }}</i> 学时，学习进度 100%
                  </p>
                  <!--自主选课-->
                  <p v-if="uiSchemeType == 2">
                    1. 课程学习整体要求≥ <i class="f-cr">{{ courseLearningInfo.requirePeriod }}</i> 学时，学习进度 100%
                  </p>
                  <p v-show="hasConfigureCourseQuiz">
                    2. <span v-show="courseLearningInfo.courseQuizPagerStandard">课程测验纳入考核</span
                    ><span v-show="!courseLearningInfo.courseQuizPagerStandard">课程测验不纳入考核</span
                    >，每门课程学习进度达
                    <i class="f-cr">{{ courseLearningInfo.quizConfigModel.minCourseSchedule }}%</i>
                    可参加，测验及格分
                    <i class="f-cr">{{ courseLearningInfo.quizConfigModel.passScore }}</i>
                    分，次数不限次。
                  </p>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
    <!--发布/编辑课程测验-->
    <el-drawer
      :title="currentQuizTitle"
      :visible.sync="uiConfig.dialog.quizPublishVisible"
      size="800px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <el-form label-width="auto" class="m-form f-mt10 f-mlr20">
          <el-form-item label="参加测验要求：">
            每门课程学习进度到达
            <el-input v-model="currentCourseLearningSchedule" size="small" class="input-num f-mlr5" /> %
          </el-form-item>
          <el-form-item label="测验组卷试题范围：">按课程ID出题</el-form-item>
          <el-form-item label="试卷总分：">100 分</el-form-item>
          <el-form-item label="测验试题数量配置：">
            <el-radio-group v-model="radioValue">
              <el-radio :label="0"> 固定题数 </el-radio>
              <el-radio :label="1"
                >按课程学时数计算
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                  <div slot="content">
                    支持根据课程学时配置课程所需的测验答题数量，例：课程学时为10学时，课程总测验数量=每学时测验试题数量*10。
                    注：该配置不适用智能学习场景。
                  </div>
                </el-tooltip>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="测验试题数量：" v-if="radioValue == 0">
            <el-input-number v-model="currentQuestionNum" :precision="0" size="small" :min="0"></el-input-number>
            <!-- <span class="f-ml10">题</span> -->
            <div class="f-co el-upload__tip">
              说明：测验关联的课程若试题数达不到要求，则抽取关联课程所有的试题。试题每题分值按照实际抽取题数平均分。
            </div>
          </el-form-item>
          <el-form-item label="测验试题数量：" v-if="radioValue == 1">
            <!-- <el-input-number v-model="currentQuestionNum" size="small" :min="0"></el-input-number> -->
            每学时 <el-input v-model="currentQuestionNum" size="small" class="input-num f-mlr5" />
            <span class="f-ml10">题</span>
            <div class="f-co el-upload__tip">
              说明：测验关联的课程若试题数达不到要求，则抽取关联课程所有的试题。试题每题分值按照实际抽取题数平均分。
            </div>
          </el-form-item>
          <el-form-item label="每道测验题答题时长：" v-if="radioValue === 1">
            <el-input-number v-model="testTimeNum" :precision="0" size="small" :min="0"></el-input-number>
            <span class="f-ml10">分钟</span>
          </el-form-item>
          <el-form-item label="试题每题分值：" v-if="radioValue == 1"
            >按实际抽题题数计算，题数x每题分值=100分</el-form-item
          >
          <el-form-item label="是否提供答题解析：">
            <el-radio-group v-model="analysisValue">
              <el-radio :label="1"> 是 </el-radio>
              <el-radio :label="2">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="测验及格分：">
            <el-input v-model="currentPassScore" size="small" class="input-num f-mr5" /> 分
          </el-form-item>
          <el-form-item label="作答次数：">不限次，合格后不能作答</el-form-item>
          <el-form-item class="m-btn-bar">
            <el-button @click="cancelPublicQuiz">取消</el-button>
            <el-button type="primary" @click="confirmPublishQuiz">确定</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, PropSync, Prop, Watch, Ref } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import ChooseCoursePackage from '@hbfe/jxjy-admin-scheme/src/components/functional-components/choose-course-package.vue'
  import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'
  import EditChooseCourseOutline from '@hbfe/jxjy-admin-scheme/src/components/functional-components/edit-choose-course-outline.vue'
  import ModifyChooseCourseOutline from '@hbfe/jxjy-admin-scheme/src/components/functional-components/modify-choose-course-outline.vue'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import { CreateSchemeUtils, TopLevelOutlinePeriod } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import QuizConfig from '@api/service/management/train-class/mutation/vo/QuizConfig'
  import CreateSchemeUIModule from '@/store/modules-ui/scheme/CreateSchemeUIModule'
  import EditAutoChooseOutline from '@hbfe/jxjy-admin-scheme/src/components/functional-components/edit-auto-choose-outline.vue'
  import ModifyAutoChooseOutline from '@hbfe/jxjy-admin-scheme/src/components/functional-components/modify-auto-choose-outline.vue'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import DataResolve from '@api/service/common/utils/DataResolve'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
  import LearningExperience from '@api/service/management/train-class/mutation/vo/LearningExperience'
  import StudyNotes from '@hbfe/jxjy-admin-scheme/src/components/study-notes.vue'

  class CourseQuizDetail {
    // 测验题数
    questionNum: number
    // 测验及格分
    quizPassScore: number
    // 测验最低要求课程进度
    minCourseSchedule: number
    // 是否提供答题解析
    analysisValue: number
    // 测验试题数量配置
    radioValue: number
    // 每道题测试时长
    testTimeNum: number
  }

  @Component({
    components: {
      StudyNotes,
      EditAutoChooseOutline,
      ChooseCoursePackage,
      EditChooseCourseOutline,
      ModifyChooseCourseOutline,
      ModifyAutoChooseOutline
    }
  })
  export default class extends Vue {
    /**
     * 课程学习配置 - 双向绑定
     */
    @PropSync('courseLearning', { type: CourseLearningLearningType }) courseLearningInfo!: CourseLearningLearningType

    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassBaseModel }) schemeDetail!: TrainClassBaseModel

    /**
     * 学习心得配置 - 双向绑定
     */
    @Prop({ type: LearningExperience }) learningExperience: LearningExperience

    /**
     * 监听数据判断显示二级分类
     */
    @Prop({
      type: Boolean,
      default: false
    })
    isTowElectiveListShow: boolean
    /**
     * 初始数据
     */
    @Prop({
      type: LearningType,
      default: () => new LearningType()
    })
    learningTypeModelCopy: LearningType
    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number

    // 创建模式列表（课程包下课程读取最新的课程包）
    get isCreateMode() {
      return CreateSchemeUtils.createModeList.includes(this.routerMode)
    }

    // 修改模式列表（课程包下课程读取大纲下的课程）
    get isModifyMode() {
      return CreateSchemeUtils.modifyModeList.includes(this.routerMode)
    }

    // 自主选课创建模式列表（课程包下课程读取最新的课程包）
    get isAutoCreateMode() {
      return [1].includes(this.routerMode)
    }

    // 自主选课修改/复制模式列表（课程包下课程读取大纲下的课程）
    get isAutoModifyMode() {
      return [2, 3].includes(this.routerMode)
    }
    /**
     * 选课规则
     */
    @Ref('editChooseCourseOutlineRef') editChooseCourseOutlineRef: EditChooseCourseOutline
    // 自主选课
    @Ref('editAutoChooseOutlineRef') editAutoChooseOutlineRef: EditAutoChooseOutline
    // 自主选课
    @Ref('modifyAutoChooseOutlineRef') modifyAutoChooseOutlineRef: ModifyAutoChooseOutline
    // 选课规则-编辑
    @Ref('modifyChooseCourseOutlineRef') modifyChooseCourseOutlineRef: ModifyChooseCourseOutline

    // 学习要求
    @Ref('learningRequireFormRef') learningRequireFormRef: any

    /**
     *  ui相关的变量控制
     */
    uiConfig = {
      // 对话框是否展示
      dialog: {
        // 发布课程测验 - 选课规则
        quizPublishVisible: false
      }
    }

    // 课程测验列表
    courseQuizList: Array<CourseQuizDetail> = new Array<CourseQuizDetail>()
    // 课程学习进度
    // courseLearningSchedule = 100
    // 课程学习进度 - 编辑课程测验
    currentCourseLearningSchedule = 0
    // 测验试题数 - 编辑课程测验
    currentQuestionNum = 0
    // 测验及格分 - 编辑课程测验
    currentPassScore = 0
    // 头部标题 - 编辑课程测验
    currentQuizTitle = ''
    // 模式，1-选课模式，2-自主选课
    uiSchemeType = 1
    // 测验试题数量配置 默认选中
    radioValue = 0
    //解析配置 默认选中
    analysisValue = 1
    // 测验试题时长
    testTimeNum = 0
    // 校验规则
    rules = {}
    /**
     * 是否勾选二级选课要求
     */
    TowElectiveListShow = false
    /**
     * 是否勾选“课程学习”
     */
    get enableCourseLearning() {
      return this.courseLearningInfo.isSelected
    }

    /**
     * 【自主选课】一级分类学时信息
     */
    get topLevelPeriodInfo() {
      return (item: Classification) => {
        const info = new TopLevelOutlinePeriod()
        info.requirePeriod = item.assessSetting.requirePeriod || 0
        info.name = item.name || ''
        info.compulsoryCoursePeriodTotal = CreateSchemeUtils.getOutlineCompulsoryCoursePeriod(item)
        return info
      }
    }

    /**
     * 【自主选课】一级分类学时信息列表
     */
    get topLevelPeriodInfoList() {
      const infoList = [] as TopLevelOutlinePeriod[]
      this.courseLearningInfo.classification?.childOutlines?.forEach((el: Classification) => {
        const option: TopLevelOutlinePeriod = this.topLevelPeriodInfo(el)
        infoList.push(option)
      })
      return infoList
    }

    /**
     * 【自主选课】是否展示一级分类相关信息（一级分类数量至少为2）
     */
    get isTopLevelWeighty() {
      return CreateSchemeUtils.isWeightyArray(this.topLevelPeriodInfoList) && this.topLevelPeriodInfoList.length > 1
        ? true
        : false
    }

    /**
     * 【自主选课】必学课程学时
     */
    get compulsoryCoursePeriodTotal() {
      const outlineInfo = this.courseLearningInfo.classification
      if (CreateSchemeUtils.isWeightyArray(outlineInfo.childOutlines)) {
        console.log(this.topLevelPeriodInfoList, 'topLevelPeriodInfoList')

        // 有分类
        const result =
          this.topLevelPeriodInfoList?.reduce((prev, cur) => {
            return CalculatorObj.add(cur.compulsoryCoursePeriodTotal, prev)
          }, 0) || 0
        return result
      } else {
        // 无分类
        return outlineInfo.compulsoryCoursePeriodTotal || 0
      }
    }

    /**
     * 【自主选课】要求学时合计
     */
    get requirePeriodTotal() {
      const outlineInfo = this.courseLearningInfo.classification
      if (CreateSchemeUtils.isWeightyArray(outlineInfo.childOutlines)) {
        // 有分类
        const result =
          this.topLevelPeriodInfoList?.reduce((prev, cur) => {
            return CalculatorObj.add(cur.requirePeriod, prev)
          }, 0) || 0
        return result
      } else {
        // 无分类
        return outlineInfo.assessSetting.requirePeriod || 0
      }
    }
    /**
     * 【自主选课】总学时
     */
    get periodTotal() {
      const outlineInfo = this.courseLearningInfo.classification
      if (CreateSchemeUtils.isWeightyArray(outlineInfo.childOutlines)) {
        const result =
          outlineInfo.childOutlines?.reduce((prev, cur) => {
            return CalculatorObj.add(cur.coursePeriodTotal, prev)
          }, 0) || 0
        return result
      } else {
        return this.courseLearningInfo?.classification?.coursePeriodTotal || 0
      }
    }
    /**
     * 是否有二级分类
     */
    get isTowElectiveRequire() {
      const towElectiveList = this.courseLearningInfo.classification.childOutlines
      let isShow = false
      towElectiveList.map((item) => {
        if (item.childOutlines) {
          if (item.childOutlines.length > 0 && item.category == 2) {
            isShow = true
          }
        }
      })
      return isShow
    }
    /**
     *  课程大纲数 - 包含选修、必修
     */
    get outlineTree() {
      return this.courseLearningInfo.classification.childOutlines
    }

    /**
     * 【选课规则】是否可编辑选修学时 - 学习要求
     */
    get canEditElectiveRequirePeriod() {
      const electiveNode = this.outlineTreeFind(this.outlineTree, (node: Classification) => {
        return node.coursePackageId && node.category === 2
      })
      return electiveNode ? true : false
    }

    /**
     * 是否已添加课程测验（是否可编辑课程测验纳入考核） - 学习要求
     */
    get hasConfigureCourseQuiz() {
      if (!this.hasConfigureCourse) return false
      return this.courseQuizList.length ? true : false
    }

    /**
     * 是否已配置课程（是否可编辑开放课程评价） - 学习要求
     */
    get hasConfigureCourse() {
      if (this.uiSchemeType === 1) {
        // 选课模式
        const hasCourseNode = this.outlineTreeFind(this.outlineTree, (node: Classification) => {
          return node.coursePackageId && node.coursePackageId !== ''
        })
        return hasCourseNode ? true : false
      } else {
        // 自主选课
        const classification = this.courseLearningInfo.classification
        if (CreateSchemeUtils.isWeightyArray(classification.childOutlines)) {
          // 有分类
          const hasCourseNode = this.outlineTreeFind(this.outlineTree, (node: Classification) => {
            return node.coursePackageId && node.coursePackageId !== ''
          })
          return hasCourseNode ? true : false
        } else {
          // 无分类
          return classification.coursePackageId ? true : false
        }
      }
    }

    /**
     * 【选课规则】获取必修总学时
     */
    get compulsoryTotalPeriod() {
      const topCompulsoryNode = this.outlineTreeFind(this.outlineTree, (node: Classification) => {
        return node.category === 1
      })
      this.courseLearningInfo.compulsoryRequirePeriod = topCompulsoryNode?.coursePeriodTotal || 0
      return topCompulsoryNode?.coursePeriodTotal || 0
    }

    /**
     * 培训方案类型，1-选课规则，2-自主选课，默认：0
     */
    get schemeType() {
      return CreateSchemeUIModule.schemeType
    }

    @Watch('schemeType', {
      immediate: true,
      deep: true
    })
    schemeTypeChange(newVal: any, oldVal: any) {
      // console.log('schemeType-course-learn', newVal, oldVal)
      if (newVal) {
        this.uiSchemeType = newVal
      }
      // 手动切换才会触发
      if (newVal && oldVal) {
        this.courseLearningInfo.classification = new Classification()
        this.updateLearningRequire()
      }
    }
    @Watch('isTowElectiveListShow', {
      immediate: true,
      deep: true
    })
    isTowElectiveListShowChange(newVal: any, oldVal: any) {
      if (newVal) {
        this.TowElectiveListShow = true
      }
    }
    /**
     * 点击二级分类选课要求，显示二级分类
     */
    onChangeTowElectiveRequire(val: string) {
      if (!val) {
        this.courseLearningInfo.chooseCourseRule.secondElectiveMaxPeriod = []
        return
      }
      if (val) {
        this.courseLearningInfo.classification.childOutlines.map((item) => {
          if (item.category == 2) {
            item.childOutlines.map((subItem) => {
              const elective = {
                id: subItem.id,
                electiveMaxPeriod: 0
              }
              this.courseLearningInfo.chooseCourseRule.secondElectiveMaxPeriod.push(elective)
            })
          }
        })
      }
    }
    /**
     * 查询符合条件的二级选修课名称
     */
    outTowElectiveName(id: string) {
      let name = ''
      this.courseLearningInfo.classification.childOutlines.map((item) => {
        if (item.category == 2) {
          item.childOutlines.map((subItem) => {
            if (subItem.id == id) {
              name = subItem.name
            }
          })
        }
      })
      return name
    }
    /**
     * 查询符合条件的二级选修课要求学分
     */
    outTowElectiveNum(id: string) {
      let num = 0
      this.courseLearningInfo.classification.childOutlines.map((item) => {
        if (item.category == 2) {
          item.childOutlines.map((subItem) => {
            if (subItem.id == id) {
              num = subItem.coursePeriodTotal
            }
          })
        }
      })
      return num
    }
    /**
     * 查询符合条件的节点 - 课程大纲树通用
     */
    outlineTreeFind(tree: Array<Classification>, func: any) {
      return CreateSchemeUtils.treeFind<Classification>(tree, func, 'childOutlines')
    }

    /**
     * 查找节点路径 - 课程大纲树通用
     */
    outlineTreeFindPath(func: any) {
      return CreateSchemeUtils.treeFindPath<Classification>(this.outlineTree, func, 'id', 'childOutlines')
    }

    /**
     * 页面初始化
     */
    created() {
      if (this.routerMode !== 1) {
        this.getCourseQuizList()
      }
    }

    /**
     * 是否可操作 - 未勾选课程学习不可操作
     */
    canOperate() {
      let result = true
      if (!this.enableCourseLearning) {
        result = false
        this.$message.error('请先勾选“课程学习”的学习内容，再配置课程测验！')
      }
      return result
    }

    /**
     * 初始化课程测验
     */
    getCourseQuizList() {
      this.courseQuizList = new Array<CourseQuizDetail>()
      if (this.courseLearningInfo.configCourseQuiz) {
        const option = new CourseQuizDetail()
        option.questionNum = this.courseLearningInfo.quizConfigModel.questionCount
        option.quizPassScore = this.courseLearningInfo.quizConfigModel.passScore
        option.minCourseSchedule = this.courseLearningInfo.quizConfigModel.minCourseSchedule
        option.analysisValue = this.courseLearningInfo.quizConfigModel.openDissects === false ? 2 : 1
        option.radioValue = this.courseLearningInfo.quizConfigModel.questionCountConfigureType
        this.radioValue = this.courseLearningInfo.quizConfigModel.questionCountConfigureType || 0
        option.testTimeNum = this.courseLearningInfo.quizConfigModel.timeLengthPerQuestion / 60
        console.log(option, '初始化课程测验')
        this.courseQuizList.push(option)
      }
    }

    /**
     * 添加课程测验
     */
    addCourseQuiz() {
      const canOperate = this.canOperate()
      if (!canOperate) return
      if (!this.hasConfigureCourse) {
        this.$message.error('请先添加课程再添加课程测验！')
        return
      }
      this.currentCourseLearningSchedule = 100
      this.currentQuestionNum = 10
      this.currentPassScore = 60
      this.currentQuizTitle = '发布课程测验'
      this.uiConfig.dialog.quizPublishVisible = true
    }

    /**
     * 取消发布 - 课后测验
     */
    cancelPublicQuiz() {
      this.uiConfig.dialog.quizPublishVisible = false
    }

    /**
     * 确认发布 - 课后测验
     */
    confirmPublishQuiz() {
      if (!this.currentCourseLearningSchedule) {
        this.$message.error('请输入课程学习进度')

        return
      }
      let validResult = DataResolve.validNumber(this.currentCourseLearningSchedule)
      if (!validResult) {
        this.$message.error('请输入正整数')
        return
      }
      const schedule = Number(this.currentCourseLearningSchedule)
      if (schedule > 100) {
        this.$message.error('课程学习进度不超过100%')
        return
      }
      if (!this.currentQuestionNum) {
        this.$message.error('请输入测验试题数，不能为空！')
        return
      }
      validResult = DataResolve.validNumber(this.currentQuestionNum)
      if (!validResult) {
        this.$message.error('请输入大于0的试题数')
        return
      }

      const questionNum = Number(this.currentQuestionNum)
      if (!this.currentPassScore) {
        this.$message.error('请输入测验试题数量，不能为空！')
        return
      }
      const questionNumValidResult = DataResolve.validNumber(this.currentPassScore)
      if (!questionNumValidResult) {
        this.$message.error('请输入正整数')
        return
      }
      validResult = DataResolve.validNumber(this.currentPassScore)
      if (!validResult) {
        this.$message.error('请输入正整数')
        return
      }
      const pasScore = Number(this.currentPassScore)
      if (pasScore > 100) {
        this.$message.error('测验及格分不超过100分')
        return
      }
      if (this.radioValue === 1 && !this.testTimeNum) {
        this.$message.error('请输入每道测验题答题时长，不能为空！')
        return
      }
      validResult = DataResolve.validNumber(this.testTimeNum)
      if (this.radioValue === 1 && !validResult) {
        this.$message.error('请输入正整数')
        return
      }

      // 先设置本地值
      this.courseLearningInfo.quizConfigModel.minCourseSchedule = schedule
      // if (this.radioValue === 0) {
      this.courseLearningInfo.quizConfigModel.questionCount = questionNum
      // }
      this.courseLearningInfo.quizConfigModel.totalScore = 100
      this.courseLearningInfo.quizConfigModel.passScore = pasScore
      this.courseLearningInfo.quizConfigModel.questionCountConfigureType = this.radioValue
      if (this.radioValue === 1) {
        this.courseLearningInfo.quizConfigModel.timeLengthPerQuestion = this.testTimeNum * 60
        this.courseLearningInfo.quizConfigModel.questionCountPerPeriod = questionNum
      }
      this.courseLearningInfo.quizConfigModel.openDissects = this.analysisValue === 1 ? true : false
      this.courseQuizList = new Array<CourseQuizDetail>()
      const option = new CourseQuizDetail()
      option.quizPassScore = pasScore
      option.questionNum = questionNum
      option.minCourseSchedule = schedule
      option.analysisValue = this.analysisValue
      option.radioValue = this.radioValue ? this.radioValue : 0
      option.testTimeNum = this.testTimeNum
      this.courseQuizList.push(option)
      this.updateLearningRequire()
      this.uiConfig.dialog.quizPublishVisible = false
    }

    /**
     * 添加成绩正则校验
     */
    validGradesNumber(input: any) {
      const pattern = /^[1-9][0-9]*(\.\d{1})?$/
      const result = pattern.test(input)
      return result
    }

    /**
     * 编辑测验
     */
    editQuiz(row: CourseQuizDetail) {
      console.log(row, '编辑测验')

      const canOperate = this.canOperate()
      if (!canOperate) return
      this.currentCourseLearningSchedule = cloneDeep(parseInt(row.minCourseSchedule + ''))
      this.currentQuestionNum = cloneDeep(row.questionNum)
      this.currentPassScore = cloneDeep(parseInt(row.quizPassScore + ''))
      this.radioValue = row.radioValue ? cloneDeep(row.radioValue) : 0
      this.analysisValue = cloneDeep(row.analysisValue)
      this.testTimeNum = cloneDeep(row.testTimeNum)
      this.currentQuizTitle = '编辑课程测验'
      this.uiConfig.dialog.quizPublishVisible = true
    }

    /**
     * 移除测验
     */
    removeQuiz(index: number) {
      const canOperate = this.canOperate()
      if (!canOperate) return
      this.courseQuizList.splice(index, 1)
      this.updateLearningRequire()
    }

    /**
     * 开启/关闭开放课程评价响应事件
     */
    handleEnableAppraisalChange(value: boolean) {
      if (!value) {
        this.courseLearningInfo.enableCompulsoryAppraisal = false
        this.courseLearningInfo.preconditionCourseSchedule = 0
      }
    }

    /**
     * 更新学习要求总控 - 学习要求
     */
    updateLearningRequire() {
      // 未配置课程测验
      if (!this.hasConfigureCourseQuiz) {
        this.courseLearningInfo.configCourseQuiz = false
        this.courseLearningInfo.courseQuizPagerStandard = false
      } else {
        this.courseLearningInfo.configCourseQuiz = true
      }
      // 未配置选修课，重置选修要求学时
      if (!this.canEditElectiveRequirePeriod) {
        this.courseLearningInfo.electiveRequirePeriod = 0
      }
      // 未配置课程
      if (!this.hasConfigureCourse) {
        // 清空课程测验列表（页面）
        this.courseQuizList = [] as CourseQuizDetail[]
        // 还原课程测验配置
        this.courseLearningInfo.quizConfigModel = new QuizConfig()
        if (this.routerMode === 3) {
          this.courseLearningInfo.quizConfigModel.id =
            this.learningTypeModelCopy.courseLearning.quizConfigModel.id || ''
          this.courseLearningInfo.quizConfigModel.configId =
            this.learningTypeModelCopy.courseLearning.quizConfigModel.configId || ''
        }
        this.courseLearningInfo.courseQuizPagerStandard = false
        // 还原课程评价配置
        this.courseLearningInfo.enableAppraisal = false
        this.courseLearningInfo.enableCompulsoryAppraisal = false
        this.courseLearningInfo.preconditionCourseSchedule = 0
        // 还原必修、选修要求学时
        this.courseLearningInfo.compulsoryRequirePeriod = 0
        this.courseLearningInfo.electiveRequirePeriod = 0
        // 还原要求学时
        this.courseLearningInfo.requirePeriod = 0
        // 还原课程进度要求
        this.courseLearningInfo.courseSchedule = 0
      } else {
        this.courseLearningInfo.compulsoryRequirePeriod =
          this.courseLearningInfo.classification.childOutlines?.find((el: Classification) => {
            return el.category === 1
          })?.coursePeriodTotal || 0
        this.courseLearningInfo.courseSchedule = 100
      }
    }
    /**
     * 更新选修课二级学习要求 - 学习要求
     */
    towElectiveRequire(val: any) {
      // TODO 修改先拦截待调整
      if (
        this.routerMode === 3 &&
        !this.learningTypeModelCopy.courseLearning.chooseCourseRule?.secondElectiveMaxPeriod?.length
      )
        return
      const param = {
        id: val.id,
        electiveMaxPeriod: 0
      }
      this.courseLearningInfo.chooseCourseRule.secondElectiveMaxPeriod.push(param)
    }
    /**
     * 删除选修课二级学习要求 - 学习要求
     */
    deleteTowElectiveRequire(val: any) {
      this.courseLearningInfo.chooseCourseRule.secondElectiveMaxPeriod.splice(
        this.courseLearningInfo.chooseCourseRule.secondElectiveMaxPeriod.findIndex((e) => e.id == val.id),
        1
      )
      // console.log(this.courseLearningInfo.chooseCourseRule.secondElectiveMaxPeriod)
      // console.log('==========================')
    }
    /**
     * 校验学时
     */
    validatePeriod(rules: any, value: any, callback: any, min: number) {
      if (!value) {
        callback(new Error('请输入课程学习整体要求学时'))
      }
      if (min && value < min) {
        callback(new Error('课程学习整体要求学时要大于等于必学课程学时,请调整'))
      }
      if (value > this.periodTotal) {
        callback(new Error('课程学习整体要求学时超出添加的课程总学时，请调整'))
      }
    }

    /**
     * 表单校验
     */
    validateForm() {
      // debugger
      let finalValid = true
      try {
        const courseLearningInfo = this.courseLearningInfo
        //  校验要求学时
        if (this.canEditElectiveRequirePeriod && courseLearningInfo.electiveRequirePeriod == 0) {
          this.$message.error(`请输入选修课选修要求学时`)
          throw new Error('中断函数执行')
        }
        if (courseLearningInfo.enableAppraisal) {
          const validResult = DataResolve.validNumber(courseLearningInfo.preconditionCourseSchedule)
          if (!validResult) {
            this.$message.error('请输入大于0的课程评价每门课程最低学习进度要求')
            throw new Error('中断函数执行')
          }
          const preconditionCourseSchedule = Number(courseLearningInfo.preconditionCourseSchedule)
          if (preconditionCourseSchedule > 100) {
            this.$message.error('课程评价每门课程最低学习进度不能大于100%')
            throw new Error('中断函数执行')
          }
        }
        if (this.schemeType === 1) {
          /** 选课规则 */
          let valid: boolean
          if (this.isCreateMode) {
            valid = this.editChooseCourseOutlineRef.validateForm()
          }
          if (this.isModifyMode) {
            valid = this.modifyChooseCourseOutlineRef.validateForm()
            if (this.learningTypeModelCopy.courseLearning.configId) {
              if (
                Number(this.learningTypeModelCopy.courseLearning.compulsoryRequirePeriod) &&
                Number(this.learningTypeModelCopy.courseLearning.compulsoryRequirePeriod) !==
                  Number(this.courseLearningInfo.compulsoryRequirePeriod)
              ) {
                valid = false
                this.$message.error(
                  `方案的必修学时只能设置为${this.learningTypeModelCopy.courseLearning.compulsoryRequirePeriod}学时，当前不一致，请调整`
                )
                throw new Error('中断函数执行')
              }
            }
          }
          if (!valid) {
            throw new Error('中断函数执行')
          }
          if (courseLearningInfo.electiveRequirePeriod) {
            const electiveCoursePeriodTotal =
              courseLearningInfo.classification.childOutlines?.find((item) => item.category == 2)?.coursePeriodTotal ||
              0
            let towElectivePeriodTotal = 0
            if (
              courseLearningInfo.chooseCourseRule?.secondElectiveMaxPeriod &&
              courseLearningInfo.chooseCourseRule?.secondElectiveMaxPeriod?.length > 0
            ) {
              courseLearningInfo.chooseCourseRule.secondElectiveMaxPeriod.map((item) => {
                towElectivePeriodTotal += item.electiveMaxPeriod
              })
              if (this.TowElectiveListShow && towElectivePeriodTotal != courseLearningInfo.electiveRequirePeriod) {
                this.$message.error('二级分类存在选课要求需等于选修课要求学时，请调整。')
                throw new Error('中断函数执行')
              }
              courseLearningInfo.chooseCourseRule.secondElectiveMaxPeriod.map((item) => {
                const num = this.outTowElectiveNum(item.id)
                if (item.electiveMaxPeriod > num) {
                  this.$message.error(`${this.outTowElectiveName(item.id)}选课要求需不能超过课程包要求学时，请调整。`)
                  throw new Error('中断函数执行')
                }
              })
            }
          }
        }
        if (this.schemeType === 2) {
          console.log(this.periodTotal, 'this.periodTotal')

          /** 自主选课 */

          let valid: boolean
          if (this.isAutoCreateMode) {
            valid = this.editAutoChooseOutlineRef.validateForm()
          }
          if (this.isAutoModifyMode) {
            valid = this.modifyAutoChooseOutlineRef.validateForm()
          }
          if (!valid) {
            throw new Error('中断函数执行')
          }
          //  校验要求学时
          const requirePeriod = courseLearningInfo.requirePeriod
          if (!requirePeriod) {
            this.$message.error('请输入课程学习整体要求学时')
            throw new Error('中断函数执行')
          }
          // console.log('requirePeriodTotal', this.requirePeriodTotal)
          if (this.requirePeriodTotal && requirePeriod < this.requirePeriodTotal) {
            this.$message.error('课程学习整体要求学时要大于等于必学课程学时,请调整')
            throw new Error('中断函数执行')
          }
          if (requirePeriod > this.periodTotal) {
            this.$message.error('课程学习整体要求学时超出添加的课程总学时，请调整')
            throw new Error('中断函数执行')
          }
        }
      } catch (e) {
        console.log('报错了', e)
        finalValid = false
      }
      return finalValid
    }

    /**
     * 选修要求学时输入时响应
     */
    handleElectiveRequirePeriod(val: number) {
      if (!val) {
        this.courseLearningInfo.electiveRequirePeriod = 0
      }
    }

    /**
     * 重置课程学习
     */
    resetCourseLearning() {
      if (this.schemeType === 1) {
        // 选课规则
        // 创建方案模式
        if (this.isCreateMode) {
          this.editChooseCourseOutlineRef.resetClassification()
        }
        // 修改方案模式
        if (this.isModifyMode) {
          this.modifyChooseCourseOutlineRef.resetClassification()
        }
      } else {
        // 自主选课
        if (this.isAutoCreateMode) this.editAutoChooseOutlineRef.resetClassification()
        if (this.isAutoModifyMode) this.modifyAutoChooseOutlineRef.resetClassification()
        this.$nextTick(() => {
          this.learningRequireFormRef.clearValidate('requirePeriod')
        })
      }
      this.courseQuizList = new Array<CourseQuizDetail>()
    }
  }
</script>
