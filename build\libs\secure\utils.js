const fs = require('fs')
const path = require('path')
const config = require('./config')

const util = {
  collectHbfeModuleName: () => {
    return fs.readdirSync(config.hbfeModulePath, { encoding: 'utf-8' })
  },
  collectHbfeUiModuleSecurity: (moduleNames, prefixes) => {
    // 将多个前缀用 | 连接，形成一个匹配任意前缀的正则表达式
    const prefixPattern = prefixes.map(prefix => `^${prefix}-`).join('|')
    // 动态生成正则表达式，排除以 -design 结尾的情况
    const apiReg = new RegExp(`(${prefixPattern})(?!.*-design$)`)
    const apiModulePath = []
    moduleNames.forEach(moduleName => {
      if (apiReg.test(moduleName)) {
        const apiPath = path.join(config.hbfeModulePath, moduleName)
        const securityFilePath = path.join(apiPath, config.apiModuleName)
        apiModulePath.push(securityFilePath)
      }
    })
    return apiModulePath
  }
}

module.exports = util
