<template>
  <div>
    <keep-alive ref="whiteRef" v-if="$route.meta.keepAlive">
      <router-view />
    </keep-alive>
    <!-- 不需要缓存的视图组件 -->
    <router-view v-if="!$route.meta.keepAlive"></router-view>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import RootModule from '@/store/RootModule'

  @Component
  export default class extends Vue {
    get whiteList() {
      return RootModule.keepAliveWhiteList
    }

    @Watch('$route.path')
    RouteChange() {
      const key = this.$vnode.data?.key as string
      if (key) {
        const { route } = this.$router.resolve(key)
        if (!RootModule.routeOnTab(route.path, route.meta.isMenu)) {
          if (this.$vnode.tag) {
            const getKey = this.$vnode.tag?.split('-')[2]
            let id = ''
            const theKey = this.$vnode.key as string
            if (process.env.NODE_ENV === 'development') {
              id = theKey + '::' + getKey
            } else {
              id = theKey + ''
            }
            const parent = this.$options.parent as any

            parent.cache[id]?.$destroy?.()
            parent.keys.splice(
              parent.keys.findIndex((itemId: string) => itemId === id),
              1
            )
            delete parent.cache[id]
          }
        }
      }
    }
  }
</script>
