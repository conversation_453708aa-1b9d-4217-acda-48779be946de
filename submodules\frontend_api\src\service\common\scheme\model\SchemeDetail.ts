import LearningType from '@api/service/common/scheme/model/LearningType'
import CertificateConfig from '@api/service/common/scheme/model/CertificateConfig'
import SchemeBaseInfo from '@api/service/common/scheme/model/SchemeBaseInfo'
import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * @description 方案详情
 */
class SchemeDetail {
  /**
   *可见的购买渠道，1：用户自主购买，2：集体缴费，3：管理员导入4：所有，类型为number[]
   */
  visibleChannelList: number[] = []
  /**
   * 商品抬头
   */
  saleTitle = ''
  /**
   * 商品id
   */
  commoditySkuId = ''
  /**
   * 门户商品id（详情查询）
   */
  portalCommodityId = ''
  /**
   * 分类id
   */
  categoryId = ''
  /**
   * 价格
   */
  price = 0
  /**
   * 是否关闭学员报名
   */
  closeCustomerPurchase = false
  /**
   *上架计划时间
   */
  onShelvesPlanTime = ''
  /**
   * 税务编码
   */
  taxCode = ''
  /**
   *下架计划时间
   */
  offShelvesPlanTime = ''
  /**
   *是否立即上架
   */
  onShelves = true
  /**
   * 培训班基础信息
   */
  trainClassBaseInfo = new SchemeBaseInfo()
  /**
   * 学习方式
   */
  learningType = new LearningType()
  /**
   * 学习方式初始数据，修改用
   */
  learningTypeCopy = new LearningType()
  /**
   * 培训证明配置
   * @description 修改方案时判断证明是否移除
   */
  certificate = new CertificateConfig()

  /**
   * 获取方案类型展示文本
   * @param param.trainingMode 培训形式
   * @param param.schemeType 方案类型
   * @param param.isShowTrainingMode 是否展示培训形式文本，默认true，若为false则不展示培训形式文本
   * @returns 方案类型展示文本
   */
  static getSchemeTypeShowText(param: {
    trainingMode: TrainingModeEnum
    schemeType: number | string
    isShowTrainingMode: boolean
  }): string {
    const { trainingMode, schemeType, isShowTrainingMode } = param
    if (!trainingMode || !schemeType) {
      return ''
    }
    const showTextTmpArr = [] as string[]
    const trainingModeText = TrainingMode.list().find((el) => el.code === trainingMode).desc
    if (isShowTrainingMode) {
      showTextTmpArr.push(trainingModeText)
    }
    let tmpSchemeType = 0
    if (typeof schemeType === 'string') {
      if (schemeType === 'chooseCourseLearning') {
        tmpSchemeType = 1
      }
      if (schemeType === 'autonomousCourseLearning') {
        tmpSchemeType = 2
      }
      if (schemeType === 'trainingCooperation') {
        tmpSchemeType = 3
      }
    }
    if (typeof schemeType === 'number') {
      tmpSchemeType = schemeType
    }
    if (tmpSchemeType === 1 || tmpSchemeType === 2) {
      showTextTmpArr.push('培训班')
      if (trainingMode !== TrainingModeEnum.offline) {
        // 非纯面授时，展示网授选课方式
        if (tmpSchemeType === 1) {
          showTextTmpArr.push('选课规则')
        }
        if (tmpSchemeType === 2) {
          showTextTmpArr.push('自主选课')
        }
      }
    }
    if (tmpSchemeType === 3) {
      showTextTmpArr.push('合作办学')
    }
    return showTextTmpArr.join('-')
  }
}

export default SchemeDetail
