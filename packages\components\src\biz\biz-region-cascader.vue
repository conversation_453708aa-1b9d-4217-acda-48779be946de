<route-meta>
{
"title": "全国地区级联选择器"
}
</route-meta>
<template>
  <el-cascader
    ref="elCascaderRef"
    v-if="show"
    :props="props"
    v-model="selctValue"
    :options="options"
    :placeholder="placeholder"
    :clearable="clearable"
    :style="{ width: '100%' }"
    collapse-tags
    @change="onInput"
    v-bind="$attrs"
    :disabled="disabled"
  ></el-cascader>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Emit, Watch, Ref } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import RegionVo from '@api/service/common/basic-data-dictionary/query/vo/RegionVo'
  import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
  import { ElCascader } from 'element-ui/types/cascader'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  import QueryUserFactory from '@api/service/management/user/QueryUserFactory'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    disabled: boolean

    @Prop({
      default: true
    })
    clearable: boolean

    @Prop({
      default: false
    })
    multiple: boolean

    // 传入的必须是数组
    @Prop({
      type: [Array, String]
    })
    value: string[]

    @Prop({
      default: '请选择地区',
      type: String
    })
    placeholder: string

    /**
     * 省份id、用于过滤省份
     */
    @Prop({
      default: '0',
      type: String
    })
    provinceId: string

    @Prop({
      default: false
    })
    checkStrictly: boolean

    @Ref('elCascaderRef') elCascaderRef: ElCascader

    // 当前省份
    // provinces = new Array<RegionInfo>()
    provinces: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    // 初始内容 无回显的情况 string[]
    // 回显的数据结构参见 https://element.eleme.cn/#/zh-CN/component/cascader
    options = new Array<any>()
    // 当前选中的值
    selctValue: string[] = []
    show = true
    props = {}
    toParent = {
      id: '',
      name: ''
    }
    @Watch('value', {
      deep: true,
      immediate: true
    })
    setValue() {
      this.selctValue = this.value
    }
    @Emit('input')
    onInput(values: any) {
      const id = '/' + values?.join('/')
      this.$emit('toParent', id)
    }

    getName(currentnode: any) {
      const result = [] as string[]
      const getParent = (currentnode: any, result: string[]) => {
        result.push(currentnode.label)
        if (currentnode.parent) {
          getParent(currentnode.parent, result)
        }
      }
      getParent(currentnode, result)
      return result
    }

    async created() {
      this.setProps()
      this.provinces = await QueryBusinessRegion.getCountrywideRegion()
      let serveIds = new Array<string>()
      if (QueryUserFactory.queryManagerDetail.isRegionAdmin) {
        serveIds = QueryBusinessRegion.getRegionAdminArea()
      } else {
        serveIds = await QueryBusinessRegion.getServiceRegionIds()
      }
      this.options = QueryBusinessRegion.filterRegionTree(this.provinces, serveIds)
      // await this.echo()
    }

    setProps() {
      this.props = {
        lazy: false,
        value: 'id',
        label: 'name',
        multiple: this.multiple,
        checkStrictly: this.checkStrictly
        // async lazyLoad(node: any, resolve: (val: any) => {}) {
        //   const { level } = node
        //   if (level < 1) {
        //     return
        //   }
        //   // if (level == 1) {
        //   //   const res = await QueryPhysicalRegion.queryPhysicalCity(node.value)
        //   //   if (res.isSuccess()) {
        //   //     const arr = cloneDeep(QueryPhysicalRegion.cityMap[node.value])
        //   //     const nodes = arr.map(item => Object.assign(cloneDeep(item), { leaf: level > 1 }))
        //   //     resolve(nodes)
        //   //   }
        //   // } else {
        //   //   const res = await QueryPhysicalRegion.queryPhysicalCounty(node.value)
        //   //   if (res.isSuccess()) {
        //   //     const arr = cloneDeep(QueryPhysicalRegion.countyMap[node.value])
        //   //     const nodes = arr.map(item => Object.assign(cloneDeep(item), { leaf: level >= 2 }))
        //   //     resolve(nodes)
        //   //   }
        //   // }
        //   else {
        //     const res = await QueryPhysicalRegion.queryLowerLevelRegion(node.value)
        //     const arr = cloneDeep(QueryPhysicalRegion.countyMap[node.value])
        //     if (!arr.length) {
        //       resolve(undefined)
        //     } else {
        //       // arr.map((el: RegionVo) => {
        //       //   el['leaf'] = level >= 2 ? true : false
        //       // })
        //       resolve(arr)
        //     }
        //   }

        //   //通过调用resolve将子节点数据返回，通知组件数据加载完成
        // }
      }
    }

    /**
     * 数据回显（回显需要把options的路径拼全，具体看源码吧）
     * 这边回显只考虑单选。多选需要另外封装。本项目暂无
     * 原理类似
     */
    // async echo() {
    //   console.log('111')
    //   if (this.value?.length !== 2) {
    //     const dataList = this.treeData(this.provinces)
    //     return (this.options = dataList)
    //   }
    //   this.options = this.provinces
    //   this.show = true
    // }

    /**
     * 地区去掉最末级children为空
     */
    treeData(data: any) {
      for (let i = 0; i < data.length; i++) {
        if (!data[i].children || data[i].children.length < 1) {
          delete data[i].children
        } else {
          this.treeData(data[i].children)
        }
      }
      return data
    }
  }
</script>
