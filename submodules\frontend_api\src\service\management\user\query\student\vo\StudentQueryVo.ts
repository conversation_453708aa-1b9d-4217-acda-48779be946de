/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-05-20 16:50:50
 * @LastEditors: <PERSON><PERSON>ong
 * @LastEditTime: 2022-11-22 09:46:15
 */
import {
  AccountRequest,
  DateScopeRequest,
  PersonAccountSortFieldEnum,
  SortTypeEnum,
  StudentQueryRequest,
  StudentSortRequest,
  StudentUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

import {
  StudentQueryRequest as ExportRequest,
  AccountRequest as ExportAccoutRequest,
  DateScopeRequest as ExportDateScopeRequest,
  StudentSortRequest as ExportStudentSortRequest,
  StudentSortFieldEnum
} from '@api/ms-gateway/ms-data-export-front-gateway-DataExportBackstage'

class StudentQueryVo extends StudentQueryRequest {
  /**
   * 用户id
   */
  userId = ''
  /**
   * 用户名称（模糊）
   */
  userName = ''
  /**
   * 登录账号
   */
  loginAccount = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 人员地区
   */
  regionPathList = new Array<string>()

  /**
   * 工作单位
   */
  companyName = ''
  /**
   * 注册开始时间
   */
  beginTime = ''
  /**
   * 注册结束时间
   */
  endTime = ''
  /**
   * 订单号【给页面用】
   */
  orderNo = ''

  toDto() {
    const params = new StudentQueryRequest()
    params.user = new StudentUserRequest()
    params.user.userIdList = new Array<string>()
    params.user.companyRegionPathList = new Array<string>()
    params.account = new AccountRequest()
    params.account.createTimeScope = new DateScopeRequest()
    params.sortList = new Array<StudentSortRequest>()
    params.sortList[0] = new StudentSortRequest()
    params.sortList[0].sortType = SortTypeEnum.DESC
    params.sortList[0].sortField = PersonAccountSortFieldEnum.createdTime

    params.user.companyName = this.companyName?.trim()
    params.user.userName = this.userName?.trim()
    params.authentication = this.loginAccount?.trim() ? { userName: this.loginAccount?.trim() } : undefined
    if (this.regionPathList?.length) {
      const path = '/' + this.regionPathList?.join('/')
      params.user.companyRegionPathList.push(path)
      params.user.companyRegionPathListMatchType = 1
    } else {
      params.user.companyRegionPathList = []
    }
    if (this.userId) {
      params.user.userIdList = [this.userId]
    }
    params.user.userName = this.userName?.trim()
    params.user.idCard = this.idCard?.trim()
    params.user.phone = this.phone?.trim()
    params.account.createTimeScope.beginTime = this.beginTime
    params.account.createTimeScope.endTime = this.endTime
    return params
  }
  toExport() {
    const params = new ExportRequest()
    params.user = new StudentUserRequest()
    params.user.userIdList = new Array<string>()
    params.user.companyRegionPathList = new Array<string>()
    params.account = new ExportAccoutRequest()
    params.account.createTimeScope = new ExportDateScopeRequest()

    params.user.companyName = this.companyName?.trim()
    params.user.userName = this.userName?.trim()
    if (this.regionPathList?.length) {
      const path = '/' + this.regionPathList?.join('/')
      params.user.companyRegionPathList.push(path)
    } else {
      params.user.companyRegionPathList = []
    }
    if (this.userId) {
      params.user.userIdList = [this.userId]
    }
    params.user.userName = this.userName?.trim()
    params.user.idCard = this.idCard?.trim()
    params.user.phone = this.phone?.trim()
    params.account.createTimeScope.begin = this.beginTime
    params.account.createTimeScope.end = this.endTime
    return params
  }
}

export default StudentQueryVo
