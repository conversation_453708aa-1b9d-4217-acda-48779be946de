import store from '../../../../store'
import { Module as Mod } from 'vuex'
import { Module, VuexModule, getModule, Action, Mutation } from 'vuex-module-decorators'
import PlatformExamGateway, {
  UserWrongQuestionStatisticDTO,
  UserWrongQuestionStatisticParamDTO,
  UserWrongQuestionStatisticSummaryDTO,
  UserWrongQuestionSummaryParamDTO,
  WrongQuestionType
} from '@api/gateway/PlatformExam'
import Response, { ResponseStatus } from '../../../../Response'
import moment from 'moment'
import ChapterTree from '../../../common/syllabus/model/ChapterTree'
import { Role, RoleType } from '../../../../Secure'
import CorrectionPracticePaper from '@api/service/customer/my-question/correction/models/CorrectionPracticePaper'
import CorrectionPracticeAnswer from '@api/service/customer/my-question/correction/models/CorrectionPracticeAnswer'
import SyllabusModule from '@api/service/customer/syllabus/SyllabusModule'
import Chapter from '@api/service/common/models/syllabus/Chapter'

class StateCache {
  constructor(schemeId: string, majorId: string, wrongQuestionType: WrongQuestionType, correctCount: number) {
    this.schemeId = schemeId
    this.majorId = majorId
    this.wrongQuestionType = wrongQuestionType
    this.correctCount = correctCount
  }

  isOk() {
    return this.schemeId && this.majorId && this.wrongQuestionType
  }

  // 方案id
  schemeId: string
  // 专业id
  majorId: string
  // 错题类型
  wrongQuestionType: WrongQuestionType = WrongQuestionType.ALL
  // 作对次数
  correctCount = 0
  // 错题重练卷
  questionPractice: CorrectionPracticePaper = new CorrectionPracticePaper()
  // 答卷列表
  latestCorrectionPracticeAnswer: Array<CorrectionPracticeAnswer> = new Array<CorrectionPracticeAnswer>()
  // 各章节错题统计
  allChapterQuestionStatistic: Array<UserWrongQuestionStatisticDTO> = new Array<UserWrongQuestionStatisticDTO>()
  // 各错题题型错题统计
  allQuestionTypeQuestionStatistic: Array<UserWrongQuestionStatisticDTO> = new Array<UserWrongQuestionStatisticDTO>()
  // 各错题题型错题总量统计（包含已消灭）
  allQuestionTypeQuestionTotalStatistic: Array<UserWrongQuestionStatisticDTO> = new Array<
    UserWrongQuestionStatisticDTO
  >()
  // 各错题类型错题统计
  allQuestionCategoryQuestionStatistic: Array<UserWrongQuestionStatisticDTO> = new Array<
    UserWrongQuestionStatisticDTO
  >()
  // 各错题类型错题总量统计（包含已消灭）
  allQuestionCategoryQuestionTotalStatistic: Array<UserWrongQuestionStatisticDTO> = new Array<
    UserWrongQuestionStatisticDTO
  >()
  // 已答总数、错题总数及错题率统计
  summary: UserWrongQuestionStatisticSummaryDTO = new UserWrongQuestionStatisticSummaryDTO()
  // 已答总数、错题总数及错题率统计
  summary7DaysBefore: UserWrongQuestionStatisticSummaryDTO = new UserWrongQuestionStatisticSummaryDTO()
  // 是否需要重载，在学员触发去作答练习是置为true，在下次取数时先清空数据然后加载新数据
  needReload = false
  // 最新一次加载时间，从apollo配置的超时时间与当前时间判断，在下次取数时清空当前取数数据然后加载新数据
  latestLoadTime: Date = new Date()
}

export class CorrectionQuestionSyllabusTree {
  /**
   * 章节编号，对应标签编号
   */
  id: string
  /**
   * 章节名称，对应标签名称
   */
  name: string
  /**
   * 当前节点关系编号
   * @description 由于同一个标签可能存在在同一个关系中的不同节点位置,
   * 只有节点关系编号才能定位一个章节的位置
   *
   */
  relationId: string
  /**
   * 同一级序号
   */
  sequence: number
  /**
   * 子章节
   */
  children: Array<CorrectionQuestionSyllabusTree> = new Array<CorrectionQuestionSyllabusTree>()

  /**
   * 是否正在作答
   */
  answering: boolean

  /**
   * 是否已答玩
   */
  answered: boolean
  /**
   * 作答id
   */
  answerId: string
  /**
   * 作答记录id
   */
  answerRecordId: string

  /**
   * 试题数
   */
  questionCount: number

  hasChild() {
    return this.children && this.children.length > 0
  }
}

export class CorrectionQuestionTypeItem {
  /**
   * 试题题型 - 按题型分组统计，该字段才生效
   */
  questionType: string
  /**
   * 试题数量
   */
  questionCount: number
  /**
   * 是否正在作答
   */
  answering: boolean

  /**
   * 是否已答玩
   */
  answered: boolean
  /**
   * 作答id
   */
  answerId: string
  /**
   * 作答记录id
   */
  answerRecordId: string
}

export class QuestionTypeDistribution {
  /**
   * 试题题型
   */
  questionType: string
  /**
   * 试题数量
   */
  questionCount: number
  /**
   * 消灭数量
   */
  eliminateCount: number
  /**
   * 占比
   */
  proportion: number
}

export class QuestionCategoryDistribution {
  /**
   * 试题类型
   */
  questionCategory: string
  /**
   * 试题数量
   */
  questionCount: number
  /**
   * 消灭数量
   */
  eliminateCount: number
  /**
   * 占比
   */
  proportion: number
}

export interface IState {
  /**
   * 各学习方式错题重练数据
   */
  schemeCorrectionPracticeListMap: Array<StateCache>
  currentAnswerId: string
}

@Module({
  namespaced: true,
  store,
  name: 'CustomerCorrectionModule',
  dynamic: true
})
class CorrectionModule extends VuexModule implements IState {
  schemeCorrectionPracticeListMap = new Array<StateCache>()
  currentAnswerId = ''

  constructor(module: Mod<ThisType<any>, any>) {
    super(module)
    store.subscribe(mutation => {
      if (mutation.type?.indexOf('ALERT_EXAM_RELOAD') !== -1) {
        this.processSubmitPaperMessage(mutation.payload)
      }
    })
  }

  @Role([RoleType.user])
  @Action
  async init(payload: {
    schemeId: string
    majorId: string
    wrongQuestionType: WrongQuestionType
    correctCount: number
  }) {
    let stateCache = new StateCache(payload.schemeId, payload.majorId, payload.wrongQuestionType, payload.correctCount)

    if (!stateCache.isOk()) {
      return new ResponseStatus(500, '初始化需要方案id和专业id还有错题类型')
    }

    if (
      !this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === payload.schemeId &&
          p.wrongQuestionType === payload.wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === payload.correctCount)
      ) ||
      this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === payload.schemeId &&
          p.wrongQuestionType === payload.wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === payload.correctCount)
      )?.needReload
    ) {
      if (
        !this.schemeCorrectionPracticeListMap.find(
          p =>
            p.schemeId === payload.schemeId &&
            p.wrongQuestionType === payload.wrongQuestionType &&
            (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === payload.correctCount)
        )
      ) {
        stateCache.wrongQuestionType = payload.wrongQuestionType
        stateCache.correctCount = payload.correctCount
        this.setStateCacheToSchemeCorrectionPracticeListMap(stateCache)
      }

      stateCache = new StateCache(payload.schemeId, payload.majorId, payload.wrongQuestionType, payload.correctCount)
      const currentDate = new Date()

      stateCache.wrongQuestionType = payload.wrongQuestionType
      stateCache.correctCount = payload.correctCount

      // 初始化考纲信息
      const status = await SyllabusModule.init()
      if (!status.isSuccess()) {
        return status
      }
      const leafSyllabus: Array<Chapter> = SyllabusModule.getLeafSyllabusByMajorId(payload.majorId)

      // 获取错题重练卷子
      let response: Response<any>
      // let response: Response<any> = await PreExamGateway.getOrCreateCorrectionPracticePaper(payload.schemeId)
      // if (!response.status.isSuccess()) {
      //   return response.status
      // }
      // stateCache.questionPractice = response.data

      // 获取错题重练最新一份答卷
      // response = await PreExamGateway.getPreExamCorrectionPracticeList(stateCache.questionPractice.id)
      // if (!response.status.isSuccess()) {
      //   return response.status
      // }
      // stateCache.latestCorrectionPracticeAnswer = response.data

      const userWrongQuestionParam: UserWrongQuestionStatisticParamDTO = new UserWrongQuestionStatisticParamDTO()
      userWrongQuestionParam.schemeId = payload.schemeId
      userWrongQuestionParam.type = stateCache.wrongQuestionType
      userWrongQuestionParam.correctCountBegin = stateCache.correctCount
      userWrongQuestionParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      // 获取各章节错题统计信息
      response = await PlatformExamGateway.statisticUserWrongQuestionGroupByTag(userWrongQuestionParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allChapterQuestionStatistic = response.data
      // 获取各错题类型错题统计
      response = await PlatformExamGateway.statisticUserWrongQuestionGroupByQuestionType(userWrongQuestionParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionTypeQuestionStatistic = response.data
      // 获取各错题题型错题统计
      response = await PlatformExamGateway.statisticUserWrongQuestionGroupByQuestionCategory(userWrongQuestionParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionCategoryQuestionStatistic = response.data
      const userWrongQuestionTotalParam: UserWrongQuestionStatisticParamDTO = new UserWrongQuestionStatisticParamDTO()
      userWrongQuestionTotalParam.schemeId = payload.schemeId
      userWrongQuestionTotalParam.type = stateCache.wrongQuestionType
      userWrongQuestionTotalParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      userWrongQuestionTotalParam.correctCountBegin = stateCache.correctCount
      // userWrongQuestionTotalParam.state = -1
      // userWrongQuestionTotalParam.beforeDays = 0
      // 获取各错题题型错题总量统计信息
      response = await PlatformExamGateway.statisticUserWrongQuestionGroupByQuestionType(userWrongQuestionTotalParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionTypeQuestionTotalStatistic = response.data
      // 获取各错题类型错题总量统计信息
      response = await PlatformExamGateway.statisticUserWrongQuestionGroupByQuestionCategory(
        userWrongQuestionTotalParam
      )
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionCategoryQuestionTotalStatistic = response.data

      const answerQuestionParam: UserWrongQuestionSummaryParamDTO = new UserWrongQuestionSummaryParamDTO()
      answerQuestionParam.schemeId = payload.schemeId
      answerQuestionParam.type = stateCache.wrongQuestionType
      answerQuestionParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      answerQuestionParam.correctCountBegin = stateCache.correctCount
      // 当前已答总数、错题总数及错题率统计
      response = await PlatformExamGateway.statisticUserWrongQuestionSummary(answerQuestionParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.summary = response.data
      // 7天前已答总数、错题总数及错题率统计
      answerQuestionParam.answerTimeEnd = moment(currentDate)
        .add(-6, 'days')
        .format('YYYY-MM-DD 00:00:00')
      response = await PlatformExamGateway.statisticUserWrongQuestionSummary(answerQuestionParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.summary7DaysBefore = response.data
      this.setStateCacheToSchemeCorrectionPracticeListMap(stateCache)
    }
    // else if ((this.schemeCorrectionPracticeListMap.find(p => p.schemeId === payload.schemeId && p.wrongQuestionType === payload.wrongQuestionType && (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === payload.correctCount))?.latestLoadTime.getTime() || 0) < new Date().getTime() - 10000) {
    //   ctx.commit(mutations.SET_NEED_RELOAD, {
    //     schemeId: payload.schemeId,
    //     learningId: payload.learningId,
    //     majorId: payload.majorId
    //   })
    //   await ctx.dispatch('init', payload)
    // }
    return new ResponseStatus(200)
  }

  @Role([RoleType.user])
  @Action
  async goExamOutlinePractice(payload: {
    schemeId: string
    issueId: string
    wrongQuestionType: WrongQuestionType
    correctCount: number
    chapterId: string
    totalQuestionSize: number
  }) {
    console.log(payload)
    // const init: CorrectionAnswerInitRequest = new CorrectionAnswerInitRequest()
    // const leafSyllabus: Array<Chapter> = SyllabusModule.getLeafSyllabusByChapterId(payload.chapterId)
    // init.examOutlineId = payload.chapterId
    // init.fetchWay = 1
    //
    // const stateCache = this.schemeCorrectionPracticeListMap.find(
    //   p =>
    //     p.schemeId === payload.schemeId &&
    //     p.wrongQuestionType === payload.wrongQuestionType &&
    //     (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === payload.correctCount)
    // )
    //
    // if (!stateCache) {
    //   return new ResponseStatus(500, '请先初始化业务状态层')
    // }
    //
    // init.paperId = stateCache.questionPractice?.id
    // init.totalQuestionSize = payload.totalQuestionSize
    // init.questionType = -1
    // switch (stateCache.wrongQuestionType) {
    //   case WrongQuestionType.ALL:
    //     init.wrongType = -1
    //     init.correctTimesAtLeast = stateCache.correctCount
    //     break
    //   case WrongQuestionType.NEVER_CORRECT_IN_CORRECTION:
    //     init.wrongType = 1
    //     init.correctTimesAtLeast = stateCache.correctCount
    //     break
    //   case WrongQuestionType.CORRECT_IN_CORRECTION:
    //     init.wrongType = 2
    //     init.correctTimesAtLeast = stateCache.correctCount
    //     break
    // }
    // init.tagIds = leafSyllabus.map(leaf => leaf.id)
    // const response = await PreExamGateway.goCorrectionPractice(init)
    // if (!response.status.isSuccess()) {
    //   return response.status
    // }
    // this.setAnswerId(response.data)
    // return response.status
  }

  @Role([RoleType.user])
  @Action
  async goQuestionTypePractice(payload: {
    schemeId: string
    issueId: string
    wrongQuestionType: WrongQuestionType
    correctCount: number
    majorId: string
    questionType: number
    totalQuestionSize: number
  }) {
    console.log(payload)
    // const init: CorrectionAnswerInitRequest = new CorrectionAnswerInitRequest()
    // const leafSyllabus: Array<Chapter> = SyllabusModule.getLeafSyllabusByMajorId(payload.majorId)
    // init.fetchWay = 3
    //
    // const stateCache = this.schemeCorrectionPracticeListMap.find(
    //   p =>
    //     p.schemeId === payload.schemeId &&
    //     p.wrongQuestionType === payload.wrongQuestionType &&
    //     (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === payload.correctCount)
    // )
    //
    // if (!stateCache) {
    //   return new ResponseStatus(500, '请先初始化业务状态层')
    // }
    //
    // init.paperId = stateCache.questionPractice?.id
    // init.totalQuestionSize = payload.totalQuestionSize
    // init.questionType = payload.questionType
    // switch (stateCache.wrongQuestionType) {
    //   case WrongQuestionType.ALL:
    //     init.wrongType = -1
    //     init.correctTimesAtLeast = stateCache.correctCount
    //     break
    //   case WrongQuestionType.NEVER_CORRECT_IN_CORRECTION:
    //     init.wrongType = 1
    //     init.correctTimesAtLeast = stateCache.correctCount
    //     break
    //   case WrongQuestionType.CORRECT_IN_CORRECTION:
    //     init.wrongType = 2
    //     init.correctTimesAtLeast = stateCache.correctCount
    //     break
    // }
    // init.tagIds = leafSyllabus.map(leaf => leaf.id)
    // const response = await PreExamGateway.goCorrectionPractice(init)
    // if (!response.status.isSuccess()) {
    //   return response.status
    // }
    // this.setAnswerId(response.data)
    // return response.status
  }

  @Action
  async processSubmitPaperMessage(payload: { schemeId: string; issueId: string; answersId: string }) {
    console.log('错题重练处理交卷消息')
    this.schemeCorrectionPracticeListMap.forEach((stateCache: StateCache) => {
      if (stateCache?.schemeId === payload.schemeId) {
        console.log('错题重练匹配到' + JSON.stringify(payload))
        this.setNeedReload({
          schemeId: stateCache.schemeId,
          majorId: stateCache.majorId,
          wrongQuestionType: stateCache.wrongQuestionType,
          correctCount: stateCache.correctCount
        })
        // ctx.dispatch('init', {
        //   schemeId: stateCache.schemeId,
        //   majorId: stateCache.majorId,
        //   wrongQuestionType: stateCache.wrongQuestionType,
        //   correctCount: stateCache.correctCount
        // })
      }
    })
  }

  @Mutation
  setStateCacheToSchemeCorrectionPracticeListMap(payload: StateCache) {
    this.schemeCorrectionPracticeListMap = this.schemeCorrectionPracticeListMap.filter(
      p =>
        p.schemeId !== payload.schemeId ||
        p.wrongQuestionType !== payload.wrongQuestionType ||
        (p.wrongQuestionType === WrongQuestionType.CORRECT_IN_CORRECTION && p.correctCount !== payload.correctCount)
    )
    this.schemeCorrectionPracticeListMap.push(payload)
  }

  @Mutation
  setNeedReload(payload: any) {
    const stateCache = this.schemeCorrectionPracticeListMap.find(
      p =>
        p.schemeId === payload.schemeId &&
        p.wrongQuestionType === payload.wrongQuestionType &&
        (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === payload.correctCount)
    )
    if (stateCache) {
      stateCache.needReload = true
    }
  }

  @Mutation
  setAnswerId(answerId: string) {
    this.currentAnswerId = answerId
  }

  /**
   * 获取错题重练卷子
   */
  get getTime() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === schemeId &&
          p.wrongQuestionType === wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === correctCount)
      )?.latestLoadTime
    }
  }

  /**
   * 获取错题重练卷子
   */
  get getQuestionPractice() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === schemeId &&
          p.wrongQuestionType === wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === correctCount)
      )?.correctCount
    }
  }

  /**
   * 获取最后一次作答错题答卷
   */
  get getLatestCorrectionPracticeAnswer() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === schemeId &&
          p.wrongQuestionType === wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === correctCount)
      )?.latestCorrectionPracticeAnswer
    }
  }

  /**
   * 获取各章节错题统计
   */
  get getAllChapterQuestionStatistic() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === schemeId &&
          p.wrongQuestionType === wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === correctCount)
      )?.allChapterQuestionStatistic
    }
  }

  /**
   * 获取各错题题型错题统计
   */
  get getAllQuestionTypeQuestionStatistic() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === schemeId &&
          p.wrongQuestionType === wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === correctCount)
      )?.allQuestionTypeQuestionStatistic
    }
  }

  /**
   * 各错题题型错题总量统计（包含已消灭）
   */
  get getAllQuestionTypeQuestionTotalStatistic() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === schemeId &&
          p.wrongQuestionType === wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === correctCount)
      )?.allQuestionTypeQuestionTotalStatistic
    }
  }

  /**
   * 各错题类型错题统计
   */
  get getAllQuestionCategoryQuestionStatistic() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === schemeId &&
          p.wrongQuestionType === wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === correctCount)
      )?.allQuestionCategoryQuestionStatistic
    }
  }

  /**
   * 各错题类型错题总量统计（包含已消灭）
   */
  get getAllQuestionCategoryQuestionTotalStatistic() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === schemeId &&
          p.wrongQuestionType === wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === correctCount)
      )?.allQuestionCategoryQuestionTotalStatistic
    }
  }

  /**
   * 已答总数、错题总数及错题率统计
   */
  get getSummary() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === schemeId &&
          p.wrongQuestionType === wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === correctCount)
      )?.summary
    }
  }

  /**
   * 7天前已答总数、错题总数及错题率统计
   */
  get getSummary7DaysBefore() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return this.schemeCorrectionPracticeListMap.find(
        p =>
          p.schemeId === schemeId &&
          p.wrongQuestionType === wrongQuestionType &&
          (p.wrongQuestionType !== WrongQuestionType.CORRECT_IN_CORRECTION || p.correctCount === correctCount)
      )?.summary7DaysBefore
    }
  }

  /**
   * 当前章节或题型答卷是否正在作答
   */
  get isAnswering() {
    return (
      schemeId: string,
      wrongQuestionType: WrongQuestionType,
      correctCount: number,
      fetchWay: number,
      examinationOutlineId: string | null,
      questionType: string
    ) => {
      const practiceAnswer = this.getLatestCorrectionPracticeAnswer(schemeId, wrongQuestionType, correctCount)?.find(
        (p: CorrectionPracticeAnswer) => {
          if (fetchWay === 3) {
            return p.fetchWay === fetchWay && p.questionType === questionType
          } else {
            return p.fetchWay === fetchWay && p.examinationOutlineId === examinationOutlineId
          }
        }
      )
      return practiceAnswer ? !practiceAnswer.complete : false
    }
  }

  /**
   * 当前章节或题型答卷是否作答完毕
   */
  get isAnswered() {
    return (
      schemeId: string,
      wrongQuestionType: WrongQuestionType,
      correctCount: number,
      fetchWay: number,
      examinationOutlineId: string | null,
      questionType: string
    ) => {
      const practiceAnswer = this.getLatestCorrectionPracticeAnswer(schemeId, wrongQuestionType, correctCount)?.find(
        (p: CorrectionPracticeAnswer) => {
          if (fetchWay === 3) {
            return p.fetchWay === fetchWay && p.questionType === questionType
          } else {
            return p.fetchWay === fetchWay && p.examinationOutlineId === examinationOutlineId
          }
        }
      )
      return practiceAnswer?.complete || false
    }
  }

  /**
   * 当前章节或题型答卷
   */
  get answerPaper() {
    return (
      schemeId: string,
      wrongQuestionType: WrongQuestionType,
      correctCount: number,
      fetchWay: number,
      examinationOutlineId: string | null,
      questionType: string
    ) => {
      const practiceAnswer = this.getLatestCorrectionPracticeAnswer(schemeId, wrongQuestionType, correctCount)?.find(
        (p: CorrectionPracticeAnswer) => {
          if (fetchWay === 3) {
            return p.fetchWay === fetchWay && p.questionType === questionType
          } else {
            return p.fetchWay === fetchWay && p.examinationOutlineId === examinationOutlineId
          }
        }
      )
      return practiceAnswer
    }
  }

  /**
   *  错题数变化
   */
  get wrongQuestionCountChange() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return (
        (this.getSummary(schemeId, wrongQuestionType, correctCount)?.wrongQuestionCount || 0) -
        (this.getSummary7DaysBefore(schemeId, wrongQuestionType, correctCount)?.wrongQuestionCount || 0)
      )
    }
  }

  /**
   * 错题次数变化
   */
  get wrongQuestionTimesChange() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return (
        (this.getSummary(schemeId, wrongQuestionType, correctCount)?.wrongQuestionTimes || 0) -
        (this.getSummary7DaysBefore(schemeId, wrongQuestionType, correctCount)?.wrongQuestionTimes || 0)
      )
    }
  }

  /**
   * 错题率变化
   */
  get wrongRateChange() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return (
        (this.getSummary(schemeId, wrongQuestionType, correctCount)?.wrongRate || 0) -
        (this.getSummary7DaysBefore(schemeId, wrongQuestionType, correctCount)?.wrongRate || 0)
      )
    }
  }

  /**
   * 各错题题型错题统计
   */
  get allQuestionTypeQuestionStatistic() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      return this.getAllQuestionTypeQuestionStatistic(schemeId, wrongQuestionType, correctCount)?.map(
        (p: UserWrongQuestionStatisticDTO) => {
          const item: CorrectionQuestionTypeItem = new CorrectionQuestionTypeItem()
          item.questionType = p.questionType
          item.questionCount = p.questionCount
          item.answering = this.isAnswering(schemeId, wrongQuestionType, correctCount, 3, null, p.questionType)
          item.answered = this.isAnswered(schemeId, wrongQuestionType, correctCount, 3, null, p.questionType)
          item.answerId =
            this.answerPaper(schemeId, wrongQuestionType, correctCount, 3, null, p.questionType)?.answerId || ''
          item.answerRecordId =
            this.answerPaper(schemeId, wrongQuestionType, correctCount, 3, null, p.questionType)?.id || ''
          return item
        }
      )
    }
  }

  get specifyExaminationOutlineQuestionCount() {
    return (
      schemeId: string,
      wrongQuestionType: WrongQuestionType,
      correctCount: number,
      leafSyllabus: Array<string>
    ) => {
      return this.getAllChapterQuestionStatistic(schemeId, wrongQuestionType, correctCount)
        ?.filter(
          (p: UserWrongQuestionStatisticDTO) =>
            p.tagIds && p.tagIds.some((tagId: string) => leafSyllabus.includes(tagId))
        )
        .map((p: UserWrongQuestionStatisticDTO) => p.questionCount)
        .reduce((a: number, b: number) => a + b, 0)
    }
  }

  get allChapterQuestionStatisticWithSyllabus() {
    return (
      schemeId: string,
      wrongQuestionType: WrongQuestionType,
      correctCount: number,
      syllabusTree: Array<ChapterTree>
    ) => {
      // 自定义函数，填充已答试题信息
      const fillQuestionAnswerStatistic = (syllabusTree: Array<ChapterTree>) => {
        const questionPracticeSyllabusTree: Array<CorrectionQuestionSyllabusTree> = new Array<
          CorrectionQuestionSyllabusTree
        >()
        syllabusTree.forEach((syllabus: ChapterTree) => {
          const questionPracticeSyllabus: CorrectionQuestionSyllabusTree = new CorrectionQuestionSyllabusTree()
          questionPracticeSyllabus.id = syllabus.id
          questionPracticeSyllabus.name = syllabus.name
          questionPracticeSyllabus.relationId = syllabus.relationId
          questionPracticeSyllabus.sequence = syllabus.sequence
          questionPracticeSyllabus.answering = this.isAnswering(
            schemeId,
            wrongQuestionType,
            correctCount,
            1,
            syllabus.id,
            ''
          )
          questionPracticeSyllabus.answered = this.isAnswered(
            schemeId,
            wrongQuestionType,
            correctCount,
            1,
            syllabus.id,
            ''
          )
          questionPracticeSyllabus.answerId =
            this.answerPaper(schemeId, wrongQuestionType, correctCount, 1, syllabus.id, '')?.answerId || ''
          questionPracticeSyllabus.answerRecordId =
            this.answerPaper(schemeId, wrongQuestionType, correctCount, 1, syllabus.id, '')?.id || ''
          questionPracticeSyllabus.questionCount =
            this.specifyExaminationOutlineQuestionCount(
              schemeId,
              wrongQuestionType,
              correctCount,
              syllabus.getLeafChapterIds()
            ) || 0
          if (syllabus.children) {
            questionPracticeSyllabus.children = fillQuestionAnswerStatistic(syllabus.children)
          }
          questionPracticeSyllabusTree.push(questionPracticeSyllabus)
        })
        return questionPracticeSyllabusTree
      }
      return fillQuestionAnswerStatistic(syllabusTree)
    }
  }

  /**
   * 获取错题题型占比数据
   */
  get questionTypeDistributions() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      const totalNum = this.getAllQuestionTypeQuestionTotalStatistic(schemeId, wrongQuestionType, correctCount)
        ?.map((p: UserWrongQuestionStatisticDTO) => p.questionCount)
        .reduce((a: number, b: number) => a + b, 0)
      return this.getAllQuestionTypeQuestionTotalStatistic(schemeId, wrongQuestionType, correctCount)?.map(
        (p: UserWrongQuestionStatisticDTO) => {
          const distribution: QuestionTypeDistribution = new QuestionTypeDistribution()
          distribution.questionType = p.questionType
          const statistic = this.getAllQuestionTypeQuestionStatistic(schemeId, wrongQuestionType, correctCount)?.find(
            (e: UserWrongQuestionStatisticDTO) => e.questionType === p.questionType
          )
          distribution.questionCount = statistic?.questionCount || 0
          distribution.eliminateCount = p.questionCount - distribution.questionCount
          distribution.proportion = (totalNum && distribution.questionCount / totalNum) || 0
          return distribution
        }
      )
    }
  }

  /**
   * 获取错题题类占比数据
   */
  get questionCategoryDistributions() {
    return (schemeId: string, wrongQuestionType: WrongQuestionType, correctCount: number) => {
      const totalNum = this.getAllQuestionCategoryQuestionTotalStatistic(schemeId, wrongQuestionType, correctCount)
        ?.map((p: UserWrongQuestionStatisticDTO) => p.questionCount)
        .reduce((a: number, b: number) => a + b, 0)
      return this.getAllQuestionCategoryQuestionTotalStatistic(schemeId, wrongQuestionType, correctCount)?.map(
        (p: UserWrongQuestionStatisticDTO) => {
          const distribution: QuestionCategoryDistribution = new QuestionCategoryDistribution()
          distribution.questionCategory = p.questionCategory
          const statistic = this.getAllQuestionCategoryQuestionStatistic(
            schemeId,
            wrongQuestionType,
            correctCount
          )?.find((e: UserWrongQuestionStatisticDTO) => e.questionType === p.questionType)
          distribution.questionCount = statistic?.questionCount || 0
          distribution.eliminateCount = p.questionCount - distribution.questionCount
          distribution.proportion = (totalNum && distribution.questionCount / totalNum) || 0
          return distribution
        }
      )
    }
  }
}

export default getModule(CorrectionModule)
