import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/ParticipatingUnit'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'ParticipatingUnit'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum ServicerStatusEnums {
  ALL = 'ALL',
  NORMAL = 'NORMAL',
  SUSPEND = 'SUSPEND'
}

// 类

/**
 * 参训单位名称查询信息
 */
export class ParticipatingUnitParams {
  /**
   * 参训单位名称
   */
  name?: string
  /**
   * 统一社会信用代码
   */
  code?: string
  /**
   * 所在地区路径
   */
  regionPath?: string
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 参训单位列表信息
 */
export class ParticipatingUnitDto {
  /**
   * Id
   */
  id: string
  /**
   * 参训单位
   */
  name: string
  /**
   * 地区名称列表，省市区县...
   */
  regionNames: Array<string>
  /**
   * 所在地区路径
   */
  regionPath: string
  /**
   * 统一社会信用代码
   */
  code: string
  /**
   * 联系人
   */
  contactPerson: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 服务商状态
@see ServicerStatusEnums
   */
  servicerStatus: ServicerStatusEnums
}

export class ParticipatingUnitDtoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ParticipatingUnitDto>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 验证当前登录的token对应账号是否已注册参训单位服务商
   * @param mztToken
   * @return
   * @param query 查询 graphql 语法文档
   * @param mztToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async hasParticipatingUnit(
    mztToken: string,
    query: DocumentNode = GraphqlImporter.hasParticipatingUnit,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { mztToken },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 子项目管理员查询参数单位分页接口
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async page(
    params: { page?: Page; params?: ParticipatingUnitParams },
    query: DocumentNode = GraphqlImporter.page,
    operation?: string
  ): Promise<Response<ParticipatingUnitDtoPage>> {
    return commonRequestApi<ParticipatingUnitDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 功能描述 : 参训单位单点登录需要同步信息接口
   * @date : 2021/10/12 16:37
   * @param mztToken : 闽政通token
   * @return : void
   * @param mutate 查询 graphql 语法文档
   * @param mztToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async syncTrainingInstitution(
    mztToken: string,
    mutate: DocumentNode = GraphqlImporter.syncTrainingInstitution,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mztToken },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
