<template>
  <el-drawer title="读取专题名称" :visible.sync="dialog6" size="700px" custom-class="m-drawer">
    <div class="drawer-bd">
      <div class="m-poster">
        <div class="poster-tab">
          <div class="item" :class="posterType === 'template-01' ? 'current' : ''" @click="chooseTemplate(1)">
            <img src="@design/admin/assets/images/poster-thumb-01.jpg" />
          </div>
          <div class="item" :class="posterType === 'template-02' ? 'current' : ''" @click="chooseTemplate(2)">
            <img src="@design/admin/assets/images/poster-thumb-02.jpg" />
          </div>
          <div class="item" :class="posterType === 'template-03' ? 'current' : ''" @click="chooseTemplate(3)">
            <img src="@design/admin/assets/images/poster-thumb-03.jpg" />
          </div>
        </div>
        <div class="poster-cont">
          <!--模板1 - 编辑样式-->
          <div class="poster-box" v-if="posterType === 'template-01'" :id="posterType">
            <div class="poster poster-01">
              <div class="name">{{ thematicManagementItem.basicInfo.subjectName }}</div>
              <div class="custom-text" v-if="editType === true">
                <el-input
                  type="textarea"
                  placeholder="可输入自定义文字"
                  maxlength="18"
                  show-word-limit
                  v-model="slogan"
                ></el-input>
              </div>
              <!--保存后-->
              <div class="custom-text" v-else>{{ slogan }}</div>
              <div class="tel">
                <img class="tel-icon" src="@design/admin/assets/images/poster-01-tel.png" />
                <div class="cont">
                  <p>客服热线：</p>
                  <div class="tel-num">{{ thematicManagementItem.portalInfo.phone }}</div>
                </div>
              </div>
              <div class="qrcode-box">
                <div class="item">
                  <div class="code"><img :src="h5QrcCode" /></div>
                  <el-input
                    v-if="editType === true"
                    v-model="qrCodeDetail"
                    placeholder="请输入二维码名称"
                    maxlength="8"
                    show-word-limit
                  ></el-input>
                  <!--保存后-->
                  <div class="txt" v-else>{{ qrCodeDetail }}</div>
                </div>
                <!-- <div class="item">
                  <el-input
                    v-if="editType === true"
                    v-model="input"
                    placeholder="请输入二维码名称"
                    maxlength="8"
                    show-word-limit
                  ></el-input>
                  <div class="txt" v-else>扫码立即访问</div>
                </div> -->
              </div>
            </div>
          </div>

          <!-- 模板2 -->
          <div class="poster-box" v-if="posterType === 'template-02'" :id="posterType">
            <div class="poster poster-02">
              <div class="name">{{ thematicManagementItem.basicInfo.subjectName }}</div>
              <div class="custom-text" v-if="editType === true">
                <el-input v-model="slogan" placeholder="可输入自定义文字" maxlength="24" show-word-limit></el-input>
              </div>
              <!--保存后-->
              <div class="custom-text" v-else>{{ slogan }}</div>
              <div class="tel">
                <img class="tel-icon" src="@design/admin/assets/images/poster-01-tel.png" />
                <div class="cont">
                  <p>客服热线：</p>
                  <div class="tel-num">{{ thematicManagementItem.portalInfo.phone }}</div>
                </div>
              </div>
              <div class="qrcode-box">
                <div class="item">
                  <div class="code"><img :src="h5QrcCode" /></div>
                  <el-input
                    v-if="editType === true"
                    v-model="qrCodeDetail"
                    placeholder="请输入二维码名称"
                    maxlength="8"
                    show-word-limit
                  ></el-input>
                  <!--保存后-->
                  <div class="txt" v-else>{{ qrCodeDetail }}</div>
                </div>
                <!-- <div class="item">
                  <el-input
                    v-if="editType === true"
                    v-model="input"
                    placeholder="请输入二维码名称"
                    maxlength="8"
                    show-word-limit
                  ></el-input>
                  <div class="txt" v-else>扫码立即访问</div>
                </div> -->
              </div>
            </div>
          </div>

          <!-- 模板3 -->
          <div class="poster-box" v-if="posterType === 'template-03'" :id="posterType">
            <div class="poster poster-03">
              <div class="name">{{ thematicManagementItem.basicInfo.subjectName }}</div>
              <div class="custom-text" v-if="editType === true">
                <el-input v-model="slogan" placeholder="可输入自定义文字" maxlength="24" show-word-limit></el-input>
              </div>
              <!--保存后-->
              <div class="custom-text" v-if="editType === false">{{ slogan }}</div>
              <div class="tel">
                <img class="tel-icon" src="@design/admin/assets/images/poster-03-tel.png" />
                <div class="cont">
                  <p>客服热线：</p>
                  <div class="tel-num">{{ thematicManagementItem.portalInfo.phone }}</div>
                </div>
              </div>
              <!--如果有3个，需要添加 is-more-->
              <div class="qrcode-box">
                <div class="item">
                  <div class="code"><img :src="h5QrcCode" /></div>
                  <el-input
                    v-if="editType === true"
                    v-model="qrCodeDetail"
                    placeholder="请输入二维码名称"
                    maxlength="8"
                    show-word-limit
                  ></el-input>
                  <!--保存后-->
                  <div class="txt" v-else>{{ qrCodeDetail }}</div>
                </div>
                <!-- <div class="item">
                  <el-input
                    v-if="editType === true"
                    v-model="input"
                    placeholder="请输入二维码名称"
                    maxlength="8"
                    show-word-limit
                  ></el-input>
                  <div class="txt" v-else>扫码立即访问</div>
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="dialog6 = false">关闭</el-button>
      <el-button type="primary" @click="downloadPoster">下载图片</el-button>
      <el-button type="primary" v-if="editType === false" @click="editPoster" disabled>编辑</el-button>
      <el-button type="primary" v-else @click="savePoster">保存</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import QrCode from 'qrcode'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import Env from '@api/service/common/utils/Env'

  @Component
  export default class extends Vue {
    //TODO
    // logo
    slogan = ''
    // 二维码描述
    qrCodeDetail = ''
    dialog6 = false
    posterType = 'template-01' // 海报模板
    editType = false // 编辑状态
    topicID = '' //专题id
    domainUrl = '' //专题域名
    h5Url = '' //专题h5域名
    h5QrcCode = '' //h5专题二维码
    thematicManagementItem = new ThematicManagementItem()
    // 环境单例
    env = Env

    async search() {
      this.thematicManagementItem = await this.thematicManagementItem.getDetail(this.topicID)
    }

    @Watch('domainUrl', {
      deep: true,
      immediate: true
    })
    async setH5Url(val: string) {
      if (val) {
        if (!val.includes('http')) {
          val = `${window.location.protocol}//${val}`
        }
        this.h5Url = val + this.env.proxyPortStr + '/h5'
        console.log('this.h5Url', this.h5Url)
        this.h5QrcCode = await QrCode.toDataURL(this.h5Url)
      }
    }
    // 选择海报模板
    chooseTemplate(index: number) {
      switch (index) {
        case 1:
          this.posterType = 'template-01'
          break
        case 2:
          this.posterType = 'template-02'
          break
        case 3:
          this.posterType = 'template-03'
          break
      }
    }
    // 下载海报
    downloadPoster() {
      this.$domToPic(this.posterType, { width: 1040, height: 1477 })
    }

    // 编辑
    editPoster() {
      this.editType = true
    }

    // 保存海报
    savePoster() {
      this.editType = false
    }
  }
</script>
