import QueryCourseware from '@api/service/management/resource/courseware/query/QueryCourseware'
import MutationCreateCourseware from '@api/service/management/resource/courseware/mutation/MutationCreateCourseware'
import MutationUpdateCourseware from '@api/service/management/resource/courseware/mutation/MutationUpdateCourseware'
import MutationBizCourseware from '@api/service/management/resource/courseware/mutation/MutationBizCourseware'
import QueryStudentCourseware from '@api/service/management/resource/courseware/query/QueryStudentCourseware'
import UpdateCoursewareVo from '@api/service/management/resource/courseware/mutation/vo/UpdateCoursewareVo'
import { CheckCoursewareRequest } from '@api/ms-gateway/ms-course-resource-v1'
import MsCourseResourceV1 from '@api/ms-gateway/ms-course-resource-v1'

class CoursewareFactory {
  queryCourseware = new QueryCourseware()

  getBizCourseware(coursewareId: string) {
    return new MutationBizCourseware(coursewareId)
  }

  get createCourseware() {
    return new MutationCreateCourseware()
  }

  /**
   * 查询学员课件实例
   */
  get queryStudentCourseware() {
    return new QueryStudentCourseware()
  }
  /**
   * 查课件名称是否重复
   */
  async getCheckCourseWare(courseName: string, id?: string) {
    const request = new CheckCoursewareRequest()
    request.name = courseName
    request.id = id
    const { data } = await MsCourseResourceV1.checkCourseware(request)
    return data
  }

  /**
   * 获取更新课件的实体
   * @param coursewareId
   */
  async getUpdateCourseware(coursewareId: string): Promise<MutationUpdateCourseware> {
    const coursewareDetailVo = await this.queryCourseware.queryCoursewareById(coursewareId)
    const mutationUpdateCourseware = new MutationUpdateCourseware()
    mutationUpdateCourseware.from(coursewareDetailVo)
    return mutationUpdateCourseware
  }
}

export default CoursewareFactory
