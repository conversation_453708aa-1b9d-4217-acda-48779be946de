import ObsReceive, { FileResponse } from '@api/service/common/obs/ObsReceive'
import Env from '@api/service/common/utils/Env'

/**
 * @description obs文件移动端管理器
 */
class ObsMobileFileManager {
  private obsReceive: ObsReceive = new ObsReceive()
  // 初始化配置
  private init() {
    let env = process.env.VUE_APP_SERVICE_ENV
    let isDev = ['dev', 'test2', 'test1'].includes(process.env.VUE_APP_SERVICE_ENV)
    // #ifdef H5
    env = Env.proxyEnvStr
    if (Env.isProxyProdEnv) {
      // 非内网环境
      isDev = false
    } else {
      isDev = true
    }
    // #endif
    let action
    const headers = new Object()
    if (isDev) {
      action = `${['https://api.', env, '.59iedu.com:9443'].join('')}${'/web/ms-obsfile-v1'}`
      // #ifdef H5
      action = `${['https://api', env, '59iedu.com:9443'].join('')}${'/web/ms-obsfile-v1'}`

      // #endif
    } else {
      action = `${process.env.VUE_APP_RELEASE_URL}${'/web/ms-obsfile-v1'}`
    }
    action += '/web/getResourceVisitInfo'
    // 设置额外请求头
    headers['Service-Name'] = 'ms-obsfile-v1'
    headers['Graphql-SchemaName'] = 'ms-obsfile-v1'
    headers['Content-Type'] = 'application/json'
    return {
      action,
      headers
    }
  }

  /**
   * 根据文件路径获取移动端Obs文件路径
   * @param filePath 文件路径
   * @private
   */
  private async getMobileOBSFilePath(filePath: string) {
    const config = this.init()
    return await new Promise((resolve, reject) => {
      uni.request({
        url: config.action,
        method: 'POST',
        header: {
          Authorization: 'Mship ' + uni.getStorageSync('customer.Access-Token'),
          'App-Authentication': 'Basic ' + uni.getStorageSync('App-Authentication'),
          ...config.headers
        },
        data: {
          path: filePath
        },
        success: (res: any) => {
          if (res.statusCode === 200) {
            resolve(res.data?.data)
          } else {
            reject(new FileResponse())
          }
        }
      })
    })
  }

  /**
   * 获取obs文件信息
   * @param filePath 文件路径，形如'/ms-obsfile/protected/xxxx'
   * @return FileResponse
   */
  async getMobileResolveFilePath(filePath: string): Promise<FileResponse> {
    let result = new FileResponse()
    if (!filePath) return result
    const publicRegx = /\/public\//
    const isPublic = publicRegx.test(filePath)
    if (!isPublic) {
      result = (await this.getMobileOBSFilePath(filePath)) as FileResponse
      result.isPublic = false
      result.originPath = filePath
      const { fileType } = this.obsReceive.getFileTypeByFilePath(filePath)
      result.fileType = fileType
      result.size = result.size ? Number(result.size) : 0
    } else {
      result.isPublic = true
      result.originPath = filePath
      result.path = filePath
      const { fileType, fileName } = this.obsReceive.getFileTypeByFilePath(filePath)
      result.fileType = fileType
      result.fileName = fileName
    }
    return result
  }

  /**
   * 获取移动端访问全路径
   */
  getMobileFullPath(urlPath: string) {
    const urlRegx = /^http|https:\/\/+/
    const httpRegx = /^http:\/\/+/
    if (urlRegx.test(urlPath)) {
      // 如果以http(s)://开头的url，直接返回
      return urlPath
    } else {
      const isDev = ['dev', 'test2', 'test1'].includes(process.env.VUE_APP_SERVICE_ENV)
      if (isDev) {
        return `${process.env.VUE_APP_HOST}${urlPath}`
      } else {
        return `https://btglxt-wx.59iedu.com${urlPath}`
      }
    }
  }
}

export default ObsMobileFileManager
