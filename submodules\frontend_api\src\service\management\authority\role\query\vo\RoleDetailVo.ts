import { RoleResponse } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'

export class RoleDetailVo {
  /**
   * 唯一标识
   */
  id: string

  /**
   * 角色名称
   */
  name: string

  /**
   * 角色说明
   */
  description: string

  /**
   * 角色类别
   1-学员  2-集体报名管理员  3-管理员  4-人设管理员  5-企业管理员  6-人社审批管理员  7-企业经办  8-企业法人  9-企业超管  10-人社超管
   */
  category: number

  /**
   * 是否内置
   */
  isBuiltIn = false

  /**
   * 是否选中
   */
  isSelect = false

  /**
   * 角色code码
   */
  roleCode = ''

  /**
   * 角色所属方
   * @param roleDetail
   */
  roleApplicationMemberType: number = null
  static from(roleDetail: RoleResponse) {
    const roleDetailVo = new RoleDetailVo()
    roleDetailVo.id = roleDetail?.id
    roleDetailVo.name = roleDetail?.name
    roleDetailVo.description = roleDetail?.description
    roleDetailVo.category = roleDetail?.category
    roleDetailVo.isBuiltIn = roleDetail?.nature === 1
    roleDetailVo.roleCode = roleDetail?.code
    roleDetailVo.roleApplicationMemberType = roleDetail?.applicationMemberType
    return roleDetailVo
  }
}
