import CheckSchemeRegisterResult from '@api/service/customer/train-class/offlinePart/model/CheckSchemeRegisterResult'
import { Page, Response, ResponseStatus } from '@hbfe/common'
import MsCommodityV1 from '@api/ms-gateway/ms-commodity-v1'
import MsLearningschemeV1, { ReservingSchemeIssueValidateRequest } from '@api/ms-gateway/ms-learningscheme-v1'
import MsTradeQueryFrontGatewayTradeQueryForestage, {
  OrderRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'

/**
 * 校验方案参数
 */
export class CheckTrainClassParam {
  /**
   * 商品skuId
   */
  commoditySkuId = ''
  /**
   * 方案Id
   */
  schemeId = ''
  /**
   * 期别Id
   * @description 当前报名的培训班类型为面授、面网授班时，必传
   */
  issueId = ''
  /**
   * 购买渠道类型
   */
  channelType = 1
  /**
   * 终端
   */
  terminalCode = 'Web'
}

/**
 * @description 方案工具类
 */
class SchemeUtil {
  /**
   * 校验方案
   * @description 校验方案是否可购买
   * @param checkParams 校验参数
   */
  async checkTrainClass(checkParams: CheckTrainClassParam): Promise<Response<CheckSchemeRegisterResult>> {
    const result = new Response<CheckSchemeRegisterResult>()
    const { commoditySkuId, schemeId, channelType, terminalCode, issueId } = checkParams
    result.data = new CheckSchemeRegisterResult()
    const validRes = await MsCommodityV1.validateCommodity({
      commoditySkuId,
      channelType,
      terminalCode
    })
    result.status = validRes.status
    if (validRes.status && validRes.status.isSuccess() && validRes.data) {
      result.status = new ResponseStatus(Number(validRes.data.code), validRes.data.message)
      if (validRes.data.code === '200') {
        const params = new ReservingSchemeIssueValidateRequest()
        params.schemeId = schemeId
        if (issueId) {
          params.issueId = issueId
        }
        const schemeRes = await MsLearningschemeV1.reservingSchemeIssueValidate(params)
        result.status = schemeRes.status
        if (schemeRes.status && schemeRes.status.isSuccess() && schemeRes.data) {
          const code = Number(schemeRes.data.code)
          result.status = new ResponseStatus(code, schemeRes.data.message)
          if (code === 50001 || code === 60002) {
            try {
              const subOrderNo = schemeRes.data.duplicateReservingInfos[0].sourceId
              const request = new OrderRequest()
              request.subOrderNoList = [subOrderNo]
              const orderListRes = await MsTradeQueryFrontGatewayTradeQueryForestage.pageOrderInMyself({
                page: new Page(1, 1),
                request
              })
              if (
                orderListRes.status.isSuccess() &&
                orderListRes.data &&
                orderListRes.data.currentPageData &&
                orderListRes.data.currentPageData.length
              ) {
                const order = orderListRes.data.currentPageData[0]
                if ([0, 1].includes(order.basicData.orderPaymentStatus)) {
                  result.data.orderNo = order.orderNo
                  if (order.basicData.channelType == 2) {
                    result.status.code = 52009
                  } else {
                    result.status.code = 52001
                  }
                }
              }
            } catch (e) {
              console.log('获取订单信息失败，', e)
            }
          }
        }
      }
    }
    return result
  }
}

export default new SchemeUtil()
