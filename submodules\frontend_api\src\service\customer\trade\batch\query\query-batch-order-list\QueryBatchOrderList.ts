import PageSearch from '@api/service/common/page-search/PageSearch'
import QueryBatchOrderListParams from './vo/QueryBatchOrderListParams'
class QueryBatchOrderList extends PageSearch {
  queryBatchOrderListParams = new QueryBatchOrderListParams()

  /**
   * @description: 获取批次单列表
   * @param {*}
   * @return {*}
   */

  async queryBatchOrderListPage() {
    return
  }
}
export default QueryBatchOrderList
