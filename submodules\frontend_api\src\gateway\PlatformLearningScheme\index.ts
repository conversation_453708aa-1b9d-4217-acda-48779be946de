import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformLearningScheme'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformLearningScheme'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum CommoditySkuState {
  STORED = 'STORED',
  UPED = 'UPED',
  DOWNED = 'DOWNED'
}

// 类

/**
 * 期数相关的查询
@author: eleven
@date: 2020/3/11
 */
export class IssueParamDTO {
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 学习方案id
   */
  schemeIdList?: Array<string>
  /**
   * 方案名称
   */
  scheme?: string
  /**
   * 单位id集合
   */
  unitIdList?: Array<string>
  /**
   * 年度
   */
  year?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId?: string
}

/**
 * @author: eleven
@date: 2020/3/27
 */
export class LearningSchemeParamDTO {
  /**
   * 学习方案id
   */
  schemeIdList?: Array<string>
  /**
   * 方案名称
   */
  scheme?: string
  /**
   * 单位id集合
   */
  unitIdList?: Array<string>
  /**
   * 年度
   */
  year?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId?: string
}

/**
 * 期数销售属性
@author: eleven
@date: 2020/3/13
 */
export class LSIssueCreateDTO {
  schemeId?: string
  title?: string
  startTime?: string
  endTime?: string
  studyTimeConfigType?: number
  openCustomerPurchase: boolean
  sort: number
  saleIntroduce?: string
  price?: number
  shelvePlain?: ShelvePlain1
}

/**
 * 期数更新对象
@author: eleven
@date: 2020/3/13
 */
export class LSIssueUpdateDTO {
  schemeId?: string
  issueId?: string
  title?: string
  startTime?: string
  endTime?: string
  studyTimeConfigType?: number
  sort: number
  openCustomerPurchase: boolean
  price?: number
  saleIntroduce?: string
  shelvePlain?: ShelvePlain1
}

/**
 * 学习方案创建差异化对象
<AUTHOR>
@date 2020/8/11
@description
 */
export class BTPXIssueClassLSCreateRequest {
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种
   */
  workTypeId?: string
  /**
   * 是否开放证明打印
   */
  openPrintCertificate: boolean
  /**
   * 兴趣课程包 - 服务内部使用
   */
  interestPoolIdList?: string
  /**
   * 适用人群
   */
  suitablePeople?: Array<string>
  /**
   * 兴趣课程包配置
   */
  interestCourseSetting?: InterestCourseSetting1
  /**
   * 课程供应商id
   */
  coursewareSupplierId: string
  name?: string
  year: number
  picture?: string
  unitId?: string
  content?: string
  mobileContent?: string
  achieveSetting?: IssueClassLSAchieveSettingDto
  courseLearningSettings?: CourseLearningSettingsDto
  examLearningSettings?: ExamLearningSettingsDto
  questionPracticeLearningSettings?: QuestionPracticeLearningSettingsDto
}

/**
 * <AUTHOR>
@date 2020/8/11
@description
 */
export class BTPXIssueClassLSUpdateRequest {
  /**
   * 是否开放证明打印
   */
  openPrintCertificate: boolean
  /**
   * 兴趣课程包 - 服务内部使用
   */
  interestPoolIdList?: string
  /**
   * 适用人群
   */
  suitablePeople?: Array<string>
  /**
   * 兴趣课程包配置
   */
  interestCourseSetting?: InterestCourseSetting1
  /**
   * 课程供应商id
   */
  coursewareSupplierId?: string
  schemeId?: string
  name?: string
  picture?: string
  content?: string
  mobileContent?: string
  achieveSetting?: IssueClassLSAchieveSettingDto
  courseLearningSettings?: CourseLearningSettingsDto
  examLearningSettings?: ExamLearningSettingsDto
  questionPracticeLearningSettings?: QuestionPracticeLearningSettingsDto
}

/**
 * <AUTHOR>
@date 2020/8/12
@description
 */
export class InterestCourseSetting1 {
  /**
   * 兴趣课程包集合
前端传递的参数，服务内部转为 interestPoolIdList
   */
  poolList?: Array<string>
}

export class Page {
  pageNo?: number
  pageSize?: number
}

export class CourseLearningSettingsDto {
  enabled: boolean
  compulsoryPackages?: Array<PackageRuleSettingDto1>
  optionalPackages?: Array<PackageRuleSettingDto1>
  optionalTotalPeriod?: number
  assessSetting?: CourseLearningAssessSettingDto
}

export class CourseLearningAssessSettingDto {
  allSelectedComplete: boolean
  schedule: number
}

export class ExamLearningSettingsDto {
  enabled: boolean
  name?: string
  examPaperId?: string
  examTimeLength: number
  examCount: number
  configExamTime: boolean
  beginTime?: string
  endTime?: string
  passScore: number
  openResolvedExam: boolean
  minSubmitTimeLength: number
  less: boolean
  missScorePattern: number
  enabledExamLearningPrecondition: boolean
  assessSetting?: ExamAssessSettingDto
}

export class ExamAssessSettingDto {
  score: number
}

export class IssueClassLSAchieveSettingDto {
  enabledAssess: boolean
  grade?: number
  templateId?: string
}

export class LibraryWaySettingDto1 {
  libraryIds?: Array<string>
  recursive: boolean
}

export class PackageRuleSettingDto1 {
  packageId?: string
  limit: boolean
  maxPeriod?: number
}

export class QuestionPracticeLearningSettingsDto {
  enabled: boolean
  fetchWay: number
  libraryWaySetting?: LibraryWaySettingDto1
  tagsWaySetting?: TagWaySettingDto1
}

export class ShelvePlain1 {
  onShelve: boolean
  onShelvePlanTime?: string
  offShelvePlanTime?: string
}

export class TagWaySettingDto1 {
  tagIds?: Array<string>
}

/**
 * <AUTHOR>
@date 2020/8/17
@description
 */
export class CourseInPoolResponse {
  courseId: string
  poolId: string
  poolName: string
  showName: string
}

/**
 * 课程考核信息
@author: eleven
@date: 2020/6/5
 */
export class CourseLearningAssessSettingResponse {
  allSelectedComplete: boolean
  schedule: number
}

/**
 * @author: eleven
@date: 2020/6/6
 */
export class CourseLearningResponse {
  /**
   * 学习方案要求的最少学时
   */
  minTotalPeriod: number
  /**
   * 课程考核要求
   */
  assessSetting: CourseLearningAssessSettingResponse
  learningId: string
  enabled: boolean
  compulsoryPackages: Array<PackageRuleSettingDto>
  optionalPackages: Array<PackageRuleSettingDto>
}

/**
 * @author: eleven
@date: 2020/6/5
 */
export class ExamAssessSettingResponse {
  score: number
}

/**
 * @author: eleven
@date: 2020/6/6
 */
export class ExamLearningResponse {
  /**
   * 考试考核，null表示不设置考核
   */
  assessSetting: ExamAssessSettingResponse
  learningId: string
  enabled: boolean
  name: string
  examPaperId: string
  examTimeLength: number
  examCount: number
  configExamTime: boolean
  beginTime: string
  endTime: string
  passScore: number
  openResolvedExam: boolean
  minSubmitTimeLength: number
  less: boolean
  missScorePattern: number
}

/**
 * @author: eleven
@date: 2020/6/5
 */
export class IssueClassLSAchieveSettingResponse {
  enabledAssess: boolean
  grade: number
  templateId: string
}

/**
 * 学习方案详情
@author: eleven
@date: 2020/3/12
 */
export class LearningSchemeDetailResponse {
  /**
   * 单位id
   */
  unitId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 培训方案名称
   */
  name: string
  /**
   * 封面图片地址
   */
  picture: string
  /**
   * web内容
   */
  content: string
  /**
   * 手机端内容
   */
  mobileContent: string
  /**
   * 是否开放证明打印
   */
  openPrintCertificate: boolean
  /**
   * 课程学习方式信息
   */
  courseLearning: CourseLearningResponse
  /**
   * 考试学习方式信息
   */
  examLearning: ExamLearningResponse
  /**
   * 试题练习学习方式信息
   */
  questionPracticeLearning: QuestionPracticeLearningDto
  /**
   * 培训班成果设置
   */
  achieveSetting: IssueClassLSAchieveSettingResponse
  /**
   * 兴趣课程包配置
   */
  interestCourseSetting: InterestCourseSetting
  /**
   * 创建人ID
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 适用人群
   */
  suitablePeople: Array<string>
  /**
   * 课程供应商id
   */
  coursewareSupplierId: string
  /**
   * 课程供应商名称
   */
  coursewareSupplierName: string
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * @author: eleven
@date: 2020/6/10
 */
export class SkuPropertyAndOptionResponse {
  /**
   * sku属性id
   */
  skuPropertyId: string
  /**
   * 属性code
   */
  code: string
  /**
   * 属性值
   */
  optionList: Array<SkuPropertyOptionDTO>
}

/**
 * 商品分页对象
@author: eleven
@date: 2020/3/11
 */
export class IssueDTO {
  /**
   * 商品skuId
   */
  commoditySkuId: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方案名称
   */
  scheme: string
  /**
   * 期数id
   */
  issueId: string
  /**
   * 期数名称
   */
  issueTitle: string
  /**
   * 学习截止时间
   */
  learningEndTime: string
  /**
   * 学习开始时间
   */
  learningStartTime: string
  /**
   * 是否开放报名
   */
  openEnrolment: boolean
  /**
   * 排序
   */
  sort: number
  /**
   * 商品sku上下架状态
   */
  skuState: CommoditySkuState
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * 学习方案分页查询
@author: eleven
@date: 2020/3/27
 */
export class LearningSchemeDTO {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 培训方案名称
   */
  name: string
  /**
   * 培训班封面图片地址
   */
  picture: string
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * sku可选值
@author: eleven
@date: 2020/3/18
 */
export class SkuPropertyOptionDTO {
  /**
   * 主键(可选值id)
   */
  id: string
  /**
   * sku属性id
   */
  skuPropertyId: string
  /**
   * 可选值名称
   */
  optionName: string
  /**
   * 可选值代码
   */
  optionCode: string
  /**
   * 排序号码
   */
  orderNumber: number
}

/**
 * 商品内容仓储返回(销售卖点)
Author:FangKunSen
Time:2021-03-19,10:42
 */
export class CommodityContent {
  id: string
  content: string
}

/**
 * 学习方案对象
@author: eleven
@date: 2020/3/13
 */
export class LearningSchemeDTO1 {
  /**
   * 方案对象
   */
  preExamLSDto: IssueClassLSDto
  /**
   * 期数创建对象
   */
  issueAndCommoditySkuDtoList: Array<IssueAndCommoditySkuDto>
}

/**
 * <AUTHOR>
@date 2020/8/12
@description
 */
export class InterestCourseSetting {
  /**
   * 兴趣课程包集合
前端传递的参数，服务内部转为 interestPoolIdList
   */
  poolList: Array<string>
}

/**
 * <AUTHOR>
@date 2021/5/19 9:34
@Description:
 */
export class BTPXIssueClassLSResponse {
  /**
   * 兴趣课程包 - 服务内部使用
   */
  interestPoolIdList: Array<string>
  /**
   * 课程供应商id
   */
  coursewareSupplierId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 培训方案名称
   */
  name: string
  /**
   * 年度
   */
  year: number
  /**
   * 封面图片地址
   */
  picture: string
  /**
   * web内容
   */
  content: string
  /**
   * 手机端内容
   */
  mobileContent: string
  /**
   * 发布机构
   */
  unitId: string
  /**
   * 课程学习方式信息
   */
  courseLearning: CourseLearningResponse1
  /**
   * 考试学习方式信息
   */
  examLearning: ExamLearningResponse1
  /**
   * 试题练习学习方式信息
   */
  questionPracticeLearning: QuestionPracticeLearningResponse
  /**
   * 创建人ID
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 适用人群
   */
  suitablePeople: string
}

export class CourseLearningDto {
  learningId: string
  enabled: boolean
  compulsoryPackages: Array<PackageRuleSettingDto>
  optionalPackages: Array<PackageRuleSettingDto>
}

export class ExamLearningDto {
  learningId: string
  enabled: boolean
  name: string
  examPaperId: string
  examTimeLength: number
  examCount: number
  configExamTime: boolean
  beginTime: string
  endTime: string
  passScore: number
  openResolvedExam: boolean
  minSubmitTimeLength: number
  less: boolean
  missScorePattern: number
}

export class IssueAndCommoditySkuDto {
  schemeId: string
  issueId: string
  commoditySkuId: string
  title: string
  startTime: string
  endTime: string
  studyTimeConfigType: number
  openCustomerPurchase: boolean
  price: number
  saleIntroduce: string
  shelvePlain: ShelvePlain
}

export class IssueClassLSDto {
  schemeId: string
  name: string
  year: number
  unitId: string
  picture: string
  content: string
  mobileContent: string
  courseLearning: CourseLearningDto
  examLearning: ExamLearningDto
  questionPracticeLearning: QuestionPracticeLearningDto
  createUserId: string
  createTime: string
}

export class LibraryWaySettingDto {
  libraryIds: Array<string>
  recursive: boolean
}

export class PackageRuleSettingDto {
  packageId: string
  limit: boolean
  maxPeriod: number
}

export class QuestionPracticeLearningDto {
  learningId: string
  enabled: boolean
  fetchWay: number
  libraryWaySetting: LibraryWaySettingDto
  tagsWaySetting: TagWaySettingDto
}

export class ShelvePlain {
  onShelve: boolean
  onShelvePlanTime: string
  offShelvePlanTime: string
}

export class TagWaySettingDto {
  tagIds: Array<string>
}

/**
 * 课程学习方式信息
 */
export class CourseLearningResponse1 {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 是否启用课程学习方式
   */
  enabled: boolean
  /**
   * 必修课程包编号列表
   */
  compulsoryPackages: Array<PackageResponse>
  /**
   * 选修课程包集合
   */
  optionalPackages: Array<PackageResponse>
}

/**
 * 考试学习方式信息
 */
export class ExamLearningResponse1 {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 是否启用考试学习方式
   */
  enabled: boolean
  /**
   * 考试名称
   */
  name: string
  /**
   * 试卷ID
   */
  examPaperId: string
  /**
   * 考试时长，单位分钟
   */
  examTimeLength: number
  /**
   * 考试次数 0表示不限制次数
   */
  examCount: number
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 是否开放试题解析
   */
  openResolvedExam: boolean
  /**
   * 最短提交时长
   */
  minSubmitTimeLength: number
  /**
   * 是否配置考试时间
   */
  configExamTime: boolean
  /**
   * 开考时间
   */
  beginTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 多选漏选是否的分
   */
  less: boolean
  /**
   * 漏选得分模式
0:不得分，适用于漏选不得分情况
1:全得
2:得一半
3:平均得分
   */
  missScorePattern: number
}

/**
 * 题库配置
<AUTHOR>
@date 2020/6/3
@since 1.0.0
 */
export class LibraryWaySettingResponse {
  /**
   * 题库id集合
   */
  libraryIds: Array<string>
  /**
   * 是否递归取题
如果是题库卷时，才生效。
   */
  recursive: boolean
}

/**
 * 课程包规则设置
<AUTHOR>
@date 2020/6/1
@since 1.0.0
 */
export class PackageResponse {
  /**
   * 课程包编号
   */
  packageId: string
  /**
   * 是否限制最大可选学时,如果设置true,则需要填写{@link #maxPeriod}
   */
  limit: boolean
  /**
   * 当前课程包要求最多选课学时，如果不填，表示不设置要求
   */
  maxPeriod: number
}

/**
 * 试题练习学习方式信息
 */
export class QuestionPracticeLearningResponse {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 是否启用试题练习
   */
  enabled: boolean
  /**
   * 抽题方式， 1：自定义（此时需要指定random配置），2：题库，3：考场所属方案下已选课程关联试题，4：标签方式抽题
   */
  fetchWay: number
  /**
   * 题库抽题方式配置
   */
  libraryWaySetting: LibraryWaySettingResponse
  /**
   * 标签方式
   */
  tagsWaySetting: TagWaySettingResponse
}

/**
 * 标签方式抽题
<AUTHOR>
@date 2020/6/19
@since
 */
export class TagWaySettingResponse {
  /**
   * 标签编号集合
   */
  tagIds: Array<string>
}

export class IssueDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<IssueDTO>
}

export class LearningSchemeDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LearningSchemeDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取指定的考前培训方案
   * @param schemeId 学习方案ID
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findById(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.findById,
    operation?: string
  ): Promise<Response<BTPXIssueClassLSResponse>> {
    return commonRequestApi<BTPXIssueClassLSResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取内容仓储的具体web内容
   * @param contentId
   * @return
   * @param query 查询 graphql 语法文档
   * @param contentId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getContent(
    contentId: string,
    query: DocumentNode = GraphqlImporter.getContent,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { contentId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量获取内容仓储的具体web内容
   * @param contentIds
   * @return
   * @param query 查询 graphql 语法文档
   * @param contentIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getContentByIds(
    contentIds: Array<string>,
    query: DocumentNode = GraphqlImporter.getContentByIds,
    operation?: string
  ): Promise<Response<Array<CommodityContent>>> {
    return commonRequestApi<Array<CommodityContent>>(
      SERVER_URL,
      {
        query: query,
        variables: { contentIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习方案商情
   * UI上各种学习方式的tab是否显示条件：对应的学习方式不为空 且 enable为true展示
   * todo 单位id:方案管理-方案详情（需要调整为机构id），待方案微服务化后再做调整
   * @param schemeId
   * @return
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLearningSchemeDetail(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.getLearningSchemeDetail,
    operation?: string
  ): Promise<Response<LearningSchemeDetailResponse>> {
    return commonRequestApi<LearningSchemeDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取指定培训方案是否可以下架或删除
   * @param schemeId
   * @return true/false
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async isCanDownOrDeleteScheme(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.isCanDownOrDeleteScheme,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取sku属性及选项值
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listAllSkuPropertyAndOption(
    query: DocumentNode = GraphqlImporter.listAllSkuPropertyAndOption,
    operation?: string
  ): Promise<Response<Array<SkuPropertyAndOptionResponse>>> {
    return commonRequestApi<Array<SkuPropertyAndOptionResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取方案下面的兴趣课程
   * @param schemeId
   * @return
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listInterestCourse(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.listInterestCourse,
    operation?: string
  ): Promise<Response<Array<CourseInPoolResponse>>> {
    return commonRequestApi<Array<CourseInPoolResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习方案下面的期数商品
   * @param schemeId
   * @return
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listIssue(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.listIssue,
    operation?: string
  ): Promise<Response<Array<IssueAndCommoditySkuDto>>> {
    return commonRequestApi<Array<IssueAndCommoditySkuDto>>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取年度的可选值
   * @deprecated 目前补贴未用到，暂未提供
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listYearSkuOption(
    query: DocumentNode = GraphqlImporter.listYearSkuOption,
    operation?: string
  ): Promise<Response<Array<SkuPropertyOptionDTO>>> {
    return commonRequestApi<Array<SkuPropertyOptionDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageIssue(
    params: { page: Page; paramDTO: IssueParamDTO },
    query: DocumentNode = GraphqlImporter.pageIssue,
    operation?: string
  ): Promise<Response<IssueDTOPage>> {
    return commonRequestApi<IssueDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageLearningScheme(
    params: { page: Page; paramDTO: LearningSchemeParamDTO },
    query: DocumentNode = GraphqlImporter.pageLearningScheme,
    operation?: string
  ): Promise<Response<LearningSchemeDTOPage>> {
    return commonRequestApi<LearningSchemeDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建学习方案
   * @param createInfo
   * @param issueCreateDTOList
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createBTPXLS(
    params: { createInfo: BTPXIssueClassLSCreateRequest; issueCreateDTOList: Array<LSIssueCreateDTO> },
    mutate: DocumentNode = GraphqlImporter.createBTPXLS,
    operation?: string
  ): Promise<Response<LearningSchemeDTO1>> {
    return commonRequestApi<LearningSchemeDTO1>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建学习方案
   * @param createInfo
   * @param issueCreateDTOList
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createLS(
    params: { createInfo: BTPXIssueClassLSCreateRequest; issueCreateDTOList: Array<LSIssueCreateDTO> },
    mutate: DocumentNode = GraphqlImporter.createLS,
    operation?: string
  ): Promise<Response<LearningSchemeDTO1>> {
    return commonRequestApi<LearningSchemeDTO1>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 下架指定的学习方案
   * @param id 商品id
   * @return true/false 是否下架成功
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async offShelfBTPXLS(
    id: string,
    mutate: DocumentNode = GraphqlImporter.offShelfBTPXLS,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新学习方案
   * @param updateInfo
   * @param issueUpdateDTOList
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateBTPXLS(
    params: { updateInfo: BTPXIssueClassLSUpdateRequest; issueUpdateDTOList: Array<LSIssueUpdateDTO> },
    mutate: DocumentNode = GraphqlImporter.updateBTPXLS,
    operation?: string
  ): Promise<Response<LearningSchemeDTO1>> {
    return commonRequestApi<LearningSchemeDTO1>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新考前学习方案指定的期数
   * @param updateInfo 期数更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateIssue(
    updateInfo: LSIssueUpdateDTO,
    mutate: DocumentNode = GraphqlImporter.updateIssue,
    operation?: string
  ): Promise<Response<IssueAndCommoditySkuDto>> {
    return commonRequestApi<IssueAndCommoditySkuDto>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新学习方案
   * @param updateInfo
   * @param issueUpdateDTOList
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateLS(
    params: { updateInfo: BTPXIssueClassLSUpdateRequest; issueUpdateDTOList: Array<LSIssueUpdateDTO> },
    mutate: DocumentNode = GraphqlImporter.updateLS,
    operation?: string
  ): Promise<Response<LearningSchemeDTO1>> {
    return commonRequestApi<LearningSchemeDTO1>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
