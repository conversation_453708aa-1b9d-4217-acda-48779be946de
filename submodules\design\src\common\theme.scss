/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/packages/theme-chalk/src/fonts';

//color
$--color-primary: #1f86f0;
$--color-warning: #e89821;
$--color-success: #5ead37;
$--color-danger: #e93737;

// element-ui
$--color-text-primary: #444;

/* Button
-------------------------- */
$--button-font-size: 14px !default;
$--button-padding-vertical: 10px !default;

/* Border
-------------------------- */
//$--border-color-base: #e6e6e6 !default;
//$--border-color-light: #eee !default;
//$--border-color-lighter: #e8e8e8 !default;

/* Input
-------------------------- */
$--input-height: 36px !default;

/* Checkbox
-------------------------- */
$--checkbox-input-height: 14px !default;
$--checkbox-input-width: 14px !default;

/* Radio
-------------------------- */
$--radio-input-height: 14px !default;
$--radio-input-width: 14px !default;

/* Break-point
-------------------------- */
$--sm: 768px !default;
$--md: 1400px !default;
$--lg: 1650px !default;
$--xl: 1800px !default;

@import "~element-ui/packages/theme-chalk/src/index.scss";
