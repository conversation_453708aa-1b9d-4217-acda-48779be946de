/**
 * 我的培训班列表Vo
 */
import {
  StudentSchemeLearningResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import SkuPropertyResponseVo from '@api/service/customer/train-class/query/vo/SkuPropertyResponseVo'

class MyTrainClassCommodityVo extends StudentSchemeLearningResponse {
  // region properties
  /**
   *班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuValueNameProperty = new SkuPropertyResponseVo()
  /**
   *培训方案类型1: 选课规则2: 自主选课，类型为number3.培训合作
   */
  schemeType = 0
  /**
   *报名开始时间，类型为string
   */
  registerBeginDate = ''
  /**
   *报名结束时间，类型为string
   */
  registerEndDate = ''
  /**
   *培训开始时间，类型为string
   */
  trainingBeginDate = ''
  /**
   *培训结束时间，类型为string
   */
  trainingEndDate = ''
  /**
   * 学时，类型为number
   */
  period = 0
  /**
   *培训班名称，类型为string
   */
  trainClassName = ''
  /**
   * 培训班图片
   */
  picture = ''
  /**
   * 是否有配置培训模板
   */
  hasLearnResult = false
  /**
   * 培训模板Id
   */
  learningResultId = ''
  /**
   *开放证明打印
   */
  openPrintTemplate = false
  /**
   *是否智能学习中 班级列表
   */
  intelligentLearning = false
  /**
   * 培训方案须知
   */
  notice = ''

  /**
   * 是否弹窗展示须知
   */
  showNoticeDialog = false
  // endregion
  // region methods
  // endregion
}
export default MyTrainClassCommodityVo
