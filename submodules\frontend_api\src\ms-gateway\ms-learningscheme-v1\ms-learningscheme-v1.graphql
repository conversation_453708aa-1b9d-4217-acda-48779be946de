"""独立部署的微服务,K8S服务名:ms-learningscheme-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取导入模版下载路径
		/ms-file/~~
	"""
	getImportUpdateTemplateUrl:String
	"""方案配置是否由事务处理查询(临时接口)"""
	isProcessedByTransaction(request:IsProcessedByTransactionRequest):ProcessedByTransactionResponse
	"""学习方案配置执行事务阶段查询请求
		@see LearningSchemeConfigureTransactionStatus
		状态值说明：
		已开始 = 1
		预提交 = 2
		提交 = 3
		回滚 = 4
		完成 = 5(仅此状态，且重算完成的情况下，方案可修改)
	"""
	learningSchemeProcessTransactionStepQuery(schemeId:String):LearningSchemeProcessStatusResponse
	"""查询批量更新培训方案导入任务结果分页列表"""
	pageImportLearningSchemeImportTask(page:Page,request:PageLearningSchemeImportTaskRequest):LearningSchemeImportTaskResponsePage @page(for:"LearningSchemeImportTaskResponse")
}
type Mutation {
	"""管理人员申请自动学习token"""
	applyAutoLearningTokenForManage(request:ApplyAutoLearningTokenRequest):ApplyTrainingQualificationAutoLearningTokenResponse
	"""管理人员申请重学token(班级不提供重学也能获取到重学token)"""
	applyRelearnTokenForManage(request:ApplyReLearnTokenRequest):ApplyTrainingQualificationRelearnTokenResponse
	"""异步创建学习方案配置接口
		@return 学习方案id
	"""
	asyncCreateLearningScheme(request:AsyncCreateLearningSchemeRequest):String
	asyncRemoveLearningScheme(request:AsyncRemoveLearningSchemeRequest):String
	"""异步修改学习方案配置接口
		@return 学习方案id
	"""
	asyncUpdateLearningScheme(request:AsyncUpdateLearningSchemeRequest):String
	"""批量更新商品购买渠道
		@param request:
		<AUTHOR> By Cb
		@since 2024/4/26 10:29
	"""
	batchUpdateCommodityPurchaseChannel(request:BatchUpdateCommodityPurchaseChannelRequest):Void
	"""批量修改培训方案"""
	batchUpdateLearningScheme(request:BatchUpdateLearningSchemeRequest):String
	"""导出全部培训方案数据（本次批量修改的全部数据）"""
	batchUpdateLearningSchemeExport(mainTaskId:String!):ExportUpdateLearningSchemeImportResultResponse
	"""导出失败数据（批量修改培训方案数据）"""
	batchUpdateLearningSchemeExportFail(mainTaskId:String!):ExportUpdateLearningSchemeImportResultResponse
	"""批量刷新方案地区
		@param request:
		<AUTHOR> By Cb
		@since 2024/5/6 10:12
	"""
	batchUpdateSchemeRegion(request:BatchUpdateSchemeRegionRequest):Void
	"""创建学习方案"""
	createLearningScheme(token:String,configJson:String):String
	"""学习方案创建修复
		@param requestCommand :
		@return java.lang.String
		<AUTHOR> By Cb
		@since 2023/8/30 9:58
	"""
	learningSchemeCreateRepair(requestCommand:LearningSchemeCreateRepairRequestCommand):String
	"""学习方案更新修复
		@param command:
		@return {@link String}
		<AUTHOR> By Cb
		@since 2024/4/24 15:18
	"""
	learningSchemeUpdateRepair(command:UpdateLearningSchemeCommand):String
	"""一键合格"""
	oneKeyPass(request:OneKeyPassRequest):Void
	"""刷新方案配置且会同步更新学习方案
		@param request:
		<AUTHOR> By Cb
		@since 2024/5/6 10:02
	"""
	refreshConfigAndUpdate(request:RefreshSchemeConfigRequest):Void
	refreshSchemeConfig(request:RefreshSchemeConfigRequest):Void
	"""刷新方案配置
		@param request:
		<AUTHOR> By Cb
		@since 2024/4/26 10:29
	"""
	refreshSchemeConfigAndSaveConfig(request:RefreshSchemeConfigRequest):Void
	"""学员重学"""
	relearnForStudent(qualificationId:String):Void
	"""删除学习方案"""
	removeLearningScheme(id:String):Void
	"""事务修复接口
		@param schemeId      学习方案id
		@param transactionId 事务ID
	"""
	repairLearningSchemeProcessTransaction(schemeId:String,transactionId:String):Void
	"""校验方案期别是否能够预约"""
	reservingSchemeIssueValidate(request:ReservingSchemeIssueValidateRequest):ReservingSchemeValidateResponse
	"""校验方案是否能够预约"""
	reservingSchemeValidate(request:ReservingSchemeValidateRequest):ReservingSchemeValidateResponse
	"""重试配置资源处理完成事件处理
		@return java.lang.String
		<AUTHOR> By Cb
		@since 2023/9/11 16:51
	"""
	retryLearningConfigureResourceProcessCompletedEventHandle(payload:String):String
	"""特殊修改学习方案
		只针对方案上的配置和商品配置进行修改 其余配置就算传入也不会进行操作
		@param token      :
		@param configJson :
		@return java.lang.String
		<AUTHOR> By Cb
		@since 2023/9/4 9:40
	"""
	specialUpdateLearningScheme(token:String,configJson:String):String
	"""修改学习方案"""
	updateLearningScheme(token:String,configJson:String):String
	"""用户报名方案补偿接口"""
	userEnrolmentSchemeRepair(request:UserEnrolmentSchemeRepairRequest):Void
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input Property @type(value:"com.fjhb.domain.seedwork.property.Property") {
	name:String!
	value:String
}
"""<AUTHOR> create 2021/7/6 9:04"""
input UpdateLearningSchemeCommand @type(value:"com.fjhb.ms.learningscheme.v1.api.command.learningscheme.UpdateLearningSchemeCommand") {
	"""学习方案ID"""
	schemeId:String!
	"""学习方案名称"""
	name:String!
	"""学习方案封面图片"""
	picture:String
	"""学习方案介绍内容ID"""
	commentId:String
	"""报名开始时间"""
	registerBeginDate:DateTime
	"""报名结束时间"""
	registerEndDate:DateTime
	"""培训开始时间"""
	trainingBeginDate:DateTime
	"""培训结束时间"""
	trainingEndDate:DateTime
	"""是否提供重学"""
	provideRelearn:Boolean!
	"""简介id"""
	introId:String
	"""培训须知"""
	notice:String
	"""更新时间"""
	updatedTime:DateTime!
	"""扩展属性集合"""
	properties:[Property]
	"""地区"""
	region:String
}
input MetaData @type(value:"com.fjhb.ms.learningscheme.v1.kernel.appservice.dto.MetaDataDto$MetaData") {
	"""key
		包含学习方式key+合格属性key以.分隔
		chooseCourseLearning:选课学习 chooseCourseLearning.courseQuizQualifiedScore
		examLearning:考试 examLearning.qualifiedScore
		practiceLearning:练习
		autonomousCourseLearning:自主学习 autonomousCourseLearning.courseQuizQualifiedScore
		interestCourseLearning:兴趣课学习
		@see
	"""
	key:String
	"""值"""
	value:String
}
"""生成自动学习 token
	<AUTHOR> create 2021/4/15 8:48
"""
input ApplyAutoLearningTokenRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.ApplyAutoLearningTokenRequest") {
	"""参训资格ID"""
	qualificationId:String!
	"""学习方式id"""
	learningId:String!
}
"""生成重新学习 token
	<AUTHOR> create 2021/4/15 8:48
"""
input ApplyReLearnTokenRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.ApplyReLearnTokenRequest") {
	"""参训资格ID"""
	qualificationId:String!
	"""学习方式id"""
	learningId:String!
}
"""异步创建学习方案请求
	<AUTHOR>
"""
input AsyncCreateLearningSchemeRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.AsyncCreateLearningSchemeRequest") {
	"""校验token"""
	token:String
	"""方案配置json字符串"""
	configJson:String
}
"""异步删除学习方案请求
	<AUTHOR>
"""
input AsyncRemoveLearningSchemeRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.AsyncRemoveLearningSchemeRequest") {
	schemeId:String
}
"""异步修改学习方案请求
	<AUTHOR>
"""
input AsyncUpdateLearningSchemeRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.AsyncUpdateLearningSchemeRequest") {
	"""校验token"""
	token:String
	"""方案配置json字符串"""
	configJson:String
}
"""批量更新商品购买渠道命令
	<AUTHOR> By Cb
	@since 2024/4/26 10:21
"""
input BatchUpdateCommodityPurchaseChannelRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.BatchUpdateCommodityPurchaseChannelRequest") {
	"""服务商ID"""
	servicerId:String
	"""需要更新的方案ID列表"""
	schemeIdList:[String]
	"""跳过更新的方案ID列表"""
	skipSchemeIdList:[String]
	"""是否覆盖方案配置
		默认: true
	"""
	overwriteSchemeConfigure:Boolean!
	"""开启的购买渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	openPurchaseChannelList:[Int]
}
input BatchUpdateLearningSchemeRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.BatchUpdateLearningSchemeRequest") {
	"""文件路径"""
	filePath:String!
	"""文件名"""
	fileName:String
}
"""批量方案地区命令
	<AUTHOR> By Cb
	@since 2024/4/26 10:21
"""
input BatchUpdateSchemeRegionRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.BatchUpdateSchemeRegionRequest") {
	"""服务商ID"""
	servicerId:String
	"""需要更新的方案数据"""
	schemeInfoList:[StringKeyValue]
}
input StringKeyValue @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.BatchUpdateSchemeRegionRequest$StringKeyValue") {
	key:String
	value:String
}
"""方案配置是否由事务处理查询请求
	<AUTHOR>
"""
input IsProcessedByTransactionRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.IsProcessedByTransactionRequest") {
	"""学习方案id"""
	schemeIds:[String]
}
"""学习方案创建修复请求
	<AUTHOR> By Cb
	@since 2023/08/30 9:54
"""
input LearningSchemeCreateRepairRequestCommand @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.LearningSchemeCreateRepairRequestCommand") {
	"""学习方案ID"""
	schemeId:String
	"""学习方案名称"""
	name:String
	"""学习方案封面图片"""
	picture:String
	"""学习方案介绍内容ID"""
	commentId:String
	"""类型"""
	type:String
	"""年度"""
	year:String
	"""地区"""
	region:String
	"""报名开始时间"""
	registerBeginDate:DateTime
	"""报名结束时间"""
	registerEndDate:DateTime
	"""培训开始时间"""
	trainingBeginDate:DateTime
	"""培训结束时间"""
	trainingEndDate:DateTime
	"""是否提供重学"""
	provideRelearn:Boolean!
	"""扩展属性集合"""
	properties:[Property]
	"""简介内容"""
	intro:String
	"""培训须知"""
	notice:String
}
"""<AUTHOR> create 2022/5/25 16:14"""
input OneKeyPassRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.OneKeyPassRequest") {
	"""参训资格id"""
	qualificationId:String
	"""合格时间配置 1：按系统当前操作成功时间"""
	passTimeType:Int!
	"""合格元数据"""
	metaDataList:[MetaData]
}
"""分页查询培训方案批量更新导入任务数据请求信息"""
input PageLearningSchemeImportTaskRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.PageLearningSchemeImportTaskRequest") {
	"""任务名称"""
	taskName:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""执行时间（起始）"""
	executeStartTime:DateTime
	"""执行时间（终止）"""
	executeEndTime:DateTime
}
"""数据修复使用
	<AUTHOR>
"""
input RefreshSchemeConfigRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.RefreshSchemeConfigRequest") {
	schemeId:String
	configJson:String
}
"""预约方案校验请求
	<AUTHOR> create 2022/3/10 14:30
"""
input ReservingSchemeIssueValidateRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.ReservingSchemeIssueValidateRequest") {
	"""方案id"""
	schemeId:String!
	issueId:String
}
"""预约方案校验请求
	<AUTHOR> create 2022/3/10 14:30
"""
input ReservingSchemeValidateRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.ReservingSchemeValidateRequest") {
	"""方案id"""
	schemeId:String!
}
"""用户报名方案数据修复请求
	<AUTHOR>
"""
input UserEnrolmentSchemeRepairRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.UserEnrolmentSchemeRepairRequest") {
	"""用户id"""
	userId:String
	"""方案id"""
	schemeId:String
	"""报名来源类型
		@see StudentSourceTypes
	"""
	sourceType:String
	"""报名来源ID"""
	sourceId:String
}
type DuplicateReservingInfo @type(value:"com.fjhb.domain.properties.validators.learningscheme.exception.ReservingSchemeNotUniqueException$DuplicateReservingInfo") {
	id:String
	sourceType:String
	sourceId:String
	status:Int!
}
"""学习方案处理转台响应
	<AUTHOR>
"""
type LearningSchemeProcessStatusResponse @type(value:"com.fjhb.ms.learningscheme.v1.api.query.learningscheme.response.LearningSchemeProcessStatusResponse") {
	"""方案配置最后执行事务状态
		@see com.fjhb.domain.learningscheme.api.configure.consts.LearningSchemeConfigureTransactionStatus
	"""
	lastTransactionStep:Int!
	"""事务是否挂起"""
	hangUp:Boolean!
	"""是否重算中"""
	recalculating:Boolean!
	"""异常"""
	errors:[ResultResponse]
}
"""执行结果响应对象
	<AUTHOR>
"""
type ResultResponse @type(value:"com.fjhb.ms.learningscheme.v1.api.query.learningscheme.response.ResultResponse") {
	"""状态码"""
	code:String
	"""信息"""
	message:String
}
"""申请自动学习token响应
	<AUTHOR> create 2022/1/12 14:54
"""
type ApplyTrainingQualificationAutoLearningTokenResponse @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.response.ApplyTrainingQualificationAutoLearningTokenResponse") {
	"""重学token"""
	token:String
}
"""参训资格重新学习命令
	将清空所有学习记录和考核
	<AUTHOR> create 2022/1/12 14:54
"""
type ApplyTrainingQualificationRelearnTokenResponse @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.response.ApplyTrainingQualificationRelearnTokenResponse") {
	"""ID"""
	qualificationId:String
	"""学习方式id"""
	learningId:String
	"""重学token"""
	token:String
	"""培训结果.
		-1:未知，培训尚未完成
		1:培训合格
		0:培训不合格
		@see StudentTrainingResults
		@see StudentTrainingQualifiedEvent
		@see StudentTrainingUnqualifiedEvent
		@see StudentTrainingResultRevokedEvent
	"""
	trainingResult:Int!
}
type ExportUpdateLearningSchemeImportResultResponse @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.response.ExportUpdateLearningSchemeImportResultResponse") {
	"""导入批量修改方案信息执行结果文件地址"""
	fileUrl:String
}
"""批量更新培训方案导入任务数据信息"""
type LearningSchemeImportTaskResponse @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.response.LearningSchemeImportTaskResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""任务名称"""
	name:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""处理时间"""
	executingTime:DateTime
	"""结束（完成）时间"""
	completedTime:DateTime
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
}
"""各状态及执行结果对应数量"""
type EachStateCount @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.response.LearningSchemeImportTaskResponse$EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Int!
}
"""方案是否被事务方式处理结果集
	<AUTHOR>
"""
type ProcessedByTransactionResponse @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.response.ProcessedByTransactionResponse") {
	results:[ProcessedByTransactionResult]
}
"""方案是否被事务方式处理
	<AUTHOR>
"""
type ProcessedByTransactionResult @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.response.ProcessedByTransactionResult") {
	"""学习方案id"""
	schemeId:String
	"""是否被处理过"""
	isProcessed:Boolean!
}
"""预约方案校验响应
	<AUTHOR> create 2022/3/10 15:19
"""
type ReservingSchemeValidateResponse @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.response.ReservingSchemeValidateResponse") {
	"""正常=200.
		不可重复报名同一个班级 = 50001
		培训未开始 = 50002
		培训已结束 = 50003
		报名未开始 = 50004
		报名已结束 = 50005
		500 其他未定义异常
	"""
	code:String
	"""异常信息"""
	message:String
	"""重复预约信息
		不可重复报名同一个班级 = 50001返回
	"""
	duplicateReservingInfos:[DuplicateReservingInfo]
}

scalar List
type LearningSchemeImportTaskResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LearningSchemeImportTaskResponse]}
