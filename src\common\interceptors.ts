import SystemLog, { SystemLogType } from '@hbfe/jxjy-admin-components/src/system-event-logs/models/SystemLog'
import RootModule from '@/store/RootModule'
import DevToolsModule from '@/store/devtools/DevToolsModule'
import { ExtraConfig } from '@api/request'
import Authentication from '@api/service/common/authentication'
import Cfetch from '@api/service/common/utils/fetch-interceptors'
import { IdentityType } from '@hbfe-biz/biz-authentication/dist/enums/IdentityType'
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios'
import { Notification } from 'element-ui'
import gql from 'graphql-tag'
import { cloneDeep } from 'lodash'
import Vue from 'vue'
import ReportRequestTimeout from './util/ReportRequestTimeout'
import RefererUrls from '@api/service/common/authentication/modules/RefererUrls'
import BusinessClientTypeEnum from '@hbfe-biz/biz-authentication/src/enums/BusinessClientTypeEnum'
import BizAuthentication from '@api/service/common/authentication/Authentication'
import { default as CommonConfigCenter } from '@api/service/common/config/ConfigCenterModule'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
import Env from '@api/service/common/utils/Env'

const env = Env.proxyEnvStr

console.log('当前环境:' + env)

export let repeatConfig: any // 重发请求配置信息

let conditionResolver: any
let conditionPromise: any

// 初始化控制执行的Promise
const initConditionPromise = () => {
  conditionPromise = new Promise((resolve) => {
    conditionResolver = resolve
  })
}

/**
 * 在各种环境都需要开启，
 *  主要应用于统计模块
 */
axios.interceptors.response.use((response: AxiosResponse) => {
  const metadata = (response.config as ExtraConfig).metadata
  metadata.endTime = +new Date()
  const duration = metadata.endTime - metadata.startTime
  try {
    ReportRequestTimeout.upSlowRQEvent(duration, response.config.url)
  } catch (error) {
    console.log('上报爆炸啦')
  }
  const timestamp = response.headers['current-timestamp']
  RootModule.setCurrentSystemTime(timestamp)
  return response
})

// 函数用于触发条件并执行相关代码
const triggerConditionAndExecuteCode = async () => {
  // 执行相关代码，这里用setTimeout模拟异步操作
  if (RefererUrls.authUrl || RefererUrls.ssoUrl) {
    const $authentication = new BizAuthentication({
      authUrl: RefererUrls.authUrl,
      ssoUrl: RefererUrls.ssoUrl,
      request: axios,
      appKey: process.env.VUE_APP_KEY,
      prefix: BusinessClientTypeEnum.admin,
      thirdAuthUrl: RefererUrls.authUrl,
      service: location.hostname
    })
    Cfetch.interceptors.request.use((config: any) => {
      config.headers['App-Authentication'] = `Basic ${process.env.VUE_APP_KEY}`
      const accessToken = localStorage.getItem('admin.Access-Token')
      if (accessToken) {
        if (
          [IdentityType.proxy_identity, IdentityType.change_identity, IdentityType.refresh].includes(
            Vue.prototype.$authentication.identityType
          )
        ) {
          config.headers.Authorization = `Mship ${accessToken}`
        }
        if (!config?.body) {
          config.headers.Authorization = `Mship ${accessToken}`
        }
      }
      if (config.headers.Authorization) {
        delete config.headers['App-Service-Provider']
      }
      return config
    })
    Cfetch.interceptors.request.use((config: any) => {
      config.headers['App-Authentication'] = `Basic ${process.env.VUE_APP_KEY}`
      const accessToken = localStorage.getItem('admin.Access-Token')
      if (accessToken) {
        if (
          [IdentityType.proxy_identity, IdentityType.change_identity, IdentityType.refresh].includes(
            Vue.prototype.$authentication.identityType
          )
        ) {
          config.headers.Authorization = `Mship ${accessToken}`
        }
        if (!config?.body) {
          config.headers.Authorization = `Mship ${accessToken}`
        }
      }
      if (config.headers.Authorization) {
        delete config.headers['App-Service-Provider']
      }
      return config
    })
    const authResponse = (await $authentication.ssoRefreshToken()) as any
    if (authResponse.code != 200 || !authResponse.data?.access_token) {
      $authentication.removeToken()
      if (QueryManagerDetail.hasCategory(CategoryEnums.ztgly)) {
        window.location.replace('#/specialSubjectLogin')
      } else {
        window.location.replace('#/login')
      }
      localStorage.removeItem('lastFeedBackTime')
    }
  }
  conditionResolver()
}

let isLocked = false
let interval: any

const AppAuthenticationKey = 'App-Authentication'
axios.interceptors.request.use(async (config: AxiosRequestConfig) => {
  repeatConfig = config
  if (localStorage.getItem('admin.Access-Token')) {
    // 第一层保险 判断时间是否超过临界值 5分钟  300000毫秒  10分钟 600000毫秒
    // 当前时间
    const currentTime = new Date().getTime()
    // 有效时间
    const expiresInTime = Number(localStorage.getItem('admin.ExpiresIn'))
    const remainderExpiresInTime = expiresInTime - currentTime
    // 剩余时间超出5分钟小于10分钟
    if (!isLocked) {
      if (remainderExpiresInTime > -300000 && remainderExpiresInTime < 300000) {
        isLocked = true
        initConditionPromise()
        await triggerConditionAndExecuteCode()
        await conditionPromise
        isLocked = false
        // 执行刷新令牌 需要执行完才能继续走下去
      } else if (remainderExpiresInTime <= -300000) {
        // 执行删除令牌
        const $authentication = new BizAuthentication({
          authUrl: RefererUrls.authUrl,
          ssoUrl: RefererUrls.ssoUrl,
          request: axios,
          appKey: process.env.VUE_APP_KEY,
          prefix: BusinessClientTypeEnum.admin,
          thirdAuthUrl: RefererUrls.authUrl,
          service: location.hostname
        })
        $authentication?.removeToken()
        localStorage.removeItem('currentUnitId')
        try {
          await CommonConfigCenter.queryApplicationConfig()
        } catch (error) {
          console.error(error)
        }
        if (QueryManagerDetail.hasCategory(CategoryEnums.ztgly)) {
          window.location.replace('#/specialSubjectLogin')
        } else {
          window.location.replace('#/login')
        }
        return
        //中断操作 弹出弹窗
      }
    } else {
      await new Promise((resolve) => {
        if (!interval) {
          interval = setInterval(() => {
            if (!isLocked) {
              clearInterval(interval)
              interval = null
              resolve(config)
            }
          }, 500)
        }
      })
    }
  }
  config.headers[AppAuthenticationKey] = `Basic ${process.env.VUE_APP_KEY}`
  config.withCredentials = false
  return config
})

/**
 * 只有研发环境才能启动的配置
 */
if (process.env.NODE_ENV === 'development') {
  axios.interceptors.response.use((response: AxiosResponse) => {
    response.config['requestEnd'] = window.performance.now()
    const spentTime = response.config['requestEnd'] - response.config['requestStart']
    if (spentTime > DevToolsModule.logIfBigThan) {
      const systemLog = new SystemLog()
      systemLog.spentTime = parseInt(`${spentTime / 1000}`)
      systemLog.content = response.config.data
      systemLog.title = response.config.url
      systemLog.type = SystemLogType.requestTooLong
      // systemLog.router = router.currentRoute
      DevToolsModule.addSystemLog(systemLog)
      Notification({
        title: '警告',
        dangerouslyUseHTMLString: true,
        message: `
        <div>该请求耗时不符合系统性能指标</div>
        <div style="overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 220px;font-size: 12px;">请求路径：${systemLog.title}</div>
        <div>耗时【 ${systemLog.spentTime} s 】</div>
        `,
        type: 'warning'
      })
    }
    return response
  })

  axios.interceptors.request.use((config: AxiosRequestConfig) => {
    config['requestStart'] = window.performance.now()
    return config
  })

  /*
   mock 功能需要的拦截器
   */
  if (DevToolsModule.developmentSettings.openMock) {
    // 实现开发环境 mock 功能
    axios.interceptors.request.use((config: AxiosRequestConfig) => {
      try {
        const requestBody = config.data
        const queryBody = requestBody.query
        const schema = config['schemaName']
        const gqlTransfer = gql`
          ${queryBody}
        `
        if (gqlTransfer.definitions.length === 1) {
          const definition = gqlTransfer.definitions[0]
          const requestMethod = definition['selectionSet'].selections[0].name.value
          const findMockItem = DevToolsModule.getByQuery({
            schemaName: schema,
            method: requestMethod
          })
          if (findMockItem) {
            const requestPrefix = config.url.replace(/(.*?)\/gql/, '/gql').split('/')
            requestPrefix.pop()
            const requestTarget = [requestPrefix.join('/'), schema].join('/')
            if (config['isMicroService']) {
              const projectId = process.env.VUE_APP_YAPI_COMMON_MICRO_SERVICE_PROJECT_ID
              config.url = `/mock/${projectId}${requestTarget}?${requestMethod}`
            } else {
              config.url = `/mock/${process.env.VUE_APP_YAPI_PROJECT_ID}${requestTarget}?${requestMethod}`
            }
          }
        }
      } catch (e) {
        // ignore
        console.log(e)
      }

      return config
    })

    if (process.env.VUE_APP_DEVELOPMENT_WS_SERVER) {
      const ws = new WebSocket(process.env.VUE_APP_DEVELOPMENT_WS_SERVER)

      ws.onopen = function () {
        ws.send(
          JSON.stringify({
            type: 'loadMockList',
            appKey: process.env.VUE_APP_COMMON_MS_APP_KEY,
            ip: process.env.CURRENT_IP
          })
        )
      }
      // 响应onmessage事件:
      ws.onmessage = function (msg: MessageEvent) {
        try {
          const data = JSON.parse(msg.data || '{}')
          DevToolsModule.initMockList(data)
        } catch (e) {
          // 不重要
        }
      }
    }
  }
}

axios.interceptors.request.use((config: ExtraConfig) => {
  config.metadata = { startTime: +new Date(), endTime: 0 }
  // 判断如果是微服务，则请求微服务特有的路径
  if (config.isMicroService) {
    let port = Env.proxyPortStr
    let ENV = cloneDeep(env)
    const ev = localStorage.getItem('ENV')
    if (ev) {
      ENV = `.${ev}.`
      port = ':9443'
    }
    config.url = `${['https://api', ENV, '59iedu.com', port].join('')}${config.url}`
    config.headers['Service-Name'] = config.microServiceName
  }
  return config
})

axios.interceptors.request.use((config: AxiosRequestConfig) => {
  try {
    const gqls = gql`
      ${config.data.query}
    `
    const requestMethods: Array<string> = new Array<string>()
    gqls.definitions.forEach((definition: any) => {
      if (definition.name) {
        requestMethods.push(definition.name.value as string)
      } else {
        definition.selectionSet.selections.forEach((selection: any) => {
          if (selection.name) {
            requestMethods.push(selection.name.value)
          }
        })
      }
    })
    config.headers['Request-Meta'] = `${config.url?.split('/').pop()}.${requestMethods.join(',')}`
  } catch (e) {
    // 因为这边处理的错误不重要，所以没处理
  }
  return config
})

axios.interceptors.request.use((config: AxiosRequestConfig) => {
  const accessToken = Authentication.hasToken('admin')
  if (accessToken) {
    config.headers.Authorization = `Mship ${accessToken}`
  }
  if (config.headers.Authorization && !config['isUnAuthorize']) {
    delete config.headers['App-Service-Provider']
  }
  return config
})

Cfetch.interceptors.request.use((config: any) => {
  config.headers['App-Authentication'] = `Basic ${process.env.VUE_APP_KEY}`
  const accessToken = localStorage.getItem('admin.Access-Token')
  if (accessToken) {
    if (
      [IdentityType.proxy_identity, IdentityType.change_identity, IdentityType.refresh].includes(
        Vue.prototype.$authentication.identityType
      )
    ) {
      config.headers.Authorization = `Mship ${accessToken}`
    }
    if (!config?.body) {
      config.headers.Authorization = `Mship ${accessToken}`
    }
  }
  if (config.headers.Authorization) {
    delete config.headers['App-Service-Provider']
  }
  return config
})
