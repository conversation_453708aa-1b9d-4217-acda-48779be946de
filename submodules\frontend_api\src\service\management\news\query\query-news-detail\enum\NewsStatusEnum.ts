/*
 * @Description: 资讯状态
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-09 19:08:53
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 15:42:33
 */

enum NewsStatusEnum {
  ALL = -1,
  DRAFT,
  RELEASE
}

export { NewsStatusEnum }

class NewsStatus {
  static enum = NewsStatusEnum
  map = new Map()
  constructor() {
    this.map[NewsStatusEnum.ALL] = '全部'
    this.map[NewsStatusEnum.DRAFT] = '草稿'
    this.map[NewsStatusEnum.RELEASE] = '发布'
  }
}

export default NewsStatus
