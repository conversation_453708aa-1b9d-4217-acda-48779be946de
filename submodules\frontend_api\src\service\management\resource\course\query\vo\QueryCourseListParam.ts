import CourseStatus from '@api/service/common/enums/course/CourseStatus'
import { CourseRequest } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import TimeRequest from '@api/service/common/models/TimeRequest'
import CourseTransformStatus, { TransformStatusEnum } from '@api/service/common/enums/course/CourseTransformStatus'

/**
 * 分页查询类
 */
class QueryCourseListParam {
  /*
   名称
   */
  name?: string = ''

  /**
   * 状态
   * @see CourseStatusEnum
   */
  enable?: CourseStatus = new CourseStatus()

  /**
   * 课程转换状态
   */
  status?: CourseTransformStatus = new CourseTransformStatus()
  /**
   * 课程转换状态【适配于转换失败】
   */
  statuses = [] as TransformStatusEnum[]
  /*
   * 创建时间
   */
  createTime?: TimeRequest | Array<string> | string | any = []

  /*
   课程分类
   */
  categoryIdList?: Array<string> = new Array<string>()

  /**
   * 课件ID
   */
  coursewareId = ''

  /**
   * 课件供应商ID
   * 传-1代表查询未被授权的课程
   -2 为 已分配的
   */
  providerId = ''

  reset() {
    Object.assign(this, new QueryCourseListParam())
  }

  to() {
    const courseRequest = new CourseRequest()
    courseRequest.enable = this.enable.current
    courseRequest.name = this.name
    if (this.status.current == TransformStatusEnum.UNAVAILABLE) {
      courseRequest.statuses = [parseInt(TransformStatusEnum.EMPTY), parseInt(TransformStatusEnum.UNAVAILABLE)]
    } else {
      courseRequest.statuses = []
      courseRequest.status = this.status.current as unknown as number
    }
    courseRequest.createTimeBegin = this.createTime[0]
    courseRequest.createTimeEnd = this.createTime[1]
    courseRequest.categoryIdList = this.categoryIdList
    courseRequest.coursewareId = this.coursewareId
    courseRequest.supplierId = this.providerId
    return courseRequest
  }
}

export default QueryCourseListParam
