<route-meta>
  {
  "isMenu": true,
  "title": "批量打印证明",
  "sort": 10,
  "icon": "icon-piliangdayin"
  }
</route-meta>

<script lang="ts">
  import BatchPrint from '@hbfe/jxjy-admin-batchPrint/src/diff/jxgx/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    batchPrintCertify: [WXGLY],
    batchPrint: [WXGLY],
    preview: [WXGLY],
    downLoad: [WXGLY],
    studentBatchPrint: [WXGLY],
    axPrintType: [WXGLY],
    schemeQuery: [WXGLY],
    importListPrinting: [WXGLY]
  })
  export default class extends BatchPrint {}
</script>
