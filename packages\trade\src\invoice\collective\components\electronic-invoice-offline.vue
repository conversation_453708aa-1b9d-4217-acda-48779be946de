<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <!--条件查询-->
      <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
      <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
      <hb-search-wrapper :model="pageQueryParam" @reset="resetCondition">
        <el-form-item label="培训方案">
          <biz-learning-scheme-select v-model="hasSelectSchemeMode"></biz-learning-scheme-select>
        </el-form-item>
        <el-form-item label="期别名称" v-if="showPeriodName">
          <biz-period-select :scheme-id="hasSelectSchemeMode[0].id" v-model="pageQueryParam.periodId" />
        </el-form-item>
        <el-form-item label="批次号">
          <el-input v-model="pageQueryParam.orderNoList" clearable placeholder="请输入集体报名批次号" />
        </el-form-item>

        <el-form-item label="姓名">
          <el-input v-model="pageQueryParam.userName" clearable placeholder="请输入姓名" />
        </el-form-item>
        <el-form-item label="手机号">
          <el-input v-model="pageQueryParam.phone" clearable placeholder="请输入手机号" />
        </el-form-item>

        <el-form-item label="发票状态">
          <el-select v-model="pageQueryParam.invoiceStatusList" clearable filterable placeholder="请选择">
            <el-option v-for="item in blueInvoiceStatus" :label="item.name" :value="item.value" :key="item.value">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="冻结状态">
          <el-select v-model="pageQueryParam.invoiceFreezeStatus" clearable filterable placeholder="请选择">
            <el-option label="正常" :value="false"></el-option>
            <el-option label="冻结" :value="true"></el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="发票号">
          <el-input v-model="pageQueryParam.invoiceNoList" clearable placeholder="请输入发票号" />
        </el-form-item>

        <el-form-item label="申请开票时间">
          <double-date-picker
            :begin-create-time.sync="pageQueryParam.billStatusChangeTime.billing.begin"
            :end-create-time.sync="pageQueryParam.billStatusChangeTime.billing.end"
            begin-time-placeholder="请选择申请开票时间开始时间"
            end-time-placeholder="请选择申请开票时间结束时间"
          ></double-date-picker>
        </el-form-item>

        <el-form-item label="开票时间">
          <double-date-picker
            :begin-create-time.sync="pageQueryParam.billStatusChangeTime.success.begin"
            :end-create-time.sync="pageQueryParam.billStatusChangeTime.success.end"
            begin-time-placeholder="请选择开票开始时间"
            end-time-placeholder="请选择开票开始时间"
          ></double-date-picker>
        </el-form-item>

        <!-- <el-form-item label="专题">
          <el-select
            v-model="pageQueryParam.isFromSpecialSubject"
            clearable
            filterable
            placeholder="请选择订单是否来源专题"
          >
            <el-option :value="true" label="是"></el-option>
            <el-option :value="false" label="否"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="专题名称">
          <el-input v-model="pageQueryParam.specialSubjectName" clearable placeholder="请输入专题进行查询" />
        </el-form-item>
        <template slot="actions">
          <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
          <el-button @click="exportSpecialInvoice">导出列表数据</el-button>
          <el-button v-if="!isZtlogin" @click="importElectronicInvoice">导入电子发票</el-button>
        </template>
      </hb-search-wrapper>

      <!--表格-->
      <el-table stripe :data="pageData" v-loading="query.loading" max-height="500px" class="m-table">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="集体报名批次号" prop="associationId" min-width="220" fixed="left">
          <!-- <template>2112071509467489926</template> -->
          <template slot-scope="scope"
            >{{ scope.row.associationId || '-' }}
            <p>
              <el-tag type="success" size="mini" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag>
            </p>
          </template>
        </el-table-column>
        <el-table-column label="退款状态" min-width="130">
          <template slot-scope="scope">
            <el-badge
              is-dot
              :type="refundStatusMapType[scope.row.orderReturnStatus]"
              class="badge-status"
              v-if="refundStatusMapName[scope.row.orderReturnStatus]"
            >
              {{ refundStatusMapName[scope.row.orderReturnStatus] }}</el-badge
            >
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="付款金额(元)" width="140" align="right">
          <template slot-scope="scope">
            {{ scope.row.payAmount || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="开票金额(元)" width="140" align="right">
          <template slot-scope="scope">
            {{ scope.row.totalAmount || 0 }}
          </template>
        </el-table-column>
        <el-table-column label="购买人信息" min-width="240">
          <template slot-scope="scope">
            <p>姓名：{{ scope.row.name || '-' }}</p>
            <p>手机号：{{ scope.row.phone || '-' }}</p>
          </template>
        </el-table-column>
        <el-table-column label="发票抬头" min-width="300">
          <template slot-scope="scope"
            >【{{ invoiceTitleMapType[scope.row.titleType] }}】{{ scope.row.title }}</template
          >
        </el-table-column>
        <el-table-column label="统一社会信用代码" min-width="180">
          <template slot-scope="scope">
            {{ scope.row.taxpayerNo || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="申请开票时间" min-width="180">
          <template slot-scope="scope">
            {{ scope.row.applyForDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="发票状态" min-width="130">
          <template slot-scope="scope">
            <el-badge
              is-dot
              :type="invoiceStatusMapType[scope.row.invoiceStatus]"
              class="badge-status"
              v-if="invoiceStatusMapName[scope.row.invoiceStatus]"
            >
              {{ invoiceStatusMapName[scope.row.invoiceStatus] }}
            </el-badge>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column label="开票时间" min-width="180">
          <template slot-scope="scope">
            {{ scope.row.invoiceDate || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="发票号" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.invoiceNo || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="手机号 / 邮箱" min-width="220">
          <template slot-scope="scope">
            <p>{{ scope.row.invoiceFaceInfo.contactPhone || '-' }}</p>
            /
            <p>{{ scope.row.invoiceFaceInfo.contactEmail || '-' }}</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              v-if="scope.row.invoiceStatus === 0 && !isZtlogin && !scope.row.invoiceFreezeStatus"
              @click="processInvoiceDialog(scope.row)"
              >处理发票</el-button
            >
            <el-button
              type="text"
              size="mini"
              v-if="scope.row.invoiceStatus === 2 && !isZtlogin && !scope.row.invoiceFreezeStatus"
              @click="cancelleationDialog(scope.row)"
              >作废</el-button
            >
            <el-button type="text" size="mini" @click="invoiceLog(scope.row.invoiceId)">记录</el-button>
            <el-button
              type="text"
              size="mini"
              v-if="
                (scope.row.invoiceStatus === 0 || scope.row.invoiceStatus === 3) &&
                !isZtlogin &&
                !scope.row.invoiceFreezeStatus
              "
              @click="editInvoice(scope.row)"
              >修改发票信息</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
      <!--作废发票-->
      <el-drawer title="作废发票" :visible.sync="voidInvoice" size="600px" custom-class="m-drawer">
        <div class="drawer-bd">
          <el-row type="flex" justify="center">
            <el-col :span="18">
              <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                <el-form-item label="发票号：" class="is-text">{{ currentItem.invoiceNo }}</el-form-item>
                <el-form-item label="发票抬头：" class="is-text"
                  >【{{ currentItem.titleType === 1 ? '个人' : '单位' }}】{{ currentItem.title }}</el-form-item
                >
                <el-form-item class="m-btn-bar">
                  <el-button @click="voidInvoice = false">取消</el-button>
                  <el-button type="primary" @click="toInvoice">作废发票</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-drawer>
      <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb">导出成功，是否前往下载数据？</p>
            <p class="f-f13 f-mt5">下载入口：导出任务管理-集体报名增值税电子普通发票（线下开票）</p>
          </div>
        </div>
        <div slot="footer">
          <el-button @click="exportSuccessVisible = false">暂 不</el-button>
          <el-button type="primary" @click="goExportDownloadPage">前往下载</el-button>
        </div>
      </el-dialog>
      <el-dialog title="提示" :visible.sync="importSuccessVisible" width="450px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb">导入成功，是否前往下载数据？</p>
            <p class="f-f13 f-mt5">下载入口：导入任务查看-集体报名增值税电子普通发票（线下开票）</p>
          </div>
        </div>
        <div slot="footer">
          <el-button @click="importSuccessVisible = false">暂 不</el-button>
          <el-button type="primary" @click="goImportDownloadPage">前往下载</el-button>
        </div>
      </el-dialog>
      <el-drawer title="处理发票" :visible.sync="processInvoice" size="600px" custom-class="m-drawer">
        <div class="drawer-bd">
          <el-row type="flex" justify="center">
            <el-col :span="18">
              <el-form ref="elForm" :model="InvoiceObject" :rules="rules" label-width="auto" class="m-form f-mt20">
                <el-form-item label="发票号码：" prop="invoiceNo">
                  <el-input v-model="InvoiceObject.invoiceNo" placeholder="请输入发票号码" />
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button @click="processInvoice = false">取消</el-button>
                  <el-button type="primary" @click="confirm">确定</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-drawer>
      <!-- <edit-invoice-dialog :dialog-ctrl.sync="editInvoiceVisible" :invoice-id="invoiceId"></edit-invoice-dialog> -->
      <invoice-log-dialog ref="logDialogRef" :is-auto="false"></invoice-log-dialog>
      <edit-offline-invoice-dialog
        :dialog-ctrl.sync="editInvoiceVisible"
        :invoice-id="invoiceId"
        invoice-type="2"
      ></edit-offline-invoice-dialog>
      <import-invoice-dialog
        :import-dialog.sync="importInvoiceVisible"
        :invoice-type="3"
        @importSuccess="importSuccessVisible = true"
      ></import-invoice-dialog>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import { Query, UiPage } from '@hbfe/common'
  import { InvoiceStatusEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import EditInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/edit-invoice-dialog.vue'
  import InvoiceLogDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/invoice-log-dialog.vue'
  import { ElForm } from 'element-ui/types/form'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import offLinePageInvoiceVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import QueryOffLinePageInvoiceParam from '@api/service/management/trade/batch/invoice/query/vo/QueryOffLinePageInvoiceParam'
  import { OrderReturnStatusEnum, TitleTypeEnum } from '@api/service/management/trade/batch/invoice/enum/InvoiceEnum'
  import {
    BillStatusChangeTimeRequest,
    DateScopeRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import MutationOffLineInvoice from '@api/service/management/trade/batch/invoice/mutation/MutationOffLineInvoice'
  import EditOfflineInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/edit-offline-invoice-dialog.vue'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import ImportInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/collective/components/import-invoice-dialog.vue'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import QueryOffLineInvoiceInTrainingChannel from '@api/service/management/trade/batch/invoice/query/QueryOffLineInvoiceInTrainingChannel'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

  @Component({
    components: { EditOfflineInvoiceDialog, DoubleDatePicker, EditInvoiceDialog, InvoiceLogDialog, ImportInvoiceDialog }
  })
  export default class extends Vue {
    @Ref('elForm') elForm: ElForm
    @Ref('logDialogRef') logDialogRef: InvoiceLogDialog
    SaleChannelEnum = SaleChannelEnum

    select = ''
    input = ''
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    page: UiPage
    query: Query = new Query()
    form = {
      data1: ''
    }
    hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
    //查询入参
    pageQueryParam: QueryOffLinePageInvoiceParam = new QueryOffLinePageInvoiceParam()
    exportQueryParam: QueryOffLinePageInvoiceParam = new QueryOffLinePageInvoiceParam()
    pageData: Array<offLinePageInvoiceVo> = new Array<offLinePageInvoiceVo>()
    currentItem = new offLinePageInvoiceVo()
    //修改发票弹窗
    editInvoiceVisible = false
    //操作日志弹窗
    invoiceId = ''
    voidInvoice = false
    processInvoice = false
    //导出成功弹窗
    exportSuccessVisible = false
    //导入电子发票弹窗
    importInvoiceVisible = false
    //导入成功弹窗
    importSuccessVisible = false

    InvoiceObject = {
      invoiceNo: ''
    }
    queryOffLineInvoice: MutationOffLineInvoice = TradeModule.batchTradeBatchFactor.invoiceFactor.mutationOffLineInvoice
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    queryZtInvoice = new QueryOffLineInvoiceInTrainingChannel()
    @Watch('InvoiceObject', {
      deep: true,
      immediate: true
    })
    InvoiceObjectChange(val: any) {
      if (val) {
        console.log(val, 'val')
      }
    }
    rules = {
      invoiceNo: [
        {
          required: true,
          message: '请输入发票号码',
          trigger: ['change', 'blur']
        }
      ]
    }
    refundStatusMapType = {
      [OrderReturnStatusEnum.RETURNSING]: 'primary',
      [OrderReturnStatusEnum.RETURNSUCCESS]: 'success'
    }
    invoiceStatusMapType = {
      [InvoiceStatusEnum.NOTPTOOPEN]: 'info',
      [InvoiceStatusEnum.OPENING]: 'primary',
      [InvoiceStatusEnum.OPENERROR]: 'danger',
      [InvoiceStatusEnum.OPEMSUCCESS]: 'success'
    }
    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }
    blueInvoiceStatus = [
      {
        name: '待开票',
        value: InvoiceStatusEnum.NOTPTOOPEN
      },
      {
        name: '开票中',
        value: InvoiceStatusEnum.OPENING
      },
      {
        name: '开票成功',
        value: InvoiceStatusEnum.OPEMSUCCESS
      },
      {
        name: '开票失败',
        value: InvoiceStatusEnum.OPENERROR
      }
    ]
    invoiceStatusMapName = {
      [InvoiceStatusEnum.NOTPTOOPEN]: '待开票',
      [InvoiceStatusEnum.OPENING]: '开票中',
      [InvoiceStatusEnum.OPENERROR]: '开票失败',
      [InvoiceStatusEnum.OPEMSUCCESS]: '开票成功'
    }
    refundStatusMapName = {
      [OrderReturnStatusEnum.RETURNSING]: '退款中',
      [OrderReturnStatusEnum.RETURNSUCCESS]: '退款成功'
    }

    TrainingModeEnum = TrainingModeEnum

    constructor() {
      super()
      this.pageQueryParam.billStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.billStatusChangeTime.billing = new DateScopeRequest()
      this.pageQueryParam.billStatusChangeTime.success = new DateScopeRequest()
      if (this.isZtlogin) {
        this.page = new UiPage(this.doSearchZt, this.doSearchZt)
      } else {
        this.page = new UiPage(this.doSearch, this.doSearch)
      }
    }
    get showPeriodName() {
      return [this.TrainingModeEnum.mixed, this.TrainingModeEnum.offline].includes(
        this.hasSelectSchemeMode[0]?.trainingMode?.skuPropertyValueId
      )
    }
    // 培训方案入参
    @Watch('hasSelectSchemeMode', {
      deep: true
    })
    changeScheme(val: Array<HasSelectSchemeMode>) {
      this.pageQueryParam.periodId = ''
    }
    async doSearch() {
      this.query.loading = true
      if (this.hasSelectSchemeMode.length) {
        //培训方案
        this.pageQueryParam.commoditySkuIdList = this.hasSelectSchemeMode[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }

      const queryInvoice = TradeModule.batchTradeBatchFactor.invoiceFactor.queryOffLineInvoice
      try {
        this.pageData = await queryInvoice.offLinePageInvoiceInServicer(this.page, this.pageQueryParam)
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        this.query.loading = false
        this.exportQueryParam = Object.assign(new QueryOffLinePageInvoiceParam(), this.pageQueryParam)
      }
    }
    async doSearchZt() {
      this.query.loading = true
      if (this.hasSelectSchemeMode.length) {
        //培训方案
        this.pageQueryParam.commoditySkuIdList = this.hasSelectSchemeMode[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }

      const queryInvoice = TradeModule.batchTradeBatchFactor.invoiceFactor.queryOffLineInvoice
      try {
        this.pageData = await this.queryZtInvoice.offLinePageInvoiceInServicer(this.page, this.pageQueryParam)
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        this.query.loading = false
        this.exportQueryParam = Object.assign(new QueryOffLinePageInvoiceParam(), this.pageQueryParam)
      }
    }
    async resetCondition() {
      this.page.pageNo = 1
      this.pageQueryParam = new QueryOffLinePageInvoiceParam()
      this.pageQueryParam.billStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.billStatusChangeTime.billing = new DateScopeRequest()
      this.pageQueryParam.billStatusChangeTime.success = new DateScopeRequest()
      this.hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
      if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }
    //修改发票信息
    editInvoice(item: any) {
      if (item.invoiceFreezeStatus) {
        this.$message.warning('发票已冻结，无法修改！')
        return
      }
      this.invoiceId = item.invoiceId
      this.editInvoiceVisible = true
    }
    //发票操作记录
    invoiceLog(id: string) {
      this.logDialogRef.init(id)
    }
    cancelleationDialog(item: offLinePageInvoiceVo) {
      this.currentItem = item

      const h = this.$createElement
      this.$confirm('确认作废', {
        message: h('div', [h('p', '确认要作废该发票号？'), h('p', '确认作废后，请重新开票！')]),
        confirmButtonText: '确认作废',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.voidInvoice = true
        })
        .catch(() => {
          console.log('已取消确认作废')
        })
    }
    async toInvoice() {
      const result = await this.queryOffLineInvoice.invalidInvoice(this.currentItem.invoiceId)
      if (result.isSuccess()) {
        this.$message.success('作废发票成功')
        await this.doSearch()
        this.voidInvoice = false
      } else {
        this.$message.success('作废发票成功')
      }
    }
    processInvoiceDialog(item: offLinePageInvoiceVo) {
      this.processInvoice = true
      this.currentItem = item
    }
    async confirm() {
      this.elForm.validate(async (valid: boolean) => {
        if (valid) {
          const result = await this.queryOffLineInvoice.dealWithInvoice(
            this.currentItem.invoiceId,
            this.InvoiceObject.invoiceNo
          )
          if (result.isSuccess()) {
            this.$message.success('处理发票成功')
            await this.doSearch()
            this.processInvoice = false
          } else {
            this.$message.error('处理发票失败')
          }
        }
      })
    }
    // 导出列表数据
    async exportSpecialInvoice() {
      try {
        let res
        if (this.isZtlogin) {
          res = await this.offLinePageInvoiceInZtExport()
        } else {
          res = await this.offLinePageInvoiceInExport()
        }
        if (res) {
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }
    /**
     * 导出列表数据
     */
    async offLinePageInvoiceInExport() {
      return await TradeModule.batchTradeBatchFactor.invoiceFactor.queryOffLineInvoice.offLinePageInvoiceInExport(
        this.exportQueryParam
      )
    }
    /**
     * 导出列表数据
     */
    async offLinePageInvoiceInZtExport() {
      return await this.queryZtInvoice.offLinePageInvoiceInExport(this.exportQueryParam)
    }
    // 下载导出数据
    goExportDownloadPage() {
      this.exportSuccessVisible = false
      // this.$router.push('/training/task/export')
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportBatchNormalOfflineInvoice' }
      })
    }
    // 下载导入数据
    goImportDownloadPage() {
      this.importSuccessVisible = false
      this.$router.push({
        path: '/training/task/importtask',
        query: { type: '集体报名增值税电子普通发票（线下开票）' }
      })
    }
    importElectronicInvoice() {
      //导入电子发票
      this.importInvoiceVisible = true
    }
    async created() {
      if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }
  }
</script>
