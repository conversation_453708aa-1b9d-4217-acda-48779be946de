import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 子订单退款状态枚举
 */
export enum SubOrderRefundStatusEnum {
  // 1: 退款审核中
  Auditing = 1,
  // 2：退款中
  Refunding,
  // 3：退款成功
  Refund_Success,
  // 4：退款申请被拒绝
  Rejected,
  // 5：取消退款
  Cancel_Refund,
  // 6: 退货完成
  Return_Success
}

/**
 * @description 子订单退款状态枚举
 */
class SubOrderRefundStatusList extends AbstractEnum<SubOrderRefundStatusEnum> {
  static enum = SubOrderRefundStatusEnum
  constructor(status?: SubOrderRefundStatusEnum) {
    super()
    this.current = status
    this.map.set(SubOrderRefundStatusEnum.Auditing, '退款审核中')
    this.map.set(SubOrderRefundStatusEnum.Refunding, '退款中')
    this.map.set(SubOrderRefundStatusEnum.Refund_Success, '退款成功')
    this.map.set(SubOrderRefundStatusEnum.Rejected, '退款申请被拒绝')
    this.map.set(SubOrderRefundStatusEnum.Cancel_Refund, '取消退款')
    this.map.set(SubOrderRefundStatusEnum.Return_Success, '退货完成')
  }
}

export default new SubOrderRefundStatusList()
