import GenderTypes from '@api/service/common/enums/user/GenderTypes'
import RegionModelVo from '@api/service/management/user/query/student/vo/RegionModelVo'
import { Component, Vue } from 'vue-property-decorator'

export class IndustryOption {
  id?: string
  /*
    行业属性id，用于查询科目类别，专业
  */
  propertyId: string
  name: string
}

import { RegionModel } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import QueryLeaderPositionLevel from '@api/service/common/basic-data-dictionary/query/QueryLeaderPositionLevel'
import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
import LeaderPositionLevelVo from '@api/service/common/basic-data-dictionary/query/vo/LeaderPositionLevelVo'
import OnlineSchoolConfigModule from '@api/service/common/online-school-config/OnlineSchoolConfigModule'
import AccountSourceTypes from '@api/service/diff/management/jxgx/user/student/enums/AccountSourceTypes'

@Component
export default class extends Vue {
  placeChannelOptions: Array<LeaderPositionLevelVo>
  // 查询学员加密值实例；修改信息时必传的入参
  studentFieldVerifyTokenObj = OnlineSchoolConfigModule.queryOnlineSchoolConfigFactory.QueryStudentFieldVerifyToken
  trainingCategoryObj = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryTrainingCategory

  // 行业数组
  industryOptions = new Array<IndustryOption>()
  // 人社行业属性id
  rsIndustryPropertyId = ''
  // 卫生行业属性id
  wsIndustryPropertyId = ''
  // 工勤行业属性id
  gqIndustryPropertyId = ''
  // 教师行业属性id
  teacherIndustryPropertyId = ''
  ysIndustryPropertyId = ''

  // industryId
  industryId = ''
  wsIndustryId = ''
  gqIndustryId = ''
  teacherIndustryId = ''
  ysIndustryId = ''

  async created() {
    await this.queryLeaderPositionLevelList()
    // 优化 屏蔽重复请求 只在选中用户之后进行token请求
    // await this.queryStudentFieldVerifyToken()
    await this.queryIndustryList()
  }

  // 获取行业列表
  async queryIndustryList() {
    const res = await QueryIndustry.queryIndustry()
    const list = res.isSuccess() ? QueryIndustry.industryList : ([] as IndustryVo[])
    this.industryOptions =
      list?.map((el: IndustryVo) => {
        return {
          id: el.id,
          propertyId: el.propertyId,
          name: el.name
        }
      }) || []
    this.getIndustryRsId(this.industryOptions)
    this.getIndustryWsId(this.industryOptions)
    this.getIndustryGqId(this.industryOptions)
    this.getIndustryTeacherId(this.industryOptions)
    this.getIndustryMedicineId(this.industryOptions)
  }

  // 获取加密值
  async queryStudentFieldVerifyToken() {
    try {
      await this.studentFieldVerifyTokenObj.queryStudentFieldVerifyToken()
    } catch (e) {
      this.$message.error('获取加密值请求失败！')
      console.log(e)
    }
  }

  // 获取职称数据
  async queryLeaderPositionLevelList() {
    const res = await QueryLeaderPositionLevel.queryLeaderPositionLevel()
    if (res.isSuccess()) {
      this.placeChannelOptions = QueryLeaderPositionLevel.leaderPositionLevelList
    } else {
      this.$message.error('职称等级列表获取失败！')
    }
  }
  // 获取人社行业属性id，查询培训类别
  getIndustryRsId(data: IndustryOption[]) {
    const res = data.find((item) => item.name === '人社行业')
    this.rsIndustryPropertyId = res?.propertyId
    this.industryId = res?.id
  }

  getIndustryWsId(data: IndustryOption[]) {
    const res = data.find((item) => item.name === '职业卫生行业')
    this.wsIndustryPropertyId = res?.propertyId
    this.wsIndustryId = res?.id
  }

  getIndustryGqId(data: IndustryOption[]) {
    const res = data.find((item) => item.name === '工勤行业')
    this.gqIndustryPropertyId = res?.propertyId
    this.gqIndustryId = res?.id
  }
  getIndustryTeacherId(data: IndustryOption[]) {
    const res = data.find((item) => item.name === '教师行业')
    this.teacherIndustryPropertyId = res?.propertyId
    this.teacherIndustryId = res?.id
  }

  //   药师行业
  getIndustryMedicineId(data: IndustryOption[]) {
    const res = data.find((item) => item.name === '药师行业')
    this.ysIndustryPropertyId = res?.propertyId
    this.ysIndustryId = res?.id
  }

  getBindWXStatus(val: boolean) {
    if (val) {
      return '是'
    } else {
      return '否'
    }
  }

  getSourceTypeName(val: number) {
    const res = AccountSourceTypes.map.get(val) || '-'
    return res
  }

  // 根据职称id返回职称
  getPositionLevelNameById(id: string) {
    const res = this.placeChannelOptions?.find((item) => item.id === id)
    if (res) {
      return res?.name
    }
    return '-'
  }

  transformGenderTypeToName(val: number) {
    const res = GenderTypes.map.get(val)
    return res
  }

  // 转换单位地区id
  getRegionIdsFromRegionModel(region: RegionModelVo) {
    const temp = new Array<string>()
    if (region?.provinceId) {
      temp.push(region.provinceId)
    }
    if (region?.cityId) {
      temp.push(region.cityId)
    }
    if (region?.countyId) {
      temp.push(region.countyId)
    }
    const res = temp?.length > 0 ? temp : []
    return res
  }

  // 转换单位地区
  getRegionNameFromRegionModel(region: RegionModel) {
    const temp = new Array<string>()
    if (region?.provinceName) {
      temp.push(region.provinceName)
    }
    if (region?.cityName) {
      temp.push(region.cityName)
    }
    if (region?.countyName) {
      temp.push(region.countyName)
    }
    return temp.join('-') || '-'
  }
}
