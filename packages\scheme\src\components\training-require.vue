<template>
  <el-card shadow="never" class="m-card f-mb15">
    <div slot="header" class=""><span class="tit-txt">培训要求</span></div>
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form ref="form" :model="commodityInfo" label-width="150px" class="m-form f-mt10">
          <el-form-item label="考核规则：" required v-show="assessmentRuleList.length">
            <p v-show="enableCourseLearning">
              {{ assessmentRuleNo('courseLearning') }}. 课程学习要求不低于
              <i class="f-cr">{{ schemeRequirePeriodTotal }}</i> 学时。
            </p>
            <div v-show="enableCourseLearning" class="f-ml15">
              <p>① 每门课程学习进度=100%</p>
              <p v-show="courseQuizPagerStandard">
                ② 课程测验纳入考核，每门课程学习进度达
                <i class="f-cr"
                  >{{ commodityInfo.learningTypeModel.courseLearning.quizConfigModel.minCourseSchedule }}%</i
                >
                可参加，测验及格分 ≥
                <i class="f-cr">{{ commodityInfo.learningTypeModel.courseLearning.quizConfigModel.passScore }}</i>
                分，次数不限次。
              </p>
            </div>
            <p :class="enableCourseLearning ? 'f-mt10' : ''" v-show="enableExamLearning">
              {{ assessmentRuleNo('exam') }}.
              {{
                commodityInfo.learningTypeModel.exam.isExamAssessed ? '考试纳入考核' : '班级考试不纳入考核'
              }}，班级考试成绩 ≥
              {{ commodityInfo.learningTypeModel.exam.examPassScore }}
              分，{{ commodityInfo.learningTypeModel.exam.isExamAssessed ? '视为通过' : '考试合格' }}
            </p>
            <div v-show="enableExperienceLearning">
              {{ assessmentRuleNo('learningExperience') }}. 学习心得纳入考核
              <div class="f-ml15">
                <p>① 各项学习心得要求以具体配置为准</p>
                <p>
                  ②学习心得纳入考核，至少参加
                  <i class="f-cr">{{ enableExperiencejoinCount }}</i> 个心得，且每项心得均为通过。
                </p>
              </div>
            </div>
            <p v-show="questionnaireStandard">
              {{ assessmentRuleNo('questionnaire') }}. 调研问卷纳入考核，按具体问卷要求提交。<el-button
                type="text"
                class="f-ml10"
                @click="uiConfig.dialog.questionnaireAssessVisible = true"
                >[查看详情]</el-button
              >
            </p>
            <p v-show="issuePeriodStandard">
              {{ assessmentRuleNo('issue') }}. 培训期别：至少完成一个期别并考核通过。<el-button
                type="text"
                class="f-ml10"
                @click="uiConfig.dialog.issueAssessVisible = true"
                >[查看详情]</el-button
              >
            </p>
          </el-form-item>

          <el-form-item label="获得学时：" required>
            考核通过后将获得
            <el-input
              v-model="commodityInfo.trainClassBaseInfo.period"
              size="small"
              @change="handlePeriodChange"
              class="input-num f-mlr5"
            />
            学时
          </el-form-item>
          <slot
            name="hasLearningResult"
            :routerMode="routerMode"
            :recalculating="recalculating"
            :isIntelligenceLearning="isIntelligenceLearning"
            :commodityInfo="commodityInfo"
          >
            <el-form-item label="培训成果：" v-if="!(routerMode === 3 && (recalculating || isIntelligenceLearning))">
              <el-radio-group
                v-model="commodityInfo.trainClassBaseInfo.hasLearningResult"
                @change="handleHasLearningResult"
              >
                <el-radio :label="true">提供培训证明，达到培训要求后可打印</el-radio>
                <el-radio :label="false">不提供培训证明</el-radio>
              </el-radio-group>
            </el-form-item>
          </slot>
          <slot
            name="certificateTemplate"
            :routerMode="routerMode"
            :recalculating="recalculating"
            :isIntelLigenceLearning="isIntelligenceLearning"
            :commodityInfo="commodityInfo"
            :learningResultName="learningResultName"
            :cLosePrintTempLate="closePrintTemplate"
          >
            <!--选择提供证明后显示-->
            <el-form-item label="培训证明模板：">
              <!--选择后 隐藏-->
              <template v-if="!(routerMode === 3 && (recalculating || isIntelligenceLearning))">
                <el-button
                  size="small"
                  type="primary"
                  class="mr-10"
                  plain
                  v-show="!commodityInfo.trainClassBaseInfo.learningResultId"
                  :disabled="!commodityInfo.trainClassBaseInfo.hasLearningResult"
                  @click="chooseCertificateTemplate"
                >
                  选择模板
                </el-button>
                <!--选择后 出现-->
                <p v-show="commodityInfo.trainClassBaseInfo.learningResultId">
                  {{ learningResultName }}
                  <el-button type="text" class="f-ml20" @click="chooseCertificateTemplate">替换模板</el-button>
                </p>
              </template>
              <el-checkbox
                class="f-mt5 f-show"
                v-model="closePrintTemplate"
                @change="handleClosePrintTemplate"
                :disabled="!commodityInfo.trainClassBaseInfo.hasLearningResult"
              >
                不开放学员和集体报名人员打印，仅管理员可打印
              </el-checkbox>
            </el-form-item>
          </slot>
        </el-form>
      </el-col>
    </el-row>
    <!--选择培训证明-->
    <el-drawer
      title="选择培训证明"
      :visible.sync="uiConfig.dialog.chooseCertificateVisible"
      size="1200px"
      custom-class="m-drawer"
    >
      <div class="drawer-bd">
        <!--表格-->
        <el-table stripe :data="templateList" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="模板名称" min-width="240" prop="name">
            <!-- <template>模板名称模板名称模板名称</template> -->
          </el-table-column>
          <el-table-column label="所属行业" width="180" align="center" prop="belongsIndustryName">
            <!-- <template>建设</template> -->
          </el-table-column>
          <el-table-column label="模板说明" min-width="240" prop="describe">
            <!-- <template>模板说明模板说明模板说明模板说明模板说明</template> -->
          </el-table-column>
          <el-table-column label="创建时间" min-width="180" prop="createdTime">
            <!-- <template>2020-11-11 12:20:20</template> -->
          </el-table-column>
          <el-table-column label="查看" width="80" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="previewTemplate(scope.row)">预览</el-button>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-radio
                v-model="templateId"
                :label="scope.row.id"
                @change="radioChange(scope.row)"
                :disabled="scope.row.available === false"
                >选择</el-radio
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="cancelChooseCertificateTemplate">取消</el-button>
        <el-button type="primary" @click="confirmChooseCertificateTemplate">确定</el-button>
      </div>
    </el-drawer>
    <questionnaire-assess
      :questionnaire-assess.sync="uiConfig.dialog.questionnaireAssessVisible"
      :questionnaireList="questionnaireList"
      :trainClassBaseInfo="commodityInfo.trainClassBaseInfo"
    ></questionnaire-assess>
    <issue-assess
      :issue-assess.sync="uiConfig.dialog.issueAssessVisible"
      :questionnaire="commodityInfo.learningTypeModel.questionnaire"
      :issue="commodityInfo.learningTypeModel.issue"
      :trainClassBaseInfo="commodityInfo.trainClassBaseInfo"
    ></issue-assess>
  </el-card>
</template>

<script lang="ts">
  import { Component, Prop, PropSync, Vue } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
  import TrainingCertificateModule from '@api/service/management/personal-leaning/TrainingCertificateModule'
  import CertificateTemplateResponseVo from '@api/service/management/personal-leaning/query/vo/CertificateTemplateResponseVo'
  import CreateSchemeUIModule from '@/store/modules-ui/scheme/CreateSchemeUIModule'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import QuestionnaireAssess from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/questionnaire-assess.vue'
  import IssueAssess from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/issue-assess.vue'
  import Questionnaire from '@hbfe/jxjy-admin-scheme/src/components/questionnaire.vue'
  import { QuestionnaireAppliedRangeTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
  import { cloneDeep } from 'lodash'

  @Component({
    components: { Questionnaire, IssueAssess, QuestionnaireAssess }
  })
  export default class extends Vue {
    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number

    /**
     * 商品信息 - 双向绑定
     */
    @PropSync('commodity', { type: Object })
    commodityInfo!: MutationCreateTrainClassCommodity
    /**
     * 是否重算中
     */
    @Prop({ type: Boolean, default: false })
    recalculating: boolean
    /**
     * 是否智能学习中
     */
    @Prop({ type: Boolean, default: false })
    isIntelligenceLearning: boolean

    templateId = ''

    page: UiPage
    query: Query = new Query()
    /**
     * 获取证明模板类
     */
    queryCertificateTemplateList = TrainingCertificateModule.queryTrainingCertificateFactory.certificateTemplate
    templateList = new Array<CertificateTemplateResponseVo>()
    // 培训证明模板名称
    provisionalName = ''
    learningResultName = ''
    // 不开放证明打印
    closePrintTemplate = false
    // 是否支持整班重学
    enableRelearn = false
    /**
     *  ui相关的变量控制
     */
    uiConfig = {
      // 对话框是否展示
      dialog: {
        // 选择培训证明模板 - 选课规则
        chooseCertificateVisible: false,
        //   问卷考核
        questionnaireAssessVisible: false,
        //   期别考核
        issueAssessVisible: false
      }
    }

    /**
     * 是否勾选课程学习
     */
    get enableCourseLearning() {
      return this.commodityInfo.learningTypeModel.courseLearning.isSelected
    }

    /**
     * 是否勾选考试
     */
    get enableExamLearning() {
      return this.commodityInfo.learningTypeModel.exam.isSelected
    }

    /**
     * 是否学习心得被纳入考核 并勾选
     */
    get enableExperienceLearning() {
      return (
        this.commodityInfo.learningTypeModel.learningExperience.isExamine &&
        this.commodityInfo.learningTypeModel.learningExperience.isSelected
      )
    }

    /**
     * 是否学习心得被纳入考核
     */
    get enableExperiencejoinCount() {
      return this.commodityInfo.learningTypeModel.learningExperience.joinCount
    }

    /**
     * 学习期别是否有考核
     */
    get issuePeriodStandard() {
      return this.commodityInfo.learningTypeModel.issue.isSelected
    }

    /**
     * 问卷是否有线上或方案级
     */
    get questionnaireStandard() {
      return this.commodityInfo.learningTypeModel.questionnaire.isSelected && this.questionnaireList.length
    }
    /**
     * 课后测验是否纳入考核
     */
    get courseQuizPagerStandard() {
      return this.commodityInfo.learningTypeModel.courseLearning.courseQuizPagerStandard
    }

    /**
     * 培训方案类型，1-选课规则，2-自主选课
     */
    get schemeType() {
      return CreateSchemeUIModule.schemeType
    }

    /**
     * 获取培训方案要求总学时
     */
    get schemeRequirePeriodTotal() {
      /** 选课规则 */
      if (this.schemeType == 1) {
        const compulsoryRequirePeriod = Number(
          this.commodityInfo.learningTypeModel.courseLearning.compulsoryRequirePeriod
        )
        const electiveRequirePeriod = Number(this.commodityInfo.learningTypeModel.courseLearning.electiveRequirePeriod)
        return CalculatorObj.add(compulsoryRequirePeriod, electiveRequirePeriod)
      }
      /** 自主选课 */
      if (this.schemeType == 2) {
        return Number(this.commodityInfo.learningTypeModel.courseLearning.requirePeriod)
      }
      return 0
    }

    /**
     * 考核规则数组
     */
    get assessmentRuleList() {
      const ruleList: string[] = []
      if (this.enableCourseLearning) {
        ruleList.push('courseLearning')
      }
      if (this.enableExamLearning) {
        ruleList.push('exam')
      }
      if (this.enableExperienceLearning) {
        ruleList.push('learningExperience')
      }
      if (this.questionnaireStandard) {
        ruleList.push('questionnaire')
      }
      if (this.issuePeriodStandard) {
        ruleList.push('issue')
      }
      return ruleList.map((item, index) => {
        return {
          type: item,
          no: index + 1
        }
      })
    }

    /**
     * 考核规则序号
     */
    get assessmentRuleNo() {
      return (type: string) => {
        return this.assessmentRuleList.find((item) => item.type === type)?.no
      }
    }
    /**
     * 列表显示及方案问卷
     */
    get questionnaireList() {
      return this.commodityInfo.learningTypeModel.questionnaire.questionnaireConfigList.filter(
        (item) =>
          [QuestionnaireAppliedRangeTypeEnum.scheme, QuestionnaireAppliedRangeTypeEnum.online_course].includes(
            item.appliedRangeType
          ) && item.isAssessed
      )
    }
    constructor() {
      super()
      this.page = new UiPage(this.pageCertificateTemplate, this.pageCertificateTemplate)
      this.page.pageNo = 1
      this.page.pageSize = 10
    }

    async created() {
      if (this.routerMode === 1) {
        // 默认提供培训证明
        this.commodityInfo.trainClassBaseInfo.hasLearningResult = true
        // 设置关闭证明打印的值
        this.commodityInfo.trainClassBaseInfo.openPrintTemplate = true
        this.closePrintTemplate = !this.commodityInfo.trainClassBaseInfo.openPrintTemplate
      }
      const response = await this.queryCertificateTemplateList.queryCertificateTemplateList(this.page)
      this.templateList = cloneDeep(response.data)
      const page = cloneDeep(this.page)
      if (this.page?.totalPageSize && this.page?.totalPageSize > 1) {
        for (let i = 2; i <= this.page?.totalPageSize; i++) {
          page.pageNo = i
          const temp = await this.queryCertificateTemplateList.queryCertificateTemplateList(page)
          if (temp?.data?.length) {
            response.data.push(...temp.data)
          }
        }
      }
      if (this.commodityInfo.trainClassBaseInfo.learningResultId) {
        for (let i = 0; i < response.data.length; i++) {
          const element = response.data[i]
          if (this.commodityInfo.trainClassBaseInfo.learningResultId == element.id) {
            this.provisionalName = element.name
            this.learningResultName = element.name
          }
        }
        this.closePrintTemplate = !this.commodityInfo.trainClassBaseInfo.openPrintTemplate
      }
      console.group(
        '%c%s',
        'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#32FFFF, #fff)',
        'this.commodityInfo调试输出'
      )

      console.log(this.commodityInfo.trainClassBaseInfo.learningResultId)
      console.log(this.templateList)
      console.count('this.commodityInfo输出次数')
      console.groupEnd()
    }
    /**
     * 增加学时提示
     */
    handlePeriodChange() {
      if (/\./.test(this.commodityInfo.trainClassBaseInfo.period.toString())) {
        this.$message.warning('请输入整数')
      }
    }

    /**
     * 考试次数
     */
    get examAllowCountConfigure() {
      return this.commodityInfo.learningTypeModel.exam.allowCount === -1
        ? '不限'
        : this.commodityInfo.learningTypeModel.exam.allowCount
    }

    /**
     * 响应UI设置是否提供证明
     */
    handleHasLearningResult(val: boolean) {
      if (!val) {
        this.commodityInfo.trainClassBaseInfo.learningResultId = ''
        this.learningResultName = ''
        this.commodityInfo.trainClassBaseInfo.openPrintTemplate = true
        this.closePrintTemplate = false
      }
    }

    /**
     * 响应UI设置是否关闭证明打印
     */
    handleClosePrintTemplate(val: boolean) {
      this.commodityInfo.trainClassBaseInfo.openPrintTemplate = !val
    }
    /**
     * 选择培训证明模板
     */
    async chooseCertificateTemplate() {
      this.uiConfig.dialog.chooseCertificateVisible = true
    }
    previewTemplate(item: CertificateTemplateResponseVo) {
      console.group(
        '%c%s',
        'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#32FFFF, #fff)',
        'item调试输出'
      )
      console.log(item)
      console.count('item输出次数')
      console.groupEnd()
      window.open('/mfs/Cooper/templates/' + item.previewUrl, '_blank')
    }

    radioChange(value: CertificateTemplateResponseVo) {
      console.group(
        '%c%s',
        'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#32FFFF, #fff)',
        'value调试输出'
      )
      console.log(value)
      console.count('value输出次数')
      console.groupEnd()
      this.provisionalName = value.name
    }
    /**
     * 取消选择 - 选择培训证明模板
     */
    cancelChooseCertificateTemplate() {
      this.uiConfig.dialog.chooseCertificateVisible = false
    }

    /**
     * 确认选择 - 选择培训证明模板
     */
    confirmChooseCertificateTemplate() {
      this.uiConfig.dialog.chooseCertificateVisible = false
      this.commodityInfo.trainClassBaseInfo.learningResultId = this.templateId
      this.learningResultName = this.provisionalName
    }

    /**
     * 培训证明模板分页方法
     */
    async pageCertificateTemplate() {
      const response = await this.queryCertificateTemplateList.queryCertificateTemplateList(this.page)
      this.templateList = response.data
    }

    /**
     * 表单校验 - 培训要求配置
     */
    validateForm() {
      let isValid = false
      if (!this.commodityInfo.trainClassBaseInfo.period) {
        this.$message.error('请输入获得学时')
        return isValid
      }
      if (this.commodityInfo.learningTypeModel.exam.isSelected) {
        if (!this.commodityInfo.learningTypeModel.exam.examPassScore) {
          this.$message.error('请输入班级考试考核成绩')
          return isValid
        }
      }
      if (this.commodityInfo.trainClassBaseInfo.hasLearningResult) {
        if (!this.commodityInfo.trainClassBaseInfo.learningResultId) {
          this.$message.error('请选择培训证明模板')
          return isValid
        }
      }
      isValid = true
      return isValid
    }
  }
</script>

<style scoped>
  .mr-10 {
    margin-right: 10px;
  }
  /*去除input为number类型时的上下加减箭头 */
  /* google、safari */
  ::v-deep input::-webkit-outer-spin-button,
  ::v-deep input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
    margin: 0;
  }
  /* 火狐 */
  ::v-deep input[type='number'] {
    -moz-appearance: textfield;
  }
</style>
