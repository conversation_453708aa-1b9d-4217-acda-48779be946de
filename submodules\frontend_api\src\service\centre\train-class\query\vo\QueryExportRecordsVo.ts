import { ImportTaskQueryResponse } from '@api/ms-gateway/ms-certificate-v1'
import ExamResultStatus from '@api/service/centre/train-class/enum/ProcessResultStatus'
import { UserJobLogResponse } from '@api/platform-gateway/fxnl-data-export-gateway-forestage'
export default class QueryExportRecordsVo {
  /**
   * 任务id
   */
  maskId = ''
  /**
   * 任务名称
   */
  name = ''
  /**
   * 执行结果
   0 导出中 1 导出成功 2 导出失败 3 就绪失败
   */
  processResult = 0
  /**
   * 导出结果
   */
  exportResults = ''
  /**
   * 导出任务创建时间
   */
  ceratedTime = ''
  /**
   * 压缩包路径
   */
  zipPath = ''
  /**
   * 导出数量
   */
  count = 0
  /**
   * 导入进度 1-导入成功 2-导入中 3-导入失败 4-部分成功
   */
  importProgress = 0

  static from(dto: ImportTaskQueryResponse) {
    const vo = new QueryExportRecordsVo()
    vo.maskId = dto.id
    vo.name = dto.name
    vo.processResult = dto.processResult
    vo.exportResults = ExamResultStatus.map.get(dto.processResult)
    vo.ceratedTime = dto.ceratedTime
    vo.zipPath = dto.zipPath
    vo.count = vo.processResult === 1 ? dto.eachStateCounts?.find((item) => item.result === 1)?.count : 0
    if (dto.processResult != 1 && dto.taskState == 3 && [1, 3].includes(dto.importProgress)) {
      vo.importProgress = 3
    } else if (dto.processResult != 1 && dto.taskState != 3) {
      vo.importProgress = 2
    } else if (dto.processResult != 1 && dto.taskState == 3 && dto.importProgress == 4) {
      vo.importProgress = 4
    } else {
      vo.importProgress = 1
    }
    return vo
  }

  static fromStudy(dto: UserJobLogResponse) {
    const vo = new QueryExportRecordsVo()
    vo.name = dto.jobName
    switch (dto.jobState) {
      case 'executing':
        vo.processResult = 0
        break
      case 'executed':
        vo.processResult = 1
        break
      case 'fail':
        vo.processResult = 2
        break
    }
    vo.exportResults = ExamResultStatus.map.get(vo.processResult)
    vo.ceratedTime = dto.beginTime
    vo.zipPath = dto.exportFilePath
    return vo
  }
}
