import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 方案开通类型枚举
 * self_apply 个人报名
 * collectivity_apply 集体报名
 * manager_import 管理员导入
 */
export enum SchemeOpenTypeEnum {
  self_apply = 1,
  collectivity_apply = 2,
  manager_import = 3
}

/**
 * @description 方案开通类型
 */
class SchemeOpenType extends AbstractEnum<SchemeOpenTypeEnum> {
  static enum = SchemeOpenTypeEnum

  constructor(status?: SchemeOpenTypeEnum) {
    super()
    this.current = status
    this.map.set(SchemeOpenTypeEnum.self_apply, '个人报名')
    this.map.set(SchemeOpenTypeEnum.collectivity_apply, '集体报名')
    this.map.set(SchemeOpenTypeEnum.manager_import, '管理员导入')
  }
}

export default new SchemeOpenType()
