<route-meta>
  {
  "title": "药师行业执业类别-级联选择器"
  }
  </route-meta>
<template>
  <el-cascader
    v-model="editInputValue"
    clearable
    :options="selectorList"
    placeholder="请选择执业类别"
    :props="props"
    :multiple="true"
    separator="-"
    @change="valChange"
    class="form-l"
  />
</template>

<script lang="ts">
  import { Vue, Component, Mixins, Prop, PropSync, Watch } from 'vue-property-decorator'
  import QueryGrade from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryGrade'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'
  import CommonSkuMixins from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSkuMixins'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

  @Component
  // export default class extends Mixins(CommonSkuMixins) {
  export default class extends Vue {
    // 修改的值
    editInputValue: Array<number> = []
    // 输入框显隐
    isShowEditbox = false
    // 修改后的值
    newValue = ''
    props = {}
    QueryGrade = QueryGrade

    setProps() {
      this.props = {
        label: 'name',
        value: 'propertyId',
        children: 'children',
        leaf: 'leaf',
        multiple: true
        // checkStrictly: true
      }
    }

    @PropSync('learningPhase', {
      type: Number,
      default: 0
    })
    learningPhaseSync: number
    @PropSync('disciplin', {
      type: Number,
      default: 0
    })
    disciplinSync: number

    // 输入框提示语
    @Prop({
      type: String,
      default: '请选择执业类别'
    })
    placeholder: string

    @Prop({
      type: Array,
      default: []
    })
    value: Array<number>

    //行业属性Id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    //行业Id
    @Prop({
      type: String,
      default: ''
    })
    industryId: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valChange(val: Array<number>) {
      this.editInputValue = val
      this.learningPhaseSync = this.editInputValue[0]
      this.disciplinSync = this.editInputValue[1]
    }

    // 修改的值
    @Watch('editInputValue', {
      deep: true,
      immediate: true
    })
    inputValueChange(val: Array<number>) {
      this.$emit('input', val)
      this.editInputValue = val
    }

    /**
     * 后续维护补充类型
     */
    selectorList: Array<any> = []

    isShowEditBtn() {
      this.isShowEditbox = true
      this.editInputValue = this.value || []
    }

    async created() {
      this.setProps()
      this.selectorList = await QueryPersonIndustry.getCertificatesType(this.industryId)

      await Promise.all(
        this.selectorList.map(async (item) => {
          const res = await QueryPersonIndustry.getPractitionerCategory(item.propertyId)

          res?.map((ite) => {
            ite['leaf'] = true
          })
          if (res?.length) {
            item.leaf = false
            // item.children = res
            this.$set(item, 'children', res)
          } else {
            item.leaf = true
          }
        })
      )
      const param = new TrainingPropertyResponse()
      param.propertyId = '-1'
      param.name = '全部'
      param.sort = 0
      param.showName = ''
      this.selectorList.unshift(param)
      console.log(this.selectorList, '当前筛选项')
    }
  }
</script>
