<route-meta>
{
"title": "通用弹窗组件"
}
</route-meta>
<template>
  <el-dialog
    :title="$slots.title ? '' : title"
    :width="width"
    :center="center"
    :modal-append-to-body="modalAppendToBody"
    :append-to-body="appendToBody"
    :modal="modal"
    :show-close="showClose"
    :visible.sync="outerVisible"
    :before-close="cancel"
  >
    <template v-if="$slots.title">
      <span slot="title">
        <slot name="title" />
      </span>
    </template>
    <slot name="footer" />

    <span slot="footer" class="dialog-footer">
      <el-button @click="cancel">{{ cancelWord ? cancelWord : '取 消' }}</el-button>
      <el-button type="primary" @click="confirm">{{ confirmWord ? confirmWord : '确 定' }}</el-button>
    </span>
  </el-dialog>
</template>
<script lang="ts">
  import { Component, Vue, Prop } from 'vue-property-decorator'
  @Component({
    name: 'diyDialog'
  })
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    show: boolean //是否显示
    @Prop({
      type: String,
      default: ''
    })
    title: string //Dialog 的标题，也可通过具名 slot
    @Prop({
      type: String,
      default: ''
    })
    footer: string //Dialog 按钮操作区的内容
    @Prop({
      type: String,
      default: '50%'
    })
    width: string //Dialog 的宽度
    @Prop({
      type: Boolean,
      default: false
    })
    modal: boolean //是否需要遮罩层
    @Prop({
      type: Boolean,
      default: false
    })
    appendToBody: boolean //	Dialog 自身是否插入至 body 元素上。嵌套的 Dialog 必须指定该属性并赋值为 true
    @Prop({
      type: Boolean,
      default: false
    })
    modalAppendToBody: boolean //		遮罩层是否插入至 body 元素上，若为 false，则遮罩层会插入至 Dialog 的父元素上
    @Prop({
      type: Boolean,
      default: true
    })
    showClose: boolean //是否显示关闭按钮
    @Prop({
      type: Boolean,
      default: false
    })
    center: boolean //	是否对头部和底部采用居中布局
    // outerVisible: boolean = true
    @Prop({
      type: String,
      default: ''
    })
    cancelWord: string
    @Prop({
      type: String,
      default: ''
    })
    confirmWord: string

    get outerVisible() {
      return this.show
    }
    set outerVisible(val: boolean) {
      if (val) this.$emit('update:show', val)
    }
    cancel() {
      this.show = true
      this.$emit('update:show', false)
    }
    confirm() {
      this.show = true
      this.$emit('update:show', false)
    }
  }
</script>
