import {
  CommodityInvoiceTicketConfigUpdateRequest,
  ElectronicInvoiceTaxpayerUpdateRequest,
  InvoiceServiceAuthUpdateRequest
} from '@api/ms-gateway/ms-trade-configuration-v1'
import EleInvoiceVo from './common/EleInvoiceVo'
/*
  增值税发票配置
*/
class ElectronicInvoiceUpdateVo extends EleInvoiceVo {
  /**
   * 纳税人编号
   */
  taxpayerId = ''

  /**
   * 是否标记删除
   * false表示更新 true 删除 【必传】
   */
  markDelete = false

  toDto() {
    const param = new ElectronicInvoiceTaxpayerUpdateRequest()
    param.taxpayerId = this.taxpayerId
    param.name = this.name
    param.taxpayerNo = this.taxpayerNo
    param.address = this.address
    param.phone = this.phone
    param.bankName = this.bankName
    param.bankAccount = this.bankAccount
    param.invoiceMaxMoney = this.invoiceMaxMoney
    param.payee = this.payee
    param.issuer = this.issuer
    param.reviewer = this.reviewer
    param.invoiceAuthList = new Array<InvoiceServiceAuthUpdateRequest>()
    const invoice = new InvoiceServiceAuthUpdateRequest()
    invoice.invoiceProviderId = this.invoiceProviderId
    invoice.secretAssessKey = this.secretAssessKey
    invoice.deptId = this.deptId
    invoice.markDelete = this.markDelete
    invoice.accessKey = this.accessKey
    invoice.remark = this.remark
    param.invoiceAuthList.push(invoice)

    param.commodityTicketList = new Array<CommodityInvoiceTicketConfigUpdateRequest>()
    const commodityTicket = new CommodityInvoiceTicketConfigUpdateRequest()
    commodityTicket.commodityCode = this.commodityCode
    commodityTicket.printPrice = this.printPrice || false
    commodityTicket.printQuantity = this.printQuantity || false
    commodityTicket.rate = this.rate
    commodityTicket.serviceTitle = this.serviceTitle
    commodityTicket.specificationMode = this.specificationMode
    commodityTicket.unitTitle = this.unitTitle
    commodityTicket.markDelete = this.markDelete
    commodityTicket.taxFavoured = this.taxFavoured
    param.commodityTicketList.push(commodityTicket)
    return param
  }

  /**
   * 转换成诺穗通
   */
  toNST() {
    const param = new ElectronicInvoiceTaxpayerUpdateRequest()
    param.taxpayerId = this.taxpayerId
    param.name = this.name
    param.taxpayerNo = this.taxpayerNo
    param.address = this.address
    param.phone = this.phone
    param.bankName = this.bankName
    param.bankAccount = this.bankAccount
    param.invoiceMaxMoney = this.invoiceMaxMoney
    param.payee = this.payee
    param.issuer = this.issuer
    param.reviewer = this.reviewer
    param.invoiceAuthList = new Array<InvoiceServiceAuthUpdateRequest>()
    const invoice = new InvoiceServiceAuthUpdateRequest()
    invoice.invoiceProviderId = this.invoiceProviderId
    invoice.secretAssessKey = this.secretAssessKey
    invoice.deptId = this.deptId
    invoice.markDelete = this.markDelete
    invoice.accessKey = this.accessKey
    invoice.remark = this.remark
    invoice.extensionNumber = this.extensionNumber
    param.invoiceAuthList.push(invoice)

    param.commodityTicketList = new Array<CommodityInvoiceTicketConfigUpdateRequest>()
    const commodityTicket = new CommodityInvoiceTicketConfigUpdateRequest()
    commodityTicket.commodityCode = this.commodityCode
    commodityTicket.printPrice = this.printPrice || false
    commodityTicket.printQuantity = this.printQuantity || false
    commodityTicket.rate = this.rate
    commodityTicket.serviceTitle = this.serviceTitle
    commodityTicket.specificationMode = this.specificationMode
    commodityTicket.unitTitle = this.unitTitle
    commodityTicket.markDelete = this.markDelete
    commodityTicket.taxFavoured = this.taxFavoured
    param.commodityTicketList.push(commodityTicket)
    return param
  }

  toBWJFDto() {
    const param = new ElectronicInvoiceTaxpayerUpdateRequest()
    param.taxpayerId = this.taxpayerId
    param.name = this.name
    param.taxpayerNo = this.taxpayerNo
    param.address = this.address
    param.phone = this.phone
    param.bankName = this.bankName
    param.bankAccount = this.bankAccount
    param.invoiceMaxMoney = this.invoiceMaxMoney
    param.payee = this.payee
    param.issuer = this.issuer
    param.reviewer = this.reviewer
    param.invoiceAuthList = new Array<InvoiceServiceAuthUpdateRequest>()
    const invoice = new InvoiceServiceAuthUpdateRequest()
    invoice.invoiceProviderId = this.invoiceProviderId
    invoice.markDelete = this.markDelete
    invoice.remark = this.remark
    invoice.secretAssessKey = this.secretAssessKey
    invoice.accessKey = this.accessKey
    invoice.enterpriseCode = this.enterpriseCode
    invoice.taxpayerIdNumber = this.taxIdCard
    param.invoiceAuthList.push(invoice)

    param.commodityTicketList = new Array<CommodityInvoiceTicketConfigUpdateRequest>()
    const commodityTicket = new CommodityInvoiceTicketConfigUpdateRequest()
    commodityTicket.commodityCode = this.commodityCode
    commodityTicket.printPrice = this.printPrice || false
    commodityTicket.printQuantity = this.printQuantity || false
    commodityTicket.rate = this.rate
    commodityTicket.serviceTitle = this.serviceTitle
    commodityTicket.specificationMode = this.specificationMode
    commodityTicket.unitTitle = this.unitTitle
    commodityTicket.markDelete = this.markDelete
    commodityTicket.taxFavoured = this.taxFavoured
    param.commodityTicketList.push(commodityTicket)
    return param
  }
}

export default ElectronicInvoiceUpdateVo
