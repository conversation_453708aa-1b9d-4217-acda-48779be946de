import {
  CourseOfCourseTrainingOutlineRequest,
  StudentCourseLearningRequest,
  StudentCourseRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import TrainingCourseOutline from '@api/service/customer/course/query/vo/TrainingCourseOutline'
import MyCourseLearningStatusEnum from '@api/service/customer/course/query/enum/MyCourseLearningStatusEnum'
import CourseTypeEnum from '@api/service/customer/course/query/enum/CourseTypeEnum'
import CategoryTypeEnum from '../enum/CategoryTypeEnum'

enum QueryMyLearningCoursePageEnum {
  compulsory = 'COMPULSORY',
  elective = 'ELECTIVE'
}

/**
 * 查询我的学习的分页
 */
class QueryMyLearningCoursePageParam {
  // 选课规则--未完成
  static isNotComplete = [MyCourseLearningStatusEnum.not_qualified, MyCourseLearningStatusEnum.not_judged]
  // 选课规则--已完成
  static isComplete = [MyCourseLearningStatusEnum.qualified]
  // 自主选课--未学习
  static not_learing = [MyCourseLearningStatusEnum.not_qualified, MyCourseLearningStatusEnum.not_judged]
  // 自主选课--学习中
  static learing = [MyCourseLearningStatusEnum.not_qualified, MyCourseLearningStatusEnum.not_judged]
  // 自主选课--已完成
  static complete = [MyCourseLearningStatusEnum.qualified]
  studentNo: string
  learningStatus?: Array<MyCourseLearningStatusEnum>
  courseOutline: TrainingCourseOutline
  category?: CategoryTypeEnum
  courseType?: CourseTypeEnum

  to(): StudentCourseLearningRequest {
    const request = new StudentCourseLearningRequest()
    request.studentNo = this.studentNo
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    if (this.courseOutline) {
      request.courseOfCourseTrainingOutline.outlineIds = [this.courseOutline.id]
    }
    if (this.category) {
      request.courseOfCourseTrainingOutline.courseType = this.category
    }
    request.courseOfCourseTrainingOutline.courseType = this.courseType
    request.studentCourse = new StudentCourseRequest()
    request.studentCourse.courseLearningStatus = this.learningStatus
    return request
  }
}

export default QueryMyLearningCoursePageParam
