<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="注册登录" name="first">
        <div class="f-p15">
          <el-tabs v-model="activeName2" type="card" class="m-tab-card">
            <el-tab-pane label="注册设置" name="first">
              <el-form ref="form" :model="form" label-width="auto" class="m-form">
                <!--注册设置-->
                <el-card shadow="never" class="m-card is-header f-mb15">
                  <el-row type="flex" justify="center" class="f-plr20 f-pt40">
                    <el-col :lg="20" :xl="17">
                      <el-form-item label="开放学员注册入口：" required>
                        <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                      </el-form-item>
                      <el-col :lg="20" :xl="17">
                        <el-form-item label="工作单位对接天眼查/企查查：" required>
                          <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                          <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-question m-tooltip-icon f-co f-ml10"></i>
                            <div slot="content">
                              如网校采集学员的工作单位字段需对接企查查/天眼查。需在工作单位配置信息中，选择需要对接的服务商类型与账号信息。<br />
                              该功能生效与学员端账号信息管理、运营端的导入 和业务咨询功能。
                            </div>
                          </el-tooltip>
                        </el-form-item>
                      </el-col>
                    </el-col>
                  </el-row>
                  <div class="m-tit is-small is-border-bottom">
                    <span class="tit-txt">注册基础信息</span>
                  </div>
                  <el-row type="flex" justify="center" class="f-p20">
                    <el-col :lg="20" :xl="17">
                      <el-form-item>
                        <div slot="label" class="f-mt20">注册字段：</div>
                        <el-row class="m-function-set" type="flex">
                          <el-col :span="8" class="item">
                            <div class="name">姓名</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">证件类型</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">证件号</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">密码</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">手机号</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">单位所在地区</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">工作单位</div>
                            <el-switch v-model="form.delivery1" active-text="必填" inactive-text="非必填" disabled />
                          </el-col>
                        </el-row>
                      </el-form-item>
                      <el-form-item label="证件类型信息：">
                        <el-checkbox-group v-model="form.type">
                          <el-checkbox label="身份证" name="type"></el-checkbox>
                          <el-checkbox label="护照" name="type"></el-checkbox>
                          <el-checkbox label="港澳居民来往内地通行证" name="type"></el-checkbox>
                          <el-checkbox label="台湾居民来往大陆通行证" name="type"></el-checkbox>
                          <el-checkbox label="中国人民解放军军官证" name="type"></el-checkbox>
                          <el-checkbox label="外国人永久居留证" name="type"></el-checkbox>
                          <el-checkbox label="台湾居民居住证" name="type"></el-checkbox>
                          <el-checkbox label="港澳居民居住证" name="type"></el-checkbox>
                        </el-checkbox-group>
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <div class="m-tit is-small is-border-bottom">
                    <span class="tit-txt">工作单位配置信息</span>
                  </div>
                  <el-row type="flex" justify="center" class="f-p20">
                    <el-col :lg="20" :xl="17">
                      <el-form-item label="服务商类型：" required>
                        <el-radio v-model="radio" label="1" class="f-mr30">天眼查</el-radio>
                        <el-radio v-model="radio1" label="2" class="f-mr10">企查查</el-radio>
                        <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                          <i class="el-icon-question m-tooltip-icon f-co"></i>
                          <div slot="content">
                            <p>
                              请选择工作单位开放对接外部接口的服务商类型，如同时选择了天眼查和企查查，系统默认先查询天眼查再查询企查查。
                            </p>
                          </div>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="天眼查账号信息：" required>
                        <el-input v-model="form.name" clearable placeholder="请输入授权token" class="form-l" />
                      </el-form-item>
                      <el-form-item label="企查查账号信息：" required>
                        <el-input v-model="form.name" clearable placeholder="请输入授权token" class="form-l" />
                      </el-form-item>
                      <el-form-item label="企查查密钥信息：" required>
                        <el-input v-model="form.name" clearable placeholder="请输入密钥" class="form-l" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <div class="m-tit is-small is-border-bottom">
                    <span class="tit-txt">行业信息</span>
                    <el-button type="primary" size="small" class="f-ml20">查看详情</el-button>
                  </div>
                  <el-row type="flex" justify="center" class="f-p20">
                    <el-col :lg="20" :xl="17">
                      <el-form-item label="行业：">
                        <el-checkbox-group v-model="form.type">
                          <el-checkbox label="人社行业" name="type"></el-checkbox>
                          <el-checkbox label="建设行业" name="type"></el-checkbox>
                          <el-checkbox label="职业卫生行业" name="type"></el-checkbox>
                          <el-checkbox label="工勤行业" name="type"></el-checkbox>
                          <el-checkbox label="教师行业" name="type"></el-checkbox>
                        </el-checkbox-group>
                      </el-form-item>
                      <!--人社行业-->
                      <el-form-item>
                        <div slot="label" class="f-mt20">人社行业信息：</div>
                        <el-row class="m-function-set" type="flex">
                          <el-col :span="8" class="item">
                            <div class="name">专业类别</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">职称等级</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                        </el-row>
                      </el-form-item>
                      <!--建设行业-->
                      <el-form-item>
                        <div slot="label" class="f-mt20">建设行业信息：</div>
                        <el-row class="m-function-set" type="flex">
                          <el-col :span="8" class="item">
                            <div class="name">证书类别</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">注册专业</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">证书编号</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">证书发证日期</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">证书有效期</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">证书附件</div>
                            <span class="f-cb">必填</span>
                          </el-col>
                        </el-row>
                      </el-form-item>
                      <!--职业卫生行业-->
                      <el-form-item>
                        <div slot="label" class="f-mt20">职业卫生行业：</div>
                        <el-row class="m-function-set" type="flex">
                          <el-col :span="8" class="item">
                            <div class="name">人员类别</div>
                            <el-switch v-model="form.delivery1" active-text="必填" inactive-text="非必填" disabled />
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">岗位类别</div>
                            <el-switch v-model="form.delivery1" active-text="必填" inactive-text="非必填" disabled />
                          </el-col>
                        </el-row>
                      </el-form-item>
                      <!--工勤行业信息-->
                      <el-form-item>
                        <div slot="label" class="f-mt20">工勤行业信息：</div>
                        <el-row class="m-function-set" type="flex">
                          <el-col :span="8" class="item">
                            <div class="name">技术等级</div>
                            <el-switch v-model="form.delivery1" active-text="必填" inactive-text="非必填" disabled />
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">工种</div>
                            <el-switch v-model="form.delivery1" active-text="必填" inactive-text="非必填" disabled />
                          </el-col>
                        </el-row>
                      </el-form-item>
                      <!--教师行业信息-->
                      <el-form-item>
                        <div slot="label" class="f-mt20">教师行业信息：</div>
                        <el-row class="m-function-set" type="flex">
                          <el-col :span="8" class="item">
                            <div class="name">学段</div>
                            <el-switch v-model="form.delivery1" active-text="必填" inactive-text="非必填" disabled />
                          </el-col>
                          <el-col :span="8" class="item">
                            <div class="name">学科</div>
                            <el-switch v-model="form.delivery1" active-text="必填" inactive-text="非必填" disabled />
                          </el-col>
                        </el-row>
                      </el-form-item>
                    </el-col>
                  </el-row>
                </el-card>
              </el-form>
              <div class="m-btn-bar f-tc is-sticky-1">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </div>
            </el-tab-pane>
            <el-tab-pane label="登录设置" name="second">
              <el-card shadow="never" class="m-card f-mb15">
                <div class="m-tit is-small bg-gray">
                  <span class="tit-txt f-cb f-pl20">Web 登录设置</span>
                </div>
                <div class="m-sub-tit is-border-bottom">
                  <span class="tit-txt">微信扫码登录设置</span>
                </div>
                <el-row type="flex" justify="center" class="width-limit f-p20">
                  <el-col :md="14" :lg="13" :xl="10">
                    <el-form ref="form" :model="form" label-width="140px" class="m-form">
                      <el-form-item>
                        <div slot="label">
                          <span class="f-vm">微信扫码登录</span>
                          <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                            <div slot="content">
                              <p>开启微信扫码登录功能需要配置以下信息：</p>
                              <p>1. 申请微信开放平台账户信息：（帐号、密码、绑定人）</p>
                              <p>2. 前往开放平台申请“网站应用”并配置相关域名和回调地址</p>
                              <p>3. 填写APP ID 和 秘钥信息</p>
                              <p class="f-mt10">
                                操作提醒：如果修改APPID和秘钥信息会将原绑定微信的学员全部解绑，请联系平台运营管理员进行操作。
                              </p>
                            </div>
                          </el-tooltip>
                          <span>：</span>
                        </div>
                        <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                      </el-form-item>
                      <el-form-item label="App ID：" required>
                        <el-input v-model="form.name" clearable placeholder="请输入 App ID 信息" />
                      </el-form-item>
                      <el-form-item label="AppSecret：" required>
                        <el-input v-model="form.name" clearable placeholder="请输入 AppSecret 信息" />
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
                <div class="m-tit is-small bg-gray">
                  <span class="tit-txt f-cb f-pl20">H5 登录设置</span>
                </div>
                <div class="m-sub-tit is-border-bottom">
                  <span class="tit-txt">微信授权登录设置</span>
                </div>
                <el-row type="flex" justify="center" class="width-limit f-pt35 f-p20">
                  <el-col :md="14" :lg="13" :xl="10">
                    <el-form ref="form" :model="form" label-width="140px" class="m-form">
                      <el-form-item>
                        <div slot="label">
                          <span class="f-vm">微信授权登录</span>
                          <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                            <div slot="content">
                              <p>如网校未提供公众号，无法使用微信授权登录，请点击关闭。</p>
                              <p>微信内开启绑定登录功能需要配置以下信息：</p>
                              <p>1.申请微信开放平台账户信息：（帐号、密码、绑定人）</p>
                              <p>2.前往开放平台申请“网站应用”并配置相关域名和回调地址</p>
                              <p>3.填写APP ID 和 密钥信息</p>
                              <p class="f-mt10">
                                操作提醒：如果修改APPID和密钥信息会将原绑定微信的学员全部解绑，请联系平台运营管理员进行操作。
                              </p>
                            </div>
                          </el-tooltip>
                          <span>：</span>
                        </div>
                        <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                      </el-form-item>
                      <el-form-item>
                        <div slot="label">
                          <span class="f-cr f-mr5">*</span>
                          <span class="f-vm">App ID：</span>
                        </div>
                        <el-input v-model="form.name" clearable placeholder="请输入 App ID 信息" />
                      </el-form-item>
                      <el-form-item label="AppSecret：" required>
                        <el-input v-model="form.name" clearable placeholder="请输入 AppSecret 信息" />
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>

                <div class="m-sub-tit is-border-bottom">
                  <span class="tit-txt">账号登录设置</span>
                </div>
                <el-row type="flex" justify="center" class="width-limit f-pt35 f-p20">
                  <el-col :md="14" :lg="13" :xl="10">
                    <el-form ref="form" :model="form" label-width="160px" class="m-form">
                      <el-form-item>
                        <div slot="label">
                          <span class="f-vm">账号登录（H5）</span>
                          <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                            <div slot="content">
                              开放设置H5的登录方式，如开启，支持使用账号密码登录平台。<br />
                              需注意：在微信内部访问，在支付环节需学员账号绑定当前的微信，否则无法进入支付。
                            </div>
                          </el-tooltip>
                          <span>：</span>
                        </div>
                        <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-card>
              <div class="m-btn-bar f-tc is-sticky-1">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      <el-tab-pane label="集体报名" name="second">详见 0303_功能设置_集体报名.vue</el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="third">详见 0304_功能设置_增值税发票.vue</el-tab-pane>
      <el-tab-pane label="培训证明" name="fourth">详见 0305_功能设置_培训证明.vue</el-tab-pane>
      <el-tab-pane label="视频播放设置" name="five">详见 0306_功能设置_视频播放设置.vue</el-tab-pane>
      <el-tab-pane label="门户精品课程" name="six">详见 0307_功能设置_门户精品课程.vue</el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'second',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          delivery1: true,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
