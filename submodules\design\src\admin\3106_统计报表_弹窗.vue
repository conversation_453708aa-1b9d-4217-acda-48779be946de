<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--请选择导出方式-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20 f-mb20">选择导出方式</el-button>
        <el-drawer title="提示" :visible.sync="dialog2" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="请选择导出方式：">
                <el-radio-group v-model="form.resource">
                  <el-radio label="导出数据列表"></el-radio>
                  <el-radio label="导出列表人员详细"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">确定</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <!--考试记录-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20 f-mb20">考试记录</el-button>
        <el-drawer title="考试记录" :visible.sync="dialog3" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <p class="f-fb f-f15 f-mtb10">试卷名称：这是试卷名称这是试卷名称</p>
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mb30">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="试卷提交时间" min-width="300">
                <template>2021.02.0 14:00:00</template>
              </el-table-column>
              <el-table-column label="考试成绩" min-width="180">
                <template>60分</template>
              </el-table-column>
            </el-table>
          </div>
        </el-drawer>
        <!--测验记录-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20">测验记录</el-button>
        <el-drawer title="测验记录" :visible.sync="dialog4" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <p class="f-fb f-f15 f-mtb10">课程名称：这是课程名称这是课程名称</p>
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mb30">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="测验提交时间" min-width="300">
                <template>2021.02.0 14:00:00</template>
              </el-table-column>
              <el-table-column label="测验成绩" min-width="180">
                <template>60分</template>
              </el-table-column>
            </el-table>
            <p class="f-fb f-f15 f-mtb10">课程名称：这是课程名称这是课程名称</p>
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mb30">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="测验提交时间" min-width="300">
                <template>2021.02.0 14:00:00</template>
              </el-table-column>
              <el-table-column label="测验成绩" min-width="180">
                <template>60分</template>
              </el-table-column>
            </el-table>
          </div>
        </el-drawer>
        <!--方案开通统计口径说明-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">方案开通统计口径说明</el-button>
        <el-drawer
          title="统计口径说明"
          :visible.sync="dialog5"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="tableData5" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">方案属性</div>
                  <div v-else-if="scope.$index === 1">方案类型</div>
                  <div v-else-if="scope.$index === 2">选择方案</div>
                  <div v-else-if="scope.$index === 3">开通时间</div>
                  <div v-else-if="scope.$index === 4">供应商</div>
                  <div v-else>分销推广</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    选择不同的行业，可以查询不同的培训属性值。属性值为空不展示
                  </div>
                  <div v-else-if="scope.$index === 1">创建培训方案时定义的类型属性，如培训班等</div>
                  <div v-else-if="scope.$index === 2">选择全部已发布的培训方案</div>
                  <div v-else-if="scope.$index === 3">查看在某个开通时间内，培训方案开通数据</div>
                  <div v-else-if="scope.$index === 4">
                    默认显示所有授权关联的供应商数据，可以选择指定的供应商查看具体的分销商数据
                  </div>
                  <div v-else>默认显示所有订单的开通数据，支持选择查看分销订单或非分销订单的开通数据</div>
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="tableData12" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">方案名称</div>
                  <div v-else-if="scope.$index === 1">方案属性</div>
                  <div v-else-if="scope.$index === 2">合计</div>
                  <div v-else-if="scope.$index === 3">个人缴费</div>
                  <div v-else-if="scope.$index === 4">集体报名</div>
                  <div v-else-if="scope.$index === 5">导入开通</div>
                  <div v-else-if="scope.$index === 6">线上缴费</div>
                  <div v-else-if="scope.$index === 7">线下缴费</div>
                  <div v-else-if="scope.$index === 8">开通</div>
                  <div v-else-if="scope.$index === 9">退班</div>
                  <div v-else-if="scope.$index === 10">换入（换班）</div>
                  <div v-else>换出（换班）</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">培训方案名称，默认显示全部已发布的方案</div>
                  <div v-else-if="scope.$index === 1">培训方案的属性值展示</div>
                  <div v-else-if="scope.$index === 2">各项数据的合计数值</div>
                  <div v-else-if="scope.$index === 3">学员自主缴费的方式</div>
                  <div v-else-if="scope.$index === 4">学员参加培训的方式通过集体报名的方式</div>
                  <div v-else-if="scope.$index === 5">学员参加培训的方式是通过后台教务人员直接导入</div>
                  <div v-else-if="scope.$index === 6">通过网络在线的形式进行缴费</div>
                  <div v-else-if="scope.$index === 7">通过线下的形式进行缴费</div>
                  <div v-else-if="scope.$index === 8">
                    在开通日期段内，为学员成功开通培训班的人次。缴费成功订单状态为交易成功的培训班则开通数+1
                  </div>
                  <div v-else-if="scope.$index === 9">在开通日期段内，发起退班且退班成功则对应的培训班退班人次+1</div>
                  <div v-else-if="scope.$index === 10">在开通日期段内，发起换入班级且换入成功的人次+1</div>
                  <div v-else>在开通日期段内，发起换出班级且换出成功的人次+1</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--地区开通统计口径说明-->
        <el-button @click="dialog6 = true" type="primary" class="f-mr20">地区开通统计口径说明</el-button>
        <el-drawer
          title="统计口径说明"
          :visible.sync="dialog6"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="tableData6" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">方案属性</div>
                  <div v-else-if="scope.$index === 1">方案类型</div>
                  <div v-else-if="scope.$index === 2">选择方案</div>
                  <div v-else-if="scope.$index === 3">工作单位所在地区</div>
                  <div v-else-if="scope.$index === 4">报名成功时间</div>
                  <div v-else-if="scope.$index === 5">供应商</div>
                  <div v-else>分销推广</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">选择不同的行业，可以查询不同的培训属性值。</div>
                  <div v-else-if="scope.$index === 1">创建培训方案时定义的类型属性，如培训班等</div>
                  <div v-else-if="scope.$index === 2">选择全部已发布的培训方案</div>
                  <div v-else-if="scope.$index === 3">
                    默认显示省份下各地市的数据，如xx省、省直单位、xx市等；选xx市则显示的是xx市各区县数据，如xx市、市直、xx区等
                  </div>
                  <div v-else-if="scope.$index === 4">查看在某个开通时间内，地区开通方案数据</div>
                  <div v-else-if="scope.$index === 5">
                    默认显示所有授权关联的供应商数据，可以选择指定的供应商查看具体的分销商数据
                  </div>
                  <div v-else>默认显示所有订单的开通数据，支持选择查看分销订单或非分销订单的开通数据</div>
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="tableData6" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">工作单位所在地区</div>
                  <div v-else-if="scope.$index === 1">开通</div>
                  <div v-else-if="scope.$index === 2">退班</div>
                  <div v-else-if="scope.$index === 3">换出（换班）</div>
                  <div v-else-if="scope.$index === 4">换入（换班）</div>
                  <div v-else>净开通</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">学员工作单位所在地区信息，以学员报名时信息为准</div>
                  <div v-else-if="scope.$index === 1">
                    统计搜索时间段内实际开通成功的班级人次数据，开通成功：指实际缴费成功的情况
                  </div>
                  <div v-else-if="scope.$index === 2">
                    统计搜索时间段内，通过管理后台执行退班操作时生成的退班订单数
                  </div>
                  <div v-else-if="scope.$index === 3">
                    统计搜索时间段内，通过管理后台执行换班操作时生成换出班级的订单数
                  </div>
                  <div v-else-if="scope.$index === 4">
                    统计搜索时间段内，通过管理后台执行换班操作时生成换入班级的订单数
                  </div>
                  <div v-else>
                    净开通=开通+换入-换出-退班；如9.1查8.1-8.31开通数100，退班数2，换出5，换入5，净开通=100+5-2-5=98
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--方案学习统计口径说明-->
        <el-button @click="dialog7 = true" type="primary" class="f-mr20">方案学习统计口径说明</el-button>
        <el-drawer
          title="统计口径说明"
          :visible.sync="dialog7"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="tableData06" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="160">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">年度</div>
                  <div v-else-if="scope.$index === 1">培训类别</div>
                  <div v-else-if="scope.$index === 2">培训专业</div>
                  <div v-else-if="scope.$index === 3">科目类型</div>
                  <div v-else-if="scope.$index === 4">培训方案名称</div>
                  <div v-else-if="scope.$index === 5">报名时间</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">按培训方案年度查询数据</div>
                  <div v-else-if="scope.$index === 1">创建培训方案时定义的培训类别，按培训类别查询数据</div>
                  <div v-else-if="scope.$index === 2">选择具体培训类别下的某一专业查询数据</div>
                  <div v-else-if="scope.$index === 3">职称申报人员可按科目类型查询数据</div>
                  <div v-else-if="scope.$index === 4">按培训方案名称搜索，支持模糊查询</div>
                  <div v-else-if="scope.$index === 5">查看在某个开通时间内，培训方案开通数据</div>
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="tableData14" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="160">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">方案名称</div>
                  <div v-else-if="scope.$index === 1">方案属性</div>
                  <div v-else-if="scope.$index === 2">净报名</div>
                  <div v-else-if="scope.$index === 3">未学习</div>
                  <div v-else-if="scope.$index === 4">学习中</div>
                  <div v-else-if="scope.$index === 5">已学完</div>
                  <div v-else-if="scope.$index === 6">已考试</div>
                  <div v-else-if="scope.$index === 7">未考试</div>
                  <div v-else-if="scope.$index === 8">培训期别已合格</div>
                  <div v-else-if="scope.$index === 9">培训期别未合格</div>
                  <div v-else-if="scope.$index === 10">调研问卷已提交</div>
                  <div v-else-if="scope.$index === 11">调研问卷未提交</div>
                  <div v-else-if="scope.$index === 12">已合格</div>
                  <div v-else-if="scope.$index === 13">合格率</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">培训方案名称，默认显示全部已发布的方案</div>
                  <div v-else-if="scope.$index === 1">培训方案的属性值展示</div>
                  <div v-else-if="scope.$index === 2">
                    统计截止到当前时间扣除退班后的实际有效的报名人次，净报名=未学习+学习中+已学完
                  </div>
                  <div v-else-if="scope.$index === 3">统计截止到当前时间净报名人数中，班级课程学习获得学时=0</div>
                  <div v-else-if="scope.$index === 4">
                    统计截止到当前时间净报名人数中，0&lt;课程学习已获得的学时&lt;课程学习要求学时，方案无配置课程学习显示：无需学习
                  </div>
                  <div v-else-if="scope.$index === 5">统计截止到当前时间净报名人数中，课程学习已获得要求学时</div>
                  <div v-else-if="scope.$index === 6">
                    统计截止到当前时间净报名人数中，已参加过考试的人（有提交试卷成功就计一次考试）
                  </div>
                  <div v-else-if="scope.$index === 7">
                    统计截止到当前时间净报名人数中，未参加考试的人（有提交试卷成功就计一次考试）
                  </div>
                  <div v-else-if="scope.$index === 8">统计截止到当前时间净报名人数中，已满足培训期别考核的人</div>
                  <div v-else-if="scope.$index === 9">统计截止到当前时间净报名人数中，不满足培训期别考核的人</div>
                  <div v-else-if="scope.$index === 10">
                    统计截止到当前时间净报名人数中，已提交所有要求填写（纳入考核）的问卷的人数。<br />仅统计班级维度的问卷
                  </div>
                  <div v-else-if="scope.$index === 11">
                    统计截止到当前时间净报名人数中，存在未提交要求填写（纳入考核）的问卷的人数。<br />仅统计班级维度的问卷
                  </div>
                  <div v-else-if="scope.$index === 12">统计截止到当前时间净报名人数中，已达到培训考核要求的人</div>
                  <div v-else-if="scope.$index === 13">
                    统计截止到当前时间净报名人数中的合格率，合格率=已合格/净报名
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--地区学习统计口径说明-->
        <el-button @click="dialog8 = true" type="primary" class="f-mr20">地区学习统计口径说明</el-button>
        <el-drawer
          title="统计口径说明"
          :visible.sync="dialog8"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="tableData03" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">方案属性</div>
                  <div v-else-if="scope.$index === 1">培训方案</div>
                  <div v-else-if="scope.$index === 2">方案类型</div>
                  <div v-else-if="scope.$index === 3">报名成功时间</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    选择不同的行业，可以查询不同的培训属性值。如建设行业方案属性：地区、培训年度、科目类型、培训类别、培训专业。属性值为空，显示“-”
                  </div>
                  <div v-else-if="scope.$index === 1">创建培训方案时定义的类型属性，如培训班-选课规则等</div>
                  <div v-else-if="scope.$index === 2">选择全部已发布的培训班</div>
                  <div v-else-if="scope.$index === 3">查看在某个开通时间内，各地区学员学习数据</div>
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="tableData13" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">方案名称</div>
                  <div v-if="scope.$index === 1">方案属性</div>
                  <div v-else-if="scope.$index === 2">净报名</div>
                  <div v-else-if="scope.$index === 3">未学习</div>
                  <div v-else-if="scope.$index === 4">学习中</div>
                  <div v-else-if="scope.$index === 5">已学完</div>
                  <div v-else-if="scope.$index === 6">已考试</div>
                  <div v-else-if="scope.$index === 7">已合格</div>
                  <div v-else-if="scope.$index === 8">合格率</div>
                  <div v-else-if="scope.$index === 9">已提交</div>
                  <div v-else-if="scope.$index === 10">未提交</div>
                  <div v-else-if="scope.$index === 11">培训期别已合格</div>
                  <div v-else-if="scope.$index === 12">培训期别未合格</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">培训方案名称，默认显示全部已发布方案</div>
                  <div v-else-if="scope.$index === 1">培训方案的属性值展示</div>
                  <div v-else-if="scope.$index === 2">
                    统计截止到当前时间扣除退班后的实际有效的报名人次，净报名=未学习+学习中+已学完
                  </div>
                  <div v-else-if="scope.$index === 3">统计截止到当前时间净报名人数中，班级课程获得学时=0</div>
                  <div v-else-if="scope.$index === 4">
                    统计截止到当前时间净报名人数中，0&lt;课程学习已获得学时&lt;课程要求学时，方案无配置课程学习显示：无需学习
                  </div>
                  <div v-else-if="scope.$index === 5">统计截止到当前时间净报名人数中，课程学习已获得要求学时</div>
                  <div v-else-if="scope.$index === 6">
                    统计截止到当前时间净报名人数中，已参加过考试的人（有提交试卷成功就计一次考试）
                  </div>
                  <div v-else-if="scope.$index === 7">统计截止到当前时间净报名人数中，已达到培训考核要求的人</div>
                  <div v-else-if="scope.$index === 8">统计截止到当前时间净报名人数中的合格率，合格率=已合格/净报名</div>
                  <div v-else-if="scope.$index === 9">
                    统计截止到当前时间净报名人数中，已提交所有要求填写（纳入考核）的问卷的人数。
                  </div>
                  <div v-else-if="scope.$index === 10">
                    统计截止到当前时间净报名人数中，存在未提交要求填写（纳入考核）的问卷的人数。
                  </div>
                  <div v-else-if="scope.$index === 11">统计截止到当前时间净报名人数中，已满足培训期别考核的人</div>
                  <div v-else-if="scope.$index === 12">统计截止到当前时间净报名人数中，不满足培训期别考核的人</div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--学员学习统计口径说明-->
        <el-button @click="dialog9 = true" type="primary" class="f-mr20">学员学习明细统计口径说明</el-button>
        <el-drawer
          title="统计口径说明"
          :visible.sync="dialog9"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="tableData21" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">学员姓名</div>
                  <div v-else-if="scope.$index === 1">学员帐号</div>
                  <div v-else-if="scope.$index === 2">手机号</div>
                  <div v-else-if="scope.$index === 3">工作单位所在地区</div>
                  <div v-else-if="scope.$index === 4">单位名称</div>
                  <div v-else-if="scope.$index === 5">单位所在地区</div>
                  <div v-else-if="scope.$index === 6">方案名称</div>
                  <div v-else-if="scope.$index === 7">方案类型</div>
                  <div v-else-if="scope.$index === 8">方案属性</div>
                  <div v-else-if="scope.$index === 9">报名学时</div>
                  <div v-else-if="scope.$index === 10">要求学时</div>
                  <div v-else-if="scope.$index === 11">获得学时</div>
                  <div v-else-if="scope.$index === 12">培训结果</div>
                  <div v-else-if="scope.$index === 13">报名时间</div>
                  <div v-else-if="scope.$index === 14">培训通过时间</div>
                  <div v-else-if="scope.$index === 15">培训类别</div>
                  <div v-else-if="scope.$index === 16">培训专业</div>
                  <div v-else-if="scope.$index === 17">科目类型</div>
                  <div v-else-if="scope.$index === 18">培训方案名称</div>
                  <div v-else-if="scope.$index === 19">报名时间</div>
                  <div v-else-if="scope.$index === 20">期别名称</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">查询指定姓名的学员</div>
                  <div v-else-if="scope.$index === 1">学员的登录帐号（证件号）</div>
                  <div v-else-if="scope.$index === 2">查看某个培训方案下各个单位的学习数据</div>
                  <div v-else-if="scope.$index === 3">选择需查询的地区</div>
                  <div v-else-if="scope.$index === 4">学员工作单位信息</div>
                  <div v-else-if="scope.$index === 5">学员工作单位所在地区</div>
                  <div v-else-if="scope.$index === 6">学员报名的培训方案名称信息</div>
                  <div v-else-if="scope.$index === 7">创建培训方案时定义的类型属性，如培训班等</div>
                  <div v-else-if="scope.$index === 8">选择不同的行业，可以查询不同的培训属性值。属性值为空不显示</div>
                  <div v-else-if="scope.$index === 9">查看培训方案考核通过获得学时</div>
                  <div v-else-if="scope.$index === 10">查看课程学习的考核要求学时</div>
                  <div v-else-if="scope.$index === 11">查看学员当前培训方案获得的学时情况</div>
                  <div v-else-if="scope.$index === 12">根据培训结果，筛选学员学习情况</div>
                  <div v-else-if="scope.$index === 13">查看在某个开通时间内，各学员学习数据</div>
                  <div v-else-if="scope.$index === 14">查看在某个培训通过时间内，各个学员的学习情况</div>
                  <div v-else-if="scope.$index === 15">创建培训方案时定义的培训类别，按培训类别查询数据</div>
                  <div v-else-if="scope.$index === 16">选择具体培训类别下的某一专业查询数据</div>
                  <div v-else-if="scope.$index === 17">职称申报人员可按科目类型查询数据</div>
                  <div v-else-if="scope.$index === 18">按培训方案名称搜索，支持模糊查询</div>
                  <div v-else-if="scope.$index === 19">查看在某个开通时间内，培训方案学习数据</div>
                  <div v-else-if="scope.$index === 20">根据培训期别筛选学员学习情况</div>
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="tableData21" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">学员姓名</div>
                  <div v-else-if="scope.$index === 1">手机号</div>
                  <div v-else-if="scope.$index === 2">学员证件号</div>
                  <div v-else-if="scope.$index === 3">单位名称</div>
                  <div v-else-if="scope.$index === 4">单位所在地区</div>
                  <div v-else-if="scope.$index === 5">方案名称</div>
                  <div v-else-if="scope.$index === 6">方案属性</div>
                  <div v-else-if="scope.$index === 7">报名成功时间</div>
                  <div v-else-if="scope.$index === 8">报名学时</div>
                  <div v-else-if="scope.$index === 9">要求学时</div>
                  <div v-else-if="scope.$index === 10">已获得</div>
                  <div v-else-if="scope.$index === 11">课程合格时间</div>
                  <div v-else-if="scope.$index === 12">班级考试成绩最高分</div>
                  <div v-else-if="scope.$index === 13">培训结果</div>
                  <div v-else-if="scope.$index === 14">培训通过时间</div>
                  <div v-else-if="scope.$index === 15">心得要求</div>
                  <div v-else-if="scope.$index === 16">已通过</div>
                  <div v-else-if="scope.$index === 17">培训期别-结业测试</div>
                  <div v-else-if="scope.$index === 18">培训期别-考勤签到</div>
                  <div v-else-if="scope.$index === 19">培训期别-调研问卷</div>
                  <div v-else-if="scope.$index === 20">调研问卷</div>
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">查询指定姓名的学员</div>
                  <div v-else-if="scope.$index === 1">学员手机号信息，为空显示-</div>
                  <div v-else-if="scope.$index === 2">学员的登录帐号</div>
                  <div v-else-if="scope.$index === 3">学员工作单位信息，为空显示-</div>
                  <div v-else-if="scope.$index === 4">学员工作单位所在地区</div>
                  <div v-else-if="scope.$index === 5">学员报名的培训方案名称信息</div>
                  <div v-else-if="scope.$index === 6">培训方案的属性值展示</div>
                  <div v-else-if="scope.$index === 7">学员成功开通培训班的时间</div>
                  <div v-else-if="scope.$index === 8">培训方案考核通过获得学时</div>
                  <div v-else-if="scope.$index === 9">课程学习要求的考核学时</div>
                  <div v-else-if="scope.$index === 10">课程学习已获得的学时数统计，无课程学习显示：无需学习</div>
                  <div v-else-if="scope.$index === 11">完成培训班要求的课程学时时间</div>
                  <div v-else-if="scope.$index === 12">班级考试当前取得的最高分，班级未配置考试显示：无需考试</div>
                  <div v-else-if="scope.$index === 13">学员的培训结果</div>
                  <div v-else-if="scope.$index === 14">学员通过培训考核时间</div>
                  <div v-else-if="scope.$index === 15">学习心得要求至少要参与的活动数</div>
                  <div v-else-if="scope.$index === 16">已参加且满足合格要求的活动数</div>
                  <div v-else-if="scope.$index === 17">学员报名期别是否已被认定合格，如无需结业测试，显示 -</div>
                  <div v-else-if="scope.$index === 18">学员报名期别要求的考勤总次数及学员实际完成考勤的次数。</div>
                  <div v-else-if="scope.$index === 19">
                    学员报名期别的问卷要求提交份数及学员实际提交份数，无要求显示 -
                  </div>
                  <div v-else-if="scope.$index === 20">
                    学员报名班级的问卷要求提交份数及学员实际提交份数，无要求显示 -
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }],
        tableData4: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }],
        tableData5: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '6' }
        ],
        tableData6: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '6' },
          { field101: '7' }
        ],
        tableData8: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '4' },
          { field101: '5' }
        ],
        tableData9: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' }
        ],
        tableData12: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' }
        ],
        tableData15: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' }
        ],
        tableData16: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' }
        ],
        tableData03: [{ field101: '1' }, { field101: '2' }, { field101: '3' }],
        tableData06: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '6' }
        ],
        tableData14: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '6' },
          { field101: '7' },
          { field101: '8' },
          { field101: '9' },
          { field101: '10' },
          { field101: '11' },
          { field101: '12' },
          { field101: '13' },
          { field101: '14' }
        ],
        tableData13: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '6' },
          { field101: '7' },
          { field101: '8' },
          { field101: '9' },
          { field101: '10' },
          { field101: '11' },
          { field101: '12' },
          { field101: '13' }
        ],
        tableData21: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '6' },
          { field101: '7' },
          { field101: '8' },
          { field101: '9' },
          { field101: '10' },
          { field101: '11' },
          { field101: '12' },
          { field101: '13' },
          { field101: '14' },
          { field101: '15' },
          { field101: '16' },
          { field101: '17' },
          { field101: '18' },
          { field101: '19' },
          { field101: '20' },
          { field101: '21' }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
