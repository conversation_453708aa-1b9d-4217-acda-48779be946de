/**
 * @description 订单-交易状态
 */
import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum OrderTransaction {
  Wait_Pay = 1,
  Paying = 2,
  Opening = 3,
  Complete_Transaction = 4,
  Close_Transaction = 5
}

class OrderTransactionStatus extends AbstractEnum<OrderTransaction> {
  static enum = OrderTransaction
  constructor(status?: OrderTransaction) {
    super()
    this.current = status
    this.map.set(OrderTransaction.Wait_Pay, '等待付款')
    this.map.set(OrderTransaction.Paying, '支付中')
    this.map.set(OrderTransaction.Opening, '开通中')
    this.map.set(OrderTransaction.Complete_Transaction, '交易成功')
    this.map.set(OrderTransaction.Close_Transaction, '交易关闭')
  }
}

export default new OrderTransactionStatus()
