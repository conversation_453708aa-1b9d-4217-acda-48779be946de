import {
  AlipayEncryptionKeyDataResponse,
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ReceiveAccountDetailVo from './ReceiveAccountDetailVo'

class AliPayReceiveAccountDetailVo extends ReceiveAccountDetailVo {
  /**
   * 支付账号类型id
   * 支付宝:ALIPAY
   * 微信：WXPAY
   * 支付宝H5:ALIPAYH5
   * 微信H5：WXPAYH5
   */
  paymentChannelId = ''
  /**
   * 支付宝密钥 -- 原型给的支付宝密钥不需要了
   */

  /**
   * 合作者身份ID
   */
  partner = ''
  /**
   * 支付宝应用私钥
   */
  privateKey = ''
  /**
   * 支付宝公钥
   */
  publicKey = ''
  /**
   * 支付宝应用id
   */
  appId = ''

  from(res: ReceiveAccountConfigResponse) {
    this.accountType = res.accountType
    this.accountNo = res.accountNo
    this.accountName = res.name
    this.taxPayerId = res.taxPayerId
    this.refundWay = res.returnType
    this.paymentChannelId = res.paymentChannelId
    if (res.encryptionKeyData.encryptionKeyType === 'Alipay') {
      const temp = res.encryptionKeyData as AlipayEncryptionKeyDataResponse
      this.partner = temp.partner
      this.privateKey = temp.privateKey
      this.publicKey = temp.publicKey
      this.appId = temp.appId
    }
  }
}
export default AliPayReceiveAccountDetailVo
