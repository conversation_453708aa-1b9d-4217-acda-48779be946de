import LearningTypeBase from '@api/service/management/train-class/mutation/vo/LearningTypeBase'

/**
 * 练习
 */
class PracticeLearningType extends LearningTypeBase {
  // region properties

  /**
   *出题类型1、按题库id出题，2、按课程id出题，3：同考试，类型为number
   */
  type = 0
  /**
   *出卷配置id，当type=3时需要提供，类型为string
   */
  paperPublishConfigureId = ''
  /**
   *是否开放题析，类型为boolean
   */
  openDissects = false
  /**
   *题库id集合当type=1时需要提供，类型为string[]
   */
  libraryIds: string[] = []
  /**
   *多选题漏选得分方式，0不得分，1得全部分数，2得一半分数，3每个选项按平均得分，类型为number
   */
  multipleQuestionMissScorePatterns = 0
  // endregion
  // region methods

  // endregion
}
export default PracticeLearningType
