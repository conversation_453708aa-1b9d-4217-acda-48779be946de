<template>
  <ZzttStudyRecords ref="studyRecordsRef">
    <template #training-program="{ scope }">
      <el-tag type="warning" class="f-mr10" v-if="getTrainClassOpenTypeNameById(scope.row.basicInfo.openType)">{{
        getTrainClassOpenTypeNameById(scope.row.basicInfo.openType)
      }}</el-tag>
      <!-- TODO 是否显示专题 -->
      <el-tag type="success" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题 </el-tag>
      <!-- 是否分销推广 -->
      <el-tag type="warning" class="f-mr10" v-if="scope.row.saleChannel == SaleChannelEnum.distribution"
        >分销推广</el-tag
      >
      <el-tag type="danger" v-if="scope.row.thirdPartyPlatform">{{ scope.row.thirdPartyPlatform }}</el-tag>
    </template>
  </ZzttStudyRecords>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import StudyRecords from '@hbfe/jxjy-admin-customerService/src/personal/components/study-records.vue'
  import QueryStudentTrainClass from '@api/service/diff/management/zztt/train-class/QueryStudentTrainClass'
  import StudentTrainClassDetailVo from '@api/service/diff/management/zztt/train-class/model/StudentTrainClassDetailVo'
  import { SaleChannelEnum } from '@api/service/diff/management/zztt/trade/enums/SaleChannelType'

  class ZzttStudyRecords extends StudyRecords {
    // 查询实例培训班列表
    queryStudentTrainClassObj = new QueryStudentTrainClass()
    trainClassTableData = new Array<StudentTrainClassDetailVo>()
    queryTrainClassTableData = new StudentTrainClassDetailVo()
  }
  @Component({
    components: {
      ZzttStudyRecords
    }
  })
  export default class extends Vue {
    @Ref('studyRecordsRef') studyRecordsRef: ZzttStudyRecords

    SaleChannelEnum = SaleChannelEnum

    async userIdChange(val: string) {
      return this.studyRecordsRef.userIdChange(val)
    }

    getTrainClassOpenTypeNameById(val: number) {
      switch (val) {
        case 1:
        case 5:
          return '个人报名'
        case 2:
          return '集体报名'
        case 3:
          return '导入开通'
        default:
          return ''
      }
    }
  }
</script>
