// 原始商品模型
import { LazyCommodityDTO } from '@api/gateway/PlatformCommodity'
// 创建订单页面商品模型
import Commodity from '@api/service/customer/trade/single/mutation/customer-order/vo/commodity/Commodity'

export default class ParseUtil {
  private constructor() {
    // noop
  }
  /**
   * @description: 把商品模型转化为创建订单页面做业务的商品模型
   * @param {LsCommodityDetail} LazyCommodityDTO
   * @return {*}
   */

  static parseCommodity(lsCommodityDetail: LazyCommodityDTO): Commodity {
    const createOrderViewCommodity = new Commodity()
    createOrderViewCommodity.commodityAvailable = lsCommodityDetail.commodityAvailable
    createOrderViewCommodity.commodityDescription = lsCommodityDetail.commodityDescription
    createOrderViewCommodity.commodityDescriptionUniApp = lsCommodityDetail.commodityDescriptionUniApp
    createOrderViewCommodity.commodityId = lsCommodityDetail.commodityId
    createOrderViewCommodity.commoditySaleTitle = lsCommodityDetail.commoditySaleTitle
    createOrderViewCommodity.commoditySellingPoint = lsCommodityDetail.commoditySellingPoint
    createOrderViewCommodity.commodityState = lsCommodityDetail.commodityState
    createOrderViewCommodity.coursewareSupplierId = lsCommodityDetail.coursewareSupplierId
    createOrderViewCommodity.coursewareSupplierName = lsCommodityDetail.coursewareSupplierName
    createOrderViewCommodity.issueId = lsCommodityDetail.issueId
    createOrderViewCommodity.issueName = lsCommodityDetail.issueName
    createOrderViewCommodity.period = lsCommodityDetail.period
    createOrderViewCommodity.price = lsCommodityDetail.price
    createOrderViewCommodity.schemeId = lsCommodityDetail.schemeId
    createOrderViewCommodity.schemeName = lsCommodityDetail.schemeName
    createOrderViewCommodity.schemePicturePath = lsCommodityDetail.schemePicturePath
    createOrderViewCommodity.stageId = lsCommodityDetail.stageId
    createOrderViewCommodity.stageName = lsCommodityDetail.stageName
    createOrderViewCommodity.suitableCrowNames = lsCommodityDetail.suitableCrowNames
    createOrderViewCommodity.trainingCategoryId = lsCommodityDetail.trainingCategoryId
    createOrderViewCommodity.trainingCategoryName = lsCommodityDetail.trainingCategoryName
    createOrderViewCommodity.trainingCategoryNamePath = lsCommodityDetail.trainingCategoryNamePath
    createOrderViewCommodity.trainingInstitutionAbouts = lsCommodityDetail.trainingInstitutionAbouts
    createOrderViewCommodity.trainingInstitutionId = lsCommodityDetail.trainingInstitutionId
    createOrderViewCommodity.trainingInstitutionName = lsCommodityDetail.trainingInstitutionName
    createOrderViewCommodity.workTypeId = lsCommodityDetail.workTypeId
    createOrderViewCommodity.workTypeName = lsCommodityDetail.workTypeName
    return createOrderViewCommodity
  }
}
