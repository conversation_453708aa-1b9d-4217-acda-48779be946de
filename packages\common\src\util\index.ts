import { Message } from 'element-ui'
import router from '@/router'
import { handleClipboard } from '@hbfe/jxjy-admin-common/src/clipboard'
import ConfigCenterModule from '@api/service/common/config-center/ConfigCenter'
import FileModule from '@api/service/common/file/FileModule'

export class UtilClass {
  /**
   * @description: 获取级联选择器的值  多选   二维数组转一维数组
   * @param {string} val
   * @return {*}
   */
  getCascaderValueByMultiple(val: string[][]) {
    if (!val) {
      return []
    }
    return val.map((el) => {
      return el[el?.length - 1]
    })
  }

  /**
   * @description: 获取工种级联选择器的值  多选   数组转path路径
   * @param {string} val
   * @return {*}
   */
  getCascaderValueByMultipleByPath(val: string[][]) {
    if (!val) {
      return []
    }
    return val.map((el) => {
      return `/${el[el.length - 2]}/${el[el.length - 1]}`
    })
  }
  /**
   * @description:  打开指定域名的地址,根据开发环境和生产环境切换域名形态
   * @param {string} url 打开的路由
   * @param {*} target 打开方式
   * @return {*}
   */
  openUrl(url: string, target = '_blank') {
    const resolver = router().resolve({
      name: url
    })
    let prefix = '/admin/#'
    if (process.env.NODE_ENV !== 'production') {
      prefix = '/#'
    }
    window.open(prefix + resolver.location.name, target)
  }

  /**
   * 粘贴板
   */
  copy = handleClipboard

  // 业务工具函数（比较有项目特色的）
  /**
   * @description: 获取多选工种选择器、多选全国地区选择器返回的路径
   * @param {string} val
   * @return {*} 路径字符串
   */
  getCategoryWorkTypePath(val: string[][]) {
    return val?.map((el) => {
      return '/' + el.join('/')
    })
  }

  /**
   * @description: 格式化表格空数据
   * @param {any} row el-table 行
   * @param {any} column  el-table  列
   * @param {any} cellValue 当前单元格的值
   * @return {*}
   */
  formatTableEmptyData(row: any, column: any, cellValue: any) {
    return cellValue ? cellValue : '-'
  }

  /**
   * @description: 单纯复制文字
   * @param {string} text 复制的文字
   * @return {*}
   */
  textCopy(text: string) {
    return new Promise((resolve, reject) => {
      try {
        const input = document.createElement('input')
        input.value = text
        document.body.appendChild(input)
        input.select()
        document.execCommand('copy')
        document.body.removeChild(input)
        Message.success('复制成功！')
      } catch (error) {
        console.error(error)

        Message.error('该浏览器暂时不支持复制功能哦~')
        reject()
      }
      resolve(text)
    })
  }

  async openWindow(loadingText = '加载中...') {
    const windowInstance = window.open('about:blank')
    windowInstance.document.write(`<h1>${loadingText}</h1>`)
    return {
      setUrl(url: string) {
        windowInstance.location.href = url
      },
      close() {
        windowInstance.close()
      }
    }
  }
  isMobileDevice() {
    // 方法1：检查User-Agent
    const userAgentCheck = () => {
      const ua = navigator.userAgent || navigator.vendor || (window as any).opera
      return /android|webos|iphone|ipod|blackberry|iemobile|opera mini|mobile|tablet|ipad/i.test(ua)
    }

    // 方法2：检查屏幕宽度
    const screenWidthCheck = () => {
      return window.innerWidth <= 768
    }

    // 方法3：检查触摸功能
    const touchCheck = () => {
      return 'ontouchstart' in window || navigator.maxTouchPoints > 0 || navigator.msMaxTouchPoints > 0
    }

    // 方法4：检查设备方向功能（通常移动设备才有）
    const orientationCheck = () => {
      return typeof window.orientation !== 'undefined'
    }

    // 综合判断：至少满足两个条件才认为是移动设备
    let score = 0
    if (userAgentCheck()) score++
    if (screenWidthCheck()) score++
    if (touchCheck()) score++
    if (orientationCheck()) score++

    return score >= 2 // 满足至少两个条件则判定为移动设备
  }

  /**
   * 自动补全域名
   * https://hb.btpxv2.test1.59iedu.com:9443/home
   * domain =>  hb.btpxv2.test1.59iedu.com
   */
  parseUrl(domain: string) {
    // 兼容后端返回带有https
    if (domain?.indexOf('//') > -1) {
      return domain ? domain.split('.')[0] + '.' + this.domainUrl : ''
    }
    return domain ? this.domainUrlPrefix + domain.split('.')[0] + '.' + this.domainUrl : ''
  }

  /**
   * @description: 判断是不是现网环境
   * @param {*}
   * @return {*} true 生产环境
   */

  isProduction() {
    const href = window.location.href
    const excludeUrl = ['dev', 'test1', 'test2']
    const result = excludeUrl.filter((el) => {
      return href.includes(el)
    })
    return result.length ? false : true
  }
  /**
   * 获取域名网址
   */
  get domainUrl() {
    return ConfigCenterModule.getApplicationByName('applicationDiff.platformNoPrefixDomain')
  }

  get domainUrlPrefix() {
    return ConfigCenterModule.getApplicationByName('application.platformProxyDomainPrefix')
  }

  channel = this.isMobileDevice() ? null : new BroadcastChannel('myChannel') // * 统一管理频道

  /**
   * 缩略图url拼接token
   * @description 判断逻辑：
   * 1.先判断src是否有值
   * 2.判断src是否受保护的（包含/ms-file/protected/）
   *  2.1 是受保护的，则拼接token
   *  2.2 不是受保护的，则直接返回
   */
  imgUrlWithToken(src: string) {
    if (!src) return
    const protectedPrefixReg = /\/ms-file\/protected\//
    if (protectedPrefixReg.test(src)) {
      FileModule.applyResourceAccessToken()
      if (src?.includes('token')) {
        return `${src.split('?')[0]}?token=${FileModule.resourceAccessToken}`
      }
      return `${src}?token=${FileModule.resourceAccessToken}`
    } else {
      return src
    }
  }
}

export default new UtilClass()
