import { TimeTypeEnum } from '@api/service/management/implement/enums/TimeTypeEnum'
import { ReportingTypeEnum } from '@api/service/common/implement/enums/ReportingTypeEnum'

export default class ReportConfigDto {
  /**
   * 报道形式
   */
  reportingType: ReportingTypeEnum = undefined

  /**
   * 上午时间类型
   */
  morningClassTimeType: TimeTypeEnum = undefined

  /**
   * 下午时间类型
   */
  afternoonClassTimeType: TimeTypeEnum = undefined

  /**
   * 上午上课时间
   */
  morningClassTime: string = undefined

  /**
   * 下午上课时间
   */
  afternoonClassTime: string = undefined

  /**
   * 听课模板ID
   */
  attendanceProofId: string = undefined

  /**
   * 听课模板名称
   */
  attendanceProofName: string = undefined

  /**
   * 听课模板文件路径
   */
  attendanceProofPath: string = undefined

  /**
   * 有效范围
   */
  validRange = 500
}
