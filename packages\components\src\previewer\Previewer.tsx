import Vue from 'vue'
import PreviewCourseware from '@hbfe/jxjy-admin-components/src/previewer/components/preview-courseware.vue'
import CoursewareType from '@api/service/management/resource/courseware/enum/CoursewareType'

class Previewer {
  previewCourseware(
    id: string,
    courseWareType?: CoursewareType,
    list?: Array<{ name: string; id: string; type: string }>
  ) {
    const div = document.createElement('div')
    div.setAttribute('class', 'center')
    document.body.appendChild(div)
    return new Promise(resolve => {
      new Vue({
        components: {
          PreviewCourseware
        },
        data() {
          return {
            visible: true,
            destroyOnClose: true
          }
        },
        methods: {
          close() {
            this.visible = false
          }
        },
        mounted() {
          this.$nextTick(() => {
            resolve(this.$refs.target)
          })
        },
        render() {
          return (
            <preview-courseware
              courseWareType={courseWareType}
              coursewareId={id}
              visible={this.visible}
              view-list={list}
            ></preview-courseware>
          )
        }
      }).$mount(div)
    })
  }
}

export default new Previewer()
