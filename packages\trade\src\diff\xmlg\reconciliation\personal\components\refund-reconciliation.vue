<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <!--条件查询-->
      <template
        v-if="$hasPermission('refundReconciliation,refundReconciliationFx,refundReconciliationZt')"
        query
        desc="refundReconciliation:退款订单对账,refundReconciliationFx:分销退款订单对账,refundReconciliationZt:专题退款订单对账"
        actions="refundReconciliation:doSearch,@BizPortalSelect,@BizDistributorSelect#refundReconciliationFx:doSearchFx,@BizPortalDistributorSelect#refundReconciliationZt:doSearchZt"
      >
        <hb-search-wrapper @reset="reset" class="m-query is-border-bottom">
          <el-form-item label="收款账号" v-if="!isZtlogin">
            <el-input
              id="input1"
              v-model="accountName"
              clearable
              placeholder="请选择收款账号"
              @focus="editInvoicePopup()"
            />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input v-model="refundCheckAccountParam.orderId" clearable placeholder="请输入订单号" />
          </el-form-item>
          <el-form-item label="交易流水号">
            <el-input v-model="refundCheckAccountParam.batchId" clearable placeholder="请输入交易流水号" />
          </el-form-item>
          <el-form-item label="退款单号">
            <el-input v-model="refundCheckAccountParam.refundId" clearable placeholder="请输入退款单号" />
          </el-form-item>
          <el-form-item label="退款成功时间">
            <double-date-picker
              :begin-create-time.sync="refundCheckAccountParam.startDate"
              :end-create-time.sync="refundCheckAccountParam.endDate"
            ></double-date-picker>
          </el-form-item>

          <el-form-item label="销售渠道" v-if="!isFxlogin && !isZtlogin">
            <el-select v-model="refundCheckAccountParam.saleSource" clearable filterable placeholder="请选择销售渠道">
              <el-option
                v-for="item in saleChannelList"
                :key="item.code"
                :value="item.code"
                :label="item.desc"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="培训方案" v-if="!isFxlogin && !isZtlogin">
            <!-- <biz-learning-scheme-select v-model="commoditySkuIdList"></biz-learning-scheme-select> -->
            <learning-scheme-select-diff
              v-model="commoditySkuIdList"
              @clearPeriod="clearPeriod()"
            ></learning-scheme-select-diff>
          </el-form-item>
          <el-form-item label="培训方案" v-if="isFxlogin">
            <fx-learning-scheme-select v-model="commoditySkuIdList"></fx-learning-scheme-select>
            <!-- <learning-scheme-select v-model="commoditySkuIdList"></learning-scheme-select> -->
          </el-form-item>
          <el-form-item label="培训方案" v-if="isZtlogin">
            <zt-learning-scheme-select v-model="commoditySkuIdList"></zt-learning-scheme-select>
          </el-form-item>
          <el-form-item label="期别名称" v-if="showPeriodName && !isFxlogin">
            <biz-period-select :scheme-id="commoditySkuIdList[0].id" v-model="refundCheckAccountParam.periodId" />
          </el-form-item>
          <el-form-item label="期别名称" v-if="showPeriodName && isFxlogin">
            <biz-fx-period-select :scheme-id="commoditySkuIdList[0].id" v-model="refundCheckAccountParam.periodId" />
          </el-form-item>

          <!--  v-if="isFXshow" -->
          <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="分销商">
            <biz-distributor-select
              v-model="refundCheckAccountParam.distributorId"
              :name="distributorName"
            ></biz-distributor-select>
          </el-form-item>
          <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
            <biz-portal-select
              v-model="refundCheckAccountParam.promotionPortalId"
              :disabled="refundCheckAccountParam.isDistributionExcludePortal"
              :name="promotionPortalName"
            ></biz-portal-select>
          </el-form-item>
          <el-form-item v-if="isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
            <biz-portal-distributor-select
              v-model="refundCheckAccountParam.promotionPortalId"
              :disabled="refundCheckAccountParam.isDistributionExcludePortal"
              :name="promotionPortalName"
            ></biz-portal-distributor-select>
          </el-form-item>
          <el-form-item v-if="!isZtlogin">
            <el-checkbox
              label="查看非门户推广数据"
              name="type"
              @change="refundCheckAccountParam.promotionPortalId = ''"
              v-model="refundCheckAccountParam.isDistributionExcludePortal"
            ></el-checkbox>
          </el-form-item>

          <el-form-item label="专题名称" v-if="topPicNameFilterShow && !isFxlogin">
            <el-input v-model="refundCheckAccountParam.specialSubjectName" clearable placeholder="请输入专题进行查询" />
          </el-form-item>
          <el-form-item label="退货/款类别">
            <biz-refund-category v-model="refundCheckAccountParam.refundType" :is-diff="true" />
          </el-form-item>
          <!--  v-if="isFXshow" -->
          <template slot="actions">
            <el-button type="primary" @click="search">查询</el-button>
            <template
              v-if="$hasPermission('export,exportFx,exportZt')"
              query
              desc="export:导出,exportFx:导出（分销）,exportZt:导出（专题）"
              actions="export:exportDataTy#exportFx:exportDataFx#exportZt:exportDataZt"
            >
              <el-button @click="exportData">导出列表数据</el-button>
            </template>
          </template>
        </hb-search-wrapper>
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert">
            <div class="f-c6">
              当前共有 <span class="f-fb f-co">{{ refundTradeCount }}</span> 笔退款订单，退款总额
              <span class="f-fb f-co">¥ {{ refundAmountCount }}</span
              >。
            </div>
          </el-alert>
        </div>
        <!--表格-->
        <el-table
          stripe
          ref="tableRef"
          :data="refundCheckAccountListResponse"
          max-height="500px"
          v-loading="query.loading"
          @sort-change="sortChange"
          class="m-table f-mt10"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="订单号" min-width="220" fixed="left">
            <template #default="scope">
              <hb-copy :content="scope.row.orderId"></hb-copy>
              {{ scope.row.orderId }}
              <p>
                <el-tag type="warning" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.distribution"
                  >分销推广
                </el-tag>
                <el-tag type="success" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag>
                <el-tag type="danger" size="small" v-if="scope.row.thirdPartyPlatform">{{
                  scope.row.thirdPartyPlatform
                }}</el-tag>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="交易流水号" prop="batchId" min-width="300"> </el-table-column>
          <el-table-column label="退款单号" min-width="300">
            <template #default="scope"
              ><a class="f-link f-cb f-underline" @click="goDetail(scope.row.refundId)">{{
                scope.row.refundId
              }}</a></template
            >
          </el-table-column>
          <el-table-column label="退款成功时间" min-width="180" prop="startDate" sortable>
            <!-- <template>2020-11-11 12:20:20</template> -->
          </el-table-column>
          <el-table-column label="购买人信息" min-width="240">
            <template #default="scope">
              <p>姓名：{{ scope.row.name || '-' }}</p>
              <p v-if="queryShowLoginAccount.isShowLoginAccount">登录账号：{{ scope.row.loginAccount || '-' }}</p>
              <p>证件号：{{ scope.row.idCard || '-' }}</p>
              <p>手机号：{{ scope.row.phone || '-' }}</p>
            </template>
          </el-table-column>
          <el-table-column label="退货/款类型" width="150">
            <template slot-scope="scope">
              {{ OrderRefundType.map.get(scope.row.refundType) || '-' }}
            </template>
          </el-table-column>
          <el-table-column label="退款金额(元)" width="140" prop="money" align="right"> </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-个人报名退款对账</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
          </div>
        </el-dialog>
        <template v-if="$hasPermission('editInvoicePopup')" query desc="选择收款账号" actions="@accountNumber">
          <account-number
            :visible.sync="editInvoiceDialog"
            ref="accountNumberRef"
            :get-data="getData"
            @getAccountNumber="getAccountNumber"
          ></account-number>
        </template>
      </template>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import accountNumber from '@hbfe/jxjy-admin-trade/src/refund/components/account-number.vue'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import QueryCheckAccount from '@api/service/diff/management/xmlg/trade/order/QueryCheckAccount'
  import RefundCheckAccountListResponse from '@api/service/diff/management/xmlg/trade/order/model/RefundCheckAccountListResponse'
  import RefundCheckAccountParam from '@api/service/diff/management/xmlg/trade/order/model/RefundCheckAccountParam'
  import ReceiveAccountVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountVo'
  import { SortPolicy } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { ReturnSortRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/xmlg/trade/enums/SaleChannelType'
  import LearningSchemeSelectDiff from '@hbfe/jxjy-admin-trade/src/diff/xmlg/order/personal/components/learning-scheme-select-diff.vue'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'

  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import BizPortalDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-distributor-select.vue'
  import { bind, debounce } from 'lodash-decorators'
  import QueryCheckAccountInTrainingChannel from '@api/service/diff/management/xmlg/trade/single/checkAccount/query/QueryCheckAccountInTrainingChannel'
  import MutationCheckAccountInTrainingChannel from '@api/service/management/trade/single/checkAccount/mutation/MutationCheckAccountInTrainingChannel'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import OrderRefundType from '@api/service/common/return-order/enums/OrderRefundType'
  import ZtLearningSchemeSelect from '@hbfe/jxjy-admin-trade/src/order/personal/components/zt-learning-scheme-select.vue'
  import FxLearningSchemeSelect from '@hbfe/fx-manage/src/components/biz/biz-learning-scheme-select.vue'
  @Component({
    components: {
      DoubleDatePicker,
      accountNumber,
      LearningSchemeSelectDiff,
      BizDistributorSelect,
      BizPortalSelect,
      BizPortalDistributorSelect,
      FxLearningSchemeSelect,
      ZtLearningSchemeSelect
    }
  })
  export default class extends Vue {
    @Ref('accountNumberRef') accountNumberRef: any
    SaleChannelEnum = SaleChannelEnum
    page: UiPage
    query: Query = new Query()
    queryCheckAccount = new QueryCheckAccount()
    refundCheckAccountParam = new RefundCheckAccountParam()
    exportQueryParam: RefundCheckAccountParam = new RefundCheckAccountParam()
    refundCheckAccountListResponse: Array<RefundCheckAccountListResponse> = new Array<RefundCheckAccountListResponse>()
    accountName = ''
    getData = new ReceiveAccountVo()
    sortRequest: Array<ReturnSortRequest> = new Array<ReturnSortRequest>()
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    queryZtReconciliation = new QueryCheckAccountInTrainingChannel()
    mutationZtCheckAccount = new MutationCheckAccountInTrainingChannel()
    TrainingModeEnum = TrainingModeEnum // 退款类型
    // 退款类型
    OrderRefundType = OrderRefundType
    /**
     * 打开-弹窗标识
     */
    editInvoiceDialog = false
    form = {
      data1: ''
    }
    loading = false // 加载中
    //导出成功弹窗
    exportSuccessVisible = false

    // 获取销售渠道列表
    saleChannelList = SaleChannelType.list()
    /**
     * 商品id数组
     */
    commoditySkuIdList = new Array<HasSelectSchemeMode>()
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    // 专题名称筛选显示
    get topPicNameFilterShow() {
      return (
        this.refundCheckAccountParam.saleSource === SaleChannelEnum.topic ||
        (!this.refundCheckAccountParam.saleSource && this.refundCheckAccountParam.saleSource !== SaleChannelEnum.self)
      )
    }
    get showPeriodName() {
      return [this.TrainingModeEnum.mixed, this.TrainingModeEnum.offline].includes(
        this.commoditySkuIdList[0]?.trainingMode?.skuPropertyValueId
      )
    }
    @Watch('commoditySkuIdList', { deep: true })
    trainingProgrammeListChange() {
      this.refundCheckAccountParam.periodId = ''
    }
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable

    promotionPortalName = ''
    distributorName = ''
    get refundTradeCount() {
      if (!this.isZtlogin) {
        return this.queryCheckAccount.refundTradeCount
      } else {
        return this.queryZtReconciliation.refundTradeCount
      }
    }
    get refundAmountCount() {
      if (!this.isZtlogin) {
        return this.queryCheckAccount.refundAmountCount
      } else {
        return this.queryZtReconciliation.refundAmountCount
      }
    }
    constructor() {
      super()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.page = new UiPage(this.doSearchFx, this.doSearchFx)
      } else if (this.isZtlogin) {
        this.page = new UiPage(this.doSearchZt, this.doSearchZt)
      } else {
        this.page = new UiPage(this.doSearch, this.doSearch)
      }
    }
    // 培训方案入参
    @Watch('commoditySkuIdList', {
      deep: true
    })
    changeScheme(val: Array<HasSelectSchemeMode>) {
      if (val?.length && val[0]?.schemeId) {
        this.refundCheckAccountParam.commoditySkuId = val[0].schemeId
      } else {
        this.refundCheckAccountParam.commoditySkuId = undefined
      }
    }
    async doSearch() {
      this.query.loading = true
      try {
        this.refundCheckAccountListResponse = []
        this.refundCheckAccountParam.trainingProgramId = this.commoditySkuIdList[0]?.schemeId
        this.refundCheckAccountListResponse = await this.queryCheckAccount.queryOfRefundOrder(
          this.page,
          this.refundCheckAccountParam
        )
        ;(this.$refs['tableRef'] as any)?.doLayout()
        console.log('this.refundCheckAccountListResponse', this.refundCheckAccountListResponse)
      } catch (e) {
        console.log(e, '退款订单查询列表失败')
      } finally {
        this.query.loading = false
      }
    }
    async doSearchFx() {
      this.query.loading = true
      try {
        this.refundCheckAccountListResponse = []
        this.refundCheckAccountParam.trainingProgramId = this.commoditySkuIdList[0]?.schemeId
        this.refundCheckAccountListResponse = await this.queryCheckAccount.queryOfFxRefundOrder(
          this.page,
          this.refundCheckAccountParam
        )
        ;(this.$refs['tableRef'] as any)?.doLayout()
      } catch (e) {
        console.log(e, '分销退款订单查询列表失败')
      } finally {
        this.query.loading = false
      }
    }

    async doSearchZt() {
      this.query.loading = true
      try {
        this.refundCheckAccountListResponse = []
        this.refundCheckAccountParam.trainingProgramId = this.commoditySkuIdList[0]?.schemeId
        this.refundCheckAccountListResponse =
          (await this.queryOfZtRefundOrder()) as Array<RefundCheckAccountListResponse>
        ;(this.$refs['tableRef'] as any)?.doLayout()
      } catch (e) {
        console.log(e, '分销退款订单查询列表失败')
      } finally {
        this.query.loading = false
      }
    }
    async queryOfZtRefundOrder() {
      return await this.queryZtReconciliation.queryOfRefundOrder(this.page, this.refundCheckAccountParam)
    }
    async exportDataTy() {
      return await this.queryCheckAccount.listReturnExport(this.exportQueryParam)
    }

    async exportDataFx() {
      return await this.queryCheckAccount.listFxReturnExport(this.exportQueryParam)
    }
    async exportDataZt() {
      return await this.mutationZtCheckAccount.listReturnExport(this.exportQueryParam)
    }
    async exportData() {
      try {
        this.exportQueryParam = Object.assign(new RefundCheckAccountParam(), this.refundCheckAccountParam)

        let res
        if (this.isHadFxAbility && this.isFxlogin) {
          res = await this.exportDataFx()
        } else if (this.isZtlogin) {
          res = await this.exportDataZt()
        } else {
          res = await this.exportDataTy()
        }
        if (res) {
          //   this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }
    // 请求
    async search() {
      this.page.pageNo = 1
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.doSearchFx()
      } else if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }
    async reset() {
      // this.questionRequestVo = new QuestionRequestVo()
      this.page.pageNo = 1
      this.refundCheckAccountParam = new RefundCheckAccountParam()
      this.commoditySkuIdList = new Array<HasSelectSchemeMode>()
      this.promotionPortalName = ''
      this.distributorName = ''
      this.accountName = ''
      await this.search()
    }
    sortChange(column: any) {
      // console.log(column.order, 1)
      this.sortRequest = []
      const item = new ReturnSortRequest()
      if (column.order === 'ascending') {
        //正序
        item.policy = 'ASC' as SortPolicy
        this.sortRequest.push(item)
      } else if (column.order === 'descending') {
        item.policy = 'DESC' as SortPolicy
        this.sortRequest.push(item)
      } else {
        this.sortRequest = []
      }
      // console.log(this.sortRequest, ' this.sortRequest')
    }
    async editInvoicePopup() {
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.accountNumberRef.doQueryPagefx()
      } else {
        await this.accountNumberRef.doQueryPage()
      }
      const inputEl = document.getElementById('input1')
      inputEl.blur()
      this.editInvoiceDialog = true
    }
    getAccountNumber(idList: ReceiveAccountVo[]) {
      this.getData = idList[0]
      this.refundCheckAccountParam.paymentAccountID = this.getData?.id ? this.getData.id : ''
      this.accountName = this.getData?.accountName
    }
    @Watch('accountName', {
      deep: true,
      immediate: true
    })
    accountNameChange() {
      if (this.accountName === '') {
        this.getAccountNumber([])
      }
    }

    // 清空期别
    clearPeriod() {
      this.refundCheckAccountParam.periodId = undefined
    }
    goDownloadPage() {
      this.exportSuccessVisible = false

      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportRefundReconciliation' }
      })
    }
    // 跳转退款详情页
    goDetail(id: string) {
      const routeData = this.$router.resolve('/training/trade/refund/personal/detail/' + id)
      window.open(routeData.href, '_blank')
    }
  }
</script>
