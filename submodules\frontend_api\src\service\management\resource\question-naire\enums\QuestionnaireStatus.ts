import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum QuestionnaireStatusEnum {
  /**
   * 全部
   */
  all = 0,
  /**
   * 草稿
   */
  draft = 1,
  /**
   * 发布
   */
  publish = 2
}
class QuestionnaireStatus extends AbstractEnum<QuestionnaireStatusEnum> {
  static enum = QuestionnaireStatusEnum

  constructor(status?: QuestionnaireStatusEnum) {
    super()
    this.current = status
    this.map.set(QuestionnaireStatusEnum.all, '全部')
    this.map.set(QuestionnaireStatusEnum.draft, '草稿')
    this.map.set(QuestionnaireStatusEnum.publish, '发布')
  }
}
export default QuestionnaireStatus
