<template>
  <!-- 学习监管规则 -->
  <el-card shadow="never" class="m-card f-mb15">
    <div slot="header" class="">
      <span class="tit-txt">学习监管</span>
    </div>
    <el-form
      ref="learningSupervisionForm"
      :rules="rules"
      label-width="160px"
      :model="antiCheatConfig.antiCheatConfig.studyConfig"
      class="m-form"
      hide-required-asterisk
    >
      <el-row type="flex" justify="center" class="width-limit m-border">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form-item label="监管场景：">进入学习</el-form-item>
          <el-form-item label="监管状态：">
            <el-switch v-model="antiCheatConfig.antiCheatConfig.studyConfig.enterLearnVerify"></el-switch>
          </el-form-item>
          <el-form-item
            label="监管方式："
            v-if="antiCheatConfig.antiCheatConfig.studyConfig.enterLearnVerify"
            prop="enterVerify"
          >
            <el-radio-group :value="antiCheatConfig.antiCheatConfig.studyConfig.enterVerify[0]" @input="enterVerifyWay">
              <el-radio v-if="isOpenFaceVerify" :label="VerifyTypeEnum.face" class="f-mr30"
                >{{ VerifyType.map.get(VerifyTypeEnum.face) }}
              </el-radio>
              <el-radio :label="VerifyTypeEnum.systemRandom" class="f-mr10"
                >{{ VerifyType.map.get(VerifyTypeEnum.systemRandom) }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row type="flex" justify="center" class="width-limit m-border f-mt15">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form-item label="监管场景：">学习过程</el-form-item>
          <el-form-item label="监管状态：">
            <el-switch v-model="antiCheatConfig.antiCheatConfig.studyConfig.processVerify"></el-switch>
          </el-form-item>
          <template v-if="antiCheatConfig.antiCheatConfig.studyConfig.processVerify">
            <el-form-item label="监管方式：" prop="processVerifyType">
              <el-radio-group
                :value="antiCheatConfig.antiCheatConfig.studyConfig.processVerifyType[0]"
                @input="processVerifyWay"
              >
                <el-radio v-if="isOpenFaceVerify" :label="VerifyTypeEnum.face" class="f-mr30"
                  >{{ VerifyType.map.get(VerifyTypeEnum.face) }}
                </el-radio>
                <el-radio :label="VerifyTypeEnum.systemRandom" class="f-mr10"
                  >{{ VerifyType.map.get(VerifyTypeEnum.systemRandom) }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="监管频率：" prop="processVerifyMode">
              <el-radio-group
                :value="antiCheatConfig.antiCheatConfig.studyConfig.processVerifyMode"
                @input="processVerifyMode"
              >
                <el-radio
                  :label="ProcessVerifyModeEnum.TIME"
                  style="display: block; margin-bottom: 20px; line-height: 36px"
                >
                  间隔
                  <el-form-item style="display: inline-block" prop="playVerifyIntervalTime">
                    <el-input
                      v-model="antiCheatConfig.antiCheatConfig.studyConfig.playVerifyIntervalTime"
                      placeholder="请输入"
                      class="f-input-num f-mlr5 f-mb5"
                    ></el-input>
                  </el-form-item>
                  分钟监管1次，监管通过方可继续学习（间隔时间以课件播放时长计算，课件时长不足设置的间隔分钟，则在课件进度100%时监管）
                </el-radio>
                <el-radio
                  :label="ProcessVerifyModeEnum.SCHEDULE"
                  class="f-mt10"
                  style="display: block; line-height: 36px"
                >
                  间隔
                  <el-form-item style="display: inline-block; margin-bottom: 20px" prop="scheduleVerifyIntervalTime">
                    <el-input
                      :value="antiCheatConfig.antiCheatConfig.studyConfig.scheduleVerifyIntervalTime"
                      @input="inputScheduleVerifyIntervalTime"
                      placeholder="请输入"
                      class="f-input-num f-mlr5"
                    ></el-input>
                  </el-form-item>
                  %监管1次，监管通过方可继续学习（间隔进度以课件播放进度计算）
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="重试次数：" prop="reVerifyRuleMode">
              <el-radio-group
                :value="antiCheatConfig.antiCheatConfig.studyConfig.reVerifyRuleMode"
                @input="reVerifyRuleMode"
              >
                <el-radio :label="ReVerifyRuleModeEnum.UNTIL_SUCCESS" style="display: block; line-height: 36px">
                  无限次
                </el-radio>
                <el-radio
                  :label="ReVerifyRuleModeEnum.LIMIT_NUM"
                  class="f-mt10"
                  style="display: block; line-height: 36px"
                >
                  重试
                  <el-form-item style="display: inline-block; margin-bottom: 20px" prop="reVerifyNums">
                    <el-input
                      :value="antiCheatConfig.antiCheatConfig.studyConfig.reVerifyNums"
                      @input="inputReVerifyNums"
                      placeholder="请输入"
                      class="f-input-num f-mlr5"
                    ></el-input>
                  </el-form-item>
                  次仍未通过记为监管不通过
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="监管不通过处理结果：" prop="resultVerifyMode">
              <el-radio-group
                :value="antiCheatConfig.antiCheatConfig.studyConfig.resultVerifyMode"
                @input="resultVerifyMode"
              >
                <el-radio :label="FailureHandleStrategy.NONE" style="display: block; line-height: 36px">
                  两次监管之间学习进度有效
                </el-radio>
                <el-radio
                  :label="FailureHandleStrategy.INVALIDATE_LAST_CHECK_POINT"
                  class="f-mt10"
                  style="display: block; line-height: 36px"
                >
                  两次监管之间学习进度无效
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </template>
        </el-col>
      </el-row>
    </el-form>
  </el-card>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import AntiCheatConfig from '@hbfe-biz/biz-anticheat/dist/config/AntiCheatConfig'
  import VerifyType, { VerifyTypeEnum } from '@hbfe-biz/biz-anticheat/dist/config/enums/VerifyType'
  import {
    FailureHandleStrategy,
    ProcessVerifyModeEnum,
    ReVerifyRuleModeEnum
  } from '@hbfe-biz/biz-anticheat/dist/config/enums/VerifyConfigModeEnum'
  import { ElForm } from 'element-ui/types/form'

  @Component
  export default class extends Vue {
    @PropSync('antiCheatConfigProp', {
      type: AntiCheatConfig,
      required: true,
      default: () => new AntiCheatConfig()
    })
    antiCheatConfig!: AntiCheatConfig

    @Prop({
      type: Boolean,
      default: false
    })
    isOpenFaceVerify!: boolean

    @Ref('learningSupervisionForm')
    learningSupervisionForm: ElForm

    VerifyTypeEnum = VerifyTypeEnum

    ProcessVerifyModeEnum = ProcessVerifyModeEnum

    ReVerifyRuleModeEnum = ReVerifyRuleModeEnum

    FailureHandleStrategy = FailureHandleStrategy

    VerifyType = VerifyType

    /**
     * 学习过程间隔进度校验
     */
    playVerifyIntervalTimeRule = (rule: any, value: any, callback: (arg?: Error) => void) => {
      const exp = /(^[0-9]\d*$)/
      if (value === undefined || value === '') {
        return callback(new Error('请输入间隔时间'))
      } else if (!exp.test(value) || isNaN(Number(value))) {
        return callback(new Error('请输入正确的数值'))
      } else {
        return callback()
      }
    }
    /**
     * 学习过程间隔进度校验
     */
    scheduleVerifyIntervalTimeRule = (rule: any, value: any, callback: (arg?: Error) => void) => {
      const exp = /(^[0-9]\d*$)/
      if (value === undefined || value === '') {
        return callback(new Error('请输入间隔进度'))
      } else if (!exp.test(value) || isNaN(Number(value))) {
        return callback(new Error('请输入正确的数值'))
      } else {
        return callback()
      }
    }

    /**
     * 重试次数校验
     */
    reVerifyRule = (rule: any, value: any, callback: (arg?: Error) => void) => {
      const exp = /(^[0-9]\d*$)/
      if (value === undefined || value === '') {
        return callback(new Error('请输入重试次数'))
      } else if (!exp.test(value) || isNaN(Number(value))) {
        return callback(new Error('请输入正确的数值'))
      } else {
        return callback()
      }
    }

    get rules() {
      return {
        enterVerify: {
          required: this.antiCheatConfig.antiCheatConfig.studyConfig.enterLearnVerify,
          message: '请选择监管方式',
          trigger: ['blur', 'change']
        },
        processVerifyType: {
          required: this.antiCheatConfig.antiCheatConfig.studyConfig.processVerify,
          message: '请选择监管方式',
          trigger: ['blur', 'change']
        },
        processVerifyMode: {
          required: this.antiCheatConfig.antiCheatConfig.studyConfig.processVerify,
          message: '请选择监管频率规则',
          trigger: ['blur', 'change']
        },
        reVerifyRuleMode: {
          required: this.antiCheatConfig.antiCheatConfig.studyConfig.processVerify,
          message: '请选择重试次数规则',
          trigger: ['blur', 'change']
        },
        resultVerifyMode: {
          required: this.antiCheatConfig.antiCheatConfig.studyConfig.processVerify,
          message: '请选择监管不通过处理结果规则',
          trigger: ['blur', 'change']
        },
        playVerifyIntervalTime: {
          validator:
            this.antiCheatConfig.antiCheatConfig.studyConfig.processVerifyMode === ProcessVerifyModeEnum.TIME
              ? this.playVerifyIntervalTimeRule
              : (rule: any, value: any, callback: (arg?: Error) => void) => {
                  return callback()
                },
          trigger: 'blur'
        },
        scheduleVerifyIntervalTime: {
          validator:
            this.antiCheatConfig.antiCheatConfig.studyConfig.processVerifyMode === ProcessVerifyModeEnum.SCHEDULE
              ? this.scheduleVerifyIntervalTimeRule
              : (rule: any, value: any, callback: (arg?: Error) => void) => {
                  return callback()
                },
          trigger: 'blur'
        },
        reVerifyNums: {
          validator:
            this.antiCheatConfig.antiCheatConfig.studyConfig.reVerifyRuleMode === ReVerifyRuleModeEnum.LIMIT_NUM
              ? this.reVerifyRule
              : (rule: any, value: any, callback: (arg?: Error) => void) => {
                  return callback()
                },
          trigger: 'blur'
        }
      }
    }

    /**
     * 监管方式选择
     * @param val
     */
    enterVerifyWay(val: VerifyTypeEnum) {
      this.antiCheatConfig.antiCheatConfig.studyConfig.enterVerify = [val]
      this.learningSupervisionForm.validateField(['enterVerify'])
    }

    processVerifyWay(val: VerifyTypeEnum) {
      this.antiCheatConfig.antiCheatConfig.studyConfig.processVerifyType = [val]
      this.learningSupervisionForm.validateField(['processVerifyType'])
    }

    /**
     * 监管频率方式选择
     * @param val
     */
    processVerifyMode(val: ProcessVerifyModeEnum) {
      this.antiCheatConfig.antiCheatConfig.studyConfig.processVerifyMode = val
      // 添加这行，手动触发表单验证
      this.$nextTick(() => {
        val === ProcessVerifyModeEnum.TIME
          ? (this.antiCheatConfig.antiCheatConfig.studyConfig.scheduleVerifyIntervalTime = undefined)
          : (this.antiCheatConfig.antiCheatConfig.studyConfig.playVerifyIntervalTime = undefined)
        this.learningSupervisionForm.validateField([
          'processVerifyMode',
          'playVerifyIntervalTime',
          'scheduleVerifyIntervalTime'
        ])
      })
    }

    /**
     * 拦截间隔进度输入框
     */
    inputScheduleVerifyIntervalTime(newValue: number | string) {
      let val = Math.min(Math.max(isNaN(Number(newValue)) ? 0 : Number(newValue), 1), 100)
      if (newValue === '') {
        val = undefined
      }
      this.antiCheatConfig.antiCheatConfig.studyConfig.scheduleVerifyIntervalTime = val
    }

    /**
     * 重试规则选择
     * @param val
     */
    reVerifyRuleMode(val: ReVerifyRuleModeEnum) {
      this.antiCheatConfig.antiCheatConfig.studyConfig.reVerifyRuleMode = val
      // 添加这行，手动触发表单验证
      this.$nextTick(() => {
        val === ReVerifyRuleModeEnum.UNTIL_SUCCESS &&
          (this.antiCheatConfig.antiCheatConfig.studyConfig.reVerifyNums = undefined)
        this.learningSupervisionForm.validateField(['reVerifyRuleMode', 'reVerifyNums'])
      })
    }

    /**
     * 结果处理方式选择
     * @param val
     */
    resultVerifyMode(val: FailureHandleStrategy) {
      this.antiCheatConfig.antiCheatConfig.studyConfig.resultVerifyMode = val
      this.learningSupervisionForm.validateField(['resultVerifyMode'])
    }

    inputReVerifyNums(newValue: number | string) {
      let val = isNaN(Number(newValue)) ? 0 : Number(newValue)
      if (newValue === '') {
        val = undefined
      }
      this.antiCheatConfig.antiCheatConfig.studyConfig.reVerifyNums = val
    }
  }
</script>
