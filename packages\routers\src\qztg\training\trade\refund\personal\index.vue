<route-meta>
{
"isMenu": true,
"title": "个人报名退款订单",
"sort": 1,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>

<script lang="ts">
  import RefundPersonalIndex from '@hbfe/jxjy-admin-trade/src/diff/qztg/refund/personal/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    query: [WXGLY, FXS, GYS],
    queryfx: [NZFXS, NZFXSJCB],
    queryzt: [ZTGLY],
    export: [WXGLY, FXS, GYS],
    exportfx: [NZFXS, NZFXSJCB],
    exportzt: [ZTGLY],
    refundDetail: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    refundApproval: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB],
    approve: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB],
    retryRecycleRefund: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB],
    confirmRefund: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB],
    continueRefund: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB],
    batchAgreeRefund: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB],
    batchConfirmRefund: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB],
    editInvoicePopup: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB]
  })
  export default class extends RefundPersonalIndex {}
</script>
