<template>
  <el-main>
    <div class="f-p15 m-table-auto">
      <div class="f-mb15">
        <el-button type="primary" icon="el-icon-plus">添加供应商</el-button>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--表格-->
        <el-table stripe :data="tableData" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="供应商名称" min-width="180">
            <template>
              供应商名称供应商名称
            </template>
          </el-table-column>
          <el-table-column label="类型" min-width="120" align="center">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-tag type="success">个人</el-tag>
              </div>
              <div v-else>
                <el-tag type="primary">企业</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="统一社会信用代码/身份证" min-width="210">
            <template>4125685*******2356</template>
          </el-table-column>
          <el-table-column label="合作周期" min-width="210">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                长期有效
              </div>
              <div v-else>2020-11-11<i class="f-plr5">至</i>2024-07-15</div>
            </template>
          </el-table-column>
          <el-table-column label="产品数量" min-width="120" align="right">
            <template>15</template>
          </el-table-column>
          <el-table-column label="状态" min-width="120" fixed="right">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-badge is-dot type="success" class="badge-status">合作中</el-badge>
              </div>
              <div v-else-if="scope.$index === 1">
                <el-badge is-dot type="primary" class="badge-status">未开始</el-badge>
              </div>
              <div v-else-if="scope.$index === 2">
                <el-badge is-dot type="danger" class="badge-status">中止合作</el-badge>
              </div>
              <div v-else-if="scope.$index === 3">
                <el-badge is-dot type="info" class="badge-status">已结束</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="warning" class="badge-status">即将到期</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">详情</el-button>
              <el-button type="text" size="mini">修改</el-button>
              <el-button type="text" size="mini">中止合作</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        >
        </el-pagination>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          delivery1: true,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
