<route-meta>
{ "title": "课件分类下拉搜索选择器" }
</route-meta>
<template>
  <el-cascader
    class="biz-common"
    v-model="categoryIdList"
    :clearable="clearable"
    :filterable="filterable"
    @clear="categoryIdList = undefined"
    @focus="handleFocus"
    :props="props"
    :options="options"
    :placeholder="placeholder"
    @change="selectedChange"
    :disabled="disabled"
    :filter-method="dataFilter"
    :before-filter="beforeFilter"
  />
</template>

<script lang="ts">
  import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import CoursewareTreeListDetail from '@api/service/management/resource/courseware/query/vo/CoursewareTreeListDetail'
  import { rootCoursewareCategory } from '@api/service/common/config/CommonConfig'
  import { cloneDeep } from 'lodash'
  import { CascaderOptions } from '@hbfe/jxjy-admin-components/src/models/CascaderOptions'
  import CoursewareCategoryDetail from '@api/service/management/resource/courseware-category/query/vo/CoursewareCategoryDetail'

  @Component({
    beforeRouteEnter: (to, from, next) => {
      next((vm: User) => {
        vm.name = ''
      })
    }
  })
  export default class User extends Vue {
    name = ''
    @Prop({
      type: Boolean,
      default: false
    })
    multiple: boolean

    @Prop({
      type: String,
      default: '请选择课件分类'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      type: Boolean,
      default: true
    })
    checkStrictly: boolean

    @Prop({
      type: [Array, String],
      default: () => new Array<string>()
    })
    value: Array<string>

    @Prop({
      type: Boolean,
      default: true
    })
    showRootNode: boolean

    @Prop({
      type: Boolean,
      default: false
    })
    disabled: boolean

    @Prop({
      type: Boolean,
      default: false
    })
    isArray: boolean

    @Prop({
      type: Boolean,
      default: false
    })
    filterable: boolean

    categoryIdList: Array<string> = new Array<string>()
    props = {
      multiple: false,
      label: 'name',
      value: 'id',
      lazy: true,
      leaf: 'leaf',
      checkStrictly: true,
      lazyLoad: async (node: { data: CoursewareTreeListDetail }, resolve: any) => {
        if (node?.data?.id) {
          if (node?.data?.children?.length) {
            resolve(null)
          } else {
            const result = await this.query(node?.data?.id)
            resolve(result)
          }
        }
      }
    }

    @Watch('categoryIdList')
    @Emit('input')
    changeValue() {
      return this.categoryIdList
    }

    @Watch('value', { immediate: true })
    async valueChange() {
      // 修复回显问题
      if (!this.options.length) {
        await this.handleFocus()
      }
      this.categoryIdList = this.value
    }

    selectedChange() {
      if (this.categoryIdList && this.categoryIdList.length) {
        return this.categoryIdList
      }
      // if (this.categoryIdList?.length) {
      //   if (this.isArray) {
      //     this.$emit('update:idList', [this.categoryIdList[this.categoryIdList.length - 1]])
      //   } else {
      //     this.$emit('update:idList', this.categoryIdList[this.categoryIdList.length - 1])
      //   }
      // } else {
      //   this.$emit('update:idList', null)
      // }
    }

    options: any = []
    curName = ''

    async loadDataList() {
      if (this.showRootNode) {
        this.options = [rootCoursewareCategory]
      } else {
        this.options = await this.query('-1')
      }
    }

    async query(id?: string) {
      return await ResourceModule.coursewareCategoryFactory.query.queryChildrenById(id)
    }

    /**
     * 查询全部课件类别
     */
    async queryAll() {
      return await ResourceModule.coursewareCategoryFactory.query.queryAllChildren()
    }

    async beforeFilter(name: string) {
      if (name !== this.curName) {
        this.curName = name
        const filterList = await ResourceModule.coursewareCategoryFactory.query.queryListByName(name)
        console.log('过滤的列表：', filterList)
      }
    }

    dataFilter(node: any, name: string) {
      return node.text.indexOf(name) !== -1
    }

    /**
     * 元素获得焦点时响应事件
     */
    async handleFocus() {
      this.setProps()
      if (this.filterable) {
        this.props.lazy = false
        this.props.label = 'label'
        this.props.value = 'value'
        if (this.showRootNode) {
          this.options = [
            {
              value: '-1',
              label: '课件分类',
              children: []
            }
          ]
          await this.getAllCategoryData(this.options[0].children)
          this.reShade(this.options)
        } else {
          this.options = []
          await this.getAllCategoryData(this.options)
          this.reShade(this.options)
        }
        console.log(this.options, 'this.options')
      } else {
        await this.loadDataList()
        if (this.value) await this.echo()
        this.categoryIdList = this.value
      }
    }

    //获取所有数据
    async getAllCategoryData(option: Array<CascaderOptions>) {
      const tree = await this.queryAll()

      tree
        .filter((res) => res.parentId === '-1')
        .map((item) => {
          const category = new CascaderOptions()
          category.label = item.name
          category.value = item.id
          category.children = []
          option.push(category)
          // return this.getAllCategoryData(item.id, option[index].children})
        })
      this.assembleCategory(tree, option)
    }

    /**
     * 组装树
     * @param tree
     * @param option
     */
    assembleCategory(tree: CoursewareCategoryDetail[], option: Array<CascaderOptions>) {
      // * 递归处理组装数据
      option.forEach((item) => {
        const treeTemp = tree
          .filter((res) => res.parentId === item.value)
          .map((child) => {
            const category = new CascaderOptions()
            category.label = child.name
            category.value = child.id
            category.parentId = child.parentId
            category.children = []
            return category
          })
        if (!treeTemp.length) {
          item.children = []
          return
        }
        item.children = [...treeTemp]
        this.assembleCategory(tree, item.children)
      })
    }

    setProps() {
      this.props.multiple = this.multiple
      this.props.checkStrictly = this.checkStrictly
    }

    async echo() {
      // await ResourceModule.coursewareCategoryFactory.query.queryReverserById(id)
      const list: string[] = cloneDeep(this.value)
      // 递归查询 拼装数据
      await this.formatTree(this.options, list.shift(), list)
      console.log(this.options, this.value)
    }

    /**
     * @description: 格式化这颗课件树
     * @param {*} optionList 当前层级的可选项
     * @param {*} searchNode 需要查找的节点
     * @param {*} nodes 当前节点集合
     * @return {*}
     */
    async formatTree(optionList: CoursewareTreeListDetail[], searchNode: string, nodes: string[]) {
      if (!searchNode) {
        return
      }
      const currentOptions = optionList.filter((el) => {
        console.log(el.id, searchNode)
        return el.id === searchNode
      })
      const currentOption: any = currentOptions.length ? currentOptions[0] : undefined
      if (!currentOption) return
      const subNodes = await this.query(currentOption?.id)
      currentOption.hasChildren = true
      currentOption.children = subNodes
      //currentOption.leaf = !nodes.length
      if (this.checkStrictly) {
        currentOption.leaf = false
      } else {
        currentOption.leaf = !nodes.length
      }
      await this.formatTree(subNodes, nodes.shift(), nodes)
    }

    /**
     * 树形结构选项重塑
     * @description 没有子级的节点，children统一改为null
     */
    reShade(options: Array<CascaderOptions>) {
      if (options && options.length) {
        options.forEach((item) => {
          if (!item.children || !item.children.length) {
            item.children = null
          } else {
            this.reShade(item.children)
          }
        })
      }
    }
  }
</script>
