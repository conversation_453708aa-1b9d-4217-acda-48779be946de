import { TrainingChannelOfflineCollectiveSignUpSettingResponse } from '@api/platform-gateway/platform-training-channel-back-gateway'

export default class OfflineCollectiveInfo {
  id: string
  /**
   * 线下集体报名入口是否
   */
  offlineCollectiveEntry = false
  /**
   * 线下集体报名入口图片
   */
  offlineCollectiveEntryPicture = ''
  /**
   * 线下集体报名名称
   */
  offlineCollectiveEntryName = ''
  /**
   * 线下集体报名模板
   */
  offlineCollectiveEntryTemplate: { name?: string; url?: string } = null
  /**
   * 底部文本说明
   */
  bottomText = ''
  /**
   * 报名步骤
   */
  steps: { no: number; title: string; content: string }[] = []
  /**
   * 班级报名链接
   */
  signUpClassUrl = `${window.location.origin}/collective-registry`
  static from(dto: TrainingChannelOfflineCollectiveSignUpSettingResponse) {
    const vo = new OfflineCollectiveInfo()
    vo.id = dto.id
    vo.offlineCollectiveEntry = dto.openEntrySwitch
    vo.offlineCollectiveEntryPicture = dto.entryPictureAttachments[0]?.url || ''
    vo.offlineCollectiveEntryName = dto.name
    vo.offlineCollectiveEntryTemplate = dto.templateAttachment
    vo.bottomText = dto.bottomDescription
    vo.steps = dto.signUpSteps.map(step => {
      const item = {
        no: step.index,
        title: step.title,
        content: step.content,
        id: step.contentId
      }
      return item
    })
    return vo
  }
}
