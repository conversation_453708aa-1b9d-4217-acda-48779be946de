import MSCommodity from '@api/ms-gateway/ms-commodity-v1'
import MsLearningScheme from '@api/ms-gateway/ms-learningscheme-v1'
import MsLearningQueryFrontGatewayCourseLearningForestage, {
  LearningRegisterRequest,
  StudentSchemeLearningRequest,
  SchemeRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import MsTradeQueryFrontGatewayCourseLearningForestage, {
  CommoditySkuForestageResponse,
  CommoditySkuPropertyResponse,
  OrderRequest,
  PortalCommoditySkuPropertyResponse,
  UserPossessionInfoResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import SkuPropertyResponseVo from '@api/service/customer/train-class/query/vo/SkuPropertyResponseVo'
import { Response } from '@hbfe/common'
import { SourceEnum } from '@api/service/customer/train-class/query/Enum/SourceEnum'
import { SubjectType } from '@api/service/customer/thematic-config/enum/SubjectType'
import ThirdPartyItem from '@api/service/diff/customer/xmlg/train-class/model/ThirdPartyItem'
import { default as CommonTrainClassCommodityVo } from '@api/service/customer/train-class/query/vo/TrainClassCommodityVo'
import xmlgGateway, { DropClassExtendedInfoResponse } from '@api/diff-gateway/platform-jxjypxtypt-xmlg-school'
import HywSchemeRefundStatus, {
  HywSchemeRefundEnum
} from '@api/service/diff/customer/fjzj/train-class/enums/HywSchemeRefundStatus'
import xmlgTradeGateway from '@api/diff-gateway/platform-jxjypxtypt-xmlg-trade'
import { ResponseStatus } from '@hbfe/common'
import { CourseType } from '@api/service/diff/customer/xmlg/train-class/enums/CourseType'
import SchemeUtil, { CheckTrainClassParam } from '@api/service/customer/train-class/offlinePart/util/SchemeUtil'

/**
 * 培训班列表Vo
 */
class TrainClassCommodityVo extends CommonTrainClassCommodityVo {
  /**
   * 门户商品 - 商品id
   */
  portalCommoditySkuId: string
  /**
   * 门户商品 - 来源类型
   1-网校商品 2-专题商品
   */
  portalCommoditySkuSourceType: SourceEnum
  /**
   * 门户商品 - 来源id(服务商id,专题id)
   */
  portalCommoditySkuSourceId: string
  /**
   * 专题类型
   */
  specialType: Array<SubjectType> = Array<SubjectType>()
  /**
   * 专题行业
   */
  specialIndustry = ''
  /**
   * 专题单位名称
   */
  specialUnit = ''
  /**
   * 商品属性信息
   */
  declare skuProperty: CommoditySkuPropertyResponse & PortalCommoditySkuPropertyResponse
  // region properties
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 方案类型
   */
  schemeType: string
  /**
   *班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuValueNameProperty = new SkuPropertyResponseVo()
  /**
   * 培训方案须知
   */
  notice = ''

  /**
   * 是否弹窗展示须知
   */
  showNoticeDialog = false
  /**
   * 第三方平台信息
   */
  thirdPartyInfo: ThirdPartyItem = null
  /**
   * 校验口退款状态
   */
  refundStatus: HywSchemeRefundStatus = new HywSchemeRefundStatus()
  // endregion
  // region methods

  /**
   * 购买前校验
   */
  async checkTrainClass(channelType = 1, terminalCode = 'Web'): Promise<Response<string>> {
    const res = await MSCommodity.validateCommodity({
      commoditySkuId: this.commoditySkuId,
      channelType: channelType,
      terminalCode: terminalCode
    })
    let orderNo = ''
    if (res.status.isSuccess()) {
      if (res.data.code == '200') {
        const schemeRes = await MsLearningScheme.reservingSchemeValidate({
          schemeId: this.schemeId
        })
        if (res.status.isSuccess()) {
          res.status.code = parseInt(schemeRes.data.code)
          res.status.message = schemeRes.data.message
        } else {
          res.status = schemeRes.status
        }
        if (res.status.code == 50001) {
          try {
            const subOrderNo = schemeRes.data.duplicateReservingInfos[0].sourceId
            const request = new OrderRequest()
            request.subOrderNoList = [subOrderNo]
            const orderListRes = await MsTradeQueryFrontGatewayCourseLearningForestage.pageOrderInMyself({
              page: {
                pageNo: 1,
                pageSize: 1
              },
              request: request
            })
            if (orderListRes.status.isSuccess()) {
              const order = orderListRes.data.currentPageData[0]
              if ([0, 1].includes(order.basicData.orderPaymentStatus)) {
                if (order.basicData.channelType == 2) {
                  res.status.code = 52009
                  orderNo = order.orderNo
                } else {
                  res.status.code = 52001
                  orderNo = order.orderNo
                }
              }
            }
          } catch (e) {
            console.log('获取订单信息失败，', e)
          }
        }
      } else {
        res.status.code = parseInt(res.data.code)
        res.status.message = res.data.message
      }
    }
    const newRes = new Response<string>()
    Object.assign(newRes, res)
    if (newRes.status.code == 52001) {
      newRes.data = orderNo
    }
    return newRes
  }
  /*
   *    获取参训资格id，仅在已报名状态才能获取
   * */
  async getQualificationId() {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.sourceType = this.possessionInfo.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
    request.learningRegister.sourceId = this.possessionInfo.sourceId
    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      this.studentNo = res.data.currentPageData[0].studentNo
      return res.data.currentPageData[0].qualificationId
    }
    return ''
  }
  /**
   * 获取订单号，仅在已报名状态才能获取
   */
  async getOrderNo(possessionInfo?: UserPossessionInfoResponse) {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    if (possessionInfo?.sourceId) {
      request.learningRegister.sourceType = possessionInfo?.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
      request.learningRegister.sourceId = possessionInfo?.sourceId
    } else {
      request.scheme = new SchemeRequest()
      request.scheme.schemeId = this.schemeId || ''
    }

    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      return res.data.currentPageData[0].learningRegister.orderNo
    }
    return ''
  }
  // endregion

  /**
   * 获取退款状态，仅在已报名状态才能获取
   */
  async getCourseRefund(possessionInfo?: UserPossessionInfoResponse) {
    const request = new StudentSchemeLearningRequest()
    const response = new DropClassExtendedInfoResponse()
    request.learningRegister = new LearningRegisterRequest()
    if (possessionInfo.sourceId) {
      request.learningRegister.sourceType = possessionInfo.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
      request.learningRegister.sourceId = possessionInfo.sourceId
    } else {
      request.scheme = new SchemeRequest()
      request.scheme.schemeId = this.schemeId || ''
    }
    const res = await xmlgGateway.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      response.publicSign = res.data.currentPageData[0].dropClassExtendedInfoResponse.publicSign
      response.professionalSign = res.data.currentPageData[0].dropClassExtendedInfoResponse.professionalSign
      return response
    }
    return response
  }

  async checkTrainClassNew(param: { channelType: 1; terminalCode: 'Web'; issueId?: string }) {
    const request = new CheckTrainClassParam()
    request.commoditySkuId = this.commoditySkuId
    request.schemeId = this.schemeId
    request.channelType = param.channelType
    request.terminalCode = param.terminalCode
    if (param.issueId) {
      request.issueId = param.issueId
    }
    return await SchemeUtil.checkTrainClass(request)
  }
  /**
   *前往课程方案退款状态
   * @param orderNo 订单号
   * @param type 退课类型
   */
  async checkPublicRefundStatus(orderNo: string, type: CourseType): Promise<Response<HywSchemeRefundStatus>> {
    const response = new Response<HywSchemeRefundStatus>()
    const res = await xmlgTradeGateway.queryHymSchemeRefundInfo(orderNo)
    if (res.status.isSuccess() && res.data) {
      const publicCourseRefundStatus = res.data.publicCourse
      const professionalCourseRefundStatus = res.data.professionalCourse
      if (publicCourseRefundStatus.refund && publicCourseRefundStatus.completed && type === CourseType.public_course) {
        //公需课退款完成
        this.refundStatus.current = HywSchemeRefundEnum.public_refunded
        response.status = new ResponseStatus(Number(res.data.code), res.data.message)
        response.data = this.refundStatus
        return response
      }
      if (
        professionalCourseRefundStatus.refund &&
        professionalCourseRefundStatus.completed &&
        type === CourseType.professional_course
      ) {
        //专业课退款完成
        this.refundStatus.current = HywSchemeRefundEnum.professional_refunded
        response.status = new ResponseStatus(Number(res.data.code), res.data.message)
        response.data = this.refundStatus
        return response
      }
      if (
        (professionalCourseRefundStatus.refund && !professionalCourseRefundStatus.completed) ||
        (publicCourseRefundStatus.refund && !publicCourseRefundStatus.completed)
      ) {
        //有课程处于退款状态中
        this.refundStatus.current = HywSchemeRefundEnum.scheme_refunding
      }
      response.status = new ResponseStatus(Number(res.data.code), res.data.message)
      response.data = this.refundStatus
    }
    return response
  }
}
export default TrainClassCommodityVo
