import CourseCategoryListDetail from './vo/CourseCategoryListDetail'
import CourseCategoryDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryDetail'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CourseCategoryRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import AbstractQueryCategory from '@api/service/management/resource/AbstractQueryCategory'
import { rootCourseCategory } from '@api/service/common/config/CommonConfig'

class QueryCourseCategory extends AbstractQueryCategory<CourseCategoryDetail, CourseCategoryListDetail> {
  courseCategoryTreeList: Array<CourseCategoryListDetail> = new Array<CourseCategoryListDetail>()

  /**
   * 查询夫分类下面某个分类名称是否存在
   * @param parentId
   * @param name
   */
  async queryByName(parentId: string, name: string) {
    const request = new CourseCategoryRequest()
    request.parentId = parentId
    request.name = name
    return this.query(request)
  }

  /**
   * 获取分类的子类列表
   * @param id
   */
  async queryChildrenById(id: string): Promise<Array<CourseCategoryListDetail>> {
    return this.queryList(id)
  }

  /**
   * 获取分类的所有子类列表
   */
  async queryChildrenAll() {
    const params = new CourseCategoryRequest()
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listCourseCategoryInServicer(params)
    return result.data.map(CourseCategoryListDetail.from)
  }

  /**
   * 内部封装的查询
   * @param params
   * @private
   */
  private async query(params: CourseCategoryRequest) {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listCourseCategoryInServicer(params)
    return result.data.map(CourseCategoryListDetail.from)
  }

  /**
   * 根据课程 id 列表查询课程信息
   * @param idList
   */
  async queryListByIdList(idList: Array<string>) {
    const request = new CourseCategoryRequest()
    request.categoryIdList = idList
    return this.query(request)
  }

  /**
   * 根据课程 id 查询课程详情
   * @param id
   */
  async queryById(id: string): Promise<CourseCategoryDetail> {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.getCourseCategoryInServicer(id)
    const detail = CourseCategoryDetail.from(result.data)
    await detail.queryParent()
    return detail
  }

  /**
   * 查询课程分类列表，当前课程分类不会有分页的情况。
   * @param id
   * @param name 提供名称查询
   */
  async queryList(id?: string, name?: string) {
    const request = new CourseCategoryRequest()
    request.parentId = id
    request.name = name
    return this.query(request)
  }

  /**
   * 根据提供的层级结构的 id，查询分类
   * @param idList
   */
  async queryCoursewareCategoryListByLevelConstruct(idList: Array<string>) {
    const newIdList = [...idList]
    const reverserSearch = async (node: { children: Array<CourseCategoryListDetail> }) => {
      const id = newIdList[0]
      const result = await this.queryList(id)
      if (!result.length) {
        delete node.children
      } else {
        node.children.push(...result)
      }
      newIdList.splice(0, 1)
      const nextId = newIdList[0]
      const item = result.find((detail: CourseCategoryListDetail) => {
        return detail.id === nextId
      })
      if (newIdList.length && item) {
        item.children = new Array<CourseCategoryListDetail>()
        await reverserSearch(item)
      }
    }
    const result = {
      children: new Array<CourseCategoryListDetail>()
    }
    await reverserSearch(result)
    return result.children
  }

  /**
   * 提供最后一个分类的 id， 反向查询父级分类
   * @param lastLevelCategory
   */
  async queryReverserById(lastLevelCategory: string): Promise<Array<CourseCategoryListDetail>> {
    const list = new Array<CourseCategoryListDetail>()
    const traversSearch = async (id: string) => {
      const {
        data: detail
      } = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.getCourseCategoryInServicer(id)
      if (detail.parentId && detail.parentId !== rootCourseCategory.id) {
        await traversSearch(detail.parentId)
      }
      list.push(CourseCategoryListDetail.from(detail))
    }
    await traversSearch(lastLevelCategory)
    return list
  }
}

export default QueryCourseCategory
