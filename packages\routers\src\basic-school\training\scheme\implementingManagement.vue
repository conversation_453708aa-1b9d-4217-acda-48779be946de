<route-params content="/:schemeId"></route-params>
<route-meta>
{
"isMenu": true,
"title": "实施管理",
"sort": 2,
"icon": "icon_guanli",
"hideMenu": true
}
</route-meta>
<script lang="ts">
  import ImplementingManagement from '@hbfe/jxjy-admin-scheme/src/implementingManagement/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    preTrainingImplementation: [WXGLY],
    trainingProcess: [WXGLY],
    trainingResults: [WXGLY],
    trainingResultList: [WXGLY],
    trainingResultListQuery: [WXGLY],
    viewLearningStatistic: [WXGLY],
    trainingResultMixListQuery: [WXGLY],
    viewOnlineLearningStatistic: [WXGLY]
  })
  export default class extends ImplementingManagement {}
</script>
