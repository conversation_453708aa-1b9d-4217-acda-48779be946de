import MsExamQuestionGateway from '@api/ms-gateway/ms-examquestion-v1'
import { ResponseStatus } from '@hbfe/common'
/*
 * 试题基础操作
 */
class QuestionAction {
  // 试题id
  private questionId = ''

  constructor(id: string) {
    this.questionId = id
  }

  /**
   * @description: 禁用试题
   * @param {string} 试题 id
   */
  async doDisabled(): Promise<ResponseStatus> {
    const { status } = await MsExamQuestionGateway.disableQuestion(this.questionId)
    return status
  }

  /**
   * @description: 启用试题
   * @param {string} 试题 id
   */
  async doEnable(): Promise<ResponseStatus> {
    const { status } = await MsExamQuestionGateway.enableQuestion(this.questionId)
    return status
  }

  /**
   * @description: 删除试题
   * @param {string} 试题 id
   */
  async doDelete(): Promise<ResponseStatus> {
    const { status } = await MsExamQuestionGateway.removeQuestion(this.questionId)
    return status
  }
}
export default QuestionAction
