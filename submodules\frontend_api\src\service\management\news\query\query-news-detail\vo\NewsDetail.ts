import { NewsDetailResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { TrainingChannelDetailResponse } from '@api/platform-gateway/platform-training-channel-back-gateway'
import { SpecialSubjectInfo } from '@api/ms-gateway/ms-news-v1'

export default class NewsDetailVo {
  /**
   * 资讯ID
   */
  newId: string
  /**
   * 发布时间
   */
  time: string
  /**
   * 资讯标题
   */
  title: string
  /**
   * 是否弹窗
   */
  isPopup: boolean
  /**
   * 是否置顶
   */
  top: boolean
  /**
   * 资讯类别
   */
  categoryType: Array<string>
  /**
   * 资讯来源
   */
  source?: string
  /**
   * 资讯摘要
   */
  abstract?: string
  /**
   * 封面图片
   */
  bgImage?: string
  /**
   * 资讯内容
   */
  content: string
  /**
   * 弹窗起始时间
   */
  popupBeginTime?: string
  /**
   * 弹窗截止时间
   */
  popupEndTime?: string
  /**
   * 发布地区编码，null不更新
   */
  areaCodeList?: Array<string>

  /**
   * 专题信息
   */
  specialList: Array<SpecialSubjectInfo> = new Array<SpecialSubjectInfo>()

  /**
   * 是否校验时间重复
   */
  verifyPopUps = true

  static from(newsDetailResponse: NewsDetailResponse, trainingChannelDetail?: TrainingChannelDetailResponse) {
    const {
      publishTime,
      title,
      isPopUps,
      isTop,
      necId,
      source,
      summary,
      coverPath,
      content,
      newId,
      popupBeginTime,
      popupEndTime
    } = newsDetailResponse
    const newsDetailVo = new NewsDetailVo()
    newsDetailVo.time = publishTime
    newsDetailVo.title = title
    newsDetailVo.isPopup = isPopUps
    newsDetailVo.top = isTop
    newsDetailVo.categoryType = [necId]
    newsDetailVo.source = source
    newsDetailVo.abstract = summary
    newsDetailVo.bgImage = coverPath
    newsDetailVo.content = content
    newsDetailVo.newId = newId
    newsDetailVo.popupBeginTime = popupBeginTime
    newsDetailVo.popupEndTime = popupEndTime
    newsDetailVo.areaCodeList = newsDetailResponse.areaCodePath?.slice(1)?.split('/')
    if (trainingChannelDetail) {
      const special = new SpecialSubjectInfo()
      special.specialSubjectName = trainingChannelDetail.name
      special.specialSubjectId = trainingChannelDetail.id
      newsDetailVo.specialList.push(special)
    }
    return newsDetailVo
  }
}
