import findUserCoursePage from './queries/findUserCoursePage.graphql'
import getAssessKey from './queries/getAssessKey.graphql'
import getLastLearningMedia from './queries/getLastLearningMedia.graphql'
import getLearningByToken from './queries/getLearningByToken.graphql'
import getLearningScheduleByToken from './queries/getLearningScheduleByToken.graphql'
import getNeedAnswerPopQuestions from './queries/getNeedAnswerPopQuestions.graphql'
import getPopQuestion from './queries/getPopQuestion.graphql'
import getUserCourse from './queries/getUserCourse.graphql'
import applyCoursewarePlayingResource from './mutates/applyCoursewarePlayingResource.graphql'
import applyListenCourseToken from './mutates/applyListenCourseToken.graphql'
import applyPlaying from './mutates/applyPlaying.graphql'
import applyPreviewCourseToken from './mutates/applyPreviewCourseToken.graphql'
import applyReloadHwyVideoAntiLeech from './mutates/applyReloadHwyVideoAntiLeech.graphql'
import batchChooseCourse from './mutates/batchChooseCourse.graphql'
import batchRemoveUserCourse from './mutates/batchRemoveUserCourse.graphql'
import removeUserCourse from './mutates/removeUserCourse.graphql'
import submitAnswer from './mutates/submitAnswer.graphql'
import updateCoursewareSchedule from './mutates/updateCoursewareSchedule.graphql'
import userBatchChooseCourse from './mutates/userBatchChooseCourse.graphql'
import userChooseCourse from './mutates/userChooseCourse.graphql'

export {
  findUserCoursePage,
  getAssessKey,
  getLastLearningMedia,
  getLearningByToken,
  getLearningScheduleByToken,
  getNeedAnswerPopQuestions,
  getPopQuestion,
  getUserCourse,
  applyCoursewarePlayingResource,
  applyListenCourseToken,
  applyPlaying,
  applyPreviewCourseToken,
  applyReloadHwyVideoAntiLeech,
  batchChooseCourse,
  batchRemoveUserCourse,
  removeUserCourse,
  submitAnswer,
  updateCoursewareSchedule,
  userBatchChooseCourse,
  userChooseCourse
}
