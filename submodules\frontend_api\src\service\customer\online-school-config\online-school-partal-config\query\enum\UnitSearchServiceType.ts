import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum UnitSearchServiceTypeEnum {
  /**
   * 天眼查
   */
  tySearch = 1,

  /**
   * 企查查
   */
  qccSearch = 2
}

export default class UnitSearchServiceType extends AbstractEnum<UnitSearchServiceTypeEnum> {
  static enum = UnitSearchServiceTypeEnum

  constructor(status?: UnitSearchServiceTypeEnum) {
    super()
    this.current = status
    this.map.set(UnitSearchServiceTypeEnum.tySearch, '天眼查')
    this.map.set(UnitSearchServiceTypeEnum.qccSearch, '企查查')
  }
}
