import exportAlreadyFailExcel from './queries/exportAlreadyFailExcel.graphql'
import findImportFailDataByPage from './queries/findImportFailDataByPage.graphql'
import findImportSuccessDataByPage from './queries/findImportSuccessDataByPage.graphql'
import findOpenLearningPermissionSubTuskByPage from './queries/findOpenLearningPermissionSubTuskByPage.graphql'
import findTaskExecuteByBatchAndServicer from './queries/findTaskExecuteByBatchAndServicer.graphql'
import findTaskExecuteWithServicerResponseByPage from './queries/findTaskExecuteWithServicerResponseByPage.graphql'
import queryImportOpenTemplatePath from './queries/queryImportOpenTemplatePath.graphql'
import supplierImportDataAnalysis from './queries/supplierImportDataAnalysis.graphql'
import supplierImportDataProcess from './queries/supplierImportDataProcess.graphql'
import batchMarkDeleteSignupData from './mutates/batchMarkDeleteSignupData.graphql'
import clearFailureData from './mutates/clearFailureData.graphql'
import openLearningPermission from './mutates/openLearningPermission.graphql'
import supplierImportOpenBasic from './mutates/supplierImportOpenBasic.graphql'
import supplierImportOpenMajor from './mutates/supplierImportOpenMajor.graphql'

export {
  exportAlreadyFailExcel,
  findImportFailDataByPage,
  findImportSuccessDataByPage,
  findOpenLearningPermissionSubTuskByPage,
  findTaskExecuteByBatchAndServicer,
  findTaskExecuteWithServicerResponseByPage,
  queryImportOpenTemplatePath,
  supplierImportDataAnalysis,
  supplierImportDataProcess,
  batchMarkDeleteSignupData,
  clearFailureData,
  openLearningPermission,
  supplierImportOpenBasic,
  supplierImportOpenMajor
}
