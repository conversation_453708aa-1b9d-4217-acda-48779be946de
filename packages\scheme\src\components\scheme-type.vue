<route-meta>
{
"title": "方案类型下拉搜索选择器（单选）"
}
</route-meta>
<template>
  <el-cascader
    v-model="categoryIdList"
    :clearable="clearable"
    filterable
    @clear="categoryIdList = undefined"
    :props="props"
    :options="options"
    :placeholder="placeholder"
  />
</template>

<script lang="ts">
  import { CascaderOptions } from '@hbfe/jxjy-admin-components/src/models/CascaderOptions'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import Context from '@api/service/common/context/Context'
  import SchemeType, { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: true
    })
    checkStrictly: boolean

    @Prop({
      type: String,
      default: '请选择培训方案类型'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      type: Array,
      default: () => new Array<string>()
    })
    value: Array<string>
    categoryIdList: Array<string> = new Array<string>()
    options: Array<CascaderOptions> = new Array<CascaderOptions>()
    props = {
      checkStrictly: true
    }

    @Watch('value')
    valueChange() {
      this.categoryIdList = this.value
    }

    @Emit('input')
    @Watch('categoryIdList')
    selectedChange() {
      return this.categoryIdList
    }

    created() {
      this.setProps()
      this.options = [
        {
          value: '-1',
          label: '培训班',
          children: [
            {
              value: 'chooseCourseLearning',
              label: '选课规则'
            },
            {
              value: 'autonomousCourseLearning',
              label: '自主选课'
            }
          ]
        }
      ]
      // 是否对接第三方平台
      const dockThirdPartyServicerIds = CommonConfigCenter.getFrontendApplication(
        frontendApplication.dockThirdPartyServicerIds
      )
      if (dockThirdPartyServicerIds?.includes(Context.servicerInfo.id)) {
        this.options.push({
          value: SchemeTypeEnum[SchemeTypeEnum.trainingCooperation],
          label: SchemeType.map.get(SchemeTypeEnum.trainingCooperation)
        })
      }
    }

    setProps() {
      this.props.checkStrictly = this.checkStrictly
    }
  }
</script>
