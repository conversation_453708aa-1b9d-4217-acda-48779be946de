import courseCategoryNameExists from './queries/courseCategoryNameExists.graphql'
import courseNameExists from './queries/courseNameExists.graphql'
import coursePoolNameExists from './queries/coursePoolNameExists.graphql'
import courseWareCategoryNameExists from './queries/courseWareCategoryNameExists.graphql'
import courseWareNameExists from './queries/courseWareNameExists.graphql'
import getCourse from './queries/getCourse.graphql'
import getCourseCategory from './queries/getCourseCategory.graphql'
import getCourseInPoolList from './queries/getCourseInPoolList.graphql'
import getCourseInPoolPage from './queries/getCourseInPoolPage.graphql'
import getCoursePage from './queries/getCoursePage.graphql'
import getCoursePoolDTO from './queries/getCoursePoolDTO.graphql'
import getCoursePoolPage from './queries/getCoursePoolPage.graphql'
import getCourseWare from './queries/getCourseWare.graphql'
import getCourseWareCategory from './queries/getCourseWareCategory.graphql'
import getCourseWareCategoryListByParentId from './queries/getCourseWareCategoryListByParentId.graphql'
import getCourseWarePage from './queries/getCourseWarePage.graphql'
import getCourseWareSupplier from './queries/getCourseWareSupplier.graphql'
import getPopQuestion from './queries/getPopQuestion.graphql'
import getSubCourseCategory from './queries/getSubCourseCategory.graphql'
import getTeacher from './queries/getTeacher.graphql'
import getTeacherPage from './queries/getTeacherPage.graphql'
import listAllCourseWarePopQuestions from './queries/listAllCourseWarePopQuestions.graphql'
import usedAsCompulsoryPackage from './queries/usedAsCompulsoryPackage.graphql'
import addCourseIntoPool from './mutates/addCourseIntoPool.graphql'
import addCourseOutline from './mutates/addCourseOutline.graphql'
import copyCoursePool from './mutates/copyCoursePool.graphql'
import createCourse from './mutates/createCourse.graphql'
import createCourseCategory from './mutates/createCourseCategory.graphql'
import createCoursePool from './mutates/createCoursePool.graphql'
import createCourseWare from './mutates/createCourseWare.graphql'
import createCourseWareCategory from './mutates/createCourseWareCategory.graphql'
import createPopQuestion from './mutates/createPopQuestion.graphql'
import createTeacher from './mutates/createTeacher.graphql'
import deleteCourse from './mutates/deleteCourse.graphql'
import deleteCourseCategory from './mutates/deleteCourseCategory.graphql'
import deleteCourseIntoPool from './mutates/deleteCourseIntoPool.graphql'
import deleteCourseOutline from './mutates/deleteCourseOutline.graphql'
import deleteCoursePool from './mutates/deleteCoursePool.graphql'
import deleteCourseWare from './mutates/deleteCourseWare.graphql'
import deleteCourseWareCategory from './mutates/deleteCourseWareCategory.graphql'
import deleteTeacher from './mutates/deleteTeacher.graphql'
import disableCourse from './mutates/disableCourse.graphql'
import disableCourseWare from './mutates/disableCourseWare.graphql'
import enableCourse from './mutates/enableCourse.graphql'
import enableCourseWare from './mutates/enableCourseWare.graphql'
import exchangeCourseOutlineSort from './mutates/exchangeCourseOutlineSort.graphql'
import moveCourseInPool from './mutates/moveCourseInPool.graphql'
import removePopQuestion from './mutates/removePopQuestion.graphql'
import updateCourse from './mutates/updateCourse.graphql'
import updateCourseCategory from './mutates/updateCourseCategory.graphql'
import updateCourseIntoPool from './mutates/updateCourseIntoPool.graphql'
import updateCourseOutline from './mutates/updateCourseOutline.graphql'
import updateCoursePool from './mutates/updateCoursePool.graphql'
import updateCourseWare from './mutates/updateCourseWare.graphql'
import updateCourseWareCategory from './mutates/updateCourseWareCategory.graphql'
import updatePopQuestion from './mutates/updatePopQuestion.graphql'
import updateTeacher from './mutates/updateTeacher.graphql'

export {
  courseCategoryNameExists,
  courseNameExists,
  coursePoolNameExists,
  courseWareCategoryNameExists,
  courseWareNameExists,
  getCourse,
  getCourseCategory,
  getCourseInPoolList,
  getCourseInPoolPage,
  getCoursePage,
  getCoursePoolDTO,
  getCoursePoolPage,
  getCourseWare,
  getCourseWareCategory,
  getCourseWareCategoryListByParentId,
  getCourseWarePage,
  getCourseWareSupplier,
  getPopQuestion,
  getSubCourseCategory,
  getTeacher,
  getTeacherPage,
  listAllCourseWarePopQuestions,
  usedAsCompulsoryPackage,
  addCourseIntoPool,
  addCourseOutline,
  copyCoursePool,
  createCourse,
  createCourseCategory,
  createCoursePool,
  createCourseWare,
  createCourseWareCategory,
  createPopQuestion,
  createTeacher,
  deleteCourse,
  deleteCourseCategory,
  deleteCourseIntoPool,
  deleteCourseOutline,
  deleteCoursePool,
  deleteCourseWare,
  deleteCourseWareCategory,
  deleteTeacher,
  disableCourse,
  disableCourseWare,
  enableCourse,
  enableCourseWare,
  exchangeCourseOutlineSort,
  moveCourseInPool,
  removePopQuestion,
  updateCourse,
  updateCourseCategory,
  updateCourseIntoPool,
  updateCourseOutline,
  updateCoursePool,
  updateCourseWare,
  updateCourseWareCategory,
  updatePopQuestion,
  updateTeacher
}
