import TypeEnum from '@api/service/management/online-school-config/column/query/enum/TypeEnum'
import { MenuInfo } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

class Column {
  constructor(id?: string, code?: string, name?: string, type?: TypeEnum) {
    this.id = id
    this.name = name
    this.code = code
    this.type = type
  }

  id: string
  name = ''
  type: number
  code: string
  enable: boolean
  parentId: string
  children: Array<Column> = new Array<Column>()
  sourceType: number
  referenceId: string
  sort: number
  link = ''

  static from(response: MenuInfo) {
    const detail = new Column()
    detail.id = response.id
    detail.name = response.name
    detail.type = response.menuType
    detail.parentId = response.parentId || '-1'
    detail.link = response.link
    detail.code = response.code
    detail.sort = response.sort
    detail.sourceType = response.sourceType
    detail.referenceId = response.referenceId
    return detail
  }

  get isBuildIn() {
    return this.sourceType === 1
  }

  get hasChildren() {
    return this.children.length
  }

  get isInformation() {
    return this.type === TypeEnum.information
  }
}

export default Column
