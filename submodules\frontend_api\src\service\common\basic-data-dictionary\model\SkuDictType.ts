import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

export default class SkuDictType extends TrainingPropertyResponse {
  /**
   * 属性编号
   */
  propertyId = ''
  /**
   * 属性名称
   */
  name = ''
  /**
   * 序号
   */
  sort: number = null
  /**
   * code值
   */
  code: number = null
  /**
   * 展示名称
   */
  showName = ''
  /**
   * 如果是科目类型下的属性，则该值为null
   */
  parentId = ''
}
