import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
import CertificateVo from '@api/service/management/train-class/mutation/vo/CertificateVo'
import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
import LearningResult from '@api/service/common/scheme/model/schemeDto/common/learning-results/LearningResult'
import MyTrainClassDetailClassVo from '@api/service/management/train-class/query/vo/MyTrainClassDetailClassVo'

/**
 * @description 查询培训方案
 */
class QueryTrainClass {
  /**
   * 详情-解析方案JSON配置
   * @param jsonStr 方案json字符串
   * @param trainClassDetail 培训方案详情
   */
  parseTrainClassJsonConfigInDetail(jsonStr: string, trainClassDetail: TrainClassDetailClassVo) {
    // 获取考核项配置
    const jsonConfig = JSON.parse(jsonStr) as Scheme
    const { schemeAssessItem, issueAssessItem } = Scheme.parseTrainClassAssess(jsonConfig)
    trainClassDetail.trainingType = jsonConfig.trainingType
    if (schemeAssessItem) {
      //获取培训成果中学分
      const creditLearningResult = schemeAssessItem.learningResults.find((learnResult: any) => learnResult.type == 1)
      trainClassDetail.trainClassBaseInfo.period = creditLearningResult.grade || 0
      trainClassDetail.trainClassBaseInfo.assessSettingId = schemeAssessItem.id
      trainClassDetail.trainClassBaseInfo.assessSettingName = schemeAssessItem.name
      trainClassDetail.trainClassBaseInfo.creditId = creditLearningResult.id
      //配置培训模板
      const templateResult = schemeAssessItem.learningResults.find((learnResult: any) => learnResult.type == 2)
      if (templateResult) {
        const certificate = new CertificateVo()
        certificate.id = templateResult.id
        certificate.type = templateResult.type
        certificate.provideCert = templateResult.provideCert
        certificate.openPrintTemplate = templateResult.openPrintTemplate
        certificate.certificateTemplateId = templateResult.certificateTemplateId
        trainClassDetail.certificate = certificate
        trainClassDetail.trainClassBaseInfo.hasLearningResult = true
        trainClassDetail.trainClassBaseInfo.learningResultId = templateResult.certificateTemplateId
        trainClassDetail.trainClassBaseInfo.learningResultAchievementsId = templateResult.id
        trainClassDetail.trainClassBaseInfo.openPrintTemplate = templateResult.openPrintTemplate
      }
    }
    if (issueAssessItem) {
      trainClassDetail.learningTypeModel.issue.assessId = issueAssessItem.id
      trainClassDetail.learningTypeModel.issue.assessName = issueAssessItem.name
    }
  }

  /**
   * 列表-解析方案JSON配置
   * @param jsonStr 方案json字符串
   * @param trainClassDetail 培训类详情
   */
  parseTrainClassJsonConfigInList(jsonStr: any, trainClassDetail: TrainClassCommodityVo): void {
    const jsonConfig = jsonStr as Scheme
    const { schemeAssessItem } = Scheme.parseTrainClassAssess(jsonConfig)
    if (schemeAssessItem) {
      const creditResult = schemeAssessItem.learningResults?.find((item: LearningResult) => {
        return item.type == 1
      })
      const certificateTemplate = schemeAssessItem.learningResults?.find((item: LearningResult) => {
        return item.type == 2
      })
      if (creditResult) {
        trainClassDetail.period = creditResult.grade
      }
      if (certificateTemplate) {
        trainClassDetail.certificateTemplateId = certificateTemplate.certificateTemplateId
      }
    }
  }

  /**
   * 详情-解析方案JSON配置
   * @param jsonStr 方案json字符串
   * @param trainClassDetail 培训方案详情
   */
  parseMyTrainClassDetailJsonConfig(jsonStr: string, trainClassDetail: MyTrainClassDetailClassVo): void {
    // 获取考核项配置
    const jsonConfig = JSON.parse(jsonStr) as Scheme
    trainClassDetail.trainingType = jsonConfig?.trainingType
    const { schemeAssessItem, issueAssessItem } = Scheme.parseTrainClassAssess(jsonConfig)
    if (schemeAssessItem) {
      //获取培训成果中学分
      const creditLearningResult = schemeAssessItem.learningResults?.find((learnResult: any) => learnResult.type == 1)
      trainClassDetail.trainClassBaseInfo.period = creditLearningResult?.grade || 0
      trainClassDetail.trainClassBaseInfo.assessSettingId = schemeAssessItem?.id
      trainClassDetail.trainClassBaseInfo.assessSettingName = schemeAssessItem?.name
      trainClassDetail.trainClassBaseInfo.creditId = creditLearningResult?.id
      trainClassDetail.taxCode = jsonConfig?.commoditySale?.taxCode
      trainClassDetail.categoryId = jsonConfig?.commoditySale?.categoryId
      //配置培训模板
      if (schemeAssessItem?.learningResults) {
        const templateResult = schemeAssessItem.learningResults?.find((learnResult: any) => learnResult.type == 2)
        if (templateResult) {
          trainClassDetail.trainClassBaseInfo.hasLearningResult = true
          trainClassDetail.trainClassBaseInfo.learningResultId = templateResult?.certificateTemplateId
          trainClassDetail.trainClassBaseInfo.learningResultAchievementsId = templateResult?.id

          trainClassDetail.trainClassBaseInfo.openPrintTemplate = templateResult?.openPrintTemplate
        }
        const creditResult = schemeAssessItem.learningResults?.find((learnResult: any) => learnResult.type == 1)
        if (creditResult) {
          trainClassDetail.trainClassBaseInfo.period = creditResult?.grade
        }
      }
    }
  }

  /**
   * 解析方案JSON配置
   * @param jsonStr 方案json字符串
   */
  parseTrainClassJsonConfig(jsonStr: string): {
    period: number
  } {
    const result = {
      period: null as number
    }
    try {
      const jsonConfig = JSON.parse(jsonStr) as Scheme
      const { schemeAssessItem } = Scheme.parseTrainClassAssess(jsonConfig)
      if (schemeAssessItem) {
        result.period = schemeAssessItem.learningResults?.find(
          (item: LearningResult) => item.gradeType === 'CREDIT'
        )?.grade
      }
    } catch (e) {
      // e
    }
    return result
  }
}

export default new QueryTrainClass()
