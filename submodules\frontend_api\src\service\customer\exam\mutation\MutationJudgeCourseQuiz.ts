import msCourseLearning from '@api/ms-gateway/ms-studentcourselearning-v1'
import msLearning, { ApplyLearningTokenRequest } from '@api/ms-gateway/ms-studentlearning-v1'
import { Response, ResponseStatus } from '@hbfe/common'
import MsStudentLearningV1 from '@api/ms-gateway/ms-studentlearning-v1'
import msChooseCourse, { StudentCourseLearningTokenResponse } from '@api/ms-gateway/ms-choosecourselearningscene-v1'
import msAutoCourse from '@api/ms-gateway/ms-autonomouscourselearningscene-v1'
import TrainClassModule from '@api/service/customer/train-class/TrainClassModule'
import ApplyStudentLearningToken from '@api/service/common/token/ApplyStudentLearningToken'
export class MutationJudgeCourseQuiz {
  /**
   * 学员学习token，类型为string
   */
  private studentToken = ''
  /**
   * 学员课程学习token，类型为string
   */
  private courseLearningToken = ''
  /**
   * 参训资格ID
   */
  qualificationId = ''
  /**
   * 学习方式id
   */
  learningId = ''
  /**
   * 班级类型 1 选课规则 2自主选课
   */
  schemeType = 1

  /**
   * 课程id
   */
  courseId = ''
  /**
   * 大纲id，如果是自主选课需要用到
   */
  outlineId = ''
  /**
   * code 60001 查询不到课后测验配置
   60002 课后测验已达标，不可进入
   60003 不大于最小学习进度
   21001 试题不足，自动合格
   */
  async judgeCanQuiz() {
    const classStatueQuery = TrainClassModule.queryTrainClassFactory.getQueryClassStatue()
    classStatueQuery.qualificationId = this.qualificationId
    classStatueQuery.learningId = this.learningId
    let newResStatus = await classStatueQuery.judgeClass()
    if (newResStatus.isSuccess() && newResStatus.code != 51001) {
      const getToken = new ApplyStudentLearningToken(this.qualificationId, this.learningId)
      await getToken.apply()
      this.studentToken = getToken.token
      newResStatus = await this.getCourseLearningToken()
      if (newResStatus.isSuccess()) {
        newResStatus = await this.applyCourseQuizLearning()
      }
    }

    return newResStatus
  }
  private async applyCourseQuizLearning() {
    const newResStatus = new ResponseStatus(200, '')

    const res = await msCourseLearning.applyCourseQuizLearning(this.courseLearningToken)

    if (!res.status.isSuccess() || res.data.applyResult.code != '200') {
      if (!res.status.isSuccess()) {
        newResStatus.code = res.status.code
        newResStatus.message = res.status.message
      } else {
        newResStatus.code = parseInt(res.data.applyResult.code)
        newResStatus.message = res.data.applyResult.message
      }
    }
    return newResStatus
  }
  private async getCourseLearningToken() {
    let courseLeanrnRes
    if (this.schemeType == 1) {
      courseLeanrnRes = await msChooseCourse.applyCourseLearning({
        studentLearningToken: this.studentToken,
        studentCourseId: this.courseId
      })
    } else {
      courseLeanrnRes = await msAutoCourse.applyCourseLearning({
        studentLearningToken: this.studentToken,
        courseId: this.courseId,
        outlineId: this.outlineId
      })
    }
    const newResStatus = this.filterCode(courseLeanrnRes)
    if (newResStatus.isSuccess()) {
      this.courseLearningToken = courseLeanrnRes.data.token
    }
    return newResStatus
  }
  private filterCode(res: Response<StudentCourseLearningTokenResponse>) {
    let newResStatus = new ResponseStatus(200, '')

    if (!res.status.isSuccess() || res.data.applyResult.code != '200') {
      if (!res.status.isSuccess()) {
        newResStatus = res.status
      } else {
        newResStatus.code = parseInt(res.data.applyResult.code)
        newResStatus.message = res.data.applyResult.message
      }
    }
    return newResStatus
  }
}
