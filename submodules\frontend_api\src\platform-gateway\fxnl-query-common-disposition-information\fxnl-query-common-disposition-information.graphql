schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getIndustriesByServiceIdInDistributor:IndustryDictionaryResponse
	"""获取分销商所属服务商专属年度列表"""
	getYearsByServiceIdInDistributor:YearDictionaryResponse
}
"""<AUTHOR>
	@description:行业字典数据
	@date 2024/10/16 15:02
"""
type IndustryDictionaryResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.disposition.IndustryDictionaryResponse") {
	industryDictionaryList:[IndustryDictionary]
	code:String
	message:String
}
"""<AUTHOR>
	@description:年度字典数据
	@date 2024/10/16 15:03
"""
type YearDictionaryResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.disposition.YearDictionaryResponse") {
	yearList:[Dictionary]
	code:String
	message:String
}
"""字典数据
	<AUTHOR>
"""
type Dictionary @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.disposition.model.Dictionary") {
	"""字典ID"""
	id:String
	"""排序"""
	sort:Int!
}
"""行业字典实体
	<AUTHOR>
"""
type IndustryDictionary @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.disposition.model.IndustryDictionary") {
	"""行业字典ID"""
	id:String
	"""行业属性字典ID"""
	propertyId:String
	"""排序"""
	sort:Int!
}

scalar List
