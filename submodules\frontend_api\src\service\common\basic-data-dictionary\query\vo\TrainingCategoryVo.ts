import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

class TrainingCategoryVo {
  /**
   * 唯一标识
   */
  propertyId: string
  /**
   * 培训类别名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
  /**
   * 展示名称
   */
  showName: string
  /**
   * 父级id
   */
  parentId: string

  static from(res: TrainingPropertyResponse) {
    const trainingCategory = new TrainingPropertyResponse()
    trainingCategory.name = res.name
    trainingCategory.propertyId = res.propertyId
    trainingCategory.sort = res.sort
    trainingCategory.parentId = res.parentId
    trainingCategory.showName = res.showName
    return trainingCategory
  }
}

export default TrainingCategoryVo
