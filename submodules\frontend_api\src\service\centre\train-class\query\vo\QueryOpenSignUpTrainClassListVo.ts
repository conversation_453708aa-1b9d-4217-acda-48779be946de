/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-09-25 10:46:19
 * @LastEditors: chenweinian
 * @LastEditTime: 2023-09-26 11:04:00
 * @Description:
 */
import {
  CommoditySkuRequest,
  OnShelveRequest,
  SchemeRequest,
  SkuPropertyRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import TrainClassUtils from '@api/service/centre/train-class/util/TrainClassUtils'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

export class RegionSkuPropertySearchRequest {
  /**
     * 地区匹配方式
     <p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
     <p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
     @see RegionSearchType
     */
  regionSearchType = 1
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest> = []
}

export class RegionSkuPropertyRequest {
  /**
   * 地区: 省
   */
  province = ''
  /**
   * 地区: 市
   */
  city = ''
  /**
   * 地区: 区县
   */
  county = ''
}
/**
 * @description 【无需登录】开放报名的方案列表查询参数
 */
class QueryOpenSignUpTrainClassListVo {
  /**
   * 培训形式
   */
  trainingForm: TrainingModeEnum = null
  /**
   * 年度
   */
  year = ''

  /**
   * 行业
   */
  industry = ''

  /**
   * 地区
   */
  region = new RegionSkuPropertySearchRequest()

  /**
   * 科目类型
   */
  subjectType = ''

  /**
   * 培训类别
   */
  trainingCategory = ''

  /**
   * 培训专业
   */
  trainingProfessional = ''

  /**
   * 卫生行业-培训对象
   */
  trainingObject = ''
  /**
   * 卫生行业-岗位类别
   */
  positionCategory = ''
  /**
   * 工勤行业-技术等级
   */
  jobLevel = ''
  /**
   * 工勤行业-工种
   */
  jobCategory = ''
  /**
   * 教师行业-学段
   */
  learningPhase = ''
  /**
   * 教师行业-学科
   */
  discipline = ''
  /**
   * 药师行业-证书类型
   */
  certificatesType = ''
  /**
   * 药师行业-执业类别
   */
  practitionerCategory = ''
  /**
   * 培训班名称（模糊匹配）
   */
  schemeName = ''
  /**
   * 培训班名称（精确匹配）
   */
  schemeNameExact = ''
  /**
   * 培训方案Id
   */
  schemeId = ''
  /**
   * 专题id
   */
  trainingChannelIds = ''

  /**
   * 是否展示所有资源
（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
   */
  isShowAll = false
  /**
   * 转换本地vo模型为远端模型
   */
  to(): CommoditySkuRequest {
    const to = new CommoditySkuRequest()
    to.schemeRequest = new SchemeRequest()
    to.schemeRequest.schemeName = this.schemeName ?? undefined
    to.saleTitleList = this.schemeNameExact ? [this.schemeNameExact] : undefined
    to.isShowAll = this.isShowAll
    to.skuPropertyRequest = new SkuPropertyRequest()
    to.skuPropertyRequest.year = this.year ? [this.year] : undefined
    to.skuPropertyRequest.industry = this.industry ? [this.industry] : undefined
    to.skuPropertyRequest.subjectType = this.subjectType ? [this.subjectType] : undefined
    to.skuPropertyRequest.trainingProfessional = this.trainingProfessional ? [this.trainingProfessional] : undefined
    to.skuPropertyRequest.trainingCategory = this.trainingCategory ? [this.trainingCategory] : undefined
    to.skuPropertyRequest.trainingObject = this.trainingObject ? [this.trainingObject] : undefined
    to.skuPropertyRequest.positionCategory = this.positionCategory ? [this.positionCategory] : undefined
    to.skuPropertyRequest.jobLevel = this.jobLevel ? [this.jobLevel] : undefined
    to.skuPropertyRequest.jobCategory = this.jobCategory ? [this.jobCategory] : undefined
    to.skuPropertyRequest.learningPhase = this.learningPhase ? [this.learningPhase] : undefined
    to.skuPropertyRequest.discipline = this.discipline ? [this.discipline] : undefined
    to.skuPropertyRequest.practitionerCategory = this.practitionerCategory ? [this.practitionerCategory] : undefined
    to.skuPropertyRequest.certificatesType = this.certificatesType ? [this.certificatesType] : undefined
    to.trainingChannelIds = this.trainingChannelIds ? [this.trainingChannelIds] : undefined
    to.skuPropertyRequest.trainingForm = this.trainingForm ? [this.trainingForm] : undefined
    if (this.schemeId) {
      to.schemeRequest.schemeIdList = [this.schemeId]
    }
    const newRegion = new Array<string>()
    this.region.region.forEach((item) => {
      if (item.province) {
        newRegion.push(item.province)
      }
      if (item.city) {
        newRegion.push(item.city)
      }
      if (item.county) {
        newRegion.push(item.county)
      }
    })
    to.skuPropertyRequest.regionSkuPropertySearch = TrainClassUtils.setRegion(newRegion)
    // 默认查询上架的班级
    to.onShelveRequest = new OnShelveRequest()
    to.onShelveRequest.onShelveStatus = 1
    return to
  }
}

export default QueryOpenSignUpTrainClassListVo
