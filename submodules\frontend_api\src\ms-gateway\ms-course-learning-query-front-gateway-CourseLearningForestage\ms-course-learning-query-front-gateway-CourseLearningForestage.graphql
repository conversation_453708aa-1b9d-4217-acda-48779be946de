"""独立部署的微服务,K8S服务名:ms-course-learning-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取当前服务商下课程数量
		@param status 课程状态，传入查询指定状态课程数量
		@return
	"""
	countCourseInServicer(status:CourseStatusEnum):Long!
	"""获取当前服务商下课件数量
		@param status 课件状态，传入查询指定状态课件数量
		@return
	"""
	countCoursewareInServicer(status:CoursewareStatusEnum):Long!
	"""获取当前服务商下根据方案统计大纲信息
		@param
		@param dataFetchingEnvironment
		@return
	"""
	countPeriodCountOfCourseTrainingOutlineInSchemeInServicer(request:CourseInSchemeRequest):CoursePeriodCountOfCourseTrainingOutlineResponse @optionalLogin
	"""获取我的课程评价
		@param studentCourseAppraisalId 学员课程评价编号
		@return 我的课程评价信息
	"""
	getCourseAppraiseInMyself(studentCourseAppraisalId:String):StudentCourseAppraiseResponse
	"""获取当前服务商下课程评价统计
		@param courseIds 课程id集合
		@return 课程评价统计信息
	"""
	getCourseAppraiseStatisticsInServicer(courseIds:[String]):[CourseAppraiseStatisticsResponse]
	"""获取当前服务商下课程分类详情
		@param courseCategoryId 课程分类ID
		@return
	"""
	getCourseCategoryInServicer(courseCategoryId:String):CourseCategoryResponse @optionalLogin
	"""获取服务商下指定方案的课程数量
		@return
	"""
	getCourseCountBySchemeIdInServicer(getCourseCountRequest:GetCourseCountRequest):[CourseCountBySchemeIdResponse] @optionalLogin
	"""获取当前服务商下课程详情
		@param courseId 课程分类ID
		@return
	"""
	getCourseInServicer(courseId:String):CourseDetailResponse @optionalLogin
	"""获取当前服务商对应课程的选课人数
		@param courseId
		@param dataFetchingEnvironment
		@return
	"""
	getCourseLearnStatistics(courseId:String):CourseLearnStatisticsResponse @optionalLogin
	"""获取我的学员课程学习详情
		@param studentCourseId 学员课程Id
	"""
	getCourseLearningRecordInMyself(studentCourseId:String):StudentCourseLearningResponse
	"""获取当前服务商下的课程学习大纲
		@param outlineId 课程学习大纲id(必填)
		@return
	"""
	getCourseTrainingOutlineInServicer(outlineId:String):CourseTrainingOutlineResponse @optionalLogin
	"""获取当前学员当前课程最后一门课程学习详情"""
	getLastStudentCourseLearningInMyself(request:StudentCourseLearningRequest):StudentCourseLearningResponse
	"""获取当前学员最后一门课程学习详情"""
	getLastStudentCourseLearningInServicer:StudentCourseLearningResponse
	"""获取当前学员最后一门课程学习详情"""
	getLastStudentCourseLearningInSubProject:StudentCourseLearningResponse
	"""查询我的最近在学习的期别参训资格
		@return 期别参训资格id
	"""
	getLastStudyQualificationInMyself:StudentIssueLearningResponse
	"""获取当前服务商下内容id对应的学习心得内容
		@returns
	"""
	getLearningExperienceContentInServicer(contentId:String):String @optionalLogin
	getSchemeLearningRule(schemeId:String):LearningRuleSnapshotResponse
	"""按星级分组统计课程评价数量"""
	getStudentCourseAppraiseStarStatisticsInCourseSupplier(courseId:String):StudentCourseAppraiseStarStatisticsResponse @optionalLogin
	"""按星级分组统计课程评价数量"""
	getStudentCourseAppraiseStarStatisticsInServicer(courseId:String):StudentCourseAppraiseStarStatisticsResponse @optionalLogin
	"""获取我的选课规则培训方案去重后可选课程列表 用于只能选课返回后填充课程信息"""
	listCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself(request:CanStudentChooseCourseRequest,sort:[CanChooseCourseSortRequest]):[CourseInSchemeV2Response]
	"""获取我的自主选课可选课程学习大纲列表"""
	listCanChooseCourseTrainingOutlineOfAutonomousCourseLearningSceneInMyself(request:StudentChooseCourseTrainingOutlineRequest):[CourseTrainingOutlineResponse]
	"""获取我的选课规则可选课程学习大纲列表"""
	listCanChooseCourseTrainingOutlineOfChooseCourseLearningSceneInMyself(request:StudentChooseCourseTrainingOutlineRequest):[CourseTrainingOutlineResponse]
	"""获取当前服务商下课程分类列表"""
	listCourseCategoryInServicer(request:CourseCategoryRequest):[CourseCategoryResponse] @optionalLogin
	"""获取当前服务商下的培训方案配置的课程列表
		@return
	"""
	listCourseInSchemeInServicer(request:CourseInSchemeRequest):[CourseInSchemeResponse] @optionalLogin
	"""list查询学习心得主题配置
		@param
		@return
	"""
	listLearningExperienceTopic(request:LearningExperienceTopicRequest):[LearningExperienceTopicResponse]
	"""获取当前服务商下教师列表
		@param teacherIds 教师id集合
		@return 当前服务商下教师列表
	"""
	listTeacherInServicer(teacherIds:[String]):[TeacherResponse] @optionalLogin
	"""分页获取我的选课规则培训方案去重后可选课程列表"""
	pageCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself(page:Page,request:CanStudentChooseCourseRequest,sort:[CanChooseCourseSortRequest]):CourseInSchemeV2ResponsePage @page(for:"CourseInSchemeV2Response")
	"""分页获取我的选课规则培训方案可选课程列表"""
	pageCanChooseCourseOfChooseCourseLearningSceneInMyself(page:Page,request:StudentChooseCourseRequest,sort:[ChooseCourseSortRequest]):CourseInSchemeResponsePage @page(for:"CourseInSchemeResponse")
	"""分页获取课程分布  只有工勤有需求 在工勤上实现  所以这个口暂时不做实现
		@param request 分页获取课程分布
		@return 分页获取课程分布
	"""
	pageCourseDistributionResponse(page:Page,request:CourseDistributionRequest,sort:[CourseDistributionSortRequest]):CourseDistributionResponsePage @page(for:"CourseDistributionResponse")
	"""获取当前课程/课件供应商下课程分页
		@param page    分页对象
		@param request 查询参数对象
		@return
	"""
	pageCourseInCourseSupplier(page:Page,request:CourseRequest,sort:[CourseSortRequest]):CourseResponsePage @page(for:"CourseResponse") @optionalLogin
	"""分页获取当前服务商下的课程包下课程列表
		@param request 课程包下课程请求参数
		@return 当前服务商下的课程包下课程列表 {@link  Page<CourseInPackageResponse> }
	"""
	pageCourseInPackageV2InServicer(request:CourseInPackageRequest):CourseInPackageResponsePage @page(for:"CourseInPackageResponse") @optionalLogin
	"""分页获取当前服务商下的培训方案配置的课程列表
		@return
	"""
	pageCourseInSchemeInServicer(page:Page,request:CourseInSchemeRequest):CourseInSchemeResponsePage @page(for:"CourseInSchemeResponse") @optionalLogin
	"""获取当前服务商下课程分页
		@param page    分页对象
		@param request 查询参数对象
		@return
	"""
	pageCourseInServicer(page:Page,request:CourseRequest,sort:[CourseSortRequest]):CourseResponsePage @page(for:"CourseResponse") @optionalLogin
	"""分页获取我的自主选课培训方案所有课程列表"""
	pageCourseOfAutonomousCourseLearningSceneInMyself(page:Page,request:StudentCourseLearningRequest,sort:[CourseLearningSortRequest]):StudentCourseLearningResponsePage @page(for:"StudentCourseLearningResponse")
	"""分页获取我的自主选课课程学习大纲列表(目前需求没有要求，暂不实现)"""
	pageCourseTrainingOutlineOfAutonomousCourseLearningSceneInMyself(page:Page,request:StudentCourseTrainingOutlineRequest):CourseTrainingOutlineResponsePage @page(for:"CourseTrainingOutlineResponse")
	"""分页获取我的兴趣课程学习大纲列表(目前需求没有要求，暂不实现)"""
	pageCourseTrainingOutlineOfInterestCourseLearningSceneInMyself(page:Page,request:StudentCourseTrainingOutlineRequest):CourseTrainingOutlineResponsePage @page(for:"CourseTrainingOutlineResponse")
	"""获取当前服务商下课程分页
		@param request 查询参数对象
		@return 当前服务商下课程分页 {@link Page<CourseResponse>}
	"""
	pageCourseV2InServicer(request:CourseV2Request):CourseResponsePage @page(for:"CourseResponse") @optionalLogin
	"""分页获取我的课件学习记录列表"""
	pageCoursewareLearningRecordInMyself(page:Page,request:CoursewareLearningRecordRequest):CoursewareLearningRecordResponsePage @page(for:"CoursewareLearningRecordResponse")
	"""分页获取前服务商下精品课程信息
		@param page    分页参数
		@param request 请求擦书
		@param sort    排序参数
		@return 精品课程
	"""
	pageExcellentCourseInServicer(page:Page,request:ExcellentCourseRequest,sort:[ExcellentCourseSortRequest]):ExcellentCourseResponsePage @page(for:"ExcellentCourseResponse") @optionalLogin
	"""查询当前学员的学员心得所有的提交记录的分页列表 ->历史记录"""
	pageLearningExperienceInStudent(page:Page,request:StudentLearningExperienceRequest,sorts:[StudentLearningExperienceSortRequest]):StudentLearningExperienceResponsePage @page(for:"StudentLearningExperienceResponse")
	"""当前学员的学员心得主题列表 ->未参加"""
	pageLearningExperienceNotParticipatedInStudent(page:Page,request:StudentLearningExperienceRequest,sorts:[StudentLearningExperienceSortRequest]):StudentLearningExperienceLastedResponsePage @page(for:"StudentLearningExperienceLastedResponse")
	"""当前学员的学员心得主题列表 ->已参加"""
	pageLearningExperienceParticipatedInStudent(page:Page,request:StudentLearningExperienceRequest,sorts:[StudentLearningExperienceSortRequest]):StudentLearningExperienceLastedResponsePage @page(for:"StudentLearningExperienceLastedResponse")
	"""分页查询我的课表考勤记录列表
		@param page    分页对象
		@param request 请求参数
		@return 分页结果
	"""
	pagePlanItemAttendanceInMyself(page:Page,request:PlanItemAttendanceRequest):PlanItemAttendanceResponsePage @page(for:"PlanItemAttendanceResponse")
	"""课程供应商 学员课程评价分页
		@param page
		@param courseId 课程id
		@return 学员课程评价
	"""
	pageStudentCourseAppraiseInCourseSupplier(page:Page,courseId:String):StudentCourseAppraiseResponsePage @page(for:"StudentCourseAppraiseResponse") @optionalLogin
	"""获取当前服务商下学员课程评价分页
		@param page
		@param courseId 课程id
		@return 学员课程评价
	"""
	pageStudentCourseAppraiseInServicer(page:Page,courseId:String):StudentCourseAppraiseResponsePage @page(for:"StudentCourseAppraiseResponse")
	"""查询课程包下课程评价列表
		只返回未屏蔽/可展示的评价
	"""
	pageStudentCourseAppraisedByCoursePackageIdInServicer(page:Page,coursePackageId:String,sort:[StudentCourseAppraisedSortRequest]):StudentCourseAppraisedResponsePage @page(for:"StudentCourseAppraisedResponse") @optionalLogin
	"""分页获取当前服务商下的学员课程学习列表"""
	pageStudentCourseInServicer(page:Page,request:StudentCourseLearningCommonRequest):StudentCourseLearningResponsePage @page(for:"StudentCourseLearningResponse") @optionalLogin
	"""分页获取我的选课规则培训方案已选课程列表"""
	pageStudentCourseOfChooseCourseLearningSceneInMyself(page:Page,request:StudentCourseLearningRequest,sort:[CourseLearningSortRequest]):StudentCourseLearningResponsePage @page(for:"StudentCourseLearningResponse")
	"""分页获取我的选课规则培训方案已选课程列表（默认带的课程信息）"""
	pageStudentCourseOfChooseCourseLearningSceneV2InMyself(page:Page,request:StudentCourseLearningRequest,sort:[CourseLearningSortRequest]):StudentCourseLearningV2ResponsePage @page(for:"StudentCourseLearningV2Response")
	"""分页获取我的兴趣课列表"""
	pageStudentCourseOfInterestCourseLearningSceneInMyself(page:Page,request:StudentCourseLearningRequest,sort:[CourseLearningSortRequest]):StudentCourseLearningResponsePage @page(for:"StudentCourseLearningResponse")
	"""获取当前服务商下根据方案统计大纲信息
		@param studentNo               学号
		@param dataFetchingEnvironment
		@returns
	"""
	statisticsCourseInSchemeInMyself(studentNo:String,schemeId:String):[CourseTrainingOutlineResponse] @optionalLogin
	"""获取当前服务商下根据方案统计大纲信息
		@param schemeId                学习方案id
		@param dataFetchingEnvironment
		@return
	"""
	statisticsCourseInSchemeInServicer(schemeId:String):[CourseTrainingOutlineResponse] @optionalLogin
	"""获取我的选课规则下按大纲选课统计"""
	statisticsCourseLearningSceneChooseCourseByOutlineOfInMyself(StudentNo:String!):[StatisticsStudentCourseOfChooseCourseLearningResponse]
	"""按照日期分组查询我的期别的日期列表
		@param qualificationId 期数参训资格ID（不能为空）
		@return 按日期分组的日期信息列表
	"""
	statisticsIssueByDateInMyself(qualificationId:String):[String]
	"""分页获取我的兴趣课列表"""
	testPageStudentCourseOfInterestCourseLearningSceneInMyself(page:Page,request:StudentCourseLearningRequest,sort:[CourseLearningSortRequest],needFieldList:[String]):StudentCourseLearningResponsePage @page(for:"StudentCourseLearningResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""@version: 1.0
	@description: 学员可选课查询条件
	@author: sugs
	@create: 2023-03-10 09:44
"""
input CanStudentChooseCourseRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.CanStudentChooseCourseRequest") {
	"""学号（必填）"""
	studentNo:String
	"""课程学习大纲课程"""
	courseOfCourseTrainingOutline:CourseOfCourseTrainingOutlineRequest
	"""课程信息"""
	course:CourseInfoRequest
	"""拓展信息"""
	extInfo:ExtInfoRequest
}
"""课程分类查询条件"""
input CourseCategoryRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.CourseCategoryRequest") {
	"""分类ID集合"""
	categoryIdList:[String]
	"""父级分类id"""
	parentId:String
	"""分类名称"""
	name:String
}
"""@Description  查看课程分布情况 请求参数
	<AUTHOR>
	@Date 2023/4/18 10:23
"""
input CourseDistributionRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.CourseDistributionRequest") {
	"""课件供应商Id"""
	supplierId:String
	"""培训方案Id"""
	schemeId:String
	"""方案状态"""
	schemeState:Int!
	"""分类Id"""
	classificationId:String
	"""课程数量范围  左值"""
	leftCount:Int!
	"""课程数量范围  右值"""
	rightCount:Int!
}
"""@version: 1.0
	@description: 课程包下课程请求参数
	@author: sugs
	@create: 2023-02-28 14:21
"""
input CourseInPackageRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.CourseInPackageRequest") {
	"""页码"""
	pageNo:Int!
	"""页面条数"""
	pageSize:Int!
	"""课程包id"""
	coursePackageId:String!
	"""课程id集合"""
	courseIds:[String]
	"""课程名称"""
	courseName:String
	"""课程名称匹配方式"""
	courseNameMatchType:Int
}
"""方案下课程配置请求参数
	<AUTHOR>
	@version 1.0
	@date 2022/1/6 19:22
"""
input CourseInSchemeRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.CourseInSchemeRequest") {
	"""课程学习大纲下课程"""
	courseOfCourseTrainingOutline:CourseOfCourseTrainingOutlineRequest
	"""课程信息"""
	course:CourseInfoRequest
	"""方案id"""
	schemeId:String
	"""课程包id"""
	coursePackageId:String
}
"""课程查询条件"""
input CourseRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.CourseRequest") {
	"""课程ID集合"""
	courseIdList:[String]
	"""分类ID集合"""
	categoryIdList:[String]
	"""课件供应商ID
		传-1代表查询未被授权的课程
		-2 为 已分配的
	"""
	supplierId:String
	"""是否排除0学时"""
	excludeZeroPeriod:Boolean
	"""课程名称"""
	name:String
	"""转换状态 0.草稿 1.转换中 2.转换成功 3.转换失败
		@see CourseStatus
	"""
	status:Int
	"""转换状态数组，当此参数不为空时，将忽略status参数
		0.草稿 1.转换中 2.转换成功 3.转换失败
		@see CourseStatus
	"""
	statuses:[Int]
	"""课程状态 0停用，1启用"""
	enable:Int
	"""创建时间开始"""
	createTimeBegin:DateTime
	"""创建时间结束"""
	createTimeEnd:DateTime
	"""课件id"""
	coursewareId:String
}
"""@version: 1.0
	@description: 课程查询条件
	@author: sugs
	@create: 2023-02-28 14:35
"""
input CourseV2Request @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.CourseV2Request") {
	"""页码"""
	pageNo:Int!
	"""页面条数"""
	pageSize:Int!
	"""课程排序请求参数"""
	sort:[CourseSortRequest]
	"""课程ID集合"""
	courseIdList:[String]
	"""分类ID集合"""
	categoryIdList:[String]
	"""课件供应商ID
		传-1代表查询未被授权的课程
		-2 为 已分配的
	"""
	supplierId:String
	"""是否排除0学时"""
	excludeZeroPeriod:Boolean
	"""课程名称"""
	name:String
	"""转换状态 0.草稿 1.转换中 2.转换成功 3.转换失败
		@see CourseStatus
	"""
	status:Int
	"""转换状态数组，当此参数不为空时，将忽略status参数
		0.草稿 1.转换中 2.转换成功 3.转换失败
		@see CourseStatus
	"""
	statuses:[Int]
	"""课程状态 0停用，1启用"""
	enable:Int
	"""创建时间开始"""
	createTimeBegin:DateTime
	"""创建时间结束"""
	createTimeEnd:DateTime
	"""课件id"""
	coursewareId:String
}
"""课件学习记录查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/2/10 9:28
"""
input CoursewareLearningRecordRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.CoursewareLearningRecordRequest") {
	"""学员课程id"""
	studentCourseId:String
	"""学员学号"""
	studentNo:String
}
"""@version: 1.0
	@description: 精品课程请求参数
	@author: sugs
	@create: 2023-05-11 17:41
"""
input ExcellentCourseRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.ExcellentCourseRequest") {
	"""课程名称"""
	courseName:String
	"""课程分类"""
	courseCategory:String
}
input GetCourseCountRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.GetCourseCountRequest") {
	"""方案id"""
	schemeIds:[String]
	"""课程学习场景
		@see com.fjhb.ms.study.query.constants.CourseLearningSceneTypes
	"""
	courseLearningSceneTypes:[Int]
}
input LearningExperienceTopicRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.LearningExperienceTopicRequest") {
	"""学习心得主题配置id"""
	topicIds:[String]
	"""数据附属信息"""
	owner:OwnerInfo1
	"""方案信息"""
	scheme:SchemeRequest
	"""学习心得类型"""
	experienceType:ExperienceType
	"""参与形式"""
	participateType:ParticipateType
	"""课程id"""
	courseId:String
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""审核方式"""
	auditType:AuditType
	"""不传值默认全查"""
	isDelete:Boolean
}
"""@version: 1.0
	@description: 查询我的课表考勤记录列表
	@author: sugs
	@create: 2024-11-13 10:31
"""
input PlanItemAttendanceRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.PlanItemAttendanceRequest") {
	"""期数参训资格id"""
	qualificationId:String
	"""开始时间
		查询大于等于教学计划项的开始时间
	"""
	beginTime:DateTime
	"""结束时间
		查询小于等于教学计划项的开始时间
	"""
	endTime:DateTime
}
"""学员选课查询条件"""
input StudentChooseCourseRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.StudentChooseCourseRequest") {
	"""学号（必填）"""
	studentNo:String
	"""课程学习大纲课程"""
	courseOfCourseTrainingOutline:CourseOfCourseTrainingOutlineRequest
	"""课程信息"""
	course:CourseInfoRequest
	"""拓展信息"""
	extInfo:ExtInfoRequest
	"""排序"""
	sort:CourseInSchemeSort
}
"""课程学习大纲信息"""
input StudentChooseCourseTrainingOutlineRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.StudentChooseCourseTrainingOutlineRequest") {
	"""学员学号"""
	studentNo:String
	"""方案id"""
	schemeId:String
	"""排除的课程id"""
	excludeCourseIds:[String]
}
"""学员课程学习通用查询条件"""
input StudentCourseLearningCommonRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.StudentCourseLearningCommonRequest") {
	"""学员课程学习数据范围"""
	studentCourseLearningRange:StudentCourseLearningRangeRequest
	"""学号（必填）"""
	studentNo:String
	"""课程学习大纲课程"""
	courseOfCourseTrainingOutline:CourseOfCourseTrainingOutlineRequest
	"""课程信息"""
	course:CourseInfoRequest
	"""学员课程信息"""
	studentCourse:StudentCourseRequest
	"""学员媒体课程学习信息"""
	studentCourseMediaLearningRecord:StudentCourseMediaLearningRecordRequest
}
"""学员课程学习查询条件"""
input StudentCourseLearningRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.StudentCourseLearningRequest") {
	"""学号（必填）"""
	studentNo:String
	"""课程学习大纲课程"""
	courseOfCourseTrainingOutline:CourseOfCourseTrainingOutlineRequest
	"""课程信息"""
	course:CourseInfoRequest
	"""学员课程信息"""
	studentCourse:StudentCourseRequest
	"""学员媒体课程学习信息"""
	studentCourseMediaLearningRecord:StudentCourseMediaLearningRecordRequest
}
"""课程学习大纲信息"""
input StudentCourseTrainingOutlineRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.StudentCourseTrainingOutlineRequest") {
	"""学员学号"""
	studentNo:String
	"""学员课程信息"""
	studentCourse:StudentCourseRequest
}
input StudentLearningExperienceRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.StudentLearningExperienceRequest") {
	"""学员学习心得ID"""
	studentLearningExperienceIds:[String]
	"""数据归属"""
	owner:OwnerInfo1
	"""学习心得主题"""
	learningExperienceTopic:LearningExperienceTopicRequest1
	"""学员方案学习"""
	studentLearning:StudentSchemeLearningRequest
	"""用户信息"""
	user:UserRequest
	"""学习心得类型（班级心得，课程心得）"""
	experienceType:ExperienceType
	"""课程id"""
	courseId:String
	"""状态
		@see com.fjhb.ms.course.learning.query.constants.StudentLearningExperienceStatus
	"""
	status:[StudentLearningExperienceStatus]
	"""true只要被删除的 false只要未被删除的  null全查"""
	isDelete:Boolean
}
"""课程信息"""
input CourseInfoRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.nested.CourseInfoRequest") {
	"""课程Id"""
	courseId:String
	"""排除的课程id"""
	excludeCourseIds:[String]
	"""课程id集合"""
	courseIdList:[String]
	"""课程名称"""
	courseName:String
	"""课程名称匹配方式"""
	courseNameMatchType:Int
}
"""大纲课程配置信息"""
input CourseOfCourseTrainingOutlineRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.nested.CourseOfCourseTrainingOutlineRequest") {
	"""课程学习大纲课程id"""
	courseOfCourseTrainingOutlineId:String
	"""课程学习大纲课程id"""
	courseOfCourseTrainingOutlineIds:[String]
	"""课程学习大纲id集合"""
	outlineIds:[String]
	"""排除课程学习大纲id"""
	excludeOutlineIds:[String]
	"""大纲内课程类型(1：必修 2：选修 3：兴趣课)
		@see CourseTypes
	"""
	courseType:Int
}
"""@version: 1.0
	@description: 拓展信息
	@author: sugs
	@create: 2023-02-21 16:35
"""
input ExtInfoRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.nested.ExtInfoRequest") {
	"""课件供应商id集合"""
	supplierIds:[String]
}
"""学习心得主题"""
input LearningExperienceTopicRequest1 @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.nested.LearningExperienceTopicRequest") {
	"""心得主题id"""
	topicIds:[String]
	"""参与活动时间"""
	dateScopeRequest:DateScopeRequest
	"""审核方式"""
	auditType:AuditType
	"""心得参与形式"""
	participateType:ParticipateType
	"""true只要被删除的 false只要未被删除的  null全查"""
	isDelete:Boolean
}
input SchemeRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.nested.SchemeRequest") {
	"""培训方案id"""
	schemeId:String
	"""学习方式od"""
	learningId:String
}
"""@description: 学员课程学习数据范围
	@author: sugs
	@create: 2022-04-02 15:34
"""
input StudentCourseLearningRangeRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.nested.StudentCourseLearningRangeRequest") {
	"""课程学习方式资源类型 1.选课规则课程学习场景 2.自主选课课程学习场景 3.兴趣课课程学习场景
		@see CourseLearningSceneTypes
	"""
	courseLearningResourceType:Int
}
input StudentCourseMediaLearningRecordRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.nested.StudentCourseMediaLearningRecordRequest") {
	"""学习状态"""
	studyStatus:StudyStatusEnum
}
"""课程学习信息条件"""
input StudentCourseRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.nested.StudentCourseRequest") {
	"""学员课程学习状态（0：未评定 1：未合格 2：合格）
		@see StudentCourseLearningStatus
	"""
	courseLearningStatus:[Int]
}
input StudentSchemeLearningRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.nested.StudentSchemeLearningRequest") {
	"""参训资格ID"""
	qualificationIds:[String]
	"""学号"""
	studentNos:[String]
	"""方案id"""
	schemeIds:[String]
	"""学习方式id"""
	learningIds:[String]
}
input UserRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.nested.UserRequest") {
	"""用户id"""
	userIds:[String]
}
"""<AUTHOR>
	@Date2022/1/27 16:49
"""
input CanChooseCourseSortRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.sort.CanChooseCourseSortRequest") {
	"""可选课排序条件"""
	chooseCourseSort:CanStudentChooseCourseSortEnum
	"""支持排序方式"""
	sortType:SortTypeEnum
}
"""<AUTHOR>
	@Date2022/1/27 16:49
"""
input ChooseCourseSortRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.sort.ChooseCourseSortRequest") {
	"""选课排序条件"""
	chooseCourseSort:StudentChooseCourseSortEnum
	"""支持排序方式"""
	sortType:SortTypeEnum
}
"""@Description  查看课程分布情况 请求参数
	<AUTHOR>
	@Date 2023/4/18 10:23
"""
input CourseDistributionSortRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.sort.CourseDistributionSortRequest") {
	"""课程分布排序"""
	courseDistributionSort:CourseDistributionSortEnum
	"""支持排序方式"""
	sortType:SortTypeEnum
}
"""<AUTHOR>
input CourseInSchemeSort @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.sort.CourseInSchemeSort") {
	"""排序枚举
		@see com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.enums.CourseInSchemeSortEnum;
	"""
	courseInSchemeSort:CourseInSchemeSortEnum1
	"""支持排序方式"""
	sortType:SortTypeEnum
}
"""<AUTHOR>
	@Date2022/1/27 16:49
"""
input CourseLearningSortRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.sort.CourseLearningSortRequest") {
	"""学员课程学习排序条件"""
	studentCourseLearningSort:StudentCourseLearningSortEnum
	"""支持排序方式"""
	sortType:SortTypeEnum
}
"""@description: 课程排序请求参数
	@author: sugs
	@create: 2022-03-14 09:30
"""
input CourseSortRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.sort.CourseSortRequest") {
	"""课程排序枚举"""
	courseSort:CourseSortEnum
	"""排序方式 1降序 0升序"""
	sortType:Int
}
"""@version: 1.0
	@description: 精品课程排序参数
	@author: sugs
	@create: 2023-05-11 18:21
"""
input ExcellentCourseSortRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.sort.ExcellentCourseSortRequest") {
	"""方案下课程排序"""
	courseInSchemeSortEnum:CourseInSchemeSortEnum
	"""支持排序方式"""
	sortType:SortTypeEnum
}
input StudentCourseAppraisedSortRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.sort.StudentCourseAppraisedSortRequest") {
	"""排序字段"""
	sortField:String
	"""排序方式 1降序 0升序"""
	sortType:Int
}
input StudentLearningExperienceSortRequest @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.request.sort.StudentLearningExperienceSortRequest") {
	"""大纲下课程数量排序"""
	sort:StudentLearningExperienceSortEnum
	"""支持排序方式"""
	sortType:SortTypeEnum1
}
"""数据归属模型"""
input OwnerInfo1 @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.common.OwnerInfo") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位id"""
	unitId:String
	"""所属服务商类型
		@see ServicerTypes
		1.培训机构; 2.课件供应商; 3.渠道商; 4.参训单位;
	"""
	servicerType:Int
	"""所属服务商id"""
	servicerId:String
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
"""可选课查询排序条件"""
enum CanStudentChooseCourseSortEnum @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.enums.CanStudentChooseCourseSortEnum") {
	"""学时"""
	PERIOD
	"""综合评分"""
	TOTAL_APPRAISE
	"""选课人数"""
	CHOOSE_COURSE_COUNT
	"""大纲id"""
	OUTLINE_ID
	"""课程类型"""
	COURSE_TYPE
	"""大纲内课程序号"""
	SORT
	"""配置规则排序"""
	COURSE_CONFIG_SORT
}
enum CourseDistributionSortEnum @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.enums.CourseDistributionSortEnum") {
	"""课程数量"""
	COUNT
}
enum CourseInSchemeSortEnum1 @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.enums.CourseInSchemeSortEnum") {
	"""选课人数"""
	CHOOSE_COURSE_COUNT
	"""综合评分"""
	TOTAL_APPRAISE
	"""默认课程排序"""
	COURSE_SORT
}
"""@description:课程排序枚举
	@author: sugs
	@create: 2022-03-14 09:28
"""
enum CourseSortEnum @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.enums.CourseSortEnum") {
	"""创建时间"""
	CREATE_TIME
}
"""课程状态"""
enum CourseStatusEnum @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.enums.CourseStatusEnum") {
	"""停用"""
	DISABLE
	"""启用"""
	ENABLE
}
"""课件状态"""
enum CoursewareStatusEnum @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.enums.CoursewareStatusEnum") {
	"""解析中"""
	TRANSCODING
	"""可用"""
	AVAILABLE
	"""不可用"""
	UNAVAILABLE
}
"""排序类型"""
enum SortTypeEnum @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.enums.SortTypeEnum") {
	"""升序"""
	ASC
	"""降序"""
	DESC
}
"""选课查询排序条件"""
enum StudentChooseCourseSortEnum @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.enums.StudentChooseCourseSortEnum") {
	"""学时"""
	PERIOD
}
"""学员课程学习查询排序条件"""
enum StudentCourseLearningSortEnum @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.enums.StudentCourseLearningSortEnum") {
	"""学时"""
	PERIOD
	"""进度"""
	SCHEDULE
	"""时长"""
	TIME_LENGTH
}
"""@description: 课程评价统计信息
	@author: sugs
	@create: 2022-05-30 10:17
"""
type CourseAppraiseStatisticsResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseAppraiseStatisticsResponse") {
	"""课程信息"""
	course:CourseInfo
	"""课程评价统计信息"""
	courseAppraisalStatistics:CourseAppraise
}
"""课程分类"""
type CourseCategoryResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseCategoryResponse") {
	"""分类id"""
	id:String
	"""父级分类id"""
	parentId:String
	"""分类名称"""
	name:String
	"""当前排序"""
	sort:Int
}
type CourseCountBySchemeIdResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseCountBySchemeIdResponse") {
	"""方案id"""
	schemeId:String
	"""课程数"""
	courseCount:Int!
}
"""课程"""
type CourseDetailResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseDetailResponse") {
	"""课程目录"""
	courseOutlines:[CourseOutline]
	"""课程章节"""
	courseChapters:[CourseChapter]
	"""课程ID"""
	id:String
	"""课件供应商ID"""
	supplierId:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""课程简介内容"""
	aboutsContent:String
	"""课程分类"""
	categorys:[CourseCategoryResponse]
	"""教师ID集合"""
	teacherIds:[String]
	"""学时"""
	period:Double!
	"""转换状态 0.草稿 1.转换中 2.转换成功 3.转换失败
		@see CourseStatus
	"""
	status:Int
	"""课程状态 0停用，1启用"""
	enable:Int
	"""创建时间"""
	createTime:DateTime
	"""课程时长"""
	courseTimeLength:Long!
	"""试听 0禁止 1启用"""
	auditionStatus:Int
	"""课程评价信息"""
	courseAppraiseInfo:CourseAppraise
}
"""@Description  查看课程分布情况 响应参数
	<AUTHOR>
	@Date 2023/4/18 10:23
"""
type CourseDistributionResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseDistributionResponse") {
	"""课件供应商Id"""
	supplierId:String
	"""培训方案Id"""
	schemeId:String
	"""方案状态"""
	schemeState:Int!
	"""分类Id"""
	classificationId:String
	"""课程数量"""
	count:Int!
}
"""@description: 课程包下的课程信息
	@author: sugs
	@create: 2022-01-27 10:59
"""
type CourseInPackageResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseInPackageResponse") {
	"""包下课程id"""
	courseInPackageId:String
	"""数据归属信息"""
	owner:OwnerInfo
	"""包下课程配置信息"""
	courseInPackage:CourseInPackageInfo
	"""课程信息"""
	course:CourseInfo
}
"""方案课程配置主题模型
	<AUTHOR>
	@version 1.0
	@date 2022/1/6 20:10
"""
type CourseInSchemeResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseInSchemeResponse") {
	"""课程学习大纲课程id"""
	courseOfCourseTrainingOutlineId:String
	"""数据归属信息"""
	owner:OwnerInfo
	"""数据范围"""
	range:CourseInSchemeRange
	"""课程学习大纲课程"""
	courseOfCourseTrainingOutline:CourseOfCourseTrainingOutlineInfo
	"""课程信息"""
	course:CourseInfo
	"""选择课程数量"""
	chooseCoursePeopleCount:BigDecimal
	"""综合评价值"""
	totalAppraise:BigDecimal
}
"""方案课程配置主题模型
	<AUTHOR>
	@version 1.0
	@date 2022/1/6 20:10
"""
type CourseInSchemeV2Response @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseInSchemeV2Response") {
	"""课程学习大纲课程id"""
	courseOfCourseTrainingOutlineId:String
	"""数据归属信息"""
	owner:OwnerInfo
	"""数据范围"""
	range:CourseInSchemeRange
	"""课程学习大纲课程"""
	courseOfCourseTrainingOutline:CourseOfCourseTrainingOutlineInfo
	"""课程信息"""
	course:CourseDetailInfo
	"""课程配置是否配置学习心得"""
	hasLearningExperienceTopic:Boolean!
}
"""<AUTHOR>
	@date 2022/11/21 10:16
"""
type CourseLearnStatisticsResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseLearnStatisticsResponse") {
	"""课程学习人数"""
	courseLearnPeopleCount:Double!
}
"""大纲下课程统计"""
type CoursePeriodCountOfCourseTrainingOutlineResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CoursePeriodCountOfCourseTrainingOutlineResponse") {
	"""课程学习大纲下课程总学时"""
	courseTotalPeriod:Double
}
"""课程"""
type CourseResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseResponse") {
	"""课程ID"""
	id:String
	"""课件供应商ID"""
	supplierId:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""课程简介内容"""
	aboutsContent:String
	"""课程分类"""
	categorys:[CourseCategoryResponse]
	"""教师ID集合"""
	teacherIds:[String]
	"""学时"""
	period:Double!
	"""转换状态 0.草稿 1.转换中 2.转换成功 3.转换失败
		@see CourseStatus
	"""
	status:Int
	"""课程状态 0停用，1启用"""
	enable:Int
	"""创建时间"""
	createTime:DateTime
	"""课程时长"""
	courseTimeLength:Long!
	"""试听 0禁止 1启用"""
	auditionStatus:Int
	"""课程评价信息"""
	courseAppraiseInfo:CourseAppraise
}
"""大纲下课程统计"""
type CourseStatisticsOfCourseTrainingOutlineResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseStatisticsOfCourseTrainingOutlineResponse") {
	"""课程学习大纲下课程数"""
	courseCount:Int
	"""课程学习大纲下课程总学时"""
	courseTotalPeriod:Double
	"""课程学习大纲下课程完成学时"""
	completeTotalPeriod:Double
	"""课程学习大纲下课程学习中的学时"""
	learningTotalPeriod:Double
}
"""课程学习大纲信息"""
type CourseTrainingOutlineResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CourseTrainingOutlineResponse") {
	"""课程学习大纲Id"""
	outlineId:String
	"""数据归属信息"""
	owner:OwnerInfo
	"""课程学习大纲信息"""
	courseTrainingOutline:CourseTrainingOutlineInfo
	"""课程学习大纲课程统计信息"""
	courseStatisticOfCourseTrainingOutline:CourseStatisticsOfCourseTrainingOutlineResponse
	"""创建人信息"""
	creator:UserInfo
}
"""课件学习记录返回信息"""
type CoursewareLearningRecordResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.CoursewareLearningRecordResponse") {
	"""课件学习记录id"""
	coursewareLearningRecordId:String
	"""数据归属信息"""
	owner:OwnerInfo
	"""课件信息"""
	courseware:CoursewareInfo
	"""课件学习信息"""
	coursewareLearningRecord:CoursewareLearningRecordInfo
}
"""@version: 1.0
	@description: 精品课程信息
	@author: sugs
	@create: 2023-05-11 17:41
"""
type ExcellentCourseResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.ExcellentCourseResponse") {
	"""课程id"""
	courseId:String
	"""课程名称"""
	courseName:String
	"""课程学时"""
	period:Double
	"""封面图片路径"""
	iconPath:String
	"""课程简介内容"""
	aboutsContent:String
	"""课程分类"""
	categorys:[CourseCategoryResponse]
	"""教师ID集合"""
	teacherIds:[String]
	"""教师名称"""
	teacherNames:[String]
	"""课程评价信息"""
	courseAppraiseInfo:CourseAppraise
}
type LearningExperienceTopicResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.LearningExperienceTopicResponse") {
	"""学习心得主题id"""
	topicId:String
	"""数据归属"""
	owner:OwnerInfo
	"""方案信息"""
	scheme:SchemeInfo
	"""心得主题"""
	experienceTopicName:String
	"""学习心得类型"""
	experienceType:ExperienceType
	"""学习心得类型信息"""
	experienceTypeInfo:Map
	"""参与形式"""
	participateType:ParticipateType
	"""活动文本内容id"""
	descriptionContent:String
	"""参加活动类型"""
	participateTimeType:ParticipateTimeType
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""提交要求"""
	submitLimitNum:Double!
	"""审核方式"""
	auditType:AuditType
	"""提交次数"""
	submitLimitCount:Int!
	"""通过分"""
	passScore:Double!
	"""总分"""
	totalScore:Double!
	"""是否必选"""
	isRequired:Boolean
	"""数据操作信息"""
	dataManipulation:DataManipulationResponse
}
type LearningRuleSnapshotResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.LearningRuleSnapshotResponse") {
	"""规则快照id
		规则id+事件内业务时间+1天作为主键
	"""
	ruleSnapshotId:String
	"""规则id"""
	ruleId:String
	"""最大学习时长(单位秒)"""
	maxStudyTimeLength:Int
	"""学习时长限制方式 默认0-按日限制"""
	timeLengthLimitWay:Int
	"""规则状态"""
	status:Int
	"""规则快照生效时间"""
	effectiveStartTime:DateTime
	"""规则快照生效结束时间"""
	effectiveEndTime:DateTime
}
"""@version: 1.0
	@description: 教学计划考勤请求参数
	@author: sugs
	@create: 2024-11-09 15:25
"""
type PlanItemAttendanceResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.PlanItemAttendanceResponse") {
	"""教学计划项信息"""
	planItem:PlanItemResponse
	"""教学计划签到点信息"""
	signInPoint:[SignInPointResponse]
	"""签到记录信息"""
	signInRecord:[SignInRecordResponse]
}
"""@version: 1.0
	@description: 统计学员选课规则方案已选课数据
	@author: sugs
	@create: 2023-03-03 15:18
"""
type StatisticsStudentCourseOfChooseCourseLearningResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.StatisticsStudentCourseOfChooseCourseLearningResponse") {
	"""大纲id"""
	outlineId:String
	"""选课总学时"""
	chooseCourseTotalPeriod:Double
}
"""学员课程评价"""
type StudentCourseAppraiseResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.StudentCourseAppraiseResponse") {
	"""数据归属信息"""
	owner:OwnerInfo
	"""课程信息"""
	course:CourseInfo
	"""学员课程信息"""
	studentCourse:StudentCourseInfo
	"""课程评价信息"""
	courseAppraisalInfo:CourseAppraisalInfo
	"""学员课程评价数据范围"""
	range:StudentCourseAppraisalRange
	"""true 已屏蔽 false 未屏蔽"""
	isBlocked:Boolean
}
type StudentCourseAppraiseStarStatisticsResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.StudentCourseAppraiseStarStatisticsResponse") {
	"""课程id"""
	courseId:String
	"""该课程的评价总数"""
	appraiseTotalCount:Int
	"""各星级数量
		固定 100 200 300 400 500
	"""
	starStatisticsResponseList:[StarStatisticsResponse]
}
type StudentCourseAppraisedResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.StudentCourseAppraisedResponse") {
	"""评价用户id"""
	appraisalUserId:String
	"""学员课程评价编号"""
	studentCourseAppraisalId:String
	"""评价内容"""
	content:String
	"""课程评价值"""
	courseAppraise:BigDecimal
	"""评价时间"""
	appraisedTime:DateTime
}
"""用户课程信息"""
type StudentCourseLearningResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.StudentCourseLearningResponse") {
	"""数据归属信息"""
	owner:OwnerInfo
	"""数据范围"""
	range:StudentCourseLearningRange
	"""课程学习大纲下课程配置信息"""
	courseOfCourseTrainingOutline:CourseOfCourseTrainingOutlineInfo
	"""课程信息"""
	course:CourseInfo
	"""所属学员信息"""
	student:StudentInfo
	"""所属用户信息"""
	user:UserInfo
	"""学员课程信息"""
	studentCourse:StudentCourse
	"""学员媒体课程学习信息"""
	studentCourseMediaLearningRecord:StudentCourseMediaLearningRecord
	"""学员课后测验"""
	studentCourseQuiz:StudentCourseQuiz
	"""学员课程已评价信息"""
	studentCourseAppraised:StudentCourseAppraised
	"""课程配置是否配置学习心得"""
	hasLearningExperienceTopic:Boolean!
}
"""用户课程信息"""
type StudentCourseLearningV2Response @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.StudentCourseLearningV2Response") {
	"""数据归属信息"""
	owner:OwnerInfo
	"""数据范围"""
	range:StudentCourseLearningRange
	"""课程学习大纲下课程配置信息"""
	courseOfCourseTrainingOutline:CourseOfCourseTrainingOutlineInfo
	"""课程信息"""
	course:CourseDetailInfo
	"""所属学员信息"""
	student:StudentInfo
	"""所属用户信息"""
	user:UserInfo
	"""学员课程信息"""
	studentCourse:StudentCourse
	"""学员媒体课程学习信息"""
	studentCourseMediaLearningRecord:StudentCourseMediaLearningRecord
	"""学员课后测验"""
	studentCourseQuiz:StudentCourseQuiz
	"""学员课程已评价信息"""
	studentCourseAppraised:StudentCourseAppraised
	"""课程配置是否配置学习心得"""
	hasLearningExperienceTopic:Boolean!
}
"""@version: 1.0
	@description: 学员期数学习信息
	@author: sugs
	@create: 2024-11-18 14:31
"""
type StudentIssueLearningResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.StudentIssueLearningResponse") {
	"""期数参训资格id"""
	qualificationId:String
	"""学员方案参训资格id"""
	schemeQualificationId:String
	"""数据归属平台ID"""
	platformId:String
	"""数据归属平台版本ID"""
	platformVersionId:String
	"""数据归属项目ID"""
	projectId:String
	"""数据归属子项目ID"""
	subProjectId:String
	"""数据归属单位ID"""
	unitId:String
	"""数据归属服务商ID"""
	servicerId:String
	"""平台租户ID"""
	tenantId:String
	"""学员学号"""
	studentNo:String
	"""关联的用户ID"""
	userId:String
	"""关联的学习方案ID"""
	schemeId:String
	"""关联的期别ID"""
	issueId:String
	"""参训资格状态 | 1-正常 2-冻结 3-失效"""
	status:Int!
	"""状态最后变更时间"""
	statusChangeTime:DateTime
	"""学员方案来源类型 | 子订单、换货单、更换期别"""
	sourceType:String
	"""学员方案参训资格来源ID | 子订单号、换货单号、原期别参训资格ID"""
	qualificationSourceId:String
	"""来源子订单号（冗余，当来源是交易时，换期依然是记子订单）"""
	subOrderNo:String
	"""失效来源类型 | 退款单、换货单、更换期别"""
	frozenAndInvalidSourceType:String
	"""失效来源ID | 退货单号、换货单号、新期别参训资格ID"""
	frozenAndInvalidSourceId:String
	"""学员培训结果 | -1 -培训中 0-培训未合格 1-培训合格"""
	trainingResult:Int!
	"""培训合格时间"""
	trainingResultGainTime:DateTime
	"""参训资格创建时间"""
	createdTime:DateTime
}
type StudentLearningExperienceLastedResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.StudentLearningExperienceLastedResponse") {
	"""学员学习心得模型  未参加 待提交的会有这个模型 用于回显"""
	studentLearningExperience:StudentLearningExperienceResponse
	"""已提交的次数"""
	count:Long
	"""心得主题模型  已参加的口该值为null"""
	topicId:String
	"""大纲id 已参加的口该值为null"""
	outLineId:String
	"""心得配置的可提交总次数  （未减去已提交的次数，如果提交）"""
	submitLimitCount:Int
}
type StudentLearningExperienceResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.StudentLearningExperienceResponse") {
	"""学员学习心得ID"""
	studentLearningExperienceId:String
	"""数据归属"""
	owner:OwnerInfo
	"""学员方案学习"""
	studentLearning:StudentSchemeLearningResponse
	"""学员"""
	student:UserInfo
	"""学习心得主题"""
	learningExperienceTopic:LearningExperienceTopicResponse1
	"""学习心得类型"""
	experienceType:ExperienceType
	"""学习心得类型信息  根据心得类型判断 是课程就是课程id 是班级就是班级id"""
	experienceTypeInfo:[String]
	"""提交内容（如果为文本时，存储关联文本id，如果是附件是一个json结构，包含名称和地址）"""
	content:String
	"""状态"""
	status:StudentLearningExperienceStatus
	"""状态变更时间"""
	statusChangeTime:[StatusChangeResponse]
	"""审核意见"""
	remark:String
	"""得分"""
	score:Double!
	"""数据操作信息"""
	dataManipulation:DataManipulationResponse
}
"""@description: 教师信息
	@author: sugs
	@create: 2022-03-10 10:04
"""
type TeacherResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.TeacherResponse") {
	"""教师id"""
	id:String
	"""数据归属信息"""
	owner:OwnerInfo
	"""教师名称"""
	name:String
	"""头像/照片"""
	photo:String
	"""简介内容"""
	aboutsContent:String
	"""性别 -1:未知 0:女 1:男
		@see TeacherGenders
	"""
	gender:Int!
	"""创建时间"""
	createTime:DateTime
	"""创建人id"""
	createUserId:String
}
type StarStatisticsResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.StarStatisticsResponse") {
	"""评价值"""
	appraisalValue:Int
	"""数量"""
	count:Int
}
"""课程详情信息"""
type CourseDetailInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.common.CourseDetailInfo") {
	"""课程Id"""
	courseId:String
	"""课程名称"""
	courseName:String
	"""教师id"""
	teacherIds:[String]
	"""教师名称"""
	teacherNames:[String]
	"""课程学时"""
	period:Double
	"""试听 0禁止 1启用"""
	auditionStatus:Int
	"""封面图片路径"""
	iconPath:String
	"""课程评价信息"""
	courseAppraiseInfo:CourseAppraise
	"""选择课程数量"""
	chooseCoursePeopleCount:BigDecimal
}
"""方案课程配置数据范围
	<AUTHOR>
	@version 1.0
	@date 2022/2/9 20:53
"""
type CourseInSchemeRange @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.common.CourseInSchemeRange") {
	"""学习方案id"""
	schemeId:String
	"""期数id"""
	issueId:String
	"""课程学习方式id"""
	courseLearningId:String
	"""课程学习方式资源类型(1：选课学习场景 2：自主课程学习场景 3：兴趣课程学习场景)
		@see CourseLearningSceneTypes
	"""
	courseLearningResourceType:String
	"""课程学习方式资源id"""
	courseLearningResourceId:String
	"""课程来源类型//1-课程包 2-课程"""
	courseSourceType:String
	"""课程来源id"""
	courseSourceId:String
}
"""课程信息"""
type CourseInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.common.CourseInfo") {
	"""课程Id"""
	courseId:String
	"""课程名称"""
	courseName:String
	"""课程时长"""
	courseTimeLength:Long!
	"""教师名称"""
	teacherNames:[String]
	"""教师id"""
	teacherIds:[String]
	"""课程学时"""
	period:Double
	"""试听 0禁止 1启用"""
	auditionStatus:Int
	"""封面图片路径"""
	iconPath:String
	"""课程评价信息"""
	courseAppraiseInfo:CourseAppraise
	"""选择课程数量"""
	chooseCoursePeopleCount:BigDecimal
	"""课程分类"""
	categorys:[CourseCategoryResponse]
}
"""课件信息"""
type CoursewareInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.common.CoursewareInfo") {
	"""课件ID"""
	coursewareId:String
}
"""数据归属模型"""
type OwnerInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.common.OwnerInfo") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位id"""
	unitId:String
	"""所属服务商类型
		@see ServicerTypes
		1.培训机构; 2.课件供应商; 3.渠道商; 4.参训单位;
	"""
	servicerType:Int
	"""所属服务商id"""
	servicerId:String
}
"""@description: 学员课程评价数据范围
	@author: sugs
	@create: 2022-05-30 09:59
"""
type StudentCourseAppraisalRange @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.common.StudentCourseAppraisalRange") {
	"""学习方案id"""
	schemeId:String
	"""期数id"""
	issueId:String
	"""课程学习方式id"""
	courseLearningId:String
	"""课程学习方式资源类型(1：选课学习场景 2：自主课程学习场景 3：兴趣课程学习场景)
		@see CourseLearningSceneTypes
	"""
	courseLearningResourceType:String
	"""课程学习方式资源id"""
	courseLearningResourceId:String
}
"""学员课程学习数据范围
	<AUTHOR>
	@version 1.0
	@date 2022/2/9 20:53
"""
type StudentCourseLearningRange @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.common.StudentCourseLearningRange") {
	"""学习方案id"""
	schemeId:String
	"""期数id"""
	issueId:String
	"""课程学习方式id"""
	courseLearningId:String
	"""课程学习方式资源类型(1：选课学习场景 2：自主课程学习场景 3：兴趣课程学习场景)
		@see CourseLearningSceneTypes
	"""
	courseLearningResourceType:String
	"""课程学习方式资源id"""
	courseLearningResourceId:String
}
"""学员信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:00
"""
type StudentInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.common.StudentInfo") {
	"""参训资格ID"""
	qualificationId:String
	"""学号"""
	studentNo:String
}
type UserInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.common.UserInfo") {
	"""账户id"""
	accountId:String
	"""用户id"""
	userId:String
}
"""课程评价值"""
type CourseAppraise @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.course.CourseAppraise") {
	"""综合评价值"""
	totalAppraise:BigDecimal
	"""课程评价值"""
	courseAppraise:BigDecimal
	"""教师评价值"""
	teacherAppraise:BigDecimal
}
"""课程章节"""
type CourseChapter @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.course.CourseChapter") {
	"""课程章节id"""
	id:String
	"""章节名称"""
	name:String
	"""课程目录"""
	courseOutline:CourseOutline
	"""课件id"""
	coursewareId:String
	"""课件名称"""
	coursewareName:String
	"""课件媒体时长"""
	timeLength:Long!
	"""试听 0禁止 1启用"""
	auditionStatus:Int
	"""排序"""
	sort:Int!
}
"""课程目录"""
type CourseOutline @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.course.CourseOutline") {
	"""课程目录id"""
	id:String
	"""父级课程目录id"""
	parentId:String
	"""课程目录名称"""
	name:String
	"""排序"""
	sort:Int!
}
"""@description: 课程评价信息
	@author: sugs
	@create: 2022-05-30 09:57
"""
type CourseAppraisalInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.course.courseappraise.CourseAppraisalInfo") {
	"""学员课程评价编号"""
	studentCourseAppraisalId:String
	"""评价内容"""
	content:String
	"""课程评价值"""
	courseAppraise:BigDecimal
	"""教师评价值"""
	teacherAppraise:BigDecimal
	"""评价用户id"""
	appraisalUserId:String
	"""评价时间"""
	appraisalTime:DateTime
}
"""课程包下课程信息
	<AUTHOR>
	@version 1.0
	@date 2022/2/9 20:16
"""
type CourseInPackageInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.course.packages.CourseInPackageInfo") {
	"""课程排序"""
	sort:Int
	"""选课学时"""
	period:Double
}
"""课程学习大纲下课程信息
	<AUTHOR>
	@version 1.0
	@date 2022/2/9 19:59
"""
type CourseOfCourseTrainingOutlineInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.course.training.outline.CourseOfCourseTrainingOutlineInfo") {
	"""课程学习大纲课程id"""
	courseOfCourseTrainingOutlineId:String
	"""课程学习大纲id"""
	outlineId:String
	"""大纲内课程序号"""
	sort:Int
	"""选课学时"""
	period:Double
	"""大纲内课程类型(1：必修 2：选修 3：兴趣课)
		@see CourseTypes
	"""
	courseType:Int!
}
"""课程学习大纲信息
	<AUTHOR>
	@version 1.0
	@date 2022/2/10 9:55
"""
type CourseTrainingOutlineInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.course.training.outline.CourseTrainingOutlineInfo") {
	"""课程学习大纲Id"""
	outlineId:String
	"""课程学习大纲名称"""
	name:String
	"""所属场景id"""
	sceneId:String
	"""课程学习大纲上级id"""
	parentId:String
	"""课程学习大纲序号"""
	sort:Int
	"""大纲类型 1.必修 2.选修
		@see CourseTrainingCategorys
	"""
	category:Int
	"""创建时间"""
	createdTime:DateTime
	"""最后更新时间"""
	lastUpdatedTime:DateTime
}
type SchemeInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.learningexperience.SchemeInfo") {
	"""方案id"""
	schemeId:String
	"""学习方式id"""
	learningId:String
}
"""@version: 1.0
	@description: 教学计划参数
	@author: sugs
	@create: 2024-11-10 17:31
"""
type PlanItemResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.plan.PlanItemResponse") {
	"""教学计划项id"""
	id:String
	"""教学计划id"""
	planId:String
	"""教学计划组id"""
	planItemGroupId:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位id"""
	unitId:String
	"""所属服务商id"""
	servicerId:String
	"""平台租户id"""
	tenantId:String
	"""教学计划项名称"""
	name:String
	"""划教学模式"""
	planMode:Int
	"""计划项类型
		@see PlanItemTypes
	"""
	planItemType:Int
	"""学时"""
	period:Double
	"""教学开始时间"""
	startTime:DateTime
	"""教学结束时间"""
	endTime:DateTime
	"""简介"""
	abouts:String
	"""时长"""
	timeLength:Int
	"""教师"""
	teachers:[PlanTeacherResponse]
}
"""@version: 1.0
	@description: 教学计划教师
	@author: sugs
	@create: 2024-11-15 13:47
"""
type PlanTeacherResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.plan.PlanTeacherResponse") {
	"""教师id"""
	id:String
	"""教师名称"""
	teacherName:String
	"""教师类型"""
	teacherType:Int
	"""性质"""
	nature:Int
	"""教师职称"""
	teacherTitle:String
	"""教师所在单位"""
	unitName:String
}
"""@version: 1.0
	@description: 教学计划签到点信息
	@author: sugs
	@create: 2024-11-10 17:24
"""
type SignInPointResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.plan.SignInPointResponse") {
	"""签到点id"""
	id:String
	"""教学计划id"""
	planId:String
	"""教学计划项id"""
	planItemId:String
	"""学习方案id"""
	schemeId:String
	"""学习方式id"""
	learningId:String
	"""期别id"""
	issueId:String
	"""签到点规则id"""
	pointRuleId:String
	"""签到开始时间"""
	startTime:DateTime
	"""签到结束时间"""
	endTime:DateTime
	"""地点坐标经度"""
	longitude:Double
	"""地点坐标维度"""
	latitude:Double
	"""有效半径"""
	diameter:Double
	"""教学计划签到点状态
		1.正常 2.作废
		@see PlanSignInPointStatus
	"""
	status:Int
	"""状态描述"""
	statusRemark:String
	"""签到类型
		1.签到 2.签退
		@see PlanSignTypes
	"""
	signType:Int
}
"""@version;1.0
	@description;签到记录
	@author;sugs
	@create;2024-11-10 17:31
"""
type SignInRecordResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.plan.SignInRecordResponse") {
	"""记录ID"""
	id:String
	"""平台租户id"""
	tenantId:String
	"""教学计划ID"""
	planId:String
	"""教学计划项组ID"""
	planItemGroupId:String
	"""教学计划项ID"""
	planItemId:String
	"""学习方案id（冗余，当教学计划来自方案\期别时）"""
	schemeId:String
	"""学习方式id（冗余，当教学计划来自方案\期别时）"""
	learningId:String
	"""期别ID（冗余，当教学计划来自期别时）"""
	issueId:String
	"""学号"""
	studentNo:String
	"""参训资格ID"""
	qualificationId:String
	"""用户ID"""
	userId:String
	"""签到时间"""
	signTime:DateTime
	"""地点坐标经度"""
	longitude:Double
	"""地点坐标纬度"""
	latitude:Double
	"""签到Key（签到点id）"""
	pointKey:String
	"""签到类型 (1:签到, 2:签退)
		@see com.fjhb.domain.teachingplan.api.sign.consts.attendance.PlanAttendanceType
		attendanceType
	"""
	signType:Int
	"""签到结果代码
		正常考勤 200
		重复考勤 300
		过早   301
		过晚   302
		不在地点范围   400
		系统异常 500
		@see com.fjhb.domain.teachingplan.api.sign.consts.attendance.PlanAttendanceResultCodes
		attendanceResultCode
	"""
	signResultCode:Int
	"""签到模式
		签到 1
		签退 2
		@see com.fjhb.domain.teachingplan.api.sign.consts.attendance.PlanAttendanceType
		attendanceSourceType
	"""
	signMode:Int
	"""签到来源类型（1：学员签到、2：审批）"""
	signSourceType:Int
	"""签到来源ID（学员id，审批单id？）"""
	signSourceId:String
}
"""课件学习信息"""
type CoursewareLearningRecordInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.studentcourse.CoursewareLearningRecordInfo") {
	"""学员课程学习记录id"""
	courseLearningRecordId:String
	"""学习状态 0/1/2，未学习/学习中/学习完成
		@see StudyStatus
	"""
	status:Int
	"""课件学习进度百分比"""
	schedule:Double
	"""开始学习时间"""
	startLearningTime:DateTime
	"""最后一次学习时间"""
	lastLearningTime:DateTime
	"""学习完成时间"""
	completedTime:DateTime
}
"""学员课程信息
	<AUTHOR>
	@version 1.0
	@date 2022/2/9 19:07
"""
type StudentCourse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.studentcourse.StudentCourse") {
	"""学员课程id"""
	studentCourseId:String
	"""学员课程状态(0：失效 1：有效)
		@see StudentCourseStatus
	"""
	studentCourseStatus:Int!
	"""学员课程学习状态（0：未评定 1：未合格 2：合格）
		@see StudentCourseLearningStatus
	"""
	courseLearningStatus:Int!
	"""选课时间"""
	selectCourseTime:DateTime
	"""课程学习取得结果时间"""
	learningResultTime:DateTime
}
"""学员课程已评价信息
	<AUTHOR>
	@version 1.0
	@date 2022/2/9 19:01
"""
type StudentCourseAppraised @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.studentcourse.StudentCourseAppraised") {
	"""学员是否已评价"""
	appraisalCourse:Boolean
	"""学员课程评价编号"""
	studentCourseAppraisalId:String
	"""评价时间"""
	appraisedTime:DateTime
}
"""@description: 学员课程信息
	@author: sugs
	@create: 2022-05-30 09:39
"""
type StudentCourseInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.studentcourse.StudentCourseInfo") {
	"""学员课程id"""
	studentCourseId:String
}
"""学员媒体课程学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/2/9 19:22
"""
type StudentCourseMediaLearningRecord @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.studentcourse.StudentCourseMediaLearningRecord") {
	"""课程学习记录id"""
	courseLearningRecordId:String
	"""课程媒体学习进度状态（0：未评定 1：未合格 2：合格）"""
	courseScheduleStatus:Int
	"""课程学习进度百分比"""
	schedule:Double
	"""开始学习时间"""
	startLearningTime:DateTime
	"""最后一次学习时间"""
	lastLearningTime:DateTime
	"""媒体课程学习取得结果时间"""
	learningResultTime:DateTime
}
"""课后测验
	<AUTHOR>
	@version 1.0
	@date 2022/2/9 19:28
"""
type StudentCourseQuiz @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.studentcourse.StudentCourseQuiz") {
	"""课后测验id"""
	courseQuizId:String
	"""课后测验学习状态（0：未评定 1：未合格 2：合格）"""
	courseQuizStatus:Int
	"""课后测验答卷id"""
	courseQuizAnswerId:String
	"""课后测验得分"""
	score:Double
	"""课后测验取得结果时间"""
	learningResultTime:DateTime
}
type DataManipulationResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.studentlearningexperience.DataManipulationResponse") {
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""删除时间"""
	deletedTime:DateTime
	"""是否删除"""
	isDeleted:Boolean
}
"""学习心得主题"""
type LearningExperienceTopicResponse1 @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.studentlearningexperience.LearningExperienceTopicResponse") {
	"""主题ID"""
	topicId:String
	"""心得主题名称"""
	experienceTopicName:String
	"""参与形式"""
	participateType:ParticipateType
	"""参加活动时间类型"""
	participateTimeType:ParticipateTimeType
	"""活动开始时间"""
	startTime:DateTime
	"""活动结束时间"""
	endTime:DateTime
	"""提交要求（根据参与形式不同，表征的意义不同：提交附件表示附件大小限制，在线编辑表示编辑字数限制）"""
	submitLimitNum:Double!
	"""是否必选"""
	isRequired:Boolean
	"""审核方式"""
	auditType:AuditType
	"""活动内容文本"""
	descriptionContent:String
	"""是否删除"""
	isDelete:Boolean
}
type StatusChangeResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.studentlearningexperience.StatusChangeResponse") {
	status:StudentLearningExperienceStatus
	changeTime:DateTime
}
type StudentSchemeLearningResponse @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.studentlearningexperience.StudentSchemeLearningResponse") {
	"""参训资格ID"""
	qualificationId:String
	"""学号"""
	studentNo:String
	"""方案id"""
	schemeId:String
	"""学习id"""
	learningId:String
}
enum AuditType @type(value:"com.fjhb.ms.course.learning.query.constants.AuditType") {
	AUTO_AUDIT
	MANUAL_AUDIT
}
enum ExperienceType @type(value:"com.fjhb.ms.course.learning.query.constants.ExperienceType") {
	SCHEME
	COURSE
}
enum ParticipateTimeType @type(value:"com.fjhb.ms.course.learning.query.constants.ParticipateTimeType") {
	SCHEME_TIME
	CUSTOME_TIME
}
enum ParticipateType @type(value:"com.fjhb.ms.course.learning.query.constants.ParticipateType") {
	SUBMIT_FILE
	EDIT_ONLINE
}
enum StudentLearningExperienceStatus @type(value:"com.fjhb.ms.course.learning.query.constants.StudentLearningExperienceStatus") {
	SUBMITING
	SUBMITTED
	PASS
	RETURNED
}
"""方案下课程排序枚举"""
enum CourseInSchemeSortEnum @type(value:"com.fjhb.ms.course.learning.query.kernel.service.enums.CourseInSchemeSortEnum") {
	"""学时"""
	PERIOD
	"""综合评分"""
	TOTAL_APPRAISE
	"""选课人数"""
	CHOOSE_COURSE_COUNT
	"""大纲id"""
	OUTLINE_ID
	"""课程类型"""
	COURSE_TYPE
	"""大纲内课程序号"""
	SORT
	"""配置规则排序"""
	COURSE_CONFIG_SORT
	"""创建时间"""
	CREATE_TIME
}
"""排序类型"""
enum SortTypeEnum1 @type(value:"com.fjhb.ms.course.learning.query.kernel.service.enums.SortTypeEnum") {
	"""升序"""
	ASC
	"""降序"""
	DESC
}
enum StudentLearningExperienceSortEnum @type(value:"com.fjhb.ms.course.learning.query.kernel.service.enums.StudentLearningExperienceSortEnum") {
	"""学员学习心得心得创建的时间"""
	CREATE_TIME
	"""心得参加时间"""
	START_TIME
	"""是否必选"""
	IS_REQUIRED
}
enum StudyStatusEnum @type(value:"com.fjhb.ms.study.query.enums.StudyStatusEnum") {
	UN_LEARNING
	LEARNING
	COMPLETED
}

scalar List
type CourseInSchemeV2ResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseInSchemeV2Response]}
type CourseInSchemeResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseInSchemeResponse]}
type CourseDistributionResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseDistributionResponse]}
type CourseResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseResponse]}
type CourseInPackageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseInPackageResponse]}
type StudentCourseLearningResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentCourseLearningResponse]}
type CourseTrainingOutlineResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseTrainingOutlineResponse]}
type CoursewareLearningRecordResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CoursewareLearningRecordResponse]}
type ExcellentCourseResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ExcellentCourseResponse]}
type StudentLearningExperienceResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentLearningExperienceResponse]}
type StudentLearningExperienceLastedResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentLearningExperienceLastedResponse]}
type PlanItemAttendanceResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PlanItemAttendanceResponse]}
type StudentCourseAppraiseResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentCourseAppraiseResponse]}
type StudentCourseAppraisedResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentCourseAppraisedResponse]}
type StudentCourseLearningV2ResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentCourseLearningV2Response]}
