import { ChangeEnterpriseAdminUnitInfo } from '@api/platform-gateway/platform-jxjypxtypt-account-v1'

export enum UnitStatusEnums {
  /**
   * 正常
   */
  normal = 1,
  /**
   * 冻结
   */
  freeze = 2
}

export enum PersonUnitRelationStatusEnums {
  /**
   * 冻结
   */
  freeze,
  /**
   * 正常
   */
  normal
}

export default class DistributionUnitInformation {
  /**
   * 单位ID
   */
  unitId = ''
  /**
   * 单位名称
   */
  name = ''
  /**
   * 单位状态 1正常,2冻结
   */
  unitStatus: UnitStatusEnums = UnitStatusEnums.normal
  /**
   * 应用方类型
   */
  applicationMemberType = 0
  /**
   * 应用方id
   */
  applicationMemberId = ''

  static toDistributionUnitInformation(dto: ChangeEnterpriseAdminUnitInfo) {
    const vo = new DistributionUnitInformation()
    vo.unitId = dto.unitId
    vo.name = dto.unitName
    vo.unitStatus = dto.unitStatus
    vo.applicationMemberType = dto.applicationMemberType
    vo.applicationMemberId = dto.applicationMemberId
    return vo
  }
}
