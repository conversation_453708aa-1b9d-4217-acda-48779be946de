<template>
  <!--大题弹窗配置-->
  <el-drawer
    title="添加大题"
    :visible.sync="randomConfigurationShow"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    size="800px"
    :close-on-click-modal="false"
    @close="whenClose"
  >
    <el-form
      ref="elForm"
      :model="currentConfigurationItem"
      :rules="rules"
      size="medium"
      label-width="140px"
      class="mt20"
    >
      <el-col :span="22">
        <el-form-item label="大题标题" prop="groupName">
          <el-input
            v-model="currentConfigurationItem.groupName"
            placeholder="请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。"
            clearable
            :style="{ width: '100%' }"
          ></el-input>
        </el-form-item>
      </el-col>
      <el-col :span="24">
        <el-form-item label="题型" prop="questionType">
          <!-- currentConfigurationItem.questionType -->
          <el-radio-group v-model="drawingQuestionType" size="medium">
            <!-- <el-radio
              v-for="(item, index) in questionTypeList"
              :key="index"
              :label="item.value"
              :disabled="item.disabled"
              >{{ item.title }}
            </el-radio> -->
            <el-radio :label="1">单选</el-radio>
            <el-radio :label="4">判断</el-radio>
            <el-radio :label="2">多选</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="24" v-show="rangeQuestion !== 2">
        <el-form-item label="抽题规则" prop="questionScopesTypes">
          <!-- currentConfigurationItem.questionScopesTypes -->
          <el-radio-group v-model="drawingQuestionRule" size="medium" @change="handleDrawingQuestionRule">
            <el-radio :label="1">智能抽题</el-radio>
            <el-radio :label="3">按照题库指定数量</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-col>
      <el-col :span="16">
        <el-form-item label="本大题总分" prop="totalScore">
          <el-input-number
            :min="0"
            controls-position="right"
            :precision="0"
            size="mini"
            v-model.number="currentConfigurationItem.totalScore"
            placeholder="请输入本大题总分"
            clearable
          >
          </el-input-number>
        </el-form-item>
      </el-col>
      <el-col :span="16">
        <el-form-item label="本大题题数" prop="questionCount">
          <el-input-number
            :min="0"
            size="mini"
            :max="totalEnableQuestionCount"
            controls-position="right"
            v-model.number="currentConfigurationItem.questionCount"
            placeholder="请输入本大题题数"
            clearable
          >
          </el-input-number>
        </el-form-item>
      </el-col>
      <div v-if="drawingQuestionRule === 3">
        <el-col :span="16" v-for="(item, index) in questionBankList" :key="index">
          <el-form-item :label="`${item.name}` + '题库:'" prop="questionBankList" :rules="rules.questionBankList">
            <el-input-number
              :min="0"
              size="mini"
              :max="item.enabledQuestions"
              controls-position="right"
              v-model="item.count"
              :placeholder="'请输入' + `${item.name}` + '抽题数'"
              class="input-num"
            />
          </el-form-item>
        </el-col>
      </div>

      <el-col :span="22">
        <el-form-item label="">
          <span class="f-co">注：试卷为智能组卷，配置的大题数量，需先核实是否有足够的试题满足抽取。</span>
        </el-form-item>
      </el-col>
      <el-col :span="22">
        <el-form-item class="dialog-footer">
          <el-button @click="cancelRandomConfigurationItem">取消</el-button>
          <el-button @click="commitRandomConfigurationItem" type="primary">保存</el-button>
        </el-form-item>
      </el-col>
    </el-form>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import { ElForm } from 'element-ui/types/form'
  import { cloneDeep } from 'lodash'
  import ExamPaperUIModule from '@/store/modules-ui/exam/ExamPaperUIModule'
  import QuestionExtract from '@api/service/management/resource/exam-paper/mutation/vo/common/QuestionExtract'
  import ExamLibrary from '@api/service/management/resource/exam-paper/mutation/vo/common/ExamLibrary'

  @Component
  export default class extends Vue {
    @Ref('elForm') elForm: ElForm
    /**
     *   1.增加答题  2、编辑大题
     */
    @Prop({ type: Number, default: 1 }) randomConfigurationOperationType: number
    /**
     *   当前编辑的试题类型
     */
    @Prop({ type: Number, default: 0 }) currentEditQuestionType: number
    /**
     *   弹窗是否展示
     */
    @Prop({ type: Boolean, default: false }) show: boolean
    /*
     * 出题范围
     */
    @Prop({ type: Number, default: 1 }) rangeQuestion: number

    @Watch('rangeQuestion', {
      immediate: true,
      deep: true
    })
    rangeQuestionChange(val: any) {
      if (val) {
        this.questionAccordingId = 2
      }
    }

    /**
     *  抽题规则
     */
    @Prop({ type: Number, default: 0 }) questionRules: number

    @Watch('questionRules', {
      deep: true,
      immediate: true
    })
    questionRulesChange(val: any) {
      if (val) {
        this.drawingQuestionRule = 1
      }
    }

    /**
     *  答题配置项
     */
    @Prop({
      type: Object,
      default: () => {
        return new QuestionExtract()
      }
    })
    currentItem: QuestionExtract

    /**
     *获取题库数据
     **/

    @Prop({
      type: Array,
      default: () => {
        return new Array<ExamLibrary>()
      }
    })
    examLibrary: Array<ExamLibrary>

    @Watch('examLibrary', {
      deep: true,
      immediate: true
    })
    examLibraryChange(val: any) {
      if (val) {
        this.questionBankList = val
        console.log(this.questionBankList, ' this.questionBankList')
      }
    }

    handleDrawingQuestionRule() {
      this.questionBankList.forEach(p => {
        this.everyQuestionCountList.push(p.enabledQuestions)
      })
    }

    everyQuestionCountList = new Array<number>()
    drawingQuestionRule = 1
    drawingQuestionType = 1
    questionBankList = new Array<ExamLibrary>()
    questionBankListIndex = 0
    questionAccordingId = 1
    /**
     *  获取系统提供的试题题型
     */
    questionTypeList = [
      {
        title: '单选',
        value: 1
      },
      {
        title: '多选',
        value: 2
      },
      {
        title: '判断',
        value: 4
      }
    ]

    currentConfigurationItem = new QuestionExtract()

    @Watch('currentConfigurationItem', {
      immediate: true,
      deep: true
    })
    currentConfigurationItemChange(val: any) {
      if (val) {
        if (val.questionScopesTypes == 3) {
          this.drawingQuestionRule = val.questionScopesTypes

          this.drawingQuestionType = val.questionType
          val.libraryMapQuestionNumSettings?.forEach((p: any, i: any) => {
            this.questionBankList[i].count = p.count
          })
        }
      }
    }

    rules = {
      groupName: [
        {
          required: true,
          message: '请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。',
          trigger: 'blur'
        }
      ],
      questionType: [
        {
          required: true,
          message: '请选择题型',
          trigger: 'blur'
        }
      ],
      questionScopesTypes: [
        {
          required: true,
          message: '抽题规则不能为空',
          trigger: 'change'
        }
      ],
      totalScore: [
        {
          required: true,
          message: '请输入本大题总分',
          trigger: 'blur'
        },
        {
          validator: async (rule: any, value: any, callback: (error?: Error) => void) => {
            if (!Number.isInteger(value)) {
              callback(new Error('请输入数字值'))
            } else {
              if (value <= 0) {
                callback(new Error('本大题总分必须大于0'))
              } else {
                callback()
              }
            }
          }
        }
      ],
      questionCount: [
        {
          required: true,
          message: '请输入本大题题数',
          trigger: 'blur'
        },
        {
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            this.heckAge(rule, value, callback)
          },
          trigger: 'blur'
        }
      ],

      questionBankList: [
        {
          validator: this.validateQuestionBankList,
          trigger: 'blur'
        }
      ]
    }
    @Watch('randomConfigurationOperationType', {
      deep: true,
      immediate: true
    })
    // randomConfigurationOperationTypeChange(val: any) {
    //   if (val) {
    //     val
    //     if (this.randomConfigurationOperationType == 1) {
    //       this.drawingQuestionRule = 1
    //       this.drawingQuestionType = 1
    //     }
    //   }
    // }
    @Watch('drawingQuestionRule')
    drawingQuestionRuleChange() {
      // this.currentConfigurationItem.questionCount = 0
      // this.questionBankList.forEach((exam: ExamLibrary) => {
      //   exam.count = 0
      // })
    }

    get totalCount() {
      let count = 0
      this.questionBankList.forEach((exam: ExamLibrary) => {
        count += exam.count || 0
      })
      return count
    }

    get totalEnableQuestionCount() {
      let enableCount = 0
      this.questionBankList.forEach((exam: ExamLibrary) => {
        enableCount += exam.enabledQuestions
      })
      return this.drawingQuestionRule === 1 ? undefined : enableCount
    }

    validateQuestionBankList(rule: any, value: any, callback: (error?: Error) => void) {
      if (this.totalCount !== this.currentConfigurationItem.questionCount) {
        return callback(new Error('各题库抽题的数量必须等于本大题题数'))
      }
      callback()
    }

    // 校验
    heckAge(rule: any, value: number, callback: (error?: Error) => void) {
      {
        if (!Number.isInteger(value)) {
          callback(new Error('请输入数字值'))
        } else if (value < 1) {
          callback(new Error('该大题至少有 1 题'))
        } else {
          const singleScore = this.currentConfigurationItem.totalScore % value

          // 0.5分
          const score = this.currentConfigurationItem.totalScore / value
          const index = String(score).indexOf('.')
          const str = String(score).slice(index)

          if (singleScore !== 0 && value) {
            //有余数，判断是否为 0.5 分；比如 10.5, 5.5此类分值
            if (str == '.5' && str.length === 2) {
              callback()
            } else {
              callback(new Error('大题总分/大题总数需能除得尽'))
            }
          } else {
            callback()
          }
        }
      }
    }

    /**
     *  获取答题弹窗是否显示
     */
    get randomConfigurationShow() {
      return this.show
    }

    set randomConfigurationShow(val: boolean) {
      this.$emit('update:show', val)
    }

    /**
     *  获取答题配置项
     */
    @Watch('currentItem', { immediate: true, deep: true })
    setItem(obj: QuestionExtract) {
      this.currentConfigurationItem = cloneDeep(obj)
    }

    /**
     * 保存大题配置项
     */
    commitRandomConfigurationItem() {
      this.currentConfigurationItem.questionScopesTypes = this.drawingQuestionRule
      this.currentConfigurationItem.questionType = this.drawingQuestionType
      if (this.questionBankList) {
        this.currentConfigurationItem.libraryMapQuestionNumSettings = this.questionBankList
        const p = this.currentConfigurationItem.libraryMapQuestionNumSettings
        p.forEach(item => {
          item.count = Number(item.count)
        })
      }
      // 增加答题还是修改
      this.elForm.validate(val => {
        if (val) {
          if (this.randomConfigurationOperationType === 1) {
            console.log(this.currentConfigurationItem, 'newCreate')
            this.$emit('questionConfigItem', this.currentConfigurationItem)
          } else {
            this.currentConfigurationItem.eachQuestionScore =
              this.currentConfigurationItem.totalScore / this.currentConfigurationItem.questionCount
            console.log(this.currentConfigurationItem, 'modifyCreate')
            this.$emit('questionConfigItem', this.currentConfigurationItem)
          }
          this.randomConfigurationShow = false
        }
      })
    }

    /**
     * 取消大题配置项配置
     */
    cancelRandomConfigurationItem() {
      this.randomConfigurationShow = false
    }

    whenClose() {
      this.elForm.clearValidate()
      this.drawingQuestionRule = 1
      this.drawingQuestionType = 1
    }
  }
</script>
