import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 换班状态枚举
 * apply_exchange 发起换班
 * drop_processing 退班处理中
 * drop_fail 退班失败
 * apply_delivery 申请发货
 * delivery_processing 发货处理中
 * complete 换班成功
 */
export enum ExchangeSchemeStatusEnum {
  apply_exchange = 'apply',
  drop_processing = 'drop_processing',
  drop_fail = 'drop_fail',
  apply_delivery = 'apply_delivery',
  delivery_processing = 'delivery_processing',
  complete = 'complete'
}

/**
 * @description 换班状态
 */
class ExchangeSchemeStatus extends AbstractEnum<ExchangeSchemeStatusEnum> {
  static enum = ExchangeSchemeStatusEnum

  constructor(status?: ExchangeSchemeStatusEnum) {
    super()
    this.current = status
    this.map.set(ExchangeSchemeStatusEnum.apply_exchange, '发起换班')
    this.map.set(ExchangeSchemeStatusEnum.drop_processing, '退班处理中')
    this.map.set(ExchangeSchemeStatusEnum.drop_fail, '退班失败')
    this.map.set(ExchangeSchemeStatusEnum.apply_delivery, '申请发货')
    this.map.set(ExchangeSchemeStatusEnum.delivery_processing, '发货处理中')
    this.map.set(ExchangeSchemeStatusEnum.complete, '换班成功')
  }
}

export default new ExchangeSchemeStatus()
