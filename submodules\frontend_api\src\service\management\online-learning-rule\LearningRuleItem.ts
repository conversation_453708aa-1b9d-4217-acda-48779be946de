import BasicInfo from '@api/service/management/online-learning-rule/model/BasicInfo'
import RuleInfo from '@api/service/management/online-learning-rule/model/RuleInfo'
import existingProperty from '@api/service/management/online-learning-rule/model/existingProperty'
import { Page, Response, ResponseStatus } from '@hbfe/common'
import { cloneDeep } from 'lodash'
// import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import LearningConstraint, {
  CommonResponse,
  SpecifySchemeRuleDto,
  StudyConstraintRuleCreateRequest,
  StudyConstraintRuleStatusRequest,
  StudyConstraintRuleUpdateRequest,
  SuitIndustryRangeDto
} from '@api/ms-gateway/ms-learning-constraint-v1'
import IndustryEnum, { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import CourseLearningBackstage, {
  StudentLearningRuleResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import QueryCategoryInfo from '@api/service/common/basic-data-dictionary/query/QueryCategoryInfo'
import { TimeModeEnum } from '@api/service/management/online-learning-rule/enum/TimeModeEnum'
import { BusinessDataDictionaryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import SchemeSpecialRuleItem from '@api/service/management/online-learning-rule/model/SchemeSpecialRuleItem'
import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
import RuleSchemeParams from '@api/service/management/train-class/query/vo/RuleSchemeParams'
import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import QueryPropertyDetail, {
  BatchQueryPropertyRequest
} from '@api/service/common/basic-data-dictionary/query/QueryPropertyDetail'
import QueryYear from '@api/service/common/basic-data-dictionary/query/QueryYear'
export default class LearnRuleItem {
  id = ''
  /**
   *基础设置
   */
  basicInfo: BasicInfo = new BasicInfo()
  /**
   * 规则设置
   */
  ruleInfo: RuleInfo = new RuleInfo()
  /**
   * 状态启停用
   */
  enable = false
  /**
   * 更新时间
   */
  updateTime = ''

  /**
   * 生效时间
   */
  effectiveTime = ''
  /**
   * 操作人id
   */
  createUserId = ''
  /**
   * 操作人
   */
  operator = ''
  /**
   * 比对自身
   */
  private compareData: LearnRuleItem = null
  /**
   * 重复数组
   */
  duplicateList: Array<string> = []

  // 培训属性
  trainingAttribute = [
    {
      type: 'subjectType',
      name: '科目类型'
    },
    {
      type: 'trainingCategory',
      name: '培训类别'
    },
    {
      type: 'trainingObject',
      name: '培训对象'
    },
    {
      type: 'positionCategory',
      name: '岗位类别'
    },
    {
      type: 'jobLevel',
      name: '技术等级'
    },
    {
      type: 'learningPhase',
      name: '学段'
    },
    {
      type: 'discipline',
      name: '学科'
    },
    {
      type: 'certificatesType',
      name: '证书类型'
    },
    {
      type: 'practitionerCategory',
      name: '执业类别'
    },
    {
      type: 'trainingProfessional',
      name: '培训专业'
    }
  ]
  existingPropertyList: existingProperty[] = []
  itemValueListres: Array<BusinessDataDictionaryResponse> = []
  /**
   * 获取详情
   * @param id
   */
  async getDetail(id: string) {
    const res = await CourseLearningBackstage.getStudentLearningRule(id)
    if (res.status.isSuccess()) {
      Object.assign(this, await LearnRuleItem.fromDetail(res.data))
    }
    console.log(this, 'detail')

    return res.status
  }

  static from(dto: StudentLearningRuleResponse) {
    const vo = new LearnRuleItem()
    vo.id = dto.ruleId
    vo.updateTime = dto.updatedTime
    vo.createUserId = dto.operatorId
    vo.effectiveTime = dto.effectTime
    // 1启用 2停用
    vo.enable = dto.status == 1 ? true : false
    if (dto.ruleType == 1) {
      vo.basicInfo.timeMode = TimeModeEnum.physical
      vo.basicInfo.everyDayLearningTime = Math.floor(dto.maxStudyTimeLength / 60)
    } else if (dto.ruleType == 2) {
      vo.basicInfo.timeMode = TimeModeEnum.learning
      vo.basicInfo.everyDayLearningHours = dto.maxStudyTimeLength
    }
    const industryNameList = new Array<string>()
    vo.basicInfo.industryIdList = dto.suitIndustryRangeList?.map((ite) => {
      if (ite.industryId == IndustryIdEnum.RS) {
        vo.basicInfo.RSProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.RSProperty.setData(ite, 'subjectType', ite.industryId)
        vo.basicInfo.RSProperty.setData(ite, 'trainingProfessional', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.RS))
      } else if (ite.industryId == IndustryIdEnum.JS) {
        vo.basicInfo.JSProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.JSProperty.setData(ite, 'subjectType', ite.industryId)
        vo.basicInfo.JSProperty.setData(ite, 'trainingCategory', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.JS))
      } else if (ite.industryId == IndustryIdEnum.WS) {
        vo.basicInfo.WSProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.WSProperty.setData(ite, 'trainingCategory', ite.industryId)
        vo.basicInfo.WSProperty.setData(ite, 'trainingObject', ite.industryId)
        vo.basicInfo.WSProperty.setData(ite, 'positionCategory', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.WS))
      } else if (ite.industryId == IndustryIdEnum.GQ) {
        vo.basicInfo.GQProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.GQProperty.setData(ite, 'jobLevel', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.GQ))
      } else if (ite.industryId == IndustryIdEnum.LS) {
        vo.basicInfo.LSProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.LSProperty.setData(ite, 'discipline', ite.industryId)
        vo.basicInfo.LSProperty.setData(ite, 'learningPhase', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.LS))
      } else if (ite.industryId == IndustryIdEnum.YS) {
        vo.basicInfo.YSProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.YSProperty.setData(ite, 'subjectType', ite.industryId)
        // vo.basicInfo.YSProperty.setData(ite, 'certificatesType', ite.industryId)
        // vo.basicInfo.YSProperty.setData(ite, 'practitionerCategory', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.YS))
      }
      return ite.industryId
    })
    industryNameList.map((item, index) => {
      vo.basicInfo.industryName += item + (index != industryNameList.length - 1 ? '、' : '')
    })
    return vo
  }

  static async fromDetail(dto: StudentLearningRuleResponse) {
    const vo = new LearnRuleItem()
    vo.compareData = new LearnRuleItem()
    vo.compareData.ruleInfo = cloneDeep(vo.ruleInfo)
    vo.compareData.basicInfo = cloneDeep(vo.basicInfo)
    vo.id = dto.ruleId
    vo.updateTime = dto.updatedTime
    vo.createUserId = dto.operatorId
    // 1启用 2停用
    vo.enable = dto.status == 1 ? true : false
    if (dto.ruleType == 1) {
      vo.basicInfo.timeMode = TimeModeEnum.physical
      vo.basicInfo.everyDayLearningTime = Math.floor(dto.maxStudyTimeLength / 60)
    } else if (dto.ruleType == 2) {
      vo.basicInfo.timeMode = TimeModeEnum.learning
      vo.basicInfo.everyDayLearningHours = dto.maxStudyTimeLength
    }
    const industryNameList = new Array<string>()
    vo.basicInfo.industryIdList = dto.suitIndustryRangeList?.map((ite) => {
      if (ite.industryId == IndustryIdEnum.RS) {
        vo.basicInfo.RSProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.RSProperty.setData(ite, 'subjectType', ite.industryId)
        vo.basicInfo.RSProperty.setData(ite, 'trainingProfessional', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.RS))
      } else if (ite.industryId == IndustryIdEnum.JS) {
        vo.basicInfo.JSProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.JSProperty.setData(ite, 'subjectType', ite.industryId)
        vo.basicInfo.JSProperty.setData(ite, 'trainingCategory', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.JS))
      } else if (ite.industryId == IndustryIdEnum.WS) {
        vo.basicInfo.WSProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.WSProperty.setData(ite, 'trainingCategory', ite.industryId)
        vo.basicInfo.WSProperty.setData(ite, 'trainingObject', ite.industryId)
        vo.basicInfo.WSProperty.setData(ite, 'positionCategory', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.WS))
      } else if (ite.industryId == IndustryIdEnum.GQ) {
        vo.basicInfo.GQProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.GQProperty.setData(ite, 'jobLevel', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.GQ))
      } else if (ite.industryId == IndustryIdEnum.LS) {
        vo.basicInfo.LSProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.LSProperty.setData(ite, 'discipline', ite.industryId)
        vo.basicInfo.LSProperty.setData(ite, 'learningPhase', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.LS))
      } else if (ite.industryId == IndustryIdEnum.YS) {
        vo.basicInfo.YSProperty.setData(ite, 'year', ite.industryId)
        vo.basicInfo.YSProperty.setData(ite, 'subjectType', ite.industryId)
        // vo.basicInfo.YSProperty.setData(ite, 'certificatesType', ite.industryId)
        // vo.basicInfo.YSProperty.setData(ite, 'practitionerCategory', ite.industryId)
        industryNameList.push(new IndustryEnum().map.get(IndustryIdEnum.YS))
      }
      vo.ruleInfo.schemeIds = dto.excludeSchemeIds
      vo.ruleInfo.initSchemeIds = dto.excludeSchemeIds ? [...dto.excludeSchemeIds] : []

      return ite.industryId
    })
    industryNameList.map((item, index) => {
      vo.basicInfo.industryName += item + (index != industryNameList.length - 1 ? '、' : '')
    })
    let schemeIdList = new Array<string>()
    ;(dto.specialSchemeRuleList || []).map((item) => {
      schemeIdList = schemeIdList.concat(item.schemeIdList)
    })
    const QueryTrainClassCommodityListObj = new QueryTrainClassCommodityList()
    const params = new RuleSchemeParams()
    // 去重
    params.schemeIds = schemeIdList?.filter((item, index) => schemeIdList.indexOf(item) === index) || []
    const schemeMap = new Map<string, RuleSchemeItem>()
    let schemeList = new Array<RuleSchemeItem>()
    if (params.schemeIds?.length <= 200) {
      schemeList = await QueryTrainClassCommodityListObj.pageRuleSchemeList(new Page(1, 200), params)
    } else {
      const fetchQuestionList = async (ids: string[]) => {
        const list = await QueryTrainClassCommodityListObj.pageRuleSchemeList(
          new Page(1, params.schemeIds?.length),
          params
        )
        if (schemeList?.length) {
          schemeList.push(...list)
        }
      }

      for (let i = 0; i < params.schemeIds?.length; i += 200) {
        const chunk = params.schemeIds.slice(i, i + 200)
        await fetchQuestionList(chunk)
      }
    }
    schemeList.map((item) => {
      schemeMap.set(item.schemeId, item)
    })
    vo.ruleInfo.schemeSpecialRuleList = (dto.specialSchemeRuleList || []).map((item) => {
      const temp = new SchemeSpecialRuleItem()
      temp.id = item.specifySchemeRuleId
      temp.schemeList = item.schemeIdList.map((ite) => {
        return schemeMap.get(ite)
      })
      temp.timeMode = item.ruleType
      if (temp.timeMode == TimeModeEnum.physical) {
        temp.everyDayLearningTime = Math.floor(item.maxStudyTimeLength / 60)
      } else {
        temp.everyDayLearningHours = item.maxStudyTimeLength
      }
      return temp
    })
    vo.ruleInfo.compareSpecialRuleList = vo.ruleInfo.schemeSpecialRuleList.length
      ? cloneDeep(vo.ruleInfo.schemeSpecialRuleList)
      : []
    vo.ruleInfo.removeRuleIds = vo.ruleInfo.schemeSpecialRuleList.map((item) => item.id)
    return vo
  }

  /**
   * 启停用规则
   */
  async changeStatus() {
    let res = new Response<CommonResponse>() || new Response<void>()
    const params = new StudyConstraintRuleStatusRequest()
    params.ruleId = this.id
    if (this.enable) {
      // 停用
      res = await LearningConstraint.disableStudyConstraintRule(params)
    } else {
      // 启用
      res = await LearningConstraint.enableStudyConstraintRule(params)
    }
    if (res.status.isSuccess() && res.data) {
      return new ResponseStatus(Number(res.data.code), res.data.errMsg)
    } else {
      return res.status
    }
  }

  /**
   * 保存学习规则
   */
  async save() {
    const request = LearnRuleItem.to(this)
    this.duplicateList = []
    this.existingPropertyList = []
    const res = await LearningConstraint.createStudyConstraintRule(request)
    if (res.status.isSuccess() && res.data) {
      const code = res.data.code
      const data = JSON.parse(res.data.data)
      let errMsg = res.data.errMsg
      errMsg = await this.filterCode(code, data, errMsg)
      return new ResponseStatus(Number(code), errMsg)
    }
    return res.status
  }
  /**
   * 更新学习规则
   */
  async update() {
    const request = LearnRuleItem.toUpdate(this)
    // return
    this.duplicateList = []
    this.existingPropertyList = []
    const res = await LearningConstraint.updateStudyConstraintRule(request)
    if (res.status.isSuccess() && res.data) {
      const code = res.data.code
      const data = JSON.parse(res.data.data)
      let errMsg = res.data.errMsg
      errMsg = await this.filterCode(code, data, errMsg)
      console.log(errMsg, 'errMsg lzh')

      return new ResponseStatus(Number(code), errMsg)
    }
    return res.status
  }
  static to(vo: LearnRuleItem) {
    const dto = new StudyConstraintRuleCreateRequest()
    dto.addNotSuitSchemeList = vo.ruleInfo.schemeIds.map((item) => {
      return {
        schemeId: item
      }
    })
    dto.suitIndustryRangeList = new Array<SuitIndustryRangeDto>()
    vo.basicInfo.industryIdList.map((item) => {
      let range: any
      if (item == IndustryIdEnum.RS) {
        range = vo.basicInfo.RSProperty
        if (vo.basicInfo.RSProperty.trainingProfessional && !vo.basicInfo.RSProperty.trainingProfessional.length) {
          delete range.trainingProfessional
        }
      }
      if (item == IndustryIdEnum.JS) range = vo.basicInfo.JSProperty
      if (item == IndustryIdEnum.WS) range = vo.basicInfo.WSProperty
      if (item == IndustryIdEnum.GQ) range = vo.basicInfo.GQProperty
      if (item == IndustryIdEnum.LS) {
        if (!vo.basicInfo.LSProperty.discipline.length) vo.basicInfo.LSProperty.discipline = ['-1']
        if (!vo.basicInfo.LSProperty.learningPhase.length) vo.basicInfo.LSProperty.learningPhase = ['-1']
        range = vo.basicInfo.LSProperty
      }
      if (item == IndustryIdEnum.YS) range = vo.basicInfo.YSProperty
      delete range['yearName']
      delete range['subjectTypeName']
      delete range['trainingCategoryName']
      delete range['trainingObjectName']
      delete range['disciplineName']
      delete range['positionCategoryName']
      delete range['jobLevelName']
      delete range['learningPhaseName']
      delete range['certificatesTypeName']
      delete range['practitionerCategoryName']
      delete range['trainingProfessionalName']

      dto.suitIndustryRangeList?.push({
        industryId: item,
        additionalRange: range
      })
    })
    dto.timeLengthLimitValue = 1
    dto.timeLengthLimitWay = 0
    if (vo.basicInfo.timeMode === TimeModeEnum.physical) {
      dto.ruleType = 1
      dto.maxStudyTimeLength = vo.basicInfo.everyDayLearningTime * 60
    } else if (vo.basicInfo.timeMode === TimeModeEnum.learning) {
      dto.ruleType = 2
      dto.maxStudyTimeLength = vo.basicInfo.everyDayLearningHours
    }
    dto.specifySchemeRuleList = vo.ruleInfo.schemeSpecialRuleList.map((item) => {
      const temp = new SpecifySchemeRuleDto()
      temp.schemeIdList = item.schemeList.map((scheme) => {
        return scheme.schemeId
      })
      if (item.timeMode === TimeModeEnum.physical) {
        temp.ruleType = 1
        temp.maxStudyTimeLength = item.everyDayLearningTime * 60
      } else if (item.timeMode === TimeModeEnum.learning) {
        temp.ruleType = 2
        temp.maxStudyTimeLength = item.everyDayLearningHours
      }
      temp.timeLengthLimitValue = 1
      temp.timeLengthLimitWay = 0
      return temp
    })
    return dto
  }

  static toUpdate(vo: LearnRuleItem) {
    console.log(vo, 'vo lzh update')

    const dto = new StudyConstraintRuleUpdateRequest()
    dto.ruleId = vo.id
    dto.addNotSuitSchemeList = vo.ruleInfo.schemeIds.map((item) => {
      return {
        schemeId: item
      }
    })
    dto.suitIndustryRangeList = new Array<SuitIndustryRangeDto>()
    vo.basicInfo.industryIdList.map((item) => {
      let range: any
      if (item == IndustryIdEnum.RS) {
        range = vo.basicInfo.RSProperty
        if (vo.basicInfo.RSProperty.trainingProfessional && !vo.basicInfo.RSProperty.trainingProfessional.length) {
          delete range.trainingProfessional
        }
      }
      if (item == IndustryIdEnum.JS) range = vo.basicInfo.JSProperty
      if (item == IndustryIdEnum.WS) range = vo.basicInfo.WSProperty
      if (item == IndustryIdEnum.GQ) range = vo.basicInfo.GQProperty
      if (item == IndustryIdEnum.LS) {
        if (!vo.basicInfo.LSProperty.discipline.length) vo.basicInfo.LSProperty.discipline = ['-1']
        if (!vo.basicInfo.LSProperty.learningPhase.length) vo.basicInfo.LSProperty.learningPhase = ['-1']
        range = vo.basicInfo.LSProperty
      }
      if (item == IndustryIdEnum.YS) range = vo.basicInfo.YSProperty
      delete range['yearName']
      delete range['subjectTypeName']
      delete range['trainingCategoryName']
      delete range['trainingObjectName']
      delete range['disciplineName']
      delete range['positionCategoryName']
      delete range['jobLevelName']
      delete range['learningPhaseName']
      delete range['certificatesTypeName']
      delete range['practitionerCategoryName']
      delete range['trainingProfessionalName']
      dto.suitIndustryRangeList?.push({
        industryId: item,
        additionalRange: range
      })
    })
    dto.timeLengthLimitValue = 1
    dto.timeLengthLimitWay = 0
    dto.removeSchemeIds = vo.ruleInfo.initSchemeIds.filter((item) => !vo.ruleInfo.schemeIds.includes(item))
    if (vo.basicInfo.timeMode === TimeModeEnum.physical) {
      dto.ruleType = 1
      dto.maxStudyTimeLength = vo.basicInfo.everyDayLearningTime * 60
    } else if (vo.basicInfo.timeMode === TimeModeEnum.learning) {
      dto.ruleType = 2
      dto.maxStudyTimeLength = vo.basicInfo.everyDayLearningHours
    }
    dto.addSpecifySchemeRuleList =
      vo.ruleInfo.schemeSpecialRuleList
        .filter((item) => {
          return !vo.ruleInfo.compareSpecialRuleList
            .map((ite) => {
              return ite.id
            })
            .includes(item.id)
        })
        .map((item) => {
          return SchemeSpecialRuleItem.to(item, 'add')
        }) || []
    // 尝试比对筛选修改的数组
    const updateList = new Array<SpecifySchemeRuleDto>()
    vo.ruleInfo.schemeSpecialRuleList.map((item) => {
      vo.ruleInfo.compareSpecialRuleList.map((citem) => {
        const differentProps =
          item.id == citem.id
            ? this.findDifferentProperties(item, citem, [
                'schemeList',
                'everyDayLearningTime',
                'everyDayLearningHours',
                'timeMode'
              ])
            : []
        console.log(differentProps, 'differentProps lzh')
        // 数值比对不出来，只能直接写了
        if (
          differentProps.length ||
          (item.id == citem.id &&
            (item.timeMode != citem.timeMode ||
              item.everyDayLearningHours != citem.everyDayLearningHours ||
              item.everyDayLearningTime != citem.everyDayLearningTime ||
              item.schemeList?.length != citem.schemeList?.length))
        ) {
          updateList.push(SchemeSpecialRuleItem.to(item))
        }
      })
    })
    dto.updateSpecifySchemeRuleList = updateList.filter((item, index) => updateList.indexOf(item) === index) || []
    // .map(item => {
    //   return SchemeSpecialRuleItem.to(item)
    // }) || []
    const ruleIds = vo.ruleInfo.schemeSpecialRuleList.map((item) => {
      return item.id
    })
    dto.removeSpecifySchemeRuleList = vo.ruleInfo.removeRuleIds.filter((item) => !ruleIds.includes(item))
    return dto
  }
  static findDifferentProperties<T>(curr: T, prev: T, filterKey: string[] = []): string[] {
    const differentProps: string[] = []

    const recursiveCompare = (curr: T, prev: T, path = '') => {
      Object.keys(curr).forEach((key) => {
        if (!Object.prototype.hasOwnProperty.call(prev, key)) {
          differentProps.push(path ? `${path}.${key}` : key)
        } else {
          const value1 = curr[key]
          const value2 = prev[key]

          if (typeof value1 !== 'object' || value1 === null || value2 === null) {
            if (value1 !== value2 && !filterKey.includes(key)) {
              differentProps.push(path ? `${path}.${key}` : key)
            }
          } else {
            recursiveCompare(value1, value2, path ? `${path}.${key}` : key)
          }
        }
      })
    }
    recursiveCompare(curr, prev)
    return differentProps
  }

  /**
   * 拼接错误信息
   */
  async doErrMsg(msgDataList: any) {
    this.existingPropertyList = []
    const itemValueList = new Array<string>()
    await Promise.all(
      (msgDataList || []).map((mitem: any) => {
        mitem.conflictAttributes.map((m: any) => {
          if (m.type == 'year') {
            return
          }
          const itemValue = m.value.includes('-1') ? m.conflictValue : m.value
          if (itemValue != '-1') {
            itemValue.map((s: string) => {
              if (!itemValueList.includes(s)) {
                itemValueList.push(s)
              }
            })
          }
        })
      })
    )
    this.itemValueListres = await QueryCategoryInfo.queryCategoryInfoByIds(itemValueList)
    ;(msgDataList || []).map((msgData: any) => {
      if (!msgData?.conflictAttributes.length) {
        return
      }
      const yearList: any =
        (msgData.conflictAttributes[0].value.includes('-1')
          ? msgData.conflictAttributes[0].conflictValue
          : msgData.conflictAttributes[0].value) || []

      msgData.conflictAttributes.shift()
      let list1: string[] = []
      let list2: string[] = []
      let list3: string[] = []
      // let list4: string[] = []
      const results: string[] = []
      msgData.conflictAttributes.map((item: any, index: number) => {
        switch (index) {
          case 0:
            list1 = (item.value.includes('-1') ? item.conflictValue : item.value) as string[]
            list1 = this.getPropertyKeyName(list1, item)
            break
          case 1:
            list2 = (item.value.includes('-1') ? item.conflictValue : item.value) as string[]
            list2 = this.getPropertyKeyName(list2, item)
            break
          case 2:
            list3 = (item.value.includes('-1') ? item.conflictValue : item.value) as string[]
            list3 = this.getPropertyKeyName(list3, item)
            break
          // case 3:
          //   list4 = (item.value.includes('-1') ? item.conflictValue : item.value) as string[]
          //   list4 = this.getPropertyKeyName(list4, item)
          //   break
          default:
            break
        }
      })
      if (!list2.length && !list3.length) {
        for (let i = 0; i < list1.length; i++) {
          results.push(list1[i])
        }
      } else if (!list3.length) {
        for (let i = 0; i < list1.length; i++) {
          for (let j = 0; j < list2.length; j++) {
            results.push(list1[i] + '、' + list2[j])
          }
        }
      } else {
        for (let i = 0; i < list1.length; i++) {
          for (let j = 0; j < list2.length; j++) {
            for (let k = 0; k < list3.length; k++) {
              results.push(list1[i] + '、' + list2[j] + '、' + list3[k])
            }
          }
        }
      }
      yearList.map((year: string) => {
        results.map((r) => {
          this.existingPropertyList.push({
            industryName: msgData.industryName,
            year: year == '-1' ? '全部年度' : year,
            propertyName: r
          })
        })
      })
    })
  }
  async filterCode(code: number, data: string[], errMsg: string) {
    let schemeList = new Array<RuleSchemeItem>()
    let schemeNameList = new Array<string>()
    switch (code) {
      case 400:
        errMsg = '一天学习时长不能超过1440分钟'
        break
      case 401:
        errMsg = '一天学习时长不能超过32学时'
        break
      case 410:
        errMsg = '适用行业与指定方案设置规则间存在不符合的方案'
        break
      case 411:
        // data
        if (data?.length) {
          schemeList = await new QueryTrainClassCommodityList().pageRuleSchemeList(new Page(1, 200), {
            schemeIds: data
          })
          schemeNameList = schemeList.map((item) => {
            return item.schemeName
          })
          errMsg = schemeNameList.join('、') + '被引用于多个特殊规则中，同一方案不支持同时被多个规则引用，请调整！'
        } else {
          errMsg = '指定方案配置规则不同特殊规则间存在重复方案'
        }
        break
      case 412:
        if (data?.length) {
          schemeList = await new QueryTrainClassCommodityList().pageRuleSchemeList(new Page(1, 200), {
            schemeIds: data
          })
          schemeNameList = schemeList.map((item) => {
            return item.schemeName
          })
          errMsg =
            '不包含的方案、指定方案设置规则都存在' +
            schemeNameList.join('、') +
            '，同一方案不支持同时被不包含的方案、指定方案设置规则引用，请调整！'
        } else {
          errMsg = '不包含的方案、指定方案设置规则存在重复方案'
        }
        break
      case 300:
        await this.doErrMsg(data)
        break
      default:
        errMsg = '操作异常'
        break
    }
    return errMsg
  }

  getPropertyKeyName(list: string[], item: any) {
    list = list.map((s: string) => {
      s =
        this.trainingAttribute.find((t) => t.type == item.type)?.name +
        '：' +
        (this.itemValueListres.find((ite) => ite.id == s)?.name ||
          '全部' + this.trainingAttribute.find((t) => t.type == item.type)?.name)
      return s
    })
    return list
  }
}
