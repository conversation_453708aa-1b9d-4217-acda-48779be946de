import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
import TimeFormat from '@api/service/customer/course/query/vo/TimeFormat'
import { StudentCourseLearningResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

class MyInterestCourse {
  id: string
  // 所属大纲id
  outlineId: string
  // 课程名称
  name: string
  // 教师信息
  teachers: Array<SimpleUserInfo> = new Array<SimpleUserInfo>()
  // 时长
  timeLength: number
  // 格式化的时间
  timeFormat: TimeFormat
  // 进度
  schedule: number

  static from(response: StudentCourseLearningResponse): MyInterestCourse {
    const course = new MyInterestCourse()
    course.id = response.course.courseId
    course.outlineId = response.courseOfCourseTrainingOutline.outlineId
    course.schedule = response.studentCourseMediaLearningRecord ? response.studentCourseMediaLearningRecord.schedule : 0
    return course
  }

  /**
   * 获取教师信息
   */
  getTeacherNames() {
    return this.teachers
      .map((teacher: SimpleUserInfo) => {
        return teacher.name
      })
      .join('、')
  }
}

export default MyInterestCourse
