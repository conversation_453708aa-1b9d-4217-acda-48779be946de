import Mockjs from 'mockjs'

class UserInfo {
  id: string
  name: string
}

/**
 * 课程包同步日志模型
 */
class CoursePackageSyncLog {
  // id
  id: string
  // 日志时间
  logTime: string
  // 同步操作用户
  creator: UserInfo
  // 状态
  // 0 失败， 1成功
  status: number
  // 同步消息
  message: string

  isSuccess() {
    return this.status === 1
  }

  static from(logResponse: any): CoursePackageSyncLog {
    const log = new CoursePackageSyncLog()
    log.id = logResponse.id
    log.logTime = logResponse.logTime
    log.message = logResponse.message
    log.status = logResponse.status
    log.creator = new UserInfo()
    log.creator.id = Mockjs.Random.guid()
    log.creator.name = `${Mockjs.Random.cfirst()} ${Mockjs.Random.clast()}`
    return log
  }
}

export default CoursePackageSyncLog
