import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
import {
  SinglePrintCertificateReponse,
  UnaccomplishedQuestionnaire
} from '@api/diff-gateway/platform-jxjypxtypt-jxgx-certificate'

/**
 * @description 打印证书返回结果
 */
class PrintCertResponse extends SinglePrintCertificateReponse {
  /**
   * 文件路径
   */
  filePath = ''
  /**
   * 是否需要强制完成调查问卷
   */
  needForceQuestionnaire = false
  /**
   * 强制问卷配置列表
   */
  forcedQuestionnaireList: QuestionnaireConfigDetail[] = []

  static from(response: SinglePrintCertificateReponse): PrintCertResponse {
    const result = new PrintCertResponse()
    const { needForceQuestionnaire, unaccomplishedQuestionnaire, data } = response
    Object.assign(result, response)
    result.filePath = data
    result.needForceQuestionnaire = needForceQuestionnaire
    if (needForceQuestionnaire && unaccomplishedQuestionnaire && unaccomplishedQuestionnaire.length) {
      result.forcedQuestionnaireList = unaccomplishedQuestionnaire.map((el: UnaccomplishedQuestionnaire) => {
        const opt = new QuestionnaireConfigDetail()
        opt.id = el.learningId
        opt.configId = el.unaccomplishedQuestionnaireId
        opt.openDateRange.startDate = el.allowStartTime
        opt.openDateRange.endDate = el.allowEndTime
        return opt
      })
    }
    return result
  }
}

export default PrintCertResponse
