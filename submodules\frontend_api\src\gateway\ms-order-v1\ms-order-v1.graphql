"""独立部署的微服务,K8S服务名:ms-order-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""买家申请取消订单
		@param applyCancelOrder 申请取消订单信息
		@return 取消订单结果
	"""
	applyCancelOrder(applyCancelOrder:BuyerApplyCancelOrderRequest):CancelOrderResultResponse
	"""索取订单发票
		@param orderInvoiceRequest 发票信息
	"""
	applyInvoice(orderInvoiceRequest:ApplyOrderInvoiceRequest):Void
	"""批次单申请发票
		@param applyInvoiceParam 申请发票信息
	"""
	batchApplyInvoice(applyInvoiceParam:BatchOrderApplyInvoiceRequest):Void
	"""取消批次
		@param cancelParam 取消批次参数
	"""
	batchOrderCancel(cancelParam:BatchOrderCancelRequest):Void
	"""提交批次"""
	commitBatchOrder(batchOrderNo:String):Void
	"""创建订单
		@param createOrderInfo 创建参数
		@return 订单创建序列号
	"""
	createOrder(createOrderInfo:CreateOrderRequest):CreateOrderResultResponse
	"""申请线下支付
		<p>
		TODO 线下付款暂未对接
		@param offlinePaymentRequest 线下支付信息
	"""
	offlineApplyPayOrder(offlinePaymentRequest:OrderOfflinePaymentRequest):Void
	"""批次线下支付"""
	offlinePayBatchOrder(batchOrderOfflinePayParam:BatchOrderOfflinePaymentRequest):Void
	"""批次线上支付"""
	onlinePayBatchOrder(batchOrderOnlinePayParam:BatchOrderOnlinePaymentRequest):BatchApplyOnlinePaymentResponse
	"""线上支付
		@param onlinePaymentRequest 线上支付信息
		@return 线上支付结果
	"""
	onlinePayOrder(onlinePaymentRequest:OrderOnlinePaymentRequest):ApplyOnlinePaymentResultResponse
	"""卖家取消订单
		@param sellerCancelOrder 卖家申请取消信息
		@return 取消订单结果
	"""
	sellerCancelOrder(sellerCancelOrder:SellerCancelOrderRequest):CancelOrderResultResponse
}
"""索取发票
	<AUTHOR>
	@since 2021/3/25
"""
input ApplyOrderInvoiceRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.ApplyOrderInvoiceRequest") {
	"""订单号"""
	orderNo:String
	"""发票信息"""
	invoiceInfo:InvoiceInfoRequest
}
"""买家申请取消订单
	<AUTHOR> create 2021/1/27 19:32
"""
input BuyerApplyCancelOrderRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.BuyerApplyCancelOrderRequest") {
	"""订单号"""
	orderNo:String!
	"""取消原因编号"""
	reasonId:String
	"""取消原因描述"""
	reason:String
}
"""请求创建订单
	<AUTHOR>
	@since 2021/1/22
"""
input CreateOrderRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.CreateOrderRequest") {
	"""买家编号"""
	buyerId:String!
	"""商品列表"""
	commodities:[Commodity]!
	"""购买渠道类型
		1-用户自主购买
		2-集体缴费
		3-管理员导入
	"""
	purchaseChannelType:Int!
	"""终端类型
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	terminalCode:String!
	"""渠道商编号"""
	channelVendorId:String
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票信息"""
	invoiceInfo:InvoiceInfoRequest
}
"""商品描述"""
input Commodity @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.CreateOrderRequest$Commodity") {
	"""商品sku编号"""
	skuId:String
	"""商品数量"""
	quantity:Int
}
"""发票信息
	<AUTHOR>
	@since 2021/3/23
"""
input InvoiceInfoRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.InvoiceInfoRequest") {
	"""发票抬头"""
	title:String
	"""发票抬头类型
		<pre>
		1-个人
		2-企业
		</pre>
	"""
	titleType:Int
	"""购买方纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""邮箱"""
	email:String
	"""发票票面备注"""
	remark:String
}
"""graphql 使用的key、value 返回对象
	<AUTHOR> create 2021/2/24 20:07
"""
input KeyValueDataRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.KeyValueDataRequest") {
	"""字段的key"""
	key:String
	"""字段value"""
	value:String
}
"""线上支付参数
	<AUTHOR> create 2021/1/28 19:39
"""
input OrderOfflinePaymentRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.OrderOfflinePaymentRequest") {
	"""订单号"""
	orderNo:String!
	"""付款人"""
	payer:String
	"""备注
		选填
	"""
	remark:String
	"""线下付款凭证文件路径集合"""
	paths:[String]
}
"""线上支付参数
	<AUTHOR> create 2021/1/28 19:39
"""
input OrderOnlinePaymentRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.OrderOnlinePaymentRequest") {
	"""订单号"""
	orderNo:String!
	"""支付渠道编号
		培训券，对接众智汇云培训券:TRAINING_VOUCHER
		支付宝:ALIPAY
		微信：WXPAY
	"""
	paymentChannelId:String!
	"""购买渠道类型
		1-用户自主购买
		2-集体缴费
		3-管理员导入
	"""
	purchaseChannelType:Int!
	"""支付终端
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	purchaseChannelTerminal:String!
	"""支付描述"""
	description:String
	"""支付的附加属性，由支付渠道决定
		当支付渠道为培训券（TRAINING4_VOUCHER）时，
		{
		"couponCode":"培训券编码",
		"educationCode":"机构编码（企业统一社会信用代码）",
		"workType": "工种名称",
		"learningSchemeId": "培训班id"
		}
	"""
	paymentProperties:[KeyValueDataRequest]
}
"""卖家取消订单
	<AUTHOR> create 2021/1/27 20:08
"""
input SellerCancelOrderRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.SellerCancelOrderRequest") {
	"""订单号"""
	orderNo:String!
	"""取消原因ID"""
	reasonId:String
	"""取消原因说明
		选填
	"""
	reason:String
}
"""批次发票
	<AUTHOR> create 2021/4/30 9:36
"""
input BatchInvoiceRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchInvoiceRequest") {
	"""发票种类，默认2
		<pre>
		1-普通发票
		2-增值税普通发票
		3-增值税专用发票
		</pre>
	"""
	invoiceCategory:Int!
	"""发票种类
		1-电子发票
		2-纸质发票
	"""
	invoiceType:Int!
	"""发票抬头"""
	title:String
	"""发票抬头类型
		<pre>
		1-个人
		2-企业
		</pre>
	"""
	titleType:Int
	"""购买方纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""邮箱"""
	email:String
	"""发票票面备注"""
	remark:String
}
"""批次单申请发票
	<AUTHOR> create 2021/4/30 9:12
"""
input BatchOrderApplyInvoiceRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchOrderApplyInvoiceRequest") {
	"""批次单编号"""
	batchOrderNo:String
	"""申请批次发票信息"""
	invoiceInfo:BatchInvoiceRequest
}
"""批次单取消
	<AUTHOR> create 2021/4/22 16:11
"""
input BatchOrderCancelRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchOrderCancelRequest") {
	"""批次单号"""
	batchOrderNo:String
	"""取消时间"""
	cancelledTime:DateTime
	"""取消原因编号"""
	reasonId:String
	"""取消原因描述"""
	reason:String
}
"""批次单线下支付请求参数
	<AUTHOR> create 2021/4/23 11:11
"""
input BatchOrderOfflinePaymentRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchOrderOfflinePaymentRequest") {
	"""批次单号"""
	batchOrderNo:String!
	"""付款人"""
	payer:String
	"""备注
		选填
	"""
	remark:String
	"""线下付款凭证文件路径集合"""
	paths:[String]
}
"""批次单线上支付请求参数
	<AUTHOR> create 2021/4/23 11:10
"""
input BatchOrderOnlinePaymentRequest @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.request.batchorder.BatchOrderOnlinePaymentRequest") {
	"""批次单号"""
	batchOrderNo:String
	"""支付渠道ID"""
	paymentChannelId:String
	"""终端类型"""
	terminalCode:String
	"""支付成功后跳转地址"""
	pageUrl:String
	"""支付描述"""
	description:String
	"""支付的附加属性，由支付渠道决定
		当支付渠道为培训券（TRAINING_VOUCHER）时，
		{
		"couponCode":"培训券编码",
		"educationCode":"机构编码（企业统一社会信用代码）",
		"workType": "工种名称",
		"learningSchemeId": "培训班id"
		}
	"""
	paymentProperties:[KeyValueDataRequest]
}
"""请求线上支付结果
	<AUTHOR> create 2021/2/4 15:16
"""
type ApplyOnlinePaymentResultResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.ApplyOnlinePaymentResultResponse") {
	"""订单号"""
	orderNo:String
	"""去支付的地址"""
	payUrl:String
	"""请求支付结果"""
	result:Boolean!
	"""失败错误代码，仅当result=false时有值
		培训券支付渠道返回错误代码：
		261-培训券已使用
		262-培训券应用条件不符合
		263-培训券已过期
		264-培训券已作废
		265-已兑换过【xxx】的线上培训课程
		266-当前券仅用于兑换【xxx】工种的课程
		267-今年内已参加过该工种培训
		269-培训券余额不足
	"""
	code:String
	"""错误信息"""
	message:String
	"""支付网关支付模式
		0-同步
		1-重定向
	"""
	payMode:Int!
	"""交易号（付款流水号）"""
	payFlowNo:String
}
"""取消订单结果
	<AUTHOR> create 2021/1/29 17:27
"""
type CancelOrderResultResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.CancelOrderResultResponse") {
	"""是否取消成功"""
	success:Boolean!
	"""订单号"""
	orderNo:String
	"""取消结果信息"""
	message:String
	"""商品验证结果信息"""
	resultList:[VerifyResultResponse]
}
"""创建订单结果
	<AUTHOR> create 2021/1/29 17:27
"""
type CreateOrderResultResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.CreateOrderResultResponse") {
	"""是否创建成功"""
	success:Boolean!
	"""订单号，仅当{@link #success}为{@code true}时有值"""
	orderNo:String
	"""订单创建时间，仅当{@link #success}为{@code true}时有值"""
	createTime:DateTime
	"""下单结果信息"""
	message:String
	"""商品验证结果信息"""
	resultList:[VerifyResultResponse]
}
"""校验结果返回
	<AUTHOR> create 2021/2/3 10:53
"""
type VerifyResultResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.VerifyResultResponse") {
	"""校验结果"""
	message:String
	"""校验code"""
	code:String
	"""订单内的商品skuId"""
	skuId:String
}
"""请求线上支付结果
	<AUTHOR> create 2021/2/4 15:16
"""
type BatchApplyOnlinePaymentResponse @type(value:"com.fjhb.ms.order.v1.kernel.gateway.graphql.response.batchorder.BatchApplyOnlinePaymentResponse") {
	"""批次单号"""
	batchOrderNo:String
	"""付款地址"""
	payUrl:String
	"""支付网关支付模式
		<pre>
		0-同步
		1-重定向
		</pre>
	"""
	payMode:Int!
	"""请求支付结果"""
	result:Boolean!
	"""错误信息"""
	message:String
}

scalar List
