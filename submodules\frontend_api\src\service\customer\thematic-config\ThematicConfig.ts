/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-01-11 15:30:01
 * @LastEditors: <PERSON>
 * @LastEditTime: 2024-07-09 11:53:12
 * @Description:
 */
import BasicDataQueryForestage from '@api/platform-gateway/platform-training-channel-fore-gateway'
import { SubjectType } from './enum/SubjectType'
import BasicInfo from './model/BasicInfo'
import PortalInfo from './model/PortalInfo'
import OnlineSchoolConfigModule from '@api/service/customer/online-school-config/OnlineSchoolConfigModule'

/**
 * 专题子模型
 */
class ThematicConfig {
  /**
   * 专题ID
   */
  topicID = ''
  /**
   * 专题状态
   */
  enable: boolean = undefined
  /**
   * 基础信息
   */
  basicInfo = new BasicInfo()
  /**
   * 门户信息
   */
  portalInfo = new PortalInfo()
  // /**
  //  * 已选中培训方案ID
  //  */
  // selectedTrainingPlanID: string[] = []
  /**
   * 获取详情
   * @param id 专题ID
   */
  async getDetail(id: string) {
    const res = await BasicDataQueryForestage.getTrainingChannelDetailById(id)
    const data = res.data
    if (data) {
      this.topicID = data.id
      this.enable = data.enable
      this.basicInfo.displayInSchool = data.showOnNetSchool
      this.basicInfo.domainType = data.domainNameType
      this.basicInfo.entryName = data.entryName
      this.basicInfo.onlineSchoolName = data.netSchoolName
      this.basicInfo.allowAccess = data.allowAccess
      data.netSchoolDoMain.map((res) => {
        if (res.portalType === 1) {
          this.basicInfo.netSchoolDoMain = res.netSchoolDomainName
        } else {
          this.basicInfo.netSchoolH5DoMain = res.netSchoolDomainName
        }
      })
      this.basicInfo.subjectDomain = data.domainName
      this.basicInfo.subjectName = data.name
      this.basicInfo.subjectType = data.types
      this.basicInfo.suiteUnit = data.unitName
      this.basicInfo.suiteAreas = data.regions?.map((item) => item.regionPath) || []
      this.basicInfo.suiteIndustrys = data.industrys || []
      this.basicInfo.templateWeb = data.pcTemplateNo
      this.basicInfo.templateH5 = data.h5TemplateNo
      // 增加topic无值时的代码保护
      data.topic?.photos?.forEach((item) => {
        if (item.type === 1) {
          this.portalInfo.webCarouselSettings.push({
            id: item.id,
            url: item.pictureUrl,
            sort: item.sort,
            link: item.linkUrl
          })
        }
        if (item.type === 2) {
          this.portalInfo.H5CarouselSettings.push({
            id: item.id,
            url: item.pictureUrl,
            sort: item.sort,
            link: item.linkUrl
          })
        }
      })
      this.portalInfo.footer = data.topic?.bottomShowContent
      this.portalInfo.informationTime = data.topic?.seekTime
      this.portalInfo.logoType = data.topic?.logoType
      if (data.topic?.logoType === 1) {
        this.portalInfo.logo = data.topic?.logoName
      }
      if (data.topic?.logoType === 2) {
        this.portalInfo.logo = data.topic?.logoPictureUrl
      }
      this.portalInfo.phone = data.topic?.customerServicePhone
      this.portalInfo.phoneImgUrl = data.topic?.customerServicePhonePictureUrl
      this.portalInfo.phoneType = data.topic?.customerServicePhoneType
      if (data.topic?.trainingProcessType) {
        this.portalInfo.processType = data.topic?.trainingProcessType
        this.portalInfo.process = data.topic?.trainingProcessAttachments?.[0]?.url ?? ''
      } else {
        this.portalInfo.processType = 1
        this.portalInfo.process =
          OnlineSchoolConfigModule.queryOnlineSchoolConfigFactory.queryWebPortalConfig.webPortal.trainingFlowPicture
      }
      if (data.topic?.enterpriseWechatCustomerType) {
        this.portalInfo.customerServiceType = data.topic?.enterpriseWechatCustomerType
        this.portalInfo.customerService = data.topic?.enterpriseWechatCustomerAttachments?.[0]?.url ?? ''
      } else {
        this.portalInfo.customerServiceType = 1
        this.portalInfo.customerService =
          OnlineSchoolConfigModule.queryOnlineSchoolConfigFactory.queryWebPortalConfig.webPortal.enterPriseWxCustomer
      }
    }
  }
}

export default ThematicConfig
