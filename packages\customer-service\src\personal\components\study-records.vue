<template>
  <div class="f-p15" v-if="$hasPermission('studyRecords')" desc="学习档案" actions="userIdChange">
    <el-card shadow="never" class="m-card f-mb15">
      <hb-search-wrapper @reset="resetQuery" class="m-query is-border-bottom">
        <el-form-item label="年度">
          <year-select v-model="queryTrainClassParams.year" placeholder="请选择年度"></year-select>
        </el-form-item>
        <el-form-item label="培训方案">
          <el-input v-model="queryTrainClassParams.schemeName" clearable placeholder="请输入培训方案名称" />
        </el-form-item>
        <el-form-item label="考核结果">
          <el-select v-model="queryTrainClassParams.assessResult" clearable placeholder="是否合格">
            <el-option v-for="item in examinationStatus" :value="item.id" :key="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <template slot="actions">
          <template v-if="$hasPermission('query')" desc="查询" actions="clickSearch">
            <el-button type="primary" @click="clickSearch" :loading="uiStatus.query.loadTrainClassPage">查询</el-button>
          </template>
        </template>
      </hb-search-wrapper>
      <!--表格-->
      <el-table
        stripe
        :data="trainClassTableData"
        max-height="500px"
        class="m-table"
        v-loading="uiStatus.query.loadTrainClassPage"
        ref="elTable"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
          <template slot-scope="scope">
            <span :data-index="scope.$index + 1" v-observe-visibility="visibilityTableConfig">{{
              scope.$index + 1
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="培训方案信息" min-width="300" fixed="left">
          <template slot-scope="scope">
            <div>
              <el-tag type="primary" effect="dark" size="mini">{{ getSchemeTypeName(scope.row) }} </el-tag>
              {{ scope.row.basicInfo.schemeName }}
              <p v-if="isMixedClass(scope.row)">培训期别：{{ issueInfo(scope.row).issueName || '-' }}</p>
              <p>
                <el-tag type="info" class="f-mr10" v-if="scope.row.schemeStatus === 2">冻结</el-tag>
                <el-tag type="success" class="f-mr10" v-if="scope.row.schemeStatus === 1">有效</el-tag>
                <slot name="training-program" :scope="scope">
                  <el-tag type="warning" class="f-mr10"
                    >{{ getTrainClassOpenTypeNameById(scope.row.basicInfo.openType) }}
                  </el-tag>
                </slot>
              </p>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="培训属性" min-width="240">
          <template slot-scope="scope">
            <div v-html="getProperty(scope.row.basicInfo.skuValueNameProperty)"></div>
          </template>
        </el-table-column>
        <el-table-column label="学时" min-width="100" align="center">
          <template slot-scope="scope">{{ scope.row.basicInfo.assessPeriod }}</template>
        </el-table-column>

        <el-table-column label="考核要求" min-width="300">
          <template slot-scope="scope">
            <echo-assessment-requirements :line-data="scope.row"></echo-assessment-requirements>
            <!--            <template-->
            <!--              v-if="-->
            <!--                scope.row.assessRequire.hasConfigCourse ||-->
            <!--                scope.row.assessRequire.hasConfigExam ||-->
            <!--                scope.row.trainClassDetail.learningTypeModel.learningExperience.isExamine-->
            <!--              "-->
            <!--            >-->
            <!--              &lt;!&ndash; 是否配置课程&ndash;&gt;-->
            <!--              <div class="f-flex" v-if="scope.row.assessRequire.hasConfigCourse">-->
            <!--                <span>1.</span>-->
            <!--                <div class="f-flex-sub">-->
            <!--                  <p>整体考核学时：{{ scope.row.assessRequire.totalPeriod }}</p>-->
            <!--                  <div>-->
            <!--                    &lt;!&ndash; 是否配置测验 &ndash;&gt;-->
            <!--                    <p v-if="scope.row.basicInfo.schemeType == 1">① 班级学习进度=100%</p>-->
            <!--                    <div>-->
            <!--                      &lt;!&ndash; 开放评价并且强制评价 &ndash;&gt;-->
            <!--                      <p-->
            <!--                        v-if="-->
            <!--                          scope.row.assessRequire.enableAppraisal && scope.row.assessRequire.enableCompulsoryAppraisal-->
            <!--                        "-->
            <!--                      >-->
            <!--                        {{ scope.row.basicInfo.schemeType == 1 ? '②' : '①' }} 课程评价：需评价-->
            <!--                      </p>-->
            <!--                      <p v-else>-->
            <!--                        {{ scope.row.basicInfo.schemeType == 1 ? '②' : '①' }} 每门课程进度=100%-->
            <!--                        <span v-if="scope.row.assessRequire.hasConfigCourseQuiz">-->
            <!--                          且测验达{{ scope.row.assessRequire.courseQuizPassScore }}分</span-->
            <!--                        >-->
            <!--                      </p>-->
            <!--                    </div>-->
            <!--                    &lt;!&ndash; 开放评价并且强制评价 &ndash;&gt;-->
            <!--                    &lt;!&ndash; <p-->
            <!--                      v-if="-->
            <!--                        scope.row.assessRequire.enableAppraisal && scope.row.assessRequire.enableCompulsoryAppraisal-->
            <!--                      "-->
            <!--                    >-->
            <!--                      ② 课程评价：需评价-->
            <!--                    </p> &ndash;&gt;-->
            <!--                  </div>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--              &lt;!&ndash; 是否配置考试 &ndash;&gt;-->
            <!--              <div class="f-flex" v-if="scope.row.assessRequire.hasConfigExam">-->
            <!--                <template v-if="scope.row.assessRequire.isExamAssessed">-->
            <!--                  <span v-if="scope.row.hasConfigExam && scope.row.hasConfigCourse">2.</span>-->
            <!--                  <span v-else>1.</span>-->
            <!--                  <div class="f-flex-sub">-->
            <!--                    <p>考试成绩>={{ scope.row.assessRequire.examPassScore }}分</p>-->
            <!--                  </div>-->
            <!--                </template>-->
            <!--                <template v-else>-->
            <!--                  <span>2.</span>-->
            <!--                  <div class="f-flex-sub">-->
            <!--                    <p>班级考试不纳入考核，考试及格分{{ scope.row.basicInfo.assessScore }}分</p>-->
            <!--                  </div>-->
            <!--                </template>-->
            <!--              </div>-->
            <!--              &lt;!&ndash; 学习心得 &ndash;&gt;-->
            <!--              <div class="f-flex" v-if="scope.row.trainClassDetail.learningTypeModel.learningExperience.isExamine">-->
            <!--                <span v-show="scope.row.hasConfigCourse && scope.row.hasConfigExam">3.</span>-->
            <!--                <span-->
            <!--                  v-show="-->
            <!--                    (!scope.row.hasConfigCourse && scope.row.hasConfigExam) ||-->
            <!--                    (scope.row.hasConfigCourse && !scope.row.hasConfigExam)-->
            <!--                  "-->
            <!--                  >2.</span-->
            <!--                >-->
            <!--                <span v-show="!scope.row.hasConfigCourse && !scope.row.hasConfigExam">1.</span>-->
            <!--                <div class="f-flex-sub">-->
            <!--                  <p>学习心得纳入考核</p>-->
            <!--                  <div>-->
            <!--                    &lt;!&ndash; 是否配置测验 &ndash;&gt;-->
            <!--                    <p>① 各项学习心得要求以具体配置为准</p>-->
            <!--                    <div>-->
            <!--                      <p>-->
            <!--                        ② 至少参加-->
            <!--                        {{ scope.row.trainClassDetail.learningTypeModel.learningExperience.joinCount }}-->
            <!--                        个心得，其中必选心得-->
            <!--                        {{ scope.row.trainClassDetail.learningTypeModel.learningExperience.requireCount }} 个。-->
            <!--                      </p>-->
            <!--                    </div>-->
            <!--                    <div>-->
            <!--                      <p>③ 学习心得纳入考核，且每项心得均为通过。</p>-->
            <!--                    </div>-->
            <!--                  </div>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--            </template>-->
            <!--            &lt;!&ndash; 课程考试都没有配置 &ndash;&gt;-->
            <!--            <template v-if="!scope.row.assessRequire.hasConfigCourse && !scope.row.assessRequire.hasConfigExam">-->
            <!--              无考核要求-->
            <!--            </template>-->
          </template>
        </el-table-column>
        <el-table-column label="考核结果" min-width="240">
          <template slot-scope="scope">
            <echo-assessment-results :line-data="scope.row"></echo-assessment-results>
            <!--            <template-->
            <!--              v-if="-->
            <!--                scope.row.hasConfigCourse ||-->
            <!--                scope.row.hasConfigExam ||-->
            <!--                scope.row.trainClassDetail.learningTypeModel.learningExperience.isExamine-->
            <!--              "-->
            <!--            >-->
            <!--              &lt;!&ndash; 是否配置课程 &ndash;&gt;-->
            <!--              <div class="f-flex" v-if="scope.row.hasConfigCourse">-->
            <!--                <span>1.</span>-->
            <!--                <div class="f-flex-sub">-->
            <!--                  <p>已获得 {{ scope.row.course.totalCompletePeriod || 0 }} 学时</p>-->
            <!--                  &lt;!&ndash; 选课规则显示班级学习进度 &ndash;&gt;-->
            <!--                  <p v-if="scope.row.basicInfo.schemeType == 1">-->
            <!--                    ① 班级学习进度={{ formatNum(scope.row.assessResult.courseLearningSchedule, '0.00') }}%-->
            <!--                  </p>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--              &lt;!&ndash; 是否配置考试 &ndash;&gt;-->
            <!--              <div class="f-flex" v-if="scope.row.hasConfigExam">-->
            <!--                <span v-if="scope.row.hasConfigExam && scope.row.hasConfigCourse">2.</span>-->
            <!--                <span v-else>1.</span>-->
            <!--                <div class="f-flex-sub">-->
            <!--                  <template v-if="scope.row.assessRequire.isExamAssessed">-->
            <!--                    <p>考试成绩={{ scope.row.assessResult.examScore }}分</p>-->
            <!--                  </template>-->
            <!--                  <template v-else>-->
            <!--                    <p v-if="scope.row.exam.examHighestScore">考试成绩={{ scope.row.exam.examHighestScore }}分</p>-->
            <!--                  </template>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--              &lt;!&ndash; 学习心得 &ndash;&gt;-->
            <!--              <div class="f-flex" v-if="scope.row.trainClassDetail.learningTypeModel.learningExperience.isExamine">-->
            <!--                <span v-show="scope.row.hasConfigCourse && scope.row.hasConfigExam">3.</span>-->
            <!--                <span-->
            <!--                  v-show="-->
            <!--                    (!scope.row.hasConfigCourse && scope.row.hasConfigExam) ||-->
            <!--                    (scope.row.hasConfigCourse && !scope.row.hasConfigExam)-->
            <!--                  "-->
            <!--                  >2.</span-->
            <!--                >-->
            <!--                <span v-show="!scope.row.hasConfigCourse && !scope.row.hasConfigExam">1.</span>-->
            <!--                <div class="f-flex-sub">-->
            <!--                  <p>-->
            <!--                    已参加{{ scope.row.assessResult.learningExperienceCount }}个心得，已通过{{-->
            <!--                      scope.row.assessResult.passLearningExperienceCount-->
            <!--                    }}个-->
            <!--                  </p>-->
            <!--                </div>-->
            <!--              </div>-->
            <!--            </template>-->
            <!--            &lt;!&ndash; 课程考试都没有配置 &ndash;&gt;-->
            <!--            <template v-if="!scope.row.hasConfigCourse && !scope.row.hasConfigExam"> 无考核要求</template>-->
          </template>
        </el-table-column>
        <el-table-column label="是否合格" min-width="120">
          <template slot-scope="scope">
            <div v-if="scope.row.assessResult.isQualified">
              <el-badge is-dot type="success" class="badge-status">是</el-badge>
            </div>
            <div v-else>
              <el-badge is-dot type="danger" class="badge-status">否</el-badge>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="合格时间" min-width="200">
          <template slot-scope="scope">
            <div v-if="scope.row.haveSimulate && !scope.row.onlyHaveExamSimulate">
              <el-tooltip
                popper-class="studnet-simulate-tooltip-style"
                class="item"
                effect="dark"
                :content="'同步第三方数据：' + scope.row.simulatePassTime + '/' + scope.row.simulateExamScore + '分'"
                placement="top"
              >
                <i class="el-icon-info f-co f-mr5"></i>
              </el-tooltip>
              {{ scope.row.assessResult.isQualified ? formatTime(scope.row.assessResult.qualifiedTime) : '-' }}
            </div>
            <div v-else>
              {{ scope.row.assessResult.isQualified ? formatTime(scope.row.assessResult.qualifiedTime) : '-' }}
            </div>
          </template>
          <!-- <template slot-scope="scope">{{
            scope.row.assessResult.isQualified ? formatTime(scope.row.assessResult.qualifiedTime) : '-'
          }}</template> -->
        </el-table-column>
        <el-table-column label="成果是否同步" min-width="120">
          <template slot-scope="scope">
            <!-- 方案配置成功是否同步 -->
            <template v-if="isNeedDataSync(scope.row.needDataSync)">
              <!-- 对接管理系统同步状态 -->
              <div v-if="scope.row.connectManageSystem.syncStatus == 1">
                <el-badge is-dot type="success" class="badge-status">已同步</el-badge>
              </div>
              <div v-else-if="scope.row.connectManageSystem.syncStatus == 2">
                <el-badge is-dot type="danger" class="badge-status"
                  >同步失败
                  <template v-if="scope.row.connectManageSystem.syncMessage"
                    >({{ scope.row.connectManageSystem.syncMessage }})
                  </template>
                </el-badge>
              </div>
              <div v-else-if="scope.row.connectManageSystem.syncStatus == 3">
                <el-badge is-dot type="danger" class="badge-status">待同步</el-badge>
              </div>
              <div v-else>
                <el-badge v-if="schoolConfigFlag" is-dot type="danger" class="badge-status">未同步</el-badge>
                <el-badge v-else is-dot type="danger" class="badge-status">不同步</el-badge>
              </div>
            </template>
            <template v-else>
              <el-badge is-dot type="danger" class="badge-status">不同步</el-badge>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template
            slot-scope="scope"
            v-if="$hasPermission('lookCourseCertification')"
            desc="查阅学时证明，重新推送"
            actions="lookCourseCertification,handleRePush"
          >
            <el-button
              :disabled="!scope.row.assessResult.isQualified"
              type="text"
              size="mini"
              @click="lookCourseCertification(scope.row)"
              >查阅学时证明
            </el-button>
            <el-button
              :disabled="
                !scope.row.assessResult.isQualified ||
                (scope.row.connectManageSystem.syncStatus !== 1 &&
                  scope.row.connectManageSystem.syncStatus !== 2 &&
                  scope.row.connectManageSystem.syncStatus !== 3)
              "
              v-if="schoolConfigFlag"
              size="mini"
              type="text"
              @click="handleRePush(scope.row)"
              >重新推送
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch, Ref } from 'vue-property-decorator'
  import YearSelect from '@hbfe/jxjy-admin-customerService/src/personal/components/components/year-select.vue'
  import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
  import { UiPage } from '@hbfe/common'
  import QueryStudentTrainClassListVo from '@api/service/management/train-class/query/vo/QueryStudentTrainClassListVo'
  import StudentTrainClassDetailVo from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'
  import TrainClassOpenType from '@api/service/common/enums/train-class/TrainClassOpenType'
  import TrainingCertificateModule from '@api/service/management/personal-leaning/TrainingCertificateModule'
  import { FileTypesEnum } from '@api/service/common/enums/personal-leaning/FileTypes'
  import { debounce, bind } from 'lodash-decorators'
  import MutationBatchPrintTraining from '@api/service/management/personal-leaning/mutation/MutationBatchPrintTraining'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
  import { ElTable } from 'element-ui/types/table'
  import getServicerIsDocking from '@api/service/management/online-school-config/portal/query/QueryPortal'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication, ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { isBoolean } from 'lodash'
  import { isAhServicerId } from '@hbfe/jxjy-admin-common/src/util/differentiateServicerId'
  import AhStudentTrainClassDetailVo from '@api/service/diff/management/anhui/train-class/StudentTrainClassDetailVo'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import { Response } from '@hbfe/common'
  import { CheckPrintConditionResponse } from '@api/platform-gateway/platform-certificate-v1'
  import EchoAssessmentRequirements from '@hbfe/jxjy-admin-customerService/src/personal/components/components/study-records-components/echo-assessment-requirements.vue'
  import EchoAssessmentResults from '@hbfe/jxjy-admin-customerService/src/personal/components/components/study-records-components/echo-assessment-results.vue'
  import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import TrainClassSchemeType, {
    TrainClassSchemeEnum
  } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  // import {
  //   StudentSchemeLearningSortRequest,
  //   StudentSchemeLearningSortField,
  //   SortPolicy
  // } from '@api/platform-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
  @Component({
    components: {
      YearSelect,
      EchoAssessmentRequirements,
      EchoAssessmentResults
    }
  })
  export default class extends Vue {
    // 查询实例培训班列表
    queryStudentTrainClassObj = TrainClassManagerModule.queryTrainClassFactory.queryStudentTrainClass
    page: UiPage

    // 打印证明实例
    batchPrintTrainingObj = new MutationBatchPrintTraining()
    // 打印证明入参
    batchPrintTrainingParams = this.batchPrintTrainingObj.printCertificateParams

    queryTrainClassParams = new QueryStudentTrainClassListVo()
    trainClassTableData = new Array<StudentTrainClassDetailVo>()
    queryTrainClassTableData = new StudentTrainClassDetailVo()
    // 网校对接与否
    schoolConfigFlag = true
    @Ref('elTable') elTable: any
    get isMixedClass() {
      return (row: StudentTrainClassDetailVo) => {
        return (
          row?.basicInfo?.skuValueNameProperty?.trainingMode?.skuPropertyValueId == TrainingModeEnum.mixed ||
          row?.basicInfo?.skuValueNameProperty?.trainingMode?.skuPropertyValueId == TrainingModeEnum.offline
        )
      }
    }
    /**
     * @description 获取期别信息
     * */
    get issueInfo() {
      return (row: StudentTrainClassDetailVo) => {
        const periodId = row?.periodStudy?.periodId
        if (!periodId) return new IssueConfigDetail()
        return row?.getIssueConfigById(periodId) ?? new IssueConfigDetail()
      }
    }
    examinationStatus = [
      {
        id: true,
        name: '是'
      },
      {
        id: false,
        name: '否'
      }
    ]
    uiStatus = {
      query: {
        loadTrainClassPage: false
      }
    }

    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }

    /**
     * @description 培训形式枚举
     */
    TrainingModeEnum = TrainingModeEnum
    // 学员id 由主文件ref传入
    userId = ''

    // @Watch('userId', {
    //   immediate: true,
    //   deep: true
    // })
    async created() {
      this.schoolConfigFlag = await getServicerIsDocking.getServicerIsDocking()
    }

    async userIdChange(val: string) {
      this.queryTrainClassParams.userId = val
      if (this.queryTrainClassParams.userId) {
        await this.clickSearch()
      } else {
        this.trainClassTableData = new Array<StudentTrainClassDetailVo>()
      }
    }

    // 滚动查询
    async visibilityTableConfig(isVisible: boolean, entry: any) {
      if (isVisible) {
        if (entry.target.dataset.index >= this.page.totalSize) {
          return
        }
        if (parseInt(entry.target.dataset.index) == this.trainClassTableData?.length) {
          this.page.pageNo++
          const list = await this.queryStudentTrainClassObj.queryStudentTrainClassList(
            this.page,
            this.queryTrainClassParams
          )
          this.trainClassTableData = this.trainClassTableData.concat(list)
        }
        this.$nextTick(() => {
          this.elTable.doLayout()
        })
      }
    }

    // 查询培训档案列表
    async doQuery() {
      this.page.pageNo = 1
      this.$nextTick(() => {
        this.elTable.doLayout()
        this.elTable.bodyWrapper.scrollTop = 0
      })
      if (!this.queryTrainClassParams.userId) {
        return
      }
      try {
        this.uiStatus.query.loadTrainClassPage = true
        // const sort = new StudentSchemeLearningSortRequest()
        // sort.field = StudentSchemeLearningSortField.REGISTER_TIME
        // sort.policy = SortPolicy.ASC
        this.trainClassTableData = await this.queryStudentTrainClassObj.queryStudentTrainClassList(
          this.page,
          this.queryTrainClassParams
          // [sort]
        )
        console.log('this.trainClassTableData*********', this.trainClassTableData)
      } catch (e) {
        this.$message.error('获取培训档案列表失败！')
      } finally {
        this.uiStatus.query.loadTrainClassPage = false
      }
    }

    async clickSearch() {
      this.page.pageNo = 1
      await this.doQuery()
    }

    resetQuery() {
      this.queryTrainClassParams.year = undefined
      this.queryTrainClassParams.schemeName = undefined
      this.queryTrainClassParams.assessResult = undefined
      this.doQuery()
    }

    //重新推送
    async handleRePush(row: StudentTrainClassDetailVo) {
      console.log('row+++++++', row)

      //   专业科目不对接，点击推送按钮无交互
      if (
        row.basicInfo.skuValueNameProperty.subjectType.skuPropertyValueId == 'subjectType1018501809dc4d43e0001' &&
        isAhServicerId()
      ) {
        return
      }

      // this.queryTrainClassTableData.studentNo = row.studentNo
      const noAllowPushSchemeIdsArr = (
        CommonConfigCenter.getFrontendApplication(frontendApplication.noAllowPushSchemeIds) || ''
      ).split(',')
      if (noAllowPushSchemeIdsArr.includes(row.trainClassDetail.trainClassBaseInfo.id)) {
        this.$message.error('存在不在推送范围内的方案！')
        return
      }
      let res
      if (isAhServicerId()) {
        const obj = new AhStudentTrainClassDetailVo()
        res = await obj.rePush([row.studentNo])
      } else {
        res = await row.rePush()
      }
      if (res.status.code == 200 && res.data == '重推成功') {
        this.$message.success('重推成功！')
      } else {
        this.$message.error('重推失败！')
      }
    }

    // 查看学时证明
    @bind
    @debounce(150)
    async lookCourseCertification(val: StudentTrainClassDetailVo) {
      // 判断是否开放打印
      if (val?.assessResult?.provideCert) {
        await this.doPrintTraining(val)
      } else {
        this.$confirm('未配置证书！', '提示', {
          confirmButtonText: '确定',
          showCancelButton: false
        })
      }
    }

    // 打印学时证明
    async doPrintTraining(val: StudentTrainClassDetailVo) {
      this.batchPrintTrainingParams.studentNo = val?.studentNo
      this.batchPrintTrainingParams.fileType = FileTypesEnum.PDF
      this.batchPrintTrainingParams.qualificationId = val.qualificationId
      const res = await this.batchPrintTrainingObj.doPrintTraining()
      if (res.data?.code === '50001') {
        return this.$message.error('培训成果未推送，无法打印培训证明')
      }
      if (res?.data?.path) {
        // 新页面打开证明模板
        const newPage = window.open('', '_blank')
        newPage.document.write('<h2>加载中...</h2>')
        newPage.location.href = res.data.path
      } else {
        // this.$message.error('打印学时证明请求失败！')
        this.errorResTips(res)
      }
    }

    errorResTips(res: Response<CheckPrintConditionResponse>) {
      const code = res.status?.errors[0]?.code
      if (code === 46002) {
        return this.$message.error('该学员还未上传证书照片')
      } else {
        return this.$message.error('打印学时证明请求失败！')
      }
    }

    getSchemeTypeName(val: StudentTrainClassDetailVo) {
      return SchemeType.getNewSchemeType(val.basicInfo, true)
    }

    getTrainClassOpenTypeNameById(val: number) {
      const res = TrainClassOpenType.map.get(val)
      return res
    }

    formatTime(val: string) {
      if (!val) return '-'
      const res = this.$moment(new Date(val)).format('YYYY.MM.DD HH:mm:ss')
      return res
    }

    getProperty(val: SkuPropertyResponseVo) {
      if (!val) return
      const topList = new Array<string>()
      const midList = new Array<string>()
      const bottomList = new Array<string>()
      if (val.year && val.year.skuPropertyName) topList.push(val.year.skuPropertyName + '年')
      if (val.industry && val.industry.skuPropertyName) topList.push(val.industry.skuPropertyName)
      if (val.subjectType && val.subjectType.skuPropertyName) topList.push(val.subjectType.skuPropertyName)
      if (val.trainingObject && val.trainingObject.skuPropertyName) topList.push(val.trainingObject.skuPropertyName)
      if (val.positionCategory && val.positionCategory.skuPropertyName)
        topList.push(val.positionCategory.skuPropertyName)
      if (val.jobLevel && val.jobLevel.skuPropertyName) topList.push(val.jobLevel.skuPropertyName)

      if (val.region && val.region.skuPropertyName) midList.push(val.region.skuPropertyName)

      if (val.trainingCategory && val.trainingCategory.skuPropertyName)
        bottomList.push(val.trainingCategory.skuPropertyName)
      if (val.trainingMajor && val.trainingMajor.skuPropertyName) bottomList.push(val.trainingMajor.skuPropertyName)
      if (val.learningPhase && val.learningPhase.skuPropertyName) bottomList.push(val.learningPhase.skuPropertyName)
      if (val.discipline && val.discipline.skuPropertyName) bottomList.push(val.discipline.skuPropertyName)

      let str = ''
      if (topList.length) str += `<p>${topList.join(' / ')}</p>`
      if (midList.length) str += `<p>${midList.join(' / ')}</p>`
      if (bottomList.length) str += `<p>${bottomList.join(' / ')}</p>`
      return str
    }

    // 格式化工具
    formatNum(val: number, format: string) {
      const numeral = require('numeral')
      return numeral(val).format('0.00')
    }

    // 判断是否成果同步
    isNeedDataSync(needDataSync: boolean) {
      return !(isBoolean(needDataSync) && !needDataSync)
    }
  }
</script>
<style scoped lang="scss">
  //   .study-content-tooltip-style.el-tooltip__popper {
  //     background: rgb(242, 184, 191);
  //   }
  //   // 如果是light，就改成is-light
  //   .study-content-tooltip-style.el-tooltip__popper.is-light {
  //     background: rgb(242, 184, 191);
  //   }
  //   // 修改箭头
  //   .study-content-tooltip-style.el-tooltip__popper[x-placement^='top'] .popper__arrow:after {
  //     border-top-color: rgb(242, 184, 191);
  //   }
  //   .study-content-tooltip-style.el-tooltip__popper[x-placement^='top'] .popper__arrow {
  //     border-top-color: rgb(242, 184, 191);
  //   }
  //
</style>
