import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 结束时间类型枚举
 * no_end 无关闭时间
 * assign 指定关闭时间
 */
export enum EndTimeTypeEnum {
  no_end = 'no_end',
  assign = 'assign'
}

/**
 * @description 结束时间类型
 */
class EndTimeType extends AbstractEnum<EndTimeTypeEnum> {
  static enum = EndTimeTypeEnum

  constructor(status?: EndTimeTypeEnum) {
    super()
    this.current = status
    this.map.set(EndTimeTypeEnum.no_end, '无关闭时间')
    this.map.set(EndTimeTypeEnum.assign, '指定关闭时间')
  }
}

export default new EndTimeType()
