import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
import { AchievementExhibitionEnum } from '@api/service/common/enums/train-class/AchievementExhibitionEnum'

/**
 * 培训班基础信息
 */
class TrainClassBaseModel {
  // region properties
  /**
   * 参训资格id
   */
  qualificationId = ''
  /**
   *  是否提供整班重学
   */
  provideRelearn = false

  /**
   *开放证明打印
   */
  openPrintTemplate = true
  /**
   *班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuProperty = new SkuPropertyResponseVo()
  /**
   *培训方案id，类型为string
   */
  id = ''
  /**
   *培训方案类型1: 选课规则2: 自主选课，类型为number3: 培训合作
   */
  schemeType = 0
  /**
   *培训班名称，类型为string
   */
  name = ''
  /**
   *图片url，类型为string
   */
  picture = ''
  /**
   *方案介绍内容，类型为string
   */
  comment = ''
  /**
   * 考核Id
   */
  assessSettingId = ''
  /**
   * 考核模板名称
   */
  assessSettingName = ''
  /**
   * 学分成果id
   */
  creditId = ''
  /**
   *报名开始时间，类型为string
   */
  registerBeginDate = ''
  /**
   *报名结束时间，类型为string
   */
  registerEndDate = ''
  /**
   *培训开始时间，类型为string
   */
  trainingBeginDate = ''
  /**
   *培训结束时间，类型为string
   */
  trainingEndDate = ''
  /**
   *获得学时，类型为number
   */
  period = 0
  /**
   *是否有培训证明，类型为boolean
   */
  hasLearningResult = false
  /**
   *培训证明模板id，类型为string
   */
  learningResultId = ''
  /**
   *培训证明成果id，类型为string
   */
  learningResultAchievementsId = ''
  // endregion
  // region methods

  // endregion
  /**
   *培训方案id，自主选课复制用
   */
  idCopy? = ''
  /**
   *成果是否同步
   */
  needDataSync? = true

  /**
   * 成果开始时间规则
   */
  achievementExhibition = AchievementExhibitionEnum.signUpTime

  /**
   * 培训方案须知
   */
  notice = ''
  /**
   * 培训期别-通用培训须知
   */
  issueNotice = ''

  /**
   * 培训方案简介id
   */
  introId = ''

  /**
   * 培训方案简介
   */
  introContent = ''
  /**
   * 是否智能学习中
   */
  isIntelligenceLearning? = false
  /**
   * 培训方案须知弹窗
   */
  showNoticeDialog = false
}
export default TrainClassBaseModel
