import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * @description 终端类型
 */
export enum OrderTerminalTypeEnum {
  Web = 1,
  IOS = 2,
  Android = 3,
  WechatMini = 4,
  WechatOfficial = 5,
  ExternalSystemManage = 6,
  H5 = 7
}

class OrderTerminalTypes extends AbstractEnum<OrderTerminalTypeEnum> {
  static enum = OrderTerminalTypeEnum

  constructor(status?: OrderTerminalTypeEnum) {
    super()
    this.current = status
    this.map.set(OrderTerminalTypeEnum.Web, 'Web端')
    this.map.set(OrderTerminalTypeEnum.IOS, 'IOS端')
    this.map.set(OrderTerminalTypeEnum.Android, '安卓端')
    this.map.set(OrderTerminalTypeEnum.WechatMini, '微信小程序')
    this.map.set(OrderTerminalTypeEnum.WechatOfficial, '微信公众号')
    this.map.set(OrderTerminalTypeEnum.ExternalSystemManage, '外部管理系统')
    this.map.set(OrderTerminalTypeEnum.H5, 'H5')
  }
}

export default new OrderTerminalTypes()
