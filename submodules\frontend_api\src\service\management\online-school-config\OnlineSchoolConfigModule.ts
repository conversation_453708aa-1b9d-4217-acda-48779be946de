import store from '@/store'
import MutationBanner from '@api/service/management/online-school-config/banner/mutation/MutationBanner'
import QueryBanner from '@api/service/management/online-school-config/banner/query/QueryBanner'
import MutationDistributionChannelsConfigFactory from '@api/service/management/online-school-config/distribution-channels-config/MutationDistributionChannelsConfigFactory'
import QueryDistributionChannelsConfigFactory from '@api/service/management/online-school-config/distribution-channels-config/QueryDistributionChannelsConfigFactory'
import MutationFunctionalitySettingFactory from '@api/service/management/online-school-config/functionality-setting/MutationFunctionalitySettingFactory'
import QueryFunctionalitySettingFactory from '@api/service/management/online-school-config/functionality-setting/QueryFunctionalitySettingFactory'
import MutationPortal from '@api/service/management/online-school-config/portal/mutation/MutationPortal'
import QueryPortal from '@api/service/management/online-school-config/portal/query/QueryPortal'
import MutationWebPreview from '@api/service/management/online-school-config/preview/mutation/MutationWebPreview'
import MutationTheme from '@api/service/management/online-school-config/theme/mutation/MutationTheme'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'

@Module({
  name: 'ManagementOnlineSchoolConfigModule',
  dynamic: true,
  namespaced: true,
  store
})
class OnlineSchoolConfigModule extends VuexModule {
  /**
   * @description 查询配送渠道工厂类
   */
  get queryDistributionChannelsConfigFactory() {
    return QueryDistributionChannelsConfigFactory
  }

  /**
   * @description 业务配送渠道工厂类
   */
  get mutationDistributionChannelsConfigFactory() {
    return MutationDistributionChannelsConfigFactory
  }

  /**
   * @description 功能设置业务工厂类
   */
  get mutationFunctionalitySettingFactory() {
    return MutationFunctionalitySettingFactory
  }

  /**
   * @description 功能设置业务工厂类
   */
  get queryFunctionalitySettingFactory() {
    return QueryFunctionalitySettingFactory
  }

  /**
   * 风格设置类
   */
  get mutationTheme() {
    return MutationTheme
  }

  /**
   * 轮播图查询类
   */
  get queryBanner() {
    return QueryBanner
  }

  /**
   * 轮播图业务类
   */
  get mutationBanner() {
    return MutationBanner
  }

  /**
   * 门户信息配置查询类
   */
  get queryPortal() {
    return QueryPortal
  }

  /**
   * 门户信息配置业务类
   */
  get mutationPortal() {
    return MutationPortal
  }

  /**
   * 门户信息预览业务类
   */
  get mutationPreview() {
    return MutationWebPreview
  }
}

export default getModule(OnlineSchoolConfigModule)
