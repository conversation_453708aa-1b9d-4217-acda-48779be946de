import {
  StudentQueryRequest,
  StudentUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

class StudentQueryIdVo {
  /**
   * 用户名称（模糊）
   */
  userName?: string = ''

  /**
   * 证件号
   */
  idCard?: string = ''

  /**
   * 手机号
   */
  phone?: string = ''

  /**
   * 工作单位
   */
  unit?: string = ''

  /**
   * 登录账号
   */
  loginAccount?: string = ''
  toDto() {
    const params = new StudentQueryRequest()
    params.user = new StudentUserRequest()
    params.user.userName = this.userName
    params.user.idCard = this.idCard
    params.user.phone = this.phone
    params.user.companyName = this.unit
    params.authentication = this.loginAccount
      ? {
          userName: this.loginAccount
        }
      : undefined
    if (params.user.companyRegionPathList) {
      params.user.companyRegionPathListMatchType = 2
    }
    return params
  }
}

export default StudentQueryIdVo
