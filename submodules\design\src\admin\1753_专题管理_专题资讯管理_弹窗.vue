<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--选择专题-空数据-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">选择专题-空数据</el-button>
        <el-drawer
          title="选择专题"
          :visible.sync="dialog1"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-no-date f-mt50">
              <img class="img" src="./assets/images/no-data-normal.png" alt="" />
              <div class="date-bd">
                <p class="f-f15 f-c9">暂无数据</p>
                <div class="f-mt20">
                  <el-button type="primary" icon="el-icon-plus">新建专题</el-button>
                </div>
              </div>
            </div>
          </div>
        </el-drawer>

        <!--选择专题-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">选择专题</el-button>
        <el-drawer
          title="选择专题"
          :visible.sync="dialog2"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="8">
                  <el-form-item label="专题名称">
                    <el-input v-model="input" clearable placeholder="请输入专题名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="专题类型">
                    <el-select clearable placeholder="请选择专题类型">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="专题名称" min-width="250">
                <template>福州地区专题页</template>
              </el-table-column>
              <el-table-column label="专题类型" min-width="250">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        >
                      </div>
                      <el-button type="text" class="f-to-three"
                        ><i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        ></el-button
                      >
                    </el-tooltip>
                  </div>
                  <div v-else><el-tag type="warning" size="mini" class="f-mt5">行业</el-tag>工艺美术</div>
                </template>
              </el-table-column>
              <el-table-column label="状态" min-width="100">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 1">
                    <el-button type="text" size="mini">取消选择</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text" size="mini">选择</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
            <div class="m-tit">
              <span class="tit-txt">已选择的专题（X 个）</span>
            </div>
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="8">
                  <el-form-item label="专题名称">
                    <el-input v-model="input" clearable placeholder="请输入专题名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="专题类型">
                    <el-select clearable placeholder="请选择专题类型">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="专题名称" min-width="200">
                <template>福州地区专题页福州地区专题页</template>
              </el-table-column>
              <el-table-column label="专题类型" min-width="250">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        >
                      </div>
                      <el-button type="text" class="f-to-three"
                        ><i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        ></el-button
                      >
                    </el-tooltip>
                  </div>
                  <div v-else><el-tag type="warning" size="mini" class="f-mt5">行业</el-tag>工艺美术</div>
                </template>
              </el-table-column>
              <el-table-column label="状态" min-width="100">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">取消选择</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: true,
        dialog2: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
