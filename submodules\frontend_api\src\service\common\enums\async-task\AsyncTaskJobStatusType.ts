import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * 异步任务处理状态
 */
enum AsyncTaskJobStatusEnum {
  EXECUTING = 'executing',
  EXECUTED = 'executed',
  FAIL = 'fail'
}
export { AsyncTaskJobStatusEnum }

class AsyncTaskJobStatusType extends AbstractEnum<AsyncTaskJobStatusEnum> {
  static enum = AsyncTaskJobStatusEnum
  constructor(status?: AsyncTaskJobStatusEnum) {
    super()
    this.current = status
    this.map.set(AsyncTaskJobStatusEnum.EXECUTING, '执行中')
    this.map.set(AsyncTaskJobStatusEnum.EXECUTED, '已执行')
    this.map.set(AsyncTaskJobStatusEnum.FAIL, '执行失败')
  }
}

export default new AsyncTaskJobStatusType()
