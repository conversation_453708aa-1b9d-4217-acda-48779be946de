<!--根据闽政通登录状态，判断是否自动登录-->
<template>
  <div v-loading="loading" style="width: 100%; height: 100%" :element-loading-text="text"></div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import ConnectState from '@hbfe/jxjy-admin-authentication/src/login/models/ConnectState'
  import AuthModule from '@api/service/common/auth/AuthModule'
  import MZTStatus from '@hbfe/jxjy-admin-authentication/src/login/models/MZTStatus'
  import { AccountType } from '@api/service/common/authentication/interfaces/LoginParams'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { ConnectByTicketData } from '@hbfe-biz/biz-authentication/dist/models/ConnectByTicketParams'
  import ApplyType from '@hbfe-biz/biz-authentication/dist/enums/ApplyType'

  const text = '正在登录'
  @Component
  export default class extends Vue {
    timer: any = 0
    text = text
    time = 0
    loading = false

    get query(): MZTStatus {
      return this.$route.query as any
    }

    async apply() {
      const href = this.$router.resolve('binding-mzt').href
      const response = await this.$authentication.mztAuth.apply(
        process.env.NODE_ENV === 'development' ? `${location.origin}/${href}` : `${location.origin}/admin/${href}`,
        ConfigCenterModule.getFrontendApplication(frontendApplication.wxOpenIdToken),
        AccountType.admin,
        ApplyType.login
      )
      return response
    }

    beforeDestroy() {
      window.clearInterval(this.timer)
    }

    isOnLoginStatus() {
      this.loading = true
      window.clearInterval(this.timer)
      this.timer = setInterval(() => {
        this.time++
        this.text = text.padEnd(text.length + this.time, '.')
        if (this.time >= 3) {
          this.time = 0
        }
      }, 1000)
    }

    async created() {
      this.loading = true
      const loginflag = this.query.loginflag
      const trustticket = this.query.trustticket as string
      if (loginflag !== undefined) {
        if (loginflag === 'true') {
          // 闽政通登录状态 ——>自动登录
          const connectByTicketResult = await this.$authentication.mztAuth.connectByTicket(trustticket)
          console.log('connectByTicketResult', connectByTicketResult)
          if (connectByTicketResult.code === String(await this.$authentication.mztAuth.connectByTicket(trustticket))) {
            this.analysisConnectStatus(connectByTicketResult)
          } else {
            // 获取失败
            this.$router.replace('/binding-mzt')
          }
        } else {
          // 未登录
          this.$router.replace('/binding-mzt')
        }
        // 需要return 避免死循环
        this.loading = false
        return
      }

      // 闽政通登录接口入口文档：http://192.168.1.225:8090/pages/viewpage.action?pageId=********
      let resultUrl =
        process.env.NODE_ENV === 'development'
          ? `${location.origin}/#${this.$route.fullPath}`
          : `${location.origin}/admin/#${this.$route.fullPath}`
      resultUrl = encodeURIComponent(resultUrl)
      await this.$authentication.mztAuth.apply(
        resultUrl,
        ConfigCenterModule.getFrontendApplication(frontendApplication.wxOpenIdToken),
        AccountType.admin,
        ApplyType.checkLogin
      )
    }

    async analysisConnectStatus(connectByTicketResult: ConnectByTicketData) {
      this.isOnLoginStatus()
      const stateValue = Number(connectByTicketResult.connectState)
      const token = connectByTicketResult.token
      if (!ConnectState.isStateIllegal(stateValue) || !token) return this.$message.error('参数非法')
      const connectState = new ConnectState(stateValue)
      // 判断是否绑定
      if (!connectState.isBind()) {
        // 执行同步培训机构的动作
        const result = await AuthModule.doSyncTeachUnit(token)
        if (!result.status.isSuccess()) {
          // 不是培训机构
          if (result.status.code === 30001) {
            return this.apply()
          }
          // 非法人登录
          if (result.status.code === 30002) {
            try {
              await this.$confirm('当前登录用户非法人登录，请使用法人登录！', '系统提醒', {
                confirmButtonText: '使用法人用户登录',
                closeOnPressEscape: false,
                closeOnClickModal: false,
                center: true
              })
              const returnUrl =
                process.env.NODE_ENV === 'development'
                  ? `${location.protocol}//${location.hostname}:8080`
                  : `${window.location.origin}${window.location.pathname}`
              this.$authentication.mztAuth.logout(returnUrl)
            } catch (e) {
              // nothing
              window.location.replace(window.location.origin)
              window.location.reload()
            }
            return ''
          }
          try {
            await this.$confirm(
              '暂无培训机构信息，请确认已在“福建省补贴性职业培训管理平台”完成注册确认。',
              '系统提醒',
              {
                confirmButtonText: '前往补贴管理系统',
                closeOnPressEscape: false,
                closeOnClickModal: false,
                center: true
              }
            )
            return window.location.replace('https://zypx.fjrst.cn/#/login')
          } catch (e) {
            // nothing
            window.location.replace(window.location.origin)
            window.location.reload()
          }
          // 如果执行失败，将会抛出信息，并不再执行下面的动作。
          // window.location.replace(window.location.origin)
          const message = result.status.getMessage()
          return message && this.$message.error(message)
        }
      }

      try {
        await this.$authentication.mztAuth.login(token)
        // await this.$router.replace('/home')
        window.location.href = this.$router.resolve('/home').href
        // window.location.reload()
      } catch (e) {
        if (e.code !== 200) {
          this.$message.error(e.message || '登录失败')
        }
        // 登录失败
        window.location.href = this.$router.resolve('/login').href
      }
    }
  }
</script>
