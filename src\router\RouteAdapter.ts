// import orgRouterConfig from 'org-routes'
// import providerRouterConfig from 'provider-routes'
// import channelRouterConfig from 'channel-routes'
import boilerplateRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/boilerplate-routes'
import basicSchoolRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/basic-school-routes'
import fjzjRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/fjzj-routes'
import scjzsRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/scjzs-routes'
import xmlgRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/xmlg-routes'
import yzzjRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/yzzj-routes'
// import superRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/super-routes'
// import participatingUnitRoutes from 'participating-unit-routes'
import mergeDiffRoutes from '@/router/mergeDiffRoutes'
import UnitTypeEnum from '@/router/models/UnitTypeEnum'
import SchoolEnum from '@api/service/common/diffSchool/enums/SchoolEnum'
import SchoolServiceIdStrategy from '@api/service/common/diffSchool/SchoolServiceIdStrategy'
import fxRouter from '@hbfe/fx-manage'
import jxgxRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/jxgx-routes'
import qztgRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/qztg-routes'
import zzkdRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/zzkd-routes'
import zjzjRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/zjzj-routes'
import gstybRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/gstyb-routes'
import zzttRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/zztt-routes'
import gszjRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/gszj-routes'
import byzjRouterConfig from '@hbfe/jxjy-admin-common/src/router-config/byzj-routes'

// 差异化网校路由信息
const diffSchoolRouteMap = {
  // 合并hotfix 1.15.15.0 把福建专技配置合并过来
  // 四川建造师差异化路由
  [SchoolEnum.SCJZS]: scjzsRouterConfig,
  [SchoolEnum.FJZJ]: fjzjRouterConfig,
  // 扬州专技差异化路由
  [SchoolEnum.YZZJ]: yzzjRouterConfig,
  [SchoolEnum.XMLG]: xmlgRouterConfig,
  [SchoolEnum.JXGX]: jxgxRouterConfig,
  [SchoolEnum.QZTG]: qztgRouterConfig,
  [SchoolEnum.ZZKD]: zzkdRouterConfig,
  [SchoolEnum.ZJZJ]: zjzjRouterConfig,
  [SchoolEnum.GSTYB]: gstybRouterConfig,
  [SchoolEnum.ZZTT]: zzttRouterConfig,
  [SchoolEnum.GSZJ]: gszjRouterConfig,
  [SchoolEnum.BYZJ]: byzjRouterConfig
}

const routesMap = {
  // [UnitTypeEnum.SUPER]: superRouterConfig,
  [UnitTypeEnum.WXGLY]: basicSchoolRouterConfig
  // [UnitTypeEnum.ORG]: orgRouterConfig,
  // [UnitTypeEnum.PROVIDER]: providerRouterConfig,
  // [UnitTypeEnum.CHANNEL]: channelRouterConfig,
  // [UnitTypeEnum['PARTICIPATING-UNIT']]: participatingUnitRoutes
}
const unitMap = {
  WXGLY: UnitTypeEnum.WXGLY
}
export { unitMap }
const unitTypeMap: Map<number, UnitTypeEnum> = new Map<number, UnitTypeEnum>()
unitTypeMap.set(1, UnitTypeEnum.ORG)
unitTypeMap.set(2, UnitTypeEnum.PROVIDER)
unitTypeMap.set(3, UnitTypeEnum.CHANNEL)
unitTypeMap.set(4, UnitTypeEnum['PARTICIPATING-UNIT'])

export { unitTypeMap }
/**
 * 合并路由
 * @param target
 * @param source
 */
const mergeRoutes = (target: any, source: any) => {
  if (target && !target?.hasNo) {
    target.hasNo = []
  }
  source?.forEach((sourceItem: any) => {
    const item = target?.find((item: any) => {
      return item.path === sourceItem.path
    })
    if (!item) {
      target?.hasNo.push(sourceItem)
    } else {
      item.component = sourceItem.component

      mergeRoutes(item.children, sourceItem.children)
    }
  })
}

/**
 * 追加不存在的路由
 * @param arr
 */
const concat = (arr: any) => {
  const length = arr.length
  arr.push(...(arr.hasNo || []))
  for (let i = 0; i < length; i++) {
    const item = arr[i]
    if (item?.children && item.children.length) {
      concat(item.children)
    }
  }
}

class RouteAdapter {
  mergedRoutes = boilerplateRouterConfig
  //分销路由
  fxRouters = fxRouter

  basicSchool = basicSchoolRouterConfig
  mergeRouteByUnitType(unitType: UnitTypeEnum) {
    // 获取基准网校路由
    const basicSchoolRouteMap = routesMap[unitType] || []
    this.basicSchool = basicSchoolRouteMap
    console.log(basicSchoolRouteMap, '网校基础路由')
    console.log(this.mergedRoutes, '样板完全路由')

    // 合并差异化网校之后的路由
    let newSchoolRouteMap = new Array<any>()
    const currentSchool = SchoolServiceIdStrategy.currentSchool()
    if (currentSchool) {
      console.log(diffSchoolRouteMap[currentSchool], '差异化网校路由')

      newSchoolRouteMap = mergeDiffRoutes(basicSchoolRouteMap, diffSchoolRouteMap[currentSchool] || [])
      console.log(newSchoolRouteMap, '网校基础路由和差异化网校路由合并后')
    }

    // 执行合并路由
    newSchoolRouteMap.length
      ? mergeRoutes(boilerplateRouterConfig, newSchoolRouteMap)
      : mergeRoutes(boilerplateRouterConfig, basicSchoolRouteMap)
    concat(boilerplateRouterConfig)
    this.mergedRoutes = boilerplateRouterConfig
    console.log(this.mergedRoutes, '与样板路由合并完的路由')

    return this.mergedRoutes
  }

  initFxRouter() {
    this.fxRouters = mergeDiffRoutes(this.fxRouters, [])
    return this.fxRouters
  }
}

const routeAdapter = new RouteAdapter()
export default routeAdapter
