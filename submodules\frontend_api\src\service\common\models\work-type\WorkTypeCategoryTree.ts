import WorkTypeCategory from '@api/service/common/models/work-type/WorkTypeCategory'

class WorkTypeCategoryTree extends WorkTypeCategory {
  children = new Array<WorkTypeCategoryTree>()

  addChild(child: WorkTypeCategoryTree) {
    if (!this.children) {
      this.children = new Array<WorkTypeCategoryTree>()
    }
    this.children.push(child)
    return this
  }

  hasChildren(): boolean {
    return this.children?.length > 0
  }

  sortChildren() {
    this.children?.sort((a, b) => {
      let n = a.sort - b.sort
      if (n === 0) n = a.lastUpdateTime.getTime() - b.lastUpdateTime.getTime()
      return n
    })
    this.children?.forEach(child => child.sortChildren())
  }
}

export default WorkTypeCategoryTree
