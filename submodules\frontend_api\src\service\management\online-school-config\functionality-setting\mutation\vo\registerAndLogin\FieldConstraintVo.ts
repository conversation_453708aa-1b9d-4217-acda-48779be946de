import { FieldConstraintResponse } from '@api/ms-gateway/ms-servicer-series-v1'

/**
 * @description 表单字段约束
 */
class FieldConstraintVo {
  /**
   * 字段名称
   */
  field: string
  /**
   * 是否必填
   */
  isRequire: boolean
  /**
   * 验证器
   */
  validator: string
  /**
   * 关联字段id
   */
  relateField: string
  /**
   * 关联字段的值
   */
  relateFieldValue: string

  static from(response: FieldConstraintResponse) {
    const fieldConstraint = new FieldConstraintVo()
    fieldConstraint.field = response?.field
    fieldConstraint.isRequire =
      response?.validators?.length && response.validators[0]
        ? response.validators[0].type === 'require'
          ? true
          : false
        : false
    return fieldConstraint
  }
}

export default FieldConstraintVo
