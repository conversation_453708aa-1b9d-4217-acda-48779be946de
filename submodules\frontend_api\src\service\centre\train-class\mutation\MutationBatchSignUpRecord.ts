import { Response } from '@hbfe/common'
import MsCertificate from '@api/ms-gateway/ms-certificate-v1'
import BatchPrintCertificatesRequestVo from '@api/service/centre/train-class/mutation/vo/BatchPrintCertificatesRequestVo'
import PlatformCertificate, {
  SchemeMessage,
  ScreeningBatchPrintCertificatesRequest
} from '@api/platform-gateway/platform-certificate-v1'

/**
 * @description
 */
class MutationBatchSignUpRecord {
  batchPrintParams: BatchPrintCertificatesRequestVo = new BatchPrintCertificatesRequestVo()

  /**
   * 批量导出证明模板
   */
  async doBatchPrint(): Promise<Response<string>> {
    // TODO 剔除已导出模板字段待后端提供
    const result = new Response<string>()
    const response = await MsCertificate.batchPrintCertificates(this.batchPrintParams)
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }

  /**
   * 集体缴费批量导出证明
   * @param schemeIdList 方案ID
   * @param screening 是否剔除已导出证明
   * @param fileType 文件类型 1-PDF   2-IMAGE
   * @returns {Promise<Response<string>>}
   * @constructor
   */
  async PrintBatch(schemeIdList: Array<SchemeMessage>, screening: boolean, fileType = 1, specialId?: string) {
    const request = new ScreeningBatchPrintCertificatesRequest()
    request.screening = screening
    request.schemeMessages = schemeIdList
    request.fileType = fileType
    if (specialId) {
      request.specialId = specialId
    }
    const response = await PlatformCertificate.screeningBatchPrintCertificates(request)
    return response
  }
}

export default MutationBatchSignUpRecord
