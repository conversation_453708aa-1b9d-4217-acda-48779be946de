import { UiPage } from '@hbfe/common'
import { CourseQuizAnswerPaperResponseVo } from '@api/service/customer/exam/query/vo/CourseQuizAnswerPaperResponseVo'
import MsExamQueryFrontGatewayCourseLearningForeStage, {
  AnswerPaperSort,
  SortTypeEnum
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryForeStage'
import AfterCourseTestLastResult from '@api/service/customer/course/query/vo/AfterCourseTestLastResult'
import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  StudentCourseLearningRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import StudentLearningCourseTypeEnum from '@api/service/customer/learning/scene/gates/enum/StudentLearningCourseTypeEnum'

class QueryAfterCourseTest {
  studentCourseId: string
  qualificationId: string
  schemeType: StudentLearningCourseTypeEnum
  courseId: string

  constructor(
    studentCourseId: string,
    qualificationId: string,
    schemeType: StudentLearningCourseTypeEnum,
    courseId?: string
  ) {
    this.studentCourseId = studentCourseId
    this.qualificationId = qualificationId
    this.schemeType = schemeType
    this.courseId = courseId
  }

  /**
   * 获取考试记录
   */
  async queryQuizRecordPage(
    page: UiPage,
    paper?: AfterCourseTestLastResult
  ): Promise<Array<CourseQuizAnswerPaperResponseVo>> {
    const res = await MsExamQueryFrontGatewayCourseLearningForeStage.pageMyCourseQuizRecordInMyself({
      courseId: this.courseId,
      page: {
        pageSize: page.pageSize,
        pageNo: page.pageNo
      },
      qualificationId: this.qualificationId,
      answerPaperStatus: 2,
      answerPaperSort: AnswerPaperSort.HANDED_TIME,
      sort: SortTypeEnum.DESC
    })
    page.totalSize = res.data.totalSize
    page.totalPageSize = res.data.totalPageSize
    if (paper) {
      res.data.currentPageData.map(item => {
        if (item.answerPaperId === paper.courseQuizAnswerId && item.answerPaperMarkInfo.score === 0) {
          item.answerPaperMarkInfo.score = paper.score
          item.answerPaperTimeInfo.handingTime = paper.learningResultTime
          item.answerPaperStateInfo.answerPaperEvaluateResults = -1
        }
      })
    }
    return res.data.currentPageData as CourseQuizAnswerPaperResponseVo[]
  }

  private async queryCourseLearningScene(studentCourseId: string) {
    const request = new StudentCourseLearningRequest()
    request.studentNo = this.qualificationId
    request.course = {
      courseId: studentCourseId
    }
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    if (this.schemeType === StudentLearningCourseTypeEnum.ChooseCourseRule) {
      const resposne = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneInMyself(
        {
          page,
          request
        }
      )
      page.pageSize = resposne?.data?.totalPageSize || 1
      return await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfChooseCourseLearningSceneInMyself(
        {
          page,
          request
        }
      )
    } else if (this.schemeType === StudentLearningCourseTypeEnum.AutonomousCourse) {
      const resposne = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseOfAutonomousCourseLearningSceneInMyself(
        {
          page,
          request
        }
      )
      page.pageSize = resposne?.data?.totalPageSize || 1
      return await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseOfAutonomousCourseLearningSceneInMyself(
        {
          page,
          request
        }
      )
    } else if (this.schemeType === StudentLearningCourseTypeEnum.InterestCourse) {
      const resposne = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfInterestCourseLearningSceneInMyself(
        {
          page,
          request
        }
      )
      page.pageSize = resposne?.data?.totalPageSize || 1
      return await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfInterestCourseLearningSceneInMyself(
        {
          page,
          request
        }
      )
    }
  }

  async queryAfterCourseTestLastResult(): Promise<AfterCourseTestLastResult> {
    // const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.getCourseLearningRecordInMyself(
    //   this.studentCourseId
    // )
    // if (result.status.code === 200 && result.status.isSuccess()) {
    //   return AfterCourseTestLastResult.from(result.data.studentCourseQuiz)
    // }
    const result = await this.queryCourseLearningScene(undefined)
    if (result.data?.currentPageData?.length) {
      const lastResult = result.data.currentPageData.find(
        res => res.studentCourse?.studentCourseId === this.studentCourseId
      )
      return AfterCourseTestLastResult.from(lastResult?.studentCourseQuiz)
    }
    return new AfterCourseTestLastResult()
  }
}

export default QueryAfterCourseTest
