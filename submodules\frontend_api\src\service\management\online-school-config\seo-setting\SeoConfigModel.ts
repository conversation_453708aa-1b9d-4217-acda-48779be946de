import MsSeries, { OnlineSchoolSEOConfigRequest } from '@api/ms-gateway/ms-servicer-series-v1'
import SeoConfigItem from './models/SeoConfigItem'
export default class SeoConfigModel {
  /**
   * 网校SEO配置
   */
  config: SeoConfigItem = new SeoConfigItem()

  /**
   * 获取网校SEO配置
   */
  async getConfig() {
    const res = await MsSeries.getOnlineSchoolConfig()
    if (res.status.isSuccess() && res.data?.seoConfig) {
      // this.config.title = res.data.seoConfig.title
      this.config.description = res.data.seoConfig.description
      this.config.keywords = res.data.seoConfig.keywords
    } else {
      this.config = new SeoConfigItem()
    }
    return this.config
  }
  /**
   * 保存网校SEO配置
   */
  async saveConfig() {
    const request = new OnlineSchoolSEOConfigRequest()
    request.description = this.config.description
    request.keywords = this.config.keywords
    const res = await MsSeries.saveOnlineSchoolSEOConfig(request)
    return res.status
  }
}
