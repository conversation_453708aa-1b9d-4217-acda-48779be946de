import PlatformJxjypxtyptJxgxUserAuth, {
  CheckUserAuthInfoRequest
} from '@api/diff-gateway/platform-jxjypxtypt-jxgx-user-auth'

export default class CreateOrder {
  /**
   * 判断是否允许保存发票
   */
  async isAllowCreateOrder(idCard: string): Promise<boolean> {
    const checkUserAuthInfoRequest = new CheckUserAuthInfoRequest()
    checkUserAuthInfoRequest.idCard = idCard
    const res = await PlatformJxjypxtyptJxgxUserAuth.checkUserAuthInfo(checkUserAuthInfoRequest)
    if (res.status.isSuccess()) {
      return res.data
    }
    return false
  }
}
