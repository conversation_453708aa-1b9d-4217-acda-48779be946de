/**
 *
 * ui 试卷分类模型
 * @author: eleven
 * @date: 2020/4/5
 */
export class ExamPaperClassifyUI {
  id = ''
  name = ''
  parentId = '-1'
  parentName = '试卷分类'
  // 两个判断是否有子级的字段冗余
  leaf = false
  hasChild = true
  unitId = '-1' //机构的时候为 -1
  /**
   * 子节点
   */
  subCategory = new Array<ExamPaperClassifyUI>()

  static createRootNode() {
    const result = new ExamPaperClassifyUI()
    result.id = '-1'
    result.name = '试卷分类'
    return result
  }
}
