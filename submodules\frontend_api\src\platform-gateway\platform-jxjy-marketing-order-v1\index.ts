import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjy-marketing-order-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjy-marketing-order-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class DeliveryAddress {
  consignee: string
  phone: string
  region: string
  address: string
}

export class TakePoint {
  pickupLocation: string
  pickupTime: string
  remark?: string
}

/**
 * 发票信息
<AUTHOR>
@since 2021/3/23
 */
export class InvoiceInfoRequest {
  /**
   * 发票抬头
   */
  title?: string
  /**
   * 发票抬头类型
<pre>
1-个人
2-企业
</pre>
   */
  titleType?: number
  /**
   * 发票类型
<pre>
1-电子发票
2-纸质发票
</pre>
   */
  invoiceType?: number
  /**
   * 发票种类
<pre>
1-普通发票
2-增值税普通发票
3-增值税专用发票
</pre>
   */
  invoiceCategory?: number
  /**
   * 购买方纳税人识别号
   */
  taxpayerNo?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bankName?: string
  /**
   * 账户
   */
  account?: string
  /**
   * 发票票面备注
   */
  remark?: string
  /**
   * 开票方式
1 - 线上开票
2 - 线下开票
   */
  invoiceMethod?: number
  /**
   * 联系电子邮箱
   */
  email?: string
  /**
   * 联系电话
   */
  contactPhone?: string
  /**
   * 营业执照
   */
  businessLicensePath?: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath?: string
  /**
   * 配送方式
0/1/2,无/自取/快递
@see OfflineShippingMethods
   */
  shippingMethod?: number
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddress
  /**
   * 自取点信息
   */
  takePoint?: TakePoint
}

/**
 * 推广门户id
<AUTHOR>
@date 2024/9/13 10:55
 */
export class ApplyEnterSaleChannelByPortalIdRequest {
  /**
   * 推广门户id
   */
  portalId?: string
}

/**
 * 下单商品描述
 */
export class CommodityRequest {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品授权ID(分销上下文产品分销授权ID)
   */
  commodityAuthId?: string
  /**
   * 商品skuId
   */
  commoditySkuId?: string
  /**
   * 商品使用的价格策略类型 1-定价策略 2-优惠策略
   */
  policyType?: number
  /**
   * 策略id
若使用定价策略则为定价策略id,若使用优惠策略则为优惠策略id
   */
  policyId?: string
  /**
   * 面授班时有值
   */
  issueInfo?: IssueInfo
}

export class IssueInfo {
  /**
   * 期别id
   */
  issueId?: string
  /**
   * 住宿类型
住宿类型 0-无需住宿 1-单人住宿 2-合住
   */
  accommodationType?: number
}

/**
 * <AUTHOR>
@since 2023/12/15  13:37
 */
export class CreatedMarketingOrderRequest {
  saleChannelPurchaseToken: string
  /**
   * 买家编号
   */
  buyerId: string
  /**
   * 商品列表
   */
  commodities: Array<CommodityRequest>
  /**
   * 购买渠道类型
1-用户自主购买
2-集体缴费
3-管理员导入
   */
  purchaseChannelType: number
  /**
   * 终端类型
<p>
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  terminalCode: string
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceInfoRequest
  /**
   * 参训单位id
   */
  participatingUnitId?: string
}

/**
 * 校验营销订单请求
<AUTHOR>
@since 2023/12/15  13:37
 */
export class VerifyMarketingOrderRequest {
  /**
   * 销售渠道购买凭证
   */
  saleChannelPurchaseToken: string
  /**
   * 买家编号
   */
  buyerId: string
  /**
   * 商品列表
   */
  commodities: Array<CommodityRequest>
}

/**
 * 校验结果返回
<AUTHOR> create 2021/2/3 10:53
 */
export class VerifyResultResponse {
  /**
   * 校验结果
   */
  message: string
  /**
   * 校验code
   */
  code: string
  /**
   * 订单内的商品skuId
   */
  skuId: string
}

/**
 * <AUTHOR>
@date 2024/8/16 10:38
 */
export class ApplyEnterSaleChannelResponse {
  /**
   * 销售渠道购买凭证,目前为销售渠道id
   */
  saleChannelPurchaseToken: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 创建营销订单结果
<AUTHOR> create 2021/1/29 17:27
 */
export class CreateMarketingOrderResponse {
  /**
   * 是否创建成功
对应false code
&quot;62000&quot;,&quot;商品无效: 网校不在服务期限内&quot;
&quot;62001&quot;,&quot;商品无效: 网校没有开启分销服务&quot;
&quot;62002&quot;,&quot;商品无效: 分销商分销关系没有启用&quot;
&quot;62003&quot;,&quot;商品无效: 分销关系不在合同有效期内&quot;
&quot;62004&quot;,&quot;商品无效: 分销授权地区为空&quot;
&quot;62006&quot;,&quot;商品无效: 分销商品不在分销有效期内&quot;
&quot;60005&quot;,&quot;上属一级分销商在本网校的合作周期已到期&quot;
&quot;60006&quot;,&quot;上属一级分销商在本网校该商品分销有效期已到期&quot;
&quot;60014&quot;,&quot;未查询到上属一级分销商合同信息&quot;
&quot;60015&quot;,&quot;未查询到一级分销商账号信息&quot;
&quot;60016&quot;,&quot;未查询到二级分销商账号信息&quot;
&quot;60027&quot;,&quot;优惠策略时间未开始&quot;
&quot;60028&quot;,&quot;优惠策略时间已结束&quot;
&quot;60029&quot;,&quot;未查询到上级分销授权信息&quot;
&quot;60033&quot;,&quot;当前优惠策略无剩余优惠名额&quot;
&quot;60037&quot;,&quot;上属一级分销商在本网校的合作状态已中止&quot;
&quot;60040&quot;,&quot;上级分销商在本网校该商品分销授权状态已停止&quot;
&quot;60044&quot;,&quot;定价策略未启用&quot;
&quot;60045&quot;,&quot;定价策略销售地区为空&quot;
&quot;60046&quot;,&quot;定价策略不存在&quot;
&quot;60047&quot;,&quot;您的地区/单位不在分销报名范围内&quot;
&quot;60048&quot;,&quot;优惠策略未启用&quot;
&quot;60049&quot;,&quot;优惠策略不存在&quot;
   */
  success: boolean
  /**
   * 状态信息
   */
  message: string
  /**
   * 订单号，仅当{@link #success}为{@code true}时有值
   */
  orderNo: string
  /**
   * 订单创建时间，仅当{@link #success}为{@code true}时有值
   */
  createTime: string
  /**
   * 商品验证结果信息
   */
  resultList: Array<VerifyResultResponse>
}

export class SkuVerifyResponse {
  /**
   * 商品skuId
   */
  commoditySkuId: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
  /**
   * 商品对应的定价策略销售范围
   */
  pricingPolicySaleScopeRespList: Array<PricingPolicySaleScopeResp>
  /**
   * 商品对应的优惠策略策略销售范围
   */
  discountPolicySaleScopeRespList: Array<DiscountPolicySaleScopeResp>
}

export class DiscountPolicySaleScopeResp {
  /**
   * 销售范围ID
   */
  saleScopeId: string
  /**
   * 优惠策略ID
   */
  discountPolicyId: string
  /**
   * 【必传】销售区域集合
需知：当地区code存在父code时，不能存在子code 如 存在福建省code 则不能存在福州市code
   */
  regionList: Array<string>
}

export class PricingPolicySaleScopeResp {
  /**
   * 定价策略ID
   */
  pricingPolicyId: string
  /**
   * 销售范围ID
   */
  saleScopeId: string
  /**
   * 销售单位集合
   */
  saleUnitList: Array<SaleUnitResponse>
  /**
   * 【必传】销售区域集合
需知：当地区code存在父code时，不能存在子code 如 存在福建省code 则不能存在福州市code
   */
  regionList: Array<string>
  /**
   * 地区来源类型
@see com.fjhb.domain.trade.api.marketing.consts.RegionSourceType
   */
  regionSourceType: number
  /**
   * 地区来源ID
@see com.fjhb.domain.trade.api.marketing.consts.RegionSourceType
   */
  regionSourceId: string
  /**
   * 是否启用 0 否 1 是
   */
  enabled: boolean
}

export class SaleUnitResponse {
  /**
   * 单位名称
   */
  name: string
  /**
   * 统一社会信用代码
   */
  creditCode: string
}

/**
 * 创建营销订单结果
<AUTHOR> create 2021/1/29 17:27
 */
export class VerifyMarketingOrderResponse {
  /**
   * 是否创建成功
对应false code
&quot;62000&quot;,&quot;商品无效: 网校不在服务期限内&quot;
&quot;62001&quot;,&quot;商品无效: 网校没有开启分销服务&quot;
&quot;62002&quot;,&quot;商品无效: 分销商分销关系没有启用&quot;
&quot;62003&quot;,&quot;商品无效: 分销关系不在合同有效期内&quot;
&quot;62004&quot;,&quot;商品无效: 分销授权地区为空&quot;
&quot;62006&quot;,&quot;商品无效: 分销商品不在分销有效期内&quot;
&quot;62007&quot;,&quot;商品无效: 定价策略未设置为启用&quot;
&quot;62008&quot;,&quot;商品无效: 优惠无效&quot;
&quot;60005&quot;,&quot;上属一级分销商在本网校的合作周期已到期&quot;
&quot;60006&quot;,&quot;上属一级分销商在本网校该商品分销有效期已到期&quot;
&quot;60014&quot;,&quot;未查询到上属一级分销商合同信息&quot;
&quot;60015&quot;,&quot;未查询到一级分销商账号信息&quot;
&quot;60016&quot;,&quot;未查询到二级分销商账号信息&quot;
&quot;60027&quot;,&quot;优惠策略时间未开始&quot;
&quot;60028&quot;,&quot;优惠策略时间已结束&quot;
&quot;60029&quot;,&quot;未查询到上级分销授权信息&quot;
&quot;60033&quot;,&quot;当前优惠策略无剩余优惠名额&quot;
&quot;60037&quot;,&quot;上属一级分销商在本网校的合作状态已中止&quot;
&quot;60040&quot;,&quot;上级分销商在本网校该商品分销授权状态已停止&quot;
&quot;60044&quot;,&quot;定价策略未启用&quot;
&quot;60045&quot;,&quot;定价策略销售地区为空&quot;
&quot;60046&quot;,&quot;定价策略不存在&quot;
&quot;60047&quot;,&quot;您的地区/单位不在分销报名范围内&quot;
&quot;60048&quot;,&quot;优惠策略未启用&quot;
&quot;60049&quot;,&quot;优惠策略不存在&quot;
   */
  success: boolean
  /**
   * 基础校验信息，如参数填写有误，购买渠道失效等
   */
  message: string
  /**
   * 商品校验结果
   */
  skuVerifyResponseList: Array<SkuVerifyResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请进入分销销售渠道
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyEnterSaleChannelByPortalId(
    request: ApplyEnterSaleChannelByPortalIdRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyEnterSaleChannelByPortalId,
    operation?: string
  ): Promise<Response<ApplyEnterSaleChannelResponse>> {
    return commonRequestApi<ApplyEnterSaleChannelResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建门户分销订单
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createMarketingOrder(
    request: CreatedMarketingOrderRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createMarketingOrder,
    operation?: string
  ): Promise<Response<CreateMarketingOrderResponse>> {
    return commonRequestApi<CreateMarketingOrderResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验门户分销订单
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifyMarketingOrder(
    request: VerifyMarketingOrderRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.verifyMarketingOrder,
    operation?: string
  ): Promise<Response<VerifyMarketingOrderResponse>> {
    return commonRequestApi<VerifyMarketingOrderResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
