export class TimeRange {
  constructor(startTime?: string, endTime?: string) {
    this.startTime = startTime ?? ''
    this.endTime = endTime ?? ''
  }
  /**
   * 起始时间
   */
  startTime = ''
  /**
   * 结束时间
   */
  endTime = ''
  set time(array: Array<string>) {
    this.startTime = array?.[0] || ''
    this.endTime = array?.[1] || ''
  }
  get time(): Array<string> {
    return [this.startTime, this.endTime]
  }
}
export default class RuleInfo {
  /**
   * 是否错开学习时间
   */
  staggerStartTrainingTime = true
  /**
   * 每天学习时间段
   */
  learningTimeRange: TimeRange = new TimeRange('00:00:00', '23:59:59')
  /**
   * 每天不学习时间段
   */
  notLearningTimes: Array<TimeRange> = [
    new TimeRange('00:00:00', '08:00:00'),
    new TimeRange('12:00:00', '14:00:00'),
    new TimeRange('22:00:00', '23:59:59')
  ]
  /**
   * 首次开始学习时间
   */
  firstLearningStartTime = 3
  /**
   * 首次开始学习时间
   */
  firstLearningEndTime = 5
  /**
   * 每天最多学习时长
   */
  maxLearningTime = 8
  /**
   * 每次学习时长
   */
  learningTime = 4
  /**
   * 最小提前天数
   */
  minAdvanceDay = 5
  /**
   * 最大提前天数
   */
  maxAdvanceDay = 10

  // 添加不学习时间
  addNoStudyTime(timeRange: TimeRange) {
    this.notLearningTimes.push(timeRange)
  }
  // 添加不学习时间
  removeNoStudyTime(index: number) {
    this.notLearningTimes.splice(index, 1)
  }
}
