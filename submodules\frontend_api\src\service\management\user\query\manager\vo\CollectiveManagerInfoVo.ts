import { CollectiveInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

/**
 * @description 集体缴费管理员详情
 */
class CollectiveManagerInfoVo extends CollectiveInfoResponse {
  /**
   * 用户id
   */
  userId: string

  /**
   * 用户名称
   */
  userName: string

  /**
   * 证件号
   */
  idCard: string

  /**
   * 手机号
   */
  phone: string

  /**
   * 注册时间
   */
  createTime: string

  static from(response: CollectiveInfoResponse): CollectiveManagerInfoVo {
    const detail = new CollectiveManagerInfoVo()
    detail.userId = response.userInfo?.userId ?? ''
    detail.userName = response.userInfo?.userName ?? ''
    detail.idCard = response.userInfo?.idCard ?? ''
    detail.phone = response.userInfo?.phone ?? ''
    detail.createTime = response.accountInfo?.createdTime ?? ''
    return detail
  }
}

export default CollectiveManagerInfoVo
