<template>
  <el-container>
    <el-aside width="240px">
      <!--侧边栏按钮 展开时显示-->
      <a href="#" class="aside-btn el-icon-s-fold"></a>
      <!--侧边栏按钮 收起时显示-->
      <!--<a href="#" class="aside-btn el-icon-s-unfold"></a>-->
      <div class="logo">
        <span class="logo-txt">培训管理平台</span>
      </div>
      <div class="user-info">
        <img class="photo" src="./assets/images/default-photo.jpg" alt=" " />
        <p class="name">张某某</p>
        <div class="op-btn f-mt10">
          <el-button type="primary" size="mini" round plain>
            <i class="el-icon-s-tools"></i>
            帐号设置
          </el-button>
        </div>
      </div>
      <el-menu default-active="8" class="aside-nav" unique-opened="true" @open="handleOpen" @close="handleClose">
        <el-menu-item index="0">
          <template slot="title">
            <i class="iconfont icon-shouye1"></i>
            <span>首页</span>
          </template>
        </el-menu-item>
        <!--系统基础配置-->
        <el-submenu index="1">
          <template slot="title">
            <i class="iconfont icon-weiwangxiao"></i>
            <span>网校配置</span>
          </template>
          <el-menu-item index="1-1">基础信息配置</el-menu-item>
          <el-menu-item index="1-2">功能配置</el-menu-item>
          <el-menu-item index="1-2">修改日志</el-menu-item>
        </el-submenu>
        <el-menu-item index="12">
          <template slot="title">
            <i class="iconfont icon-zixun"></i>
            <span>资讯管理</span>
          </template>
        </el-menu-item>
        <el-submenu index="998">
          <template slot="title">
            <i class="iconfont icon-xuanzeleixing"></i>
            <span>交易信息配置</span>
          </template>
          <el-menu-item index="1-1">收款账户管理</el-menu-item>
          <el-menu-item index="1-2">报名方式配置</el-menu-item>
        </el-submenu>
        <el-menu-item index="14">
          <template slot="title">
            <i class="iconfont icon-peisong"></i>
            <span>配送渠道配置</span>
          </template>
        </el-menu-item>
        <el-submenu index="999">
          <template slot="title">
            <i class="iconfont icon-yunyingrenyuan"></i>
            <span>运营帐号管理</span>
          </template>
          <el-menu-item index="1-1">平台管理员账号管理</el-menu-item>
          <el-menu-item index="1-2">角色管理</el-menu-item>
          <el-menu-item index="1-3">地区管理员</el-menu-item>
        </el-submenu>
        <!--教学资源管理-->
        <el-menu-item index="4">
          <template slot="title">
            <i class="iconfont icon-kejian"></i>
            <span>课件管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="5">
          <template slot="title">
            <i class="iconfont icon-xuexi"></i>
            <span>课程管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="6">
          <template slot="title">
            <i class="iconfont icon-tiku"></i>
            <span>题库管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="7">
          <template slot="title">
            <i class="iconfont icon-shenqing"></i>
            <span>试题管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="8">
          <template slot="title">
            <i class="iconfont icon-shijuan"></i>
            <span>试卷管理</span>
          </template>
        </el-menu-item>
        <!--培训管理-->
        <el-menu-item index="9">
          <template slot="title">
            <i class="iconfont icon-dabao"></i>
            <span>课程包管理</span>
          </template>
        </el-menu-item>
        <el-submenu index="10">
          <template slot="title">
            <i class="iconfont icon-fangan"></i>
            <span>培训方案管理</span>
          </template>
          <el-menu-item index="1-1">新建培训方案</el-menu-item>
          <el-menu-item index="1-2">培训方案管理</el-menu-item>
          <el-menu-item index="1-3">培训属性值管理</el-menu-item>
        </el-submenu>
        <el-submenu index="100">
          <template slot="title">
            <i class="iconfont icon-jiaoyi"></i>
            <span>交易管理</span>
          </template>
          <el-submenu index="1-4">
            <template slot="title">订单管理</template>
            <el-menu-item index="1-4-1">个人报名订单</el-menu-item>
            <el-menu-item index="1-4-2">集体报名订单</el-menu-item>
          </el-submenu>
          <el-submenu index="1-5">
            <template slot="title">退款管理</template>
            <el-menu-item index="1-5-1">个人报名退款订单</el-menu-item>
            <el-menu-item index="1-5-2">集体报名退款订单</el-menu-item>
          </el-submenu>
          <el-submenu index="1-6">
            <template slot="title">发票管理</template>
            <el-menu-item index="1-5-1">个人报名发票管理</el-menu-item>
            <el-menu-item index="1-5-2">集体报名发票管理</el-menu-item>
          </el-submenu>
          <el-submenu index="1-7">
            <template slot="title">对账管理</template>
            <el-menu-item index="1-5-1">个人报名对账</el-menu-item>
            <el-menu-item index="1-5-2">集体报名对账</el-menu-item>
          </el-submenu>
          <el-submenu index="1-8">
            <template slot="title">导入开通</template>
            <el-menu-item index="1-5-1">导入学员</el-menu-item>
            <el-menu-item index="1-5-2">导入学员并开班</el-menu-item>
            <el-menu-item index="1-5-2">导入开通结果跟踪</el-menu-item>
          </el-submenu>
          <el-submenu index="1-9">
            <template slot="title">用户管理</template>
            <el-menu-item index="1-5-1">学员管理</el-menu-item>
            <el-menu-item index="1-5-2">集体报名帐号管理</el-menu-item>
          </el-submenu>
          <el-submenu index="1-10">
            <template slot="title">客服管理</template>
            <el-menu-item index="1-5-1">业务咨询</el-menu-item>
            <el-menu-item index="1-5-2">集体报名咨询</el-menu-item>
          </el-submenu>
          <el-menu-item index="1-11">
            <template slot="title">批量打印证明</template>
          </el-menu-item>
          <el-submenu index="1-12">
            <template slot="title">导入导出任务管理</template>
            <el-menu-item index="1-5-1">导入任务管理</el-menu-item>
            <el-menu-item index="1-5-2">导出任务管理</el-menu-item>
          </el-submenu>
        </el-submenu>
        <!--统计报表-->
        <el-menu-item index="13">
          <template slot="title">
            <i class="iconfont icon-ribaotongji"></i>
            <span>方案开通统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="14">
          <template slot="title">
            <i class="iconfont icon-cptj"></i>
            <span>地区开通统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="15">
          <template slot="title">
            <i class="iconfont icon-tongjiyuce"></i>
            <span>方案学习统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="16">
          <template slot="title">
            <i class="iconfont icon-tongjibaobiao"></i>
            <span>地区学习统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="17">
          <template slot="title">
            <i class="iconfont icon-mingxi"></i>
            <span>学员学习明细</span>
          </template>
        </el-menu-item>
      </el-menu>
      <div class="m-company-info">
        <p>福建华博教育科技股份有限公司</p>
        <a class="a-txt" href="http://beian.miit.gov.cn/" target="_blank">闽ICP备2021002737号</a>
      </div>
    </el-aside>
    <el-container>
      <el-header height="100px" class="f-flex">
        <ul class="header-nav f-flex-sub">
          <li class="nav-item current">
            <i class="iconfont icon-shouye"></i>
            <span class="txt">首页</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-peizhi"></i>
            <span class="txt">系统基础配置</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-kecheng"></i>
            <span class="txt">教学资源管理</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-peixun"></i>
            <span class="txt">培训管理</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-shuju"></i>
            <span class="txt">统计报表</span>
          </li>
          <li class="current-bg"></li>
        </ul>
        <ul class="header-nav">
          <li class="nav-item nav-item-1"><i class="iconfont icon-tuichu"></i>退出</li>
        </ul>
      </el-header>
      <div class="tags">
        <span class="prev"><i class="el-icon el-icon-arrow-left"></i></span>
        <div class="tags-bd">
          <div class="tags-items" style="width: 500%;">
            <el-tag class="current" closable>首页</el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
          </div>
        </div>
        <span class="next"><i class="el-icon el-icon-arrow-right"></i></span>
        <el-dropdown :hide-on-click="false">
          <span class="more"><i class="el-icon el-icon-more"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>黄金糕</el-dropdown-item>
            <el-dropdown-item>狮子头</el-dropdown-item>
            <el-dropdown-item>螺蛳粉</el-dropdown-item>
            <el-dropdown-item disabled>双皮奶</el-dropdown-item>
            <el-dropdown-item divided>蚵仔煎</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-main>
        <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb10">
          温馨提示：当前网校未开启web、h5访问。请完成网校配置后开启对外访问。
          <a href="#" class="f-link f-cb f-underline">立即前往 &gt;</a>
        </el-alert>
        <el-card shadow="never" class="m-card is-bg">
          <div class="f-flex f-align-center f-ptb10 f-plr15">
            <div class="f-flex-sub">2021年11月15日周三，欢迎登入~</div>
            <el-button type="primary" size="small">
              访问网校<i class="iconfont icon-qianwang el-icon--right"></i>
            </el-button>
          </div>
        </el-card>
        <div class="f-p20">
          <el-card shadow="never" class="m-card is-header f-mb20">
            <div slot="header" class="f-flex f-align-center">
              <span class="f-f16 f-fb f-flex-sub">网校配置概览</span>
              <a href="#" class="f-link f-cb">关闭</a>
            </div>
            <el-row type="flex" class="m-school-set is-height">
              <el-col :sm="12" :xl="6">
                <div class="item">
                  <div class="item-hd">
                    <div class="name">完善网校设置</div>
                    <!--全部配置完成-->
                    <div class="statue f-cg"><i class="el-icon-success f-mr5"></i>配置完成</div>
                    <!--部分配置完成-->
                    <!--<div class="statue f-cb">配置中（1/3）</div>-->
                    <!--未开始配置-->
                    <!--<div class="statue f-co"><i class="el-icon-warning f-mr5"></i>未配置</div>-->
                  </div>
                  <div class="item-bd">
                    <!--已完成-->
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="primary" round>门户配置</el-button>
                      <div slot="content">已配置</div>
                    </el-tooltip>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="primary" round>移动端配置</el-button>
                      <div slot="content">已配置</div>
                    </el-tooltip>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="primary" round>功能配置</el-button>
                      <div slot="content">已配置</div>
                    </el-tooltip>
                  </div>
                  <i class="el-icon-d-arrow-right arrow"></i>
                </div>
              </el-col>
              <el-col :sm="12" :xl="6">
                <div class="item">
                  <div class="item-hd">
                    <div class="name">设置收款、发票、报名方式</div>
                    <div class="statue f-cb">配置中（1/3）</div>
                  </div>
                  <div class="item-bd">
                    <!--已完成-->
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="primary" round>收款账户</el-button>
                      <div slot="content">已配置</div>
                    </el-tooltip>
                    <!--未完成-->
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="info" round>报名方式配置</el-button>
                      <div slot="content">未配置，点击前往</div>
                    </el-tooltip>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="info" round>配送渠道配置</el-button>
                      <div slot="content">未配置，点击前往</div>
                    </el-tooltip>
                  </div>
                  <i class="el-icon-d-arrow-right arrow"></i>
                </div>
              </el-col>
              <el-col :sm="12" :xl="6">
                <div class="item">
                  <div class="item-hd">
                    <div class="name">创建教学课程和试题等资源</div>
                    <div class="statue f-cb">配置中（3/4）</div>
                  </div>
                  <div class="item-bd">
                    <!--已完成-->
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="primary" round>课件配置</el-button>
                      <div slot="content">已配置</div>
                    </el-tooltip>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="primary" round>课程配置</el-button>
                      <div slot="content">已配置</div>
                    </el-tooltip>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="primary" round>试题配置</el-button>
                      <div slot="content">已配置</div>
                    </el-tooltip>
                    <!--未完成-->
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="info" round>试卷配置</el-button>
                      <div slot="content">未配置，点击前往</div>
                    </el-tooltip>
                  </div>
                  <i class="el-icon-d-arrow-right arrow"></i>
                </div>
              </el-col>
              <el-col :sm="12" :xl="6">
                <div class="item">
                  <div class="item-hd">
                    <div class="name">发布培训方案</div>
                    <div class="statue f-co"><i class="el-icon-warning f-mr5"></i>未配置</div>
                  </div>
                  <div class="item-bd">
                    <!--未完成-->
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="info" round>课程包配置</el-button>
                      <div slot="content">未配置，点击前往</div>
                    </el-tooltip>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                      <el-button type="info" round>培训方案</el-button>
                      <div slot="content">未配置，点击前往</div>
                    </el-tooltip>
                  </div>
                  <i class="el-icon-d-arrow-right arrow"></i>
                </div>
              </el-col>
            </el-row>
          </el-card>
          <el-row :gutter="20" class="is-height f-mb20">
            <el-col :span="12">
              <el-card shadow="never" class="m-card">
                <div slot="header" class="f-flex">
                  <span class="f-f16 f-fb">在线学习人数</span>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml5"></i>
                    <div slot="content">在线学习人数统计学员登录平台学习的人数，一个学员一天只能算一次</div>
                  </el-tooltip>
                </div>
                <div class="m-index-data data-1">
                  <div class="f-flex-sub">
                    <div class="icon"><i class="iconfont icon-kecheng"></i></div>
                  </div>
                  <div class="num-item">
                    <div class="num important">2,547</div>
                    <div class="tit">今日学习人数</div>
                  </div>
                  <div class="num-item">
                    <div class="num">15,700</div>
                    <div class="tit">累计学习人数</div>
                  </div>
                </div>
              </el-card>
            </el-col>
            <el-col :span="12">
              <el-card shadow="never" class="m-card">
                <div slot="header" class="f-flex">
                  <span class="f-f16 f-fb">报名人次</span>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml5"></i>
                    <div slot="content">报名人数统计网校报名人次，一个学员可以多次报名</div>
                  </el-tooltip>
                </div>
                <div class="m-index-data data-2">
                  <div class="f-flex-sub">
                    <div class="icon"><i class="iconfont icon-xueyuan1"></i></div>
                  </div>
                  <div class="num-item">
                    <div class="num important">54</div>
                    <div class="tit">今日报名人次</div>
                  </div>
                  <div class="num-item">
                    <div class="num">2,587</div>
                    <div class="tit">累积报名人次</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
          <el-row :gutter="20" class="is-height">
            <el-col :span="16">
              <el-card shadow="never" class="m-card">
                <div slot="header" class="f-flex">
                  <span class="f-f16 f-fb">交易概述</span>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml5"></i>
                    <div slot="content">按时间统计网校交易的金额</div>
                  </el-tooltip>
                </div>
                <div class="m-index-chart f-tc">
                  <!--以下为图表实例图-->
                  <img style="max-width: 100%" src="./assets/images/chart-1.png" alt="" />
                </div>
              </el-card>
            </el-col>
            <el-col :span="8">
              <el-card shadow="never" class="m-card">
                <div slot="header" class="f-flex">
                  <span class="f-f16 f-fb">方案报名排行</span>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml5"></i>
                    <div slot="content">统计网校当前报名最多的培训班前十位</div>
                  </el-tooltip>
                </div>
                <div class="m-index-rank">
                  <div class="item hd">
                    <div class="rank">排行</div>
                    <div class="name">方案名称</div>
                    <div class="num">成交量</div>
                  </div>
                  <div class="item">
                    <!--第一 first，第二 second，第三 third-->
                    <div class="rank first"><i class="rank-num">1</i></div>
                    <div class="name">方案名称方案名称方案</div>
                    <div class="num">10</div>
                  </div>
                  <div class="item">
                    <div class="rank second"><i class="rank-num">2</i></div>
                    <div class="name">
                      方案名称方案名称方案名称方案名称方案名称方案名称方案名称方案名称方案名称方案名称
                    </div>
                    <div class="num">9</div>
                  </div>
                  <div class="item">
                    <div class="rank third"><i class="rank-num">3</i></div>
                    <div class="name">方案名称方案名称方案名称方案名称</div>
                    <div class="num">8</div>
                  </div>
                  <div class="item">
                    <div class="rank"><i class="rank-num">4</i></div>
                    <div class="name">方案名称方案名称方案名称</div>
                    <div class="num">7</div>
                  </div>
                  <div class="item">
                    <div class="rank"><i class="rank-num">5</i></div>
                    <div class="name">方案名称方案名称方案名称方案名称方案名称方案名称方案名称</div>
                    <div class="num">6</div>
                  </div>
                  <div class="item">
                    <div class="rank"><i class="rank-num">6</i></div>
                    <div class="name">方案名称方案名称</div>
                    <div class="num">5</div>
                  </div>
                  <div class="item">
                    <div class="rank"><i class="rank-num">7</i></div>
                    <div class="name">方案名称方案名称方案名称方案名称</div>
                    <div class="num">4</div>
                  </div>
                  <div class="item">
                    <div class="rank"><i class="rank-num">8</i></div>
                    <div class="name">方案名称方案名称方案名称</div>
                    <div class="num">3</div>
                  </div>
                  <div class="item">
                    <div class="rank"><i class="rank-num">9</i></div>
                    <div class="name">方案名称方案名称方案名称方案名称</div>
                    <div class="num">2</div>
                  </div>
                  <div class="item">
                    <div class="rank"><i class="rank-num">10</i></div>
                    <div class="name">方案名称方案名称方案名称方案名称方案名称</div>
                    <div class="num">1</div>
                  </div>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
        <div class="m-layout">
          <!--服务即将到期 样式-->
          <div class="item is-warning">
            <i class="el-icon-warning icon"></i>
            <div class="bd">
              <p class="f-fb">服务即将到期</p>
              <p class="f-f12">有效期剩余：30天</p>
            </div>
          </div>
          <!--服务今日到期 样式-->
          <div class="item is-important">
            <i class="el-icon-warning icon"></i>
            <div class="bd">
              <p class="f-fb">服务今日到期</p>
            </div>
          </div>
          <div class="item f-csp">
            <i class="el-icon-s-tools icon"></i>
            <div class="bd">打开配置概览</div>
          </div>
          <div class="item f-csp">
            <i class="el-icon-s-opportunity icon"></i>
            <div class="bd">查看新手引导</div>
          </div>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
