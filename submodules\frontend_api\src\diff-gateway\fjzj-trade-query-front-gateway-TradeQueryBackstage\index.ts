import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'fjzj-trade-query-front-gateway-TradeQueryBackstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum ReturnOrderSortField {
  APPLIED_TIME = 'APPLIED_TIME'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class BigDecimalScopeRequest {
  begin?: number
  end?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class CommodityAuthInfoRequest {
  distributorId?: string
  distributionLevel?: number
  superiorDistributorId?: string
  supplierId?: string
  salesmanId?: string
}

export class CommoditySkuRequest {
  commoditySkuIdList?: Array<string>
  saleTitle?: string
  issueInfo?: IssueInfo1
  skuProperty?: SkuPropertyRequest
  externalTrainingPlatform?: Array<string>
  trainingInstitution?: Array<string>
}

export class RegionSkuPropertyRequest {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest {
  regionSearchType?: number
  region?: Array<RegionSkuPropertyRequest>
}

export class SkuPropertyRequest {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  trainingObject?: Array<string>
  positionCategory?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  grade?: Array<string>
  subject?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  trainingChannelIds?: Array<string>
  certificatesType?: Array<string>
  practitionerCategory?: Array<string>
  qualificationCategory?: Array<string>
  trainingForm?: Array<string>
}

export class IssueInfo1 {
  issueId?: string
  issueName?: string
  issueNum?: string
  trainStartTime?: string
  trainEndTime?: string
  sourceType?: string
  sourceId?: string
}

export class ReturnOrderRequestInDistributor {
  returnOrderNoList?: Array<string>
  basicData?: ReturnOrderBasicDataRequest
  approvalInfo?: ReturnOrderApprovalInfoRequest
  returnCommoditySkuIdList?: Array<string>
  refundCommoditySkuIdList?: Array<string>
  subOrderInfo?: SubOrderInfoRequest
  commodityAuthInfo?: CommodityAuthInfoRequest
  portalId?: string
  isDistributionExcludePortal?: boolean
  returnCommodity?: CommoditySkuRequest
  refundCommodity?: CommoditySkuRequest
}

export class ReturnSortRequest {
  field?: ReturnOrderSortField
  policy?: SortPolicy
}

export class OrderInfoRequest {
  orderNoList?: Array<string>
  batchOrderNoList?: Array<string>
  buyerIdList?: Array<string>
  receiveAccountIdList?: Array<string>
  flowNoList?: Array<string>
  channelTypesList?: Array<number>
  terminalCodeList?: Array<string>
  saleChannel?: number
  saleChannels?: Array<number>
  saleChannelName?: string
  saleChannelIds?: Array<string>
  policyTrainingSchemeIdList?: Array<string>
  declarationUnitCodeList?: Array<string>
}

export class ReturnCloseReasonRequest {
  closeTypeList?: Array<number>
}

export class ReturnOrderApprovalInfoRequest {
  approveTime?: DateScopeRequest
}

export class ReturnOrderBasicDataRequest {
  returnOrderStatus?: Array<number>
  returnOrderTypes?: Array<number>
  applySourceType?: string
  applySourceIdList?: Array<string>
  returnCloseReason?: ReturnCloseReasonRequest
  returnStatusChangeTime?: ReturnOrderStatusChangeTimeRequest
  refundAmountScope?: BigDecimalScopeRequest
}

export class ReturnOrderStatusChangeTimeRequest {
  applied?: DateScopeRequest
  returnCompleted?: DateScopeRequest
}

export class SubOrderInfoRequest {
  subOrderNoList?: Array<string>
  orderInfo?: OrderInfoRequest
  discountType?: number
  useDiscount?: boolean
}

/**
 * 商品查询参数
<AUTHOR>
@date 2022/05/11
 */
export class FJZJCommoditySkuRequest {
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 学习方案查询参数
   */
  scheme?: FJZJSchemeRequest
}

/**
 * 方案查询参数
<AUTHOR>
@date 2022/05/11
 */
export class FJZJSchemeRequest {
  /**
   * 培训方案ID
   */
  schemeIdList?: Array<string>
  /**
   * 方案类型
@see SchemeType
   */
  schemeType?: string
  /**
   * 方案学时
   */
  schemePeriodScope?: DoubleScopeRequest
}

/**
 * 商品开通统计报表查询参数
<AUTHOR>
@date 2022/05/11
 */
export class FJZJTradeReportRequest {
  /**
   * 交易时间范围
   */
  tradeTime?: DateScopeRequest
  /**
   * 买家所在地区路径
   */
  buyerAreaPath?: Array<string>
  /**
   * 商品查询条件
   */
  commoditySku?: FJZJCommoditySkuRequest
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 3-华医网 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 排除的销售渠道
0-自营 1-分销 2-专题 3-华医网
   */
  excludedSaleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 查看非推广门户数据 | true 为勾选效果
   */
  notDistributionPortal?: boolean
  /**
   * 收款账户
   */
  receiveAccountIdList?: Array<string>
  /**
   * 期别id
   */
  issueId?: Array<string>
  /**
   * 机构ID集合
   */
  institutionIdList?: Array<string>
}

/**
 * 退货单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderRequest {
  /**
   * 单位id集合
   */
  unitIdList?: Array<string>
  /**
   * 退货单号
   */
  returnOrderNoList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: ReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: ReturnOrderApprovalInfoRequest
  /**
   * 退货商品id集合
   */
  returnCommoditySkuIdList?: Array<string>
  /**
   * 退货商品查询条件
   */
  returnCommodity?: CommoditySkuRequest
  /**
   * 退款商品id集合
   */
  refundCommoditySkuIdList?: Array<string>
  /**
   * 退款商品查询条件
   */
  refundCommodity?: CommoditySkuRequest
  /**
   * 退货单关联子订单查询参数
   */
  subOrderInfo?: SubOrderInfoRequest
  /**
   * 商品分销授权信息
   */
  commodityAuthInfo?: CommodityAuthInfoRequest
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

export class DiscountPolicyModel {
  discountPolicyId: string
  discountId: number
}

export class RegionModel {
  regionId: string
  province: string
  city: string
  county: string
  path: string
}

export class UserModel {
  userId: string
}

export class BatchReturnOrderResponse {
  batchReturnOrderNo: string
  basicData: BatchReturnOrderBasicDataResponse
  batchOrderInfo: BatchOrderInfoResponse
  needManualApprove: boolean
  approvalInfo: BatchReturnApprovalInfoResponse
  confirmUser: UserResponse
  refundInfo: RefundInfoResponse
}

export class BatchOrderInfoResponse {
  batchOrderNo: string
  paymentInfo: PaymentInfoResponse
  creator: UserResponse
}

export class BatchReturnApprovalInfoResponse {
  approveStatus: number
  approveResult: number
  approveUser: UserResponse
  approveComment: string
  approveTime: string
}

export class BatchReturnOrderApplyInfoResponse {
  applyUser: UserResponse
  reasonId: string
  reasonContent: string
  description: string
}

export class BatchReturnOrderBasicDataResponse {
  batchReturnOrderType: number
  refundAmount: number
  returnOrderCount: number
  batchReturnOrderStatus: number
  batchReturnOrderStatusChangeTime: BatchReturnOrderStatusChangeTimeResponse
  applyInfo: BatchReturnOrderApplyInfoResponse
  closeReason: BatchReturnOrderCloseReasonResponse
  saleChannel: number
  saleChannelId: string
  saleChannelName: string
  salePathList: Array<SalePathResponse>
}

export class BatchReturnOrderCloseReasonResponse {
  closeType: number
  cancelUser: UserResponse
  cancelReason: string
  confirmFailureMessage: string
}

export class BatchReturnOrderStatusChangeTimeResponse {
  created: string
  confirmed: string
  cancelApplying: string
  returning: string
  returnFailed: string
  refundApplying: string
  refundApplied: string
  refunding: string
  refundFailed: string
  returned: string
  refunded: string
  returnedAndRefunded: string
  returnCompleted: string
  closed: string
}

export interface ResourceResponse {
  resourceType: string
}

export class SchemeResourceResponse implements ResourceResponse {
  schemeId: string
  schemeName: string
  period: number
  schemeType: string
  resourceType: string
}

export class CommodityAuthInfoResponse {
  commodityAuthId: string
  distributorId: string
  distributorName: string
  distributionLevel: number
  superiorDistributorId: string
  superiorDistributorName: string
  distributorIdPath: string
  supplierId: string
  supplierName: string
  salesmanId: string
  salesmanName: string
  distributorPhone: string
  distributorUnitCreditCode: string
  distributorPartnerType: number
  supplierPartnerType: number
}

export class CommoditySkuPropertyResponse {
  year: SkuPropertyResponse
  province: SkuPropertyResponse
  city: SkuPropertyResponse
  county: SkuPropertyResponse
  industry: SkuPropertyResponse
  subjectType: SkuPropertyResponse
  trainingCategory: SkuPropertyResponse
  trainingProfessional: SkuPropertyResponse
  technicalGrade: SkuPropertyResponse
  trainingObject: SkuPropertyResponse
  positionCategory: SkuPropertyResponse
  jobLevel: SkuPropertyResponse
  jobCategory: SkuPropertyResponse
  grade: SkuPropertyResponse
  subject: SkuPropertyResponse
  learningPhase: SkuPropertyResponse
  discipline: SkuPropertyResponse
  certificatesType: SkuPropertyResponse
  practitionerCategory: SkuPropertyResponse
  qualificationCategory: SkuPropertyResponse
  trainingWay: SkuPropertyResponse
}

export class CommoditySkuResponse {
  commoditySkuId: string
  saleTitle: string
  commodityPicturePath: string
  price: number
  originalPrice: number
  enableSpecialPrice: boolean
  showPrice: boolean
  skuProperty: CommoditySkuPropertyResponse
  resource: ResourceResponse
  tppTypeId: string
  externalTrainingPlatform: string
  trainingInstitution: string
  issueInfo: IssueInfo
}

export class DiscountSchemeResponse {
  specialPrice: number
  discountPolicyList: Array<DiscountPolicyModel>
  discountType: number
  hasEnabled: boolean
}

export class IssueInfo {
  issueId: string
  issueName: string
  issueNum: string
  trainStartTime: string
  trainEndTime: string
  sourceType: string
  sourceId: string
}

export class OwnerInfoResponse {
  servicerId: string
  servicerName: string
}

export class PaymentInfoResponse {
  payAmount: number
  flowNo: string
  receiveAccountId: string
  paymentOrderType: number
}

export class PricingPolicyResponse {
  pricingPolicyId: string
  price: number
  hasEnabled: boolean
}

export class RefundInfoResponse {
  refundOrderNo: string
  refundOrderType: number
  refundOrderStatus: number
  refundOrderStatusChangeTime: RefundOrderStatusChangeTimeResponse
  refundFlow: string
  refundAmount: number
  refundFailReason: string
  refundConfirmedTime: string
}

export class SalePathResponse {
  id: string
  fullPath: string
  currentPath: string
  currentPathLastCode: string
  currentPathLastType: number
  isLast: boolean
}

export class UserResponse {
  userId: string
  userArea: RegionModel
  managementUnitRegionCode: RegionModel
  jobCategoryId: string
  professionalLevel: number
  jobCategoryName: string
}

export class SkuPropertyResponse {
  skuPropertyValueId: string
  skuPropertyValueName: string
  skuPropertyValueShowName: string
}

export class OrderInfoResponse {
  orderNo: string
  orderType: number
  batchOrderNo: string
  channelType: number
  terminalCode: string
  orderPaymentInfo: PaymentInfoResponse
  buyer: UserResponse
  creator: UserResponse
  saleChannel: number
  saleChannelId: string
  saleChannelName: string
  policyTrainingSchemeIds: string
  declarationUnitCode: string
}

export class RefundCommodityResponse {
  quantity: number
  commoditySku: CommoditySkuResponse
}

export class RefundOrderStatusChangeTimeResponse {
  waiting: string
  refunding: string
  refunded: string
  failed: string
}

export class ReturnApprovalInfoResponse {
  approveStatus: number
  approveResult: number
  approveUser: UserResponse
  approveComment: string
  approveTime: string
  cancelApproveTime: string
}

export class ReturnCloseReasonResponse {
  closeType: number
  cancelUser: UserModel
  cancelReason: string
}

export class ReturnCommodityResponse {
  quantity: number
  commoditySku: CommoditySkuResponse
}

export class ReturnOrderApplyInfoResponse {
  applyUser: UserResponse
  reasonId: string
  reasonContent: string
  description: string
}

export class ReturnOrderBasicDataResponse {
  returnOrderType: number
  refundAmount: number
  returnOrderStatus: number
  returnOrderStatusChangeTime: ReturnOrderStatusChangeTimeResponse
  applyInfo: ReturnOrderApplyInfoResponse
  returnFailReason: string
  returnCloseReason: ReturnCloseReasonResponse
  applySourceType: string
  applySourceId: string
  saleChannel: number
  saleChannelId: string
  saleChannelName: string
}

export class ReturnOrderStatusChangeTimeResponse {
  applied: string
  cancelApplying: string
  returning: string
  returnFailed: string
  refundApplying: string
  refundApplied: string
  refunding: string
  refundFailed: string
  returned: string
  refunded: string
  returnedAndRefunded: string
  returnCompleted: string
  closed: string
}

export class SubOrderInfoResponse {
  subOrderNo: string
  exchanged: boolean
  orderInfo: OrderInfoResponse
  quantity: number
  discountSourceId: string
  discountType: number
  useDiscount: boolean
  saleChannel: number
  discountScheme: DiscountSchemeResponse
  pricingPolicy: PricingPolicyResponse
  salePathList: Array<SalePathResponse>
  isExchangeIssue: boolean
  amount: number
  finalPrice: number
}

export class ReportSummaryResponse {
  totalNetAmount: number
  tradeCountSummaryInfo: SubOrderStatisticDto
  purchaseChannelStatisticInfoList: Array<PurchaseChannelStatisticDto>
}

export class PaymentTypeStatisticDto {
  paymentType: number
  statisticInfo: SubOrderStatisticDto
}

export class PurchaseChannelStatisticDto {
  purchaseChannel: number
  paymentTypeStatisticInfoList: Array<PaymentTypeStatisticDto>
}

export class SubOrderStatisticDto {
  tradeSuccessCount: number
  returnCount: number
  exchangeInCount: number
  exchangeOutCount: number
  netTradeSuccessCount: number
}

export class PaymentTypeStatisticDto1 {
  paymentType: number
  statisticInfo: SubOrderStatisticDto1
}

export class PurchaseChannelStatisticDto1 {
  purchaseChannel: number
  paymentTypeStatisticInfoList: Array<PaymentTypeStatisticDto1>
}

export class SubOrderStatisticDto1 {
  tradeSuccessCount: number
  returnCount: number
  exchangeInCount: number
  exchangeOutCount: number
  netTradeSuccessCount: number
}

/**
 * 组合商品开通统计信息
<AUTHOR>
@date 2022/05/10
 */
export class CombinationCommodityOpenReportFormResponse {
  /**
   * 商品ID
   */
  commoditySkuId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方案名称
   */
  schemeName: string
  /**
   * 学习方案学时
   */
  period: string
  /**
   * 组合开班统计信息
   */
  combinationStatisticList: Array<CombinationStatisticDto>
}

/**
 * 退货单网关模型
 */
export class ReturnOrderResponse {
  /**
   * 退货单号
   */
  returnOrderNo: string
  /**
   * 退货单基本信息
   */
  basicData: ReturnOrderBasicDataResponse
  /**
   * 退货单是否需要审批
   */
  needApprove: boolean
  /**
   * 退货单审批信息
   */
  approvalInfo: ReturnApprovalInfoResponse
  /**
   * 退款确认人
   */
  confirmUser: UserResponse
  /**
   * 退货单关联退款单信息
   */
  refundInfo: RefundInfoResponse
  /**
   * 退货商品信息
   */
  returnCommodity: ReturnCommodityResponse
  /**
   * 退款商品信息
   */
  refundCommodity: RefundCommodityResponse
  /**
   * 退货子订单信息
   */
  subOrderInfo: SubOrderInfoResponse
  /**
   * 来源批次退货单信息
   */
  batchReturnOrder: BatchReturnOrderResponse
  /**
   * 退货商品分销信息（仅分销订单的退货单有值）
   */
  commodityAuthInfo: CommodityAuthInfoResponse
  /**
   * 归属信息
   */
  ownerInfo: OwnerInfoResponse
  /**
   * 是否需要人工确认退款
   */
  needConfirmRefund: boolean
  /**
   * 退货单扩展信息
key:courseType,华医部分退款
value:1-专业课   2-公需课  3-都退
   */
  ext: Map<string, string>
}

export class CombinationStatisticDto {
  /**
   * 类型  0公需  1专业
   */
  type: number
  /**
   * 合计数据
   */
  summaryInfo: SubOrderStatisticDto1
  /**
   * 各渠道统计信息
   */
  purchaseChannelStatisticInfoList: Array<PurchaseChannelStatisticDto1>
}

export class CombinationCommodityOpenReportFormResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CombinationCommodityOpenReportFormResponse>
}

export class ReturnOrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ReturnOrderResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取组合报表统计合计数据
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCombinationCommodityReportSummaryInServicer(
    request: FJZJTradeReportRequest,
    query: DocumentNode = GraphqlImporter.getCombinationCommodityReportSummaryInServicer,
    operation?: string
  ): Promise<Response<ReportSummaryResponse>> {
    return commonRequestApi<ReportSummaryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商- 获取退货单详情
   * @param query 查询 graphql 语法文档
   * @param returnOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getReturnOrderInDistributor(
    returnOrderNo: string,
    query: DocumentNode = GraphqlImporter.getReturnOrderInDistributor,
    operation?: string
  ): Promise<Response<ReturnOrderResponse>> {
    return commonRequestApi<ReturnOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { returnOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取退货单详情
   * @param returnOrderNo : 退货单号
   * @return 退货单信息
   * @param query 查询 graphql 语法文档
   * @param returnOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getReturnOrderInServicer(
    returnOrderNo: string,
    query: DocumentNode = GraphqlImporter.getReturnOrderInServicer,
    operation?: string
  ): Promise<Response<ReturnOrderResponse>> {
    return commonRequestApi<ReturnOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { returnOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取商品开通统计列表（含所有商品）
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCombinationOpenReportFormsInServicer(
    params: { page?: Page; request?: FJZJTradeReportRequest },
    query: DocumentNode = GraphqlImporter.pageCombinationOpenReportFormsInServicer,
    operation?: string
  ): Promise<Response<CombinationCommodityOpenReportFormResponsePage>> {
    return commonRequestApi<CombinationCommodityOpenReportFormResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-  退货单分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageReturnOrderInDistributor(
    params: { page?: Page; request?: ReturnOrderRequestInDistributor; sort?: Array<ReturnSortRequest> },
    query: DocumentNode = GraphqlImporter.pageReturnOrderInDistributor,
    operation?: string
  ): Promise<Response<ReturnOrderResponsePage>> {
    return commonRequestApi<ReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 退货单分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageReturnOrderInServicer(
    params: { page?: Page; request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    query: DocumentNode = GraphqlImporter.pageReturnOrderInServicer,
    operation?: string
  ): Promise<Response<ReturnOrderResponsePage>> {
    return commonRequestApi<ReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 退货单分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageReturnOrderInTrainingChannel(
    params: { page?: Page; request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    query: DocumentNode = GraphqlImporter.pageReturnOrderInTrainingChannel,
    operation?: string
  ): Promise<Response<ReturnOrderResponsePage>> {
    return commonRequestApi<ReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
