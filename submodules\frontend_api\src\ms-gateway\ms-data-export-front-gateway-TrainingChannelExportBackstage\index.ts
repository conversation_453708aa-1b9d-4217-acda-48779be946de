import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-data-export-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-data-export-front-gateway-TrainingChannelExportBackstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum TrainingChannelEnum {
  publishedTime = 'publishedTime'
}

// 类

export class TrainingChannelSortKParam {
  sortField?: TrainingChannelEnum
  sortType?: SortTypeEnum
}

export class DateScopeRequest {
  beginTime?: string
  endTime?: string
}

export class TrainingChannelRequest {
  /**
   * 专题名称
   */
  name?: string
  /**
   * 专题入口名称
   */
  entryName?: string
  /**
   * 专题类型
AREA &#x3D; 1; 地区
INDUSTRY &#x3D; 2；行业
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.TrainingChannelTypes
   */
  type?: number
  /**
   * 行业id
   */
  industryId?: string
  /**
   * 地区路径
   */
  regionPath?: string
  /**
   * 启用状态（0：停用 1：启用）
   */
  enable?: boolean
  /**
   * 是否显示在网校
   */
  showOnNetSchool?: boolean
  /**
   * 排序
   */
  sort?: number
  /**
   * 编辑时间范围
   */
  createdDateScope?: DateScopeRequest
  /**
   * 排序
   */
  sortList?: Array<TrainingChannelSortKParam>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出专题列表
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingChannel(
    request: TrainingChannelRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportTrainingChannel,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
