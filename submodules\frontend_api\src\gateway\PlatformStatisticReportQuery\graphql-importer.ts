import countUserOnlineCount from './queries/countUserOnlineCount.graphql'
import createBatchExportLearningLogTask from './queries/createBatchExportLearningLogTask.graphql'
import createBatchExportLearningLogTaskForChannelVendor from './queries/createBatchExportLearningLogTaskForChannelVendor.graphql'
import createBatchExportLearningLogTaskForParticipatingUnit from './queries/createBatchExportLearningLogTaskForParticipatingUnit.graphql'
import createBatchExportLearningLogTaskForTrainingInstitution from './queries/createBatchExportLearningLogTaskForTrainingInstitution.graphql'
import createExportLearningLogTask from './queries/createExportLearningLogTask.graphql'
import createExportLearningLogTaskForChannelVendor from './queries/createExportLearningLogTaskForChannelVendor.graphql'
import createExportLearningLogTaskForParticipatingUnit from './queries/createExportLearningLogTaskForParticipatingUnit.graphql'
import createExportLearningLogTaskForTrainingInstitution from './queries/createExportLearningLogTaskForTrainingInstitution.graphql'
import exportAnswerQuestionStatistic from './queries/exportAnswerQuestionStatistic.graphql'
import exportCourseAppraiseStatistic from './queries/exportCourseAppraiseStatistic.graphql'
import exportCourseAppraiseStatisticForCoursewareSupplier from './queries/exportCourseAppraiseStatisticForCoursewareSupplier.graphql'
import exportCourseAppraiseStatisticForTrainingInstitution from './queries/exportCourseAppraiseStatisticForTrainingInstitution.graphql'
import exportLearningDateHistogramForChannelVendor from './queries/exportLearningDateHistogramForChannelVendor.graphql'
import exportLearningDateHistogramForCoursewareSupplier from './queries/exportLearningDateHistogramForCoursewareSupplier.graphql'
import exportLearningDateHistogramForParticipatingUnit from './queries/exportLearningDateHistogramForParticipatingUnit.graphql'
import exportLearningDateHistogramForTrainingInstitution from './queries/exportLearningDateHistogramForTrainingInstitution.graphql'
import exportOpenDateHistogramForChannelVendor from './queries/exportOpenDateHistogramForChannelVendor.graphql'
import exportOpenDateHistogramForCoursewareSupplier from './queries/exportOpenDateHistogramForCoursewareSupplier.graphql'
import exportOpenDateHistogramForTrainingInstitution from './queries/exportOpenDateHistogramForTrainingInstitution.graphql'
import exportRegionLearningStatistic from './queries/exportRegionLearningStatistic.graphql'
import exportRegionLearningStatisticForTrainingInstitution from './queries/exportRegionLearningStatisticForTrainingInstitution.graphql'
import exportSchemeLearningStatistic from './queries/exportSchemeLearningStatistic.graphql'
import exportSchemeLearningStatisticForTrainingInstitution from './queries/exportSchemeLearningStatisticForTrainingInstitution.graphql'
import exportSchemeOpenStatistic from './queries/exportSchemeOpenStatistic.graphql'
import exportSchemeOpenStatisticForChannelVendor from './queries/exportSchemeOpenStatisticForChannelVendor.graphql'
import exportSchemeOpenStatisticForCoursewareSupplier from './queries/exportSchemeOpenStatisticForCoursewareSupplier.graphql'
import exportSchemeOpenStatisticForTrainingInstitution from './queries/exportSchemeOpenStatisticForTrainingInstitution.graphql'
import exportTrainingInstitutionCourseAppraiseStatistic from './queries/exportTrainingInstitutionCourseAppraiseStatistic.graphql'
import exportTrainingInstitutionLearningStatistic from './queries/exportTrainingInstitutionLearningStatistic.graphql'
import exportTrainingInstitutionOpenStatistic from './queries/exportTrainingInstitutionOpenStatistic.graphql'
import exportTrainingInstitutionOpenStatisticForChannelVendor from './queries/exportTrainingInstitutionOpenStatisticForChannelVendor.graphql'
import exportTrainingInstitutionOpenStatisticForTrainingInstitution from './queries/exportTrainingInstitutionOpenStatisticForTrainingInstitution.graphql'
import exportUserLearningStatistic from './queries/exportUserLearningStatistic.graphql'
import exportUserLearningStatisticForChannelVendor from './queries/exportUserLearningStatisticForChannelVendor.graphql'
import exportUserLearningStatisticForParticipatingUnit from './queries/exportUserLearningStatisticForParticipatingUnit.graphql'
import exportUserLearningStatisticForTrainingInstitution from './queries/exportUserLearningStatisticForTrainingInstitution.graphql'
import exportWorkTypeLearningStatistic from './queries/exportWorkTypeLearningStatistic.graphql'
import exportWorkTypeLearningStatisticForCoursewareSupplier from './queries/exportWorkTypeLearningStatisticForCoursewareSupplier.graphql'
import exportWorkTypeLearningStatisticForParticipatingUnit from './queries/exportWorkTypeLearningStatisticForParticipatingUnit.graphql'
import exportWorkTypeLearningStatisticForTrainingInstitution from './queries/exportWorkTypeLearningStatisticForTrainingInstitution.graphql'
import findLearningProcessLog from './queries/findLearningProcessLog.graphql'
import getLearningStatisticDetail from './queries/getLearningStatisticDetail.graphql'
import getTotalAnswerQuestionStatistic from './queries/getTotalAnswerQuestionStatistic.graphql'
import getTotalOpenCount from './queries/getTotalOpenCount.graphql'
import getTotalOpenStatistic from './queries/getTotalOpenStatistic.graphql'
import getTotalOpenStatisticForChannelVendor from './queries/getTotalOpenStatisticForChannelVendor.graphql'
import getTotalOpenStatisticForChannelVendorWithoutContext from './queries/getTotalOpenStatisticForChannelVendorWithoutContext.graphql'
import getTotalOpenStatisticForCoursewareSupplier from './queries/getTotalOpenStatisticForCoursewareSupplier.graphql'
import getTotalOpenStatisticForTrainingInstitution from './queries/getTotalOpenStatisticForTrainingInstitution.graphql'
import getTrainingInstitutionCourseAppraiseStatistic from './queries/getTrainingInstitutionCourseAppraiseStatistic.graphql'
import getUserPracticeChart from './queries/getUserPracticeChart.graphql'
import learningDateHistogramForChannelVendor from './queries/learningDateHistogramForChannelVendor.graphql'
import learningDateHistogramForCoursewareSupplier from './queries/learningDateHistogramForCoursewareSupplier.graphql'
import learningDateHistogramForParticipatingUnit from './queries/learningDateHistogramForParticipatingUnit.graphql'
import learningDateHistogramForTrainingInstitution from './queries/learningDateHistogramForTrainingInstitution.graphql'
import listUserCourseLearningSchedule from './queries/listUserCourseLearningSchedule.graphql'
import loadStatisticDataUpdateTime from './queries/loadStatisticDataUpdateTime.graphql'
import monitorStudy from './queries/monitorStudy.graphql'
import monitorStudyForChannelVendor from './queries/monitorStudyForChannelVendor.graphql'
import monitorStudyForParticipatingUnit from './queries/monitorStudyForParticipatingUnit.graphql'
import monitorStudyForTrainingInstitution from './queries/monitorStudyForTrainingInstitution.graphql'
import openDateHistogramForChannelVendor from './queries/openDateHistogramForChannelVendor.graphql'
import openDateHistogramForCoursewareSupplier from './queries/openDateHistogramForCoursewareSupplier.graphql'
import openDateHistogramForTrainingInstitution from './queries/openDateHistogramForTrainingInstitution.graphql'
import pageAnswerQuestionStatistic from './queries/pageAnswerQuestionStatistic.graphql'
import pageCourseAppraiseStatistic from './queries/pageCourseAppraiseStatistic.graphql'
import pageCourseAppraiseStatisticForCoursewareSupplier from './queries/pageCourseAppraiseStatisticForCoursewareSupplier.graphql'
import pageCourseAppraiseStatisticForTrainingInstitution from './queries/pageCourseAppraiseStatisticForTrainingInstitution.graphql'
import pageHotCourseChooseStatistic from './queries/pageHotCourseChooseStatistic.graphql'
import pageRegionLearningStatistic from './queries/pageRegionLearningStatistic.graphql'
import pageRegionLearningStatisticForTrainingInstitution from './queries/pageRegionLearningStatisticForTrainingInstitution.graphql'
import pageSchemeCourseAppraiseStatistic from './queries/pageSchemeCourseAppraiseStatistic.graphql'
import pageSchemeCourseAppraiseStatisticForChannelVendor from './queries/pageSchemeCourseAppraiseStatisticForChannelVendor.graphql'
import pageSchemeCourseAppraiseStatisticForTrainingInstitution from './queries/pageSchemeCourseAppraiseStatisticForTrainingInstitution.graphql'
import pageSchemeLearningStatistic from './queries/pageSchemeLearningStatistic.graphql'
import pageSchemeLearningStatisticForTrainingInstitution from './queries/pageSchemeLearningStatisticForTrainingInstitution.graphql'
import pageSchemeOpenStatistic from './queries/pageSchemeOpenStatistic.graphql'
import pageSchemeOpenStatisticForChannelVendor from './queries/pageSchemeOpenStatisticForChannelVendor.graphql'
import pageSchemeOpenStatisticForCoursewareSupplier from './queries/pageSchemeOpenStatisticForCoursewareSupplier.graphql'
import pageSchemeOpenStatisticForTrainingInstitution from './queries/pageSchemeOpenStatisticForTrainingInstitution.graphql'
import pageTrainingInstitutionCourseAppraiseStatistic from './queries/pageTrainingInstitutionCourseAppraiseStatistic.graphql'
import pageTrainingInstitutionLearningStatistic from './queries/pageTrainingInstitutionLearningStatistic.graphql'
import pageTrainingInstitutionOpenStatistic from './queries/pageTrainingInstitutionOpenStatistic.graphql'
import pageTrainingInstitutionOpenStatisticForChannelVendor from './queries/pageTrainingInstitutionOpenStatisticForChannelVendor.graphql'
import pageTrainingInstitutionOpenStatisticForTrainingInstitution from './queries/pageTrainingInstitutionOpenStatisticForTrainingInstitution.graphql'
import pageUserLearningStatistic from './queries/pageUserLearningStatistic.graphql'
import pageUserLearningStatisticForChannelVendor from './queries/pageUserLearningStatisticForChannelVendor.graphql'
import pageUserLearningStatisticForParticipatingUnit from './queries/pageUserLearningStatisticForParticipatingUnit.graphql'
import pageUserLearningStatisticForTrainingInstitution from './queries/pageUserLearningStatisticForTrainingInstitution.graphql'
import pageUserLoginLog from './queries/pageUserLoginLog.graphql'
import pageWorkTypeLearningStatistic from './queries/pageWorkTypeLearningStatistic.graphql'
import pageWorkTypeLearningStatisticForCoursewareSupplier from './queries/pageWorkTypeLearningStatisticForCoursewareSupplier.graphql'
import pageWorkTypeLearningStatisticForParticipatingUnit from './queries/pageWorkTypeLearningStatisticForParticipatingUnit.graphql'
import pageWorkTypeLearningStatisticForTrainingInstitution from './queries/pageWorkTypeLearningStatisticForTrainingInstitution.graphql'
import pageWorkTypeOpenStatisticForChannelVendor from './queries/pageWorkTypeOpenStatisticForChannelVendor.graphql'
import pageWorkTypeOpenStatisticForCoursewareSupplier from './queries/pageWorkTypeOpenStatisticForCoursewareSupplier.graphql'
import pageWorkTypeOpenStatisticForTrainingInstitution from './queries/pageWorkTypeOpenStatisticForTrainingInstitution.graphql'
import schemeSkuLearningStatistic from './queries/schemeSkuLearningStatistic.graphql'
import statisticUserOnlineMajorDistribute from './queries/statisticUserOnlineMajorDistribute.graphql'
import statisticUserOnlineTerminalDistribute from './queries/statisticUserOnlineTerminalDistribute.graphql'
import statisticUserOnlineUnitDistribute from './queries/statisticUserOnlineUnitDistribute.graphql'
import totalCourseAverage from './queries/totalCourseAverage.graphql'

export {
  countUserOnlineCount,
  createBatchExportLearningLogTask,
  createBatchExportLearningLogTaskForChannelVendor,
  createBatchExportLearningLogTaskForParticipatingUnit,
  createBatchExportLearningLogTaskForTrainingInstitution,
  createExportLearningLogTask,
  createExportLearningLogTaskForChannelVendor,
  createExportLearningLogTaskForParticipatingUnit,
  createExportLearningLogTaskForTrainingInstitution,
  exportAnswerQuestionStatistic,
  exportCourseAppraiseStatistic,
  exportCourseAppraiseStatisticForCoursewareSupplier,
  exportCourseAppraiseStatisticForTrainingInstitution,
  exportLearningDateHistogramForChannelVendor,
  exportLearningDateHistogramForCoursewareSupplier,
  exportLearningDateHistogramForParticipatingUnit,
  exportLearningDateHistogramForTrainingInstitution,
  exportOpenDateHistogramForChannelVendor,
  exportOpenDateHistogramForCoursewareSupplier,
  exportOpenDateHistogramForTrainingInstitution,
  exportRegionLearningStatistic,
  exportRegionLearningStatisticForTrainingInstitution,
  exportSchemeLearningStatistic,
  exportSchemeLearningStatisticForTrainingInstitution,
  exportSchemeOpenStatistic,
  exportSchemeOpenStatisticForChannelVendor,
  exportSchemeOpenStatisticForCoursewareSupplier,
  exportSchemeOpenStatisticForTrainingInstitution,
  exportTrainingInstitutionCourseAppraiseStatistic,
  exportTrainingInstitutionLearningStatistic,
  exportTrainingInstitutionOpenStatistic,
  exportTrainingInstitutionOpenStatisticForChannelVendor,
  exportTrainingInstitutionOpenStatisticForTrainingInstitution,
  exportUserLearningStatistic,
  exportUserLearningStatisticForChannelVendor,
  exportUserLearningStatisticForParticipatingUnit,
  exportUserLearningStatisticForTrainingInstitution,
  exportWorkTypeLearningStatistic,
  exportWorkTypeLearningStatisticForCoursewareSupplier,
  exportWorkTypeLearningStatisticForParticipatingUnit,
  exportWorkTypeLearningStatisticForTrainingInstitution,
  findLearningProcessLog,
  getLearningStatisticDetail,
  getTotalAnswerQuestionStatistic,
  getTotalOpenCount,
  getTotalOpenStatistic,
  getTotalOpenStatisticForChannelVendor,
  getTotalOpenStatisticForChannelVendorWithoutContext,
  getTotalOpenStatisticForCoursewareSupplier,
  getTotalOpenStatisticForTrainingInstitution,
  getTrainingInstitutionCourseAppraiseStatistic,
  getUserPracticeChart,
  learningDateHistogramForChannelVendor,
  learningDateHistogramForCoursewareSupplier,
  learningDateHistogramForParticipatingUnit,
  learningDateHistogramForTrainingInstitution,
  listUserCourseLearningSchedule,
  loadStatisticDataUpdateTime,
  monitorStudy,
  monitorStudyForChannelVendor,
  monitorStudyForParticipatingUnit,
  monitorStudyForTrainingInstitution,
  openDateHistogramForChannelVendor,
  openDateHistogramForCoursewareSupplier,
  openDateHistogramForTrainingInstitution,
  pageAnswerQuestionStatistic,
  pageCourseAppraiseStatistic,
  pageCourseAppraiseStatisticForCoursewareSupplier,
  pageCourseAppraiseStatisticForTrainingInstitution,
  pageHotCourseChooseStatistic,
  pageRegionLearningStatistic,
  pageRegionLearningStatisticForTrainingInstitution,
  pageSchemeCourseAppraiseStatistic,
  pageSchemeCourseAppraiseStatisticForChannelVendor,
  pageSchemeCourseAppraiseStatisticForTrainingInstitution,
  pageSchemeLearningStatistic,
  pageSchemeLearningStatisticForTrainingInstitution,
  pageSchemeOpenStatistic,
  pageSchemeOpenStatisticForChannelVendor,
  pageSchemeOpenStatisticForCoursewareSupplier,
  pageSchemeOpenStatisticForTrainingInstitution,
  pageTrainingInstitutionCourseAppraiseStatistic,
  pageTrainingInstitutionLearningStatistic,
  pageTrainingInstitutionOpenStatistic,
  pageTrainingInstitutionOpenStatisticForChannelVendor,
  pageTrainingInstitutionOpenStatisticForTrainingInstitution,
  pageUserLearningStatistic,
  pageUserLearningStatisticForChannelVendor,
  pageUserLearningStatisticForParticipatingUnit,
  pageUserLearningStatisticForTrainingInstitution,
  pageUserLoginLog,
  pageWorkTypeLearningStatistic,
  pageWorkTypeLearningStatisticForCoursewareSupplier,
  pageWorkTypeLearningStatisticForParticipatingUnit,
  pageWorkTypeLearningStatisticForTrainingInstitution,
  pageWorkTypeOpenStatisticForChannelVendor,
  pageWorkTypeOpenStatisticForCoursewareSupplier,
  pageWorkTypeOpenStatisticForTrainingInstitution,
  schemeSkuLearningStatistic,
  statisticUserOnlineMajorDistribute,
  statisticUserOnlineTerminalDistribute,
  statisticUserOnlineUnitDistribute,
  totalCourseAverage
}
