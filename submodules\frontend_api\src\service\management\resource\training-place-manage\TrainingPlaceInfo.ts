import TrainingPlaceInfoDto from '@api/service/management/resource/training-place-manage/models/TrainingPlaceInfoDto'
import TeachingplanV1, {
  TrainingPointDeleteRequest,
  TrainingPointStatusRequest
} from '@api/ms-gateway/ms-teachingplan-v1'
import { CourseStatusEnum } from '@api/service/common/enums/course/CourseStatus'
import SchemeLearningQueryBackstage, {
  GetTrainingPointDetailRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
import TeachingplanDdsV1 from '@api/ms-gateway/ms-teachingplan-dds-v1'

/**
 * 培训点信息
 */
export default class TrainingPlaceInfo extends TrainingPlaceInfoDto {
  /**
   * 检测培训名称是否已存在
   */
  async checkTrainingNameIsExist() {
    const response = await TeachingplanV1.checkTrainingPointName({
      name: this.trainingPlaceName,
      excludeId: this.id || undefined
    })
    if (response.data?.code != '200' || response.data?.id) {
      return true
    } else {
      return false
    }
  }
  /**
   * 查询培训点详情
   */
  async queryDetail(id?: string) {
    const request = new GetTrainingPointDetailRequest()
    request.id = id || this.id
    const response = await SchemeLearningQueryBackstage.getTrainingPointDetailInServicer(request)

    if (!response.status.isSuccess()) {
      console.error('获取培训点详情报错', response)
      return response
    }
    Object.assign(this, TrainingPlaceInfoDto.toTrainingPlaceInfo(response.data))
    // 整理地区
    if (this.trainingPlaceRegionIds?.length) {
      const regionList = await QueryBusinessRegion.queryRegionsNameByIds(this.trainingPlaceRegionIds)
      if (regionList.size) {
        this.trainingPlaceRegionNames = this.toRegionName(this.trainingPlaceRegionIds, regionList)
      }
    }
    return response
  }
  /**
   * 删除培训点
   */
  async deleteTrainingPlace() {
    const request = new TrainingPointDeleteRequest()
    request.id = this.id
    const response = await TeachingplanV1.deleteTrainingPoints(request)
    if (!response.status.isSuccess()) {
      console.error('删除培训点报错', response)
    }
    return response
  }
  /**
   * 修改培训点
   */
  async editTrainingPlace() {
    const request = TrainingPlaceInfoDto.toTrainingPointUpdateRequest(this)
    return TeachingplanV1.updateTrainingPoint(request)
  }
  /**
   * 创建培训点
   */
  async createTrainingPlace() {
    const request = TrainingPlaceInfoDto.toTrainingPointCreateRequest(this)
    return TeachingplanV1.createTrainingPoint(request)
  }
  /**
   * 修改培训点状态
   */
  async changeStatus() {
    const request = new TrainingPointStatusRequest()
    request.id = this.id
    if (this.trainingPlaceStatus.equal(CourseStatusEnum.ENABLE)) {
      request.status = CourseStatusEnum.DISABLE
    } else {
      request.status = CourseStatusEnum.ENABLE
    }
    const response = await TeachingplanV1.changeTrainingPointStatus(request)
    if (!response.status.isSuccess()) {
      console.error('起停用培训点报错', response)
      return response
    }
    // this.trainingPlaceStatus = new CourseStatus(request.status)
    return response
  }

  /**
   * 为他人创建培训地点
   * @param unitId 单位ID
   */
  async createTrainingPlaceForOthers(unitId: string) {
    const request = TrainingPlaceInfoDto.toTrainingPointCreateRequestForOther(this)
    request.ownerId = unitId
    return TeachingplanDdsV1.createTrainingPoint(request)
  }

  /**
   * 获取区域名称
   * @param ids
   * @param regionList
   * @private
   */
  toRegionName(ids: string[], regionList: Map<string, string>) {
    const nameList: string[] = []
    ids.map((res) => {
      const name = regionList.get(res)
      if (name) {
        nameList.push(name)
      }
    })
    return nameList.join('-')
  }
}
