<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--批量导入公需课课程-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">批量导入公需课课程</el-button>
        <el-drawer
          title="导入公需课课程"
          :visible.sync="dialog1"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" :closable="false" class="m-alert">
              <p>温馨提示：</p>
              <p>
                1. 在批量导入公需课课程前请下载<span class="f-cb f-csp">[导入公需课课程模板]</span
                >，并严格根据表格内容填写保存后再导入系统；
              </p>
              <p>2. 导入表格一次最多支持1000条记录，若超过1000条则不能正常导入；</p>
              <p>3. 导入后可以通过“导入任务管理” 查看并确认导入结果。</p>
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt50">
                  <el-form-item label="导入文件：">
                    <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                      <el-button type="primary" plain>点击上传</el-button>
                      <div slot="tip" class="el-upload__tip">
                        <i class="el-icon-warning"></i>
                        <span class="txt">导入的文件格式必须为xls,xlsx文件</span>
                      </div>
                    </el-upload>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">导入</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        num: 100,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        checked: false,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
