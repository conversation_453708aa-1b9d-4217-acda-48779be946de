import getAllBigCategoryList from './queries/getAllBigCategoryList.graphql'
import getAllBigCategoryTreeList from './queries/getAllBigCategoryTreeList.graphql'
import getAllMediumCategoryList from './queries/getAllMediumCategoryList.graphql'
import getAllProfessionList from './queries/getAllProfessionList.graphql'
import getAllSmallCategoryList from './queries/getAllSmallCategoryList.graphql'
import getBigCategoryTree from './queries/getBigCategoryTree.graphql'
import getMediumCategoryListByBigCategoryId from './queries/getMediumCategoryListByBigCategoryId.graphql'
import getMediumCategoryTree from './queries/getMediumCategoryTree.graphql'
import getProfessionListBySmallCategoryId from './queries/getProfessionListBySmallCategoryId.graphql'
import getSmallCategoryListByMediumCategoryId from './queries/getSmallCategoryListByMediumCategoryId.graphql'
import getSmallCategoryTree from './queries/getSmallCategoryTree.graphql'
import createBigCategory from './mutates/createBigCategory.graphql'
import createBigCategoryTree from './mutates/createBigCategoryTree.graphql'
import createMediumCategory from './mutates/createMediumCategory.graphql'
import createMediumCategoryTree from './mutates/createMediumCategoryTree.graphql'
import createProfession from './mutates/createProfession.graphql'
import createSmallCategory from './mutates/createSmallCategory.graphql'
import createSmallCategoryTree from './mutates/createSmallCategoryTree.graphql'
import diableBigCategory from './mutates/diableBigCategory.graphql'
import diableProfession from './mutates/diableProfession.graphql'
import disableMediumCategory from './mutates/disableMediumCategory.graphql'
import disableSmallCategory from './mutates/disableSmallCategory.graphql'
import enableSmallCategory from './mutates/enableSmallCategory.graphql'
import enableBigCategory from './mutates/enableBigCategory.graphql'
import enableMediumCategory from './mutates/enableMediumCategory.graphql'
import enableProfession from './mutates/enableProfession.graphql'
import removeBigCategory from './mutates/removeBigCategory.graphql'
import removeMediumCategory from './mutates/removeMediumCategory.graphql'
import removeProfession from './mutates/removeProfession.graphql'
import removeSmallCategory from './mutates/removeSmallCategory.graphql'
import updateBigCategory from './mutates/updateBigCategory.graphql'
import updateMediumCategory from './mutates/updateMediumCategory.graphql'
import updateProfession from './mutates/updateProfession.graphql'
import updateSmallCategory from './mutates/updateSmallCategory.graphql'

export {
  getAllBigCategoryList,
  getAllBigCategoryTreeList,
  getAllMediumCategoryList,
  getAllProfessionList,
  getAllSmallCategoryList,
  getBigCategoryTree,
  getMediumCategoryListByBigCategoryId,
  getMediumCategoryTree,
  getProfessionListBySmallCategoryId,
  getSmallCategoryListByMediumCategoryId,
  getSmallCategoryTree,
  createBigCategory,
  createBigCategoryTree,
  createMediumCategory,
  createMediumCategoryTree,
  createProfession,
  createSmallCategory,
  createSmallCategoryTree,
  diableBigCategory,
  diableProfession,
  disableMediumCategory,
  disableSmallCategory,
  enableSmallCategory,
  enableBigCategory,
  enableMediumCategory,
  enableProfession,
  removeBigCategory,
  removeMediumCategory,
  removeProfession,
  removeSmallCategory,
  updateBigCategory,
  updateMediumCategory,
  updateProfession,
  updateSmallCategory
}
