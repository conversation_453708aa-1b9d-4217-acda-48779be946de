import {
  StudentQueryRequest,
  StudentUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

/**
 * @description 查询学员列表参数
 */
class QueryStudentUserListVo extends StudentQueryRequest {
  /**
   * 工作单位名称（模糊）
   */
  companyName?: string

  /**
   * 用户名称
   */
  userName?: string

  /**
   * 证件号
   */
  idCard?: string

  /**
   * 手机号
   */
  phone?: string

  to(): StudentQueryRequest {
    const to = new StudentQueryRequest()
    to.user = new StudentUserRequest()
    to.user.companyName = this.companyName
    to.user.userName = this.userName
    to.user.idCard = this.idCard
    to.user.phone = this.phone
    return to
  }
}

export default QueryStudentUserListVo
