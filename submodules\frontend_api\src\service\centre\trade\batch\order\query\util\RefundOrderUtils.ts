import RefundStatusInfoVo from '@api/service/centre/trade/batch/order/query/vo/RefundStatusInfoVo'
import { ReturnOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { SubOrderRefundStatusEnum } from '@api/service/centre/trade/batch/order/enum/SubOrderRefundStatusList'

/**
 * @description 退货
 */
class RefundOrderUtils {
  /**
   * 获取退款信息
   */
  static getRefundStatusInfoList(response: ReturnOrderResponse): RefundStatusInfoVo[] {
    let result = [] as RefundStatusInfoVo[]
    const statusChangeTime = response.basicData?.returnOrderStatusChangeTime ?? undefined
    if (statusChangeTime) {
      const closeType = response.basicData?.returnCloseReason?.closeType ?? undefined
      const auditingStatus = new RefundStatusInfoVo(SubOrderRefundStatusEnum.Auditing) // 退款审核中
      const refundingStatus = new RefundStatusInfoVo(SubOrderRefundStatusEnum.Refunding) // 退款中
      const refundSuccessStatus = new RefundStatusInfoVo(SubOrderRefundStatusEnum.Refund_Success) // 退款成功
      const rejectedStatus = new RefundStatusInfoVo(SubOrderRefundStatusEnum.Rejected) // 退款申请被拒绝
      const cancelRefundStatus = new RefundStatusInfoVo(SubOrderRefundStatusEnum.Cancel_Refund) // 取消退款
      // 取消退款
      const cancelOption: RefundStatusInfoVo[] = new Array<RefundStatusInfoVo>(auditingStatus, cancelRefundStatus)
      // 退款成功
      const successOption: RefundStatusInfoVo[] = new Array<RefundStatusInfoVo>(
        auditingStatus,
        refundingStatus,
        refundSuccessStatus
      )
      // 退款申请被拒绝
      const rejectOption: RefundStatusInfoVo[] = new Array<RefundStatusInfoVo>(auditingStatus, rejectedStatus)
      const statusMap: Map<SubOrderRefundStatusEnum, string> = new Map<SubOrderRefundStatusEnum, string>()
        .set(SubOrderRefundStatusEnum.Auditing, statusChangeTime.applied ?? '')
        .set(
          SubOrderRefundStatusEnum.Refunding,
          statusChangeTime.refundFailed ??
            statusChangeTime.refunding ??
            statusChangeTime.refundApplied ??
            statusChangeTime.refundApplying ??
            statusChangeTime.returnFailed ??
            statusChangeTime.returning ??
            ''
        )
        .set(
          SubOrderRefundStatusEnum.Refund_Success,
          statusChangeTime.returnedAndRefunded ??
            statusChangeTime.refunded ??
            (statusChangeTime.returned &&
            response.batchReturnOrder?.batchReturnOrderNo &&
            [5, 6, 7].includes(response.batchReturnOrder?.basicData?.batchReturnOrderStatus)
              ? ''
              : statusChangeTime.returned)
        )
        .set(SubOrderRefundStatusEnum.Cancel_Refund, statusChangeTime.closed ?? '')
        .set(SubOrderRefundStatusEnum.Rejected, statusChangeTime.closed ?? '')
      // 遍历数组赋值
      cancelOption.forEach(item => {
        item.date = statusMap.get(item.status) ?? ''
      })
      rejectOption.forEach(item => {
        item.date = statusMap.get(item.status) ?? ''
        if (item.status === SubOrderRefundStatusEnum.Rejected) {
          item.remark = response.approvalInfo?.approveComment ?? ''
        }
      })
      successOption.forEach(item => {
        item.date = statusMap.get(item.status) ?? ''
      })
      if ([1, 2, 4].includes(closeType) && statusChangeTime.closed) {
        result = cancelOption
      } else if (closeType === 3 && statusChangeTime.closed) {
        result = rejectOption
      } else {
        result = successOption
      }
    }
    // 过滤掉没有时间的项
    result = result.filter(item => item.date)
    console.log('refundStatusList', result)
    return result
  }

  /**
   * 获取退款状态
   */
  static getRefundStatus(response: ReturnOrderResponse): SubOrderRefundStatusEnum {
    const status: number = response.basicData?.returnOrderStatus ?? undefined
    const closeType: number = response.basicData?.returnCloseReason?.closeType ?? undefined
    // 退款审核中
    if (status === 0) {
      return SubOrderRefundStatusEnum.Auditing
    }
    // 退款中
    if ([2, 3, 4, 5, 6, 7].includes(status)) {
      return SubOrderRefundStatusEnum.Refunding
    }
    // 退款成功
    if ([8].includes(status)) {
      if (response.batchReturnOrder?.batchReturnOrderNo) {
        if ([5, 6, 7].includes(response.batchReturnOrder?.basicData?.batchReturnOrderStatus)) {
          return SubOrderRefundStatusEnum.Refunding
        } else {
          return SubOrderRefundStatusEnum.Refund_Success
        }
      } else {
        return SubOrderRefundStatusEnum.Refund_Success
      }
    }
    if ([9, 10].includes(status)) {
      return SubOrderRefundStatusEnum.Refund_Success
    }
    // 退款申请被拒绝
    if (status === 11 && closeType === 3) {
      return SubOrderRefundStatusEnum.Rejected
    }
    // 取消退款
    if (status === 11 && [1, 2, 4].includes(closeType)) {
      return SubOrderRefundStatusEnum.Cancel_Refund
    }
    return null
  }
}

export default RefundOrderUtils
