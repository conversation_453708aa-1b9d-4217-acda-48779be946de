const path = require('path')
const unitTypeDir = path.resolve(__dirname, '../../../packages/routers/src')
const fastGlob = require('fast-glob').sync

const dirList = fastGlob('*', {
  cwd: unitTypeDir,
  onlyDirectories: true
})

const suffix = '-routes'
const boilerplate = `boilerplate${suffix}`

/**
 *
 * E:\\WorkPlaces\\codes\\_Bitbuckets\\补贴培训联盟\\frontend_web_admin\\
 *
 * {
 *  'boilerplate-routes': '.cache\\boilerplate-routes',
 *  'channel-routes': '.cache\\channel-routes',
 *  'content-routes': '.cache\\content-routes',
 *  'org-routes': '.cache\\org-routes'
 * }
 * @type {{[p: string]: string}}
 */
const alias = {
  [boilerplate]: path.resolve(__dirname, '../../../.cache/' + boilerplate)
}

dirList.forEach(dir => {
  const key = `${dir}${suffix}`
  alias[key] = path.resolve(__dirname, '../../../.cache/' + key)
})
module.exports = {
  unitTypeDir,
  dirList,
  suffix,
  alias
}
