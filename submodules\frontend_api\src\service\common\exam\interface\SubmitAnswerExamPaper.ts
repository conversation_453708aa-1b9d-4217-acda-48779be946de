export interface SubmitQuestionReplyDto {
  id: string
  answersResult: Array<string>
}

export interface SubmitQuestion {
  id: string
  answersResult: Array<string>
  replyDtos: Array<SubmitQuestionReplyDto>
}

export interface SubmitQuestionItem {
  id: string
  questions: Array<SubmitQuestion>
}

export interface ReplyDto {
  id: string
  questionItems: Array<SubmitQuestionItem>
}

export default interface SubmitAnswerExamPaper {
  replyDto: ReplyDto
  // 是否结束考试
  end: boolean
}
