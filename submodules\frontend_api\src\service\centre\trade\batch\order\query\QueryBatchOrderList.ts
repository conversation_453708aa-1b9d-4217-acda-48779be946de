import { Page } from '@hbfe/common'
import QueryBatchOrderListVo from '@api/service/centre/trade/batch/order/query/vo/QueryBatchOrderListVo'
import BatchOrderListDetailVo from '@api/service/centre/trade/batch/order/query/vo/BatchOrderListDetailVo'
import RefundInfoListDetailVo from '@api/service/centre/trade/batch/order/query/vo/RefundInfoListDetailVo'
import RefundInfoListStatisticVo from '@api/service/centre/trade/batch/order/query/vo/RefundInfoListStatisticVo'
import msTradeQuery, {
  BatchOrderBasicDataRequest,
  BatchOrderInfoRequest,
  BatchOrderRequest,
  BatchOrderSortField,
  BatchOrderSortRequest,
  BatchReturnOrderBasicDataRequest,
  BatchReturnOrderRequest,
  BatchReturnOrderResponse,
  OrderInfoRequest,
  ReturnOrderBasicDataRequest,
  ReturnOrderRequest,
  ReturnOrderResponse,
  SortPolicy,
  SubOrderInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import DataResolve from '@api/service/common/utils/DataResolve'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import UserModule from '@api/service/centre/user/UserModule'
import SaleChannelType from '@api/service/common/enums/trade/SaleChannelType'
import Context from '@api/service/common/context/Context'

/**
 * @description 【集体缴费管理员】查询集体报名订单列表
 */
class QueryBatchOrderList {
  /**
   * 【集体缴费管理员】查询集体报名订单列表
   * @param {Page} page - 分页参数
   * @param {QueryBatchOrderListVo} queryParams - 查询参数
   * @return {Promise<BatchOrderListDetailVo[]>} -【集体缴费管理员】集体报名订单列表
   */
  async queryBatchOrderList(page: Page, queryParams: QueryBatchOrderListVo): Promise<BatchOrderListDetailVo[]> {
    // 按批次创建时间（点击开始报名时的时间，不是提交报名信息的时间）降序排
    const sortOption = new BatchOrderSortRequest()
    sortOption.field = BatchOrderSortField.BATCH_ORDER_UN_CONFIRMED_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = [] as BatchOrderSortRequest[]
    sortRequest.push(sortOption)
    const response = await msTradeQuery.pageBatchOrderInMySelf({
      page,
      request: queryParams.to(),
      sortRequest
    })
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    const result =
      response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)
        ? await Promise.all(
            response.data.currentPageData.map(async item => {
              return await BatchOrderListDetailVo.from(item)
            })
          )
        : ([] as BatchOrderListDetailVo[])

    return result
  }

  /**
   * 【集体缴费管理员】查询交易成功且未开票的集体报名订单列表
   */
  async queryWithoutInvoiceBatchOrderList(page: Page, isSpecialTopic?: boolean): Promise<BatchOrderListDetailVo[]> {
    // 按批次创建时间（点击开始报名时的时间，不是提交报名信息的时间）降序排
    const sortOption = new BatchOrderSortRequest()
    sortOption.field = BatchOrderSortField.BATCH_ORDER_UN_CONFIRMED_TIME
    sortOption.policy = SortPolicy.DESC
    const sortRequest = [] as BatchOrderSortRequest[]
    sortRequest.push(sortOption)
    const queryParams = new BatchOrderRequest()
    queryParams.basicData = new BatchOrderBasicDataRequest()
    queryParams.basicData.batchOrderStatusList = [2]
    queryParams.isInvoiceApplied = false
    queryParams.basicData.batchOrderAmountScope = {
      begin: 0.01
    }
    if (isSpecialTopic) {
      queryParams.basicData.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(true)
      queryParams.basicData.saleChannelIds = [Context.businessEnvironment?.specialTopicsInfo?.id]
    }
    const response = await msTradeQuery.pageBatchOrderInMySelf({
      page,
      request: queryParams,
      sortRequest
    })
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    const result =
      response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)
        ? await Promise.all(
            response.data.currentPageData.map(async item => {
              return await BatchOrderListDetailVo.from(item)
            })
          )
        : ([] as BatchOrderListDetailVo[])
    return result
  }

  /**
   * 【集体缴费管理员】查询批次单退款列表
   * @param {string[]} batchOrderNo - 批次单id
   * @param {Page} page - 分页参数
   */
  async queryBatchOrderRefundInfoList(batchOrderNo: string, page: Page): Promise<RefundInfoListDetailVo[]> {
    let result = [] as RefundInfoListDetailVo[]
    if (!batchOrderNo) return result
    const queryParams = new ReturnOrderRequest()
    queryParams.subOrderInfo = new SubOrderInfoRequest()
    queryParams.subOrderInfo.orderInfo = new OrderInfoRequest()
    queryParams.subOrderInfo.orderInfo.batchOrderNoList = [batchOrderNo]
    queryParams.basicData = new ReturnOrderBasicDataRequest()
    queryParams.basicData.returnOrderStatus = [8, 9, 10]
    queryParams.basicData.refundAmountScope = { begin: 0.01 }
    const response = await msTradeQuery.pageReturnOrderInMyself({
      page,
      request: queryParams
    })
    page.totalSize = response.data.totalSize ?? 0
    page.totalPageSize = response.data.totalPageSize ?? 0
    result =
      response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)
        ? response.data.currentPageData.map((item: ReturnOrderResponse) => {
            return RefundInfoListDetailVo.from(item)
          })
        : ([] as RefundInfoListDetailVo[])
    return await this.fillStudentInfo(result)
  }

  /**
   * 【集体缴费管理员】查询批次单退款列表统计数据
   * @param {string} batchOrderNo - 批次单id
   */
  async queryBatchOrderRefundInfoListStatistic(batchOrderNo: string): Promise<RefundInfoListStatisticVo> {
    if (!batchOrderNo) return new RefundInfoListStatisticVo()
    const result = new RefundInfoListStatisticVo()
    const queryParams = new BatchReturnOrderRequest()
    queryParams.batchOrderInfo = new BatchOrderInfoRequest()
    queryParams.batchOrderInfo.batchOrderNoList = [batchOrderNo]
    queryParams.basicData = new BatchReturnOrderBasicDataRequest()
    queryParams.basicData.batchReturnOrderStatus = [9, 10, 11]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 10
    const response = await msTradeQuery.pageBatchReturnOrderInMySelf({
      page,
      request: queryParams
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      const origin: BatchReturnOrderResponse[] = response.data.currentPageData
      const batchReturnOrderIds = origin.map(item => {
        return item.batchReturnOrderNo ?? ''
      })
      result.batchReturnOrderNoList = [...new Set(batchReturnOrderIds)]
      result.refundAmount =
        origin.reduce((prev, cur) => {
          return CalculatorObj.add(cur.basicData?.refundAmount ?? 0, prev)
        }, 0) ?? 0
      result.refundSuccessPersonTime =
        origin.reduce((prev, cur) => {
          return CalculatorObj.add(cur.basicData?.returnOrderCount ?? 0, prev)
        }, 0) ?? 0
    }
    return result
  }

  /**
   * 填充学员信息
   */
  private async fillStudentInfo(list: RefundInfoListDetailVo[]): Promise<RefundInfoListDetailVo[]> {
    const userIds = [...new Set(list?.map(item => item.studentId ?? ''))]
    if (DataResolve.isWeightyArr(userIds)) {
      const queryRemote = UserModule.queryUserFactory.queryStudentList
      const response = await queryRemote.queryStudentInfoListById(userIds)
      if (DataResolve.isWeightyArr(response)) {
        list.forEach(item => {
          const userId = item.studentId
          const target = response.find(item => item.userId === userId)
          if (target) {
            item.studentName = target.userName ?? ''
            item.studentAccount = target.idCard ?? ''
            item.studentPhone = target.phone ?? ''
          }
        })
      }
    }
    return list
  }
}

export default QueryBatchOrderList
