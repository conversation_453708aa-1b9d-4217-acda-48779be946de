/**
 *    200 - 成功
 *    E10000 - 参数异常
 *    E90001 - 内部异常
 *    T10000 - 计时凭证不存在
 *    T10001 - 用户已学习其他课程
 *    T10002 - 用户已学习当前课程，其他课件
 *    T10003 - 用户已在其他地方学习当前课程当前课件
 *    T10004 - 用户学习已离线
 *    T10005 - 服务拒绝用户学习
 */
enum LearningHeatBeatErrorCodeEnum {
  success = '200',
  E10000 = 'E10000',
  E90001 = 'E90001',
  T10000 = 'T10000',
  T10001 = 'T10001',
  T10002 = 'T10002',
  T10003 = 'T10003',
  T10004 = 'T10004',
  T10005 = 'T10005'
}

export default LearningHeatBeatErrorCodeEnum
