<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="注册登录" name="first">详见 0301_功能设置_注册登录.vue</el-tab-pane>
      <el-tab-pane label="集体报名" name="second">详见 0303_功能设置_集体报名.vue</el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="third">
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15">
            <el-row :gutter="20" type="flex" justify="center">
              <el-col :lg="20" :xl="18">
                <el-form ref="form" :inline="true" :model="form" label-width="auto" class="m-form f-clear f-mt20">
                  <el-col :span="12">
                    <el-form-item label="电子票服务商：">
                      <el-radio-group v-model="form.resource">
                        <el-radio label="诺诺发票"></el-radio>
                        <el-radio label="百旺金赋"></el-radio>
                        <el-radio label="诺税通"></el-radio>
                        <el-radio label="诺税通全电票"></el-radio>
                      </el-radio-group>
                      <!--<el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">-->
                      <!--  <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>-->
                      <!--  <div slot="content">-->
                      <!--    平台仅支持诺诺发票的增值税电子普通发票的自动开票对接。请先购买诺诺开票服务，再提供以下的信息。-->
                      <!--    <a href="#" class="f-link f-cb f-underline">前往诺诺发票 &gt;</a>-->
                      <!--  </div>-->
                      <!--</el-tooltip>-->
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item class="f-tr">
                      <span class="f-link f-cb f-underline" @click="dialog1 = true">查看发票示例图片</span>
                      <!--示例图片弹窗-->
                      <el-dialog :visible.sync="dialog1" width="1100px" class="m-dialog-pic">
                        <img src="./assets/images/demo-invoice.png" alt="" />
                      </el-dialog>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-col>
            </el-row>
          </el-card>
          <el-card shadow="never" class="m-card f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">基础信息</span>
            </div>
            <el-row :gutter="20" type="flex" justify="center">
              <el-col :lg="20" :xl="18">
                <el-form ref="form" :inline="true" :model="form" label-width="auto" class="m-form f-clear f-mt20">
                  <el-col :span="12">
                    <el-form-item label="单位名称：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入单位名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="纳税人识别号：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入识别号纳税人识别号" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="地址：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入地址" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="电话：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入电话" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="开户行：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入开户行" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="开户帐号：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入开户帐号" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="开票最大金额：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入开票最大金额（元）" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="收款人：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入收款人" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="复核：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入复核" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="开票人：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入开票人" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="授权码：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入授权码" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item>
                      <div slot="label">
                        <span class="f-vm">部门ID</span>
                        <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                          <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                          <div slot="content">
                            <p>部门ID用于第三方开票平台区分开票的来源，该信息由第三方开票平台提供，请确认后再填写。</p>
                            <p>如网校存在以下任一情况，需要提供部门ID：</p>
                            <p>1.网校需求区分，开票是属于单机版、网络版；</p>
                            <p>2.同一个公司，存在多税盘的情况；</p>
                          </div>
                        </el-tooltip>
                        <span>：</span>
                      </div>
                      <el-input v-model="form.name" clearable placeholder="请输入部门ID" />
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-col>
            </el-row>
          </el-card>
          <el-card shadow="never" class="m-card f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">服务类型 / 商品类型信息</span>
            </div>
            <el-row :gutter="20" type="flex" justify="center">
              <el-col :lg="20" :xl="18">
                <el-form ref="form" :inline="true" :model="form" label-width="auto" class="m-form f-clear f-mt20">
                  <el-col :span="12">
                    <el-form-item label="税务名称(税务编码)：" required>
                      <el-input v-model="form.name" clearable placeholder="请选择税务编码" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="服务名称：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入网校开票具体服务名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="规格型号：">
                      <el-input v-model="form.name" clearable placeholder="请输入规格型号" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="单位：">
                      <el-select v-model="form.region" clearable placeholder="请选择票面单位">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="自定义单位：" required>
                      <el-input
                        v-model="form.name"
                        show-word-limit
                        maxlength="5"
                        clearable
                        placeholder="请输入自定义单位"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="是否显示数量和单价：">
                      <el-select v-model="form.region" clearable placeholder="请选择发票上是否显示数量和单价">
                        <el-option value="显示"></el-option>
                        <el-option value="不显示"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="税率：" required>
                      <el-input v-model="form.name" clearable placeholder="请输入税率（%）" />
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-col>
            </el-row>
          </el-card>
          <div class="m-btn-bar f-tc is-sticky-1">
            <el-button>取消</el-button>
            <el-button type="primary">保存</el-button>
          </div>
        </div>
      </el-tab-pane>
      <el-tab-pane label="培训证明" name="fourth">详见 0305_功能设置_培训证明.vue</el-tab-pane>
      <el-tab-pane label="视频播放设置" name="five">详见 0306_功能设置_视频播放设置.vue</el-tab-pane>
      <el-tab-pane label="门户精品课程" name="six">详见 0307_功能设置_门户精品课程.vue</el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'third',
        activeName1: 'first',
        activeName2: 'first',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
