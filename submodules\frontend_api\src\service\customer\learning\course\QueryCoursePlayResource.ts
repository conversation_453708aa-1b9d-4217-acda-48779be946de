import MsCoursePlayResourceV1 from '@api/ms-gateway/ms-course-play-resource-v1'
import LearningCourseDetail from '@api/service/customer/learning/course/vo/LearningCourseDetail'

/**
 * 获取课程学习播放资源
 */
class QueryCoursePlayResource {
  constructor(token: string) {
    this.getCoursePlayResourceToken = token
  }

  // 获取课程播放资源的 token
  private readonly getCoursePlayResourceToken: string

  /**
   * 获取课程播放资源
   */
  async queryCourseResource(withoutVerify = false): Promise<LearningCourseDetail> {
    const courseResource = await MsCoursePlayResourceV1.applyCoursePlayResource(this.getCoursePlayResourceToken)
    return LearningCourseDetail.from(courseResource.data, withoutVerify) as LearningCourseDetail
  }
}

export default QueryCoursePlayResource
