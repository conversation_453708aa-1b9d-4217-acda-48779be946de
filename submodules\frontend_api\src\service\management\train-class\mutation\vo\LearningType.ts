import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'
import ExamLearningType from '@api/service/management/train-class/mutation/vo/ExamLearningType'
import PracticeLearningType from '@api/service/management/train-class/mutation/vo/PracticeLearningType'
import InterestCourseType from '@api/service/management/train-class/mutation/vo/InterestCourseType'
import LearningExperience from '@api/service/management/train-class/mutation/vo/LearningExperience'
import IssueLearningType from '@api/service/common/scheme/model/IssueLearningType'
import QuestionnaireLearningType from '@api/service/common/scheme/model/QuestionnaireLearningType'

/**
 * 培训班学习方式聚合类
 */
class LearningType {
  // region properties

  /**
   *课程学习，类型为CourseLearningLearningType
   */
  courseLearning = new CourseLearningLearningType()
  /**
   *考试，类型为ExamLearningType
   */
  exam = new ExamLearningType()
  /**
   *练习，类型为PracticeLearningType
   */
  practiceLearning = new PracticeLearningType()
  /**
   *兴趣课，类型为InterestCourseType
   */
  interestCourse = new InterestCourseType()
  // endregion
  // region methods
  // 心得模型接收
  learningExperience = new LearningExperience()

  /**
   * 期别学习方式
   */
  issue = new IssueLearningType()
  /**
   * 问卷学习方式
   */
  questionnaire = new QuestionnaireLearningType()
  // endregion
}
export default LearningType
