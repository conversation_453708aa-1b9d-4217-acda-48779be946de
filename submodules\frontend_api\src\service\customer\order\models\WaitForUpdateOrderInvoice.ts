import WaitForSubmitInvoiceInfo from './WaitForSubmitInvoiceInfo'
import { OrderInvoiceUpdateRequest } from '@api/gateway/Trade-default'

export class WaitForUpdateOrderInvoice {
  /**
   * 要修改发票信息的订单号
   */
  orderNo: string
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 不需要发票时不用填，需要发票时再填入发票信息
   */
  invoiceInfo?: WaitForSubmitInvoiceInfo

  toOrderInvoiceUpdateRequest(): OrderInvoiceUpdateRequest {
    const dto = new OrderInvoiceUpdateRequest()
    dto.orderNo = this.orderNo
    dto.needInvoice = this.needInvoice
    if (this.needInvoice && this.invoiceInfo) {
      dto.invoiceInfo = this.invoiceInfo.toInvoiceRequestDTO()
    }
    return dto
  }
}
