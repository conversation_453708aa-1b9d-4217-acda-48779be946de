import { ResponseStatus } from '@hbfe/common'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
import { LearningResultErrorResponse } from '@api/platform-gateway/student-course-learning-query-back-gateway'
import MSAutoLearningTask, {
  StudentAutoLearningTaskResultQueryPageRequest,
  StudentAutoLearningTaskResult
} from '@api/ms-gateway/ms-autolearning-student-auto-learning-task-result-v1'
export default class IntelligenceAbnormalItem {
  /**
   * 姓名
   */
  name = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 培训方案id
   */
  schemeId = ''
  /**
   * 方案名称
   */
  schemeName = ''
  /**
   * 操作时间
   */
  operationTime = ''
  /**
   * 失败原因
   */
  failReason = ''
  mainTaskId = ''
  static from(dto: StudentAutoLearningTaskResult) {
    const vo = new IntelligenceAbnormalItem()
    vo.name = dto.name
    vo.idCard = dto.idCard
    vo.phone = dto.phone
    vo.schemeId = dto.learningSchemeId
    vo.schemeName = dto.learningSchemeName
    vo.operationTime = dto.completedTime
    vo.failReason = dto.message
    vo.mainTaskId = dto.mainTaskId
    return vo
  }
}
