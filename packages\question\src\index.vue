<route-meta>
{
"isMenu": true,
"title": "试题管理",
"sort": 5,
"icon": "icon-shenqing"
}
</route-meta>

<template>
  <el-main>
    <div class="f-p15" v-if="$hasPermission('query')" desc="查询" actions="doSearch,search,activated">
      <div class="f-mb15">
        <template
          desc="手动创建"
          v-if="$hasPermission('create')"
          actions="@hbfe/jxjy-admin-question/src/create.vue"
        >
          <el-button type="primary" icon="el-icon-plus" @click="create">手动创建试题</el-button>
        </template>
        <template
          desc="批量创建"
          v-if="$hasPermission('batchcreate')"
          actions="@hbfe/jxjy-admin-question/src/import.vue"
        >
          <el-button type="primary" icon="el-icon-plus" @click="questionImport">批量创建试题</el-button>
        </template>
        <template
          desc="导出任务查看"
          v-if="$hasPermission('queryTask')"
          actions="@hbfe/jxjy-admin-task/src/exporttask/index.vue"
        >
          <el-button type="primary" plain @click="goImportDownloadPage">试题导入任务查看</el-button>
        </template>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <hb-search-wrapper :model="questionRequestVo" @reset="resetCondition">
          <el-form-item label="试题名称">
            <el-input clearable v-model="questionRequestVo.topic" placeholder="请输入试题名称" />
          </el-form-item>

          <el-form-item label="题库" prop="librarys">
            <question-library-list
              v-if="show"
              v-model="questionRequestVo.libraryIdList"
              ref="questionLibraryListRef"
              placeholder="请选择题库"
              class="el-select"
              :multiple="true"
              :remote-data="remoteFlag"
            ></question-library-list>
          </el-form-item>
          <el-form-item label="关联课程">
            <course-drawer :value.sync="relateCourseIds" ref="relateCourse"></course-drawer>
          </el-form-item>

          <el-form-item label="试题题型" prop="questionTypes">
            <el-select v-model="questionRequestVo.questionType" placeholder="请选择试题题型" clearable>
              <el-option value="1" label="单选题"></el-option>
              <el-option value="2" label="多选题"></el-option>
              <el-option value="4" label="判断题"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="创建时间">
            <double-date-picker
              :beginCreateTime.sync="questionRequestVo.createTimeScope.beginTime"
              :endCreateTime.sync="questionRequestVo.createTimeScope.endTime"
              range-separator="至"
              start-placeholder="起始时间"
              end-placeholder="结束时间"
            ></double-date-picker>
          </el-form-item>

          <el-form-item label="试题状态" prop="enable">
            <el-select v-model="questionRequestVo.enable" placeholder="请选择试题状态" clearable>
              <el-option value="true" label="启用"></el-option>
              <el-option value="false" label="停用"></el-option>
            </el-select>
          </el-form-item>

          <template slot="actions">
            <el-button type="primary" @click="search">查询</el-button>
          </template>
          <template slot="actions" v-if="$hasPermission('export')" desc="导出" actions="exportQuest">
            <el-button type="primary" @click="exportQuest">导出</el-button>
          </template>
        </hb-search-wrapper>
        <!--表格-->
        <el-table
          stripe
          :data="questionListDetailVo"
          ref="questionTable"
          max-height="500px"
          class="m-table"
          v-loading="query.loading"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="试题题目" min-width="300" fixed="left">
            <template slot-scope="scope" :show-overflow-tooltip="true">
              <span v-html="scope.row.topic"></span>
            </template>
          </el-table-column>
          <el-table-column label="所属题库" min-width="180" prop="libraryName"> </el-table-column>
          <el-table-column label="关联课程" prop="relateCourseNames" min-width="280"> </el-table-column>
          <el-table-column label="题型" min-width="120">
            <template slot-scope="scope">
              <label>{{ statusMapType[scope.row.questionType] }}</label>
            </template>
          </el-table-column>
          <el-table-column label="状态" min-width="120">
            <template slot-scope="scope">
              <div v-if="scope.row.isEnabled === false">
                <el-badge is-dot type="info" class="badge-status">停用</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="success" class="badge-status">启用</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="180" align="center"></el-table-column>

          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template slot-scope="scope">
              <template
                v-if="$hasPermission('detail')"
                desc="详情"
                actions="@hbfe/jxjy-admin-question/src/detail.vue"
              >
                <el-button type="text" size="mini" @click="detail(scope.row.questionId, scope.row.questionType)"
                  >详情</el-button
                >
              </template>
              <template
                v-if="$hasPermission('modify')"
                desc="修改"
                actions="@hbfe/jxjy-admin-question/src/modify.vue"
              >
                <el-button type="text" size="mini" @click="modify(scope.row.questionId, scope.row.questionType)"
                  >修改</el-button
                >
              </template>
              <template v-if="$hasPermission('disable')" desc="停用" actions="disable">
                <hb-popconfirm placement="top" title="确定停用该试题？" @confirm="disable(scope.row.questionId)">
                  <el-button type="text" slot="reference" v-show="scope.row.isEnabled" size="mini">停用</el-button>
                </hb-popconfirm>
              </template>

              <!-- <el-button size="mini" @click="disableOpen(scope.row.questionId)" v-if="scope.row.isEnabled" type="text">
                停用
              </el-button> -->
              <template v-if="$hasPermission('enable')" desc="启用" actions="enable">
                <hb-popconfirm placement="top" title="确定启用该试题？" @confirm="enable(scope.row.questionId)">
                  <el-button type="text" slot="reference" size="mini" v-show="!scope.row.isEnabled">启用</el-button>
                </hb-popconfirm>
              </template>

              <!-- <el-button @click="openEnable(scope.row.questionId)" v-if="!scope.row.isEnabled" size="mini" type="text">
                启用
              </el-button> -->
              <template v-if="$hasPermission('remove')" desc="删除" actions="deleteItem">
                <hb-popconfirm placement="top" title="确定删除该试题？" @confirm="deleteItem(scope.row.questionId)">
                  <el-button type="text" slot="reference" size="mini">删除</el-button>
                </hb-popconfirm>
              </template>

              <!-- <el-button type="text" size="mini" @click="openDelete(scope.row.questionId)">删除</el-button> -->
            </template>
          </el-table-column>
        </el-table>

        <hb-pagination :page="page" v-bind="page"></hb-pagination>
        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务查看-试题</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goExportDownloadPage(true)">前往下载</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch, Ref } from 'vue-property-decorator'
  import QuestionLibraryList from '@hbfe/jxjy-admin-components/src/question-library-list.vue'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import courseDrawer from '@hbfe/jxjy-admin-question/src/components/course-drawer.vue'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { UiPage, Query } from '@hbfe/common'
  import QuestionRequestVo from '@api/service/management/resource/question/query/vo/QuestionRequestVo'
  import QuestionListDetailVo from '@api/service/management/resource/question/query/vo/QuestionListDetailVo'
  import { QuestionTypes } from '@api/service/management/resource/exam-paper/enum/ExamQuestionTypes'
  import MutationQuestionAsynTask from '@api/service/management/resource/question/mutation/MutationQuestionAsynTask'
  import QuestionOutputVo from '@api/service/management/resource/question/mutation/vo/QuestionOutputVo'
  import QestionType from '@hbfe/jxjy-admin-question/src/models/QestionType'
  import { ElTable } from 'element-ui/types/table'
  import { debounce, bind } from 'lodash-decorators'
  @Component({
    components: {
      QuestionLibraryList,
      DoubleDatePicker,
      courseDrawer
    }
  })
  export default class extends Vue {
    show = false
    @Ref('relateCourse') relateCourse: courseDrawer
    @Ref('questionLibraryListRef') questionLibraryListRef: QuestionLibraryList
    @Ref('questionTable') questionTable: ElTable
    @Prop({
      type: String,
      default: QestionType.JIGOU
    })
    questionType: QestionType

    loading = false // 加载中
    // defaultNode = new Array<QuestionLibraryUI>() // 题库默认选中项
    page: UiPage
    query: Query = new Query()
    questionRequestVo: QuestionRequestVo = new QuestionRequestVo()
    questionListDetailVo = new Array<QuestionListDetailVo>()
    questionTypesQuery = QuestionTypes.RadioQuestion
    remoteFlag = false
    relateCourseIds = ''
    routeId = ''
    routeIdFlag = 1
    isReload = true
    exportSuccessVisible = false
    //导出请求
    mutationQuestionAsynTask: MutationQuestionAsynTask = ResourceModule.mutationQuestionFactory.mutationQuestionAsynTask
    //导出入参
    questionOutputVo: QuestionOutputVo = new QuestionOutputVo()
    /**
     *  获取系统提供的是否启用选项
     */
    // enableOptionList = ExamUtil.productEnableOption()
    enableOptionList = [
      { value: true, title: '启用' },
      { value: false, title: '停用' }
    ]
    questionTypeList = [
      { value: 2, title: '单选题' },
      { value: 1, title: '判断题' },
      { value: 3, title: '多选题' }
    ] // 题型下拉
    statusMapType = {
      [QuestionTypes.RadioQuestion]: '单选题',
      [QuestionTypes.MultipleQuestion]: '多选题',
      [QuestionTypes.OpinionQuestion]: '判断题'
    }

    // 延时调用时间
    delayTime = 1500
    // get unitIdlist() {
    //   if (!isEmpty(this.params?.unitIds)) {
    //     return this.params.unitIds
    //   }
    //   // 当机构角色事 获取上下文中的id
    //   if (Context.businessContext?.serviceProvider?.serviceType === 1) {
    //     return Context.businessContext?.serviceProvider?.serviceId
    //   }
    //   if (this.questionType === QestionType.JIGOU) {
    //     return ['0']
    //   } else {
    //     return '-1'
    //   }
    // }
    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }
    async doSearch() {
      this.query.loading = true

      this.show = false
      if (this.routeId && this.routeIdFlag == 1) {
        this.questionRequestVo.libraryIdList = [this.routeId]
        this.routeIdFlag++
      }
      if (this.questionRequestVo.libraryIdList.length > 0) {
        this.questionRequestVo.libraryIdList.forEach((p, i) => {
          if (p == '-1') {
            this.questionRequestVo.libraryIdList.splice(i, 1)
          }
        })
      }
      this.show = true
      if (this.relateCourseIds) {
        this.questionRequestVo.relateCourseIds = [this.relateCourseIds]
      } else {
        this.questionRequestVo.relateCourseIds = []
      }
      this.query.loading = true
      console.log(this.questionRequestVo, '222222233333333333')
      const res = await ResourceModule.queryQuestionFactory.queryQuestionList.queryQuestionList(
        this.page,
        this.questionRequestVo
      )
      this.query.loading = false
      this.questionOutputVo = Object.assign(new QuestionOutputVo(), this.questionRequestVo)
      this.questionListDetailVo = res.data
      this.$nextTick(() => {
        this.questionTable.doLayout()
      })
      console.log(this.questionListDetailVo, 'this.questionListDetailVo')
    }

    /**
     * 组件被激活的时候
     * */

    async activated() {
      if (this.$route.query.id) {
        this.routeId = this.$route.query?.id as string

        this.routeIdFlag = 1
      }
      // this.$nextTick(async () => {
      //   this.questionLibraryListRef.selectNode = ['', this.routeId]
      //   await Promise.all(
      //     this.questionLibraryListRef.selectNode.map(async (item: string) => {
      //       await this.questionLibraryListRef.load(item)
      //     })
      //   )
      // })
      await this.doSearch()
    }
    // 清空
    clearEnable() {
      // this.params.enable = -1
      1
    }

    /**
     * 创建试题
     */
    create() {
      this.$router.push('/resource/question/create')
    }

    /**
     * 修改试题
     */
    modify(id: string, questionType: any) {
      // this.$router.push(`/resource/question/modify/${id}${questionType}`)

      this.$router.push('/resource/question/modify/' + id + '/' + questionType)
    }

    /**
     * 试题详情
     */
    detail(id: string, questionType: any) {
      // const randomId = Mock.Random.guid()
      // this.$router.push(`/resource/question/detail/${randomId}`)
      this.$router.push('/resource/question/detail/' + id + '/' + questionType)
    }

    /**
     * 导入试题
     */
    questionImport() {
      this.$router.push('/resource/question/import')
    }
    // 启用试题弹窗
    openEnable(id: string) {
      this.$confirm('确定启用该试题？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      })
        .then(() => {
          this.enable(id)
        })
        .catch(() => {
          console.log('取消')
        })
    }

    /**
     * 启用试题
     */
    async enable(id: string) {
      const res = await ResourceModule.mutationQuestionFactory.getQuestionAction(id).doEnable()
      if (res.isSuccess()) {
        this.$message.success('启用成功')

        // setTimeout(() => {
        //   this.$nextTick(() => {
        this.delayToDo(() => {
          this.doSearch()
        })
        // await this.doSearch()
        //   })
        // }, 50)
      } else {
        this.$message.error('启用失败')
      }
    }

    /**
     * 停用试题弹窗
     */
    disableOpen(id: string) {
      this.$confirm('确定停用该试题', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.disable(id)
        })
        .catch(() => {
          console.log('取消')
        })
    }

    /**
     * 停用试题
     */
    async disable(id: string) {
      const res = await ResourceModule.mutationQuestionFactory.getQuestionAction(id).doDisabled()
      if (res.isSuccess()) {
        this.$message.success('停用成功')
        // setTimeout(() => {
        //   this.$nextTick(() => {
        this.delayToDo(() => {
          this.doSearch()
        })
        //   })
        // }, 50)
      } else {
        this.$message.error('停用失败')
      }
    }

    /**
     * 重置查询条件
     */
    async resetCondition() {
      if (this.routeId) {
        this.$router.replace('/resource/question')
      }
      //else {
      //   location.reload()
      // }
      this.page.pageNo = 1
      // 题库被选中才做清除，避免空清报错
      if (this.questionRequestVo.libraryIdList.length) {
        this.questionLibraryListRef?.clearChoose()
      }
      this.questionRequestVo = new QuestionRequestVo()
      this.relateCourseIds = ''
      this.relateCourse.clear()
      this.remoteFlag = true
      await this.doSearch()
    }

    // 请求
    async search() {
      this.page.pageNo = 1
      this.loading = true
      await this.doSearch()
      this.loading = false
    }

    // 删除试题弹窗
    openDelete(id: string) {
      // this.deleteItem(item)
      this.$confirm('确定删除该试题？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'error'
      })
        .then(() => {
          this.deleteItem(id)
        })
        .catch(() => {
          console.log('已取消删除')
        })
    }

    /**
     * 删除试题
     */
    @bind
    @debounce(200)
    async deleteItem(id: string) {
      const res = await ResourceModule.mutationQuestionFactory.getQuestionAction(id).doDelete()
      if (res.isSuccess()) {
        this.$message.success('删除成功')
        // setTimeout(() => {
        //   this.$nextTick(() => {
        this.delayToDo(() => {
          this.doSearch()
        })
        // await this.doSearch()
        //   })
        // }, 1000)
      } else {
        if (res.errors[0].message === '试题已被引用，无法删除！可使用停用功能，试题停用后系统将不再随机抽取该试题！') {
          this.$confirm('提示', {
            title: '提示',
            message: res.errors[0].message,
            showCancelButton: true,
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'success'
          })
            .then(() => {
              console.log(1)
            })
            .catch(e => {
              console.log(e)
            })
        } else {
          this.$message.error(`${res.errors[0].message}`)
        }
      }
    }
    async exportQuest() {
      try {
        this.questionOutputVo = Object.assign(new QuestionOutputVo(), this.questionRequestVo)
        this.questionOutputVo.isEnabled = this.questionRequestVo.enable
        this.questionOutputVo.beginTime = this.questionRequestVo.createTimeScope.beginTime
        this.questionOutputVo.endTime = this.questionRequestVo.createTimeScope.endTime
        const res = await this.mutationQuestionAsynTask.doOutputQuestions(this.questionOutputVo)
        if (res) {
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }
    // 下载导出数据
    goExportDownloadPage(flag?: boolean) {
      if (flag) {
        this.exportSuccessVisible = false
      }
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportResourcesQuestions' }
      })
    }
    goImportDownloadPage() {
      this.$router.push({
        path: '/training/task/importtask',
        query: { type: '试题导入任务' }
      })
    }

    // 延时加载
    delayToDo(f: Function) {
      // todo
      setTimeout(() => {
        f()
      }, this.delayTime)
    }
  }
</script>
