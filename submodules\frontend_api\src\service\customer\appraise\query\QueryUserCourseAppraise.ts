import MsCourseLearningQueryFrontGatewayCourseLearningForestage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import CourseAppraiseListDetail from '@api/service/customer/appraise/query/vo/CourseAppraiseListDetail'

class QueryUserCourseAppraise extends CourseAppraiseListDetail {
  constructor(studentCourseAppraisalId: string) {
    super()
    this.studentCourseAppraisalId = studentCourseAppraisalId
  }

  private readonly studentCourseAppraisalId: string

  /**
   * 判断是否已经评价过了
   */
  hasAppraise() {
    return this.studentCourseAppraisalId
  }

  /**
   * 查询集合
   */
  async queryMyAppraise() {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.getCourseAppraiseInMyself(
      this.studentCourseAppraisalId
    )
    Object.assign(this, CourseAppraiseListDetail.from(result.data))
  }
}

export default QueryUserCourseAppraise
