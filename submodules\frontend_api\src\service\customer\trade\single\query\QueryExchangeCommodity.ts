import MsTradeQueryFrontGatewayCourseLearningForestage, {
  ExchangeOrderRequest,
  Page
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import ExchangeOrderResponseVo from '@api/service/customer/trade/single/query/vo/ExchangeOrderResponseVo'
import { ExchangeOrderStatue } from '@api/service/common/trade/ExchangeOrderStatue'

export class QueryExchangeCommodity {
  //  pageExchangeOrderInMySelf
  //  子订单号
  subOrderNo = ''
  //  子订单数组
  subOrderNoList: string[] = []
  /**
   * 获取换货单列表
   *
   */

  async queryExchangeOrderList(page: Page): Promise<Array<ExchangeOrderResponseVo>> {
    const request = new ExchangeOrderRequest()
    if (this.subOrderNoList?.length) {
      request.subOrderNoList = this.subOrderNoList
    } else {
      request.subOrderNoList = [this.subOrderNo]
    }
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageExchangeOrderInMySelf({
      page: page,
      request: request
    })
    if (res.status.isSuccess()) {
      const tmpArr = res.data.currentPageData as ExchangeOrderResponseVo[]
      for (const item of tmpArr) {
        const exchangingStatues = [0, 1, 2, 3, 4, 5, 6]
        if (exchangingStatues.indexOf(item.basicData.status) != -1) {
          item.exchangeStatue = ExchangeOrderStatue.ExchangeOrderStatueExchanging
        } else if (item.basicData.status == 7) {
          item.exchangeStatue = ExchangeOrderStatue.ExchangeOrderStatueExchangeSuccess
        } else {
          item.exchangeStatue = ExchangeOrderStatue.ExchangeOrderStatueExchangeCancel
        }
      }
      return tmpArr
    } else {
      return []
    }
  }
}
