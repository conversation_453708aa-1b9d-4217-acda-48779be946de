import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/NormalIssueClassLS-default'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'NormalIssueClassLS-default'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举

// 类

export class ExamAnswerSettingsDto {
  score: number
}

export class ShelvePlain {
  onShelve: boolean
  onShelvePlanTime?: string
  offShelvePlanTime?: string
}

/**
 * 申请选课Token信息
 */
export class ApplyChooseCourseTokenRequest {
  /**
   * 用户id
   */
  userId?: string
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
}

/**
 * 申请兴趣课选课Token信息
 */
export class ApplyChooseInterestCourseTokenRequest {
  /**
   * 用户id
   */
  userId?: string
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
}

/**
 * 申请课程学习Token信息
 */
export class ApplyCourseLearningTokenRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
  /**
   * 课程包ID
   */
  packageId?: string
  /**
   * 课程ID
   */
  courseId?: string
}

/**
 * 考试Token信息
 */
export class ApplyExamLearningTokenRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
}

/**
 * <AUTHOR>
@date 2020/8/18
@description
 */
export class ApplyInterestCourseLearningTokenRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
  /**
   * 课程包ID
   */
  poolId?: string
  /**
   * 课程ID
   */
  courseId?: string
}

/**
 * 申请练习学习Token
 */
export class ApplyQuestionLibPLTokenDto {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
  /**
   * 抽题数量
   */
  questionCount: number
}

/**
 * 清楚指定条件下的所有学习记录
<AUTHOR>
@date 2020/8/22
@description
 */
export class ClearLearningRecordRequest {
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 期数id
   */
  issueId?: string
}

/**
 * 完成指定条件下的所有学习方式的学习并通过考核
<AUTHOR>
@date 2020/8/22
@description
 */
export class CompletedLearningRequest {
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 用户id
   */
  userId?: string
  /**
   * 考试自动答题设置信息
   */
  examAutomaticAnswerSettings?: ExamAnswerSettingsDto
}

/**
 * 完成指定条件下的课程的学习
<AUTHOR>
@date 2020/8/22
@description
 */
export class CompletedOneCourseRequest {
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 学习方式id
   */
  learningId?: string
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 用户id
   */
  userId?: string
  /**
   * 弹窗题对题率，1~100
   */
  correctRate: number
}

/**
 * 完成指定条件下的课件的学习
<AUTHOR>
@date 2020/8/22
@description
 */
export class CompletedOneCoursewareRequest {
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 学习方式id
   */
  learningId?: string
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 用户id
   */
  userId?: string
  /**
   * 课件id
   */
  coursewareId?: string
}

/**
 * 课程学习方式设置信息
 */
export class CourseLearningSettingsRequest {
  /**
   * 是否启用课程学习方式
   */
  enabled: boolean
  /**
   * 必修课程包编号列表
   */
  compulsoryPackages?: Array<PackageRuleSettingRequest>
  /**
   * 选修课程包集合
   */
  optionalPackages?: Array<PackageRuleSettingRequest>
  /**
   * 所有选修包要求最多选课学时，如果不填，表示不设置要求
   */
  optionalTotalPeriod?: number
  /**
   * 课程学习考核，null表示不设置考核
   */
  assessSetting?: CourseLearningAssessSettingRequest
}

export class CourseLearningAssessSettingRequest {
  /**
   * 是否要求已选课程全部完成
   */
  allSelectedComplete: boolean
  /**
   * 课程学习完成进度
   */
  schedule: number
}

/**
 * <AUTHOR>
@date 2020/8/29
@description
 */
export class DeleteUserAnswerRecordRequest {
  userId?: string
  issueId?: string
  roundId?: string
  answerInfoId?: string
}

/**
 * 考试学习方式设置信息
 */
export class ExamLearningSettingsRequest {
  /**
   * 是否启用考试学习方式
   */
  enabled: boolean
  /**
   * 考试名称
   */
  name?: string
  /**
   * 试卷ID
   */
  examPaperId?: string
  /**
   * 考试时长，单位分钟
   */
  examTimeLength: number
  /**
   * 考试次数 0表示不限制次数
   */
  examCount: number
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 是否开放试题解析
   */
  openResolvedExam: boolean
  /**
   * 最短提交时长
   */
  minSubmitTimeLength: number
  /**
   * 是否启用考试前置条件
   */
  enabledExamLearningPrecondition: boolean
  /**
   * 考试考核，null表示不设置考核
   */
  assessSettingDto?: ExamAssessSettingRequest
  /**
   * 是否配置考试时间
   */
  configExamTime: boolean
  /**
   * 开考时间
   */
  beginTime?: string
  /**
   * 结束时间
   */
  endTime?: string
  /**
   * 多选漏选是否的分
   */
  less: boolean
  /**
   * 漏选得分模式
0:不得分，适用于漏选不得分情况
1:全得
2:得一半
3:平均得分
   */
  missScorePattern: number
}

export class ExamAssessSettingRequest {
  /**
   * 考试成绩
   */
  score: number
}

/**
 * 配置培训成果
<AUTHOR>
@date 2020/6/2
@since 1.0.0
 */
export class IssueClassLSAchieveSettingRequest {
  /**
   * 是否启用培训班考核
<pre>
如果启用，则默认使用选课学习方式和考试学习方式配置的考核作为子指标
</pre>
   */
  enabledAssess: boolean
  /**
   * 只有{@link #enabledAssess}为true，才生效；获取的学时，不填表示不获取学时成果
   */
  grade?: number
  /**
   * 只有{@link #enabledAssess}为true，才生效；获取证书的模板编号，不填表示不获取证书
   */
  templateId?: string
}

export class IssueClassLSCreateRequest {
  /**
   * 培训方案名称
   */
  name?: string
  /**
   * 年度
   */
  year: number
  /**
   * 封面图片地址
   */
  picture?: string
  /**
   * 发布机构
   */
  unitId?: string
  /**
   * web内容
   */
  content?: string
  /**
   * 手机端内容
   */
  mobileContent?: string
  /**
   * 培训班成果设置
   */
  achieveSetting?: IssueClassLSAchieveSettingRequest
  /**
   * 课程学习方式设置信息
   */
  courseLearningSettings?: CourseLearningSettingsRequest
  /**
   * 考试学习方式设置信息
   */
  examLearningSettings?: ExamLearningSettingsRequest
  /**
   * 试题练习学习方式配置
   */
  questionPracticeLearningSettings?: QPracticeLearningSettingsRequest
  /**
   * 适用人群
   */
  suitablePeople?: string
}

/**
 * 普通期数培训班期数创建信息
 */
export class IssueClassLSIssueCreateRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数名称
   */
  title?: string
  /**
   * 培训开始时间
   */
  startTime?: string
  /**
   * 培训结束时间
   */
  endTime?: string
  /**
   * 关闭的渠道
   */
  closeChannelList?: Array<number>
  /**
   * 排序
   */
  sort: number
  /**
   * 销售价格
   */
  price?: number
  /**
   * 销售介绍
   */
  saleIntroduce?: string
  /**
   * 上下架设置
   */
  shelvePlain?: ShelvePlain
  /**
   * 学习起止时间配置类型
@see com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.constants.IssueStudyTimeConfigTypeConst
1:分配指定学习时间
2:长期培训，无限制
   */
  studyTimeConfigType: number
  /**
   * 重新开通的渠道
   */
  reopenChannelList?: Array<number>
  /**
   * 是否开放学员购买
   */
  openCustomerPurchase: boolean
}

/**
 * 普通期数培训班期数更新信息
 */
export class IssueClassLSIssueUpdateRequest {
  /**
   * 要更新期数的学习方案ID
   */
  schemeId?: string
  /**
   * 要更新的期数ID
   */
  issueId?: string
  /**
   * 期数名称
   */
  title?: string
  /**
   * 培训开始时间
   */
  startTime?: string
  /**
   * 培训结束时间
   */
  endTime?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 关闭的渠道
   */
  closeChannelList?: Array<number>
  /**
   * 销售介绍
   */
  saleIntroduce?: string
  /**
   * 销售价格
   */
  price?: number
  /**
   * 上下架设置
   */
  shelvePlain?: ShelvePlain
  /**
   * 学习起止时间配置类型 1:分配指定学习时间 2:长期培训，无限制
   */
  studyTimeConfigType: number
  /**
   * 重新开通的渠道
   */
  reopenChannelList?: Array<number>
  /**
   * 是否开放学员购买
   */
  openCustomerPurchase: boolean
}

export class IssueClassLSUpdateRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 培训方案名称
   */
  name?: string
  /**
   * 封面图片地址
   */
  picture?: string
  /**
   * web内容
   */
  content?: string
  /**
   * 手机端内容
   */
  mobileContent?: string
  /**
   * 培训班成果设置
   */
  achieveSetting?: IssueClassLSAchieveSettingRequest
  /**
   * 课程学习方式设置信息
   */
  courseLearningSettings?: CourseLearningSettingsRequest
  /**
   * 考试学习方式设置信息
   */
  examLearningSettings?: ExamLearningSettingsRequest
  /**
   * 试题练习学习方式配置
   */
  questionPracticeLearningSettings?: QPracticeLearningSettingsRequest
  /**
   * 适用人群
   */
  suitablePeople?: string
}

/**
 * 期数是否可以预约校验信息
<AUTHOR>
 */
export class IssueJoinValidateRequest {
  /**
   * 商品SKU
   */
  skuId?: string
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 渠道类型
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  purchaseChannelType: number
  /**
   * 用户id
   */
  userId?: string
}

/**
 * 题库配置
<AUTHOR>
@date 2020/6/3
@since 1.0.0
 */
export class LibraryWaySettingRequest {
  /**
   * 题库id集合
   */
  libraryIds?: Array<string>
  /**
   * 是否递归取题
如果是题库卷时，才生效。
   */
  recursive: boolean
}

/**
 * 课程包规则设置
<AUTHOR>
@date 2020/6/1
@since 1.0.0
 */
export class PackageRuleSettingRequest {
  /**
   * 课程包编号
   */
  packageId?: string
  /**
   * 是否限制最大可选学时,如果设置true,则需要填写{@link #maxPeriod}
   */
  limit: boolean
  /**
   * 当前课程包要求最多选课学时，如果不填，表示不设置要求
   */
  maxPeriod?: number
}

/**
 * 试题练习学习方式配置
 */
export class QPracticeLearningSettingsRequest {
  /**
   * 是否启用试题练习
   */
  enabled: boolean
  /**
   * 抽题方式， 1：自定义（此时需要指定random配置），2：题库，3：考场所属方案下已选课程关联试题，4：标签方式抽题
   */
  fetchWay: number
  /**
   * 题库抽题方式配置
   */
  libraryWaySetting?: LibraryWaySettingRequest
  /**
   * 标签方式
   */
  tagsWaySetting?: TagWaySettingRequest
}

/**
 * 标签方式抽题
<AUTHOR>
@date 2020/6/19
@since
 */
export class TagWaySettingRequest {
  /**
   * 标签编号集合
   */
  tagIds?: Array<string>
}

export class ShelvePlain1 {
  onShelve: boolean
  onShelvePlanTime: string
  offShelvePlanTime: string
}

/**
 * 课程学习方式信息
 */
export class CourseLearningResponse {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 是否启用课程学习方式
   */
  enabled: boolean
  /**
   * 必修课程包编号列表
   */
  compulsoryPackages: Array<PackageResponse>
  /**
   * 选修课程包集合
   */
  optionalPackages: Array<PackageResponse>
}

/**
 * 考试学习方式信息
 */
export class ExamLearningResponse {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 是否启用考试学习方式
   */
  enabled: boolean
  /**
   * 考试名称
   */
  name: string
  /**
   * 试卷ID
   */
  examPaperId: string
  /**
   * 考试时长，单位分钟
   */
  examTimeLength: number
  /**
   * 考试次数 0表示不限制次数
   */
  examCount: number
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 是否开放试题解析
   */
  openResolvedExam: boolean
  /**
   * 最短提交时长
   */
  minSubmitTimeLength: number
  /**
   * 是否配置考试时间
   */
  configExamTime: boolean
  /**
   * 开考时间
   */
  beginTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 多选漏选是否的分
   */
  less: boolean
  /**
   * 漏选得分模式
0:不得分，适用于漏选不得分情况
1:全得
2:得一半
3:平均得分
   */
  missScorePattern: number
}

/**
 * 期数和商品SKU信息
 */
export class IssueAndCommoditySkuResponse {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 期数ID
   */
  issueId: string
  /**
   * 商品SkuId
   */
  commoditySkuId: string
  /**
   * 标题
   */
  title: string
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 关闭的渠道
   */
  closeChannelList: Array<number>
  /**
   * 销售价格
   */
  price: number
  /**
   * 上下架设置
   */
  shelvePlain: ShelvePlain1
  /**
   * 学习起止时间配置类型 1:分配指定学习时间 2:长期培训，无限制
   */
  studyTimeConfigType: number
  /**
   * 重新开通的渠道
   */
  reopenChannelList: Array<number>
  /**
   * 是否开放学员购买
@see PurchaseChannelTypes#CUSTOMER_PURCHASE
   */
  openCustomerPurchase: boolean
}

/**
 * <AUTHOR>
 */
export class JoinValidateResponse {
  /**
   * 验证结果
200：验证通过
500：验证不通过，带 message
40001：已经存在预定记录
40002：已经存在预约记录
40003：培训时间已结束
   */
  code: string
  /**
   * 验证失败原因
   */
  message: string
  /**
   * 当 code&#x3D; 40001，该值有效
   */
  orderNo: string
}

/**
 * 题库配置
<AUTHOR>
@date 2020/6/3
@since 1.0.0
 */
export class LibraryWaySettingResponse {
  /**
   * 题库id集合
   */
  libraryIds: Array<string>
  /**
   * 是否递归取题
如果是题库卷时，才生效。
   */
  recursive: boolean
}

/**
 * 普通期数培训班信息
 */
export class NormalIssueClassLSResponse {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 培训方案名称
   */
  name: string
  /**
   * 年度
   */
  year: number
  /**
   * 封面图片地址
   */
  picture: string
  /**
   * web内容
   */
  content: string
  /**
   * 手机端内容
   */
  mobileContent: string
  /**
   * 发布机构
   */
  unitId: string
  /**
   * 课程学习方式信息
   */
  courseLearning: CourseLearningResponse
  /**
   * 考试学习方式信息
   */
  examLearning: ExamLearningResponse
  /**
   * 试题练习学习方式信息
   */
  questionPracticeLearning: QuestionPracticeLearningResponse
  /**
   * 创建人ID
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 适用人群
   */
  suitablePeople: string
}

/**
 * 课程包规则设置
<AUTHOR>
@date 2020/6/1
@since 1.0.0
 */
export class PackageResponse {
  /**
   * 课程包编号
   */
  packageId: string
  /**
   * 是否限制最大可选学时,如果设置true,则需要填写{@link #maxPeriod}
   */
  limit: boolean
  /**
   * 当前课程包要求最多选课学时，如果不填，表示不设置要求
   */
  maxPeriod: number
}

/**
 * 试题练习学习方式信息
 */
export class QuestionPracticeLearningResponse {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 是否启用试题练习
   */
  enabled: boolean
  /**
   * 抽题方式， 1：自定义（此时需要指定random配置），2：题库，3：考场所属方案下已选课程关联试题，4：标签方式抽题
   */
  fetchWay: number
  /**
   * 题库抽题方式配置
   */
  libraryWaySetting: LibraryWaySettingResponse
  /**
   * 标签方式
   */
  tagsWaySetting: TagWaySettingResponse
}

/**
 * 标签方式抽题
<AUTHOR>
@date 2020/6/19
@since
 */
export class TagWaySettingResponse {
  /**
   * 标签编号集合
   */
  tagIds: Array<string>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取指定的考前培训方案
   * @param schemeId 学习方案ID
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findById(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.findById,
    operation?: string
  ): Promise<Response<NormalIssueClassLSResponse>> {
    return commonRequestApi<NormalIssueClassLSResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 期数购买校验
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validateJoinIssueLearningScheme(
    request: IssueJoinValidateRequest,
    query: DocumentNode = GraphqlImporter.validateJoinIssueLearningScheme,
    operation?: string
  ): Promise<Response<JoinValidateResponse>> {
    return commonRequestApi<JoinValidateResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请选课Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyChooseCourseToken(
    applyInfo: ApplyChooseCourseTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applyChooseCourseToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请兴趣课程选课Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyChooseInterestCourseToken(
    applyInfo: ApplyChooseInterestCourseTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applyChooseInterestCourseToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请考试Token，可能返回其他代码，如：
   * "403001" - 考试前置条件未通过
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyExamLearningToken(
    applyInfo: ApplyExamLearningTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applyExamLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请兴趣课程学习Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyInterestCourseLearningToken(
    applyInfo: ApplyInterestCourseLearningTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applyInterestCourseLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请练习试题练习Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyQuestionLibPracticeLearningToken(
    applyInfo: ApplyQuestionLibPLTokenDto,
    mutate: DocumentNode = GraphqlImporter.applyQuestionLibPracticeLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请课程学习Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applySingleCourseLearningToken(
    applyInfo: ApplyCourseLearningTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applySingleCourseLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 清除指定用户期数下的所有学习记录
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async clearAllLearningRecordForIssue(
    request: ClearLearningRecordRequest,
    mutate: DocumentNode = GraphqlImporter.clearAllLearningRecordForIssue,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 立即完成指定用户的一个期数的学习，涉及的所有学习方式都将自动完成，并通过所有考核
   * @param request 要立即完成的期数信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async completedAllLearningForIssue(
    request: CompletedLearningRequest,
    mutate: DocumentNode = GraphqlImporter.completedAllLearningForIssue,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 立即完成指定用户的一门课程的学习，学习进度达100%并答对所有未答的弹窗题
   * @param request 课程信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async completedLearningForOneCourse(
    request: CompletedOneCourseRequest,
    mutate: DocumentNode = GraphqlImporter.completedLearningForOneCourse,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 立即完成指定用户的一门课程下一个课件的学习，学习进度达100%并答对所有未答的弹窗题
   * @param request 课件信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async completedLearningForOneCourseware(
    request: CompletedOneCoursewareRequest,
    mutate: DocumentNode = GraphqlImporter.completedLearningForOneCourseware,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 在指定的普通期数培训班下新增期数
   * @param createInfo 期数创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createIssue(
    createInfo: IssueClassLSIssueCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createIssue,
    operation?: string
  ): Promise<Response<IssueAndCommoditySkuResponse>> {
    return commonRequestApi<IssueAndCommoditySkuResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建一个普通期数培训班
   * @param createInfo 创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createLS(
    createInfo: IssueClassLSCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createLS,
    operation?: string
  ): Promise<Response<NormalIssueClassLSResponse>> {
    return commonRequestApi<NormalIssueClassLSResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除用户答卷记录
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteUserExamAnswerInfoById(
    request: DeleteUserAnswerRecordRequest,
    mutate: DocumentNode = GraphqlImporter.deleteUserExamAnswerInfoById,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 发布普通期数培训班
   * @param schemeId 学习方案ID
   * @param mutate 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async publishLS(
    schemeId: string,
    mutate: DocumentNode = GraphqlImporter.publishLS,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除指定期数
   * @param schemeId 学习方案ID
   * @param issueId  期数ID
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeIssue(
    params: { schemeId?: string; issueId?: string },
    mutate: DocumentNode = GraphqlImporter.removeIssue,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除学习方案
   * @param schemeId 学习方案ID
   * @param mutate 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeLS(
    schemeId: string,
    mutate: DocumentNode = GraphqlImporter.removeLS,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新普通期数培训班指定的期数
   * @param updateInfo 期数更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateIssue(
    updateInfo: IssueClassLSIssueUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateIssue,
    operation?: string
  ): Promise<Response<IssueAndCommoditySkuResponse>> {
    return commonRequestApi<IssueAndCommoditySkuResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新一个普通期数培训班信息
   * @param updateInfo 更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateLS(
    updateInfo: IssueClassLSUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateLS,
    operation?: string
  ): Promise<Response<NormalIssueClassLSResponse>> {
    return commonRequestApi<NormalIssueClassLSResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateInfo },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
