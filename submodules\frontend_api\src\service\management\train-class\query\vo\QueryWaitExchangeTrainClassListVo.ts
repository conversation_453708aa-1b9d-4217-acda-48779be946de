import {
  BuyerValidCommodityRequest,
  SkuPropertyRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * @description 待换班列表查询条件
 */

class QueryWaitExchangeTrainClassListVo {
  /**
   * 买家id
   */
  buyerId = ''

  /**
   * 年度
   */
  year = ''

  /**
   * 班级名称
   */
  schemeName = ''

  /**
   * 培训形式
   */
  trainingMode: TrainingModeEnum = null

  /**
   * 期别名称
   */
  issueName = ''

  /**
   * 转为接口查询参数
   */
  to(): BuyerValidCommodityRequest {
    const to: BuyerValidCommodityRequest = new BuyerValidCommodityRequest()
    to.buyerId = this.buyerId || undefined
    to.saleTitle = this.schemeName || undefined
    to.skuProperty = new SkuPropertyRequest()
    to.skuProperty.year = this.year ? [this.year] : undefined
    to.issueName = this.issueName || undefined
    if (this.trainingMode) {
      to.skuProperty.trainingForm = [this.trainingMode]
    }
    return to
  }
}

export default QueryWaitExchangeTrainClassListVo
