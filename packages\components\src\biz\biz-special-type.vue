<route-meta>
{
"title": "选择专题类别"
}
</route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option v-for="item in options" :label="item.label" :value="item.value" :key="item.value"></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    selected = ''

    options = [
      {
        label: '地区',
        value: '1'
      },
      {
        label: '行业',
        value: '0'
      }
    ]

    @Prop({
      type: String,
      default: '请选择专题类型'
    })
    placeholder: string

    /**
     * 监听参数变化
     * @param val
     */
    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    /**
     * 监听选择值
     */
    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }
  }
</script>
