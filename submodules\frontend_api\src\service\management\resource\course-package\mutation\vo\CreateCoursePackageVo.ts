import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'

/**
 * 提供给 ui 使用的创建模型
 */
class CreateCoursePackageVo {
  // 业务名称
  name: string
  // 展示名称
  showName: string
  // 已选择待保存课程列表
  addedList: Array<CourseInCoursePackage> = new Array<CourseInCoursePackage>()

  /**
   * 根据 id 查询待选列表中的课程
   * @param id
   */
  getCourseById(id: string) {
    return this.addedList.find((course: CourseInCoursePackage) => course.id === id)
  }

  /**
   * 往待选确认列表中增加课程
   * @param courseInPackage
   */
  addCourse(courseInPackage: CourseInCoursePackage) {
    if (!this.getCourseById(courseInPackage.id)) {
      courseInPackage.period = courseInPackage.physicsPeriod
      this.addedList.push(courseInPackage)
    }
  }

  /**
   * 批量增加课程
   * @param courseInPackage
   */
  batchAddCourse(courseInPackage: Array<CourseInCoursePackage>) {
    this.addedList = this.addedList.concat(courseInPackage)
  }

  /**
   * 批量删除
   * @param courseInPackage
   */
  batchRemoveCourse(courseInPackage: Array<CourseInCoursePackage> = []) {
    courseInPackage.forEach((course: CourseInCoursePackage) => {
      const index = this.addedList.findIndex((addedCourse: CourseInCoursePackage) => {
        return addedCourse.id === course.id
      })
      this.addedList.splice(index, 1)
    })
  }

  /**
   * 根据索引值删除待选列表的课程
   * @param index
   * @private
   */
  private removeCourse(index: number) {
    if (index !== -1) {
      this.addedList.splice(index, 1)
    }
  }

  /**
   * 取消选择课程
   * @param course
   */
  cancelChoose(course: CourseInCoursePackage) {
    const findItemIndex = this.addedList.findIndex((itemCourse: CourseInCoursePackage) => course.id === itemCourse.id)
    this.removeCourse(findItemIndex)
  }

  /**
   * 换位置
   * @param oldIndex 旧索引
   * @param newIndex 新索引
   */
  changeCoursePositionByIndex(oldIndex: number, newIndex: number) {
    this.addedList[oldIndex] = this.addedList.splice(newIndex, 1, this.addedList[oldIndex])[0]
  }
}

export default CreateCoursePackageVo
