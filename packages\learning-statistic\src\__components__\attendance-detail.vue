<template>
  <div>
    <!-- 查看考勤详情抽屉 -->
    <el-drawer title="查看考勤详情" :visible.sync="isShowDrawer" direction="rtl" size="1000px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-alert type="warning" :closable="false" class="m-alert">
          <p><b>考勤规则</b></p>
          <p>1、{{ attendanceRuleText('签到') }}</p>
          <p>2、{{ attendanceRuleText('签退') }}</p>
        </el-alert>
        <!--表格-->
        <el-table stripe :data="attendanceReportObj.reportList" max-height="500px" class="m-table f-mt10">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="课程名称" min-width="220">
            <template slot-scope="scope">
              {{ scope.row.courseName }}
            </template>
          </el-table-column>
          <el-table-column label="授课时间" min-width="190" align="center">
            <template slot-scope="scope">
              <p><el-tag type="info" size="mini">开始</el-tag>{{ scope.row.teachTime.begin }}</p>
              <p><el-tag type="info" size="mini">结束</el-tag>{{ scope.row.teachTime.end }}</p>
            </template>
          </el-table-column>
          <el-table-column label="要求签到/签退情况" min-width="160" align="center">
            <template slot-scope="scope">
              {{ signText(scope.row) }}
            </template>
          </el-table-column>
          <el-table-column label="签到时间" min-width="120" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.signInTime">{{ scope.row.signInTime }}</span>
              <span v-else>
                {{ scope.row.signIning ? '' : '未签到' }}
              </span>
            </template>
          </el-table-column>
          <el-table-column label="签退时间" min-width="120" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.signOutTime">{{ scope.row.signOutTime }}</span>
              <span v-else>
                {{ scope.row.signOuting ? '' : '未签退' }}
              </span>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </div>
      <div class="drawer-ft m-btn-bar">
        <el-button type="primary" @click="handleBack">返回</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import { Page, UiPage } from '@hbfe/common'
  import QueryAttendanceReportList from '@api/service/management/statisticalReport/query/QueryAttendanceReportList'
  import AttendanceReportItem from '@api/service/management/statisticalReport/query/vo/AttendanceReportItem'
  import { CheckInFrequencyEnum } from '@api/service/common/implement/enums/CheckInFrequencyEnum'
  import AttendanceConfigDto from '@api/service/common/implement/models/AttendanceConfigDto'
  @Component
  export default class extends Vue {
    /**
     * 是否展示
     */
    @Prop({
      required: true,
      type: Boolean,
      default: false
    })
    value: boolean
    @Prop({
      required: true,
      type: String
    })
    qualificationId: string
    @Prop({
      required: true,
      type: String
    })
    period: string
    @Watch('value', {
      immediate: true,
      deep: true
    })
    valueChange(val: boolean) {
      this.isShowDrawer = cloneDeep(val)
    }
    @Emit('isAttendanceDialog')
    @Watch('isShowDrawer', {
      immediate: true,
      deep: true
    })
    showRoleDialogChange(val: number) {
      return val
    }
    /**
     * 弹窗显隐
     */
    isShowDrawer = false
    /**
     * 分页参数
     */
    page: UiPage = new UiPage()
    /**
     * 考勤详情对象
     */
    attendanceReportObj = new QueryAttendanceReportList()
    async created() {
      this.attendanceReportObj.qualificationId = this.qualificationId
      this.attendanceReportObj.periodId = this.period
      await this.attendanceReportObj.queryReportList(this.page)
      await this.attendanceReportObj.queryStudentPeriodLog()
      console.log(this.attendanceReportObj.reportList, '获取reportList')
    }
    /**
     * 返回
     */
    handleBack() {
      this.isShowDrawer = false
    }
    /**
     * 要求签退/签到情况文本
     */
    signText(row: AttendanceReportItem) {
      return (row.isSignIn ? '要求签到' : '不要求签到') + '/' + (row.isSignOut ? '要求签退' : '不要求签退')
    }
    /**
     * 考勤规则文本
     */
    attendanceRuleText(type: string) {
      let config = new AttendanceConfigDto()
      if (type == '签到') {
        config = this.attendanceReportObj.signIn
      } else {
        config = this.attendanceReportObj.signOut
      }
      return (
        '开启' +
        type +
        '：' +
        (config.checkInFrequency == CheckInFrequencyEnum.halfDay ? '每半天' : '每节课') +
        type +
        '一次，第一节课开始授课前' +
        (config.preCheckInTime || 0) +
        '分钟和开始授课后' +
        (config.afterCheckInTime || 0) +
        '分钟之间，需要' +
        type +
        '1次'
      )
    }
  }
</script>

<style scoped>
  .drawer-bd {
    flex: none;
  }
</style>
