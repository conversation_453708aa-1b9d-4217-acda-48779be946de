import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
import { cloneDeep } from 'lodash'
import { ResponseStatus } from '@hbfe/common'
import RegionTreeItem from '@api/service/management/online-learning-rule/model/RegionTreeItem'
import {
  RSProperty,
  JSProperty,
  WSProperty,
  LSProperty,
  GQProperty,
  YSProperty
} from '@api/service/management/online-learning-rule/model/Property'
import { TimeModeEnum } from '../enum/TimeModeEnum'
export default class BasicInfo {
  /**
   * 适用行业列表
   */
  industryIdList: Array<string> = []
  /**
   * 适用行业名称,如人社行业、建设行业
   */
  industryName = ''
  /**
   * 培训属性集合 人社行业
   */
  RSProperty = new RSProperty()
  /**
   * 培训属性集合 建设行业
   */
  JSProperty = new JSProperty()
  /**
   * 培训属性集合 职业卫生行业
   */
  WSProperty = new WSProperty()
  /**
   * 培训属性集合 工勤行业
   */
  GQProperty = new GQProperty()
  /**
   * 培训属性集合 教师行业
   */
  LSProperty = new LSProperty()
  /**
   * 培训属性集合 药师行业
   */
  YSProperty = new YSProperty()
  /**
   * 每天学习时长
   * 每天学习时长/学时学时
   * 仅支持文字输入，仅支持输入正整数
   * 单位分钟/小时
   */
  everyDayLearningTime = 0
  /**
   * 每天学习学时
   * 仅支持文字输入，仅支持输入正整数
   * 单位小时
   */
  everyDayLearningHours = 0
  /**
   * 时长方式
   * 1：按课程物理时长 2：按课程学习学时
   */
  timeMode: TimeModeEnum = undefined
  /**
   * 地区树
   */
  regionTree: Array<RegionTreeItem> = []
  /**
   * 培训区域-地区级使用，路径path
   */
  regionIds: Array<string> = []
  /**
   * 已选地区排序
   */
  private regionSort = 0
  /**
   * 剔除的地区按行业Map
   */
  // private excludeRegionsMap: Map<string, string[]> = new Map()
  /*----------------------地区相关方法----------------------*/
  /**
   * 获取地区树
   */
  async getRegionTree() {
    if (this.regionTree.length) return
    const serviceRegionIds = await QueryBusinessRegion.getServiceRegionIds()
    const regionTree = await QueryBusinessRegion.getCountrywideRegion()
    const getRegion = (
      tree: Array<RegionTreeVo>,
      newTree: Array<RegionTreeItem>,
      names: Array<string> = [],
      level = 1,
      parentCode = '-1'
    ) => {
      tree.forEach((item) => {
        const region = new RegionTreeItem()
        region.name = item.name
        region.names = [...names, item.name]
        region.code = item.code || item.id
        region.codePath = item.codePath || item.regionPath
        region.parentCode = parentCode
        region.level = level
        region.checked = this.regionIds.includes(region.code)
        region.isServiceArea = serviceRegionIds.includes(region.code)
        if (item.children && item.children.length > 0) {
          region.leaf = false
          region.children = new Array<RegionTreeItem>()
          getRegion(item.children, region.children, region.names, level + 1, region.code)
        } else {
          region.leaf = true
        }
        newTree.push(region)
      })
    }
    getRegion(regionTree, this.regionTree)
    if (this.regionIds.length) {
      const selectRegionList = this.getSelectedRegion()
      this.regionIds.forEach((item) => {
        // 查找列表指定节点路径
        const selectRegion = selectRegionList.find(
          (ite) => ite.code == item || ite.children?.findIndex((val) => val.code == item) > -1
        )
        const regionPath = selectRegion?.codePath.slice(1).split('/')
        // 找到树中指定节点进行回显顺序
        let region = new RegionTreeItem(),
          regionList = this.regionTree
        const getRegion = (regionId: string, regionList: Array<RegionTreeItem>) => {
          return regionList.find((item) => item.code == regionId)
        }
        regionPath.forEach((item) => {
          region = getRegion(item, regionList)
          regionList = region.children
        })
        region.selectSort = region.selectSort ?? this.regionSort++
      })
    }
  }

  /**
   * 获取剔除地区
   */
  // async getExcludeRegions() {
  //   if (this.excludeRegionsMap.has(this.industryId)) return
  //   const res = await StudyRulesSetting.getUseSupplementStudyRuleSettingRegionInServicer(this.industryId)
  //   if (res.status.isSuccess()) {
  //     this.excludeRegionsMap.set(this.industryId, res.data?.filter(item => !this.regionIds.includes(item)) ?? [])
  //   }
  // }

  /**
   * 选中地区
   * @param regionIds 选中的地区
   */
  selectRegion(regionIds: string[]) {
    if (regionIds.length) {
      this.recursionRegionTree(regionIds, this.regionTree, 'add')
    } else {
      console.log('咋，传个空数组？')
    }
  }

  /**
   * 更新指定节点下级地区
   * @param regionCode 指定节点
   * @param regionIds 选中的地区
   * @param type 删除传remove
   */
  updateRegionTree(regionCode: string, regionIds: string[], type?: string) {
    // 选中地区为空也当作删除处理
    type = regionIds?.length == 0 ? 'remove' : type
    const node = this.findRegionTreeNode(regionCode, this.regionTree)
    if (node) {
      if (type == 'remove') node.selectSort = undefined
      if (node.children) {
        this.recursionRegionTree(regionIds, node.children, type)
      } else if (type == 'remove') {
        node.checked = false
      }
    }
  }

  /**
   * 递归遍历地区树选中
   */
  recursionRegionTree(regionIds: string[], regionTree: Array<RegionTreeItem>, type?: string, isAllLeaf = false) {
    regionTree.forEach((item) => {
      switch (type) {
        case 'add':
          if (regionIds.includes(item.code)) {
            item.checked = true
            if (isAllLeaf) {
              item.selectSort = item.selectSort ?? this.regionSort
              this.regionSort++
            }
          } else if (item.children?.some((ite) => regionIds.includes(ite.code))) {
            item.selectSort = item.selectSort ?? this.regionSort
            this.regionSort++
          }
          break
        case 'remove':
          item.checked = false
          break
        default:
          item.checked = regionIds.includes(item.code)
          break
      }
      if (item.children?.length) this.recursionRegionTree(regionIds, item.children, type, item.isAllLeaf)
    })
  }

  /**
   * 查找指定节点
   */
  findRegionTreeNode(regionCode: string, regionTree: Array<RegionTreeItem>): RegionTreeItem {
    for (const item of regionTree) {
      if (item.code == regionCode) {
        return item
      }
      if (item.children?.length) {
        const target = this.findRegionTreeNode(regionCode, item.children)
        if (target) {
          return target
        }
      }
    }
  }

  /**
   * 打平地区树
   */
  getRegionList(regionTree: Array<RegionTreeItem>, regionList: Array<RegionTreeItem> = []) {
    regionTree.forEach((item) => {
      const region = Object.assign(new RegionTreeItem(), item)
      region.children = undefined
      regionList.push(item)
      if (item.children?.length) this.getRegionList(item.children, regionList)
    })
  }

  /**
   * 获取弹窗地区列表
   * @param regionCode 指定地区下所有末级
   * @param isExclude 是否剔除
   */
  // getRegionListDialog(regionCode: string, isExclude = false): Array<RegionTreeItem> {
  //   const regionLastList: Array<RegionTreeItem> = []
  //   const regionNode = this.findRegionTreeNode(regionCode, this.regionTree)
  //   if (regionNode) {
  //     if (regionNode.children?.length) {
  //       const regionList: Array<RegionTreeItem> = []
  //       this.getRegionList(regionNode.children, regionList)
  //       regionLastList.push(...regionList.filter(item => item.leaf && item.isServiceArea))
  //     } else if (regionNode.leaf && regionNode.isServiceArea) {
  //       const region = Object.assign(new RegionTreeItem(), regionNode)
  //       region.children = undefined
  //       regionLastList.push(region)
  //     }
  //   }
  //   if (isExclude) {
  //     return cloneDeep(regionLastList.filter(item => !this.excludeRegionsMap.get(this.industryId).includes(item.code)))
  //   } else {
  //     return cloneDeep(regionLastList)
  //   }
  // }

  /**
   * 获取选中的地区
   */
  getSelectedRegion(regionCode?: string): Array<RegionTreeItem> {
    const regionList: Array<RegionTreeItem> = []
    let regionTree: RegionTreeItem[] = this.regionTree
    if (regionCode) {
      const regionNode = this.findRegionTreeNode(regionCode, this.regionTree)
      if (regionNode.children?.length) {
        regionTree = [regionNode]
      } else if (regionNode && regionNode.checked) {
        return [regionNode]
      } else {
        return []
      }
    }
    regionTree.forEach((item) => {
      if (item.children?.length && item.hasChildrenChecked) {
        if (item.children.every((child) => child.leaf)) {
          const region = Object.assign(new RegionTreeItem(), item)
          region.children = item.children?.filter((child) => child.checked)
          regionList.push(region)
        } else {
          item.children.forEach((child) => {
            if (child.checked || child.hasChildrenChecked) {
              const region = Object.assign(new RegionTreeItem(), child)
              region.children = child.children?.filter((ite) => ite.checked)
              regionList.push(region)
            }
          })
        }
      }
    })
    return regionList.sort((a, b) => a.selectSort - b.selectSort)
  }
}
