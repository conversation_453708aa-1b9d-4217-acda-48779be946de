<template>
  <el-drawer title="查看详情" :visible.sync="isShow" size="600px" custom-class="m-drawer" :append-to-body="true">
    <div class="drawer-bd f-mt20 f-mlr40">
      <el-timeline v-for="(item, index) in changeShiftsDetailList" :key="index">
        <el-timeline-item>
          <p class="f-mb10 f-fb f-f15">
            {{ item.time }}<span class="f-ml30">{{ item.stateDescription }}</span>
          </p>
        </el-timeline-item>
      </el-timeline>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { ExchangeOrderRecordVo } from '@api/service/management/trade/single/order/query/vo/ExchangeOrderRecordVo'
  import { Component, Vue, Prop } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    @Prop({ type: Array, default: {} }) changeShiftsDetailList: Array<ExchangeOrderRecordVo>
    isShow = false

    isShowDialog() {
      this.isShow = !this.isShow
    }
  }
</script>

<style scoped></style>
