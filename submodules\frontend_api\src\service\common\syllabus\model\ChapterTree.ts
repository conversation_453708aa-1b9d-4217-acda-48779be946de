import Chapter from '@api/service/common/models/syllabus/Chapter'

class ChapterTree extends Chapter {
  /**
   * 子章节
   */
  children?: Array<ChapterTree> = new Array<ChapterTree>()

  /**
   * 是否包含子章节
   */
  hasChild(): boolean {
    return !!this.children?.length
  }

  /**
   * 获取第一个子章节
   */
  getFirstChildren(): Chapter | undefined {
    if (this.children && this.hasChild()) {
      return this.children[0] as Chapter
    }
    return undefined
  }

  /**
   * 获取章节下所有叶子节点
   */
  getLeafChapterIds(): Array<string> {
    if (this.hasChild()) {
      return this.children?.flatMap((child: ChapterTree) => child.getLeafChapterIds()) || []
    } else {
      return [this.id]
    }
  }

  /**
   * 获取章节树下所有叶子章节
   */
  getLeafChapterList(): Array<Chapter> {
    if (this.hasChild()) {
      return this.children?.flatMap((child: ChapterTree) => child.getLeafChapterList()) || []
    } else {
      return [this as Chapter]
    }
  }
}

export default ChapterTree
