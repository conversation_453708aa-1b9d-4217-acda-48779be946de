import QualifiedCourseVo from '@api/service/management/train-class/mutation/vo/QualifiedCourseVo'
import { Response } from '@hbfe/common'
import MsLearningScheme, { ApplyAutoLearningTokenRequest } from '@api/ms-gateway/ms-learningscheme-v1'
import MsChooseCourseLearningScene from '@api/ms-gateway/ms-choosecourselearningscene-v1'
import MsAutonomousCourseLearningScene from '@api/ms-gateway/ms-autonomouscourselearningscene-v1'
import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import ApplyQualifiedResponseVo from '@api/service/management/train-class/mutation/vo/ApplyQualifiedResponseVo'
import MsStudentCourseLearning from '@api/ms-gateway/ms-studentcourselearning-v1'

/**
 * @description
 */

class MutationQualifiedCourse {
  /**
   * 合格参数
   */
  qualifiedParams: QualifiedCourseVo = new QualifiedCourseVo()

  /**
   * 一键合格申请结果
   */
  applyResult: Response<ApplyQualifiedResponseVo> = new Response<ApplyQualifiedResponseVo>()

  /**
   * 课程一键合格
   */
  async doQualifiedCourse(qualifiedParams: QualifiedCourseVo) {
    const requestSuccess = this.applyResult.status?.isSuccess()
    // 1.申请学员学习token
    const studentStudyToken = await this.applyAutoLearningTokenForManage()
    if (!requestSuccess || !studentStudyToken) return
    // 2.申请一键学习token
    const oneKeyStudyToken = await this.applyCourseImmediatelyLearning(qualifiedParams, studentStudyToken)
    if (!requestSuccess || !oneKeyStudyToken) return
    // 3.提交一键学习
    await this.commitImmediatelyCourseLearning(oneKeyStudyToken)
  }

  /**
   * 1.申请学员学习token
   * @return 学员学习token
   */
  private async applyAutoLearningTokenForManage(): Promise<string> {
    let result = ''
    const request = new ApplyAutoLearningTokenRequest()
    request.qualificationId = this.qualifiedParams.qualificationId
    request.learningId = this.qualifiedParams.learningId
    const response = await MsLearningScheme.applyAutoLearningTokenForManage(request)
    // 网络不成功
    if (!response.status?.isSuccess()) {
      this.applyResult.status = response.status
      return
    }
    result = response.data?.token
    return result
  }

  /**
   * 2.申请课程一键学习token
   * @param {string} studentLearningToken - 学员学习token
   * @return 一键学习token
   */
  private async applyCourseImmediatelyLearning(
    qualifiedParams: QualifiedCourseVo,
    studentLearningToken: string
  ): Promise<string> {
    let response: any,
      result = ''
    const params = {
      studentLearningToken: studentLearningToken,
      studentCourseId: qualifiedParams.studentCourseId,
      quizScore: this.qualifiedParams.hasConfigCourseQuiz ? this.qualifiedParams.quizScore : undefined
    }
    if (this.qualifiedParams.schemeType === TrainClassSchemeEnum.Choose_Course_Learning) {
      /** 选课规则 */

      response = await MsChooseCourseLearningScene.applyCourseImmediatelyLearning(params)
    } else {
      /** 自主选课 */
      response = await MsAutonomousCourseLearningScene.applyCourseImmediatelyLearning(params)
    }
    // 网络不成功
    if (!response.status?.isSuccess()) {
      this.applyResult.status = response.status
      return
    }
    // 申请token不成功
    const applyResultCode = response.data?.applyResult?.code
    const applyResultMessage = response.data?.applyResult?.message
    if (applyResultCode !== '200') {
      const applyResult = new ApplyQualifiedResponseVo()
      applyResult.applyResultCode = applyResultCode
      applyResult.applyResultMessage = applyResultMessage
      this.applyResult.data = applyResult
      return
    }
    result = response.data?.token
    return result
  }

  /**
   * 3.提交课程一键学习token
   * @param {string} oneKeyStudyToken - 学员课程一键学习token
   *
   */
  private async commitImmediatelyCourseLearning(oneKeyStudyToken: string) {
    const response = await MsStudentCourseLearning.commitImmediatelyCourseLearning(oneKeyStudyToken)
    // 网络不成功
    if (!response.status?.isSuccess()) {
      this.applyResult.status = response.status
      return
    }
    this.applyResult.data = new ApplyQualifiedResponseVo()
    this.applyResult.data.applyResult = true
  }
}

export default MutationQualifiedCourse
