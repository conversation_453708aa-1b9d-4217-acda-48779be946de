import getImportUpdateTemplateUrl from './queries/getImportUpdateTemplateUrl.graphql'
import isProcessedByTransaction from './queries/isProcessedByTransaction.graphql'
import learningSchemeProcessTransactionStepQuery from './queries/learningSchemeProcessTransactionStepQuery.graphql'
import pageImportLearningSchemeImportTask from './queries/pageImportLearningSchemeImportTask.graphql'
import applyAutoLearningTokenForManage from './mutates/applyAutoLearningTokenForManage.graphql'
import applyRelearnTokenForManage from './mutates/applyRelearnTokenForManage.graphql'
import asyncCreateLearningScheme from './mutates/asyncCreateLearningScheme.graphql'
import asyncRemoveLearningScheme from './mutates/asyncRemoveLearningScheme.graphql'
import asyncUpdateLearningScheme from './mutates/asyncUpdateLearningScheme.graphql'
import batchUpdateCommodityPurchaseChannel from './mutates/batchUpdateCommodityPurchaseChannel.graphql'
import batchUpdateLearningScheme from './mutates/batchUpdateLearningScheme.graphql'
import batchUpdateLearningSchemeExport from './mutates/batchUpdateLearningSchemeExport.graphql'
import batchUpdateLearningSchemeExportFail from './mutates/batchUpdateLearningSchemeExportFail.graphql'
import batchUpdateSchemeRegion from './mutates/batchUpdateSchemeRegion.graphql'
import createLearningScheme from './mutates/createLearningScheme.graphql'
import learningSchemeCreateRepair from './mutates/learningSchemeCreateRepair.graphql'
import learningSchemeUpdateRepair from './mutates/learningSchemeUpdateRepair.graphql'
import oneKeyPass from './mutates/oneKeyPass.graphql'
import refreshConfigAndUpdate from './mutates/refreshConfigAndUpdate.graphql'
import refreshSchemeConfig from './mutates/refreshSchemeConfig.graphql'
import refreshSchemeConfigAndSaveConfig from './mutates/refreshSchemeConfigAndSaveConfig.graphql'
import relearnForStudent from './mutates/relearnForStudent.graphql'
import removeLearningScheme from './mutates/removeLearningScheme.graphql'
import repairLearningSchemeProcessTransaction from './mutates/repairLearningSchemeProcessTransaction.graphql'
import reservingSchemeIssueValidate from './mutates/reservingSchemeIssueValidate.graphql'
import reservingSchemeValidate from './mutates/reservingSchemeValidate.graphql'
import retryLearningConfigureResourceProcessCompletedEventHandle from './mutates/retryLearningConfigureResourceProcessCompletedEventHandle.graphql'
import specialUpdateLearningScheme from './mutates/specialUpdateLearningScheme.graphql'
import updateLearningScheme from './mutates/updateLearningScheme.graphql'
import userEnrolmentSchemeRepair from './mutates/userEnrolmentSchemeRepair.graphql'

export {
  getImportUpdateTemplateUrl,
  isProcessedByTransaction,
  learningSchemeProcessTransactionStepQuery,
  pageImportLearningSchemeImportTask,
  applyAutoLearningTokenForManage,
  applyRelearnTokenForManage,
  asyncCreateLearningScheme,
  asyncRemoveLearningScheme,
  asyncUpdateLearningScheme,
  batchUpdateCommodityPurchaseChannel,
  batchUpdateLearningScheme,
  batchUpdateLearningSchemeExport,
  batchUpdateLearningSchemeExportFail,
  batchUpdateSchemeRegion,
  createLearningScheme,
  learningSchemeCreateRepair,
  learningSchemeUpdateRepair,
  oneKeyPass,
  refreshConfigAndUpdate,
  refreshSchemeConfig,
  refreshSchemeConfigAndSaveConfig,
  relearnForStudent,
  removeLearningScheme,
  repairLearningSchemeProcessTransaction,
  reservingSchemeIssueValidate,
  reservingSchemeValidate,
  retryLearningConfigureResourceProcessCompletedEventHandle,
  specialUpdateLearningScheme,
  updateLearningScheme,
  userEnrolmentSchemeRepair
}
