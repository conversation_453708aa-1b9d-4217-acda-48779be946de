import { LoginAuthenticationDTO, UserInfoDTO } from '@api/gateway/PlatformUser'
import UserConstantsAdapter from '@api/service/common/common-user/UserConstantsAdapter'

/**
 * 角色信息
 */
export class Role {
  /**
   * 角色id
   */
  id: string
  /**
   * 角色名称
   */
  roleName: string
}

/**
 * 角色信息
 */
export class RoleInfo {
  /**
   * 角色id
   */
  roleId: string
  /**
   * 角色名称
   */
  name: string
}

/**
 * 用户信息
 */
class UserInfo {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 学员ID
   */
  userId = ''

  /**
   * 上下文平台ID
   */
  platformId: string
  /**
   * 上下文平台版本ID
   */
  platformVersionId: string
  /**
   * 上下文项目ID
   */
  projectId: string
  /**
   * 上下文子项目ID
   */
  subProjectId: string
  /**
   * 上下文单位ID
   */
  unitId: string
  /**
   * 上下文组织机构ID
   */
  organizationId: string

  /**
   * 姓名
   */
  name: string

  /**
   * 昵称
   */
  nickName: string

  /**
   * 唯一性类型,-1:未指定的唯一性标识,1:使用居民身份证作为用户唯一性标识
   */
  uniqueType: number

  /**
   * 唯一性值
   */
  uniqueData: string

  /**
   * 头像地址
   */
  displayPhotoUrl: string

  /**
   * 性别，1：男，2：女，3：中性，4：未知
   */
  sex: number

  /**
   * 出生年月
   */
  bornDay: Date

  /**
   * 手机号码
   */
  phoneNumber: string

  /**
   * 邮政编码
   */
  postCode: string

  /**
   * 所属地区编码
   */
  areaPath: string

  /**
   * 联系地址
   */
  address: string

  /**
   * 注册方式，11：平台注册，21：第三方互联之QQ，31：第三方互联之新浪微博，41：第三方互联之微信，42：第三方互联之 微信公众号，51：第三方互联之 外部来源
   */
  registerType: number

  /**
   * 注册来源，1：项目主网站，2：安卓，3：IOS 苹果
   */
  sourceType: number

  /**
   * 注册来源单位id
   */
  registerUnitId: string

  /**
   * 登陆账号
   */
  loginAccountNo: string

  /**
   * 用户角色
   */
  roles: Array<Role>
  /**
   * 工作单位名称(只存储名称，用于给没有实体工作单位的项目使用)
   */
  workUnitName: string
  /**
   * 工作单位所在地区路径(用于给没有实体工作单位的项目使用)
   */
  workUnitAreaPath: string
  /**
   * 登陆账号集合
   */
  loginAuthentications: Array<LoginAuthenticationDTO>

  /**
   * 所属人群
   */
  peoples: string

  /**
   * 判断是否登录
   */
  isLogin() {
    return !!this.userId
  }

  static from(dto: UserInfoDTO): UserInfo {
    const user = new UserInfo()
    user.peoples = dto.peoples
    user.accountId = dto.accountId
    user.userId = dto.userId
    user.name = dto.name
    user.nickName = dto.nickName
    // user.uniqueType = dto.uniqueType
    user.phoneNumber = dto.phone
    user.uniqueData = dto.idCard
    user.displayPhotoUrl = dto.photo
    user.sex = UserConstantsAdapter.getSex(dto.gender)
    // if (dto.bornDay) {
    //   user.bornDay = moment(dto.bornDay, Constants.DATE_PATTERN).toDate()
    // }
    // user.postCode = dto.postCode
    user.areaPath = dto.areaPath
    user.address = dto.address
    user.registerType = dto.registerType
    user.sourceType = dto.sourceType
    // user.registerUnitId = dto.registerUnitId
    user.roles = new Array<Role>()
    if (dto.roleList && dto.roleList.length) {
      user.roles.push(
        ...dto.roleList.map(r => {
          const role = new Role()
          role.id = r.id
          role.roleName = r.name
          return role
        })
      )
    }
    // user.workUnitAreaPath = dto.workUnitAreaPath
    user.workUnitName = dto.companyName
    user.loginAuthentications = dto.loginAuthentications
    return user
  }

  clone(): UserInfo {
    const user = new UserInfo()
    Object.assign(user, this)
    return user
  }

  // toStudentUpdateDTO(): BTPXStudentUpdateRequest {
  //   const dto = new BTPXStudentUpdateRequest()
  //   dto.userId = this.userId
  //   dto.name = this.name
  //   dto.nickName = this.nickName
  //   dto.uniqueType = this.uniqueType
  //   dto.phoneNumber = this.phoneNumber
  //   dto.uniqueData = this.uniqueData
  //   dto.displayPhotoUrl = this.displayPhotoUrl
  //   dto.sex = this.sex
  //   if (this.bornDay) {
  //     dto.bornDay = moment(this.bornDay).format(Constants.DATE_PATTERN)
  //   }
  //   dto.postCode = this.postCode
  //   dto.areaPath = this.areaPath
  //   dto.address = this.address
  //   dto.workUnitAreaPath = this.workUnitAreaPath
  //   dto.workUnitName = this.workUnitName
  //   return dto
  // }
}

export default UserInfo
