schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取防作弊结果
		@param randomCode
		@return
	"""
	getAntiCheatResult(randomCode:String):AntiResultResponse
	"""获取登录验证结果
		@param randomCode
		@return
	"""
	getLoginRandomCodeResult(randomCode:String):LoginRandomCodeResponse @NotAuthenticationRequired
}
type Mutation {
	"""申请防作弊code
		@param request
		@return
	"""
	applyAntiCheatCode(request:ApplyAntiCheatCodeRequest):CodeInitInfoResponse
	"""申请登录的随机码
		@return 返回随机码信息
	"""
	applyLoginRandomCode:LoginCodeInitInfoResponse @NotAuthenticationRequired
}
"""申请随机码参数
	<AUTHOR>
"""
input ApplyAntiCheatCodeRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.ApplyAntiCheatCodeRequest") {
	"""能力服务标记的code"""
	abilityCode:String
}
"""防作弊结果信息
	<AUTHOR>
"""
type AntiResultResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.AntiResultResponse") {
	"""随机码"""
	randomCode:String
	"""是否已过期"""
	expire:Boolean!
	"""是否验证通过"""
	verificationSuccess:Boolean!
	"""剩余可拍摄次数，-1表示无限制"""
	allowCount:Int!
	"""当前记录点已拍摄次数"""
	totalCount:Int!
}
"""<AUTHOR>
type CodeInitInfoResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.CodeInitInfoResponse") {
	"""随机码"""
	code:String
	"""二维码内容"""
	content:String
}
"""<AUTHOR>
type LoginCodeInitInfoResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.LoginCodeInitInfoResponse") {
	"""随机码"""
	code:String
}
"""随机码生成
	<AUTHOR> create 2021/2/6 16:37
"""
type LoginRandomCodeResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.LoginRandomCodeResponse") {
	"""登录使用的token信息
		若token信息没有值，则需要继续获取结果
	"""
	token:String
	"""随机码状态，若随机码失效则需要重新申请
		true-有效
		false-失效
	"""
	status:Boolean!
	"""若status=false 则有值
		<p>
		随机码失败的信息
	"""
	message:String
	"""若人脸认证失败则随机码还是有效，可继续使用重新认证
		<p>
		是否人脸认证失败
	"""
	validateFaceError:Boolean!
	"""人脸认证回调失败的code
		详细规则查看微信文档：https://developers.weixin.qq.com/community/business/doc/000442d352c1202bd498ecb105c00d
	"""
	faceErrorCode:String
	"""人脸认证回调失败的内容
		详细规则查看微信文档：https://developers.weixin.qq.com/community/business/doc/000442d352c1202bd498ecb105c00d
	"""
	faceErrorMsg:String
}

scalar List
