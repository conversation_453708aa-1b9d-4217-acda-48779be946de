import batchExportAllChooseDataInServicer from './queries/batchExportAllChooseDataInServicer.graphql'
import batchExportFailChooseDataInServicer from './queries/batchExportFailChooseDataInServicer.graphql'
import deleteCourseSubjectByIdInServicer from './queries/deleteCourseSubjectByIdInServicer.graphql'
import exportStudentCourseLearningQuotationInServicer from './queries/exportStudentCourseLearningQuotationInServicer.graphql'
import findBatchImportByPageInServicer from './queries/findBatchImportByPageInServicer.graphql'
import importCourseSubjectByExcelInServicer from './queries/importCourseSubjectByExcelInServicer.graphql'
import pageCourseSubjectInServicer from './queries/pageCourseSubjectInServicer.graphql'

export {
  batchExportAllChooseDataInServicer,
  batchExportFailChooseDataInServicer,
  deleteCourseSubjectByIdInServicer,
  exportStudentCourseLearningQuotationInServicer,
  findBatchImportByPageInServicer,
  importCourseSubjectByExcelInServicer,
  pageCourseSubjectInServicer
}
