import TrainClassBaseModel from '@api/service/diff/management/xmlg/train-class/model/TrainClassBaseModel'
import MutationCreateTrainClassCommodity, {
  CreateCommodityResponse
} from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
import { Response } from '@hbfe/common'
import TrainClassConfigJsonManager from '@api/service/management/train-class/Utils/TrainClassConfigJsonManager'
import MsLearningScheme from '@api/ms-gateway/ms-learningscheme-v1'
import PlatformLearningschemeV1 from '@api/platform-gateway/platform-learningscheme-v1'

/**
 * 创建制培训班商品类//创建，修改和复制，最终都是拿到这个业务类，ui层无需关心，就按照统一逻辑调用即可
 */
class MutationCreateTrainClassCommodityDiff extends MutationCreateTrainClassCommodity {
  // region properties
  /**
   *培训班基础信息，类型为TrainClassBaseModel
   */
  trainClassBaseInfo = new TrainClassBaseModel()

  /**
   * 创建或者更新培训班商品
   */
  async createTrainClassCommodity() {
    // if (this.trainClassBaseInfo.id)
    //   console.log(TrainClassConfigJsonManager.convertCreateCommodityToJsonString(this, this.learningTypeModelCopy))
    // else console.log(TrainClassConfigJsonManager.convertCreateCommodityToJsonString(this))
    // return new ResponseStatus(30006, '报错')
    let configJson = ''
    const result = new Response<CreateCommodityResponse>()
    result.data = new CreateCommodityResponse()
    const convertUtil = TrainClassConfigJsonManager
    if (this.trainClassBaseInfo.id) {
      configJson = convertUtil.convertCreateCommodityToJsonString(this, this.learningTypeModelCopy)
      if (this.trainClassBaseInfo.thirdPartyId) {
        const jsonObj = JSON.parse(configJson)
        if (!jsonObj.extendProperties) {
          jsonObj.extendProperties = []
        }
        jsonObj.extendProperties.push({
          name: 'externalTrainingPlatform',
          value: this.trainClassBaseInfo.thirdPartyId
        })
        configJson = JSON.stringify(jsonObj)
      }
      const params = {
        token: 'W10=',
        configJson
      }
      if (this.isBaseInfoModify) {
        // 基础信息修改
        const response = await MsLearningScheme.specialUpdateLearningScheme(params)
        result.status = response.status
        result.data.schemeId = response.data
      } else {
        // 修改培训方案
        const response = await PlatformLearningschemeV1.asyncUpdateLearningScheme(params)
        result.status = response.status
        const data = response.data
        if (data) {
          result.data.schemeId = data.schemeId
        }
      }
    } else {
      configJson = convertUtil.convertCreateCommodityToJsonString(this)
      if (this.trainClassBaseInfo.thirdPartyId) {
        const jsonObj = JSON.parse(configJson)
        if (!jsonObj.extendProperties) {
          jsonObj.extendProperties = []
        }
        jsonObj.extendProperties.push({
          name: 'externalTrainingPlatform',
          value: this.trainClassBaseInfo.thirdPartyId
        })
        configJson = JSON.stringify(jsonObj)
      }
      const response = await PlatformLearningschemeV1.asyncCreateLearningScheme({
        token: 'W10=',
        configJson
      })
      result.status = response.status
      const data = response.data
      if (data) {
        result.data.schemeId = data.schemeId
      }
    }
    return result
  }

  // endregion
}
export default MutationCreateTrainClassCommodityDiff
