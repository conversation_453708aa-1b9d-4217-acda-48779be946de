import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-dictionary-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-dictionary-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 行业新增
 */
export class IndustryPropertyAddRequest {
  /**
   * 所属行业id
   */
  bisId?: string
  /**
   * 行业属性名称
   */
  propertyName?: string
  /**
   * 行业培训属性新增对象集合
   */
  trainingPropertyList?: Array<IndustryPropertyCategoryAddRequest>
  /**
   * 序号
   */
  sort: number
}

/**
 * 行业属性类别新增对象
 */
export class IndustryPropertyCategoryAddRequest {
  /**
   * 行业属性分类code代码
@see DictionaryConstant.IndustryPropertyCode
   */
  industryPropertyCategoryCode?: string
  /**
   * 序号
   */
  sort: number
  /**
   * 行业培训属性新增对象
   */
  propertyList?: Array<IndustryTrainingPropertyAddRequest>
}

/**
 * 行业属性类别新增对象
 */
export class IndustryPropertyCategoryUpdateRequest {
  /**
   * 行业属性分类code代码
@see DictionaryConstant.IndustryPropertyCode
   */
  industryPropertyCategoryCode?: string
  /**
   * 序号
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 新增的行业培训属性id集合
新增先执行
   */
  addPropertyIdList?: Array<string>
  /**
   * 移除的行业培训属性id集合
删除后执行
   */
  removePropertyIdList?: Array<string>
}

export class IndustryPropertyUpdateRequest {
  /**
   * 行业属性id
   */
  industryPropertyId?: string
  /**
   * 所属行业id
   */
  bisId?: string
  /**
   * 行业属性名称
   */
  propertyName?: string
  /**
   * 行业培训属性新增对象集合
   */
  trainingPropertyList?: Array<IndustryPropertyCategoryUpdateRequest>
  /**
   * 序号
   */
  sort: number
}

/**
 * 行业培训属性新增对象
 */
export class IndustryTrainingPropertyAddRequest {
  /**
   * 行业培训属性id
   */
  propertyId?: string
}

export class IndustryTrainingPropertyUpdateRequest {
  /**
   * 属性id
   */
  propertyId?: string
  /**
   * 所属行业id
   */
  bisId?: string
  /**
   * 行业属性名称
   */
  showName?: string
  /**
   * 网校id
   */
  schoolId?: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 新增行业属性
   * @param industryAdd 新增参数
   * @return id
   * @param mutate 查询 graphql 语法文档
   * @param industryAdd 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addIndustryProperty(
    industryAdd: IndustryPropertyAddRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.addIndustryProperty,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { industryAdd },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 移除行业属性
   * @param propertyId 属性id
   * @param mutate 查询 graphql 语法文档
   * @param propertyId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeIndustryProperty(
    propertyId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeIndustryProperty,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { propertyId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改行业属性
   * @param updateDto 更新参数
   * @param mutate 查询 graphql 语法文档
   * @param updateDto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateIndustryProperty(
    updateDto: IndustryPropertyUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateIndustryProperty,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateDto },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改网校名称
   * @param request 参数信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateShowName(
    request: IndustryTrainingPropertyUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateShowName,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 验证属性名称是否存在
   * @param propertyId   属性id
   * @param propertyName 属性名称
   * @return 属性名称是否存在
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifyNameExists(
    params: { propertyId?: string; propertyName?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.verifyNameExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
