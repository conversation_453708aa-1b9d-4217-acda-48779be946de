import StatisticInfoItem from '@api/service/management/statisticalReport/models/StatisticInfoItem'
import {
  DistributionSellPayTypeStatisticResponse,
  DistributorSellStatisticIncludedPurchaseResponse
} from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import { PurchaseChannelEnums } from '@api/service/management/statisticalReport/DistributionGoodsOpeningStatistics/model/DistributionGoodsOpeningStatisticsItem'

export default class SupplierDistributorSalesStatisticsInfo {
  /**
   * 商品ID
   */
  schemeId = ''
  /**
   * 分销商名称
   */
  distributorName = ''
  /**
   * 合计
   */
  total = new StatisticInfoItem()
  /**
   * 个人 - 线上支付
   */
  individualOnline = new StatisticInfoItem()
  /**
   * 集体 - 线上支付
   */
  collectivelyOnline = new StatisticInfoItem()
  /**
   * 集体 - 线下支付
   */
  collectivelyOffline = new StatisticInfoItem()
  /**
   * 导入 - 线下支付
   */
  importOffline = new StatisticInfoItem()

  static fromDistributorSellStatisticIncludedPurchaseResponse(dto: DistributorSellStatisticIncludedPurchaseResponse) {
    const vo = new SupplierDistributorSalesStatisticsInfo()
    vo.schemeId = dto.distributorId
    vo.distributorName = dto.distributorName
    vo.total = StatisticInfoItem.toStatisticInfoItem(dto.distributorSellSummary)
    if (dto?.distributionSellPurchaseStatisticList) {
      dto.distributionSellPurchaseStatisticList.map(res => {
        // 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
        // 支付方式  支付方式 1：线上支付 2：线下支付,-1:表示未支付
        if (res.purchaseChannel === PurchaseChannelEnums.user_made_purchases) {
          const temp = SupplierDistributorSalesStatisticsInfo.returnPayTypeData(res.payTypeStatisticResponseList, 1)
          if (temp) {
            vo.individualOnline = StatisticInfoItem.toStatisticInfoItem(temp)
          }
        }
        if (res.purchaseChannel === PurchaseChannelEnums.group_contributions) {
          let temp = SupplierDistributorSalesStatisticsInfo.returnPayTypeData(res.payTypeStatisticResponseList, 1)
          if (temp) {
            vo.collectivelyOnline = StatisticInfoItem.toStatisticInfoItem(temp)
          }
          temp = SupplierDistributorSalesStatisticsInfo.returnPayTypeData(res.payTypeStatisticResponseList, 2)
          if (temp) {
            vo.collectivelyOffline = StatisticInfoItem.toStatisticInfoItem(temp)
          }
        }
        if (res.purchaseChannel === PurchaseChannelEnums.admin_import) {
          const temp = res.payTypeStatisticResponseList.find(item => item.payType === 2)?.statisticInfo
          if (temp) {
            vo.importOffline = StatisticInfoItem.toStatisticInfoItem(temp)
          }
        }
      })
    }
    return vo
  }

  static returnPayTypeData(data: DistributionSellPayTypeStatisticResponse[], type: number) {
    return data.find(item => item.payType === type)?.statisticInfo
  }
}
