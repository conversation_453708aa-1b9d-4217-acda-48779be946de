import { Mu<PERSON><PERSON>reatePaper } from '@api/service/customer/exam/mutation/MutationCreatePaper'
import { ResponseStatus } from '@hbfe/common'
import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
import MsStudentlearningV1, { UnaccomplishedQuestionnaire } from '@api/ms-gateway/ms-studentlearning-v1'

export class MutationCreateExamPaper extends MutationCreatePaper {
  /**
   * 作答token
   */
  answerToken = ''

  /**
   * 强制问卷列表
   */
  forcedQuestionnaireList = [] as QuestionnaireConfigDetail[]

  /**
   * 获取作答token
   * @description
   * 7001 需要强制完成调查问卷
   */
  async getAnswerToken(): Promise<ResponseStatus> {
    let result = new ResponseStatus(200)
    const { status, data } = await MsStudentlearningV1.applyExam(this.studentToken)
    result = status
    if (status.isSuccess() && data) {
      this.answerToken = data.examToken
      const { needForceQuestionnaire, unaccomplishedQuestionnaire } = data
      if (needForceQuestionnaire) {
        result = new ResponseStatus(7001, '需要强制完成调查问卷')
        if (unaccomplishedQuestionnaire && unaccomplishedQuestionnaire.length) {
          this.forcedQuestionnaireList = unaccomplishedQuestionnaire.map((el: UnaccomplishedQuestionnaire) => {
            const opt = new QuestionnaireConfigDetail()
            opt.id = el.learningId
            opt.configId = el.unaccomplishedQuestionnaireId
            opt.openDateRange.startDate = el.allowStartTime
            opt.openDateRange.endDate = el.allowEndTime
            return opt
          })
        }
      }
    }
    return result
  }
}
