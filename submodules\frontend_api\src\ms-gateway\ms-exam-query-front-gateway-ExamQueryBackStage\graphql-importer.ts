import getAnswerPaperRecordInServicer from './queries/getAnswerPaperRecordInServicer.graphql'
import getAskQuestionInServicer from './queries/getAskQuestionInServicer.graphql'
import getFatherQuestionInServicer from './queries/getFatherQuestionInServicer.graphql'
import getFillQuestionInServicer from './queries/getFillQuestionInServicer.graphql'
import getLibraryInServicer from './queries/getLibraryInServicer.graphql'
import getMultipleQuestionInServicer from './queries/getMultipleQuestionInServicer.graphql'
import getOpinionQuestionInServicer from './queries/getOpinionQuestionInServicer.graphql'
import getPaperPublishConfigureCategoryInServicer from './queries/getPaperPublishConfigureCategoryInServicer.graphql'
import getPaperPublishConfigureInServicer from './queries/getPaperPublishConfigureInServicer.graphql'
import getQuestionCountByRelateCourseInServicer from './queries/getQuestionCountByRelateCourseInServicer.graphql'
import getQuestionCountInServicer from './queries/getQuestionCountInServicer.graphql'
import getQuestionCountInSubProject from './queries/getQuestionCountInSubProject.graphql'
import getRadioQuestionInServicer from './queries/getRadioQuestionInServicer.graphql'
import pageCourseQuizRecordInSubProject from './queries/pageCourseQuizRecordInSubProject.graphql'
import pageExaminationRecordInSubProject from './queries/pageExaminationRecordInSubProject.graphql'
import pageLibraryInServicer from './queries/pageLibraryInServicer.graphql'
import pagePaperPublishConfigureCategoryInServicer from './queries/pagePaperPublishConfigureCategoryInServicer.graphql'
import pagePaperPublishConfigureInServicer from './queries/pagePaperPublishConfigureInServicer.graphql'
import pagePracticeRecordInSubProject from './queries/pagePracticeRecordInSubProject.graphql'
import pageQuestionInServicer from './queries/pageQuestionInServicer.graphql'

export {
  getAnswerPaperRecordInServicer,
  getAskQuestionInServicer,
  getFatherQuestionInServicer,
  getFillQuestionInServicer,
  getLibraryInServicer,
  getMultipleQuestionInServicer,
  getOpinionQuestionInServicer,
  getPaperPublishConfigureCategoryInServicer,
  getPaperPublishConfigureInServicer,
  getQuestionCountByRelateCourseInServicer,
  getQuestionCountInServicer,
  getQuestionCountInSubProject,
  getRadioQuestionInServicer,
  pageCourseQuizRecordInSubProject,
  pageExaminationRecordInSubProject,
  pageLibraryInServicer,
  pagePaperPublishConfigureCategoryInServicer,
  pagePaperPublishConfigureInServicer,
  pagePracticeRecordInSubProject,
  pageQuestionInServicer
}
