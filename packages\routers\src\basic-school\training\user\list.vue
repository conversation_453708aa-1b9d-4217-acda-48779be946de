<route-meta>
{
  "title": "用户列表",
  "requiresAuth": true,
  "permission": "user.view",
  "isMenu": true
}
</route-meta>

<template>
  <div>
    <!-- 直接引用我们创建的用户列表组件 -->
    <UserListPage />
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import UserListPage from '@packages/user/src/views/user-list/index.vue'

  @Component({
    components: {
      UserListPage
    }
  })
  export default class UserListRoute extends Vue {
    // 这个文件会自动生成路由: /training/user/list
  }
</script>
