import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import QueryStudentDetail from '@api/service/management/user/query/student/QueryStudentDetail'
import TopicInformationVo from '@api/service/diff/management/gszj/user/student/model/TopicInformationVo'
import { Response } from '@hbfe/common'
import UserDetailVo from '@api/service/diff/management/gszj/user/student/model/UserDetailVo'
import BasicDataQueryBackstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { DictionaryTypeEnum } from '@api/service/diff/common/gszj/basic-data-dictionary/query/enum/DictionaryType'
import QueryDictionaryType from '@api/service/diff/common/gszj/basic-data-dictionary/query/QueryDictionaryType'
/**
 * 查询用户详情
 */

class QueryStudentDetailDiff extends QueryStudentDetail {
  /**
   * 查询差异化的用户信息字段
   */
  async queryDetailDiff(): Promise<Response<UserDetailVo>> {
    const userInfo = await this.queryDetail()
    const { data } = await BasicDataQueryBackstage.listAreaTrainingChannelStudentInfoInSubProject(this.studentUserId)
    const dictionaryTypeList = [
      DictionaryTypeEnum.GSZJ_STAFFING_STATUS,
      DictionaryTypeEnum.GSZJ_TITLE_EFFECTIVE_RANGE,
      DictionaryTypeEnum.GSZJ_TITLE_GRADE,
      DictionaryTypeEnum.GSZJ_TITLE_SERIES,
      DictionaryTypeEnum.GSZJ_UNIT_NATURE
    ]
    // 使用Promise.all并行请求
    await Promise.all(dictionaryTypeList.map((item) => QueryDictionaryType.queryDictionaryTypeList(item)))
    return UserDetailVo.from(userInfo, data)
  }
}
export default QueryStudentDetailDiff
