const path = require('path')
const VueAutoRoutingPlugin = require('../vue-auto-routing/lib/webpack-plugin')
const { dirList, unitTypeDir, suffix } = require('./util')

module.exports = dirList.map(dir => {
  const _path = `@hbfe/jxjy-admin-routers/src/${dir}/`
  return new VueAutoRoutingPlugin({
    importPrefix: _path,
    pages: path.join(unitTypeDir, dir),
    chunkNamePrefix: _path,
    cacheDir: dir + suffix,
    baseList: []
  })
})
