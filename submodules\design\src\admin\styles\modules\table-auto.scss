@import "../../../common/variables";

//表格高度自适应
.m-table-auto {
  height: 100%;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;

  .el-card {
    flex-shrink: 0;
  }

  .el-table {
    flex: initial;
    width: calc(100% + 10px);

    &::before,
    &::after {
      content: ' ';
      width: 100%;
      height: 1px;
      position: absolute;
      bottom: 0;
      left: 0;
      background-color: #fff;
      z-index: 9;
    }

    &::before {
      background-color: #ebeef5;
      bottom: 10px;
    }

    tr {
      .el-table__cell {
        &:first-child {
          position: relative;
          left: 10px;
        }

        &.is-hidden {
          & + .el-table__cell {
            padding-left: 10px;
          }

          & + .is-hidden {
            padding-left: 0;
          }
        }
      }
    }

    &.el-table--scrollable-x {
      .el-table__fixed-body-wrapper {
        max-height: calc(100% - 50px);//减去表头高度
      }
    }

    .el-table__fixed-right,
    .el-table__fixed {
      bottom: 10px;

      tr {
        .el-table__cell {
          &:first-child {
            left: 0;
          }
        }
      }
    }

    .el-table__fixed-right {
      right: 10px;
    }

    .el-table__header-wrapper {
      position: relative;
      left: -10px;
    }

    .el-table__body-wrapper {
      overflow: scroll;
      width: calc(100% + 10px);
      max-height: calc(100% - 50px);//减去表头高度
      position: relative;
      right: 10px;

      &::-webkit-scrollbar-track {
        background: #fff;
      }
    }

    .el-table__fixed-body-wrapper {
      max-height: calc(100% - 60px);//减去表头高度 + 10px
    }

    .el-table__fixed-right-patch {
      width: 10px !important;
      background-color: #fff;
      border-bottom-color: #fff;
    }
  }

  .m-card {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin-bottom: 0;
    min-height: 580px;

    .el-card__header {
      & + .el-card__body {
        height: calc(100% - 54px);
      }
    }

    .el-card__body {
      display: flex;
      flex-direction: column;
      height: 100%;
      box-sizing: border-box;
    }
  }

  .el-alert {
    flex-shrink: 0;
  }

  .m-tab-card {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    min-height: 580px;

    .el-tabs__content {
      flex: 1;
    }

    .el-tab-pane {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }

  .el-pagination {
    flex: 1;
    margin-top: 5px;
    margin-bottom: -8px;
  }

  .m-tab-top {
    display: flex;
    flex-direction: column;
    height: 100%;

    .el-tabs__content {
      height: calc(100% - 60px);
    }

    .el-tab-pane {
      height: 100%;
    }
  }

  .el-row {
    &.is-height {
      height: 100%;

      .el-col {
        height: 100%;
      }
    }
  }

  &.m-drawer {
    .el-drawer__body {
      overflow: auto;
    }

    .drawer-bd {
      overflow: auto;
      display: flex;
      flex-direction: column;
      min-height: 580px;
    }
  }

  &.m-tab-top {
    display: flex;
    flex-direction: column;
    height: 100%;

    .m-card {
      height: 100%;
    }

    .el-tabs__content {
      height: calc(100% - 60px);
    }

    .el-tab-pane {
      height: 100%;

      & > .f-p15 {
        height: 100%;
        box-sizing: border-box;
      }
    }

    .m-tab-card {
      display: flex;
      flex-direction: column;
      height: 100%;

      &.m-table-auto {
        .m-card {
          height: 100%;
        }
      }

      .el-tabs__content {
        flex: 1;
      }

      .el-tab-pane {
        display: flex;
        flex-direction: column;
      }
    }
  }
}

.el-breadcrumb {
  & + .m-table-auto {
    height: calc(100% - 45px);
  }

  & + div {
    .m-table-auto {
      height: calc(100% - 45px);
    }
  }
}

.m-dialog {
  .m-table-auto {
    .m-query {
      margin-right: 0 !important;
    }

    .el-pagination {
      margin-bottom: -4px;
    }
  }
}

@media screen and (max-height: 800px) {
  //表格高度自适应
  .m-table-auto {
    .m-card {
      min-height: calc(100vh - 55px);
    }

    .m-tab-card {
      min-height: calc(100vh - 55px);

      .m-card {
        min-height: inherit;
      }
    }
  }
}
