import { ResponseStatus, Response } from '@hbfe/common'

import msPay, { TimeoutPayFlowResponse } from '@api/ms-gateway/ms-payment-v1'
import { TimeOut } from '@api/service/common/utils/TimeOut'
export class QueryPayingStatue {
  /*
   *  流水号
   * */
  flowNo = ''
  async queryPayStatue() {
    const res = await TimeOut.timeoutWithCount<Response<TimeoutPayFlowResponse>>(
      this['getRecord'],
      this['judgeFunc'],
      [this.flowNo],
      1500,
      Infinity
    )
    return res
  }
  private async judgeFunc(res: Response<TimeoutPayFlowResponse>) {
    return res.status.isSuccess() && [2, 3, 4].indexOf(res.data.payStatus) != 1
  }
  private async getRecord(flowNo: string) {
    return msPay.timeoutPayFlow(flowNo)
  }
}
