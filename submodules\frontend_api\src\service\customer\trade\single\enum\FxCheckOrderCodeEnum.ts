import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum FxCheckOrderCodeEnum {
  /**
   * 校验通过
   */
  success = 200,

  /**
   * 商品无效，网校过期
   */
  signRepeat = 40003,

  /**
   * 商品无效，网校过期
   */
  schoolExpired = 62000,

  /**
   * 商品无效，网校没有开启分销能力服务
   */
  distributionNotService = 62001,

  /**
   * 商品无效，分销商分销关系没有启用
   */
  distributionNotActivated = 62002,

  /**
   * 商品无效，分销关系不在合同有效期内
   */
  distributionContractOver = 62003,

  /**
   * 商品无效，分销授权地区为空
   */
  distributionRegionEmpty = 62004,

  /**
   * 商品无效，分销商品不在分销有效期内
   */
  productDistributionOver = 62006,

  /**
   * 商品无效，定价策略未设置为启用
   */
  pricingNotEnabled = 62007,

  /**
   * 商品无效，优惠无效
   */
  discountsInvalid = 62008,

  /**
   * 上属一级分销商在本网校的合作周期已到期
   */
  superiorDistributorExpired = 60005,

  /**
   * 上属一级分销商在本网校该商品分销有效期已到期
   */
  superiorDistributorProductExpired = 60006,

  /**
   * 未查询到上属一级分销商合同信息
   */
  superiorDistributorContractNotFound = 60014,

  /**
   * 未查询到一级分销商账号信息
   */
  superiorDistributorAccountNotFound = 60015,

  /**
   * 未查询到二级分销商账号信息
   */
  secondDistributorAccountNotFound = 60016,

  /**
   * 优惠策略时间未开始
   */
  discountNotStart = 60027,

  /**
   * 优惠策略时间已结束
   */
  discountOver = 60028,

  /**
   * 未查询到上级分销授权信息
   */
  superAuthorizationNotFound = 60029,

  /**
   * 当前优惠策略无剩余优惠名额
   */
  discountNoQuotaAvailable = 60033,

  /**
   * 上属一级分销商在本网校的合作状态已中止
   */
  superiorDistributorContractStop = 60037,

  /**
   * 上级分销商在本网校该商品分销授权状态已停止
   */
  superiorDistributorProductContractStop = 60040,

  /**
   * 定价策略未启用
   */
  pricingNotStart = 60044,

  /**
   * 定价策略销售地区为空
   */
  pricingRegionEmpty = 60045,

  /**
   * 定价策略不存在
   */
  pricingNotFound = 60046,

  /**
   * 您的地区/单位不在分销报名范围内
   */
  regionUnitOverScope = 60047,

  /**
   * 优惠策略未启用
   */
  discountUnable = 60048,

  /**
   * 优惠策略不存在
   */
  discountNotFound = 60049
}

class FxCheckOrderCode extends AbstractEnum<FxCheckOrderCodeEnum> {
  static enum = FxCheckOrderCodeEnum
  constructor(status?: FxCheckOrderCodeEnum) {
    super()
    this.current = status
    this.map.set(FxCheckOrderCodeEnum.success, '成功')
    this.map.set(FxCheckOrderCodeEnum.schoolExpired, '商品无效，网校过期')
    this.map.set(FxCheckOrderCodeEnum.signRepeat, '商品已报名，请前往学习')
    this.map.set(FxCheckOrderCodeEnum.distributionNotService, '商品无效，网校没有开启分销能力服务')
    this.map.set(FxCheckOrderCodeEnum.distributionNotActivated, '商品无效，分销商分销关系没有启用')
    this.map.set(FxCheckOrderCodeEnum.distributionContractOver, '商品无效，分销关系不在合同有效期内')
    this.map.set(FxCheckOrderCodeEnum.distributionRegionEmpty, '商品无效，分销授权地区为空')
    this.map.set(FxCheckOrderCodeEnum.productDistributionOver, '商品无效，分销商品不在分销有效期内')
    this.map.set(FxCheckOrderCodeEnum.pricingNotEnabled, '商品无效，定价策略未设置为启用')
    this.map.set(FxCheckOrderCodeEnum.discountsInvalid, '商品无效，优惠无效')
    this.map.set(FxCheckOrderCodeEnum.superiorDistributorExpired, '上属一级分销商在本网校的合作周期已到期')
    this.map.set(FxCheckOrderCodeEnum.superiorDistributorProductExpired, '上属一级分销商在本网校该商品分销有效期已到期')
    this.map.set(FxCheckOrderCodeEnum.superiorDistributorContractNotFound, '未查询到上属一级分销商合同信息')
    this.map.set(FxCheckOrderCodeEnum.superiorDistributorAccountNotFound, '未查询到一级分销商账号信息')
    this.map.set(FxCheckOrderCodeEnum.secondDistributorAccountNotFound, '未查询到二级分销商账号信息')
    this.map.set(FxCheckOrderCodeEnum.discountNotStart, '优惠策略时间未开始')
    this.map.set(FxCheckOrderCodeEnum.discountOver, '优惠策略时间已结束')
    this.map.set(FxCheckOrderCodeEnum.superAuthorizationNotFound, '未查询到上级分销授权信息')
    this.map.set(FxCheckOrderCodeEnum.discountNoQuotaAvailable, '当前优惠策略无剩余优惠名额')
    this.map.set(FxCheckOrderCodeEnum.superiorDistributorContractStop, '上属一级分销商在本网校的合作状态已中止')
    this.map.set(
      FxCheckOrderCodeEnum.superiorDistributorProductContractStop,
      '上级分销商在本网校该商品分销授权状态已停止'
    )
    this.map.set(FxCheckOrderCodeEnum.pricingNotStart, '定价策略未启用')
    this.map.set(FxCheckOrderCodeEnum.pricingRegionEmpty, '定价策略销售地区为空')
    this.map.set(FxCheckOrderCodeEnum.pricingNotFound, '定价策略不存在')
    this.map.set(FxCheckOrderCodeEnum.regionUnitOverScope, '您的地区/单位不在分销报名范围内')
    this.map.set(FxCheckOrderCodeEnum.discountUnable, '优惠策略未启用')
    this.map.set(FxCheckOrderCodeEnum.discountNotFound, '优惠策略不存在')
  }
}

export default new FxCheckOrderCode()
