<template>
  <div>
    <h1>测试用户信息页面</h1>
    <p>用户ID: {{ $route.params.id || '123' }}</p>
    
    <!-- 引用我们的用户信息组件 -->
    <UserInfoPage />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import UserInfoPage from '@packages/user/src/views/user-info/index.vue'

@Component({
  components: {
    UserInfoPage
  }
})
export default class TestUserInfo extends Vue {
  // 简单的测试页面
}
</script>

<route-meta>
{
  "title": "测试用户信息",
  "isMenu": false
}
</route-meta>
