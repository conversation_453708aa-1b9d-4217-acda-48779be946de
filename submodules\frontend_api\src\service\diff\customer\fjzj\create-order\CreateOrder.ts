import PlatformJxjypxtyptHljysxhSchool from '@api/diff-gateway/platform-jxjypxtypt-hljysxh-school'
import CreateOrderParams from '@api/service/diff/customer/fjzj/create-order/CreateOrderParams'
import CreateOrderCommon from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/CreateOrder'
import CreatedGateway from '@api/diff-gateway/platform-jxjypxtypt-fjzj-school'

export default class CreateOrder extends CreateOrderCommon {
  //  创建订单入参
  createOrderParams: CreateOrderParams = new CreateOrderParams()
  // constructor() {}
  /**
   * @description: 创建订单 待差异化
   * @return {*}
   */
  /**
   * 下单渠道token (目前只有分销使用)
   */
  channelToken: string = undefined

  /**
   * 创建订单
   */
  async doCreateOrder() {
    if (this.createOrderParams.key) {
      const res = await CreatedGateway.createHYWOrder(this.createOrderParams.toGeneralCreateOrderRequestDiff())
      if (res.status.isSuccess() && !res.data.success) {
        res.status.message = res.data.message
      }
      return res
    } else {
      return await super.doCreateOrder()
    }
  }
}
