import DateScope from '@api/service/common/models/DateScope'

export default class DateGroupItem {
  /**
   * 后端完整日期 yyyy-MM-dd
   */
  fullDate: string = undefined

  /**
   * ui展示日期 xx月xx日
   */
  showDate: string = undefined

  /**
   * 转换成scope范围时间 给查询使用
   */
  toDateScope() {
    const scope = new DateScope()
    // 后端接收-格式的日期时间
    const serviceTime = this.fullDate.replaceAll('/', '-')
    scope.begin = `${serviceTime} 00:00:00`
    scope.end = `${serviceTime} 23:59:59`

    return scope
  }
}
