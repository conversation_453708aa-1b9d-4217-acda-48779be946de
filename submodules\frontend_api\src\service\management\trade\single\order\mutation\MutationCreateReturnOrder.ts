import msOrder, {
  SellerApplyAfterSaleRequest,
  SellerApplyOrderReturnRequest,
  SubOrderAfterInfo
} from '@api/ms-gateway/ms-order-v1'
import { Response } from '@hbfe/common'
import { CalReturnOrderUtil } from '@api/service/management/trade/single/order/utils/CalReturnOrderUtil'
import { OrderRefundTypeEnum, IncludeRefundList } from '@api/service/common/return-order/enums/OrderRefundType'
import { RefundDtoAmountTypeEnum } from '@api/service/management/trade/single/order/enum/RefundDtoAmountTypeEnum'
export default class MutationCreateReturnOrder {
  orderNo = ''
  subOrderNo = ''
  /**
   * 退货/款物品 是否选中（UI交互使用）
   */
  refundedGoods = false

  /**
   * 退货原因id
   */
  reasonId?: string
  /**
   * 退货原因描述
   */
  description?: string

  /**
   * 退款类型
   */
  refundType: OrderRefundTypeEnum = undefined

  /**
   * 退款金额
   */
  refundAmount: number = undefined

  /**
   * 数量（暂时没用上，目前先固定为 1，后续多商品再调整）
   */
  quantity = 1

  /*
   *  发起退货
   * */
  async agreeReturnApply(returnOrderType?: OrderRefundTypeEnum) {
    const refundType = returnOrderType || this.refundType
    const request = new SellerApplyAfterSaleRequest()
    request.orderNo = this.orderNo
    request.reasonId = this.reasonId
    request.description = this.description
    request.returnOrderType = refundType
    const subOrderAfterInfo = new SubOrderAfterInfo()
    subOrderAfterInfo.subOrderNo = this.subOrderNo
    // 后端cdb说 amount 一定要有值，全额退款要给个0，仅退货也要给个0
    switch (refundType) {
      case OrderRefundTypeEnum.refundOnly:
        // subOrderAfterInfo.quantity = 0
        // subOrderAfterInfo.amount = 0
        subOrderAfterInfo.quantity = 0
        subOrderAfterInfo.amount = this.refundAmount
        subOrderAfterInfo.amountSource = RefundDtoAmountTypeEnum.allRemaining
        break
      case OrderRefundTypeEnum.returnOnly:
        subOrderAfterInfo.amount = 0
        subOrderAfterInfo.quantity = this.quantity
        break
      case OrderRefundTypeEnum.returnAndRefund:
        subOrderAfterInfo.quantity = 1
        subOrderAfterInfo.amount = this.refundAmount
        subOrderAfterInfo.amountSource = RefundDtoAmountTypeEnum.allRemaining
        break
      case OrderRefundTypeEnum.partialReturnNoRefund:
        subOrderAfterInfo.quantity = this.quantity
        subOrderAfterInfo.amount = 0
        break
      case OrderRefundTypeEnum.partialRefundNoReturn:
        subOrderAfterInfo.quantity = 0
        subOrderAfterInfo.amountSource = RefundDtoAmountTypeEnum.userEnter
        subOrderAfterInfo.amount = this.refundAmount
        break
      case OrderRefundTypeEnum.partialReturnPartialRefund:
        subOrderAfterInfo.quantity = this.quantity
        subOrderAfterInfo.amountSource = RefundDtoAmountTypeEnum.userEnter
        subOrderAfterInfo.amount = this.refundAmount
        break
      case OrderRefundTypeEnum.partialReturnFullRefund:
        subOrderAfterInfo.quantity = this.quantity
        subOrderAfterInfo.amount = 0
        subOrderAfterInfo.amountSource = RefundDtoAmountTypeEnum.allRemaining
        break
      case OrderRefundTypeEnum.fullReturnPartialRefund:
        subOrderAfterInfo.quantity = this.quantity
        subOrderAfterInfo.amountSource = RefundDtoAmountTypeEnum.userEnter
        subOrderAfterInfo.amount = this.refundAmount
        break
    }
    request.returnList = [subOrderAfterInfo]
    const res = await msOrder.sellerApplyAfterSale(request)
    return this.filterRes(res).status
  }

  filterRes(res: Response<any>) {
    return CalReturnOrderUtil.filterRes(res)
  }
}
