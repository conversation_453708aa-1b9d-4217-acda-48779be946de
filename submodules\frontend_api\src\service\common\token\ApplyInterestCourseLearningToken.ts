import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import ApplyStudentLearningToken from '@api/service/common/token/ApplyStudentLearningToken'
import MsChooseCourseLearningSceneV1 from '@api/ms-gateway/ms-choosecourselearningscene-v1'
import { ResponseStatus } from '@hbfe/common'
import MsInterestcourselearningsceneV1 from '@api/ms-gateway/ms-interestcourselearningscene-v1'

/**
 * 学习课程token
 */
class ApplyInterestCourseLearningToken extends AbstractApplyToken {
  private readonly applyStudentLearningToken: string
  private readonly courseId: string
  private readonly outlineId: string

  constructor(courseId: string, applyStudentLearningToken: string, outlineId: string) {
    super()
    this.applyStudentLearningToken = applyStudentLearningToken
    this.courseId = courseId
    this.outlineId = outlineId
  }

  async apply(): Promise<ResponseStatus> {
    // 学习方案场景申请用户 token
    const tokenValue = await MsInterestcourselearningsceneV1.applyCourseLearning({
      studentLearningToken: this.applyStudentLearningToken,
      courseId: this.courseId,
      outlineId: this.outlineId
    })
    if (!tokenValue.data.token) {
      return Promise.reject(
        new ResponseStatus(parseInt(tokenValue.data.applyResult.code), tokenValue.data.applyResult.message)
      )
    }
    this.token = tokenValue.data.token
    return new ResponseStatus(tokenValue.status.code, '')
  }
}

export default ApplyInterestCourseLearningToken
