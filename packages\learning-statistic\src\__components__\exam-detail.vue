<template>
  <div>
    <!-- 查看详情抽屉 -->
    <el-drawer title="考试记录" :visible.sync="isShowDrawer" size="700px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-table stripe :data="tableData" max-height="500px" class="m-table f-mb30" v-loading="loading">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="试卷提交时间" min-width="300">
            <template slot-scope="scope">
              <div>
                <el-tooltip
                  class="item"
                  effect="dark"
                  placement="top"
                  popper-class="m-tooltip"
                  v-if="scope.$index === 0 && dataItem.onlyHaveExamSimulate"
                  :content="
                    '同步第三方数据：' + dataItem.simulateExamTime.begin + '/' + dataItem.simulateExamScore + '分'
                  "
                >
                  <i class="el-icon-info f-co f-mr5"></i>
                </el-tooltip>
                {{ scope.row.answerPaperTimeInfo.handingTime }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="考试成绩" min-width="180">
            <template slot-scope="scope">{{ scope.row.answerPaperMarkInfo.score }}</template>
          </el-table-column>
        </el-table>
      </div>

      <hb-pagination :page="page" v-bind="page"> </hb-pagination>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'
  import { UiPage } from '@hbfe/common'
  import { ExaminationAnswerPaperResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
  import QueryExamRecordList from '@api/service/management/statisticalReport/query/QueryExamRecordList'
  import { StudentLearningStaticsVo } from '@api/service/management/statisticalReport/query/vo/StudentLearningStaticsVo'

  @Component
  export default class extends Vue {
    /**
     * 是否展示
     */
    @Prop({
      required: true,
      type: Boolean,
      default: false
    })
    value: boolean
    @Prop({
      required: true,
      type: String
    })
    qualificationId: string
    @Watch('value', {
      immediate: true,
      deep: true
    })
    valueChange(val: boolean) {
      this.isShowDrawer = cloneDeep(val)
    }
    @Emit('isExamDialog')
    @Watch('isShowDrawer', {
      immediate: true,
      deep: true
    })
    showRoleDialogChange(val: number) {
      return val
    }
    isShowDrawer = false
    loading = false
    page: UiPage
    tableData: Array<ExaminationAnswerPaperResponse> = new Array<ExaminationAnswerPaperResponse>()
    dataItem = new StudentLearningStaticsVo()
    constructor() {
      super()
      this.page = new UiPage(this.getDetailList, this.getDetailList)
    }
    async created() {
      this.getDetailList()
    }
    async getDetailList() {
      this.loading = true
      const detailStatic = new QueryExamRecordList()
      this.tableData = await detailStatic.pagePracticeRecordInSubProject(this.page, this.qualificationId)
      this.loading = false
    }
  }
</script>

<style scoped>
  .drawer-bd {
    flex: none;
  }
</style>
