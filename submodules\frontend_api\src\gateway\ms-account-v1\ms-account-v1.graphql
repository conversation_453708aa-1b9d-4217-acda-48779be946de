"""独立部署的微服务,K8S服务名:ms-account-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""变更当前登录帐户的密码
		@param changeInfo 更新信息
	"""
	changePasswordByCurrent(changeInfo:CurrentAccountChangePasswordRequest):Void
	"""禁用当前帐户微信小程序通知"""
	disableNoticeForWebChatApplet:Void
	"""启用当前帐户微信小程序通知"""
	enableNoticeForWebChatApplet:Void
	"""冻结帐户
		@param accountId 【必填】帐户ID
	"""
	freezeAccount(accountId:String):Void
	"""立即重置密码
		@param resetInfo 重置密码信息
	"""
	immediateResetPassword(resetInfo:ImmediateResetPasswordRequest):Void
	"""重新设置账户的帐号认证方式的帐号"""
	resetAccountPwdAuthIdentity(resetInfo:AccountPwdAuthIdentityResetInfo):Void
	"""恢复冻结的帐户
		@param accountId 【必填】帐户ID
	"""
	resumeAccount(accountId:String):Void
	"""更新用户信息
		@param updateInfo 【必填】要更新的用户信息
	"""
	updateUser(updateInfo:UserUpdateRequest):Void
	"""更新当前登录用户信息
		@param updateInfo 【必填】要更新的用户信息
	"""
	updateUserByCurrent(updateInfo:CurrentUserUpdateRequest):Void
}
"""重新设置账户的帐号密码认证方式认证标识信息
	<AUTHOR>
"""
input AccountPwdAuthIdentityResetInfo @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.AccountPwdAuthIdentityResetInfo") {
	"""【必填】账户ID"""
	accountId:String
	"""【必填】认证标识类型 1用户名，2手机，3身份证，4电子邮箱
		@see com.fjhb.domain.basicdata.api.account.consts.AuthenticationIdentityTypes
	"""
	identityType:Int!
	"""【必填】认证标识：帐号"""
	identity:String
}
"""变更当前登录帐户的密码信息
	<AUTHOR>
"""
input CurrentAccountChangePasswordRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CurrentAccountChangePasswordRequest") {
	"""【必填】原始密码"""
	originalPassword:String
	"""【必填】新密码"""
	newPassword:String
}
"""当前登录用户的用户修改信息
	<AUTHOR>
"""
input CurrentUserUpdateRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CurrentUserUpdateRequest") {
	"""身份证号"""
	idCard:String
	"""用户名称"""
	name:String
	"""用户昵称"""
	nickName:String
	"""手机号"""
	phone:String
	"""头像地址"""
	photo:String
	"""所属区域"""
	area:String
	"""联系地址"""
	address:String
	"""性别
		@see com.fjhb.domain.basicdata.api.user.consts.Genders
	"""
	gender:Int
	"""工作单位"""
	companyName:String
	"""所属人群"""
	peoples:String
	"""电子邮箱"""
	email:String
}
"""立即重置密码
	<AUTHOR>
"""
input ImmediateResetPasswordRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.ImmediateResetPasswordRequest") {
	"""【必填】帐户ID"""
	accountId:String
	"""【必填】重置后的密码"""
	password:String
}
"""用户更新信息，不设置或者设置null表示字段不更新
	<AUTHOR>
"""
input UserUpdateRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.UserUpdateRequest") {
	"""【必填】用户ID"""
	id:String
	"""身份证号"""
	idCard:String
	"""用户名称"""
	name:String
	"""用户昵称"""
	nickName:String
	"""手机号"""
	phone:String
	"""头像地址"""
	photo:String
	"""所属区域"""
	area:String
	"""联系地址"""
	address:String
	"""性别
		@see com.fjhb.domain.basicdata.api.user.consts.Genders
	"""
	gender:Int
	"""工作单位"""
	companyName:String
	"""所属人群"""
	peoples:String
	"""电子邮箱"""
	email:String
}

scalar List
