<route-params content="/:id"></route-params>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/courseware')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/courseware' }">课件管理</el-breadcrumb-item>
      <el-breadcrumb-item>课件详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" label-width="120px" class="m-text-form">
                <el-col :span="12">
                  <el-form-item label="课件分类：">{{ detail.categoryName }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="课件名称：">{{ detail.name }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="课件提供商：">{{ detail.providerName }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="课件类型：">{{ detail.type }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="转换状态：">{{ detail.status }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="课件状态：">{{ detail.enable ? '启用' : '停用' }}</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="课件教师：">
                    <template v-if="detail.teachers && detail.teachers.length">
                      {{ detail.teachers[0].name || '无' }}
                    </template>
                    <template v-else>
                      无
                    </template>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="教师简介：">
                    <template v-if="detail.teachers && detail.teachers.length">
                      {{ detail.teachers[0].description || '暂无简介' }}
                    </template>
                    <template v-else>暂无简介</template>
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="课件简介：">
                    {{ detail.description || '暂无简介' }}
                  </el-form-item>
                </el-col>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">关联媒体</span>
        </div>
        <media-detail
          v-model="coursewareOutline"
          :time.sync="documentTime"
          :allDetail="detail"
          :name="detail.name"
          :fileType="fileMapType[detail.type]"
          :detailType="detail.type"
          :mediaType="mediaType"
          ref="media"
        ></media-detail>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">使用该课件的课程清单</span>
        </div>
        <div class="f-p20">
          <el-table stripe :data="useCourseList" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="课程名称" min-width="300" fixed="left" prop="name"> </el-table-column>
            <el-table-column label="课程分类" min-width="180" prop="type">
              <template slot-scope="scope">
                <span v-if="scope.row.categories.length">
                  {{ scope.row.categories[0].name }}
                </span>
                <span v-else></span>
              </template>
            </el-table-column>
            <el-table-column label="学时" min-width="180" align="center" prop="period"> </el-table-column>
            <el-table-column label="创建时间" width="180" prop="createTime" align="center"> </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import MediaDetail from '@hbfe/jxjy-admin-courseware/src/components/media-detail.vue'
  import QueryCourseware from '@api/service/management/resource/courseware/query/QueryCourseware'
  import CoursewareDetail from '@api/service/management/resource/courseware/query/vo/CoursewareDetail'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'

  @Component({
    components: { MediaDetail },
    filters: {
      exchangeStatus(val: number) {
        switch (val) {
          case 1:
            return '转换中'
          case 2:
            return '转换成功'
          case 3:
            return '转换失败'
          default:
            return '转换失败'
        }
      },
      coursewareType(val: number) {
        switch (val) {
          case 1:
            return '文档'
          case 2:
            return '单视频'
          case 3:
            return '视频包'
          default:
            return '无'
        }
      }
    }
  })
  export default class extends Vue {
    @Ref('media') media: any
    detail: CoursewareDetail = new CoursewareDetail()
    form = {}

    //课程清单数据
    coursewareOutline: Array<any> = new Array<any>()
    documentTime = 0
    // 判断视频类型 1：华为云 2：外链
    mediaType = 1

    useCourseList: Array<CourseListDetail> = new Array<CourseListDetail>()

    fileMapType = {
      ['文档']: 1,
      ['单视频']: 2,
      ['视频包']: 3
    }

    get id(): string {
      return this.$route.params.id
    }

    queryCourseware: QueryCourseware = new QueryCourseware()

    async created() {
      //获取课件详情
      this.detail = await this.queryCourseware.queryCoursewareById(this.$route.params.id)
      ResourceModule.courseFactory.queryCourse.queryCourseListByCoursewareId(this.$route.params.id).then(res => {
        this.useCourseList = res
      })
      this.detail.isOuter ? (this.mediaType = 2) : (this.mediaType = 1)
    }
  }
</script>
