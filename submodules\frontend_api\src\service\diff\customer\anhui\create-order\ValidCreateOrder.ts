import PlatformJxjypxtyptAhzjSchool from '@api/platform-gateway/platform-jxjypxtypt-ahzj-school'

export default class ValidCreateOrder {
  /**
   * 校验报名商品是否有序列号
   */
  async validHasSerialNumberCreateOrder(commoditySkuId: string, idCard: string) {
    const { data } = await PlatformJxjypxtyptAhzjSchool.validAllowToCreateOrder({
      skuId: commoditySkuId,
      idCard: idCard
    })
    return data
  }
}
