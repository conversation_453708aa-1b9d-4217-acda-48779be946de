"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""分销管理员导出学习明细导出（超级管理员导出学习明细新口）
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInDistributor(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""超级管理员导出学习明细（超级管理员导出学习明细新口）
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInServicer(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""地区管理员导出学习明细（超级管理员导出学习明细新口）
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInServicerManageRegion(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""专题管理员导出学习明细导出（超级管理员导出学习明细新口）
		@param request
		@return
	"""
	exportStudentSchemeLearningExcelInTrainingChannelV2(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""集体查询学习明细接口
		@param page
		@param request
		@param dataFetchingEnvironment
		@return
	"""
	pageStudentSchemeLearningInCollectiveV2(page:Page,request:StudentSchemeLearningRequest):ZZKDStudentSchemeLearningResponsePage @page(for:"ZZKDStudentSchemeLearningResponse")
	"""分销商管理员查询学习明细接口(超级管理员查询学习明细新口)
		@param page
		@param request
		@param dataFetchingEnvironment
		@return
	"""
	pageStudentSchemeLearningInDistributor(page:Page,request:StudentSchemeLearningRequest):ZZKDStudentSchemeLearningResponsePage @page(for:"ZZKDStudentSchemeLearningResponse")
	"""地区管理员管理员查询学习明细接口(超级管理员查询学习明细新口)
		@param page
		@param request
		@param dataFetchingEnvironment
		@return
	"""
	pageStudentSchemeLearningInServicerManageRegion(page:Page,request:StudentSchemeLearningRequest):ZZKDStudentSchemeLearningResponsePage @page(for:"ZZKDStudentSchemeLearningResponse")
	"""超级管理员查询学习明细接口(超级管理员查询学习明细新口)
		@param page
		@param request
		@param dataFetchingEnvironment
		@return
	"""
	pageStudentSchemeLearningInServicerV2(page:Page,request:StudentSchemeLearningRequest):ZZKDStudentSchemeLearningResponsePage @page(for:"ZZKDStudentSchemeLearningResponse")
	"""专题管理员管理员查询学习明细接口(超级管理员查询学习明细新口)
		@param page
		@param request
		@param dataFetchingEnvironment
		@return
	"""
	pageStudentSchemeLearningInTrainingChannelV2(page:Page,request:StudentSchemeLearningRequest):ZZKDStudentSchemeLearningResponsePage @page(for:"ZZKDStudentSchemeLearningResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input StudentSchemeLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningRequest") {
	studentNoList:[String]
	student:UserRequest
	learningRegister:LearningRegisterRequest
	scheme:SchemeRequest
	studentLearning:StudentLearningRequest
	dataAnalysis:DataAnalysisRequest
	connectManageSystem:ConnectManageSystemRequest
	extendedInfo:ExtendedInfoRequest
	openPrintTemplate:Boolean
	saleChannels:[Int]
	trainingChannelName:String
	trainingChannelId:String
	notDistributionPortal:Boolean
	trainingType:String
	issueId:String
}
input ConnectManageSystemRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ConnectManageSystemRequest") {
	syncStatus:Int
}
input DataAnalysisRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.DataAnalysisRequest") {
	trainingResultPeriod:DoubleScopeRequest
	requirePeriod:DoubleScopeRequest
	acquiredPeriod:DoubleScopeRequest
}
input ExtendedInfoRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ExtendedInfoRequest") {
	whetherToPrint:Boolean
	applyCompanyCode:String
	policyTrainingSchemeId:String
	policyTrainingSchemeName:String
}
input LearningRegisterRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.LearningRegisterRequest") {
	registerType:Int
	sourceType:String
	sourceId:String
	status:[Int]
	registerTime:DateScopeRequest
	saleChannels:[Int]
	orderNoList:[String]
	subOrderNoList:[String]
	batchOrderNoList:[String]
	distributorId:String
	portalId:String
}
input RegionRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.RegionRequest") {
	province:String
	city:String
	county:String
}
input StudentLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.learning.StudentLearningRequest") {
	trainingResultList:[Int]
	trainingResultTime:DateScopeRequest
	notLearningTypeList:[Int]
	courseScheduleStatus:Int
	examAssessResultList:[Int]
}
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertySearchRequest") {
	region:[RegionSkuPropertyRequest]
	regionSearchType:Int
}
input SchemeRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeRequest") {
	schemeId:String
	schemeIdList:[String]
	schemeType:String
	schemeName:String
	skuProperty:SchemeSkuPropertyRequest
}
input SchemeSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeSkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	positionCategory:[String]
	trainingObject:[String]
	jobLevel:[String]
	jobCategory:[String]
	subject:[String]
	grade:[String]
	learningPhase:[String]
	discipline:[String]
	qualificationCategory:[String]
	trainingWay:[String]
	trainingInstitution:[String]
	mainAdditionalItem:[String]
}
input UserPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserPropertyRequest") {
	regionList:[RegionRequest]
	companyName:String
	payOrderRegionList:[RegionRequest]
}
input UserRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserRequest") {
	userIdList:[String]
	accountIdList:[String]
	userProperty:UserPropertyRequest
}
type ConnectManageSystemResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.ConnectManageSystemResponse") {
	syncStatus:Int
	syncMessage:String
}
type BatchOwnerResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.BatchOwnerResponse") {
	unitId:String
	userId:String
}
type ExtendedInfoResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.ExtendedInfoResponse") {
	whetherToPrint:Boolean
	printTime:DateTime
	pdfUrl:String
	certificateId:String
	certificateNo:String
}
type LearningRegisterResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.LearningRegisterResponse") {
	registerType:Int
	sourceType:String
	sourceId:String
	status:Int
	statusChangeTime:DateTime
	registerTime:DateTime
	saleChannel:Int
	orderNo:String
	subOrderNo:String
	batchOrderNo:String
	frozenAndInvalidSourceType:String
	frozenAndInvalidSourceId:String
}
type OwnerResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.OwnerResponse") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerType:Int
	servicerId:String
	batchOwner:BatchOwnerResponse
}
type CourseLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.CourseLearningResponse") {
	courseScheduleStatus:Int
	courseQualifiedTime:DateTime
	selectedCourseCount:Int
	selectedCoursePeriod:Double
	learningId:String
	learningType:Int
	enabled:Boolean
	learningResourceType:Int
	learningResourceId:String
	userAssessResult:[String]
	learningResult:[LearningResultResponse]
}
type DataAnalysisResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.DataAnalysisResponse") {
	trainingResultPeriod:Double
	requirePeriod:Double
	acquiredPeriod:Double
}
type ExamLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.ExamLearningResponse") {
	committedExam:Boolean
	examAssessResult:Int
	examQualifiedTime:DateTime
	examCount:Int
	maxExamScore:Double
	learningId:String
	learningType:Int
	enabled:Boolean
	learningResourceType:Int
	learningResourceId:String
	userAssessResult:[String]
	learningResult:[LearningResultResponse]
}
type LearningExperienceLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.LearningExperienceLearningResponse") {
	committedLearningExperience:Boolean
	learningExperienceAssessResult:Int
	learningExperienceQualifiedTime:DateTime
	maxLearningExperienceScore:Double
	learningExperiencePassCount:Long
	learningId:String
	learningType:Int
	enabled:Boolean
	learningResourceType:Int
	learningResourceId:String
	userAssessResult:[String]
	learningResult:[LearningResultResponse]
}
type StudentLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.StudentLearningResponse") {
	trainingResult:Int
	trainingResultTime:DateTime
	courseLearning:CourseLearningResponse
	examLearning:ExamLearningResponse
	learningExperienceLearning:LearningExperienceLearningResponse
	userAssessResult:[String]
	learningResult:[LearningResultResponse]
}
type CertificateLearningConfigResultResponse implements LearningResultConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.CertificateLearningConfigResultResponse") {
	certificateTemplateId:String
	openPrintTemplate:Boolean!
	resultType:Int
}
type GradeLearningConfigResultResponse implements LearningResultConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.GradeLearningConfigResultResponse") {
	gradeType:String
	grade:Double
	resultType:Int
}
interface LearningResultConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.LearningResultConfigResponse") {
	resultType:Int
}
type LearningResultResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.LearningResultResponse") {
	learningResultId:String
	gainedTime:DateTime
	learningResultConfig:LearningResultConfigResponse
}
type SchemeResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeResponse") {
	schemeId:String
	schemeType:String
	skuProperty:SchemeSkuPropertyResponse
	schemeName:String
	learningResult:[LearningResultConfigResponse]
}
type SchemeSkuPropertyResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeSkuPropertyResponse") {
	year:SchemeSkuPropertyValueResponse
	province:SchemeSkuPropertyValueResponse
	city:SchemeSkuPropertyValueResponse
	county:SchemeSkuPropertyValueResponse
	industry:SchemeSkuPropertyValueResponse
	subjectType:SchemeSkuPropertyValueResponse
	trainingCategory:SchemeSkuPropertyValueResponse
	trainingProfessional:SchemeSkuPropertyValueResponse
	technicalGrade:SchemeSkuPropertyValueResponse
	positionCategory:SchemeSkuPropertyValueResponse
	trainingObject:SchemeSkuPropertyValueResponse
	jobLevel:SchemeSkuPropertyValueResponse
	jobCategory:SchemeSkuPropertyValueResponse
	subject:SchemeSkuPropertyValueResponse
	grade:SchemeSkuPropertyValueResponse
	learningPhase:SchemeSkuPropertyValueResponse
	discipline:SchemeSkuPropertyValueResponse
	certificatesType:SchemeSkuPropertyValueResponse
	practitionerCategory:SchemeSkuPropertyValueResponse
	trainingInstitution:SchemeSkuPropertyValueResponse
	mainAdditionalItem:SchemeSkuPropertyValueResponse
	trainingWay:SchemeSkuPropertyValueResponse
}
type SchemeSkuPropertyValueResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeSkuPropertyValueResponse") {
	skuPropertyValueId:String
}
type RegionResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.user.RegionResponse") {
	province:String
	city:String
	county:String
}
type UserPropertyResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.user.UserPropertyResponse") {
	region:RegionResponse
	payOrderRegion:RegionResponse
}
type UserResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.user.UserResponse") {
	userId:String
	accountId:String
	userProperty:UserPropertyResponse
}
type ZZKDStudentSchemeLearningResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.zzkd.v1.kernel.geteway.response.ZZKDStudentSchemeLearningResponse") {
	"""证书编号"""
	certificateNumber:String
	qualificationId:String
	studentNo:String
	owner:OwnerResponse
	student:UserResponse
	learningRegister:LearningRegisterResponse
	scheme:SchemeResponse
	studentLearning:StudentLearningResponse
	dataAnalysis:DataAnalysisResponse
	connectManageSystem:ConnectManageSystemResponse
	extendedInfo:ExtendedInfoResponse
	schemeQuestionnaireRequirementCount:Int
	schemeQuestionnaireNoAssessSubmittedCount:Int
	schemeQuestionnaireSubmittedCount:Int
	issueName:String
}

scalar List
type ZZKDStudentSchemeLearningResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ZZKDStudentSchemeLearningResponse]}
