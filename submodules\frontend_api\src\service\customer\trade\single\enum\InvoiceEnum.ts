export enum InvoiceIdentificationEnum {
  /**
   * 普通发票
   */
  PLAIN_INVOICE = 1,
  /**
   * 增值税普通发票
   */
  VAT_PLAIN_INVOICE = 2,
  /**
   * 增值税专用发票（纸质）
   */
  VAT_SPECIAL_PLAIN_INVOICE = 3,
  /**
   * 增值税专用发票（电子）
   */
  VAT_SPECIAL_ELECT_PLAIN_INVOICE = 4
}

/**
 * 发票种类 PLAININVOICE:普通发票 VATPLAININVOICE:增值税普通发票 VATSPECIALPLAININVOICE:增值税专用发票
 */
export enum InvoiceCategoryEnum {
  PLAININVOICE = 1,
  VATPLAININVOICE = 2,
  VATSPECIALPLAININVOICE = 3
}

/**
 * 发票类型 ONLINE:线上 OFFLINE:线下
 */
export enum InvoiceTypeEnum {
  ONLINE = 1,
  OFFLINE = 2
}

/**
 * 开票方式
 */
export enum InvoiceMethodEnum {
  /**
   * 电子
   */
  ELECT = 1,
  /**
   * 纸质
   */
  PAPER = 1
}
