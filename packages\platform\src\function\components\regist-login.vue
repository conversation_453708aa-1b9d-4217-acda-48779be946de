<!--
 * @Author: z张仁榕
 * @Date: 2025-01-21 17:09:50
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-03-21 15:41:51
 * @Description: 
-->
<template>
  <div class="f-p15">
    <el-tabs v-model="activeName2" type="card" class="m-tab-card">
      <el-tab-pane label="注册设置" name="regist">
        <template v-if="$hasPermission('registConfig')" desc="注册设置" actions="@RegistConfig">
          <regist-config></regist-config>
        </template>
      </el-tab-pane>
      <el-tab-pane label="登录设置" name="login">
        <template v-if="$hasPermission('loginConfig')" desc="登录设置" actions="@LoginConfig">
          <login-config></login-config>
        </template>
      </el-tab-pane>
      <el-tab-pane label="验证设置" name="apply">
        <template v-if="$hasPermission('applyConfig')" desc="验证设置" actions="@ApplyConfig">
          <apply-config></apply-config>
        </template>
      </el-tab-pane>
      <el-tab-pane label="协议设置" name="protocol">
        <template v-if="$hasPermission('protocolConfig')" desc="协议设置" actions="@ProtocolConfig"
          ><protocol-config></protocol-config
        ></template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
  import ApplyConfig from '@hbfe/jxjy-admin-platform/src/function/components/apply-config.vue'
  import LoginConfig from '@hbfe/jxjy-admin-platform/src/function/components/login-config.vue'
  import ProtocolConfig from '@hbfe/jxjy-admin-platform/src/function/components/protocol-config.vue'
  import RegistConfig from '@hbfe/jxjy-admin-platform/src/function/components/regist-config.vue'
  import { Component, Vue } from 'vue-property-decorator'

  @Component({
    components: { LoginConfig, RegistConfig, ApplyConfig, ProtocolConfig }
  })
  export default class extends Vue {
    activeName2 = 'regist'
    form = {
      delivery1: '',
      resource: '',
      name: ''
    }
  }
</script>
