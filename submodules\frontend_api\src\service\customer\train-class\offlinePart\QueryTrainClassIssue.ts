import IssueDetail from '@api/service/customer/train-class/offlinePart/model/IssueDetail'
import MsTradeQueryFrontGatewayTradeQueryForestage, {
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  IssueCommoditySkuRequest,
  IssueCommoditySkuResponse,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { Page } from '@hbfe/common'
import { cloneDeep } from 'lodash'
import DataResolve from '@api/service/common/utils/DataResolve'
import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage, {
  SchemeIssueConfigRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
import LearningType from '@api/service/common/scheme/model/LearningType'

/**
 * 方案-期别查询参数
 */
export class SchemeIssueParam {
  /**
   * 方案Id
   */
  schemeId = ''
  /**
   * 期别Id集合
   */
  issueIds: string[] = []
}

/**
 * 批量查询方案期别信息查询参数
 */
export class BatchQuerySchemeIssueParam {
  /**
   * 方案-期别查询参数
   */
  items: SchemeIssueParam[] = []
}

/**
 * @description 查询培训方案期别
 */
class QueryTrainClassIssue {
  /**
   * 转换方案期别列表信息
   * @param respDataList 接口数据列表
   * @private
   */
  private async convertSchemeIssueList(respDataList: IssueCommoditySkuResponse[]): Promise<IssueDetail[]> {
    let result = [] as IssueDetail[]
    if (respDataList.length) {
      let schemeId = ''
      result = respDataList.map((dto) => {
        const vo = new IssueDetail()
        const { issueResource, originCommodityInfo } = dto
        if (originCommodityInfo) {
          const { possessionInfo } = originCommodityInfo
          if (possessionInfo) {
            // 用户是否拥有此商品
            vo.possessing = possessionInfo.possessing
          }
        }
        if (issueResource) {
          schemeId = dto.issueResource.schemeId
          vo.periods = issueResource.period
          vo.id = issueResource.issueId
          vo.issueName = issueResource.issueName
          vo.registeredCount = issueResource.registeredCount
          const { issueReportDateScope, issueTrainingDateScope, issueSignUpDateScope } = issueResource
          if (issueReportDateScope) {
            vo.checkDateRange.startDate = issueReportDateScope.begin
            vo.checkDateRange.endDate = issueReportDateScope.end
          }
          if (issueTrainingDateScope) {
            vo.trainingDateRange.startDate = issueTrainingDateScope.begin
            vo.trainingDateRange.endDate = issueTrainingDateScope.end
          }
          if (issueSignUpDateScope) {
            vo.registerBeginTime = issueSignUpDateScope.begin
            vo.registerEndTime = issueSignUpDateScope.end
          }
          vo.trainingPointId = issueResource.trainingPointId
        }
        return vo
      })
      // 获取期别学时
      const issueIds = DataResolve.unique(result.map((el) => el.id))
      if (issueIds.length && schemeId) {
        const request = new SchemeIssueConfigRequest()
        request.schemeIdList = [schemeId]
        const { status, data } =
          await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.pageSchemeIssueConfigListOptionalLoginInServicer(
            {
              request,
              page: new Page(1, 1)
            }
          )
        if (status && status.isSuccess() && data && data.currentPageData && data.currentPageData.length) {
          const schemeResp = data.currentPageData[0]
          const { issueConfig, schemeConfig } = schemeResp
          if (issueConfig && issueConfig.length) {
            result.forEach((issueVo) => {
              const issueDto = issueConfig.find((el) => el.issueId === issueVo.id)
              if (issueDto) {
                issueVo.periods = issueDto.periods
              }
            })
          }
          if (schemeConfig) {
            const schemeConfigStr = schemeConfig.schemeConfig
            if (schemeConfigStr) {
              const { issue } = LearningType.configIssueAndQuestionnaire(JSON.parse(schemeConfigStr) as Scheme)
              const issueList = issue.issueConfigList
              result = result.map((issueVo) => {
                const item = new IssueDetail()
                const issueConfig = issueList.find((el) => el.id === issueVo.id)
                if (issueConfig) {
                  Object.assign(item, issueConfig, {
                    periods: issueVo.periods,
                    registeredCount: issueVo.registeredCount,
                    possessing: issueVo.possessing
                  } as Partial<IssueDetail>)
                } else {
                  Object.assign(item, issueVo)
                }
                return item
              })
            }
          }
        }
      }
    }
    return result
  }

  /**
   * 【网校】查询方案可用期别列表
   * @param commoditySkuId 商品id
   */
  async querySchemeAvailableIssueList(commoditySkuId: string): Promise<IssueDetail[]> {
    let result = [] as IssueDetail[]
    const queryRequest = new IssueCommoditySkuRequest()
    const page = new Page(1, 200)
    queryRequest.commoditySkuIdList = [commoditySkuId]
    const respDataList = [] as IssueCommoditySkuResponse[]
    const sortRequest = [
      {
        sortField: CommoditySkuSortField.ISSUE_SIGN_UP_BEGIN_TIME,
        policy: SortPolicy.ASC
      } as CommoditySkuSortRequest
    ]
    const { data } = await MsTradeQueryFrontGatewayTradeQueryForestage.pageIssueCommoditySkuCustomerPurchaseInServicer({
      page,
      queryRequest,
      sortRequest
    })
    if (data.currentPageData && data.currentPageData.length) {
      respDataList.push(...data.currentPageData)
    }
    if (data && data.totalPageSize > 200) {
      const requestList = Array(Math.ceil(data.totalSize / 200)).fill('')
      const tmpRespList = await Promise.all(
        requestList.map(async (item, index) => {
          const tmpPage = new Page(index + 2, 200)
          const tmpReq = cloneDeep(queryRequest)
          const tmpSortRequest = cloneDeep(sortRequest)
          return MsTradeQueryFrontGatewayTradeQueryForestage.pageIssueCommoditySkuCustomerPurchaseInServicer({
            page: tmpPage,
            queryRequest: tmpReq,
            sortRequest: tmpSortRequest
          })
        })
      )
      tmpRespList.forEach((tmpResp) => {
        const { status, data } = tmpResp
        if (status && status.isSuccess() && data && data.currentPageData && data.currentPageData.length) {
          respDataList.push(...data.currentPageData)
        }
      })
    }
    if (respDataList.length) {
      result = await this.convertSchemeIssueList(respDataList)
    }
    return result
  }

  /**
   * 【专题】查询方案可用期别列表
   * @param commoditySkuId 商品id
   */
  async queryChannelSchemeAvailableIssueList(commoditySkuId: string): Promise<IssueDetail[]> {
    let result = [] as IssueDetail[]
    const queryRequest = new IssueCommoditySkuRequest()
    const page = new Page(1, 200)
    queryRequest.commoditySkuIdList = [commoditySkuId]
    const respDataList = [] as IssueCommoditySkuResponse[]
    const sortRequest = [
      {
        sortField: CommoditySkuSortField.ISSUE_TRAINING_BEGIN_TIME,
        policy: SortPolicy.ASC
      } as CommoditySkuSortRequest
    ]
    const { data } = await MsTradeQueryFrontGatewayTradeQueryForestage.pageIssueCommoditySkuTrainingChannelInServicer({
      page,
      queryRequest,
      sortRequest
    })
    if (data.currentPageData && data.currentPageData.length) {
      respDataList.push(...data.currentPageData)
    }
    if (data && data.totalPageSize > 200) {
      const requestList = Array(Math.ceil(data.totalSize / 200)).fill('')
      const tmpRespList = await Promise.all(
        requestList.map(async (item, index) => {
          const tmpPage = new Page(index + 2, 200)
          const tmpReq = cloneDeep(queryRequest)
          const tmpSortRequest = cloneDeep(sortRequest)
          return MsTradeQueryFrontGatewayTradeQueryForestage.pageIssueCommoditySkuTrainingChannelInServicer({
            page: tmpPage,
            queryRequest: tmpReq,
            sortRequest: tmpSortRequest
          })
        })
      )
      tmpRespList.forEach((tmpResp) => {
        const { status, data } = tmpResp
        if (status && status.isSuccess() && data && data.currentPageData && data.currentPageData.length) {
          respDataList.push(...data.currentPageData)
        }
      })
    }
    if (respDataList.length) {
      result = await this.convertSchemeIssueList(respDataList)
    }
    return result
  }

  /**
   * 批量查询方案期别信息Map
   * @param param 批量查询方案期别信息查询参数
   * @returns 方案期别信息Map
   */
  async batchQuerySchemeIssueMap(param: BatchQuerySchemeIssueParam): Promise<Map<string, IssueDetail[]>> {
    const result = new Map<string, IssueDetail[]>()
    const schemeIds = DataResolve.unique(param.items.map((el) => el.schemeId))
    if (!schemeIds.length) {
      return result
    }
    const request = new SchemeIssueConfigRequest()
    request.schemeIdList = schemeIds
    const { data } =
      await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.pageSchemeIssueConfigListOptionalLoginInServicer(
        {
          page: new Page(1, schemeIds.length),
          request
        }
      )
    const schemeConfigList: Scheme[] = []
    if (data && data.currentPageData && data.currentPageData.length) {
      data.currentPageData.forEach((el) => {
        const schemeConfig = el.schemeConfig
        if (schemeConfig) {
          schemeConfigList.push(JSON.parse(schemeConfig.schemeConfig) as Scheme)
        }
      })
    }
    if (schemeConfigList.length) {
      param.items.forEach((item) => {
        const { schemeId, issueIds } = item
        const targetJsonConfig = schemeConfigList.find((el) => el.id === schemeId)
        if (targetJsonConfig) {
          const { issue } = LearningType.configIssueAndQuestionnaire(targetJsonConfig)
          const issueList = issue.issueConfigList
          const targetIssueList = issueList.filter((el) => issueIds.includes(el.id))
          result.set(schemeId, targetIssueList as IssueDetail[])
        }
      })
    }
    return result
  }
}

export default QueryTrainClassIssue
