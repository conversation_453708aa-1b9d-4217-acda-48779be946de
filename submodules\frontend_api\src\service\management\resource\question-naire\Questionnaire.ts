import QuestionAnswer from '@api/service/management/resource/question-naire/models/QuestionAnswer'
import PlatformQuestionnaireQueryForeStage, {
  AskQuestion,
  ScaleQuestion,
  GetQuestionnaireDetailRequest,
  MultipleQuestion,
  RadioQuestion,
  SurveyQuestionnaireResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryForeStage'
import PlatfromExamQueryBackStage, {
  BaseQuestionResponse,
  ChooseAnswerOption
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { QuestionTypeEnum } from '@api/service/common/enums/question-naire/QuestionType'
import QuestionSingleOption from '@api/service/common/question-naire/QuestionSingleOption'
import MsQuestionnaireQueryBackStage from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'
import { Response, ResponseStatus } from '@hbfe/common'
import PlatSchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import { TeacherQuestionTypeEnum } from '@api/service/common/question-naire/enums/TeacherQuestionType'
/**
 * 问卷详情
 */
export default class Questionnaire {
  /**
   * 问卷id
   */
  questionnaireId = ''
  /**
   * 答卷id
   */
  answerPaperId = ''
  /**
   * 问卷名称
   */
  questionnaireName = ''

  /**
   * 问卷内试题
   */
  questions: Array<QuestionAnswer> = new Array<QuestionAnswer>()

  /**
   * 查看问卷
   * 获取已答完的作答详情
   */
  async queryAnsweredQuestions(issueId?: string) {
    const request = new GetQuestionnaireDetailRequest()
    request.id = this.answerPaperId
    const res = await MsQuestionnaireQueryBackStage.getQuestionnaireDetailInServicer(request)
    const paper = res.data
    this.questionnaireName = paper.name
    this.questions = (paper.questions || []).map((item) => {
      // 四种返回格式 故用any
      return QuestionAnswer.fromAnswer(item as RadioQuestion | MultipleQuestion | AskQuestion | ScaleQuestion)
    })
    const questionIdList = this.questions.map((item) => {
      return item.id
    })
    if (!questionIdList.length) {
      this.questions = []
      return
    }
    const newMap = new Map<string, BaseQuestionResponse>()
    const currentPageData = new Array<BaseQuestionResponse>()
    const fetchQuestionList = async (ids: string[]) => {
      const questRes = await PlatfromExamQueryBackStage.pageQuestionInServicer({
        page: { pageNo: 1, pageSize: ids.length },
        request: {
          queryScope: 2,
          questionIdList: ids
        }
      })
      if (questRes.status.isSuccess() && questRes.data.currentPageData?.length) {
        currentPageData.push(...questRes.data.currentPageData)
      }
    }
    for (let i = 0; i < questionIdList.length; i += 200) {
      const chunk = questionIdList.slice(i, i + 200)
      await fetchQuestionList(chunk)
    }
    if (currentPageData?.length) {
      // 试题信息
      currentPageData.map((item) => {
        newMap.set(item.questionId, item)
      })
      const newArray = this.questions.map((temp) => {
        const ite: any = newMap.get(temp.id)
        return QuestionAnswer.fromQuestionAnwer(temp, ite)
      })

      const map = new Map(newArray.map((item) => [item.id, item]))

      // 需要按照试卷获取的id排序
      const mergedArray = this.questions.map((item1) => {
        // const temp = Object.assign(item1, map.get(item1.questionId))
        const temp = map.get(item1.id)
        temp.id = item1.id
        temp.type = item1.type
        temp.isAnswered = item1.isAnswered
        temp.isMustAnswered = item1.isMustAnswered
        temp.answers = item1.answers
        temp.fillContentMap = item1.fillContentMap
        // 填充UI使用的绑定数据
        if (item1.type === QuestionTypeEnum.single) {
          temp.optionQuestion.options.map((it) => {
            it.isSelect = item1.answers.some((answer) => answer == it.id)
            it.completionContent = item1.fillContentMap.get(it.id)
          })
          temp.optionQuestion.selectedContent = item1.answers[0]
          // }
        } else if (item1.type === QuestionTypeEnum.multiple) {
          temp.multipleOptionQuestion.options.map((it) => {
            it.isSelect = item1.answers.some((answer) => answer == it.id)
            it.completionContent = item1.fillContentMap.get(it.id)
          })
          temp.multipleOptionQuestion.selectedContentArr = item1.answers
          // }
        } else if (item1.type === QuestionTypeEnum.answer) {
          temp.answerQuestion.selectedContent = item1.answers[0]
        } else if (item1.type === QuestionTypeEnum.gauge) {
          temp.gaugeQuestion.selectedContent = Number(item1.answers[0])
        }
        temp.onlineOrOffline = item1.onlineOrOffline
        return temp
      })
      this.questions = mergedArray as QuestionAnswer[]
    }
    return this
  }
}
