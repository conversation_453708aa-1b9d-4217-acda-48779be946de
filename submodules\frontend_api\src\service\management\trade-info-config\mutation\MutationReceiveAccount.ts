import { ResponseStatus } from '@hbfe/common'
import msTradeConfigurationV1 from '@api/ms-gateway/ms-trade-configuration-v1'
import ReceiveAccountVo from '../query/vo/ReceiveAccountVo'

class MutationReceiveAccount {
  /**
   * 收款账号id
   */
  receiveAccount: ReceiveAccountVo
  constructor(receiveAccount: ReceiveAccountVo) {
    this.receiveAccount = receiveAccount
  }

  async doEnable(): Promise<ResponseStatus> {
    const msRes = await msTradeConfigurationV1.enableReceiveAccount(this.receiveAccount.id)
    if (msRes.status.isSuccess()) {
      this.receiveAccount.status = 1
    }
    return msRes.status
  }

  async doDisable(): Promise<ResponseStatus> {
    const msRes = await msTradeConfigurationV1.disableReceiveAccount(this.receiveAccount.id)
    if (msRes.status.isSuccess()) {
      this.receiveAccount.status = 0
    }
    return msRes.status
  }

  async doDelete(): Promise<ResponseStatus> {
    if (this.receiveAccount.status != 0) {
      return new ResponseStatus(500, '账号未停用不能删除')
    }
    const msRes = await msTradeConfigurationV1.deleteReceiveAccount(this.receiveAccount.id)
    if (!msRes.status.isSuccess()) {
      msRes.status.errors[0].message = msRes.status.errors[0].message.split(':')[1].trim()
    }
    return msRes.status
  }
}
export default MutationReceiveAccount
