<route-meta>
{
"isMenu": true,
"openWhenInit": false,
"closeAble": false,
"title": "营销中心",
"sort": 1,
"icon": "icon_shuju"
}
</route-meta>
<script lang="ts">
  import MarketingCenter from '@hbfe/jxjy-admin-marketingCenter/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { NZFXS, NZGYS, NZGYSJCB } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    query: [NZFXS, NZGYS, NZGYSJCB]
  })
  export default class extends MarketingCenter {}
</script>
