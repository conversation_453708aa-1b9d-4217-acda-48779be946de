/*
 * @Description: 发票查询
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:32:55
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-11-08 10:30:43
 */
import { Page } from '@hbfe/common'
import QueryPageInvoiceParam from '@api/service/management/trade/single/invoice/query/dto/QueryPageInvoiceParam'
import {
  InvoiceAutoBillPolicyResponse,
  InvoiceAssociationInfoRequest,
  OnlineInvoiceRequest,
  OnlineInvoiceSortRequest,
  OnlineInvoiceSortField,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import TradeQueryBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import InvoiceListResponse from '@api/service/management/trade/single/invoice/query/vo/InvoiceListResponse'
import UserModule from '@api/service/management/user/UserModule'

import ExportGateWay from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import { InvoiceStatusEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
import QueryInvoiceBase from '@api/service/management/trade/single/invoice/query/vo/QueryInvoiceBase'

export default class QueryInvoice extends QueryInvoiceBase {
  /**
   * 开票总金额
   */
  totalAmount = 0
  /**
   * 发票总税额
   */
  totalTax = 0
  /**
   * 分页查询发票
   * @param page 页数
   * @param queryPageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<InvoiceListResponse>
   */
  async onLinePageInvoiceInServicer(
    page: Page,
    queryPageInvoiceParam?: QueryPageInvoiceParam,
    sort: Array<OnlineInvoiceSortRequest> = [
      { field: OnlineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy.DESC }
    ]
  ): Promise<Array<InvoiceListResponse>> {
    const request = QueryPageInvoiceParam.to(queryPageInvoiceParam)
    if (
      queryPageInvoiceParam.userName ||
      queryPageInvoiceParam.idCard ||
      queryPageInvoiceParam.phone ||
      queryPageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryPageInvoiceParam.userName
        ? queryPageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryPageInvoiceParam.idCard
        ? queryPageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryPageInvoiceParam.phone
        ? queryPageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryPageInvoiceParam.loginAccount
        ? queryPageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        if (idList.data?.length === 0) {
          page.totalSize = 0
          page.totalPageSize = 0
          return []
        }
        if (request.associationInfoList[0]) {
          request.associationInfoList[0].buyerIdList = idList.data
        } else {
          request.associationInfoList[0] = new InvoiceAssociationInfoRequest()
          request.associationInfoList[0].buyerIdList = idList.data
        }
      }
    }
    const params = {
      page,
      request,
      sort
    }

    const result = await TradeQueryBackstage.pageOnlineInvoiceInServicer(params)
    await this.onLineStatisticInvoiceInServicer(page, request, sort)
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize
    const data = new Array<InvoiceListResponse>()
    const userIds = new Array<string>()
    const blueInvoiceIdList: string[] = [],
      redInvoiceIdList: string[] = []
    result.data.currentPageData.forEach(item => {
      data.push(InvoiceListResponse.from(item))
      userIds.push(item.associationInfoList[0]?.buyer?.userId)
      if (item.basicData?.blueInvoiceItemBillStatus === InvoiceStatusEnum.OPENING)
        blueInvoiceIdList.push(item.invoiceId)
      if (item.basicData?.redInvoiceItemBillStatus === InvoiceStatusEnum.OPENING) redInvoiceIdList.push(item.invoiceId)
    })
    let blueMsgs: Map<string, string>, redMsgs: Map<string, string>
    if (blueInvoiceIdList.length) {
      blueMsgs = await this.queryBlueInvoiceException(blueInvoiceIdList)
    }
    if (redInvoiceIdList.length) {
      redMsgs = await this.queryRedInvoiceException(redInvoiceIdList)
    }
    if (blueMsgs?.size || redMsgs?.size) {
      data.forEach(item => {
        item.blueInvoiceExceptionMsg = blueMsgs?.get(item.invoiceId)
        item.redInvoiceExceptionMsg = redMsgs?.get(item.invoiceId)
      })
    }
    if (userIds?.length === 0) {
      return data
    }
    const userIdMap = await this.getUserInfo(userIds)
    data.forEach(item => {
      if (userIdMap.has(item.userId)) {
        const result = userIdMap.get(item.userId)
        item.setUserInfo(result.userName, result.idCard, result.phone, result.email, result.loginAccount)
      }
    })
    return data
  }
  /**
   * 线上发票导出
   * @param queryPageInvoiceParam 查询参数
   */
  async onLinePageInvoiceInExport(queryPageInvoiceParam?: QueryPageInvoiceParam): Promise<boolean> {
    const request = QueryPageInvoiceParam.to(queryPageInvoiceParam)
    if (
      queryPageInvoiceParam.userName ||
      queryPageInvoiceParam.idCard ||
      queryPageInvoiceParam.phone ||
      queryPageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryPageInvoiceParam.userName
        ? queryPageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryPageInvoiceParam.idCard
        ? queryPageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryPageInvoiceParam.phone
        ? queryPageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryPageInvoiceParam.loginAccount
        ? queryPageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        if (request.associationInfoList[0]) {
          request.associationInfoList[0].buyerIdList = idList.data
        } else {
          request.associationInfoList[0] = new InvoiceAssociationInfoRequest()
          request.associationInfoList[0].buyerIdList = idList.data
        }
      }
    }
    const result = await ExportGateWay.exportOnlineInvoiceInServicerForJxjy(request)
    return result.data
  }
  /**
   * 查询发票详情
   * @param invoiceId 发票ID
   */
  async onLineGetInvoiceInServicer(invoiceId: Array<string> | string) {
    if (typeof invoiceId === 'string') {
      const { data } = await TradeQueryBackstage.getOnlineInvoiceInServicer(invoiceId)
      return InvoiceListResponse.from(data)
    } else {
      const promiseArr = []
      const data: InvoiceListResponse[] = []
      for (let i = 0; i < invoiceId?.length; i++) {
        const element = invoiceId[i]
        promiseArr.push(TradeQueryBackstage.getOnlineInvoiceInServicer(element))
      }
      await Promise.all(promiseArr).then(res => {
        for (let i = 0; i < res?.length; i++) {
          const element = res[i]
          data.push(InvoiceListResponse.from(element.data))
        }
      })
      return data
    }
  }
  /**
   * 获取自动开票时间
   * @returns InvoiceAutoBillPolicyResponse
   */
  async getInvoiceAutoBillPolicyInServicer(): Promise<InvoiceAutoBillPolicyResponse> {
    const result = await TradeQueryBackstage.getInvoiceAutoBillPolicyInServicer({
      configType: 1
    })
    return result.data
  }
  /**
   * statisticInvoiceInServicer
   */
  async onLineStatisticInvoiceInServicer(
    page: Page,
    request?: OnlineInvoiceRequest,
    sort?: Array<OnlineInvoiceSortRequest>
  ) {
    const result = await TradeQueryBackstage.statisticOnlineInvoiceInServicer(request)
    this.totalTax = result.data.totalTax ? result.data.totalTax : 0
    this.totalAmount = result.data.totalAmount ? result.data.totalAmount : 0
  }
}
