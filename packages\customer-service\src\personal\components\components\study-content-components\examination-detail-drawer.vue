<template>
  <div>
    <el-drawer title="测验详情" :visible.sync="isShowCourseExamDrawer" size="800px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="测验提交时间" min-width="300">
            <template>2021.02.0 14:00:00</template>
          </el-table-column>
          <el-table-column label="测验成绩" min-width="180">
            <template>60分</template>
          </el-table-column>
          <el-table-column label="是否合格" min-width="120">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-badge is-dot type="danger" class="badge-status">未合格</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="success" class="badge-status">合格</el-badge>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { Vue, Component } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    // 抽屉显隐
    isShowCourseExamDrawer = false

    tableData = [{ field101: '1' }, { field101: '2' }]
  }
</script>
