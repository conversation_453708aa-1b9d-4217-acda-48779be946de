export enum IssueEffectivenessCode {
  /**
   * 有效
   */
  EFFECTIVE = '200',
  /**
   * 期数已过期
   */
  EXPIRED = '201',
  /**
   * 期数已失效
   */
  INVALID = '202',
  /**
   * 期数退款冻结
   */
  REFUND_FROZEN = '203',
  /**
   * 期数换货冻结
   */
  RETURNS_FROZE = '204'
}

/**
 * 有效性
 */
class Effectiveness {
  /**
   * 是否有效
   */
  isEffective = false
  /**
   * 代码
   */
  code = IssueEffectivenessCode.INVALID
  /**
   * 信息
   */
  message = ''
}

export default Effectiveness
