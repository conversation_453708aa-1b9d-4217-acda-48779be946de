<template>
  <el-main>
    <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb15">
      <!--      <div class="f-fl f-mr50">温馨提示：当前网校未开启web、h5访问。请完成网校配置后开启对外访问。</div>-->
      <div class="f-flex f-fl">
        <div class="f-mr50">
          <i class="f-vm f-c6">Web 访问</i>
          <el-switch v-model="switch1" class="f-ml10"></el-switch>
        </div>
        <div class="f-mr50">
          <i class="f-vm f-c6">H5 访问</i>
          <el-switch v-model="switch2" class="f-ml10"></el-switch>
        </div>
      </div>
    </el-alert>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="Web 端" name="first">
        <div class="f-p15">
          <el-tabs v-model="activeName2" type="card" class="m-tab-card">
            <div class="tab-right">
              <el-button type="primary" size="medium" class="f-mr15">
                <i class="hb-iconfont icon-complelearn f-mr5"></i>预览
              </el-button>
            </div>
            <el-tab-pane label="门户信息配置" name="first">
              <el-card shadow="never" class="m-card f-mb15">
                详见 0201_基础信息配置_门户信息配置.vue
              </el-card>
              <div class="m-btn-bar f-tc is-sticky-1">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </div>
            </el-tab-pane>
            <el-tab-pane label="栏目设置" name="second">
              <el-card shadow="never" class="m-card f-mb15">详见 0202_基础信息配置_栏目设置.vue</el-card>
            </el-tab-pane>
            <el-tab-pane label="轮播图设置" name="third">
              <el-card shadow="never" class="m-card f-mb15">详见 0203_基础信息配置_轮播图设置.vue</el-card>
            </el-tab-pane>
            <el-tab-pane label="风格设置" name="fourth">
              <el-card shadow="never" class="m-card f-mb15">详见 0205_基础信息配置_风格设置.vue</el-card>
            </el-tab-pane>
            <el-tab-pane label="网校SEO配置" name="fifth">
              <el-card shadow="never" class="m-card f-mb15">
                <div class="f-p10">
                  <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
                    配置该网站的标题、描述、关键词，使之能够被搜索引擎更容易收录采集，便于学员在通过搜索引擎（如：百度）搜索到的结果排名较为靠前
                  </el-alert>
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
                      <el-form ref="form" :model="form" label-width="auto" class="m-form">
                        <el-form-item label="标题：">
                          <el-input
                            v-model="form.name"
                            clearable
                            placeholder="读取“门户信息配置 - 平台名称”字段内容"
                            class="wp-95"
                            disabled
                          />
                          <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                            <div slot="content">请前往“门户信息配置 - 平台名称”修改</div>
                          </el-tooltip>
                        </el-form-item>
                        <el-form-item label="描述：">
                          <el-input
                            type="textarea"
                            placeholder="请输入该网校的描述"
                            :rows="6"
                            v-model="form.desc"
                            class="wp-95"
                          />
                        </el-form-item>
                        <el-form-item label="关键词：">
                          <el-input
                            v-model="form.name"
                            clearable
                            placeholder="多个关键词请使用英文(,)逗号隔开"
                            class="wp-95"
                          />
                        </el-form-item>
                      </el-form>
                      <div class="m-btn-bar f-tc">
                        <el-button>取消</el-button>
                        <el-button type="primary">保存</el-button>
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      <el-tab-pane label="移动端" name="second">
        <div class="f-p15">详见 0207_基础信息配置_移动学习.vue</div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        radio1: '1',
        switch1: false,
        switch2: false,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'fifth',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          name1: '周一至周五 (09:00 - 12:00 14:00 - 17:30)',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
