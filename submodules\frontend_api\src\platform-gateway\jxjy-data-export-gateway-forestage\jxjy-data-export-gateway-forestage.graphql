schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""集体缴费管理员导出批次单明细"""
	exportBatchOrderDetailInMyself(request:BatchOrderRequest,sort:[BatchOrderSortRequest]):Boolean!
}
"""@Description 范围查询条件
	<AUTHOR>
	@Date 8:51 2022/5/23
"""
input BigDecimalScopeExcelRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.common.request.BigDecimalScopeExcelRequest") {
	"""result >= begin"""
	begin:BigDecimal
	"""result <= end"""
	end:BigDecimal
}
"""范围查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/5/7 15:34
"""
input DateScopeExcelRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.executor.service.common.request.DateScopeExcelRequest") {
	"""result >= begin"""
	begin:DateTime
	"""result <= end"""
	end:DateTime
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input BatchOrderRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchorder.BatchOrderRequest") {
	"""批次单号集合"""
	batchOrderNoList:[String]
	"""批次单基本信息查询参数"""
	basicData:BatchOrderBasicDataRequest
	"""批次单支付信息查询参数"""
	payInfo:OrderPayInfoRequest
	"""批次单创建人查询参数"""
	creatorIdList:[String]
	"""是否已经申请发票"""
	isInvoiceApplied:Boolean
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""批次单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input BatchOrderSortRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchorder.BatchOrderSortRequest") {
	"""需要排序的字段"""
	field:BatchOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""批次单基本信息查询参数
	<AUTHOR>
	@date 2022/04/17
"""
input BatchOrderBasicDataRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchorder.nested.BatchOrderBasicDataRequest") {
	"""批次单状态
		0: 未确认，批次单初始状态
		1: 正常
		2: 交易完成
		3: 交易关闭
		4: 提交处理中 提交处理完成后，变更为NORMAl
		5: 取消处理中
		@see BatchOrderStatus
	"""
	batchOrderStatusList:[Int]
	"""批次单状态变更时间"""
	batchOrderStatusChangeTime:BatchOrderStatusChangeTimeRequest
	"""批次单支付状态
		<p>
		0：未支付
		1：支付中
		2：已支付
		@see BatchOrderPaymentStatus
	"""
	batchOrderPaymentStatusList:[Int]
	"""批次单发货状态
		0: 未发货
		1: 发货中
		2: 已发货
		@see BatchOrderDeliveryStatus
	"""
	batchOrderDeliveryStatusList:[Int]
	"""批次单价格范围
		<p> 查询非0元批次单 begin填0.01
	"""
	batchOrderAmountScope:BigDecimalScopeExcelRequest
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""批次单状态变更时间查询参数
	<AUTHOR>
	@date 2022/04/17
"""
input BatchOrderStatusChangeTimeRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchorder.nested.BatchOrderStatusChangeTimeRequest") {
	"""未确认"""
	unConfirmed:DateScopeExcelRequest
	"""正常"""
	normal:DateScopeExcelRequest
	"""交易成功"""
	completed:DateScopeExcelRequest
	"""已关闭"""
	closed:DateScopeExcelRequest
	"""提交中"""
	committing:DateScopeExcelRequest
	"""取消处理中"""
	canceling:DateScopeExcelRequest
}
"""订单支付信息相关查询参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderPayInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.order.request.nested.OrderPayInfoRequest") {
	"""收款账号ID"""
	receiveAccountIdList:[String]
	"""交易流水号"""
	flowNoList:[String]
	"""付款类型
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
	"""
	paymentOrderTypeList:[Int]
}
"""排序参数
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum SortPolicy @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.common.request.SortPolicy") {
	"""正序"""
	ASC
	"""倒序"""
	DESC
}
"""批次单可用于排序的字段
	<AUTHOR>
	@date 2022/04/17
"""
enum BatchOrderSortField @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.trade.batchorder.nested.BatchOrderSortField") {
	"""批次单未确认时间（批次单创建）"""
	BATCH_ORDER_UN_CONFIRMED_TIME
	"""批次单提交时间"""
	BATCH_ORDER_COMMIT_TIME
}

scalar List
