<route-meta>
{
"isMenu": true,
"title": "新建专题",
"sort": 1,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>
<template>
  <div>
    <el-main>
      <div class="f-tr f-pt15">
        <el-button type="primary" size="medium" class="f-mr5" @click="preview('web')">
          <i class="hb-iconfont icon-complelearn f-mr5"></i>预览专题web
        </el-button>
        <el-button type="primary" size="medium" class="f-mr15" @click="preview('h5')">
          <i class="hb-iconfont icon-complelearn f-mr5"></i>预览专题h5
        </el-button>
      </div>
      <div class="f-p15">
        <!--第一步-->
        <template v-if="$hasPermission('addTopics')" desc="新建专题" actions="created,activated">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <el-row type="flex" justify="center">
              <el-col :sm="20" :lg="12">
                <el-steps :active="currentStep" align-center class="m-steps f-pt40 f-pb10">
                  <el-step title="设置专题基础信息"></el-step>
                  <el-step title="设置专题门户信息"></el-step>
                  <el-step title="设置专题培训方案"></el-step>
                  <el-step title="保存专题"></el-step>
                </el-steps>
              </el-col>
            </el-row>

            <template v-if="$hasPermission('addBasicInfo')" desc="专题基础信息" actions="@StepOne,saveBasicInfo">
              <step-one
                ref="oneRef"
                v-show="currentStep === 1"
                @getCurrentStep="getCurrentStep"
                @cancel="cancel"
                @saveBasicInfo="saveBasicInfo"
                @clearPortalInfo="clearPortalInfo"
                :basicInfo="thematicManagementItem.basicInfo"
                :portalInfo="thematicManagementItem.portalInfo"
              ></step-one
            ></template>
            <template v-if="$hasPermission('addPortalInfo')" desc="专题门户信息" actions="@StepTwo,savePortalInfo">
              <step-two
                ref="twoRef"
                v-show="currentStep === 2"
                @getCurrentStep="getCurrentStep"
                @BackFirstStep="backFirstStep"
                @cancel="cancel"
                @savePortalInfo="savePortalInfo"
                :portalInfo="thematicManagementItem.portalInfo"
              ></step-two
            ></template>
            <template
              v-if="$hasPermission('addTraining')"
              desc="专题培训方案"
              actions="@StepThree,saveTrainingChannelScheme"
            >
              <step-three
                ref="threeRef"
                v-show="currentStep === 3"
                @BackFirstStep="backFirstStep"
                @commitDraft="commitDraft"
                @saveTrainingChannelScheme="saveTrainingChannelScheme"
                @cancel="cancel"
                :selectedTrainingPlanID="thematicManagementItem.selectedTrainingPlanID"
                :trainClassList.sync="trainClassList"
              ></step-three
            ></template>
            <template v-if="$hasPermission('addFinish')" desc="保存专题" actions="@StepFour">
              <step-four
                v-show="currentStep === 4"
                @newCreate="newCreate"
                :thematicManagementItem="thematicManagementItem"
              ></step-four
            ></template>
          </el-card>
        </template>
      </div>
    </el-main>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import StepOne from '@hbfe/jxjy-admin-specialTopics/src/add/components/step-one.vue'
  import StepTwo from '@hbfe/jxjy-admin-specialTopics/src/add/components/step-two.vue'
  import StepThree from '@hbfe/jxjy-admin-specialTopics/src/add/components/step-three.vue'
  import StepFour from '@hbfe/jxjy-admin-specialTopics/src/add/components/step-four.vue'
  import ThematicManagementList from '@api/service/management/thematic-management/ThematicManagementList'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import ThematicManagementItemBase from '@api/service/management/thematic-management/ThematicManagementItemBase'
  import { DeleteScheme, SchemeList } from '@api/platform-gateway/platform-training-channel-v1'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'

  @Component({
    components: { StepOne, StepTwo, StepThree, StepFour }
  })
  export default class extends Vue {
    //进度条当前所在进度
    currentStep = 1
    isOpenConfig = false //是否开启专题配置
    thematicManagementList = new ThematicManagementList()
    thematicManagementItem = new ThematicManagementItem()
    thematicManagementItemBase = new ThematicManagementItemBase()
    dialogVisible = true
    /**
     * 保存培训报名配置
     */
    trainClassList: TrainClassCommodityVo[] = []
    @Ref('oneRef') oneRef: any
    @Ref('twoRef') twoRef: any
    @Ref('threeRef') threeRef: any

    //下一步
    getCurrentStep(value: number) {
      console.log('当前配置信息', this.thematicManagementItem)
      this.currentStep = value
    }

    // 返回上一步
    backFirstStep(value: number) {
      this.currentStep = value
    }

    // 提交
    async commitDraft(value: number) {
      this.currentStep = value
    }

    // 取消新建
    cancel(value: number) {
      const that = this as any
      this.$confirm('确认要放弃编辑吗?', '提示', {
        confirmButtonText: '确认',
        showCancelButton: true
      }).then(() => {
        this.currentStep = value
        this.oneRef.clear()
        this.twoRef.clear()
        this.threeRef.clear()
        that.$router.push({
          path: '/training/special-topics/manage'
        })
      })
    }

    //继续新建
    newCreate() {
      window.location.reload()
    }

    /**
     * 保存基础信息
     */
    async saveBasicInfo(value: number) {
      await this.thematicManagementItem.validTrainingChannelEntryNameUnique().then(async data => {
        if (data.code == '200') {
          let status
          if (this.thematicManagementItem.topicID) {
            status = await this.thematicManagementItem.updateBasicInfo()
          } else {
            status = await this.thematicManagementItem.saveBasicInfo()
          }
          if (status && status.data.code == '200') {
            this.currentStep = value
            this.oneRef.loading = false
          } else if (status.data.code == '200001') {
            this.$message.error(status.data.message)
            this.oneRef.loading = false
          } else {
            this.$message.error('系统异常!')
            this.oneRef.loading = false
          }
        } else {
          this.$message.error(data.message)
          this.oneRef.loading = false
        }
      })
    }

    /**
     * 保存门户信息
     */
    async savePortalInfo(value: number) {
      let status
      if (this.thematicManagementItem.topicID) {
        status = await this.thematicManagementItem.savePortalInfo()
      } else {
        status = await this.thematicManagementItem.updatePortalInfo()
      }
      if (status.status.code == 200) {
        this.currentStep = value
        this.twoRef.loading = false
      } else {
        this.$message.error(status.status.errors[0].message)
        // this.$message.error('系统异常!')
        this.twoRef.loading = false
      }
    }

    /**
     * 保存培训方案
     */
    async saveTrainingChannelScheme(
      addScheme: Array<SchemeList>,
      updateScheme: Array<SchemeList>,
      deleteScheme: Array<DeleteScheme>,
      value: number
    ) {
      const status = await this.thematicManagementItem.saveTrainingChannelScheme(addScheme, updateScheme, deleteScheme)
      if (status.status.code == 200) {
        this.currentStep = value
        this.threeRef.btLoading = false
      } else {
        this.$message.error('系统异常!')
        this.threeRef.btLoading = false
      }
    }

    //清除门户信息
    clearPortalInfo() {
      this.twoRef.clear()
    }

    /**
     *打开浏览专题页面
     */
    async preview(type: string) {
      localStorage.setItem('topicPortal', JSON.stringify(this.thematicManagementItem))
      localStorage.setItem('topicPortal-trainClassList', JSON.stringify(this.trainClassList))
      if (type == 'web') {
        window.open(window.location.origin + '/topicPortal', '_blank')
      } else if (type == 'h5') {
        window.open(window.location.origin + '/h5/#/platforms/h5/tab/topicPortal', '_blank')
      }
    }

    async created() {
      // 查询专题配置
    }

    async activated() {
      try {
        await this.thematicManagementItemBase.getBaseConfig()
        this.isOpenConfig = this.thematicManagementItemBase.isOpen
      } catch (e) {
        this.$message.error('系统异常')
        this.$router.push({
          path: '/training/special-topics/manage'
        })
      }
      if (!this.isOpenConfig) {
        this.$confirm('网校专题功能暂未开启，请先完成专题基础配置，再进行具体专题页的发布。', '系统提醒', {
          confirmButtonText: '立即前往',
          showCancelButton: false,
          showClose: false,
          closeOnClickModal: false
        }).then(() => {
          this.$router.push({
            path: '/training/special-topics/manage'
          })
        })
      }
    }
  }
</script>
