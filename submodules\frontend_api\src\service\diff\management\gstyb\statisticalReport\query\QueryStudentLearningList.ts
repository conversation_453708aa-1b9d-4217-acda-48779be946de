import QueryStudentLearningList from '@api/service/management/statisticalReport/query/QueryStudentLearningList'
import { Page } from '@hbfe/common'
import {
  StudentSchemeLearningRequestVoDiff as StudentSchemeLearningRequestVo,
  SyncResultEnmu
} from '@api/service/diff/management/gszj/statisticalReport/query/vo/StudentSchemeLearningRequestVo'

export default class QueryStudentLearningListDiff extends QueryStudentLearningList {
  syncResult: Record<SyncResultEnmu, number> = {
    [SyncResultEnmu.DisableSynchronized]: -1,
    [SyncResultEnmu.Unsynchronized]: 0,
    [SyncResultEnmu.Synchronized]: 1,
    [SyncResultEnmu.SynchronizationFailure]: 2,
    [SyncResultEnmu.Waitsynchronized]: 3
  }
}
