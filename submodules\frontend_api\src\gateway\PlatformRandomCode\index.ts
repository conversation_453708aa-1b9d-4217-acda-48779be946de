import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformRandomCode'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformRandomCode'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举

// 类

/**
 * 申请随机码参数
<AUTHOR>
 */
export class ApplyAntiCheatCodeRequest {
  /**
   * 能力服务标记的code
   */
  abilityCode?: string
}

/**
 * 防作弊结果信息
<AUTHOR>
 */
export class AntiResultResponse {
  /**
   * 随机码
   */
  randomCode: string
  /**
   * 是否已过期
   */
  expire: boolean
  /**
   * 是否验证通过
   */
  verificationSuccess: boolean
  /**
   * 剩余可拍摄次数，-1表示无限制
   */
  allowCount: number
  /**
   * 当前记录点已拍摄次数
   */
  totalCount: number
}

/**
 * <AUTHOR>
 */
export class CodeInitInfoResponse {
  /**
   * 随机码
   */
  code: string
  /**
   * 二维码内容
   */
  content: string
}

/**
 * <AUTHOR>
 */
export class LoginCodeInitInfoResponse {
  /**
   * 随机码
   */
  code: string
}

/**
 * 随机码生成
<AUTHOR> create 2021/2/6 16:37
 */
export class LoginRandomCodeResponse {
  /**
   * 登录使用的token信息
若token信息没有值，则需要继续获取结果
   */
  token: string
  /**
   * 随机码状态，若随机码失效则需要重新申请
true-有效
false-失效
   */
  status: boolean
  /**
   * 若status&#x3D;false 则有值
<p>
随机码失败的信息
   */
  message: string
  /**
   * 若人脸认证失败则随机码还是有效，可继续使用重新认证
<p>
是否人脸认证失败
   */
  validateFaceError: boolean
  /**
   * 人脸认证回调失败的code
详细规则查看微信文档：https://developers.weixin.qq.com/community/business/doc/000442d352c1202bd498ecb105c00d
   */
  faceErrorCode: string
  /**
   * 人脸认证回调失败的内容
详细规则查看微信文档：https://developers.weixin.qq.com/community/business/doc/000442d352c1202bd498ecb105c00d
   */
  faceErrorMsg: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取防作弊结果
   * @param randomCode
   * @return
   * @param query 查询 graphql 语法文档
   * @param randomCode 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAntiCheatResult(
    randomCode: string,
    query: DocumentNode = GraphqlImporter.getAntiCheatResult,
    operation?: string
  ): Promise<Response<AntiResultResponse>> {
    return commonRequestApi<AntiResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { randomCode },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取登录验证结果
   * @param randomCode
   * @return
   * @param query 查询 graphql 语法文档
   * @param randomCode 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLoginRandomCodeResult(
    randomCode: string,
    query: DocumentNode = GraphqlImporter.getLoginRandomCodeResult,
    operation?: string
  ): Promise<Response<LoginRandomCodeResponse>> {
    return commonRequestApi<LoginRandomCodeResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { randomCode },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请防作弊code
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyAntiCheatCode(
    request: ApplyAntiCheatCodeRequest,
    mutate: DocumentNode = GraphqlImporter.applyAntiCheatCode,
    operation?: string
  ): Promise<Response<CodeInitInfoResponse>> {
    return commonRequestApi<CodeInitInfoResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请登录的随机码
   * @return 返回随机码信息
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyLoginRandomCode(
    mutate: DocumentNode = GraphqlImporter.applyLoginRandomCode,
    operation?: string
  ): Promise<Response<LoginCodeInitInfoResponse>> {
    return commonRequestApi<LoginCodeInitInfoResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
