<route-meta>
{
"isMenu": true,
"title": "集体报名订单",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main>
    <template
      v-if="$hasPermission('query,queryFx,queryZt')"
      desc="query:查询,queryFx:查询（分销）,queryZt:查询（专题）"
      actions="query:searchBase,@BizDistributorSelect,@BizPortalSelect#queryFx:searchBaseFx,@BizPortalDistributorSelect#queryZt:searchBaseZt"
    >
      <div class="f-p15">
        <el-card shadow="never" class="m-card f-mb15">
          <!--条件查询-->
          <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
          <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
          <hb-search-wrapper @reset="resetCondition" class="m-query is-border-bottom">
            <template v-if="$hasPermission('editInvoicePopup')" desc="选择收款账号" actions="@BizReceiveAccountDrawer">
              <el-form-item label="收款账号" v-if="!isZtlogin">
                <biz-receive-account-drawer
                  v-model="receiveAccountList"
                  ref="bizReceiveAccountDrawerRef"
                ></biz-receive-account-drawer>
              </el-form-item>
            </template>
            <el-form-item label="报名批次">
              <el-input v-model="queryParams.batchOrderNo" clearable placeholder="请输入报名批次号" />
            </el-form-item>
            <el-form-item label="交易流水号">
              <el-input v-model="queryParams.flowNo" clearable placeholder="请输入交易流水号" />
            </el-form-item>
            <el-form-item label="购买人帐号">
              <el-input v-model="queryParams.buyerAccount" clearable placeholder="请输入购买人帐号" />
            </el-form-item>
            <el-form-item label="姓名">
              <el-input v-model="queryParams.buyerName" clearable placeholder="请输入购买人姓名" />
            </el-form-item>
            <el-form-item label="交易状态">
              <el-select
                v-model="queryParams.orderStatus"
                clearable
                filterable
                placeholder="请选择交易状态"
                @clear="queryParams.orderStatus = null"
              >
                <el-option v-for="item in orderStatusList" :key="item.code" :value="item.code" :label="item.desc">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="批次提交时间">
              <el-date-picker
                v-model="queryParams.applyTime"
                type="datetimerange"
                :value-format="'yyyy-MM-dd HH:mm:ss'"
                range-separator="至"
                start-placeholder="批次提交时间"
                end-placeholder="批次提交时间"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="交易成功时间">
              <el-date-picker
                v-model="queryParams.paymentCompleteTime"
                type="datetimerange"
                :value-format="'yyyy-MM-dd HH:mm:ss'"
                range-separator="至"
                start-placeholder="交易成功时间"
                end-placeholder="交易成功时间"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item label="销售渠道" v-if="!isFxlogin && !isZtlogin">
              <el-select v-model="queryParams.saleSource" clearable filterable placeholder="请选择销售渠道">
                <el-option
                  v-for="item in saleChannelList"
                  :key="item.code"
                  :value="item.code"
                  :label="item.desc"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="专题名称" v-if="topPicNameFilterShow && !isFxlogin">
              <el-input v-model="queryParams.specialSubjectName" clearable placeholder="请输入专题进行查询" />
            </el-form-item>
            <!--  v-if="isFXshow" -->
            <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="分销商">
              <biz-distributor-select
                v-model="queryParams.distributorId"
                :name="distributorName"
              ></biz-distributor-select>
            </el-form-item>
            <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
              <biz-portal-select
                v-model="queryParams.promotionPortalId"
                :name="promotionPortalName"
              ></biz-portal-select>
            </el-form-item>
            <el-form-item v-if="isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
              <biz-portal-distributor-select
                v-model="queryParams.promotionPortalId"
                :name="promotionPortalName"
              ></biz-portal-distributor-select>
            </el-form-item>
            <el-form-item label="缴费方式">
              <payment-mode v-model="queryParams.paymentMethod"></payment-mode>
            </el-form-item>
            <!--  v-if="isFXshow" -->
            <el-form-item label=" ">
              <el-checkbox label="剔除 0 元订单" name="type" v-model="queryParams.isRemoveZeroOrder"></el-checkbox>
            </el-form-item>
            <template slot="actions">
              <template
                v-if="$hasPermission('search,searchFx,searchZt')"
                desc="search:搜索,searchFx:搜索（分销）,searchZt:搜索（专题）"
                actions="search:searchBase#searchFx:searchBaseFx#searchZt:searchBaseZt"
              >
                <el-button type="primary" @click="search()">查询</el-button>
              </template>
              <template
                v-if="$hasPermission('export,exportFx,exportZt')"
                desc="export:导出,export:导出（分销）,exportZt:导出（专题）"
                actions="export:exportListDataTy#exportFx:exportListDataFx#exportZt:exportListDataZt"
              >
                <el-button @click="exportListData()">导出列表数据</el-button>
              </template>
              <template
                v-if="$hasPermission('exportDetail,exportDetailFx,exportDetailZt')"
                desc="exportDetail:导出明细,exportDetailFx:导出明细（分销）,exportDetailZt:导出明细（专题）"
                actions="exportDetail:exportListDataDetailTy#exportDetailFx:exportListDataDetailFx,exportDetailZt:exportListDataDetailZt"
              >
                <el-button @click="exportListDataDetail()">导出明细数据</el-button>
              </template>
            </template>
          </hb-search-wrapper>
          <!--操作栏-->
          <div class="f-mt20" v-loading="uiConfig.staticLoading">
            <el-alert type="warning" :closable="false" class="m-alert">
              <div class="f-c6">
                当前共有 <span class="f-fb f-co">{{ statistic.totalOrderCount }}</span> 笔批次订单，<span
                  v-show="totalOrderAmountVisible"
                >
                  成交总额 <span class="f-fb f-co">¥ {{ statistic.totalOrderAmount }} </span>。
                </span>
              </div>
            </el-alert>
          </div>
          <!--表格-->
          <el-table
            stripe
            ref="collectiveTableRef"
            :data="collectiveOrderList"
            max-height="500px"
            class="m-table f-mt10"
            v-loading="query.loading"
          >
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="报名批次号" min-width="220">
              <template slot-scope="scope">
                {{ scope.row.batchOrderNo }}
                <hb-copy :content="scope.row.batchOrderNo"></hb-copy>
                <!-- TODO 是否显示专题 -->
                <el-popover
                  placement="top-start"
                  title=""
                  width="200"
                  trigger="hover"
                  :content="scope.row.basicData.saleChannelName"
                >
                  <el-tag
                    slot="reference"
                    type="success"
                    size="small"
                    v-show="scope.row.basicData.saleChannel == SaleChannelEnum.topic"
                    >专题</el-tag
                  >
                </el-popover>
              </template>
            </el-table-column>
            <el-table-column label="购买人信息" min-width="240">
              <template slot-scope="scope">
                <p>姓名：{{ scope.row.buyerInfo.buyerName }}</p>
                <p>帐号：{{ scope.row.buyerInfo.buyerAccount }}</p>
              </template>
            </el-table-column>
            <el-table-column label="缴费方式" min-width="120" align="center">
              <template slot-scope="scope">{{ PaymentMode(scope.row.orderStatus, scope.row.paymentMethod) }}</template>
            </el-table-column>
            <el-table-column label="缴费人次" min-width="120" align="center">
              <template slot-scope="scope">{{ scope.row.payTimes }}</template>
            </el-table-column>
            <el-table-column label="实付金额(元)" width="140" align="right">
              <template slot-scope="scope">
                <div>{{ scope.row.payAmount }}</div>
              </template>
            </el-table-column>
            <el-table-column label="批次提交时间 / 交易成功时间" min-width="220">
              <template slot-scope="scope">
                <p v-if="!isWaitForPlaceOrder(scope.row)">提交：{{ scope.row.applyTime }}</p>
                <p v-if="!isWaitForPlaceOrder(scope.row)">交易：{{ scope.row.paymentCompleteTime }}</p>
                <p v-if="isWaitForPlaceOrder(scope.row)">无</p>
              </template>
            </el-table-column>
            <el-table-column label="交易状态" min-width="120">
              <template slot-scope="scope">
                {{ orderStatus(scope.row.orderStatus) }}
              </template>
            </el-table-column>
            <el-table-column label="操作" width="280" align="center" fixed="right">
              <template slot-scope="scope">
                <template
                  v-if="$hasPermission('placeOrderResult')"
                  desc="下单结果"
                  actions="viewOrderResults, @ViewOrderResult"
                >
                  <el-button
                    type="text"
                    size="mini"
                    v-show="orderResultsVisible(scope.row)"
                    @click="viewOrderResults(scope.row)"
                  >
                    下单结果
                  </el-button>
                </template>
                <template
                  v-if="$hasPermission('detail')"
                  desc="详情"
                  actions="@hbfe/jxjy-admin-trade/src/order/collective/detail.vue"
                >
                  <el-button
                    type="text"
                    size="mini"
                    v-show="isViewDetailVisible(scope.row)"
                    @click="viewDetail(scope.row)"
                  >
                    详情
                  </el-button>
                </template>
                <template v-if="$hasPermission('closeBatch')" desc="关闭批次" actions="@ForceCloseBatchOrder">
                  <el-button
                    type="text"
                    size="mini"
                    v-show="closeBatchOrderVisible(scope.row) && !isZtlogin"
                    @click="closeBatchOrder(scope.row)"
                  >
                    关闭批次
                  </el-button>
                </template>
                <template
                  v-if="$hasPermission('viewRemittanceVoucher')"
                  desc="查看汇款凭证"
                  actions="@AuditRemittanceVoucher"
                >
                  <el-button
                    type="text"
                    size="mini"
                    v-show="isOfflineOrder(scope.row)"
                    @click="viewRemittanceVoucher(scope.row)"
                  >
                    查看汇款凭证
                  </el-button>
                </template>
                <template v-if="$hasPermission('confirmReceipt')" desc="确认收款" actions="confirmReceipt">
                  <el-button
                    type="text"
                    size="mini"
                    v-show="isOfflineOrderWaitPay(scope.row) && !isZtlogin"
                    @click="confirmReceipt(scope.row)"
                  >
                    确认收款
                  </el-button>
                </template>
                <template v-if="$hasPermission('applyRefund')" desc="申请退款" actions="applyRefund, @ApplyOrderRefund">
                  <el-button
                    type="text"
                    size="mini"
                    v-show="isApplyRefundVisible(scope.row) && !isZtlogin"
                    @click="applyRefund(scope.row)"
                  >
                    申请退款
                  </el-button>
                </template>
                <template
                  v-if="$hasPermission('refundDetail')"
                  desc="退款详情"
                  actions="@hbfe/jxjy-admin-trade/src/refund/collective.vue"
                >
                  <el-button
                    type="text"
                    size="mini"
                    v-show="isViewRefundOrderVisible(scope.row)"
                    @click="viewRefundOrderDetail(scope.row)"
                  >
                    退款单详情
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"> </hb-pagination>
        </el-card>
      </div>
      <!--导出数据成功弹窗-->
      <el-dialog
        title="提示"
        :visible.sync="uiConfig.dialog.exportSuccessVisible"
        width="450px"
        class="m-dialog"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
      >
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb">导出成功，是否前往下载数据？</p>
            <p class="f-f13 f-mt5">下载入口：导出任务管理-{{ isDetails ? '集体报名订单明细' : '集体报名订单' }}</p>
          </div>
        </div>
        <div slot="footer">
          <el-button @click="closeExportSuccessDialog">暂 不</el-button>
          <el-button type="primary" @click="jumpToExportTaskManage">前往下载</el-button>
        </div>
      </el-dialog>
      <view-order-result ref="viewOrderResultRef" :visible.sync="uiConfig.dialog.viewOrderVisible"></view-order-result>
      <force-close-batch-order
        ref="forceCloseBatchOrderRef"
        :visible.sync="uiConfig.dialog.closeBatchOrderVisible"
        :batchOrderNo="batchOrderNo"
        @reloadData="handleReloadData"
      ></force-close-batch-order>
      <audit-remittance-voucher
        ref="auditRemittanceVoucherRef"
        :payment-order-no="paymentOrderNo"
        :visible.sync="uiConfig.dialog.auditRemittanceVoucherVisible"
        @reloadData="handleReloadData"
      ></audit-remittance-voucher>
      <apply-order-refund
        ref="applyOrderRefundRef"
        :batchOrderNo="batchOrderNo"
        :visible.sync="uiConfig.dialog.applyOrderRefundVisible"
        :refundType="refundType"
        :invoiceDraftType="invoiceDraftType"
        :refundPersonTime="refundPersonTime"
        :refundAmount="refundAmount"
        @reloadData="handleReloadData"
      ></apply-order-refund>
    </template>
  </el-main>
</template>
<script lang="ts">
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import DataResolve from '@api/service/common/utils/DataResolve'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import { BatchOrderInvoiceStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderInvoiceStatus'
  import BatchOrderTradeStatus, {
    BatchOrderTradeStatusEnum
  } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
  import MutationBatchOrderExport from '@api/service/management/trade/batch/order/mutation/MutationBatchOrderExport'
  import MutationBatchOrderExportInTrainingChannel from '@api/service/management/trade/batch/order/mutation/MutationBatchOrderExportInTrainingChannel'
  import BizReceiveAccountDrawer from '@hbfe/jxjy-admin-components/src/biz/biz-receive-account-drawer.vue'
  import { HasSelectAccountMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectAccountMode'
  import ApplyOrderRefund from '@hbfe/jxjy-admin-trade/src/order/collective/components/apply-order-refund.vue'
  import AuditRemittanceVoucher from '@hbfe/jxjy-admin-trade/src/order/collective/components/audit-remittance-voucher.vue'
  import ForceCloseBatchOrder from '@hbfe/jxjy-admin-trade/src/order/collective/components/force-close-batch-order.vue'
  import PaymentMode from '@hbfe/jxjy-admin-trade/src/order/collective/components/payment-mode.vue'
  import ViewOrderResult from '@hbfe/jxjy-admin-trade/src/order/collective/components/view-order-result.vue'

  import PaymentMethod, { PaymentMethodEnum } from '@api/service/management/trade/batch/order/enum/PaymentMethod'
  import SellerApplyBatchOrderReturnRequestVo from '@api/service/management/trade/batch/order/mutation/vo/SellerApplyBatchOrderReturnRequestVo'
  import QueryBatchOrderList from '@api/service/management/trade/batch/order/query/QueryBatchOrderList'
  import QueryBatchOrderListInTrainingChannel from '@api/service/management/trade/batch/order/query/QueryBatchOrderListInTrainingChannel'
  import BatchOrderUtils from '@api/service/management/trade/batch/order/query/utils/BatchOrderUtils'
  import BatchOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListDetailVo'
  import BatchOrderListPlaceOrderResultVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListPlaceOrderResultVo'
  import BatchOrderListStatisticVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListStatisticVo'
  import QueryBatchOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderListVo'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { Query, UiPage } from '@hbfe/common'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import { ElTable } from 'element-ui/types/table'
  import { cloneDeep } from 'lodash'
  import { bind, debounce } from 'lodash-decorators'
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  @Component({
    components: {
      ApplyOrderRefund,
      AuditRemittanceVoucher,
      ForceCloseBatchOrder,
      ViewOrderResult,
      BizReceiveAccountDrawer,
      BizPortalSelect,
      BizPortalDistributorSelect,
      BizDistributorSelect,
      PaymentMode
    }
  })
  export default class extends Vue {
    // 查看下单结果
    @Ref('viewOrderResultRef') viewOrderResultRef: ViewOrderResult

    // 查看汇款凭证
    @Ref('auditRemittanceVoucherRef') auditRemittanceVoucherRef: AuditRemittanceVoucher

    // 关闭批次
    @Ref('forceCloseBatchOrderRef') forceCloseBatchOrderRef: ForceCloseBatchOrder

    // 收款账号组件
    @Ref('bizReceiveAccountDrawerRef') bizReceiveAccountDrawerRef: BizReceiveAccountDrawer

    @Ref('applyOrderRefundRef') applyOrderRefundRef: ApplyOrderRefund

    @Ref('collectiveTableRef') collectiveTableRef: ElTable
    @Watch('page', {
      deep: true,
      immediate: true
    })
    pageChange(val: any) {
      console.log(val, 'page')
      // this.doQueryPage()
    }

    // 缴费方式枚举
    PaymentMethod = PaymentMethod
    // 查询
    query: Query = new Query()
    // 分页
    page: UiPage
    // 查询条件
    queryParams: QueryBatchOrderListVo = new QueryBatchOrderListVo()
    // 列表
    collectiveOrderList: BatchOrderListDetailVo[] = [] as BatchOrderListDetailVo[]
    // 统计数据
    statistic: BatchOrderListStatisticVo = new BatchOrderListStatisticVo()
    //是否 分销登录
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    //是否 专题登录
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    promotionPortalName = ''
    distributorName = ''
    //专题请求
    queryZtOrder = new QueryBatchOrderListInTrainingChannel()
    // 专题业务请求
    mutationZtOrder = new MutationBatchOrderExportInTrainingChannel()
    firstLoad = false
    /**
     * 批次订单号
     */
    batchOrderNo = ''

    /**
     * 退款类型 0：普通 1：存在考核结果 2：存在学习完成100%
     */
    refundType: number[] = []

    /**
     * 开票状态 1：已开票 2：未开票
     */
    invoiceDraftType: number = null

    /**
     * 退款金额
     */
    refundAmount = 0

    /**
     * 退款人次
     */
    refundPersonTime = 0

    /**
     * 批次单付款单号
     */
    paymentOrderNo = ''

    /**
     * 接口查询
     */
    queryRemote: QueryBatchOrderList =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchOrderList

    /**
     * 收款账号列表
     */
    receiveAccountList: HasSelectAccountMode[] = [] as HasSelectAccountMode[]

    /**
     * 交易状态列表
     */
    orderStatusList: EnumOption<BatchOrderTradeStatusEnum>[] = BatchOrderTradeStatus.list()

    /**
     * 【查询全部或者交易成功】成交金额是否可见
     */
    totalOrderAmountVisible = false

    /**
     * 是否导出明细
     */
    isDetails = false

    SaleChannelEnum = SaleChannelEnum

    /**
     * ui控制组
     */
    uiConfig = {
      // 统计模块过渡动画
      staticLoading: false,
      dialog: {
        // 导出成功
        exportSuccessVisible: false,
        // 下单结果
        viewOrderVisible: false,
        // 关闭批次
        closeBatchOrderVisible: false,
        // 查看汇款凭证
        auditRemittanceVoucherVisible: false,
        // 申请退款
        applyOrderRefundVisible: false
      }
    }

    // 获取销售渠道列表
    saleChannelList = [
      {
        code: SaleChannelEnum.self,
        desc: '网校'
      },
      {
        code: SaleChannelEnum.distribution,
        desc: '分销'
      },
      {
        code: SaleChannelEnum.topic,
        desc: '专题'
      }
    ]

    // 专题名称筛选显示
    get topPicNameFilterShow() {
      return (
        this.queryParams.saleSource === SaleChannelEnum.topic ||
        (!this.queryParams.saleSource && this.queryParams.saleSource !== SaleChannelEnum.self)
      )
    }

    /**
     * 交易状态
     */
    get orderStatus() {
      return (status: number) => {
        return this.orderStatusList.find((item) => item.code === status)?.desc || ''
      }
    }

    /**
     * 缴费方式
     */
    get PaymentMode() {
      return (status: number, PaymentMethod: PaymentMethodEnum) => {
        if (
          status === BatchOrderTradeStatusEnum.Pay_Success ||
          status === BatchOrderTradeStatusEnum.Paying ||
          status === BatchOrderTradeStatusEnum.Opening
        ) {
          return PaymentMethod === PaymentMethodEnum.Online_Payment
            ? this.PaymentMethod.map.get(PaymentMethodEnum.Online_Payment)
            : PaymentMethod === PaymentMethodEnum.Offline_Payment
            ? this.PaymentMethod.map.get(PaymentMethodEnum.Offline_Payment)
            : '-'
        } else {
          return '-'
        }
      }
    }

    /**
     * 是否展示查看详情
     */
    get isViewDetailVisible() {
      return (item: BatchOrderListDetailVo) => {
        return !!item.orderStatus
      }
    }

    /**
     * 是否待下单
     */
    get isWaitForPlaceOrder() {
      return (item: BatchOrderListDetailVo) => {
        return item.orderStatus === BatchOrderTradeStatusEnum.Wait_Place_Order ? true : false
      }
    }

    /**
     * 是否是线下订单-待付款（是否展示“确认收款”）
     */
    get isOfflineOrderWaitPay() {
      return (item: BatchOrderListDetailVo) => {
        const optionList = Array(1).fill(BatchOrderTradeStatusEnum.Paying)
        return optionList.includes(item.orderStatus) && item.isOffLine
      }
    }
    /**
     * 是否是线下订单-待付款（是否展示“查看汇款凭证”）
     */
    get isOfflineOrder() {
      return (item: BatchOrderListDetailVo) => {
        const optionList = [BatchOrderTradeStatusEnum.Paying, BatchOrderTradeStatusEnum.Pay_Success]
        return optionList.includes(item.orderStatus) && item.isOffLine
      }
    }
    @Watch('$route', {
      immediate: true,
      deep: true
    })
    async routeChange(val: any) {
      console.log('$route', val)
      this.batchOrderNo = val.query?.batchOrderNo ?? ''
      if (this.batchOrderNo) {
        this.firstLoad = true
        await this.search()
        this.firstLoad = false
      }
    }
    /**
     * 是否展示下单结果【待付款、支付中、开通中、交易成功、交易关闭中、交易关闭】
     */
    get orderResultsVisible() {
      return (item: BatchOrderListDetailVo) => {
        const optionList: BatchOrderTradeStatusEnum[] = []
        optionList.push(
          BatchOrderTradeStatusEnum.Wait_Pay,
          BatchOrderTradeStatusEnum.Paying,
          BatchOrderTradeStatusEnum.Opening,
          BatchOrderTradeStatusEnum.Pay_Success,
          BatchOrderTradeStatusEnum.Closing_Pay,
          BatchOrderTradeStatusEnum.Close_Pay
        )
        return optionList.includes(item.orderStatus)
      }
    }

    /**
     * 是否展示关闭批次【待付款、支付中】
     */
    get closeBatchOrderVisible() {
      return (item: BatchOrderListDetailVo) => {
        const optionList: BatchOrderTradeStatusEnum[] = []
        optionList.push(BatchOrderTradeStatusEnum.Wait_Pay, BatchOrderTradeStatusEnum.Paying)
        return optionList.includes(item.orderStatus)
      }
    }

    /**
     * 是否展示申请退款
     */
    get isApplyRefundVisible() {
      return (item: BatchOrderListDetailVo) => {
        const optionList = Array(1).fill(BatchOrderTradeStatusEnum.Pay_Success)
        const result =
          optionList.includes(item.orderStatus) && !item.isBatchOrderWaitDelivery && item.refundInfo.enableRefund
        return result ? true : false
      }
    }

    /**
     * 是否展示退款单详情
     */
    get isViewRefundOrderVisible() {
      return (item: BatchOrderListDetailVo) => {
        const optionList = Array(1).fill(BatchOrderTradeStatusEnum.Pay_Success)
        return optionList.includes(item.orderStatus) && !item.isBatchOrderWaitDelivery && item.hasRefundRecord
          ? true
          : false
      }
    }

    @Watch('queryParams', {
      immediate: true,
      deep: true
    })
    queryParamsChange(val: any) {
      console.log('queryParams', JSON.stringify(val))
    }

    constructor() {
      super()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.page = new UiPage(this.pageCollectiveOrderFX, this.pageCollectiveOrderFX)
      } else if (this.isZtlogin) {
        this.page = new UiPage(this.pageCollectiveOrderZt, this.pageCollectiveOrderZt)
      } else {
        this.page = new UiPage(this.pageCollectiveOrder, this.pageCollectiveOrder)
      }
    }

    /**
     * 页面初始化
     */
    async created() {
      await this.search()
    }

    async search() {
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.searchBaseFx()
      } else if (this.isZtlogin) {
        await this.searchBaseZt()
      } else {
        await this.searchBase()
      }
    }
    /**
     * 查询集体报名订单列表
     */
    async searchBaseZt() {
      this.page.pageNo = 1
      await this.pageCollectiveOrderZt()
    }

    /**
     * 查询集体报名订单列表
     */
    async pageCollectiveOrderZt() {
      this.query.loading = true
      try {
        this.queryParams.receiveAccountIdList = this.getReceiveAccountIdList()
        this.totalOrderAmountVisible =
          !this.queryParams.orderStatus || this.queryParams.orderStatus === 6 ? true : false
        if (this.firstLoad) {
          this.queryParams.batchOrderNo = this.batchOrderNo
        }
        this.collectiveOrderList = await this.queryZtOrder.queryBatchOrderList(this.page, this.queryParams)
        await this.getCollectiveOrderStaticZt()
      } catch (e) {
        console.log(e)
      } finally {
        this.collectiveTableRef.doLayout()
        this.query.loading = false
      }
    }

    /**
     * 查询集体报名订单列表
     */
    async searchBase() {
      this.page.pageNo = 1
      await this.pageCollectiveOrder()
    }

    /**
     * 查询集体报名订单列表
     */
    async pageCollectiveOrder() {
      this.query.loading = true
      try {
        this.queryParams.receiveAccountIdList = this.getReceiveAccountIdList()
        this.totalOrderAmountVisible =
          !this.queryParams.orderStatus || this.queryParams.orderStatus === 6 ? true : false
        if (this.firstLoad) {
          this.queryParams.batchOrderNo = this.batchOrderNo
        }
        this.collectiveOrderList = await this.queryRemote.queryBatchOrderList(this.page, this.queryParams)
        await this.getCollectiveOrderStatic()
      } catch (e) {
        console.log(e)
      } finally {
        this.collectiveTableRef.doLayout()
        this.query.loading = false
      }
    }

    /**
     * 查询分销商集体报名订单列表
     */
    async searchBaseFx() {
      this.page.pageNo = 1
      await this.pageCollectiveOrderFX()
    }

    /**
     * 查询分销商集体报名订单列表
     */
    async pageCollectiveOrderFX() {
      this.query.loading = true
      try {
        this.queryParams.receiveAccountIdList = this.getReceiveAccountIdList()
        this.totalOrderAmountVisible =
          !this.queryParams.orderStatus || this.queryParams.orderStatus === 6 ? true : false
        if (this.firstLoad) {
          this.queryParams.batchOrderNo = this.batchOrderNo
        }
        this.collectiveOrderList = await this.queryRemote.queryFxBatchOrderList(this.page, this.queryParams)
        await this.getCollectiveOrderStaticFX()
      } catch (e) {
        console.log(e)
      } finally {
        this.collectiveTableRef.doLayout()
        this.query.loading = false
      }
    }

    /**
     * 获取统计数据
     */
    async getCollectiveOrderStatic() {
      this.uiConfig.staticLoading = true
      this.statistic = await this.queryRemote.queryBatchOrderListStatistic(this.queryParams)
      this.uiConfig.staticLoading = false
    }

    /**
     * 获取分销商统计数据
     */
    async getCollectiveOrderStaticFX() {
      this.uiConfig.staticLoading = true
      this.statistic = await this.queryRemote.queryFxBatchOrderListStatistic(this.queryParams)
      this.uiConfig.staticLoading = false
    }
    /**
     * 获取专题统计数据
     */
    async getCollectiveOrderStaticZt() {
      this.uiConfig.staticLoading = true
      this.statistic = await this.queryZtOrder.queryBatchOrderListStatistic(this.queryParams)
      this.uiConfig.staticLoading = false
    }
    /**
     * 获取收款账号id集合
     */
    getReceiveAccountIdList(): string[] | null {
      if (DataResolve.isWeightyArr(this.receiveAccountList)) {
        return this.receiveAccountList.map((item: HasSelectAccountMode) => {
          return item.accountId
        })
      }
      return null
    }

    /**
     * 重置条件
     */
    async resetCondition() {
      this.receiveAccountList = new Array<HasSelectAccountMode>()
      if (!this.isZtlogin) {
        this.bizReceiveAccountDrawerRef?.clear()
      }
      this.queryParams = new QueryBatchOrderListVo()
      this.promotionPortalName = ''
      this.distributorName = ''
      await this.search()
    }

    /**
     * 导出数据
     */
    @bind
    @debounce(200)
    async exportListData() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      this.isDetails = false
      try {
        const exportM = new MutationBatchOrderExport()
        this.queryParams.receiveAccountIdList = this.getReceiveAccountIdList()
        exportM.exportParams = cloneDeep(this.queryParams)
        let response
        if (this.isFxlogin && this.isHadFxAbility) {
          response = await this.exportListDataFx(exportM)
        } else if (this.isZtlogin) {
          response = await this.exportListDataZt()
        } else {
          response = await this.exportListDataTy(exportM)
        }
        if (response.status?.isSuccess()) {
          this.uiConfig.dialog.exportSuccessVisible = true
        } else {
          this.$message.error(response.status?.getMessage() || '操作失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }

    async exportListDataTy(exportM: MutationBatchOrderExport) {
      return await exportM.doBatchOrderExport()
    }

    async exportListDataFx(exportM: MutationBatchOrderExport) {
      return await exportM.doFxBatchOrderExport()
    }
    async exportListDataZt() {
      this.mutationZtOrder.exportParams = cloneDeep(this.queryParams)
      return await this.mutationZtOrder.doBatchOrderExport()
    }

    /**
     * 导出明细数据
     */
    @bind
    @debounce(200)
    async exportListDataDetail() {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      this.isDetails = true
      try {
        const exportM = new MutationBatchOrderExport()
        this.queryParams.receiveAccountIdList = this.getReceiveAccountIdList()
        exportM.exportParams = cloneDeep(this.queryParams)
        let response
        if (this.isFxlogin && this.isHadFxAbility) {
          response = await this.exportListDataDetailFx(exportM)
        } else if (this.isZtlogin) {
          response = await this.exportListDataDetailZt()
        } else {
          response = await this.exportListDataDetailTy(exportM)
        }
        if (response.status?.isSuccess()) {
          this.uiConfig.dialog.exportSuccessVisible = true
        } else {
          this.$message.error(response.status?.getMessage() || '操作失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }

    async exportListDataDetailTy(exportM: MutationBatchOrderExport) {
      return await exportM.exportBatchOrderDetailInServicer()
    }

    async exportListDataDetailFx(exportM: MutationBatchOrderExport) {
      return await exportM.exportFxBatchOrderDetailInServicer()
    }
    async exportListDataDetailZt() {
      this.mutationZtOrder.exportParams = cloneDeep(this.queryParams)
      return await this.mutationZtOrder.exportBatchOrderDetailInServicer()
    }
    /**
     * 关闭导出成功弹窗
     */
    closeExportSuccessDialog() {
      this.uiConfig.dialog.exportSuccessVisible = false
    }

    /**
     * 前往下载
     */
    jumpToExportTaskManage() {
      this.uiConfig.dialog.exportSuccessVisible = false
      if (this.isDetails) {
        this.$router.push({
          path: '/training/task/exporttask',
          query: {
            type: 'exportBatchOrderDetail'
          }
        })
      } else {
        this.$router.push({
          path: '/training/task/exporttask',
          query: {
            type: 'exportBatchOrder'
          }
        })
      }
    }

    /**
     * 查看下单结果
     */
    @bind
    @debounce(200)
    async viewOrderResults(row: BatchOrderListDetailVo) {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        const batchOrderNo = row.batchOrderNo
        let result: BatchOrderListPlaceOrderResultVo
        if (this.isFxlogin && this.isHadFxAbility) {
          result = await this.queryRemote.queryFxPlaceBatchOrderResult(batchOrderNo)
        } else {
          result = await this.queryRemote.queryPlaceBatchOrderResult(batchOrderNo)
        }
        this.viewOrderResultRef.data = result
        this.uiConfig.dialog.viewOrderVisible = true
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }

    /**
     * 查看详情
     */
    viewDetail(row: BatchOrderListDetailVo) {
      this.$router.push('/training/trade/order/collective/detail/' + row.batchOrderNo)
    }

    /**
     * 关闭批次
     */
    closeBatchOrder(row: BatchOrderListDetailVo) {
      this.batchOrderNo = row.batchOrderNo
      this.forceCloseBatchOrderRef.reason = ''
      this.uiConfig.dialog.closeBatchOrderVisible = true
    }

    /**
     * 查看汇款凭证
     */
    async viewRemittanceVoucher(row: BatchOrderListDetailVo) {
      this.auditRemittanceVoucherRef.batchOrderNo = row.batchOrderNo
      this.auditRemittanceVoucherRef.remittanceVoucherInfoList = row.remittanceVoucherInfoList
      this.auditRemittanceVoucherRef.isAudit = row.orderStatus !== BatchOrderTradeStatusEnum.Pay_Success
      this.paymentOrderNo = row.paymentOrderNo
      this.uiConfig.dialog.auditRemittanceVoucherVisible = true
    }

    /**
     * 确认收款
     */
    @bind
    @debounce(200)
    async confirmReceipt(row: BatchOrderListDetailVo) {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        const paymentOrderNo = row.paymentOrderNo
        const response =
          await TradeModule.batchTradeBatchFactor.orderFactor.mutationOrderFactor.mutationBatchOrderPay.confirmCollectivePay(
            paymentOrderNo
          )
        if (response.isSuccess()) {
          this.$message.success('操作成功')
          await this.resetCondition()
        } else {
          this.$message.error(response.getMessage() || '操作失败')
        }
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }

    /**
     * 申请退款
     */
    @bind
    @debounce(200)
    async applyRefund(row: BatchOrderListDetailVo) {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      let errorMessage = ''
      try {
        const batchOrderNo = row.batchOrderNo ?? ''
        if (!batchOrderNo) {
          errorMessage = '当前批次单信息异常，请稍后再试！'
          throw new Error('中断函数执行')
        }
        const newestRefundInfo = await BatchOrderUtils.getRefundInfo(row.batchOrderNo)
        row.updateRefundInfo(newestRefundInfo)
        if (newestRefundInfo.enableRefundPersonTime <= 0) {
          errorMessage = '当前批次单无可退款人次和金额，请刷新获取最新数据！'
          throw new Error('中断函数执行')
        }
        // 获取批次开票状态
        const invoiceInfoList = row.invoiceInfoList
        if (DataResolve.isWeightyArr(invoiceInfoList)) {
          invoiceInfoList.forEach((item) => {
            if (item.invoiceStatus === BatchOrderInvoiceStatusEnum.Complete) {
              this.invoiceDraftType = 1
            }
          })
        } else {
          this.invoiceDraftType = 2
        }
        // 获取退款类型
        this.refundType =
          await TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchRefound.queryIsForce(
            row.batchOrderNo
          )
        console.log('refund')
        this.refundPersonTime = row.refundInfo.enableRefundPersonTime ?? 0
        this.refundAmount = row.refundInfo.enableRefundAmount ?? 0
        this.batchOrderNo = batchOrderNo
        this.applyOrderRefundRef.refundParams = new SellerApplyBatchOrderReturnRequestVo()
        this.uiConfig.dialog.applyOrderRefundVisible = true
      } catch (e) {
        console.log(e)
        this.$message.error(errorMessage)
      } finally {
        loading.close()
      }
    }

    /**
     * 查看退款单详情
     */
    viewRefundOrderDetail(row: BatchOrderListDetailVo) {
      this.$router.push({
        path: '/training/trade/refund/collective',
        query: {
          batchOrderNo: row.batchOrderNo ?? ''
        }
      })
    }

    /**
     * 响应列表刷新
     */
    async handleReloadData() {
      await this.resetCondition()
    }
  }
</script>
