import { CoursewareSupplierCourseQueryRequest } from '@api/gateway/btpx@Course-default'

/**
 * <AUTHOR> update 2021/1/28  TODO
 */
class BTPXSupplierCourseParam {
  /**
   * 课程名称
   */
  name?: string
  /**
   * 课程状态
   -1不查
   0表示解析中，1表示解析成功，2表示解析失败
   */
  status = -1
  /**
   * 课程分类id集合
   */
  categoryIdList?: Array<string>
  /**
   * 创建时间查询的起始时间
   */
  startCreateTime?: string
  /**
   * 创建时间查询的截止时间
   */
  endCreateTime?: string
  /**
   * 是否启用， -1表示不查询，0表示不启用，1表示启用
   */
  enable = -1
  /**
   * 课程id集合
   */
  includeCourseIdList: Array<string> = new Array<string>()
  /**
   * 工种
   */
  workTypes?: Array<string>
  /**
   * 标签
   <p>资源平台标签关键字查询，添哥说按照选标签模式做，这里使用标签id集合</p>
   */
  tagIds?: Array<string>
  /**
   * 课程创建单位id
   */
  unitId?: string
  /**
   * 开发资源使用平台
   */
  openUsedPlatforms?: Array<string>
  /**
   * 学分起
   */
  periodBegin?: number
  /**
   * 学分止
   */
  periodEnd?: number
  /**
   * 创建的资源供应商id
   */
  createResourceVendorId?: string
  /**
   * 是否已分配使用平台
   */
  arrangeUsedPlatform: boolean
  /**
   * 工种分类->工种路径，以/开始,/分隔，只需要填两级
   */
  workTypePaths?: Array<string>
  /**
   * 工种分类id
   */
  workTypeCategoryId: string
  /**
   * 资源上传类型， -1表示不查询，0媒体资源，1外链地址
   */
  resourceUploadType?: number
  /**
   * 培训机构id集合
   */
  trainingInstitutionIds?: Array<string>

  /**
   * 转换为远程查询参数
   */
  convert() {
    const remote = new CoursewareSupplierCourseQueryRequest()
    remote.includeCourseIdList = this.includeCourseIdList
    remote.name = this.name
    remote.status = this.status
    remote.workTypes = this.workTypes
    remote.tagIds = this.tagIds
    remote.trainingInstitutionIds = this.trainingInstitutionIds
    // remote.openUsedPlatforms = this.openUsedPlatforms
    remote.periodBegin = this.periodBegin
    remote.periodEnd = this.periodEnd
    remote.createResourceVendorId = this.createResourceVendorId
    remote.arrangeUsedPlatform = this.arrangeUsedPlatform
    remote.workTypePaths = this.workTypePaths
    remote.startCreateTime = this.startCreateTime
    remote.endCreateTime = this.endCreateTime
    remote.enable = this.enable
    remote.resourceUploadType = -1
    return remote
  }
}

export default BTPXSupplierCourseParam
