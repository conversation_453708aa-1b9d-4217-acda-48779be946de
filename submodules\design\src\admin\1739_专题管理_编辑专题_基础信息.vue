<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">专题管理</el-breadcrumb-item>
      <el-breadcrumb-item>编辑专题</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-tabs v-model="activeName" type="card" class="m-tab-card">
        <div class="tab-right">
          <el-button type="primary" size="medium" class="f-mr5">查看专题示例</el-button>
          <el-button type="primary" size="medium" class="f-mr5">
            <i class="hb-iconfont icon-complelearn f-mr5"></i>预览专题web
          </el-button>
          <el-button type="primary" size="medium" class="f-mr15">
            <i class="hb-iconfont icon-complelearn f-mr5"></i>预览专题h5
          </el-button>
        </div>
        <el-tab-pane label="基础信息" name="first">
          <el-card shadow="never" class="m-card f-mb15">
            <div class="m-tit is-border-bottom f-justify-between">
              <span class="tit-txt">基本信息</span>
              <el-link type="primary" :underline="false" class="m-specialimg-pop"
                ><i class="el-icon-picture f-f20 f-mr5 f-vm"></i>查看专题示例<el-image
                  class="transparent-pic"
                  src="/assets/images/transparent-pic.png"
                  :preview-src-list="['/assets/images/demo-special-web-001.png']"
              /></el-link>
            </div>
            <el-row type="flex" justify="center" class="width-limit">
              <el-col :md="20" :lg="16" :xl="13">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20 f-mb40">
                  <el-form-item label="专题入口名称：" required>
                    <el-input
                      v-model="form.name"
                      clearable
                      placeholder="请输入本专题在网校上呈现的名称，不允许重复，最多8个字"
                    />
                  </el-form-item>
                  <el-form-item label="专题名称：" required>
                    <el-input v-model="form.name" clearable placeholder="请输入专题名称" />
                    <div class="f-c9">
                      <i class="el-icon-warning f-f16 f-mr5 f-vm"></i
                      >专题名称将同步显示在web端浏览器标签页标题与H5专题的标题。
                    </div>
                  </el-form-item>
                  <el-form-item label="域名类型：" required>
                    <el-radio-group v-model="form.resource">
                      <el-radio label="系统默认域名" border class="f-mr10"></el-radio>
                      <el-radio label="自有域名" border></el-radio>
                    </el-radio-group>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                      <div slot="content">
                        <p>域名需联系平台技术方进行域名处理才能访问。如使用自有域名请提前进行华为云备案。</p>
                      </div>
                    </el-tooltip>
                  </el-form-item>
                  <el-form-item label="专题域名：" required>
                    <el-input placeholder="请输入指定的二级域名" v-model="input1">
                      <template slot="prepend">https://</template>
                      <template slot="append">.59iedu.com</template>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="专题域名：" required>
                    <el-input v-model="form.name" clearable placeholder="请输入指定的专题域名" />
                  </el-form-item>
                  <el-form-item label="专题类型：" required>
                    <el-checkbox-group v-model="form.resource" disabled>
                      <el-checkbox label="地区" border class="f-mr10"></el-checkbox>
                      <el-checkbox label="行业" border></el-checkbox>
                      <el-checkbox label="单位" border></el-checkbox>
                    </el-checkbox-group>
                    <div class="f-mt10 u-w300">
                      <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" disabled />
                    </div>
                  </el-form-item>
                  <el-form-item label="显示在网校：" required>
                    <el-radio-group v-model="form.resource">
                      <el-radio label="显示" border class="f-mr10"></el-radio>
                      <el-radio label="不显示" border></el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
            <div class="m-tit is-border-bottom">
              <span class="tit-txt">专题模板</span>
            </div>
            <el-row type="flex" justify="center" class="width-limit">
              <el-col :lg="20" :xl="18">
                <el-form label-width="auto" class="m-form f-mt40">
                  <el-form-item label="">
                    <div slot="label">
                      <el-checkbox v-model="checked"><i class="f-fb">模板编号：001</i></el-checkbox>
                    </div>
                    <ul class="m-demo-pic">
                      <li>
                        <div class="demo-pic">
                          <div class="pic">
                            <img src="./assets/images/demo-special-web-001.png" />
                          </div>
                          <div class="preview"><i class="el-icon-zoom-in"></i>预览</div>
                        </div>
                        <div class="f-tc">
                          PC端模板
                        </div>
                      </li>
                      <li>
                        <div class="demo-pic">
                          <div class="pic"><img src="./assets/images/demo-special-h5-001.png" /></div>
                          <div class="preview"><i class="el-icon-zoom-in"></i>预览</div>
                        </div>
                        <div class="f-tc">
                          H5端模板
                        </div>
                      </li>
                    </ul>
                  </el-form-item>
                  <el-form-item label="">
                    <div slot="label">
                      <el-checkbox v-model="checked"><i class="f-fb">模板编号：002</i></el-checkbox>
                    </div>
                    <ul class="m-demo-pic">
                      <li>
                        <div class="demo-pic">
                          <div class="pic">
                            <img src="./assets/images/s-template01.jpg" />
                          </div>
                          <div class="preview"><i class="el-icon-zoom-in"></i>预览</div>
                        </div>
                        <div class="f-tc">
                          PC端模板
                        </div>
                      </li>
                      <li>
                        <div class="demo-pic">
                          <div class="pic"><img src="./assets/images/demo-h5-homepage-2.png" alt=" " /></div>
                          <div class="preview"><i class="el-icon-zoom-in"></i>预览</div>
                        </div>
                        <div class="f-tc">
                          H5端模板
                        </div>
                      </li>
                    </ul>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
            <div class="m-btn-bar f-tc is-sticky f-pt15">
              <el-button>取 消</el-button>
              <el-button type="primary">保 存</el-button>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="门户信息" name="second">
          见1739_专题管理_编辑专题_基础信息.vue
        </el-tab-pane>
        <el-tab-pane label="培训方案" name="third">
          1741_专题管理_编辑专题_设置培训信息.vue
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
