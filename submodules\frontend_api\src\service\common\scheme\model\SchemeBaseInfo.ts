import SkuProperty from '@api/service/common/scheme/model/SkuProperty'
import { SchemeLearningTypeEnum } from '@api/service/common/scheme/enum/SchemeLearningType'
import { SchemeAssessSettingTypeEnum } from '@api/service/common/scheme/enum/SchemeAssessSettingType'

/**
 * @description 培训班基础信息
 */
class SchemeBaseInfo {
  /**
   * 参训资格id
   */
  qualificationId = ''
  /**
   * 是否提供整班重学
   */
  provideRelearn = false
  /**
   * 开放证明打印
   */
  openPrintTemplate = true
  /**
   * 方案sku培训属性
   */
  skuProperty = new SkuProperty()
  /**
   * 培训方案id
   */
  id = ''
  /**
   * 网授课选课方式
   */
  selectCourseType: SchemeLearningTypeEnum = null
  /**
   * 培训班名称
   */
  name = ''
  /**
   * 图片url
   */
  picture = ''
  /**
   * 方案介绍内容
   */
  comment = ''
  /**
   * 考核Id
   */
  assessSettingId = ''
  /**
   * 考核模板名称
   */
  assessSettingName = ''
  /**
   * 期别考核项Id
   * @description 包含线下期别时有值
   */
  issueAssessSettingId = ''
  /**
   * 期别考核项名称
   * @description 包含线下期别时有值
   */
  issueAssessSettingName = ''
  /**
   * 课程考核项Id
   */
  /**
   * 学分成果id
   */
  creditId = ''
  /**
   * 报名开始时间
   */
  registerBeginDate = ''
  /**
   * 报名结束时间
   */
  registerEndDate = ''
  /**
   * 培训开始时间
   */
  trainingBeginDate = ''
  /**
   * 培训结束时间
   */
  trainingEndDate = ''
  /**
   * 获得学时
   */
  period = 0
  /**
   * 是否有培训证明
   */
  hasLearningResult = false
  /**
   * 培训证明模板id
   */
  learningResultId = ''
  /**
   * 培训证明成果id
   */
  learningResultAchievementsId = ''
  /**
   *培训方案id，自主选课复制用
   */
  idCopy = ''
  /**
   *成果是否同步
   */
  needDataSync = true
  /**
   * 培训方案须知
   */
  notice = ''
  /**
   * 培训方案简介id
   */
  introId = ''
  /**
   * 培训方案简介
   */
  introContent = ''
  /**
   * 是否智能学习中
   */
  isIntelligenceLearning = false
  /**
   * 方案考核设置类型
   * @description 为兼容旧数据，默认值为object
   */
  schemeAssessSettingType: SchemeAssessSettingTypeEnum = SchemeAssessSettingTypeEnum.object
}

export default SchemeBaseInfo
