"""独立部署的微服务,K8S服务名:ms-exchange-order-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""返回所有可供填写的换货原因id和原因描述Map"""
	getExchangeApplyReason:Map
}
type Mutation {
	"""同意换货申请"""
	agreeExchangeApply(request:ExchangeOrderAgreeApplyRequest):ExchangeOrderAgreeApplyResponse
	"""拒绝换货申请"""
	rejectExchangeApply(request:ExchangeOrderRejectApplyRequest):ExchangeOrderRejectApplyResponse
	"""重试发货"""
	retryDelivery(request:ExchangeOrderRetryDeliveryRequest):Void
	"""重试回收资源"""
	retryRecycleResouce(request:ExchangeOrderRetryRecycleRequest):Void
	"""取消换货申请"""
	sellerCancelExchangeApply(request:ExchangeOrderCancelApplyRequest):ExchangeOrderCancelApplyResponse
}
"""换货单同意申请请求
	<AUTHOR>
"""
input ExchangeOrderAgreeApplyRequest @type(value:"com.fjhb.ms.exchangeorder.v1.kernel.gateway.graphql.request.ExchangeOrderAgreeApplyRequest") {
	exchangeOrderNo:String!
	approveComment:String
}
"""换货单取消换货申请请求
	<AUTHOR>
"""
input ExchangeOrderCancelApplyRequest @type(value:"com.fjhb.ms.exchangeorder.v1.kernel.gateway.graphql.request.ExchangeOrderCancelApplyRequest") {
	exchangeOrderNo:String!
	cancelReason:String
}
"""换货单拒绝申请请求
	<AUTHOR>
"""
input ExchangeOrderRejectApplyRequest @type(value:"com.fjhb.ms.exchangeorder.v1.kernel.gateway.graphql.request.ExchangeOrderRejectApplyRequest") {
	exchangeOrderNo:String!
	approveComment:String
}
input ExchangeOrderRetryDeliveryRequest @type(value:"com.fjhb.ms.exchangeorder.v1.kernel.gateway.graphql.request.ExchangeOrderRetryDeliveryRequest") {
	exchangeOrderNo:String!
}
input ExchangeOrderRetryRecycleRequest @type(value:"com.fjhb.ms.exchangeorder.v1.kernel.gateway.graphql.request.ExchangeOrderRetryRecycleRequest") {
	exchangeOrderNo:String!
}
type ExchangeOrderAgreeApplyResponse @type(value:"com.fjhb.ms.exchangeorder.v1.kernel.gateway.graphql.response.ExchangeOrderAgreeApplyResponse") {
	code:String
	message:String
}
type ExchangeOrderCancelApplyResponse @type(value:"com.fjhb.ms.exchangeorder.v1.kernel.gateway.graphql.response.ExchangeOrderCancelApplyResponse") {
	code:String
	message:String
}
type ExchangeOrderRejectApplyResponse @type(value:"com.fjhb.ms.exchangeorder.v1.kernel.gateway.graphql.response.ExchangeOrderRejectApplyResponse") {
	code:String
	message:String
}

scalar List
