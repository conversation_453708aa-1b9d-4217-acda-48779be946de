import { QuestionAnswerResult } from '@api/service/common/models/exam/question/answer/AnswerQuestion'

class AnswerChildQuestionDTO {
  questionId: string
  // 试题的分数
  score: number
  // 开始答题时间
  beginAnswerTime: Date
  // 是否打标记、做记号
  flag: boolean
  // 是否已作答
  answered: boolean
  // 提交的作答信息
  submitAnswer: any
  // 答案提交时间
  submitAnswerTime: Date
  // 阅卷得分
  markedScore: number
  // 试题作答结果
  answerResult: QuestionAnswerResult
}

export default AnswerChildQuestionDTO
