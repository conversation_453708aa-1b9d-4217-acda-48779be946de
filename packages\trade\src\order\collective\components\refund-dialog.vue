<template>
  <el-drawer title="退款" :visible.sync="isShow" size="900px" @close="cancel" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-alert type="warning" show-icon :closable="false" class="m-alert">
        退款后，原有的学习记录将清空，请确认是否继续。
      </el-alert>
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="formRef" :rules="rules" :model="form" label-width="auto" class="m-form f-mt20">
            <el-form-item label="退款金额：" class="is-text">
              <span class="f-cr f-fb f-f16">¥ {{ refundDetail.payAmount }}</span>
              <span class="f-cr">（换班订单退款退的是初始物品的订单价格，退物品是最新换入成功的物品。）</span>
            </el-form-item>
            <el-form-item label="退款说明：" class="is-text">
              退款后，原有的学习记录将清空，请确认是否继续。
            </el-form-item>
            <el-form-item
              label="退款提示："
              v-if="isForceType.includes(2) && !invoiceStatus.includes(2)"
              class="is-text"
            >
              已有学员培训班的学习进度已达到100%，是否强制退款？
            </el-form-item>
            <el-form-item
              label="退款提示："
              v-if="isForceType.includes(0) && invoiceStatus.includes(2)"
              class="is-text"
            >
              此报名批次订单已开票，是否强制退款？
            </el-form-item>
            <el-form-item label="退款提示：" v-if="isForceType.includes(1)" class="is-text">
              已有学员考核通过，是否强制退款？
            </el-form-item>
            <el-form-item
              label="退款提示："
              v-if="isForceType.includes(2) && invoiceStatus.includes(2)"
              class="is-text"
            >
              此报名批次订单发票已开具且已有学员培训班学习进度已达到100%，是否强制退款？
            </el-form-item>

            <el-form-item label="退款理由：" required prop="reason">
              <el-select v-model="form.reason" clearable placeholder="请选择退款理由">
                <el-option
                  v-for="refundReasonItem in refundReasonList"
                  :key="refundReasonItem.refundReasonId"
                  :label="refundReasonItem.refundReasonValue"
                  :value="refundReasonItem.refundReasonId"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="退款说明：" required prop="explain">
              <el-input type="textarea" :rows="6" v-model="form.explain" placeholder="请输入退款说明" />
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="cancel">取消</el-button>
              <el-button type="primary" @click="submit">确认退款</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import {
    ReturnReasonInfoResponse,
    SubOrderResponse
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import { ForceReasonEnum } from '@api/service/management/trade/single/order/enum/ForceReasonEnum'
  import BatchOrderMainOrderSubOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderSubOrderListDetailVo'
  @Component
  export default class extends Vue {
    @Prop({ type: Object, default: {} }) refundDetail: BatchOrderMainOrderSubOrderListDetailVo //退款单详情
    @Prop({ type: Array, default: {} }) refundReasonList: Array<ReturnReasonInfoResponse> //退款原因
    @Prop({ type: Array, default: [] }) isForceType: Array<number> //强制退款类型
    @Prop({ type: Array, default: 0 }) invoiceStatus: Array<number> //强制退款发票类型
    @Ref('formRef') formRef: any
    isShow = false

    form = {
      reason: '', // 退款理由
      explain: '', // 退款说明
      // orderNo: '', // 订单号
      subOrderNo: '' // 子订单号
    }

    rules = {
      reason: [{ required: true, message: '请选择退款理由', trigger: 'blur' }],
      explain: [{ required: true, message: '请输入退款说明', trigger: ['blur', 'change'] }]
    }
    @Watch('refundDetail', {
      deep: true,
      immediate: true
    })
    refundDetailChange(val: any) {
      if (val) {
        console.log(val, '退款数据')
      }
    }
    isShowDialog() {
      this.isShow = !this.isShow
    }
    cancel() {
      this.form.reason = ''

      this.form.explain = ''
      this.$nextTick(() => {
        this.formRef.clearValidate()
      })
      this.isShow = false
    }
    submit() {
      this.formRef.validate((valid: any) => {
        if (valid) {
          this.form.subOrderNo = this.refundDetail.subOrderNo
          this.$emit('refundClick', this.form)
          this.isShow = false
        }
      })
    }
  }
</script>

<style scoped></style>
