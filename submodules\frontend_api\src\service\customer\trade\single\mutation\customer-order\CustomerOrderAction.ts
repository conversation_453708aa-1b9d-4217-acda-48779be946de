// // import Commodity from '@api/service/customer/trade/mutation/customer-order/vo/commodity/Commodity'
// import CreateOrder from '@api/service/customer/trade/mutation/customer-order/vo/create-order/CreateOrder'
// import PayOrder from '@api/service/customer/trade/mutation/customer-order/vo/pay-order/PayOrder'
// import QueryTradeFacade from '@api/service/customer/trade/QueryTradeFacade'
// import commonError from '@api/helpers/decorator/error/common'
// import UserModule from '@api/module/customer/user/UserModule'
// import BuyCommodityValidationResult from '@api/service/customer/trade/mutation/customer-order/vo/commodity/BuyCommodityValidationResult'
// import { TerminalEnum } from '@api/service/common/enums/trade/Terminal'
// // 订单业务模型 补贴比较特殊 创建订单和支付订单都在同一个页面。所以封装了一个类来负责管理
export default class CustomerOrder {
  //   // 创建订单模型
  //   private createOrder: CreateOrder
  //   // 支付订单模型
  //   private payOrder: PayOrder
  //   // 是否已经对业务对象赋值
  //   private isSetValueOver = false
  //   private orderNo = ''
  //   // 商品信息
  //   // private _commodityDetail: Commodity = undefined
  //
  //   // 注意: 如果是只用到取消订单 可不传
  //   // 如果是创建/支付 必传
  //   constructor(commodity: Commodity) {
  //     // this._commodityDetail = commodity
  //   }
  //
  //   get commodityDetail() {
  //     // return this._commodityDetail
  //   }
  //
  //   /**
  //    * @description: 创建订单校验并获取业务操作实例。如果业务操作实例是undefined 则请提示报错。
  //    * @param terminal
  //    * @return {{actionInstance: 操作对象实例, validateResult: 验证结果}}
  //    */
  //
  //   async doValidateAndGetActionInstance(
  //     terminal: TerminalEnum
  //   ): Promise<{
  //     actionInstance?: {
  //       payOrder?: PayOrder
  //       createOrder?: CreateOrder
  //     }
  //     validateResult: BuyCommodityValidationResult
  //   }> {
  //     const validateResult = await this.doValidateCreateOrder(terminal)
  //     const actionInstance = await this.getCreateOrderAndPayOrderOnly(terminal, validateResult)
  //     return {
  //       actionInstance,
  //       validateResult
  //     }
  //   }
  //   /**
  //    * @description: 创建订单验证.请先调用该方法进行商品校验。然后获取对应
  //    * @param terminal 终端类型
  //    * @return {*} 返回创建订单模型 或者支付订单模型
  //    */
  //   @commonError
  //   private async doValidateCreateOrder(terminal: TerminalEnum) {
  //     // TODO: 异常处理
  //     // const validateResult = await this._commodityDetail.validate(terminal)
  //     const errorCode = validateResult.errCode
  //     // 业务异常错误代码封装
  //     if (errorCode === '40001') {
  //       // 当代码为40001 存在两种情况 一种是创建订单未付款 一种是创建订单已付款
  //       const orderNo = validateResult.orderNo || ''
  //       this.orderNo = orderNo
  //       if (orderNo) {
  //         const res = await this.doValidateIsPay(orderNo)
  //         // 后端服务慢的情况下 可能会出现订单号出现 但是订单还没生成的情况，这时候预警
  //         if (res.status.code === 30001) {
  //           validateResult.errCode = '40005'
  //           validateResult.errMsg = '订单清洗未完成'
  //         }
  //         // 请求订单失败
  //         else if (!res.status.isSuccess()) {
  //           // todo
  //           validateResult.errCode = '500'
  //           validateResult.errMsg = '请求订单失败！'
  //         } else if (res.data) {
  //           validateResult.errCode = '40004'
  //           validateResult.errMsg = '订单已支付！'
  //         }
  //       }
  //     }
  //     return validateResult
  //   }
  //
  //   /**
  //    * @description: 获取创建订单对象和支付对象。只需要调用一次 请在商品校验之后调用
  //    * @param terminal 支付终端
  //    * @param validateResult 商品校验结果
  //    * @return
  //     * // 当订单已经支付了(出错了) 返回空对象
  //       // 当订单已经创建了 返回支付业务实例
  //       // 其他情况： 返回订单和创建实例
  //    */
  //
  //   private async getCreateOrderAndPayOrderOnly(terminal: TerminalEnum, validateResult: BuyCommodityValidationResult) {
  //     if (this.isSetValueOver) {
  //       console.error('请不要多次创建。')
  //       return undefined
  //     }
  //
  //     // 错误情况下的异常代码
  //     const errorCode = ['500', '30001', '30002', '30004', '40002', '40004']
  //     if (errorCode.includes(validateResult.errCode)) {
  //       return undefined
  //     }
  //     if (validateResult.errCode === '40001') {
  //       this.createInstanceByPayOrder(terminal)
  //       return {
  //         payOrder: this.payOrder
  //       }
  //     } else {
  //       this.createInstanceByCreateOrder(terminal)
  //       this.createInstanceByPayOrder(terminal)
  //       return {
  //         payOrder: this.payOrder,
  //         createOrder: this.createOrder
  //       }
  //     }
  //   }
  //   /**
  //    * @description: 校验订单是不是已经购买
  //    * @param {*} orderNo 订单号
  //    * @return {{data, status}} data: true/false 已支付
  //    */
  //
  //   private async doValidateIsPay(orderNo: string) {
  //     const queryOrderDetail = QueryTradeFacade.queryOrderDetailMultiton(orderNo)
  //     const res = await queryOrderDetail.queryOrderDetailByOrderNo()
  //     if (!res.isSuccess()) {
  //       return {
  //         data: false,
  //         status: res
  //       }
  //     }
  //     const orderDetail = queryOrderDetail.orderDetail
  //     const status = orderDetail.orderInfo.status
  //     let result = false
  //     if (status > 2 && status !== 8) {
  //       result = true
  //     }
  //
  //     return {
  //       data: result,
  //       status: res
  //     }
  //   }
  //
  //   // 创建对应的_createOrder实例
  //   private createInstanceByCreateOrder(terminal: TerminalEnum) {
  //     // eslint-disable-next-line prefer-rest-params
  //     const buyerId = UserModule.userInfo.userId
  //     const commodities = [
  //       {
  //         skuId: this._commodityDetail.commodityId,
  //         quantity: 1
  //       }
  //     ]
  //
  //     this.createOrder = new CreateOrder(this.commodityDetail.trainingInstitutionId, buyerId, commodities, terminal)
  //   }
  //
  //   // 创建对应的_payOrder实例  ui禁止调用
  //   private createInstanceByPayOrder(terminal: TerminalEnum) {
  //     this.payOrder = new PayOrder(terminal, this.orderNo)
  //   }
}
