import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjypxtypt-student-learning-backstage'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-student-learning-backstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 重推学员学习合格数据
   * @return
   * @param query 查询 graphql 语法文档
   * @param studentNos 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rePushStudentTrainingResultInServicer(
    studentNos: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.rePushStudentTrainingResultInServicer,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { studentNos },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重推学员学习合格数据-统一网关 v2
   * @return
   * @param query 查询 graphql 语法文档
   * @param studentNos 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rePushStudentTrainingResultToGatewayInServicerV2(
    studentNos: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.rePushStudentTrainingResultToGatewayInServicerV2,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { studentNos },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
