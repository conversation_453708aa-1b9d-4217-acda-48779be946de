import Menu from '@/models/Menu'
import routeAdapter from '@/router/RouteAdapter'
import { Route } from 'vue-router'

const routes = routeAdapter.mergedRoutes
const basicSchool = routeAdapter.basicSchool
const fxRoutes = routeAdapter.fxRouters
const menus: Array<Menu> = new Array<Menu>()
// 切换后的菜单信息
const fxMenus: Array<Menu> = new Array<Menu>()
function collectRouteGroups(routes: any, groupSet: Set<string> = new Set()): Set<string> {
  if (!Array.isArray(routes)) {
    return groupSet
  }
  for (const route of routes) {
    if (route.meta && route.meta.group) {
      groupSet.add(route.meta.group)
    }
    if (route.children && Array.isArray(route.children) && route.children.length > 0) {
      collectRouteGroups(route.children, groupSet)
    }
  }

  return groupSet
}

const setId = (list: Array<Menu>, id: string, index: number) => {
  list?.forEach((menu: Menu, subIndex: number) => {
    const _thisId = id + subIndex
    menu.meta.id = _thisId
    menu.meta.index = index
    if (menu?.children?.length) {
      setId(menu.children, _thisId, index)
    }
  })
}

function createMenuListFromRoutes(_routes: any, menuChildren: Array<Menu>, path: string) {
  const le = _routes.length
  _routes
    // 只有被定义为是菜单项，才出现在菜单列表
    .filter((route: Route) => {
      return route?.meta?.isMenu && !route?.meta?.hideMenu
    })
    // 执行排序
    .sort((a: Menu, b: Menu): number => {
      return (a.meta.sort || le) - (b.meta.sort || le)
    })
    .forEach((route: any) => {
      const menu: Menu = new Menu()
      menu.closeAble = true
      menu.name = route.meta?.title
      menu.openWhenInit = false
      menu.id = route.path
      menu.code = route.path
      menu.router.path = `${path || ''}${route.path}`
      menu.meta = route.meta
      menu.router.path = menu.router.path.replace(/\/$/, '')
      if (route?.children?.length) {
        createMenuListFromRoutes(route.children, menu.children, `${menu.router.path}/`)
        route.redirect = menu.children[0]?.router?.path
      }
      menuChildren.push(menu)
    })
}

createMenuListFromRoutes(routes, menus, '')
routes
  // 只有被定义为是菜单项，才出现在菜单列表
  .filter((route: Route) => {
    return route?.meta?.isMenu && !route?.meta?.hideMenu
  })
  // 执行排序
  .sort((a: Menu, b: Menu): number => {
    return (a.meta.sort || routes.length) - (b.meta.sort || routes.length)
  })
  .forEach((route: any, index: number) => {
    setId(route.children, route.path, index)
  })
const menuMap = {}
const fxMenuMap = {}
const convertMenuMap = (theMenus: Array<Menu>) => {
  theMenus.forEach((menu) => {
    menuMap[menu.router.path] = true
    if (menu.children?.length) {
      convertMenuMap(menu.children)
    }
  })
}

// 处理分销路由
createMenuListFromRoutes(fxRoutes, fxMenus, '')
fxRoutes
  // 只有被定义为是菜单项，才出现在菜单列表
  .filter((route: Route) => {
    return route?.meta?.isMenu && !route?.meta?.hideMenu
  })
  // 执行排序
  .sort((a: Menu, b: Menu): number => {
    return (a.meta.sort || fxRoutes.length) - (b.meta.sort || fxRoutes.length)
  })
  .forEach((route: any, index: number) => {
    setId(route.children, route.path, index)
  })
const convertFxMenuMap = (theMenus: Array<Menu>) => {
  theMenus.forEach((menu) => {
    fxMenuMap[menu.router.path] = true
    if (menu.children?.length) {
      convertFxMenuMap(menu.children)
    }
  })
}
const basicSchoolSet = collectRouteGroups(basicSchool, new Set())
export { convertFxMenuMap, convertMenuMap, fxMenuMap, menuMap, routes, basicSchoolSet }
export const menuList = menus
/**
 * 切换后的菜单信息
 */
export const fxMenuList = fxMenus
