import Question<PERSON>ibraryUI from '@/store/modules-ui/exam/models/QuestionLibraryUI'
import { getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ExamLibraryDto, LibraryQuestionCountResponse } from '@api/gateway/PlatformExam'
import { ExamLibrary } from '@api/service/common/models/exam/library/ExamLibrary'

export interface IQuestionLibraryUIState {
  questionLibraryList: Array<QuestionLibraryUI>
}

@Module({ namespaced: true, store, dynamic: true, name: 'QuestionLibraryUIModule' })
class QuestionLibraryUIModule extends VuexModule implements IQuestionLibraryUIState {
  questionLibraryList: Array<QuestionLibraryUI> = new Array<QuestionLibraryUI>()

  @Mutation
  SET_QUESTION_LIBRARY_LIST(list: Array<ExamLibrary>) {
    this.questionLibraryList = new Array<QuestionLibraryUI>()
    this.questionLibraryList.push(
      ...list.map(library => {
        const item = new QuestionLibraryUI()
        item.id = library.id
        item.name = library.name
        item.createTime = library.createTime
        item.questionCount = library.enableQuestionCount
        item.createUserName = library.createUserName
        return item
      })
    )
  }

  /**
   * 更新题库的选择状态
   * @param param
   * @constructor
   */
  @Mutation
  UPDATE_QUESTION_LIBRARY_SELECT_STATE(param: { id: string; hasSelect: boolean }) {
    this.questionLibraryList = this.questionLibraryList.map(p => {
      if (p.id === param.id) {
        p.hasSelect = param.hasSelect
      }
      return p
    })
  }
}

export default getModule(QuestionLibraryUIModule)
