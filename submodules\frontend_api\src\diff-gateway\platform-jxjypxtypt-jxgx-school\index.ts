import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-jxgx-school'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 单点登录请求
<AUTHOR>
@since 2024/8/20
 */
export class EnterIndexRequest {
  /**
   * 学员信息key
   */
  studentInfoKey: string
}

/**
 * 获取学员学时数请求条件
 */
export class GetStudyCourseNumRequest {
  /**
   * 学员ID
   */
  studentId?: string
  /**
   * 年度
   */
  year?: string
  /**
   * 培训类别（职称系列）
   */
  trainingCategoryId?: string
  /**
   * 培训专业（职称专业）
   */
  trainingProfessionId?: string
}

/**
 * 学员课程批量作废
 */
export class StudentCoursesInvalidRequest {
  /**
   * 学号
   */
  studentNo?: string
}

export class VerifyPermissionsToCourseRequest {
  /**
   * 年度
   */
  year?: string
  /**
   * 人员ID
   */
  id?: string
  /**
   * 系列代码
   */
  zcxlCode?: string
  /**
   * 系列名称
   */
  zcxlName?: string
  /**
   * 专业代码
   */
  zczyCode?: string
  /**
   * 专业名称
   */
  zczyName?: string
}

/**
 * 单点响应
<AUTHOR>
@since 2024/5/24
 */
export class EnterIndexResponse {
  /**
   * 登录token
   */
  token: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 平台对应培训类别id
   */
  trainingCategory: string
  /**
   * 平台对应培训专业Id
   */
  trainingProfessional: string
  /**
   * 管理平台课程id
   */
  commodityId: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 学员课程批量作废
 */
export class StudentCoursesInvalidResponse {
  /**
   * 作废失败学员课程id
   */
  studentCourseIdList: Array<string>
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 获取学员学时数
 */
export class StudyCourseNumResponse {
  /**
   * 学员ID
   */
  id: string
  /**
   * 年度
   */
  year: string
  /**
   * 职称系列对应的培训类别
   */
  trainingCategoryId: string
  /**
   * 职称专业对应的培训专业
   */
  trainingProfessionId: string
  /**
   * 公需课学时数
   */
  publicHours: number
  /**
   * 专业课学时数
   */
  professionHours: number
  /**
   * 第三方接口信息
   */
  msg: string
}

export class VerifyPermissionsToCourseResponse {
  /**
   * 是否可以学习
   */
  flag: boolean
  /**
   * 管理系统方的msg
   */
  msg: string
}

/**
 * 验证职称
 */
export class VerifyPersonZcInfoResponse {
  /**
   * 学员ID
   */
  id: string
  /**
   * 是否一致，方案上的系列、专业和学员一致的时候返回true
   */
  validResult: boolean
  /**
   * 错误信息
   */
  msg: string
  /**
   * 学员上的职称信息
   */
  studentProfessionInfo: StudentProfessionInfoResponse
  /**
   * 商品上的职称信息
   */
  commodityProfessionInfo: CommodityProfessionInfoResponse
}

/**
 * <AUTHOR>
@since 2024/12/21
 */
export class VerifyStudyStatusResponse {
  id: string
  /**
   * 1：没有在学课程，<0:有
在学课程，具体信息见
MSG
   */
  code: number
  msg: string
  /**
   * 平台名称
   */
  platformName: string
  /**
   * 管理平台课程id
   */
  commodityId: string
  /**
   * 课程名称
   */
  CommodityName: string
}

/**
 * 商品职称职称信息
 */
export class CommodityProfessionInfoResponse {
  /**
   * 培训类别
   */
  trainingCategoryId: string
  /**
   * 培训专业
   */
  trainingProfessionId: string
}

/**
 * 学员职称信息
 */
export class StudentProfessionInfoResponse {
  /**
   * 培训类别
   */
  trainingCategoryId: string
  /**
   * 培训专业
   */
  trainingProfessionId: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 单点登录入口
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async enterIndex(
    request: EnterIndexRequest,
    mutate: DocumentNode = GraphqlImporter.enterIndex,
    operation?: string
  ): Promise<Response<EnterIndexResponse>> {
    return commonRequestApi<EnterIndexResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取学员学时数
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudyCourseNum(
    request: GetStudyCourseNumRequest,
    mutate: DocumentNode = GraphqlImporter.getStudyCourseNum,
    operation?: string
  ): Promise<Response<StudyCourseNumResponse>> {
    return commonRequestApi<StudyCourseNumResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量作废学员课程
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async invalidStudentCourses(
    request: StudentCoursesInvalidRequest,
    mutate: DocumentNode = GraphqlImporter.invalidStudentCourses,
    operation?: string
  ): Promise<Response<StudentCoursesInvalidResponse>> {
    return commonRequestApi<StudentCoursesInvalidResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 验证是否有开课权限
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifyPermissionsToCourse(
    request: VerifyPermissionsToCourseRequest,
    mutate: DocumentNode = GraphqlImporter.verifyPermissionsToCourse,
    operation?: string
  ): Promise<Response<VerifyPermissionsToCourseResponse>> {
    return commonRequestApi<VerifyPermissionsToCourseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 验证学员职称和商品上的是否一致
   * @param studentId 学员ID
   * @param schemeId  方案ID
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifyPersonZcInfo(
    params: { studentId?: string; schemeId?: string },
    mutate: DocumentNode = GraphqlImporter.verifyPersonZcInfo,
    operation?: string
  ): Promise<Response<VerifyPersonZcInfoResponse>> {
    return commonRequestApi<VerifyPersonZcInfoResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 验证是否正在其他平台学习
   * @param mutate 查询 graphql 语法文档
   * @param studentId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifyStudyStatus(
    studentId: string,
    mutate: DocumentNode = GraphqlImporter.verifyStudyStatus,
    operation?: string
  ): Promise<Response<VerifyStudyStatusResponse>> {
    return commonRequestApi<VerifyStudyStatusResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
