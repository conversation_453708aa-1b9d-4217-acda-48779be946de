import { StudentIndustryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

export class CertAndPractitioner {
  certificatesTypeCode?: number
  certificatesType: string
  practitionerCategoryCode?: number
  practitionerCategory: string
}
export default class LsStudentIndustryInfoVo extends StudentIndustryResponse {
  /**
   * 药师行业 证书类型和执业类别信息
   */
  certificatesTypeName: string = undefined
  practitionerCategoryName: string = undefined
}
