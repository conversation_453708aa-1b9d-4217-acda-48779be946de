<template>
  <div class="course-drawer-content clear-chrome-input-class">
    <el-input
      :value="courseName"
      :placeholder="placeholder"
      @clear="clear"
      v-on:click.native="handleShow"
      class="el-select clear-chrome-input-class"
    >
      <template slot="suffix">
        <i
          class="el-select__caret el-input__icon el-icon-arrow-down"
          v-show="!showClear"
          :class="{ true: 'is-reverse' }[isShow]"
        ></i>
        <i class="el-input__icon el-icon-circle-close el-input__clear" @click.stop="clear()" v-show="showClear"></i>
      </template>
    </el-input>
    <div>
      <el-drawer title="选择关联课程" :visible.sync="isShow" size="50%" custom-class="m-drawer" :append-to-body="true">
        <div class="drawer-bd">
          <el-row :gutter="16" class="m-query f-mt10">
            <el-col :span="10">
              <el-input v-model="pageQueryParam.name" placeholder="课程名称" clearable></el-input>
            </el-col>
            <el-col :span="4">
              <el-button type="primary" @click="doSearch()" style="margin-left: 20px">
                查询
              </el-button>
            </el-col>
          </el-row>
          <!-- <template> -->
          <el-table :data="pageList" max-height="500px" class="m-table" v-loading="query.loading">
            <el-table-column type="index" label="No." width="80" align="center"></el-table-column>
            <el-table-column prop="name" label="课程名称" min-width="300"></el-table-column>

            <el-table-column prop="period" label="学时" min-width="180"></el-table-column>
            <el-table-column fixed="right" label="操作" width="120">
              <template slot-scope="{ row }">
                <el-link type="primary" @click="select(row)">{{ isSelect(row) }}</el-link>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"></hb-pagination>
          <!-- </template> -->
        </div>
      </el-drawer>
    </div>
  </div>
</template>

<script lang="ts">
  import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import QueryCourseListParam from '@api/service/management/resource/course//query/vo/QueryCourseListParam'
  import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'

  @Component
  export default class extends Vue {
    page: UiPage

    query: Query = new Query()

    pageQueryParam: QueryCourseListParam = new QueryCourseListParam()

    pageList: Array<CourseListDetail> = new Array<CourseListDetail>()

    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }
    /**
     * 外部传过来接收课程的数据
     */
    @Prop({
      required: true
    })
    value: string

    @Prop({
      type: String,
      default: '请选择相关课程'
    })
    placeholder: string

    isShow = false
    showClear = false
    loading = false
    preliminaryId = ''
    preliminaryData = new CourseListDetail()
    courseName = '' //关联课程显示的名称

    // 初始化的情况输入框的名字
    async setName() {
      const res = await ResourceModule.courseFactory.queryCourse.queryCourseByIdList([this.value])
      // 获取课程关联ID
      if (!this.value) {
        this.courseName = ''
        return
      }
      const name = res[0].name
      this.preliminaryId = res[0].id
      this.preliminaryData = res[0]
      name && (this.courseName = name)
    }

    // 显示
    async handleShow() {
      this.isShow = true
      // await this.search()
      await this.doSearch()
    }

    // 选中的时候
    @Emit('update:value')
    select(course: CourseListDetail) {
      this.closeDrawer()
      this.courseName = course.name
      this.showClear = true
      return course.id
    }

    // 是否选中
    isSelect(course: CourseListDetail) {
      return course.id === this.value ? '取消选择' : '选择'
    }

    closeDrawer() {
      this.isShow = false
    }

    // 清除
    @Emit('update:value')
    clear(): undefined {
      this.isShow = false
      this.courseName = ''
      return undefined
    }

    async doSearch() {
      this.query.loading = true
      try {
        this.pageList = await ResourceModule.courseFactory.queryCourse.queryCoursePage(this.page, this.pageQueryParam)
        this.pageList.find(p => {
          if (p.id === this.preliminaryId) {
            this.isSelect(this.preliminaryData)
          }
        })
      } catch (e) {
        // nothing
      } finally {
        this.query.loading = false
      }
    }
  }
</script>
<style scoped>
  .course-select {
    margin-bottom: 20px;
  }

  .course-drawer-content {
    display: inline;
  }
</style>

<style>
  /* 清除浏览器弹出层选中样式 */
  /* .my-drawer:focus {
    outline: 0;
  } */
  :focus {
    outline: 0;
  }
</style>
