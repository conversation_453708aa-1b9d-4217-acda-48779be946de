import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum LearningResultEnum {
  /**
   * 未完成
   */
  un_complete = 1,
  /**
   * 已完成
   */
  complete,
  /**
   * 自主学习插入(终止)
   */
  stop,
  /**
   * 培训时间缩短执行失败
   */
  shorten_failed,

  /**
   * 管理员手动终止
   */
  stop_by_admin
}
export default class LearningResulType extends AbstractEnum<LearningResultEnum> {
  static enum = LearningResultEnum
  constructor(status?: LearningResultEnum) {
    super()
    this.current = status
    this.map.set(LearningResultEnum.un_complete, '-')
    this.map.set(LearningResultEnum.complete, '已完成')
    this.map.set(LearningResultEnum.stop, '自主学习插入')
    this.map.set(LearningResultEnum.stop_by_admin, '管理员手动终止')
    this.map.set(LearningResultEnum.shorten_failed, '培训时间缩短执行失败')
  }
}
