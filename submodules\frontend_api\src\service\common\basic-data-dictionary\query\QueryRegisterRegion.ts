import RegionTreeVo from './vo/RegionTreeVo'

import Basicdata, { RegionRequest, RegionResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { RewriteGraph } from '../../utils/RewriteGraph'
import { listChildRegionInSubProject } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage/graphql-importer'
import AssembleTree from '../../utils/AssembleTree'

/**
 *
 */
class QueryRegisterRegion {
  /**
   * 地区树
   */
  regionTree: Array<RegionTreeVo> = new Array<RegionTreeVo>()
  businessId = 'PLATFORM_BUSINESS_REGION'

  /**
   * 下级地区缓存
   */
  private subRegionCache = new Map<string, Array<RegionResponse>>()
  /**
   * 查询网校下的物理地区（可能是省、市、区县、省直单位） --- 做查询使用的数据
   * @return {ResponseStatus}
   */
  async queryRegion() {
    if (!this.regionTree.length) {
      const result: any[] = []
      const req = new RegionRequest()
      req.businessId = this.businessId
      req.code = '320000'
      req.level = 1
      const response = await Basicdata.listRegionInSubProject(req)
      result.push(response.data[0])
      const level2 = await Basicdata.listChildRegionInSubProject({
        businessId: this.businessId,
        code: response.data[0].code
      })
      const code = level2.data.map(item => {
        result.push(item)
        return item.code
      })
      const rew = new RewriteGraph(Basicdata._commonQuery, listChildRegionInSubProject)
      const param = code.map(item => {
        return {
          businessId: this.businessId,
          code: item
        }
      })
      const rewResult = await rew.request(param)
      for (const key in rewResult.data) {
        if (Object.prototype.hasOwnProperty.call(rewResult.data, key)) {
          const element = rewResult.data[key]
          result.push(...element)
        }
      }
      result.forEach(item => {
        item.children = []
      })
      const tree = new AssembleTree<RegionTreeVo>(result, 'code', 'parentCode')
      this.regionTree = tree.assembleTree()
    }

    return this.regionTree
  }

  /**
   * 根据code查下级地区
   */
  async queryLowerLevelRegion(code: string): Promise<Array<RegionResponse>> {
    if (!this.subRegionCache.get(code)) {
      const { data } = await Basicdata.listChildRegionInSubProject({
        businessId: this.businessId,
        code: code
      })
      this.subRegionCache.set(code, data)
    }

    return this.subRegionCache.get(code)
  }
  /**
   * 根据code查地区信息 - 批量
   */
  async querRegionDetil(codes: string[]): Promise<Array<RegionResponse>> {
    const response = await Basicdata.listRegionByCodeInSubProject({
      businessId: this.businessId,
      codeList: codes
    })
    return response.data
  }
}
export default new QueryRegisterRegion()
