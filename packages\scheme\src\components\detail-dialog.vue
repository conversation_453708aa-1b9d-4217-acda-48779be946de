<template>
  <el-drawer
    title="学习心得详情"
    :visible.sync="logVisible"
    :direction="direction"
    size="800px"
    custom-class="m-drawer"
    @close="closeDetail"
  >
    <template>
      <div class="drawer-bd">
        <el-form
          :model="auditForm"
          ref="auditScoreForm"
          label-width="110px"
          class="m-form f-mt10 f-mlr20"
          :rules="rules"
        >
          <el-form-item label="用户姓名：">{{ ActivityManangeDetail.name }}</el-form-item>
          <el-form-item label="培训方案名称：">{{ ActivityManangeDetail.schemeName }}</el-form-item>
          <el-form-item label="学习心得类型：">
            <div v-for="item in LearningExperienceTypeList" :key="item.code">
              <span v-if="ActivityManangeDetail.learningExperienceType === item.code">
                {{ item.desc }}
              </span>
            </div>
          </el-form-item>
          <el-form-item label="主题：">{{ ActivityManangeDetail.theme }}</el-form-item>
          <el-form-item label="内容：">
            <div v-html="ActivityManangeDetail.content"></div>
          </el-form-item>
          <el-form-item label="参加时间：">
            <template>
              <span
                v-if="
                  ActivityManangeDetail.joinStartTime == Constant.START_TIME_DEFAULT &&
                  ActivityManangeDetail.joinEndTime == Constant.END_TIME_DEFAULT
                "
                >长期有效</span
              >
              <span v-else> {{ ActivityManangeDetail.joinStartTime }}至{{ ActivityManangeDetail.joinEndTime }} </span>
            </template>
          </el-form-item>
          <el-form-item label="作答形式：">
            <div v-for="item in AnswerMethodTypeList" :key="item.code">
              <span v-if="ActivityManangeDetail.answerMethod === item.code">
                {{ item.desc }}
              </span>
            </div>
          </el-form-item>
          <el-form-item label="审核方式：">
            <div v-for="item in ApproveMethodTypeList" :key="item.code">
              <span v-if="ActivityManangeDetail.approveMethod === item.code">
                {{ item.desc }}
              </span>
            </div>
          </el-form-item>
          <el-form-item label="学习心得：">
            <!-- todo 文件和富文本判断逻辑 文件查看、下载逻辑 -->
            <div v-if="isEdit">
              <div style="white-space: pre-wrap; word-wrap: break-word">
                {{ LearningExperience }}
              </div>
              <div>
                字数要求：{{ ActivityManangeDetail.wordLimit }} 字 ，当前字数：{{ currentWordLength }}
                字
              </div>
            </div>
            <template v-else>
              <span>
                {{ ActivityManangeDetail.fileName }}
              </span>
              <!-- <el-image
              v-if="isImage"
              :src="getPath(ActivityManangeModel.ActivityManange.fileUrl)"
              :preview-src-list="[getPath(ActivityManangeModel.ActivityManange.fileUrl)]"
            >
            </el-image> -->
              <el-button v-if="isView || isImage" type="text" class="f-ml10" @click="handleView">查看</el-button>
              <el-button type="text" class="f-ml10" @click="handleDownload">下载</el-button>
            </template>
          </el-form-item>
          <el-form-item label="提交时间：">{{ ActivityManangeDetail.submitTime }}</el-form-item>
          <el-form-item label="审核状态：">
            <div v-for="item in ApproveStatusTypeList" :key="item.code">
              <span v-if="ActivityManangeDetail.status === item.code">
                {{ item.desc }}
              </span>
            </div>
          </el-form-item>
          <template v-if="ActivityManangeDetail.status === ApproveStatusEnum.SUCCESS">
            <el-form-item label="审核结果：">
              <div v-for="item in ApproveResultTypeList" :key="item.code">
                <span v-if="ActivityManangeDetail.approveResult === item.code">
                  {{ item.desc }}
                  <span>
                    （{{ ActivityManangeDetail.approveMethod == 2 ? ActivityManangeDetail.approveResultScore : 100 }}）
                  </span>
                </span>
              </div>
            </el-form-item>
            <el-form-item label="审核时间：">{{ ActivityManangeDetail.approveTime }}</el-form-item>
            <el-form-item label="审核意见：">{{ ActivityManangeDetail.approveComment }}</el-form-item>
            <!-- <el-form-item v-if="ActivityManangeDetail.approveNote.length" label="审核日志：">
            <div v-for="item in ActivityManangeDetail.approveNote" :key="item.time">
              【{{ item.time }}】 ({{ item.manager }})
              <span v-if="item.score"> 评分： {{ item.score }},审核意见：{{ item.comment }} </span>
              <span v-else-if="item.operate">
                {{ item.operate }}
              </span>
            </div>
          </el-form-item> -->
          </template>
          <template v-if="ActivityManangeDetail.status === ApproveStatusEnum.FAIL">
            <el-form-item label="审核结果：" prop="result">
              <el-input-number
                v-model="auditForm.result"
                size="small"
                class="input-num f-mr5"
                :controls="false"
                :max="100"
                :min="0"
              />
              分（总分：100分，通过分： <i class="f-cr">{{ ActivityManangeDetail.passScore }}</i> 分）
            </el-form-item>
            <el-form-item label="审核意见：" prop="comment">
              <el-input
                type="textarea"
                :rows="6"
                :maxlength="300"
                v-model="auditForm.comment"
                placeholder="不合格时需填写审核意见"
              />
            </el-form-item>
          </template>

          <el-form-item class="m-btn-bar is-sticky">
            <el-button @click="closeDetail">关闭</el-button>
            <!-- <el-button
            v-if="
              ActivityManangeDetail.status === ApproveStatusEnum.SUCCESS &&
                ActivityManangeDetail.approveMethod == ApproveMethodEnum.ARTIFICIAL
            "
            type="primary"
            @click="handleReAudit"
            >退回重审</el-button
          > -->
            <el-button
              v-if="ActivityManangeDetail.status === ApproveStatusEnum.FAIL"
              type="primary"
              @click="handleAudit"
              :loading="loading"
              >确定
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import ActivityManangeDetailModel from '@api/service/management/activity/models/ActivityManangeDetailModel'
  import ActivityManangeModel from '@api/service/management/activity/ActivityManangeModel'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import {
    LearningExperienceType,
    AnswerMethodType,
    ApproveMethodType,
    ApproveResultType,
    ApproveStatusType,
    LearningExperienceEnum,
    AnswerMethodEnum,
    ApproveMethodEnum,
    ApproveResultEnum,
    ApproveStatusEnum
  } from '@api/service/management/activity/enum/ActivityEnum'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import Constant from '@api/service/common/models/constant/index'
  import { bind, debounce } from 'lodash-decorators'

  @Component
  export default class extends Vue {
    @Ref('auditScoreForm') auditScoreForm: any
    Constant = Constant
    // 加载
    loading = false
    // 开关
    logVisible = false
    ApproveStatusEnum = ApproveStatusEnum
    ApproveMethodEnum = ApproveMethodEnum
    ApproveResultEnum = ApproveResultEnum
    //
    ActivityManangeModel: ActivityManangeModel = new ActivityManangeModel()
    // 详情
    ActivityManangeDetail: ActivityManangeDetailModel = new ActivityManangeDetailModel()
    // 学习心得类型枚举列表
    LearningExperienceTypeList: EnumOption<LearningExperienceEnum>[] = new LearningExperienceType().list()
    // 作答形式枚举列表
    AnswerMethodTypeList: EnumOption<AnswerMethodEnum>[] = new AnswerMethodType().list()
    // 审核方式枚举列表
    ApproveMethodTypeList: EnumOption<ApproveMethodEnum>[] = new ApproveMethodType().list()
    // 审核结果枚举列表
    ApproveResultTypeList: EnumOption<ApproveResultEnum>[] = new ApproveResultType().list()
    // 审核状态枚举列表
    ApproveStatusTypeList: EnumOption<ApproveStatusEnum>[] = new ApproveStatusType().list()
    rules = {
      result: [{ required: true, message: '请输入审核结果', trigger: 'blur' }],
      comment: [{ required: true, validator: this.validatorComment, trigger: 'blur' }]
    }
    auditForm = {
      result: 0,
      comment: ''
    }
    LearningExperience = ''
    // 学习心得id
    @Prop({
      type: ActivityManangeDetailModel,
      default: () => new ActivityManangeDetailModel()
    })
    learningExperienceDetail: ActivityManangeDetailModel
    // 学习心得id
    @Prop({
      type: String,
      default: () => ''
    })
    learningExperienceId: string

    @Watch('learningExperienceDetail', {
      deep: true,
      immediate: true
    })
    learningExperienceDetailChange() {
      this.ActivityManangeDetail = this.learningExperienceDetail
      this.ActivityManangeModel.ActivityManangeId = this.learningExperienceId
    }

    direction = 'rtl'
    fileType = ''

    validatorComment(rule: any, value: string, callback: any) {
      if (this.auditForm.result < this.ActivityManangeDetail.passScore && !value) {
        callback(new Error('请输入审核意见'))
      } else {
        callback()
      }
    }

    // 判断文件类型是否为图片
    get isImage() {
      const fileUrl = this.ActivityManangeDetail.fileName
      if (
        fileUrl.includes('.jpg') ||
        fileUrl.includes('.png') ||
        fileUrl.includes('.gif') ||
        fileUrl.includes('.jpeg')
      ) {
        return true
      } else {
        return false
      }
    }

    // 判断需要查看按钮
    get isView() {
      const fileUrl = this.LearningExperience

      if (fileUrl.includes('.docx') || fileUrl.includes('.pdf') || fileUrl.includes('.xls')) {
        return true
      } else {
        // 预览不了的执行下载
        return false
      }
    }

    /**
     *判断作答格式是否为在线编辑
     **/
    get isEdit() {
      if (this.ActivityManangeDetail.answerMethod == AnswerMethodEnum.EDIT) {
        return true
      } else {
        return false
      }
    }

    get currentWordLength() {
      return this.LearningExperience.replace(/\s/g, '').length
    }

    async init() {
      this.ActivityManangeDetail = this.learningExperienceDetail
      this.ActivityManangeModel.ActivityManangeId = this.learningExperienceId
      this.ActivityManangeModel.ActivityManange.LearningExperience = this.learningExperienceDetail.LearningExperience
      if (this.learningExperienceDetail.answerMethod == AnswerMethodEnum.EDIT) {
        this.LearningExperience = await this.ActivityManangeModel.getText()
      } else if (this.learningExperienceDetail.answerMethod == AnswerMethodEnum.UPLOAD) {
        const text = await this.ActivityManangeModel.getText()
        try {
          const obj = JSON.parse(text)
          this.LearningExperience = obj.fileUrl
          this.ActivityManangeDetail.fileName = obj.fileName
        } catch (error) {
          console.log(error, '获取文件 error')
        }
      }
      this.logVisible = true
    }

    // getPath(url: string) {
    //   if (!url) return ''
    //   // const fileUrl = this.ActivityManangeDetail.LearningExperience
    //   console.log(url, 'url')
    //   console.log(url.slice(0, url.indexOf('.') + 1) + this.fileType, 'require')

    //   this.fileType = url.slice(url.indexOf('.'), url.length)
    //   // return ''
    //   return require(url.slice(0, url.indexOf('.') + 1) + this.fileType)
    // }
    // 查看
    handleView() {
      // 预览页需求https 所以要远端地址
      let type = ''
      // const host = 'https://fjjsjxjy.test2.59iedu.com:9443'
      // const fileUrl = '/mfs/补贴人员花名册.jpg'
      const fileUrl = this.LearningExperience
      // const file = host + fileUrl
      // console.log(fileUrl.slice(fileUrl.indexOf('.'), fileUrl.length), 'file.slice')

      const file = location.origin + fileUrl
      if (fileUrl.includes('.docx')) {
        type = 'docs'
      } else if (fileUrl.includes('.pdf')) {
        type = 'pdf'
      } else if (fileUrl.includes('.xls')) {
        type = 'excel'
      } else {
        type = file.slice(file.indexOf('.'), file.length)
        window.open(file)
        // this.downloadFile(file, this.ActivityManangeDetail.fileName)
        return
      }

      const url = `${ConfigCenterModule.getIngress(ingress.hbfeAppHost)}/file-viewer/2.0.0/#/${type}?file=${file}`
      window.open(url)
    }

    // 下载
    handleDownload() {
      if (this.LearningExperience) {
        this.downloadFile(this.LearningExperience, this.ActivityManangeDetail.fileName)
      }
    }

    // 下载文件
    downloadFile(fileUrl: string, fileName: string) {
      const downloadDom = document.createElement('a')
      downloadDom.download = fileName
      downloadDom.href = fileUrl
      document.body.appendChild(downloadDom)
      downloadDom.click()
      document.body.removeChild(downloadDom)
    }

    // 审核
    @bind
    @debounce(200)
    async handleAudit() {
      this.loading = true
      this.auditScoreForm.validate(async (valid: boolean) => {
        if (valid) {
          // console.log(this.auditForm, 'form')
          // this.ActivityManangeModel.ActivityManange.approveResult = this.ActivityManangeDetail.approveResult
          this.ActivityManangeModel.ActivityManange.approveComment = this.auditForm.comment
          this.ActivityManangeModel.ActivityManange.approveResultScore = this.auditForm.result
          this.ActivityManangeModel.ActivityManange.learningExperienceType =
            this.ActivityManangeDetail.learningExperienceType
          const res = await this.ActivityManangeModel.doAudit()
          if (res.status.isSuccess()) {
            if (res.data.code == '500') {
              this.$message.error('审核失败！')
              this.loading = false
              return
            }
            this.$emit('handleReLoad')
          } else {
            this.$message.error('审核失败！')
          }
          this.closeDetail()
          this.loading = false
        } else {
          this.loading = false
        }
      })
    }

    closeDetail() {
      if (this.auditScoreForm) {
        try {
          this.auditScoreForm.resetFields()
        } catch (error) {
          // 清除校验结果
          console.log('清除校验')
        }
      }
      this.logVisible = false
    }
  }
</script>
