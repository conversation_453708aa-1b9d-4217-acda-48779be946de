<route-meta>
{
"isMenu": true,
"title": "登录登出日志",
"sort": 1,
"icon": "icon-zixun"
}
</route-meta>
<script lang="ts">
  import LoginLog from '@hbfe/jxjy-admin-logManagement/src/login-log/index.vue'

  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    queryData: [WXGLY]
  })
  export default class extends LoginLog {}
</script>
