//媒体样式
@import "../../../common/variables";

@media screen and (max-height: 800px) {
  .m-login-wrap {
    .wrap-bd {
      min-height: 620px;

      .logo-txt {
        padding: 40px 0 10px;
      }

      .m-login {
        width: 340px;

        .m-form {
          margin-top: 20px;
        }

        .el-form-item {
          margin-bottom: 18px;

          &.op {
            margin-bottom: 4px;
          }
        }
      }
    }

    .login-footer {
      max-height: 60px;
      padding: 10px 0;
    }
  }
}

@media only screen and (max-width: 1800px) {
  .m-school-set {
    .el-col-sm-12 {
      &:nth-child(2n) {
        border-right: 0;

        .arrow {
          display: none;
        }
      }

      &:nth-child(-n + 2) {
        .item {
          border-bottom: 1px solid #ebeef5;
        }
      }
    }
  }
}
