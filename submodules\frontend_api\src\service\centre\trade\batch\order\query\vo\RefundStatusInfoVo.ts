import { SubOrderRefundStatusEnum } from '@api/service/centre/trade/batch/order/enum/SubOrderRefundStatusList'

/**
 * @description 退款信息
 */
class RefundStatusInfoVo {
  /**
   * 退款状态
   */
  status: SubOrderRefundStatusEnum = null

  /**
   * 时间
   */
  date = ''

  /**
   * 备注信息（原因）
   */
  remark = ''

  constructor(status?: SubOrderRefundStatusEnum) {
    this.status = status
  }
}

export default RefundStatusInfoVo
