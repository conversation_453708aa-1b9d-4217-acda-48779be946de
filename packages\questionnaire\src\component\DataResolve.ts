// -----------------
//  试题相关的方法封装
// -----------------
class DataResolve {
  /**
   * 删除操作
   */
  static doQuestionDelete<T>(arr: T[], idx: number) {
    if (!arr.length) {
      console.error('没有可删除的试题！')
      return
    }
    arr?.splice(idx, 1)
    return arr
  }
  /**
   * @description: 试题上移
   * @param {Array} arr 操作的数组
   * @param {number} idx 选中项下标
   * @return {*}
   */
  static doQuestionUp<T>(arr: T[], idx: number) {
    if (arr?.length < 2) {
      console.error('当前情况不支持移动试题！')
      return
    }
    // 当前选中元素下标
    const curIdx = idx
    // 前一个元素的下标
    const preIdx = idx - 1
    const curItem = arr[curIdx]
    arr?.splice(curIdx, 1)
    arr?.splice(preIdx, 0, curItem)
    return arr
  }

  /**
   /**
   * @description: 试题下移
   * @param {Array} arr 操作的数组
   * @param {number} idx 选中项下标
   * @return {*}
   */
  static doQuestionDown<T>(arr: T[], idx: number) {
    if (arr?.length < 2) {
      console.error('当前情况不支持移动试题！')
      return
    }
    // 当前选中元素下标
    const curIdx = idx
    // 后一个
    const lastIdx = idx + 1
    const curItem = arr[curIdx]
    arr?.splice(curIdx, 1)
    arr?.splice(lastIdx, 0, curItem)
    return arr
  }
  /**
   * 移动至最前 or 最后
   */
  static moveItemToPosition<T>(arr: T[], item: T, position: string) {
    const index = arr.indexOf(item)
    if (index > -1) {
      arr.splice(index, 1) // 先将该项从数组中删除
      if (position === 'first') {
        arr.unshift(item) // 将该项添加到数组最前面
      } else if (position === 'last') {
        arr.push(item) // 将该项添加到数组最后面
      }
    }
    return arr
  }
}
export default DataResolve
