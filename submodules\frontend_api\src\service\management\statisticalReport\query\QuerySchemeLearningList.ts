import exportMsgateway, {
  LearningReportFormsRequest,
  RegionSkuPropertySearchRequest,
  SchemeSkuPropertyRequest
} from '@api/platform-gateway/diff-ms-data-export-front-gateway-DataExportBackstage'
import schemeLearningMsGateway, {
  DateScopeRequest,
  LearningRegisterRequest,
  RegionRequest,
  SchemeRequest,
  UserPropertyRequest,
  UserRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { CommoditySkuRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { CommoditySkuRequestVo } from '@api/service/management/statisticalReport/query/vo/CommoditySkuRequestVo'
import { LearningStatisticsResponse } from '@api/service/management/statisticalReport/query/vo/LearningStatisticsResponseVo'
import { SchemeLearningStatisticsResponseVo } from '@api/service/management/statisticalReport/query/vo/SchemeLearningStatisticsResponseVo'
import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
import { Page } from '@hbfe/common'
import { cloneDeep } from 'lodash'
export class QuerySchemeLearningList {
  statisticsM = new LearningStatisticsResponse()
  /**
   * 方案学习统计列表
   */

  async listSchemeLearningReportFormsInServicer(
    page: Page,
    commodityFilter: CommoditySkuRequestVo,
    vendorId = '',
    isPopularize = false
  ): Promise<Array<SchemeLearningStatisticsResponseVo>> {
    try {
      console.log('page参数=', page, 'commodityFilter参数=', commodityFilter) // filter: LearningReportFormsRequest
      const commodityFilterTmp = new CommoditySkuRequest()
      Object.assign(commodityFilterTmp, commodityFilter)
      if (commodityFilter.notDistributionPortal) {
        commodityFilterTmp.portalId = ''
      }
      const trainClassListQuery = TrainClassManagerModule.queryTrainClassFactory.getQueryTrainClassCommodityList()
      const trainClassList = await trainClassListQuery.queryTrainClassCommodityList(page, commodityFilterTmp)
      const classIdS = trainClassList.map((item) => item.schemeId)
      if (classIdS.length == 0) {
        return []
      }
      const schemeLearningFilter: LearningReportFormsRequest = this.queryFrom(
        commodityFilter,
        commodityFilterTmp,
        classIdS
      )

      let res
      let listData
      if (schemeLearningFilter.scheme?.skuProperty?.trainingWay?.includes('trainingWay0001')) {
        res = await schemeLearningMsGateway.listSchemeLearningReportFormsInServicer(schemeLearningFilter)
        listData = res.data
      } else {
        res = await schemeLearningMsGateway.pageStudentSchemeLearningStatisticsInServicer({
          page: new Page(1, page.pageSize || 200),
          request: schemeLearningFilter
        })
        listData = res.data?.currentPageData
      }
      const tmpArr = []
      if (res.status.isSuccess()) {
        for (const tmpArrElement of listData) {
          const reportVo = new SchemeLearningStatisticsResponseVo()
          Object.assign(reportVo, tmpArrElement)
          const findClass = trainClassList.find((item) => item.schemeId == reportVo.schemeId)
          reportVo.trainClassDetail = findClass
          tmpArr.push(reportVo)
        }
        const request: LearningReportFormsRequest = this.statisticsFrom(commodityFilter, schemeLearningFilter)
        await this.getStatistics(request)
      }

      console.log('调用了listSchemeLearningReportFormsInServicer方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QuerySchemeLearningList.ts所处方法，listSchemeLearningReportFormsInServicer',
        e
      )
    }
  }
  /**
   * 报表查询口入参
   */
  queryFrom(commodityFilter: CommoditySkuRequestVo, commodityFilterTmp: CommoditySkuRequest, classIdS: string[]) {
    const schemeLearningFilter = new LearningReportFormsRequest()
    schemeLearningFilter.scheme = new SchemeRequest()

    if (commodityFilter.skuPropertyRequest) {
      if (!schemeLearningFilter.scheme.skuProperty) {
        schemeLearningFilter.scheme.skuProperty = new SchemeSkuPropertyRequest()
      }
      if (commodityFilter.skuPropertyRequest?.regionSkuPropertySearch?.region?.length) {
        schemeLearningFilter.scheme.skuProperty.regionSkuPropertySearch =
          commodityFilter.skuPropertyRequest?.regionSkuPropertySearch
        schemeLearningFilter.scheme.skuProperty.regionSkuPropertySearch.regionSearchType = 1
      }
      if (commodityFilter.trainingType) {
        schemeLearningFilter.scheme.skuProperty.trainingWay = [commodityFilter.trainingType]
      }
    }
    schemeLearningFilter.learningRegister = new LearningRegisterRequest()
    if (commodityFilterTmp?.distributorId) {
      schemeLearningFilter.learningRegister.distributorId = commodityFilterTmp.distributorId
    }
    if (commodityFilterTmp?.portalId) {
      schemeLearningFilter.learningRegister.portalId = commodityFilterTmp.portalId
    }

    schemeLearningFilter.scheme.schemeIdList = classIdS
    schemeLearningFilter.learningRegister.registerTime = commodityFilter.registerTime
    schemeLearningFilter.saleChannels = commodityFilter.saleChannel
    schemeLearningFilter.trainingChannelName = commodityFilter.trainingChannelName
    schemeLearningFilter.notDistributionPortal = commodityFilter.notDistributionPortal
    if (schemeLearningFilter?.notDistributionPortal) {
      schemeLearningFilter.learningRegister.portalId = ''
    }
    schemeLearningFilter.trainingType = commodityFilter.trainingType
    return schemeLearningFilter
  }

  /**
   * 报表统计口入参
   */
  statisticsFrom(commodityFilter: CommoditySkuRequestVo, schemeLearningFilter: LearningReportFormsRequest) {
    const request = new LearningReportFormsRequest()
    request.scheme = new SchemeRequest()
    request.student = new UserRequest()
    request.student.userProperty = new UserPropertyRequest()
    request.student.userProperty.regionList = new Array<RegionRequest>()
    request.scheme.skuProperty = new SchemeSkuPropertyRequest()
    request.scheme.skuProperty = cloneDeep(commodityFilter.skuPropertyRequest) || new SchemeSkuPropertyRequest()
    request.scheme.skuProperty.year = commodityFilter.skuPropertyRequest.year
    request.scheme.skuProperty.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
    request.scheme.skuProperty.regionSkuPropertySearch = commodityFilter.skuPropertyRequest.regionSkuPropertySearch
    request.scheme.skuProperty.regionSkuPropertySearch.regionSearchType = 1
    if (schemeLearningFilter?.student?.userProperty?.regionList?.length) {
      request.student.userProperty.regionList = cloneDeep(schemeLearningFilter.student.userProperty.regionList)
    }
    request.scheme.skuProperty.discipline = commodityFilter.skuPropertyRequest.discipline
    request.scheme.skuProperty.learningPhase = commodityFilter.skuPropertyRequest.learningPhase
    request.scheme.schemeName = commodityFilter.saleTitleMatchLike
    request.scheme.schemeType = commodityFilter.schemeRequest.schemeType
    if (commodityFilter.trainingType) {
      request.scheme.skuProperty.trainingWay = [commodityFilter.trainingType]
    }
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.registerTime = new DateScopeRequest()
    request.learningRegister.registerTime.begin = commodityFilter.registerTime.begin
    request.learningRegister.registerTime.end = commodityFilter.registerTime.end
    request.saleChannels = commodityFilter.saleChannel
    request.trainingChannelName = commodityFilter.trainingChannelName
    request.learningRegister.distributorId = commodityFilter.distributorId
    request.learningRegister.portalId = commodityFilter.portalId
    request.notDistributionPortal = commodityFilter.notDistributionPortal
    request.trainingType = commodityFilter.trainingType
    if (request.notDistributionPortal) {
      request.learningRegister.portalId = ''
    }
    return request
  }

  /**
   * 导出
   */

  async exportExcel(commodityFilter: CommoditySkuRequestVo) {
    try {
      console.log('commodityFilter参数=', commodityFilter)
      const schemeLearningFilter: LearningReportFormsRequest = this.exportfrom(commodityFilter)
      let res
      if (commodityFilter.trainingType == 'trainingWay0001') {
        res = this.exportExcelTrainingChannel(schemeLearningFilter)
      } else if (commodityFilter.trainingType == 'trainingWay0002') {
        res = await exportMsgateway.exportBlendedSchemeLearningReportFormsExcelInServicer(schemeLearningFilter)
      } else {
        res = await exportMsgateway.exportFaceToFaceSchemeLearningReportFormsExcelInServicer(schemeLearningFilter)
      }
      console.log('调用了exportExcel方法，返回值=', res)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QuerySchemeLearningList.ts所处方法，exportExcel',
        e
      )
    }
  }

  async exportExcelTrainingChannel(schemeLearningFilter: LearningReportFormsRequest) {
    return await exportMsgateway.exportSchemeLearningReportFormsExcelInServicer(schemeLearningFilter)
  }

  /**
   * 导出明细
   */

  async exportExcelDetail(commodityFilter: CommoditySkuRequestVo) {
    try {
      console.log('commodityFilter参数=', commodityFilter)

      const schemeLearningFilter: LearningReportFormsRequest = this.exportfrom(commodityFilter)
      schemeLearningFilter.learningRegister.status = [1, 2]
      let res
      if (commodityFilter.trainingType == 'trainingWay0001') {
        res = this.exportSchemeLearningReportFormsDetailExcelInServicer(schemeLearningFilter)
      } else if (commodityFilter.trainingType == 'trainingWay0002') {
        res = await exportMsgateway.exportBlendedSchemeLearningReportFormsDetailExcelInServicer(schemeLearningFilter)
      } else {
        res = await exportMsgateway.exportFaceToFaceSchemeLearningReportFormsDetailExcelInServicer(schemeLearningFilter)
      }
      console.log('调用了exportExcel方法，返回值=', res)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QuerySchemeLearningList.ts所处方法，exportExcel',
        e
      )
    }
  }

  async exportSchemeLearningReportFormsDetailExcelInServicer(schemeLearningFilter: LearningReportFormsRequest) {
    return await exportMsgateway.exportSchemeLearningReportFormsDetailExcelInServicer(schemeLearningFilter)
  }

  /**
   * 报表导出口入参
   */
  exportfrom(commodityFilter: CommoditySkuRequestVo) {
    const schemeLearningFilter = new LearningReportFormsRequest()
    schemeLearningFilter.scheme = new SchemeRequest()
    schemeLearningFilter.learningRegister = new LearningRegisterRequest()
    schemeLearningFilter.scheme.skuProperty = commodityFilter.skuPropertyRequest
    schemeLearningFilter.scheme.schemeType = commodityFilter.schemeRequest.schemeType
    schemeLearningFilter.scheme.schemeName = commodityFilter.saleTitleMatchLike
    schemeLearningFilter.learningRegister.registerTime = commodityFilter.registerTime
    schemeLearningFilter.scheme.schemeIdList = commodityFilter.commoditySkuIdList
    schemeLearningFilter.saleChannels = commodityFilter.saleChannel
    schemeLearningFilter.trainingChannelName = commodityFilter.trainingChannelName
    schemeLearningFilter.learningRegister.distributorId = commodityFilter.distributorId
    schemeLearningFilter.learningRegister.portalId = commodityFilter.portalId
    schemeLearningFilter.notDistributionPortal = commodityFilter.notDistributionPortal
    schemeLearningFilter.trainingType = commodityFilter.trainingType
    if (schemeLearningFilter.notDistributionPortal) {
      schemeLearningFilter.learningRegister.portalId = ''
    }
    return schemeLearningFilter
  }

  /**
   * 获取报表统计合计数据
   */

  async getStatistics(filter: LearningReportFormsRequest) {
    try {
      console.log('getStatistics filter参数=', filter)
      let res
      if (filter?.scheme?.skuProperty?.trainingWay?.includes('trainingWay0001')) {
        res = await schemeLearningMsGateway.getSchemeLearningReportSummeryInServicer(filter)
      } else {
        res = await schemeLearningMsGateway.getStudentSchemeLearningTotalStatisticsInServicer(filter)
      }
      if (res.status.isSuccess()) {
        this.statisticsM = res.data
      }
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QuerySchemeLearningList.ts所处方法，getStatistics',
        e
      )
    }
  }
}
