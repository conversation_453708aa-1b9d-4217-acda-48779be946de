import { StudentInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import RegionModelVo from './RegionModelVo'

class StudentUserInfoVo extends StudentInfoResponse {
  /**
   * 用户昵称
   */
  nickName = ''
  /**
   * 单位所属地区
   */
  region: RegionModelVo = new RegionModelVo()
  /**
   * 工作单位名称
   */
  companyName = ''
  /**
   * 工作单位统一社会信用代码
   */
  companyCode = ''
  /**
   * 头像地址
   */
  photo = ''
  /**
   * 联系地址
   */
  address = ''
  /**
   * 是否绑定微信
   */
  bindWX: boolean = null
  /**
   * 微信昵称
   */
  nickNameByWX = ''
  /**
   * 用户id
   */
  userId = ''
  /**
   * 用户名称
   */
  userName = ''
  /**
   * 证件号
   */
  idCard = ''

  /**
   * 证件类型
   */
  idCardType = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 注册时间
   */
  createTime = ''
  /**
   * 邮箱
   */
  email = ''

  from(item: StudentInfoResponse) {
    this.nickName = item.userInfo?.nickName
    this.region = item.userInfo?.region
    this.companyName = item.userInfo?.companyName
    this.companyCode = item.userInfo?.companyCode
    this.photo = item.userInfo?.photo
    this.address = item.userInfo?.address
    this.bindWX = item.openPlatformBind?.bindWX
    this.nickNameByWX = item.openPlatformBind?.nickNameByWX
    this.userId = item.userInfo?.userId
    this.userName = item.userInfo?.userName
    this.idCard = item.userInfo?.idCard
    this.idCardType = item.userInfo?.idCardType
    this.phone = item.userInfo?.phone
    this.createTime = item.accountInfo?.createdTime
    this.email = item.userInfo?.email
  }
}

export default StudentUserInfoVo
