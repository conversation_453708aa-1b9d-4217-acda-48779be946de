<route-meta>
{ "isMenu": true, "title": "课程包管理", "sort": 1, "icon": "icon-dabao" }
</route-meta>
<template>
  <el-main>
    <div class="f-p15" v-if="$hasPermission('query')" desc="查询" actions="doQueryPage">
      <div class="f-mb15">
        <template desc="创建" v-if="$hasPermission('create')" actions="@hbfe/jxjy-admin-coursePackage/src/create.vue">
          <el-button type="primary" icon="el-icon-plus" @click="create">新建课程包</el-button>
        </template>
        <template desc="导入" v-if="$hasPermission('import')" actions="@hbfe/jxjy-admin-coursePackage/src/import.vue">
          <el-button type="primary" @click="importPackage" plain>导入课程包</el-button>
        </template>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <hb-search-wrapper @reset="resetParam" class="m-query is-border-bottom">
          <el-form-item label="课程包名称">
            <el-input clearable v-model="QueryCoursePackageListVo.name" placeholder="请输入课程包名称" />
          </el-form-item>
          <el-form-item label="方案使用情况">
            <el-select clearable placeholder="是否被方案使用" v-model="QueryCoursePackageListVo.isBeingUsedByScheme">
              <el-option :value="true" label="是"></el-option>
              <el-option :value="false" label="否"></el-option>
            </el-select>
          </el-form-item>
          <template slot="actions">
            <template v-if="$hasPermission('query')" desc="查询" actions="clickQuery">
              <el-button type="primary" @click="clickQuery">查询</el-button>
            </template>
          </template>
        </hb-search-wrapper>
        <!--表格-->
        <el-table stripe :data="pageData" max-height="500px" class="m-table" v-loading="uiConfig.loadStatus.loadPage">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="课程包名称" min-width="280" fixed="left">
            <template slot-scope="scope"
              ><span>{{ scope.row.name }}</span></template
            >
          </el-table-column>
          <el-table-column label="课程数量" prop="courseCount" min-width="120" align="center"></el-table-column>
          <el-table-column label="总学时" prop="totalPeriod" min-width="120" align="center"></el-table-column>
          <el-table-column
            label="是否被方案使用"
            prop="isBeingUsedByScheme"
            min-width="140"
            align="center"
            header-align="center"
          >
            <template slot-scope="scope">
              {{ scope.row.isBeingUsedByScheme ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column label="最新修改时间" prop="updateTime" min-width="160" align="center" header-align="center"
            ><template slot-scope="scope">
              {{ scope.row.updateTime || scope.row.creatTime }}
            </template></el-table-column
          >
          <el-table-column label="操作" width="320" align="center" fixed="right">
            <template slot-scope="scope">
              <template
                v-if="$hasPermission('detail')"
                desc="详情"
                actions="@hbfe/jxjy-admin-coursePackage/src/detail.vue"
              >
                <el-button type="text" size="mini" @click="goDetail(scope.row.id)">详情</el-button>
              </template>
              <template v-if="$hasPermission('copy')" desc="复制" actions="saveCopy">
                <el-button type="text" size="mini" @click="openCopyPool(scope.row)">复制</el-button>
              </template>
              <template
                v-if="$hasPermission('async')"
                desc="同步方案"
                actions="@hbfe/jxjy-admin-coursePackage/src/sync-scheme.vue"
              >
                <el-button type="text" size="mini" @click="syncScheme(scope.row.id)">同步方案</el-button>
              </template>
              <template
                v-if="$hasPermission('modify')"
                desc="修改"
                actions="@hbfe/jxjy-admin-coursePackage/src/modify.vue"
              >
                <el-button type="text" size="mini" @click="editInfo(scope.row.id)">修改</el-button>
              </template>

              <!-- <el-button type="text" size="mini" @click="doDeleteBefore">删除</el-button> -->
              <template v-if="$hasPermission('remove')" desc="删除" actions="deleteCoursePool">
                <hb-popconfirm
                  placement="top"
                  title="删除后课程包需重新创建，是否确认删除？"
                  @confirm="deleteCoursePool(scope.row.id)"
                >
                  <el-button type="text" slot="reference" size="mini">删除</el-button>
                </hb-popconfirm>
              </template>
              <!-- <template v-if="$hasPermission('modify-daily')" desc="修改日志" actions="modifyLog">
                <el-button type="text" size="mini" @click="modifyLog(scope.row.id)">修改日志</el-button>
              </template> -->
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <!-- <hb-pagination
          :total-size="400"
          :page="page"
          class="f-mt15 f-tr"
          @size-change="pageSizeChange"
          @current-change="currentPageChange"
        >
        </hb-pagination> -->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </el-card>
      <!--复制课程包-->

      <el-drawer title="复制课程包" :visible.sync="uiConfig.copyPoolDialog" size="600px" custom-class="m-drawer">
        <div class="drawer-bd">
          <el-alert type="warning" :closable="false" class="m-alert f-mb20">
            {{ this.copyPoolTips }}
          </el-alert>
          <el-form ref="copyForm" :rules="copyRules" :model="copyPoolParam" label-width="auto" class="m-form f-mt30">
            <el-form-item label="课程包名称：" required>
              <el-input v-model="copyPoolParam.newName" :style="{ width: '100%' }" placeholder="请输入" />
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="saveCopy(false)">取消</el-button>
              <el-button type="primary" @click="saveCopy(true)">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </el-drawer>

      <!--    删除提示-->
      <el-dialog
        title="删除确认"
        :visible.sync="uiConfig.deleteDialog"
        :lock-scroll="true"
        :append-to-body="true"
        width="30%"
      >
        <span>删除后课程包需重新创建，是否确认删除？</span>
        <span slot="footer" class="dialog-footer">
          <el-button @click="uiConfig.deleteDialog = false">取 消</el-button>
          <el-button
            type="primary"
            :disabled="uiConfig.doDeleteConfirmButton"
            @click="deleteCoursePool(currentDeleteId)"
          >
            确 定
          </el-button>
        </span>
      </el-dialog>
      <el-drawer title="日志详情" :visible.sync="uiConfig.logDetailDialog" size="800px" custom-class="m-drawer">
        <div class="drawer-bd f-mt20 f-mlr40">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in logDetailList" :key="index">
              <p class="f-mb10 f-fb f-f15">
                {{ item.logTime }} <span class="f-ml30">{{ item.creator.name }}</span>
              </p>
              <div class="f-mt5" v-for="(itm, idx) in item.message" :key="idx">
                <!-- <p>{{ item.message.length }}</p> -->
                <span v-if="item.message.length <= 2">{{ itm }}</span>

                <span v-if="item.message.length > 2 && !item.reallyOpen">{{ item.message[0] }}</span>
                <span v-if="item.message.length > 2 && !item.reallyOpen">{{ item.message[1] }}</span>
                <span v-if="item.message.length > 2 && item.reallyOpen">{{ itm }}</span>
              </div>

              <el-button
                type="text"
                v-show="item.message.length > 2 && !item.reallyOpen"
                @click="item.reallyOpen = true"
                >收起更多 -
              </el-button>
              <el-button
                type="text"
                v-show="item.message.length > 2 && item.reallyOpen"
                @click="item.reallyOpen = false"
                >查看更多 +
              </el-button>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-drawer>
    </div>
  </el-main>
</template>

<script lang="ts">
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import BizCoursePackage from '@api/service/management/resource/course-package/mutation/MutationBizCoursePackage'
  import CoursePackageDetailVo from '@api/service/management/resource/course-package/query/vo/CoursePackageDetailVo'
  import QueryCoursePackageListVo from '@api/service/management/resource/course-package/query/vo/QueryCoursePackageListVo'
  import { UiPage } from '@hbfe/common'
  import CopyPoolParam from '@hbfe/jxjy-admin-coursePackage/src/models/CopyPoolParam'
  import NewCoursePackageOperationLog from '@hbfe/jxjy-admin-coursePackage/src/models/NewCoursePackageOperationLog'
  import { Component, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    // ui控制变量
    uiConfig = {
      loadStatus: { loadPage: true },
      copyPoolDialog: false,
      deletePopover: false,
      deleteDialog: false,
      doDeleteConfirmButton: false,
      logDetailDialog: false
    }

    page: UiPage
    bizCoursePackage: BizCoursePackage
    pageData = new Array<CoursePackageDetailVo>()
    copyPoolParam = new CopyPoolParam()
    QueryCoursePackageListVo: QueryCoursePackageListVo = new QueryCoursePackageListVo()

    refName = 'copyForm'
    copyRules = {
      newName: [
        {
          required: true,
          message: '课程包名称必填！',
          trigger: 'blur'
        },
        {
          validator: this.checkPooName,
          trigger: 'blur'
        }
      ]
    }
    totalSize = 0
    unitId = ''
    currentDeleteId = ''
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    editList = [
      { id: 'a', label: '修改课程包a' },
      { id: 'B', label: '修改课程包B' }
    ]
    detailList = [
      { id: '122', label: '课程包122详情' },
      { id: 'B12', label: '课程包B12详情' }
    ]
    logDetailList = new Array<NewCoursePackageOperationLog>()
    mutationFaction = ResourceModule.coursePackageFactory

    get copyPoolTips() {
      return (
        '您正在复制【' +
        this.copyPoolParam.name +
        '】 ，课程包内含' +
        this.copyPoolParam.courseCount +
        '门课程 ，合计' +
        this.copyPoolParam.totalPeriod +
        '学时！'
      )
    }

    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }

    create() {
      this.$router.push('/training/course-package/create')
    }

    editInfo(id: string) {
      this.$router.push('/training/course-package/modify/' + id)
    }

    goDetail(id: string) {
      this.$router.push('/training/course-package/detail/' + id)
    }

    importPackage() {
      this.$router.push('/training/course-package/import')
    }

    syncScheme(id: string) {
      this.$router.push('/training/course-package/sync-scheme/' + id)
    }

    async checkPooName(rule: any, value: any, callback: any) {
      // const status = await CoursePoolModule.checkName({ name: value, id: '' })
      // if (status.isSuccess()) {
      //   if (this.poolNameCheck) {
      //     callback(new Error('课程包名称重复'))
      //   } else {
      //     callback()
      //   }
      // } else {
      //   callback(new Error('查询课程包名称失败'))
      // }
      console.log(rule, value, callback)
    }

    pageSizeChange(val: number) {
      this.page.pageSize = val
      this.page.pageNo = 1
      this.doQueryPage()
    }

    currentPageChange(val: number) {
      this.page.pageNo = val
      this.doQueryPage()
    }

    clickQuery() {
      this.page.pageNo = 1
      this.doQueryPage()
    }

    async doQueryPage() {
      this.uiConfig.loadStatus.loadPage = true
      this.pageData = await this.mutationFaction.queryCoursePackage.pageCoursePackage(
        this.page,
        this.QueryCoursePackageListVo
      )
      this.uiConfig.loadStatus.loadPage = false
    }

    // async created() {
    //   await this.doQueryPage()
    // }
    async activated() {
      await this.doQueryPage()
    }
    openCopyPool(coursePool: CoursePackageDetailVo) {
      const el: any = this.$refs[this.refName]
      if (el) {
        el.resetFields()
      }
      this.copyPoolParam.id = coursePool.id
      this.copyPoolParam.name = coursePool.name
      this.copyPoolParam.newName = '【复制】' + coursePool.name
      this.copyPoolParam.courseCount = coursePool.courseCount
      this.copyPoolParam.totalPeriod = coursePool.totalPeriod
      this.bizCoursePackage = this.mutationFaction.bizCoursePackage(this.copyPoolParam.id)
      this.uiConfig.copyPoolDialog = true
    }

    saveCopy(operation: boolean) {
      if (operation) {
        const el: any = this.$refs[this.refName]
        el.validate(async (valid: any) => {
          if (valid) {
            const res = await this.bizCoursePackage.doCopy(this.copyPoolParam.id, this.copyPoolParam.newName)
            if (res.isSuccess()) {
              this.uiConfig.copyPoolDialog = false

              this.$message.success('复制成功')
              // setTimeout(() => {
              //   this.$nextTick(() => {
              await this.doQueryPage()
              //   })
              // }, 1000)
            } else {
              this.$message.error('复制失败')
            }
            // const status = await CoursePoolModule.copyCoursePool({
            //   id: this.copyPoolParam.id,
            //   newName: this.copyPoolParam.newName
            // })
            // this.uiConfig.copyPoolDialog = false

            // if (status.code === 200) {
            //   this.$message.success('复制成功')
            //   setTimeout(() => {
            //     this.doQueryPage()
            //   }, 500)
            // } else {
            //   this.$message.error('复制失败! ' + status.errors[0].message)
            // }
          }
        })
      } else {
        this.uiConfig.copyPoolDialog = false
      }
    }

    doDeleteBefore(coursePoolId: string) {
      // this.currentDeleteId = coursePoolId
      this.uiConfig.deleteDialog = true
    }

    // 重置按钮
    resetParam() {
      this.QueryCoursePackageListVo = new QueryCoursePackageListVo()
      this.clickQuery()
    }

    async deleteCoursePool(coursePoolId: string) {
      this.bizCoursePackage = this.mutationFaction.bizCoursePackage(coursePoolId)

      this.uiConfig.doDeleteConfirmButton = true
      // const status = await CoursePoolModule.deleteCoursePool(coursePoolId)
      // if (status.code === 200) {
      //   this.resetParam()
      //   this.$message.success('删除成功')
      // } else {
      //   this.$message.error('删除失败，' + status.errors[0].message)
      // }
      const res = await this.bizCoursePackage.doRemove(coursePoolId)
      if (res.isSuccess()) {
        this.resetParam()
        this.$message.success('删除成功')
        // setTimeout(() => {
        // this.$nextTick(() => {
        await this.doQueryPage()
        //   })
        // }, 1000)
      } else {
        this.$message.error('删除失败，课程包已被使用')
      }
      // setTimeout(() => {
      await this.doQueryPage()
      // }, 500)
      this.uiConfig.deleteDialog = false
      this.uiConfig.doDeleteConfirmButton = false
      //}
    }

    /**
     * 修改日志
     */

    async modifyLog(coursePoolId: string) {
      this.uiConfig.logDetailDialog = true
      // await CoursePoolModule.loadCourseInPools(coursePoolId)
      const res = await this.mutationFaction.queryCoursePackage.queryCoursePackageOperationLogList()
      // Object.assign(this.logDetailList, res)
      console.log(this.logDetailList, ' this.logDetailList')
      res.forEach((item: any) => {
        const option = new NewCoursePackageOperationLog()
        Object.assign(option, item)
        option.reallyOpen = false
        this.logDetailList.push(option)
      })
      console.log(this.logDetailList, 'NewCoursePackageOperationLogNewCoursePackageOperationLog')
    }

    clickReallyChoose(item: NewCoursePackageOperationLog) {
      item.reallyOpen = true
    }
  }
</script>
<style scoped></style>
