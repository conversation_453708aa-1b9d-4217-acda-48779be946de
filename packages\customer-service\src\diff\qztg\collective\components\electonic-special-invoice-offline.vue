<script lang="ts">
  import { Component } from 'vue-property-decorator'
  import EditElectronicSpecialOfflineInvoiceDialog from '@hbfe/jxjy-admin-trade/src/diff/qztg/invoice/collective/components/edit-electronic-special-invoice-dialog.vue'
  import ElectronicSpecialInvoiceOffline from '@hbfe/jxjy-admin-customerService/src/collective/components/electonic-special-invoice-offline.vue'

  @Component({
    components: { EditElectronicSpecialOfflineInvoiceDialog }
  })
  export default class extends ElectronicSpecialInvoiceOffline {}
</script>
