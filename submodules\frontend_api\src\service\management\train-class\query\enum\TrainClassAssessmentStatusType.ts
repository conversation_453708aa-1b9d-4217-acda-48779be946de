import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 培训班考核情况枚举
 * 1：不合格
 * 2：待考核
 * 3：已合格
 */
export enum TrainClassAssessmentStatusEnum {
  Unqualified = 1,
  For_Inspection = 2,
  Qualified = 3
}

class TrainClassAssessmentStatusType extends AbstractEnum<TrainClassAssessmentStatusEnum> {
  static enum = TrainClassAssessmentStatusEnum
  constructor(status?: TrainClassAssessmentStatusEnum) {
    super()
    this.current = status
    this.map.set(TrainClassAssessmentStatusEnum.Unqualified, '不合格')
    this.map.set(TrainClassAssessmentStatusEnum.For_Inspection, '待考核')
    this.map.set(TrainClassAssessmentStatusEnum.Qualified, '已合格')
  }
}

export default new TrainClassAssessmentStatusType()
