#!/usr/bin/env node
/**
 * merge-diff-security.js
 *
 * 直接使用 Node 运行：
 *   TARGET=WXGLY FLAG=byzj,fjzj node merge-diff-security.js
 *
 * - TARGET：通用安全对象名（不带 -ui-assigns.json 后缀）
 * - FLAG  ：逗号分隔的差异化网校标识列表
 *
 * 产物写入 .cache/merged-ui-assigns.json
 */
'use strict';
const fs = require('fs').promises;
const path = require('path');

const CACHE_DIR = path.resolve(process.cwd(), '.cache');
const TARGET = process.env.TARGET;
const FLAGS = (process.env.FLAG || '').split(',').filter(Boolean);

if (!TARGET || FLAGS.length === 0) {
  console.error('Usage: TARGET=<common> FLAG=<a,b,c> node merge-diff-security.js');
  process.exit(1);
}
const safeJson = name => path.join(CACHE_DIR, `${path.basename(name)}-ui-assigns.json`);

const readJson = async f => JSON.parse(await fs.readFile(f, 'utf8'));
const writeJson = async (f, d) => fs.writeFile(f, JSON.stringify(d, null, 2));

const uniq = (a = [], b = []) => [...new Set([...a, ...b])];
const idOf = node => {
  const og = node?.meta?.ownerGroup;
  return Array.isArray(og) && og.length ? og.slice().sort().join(',') : node.name;
};

// ----- 标记通用来源 -----
function tagCommon (node) {
  if (!node || typeof node !== 'object') return;
  node.meta = node.meta || {};
  node.meta.__fromCommon = true;
  const pm = node.meta.permissionMap || {};
  Object.values(pm).forEach(op => (op.__fromCommon = true));
  (node.children || []).forEach(tagCommon);
}

// ----- permissionMap 合并 -----
function mergePM (base = {}, extra = {}) {
  const res = { ...base };
  for (const [k, v] of Object.entries(extra)) {
    if (!res[k]) {
      res[k] = JSON.parse(JSON.stringify(v));
      continue;
    }
    const bop = res[k];
    bop.roles = uniq(bop.roles, v.roles);
    bop.graphql = uniq(bop.graphql, v.graphql);
    const bext = bop.ext ||= {};
    const vext = v.ext || {};
    bext.diffSchool = uniq(bext.diffSchool, vext.diffSchool);
    if (bop.__fromCommon) bext.isCommon = true;
  }
  return res;
}

// ----- 节点合并 -----
function mergeNode (a, b) {
  const m = { ...a };
  m.meta ||= {};
  m.meta.roles = uniq(m.meta.roles, b.meta?.roles);
  m.meta.diffSchool = uniq(m.meta.diffSchool, b.meta?.diffSchool);
  m.meta.permissionMap = mergePM(m.meta.permissionMap, b.meta?.permissionMap);
  if (m.meta.__fromCommon) m.meta.isCommon = true;

  const cmap = new Map((m.children || []).map(c => [idOf(c), c]));
  (b.children || []).forEach(c => {
    const key = idOf(c);
    cmap.set(key, cmap.has(key) ? mergeNode(cmap.get(key), c) : JSON.parse(JSON.stringify(c)));
  });
  m.children = [...cmap.values()];
  return m;
}

// 顶层数组合并
function mergeArr (baseArr, addArr) {
  const m = new Map(baseArr.map(n => [idOf(n), n]));
  addArr.forEach(n => {
    const key = idOf(n);
    m.set(key, m.has(key) ? mergeNode(m.get(key), n) : JSON.parse(JSON.stringify(n)));
  });
  return [...m.values()];
}

// 清理
function clean (n) {
  if (!n || typeof n !== 'object') return;
  delete n.meta?.__fromCommon;
  if (n.meta?.permissionMap) {
    Object.values(n.meta.permissionMap).forEach(op => delete op.__fromCommon);
  }
  (n.children || []).forEach(clean);
  if (n.children?.length === 0) delete n.children;
  if (n.meta && Object.keys(n.meta).length === 0) delete n.meta;
}

(async () => {
  try {
    let merged = await readJson(safeJson(TARGET));
    merged.forEach(tagCommon);

    for (const flag of FLAGS) {
      const diff = await readJson(safeJson(flag));
      merged = mergeArr(merged, diff);
    }
    merged.forEach(clean);
    await writeJson(path.join(CACHE_DIR, 'merged-ui-assigns.json'), merged);
    console.log('✅  merged-ui-assigns.json generated');
  } catch (e) {
    console.error(e);
    process.exit(1);
  }
})();

