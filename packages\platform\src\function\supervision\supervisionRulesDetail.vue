<route-meta>
{
"title": "监管规则配置"
}
</route-meta>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/basic-data/platform/function' }">培训监管管理</el-breadcrumb-item>
      <!-- TODO 调整面包屑动态名称 -->
      <el-breadcrumb-item>{{ title }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">监管规则名称</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" label-width="auto" class="m-form">
              <el-form-item label="监管规则名称：" required>
                <el-input
                  v-model="antiCheatConfig.antiCheatConfig.antiConfigName"
                  placeholder="请输入监管规则名称"
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">监管方案</span>
        </div>
        <el-button type="primary" class="f-mb20" @click="openSelectSchemeDialog">选择方案</el-button>
        <el-table
          stripe
          :data="schemeList"
          empty-text="请选择监管方案"
          max-height="500px"
          class="m-table"
          ref="schemeList"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="培训方案名称" min-width="240">
            <template slot-scope="scope">{{ scope.row.schemeName }}</template>
          </el-table-column>
          <el-table-column label="方案属性" min-width="200">
            <template slot-scope="scope">
              <p v-if="getSkuPropertyName(scope.row, 'industry')">
                行业：{{ getSkuPropertyName(scope.row, 'industry') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'region')">地区：{{ getSkuPropertyName(scope.row, 'region') }}</p>
              <p v-if="getSkuPropertyName(scope.row, 'technicalGrade')">
                技术等级：{{ getSkuPropertyName(scope.row, 'technicalGrade') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
                科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
              </p>
              <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
                培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
                培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'year')">培训年度：{{ getSkuPropertyName(scope.row, 'year') }}</p>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="remove(scope.row)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!-- 添加培训方案抽屉 -->
        <training-scheme-select
          ref="addTrainSchemeRef"
          :scheme-list="schemeList"
          @isCheckIdList="getNewSchemeList"
        ></training-scheme-select>
      </el-card>

      <!-- 学习监管规则 -->
      <learning-supervision
        ref="learningSupervision"
        :is-open-face-verify="baseConfig.baseConfig.datumConfig.enable"
        :antiCheatConfigProp.sync="antiCheatConfig"
      />

      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="cancel">取消</el-button>
        <el-button type="primary" :loading="isLoading" @click="saveAntiConfig">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import trainingSchemeSelect from '@hbfe/jxjy-admin-platform/src/function/supervision/components/training-scheme-select.vue'
  import AntiCheatConfig from '@hbfe-biz/biz-anticheat/dist/config/AntiCheatConfig'
  import { RangeTypeEnum } from '@hbfe-biz/biz-anticheat/dist/config/enums/VerifyConfigModeEnum'
  import AntiSchemeItem from '@api/service/management/train-class/query/vo/AntiSchemeItem'
  import { cloneDeep } from 'lodash'
  import AntiSchemeParams from '@api/service/management/train-class/query/vo/AntiSchemeParams'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import { UiPage } from '@hbfe/common'
  import Page from '@hbfe/jxjy-admin-common/src/models/Page'
  import LearningSupervision from '@hbfe/jxjy-admin-platform/src/function/supervision/components/learning-supervision.vue'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
  import { ElTable } from 'element-ui/types/table'
  import { EnterVerifyMode } from '@hbfe-biz/biz-anticheat/src/config/enums/VerifyConfigModeEnum'
  import BaseConfig from '@hbfe-biz/biz-anticheat/dist/config/BaseConfig'
  import { VerifyTypeEnum } from '@hbfe-biz/biz-anticheat/dist/config/enums/VerifyType'

  @Component({
    components: { LearningSupervision, trainingSchemeSelect }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.querySchemeList, this.querySchemeList)
    }

    @Ref('addTrainSchemeRef') addTrainSchemeRef: trainingSchemeSelect

    @Ref('learningSupervision')
    learningSupervision: LearningSupervision

    antiCheatConfig = new AntiCheatConfig()

    /**
     * 当前监管方案列表
     */
    schemeList = new Array<AntiSchemeItem>()

    /**
     * 查询参数
     */
    queryParams = new AntiSchemeParams()
    /**
     * 查询方案列表方法
     */
    queryTrainClassCommodityList = new QueryTrainClassCommodityList()

    /**
     * 加载中
     */
    isLoading = false

    /**
     * 分页
     */
    page: UiPage

    /**
     * 查询监管配置page
     */
    newPage = new Page()

    /**
     * 基础信息配置
     */
    baseConfig = new BaseConfig()

    get title() {
      return this.$route.params.id && this.$route.params.id !== ':id' ? '编辑方案级监管规则' : '新建方案级监管规则'
    }

    /**
     * 打开培训方案抽屉
     */
    openSelectSchemeDialog() {
      this.addTrainSchemeRef.init()
    }

    /**
     * 删除选中方案
     * @param item
     */
    remove(item: AntiSchemeItem) {
      const index = this.schemeList.indexOf(item)
      if (index > -1) {
        this.schemeList.splice(index, 1)
      }
      ;(this.$refs['schemeList'] as ElTable)?.doLayout()
    }

    /**
     * 获取选中的方案
     * @param newSchemeItemList
     */
    getNewSchemeList(newSchemeItemList: Array<AntiSchemeItem>) {
      this.schemeList = cloneDeep(newSchemeItemList)
      ;(this.$refs['schemeList'] as ElTable)?.doLayout()
    }

    /**
     * 获取方案列表
     */
    async querySchemeList() {
      this.queryParams.schemeIds = this.antiCheatConfig.antiCheatConfig.schemaIds
      this.page.pageSize = 200
      this.schemeList = await this.queryTrainClassCommodityList.pageAntiSchemeList(this.page, this.queryParams)
      ;(this.$refs['schemeList'] as ElTable)?.doLayout()
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: AntiSchemeItem, type: keyof SkuPropertyResponseVo): string {
      return row.sku[type]?.skuPropertyName ?? ''
    }

    /**
     * 保存、修改基础配置(校验模块)
     */
    async saveAntiConfig() {
      if (!this.antiCheatConfig.antiCheatConfig.antiConfigName) {
        return this.$message.error('请输入监管名称')
      }
      this.antiCheatConfig.antiCheatConfig.range = RangeTypeEnum.SCHEME
      this.antiCheatConfig.antiCheatConfig.schemaIds = this.schemeList.map((item) => item.schemeId)
      if (!this.antiCheatConfig.antiCheatConfig.schemaIds.length) {
        return this.$message.error('请添加培训方案')
      }

      this.learningSupervision.learningSupervisionForm
        .validate()
        .then(() => {
          this.savePort()
        })
        .catch((e) => {
          console.error(e)
        })
    }

    /**
     * 保存接口
     */
    async savePort() {
      this.isLoading = true
      if (this.antiCheatConfig.antiCheatConfig.studyConfig.enterLearnVerify) {
        this.antiCheatConfig.antiCheatConfig.studyConfig.enterVerifyMode = EnterVerifyMode.EVERYONE
      }
      // 判断是否存在配置id，存在走修改
      if (this.$route.params.id && this.$route.params.id !== ':id') {
        try {
          this.antiCheatConfig.antiCheatConfig.studyConfig.enable = true
          await this.antiCheatConfig.updateConfig()
          this.$message.success('保存成功')
          await this.$router.push('/basic-data/platform/function')
        } catch (error) {
          this.$message.error('系统异常')

          this.isLoading = false
        }
      } else {
        // 路由不存在监管id，走新建配置流程
        try {
          await this.antiCheatConfig.createConfig()
          this.$message.success('保存成功')
          await this.$router.push('/basic-data/platform/function')
        } catch (error) {
          this.$message.error('系统异常')
          this.isLoading = false
        }
      }
    }

    /**
     * 取消-返回列表
     */
    cancel() {
      this.$router.push('/basic-data/platform/function')
    }

    async created() {
      //查询基础监管配置
      await this.baseConfig.queryDetail()

      this.antiCheatConfig = new AntiCheatConfig(this.$route.params.id as string)
      if (this.$route.params.id && this.$route.params.id !== ':id') {
        await this.antiCheatConfig.queryDetail()
        await this.querySchemeList()
        if (!this.baseConfig.baseConfig.datumConfig.enable) {
          if (this.antiCheatConfig.antiCheatConfig.studyConfig.enterVerify[0] === VerifyTypeEnum.face) {
            this.antiCheatConfig.antiCheatConfig.studyConfig.enterVerify = []
          }
          if (this.antiCheatConfig.antiCheatConfig.studyConfig.processVerifyType[0] === VerifyTypeEnum.face) {
            this.antiCheatConfig.antiCheatConfig.studyConfig.processVerifyType = []
          }
        }
      }
    }
  }
</script>
