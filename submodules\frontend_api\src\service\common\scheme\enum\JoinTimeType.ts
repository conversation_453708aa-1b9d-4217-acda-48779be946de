import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 参加时间类型枚举
 * class_time 同培训班的学习起止时间
 * designate_time 指定的参加时间
 */
export enum JoinTimeTypeEnum {
  class_time = 1,
  designate_time = 2
}

/**
 * @description 参加时间类型
 */
class JoinTimeType extends AbstractEnum<JoinTimeTypeEnum> {
  static enum = JoinTimeTypeEnum

  constructor(status?: JoinTimeTypeEnum) {
    super()
    this.current = status
    this.map.set(JoinTimeTypeEnum.class_time, '同培训班的学习起止时间')
    this.map.set(JoinTimeTypeEnum.designate_time, '指定的参加时间')
  }
}

export default JoinTimeType
