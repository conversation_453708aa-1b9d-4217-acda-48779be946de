import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 【集体报名订单】主单售后状态枚举
 */
export enum BatchOrderMainOrderAfterSaleStatusEnum {
  // 未退款
  Wait_For_Refund = 1,
  // 退款中
  Refunding,
  // 退款成功
  Success_Refund
}

/**
 * @description 【集体报名订单】主单售后状态
 */
class BatchOrderMainOrderAfterSaleStatus extends AbstractEnum<BatchOrderMainOrderAfterSaleStatusEnum> {
  static enum = BatchOrderMainOrderAfterSaleStatusEnum
  constructor(status?: BatchOrderMainOrderAfterSaleStatusEnum) {
    super()
    this.current = status
    this.map.set(BatchOrderMainOrderAfterSaleStatusEnum.Wait_For_Refund, '未退款')
    this.map.set(BatchOrderMainOrderAfterSaleStatusEnum.Refunding, '退款中')
    this.map.set(BatchOrderMainOrderAfterSaleStatusEnum.Success_Refund, '退款成功')
  }
}

export default new BatchOrderMainOrderAfterSaleStatus()
