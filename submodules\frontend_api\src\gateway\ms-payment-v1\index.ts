import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/gql/ms-payment-v1'

// 是否微服务
const isMicroService = true

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-payment-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: 'ms-payment-v1'
}

// 枚举

// 类

/**
 * 创建收款账号
<AUTHOR>
@since 2021/2/6
 */
export class DefaultCreateReceiveAccountRequest {
  /**
   * 收款账户类型
<pre>
1-线上支付
2-线下支付
</pre>
   */
  accountType?: number
  /**
   * 支付渠道ID，由支付服务提供的通用支付渠道编号
   */
  paymentChannelId?: string
  /**
   * 收款帐号名称
   */
  name?: string
  /**
   * 所属商户名称
   */
  merchantName?: string
  /**
   * 所属商户电话
   */
  merchantPhone?: string
}

/**
 * 创建收款账号
<AUTHOR>
@since 2021/2/6
 */
export class DefaultUpdateReceiveAccountRequest {
  /**
   * 收款账号编号
   */
  receiveAccountId?: string
  /**
   * 收款帐号名称
   */
  name?: string
  /**
   * 所属商户名称，null表示不更新
   */
  merchantName?: string
  /**
   * 所属商户电话，null表示不更新
   */
  merchantPhone?: string
}

/**
 * 订单线下付款更新凭证命令
<AUTHOR>
@since 2021/1/29
 */
export class OfflinePaymentOrderUpdateVoucherRequest {
  /**
   * 付款单号
   */
  paymentOrderNo?: string
  /**
   * 付款凭证列表
   */
  paymentVouchers?: Array<PaymentVoucherRequest>
}

/**
 * <AUTHOR>
@since 2021/2/1
 */
export class PaymentVoucherRequest {
  /**
   * 付款凭证文件路径，通常是图片的路径
   */
  path?: string
  /**
   * 凭证上传时间
   */
  uploadTime?: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 管理员确认付款单已线下付款
   * @param paymentOrderNo 付款单号
   * @param mutate 查询 graphql 语法文档
   * @param paymentOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async confirmPaymentOrderOfflinePaid(
    paymentOrderNo: string,
    mutate: DocumentNode = GraphqlImporter.confirmPaymentOrderOfflinePaid,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { paymentOrderNo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建收款账号
   * @param info 收款账号信息
   * @param mutate 查询 graphql 语法文档
   * @param info 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createReceiveAccount(
    info: DefaultCreateReceiveAccountRequest,
    mutate: DocumentNode = GraphqlImporter.createReceiveAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { info },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除收款账号
   * @param receiveAccountId 收款账号编号
   * @param mutate 查询 graphql 语法文档
   * @param receiveAccountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteReceiveAccount(
    receiveAccountId: string,
    mutate: DocumentNode = GraphqlImporter.deleteReceiveAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { receiveAccountId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 禁用收款账号
   * @param receiveAccountId 收款账号编号
   * @param mutate 查询 graphql 语法文档
   * @param receiveAccountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableReceiveAccount(
    receiveAccountId: string,
    mutate: DocumentNode = GraphqlImporter.disableReceiveAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { receiveAccountId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 启用收款账号
   * @param receiveAccountId 收款账号编号
   * @param mutate 查询 graphql 语法文档
   * @param receiveAccountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableReceiveAccount(
    receiveAccountId: string,
    mutate: DocumentNode = GraphqlImporter.enableReceiveAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { receiveAccountId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新线下支付凭证
   * @param request 凭证信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateOfflinePaymentVoucher(
    request: OfflinePaymentOrderUpdateVoucherRequest,
    mutate: DocumentNode = GraphqlImporter.updateOfflinePaymentVoucher,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新收款账号
   * @param info 更新的收款账号信息
   * @param mutate 查询 graphql 语法文档
   * @param info 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateReceiveAccount(
    info: DefaultUpdateReceiveAccountRequest,
    mutate: DocumentNode = GraphqlImporter.updateReceiveAccount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { info },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
