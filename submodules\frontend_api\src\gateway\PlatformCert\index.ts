import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformCert'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformCert'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举

// 类

export class CertificatePrintJobQueryRequest {
  /**
   * 打印状态 1、打印中 2：打印成功 3：打印失败
   */
  state?: string
  /**
   * >&#x3D;打印时间(即任务开始时间)开始
   */
  printDateStart?: string
  /**
   * <&#x3D;打印时间(即任务开始时间)结束
   */
  printDateEnd?: string
}

export class CertificateQueryRequest {
  /**
   * 学员姓名
   */
  userName?: string
  /**
   * 学员省份证号
   */
  uniqueData?: string
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 打印状态：1、未打 2、打印中、3、打印成功 4、打印失败 5、作废 6入库中 7 入库成功  8入库失败
   */
  printStatusList?: Array<string>
  /**
   * >&#x3D;考核通过时间开始
   */
  passedTimeStart?: string
  /**
   * <&#x3D;考核通过时间结束
   */
  passedTimeEnd?: string
}

export class Page {
  pageNo?: number
  pageSize?: number
}

export class CertificateOperationLogResponse {
  /**
   * 主键 证书操作日志表
   */
  id: string
  /**
   * 操作人ID
   */
  userId: string
  /**
   * 操作类型 操作类型|1：创建 2：开始打印 3：打印中 4：打印成功 5：打印失败6、作废7、预览
8 修改,9 入库中 10 入库成功 11入库失败
   */
  type: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 证书ID
   */
  certId: string
  /**
   * 证书号
   */
  certNo: string
  /**
   * 备注
   */
  remark: string
}

export class CertificatePrintJobResponse {
  /**
   * 主键 证书ID
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 打印类别:1、个人打印 2、批量打印
   */
  type: string
  /**
   * 打印状态 1、打印中 2：打印成功 3：打印失败
   */
  state: string
  /**
   * 打印时间
   */
  printDate: string
  /**
   * 完成时间
   */
  returnDate: string
  /**
   * 日志名称
   */
  name: string
  /**
   * 生成图片路径
   */
  picPath: string
  /**
   * 创建人
   */
  creatorId: string
}

export class CertificateRenderDataResponse {
  /**
   * 学员姓名
   */
  userName: string
  /**
   * 学员身份证号
   */
  uniqueData: string
  /**
   * 报名日期 XXXX年XX月XX日
   */
  registerTime: string
  /**
   * 合格日期 XXXX年XX月XX日
   */
  passedTime: string
  /**
   * 工种名称
   */
  workTypeName: string
  /**
   * 学时
   */
  period: number
  /**
   * 证书编号
   */
  certNo: string
  /**
   * 培训机构名称
   */
  unitName: string
  /**
   * 培训机构简称
   */
  unitAbouts: string
  /**
   * 培训机构电子公章
   */
  unitElectronicSealPath: string
  /**
   * 二维码图片地址
   */
  qrcodeText: string
}

/**
 * 用于培训证明cooper渲染模型数据
<AUTHOR>
 */
export class CertificateRenderModelResponse {
  real: boolean
  list: Array<CertificateRenderDataResponse>
}

export class CertificateRenderResponse {
  /**
   * 证书模板地址
   */
  templateUrl: string
  /**
   * 用于cooper渲染的model数据
   */
  model: CertificateRenderModelResponse
}

export class CertificateResponse {
  /**
   * 主键 证书ID
   */
  id: string
  /**
   * 证书名称
   */
  name: string
  /**
   * 证书编号
   */
  certNo: string
  /**
   * 证书持有人
   */
  owner: string
  /**
   * 证书签发时间
   */
  signTime: string
  /**
   * 证书状态：1、未打 2、打印中、3、打印成功 4、打印失败 5、作废 6入库中 7 入库成功  8入库失败
   */
  state: string
  /**
   * 是否测试数据
   */
  test: boolean
  /**
   * 是否打印
   */
  print: boolean
  /**
   * 是否批量打印
   */
  batchPrint: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人ID
   */
  creatorId: string
  /**
   * 图片路径
   */
  picPath: string
  /**
   * 考核完成的学习方案ID
   */
  schemeId: string
  /**
   * 考核完成的期数编号
   */
  issueId: string
  /**
   * 考核ID
   */
  assessId: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 用户信息
   */
  userInfo: UserSimpleInfoDTO
}

export class IdNameResponse {
  id: string
  name: string
}

/**
 * 用户对象
<AUTHOR> create 2020/3/13 9:42
 */
export class UserSimpleInfoDTO {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 昵称
   */
  nickName: string
  /**
   * 身份证号
   */
  idCard: string
  /**
   * 头像地址
   */
  photo: string
  /**
   * 性别
   */
  gender: number
  /**
   * 手机号码
   */
  phone: string
  /**
   * 所属地区编码
   */
  areaPath: string
}

export class CertificateTemplateResponse {
  /**
   * 主键 证书模板ID
   */
  id: string
  /**
   * 模板名称
   */
  name: string
  /**
   * 模板路径
   */
  temPath: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人ID
   */
  creatorId: string
  /**
   * 备注
   */
  remark: string
  /**
   * 使用范围
   */
  useableRange: string
  /**
   * 适用形式
   */
  applicableType: string
}

export class CertificatePrintJobResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CertificatePrintJobResponse>
}

export class CertificateResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CertificateResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取批量打印证明日志分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCertificateBatchPrintJobPage(
    params: { page?: Page; request?: CertificatePrintJobQueryRequest },
    query: DocumentNode = GraphqlImporter.getCertificateBatchPrintJobPage,
    operation?: string
  ): Promise<Response<CertificatePrintJobResponsePage>> {
    return commonRequestApi<CertificatePrintJobResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量获取证书操作员姓名集合
   * @param query 查询 graphql 语法文档
   * @param userIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCertificateOperatorIdNameByUserIds(
    userIds: Array<string>,
    query: DocumentNode = GraphqlImporter.getCertificateOperatorIdNameByUserIds,
    operation?: string
  ): Promise<Response<Array<IdNameResponse>>> {
    return commonRequestApi<Array<IdNameResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { userIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习证明分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCertificatePage(
    params: { page?: Page; request?: CertificateQueryRequest },
    query: DocumentNode = GraphqlImporter.getCertificatePage,
    operation?: string
  ): Promise<Response<CertificateResponsePage>> {
    return commonRequestApi<CertificateResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取指定证书的打印日志集合
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCertificatePrintLogList(
    id: string,
    query: DocumentNode = GraphqlImporter.getCertificatePrintLogList,
    operation?: string
  ): Promise<Response<Array<CertificateOperationLogResponse>>> {
    return commonRequestApi<Array<CertificateOperationLogResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取证书的渲染数据
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCertificateRenderInfo(
    id: string,
    query: DocumentNode = GraphqlImporter.getCertificateRenderInfo,
    operation?: string
  ): Promise<Response<CertificateRenderResponse>> {
    return commonRequestApi<CertificateRenderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据期数id获取证明模板信息
   * @param query 查询 graphql 语法文档
   * @param issueId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCertificateTemplateByIssueId(
    issueId: string,
    query: DocumentNode = GraphqlImporter.getCertificateTemplateByIssueId,
    operation?: string
  ): Promise<Response<CertificateTemplateResponse>> {
    return commonRequestApi<CertificateTemplateResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { issueId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据培训方案id获取证明模板信息
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCertificateTemplateBySchemeId(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.getCertificateTemplateBySchemeId,
    operation?: string
  ): Promise<Response<CertificateTemplateResponse>> {
    return commonRequestApi<CertificateTemplateResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取证书模板预览信息
   * @param query 查询 graphql 语法文档
   * @param templateId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCertificateTemplatePreview(
    templateId: string,
    query: DocumentNode = GraphqlImporter.getCertificateTemplatePreview,
    operation?: string
  ): Promise<Response<CertificateRenderResponse>> {
    return commonRequestApi<CertificateRenderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { templateId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量获取证书中的期数id集合对应的期数名
   * @param query 查询 graphql 语法文档
   * @param issueIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getIssueIdNameListByIds(
    issueIds: Array<string>,
    query: DocumentNode = GraphqlImporter.getIssueIdNameListByIds,
    operation?: string
  ): Promise<Response<Array<IdNameResponse>>> {
    return commonRequestApi<Array<IdNameResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { issueIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量获取证书中的方案id集合对应的方案名
   * @param query 查询 graphql 语法文档
   * @param schemeIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSchemeIdNameListByIds(
    schemeIds: Array<string>,
    query: DocumentNode = GraphqlImporter.getSchemeIdNameListByIds,
    operation?: string
  ): Promise<Response<Array<IdNameResponse>>> {
    return commonRequestApi<Array<IdNameResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学员购买的指定期数的培训证明数据
   * @param query 查询 graphql 语法文档
   * @param issueId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentCertificateData(
    issueId: string,
    query: DocumentNode = GraphqlImporter.getStudentCertificateData,
    operation?: string
  ): Promise<Response<CertificateRenderResponse>> {
    return commonRequestApi<CertificateRenderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { issueId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学员已有的培训证明分页信息
   * @param query 查询 graphql 语法文档
   * @param page 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentCertificatePage(
    page: Page,
    query: DocumentNode = GraphqlImporter.getStudentCertificatePage,
    operation?: string
  ): Promise<Response<CertificateResponsePage>> {
    return commonRequestApi<CertificateResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: { page },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学员购买的指定期数的培训证明数据
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserCertificateData(
    params: { userId?: string; issueId?: string },
    query: DocumentNode = GraphqlImporter.getUserCertificateData,
    operation?: string
  ): Promise<Response<CertificateRenderResponse>> {
    return commonRequestApi<CertificateRenderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量打印证书
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchPrintCertificate(
    request: CertificateQueryRequest,
    mutate: DocumentNode = GraphqlImporter.batchPrintCertificate,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 打印证书
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async printCertificate(
    id: string,
    mutate: DocumentNode = GraphqlImporter.printCertificate,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
