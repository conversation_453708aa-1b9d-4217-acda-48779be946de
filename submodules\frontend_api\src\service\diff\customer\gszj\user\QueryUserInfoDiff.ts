import QueryUserInfo from '@api/service/customer/user/query/QueryUserInfo'
import Context from '@api/service/common/context/Context'
import OnlineSchoolConfigModule from '@api/service/customer/online-school-config/OnlineSchoolConfigModule'
import SchoolServiceIdStrategy from '@api/service/common/diffSchool/SchoolServiceIdStrategy'
import SchoolEnum from '@api/service/common/diffSchool/enums/SchoolEnum'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import UserInfoVoDiff from '@api/service/diff/customer/gszj/user/models/UserInfoVoDiff'
import BasicDataQueryForestage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import DictionaryTypeTypes, {
  DictionaryTypeEnum
} from '@api/service/diff/common/gszj/basic-data-dictionary/query/enum/DictionaryType'
import QueryDictionaryType from '@api/service/diff/common/gszj/basic-data-dictionary/query/QueryDictionaryType'

class QueryUserInfoDiff {
  isPerfectInfo = false

  /**
   * 获取用户信息
   */
  userInfoDiff = new UserInfoVoDiff()

  /**
   * 判断当前地区是否为阿波罗配置的地区
   */
  get getRegionIsDiff() {
    const regionList = ['620300']
    return Context.businessEnvironment?.specialTopicsInfo?.regions?.some((item) =>
      regionList.some((region) => item.regionPath.includes(region))
    )
  }
  get areaCode() {
    return '620300'
  }
  /**
   * 获取通用用户信息
   */
  get userInfo() {
    return QueryUserInfo.userInfo
  }

  /**
   * 获取用户信息
   */
  async getUserInfo() {
    if (!this.userInfoDiff?.userId) {
      await QueryUserInfo.getCurrenUserInfo()
    }
    this.userInfoDiff = Object.assign(new UserInfoVoDiff(), this.userInfoDiff)
    if (this.getRegionIsDiff) {
      const { data, status } = await BasicDataQueryForestage.getAreaTrainingChannelStudentInfoInMyself(this.areaCode)
      const dictionaryTypeList = [
        DictionaryTypeEnum.GSZJ_STAFFING_STATUS,
        DictionaryTypeEnum.GSZJ_TITLE_EFFECTIVE_RANGE,
        DictionaryTypeEnum.GSZJ_TITLE_GRADE,
        DictionaryTypeEnum.GSZJ_TITLE_SERIES,
        DictionaryTypeEnum.GSZJ_UNIT_NATURE
      ]
      // 使用Promise.all并行请求
      await Promise.all(dictionaryTypeList.map((item) => QueryDictionaryType.queryDictionaryTypeList(item)))
      if (status.isSuccess() && data?.userId) {
        this.userInfoDiff.from(data)
      }
    }
    return this.userInfoDiff
  }
  /**
   * 判断用户信息是否完善
   */
  async checkUserInfoPerfect() {
    await this.getUserInfo()
    // 查询注册必填字段设置
    await OnlineSchoolConfigModule.queryOnlineSchoolConfigFactory.queryWebPortalConfig.queryRegisterSetting()
    const registerSetting = OnlineSchoolConfigModule.queryOnlineSchoolConfigFactory.queryWebPortalConfig.registerSetting
    const nameIsrequired = registerSetting.studentRegister.find((item) => item.field === 'name')?.isRequire
    const phoneIsrequired = registerSetting.studentRegister.find((item) => item.field === 'phone')?.isRequire
    const idCardIsrequired = registerSetting.studentRegister.find((item) => item.field === 'idCard')?.isRequire
    const areaIsrequired = registerSetting.studentRegister.find((item) => item.field === 'area')?.isRequire
    // 和项目组沟通后，证件类型是否必填不根据接口返回来做判断。详细可以联系->liuzhichun
    const idCardTypeIsrequired = true

    const genderIsrequired = registerSetting.studentRegister.find((item) => item.field === 'gender')?.isRequire
    this.isPerfectInfo = true
    // 名称是否完善
    if (nameIsrequired && !this.userInfo.userInfo.userName) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    // 电话号码是否完善
    if (phoneIsrequired && !this.userInfo.userInfo.phone) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    // 证件类型是否完善
    if (idCardTypeIsrequired && !this.userInfo.userInfo.idCardType) {
      this.isPerfectInfo = false
    }
    // 身份证号码是否完善
    if (idCardIsrequired && !this.userInfo.userInfo.idCard) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    // 性别是否完善
    if (genderIsrequired && this.userInfo.userInfo.gender === -1) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    // 地区是否完善
    if (areaIsrequired && !this.userInfo.userInfo.companyRegion?.regionId) {
      this.isPerfectInfo = false
      return this.isPerfectInfo
    }
    if (this.userInfoDiff.isEmpty) {
      this.isPerfectInfo = false
    }
    return this.isPerfectInfo
  }
}
export default new QueryUserInfoDiff()
