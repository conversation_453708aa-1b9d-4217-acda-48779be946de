@charset "utf-8";

@import "../variables";

// function工具类

// 定位
.f-rel {
  position: relative;
}

.f-abs {
  position: absolute;
}

// 文字
.f-fb {
  font-weight: bold;
}

.f-f12 {
  font-size: 12px;
}

.f-f13 {
  font-size: 13px;
}

.f-f14 {
  font-size: 14px;
}

.f-f15 {
  font-size: 15px;
}

.f-f16 {
  font-size: 16px;
}

.f-f18 {
  font-size: 18px;
}

.f-f20 {
  font-size: 20px;
}

.f-f22 {
  font-size: 22px;
}

.f-f24 {
  font-size: 24px;
}

.f-to {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.f-tc {
  text-align: center;
}

.f-tr {
  text-align: right;
}

.f-tl {
  text-align: left;
}

.f-tj {
  text-align: justify;
  text-justify: inter-ideograph;
}

// 颜色(配合变量使用)
.f-cb {
  color: $base;
}

.f-ci {
  color: $important;
}

.f-cr {
  color: $danger;
}

.f-co {
  color: $warning;
}

.f-cg {
  color: $success;
}

// 显示隐藏
.f-show {
  display: block !important;
}

.f-hide {
  display: none !important;
}

// 浮动
.f-fl {
  float: left;
}

.f-fr {
  float: right;
}

// 清除浮动
.f-clear {
  *zoom: 1;
}

.f-clear::before,
.f-clear::after {
  content: "";
  display: table;
}

.f-clear::after {
  clear: both;
}

// 内容垂直与水平居中
.f-cv {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
}

.f-ch {
  position: relative;
  left: 50%;
  transform: translateX(-50%);
}

// 指针
.f-csp {
  cursor: pointer;
}

.f-csd {
  cursor: default;
}

.f-csh {
  cursor: help;
}

.f-csm {
  cursor: move;
}

// 不可选
.f-usn {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  -o-user-select: none;
  user-select: none;
}

// flex布局
.f-flex {
  display: flex;
}

.f-flex-wrap {
  flex-wrap: wrap;
}

.f-flex-sub {
  flex: 1;
  min-width: 0;
}

.f-flex-twice {
  flex: 2;
}

.f-flex-treble {
  flex: 3;
}

.f-justify-start {
  justify-content: flex-start;
}

.f-justify-end {
  justify-content: flex-end;
}

.f-justify-between {
  justify-content: space-between;
}

.f-justify-around {
  justify-content: space-around;
}

.f-justify-center {
  justify-content: center;
}

.f-align-start {
  align-items: flex-start;
}

.f-align-end {
  align-items: flex-end;
}

.f-align-center {
  align-items: center;
}

// margin(5-50)
@for $i from 1 through 10 {
  .f-mt#{$i * 5} {
    margin-top: 1px * $i * 5;
  }

  .f-mr#{$i * 5} {
    margin-right: 1px * $i * 5;
  }

  .f-mb#{$i * 5} {
    margin-bottom: 1px * $i * 5;
  }

  .f-ml#{$i * 5} {
    margin-left: 1px * $i * 5;
  }

  .f-m#{$i * 5} {
    margin: 1px * $i * 5;
  }

  .f-mtb#{$i * 5} {
    margin-top: 1px * $i * 5;
    margin-bottom: 1px * $i * 5;
  }

  .f-mlr#{$i * 5} {
    margin-left: 1px * $i * 5;
    margin-right: 1px * $i * 5;
  }

  .f-pt#{$i * 5} {
    padding-top: 1px * $i * 5;
  }

  .f-pr#{$i * 5} {
    padding-right: 1px * $i * 5;
  }

  .f-pb#{$i * 5} {
    padding-bottom: 1px * $i * 5;
  }

  .f-pl#{$i * 5} {
    padding-left: 1px * $i * 5;
  }

  .f-p#{$i * 5} {
    padding: 1px * $i * 5;
  }

  .f-ptb#{$i * 5} {
    padding-top: 1px * $i * 5;
    padding-bottom: 1px * $i * 5;
  }

  .f-plr#{$i * 5} {
    padding-left: 1px * $i * 5;
    padding-right: 1px * $i * 5;
  }
}

// 20210407补充
//字体颜色设置
.f-c6 {
  color: #666;
}

.f-c9 {
  color: #999;
}

.f-link {
  cursor: pointer;

  &:hover {
    color: $base;
  }
}

.f-link-gray {
  cursor: pointer;

  &:hover {
    color: $base;
  }
}

.f-underline {
  &:hover {
    text-decoration: underline;
  }
}

.f-wf {
  width: 100%;
}
// 2023061407补充
.f-vm {
  display: inline-block;
  vertical-align: middle;
}

.f-dot {
  width: 6px;
  height: 6px;
  border-radius: 100%;
  background-color: $base;
  display: inline-block;
  vertical-align: 2px;

  &.gray {
    background-color: #ddd;
  }
}
//2行文字省略号
.f-to-two {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

//重置字体粗体变正常
.f-fn {
  font-weight: normal;
}
