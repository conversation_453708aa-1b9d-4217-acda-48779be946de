<template>
  <div class="m-login-wrap">
    <div class="wrap-bd">
      <div class="logo-txt">{{ webPortal.title }}管理平台</div>
      <div class="f-flex-sub f-flex">
        <div class="login-pic">
          <img src="@design/admin/assets/images/login-pic.png" alt=" " />
        </div>
        <div class="f-flex-sub">
          <el-tabs v-model="activeName" :stretch="true" class="m-login">
            <el-tab-pane label="帐号登录" name="first">
              <el-form
                ref="loginForm"
                :model="formData"
                :rules="rules"
                label-width="auto"
                class="m-form"
                @keyup.native.enter="beforeLogin"
                v-if="!showSmsModal"
              >
                <el-form-item prop="roleType">
                  <el-select
                    v-model="formData.roleType"
                    clearable
                    placeholder="请选择帐号类型"
                    @change="roleTypeChange"
                  >
                    <el-option label="管理员" :value="1">管理员</el-option>
                    <!-- <el-option label="集体缴费管理员" :value="3">集体缴费管理员</el-option> -->
                    <el-option label="地区管理员" :value="3">地区管理员</el-option>
                    <!-- <el-option label="子项目管理员" :value="4">子项目管理员</el-option> -->
                    <el-option label="分销管理员" :value="4">分销管理员</el-option>
                    <i slot="prefix" class="el-input__icon iconfont icon-xiangmu"></i>
                  </el-select>
                </el-form-item>
                <el-form-item prop="account">
                  <el-input v-model="formData.account" clearable placeholder="请输入帐号/手机号">
                    <i slot="prefix" class="el-input__icon iconfont icon-zhanghao"></i>
                  </el-input>
                </el-form-item>
                <el-form-item prop="password">
                  <el-input v-model="formData.password" clearable show-password placeholder="请输入密码">
                    <i slot="prefix" class="el-input__icon iconfont icon-mima"></i>
                  </el-input>
                </el-form-item>
                <biz-slider ref="sliderRef" v-if="isUseSlider" @validCaptcha="validCaptcha" :token="localToke" />
                <el-form-item prop="captcha" v-else>
                  <div class="f-flex">
                    <el-input v-model="formData.captcha" clearable placeholder="请输入图形验证码" class="f-flex-sub">
                      <i slot="prefix" class="el-input__icon iconfont icon-yanzhengma"></i>
                    </el-input>
                    <div class="code">
                      <img :src="validateCodePic" alt=" " @click="refreshValidateCodePic" title="看不清，点击刷新" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item class="is-text op f-pb10">
                  <el-checkbox v-model="rememberPassword">记住密码</el-checkbox>
                  <a href="#" class="f-link f-c9 f-fr" @click.stop="forget">忘记密码？</a>
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-alert type="error" show-icon :closable="false" class="m-alert f-mb10" v-if="loginResult.message">
                    {{ loginResult.message }}
                  </el-alert>
                  <el-button type="primary" @click="beforeLogin" :loading="loginIng">
                    {{ loginIng ? '登录中...' : '立即登录' }}
                  </el-button>
                </el-form-item>
              </el-form>
              <sms-login-modal
                :phone="phoneNumber"
                :roleType="formData.roleType"
                :continueLoading="continueLoading"
                :token="chainToken"
                @success="operationSuccessful"
                v-if="showSmsModal"
              ></sms-login-modal>
            </el-tab-pane>
            <el-tab-pane label="验证码登录" name="second" v-if="!enableRisk">
              <el-form
                ref="loginFormSec"
                :model="formData"
                :rules="rules"
                label-width="auto"
                class="m-form f-mt30"
                @keyup.native.enter="smsCodeLogin"
              >
                <el-form-item prop="roleType">
                  <el-select v-model="formData.roleType" clearable placeholder="请选择帐号类型">
                    <el-option label="管理员" :value="1">管理员</el-option>
                    <!-- <el-option label="集体缴费管理员" :value="3">集体缴费管理员</el-option> -->
                    <el-option label="地区管理员" :value="3">地区管理员</el-option>
                    <!-- <el-option label="子项目管理员" :value="4">子项目管理员</el-option> -->
                    <el-option v-if="isHadFxAbility" label="分销管理员" :value="4">分销管理员</el-option>
                    <i slot="prefix" class="el-input__icon iconfont icon-xiangmu"></i>
                  </el-select>
                </el-form-item>
                <el-form-item prop="account">
                  <el-input v-model="formData.account" clearable placeholder="请输入手机号">
                    <i slot="prefix" class="el-input__icon iconfont icon-shouji"></i>
                  </el-input>
                </el-form-item>
                <el-form-item prop="phoneCaptcha">
                  <div class="f-flex">
                    <el-input
                      v-model="formData.phoneCaptcha"
                      clearable
                      :disabled="!isPhoneNumberValid"
                      placeholder="请输入短信验证码"
                      class="f-flex-sub"
                    >
                      <i slot="prefix" class="el-input__icon iconfont icon-yanzhengma"></i>
                    </el-input>
                    <div class="code">
                      <el-button
                        :disabled="loginSending && !isPhoneNumberValid"
                        :loading="applyingCaptcha"
                        type="primary"
                        @click="sendMessageWithoutCaptcha"
                        plain
                        v-if="loginSending"
                      >
                        获取短信验证码
                      </el-button>
                      <el-button v-if="!loginSending" type="info" plain disabled>
                        重新获取（{{ countTime }}）
                      </el-button>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-alert type="error" show-icon :closable="false" class="m-alert f-mb10" v-if="loginResult.message">
                    {{ loginResult.message }}
                  </el-alert>
                  <el-button type="primary" :loading="loginIng" @click="smsCodeLogin">立即登录</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="login-footer">
        <div v-html="webPortal.footContent"></div>
      </div>
    </div>

    <el-dialog
      title="绑定手机号"
      :visible.sync="dialog1"
      width="600px"
      class="m-dialog"
      :before-close="cancel"
      :show-close="!enableSms"
    >
      <el-form
        ref="changePhoneRef"
        :model="changePhoneFrom"
        :rules="checkPhoneRules"
        label-width="auto"
        class="m-form f-mlr50"
      >
        <el-form-item label="手机号：" prop="phone">
          <el-input v-model="changePhoneFrom.phone" clearable placeholder="请输入11位手机号" />
        </el-form-item>
        <el-form-item label="图形验证码：" prop="captcha">
          <div class="f-flex">
            <el-input v-model="changePhoneFrom.captcha" clearable placeholder="请输入图形验证码" class="f-flex-sub" />
            <div class="code">
              <img :src="validatePic" @click="refreshValidateCodePicPhone" title="看不清，点击刷新" />
            </div>
          </div>
        </el-form-item>
        <el-form-item label="短信校验码：" prop="smsCode">
          <div class="f-flex">
            <el-input v-model="changePhoneFrom.smsCode" clearable placeholder="请输入短信校验码" class="f-flex-sub" />
            <div class="code">
              <el-button
                type="primary"
                v-if="loginSending"
                :disabled="isPhoneCaptchaValid || changePhoneFrom.phone.length != 11"
                @click="sendSmsCodeByUpdatePhone"
                plain
                >获取短信验证码</el-button
              >
              <el-button v-if="!loginSending" type="info" plain disabled>重新获取（{{ countTime }}s）</el-button>
            </div>
          </div>
        </el-form-item>
        <el-form-item class="m-btn-bar">
          <!-- <el-button @click="cancel">取消</el-button> -->
          <el-button @click="submit" :loading="changePhoneLoading" type="primary">确认提交</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="提示" :visible.sync="dialogVisible3" width="400px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb f-f16">帐号绑定手机号成功</p>
          <p class="f-f13 f-mt5">可以使用手机号直接登录~</p>
        </div>
      </div>
      <div slot="footer">
        <el-button type="primary" @click="changePhoneClose">确定{{ sureTime }}s</el-button>
      </div>
    </el-dialog>

    <el-dialog title="修改密码" :visible.sync="passwordDialog" width="600px" class="m-dialog" :show-close="false">
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form
            :model="checkPassword"
            label-width="auto"
            class="m-form"
            ref="checkPassworRef"
            :rules="checkPasswordRules"
          >
            <el-form-item label="新密码：" prop="password">
              <el-input clearable placeholder="请填写新密码" show-password v-model="checkPassword.password" />
            </el-form-item>
            <el-form-item label="确认密码：" prop="againPassword">
              <el-input clearable placeholder="请填写确认密码" show-password v-model="checkPassword.againPassword" />
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <div slot="footer">
        <el-button type="primary" @click="changePasswordSubmit">提交新密码</el-button>
      </div>
    </el-dialog>

    <UnitDialog ref="unitDialogRef" />
  </div>
</template>

<script lang="ts">
  import LoginCore, { RoleType } from '@hbfe/jxjy-admin-authentication/src/login/login'
  import { Component, Mixins, Ref, Watch } from 'vue-property-decorator'
  import UnitDialog from '@hbfe/jxjy-admin-authentication/src/login/components/unit-dialog.vue'
  import MZTStatus from '@hbfe/jxjy-admin-authentication/src/login/models/MZTStatus'
  import ServiceTokenMixin from '@hbfe/jxjy-admin-common/src/mixins/ServiceTokenMixin'
  import RootModule from '@/store/RootModule'
  import UserModule from '@api/service/management/user/UserModule'
  import AccountModule from '@api/service/common/account/AccountModule'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import { SmsCodeApplyRequest } from '@hbfe-ms/ms-basicdata-domain-gateway'
  import { BusinessTypeEnum } from '@hbfe-biz/biz-authentication/dist/enums/BusinessTypeEnum'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import BizSlider from '@hbfe/jxjy-admin-components/src/biz/biz-slider.vue'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import SmsLoginModal from '@hbfe/jxjy-admin-authentication/src/login/components/sms-login-modal.vue'

  @Component({
    components: {
      SmsLoginModal,
      BizSlider,
      UnitDialog
    }
  })
  export default class extends Mixins(LoginCore) {
    success = 5

    dialogVisible3 = false

    dialog1 = false
    form = {
      name: '',
      region: '',
      date1: '',
      date2: '',
      delivery: false,
      resource: '',
      desc: ''
    }
    webPortal = OnlineSchoolConfigModule.queryPortal?.webPortalInfo

    input = ''
    select = ''
    UserModule = UserModule.queryUserFactory.queryManagerDetail
    // footContent = ''
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable

    passwordLogin = true
    AccountModule = AccountModule.mutationFactory.getMutationBizAccount()
    hasPhoneNumber = true

    async checkPhoneCaptchFunc(rule: any, value: any, callback: any) {
      if (value && rule.regexp.test(value)) {
        console.log(this.isPhoneCaptchaValid, 'this.isPhoneCaptchaValid valid')
        if (!this.phoneCaptchaVaildFlag) {
          try {
            const res = await this.$authentication.verify.msValidateCaptcha(value)
            if (res.data.code == 200) {
              this.phoneCaptchaValidChange()
              this.phoneCaptchaVaildFlag = true
              callback()
            } else {
              await this.refreshValidateCodePicPhone()
              callback(new Error('验证失败'))
            }
          } catch (e) {
            await this.refreshValidateCodePicPhone()
            callback(new Error('验证失败'))
          }
        } else {
          callback()
        }
      } else {
        callback(new Error('验证码为4位数'))
      }
    }

    checkPhoneRules = {
      captcha: [
        {
          // 以大写小写字母或者数字组合的4位
          regexp: /^[a-zA-z0-9]{4}$/,
          validator: this.checkPhoneCaptchFunc,
          required: true,
          trigger: 'change'
        },
        { required: true, message: '图形验证码不能为空', trigger: 'blur' }
      ],
      smsCode: {
        required: true,
        message: '短信验证码不能为空',
        trigger: 'blur'
      },
      phone: {
        required: true,
        validator: this.validatePhone,
        trigger: 'blur'
      }
    }

    resetPwdAdmin = UserModule.mutationUserFactory.resetPwdAdmin
    queryManagerDetail = UserModule.queryUserFactory.queryManagerDetail

    get Root() {
      return RootModule || {}
    }

    toggleTab(flag: boolean) {
      this.refreshValidateCodePic()
      this.passwordLogin = flag
    }

    async loginSuccess() {
      try {
        await this.UserModule.queryManagerDetail()
        const phone = this.UserModule?.adminInfo?.userInfo?.phone
        if (!phone) {
          this.dialog1 = true
          await this.refreshValidateCodePicPhone()
          return
        } else {
          window.location.replace(this.$router.resolve('/home').href)
          window.location.reload()
        }
      } catch (e) {
        // this.$router.push('/home')
      }
    }

    loginFail(message: string) {
      // 登录失败
      this.$message.warning(message)
    }

    get query(): MZTStatus {
      return this.$route.query as any
    }

    async created() {
      ServiceTokenMixin.shareInstance().clearService()
      // this.footContent = OnlineSchoolConfigModule.queryPortal?.webPortalInfo.footContent
      // const res = await OnlineSchoolConfigModule.queryPortal.queryDetail()
      // if (res.isSuccess()) {
      // }

      this.passwordLogin = !this.$route.query.channel
      const loginflag = this.query.loginflag
      const trustticket = this.query.trustticket as string
      if (loginflag !== undefined) {
        if (loginflag === 'true') {
          this.$router.replace(`/auto-mzt?loginflag=${loginflag}&trustticket=${trustticket}`)
        }
        // 需要return 避免死循环
        return
      }
      console.log(loginflag, trustticket, '路由参数')
      // 闽政通登录接口入口文档：http://192.168.1.225:8090/pages/viewpage.action?pageId=********
      let resultUrl =
        process.env.NODE_ENV === 'development'
          ? `${location.origin}/#${this.$route.fullPath}`
          : `${location.origin}/admin/#${this.$route.fullPath}`
      resultUrl = encodeURIComponent(resultUrl)
      // await this.$authentication.thirdParty.applyCheckLogin(
      //   ThirdPartyType.BTAdmin,
      //   ConnectIdType.unionId,
      //   AccountType.admin,
      //   resultUrl
      // )
    }

    /**
     *  忘记密码
     */
    forget() {
      this.$router.push('/forget')
    }

    toback() {
      this.$router.push('/home/<USER>')
    }
    cancel() {
      //   window.location.replace(this.$router.resolve('/home').href)
      //   window.location.reload()
      if (this.enableSms) {
        this.dialog1 = false
        this.loginIng = false
      } else {
        window.location.replace(this.$router.resolve('/home').href)
        window.location.reload()
      }
    }

    /**
     * 验证手机号格式
     */
    validatePhone(rule: any, value: any, callback: any) {
      const reg = new RegExp(/^[1]([3-9])[0-9]{9}$/)
      if (value === '') {
        callback(new Error('手机号不能为空'))
      } else {
        reg.test(value) ? callback() : callback(new Error('请输入正确的手机号'))
      }
    }

    phoneCaptchaValidChange() {
      this.isPhoneCaptchaValid = false
      console.log(this.isPhoneCaptchaValid, 'this.isPhoneCaptchaValid change false')
    }

    changePhoneClose() {
      this.dialogVisible3 = false
      this.dialog1 = false
      if (this.enableSms) {
        this.doAuthLogin()
      } else {
        window.location.replace(this.$router.resolve('/home').href)
        window.location.reload()
      }
    }
    changePhone() {
      this.dialog1 = true
    }

    get phone() {
      return this.queryManagerDetail.adminInfo.userInfo.phone
    }
    get userName() {
      return this.queryManagerDetail.adminInfo.userInfo.userName
    }
    get accountId() {
      return this.queryManagerDetail.adminInfo.accountInfo.accountId
    }
    // 获取短信验证码
    async sendSmsCodeByUpdatePhone() {
      const params = new SmsCodeApplyRequest()
      params.phone = this.changePhoneFrom.phone
      params.token = this.$authentication.verify.captchaToken
      params.businessType = BusinessTypeEnum.change_binding_phone
      let res
      if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
        res = await this.$authentication.verify.applySmsCodeForLoginDistributor(params.phone)
      } else {
        res = await this.$authentication.verify.msSendSmsCode(params)
      }
      if (res.data.code === 409) {
        this.$message.error('一个手机号一天只能接收4条短信，今日次数已用完，请隔天再试。')
      } else if (res.data.code === 410) {
        this.$message.error('发送验证码失败')
      } else if (res.data.code === 701) {
        this.$message.error('请重新验证图形验证码后，再次获取短信验证码')
      } else if (res.data.code === 515) {
        this.$message.error('不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)')
      } else if (res.data.code === 400) {
        this.$message.error('token无效')
      } else if (res.data.code === 509) {
        this.$message.error('未绑定手机号')
      } else if (res.data.code === 514) {
        this.$message.error('token中未携带手机号')
      }
      if (res.status.code === 200) {
        this.loginSending = false
        this.countDown(this.count)
      } else {
        this.$message.error('发送验证码失败')
      }
    }

    beforeDestroy() {
      this.stopIntervalTimer()
    }
  }
</script>
<style scoped>
  .el-dialog__header {
    border: 0;
  }
</style>
