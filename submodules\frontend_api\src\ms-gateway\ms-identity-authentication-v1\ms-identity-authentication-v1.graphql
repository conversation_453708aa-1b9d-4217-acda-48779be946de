"""独立部署的微服务,K8S服务名:ms-identity-authentication-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getAccessTokenValue(accessToken:String!):AccessTokenResponse @optionalLogin
	"""返回当前平台绑定的账户信息(支持再认证单点)
		@param credential {token,rgtCredentialToken}
		@return {boundAccounts}
	"""
	getBoundAccountsForReAuthenticate(credential:RgtCredentialRequest):BoundAccountResponse @optionalLogin
	"""返回当前平台业务域绑定的代理平台业务域信息
		@param request {proxyBusinessDomain}
		@return {boundProxyBusinessDomains}
	"""
	getBoundProxyBusinessDomains(request:ProxyIdentityCredentialRequest):ProxyBusinessDomainResponse
}
type Mutation {
	"""使用账户ID凭据申请获取身份凭证
		@param credential {token,accountId}
		@return {code,message,identityAuthenticationToken}
	"""
	applyAuthenticateByAccountId(credential:AccountIdCredentialRequest):IdentityAuthenticationTokenResponse @optionalLogin
	"""使用账户密码凭据申请获取身份凭证
		@param credential {token,identity,password}
		@return {code,message,identityAuthenticationToken}
	"""
	applyAuthenticateByAccountPwd(credential:AccountPwdCredentialRequest):IdentityAuthenticationTokenResponse @optionalLogin
	"""使用账户密码图形验证码凭据申请获取身份凭证
		@param credential {token,identity,password,captchaValue,grantType}
		@return {code,message,identityAuthenticationToken}
	"""
	applyAuthenticateByAccountPwdCaptcha(credential:AccountPwdCaptchaCredentialRequest):IdentityAuthenticationTokenResponse @optionalLogin
	"""使用账户密码图形验证码凭据申请获取身份凭证【分销商专用】
		@param credential {token,identity,password,captchaValue}
		@return {code,message,identityAuthenticationToken}
	"""
	applyAuthenticateByAccountPwdCaptchaForFXS(credential:AccountPwdCaptchaCredentialForFXSRequest):IdentityAuthenticationTokenResponse @optionalLogin
	"""使用账户密码图形验证码凭据申请获取身份凭证（分销商管理员专用临时接口，后续删除）
		@param credential {token,identity,password,captchaValue}
		@return {code,message,identityAuthenticationToken}
	"""
	applyAuthenticateByAccountPwdCaptchaTempForFXSAdmin(credential:AccountPwdCaptchaCredentialForFXSRequest):IdentityAuthenticationTokenResponse @optionalLogin
	"""使用账户密码图形验证码凭据申请获取身份凭证（分销商推广门户专用临时接口，后续删除）
		@param credential {token,identity,password,captchaValue,grantType}
		@return {code,message,identityAuthenticationToken}
	"""
	applyAuthenticateByAccountPwdCaptchaTempForFXSPromotionPortal(credential:AccountPwdCaptchaCredentialRequest):IdentityAuthenticationTokenResponse @optionalLogin
	"""使用短信验证码凭据申请获取身份凭证
		@param credential {token,phone,smsCode}
		@return {code,message,identityAuthenticationToken}
	"""
	applyAuthenticateBySmsCode(credential:SmsCodeCredentialRequest):IdentityAuthenticationTokenResponse @optionalLogin
	"""使用短信验证码凭据申请获取身份凭证
		@param credential {token,phone,smsCode}
		@return {code,message,identityAuthenticationToken}
	"""
	applyAuthenticateBySmsCodeForFXS(credential:SmsCodeCredentialForFXSRequest):IdentityAuthenticationTokenResponse @optionalLogin
	"""申请（业务域）身份凭证切换列表
		@param credential {applicationMemberType,applicationMemberId}
		@return {code,message,identityAuthenticationToken}
	"""
	applyChangeIdentityAuthentication(credential:ChangeBusinessDomainCredentialRequest):IdentityAuthenticationTokenResponse
	"""使用代理标识凭据进行身份认证（获取代理身份凭证）
		@param credential {proxyBusinessDomain}
		若要进行代理的用户身份属于平台级则不需要传代理业务域标识，否则需要明确代理业务域标识
		@return {code,message,identityAuthenticationToken}
	"""
	applyProxyIdentity(credential:ProxyIdentityCredentialRequest):IdentityAuthenticationTokenResponse
	"""申请代理身份(需要调用平台与成员资格授信,签出的身份凭证会依据传入的代理业务域)
		@param credential
		@return
	"""
	applyProxyIdentityAccessSecure(credential:ProxyIdentityAccessSecureCredentialRequest):IdentityAuthenticationTokenResponse
	"""申请代理身份(统一主体)
		@param credential 代理业务域信息
		@return 身份凭证Token
	"""
	applyProxyIdentityUnifiedSubject(credential:ProxyIdentityCredentialRequest):IdentityAuthenticationTokenResponse
	"""使用根令牌凭据申请身份再认证（跨平台）
		@param credential {token,rgtCredential}
		@return {code,message,identityAuthenticationToken}
	"""
	applyReAuthenticate(credential:RgtCredentialRequest):IdentityAuthenticationTokenResponse @optionalLogin
	"""使用默认再认证凭据申请身份再认证（平台内）
		@param credential {token}
		@return {code,message,identityAuthenticationToken}
	"""
	applyReAuthenticateBasic(credential:ReAuthenticateCredentialRequest):IdentityAuthenticationTokenResponse
	"""申请第三方身份
		@param credential {token}
		@return {身份凭证Token,metadata}
	"""
	applyThirdPartyIdentity(credential:ThirdPartyIdentityCredentialRequest):ThirdPartyIdentityAuthTokenResponse @optionalLogin
}
input BusinessDomainIdentity @type(value:"com.fjhb.domain.basicdata.api.membership.BusinessDomainIdentity") {
	applicationType:Int!
	applicationMemberType:Int!
	applicationMemberId:String
}
input PlatformIdentity @type(value:"com.fjhb.domain.basicdata.api.membership.PlatformIdentity") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
}
"""账户信息
	<AUTHOR>
"""
input AccountInfo @type(value:"com.fjhb.ms.identity.authentication.v1.api.command.account.entities.AccountInfo") {
	accountId:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerId:String
}
"""@author: xucenhao
	@time: 2024-10-09
	@description:
"""
input ImageCaptchaTrack @type(value:"com.fjhb.ms.identity.authentication.v1.api.dto.ImageCaptchaTrack") {
	"""背景图片宽度."""
	bgImageWidth:Int
	"""背景图片高度."""
	bgImageHeight:Int
	"""滑块图片宽度."""
	sliderImageWidth:Int
	"""滑块图片高度."""
	sliderImageHeight:Int
	"""滑动开始时间."""
	startSlidingTime:DateTime
	"""滑动结束时间."""
	endSlidingTime:DateTime
	"""滑动的轨迹."""
	trackList:[Track]
}
input Track @type(value:"com.fjhb.ms.identity.authentication.v1.api.dto.ImageCaptchaTrack$Track") {
	"""x."""
	x:Int
	"""y."""
	y:Int
	"""时间."""
	t:Int
	"""类型."""
	type:String
}
"""账户ID-身份认证凭据
	<AUTHOR>
"""
input AccountIdCredentialRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.AccountIdCredentialRequest") {
	"""初始token"""
	token:String!
	"""授权类型
		@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
		identity_auth_token:身份凭证Token(默认授权类型)
		identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
	"""
	grantType:String
}
"""账户密码图形验证码-身份认证凭据
	<AUTHOR>
"""
input AccountPwdCaptchaCredentialForFXSRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.AccountPwdCaptchaCredentialForFXSRequest") {
	"""图形验证码"""
	captchaValue:String!
	"""验证验证码数据"""
	verifyCaptchaData:ImageCaptchaTrack
	"""认证标识"""
	identity:String!
	"""密码"""
	password:String!
	"""初始token"""
	token:String!
	"""授权类型
		@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
		identity_auth_token:身份凭证Token(默认授权类型)
		identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
	"""
	grantType:String
}
"""账户密码图形验证码-身份认证凭据
	<AUTHOR>
"""
input AccountPwdCaptchaCredentialRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.AccountPwdCaptchaCredentialRequest") {
	"""图形验证码"""
	captchaValue:String!
	"""验证验证码数据"""
	verifyCaptchaData:ImageCaptchaTrack
	"""认证标识"""
	identity:String!
	"""密码"""
	password:String!
	"""初始token"""
	token:String!
	"""授权类型
		@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
		identity_auth_token:身份凭证Token(默认授权类型)
		identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
	"""
	grantType:String
}
"""账户密码-身份认证凭据
	<AUTHOR>
"""
input AccountPwdCredentialRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.AccountPwdCredentialRequest") {
	"""认证标识"""
	identity:String!
	"""密码"""
	password:String!
	"""初始token"""
	token:String!
	"""授权类型
		@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
		identity_auth_token:身份凭证Token(默认授权类型)
		identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
	"""
	grantType:String
}
"""切换业务域-身份认证凭据
	<AUTHOR>
"""
input ChangeBusinessDomainCredentialRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.ChangeBusinessDomainCredentialRequest") {
	"""应用方类型【如单位：5，服务商：6】，表示用于切换身份所在的业务域类型"""
	applicationMemberType:Int!
	"""应用方ID（主体ID，如单位ID、服务商ID）"""
	applicationMemberId:String!
}
"""代理身份凭据请求
	<AUTHOR>
"""
input ProxyIdentityAccessSecureCredentialRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.ProxyIdentityAccessSecureCredentialRequest") {
	"""访问令牌"""
	accessToken:String!
	"""访问的代理业务域标识(需要安全进入的目标代理业务域)"""
	accessProxyBusinessDomainIdentity:BusinessDomainIdentity!
	"""代理业务域信息"""
	proxyBusinessDomain:ProxyBusinessDomain!
}
"""代理身份凭据请求
	<AUTHOR>
"""
input ProxyIdentityCredentialRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.ProxyIdentityCredentialRequest") {
	"""代理业务域信息"""
	proxyBusinessDomain:ProxyBusinessDomain!
}
"""再认证凭据-身份认证凭据
	当前再认证平台需要自动进入的应用域初始token {@link CredentialRequest#getToken()}
	<AUTHOR>
"""
input ReAuthenticateCredentialRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.ReAuthenticateCredentialRequest") {
	"""初始token"""
	token:String!
	"""授权类型
		@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
		identity_auth_token:身份凭证Token(默认授权类型)
		identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
	"""
	grantType:String
}
"""再认证根凭据-身份认证凭据
	<AUTHOR>
"""
input RgtCredentialRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.RgtCredentialRequest") {
	"""根令牌凭据Token"""
	rgtCredentialToken:String!
	"""绑定的账户"""
	boundAccount:AccountInfo
	"""初始token"""
	token:String!
	"""授权类型
		@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
		identity_auth_token:身份凭证Token(默认授权类型)
		identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
	"""
	grantType:String
}
"""手机验证码-身份认证凭据
	<AUTHOR>
"""
input SmsCodeCredentialForFXSRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.SmsCodeCredentialForFXSRequest") {
	"""手机号"""
	phone:String!
	"""短信验证码"""
	smsCode:String!
	"""初始token"""
	token:String!
	"""授权类型
		@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
		identity_auth_token:身份凭证Token(默认授权类型)
		identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
	"""
	grantType:String
}
"""手机验证码-身份认证凭据
	<AUTHOR>
"""
input SmsCodeCredentialRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.SmsCodeCredentialRequest") {
	"""手机号"""
	phone:String!
	"""短信验证码"""
	smsCode:String!
	"""初始token"""
	token:String!
	"""授权类型
		@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
		identity_auth_token:身份凭证Token(默认授权类型)
		identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
	"""
	grantType:String
}
"""代理业务域信息
	<AUTHOR>
"""
input ProxyBusinessDomain @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.dto.ProxyBusinessDomain") {
	"""代理平台标识"""
	platformIdentity:PlatformIdentity
	"""代理业务域标识
		applicationType 应用类型
		applicationMemberType 应用方类型 4:子项目、5:单位、6:服务商
		@see com.fjhb.domain.basicdata.api.consts.ApplicationTypes
		@see com.fjhb.domain.basicdata.api.consts.SystemMemberTypes
	"""
	businessDomainIdentity:BusinessDomainIdentity
}
"""第三方身份凭据请求
	<AUTHOR>
"""
input ThirdPartyIdentityCredentialRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.thirdparty.ThirdPartyIdentityCredentialRequest") {
	"""第三方授权流程产生的token"""
	token:String!
}
type BusinessDomainIdentity1 @type(value:"com.fjhb.domain.basicdata.api.membership.BusinessDomainIdentity") {
	applicationType:Int!
	applicationMemberType:Int!
	applicationMemberId:String
}
type PlatformIdentity1 @type(value:"com.fjhb.domain.basicdata.api.membership.PlatformIdentity") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
}
"""账户信息
	<AUTHOR>
"""
type AccountInfo1 @type(value:"com.fjhb.ms.identity.authentication.v1.api.command.account.entities.AccountInfo") {
	accountId:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerId:String
}
"""代理业务域信息
	<AUTHOR>
"""
type ProxyBusinessDomain1 @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.dto.ProxyBusinessDomain") {
	"""代理平台标识"""
	platformIdentity:PlatformIdentity1
	"""代理业务域标识
		applicationType 应用类型
		applicationMemberType 应用方类型 4:子项目、5:单位、6:服务商
		@see com.fjhb.domain.basicdata.api.consts.ApplicationTypes
		@see com.fjhb.domain.basicdata.api.consts.SystemMemberTypes
	"""
	businessDomainIdentity:BusinessDomainIdentity1
}
"""代理业务域响应
	<AUTHOR>
"""
type AccessTokenResponse @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.AccessTokenResponse") {
	authentication:String
	"""状态码
		@see IdentityAuthenticationStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""绑定账户响应
	<AUTHOR>
"""
type BoundAccountResponse @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.BoundAccountResponse") {
	"""绑定的账户信息"""
	boundAccounts:[AccountInfo1]
	"""状态码
		@see IdentityAuthenticationStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""身份认证统一响应
	<AUTHOR>
"""
type IdentityAuthenticationTokenResponse @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.IdentityAuthenticationTokenResponse") {
	"""根据授权模式不同，产生的token不同 {@link com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes}"""
	identityAuthenticationToken:String
	"""Token元数据"""
	tokenMetadata:TokenMetadata
	"""安全元数据"""
	securityMetadata:SecurityMetadata
	"""状态码
		@see IdentityAuthenticationStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""代理业务域响应
	<AUTHOR>
"""
type ProxyBusinessDomainResponse @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.ProxyBusinessDomainResponse") {
	"""绑定的代理业务域信息"""
	boundProxyBusinessDomains:[ProxyBusinessDomain1]
	"""状态码
		@see IdentityAuthenticationStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""Token元数据
	<AUTHOR>
"""
type TokenMetadata @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.TokenMetadata") {
	"""accountId:用户身份凭证的账户ID"""
	accountId:String
	"""userId:用户身份凭证的用户ID"""
	userId:String
	"""phone:用户身份凭证的手机号(脱敏)"""
	phone:String
}
"""失败认证元数据
	<AUTHOR>
"""
type FailedAuthMetadata @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.FailedAuthMetadata") {
	"""失败上限次数"""
	failedAuthAttemptUpperLimit:Int!
	"""已失败次数"""
	failedAuthAttemptLimit:Int!
	"""账户是否被锁定"""
	accountLock:Boolean!
	"""账户解锁时效(秒)"""
	accountUnLockExpire:Long
	"""账户解锁时间(完整时间戳)"""
	accountUnLockTime:Long
}
"""非安全（危险）信息
	<AUTHOR>
"""
type NonSecurityInfo @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.NonSecurityInfo") {
	"""更换密码认证元数据"""
	passwordChangeAuthMetadata:PasswordChangeAuthMetadata
	"""失败认证元数据"""
	failedAuthMetadata:FailedAuthMetadata
}
"""更换密码认证元数据
	<AUTHOR>
"""
type PasswordChangeAuthMetadata @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.PasswordChangeAuthMetadata") {
	"""是否需要强制更换密码"""
	forcedChangePassword:Boolean!
	"""更换密码周期（天）"""
	passwordChangeCycle:Int!
}
"""风险信息
	<AUTHOR>
"""
type RiskInfo @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.RiskInfo") {
	"""更换密码认证元数据"""
	passwordChangeAuthMetadata:PasswordChangeAuthMetadata
}
"""安全元数据
	<AUTHOR>
"""
type SecurityMetadata @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.SecurityMetadata") {
	"""安全级别"""
	securityLevel:Int!
	"""风险信息"""
	riskInfo:RiskInfo
	"""非安全（危险）信息"""
	nonSecurityInfo:NonSecurityInfo
}
"""第三方身份凭证响应
	<AUTHOR>
"""
type ThirdPartyIdentityAuthTokenResponse @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.thirdparty.ThirdPartyIdentityAuthTokenResponse") {
	"""元数据"""
	metadata:String
	"""根据授权模式不同，产生的token不同 {@link com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes}"""
	identityAuthenticationToken:String
	"""Token元数据"""
	tokenMetadata:TokenMetadata
	"""安全元数据"""
	securityMetadata:SecurityMetadata
	"""状态码
		@see IdentityAuthenticationStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
