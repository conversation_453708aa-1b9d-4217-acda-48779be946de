<template>
  <div class="bare-message">
    <el-tabs v-model="activeName" :stretch="true" style="max-height: 450px;">
      <el-tab-pane label="通知（1）" name="first">
        <el-alert
          title="成功提示的文案"
          type="success"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
        <el-alert
          title="消息提示的文案"
          type="info"
          class="mt-10"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
        <el-alert
          title="警告提示的文案"
          type="warning"
          class="mt-10"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
        <el-alert
          title="错误提示的文案"
          type="error"
          class="mt-10"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
      </el-tab-pane>
      <el-tab-pane label="关注（10）" name="second">
        <el-alert
          title="成功提示的文案"
          type="success"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
        <el-alert
          title="消息提示的文案"
          type="info"
          class="mt-10"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
        <el-alert
          title="警告提示的文案"
          type="warning"
          class="mt-10"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
        <el-alert
          title="错误提示的文案"
          type="error"
          class="mt-10"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
      </el-tab-pane>
      <el-tab-pane label="代办（10）" name="third">
        <el-alert
          title="成功提示的文案"
          type="success"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
        <el-alert
          title="消息提示的文案"
          type="info"
          class="mt-10"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
        <el-alert
          title="警告提示的文案"
          type="warning"
          class="mt-10"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
        <el-alert
          title="错误提示的文案"
          type="error"
          class="mt-10"
          description="文字说明文字说明文字说明文字说明文字说明文字说明"
          show-icon
        >
        </el-alert>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  export default {
    data() {
      return {
        activeName: undefined
      }
    }
  }
</script>

<style lang="scss">
  .bare-message {
    .el-tabs--top .el-tabs__item {
      font-size: 12px;
      min-width: auto;
      padding-left: 0;
    }
  }
</style>
