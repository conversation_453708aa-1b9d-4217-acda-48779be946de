<template>
  <el-dialog
    title="智能学习任务反馈"
    :visible.sync="visible"
    :show-close="false"
    width="450px"
    class="m-dialog"
    @close="remindMe"
  >
    <div class="dialog-alert">
      <!--警告-->
      <i class="icon el-icon-warning warning"></i>
      <span class="txt" v-if="isAdditional">
        检测到当前系统新增
        <label class="f-cr f-fb" @click="remindMe">{{ this.count }}</label> 条智能学习任务执行异常，是否立即前往处理？
      </span>
      <span class="txt" v-else>
        检测到当前系统存在
        <label class="f-cr f-fb" @click="remindMe">{{ this.count }}</label> 条智能学习任务执行异常，是否立即前往处理？
      </span>
    </div>
    <div slot="footer">
      <el-button @click="goHandle">去处理</el-button>
      <el-button type="primary" @click="remindMe">稍后提醒我</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { Events } from '@api/service/common/timer/TimerTask'
  import ExecutionListInServicer from '@api/service/management/intelligence-learning/ExecutionListInServicer'
  import { TaskStatusEnum } from '@api/service/management/intelligence-learning/enum/TaskStatusEnum'
  import moment from 'moment'

  @Component
  export default class extends Vue {
    /**
     * 新增或者存在的条数
     */
    count: number

    /**
     * 是否是新增
     */
    isAdditional: boolean

    visible = false

    TimerTask = this.$showIntelligentLearningTaskFeedback.getTimerTask()

    /**
     * 去处理
     */
    goHandle() {
      this.visible = false
      this.$emit('goHandle')
      // 后续新增异常任务，则弹窗提示
      console.log('去处理')
      localStorage.setItem('lastFeedBackTime', 'Feedback_Handled')
      this.TimerTask.stop()
    }

    async listener() {
      this.TimerTask.stop()
      const isLoginPage =
        window.location.href.includes('/login') || window.location.href.includes('/specialSubjectLogin')
      if (isLoginPage) {
        return
      }
      const lastFeedBackTime = localStorage.getItem('lastFeedBackTime')
      //获取当前时间戳
      const now = moment().format('x')
      localStorage.setItem('lastFeedBackTime', now)

      const res = await ExecutionListInServicer.queryCountByStatusAndTime({
        // startTime: lastFeedBackTime ? moment(Number(lastFeedBackTime)).format('YYYY-MM-DD HH:mm:ss') : undefined,
        // endTime: lastFeedBackTime ? moment(Number(now)).format('YYYY-MM-DD HH:mm:ss') : undefined,
        status: [TaskStatusEnum.fail, TaskStatusEnum.stop]
      })
      if (res.status.isSuccess() && res.data) {
        this.$showIntelligentLearningTaskFeedback.show({
          // isAdditional: !!lastFeedBackTime,
          count: res.data
        })
      }
    }

    /**
     * 稍后提醒我
     */
    remindMe() {
      this.visible = false
      this.$emit('remindMe')
      console.log('提醒我')
      this.TimerTask.stop()
      const now = moment().format('x')
      localStorage.setItem('lastFeedBackTime', now)
      this.TimerTask.once(Events.timeupdate, this.listener)
      this.TimerTask.start()
    }
  }
</script>
