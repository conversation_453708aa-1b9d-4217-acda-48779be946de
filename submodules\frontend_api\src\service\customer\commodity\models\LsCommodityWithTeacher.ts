/**
 * 带教师信息的学习方案商品
 */
import TeacherInfo from './TeacherInfo'
import LsCommodity from './LsCommodity'
import { PreExamLSInfoDTO } from '@api/gateway/PlatformTrade'
import moment from 'moment'
import { Constants } from '@api/service/common/models/common/Constants'

class LsCommodityWithTeacher extends LsCommodity {
  /**
   * 该培训方案所有的教师信息
   */
  teachers = new Array<TeacherInfo>()

  issueId = ''

  static from(dto: PreExamLSInfoDTO): LsCommodityWithTeacher {
    const commodity = new LsCommodityWithTeacher()
    commodity.schemeId = dto.schemeId
    commodity.name = dto.name
    commodity.year = dto.year
    commodity.workTypeId = dto.workTypeId
    commodity.trainingTypeId = dto.trainingTypeId
    // commodity.jobCategoryId = dto.jobCategoryId
    // commodity.unitCategoryId = dto.unitCategoryId
    commodity.picture = dto.picture ? '/mfs' + dto.picture : ''
    commodity.publishTime = moment(dto.publishTime, Constants.DATE_PATTERN).toDate()
    commodity.createUserId = dto.createUserId
    commodity.createTime = moment(dto.createTime, Constants.DATE_PATTERN).toDate()
    commodity.recommendIndex = dto.recommendIndex
    commodity.price = dto.price
    if (dto.achieveSetting?.grade) {
      commodity.grade = dto.achieveSetting.grade
    }
    if (dto.teachers) {
      dto.teachers.forEach(teacher => commodity.teachers.push(TeacherInfo.from(teacher)))
    }
    commodity.issueId = dto.issueId
    return commodity
  }
}

export default LsCommodityWithTeacher
