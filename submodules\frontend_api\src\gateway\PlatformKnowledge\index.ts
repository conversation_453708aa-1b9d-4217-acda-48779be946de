import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PlatformKnowledge'

// 枚举

// 类

export class ChapterDTO {
  relationId: string
  parentRelationId: string
  sort: number
  id: string
  type: string
  name: string
  code: string
  enabled: boolean
}

export class IndustryDTO {
  relationId: string
  majorModelList: Array<MajorDTO>
  id: string
  type: string
  name: string
  code: string
  enabled: boolean
}

export class MajorDTO {
  relationId: string
  sort: number
  id: string
  type: string
  name: string
  code: string
  enabled: boolean
}

export class MajorMapChapterListDTO {
  majorRelationId: string
  chapterList: Array<ChapterDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取某个专业关系节点下一级章节子节点
   * @param majorRelationId 专业关系子节点
   * @return 章节列表
   * @param query 查询 graphql 语法文档
   * @param majorRelationId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllChildChapterByMajor(
    majorRelationId: string,
    query: DocumentNode = GraphqlImporter.findAllChildChapterByMajor,
    operation?: string
  ): Promise<Response<Array<ChapterDTO>>> {
    return commonRequestApi<Array<ChapterDTO>>(SERVER_URL, {
      query: query,
      variables: { majorRelationId },
      operation: operation
    })
  }

  /**   * 获取指定多个专业关系下章节节点及子节点
   * @param majorRelationIdList 专业关系节点列表
   * @return 专业关系节点对应章节列表
   * @param query 查询 graphql 语法文档
   * @param majorRelationIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllChildChapterByMajorList(
    majorRelationIdList: Array<string>,
    query: DocumentNode = GraphqlImporter.findAllChildChapterByMajorList,
    operation?: string
  ): Promise<Response<Array<MajorMapChapterListDTO>>> {
    return commonRequestApi<Array<MajorMapChapterListDTO>>(SERVER_URL, {
      query: query,
      variables: { majorRelationIdList },
      operation: operation
    })
  }

  /**   * 获取所有行业及专业
   * @return 行业专业关系树
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllIndustryRelationList(
    query: DocumentNode = GraphqlImporter.findAllIndustryRelationList,
    operation?: string
  ): Promise<Response<Array<IndustryDTO>>> {
    return commonRequestApi<Array<IndustryDTO>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }
}

export default new DataGateway()
