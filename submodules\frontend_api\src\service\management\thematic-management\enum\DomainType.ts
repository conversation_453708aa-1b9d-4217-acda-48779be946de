import AbstractEnum from '@api/service/common/enums/AbstractEnum'
// 定义域名类型
export enum DomainTypeEnum {
  systemdefault = 1,
  owndomain = 2
}

export default class ApproveStatusType extends AbstractEnum<DomainTypeEnum> {
  static enum = DomainTypeEnum
  constructor(status?: DomainTypeEnum) {
    super()
    this.current = status
    this.map.set(DomainTypeEnum.systemdefault, '系统默认域名')
    this.map.set(DomainTypeEnum.owndomain, '自有域名')
  }
}
