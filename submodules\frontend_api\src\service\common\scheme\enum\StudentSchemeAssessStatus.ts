import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 学员方案考核状态枚举
 * unqualified 不合格
 * for_inspection 待考核
 * qualified 已合格
 */
export enum StudentSchemeAssessStatusEnum {
  unqualified = 1,
  for_inspection = 2,
  qualified = 3
}

/**
 * @description 学员方案考核状态
 */
class StudentSchemeAssessStatus extends AbstractEnum<StudentSchemeAssessStatusEnum> {
  static enum = StudentSchemeAssessStatusEnum

  constructor(status?: StudentSchemeAssessStatusEnum) {
    super()
    this.current = status
    this.map.set(StudentSchemeAssessStatusEnum.unqualified, '不合格')
    this.map.set(StudentSchemeAssessStatusEnum.for_inspection, '待考核')
    this.map.set(StudentSchemeAssessStatusEnum.qualified, '已合格')
  }
}

export default new StudentSchemeAssessStatus()
