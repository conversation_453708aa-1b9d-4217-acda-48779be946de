/**
 * 选课规则
 */
class ChooseCourseRule {
  // region properties

  /**
   *还需选课学时，类型为number
   */
  needSelectedCoursePeriod = 0
  /**
   *已选课总学时，类型为number
   */
  selectedCoursePeriod = 0
  /**
   *必修要求学时，类型为number
   */
  compulsoryPeriod = 0
  /**
   *选修课最大允许学时，类型为number
   */
  electiveMaxPeriod = 0
  /**
   *是否允许最后一门超出最大学时，类型为boolean
   */
  allowLastChooseOver = false
  /**
   *是否约束重复选课，类型为boolean
   */
  constrainedRepeatSelection = false
  /**
   *约束重复选课范围，类型为string[]
   */
  constrainedRangeKeyList: string[] = []
  // endregion
  // region methods

  // endregion
}
export default ChooseCourseRule
