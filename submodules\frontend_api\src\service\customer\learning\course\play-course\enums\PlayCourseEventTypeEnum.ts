enum PlayCourseEventTypeEnum {
  /**
   * 初始化成功
   */
  init = 'init',
  /**
   * 视频资源准备成功
   */
  prepareSuccess = 'prepare-success',
  /**
   * 播放下一个
   */
  playNext = 'play-next',
  /**
   * 播放上一个
   */
  playPrev = 'play-prev',
  /**
   * 切换音视频播放
   */
  toggleAudioPlay = 'toggle-audio-play',
  /**
   * 切换课件之前触发
   */
  beforeChangePlayCourseware = 'before-change-play-courseware',
  /**
   * 切换播放课件后触发
   */
  afterChangePlayCourseware = 'after-change-play-courseware',
  /**
   * 资源不支持、800li 保利威视
   */
  sourceNotSupport = 'source-not-support',
  /**
   * 播放结束事件
   */
  currentIsEnd = 'current-is-end',
  /**
   * 是否第一次完成视频的播放
   */
  isFirstComplete = 'is-first-complete',
  /**
   * 统一的错误处理
   */
  error = 'error',
  /**
   * 课件不支持试听
   */
  trialViewIsNotSupport = 'trial-view-is-not-support',
  /**
   * 允许试听的时间用光
   */
  trialPlayTimeUsedOut = 'trial-play-time-used-out',
  /**
   * 准备失败
   */
  prepareFail = 'prepare-fail',
  /**
   * 在线学习规则超时
   */
  onlineLearningRuleTimeout = 'onlinelearning-rule-timeout',
  /**
   * 课程资源加载失败
   */
  loadCourseResourceError = 'load-course-resource-error',

  /**
   * 需要监管（学习过程）
   */
  needAntiCheat = 'need-anti-cheat',

  /**
   * 无需监管（学习过程）
   */
  notAntiCheat = 'not-anti-cheat',

  /**
   * 需要监管（进入学习）
   */
  needEnterAntiCheat = 'need-enter-anti-cheat',

  /**
   * 无需监管（进入学习）
   */
  notEnterAntiCheat = 'not-enter-anti-cheat',

  /**
   * 监管code错误
   */
  antiError = 'anti-error'
}

export default PlayCourseEventTypeEnum
