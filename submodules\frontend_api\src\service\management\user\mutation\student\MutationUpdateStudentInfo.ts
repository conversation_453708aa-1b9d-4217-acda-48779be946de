import MsAccountGateway, {
  CreateStudentCertificateRequest,
  DeleteStudentCertificateRequest,
  TokenResponse,
  UpdateStudentCertificateRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { Response, ResponseStatus } from '@hbfe/common'
import UpdateStudentRequestVo from './vo/UpdateStudentRequestVo'
import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'
/**
 * 修改学员信息
 */
class MutationUpdateStudentInfo {
  updateStudentParams = new UpdateStudentRequestVo()

  async doUpdateStudentInfo(): Promise<Response<TokenResponse>> {
    const params = this.updateStudentParams

    // 获取注册加密值
    const res = await ServicerSeriesV1Gateway.getStudentRegisterFormConstraint()
    params.encrypt = res?.data?.token

    const result = await MsAccountGateway.updateStudentByAdmin(params.toDto())
    return result
  }

  /**
   * 更新证书
   * @param param
   */
  async doUpdateStudentCertificateInfo(param: UpdateStudentCertificateRequest) {
    const res = await MsAccountGateway.updateStudentCertificateInfo(param)
    return res
  }

  /**
   * 添加证书
   * @param param
   */
  async addStudentCertificateInfo(param: CreateStudentCertificateRequest) {
    const res = await MsAccountGateway.addStudentCertificateInfo(param)
    return res
  }

  /**
   * 删除证书
   * @param certificateId 证书id
   */
  async deleteCertificateInfoById(param: DeleteStudentCertificateRequest) {
    const res = await MsAccountGateway.deleteCertificateInfo(param)
    return res
  }
}

export default MutationUpdateStudentInfo
