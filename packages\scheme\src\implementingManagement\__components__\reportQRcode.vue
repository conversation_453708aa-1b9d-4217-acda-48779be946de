<template>
  <el-drawer title="报到二维码" :visible.sync="openDialog" size="480px" custom-class="m-drawer" :modal="false">
    <div class="drawer-bd">
      <div class="m-tit" style="flex-direction: column">
        <span class="tit-txt">报到二维码</span>
        <div class="f-mt5 f-c6">学员可以通过微信扫一扫进入期别进行报到打卡</div>
      </div>
      <div class="m-view-qrcode">
        <div class="item">
          <div class="content" :id="qrCodeModule.id">
            <div class="tit">{{ qrCodeModule.name }}</div>
            <div class="cate">
              培训报到时段：{{ timeChange(qrCodeModule.reportStartTime) }}至{{ timeChange(qrCodeModule.reportEndTime) }}
            </div>
            <div class="code">
              <img :src="checkImg" class="u-qr-code" />
            </div>
          </div>
          <div class="op">
            <el-button type="primary" round size="medium" @click="downloadPoster(qrCodeModule.id)"
              >保存至本地</el-button
            >
          </div>
        </div>
      </div>
      <div class="m-tit" style="flex-direction: column">
        <span class="tit-txt">报到二维码（不校验定位）</span>
        <div class="f-mt5 f-c6">学员可以通过微信扫一扫进入期别进行报到打卡并且不校验学员当前位置是否抵达报到范围</div>
      </div>
      <div class="m-view-qrcode">
        <div class="item">
          <div class="content" :id="qrCodeModule.id + 'false'">
            <div class="tit">{{ qrCodeModule.name }}</div>
            <div class="cate">
              培训报到时段：{{ timeChange(qrCodeModule.reportStartTime) }}至{{ timeChange(qrCodeModule.reportEndTime) }}
            </div>
            <div class="code"><img :src="notCheckImg" class="u-qr-code" /></div>
          </div>
          <div class="op">
            <el-button type="primary" round size="medium" @click="downloadNotPoster(qrCodeModule.id + 'false')"
              >保存至本地</el-button
            >
          </div>
        </div>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button type="primary" @click="openDialog = false">关闭</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Prop } from 'vue-property-decorator'
  import * as htmlToImage from 'html-to-image'
  import QRCode from 'qrcode'
  import CryptUtil from '@api/service/common/crypt/CryptUtil'
  import { EnterTypeEnums } from '@api/service/common/auth-status-tool/enums/EnterTypeEnums'
  import { AttendanceTypeEnum } from '@api/service/management/implement/enums/AttendanceTypeEnum'
  import PeriodProcess from '@api/service/management/implement/models/PeriodProcess'

  @Component({})
  export default class extends Vue {
    @Prop({
      type: String,
      default: ''
    })
    schemeId: string
    periodId = ''
    openDialog = false
    qrCodeModule = new PeriodProcess()
    checkImg = ''
    notCheckImg = ''

    // 二维码枚举赋值
    EnterTypeEnums = EnterTypeEnums
    qrcode = 'https://www.baidu.com/' // url  el-image  src=qrcode
    async open(row: PeriodProcess) {
      this.qrCodeModule = row
      this.openDialog = true
      this.checkImg = await this.getQrcode()
      this.notCheckImg = await this.getNotCheckQrcode()
    }
    // 下载海报
    downloadPoster(id: string) {
      this.domToPic(id, {
        width: 2000,
        height: 1500,
        name: this.qrCodeName
      })
    }
    downloadNotPoster(id: string) {
      this.domToPic(id, {
        width: 2000,
        height: 1500,
        name: this.qrNotCheckCodeName
      })
    }
    // 下载方法
    domToPic(domId: string, config = { width: 3035, height: 3035, name: this.qrCodeName }) {
      const node = document.getElementById(domId)
      htmlToImage
        .toPng(node)
        .then((pngUrl: string) => {
          const img = new Image()
          img.src = pngUrl
          img.onload = () => {
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')
            canvas.width = config.width
            canvas.height = config.height
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
            canvas.toBlob((blob: Blob) => {
              const url = window.URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.href = url
              link.download = `${config.name}.jpeg`
              document.body.appendChild(link)
              link.click()
              window.URL.revokeObjectURL(url)
            })
          }
        })
        .catch(function (error: any) {
          console.error('oops, something went wrong!', error)
        })
    }
    // 获取二维码
    get getQrcode() {
      return async () => {
        // 考勤入参 教学计划id 教学计划项id 方案id 期别id考勤类型 目标路径：/pages/training/attendance
        const origin = window.location.origin
        const accessPath = origin + `/h5/#/pages/transfer/qr_transfer?entryType=${EnterTypeEnums.report}&nextAddress=`
        const storageURL = await CryptUtil.encryptStr(
          `/pages/training/report-for-duty?checkLocation=${true}&schemeId=${this.qrCodeModule.schemeId}&periodId=${
            this.qrCodeModule.id
          }`
        )

        const qrcode = await QRCode.toDataURL(accessPath + storageURL)
        return qrcode
      }
    }
    get getNotCheckQrcode() {
      return async () => {
        // 考勤入参 教学计划id 教学计划项id 方案id 期别id考勤类型 目标路径：/pages/training/attendance
        const origin = window.location.origin
        const accessPath = origin + `/h5/#/pages/transfer/qr_transfer?entryType=${EnterTypeEnums.report}&nextAddress=`
        const storageURL = await CryptUtil.encryptStr(
          `/pages/training/report-for-duty?checkLocation=${false}&schemeId=${this.qrCodeModule.schemeId}&periodId=${
            this.qrCodeModule.id
          }`
        )
        const qrcode = await QRCode.toDataURL(accessPath + storageURL)
        return qrcode
      }
    }

    timeChange(time: string) {
      const dateObj = new Date(time)
      const yyyy = dateObj.getFullYear()
      const mm = String(dateObj.getMonth() + 1).padStart(2, '0') // 月份从 0 开始
      const dd = String(dateObj.getDate()).padStart(2, '0')
      const formattedDate = `${yyyy}-${mm}-${dd}`
      return formattedDate
    }

    // 二维码名称
    get qrCodeName() {
      const attendanceType = '报到'
      return (
        this.qrCodeModule.name +
          '(' +
          this.timeChange(this.qrCodeModule.reportStartTime) +
          '至' +
          this.timeChange(this.qrCodeModule.reportEndTime) +
          attendanceType +
          ')' || '报到二维码'
      )
    }
    // 二维码名称
    get qrNotCheckCodeName() {
      const attendanceType = '报到'
      return (
        this.qrCodeModule.name +
          '(' +
          this.timeChange(this.qrCodeModule.reportStartTime) +
          '至' +
          this.timeChange(this.qrCodeModule.reportEndTime) +
          attendanceType +
          '不校验定位' +
          ')' || '报到二维码'
      )
    }

    get getAttendanceType() {
      return (item: AttendanceTypeEnum) => {
        return item == AttendanceTypeEnum.signIn ? '签到' : '签退'
      }
    }
  }
</script>

<style scoped lang="scss"></style>
