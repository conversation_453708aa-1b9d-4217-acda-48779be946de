import { FieldConstraintResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import FieldConstraintVo from './FieldConstraintVo'

class OccupationalHealthIndustryRegisterVo {
  /**
   * 人员类别
   */
  personnelCategory: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 岗位类别
   */
  positionCategory: FieldConstraintVo = new FieldConstraintVo()

  from(res: Array<FieldConstraintResponse>) {
    const resMap = new Map()
    res?.forEach(item => {
      resMap.set(item.field, item)
    })
    this.personnelCategory = FieldConstraintVo.from(resMap.get('personnelCategory'))
    this.positionCategory = FieldConstraintVo.from(resMap.get('positionCategory'))
  }
}
export default OccupationalHealthIndustryRegisterVo
