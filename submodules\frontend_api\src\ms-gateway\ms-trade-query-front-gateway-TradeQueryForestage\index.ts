import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-trade-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-trade-query-front-gateway-TradeQueryForestage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum BatchOrderSortField {
  BATCH_ORDER_UN_CONFIRMED_TIME = 'BATCH_ORDER_UN_CONFIRMED_TIME',
  BATCH_ORDER_COMMIT_TIME = 'BATCH_ORDER_COMMIT_TIME'
}
export enum BatchReturnOrderSortField {
  CREATED_TIME = 'CREATED_TIME'
}
export enum CommoditySkuSortField {
  ON_SHELVE_TIME = 'ON_SHELVE_TIME',
  ISSUE_TRAINING_BEGIN_TIME = 'ISSUE_TRAINING_BEGIN_TIME',
  ISSUE_SIGN_UP_BEGIN_TIME = 'ISSUE_SIGN_UP_BEGIN_TIME',
  COMMODITY_CREATED_TIME = 'COMMODITY_CREATED_TIME',
  LAST_EDIT_TIME = 'LAST_EDIT_TIME',
  SALE_TOTAL_NUMBER = 'SALE_TOTAL_NUMBER',
  SKU_PROPERTY_YEAR = 'SKU_PROPERTY_YEAR',
  TRAINING_CHANNEL = 'TRAINING_CHANNEL'
}
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum ExchangeOrderSortField {
  APPLIED_TIME = 'APPLIED_TIME'
}
export enum OfflineInvoiceSortField {
  INVOICE_CREAT_TIME = 'INVOICE_CREAT_TIME'
}
export enum OnlineInvoiceSortField {
  INVOICE_CREAT_TIME = 'INVOICE_CREAT_TIME'
}
export enum OrderSortField {
  ORDER_NORMAL_TIME = 'ORDER_NORMAL_TIME',
  ORDER_COMPLETED_TIME = 'ORDER_COMPLETED_TIME'
}
export enum ReturnOrderSortField {
  APPLIED_TIME = 'APPLIED_TIME'
}
export enum SortPolicy1 {
  ASC = 'ASC',
  DESC = 'DESC'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class BigDecimalScopeRequest {
  begin?: number
  end?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class BatchOrderRequest {
  /**
   * 批次单号集合
   */
  batchOrderNoList?: Array<string>
  /**
   * 批次单基本信息查询参数
   */
  basicData?: BatchOrderBasicDataRequest
  /**
   * 批次单支付信息查询参数
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 批次单创建人查询参数
   */
  creatorIdList?: Array<string>
  /**
   * 是否已经申请发票
   */
  isInvoiceApplied?: boolean
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 批次单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class BatchOrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: BatchOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 批次单基本信息查询参数
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderBasicDataRequest {
  /**
   * 批次单状态
0: 未确认，批次单初始状态
1: 正常
2: 交易完成
3: 交易关闭
4: 提交处理中 提交处理完成后，变更为NORMAl
5: 取消处理中
@see BatchOrderStatus
   */
  batchOrderStatusList?: Array<number>
  /**
   * 批次单状态变更时间
   */
  batchOrderStatusChangeTime?: BatchOrderStatusChangeTimeRequest
  /**
   * 批次单支付状态
<p>
0：未支付
1：支付中
2：已支付
@see BatchOrderPaymentStatus
   */
  batchOrderPaymentStatusList?: Array<number>
  /**
   * 批次单发货状态
0: 未发货
1: 发货中
2: 已发货
@see BatchOrderDeliveryStatus
   */
  batchOrderDeliveryStatusList?: Array<number>
  /**
   * 批次单价格范围
<p> 查询非0元批次单 begin填0.01
   */
  batchOrderAmountScope?: BigDecimalScopeRequest
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 批次单状态变更时间查询参数
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderStatusChangeTimeRequest {
  /**
   * 未确认
   */
  unConfirmed?: DateScopeRequest
  /**
   * 正常
   */
  normal?: DateScopeRequest
  /**
   * 交易成功
   */
  completed?: DateScopeRequest
  /**
   * 已关闭
   */
  closed?: DateScopeRequest
  /**
   * 提交中
   */
  committing?: DateScopeRequest
  /**
   * 取消处理中
   */
  canceling?: DateScopeRequest
}

/**
 * 批次退货单审批信息查询参数
<AUTHOR>
@date 2022/03/18
 */
export class BatchReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime?: DateScopeRequest
}

/**
 * 批次退货单查询参数
<AUTHOR>
@date 2022/04/19
 */
export class BatchReturnOrderRequest {
  /**
   * 批次退货单号集合
   */
  batchReturnOrderList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: BatchReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: BatchReturnOrderApprovalInfoRequest
  /**
   * 批次退货单关联批次单
   */
  batchOrderInfo?: BatchOrderInfoRequest
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 批次退货单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class BatchReturnOrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: BatchReturnOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 批次退货单关联批次单查询参数
<AUTHOR>
@date 2022/04/19
 */
export class BatchOrderInfoRequest {
  /**
   * 批次单号集合
   */
  batchOrderNoList?: Array<string>
  /**
   * 批次单创建人id集合
   */
  creatorIdList?: Array<string>
  /**
   * 收款账号ID集合
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号集合
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

/**
 * 批次退货单关闭信息
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnCloseReasonRequest {
  /**
   * 批次退货单关闭类型（1：卖家取消 2：卖家拒绝退货 3：买家取消 4：确认失败取消）
@see BatchReturnCloseTypes
   */
  closeTypeList?: Array<number>
}

/**
 * 批次退货单基本信息查询参数
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnOrderBasicDataRequest {
  /**
   * 批次退货单状态
0: 已创建
1: 已确认
2: 取消申请中
3: 退货处理中
4: 退货失败
5: 正在申请退款
6: 已申请退款
7: 退款处理中
8: 退款失败
9: 退货完成
10: 退款完成
11: 退货退款完成
12: 已关闭
@see BatchReturnOrderStatus
   */
  batchReturnOrderStatus?: Array<number>
  /**
   * 批次退货单关闭信息
   */
  batchReturnCloseReason?: BatchReturnCloseReasonRequest
  /**
   * 批次退货单状态变更时间
   */
  batchReturnStatusChangeTime?: BatchReturnOrderStatusChangeTimeRequest
  /**
   * 退款金额范围
<br> 查询非0元  begin填0.01
   */
  refundAmountScope?: BigDecimalScopeRequest
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 批次退货单状态变更时间查询参数
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied?: DateScopeRequest
  /**
   * 批次退货完成时间
<p> 这个参数包含了退货退款完成（批次退货单类型为退货退款）、仅退货完成（批次退货单类型为仅退货）、仅退款完成（批次退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted?: DateScopeRequest
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuRequest {
  /**
   * 指定需要查询的sku属性（不指定的话默认查全部）
   */
  needQuerySkuPropertyList?: Array<string>
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品名称（精确匹配）
   */
  saleTitleList?: Array<string>
  /**
   * 商品名称（模糊查询）
   */
  saleTitleMatchLike?: string
  /**
   * 要从查询结果中剔除的商品ID集合
   */
  notShowCommoditySkuIdList?: Array<string>
  /**
   * 商品售价
   */
  price?: number
  /**
   * 商品上下架信息
   */
  onShelveRequest?: OnShelveRequest
  /**
   * 培训方案信息
   */
  schemeRequest?: SchemeRequest
  /**
   * 商品sku属性查询
   */
  skuPropertyRequest?: SkuPropertyRequest
  /**
   * 商品sku属性查询(组合查询)
！！ 由于mongodb限制 不可与 {skuPropertyRequest}同时使用 ！！
   */
  fixSkuPropertyRequest?: Array<SkuPropertyRequest>
  /**
   * 是否展示资源不可用的商品
   */
  isDisabledResourceShow?: boolean
  /**
   * 是否展示所有资源
（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
   */
  isShowAll?: boolean
  /**
   * 是否存在专题
   */
  existTrainingChannel?: boolean
  /**
   * 专题
   */
  trainingChannelName?: string
  /**
   * 专题id
   */
  trainingChannelIds?: Array<string>
  /**
   * 第三方平台类型ids
   */
  tppTypeIds?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 管理系统平台
   */
  externalTrainingPlatform?: Array<string>
  /**
   * 所属单位ID
   */
  unitIdList?: Array<string>
  /**
   * 收款主体id集合
   */
  payeeIdList?: Array<string>
  /**
   * 商品拓展信息请求入参
   */
  extInfoRequest?: CommoditySkuExtInfoRequest
  /**
   * 购买渠道列表
   */
  purchaseChannelList?: Array<PurchaseChannelRequest>
  /**
   * 是否展示在专题销售渠道
   */
  isShowTrainingChannel?: boolean
}

/**
 * 商品排序参数
<AUTHOR>
@date 2022/01/27
 */
export class CommoditySkuSortRequest {
  /**
   * 用来排序的字段
   */
  sortField?: CommoditySkuSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * <AUTHOR> linq
@date : 2024-11-13 16:04
@description：期别商品查询入参
 */
export class IssueCommoditySkuRequest {
  /**
   * 指定需要查询的sku属性（不指定的话默认查全部）
   */
  needQuerySkuPropertyList?: Array<string>
  /**
   * 期别商品id
   */
  portalCommoditySkuIdList?: Array<string>
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品名称（精确匹配）
   */
  saleTitleList?: Array<string>
  /**
   * 商品名称（模糊查询）
   */
  saleTitleMatchLike?: string
  /**
   * 要从查询结果中剔除的期别商品ID集合
   */
  notShowPortalCommoditySkuIdList?: Array<string>
  /**
   * 要从查询结果中剔除的商品ID集合
   */
  notShowCommoditySkuIdList?: Array<string>
  /**
   * 商品售价
   */
  price?: number
  /**
   * 商品上下架信息
   */
  onShelveRequest?: OnShelveRequest
  /**
   * (弃用)培训方案信息
   */
  schemeRequest?: SchemeRequest
  /**
   * 方案期别信息
   */
  issueRequest?: IssueInfoNestRequest
  /**
   * 是否展示资源不可用的商品
   */
  isDisabledResourceShow?: boolean
  /**
   * 是否展示所有资源
（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
   */
  isShowAll?: boolean
  /**
   * 专题
   */
  trainingChannelName?: string
  /**
   * 专题id
   */
  trainingChannelIds?: Array<string>
  /**
   * 第三方平台类型ids
   */
  tppTypeIds?: Array<string>
  /**
   * 商品方案学时
   */
  period?: number
  /**
   * 门户商品 - 商品sku属性查询
   */
  portalCommoditySkuPropertyRequest?: PortalCommoditySkuPropertyRequest
}

/**
 * 门户商品 - 商品查询条件
<AUTHOR>
@date 2024/04/24
 */
export class PortalCommoditySkuRequest {
  /**
   * 指定需要查询的sku属性（不指定的话默认查全部）
   */
  needQuerySkuPropertyList?: Array<string>
  /**
   * 门户商品 - 商品id
   */
  portalCommoditySkuIdList?: Array<string>
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品名称（精确匹配）
   */
  saleTitleList?: Array<string>
  /**
   * 商品名称（模糊查询）
   */
  saleTitleMatchLike?: string
  /**
   * 门户商品 - 要从查询结果中剔除的商品ID集合
   */
  notShowPortalCommoditySkuIdList?: Array<string>
  /**
   * 要从查询结果中剔除的商品ID集合
   */
  notShowCommoditySkuIdList?: Array<string>
  /**
   * 商品售价
   */
  price?: number
  /**
   * 商品上下架信息
   */
  onShelveRequest?: OnShelveRequest
  /**
   * 培训方案信息
   */
  schemeRequest?: SchemeRequest
  /**
   * 是否展示资源不可用的商品
   */
  isDisabledResourceShow?: boolean
  /**
   * 是否展示所有资源
（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
   */
  isShowAll?: boolean
  /**
   * 专题
   */
  trainingChannelName?: string
  /**
   * 专题id
   */
  trainingChannelIds?: Array<string>
  /**
   * 门户商品 - 商品sku属性查询
   */
  portalCommoditySkuPropertyRequest?: PortalCommoditySkuPropertyRequest
  /**
   * 门户商品 - 来源类型
1-网校商品 2-专题商品
   */
  portalCommoditySkuSourceType?: number
  /**
   * 门户商品 - 来源id(服务商id,专题id)
   */
  portalCommoditySkuSourceId?: string
  /**
   * 第三方平台类型ids
   */
  tppTypeIds?: Array<string>
  /**
   * 商品方案学时
   */
  period?: number
}

/**
 * <AUTHOR> linq
@date : 2025-01-07 14:47
@description：商品拓展信息请求入参
 */
export class CommoditySkuExtInfoRequest {
  /**
   * 资源供应商id集合
   */
  resourceProviderIdList?: Array<string>
}

export class PurchaseChannelRequest {
  /**
   * 渠道类型 | 1、用户web自主购买 2、用户h5自主购买 3、用户小程序自主购买 4、用户公众号自主购买 5、集体缴费 6、管理员导入 7、集体报名个人缴费渠道
@see PurchaseChannelType
   */
  purchaseChannelType?: number
  /**
   * 是否可见
   */
  couldSee?: boolean
  /**
   * 是否可购买
   */
  couldBuy?: boolean
}

/**
 * <AUTHOR> linq
@date : 2025-05-06 10:55
@description : 期别资源信息查询请求入参
 */
export class IssueInfoNestRequest {
  /**
   * 期别ID集合
   */
  issueIdList?: Array<string>
  /**
   * 需要排除的期别ID集合
   */
  excludedIssueIdList?: Array<string>
  /**
   * 培训方案ID集合
   */
  schemeIdList?: Array<string>
  /**
   * 期别名称（模糊查询）
   */
  issueName?: string
  /**
   * 期别名称（精确查询）
   */
  issueNameExact?: string
  /**
   * 期别培训开始时间
   */
  startTrainingTime?: DateScopeRequest
  /**
   * 期别培训结束时间
   */
  endTrainingTime?: DateScopeRequest
  /**
   * 期别报名开始时间
   */
  issueSignUpBeginTime?: DateScopeRequest
  /**
   * 期别报名结束时间
   */
  issueSignUpEndTime?: DateScopeRequest
  /**
   * 方案学时
   */
  period?: DoubleScopeRequest
}

/**
 * 商品上下架相关查询参数
<AUTHOR>
@date 2022/01/25
 */
export class OnShelveRequest {
  /**
   * 商品上下架状态
<br> 0:已下架 1：已上架
   */
  onShelveStatus?: number
}

/**
 * 培训方案相关查询参数
<AUTHOR>
@date 2022/01/25
 */
export class SchemeRequest {
  /**
   * 培训方案ID
   */
  schemeIdList?: Array<string>
  /**
   * 排除的培训方案ID
   */
  excludedSchemeIdList?: Array<string>
  /**
   * 培训方案类型
<br> chooseCourseLearning:选课规则 autonomousCourseLearning:自主学习 trainingCooperation:培训合作
   */
  schemeType?: string
  /**
   * 培训方案名称(模糊查询)
   */
  schemeName?: string
  /**
   * 培训开始时间
   */
  trainingBeginDate?: DateScopeRequest
  /**
   * 培训结束时间
   */
  trainingEndDate?: DateScopeRequest
  /**
   * 方案学时
   */
  period?: DoubleScopeRequest
}

/**
 * <AUTHOR> linq
@date : 2023-12-25 12:08
@description：商品分销授权信息请求参数
 */
export class CommodityAuthInfoRequest {
  /**
   * 商品授权分销商ID
   */
  distributorId?: string
  /**
   * 分销级别
@see DistriButionLevel
   */
  distributionLevel?: number
  /**
   * 上级分销商ID（仅二级分销商有该值，分销合同内的上级分销商ID）
   */
  superiorDistributorId?: string
  /**
   * 商品授权供应商ID
   */
  supplierId?: string
  /**
   * 供应商对接业务员ID（仅订单创建时有该分销商+网校有业务员时才有值）
   */
  salesmanId?: string
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuRequest1 {
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 期别信息
   */
  issueInfo?: IssueInfo1
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 管理系统平台
   */
  externalTrainingPlatform?: Array<string>
  /**
   * 培训机构
   */
  trainingInstitution?: Array<string>
}

/**
 * 发票关联订单查询参数
<AUTHOR>
@date 2022/3/18
 */
export class InvoiceAssociationInfoRequest {
  /**
   * 关联订单类型
0:订单号
1:批次单号
@see AssociationTypes
   */
  associationType?: number
  /**
   * 订单号 | 批次单号
   */
  associationIdList?: Array<string>
  /**
   * 买家信息
   */
  buyerIdList?: Array<string>
  /**
   * 收款账号
   */
  receiveAccountIdList?: Array<string>
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 门户商品sku属性查询参数
<AUTHOR>
@date 2024/04/23
 */
export class PortalCommoditySkuPropertyRequest {
  /**
   * 指定需要查询的sku属性（不指定的话默认查全部）
   */
  needQuerySkuPropertyList?: Array<string>
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 卫生行业-培训对象
   */
  trainingObject?: Array<string>
  /**
   * 卫生行业-岗位类别
   */
  positionCategory?: Array<string>
  /**
   * 工勤行业-技术等级
   */
  jobLevel?: Array<string>
  /**
   * 工勤行业-工种
   */
  jobCategory?: Array<string>
  /**
   * 年级
   */
  grade?: Array<string>
  /**
   * 科目
   */
  subject?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
  /**
   * 门户商品sku-培训年度
   */
  yearForPortal?: Array<string>
  /**
   * 门户商品sku-培训行业
   */
  industryForPortal?: Array<string>
  /**
   * 门户商品sku-所属行业
   */
  belongIndustryForPortal?: Array<string>
  /**
   * 门户商品sku-培训专业
   */
  trainingProfessionalForPortal?: Array<string>
  /**
   * 门户商品sku - 黑龙江药师-证书类型
   */
  certificatesTypeForPortal?: Array<string>
  /**
   * 门户商品sku - 黑龙江药师-执业类别
   */
  practitionerCategoryForPortal?: Array<string>
  /**
   * 资质类别
   */
  qualificationCategory?: Array<string>
  /**
   * 培训形式
   */
  trainingForm?: Array<string>
  /**
   * 门户商品sku-地区
   */
  regionSkuPropertySearchForPortal?: RegionSkuPropertySearchRequest
  /**
   * 门户商品 - 来源类型
1-网校商品 2-专题商品
   */
  portalCommoditySkuSourceType?: number
  /**
   * 门户商品 - 来源id(服务商id,专题id)
   */
  portalCommoditySkuSourceId?: string
  /**
   * 商品名称（精确匹配）
   */
  saleTitleList?: Array<string>
  /**
   * 商品名称（模糊查询）
   */
  saleTitleMatchLike?: string
  /**
   * 商品方案学时
   */
  period?: number
}

/**
 * 地区查询参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertyRequest {
  /**
   * 地区: 省
   */
  province?: string
  /**
   * 地区: 市
   */
  city?: string
  /**
   * 地区: 区县
   */
  county?: string
}

/**
 * 地区查询请求参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertySearchRequest {
  /**
   * 地区匹配方式
<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
@see RegionSearchType
   */
  regionSearchType?: number
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest>
}

/**
 * 商品sku属性查询参数
<AUTHOR>
@date 2022/01/25
 */
export class SkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 卫生行业-培训对象
   */
  trainingObject?: Array<string>
  /**
   * 卫生行业-岗位类别
   */
  positionCategory?: Array<string>
  /**
   * 工勤行业-技术等级
   */
  jobLevel?: Array<string>
  /**
   * 工勤行业-工种
   */
  jobCategory?: Array<string>
  /**
   * 年级
   */
  grade?: Array<string>
  /**
   * 科目
   */
  subject?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
  /**
   * 专题id
   */
  trainingChannelIds?: Array<string>
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType?: Array<string>
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory?: Array<string>
  /**
   * 资质类别
   */
  qualificationCategory?: Array<string>
  /**
   * 培训形式
   */
  trainingForm?: Array<string>
}

export class IssueInfo1 {
  /**
   * 期别id
   */
  issueId?: string
  /**
   * 期别名称
   */
  issueName?: string
  /**
   * 培训编号
   */
  issueNum?: string
  /**
   * 培训开始时间
   */
  trainStartTime?: string
  /**
   * 培训结束时间
   */
  trainEndTime?: string
  /**
   * 当前期别来源类型。下单：SUB_ORDER 换班：EXCHANGE_ORDER 换期:CHANGE_ISSUE
   */
  sourceType?: string
  /**
   * 期别来源类型id 1:子订单号：2：换货单号：3：旧的期别参训资格D
   */
  sourceId?: string
}

/**
 * 换货单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ExchangeOrderRequest {
  /**
   * 换货单号集合
   */
  exchangeOrderNoList?: Array<string>
  /**
   * 换货单关联子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 换货单关联批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 换货单关联子订单买家ID集合
   */
  buyerIdList?: Array<string>
  /**
   * 商品信息: 任一匹配原始商品和新商品
   */
  commodity?: CommoditySkuRequest1
  /**
   * 换货单状态
<p>
0: 申请换货
1: 取消中
2: 退货中
3: 退货失败
4: 申请发货
5: 发货中
6: 发货失败
7: 换货完成
8: 已关闭
@see ExchangeOrderStatus
   */
  statusList?: Array<number>
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 换货单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class ExchangeOrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: ExchangeOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 线下发票查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceRequest {
  /**
   * 发票ID集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基本信息
   */
  basicData?: OfflineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfo?: InvoiceAssociationInfoRequest
  /**
   * 发票配送信息
   */
  invoiceDeliveryInfo?: OfflineInvoiceDeliveryInfoRequest
  /**
   * 所属单位id集合
   */
  unitIds?: Array<string>
  /**
   * 收款账号id
   */
  receiveAccountId?: Array<string>
  /**
   * 期别名称
   */
  issueId?: Array<string>
}

/**
 * 发票排序条件
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceSortRequest {
  /**
   * 用于排序的字段
   */
  field?: OfflineInvoiceSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy1
}

/**
 * 配送地址信息
<AUTHOR>
@date 2022/05/07
 */
export class DeliveryAddressRequest {
  /**
   * 收件人
   */
  consignee?: string
}

/**
 * 配送状态变更时间查询参数
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryStatusChangeTimeRequest {
  /**
   * 未就绪
   */
  unReady?: DateScopeRequest
  /**
   * 已就绪
   */
  ready?: DateScopeRequest
  /**
   * 已配送
   */
  shipped?: DateScopeRequest
  /**
   * 已自取
   */
  taken?: DateScopeRequest
}

/**
 * 快递信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class ExpressRequest {
  /**
   * 快递单号
   */
  expressNo?: string
}

/**
 * 发票开票状态变更时间记录查询参数
<AUTHOR>
@date 2022/04/06
 */
export class InvoiceBillStatusChangTimeRequest {
  /**
   * 发票申请开票时间
   */
  unBillDateScope?: DateScopeRequest
  /**
   * 发票开票时间
   */
  successDateScope?: DateScopeRequest
}

/**
 * 线下发票基本信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceTypeList?: Array<number>
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory?: Array<number>
  /**
   * 发票状态
1:正常
2:作废
@see InvoiceStatus
   */
  invoiceStatus?: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime?: InvoiceStatusChangeTimeRequest
  /**
   * 发票开票状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  billStatusList?: Array<number>
  /**
   * 发票开票状态变更时间记录
   */
  billStatusChangTime?: InvoiceBillStatusChangTimeRequest
  /**
   * 发票是否冻结
   */
  freeze?: boolean
  /**
   * 发票号集合
   */
  invoiceNoList?: Array<string>
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
}

/**
 * 线下发票配送信息
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceDeliveryInfoRequest {
  /**
   * 配送状态
0:未就绪 1：已就绪 2：已自取 3：已配送
@see OfflineDeliveryStatus
   */
  deliveryStatusList?: Array<number>
  /**
   * 配送状态变更时间记录 or 匹配
0:未就绪 1：已就绪 2：已自取 3：已配送
key值 {@link OfflineDeliveryStatus}
   */
  deliveryStatusChangeTime?: DeliveryStatusChangeTimeRequest
  /**
   * 配送方式
0:无 1：自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethodList?: Array<number>
  /**
   * 快递信息
   */
  express?: ExpressRequest
  /**
   * 自取信息
   */
  takeResult?: TakeResultRequest
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddressRequest
}

/**
 * 取件信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class TakeResultRequest {
  /**
   * 领取人
   */
  takePerson?: string
}

/**
 * 发票查询参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceRequest {
  /**
   * 发票id集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基础信息查询参数
   */
  basicData?: OnlineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfoList?: Array<InvoiceAssociationInfoRequest>
  /**
   * 蓝票票据查询参数
   */
  blueInvoiceItem?: OnlineInvoiceItemRequest
  /**
   * 红票票据查询参数
   */
  redInvoiceItem?: OnlineInvoiceItemRequest
  /**
   * 所属单位id集合
   */
  unitIds?: Array<string>
  /**
   * 收款账号id
   */
  receiveAccountId?: Array<string>
  /**
   * 期别名称
   */
  issueId?: Array<string>
}

/**
 * 发票排序参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceSortRequest {
  /**
   * 需要排序的字段
   */
  field?: OnlineInvoiceSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 发票开具状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class BillStatusChangeTimeRequest {
  /**
   * 未开具
   */
  unBill?: DateScopeRequest
  /**
   * 开票中
   */
  billing?: DateScopeRequest
  /**
   * 开票成功
   */
  success?: DateScopeRequest
  /**
   * 开票失败
   */
  failure?: DateScopeRequest
}

/**
 * 发票状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class InvoiceStatusChangeTimeRequest {
  /**
   * 正常
   */
  normal?: DateScopeRequest
  /**
   * 作废
   */
  invalid?: DateScopeRequest
}

/**
 * 发票基础信息查询参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceType?: number
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategoryList?: Array<number>
  /**
   * 发票状态变更时间
@see InvoiceStatus
   */
  invoiceStatusChangeTime?: InvoiceStatusChangeTimeRequest
  /**
   * 发票状态
1：正常 2：作废
@see InvoiceStatus
   */
  invoiceStatusList?: Array<number>
  /**
   * 蓝票票据开具状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  blueInvoiceItemBillStatusList?: Array<number>
  /**
   * 红票票据开具状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  redInvoiceItemBillStatusList?: Array<number>
  /**
   * 发票是否已冲红
   */
  flushed?: boolean
  /**
   * 发票是否已生成红票票据
   */
  redInvoiceItemExist?: boolean
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
  /**
   * 发票是否冻结
   */
  freeze?: boolean
}

/**
 * 发票票据
<AUTHOR>
@date 2022/03/18
 */
export class OnlineInvoiceItemRequest {
  /**
   * 票据开具状态变更时间
   */
  billStatusChangeTime?: BillStatusChangeTimeRequest
  /**
   * 发票号码
   */
  billNoList?: Array<string>
}

export class IssueLogRequest {
  /**
   * 子订单号
   */
  subOrderNoList?: Array<string>
  /**
   * 买家id
   */
  buyerId?: string
  /**
   * 商品sku查询
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 商品名称（模糊查询）
   */
  saleTitle?: string
  /**
   * 期别名称（模糊查询）
   */
  issueName?: string
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class OrderRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 子订单退货状态
0: 未退货
1: 退货申请中
2: 退货中
3: 退货成功
4: 退款中
5: 退款成功
@see SubOrderReturnStatus
   */
  subOrderReturnStatus?: Array<number>
  /**
   * 订单基本信息查询参数
   */
  orderBasicData?: OrderBasicDataRequest
  /**
   * 子订单基本信息查询参数
   */
  subOrderBasicData?: SubOrderBasicDataRequest
  /**
   * 订单支付信息查询
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 买家查询参数
   */
  buyerIdList?: Array<string>
  /**
   * 发货商品信息
   */
  deliveryCommodity?: CommoditySkuRequest1
  /**
   * 现有商品信息
   */
  currentCommodity?: CommoditySkuRequest1
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 需要排除的销售渠道
0-自营 1-分销 2专题
   */
  excludeSaleChannels?: Array<number>
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 华医网卡类型id
   */
  cardTypeId?: string
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 订单属性组合查询
   */
  orderFixQuery?: OrderFixQueryRequest
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
  /**
   * 管理系统平台
   */
  externalTrainingPlatform?: Array<string>
  /**
   * 单位id集合
   */
  unitIds?: Array<string>
  /**
   * 期别id
   */
  issueId?: Array<string>
  /**
   * 培训计划ID，例如补贴性培训平台和补贴管理系统对接
   */
  policyTrainingSchemeIdList?: Array<string>
  /**
   * 申报单位统一信用代码，精确匹配
   */
  declarationUnitCodeList?: Array<string>
  /**
   * 结算状态
@see com.fjhb.ms.trade.query.common.constants.SettlementStatusConstants
   */
  settlementStatus?: number
  /**
   * 结算时间
   */
  settlementDate?: DateScopeRequest
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: OrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 订单基本信息查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderBasicDataRequest {
  /**
   * 订单类型
1:常规订单 2:批次关联订单
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType?: number
  /**
   * 批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 订单状态
<br> 1:正常 2：交易完成 3：交易关闭
@see OrderStatus
   */
  orderStatusList?: Array<number>
  /**
   * 订单支付状态
<br> 0:未支付 1：支付中 2：已支付
@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
   */
  orderPaymentStatusList?: Array<number>
  /**
   * 订单发货状态
<br> 0:未发货 1：发货中 2：已发货
@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
   */
  orderDeliveryStatusList?: Array<number>
  /**
   * 订单状态变更时间
   */
  orderStatusChangeTime?: OrderStatusChangeTimeRequest
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 需要排除的购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  excludeChannelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
  /**
   * 订单价格范围
<br> 查询非0元订单 begin填0.01
   */
  orderAmountScope?: BigDecimalScopeRequest
}

/**
 * <AUTHOR> linq
@date : 2024-09-05 18:18
@description：订单属性组合查询
 */
export class OrderFixQueryRequest {
  /**
   * 需要排除的购买渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  excludeChannelTypesList?: Array<number>
  /**
   * 需要排除的销售渠道
   */
  excludeSaleChannels?: Array<number>
}

/**
 * 订单支付信息相关查询参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderPayInfoRequest {
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

/**
 * 订单状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderStatusChangeTimeRequest {
  /**
   * 订单处于正常状态时间范围(创建时间范围)
   */
  normalDateScope?: DateScopeRequest
  /**
   * 订单创建时间范围
   */
  completedDatesScope?: DateScopeRequest
}

/**
 * 子订单基本信息查询参数
<AUTHOR>
@date 2023/12/25
 */
export class SubOrderBasicDataRequest {
  /**
   * 优惠类型
@see DiscountType
   */
  discountType?: number
  /**
   * 优惠来源ID | 优惠申请单ID
   */
  discountSourceId?: string
  /**
   * 是否使用优惠
   */
  useDiscount?: boolean
  /**
   * 商品分销授权信息
   */
  commodityAuthInfo?: CommodityAuthInfoRequest
}

/**
 * 退货单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderRequest {
  /**
   * 单位id集合
   */
  unitIdList?: Array<string>
  /**
   * 退货单号
   */
  returnOrderNoList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: ReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: ReturnOrderApprovalInfoRequest
  /**
   * 退货商品id集合
   */
  returnCommoditySkuIdList?: Array<string>
  /**
   * 退货商品查询条件
   */
  returnCommodity?: CommoditySkuRequest1
  /**
   * 退款商品id集合
   */
  refundCommoditySkuIdList?: Array<string>
  /**
   * 退款商品查询条件
   */
  refundCommodity?: CommoditySkuRequest1
  /**
   * 退货单关联子订单查询参数
   */
  subOrderInfo?: SubOrderInfoRequest
  /**
   * 商品分销授权信息
   */
  commodityAuthInfo?: CommodityAuthInfoRequest
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class ReturnSortRequest {
  /**
   * 需要排序的字段
   */
  field?: ReturnOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 退货单关联子订单的主订单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class OrderInfoRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 关联批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 买家id集合
   */
  buyerIdList?: Array<string>
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 原始订单交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 培训计划ID，例如补贴性培训平台和补贴管理系统对接
   */
  policyTrainingSchemeIdList?: Array<string>
  /**
   * 申报单位统一信用代码，精确匹配
   */
  declarationUnitCodeList?: Array<string>
}

/**
 * 退货单关闭信息
<AUTHOR>
@date 2022年4月11日 11:33:35
 */
export class ReturnCloseReasonRequest {
  /**
   * 退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
@see ReturnOrderCloseTypes
   */
  closeTypeList?: Array<number>
}

/**
 * 退货单审批信息查询参数
<AUTHOR>
@date 2022/03/18
 */
export class ReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime?: DateScopeRequest
}

/**
 * 退货单基本信息查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderBasicDataRequest {
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   */
  returnOrderStatus?: Array<number>
  /**
   * 退货单类型
1-仅退货
2-仅退款
3-退货并退款
4-部分退货
5-部分退款
6-部分退货并部分退款
7-部分退货并全额退款
8-全部退货并部分退款
   */
  returnOrderTypes?: Array<number>
  /**
   * 退货单申请来源类型
SUB_ORDER
BATCH_RETURN_ORDER
@see ReturnOrderApplySourceTypes
   */
  applySourceType?: string
  /**
   * 来源ID集合
   */
  applySourceIdList?: Array<string>
  /**
   * 退货单关闭信息
   */
  returnCloseReason?: ReturnCloseReasonRequest
  /**
   * 退货单状态变更时间
   */
  returnStatusChangeTime?: ReturnOrderStatusChangeTimeRequest
  /**
   * 退款金额范围
<br> 查询非0元  begin填0.01
   */
  refundAmountScope?: BigDecimalScopeRequest
}

/**
 * 退货单状态变更时间查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied?: DateScopeRequest
  /**
   * 退货单完成时间
<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted?: DateScopeRequest
}

/**
 * <AUTHOR>
@date 2022/03/24
 */
export class SubOrderInfoRequest {
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 订单查询参数
   */
  orderInfo?: OrderInfoRequest
  /**
   * 子订单优惠类型
@see DiscountType
   */
  discountType?: number
  /**
   * 子订单是否使用优惠
   */
  useDiscount?: boolean
}

export class UserEntity {
  userId: string
}

export class DiscountPolicyModel {
  discountPolicyId: string
  discountId: number
}

export class RegionModel {
  regionId: string
  province: string
  city: string
  county: string
  path: string
}

export class UserModel {
  userId: string
}

/**
 * 批次单返回模型
<AUTHOR>
@date 2022/4/16
 */
export class BatchOrderResponse {
  /**
   * 批次单号
   */
  batchOrderNo: string
  /**
   * 批次单基本信息
   */
  basicData: BatchOrderBasicDataResponse
  /**
   * 支付信息
   */
  payInfo: PayInfoResponse
  /**
   * 创建人
   */
  creator: UserResponse
  /**
   * 是否已经申请发票
   */
  isInvoiceApplied: boolean
  /**
   * 发票申请信息
   */
  invoiceApplyInfo: InvoiceApplyInfoResponse
}

export class BatchRegistrationSourceResponse {
  /**
   * 销售渠道
0-自营 1-分销 2专题
   */
  saleChannel: number
  /**
   * 销售渠道id
   */
  saleChannelId: string
  /**
   * 销售渠道名称
   */
  saleName: string
}

/**
 * 批次单基本信息
<AUTHOR>
@date 2022/4/17
 */
export class BatchOrderBasicDataResponse {
  /**
   * 批次单类型
<p>
0；批次缴费批次单
1；个人缴费批次单
2；无需缴费批次单
@see BatchOrderTypes
   */
  batchOrderType: number
  /**
   * 终端类型
<p>
Web: Web端
H5: H5端
IOS: IOS端
Android: 安卓端
WechatMini: 微信小程序
WechatOfficial: 微信公众号
ExternalSystemManage: 外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCode: string
  /**
   * 批次单金额
   */
  amount: number
  /**
   * 批次单的批次订单数量
   */
  orderForBatchCount: number
  /**
   * 批次单状态
0: 未确认，批次单初始状态
1: 正常
2: 交易完成
3: 交易关闭
4: 提交处理中 提交处理完成后，变更为NORMAl
5: 取消处理中
@see BatchOrderStatus
   */
  batchOrderStatus: number
  /**
   * 批次单状态变更时间记录
   */
  batchOrderStatusChangeTime: BatchOrderStatusChangeTimeResponse
  /**
   * 批次单支付状态
<p>
0：未支付
1：支付中
2：已支付
@see BatchOrderPaymentStatus
   */
  paymentStatus: number
  /**
   * 批次单支付状态变更时间
   */
  paymentStatusChangeTime: BatchOrderPaymentStatusChangeTimeResponse
  /**
   * 批次单发货状态
0: 未发货
1: 发货中
2: 已发货
@see BatchOrderDeliveryStatus
   */
  deliveryStatus: number
  /**
   * 批次单发货状态变更时间
   */
  deliveryStatusChangeTime: BatchOrderDeliveryStatusChangeTimeResponse
  /**
   * 批次单退货状态
0: 未退货
1: 退货中
2: 已部分退货
3: 退货完成
@see BatchOrderReturnStatus
   */
  batchOrderReturnStatus: number
  /**
   * 批次单退款状态
0: 未退款
1: 退款中
2: 已部分退货
3: 退货完成
@see BatchOrderRefundStatus
   */
  batchOrderRefundStatus: number
  /**
   * 批次单关闭原因
   */
  batchOrderCloseReason: BatchOrderCloseReasonResponse
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 销售渠道id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  /**
   * 销售全路径
   */
  salePathList: Array<SalePathResponse>
}

/**
 * 批次单交易关闭原因
<AUTHOR>
@date 2022/2/14
 */
export class BatchOrderCloseReasonResponse {
  /**
   * 交易关闭类型
<p>
0：未关闭
1：买家关闭
2：系统关闭
@see BatchOrderCloseTypes
   */
  closedType: number
  /**
   * 交易关闭原因ID
   */
  reasonId: string
  /**
   * 交易关闭原因说明
   */
  reason: string
  /**
   * 取消操作人
   */
  cancelUser: UserResponse
}

/**
 * 批次单发货状态变更时间
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderDeliveryStatusChangeTimeResponse {
  /**
   * 未确认
   */
  none: string
  /**
   * 发货中
   */
  delivering: string
  /**
   * 已发货
   */
  delivered: string
}

/**
 * 批次单支付状态变更时间
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderPaymentStatusChangeTimeResponse {
  /**
   * 未确认
   */
  none: string
  /**
   * 支付中
   */
  paying: string
  /**
   * 已支付
   */
  paid: string
}

/**
 * 批次单状态变更时间
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderStatusChangeTimeResponse {
  /**
   * 未确认
   */
  unConfirmed: string
  /**
   * 正常
   */
  normal: string
  /**
   * 交易成功
   */
  completed: string
  /**
   * 已关闭
   */
  closed: string
  /**
   * 提交中
   */
  committing: string
  canceling: string
}

/**
 * 批次退货单
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnOrderResponse {
  /**
   * 批次退货单号
   */
  batchReturnOrderNo: string
  /**
   * 批次退货单基本信息
   */
  basicData: BatchReturnOrderBasicDataResponse
  /**
   * 批次退货单关联批次单信息
   */
  batchOrderInfo: BatchOrderInfoResponse
  /**
   * 批次退货单是否需要人工审批
   */
  needManualApprove: boolean
  /**
   * 批次退货单审批信息
   */
  approvalInfo: BatchReturnApprovalInfoResponse
  /**
   * 退款确认人
   */
  confirmUser: UserResponse
  /**
   * 批次退货单关联退款单信息
   */
  refundInfo: RefundInfoResponse
}

/**
 * 批次退货单关联批次单信息
<AUTHOR>
@date 2022/04/19
 */
export class BatchOrderInfoResponse {
  /**
   * 批次单号
   */
  batchOrderNo: string
  /**
   * 批次单支付信息
   */
  paymentInfo: PaymentInfoResponse
  /**
   * 批次单创建人
   */
  creator: UserResponse
}

/**
 * 批次退货审批信息
<AUTHOR>
@date 2022/03/18
 */
export class BatchReturnApprovalInfoResponse {
  /**
   * 审批状态（0：未审批 1：已审批）
@see BatchReturnApprovalReportStatus
   */
  approveStatus: number
  /**
   * 审批结果（-1：无 0：拒绝 1：同意）
@see BatchReturnApprovalReportResults
   */
  approveResult: number
  /**
   * 审批人
   */
  approveUser: UserResponse
  /**
   * 审批意见
   */
  approveComment: string
  /**
   * 审批时间
   */
  approveTime: string
}

/**
 * 批次退货申请信息返回值
<AUTHOR>
@date 2022/03/24
 */
export class BatchReturnOrderApplyInfoResponse {
  /**
   * 申请人
   */
  applyUser: UserResponse
  /**
   * 申请原因内容id
   */
  reasonId: string
  /**
   * 申请原因内容
   */
  reasonContent: string
  /**
   * 申请描述
   */
  description: string
}

/**
 * 批次退货单基本信息
<AUTHOR>
@date 2022/04/19
 */
export class BatchReturnOrderBasicDataResponse {
  /**
   * 批次退货单类型
1: 仅退货
2: 仅退款
3: 退货退款
@see BatchReturnOrderTypes
   */
  batchReturnOrderType: number
  /**
   * 退款总额
   */
  refundAmount: number
  /**
   * 批次退货单的退货单数量
   */
  returnOrderCount: number
  /**
   * 批次退货单状态
0: 已创建
1: 已确认
2: 取消申请中
3: 退货处理中
4: 退货失败
5: 正在申请退款
6: 已申请退款
7: 退款处理中
8: 退款失败
9: 退货完成
10: 退款完成
11: 退货退款完成
12: 已关闭
@see BatchReturnOrderStatus
   */
  batchReturnOrderStatus: number
  /**
   * 批次退货单状态变更时间
   */
  batchReturnOrderStatusChangeTime: BatchReturnOrderStatusChangeTimeResponse
  /**
   * 批次退货单申请信息
   */
  applyInfo: BatchReturnOrderApplyInfoResponse
  /**
   * 批次退货单关闭信息
   */
  closeReason: BatchReturnOrderCloseReasonResponse
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 销售渠道id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  salePathList: Array<SalePathResponse>
}

/**
 * 批次退货单关闭原因
<AUTHOR>
@date 2022/03/29
 */
export class BatchReturnOrderCloseReasonResponse {
  /**
   * 批次退货单关闭类型（（1：卖家取消 2：卖家拒绝退货 3：买家取消 4：确认失败取消））
@see BatchReturnCloseTypes
   */
  closeType: number
  /**
   * 退货单取消人
   */
  cancelUser: UserResponse
  /**
   * 取消原因
   */
  cancelReason: string
  /**
   * 确认失败信息
   */
  confirmFailureMessage: string
}

/**
 * 退货单状态变更时间
<AUTHOR>
@date 2022/03/23
 */
export class BatchReturnOrderStatusChangeTimeResponse {
  /**
   * 已创建
   */
  created: string
  /**
   * 已确认
   */
  confirmed: string
  /**
   * 申请退货取消处理中时间
   */
  cancelApplying: string
  /**
   * 退货处理中时间
   */
  returning: string
  /**
   * 退货失败时间
   */
  returnFailed: string
  /**
   * 正在申请退款时间
   */
  refundApplying: string
  /**
   * 已申请退款时间
   */
  refundApplied: string
  /**
   * 退款处理中时间
   */
  refunding: string
  /**
   * 退款失败
   */
  refundFailed: string
  /**
   * 退货完成时间
   */
  returned: string
  /**
   * 退款完成时间
   */
  refunded: string
  /**
   * 退货退款完成时间
   */
  returnedAndRefunded: string
  /**
   * 退货单完成时间
   */
  returnCompleted: string
  /**
   * 已关闭时间
   */
  closed: string
}

/**
 * 前台商品返回值模型
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuForestageResponse {
  /**
   * 商品id
   */
  commoditySkuId: string
  /**
   * 培训班商品基本信息
   */
  commodityBasicData: CommodityBasicDataResponse
  /**
   * 商品属性信息
   */
  skuProperty: CommoditySkuPropertyResponse
  /**
   * 用户商品拥有信息
   */
  possessionInfo: UserPossessionInfoResponse
  /**
   * 当前渠道配置信息
   */
  commodityPurchaseChannelConfig: PurchaseChannelConfigResponse
  /**
   * 上下架信息
   */
  onShelve: OnShelveResponse
  /**
   * 商品关联资源信息
   */
  resource: ResourceResponse
  /**
   * 专题
   */
  trainingChannels: Array<CommodityTrainingChannelResponse>
  /**
   * 第三方平台类型
   */
  tppTypeId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属单位名称
   */
  unitName: string
}

/**
 * <AUTHOR> linq
@date : 2024-11-13 09:35
@description：期别商品查询gql返回值
 */
export class IssueCommoditySkuResponse {
  /**
   * 原商品信息
   */
  originCommodityInfo: CommoditySkuForestageResponse
  /**
   * 门户商品信息
   */
  portalCommoditySkuForestageResponse: PortalCommoditySkuForestageResponse
  /**
   * 面网授学习方案期别资源信息
   */
  issueResource: SchemeIssueResourceResponse
}

/**
 * 门户商品 - 前台商品返回值模型
<AUTHOR>
@date 2024/04/23
 */
export class PortalCommoditySkuForestageResponse {
  /**
   * 门户商品-商品id
   */
  portalCommoditySkuId: string
  /**
   * 门户商品-商品名称
   */
  portalSaleTitle: string
  /**
   * 门户商品-来源类型
1-网校商品 2-专题商品
   */
  portalCommoditySkuSourceType: number
  /**
   * 门户商品-来源id(服务商id,专题id)
   */
  portalCommoditySkuSourceId: string
  /**
   * 门户商品-商品属性信息
   */
  skuProperty: PortalCommoditySkuPropertyResponse
  /**
   * 门户商品-销售数量
   */
  portalCommoditySoldCount: number
}

/**
 * 门户商品sku属性列表返回值
字段名需要sku属性名保持一致，否则会影响needField的使用
<AUTHOR>
@date 2024/04/23
 */
export class PortalCommoditySkuPropertyListResponse {
  /**
   * 门户商品sku - 培训年度
   */
  yearForPortal: Array<SkuPropertyResponse>
  /**
   * 门户商品sku - 培训行业
   */
  industryForPortal: Array<SkuPropertyResponse>
  /**
   * 门户商品sku - 培训专业
   */
  trainingProfessionalForPortal: Array<SkuPropertyResponse>
  /**
   * 门户商品sku - 地区 - 省
   */
  provinceForPortal: Array<SkuPropertyResponse>
  /**
   * 门户商品sku - 地区 - 市
   */
  cityForPortal: Array<SkuPropertyResponse>
  /**
   * 门户商品sku - 地区 - 县
   */
  countyForPortal: Array<SkuPropertyResponse>
  /**
   * 门户商品sku - 地区 - 最末一级
   */
  regionForPortal: Array<SkuPropertyResponse>
  /**
   * 门户商品sku - 所属行业
   */
  belongIndustryForPortal: Array<SkuPropertyResponse>
  /**
   * 门户商品sku - 黑龙江药师-证书类型
   */
  certificatesTypeForPortal: Array<SkuPropertyResponse>
  /**
   * 门户商品sku - 黑龙江药师-执业类别
   */
  practitionerCategoryForPortal: Array<SkuPropertyResponse>
  /**
   * 门户商品学时
   */
  periodForPortal: Array<SkuPropertyResponse>
  /**
   * 资质类别
   */
  qualificationCategory: Array<SkuPropertyResponse>
  /**
   * 年度
   */
  year: Array<SkuPropertyResponse>
  /**
   * 地区 - 省
   */
  province: Array<SkuPropertyResponse>
  /**
   * 地区 - 市
   */
  city: Array<SkuPropertyResponse>
  /**
   * 地区 - 县
   */
  county: Array<SkuPropertyResponse>
  /**
   * 行业
   */
  industry: Array<SkuPropertyResponse>
  /**
   * 科目类型
   */
  subjectType: Array<SkuPropertyResponse>
  /**
   * 培训类别
   */
  trainingCategory: Array<SkuPropertyResponse>
  /**
   * 培训专业
   */
  trainingProfessional: Array<SkuPropertyResponse>
  /**
   * 卫生行业-培训对象
   */
  trainingObject: Array<SkuPropertyResponse>
  /**
   * 卫生行业-岗位类别
   */
  positionCategory: Array<SkuPropertyResponse>
  /**
   * 工勤行业-技术等级
   */
  jobLevel: Array<SkuPropertyResponse>
  /**
   * 工勤行业-工种
   */
  jobCategory: Array<SkuPropertyResponse>
  /**
   * 年级
   */
  grade: Array<SkuPropertyResponse>
  /**
   * 科目
   */
  subject: Array<SkuPropertyResponse>
  /**
   * 学段
   */
  learningPhase: Array<SkuPropertyResponse>
  /**
   * 学科
   */
  discipline: Array<SkuPropertyResponse>
  /**
   * 地区 - 最末一级
   */
  region: Array<SkuPropertyResponse>
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType: Array<SkuPropertyResponse>
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory: Array<SkuPropertyResponse>
  /**
   * 培训形式
   */
  trainingForm: Array<SkuPropertyResponse>
}

/**
 * <AUTHOR> linq
@date : 2024-04-24 19:12
@description：门户商品 - response
 */
export class PortalCommoditySkuResponse {
  /**
   * 原商品信息
   */
  originCommodityInfo: CommoditySkuForestageResponse
  /**
   * 门户商品信息
   */
  portalCommoditySkuForestageResponse: PortalCommoditySkuForestageResponse
}

/**
 * <AUTHOR> linq
@date : 2024-11-08 19:25
@description：商品资源信息 - 面网授学习方案期别
 */
export class SchemeIssueResourceResponse {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 方案学时
   */
  period: number
  /**
   * 期别id
   */
  issueId: string
  /**
   * 期别已报名人数
   */
  registeredCount: number
  /**
   * 期别最大报名人数
   */
  maxRegisterCount: number
  /**
   * 期别报到时段
   */
  issueReportDateScope: DateScopeResponse
  /**
   * 期别报名时段
   */
  issueSignUpDateScope: DateScopeResponse
  /**
   * 期别培训时段
   */
  issueTrainingDateScope: DateScopeResponse
  /**
   * 期别名称
   */
  issueName: string
  /**
   * 培训点ID
   */
  trainingPointId: string
}

/**
 * 商品sku属性列表返回值
字段名需要sku属性名保持一致，否则会影响needField的使用
<AUTHOR>
@date 2022/01/25
 */
export class SkuPropertyListResponse {
  /**
   * 年度
   */
  year: Array<SkuPropertyResponse>
  /**
   * 地区 - 省
   */
  province: Array<SkuPropertyResponse>
  /**
   * 地区 - 市
   */
  city: Array<SkuPropertyResponse>
  /**
   * 地区 - 县
   */
  county: Array<SkuPropertyResponse>
  /**
   * 行业
   */
  industry: Array<SkuPropertyResponse>
  /**
   * 科目类型
   */
  subjectType: Array<SkuPropertyResponse>
  /**
   * 培训类别
   */
  trainingCategory: Array<SkuPropertyResponse>
  /**
   * 培训专业
   */
  trainingProfessional: Array<SkuPropertyResponse>
  /**
   * 卫生行业-培训对象
   */
  trainingObject: Array<SkuPropertyResponse>
  /**
   * 卫生行业-岗位类别
   */
  positionCategory: Array<SkuPropertyResponse>
  /**
   * 工勤行业-技术等级
   */
  jobLevel: Array<SkuPropertyResponse>
  /**
   * 工勤行业-工种
   */
  jobCategory: Array<SkuPropertyResponse>
  /**
   * 年级
   */
  grade: Array<SkuPropertyResponse>
  /**
   * 科目
   */
  subject: Array<SkuPropertyResponse>
  /**
   * 学段
   */
  learningPhase: Array<SkuPropertyResponse>
  /**
   * 学科
   */
  discipline: Array<SkuPropertyResponse>
  /**
   * 地区 - 最末一级
   */
  region: Array<SkuPropertyResponse>
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType: Array<SkuPropertyResponse>
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory: Array<SkuPropertyResponse>
  /**
   * 培训形式
   */
  trainingForm: Array<SkuPropertyResponse>
}

/**
 * 商品基本信息返回值
<AUTHOR>
@date 2022/1/25
 */
export class CommodityBasicDataResponse {
  /**
   * 商品销售标题
   */
  saleTitle: string
  /**
   * 商品价格
   */
  price: number
  /**
   * 商品封面图路径
   */
  commodityPicturePath: string
}

/**
 * 商品sku属性返回值
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuPropertyResponse {
  /**
   * 年度
   */
  year: SkuPropertyResponse
  /**
   * 地区 - 省
   */
  province: SkuPropertyResponse
  /**
   * 地区 - 市
   */
  city: SkuPropertyResponse
  /**
   * 地区 - 县
   */
  county: SkuPropertyResponse
  /**
   * 行业
   */
  industry: SkuPropertyResponse
  /**
   * 科目类型
   */
  subjectType: SkuPropertyResponse
  /**
   * 培训类别
   */
  trainingCategory: SkuPropertyResponse
  /**
   * 培训专业
   */
  trainingProfessional: SkuPropertyResponse
  /**
   * 技术等级
   */
  technicalGrade: SkuPropertyResponse
  /**
   * 卫生行业-培训对象
   */
  trainingObject: SkuPropertyResponse
  /**
   * 卫生行业-岗位类别
   */
  positionCategory: SkuPropertyResponse
  /**
   * 工勤行业-技术等级
   */
  jobLevel: SkuPropertyResponse
  /**
   * 工勤行业-工种
   */
  jobCategory: SkuPropertyResponse
  /**
   * 年级
   */
  grade: SkuPropertyResponse
  /**
   * 科目
   */
  subject: SkuPropertyResponse
  /**
   * 学段
   */
  learningPhase: SkuPropertyResponse
  /**
   * 学科
   */
  discipline: SkuPropertyResponse
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType: SkuPropertyResponse
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory: SkuPropertyResponse
  /**
   * 资质类别
   */
  qualificationCategory: SkuPropertyResponse
  /**
   * 培训形式
   */
  trainingForm: SkuPropertyResponse
}

export class CommodityTrainingChannelResponse {
  /**
   * 专题id
   */
  trainingChannelId: string
  /**
   * 专题名称
   */
  trainingChannelName: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 商品上下架信息
<AUTHOR>
@date 2022/1/25
 */
export class OnShelveResponse {
  /**
   * 商品当前上架状态
<br> 0:已下架 1：已上架
@see CommoditySkuShelveStatus
   */
  shelveStatus: number
  /**
   * 最新上架时间
   */
  lastOnShelveTime: string
  /**
   * 最新下架时间
   */
  offShelveTime: string
  /**
   * 最新计划上架时间
   */
  onShelvePlanTime: string
  /**
   * 最新计划下架时间
   */
  offShelvePlanTime: string
  /**
   * 发布时间
   */
  publishTime: string
}

/**
 * 门户商品 - 商品sku属性返回值
<AUTHOR>
@date 2024/04/23
 */
export class PortalCommoditySkuPropertyResponse {
  /**
   * 门户商品 - 年度
   */
  yearForPortal: SkuPropertyResponse
  /**
   * 门户商品 - 地区 - 省
   */
  provinceForPortal: SkuPropertyResponse
  /**
   * 门户商品 - 地区 - 市
   */
  cityForPortal: SkuPropertyResponse
  /**
   * 门户商品 - 地区 - 县
   */
  countyForPortal: SkuPropertyResponse
  /**
   * 门户商品 - 行业
   */
  industryForPortal: SkuPropertyResponse
  /**
   * 门户商品 - 培训专业
   */
  trainingProfessionalForPortal: SkuPropertyResponse
  /**
   * 门户商品 - 所属专业
   */
  belongIndustryForPortal: SkuPropertyResponse
}

/**
 * 购买渠道配置
<AUTHOR>
@date 2022/01/26
 */
export class PurchaseChannelConfigResponse {
  /**
   * 是否开启可见
   */
  couldSee: boolean
  /**
   * 是否开启可购买
   */
  couldBuy: boolean
}

/**
 * 商品sku属性
<AUTHOR>
@date 2022/01/25
 */
export class SkuPropertyResponse {
  /**
   * sku属性值id
   */
  skuPropertyValueId: string
  /**
   * sku属性值名称
   */
  skuPropertyValueName: string
}

/**
 * 用户商品拥有信息
<AUTHOR>
@date 2022/01/25
 */
export class UserPossessionInfoResponse {
  /**
   * 用户是否拥有此商品
   */
  possessing: boolean
  /**
   * 商品来源类型
<br> 0：子订单 1：换货单
@see com.fjhb.ms.trade.query.order.constants.CommoditySkuSourceType
   */
  sourceType: number
  /**
   * 商品来源ID
<p>
<br> 如果来源类型是子订单，那么来源ID是子订单号
<br> 如果来源类型是换货单，那么来源ID是换货单
   */
  sourceId: string
  /**
   * 商品子订单发货状态.
0: 等待发货,
100: 发货中,
200: 发货成功,
401: 发货失败
   */
  subOrderDeliveryStatus: number
}

/**
 * 商品资源
<AUTHOR>
@date 2022/03/02
 */
export interface ResourceResponse {
  /**
   * 资源类型
<br> Scheme:方案 ,Issue:期数
@see com.fjhb.domain.trade.api.commodity.consts.CommoditySaleResourceTypes
   */
  resourceType: string
}

/**
 * 培训方案信息
<AUTHOR>
@date 2022/03/02
 */
export class SchemeResourceResponse implements ResourceResponse {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 方案名
   */
  schemeName: string
  /**
   * 学时
   */
  period: number
  /**
   * 培训方案类型
<p>
chooseCourseLearning：选课学习
autonomousCourseLearning：自主学习
@see com.fjhb.domain.learningscheme.api.scheme.consts.SchemeType
   */
  schemeType: string
  /**
   * 资源类型
<br> Scheme:方案 ,Issue:期数
@see com.fjhb.domain.trade.api.commodity.consts.CommoditySaleResourceTypes
   */
  resourceType: string
}

export class Accommodation {
  /**
   * 是否安排住宿
   */
  IsAccommodation: boolean
  /**
   * 住宿方式
0-无需住宿
1-1单人住宿
2-合住
   */
  accommodationType: number
}

/**
 * <AUTHOR> linq
@date : 2023-12-20 19:09
@description：商品分销授权信息
 */
export class CommodityAuthInfoResponse {
  /**
   * 商品授权ID（产品分销授权ID）
   */
  commodityAuthId: string
  /**
   * 商品授权分销商ID
   */
  distributorId: string
  /**
   * 商品授权分销商 名称
   */
  distributorName: string
  /**
   * 分销级别
@see DistriButionLevel
   */
  distributionLevel: number
  /**
   * 上级分销商ID（仅二级分销商有该值，分销合同内的上级分销商ID）
   */
  superiorDistributorId: string
  /**
   * 上级分销商 名称
   */
  superiorDistributorName: string
  /**
   * 商品授权分销商ID路径（格式为 /供应商ID/一级分销商ID/二级分销商ID）
   */
  distributorIdPath: string
  /**
   * 商品授权供应商ID
   */
  supplierId: string
  /**
   * 商品授权供应商名称
   */
  supplierName: string
  /**
   * 供应商对接业务员ID（仅订单创建时有该分销商+网校有业务员时才有值）
   */
  salesmanId: string
  /**
   * 供应商对接业务员名称
   */
  salesmanName: string
  /**
   * 分销商电话
   */
  distributorPhone: string
  /**
   * 分销商单位统一社会信用代码
   */
  distributorUnitCreditCode: string
  /**
   * 分销商类型 1-个人 2-企业
   */
  distributorPartnerType: number
  /**
   * 供应商类型 1-个人 2-企业
   */
  supplierPartnerType: number
}

/**
 * 订单商品sku属性信息
<AUTHOR>
@date 2022/01/26
 */
export class CommoditySkuPropertyResponse1 {
  /**
   * 年度
   */
  year: SkuPropertyResponse1
  /**
   * 地区 - 省
   */
  province: SkuPropertyResponse1
  /**
   * 地区 - 市
   */
  city: SkuPropertyResponse1
  /**
   * 地区 - 区县
   */
  county: SkuPropertyResponse1
  /**
   * 行业
   */
  industry: SkuPropertyResponse1
  /**
   * 科目类型
   */
  subjectType: SkuPropertyResponse1
  /**
   * 培训类别
   */
  trainingCategory: SkuPropertyResponse1
  /**
   * 培训专业
   */
  trainingProfessional: SkuPropertyResponse1
  /**
   * 技术等级
   */
  technicalGrade: SkuPropertyResponse1
  /**
   * 卫生行业-培训对象
   */
  trainingObject: SkuPropertyResponse1
  /**
   * 卫生行业-岗位类别
   */
  positionCategory: SkuPropertyResponse1
  /**
   * 工勤行业-技术等级
   */
  jobLevel: SkuPropertyResponse1
  /**
   * 工勤行业-工种
   */
  jobCategory: SkuPropertyResponse1
  /**
   * 年级
   */
  grade: SkuPropertyResponse1
  /**
   * 科目
   */
  subject: SkuPropertyResponse1
  /**
   * 学段
   */
  learningPhase: SkuPropertyResponse1
  /**
   * 学科
   */
  discipline: SkuPropertyResponse1
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType: SkuPropertyResponse1
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory: SkuPropertyResponse1
  /**
   * 资质类别-主项增项
   */
  qualificationCategory: SkuPropertyResponse1
  /**
   * 培训形式 网授班,面授
   */
  trainingWay: SkuPropertyResponse1
}

/**
 * 订单商品信息返回值
<AUTHOR>
@date 2022/1/26
 */
export class CommoditySkuResponse {
  /**
   * 商品id
   */
  commoditySkuId: string
  /**
   * 商品Sku名称
   */
  saleTitle: string
  /**
   * 商品封面图路径
   */
  commodityPicturePath: string
  /**
   * 商品售价
   */
  price: number
  /**
   * 商品原始单价
   */
  originalPrice: number
  /**
   * 是否使用优惠价
   */
  enableSpecialPrice: boolean
  /**
   * 是否展示原价
   */
  showPrice: boolean
  /**
   * 商品sku 配置
   */
  skuProperty: CommoditySkuPropertyResponse1
  /**
   * 商品关联资源
   */
  resource: ResourceResponse
  /**
   * 第三方平台类型
   */
  tppTypeId: string
  /**
   * 管理系统平台
   */
  externalTrainingPlatform: string
  /**
   * 培训机构名称
   */
  trainingInstitution: string
  /**
   * 期别信息
   */
  issueInfo: IssueInfo
}

/**
 * <AUTHOR> linq
@date : 2024-11-08 19:28
@description：事件范围返回值
 */
export class DateScopeResponse {
  /**
   * 开始时间
   */
  begin: string
  /**
   * 结束时间
   */
  end: string
}

/**
 * 优惠策略
 */
export class DiscountSchemeResponse {
  /**
   * 优惠价
   */
  specialPrice: number
  /**
   * 优惠策略
   */
  discountPolicyList: Array<DiscountPolicyModel>
  /**
   * 优惠类型
1-单项优惠 2-聚合优惠
   */
  discountType: number
  /**
   * 是否启用
   */
  hasEnabled: boolean
}

/**
 * 发票申请信息
<AUTHOR>
@date 2022/03/17
 */
export class InvoiceApplyInfoResponse {
  /**
   * 开票方式
1: 线上开票
2: 线下开票
@see InvoiceMethods
   */
  invoiceMethod: number
  /**
   * 发票类型
1：电子发票
2：纸质发票
@see InvoiceTypes
   */
  invoiceType: number
  /**
   * 发票种类
1：普通发票
2：增值税普通发票
3：增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory: number
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型
1：个人
2：企业
@see InvoiceTitleTypes
   */
  titleType: number
  /**
   * 纳税人识别号（统一社会信用代码）
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账户
   */
  account: string
  /**
   * 联系邮箱
   */
  email: string
  /**
   * 联系邮箱
   */
  contactEmail: string
  /**
   * 联系电话
   */
  contactPhone: string
  /**
   * 发票票面备注
   */
  remark: string
  /**
   * 营业执照
   */
  businessLicensePath: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath: string
  /**
   * 配送方式
0：无
1：自取
2：快递
@see OfflineShippingMethods
   */
  shippingMethod: number
  /**
   * 配送地址信息
   */
  deliveryAddress: DeliveryAddressResponse
  /**
   * 自取点信息
   */
  takePoint: TakePointResponse
  /**
   * 发票申请时间
   */
  appliedTime: string
  /**
   * 关联发票id集合
   */
  invoiceIdList: Array<string>
}

/**
 * 发票票面信息
<AUTHOR>
@date 2022/3/18
 */
export class InvoiceFaceInfoResponse {
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型
@see InvoiceTitleTypes
   */
  titleType: number
  /**
   * 购买方纳税人识别号
   */
  taxpayerNo: string
  /**
   * 购买方地址
   */
  address: string
  /**
   * 购买方电话号码
   */
  phone: string
  /**
   * 购买方开户行名称
   */
  bankName: string
  /**
   * 购买方银行账户
   */
  account: string
  /**
   * 购买方电子邮箱
   */
  email: string
  /**
   * 购买方营业执照
   */
  businessLicensePath: string
  /**
   * 购买方开户许可
   */
  accountOpeningLicensePath: string
  /**
   * 联系电子邮箱
   */
  contactEmail: string
  /**
   * 联系电话
   */
  contactPhone: string
  /**
   * 发票票面备注
   */
  remark: string
}

export class IssueInfo {
  /**
   * 期别id
   */
  issueId: string
  /**
   * 期别名称
   */
  issueName: string
  /**
   * 培训编号
   */
  issueNum: string
  /**
   * 培训开始时间
   */
  trainStartTime: string
  /**
   * 培训结束时间
   */
  trainEndTime: string
  /**
   * 当前期别来源类型。下单：SUB_ORDER 换班：EXCHANGE_ORDER 换期:CHANGE_ISSUE
   */
  sourceType: string
  /**
   * 期别来源类型id 1:子订单号：2：换货单号：3：旧的期别参训资格D
   */
  sourceId: string
}

/**
 * <AUTHOR> linq
@date : 2023-12-25 14:16
@description：归属信息
 */
export class OwnerInfoResponse {
  /**
   * 所属服务商ID
   */
  servicerId: string
  /**
   * 所属服务商名称
   */
  servicerName: string
}

/**
 * 退货单/批次退货单的原订单支付信息
<AUTHOR>
@date 2022/03/18
 */
export class PaymentInfoResponse {
  /**
   * 支付金额
   */
  payAmount: number
  /**
   * 支付流水号
   */
  flowNo: string
  /**
   * 收款账号
   */
  receiveAccountId: string
  /**
   * 付款单类型
<p>
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
   */
  paymentOrderType: number
}

/**
 * 定价策略
 */
export class PricingPolicyResponse {
  /**
   * 定价策略id
   */
  pricingPolicyId: string
  /**
   * 定价价格
   */
  price: number
  /**
   * 是否启用
   */
  hasEnabled: boolean
}

/**
 * 收款账号返回值
<AUTHOR>
@date 2022/01/26
 */
export class ReceiveAccountResponse {
  /**
   * 收款账号id
   */
  receiveAccountId: string
  /**
   * 收款账号类型（1:线上收款账号 2:线下收款账号）
@see ReceiveAccountTypes
   */
  receiveAccountType: number
  /**
   * 支付渠道id
   */
  payChannelId: string
  /**
   * 支付渠道名称
   */
  payChannelName: string
  /**
   * 收款账户的单位id
   */
  receiveAccountUnitId: string
}

/**
 * 退货单退款信息
<AUTHOR>
@date 2022/03/18
 */
export class RefundInfoResponse {
  /**
   * 退款单号
   */
  refundOrderNo: string
  /**
   * 退款单类型（1：线上退款 2：线下退款）
@see RefundOrderTypes
   */
  refundOrderType: number
  /**
   * 退款单状态（0：等待退款 1：退款中 2：已退款 3：退款失败）
@see com.fjhb.domain.trade.api.payment.consts.RefundOrderStatus
   */
  refundOrderStatus: number
  /**
   * 退款单状态变更时间
   */
  refundOrderStatusChangeTime: RefundOrderStatusChangeTimeResponse
  /**
   * 退款流水
   */
  refundFlow: string
  /**
   * 退款金额
   */
  refundAmount: number
  /**
   * 退款失败原因
   */
  refundFailReason: string
  /**
   * 已确认退款时间
   */
  refundConfirmedTime: string
}

export class SalePathResponse {
  /**
   * 主键id,  当前销售路径_销售全路径
   */
  id: string
  /**
   * 销售全路径
   */
  fullPath: string
  /**
   * 当前销售路径
   */
  currentPath: string
  /**
   * 当前销售路径末级code
   */
  currentPathLastCode: string
  /**
   * 当前销售路径末级类型
   */
  currentPathLastType: number
  /**
   * 是否为全路径的最后一级
@see CurrentSalePathType
   */
  isLast: boolean
}

export class UserInfoResponse {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 用户名
   */
  name: string
}

/**
 * 用户相关返回值
<AUTHOR>
@date 2022/01/26
 */
export class UserResponse {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 用户所在地区
   */
  userArea: RegionModel
  /**
   * 江苏工考 - 用户所在地区
   */
  managementUnitRegionCode: RegionModel
  /**
   * 江苏工考 - 工种
   */
  jobCategoryId: string
  /**
   * 江苏工考 - 工种等级
   */
  professionalLevel: number
  /**
   * 江苏工考 - 证书技术工种名称
   */
  jobCategoryName: string
}

/**
 * 换货单主题模型
<AUTHOR>
@date 2022/03/23
 */
export class ExchangeOrderResponse {
  /**
   * 换货单号
   */
  exchangeOrderNo: string
  /**
   * 换货单基本信息
   */
  basicData: ExchangeOrderBasicDataResponse
  /**
   * 换货单审批信息
   */
  approvalInfo: ExchangeOrderApprovalInfoResponse
  /**
   * 原始商品信息
   */
  originalCommodity: OriginalCommodityResponse
  /**
   * 换货商品信息
   */
  exchangeCommodity: ExchangeCommodityResponse
  /**
   * 子订单发货商品分销授权信息（仅分销订单有值）
   */
  commodityAuthInfo: CommodityAuthInfoResponse
  /**
   * 换货单关联子订单信息
   */
  subOrderInfo: SubOrderInfoResponse
}

/**
 * 换货商品模型
<AUTHOR>
@date 2022/03/31
 */
export class ExchangeCommodityResponse {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse
}

/**
 * 退货申请信息返回值
<AUTHOR>
@date 2022/03/24
 */
export class ExchangeOrderApplyInfoResponse {
  /**
   * 申请人
   */
  applyUser: UserResponse
  /**
   * 申请原因内容id
   */
  reasonId: string
  /**
   * 申请原因内容
   */
  reasonContent: string
  /**
   * 申请描述
   */
  description: string
}

/**
 * 换货单审批信息
<AUTHOR>
@date 2022/03/18
 */
export class ExchangeOrderApprovalInfoResponse {
  /**
   * 审批状态
<p>
0: 未审批
1: 已审批
@see ExchangeApprovalReportStatus
   */
  approveStatus: number
  /**
   * 审批结果
<p>
-1：无
0：拒绝
1：同意
@see ExchangeApprovalReportResults
   */
  approveResult: number
  /**
   * 审批人
   */
  approveUser: UserModel
  /**
   * 审批意见
   */
  approveComment: string
  /**
   * 审批时间
   */
  approveTime: string
}

/**
 * 换货单基本信息
<AUTHOR>
@date 2022/03/23
 */
export class ExchangeOrderBasicDataResponse {
  /**
   * 换货单状态
<p>
0: 申请换货
1: 取消中
2: 退货中
3: 退货失败
4: 申请发货
5: 发货中
6: 发货失败
7: 换货完成
8: 已关闭
@see ExchangeOrderStatus
   */
  status: number
  /**
   * 换货单状态变更时间
   */
  statusChangeTime: ExchangeOrderStatusChangeTimeResponse
  /**
   * 换货单申请信息
   */
  applyInfo: ExchangeOrderApplyInfoResponse
  /**
   * 换货单退货失败信息
   */
  exchangeReturnFailReason: string
  /**
   * 换货单发货失败信息
   */
  exchangeDeliveryFailReason: string
  /**
   * 换货单关闭信息
   */
  exchangeOrderCloseReason: ExchangeOrderCloseReasonResponse
}

/**
 * 换货单交易关闭信息
<AUTHOR>
@date 2022/2/14
 */
export class ExchangeOrderCloseReasonResponse {
  /**
   * 换货单关闭类型
<p>
1：买家关闭
2：卖家关闭
@see ExchangeOrderCloseTypes
   */
  closeType: number
  /**
   * 换货单取消人
   */
  cancelUser: UserResponse
  /**
   * 换货单取消原因
   */
  cancelReason: string
}

/**
 * 换货单状态变更时间
<AUTHOR>
@date 2022/03/24
 */
export class ExchangeOrderStatusChangeTimeResponse {
  /**
   * 申请换货
   */
  applied: string
  /**
   * 取消中
   */
  cancelling: string
  /**
   * 退货中
   */
  returning: string
  /**
   * 退货失败
   */
  returnFailed: string
  /**
   * 申请发货
   */
  deliveryApplied: string
  /**
   * 发货中
   */
  delivering: string
  /**
   * 换货完成
   */
  exchanged: string
  /**
   * 已关闭
   */
  closed: string
}

/**
 * 主订单信息
<AUTHOR>
@date 2022/03/23
 */
export class OrderInfoResponse {
  /**
   * 订单类型
@see OrderTypes
   */
  orderType: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 批次单号
   */
  batchOrderNo: string
  /**
   * 买家
   */
  buyer: UserResponse
  /**
   * 创建人
   */
  creator: UserResponse
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 销售渠道id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
}

/**
 * 原商品模型
<AUTHOR>
@date 2022/03/31
 */
export class OriginalCommodityResponse {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse
}

/**
 * 子订单信息
<AUTHOR>
@date 2022/03/23
 */
export class SubOrderInfoResponse {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 子订单总额
   */
  amount: number
  /**
   * 订单信息
   */
  orderInfo: OrderInfoResponse
  /**
   * 子订单是否使用优惠
   */
  useDiscount: boolean
  /**
   * 子订单价格来源 0.自营 1.分销
   */
  discountType: number
  /**
   * 子订单优惠来源ID
   */
  discountSourceId: string
}

/**
 * 线下发票网关模型
<AUTHOR>
@date 2022/3/18
 */
export class OfflineInvoiceResponse {
  /**
   * 发票id
   */
  offlineInvoiceId: string
  /**
   * 发票基本信息
   */
  basicData: OfflineInvoiceBasicDataResponse
  /**
   * 发票关联订单信息
   */
  associationInfo: OfflineInvoiceAssociationInfoResponse
  /**
   * 发票配送信息
   */
  deliveryInfo: OfflineInvoiceDeliveryInfoResponse
  /**
   * 发票配送记录
   */
  deliveryRecordList: Array<OfflineDeliveryRecord>
}

/**
 * 发票开票状态变更时间记录
<AUTHOR>
@date 2022/04/06
 */
export class BillStatusChangeTimeResponse {
  /**
   * 未开票
   */
  unBill: string
  /**
   * 已开票
   */
  success: string
}

/**
 * 配送地址信息
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryAddressResponse {
  /**
   * 收件人
   */
  consignee: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 所在物理地区
   */
  region: string
  /**
   * 详细地址
   */
  address: string
}

/**
 * 发票配送状态变更时间记录
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryStatusChangeTimeResponse {
  /**
   * 未就绪
   */
  unReady: string
  /**
   * 已就绪
   */
  ready: string
  /**
   * 已配送
   */
  shipped: string
  /**
   * 已自取
   */
  taken: string
}

/**
 * 快递信息
<AUTHOR>
@date 2022/04/06
 */
export class ExpressResponse {
  /**
   * 快递公司名称
   */
  expressCompanyName: string
  /**
   * 快递单号
   */
  expressNo: string
}

/**
 * 发票状态变更时间记录
<AUTHOR>
@date 2022/04/06
 */
export class InvoiceStatusChangeTimeResponse {
  /**
   * 正常
   */
  normal: string
  /**
   * 作废
   */
  invalid: string
}

/**
 * 发票配送记录
<AUTHOR>
@date 2022/05/17
 */
export class OfflineDeliveryRecord {
  /**
   * 发票号
   */
  invoiceNoList: Array<string>
  /**
   * 配送方式
<p>
0:无 1:自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethod: number
  /**
   * 自取结果
   */
  takeResult: TakeResultResponse
  /**
   * 快递信息
   */
  express: ExpressResponse
  /**
   * 配送时间
   */
  deliveryTime: string
}

/**
 * 发票关联订单信息
<AUTHOR>
@date 2022/3/18
 */
export class OfflineInvoiceAssociationInfoResponse {
  /**
   * 关联订单类型 | 批次单、普通订单
@see AssociationTypes
   */
  associationType: number
  /**
   * 订单号 | 批次单号
   */
  associationId: string
  /**
   * 付款金额
   */
  payAmount: number
  /**
   * 买家信息
   */
  buyer: UserModel
  /**
   * 收款账号ID
   */
  receiveAccountId: string
  /**
   * 订单退货状态
0:未退货 1：退货中 2：退货成功
   */
  orderReturnStatus: number
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 专题id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  /**
   * 订单发货商品分销信息（仅分销订单的发票有值）
   */
  commodityAuthInfoSet: Array<CommodityAuthInfoResponse>
  /**
   * 第三方平台类型
   */
  tppTypeId: string
}

/**
 * 发票基本信息
<AUTHOR>
@date 2022/3/18
 */
export class OfflineInvoiceBasicDataResponse {
  /**
   * 发票类型
@see InvoiceTypes
   */
  invoiceType: number
  /**
   * 发票种类
@see InvoiceCategories
   */
  invoiceCategory: number
  /**
   * 发票状态
@see InvoiceStatus
   */
  invoiceStatus: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime: InvoiceStatusChangeTimeResponse
  /**
   * 发票开票状态
@see InvoiceBillStatus
   */
  billStatus: number
  /**
   * 发票开票状态变更时间记录
   */
  billStatusChangeTime: BillStatusChangeTimeResponse
  /**
   * 发票是否冻结
   */
  freeze: boolean
  /**
   * 冻结来源类型
@see InvoiceFrozenSourceTypes
   */
  freezeSourceType: number
  /**
   * 冻结来源编号
   */
  freezeSourceId: string
  /**
   * 发票票面信息
   */
  invoiceFaceInfo: InvoiceFaceInfoResponse
  /**
   * 发票号集合
   */
  invoiceNoList: Array<string>
  /**
   * 总金额（开票金额）
   */
  amount: number
}

/**
 * 线下发票配送信息
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceDeliveryInfoResponse {
  /**
   * 配送状态
<p>
0:未就绪 1：已就绪 2：已自取 3：已配送
@see OfflineDeliveryStatus
   */
  deliveryStatus: number
  /**
   * 配送状态变更时间记录
   */
  deliveryStatusChangeTime: DeliveryStatusChangeTimeResponse
  /**
   * 配送方式
<p>
0:无 1:自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethod: number
  /**
   * 配送地址信息
   */
  deliveryAddress: DeliveryAddressResponse
  /**
   * 自取点信息
   */
  takePoint: TakePointResponse
  /**
   * 快递信息
   */
  express: ExpressResponse
  /**
   * 自取信息
   */
  takeResult: TakeResultResponse
}

/**
 * 自取点信息
<AUTHOR>
@date 2022/04/06
 */
export class TakePointResponse {
  /**
   * 领取地点
   */
  pickupLocation: string
  /**
   * 领取时间
   */
  pickupTime: string
  /**
   * 备注
   */
  remark: string
}

/**
 * 取件信息
<AUTHOR>
@date 2022/04/06
 */
export class TakeResultResponse {
  /**
   * 领取人
   */
  takePerson: string
  /**
   * 手机号
   */
  phone: string
}

/**
 * 发票网关模型
<AUTHOR>
@date 2022/3/18
 */
export class OnlineInvoiceResponse {
  /**
   * 发票id
   */
  invoiceId: string
  /**
   * 发票基本信息
   */
  basicData: OnlineInvoiceBasicDataResponse
  /**
   * 发票关联订单信息
   */
  associationInfoList: Array<OnlineInvoiceAssociationInfo>
  /**
   * 蓝票票据
   */
  blueInvoiceItem: OnlineInvoiceItemResponse
  /**
   * 红票票据
   */
  redInvoiceItem: OnlineInvoiceItemResponse
}

/**
 * 发票开具状态变更时间
<AUTHOR>
@date 2022/03/22
 */
export class BillStatusChangeTimeResponse1 {
  /**
   * 未开具
   */
  unBill: string
  /**
   * 开票中
   */
  billing: string
  /**
   * 开票成功
   */
  success: string
  /**
   * 开票失败
   */
  failure: string
}

/**
 * 发票关联订单信息
<AUTHOR>
@date 2022/3/18
 */
export class OnlineInvoiceAssociationInfo {
  /**
   * 关联订单类型
0:订单号
1:批次单号
@see AssociationTypes
   */
  associationType: number
  /**
   * 订单号 | 批次单号
   */
  associationId: string
  /**
   * 订单付款金额
   */
  payAmount: number
  /**
   * 买家信息
   */
  buyer: UserEntity
  /**
   * 收款账号
   */
  receiveAccountId: string
  /**
   * 订单退货状态
0:未退货 1：退货中 2：退货成功
   */
  orderReturnStatus: number
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 订单发货商品分销信息（仅分销订单的发票有值）
   */
  commodityAuthInfoSet: Array<CommodityAuthInfoResponse>
  /**
   * 专题id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  /**
   * 第三方平台类型
   */
  tppTypeId: string
}

/**
 * 发票基本信息
<AUTHOR>
@date 2022/3/18
 */
export class OnlineInvoiceBasicDataResponse {
  /**
   * 是否为拆票后的发票
   */
  spilt: boolean
  /**
   * 发票类型
1:电子发票
2:纸质发票
@see InvoiceTypes
   */
  invoiceType: number
  /**
   * 发票种类
1:普通发票
2:增值税普通发票
3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory: number
  /**
   * 发票状态
1:正常
2:作废
@see InvoiceStatus
   */
  invoiceStatus: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime: InvoiceStatusChangeTimeResponse
  /**
   * 发票是否冻结
   */
  freeze: boolean
  /**
   * 冻结来源类型
@see InvoiceFrozenSourceTypes
   */
  freezeSourceType: number
  /**
   * 冻结来源编号
   */
  freezeSourceId: string
  /**
   * 蓝票票据开具状态
0:未开具
1:开票中
2:开票成功
3:开票失败
@see InvoiceBillStatus
   */
  blueInvoiceItemBillStatus: number
  /**
   * 红票票据开具状态
0:未开具
1:开票中
2:开票成功
3:开票失败
@see InvoiceBillStatus
   */
  redInvoiceItemBillStatus: number
  /**
   * 发票票面信息
   */
  invoiceFaceInfo: InvoiceFaceInfoResponse
  /**
   * 纳税人编号
   */
  taxpayerId: string
  /**
   * 开票总金额
<br> 计算方式： 累加开票项目中的总价
   */
  amount: number
}

/**
 * 发票票据
<AUTHOR>
@date 2022/03/18
 */
export class OnlineInvoiceItemResponse {
  /**
   * 票据id
   */
  invoiceItemId: string
  /**
   * 开票流水号，由系统生成
   */
  flowNo: string
  /**
   * 票据开具状态
@see InvoiceBillStatus
   */
  billStatus: number
  /**
   * 票据开具状态变更时间
   */
  billStatusChangeTime: BillStatusChangeTimeResponse1
  /**
   * 价税合计（开票金额）
价税合计&#x3D;合计金额(不含税)+合计税额
   */
  totalAmount: number
  /**
   * 不含税总金额
   */
  totalExcludingTaxAmount: number
  /**
   * 总税额
   */
  totalTax: number
  /**
   * 发票代码
   */
  billCode: string
  /**
   * 发票号码
   */
  billNo: string
  /**
   * 校验码
   */
  checkCode: string
  /**
   * 票据存储文件路径
   */
  filePath: string
  /**
   * ofd票据存储文件路径
   */
  ofdFilePath: string
  /**
   * xml票据存储文件路径
   */
  xmlFilePath: string
  /**
   * 开票失败信息
   */
  failMessage: string
}

export class IssueLogResponse {
  /**
   * 子订单号
   */
  subOrder: string
  /**
   * 换出商品
   */
  originalCommodity: CommoditySkuResponse
  /**
   * 换入商品
   */
  exchangeCommodity: CommoditySkuResponse
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 操作人信息
   */
  creator: UserInfoResponse
  /**
   * 换期类型 ：  0：换班换期   1： 班内换期
   */
  exchangeType: number
}

/**
 * 交易成功订单sku topN响应内容
 */
export class ListOrderTopNBySkuResponse {
  /**
   * sku类型
   */
  skuProperty: string
  /**
   * sku属性ID
   */
  skuPropertyId: string
  /**
   * sku名称
   */
  skuPropertyName: string
  /**
   * 包含订单数量
   */
  belongOrderCount: number
}

/**
 * 订单返回值
<AUTHOR>
@date 2022/1/25
 */
export class OrderResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 基本信息
   */
  basicData: OrderBasicDataResponse
  /**
   * 支付信息
   */
  payInfo: PayInfoResponse
  /**
   * 子订单信息
   */
  subOrderItems: Array<SubOrderResponse>
  /**
   * 买家信息
   */
  buyer: UserResponse
  /**
   * 订单创建人
   */
  creator: UserResponse
  /**
   * 订单是否已经申请发票
   */
  isInvoiceApplied: boolean
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 销售渠道id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  /**
   * 发票申请信息
   */
  invoiceApplyInfo: InvoiceApplyInfoResponse
  /**
   * 购买来源，门户-GATEWAY、专题-TRAINING
   */
  purchaseSourceType: string
  /**
   * 销售全路径
   */
  salePathList: Array<SalePathResponse>
  /**
   * 推广门户标识
   */
  portalIdentifier: string
  /**
   * 培训计划ID，例如补贴性培训平台和补贴管理系统对接
   */
  policyTrainingSchemeIds: string
  /**
   * 申报单位统一信用代码
   */
  declarationUnitCode: string
  /**
   * 结算状态
@see com.fjhb.ms.trade.query.common.constants.SettlementStatusConstants
   */
  settlementStatus: number
  /**
   * 结算金额
   */
  settlementAmount: number
  /**
   * 结算时间
   */
  settlementDate: string
  /**
   * 兑付状态
@see com.fjhb.ms.trade.query.common.constants.RedeemedStatusConstants
   */
  redeemedStatus: number
  /**
   * 兑付金额
   */
  redeemedAmount: number
  /**
   * 兑付时间
   */
  redeemedDate: string
  /**
   * 参训资格ID
   */
  registrationId: string
}

/**
 * 订单统计信息返回值
<AUTHOR>
@date 2022/01/26
 */
export class OrderStatisticResponse {
  /**
   * 订单总数量
   */
  totalOrderCount: number
  /**
   * 订单总金额
   */
  totalOrderAmount: number
  /**
   * 订单培训方案商品总学时数
   */
  totalPeriod: number
}

/**
 * 订单基本信息返回值
<AUTHOR>
@date 2022/01/26
 */
export class OrderBasicDataResponse {
  /**
   * 订单类型
<br> 1：常规订单 2：批次关联订单
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType: number
  /**
   * 关联批次单号
   */
  batchOrderNo: string
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3：管理员导入 4：集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelType: number
  /**
   * 终端
<br> Web:Web端 H5:H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTerminalCodes
   */
  terminalCode: string
  /**
   * 订单总金额
   */
  amount: number
  /**
   * 订单状态
0:未确认，批次单订单初始状态
1:正常
2:交易完成
3:交易关闭
<p>
ui订单状态：数据微服务订单状态
等待付款：订单状态正常&amp;支付状态未支付
支付中：订单状态正常&amp;支付状态支付中
开通中：订单状态正常&amp;支付状态已支付
交易成功：订单状态交易成功
交易关闭：订单状态交易关闭
@see OrderStatus
   */
  orderStatus: number
  /**
   * 订单支付状态
0:未支付
1:支付中
2:已支付
@see OrderPaymentStatus
   */
  orderPaymentStatus: number
  /**
   * 订单发货状态
0：未发货
1：发货中
2：已发货
@see OrderDeliveryStatus
   */
  orderDeliveryStatus: number
  /**
   * 订单状态变更时间
   */
  orderStatusChangeTime: OrderStatusChangeTimeResponse
  /**
   * 订单支付状态变更时间
   */
  orderPaymentStatusChangeTime: OrderPaymentStatusChangeTimeResponse
  /**
   * 自动关闭时间
   */
  autoCloseTime: string
  /**
   * 交易关闭原因
   */
  closeReason: OrderCloseReasonResponse
  /**
   * 单位id
   */
  unitId: string
}

/**
 * 订单交易关闭返回值
<AUTHOR>
@date 2022/01/26
 */
export class OrderCloseReasonResponse {
  /**
   * 交易关闭类型
<br> 1:买家取消 2:卖家取消 3：超时取消 4：批次关联取消
@see com.fjhb.domain.trade.api.order.consts.OrderClosedTypes
   */
  closedType: number
  /**
   * 交易关闭原因ID
   */
  reasonId: string
  /**
   * 交易关闭原因说明
   */
  reason: string
}

/**
 * 订单支付状态变更时间
<AUTHOR>
@date 2022/01/26
 */
export class OrderPaymentStatusChangeTimeResponse {
  /**
   * 支付中
   */
  paying: string
  /**
   * 已支付
   */
  paid: string
}

/**
 * 订单状态变更时间
<AUTHOR>
@date 2022/01/26
 */
export class OrderStatusChangeTimeResponse {
  /**
   * 订单处于正常状态的时间（订单创建时间）
   */
  normal: string
  /**
   * 订单交易完成时间
   */
  completed: string
  /**
   * 订单交易关闭时间
   */
  closed: string
}

/**
 * 订单支付信息
<AUTHOR>
@date 2022/1/26
 */
export class PayInfoResponse {
  /**
   * 付款单号
   */
  paymentOrderNo: string
  /**
   * 付款单类型
<p>
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
   */
  paymentOrderType: number
  /**
   * 付款单状态
0:待付款
1:付款中
2:已支付
3:已取消
@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderStatus
   */
  paymentOrderStatus: number
  /**
   * 支付流水号
   */
  flowNo: string
  /**
   * 支付金额
   */
  payAmount: number
  /**
   * 支付金额类型
<br> 0:人民币现金支付（线下人民币现金、微信支付、支付宝） 1:华博电子钱包虚拟币 2:消费券(培训券)
@see CurrencyType
   */
  currencyType: number
  /**
   * 收款账号
   */
  receiveAccount: ReceiveAccountResponse
  /**
   * 汇款凭证url
   */
  paymentVoucherList: Array<PaymentVoucherResponse>
  /**
   * 汇款凭证确认人
   */
  paymentVoucherConfirmUser: UserModel
}

/**
 * 付款凭证
<AUTHOR>
@date 2022/03/07
 */
export class PaymentVoucherResponse {
  /**
   * 凭证ID
   */
  id: string
  /**
   * 凭证文件路径
   */
  path: string
  /**
   * 创建人
   */
  createUserId: UserModel
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 订单商品sku属性
<AUTHOR>
@date 2022/1/25
 */
export class SkuPropertyResponse1 {
  /**
   * sku属性值id
   */
  skuPropertyValueId: string
  /**
   * sku属性值名
   */
  skuPropertyValueName: string
  /**
   * sku属性值展示名
   */
  skuPropertyValueShowName: string
}

/**
 * 子订单发货状态变更时间
<AUTHOR>
@date 2022/01/26
 */
export class SubOrderDeliveryStatusChangeTimeResponse {
  /**
   * 等待发货
   */
  waiting: string
  /**
   * 发货中
   */
  delivering: string
  /**
   * 发货成功
   */
  successDelivered: string
  /**
   * 发货失败
   */
  failDelivered: string
}

/**
 * 子订单返回值
<AUTHOR>
@date 2022/1/26
 */
export class SubOrderResponse {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 子订单发货状态
0:等待发货 100:发货中 200:发货成功 400:发货失败
@see DeliveryOrderSkuStatus
   */
  deliveryStatus: number
  /**
   * 子订单退货情况
0: 未退货,
1: 已部分退货,
2: 已全部退货
   */
  returnSchedule: number
  /**
   * 子订单退款情况
0: 未退款,
1: 已部分退款,
2: 已全部退款
   */
  refundSchedule: number
  /**
   * 子订单换货状态
<p>
0:未换货
1:换货申请中
2:换货中
3:已换货
@see com.fjhb.domain.trade.api.order.consts.SubOrderExchangeStatus
   */
  exchangeStatus: number
  /**
   * 子订单最新退货单号
   */
  returnOrderNo: string
  /**
   * 子订单退货状态
0：未退货
1：退货申请中
2：退货中
3：退货成功
4：退款中
5：退款成功
@see SubOrderReturnStatus
   */
  returnStatus: number
  /**
   * 子订单发货状态变更时间
0: 等待发货
100: 发货中
200: 发货成功
400: 发货失败
<br> key值 {@link DeliveryOrderSkuStatus}
   */
  deliveryStatusChangeTime: SubOrderDeliveryStatusChangeTimeResponse
  /**
   * 发货失败信息
   */
  deliverFailMessage: string
  /**
   * 子订单商品数量
   */
  quantity: number
  /**
   * 子订单剩余商品数量
   */
  leftQuantity: number
  /**
   * 商品单价
   */
  price: number
  /**
   * 子订单总金额
   */
  amount: number
  /**
   * 子订单剩余金额
   */
  leftAmount: number
  /**
   * 子订单优惠价（优惠的减免额度）
   */
  specialPrice: number
  /**
   * 商品分销授权信息（仅分销订单有值）
   */
  commodityAuthInfo: CommodityAuthInfoResponse
  /**
   * 优惠类型
@see DiscountType
   */
  discountType: number
  /**
   * 优惠来源ID | 优惠申请单ID
   */
  discountSourceId: string
  /**
   * 是否使用优惠
   */
  useDiscount: boolean
  /**
   * 发货商品信息
   */
  deliveryCommoditySku: CommoditySkuResponse
  /**
   * 当前商品信息
   */
  currentCommoditySku: CommoditySkuResponse
  /**
   * 当前商品来源类型
@see CommoditySkuSourceType
子订单发货: 0
换货单换货：1
   */
  currentCommoditySourceType: number
  /**
   * 当前商品来源ID
<p>
如果来源类型是子订单，那么来源ID是子订单号
如果来源类型是换货单，那么来源ID是换货单
   */
  currentCommoditySourceId: string
  /**
   * 最终成交单价
   */
  finalPrice: number
  /**
   * 优惠方案
   */
  discountScheme: DiscountSchemeResponse
  /**
   * 定价方案
   */
  pricingPolicy: PricingPolicyResponse
  /**
   * 住宿信息
   */
  accommodation: Accommodation
  /**
   * 是否换期
   */
  isExchangeIssue: boolean
}

/**
 * 退货单网关模型
 */
export class ReturnOrderResponse {
  /**
   * 退货单号
   */
  returnOrderNo: string
  /**
   * 退货单基本信息
   */
  basicData: ReturnOrderBasicDataResponse
  /**
   * 退货单是否需要审批
   */
  needApprove: boolean
  /**
   * 退货单审批信息
   */
  approvalInfo: ReturnApprovalInfoResponse
  /**
   * 退款确认人
   */
  confirmUser: UserResponse
  /**
   * 退货单关联退款单信息
   */
  refundInfo: RefundInfoResponse
  /**
   * 退货商品信息
   */
  returnCommodity: ReturnCommodityResponse
  /**
   * 退款商品信息
   */
  refundCommodity: RefundCommodityResponse
  /**
   * 退货子订单信息
   */
  subOrderInfo: SubOrderInfoResponse1
  /**
   * 来源批次退货单信息
   */
  batchReturnOrder: BatchReturnOrderResponse
  /**
   * 退货商品分销信息（仅分销订单的退货单有值）
   */
  commodityAuthInfo: CommodityAuthInfoResponse
  /**
   * 归属信息
   */
  ownerInfo: OwnerInfoResponse
  /**
   * 是否需要人工确认退款
   */
  needConfirmRefund: boolean
}

/**
 * 退货单统计信息返回值
<AUTHOR>
@date 2022/01/26
 */
export class ReturnOrderStatisticResponse {
  /**
   * 退货单总数
   */
  totalReturnOrderCount: number
  /**
   * 退货单退款总额
   */
  totalRefundAmount: number
}

/**
 * 退货单关联订单信息
<AUTHOR>
@date 2022/3/23
 */
export class OrderInfoResponse1 {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 订单类型（1：常规订单 2：批次关联订单）
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType: number
  /**
   * 关联批次单号
   */
  batchOrderNo: string
  /**
   * 购买渠道（1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道）
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelType: number
  /**
   * 终端（Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统）
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTerminalCodes
   */
  terminalCode: string
  /**
   * 订单支付信息
   */
  orderPaymentInfo: PaymentInfoResponse
  /**
   * 买家信息
   */
  buyer: UserResponse
  /**
   * 创建人
   */
  creator: UserResponse
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 专题id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
  /**
   * 培训计划ID，例如补贴性培训平台和补贴管理系统对接
   */
  policyTrainingSchemeIds: string
  /**
   * 申报单位统一信用代码
   */
  declarationUnitCode: string
}

/**
 * 退款商品信息
<AUTHOR>
@date 2022/03/28
 */
export class RefundCommodityResponse {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse
}

/**
 * 退款单状态变更时间
<AUTHOR>
@date 2022/03/23
 */
export class RefundOrderStatusChangeTimeResponse {
  /**
   * 等待退款
   */
  waiting: string
  /**
   * 退款中
   */
  refunding: string
  /**
   * 已退款
   */
  refunded: string
  /**
   * 退款失败
   */
  failed: string
}

/**
 * 退货审批信息
<AUTHOR>
@date 2022/03/18
 */
export class ReturnApprovalInfoResponse {
  /**
   * 审批状态（0：未审批 1：已审批）
@see ReturnApprovalReportStatus
   */
  approveStatus: number
  /**
   * 审批结果（-1：无 0：拒绝 1：同意）
@see ReturnApprovalReportResults
   */
  approveResult: number
  /**
   * 审批人
   */
  approveUser: UserResponse
  /**
   * 审批意见
   */
  approveComment: string
  /**
   * 审批时间
   */
  approveTime: string
  /**
   * 审批取消时间
   */
  cancelApproveTime: string
}

/**
 * 退货单关闭信息
<AUTHOR>
@date 2022年3月29日 15:48:33
 */
export class ReturnCloseReasonResponse {
  /**
   * 退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
@see ReturnOrderCloseTypes
   */
  closeType: number
  /**
   * 退货单取消人
   */
  cancelUser: UserModel
  /**
   * 取消原因
   */
  cancelReason: string
}

/**
 * 退货商品信息
<AUTHOR>
@date 2022/03/28
 */
export class ReturnCommodityResponse {
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 商品信息
   */
  commoditySku: CommoditySkuResponse
}

/**
 * 退货申请信息返回值
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderApplyInfoResponse {
  /**
   * 申请人
   */
  applyUser: UserResponse
  /**
   * 申请原因内容id
   */
  reasonId: string
  /**
   * 申请原因内容
   */
  reasonContent: string
  /**
   * 申请描述
   */
  description: string
}

/**
 * 退货单基本信息
<AUTHOR>
@date 2022/3/18
 */
export class ReturnOrderBasicDataResponse {
  /**
   * 退货单类型
1-仅退货
2-仅退款
3-退货并退款
4-部分退货
5-部分退款
6-部分退货并部分退款
7-部分退货并全额退款
8-全部退货并部分退款
   */
  returnOrderType: number
  /**
   * 退款总额
   */
  refundAmount: number
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
@see ReturnOrderStatus
   */
  returnOrderStatus: number
  /**
   * 退货单状态变更时间
   */
  returnOrderStatusChangeTime: ReturnOrderStatusChangeTimeResponse
  /**
   * 退货单申请信息
   */
  applyInfo: ReturnOrderApplyInfoResponse
  /**
   * 退货单退货失败信息
   */
  returnFailReason: string
  /**
   * 退货单关闭信息
   */
  returnCloseReason: ReturnCloseReasonResponse
  /**
   * 退货单申请来源类型
SUB_ORDER
BATCH_RETURN_ORDER
@see ReturnOrderApplySourceTypes
   */
  applySourceType: string
  /**
   * 退货单申请来源id
当来源类型为子订单时,该申请来源id为子订单号,为批次退货单申请来源的,该申请来源id为批次退货单号
   */
  applySourceId: string
  /**
   * 销售渠道
@see SaleChannel
   */
  saleChannel: number
  /**
   * 专题id
   */
  saleChannelId: string
  /**
   * 专题名称
   */
  saleChannelName: string
}

/**
 * 退货单状态变更时间
<AUTHOR>
@date 2022/03/23
 */
export class ReturnOrderStatusChangeTimeResponse {
  /**
   * 申请退货时间
   */
  applied: string
  /**
   * 申请退货取消处理中时间
   */
  cancelApplying: string
  /**
   * 退货处理中时间
   */
  returning: string
  /**
   * 退货失败时间
   */
  returnFailed: string
  /**
   * 正在申请退款时间
   */
  refundApplying: string
  /**
   * 已申请退款时间
   */
  refundApplied: string
  /**
   * 退款处理中时间
   */
  refunding: string
  /**
   * 退款失败
   */
  refundFailed: string
  /**
   * 退货完成时间
   */
  returned: string
  /**
   * 退款完成时间
   */
  refunded: string
  /**
   * 退货退款完成时间
   */
  returnedAndRefunded: string
  /**
   * 退货单完成时间
   */
  returnCompleted: string
  /**
   * 已关闭时间
   */
  closed: string
}

/**
 * 退货子订单信息
<AUTHOR>
@date 2022/3/18
 */
export class SubOrderInfoResponse1 {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 子订单有换货
   */
  exchanged: boolean
  /**
   * 主订单信息
   */
  orderInfo: OrderInfoResponse1
  /**
   * 子订单商品数量
   */
  quantity: number
  /**
   * 子订单优惠来源ID
   */
  discountSourceId: string
  /**
   * 子订单优惠类型
@see DiscountType
   */
  discountType: number
  /**
   * 子订单是否使用优惠
   */
  useDiscount: boolean
  /**
   * 销售渠道
   */
  saleChannel: number
  /**
   * 优惠方案
   */
  discountScheme: DiscountSchemeResponse
  /**
   * 定价方案
   */
  pricingPolicy: PricingPolicyResponse
  salePathList: Array<SalePathResponse>
  /**
   * 是否换期
   */
  isExchangeIssue: boolean
  /**
   * 子订单总金额
   */
  amount: number
  /**
   * 子订单最终成交单价
   */
  finalPrice: number
}

export class BatchOrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BatchOrderResponse>
}

export class BatchReturnOrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BatchReturnOrderResponse>
}

export class CommoditySkuForestageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CommoditySkuForestageResponse>
}

export class ExchangeOrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ExchangeOrderResponse>
}

export class IssueCommoditySkuResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<IssueCommoditySkuResponse>
}

export class OfflineInvoiceResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OfflineInvoiceResponse>
}

export class OnlineInvoiceResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OnlineInvoiceResponse>
}

export class OrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OrderResponse>
}

export class PortalCommoditySkuResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PortalCommoditySkuResponse>
}

export class ReturnOrderResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ReturnOrderResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取批次单详情
   * @param query 查询 graphql 语法文档
   * @param batchOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBatchOrderInMySelf(
    batchOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBatchOrderInMySelf,
    operation?: string
  ): Promise<Response<BatchOrderResponse>> {
    return commonRequestApi<BatchOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { batchOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取批次退货单详情
   * @param query 查询 graphql 语法文档
   * @param batchReturnOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBatchReturnOrderInMySelf(
    batchReturnOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBatchReturnOrderInMySelf,
    operation?: string
  ): Promise<Response<BatchReturnOrderResponse>> {
    return commonRequestApi<BatchReturnOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { batchReturnOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取集体缴费购买渠道商品详情
   * @param query 查询 graphql 语法文档
   * @param commoditySkuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCommoditySkuCollectivePurchaseInServicer(
    commoditySkuId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCommoditySkuCollectivePurchaseInServicer,
    operation?: string
  ): Promise<Response<CommoditySkuForestageResponse>> {
    return commonRequestApi<CommoditySkuForestageResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { commoditySkuId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取用户自主购买渠道商品详情
   * @Param commoditySkuId 商品ID
   * @Param isShowAll 是否展示所有资源
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCommoditySkuCustomerPurchaseInServicer(
    params: { commoditySkuId?: string; isShowAll?: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCommoditySkuCustomerPurchaseInServicer,
    operation?: string
  ): Promise<Response<CommoditySkuForestageResponse>> {
    return commonRequestApi<CommoditySkuForestageResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取专题商品详情
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCommoditySkuTrainingChannelInServicer(
    params: { commoditySkuId?: string; trainingChannelId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCommoditySkuTrainingChannelInServicer,
    operation?: string
  ): Promise<Response<CommoditySkuForestageResponse>> {
    return commonRequestApi<CommoditySkuForestageResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取换货单详情
   * @param query 查询 graphql 语法文档
   * @param exchangeOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getExchangeOrderInMySelf(
    exchangeOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getExchangeOrderInMySelf,
    operation?: string
  ): Promise<Response<ExchangeOrderResponse>> {
    return commonRequestApi<ExchangeOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { exchangeOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取用户自主购买渠道的面网授期数型销售商品详情
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIssueCommoditySkuCustomerPurchaseInServicer(
    params: { portalCommoditySkuId?: string; isShowAll?: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIssueCommoditySkuCustomerPurchaseInServicer,
    operation?: string
  ): Promise<Response<IssueCommoditySkuResponse>> {
    return commonRequestApi<IssueCommoditySkuResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取发票详情
   * @param query 查询 graphql 语法文档
   * @param invoiceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOfflineInvoiceInMySelf(
    invoiceId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOfflineInvoiceInMySelf,
    operation?: string
  ): Promise<Response<OfflineInvoiceResponse>> {
    return commonRequestApi<OfflineInvoiceResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { invoiceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取发票详情
   * @param query 查询 graphql 语法文档
   * @param invoiceId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOnlineInvoiceInMySelf(
    invoiceId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineInvoiceInMySelf,
    operation?: string
  ): Promise<Response<OnlineInvoiceResponse>> {
    return commonRequestApi<OnlineInvoiceResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { invoiceId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取我的订单详情
   * @param query 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOrderInMyself(
    orderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOrderInMyself,
    operation?: string
  ): Promise<Response<OrderResponse>> {
    return commonRequestApi<OrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { orderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 门户商品 - 获取用户自主购买渠道商品详情
   * @Param commoditySkuId 商品ID
   * @Param portalCommoditySkuId 门户商品ID或原商品id(使用原商品id的话只能获取到原商品信息）
   * @Param isShowAll 是否展示所有资源
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPortalCommoditySkuCustomerPurchaseInServicer(
    params: { portalCommoditySkuId?: string; isShowAll?: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPortalCommoditySkuCustomerPurchaseInServicer,
    operation?: string
  ): Promise<Response<PortalCommoditySkuResponse>> {
    return commonRequestApi<PortalCommoditySkuResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取我购买的订单的退货单详情
   * @param query 查询 graphql 语法文档
   * @param returnOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getReturnOrderInMyself(
    returnOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getReturnOrderInMyself,
    operation?: string
  ): Promise<Response<ReturnOrderResponse>> {
    return commonRequestApi<ReturnOrderResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { returnOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取批次报名来源
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listBatchRegistrationSources(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBatchRegistrationSources,
    operation?: string
  ): Promise<Response<Array<BatchRegistrationSourceResponse>>> {
    return commonRequestApi<Array<BatchRegistrationSourceResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询用户自主购买渠道的面网授期别型销售商品属性集合
   * @param query 查询 graphql 语法文档
   * @param queryRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listIssueCommoditySkuPropertyCustomerPurchaseInServicer(
    queryRequest: PortalCommoditySkuPropertyRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIssueCommoditySkuPropertyCustomerPurchaseInServicer,
    operation?: string
  ): Promise<Response<PortalCommoditySkuPropertyListResponse>> {
    return commonRequestApi<PortalCommoditySkuPropertyListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { queryRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 学员查看换期记录
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listIssueLogInMySelf(
    request: IssueLogRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listIssueLogInMySelf,
    operation?: string
  ): Promise<Response<Array<IssueLogResponse>>> {
    return commonRequestApi<Array<IssueLogResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 按指定sku统计前N个sku的信息和交易成功的订单数量（有Redis缓存）
   * @param skuProperty sku类型
   * @param topN        前N个，不可超过50
   * @return sku信息和所属订单数量（交易成功）
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listOrderTopNBySkuInServicer(
    params: { skuProperty?: string; topN: number },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listOrderTopNBySkuInServicer,
    operation?: string
  ): Promise<Response<Array<ListOrderTopNBySkuResponse>>> {
    return commonRequestApi<Array<ListOrderTopNBySkuResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 门户商品 - 获取用户自主购买渠道属性集合
   * @param query 查询 graphql 语法文档
   * @param queryRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listPortalCommoditySkuPropertyCustomerPurchaseInServicer(
    queryRequest: PortalCommoditySkuPropertyRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listPortalCommoditySkuPropertyCustomerPurchaseInServicer,
    operation?: string
  ): Promise<Response<PortalCommoditySkuPropertyListResponse>> {
    return commonRequestApi<PortalCommoditySkuPropertyListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { queryRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取集体缴费购买渠道商品属性集合
   * @param query 查询 graphql 语法文档
   * @param queryRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listSkuPropertyCollectivePurchaseInServicer(
    queryRequest: CommoditySkuRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listSkuPropertyCollectivePurchaseInServicer,
    operation?: string
  ): Promise<Response<SkuPropertyListResponse>> {
    return commonRequestApi<SkuPropertyListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { queryRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取用户自主购买渠道商品属性集合
   * @param query 查询 graphql 语法文档
   * @param queryRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listSkuPropertyCustomerPurchaseInServicer(
    queryRequest: SkuPropertyRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listSkuPropertyCustomerPurchaseInServicer,
    operation?: string
  ): Promise<Response<SkuPropertyListResponse>> {
    return commonRequestApi<SkuPropertyListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { queryRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 面向专题 获取专题下商品属性集合（专题门户开放集体缴费管理员可见）
   * @param query 查询 graphql 语法文档
   * @param queryRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listSkuPropertyTrainingChannelCollectiveInServicer(
    queryRequest: CommoditySkuRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listSkuPropertyTrainingChannelCollectiveInServicer,
    operation?: string
  ): Promise<Response<SkuPropertyListResponse>> {
    return commonRequestApi<SkuPropertyListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { queryRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 面向专题 获取专题下商品属性集合（专题门户开放学员可见）
   * @param query 查询 graphql 语法文档
   * @param queryRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listSkuPropertyTrainingChannelCustomerInServicer(
    queryRequest: CommoditySkuRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listSkuPropertyTrainingChannelCustomerInServicer,
    operation?: string
  ): Promise<Response<SkuPropertyListResponse>> {
    return commonRequestApi<SkuPropertyListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { queryRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取专题下商品属性集合
   * @param query 查询 graphql 语法文档
   * @param queryRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listSkuPropertyTrainingChannelInServicer(
    queryRequest: CommoditySkuRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listSkuPropertyTrainingChannelInServicer,
    operation?: string
  ): Promise<Response<SkuPropertyListResponse>> {
    return commonRequestApi<SkuPropertyListResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { queryRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取批次单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageBatchOrderInMySelf(
    params: { page?: Page; request?: BatchOrderRequest; sortRequest?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBatchOrderInMySelf,
    operation?: string
  ): Promise<Response<BatchOrderResponsePage>> {
    return commonRequestApi<BatchOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取批次退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageBatchReturnOrderInMySelf(
    params: { page?: Page; request?: BatchReturnOrderRequest; sortRequest?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBatchReturnOrderInMySelf,
    operation?: string
  ): Promise<Response<BatchReturnOrderResponsePage>> {
    return commonRequestApi<BatchReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取集体缴费购买渠道商品
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommoditySkuCustomerCollectivePurchaseInServicer(
    params: { page?: Page; queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuCustomerCollectivePurchaseInServicer,
    operation?: string
  ): Promise<Response<CommoditySkuForestageResponsePage>> {
    return commonRequestApi<CommoditySkuForestageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取用户自主购买渠道商品
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommoditySkuCustomerPurchaseInServicer(
    params: { page?: Page; queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuCustomerPurchaseInServicer,
    operation?: string
  ): Promise<Response<CommoditySkuForestageResponsePage>> {
    return commonRequestApi<CommoditySkuForestageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 面向专题 商品查询（专题门户开放集体缴费管理员可见）
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommoditySkuTrainingChannelCollectiveInServicer(
    params: { page?: Page; queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuTrainingChannelCollectiveInServicer,
    operation?: string
  ): Promise<Response<CommoditySkuForestageResponsePage>> {
    return commonRequestApi<CommoditySkuForestageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 面向专题 商品查询（专题门户开放学员可见）
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommoditySkuTrainingChannelCustomerInServicer(
    params: { page?: Page; queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuTrainingChannelCustomerInServicer,
    operation?: string
  ): Promise<Response<CommoditySkuForestageResponsePage>> {
    return commonRequestApi<CommoditySkuForestageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 面向专题 商品查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCommoditySkuTrainingChannelInServicer(
    params: { page?: Page; queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCommoditySkuTrainingChannelInServicer,
    operation?: string
  ): Promise<Response<CommoditySkuForestageResponsePage>> {
    return commonRequestApi<CommoditySkuForestageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 换货单分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageExchangeOrderInMySelf(
    params: { page?: Page; request?: ExchangeOrderRequest; sort?: Array<ExchangeOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageExchangeOrderInMySelf,
    operation?: string
  ): Promise<Response<ExchangeOrderResponsePage>> {
    return commonRequestApi<ExchangeOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询集体缴费购买渠道的面网授期别型销售商品
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageIssueCommoditySkuCollectivePurchaseInServicer(
    params: { page?: Page; queryRequest?: IssueCommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageIssueCommoditySkuCollectivePurchaseInServicer,
    operation?: string
  ): Promise<Response<IssueCommoditySkuResponsePage>> {
    return commonRequestApi<IssueCommoditySkuResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询用户自主购买渠道的面网授期别型销售商品
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageIssueCommoditySkuCustomerPurchaseInServicer(
    params: { page?: Page; queryRequest?: IssueCommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageIssueCommoditySkuCustomerPurchaseInServicer,
    operation?: string
  ): Promise<Response<IssueCommoditySkuResponsePage>> {
    return commonRequestApi<IssueCommoditySkuResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询专题下的面网授期别型销售商品
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageIssueCommoditySkuTrainingChannelInServicer(
    params: { page?: Page; queryRequest?: IssueCommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageIssueCommoditySkuTrainingChannelInServicer,
    operation?: string
  ): Promise<Response<IssueCommoditySkuResponsePage>> {
    return commonRequestApi<IssueCommoditySkuResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询发票
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOfflineInvoiceInMySelf(
    params: { page?: Page; request?: OfflineInvoiceRequest; sort?: Array<OfflineInvoiceSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOfflineInvoiceInMySelf,
    operation?: string
  ): Promise<Response<OfflineInvoiceResponsePage>> {
    return commonRequestApi<OfflineInvoiceResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询发票
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOnlineInvoiceInMySelf(
    params: { page?: Page; request?: OnlineInvoiceRequest; sort?: Array<OnlineInvoiceSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOnlineInvoiceInMySelf,
    operation?: string
  ): Promise<Response<OnlineInvoiceResponsePage>> {
    return commonRequestApi<OnlineInvoiceResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询我的订单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOrderInMyself(
    params: { page?: Page; request?: OrderRequest; sortRequest?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOrderInMyself,
    operation?: string
  ): Promise<Response<OrderResponsePage>> {
    return commonRequestApi<OrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 门户商品 - 分页获取用户自主购买渠道商品
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pagePortalCommoditySkuCustomerPurchaseInServicer(
    params: { page?: Page; queryRequest?: PortalCommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pagePortalCommoditySkuCustomerPurchaseInServicer,
    operation?: string
  ): Promise<Response<PortalCommoditySkuResponsePage>> {
    return commonRequestApi<PortalCommoditySkuResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取我购买的订单的退货单分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageReturnOrderInMyself(
    params: { page?: Page; request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageReturnOrderInMyself,
    operation?: string
  ): Promise<Response<ReturnOrderResponsePage>> {
    return commonRequestApi<ReturnOrderResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取订单总金额、总数量
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticOrderInMyself(
    request: OrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticOrderInMyself,
    operation?: string
  ): Promise<Response<OrderStatisticResponse>> {
    return commonRequestApi<OrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取退货单总数量、退款总金额
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticReturnOrderInMyself(
    request: ReturnOrderRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticReturnOrderInMyself,
    operation?: string
  ): Promise<Response<ReturnOrderStatisticResponse>> {
    return commonRequestApi<ReturnOrderStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 统计当前网校下的报名数，即交易成功的订单数量（有Redis缓存）
   * @return 报名数，交易成功的订单数量
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async statisticSignUpCountInServicer(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticSignUpCountInServicer,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
