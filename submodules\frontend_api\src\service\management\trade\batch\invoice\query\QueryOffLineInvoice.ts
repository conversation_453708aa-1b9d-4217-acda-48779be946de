/*
 * @Description: 发票查询(线下)
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:32:55
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-06-07 16:25:35
 */
import { Page, UiPage } from '@hbfe/common'
import {
  OfflineInvoiceSortField,
  OfflineInvoiceSortRequest,
  SortPolicy1
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import TradeQueryBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import OffLinePageInvoiceVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'
import UserModule from '@api/service/management/user/UserModule'
import QueryOffLinePageInvoiceParam from './vo/QueryOffLinePageInvoiceParam'
import ExportGateWay from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import QueryRegion from '@api/service/common/basic-data-dictionary/query/QueryRegion'
import QueryOffLineInvoiceBase from '@api/service/management/trade/batch/invoice/query/vo/QueryOffLineInvoiceBase'

export default class QueryOffLineInvoice extends QueryOffLineInvoiceBase {
  /**
   * 分页电子查询发票 【增值税电子普通发票】
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<offLinePageInvoiceVo>
   */
  async offLinePageInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest> = [
      { field: OfflineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy1.DESC }
    ]
  ): Promise<Array<OffLinePageInvoiceVo>> {
    queryOffLinePageInvoiceParam.invoiceType = 1
    queryOffLinePageInvoiceParam.invoiceCategoryList = [2]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (queryOffLinePageInvoiceParam.userId) {
      request.associationInfo.buyerIdList = [queryOffLinePageInvoiceParam.userId]
    } else if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const idList = await queryStudentIdList.queryPageCollectiveList(page, {
        name: queryOffLinePageInvoiceParam.userName ? queryOffLinePageInvoiceParam.userName : undefined,
        idCard: queryOffLinePageInvoiceParam.idCard ? queryOffLinePageInvoiceParam.idCard : undefined,
        phone: queryOffLinePageInvoiceParam.phone ? queryOffLinePageInvoiceParam.phone : undefined
      })
      if (idList.length === 0) {
        page.totalSize = 0
        page.totalPageSize = 0
        return []
      }
      request.associationInfo.buyerIdList = idList.map(item => item.userInfo.userId)
    }
    const params = {
      page,
      request,
      sort
    }
    const result = await TradeQueryBackstage.pageOfflineInvoiceInServicer(params)
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize
    const data = new Array<OffLinePageInvoiceVo>()
    const userIds = new Array<string>()
    result.data.currentPageData.forEach(item => {
      data.push(OffLinePageInvoiceVo.from(item))
      userIds.push(item.associationInfo.buyer.userId)
    })
    const userIdMap = await this.getUserInfo(userIds)
    data.forEach(item => {
      if (userIdMap.has(item.userId)) {
        const result = userIdMap.get(item.userId)
        item.setUserInfo(result.userName, result.idCard, result.phone, result.email)
      }
    })
    return data
  }
  /**
   * 集体线下发票导出
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  async offLinePageInvoiceInExport(queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 1
    queryOffLinePageInvoiceParam.invoiceCategoryList = [2]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const idList = await queryStudentIdList.queryPageCollectiveList(page, {
        name: queryOffLinePageInvoiceParam.userName ? queryOffLinePageInvoiceParam.userName : undefined,
        idCard: queryOffLinePageInvoiceParam.idCard ? queryOffLinePageInvoiceParam.idCard : undefined,
        phone: queryOffLinePageInvoiceParam.phone ? queryOffLinePageInvoiceParam.phone : undefined
      })
      if (idList.length === 0) {
        request.associationInfo.buyerIdList = ['-1']
      } else {
        request.associationInfo.buyerIdList = idList.map(item => item.userInfo.userId)
      }
    }
    const result = await ExportGateWay.exportOfflineInvoiceInServicerForJxjy(request)
    return result.data
  }
  /**
   * 分页专票查询发票 【增值税专用发票（纸质票）】
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  async offLinePageVatspecialplaInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest> = [
      { field: OfflineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy1.DESC }
    ]
  ): Promise<Array<OffLinePageInvoiceVo>> {
    queryOffLinePageInvoiceParam.invoiceType = 2
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (request.invoiceDeliveryInfo?.deliveryStatusList?.length > 0) {
      request.basicData.billStatusList = []
    }
    if (queryOffLinePageInvoiceParam.userId) {
      request.associationInfo.buyerIdList = [queryOffLinePageInvoiceParam.userId]
    } else if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const idList = await queryStudentIdList.queryPageCollectiveList(page, {
        name: queryOffLinePageInvoiceParam.userName ? queryOffLinePageInvoiceParam.userName : undefined,
        idCard: queryOffLinePageInvoiceParam.idCard ? queryOffLinePageInvoiceParam.idCard : undefined,
        phone: queryOffLinePageInvoiceParam.phone ? queryOffLinePageInvoiceParam.phone : undefined
      })
      if (idList.length === 0) {
        page.totalSize = 0
        page.totalPageSize = 0
        return []
      }
      request.associationInfo.buyerIdList = idList.map(item => item.userInfo.userId)
    }
    const params = {
      page,
      request,
      sort
    }
    const result = await TradeQueryBackstage.pageOfflineInvoiceInServicer(params)
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize
    const data = new Array<OffLinePageInvoiceVo>()
    const userIds = new Array<string>()
    result.data.currentPageData.forEach(item => {
      data.push(OffLinePageInvoiceVo.from(item))
      userIds.push(item.associationInfo.buyer.userId)
    })
    const userIdMap = await this.getUserInfo(userIds)
    data.forEach(item => {
      if (userIdMap.has(item.userId)) {
        const result = userIdMap.get(item.userId)
        item.setUserInfo(result.userName, result.idCard, result.phone, result.email)
      }
    })
    if (data.length === 0) return data
    const regionS = data
      .map(item => {
        if (item.deliveryInfo?.deliveryAddress) {
          return item.deliveryInfo.deliveryAddress?.region
        }
      })
      .filter(item => item)
    let map = new Map<string, string>()
    if (regionS?.length) {
      map = await QueryRegion.querRegionDetilAll(regionS)
    }
    data.forEach(item => {
      if (item.deliveryInfo?.deliveryAddress && item.deliveryInfo.deliveryAddress?.region) {
        if (item.deliveryInfo.deliveryAddress.region[0] === '/') {
          item.deliveryInfo.deliveryAddress.region = map.get(item.deliveryInfo.deliveryAddress.region)
            ? map.get(item.deliveryInfo.deliveryAddress.region)
            : item.deliveryInfo.deliveryAddress.region
        } else {
          item.deliveryInfo.deliveryAddress.region = map.get('/' + item.deliveryInfo.deliveryAddress.region)
            ? map.get('/' + item.deliveryInfo.deliveryAddress.region)
            : item.deliveryInfo.deliveryAddress.region
        }
      }
    })
    return data
  }
  /**
   * 分页专票查询发票 【增值税专用发票（电子）】
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  async offLinePageElectVatspecialplaInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest> = [
      { field: OfflineInvoiceSortField.INVOICE_CREAT_TIME, policy: SortPolicy1.DESC }
    ]
  ): Promise<Array<OffLinePageInvoiceVo>> {
    queryOffLinePageInvoiceParam.invoiceType = 1
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (request.invoiceDeliveryInfo?.deliveryStatusList?.length > 0) {
      request.basicData.billStatusList = []
    }
    if (queryOffLinePageInvoiceParam.userId) {
      request.associationInfo.buyerIdList = [queryOffLinePageInvoiceParam.userId]
    } else if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const idList = await queryStudentIdList.queryPageCollectiveList(page, {
        name: queryOffLinePageInvoiceParam.userName ? queryOffLinePageInvoiceParam.userName : undefined,
        idCard: queryOffLinePageInvoiceParam.idCard ? queryOffLinePageInvoiceParam.idCard : undefined,
        phone: queryOffLinePageInvoiceParam.phone ? queryOffLinePageInvoiceParam.phone : undefined
      })
      if (idList.length === 0) {
        page.totalSize = 0
        page.totalPageSize = 0
        return []
      }
      request.associationInfo.buyerIdList = idList.map(item => item.userInfo.userId)
    }
    const params = {
      page,
      request,
      sort
    }
    const result = await TradeQueryBackstage.pageOfflineInvoiceInServicer(params)
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize
    const data = new Array<OffLinePageInvoiceVo>()
    const userIds = new Array<string>()
    result.data.currentPageData.forEach(item => {
      data.push(OffLinePageInvoiceVo.from(item))
      userIds.push(item.associationInfo.buyer.userId)
    })
    const userIdMap = await this.getUserInfo(userIds)
    data.forEach(item => {
      if (userIdMap.has(item.userId)) {
        const result = userIdMap.get(item.userId)
        item.setUserInfo(result.userName, result.idCard, result.phone, result.email)
      }
    })
    if (data.length === 0) return data
    const regionS = data
      .map(item => {
        if (item.deliveryInfo?.deliveryAddress) {
          return item.deliveryInfo.deliveryAddress?.region
        }
      })
      .filter(item => item)
    let map = new Map<string, string>()
    if (regionS?.length) {
      map = await QueryRegion.querRegionDetilAll(regionS)
    }
    data.forEach(item => {
      if (item.deliveryInfo?.deliveryAddress && item.deliveryInfo.deliveryAddress?.region) {
        if (item.deliveryInfo.deliveryAddress.region[0] === '/') {
          item.deliveryInfo.deliveryAddress.region = map.get(item.deliveryInfo.deliveryAddress.region)
            ? map.get(item.deliveryInfo.deliveryAddress.region)
            : item.deliveryInfo.deliveryAddress.region
        } else {
          item.deliveryInfo.deliveryAddress.region = map.get('/' + item.deliveryInfo.deliveryAddress.region)
            ? map.get('/' + item.deliveryInfo.deliveryAddress.region)
            : item.deliveryInfo.deliveryAddress.region
        }
      }
    })
    return data
  }
  /**
   * 集体线下发票导出 - 专票（纸质票）
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  async offLinePageVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 2
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const idList = await queryStudentIdList.queryPageCollectiveList(page, {
        name: queryOffLinePageInvoiceParam.userName ? queryOffLinePageInvoiceParam.userName : undefined,
        idCard: queryOffLinePageInvoiceParam.idCard ? queryOffLinePageInvoiceParam.idCard : undefined,
        phone: queryOffLinePageInvoiceParam.phone ? queryOffLinePageInvoiceParam.phone : undefined
      })
      if (idList.length === 0) {
        request.associationInfo.buyerIdList = ['-1']
      } else {
        request.associationInfo.buyerIdList = idList.map(item => item.userInfo.userId)
      }
    }
    const result = await ExportGateWay.exportOfflineInvoiceInServicerForJxjy(request)
    return result.data
  }
  /**
   * 集体线下发票导出 - 专票（电子票）
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  async offLinePageElectVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 1
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryCollectiveManagerList
      const page = new UiPage()
      page.pageNo = 1
      page.pageSize = 200
      const idList = await queryStudentIdList.queryPageCollectiveList(page, {
        name: queryOffLinePageInvoiceParam.userName ? queryOffLinePageInvoiceParam.userName : undefined,
        idCard: queryOffLinePageInvoiceParam.idCard ? queryOffLinePageInvoiceParam.idCard : undefined,
        phone: queryOffLinePageInvoiceParam.phone ? queryOffLinePageInvoiceParam.phone : undefined
      })
      if (idList.length === 0) {
        request.associationInfo.buyerIdList = ['-1']
      } else {
        request.associationInfo.buyerIdList = idList.map(item => item.userInfo.userId)
      }
    }
    const result = await ExportGateWay.exportOfflineInvoiceInServicerForJxjy(request)
    return result.data
  }
}
