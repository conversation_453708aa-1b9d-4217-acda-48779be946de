import QueryExamPaperList from './query/QueryExamPaperList'
import QueryExamPaperDetail from './query/QueryExamPaperDetail'
class QueryExamPaperFactory {
  /**
   * @description: 获取查询试卷列表
   * @param {*}
   * @return {*}
   */
  get queryExamPaperList() {
    return new QueryExamPaperList()
  }

  /**
   * @description: 获取试卷详情
   * @param {string} id 试卷id
   */
  get queryExamPaperDetail() {
    return (id: string) => {
      return new QueryExamPaperDetail(id)
    }
  }
}
export default new QueryExamPaperFactory()
