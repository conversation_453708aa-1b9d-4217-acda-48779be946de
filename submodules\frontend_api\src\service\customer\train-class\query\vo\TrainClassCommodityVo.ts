import MSCommodity from '@api/ms-gateway/ms-commodity-v1'
import MsLearningScheme from '@api/ms-gateway/ms-learningscheme-v1'
import MsLearningQueryFrontGatewayCourseLearningForestage, {
  LearningRegisterRequest,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import MsTradeQueryFrontGatewayCourseLearningForestage, {
  CommoditySkuForestageResponse,
  CommoditySkuPropertyResponse,
  OrderRequest,
  PortalCommoditySkuPropertyResponse,
  UserPossessionInfoResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { SubjectType } from '@api/service/customer/thematic-config/enum/SubjectType'
import SchemeUtil, { CheckTrainClassParam } from '@api/service/customer/train-class/offlinePart/util/SchemeUtil'
import { SourceEnum } from '@api/service/customer/train-class/query/Enum/SourceEnum'
import SkuPropertyResponseVo from '@api/service/customer/train-class/query/vo/SkuPropertyResponseVo'
import { Response } from '@hbfe/common'

/**
 * 培训班列表Vo
 */
class TrainClassCommodityVo extends CommoditySkuForestageResponse {
  /**
   * 门户商品 - 商品id
   */
  portalCommoditySkuId: string
  /**
   * 门户商品 - 来源类型
   1-网校商品 2-专题商品
   */
  portalCommoditySkuSourceType: SourceEnum
  /**
   * 门户商品 - 来源id(服务商id,专题id)
   */
  portalCommoditySkuSourceId: string
  /**
   * 专题类型
   */
  specialType: Array<SubjectType> = null
  /**
   * 专题行业
   */
  specialIndustry = ''
  /**
   * 专题单位
   */
  specialUnit = ''
  /**
   * 商品属性信息
   */
  declare skuProperty: CommoditySkuPropertyResponse & PortalCommoditySkuPropertyResponse
  // region properties
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 方案类型
   */
  schemeType: string
  /**
   *班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuValueNameProperty = new SkuPropertyResponseVo()
  /**
   * 培训方案须知
   */
  notice = ''
  /**
   * 学号（用于期别跳转）
   */
  studentNo = ''

  /**
   * 是否弹窗展示须知
   */
  showNoticeDialog = false
  // /**
  //  * 第三方平台信息
  //  */
  // thirdPartyInfo: ThirdPartyItem = null
  // endregion
  // region methods

  /**
   * 购买前校验
   */
  async checkTrainClass(channelType = 1, terminalCode = 'Web'): Promise<Response<string>> {
    const res = await MSCommodity.validateCommodity({
      commoditySkuId: this.commoditySkuId,
      channelType: channelType,
      terminalCode: terminalCode
    })
    let orderNo = ''
    if (res.status.isSuccess()) {
      if (res.data.code == '200') {
        const schemeRes = await MsLearningScheme.reservingSchemeValidate({
          schemeId: this.schemeId
        })
        if (res.status.isSuccess()) {
          res.status.code = parseInt(schemeRes.data.code)
          res.status.message = schemeRes.data.message
        } else {
          res.status = schemeRes.status
        }
        if (res.status.code == 50001) {
          try {
            const subOrderNo = schemeRes.data.duplicateReservingInfos[0].sourceId
            const request = new OrderRequest()
            request.subOrderNoList = [subOrderNo]
            const orderListRes = await MsTradeQueryFrontGatewayCourseLearningForestage.pageOrderInMyself({
              page: {
                pageNo: 1,
                pageSize: 1
              },
              request: request
            })
            if (orderListRes.status.isSuccess()) {
              const order = orderListRes.data.currentPageData[0]
              if ([0, 1].includes(order.basicData.orderPaymentStatus)) {
                if (order.basicData.channelType == 2) {
                  res.status.code = 52009
                  orderNo = order.orderNo
                } else {
                  res.status.code = 52001
                  orderNo = order.orderNo
                }
              }
            }
          } catch (e) {
            console.log('获取订单信息失败，', e)
          }
        }
      } else {
        res.status.code = parseInt(res.data.code)
        res.status.message = res.data.message
      }
    }
    const newRes = new Response<string>()
    Object.assign(newRes, res)
    if (newRes.status.code == 52001) {
      newRes.data = orderNo
    }
    return newRes
  }

  /*
   *    获取参训资格id，仅在已报名状态才能获取
   * */
  async getQualificationId() {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.sourceType = this.possessionInfo.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
    request.learningRegister.sourceId = this.possessionInfo.sourceId
    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      this.studentNo = res.data.currentPageData[0].studentNo
      return res.data.currentPageData[0].qualificationId
    }
    return ''
  }

  /*
   *    获取培训状态是否合格，仅在已报名状态才能获取
   * */
  async getTrainingResult() {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.sourceType = this.possessionInfo.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
    request.learningRegister.sourceId = this.possessionInfo.sourceId
    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      return res.data.currentPageData[0].studentLearning.trainingResult
    }
    return ''
  }

  // endregion
  /**
   * 获取订单号，仅在已报名状态才能获取
   */
  async getOrderNo(possessionInfo?: UserPossessionInfoResponse) {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.sourceType = possessionInfo.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
    request.learningRegister.sourceId = possessionInfo.sourceId
    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      return res.data.currentPageData[0].learningRegister.orderNo
    }
    return ''
  }

  /**
   * 购买前校验
   * @param param.channelType 渠道类型
   * @param param.terminalCode 终端类型
   * @param param.issueId 期别id，面网授班、面授班必传
   */
  async checkTrainClassNew(param: { channelType: 1; terminalCode: 'Web'; issueId?: string }) {
    const request = new CheckTrainClassParam()
    request.commoditySkuId = this.commoditySkuId
    request.schemeId = this.schemeId
    request.channelType = param.channelType
    request.terminalCode = param.terminalCode
    if (param.issueId) {
      request.issueId = param.issueId
    }
    return await SchemeUtil.checkTrainClass(request)
  }
}

export default TrainClassCommodityVo
