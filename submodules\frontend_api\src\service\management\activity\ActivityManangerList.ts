import { Page } from '@hbfe/common'
import ActivityManangeFilterModel from '@api/service/management/activity/models/ActivityManangeFilterModel'
import ActivityManangeItemModel from '@api/service/management/activity/models/ActivityManangeItemModel'
import CourseLearningBackstage, {
  StudentLearningExperienceRequest,
  StudentLearningExperienceSortRequest,
  StudentLearningExperienceSortEnum,
  LearningExperienceTopicRequest,
  SortTypeEnum12,
  SchemeRequest,
  UserRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import DataExportBackstage, {
  StudentLearningExperienceRequest as StudentLearningExperienceExportRequest
} from '@api/platform-gateway/diff-ms-data-export-front-gateway-DataExportBackstage'
import MsBasicDataQueryBackstageGateway, {
  StudentQueryRequest,
  StudentUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import UserModule from '@api/service/management/user/UserModule'
import ConfigJsonUtil from '@api/service/management/train-class/Utils/ConfigJsonUtil'
import MsMySchemeQueryFrontGatewayCourseLearningForeStage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
export default class ActivityManangerList {
  /**
   * 筛选项
   */
  filterItem: ActivityManangeFilterModel = new ActivityManangeFilterModel()
  /**
   * 列表项
   */
  items: Array<ActivityManangeItemModel> = new Array<ActivityManangeItemModel>()

  /**
   * 查询列表
   */
  async queryList(page?: Page) {
    let request = new StudentLearningExperienceRequest()
    request = this.filterItem.toQueryRequest()
    if (this.filterItem.idCard || this.filterItem.name) {
      const userIds = await this.queryUserList()
      if (userIds?.length) {
        request.user = new UserRequest()
        request.user.userIds = userIds
      }
    }
    const sorts = new Array<StudentLearningExperienceSortRequest>()
    sorts.push(new StudentLearningExperienceSortRequest())
    sorts[0].sort = StudentLearningExperienceSortEnum.CREATE_TIME
    sorts[0].sortType = SortTypeEnum12.DESC
    const res = await CourseLearningBackstage.pageLearningExperienceInServicer({ page, request, sorts })
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize
    const module = UserModule.queryUserFactory.queryStudentList
    if (res.status.isSuccess()) {
      const userIdList = res.data.currentPageData.map(item => {
        return item.student.userId
      })
      if (userIdList?.length) {
        const userList = await module.queryStudentListInSubject(userIdList)
        this.items = res.data.currentPageData.map((item, index) => {
          let sitem = new ActivityManangeItemModel()
          sitem = ActivityManangeItemModel.from(item)
          userList.data.map(ssitem => {
            if (ssitem.userId == item.student.userId) {
              sitem.idCard = ssitem.idCard
              sitem.detail.name = sitem.name = ssitem.userName
            }
          })
          return sitem
        })
      } else {
        this.items = res.data.currentPageData.map(item => {
          return ActivityManangeItemModel.from(item)
        })
      }
      const schemeIdList = this.items.map(item => {
        return item.schemeId
      })
      const queryM = new ConfigJsonUtil()
      const configJsonMap = await queryM.batchQuerySchemeJsonConfigMapBySchemeId(schemeIdList, ['name'])
      this.items.forEach(item => {
        const temp = configJsonMap.get(item.schemeId)
        if (temp) {
          item.detail.schemeName = item.schemeName = temp.name
        }
      })
    } else {
      console.error(res, 'activityList queryList')
      return []
    }

    return this.items
  }
  /**
   * 导出列表数据
   */
  async exportList() {
    let request = new StudentLearningExperienceExportRequest()
    request = this.filterItem.toExportQueryRequest()
    const result = await DataExportBackstage.exportStudentLearningExperienceInServicer(request)
    return result
  }

  /**
   * 批量导出学习心得
   */
  async exportLearningExpreList() {
    let request = new StudentLearningExperienceExportRequest()
    request = this.filterItem.toExportQueryRequest()
    const result = await DataExportBackstage.BatchStudentLearningExperienceExportPdf(request)
    return result
  }
  /**
   * 查询当前服务商下的学员心得主题列表
   */
  async queryThemeList() {
    const request = new LearningExperienceTopicRequest()
    request.scheme = new SchemeRequest()
    request.scheme.schemeId = this.filterItem.schemeIds[0]
    const result = await CourseLearningBackstage.listLearningExperienceTopic(request)
    return result.data
  }
  /**
   * 获取用户Id列表
   */
  async queryUserList() {
    const request = new StudentQueryRequest()
    request.user = new StudentUserRequest()
    request.user.userName = this.filterItem.name?.trim()
    request.user.idCard = this.filterItem.idCard?.trim()
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    let res
    if (this.filterItem.name || this.filterItem.idCard) {
      res = await MsBasicDataQueryBackstageGateway.pageStudentInfoInServicer({
        page,
        request
      })
    } else {
      return []
    }
    if (!res.status?.isSuccess() || !res.data.currentPageData?.length) {
      return []
    }
    const temp = res.data.currentPageData.map(item => {
      return item.userInfo.userId
    })
    return temp
  }
  /**
   * 获取学习心得考核结果
   */
  async getStudentLearningExperienceResult(qualificationId: string) {
    console.log(qualificationId, 'qualificationId')
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getSchemeLearningInMyself(qualificationId)
    if (res.status.isSuccess()) {
      console.log(
        res.data?.studentLearning?.learningExperienceLearning?.learningExperienceAssessResult,
        '学习心得考核结果'
      )
      return res.data.studentLearning.learningExperienceLearning.learningExperienceAssessResult
    } else {
      return false
    }
  }
}
