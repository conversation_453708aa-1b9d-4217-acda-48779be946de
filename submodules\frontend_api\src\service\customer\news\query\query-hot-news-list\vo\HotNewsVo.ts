/*
 * @Description: 热门资讯Vo
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-25 08:46:47
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 14:25:23
 */

import { NewsInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
class HotNewsVo {
  // 资讯类别ID
  necId: string = null
  // * 资讯类别名称
  necName: string = null
  // 资讯ID
  id: string = null
  // 资讯标题
  title: string = null
  // 发布时间
  date: string = null
  /**
   * DTO转VO
   * @param DTO 后端模型
   * @returns VO
   */
  static from(newsInfoResponse: NewsInfoResponse) {
    const hotNewsVo = new HotNewsVo()
    hotNewsVo.id = newsInfoResponse.newId
    hotNewsVo.title = newsInfoResponse.title
    hotNewsVo.date = newsInfoResponse.publishTime
    hotNewsVo.necId = newsInfoResponse.necId
    return hotNewsVo
  }
}

export default HotNewsVo
