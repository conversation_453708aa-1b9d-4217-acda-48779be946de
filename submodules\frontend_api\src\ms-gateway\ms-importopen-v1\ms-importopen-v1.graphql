"""独立部署的微服务,K8S服务名:ms-importopen-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出导入开班类型的导入数据，
		@param batchNo 批次编号
		@return excel文件路径
	"""
	exportExcel(batchNo:String!,onlyFail:Boolean!):String
	"""导出所有导入数据
		@param mainTaskId:
		@return {@link String}
		<AUTHOR> By Cb
		@date 2022/5/24 17:35
	"""
	exportExcelAllData(mainTaskId:String!):String
	findImportDataByPage(request:ImportOpenQueryRequest,page:Page):MetaRowPage @page(for:"MetaRow")
	findImportDataWithSelfByPage(request:ImportOpenQueryRequest,page:Page):MetaRowPage @page(for:"MetaRow")
	findImportDataWithServicerByPage(request:ImportOpenQueryRequest,page:Page):MetaRowPage @page(for:"MetaRow")
	findTaskExecuteResponseByPage(param:TaskExecuteParamRequest,page:Page):TaskExecuteByPageResponsePage @page(for:"TaskExecuteByPageResponse")
	findTaskExecuteWithSelfResponseByPage(param:TaskExecuteParamRequest,page:Page):TaskExecuteByPageResponsePage @page(for:"TaskExecuteByPageResponse")
	findTaskExecuteWithServicerResponseByPage(param:TaskExecuteParamRequest,page:Page):TaskExecuteByPageResponsePage @page(for:"TaskExecuteByPageResponse")
	"""查询导入开班类型的模板地址"""
	queryImportOpenTemplatePath:String
	"""查询模板地址"""
	queryImportOpenTemplatePathByCategory(request:ImportOpenModelQueryRequest):String
}
type Mutation {
	"""导入开通，用于江苏工考
		@param importRequest 导入信息
		@return 集体报名编号
	"""
	importOpen(importRequest:ImportOpenImportInfoRequest):String
	"""导入开通，校验参数不能为空，包含密码优化版本，目前用于通用平台，补贴性培训平台
		@param importRequest 导入信息
		@return 集体报名编号
	"""
	importOpenForVerify(importRequest:ImportOpenImportInfoForVerifyRequest):ImportOpenResponse
	"""申报单位导入开通，校验参数不能为空，包含密码优化版本，目前用于补贴性培训平台
		@param importRequest 导入信息
		@return 集体报名编号
	"""
	importOpenSignUpUnitForVerify(importRequest:ImportOpenImportInfoForVerifyRequest):ImportOpenResponse
	"""专题管理员导入开通，校验参数不能为空
		@param importRequest 导入信息
		@return 集体报名编号
	"""
	trainingChannelAdministratorImportOpenForVerify(importRequest:ImportOpenImportInfoForVerifyRequest):ImportOpenResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""批量并行查询"""
input BatchParallelQueryDto @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.entities.BatchParallelQueryDto") {
	"""不同ParallelQueryDto间查询条件相互独立"""
	parallelQueryDtoList:[ParallelQueryDto]
}
input ImportOpenQueryParamRequest @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.entities.ImportOpenQueryParamRequest") {
	"""主任务分类
		导入开班-ADMIN_NORMAL_IMPORT（默认）
		导入开班并学习-ADMIN_NORMAL_IMPORT_AND_LEARNING
	"""
	taskCategoryList:[String]
	"""主任务id"""
	mainTaskId:String
	"""不同的BatchParallelQueryDto间查询条件不相互独立，需要同时满足条件才可以查到结果"""
	batchParallelQueryDtoList:[BatchParallelQueryDto]
	"""学习状态
		流程还没到学习-null
		待处理-0
		进行中-1
		处理失败-2
		处理成功-3
	"""
	learningState:Int
	"""商品id"""
	commodityId:String
	"""子任务状态
		0 - 已创建
		1 - 已就绪
		2 - 执行中
		3 - 执行完成
	"""
	subTaskState:Int
	"""子任务处理结果
		0 - 未处理
		1 - 处理成功
		2 - 处理失败
		3 - 就绪失败
	"""
	subTaskProcessResult:Int
	"""导入开始时间"""
	importStartTime:DateTime
	"""导入结束时间"""
	importEndTime:DateTime
}
"""单个属性元数据
	<AUTHOR>
	@since 2022/4/20
"""
input MetaProperty @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.entities.MetaProperty") {
	"""属性键"""
	key:String
	"""属性值"""
	value:String
}
"""单个属性元数据
	<AUTHOR>
	@since 2022/4/20
"""
input MetaPropertyExist @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.entities.MetaPropertyExist") {
	"""属性键
		订单状态：orderState
	"""
	key:String
	"""是否存在"""
	value:Boolean!
}
input ParallelQueryDto @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.entities.ParallelQueryDto") {
	commodityId:String
	"""子任务状态
		0 - 已创建
		1 - 已就绪
		2 - 执行中
		3 - 执行完成
	"""
	subTaskState:Int
	"""子任务处理结果
		0 - 未处理
		1 - 处理成功
		2 - 处理失败
		3 - 就绪失败
	"""
	subTaskProcessResult:Int
	"""查询属性是否存在集合"""
	existProperties:[MetaPropertyExist]
	"""订单状态
		0-未开通 1-开通中 2-已开通，3-无法创建订单，默认null
		@see OrderStateConstant
	"""
	orderState:Int
}
"""导入开通导入参数"""
input ImportOpenImportInfoForVerifyRequest @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.request.ImportOpenImportInfoForVerifyRequest") {
	"""文件路径"""
	filePath:String
	"""文件名称"""
	fileName:String
	"""主任务分类
		导入开班-ADMIN_NORMAL_IMPORT（默认）
		导入开班并学习-ADMIN_NORMAL_IMPORT_AND_LEARNING
	"""
	category:String
	"""终端类型
		<p>
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	terminalCode:String
	"""注册方式
		@see AccountRegisterTypes
	"""
	registerType:Int!
	"""来源类型
		@see AccountSourceTypes
	"""
	sourceType:Int!
	"""默认密码"""
	password:String
	"""密码模式
		1-默认密码——password
		2-使用身份证后六位
	"""
	passwordModel:Int!
	"""密码生效范围，1-默认仅新用户，2-全部用户（含已注册）"""
	passwordEffectiveRange:Int!
	"""是否更新基础信息"""
	updateBasicInfo:Boolean!
	"""销售渠道Id（自营渠道为空，专题渠道时必填）"""
	saleChannelId:String
	"""销售渠道类型
		0-自营渠道
		2-专题渠道
	"""
	saleChannel:Int!
	"""培训方案名称
		<p>放在data.incidentalInformation中<p/>
	"""
	schemeName:String
	"""培训方案id
		<p>放在data中<p/>
	"""
	schemeId:String
	"""培训计划id"""
	trainingPlanId:String
	"""申报单位统一社会信用代码"""
	applyCompanyCode:String
}
"""导入开通导入参数"""
input ImportOpenImportInfoRequest @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.request.ImportOpenImportInfoRequest") {
	"""文件路径"""
	filePath:String
	"""文件名称"""
	fileName:String
	"""主任务分类
		导入开班-ADMIN_NORMAL_IMPORT（默认）
		导入开班并学习-ADMIN_NORMAL_IMPORT_AND_LEARNING
	"""
	category:String
	"""终端类型
		<p>
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	terminalCode:String
	"""注册方式
		@see AccountRegisterTypes
	"""
	registerType:Int!
	"""来源类型
		@see AccountSourceTypes
	"""
	sourceType:Int!
	"""默认密码"""
	password:String
	"""密码模式
		1-默认密码——password
		2-使用身份证后六位
	"""
	passwordModel:Int!
	"""销售渠道Id（自营渠道为空，专题渠道时必填）"""
	saleChannelId:String
	"""销售渠道类型
		0-自营渠道
		2-专题渠道
	"""
	saleChannel:Int!
}
"""导入开通模板查询"""
input ImportOpenModelQueryRequest @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.request.ImportOpenModelQueryRequest") {
	"""主任务分类
		导入开班-ADMIN_NORMAL_IMPORT（默认）
		导入开班并学习-ADMIN_NORMAL_IMPORT_AND_LEARNING
	"""
	category:String
}
"""集体缴费查询对象
	<AUTHOR>
"""
input ImportOpenQueryRequest @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.request.ImportOpenQueryRequest") {
	"""查询属性集合"""
	metaPropertyList:[MetaProperty]
	"""额外附加查询参数"""
	queryParam:ImportOpenQueryParamRequest
}
input TaskExecuteParamRequest @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.request.TaskExecuteParamRequest") {
	"""主任务分类
		导入开班-ADMIN_NORMAL_IMPORT（默认）
		导入开班并学习-ADMIN_NORMAL_IMPORT_AND_LEARNING
	"""
	taskCategoryList:[String]
	"""任务名称"""
	taskName:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""执行时间（起始）"""
	executeStartTime:DateTime
	"""执行时间（终止）"""
	executeEndTime:DateTime
}
"""各状态及执行结果对应数量
	<AUTHOR>
"""
type EachStateCount @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.entities.EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Int!
}
"""单个属性元数据
	<AUTHOR>
	@since 2022/4/20
"""
type MetaProperty1 @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.entities.MetaProperty") {
	"""属性键"""
	key:String
	"""属性值"""
	value:String
}
"""数据行对象
	<AUTHOR>
	@since 2022/4/24
"""
type MetaRow @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.entities.MetaRow") {
	"""每一行的数据"""
	row:[MetaProperty1]
	"""子任务状态"""
	subTaskState:Int!
	"""订单号"""
	orderNo:String
	"""是否更新密码"""
	updatePassword:Boolean
	"""是否更新基础信息"""
	updateBasicInfo:Boolean
	"""学员状态
		null-暂无记录
		0-默认
		1-新学员
		2-已注册的学员
	"""
	studentState:Int
	"""流程还没到下单-null
		未开通-0
		开通中-1
		已开通-2
		无法创建订单-3
	"""
	orderState:Int
	"""学习状态
		流程还没到学习-null
		待处理-0
		进行中-1
		处理失败-2
		处理成功-3
	"""
	learningState:Int
	"""创建时间"""
	createTime:DateTime
	"""完成时间"""
	completeTime:DateTime
	"""错误日志"""
	errorMessage:String
	"""执行结果"""
	result:Int!
}
"""导入开班上传模板结果
	异常code
	500-接口异常
	3003-表头校验失败
	3004-excel表最大长度校验失败
	<AUTHOR>
	@since 2022/5/12
"""
type ImportOpenResponse @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.response.ImportOpenResponse") {
	"""批次订单号"""
	batchOrderNo:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""任务执行情况
	<AUTHOR>
	@since 2022/5/5
"""
type TaskExecuteByPageResponse @type(value:"com.fjhb.ms.importopen.v1.kernel.gateway.graphql.response.TaskExecuteByPageResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""任务名称"""
	name:String
	"""任务分类"""
	category:String
	"""所属批次单编号"""
	batchNo:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""创建时间"""
	createdTime:DateTime
	"""就绪时间"""
	alreadyTime:DateTime
	"""执行时间"""
	executingTime:DateTime
	"""完成时间"""
	completedTime:DateTime
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
}

scalar List
type MetaRowPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [MetaRow]}
type TaskExecuteByPageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TaskExecuteByPageResponse]}
