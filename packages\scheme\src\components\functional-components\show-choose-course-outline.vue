<template>
  <div class="pure">
    <el-card
      shadow="never"
      class="m-card is-header f-mb15"
      v-for="(item, index) in outlineInfo.childOutlines"
      :key="index"
    >
      <div class="m-tit is-small bg-gray is-border-bottom">
        <span class="tit-txt">{{ item.name }}</span>
        <el-tag type="danger" effect="dark" size="mini" class="f-ml10" v-if="item.category">
          {{ item.category === 1 ? '必修' : '选修' }}
        </el-tag>
        <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
          <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
          <div slot="content">
            {{
              item.category
                ? item.category == 1
                  ? '本分类的课程为必修课程，报名成功后直接推送给学员，无需选课。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。'
                  : '此分类下的课程为选修课程，支持配置选课要求学时。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。'
                : '本分类的课程为兴趣课程，报名成功后直接推送给学员，无需选课。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置'
            }}
          </div>
        </el-tooltip>
        <span class="f-fb f-ml20 f-flex-sub f-tr">
          一共 <i class="f-cr">{{ courseStatistic(item).courseTotal }}</i> 门，<i class="f-cr">{{
            courseStatistic(item).coursePeriodTotal
          }}</i>
          学时
        </span>
      </div>
      <el-row class="is-height" v-loading="selectedOutlineInfoList[index].contentLoading">
        <el-col :sm="8" :xl="6" class="is-border-right">
          <div class="f-p20">
            <el-tree
              :data="[item]"
              node-key="id"
              :expand-on-click-node="false"
              class="m-course-tree"
              default-expand-all
              :props="{ label: 'name', children: 'childOutlines' }"
              @node-click="
                (data, node, obj) => {
                  return handleNodeClick(data, node, obj, index)
                }
              "
            >
            </el-tree>
          </div>
        </el-col>
        <el-col :sm="16" :xl="18">
          <div class="f-p20">
            <div class="f-flex f-align-center">
              <div class="m-tit is-mini">
                <span class="tit-txt">{{ selectedOutlineInfoList[index].currentNode.name }}</span>
              </div>
              <div class="f-fb f-flex-sub">
                （一共
                <i class="f-cr">{{ courseStatistic(selectedOutlineInfoList[index].currentNode).courseTotal }}</i>
                门，<i class="f-cr">
                  {{ courseStatistic(selectedOutlineInfoList[index].currentNode).coursePeriodTotal }} </i
                >学时）
              </div>
            </div>
            <el-table
              :ref="`courseTableRef_${index}`"
              stripe
              :data="selectedOutlineInfoList[index].courseList"
              v-loading="selectedOutlineInfoList[index].courseQuery.loading"
              max-height="400px"
              class="m-table f-mt15"
            >
              <el-table-column label="No." width="60" align="center">
                <template slot-scope="scope">
                  <span
                    :data-index="scope.$index + 1"
                    v-observe-visibility="(isVisible, entry) => visibleCourseList(isVisible, entry, index)"
                    >{{ scope.$index + 1 }}</span
                  >
                </template>
              </el-table-column>
              <el-table-column label="课程名称" min-width="240">
                <template slot-scope="scope">{{ scope.row.name }}</template>
              </el-table-column>
              <el-table-column label="分类信息" min-width="200">
                <template slot-scope="scope">{{ getCourseCategory(scope.row) }}</template>
              </el-table-column>
              <el-table-column label="所属课程包名称" min-width="200">
                <template slot-scope="scope">{{ scope.row.sourceCoursePackageName }}</template>
              </el-table-column>
              <el-table-column label="课程学时数" width="120" align="center">
                <template slot-scope="scope">{{ scope.row.period }}</template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Watch, Prop, PropSync } from 'vue-property-decorator'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import { TreeNode } from 'element-ui/types/tree'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
  import TrainingOutlineCourse from '@api/service/customer/course/query/vo/TrainingOutlineCourse'
  import CourseInSchemeResult from '@api/service/management/resource/course/query/vo/CourseInSchemeResult'
  import SchemeDetailUtils from '@hbfe/jxjy-admin-scheme/src/utils/SchemeDetailUtils'
  import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
  import CourseStatisticVo from '@api/service/management/resource/course/query/vo/CourseStatisticVo'
  import { Query, UiPage } from '@hbfe/common'
  import { debounce, bind } from 'lodash-decorators'
  class EditOutlineInfo {
    // 是否展示课程统计信息
    showCourseStatistics: boolean
    // 是否显示大纲树右侧内容
    contentLoading: boolean
    // 总计课程门数
    courseTotalCount: number
    // 总计学时数
    courseTotalPeriod: number
    // 当前节点
    currentNode: Classification
    // 课程分页
    coursePage: UiPage
    // 课程查询
    courseQuery: Query = new Query()
    // 课程列表
    courseList: TrainingOutlineCourse[]

    constructor() {
      this.showCourseStatistics = false
      this.contentLoading = false
      this.currentNode = new Classification()
      this.coursePage = new UiPage()
    }
  }

  @Component
  export default class extends Vue {
    /**
     * 类型：1-课程，2-兴趣课
     */
    @Prop({
      required: true,
      type: Number
    })
    type: number

    /**
     * 课程大纲信息
     */
    @Prop({
      required: true,
      type: Classification,
      default: () => {
        return new Classification()
      }
    })
    outlineInfo: Classification

    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassDetailClassVo }) schemeDetail: TrainClassDetailClassVo

    // 查询课程总控
    queryCourseM: QueryCourse = new QueryCourse()
    // 选中的课程大纲信息列表
    selectedOutlineInfoList: Array<EditOutlineInfo> = new Array<EditOutlineInfo>()
    // 课程统计信息列表
    courseStatisticList: CourseStatisticVo[] = []

    /**
     * 培训方案id
     */
    get schemeId() {
      return this.schemeDetail?.trainClassBaseInfo?.id || ''
    }

    /**
     * 获取所有的课程大纲叶子节点
     */
    get outlineTreeLeaves() {
      return this.outlineTreeFindAllLeaves(this.outlineInfo.childOutlines)
    }

    /**
     * 获取课程统计数据
     */
    get courseStatistic() {
      return (ele: Classification) => {
        const result = new CourseStatisticVo()
        const leaves = this.outlineTreeFindAllLeaves([ele])?.filter((item) => item.coursePackageId)
        const outlineIdList = leaves?.map((item) => item.id)
        if (outlineIdList.length) {
          const filterList = this.courseStatisticList.filter((item) => outlineIdList.includes(item.outlineId))
          result.courseTotal = filterList.reduce((prev, cur) => {
            return CalculatorObj.add(cur.courseTotal, prev)
          }, 0)
          result.coursePeriodTotal = filterList.reduce((prev, cur) => {
            return CalculatorObj.add(cur.coursePeriodTotal, prev)
          }, 0)
        }
        return result
      }
    }

    /**
     * 页面初始化
     */
    async created() {
      this.initLocalData()
      this.courseStatisticList = await this.queryCourseM.queryCourseStatisticByScheme(this.schemeId)
      await this.initOutlineTree()
    }

    @Watch('outlineInfo', {
      immediate: true,
      deep: true
    })
    outlineInfoChange(val: any) {
      console.log(`outlineInfo_${this.type}`, val)
    }

    /**
     * 初始化本地数据
     */
    initLocalData() {
      this.selectedOutlineInfoList = new Array<EditOutlineInfo>()
      this.outlineInfo.childOutlines.forEach((el: Classification) => {
        console.log('详情-大纲节点', el)
        const option = new EditOutlineInfo()
        option.contentLoading = false
        option.currentNode = new Classification()
        option.showCourseStatistics = false
        this.selectedOutlineInfoList.push(option)
      })
    }

    /**
     * 初始化大纲树
     */
    async initOutlineTree() {
      SchemeDetailUtils.recursionSetOutlineParentId(this.outlineInfo.childOutlines)
      // 开始加载
      this.selectedOutlineInfoList.forEach((el) => {
        el.contentLoading = true
      })
      await Promise.all(
        this.selectedOutlineInfoList?.map(async (el: EditOutlineInfo, index: number) => {
          el.currentNode = this.outlineInfo.childOutlines[index]
          if (SchemeDetailUtils.isLeaf(el.currentNode)) {
            el.courseList = await this.queryCourseList(el)
          }
        })
      )
      // 停止加载
      this.selectedOutlineInfoList?.forEach((el: EditOutlineInfo, index: number) => {
        el.contentLoading = false
      })
    }

    /**
     * 滚动查询课程列表
     */
    async visibleCourseList(isVisible: boolean, entry: any, index: number) {
      const target = this.selectedOutlineInfoList[index]
      const scopeIndex = entry.target.dataset.index
      if (isVisible) {
        if (parseInt(scopeIndex) >= target.coursePage.totalSize) {
          // 最大值时不请求
          return
        }
        if (parseInt(scopeIndex) == target.courseList.length) {
          target.courseQuery.loading = true
          target.coursePage.pageNo++
          console.log(`list_${index}.coursePage.pageNo`, target.coursePage.pageNo)
          const list = await this.queryCourseList(target)
          target.courseList = target.courseList.concat(list)
          target.courseQuery.loading = false
        }
      }
    }

    /**
     * 查询课程列表
     */
    async queryCourseList(target: EditOutlineInfo): Promise<TrainingOutlineCourse[]> {
      const result = [] as TrainingOutlineCourse[]
      const leaves = this.outlineTreeFindAllLeaves([target.currentNode])?.filter((item) => item.coursePackageId)
      const outlineIdList = leaves?.map((item) => item.id)
      if (outlineIdList.length) {
        const respList = await this.queryCourseM.queryCourseListInSchemeByOutline(
          target.coursePage,
          outlineIdList,
          this.schemeId
        )
        respList?.forEach((item) => {
          item.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
            return node.id === item.outlineId
          }, 'name')
          result.push(item)
        })
      }
      return result
    }

    /**
     * 课程大纲节点点击响应事件
     */
    @debounce(200)
    @bind
    async handleNodeClick(data: Classification, node: TreeNode<any, Classification>, obj: any, index: number) {
      // 判断前后节点是否重复，重复不变化
      const sourceId = this.selectedOutlineInfoList[index].currentNode.id
      const target = this.selectedOutlineInfoList[index]
      const targetId = data.id
      if (sourceId === targetId) return
      // 开始加载
      target.contentLoading = true
      target.currentNode = new Classification()
      target.currentNode = data
      target.coursePage = new UiPage()
      const isLeaf = SchemeDetailUtils.isLeaf(data)
      if (isLeaf) {
        // 叶子节点：重新请求列表
        target.courseList = await this.queryCourseList(this.selectedOutlineInfoList[index])
      } else {
        // 非叶子节点：清空课程列表
        target.courseList = [] as TrainingOutlineCourse[]
      }
      // 切换节点后，表格右侧滚动条自动滚到顶部
      this.$nextTick(() => {
        const ele = this.$refs[`courseTableRef_${index}`][0] as any
        const bodyWrapper = ele.bodyWrapper as any
        if (bodyWrapper) bodyWrapper.scrollTop = 0
      })
      // 停止加载
      target.contentLoading = false
    }

    /**
     * 获取课程分类信息
     */
    getCourseCategory(row: TrainingOutlineCourse) {
      return row.courseCategoryInfo?.join('>') || ''
    }

    /**
     * 查找所有叶子节点 - 课程大纲树通用
     */
    outlineTreeFindAllLeaves(tree: Array<Classification>) {
      return CreateSchemeUtils.treeFindAllLeaves<Classification>(tree, 'childOutlines')
    }

    /**
     * 查询符合条件的节点 - 课程大纲树通用
     */
    outlineTreeFind(tree: Array<Classification>, func: any) {
      return CreateSchemeUtils.treeFind<Classification>(tree, func, 'childOutlines')
    }

    /**
     * 查找节点路径 - 课程大纲树通用
     */
    outlineTreeFindPath(func: any, key: string) {
      return CreateSchemeUtils.treeFindPath<Classification>(this.outlineInfo.childOutlines, func, key, 'childOutlines')
    }
  }
</script>

<style scoped lang="scss">
  .pure {
    margin: 0;
    padding: 0;
  }
</style>
