import getLearningResultErrorStatisticsInServicer from './queries/getLearningResultErrorStatisticsInServicer.graphql'
import getLearningResultErrorStatisticsInTrainingChannel from './queries/getLearningResultErrorStatisticsInTrainingChannel.graphql'
import getLearningType from './queries/getLearningType.graphql'
import getStudentTrainingResultSimulateResponseInServicer from './queries/getStudentTrainingResultSimulateResponseInServicer.graphql'
import pageLearningResultErrorInServicer from './queries/pageLearningResultErrorInServicer.graphql'
import pageLearningResultErrorInTrainingChannel from './queries/pageLearningResultErrorInTrainingChannel.graphql'
import reGenerateStudentTrainingResultSimulateInServicer from './queries/reGenerateStudentTrainingResultSimulateInServicer.graphql'
import rePushStudentTrainingResultInServicer from './queries/rePushStudentTrainingResultInServicer.graphql'
import rePushStudentTrainingResultToGatewayInServicer from './queries/rePushStudentTrainingResultToGatewayInServicer.graphql'

export {
  getLearningResultErrorStatisticsInServicer,
  getLearningResultErrorStatisticsInTrainingChannel,
  getLearningType,
  getStudentTrainingResultSimulateResponseInServicer,
  pageLearningResultErrorInServicer,
  pageLearningResultErrorInTrainingChannel,
  reGenerateStudentTrainingResultSimulateInServicer,
  rePushStudentTrainingResultInServicer,
  rePushStudentTrainingResultToGatewayInServicer
}
