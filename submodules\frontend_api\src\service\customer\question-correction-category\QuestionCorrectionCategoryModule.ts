import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import QuestionCorrectionCategory from './models/QuestionCorrectionCategory'
import store from '@/store'
import QuestionCorrectionGateway from '@api/gateway/QuestionCorrection-default'
import { Role, RoleType, Secure } from '@api/Secure'
import { ResponseStatus } from '../../../Response'

interface IState {
  categories: Array<QuestionCorrectionCategory>
  categoriesNeedReload: boolean
}

@Module({ namespaced: true, store, dynamic: true, name: 'CustomerQuestionCorrectionCategoryModule' })
class QuestionCorrectionCategoryModule extends VuexModule implements IState {
  categories = new Array<QuestionCorrectionCategory>()
  categoriesNeedReload = true
  // 获取所有试题纠错分类
  @Role([RoleType.user])
  @Action
  async loadAllCategory() {
    if (!this.categoriesNeedReload) {
      return new ResponseStatus(200)
    }
    const response = await QuestionCorrectionGateway.listAllQuestionCorrectionCategory()
    if (response.status.isSuccess()) {
      this.setCategories(response.data)
    }

    return response.status
  }

  @Mutation
  private setCategories(payload: Array<any>) {
    this.categories = payload
    this.categoriesNeedReload = false
  }
}

export default getModule(QuestionCorrectionCategoryModule)
