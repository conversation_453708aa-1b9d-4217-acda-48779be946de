<route-meta>
{ "isMenu": true, "title": "课程管理", "sort": 2, "icon": "icon-xuexi" }
</route-meta>

<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="created">
    <div class="f-p15">
      <div class="f-mb15">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="goCreate()"
          v-if="$hasPermission('create')"
          desc="新建"
          actions="@hbfe/jxjy-admin-course/src/create.vue"
        >
          新建课程
        </el-button>
        <el-button
          type="primary"
          plain
          @click="goCategory"
          v-if="$hasPermission('category')"
          desc="课程分类管理"
          actions="@hbfe/jxjy-admin-course/src/category.vue"
        >
          课程分类管理
        </el-button>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <hb-search-wrapper :model="pageQueryParam" @reset="resetCondition">
          <el-form-item label="课程名称" prop="name">
            <el-input v-model="pageQueryParam.name" clearable placeholder="请输入课程名称" />
          </el-form-item>
          <!-- <el-form-item label="课件供应商" prop="provriderId" v-if="!isServerProvider">
            <biz-courseware-supplier v-model="provriderId" placeholder="全部"></biz-courseware-supplier>
          </el-form-item> -->
          <el-form-item label="转换状态" prop="exchangeStatus">
            <el-select v-model="pageQueryParam.status.current" clearable placeholder="全部">
              <el-option
                v-for="item in changeStatusValues"
                :label="item.desc"
                :value="item.code"
                :key="item.code"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="课程状态" prop="enable">
            <el-select v-model="pageQueryParam.enable.current" clearable placeholder="全部">
              <el-option
                v-for="item in statusValues"
                :label="item.desc"
                :value="item.code"
                :key="item.code"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="课程分类" prop="categoryIdList">
            <biz-course-category
              :filterable="true"
              v-model="categoryIdList"
              :showRootNode="false"
              placeholder="全部"
            ></biz-course-category>
          </el-form-item>
          <el-form-item label="创建时间" prop="createTime">
            <double-date-picker
              :begin-create-time.sync="pageQueryParam.createTime[0]"
              :end-create-time.sync="pageQueryParam.createTime[1]"
            ></double-date-picker>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
          </template>
        </hb-search-wrapper>
        <el-table
          stripe
          :data="pageList"
          max-height="500px"
          class="m-table"
          v-loading="query.loading"
          ref="courseTable"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="课程名称" min-width="280" fixed="left">
            <template slot-scope="{ row }">{{ row.name }}</template>
          </el-table-column>
          <!-- <el-table-column label="课件供应商" min-width="180" v-if="!isServerProvider">
            <template slot-scope="{ row }">{{ row.courseProviderName }}</template>
          </el-table-column> -->
          <el-table-column label="转换状态" min-width="120">
            <template slot-scope="{ row }">
              <hb-badge is-dot :text="row.transformStatus" :status="statusMapType[row.transformStatus.current]">
              </hb-badge>
            </template>
          </el-table-column>
          <el-table-column label="课程状态" min-width="100" align="center">
            <template slot-scope="{ row }">
              <el-tag :type="row.enable ? 'success' : 'info'">{{ row.enableLabel }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="学时" min-width="120" align="center" prop="period"></el-table-column>
          <el-table-column label="综合评价" min-width="180" align="center">
            <template slot-scope="{ row }">
              <el-rate disabled show-score text-color="#ff9900" score-template="{value}" v-model="row.evaluation" />
            </template>
          </el-table-column>
          <el-table-column label="试题数量" min-width="120" align="center" prop="questionCount"></el-table-column>
          <el-table-column label="创建时间" min-width="180" align="center" prop="createTime"></el-table-column>
          <el-table-column label="操作" width="240" align="center" fixed="right">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                @click="goDetail(row.id)"
                v-if="$hasPermission('classDetail')"
                desc="详情"
                actions="@hbfe/jxjy-admin-course/src/detail.vue"
              >
                详情
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click="previewCourse(row.id, row.evaluation)"
                v-if="$hasPermission('preview')"
                desc="预览"
                actions="previewCourse"
                >预览</el-button
              >
              <el-button
                type="text"
                size="mini"
                @click="goModify(row.id)"
                v-if="$hasPermission('modify')"
                desc="修改"
                actions="@hbfe/jxjy-admin-course/src/modify.vue"
              >
                修改
              </el-button>
              <template v-if="$hasPermission('enable')" desc="启停用" actions="changeStatus">
                <hb-popconfirm
                  :title="getTitle(row.enable)"
                  placement="top"
                  @confirm="openSecConfirm(row.id, row.enable)"
                >
                  <el-button type="text" slot="reference" size="mini">
                    {{ row.enable ? '停用' : '启用' }}
                  </el-button>
                </hb-popconfirm>
              </template>

              <template v-if="$hasPermission('remove')" desc="删除" actions="deleteCourse">
                <hb-popconfirm
                  placement="top"
                  title="删除后该课程需重新创建，是否确认删除？"
                  @confirm="deleteCourse(row.id)"
                >
                  <el-button type="text" slot="reference" size="mini">删除</el-button>
                </hb-popconfirm>
              </template>
            </template>
          </el-table-column>
        </el-table>
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </el-card>
      <el-dialog title="提示" :visible.sync="dialogVisible" width="450px" class="m-dialog">
        <div class="dialog-alert">
          <!--警告-->
          <i class="icon el-icon-warning warning"></i> <span class="txt">{{ tipsDialogText }}</span>
        </div>
        <div slot="footer">
          <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
        </div>
      </el-dialog>

      <el-dialog
        title="提示"
        :visible.sync="confirmDialog"
        width="450px"
        class="m-dialog"
        :before-close="closeSecConfirm"
        :append-to-body="true"
      >
        <div class="dialog-alert">
          <!--警告-->
          <i class="icon el-icon-warning warning"></i>
          <span class="txt">{{ secConfirmDialogText }}</span>
        </div>
        <div slot="footer">
          <el-button @click="closeSecConfirm">取 消</el-button>
          <el-button type="primary" @click="changeStatus">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Mixins, Watch } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import StandardRouteJumper from '@hbfe/jxjy-admin-common/src/StandardRouteJumper'
  import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'
  import CourseStatus from '@api/service/management/resource/course/enums/CourseStatus'
  import QueryCourseListParam from '@api/service/management/resource/course//query/vo/QueryCourseListParam'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import CourseTransformStatus from '@api/service/common/enums/course/CourseTransformStatus'
  import MutationCourse from '@api/service/management/resource/course/mutation/MutationCourse'
  import PreviewCourseMixins from '@hbfe/jxjy-admin-common/src/mixins/PreviewCourseMixins'

  import UserModule from '@api/service/management/user/UserModule'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'

  @Component({
    components: { DoubleDatePicker }
  })
  export default class extends Mixins(PreviewCourseMixins) {
    courseStatus = CourseStatus
    page: UiPage
    query: Query = new Query()
    pageQueryParam: QueryCourseListParam = new QueryCourseListParam()
    pageList: Array<CourseListDetail> = new Array<CourseListDetail>()
    $crudJumper: StandardRouteJumper
    dialogVisible = false
    statusValues = new CourseStatus().list()
    changeStatusValues = new CourseTransformStatus().list()
    statusMapType = {
      [CourseTransformStatus.enum.UNAVAILABLE]: 'error',
      [CourseTransformStatus.enum.NOT_AVAILABLE_YET]: 'processing',
      [CourseTransformStatus.enum.AVAILABLE]: 'success',
      [CourseTransformStatus.enum.EMPTY]: 'error'
    }
    categoryIdList: Array<string> = new Array<string>()

    serviceId = ''
    isServerProvider = false
    queryServiceProvider = AuthorityModule.serviceProviderFactory.queryServiceProvider

    secConfirmDialogText = ''
    tipsDialogText = ''
    mutationId = ''
    mutationOperation = false
    confirmDialog = false

    // provriderId: Array<string> = new Array<string>()
    getQueryCurrentUserRoleList = AuthorityModule.roleFactory.getQueryCurrentUserRoleList()
    queryCourse = ResourceModule.courseFactory.queryCourse
    beforeDestroy() {
      console.log('FatherbeforeDestroy')
    }
    destroyed() {
      console.log('Fatherdestroyed')
    }
    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    async created() {
      await this.getQueryCurrentUserRoleList.getCurrentUserRoleList()
      const roleList = this.getQueryCurrentUserRoleList.roleList
      if (roleList.length && roleList.length > 0) {
        const flag = roleList.find((item) => item.category === 560)
        this.isServerProvider = !!flag
      }

      if (this.isServerProvider) {
        const res = await this.queryServiceProvider.serchProviderByUserId(
          UserModule.queryUserFactory.queryManagerDetail.adminInfo.userInfo.userId
        )
        this.serviceId = res.coursewareId
      }
      console.log(this.$route.meta.group, 'his.$route.meta.grou')
      this.$crudJumper = new StandardRouteJumper(this.$route.meta.group, this.$router)
      //   if (this.$route.query.providerId) {
      //     this.provriderId.push(this.$route.query.providerId as string)
      //     this.pageQueryParam.providerId = this.$route.query.providerId as string
      //   }
      this.doSearch()
    }
    @Watch('categoryIdList')
    changeCategoryId() {
      if (this.categoryIdList?.length) {
        this.pageQueryParam.categoryIdList = [this.categoryIdList[this.categoryIdList.length - 1]]
      } else {
        this.pageQueryParam.categoryIdList = []
      }
    }
    async activated() {
      this.doSearch()
    }
    async doSearch() {
      this.query.loading = true
      try {
        if (this.categoryIdList?.length) {
          this.pageQueryParam.categoryIdList = [this.categoryIdList[this.categoryIdList.length - 1]]
        } else {
          this.pageQueryParam.categoryIdList = []
        }

        // if (this.isServerProvider) {
        //   this.pageQueryParam.providerId = this.serviceId
        // } else if (this.provriderId?.length) {
        //   this.pageQueryParam.providerId = this.provriderId[this.provriderId.length - 1]
        // } else {
        //   this.pageQueryParam.providerId = '-2'
        // }
        this.pageList = await this.queryCourse.queryCoursePage(this.page, this.pageQueryParam)
      } catch (e) {
        console.log(e)
        // nothing
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['courseTable'] as any)?.doLayout()
        this.query.loading = false
      }
    }

    resetCondition() {
      this.page.pageNo = 1
      this.pageQueryParam = new QueryCourseListParam()
      this.categoryIdList = new Array<string>()
      //this.provriderId = new Array<string>()
      this.doSearch()
    }

    getTitle(enabled: boolean) {
      return enabled ? '确定停用该课程吗？' : '确定启用该课程吗？'
    }

    openSecConfirm(id: string, enabled: boolean) {
      this.secConfirmDialogText = enabled
        ? '停用后该课程将不能引用，是否确认停用？'
        : '启用后该课程可被使用，是否确认启用？'
      this.mutationId = id
      this.mutationOperation = enabled
      this.confirmDialog = true
    }

    closeSecConfirm() {
      this.confirmDialog = false

      this.secConfirmDialogText = ''
      this.mutationId = ''
      this.mutationOperation = false
    }

    // 启用/停用课程
    async changeStatus() {
      const mutationCourse = new MutationCourse(this.mutationId)
      let res
      if (this.mutationOperation) {
        res = await mutationCourse.doDisable()
      } else {
        res = await mutationCourse.doEnable()
      }
      if (res.code === 200) {
        this.$message.success('操作成功')
      }
      this.pageList.filter((course) => this.mutationId === course.id)[0].enable = !this.mutationOperation
      this.closeSecConfirm()
    }

    async deleteCourse(id: string) {
      const mutationCourse = new MutationCourse(id)
      const res = await mutationCourse.doRemove()
      if (res.isSuccess()) {
        this.$message.success('操作成功')
        // 加延时器，解决列表数据延迟问题
        setTimeout(async () => {
          await this.doSearch()
        }, 1000)
      } else {
        this.tipsDialogText = res?.message as string
        this.dialogVisible = true

        // this.$message.error(res.message as string)
      }
    }

    goCategory() {
      this.$router.push(this.$crudJumper.getUrl('category'))
    }

    goUnassignedCourse() {
      this.$router.push('/resource/unassigned-course')
    }
    goCreate() {
      this.$router.push('/resource/course/create')
    }
    goDetail(id: string) {
      this.$router.push('/resource/course/detail/' + id)
    }
    goModify(id: string) {
      this.$router.push('/resource/course/modify/' + id)
    }
  }
</script>
