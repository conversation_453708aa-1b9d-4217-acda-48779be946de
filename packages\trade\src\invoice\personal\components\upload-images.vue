<template>
  <div>
    <el-upload
      action="#"
      list-type="picture-card"
      class="m-pic-upload proportion-pic is-small"
      :class="{ disabled: imgList.length >= limit }"
      :disabled="imgList.length >= limit"
      :on-change="uploadImgChange"
      :auto-upload="false"
      :file-list="imgList"
      :limit="limit"
      :accept="imgType"
      :multiple="isMultiple"
    >
      <div slot="default" class="upload-placeholder">
        <i class="el-icon-plus"></i>

        <p class="f-mt10" v-if="contentText">{{ contentText }}</p>
      </div>
      <div slot="file" slot-scope="{ index, file }" class="el-upload-list__item is-ready">
        <el-image
          class="el-upload-list__item-thumbnail"
          :src="getFileWithToken(file.url)"
          alt=""
          fit="fill"
          v-loading="loading[file.uid]"
        ></el-image>

        <div class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span class="el-upload-list__item-delete" @click="handleRemove(file)">
            <i class="el-icon-delete"></i>
          </span>
        </div>
      </div>
      <!--      <div slot="tip" class="el-upload__tip">
        <i class="el-icon-warning"></i>
        <span class="txt">{{ imgTypeTip }}</span>
      </div>-->
    </el-upload>
    <el-dialog :visible.sync="picDialogVisible" class="m-dialog-pic" :append-to-body="true" :lock-scroll="false">
      <img :src="dialogImageUrl" width="100%" alt="" />
    </el-dialog>
    <!-- 大图预览 -->
    <el-image style="width: 100px; height: 100px" :previewSrcList="previewList" v-show="false" ref="elImage">
    </el-image>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Ref } from 'vue-property-decorator'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import axios from 'axios'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { ElImage } from 'element-ui/types/image'
  import FilePreview from '@api/service/common/file/FilePreview'
  import { CertificationUrl } from '@hbfe/jxjy-admin-components/src/upload-images.vue'

  @Component
  export default class extends Vue {
    loading = {}
    // 资源地址
    resourceUrl = ''
    // 图片预览对话框
    picDialogVisible = false
    // 预览地址
    dialogImageUrl = ''

    @Prop({
      type: Boolean,
      required: true,
      default: () => {
        return false
      }
    })
    isProtected: boolean

    // 大图预览ref
    @Ref('elImage')
    elImage: ElImage

    /**
     * 上传中间文字
     */
    @Prop({
      type: String,
      default: ''
    })
    contentText: string
    // 图片后缀
    @Prop({
      type: String,
      default: '.jpg, .png, .jpeg'
    })
    imgType: string
    // 数量限制
    @Prop({
      type: Number,
      default: 10
    })
    limit: string

    // 图片后缀的文字提示
    @Prop({
      type: String,
      default: '请上传*.jpg，*.jpeg，*.png格式的图片'
    })
    imgTypeTip: string

    // 默认支持多选
    @Prop({
      type: Boolean,
      default: true
    })
    isMultiple: boolean

    // 传给后端的图片路径数组/ 预览数组
    imgList = new Array<{ name: string; url: string }>()
    // 图片预览数组
    previewList = new Array<string>()
    // 用于接收回显的图片数组
    @Prop([Array, Object])
    value: Array<any>
    @Watch('value', {
      deep: true,
      immediate: true
    })
    async valueChange(val: Array<CertificationUrl>) {
      this.imgList = val
      await FilePreview.applyResourceAccessToken()
      this.previewList =
        val?.map((file) => {
          return FilePreview.getFileUrlWithToken(file.url)
        }) || ([] as string[])
    }
    @Watch('imgList', {
      deep: true
    })
    imgListChange() {
      this.$emit('input', this.imgList)
    }

    // 获取图片地址（带token）
    getFileWithToken(url: string) {
      const target = this.previewList?.find((el: string) => {
        if (typeof el === 'string') {
          const fixUrl = url.split('?')[0]
          return el.indexOf(fixUrl) > -1
        }
        return false
      })
      return target || ''
    }

    created() {
      this.resourceUrl = ConfigCenterModule.getIngress('ingress.resource')
    }

    // 上传图片
    async uploadImgChange(file: any, fileList: any) {
      this.$set(this.loading, file.uid, true)
      const imgReg = /\.jpg|\.png|\.jpeg|\.JPG|\.PNG|\.JPEG$/
      if (imgReg.test(file?.name)) {
        // 校验通过之后，转为base图片
        const reader = new FileReader()
        reader.readAsDataURL(file.raw || file)
        reader.onloadend = async () => {
          // 图片的 base64 格式, 可以直接当成 img 的 src 属性值
          const dataURL = reader.result as string
          await this.getResourceUrl(dataURL, fileList, file?.name, file?.uid)
        }
      } else {
        this.$message.warning('请上传指定后缀的图片！')
        return
      }
    }

    // 保存符合格式的图片
    async getResourceUrl(imgCode: string, fileList: any, fileName: string, uid: number) {
      const data = {
        base64Data: imgCode.split(',')[1].toString(),
        fileName
      }
      // 转为图片
      //   const baseUrl = `${this.resourceUrl}/auth/uploadBase64ToProtectedFile`
      let baseUrl
      if (this.isProtected) {
        baseUrl = `${ConfigCenterModule.getIngress(ingress.apiendpoint)}/web/ms-file-v1/web/uploadProtectedBase64`
      } else {
        baseUrl = `${ConfigCenterModule.getIngress(ingress.apiendpoint)}/web/ms-file-v1/web/uploadPublicBase64`
      }
      try {
        const res = await axios.post(baseUrl, data)
        const imgUrl = res.data.data
        fileList[fileList.length - 1].name = imgUrl
        fileList[fileList.length - 1].url = '/mfs' + imgUrl
        this.imgList = fileList
        this.previewList.push('/mfs' + imgUrl)

        // console.log('后端用', this.imgList)
        this.$emit('input', this.imgList)

        this.$set(this.loading, uid, false)
      } catch (error) {
        this.$message('图片转换失败')
      }
    }

    // 预览图片
    async handlePictureCardPreview(file: any) {
      const imgList = this.previewList
      const index = imgList.findIndex((item) => item.includes(file.url))
      const afterPicArr = imgList.slice(index)
      const beforePicArr = imgList.slice(0, index)
      await FilePreview.applyResourceAccessToken()
      // 将图片路径替换最新的token
      this.previewList = afterPicArr.concat(beforePicArr)?.map((url) => {
        return FilePreview.getFileUrlWithToken(url)
      })
      // this.setPhotoTop(this.previewList, file)
      this.$nextTick(() => {
        // 触发点击方法
        ;(this.elImage as any).clickHandler()
      })
    }

    /* 删除图片*/
    handleRemove(file: any) {
      const idx: number = this.imgList.findIndex((el) => el.url === file.url)
      this.imgList.splice(idx, 1)
      // console.log('删除后的：', this.imgList)
      const preIdx: number = this.previewList.findIndex((el) => el === file.url)
      this.previewList.splice(preIdx, 1)
    }
  }
</script>
<style lang="scss" scoped>
  ::v-deep div.el-dialog__body {
    text-align: center;
  }
</style>

<style rel="stylesheet/scss" lang="scss">
  .disabled .el-upload--picture-card {
    display: none;
  }
</style>
