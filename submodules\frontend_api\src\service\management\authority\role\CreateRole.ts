import MsBasicdataDomainGatewayV1, {
  CreateRoleByAdminTypeRequest,
  CreateRoleRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { ResponseStatus } from '@hbfe/common'
import RoleBaseInfo from '@api/service/management/authority/role/RoleBaseInfo'

class CreateRole extends RoleBaseInfo {
  /**
   * 创建范围
   * 1. 开启全部权限，包括操作
   * 2. 开启全部查看权限
   */
  createArrange: string = undefined

  async doCreate() {
    this.onSaving = true
    try {
      const result = await MsBasicdataDomainGatewayV1.createRoleNew(this)
      return result.status
    } catch (e) {
      return new ResponseStatus(500, '系统异常')
    } finally {
      this.onSaving = false
    }
  }

  /**
   * 创建角色
   */
  async createRoleByAdminType() {
    const request = RoleBaseInfo.toCreateRoleByAdminTypeRequest(this)
    return MsBasicdataDomainGatewayV1.createRoleByAdminType(request)
  }
}

export default CreateRole
