import MyOrderVo from '@api/service/management/trade/single/order/query/vo/MyOrderVo'
import ReturnOrderResponseVo from '@api/service/management/trade/single/order/query/vo/ReturnOrderResponseVo'
import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import {
  ReturnOrderSortField,
  ReturnSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import tradeQueryGateway, { SubOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
import userModule from '@api/service/management/user/UserModule'
import OrderInvoiceApplyInfoResponseVo from '@api/service/management/trade/single/order/query/vo/OrderInvoiceApplyInfoResponseVo'
import MsLearningQueryFrontGatewayCourseLearningForestage, {
  LearningRegisterRequest,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import TrainClassModule from '@api/service/management/train-class/TrainClassManagerModule'
import { ForceReasonEnum } from '@api/service/management/trade/single/order/enum/ForceReasonEnum'
import TradeModule from '@api/service/management/trade/TradeModule'
import { Page } from '@hbfe/common'
import InvoiceListResponse from '@api/service/management/trade/single/invoice/query/vo/InvoiceListResponse'
import {
  InvoiceCategoryEnum,
  InvoiceIdentificationEnum,
  InvoiceStatusEnum,
  InvoiceMethodEnum,
  InvoiceTypeEnum
} from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
import CommodityRefundStatus, {
  CommodityRefundStatusEnum
} from '@api/service/common/return-order/enums/CommodityRefundStatus'
import OrderRefundType, {
  OnlyReturnList,
  OrderRefundTypeEnum
} from '@api/service/common/return-order/enums/OrderRefundType'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import { cloneDeep } from 'lodash'

export default class OrderDetailVo extends MyOrderVo {
  //  退货单详情
  returnOrderDetail: ReturnOrderResponseVo = null
  returnOrderDetailList: ReturnOrderResponseVo[] = null
  //购买人信息
  buyerDetail = new UserDetailVo()
  //创建人信息
  creatorDetail = new UserDetailVo()

  //是否属于强制退款的类型
  isForce = false
  foreseReasonArr: ForceReasonEnum[] = []

  get email() {
    /**
     * 查订单的口 个人报名订单用email 集体报名订单用contactEmail
     * 查发票的口 线上票用email 线下票用contactEmail
     * 如果有发票 发票优先于订单
     */
    if (this.invoiceApplyInfo?.invoiceIdList?.length) {
      if (this.invoiceApplyInfo.invoiceMethod === 2) {
        return this.invoiceApplyInfo.contactEmail
      } else {
        return this.invoiceApplyInfo.email
      }
    } else {
      if (this.basicData.orderType === 2) {
        return this.invoiceApplyInfo.contactEmail
      } else {
        return this.invoiceApplyInfo.email
      }
    }
  }

  /**
   * 子订单退货状态（目前只有单一商品，直接上浮即可）
   */
  get subOrderRefundStatus() {
    return (subOrderItem: SubOrderResponse) => {
      return CommodityRefundStatus.transferSubOrderStatusToCurrent(
        subOrderItem.returnStatus,
        subOrderItem.refundSchedule,
        subOrderItem.returnSchedule
      )
    }
  }

  /**
   * 获取子单可退款类型
   */
  get subOrderCanRefundType() {
    // channelType 1:用户自主购买 2:集体缴费 3：管理员导入 4：集体报名个人缴费渠道
    return (subOrderItem: SubOrderResponse, channelType?: number) => {
      const subOrderRefundStatus = this.subOrderRefundStatus(subOrderItem)
      let typeList = new Array<OrderRefundTypeEnum>()

      typeList = OrderRefundType.switchSubOrderCanRefundType(
        subOrderRefundStatus,
        subOrderItem.leftAmount,
        subOrderItem.leftQuantity
      )

      // 导入开通订单只能选择仅退货
      if (channelType == 3 || channelType == 2) {
        typeList = typeList.filter((it) => {
          return OnlyReturnList.includes(it)
        })
      }

      if (typeList?.length) {
        return OrderRefundType.transformToUiOptions(typeList)
      } else {
        return []
      }
    }
  }

  /**
   * 子单已退款金额
   */
  get subRefundAmount() {
    return (subOrder: SubOrderResponse) => {
      let totalOrderAmount = 0
      let totalRemainingAmount = 0
      if (subOrder.amount) {
        totalOrderAmount = subOrder.amount
      }
      if (subOrder.leftAmount) {
        totalRemainingAmount = subOrder.leftAmount
      }

      return CalculatorObj.subtract(totalOrderAmount, totalRemainingAmount)
    }
  }

  /**
   * 已退款总金额（所有子单）
   */
  get refundAmount() {
    let totalOrderAmount = 0
    let totalRemainingAmount = 0
    this.subOrderItems.map((item) => {
      if (item.amount) {
        totalOrderAmount += item.amount
      }
      if (item.leftAmount) {
        totalRemainingAmount += item.leftAmount
      }
    })
    return CalculatorObj.subtract(totalOrderAmount, totalRemainingAmount)
  }

  get invoiceIdentification(): InvoiceIdentificationEnum {
    // 发票类型应由三个字段去定位 （invoiceCategory,invoiceType,invoiceMethod）
    // 但目前只有这几种发票类型，为不影响旧的发票类型判断进行判断简化
    // 若后续新增发票类型则判断需重新梳理
    switch (this.invoiceApplyInfo.invoiceCategory) {
      case InvoiceCategoryEnum.PLAININVOICE:
        return InvoiceIdentificationEnum.PLAIN_INVOICE
      case InvoiceCategoryEnum.VATPLAININVOICE:
        return InvoiceIdentificationEnum.VAT_PLAIN_INVOICE
      case InvoiceCategoryEnum.VATSPECIALPLAININVOICE:
        if (this.invoiceApplyInfo.invoiceType === InvoiceMethodEnum.ELECT) {
          return InvoiceIdentificationEnum.VAT_SPECIAL_ELECT_PLAIN_INVOICE
        } else {
          return InvoiceIdentificationEnum.VAT_SPECIAL_PLAIN_INVOICE
        }
      default:
        return null
    }
  }
  //添加购买人信息
  async addBuyer() {
    try {
      if (this.buyer && this.creator && this.buyer.userId && this.buyer.userId === this.creator.userId) {
        const userDetail = await this.getUserDetail(this.buyer.userId)
        this.buyerDetail = userDetail
        this.creatorDetail = cloneDeep(userDetail)
        return
      } else {
        const userDetailReq: Promise<void>[] = []
        if (this.buyer && this.buyer.userId) {
          userDetailReq.push(
            this.getUserDetail(this.buyer.userId).then((res) => {
              this.buyerDetail = res
            })
          )
          //this.buyerDetail = await this.getUserDetail(this.buyer.userId)
        }
        if (this.creator && this.creator.userId) {
          userDetailReq.push(
            this.getUserDetail(this.creator.userId).then((res) => {
              this.creatorDetail = res
            })
          )
          //this.creatorDetail = await this.getUserDetail(this.creator.userId)
        }
        await Promise.all(userDetailReq)
      }
    } catch (e) {
      console.log('添加用户信息出错', e)
    }
  }
  async addInvoice() {
    //如果关联发票有值，那么取关联发票
    if (this.invoiceApplyInfo && this.invoiceApplyInfo.invoiceIdList && this.invoiceApplyInfo.invoiceIdList.length) {
      try {
        let invoiceList
        if (this.invoiceApplyInfo.invoiceMethod == 1) {
          invoiceList = await TradeModule.singleTradeBatchFactor.invoiceFactor.queryInvoice.onLineGetInvoiceInServicer(
            this.invoiceApplyInfo.invoiceIdList
          )
        } else {
          invoiceList =
            await TradeModule.singleTradeBatchFactor.invoiceFactor.queryOffLineInvoice.offLineGetInvoiceInServicer(
              this.invoiceApplyInfo.invoiceIdList,
              new Page(1, this.invoiceApplyInfo.invoiceIdList.length)
            )
        }
        let invoiceNums: string[] = []
        if (this.invoiceApplyInfo.invoiceMethod == 2) {
          invoiceNums = (invoiceList as OffLinePageInvoiceVo[]).map((item) => item.invoiceNo)
        } else {
          invoiceNums = (invoiceList as InvoiceListResponse[]).map((item) => item.blueInvoiceNo)
        }

        ;(invoiceList as any[]).map((res: any) => {
          const temp = new OrderInvoiceApplyInfoResponseVo()
          temp.account = res?.account
          // temp.invoiceType = res?.invoiceType
          temp.invoiceCategory = res?.invoiceCategory
          temp.title = res?.title
          temp.titleType = res?.titleType
          temp.taxpayerNo = res?.taxpayerNo
          temp.address = res?.address
          temp.phone = res?.invoiceFaceInfo.phone

          temp.bankName = res?.bankName
          temp.contactEmail = res?.contactEmail
          temp.email = res?.email
          temp.contactEmail = res?.contactEmail
          temp.remark = res?.remark
          temp.appliedTime = res?.applyForDate
          temp.orderNum = invoiceNums.join(',')
          temp.invoiceDate = res?.invoiceDate
          temp.invoiceStatus = res?.invoiceStatus
          if (res?.invoiceFreezeStatus) {
            temp.invoiceStatus = 4
          }
          if (res?.useless) {
            temp.invoiceStatus = 3
          }

          if (temp.invoiceMethod == 2) {
            temp.deliveryInfo = (res as OffLinePageInvoiceVo)?.deliveryInfo
          }
          temp.blueFilePath = (res as InvoiceListResponse)?.blueFilePath
          temp.blueFileXmlPath = (res as InvoiceListResponse)?.blueFileXmlPath
          temp.blueFileOfdPath = (res as InvoiceListResponse)?.blueFileOfdPath
          console.log('有关联发票')
          this.invoiceApplyInfoList.push(temp)
        })

        const firstInvoice = invoiceList[0]

        this.invoiceApplyInfo.account = firstInvoice.account
        // this.invoiceApplyInfo.invoiceType = firstInvoice.invoiceType
        this.invoiceApplyInfo.invoiceCategory = firstInvoice.invoiceCategory
        this.invoiceApplyInfo.title = firstInvoice.title
        this.invoiceApplyInfo.titleType = firstInvoice.titleType
        this.invoiceApplyInfo.taxpayerNo = firstInvoice.taxpayerNo
        this.invoiceApplyInfo.address = firstInvoice.address
        this.invoiceApplyInfo.phone = firstInvoice.invoiceFaceInfo.phone

        this.invoiceApplyInfo.bankName = firstInvoice.bankName
        this.invoiceApplyInfo.contactPhone = firstInvoice.contactPhone
        /**
         * 查订单的口 个人报名订单用email 集体报名订单用contactEmail
         * 查发票的口 线上票用email 线下票用contactEmail
         */
        this.invoiceApplyInfo.contactEmail = firstInvoice?.contactEmail
        this.invoiceApplyInfo.email = firstInvoice?.email
        this.invoiceApplyInfo.contactEmail = firstInvoice.contactEmail
        this.invoiceApplyInfo.remark = firstInvoice.remark
        this.invoiceApplyInfo.appliedTime = firstInvoice.applyForDate
        ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).orderNum = invoiceNums.join(',')
        ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceDate = firstInvoice.invoiceDate
        ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus = firstInvoice.invoiceStatus
        if (firstInvoice?.useless) {
          ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus = 3
        } else if (firstInvoice.invoiceFreezeStatus) {
          ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus = 4
        }
        if (this.invoiceApplyInfo.invoiceMethod == 2) {
          ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).deliveryInfo = (
            firstInvoice as OffLinePageInvoiceVo
          ).deliveryInfo
        }
        ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).blueFilePath = (
          firstInvoice as InvoiceListResponse
        ).blueFilePath
        console.log('有关联发票')
      } catch (e) {
        console.log(e)
      }
    } else {
      if (this.invoiceApplyInfo) {
        ;(this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus = InvoiceStatusEnum.NOTPTOOPEN
      }
    }
    return this
  }
  //获取是否为强制退款
  async getForceStatue() {
    if (this.invoiceApplyInfo && (this.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus == 2) {
      this.isForce = true
      this.foreseReasonArr.push(ForceReasonEnum.ForceReasonEnumInvoiced)
    }
    const studentLearning = await this.getStudentLearning()
    if (studentLearning) {
      if (
        studentLearning.studentLearning.courseLearning?.courseScheduleStatus === 2 ||
        studentLearning.studentLearning.trainingResult == 1
      ) {
        this.isForce = true
        if (studentLearning.studentLearning.trainingResult == 1) {
          this.foreseReasonArr.push(ForceReasonEnum.ForceReasonEnumAssesed)
        }
        if (studentLearning.studentLearning.courseLearning?.courseScheduleStatus === 2) {
          this.foreseReasonArr.push(ForceReasonEnum.ForceReasonEnumCourseQuafield)
        }
      }
    }
  }
  /*
   *    获取参训资格学员学习信息
   * */
  async getStudentLearning() {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    const firstOrder = this.subOrderItems[0]
    request.learningRegister.sourceType = firstOrder.currentCommoditySourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
    request.learningRegister.sourceId = firstOrder.currentCommoditySourceId

    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageStudentSchemeLearningInServicer({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      return res.data.currentPageData[0]
    }
    return
  }
  private async getUserDetail(userId: string) {
    const queryUser = userModule.queryUserFactory.queryStudentDetail(userId)
    const data = await queryUser.queryDetail()
    if (data.status.isSuccess()) {
      return data.data
    }
    return new UserDetailVo()
  }
  async addRefundOrder() {
    try {
      const orderList = this.subOrderItems
      // if (orderDetail.returnStatus != 0) {
      const request = new ReturnOrderRequestVo()
      request.subOrderInfo.subOrderNoList = orderList.map((item) => {
        return item.subOrderNo
      })
      const sort = new ReturnSortRequest()
      sort.field = ReturnOrderSortField.APPLIED_TIME
      sort.policy = SortPolicy.DESC
      if (request.subOrderInfo?.subOrderNoList?.length && request.subOrderInfo.subOrderNoList?.length > 1) {
        const refundOrderRes = await tradeQueryGateway.pageReturnOrderInServicer({
          page: {
            pageNo: 1,
            pageSize: 200
          },
          request: request,
          sort: [sort]
        })
        if (
          refundOrderRes.status.isSuccess() &&
          refundOrderRes.data.currentPageData &&
          refundOrderRes.data.currentPageData.length
        ) {
          const returnOrderDetailList = refundOrderRes.data.currentPageData
          // const returnOrderVo = new ReturnOrderResponseVo()
          this.returnOrderDetailList = []
          await Promise.all(
            returnOrderDetailList.map(async (item) => {
              const returnOrderVo = new ReturnOrderResponseVo()
              Object.assign(returnOrderVo, item)
              returnOrderVo.changeStatus()
              await returnOrderVo.fillRecords()
              this.returnOrderDetailList.push(returnOrderVo)
              // return returnOrderVo
            })
          )
          // Object.assign(returnOrderVo, returnOrderDetail)
          // returnOrderVo.changeStatus()
          // await returnOrderVo.fillRecords()
          // this.returnOrderDetail = returnOrderVo
        } else {
          this.returnOrderDetail = null
          this.returnOrderDetailList = []
        }
      } else {
        const refundOrderRes = await tradeQueryGateway.pageReturnOrderInServicer({
          page: {
            pageNo: 1,
            pageSize: 1
          },
          request: request,
          sort: [sort]
        })
        if (
          refundOrderRes.status.isSuccess() &&
          refundOrderRes.data.currentPageData &&
          refundOrderRes.data.currentPageData.length
        ) {
          const returnOrderDetail = refundOrderRes.data.currentPageData[0]
          const returnOrderVo = new ReturnOrderResponseVo()
          Object.assign(returnOrderVo, returnOrderDetail)
          returnOrderVo.changeStatus()
          await returnOrderVo.fillRecords()
          this.returnOrderDetail = returnOrderVo
        } else {
          this.returnOrderDetail = null
        }
      }
      // }
    } catch (e) {
      console.log('退款单详情失败', e)
      this.returnOrderDetail = null
    }
  }

  async addFxRefundOrder() {
    try {
      const orderList = this.subOrderItems
      // if (orderDetail.returnStatus != 0) {
      const request = new ReturnOrderRequestVo()
      request.subOrderInfo.subOrderNoList = orderList.map((item) => {
        return item.subOrderNo
      })
      const sort = new ReturnSortRequest()
      sort.field = ReturnOrderSortField.APPLIED_TIME
      sort.policy = SortPolicy.DESC
      if (request.subOrderInfo?.subOrderNoList?.length && request.subOrderInfo.subOrderNoList?.length > 1) {
        const refundOrderRes = await tradeQueryGateway.pageReturnOrderInDistributor({
          page: {
            pageNo: 1,
            pageSize: request.subOrderInfo.subOrderNoList?.length
          },
          request: request,
          sort: [sort]
        })
        if (
          refundOrderRes.status.isSuccess() &&
          refundOrderRes.data.currentPageData &&
          refundOrderRes.data.currentPageData.length
        ) {
          const returnOrderDetailList = refundOrderRes.data.currentPageData
          // const returnOrderVo = new ReturnOrderResponseVo()
          this.returnOrderDetailList = []
          await Promise.all(
            returnOrderDetailList.map(async (item) => {
              const returnOrderVo = new ReturnOrderResponseVo()
              Object.assign(returnOrderVo, item)
              returnOrderVo.changeStatus()
              await returnOrderVo.fillRecords()
              this.returnOrderDetailList.push(returnOrderVo)
              // return returnOrderVo
            })
          )
          // Object.assign(returnOrderVo, returnOrderDetail)
          // returnOrderVo.changeStatus()
          // await returnOrderVo.fillRecords()
          // this.returnOrderDetail = returnOrderVo
        } else {
          this.returnOrderDetail = null
          this.returnOrderDetailList = []
        }
      } else {
        const refundOrderRes = await tradeQueryGateway.pageReturnOrderInDistributor({
          page: {
            pageNo: 1,
            pageSize: 1
          },
          request: request,
          sort: [sort]
        })
        if (
          refundOrderRes.status.isSuccess() &&
          refundOrderRes.data.currentPageData &&
          refundOrderRes.data.currentPageData.length
        ) {
          const returnOrderDetail = refundOrderRes.data.currentPageData[0]
          const returnOrderVo = new ReturnOrderResponseVo()
          Object.assign(returnOrderVo, returnOrderDetail)
          returnOrderVo.changeStatus()
          await returnOrderVo.fillRecords()
          this.returnOrderDetail = returnOrderVo
        } else {
          this.returnOrderDetail = null
        }
      }
      // }
    } catch (e) {
      console.log('退款单详情失败', e)
      this.returnOrderDetail = null
    }
  }
}
