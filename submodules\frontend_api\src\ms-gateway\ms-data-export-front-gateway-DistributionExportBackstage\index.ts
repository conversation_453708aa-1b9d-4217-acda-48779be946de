import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-data-export-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-data-export-front-gateway-DistributionExportBackstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum QueryWayType {
  ONLY_ME = 'ONLY_ME',
  ONLY_MY_DISTRIBUTOR = 'ONLY_MY_DISTRIBUTOR',
  ALL = 'ALL'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 异步任务组名返回对象
 */
export class JobGroupRequest {
  /**
   * 任务组key
   */
  group?: string
  /**
   * 任务组名（模糊查询）
   */
  groupName?: string
}

/**
 * 功能描述：任务查询参数
@Author： wtl
@Date： 2022/1/18 15:13
 */
export class JobRequest {
  /**
   * 任务组名（必填）
   */
  group?: string
  /**
   * 任务组名匹配方式（EQ：完全匹配 LIKE：模糊匹配[*group*] LLIKE：左模糊匹配[group*] RLIKE：右模糊匹配[*group]，不传值默认为完全匹配）
   */
  groupOperator?: string
  /**
   * 任务名（模糊查询）
   */
  jobName?: string
  /**
   * 任务状态(executing:运行中 executed:运行完成 fail:运行失败)
@see UserJobState
   */
  jobState?: string
  /**
   * 任务执行时间 yyyy-MM-dd HH:mm:ss
   */
  executeTimeScope?: DateScopeRequest
  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult?: boolean
}

/**
 * 供应商授权出的商品
 */
export class DistributorCommodityAndRelationRequest {
  /**
   * 分销商商品id集合
   */
  distributorCommodityIdList?: Array<string>
  /**
   * 商品id集合
   */
  commodityIdList?: Array<string>
  /**
   * 网校id
   */
  onlineSchoolId?: string
  /**
   * 分销商商品名称
   */
  saleTitle?: string
  /**
   * 培训方案名称
   */
  schemeName?: string
  /**
   * 培训方案类型
   */
  schemeTypeList?: Array<string>
  /**
   * 商品sku属性
   */
  propertyList?: Array<PropertyRequest>
  /**
   * 分销商id集合
   */
  distributorIdList?: Array<string>
  /**
   * 分销商等级
   */
  distributorLevel?: number
  /**
   * 分销状态
0-开启 1-关闭
商品的分销开始时间、结束时间作为判断
   */
  distributionStatus?: number
  /**
   * 分销地区路径
   */
  contractDistributionRegionPathList?: Array<string>
  /**
   * 产品分销授权是否展示
0-不展示 1-展示
   */
  relationIsShow?: boolean
  /**
   * 商品是否门户可见
0-不展示 1-展示
   */
  isShow?: boolean
  /**
   * 网校销售状态
0-开启 1-关闭
商品的网校销售开始时间、结束时间作为判断
   */
  onlineSchoolStatus?: number
  /**
   * 来源类型
   */
  commoditySourceTypeList?: Array<number>
  /**
   * 定价方案状态
   */
  statusList?: Array<number>
  /**
   * 定价方案id
   */
  productPricingSchemeIdList?: Array<string>
  /**
   * 授权价格类型 1-固定 2-区间
   */
  priceType?: number
  /**
   * 分销价格范围查询-最大价格
   */
  maxPrice?: number
  /**
   * 分销价格范围查询-最小价格
   */
  minPrice?: number
  /**
   * 是否已启用定价方案
分销商品(授权)存在一个及以上定价方案视为启用，但价格类型为固定价格时也未启用
   */
  enablePricingScheme?: boolean
  jobName?: string
}

/**
 * 网校商品查询条件
 */
export class OnlineSchoolCommodityRequest {
  /**
   * 网校商品id集合
   */
  commodityIdList?: Array<string>
  /**
   * 网校id
   */
  onlineSchoolId?: string
  /**
   * 网校商品名称
   */
  saleTitle?: string
  /**
   * 培训方案名称
   */
  schemeName?: string
  /**
   * 培训方案类型
chooseCourseLearning - 选课规则  autonomousCourseLearning - 自主学习
   */
  schemeTypeList?: Array<string>
  /**
   * 网校商品上下架状态
0-已下架、1-已上架
   */
  shelveStatusList?: Array<number>
  /**
   * 销售状态
0-关闭 1-开启
方案的报名开始时间、报名结束时间来判断
   */
  saleStatus?: number
  /**
   * 商品sku属性
   */
  propertyList?: Array<PropertyRequest>
  /**
   * 商品创建时间排序方式
0-升序 1-降序
   */
  commodityCreatedTimeSort?: number
}

/**
 * 交易统计请求参数
<AUTHOR>
 */
export class StatisticTradeRecordRequest {
  /**
   * 分销商id集合
   */
  distributorIdList?: Array<string>
  /**
   * 供应商id集合
   */
  supplierIdList?: Array<string>
  /**
   * 商品名称 模糊匹配
   */
  commodityName?: string
  /**
   * 网校id集合 List
   */
  onlineSchoolList?: Array<string>
  /**
   * 商品ID集合
   */
  commodityIdList?: Array<string>
  /**
   * 商品售价范围
   */
  commodityPriceScope?: DoubleScopeRequest
  /**
   * 查询时间范围
   */
  queryDateScope?: DateScopeRequest
  /**
   * 查询方式
仅查询自己
只查询下级分销商
包含自己和下级分销商
@see QueryWayType
   */
  queryWayType?: QueryWayType
  jobName?: string
}

/**
 * 商品sku属性查询条件
 */
export class PropertyRequest {
  /**
   * 商品skuKey
   */
  propertyKey?: string
  /**
   * 商品skuValue
   */
  propertyValue?: string
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

/**
 * 异步任务组名返回对象
 */
export class JobGroupResponse {
  /**
   * 异步任务组key
   */
  group: string
  /**
   * 异步任务组名
   */
  groupName: string
  /**
   * 排序大小
   */
  order: number
  /**
   * 所在域
   */
  domain: Array<string>
}

/**
 * 功能描述：异步任务日志返回对象
@Author： wtl
@Date： 2022/4/11 17:18
 */
export class UserJobLogResponse {
  /**
   * 任务id
   */
  jobId: string
  /**
   * 任务组名
   */
  group: string
  /**
   * 任务名
   */
  jobName: string
  /**
   * 任务开始时间
   */
  beginTime: string
  /**
   * 任务结束时间
   */
  endTime: string
  /**
   * 任务状态(executing:运行中 executed:运行完成 fail:运行失败)
@see UserJobState
   */
  jobState: string
  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult: boolean
  /**
   * 任务执行成功或失败的信息
   */
  message: string
  /**
   * 导出文件路径
   */
  exportFilePath: string
  /**
   * 是否受保护
   */
  isProtected: boolean
  /**
   * 资源id
   */
  fileResourceId: string
  /**
   * 操作人id
   */
  operatorUserId: string
  /**
   * 操作人帐户id
   */
  operatorAccountId: string
}

export class UserJobLogResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserJobLogResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出培训商品开通统计（分销商）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommodityDistributorOpenReportInDistributor(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommodityDistributorOpenReportInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出培训商品开通统计明细报表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommodityOpenStatisticsDetailExcelInSupplier(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommodityOpenStatisticsDetailExcelInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出培训商品开通统计汇总报表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommodityOpenStatisticsSummaryExcelInSupplier(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommodityOpenStatisticsSummaryExcelInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销产品数据导出
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportDistributionCommodityExcelInSupplier(
    request: OnlineSchoolCommodityRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportDistributionCommodityExcelInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商品管理数据导出
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportDistributionCommodityManageExcelInSupplier(
    request: DistributorCommodityAndRelationRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportDistributionCommodityManageExcelInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出分销商销售统计报表
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportDistributorSalesStatisticsExcelInSupplier(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportDistributorSalesStatisticsExcelInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出分销销售统计-我的分销（分销商）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportMyTradeRecordInDistributor(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportMyTradeRecordInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出分销销售统计-下级分销商（分销商）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportSubDistributorSellStatisticInDistributor(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportSubDistributorSellStatisticInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询导出任务组(系统管理域)
   * @param jobGroupRequest : 任务查询条件
   * @return : void
   * @Author： sjm
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param jobGroupRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExportTaskGroupInfoInAdmin(
    jobGroupRequest: JobGroupRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listExportTaskGroupInfoInAdmin,
    operation?: string
  ): Promise<Response<Array<JobGroupResponse>>> {
    return commonRequestApi<Array<JobGroupResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { jobGroupRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询导出任务组(分销商)
   * @param jobGroupRequest : 任务查询条件
   * @return : void
   * @Author： sjm
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param jobGroupRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExportTaskGroupInfoInDistributor(
    jobGroupRequest: JobGroupRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listExportTaskGroupInfoInDistributor,
    operation?: string
  ): Promise<Response<Array<JobGroupResponse>>> {
    return commonRequestApi<Array<JobGroupResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { jobGroupRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询导出任务组
   * @param jobGroupRequest : 任务查询条件
   * @return : void
   * @Author： sjm
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param jobGroupRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExportTaskGroupInfoInServicer(
    jobGroupRequest: JobGroupRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listExportTaskGroupInfoInServicer,
    operation?: string
  ): Promise<Response<Array<JobGroupResponse>>> {
    return commonRequestApi<Array<JobGroupResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { jobGroupRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询导出任务组(供应商)
   * @param jobGroupRequest : 任务查询条件
   * @return : void
   * @Author： sjm
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param jobGroupRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExportTaskGroupInfoInSupplier(
    jobGroupRequest: JobGroupRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listExportTaskGroupInfoInSupplier,
    operation?: string
  ): Promise<Response<Array<JobGroupResponse>>> {
    return commonRequestApi<Array<JobGroupResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { jobGroupRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询我的导出任务日志信息列表
   * @param jobRequest : 任务查询条件
   * @return : void
   * @Author： wtl
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageExportTaskInfoInMyself(
    params: { page?: Page; jobRequest?: JobRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageExportTaskInfoInMyself,
    operation?: string
  ): Promise<Response<UserJobLogResponsePage>> {
    return commonRequestApi<UserJobLogResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
