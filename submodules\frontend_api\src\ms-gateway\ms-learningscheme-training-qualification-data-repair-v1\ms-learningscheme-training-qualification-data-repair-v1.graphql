"""独立部署的微服务,K8S服务名:ms-learningscheme-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
type Query {
	getSchemaName:String
}
type Mutation {
	"""重新推送课程
		@param payload:
		<AUTHOR> By Cb
		@since 2024/7/8 17:49
	"""
	retryPushCourse(payload:String):Void
}

scalar List
