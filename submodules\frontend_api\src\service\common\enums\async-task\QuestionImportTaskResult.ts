import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 试题导入任务处理结果
 */
export enum QuestionImportTaskResultEnum {
  // 1：未处理
  NOT_EXECUTE = 0,
  // 2：成功
  SUCCESS = 1,
  // 3：失败
  FAIL = 2,
  // 4：就绪失败
  PREPARE_FAIL = 3
}

class QuestionImportTaskResult extends AbstractEnum<QuestionImportTaskResultEnum> {
  static enum = QuestionImportTaskResultEnum
  constructor(status?: QuestionImportTaskResultEnum) {
    super()
    this.current = status
    this.map.set(QuestionImportTaskResultEnum.NOT_EXECUTE, '未处理')
    this.map.set(QuestionImportTaskResultEnum.SUCCESS, '成功')
    this.map.set(QuestionImportTaskResultEnum.FAIL, '失败')
    this.map.set(QuestionImportTaskResultEnum.PREPARE_FAIL, '就绪失败')
  }
}

export default new QuestionImportTaskResult()
