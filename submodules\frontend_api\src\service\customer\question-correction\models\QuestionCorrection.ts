import Attachment from './Attachment'
import Sign from './Sign'

class QuestionCorrection {
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 创建人id
   */
  creatorId: string
  /**
   * 创建时间
   */
  createTime: Date
  /**
   * 试题id
   */
  questionId: string
  /**
   * 纠错分类id
   */
  categoryId: string
  /**
   * 描述
   */
  describe: string
  /**
   * 附件
   */
  attachments: Array<Attachment>
  /**
   * 试题纠错状态  待处理：1，已处理：2
   */
  status: number
  /**
   * 标记记录集合
   */
  signList: Array<Sign>
}

export default QuestionCorrection
