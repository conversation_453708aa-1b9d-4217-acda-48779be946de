<template>
  <el-drawer
    title="选择网校开展培训的行业属性"
    :visible="drawerVisible"
    :direction="direction"
    size="600px"
    custom-class="m-drawer"
    @close="handleCancel"
  >
    <div class="drawer-bd">
      <el-tabs
        v-if="haveRSIndustry || haveJSIndustry || haveWSIndustry || haveGQIndustry || haveLSIndustry || haveYSIndustry"
        v-model="activeIndustryName"
        type="card"
        class="m-tab-card  is-badge"
      >
        <el-tab-pane v-if="haveRSIndustry" label="人社行业" name="first">
          <div slot="label">
            人社行业<el-badge v-if="isRsNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('人社') && RSProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="人社"
            @getProperty="getProperty"
            ref="rs"
            :PropertiesId="RSProperties"
            :isCancel="styleList.length"
            @getIndustryInfo="getIndustryRSInfo"
          ></industry-properties>
        </el-tab-pane>
        <el-tab-pane v-if="haveJSIndustry" label="建设行业" name="second">
          <div slot="label">
            建设行业<el-badge v-if="isJsNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('建设') && JSProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="建设"
            @getProperty="getProperty"
            ref="js"
            :PropertiesId="JSProperties"
            :isCancel="styleList.length"
            @getIndustryInfo="getIndustryJSInfo"
          ></industry-properties>
        </el-tab-pane>
        <el-tab-pane v-if="haveWSIndustry" label="职业卫生行业" name="third">
          <div slot="label">
            职业卫生行业<el-badge v-if="isWsNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('职业卫生') && WSProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="职业卫生"
            @getProperty="getProperty"
            ref="ws"
            :PropertiesId="WSProperties"
            :isCancel="styleList.length"
            @getIndustryInfo="getIndustryWSInfo"
          ></industry-properties>
        </el-tab-pane>
        <el-tab-pane v-if="haveGQIndustry" label="工勤行业" name="fourth">
          <div slot="label">
            工勤行业<el-badge v-if="isGQNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('工勤') && GQProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="工勤"
            @getProperty="getProperty"
            ref="gq"
            :PropertiesId="GQProperties"
            :isCancel="styleList.length"
            @getIndustryInfo="getIndustryGQInfo"
          ></industry-properties>
        </el-tab-pane>
        <el-tab-pane v-if="haveLSIndustry" label="教师行业" name="fifth">
          <div slot="label">
            教师行业<el-badge v-if="isLsNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('教师') && LSProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="教师"
            @getProperty="getProperty"
            ref="ls"
            :PropertiesId="LSProperties"
            :isCancel="styleList.length"
            @getIndustryInfo="getIndustryLSInfo"
          ></industry-properties>
        </el-tab-pane>
        <el-tab-pane v-if="haveYSIndustry" label="药师行业" name="sixth">
          <div slot="label">
            药师行业<el-badge v-if="isYsNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('药师') && YSProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="药师"
            @getProperty="getProperty"
            ref="ys"
            :PropertiesId="YSProperties"
            :isCancel="styleList.length"
            @getIndustryInfo="getIndustryYSInfo"
          ></industry-properties>
        </el-tab-pane>
      </el-tabs>
      <div v-else>
        请先选择培训行业
      </div>
    </div>
    <div class="m-btn-bar drawer-ft">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handleCommit" type="primary">确认</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryOperationIndustryV2'
  import QueryIndustryV1 from '@api/service/common/basic-data-dictionary/query/QueryOperationIndustry'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import { Industry } from '@api/ms-gateway/ms-servicercontract-v1'
  import industryProperties from '@hbfe/jxjy-admin-registerSchool/src/components/industry-properties.vue'
  import IndustryEnum, { IndustryIdEnum } from '@api/service/training-institution/online-school/enum/IndustryEnum'

  import IndustryPropertyId, {
    IndustryPropertyIdEnum
  } from '@api/service/training-institution/online-school/enum/IndustryPropertyIdEnum'
  import QuerySubject from '@api/service/common/basic-data-dictionary/query/QuerySubject'
  class IndustryData {
    subjectType: Array<any> = []
    majorType: Array<any> = []
    objectType: Array<any> = []
    postType: Array<any> = []
    technicalType: Array<any> = []
    jobType: Array<any> = []
    section: Array<any> = []
    subjects: Array<any> = []
    practiceType: Array<any> = []
    certType: Array<any> = []
    title: string
    industryPropertyId: string
    isQuery = false
    isLoading = false
  }
  class IndustryPropertyList {
    RSindustryData: Array<IndustryData> = []
    JSindustryData: Array<IndustryData> = []
    WSindustryData: Array<IndustryData> = []
    GQindustryData: Array<IndustryData> = []
    LSindustryData: Array<IndustryData> = []
    YSindustryData: Array<IndustryData> = []
  }
  @Component({
    components: {
      industryProperties
    }
  })
  export default class extends Vue {
    @Prop({
      required: false,
      default: () => {
        return new Array<string>()
      }
    })
    firstIndustryList: Array<string>
    /**
     * 抽屉显示
     */
    @Prop({
      required: true,
      default: () => false
    })
    drawerVisible: boolean
    /**
     * 有无对应行业
     */
    @Prop({
      required: true,
      default: () => false
    })
    haveRSIndustry: boolean
    @Prop({
      required: true,
      default: () => false
    })
    haveJSIndustry: boolean
    @Prop({
      required: true,
      default: () => false
    })
    haveWSIndustry: boolean
    @Prop({
      required: true,
      default: () => false
    })
    haveGQIndustry: boolean
    @Prop({
      required: true,
      default: () => false
    })
    haveLSIndustry: boolean
    @Prop({
      required: true,
      default: () => false
    })
    haveYSIndustry: boolean
    /**
     * 行业培训属性值
     */
    @Prop({
      required: false,
      default: null
    })
    RSProperties: string
    @Prop({
      required: false,
      default: null
    })
    JSProperties: string
    @Prop({
      required: false,
      default: null
    })
    WSProperties: string
    @Prop({
      required: false,
      default: null
    })
    GQProperties: string
    @Prop({
      required: false,
      default: null
    })
    LSProperties: string
    @Prop({
      required: false,
      default: null
    })
    YSProperties: string
    /**
     * 当前选中行业
     */
    @Prop({
      required: false,
      default: () => '人社'
    })
    activeIndex: string
    @Watch('activeIndex', { immediate: true, deep: true })
    getIndex() {
      // 取消行业后 清除原先选择的培训属性
      this.selectedIndustyList = []
      if (this.activeIndex.indexOf('人社') != -1) {
        this.activeIndustryName = 'first'
      } else if (this.activeIndex.indexOf('建设') != -1) {
        this.activeIndustryName = 'second'
      } else if (this.activeIndex.indexOf('职业卫生') != -1) {
        this.activeIndustryName = 'third'
      } else if (this.activeIndex.indexOf('工勤') != -1) {
        this.activeIndustryName = 'fourth'
      } else if (this.activeIndex.indexOf('教师') != -1) {
        this.activeIndustryName = 'fifth'
      } else if (this.activeIndex.indexOf('药师') != -1) {
        this.activeIndustryName = 'sixth'
      }
    }
    activeIndustryName = 'first'
    /**
     * 行业培训属性详情
     */
    JSIndustry: Industry = { id: '', properties: '' }
    RSIndustry: Industry = { id: '', properties: '' }
    WSIndustry: Industry = { id: '', properties: '' }
    GQIndustry: Industry = { id: '', properties: '' }
    LSIndustry: Industry = { id: '', properties: '' }
    YSIndustry: Industry = { id: '', properties: '' }
    /**
     * 行业列表
     */
    industryList: Array<IndustryVo> = []
    /**
     * 已选培训属性行业
     */
    selectedIndustyList: Array<IndustryPropertyIdEnum> = []
    direction = 'rtl'
    styleList = new Array<string>()
    /**
     * 行业培训属性数据集合
     */
    industryPropertyObject: IndustryPropertyList = new IndustryPropertyList()
    /**
     * 行业是否没被选中
     */
    get isRsNotChoosed() {
      return !this.RSProperties && !this.styleList.includes('人社')
    }
    get isJsNotChoosed() {
      return !this.JSProperties && !this.styleList.includes('建设')
    }
    get isWsNotChoosed() {
      return !this.WSProperties && !this.styleList.includes('职业卫生')
    }
    get isGQNotChoosed() {
      return !this.GQProperties && !this.styleList.includes('工勤')
    }
    get isLsNotChoosed() {
      return !this.LSProperties && !this.styleList.includes('教师')
    }
    get isYsNotChoosed() {
      return !this.YSProperties && !this.styleList.includes('药师')
    }
    async created() {
      this.getIndustryData()
    }
    /**
     * 获取行业信息
     */
    async getIndustryData() {
      this.industryList = await QueryIndustry.getOperationIndustry()
      const industryIdList = this.industryList.map(item => {
        return item.id
      })
      const IndustryPropertyIdList = await QueryIndustry.getIndustryPropertyId('-1', industryIdList, 0)
      console.log(IndustryPropertyIdList, 'IndustryPropertyIdList')
      IndustryPropertyIdList.map(async (item: any) => {
        const RSindustryData = new IndustryData()
        const JSindustryData = new IndustryData()
        const WSindustryData = new IndustryData()
        const GQindustryData = new IndustryData()
        const LSindustryData = new IndustryData()
        const YSindustryData = new IndustryData()
        switch (item.industryPropertyId) {
          case IndustryPropertyIdEnum.RS:
          case IndustryPropertyIdEnum.HN:
          case IndustryPropertyIdEnum.AH:
          case IndustryPropertyIdEnum.GS:
          case IndustryPropertyIdEnum.JSS:
          case IndustryPropertyIdEnum.SCRS:
          case IndustryPropertyIdEnum.JX:
            // 人社
            this.industryPropertyObject.RSindustryData.unshift({
              ...RSindustryData,
              title: new IndustryPropertyId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          case IndustryPropertyIdEnum.JS:
          case IndustryPropertyIdEnum.SC:
            // 建设
            this.industryPropertyObject.JSindustryData.unshift({
              ...JSindustryData,
              title: new IndustryPropertyId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          case IndustryPropertyIdEnum.WS:
            // 职业卫生
            this.industryPropertyObject.WSindustryData.unshift({
              ...WSindustryData,
              title: new IndustryPropertyId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          case IndustryPropertyIdEnum.GQ:
            // 工勤
            this.industryPropertyObject.GQindustryData.unshift({
              ...GQindustryData,
              title: new IndustryPropertyId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          case IndustryPropertyIdEnum.LS:
            // 教师
            this.industryPropertyObject.LSindustryData.unshift({
              ...LSindustryData,
              title: new IndustryPropertyId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          case IndustryPropertyIdEnum.YS:
            // 药师
            this.industryPropertyObject.YSindustryData.unshift({
              ...YSindustryData,
              title: new IndustryPropertyId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          default:
            break
        }
      })
      console.log(this.industryPropertyObject, 'this.industryPropertyObject')
    }

    /**
     * 获取人社行业信息
     */
    async getIndustryRSInfo(industryPropertyId: IndustryPropertyIdEnum) {
      const RSindustryData = new IndustryData()
      const rsItem = this.industryPropertyObject.RSindustryData.find(
        item => item.industryPropertyId === industryPropertyId
      )
      RSindustryData.isQuery = true
      rsItem.isLoading = true
      RSindustryData.subjectType = await QueryIndustry.getOperationSubjectList(IndustryIdEnum.RS, industryPropertyId)
      RSindustryData.majorType = await QueryIndustry.getOperationTraining(IndustryIdEnum.RS, industryPropertyId)
      await Promise.all(
        RSindustryData.majorType?.map(async item => {
          item.children = await QueryIndustryV1.getIndustryDetail(item.propertyId)
        })
      )
      Object.assign(rsItem, RSindustryData)
    }

    /**
     * 获取建设行业信息
     */
    async getIndustryJSInfo(industryPropertyId: IndustryPropertyIdEnum) {
      const JSindustryData = new IndustryData()
      const jsItem = this.industryPropertyObject.JSindustryData.find(
        item => item.industryPropertyId === industryPropertyId
      )
      JSindustryData.isQuery = true
      jsItem.isLoading = true
      JSindustryData.subjectType = await QueryIndustry.getOperationSubjectList(IndustryIdEnum.JS, industryPropertyId)
      JSindustryData.majorType = await QueryIndustry.getOperationTraining(IndustryIdEnum.JS, industryPropertyId)
      await Promise.all(
        JSindustryData.majorType?.map(async item => {
          item.children = await QueryIndustryV1.getIndustryDetail(item.propertyId)
        })
      )
      Object.assign(jsItem, JSindustryData)
    }

    /**
     * 获取工勤行业信息
     */
    async getIndustryGQInfo(industryPropertyId: IndustryPropertyIdEnum) {
      const GQindustryData = new IndustryData()
      const gqItem = this.industryPropertyObject.GQindustryData.find(
        item => item.industryPropertyId === industryPropertyId
      )
      GQindustryData.isQuery = true
      gqItem.isLoading = true
      GQindustryData.technicalType = await QueryIndustry.getJobLevel(IndustryIdEnum.GQ, industryPropertyId)
      GQindustryData.jobType = await QueryIndustry.getJobCategory(IndustryIdEnum.GQ, industryPropertyId)
      Object.assign(gqItem, GQindustryData)
    }

    /**
     * 获取卫生行业信息
     */
    async getIndustryWSInfo(industryPropertyId: IndustryPropertyIdEnum) {
      const WSindustryData = new IndustryData()
      const wsItem = this.industryPropertyObject.WSindustryData.find(
        item => item.industryPropertyId === industryPropertyId
      )
      WSindustryData.isQuery = true
      wsItem.isLoading = true
      WSindustryData.majorType = await QueryIndustry.getOperationTraining(IndustryIdEnum.WS, industryPropertyId)
      WSindustryData.objectType = await QueryIndustry.getTrainingObject(IndustryIdEnum.WS, industryPropertyId)
      WSindustryData.postType = await QueryIndustry.getPositionCategory(IndustryIdEnum.WS, industryPropertyId)
      await Promise.all(
        WSindustryData.objectType?.map(async item => {
          item.children = await QueryIndustry.getPositionCategory(item.propertyId, industryPropertyId)
        })
      )
      Object.assign(wsItem, WSindustryData)
    }

    /**
     * 获取教师行业信息
     */
    async getIndustryLSInfo(industryPropertyId: IndustryPropertyIdEnum) {
      const LSindustryData = new IndustryData()
      const lsItem = this.industryPropertyObject.LSindustryData.find(
        item => item.industryPropertyId === industryPropertyId
      )
      LSindustryData.isQuery = true
      lsItem.isLoading = true
      LSindustryData.section = await QueryIndustry.getSection(IndustryIdEnum.LS, industryPropertyId)
      LSindustryData.subjects = await QuerySubject.queryAllSubject()
      await Promise.all(
        LSindustryData.section?.map(async item => {
          item.children = await QueryIndustryV1.getSubjects(item.propertyId)
        })
      )
      Object.assign(lsItem, LSindustryData)
    }
    /**
     * 获取药师行业信息
     */
    async getIndustryYSInfo(industryPropertyId: IndustryPropertyIdEnum) {
      const YSindustryData = new IndustryData()
      const ysItem = this.industryPropertyObject.YSindustryData.find(
        item => item.industryPropertyId === industryPropertyId
      )
      YSindustryData.isQuery = true
      ysItem.isLoading = true
      YSindustryData.subjectType = await QueryIndustry.getOperationSubjectList(IndustryIdEnum.YS, industryPropertyId)
      YSindustryData.certType = await QueryIndustry.getCertificatesType(IndustryIdEnum.YS, industryPropertyId)
      await Promise.all(
        YSindustryData.certType?.map(async item => {
          item.children = await QueryIndustryV1.getPractitionerCategory(item.propertyId)
        })
      )
      Object.assign(ysItem, YSindustryData)
    }
    /**
     * 获取属性值
     */
    getProperty(propertyId: IndustryPropertyIdEnum, title: string) {
      switch (title) {
        case '人社':
          if (
            !this.selectedIndustyList.includes(IndustryPropertyIdEnum.RS) ||
            !this.selectedIndustyList.includes(IndustryPropertyIdEnum.HN) ||
            !this.selectedIndustyList.includes(IndustryPropertyIdEnum.AH) ||
            !this.selectedIndustyList.includes(IndustryPropertyIdEnum.JSS) ||
            !this.selectedIndustyList.includes(IndustryPropertyIdEnum.GS) ||
            !this.selectedIndustyList.includes(IndustryPropertyIdEnum.SCRS) ||
            !this.selectedIndustyList.includes(IndustryPropertyIdEnum.JX)
          ) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        case '建设':
          if (
            !this.selectedIndustyList.includes(IndustryPropertyIdEnum.JS) ||
            !this.selectedIndustyList.includes(IndustryPropertyIdEnum.SC)
          ) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        case '职业卫生':
          if (!this.selectedIndustyList.includes(IndustryPropertyIdEnum.WS)) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        case '工勤':
          if (!this.selectedIndustyList.includes(IndustryPropertyIdEnum.GQ)) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        case '教师':
          if (!this.selectedIndustyList.includes(IndustryPropertyIdEnum.LS)) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        case '药师':
          if (!this.selectedIndustyList.includes(IndustryPropertyIdEnum.YS)) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        default:
          break
      }
      if (!this.styleList.includes(title)) {
        this.styleList.push(title)
      }
    }
    /**
     * 取消（清空内容）
     */
    handleCancel() {
      this.$emit('cancel')
      this.styleList = []
    }
    /**
     * 确定（提交）
     */
    handleCommit() {
      if (
        (this.haveRSIndustry && this.isRsNotChoosed) ||
        (this.haveJSIndustry && this.isJsNotChoosed) ||
        (this.haveWSIndustry && this.isWsNotChoosed) ||
        (this.haveGQIndustry && this.isGQNotChoosed) ||
        (this.haveLSIndustry && this.isLsNotChoosed) ||
        (this.haveYSIndustry && this.isYsNotChoosed)
      ) {
        this.$alert(
          '请选择' +
            (
              (this.haveRSIndustry && this.isRsNotChoosed ? '人社、' : '') +
              (this.haveJSIndustry && this.isJsNotChoosed ? '建设、' : '') +
              (this.haveWSIndustry && this.isWsNotChoosed ? '卫生、' : '') +
              (this.haveGQIndustry && this.isGQNotChoosed ? '工勤、' : '') +
              (this.haveLSIndustry && this.isLsNotChoosed ? '教师、' : '') +
              (this.haveYSIndustry && this.isYsNotChoosed ? '药师、' : '')
            ).slice(0, -1) +
            '行业下的培训属性值',
          '提示',
          {
            type: 'warning'
          }
        )
        return
      }
      if (this.$refs.rs) {
        ;(this.$refs.rs as any).selectProperty()
      }
      if (this.$refs.js) {
        ;(this.$refs.js as any).selectProperty()
      }
      if (this.$refs.ws) {
        ;(this.$refs.ws as any).selectProperty()
      }
      if (this.$refs.gq) {
        ;(this.$refs.gq as any).selectProperty()
      }
      if (this.$refs.ls) {
        ;(this.$refs.ls as any).selectProperty()
      }
      if (this.$refs.ys) {
        ;(this.$refs.ys as any).selectProperty()
      }
      this.JSIndustry = {}
      this.RSIndustry = {}
      this.WSIndustry = {}
      this.GQIndustry = {}
      this.LSIndustry = {}
      this.YSIndustry = {}
      if (this.haveJSIndustry) {
        this.JSIndustry.id = this.industryList[0].id
      }
      if (this.haveRSIndustry) {
        this.RSIndustry.id = this.industryList[1].id
      }
      if (this.haveWSIndustry) {
        this.WSIndustry.id = this.industryList[2].id
      }
      if (this.haveGQIndustry) {
        this.GQIndustry.id = this.industryList[3].id
      }
      if (this.haveLSIndustry) {
        this.LSIndustry.id = this.industryList[4].id
      }
      if (this.haveYSIndustry) {
        this.YSIndustry.id = this.industryList[5].id
      }
      this.selectedIndustyList.map(item => {
        switch (item) {
          case IndustryPropertyIdEnum.RS:
            this.RSIndustry.properties = IndustryPropertyIdEnum.RS
            break
          case IndustryPropertyIdEnum.HN:
            this.RSIndustry.properties = IndustryPropertyIdEnum.HN
            break
          case IndustryPropertyIdEnum.AH:
            this.RSIndustry.properties = IndustryPropertyIdEnum.AH
            break
          case IndustryPropertyIdEnum.JSS:
            this.RSIndustry.properties = IndustryPropertyIdEnum.JSS
            break
          case IndustryPropertyIdEnum.SCRS:
            this.RSIndustry.properties = IndustryPropertyIdEnum.SCRS
            break
          case IndustryPropertyIdEnum.JX:
            this.RSIndustry.properties = IndustryPropertyIdEnum.JX
            break
          case IndustryPropertyIdEnum.GS:
            this.RSIndustry.properties = IndustryPropertyIdEnum.GS
            break
          case IndustryPropertyIdEnum.SC:
            this.JSIndustry.properties = IndustryPropertyIdEnum.SC
            break
          case IndustryPropertyIdEnum.JS:
            this.JSIndustry.properties = IndustryPropertyIdEnum.JS
            break
          case IndustryPropertyIdEnum.WS:
            this.WSIndustry.properties = IndustryPropertyIdEnum.WS
            break
          case IndustryPropertyIdEnum.GQ:
            this.GQIndustry.properties = IndustryPropertyIdEnum.GQ
            break
          case IndustryPropertyIdEnum.LS:
            this.LSIndustry.properties = IndustryPropertyIdEnum.LS
            break
          case IndustryPropertyIdEnum.YS:
            this.YSIndustry.properties = IndustryPropertyIdEnum.YS
            break
          default:
            break
        }
      })
      this.$emit(
        'submit',
        this.RSIndustry,
        this.JSIndustry,
        this.WSIndustry,
        this.GQIndustry,
        this.LSIndustry,
        this.YSIndustry
      )
    }
  }
</script>

<style lang="scss" scoped>
  //抽屉
  .m-drawer {
    .el-drawer__body {
      overflow-y: scroll;
      display: flex;
      flex-direction: column;
    }

    .el-drawer__header {
      font-size: 18px;
      color: #333;
      margin-bottom: 20px;
    }

    .drawer-bd {
      padding: 0 20px 20px;
      flex: 1;
    }

    .drawer-ft {
      position: sticky;
      bottom: 0;
      z-index: 9;
      padding: 15px 0;
      background-color: rgba(#f8f8f8, 0.9);
      border-top: 1px solid #eee;
      text-align: center;
    }

    .m-btn-bar {
      &.is-sticky {
        padding-top: 10px;
      }
    }
    //行业属性弹窗-单选控件列表
    .m-attribute-select {
      width: 100%;

      .el-radio {
        border: 1px solid #e4e7ed;
        border-radius: 3px;
        padding: 15px;
        margin-top: 20px;
        margin-right: 0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;

        .el-radio__label {
          flex: 1;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }

        .tit {
          width: 400px;
          display: inline-block;
          font-size: 15px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &.is-checked {
          border: 1px solid #1f86f0;
        }
      }
    }
  }
</style>
