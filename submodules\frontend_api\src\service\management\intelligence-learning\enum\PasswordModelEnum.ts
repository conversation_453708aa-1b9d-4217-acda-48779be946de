import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum PasswordModelEnum {
  /**
   * 六个0
   */
  password_model_zero = '000000',
  /**
   * abc123
   */
  password_model_abc = 'abc123',
  /**
   * 证件号后六位
   */
  password_model_id_card = 'idCard',
  /**
   * 自定义密码
   */
  password_model_custom = 'custom'
}

export default class PasswordModelType extends AbstractEnum<PasswordModelEnum> {
  static enum = PasswordModelEnum
  constructor(status?: PasswordModelEnum) {
    super()
    this.current = status
    this.map.set(PasswordModelEnum.password_model_zero, '000000')
    this.map.set(PasswordModelEnum.password_model_abc, 'abc123')
    this.map.set(PasswordModelEnum.password_model_id_card, '证件号后六位')
    this.map.set(PasswordModelEnum.password_model_custom, '自定义密码')
  }
}
