<template>
  <div>
    <div class="el-select ty-item ty-item-1" style="user-select: none">
      <div class="el-select__tags" style="width: 100%; max-width: 208px" v-if="selectNodes.length">
        <span>
          <el-tag size="small" type="info" closable @close="remove(firstNode)">{{ firstNode.name }}</el-tag>
          <el-tag size="small" type="info" v-if="selectNodes.length > 1">+ {{ selectNodes.length - 1 }}</el-tag>
        </span>
      </div>
      <div class="el-input el-input--suffix is-focus">
        <input
          type="text"
          @focus="openDialog = true"
          readonly="readonly"
          autocomplete="off"
          :placeholder="selectNodes.length ? '' : '请选择单位'"
          class="el-input__inner"
        />
      </div>
    </div>
    <el-dialog title="请选择单位" :visible.sync="openDialog" class="tree_container_my">
      <div style="width: 70%; margin: 0 auto">
        <biz-unit-tree field="deptId" ref="unitTree" searchable show-user-count v-model="selectItems"></biz-unit-tree>
        <el-backtop class="inner-scroller" target=".tree_container_my .el-dialog__body"></el-backtop>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="certain">确定选择</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<style lang="scss">
  .inner-scroller {
    position: absolute;
  }
</style>

<script lang="ts">
  // import { Component, Emit, Ref, Vue } from 'vue-property-decorator'
  // import BizUnitTree from '@hbfe/jxjy-admin-components/src//business/biz-unit-tree.vue'

  // @Component({
  //   components: {
  //     BizUnitTree
  //   }
  // })
  // export default class extends Vue {
  //   selectItems = new Array<any>()
  //   openDialog = false
  //   @Ref('unitTree')
  //   unitTree: any

  //   selectNodes: any[] = []

  //   get firstNode() {
  //     return this.selectNodes[0] || {}
  //   }

  //   @Emit('input')
  //   certain() {
  //     this.openDialog = false
  //     this.selectNodes = this.unitTree?.selectNodes?.slice()
  //     return this.selectItems
  //   }

  //   @Emit('input')
  //   remove() {
  //     this.selectNodes = []
  //     this.selectItems = []
  //     this.unitTree.clearSelect()
  //     return ''
  //   }
  // }
</script>
