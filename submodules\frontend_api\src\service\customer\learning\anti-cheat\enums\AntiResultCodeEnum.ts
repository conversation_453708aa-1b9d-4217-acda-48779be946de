export enum AntiResultCodeEnum {
  /**
   * 无需监管
   */
  CODE_NO_TRIG = 'AN4000',

  /**
   * 需要监管
   */
  CODE_NEED_TRIG = 'AN4001',

  /**
   * 轮询中
   */
  CODE_ANTI_VERIFYING = 'AN4002',

  /**
   * 结果完成，验证成功
   */
  CODE_COMPLETE_SUCCESS = 'AN4003',

  /**
   * 结果完成，验证失败，无次数，无需回滚
   */
  CODE_COMPLETE_FAILD_NOT_ALLOW = 'AN4004',

  /**
   * 结果完成，验证失败，有次数，继续验证
   */
  CODE_COMPLETE_FAILD_ALLOW = 'AN4005',

  /**
   * 结果完成，验证失败，无次数，需回滚
   */
  CODE_COMPLETE_FAILD_ROLLBACK = 'AN4006',

  /**
   * 发生异常，无法查询到匹配的防作弊配置
   */
  CODE_ERROR_NO_MATCH_CONFIG = 'AN5001',

  /**
   * 防作弊token过期
   */
  CODE_ERROR_NO_MATCH_TOKEN = 'AN5002',

  /**
   * 防作弊验证过程出现异常，请联系排查
   */
  CODE_ERROR_CATCH_EXCEPTION = 'AN5003'
}
