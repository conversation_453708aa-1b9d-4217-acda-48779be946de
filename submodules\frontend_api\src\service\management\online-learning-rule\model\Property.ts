import { SuitIndustryRangeResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryOperationIndustry'
import QuerySubject from '@api/service/common/basic-data-dictionary/query/QuerySubject'
import IndustryEnum, { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import QueryYear from '@api/service/common/basic-data-dictionary/query/QueryYear'
import YearVo from '@api/service/common/basic-data-dictionary/query/vo/YearVo'
import QueryTrainingMajor from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'

class Property {
  // 年度展示名称
  yearName = ''
  /* *
   * 赋值给培训属性
   * 范围类型 rangeType
   * 年度 year
   * 科目 subjectType
   * 培训类别 trainingCategory
   * 培训对象 trainingObject
   * 学科 subject
   * 岗位类别 positionCategory
   * 技术等级 jobLevel
   * 学段 learningPhase
   */
  async setData(ite: SuitIndustryRangeResponse, rangeType: string, industryId: IndustryIdEnum) {
    const temp: string[] = []
    ite?.addtionalRangeList.map((sitem) => {
      if (sitem.rangeType == rangeType) {
        temp.push(sitem.rangeValue)
        return sitem.rangeValue
      }
    })
    this[rangeType] = temp
    if (!this[rangeType]?.length) {
      return
    }
    if (this[rangeType].includes('-1')) {
      this[rangeType + 'Name'] = '全部'
      switch (rangeType) {
        case 'year':
          this[rangeType + 'Name'] += '年度'
          break
        case 'subjectType':
          this[rangeType + 'Name'] += '科目'
          break
        case 'trainingCategory':
          this[rangeType + 'Name'] += '培训类别'
          break
        case 'trainingObject':
          this[rangeType + 'Name'] += '对象'
          break
        case 'discipline':
          this[rangeType + 'Name'] += '学科'
          break
        case 'positionCategory':
          this[rangeType + 'Name'] += '岗位类别'
          break
        case 'jobLevel':
          this[rangeType + 'Name'] += '技术等级'
          break
        case 'learningPhase':
          this[rangeType + 'Name'] += '学段'
          break
        case 'trainingProfessional':
          this[rangeType + 'Name'] += '培训专业'
          break
        // case 'certType':
        //   this[rangeType + 'Name'] += '证书类型'
        //   break
        // case 'professionType':
        //   this[rangeType + 'Name'] += '执业类别'
        //   break
        default:
          break
      }
      return
    }
    const yearList = [] as string[]
    const fieldList = [] as string[]
    await Promise.all(
      this[rangeType].map(async (item: string, index: number) => {
        // 名称转换
        const name = await this.toName(item, industryId, rangeType)
        if (rangeType == 'year') {
          yearList.push(name)
        } else {
          fieldList.push(name)
          // fieldName += name + (index == this[rangeType].length - 1 ? '' : '、')
        }
      })
    )
    if (rangeType == 'year') {
      // 年度倒序
      // this[rangeType + 'Name'] = fieldName
      //   .split('、')
      //   .sort((a, b) => {
      //     return Number(b) - Number(a)
      //   })
      //   .join('、')
      this[rangeType + 'Name'] = yearList
        .sort((a, b) => {
          return Number(b) - Number(a)
        })
        .join('、')
    } else {
      console.log(fieldList, 'fieldList')
      this[rangeType + 'Name'] = fieldList.join('、')
    }
  }

  async toName(value: string, industryId: IndustryIdEnum, rangeType: string) {
    let list = new Array<TrainingPropertyResponse>()
    let trainingCategory = new Array<TrainingCategoryVo>()
    let yearList = new Array<YearVo>()
    if (rangeType == 'year') {
      // 执行年度逻辑
      const res = await QueryYear.queryYearList()
      if (res.isSuccess()) {
        yearList = QueryYear.yearList
      } else {
        yearList = new Array<YearVo>()
      }
    } else if (rangeType == 'subjectType') {
      // 科目类型
      list = await QueryIndustry.getOperationSubjectList(industryId)
    } else if (rangeType == 'trainingCategory') {
      // 培训类别
      list = await QueryIndustry.getOperationTraining(industryId)
    } else if (rangeType == 'trainingObject') {
      // 培训对象
      list = await QueryIndustry.getTrainingObject(industryId)
    } else if (rangeType == 'discipline') {
      // 学科
      list = await QuerySubject.queryAllSubject()
    } else if (rangeType == 'positionCategory') {
      // 岗位类别
      list = await QueryIndustry.getPositionCategory(industryId)
    } else if (rangeType == 'jobLevel') {
      // 技术等级
      list = await QueryIndustry.getJobLevel(industryId)
    } else if (rangeType == 'learningPhase') {
      // 学段
      list = await QueryIndustry.getSection(industryId)
    } else if (rangeType == 'trainingProfessional') {
      trainingCategory = await QueryTrainingMajor.getTrainingMajorByIdList({
        industryId: industryId,
        trainingMajorIdList: [value]
      })
    }
    // else if (rangeType == 'certType') {
    //   list = await QueryIndustry.getCertificatesType(industryId)
    // } else if (rangeType == 'professionType') {
    //   list = await QueryIndustry.getPractitionerCategory(industryId)
    // }
    if (rangeType == 'year') {
      return yearList.find((item) => item.id == value)?.year
    } else if (rangeType == 'trainingProfessional') {
      return trainingCategory.find((item) => item.propertyId == value)?.name
    } else {
      return list.find((item) => item.propertyId == value)?.name
    }
  }
}

/**
 * 培训属性集合 人社行业
 */
export class RSProperty extends Property {
  // 培训年度
  year: Array<string> = []
  // 科目类型
  subjectType: Array<string> = []
  //培训专业
  trainingProfessional: Array<string> = []
  // 培训专业名称
  trainingProfessionalName = ''
  // 科目类型展示名称
  subjectTypeName = ''

  /**
   * 是否遍历
   */
  get isTraverse() {
    return this.year.length || this.subjectType.length || this.trainingProfessional.length
  }
}
/**
 * 培训属性集合 建设行业
 */
export class JSProperty extends Property {
  // 培训年度
  year: Array<string> = []
  // 科目类型
  subjectType: Array<string> = []
  // 科目类型展示名称
  subjectTypeName = ''
  // 培训类别
  trainingCategory: Array<string> = []
  //培训类别展示名称
  trainingCategoryName = ''
  /**
   * 是否遍历
   */
  get isTraverse() {
    return this.subjectType.length || this.trainingCategory.length
  }
}
/**
 * 培训属性集合 职业卫生行业
 */
export class WSProperty extends Property {
  // 培训年度
  year: Array<string> = []
  // 培训类别
  trainingCategory: Array<string> = []
  //培训类别展示名称
  trainingCategoryName = ''
  // 培训对象
  trainingObject: Array<string> = []
  // 培训对象展示名称
  trainingObjectName = ''
  // 岗位类别
  positionCategory: Array<string> = []
  // 岗位类别展示名称
  positionCategoryName = ''
  /**
   * 是否遍历
   */
  get isTraverse() {
    return this.trainingObject.length || this.positionCategory.length
  }
}
/**
 * 培训属性集合 工勤行业
 */
export class GQProperty extends Property {
  // 培训年度
  year: Array<string> = []
  // 技术等级
  jobLevel: Array<string> = []
  //技术等级展示名称
  jobLevelName = ''
  /**
   * 是否遍历
   */
  get isTraverse() {
    return this.jobLevel.length
  }
}
/**
 * 培训属性集合 教师行业
 */
export class LSProperty extends Property {
  // 培训年度
  year: Array<string> = []
  // 学段
  learningPhase: Array<string> = []
  // 学段展示名称
  learningPhaseName = ''
  // 学科
  discipline: Array<string> = []
  // 学科展示名称
  disciplineName = ''
  /**
   * 是否遍历
   */
  get isTraverse() {
    return this.learningPhase.length || this.discipline.length
  }
}

/**
 * 培训属性集合 药师行业
 */
export class YSProperty extends Property {
  // 培训年度
  year: Array<string> = []
  // 科目类型
  subjectType: Array<string> = []
  // 科目类型展示名称
  subjectTypeName = ''
  // // 证书类型
  // certificatesType: Array<string> = []
  // // 证书类型展示名称
  // certificatesTypeName = ''
  // // 执业类别
  // practitionerCategory: Array<string> = []
  // // 执业类别名称
  // practitionerCategoryName = ''
  /**
   * 是否遍历
   */
  get isTraverse() {
    return this.subjectType.length
  }
}
