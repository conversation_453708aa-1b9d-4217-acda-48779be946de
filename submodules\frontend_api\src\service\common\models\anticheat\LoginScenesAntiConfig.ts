import FaceRecognitionShape from '@api/service/common/models/anticheat/FaceRecognitionShape'

class LoginScenesAntiConfig {
  /**
   * 防作弊配置编号
   */
  id: string
  /**
   * 所属平台编号
   */
  platformId: string
  /**
   * 所属平台版本编号
   */
  platformVersionId: string
  /**
   * 所属项目编号
   */
  projectId: string
  /**
   * 所属子项目编号
   */
  subProjectId: string
  /**
   * 所属组织机构编号
   */
  organizationId: string
  /**
   * 所属单位编号
   */
  unitId: string
  /**
   * 应用场景,0/1/2,学习/考试/登录
   */
  usageScenarios: number
  /**
   * 应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
   */
  useRange: number
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 当前只支持人脸识别模式
   */
  shapeModel: FaceRecognitionShape
  /**
   * 学习前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLoginBehavior: number
  /**
   * 配置时间
   */
  createTime: Date
  /**
   * 创建人编号
   */
  createUserId: string
  /**
   * 最后更新人
   */
  updateUserId: string
  /**
   * 最后更新时间
   */
  updateTime: Date
}

export default LoginScenesAntiConfig
