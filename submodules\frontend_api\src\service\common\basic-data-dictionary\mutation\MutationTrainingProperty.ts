import msDictionaryV1 from '@api/ms-gateway/ms-dictionary-v1'
import { ResponseStatus } from '@hbfe/common'
import UpdateTrainingPropertyVo from './vo/UpdateTrainingPropertyVo'
/**
 * 培训属性即 包含科目类型、培训类别、培训专业
 */
class MutationTrainingProperty {
  async doUpdateShowName(param: UpdateTrainingPropertyVo): Promise<ResponseStatus> {
    const res = await msDictionaryV1.updateShowName(param.to())
    return res.status
  }
}
export default MutationTrainingProperty
