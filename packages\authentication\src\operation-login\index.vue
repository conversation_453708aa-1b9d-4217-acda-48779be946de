<template>
  <div class="m-login-wrap">
    <div class="wrap-bd">
      <div class="m-logo">
        <img src="@design/trainingInstitution/assets/images/huabo-logo.png" class="logo-pic" />通用平台运营管理后台
      </div>
      <div class="m-login" style="height: 500px">
        <div class="m-login-tit">帐号登录</div>
        <el-form
          ref="loginForm"
          :model="formData"
          :rules="rules"
          label-width="auto"
          class="m-form"
          @keyup.native.enter="doLogin"
        >
          <el-form-item prop="account">
            <el-input v-model="formData.account" clearable placeholder="请输入帐号/手机号登录">
              <i slot="prefix" class="el-input__icon hb-iconfont icon-s-name"></i>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input v-model="formData.password" clearable show-password placeholder="请输入密码">
              <i slot="prefix" class="el-input__icon hb-iconfont icon-s-pwd"></i>
            </el-input>
          </el-form-item>
          <el-form-item prop="captcha">
            <div class="f-flex">
              <el-input v-model="formData.captcha" clearable placeholder="请输入图形验证码" class="f-flex-sub">
                <i slot="prefix" class="el-input__icon iconfont icon-yanzhengma"></i>
              </el-input>
              <div class="code" style="height: 52px">
                <img :src="validateCodePic" alt=" " @click="refreshValidateCodePic" title="看不清，点击刷新" />
              </div>
            </div>
          </el-form-item>
          <el-form-item class="is-text op f-pb10">
            <el-checkbox v-model="rememberPassword">记住密码</el-checkbox>
            <!-- <a href="#" class="f-link f-c9 f-fr" @click.stop="forget">忘记密码？</a> -->
          </el-form-item>
          <el-form-item class="m-btn-bar">
            <el-alert type="error" show-icon :closable="false" class="m-alert f-mb10" v-if="loginResult.message">
              {{ loginResult.message }}
            </el-alert>
            <el-button type="primary" @click="doLogin" :loading="loginIng">
              {{ loginIng ? '登录中...' : '立即登录' }}
            </el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="m-login-footer">
      <div>版权所有：福建华博科技股份有限公司</div>
      <div>
        <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">闽ICP备08103886号-2</a>
        <span class="f-ml20"> 增值电信业务经营许可证 <a @click="dialog2 = true">闽B2-20180687</a> </span>
        <el-dialog :visible.sync="dialog2" width="1200px" class="m-dialog">
          <img width="1160px" src="@design/trainingInstitution/assets/images/dianxin_xuke.jpg" alt="" />
        </el-dialog>
      </div>
      <div>
        <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=%20**************" target="_blank">
          <img class="img" src="@design/trainingInstitution/assets/images/beian-icon.png" alt=" " />闽公网安备
          **************号</a
        >
      </div>
    </div>
  </div>
</template>

<script lang="ts">
  import LoginCore from '@hbfe/jxjy-admin-authentication/src/operation-login/login'
  import { Component, Mixins, Watch } from 'vue-property-decorator'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import UserModule from '@api/service/management/user/UserModule'
  import AccountModule from '@api/service/common/account/AccountModule'
  import RootModule from '@/store/RootModule'
  import MZTStatus from '@hbfe/jxjy-admin-authentication/src/operation-login/models/MZTStatus'
  import ServiceTokenMixin from '@hbfe/jxjy-admin-common/src/mixins/ServiceTokenMixin'
  import { SmsCodeApplyRequest } from '@hbfe-ms/ms-basicdata-domain-gateway'
  import { BusinessTypeEnum } from '@hbfe-biz/biz-authentication/dist/enums/BusinessTypeEnum'

  @Component
  export default class extends Mixins(LoginCore) {
    success = 5
    phoneNumbe = ''
    dialogVisible3 = false
    dialog1 = false
    dialog2 = false
    form = {
      name: '',
      region: '',
      date1: '',
      date2: '',
      delivery: false,
      resource: '',
      desc: ''
    }
    webPortal = OnlineSchoolConfigModule.queryPortal?.webPortalInfo
    activeName = 'first'
    input = ''
    select = ''
    UserModule = UserModule.queryUserFactory.queryManagerDetail
    // footContent = ''

    passwordLogin = true
    AccountModule = AccountModule.mutationFactory.getMutationBizAccount()
    hasPhoneNumber = true

    async checkPhoneCaptchFunc(rule: any, value: any, callback: any) {
      if (value && rule.regexp.test(value)) {
        console.log(this.isPhoneCaptchaValid, 'this.isPhoneCaptchaValid valid')
        if (!this.phoneCaptchaVaildFlag) {
          try {
            const res = await this.$authentication.verify.msValidateCaptcha(value)
            if (res.status.code == 200) {
              this.phoneCaptchaValidChange()
              this.phoneCaptchaVaildFlag = true
              callback()
            } else {
              await this.refreshValidateCodePicPhone()
              callback(new Error('验证失败'))
            }
          } catch (e) {
            await this.refreshValidateCodePicPhone()
            callback(new Error('验证失败'))
          }
        } else {
          callback()
        }
      } else {
        callback(new Error('验证码为4位数'))
      }
    }

    checkPhoneRules = {
      captcha: [
        {
          // 以大写小写字母或者数字组合的4位
          regexp: /^[a-zA-z0-9]{4}$/,
          validator: this.checkPhoneCaptchFunc,
          required: true,
          trigger: 'change'
        },
        { required: true, message: '图形验证码不能为空', trigger: 'blur' }
      ],
      smsCode: {
        required: true,
        message: '短信验证码不能为空',
        trigger: 'blur'
      },
      phone: {
        required: true,
        validator: this.validatePhone,
        trigger: 'blur'
      }
    }

    resetPwdAdmin = UserModule.mutationUserFactory.resetPwdAdmin
    queryManagerDetail = UserModule.queryUserFactory.queryManagerDetail

    get Root() {
      return RootModule || {}
    }

    toggleTab(flag: boolean) {
      this.refreshValidateCodePic()
      this.passwordLogin = flag
    }

    async loginSuccess() {
      try {
        await this.UserModule.queryManagerDetail()
        window.location.replace(this.$router.resolve('/school-management/management').href)
        window.location.reload()
      } catch (e) {
        // this.$router.push('/home')
      }
    }

    loginFail(message: string) {
      // 登录失败
      this.$message.warning(message)
    }

    @Watch('activeName')
    paneChange() {
      if (this.activeName === 'first') {
        this.refreshValidateCodePic()
      }
    }

    get query(): MZTStatus {
      return this.$route.query as any
    }

    async created() {
      ServiceTokenMixin.shareInstance().clearService()
      // this.footContent = OnlineSchoolConfigModule.queryPortal?.webPortalInfo.footContent
      // const res = await OnlineSchoolConfigModule.queryPortal.queryDetail()
      // if (res.isSuccess()) {
      // }

      this.passwordLogin = !this.$route.query.channel
      const loginflag = this.query.loginflag
      const trustticket = this.query.trustticket as string
      if (loginflag !== undefined) {
        if (loginflag === 'true') {
          this.$router.replace(`/auto-mzt?loginflag=${loginflag}&trustticket=${trustticket}`)
        }
        // 需要return 避免死循环
        return
      }
      console.log(loginflag, trustticket, '路由参数')
      // 闽政通登录接口入口文档：http://192.168.1.225:8090/pages/viewpage.action?pageId=********
      let resultUrl =
        process.env.NODE_ENV === 'development'
          ? `${location.origin}/#${this.$route.fullPath}`
          : `${location.origin}/admin/#${this.$route.fullPath}`
      resultUrl = encodeURIComponent(resultUrl)
      // await this.$authentication.thirdParty.applyCheckLogin(
      //   ThirdPartyType.BTAdmin,
      //   ConnectIdType.unionId,
      //   AccountType.admin,
      //   resultUrl
      // )
    }

    /**
     *  忘记密码
     */
    forget() {
      this.$router.push('/forget')
    }

    toback() {
      this.$router.push('/home/<USER>')
    }
    cancel() {
      window.location.replace(this.$router.resolve('/home').href)
      window.location.reload()
      this.dialog1 = false
    }

    /**
     * 验证手机号格式
     */
    validatePhone(rule: any, value: any, callback: any) {
      const reg = new RegExp(/^[1]([3-9])[0-9]{9}$/)
      if (value === '') {
        callback(new Error('手机号不能为空'))
      } else {
        reg.test(value) ? callback() : callback(new Error('请输入正确的手机号'))
      }
    }

    phoneCaptchaValidChange() {
      this.isPhoneCaptchaValid = false
      console.log(this.isPhoneCaptchaValid, 'this.isPhoneCaptchaValid change false')
    }

    changePhoneClose() {
      this.dialogVisible3 = false
      this.dialog1 = false
      window.location.replace(this.$router.resolve('/home').href)
      window.location.reload()
    }
    changePhone() {
      this.dialog1 = true
    }

    get phone() {
      return this.queryManagerDetail.adminInfo.userInfo.phone
    }
    get userName() {
      return this.queryManagerDetail.adminInfo.userInfo.userName
    }
    get accountId() {
      return this.queryManagerDetail.adminInfo.accountInfo.accountId
    }
    // 获取短信验证码
    async sendSmsCodeByUpdatePhone() {
      const params = new SmsCodeApplyRequest()
      params.phone = this.changePhoneFrom.phone
      params.businessType = BusinessTypeEnum.change_binding_phone
      params.token = this.$authentication.verify.captchaToken
      const res = await this.$authentication.verify.msSendSmsCode(params)
      if (res.data.code === 409) {
        this.$message.error('一个手机号一天只能接收4条短信，今日次数已用完，请隔天再试。')
      } else if (res.data.code === 410) {
        this.$message.error('发送验证码失败')
      } else if (res.data.code === 701) {
        this.$message.error('请重新验证图形验证码后，再次获取短信验证码')
      } else if (res.data.code === 515) {
        this.$message.error('不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)')
      } else if (res.data.code === 400) {
        this.$message.error('token无效')
      } else if (res.data.code === 509) {
        this.$message.error('未绑定手机号')
      } else if (res.data.code === 514) {
        this.$message.error('token中未携带手机号')
      }
      if (res.status.code === 200) {
        this.loginSending = false
        this.countDown(this.count)
      } else {
        this.$message.error('发送验证码失败')
      }
    }

    beforeDestroy() {
      this.stopIntervalTimer()
    }
  }
</script>
