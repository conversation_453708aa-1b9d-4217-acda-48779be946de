import { CreCourseSubjectRequest } from '@api/diff-gateway/platform-jxjypxtypt-byzj-school'

class CourseMaintenanceQueryVo {
  /**
   * 年度sku属性
   */
  year = ''
  /**
   * 地区传末级
   */
  region: string[] = []
  /**
   * 科目类型sku属性
   */
  name = ''
  toDto() {
    const params = new CreCourseSubjectRequest()
    params.year = this.year
    params.subjectType = this.name
    params.regional = this.region && this.region.length > 0 ? this.region[this.region.length - 1] : ''
    return params
  }
}

export default CourseMaintenanceQueryVo
