import CheckAccountParam from '../query/vo/CheckAccountParam'
import { OrderSortRequest, ReturnSortRequest } from '@api/platform-gateway/jxjy-data-export-gateway-backstage'

export default abstract class MutationCheckAccountBase {
  /**
   * 个人报名对账导出
   */
  abstract listExport(checkAccountParam: CheckAccountParam, sortRequest?: Array<OrderSortRequest>): Promise<boolean>

  /**
   * 个人退款对账导出
   */
  abstract listReturnExport(
    checkAccountParam: CheckAccountParam,
    sortRequest?: Array<ReturnSortRequest>
  ): Promise<boolean>
}
