<template>
  <el-main
    v-if="$hasPermission('query')"
    desc="智能学习任务跟踪"
    actions="created,@ArrangementResults,@ExecutionEnvironment"
  >
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky" @tab-click="tabClick">
      <el-tab-pane label="智能学习编排结果" name="arrangement">
        <template v-if="$hasPermission('arrangement')" desc="智能学习编排结果" actions="@ArrangementResults">
          <arrangement-results ref="arrangementRef" />
        </template>
      </el-tab-pane>
      <el-tab-pane label="智能学习执行情况" name="execution">
        <template v-if="$hasPermission('execution')" desc="智能学习执行情况" actions="@ExecutionEnvironment">
          <execution-environment ref="executionRef" />
        </template>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script lang="ts">
  import { Vue, Component, Ref } from 'vue-property-decorator'
  import ArrangementResults from '@hbfe/jxjy-admin-intelligentLearning/src/learning-task-tracking/components/arrangement-results.vue'
  import ExecutionEnvironment from '@hbfe/jxjy-admin-intelligentLearning/src/learning-task-tracking/components/execution-environment.vue'

  @Component({
    components: {
      ArrangementResults,
      ExecutionEnvironment
    }
  })
  export default class extends Vue {
    @Ref('arrangementRef') arrangementRef: ArrangementResults
    @Ref('executionRef') executionRef: ExecutionEnvironment

    /**
     * 选中值
     */
    activeName = 'arrangement'

    async created() {
      this.$nextTick(async () => {
        if (this.$route.query.active == 'execution') {
          this.activeName = 'execution'
          await this.executionRef.handleNow()
          return
        }
        await this.queryList()
      })
    }

    /**
     * 查询
     */
    async queryList() {
      if (this.activeName === 'arrangement') {
        await this.arrangementRef.reset()
      } else if (this.activeName === 'execution') {
        await this.executionRef.reset()
      } else {
        //   todo
      }
    }

    /**
     * 切换tab
     */
    async tabClick() {
      await this.queryList()
    }
  }
</script>
