<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div class="m-tit is-small bg-gray is-border-bottom">
      <span class="tit-txt">学习心得要求</span>
    </div>
    <div class="f-p20">
      <el-row type="flex" justify="center" class="width-limit">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form ref="form" label-width="150px" class="m-form f-mt10">
            <el-form-item label="班级心得前置条件：" required v-if="learningFeelInfo.classExperienceList.length">
              <el-radio-group
                v-model="learningFeelInfo.classCondition"
                @input="changeBtn('classCondition')"
                :disabled="isSelectedOption()"
              >
                <el-radio :label="true">可直接参加</el-radio>
                <el-radio :label="false">完成班级所有课程学习方可参加</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="课程心得前置条件：" required v-if="learningFeelInfo.courseExperienceList.length">
              <el-radio-group
                v-model="learningFeelInfo.courseCondition"
                @input="changeBtn('courseCondition')"
                :disabled="isSelectedOption()"
              >
                <el-radio :label="true">可直接参加</el-radio>
                <el-radio :label="false">完成班级指定课程学习方可参加</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="是否纳入考核：" required>
              <el-radio-group v-model="learningFeelInfo.isExamine" :disabled="isSelectedOption()">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item required v-if="learningFeelInfo.isExamine">
              至少需要参加
              <el-input-number
                @blur="blurCount('joinCount')"
                :min="0"
                :max="learningFeelInfo.experienceList.length"
                size="small"
                class="f-mlr5"
                v-model="learningFeelInfo.joinCount"
                :disabled="isSelectedOption()"
              />
              个学习心得
              <p>
                <i class="f-cr" v-if="learningFeelInfo.joinCount == 0">至少需要参加的学习心得不可为空</i>
              </p>
            </el-form-item>
            <el-form-item label="成绩要求：" required>
              成绩 ≥
              <el-input-number
                @blur="blurCount('score')"
                :min="0"
                :max="100"
                size="small"
                class="f-mlr5"
                v-model="learningFeelInfo.score"
                :disabled="isSelectedOption()"
              />
              分（总分：100分）视为通过。
            </el-form-item>
            <el-form-item label="考核要求：">
              <p>1. 各项学习心得要求以具体配置为准</p>
              <p v-if="learningFeelInfo.isExamine">
                2. 学习心得纳入考核，至少参加
                <i class="f-cr">{{ learningFeelInfo.joinCount }}</i>
                个心得，且每项心得均为通过。
              </p>
              <p v-else>2. 学习心得不纳入考核</p>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-card>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import LearningExperience from '@api/service/management/train-class/mutation/vo/LearningExperience'
  import { LearningExperienceEnum } from '@api/service/management/train-class/mutation/Enum/LearningExperienceEnum'
  import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'

  @Component
  export default class extends Vue {
    /**
     * 学习心得配置 - 双向绑定
     */
    @Prop({
      type: LearningExperience,
      default: () => {
        return new LearningExperience()
      }
    })
    learningFeelInfo!: LearningExperience

    /**
     * 课程学习信息
     */
    @Prop({
      required: true,
      type: CourseLearningLearningType
    })
    courseLearning: CourseLearningLearningType

    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number

    LearningExperienceEnum = LearningExperienceEnum

    changeBtn(attribute: string) {
      if (attribute == 'courseCondition') {
        if (!this.learningFeelInfo.courseCondition && !this.courseLearning.isSelected) {
          this.$message.error('请先勾选课程学习，才能进行操作')
          return (this.learningFeelInfo.courseCondition = true)
        }
      } else if (attribute == 'classCondition') {
        if (!this.learningFeelInfo.classCondition && !this.courseLearning.isSelected) {
          this.$message.error('请先勾选课程学习，才能进行操作')
          return (this.learningFeelInfo.classCondition = true)
        }
      }
    }

    // 判断为空的时候 返回0
    blurCount(target: string) {
      if (target == 'joinCount' && typeof this.learningFeelInfo.joinCount !== 'number') {
        this.learningFeelInfo.joinCount = 0
      } else if (target == 'score' && typeof this.learningFeelInfo.score !== 'number') {
        this.learningFeelInfo.score = 0
      }
    }

    /**
     * 校验学习心得是否被勾选
     */
    isSelectedOption() {
      if (!this.learningFeelInfo.isSelected) {
        return true
      }
    }
  }
</script>
