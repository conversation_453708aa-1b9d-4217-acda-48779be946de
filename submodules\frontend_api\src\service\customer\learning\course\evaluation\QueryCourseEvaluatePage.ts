import { UiPage } from '@hbfe/common'
import CourseEvaluateDetail from '@api/service/customer/learning/course/vo/CourseEvaluateDetail'

class QueryCourseEvaluatePage {
  constructor(courseId: string) {
    this.courseId = courseId
  }

  courseId: string
  page: UiPage
  list: Array<CourseEvaluateDetail> = new Array<CourseEvaluateDetail>()

  async query() {
    console.log(this.page)
    return {}
  }
}

export default QueryCourseEvaluatePage
