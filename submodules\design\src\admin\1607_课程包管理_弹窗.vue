<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--课程包移除课程同步方案-->
        <el-button type="primary" class="f-mr20" @click="open3">课程包移除课程同步方案</el-button>
        <!--复制课程包-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">复制课程包</el-button>
        <el-drawer
          title="复制课程包"
          :visible.sync="dialog1"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" :closable="false" class="m-alert f-mb20">
              您正在复制【长视频课程包】，课程包内含 2 门课程，合计 5 学时。
            </el-alert>
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
              <el-form-item label="课程包名称：" required>
                <el-input v-model="form.name" placeholder="请输入" />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <!--列表-日志详情-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20">列表-日志详情</el-button>
        <el-drawer title="日志详情" :visible.sync="dialog3" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">操作人姓名</span></p>
                <div class="f-c6">
                  <div class="f-mt5">修改课程包名称【福建专技培训平台专业课包】改为【福州专技培训平台专业课】</div>
                </div>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">林林一</span></p>
                <div class="f-c6">
                  <!--每次修改记录换行展示，最多显示两个修改记录-->
                  <div class="f-mt5">修改课程包名称【福建专技培训平台专业课包】改为【福州专技培训平台专业课】；</div>
                  <div class="f-mt5">修改课程包名称【福建专技培训平台专业课包】改为【福州专技培训平台专业课】；</div>
                  <el-button type="text">查看更多 +</el-button>
                  <!--展开后显示-->
                  <!--<el-button type="text">收起更多 -</el-button>-->
                </div>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">林林二</span></p>
                <div class="f-c6">
                  <div class="f-mt5">修改课程包名称【福建专技培训平台专业课包】改为【福州专技培训平台专业课】</div>
                </div>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">林林三</span></p>
                <div class="f-c6">
                  <div class="f-mt5">修改课程包名称【福建专技培训平台专业课包】改为【福州专技培训平台专业课】</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
        <!--详情页-日志详情-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20">详情页-日志详情</el-button>
        <el-drawer title="日志详情" :visible.sync="dialog4" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">操作人姓名</span></p>
                <div class="f-c6">
                  <div class="f-mt5">发起同步，同步结果【已同步/同步中/同步失败】</div>
                </div>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">林林一</span></p>
                <div class="f-c6">
                  <div class="f-mt5">发起同步，同步结果【同步失败】，失败原因：失败原因失败原因</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      open3() {
        this.$message({
          message: '方案配置的心得数不足考核要求通过的心得数。',
          type: 'warning',
          duration: 5000,
          customClass: 'm-message'
        })
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
