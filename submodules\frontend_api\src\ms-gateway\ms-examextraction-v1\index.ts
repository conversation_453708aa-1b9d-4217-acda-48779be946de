import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-examextraction-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-examextraction-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum FillAnswerType {
  disarray = 'disarray',
  sequence = 'sequence',
  sequenceRelate = 'sequenceRelate'
}

// 类

/**
 * 题库对应数量试题设置对象
<AUTHOR>
 */
export class LibraryMapQuestionNumSetting {
  /**
   * 题库id
   */
  libraryId?: string
  /**
   * 问题数量
   */
  questionNum?: number
}

/**
 * <AUTHOR> create 2021/6/3 17:35
 */
export class PaperQuestionDTO {
  /**
   * 试题ID
   */
  questionId?: string
  /**
   * 分数，-1表示不为分数评定方式为
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题类型
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
   */
  answerRequired?: boolean
}

/**
 * <AUTHOR> create 2021/6/3 17:37
 */
export class QuestionGroupDTO {
  /**
   * 序号
   */
  sequence: number
  /**
   * 试题类型
   */
  questionType: number
  /**
   * 大题名称
   */
  groupName?: string
  /**
   * 每题平均分数，-1表示不为分数评定方式
   */
  eachQuestionScore: number
}

/**
 * 创建分类请求
<AUTHOR>
 */
export class CreatePaperPublishConfigureCategoryRequest {
  /**
   * 分类名称【必填】
   */
  name?: string
  /**
   * 父级分类id(根目录填-1)【必填】
   */
  parentId?: string
  /**
   * 排序
   */
  sort?: number
}

/**
 * 出卷配置创建事件
<AUTHOR> create 2021/8/20 14:48
 */
export class PaperPublishConfigureCreateRequest {
  /**
   * 出卷配置名称
   */
  name?: string
  /**
   * 适用范围 用于筛选自定义的分类
@see com.fjhb.domain.exam.api.consts.UsageScopes
   */
  usageScope: number
  /**
   * 调查问卷类型
@see com.fjhb.domain.exam.api.consts.QuestionnairePaperPublishConfigureType
   */
  questionnaireType?: number
  /**
   * 出卷模式
   */
  publishPattern?: PublishPatternRequest
  /**
   * 分类id
   */
  paperPublishConfigureCategoryId?: string
  /**
   * 是否是草稿    1-是 2-不是
   */
  isDraft: number
}

/**
 * <AUTHOR> create 2021/8/20 14:48
 */
export class PaperPublishConfigureUpdateRequest {
  /**
   * 出卷配置id
   */
  id: string
  /**
   * 出卷配置名称
   */
  name?: string
  /**
   * 适用范围 用于筛选自定义的分类
   */
  usageScope?: number
  /**
   * 调查问卷类型
@see com.fjhb.domain.exam.api.consts.QuestionnairePaperPublishConfigureType
   */
  questionnaireType?: number
  /**
   * 出卷模式
   */
  publishPattern?: PublishPatternRequest
  /**
   * 分类id
   */
  paperPublishConfigureCategoryId?: string
  /**
   * 是否启用 1-启用    2-停用
   */
  status: number
  /**
   * 是否是草稿    1-是 2-不是
   */
  isDraft: number
}

/**
 * 出卷模式基类
<AUTHOR> create 2021/8/20 15:05
 */
export class PublishPatternRequest {
  /**
   * 出卷模式类型
@see PublishPatterns
   */
  type: number
}

/**
 * 更新分类请求
<AUTHOR>
 */
export class UpdatePaperPublishConfigureCategoryRequest {
  /**
   * 分类id【必填】
   */
  id?: string
  /**
   * 分类名称【必填】
   */
  name?: string
  /**
   * 父级分类id【必填】
   */
  parentId?: string
  /**
   * 排序【必填】
   */
  sort?: number
}

/**
 * 正确率评定方式
<AUTHOR>
 */
export class CorrectRateEvaluatePatternRequest implements EvaluatePatternRequest {
  /**
   * 评定方式类型
1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
@see EvaluatePatternTypes
   */
  type: number
}

/**
 * 评定方式
<AUTHOR>
 */
export class EvaluatePatternRequest {
  /**
   * 评定方式类型
1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
@see EvaluatePatternTypes
   */
  type: number
}

/**
 * 无评定方式
<AUTHOR>
 */
export class NoneEvaluatePatternRequest implements EvaluatePatternRequest {
  /**
   * 评定方式类型
1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
@see EvaluatePatternTypes
   */
  type: number
}

/**
 * 计分评定方式
<AUTHOR>
 */
export class OnlyScoringEvaluatePatternRequest implements EvaluatePatternRequest {
  scoringDescribes?: Array<ScoringDescribe>
  /**
   * 评定方式类型
1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
@see EvaluatePatternTypes
   */
  type: number
}

/**
 * <AUTHOR>
 */
export class QuestionScoreRequest {
  /**
   * 试题id
   */
  questionId?: string
  /**
   * 分数
   */
  score: number
}

/**
 * 试题分数设置信息
<AUTHOR>
 */
export class QuestionScoreSettingRequest {
  /**
   * 大题序号
   */
  sequence?: number
  /**
   * 试题类型
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 每题平均分
   */
  eachQuestionScore: number
  /**
   * 具体试题分数
   */
  questionScores?: Array<QuestionScoreRequest>
}

/**
 * 分值评定方式
<AUTHOR>
 */
export class ScoreEvaluatePatternRequest implements EvaluatePatternRequest {
  /**
   * 总分
   */
  totalScore: number
  /**
   * 合格分数
   */
  qualifiedScore: number
  /**
   * 试题分数
   */
  questionScores?: Array<QuestionScoreSettingRequest>
  /**
   * 多选提漏选得分模式
0:不得分|1：的全部分数|2：得一半分数|3：每个选项按平均得分
@see MultipleQuestionMissScorePatterns
   */
  multipleMissScorePattern: number
  /**
   * 评定方式类型
1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
@see EvaluatePatternTypes
   */
  type: number
}

/**
 * 单选题计分描述
 */
export class RadioScoringDescribe implements ScoringDescribe {
  /**
   * 分数来源类型
@see  ScoreSourceTypes
   */
  scoreSourceTypes?: number
  type?: number
}

/**
 * 计分描述基类
<AUTHOR>
 */
export class ScoringDescribe {
  type?: number
}

/**
 * 智能出卷配置
<AUTHOR> create 2021/8/20 15:06
 */
export class AutomaticPublishPatternRequest implements PublishPatternRequest {
  /**
   * 建议作答时长【单位：秒】
   */
  suggestionTimeLength: number
  /**
   * 抽题规则
   */
  questionExtractRule?: QuestionExtractRuleRequest
  /**
   * 评定方式
   */
  evaluatePattern?: EvaluatePatternRequest
  /**
   * 出卷模式类型
@see PublishPatterns
   */
  type: number
}

/**
 * 固定卷出卷模式
<AUTHOR> create 2021/8/20 16:08
 */
export class FixedPaperRequest implements PublishPatternRequest {
  /**
   * 试卷id
   */
  id?: string
  /**
   * 试卷名称
   */
  name?: string
  /**
   * 描述
   */
  description?: string
  /**
   * 作答时长
   */
  timeLength: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 大题集合
   */
  groups?: Array<QuestionGroupDTO>
  /**
   * 试题集合
   */
  questions?: Array<PaperQuestionDTO>
  /**
   * 评定方式
   */
  evaluatePattern?: EvaluatePatternRequest
  /**
   * 出卷模式类型
@see PublishPatterns
   */
  type: number
}

/**
 * AB卷出卷模式
<AUTHOR> create 2021/8/20 16:09
 */
export class MultipleFixedPaperPublishPatternRequest implements PublishPatternRequest {
  /**
   * 固定卷集合
   */
  fixedPapers?: Array<FixedPaperRequest>
  /**
   * 出卷模式类型
@see PublishPatterns
   */
  type: number
}

/**
 * 选择题答案选项实体
<AUTHOR>
 */
export class ChooseAnswerOptionRequest {
  /**
   * 答案ID
   */
  id: string
  /**
   * 答案内容
   */
  content: string
  /**
   * 选项建议分数
   */
  suggestionScore?: number
  /**
   * 是否允许填空
   */
  enableFillContent?: boolean
  /**
   * 填空是否必填
   */
  mustFillContent?: boolean
}

/**
 * <AUTHOR> create 2021/6/29 14:03
 */
export class CreateQuestionRequest {
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * <AUTHOR> create 2021/6/28 14:13
 */
export class CreateAskQuestionRequest implements CreateQuestionRequest {
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * <AUTHOR> create 2021/6/28 14:43
 */
export class ChildQuestionCreateInfoRequest {
  /**
   * 子题序号
   */
  no: number
  /**
   * 试题内容
   */
  question?: CreateQuestionRequest
}

/**
 * <AUTHOR> create 2021/6/28 14:43
 */
export class CreateFatherQuestionRequest implements CreateQuestionRequest {
  /**
   * 子题集合
   */
  childQuestions: Array<ChildQuestionCreateInfoRequest>
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 填空题创建命令
<AUTHOR> create 2021/6/28 14:09
 */
export class CreateFillQuestionRequest implements CreateQuestionRequest {
  /**
   * 填空数
   */
  fillCount: number
  /**
   * 正确答案
   */
  correctAnswer?: FillAnswerRequest
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 散乱无序填空题答案实体
<AUTHOR>
 */
export class DisarrayFillAnswerRequest implements FillAnswerRequest {
  /**
   * 正确答案集合
   */
  correctAnswers: Array<string>
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * 填空题答案基类
<AUTHOR>
 */
export class FillAnswerRequest {
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * <AUTHOR> create 2021/6/29 15:41
 */
export class FillCorrectAnswers {
  /**
   * 空格位置
   */
  blankNo: number
  /**
   * 答案备选项
   */
  answers?: Array<string>
}

/**
 * 按序填空题答案
<AUTHOR>
 */
export class SequenceFillAnswerRequest implements FillAnswerRequest {
  correctAnswers: Array<FillCorrectAnswers>
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * 按序关联填空题答案实体
<AUTHOR>
 */
export class SequenceRateFillAnswerRequest implements FillAnswerRequest {
  /**
   * 正确答案集合
   */
  correctAnswers?: Array<SequenceFillAnswerRequest>
  /**
   * 答案类型
   */
  type: FillAnswerType
}

/**
 * 多选题创建命令
<AUTHOR> create 2021/6/28 14:07
 */
export class CreateMultipleQuestionRequest implements CreateQuestionRequest {
  /**
   * 可选答案列表【必填】
   */
  answerOptions: Array<ChooseAnswerOptionRequest>
  /**
   * 正确答案ID集合【必填】
   */
  correctAnswerIds: Array<string>
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 判断题创建命令
<AUTHOR> create 2021/6/28 14:05
 */
export class CreateOpinionQuestionRequest implements CreateQuestionRequest {
  /**
   * 正确答案【必填】
   */
  correctAnswer: boolean
  /**
   * 正确文本【必填】
   */
  correctAnswerText?: string
  /**
   * 不正确文本【必填】
   */
  incorrectAnswerText?: string
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 单选题创建命令
<AUTHOR> create 2021/6/28 9:39
 */
export class CreateRadioQuestionRequest implements CreateQuestionRequest {
  /**
   * 可选答案列表【必填】
   */
  answerOptions: Array<ChooseAnswerOptionRequest>
  /**
   * 正确答案ID【必填】
   */
  correctAnswerId?: string
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * @Author: chenzeyu
@CreateTime: 2024-07-29  16:12
@Description: 量表题创建请求
 */
export class CreateScaleQuestionRequest implements CreateQuestionRequest {
  /**
   * 量表类型
@see ScaleTypes
   */
  scaleType: number
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  startDegree?: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  endDegree?: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
  /**
   * 试题Id
   */
  id?: string
  /**
   * 试题题目【必填】
   */
  topic: string
  /**
   * 试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题
   */
  questionType: number
  /**
   * 所属题库ID【必填】
   */
  libraryId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 试题解析
   */
  dissects?: string
  /**
   * 关联课程id
   */
  relateCourseIds?: Array<string>
  /**
   * 试题难度
@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
1-难度 2-中等难度  3-高难度
   */
  questionDifficulty: number
  /**
   * 内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。
   */
  buildIn?: boolean
}

/**
 * 抽题规则
<AUTHOR>
 */
export class QuestionExtractRuleRequest {
  /**
   * 试题总数
   */
  questionCount: number
  /**
   * 出题范围
   */
  questionScopes?: Array<QuestionScopeSettingRequest>
  /**
   * 出题描述
   */
  questionExtracts?: Array<QuestionExtractSettingRequest>
}

/**
 * 出题描述配置请求
<AUTHOR>
 */
export class QuestionExtractSettingRequest {
  /**
   * 试题类型
1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
   */
  questionType: number
  /**
   * 大题序号
   */
  sequence: number
  /**
   * 大题名称
   */
  groupName?: string
  /**
   * 试题数
   */
  questionCount: number
  /**
   * 出题范围
   */
  questionScopes?: Array<QuestionScopeSettingRequest>
}

/**
 * 题库抽取指定数量试题设置信息
<AUTHOR>
 */
export class LibraryFixedQuestionScopeSettingRequest implements QuestionScopeSettingRequest {
  /**
   * 题库对应数量出题设置信息
   */
  libraryMapQuestionNumSettings?: Array<LibraryMapQuestionNumSetting>
  /**
   * 类型
1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
@see QuestionScopeSettingTypes
   */
  type: number
}

/**
 * 题库出题配置请求
<AUTHOR>
 */
export class LibraryQuestionScopeSettingRequest implements QuestionScopeSettingRequest {
  /**
   * 题库id集合
   */
  libraryIds?: Array<string>
  /**
   * 类型
1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
@see QuestionScopeSettingTypes
   */
  type: number
}

/**
 * 出题范围请求
<AUTHOR>
 */
export class QuestionScopeSettingRequest {
  /**
   * 类型
1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
@see QuestionScopeSettingTypes
   */
  type: number
}

/**
 * 标签code出题范围请求
<AUTHOR>
 */
export class TagQuestionScopeSettingRequest implements QuestionScopeSettingRequest {
  /**
   * 标签code
   */
  tagsCode?: Array<string>
  /**
   * 类型
1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
@see QuestionScopeSettingTypes
   */
  type: number
}

/**
 * 单位id出题范围请求
<AUTHOR>
 */
export class UnitQuestionScopeSettingRequest implements QuestionScopeSettingRequest {
  /**
   * 单位id集合
   */
  unitIds?: Array<string>
  /**
   * 类型
1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
@see QuestionScopeSettingTypes
   */
  type: number
}

/**
 * 用户课程出题配置请求
<AUTHOR>
 */
export class UserCourseScopeSettingRequest implements QuestionScopeSettingRequest {
  /**
   * 课程来源
0-指定唯一课程  1-用户课程题库
@see UserCourseSources
   */
  userCourseSource: number
  /**
   * 要求的组卷信息key.
当{@link #userCourseSource} &#x3D; 用户课程题库时需要指定:USER_COURSES_SCHEME_ID
@see ExtractionMessageKeys
   */
  requireKeys?: Array<string>
  /**
   * 类型
1-题库出题范围  2-用户课程出题范围  3-题库指定数量出题范围  4-标签出题范围  5-单位出题范围
@see QuestionScopeSettingTypes
   */
  type: number
}

/**
 * <AUTHOR> create 2021/6/3 17:35
 */
export class CopyQuestionnaireQuestion {
  /**
   * 试题ID
   */
  questionId?: string
  /**
   * 分数，-1表示不为分数评定方式为
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 试题类型
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
   */
  answerRequired?: boolean
  /**
   * 教师评价题标签code，非教师评价题不用传
   */
  teacherEvaluateCode?: string
  /**
   * 复制的试题内容
   */
  copyQuestionContent?: CreateQuestionRequest
}

/**
 * 出卷模式基类
<AUTHOR>
@date 2025/04/30
 */
export class CopyQuestionnaireRequest {
  /**
   * 出卷配置名称
   */
  name?: string
  /**
   * 适用范围 用于筛选自定义的分类
@see com.fjhb.domain.exam.api.consts.UsageScopes
   */
  usageScope: number
  /**
   * 调查问卷类型
@see com.fjhb.domain.exam.api.consts.QuestionnairePaperPublishConfigureType
   */
  questionnaireType?: number
  /**
   * 出卷模式，固定卷
   */
  publishPattern?: QuestionnairePaperRequest
  /**
   * 分类id
   */
  paperPublishConfigureCategoryId?: string
  /**
   * 是否是草稿    1-是 2-不是
   */
  isDraft: number
}

/**
 * 调查问卷固定卷出卷模式
<AUTHOR> create 2021/8/20 16:08
 */
export class QuestionnairePaperRequest {
  /**
   * 试卷id
   */
  id?: string
  /**
   * 出卷模式类型
@see PublishPatterns
   */
  type: number
  /**
   * 试卷名称
   */
  name?: string
  /**
   * 描述
   */
  description?: string
  /**
   * 作答时长
   */
  timeLength: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 大题集合
   */
  groups?: Array<QuestionGroupDTO>
  /**
   * 试题集合
   */
  questions?: Array<CopyQuestionnaireQuestion>
  /**
   * 评定方式
   */
  evaluatePattern?: EvaluatePatternRequest
}

export class QuestionGroup {
  sequence: number
  questionType: number
  groupName: string
  eachQuestionScore: number
}

/**
 * 预览试卷响应类
<AUTHOR>
 */
export class PreviewPaperPublishConfigureResponse {
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 作答时长
   */
  timeLength: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 总题数
   */
  totalQuestionCount: number
  /**
   * 大题集合
   */
  groups: Array<QuestionGroup>
  /**
   * 试卷类型
@see PaperTypes
1-智能卷 2-固定卷
   */
  paperType: number
  /**
   * 试题集合
   */
  questions: Array<BaseQuestionResponse>
}

/**
 * 填空题答案基类
<AUTHOR>
 */
export interface BaseFillAnswerResponse {
  /**
   * 填空题答案类型
@see FillAnswerTypes
1-散乱无序答案 2-有序答案  3-有序关联答案
   */
  type: number
}

/**
 * 子题信息响应对象
<AUTHOR>
 */
export class ChildItemResponse {
  /**
   * 编号
   */
  no: number
  /**
   * 试题id
   */
  questionId: string
}

/**
 * 选择题选项
<AUTHOR>
 */
export class ChooseAnswerOptionResponse {
  /**
   * 选项id
   */
  id: string
  /**
   * 选项文本
   */
  content: string
  /**
   * 是否允许填空
   */
  enableFillContent: boolean
  /**
   * 填空是否必填
   */
  mustFillContent: boolean
}

/**
 * 散乱无序的填空题答案响应对象
<AUTHOR>
 */
export class DisarrayFillAnswerResponse implements BaseFillAnswerResponse {
  /**
   * 正确答案集合
   */
  disarrayCorrectAnswers: Array<string>
  /**
   * 填空题答案类型
@see FillAnswerTypes
1-散乱无序答案 2-有序答案  3-有序关联答案
   */
  type: number
}

/**
 * 有序填空题答案响应对象
<AUTHOR>
 */
export class FillCorrectAnswersResponse {
  /**
   * 空格位置
   */
  blankNo: number
  /**
   * 答案备选项
   */
  answers: Array<string>
}

/**
 * 按序填空题答案响应对象
<AUTHOR>
 */
export class SequenceFillAnswerResponse implements BaseFillAnswerResponse {
  /**
   * 正确答案集合
   */
  sequenceCorrectAnswers: Array<FillCorrectAnswersResponse>
  /**
   * 填空题答案类型
@see FillAnswerTypes
1-散乱无序答案 2-有序答案  3-有序关联答案
   */
  type: number
}

/**
 * 按序关联填空题答案实体
<AUTHOR>
 */
export class SequenceRateFillAnswerResponse implements BaseFillAnswerResponse {
  /**
   * 正确答案集合
   */
  sequenceRateCorrectAnswers: Array<SequenceFillAnswerResponse>
  /**
   * 填空题答案类型
@see FillAnswerTypes
1-散乱无序答案 2-有序答案  3-有序关联答案
   */
  type: number
}

/**
 * 问答题响应对象
<AUTHOR>
 */
export class AskQuestionResponse implements BaseQuestionResponse {
  /**
   * 已作答答案
   */
  askAnswer: string
  /**
   * 试题id
   */
  id: string
  /**
   * 大题序号
   */
  groupSequence: number
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 得分
   */
  score: number
  /**
   * 试题题目
   */
  topic: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题id
   */
  parentQuestionId: string
  /**
   * 题析
   */
  dissects: string
  /**
   * 关联课程id集合
   */
  relateCourseId: Array<string>
  /**
   * 试题难度
   */
  questionDifficulty: number
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 试题响应基类
<AUTHOR>
 */
export interface BaseQuestionResponse {
  /**
   * 试题id
   */
  id: string
  /**
   * 大题序号
   */
  groupSequence: number
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 得分
   */
  score: number
  /**
   * 试题题目
   */
  topic: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题id
   */
  parentQuestionId: string
  /**
   * 题析
   */
  dissects: string
  /**
   * 关联课程id集合
   */
  relateCourseId: Array<string>
  /**
   * 试题难度
   */
  questionDifficulty: number
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 父子题响应对象
<AUTHOR>
 */
export class FatherQuestionResponse implements BaseQuestionResponse {
  /**
   * 子题集合
   */
  childQuestions: Array<ChildItemResponse>
  /**
   * 试题id
   */
  id: string
  /**
   * 大题序号
   */
  groupSequence: number
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 得分
   */
  score: number
  /**
   * 试题题目
   */
  topic: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题id
   */
  parentQuestionId: string
  /**
   * 题析
   */
  dissects: string
  /**
   * 关联课程id集合
   */
  relateCourseId: Array<string>
  /**
   * 试题难度
   */
  questionDifficulty: number
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 填空题响应对象
<AUTHOR>
 */
export class FillQuestionResponse implements BaseQuestionResponse {
  /**
   * 填空数
   */
  fillCount: number
  /**
   * 正确答案
   */
  fillQuestionCorrectAnswer: BaseFillAnswerResponse
  /**
   * 试题id
   */
  id: string
  /**
   * 大题序号
   */
  groupSequence: number
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 得分
   */
  score: number
  /**
   * 试题题目
   */
  topic: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题id
   */
  parentQuestionId: string
  /**
   * 题析
   */
  dissects: string
  /**
   * 关联课程id集合
   */
  relateCourseId: Array<string>
  /**
   * 试题难度
   */
  questionDifficulty: number
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 多选题响应对象
<AUTHOR>
 */
export class MultipleQuestionResponse implements BaseQuestionResponse {
  /**
   * 选项
   */
  answerOptions: Array<ChooseAnswerOptionResponse>
  /**
   * 正确答案id集合
   */
  multipleQuestionCorrectAnswerIds: Array<string>
  /**
   * 已答答案
   */
  multipleAnswer: Array<string>
  /**
   * 填空内容 key是答案id，value是填空的内容
   */
  fillContentMap: Map<string, string>
  /**
   * 试题id
   */
  id: string
  /**
   * 大题序号
   */
  groupSequence: number
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 得分
   */
  score: number
  /**
   * 试题题目
   */
  topic: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题id
   */
  parentQuestionId: string
  /**
   * 题析
   */
  dissects: string
  /**
   * 关联课程id集合
   */
  relateCourseId: Array<string>
  /**
   * 试题难度
   */
  questionDifficulty: number
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 判断题响应对象
<AUTHOR>
 */
export class OpinionQuestionResponse implements BaseQuestionResponse {
  /**
   * 正确答案文本
   */
  correctAnswerText: string
  /**
   * 错误答案文本
   */
  incorrectAnswerText: string
  /**
   * 正确答案
   */
  opinionQuestionCorrectAnswer: boolean
  /**
   * 试题id
   */
  id: string
  /**
   * 大题序号
   */
  groupSequence: number
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 得分
   */
  score: number
  /**
   * 试题题目
   */
  topic: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题id
   */
  parentQuestionId: string
  /**
   * 题析
   */
  dissects: string
  /**
   * 关联课程id集合
   */
  relateCourseId: Array<string>
  /**
   * 试题难度
   */
  questionDifficulty: number
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 单选题试题响应
<AUTHOR>
 */
export class RadioQuestionResponse implements BaseQuestionResponse {
  /**
   * 选项
   */
  answerOptions: Array<ChooseAnswerOptionResponse>
  /**
   * 正确答案id
   */
  radioQuestionCorrectAnswerId: string
  /**
   * 已作答答案
   */
  radioAnswer: string
  /**
   * 填空内容
   */
  fillContent: string
  /**
   * 试题id
   */
  id: string
  /**
   * 大题序号
   */
  groupSequence: number
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 得分
   */
  score: number
  /**
   * 试题题目
   */
  topic: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题id
   */
  parentQuestionId: string
  /**
   * 题析
   */
  dissects: string
  /**
   * 关联课程id集合
   */
  relateCourseId: Array<string>
  /**
   * 试题难度
   */
  questionDifficulty: number
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

/**
 * 量表题响应对象
<AUTHOR>
 */
export class ScaleQuestionResponse implements BaseQuestionResponse {
  /**
   * 量表类型
@see com.fjhb.domain.exam.api.question.consts.ScaleTypes
   */
  scaleType: number
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  startDegree: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写
   */
  endDegree: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
  /**
   * 已作答答案
   */
  scaleAnswer: number
  /**
   * 试题id
   */
  id: string
  /**
   * 大题序号
   */
  groupSequence: number
  /**
   * 试题类型
<p>
说明：
0 表示混合题型，即该大题下存在多种试题类型的组合
1 表示单选题
2 表示多选题
3 表示填空题
4 表示判断题
5 表示简答题
6 表示父子题
7 表示量表题
</p>
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 得分
   */
  score: number
  /**
   * 试题题目
   */
  topic: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题id
   */
  parentQuestionId: string
  /**
   * 题析
   */
  dissects: string
  /**
   * 关联课程id集合
   */
  relateCourseId: Array<string>
  /**
   * 试题难度
   */
  questionDifficulty: number
  /**
   * 是否必答
   */
  answerRequired: boolean
  /**
   * 是否已作答
   */
  answered: boolean
  /**
   * 标签Code集合
   */
  labelCodeList: Array<string>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 复制问卷
   * @param request
   * @return {@link String}
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async copyQuestionnaire(
    request: CopyQuestionnaireRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.copyQuestionnaire,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建发布试卷配置
   * @param request 创建出卷配置请求
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createPaperPublishConfigure(
    request: PaperPublishConfigureCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createPaperPublishConfigure,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建出卷配置分类
   * @param request 创建出卷配置分类请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createPaperPublishConfigureCategory(
    request: CreatePaperPublishConfigureCategoryRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createPaperPublishConfigureCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 停用试卷配置
   * @param id 出卷配置id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disablePaperPublishConfigure(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disablePaperPublishConfigure,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用试卷配置
   * @param id 出卷配置id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enablePaperPublishConfigure(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enablePaperPublishConfigure,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 预览
   * @param paperPublishConfigureId 试卷id【必填】
   * @return 预览试卷响应对象
   * @param mutate 查询 graphql 语法文档
   * @param paperPublishConfigureId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async previewAnswerPaper(
    paperPublishConfigureId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.previewAnswerPaper,
    operation?: string
  ): Promise<Response<PreviewPaperPublishConfigureResponse>> {
    return commonRequestApi<PreviewPaperPublishConfigureResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { paperPublishConfigureId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 删除试卷配置
   * @param id 出卷配置id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removePaperPublishConfigure(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removePaperPublishConfigure,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除出卷配置分类
   * @param id 出卷配置分类id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removePaperPublishConfigureCategory(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removePaperPublishConfigureCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改试卷配置
   * @param request 修改出卷配置请求
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updatePaperPublishConfigure(
    request: PaperPublishConfigureUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updatePaperPublishConfigure,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改出卷配置分类
   * @param request 修改出卷配置分类请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updatePaperPublishConfigureCategory(
    request: UpdatePaperPublishConfigureCategoryRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updatePaperPublishConfigureCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
