import { ExchangeOrderStatusEnum } from '@api/service/management/train-class/query/enum/ExchangeOrderStatusType'

/**
 * @description
 */

class ExchangeOrderStatus {
  /**
   * 状态
   * 1：发起换班
   * 2：退班处理中
   * 3：退班失败
   * 4：申请发货
   * 5：发货处理中
   * 6：换班成功
   */
  status: ExchangeOrderStatusEnum = null

  /**
   * 时间
   */
  date = ''

  constructor(status?: ExchangeOrderStatusEnum) {
    this.status = status || null
  }
}

export default ExchangeOrderStatus
