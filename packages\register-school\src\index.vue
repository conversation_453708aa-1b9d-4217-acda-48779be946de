<route-meta>
  {
  "isMenu": true,
  "title": "开通网校",
  "sort": 1,
  "icon": "icon-ribaotongji"
  }
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="created">
    <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb10">
      配置提示：<br />
      1.本平台开通的网校只适用8.0通用平台提供的标准化功能，若网校功能与通用平台提供的标准化功能有较大差异，请独立建设平台，不适合开通网校。<br />
      2.网校配置成功后不会自动发布，需要网校确认相关功能都已配置完成，才开放网校前端访问。
    </el-alert>
    <div class="f-p15">
      <template v-if="$hasPermission('BasicInfo')" desc="基础信息" actions="@BasicInfo">
        <basic-info ref="base" @childrenThis="getChildren" @getIndutry="getIndutry"></basic-info>
      </template>
      <template v-if="$hasPermission('SchoolConfig')" desc="网校配置" actions="@SchoolConfig">
        <school-config ref="school" @childrenThis="getChildren" @getClient="getClient"></school-config>
      </template>
      <template v-if="$hasPermission('TemplateConfig')" desc="模板配置" actions="@TemplateConfig">
        <template-config
          ref="template"
          @childrenThis="getChildren"
          :TemplateBaseConfig="TemplateBaseConfig"
        ></template-config>
      </template>
      <template v-if="$hasPermission('ManageAccount')" desc="管理员信息" actions="@ManageAccount">
        <manage-account ref="manage" @childrenThis="getChildren"></manage-account>
      </template>
      <template v-if="$hasPermission('AddService')" desc="增值服务" actions="@AddService">
        <add-service ref="add" @childrenThis="getChildren"></add-service>
      </template>
      <div class="m-btn-bar f-tc is-sticky-1" style="z-index: 1001">
        <el-button
          @click="handleJump"
          v-if="$hasPermission('cancel')"
          desc="取消"
          actions="@hbfe/jxjy-admin-management/src/index.vue"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="handleSubmit"
          v-if="$hasPermission('submit')"
          desc="确认开通"
          actions="handleSubmit,@hbfe/jxjy-admin-management/src/index.vue"
          :loading="loading"
          >确认开通</el-button
        >
      </div>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import OnlineSchoolModule from '@api/service/training-institution/online-school/OnlineSchoolModule'
  import OnlineSchoolModel from '@api/service/training-institution/online-school/models/OnlineSchoolModel'
  import BasicInfo from '@hbfe/jxjy-admin-registerSchool/src/components/basic-info.vue'
  import SchoolConfig from '@hbfe/jxjy-admin-registerSchool/src/components/school-config.vue'
  import TemplateConfig from '@hbfe/jxjy-admin-registerSchool/src/components/template-config.vue'
  import ManageAccount from '@hbfe/jxjy-admin-registerSchool/src/components/manage-account.vue'
  import AddService from '@hbfe/jxjy-admin-registerSchool/src/components/add-service.vue'
  import { debounce, bind } from 'lodash-decorators'
  import { AddServiceEnum } from '@api/service/training-institution/online-school/enum/AddServiceEnum'
  import RootModule from '@/store/RootModule'
  class TemplateBaseConfig {
    haveRSIndustry: boolean
    haveJSIndustry: boolean
    haveWSIndustry: boolean
    haveGQIndustry: boolean
    haveLSIndustry: boolean
    haveYSIndustry: boolean
    provideWebService: boolean
    provideH5Service: boolean
  }
  @Component({
    components: { BasicInfo, SchoolConfig, TemplateConfig, ManageAccount, AddService }
  })
  export default class extends Vue {
    onlineSchoolObj: OnlineSchoolModule = new OnlineSchoolModule()
    props = { multiple: true }
    baseChild: any
    schoolChild: any
    templateChild: any
    manageChild: any
    addChild: any
    haveRSIndustry = false
    haveJSIndustry = false
    TemplateBaseConfig: TemplateBaseConfig = {
      haveRSIndustry: false,
      haveJSIndustry: false,
      haveWSIndustry: false,
      haveGQIndustry: false,
      haveLSIndustry: false,
      haveYSIndustry: false,
      provideWebService: false,
      provideH5Service: false
    }
    loading = false
    handleJump() {
      this.$router.push('/school-management/management')
    }
    getIndutry(
      haveRSIndustry: boolean,
      haveJSIndustry: boolean,
      haveWSIndustry: boolean,
      haveGQIndustry: boolean,
      haveLSIndustry: boolean,
      haveYSIndustry: boolean
    ) {
      this.TemplateBaseConfig.haveRSIndustry = haveRSIndustry
      this.TemplateBaseConfig.haveJSIndustry = haveJSIndustry
      this.TemplateBaseConfig.haveWSIndustry = haveWSIndustry
      this.TemplateBaseConfig.haveGQIndustry = haveGQIndustry
      this.TemplateBaseConfig.haveLSIndustry = haveLSIndustry
      this.TemplateBaseConfig.haveYSIndustry = haveYSIndustry
    }
    getClient(provideWebService: boolean, provideH5Service: boolean) {
      this.TemplateBaseConfig.provideWebService = provideWebService
      this.TemplateBaseConfig.provideH5Service = provideH5Service
    }
    getChildren(child: any, type: number) {
      switch (type) {
        case 1:
          this.baseChild = child
          break
        case 2:
          this.schoolChild = child
          break
        case 3:
          this.templateChild = child
          break
        case 4:
          this.manageChild = child
          break
        case 5:
          this.addChild = child
          break
        default:
          break
      }
    }
    @bind
    @debounce(200)
    async handleSubmit() {
      const baseData = await this.baseChild.handleCheck()
      if (!baseData.status) {
        this.$alert(baseData.msg, '提示', {
          type: 'warning'
        })
        return false
      }
      const schoolData = await this.schoolChild.handleCheck()
      if (!schoolData.status) {
        this.$alert(schoolData.msg, '提示', {
          type: 'warning'
        })
        return false
      }
      const templateData = await this.templateChild.handleCheck()
      if (!templateData.status) {
        this.$alert(templateData.msg, '提示', {
          type: 'warning'
        })
        return false
      }
      const manageData = await this.manageChild.handleCheck()
      if (!manageData.status) {
        this.$alert(manageData.msg, '提示', {
          type: 'warning'
        })
        return false
      }
      const addData = await this.addChild.addData
      console.log(addData, 'addData')

      this.onlineSchoolObj.onlineSchool = new OnlineSchoolModel()
      this.onlineSchoolObj.onlineSchool.id = undefined
      this.onlineSchoolObj.onlineSchool.schoolId = undefined
      this.onlineSchoolObj.onlineSchool.schoolBase = baseData.data
      this.onlineSchoolObj.onlineSchool.schoolConfig = schoolData.data
      this.onlineSchoolObj.onlineSchool.schoolConfig.webPortalTemplateId = templateData.data.webPortalTemplateId
      this.onlineSchoolObj.onlineSchool.schoolConfig.H5PortalTemplateId = templateData.data.H5PortalTemplateId
      this.onlineSchoolObj.onlineSchool.administrator = manageData.data
      if (!this.onlineSchoolObj.onlineSchool.schoolBase.haveRSIndustry) {
        this.onlineSchoolObj.onlineSchool.schoolBase.RSIndustry = undefined
      }
      if (!this.onlineSchoolObj.onlineSchool.schoolBase.haveJSIndustry) {
        this.onlineSchoolObj.onlineSchool.schoolBase.JSIndustry = undefined
      }
      this.onlineSchoolObj.onlineSchool.addServiceConfig.addServiceType = addData.addServiceType
      this.onlineSchoolObj.onlineSchool.addServiceConfig.distributionServiceType = addData.distributionServiceType
      this.loading = true
      const addServiceConfig = this.onlineSchoolObj.onlineSchool.addServiceConfig
      const includesFxService = addServiceConfig.addServiceType.includes(AddServiceEnum.fxService)
      const distributionServiceType = addServiceConfig.distributionServiceType

      if (includesFxService && (distributionServiceType == null || isNaN(distributionServiceType))) {
        this.loading = false
        return this.$message.warning('请选择开通的分销服务类型')
      }
      const res: any = await this.onlineSchoolObj.createOnlineSchool()
      if (res.status.code == 200) {
        this.loading = false
        if (res.data.code && res.data.code != 200) {
          switch (res.data.code) {
            case 3000:
              this.$alert('网校到期时间需大于当前时间', '提示', {
                type: 'warning'
              })
              break
            default:
              this.$alert(res.data.message, '提示', {
                type: 'warning'
              })
              break
          }

          return
        }
        this.$message.success('操作成功')
        const path = this.$route.path || ''
        if (path) RootModule.doRemoveNav(path)
        this.$router.push('/school-management/management')
      } else {
        this.loading = false
        this.$alert(res.status.errors[0].message, '提示', {
          type: 'warning'
        })
      }
    }
  }
</script>
