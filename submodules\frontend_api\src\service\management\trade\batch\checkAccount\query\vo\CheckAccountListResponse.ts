/*
 * @Description: 列表转换数据
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-29 15:33:01
 */

import { BatchOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { PaymentMethodEnum } from '@api/service/management/trade/batch/order/enum/PaymentMethod'

export default class CheckAccountListResponse {
  /**
   * 批次单号
   */
  orderId?: string
  /**
   * 交易流水号
   */
  batchId?: string
  /**
   * 交易成功时间
   */
  startDate?: string
  /**
   * 购买人信息 - ID
   */
  userId?: string
  /**
   * 购买人信息 - 购买人
   */
  name?: string
  /**
   * 购买人信息 - 证件号
   */
  idCard?: string
  /**
   * 购买人信息 - 手机号
   */
  phone?: string
  /**
   * 退款金额
   */
  money?: number
  /**
   * 退款人数
   */
  refundCount: number
  /**
   * 销售渠道
   */
  saleChannel: number
  /**
   * 缴费方式
   */
  paymentMethod: PaymentMethodEnum = null
  static from(batchOrderResponse: BatchOrderResponse) {
    const data = new CheckAccountListResponse()
    const {
      batchOrderNo,

      creator: { userId },
      payInfo: { flowNo, paymentOrderType },
      basicData: {
        amount,
        orderForBatchCount,
        saleChannel,
        batchOrderStatusChangeTime: { completed }
      }
    } = batchOrderResponse
    data.orderId = batchOrderNo
    data.batchId = flowNo
    data.userId = userId
    data.money = amount
    data.refundCount = orderForBatchCount
    data.startDate = completed
    data.saleChannel = saleChannel
    data.paymentMethod = paymentOrderType
    return data
  }
  setUserInfo(idCard: string, name: string, phone: string) {
    this.idCard = idCard
    this.name = name
    this.phone = phone
  }
}
