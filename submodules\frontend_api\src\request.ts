import { Response } from '@hbfe/common'
import $http, { Response as res, ResponseError } from '@packages/request'
import { AxiosRequestConfig } from 'axios'
import SchoolServiceIdStrategy from '@api/service/common/diffSchool/SchoolServiceIdStrategy'

export interface ExtraConfig extends AxiosRequestConfig {
  /**
   * 记录配置额外信息
   */
  metadata?: {
    endTime: number
    startTime: number
  }
  // 是否微服务
  isMicroService?: any
  // 服务名称，未必等于 schema 名称
  microServiceName?: string
  schemaName?: string
  // 是否差异化网关
  isDiffService?: boolean
  serviceCapability?: string
  isUnAuthorize?: boolean
}

/**
 * 修改差异化请求的url
 * @param url 请求的地址
 * @param matchFields 各个网校的标记数组
 */
function modifyRequestURL(url: string, matchFields: string[]) {
  const regex = /(.*\/gql\/)(.*)/
  const match = url.match(regex)

  for (const matchField of matchFields) {
    if (match && match[2].includes(matchField)) {
      url = match[1] + matchField + '/' + match[2]
      break // 如果匹配到一个标识，就跳出循环
    }
  }

  return url
}

/**
 * 1. 系统正常就返回对应需要的数据
 * 2. 异常就返回异常 ResponseError
 * @param uri
 * @param params
 * @param config
 */
export default function<T>(uri: string, params: any, config?: ExtraConfig) {
  return new Promise<Response<T>>((resolve, reject) => {
    if (!config.serviceCapability) {
      delete config.serviceCapability
    }
    const response: Response<T> = new Response<T>()
    let host = process.env.VUE_APP_HOST
    // 网校差异化请求打上网校标记
    // uri = modifyRequestURL(uri, ['fjzj'])

    // #ifdef H5
    host = ''
    // #endif
    if (config && config.isMicroService) {
      config.headers = {
        'Graphql-SchemaName': config.schemaName || config.microServiceName
      }
    }

    if (!config.headers) {
      config.headers = {}
    }
    // 平台也要增加这个头部去识别接口调用，如果有带 config.schemaName 才会加进去，不会覆盖别的
    if (!config.headers['Graphql-SchemaName']) {
      if (config.schemaName) {
        config.headers['Graphql-SchemaName'] = config.schemaName
      }
    }
    if (!host) {
      host = ''
    }

    const graphStrMethods = (params.query as string)
      .split('_ALL_')[0]
      .split('{')[1]
      .split('(')[0]
      .match(/[a-zA-Z_][a-zA-Z0-9_]*/g)

    let graphStrMethod = ''
    if (graphStrMethods) {
      if (graphStrMethods.includes('fn0')) {
        // 过滤拼接请求
        graphStrMethod = graphStrMethods[1]
      } else {
        graphStrMethod = graphStrMethods[0]
      }
    }

    let replaceUrl = uri

    if (config?.isDiffService) {
      const curSchool = SchoolServiceIdStrategy.currentSchool()
      replaceUrl += curSchool ? '/' + curSchool : ''
    }

    if (replaceUrl.includes('?')) {
      replaceUrl = replaceUrl + '&method=' + graphStrMethod + '&t=' + new Date().getTime()
    } else {
      replaceUrl = replaceUrl + '?method=' + graphStrMethod + '&t=' + new Date().getTime()
    }

    $http
      //   .post(`${host}${uri}`, params, config)
      .post(`${host}${replaceUrl}`, params, config)
      /* eslint-disable @typescript-eslint/no-explicit-any */
      .then((data: res<any>) => {
        response.status.code = data?.data?.code
        response.status.message = ''
        response.status.errors = data?.data?.errors
        if (data?.data?.code !== 200) {
          resolve(response)
        } else {
          const keys = Object.keys(data?.data?.data)
          if (keys.length > 1) {
            response.data = data?.data?.data
          } else {
            response.data = data?.data?.data[keys[0]]
          }
          resolve(response)
        }
      })
      .catch((e: ResponseError<T>) => {
        console.log('e', e)
        response.status.httpCode = e.response?.status || 0
        Object.assign(response.status, e.response?.data)
        reject(response)
      })
  })
}
