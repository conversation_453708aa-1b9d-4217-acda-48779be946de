import { OfflineCollectiveRegisterStepResponse } from '@api/ms-gateway/ms-servicer-series-v1'

/**
 * @description 线下集体报名步骤
 */
class StepVo {
  /**
   * 顺序
   */
  No: number
  /**
   * 步骤标题
   */
  name: string
  /**
   * 步骤内容
   */
  content: string

  static from(response: OfflineCollectiveRegisterStepResponse) {
    const stepVo = new StepVo()
    stepVo.No = response.no
    stepVo.name = response.name
    stepVo.content = response.content
    return stepVo
  }
}

export default StepVo
