<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">培训监管管理</el-breadcrumb-item>
      <el-breadcrumb-item>新建方案级监管规则</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">监管规则名称</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form">
              <el-form-item label="监管规则名称：" required>
                <el-input v-model="input" placeholder="请输入监管规则名称"></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">监管方案</span>
        </div>
        <el-button type="primary" class="f-mb20">选择方案</el-button>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="方案名称" min-width="240">
            <template>方案名称方案名称方案名称方案名称方案名称方案名称</template>
          </el-table-column>
          <el-table-column label="方案属性" min-width="200">
            <template>
              <p>班级类型：网授班</p>
              <p>培训类别：劳动监管系统人员</p>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">学习监管</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit m-border">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="140px" class="m-form">
              <el-form-item label="监管场景：">进入学习</el-form-item>
              <el-form-item label="监管状态："><el-switch v-model="switch1"></el-switch></el-form-item>
              <el-form-item>
                <div slot="label">
                  监管方式<el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co"></i>
                    <div slot="content">
                      <p>请选择监管方式，若选择多种监管方式将在每个监管点按顺序触发其中的一种</p>
                    </div> </el-tooltip
                  >：
                </div>
                <el-radio v-model="radio" label="1" class="f-mr30">人脸识别</el-radio>
                <el-radio v-model="radio1" label="2" class="f-mr10">系统随机题</el-radio>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="width-limit m-border f-mt15">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="140px" class="m-form">
              <el-form-item label="监管场景：">学习过程</el-form-item>
              <el-form-item label="监管状态："><el-switch v-model="switch1"></el-switch></el-form-item>
              <el-form-item>
                <div slot="label">
                  监管方式<el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co"></i>
                    <div slot="content">
                      <p>请选择监管方式，若选择多种监管方式将在每个监管点按顺序触发其中的一种</p>
                    </div> </el-tooltip
                  >：
                </div>
                <el-radio v-model="radio" label="1" class="f-mr30">人脸识别</el-radio>
                <el-radio v-model="radio1" label="2" class="f-mr10">系统随机题</el-radio>
              </el-form-item>
              <el-form-item>
                <div slot="label">
                  监管频率<el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co"></i>
                    <div slot="content">
                      <p>知识点答题将在试题设置的弹窗时间触发监管频率对知识点答题不生效</p>
                    </div> </el-tooltip
                  >：
                </div>
                <el-radio-group v-model="form.resource">
                  <el-radio v-model="radio1" label="1" style="display: block; line-height: 36px;">
                    间隔<el-input v-model="input" placeholder="请输入" class="f-input-num f-mlr5 f-mb5"></el-input
                    >分钟监管1次，监管通过方可继续学习（间隔时间以课件播放时长计算，课件时长不足设置的间隔分钟，则在课件进度100%时监管）
                  </el-radio>
                  <el-radio v-model="radio1" label="1" class="f-mt10" style="display: block; line-height: 36px;">
                    间隔<el-input v-model="input" placeholder="请输入" class="f-input-num f-mlr5"></el-input
                    >%监管1次，监管通过方可继续学习（间隔进度以课件播放进度计算）
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="重试次数：">
                <el-radio-group v-model="form.resource">
                  <el-radio v-model="radio1" label="1" style="display: block; line-height: 36px;">
                    无限次
                  </el-radio>
                  <el-radio v-model="radio1" label="1" class="f-mt10" style="display: block; line-height: 36px;">
                    重试<el-input v-model="input" placeholder="请输入" class="f-input-num f-mlr5"></el-input
                    >次仍未通过记为监管不通过
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="监管不通过结果：">
                <el-radio-group v-model="form.resource">
                  <el-radio v-model="radio1" label="1" style="display: block; line-height: 36px;">
                    两次监管之间学习进度有效
                  </el-radio>
                  <el-radio v-model="radio1" label="1" class="f-mt10" style="display: block; line-height: 36px;">
                    两次监管之间学习进度无效
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        checkList: [],
        checked1: false,
        checked2: true,
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
