import PlatformJxjypxtyptJxgxSchool, {
  GetStudyCourseNumRequest,
  VerifyPermissionsToCourseRequest,
  VerifyPermissionsToCourseResponse,
  VerifyPersonZcInfoResponse
} from '@api/diff-gateway/platform-jxjypxtypt-jxgx-school'
import TrainClassDetailClassVo from '@api/service/customer/train-class/query/vo/TrainClassDetailClassVo'
import UserModule from '@api/service/customer/user/UserModule'
import PlatformJxjypxtyptJxgxCourseLearningGatewayForestage from '@api/diff-gateway/platform-jxjypxtypt-jxgx-course-learning-gateway-forestage'
import { Response } from '@hbfe/common'

export default class QueryValidation {
  /**
   * 平台名字
   */
  platformName = ''

  /**
   * 课程名字
   */
  courseName = ''

  // 培训班详情
  commodityDetail = new TrainClassDetailClassVo()

  /**
   * 错误信息
   */
  errorMessage = ''
  /**
   * 培训类别（职称系列）
   */
  trainingCategoryId?: string
  /**
   * 培训专业（职称专业）
   */
  trainingProfessionId?: string

  /**
   * 查询是否有开课权限
   */
  async queryIsOpen() {
    const verifyPermissionsToCourseRequest = new VerifyPermissionsToCourseRequest()
    const info = UserModule.queryUserFactory.getQueryUserInfo().userInfo.authenticationList.find(item => {
      return item.identityType === 1
    })
    const studentId = info.identity
    verifyPermissionsToCourseRequest.zczyCode = this.commodityDetail.skuProperty.trainingMajor.skuPropertyValueId
    verifyPermissionsToCourseRequest.zczyName = this.commodityDetail.skuProperty.trainingMajor.skuPropertyName
    verifyPermissionsToCourseRequest.year = this.commodityDetail.skuProperty.year.skuPropertyValueId
    verifyPermissionsToCourseRequest.id = studentId
    const res = await PlatformJxjypxtyptJxgxSchool.verifyPermissionsToCourse(verifyPermissionsToCourseRequest)
    if (res.status.isSuccess()) {
      return res.data
    }
    return new VerifyPermissionsToCourseResponse()
  }

  /**
   * 查询是否有平台在学习
   * @params schemeId 方案id
   * @params courseNo 管理系统课程id
   */
  async queryIsLearning(schemeId: string) {
    const res = await this.verifyStudyStatus()
    const courseNo = res.data?.commodityId
    this.platformName = res.data?.platformName
    this.courseName = res.data?.CommodityName
    let getJXGXStudentSchemeId = ''
    if (courseNo) {
      getJXGXStudentSchemeId = await this.getJXGXStudentScheme(courseNo)
    }
    if (res.status.isSuccess()) {
      this.errorMessage = res.data?.msg
      return res.data?.code !== 1 && getJXGXStudentSchemeId !== schemeId
    }
    this.errorMessage = '系统异常'
    return true
  }

  /**
   * 验证是否正在其他平台学习
   */
  async verifyStudyStatus() {
    const info = UserModule.queryUserFactory.getQueryUserInfo().userInfo.authenticationList.find(item => {
      return item.identityType === 1
    })
    return await PlatformJxjypxtyptJxgxSchool.verifyStudyStatus(info.identity)
  }

  /**
   * 获取课程
   * @param id 管理系统课程编号
   */
  async getJXGXStudentScheme(id: string) {
    const { data, status } = await PlatformJxjypxtyptJxgxCourseLearningGatewayForestage.getJXGXStudentScheme(id)
    if (status.isSuccess()) {
      return data
    }
    return ''
  }

  /**
   * 校验学员现职称与课程系列是否一致
   * @param studentId 学员ID
   * @param schemeId 方案skuID
   */
  async verifyIsConsistent(schemeId: string) {
    const info = UserModule.queryUserFactory.getQueryUserInfo().userInfo.authenticationList.find(item => {
      return item.identityType === 1
    })
    const studentId = info.identity
    const res = await PlatformJxjypxtyptJxgxSchool.verifyPersonZcInfo({ studentId, schemeId })
    if (res.status.isSuccess()) {
      this.errorMessage = res.data?.msg
      this.trainingCategoryId = res.data?.studentProfessionInfo.trainingCategoryId
      this.trainingProfessionId = res.data?.studentProfessionInfo.trainingProfessionId
      return res.data.validResult
    }
    this.errorMessage = '系统异常'
    return false
  }

  /**
   * 获取学时
   */
  async getJXGXStudyCourseNum(year?: string) {
    const request = new GetStudyCourseNumRequest()
    const info = UserModule.queryUserFactory.getQueryUserInfo().userInfo.authenticationList.find(item => {
      return item.identityType === 1
    })
    request.studentId = info.identity
    request.trainingProfessionId = this.trainingProfessionId
    request.trainingCategoryId = this.trainingCategoryId
    request.year = year || String(new Date().getFullYear())
    const res = await PlatformJxjypxtyptJxgxSchool.getStudyCourseNum(request)
    if (res.status.isSuccess()) {
      this.errorMessage = res.data?.msg
      return res.data
    }
    this.errorMessage = '系统异常'
    return res.data
  }
}
