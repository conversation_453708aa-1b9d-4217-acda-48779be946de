import { CourseLearningAssessSettingResponse, PackageRuleSettingDto } from '@api/gateway/PlatformLearningScheme'

export class CourseLearning {
  /**
   * 学习方案要求的最少学时
   */
  minTotalPeriod: number
  /**
   * 必修选课学时
   */
  compulsoryPeriod: number
  /**
   * 选修要求学时
   */
  optionalPeriod: number

  /**
   * 课程考核要求
   */
  assessSetting: CourseLearningAssessSettingResponse
  learningId: string
  enabled: boolean
  compulsoryPackages: Array<PackageRuleSettingDto>
  optionalPackages: Array<PackageRuleSettingDto>
}
