schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""分页查询登录日志
		@param page    分页条件
		@param request 查询条件
		@return 分页数据
	"""
	pageLoginLogList(page:Page,request:LoginLogRequest):LoginLogResponsePage @page(for:"LoginLogResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
"""登录日志查询条件"""
input LoginLogRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.loginlog.gateway.graphql.request.LoginLogRequest") {
	"""用户名"""
	userName:String
	"""用户ID列表"""
	userIdList:[String]
	"""事件类型<br/>
		SSOLoginEvent登录 SSOLogoutEvent登出 SSOReAuthLoginEvent重授权
	"""
	eventType:String
	"""IP地址"""
	ipList:[String]
	"""事件时间"""
	eventTime:DateScopeRequest
}
"""登录日志响应体"""
type LoginLogResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.loginlog.gateway.graphql.response.LoginLogResponse") {
	"""主键ID"""
	id:String
	"""账户id"""
	accountId:String
	"""用户id"""
	userId:String
	"""用户名"""
	userName:String
	"""事件类型"""
	eventType:String
	"""事件时间"""
	eventTime:DateTime
	"""登录ip"""
	loginIp:String
	"""登录类型"""
	clientType:String
	"""登录时间，仅当登录/重授权事件时有值"""
	loginTime:DateTime
	"""登出时间，仅当登出事件时有值"""
	logoutTime:DateTime
}

scalar List
type LoginLogResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LoginLogResponse]}
