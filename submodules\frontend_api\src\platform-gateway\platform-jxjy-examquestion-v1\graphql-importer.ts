import copyQuestionnaire from './mutates/copyQuestionnaire.graphql'
import createQuestionnaireQuestion from './mutates/createQuestionnaireQuestion.graphql'
import createTeacherEvaluationMultipleChoiceQuestionnaireQuestion from './mutates/createTeacherEvaluationMultipleChoiceQuestionnaireQuestion.graphql'
import createTeacherEvaluationSingleChoiceQuestionnaireQuestion from './mutates/createTeacherEvaluationSingleChoiceQuestionnaireQuestion.graphql'
import removeQuestionnaireQuestion from './mutates/removeQuestionnaireQuestion.graphql'
import updateQuestionnaireQuestion from './mutates/updateQuestionnaireQuestion.graphql'
import updateTeacherEvaluationMultipleChoiceQuestionnaireQuestion from './mutates/updateTeacherEvaluationMultipleChoiceQuestionnaireQuestion.graphql'
import updateTeacherEvaluationSingleChoiceQuestionnaireQuestion from './mutates/updateTeacherEvaluationSingleChoiceQuestionnaireQuestion.graphql'

export {
  copyQuestionnaire,
  createQuestionnaireQuestion,
  createTeacherEvaluationMultipleChoiceQuestionnaireQuestion,
  createTeacherEvaluationSingleChoiceQuestionnaireQuestion,
  removeQuestionnaireQuestion,
  updateQuestionnaireQuestion,
  updateTeacherEvaluationMultipleChoiceQuestionnaireQuestion,
  updateTeacherEvaluationSingleChoiceQuestionnaireQuestion
}
