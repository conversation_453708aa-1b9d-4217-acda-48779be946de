import findChannelVendorPageForTrainingInstitution from './queries/findChannelVendorPageForTrainingInstitution.graphql'
import findChannelVendorPageForTrainingInstitutionNew from './queries/findChannelVendorPageForTrainingInstitutionNew.graphql'
import findCoursewareSupplierPage from './queries/findCoursewareSupplierPage.graphql'
import findCoursewareSupplierPageForTrainingInstitution from './queries/findCoursewareSupplierPageForTrainingInstitution.graphql'
import findCoursewareSupplierPageForTrainingInstitutionNew from './queries/findCoursewareSupplierPageForTrainingInstitutionNew.graphql'
import findParticipatingUnitForTrainingInstitution from './queries/findParticipatingUnitForTrainingInstitution.graphql'
import findRelationServiceListByType from './queries/findRelationServiceListByType.graphql'
import findServicerListByType from './queries/findServicerListByType.graphql'
import findTrainingInstitutionPage from './queries/findTrainingInstitutionPage.graphql'
import findTrainingInstitutionPageForChannelVendor from './queries/findTrainingInstitutionPageForChannelVendor.graphql'
import findTrainingInstitutionPageForCoursewareSupplier from './queries/findTrainingInstitutionPageForCoursewareSupplier.graphql'
import findTrainingInstitutionPageForParticipatingUnit from './queries/findTrainingInstitutionPageForParticipatingUnit.graphql'
import getCurrentUserServicer from './queries/getCurrentUserServicer.graphql'
import list from './queries/list.graphql'
import createSpecialTopicConfig from './mutates/createSpecialTopicConfig.graphql'
import resumeServicerContract from './mutates/resumeServicerContract.graphql'
import signUpServicerContract from './mutates/signUpServicerContract.graphql'
import suspendServicerContract from './mutates/suspendServicerContract.graphql'

export {
  findChannelVendorPageForTrainingInstitution,
  findChannelVendorPageForTrainingInstitutionNew,
  findCoursewareSupplierPage,
  findCoursewareSupplierPageForTrainingInstitution,
  findCoursewareSupplierPageForTrainingInstitutionNew,
  findParticipatingUnitForTrainingInstitution,
  findRelationServiceListByType,
  findServicerListByType,
  findTrainingInstitutionPage,
  findTrainingInstitutionPageForChannelVendor,
  findTrainingInstitutionPageForCoursewareSupplier,
  findTrainingInstitutionPageForParticipatingUnit,
  getCurrentUserServicer,
  list,
  createSpecialTopicConfig,
  resumeServicerContract,
  signUpServicerContract,
  suspendServicerContract
}
