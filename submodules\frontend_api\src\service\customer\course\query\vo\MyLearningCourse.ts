import {
  CourseOfCourseTrainingOutlineInfo,
  StudentCourseAppraised,
  StudentCourseLearningResponse,
  StudentCourseLearningV2Response,
  StudentCourseMediaLearningRecord,
  StudentInfo
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import CourseDetail from '@api/service/customer/course/query/vo/CourseDetail'
import CourseAssessment from '@api/service/customer/course/query/vo/CourseAssessment'
import Evaluation from '@api/service/customer/course/query/vo/Evaluation'
import StudentAfterCourseTest from '@api/service/customer/course/query/vo/StudentAfterCourseTest'
import StudentCourseDetail from '@api/service/customer/course/query/vo/StudentCourseDetail'
import CourseMediaLearningRecord from '@api/service/customer/course/query/vo/CourseMediaLearningRecord'

/**
 * 我的学习课程
 */
class MyLearningCourse {
  detail: CourseDetail = new CourseDetail()
  // 课程考核结果
  assessment: CourseAssessment
  // 是否存在课后测验
  hasTest: boolean
  // 是否存在课程心得
  hasExperience: boolean = null
  // 学习心得id
  experienceId = ''
  // 课程评价信息
  evaluation: Evaluation

  /**
   * 课程学习大纲下课程配置信息
   */
  courseOfCourseTrainingOutline: CourseOfCourseTrainingOutlineInfo

  /**
   * 所属学员信息
   */
  student: StudentInfo

  /**
   * 学员课程信息
   */
  studentCourseDetail: StudentCourseDetail

  /**
   * 学员媒体课程学习信息
   */
  studentCourseMediaLearningRecord: StudentCourseMediaLearningRecord

  /**
   * 学员课后测验
   */
  studentAfterCourseTest: StudentAfterCourseTest

  /**
   * 学员课程已评价信息
   */
  studentCourseAppraised: StudentCourseAppraised

  isQualified() {
    return this.studentAfterCourseTest.isQualified() && this.studentCourseDetail.isQualified()
  }

  /**
   * 选课规则转换
   * @param response
   */
  static from(response: StudentCourseLearningV2Response | StudentCourseLearningResponse): MyLearningCourse {
    const course = new MyLearningCourse()
    course.detail.id = response.course.courseId
    course.detail.name = response.course.courseName
    response.course.teacherNames && course.detail.teacherNames.push(...response.course.teacherNames)
    // 学员信息
    course.student = new StudentInfo()
    Object.assign(course.student, response.student)

    // 学员课程
    course.studentCourseDetail = StudentCourseDetail.from(response.studentCourse)

    course.courseOfCourseTrainingOutline = response.courseOfCourseTrainingOutline

    // 课后测验
    course.studentAfterCourseTest = StudentAfterCourseTest.from(response.studentCourseQuiz)

    // 课件学习信息
    course.studentCourseMediaLearningRecord = CourseMediaLearningRecord.from(response.studentCourseMediaLearningRecord)
    course.evaluation = Evaluation.from(response.studentCourseAppraised)

    course.hasTest = !!course.studentAfterCourseTest?.id
    // 学习心得
    course.hasExperience = response.hasLearningExperienceTopic
    // 获取课程信息
    return course
  }

  static fromAutoChoose(response: StudentCourseLearningResponse): MyLearningCourse {
    const course = new MyLearningCourse()
    course.detail.id = response.course.courseId
    // 学员信息
    course.student = new StudentInfo()
    Object.assign(course.student, response.student)

    // 学员课程
    course.studentCourseDetail = StudentCourseDetail.from(response.studentCourse)

    course.courseOfCourseTrainingOutline = response.courseOfCourseTrainingOutline

    // 课后测验
    course.studentAfterCourseTest = StudentAfterCourseTest.from(response.studentCourseQuiz)

    // 课件学习信息
    course.studentCourseMediaLearningRecord = CourseMediaLearningRecord.from(response.studentCourseMediaLearningRecord)
    course.evaluation = Evaluation.from(response.studentCourseAppraised)

    course.hasTest = !!course.studentAfterCourseTest?.id
    course.hasExperience = response.hasLearningExperienceTopic

    // 获取课程信息
    return course
  }

  /**
   * 是否可以进行测验
   */
  async goTest() {
    return false
  }

  /**
   * 准备去播放
   */
  async goContinueLearning() {
    const isOnRefund = await this.isOnRefund()
    console.log(isOnRefund)
    const isOnTraining = await this.isOnTraining()
    console.log(isOnTraining)
    return false
  }

  /**
   * 验证课程是否正在退款中
   * @private
   */
  private async isOnRefund() {
    // 班级退款中，无法继续学习
    return false
  }

  /**
   * 验证课程是否在培训时间段内
   * @private
   */
  private async isOnTraining() {
    // 不在培训时段内，无法学习
    return false
  }

  /**
   * 校准课程的学习进度
   * @private
   */
  private async queryCourseSchedule() {
    // todo
  }

  /**
   * 查询课程测验是否合格
   * @private
   */
  private async queryTestIsQualified() {
    return false
  }

  /**
   * 验证可以测验的条件
   * @private
   */
  private validateTestConditionRule() {
    return
  }
}

export default MyLearningCourse
