schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	findImportOpenedSubTaskPage(page:Page,query:ImportOpenedSubTaskQueryRequest):ImportOpenedSubTaskResponsePage @page(for:"ImportOpenedSubTaskResponse")
	findImportOpenedTaskPage(page:Page,query:TaskQueryRequest):TaskResponsePage @page(for:"TaskResponse")
}
type Mutation {
	createImportOpenedTask(createInfo:ImportOpenedTaskCreateRequest):Void
	"""导致一个导入开通任务的结果
		@param taskId 主任务ID
		@param status 状态集合
		@return 下载路径
	"""
	exportImportOpenedSubTask(taskId:String,status:[Int]):String
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input ImportOpenedSubTaskQueryRequest @type(value:"com.fjhb.fjszyws.platform.gateway.graphql.resolver.request.ImportOpenedSubTaskQueryRequest") {
	"""手机号"""
	phone:String
	"""用户名"""
	userName:String
	"""学习方案名称"""
	schemeName:String
	"""期别名称"""
	issueName:String
	"""主任务ID"""
	taskId:String
	"""子任务状态集合
		@see com.fjhb.platform.component.task.constants.SubTaskStatusConst
	"""
	statusList:[Int]
	"""创建时间 开始时间查询"""
	startCreateTime:DateTime
	"""创建时间 结束时间查询"""
	endCreateTime:DateTime
}
"""导入开通任务创建信息"""
input ImportOpenedTaskCreateRequest @type(value:"com.fjhb.fjszyws.platform.gateway.graphql.resolver.request.ImportOpenedTaskCreateRequest") {
	"""任务名称"""
	name:String
	"""文件路径"""
	filePath:String
	"""密码默认类型
		1： 000000
		2: 身份证后6位
		3: 自定义密码
	"""
	pwdType:Int!
	"""自定义密码"""
	password:String
}
"""导入开通主任务查询信息"""
input TaskQueryRequest @type(value:"com.fjhb.fjszyws.platform.gateway.graphql.resolver.request.TaskQueryRequest") {
	"""主任务状态集合"""
	statusList:[Int]
	"""创建时间 开始时间查询"""
	startCreateTime:DateTime
	"""创建时间 结束时间查询"""
	endCreateTime:DateTime
	"""任务的类型"""
	taskType:TaskTypeEnum
	"""批次号"""
	batchNo:String
}
"""导入开通子任务"""
type ImportOpenedSubTaskResponse @type(value:"com.fjhb.fjszyws.platform.gateway.graphql.resolver.response.ImportOpenedSubTaskResponse") {
	"""子任务ID"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""主任务ID"""
	taskId:String
	"""子任务状态
		@see com.fjhb.platform.component.task.constants.SubTaskStatusConst
	"""
	status:Int!
	"""子任务执行步骤"""
	executedStep:Int!
	"""子任务执行开始时间"""
	startTime:String
	"""子任务执行完成时间"""
	completedTime:String
	"""创建人ID"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""失败信息"""
	failMessage:String
	"""用户名称"""
	userName:String
	"""用户手机"""
	phone:String
	"""开通的学习方案名称"""
	schemeName:String
	"""开通的期数名称"""
	issueName:String
	"""是否为补考用户"""
	makeUpExam:String
	"""培训机构名称"""
	teachUnitName:String
	"""省（用户）"""
	province:String
	"""市（用户）"""
	city:String
	"""县（用户）"""
	county:String
	"""用户ID"""
	userId:String
	"""培训机构ID"""
	teachUnitId:String
	"""销售渠道ID"""
	MarketingChannelId:String
	"""订单号"""
	orderNo:String
	"""用户身份证号码"""
	identity:String
}
"""导入开通任务执行结果信息"""
type ResultResponse @type(value:"com.fjhb.fjszyws.platform.gateway.graphql.resolver.response.ResultResponse") {
	"""子任务总数"""
	totalCount:Long!
	"""处于创建状态的子任务数量"""
	createdStatusCount:Long!
	"""处于执行中状态的子任务数量"""
	executingStatusCount:Long!
	"""处于执行完成状态的子任务数量"""
	executedCount:Long!
	"""处于执行失败状态的子任务数量"""
	executeFailedCount:Long!
	"""执行到第一步完成的任务的数量"""
	executeStepOneCount:Long!
}
"""导入开通主任务"""
type TaskResponse @type(value:"com.fjhb.fjszyws.platform.gateway.graphql.resolver.response.TaskResponse") {
	"""主任务ID"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""任务名称"""
	name:String
	"""任务状态
		@see com.fjhb.platform.component.task.constants.TaskStatusConst
	"""
	status:Int!
	"""任务执行开始时间"""
	startTime:DateTime
	"""任务执行完成时间"""
	completedTime:DateTime
	"""创建人ID"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""失败信息"""
	failMessage:String
	"""excel 文件路径"""
	filePath:String
	"""执行结果信息"""
	result:ResultResponse
}
"""任务类型枚举
	<AUTHOR> create 2020/8/17 20:19
"""
enum TaskTypeEnum @type(value:"com.fjhb.fjszyws.platform.service.support.enums.TaskTypeEnum") {
	IMPORT_TASK
	COLLECTIVE_REGISTER
}

scalar List
type ImportOpenedSubTaskResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ImportOpenedSubTaskResponse]}
type TaskResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TaskResponse]}
