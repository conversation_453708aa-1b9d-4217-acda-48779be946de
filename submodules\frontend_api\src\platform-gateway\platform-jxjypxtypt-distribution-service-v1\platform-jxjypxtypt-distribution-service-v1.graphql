schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取指定服务商id是否开启分销服务
		@param request
		@return
	"""
	getDistributionService(request:GetDistributionServiceRequest):GetDistributionServiceResponse @optionalLogin
}
type Mutation {
	"""开通分销服务
		@param request
		@return
	"""
	openDistributionService(request:OpenDistributionServiceRequest):OpenDistributionServiceResponse @optionalLogin
}
input GetDistributionServiceRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.GetDistributionServiceRequest") {
	"""服务商id【必填】"""
	servicerId:String!
}
input OpenDistributionServiceRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.OpenDistributionServiceRequest") {
	"""网校 服务商id"""
	servicerId:String
	key:String
}
type GetDistributionServiceResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.GetDistributionServiceResponse") {
	"""是否开启分销服务"""
	distributionService:Boolean
	"""分销服务类型
		@see com.fjhb.domain.basicdata.api.servicercontract.consts.DistributionServiceType
		基础版 0（默认） 专业版 1
	"""
	distributionServiceType:Int
}
type OpenDistributionServiceResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distribution.OpenDistributionServiceResponse") {
	"""供应商定制角色id"""
	customGysRoleId:String
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
