"""独立部署的微服务,K8S服务名:ms-data-export-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出专题列表
		@param request
		@return
	"""
	exportTrainingChannel(request:TrainingChannelRequest):Boolean!
}
input TrainingChannelSortKParam @type(value:"com.fjhb.ms.basicdata.query.kernel.service.param.account.nested.TrainingChannelSortKParam") {
	sortField:TrainingChannelEnum
	sortType:SortTypeEnum
}
input DateScopeRequest @type(value:"com.fjhb.ms.basicdata.repository.DateScopeRequest") {
	beginTime:DateTime
	endTime:DateTime
}
input TrainingChannelRequest @type(value:"com.fjhb.ms.data.export.front.gateway.jxjy.graphql.basicdata.request.TrainingChannelRequest") {
	"""专题名称"""
	name:String
	"""专题入口名称"""
	entryName:String
	"""专题类型
		AREA = 1; 地区
		INDUSTRY = 2；行业
		com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.TrainingChannelTypes
	"""
	type:Int
	"""行业id"""
	industryId:String
	"""地区路径"""
	regionPath:String
	"""启用状态（0：停用 1：启用）"""
	enable:Boolean
	"""是否显示在网校"""
	showOnNetSchool:Boolean
	"""排序"""
	sort:Int
	"""编辑时间范围"""
	createdDateScope:DateScopeRequest
	"""排序"""
	sortList:[TrainingChannelSortKParam]
}
enum SortTypeEnum @type(value:"com.fjhb.ms.basicdata.enums.SortTypeEnum") {
	ASC
	DESC
}
enum TrainingChannelEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.TrainingChannelEnum") {
	publishedTime
}

scalar List
