import { UserInfoRequest } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

export default class EducationalManageParam {
  /**
   * 名称
   */
  name: string = undefined

  /**
   * 身份证号
   */
  idCard: string = undefined

  /**
   * 手机号
   */
  phone: string = undefined

  toRequest() {
    const request = new UserInfoRequest()
    request.idCard = this.idCard
    request.name = this.name
    request.phoneNumber = this.phone
    return request
  }
}
