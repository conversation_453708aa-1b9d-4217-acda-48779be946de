import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-teachingplan-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-teachingplan-attendance-sds-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 获取服务器时间
<AUTHOR>
 */
export class GetServerTimeRequest {
  studentLearningToken: string
  planId: string
}

/**
 * 获取服务器时间
<AUTHOR>
 */
export class GetServerTimeWithStudentNoRequest {
  /**
   * 学生学号
   */
  studentNo: string
  /**
   * 期别ID
   */
  issueId: string
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 教学计划学习方式ID, 没有值时请提供-1
   */
  learningId: string
  /**
   * 教学计划ID, 没有值时请提供 -1
   */
  planId: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getServerTime(
    request: GetServerTimeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getServerTime,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getServerTimeWithStudentNo(
    request: GetServerTimeWithStudentNoRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getServerTimeWithStudentNo,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
