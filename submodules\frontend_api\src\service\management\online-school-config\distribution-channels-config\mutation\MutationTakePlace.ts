import { ResponseStatus } from '@hbfe/common'
import MsOfflineinvoiceV1 from '@api/ms-gateway/ms-offlineinvoice-v1'

class MutationTakePlace {
  /**
   * 自取点id
   */
  private readonly takePlaceId: string

  constructor(takePlaceId: string) {
    this.takePlaceId = takePlaceId
  }

  async doEnable(): Promise<ResponseStatus> {
    const { status } = await MsOfflineinvoiceV1.updateChannelStatus({
      channelId: this.takePlaceId,
      enable: true
    })
    return status
  }

  async doDisable(): Promise<ResponseStatus> {
    const { status } = await MsOfflineinvoiceV1.updateChannelStatus({
      channelId: this.takePlaceId,
      enable: false
    })
    return status
  }

  async doRemove(): Promise<ResponseStatus> {
    const { status } = await MsOfflineinvoiceV1.deleteChannel({ channelId: this.takePlaceId })
    return status
  }
}

export default MutationTakePlace
