import { UiPage } from '@hbfe/common'
import msTradeQueryFrontGatewayTradeQueryBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ReceiveAccountVo from './vo/ReceiveAccountVo'
class QueryReceiveAccount {
  /**
   * 收款账户管理-列表
   * @param page
   */
  async queryPage(page: UiPage): Promise<Array<ReceiveAccountVo>> {
    const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.pageReceiveAccountInServicer({ page: page })
    if (msRes.status.isSuccess()) {
      page.totalSize = msRes.data.totalSize
      page.totalPageSize = msRes.data.totalPageSize
      return msRes.data.currentPageData?.map(ReceiveAccountVo.from)
    }
    return new Array<ReceiveAccountVo>()
  }

  /**
   * 收款账户管理-列表（分销）
   * @param page
   */
  async queryFxPage(page: UiPage): Promise<Array<ReceiveAccountVo>> {
    const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.pageReceiveAccountInDistribution({ page: page })
    if (msRes.status.isSuccess()) {
      page.totalSize = msRes.data.totalSize
      page.totalPageSize = msRes.data.totalPageSize
      return msRes.data.currentPageData?.map(ReceiveAccountVo.from)
    }
    return new Array<ReceiveAccountVo>()
  }
}
export default QueryReceiveAccount
