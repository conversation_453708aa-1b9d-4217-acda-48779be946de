import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-studentlearning-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-studentlearning-repair-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 学习成果重新获得请求
<AUTHOR> By Cb
@since 2024/6/5 17:25
 */
export class LearningResultRetryGainRequest {
  /**
   * 学习方案ID
   */
  learningSchemeId?: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 重试learningResultCommitCreatedEventRetry
   * @return java.lang.String
   * <AUTHOR> By Cb
   * @date 2023/9/11 16:51
   * @param mutate 查询 graphql 语法文档
   * @param payload 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningResultCommitCreatedEventRetry(
    payload: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.learningResultCommitCreatedEventRetry,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { payload },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重试learningResultCommitRemovedEventEventRetry
   * @return java.lang.String
   * <AUTHOR> By Cb
   * @date 2023/9/11 16:51
   * @param mutate 查询 graphql 语法文档
   * @param payload 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningResultCommitRemovedEventEventRetry(
    payload: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.learningResultCommitRemovedEventEventRetry,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { payload },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重试learningResultCommitUpdatedEventEvent
   * @return java.lang.String
   * <AUTHOR> By Cb
   * @date 2023/9/11 16:51
   * @param mutate 查询 graphql 语法文档
   * @param payload 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningResultCommitUpdatedEventEventRetry(
    payload: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.learningResultCommitUpdatedEventEventRetry,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { payload },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学习成果 - 用户考核指标合格事件处理
   * @return java.lang.String
   * <AUTHOR> By Cb
   * @date 2023/9/11 16:51
   * @param mutate 查询 graphql 语法文档
   * @param payload 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningResultRelateUserAssessIndicatorQualifiedEventHandle(
    payload: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.learningResultRelateUserAssessIndicatorQualifiedEventHandle,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { payload },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学习成果重试获取
   * @param request:
   * @return {@link String}
   * <AUTHOR> By Cb
   * @since 2024/6/5 17:35
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningResultRetryGain(
    request: LearningResultRetryGainRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.learningResultRetryGain,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取指定成果ID
   * @param mutate 查询 graphql 语法文档
   * @param learningResultId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async reGainLearningResult(
    learningResultId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.reGainLearningResult,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { learningResultId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据学生学号列表和学习成果ID，重新获取对应的学生学习成果记录。
   * @param studentNos       需要重新获取学习成果的学生学号列表
   * @param learningResultId 要重新获取的学习成果唯一标识ID
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async reGainStudentsLearningResult(
    params: { studentNos?: Array<string>; learningResultId?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.reGainStudentsLearningResult,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重试发送事件
   * @param aggregateIdentifier 聚合根的唯一标识符
   * @param globalIndex         事件的全局索引，用于定位特定事件
   * @param className           预期事件对象的类全限定名
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async retrySendEvent(
    params: { aggregateIdentifier?: string; globalIndex: number; className?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.retrySendEvent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 撤销指定学习成果及其关联的用户学习记录
   * @param learningResultId 要撤销的学习成果的唯一标识符
   * @param mutate 查询 graphql 语法文档
   * @param learningResultId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async revokeLearningResult(
    learningResultId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.revokeLearningResult,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { learningResultId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据学生学号列表和学习成果ID，撤销对应的学生学习成果记录。
   * @param studentNos       需要撤销学习成果的学生学号列表
   * @param learningResultId 要撤销的学习成果唯一标识ID
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async revokeStudentsLearningResult(
    params: { studentNos?: Array<string>; learningResultId?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.revokeStudentsLearningResult,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 参训人员证书成果撤回
   * @param schemeId
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async traineesAchievementRevocation(
    params: { schemeId?: string; time?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.traineesAchievementRevocation,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
