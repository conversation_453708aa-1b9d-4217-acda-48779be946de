import ICaptchaAction from '@api/service/common/captcha/interface/ICaptchaAction'
import ApplyPicCaptchaResultVo from '@api/service/common/captcha/mutation/pic-captcha/vo/ApplyPicCaptchaResultVo'
import Mockjs from 'mockjs'
import { ResponseStatus, Response } from '@hbfe/common'

/**
 * @description 图形验证业务动作类
 */
class PicCaptchaAction implements ICaptchaAction<Response<ApplyPicCaptchaResultVo>, string> {
  /**
   * 获取图形验证码
   * @param
   * @return {ApplyPicCaptchaResultVo}
   */
  async doApplyCaptcha(): Promise<Response<ApplyPicCaptchaResultVo>> {
    const result = new Response<ApplyPicCaptchaResultVo>()
    result.status.code = 200
    result.status.message = ''
    result.data.captcha = Mockjs.Random.guid()
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(result)
      }, 1000)
    })
  }

  /**
   * 校验用户输入的验证码
   * @param {string} value - 用户输入的验证码
   * @return {ResponseStatus}
   */
  async doValidateCaptchaValue(value: string): Promise<ResponseStatus> {
    console.log('captchaValue', value)
    const result = new ResponseStatus(200, '')
    return new Promise(resolve => {
      setTimeout(() => {
        resolve(result)
      }, 1000)
    })
  }
}

export default PicCaptchaAction
