"""独立部署的微服务,K8S服务名:ms-collectivesign-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""异步导出提交集体报名失败数据
		@param collectiveSignupNo 集体报名编号
		@return 导出数据ID
	"""
	asyncExportCollectiveSignupExcuteFailExcel(collectiveSignupNo:String!):String
	"""报名数据excel解析进度
		@param collectiveSignupNo 集体报名编号
		@return 报名数据解析响应
	"""
	collectiveSignupDataAnalysis(collectiveSignupNo:String!):SignupDataAnalysisResponse
	"""提交后报名数据执行进度
		@param collectiveSignupNo 集体报名编号
		@return 报名数据执行响应
	"""
	collectiveSignupDataProcess(collectiveSignupNo:String!):SignupDataProcessResponse
	"""导出导入集体报名失败数据
		@param collectiveSignupNo 集体报名编号
		@return excel文件路径
	"""
	exportCollectiveSignupImportFailExcel(collectiveSignupNo:String!):String
	"""查询当前网校下的数据架构
		@return 数据架构结构列表
	"""
	findCollectiveSignupMetaSchema:SchemaResponse
	findCommitCompleteAndFailSubTuskFailDataByPage(request:CollectiveSignQueryRequest,page:Page!):MetaRowPage @page(for:"MetaRow")
	findCommitCompleteAndSuccessSubTuskSuccessDataByPage(request:CollectiveSignQueryRequest,page:Page!):MetaRowPage @page(for:"MetaRow")
	"""属性名分组获取数量
		@param request 属性名
		@return 属性名分组获取数量响应对象集合
	"""
	findCountGroupByKey(request:FindCountGroupByKeyRequest):[KeyGroupResponse]
	findImportCollectiveSignupCompleteSuccessDataByPage(request:CollectiveSignQueryRequest,page:Page!):MetaRowPage @page(for:"MetaRow")
	findImportCollectiveSignupFailDataByPage(request:CollectiveSignQueryRequest,page:Page!):MetaRowPage @page(for:"MetaRow")
	"""报名数据excel最新主任务情况
		@param collectiveSignupNo 集体报名编号
		@return 报名数据解析响应
	"""
	findLastRecordByBatch(collectiveSignupNo:String!):CollectiveSignTaskInfoResponse
	findTaskExecuteResponsePage(collectiveSignupNo:String!,page:Page!):TaskExecuteResponsePage @page(for:"TaskExecuteResponse")
	"""查询模板地址"""
	queryTemplatePathByCategory(request:CollectiveSignUpModelQueryRequest):String
}
type Mutation {
	"""清空失败数据
		@param collectiveSignupNo 集体报名编号
	"""
	clearFailureData(collectiveSignupNo:String!):Void
	"""提交集体报名
		@param collectiveSignupNo 集体报名编号
	"""
	commitCollectiveSignup(collectiveSignupNo:String!):Void
	"""删除集体报名
		@param collectiveSignupNo 集体报名编号
	"""
	deleteCollectiveSignup(collectiveSignupNo:String!):Void
	"""删除报名数据
		@param collectiveSignupNo 集体报名编号
		@param subTaskId          子任务id
	"""
	deleteSignupData(collectiveSignupNo:String!,subTaskId:String!):Void
	"""导入集体缴费报名，目前江苏工考项目，国资人才项目在用
		后续如果需要返回错误码，这个接口弃用，改为importCollectiveSignupForCompatible或importCollectiveSignupForVerify
		@param importRequest 导入信息
		@return 集体报名编号
	"""
	importCollectiveSignup(importRequest:CollectiveSignImportInfoRequest):String
	"""导入集体缴费报名，闽西平台使用，不包含密码优化版本入参
		@param importRequest 导入信息
		@return 集体报名编号
	"""
	importCollectiveSignupForCompatible(importRequest:CollectiveSignImportInfoRequest):ImportCollectiveSignupResponse
	"""导入集体缴费报名，通用平台使用，包含密码优化版本
		@param importRequest 导入信息
		@return 集体报名编号
	"""
	importCollectiveSignupForVerify(importRequest:CollectiveSignImportInfoForVerifyRequest):ImportCollectiveSignupResponse
	"""集体缴费重新报名
		@param originalCollectiveSignupNo 指定需要重新报名的集体缴费报名编号
		@return 新生成的集体缴费报名编号
	"""
	signupByOriginalCollectiveSignup(originalCollectiveSignupNo:String!):String
	"""集体缴费重新报名
		@param request 请求
		@return 新生成的集体缴费报名编号
	"""
	signupByOriginalCollectiveSignupWithInfo(request:CollectiveSignUpByOriginalCollectiveSignupRequest):String
	"""修改报名数据
		@param collectiveSignupNo 集体报名编号
		@param signupDataList     一批的报名数据修改
	"""
	updateSignupData(collectiveSignupNo:String!,signupDataList:[UpdateSignupDataRequest]!):Void
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""单个属性元数据
	<AUTHOR>
	@since 2022/4/20
"""
input MetaProperty @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.entities.MetaProperty") {
	"""属性键"""
	key:String
	"""属性值"""
	value:String
}
"""单个属性元数据-任务执行期间获取
	<AUTHOR>
	@since 2022/4/20
"""
input RunTimeProperty @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.entities.RunTimeProperty") {
	"""属性键"""
	key:String
	"""属性值"""
	value:String
}
"""集体报名导入信息"""
input CollectiveSignImportInfoForVerifyRequest @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.request.CollectiveSignImportInfoForVerifyRequest") {
	"""集体报名编号
		允许为空，若为空则默认创建新的批次
	"""
	collectiveNo:String
	"""文件路径"""
	filePath:String!
	"""文件名称"""
	fileName:String
	"""终端类型
		<p>
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	terminalCode:String
	"""默认密码"""
	password:String
	"""密码模式
		1-默认密码——password
		2-使用身份证后六位
	"""
	passwordModel:Int!
	"""密码生效范围，1-默认仅新用户，2-全部用户（含已注册）"""
	passwordEffectiveRange:Int!
	"""销售渠道类型
		0-自营渠道
		2-专题渠道
	"""
	saleChannel:Int!
	"""销售渠道Id（自营渠道为空，专题渠道时必填）"""
	saleChannelId:String
}
"""集体报名导入信息"""
input CollectiveSignImportInfoRequest @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.request.CollectiveSignImportInfoRequest") {
	"""集体报名编号
		允许为空，若为空则默认创建新的批次
	"""
	collectiveNo:String
	"""文件路径"""
	filePath:String
	"""文件名称"""
	fileName:String
	"""终端类型
		<p>
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	terminalCode:String
	"""默认密码"""
	password:String
	"""密码模式
		1-默认密码——password
		2-使用身份证后六位
	"""
	passwordModel:Int
	"""密码生效范围，1-默认仅新用户，2-全部用户（含已注册）"""
	passwordEffectiveRange:Int
	"""销售渠道类型
		0-自营渠道
		2-专题渠道
	"""
	saleChannel:Int!
	"""销售渠道Id（自营渠道为空，专题渠道时必填）"""
	saleChannelId:String
}
"""集体缴费查询对象
	<AUTHOR>
"""
input CollectiveSignQueryRequest @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.request.CollectiveSignQueryRequest") {
	"""集体报名编号"""
	collectiveSignupNo:String!
	"""查询属性集合-模版中存在的数据"""
	metaPropertyList:[MetaProperty]
	"""运行时获取的属性-运行时获取的新数据"""
	runTimePropertyList:[RunTimeProperty]
}
"""集体报名模板查询"""
input CollectiveSignUpByOriginalCollectiveSignupRequest @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.request.CollectiveSignUpByOriginalCollectiveSignupRequest") {
	originalCollectiveSignupNo:String!
	"""默认密码"""
	password:String
	"""密码模式
		1-默认密码——password
		2-使用身份证后六位
	"""
	passwordModel:Int
	"""密码生效范围，1-默认仅新用户，2-全部用户（含已注册）"""
	passwordEffectiveRange:Int
}
"""集体报名模板查询"""
input CollectiveSignUpModelQueryRequest @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.request.CollectiveSignUpModelQueryRequest") {
	"""主任务分类
		集体报名分类-NORMAL_IMPORT
	"""
	category:String
}
"""属性名分组获取数量请求
	<AUTHOR>
"""
input FindCountGroupByKeyRequest @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.request.FindCountGroupByKeyRequest") {
	"""集体报名编号"""
	collectiveSignupNo:String!
	"""指定需要分组统计的key名称"""
	keyName:String!
	"""0 - 查询所有
		1 - 筛选导入成功数据
		2 - 筛序提交成功数据
	"""
	type:Int!
}
"""请求更新的报名数据
	<AUTHOR>
	@since 2022/4/20
"""
input UpdateSignupDataRequest @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.request.UpdateSignupDataRequest") {
	"""子任务数据编号"""
	subTaskDataId:String
	"""一条（或行）数据"""
	dateRow:[MetaProperty]
}
enum ProcessResult @type(value:"com.fjhb.batchtask.core.enums.ProcessResult") {
	UNTREATED
	SUCCESS
	FAILURE
	READY_FAILURE
}
enum TaskState @type(value:"com.fjhb.batchtask.core.enums.TaskState") {
	CREATED
	ALREADY
	EXECUTING
	COMPLETED
}
"""单个属性元数据
	<AUTHOR>
	@since 2022/4/20
"""
type MetaProperty1 @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.entities.MetaProperty") {
	"""属性键"""
	key:String
	"""属性值"""
	value:String
}
"""数据行对象
	<AUTHOR>
	@since 2022/4/24
"""
type MetaRow @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.entities.MetaRow") {
	"""每一行的数据-初始数据属性"""
	row:[MetaProperty1]
	"""运行时获取的属性"""
	runtimeProperties:[RunTimeProperty1]
	"""@see RunTimeDefineType
		运行时获取的属性定义
		SCHEME_ID，学习方案ID，就绪成功时获取
	"""
	runTimeDefineList:[String]
	"""处理结果是否成功
		true/false，成功/失败
	"""
	success:Boolean!
	"""失败原因，仅当success=false时，有值"""
	errorMessage:String
}
"""查询数据的数据架构
	<AUTHOR>
	@since 2022/4/24
"""
type MetaSchema @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.entities.MetaSchema") {
	"""列属性key名称"""
	propertyName:String
	"""列编号"""
	columnNumber:Int!
	"""是否必填"""
	require:Boolean!
	"""是否唯一项"""
	unique:Boolean!
	"""展示名称"""
	field:String
	"""校验器集合"""
	validators:[String]
}
"""单个属性元数据-任务执行期间获取
	<AUTHOR>
	@since 2022/4/20
"""
type RunTimeProperty1 @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.entities.RunTimeProperty") {
	"""属性键"""
	key:String
	"""属性值"""
	value:String
}
"""@Description 最新的任务状态
	<AUTHOR>
	@Date 2023/9/5 10:31
"""
type CollectiveSignTaskInfoResponse @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.response.CollectiveSignTaskInfoResponse") {
	"""任务分类"""
	category:String
	"""状态"""
	state:TaskState
	"""处理结果"""
	result:ProcessResult
	"""处理信息"""
	message:String
	"""来源批次号"""
	originalCollectiveSignup:String
	"""excel解析状态
		@see TaskConstant.AnalysisState
	"""
	analysisState:Int!
	"""主任务编号"""
	mainTaskId:String
	"""总主任务数"""
	total:Int!
}
"""批次单请求线下支付结果
	异常code
	500-接口异常
	3003-表头校验失败
	3004-excel表最大长度校验失败
	20001-当前正在导入报名学员信息，请等导入任务完成后再上传新的报名表
	20002-该集体缴费已提交或已完成，无法继续报名
	<AUTHOR>
	@since 2022/5/12
"""
type ImportCollectiveSignupResponse @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.response.ImportCollectiveSignupResponse") {
	"""批次订单号"""
	batchOrderNo:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""获取指定的属性名分组的数量响应对象
	<AUTHOR>
"""
type KeyGroupResponse @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.response.KeyGroupResponse") {
	"""指定的属性名"""
	key:String
	"""指定的属性名分组的数量"""
	count:Int!
}
"""数据架构返回值
	<AUTHOR>
	@since 2022/4/24
"""
type SchemaResponse @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.response.SchemaResponse") {
	"""不同列的数据架构"""
	metaSchemas:[MetaSchema]
}
"""报名数据解析响应
	<AUTHOR>
"""
type SignupDataAnalysisResponse @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.response.SignupDataAnalysisResponse") {
	"""解析状态
		0=未解析，1-解析中，2=解析完成
	"""
	processStatus:Int!
	"""总数"""
	totalCount:Int!
	"""已执行数"""
	executedCount:Int!
}
"""报名数据执行进度响应
	<AUTHOR>
"""
type SignupDataProcessResponse @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.response.SignupDataProcessResponse") {
	"""指定批次下是否全部所有主任务执行成功"""
	completed:Boolean!
	"""总数"""
	totalCount:Int!
	"""已执行数"""
	executedCount:Int!
}
"""任务执行情况
	<AUTHOR>
	@since 2022/5/5
"""
type TaskExecuteResponse @type(value:"com.fjhb.ms.collectivesign.v1.kernel.gateway.graphql.response.TaskExecuteResponse") {
	"""任务编号"""
	taskId:String
	"""任务名称"""
	name:String
	"""所属批次单编号"""
	batchNo:String
	"""状态
		0- 已创建
		1- 已就绪
		2- 执行中
		3- 执行完成
	"""
	state:Int!
	"""处理结果
		0- 未处理
		1- 成功
		2- 失败
		3- 就绪失败
	"""
	result:Int!
	"""处理信息"""
	message:String
	"""创建时间"""
	createdTime:DateTime
	"""就绪时间"""
	alreadyTime:DateTime
	"""执行时间"""
	executingTime:DateTime
	"""完成时间"""
	completedTime:DateTime
	"""总数"""
	totalCount:Int!
	"""成功数量"""
	successCount:Int!
	"""失败数量"""
	failCount:Int!
}

scalar List
type MetaRowPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [MetaRow]}
type TaskExecuteResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TaskExecuteResponse]}
