<template>
  <el-card shadow="never" class="m-card f-mb15">
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form
          ref="sealForm"
          :model="mutationElectronicSeal.electronicSealParams"
          label-width="7.5rem"
          class="m-form f-mt20"
        >
          <el-form-item label="落款名称：" prop="sign">
            <el-input v-model="mutationElectronicSeal.electronicSealParams.sign" clearable class="form-l" />
          </el-form-item>
          <el-form-item label="培训证明盖章：" prop="url" ref="sealPicture">
            <cropper-img-upload
              :dialogStyleOpation="{
                width: '500px',
                height: '500px'
              }"
              :ratioArr="['472:472']"
              :initWidth="472"
              title="上传培训证明盖章图片"
              v-model="mutationElectronicSeal.electronicSealParams.url"
              :mode="`472px 472px`"
              :has-preview="false"
              :accept="'.jpg,.png,.jpeg'"
            >
              <template slot="tip">
                <div slot="tip" class="el-upload__tip">
                  <i class="el-icon-warning"></i>
                  <div class="txt">上传培训证明盖章（透明底色），先设计好后上传，建议尺寸：宽度472px * 长度472px。</div>
                </div>
              </template>
            </cropper-img-upload>
          </el-form-item>
          <el-form-item class="m-btn-bar">
            <el-button @click="doCancel">取消</el-button>
            <el-button type="primary" @click="doSave">保存</el-button>
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <give-up-dialog :give-up-model="uiConfig.giveUpModel" @callBack="resetData"></give-up-dialog>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import CropperImgUpload from '@hbfe/jxjy-admin-platform/src/function/components/cropper-img-upload.vue'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import TrainingCertificateModule from '@api/service/management/personal-leaning/TrainingCertificateModule'
  import MutationConfigureElectronicSeal from '@api/service/management/personal-leaning/mutation/MutationConfigureElectronicSeal'
  import ElectronicSealResponseVo from '@api/service/management/personal-leaning/query/vo/ElectronicSealResponseVo'
  import ConfigureElectronicSealRequestVo from '@api/service/management/personal-leaning/mutation/vo/ConfigureElectronicSealRequestVo'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import PortalVo from '@api/service/common/online-school-config/vo/PortalVo'
  @Component({
    components: { CropperImgUpload, GiveUpDialog }
  })
  export default class extends Vue {
    /*@Ref('sealForm') sealForm: any
    rules = {
      sign: [{ required: true, message: '请输入落款名称', trigger: ['change', 'blur'] }],
      url: [{ required: true, message: '请选择培训证明盖章', trigger: 'blur' }]
    }*/
    webPortalInfo: PortalVo = OnlineSchoolConfigModule.queryPortal.webPortalInfo
    mutationElectronicSeal: MutationConfigureElectronicSeal =
      TrainingCertificateModule.mutationBatchPrintTrainingFactory.configureElectronicSeal
    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }

    /*@Watch('electronicSealParams.url', {
      immediate: true,
      deep: true
    })
    pictureChange(val: string) {
      const el: any = this.$refs['sealPicture']
      if (el) {
        if (val) {
          //有图片时清除校验
          el.clearValidate()
        } else {
          el.validate()
        }
      }
    }*/

    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
    }

    async resetData() {
      //重新获取数据
      await this.getCertSealInfo()
    }

    async doSave() {
      //保存配置
      const res = await this.mutationElectronicSeal.doConfigureElectronicSeal()
      if (res.status.code === 200) {
        this.$message.success('保存成功')
      } else {
        this.$message.error('保存失败')
      }
    }

    /*validSealForm() {
      this.sealForm.validate((valid: boolean) => {
        if (valid) {
          this.doSave()
        }
      })
    }*/

    async getCertSealInfo() {
      //获取培训证明配置
      const res =
        await TrainingCertificateModule.queryTrainingCertificateFactory.certificateTemplate.queryElectronicSealInServicer()
      if (res.status.code === 200) {
        this.mutationElectronicSeal.electronicSealParams = res.data
      }
    }

    async created() {
      await this.getCertSealInfo()
      if (this.mutationElectronicSeal.electronicSealParams.sign == '') {
        this.mutationElectronicSeal.electronicSealParams.sign = this.webPortalInfo.title
      }
    }
  }
</script>
