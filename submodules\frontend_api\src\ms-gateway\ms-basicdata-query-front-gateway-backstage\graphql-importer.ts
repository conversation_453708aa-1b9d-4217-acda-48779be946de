import getBusinessDataDictionaryInSubProject from './queries/getBusinessDataDictionaryInSubProject.graphql'
import getCertificateCategoryInSubProject from './queries/getCertificateCategoryInSubProject.graphql'
import getCertificateLevelInSubProject from './queries/getCertificateLevelInSubProject.graphql'
import getCertificateMajorInSubProject from './queries/getCertificateMajorInSubProject.graphql'
import getCertificateTypeInSubProject from './queries/getCertificateTypeInSubProject.graphql'
import getJobCategoryInSubProject from './queries/getJobCategoryInSubProject.graphql'
import getRegionInSubProject from './queries/getRegionInSubProject.graphql'
import listAllRegionInSubProject from './queries/listAllRegionInSubProject.graphql'
import listBusinessDataDictionaryByIdInSubProject from './queries/listBusinessDataDictionaryByIdInSubProject.graphql'
import listBusinessDataDictionaryInSubProject from './queries/listBusinessDataDictionaryInSubProject.graphql'
import listChildRegionInSubProject from './queries/listChildRegionInSubProject.graphql'
import listRegionByCodeInSubProject from './queries/listRegionByCodeInSubProject.graphql'
import listRegionInSubProject from './queries/listRegionInSubProject.graphql'
import pageBusinessDataDictionaryInSubProject from './queries/pageBusinessDataDictionaryInSubProject.graphql'
import pageCertificateCategoryInSubProject from './queries/pageCertificateCategoryInSubProject.graphql'
import pageCertificateLevelInSubProject from './queries/pageCertificateLevelInSubProject.graphql'
import pageCertificateMajorInSubProject from './queries/pageCertificateMajorInSubProject.graphql'
import pageCertificateTypeInSubProject from './queries/pageCertificateTypeInSubProject.graphql'
import pageJobCategoryInSubProject from './queries/pageJobCategoryInSubProject.graphql'

export {
  getBusinessDataDictionaryInSubProject,
  getCertificateCategoryInSubProject,
  getCertificateLevelInSubProject,
  getCertificateMajorInSubProject,
  getCertificateTypeInSubProject,
  getJobCategoryInSubProject,
  getRegionInSubProject,
  listAllRegionInSubProject,
  listBusinessDataDictionaryByIdInSubProject,
  listBusinessDataDictionaryInSubProject,
  listChildRegionInSubProject,
  listRegionByCodeInSubProject,
  listRegionInSubProject,
  pageBusinessDataDictionaryInSubProject,
  pageCertificateCategoryInSubProject,
  pageCertificateLevelInSubProject,
  pageCertificateMajorInSubProject,
  pageCertificateTypeInSubProject,
  pageJobCategoryInSubProject
}
