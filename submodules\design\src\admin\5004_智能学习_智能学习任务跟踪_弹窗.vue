<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--重启任务-->
        <el-button @click="dialog7 = true" type="primary" class="f-mr20 f-mb20">重启任务</el-button>
        <el-drawer title="重启任务" :visible.sync="dialog7" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="期望开始学习时间：" required>
                <el-radio-group v-model="radio">
                  <el-radio :label="3" class="f-mt10" style="display: block;"
                    >按上次期望开始学习时间（2025-01-18）</el-radio
                  >
                  <el-radio :label="6" class="f-mt10" style="display: block;">系统默认开始学习时间</el-radio>
                  <el-radio :label="9" class="f-mt10" style="display: block;"
                    >自定义时间<el-date-picker
                      v-model="value1"
                      type="date"
                      size="small"
                      class="f-ml10"
                      placeholder="选择日期"
                    >
                    </el-date-picker
                  ></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">确定</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <!--查看执行日志详情-->
        <el-button @click="dialog6 = true" type="primary" class="f-mr20">查看执行日志详情</el-button>
        <el-drawer
          title="查看详情"
          :visible.sync="dialog6"
          :direction="direction"
          size="1400px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="f-mb15">
              <div class="m-tit">
                <span class="tit-txt">人员信息</span>
              </div>
              <el-alert type="warning" :closable="false" show-icon class="m-alert f-mb10">
                <div class="f-c6">期望开始学习时间：2024-10-07</div>
              </el-alert>
              <!--表格-->
              <el-table stripe :data="tableData" highlight-current-row class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="课程名称" min-width="160">
                  <template>海洋经济</template>
                </el-table-column>
                <el-table-column label="学习开始时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="学习结束时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="智能学习完成进度" min-width="160">
                  <template>100%</template>
                </el-table-column>
                <el-table-column label="学习完成情况" min-width="140">
                  <template>已完成</template>
                </el-table-column>
                <el-table-column label="测验开始时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="测验结束时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="测验完成情况" min-width="140">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <p>已完成</p>
                      <p class="f-cr">测验分数：89</p>
                    </div>
                    <div v-else>
                      <p>自主学习插入</p>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="f-mb15">
              <div class="m-tit">
                <span class="tit-txt">班级考试</span>
              </div>
              <!--表格-->
              <el-table stripe :data="tableData" highlight-current-row class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="考试场次" min-width="160">
                  <template>模拟考试</template>
                </el-table-column>
                <el-table-column label="考试开始时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="考试结束时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="当前完成情况" min-width="160">
                  <template>已完成</template>
                </el-table-column>
                <el-table-column label="考试成绩" min-width="140">
                  <template>85</template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button type="primary">返 回</el-button>
          </div>
        </el-drawer>
        <!--查看执行日志-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">查看执行日志</el-button>
        <el-drawer
          title="查看执行日志"
          :visible.sync="dialog5"
          :direction="direction"
          size="1000px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table f-mt10">
              <el-table-column type="index" label="NO." width="80"> </el-table-column>
              <el-table-column label="执行状态更新时间" min-width="100">
                <template>2024-09-30 18:53:27</template>
              </el-table-column>
              <el-table-column label="执行状态更新原因" min-width="160">
                <template>智能学习未开始模拟学习，学员进班学习</template>
              </el-table-column>
              <el-table-column label="执行状态" min-width="80">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="danger" class="badge-status">执行失败</el-badge>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <el-badge is-dot type="info" class="badge-status">终止</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">已完成</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="智能学习操作类型" min-width="100">
                <template>导入智能学习</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text">查看详情</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text">查看详情</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button type="primary">返 回</el-button>
          </div>
        </el-drawer>
        <!--查看编排详情-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20">查看编排详情</el-button>
        <el-drawer
          title="查看编排详情"
          :visible.sync="dialog4"
          :direction="direction"
          size="1400px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="f-mb15">
              <div class="m-tit">
                <span class="tit-txt">人员信息</span>
              </div>
              <el-alert type="warning" :closable="false" show-icon class="m-alert f-mb10">
                <div class="f-c6">期望开始学习时间：2024-10-07</div>
              </el-alert>
              <!--表格-->
              <el-table stripe :data="tableData" highlight-current-row class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="课程名称" min-width="160">
                  <template>海洋经济</template>
                </el-table-column>
                <el-table-column label="学习开始时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="学习结束时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="智能学习完成进度" min-width="160">
                  <template>100%</template>
                </el-table-column>
                <el-table-column label="学习完成情况" min-width="140">
                  <template>已完成</template>
                </el-table-column>
                <el-table-column label="测验开始时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="测验结束时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="测验完成情况" min-width="140">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <p>已完成</p>
                      <p class="f-cr">测验分数：89</p>
                    </div>
                    <div v-else>
                      <p>自主学习插入</p>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <div class="f-mb15">
              <div class="m-tit">
                <span class="tit-txt">班级考试</span>
              </div>
              <!--表格-->
              <el-table stripe :data="tableData" highlight-current-row class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="考试场次" min-width="160">
                  <template>模拟考试</template>
                </el-table-column>
                <el-table-column label="考试开始时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="考试结束时间" min-width="160">
                  <template>2024-09-30 18:53:27</template>
                </el-table-column>
                <el-table-column label="当前完成情况" min-width="160">
                  <template>已完成</template>
                </el-table-column>
                <el-table-column label="考试成绩" min-width="140">
                  <template>85</template>
                </el-table-column>
              </el-table>
            </div>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button type="primary">返 回</el-button>
          </div>
        </el-drawer>
        <!--查看编排日志-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20">查看编排日志</el-button>
        <el-drawer
          title="查看编排日志"
          :visible.sync="dialog3"
          :direction="direction"
          size="900px"
          custom-class="m-drawer m-table-auto"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table f-mt10">
              <el-table-column type="index" label="NO." width="80"> </el-table-column>
              <el-table-column label="编排任务类型" min-width="120">
                <template>导入智能学习</template>
              </el-table-column>
              <el-table-column label="" min-width="100" align="center">
                <template slot="header">
                  允许重叠
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>
                        若为是：将按系统默认规则进行学习（一人一班每日可学习18选课学时，不限制学习时段），同一学员不同班级学习产生的学习记录允许存在重叠；
                      </p>
                      <p>若为空：该任务编排时段将允许被重叠班级使用。</p>
                    </div>
                  </el-tooltip>
                </template>
                <template>是</template>
              </el-table-column>
              <el-table-column label="创建时间" min-width="120">
                <template>2024-09-30 18:53:27</template>
              </el-table-column>
              <el-table-column label="编排完成时间" min-width="120">
                <template>2024-09-30 18:53:27</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text">查看详情</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text">查看详情</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button type="primary">返 回</el-button>
          </div>
        </el-drawer>
        <!--选择培训方案-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">选择培训方案</el-button>
        <el-drawer
          title="选择培训方案"
          :visible.sync="dialog2"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="8">
                  <el-form-item label="年度">
                    <el-select v-model="select" clearable placeholder="请选择年度">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="地区">
                    <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="行业">
                    <el-select v-model="select" clearable placeholder="请选择行业">
                      <el-option value="人社"></el-option>
                      <el-option value="建设"></el-option>
                      <el-option value="职业卫生"></el-option>
                      <el-option value="工勤"></el-option>
                      <el-option value="教师"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="学段">
                    <el-select v-model="select" clearable placeholder="请选择学段">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="学科">
                    <el-select v-model="select" clearable placeholder="请选择学科">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="科目类型">
                    <el-select v-model="select" clearable placeholder="请选择科目类型">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="培训类别">
                    <el-select v-model="select" clearable placeholder="请选择培训类别">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="培训专业">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="培训方案类型">
                    <el-select clearable placeholder="请选择培训方案类型">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="培训方案名称">
                    <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <el-alert type="warning" :closable="false" class="m-alert f-mb15">
              当前已选中 <span class="f-fb">2</span> 个培训方案
            </el-alert>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="selection" width="55"> </el-table-column>
              <el-table-column label="培训方案名称" min-width="300">
                <template>
                  <el-tooltip placement="top" effect="light">
                    <div slot="content">
                      <i class="f-c4"
                        ><el-tag type="primary" effect="dark" size="mini" class="f-mr5">培训班-选课规则</el-tag
                        >培训方案名称培训方案名称培训方案名称培训方案名称</i
                      >
                    </div>
                    <el-button type="text" class="f-to-three"
                      ><i class="f-c4"
                        ><el-tag type="primary" effect="dark" size="mini">培训班-选课规则</el-tag
                        >培训方案名称培训方案名称培训方案名称培训方案名称</i
                      ></el-button
                    >
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column label="培训属性" min-width="240">
                <template>
                  <p>行业：行业行业</p>
                  <p>地区：为空，不展示</p>
                  <p>科目类型：科目类型</p>
                  <p>培训类别：培训类别</p>
                  <p>培训专业：培训专业培训专业</p>
                  <p>培训年度：2019</p>
                </template>
              </el-table-column>
              <el-table-column label="报名状态" min-width="140">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="info" class="badge-status">报名关闭</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">开放报名</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="学习起止时间" min-width="220">
                <template>长期有效</template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text" size="mini">选择</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text" size="mini">取消选择</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取 消</el-button>
            <el-button type="primary">保 存</el-button>
          </div>
        </el-drawer>
        <!--选择具体导入任务-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">选择具体导入任务</el-button>
        <el-drawer
          title="选择具体导入任务"
          :visible.sync="dialog1"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="6">
                  <el-form-item label="执行状态">
                    <el-select v-model="select" clearable filterable placeholder="请选择执行状态">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="执行时间">
                    <el-date-picker
                      v-model="form.date1"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="任务名称" min-width="280">
                <template>任务名称任务名称任务名称</template>
              </el-table-column>
              <el-table-column label="任务处理时间 - 任务结束时间" min-width="220">
                <template>
                  <p>处理：2020-11-11 12:20:20</p>
                  <p>结束：2020-11-11 12:20:20</p>
                </template>
              </el-table-column>
              <el-table-column label="任务执行状态" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-tag type="info">未执行</el-tag>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <el-tag type="primary">执行中</el-tag>
                  </div>
                  <div v-else>
                    <el-tag type="success">执行完成</el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="任务处理结果" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="danger" class="badge-status">失败</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">成功</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="处理总条数/成功条数/失败条数" width="240" align="center">
                <template>10 / 5 / 5</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-radio v-model="checked" label="选择"></el-radio>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
            <div class="m-btn-bar f-tc f-mt20">
              <el-button>取消</el-button>
              <el-button type="primary">确定</el-button>
            </div>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames: ['1'],
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        value1: '',
        form: {},
        tableData: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleChange(val) {
        console.log(val)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
