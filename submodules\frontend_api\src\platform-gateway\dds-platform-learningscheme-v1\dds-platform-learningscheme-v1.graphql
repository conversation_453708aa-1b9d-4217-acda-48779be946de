schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""保存考勤设置
		@param request
	"""
	saveAttendanceSetting(request:AttendanceSettingRequest):PreTrainingResponse
	"""保存学习资料设置
		@param request
	"""
	saveLearningResourceSetting(request:LearningResourceSettingRequest):PreTrainingResponse
	"""保存报到规则配置
		@param request 保存报到规则配置请求体
		@return 保存结果
	"""
	saveReportRuleSetting(request:SaveReportRuleSettingRequest):PreTrainingResponse
}
"""保存报到规则配置请求体"""
input SaveReportRuleSettingRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.SaveReportRuleSettingRequest") {
	"""学习方案ID"""
	learningSchemeId:String
	"""拥有者类型"""
	ownerType:Int
	"""拥有者ID"""
	ownerId:String
	"""期别ID"""
	issueId:String
	"""打卡半径范围(x米)"""
	signRadiusRange:Int
}
"""<AUTHOR>
	@since
"""
input AttendanceSettingRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.pretraining.AttendanceSettingRequest") {
	"""方案id"""
	learningSchemeId:String
	"""所有者类型
		方案  1
		期别  3
		@see OwnerTypes
	"""
	ownerTye:Int!
	"""方案   方案id
		期数  对应期数id
	"""
	ownerId:String
	"""签到"""
	signIn:AttendanceSignRequest
	"""签退"""
	signOut:AttendanceSignRequest
}
"""<AUTHOR>
	@since
"""
input AttendanceSignRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.pretraining.AttendanceSignRequest") {
	"""是否开启"""
	enable:Boolean!
	"""签到频率
		半天  1;
		每节课  2;
		@see SignFrequencyTypes
	"""
	frequency:Int!
	"""签到半径"""
	radius:Double!
	"""签到开始前
		单位:(秒)
	"""
	beforeSecond:Int!
	"""开始后
		单位:(秒)
	"""
	afterSecond:Int!
}
"""<AUTHOR>
	@since
"""
input LearningResourceRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.pretraining.LearningResourceRequest") {
	"""附件名称"""
	name:String
	"""格式"""
	format:String
	"""路径"""
	filePath:String
}
"""<AUTHOR>
	@since
"""
input LearningResourceSettingRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.pretraining.LearningResourceSettingRequest") {
	"""方案id"""
	learningSchemeId:String
	"""所有者类型
		方案  1
		期别  3
		@see OwnerTypes
	"""
	ownerTye:Int!
	"""方案   方案id
		期数  对应期数id
	"""
	ownerId:String
	"""已添加的学习资料"""
	resourceList:[LearningResourceRequest]
}
"""E429 已存在报名学员
	<AUTHOR>
	@since
"""
type PreTrainingResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.PreTrainingResponse") {
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}

scalar List
