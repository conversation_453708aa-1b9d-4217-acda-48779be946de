import ChooseCourseVo from '@api/service/customer/learning/choose-course/mutation/vo/ChooseCourseVo'
import { Page, ResponseStatus } from '@hbfe/common'
import WaitChooseCourseTreeCacheVo from '@api/service/customer/learning/choose-course/mutation/vo/WaitChooseCourseTreeCacheVo'
import QueryCourseModule from '@api/service/customer/course/query/QueryCourseModule'
import { cloneDeep } from 'lodash'
import TrainingOutlineCourse from '@api/service/customer/course/query/vo/TrainingOutlineCourse'
import Utils from '@api/service/customer/learning/choose-course/mutation/utils'

/**
 * @description 可选待选课程列表数据持久化
 */
class MutationWaitChooseCourse {
  /**
   * 已选课学时
   */
  private _selectedPeriod: number

  /**
   * 可选待选课程列表树
   */
  private _waitChooseCourseTreeCache: Array<WaitChooseCourseTreeCacheVo> = new Array<WaitChooseCourseTreeCacheVo>()

  /**
   * 设置已选课学时
   * @param {number} val
   * @return
   */
  set selectedPeriod(val: number) {
    this._selectedPeriod = val
  }

  /**
   * 获取已选课学时
   */
  get selectedPeriod() {
    return this._selectedPeriod
  }

  /**
   * 设置可选待选课程列表树
   * @param {Array<WaitChooseCourseTreeCacheVo>} list
   * @return
   */
  set waitChooseCourseTreeCache(list: Array<WaitChooseCourseTreeCacheVo>) {
    this._waitChooseCourseTreeCache = list
  }

  /**
   * 获取可选待选课程列表树
   */
  get waitChooseCourseTreeCache() {
    return this._waitChooseCourseTreeCache
  }

  /**
   * 查询待选课程列表
   * @param studentNo - 学员学号
   * @param outlineId - 课程学习大纲Id
   * @param outlineParentId - 课程学习大纲父级Id
   * @param contentIds - 内容供应方id数组
   * @param uiPage - 分页
   * @param courseName 课程名称
   * @param excludeOutlineIds 要排除的大纲id数组
   * @return currentWaitChooseList 在currentWaitChooseList获取课程列表
   */
  async queryWaitChooseCourseList(
    studentNo: string,
    outlineId: string,
    outlineParentId: string,
    contentIds?: Array<string>,
    uiPage?: Page,
    courseName?: string,
    notEmpty?: boolean,
    excludeOutlineIds?: Array<string>
  ): Promise<ResponseStatus> {
    // 根据课程学习大纲Id查询分类下课程、分类下课程总数并本地缓存，UI只需要知道请求是否有效 - LWF
    await QueryCourseModule.queryCourse.queryTrainingOutlineCourse(
      studentNo,
      outlineId,
      contentIds,
      uiPage,
      courseName,
      excludeOutlineIds
    )
    const waitChooseCourseList = QueryCourseModule.queryCourse.trainingCourseOutlineCourseMapList[outlineId]

    this.setWaitChooseCourseListCache({
      outlineId: outlineId,
      outlineParentId: outlineParentId,
      courseList: waitChooseCourseList,
      notEmpty: notEmpty
    })
    return new ResponseStatus(200, '')
  }

  /**
   * 查询启用的内容提供方列表
   */
  /*async queryEnableContentList(): Promise<Array<ContentProviderResponse>> {
    const contentProviderRequest = new ContentProviderRequest()

    const res = await Contentproviderv1.listContentProvider(contentProviderRequest)

    const result = (res?.data?.length && res.data.filter(item => item.status === 1)) || []

    return result.sort((a, b) => {
      return a.sort - b.sort
    })
  }*/

  /**
   * 更新可选待选课程列表方法 - 私有
   * @param {string} outlineId - 课程学习大纲Id
   * @param {string} outlineParentId - 课程学习大纲父级Id
   * @param {Array<TrainingOutlineCourse>} courseList - 分类下可选待选课程信息
   * @return _waitChooseCourseTreeCache
   */
  private setWaitChooseCourseListCache(param: {
    outlineId: string
    outlineParentId: string
    courseList: Array<TrainingOutlineCourse>
    notEmpty?: boolean
  }): void {
    // todo 【待验证】对于获取到的需要过滤相同的课程然后再进行拼接
    const waitCourseList = cloneDeep(param.courseList)
    // 获取已选择课程id列表
    // const selectedCourseIdList = MutationSelectedCourse.selectedCourseIdList

    const selectedCourseIdList = Utils.getSomeOutlineTreeCourse(this._waitChooseCourseTreeCache)
    console.group(
      '%c%s',
      'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#32FFFF, #fff)',
      'selectedCourseIdList调试输出'
    )
    console.log(this._waitChooseCourseTreeCache)
    console.count('selectedCourseIdList输出次数')
    console.groupEnd()
    if (selectedCourseIdList.length) {
      // 根据已选课课程id进行过滤相同的课程id且课程学习大纲不同
      selectedCourseIdList?.map(idObj => {
        const index = waitCourseList?.findIndex((course: TrainingOutlineCourse) => course.id === idObj.courseId)
        if (index > -1) {
          waitCourseList.splice(index, 1)
        }
      })
    }

    const currentWaitChooseCourseTree = Utils.getOutlineTreeNode(
      this._waitChooseCourseTreeCache,
      param.outlineId,
      'outlineId'
    )

    // todo 优化校验currentWaitChooseCourseTree是否为对象空值
    if (currentWaitChooseCourseTree && currentWaitChooseCourseTree['outlineId']) {
      // todo 待清空需要再确认 将原先的清空重新赋值
      if (!param.notEmpty) {
        currentWaitChooseCourseTree.chooseCourseList = new Array<ChooseCourseVo>()
      }

      waitCourseList?.map((item: TrainingOutlineCourse) => {
        const findExist = selectedCourseIdList.findIndex(
          selectedId => selectedId.courseId.includes(item.id) && selectedId.outlineId.includes(item.outlineId)
        )
        if (findExist > -1) {
          // 对于已选择的重新赋值
          currentWaitChooseCourseTree.chooseCourseList.push(ChooseCourseVo.from(item, true))
        } else {
          currentWaitChooseCourseTree.chooseCourseList.push(ChooseCourseVo.from(item))
        }
      })
    } else {
      // todo 当找不到该节点时抛出报错异常
    }
    console.log(this._waitChooseCourseTreeCache, '插入课程后的完整课程大纲树')
  }

  /**
   * 根据课程学习大纲Id获取分类下可选课程列表
   * @param {string} outlineId - 课程学习大纲Id
   */
  get currentWaitChooseList() {
    // todo 如果是全部的分类的话 需要另外处理 会有多选的情况出现
    return (outlineId: string): Array<ChooseCourseVo> => {
      const outlineIds = outlineId.split('##')
      const outlineTree = Utils.getOutlineTreeNode(this._waitChooseCourseTreeCache, outlineId, 'outlineId')
      if (outlineTree && outlineTree['outlineId']) {
        return outlineTree?.chooseCourseList
      } else {
        return new Array<ChooseCourseVo>()
      }
    }
  }
}

export default new MutationWaitChooseCourse()
