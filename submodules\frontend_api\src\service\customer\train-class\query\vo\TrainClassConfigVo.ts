/**
 * 培训班考核配置信息Vo
 */
class TrainClassConfigVo {
  // region properties

  /**
   *课程学习要求学时，类型为number
   */
  courseRequirePeriod = 0
  /**
   *是否纳入课后测验，类型为boolean
   */
  incorporateCourseQuiz = false
  /**
   *每门课课后测验合格分数，类型为number
   */
  eachCourseQuizPassScore = 0
  /**
   *考试合格成绩，类型为number
   */
  examPassScore = 0
  /**
   * 考试是否纳入考核
   */
  isExamAssessed = false
  // endregion
  // region methods

  // endregion
}
export default TrainClassConfigVo
