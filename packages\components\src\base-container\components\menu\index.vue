<template>
  <el-menu
    :background-color="backgroundColor"
    class="aside-nav"
    :default-active="defaultActive"
    :unique-opened="true"
    :router="true"
    :collapse="isCollapse"
  >
    <template v-for="(menu, index) in menuList">
      <el-menu-item v-if="!menu.children || !menu.children.length" :index="menu.router.path" :key="menu.id + index">
        <i :class="[menu.meta.icon, /^el/.test(menu.meta.icon) ? '' : 'iconfont']"></i>
        <template slot="title">
          <span slot="title" :style="menu.router.path | isCurrent" :data-current="currentNav.path">
            {{ menu.name }}
          </span>
        </template>
      </el-menu-item>
      <el-submenu :index="menu.id" v-if="menu.children && menu.children.length" :key="'sub' + menu.id + index">
        <template slot="title">
          <i :class="[/^hb-iconfont/.test(menu.meta.icon) ? 'hb-iconfont' : 'iconfont', menu.meta.icon]"></i>
          <span :data-id="menu.router.path">{{ menu.name }}</span>
        </template>
        <template v-for="(subMenu, subIndex) in menu.children">
          <el-menu-item
            :data-id="subMenu.router.path"
            v-if="!subMenu.children || !subMenu.children.length"
            :index="subMenu.router.path"
            :key="subIndex"
          >
            <i class="iconfont" :class="subMenu.meta.icon"></i>
            <span slot="title" :style="subMenu.router.path | isCurrent" :data-id="subMenu.router.path">
              {{ subMenu.name }}
            </span>
          </el-menu-item>
          <el-submenu
            :index="subMenu.id"
            v-if="subMenu.children && subMenu.children.length"
            :key="'grand' + subMenu.id + subIndex"
          >
            <template slot="title">
              <i :class="[/^hb-iconfont/.test(subMenu.meta.icon) ? 'hb-iconfont' : 'iconfont', subMenu.meta.icon]"></i>
              <span :style="subMenu.router.path | isCurrent" :data-id="subMenu.router.path">
                {{ subMenu.name }}
              </span>
            </template>
            <template v-for="(grandMenu, grandIndex) in subMenu.children">
              <el-menu-item :index="grandMenu.router.path" :key="grandIndex" :data-id="grandMenu.router.path">
                <i class="iconfont" :class="'icon' + recode(grandMenu.code)"></i>
                <span slot="title">{{ grandMenu.name }}</span>
              </el-menu-item>
            </template>
          </el-submenu>
        </template>
      </el-submenu>
    </template>
  </el-menu>
</template>

<style lang="less">
  @import './index.less';

  .bare-menu {
    .iconfont {
      margin-right: 10px;
      margin-left: 1px;
    }
  }

  .el-menu-vertical-demo:not(.el-menu--collapse) {
    width: 200px;
    min-height: 400px;
  }
  .side-collapsed {
    .el-menu {
      > .el-menu-item {
        display: block !important;
      }
    }
  }
</style>

<script lang="ts">
  import rootModule from '@/store/RootModule'
  import { Vue, Prop, Component, Watch } from 'vue-property-decorator'
  import RootModule from '@/store/RootModule'
  import Menu from '@hbfe/jxjy-admin-common/src/models/Menu'

  @Component({
    filters: {
      isCurrent(path: string) {
        if (!RootModule.currentNav) return {}
        return RootModule.currentNav.path === path ? { color: '#2d8cf0' } : {}
      }
    }
  })
  export default class extends Vue {
    defaultActive = ''
    @Prop(String) backgroundColor: string
    @Prop({ default: false, type: Boolean }) isCollapse: boolean

    get menuList() {
      const currentActiveTopMenu = rootModule.menuMap[rootModule.currentActiveTopMenu]

      return currentActiveTopMenu?.menuList || []
    }

    get currentNav(): Menu {
      return this.getCurrentNavigationActive()
    }

    getCurrentNavigationActive(): any {
      if (!RootModule.currentNav) {
        this.defaultActive = this.$route.path
        return new Menu()
      }
      this.defaultActive = RootModule.currentNav.path
      return RootModule.currentNav
    }

    recode(name: string) {
      return name?.replace(/[A-Z]/g, (item: string) => {
        return `-${item.toLowerCase()}`
      })
    }

    @Watch('$route.path', {
      immediate: true
    })
    pathChange() {
      this.getCurrentNavigationActive()
    }

    async created() {
      try {
        const getDefaultRoute = (menu: Menu): string => {
          if (menu?.children?.length) {
            return getDefaultRoute(menu.children[0])
          }
          return menu?.router?.path
        }
        if (RootModule.menuList.length) {
          const path = this.$route.matched[1]?.path
          RootModule.setCurrentTopMenu({
            id: path,
            index: RootModule.menuMap[path]?.index
          })
          RootModule.SET_DEFAULT_ROUTE_PATH(path)
        }
      } catch (e) {
        // 菜单获取失败不处理
      }
    }
  }
</script>
