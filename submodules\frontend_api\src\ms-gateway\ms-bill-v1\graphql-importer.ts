import getInvoicingServiceLogOfBlueTicket from './queries/getInvoicingServiceLogOfBlueTicket.graphql'
import getInvoicingServiceLogOfRedTicket from './queries/getInvoicingServiceLogOfRedTicket.graphql'
import addOrUpdateElectronicInvoiceAutoConfig from './mutates/addOrUpdateElectronicInvoiceAutoConfig.graphql'
import flushElectronicInvoice from './mutates/flushElectronicInvoice.graphql'
import issueElectronicInvoice from './mutates/issueElectronicInvoice.graphql'
import obsoleteElectronicInvoice from './mutates/obsoleteElectronicInvoice.graphql'
import reimburseCancelElectronicInvoice from './mutates/reimburseCancelElectronicInvoice.graphql'
import retryIssueElectronicInvoice from './mutates/retryIssueElectronicInvoice.graphql'
import rushRedInvoiceItem from './mutates/rushRedInvoiceItem.graphql'
import searchInvoiceResultAndDownload from './mutates/searchInvoiceResultAndDownload.graphql'
import updateElectronicInvoice from './mutates/updateElectronicInvoice.graphql'
import userRegistration from './mutates/userRegistration.graphql'

export {
  getInvoicingServiceLogOfBlueTicket,
  getInvoicingServiceLogOfRedTicket,
  addOrUpdateElectronicInvoiceAutoConfig,
  flushElectronicInvoice,
  issueElectronicInvoice,
  obsoleteElectronicInvoice,
  reimburseCancelElectronicInvoice,
  retryIssueElectronicInvoice,
  rushRedInvoiceItem,
  searchInvoiceResultAndDownload,
  updateElectronicInvoice,
  userRegistration
}
