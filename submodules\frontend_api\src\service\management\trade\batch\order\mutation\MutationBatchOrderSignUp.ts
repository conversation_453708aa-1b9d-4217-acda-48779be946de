import { ResponseStatus } from '@hbfe/common'

import Payment from '@api/ms-gateway/ms-payment-v1'
import MsOrder from '@api/ms-gateway/ms-order-v1'
class MutationBatchOrderSignUp {
  /**
   * 关闭批次
   * @returns
   */
  async cancelCollectivePaySignUp(param: {
    batchNo?: string
    reasonId?: string
    reason?: string
  }): Promise<ResponseStatus> {
    const { status } = await MsOrder.cancelCollectiveSignup(param)
    return status
  }
  /**
   * 获取集体报名订单状态
   */
  async prepareBatchOrderRepay(batchOrderNo: string) {
    const { data } = await Payment.prepareBatchOrderRepay(batchOrderNo)
    return data
  }
}
export default MutationBatchOrderSignUp
