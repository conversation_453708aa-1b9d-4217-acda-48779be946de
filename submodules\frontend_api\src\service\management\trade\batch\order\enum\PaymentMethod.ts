import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 【集体报名订单】缴费方式
 */
export enum PaymentMethodEnum {
  // 线上缴费
  Online_Payment = 1,
  // 线下缴费
  Offline_Payment,
  // 无须付款
  No_Payment
}

/**
 * @description 【集体报名订单】缴费方式
 */
class PaymentMethod extends AbstractEnum<PaymentMethodEnum> {
  static enum = PaymentMethodEnum
  constructor(status?: PaymentMethodEnum) {
    super()
    this.current = status
    this.map.set(PaymentMethodEnum.Online_Payment, '线上缴费')
    this.map.set(PaymentMethodEnum.Offline_Payment, '线下缴费')
    this.map.set(PaymentMethodEnum.No_Payment, '无须付款')
  }
}

export default new PaymentMethod()
