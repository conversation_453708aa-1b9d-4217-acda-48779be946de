export class LearningResultVo {
  learningResultId = ''
  learningResultName = ''
}
/**
 * 学员在培训班中获取到的数据类
 */
export class UserGetLearning {
  /**
   * 已选课总学时 选课规则使用
   */
  selectedCoursePeriod = 0
  /**
   * 还需选课 选课规则使用
   */
  needSelectedCoursePeriod = 0
  /**
   * 共需完成多少学时
   */
  requirePeriod = 0
  /**
   * 已完成多少学时
   */
  currentPeriod = 0
  /**
   * 是否已考试
   */
  committedExam = false
  /**
   * 考试是否合格
   */
  examQualified = false
  /**
   * 课程学习是否合格
   */
  courseQualified = false
  /**
   * 学习心得是否合格
   */
  experienceQualified = false
  /**
   * 考试合格时间
   */
  examQualifiedTime = ''
  /**
   * 考试次数
   */
  examCount = 0
  /**
   * 最高成绩
   */
  maxExamScore = 0
  /**
   * 剩余考试次数 -1无限次
   */
  surplusExamCount = 0
  /**
   * 获得的学时
   */
  credit = 0
  /**
   * 获得的培训模板对象
   */
  learningResult = new LearningResultVo()
}
