import OrderCommodityVo from '@api/service/management/train-class/query/vo/OrderCommodityVo'
import { IssueLogResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { ChangeIssueType } from '@api/service/management/train-class/offlinePart/enum/ChangeIssueType'

export default class IssueChangeRecordItem {
  /**
   * 原始商品信息
   */
  originalCommodity: OrderCommodityVo = new OrderCommodityVo()

  /**
   * 换货商品信息
   */
  exchangeCommodity: OrderCommodityVo = new OrderCommodityVo()

  /**
   * 操作人名称
   */
  operatorName = ''

  /**
   * 操作人id
   */
  operatorId = ''

  /**
   * 子订单号
   */
  subOrderNo = ''

  /**
   * 换期类型
   */
  changeType: ChangeIssueType = undefined

  /**
   * 更换时间
   */
  changeTime = ''

  static from(dto: IssueLogResponse) {
    const vo = new IssueChangeRecordItem()
    const { originalCommodity, exchangeCommodity, creator } = dto
    vo.subOrderNo = dto.subOrder
    vo.changeType = dto.exchangeType
    vo.changeTime = dto.createTime

    if (creator) {
      vo.operatorId = creator.userId
      vo.operatorName = creator.name
    }

    if (originalCommodity) {
      vo.originalCommodity = OrderCommodityVo.getOrderCommodity(originalCommodity)
    }
    if (exchangeCommodity) {
      vo.exchangeCommodity = OrderCommodityVo.getOrderCommodity(exchangeCommodity)
    }

    return vo
  }
}
