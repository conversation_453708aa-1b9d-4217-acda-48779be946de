export default [
  {
    children: [
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0000_全屏表格弹窗.vue',
        vueRouter: '/admin_0000_全屏表格弹窗',
        moduleName: 'Admin0000_QuanPingBiaoGeDanChuang',
        title: '0000_全屏表格弹窗.vue',
        label: '0000_全屏表格弹窗.vue',
        name: '0000_全屏表格弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0000_弹窗样式汇总.vue',
        vueRouter: '/admin_0000_弹窗样式汇总',
        moduleName: 'Admin0000_DanChuangYangShiHuiZong',
        title: '0000_弹窗样式汇总.vue',
        label: '0000_弹窗样式汇总.vue',
        name: '0000_弹窗样式汇总.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0000_通用空数据.vue',
        vueRouter: '/admin_0000_通用空数据',
        moduleName: 'Admin0000_TongYongKongShuJu',
        title: '0000_通用空数据.vue',
        label: '0000_通用空数据.vue',
        name: '0000_通用空数据.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0000_通用组件样式汇总.vue',
        vueRouter: '/admin_0000_通用组件样式汇总',
        moduleName: 'Admin0000_TongYongZuJianYangShiHuiZong',
        title: '0000_通用组件样式汇总.vue',
        label: '0000_通用组件样式汇总.vue',
        name: '0000_通用组件样式汇总.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0001_登录页.vue',
        vueRouter: '/admin_0001_登录页',
        moduleName: 'Admin0001_DengLuYe',
        title: '0001_登录页.vue',
        label: '0001_登录页.vue',
        name: '0001_登录页.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0001_登录页_弹窗.vue',
        vueRouter: '/admin_0001_登录页_弹窗',
        moduleName: 'Admin0001_DengLuYe_DanChuang',
        title: '0001_登录页_弹窗.vue',
        label: '0001_登录页_弹窗.vue',
        name: '0001_登录页_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0001_登录页_等保.vue',
        vueRouter: '/admin_0001_登录页_等保',
        moduleName: 'Admin0001_DengLuYe_DengBao',
        title: '0001_登录页_等保.vue',
        label: '0001_登录页_等保.vue',
        name: '0001_登录页_等保.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0002_忘记密码.vue',
        vueRouter: '/admin_0002_忘记密码',
        moduleName: 'Admin0002_WangJiMiMa',
        title: '0002_忘记密码.vue',
        label: '0002_忘记密码.vue',
        name: '0002_忘记密码.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0101_头部及侧边栏框架.vue',
        vueRouter: '/admin_0101_头部及侧边栏框架',
        moduleName: 'Admin0101_TouBuJiCeBianLanKuangJia',
        title: '0101_头部及侧边栏框架.vue',
        label: '0101_头部及侧边栏框架.vue',
        name: '0101_头部及侧边栏框架.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0102_首页.vue',
        vueRouter: '/admin_0102_首页',
        moduleName: 'Admin0102_ShouYe',
        title: '0102_首页.vue',
        label: '0102_首页.vue',
        name: '0102_首页.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0103_帐号设置.vue',
        vueRouter: '/admin_0103_帐号设置',
        moduleName: 'Admin0103_ZhangHaoSheZhi',
        title: '0103_帐号设置.vue',
        label: '0103_帐号设置.vue',
        name: '0103_帐号设置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0104_帐号设置_弹窗.vue',
        vueRouter: '/admin_0104_帐号设置_弹窗',
        moduleName: 'Admin0104_ZhangHaoSheZhi_DanChuang',
        title: '0104_帐号设置_弹窗.vue',
        label: '0104_帐号设置_弹窗.vue',
        name: '0104_帐号设置_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0105_新手引导.vue',
        vueRouter: '/admin_0105_新手引导',
        moduleName: 'Admin0105_XinShouYinDao',
        title: '0105_新手引导.vue',
        label: '0105_新手引导.vue',
        name: '0105_新手引导.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0106_网校状态提醒.vue',
        vueRouter: '/admin_0106_网校状态提醒',
        moduleName: 'Admin0106_WangXiaoZhuangTaiTiXing',
        title: '0106_网校状态提醒.vue',
        label: '0106_网校状态提醒.vue',
        name: '0106_网校状态提醒.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0107_智能学习任务反馈提醒.vue',
        vueRouter: '/admin_0107_智能学习任务反馈提醒',
        moduleName: 'Admin0107_ZhiNengXueXiRenWuFanKuiTiXing',
        title: '0107_智能学习任务反馈提醒.vue',
        label: '0107_智能学习任务反馈提醒.vue',
        name: '0107_智能学习任务反馈提醒.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0201_基础信息配置_门户信息配置.vue',
        vueRouter: '/admin_0201_基础信息配置_门户信息配置',
        moduleName: 'Admin0201_JiChuXinXiPeiZhi_MenHuXinXiPeiZhi',
        title: '0201_基础信息配置_门户信息配置.vue',
        label: '0201_基础信息配置_门户信息配置.vue',
        name: '0201_基础信息配置_门户信息配置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0202_基础信息配置_栏目设置.vue',
        vueRouter: '/admin_0202_基础信息配置_栏目设置',
        moduleName: 'Admin0202_JiChuXinXiPeiZhi_LanMuSheZhi',
        title: '0202_基础信息配置_栏目设置.vue',
        label: '0202_基础信息配置_栏目设置.vue',
        name: '0202_基础信息配置_栏目设置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0203_基础信息配置_新增栏目.vue',
        vueRouter: '/admin_0203_基础信息配置_新增栏目',
        moduleName: 'Admin0203_JiChuXinXiPeiZhi_XinZengLanMu',
        title: '0203_基础信息配置_新增栏目.vue',
        label: '0203_基础信息配置_新增栏目.vue',
        name: '0203_基础信息配置_新增栏目.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0204_基础信息配置_轮播图设置.vue',
        vueRouter: '/admin_0204_基础信息配置_轮播图设置',
        moduleName: 'Admin0204_JiChuXinXiPeiZhi_LunBoTuSheZhi',
        title: '0204_基础信息配置_轮播图设置.vue',
        label: '0204_基础信息配置_轮播图设置.vue',
        name: '0204_基础信息配置_轮播图设置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0205_基础信息配置_添加轮播图_web端.vue',
        vueRouter: '/admin_0205_基础信息配置_添加轮播图_web端',
        moduleName: 'Admin0205_JiChuXinXiPeiZhi_TianJiaLunBoTu_webDuan',
        title: '0205_基础信息配置_添加轮播图_web端.vue',
        label: '0205_基础信息配置_添加轮播图_web端.vue',
        name: '0205_基础信息配置_添加轮播图_web端.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0206_基础信息配置_风格设置.vue',
        vueRouter: '/admin_0206_基础信息配置_风格设置',
        moduleName: 'Admin0206_JiChuXinXiPeiZhi_FengGeSheZhi',
        title: '0206_基础信息配置_风格设置.vue',
        label: '0206_基础信息配置_风格设置.vue',
        name: '0206_基础信息配置_风格设置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0207_基础信息配置_移动学习.vue',
        vueRouter: '/admin_0207_基础信息配置_移动学习',
        moduleName: 'Admin0207_JiChuXinXiPeiZhi_YiDongXueXi',
        title: '0207_基础信息配置_移动学习.vue',
        label: '0207_基础信息配置_移动学习.vue',
        name: '0207_基础信息配置_移动学习.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0208_基础信息配置_添加轮播图_移动端.vue',
        vueRouter: '/admin_0208_基础信息配置_添加轮播图_移动端',
        moduleName: 'Admin0208_JiChuXinXiPeiZhi_TianJiaLunBoTu_YiDongDuan',
        title: '0208_基础信息配置_添加轮播图_移动端.vue',
        label: '0208_基础信息配置_添加轮播图_移动端.vue',
        name: '0208_基础信息配置_添加轮播图_移动端.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0209_基础信息配置_网校SEO配置.vue',
        vueRouter: '/admin_0209_基础信息配置_网校SEO配置',
        moduleName: 'Admin0209_JiChuXinXiPeiZhi_WangXiaoSEOPeiZhi',
        title: '0209_基础信息配置_网校SEO配置.vue',
        label: '0209_基础信息配置_网校SEO配置.vue',
        name: '0209_基础信息配置_网校SEO配置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0301_功能设置_注册登录.vue',
        vueRouter: '/admin_0301_功能设置_注册登录',
        moduleName: 'Admin0301_GongNengSheZhi_ZhuCeDengLu',
        title: '0301_功能设置_注册登录.vue',
        label: '0301_功能设置_注册登录.vue',
        name: '0301_功能设置_注册登录.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0302_功能设置_行业信息详情.vue',
        vueRouter: '/admin_0302_功能设置_行业信息详情',
        moduleName: 'Admin0302_GongNengSheZhi_XingYeXinXiXiangQing',
        title: '0302_功能设置_行业信息详情.vue',
        label: '0302_功能设置_行业信息详情.vue',
        name: '0302_功能设置_行业信息详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0303_功能设置_集体报名.vue',
        vueRouter: '/admin_0303_功能设置_集体报名',
        moduleName: 'Admin0303_GongNengSheZhi_JiTiBaoMing',
        title: '0303_功能设置_集体报名.vue',
        label: '0303_功能设置_集体报名.vue',
        name: '0303_功能设置_集体报名.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0304_功能设置_增值税发票.vue',
        vueRouter: '/admin_0304_功能设置_增值税发票',
        moduleName: 'Admin0304_GongNengSheZhi_ZengZhiShuiFaPiao',
        title: '0304_功能设置_增值税发票.vue',
        label: '0304_功能设置_增值税发票.vue',
        name: '0304_功能设置_增值税发票.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0305_功能设置_培训证明.vue',
        vueRouter: '/admin_0305_功能设置_培训证明',
        moduleName: 'Admin0305_GongNengSheZhi_PeiXunZhengMing',
        title: '0305_功能设置_培训证明.vue',
        label: '0305_功能设置_培训证明.vue',
        name: '0305_功能设置_培训证明.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0306_功能设置_视频播放设置.vue',
        vueRouter: '/admin_0306_功能设置_视频播放设置',
        moduleName: 'Admin0306_GongNengSheZhi_ShiPinBoFangSheZhi',
        title: '0306_功能设置_视频播放设置.vue',
        label: '0306_功能设置_视频播放设置.vue',
        name: '0306_功能设置_视频播放设置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0307_功能设置_门户精品课程.vue',
        vueRouter: '/admin_0307_功能设置_门户精品课程',
        moduleName: 'Admin0307_GongNengSheZhi_MenHuJingPinKeCheng',
        title: '0307_功能设置_门户精品课程.vue',
        label: '0307_功能设置_门户精品课程.vue',
        name: '0307_功能设置_门户精品课程.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0308_功能设置_门户精品课程_添加.vue',
        vueRouter: '/admin_0308_功能设置_门户精品课程_添加',
        moduleName: 'Admin0308_GongNengSheZhi_MenHuJingPinKeCheng_TianJia',
        title: '0308_功能设置_门户精品课程_添加.vue',
        label: '0308_功能设置_门户精品课程_添加.vue',
        name: '0308_功能设置_门户精品课程_添加.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0309_功能设置_修改日志.vue',
        vueRouter: '/admin_0309_功能设置_修改日志',
        moduleName: 'Admin0309_GongNengSheZhi_XiuGaiRiZhi',
        title: '0309_功能设置_修改日志.vue',
        label: '0309_功能设置_修改日志.vue',
        name: '0309_功能设置_修改日志.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0310_功能设置_修改日志_查看详情.vue',
        vueRouter: '/admin_0310_功能设置_修改日志_查看详情',
        moduleName: 'Admin0310_GongNengSheZhi_XiuGaiRiZhi_ChaKanXiangQing',
        title: '0310_功能设置_修改日志_查看详情.vue',
        label: '0310_功能设置_修改日志_查看详情.vue',
        name: '0310_功能设置_修改日志_查看详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0311_功能配置_选课排序配置.vue',
        vueRouter: '/admin_0311_功能配置_选课排序配置',
        moduleName: 'Admin0311_GongNengPeiZhi_XuanKePaiXuPeiZhi',
        title: '0311_功能配置_选课排序配置.vue',
        label: '0311_功能配置_选课排序配置.vue',
        name: '0311_功能配置_选课排序配置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0312_功能配置_选课排序配置_弹窗.vue',
        vueRouter: '/admin_0312_功能配置_选课排序配置_弹窗',
        moduleName: 'Admin0312_GongNengPeiZhi_XuanKePaiXuPeiZhi_DanChuang',
        title: '0312_功能配置_选课排序配置_弹窗.vue',
        label: '0312_功能配置_选课排序配置_弹窗.vue',
        name: '0312_功能配置_选课排序配置_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0313_功能配置_选课排序配置_编辑.vue',
        vueRouter: '/admin_0313_功能配置_选课排序配置_编辑',
        moduleName: 'Admin0313_GongNengPeiZhi_XuanKePaiXuPeiZhi_BianJi',
        title: '0313_功能配置_选课排序配置_编辑.vue',
        label: '0313_功能配置_选课排序配置_编辑.vue',
        name: '0313_功能配置_选课排序配置_编辑.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0314_功能配置_选课排序配置_查看.vue',
        vueRouter: '/admin_0314_功能配置_选课排序配置_查看',
        moduleName: 'Admin0314_GongNengPeiZhi_XuanKePaiXuPeiZhi_ChaKan',
        title: '0314_功能配置_选课排序配置_查看.vue',
        label: '0314_功能配置_选课排序配置_查看.vue',
        name: '0314_功能配置_选课排序配置_查看.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0315_功能设置_培训监管管理_基础配置与监管规则.vue',
        vueRouter: '/admin_0315_功能设置_培训监管管理_基础配置与监管规则',
        moduleName: 'Admin0315_GongNengSheZhi_PeiXunJianGuanGuanLi_JiChuPeiZhiYuJianGuanGuiZe',
        title: '0315_功能设置_培训监管管理_基础配置与监管规则.vue',
        label: '0315_功能设置_培训监管管理_基础配置与监管规则.vue',
        name: '0315_功能设置_培训监管管理_基础配置与监管规则.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0316_功能设置_培训监管管理_弹窗.vue',
        vueRouter: '/admin_0316_功能设置_培训监管管理_弹窗',
        moduleName: 'Admin0316_GongNengSheZhi_PeiXunJianGuanGuanLi_DanChuang',
        title: '0316_功能设置_培训监管管理_弹窗.vue',
        label: '0316_功能设置_培训监管管理_弹窗.vue',
        name: '0316_功能设置_培训监管管理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0317_功能设置_培训监管管理_网校级监管编辑.vue',
        vueRouter: '/admin_0317_功能设置_培训监管管理_网校级监管编辑',
        moduleName: 'Admin0317_GongNengSheZhi_PeiXunJianGuanGuanLi_WangXiaoJiJianGuanBianJi',
        title: '0317_功能设置_培训监管管理_网校级监管编辑.vue',
        label: '0317_功能设置_培训监管管理_网校级监管编辑.vue',
        name: '0317_功能设置_培训监管管理_网校级监管编辑.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0318_功能设置_培训监管管理_方案级监管新建编辑.vue',
        vueRouter: '/admin_0318_功能设置_培训监管管理_方案级监管新建编辑',
        moduleName: 'Admin0318_GongNengSheZhi_PeiXunJianGuanGuanLi_FangAnJiJianGuanXinJianBianJi',
        title: '0318_功能设置_培训监管管理_方案级监管新建编辑.vue',
        label: '0318_功能设置_培训监管管理_方案级监管新建编辑.vue',
        name: '0318_功能设置_培训监管管理_方案级监管新建编辑.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0319_功能设置_培训监管管理_方案详情.vue',
        vueRouter: '/admin_0319_功能设置_培训监管管理_方案详情',
        moduleName: 'Admin0319_GongNengSheZhi_PeiXunJianGuanGuanLi_FangAnXiangQing',
        title: '0319_功能设置_培训监管管理_方案详情.vue',
        label: '0319_功能设置_培训监管管理_方案详情.vue',
        name: '0319_功能设置_培训监管管理_方案详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0320_功能设置_培训监管管理_监管配置快照.vue',
        vueRouter: '/admin_0320_功能设置_培训监管管理_监管配置快照',
        moduleName: 'Admin0320_GongNengSheZhi_PeiXunJianGuanGuanLi_JianGuanPeiZhiKuaiZhao',
        title: '0320_功能设置_培训监管管理_监管配置快照.vue',
        label: '0320_功能设置_培训监管管理_监管配置快照.vue',
        name: '0320_功能设置_培训监管管理_监管配置快照.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0320_功能设置_培训监管管理_配置日志.vue',
        vueRouter: '/admin_0320_功能设置_培训监管管理_配置日志',
        moduleName: 'Admin0320_GongNengSheZhi_PeiXunJianGuanGuanLi_PeiZhiRiZhi',
        title: '0320_功能设置_培训监管管理_配置日志.vue',
        label: '0320_功能设置_培训监管管理_配置日志.vue',
        name: '0320_功能设置_培训监管管理_配置日志.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0321_功能设置_学习规则.vue',
        vueRouter: '/admin_0321_功能设置_学习规则',
        moduleName: 'Admin0321_GongNengSheZhi_XueXiGuiZe',
        title: '0321_功能设置_学习规则.vue',
        label: '0321_功能设置_学习规则.vue',
        name: '0321_功能设置_学习规则.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0322_功能设置_学习规则_弹窗.vue',
        vueRouter: '/admin_0322_功能设置_学习规则_弹窗',
        moduleName: 'Admin0322_GongNengSheZhi_XueXiGuiZe_DanChuang',
        title: '0322_功能设置_学习规则_弹窗.vue',
        label: '0322_功能设置_学习规则_弹窗.vue',
        name: '0322_功能设置_学习规则_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0322_功能设置_学习规则_查看学习规则的方案.vue',
        vueRouter: '/admin_0322_功能设置_学习规则_查看学习规则的方案',
        moduleName: 'Admin0322_GongNengSheZhi_XueXiGuiZe_ChaKanXueXiGuiZeDeFangAn',
        title: '0322_功能设置_学习规则_查看学习规则的方案.vue',
        label: '0322_功能设置_学习规则_查看学习规则的方案.vue',
        name: '0322_功能设置_学习规则_查看学习规则的方案.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0323_功能设置_添加&编辑学习规则.vue',
        vueRouter: '/admin_0323_功能设置_添加&编辑学习规则',
        moduleName: 'Admin0323_GongNengSheZhi_TianJiaBianJiXueXiGuiZe',
        title: '0323_功能设置_添加&编辑学习规则.vue',
        label: '0323_功能设置_添加&编辑学习规则.vue',
        name: '0323_功能设置_添加&编辑学习规则.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0325_功能设置_学习规则_详情.vue',
        vueRouter: '/admin_0325_功能设置_学习规则_详情',
        moduleName: 'Admin0325_GongNengSheZhi_XueXiGuiZe_XiangQing',
        title: '0325_功能设置_学习规则_详情.vue',
        label: '0325_功能设置_学习规则_详情.vue',
        name: '0325_功能设置_学习规则_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0326_功能设置_添加学习规则_弹窗.vue',
        vueRouter: '/admin_0326_功能设置_添加学习规则_弹窗',
        moduleName: 'Admin0326_GongNengSheZhi_TianJiaXueXiGuiZe_DanChuang',
        title: '0326_功能设置_添加学习规则_弹窗.vue',
        label: '0326_功能设置_添加学习规则_弹窗.vue',
        name: '0326_功能设置_添加学习规则_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0331_功能设置_在线学习规则.vue',
        vueRouter: '/admin_0331_功能设置_在线学习规则',
        moduleName: 'Admin0331_GongNengSheZhi_ZaiXianXueXiGuiZe',
        title: '0331_功能设置_在线学习规则.vue',
        label: '0331_功能设置_在线学习规则.vue',
        name: '0331_功能设置_在线学习规则.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0332_功能设置_在线学习规则_添加&编辑规则.vue',
        vueRouter: '/admin_0332_功能设置_在线学习规则_添加&编辑规则',
        moduleName: 'Admin0332_GongNengSheZhi_ZaiXianXueXiGuiZe_TianJiaBianJiGuiZe',
        title: '0332_功能设置_在线学习规则_添加&编辑规则.vue',
        label: '0332_功能设置_在线学习规则_添加&编辑规则.vue',
        name: '0332_功能设置_在线学习规则_添加&编辑规则.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0333_功能设置_在线学习规则_详情.vue',
        vueRouter: '/admin_0333_功能设置_在线学习规则_详情',
        moduleName: 'Admin0333_GongNengSheZhi_ZaiXianXueXiGuiZe_XiangQing',
        title: '0333_功能设置_在线学习规则_详情.vue',
        label: '0333_功能设置_在线学习规则_详情.vue',
        name: '0333_功能设置_在线学习规则_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0334_功能设置_在线学习规则_弹窗汇总.vue',
        vueRouter: '/admin_0334_功能设置_在线学习规则_弹窗汇总',
        moduleName: 'Admin0334_GongNengSheZhi_ZaiXianXueXiGuiZe_DanChuangHuiZong',
        title: '0334_功能设置_在线学习规则_弹窗汇总.vue',
        label: '0334_功能设置_在线学习规则_弹窗汇总.vue',
        name: '0334_功能设置_在线学习规则_弹窗汇总.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0341_功能设置_智能学习.vue',
        vueRouter: '/admin_0341_功能设置_智能学习',
        moduleName: 'Admin0341_GongNengSheZhi_ZhiNengXueXi',
        title: '0341_功能设置_智能学习.vue',
        label: '0341_功能设置_智能学习.vue',
        name: '0341_功能设置_智能学习.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0342_功能设置_智能学习_弹窗.vue',
        vueRouter: '/admin_0342_功能设置_智能学习_弹窗',
        moduleName: 'Admin0342_GongNengSheZhi_ZhiNengXueXi_DanChuang',
        title: '0342_功能设置_智能学习_弹窗.vue',
        label: '0342_功能设置_智能学习_弹窗.vue',
        name: '0342_功能设置_智能学习_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0350_功能配置_协议配置.vue',
        vueRouter: '/admin_0350_功能配置_协议配置',
        moduleName: 'Admin0350_GongNengPeiZhi_XieYiPeiZhi',
        title: '0350_功能配置_协议配置.vue',
        label: '0350_功能配置_协议配置.vue',
        name: '0350_功能配置_协议配置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0401_资讯管理_列表.vue',
        vueRouter: '/admin_0401_资讯管理_列表',
        moduleName: 'Admin0401_ZiXunGuanLi_LieBiao',
        title: '0401_资讯管理_列表.vue',
        label: '0401_资讯管理_列表.vue',
        name: '0401_资讯管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0402_资讯管理_新建及修改.vue',
        vueRouter: '/admin_0402_资讯管理_新建及修改',
        moduleName: 'Admin0402_ZiXunGuanLi_XinJianJiXiuGai',
        title: '0402_资讯管理_新建及修改.vue',
        label: '0402_资讯管理_新建及修改.vue',
        name: '0402_资讯管理_新建及修改.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0501_收款账户管理_列表.vue',
        vueRouter: '/admin_0501_收款账户管理_列表',
        moduleName: 'Admin0501_ShouKuanZhangHuGuanLi_LieBiao',
        title: '0501_收款账户管理_列表.vue',
        label: '0501_收款账户管理_列表.vue',
        name: '0501_收款账户管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0502_收款账户管理_新建及修改.vue',
        vueRouter: '/admin_0502_收款账户管理_新建及修改',
        moduleName: 'Admin0502_ShouKuanZhangHuGuanLi_XinJianJiXiuGai',
        title: '0502_收款账户管理_新建及修改.vue',
        label: '0502_收款账户管理_新建及修改.vue',
        name: '0502_收款账户管理_新建及修改.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0503_收款账户管理_详情.vue',
        vueRouter: '/admin_0503_收款账户管理_详情',
        moduleName: 'Admin0503_ShouKuanZhangHuGuanLi_XiangQing',
        title: '0503_收款账户管理_详情.vue',
        label: '0503_收款账户管理_详情.vue',
        name: '0503_收款账户管理_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0601_报名方式配置_学员缴费.vue',
        vueRouter: '/admin_0601_报名方式配置_学员缴费',
        moduleName: 'Admin0601_BaoMingFangShiPeiZhi_XueYuanJiaoFei',
        title: '0601_报名方式配置_学员缴费.vue',
        label: '0601_报名方式配置_学员缴费.vue',
        name: '0601_报名方式配置_学员缴费.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0602_报名方式配置_集体报名.vue',
        vueRouter: '/admin_0602_报名方式配置_集体报名',
        moduleName: 'Admin0602_BaoMingFangShiPeiZhi_JiTiBaoMing',
        title: '0602_报名方式配置_集体报名.vue',
        label: '0602_报名方式配置_集体报名.vue',
        name: '0602_报名方式配置_集体报名.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0603_报名方式配置_选择收款帐号.vue',
        vueRouter: '/admin_0603_报名方式配置_选择收款帐号',
        moduleName: 'Admin0603_BaoMingFangShiPeiZhi_XuanZeShouKuanZhangHao',
        title: '0603_报名方式配置_选择收款帐号.vue',
        label: '0603_报名方式配置_选择收款帐号.vue',
        name: '0603_报名方式配置_选择收款帐号.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0604_报名方式配置_导入开通.vue',
        vueRouter: '/admin_0604_报名方式配置_导入开通',
        moduleName: 'Admin0604_BaoMingFangShiPeiZhi_DaoRuKaiTong',
        title: '0604_报名方式配置_导入开通.vue',
        label: '0604_报名方式配置_导入开通.vue',
        name: '0604_报名方式配置_导入开通.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0701_配送渠道配置.vue',
        vueRouter: '/admin_0701_配送渠道配置',
        moduleName: 'Admin0701_PeiSongQuDaoPeiZhi',
        title: '0701_配送渠道配置.vue',
        label: '0701_配送渠道配置.vue',
        name: '0701_配送渠道配置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0702_配送渠道配置_弹窗.vue',
        vueRouter: '/admin_0702_配送渠道配置_弹窗',
        moduleName: 'Admin0702_PeiSongQuDaoPeiZhi_DanChuang',
        title: '0702_配送渠道配置_弹窗.vue',
        label: '0702_配送渠道配置_弹窗.vue',
        name: '0702_配送渠道配置_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0801_管理员帐号_列表.vue',
        vueRouter: '/admin_0801_管理员帐号_列表',
        moduleName: 'Admin0801_GuanLiYuanZhangHao_LieBiao',
        title: '0801_管理员帐号_列表.vue',
        label: '0801_管理员帐号_列表.vue',
        name: '0801_管理员帐号_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0802_管理员帐号_新增及修改管理员.vue',
        vueRouter: '/admin_0802_管理员帐号_新增及修改管理员',
        moduleName: 'Admin0802_GuanLiYuanZhangHao_XinZengJiXiuGaiGuanLiYuan',
        title: '0802_管理员帐号_新增及修改管理员.vue',
        label: '0802_管理员帐号_新增及修改管理员.vue',
        name: '0802_管理员帐号_新增及修改管理员.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0803_管理员帐号_查看.vue',
        vueRouter: '/admin_0803_管理员帐号_查看',
        moduleName: 'Admin0803_GuanLiYuanZhangHao_ChaKan',
        title: '0803_管理员帐号_查看.vue',
        label: '0803_管理员帐号_查看.vue',
        name: '0803_管理员帐号_查看.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0901_角色管理_列表.vue',
        vueRouter: '/admin_0901_角色管理_列表',
        moduleName: 'Admin0901_JiaoSeGuanLi_LieBiao',
        title: '0901_角色管理_列表.vue',
        label: '0901_角色管理_列表.vue',
        name: '0901_角色管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0902_角色管理_添加及修改角色.vue',
        vueRouter: '/admin_0902_角色管理_添加及修改角色',
        moduleName: 'Admin0902_JiaoSeGuanLi_TianJiaJiXiuGaiJiaoSe',
        title: '0902_角色管理_添加及修改角色.vue',
        label: '0902_角色管理_添加及修改角色.vue',
        name: '0902_角色管理_添加及修改角色.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0903_角色管理_查看功能权限.vue',
        vueRouter: '/admin_0903_角色管理_查看功能权限',
        moduleName: 'Admin0903_JiaoSeGuanLi_ChaKanGongNengQuanXian',
        title: '0903_角色管理_查看功能权限.vue',
        label: '0903_角色管理_查看功能权限.vue',
        name: '0903_角色管理_查看功能权限.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/0904_分销商管理员帐号管理.vue',
        vueRouter: '/admin_0904_分销商管理员帐号管理',
        moduleName: 'Admin0904_FenXiaoShangGuanLiYuanZhangHaoGuanLi',
        title: '0904_分销商管理员帐号管理.vue',
        label: '0904_分销商管理员帐号管理.vue',
        name: '0904_分销商管理员帐号管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1001_地区管理员_列表.vue',
        vueRouter: '/admin_1001_地区管理员_列表',
        moduleName: 'Admin1001_DiQuGuanLiYuan_LieBiao',
        title: '1001_地区管理员_列表.vue',
        label: '1001_地区管理员_列表.vue',
        name: '1001_地区管理员_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1002_地区管理员_弹窗.vue',
        vueRouter: '/admin_1002_地区管理员_弹窗',
        moduleName: 'Admin1002_DiQuGuanLiYuan_DanChuang',
        title: '1002_地区管理员_弹窗.vue',
        label: '1002_地区管理员_弹窗.vue',
        name: '1002_地区管理员_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1003_课件供应商管理_列表.vue',
        vueRouter: '/admin_1003_课件供应商管理_列表',
        moduleName: 'Admin1003_KeJianGongYingShangGuanLi_LieBiao',
        title: '1003_课件供应商管理_列表.vue',
        label: '1003_课件供应商管理_列表.vue',
        name: '1003_课件供应商管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1004_课件供应商管理_弹窗.vue',
        vueRouter: '/admin_1004_课件供应商管理_弹窗',
        moduleName: 'Admin1004_KeJianGongYingShangGuanLi_DanChuang',
        title: '1004_课件供应商管理_弹窗.vue',
        label: '1004_课件供应商管理_弹窗.vue',
        name: '1004_课件供应商管理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1005_内容提供方管理_列表.vue',
        vueRouter: '/admin_1005_内容提供方管理_列表',
        moduleName: 'Admin1005_NeiRongTiGongFangGuanLi_LieBiao',
        title: '1005_内容提供方管理_列表.vue',
        label: '1005_内容提供方管理_列表.vue',
        name: '1005_内容提供方管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1006_内容提供方管理_弹窗.vue',
        vueRouter: '/admin_1006_内容提供方管理_弹窗',
        moduleName: 'Admin1006_NeiRongTiGongFangGuanLi_DanChuang',
        title: '1006_内容提供方管理_弹窗.vue',
        label: '1006_内容提供方管理_弹窗.vue',
        name: '1006_内容提供方管理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1101_课件管理_列表.vue',
        vueRouter: '/admin_1101_课件管理_列表',
        moduleName: 'Admin1101_KeJianGuanLi_LieBiao',
        title: '1101_课件管理_列表.vue',
        label: '1101_课件管理_列表.vue',
        name: '1101_课件管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1102_课件管理_新建及修改课件.vue',
        vueRouter: '/admin_1102_课件管理_新建及修改课件',
        moduleName: 'Admin1102_KeJianGuanLi_XinJianJiXiuGaiKeJian',
        title: '1102_课件管理_新建及修改课件.vue',
        label: '1102_课件管理_新建及修改课件.vue',
        name: '1102_课件管理_新建及修改课件.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1102_课件管理_课件详情.vue',
        vueRouter: '/admin_1102_课件管理_课件详情',
        moduleName: 'Admin1102_KeJianGuanLi_KeJianXiangQing',
        title: '1102_课件管理_课件详情.vue',
        label: '1102_课件管理_课件详情.vue',
        name: '1102_课件管理_课件详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1103_课件管理_课件分类管理.vue',
        vueRouter: '/admin_1103_课件管理_课件分类管理',
        moduleName: 'Admin1103_KeJianGuanLi_KeJianFenLeiGuanLi',
        title: '1103_课件管理_课件分类管理.vue',
        label: '1103_课件管理_课件分类管理.vue',
        name: '1103_课件管理_课件分类管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1104_课件管理_弹窗.vue',
        vueRouter: '/admin_1104_课件管理_弹窗',
        moduleName: 'Admin1104_KeJianGuanLi_DanChuang',
        title: '1104_课件管理_弹窗.vue',
        label: '1104_课件管理_弹窗.vue',
        name: '1104_课件管理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1105_课件管理_外链课件导入.vue',
        vueRouter: '/admin_1105_课件管理_外链课件导入',
        moduleName: 'Admin1105_KeJianGuanLi_WaiLianKeJianDaoRu',
        title: '1105_课件管理_外链课件导入.vue',
        label: '1105_课件管理_外链课件导入.vue',
        name: '1105_课件管理_外链课件导入.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1201_课程管理_列表.vue',
        vueRouter: '/admin_1201_课程管理_列表',
        moduleName: 'Admin1201_KeChengGuanLi_LieBiao',
        title: '1201_课程管理_列表.vue',
        label: '1201_课程管理_列表.vue',
        name: '1201_课程管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1202_课程管理_新建及修改.vue',
        vueRouter: '/admin_1202_课程管理_新建及修改',
        moduleName: 'Admin1202_KeChengGuanLi_XinJianJiXiuGai',
        title: '1202_课程管理_新建及修改.vue',
        label: '1202_课程管理_新建及修改.vue',
        name: '1202_课程管理_新建及修改.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1203_课程管理_详情.vue',
        vueRouter: '/admin_1203_课程管理_详情',
        moduleName: 'Admin1203_KeChengGuanLi_XiangQing',
        title: '1203_课程管理_详情.vue',
        label: '1203_课程管理_详情.vue',
        name: '1203_课程管理_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1204_课程管理_选择课件及添加目录.vue',
        vueRouter: '/admin_1204_课程管理_选择课件及添加目录',
        moduleName: 'Admin1204_KeChengGuanLi_XuanZeKeJianJiTianJiaMuLu',
        title: '1204_课程管理_选择课件及添加目录.vue',
        label: '1204_课程管理_选择课件及添加目录.vue',
        name: '1204_课程管理_选择课件及添加目录.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1205_课程管理_课程分类管理.vue',
        vueRouter: '/admin_1205_课程管理_课程分类管理',
        moduleName: 'Admin1205_KeChengGuanLi_KeChengFenLeiGuanLi',
        title: '1205_课程管理_课程分类管理.vue',
        label: '1205_课程管理_课程分类管理.vue',
        name: '1205_课程管理_课程分类管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1206_课程管理_课程分类_弹窗.vue',
        vueRouter: '/admin_1206_课程管理_课程分类_弹窗',
        moduleName: 'Admin1206_KeChengGuanLi_KeChengFenLei_DanChuang',
        title: '1206_课程管理_课程分类_弹窗.vue',
        label: '1206_课程管理_课程分类_弹窗.vue',
        name: '1206_课程管理_课程分类_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1210_待分配课程_列表.vue',
        vueRouter: '/admin_1210_待分配课程_列表',
        moduleName: 'Admin1210_DaiFenPeiKeCheng_LieBiao',
        title: '1210_待分配课程_列表.vue',
        label: '1210_待分配课程_列表.vue',
        name: '1210_待分配课程_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1211_待分配课程_详情.vue',
        vueRouter: '/admin_1211_待分配课程_详情',
        moduleName: 'Admin1211_DaiFenPeiKeCheng_XiangQing',
        title: '1211_待分配课程_详情.vue',
        label: '1211_待分配课程_详情.vue',
        name: '1211_待分配课程_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1212_待分配课程_批量分配课程.vue',
        vueRouter: '/admin_1212_待分配课程_批量分配课程',
        moduleName: 'Admin1212_DaiFenPeiKeCheng_PiLiangFenPeiKeCheng',
        title: '1212_待分配课程_批量分配课程.vue',
        label: '1212_待分配课程_批量分配课程.vue',
        name: '1212_待分配课程_批量分配课程.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1213_待分配课程_弹窗.vue',
        vueRouter: '/admin_1213_待分配课程_弹窗',
        moduleName: 'Admin1213_DaiFenPeiKeCheng_DanChuang',
        title: '1213_待分配课程_弹窗.vue',
        label: '1213_待分配课程_弹窗.vue',
        name: '1213_待分配课程_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1301_题库管理_列表.vue',
        vueRouter: '/admin_1301_题库管理_列表',
        moduleName: 'Admin1301_TiKuGuanLi_LieBiao',
        title: '1301_题库管理_列表.vue',
        label: '1301_题库管理_列表.vue',
        name: '1301_题库管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1302_题库管理_新建及修改.vue',
        vueRouter: '/admin_1302_题库管理_新建及修改',
        moduleName: 'Admin1302_TiKuGuanLi_XinJianJiXiuGai',
        title: '1302_题库管理_新建及修改.vue',
        label: '1302_题库管理_新建及修改.vue',
        name: '1302_题库管理_新建及修改.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1401_试题管理_列表.vue',
        vueRouter: '/admin_1401_试题管理_列表',
        moduleName: 'Admin1401_ShiTiGuanLi_LieBiao',
        title: '1401_试题管理_列表.vue',
        label: '1401_试题管理_列表.vue',
        name: '1401_试题管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1402_试题管理_手动创建试题及修改.vue',
        vueRouter: '/admin_1402_试题管理_手动创建试题及修改',
        moduleName: 'Admin1402_ShiTiGuanLi_ShouDongChuangJianShiTiJiXiuGai',
        title: '1402_试题管理_手动创建试题及修改.vue',
        label: '1402_试题管理_手动创建试题及修改.vue',
        name: '1402_试题管理_手动创建试题及修改.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1404_试题管理_关联课程.vue',
        vueRouter: '/admin_1404_试题管理_关联课程',
        moduleName: 'Admin1404_ShiTiGuanLi_GuanLianKeCheng',
        title: '1404_试题管理_关联课程.vue',
        label: '1404_试题管理_关联课程.vue',
        name: '1404_试题管理_关联课程.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1405_试题管理_批量创建试题.vue',
        vueRouter: '/admin_1405_试题管理_批量创建试题',
        moduleName: 'Admin1405_ShiTiGuanLi_PiLiangChuangJianShiTi',
        title: '1405_试题管理_批量创建试题.vue',
        label: '1405_试题管理_批量创建试题.vue',
        name: '1405_试题管理_批量创建试题.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1406_试题管理_试题详情.vue',
        vueRouter: '/admin_1406_试题管理_试题详情',
        moduleName: 'Admin1406_ShiTiGuanLi_ShiTiXiangQing',
        title: '1406_试题管理_试题详情.vue',
        label: '1406_试题管理_试题详情.vue',
        name: '1406_试题管理_试题详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1501_试卷管理_列表.vue',
        vueRouter: '/admin_1501_试卷管理_列表',
        moduleName: 'Admin1501_ShiJuanGuanLi_LieBiao',
        title: '1501_试卷管理_列表.vue',
        label: '1501_试卷管理_列表.vue',
        name: '1501_试卷管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1502_试卷管理_新建及修改.vue',
        vueRouter: '/admin_1502_试卷管理_新建及修改',
        moduleName: 'Admin1502_ShiJuanGuanLi_XinJianJiXiuGai',
        title: '1502_试卷管理_新建及修改.vue',
        label: '1502_试卷管理_新建及修改.vue',
        name: '1502_试卷管理_新建及修改.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1503_试卷管理_添加大题.vue',
        vueRouter: '/admin_1503_试卷管理_添加大题',
        moduleName: 'Admin1503_ShiJuanGuanLi_TianJiaDaTi',
        title: '1503_试卷管理_添加大题.vue',
        label: '1503_试卷管理_添加大题.vue',
        name: '1503_试卷管理_添加大题.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1504_试卷管理_详情.vue',
        vueRouter: '/admin_1504_试卷管理_详情',
        moduleName: 'Admin1504_ShiJuanGuanLi_XiangQing',
        title: '1504_试卷管理_详情.vue',
        label: '1504_试卷管理_详情.vue',
        name: '1504_试卷管理_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1505_试卷管理_试卷分类管理.vue',
        vueRouter: '/admin_1505_试卷管理_试卷分类管理',
        moduleName: 'Admin1505_ShiJuanGuanLi_ShiJuanFenLeiGuanLi',
        title: '1505_试卷管理_试卷分类管理.vue',
        label: '1505_试卷管理_试卷分类管理.vue',
        name: '1505_试卷管理_试卷分类管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1506_试卷管理_试卷分类_弹窗.vue',
        vueRouter: '/admin_1506_试卷管理_试卷分类_弹窗',
        moduleName: 'Admin1506_ShiJuanGuanLi_ShiJuanFenLei_DanChuang',
        title: '1506_试卷管理_试卷分类_弹窗.vue',
        label: '1506_试卷管理_试卷分类_弹窗.vue',
        name: '1506_试卷管理_试卷分类_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1601_课程包管理_列表.vue',
        vueRouter: '/admin_1601_课程包管理_列表',
        moduleName: 'Admin1601_KeChengBaoGuanLi_LieBiao',
        title: '1601_课程包管理_列表.vue',
        label: '1601_课程包管理_列表.vue',
        name: '1601_课程包管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1602_课程包管理_新建及修改.vue',
        vueRouter: '/admin_1602_课程包管理_新建及修改',
        moduleName: 'Admin1602_KeChengBaoGuanLi_XinJianJiXiuGai',
        title: '1602_课程包管理_新建及修改.vue',
        label: '1602_课程包管理_新建及修改.vue',
        name: '1602_课程包管理_新建及修改.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1604_课程包管理_详情.vue',
        vueRouter: '/admin_1604_课程包管理_详情',
        moduleName: 'Admin1604_KeChengBaoGuanLi_XiangQing',
        title: '1604_课程包管理_详情.vue',
        label: '1604_课程包管理_详情.vue',
        name: '1604_课程包管理_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1605_课程包管理_同步培训方案.vue',
        vueRouter: '/admin_1605_课程包管理_同步培训方案',
        moduleName: 'Admin1605_KeChengBaoGuanLi_TongBuPeiXunFangAn',
        title: '1605_课程包管理_同步培训方案.vue',
        label: '1605_课程包管理_同步培训方案.vue',
        name: '1605_课程包管理_同步培训方案.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1606_课程包管理_课程包导入.vue',
        vueRouter: '/admin_1606_课程包管理_课程包导入',
        moduleName: 'Admin1606_KeChengBaoGuanLi_KeChengBaoDaoRu',
        title: '1606_课程包管理_课程包导入.vue',
        label: '1606_课程包管理_课程包导入.vue',
        name: '1606_课程包管理_课程包导入.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1607_课程包管理_弹窗.vue',
        vueRouter: '/admin_1607_课程包管理_弹窗',
        moduleName: 'Admin1607_KeChengBaoGuanLi_DanChuang',
        title: '1607_课程包管理_弹窗.vue',
        label: '1607_课程包管理_弹窗.vue',
        name: '1607_课程包管理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1700_培训方案管理_列表.vue',
        vueRouter: '/admin_1700_培训方案管理_列表',
        moduleName: 'Admin1700_PeiXunFangAnGuanLi_LieBiao',
        title: '1700_培训方案管理_列表.vue',
        label: '1700_培训方案管理_列表.vue',
        name: '1700_培训方案管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1701_培训方案管理_新建及修改_选课规则.vue',
        vueRouter: '/admin_1701_培训方案管理_新建及修改_选课规则',
        moduleName: 'Admin1701_PeiXunFangAnGuanLi_XinJianJiXiuGai_XuanKeGuiZe',
        title: '1701_培训方案管理_新建及修改_选课规则.vue',
        label: '1701_培训方案管理_新建及修改_选课规则.vue',
        name: '1701_培训方案管理_新建及修改_选课规则.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1702_培训方案管理_新建及修改_自主选课.vue',
        vueRouter: '/admin_1702_培训方案管理_新建及修改_自主选课',
        moduleName: 'Admin1702_PeiXunFangAnGuanLi_XinJianJiXiuGai_ZiZhuXuanKe',
        title: '1702_培训方案管理_新建及修改_自主选课.vue',
        label: '1702_培训方案管理_新建及修改_自主选课.vue',
        name: '1702_培训方案管理_新建及修改_自主选课.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1703_培训方案管理_查看培训方案.vue',
        vueRouter: '/admin_1703_培训方案管理_查看培训方案',
        moduleName: 'Admin1703_PeiXunFangAnGuanLi_ChaKanPeiXunFangAn',
        title: '1703_培训方案管理_查看培训方案.vue',
        label: '1703_培训方案管理_查看培训方案.vue',
        name: '1703_培训方案管理_查看培训方案.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1704_培训方案管理_弹窗.vue',
        vueRouter: '/admin_1704_培训方案管理_弹窗',
        moduleName: 'Admin1704_PeiXunFangAnGuanLi_DanChuang',
        title: '1704_培训方案管理_弹窗.vue',
        label: '1704_培训方案管理_弹窗.vue',
        name: '1704_培训方案管理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1705_培训属性值管理.vue',
        vueRouter: '/admin_1705_培训属性值管理',
        moduleName: 'Admin1705_PeiXunShuXingZhiGuanLi',
        title: '1705_培训属性值管理.vue',
        label: '1705_培训属性值管理.vue',
        name: '1705_培训属性值管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1706_培训属性值管理_弹窗.vue',
        vueRouter: '/admin_1706_培训属性值管理_弹窗',
        moduleName: 'Admin1706_PeiXunShuXingZhiGuanLi_DanChuang',
        title: '1706_培训属性值管理_弹窗.vue',
        label: '1706_培训属性值管理_弹窗.vue',
        name: '1706_培训属性值管理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1707_培训方案管理_考试申诉处理.vue',
        vueRouter: '/admin_1707_培训方案管理_考试申诉处理',
        moduleName: 'Admin1707_PeiXunFangAnGuanLi_KaoShiShenSuChuLi',
        title: '1707_培训方案管理_考试申诉处理.vue',
        label: '1707_培训方案管理_考试申诉处理.vue',
        name: '1707_培训方案管理_考试申诉处理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1707_培训方案管理_考试申诉处理_弹窗.vue',
        vueRouter: '/admin_1707_培训方案管理_考试申诉处理_弹窗',
        moduleName: 'Admin1707_PeiXunFangAnGuanLi_KaoShiShenSuChuLi_DanChuang',
        title: '1707_培训方案管理_考试申诉处理_弹窗.vue',
        label: '1707_培训方案管理_考试申诉处理_弹窗.vue',
        name: '1707_培训方案管理_考试申诉处理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1708_培训方案管理_考试申诉处理_审批页.vue',
        vueRouter: '/admin_1708_培训方案管理_考试申诉处理_审批页',
        moduleName: 'Admin1708_PeiXunFangAnGuanLi_KaoShiShenSuChuLi_ShenPiYe',
        title: '1708_培训方案管理_考试申诉处理_审批页.vue',
        label: '1708_培训方案管理_考试申诉处理_审批页.vue',
        name: '1708_培训方案管理_考试申诉处理_审批页.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1709_培训方案管理_考试申诉处理_详情页.vue',
        vueRouter: '/admin_1709_培训方案管理_考试申诉处理_详情页',
        moduleName: 'Admin1709_PeiXunFangAnGuanLi_KaoShiShenSuChuLi_XiangQingYe',
        title: '1709_培训方案管理_考试申诉处理_详情页.vue',
        label: '1709_培训方案管理_考试申诉处理_详情页.vue',
        name: '1709_培训方案管理_考试申诉处理_详情页.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1710_培训方案管理_学习心得_列表.vue',
        vueRouter: '/admin_1710_培训方案管理_学习心得_列表',
        moduleName: 'Admin1710_PeiXunFangAnGuanLi_XueXiXinDe_LieBiao',
        title: '1710_培训方案管理_学习心得_列表.vue',
        label: '1710_培训方案管理_学习心得_列表.vue',
        name: '1710_培训方案管理_学习心得_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1711_培训方案管理_新建及修改_新增主方案.vue',
        vueRouter: '/admin_1711_培训方案管理_新建及修改_新增主方案',
        moduleName: 'Admin1711_PeiXunFangAnGuanLi_XinJianJiXiuGai_XinZengZhuFangAn',
        title: '1711_培训方案管理_新建及修改_新增主方案.vue',
        label: '1711_培训方案管理_新建及修改_新增主方案.vue',
        name: '1711_培训方案管理_新建及修改_新增主方案.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1712_培训方案管理_合并报名管理.vue',
        vueRouter: '/admin_1712_培训方案管理_合并报名管理',
        moduleName: 'Admin1712_PeiXunFangAnGuanLi_HeBingBaoMingGuanLi',
        title: '1712_培训方案管理_合并报名管理.vue',
        label: '1712_培训方案管理_合并报名管理.vue',
        name: '1712_培训方案管理_合并报名管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1713_培训方案管理_公需课课程维护_列表.vue',
        vueRouter: '/admin_1713_培训方案管理_公需课课程维护_列表',
        moduleName: 'Admin1713_PeiXunFangAnGuanLi_GongXuKeKeChengWeiHu_LieBiao',
        title: '1713_培训方案管理_公需课课程维护_列表.vue',
        label: '1713_培训方案管理_公需课课程维护_列表.vue',
        name: '1713_培训方案管理_公需课课程维护_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1714_培训方案管理_公需课课程维护_弹窗.vue',
        vueRouter: '/admin_1714_培训方案管理_公需课课程维护_弹窗',
        moduleName: 'Admin1714_PeiXunFangAnGuanLi_GongXuKeKeChengWeiHu_DanChuang',
        title: '1714_培训方案管理_公需课课程维护_弹窗.vue',
        label: '1714_培训方案管理_公需课课程维护_弹窗.vue',
        name: '1714_培训方案管理_公需课课程维护_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1721_培训方案管理_实施管理_训前实施设置.vue',
        vueRouter: '/admin_1721_培训方案管理_实施管理_训前实施设置',
        moduleName: 'Admin1721_PeiXunFangAnGuanLi_ShiShiGuanLi_XunQianShiShiSheZhi',
        title: '1721_培训方案管理_实施管理_训前实施设置.vue',
        label: '1721_培训方案管理_实施管理_训前实施设置.vue',
        name: '1721_培训方案管理_实施管理_训前实施设置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1722_培训方案管理_实施管理_训前实施设置_弹窗.vue',
        vueRouter: '/admin_1722_培训方案管理_实施管理_训前实施设置_弹窗',
        moduleName: 'Admin1722_PeiXunFangAnGuanLi_ShiShiGuanLi_XunQianShiShiSheZhi_DanChuang',
        title: '1722_培训方案管理_实施管理_训前实施设置_弹窗.vue',
        label: '1722_培训方案管理_实施管理_训前实施设置_弹窗.vue',
        name: '1722_培训方案管理_实施管理_训前实施设置_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1723_培训方案管理_实施管理_培训过程管理.vue',
        vueRouter: '/admin_1723_培训方案管理_实施管理_培训过程管理',
        moduleName: 'Admin1723_PeiXunFangAnGuanLi_ShiShiGuanLi_PeiXunGuoChengGuanLi',
        title: '1723_培训方案管理_实施管理_培训过程管理.vue',
        label: '1723_培训方案管理_实施管理_培训过程管理.vue',
        name: '1723_培训方案管理_实施管理_培训过程管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1724_培训方案管理_实施管理_培训过程管理_教务管理.vue',
        vueRouter: '/admin_1724_培训方案管理_实施管理_培训过程管理_教务管理',
        moduleName: 'Admin1724_PeiXunFangAnGuanLi_ShiShiGuanLi_PeiXunGuoChengGuanLi_JiaoWuGuanLi',
        title: '1724_培训方案管理_实施管理_培训过程管理_教务管理.vue',
        label: '1724_培训方案管理_实施管理_培训过程管理_教务管理.vue',
        name: '1724_培训方案管理_实施管理_培训过程管理_教务管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1725_培训方案管理_实施管理_培训过程管理_问卷管理.vue',
        vueRouter: '/admin_1725_培训方案管理_实施管理_培训过程管理_问卷管理',
        moduleName: 'Admin1725_PeiXunFangAnGuanLi_ShiShiGuanLi_PeiXunGuoChengGuanLi_WenJuanGuanLi',
        title: '1725_培训方案管理_实施管理_培训过程管理_问卷管理.vue',
        label: '1725_培训方案管理_实施管理_培训过程管理_问卷管理.vue',
        name: '1725_培训方案管理_实施管理_培训过程管理_问卷管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1726_培训方案管理_实施管理_培训过程管理_弹窗.vue',
        vueRouter: '/admin_1726_培训方案管理_实施管理_培训过程管理_弹窗',
        moduleName: 'Admin1726_PeiXunFangAnGuanLi_ShiShiGuanLi_PeiXunGuoChengGuanLi_DanChuang',
        title: '1726_培训方案管理_实施管理_培训过程管理_弹窗.vue',
        label: '1726_培训方案管理_实施管理_培训过程管理_弹窗.vue',
        name: '1726_培训方案管理_实施管理_培训过程管理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1727_培训方案管理_实施管理_培训过程管理_查看统计报告.vue',
        vueRouter: '/admin_1727_培训方案管理_实施管理_培训过程管理_查看统计报告',
        moduleName: 'Admin1727_PeiXunFangAnGuanLi_ShiShiGuanLi_PeiXunGuoChengGuanLi_ChaKanTongJiBaoGao',
        title: '1727_培训方案管理_实施管理_培训过程管理_查看统计报告.vue',
        label: '1727_培训方案管理_实施管理_培训过程管理_查看统计报告.vue',
        name: '1727_培训方案管理_实施管理_培训过程管理_查看统计报告.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1728_培训方案管理_实施管理_培训过程管理_查看问卷.vue',
        vueRouter: '/admin_1728_培训方案管理_实施管理_培训过程管理_查看问卷',
        moduleName: 'Admin1728_PeiXunFangAnGuanLi_ShiShiGuanLi_PeiXunGuoChengGuanLi_ChaKanWenJuan',
        title: '1728_培训方案管理_实施管理_培训过程管理_查看问卷.vue',
        label: '1728_培训方案管理_实施管理_培训过程管理_查看问卷.vue',
        name: '1728_培训方案管理_实施管理_培训过程管理_查看问卷.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1729_培训方案管理_实施管理_培训成果管理.vue',
        vueRouter: '/admin_1729_培训方案管理_实施管理_培训成果管理',
        moduleName: 'Admin1729_PeiXunFangAnGuanLi_ShiShiGuanLi_PeiXunChengGuoGuanLi',
        title: '1729_培训方案管理_实施管理_培训成果管理.vue',
        label: '1729_培训方案管理_实施管理_培训成果管理.vue',
        name: '1729_培训方案管理_实施管理_培训成果管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1731_专题管理_列表.vue',
        vueRouter: '/admin_1731_专题管理_列表',
        moduleName: 'Admin1731_ZhuanTiGuanLi_LieBiao',
        title: '1731_专题管理_列表.vue',
        label: '1731_专题管理_列表.vue',
        name: '1731_专题管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1732_专题管理_列表_弹窗.vue',
        vueRouter: '/admin_1732_专题管理_列表_弹窗',
        moduleName: 'Admin1732_ZhuanTiGuanLi_LieBiao_DanChuang',
        title: '1732_专题管理_列表_弹窗.vue',
        label: '1732_专题管理_列表_弹窗.vue',
        name: '1732_专题管理_列表_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1733_专题管理_专题基础配置.vue',
        vueRouter: '/admin_1733_专题管理_专题基础配置',
        moduleName: 'Admin1733_ZhuanTiGuanLi_ZhuanTiJiChuPeiZhi',
        title: '1733_专题管理_专题基础配置.vue',
        label: '1733_专题管理_专题基础配置.vue',
        name: '1733_专题管理_专题基础配置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1734_专题管理_新建专题_基础信息.vue',
        vueRouter: '/admin_1734_专题管理_新建专题_基础信息',
        moduleName: 'Admin1734_ZhuanTiGuanLi_XinJianZhuanTi_JiChuXinXi',
        title: '1734_专题管理_新建专题_基础信息.vue',
        label: '1734_专题管理_新建专题_基础信息.vue',
        name: '1734_专题管理_新建专题_基础信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1734_专题管理_新建专题_基础信息_弹窗.vue',
        vueRouter: '/admin_1734_专题管理_新建专题_基础信息_弹窗',
        moduleName: 'Admin1734_ZhuanTiGuanLi_XinJianZhuanTi_JiChuXinXi_DanChuang',
        title: '1734_专题管理_新建专题_基础信息_弹窗.vue',
        label: '1734_专题管理_新建专题_基础信息_弹窗.vue',
        name: '1734_专题管理_新建专题_基础信息_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1735_专题管理_新建专题_门户信息.vue',
        vueRouter: '/admin_1735_专题管理_新建专题_门户信息',
        moduleName: 'Admin1735_ZhuanTiGuanLi_XinJianZhuanTi_MenHuXinXi',
        title: '1735_专题管理_新建专题_门户信息.vue',
        label: '1735_专题管理_新建专题_门户信息.vue',
        name: '1735_专题管理_新建专题_门户信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1736_专题管理_新建专题_设置培训方案.vue',
        vueRouter: '/admin_1736_专题管理_新建专题_设置培训方案',
        moduleName: 'Admin1736_ZhuanTiGuanLi_XinJianZhuanTi_SheZhiPeiXunFangAn',
        title: '1736_专题管理_新建专题_设置培训方案.vue',
        label: '1736_专题管理_新建专题_设置培训方案.vue',
        name: '1736_专题管理_新建专题_设置培训方案.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1737_专题管理_新建专题_设置培训方案_弹窗.vue',
        vueRouter: '/admin_1737_专题管理_新建专题_设置培训方案_弹窗',
        moduleName: 'Admin1737_ZhuanTiGuanLi_XinJianZhuanTi_SheZhiPeiXunFangAn_DanChuang',
        title: '1737_专题管理_新建专题_设置培训方案_弹窗.vue',
        label: '1737_专题管理_新建专题_设置培训方案_弹窗.vue',
        name: '1737_专题管理_新建专题_设置培训方案_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1738_专题管理_新建专题_保存专题.vue',
        vueRouter: '/admin_1738_专题管理_新建专题_保存专题',
        moduleName: 'Admin1738_ZhuanTiGuanLi_XinJianZhuanTi_BaoCunZhuanTi',
        title: '1738_专题管理_新建专题_保存专题.vue',
        label: '1738_专题管理_新建专题_保存专题.vue',
        name: '1738_专题管理_新建专题_保存专题.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1739_专题管理_编辑专题_基础信息.vue',
        vueRouter: '/admin_1739_专题管理_编辑专题_基础信息',
        moduleName: 'Admin1739_ZhuanTiGuanLi_BianJiZhuanTi_JiChuXinXi',
        title: '1739_专题管理_编辑专题_基础信息.vue',
        label: '1739_专题管理_编辑专题_基础信息.vue',
        name: '1739_专题管理_编辑专题_基础信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1740_专题管理_编辑专题_门户信息.vue',
        vueRouter: '/admin_1740_专题管理_编辑专题_门户信息',
        moduleName: 'Admin1740_ZhuanTiGuanLi_BianJiZhuanTi_MenHuXinXi',
        title: '1740_专题管理_编辑专题_门户信息.vue',
        label: '1740_专题管理_编辑专题_门户信息.vue',
        name: '1740_专题管理_编辑专题_门户信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1741_专题管理_编辑专题_设置培训信息.vue',
        vueRouter: '/admin_1741_专题管理_编辑专题_设置培训信息',
        moduleName: 'Admin1741_ZhuanTiGuanLi_BianJiZhuanTi_SheZhiPeiXunXinXi',
        title: '1741_专题管理_编辑专题_设置培训信息.vue',
        label: '1741_专题管理_编辑专题_设置培训信息.vue',
        name: '1741_专题管理_编辑专题_设置培训信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1741_专题管理_编辑专题_设置培训信息_修改销售配置弹窗.vue',
        vueRouter: '/admin_1741_专题管理_编辑专题_设置培训信息_修改销售配置弹窗',
        moduleName: 'Admin1741_ZhuanTiGuanLi_BianJiZhuanTi_SheZhiPeiXunXinXi_XiuGaiXiaoShouPeiZhiDanChuang',
        title: '1741_专题管理_编辑专题_设置培训信息_修改销售配置弹窗.vue',
        label: '1741_专题管理_编辑专题_设置培训信息_修改销售配置弹窗.vue',
        name: '1741_专题管理_编辑专题_设置培训信息_修改销售配置弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1742_专题管理_编辑专题_精品课程.vue',
        vueRouter: '/admin_1742_专题管理_编辑专题_精品课程',
        moduleName: 'Admin1742_ZhuanTiGuanLi_BianJiZhuanTi_JingPinKeCheng',
        title: '1742_专题管理_编辑专题_精品课程.vue',
        label: '1742_专题管理_编辑专题_精品课程.vue',
        name: '1742_专题管理_编辑专题_精品课程.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1742_专题管理_编辑专题_精品课程_选择课程.vue',
        vueRouter: '/admin_1742_专题管理_编辑专题_精品课程_选择课程',
        moduleName: 'Admin1742_ZhuanTiGuanLi_BianJiZhuanTi_JingPinKeCheng_XuanZeKeCheng',
        title: '1742_专题管理_编辑专题_精品课程_选择课程.vue',
        label: '1742_专题管理_编辑专题_精品课程_选择课程.vue',
        name: '1742_专题管理_编辑专题_精品课程_选择课程.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1743_专题管理_编辑专题_线上集体报名.vue',
        vueRouter: '/admin_1743_专题管理_编辑专题_线上集体报名',
        moduleName: 'Admin1743_ZhuanTiGuanLi_BianJiZhuanTi_XianShangJiTiBaoMing',
        title: '1743_专题管理_编辑专题_线上集体报名.vue',
        label: '1743_专题管理_编辑专题_线上集体报名.vue',
        name: '1743_专题管理_编辑专题_线上集体报名.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1744_专题管理_编辑专题_线下集体报名.vue',
        vueRouter: '/admin_1744_专题管理_编辑专题_线下集体报名',
        moduleName: 'Admin1744_ZhuanTiGuanLi_BianJiZhuanTi_XianXiaJiTiBaoMing',
        title: '1744_专题管理_编辑专题_线下集体报名.vue',
        label: '1744_专题管理_编辑专题_线下集体报名.vue',
        name: '1744_专题管理_编辑专题_线下集体报名.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1751_专题管理_专题资讯管理_列表.vue',
        vueRouter: '/admin_1751_专题管理_专题资讯管理_列表',
        moduleName: 'Admin1751_ZhuanTiGuanLi_ZhuanTiZiXunGuanLi_LieBiao',
        title: '1751_专题管理_专题资讯管理_列表.vue',
        label: '1751_专题管理_专题资讯管理_列表.vue',
        name: '1751_专题管理_专题资讯管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1752_专题管理_专题资讯管理_新建及修改.vue',
        vueRouter: '/admin_1752_专题管理_专题资讯管理_新建及修改',
        moduleName: 'Admin1752_ZhuanTiGuanLi_ZhuanTiZiXunGuanLi_XinJianJiXiuGai',
        title: '1752_专题管理_专题资讯管理_新建及修改.vue',
        label: '1752_专题管理_专题资讯管理_新建及修改.vue',
        name: '1752_专题管理_专题资讯管理_新建及修改.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1753_专题管理_专题资讯管理_弹窗.vue',
        vueRouter: '/admin_1753_专题管理_专题资讯管理_弹窗',
        moduleName: 'Admin1753_ZhuanTiGuanLi_ZhuanTiZiXunGuanLi_DanChuang',
        title: '1753_专题管理_专题资讯管理_弹窗.vue',
        label: '1753_专题管理_专题资讯管理_弹窗.vue',
        name: '1753_专题管理_专题资讯管理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1761_专题管理员_列表.vue',
        vueRouter: '/admin_1761_专题管理员_列表',
        moduleName: 'Admin1761_ZhuanTiGuanLiYuan_LieBiao',
        title: '1761_专题管理员_列表.vue',
        label: '1761_专题管理员_列表.vue',
        name: '1761_专题管理员_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1762_专题管理员_新增及修改.vue',
        vueRouter: '/admin_1762_专题管理员_新增及修改',
        moduleName: 'Admin1762_ZhuanTiGuanLiYuan_XinZengJiXiuGai',
        title: '1762_专题管理员_新增及修改.vue',
        label: '1762_专题管理员_新增及修改.vue',
        name: '1762_专题管理员_新增及修改.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1763_专题管理员_弹窗.vue',
        vueRouter: '/admin_1763_专题管理员_弹窗',
        moduleName: 'Admin1763_ZhuanTiGuanLiYuan_DanChuang',
        title: '1763_专题管理员_弹窗.vue',
        label: '1763_专题管理员_弹窗.vue',
        name: '1763_专题管理员_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1801_个人报名订单_列表.vue',
        vueRouter: '/admin_1801_个人报名订单_列表',
        moduleName: 'Admin1801_GeRenBaoMingDingDan_LieBiao',
        title: '1801_个人报名订单_列表.vue',
        label: '1801_个人报名订单_列表.vue',
        name: '1801_个人报名订单_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1801_个人报名订单_列表（华医网）.vue',
        vueRouter: '/admin_1801_个人报名订单_列表（华医网）',
        moduleName: 'Admin1801_GeRenBaoMingDingDan_LieBiaoHuaYiWang',
        title: '1801_个人报名订单_列表（华医网）.vue',
        label: '1801_个人报名订单_列表（华医网）.vue',
        name: '1801_个人报名订单_列表（华医网）.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1802_个人报名订单_详情.vue',
        vueRouter: '/admin_1802_个人报名订单_详情',
        moduleName: 'Admin1802_GeRenBaoMingDingDan_XiangQing',
        title: '1802_个人报名订单_详情.vue',
        label: '1802_个人报名订单_详情.vue',
        name: '1802_个人报名订单_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1802_个人报名订单_详情（华医网）.vue',
        vueRouter: '/admin_1802_个人报名订单_详情（华医网）',
        moduleName: 'Admin1802_GeRenBaoMingDingDan_XiangQingHuaYiWang',
        title: '1802_个人报名订单_详情（华医网）.vue',
        label: '1802_个人报名订单_详情（华医网）.vue',
        name: '1802_个人报名订单_详情（华医网）.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1804_个人报名订单_弹窗.vue',
        vueRouter: '/admin_1804_个人报名订单_弹窗',
        moduleName: 'Admin1804_GeRenBaoMingDingDan_DanChuang',
        title: '1804_个人报名订单_弹窗.vue',
        label: '1804_个人报名订单_弹窗.vue',
        name: '1804_个人报名订单_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1901_集体报名订单_列表.vue',
        vueRouter: '/admin_1901_集体报名订单_列表',
        moduleName: 'Admin1901_JiTiBaoMingDingDan_LieBiao',
        title: '1901_集体报名订单_列表.vue',
        label: '1901_集体报名订单_列表.vue',
        name: '1901_集体报名订单_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1902_集体报名订单_其他详情.vue',
        vueRouter: '/admin_1902_集体报名订单_其他详情',
        moduleName: 'Admin1902_JiTiBaoMingDingDan_QiTaXiangQing',
        title: '1902_集体报名订单_其他详情.vue',
        label: '1902_集体报名订单_其他详情.vue',
        name: '1902_集体报名订单_其他详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1902_集体报名订单_待下单详情.vue',
        vueRouter: '/admin_1902_集体报名订单_待下单详情',
        moduleName: 'Admin1902_JiTiBaoMingDingDan_DaiXiaDanXiangQing',
        title: '1902_集体报名订单_待下单详情.vue',
        label: '1902_集体报名订单_待下单详情.vue',
        name: '1902_集体报名订单_待下单详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1904_集体报名订单_批次内主订单详情.vue',
        vueRouter: '/admin_1904_集体报名订单_批次内主订单详情',
        moduleName: 'Admin1904_JiTiBaoMingDingDan_PiCiNeiZhuDingDanXiangQing',
        title: '1904_集体报名订单_批次内主订单详情.vue',
        label: '1904_集体报名订单_批次内主订单详情.vue',
        name: '1904_集体报名订单_批次内主订单详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/1905_集体报名订单_弹窗.vue',
        vueRouter: '/admin_1905_集体报名订单_弹窗',
        moduleName: 'Admin1905_JiTiBaoMingDingDan_DanChuang',
        title: '1905_集体报名订单_弹窗.vue',
        label: '1905_集体报名订单_弹窗.vue',
        name: '1905_集体报名订单_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2001_个人报名退款_列表.vue',
        vueRouter: '/admin_2001_个人报名退款_列表',
        moduleName: 'Admin2001_GeRenBaoMingTuiKuan_LieBiao',
        title: '2001_个人报名退款_列表.vue',
        label: '2001_个人报名退款_列表.vue',
        name: '2001_个人报名退款_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2002_个人报名退款_详情.vue',
        vueRouter: '/admin_2002_个人报名退款_详情',
        moduleName: 'Admin2002_GeRenBaoMingTuiKuan_XiangQing',
        title: '2002_个人报名退款_详情.vue',
        label: '2002_个人报名退款_详情.vue',
        name: '2002_个人报名退款_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2003_个人报名退款_弹窗.vue',
        vueRouter: '/admin_2003_个人报名退款_弹窗',
        moduleName: 'Admin2003_GeRenBaoMingTuiKuan_DanChuang',
        title: '2003_个人报名退款_弹窗.vue',
        label: '2003_个人报名退款_弹窗.vue',
        name: '2003_个人报名退款_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2101_集体报名退款_列表.vue',
        vueRouter: '/admin_2101_集体报名退款_列表',
        moduleName: 'Admin2101_JiTiBaoMingTuiKuan_LieBiao',
        title: '2101_集体报名退款_列表.vue',
        label: '2101_集体报名退款_列表.vue',
        name: '2101_集体报名退款_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2102_集体报名退款_详情.vue',
        vueRouter: '/admin_2102_集体报名退款_详情',
        moduleName: 'Admin2102_JiTiBaoMingTuiKuan_XiangQing',
        title: '2102_集体报名退款_详情.vue',
        label: '2102_集体报名退款_详情.vue',
        name: '2102_集体报名退款_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2103_集体报名退款_弹窗.vue',
        vueRouter: '/admin_2103_集体报名退款_弹窗',
        moduleName: 'Admin2103_JiTiBaoMingTuiKuan_DanChuang',
        title: '2103_集体报名退款_弹窗.vue',
        label: '2103_集体报名退款_弹窗.vue',
        name: '2103_集体报名退款_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2104_集体报名退款_具体订单退款详情.vue',
        vueRouter: '/admin_2104_集体报名退款_具体订单退款详情',
        moduleName: 'Admin2104_JiTiBaoMingTuiKuan_JuTiDingDanTuiKuanXiangQing',
        title: '2104_集体报名退款_具体订单退款详情.vue',
        label: '2104_集体报名退款_具体订单退款详情.vue',
        name: '2104_集体报名退款_具体订单退款详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2105_部分退款_弹窗.vue',
        vueRouter: '/admin_2105_部分退款_弹窗',
        moduleName: 'Admin2105_BuFenTuiKuan_DanChuang',
        title: '2105_部分退款_弹窗.vue',
        label: '2105_部分退款_弹窗.vue',
        name: '2105_部分退款_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2106_(华医网)组合班部分退款_弹窗.vue',
        vueRouter: '/admin_2106_(华医网)组合班部分退款_弹窗',
        moduleName: 'Admin2106_HuaYiWangZuHeBanBuFenTuiKuan_DanChuang',
        title: '2106_(华医网)组合班部分退款_弹窗.vue',
        label: '2106_(华医网)组合班部分退款_弹窗.vue',
        name: '2106_(华医网)组合班部分退款_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2201_个人发票管理_增值税电子普通发票(自动开票).vue',
        vueRouter: '/admin_2201_个人发票管理_增值税电子普通发票(自动开票)',
        moduleName: 'Admin2201_GeRenFaPiaoGuanLi_ZengZhiShuiDianZiPuTongFaPiaoZiDongKaiPiao',
        title: '2201_个人发票管理_增值税电子普通发票(自动开票).vue',
        label: '2201_个人发票管理_增值税电子普通发票(自动开票).vue',
        name: '2201_个人发票管理_增值税电子普通发票(自动开票).vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2202_个人发票管理_增值税电子普通发票(线下开票) .vue',
        vueRouter: '/admin_2202_个人发票管理_增值税电子普通发票(线下开票) ',
        moduleName: 'Admin2202_GeRenFaPiaoGuanLi_ZengZhiShuiDianZiPuTongFaPiaoXianXiaKaiPiao',
        title: '2202_个人发票管理_增值税电子普通发票(线下开票) .vue',
        label: '2202_个人发票管理_增值税电子普通发票(线下开票) .vue',
        name: '2202_个人发票管理_增值税电子普通发票(线下开票) .vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2203_个人发票管理_增值税电子专用发票(线下开票) .vue',
        vueRouter: '/admin_2203_个人发票管理_增值税电子专用发票(线下开票) ',
        moduleName: 'Admin2203_GeRenFaPiaoGuanLi_ZengZhiShuiDianZiZhuanYongFaPiaoXianXiaKaiPiao',
        title: '2203_个人发票管理_增值税电子专用发票(线下开票) .vue',
        label: '2203_个人发票管理_增值税电子专用发票(线下开票) .vue',
        name: '2203_个人发票管理_增值税电子专用发票(线下开票) .vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2204_个人发票管理_增值税专用发票(纸质票).vue',
        vueRouter: '/admin_2204_个人发票管理_增值税专用发票(纸质票)',
        moduleName: 'Admin2204_GeRenFaPiaoGuanLi_ZengZhiShuiZhuanYongFaPiaoZhiZhiPiao',
        title: '2204_个人发票管理_增值税专用发票(纸质票).vue',
        label: '2204_个人发票管理_增值税专用发票(纸质票).vue',
        name: '2204_个人发票管理_增值税专用发票(纸质票).vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2205_个人发票管理_发票配送.vue',
        vueRouter: '/admin_2205_个人发票管理_发票配送',
        moduleName: 'Admin2205_GeRenFaPiaoGuanLi_FaPiaoPeiSong',
        title: '2205_个人发票管理_发票配送.vue',
        label: '2205_个人发票管理_发票配送.vue',
        name: '2205_个人发票管理_发票配送.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2205_集体报名发票管理_增值税电子普通发票(自动开票).vue',
        vueRouter: '/admin_2205_集体报名发票管理_增值税电子普通发票(自动开票)',
        moduleName: 'Admin2205_JiTiBaoMingFaPiaoGuanLi_ZengZhiShuiDianZiPuTongFaPiaoZiDongKaiPiao',
        title: '2205_集体报名发票管理_增值税电子普通发票(自动开票).vue',
        label: '2205_集体报名发票管理_增值税电子普通发票(自动开票).vue',
        name: '2205_集体报名发票管理_增值税电子普通发票(自动开票).vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2206_集体报名发票管理_增值税电子普通发票(线下开票) .vue',
        vueRouter: '/admin_2206_集体报名发票管理_增值税电子普通发票(线下开票) ',
        moduleName: 'Admin2206_JiTiBaoMingFaPiaoGuanLi_ZengZhiShuiDianZiPuTongFaPiaoXianXiaKaiPiao',
        title: '2206_集体报名发票管理_增值税电子普通发票(线下开票) .vue',
        label: '2206_集体报名发票管理_增值税电子普通发票(线下开票) .vue',
        name: '2206_集体报名发票管理_增值税电子普通发票(线下开票) .vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2207_集体报名发票管理_增值税电子专用发票(线下开票).vue',
        vueRouter: '/admin_2207_集体报名发票管理_增值税电子专用发票(线下开票)',
        moduleName: 'Admin2207_JiTiBaoMingFaPiaoGuanLi_ZengZhiShuiDianZiZhuanYongFaPiaoXianXiaKaiPiao',
        title: '2207_集体报名发票管理_增值税电子专用发票(线下开票).vue',
        label: '2207_集体报名发票管理_增值税电子专用发票(线下开票).vue',
        name: '2207_集体报名发票管理_增值税电子专用发票(线下开票).vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2208_集体报名发票管理_增值税专用发票(纸质票).vue',
        vueRouter: '/admin_2208_集体报名发票管理_增值税专用发票(纸质票)',
        moduleName: 'Admin2208_JiTiBaoMingFaPiaoGuanLi_ZengZhiShuiZhuanYongFaPiaoZhiZhiPiao',
        title: '2208_集体报名发票管理_增值税专用发票(纸质票).vue',
        label: '2208_集体报名发票管理_增值税专用发票(纸质票).vue',
        name: '2208_集体报名发票管理_增值税专用发票(纸质票).vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2209_集体报名发票管理_发票配送 .vue',
        vueRouter: '/admin_2209_集体报名发票管理_发票配送 ',
        moduleName: 'Admin2209_JiTiBaoMingFaPiaoGuanLi_FaPiaoPeiSong',
        title: '2209_集体报名发票管理_发票配送 .vue',
        label: '2209_集体报名发票管理_发票配送 .vue',
        name: '2209_集体报名发票管理_发票配送 .vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2210_发票管理_弹窗.vue',
        vueRouter: '/admin_2210_发票管理_弹窗',
        moduleName: 'Admin2210_FaPiaoGuanLi_DanChuang',
        title: '2210_发票管理_弹窗.vue',
        label: '2210_发票管理_弹窗.vue',
        name: '2210_发票管理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2301_个人报名对账_报名订单.vue',
        vueRouter: '/admin_2301_个人报名对账_报名订单',
        moduleName: 'Admin2301_GeRenBaoMingDuiZhang_BaoMingDingDan',
        title: '2301_个人报名对账_报名订单.vue',
        label: '2301_个人报名对账_报名订单.vue',
        name: '2301_个人报名对账_报名订单.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2302_个人报名对账_退款订单.vue',
        vueRouter: '/admin_2302_个人报名对账_退款订单',
        moduleName: 'Admin2302_GeRenBaoMingDuiZhang_TuiKuanDingDan',
        title: '2302_个人报名对账_退款订单.vue',
        label: '2302_个人报名对账_退款订单.vue',
        name: '2302_个人报名对账_退款订单.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2303_集体报名对账_报名订单.vue',
        vueRouter: '/admin_2303_集体报名对账_报名订单',
        moduleName: 'Admin2303_JiTiBaoMingDuiZhang_BaoMingDingDan',
        title: '2303_集体报名对账_报名订单.vue',
        label: '2303_集体报名对账_报名订单.vue',
        name: '2303_集体报名对账_报名订单.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2304_集体报名对账_退款订单.vue',
        vueRouter: '/admin_2304_集体报名对账_退款订单',
        moduleName: 'Admin2304_JiTiBaoMingDuiZhang_TuiKuanDingDan',
        title: '2304_集体报名对账_退款订单.vue',
        label: '2304_集体报名对账_退款订单.vue',
        name: '2304_集体报名对账_退款订单.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2501_导入开通_导入学员.vue',
        vueRouter: '/admin_2501_导入开通_导入学员',
        moduleName: 'Admin2501_DaoRuKaiTong_DaoRuXueYuan',
        title: '2501_导入开通_导入学员.vue',
        label: '2501_导入开通_导入学员.vue',
        name: '2501_导入开通_导入学员.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2502_导入开通_导入学员并开班.vue',
        vueRouter: '/admin_2502_导入开通_导入学员并开班',
        moduleName: 'Admin2502_DaoRuKaiTong_DaoRuXueYuanBingKaiBan',
        title: '2502_导入开通_导入学员并开班.vue',
        label: '2502_导入开通_导入学员并开班.vue',
        name: '2502_导入开通_导入学员并开班.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2502_导入开通_导入学员并开班（智能学习）.vue',
        vueRouter: '/admin_2502_导入开通_导入学员并开班（智能学习）',
        moduleName: 'Admin2502_DaoRuKaiTong_DaoRuXueYuanBingKaiBanZhiNengXueXi',
        title: '2502_导入开通_导入学员并开班（智能学习）.vue',
        label: '2502_导入开通_导入学员并开班（智能学习）.vue',
        name: '2502_导入开通_导入学员并开班（智能学习）.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2503_导入开通_查询培训方案.vue',
        vueRouter: '/admin_2503_导入开通_查询培训方案',
        moduleName: 'Admin2503_DaoRuKaiTong_ChaXunPeiXunFangAn',
        title: '2503_导入开通_查询培训方案.vue',
        label: '2503_导入开通_查询培训方案.vue',
        name: '2503_导入开通_查询培训方案.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2503_导入开通_查询培训方案（智能学习）.vue',
        vueRouter: '/admin_2503_导入开通_查询培训方案（智能学习）',
        moduleName: 'Admin2503_DaoRuKaiTong_ChaXunPeiXunFangAnZhiNengXueXi',
        title: '2503_导入开通_查询培训方案（智能学习）.vue',
        label: '2503_导入开通_查询培训方案（智能学习）.vue',
        name: '2503_导入开通_查询培训方案（智能学习）.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2504_导入开通_导入开通结果跟踪.vue',
        vueRouter: '/admin_2504_导入开通_导入开通结果跟踪',
        moduleName: 'Admin2504_DaoRuKaiTong_DaoRuKaiTongJieGuoGenZong',
        title: '2504_导入开通_导入开通结果跟踪.vue',
        label: '2504_导入开通_导入开通结果跟踪.vue',
        name: '2504_导入开通_导入开通结果跟踪.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2505_导入开通_弹窗.vue',
        vueRouter: '/admin_2505_导入开通_弹窗',
        moduleName: 'Admin2505_DaoRuKaiTong_DanChuang',
        title: '2505_导入开通_弹窗.vue',
        label: '2505_导入开通_弹窗.vue',
        name: '2505_导入开通_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2506_导入开通_选择专题.vue',
        vueRouter: '/admin_2506_导入开通_选择专题',
        moduleName: 'Admin2506_DaoRuKaiTong_XuanZeZhuanTi',
        title: '2506_导入开通_选择专题.vue',
        label: '2506_导入开通_选择专题.vue',
        name: '2506_导入开通_选择专题.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2507_导入开通_选择下载模板.vue',
        vueRouter: '/admin_2507_导入开通_选择下载模板',
        moduleName: 'Admin2507_DaoRuKaiTong_XuanZeXiaZaiMoBan',
        title: '2507_导入开通_选择下载模板.vue',
        label: '2507_导入开通_选择下载模板.vue',
        name: '2507_导入开通_选择下载模板.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2508_导入开通_查看期别.vue',
        vueRouter: '/admin_2508_导入开通_查看期别',
        moduleName: 'Admin2508_DaoRuKaiTong_ChaKanQiBie',
        title: '2508_导入开通_查看期别.vue',
        label: '2508_导入开通_查看期别.vue',
        name: '2508_导入开通_查看期别.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2509_导入开通_查看编排日志详情.vue',
        vueRouter: '/admin_2509_导入开通_查看编排日志详情',
        moduleName: 'Admin2509_DaoRuKaiTong_ChaKanBianPaiRiZhiXiangQing',
        title: '2509_导入开通_查看编排日志详情.vue',
        label: '2509_导入开通_查看编排日志详情.vue',
        name: '2509_导入开通_查看编排日志详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2601_客服管理_业务咨询_学员信息.vue',
        vueRouter: '/admin_2601_客服管理_业务咨询_学员信息',
        moduleName: 'Admin2601_KeFuGuanLi_YeWuZiXun_XueYuanXinXi',
        title: '2601_客服管理_业务咨询_学员信息.vue',
        label: '2601_客服管理_业务咨询_学员信息.vue',
        name: '2601_客服管理_业务咨询_学员信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2601_客服管理_业务咨询_学员信息（欣宇云锦金昌市）.vue',
        vueRouter: '/admin_2601_客服管理_业务咨询_学员信息（欣宇云锦金昌市）',
        moduleName: 'Admin2601_KeFuGuanLi_YeWuZiXun_XueYuanXinXiXinYuYunJinJinChangShi',
        title: '2601_客服管理_业务咨询_学员信息（欣宇云锦金昌市）.vue',
        label: '2601_客服管理_业务咨询_学员信息（欣宇云锦金昌市）.vue',
        name: '2601_客服管理_业务咨询_学员信息（欣宇云锦金昌市）.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2601_客服管理_业务咨询_学员信息（欣宇云锦金昌市）_编辑弹窗.vue',
        vueRouter: '/admin_2601_客服管理_业务咨询_学员信息（欣宇云锦金昌市）_编辑弹窗',
        moduleName: 'Admin2601_KeFuGuanLi_YeWuZiXun_XueYuanXinXiXinYuYunJinJinChangShi_BianJiDanChuang',
        title: '2601_客服管理_业务咨询_学员信息（欣宇云锦金昌市）_编辑弹窗.vue',
        label: '2601_客服管理_业务咨询_学员信息（欣宇云锦金昌市）_编辑弹窗.vue',
        name: '2601_客服管理_业务咨询_学员信息（欣宇云锦金昌市）_编辑弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2602_客服管理_业务咨询_学习内容.vue',
        vueRouter: '/admin_2602_客服管理_业务咨询_学习内容',
        moduleName: 'Admin2602_KeFuGuanLi_YeWuZiXun_XueXiNeiRong',
        title: '2602_客服管理_业务咨询_学习内容.vue',
        label: '2602_客服管理_业务咨询_学习内容.vue',
        name: '2602_客服管理_业务咨询_学习内容.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2602_客服管理_业务咨询_学习内容（华医网）.vue',
        vueRouter: '/admin_2602_客服管理_业务咨询_学习内容（华医网）',
        moduleName: 'Admin2602_KeFuGuanLi_YeWuZiXun_XueXiNeiRongHuaYiWang',
        title: '2602_客服管理_业务咨询_学习内容（华医网）.vue',
        label: '2602_客服管理_业务咨询_学习内容（华医网）.vue',
        name: '2602_客服管理_业务咨询_学习内容（华医网）.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2603_客服管理_业务咨询_售后信息.vue',
        vueRouter: '/admin_2603_客服管理_业务咨询_售后信息',
        moduleName: 'Admin2603_KeFuGuanLi_YeWuZiXun_ShouHouXinXi',
        title: '2603_客服管理_业务咨询_售后信息.vue',
        label: '2603_客服管理_业务咨询_售后信息.vue',
        name: '2603_客服管理_业务咨询_售后信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2603_客服管理_业务咨询_换班信息（华医网）.vue',
        vueRouter: '/admin_2603_客服管理_业务咨询_换班信息（华医网）',
        moduleName: 'Admin2603_KeFuGuanLi_YeWuZiXun_HuanBanXinXiHuaYiWang',
        title: '2603_客服管理_业务咨询_换班信息（华医网）.vue',
        label: '2603_客服管理_业务咨询_换班信息（华医网）.vue',
        name: '2603_客服管理_业务咨询_换班信息（华医网）.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2604_客服管理_业务咨询_订单信息.vue',
        vueRouter: '/admin_2604_客服管理_业务咨询_订单信息',
        moduleName: 'Admin2604_KeFuGuanLi_YeWuZiXun_DingDanXinXi',
        title: '2604_客服管理_业务咨询_订单信息.vue',
        label: '2604_客服管理_业务咨询_订单信息.vue',
        name: '2604_客服管理_业务咨询_订单信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2605_客服管理_业务咨询_发票信息.vue',
        vueRouter: '/admin_2605_客服管理_业务咨询_发票信息',
        moduleName: 'Admin2605_KeFuGuanLi_YeWuZiXun_FaPiaoXinXi',
        title: '2605_客服管理_业务咨询_发票信息.vue',
        label: '2605_客服管理_业务咨询_发票信息.vue',
        name: '2605_客服管理_业务咨询_发票信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2606_客服管理_业务咨询_退款信息.vue',
        vueRouter: '/admin_2606_客服管理_业务咨询_退款信息',
        moduleName: 'Admin2606_KeFuGuanLi_YeWuZiXun_TuiKuanXinXi',
        title: '2606_客服管理_业务咨询_退款信息.vue',
        label: '2606_客服管理_业务咨询_退款信息.vue',
        name: '2606_客服管理_业务咨询_退款信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2607_客服管理_业务咨询_培训档案.vue',
        vueRouter: '/admin_2607_客服管理_业务咨询_培训档案',
        moduleName: 'Admin2607_KeFuGuanLi_YeWuZiXun_PeiXunDangAn',
        title: '2607_客服管理_业务咨询_培训档案.vue',
        label: '2607_客服管理_业务咨询_培训档案.vue',
        name: '2607_客服管理_业务咨询_培训档案.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2608_客服管理_业务咨询_弹窗.vue',
        vueRouter: '/admin_2608_客服管理_业务咨询_弹窗',
        moduleName: 'Admin2608_KeFuGuanLi_YeWuZiXun_DanChuang',
        title: '2608_客服管理_业务咨询_弹窗.vue',
        label: '2608_客服管理_业务咨询_弹窗.vue',
        name: '2608_客服管理_业务咨询_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2701_客服管理_集体报名咨询_帐号信息.vue',
        vueRouter: '/admin_2701_客服管理_集体报名咨询_帐号信息',
        moduleName: 'Admin2701_KeFuGuanLi_JiTiBaoMingZiXun_ZhangHaoXinXi',
        title: '2701_客服管理_集体报名咨询_帐号信息.vue',
        label: '2701_客服管理_集体报名咨询_帐号信息.vue',
        name: '2701_客服管理_集体报名咨询_帐号信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2702_客服管理_集体报名咨询_批次信息.vue',
        vueRouter: '/admin_2702_客服管理_集体报名咨询_批次信息',
        moduleName: 'Admin2702_KeFuGuanLi_JiTiBaoMingZiXun_PiCiXinXi',
        title: '2702_客服管理_集体报名咨询_批次信息.vue',
        label: '2702_客服管理_集体报名咨询_批次信息.vue',
        name: '2702_客服管理_集体报名咨询_批次信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2703_客服管理_集体报名咨询_发票信息.vue',
        vueRouter: '/admin_2703_客服管理_集体报名咨询_发票信息',
        moduleName: 'Admin2703_KeFuGuanLi_JiTiBaoMingZiXun_FaPiaoXinXi',
        title: '2703_客服管理_集体报名咨询_发票信息.vue',
        label: '2703_客服管理_集体报名咨询_发票信息.vue',
        name: '2703_客服管理_集体报名咨询_发票信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2704_客服管理_集体报名咨询_退款信息.vue',
        vueRouter: '/admin_2704_客服管理_集体报名咨询_退款信息',
        moduleName: 'Admin2704_KeFuGuanLi_JiTiBaoMingZiXun_TuiKuanXinXi',
        title: '2704_客服管理_集体报名咨询_退款信息.vue',
        label: '2704_客服管理_集体报名咨询_退款信息.vue',
        name: '2704_客服管理_集体报名咨询_退款信息.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2705_客服管理_集体报名咨询_学员名单.vue',
        vueRouter: '/admin_2705_客服管理_集体报名咨询_学员名单',
        moduleName: 'Admin2705_KeFuGuanLi_JiTiBaoMingZiXun_XueYuanMingDan',
        title: '2705_客服管理_集体报名咨询_学员名单.vue',
        label: '2705_客服管理_集体报名咨询_学员名单.vue',
        name: '2705_客服管理_集体报名咨询_学员名单.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2706_客服管理_集体报名咨询_弹窗.vue',
        vueRouter: '/admin_2706_客服管理_集体报名咨询_弹窗',
        moduleName: 'Admin2706_KeFuGuanLi_JiTiBaoMingZiXun_DanChuang',
        title: '2706_客服管理_集体报名咨询_弹窗.vue',
        label: '2706_客服管理_集体报名咨询_弹窗.vue',
        name: '2706_客服管理_集体报名咨询_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2801_批量打印证明.vue',
        vueRouter: '/admin_2801_批量打印证明',
        moduleName: 'Admin2801_PiLiangDaYinZhengMing',
        title: '2801_批量打印证明.vue',
        label: '2801_批量打印证明.vue',
        name: '2801_批量打印证明.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2802_批量打印证明_导入名单打印.vue',
        vueRouter: '/admin_2802_批量打印证明_导入名单打印',
        moduleName: 'Admin2802_PiLiangDaYinZhengMing_DaoRuMingDanDaYin',
        title: '2802_批量打印证明_导入名单打印.vue',
        label: '2802_批量打印证明_导入名单打印.vue',
        name: '2802_批量打印证明_导入名单打印.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2803_批量打印证明_弹窗.vue',
        vueRouter: '/admin_2803_批量打印证明_弹窗',
        moduleName: 'Admin2803_PiLiangDaYinZhengMing_DanChuang',
        title: '2803_批量打印证明_弹窗.vue',
        label: '2803_批量打印证明_弹窗.vue',
        name: '2803_批量打印证明_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2901_用户管理_学员管理.vue',
        vueRouter: '/admin_2901_用户管理_学员管理',
        moduleName: 'Admin2901_YongHuGuanLi_XueYuanGuanLi',
        title: '2901_用户管理_学员管理.vue',
        label: '2901_用户管理_学员管理.vue',
        name: '2901_用户管理_学员管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2901_用户管理_学员管理（华医网）.vue',
        vueRouter: '/admin_2901_用户管理_学员管理（华医网）',
        moduleName: 'Admin2901_YongHuGuanLi_XueYuanGuanLiHuaYiWang',
        title: '2901_用户管理_学员管理（华医网）.vue',
        label: '2901_用户管理_学员管理（华医网）.vue',
        name: '2901_用户管理_学员管理（华医网）.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2902_用户管理_学员详情.vue',
        vueRouter: '/admin_2902_用户管理_学员详情',
        moduleName: 'Admin2902_YongHuGuanLi_XueYuanXiangQing',
        title: '2902_用户管理_学员详情.vue',
        label: '2902_用户管理_学员详情.vue',
        name: '2902_用户管理_学员详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2902_用户管理_学员详情（欣宇云锦金昌市）.vue',
        vueRouter: '/admin_2902_用户管理_学员详情（欣宇云锦金昌市）',
        moduleName: 'Admin2902_YongHuGuanLi_XueYuanXiangQingXinYuYunJinJinChangShi',
        title: '2902_用户管理_学员详情（欣宇云锦金昌市）.vue',
        label: '2902_用户管理_学员详情（欣宇云锦金昌市）.vue',
        name: '2902_用户管理_学员详情（欣宇云锦金昌市）.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/2903_用户管理_集体报名帐号.vue',
        vueRouter: '/admin_2903_用户管理_集体报名帐号',
        moduleName: 'Admin2903_YongHuGuanLi_JiTiBaoMingZhangHao',
        title: '2903_用户管理_集体报名帐号.vue',
        label: '2903_用户管理_集体报名帐号.vue',
        name: '2903_用户管理_集体报名帐号.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3001_导入任务管理.vue',
        vueRouter: '/admin_3001_导入任务管理',
        moduleName: 'Admin3001_DaoRuRenWuGuanLi',
        title: '3001_导入任务管理.vue',
        label: '3001_导入任务管理.vue',
        name: '3001_导入任务管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3002_导出任务管理.vue',
        vueRouter: '/admin_3002_导出任务管理',
        moduleName: 'Admin3002_DaoChuRenWuGuanLi',
        title: '3002_导出任务管理.vue',
        label: '3002_导出任务管理.vue',
        name: '3002_导出任务管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3003_导入导出任务_弹窗.vue',
        vueRouter: '/admin_3003_导入导出任务_弹窗',
        moduleName: 'Admin3003_DaoRuDaoChuRenWu_DanChuang',
        title: '3003_导入导出任务_弹窗.vue',
        label: '3003_导入导出任务_弹窗.vue',
        name: '3003_导入导出任务_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3101_统计报表_方案开通统计.vue',
        vueRouter: '/admin_3101_统计报表_方案开通统计',
        moduleName: 'Admin3101_TongJiBaoBiao_FangAnKaiTongTongJi',
        title: '3101_统计报表_方案开通统计.vue',
        label: '3101_统计报表_方案开通统计.vue',
        name: '3101_统计报表_方案开通统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3102_统计报表_地区开通统计.vue',
        vueRouter: '/admin_3102_统计报表_地区开通统计',
        moduleName: 'Admin3102_TongJiBaoBiao_DiQuKaiTongTongJi',
        title: '3102_统计报表_地区开通统计.vue',
        label: '3102_统计报表_地区开通统计.vue',
        name: '3102_统计报表_地区开通统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3103_统计报表_方案学习统计.vue',
        vueRouter: '/admin_3103_统计报表_方案学习统计',
        moduleName: 'Admin3103_TongJiBaoBiao_FangAnXueXiTongJi',
        title: '3103_统计报表_方案学习统计.vue',
        label: '3103_统计报表_方案学习统计.vue',
        name: '3103_统计报表_方案学习统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3104_统计报表_地区学习统计.vue',
        vueRouter: '/admin_3104_统计报表_地区学习统计',
        moduleName: 'Admin3104_TongJiBaoBiao_DiQuXueXiTongJi',
        title: '3104_统计报表_地区学习统计.vue',
        label: '3104_统计报表_地区学习统计.vue',
        name: '3104_统计报表_地区学习统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3105_统计报表_学员学习明细.vue',
        vueRouter: '/admin_3105_统计报表_学员学习明细',
        moduleName: 'Admin3105_TongJiBaoBiao_XueYuanXueXiMingXi',
        title: '3105_统计报表_学员学习明细.vue',
        label: '3105_统计报表_学员学习明细.vue',
        name: '3105_统计报表_学员学习明细.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3105_统计报表_学员学习明细_学习数据异常管理.vue',
        vueRouter: '/admin_3105_统计报表_学员学习明细_学习数据异常管理',
        moduleName: 'Admin3105_TongJiBaoBiao_XueYuanXueXiMingXi_XueXiShuJuYiChangGuanLi',
        title: '3105_统计报表_学员学习明细_学习数据异常管理.vue',
        label: '3105_统计报表_学员学习明细_学习数据异常管理.vue',
        name: '3105_统计报表_学员学习明细_学习数据异常管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3105_统计报表_学员学习明细_弹窗.vue',
        vueRouter: '/admin_3105_统计报表_学员学习明细_弹窗',
        moduleName: 'Admin3105_TongJiBaoBiao_XueYuanXueXiMingXi_DanChuang',
        title: '3105_统计报表_学员学习明细_弹窗.vue',
        label: '3105_统计报表_学员学习明细_弹窗.vue',
        name: '3105_统计报表_学员学习明细_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3105_统计报表_学员学习明细_监管日志.vue',
        vueRouter: '/admin_3105_统计报表_学员学习明细_监管日志',
        moduleName: 'Admin3105_TongJiBaoBiao_XueYuanXueXiMingXi_JianGuanRiZhi',
        title: '3105_统计报表_学员学习明细_监管日志.vue',
        label: '3105_统计报表_学员学习明细_监管日志.vue',
        name: '3105_统计报表_学员学习明细_监管日志.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3106_统计报表_弹窗.vue',
        vueRouter: '/admin_3106_统计报表_弹窗',
        moduleName: 'Admin3106_TongJiBaoBiao_DanChuang',
        title: '3106_统计报表_弹窗.vue',
        label: '3106_统计报表_弹窗.vue',
        name: '3106_统计报表_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3107_统计报表_培训单位销售统计.vue',
        vueRouter: '/admin_3107_统计报表_培训单位销售统计',
        moduleName: 'Admin3107_TongJiBaoBiao_PeiXunDanWeiXiaoShouTongJi',
        title: '3107_统计报表_培训单位销售统计.vue',
        label: '3107_统计报表_培训单位销售统计.vue',
        name: '3107_统计报表_培训单位销售统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3107_统计报表_学习日志（新）.vue',
        vueRouter: '/admin_3107_统计报表_学习日志（新）',
        moduleName: 'Admin3107_TongJiBaoBiao_XueXiRiZhiXin',
        title: '3107_统计报表_学习日志（新）.vue',
        label: '3107_统计报表_学习日志（新）.vue',
        name: '3107_统计报表_学习日志（新）.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3107_统计报表_监管日志.vue',
        vueRouter: '/admin_3107_统计报表_监管日志',
        moduleName: 'Admin3107_TongJiBaoBiao_JianGuanRiZhi',
        title: '3107_统计报表_监管日志.vue',
        label: '3107_统计报表_监管日志.vue',
        name: '3107_统计报表_监管日志.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3108_统计报表_选课统计.vue',
        vueRouter: '/admin_3108_统计报表_选课统计',
        moduleName: 'Admin3108_TongJiBaoBiao_XuanKeTongJi',
        title: '3108_统计报表_选课统计.vue',
        label: '3108_统计报表_选课统计.vue',
        name: '3108_统计报表_选课统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3201_地区管理员_首页.vue',
        vueRouter: '/admin_3201_地区管理员_首页',
        moduleName: 'Admin3201_DiQuGuanLiYuan_ShouYe',
        title: '3201_地区管理员_首页.vue',
        label: '3201_地区管理员_首页.vue',
        name: '3201_地区管理员_首页.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3202_地区管理员_地区学习统计.vue',
        vueRouter: '/admin_3202_地区管理员_地区学习统计',
        moduleName: 'Admin3202_DiQuGuanLiYuan_DiQuXueXiTongJi',
        title: '3202_地区管理员_地区学习统计.vue',
        label: '3202_地区管理员_地区学习统计.vue',
        name: '3202_地区管理员_地区学习统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3203_地区管理员_学员学习明细.vue',
        vueRouter: '/admin_3203_地区管理员_学员学习明细',
        moduleName: 'Admin3203_DiQuGuanLiYuan_XueYuanXueXiMingXi',
        title: '3203_地区管理员_学员学习明细.vue',
        label: '3203_地区管理员_学员学习明细.vue',
        name: '3203_地区管理员_学员学习明细.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3204_地区管理员_导出任务查看.vue',
        vueRouter: '/admin_3204_地区管理员_导出任务查看',
        moduleName: 'Admin3204_DiQuGuanLiYuan_DaoChuRenWuChaKan',
        title: '3204_地区管理员_导出任务查看.vue',
        label: '3204_地区管理员_导出任务查看.vue',
        name: '3204_地区管理员_导出任务查看.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3205_地区管理员_帐号设置.vue',
        vueRouter: '/admin_3205_地区管理员_帐号设置',
        moduleName: 'Admin3205_DiQuGuanLiYuan_ZhangHaoSheZhi',
        title: '3205_地区管理员_帐号设置.vue',
        label: '3205_地区管理员_帐号设置.vue',
        name: '3205_地区管理员_帐号设置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3206_地区管理员_弹窗.vue',
        vueRouter: '/admin_3206_地区管理员_弹窗',
        moduleName: 'Admin3206_DiQuGuanLiYuan_DanChuang',
        title: '3206_地区管理员_弹窗.vue',
        label: '3206_地区管理员_弹窗.vue',
        name: '3206_地区管理员_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3301_课件供应商_首页.vue',
        vueRouter: '/admin_3301_课件供应商_首页',
        moduleName: 'Admin3301_KeJianGongYingShang_ShouYe',
        title: '3301_课件供应商_首页.vue',
        label: '3301_课件供应商_首页.vue',
        name: '3301_课件供应商_首页.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3302_课件供应商_帐号设置.vue',
        vueRouter: '/admin_3302_课件供应商_帐号设置',
        moduleName: 'Admin3302_KeJianGongYingShang_ZhangHaoSheZhi',
        title: '3302_课件供应商_帐号设置.vue',
        label: '3302_课件供应商_帐号设置.vue',
        name: '3302_课件供应商_帐号设置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3303_课件供应商_课程管理_列表.vue',
        vueRouter: '/admin_3303_课件供应商_课程管理_列表',
        moduleName: 'Admin3303_KeJianGongYingShang_KeChengGuanLi_LieBiao',
        title: '3303_课件供应商_课程管理_列表.vue',
        label: '3303_课件供应商_课程管理_列表.vue',
        name: '3303_课件供应商_课程管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3304_课件供应商_课程管理_详情.vue',
        vueRouter: '/admin_3304_课件供应商_课程管理_详情',
        moduleName: 'Admin3304_KeJianGongYingShang_KeChengGuanLi_XiangQing',
        title: '3304_课件供应商_课程管理_详情.vue',
        label: '3304_课件供应商_课程管理_详情.vue',
        name: '3304_课件供应商_课程管理_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3305_课件供应商_选课统计.vue',
        vueRouter: '/admin_3305_课件供应商_选课统计',
        moduleName: 'Admin3305_KeJianGongYingShang_XuanKeTongJi',
        title: '3305_课件供应商_选课统计.vue',
        label: '3305_课件供应商_选课统计.vue',
        name: '3305_课件供应商_选课统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3306_课件供应商_导出任务查看.vue',
        vueRouter: '/admin_3306_课件供应商_导出任务查看',
        moduleName: 'Admin3306_KeJianGongYingShang_DaoChuRenWuChaKan',
        title: '3306_课件供应商_导出任务查看.vue',
        label: '3306_课件供应商_导出任务查看.vue',
        name: '3306_课件供应商_导出任务查看.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3307_课件供应商_弹窗.vue',
        vueRouter: '/admin_3307_课件供应商_弹窗',
        moduleName: 'Admin3307_KeJianGongYingShang_DanChuang',
        title: '3307_课件供应商_弹窗.vue',
        label: '3307_课件供应商_弹窗.vue',
        name: '3307_课件供应商_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3401_系统基础配置_分销服务管理.vue',
        vueRouter: '/admin_3401_系统基础配置_分销服务管理',
        moduleName: 'Admin3401_XiTongJiChuPeiZhi_FenXiaoFuWuGuanLi',
        title: '3401_系统基础配置_分销服务管理.vue',
        label: '3401_系统基础配置_分销服务管理.vue',
        name: '3401_系统基础配置_分销服务管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3402_系统基础配置_分销服务管理_添加供应商.vue',
        vueRouter: '/admin_3402_系统基础配置_分销服务管理_添加供应商',
        moduleName: 'Admin3402_XiTongJiChuPeiZhi_FenXiaoFuWuGuanLi_TianJiaGongYingShang',
        title: '3402_系统基础配置_分销服务管理_添加供应商.vue',
        label: '3402_系统基础配置_分销服务管理_添加供应商.vue',
        name: '3402_系统基础配置_分销服务管理_添加供应商.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3403_系统基础配置_分销服务管理_修改.vue',
        vueRouter: '/admin_3403_系统基础配置_分销服务管理_修改',
        moduleName: 'Admin3403_XiTongJiChuPeiZhi_FenXiaoFuWuGuanLi_XiuGai',
        title: '3403_系统基础配置_分销服务管理_修改.vue',
        label: '3403_系统基础配置_分销服务管理_修改.vue',
        name: '3403_系统基础配置_分销服务管理_修改.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3404_系统基础配置_分销服务管理_详情.vue',
        vueRouter: '/admin_3404_系统基础配置_分销服务管理_详情',
        moduleName: 'Admin3404_XiTongJiChuPeiZhi_FenXiaoFuWuGuanLi_XiangQing',
        title: '3404_系统基础配置_分销服务管理_详情.vue',
        label: '3404_系统基础配置_分销服务管理_详情.vue',
        name: '3404_系统基础配置_分销服务管理_详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3501_培训管理_分销管理.vue',
        vueRouter: '/admin_3501_培训管理_分销管理',
        moduleName: 'Admin3501_PeiXunGuanLi_FenXiaoGuanLi',
        title: '3501_培训管理_分销管理.vue',
        label: '3501_培训管理_分销管理.vue',
        name: '3501_培训管理_分销管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3502_培训管理_分销管理_授权产品.vue',
        vueRouter: '/admin_3502_培训管理_分销管理_授权产品',
        moduleName: 'Admin3502_PeiXunGuanLi_FenXiaoGuanLi_ShouQuanChanPin',
        title: '3502_培训管理_分销管理_授权产品.vue',
        label: '3502_培训管理_分销管理_授权产品.vue',
        name: '3502_培训管理_分销管理_授权产品.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3503_培训管理_分销管理_编辑.vue',
        vueRouter: '/admin_3503_培训管理_分销管理_编辑',
        moduleName: 'Admin3503_PeiXunGuanLi_FenXiaoGuanLi_BianJi',
        title: '3503_培训管理_分销管理_编辑.vue',
        label: '3503_培训管理_分销管理_编辑.vue',
        name: '3503_培训管理_分销管理_编辑.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3504_培训管理_分销管理_批量授权操作.vue',
        vueRouter: '/admin_3504_培训管理_分销管理_批量授权操作',
        moduleName: 'Admin3504_PeiXunGuanLi_FenXiaoGuanLi_PiLiangShouQuanCaoZuo',
        title: '3504_培训管理_分销管理_批量授权操作.vue',
        label: '3504_培训管理_分销管理_批量授权操作.vue',
        name: '3504_培训管理_分销管理_批量授权操作.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3505_培训管理_分销管理_培训方案授权情况.vue',
        vueRouter: '/admin_3505_培训管理_分销管理_培训方案授权情况',
        moduleName: 'Admin3505_PeiXunGuanLi_FenXiaoGuanLi_PeiXunFangAnShouQuanQingKuang',
        title: '3505_培训管理_分销管理_培训方案授权情况.vue',
        label: '3505_培训管理_分销管理_培训方案授权情况.vue',
        name: '3505_培训管理_分销管理_培训方案授权情况.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3506_培训管理_分销管理_分销详情.vue',
        vueRouter: '/admin_3506_培训管理_分销管理_分销详情',
        moduleName: 'Admin3506_PeiXunGuanLi_FenXiaoGuanLi_FenXiaoXiangQing',
        title: '3506_培训管理_分销管理_分销详情.vue',
        label: '3506_培训管理_分销管理_分销详情.vue',
        name: '3506_培训管理_分销管理_分销详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3507_培训管理_分销管理_分销日志.vue',
        vueRouter: '/admin_3507_培训管理_分销管理_分销日志',
        moduleName: 'Admin3507_PeiXunGuanLi_FenXiaoGuanLi_FenXiaoRiZhi',
        title: '3507_培训管理_分销管理_分销日志.vue',
        label: '3507_培训管理_分销管理_分销日志.vue',
        name: '3507_培训管理_分销管理_分销日志.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3601_统计报表_分销产品统计.vue',
        vueRouter: '/admin_3601_统计报表_分销产品统计',
        moduleName: 'Admin3601_TongJiBaoBiao_FenXiaoChanPinTongJi',
        title: '3601_统计报表_分销产品统计.vue',
        label: '3601_统计报表_分销产品统计.vue',
        name: '3601_统计报表_分销产品统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3602_统计报表_供应商推广统计.vue',
        vueRouter: '/admin_3602_统计报表_供应商推广统计',
        moduleName: 'Admin3602_TongJiBaoBiao_GongYingShangTuiGuangTongJi',
        title: '3602_统计报表_供应商推广统计.vue',
        label: '3602_统计报表_供应商推广统计.vue',
        name: '3602_统计报表_供应商推广统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3603_统计报表_弹窗.vue',
        vueRouter: '/admin_3603_统计报表_弹窗',
        moduleName: 'Admin3603_TongJiBaoBiao_DanChuang',
        title: '3603_统计报表_弹窗.vue',
        label: '3603_统计报表_弹窗.vue',
        name: '3603_统计报表_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3604_统计报表_分销商销售统计.vue',
        vueRouter: '/admin_3604_统计报表_分销商销售统计',
        moduleName: 'Admin3604_TongJiBaoBiao_FenXiaoShangXiaoShouTongJi',
        title: '3604_统计报表_分销商销售统计.vue',
        label: '3604_统计报表_分销商销售统计.vue',
        name: '3604_统计报表_分销商销售统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3605_统计报表_分销商品开通统计.vue',
        vueRouter: '/admin_3605_统计报表_分销商品开通统计',
        moduleName: 'Admin3605_TongJiBaoBiao_FenXiaoShangPinKaiTongTongJi',
        title: '3605_统计报表_分销商品开通统计.vue',
        label: '3605_统计报表_分销商品开通统计.vue',
        name: '3605_统计报表_分销商品开通统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3606_统计报表_分销销售统计.vue',
        vueRouter: '/admin_3606_统计报表_分销销售统计',
        moduleName: 'Admin3606_TongJiBaoBiao_FenXiaoXiaoShouTongJi',
        title: '3606_统计报表_分销销售统计.vue',
        label: '3606_统计报表_分销销售统计.vue',
        name: '3606_统计报表_分销销售统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3607_统计报表_分销商销售统计02.vue',
        vueRouter: '/admin_3607_统计报表_分销商销售统计02',
        moduleName: 'Admin3607_TongJiBaoBiao_FenXiaoShangXiaoShouTongJi02',
        title: '3607_统计报表_分销商销售统计02.vue',
        label: '3607_统计报表_分销商销售统计02.vue',
        name: '3607_统计报表_分销商销售统计02.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3608_统计报表_(华医网)组合开通统计.vue',
        vueRouter: '/admin_3608_统计报表_(华医网)组合开通统计',
        moduleName: 'Admin3608_TongJiBaoBiao_HuaYiWangZuHeKaiTongTongJi',
        title: '3608_统计报表_(华医网)组合开通统计.vue',
        label: '3608_统计报表_(华医网)组合开通统计.vue',
        name: '3608_统计报表_(华医网)组合开通统计.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3701_营销中心.vue',
        vueRouter: '/admin_3701_营销中心',
        moduleName: 'Admin3701_YingXiaoZhongXin',
        title: '3701_营销中心.vue',
        label: '3701_营销中心.vue',
        name: '3701_营销中心.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/3801_日志管理_登录日志.vue',
        vueRouter: '/admin_3801_日志管理_登录日志',
        moduleName: 'Admin3801_RiZhiGuanLi_DengLuRiZhi',
        title: '3801_日志管理_登录日志.vue',
        label: '3801_日志管理_登录日志.vue',
        name: '3801_日志管理_登录日志.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4001_专题管理员_登录页.vue',
        vueRouter: '/admin_4001_专题管理员_登录页',
        moduleName: 'Admin4001_ZhuanTiGuanLiYuan_DengLuYe',
        title: '4001_专题管理员_登录页.vue',
        label: '4001_专题管理员_登录页.vue',
        name: '4001_专题管理员_登录页.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4002_专题管理员_忘记密码.vue',
        vueRouter: '/admin_4002_专题管理员_忘记密码',
        moduleName: 'Admin4002_ZhuanTiGuanLiYuan_WangJiMiMa',
        title: '4002_专题管理员_忘记密码.vue',
        label: '4002_专题管理员_忘记密码.vue',
        name: '4002_专题管理员_忘记密码.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4003_专题管理员_首页.vue',
        vueRouter: '/admin_4003_专题管理员_首页',
        moduleName: 'Admin4003_ZhuanTiGuanLiYuan_ShouYe',
        title: '4003_专题管理员_首页.vue',
        label: '4003_专题管理员_首页.vue',
        name: '4003_专题管理员_首页.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4004_专题管理员_账号设置.vue',
        vueRouter: '/admin_4004_专题管理员_账号设置',
        moduleName: 'Admin4004_ZhuanTiGuanLiYuan_ZhangHaoSheZhi',
        title: '4004_专题管理员_账号设置.vue',
        label: '4004_专题管理员_账号设置.vue',
        name: '4004_专题管理员_账号设置.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4005_专题管理员_账号设置_弹窗.vue',
        vueRouter: '/admin_4005_专题管理员_账号设置_弹窗',
        moduleName: 'Admin4005_ZhuanTiGuanLiYuan_ZhangHaoSheZhi_DanChuang',
        title: '4005_专题管理员_账号设置_弹窗.vue',
        label: '4005_专题管理员_账号设置_弹窗.vue',
        name: '4005_专题管理员_账号设置_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4101_专题管理员_培训管理_导入学员.vue',
        vueRouter: '/admin_4101_专题管理员_培训管理_导入学员',
        moduleName: 'Admin4101_ZhuanTiGuanLiYuan_PeiXunGuanLi_DaoRuXueYuan',
        title: '4101_专题管理员_培训管理_导入学员.vue',
        label: '4101_专题管理员_培训管理_导入学员.vue',
        name: '4101_专题管理员_培训管理_导入学员.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4102_专题管理员_培训管理_导入学员并开班.vue',
        vueRouter: '/admin_4102_专题管理员_培训管理_导入学员并开班',
        moduleName: 'Admin4102_ZhuanTiGuanLiYuan_PeiXunGuanLi_DaoRuXueYuanBingKaiBan',
        title: '4102_专题管理员_培训管理_导入学员并开班.vue',
        label: '4102_专题管理员_培训管理_导入学员并开班.vue',
        name: '4102_专题管理员_培训管理_导入学员并开班.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4103_专题管理员_培训管理_导入学员并开班_弹窗.vue',
        vueRouter: '/admin_4103_专题管理员_培训管理_导入学员并开班_弹窗',
        moduleName: 'Admin4103_ZhuanTiGuanLiYuan_PeiXunGuanLi_DaoRuXueYuanBingKaiBan_DanChuang',
        title: '4103_专题管理员_培训管理_导入学员并开班_弹窗.vue',
        label: '4103_专题管理员_培训管理_导入学员并开班_弹窗.vue',
        name: '4103_专题管理员_培训管理_导入学员并开班_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4104_专题管理员_培训管理_导入学员开通结果.vue',
        vueRouter: '/admin_4104_专题管理员_培训管理_导入学员开通结果',
        moduleName: 'Admin4104_ZhuanTiGuanLiYuan_PeiXunGuanLi_DaoRuXueYuanKaiTongJieGuo',
        title: '4104_专题管理员_培训管理_导入学员开通结果.vue',
        label: '4104_专题管理员_培训管理_导入学员开通结果.vue',
        name: '4104_专题管理员_培训管理_导入学员开通结果.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4105_专题管理员_培训管理_导入任务管理.vue',
        vueRouter: '/admin_4105_专题管理员_培训管理_导入任务管理',
        moduleName: 'Admin4105_ZhuanTiGuanLiYuan_PeiXunGuanLi_DaoRuRenWuGuanLi',
        title: '4105_专题管理员_培训管理_导入任务管理.vue',
        label: '4105_专题管理员_培训管理_导入任务管理.vue',
        name: '4105_专题管理员_培训管理_导入任务管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4106_专题管理员_培训管理_导出任务管理.vue',
        vueRouter: '/admin_4106_专题管理员_培训管理_导出任务管理',
        moduleName: 'Admin4106_ZhuanTiGuanLiYuan_PeiXunGuanLi_DaoChuRenWuGuanLi',
        title: '4106_专题管理员_培训管理_导出任务管理.vue',
        label: '4106_专题管理员_培训管理_导出任务管理.vue',
        name: '4106_专题管理员_培训管理_导出任务管理.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4107_专题管理员_培训管理_导入开通_查看期别.vue',
        vueRouter: '/admin_4107_专题管理员_培训管理_导入开通_查看期别',
        moduleName: 'Admin4107_ZhuanTiGuanLiYuan_PeiXunGuanLi_DaoRuKaiTong_ChaKanQiBie',
        title: '4107_专题管理员_培训管理_导入开通_查看期别.vue',
        label: '4107_专题管理员_培训管理_导入开通_查看期别.vue',
        name: '4107_专题管理员_培训管理_导入开通_查看期别.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4108_专题管理员_培训管理_导入开通_导入开通结果跟踪.vue',
        vueRouter: '/admin_4108_专题管理员_培训管理_导入开通_导入开通结果跟踪',
        moduleName: 'Admin4108_ZhuanTiGuanLiYuan_PeiXunGuanLi_DaoRuKaiTong_DaoRuKaiTongJieGuoGenZong',
        title: '4108_专题管理员_培训管理_导入开通_导入开通结果跟踪.vue',
        label: '4108_专题管理员_培训管理_导入开通_导入开通结果跟踪.vue',
        name: '4108_专题管理员_培训管理_导入开通_导入开通结果跟踪.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/4201_专题管理员_统计报表_学员学习明细.vue',
        vueRouter: '/admin_4201_专题管理员_统计报表_学员学习明细',
        moduleName: 'Admin4201_ZhuanTiGuanLiYuan_TongJiBaoBiao_XueYuanXueXiMingXi',
        title: '4201_专题管理员_统计报表_学员学习明细.vue',
        label: '4201_专题管理员_统计报表_学员学习明细.vue',
        name: '4201_专题管理员_统计报表_学员学习明细.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/5001_培训点管理_列表.vue',
        vueRouter: '/admin_5001_培训点管理_列表',
        moduleName: 'Admin5001_PeiXunDianGuanLi_LieBiao',
        title: '5001_培训点管理_列表.vue',
        label: '5001_培训点管理_列表.vue',
        name: '5001_培训点管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/5001_智能学习_导入学员开班并学习.vue',
        vueRouter: '/admin_5001_智能学习_导入学员开班并学习',
        moduleName: 'Admin5001_ZhiNengXueXi_DaoRuXueYuanKaiBanBingXueXi',
        title: '5001_智能学习_导入学员开班并学习.vue',
        label: '5001_智能学习_导入学员开班并学习.vue',
        name: '5001_智能学习_导入学员开班并学习.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/5002_培训点管理_新建编辑培训点.vue',
        vueRouter: '/admin_5002_培训点管理_新建编辑培训点',
        moduleName: 'Admin5002_PeiXunDianGuanLi_XinJianBianJiPeiXunDian',
        title: '5002_培训点管理_新建编辑培训点.vue',
        label: '5002_培训点管理_新建编辑培训点.vue',
        name: '5002_培训点管理_新建编辑培训点.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/5002_智能学习_智能学习任务跟踪_智能学习编排结果.vue',
        vueRouter: '/admin_5002_智能学习_智能学习任务跟踪_智能学习编排结果',
        moduleName: 'Admin5002_ZhiNengXueXi_ZhiNengXueXiRenWuGenZong_ZhiNengXueXiBianPaiJieGuo',
        title: '5002_智能学习_智能学习任务跟踪_智能学习编排结果.vue',
        label: '5002_智能学习_智能学习任务跟踪_智能学习编排结果.vue',
        name: '5002_智能学习_智能学习任务跟踪_智能学习编排结果.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/5003_培训点管理_培训点详情.vue',
        vueRouter: '/admin_5003_培训点管理_培训点详情',
        moduleName: 'Admin5003_PeiXunDianGuanLi_PeiXunDianXiangQing',
        title: '5003_培训点管理_培训点详情.vue',
        label: '5003_培训点管理_培训点详情.vue',
        name: '5003_培训点管理_培训点详情.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/5003_智能学习_智能学习任务跟踪_智能学习执行情况.vue',
        vueRouter: '/admin_5003_智能学习_智能学习任务跟踪_智能学习执行情况',
        moduleName: 'Admin5003_ZhiNengXueXi_ZhiNengXueXiRenWuGenZong_ZhiNengXueXiZhiXingQingKuang',
        title: '5003_智能学习_智能学习任务跟踪_智能学习执行情况.vue',
        label: '5003_智能学习_智能学习任务跟踪_智能学习执行情况.vue',
        name: '5003_智能学习_智能学习任务跟踪_智能学习执行情况.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/5004_培训点管理_弹窗.vue',
        vueRouter: '/admin_5004_培训点管理_弹窗',
        moduleName: 'Admin5004_PeiXunDianGuanLi_DanChuang',
        title: '5004_培训点管理_弹窗.vue',
        label: '5004_培训点管理_弹窗.vue',
        name: '5004_培训点管理_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/5004_智能学习_智能学习任务跟踪_弹窗.vue',
        vueRouter: '/admin_5004_智能学习_智能学习任务跟踪_弹窗',
        moduleName: 'Admin5004_ZhiNengXueXi_ZhiNengXueXiRenWuGenZong_DanChuang',
        title: '5004_智能学习_智能学习任务跟踪_弹窗.vue',
        label: '5004_智能学习_智能学习任务跟踪_弹窗.vue',
        name: '5004_智能学习_智能学习任务跟踪_弹窗.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/6001_问卷管理_列表.vue',
        vueRouter: '/admin_6001_问卷管理_列表',
        moduleName: 'Admin6001_WenJuanGuanLi_LieBiao',
        title: '6001_问卷管理_列表.vue',
        label: '6001_问卷管理_列表.vue',
        name: '6001_问卷管理_列表.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/6002_问卷管理_新建调研问卷模板.vue',
        vueRouter: '/admin_6002_问卷管理_新建调研问卷模板',
        moduleName: 'Admin6002_WenJuanGuanLi_XinJianDiaoYanWenJuanMoBan',
        title: '6002_问卷管理_新建调研问卷模板.vue',
        label: '6002_问卷管理_新建调研问卷模板.vue',
        name: '6002_问卷管理_新建调研问卷模板.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/6003_问卷管理_修改调研问卷模板.vue',
        vueRouter: '/admin_6003_问卷管理_修改调研问卷模板',
        moduleName: 'Admin6003_WenJuanGuanLi_XiuGaiDiaoYanWenJuanMoBan',
        title: '6003_问卷管理_修改调研问卷模板.vue',
        label: '6003_问卷管理_修改调研问卷模板.vue',
        name: '6003_问卷管理_修改调研问卷模板.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/6004_问卷管理_问卷预览.vue',
        vueRouter: '/admin_6004_问卷管理_问卷预览',
        moduleName: 'Admin6004_WenJuanGuanLi_WenJuanYuLan',
        title: '6004_问卷管理_问卷预览.vue',
        label: '6004_问卷管理_问卷预览.vue',
        name: '6004_问卷管理_问卷预览.vue'
      },
      {
        children: [],
        appPath: '/admin',
        expand: true,
        fileDir: '/admin/6005_问卷管理_弹窗.vue',
        vueRouter: '/admin_6005_问卷管理_弹窗',
        moduleName: 'Admin6005_WenJuanGuanLi_DanChuang',
        title: '6005_问卷管理_弹窗.vue',
        label: '6005_问卷管理_弹窗.vue',
        name: '6005_问卷管理_弹窗.vue'
      }
    ],
    appPath: '',
    expand: true,
    fileDir: '/admin',
    vueRouter: '/admin',
    moduleName: 'Admin',
    title: 'admin',
    label: 'admin',
    name: 'admin',
    isDirectory: true
  },
  {
    children: [
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0000_弹窗样式汇总.vue',
        vueRouter: '/trainingInstitution_0000_弹窗样式汇总',
        moduleName: 'TrainingInstitution0000_DanChuangYangShiHuiZong',
        title: '0000_弹窗样式汇总.vue',
        label: '0000_弹窗样式汇总.vue',
        name: '0000_弹窗样式汇总.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0000_通用空数据.vue',
        vueRouter: '/trainingInstitution_0000_通用空数据',
        moduleName: 'TrainingInstitution0000_TongYongKongShuJu',
        title: '0000_通用空数据.vue',
        label: '0000_通用空数据.vue',
        name: '0000_通用空数据.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0000_通用组件样式汇总.vue',
        vueRouter: '/trainingInstitution_0000_通用组件样式汇总',
        moduleName: 'TrainingInstitution0000_TongYongZuJianYangShiHuiZong',
        title: '0000_通用组件样式汇总.vue',
        label: '0000_通用组件样式汇总.vue',
        name: '0000_通用组件样式汇总.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0001_登录页.vue',
        vueRouter: '/trainingInstitution_0001_登录页',
        moduleName: 'TrainingInstitution0001_DengLuYe',
        title: '0001_登录页.vue',
        label: '0001_登录页.vue',
        name: '0001_登录页.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0101_头部及侧边栏框架.vue',
        vueRouter: '/trainingInstitution_0101_头部及侧边栏框架',
        moduleName: 'TrainingInstitution0101_TouBuJiCeBianLanKuangJia',
        title: '0101_头部及侧边栏框架.vue',
        label: '0101_头部及侧边栏框架.vue',
        name: '0101_头部及侧边栏框架.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0201_网校管理_开通网校.vue',
        vueRouter: '/trainingInstitution_0201_网校管理_开通网校',
        moduleName: 'TrainingInstitution0201_WangXiaoGuanLi_KaiTongWangXiao',
        title: '0201_网校管理_开通网校.vue',
        label: '0201_网校管理_开通网校.vue',
        name: '0201_网校管理_开通网校.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0201_网校管理_开通网校_弹窗.vue',
        vueRouter: '/trainingInstitution_0201_网校管理_开通网校_弹窗',
        moduleName: 'TrainingInstitution0201_WangXiaoGuanLi_KaiTongWangXiao_DanChuang',
        title: '0201_网校管理_开通网校_弹窗.vue',
        label: '0201_网校管理_开通网校_弹窗.vue',
        name: '0201_网校管理_开通网校_弹窗.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0202_网校管理_已创建网校.vue',
        vueRouter: '/trainingInstitution_0202_网校管理_已创建网校',
        moduleName: 'TrainingInstitution0202_WangXiaoGuanLi_YiChuangJianWangXiao',
        title: '0202_网校管理_已创建网校.vue',
        label: '0202_网校管理_已创建网校.vue',
        name: '0202_网校管理_已创建网校.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0203_网校管理_修改网校_基础配置.vue',
        vueRouter: '/trainingInstitution_0203_网校管理_修改网校_基础配置',
        moduleName: 'TrainingInstitution0203_WangXiaoGuanLi_XiuGaiWangXiao_JiChuPeiZhi',
        title: '0203_网校管理_修改网校_基础配置.vue',
        label: '0203_网校管理_修改网校_基础配置.vue',
        name: '0203_网校管理_修改网校_基础配置.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0203_网校管理_修改网校_弹窗.vue',
        vueRouter: '/trainingInstitution_0203_网校管理_修改网校_弹窗',
        moduleName: 'TrainingInstitution0203_WangXiaoGuanLi_XiuGaiWangXiao_DanChuang',
        title: '0203_网校管理_修改网校_弹窗.vue',
        label: '0203_网校管理_修改网校_弹窗.vue',
        name: '0203_网校管理_修改网校_弹窗.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0203_网校管理_修改网校_模板配置.vue',
        vueRouter: '/trainingInstitution_0203_网校管理_修改网校_模板配置',
        moduleName: 'TrainingInstitution0203_WangXiaoGuanLi_XiuGaiWangXiao_MoBanPeiZhi',
        title: '0203_网校管理_修改网校_模板配置.vue',
        label: '0203_网校管理_修改网校_模板配置.vue',
        name: '0203_网校管理_修改网校_模板配置.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0203_网校管理_修改网校_管理员信息.vue',
        vueRouter: '/trainingInstitution_0203_网校管理_修改网校_管理员信息',
        moduleName: 'TrainingInstitution0203_WangXiaoGuanLi_XiuGaiWangXiao_GuanLiYuanXinXi',
        title: '0203_网校管理_修改网校_管理员信息.vue',
        label: '0203_网校管理_修改网校_管理员信息.vue',
        name: '0203_网校管理_修改网校_管理员信息.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0203_网校管理_修改网校_网校配置.vue',
        vueRouter: '/trainingInstitution_0203_网校管理_修改网校_网校配置',
        moduleName: 'TrainingInstitution0203_WangXiaoGuanLi_XiuGaiWangXiao_WangXiaoPeiZhi',
        title: '0203_网校管理_修改网校_网校配置.vue',
        label: '0203_网校管理_修改网校_网校配置.vue',
        name: '0203_网校管理_修改网校_网校配置.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0204_网校管理_修改网校_增值服务管理.vue',
        vueRouter: '/trainingInstitution_0204_网校管理_修改网校_增值服务管理',
        moduleName: 'TrainingInstitution0204_WangXiaoGuanLi_XiuGaiWangXiao_ZengZhiFuWuGuanLi',
        title: '0204_网校管理_修改网校_增值服务管理.vue',
        label: '0204_网校管理_修改网校_增值服务管理.vue',
        name: '0204_网校管理_修改网校_增值服务管理.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0301_帐号设置.vue',
        vueRouter: '/trainingInstitution_0301_帐号设置',
        moduleName: 'TrainingInstitution0301_ZhangHaoSheZhi',
        title: '0301_帐号设置.vue',
        label: '0301_帐号设置.vue',
        name: '0301_帐号设置.vue'
      },
      {
        children: [],
        appPath: '/trainingInstitution',
        expand: true,
        fileDir: '/trainingInstitution/0302_帐号设置_弹窗.vue',
        vueRouter: '/trainingInstitution_0302_帐号设置_弹窗',
        moduleName: 'TrainingInstitution0302_ZhangHaoSheZhi_DanChuang',
        title: '0302_帐号设置_弹窗.vue',
        label: '0302_帐号设置_弹窗.vue',
        name: '0302_帐号设置_弹窗.vue'
      }
    ],
    appPath: '',
    expand: true,
    fileDir: '/trainingInstitution',
    vueRouter: '/trainingInstitution',
    moduleName: 'TrainingInstitution',
    title: 'trainingInstitution',
    label: 'trainingInstitution',
    name: 'trainingInstitution',
    isDirectory: true
  }
]
