import { cloneDeep } from 'lodash'
import mockjs from 'mockjs'

enum DataTypeEnum {
  Object = '[object Object]',
  Array = '[object Array]',
  String = '[object String]',
  Number = '[object Number]',
  Boolean = '[object Boolean]'
}
/**
 * MOCK工具 --- 状态层使用
 */
export default function MockUtil(item: any) {
  const type = Object.prototype.toString.call(item)

  switch (type) {
    case DataTypeEnum.Object:
      for (const key in item) {
        if (Object.prototype.hasOwnProperty.call(item, key)) {
          const element = item[key]
          const type = Object.prototype.toString.call(element)
          switch (type) {
            case DataTypeEnum.String:
              item[key] = `${key}_${mockjs.Random.csentence(4, 7).split('。')[0]}`
              break
            case DataTypeEnum.Number:
              item[key] = mockjs.Random.natural(60, 91)
              break
            case DataTypeEnum.Boolean:
              item[key] = mockjs.Random.boolean()
              break
          }
          MockUtil(element)
        }
      }
      break
    case DataTypeEnum.Array:
      /**
       * 如果是数组，第一个有值，那么后续根据类型进行Push十个相同类型的值
       */
      if (item[0] || item[0] == '') {
        const temp = item[0]
        const type = Object.prototype.toString.call(temp)
        switch (type) {
          case DataTypeEnum.String:
            item[0] = mockjs.Random.csentence(4, 7)
            for (let i = 1; i < 10; i++) {
              item[i] = mockjs.Random.csentence(4, 7)
            }
            break
          case DataTypeEnum.Number:
            item[0] = mockjs.Random.natural(60, 91)
            for (let i = 1; i < 10; i++) {
              item[i] = mockjs.Random.natural(60, 91)
            }
            break
          case DataTypeEnum.Boolean:
            item[0] = mockjs.Random.boolean()
            for (let i = 1; i < 10; i++) {
              item[i] = mockjs.Random.boolean()
            }
            break
          case DataTypeEnum.Object:
            MockUtil(item[0])
            for (let i = 0; i < 9; i++) {
              const clone = cloneDeep(temp)
              MockUtil(clone)
              item.push(clone)
            }
            break
        }
      }
      break
  }
}
