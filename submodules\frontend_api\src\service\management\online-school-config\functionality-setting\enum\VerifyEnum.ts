import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum VerifyTypeEnum {
  /**
   * 验证码
   */
  captcha = 0,

  /**
   * 滑块验证
   */
  slide
}

export default class VerifyEnum extends AbstractEnum<VerifyTypeEnum> {
  static enum = VerifyTypeEnum

  constructor(status?: VerifyTypeEnum) {
    super()
    this.current = status
    this.map.set(VerifyTypeEnum.captcha, '验证码')
    this.map.set(VerifyTypeEnum.slide, '滑动拼图')
  }
}
