import { QuestionnaireTemplateResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'
import { QuestionnaireStatusEnum } from '@api/service/management/resource/question-naire/enums/QuestionnaireStatus'
import { QuestionnaireTypeEnum } from '@api/service/management/resource/question-naire/enums/QuestionnaireType'

/**
 * 列表项
 */
class QuestionnaireItem {
  /**
   * 试卷id
   */
  id = ''
  /**
   * 名称
   */
  name = ''
  /**
   * 类型
   */
  type: QuestionnaireTypeEnum = QuestionnaireTypeEnum.ordinary
  /**
   * 创建时间
   */
  createTime = ''
  /**
   * 状态
   */
  status: QuestionnaireStatusEnum = null
  /**
   * 是否被方案引用
   */
  schemeUseStatus = false

  static from(dto: QuestionnaireTemplateResponse) {
    const vo = new QuestionnaireItem()
    vo.createTime = dto.createdTime
    vo.id = dto.id
    vo.name = dto.name
    vo.schemeUseStatus = dto.isReferenced
    vo.status = dto.isDraft == 1 ? QuestionnaireStatusEnum.draft : QuestionnaireStatusEnum.publish
    vo.type = dto.type
    return vo
  }
}
export default QuestionnaireItem
