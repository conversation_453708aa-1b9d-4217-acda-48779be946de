import MsBasicdata, {
  AdminInfoResponse,
  ContractProviderAdminInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import PlatformTrainingChannelAdministrator, {
  CreateTrainingChannelAdminRequest,
  UpdateTrainingChannelAdminRequset
} from '@api/platform-gateway/platform-training-channel-admin-v1'
import { Page, ResponseStatus } from '@hbfe/common'
import { AdministratorStatusEnum } from '@api/service/management/user/thematic-administrator/AdministratorStatusEnum'
import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
import { TrainingChannelCountResponse } from '@api/platform-gateway/platform-training-channel-back-gateway'
import ThematicManagementList from '@api/service/management/thematic-management/ThematicManagementList'
import { cloneDeep } from 'lodash'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import basicdata, { RoleResponse } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import * as GraphqlImporter from '@api/ms-gateway/ms-basicdata-domain-gateway-v1/graphql-importer'

export default class ThematicAdministratorItem {
  /**
   * 管理员ID
   */
  userId = ''
  /**
   * 管理员账号ID
   */
  accountId = ''
  /**
   * 管理员账号
   */
  account = ''
  /**
   * 管理员名称
   */
  name = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 启停用状态 | 1.启用 | 2.禁用
   */
  status: AdministratorStatusEnum = null

  /**
   * 分配的角色
   */
  roleList: Array<RoleInfoResponseVo> = []

  /**
   * 专题数量
   */
  thematicCount: number = undefined

  /**
   * 分配的专题
   */
  thematicList: Array<ThematicManagementItem> = []

  /**
   * 角色id
   */
  get roleIdList() {
    return this.roleList.map((item) => item.id)
  }

  /**
   * 专题id
   */
  get thematicIdList() {
    return this.thematicList.map((item) => item.topicID)
  }

  /**
   * 备份数据-修改时比对使用
   */
  thematicAdministratorItem: ThematicAdministratorItem = null

  static from(
    dto: AdminInfoResponse | ContractProviderAdminInfoResponse,
    countList: Array<TrainingChannelCountResponse>
  ) {
    const vo = new ThematicAdministratorItem()
    vo.userId = dto?.userInfo?.userId
    vo.accountId = dto?.accountInfo?.accountId
    vo.name = dto?.userInfo?.userName
    vo.account =
      dto?.authenticationList && dto?.authenticationList.length
        ? dto?.authenticationList.find((item) => item.identityType === 1).identity || dto.authenticationList[0].identity
        : '-'
    vo.phone = dto?.userInfo?.phone
    vo.status = dto?.accountInfo?.status
    vo.thematicCount = countList.find((it) => it.userId == vo.userId)?.trainingChannelCount || 0
    return vo
  }
  /**
   * 添加
   */
  async addAdministrator() {
    const params = new CreateTrainingChannelAdminRequest()
    params.account = this.account
    params.name = this.name
    params.phone = this.phone || undefined
    params.status = this.status
    params.addRoleIds = this.roleIdList
    params.trainingChannelIds = this.thematicIdList
    const { status, data } = await PlatformTrainingChannelAdministrator.createTrainingChannelAdministrator(params)
    if (status.isSuccess() && data) {
      return new ResponseStatus(Number(data?.code), data?.message)
    } else return new ResponseStatus(500, '请求错误')
  }
  /**
   * 修改
   */
  async updateAdministrator() {
    const params = new UpdateTrainingChannelAdminRequset()
    params.accountId = this.accountId
    params.name = this.isUpdateValue('name') as string
    params.phone = this.isUpdateValue('phone') as string
    params.status = this.status
    const operationRole = this.getAddOrDeleteValue(this.thematicAdministratorItem.roleIdList, this.roleIdList)
    params.addRoleIds = operationRole.addArr
    params.removeRoleIds = operationRole.deleteArr
    const operationThematic = this.getAddOrDeleteValue(
      this.thematicAdministratorItem.thematicIdList,
      this.thematicIdList
    )
    params.addTrainingChannelIds = operationThematic.addArr
    params.removeTrainingChannelIds = operationThematic.deleteArr
    const { status, data } = await PlatformTrainingChannelAdministrator.updateTrainingChannelAdministrator(params)
    if (status.isSuccess() && data) {
      return new ResponseStatus(Number(data?.code), data?.message)
    } else return new ResponseStatus(500, '请求错误')
  }

  /**
   * 查询详情
   */
  async getAdministratorDetail(id: string) {
    // 查询管理员基本信息
    const res = await MsBasicdata.getAdminInfoInServicer(id)
    const adminInfo = res?.data
    if (adminInfo) {
      const { userInfo, accountInfo } = adminInfo
      if (userInfo) {
        this.userId = userInfo.userId
        this.name = userInfo.userName
        this.phone = userInfo.phone
      }
      if (accountInfo) {
        this.accountId = accountInfo.accountId
        this.status = accountInfo.status
      }
      if (adminInfo.authenticationList?.length) {
        const identityFilter = adminInfo.authenticationList.find((item) => item.identityType == 1)
        this.account = identityFilter?.identity
      }
      if (adminInfo.roleList.length) {
        const roleIds = adminInfo.roleList.map((item) => {
          return item.roleId
        })
        const roleMap = await this.batchQueryRoleDetail(roleIds)
        this.roleList = adminInfo.roleList.map((item) => {
          const role = new RoleInfoResponseVo()
          role.id = item.roleId
          role.name = roleMap.get(role.id)?.name
          role.description = roleMap.get(role.id)?.description
          role.category = item.roleCategory
          role.isSelect = true
          return role
        })
      }
    }
  }

  /**
   * 获取当前专题管理员下所有专题
   */
  async getAllThematicListByCurrent() {
    // 查当前管理员下的专题
    if (this.userId) {
      const thematicManagementList = new ThematicManagementList()
      this.thematicList = await thematicManagementList.getThematicListByUserId(this.userId)
    } else {
      this.thematicList = new Array<ThematicManagementItem>()
    }

    // 备份数据
    this.thematicAdministratorItem = cloneDeep(this)
  }

  /**
   * 分页查询当前专题管理员下的专题
   * @param page
   */
  async pageCurrentThematicList(page: Page) {
    if (!this.userId) {
      this.thematicList = []
    } else {
      const thematicManagementList = new ThematicManagementList()
      thematicManagementList.queryParam.userIds = [this.userId]
      await thematicManagementList.queryList(page)
      this.thematicList = thematicManagementList.list
    }
  }

  /**
   * 比对值是否更新
   */
  private isUpdateValue(key: keyof ThematicAdministratorItem) {
    if (this[key] && this.thematicAdministratorItem?.[key] && this[key] !== this.thematicAdministratorItem[key])
      return this[key]
    else return null
  }

  /**
   * 比对新旧两个字符串数组获取新增、删除的值
   */
  private getAddOrDeleteValue(oldArr: Array<string>, newArr: Array<string>) {
    const addArr = newArr.filter((item) => !oldArr.includes(item))
    const deleteArr = oldArr.filter((item) => !newArr.includes(item))
    return {
      addArr,
      deleteArr
    }
  }

  private async batchQueryRoleDetail(ids: string[]) {
    const rew = new RewriteGraph<RoleResponse, string>(basicdata._commonQuery, GraphqlImporter.findRoleById)
    await rew.request(ids)
    return rew.itemMap
  }
}
