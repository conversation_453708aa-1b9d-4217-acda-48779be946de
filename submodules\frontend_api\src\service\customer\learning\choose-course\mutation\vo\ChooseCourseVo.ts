import HandleChooseCourseResponseEnum from '@api/service/customer/learning/choose-course/mutation/enum/HandleChooseCourseResponseEnum'
import Mockjs from 'mockjs'
import TrainingOutlineCourse from '@api/service/customer/course/query/vo/TrainingOutlineCourse'

/**
 * @description 待选可选课程模型
 */
class ChooseCourseVo {
  /**
   * 课程学习大纲父级Id
   */
  outlineParentId = ''

  /**
   * 课程学习大纲Id
   */
  outlineId = ''

  /**
   * 课程Id
   */
  courseId = ''

  /**
   * 是否选中
   */
  isSelected: boolean = null

  /**
   * 课程名称
   */
  courseName = ''

  /**
   * 学时（待选课列表时展示）
   */
  period: number = null

  /**
   * 抹平学时（选课清单时展示）
   */
  floatPeriod: number = null

  /**
   * 评分
   */
  rate: number = null

  /**
   * 教师
   */
  teacher = ''

  /**
   * 是否试听
   */
  isCanListen = true

  /**
   * 选课人数
   */
  chooseCoursePeopleCount: number = null

  /**
   * 课程类别
   */
  courseTypeName = ''

  /**
   * 课程Logo
   */
  logoUrl = ''

  /**
   * 课程选中响应事件
   * @param
   * @return {HandleChooseCourseResponseEnum}
   */
  handleCourse(): HandleChooseCourseResponseEnum {
    return HandleChooseCourseResponseEnum.enough
  }

  /**
   * 课程试听响应事件
   * @param
   * @return {string} path - 课程试听路径
   */
  courseAudition(): string {
    return Mockjs.Random.string()
  }

  static from(response: TrainingOutlineCourse, isSelected?: boolean) {
    const chooseCourse = new ChooseCourseVo()
    chooseCourse.courseId = response.id
    chooseCourse.outlineId = response.outlineId
    chooseCourse.courseName = response.name
    chooseCourse.period = response.period
    chooseCourse.floatPeriod = response.period
    chooseCourse.rate = response.score || 0
    chooseCourse.isSelected = isSelected ? isSelected : false
    chooseCourse.isCanListen = response.isAllowAudition
    chooseCourse.teacher = response.getTeacherNames()
    chooseCourse.chooseCoursePeopleCount = response.chooseCoursePeopleCount || 0
    chooseCourse.courseTypeName = response.courseTypeName
    chooseCourse.logoUrl = response.logoUrl
    return chooseCourse
  }
}

export default ChooseCourseVo
