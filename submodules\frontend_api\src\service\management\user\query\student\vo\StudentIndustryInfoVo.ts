/*
 * 行业信息
 */

import { StudentIndustryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import StudentCertificateInfoVo from './StudentCertificateInfoVo'

class StudentIndustryInfoVo extends StudentIndustryResponse {
  /**
   * 用户行业id
   */
  userIndustryId: string = undefined
  /**
   * 行业id
   */
  industryId = ''
  /**
   * 职称等级id
   */
  professionalQualification = ''
  /**
   * 一级专业类别id
   */
  firstProfessionalCategory = ''
  /**
   * 二级专业类别id
   */
  secondProfessionalCategory: string = undefined

  /**
   * 学员证书信息集合
   */
  studentCertificateList: Array<StudentCertificateInfoVo> = []
}

export default StudentIndustryInfoVo
