######### 下面的配置开发使用 ############
## 指向 yapi 的平台
VUE_APP_YAPI_SERVER = "http://192.168.1.208:3000/"
## 指向 yapi 的平台项目 id
VUE_APP_YAPI_PROJECT_ID = "912"
## 指向 yapi 的微服务项目 id
VUE_APP_YAPI_MICRO_SERVICE_PROJECT_ID = "970"
## 指向 yapi 的业务微服务项目 id
VUE_APP_YAPI_COMMON_MICRO_SERVICE_PROJECT_ID="1044"

VUE_APP_COMMON_MS_APP_KEY = 'common-ms-schemas-4947b0f92ee693c08fbdf80345c21eab7ca31803f00fd3b8e3a17f164622df9f'

# 开发环境的服务配置
VUE_APP_DEVELOPMENT_SERVER = "http://************"
# 开发环境的 websocket 服务地址
VUE_APP_DEVELOPMENT_WS_SERVER = "ws://************:3001"
# mock-serve服务地址
VUE_APP_MOCK_SERVER_HOST = "http://************:8181"

VUE_APP_MS_SERVICE_HOST="https://api.dev.59iedu.com:9443"
