import BannerVo from '@api/service/common/online-school-config/vo/BannerVo'
import LinkVo from '@api/service/common/online-school-config/vo/LinkVo'
import MenuVo from '@api/service/common/online-school-config/vo/MenuVo'
import { FriendLinkTypeEnum } from '../enum/FriendLinkTypeEnum'

import HighQualityCourseVo from './HighQualityCourseVo'
/**
 * @description 首页配置（web）
 */
class WebPortalVo {
  /**
   * 唯一标识
   */
  id = ''
  /**
   * 平台名称
   */
  name = ''
  /**
   * 平台logo
   */
  logo = ''
  /**
   * 图标
   */
  icon = ''
  /**
   * 客服电话图片
   */
  CSPhonePicture = ''
  /**
   * 客服电话
   */
  CSPhone = ''
  /**
   * 客服咨询时间
   */
  CSCallTime = ''
  /**
   * 在线客服代码内容
   */
  CSOnlineCodeId = ''
  /**
   * 移动学习二维码
   */
  mobileQRCode = ''
  /**
   * 移动二维码来源标识，1：使用系统生成二维码 2：自定义
   */
  mobileQRCodeSign = 1
  /**
   * 轮播图列表
   */
  banners: Array<BannerVo> = new Array<BannerVo>()
  /**
   * 栏目列表
   */
  menus: Array<MenuVo> = new Array<MenuVo>()
  /**
   * 培训流程图片
   */
  trainingFlowPicture = ''
  /**
   * 底部落款内容id
   */
  footContentId: string
  /**
   * 底部落款内容
   */
  footContent = ''
  /**
   * 友情链接类型
   */
  friendLinkType: FriendLinkTypeEnum
  /**
   * 友情链接列表
   */
  friendLinks: Array<LinkVo> = new Array<LinkVo>()
  /**
   * 主题颜色
   */
  themeColor = ''
  /**
   * 门户精品课程（分类和课程）列表
   */
  highQualityCourse: HighQualityCourseVo = new HighQualityCourseVo()

  /**
   * cnzz
   */
  cnzz: string = undefined

  /**
   * 目录名
   */
  dirName: string = undefined

  /**
   * web模板id
   */
  webTemplateId: string
  /**
   * web域名
   */
  domainName = ''
  /**
   * h5地址
   */
  domainNameH5: string
  /**
   * 企业微信客服图片
   */
  enterPriseWxCustomer = ''
}

export default WebPortalVo
