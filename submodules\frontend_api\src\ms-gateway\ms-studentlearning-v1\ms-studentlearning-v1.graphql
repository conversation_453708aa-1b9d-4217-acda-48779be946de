"""独立部署的微服务,K8S服务名:ms-studentlearning-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""参训人员证书成果撤回
		@param schemeId
	"""
	TraineesAchievementRevocation(schemeId:String,time:DateTime):Void
	"""申请考试(校验是否强制问卷)"""
	applyExam(studentToken:String):ApplyExamResponse
	"""申请问卷"""
	applyQuestionnaire(request:ApplyQuestionnaireRequest):ApplyQuestionnaireResponse
	"""申请学习token
		培训未开始 = 50002
		培训已结束 = 50003
		学员培训资格已冻结 = 60001
		学员培训资格已失效 = 60002
		学习方式被停用 = 60003
		前置条件未达标 = 60004
		重算未完成 = 60005
		非期数参训资格 = 60006
		非方案参训资格 = 60007
		500 其他未定义异常
		@param request
		@return
		@see ApplyLearningTokenValidateCommand.ApplyLearningTokenValidateResult
	"""
	applyStudentLearningToken(request:ApplyLearningTokenRequest!):String
	"""申请学习token(期数)
		培训未开始 = 50002
		培训已结束 = 50003
		学员培训资格已冻结 = 60001
		学员培训资格已失效 = 60002
		学习方式被停用 = 60003
		前置条件未达标 = 60004
		重算未完成 = 60005
		非期数参训资格 = 60006
		非方案参训资格 = 60007
		500 其他未定义异常
		@param request
		@return
		@see ApplyLearningTokenValidateCommand.ApplyLearningTokenValidateResult
	"""
	applyStudentLearningTokenInIssue(request:ApplyLearningTokenRequest!):String
	"""申请学习token且中断智能学习任务（如果存在且未执行完成）
		培训未开始 = 50002
		培训已结束 = 50003
		学员培训资格已冻结 = 60001
		学员培训资格已失效 = 60002
		学习方式被停用 = 60003
		前置条件未达标 = 60004
		重算未完成 = 60005
		非期数参训资格 = 60006
		非方案参训资格 = 60007
		500 其他未定义异常
		@param request
		@return
		@see ApplyLearningTokenValidateCommand.ApplyLearningTokenValidateResult
	"""
	applyStudentLearningTokenInterruptAutoStudy(request:ApplyLearningTokenRequest!):String
	"""申请学习token校验
		@param request
		@return
	"""
	applyStudentLearningValidate(request:ApplyLearningTokenRequest!):ApplyStudentLearningValidateResponse
	"""申请学习token校验(期数)
		非期数参训资格 = 60006
		非方案参训资格 = 60007
		@param request
		@return
	"""
	applyStudentLearningValidateInIssue(request:ApplyLearningTokenRequest!):ApplyStudentLearningValidateResponse
	"""考核通过后的数据修复"""
	passedAssessDataRepair(request:PassedAssessDataRepairRequest):Void @optionalLogin
	"""参训人员证书成果撤回
		@param studentNo
	"""
	revokeLearningResult(studentNo:String):Void
	"""参训人员数据修复接口
		@param request
	"""
	trainingQualificationDataRepair(request:TrainingQualificationDataRepairRequest):Void @optionalLogin
	"""校验学员是否允许学习(支持校验 方案参训资格 期别参训资格)
		60015 - 期别未开始培训
		60012 - 期别已结束培训
		50006 - 班级正在开通中
		50007 - 班级正在退班中
		50002 - 方案培训未开始
		50003 - 方案培训已结束
		学员培训资格已冻结 = 60001
		学员培训资格已失效 = 60002
		重算未完成 = 60005
		存在未完成的强制调查问卷 = 60008
		@param qualificationId:
		<AUTHOR> By Cb
		@since 2024/11/21 16:18
	"""
	validStudentAllowLearning(qualificationId:String!):Void
}
"""生成学习 token
	<AUTHOR> create 2021/4/15 8:48
"""
input ApplyLearningTokenRequest @type(value:"com.fjhb.ms.student.learning.v1.kernel.gateway.graphql.request.ApplyLearningTokenRequest") {
	"""参训资格ID"""
	qualificationId:String!
	"""学习方式id"""
	learningId:String!
}
"""考核通过后的数据修复请求
	<AUTHOR>
"""
input PassedAssessDataRepairRequest @type(value:"com.fjhb.ms.student.learning.v1.kernel.gateway.graphql.request.PassedAssessDataRepairRequest") {
	aggId:String!
	index:Long!
}
"""参训资格数据修复请求
	<AUTHOR>
"""
input TrainingQualificationDataRepairRequest @type(value:"com.fjhb.ms.student.learning.v1.kernel.gateway.graphql.request.TrainingQualificationDataRepairRequest") {
	aggId:String!
	index:Long!
}
"""申请问卷请求
	<AUTHOR>
	@date 2025/4/22 16:30
"""
input ApplyQuestionnaireRequest @type(value:"com.fjhb.ms.student.learning.v1.kernel.gateway.graphql.request.examInfo.ApplyQuestionnaireRequest") {
	"""学员信息token"""
	studentToken:String
}
type QuestionGroup @type(value:"com.fjhb.domain.exam.api.paper.events.entities.QuestionGroup") {
	sequence:Int!
	questionType:Int!
	groupName:String
	eachQuestionScore:Double!
}
type PreviewPaperPublishConfigureResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.PreviewPaperPublishConfigureResponse") {
	name:String
	description:String
	timeLength:Int!
	totalScore:Double!
	totalQuestionCount:Int!
	groups:[QuestionGroup]
	paperType:Int!
	questions:[BaseQuestionResponse]
}
interface BaseFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.BaseFillAnswerResponse") {
	type:Int!
}
type ChildItemResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.ChildItemResponse") {
	no:Int!
	questionId:String
}
type ChooseAnswerOptionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.ChooseAnswerOptionResponse") {
	id:String
	content:String
	enableFillContent:Boolean
	mustFillContent:Boolean
}
type DisarrayFillAnswerResponse implements BaseFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.DisarrayFillAnswerResponse") {
	disarrayCorrectAnswers:[[String]]
	type:Int!
}
type FillCorrectAnswersResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.FillCorrectAnswersResponse") {
	blankNo:Int!
	answers:[String]
}
type SequenceFillAnswerResponse implements BaseFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.SequenceFillAnswerResponse") {
	sequenceCorrectAnswers:[FillCorrectAnswersResponse]
	type:Int!
}
type SequenceRateFillAnswerResponse implements BaseFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.SequenceRateFillAnswerResponse") {
	sequenceRateCorrectAnswers:[SequenceFillAnswerResponse]
	type:Int!
}
type AskQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.AskQuestionResponse") {
	askAnswer:String
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
interface BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.BaseQuestionResponse") {
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
type FatherQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.FatherQuestionResponse") {
	childQuestions:[ChildItemResponse]
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
type FillQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.FillQuestionResponse") {
	fillCount:Int!
	fillQuestionCorrectAnswer:BaseFillAnswerResponse
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
type MultipleQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.MultipleQuestionResponse") {
	answerOptions:[ChooseAnswerOptionResponse]
	multipleQuestionCorrectAnswerIds:[String]
	multipleAnswer:[String]
	fillContentMap:Map
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
type OpinionQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.OpinionQuestionResponse") {
	correctAnswerText:String
	incorrectAnswerText:String
	opinionQuestionCorrectAnswer:Boolean
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
type RadioQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.RadioQuestionResponse") {
	answerOptions:[ChooseAnswerOptionResponse]
	radioQuestionCorrectAnswerId:String
	radioAnswer:String
	fillContent:String
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
type ScaleQuestionResponse implements BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.ScaleQuestionResponse") {
	scaleType:Int!
	startDegree:String
	endDegree:String
	series:Int!
	initialValue:Int!
	scaleAnswer:Int
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
"""申请问卷返回值
	<AUTHOR>
	@date 2025/4/23 15:33
"""
type ApplyQuestionnaireResponse @type(value:"com.fjhb.ms.student.learning.v1.api.command.questionnaire.response.ApplyQuestionnaireResponse") {
	"""答卷返回对象"""
	answerPaperViewResponse:PreviewPaperPublishConfigureResponse
	"""作答token"""
	answerToken:String
	"""出卷时间"""
	answerExtractionTime:DateTime
}
"""<AUTHOR> create 2022/4/20 10:20"""
type ApplyStudentLearningValidateResponse @type(value:"com.fjhb.ms.student.learning.v1.kernel.gateway.graphql.response.ApplyStudentLearningValidateResponse") {
	"""正常=200.
		培训未开始 = 50002
		培训已结束 = 50003
		学员培训资格已冻结 = 60001
		学员培训资格已失效 = 60002
		学习方式被停用 = 60003
		前置条件未达标 = 60004
		500 其他未定义异常
	"""
	code:String
	"""异常信息"""
	message:String
	"""冻结类型 RETURN_ORDER:退货 EXCHANGE_ORDER:换货"""
	frozenType:String
	"""失效类型 RETURN_ORDER:退货 EXCHANGE_ORDER:换货"""
	invalidType:String
}
"""申请考试返回值
	<AUTHOR>
	@date 2025/4/22 16:03
"""
type ApplyExamResponse @type(value:"com.fjhb.ms.student.learning.v1.kernel.gateway.graphql.response.examInfo.ApplyExamResponse") {
	"""是否需要强制完成调查问卷"""
	needForceQuestionnaire:Boolean!
	"""需要强制完成的调查问卷信息"""
	unaccomplishedQuestionnaire:[UnaccomplishedQuestionnaire]
	"""考试token"""
	examToken:String
}
"""未完成问卷信息
	<AUTHOR>
	@date 2025/4/15 10:15
"""
type UnaccomplishedQuestionnaire @type(value:"com.fjhb.ms.student.learning.v1.kernel.gateway.graphql.response.examInfo.UnaccomplishedQuestionnaire") {
	"""未完成问卷id"""
	unaccomplishedQuestionnaireId:String
	"""学习方式id"""
	learningId:String
	"""允许开始时间"""
	allowStartTime:DateTime
	"""允许结束时间"""
	allowEndTime:DateTime
}

scalar List
