import Question from '@api/service/common/models/exam/question/Question'
import AnswerChildQuestionDTO from '@api/service/common/models/exam/question/answer/AnswerChildQuestionDTO'

export enum QuestionAnswerResult {
  /**
   * 未知, 通常代表试题未阅卷
   */
  UNKNOWN = 'UNKNOWN',
  /**
   * 无需阅卷，如问卷
   */
  UNMARKED = 'UNMARKED',
  /**
   * 正确
   */
  CORRECT = 'CORRECT',
  /**
   * 错误
   */
  WRONG = 'WRONG',
  /**
   * <p>部分正确</p>
   * <p>衡量的标准是最终得分少于标准分数，不是提交答案占正确答案的部分</p>
   * <p>阅卷配置支持配置多选题漏选是否得分</p>
   *
   * @see MissScorePattern
   */
  PARTIALLY = 'PARTIALLY'
}

class AnswerQuestion {
  questionId: string
  loaded = false
  question: Question
  // 试题的分数
  score: number
  // 开始答题时间
  beginAnswerTime: Date
  // 是否打标记、做记号
  flag: boolean
  // 是否已作答
  answered: boolean
  // 提交的作答信息
  submitAnswer: any
  // 答案提交时间
  submitAnswerTime: Date
  // 阅卷得分
  markedScore: number
  // 改卷批注
  comment: string
  // 是否收藏题
  favorite: boolean
  // 试题作答结果 考试没有结果
  answerResult: QuestionAnswerResult
  // 综合题的作答子题，仅记录子题的作答内容和阅卷信息，子题的试题内容在{@link #question}, 两者以试题ID作为关联标识
  childrenAnswers: Array<AnswerChildQuestionDTO>
}

export default AnswerQuestion
