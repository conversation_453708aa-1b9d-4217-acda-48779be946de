schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取学习规则已配置平台级别的行业
		@return
	"""
	getHasPlatformSupplementStudyRuleSettingIndustrysInServicer:[String]
	"""通过网校id查找网校学习规则"""
	getSupplementStudyRuleSettingByServiceId(serviceId:String):OnlineSchoolSupplementStudyServiceResponse
	"""获取学习规则配置详情
		@return
	"""
	getSupplementStudyRuleSettingInServicer(ruleId:String):SupplementStudyRuleSettingResponse
	"""获取学习规则配置已使用的地区最末级code
		@return
	"""
	getUseSupplementStudyRuleSettingRegionInServicer(industryId:String):[String]
	"""分页获取学习规则配置
		@return
	"""
	pageSupplementStudyRuleSettingInServicer(page:Page):PageSupplementStudyRuleSettingResponsePage @page(for:"PageSupplementStudyRuleSettingResponse")
}
type Mutation {
	"""停用学习规则
		@param ruleId 规则id
		@return
	"""
	disAbleSupplementStudyRuleSetting(ruleId:String):GenernalResponse
	"""启用学习规则
		@param ruleId 规则id
		@return
	"""
	enableSupplementStudyRuleSetting(ruleId:String):GenernalResponse
	"""添加学习规则
		@param request
		@return
	"""
	saveSupplementStudyRuleSetting(request:SaveSupplementStudyRuleSettingRequest):SaveSupplementStudyRuleSettingResponse
	"""编辑学习规则"""
	updateSupplementStudyRuleSetting(request:UpdateSupplementStudyRuleSettingRequest):UpdateSupplementStudyRuleSettingResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""@Description 课程学习规则设置
	<AUTHOR>
	@Date 2024/5/11 15:17
"""
input CourseStudyRuleSettingRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.supplementStudyRuleSetting.CourseStudyRuleSettingRequest") {
	"""每日学习时间区间"""
	allowedDailyStudyTimes:[StringTimeRequest]
	"""每日不学习时间区间"""
	disallowedDailyStudyTimes:[StringTimeRequest]
	"""开通班级后开始学习的天数-开始"""
	startDay:Int
	"""开通班级后开始学习的天数-结束"""
	endDay:Int
	"""学习限制类型 (1-课程学习时长 2-课程物理时长)"""
	studyLimitTypes:Int
	"""每日最多学习时间"""
	dailyStudyTime:Int
	"""每日每次学习时间"""
	dailyOneTimeStudyHours:Int
}
"""@Description TODO
	<AUTHOR>
	@Date 2024/5/13 11:35
"""
input IntegerRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.supplementStudyRuleSetting.IntegerRequest") {
	"""开始时间"""
	startTime:Int
	"""结束时间"""
	endTime:Int
}
"""@Description 添加学习规则请求
	<AUTHOR>
	@Date 2024/5/11 16:25
"""
input SaveSupplementStudyRuleSettingRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.supplementStudyRuleSetting.SaveSupplementStudyRuleSettingRequest") {
	"""适用行业id"""
	suitIndustryId:String
	"""适用范围
		@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
		1-平台 2-地区级别 3-培训方案级别
	"""
	suitType:Int
	"""适用属性列表"""
	sultProperties:[SuitPropertyRequest]
	"""培训时间区间"""
	suitTrainingTimes:[SuitTrainingTimeRequest]
	"""是否需要错开开始学习日期"""
	staggerStartTrainingTime:Boolean
	"""课程学习规则配置"""
	courseStudyRuleSetting:CourseStudyRuleSettingRequest
	"""特殊规则配置"""
	specialtyRuleSetting:SpecialRuleSettingRequest
}
"""@Description 特殊规则配置
	<AUTHOR>
	@Date 2024/5/11 15:38
"""
input SpecialRuleSettingRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.supplementStudyRuleSetting.SpecialRuleSettingRequest") {
	"""提前开通时间天数区间"""
	advanceOpenTimeDay:IntegerRequest
}
"""@Description TODO
	<AUTHOR>
	@Date 2024/5/15 9:44
"""
input StringTimeRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.supplementStudyRuleSetting.StringTimeRequest") {
	"""开始时间"""
	startTime:String
	"""结束时间"""
	endTime:String
}
"""@Description TODO
	<AUTHOR>
	@Date 2024/5/14 8:46
"""
input SuitPropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.supplementStudyRuleSetting.SuitPropertyRequest") {
	"""适用范围类型
		@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
		1-平台 2-地区级别 3-培训方案级别
	"""
	type:Int
	"""适用范围值"""
	value:String
}
"""@Description TODO
	<AUTHOR>
	@Date 2024/5/13 11:30
"""
input SuitTrainingTimeRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.supplementStudyRuleSetting.SuitTrainingTimeRequest") {
	"""年度"""
	year:Int
	"""时间请求"""
	timeRequest:StringTimeRequest
}
"""@Description 添加学习规则请求
	<AUTHOR>
	@Date 2024/5/11 16:25
"""
input UpdateSupplementStudyRuleSettingRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.supplementStudyRuleSetting.UpdateSupplementStudyRuleSettingRequest") {
	"""规则id"""
	ruleId:String
	"""适用行业id"""
	suitIndustryId:String
	"""适用范围
		@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
		1-平台 2-地区级别 3-培训方案级别
	"""
	suitType:Int
	"""适用属性列表"""
	sultProperties:[SuitPropertyRequest]
	"""培训时间区间"""
	suitTrainingTimes:[SuitTrainingTimeRequest]
	"""是否需要错开开始学习日期"""
	staggerStartTrainingTime:Boolean
	"""课程学习规则配置"""
	courseStudyRuleSetting:CourseStudyRuleSettingRequest
	"""特殊规则配置"""
	specialtyRuleSetting:SpecialRuleSettingRequest
}
"""<AUTHOR> [2023/7/11 20:54]"""
type GenernalResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.GenernalResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""@Description 课程学习规则设置
	<AUTHOR>
	@Date 2024/5/11 15:17
"""
type CourseStudyRuleSettingResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.supplementStudyRuleSetting.CourseStudyRuleSettingResponse") {
	"""每日学习时间区间"""
	allowedDailyStudyTimes:[RangeLocalDateBaseResponse]
	"""每日不学习时间区间"""
	disallowedDailyStudyTimes:[RangeLocalDateBaseResponse]
	"""开通班级后开始学习的天数区间"""
	dailyOneTimeStudyHoursRange:RangeIntegerBaseResponse
	"""学习限制类型 (1-课程学习时长 2-课程物理时长)"""
	studyLimitTypes:Int
	"""休息时长区间（小时）"""
	restTimeRange:RangeIntegerBaseResponse
	"""每日最多学习时间"""
	dailyStudyTime:Int
	"""每日每次学习时间"""
	dailyOneTimeStudyHours:Int
}
"""@Description 学习规则业务响应
	<AUTHOR>
	@Date 2024/5/20 10:20
"""
type OnlineSchoolSupplementStudyServiceResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.supplementStudyRuleSetting.OnlineSchoolSupplementStudyServiceResponse") {
	"""补学服务id"""
	id:String
	"""平台id"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	serviceId:String
	"""服务状态"""
	status:Boolean
	"""创建人id"""
	createdUserId:String
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
"""@Description 添加学习规则请求
	<AUTHOR>
	@Date 2024/5/11 16:25
"""
type PageSupplementStudyRuleSettingResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.supplementStudyRuleSetting.PageSupplementStudyRuleSettingResponse") {
	"""规则id"""
	ruleId:String
	"""适用行业id"""
	suitIndustryId:String
	"""适用范围
		@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
		1-平台 2-地区级别 3-培训方案级别
	"""
	suitType:Int
	"""适用属性列表"""
	suitProperties:[SuitPropertyResponse]
	"""指定年度"""
	years:[Int]
	"""更新时间"""
	updateTime:DateTime
	"""创建人"""
	createUserId:String
	"""是否启用"""
	enabled:Boolean
}
"""@Description TODO
	<AUTHOR>
	@Date 2024/5/14 17:09
"""
type RangeIntegerBaseResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.supplementStudyRuleSetting.RangeIntegerBaseResponse") {
	"""开始时间"""
	startTime:Int
	"""结束时间"""
	endTime:Int
}
"""@Description TODO
	<AUTHOR>
	@Date 2024/5/14 17:09
"""
type RangeLocalDateBaseResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.supplementStudyRuleSetting.RangeLocalDateBaseResponse") {
	"""开始时间"""
	startTime:String
	"""结束时间"""
	endTime:String
}
"""@Description 保存学习规则响应
	<AUTHOR>
	@Date 2024/5/11 16:29
"""
type SaveSupplementStudyRuleSettingResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.supplementStudyRuleSetting.SaveSupplementStudyRuleSettingResponse") {
	"""学习规则id"""
	ruleId:String
	"""重复的code"""
	codeList:[String]
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""@Description 特殊规则配置
	<AUTHOR>
	@Date 2024/5/11 15:38
"""
type SpecialRuleSettingResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.supplementStudyRuleSetting.SpecialRuleSettingResponse") {
	"""提前开通时间天数区间"""
	advanceOpenTimeDay:RangeIntegerBaseResponse
}
"""@Description TODO
	<AUTHOR>
	@Date 2024/5/14 8:46
"""
type SuitPropertyResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.supplementStudyRuleSetting.SuitPropertyResponse") {
	"""适用范围类型
		@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
		1-平台 2-地区级别 3-培训方案级别
	"""
	type:Int
	"""适用范围值"""
	value:String
}
"""@Description TODO
	<AUTHOR>
	@Date 2024/5/13 11:30
"""
type SuitTrainingTimeResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.supplementStudyRuleSetting.SuitTrainingTimeResponse") {
	"""年度"""
	year:Int
	"""时间范围"""
	trainingTime:RangeLocalDateBaseResponse
}
"""@Description 添加学习规则请求
	<AUTHOR>
	@Date 2024/5/11 16:25
"""
type SupplementStudyRuleSettingResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.supplementStudyRuleSetting.SupplementStudyRuleSettingResponse") {
	"""规则id"""
	ruleId:String
	"""适用行业id"""
	suitIndustryId:String
	"""适用范围
		@see com.fjhb.platform.jxjy.v1.kernel.appservice.supplementStudyRuleSetting.enums.SuitTypes
		1-平台 2-地区级别 3-培训方案级别
	"""
	suitType:Int
	"""适用属性列表"""
	suitProperties:[SuitPropertyResponse]
	"""培训时间区间"""
	suitTrainingTimes:[SuitTrainingTimeResponse]
	"""是否需要错开开始学习日期"""
	staggerStartTrainingTime:Boolean
	"""课程学习规则配置"""
	courseStudyRuleSetting:CourseStudyRuleSettingResponse
	"""特殊规则配置"""
	specialtyRuleSetting:SpecialRuleSettingResponse
}
"""@Description 编辑学习规则响应
	<AUTHOR>
	@Date 2024/5/11 16:29
"""
type UpdateSupplementStudyRuleSettingResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.supplementStudyRuleSetting.UpdateSupplementStudyRuleSettingResponse") {
	"""重复的code"""
	codeList:[String]
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
type PageSupplementStudyRuleSettingResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PageSupplementStudyRuleSettingResponse]}
