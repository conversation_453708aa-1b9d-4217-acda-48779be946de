import MsMySchemeQueryFrontGatewayCourseLearningForestage, {
  LearningRegisterRequest,
  SchemeSkuPropertyRequest,
  SchemeSkuPropertyResponse,
  SortPolicy,
  StudentSchemeLearningSortField,
  StudentSchemeLearningSortRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import SkuPropertyConvertUtils, {
  ComplexSkuPropertyResponse,
  SchemeSkuInfo
} from '@api/service/customer/train-class/Utils/SkuPropertyConvertUtils'
import { Page } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { StudentSchemeLearningRequest } from '@api/service/customer/train-class/query/vo/MyTrainFilter'
import SkuPropertyResponseVo from '@api/service/customer/train-class/query/vo/SkuPropertyResponseVo'
import MyTrainClassCommodityVo from '@api/service/diff/customer/xmlg/train-class/model/MyTrainClassCommodityVo'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import QueryPlatformForestage from '@api/service/diff/common/xmlg/dictionary/QueryPlatformForestage'
import IntelligenceLearningModule from '@api/service/customer/learning/choose-course/choose-course-optimize/IntelligenceLearningModule'
import xmlgGateway from '@api/diff-gateway/platform-jxjypxtypt-xmlg-school'
import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
/**
 * 用户域获取我的培训班商品列表
 */
class QueryMyTrainClassCommodityList {
  // region properties
  /**
   *总数目，类型为number
   */
  totalSize = 0
  /**
   *我的培训班商品列表，类型为MyTrainClassCommodityVo[]
   */
  trainClassCommodityList: MyTrainClassCommodityVo[] = []
  // endregion
  // region methods

  /**
   * 获取培训班列表 ---- 优化后
   *
   * 培训结果
   <p>
   -1:未知，培训尚未完成
   1:培训合格
   0:培训不合格
   @see StudentTrainingResults
   */

  async queryTrainClassCommodityListOptimize(
    page: Page,
    filter: {
      year?: number
      trainResult?: number
      trainingChannelId?: string
      trainingMode?: TrainingModeEnum
    }
  ): Promise<Array<MyTrainClassCommodityVo>> {
    const filterCommodity = new StudentSchemeLearningRequest()
    filterCommodity.scheme.skuProperty = new SchemeSkuPropertyRequest()
    filterCommodity.scheme.skuProperty.trainingWay = filter.trainingMode ? [filter.trainingMode] : undefined
    if (filter.year && filter.year != -2) {
      filterCommodity.scheme.skuProperty.year = [filter.year.toString()]
    }
    if (filter.trainResult != -2) {
      if (filter.trainResult == 1) {
        filterCommodity.studentLearning.trainingResultList = [1]
      } else {
        filterCommodity.studentLearning.trainingResultList = [-1, 0]
      }
    }
    filterCommodity.learningRegister = new LearningRegisterRequest()
    filterCommodity.learningRegister.status = [1, 2]
    if (filter.trainingChannelId) {
      filterCommodity.trainingChannelId = filter.trainingChannelId
      filterCommodity.saleChannels = [2]
    }
    const res = await xmlgGateway.pageSchemeLearningInMyself({
      page: { pageNo: page.pageNo, pageSize: page.pageSize },
      request: filterCommodity,
      sort: [{ field: StudentSchemeLearningSortField.REGISTER_TIME, policy: SortPolicy.DESC }]
    })
    if (res.status.isSuccess()) {
      const tmpArr = res.data.currentPageData.map((item) => {
        return Object.assign(new MyTrainClassCommodityVo(), item)
      })
      const qualificationIds: Map<string, string> = new Map()
      tmpArr.forEach((item) => {
        item.skuValueNameProperty = new SkuPropertyResponseVo()
        item.skuValueNameProperty.year.skuPropertyName = item.scheme.skuProperty.year.skuPropertyValueId
        // 培训形式
        item.skuValueNameProperty.trainingMode.skuPropertyValueId = item.scheme.skuProperty.trainingWay
          ?.skuPropertyValueId as TrainingModeEnum
        if (item.skuValueNameProperty.trainingMode.skuPropertyValueId) {
          item.skuValueNameProperty.trainingMode.skuPropertyName = TrainingMode.map.get(
            item.skuValueNameProperty.trainingMode.skuPropertyValueId
          )
        }
        qualificationIds.set(item.scheme.schemeId, item.qualificationId)
      })
      const configJsonMap = await this.queryConfigs(qualificationIds)
      const schemeIds = tmpArr?.map((item) => item.scheme.schemeId) || []
      const currentConfigMap = await this.getCurrentConfig(schemeIds)
      // 智能学习
      let IntelligenceLearningMap = new Map<string, string>()
      if (schemeIds?.length) {
        // 是否智能学习配置
        IntelligenceLearningMap = await new IntelligenceLearningModule().doBatchCheck(schemeIds)
      }
      for (const tmpItem of tmpArr) {
        const configJson = configJsonMap.get(tmpItem.qualificationId)
        if (configJson) {
          const getExtendProperties = (key: string) =>
            configJson.extendProperties.find((item: any) => item.name == key)?.value
          tmpItem.notice = configJson.notice
          tmpItem.showNoticeDialog = getExtendProperties('showNoticeDialog')
          tmpItem.registerBeginDate = configJson.registerBeginDate
          tmpItem.registerEndDate = configJson.registerEndDate
          tmpItem.trainingBeginDate = configJson.trainingBeginDate
          tmpItem.trainingEndDate = configJson.trainingEndDate
          tmpItem.picture = configJson.picture
          tmpItem.schemeType = SchemeTypeEnum[configJson.type as string]
          const { schemeAssessItem } = Scheme.parseTrainClassAssess(configJson)
          if (schemeAssessItem) {
            const creditResult = schemeAssessItem.learningResults.find((item: any) => {
              return item.type == 1
            })
            tmpItem.period = creditResult.grade
            tmpItem.trainClassName = configJson.name

            const learningResult = schemeAssessItem.learningResults.find((item: any) => {
              return item.type == 2
            })
            if (learningResult) {
              tmpItem.hasLearnResult = true
              tmpItem.learningResultId = learningResult.certificateTemplateId
              tmpItem.openPrintTemplate = learningResult.openPrintTemplate
            } else {
              tmpItem.hasLearnResult = false
              tmpItem.learningResultId = ''
            }
          }

          const externalTrainingPlatform = configJson.extendProperties?.find(
            (item: { name: string; value: string }) => {
              return item.name == 'externalTrainingPlatform'
            }
          )
          if (tmpItem.schemeType == SchemeTypeEnum.trainingCooperation && externalTrainingPlatform) {
            await QueryPlatformForestage.queryList()
            tmpItem.thirdPartyInfo = {
              id: externalTrainingPlatform.value as string,
              name: QueryPlatformForestage.map.get(externalTrainingPlatform.value)?.name
            }
          }
        }
        const currentConfig = currentConfigMap.get(tmpItem.scheme.schemeId)
        if (currentConfig) {
          const getExtendProperties = (key: string) =>
            currentConfig.extendProperties.find((item: any) => item.name == key)?.value
          tmpItem.notice = currentConfig.notice
          tmpItem.showNoticeDialog = getExtendProperties('showNoticeDialog')
          tmpItem.trainingBeginDate = currentConfig.trainingBeginDate
          tmpItem.trainingEndDate = currentConfig.trainingEndDate
          /*            列表读最新配置名称           */
          const { schemeAssessItem } = Scheme.parseTrainClassAssess(currentConfig)
          if (schemeAssessItem) {
            const creditResult = schemeAssessItem.learningResults?.find((item: any) => {
              return item.type == 1
            })
            tmpItem.period = creditResult?.grade || tmpItem.period
            /*           读取最新证明配置           */
            const learningResult = schemeAssessItem.learningResults?.find((item: any) => {
              return item.type == 2
            })
            tmpItem.picture = currentConfig.picture

            if (learningResult) {
              tmpItem.hasLearnResult = true
              tmpItem.learningResultId = learningResult.certificateTemplateId
              tmpItem.openPrintTemplate = learningResult.openPrintTemplate
            } else {
              tmpItem.hasLearnResult = false
              tmpItem.learningResultId = ''
            }
          }
        }
      }
      const skuRequest = [] as SchemeSkuInfo[]
      tmpArr?.forEach((item) => {
        skuRequest.push(new SchemeSkuInfo(item.scheme.schemeId, item.scheme.skuProperty as ComplexSkuPropertyResponse))
      })
      const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(skuRequest)
      tmpArr?.forEach((item) => {
        const skuInfo = skuInfos.find((el) => el.id === item.scheme.schemeId)
        if (skuInfo) item.skuValueNameProperty = skuInfo.skuName
      })
      // 智能学习
      tmpArr?.forEach((item) => {
        const intelligentStatus = IntelligenceLearningMap[item.scheme.schemeId]
        item.intelligentLearning = intelligentStatus
      })
      this.trainClassCommodityList = tmpArr
      console.log(' this.trainClassCommodityList', this.trainClassCommodityList)
      this.totalSize = res.data.totalSize
    }
    //pageCommoditySkuCustomerPurchaseInServicer
    return this.trainClassCommodityList
  }
  /**
   * 获取培训班属性 --- 批量
   */
  async queryConfigs(qualificationIds: Map<string, string>): Promise<any> {
    //获取培训班配置模板jsonString
    const schemaId = [...qualificationIds.keys()]
    const qualificationIdList = [...qualificationIds.values()]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = schemaId.length || 1
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForestage.pageStudentSchemeConfigInMySelf({
      page,
      request: {
        qualificationIdList
      }
    })
    const configJsonMap: Map<string, any> = new Map()
    if (res.data && res.data.currentPageData && res.data.currentPageData.length) {
      res.data.currentPageData.forEach((item) => {
        //
        const temp = qualificationIds.get(item.schemeId)
        if (temp) {
          configJsonMap.set(temp, JSON.parse(item.schemeConfig))
        }
      })
    }
    return configJsonMap
  }

  /**
   * 获取最新配置
   * @param schemeIds
   */
  async getCurrentConfig(schemeIds: string[]) {
    const configJsonMap: Map<string, any> = new Map()
    if (schemeIds.length) {
      const page = new Page()
      page.pageNo = 1
      page.pageSize = schemeIds.length || 1
      const res = await MsMySchemeQueryFrontGatewayCourseLearningForestage.pageSchemeConfigInServicer({
        page,
        schemeIds
      })
      res.data.currentPageData.forEach((item) => {
        configJsonMap.set(item.schemeId, JSON.parse(item.schemeConfig))
      })
    }
    return configJsonMap
  }

  // endregion
}
export default QueryMyTrainClassCommodityList
