/**
 * 考试查询工厂类
 */
import QueryExamRecordList from '@api/service/customer/exam/query/QueryExamRecordList'
import QueryPraticeRecordList from '@api/service/customer/exam/query/QueryPraticeRecordList'
import QueryQuizRecordList from '@api/service/customer/exam/query/QueryQuizRecordList'
import QueryPaperDetail from '@api/service/customer/exam/query/QueryPaperDetail'
import QueryPaperPreview from '@api/service/customer/exam/query/QueryPaperPreview'

class QueryExamFactory {
  // region properties
  // endregion
  // region methods
  /**
   * 获取查询考试记录列表
   */
  getQueryExamRecordList() {
    return new QueryExamRecordList()
  }

  /**
   * 获取查询练习记录列表
   */
  getQueryPraticeRecordList() {
    return new QueryPraticeRecordList()
  }

  /**
   * 获取查询课后测验记录列表
   */
  getQueryQuizRecordList() {
    return new QueryQuizRecordList()
  }

  /**
   * 获取卷子详情
   */
  getQueryPaperDetail() {
    return new QueryPaperDetail()
  }
  /**
   * 获取卷子预览
   */
  getQueryPaperPreview() {
    return new QueryPaperPreview()
  }
  // endregion
}

export default QueryExamFactory
