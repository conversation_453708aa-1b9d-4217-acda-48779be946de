import getQuestionnaireAnswerStaitsticsInServicer from './queries/getQuestionnaireAnswerStaitsticsInServicer.graphql'
import getQuestionnaireDetailInServicer from './queries/getQuestionnaireDetailInServicer.graphql'
import getQuestionnaireTemplateDetailInServicer from './queries/getQuestionnaireTemplateDetailInServicer.graphql'
import pageIssueQuestionnaireInServicer from './queries/pageIssueQuestionnaireInServicer.graphql'
import pageIssueQuestionnaireStatisticsInServicer from './queries/pageIssueQuestionnaireStatisticsInServicer.graphql'
import pageQuestionnaireAnswerContentInServicer from './queries/pageQuestionnaireAnswerContentInServicer.graphql'
import pageQuestionnaireAnswerInServicer from './queries/pageQuestionnaireAnswerInServicer.graphql'
import pageQuestionnaireSchemeInServicer from './queries/pageQuestionnaireSchemeInServicer.graphql'
import pageQuestionnaireTemplateInSchool from './queries/pageQuestionnaireTemplateInSchool.graphql'
import pageSchemeQuestionnaireStatisticsInServicer from './queries/pageSchemeQuestionnaireStatisticsInServicer.graphql'

export {
  getQuestionnaireAnswerStaitsticsInServicer,
  getQuestionnaireDetailInServicer,
  getQuestionnaireTemplateDetailInServicer,
  pageIssueQuestionnaireInServicer,
  pageIssueQuestionnaireStatisticsInServicer,
  pageQuestionnaireAnswerContentInServicer,
  pageQuestionnaireAnswerInServicer,
  pageQuestionnaireSchemeInServicer,
  pageQuestionnaireTemplateInSchool,
  pageSchemeQuestionnaireStatisticsInServicer
}
