import getCourseLearningCount from './queries/getCourseLearningCount.graphql'
import getUserLastCourseLearning from './queries/getUserLastCourseLearning.graphql'
import listUserCourse from './queries/listUserCourse.graphql'
import listUserCourseLearningSchedule from './queries/listUserCourseLearningSchedule.graphql'
import listUserCoursePool from './queries/listUserCoursePool.graphql'
import listUserCoursewareLearningSchedule from './queries/listUserCoursewareLearningSchedule.graphql'
import listUserUnSelectCourse from './queries/listUserUnSelectCourse.graphql'
import pageUserCourseLearning from './queries/pageUserCourseLearning.graphql'
import statisticCourseLearning from './queries/statisticCourseLearning.graphql'
import statisticUserLearningCourseLearningInfo from './queries/statisticUserLearningCourseLearningInfo.graphql'

export {
  getCourseLearningCount,
  getUserLastCourseLearning,
  listUserCourse,
  listUserCourseLearningSchedule,
  listUserCoursePool,
  listUserCoursewareLearningSchedule,
  listUserUnSelectCourse,
  pageUserCourseLearning,
  statisticCourseLearning,
  statisticUserLearningCourseLearningInfo
}
