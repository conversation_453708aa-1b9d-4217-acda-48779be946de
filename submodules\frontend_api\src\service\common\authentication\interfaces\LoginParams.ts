export abstract class Params {
  // 平台/项目的域名
  service?: string
  // 账号类型
  accountType: AccountType
  // 由CAPTCHA（验证码）服务通过验证后返回的token
  captchaToken?: string
}

/**
 * 客户端: 1:web、2:app、3: wechat 小程序
 */
export enum ClientType {
  web = 1,
  app,
  wechat
}

export class SendMessageParams extends Params {
  // 电话号码
  phoneNumber: string
  client?: ClientType = ClientType.web
}

export class ShortMessageLoginParams {
  // 电话号码
  phoneNumber: string
  // 短信码
  smsCode: string
  // 是否长期有效？ 长期是几天有效，由平台/项目在后端指定，前端只传标识
  longTerm?: boolean
  // * token
  token?: string
}

export class DingDingUserIdLoginParams {
  // 平台/项目的访问URL
  service: string
  // 钉钉用户ID
  dingTalkUserId: string
  // 用户类型 1学员 2管理员
  userType: number
}

/**
 * 登录参数
 */
export class LoginParams extends Params {
  // 账号
  account: string
  // 密码
  password: string
  // 是否长期有效？ 长期是几天有效，由平台/项目在后端指定，前端只传标识
  longTerm: boolean
  // * 验证成功返回的token
  token?: string
  // * 用户输入的账号
  identity: string
  // * 验证码
  captchaValue?: string
}

export class AuthTokenLoginParams {
  authToken = ''
}

/**
 * 请求获取钉钉登录信息的参数
 */
export class ApplyDingDingLoginQRCodeParams {
  // 平台/项目的域名
  service?: string
  // 账号类型
  accountType: AccountType
  // 第三方类型  61：钉钉第三方登录
  thirdPartyType: number
  // 钉钉扫码的回调地址
  resultUrl?: string
  connectIdType: number
}

/**
 * 请求获取钉钉登录信息的返回值
 */
export class ApplyDingDingLoginQRCodeData {
  // 如果是跳转第三方页面扫码 用 authUrl
  authUrl: string
  // 第三方类型  61：钉钉第三方登录
  thirdPartyType: number
  // 内嵌的方式  appId  = clientId
  clientId?: string
  state: string
  redirectUrl: string
}

/**
 * 账号类型
 */
export enum AccountType {
  // 运营域
  admin = '2',
  // 客户域
  customer = '1'
}

/**
 * 角色类型
 */
export enum RoleTypeEnum {
  // 学员
  STUDENT = 'student',
  // 超管
  ADMIN = 'admin',
  // 供应商
  PROVIDER = 'provider',
  //专题管理员
  ZTGLY = 'ztgly',
  // 地区管理员
  REGION = 'region',
  // 运营域管理员
  OPERATION = 'operation',
  // 集体报名管理员
  GROUPADMIN = 'groupadmin',
  // 分销管理员
  DISTRIBUTOR = 'distributor'
}

export class WeiXinUnionIdLoginParams {
  // 1 :学员, 2:管理员
  accountType: string
  // 平台/项目的访问URL
  service: string
  // 第三方类型 QQ :21 ,微信WEB :31, 微信WEB:41，微信公众号:42，微信小程序:43，微信APP:44
  type: number
  // 第三方授权返回的unionId
  unionId: string
  // 第三方授权返回的openId
  openId: string
  // 当前登录使用的ID类型 1:unionId, 2:openId(默认)
  loginIdType = '1'
}
