/*
 * @Author: z张仁榕
 * @Date: 2025-01-08 10:41:14
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-01-21 19:37:32
 * @Description:
 */
import { BatchPrintingRequest, ImportListDataRequest } from '@api/platform-gateway/platform-certificate-v1'
import { ImportStatusEnum } from '@api/service/management/personal-leaning/import-print/enums/ImportStatus'
import { ServiceTypeEnum } from '@api/service/management/personal-leaning/mutation/enums/ServiceType'

export default class QueryStudentParams {
  /**
   * 姓名
   */
  name = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 培训方案
   */
  scheme = ''
  /**
   * 类型
   */
  type: ImportStatusEnum = ImportStatusEnum.success
  /**
   * 打印类型
   */
  serviceType: ServiceTypeEnum = null

  static to(vo: QueryStudentParams) {
    const dto = new ImportListDataRequest()
    dto.name = vo.name
    dto.idCard = vo.idCard
    dto.schemeName = vo.scheme
    dto.importStatus = vo.type
    return dto
  }
  static toBatch(vo: QueryStudentParams) {
    const dto = new BatchPrintingRequest()
    dto.name = vo.name
    dto.idCard = vo.idCard
    dto.schemeName = vo.scheme
    dto.importStatus = vo.type
    dto.servicerType = vo.serviceType || undefined
    return dto
  }
}
