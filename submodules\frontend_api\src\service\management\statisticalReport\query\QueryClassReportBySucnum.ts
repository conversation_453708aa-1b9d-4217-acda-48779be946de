import msTrade, {
  CommodityOpenReportSortField,
  CommodityOpenReportSortRequest,
  SortPolicy,
  TradeReportRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { CommodityOpenReportFormResponseVo } from '@api/service/management/statisticalReport/query/vo/CommodityOpenReportFormResponseVo'
export default class QueryClassReportBySucnum {
  async listCommodityOpenReportFormsBeSoldInServicer(limit = 10) {
    const sortModel = new CommodityOpenReportSortRequest()
    sortModel.field = CommodityOpenReportSortField.TRADE_SUCCESS_COUNT
    sortModel.policy = SortPolicy.DESC
    const res = await msTrade.listCommodityOpenReportFormsBeSoldInServicer({
      limit: limit,
      request: new TradeReportRequest(),
      sortRequest: sortModel
    })
    const tmpArr: CommodityOpenReportFormResponseVo[] = []
    if (res.status.isSuccess()) {
      for (const tmpArrElement of res.data) {
        const reportVo = new CommodityOpenReportFormResponseVo()
        Object.assign(reportVo, tmpArrElement)
        reportVo.trainClassDetail.saleTitle = tmpArrElement.schemeName || ''
        tmpArr.push(reportVo)
      }
    }
    return tmpArr
  }
}
