<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--选择具体导入任务-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">选择具体导入任务</el-button>
        <el-drawer
          title="选择具体导入任务"
          :visible.sync="dialog1"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="6">
                  <el-form-item label="执行状态">
                    <el-select v-model="select" clearable filterable placeholder="请选择执行状态">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="9">
                  <el-form-item label="执行时间">
                    <el-date-picker
                      v-model="form.date1"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="任务名称" min-width="280">
                <template>任务名称任务名称任务名称</template>
              </el-table-column>
              <el-table-column label="任务处理时间 - 任务结束时间" min-width="220">
                <template>
                  <p>处理：2020-11-11 12:20:20</p>
                  <p>结束：2020-11-11 12:20:20</p>
                </template>
              </el-table-column>
              <el-table-column label="任务执行状态" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-tag type="info">未执行</el-tag>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <el-tag type="primary">执行中</el-tag>
                  </div>
                  <div v-else>
                    <el-tag type="success">执行完成</el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="任务处理结果" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="danger" class="badge-status">失败</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">成功</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="处理总条数/成功条数/失败条数" width="240" align="center">
                <template>10 / 5 / 5</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-radio v-model="checked" label="选择"></el-radio>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
            <div class="m-btn-bar f-tc f-mt20">
              <el-button>取消</el-button>
              <el-button type="primary">确定</el-button>
            </div>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
