<template>
  <el-drawer
    :title="dialogType == 0 ? '修改发票信息' : '重新开票'"
    :visible.sync="dialogVisible"
    size="800px"
    custom-class="m-drawer"
  >
    <div class="drawer-bd">
      <div class="m-tit">
        <span class="tit-txt">原发票信息</span>
      </div>
      <el-form ref="form" label-width="150px" class="m-text-form f-mt10">
        <el-col :span="12">
          <el-form-item label="发票类型：" class="is-text">
            {{ invoiceMapType[invoiceType] }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票抬头：" class="is-text">
            【{{ invoiceTitleMapType[invoiceInfo.titleType] }}】{{ invoiceInfo.title }}
          </el-form-item>
        </el-col>
        <div v-show="invoiceInfo.titleType == 2">
          <el-col :span="24">
            <el-form-item label="统一社会信用代码：" class="is-text">{{ invoiceInfo.taxpayerNo }}</el-form-item>
          </el-col>
          <el-col :span="12" v-if="invoiceInfo.bankName">
            <el-form-item label="开户银行：" class="is-text">{{ invoiceInfo.bankName }}</el-form-item>
          </el-col>
          <el-col :span="12" v-if="invoiceInfo.account">
            <el-form-item label="开户账号：" class="is-text">{{ invoiceInfo.account }}</el-form-item>
          </el-col>
          <el-col :span="12" v-if="invoiceInfo.rePhone">
            <el-form-item label="注册电话：" class="is-text">{{ invoiceInfo.rePhone }}</el-form-item>
          </el-col>
          <el-col :span="12" v-if="invoiceInfo.address">
            <el-form-item label="注册地址：" class="is-text">{{ invoiceInfo.address }}</el-form-item>
          </el-col>
        </div>
        <el-col :span="12" v-show="invoiceType != '1'">
          <el-form-item label="手机号码：" class="is-text">{{ invoiceInfo.phone }}</el-form-item>
        </el-col>
        <el-col :span="12" v-show="invoiceType != '1'">
          <el-form-item label="电子邮箱：" class="is-text">{{ invoiceInfo.email }}</el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="发票备注：" class="is-text" v-if="invoiceInfo.remark">
            {{ invoiceInfo.remark }}
          </el-form-item>
        </el-col>
      </el-form>
      <el-divider class="m-divider"></el-divider>
      <div class="m-tit">
        <span class="tit-txt">修改的发票信息</span>
      </div>
      <el-form
        ref="modifyInvoiceForm"
        :model="updateInfo"
        :rules="rulesMapType[invoiceType]"
        label-width="150px"
        class="m-form f-mt10"
      >
        <el-form-item label="发票类型：" class="is-text"> {{ invoiceMapType[invoiceType] }}</el-form-item>
        <el-form-item label="发票抬头：" prop="title">
          <el-radio-group v-model="updateInfo.titleType" @change="changeTitleType" class="f-mt10">
            <el-radio class="f-mb15" :label="1">
              <span class="f-mr10">个人</span>
            </el-radio>
            <el-radio :label="2">
              <span class="f-mr10">单位</span>
            </el-radio>
          </el-radio-group>
          <el-input v-model="updateInfo.title" clearable placeholder="请输入抬头" />
        </el-form-item>
        <el-form-item label="统一社会信用代码：" prop="taxpayerNo" ref="taxpayerNoRef" v-if="updateInfo.titleType == 2">
          <el-input
            v-model="updateInfo.taxpayerNo"
            @input="upperCase"
            clearable
            placeholder="请输入18位统一社会信用代码"
            class="form-l"
          />
        </el-form-item>
        <el-form-item label=" " class="is-text" v-show="updateInfo.titleType == 2">
          <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
        </el-form-item>
        <div class="bg-gray f-plr20 f-mb20 f-pb10" v-show="updateInfo.titleType == 2">
          <div class="f-pt5 f-pb10">
            <el-divider content-position="left">
              <span class="f-cr">* 以下内容请根据需要填写，请全部填写或者全部不填写。</span>
            </el-divider>
          </div>
          <el-form-item label="开户银行：">
            <el-input v-model="updateInfo.bankName" clearable placeholder="请输入开户银行" class="form-l" />
          </el-form-item>
          <el-form-item label="开户帐号：">
            <el-input v-model="updateInfo.account" clearable placeholder="请输入开户帐号" class="form-l" />
          </el-form-item>
          <el-form-item label="注册电话：">
            <el-input
              v-model="updateInfo.rePhone"
              clearable
              maxlength="20"
              show-word-limit
              placeholder="请输入单位注册电话"
              class="form-m"
            />
          </el-form-item>
          <el-form-item label="注册地址：">
            <el-input
              v-model="updateInfo.address"
              clearable
              maxlength="100"
              show-word-limit
              placeholder="请输入单位注册地址"
            />
          </el-form-item>
        </div>
        <el-form-item label="手机号：" prop="phone" v-if="invoiceType !== '1'">
          <el-input v-model="updateInfo.phone" clearable placeholder="请输入11位手机号码" class="form-l" />
        </el-form-item>
        <el-form-item label="电子邮箱：" prop="email" v-if="invoiceType !== '1'">
          <el-input v-model="updateInfo.email" clearable placeholder="请输入电子邮箱" class="form-l" />
        </el-form-item>
        <el-form-item label="发票备注：">
          <el-input
            v-model="updateInfo.remark"
            maxlength="100"
            type="textarea"
            :rows="3"
            placeholder="请填写发票备注信息"
            class="f-mt5 f-mb15"
          ></el-input>
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="dialogVisible = false">取消</el-button>
      <el-button type="primary" @click="commitApply">
        {{ dialogType == 0 ? '保存发票信息' : '保存并重开发票' }}
      </el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Ref, Watch } from 'vue-property-decorator'
  import MutationInvoice from '@api/service/management/trade/single/invoice/mutation/MutationInvoice'
  import UpdateElectronicInvoiceRequest from '@api/service/management/trade/single/invoice/mutation/dto/InvoiceDetail'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import {
    InvoiceCategoryEnum,
    InvoiceTypeEnum,
    TitleTypeEnum
  } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import InvoiceListResponse from '@api/service/management/trade/single/invoice/mutation/vo/InvoiceListResponse'
  import InvoiceDetail from '@api/service/management/trade/single/invoice/mutation/dto/InvoiceDetail'

  @Component
  export default class extends Vue {
    @Ref('modifyInvoiceForm') modifyInvoiceForm: any
    @Prop({
      type: Boolean,
      default: false
    })
    dialogCtrl: boolean

    @Prop({
      type: String,
      default: '1'
    })
    invoiceType: string

    @Prop({
      type: String,
      default: ''
    })
    invoiceId: string

    @Prop({
      type: Number,
      default: 0
    })
    dialogType: number
    invoiceMapType = {
      ['1']: '增值税电子普通发票（自动开票）',
      ['2']: '增值税电子发票（线下开票）'
    }

    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }

    // 原发票信息
    invoiceInfo: InvoiceListResponse = new InvoiceListResponse()
    // 修改的发票信息
    updateInfo: InvoiceDetail = new InvoiceDetail()

    //自动开票校验规则
    autoInvoiceRules = {
      title: [
        {
          required: true,
          message: '请填写抬头',
          trigger: ['change', 'blur']
        }
      ],
      taxpayerNo: [
        {
          required: true,
          message: '请输入统一社会信用代码',
          trigger: ['change', 'blur']
        }
      ]
    }

    //线下开票校验规则
    offlineInvoiceRules = {
      invoiceTitle: [
        {
          required: true,
          message: '请填写抬头',
          trigger: ['change', 'blur']
        }
      ],
      taxpayerNo: [
        {
          required: true,
          message: '请输入统一社会信用代码',
          trigger: ['change', 'blur']
        }
      ],
      phone: [
        {
          required: true,
          message: '请输入手机号码',
          trigger: ['change', 'blur']
        }
      ],
      email: [
        {
          required: true,
          message: '请输入电子邮箱',
          trigger: ['change', 'blur']
        }
      ]
    }

    rulesMapType = {
      [InvoiceTypeEnum.ONLINE]: this.autoInvoiceRules,
      [InvoiceTypeEnum.OFFLINE]: this.offlineInvoiceRules
    }

    dialogVisible = false

    @Watch('dialogCtrl')
    changeDialogCtrl() {
      if (this.dialogCtrl) {
        this.getInvoiceInfo()
      }
      this.dialogVisible = this.dialogCtrl
    }

    @Watch('dialogVisible')
    changeDialogVisible() {
      this.$emit('update:dialogCtrl', this.dialogVisible)
    }

    changeTitleType() {
      const el: any = this.$refs['taxpayerNoRef']
      if (el) {
        if (this.updateInfo.titleType == 1) {
          el.clearValidate()
        } else {
          el.validate()
        }
      }
    }

    upperCase() {
      const arr = this.updateInfo.taxpayerNo.split('')
      let newStr = ''
      arr.forEach((value) => {
        if (value >= 'a' && value <= 'z') {
          newStr += value.toUpperCase()
        } else {
          newStr += value
        }
      })
      this.updateInfo.taxpayerNo = newStr
    }

    doSave() {
      this.modifyInvoiceForm.validate((valid: boolean) => {
        if (valid) {
          this.doModify()
        }
      })
    }

    async doModify() {
      if (this.updateInfo.titleType == 1) {
        this.updateInfo.taxpayerNo = ''
        this.updateInfo.account = ''
        this.updateInfo.bankName = ''
        this.updateInfo.rePhone = ''
        this.updateInfo.address = ''
      }
      const res = await TradeModule.singleTradeBatchFactor.invoiceFactor.mutationInvoice.updateElectronicInvoice(
        this.updateInfo
      )
      if (res.status.isSuccess()) {
        this.$message.success('修改发票成功')
        this.dialogVisible = false
        this.$emit('callBack')
      } else {
        const errorDtoMsg = res.status?.errors[0]?.message || ''
        const msgList = errorDtoMsg.split(':')
        let msg = undefined
        if (msgList?.length) {
          msg = msgList[msgList.length - 1] || ''
        }

        this.$message.error(msg || '修改发票失败')
      }
    }

    //四个全填和全不填的以及格式校验
    validUnitInfo() {
      const titleReg = /^[0-9a-zA-Z\u4e00-\u9fa5（）()《》—-]+$/
      const taxpayerNoReg = /^[A-Za-z0-9]{18}$/
      if (
        this.updateInfo.titleType === TitleTypeEnum.UNIT &&
        (this.updateInfo.bankName || this.updateInfo.account || this.updateInfo.rePhone || this.updateInfo.address)
      ) {
        if (!this.updateInfo.bankName) {
          this.$message.warning('请填写开户银行')
          return false
        } else if (!this.updateInfo.account) {
          this.$message.warning('请填写开户账号')
          return false
        } else if (!this.updateInfo.rePhone) {
          this.$message.warning('请填写注册电话')
          return false
        } else if (!this.updateInfo.address) {
          this.$message.warning('请填写注册地址')
          return false
        } else if (this.updateInfo.address.indexOf('·') != -1) {
          this.$message.warning('注册地址暂不支持特殊字符“ · ”')
          return false
        }
      }
      if (this.updateInfo.titleType === TitleTypeEnum.UNIT && !titleReg.test(this.updateInfo.title)) {
        this.$message.warning('单位名称特殊符号仅支持《》、—、-、（）')
        return false
      }
      if (this.updateInfo.titleType === TitleTypeEnum.UNIT && !taxpayerNoReg.test(this.updateInfo.taxpayerNo)) {
        this.$message.warning('请输入正确的18位统一社会信用代码')
        return false
      }
      return true
    }

    //修改发票信息
    commitApply() {
      this.modifyInvoiceForm.validate((valid: boolean) => {
        if (valid && this.validUnitInfo()) {
          // if (this.dialogType == 0) {
          this.doModify()
          // }
          // else {
          //   this.reOpenInvoice(2)
          // }
        }
      })
    }
    // /**
    //  * 重新开票
    //  * @param type 1：蓝票 2：红票
    //  */
    // async reOpenInvoice(type: number) {
    //   const res = await TradeModule.singleTradeBatchFactor.invoiceFactor.mutationInvoice.retryInvoice({
    //     invoiceId: this.updateInfo.invoiceId,
    //     billType: type
    //   })
    //   console.log(res)
    //   if (res.status.isSuccess()) {
    //     this.$message.success('重新开票成功')
    //     this.dialogVisible = false
    //     this.$emit('callBack')
    //   } else {
    //     this.$message.error(res.status?.errors[0]?.message || '重新开票失败')
    //   }
    //   //重新开票
    // }
    async getInvoiceInfo() {
      this.updateInfo = new InvoiceDetail()
      try {
        this.invoiceInfo =
          (await TradeModule.singleTradeBatchFactor.invoiceFactor.queryInvoice.onLineGetInvoiceInServicer(
            this.invoiceId
          )) as InvoiceListResponse // 回显
        this.updateInfo = Object.assign(new InvoiceDetail(), this.invoiceInfo)
      } catch (e) {
        console.log(e)
      } finally {
        //都要执行的操作
      }
    }

    created() {
      this.dialogVisible = this.dialogCtrl
    }
  }
</script>
