<template>
  <el-main>
    <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb15">
      <div class="f-fl f-mr50">温馨提示：当前网校未开启web、h5访问。请完成网校配置后开启对外访问。</div>
      <div class="f-flex f-fl">
        <div class="f-mr50">
          <i class="f-vm f-c6">Web 访问</i>
          <el-switch v-model="switch1" class="f-ml10"></el-switch>
        </div>
        <div class="f-mr50">
          <i class="f-vm f-c6">H5 访问</i>
          <el-switch v-model="switch2" class="f-ml10"></el-switch>
        </div>
      </div>
    </el-alert>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="Web 端" name="first">
        <div class="f-p15">
          <el-tabs v-model="activeName2" type="card" class="m-tab-card">
            <div class="tab-right">
              <el-button type="primary" size="medium" class="f-mr15">
                <i class="hb-iconfont icon-complelearn f-mr5"></i>预览
              </el-button>
            </div>
            <el-tab-pane label="门户信息配置" name="first">
              <el-card shadow="never" class="m-card f-mb15">
                <div class="f-p10">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
                      <el-form ref="form" :model="form" label-width="auto" class="m-form">
                        <el-form-item label="平台名称：" required>
                          <el-input
                            v-model="form.name"
                            clearable
                            placeholder="请输入平台名称，学员中心和管理平台登录页面同步显示"
                          />
                        </el-form-item>
                        <el-form-item label="门户logo：" required>
                          <el-upload
                            action="#"
                            list-type="picture-card"
                            :auto-upload="false"
                            class="m-pic-upload long-pic"
                          >
                            <div slot="default" class="upload-placeholder">
                              <i class="el-icon-plus"></i>
                              <p class="txt">上传logo</p>
                            </div>
                            <div slot="file" slot-scope="{ file }" class="img-file">
                              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                              <div class="el-upload-list__item-actions">
                                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                  <i class="el-icon-zoom-in"></i>
                                </span>
                                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                                  <i class="el-icon-delete"></i>
                                </span>
                              </div>
                            </div>
                            <div slot="tip" class="el-upload__tip">
                              <i class="el-icon-warning"></i>
                              <span class="txt">
                                上传的平台名称及LOGO图片，适用于门户顶部。请先设计好后上传，尺寸：900px * 80px。
                                <i class="f-link" @click="dialog1 = true">查看示例图片</i>
                              </span>
                              <!--示例图片弹窗-->
                              <el-dialog :visible.sync="dialog1" width="1100px" class="m-dialog-pic">
                                <img src="./assets/images/demo-web-logo.png" alt="" />
                              </el-dialog>
                            </div>
                          </el-upload>
                          <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                            <img :src="dialogImageUrl" alt="" />
                          </el-dialog>
                        </el-form-item>
                        <el-form-item label="浏览器图标：" required>
                          <el-upload
                            action="#"
                            list-type="picture-card"
                            :auto-upload="false"
                            class="m-pic-upload small-pic"
                          >
                            <div slot="default" class="upload-placeholder">
                              <i class="el-icon-plus"></i>
                              <p class="txt">上传图片</p>
                            </div>
                            <div slot="file" slot-scope="{ file }" class="img-file">
                              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                              <div class="el-upload-list__item-actions">
                                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                  <i class="el-icon-zoom-in"></i>
                                </span>
                                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                                  <i class="el-icon-delete"></i>
                                </span>
                              </div>
                            </div>
                            <div slot="tip" class="el-upload__tip">
                              <i class="el-icon-warning"></i>
                              <span class="txt">
                                上传浏览器图标，适用于门户浏览器的图标（icon格式）。请先设计好后上传，尺寸：16px*16px。
                              </span>
                            </div>
                          </el-upload>
                          <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                            <img :src="dialogImageUrl" alt="" />
                          </el-dialog>
                        </el-form-item>
                        <el-form-item label="客服电话图片：" required>
                          <el-upload
                            action="#"
                            list-type="picture-card"
                            :auto-upload="false"
                            class="m-pic-upload long-pic"
                          >
                            <div slot="default" class="upload-placeholder">
                              <i class="el-icon-plus"></i>
                              <p class="txt">上传图片</p>
                            </div>
                            <div slot="file" slot-scope="{ file }" class="img-file">
                              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                              <div class="el-upload-list__item-actions">
                                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                  <i class="el-icon-zoom-in"></i>
                                </span>
                                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                                  <i class="el-icon-delete"></i>
                                </span>
                              </div>
                            </div>
                            <div slot="tip" class="el-upload__tip">
                              <i class="el-icon-warning"></i>
                              <span class="txt">
                                上传客服电话图片，请先设计好后再上传，尺寸：280px * 50px。
                                <i class="f-link" @click="dialog2 = true">查看示例图片</i>
                              </span>
                              <!--示例图片弹窗-->
                              <el-dialog :visible.sync="dialog2" width="1100px" class="m-dialog-pic">
                                <img src="./assets/images/demo-tel-head.png" alt="" />
                              </el-dialog>
                            </div>
                          </el-upload>
                          <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                            <img :src="dialogImageUrl" alt="" />
                          </el-dialog>
                        </el-form-item>
                        <el-form-item label="客服电话：">
                          <el-input
                            v-model="form.name"
                            clearable
                            placeholder="请输入客服电话，同步展示在web端和h5"
                            class="form-l"
                          />
                          <div class="el-upload__tip">
                            <i class="el-icon-warning"></i>
                            <span class="txt">
                              输入专题门户右侧息停区域显示的客服电话，如需展示多个客服电话，请用顿号“、”分隔。例如96882301、96882302
                            </span>
                          </div>
                        </el-form-item>
                        <el-form-item label="咨询时间：">
                          <el-input v-model="form.name1" clearable class="form-l" />
                        </el-form-item>
                        <el-form-item label="企业微信客服：">
                          <el-upload action="#" list-type="picture-card" :auto-upload="false" class="m-pic-upload">
                            <div slot="default" class="upload-placeholder">
                              <i class="el-icon-plus"></i>
                              <p class="txt">上传企业微信客服图片</p>
                            </div>
                            <div slot="file" slot-scope="{ file }" class="img-file">
                              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                              <div class="el-upload-list__item-actions">
                                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                  <i class="el-icon-zoom-in"></i>
                                </span>
                                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                                  <i class="el-icon-delete"></i>
                                </span>
                              </div>
                            </div>
                            <div slot="tip" class="el-upload__tip">
                              <i class="el-icon-warning"></i>
                              <span class="txt">上传企业微信客服图片，尺寸不小于：160px * 160px。</span>
                            </div>
                          </el-upload>
                          <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                            <img :src="dialogImageUrl" alt="" />
                          </el-dialog>
                        </el-form-item>
                        <el-form-item label="移动学习二维码：">
                          <el-radio v-model="radio1" label="1" border class="f-mr10">使用系统生成二维码</el-radio>
                          <el-radio v-model="radio1" label="3" border class="f-mr10">系统默认</el-radio>
                          <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                            <div slot="content">
                              <p>网校门户移动端推广二维码展示配置说明：</p>
                              <p>
                                若需要使用系统生成的平台H5页面访问网校，请选择“系统默认”类型；若需要使用微信公众号访问网校，请选择“自定义”类型。
                              </p>
                            </div>
                          </el-tooltip>
                        </el-form-item>
                        <el-form-item label="培训流程：">
                          <el-upload
                            action="#"
                            list-type="picture-card"
                            :auto-upload="false"
                            class="m-pic-upload long-pic"
                          >
                            <div slot="default" class="upload-placeholder">
                              <i class="el-icon-plus"></i>
                              <p class="txt">上传图片</p>
                            </div>
                            <div slot="file" slot-scope="{ file }" class="img-file">
                              <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                              <div class="el-upload-list__item-actions">
                                <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                  <i class="el-icon-zoom-in"></i>
                                </span>
                                <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                                  <i class="el-icon-delete"></i>
                                </span>
                              </div>
                            </div>
                            <div slot="tip" class="el-upload__tip">
                              <i class="el-icon-warning"></i>
                              <span class="txt">
                                上传培训流程图片，尺寸：1200px * 120px。
                                <i class="f-link" @click="dialog3 = true">查看示例图片</i>
                              </span>
                              <!--示例图片弹窗-->
                              <el-dialog :visible.sync="dialog3" width="1100px" class="m-dialog-pic">
                                <img src="./assets/images/demo-pic-process.jpg" alt="" />
                              </el-dialog>
                            </div>
                          </el-upload>
                          <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                            <img :src="dialogImageUrl" alt="" />
                          </el-dialog>
                        </el-form-item>
                        <el-form-item label="在线客服代码：">
                          <el-input type="textarea" :rows="6" v-model="form.desc" />
                        </el-form-item>
                        <el-form-item label="底部落款：">
                          <div class="rich-text">
                            <el-input type="textarea" :rows="6" v-model="form.desc" />
                          </div>
                        </el-form-item>
                        <el-form-item label="友情链接显示方式：">
                          <el-radio-group v-model="form.resource">
                            <el-radio label="文本"></el-radio>
                            <el-radio label="图片"></el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item label="友情链接：">
                          <div class="item f-mb10">
                            <el-input
                              v-model="form.name"
                              clearable
                              placeholder="请输入友情链接文字信息，最多15字"
                              class="form-m"
                            />
                            <span class="f-pl20">链接地址：</span>
                            <el-input v-model="form.name" clearable placeholder="请输入链接地址" class="form-s" />
                            <el-button type="danger" class="f-ml10" plain>删除</el-button>
                          </div>
                          <el-button type="primary" icon="el-icon-plus" plain>添加</el-button>
                        </el-form-item>
                        <el-form-item label="友情链接：">
                          <div class="m-upload-item">
                            <el-upload
                              action="#"
                              list-type="picture-card"
                              :auto-upload="false"
                              class="m-pic-upload long-pic"
                            >
                              <div slot="default" class="upload-placeholder">
                                <i class="el-icon-plus"></i>
                                <p class="txt">添加链接图片</p>
                              </div>
                              <div slot="file" slot-scope="{ file }" class="img-file">
                                <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                                <div class="el-upload-list__item-actions">
                                  <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                    <i class="el-icon-zoom-in"></i>
                                  </span>
                                  <span
                                    v-if="!disabled"
                                    class="el-upload-list__item-delete"
                                    @click="handleRemove(file)"
                                  >
                                    <i class="el-icon-delete"></i>
                                  </span>
                                </div>
                              </div>
                            </el-upload>
                            <div class="other">
                              <p>链接地址</p>
                              <div class="f-flex">
                                <el-input
                                  v-model="form.name"
                                  clearable
                                  class="f-flex-sub"
                                  placeholder="请输入链接地址"
                                />
                                <el-button type="danger" class="f-ml10" plain>删除</el-button>
                              </div>
                            </div>
                          </div>
                          <el-button type="primary" icon="el-icon-plus" class="f-mt10" plain>添加</el-button>
                          <div class="el-upload__tip">
                            <i class="el-icon-warning"></i>
                            <span class="txt">
                              底部链接图片，尺寸：400px * 90px。
                            </span>
                          </div>
                          <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                            <img :src="dialogImageUrl" alt="" />
                          </el-dialog>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
              <div class="m-btn-bar f-tc is-sticky-1">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </div>
            </el-tab-pane>
            <el-tab-pane label="栏目设置" name="second">
              <el-card shadow="never" class="m-card f-mb15">详见 0202_基础信息配置_栏目设置.vue</el-card>
            </el-tab-pane>
            <el-tab-pane label="轮播图设置" name="third">
              <el-card shadow="never" class="m-card f-mb15">详见 0203_基础信息配置_轮播图设置.vue</el-card>
            </el-tab-pane>
            <el-tab-pane label="风格设置" name="fourth">
              <el-card shadow="never" class="m-card f-mb15">详见 0205_基础信息配置_风格设置.vue</el-card>
            </el-tab-pane>
            <el-tab-pane label="网校SEO配置" name="fifth">
              <el-card shadow="never" class="m-card f-mb15">
                详见 0209_基础信息配置_网校SEO配置.vue
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      <el-tab-pane label="移动端" name="second">
        <div class="f-p15">详见 0207_基础信息配置_移动学习.vue</div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        radio1: '1',
        switch1: false,
        switch2: false,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          name1: '周一至周五 (09:00 - 12:00 14:00 - 17:30)',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
