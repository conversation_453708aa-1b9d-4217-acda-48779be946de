<route-params content="/:schemeId"></route-params>
<route-meta>
{
"isMenu": true,
"title": "新建培训方案",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  import SchemeCreate from '@hbfe/jxjy-admin-scheme/src/diff/xmlg/create.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    create: [WXGLY]
  })
  export default class extends SchemeCreate {}
</script>
