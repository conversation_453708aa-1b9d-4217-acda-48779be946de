/**
 * @description 查询换班记录列表参数
 */
import { ExchangeTrainClassStatusEnum } from '@api/service/management/train-class/query/enum/ExchangeTrainClassStatusType'
import {
  CommoditySkuRequest1,
  ExchangeOrderRequest,
  IssueInfo1,
  SkuPropertyRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

class QueryExchangeOrderRecordListVo {
  /**
   * 年度
   */
  year = ''

  /**
   * 班级名称
   */
  schemeName = ''

  /**
   * 期别名称
   */
  issueName = ''

  /**
   * 换班状态
   * 1：全部
   * 2：换班中
   * 3：换班成功
   */
  exchangeTrainClassStatus: ExchangeTrainClassStatusEnum = null

  /**
   * 买家id
   */
  buyerIdList: string[] = []

  /**
   * 培训形式
   */
  trainingMode: TrainingModeEnum = null

  /**
   * 转换查询vo为远端模型
   */
  to(): ExchangeOrderRequest {
    const to: ExchangeOrderRequest = new ExchangeOrderRequest()
    to.buyerIdList = QueryExchangeOrderRecordListVo.setArrValue(this.buyerIdList)
    to.commodity = new CommoditySkuRequest1()
    to.commodity.saleTitle = this.schemeName ? this.schemeName : undefined
    to.commodity.skuProperty = new SkuPropertyRequest()
    to.commodity.skuProperty.year = this.year ? [this.year] : undefined
    to.commodity.skuProperty.trainingForm = this.trainingMode ? [this.trainingMode] : undefined
    to.commodity.issueInfo = new IssueInfo1()
    to.commodity.issueInfo.issueName = this.issueName ? this.issueName : undefined
    to.statusList = QueryExchangeOrderRecordListVo.setStatus(this.exchangeTrainClassStatus)
    return to
  }

  /**
   * 设置数组值
   */
  static setArrValue<T>(arr: T[]): T[] | undefined {
    return Array.isArray(arr) && arr.length ? arr : undefined
  }

  /**
   * 设置（换货单）状态
   */
  static setStatus(status: ExchangeTrainClassStatusEnum | null): number[] | undefined {
    // 全部
    if (status === ExchangeTrainClassStatusEnum.ALL) {
      return undefined
    }
    // 换班中
    if (status === ExchangeTrainClassStatusEnum.Exchanging) {
      return [0, 2, 3, 4, 5]
    }
    // 换班成功
    if (status === ExchangeTrainClassStatusEnum.Complete_Exchange) {
      return [6, 7]
    }
    return undefined
  }
}

export default QueryExchangeOrderRecordListVo
