<route-meta>
{
"isMenu": true,
"title": "集体报名发票",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import SpecialInvoice from '@hbfe/jxjy-admin-trade/src/diff/qztg/invoice/collective/components/special-invoice.vue'
  import ElectronicSpecialInvoice from '@hbfe/jxjy-admin-trade/src/diff/qztg/invoice/collective/components/electronic-special-invoice.vue'
  import Invoice from '@hbfe/jxjy-admin-trade/src/invoice/collective/index.vue'

  @Component({
    components: {
      SpecialInvoice,
      ElectronicSpecialInvoice
    }
  })
  export default class extends Invoice {}
</script>
