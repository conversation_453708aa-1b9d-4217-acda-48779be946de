<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item v-if="!otherRouter" :to="{ path: '/training/scheme/implementingManagement/' + schemeId }">
        实施管理
      </el-breadcrumb-item>
      <el-breadcrumb-item v-else :to="{ path: '/training/customer-service/personal' }">
        业务咨询
      </el-breadcrumb-item>
      <el-breadcrumb-item>查看统计报告</el-breadcrumb-item>
    </el-breadcrumb>
    <template
      v-if="$hasPermission('holisticReport')"
      desc="查看统计报告"
      actions="created,@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/check-question.vue,@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/check-report.vue"
    >
      <div class="f-p15">
        <el-tabs v-model="activeName1" type="card" class="m-tab-card">
          <el-tab-pane label="整体报告" name="first">
            <check-report :questionId="questionId"></check-report>
          </el-tab-pane>
          <el-tab-pane label="问卷答题记录" name="second">
            <check-question :questionId="questionId"></check-question>
          </el-tab-pane>
        </el-tabs>
      </div>
    </template>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import CheckQuestion from './__components__/check-question.vue'
  import CheckReport from './__components__/check-report.vue'

  @Component({
    components: {
      CheckQuestion,
      CheckReport
    }
  })
  export default class extends Vue {
    activeName1 = 'first'
    questionId = ''
    schemeId = ''
    otherRouter = false

    created() {
      this.schemeId = this.$route.query?.schemeId as string
      this.questionId = this.$route.params?.id as string
      this.otherRouter = (this.$route.query?.otherRouter as string) === 'true'
    }
  }
</script>
