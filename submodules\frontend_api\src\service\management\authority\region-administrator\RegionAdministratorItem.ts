import {
  AdminInfoResponse,
  ContractProviderAdminInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import Administrator from '@api/platform-gateway/platform-administrator-v1'

export default class RegionAdministratorItem {
  /**
   * 管理员ID
   */
  userId: string = undefined
  /**
   * 地区管理员账号ID
   */
  accountId: string = undefined
  /**
   * 地区管理员账号
   */
  account: string = undefined
  /**
   * 地区管理员名称
   */
  name: string = undefined
  /**
   * 手机号
   */
  phone: string = undefined
  /**
   * 管辖地区
   */
  region: Array<string> = undefined
  /**
   * 管辖地区
   */
  regionName: string = undefined
  /**
   * 启停用状态 | 1.启用 | 2.禁用
   */
  status: number = undefined

  static from(dto: AdminInfoResponse | ContractProviderAdminInfoResponse) {
    const vo = new RegionAdministratorItem()
    vo.userId = dto?.userInfo?.userId
    vo.accountId = dto?.accountInfo?.accountId
    vo.name = dto?.userInfo?.userName
    vo.account =
      dto?.authenticationList && dto?.authenticationList.length
        ? dto?.authenticationList.find(item => item.identityType === 1).identity || dto.authenticationList[0].identity
        : '-'
    vo.phone = dto?.userInfo?.phone
    if (dto?.userInfo?.manageRegionList && dto?.userInfo?.manageRegionList.length) {
      const regionInfo = dto?.userInfo?.manageRegionList[0]
      vo.region = regionInfo.regionPath.slice(1).split('/')
      vo.regionName =
        regionInfo.provinceName +
        (regionInfo.cityName ? '-' + regionInfo.cityName : '') +
        (regionInfo.countyName ? '-' + regionInfo.countyName : '')
    }
    vo.status = dto?.accountInfo?.status
    return vo
  }
  /**
   * 添加
   */
  async addRegionAdministrator() {
    const res = await Administrator.addRegionAdministrator({
      name: this.name.trim(),
      account: this.account.trim(),
      area: this.region && this.region.length ? '/' + this.region?.join('/') : undefined,
      phone: this.phone ? this.phone.trim() : undefined
    })
    return res
  }
  /**
   * 修改
   */
  async updateRegionAdministrator(item: RegionAdministratorItem) {
    const res = await Administrator.updateRegionAdministrator({
      name: this.name.trim(),
      accountId: this.accountId,
      theirArea: this.region && this.region.length ? '/' + this.region?.join('/') : undefined,
      phone: this.phone ? this.phone.trim() : this.phone
    })
    return res
  }
}
