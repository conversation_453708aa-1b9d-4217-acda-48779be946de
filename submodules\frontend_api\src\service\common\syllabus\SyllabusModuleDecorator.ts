import ChapterTree from './model/ChapterTree'
import { Action, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import PlateformKnowledgePreexamGateway, { ChapterDTO, IndustryDTO } from '@api/gateway/PlatformKnowledge'
import { ResponseStatus } from '../../../Response'
import store from '../../../store'
import Node from './model/Node'
import SyllabusPath from './model/SyllabusPath'
import { Role, RoleType, Secure } from '../../../Secure'
import Industry from '@api/service/common/models/syllabus/Industry'
import Chapter from '@api/service/common/models/syllabus/Chapter'
import Major from '@api/service/common/models/syllabus/Major'

/**
 * 考纲数据状态
 */
export interface SyllabusState {
  /**
   * 行业列表
   */
  industryList: Array<Industry>
  /**
   * 所有考纲列表
   */
  syllabusList: Array<Chapter>
  /**
   * 行业数据是否加载
   */
  isIndustryLoad: boolean
  /**
   * 考纲数据是否加载
   */
  isSyllabusLoad: boolean
}

const internalFunction = {
  // region private methods

  /**
   * 递归查询父级章节
   * @param chapterList 章节列表
   * @param path 路径
   * @param parentRelationId 父级关系编号
   */
  searchParent(chapterList: Array<Chapter>, path: Array<Node>, parentRelationId: string): string {
    const list = chapterList.filter(x => x.relationId === parentRelationId)
    if (list && list.length > 0) {
      const chapter: Chapter = list[0]
      const node = new Node()
      node.id = chapter.id
      node.relationId = chapter.relationId
      node.parentRelationId = chapter.parentRelationId
      node.name = chapter.name
      path.push(node)
      return this.searchParent(chapterList, path, chapter.parentRelationId)
    }
    return parentRelationId
  },

  /**
   * 递归按关系编号查询章节子集
   * @param chapterList 章节列表
   * @param parentRelationId 父级关系编号
   * @param currentLevel 当前层级
   * @param level 遍历层级
   */
  searchChild(
    chapterList: Array<Chapter>,
    parentRelationId: string,
    currentLevel: number,
    level: number
  ): Array<ChapterTree> {
    const chapterNodeList: Array<ChapterTree> = []
    if (level !== 0 && currentLevel > level) {
      return chapterNodeList
    } else {
      currentLevel++
    }

    for (const chapter of chapterList) {
      if (chapter.parentRelationId === parentRelationId) {
        const child = this.searchChild(chapterList, chapter.relationId, currentLevel, level)
        const chapterTree: ChapterTree = new ChapterTree()
        chapterTree.parentRelationId = chapter.parentRelationId
        chapterTree.id = chapter.id
        chapterTree.name = chapter.name
        chapterTree.relationId = chapter.relationId
        chapterTree.sequence = chapter.sequence
        chapterTree.children = child
        chapterNodeList.push(chapterTree)
      }
    }
    return chapterNodeList
  },

  /**
   * 递归获取所有的叶子节点
   * @param chapterList 章节列表
   * @param parentRelationId 父级关系编号
   */
  searchLeaf(chapterList: Array<Chapter>, parentRelationId: string): Array<Chapter> {
    const chapterNodeList: Array<Chapter> = []
    for (const chapter of chapterList) {
      if (chapter.parentRelationId === parentRelationId) {
        const childList = chapterList.filter(p => p.parentRelationId === chapter.relationId)
        if (childList.length === 0) {
          chapterNodeList.push(chapter)
        } else {
          chapterNodeList.push(...this.searchLeaf(chapterList, chapter.relationId))
        }
      }
    }
    return chapterNodeList
  }

  // endregion
}

/**
 * 定义公共的考纲模块
 */
@Module({ namespaced: true, dynamic: true, name: 'SyllabusModuleDecorator', store })
class SyllabusModuleDecorator extends VuexModule implements SyllabusState {
  //region implements

  /**
   * 行业列表
   */
  public industryList: Array<Industry> = new Array<Industry>()
  /**
   * 所有考纲列表
   */
  public syllabusList: Array<Chapter> = new Array<Chapter>()
  /**
   * 行业数据是否加载
   */
  public isIndustryLoad = false
  /**
   * 考纲数据是否加载
   */
  public isSyllabusLoad = false

  //endregion

  // region Mutations

  /**
   * 设置状态对象中的行业列表
   * @param industryDTOList 原始行业列表
   */
  @Mutation
  private SET_INDUSTRY_LIST(industryDTOList: Array<any>) {
    // 定义一个行业列表用于存储从远程读取的数据，使用该列表全部覆盖状态数据（缓存）
    const currentIndustryList: Array<Industry> = new Array<Industry>()
    if (industryDTOList && industryDTOList?.length > 0) {
      for (const industryDTO of industryDTOList) {
        const industry: Industry = new Industry()
        industry.id = industryDTO.id
        industry.name = industryDTO.name
        industry.sequence = industryDTO.sort
        industry.relationId = industryDTO.relationId
        const majorList: Array<Major> = new Array<Major>()
        for (const majorDTO of industryDTO.majorModelList) {
          const item: Major = new Major()
          item.id = majorDTO.id
          item.name = majorDTO.name
          item.sequence = majorDTO.sort
          item.relationId = majorDTO.relationId
          majorList.push(item)
        }
        industry.majorList = majorList
        currentIndustryList.push(industry)
      }
    }
    this.industryList = currentIndustryList
    this.isIndustryLoad = true
  }

  /**
   * 设置状态对象中考纲列表
   * @param chapterDTOList 原始章节列表
   */
  @Mutation
  private SET_SYLLABUS_LIST(chapterDTOList: Array<any>) {
    const chapterList: Array<Chapter> = new Array<Chapter>()
    for (const chapterDTO of chapterDTOList) {
      const chapter: Chapter = new Chapter()
      chapter.id = chapterDTO.id
      chapter.name = chapterDTO.name
      chapter.relationId = chapterDTO.relationId
      chapter.parentRelationId = chapterDTO.parentRelationId
      chapter.sequence = chapterDTO.sort
      chapter.code = chapterDTO.code
      chapterList.push(chapter)
    }
    this.syllabusList = chapterList
    this.isSyllabusLoad = true
  }

  /**
   * 变更考纲数据加载状态
   * @param isLoad 是否加载完成
   */
  @Mutation
  private CHANGE_SYLLABUS_LOAD_STATE(isLoad: boolean) {
    this.isSyllabusLoad = isLoad
  }

  // endregion

  // region Actions

  /**
   * 指示从后端应用获取数据填充到State中
   */
  @Action
  @Role([RoleType.user])
  public async init(): Promise<ResponseStatus> {
    if (!this.isIndustryLoad) {
      await this.initIndustry()
    }
    if (!this.isSyllabusLoad) {
      const industryList: Array<Industry> = this.industryList
      const allMajorRelationIdList: Array<string> = new Array<string>()
      for (const industry of industryList) {
        allMajorRelationIdList.push(...industry.majorList.map(y => y.relationId))
      }

      if (allMajorRelationIdList?.length) {
        const majorMapChapterListResponse = await PlateformKnowledgePreexamGateway.findAllChildChapterByMajorList(
          allMajorRelationIdList
        )
        if (majorMapChapterListResponse.status.isSuccess()) {
          const chapterList: Array<ChapterDTO> = new Array<ChapterDTO>()
          for (const item of majorMapChapterListResponse.data) {
            chapterList.push(...item.chapterList)
          }
          this.SET_SYLLABUS_LIST(chapterList)
        } else {
          return Promise.reject(majorMapChapterListResponse.status)
        }
      }
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 指示初始化行业及专业信息
   */
  @Action
  @Role([RoleType.user])
  public async initIndustry(): Promise<ResponseStatus> {
    if (!this.isIndustryLoad) {
      const response = await PlateformKnowledgePreexamGateway.findAllIndustryRelationList()
      if (response.status.isSuccess()) {
        const industryDTOList: Array<IndustryDTO> = response.data
        this.SET_INDUSTRY_LIST(industryDTOList)
      } else {
        return Promise.reject(response.status)
      }
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 重新加载考纲
   */
  @Action
  public async reloadSyllabus() {
    // 刷新章节列表状态
    this.CHANGE_SYLLABUS_LOAD_STATE(false)
    await this.init()
  }

  // endregion

  // region Getters

  /**
   * 指示获取行业信息
   * @returns function 参数：industryId - 行业编号
   */
  get getIndustryInfo() {
    return (industryId: string): Industry | undefined => {
      return this.industryList.find(x => x.id === industryId)
    }
  }

  /**
   * 指示获取专业信息
   * @returns function 参数：majorId - 专业编号
   */
  get getMajorInfo() {
    return (majorId: string): Major | undefined => {
      const majorList: Array<Major> = new Array<Major>()
      for (const industry of this.industryList) {
        majorList.push(...industry.majorList)
      }
      return majorList.find(x => x.id === majorId)
    }
  }

  /**
   * 指示获取指定行业下的专业列表
   * @returns function 参数：industryRelationId - 行业关系编号
   */
  get getMajorListByIndustry() {
    return (industryRelationId: string): Array<Major> => {
      const currentList: Array<Industry> = this.industryList.filter(v => v.relationId === industryRelationId)
      if (currentList.length > 0) {
        return currentList[0].majorList
      }
      return []
    }
  }

  /**
   * 依据专业编号获取指定的考纲树
   * @returns function 参数：majorId  - 专业编号;level - 查询层级，0表示全部层级
   */
  get getSyllabusTreeByMajorId() {
    return (majorId: string, level: number): Array<ChapterTree> => {
      const allList: Array<ChapterTree> = new Array<ChapterTree>()
      for (const industry of this.industryList) {
        if (industry?.majorList.length > 0) {
          for (const major of industry.majorList.filter(x => x.id === majorId)) {
            const list: Array<ChapterTree> = internalFunction.searchChild(this.syllabusList, major.relationId, 1, level)
            allList.push(...list)
          }
        }
      }
      return allList
    }
  }

  /**
   * 依据专业编号获取指定的考纲树
   * @returns function 参数：majorId  - 专业编号;level - 查询层级，0表示全部层级
   */
  get getSyllabusTreeByMajorRelationId() {
    return (majorRelationId: string, level: number): Array<ChapterTree> => {
      const allList: Array<ChapterTree> = new Array<ChapterTree>()
      for (const industry of this.industryList) {
        if (industry?.majorList.length > 0) {
          for (const major of industry.majorList.filter(x => x.relationId === majorRelationId)) {
            const list: Array<ChapterTree> = internalFunction.searchChild(this.syllabusList, major.relationId, 1, level)
            allList.push(...list)
          }
        }
      }
      return allList
    }
  }

  /**
   * 查询指定章节关系下节点及其子节点
   */
  get getSyllabusTreeByChapterRelationId() {
    return (chapterRelationId: string, level: number): Array<ChapterTree> => {
      return internalFunction.searchChild(this.syllabusList, chapterRelationId, 1, level)
    }
  }

  /**
   * 查询指定章节关系下所有末子节点
   */
  get getLeafSyllabusByChapterRelationId() {
    return (chapterRelationId: string): Array<Chapter> => {
      return internalFunction.searchLeaf(this.syllabusList, chapterRelationId)
    }
  }

  /**
   * 依据专业编号及考纲id获取所有叶子考纲集合
   * @returns function 参数：majorId - 专业编号
   */
  get getLeafSyllabusByMajorId() {
    return (majorId: string): Array<Chapter> => {
      const allList: Array<Chapter> = new Array<Chapter>()
      for (const industry of this.industryList) {
        if (industry?.majorList.length > 0) {
          for (const major of industry.majorList.filter(x => x.id === majorId)) {
            allList.push(...internalFunction.searchLeaf(this.syllabusList, major.relationId))
          }
        }
      }
      return allList
    }
  }

  /**
   * 指示查询指定末节点名称的考纲结构及其所在的结构路径
   *
   * @returns function 参数： chapterName - 章节名称;isExact - 搜索是否精确，即是否等于检索，true/false;返回：[{id,name,relation,parentRelationId,sequence,path:[]}]
   */
  get getSyllabusRelationByLastNodeName() {
    return (chapterName?: string, isExact?: boolean): Array<SyllabusPath> => {
      const chapterList: Array<Chapter> = this.syllabusList
      const lastNodeList = new Array<Chapter>()
      if (chapterName) {
        const reg = new RegExp(chapterName || '', 'i')
        // 查询所有末节点
        lastNodeList.push(
          ...chapterList.filter(x => {
            return (
              (isExact ? x.name === chapterName : x.name.match(reg)) &&
              chapterList.filter(y => y.parentRelationId === x.relationId).length > 0
            )
          })
        )
      } else {
        this.industryList.forEach(x => {
          for (const major of x.majorList) {
            const list = internalFunction.searchLeaf(chapterList, major.relationId)
            lastNodeList.push(...list)
          }
        })
      }
      const list = new Array<SyllabusPath>()
      for (const chapter of lastNodeList) {
        const path: Array<Node> = new Array<Node>()
        const rootRelationId = internalFunction.searchParent(chapterList, path, chapter.parentRelationId)
        // 查询行业和专业
        const currentList: Array<Industry> = this.industryList
        let append = false
        for (const industry of currentList) {
          const majorList: Array<Major> = (industry.majorList as Array<Major>).filter(
            y => y.relationId === rootRelationId
          )
          if (majorList && majorList.length > 0) {
            append = true
            let node = new Node()
            node.id = majorList[0].id
            node.name = majorList[0].name
            node.parentRelationId = industry.relationId
            node.relationId = majorList[0].relationId
            path.push(node)
            node = new Node()
            node.id = industry.id
            node.name = industry.name
            node.parentRelationId = '-1'
            node.relationId = industry.relationId
            path.push(node)
          }
        }

        if (append) {
          const item = {
            id: chapter.id,
            name: chapter.name,
            relationId: chapter.relationId,
            sequence: chapter.sequence,
            parentRelationId: chapter.parentRelationId,
            code: chapter.code,
            path: path.map(x => x.name).reverse(),
            pathNode: path.reverse()
          }
          list.push(item)
        }
      }
      return list
    }
  }

  /**
   * 指示查询指定章节编号节点及其所在的结构路径
   * @returns function 参数：chapterId - 章节编号；返回：[{id,name,relation,parentRelationId,sequence,path:[]}]
   */
  get getSyllabusParentRelationById() {
    return (chapterId: string): Array<SyllabusPath> => {
      const chapterList: Array<Chapter> = this.syllabusList
      // 查询指定章节的所有节点
      const lastNodeList = chapterList.filter(x => x.id === chapterId)
      const constructList = new Array<SyllabusPath>()
      for (const chapter of lastNodeList) {
        const path: Array<Node> = new Array<Node>()
        const rootRelationId = internalFunction.searchParent(chapterList, path, chapter.parentRelationId)
        // 查询行业和专业
        const currentList: Array<Industry> = this.industryList
        let append = false
        for (const industry of currentList) {
          const majorList: Array<Major> = (industry.majorList as Array<Major>).filter(
            y => y.relationId === rootRelationId
          )
          if (majorList && majorList.length > 0) {
            append = true
            let node = new Node()
            node.id = majorList[0].id
            node.name = majorList[0].name
            node.parentRelationId = industry.relationId
            node.relationId = majorList[0].relationId
            path.push(node)
            node = new Node()
            node.id = industry.id
            node.name = industry.name
            node.parentRelationId = '-1'
            node.relationId = industry.relationId
            path.push(node)
          }
        }

        if (append) {
          const item = {
            id: chapter.id,
            name: chapter.name,
            relationId: chapter.relationId,
            sequence: chapter.sequence,
            parentRelationId: chapter.parentRelationId,
            path: path.map(x => x.name).reverse(),
            pathNode: path.reverse()
          }
          constructList.push(item)
        }
      }
      return constructList
    }
  }

  /**
   * 指示查询指定章节关系编号节点及其所在的结构路径
   * @returns function 参数：chapterRelationId - 章节编号；返回：[{id,name,relation,parentRelationId,sequence,path:[]}]
   */
  get getSyllabusParentRelationByRelationId() {
    return (chapterRelationId: string): SyllabusPath | undefined => {
      const chapterList: Array<Chapter> = this.syllabusList
      // 查询指定章节的所有节点
      const lastNode = chapterList.find(x => x.relationId === chapterRelationId)
      if (lastNode) {
        const path: Array<Node> = new Array<Node>()
        const rootRelationId = internalFunction.searchParent(chapterList, path, lastNode.parentRelationId)
        // 查询行业和专业
        const currentList: Array<Industry> = this.industryList
        let append = false
        for (const industry of currentList) {
          const majorList: Array<Major> = (industry.majorList as Array<Major>).filter(
            y => y.relationId === rootRelationId
          )
          if (majorList && majorList.length > 0) {
            append = true
            let node = new Node()
            node.id = majorList[0].id
            node.name = majorList[0].name
            node.parentRelationId = industry.relationId
            node.relationId = majorList[0].relationId
            path.push(node)
            node = new Node()
            node.id = industry.id
            node.name = industry.name
            node.parentRelationId = '-1'
            node.relationId = industry.relationId
            path.push(node)
          }
        }
        if (append) {
          return {
            id: lastNode.id,
            name: lastNode.name,
            relationId: lastNode.relationId,
            sequence: lastNode.sequence,
            parentRelationId: lastNode.parentRelationId,
            path: path.map(x => x.name).reverse(),
            pathNode: path.reverse()
          }
        }
      }
      return undefined
    }
  }

  /**
   * 获取系统下所有行业专业下考纲树
   */
  get getAllSyllabusTree(): Array<ChapterTree> {
    const syllabusTree: Array<ChapterTree> = new Array<ChapterTree>()
    this.industryList.forEach(x => {
      for (const major of x.majorList) {
        const currentTree = this.getSyllabusTreeByMajorRelationId(major.relationId, 0)
        syllabusTree.push(...currentTree)
      }
    })
    return syllabusTree
  }

  /**
   * 指示查询指定章节编号节点及其所在的结构路径
   * @return function 参数：chapterId - 章节编号; 返回：Chapter
   */
  get getSyllabusById() {
    return (chapterId: string) => {
      const chapterList: Array<Chapter> = this.syllabusList
      return chapterList.find(p => p.id === chapterId) || new Chapter()
    }
  }

  /**
   * 依据考纲id获取所有叶子考纲集合
   * @return function 参数：majorId - 专业编号
   */
  get getLeafSyllabusByChapterId() {
    return (chapterId: string): Array<Chapter> => {
      const chapter: Chapter = this.getSyllabusById(chapterId)
      const allList: Array<Chapter> = new Array<Chapter>()
      allList.push(...internalFunction.searchLeaf(this.syllabusList, chapter.relationId))
      if (allList.length === 0) {
        allList.push(chapter)
      }
      return allList
    }
  }

  // endregion
}

export default SyllabusModuleDecorator
