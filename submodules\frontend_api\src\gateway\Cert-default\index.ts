import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/Cert-default'

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class CertificateTemplateResponse {
  /**
   * 主键 证书模板ID
   */
  id: string
  /**
   * 模板名称
   */
  name: string
  /**
   * 模板路径
   */
  temPath: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人ID
   */
  creatorId: string
  /**
   * 备注
   */
  remark: string
  /**
   * 使用范围
   */
  useableRange: string
  /**
   * 适用形式
   */
  applicableType: string
}

export class CertificateTemplateResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CertificateTemplateResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param page 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCertificateTemplatePage(
    page: Page,
    query: DocumentNode = GraphqlImporter.getCertificateTemplatePage,
    operation?: string
  ): Promise<Response<CertificateTemplateResponsePage>> {
    return commonRequestApi<CertificateTemplateResponsePage>(SERVER_URL, {
      query: query,
      variables: { page },
      operation: operation
    })
  }
}

export default new DataGateway()
