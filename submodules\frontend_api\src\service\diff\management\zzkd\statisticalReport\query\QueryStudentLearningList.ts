import { StudentSchemeLearningRequestVo } from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
import QueryStudentLearningList from '@api/service/management/statisticalReport/query/QueryStudentLearningList'
import { Page, Response } from '@hbfe/common'
import ZzkdSchemeLearningMsGateway, {
  ZZKDStudentSchemeLearningResponse
} from '@api/diff-gateway/platform-jxjypxtypt-zzkd-school'
import { StudentLearningStaticsVoDiff } from '@api/service/diff/management/zzkd/statisticalReport/vo/StudentLearningStaticsVo'
import { StudentLearningStaticsVo } from '@api/service/management/statisticalReport/query/vo/StudentLearningStaticsVo'
import { StudentSchemeLearningResponsePage } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import exportMsgateway from '@api/platform-gateway/diff-ms-data-export-front-gateway-DataExportBackstage'

class QueryStudentLearningListDiff extends QueryStudentLearningList {
  /**
   * 列表
   */
  studentSchemeLearningList = new Array<ZZKDStudentSchemeLearningResponse>()
  /**
   * 查询列表——分销商
   */
  async pageStudentSchemeLearningInDistributorDiff(page: Page, filter: StudentSchemeLearningRequestVo, vendorId = '') {
    try {
      const data = await this.pageStudentSchemeLearningInDistributor(page, filter)
      return this.enrichWithCertificateNumber(data)
    } catch (e) {
      console.log(
        '报错了，所处位置/service/diff/management/zzkd/statisticalReport/query/QueryStudentLearningList.ts所处方法，listRegionLearningReportFormsInServicer',
        e
      )
    }
  }
  /**
   * 学员学习统计列表--专题管理员
   */
  async listRegionLearningReportFormsInTrainingChannelDiff(
    page: Page,
    filter: StudentSchemeLearningRequestVo,
    vendorId = ''
  ): Promise<Array<StudentLearningStaticsVoDiff>> {
    try {
      const data = await this.listRegionLearningReportFormsInTrainingChannel(page, filter)
      return this.enrichWithCertificateNumber(data)
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，listRegionLearningReportFormsInTrainingChannelDiff',
        e
      )
    }
  }

  /**
   * 查询学员学习统计列表
   */
  async listRegionLearningReportFormsInServicerDiff(
    page: Page,
    filter: StudentSchemeLearningRequestVo,
    vendorId = ''
  ): Promise<Array<StudentLearningStaticsVoDiff>> {
    try {
      const data = await this.listRegionLearningReportFormsInServicer(page, filter)
      return this.enrichWithCertificateNumber(data)
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryStudentLearningList.ts所处方法，listRegionLearningReportFormsInServicerDiff',
        e
      )
    }
  }

  /**
   * 超管网授
   */
  async pageStudentSchemeLearningInServicerV2(page: Page, filter: StudentSchemeLearningRequestVo) {
    const data = await ZzkdSchemeLearningMsGateway.pageStudentSchemeLearningInServicerV2({
      page: page,
      request: filter
    })
    this.studentSchemeLearningList = data.data.currentPageData || new Array<ZZKDStudentSchemeLearningResponse>()
    const studentSchemeLearningResponsePage = new Response<StudentSchemeLearningResponsePage>()
    return Object.assign(studentSchemeLearningResponsePage, data)
  }

  /**
   * 分销网授
   */
  async pageStudentSchemeLearningInDistributorV2(page: Page, filter: StudentSchemeLearningRequestVo) {
    const data = await ZzkdSchemeLearningMsGateway.pageStudentSchemeLearningInDistributor({
      page: page,
      request: filter
    })
    this.studentSchemeLearningList = data.data.currentPageData || new Array<ZZKDStudentSchemeLearningResponse>()
    const studentSchemeLearningResponsePage = new Response<StudentSchemeLearningResponsePage>()
    return Object.assign(studentSchemeLearningResponsePage, data)
  }

  /**
   * 专题网授
   */
  async pageStudentSchemeLearningInTrainingChannelV2(page: Page, filter: StudentSchemeLearningRequestVo) {
    const data = await ZzkdSchemeLearningMsGateway.pageStudentSchemeLearningInTrainingChannelV2({
      page: page,
      request: filter
    })
    this.studentSchemeLearningList = data.data.currentPageData || new Array<ZZKDStudentSchemeLearningResponse>()
    const studentSchemeLearningResponsePage = new Response<StudentSchemeLearningResponsePage>()
    return Object.assign(studentSchemeLearningResponsePage, data)
  }
  /**
   * 导出
   */
  async exportExcelDetail(param: StudentSchemeLearningRequestVo) {
    return await ZzkdSchemeLearningMsGateway.exportStudentSchemeLearningExcelInServicer(param)
  }
  /**
   * 导出数据——分销商
   */
  async exportStudentSchemeLearningExcelInDistributor(param: StudentSchemeLearningRequestVo) {
    return await ZzkdSchemeLearningMsGateway.exportStudentSchemeLearningExcelInDistributor(param)
  }
  /**
   * 导出--专题管理员
   */
  async exportStudentSchemeLearningExcelInTrainingChannel(param: StudentSchemeLearningRequestVo) {
    return await ZzkdSchemeLearningMsGateway.exportStudentSchemeLearningExcelInTrainingChannelV2(param)
  }
  /**
   * 获取证书编号
   */
  private enrichWithCertificateNumber(data: Array<StudentLearningStaticsVo>): Array<StudentLearningStaticsVoDiff> {
    return data.map((item) => {
      const tmp = Object.assign(new StudentLearningStaticsVoDiff(), item)
      const studentSchemeLearningInfo = this.studentSchemeLearningList.find((res) => res.studentNo === item.studentNo)
      if (studentSchemeLearningInfo) {
        tmp.certificateNumber = studentSchemeLearningInfo.certificateNumber
      }
      return tmp
    })
  }
}
export default QueryStudentLearningListDiff
