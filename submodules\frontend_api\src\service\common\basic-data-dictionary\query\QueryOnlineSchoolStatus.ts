import { ResponseStatus } from '@hbfe/common'
import Context from '../../context/Context'
import MsServicerContract from '@api/ms-gateway/ms-servicercontract-v1'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
import platformJxjyDistributor from '@api/platform-gateway/platform-jxjy-distributor-admin-v1'
import QueryUserInfo from '@api/service/customer/user/query/QueryUserInfo'

class QueryOnlineSchoolStatus {
  async queryStatus(): Promise<ResponseStatus> {
    if (Context.businessEnvironment.serviceToken.tokenMeta.status !== 1) {
      return new ResponseStatus(5001, '网校停用')
    }
    let res
    if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
      res = await platformJxjyDistributor.isOnlineSchoolContractExpired()
    } else {
      res = await MsServicerContract.isOnlineSchoolContractExpired()
    }
    if (res.status.isSuccess() && !res.data) {
      return new ResponseStatus(200, '网校正常')
    } else {
      QueryUserInfo.resetUserInfo()
      return new ResponseStatus(5002, '网校合约过期')
    }
  }
}
export default new QueryOnlineSchoolStatus()
