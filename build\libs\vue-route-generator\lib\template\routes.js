'use strict'
Object.defineProperty(exports, '__esModule', { value: true })
const prettier = require('prettier')

function createChildrenRoute(children) {
  return `,children: [${children.map(createRoute).join(',')}]`
}

function createRoute(meta) {
  const children = !meta.children ? '' : createChildrenRoute(meta.children)
  // If default child is exists, the route should not have a name.
  const routeName = meta.children && meta.children.some(m => m.path === '') ? '' : `name: '${meta.name}',`
  const metaInfo = Object.assign({ permissionMap: meta.permissionMap }, meta.routeMeta || {})
  const routeMeta = !metaInfo ? '' : ',meta: ' + JSON.stringify(metaInfo, null, 2)
  return `
  {
    ${routeName}
    path: '${meta.path}',
    component: ${meta.specifier}${routeMeta}${children}
  }`
}

function createImport(meta, dynamic, chunkNamePrefix) {
  const code = dynamic
    ? `function ${meta.specifier}() { return import(/* webpackChunkName: "${chunkNamePrefix}${meta.name}" */ '${meta.component}') }`
    : `import ${meta.specifier} from '${meta.component}'`
  return meta.children
    ? [code].concat(meta.children.map(child => createImport(child, dynamic, chunkNamePrefix))).join('\n')
    : code
}

function createRoutes(meta, dynamic, chunkNamePrefix) {
  const imports = meta.map(m => createImport(m, dynamic, chunkNamePrefix)).join('\n')
  const code = meta.map(createRoute).join(',')
  return prettier.format(`${imports}\n\nexport default [${code}]`, {
    parser: 'babel',
    semi: false,
    singleQuote: true
  })
}

exports.createSecure = function createSecure(meta) {
  return meta.map(createRoute)
}

exports.createRoutes = createRoutes
