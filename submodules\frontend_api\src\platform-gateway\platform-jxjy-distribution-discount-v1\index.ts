import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjy-distribution-discount-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjy-distribution-discount-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class ApplyAttach {
  fileName?: string
  filePath?: string
}

/**
 * 关闭产品优惠申请
 */
export class CloseDiscountApplyRequest {
  /**
   * 优惠申请id
   */
  applyId?: string
}

/**
 * 创建产品优惠申请请求命令
 */
export class CreateDiscountApplyRequest {
  /**
   * 优惠申请id
   */
  applyId?: string
  /**
   * 产品分销授权id
   */
  productDistributionAuthId?: string
  /**
   * 申请人id
   */
  applyUserId?: string
  /**
   * 申请方id(分销商id)
   */
  applicantId?: string
  /**
   * 审批方id(供应商id)
   */
  auditPartyId?: string
  /**
   * 优惠地区【必传】
   */
  discountRegionList?: Array<string>
  /**
   * 允许报名人数(数量限制)
1-不限
2-区间
@see QuantityConstraints
   */
  quantityConstraint?: number
  /**
   * 最少允许报名人数
   */
  minQuantity?: number
  /**
   * 最大允许报名人数
   */
  maxQuantity?: number
  /**
   * 优惠价格
   */
  discountPrice?: number
  /**
   * 优惠时间段类型
1-周期
2-长期
@see TimeConstraints
   */
  discountDateConstraint?: number
  /**
   * 【必填】是否立即开始
   */
  immediatelyStart: boolean
  /**
   * 【非立即开始时必填】优惠开始日期
   */
  discountStartDate?: string
  /**
   * 【周期必填】优惠结束日期
   */
  discountEndDate?: string
  /**
   * 申请原因
   */
  applyReason?: string
  /**
   * 附件
   */
  applyAttachList?: Array<ApplyAttach>
}

/**
 * 开启优惠申请请求
 */
export class OpenDiscountApplyRequest {
  /**
   * 产品优惠申请
   */
  applyId?: string
}

/**
 * 操作产品优惠申请
 */
export class OperateDiscountApplyRequest {
  /**
   * 申请id
   */
  applyId?: string
  /**
   * 操作
1-通过
2-未通过
   */
  operate?: number
  /**
   * 审批人id
   */
  auditUserId?: string
  /**
   * 审核说明
   */
  auditReason?: string
}

/**
 * 优惠申请通用返回类
 */
export class DistributionDiscountApplyCommonResponse {
  /**
   * 状态码：
200-成功
500-失败
   */
  code: string
  /**
   * 响应信息
   */
  message: string
  /**
   * 响应数据(目前没用)
   */
  data: string
  /**
   * 产品优申请id
   */
  applyId: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 关闭产品优惠申请
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async closeDistributionDiscountApply(
    request: CloseDiscountApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.closeDistributionDiscountApply,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建优惠申请
   * @param createRequest
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createDistributionDiscountApply(
    createRequest: CreateDiscountApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createDistributionDiscountApply,
    operation?: string
  ): Promise<Response<DistributionDiscountApplyCommonResponse>> {
    return commonRequestApi<DistributionDiscountApplyCommonResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 开启产品优惠申请
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async openDistributionDiscountApply(
    request: OpenDiscountApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.openDistributionDiscountApply,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 操作优惠申请
   * 1-通过
   * 2-驳回/拒绝
   * @param operateRequest
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param operateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async operateDistributionDiscountApply(
    operateRequest: OperateDiscountApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.operateDistributionDiscountApply,
    operation?: string
  ): Promise<Response<DistributionDiscountApplyCommonResponse>> {
    return commonRequestApi<DistributionDiscountApplyCommonResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { operateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
