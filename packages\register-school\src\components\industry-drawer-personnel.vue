<template>
  <el-drawer
    title="选择人员行业属性"
    :visible="drawerVisible"
    :direction="direction"
    size="600px"
    custom-class="m-drawer"
    @close="handleCancel"
  >
    <div class="drawer-bd">
      <el-tabs
        v-if="haveRSIndustry || haveJSIndustry || haveWSIndustry || haveGQIndustry || haveLSIndustry || haveYSIndustry"
        v-model="activeIndustryName"
        type="card"
        class="m-tab-card  is-badge"
      >
        <el-tab-pane v-if="haveRSIndustry" label="人社行业" name="first">
          <div slot="label">
            人社行业<el-badge v-if="isRsNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('人社') && RSProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="人社"
            @getProperty="getProperty"
            ref="rs"
            :PropertiesId="RSProperties"
            :isCancel="styleList.length"
            :isPersonnel="true"
            @getIndustryInfo="queryIndustryData"
          ></industry-properties>
        </el-tab-pane>
        <el-tab-pane v-if="haveJSIndustry" label="建设行业" name="second">
          <div slot="label">
            建设行业<el-badge v-if="isJsNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('建设') && JSProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="建设"
            @getProperty="getProperty"
            ref="js"
            :PropertiesId="JSProperties"
            :isCancel="styleList.length"
            :isPersonnel="true"
            @getIndustryInfo="queryIndustryData"
          ></industry-properties>
        </el-tab-pane>
        <el-tab-pane v-if="haveWSIndustry" label="职业卫生行业" name="third">
          <div slot="label">
            职业卫生行业<el-badge v-if="isWsNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('职业卫生') && WSProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="职业卫生"
            @getProperty="getProperty"
            ref="ws"
            :PropertiesId="WSProperties"
            :isCancel="styleList.length"
            :isPersonnel="true"
            @getIndustryInfo="queryIndustryData"
          ></industry-properties>
        </el-tab-pane>
        <el-tab-pane v-if="haveGQIndustry" label="工勤行业" name="fourth">
          <div slot="label">
            工勤行业<el-badge v-if="isGQNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('工勤') && GQProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="工勤"
            @getProperty="getProperty"
            ref="gq"
            :PropertiesId="GQProperties"
            :isCancel="styleList.length"
            :isPersonnel="true"
            @getIndustryInfo="queryIndustryData"
          ></industry-properties>
        </el-tab-pane>
        <el-tab-pane v-if="haveLSIndustry" label="教师行业" name="fifth">
          <div slot="label">
            教师行业<el-badge v-if="isLsNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('教师') && LSProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="教师"
            @getProperty="getProperty"
            ref="ls"
            :PropertiesId="LSProperties"
            :isCancel="styleList.length"
            :isPersonnel="true"
            @getIndustryInfo="queryIndustryData"
          ></industry-properties>
        </el-tab-pane>
        <el-tab-pane v-if="haveYSIndustry" label="药师行业" name="sixth">
          <div slot="label">
            药师行业<el-badge v-if="isYsNotChoosed" value="未选" class="u-badge"></el-badge
            ><el-badge v-else value="已选" class="u-badge" type="success"></el-badge>
          </div>
          <industry-properties
            :disabled="firstIndustryList.includes('药师') && YSProperties ? true : false"
            :industryPropertyObject="industryPropertyObject"
            title="药师"
            @getProperty="getProperty"
            ref="ys"
            :PropertiesId="YSProperties"
            :isCancel="styleList.length"
            :isPersonnel="true"
            @getIndustryInfo="queryIndustryData"
          ></industry-properties>
        </el-tab-pane>
      </el-tabs>
      <div v-else>
        请先选择培训行业
      </div>
    </div>
    <div class="m-btn-bar drawer-ft">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handleCommit" type="primary">确认</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustryV2'
  import QueryIndustryV1 from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import { Industry } from '@api/ms-gateway/ms-servicercontract-v1'
  import industryProperties from '@hbfe/jxjy-admin-registerSchool/src/components/industry-properties.vue'
  import { IndustryPropertyIdEnum } from '@api/service/training-institution/online-school/enum/IndustryPropertyIdEnum'
  import { IndustryIdEnum } from '@api/service/training-institution/online-school/enum/IndustryEnum'
  import PersonIndustryId, {
    PersonIndustryIdEnum
  } from '@api/service/training-institution/online-school/enum/PersonIndustryIdEnum'
  import QuerySubject from '@api/service/common/basic-data-dictionary/query/person-dictionary/QuerySubject'
  import { log } from 'lodash-decorators/utils'

  class IndustryData {
    subjectType: Array<any> = []
    majorType: Array<any> = []
    objectType: Array<any> = []
    postType: Array<any> = []
    technicalType: Array<any> = []
    jobType: Array<any> = []
    section: Array<any> = []
    subjects: Array<any> = []
    certType: Array<any> = []
    practiceType: Array<any> = []
    title: string
    industryPropertyId: string
    isQuery = false
    isLoading = false
  }
  class IndustryPropertyList {
    RSindustryData: Array<IndustryData> = []
    JSindustryData: Array<IndustryData> = []
    WSindustryData: Array<IndustryData> = []
    GQindustryData: Array<IndustryData> = []
    LSindustryData: Array<IndustryData> = []
    YSindustryData: Array<IndustryData> = []
  }
  @Component({
    components: {
      industryProperties
    }
  })
  export default class extends Vue {
    @Prop({
      required: false,
      default: () => {
        return new Array<string>()
      }
    })
    firstIndustryList: Array<string>
    /**
     * 抽屉显隐
     */
    @Prop({
      required: true,
      default: () => false
    })
    drawerVisible: boolean
    /**
     * 是否选中行业
     */
    @Prop({
      required: true,
      default: () => false
    })
    haveRSIndustry: boolean
    @Prop({
      required: true,
      default: () => false
    })
    haveJSIndustry: boolean
    @Prop({
      required: true,
      default: () => false
    })
    haveWSIndustry: boolean
    @Prop({
      required: true,
      default: () => false
    })
    haveGQIndustry: boolean
    @Prop({
      required: true,
      default: () => false
    })
    haveLSIndustry: boolean
    @Prop({
      required: true,
      default: () => false
    })
    haveYSIndustry: boolean
    /**
     * 行业培训属性id
     */
    @Prop({
      required: false,
      default: null
    })
    RSProperties: string
    @Prop({
      required: false,
      default: null
    })
    JSProperties: string
    @Prop({
      required: false,
      default: null
    })
    WSProperties: string
    @Prop({
      required: false,
      default: null
    })
    GQProperties: string
    @Prop({
      required: false,
      default: null
    })
    LSProperties: string
    @Prop({
      required: false,
      default: null
    })
    YSProperties: string
    /**
     * 判断默认选中tab
     */
    @Prop({
      required: false,
      default: () => '人社'
    })
    activeIndex: string
    @Watch('activeIndex', { immediate: true, deep: true })
    getIndex() {
      // 取消行业后 清除原先选择的培训属性
      this.selectedIndustyList = []
      if (this.activeIndex.indexOf('人社') != -1) {
        this.activeIndustryName = 'first'
      } else if (this.activeIndex.indexOf('建设') != -1) {
        this.activeIndustryName = 'second'
      } else if (this.activeIndex.indexOf('职业卫生') != -1) {
        this.activeIndustryName = 'third'
      } else if (this.activeIndex.indexOf('工勤') != -1) {
        this.activeIndustryName = 'fourth'
      } else if (this.activeIndex.indexOf('教师') != -1) {
        this.activeIndustryName = 'fifth'
      } else if (this.activeIndex.indexOf('药师') != -1) {
        this.activeIndustryName = 'sixth'
      }
    }
    activeIndustryName = 'first'
    /**
     * 用于存储选中的行业及培训属性id
     */
    JSIndustry: Industry = { id: '', properties: '' }
    RSIndustry: Industry = { id: '', properties: '' }
    WSIndustry: Industry = { id: '', properties: '' }
    GQIndustry: Industry = { id: '', properties: '' }
    LSIndustry: Industry = { id: '', properties: '' }
    YSIndustry: Industry = { id: '', properties: '' }
    /**
     * 行业id列表
     */
    industryList: Array<IndustryVo> = []
    /**
     * 已选的行业id列表
     */
    selectedIndustyList: Array<PersonIndustryIdEnum> = []
    direction = 'rtl'
    /**
     * 选中样式列表
     */
    styleList = new Array<string>()
    /**
     * 行业培训属性数据集合
     */
    industryPropertyObject: IndustryPropertyList = new IndustryPropertyList()

    /**
     * 行业是否未选中
     */
    get isRsNotChoosed() {
      return !this.RSProperties && !this.styleList.includes('人社')
    }
    get isJsNotChoosed() {
      return !this.JSProperties && !this.styleList.includes('建设')
    }
    get isWsNotChoosed() {
      return !this.WSProperties && !this.styleList.includes('职业卫生')
    }
    get isGQNotChoosed() {
      return !this.GQProperties && !this.styleList.includes('工勤')
    }
    get isLsNotChoosed() {
      return !this.LSProperties && !this.styleList.includes('教师')
    }
    get isYsNotChoosed() {
      return !this.YSProperties && !this.styleList.includes('药师')
    }
    async created() {
      this.getIndustryData()
    }

    async queryIndustryData(industryPropertyId: PersonIndustryIdEnum) {
      const RSindustryData = new IndustryData()
      const JSindustryData = new IndustryData()
      const WSindustryData = new IndustryData()
      const GQindustryData = new IndustryData()
      const LSindustryData = new IndustryData()
      const YSindustryData = new IndustryData()
      let dataItem = new IndustryData()
      switch (industryPropertyId) {
        case PersonIndustryIdEnum.RS:
        case PersonIndustryIdEnum.HN:
        case PersonIndustryIdEnum.AH:
        case PersonIndustryIdEnum.JSS:
        case PersonIndustryIdEnum.JX:
        case PersonIndustryIdEnum.SCRS:
        case PersonIndustryIdEnum.GS:
          // 人社
          dataItem = this.industryPropertyObject.RSindustryData.find(
            item => item.industryPropertyId === industryPropertyId
          )
          RSindustryData.isQuery = true
          dataItem.isLoading = true
          RSindustryData.subjectType = await QueryIndustry.getOperationSubjectList(
            IndustryIdEnum.RS,
            industryPropertyId
          )
          RSindustryData.majorType = await QueryIndustry.getOperationTraining(IndustryIdEnum.RS, industryPropertyId)
          await Promise.all(
            RSindustryData.majorType.map(async item => {
              item.children = await QueryIndustryV1.getIndustryDetail(item.propertyId)
            })
          )
          Object.assign(dataItem, RSindustryData)
          break
        case PersonIndustryIdEnum.JS:
        case PersonIndustryIdEnum.SC:
          // 建设
          dataItem = this.industryPropertyObject.JSindustryData.find(
            item => item.industryPropertyId === industryPropertyId
          )
          JSindustryData.isQuery = true
          dataItem.isLoading = true
          JSindustryData.subjectType = await QueryIndustry.getOperationSubjectList(
            IndustryIdEnum.JS,
            industryPropertyId
          )
          JSindustryData.majorType = await QueryIndustry.getOperationTraining(IndustryIdEnum.JS, industryPropertyId)
          await Promise.all(
            JSindustryData.majorType.map(async item => {
              item.children = await QueryIndustryV1.getIndustryDetail(item.propertyId)
            })
          )
          Object.assign(dataItem, JSindustryData)
          break
        case PersonIndustryIdEnum.WS:
          dataItem = this.industryPropertyObject.WSindustryData.find(
            item => item.industryPropertyId === industryPropertyId
          )
          WSindustryData.isQuery = true
          dataItem.isLoading = true
          // 职业卫生
          WSindustryData.majorType = await QueryIndustry.getOperationTraining(IndustryIdEnum.WS, industryPropertyId)
          WSindustryData.objectType = await QueryIndustry.getTrainingObject(IndustryIdEnum.WS, industryPropertyId)
          WSindustryData.postType = await QueryIndustry.getPositionCategory(IndustryIdEnum.WS, industryPropertyId)
          await Promise.all(
            WSindustryData.objectType.map(async item => {
              item.children = await QueryIndustry.getPositionCategory(item.propertyId, IndustryPropertyIdEnum.WS)
            })
          )
          Object.assign(dataItem, WSindustryData)
          break
        case PersonIndustryIdEnum.GQ:
          // 工勤
          dataItem = this.industryPropertyObject.GQindustryData.find(
            item => item.industryPropertyId === industryPropertyId
          )
          GQindustryData.isQuery = true
          dataItem.isLoading = true
          GQindustryData.technicalType = await QueryIndustry.getJobLevel(IndustryIdEnum.GQ, industryPropertyId)
          GQindustryData.jobType = await QueryIndustry.getJobCategory(IndustryIdEnum.GQ, industryPropertyId)
          Object.assign(dataItem, GQindustryData)
          break
        case PersonIndustryIdEnum.LS:
          // 教师
          dataItem = this.industryPropertyObject.LSindustryData.find(
            item => item.industryPropertyId === industryPropertyId
          )
          LSindustryData.isQuery = true
          dataItem.isLoading = true
          LSindustryData.section = await QueryIndustry.getSection(IndustryIdEnum.LS, industryPropertyId)
          LSindustryData.subjects = await QuerySubject.queryAllSubject()
          await Promise.all(
            LSindustryData.section.map(async item => {
              item.children = await QueryIndustryV1.getSubjects(item.propertyId)
            })
          )
          Object.assign(dataItem, LSindustryData)
          break
        case PersonIndustryIdEnum.YS:
          // 药师
          dataItem = this.industryPropertyObject.YSindustryData.find(
            item => item.industryPropertyId === industryPropertyId
          )
          YSindustryData.isQuery = true
          dataItem.isLoading = true
          YSindustryData.certType = await QueryIndustry.getCertificatesType(IndustryIdEnum.YS, industryPropertyId)
          await Promise.all(
            YSindustryData.certType.map(async item => {
              item.children = await QueryIndustryV1.getPractitionerCategory(item.propertyId)
            })
          )
          Object.assign(dataItem, YSindustryData)
          break
        default:
          break
      }
    }
    /**
     * 获取行业信息
     */
    async getIndustryData() {
      this.industryList = await QueryIndustry.getOperationIndustry()
      const industryIdList = this.industryList.map(item => {
        return item.id
      })
      const industryPropertyIdList = await QueryIndustry.getIndustryPropertyId('-1', industryIdList, 1)
      console.log(industryPropertyIdList, 'IndustryPropertyIdList 人员')
      industryPropertyIdList.map(async (item: any) => {
        const RSindustryData = new IndustryData()
        const JSindustryData = new IndustryData()
        const WSindustryData = new IndustryData()
        const GQindustryData = new IndustryData()
        const LSindustryData = new IndustryData()
        const YSindustryData = new IndustryData()
        switch (item.industryPropertyId) {
          case PersonIndustryIdEnum.RS:
          case PersonIndustryIdEnum.HN:
          case PersonIndustryIdEnum.AH:
          case PersonIndustryIdEnum.JSS:
          case PersonIndustryIdEnum.JX:
          case PersonIndustryIdEnum.SCRS:
          case PersonIndustryIdEnum.GS:
            // 人社
            this.industryPropertyObject.RSindustryData.unshift({
              ...RSindustryData,
              title: new PersonIndustryId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          case PersonIndustryIdEnum.JS:
          case PersonIndustryIdEnum.SC:
            // 建设
            this.industryPropertyObject.JSindustryData.unshift({
              ...JSindustryData,
              title: new PersonIndustryId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          case PersonIndustryIdEnum.WS:
            // 职业卫生
            for (let i = 0; i < WSindustryData.objectType?.length; i++) {
              WSindustryData.objectType[i].children = await QueryIndustry.getPositionCategory(
                WSindustryData.objectType[i].propertyId,
                IndustryPropertyIdEnum.WS
              )
            }
            this.industryPropertyObject.WSindustryData.unshift({
              ...WSindustryData,
              title: new PersonIndustryId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          case PersonIndustryIdEnum.GQ:
            // 工勤
            this.industryPropertyObject.GQindustryData.unshift({
              ...GQindustryData,
              title: new PersonIndustryId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          case PersonIndustryIdEnum.LS:
            // 教师
            this.industryPropertyObject.LSindustryData.unshift({
              ...LSindustryData,
              title: new PersonIndustryId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          case PersonIndustryIdEnum.YS:
            // 药师
            this.industryPropertyObject.YSindustryData.unshift({
              ...YSindustryData,
              title: new PersonIndustryId().map.get(item.industryPropertyId),
              industryPropertyId: item.industryPropertyId
            })
            break
          default:
            break
        }
      })
    }
    /**
     * 回传行业培训属性id
     */
    getProperty(propertyId: PersonIndustryIdEnum, title: string) {
      switch (title) {
        case '人社':
          if (!this.selectedIndustyList.includes(PersonIndustryIdEnum.RS)) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        case '建设':
          if (!this.selectedIndustyList.includes(PersonIndustryIdEnum.JS)) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        case '职业卫生':
          if (!this.selectedIndustyList.includes(PersonIndustryIdEnum.WS)) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        case '工勤':
          if (!this.selectedIndustyList.includes(PersonIndustryIdEnum.GQ)) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        case '教师':
          if (!this.selectedIndustyList.includes(PersonIndustryIdEnum.LS)) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        case '药师':
          if (!this.selectedIndustyList.includes(PersonIndustryIdEnum.YS)) {
            this.selectedIndustyList.push(propertyId)
          }
          break
        default:
          break
      }
      if (!this.styleList.includes(title)) {
        this.styleList.push(title)
      }
    }
    /**
     * 隐藏抽屉
     */
    handleCancel() {
      this.$emit('cancel')
      this.styleList = []
    }
    /**
     * 确认（关闭抽屉）
     */
    handleCommit() {
      if (
        (this.haveRSIndustry && this.isRsNotChoosed) ||
        (this.haveJSIndustry && this.isJsNotChoosed) ||
        (this.haveWSIndustry && this.isWsNotChoosed) ||
        (this.haveGQIndustry && this.isGQNotChoosed) ||
        (this.haveLSIndustry && this.isLsNotChoosed) ||
        (this.haveYSIndustry && this.isYsNotChoosed)
      ) {
        this.$alert(
          '请选择' +
            (
              (this.haveRSIndustry && this.isRsNotChoosed ? '人社、' : '') +
              (this.haveJSIndustry && this.isJsNotChoosed ? '建设、' : '') +
              (this.haveWSIndustry && this.isWsNotChoosed ? '卫生、' : '') +
              (this.haveGQIndustry && this.isGQNotChoosed ? '工勤、' : '') +
              (this.haveLSIndustry && this.isLsNotChoosed ? '教师、' : '') +
              (this.haveYSIndustry && this.isYsNotChoosed ? '教师、' : '')
            ).slice(0, -1) +
            '行业下的人员属性值',
          '提示',
          {
            type: 'warning'
          }
        )
        return
      }
      if (this.$refs.rs) {
        ;(this.$refs.rs as any).selectProperty()
      }
      if (this.$refs.js) {
        ;(this.$refs.js as any).selectProperty()
      }
      if (this.$refs.ws) {
        ;(this.$refs.ws as any).selectProperty()
      }
      if (this.$refs.gq) {
        ;(this.$refs.gq as any).selectProperty()
      }
      if (this.$refs.ls) {
        ;(this.$refs.ls as any).selectProperty()
      }
      if (this.$refs.ys) {
        ;(this.$refs.ys as any).selectProperty()
      }
      this.JSIndustry = {}
      this.RSIndustry = {}
      this.WSIndustry = {}
      this.GQIndustry = {}
      this.LSIndustry = {}
      if (this.haveJSIndustry) {
        this.JSIndustry.id = this.industryList[0].id
      }
      if (this.haveRSIndustry) {
        this.RSIndustry.id = this.industryList[1].id
      }
      if (this.haveWSIndustry) {
        this.WSIndustry.id = this.industryList[2].id
      }
      if (this.haveGQIndustry) {
        this.GQIndustry.id = this.industryList[3].id
      }
      if (this.haveLSIndustry) {
        this.LSIndustry.id = this.industryList[4].id
      }
      if (this.haveYSIndustry) {
        this.YSIndustry.id = this.industryList[5].id
      }
      this.selectedIndustyList.map(item => {
        switch (item) {
          case PersonIndustryIdEnum.RS:
            this.RSIndustry.properties = PersonIndustryIdEnum.RS
            break
          case PersonIndustryIdEnum.HN:
            this.RSIndustry.properties = PersonIndustryIdEnum.HN
            break
          case PersonIndustryIdEnum.AH:
            this.RSIndustry.properties = PersonIndustryIdEnum.AH
            break
          case PersonIndustryIdEnum.JSS:
            this.RSIndustry.properties = PersonIndustryIdEnum.JSS
            break
          case PersonIndustryIdEnum.JX:
            this.RSIndustry.properties = PersonIndustryIdEnum.JX
            break
          case PersonIndustryIdEnum.GS:
            this.RSIndustry.properties = PersonIndustryIdEnum.GS
            break
          case PersonIndustryIdEnum.SCRS:
            this.RSIndustry.properties = PersonIndustryIdEnum.SCRS
            break
          case PersonIndustryIdEnum.JS:
            this.JSIndustry.properties = PersonIndustryIdEnum.JS
            break
          case PersonIndustryIdEnum.SC:
            this.JSIndustry.properties = PersonIndustryIdEnum.SC
            break
          case PersonIndustryIdEnum.WS:
            this.WSIndustry.properties = PersonIndustryIdEnum.WS
            break
          case PersonIndustryIdEnum.GQ:
            this.GQIndustry.properties = PersonIndustryIdEnum.GQ
            break
          case PersonIndustryIdEnum.LS:
            this.LSIndustry.properties = PersonIndustryIdEnum.LS
            break
          case PersonIndustryIdEnum.YS:
            this.YSIndustry.properties = PersonIndustryIdEnum.YS
            break
          default:
            break
        }
      })
      this.$emit(
        'submit',
        this.RSIndustry,
        this.JSIndustry,
        this.WSIndustry,
        this.GQIndustry,
        this.LSIndustry,
        this.YSIndustry
      )
    }
  }
</script>

<style lang="scss" scoped>
  //抽屉
  .m-drawer {
    .el-drawer__body {
      overflow-y: scroll;
      display: flex;
      flex-direction: column;
    }

    .el-drawer__header {
      font-size: 18px;
      color: #333;
      margin-bottom: 20px;
    }

    .drawer-bd {
      padding: 0 20px 20px;
      flex: 1;
    }

    .drawer-ft {
      position: sticky;
      bottom: 0;
      z-index: 9;
      padding: 15px 0;
      background-color: rgba(#f8f8f8, 0.9);
      border-top: 1px solid #eee;
      text-align: center;
    }

    .m-btn-bar {
      &.is-sticky {
        padding-top: 10px;
      }
    }
    //行业属性弹窗-单选控件列表
    .m-attribute-select {
      width: 100%;

      .el-radio {
        border: 1px solid #e4e7ed;
        border-radius: 3px;
        padding: 15px;
        margin-top: 20px;
        margin-right: 0;
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        box-sizing: border-box;

        .el-radio__label {
          flex: 1;
          display: flex;
          flex-direction: row;
          justify-content: space-between;
          align-items: center;
        }

        .tit {
          width: 400px;
          display: inline-block;
          font-size: 15px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        &.is-checked {
          border: 1px solid #1f86f0;
        }
      }
    }
  }
</style>
