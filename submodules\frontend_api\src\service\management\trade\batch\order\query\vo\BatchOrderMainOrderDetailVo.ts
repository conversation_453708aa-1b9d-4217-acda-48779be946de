import BatchOrderMainOrderDetailOrderInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderDetailOrderInfoVo'
import BatchOrderDetailBuyerInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailBuyerInfoVo'
import BatchOrderMainOrderSubOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderSubOrderListDetailVo'
import BatchOrderMainOrderSubOrderListStatisticVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderSubOrderListStatisticVo'
import msTradeQuery, {
  OrderResponse,
  SubOrderResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import BatchOrderUtils from '@api/service/management/trade/batch/order/query/utils/BatchOrderUtils'
import { OrderTypeEnum } from '@api/service/common/enums/order/OrderTypes'
import UserModule from '@api/service/management/user/UserModule'
import { BatchOrderMainOrderStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderMainOrderStatus'
import TradeModule from '@api/service/management/trade/TradeModule'
import DataResolve from '@api/service/common/utils/DataResolve'
import BatchOrderDetailInvoiceInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailInvoiceInfoVo'

/**
 * @description 【集体报名订单】主单详情
 */
class BatchOrderMainOrderDetailVo extends OrderResponse {
  /**
   * 订单状态（方便ui展示）
   */
  mainOrderStatus: BatchOrderMainOrderStatusEnum = null
  /**
   * 提交批次时间
   */
  applyTime = ''

  /**
   * 付款时间
   */
  payTime = ''

  /**
   * 发货时间
   */
  deliveryTime = ''

  /**
   * 交易成功时间
   */
  tradeSuccessTime = ''

  /**
   * 交易关闭时间
   */
  tradeCloseTime = ''
  /**
   * 行业信息
   */
  industry = ''
  /**
   * 地区 - 省
   */
  province = ''
  /**
   * 地区 - 市
   */
  city = ''
  /**
   * 地区 - 区县
   */
  county = ''
  /**
   * 发票信息
   */
  invoiceInfoList: BatchOrderDetailInvoiceInfoVo[] = []

  /**
   * 订单信息
   */
  orderInfo: BatchOrderMainOrderDetailOrderInfoVo = new BatchOrderMainOrderDetailOrderInfoVo()

  /**
   * 购买人信息
   */
  buyerInfo: BatchOrderDetailBuyerInfoVo = new BatchOrderDetailBuyerInfoVo()

  /**
   * 购买清单
   */
  subOrderList: BatchOrderMainOrderSubOrderListDetailVo[] = []

  /**
   * 统计数据
   */
  subOrderListStatistic: BatchOrderMainOrderSubOrderListStatisticVo = new BatchOrderMainOrderSubOrderListStatisticVo()

  static async from(batchOrderNo: string, response: OrderResponse): Promise<BatchOrderMainOrderDetailVo> {
    const detail = new BatchOrderMainOrderDetailVo()
    Object.assign(detail, response)
    detail.applyTime = response.basicData?.orderStatusChangeTime?.normal ?? ''
    detail.payTime = response.basicData?.orderPaymentStatusChangeTime?.paid ?? ''
    detail.tradeCloseTime = response.basicData?.orderStatusChangeTime?.closed ?? ''
    detail.tradeSuccessTime = response.basicData?.orderStatusChangeTime.completed ?? ''
    detail.orderInfo = await this.getOrderInfo(batchOrderNo, response)
    detail.mainOrderStatus = detail.orderInfo.mainOrderStatus ?? null
    detail.buyerInfo = await this.getBuyerInfo(response)
    if (DataResolve.isWeightyArr(response.subOrderItems)) {
      detail.deliveryTime = response.subOrderItems[0].deliveryStatusChangeTime?.successDelivered ?? ''
      detail.subOrderList = await BatchOrderUtils.getSubOrderList(response)
    }
    const res = await msTradeQuery.getBatchOrderInServicer(batchOrderNo)
    if (res.status?.isSuccess()) {
      detail.invoiceInfoList = await BatchOrderUtils.getBatchOrderInvoiceInfoList(res.data)
    }
    detail.subOrderListStatistic = this.getSubOrderListStatistic(response)
    return detail
  }

  /**
   * 获取订单信息
   */
  private static async getOrderInfo(
    batchOrderNo: string,
    response: OrderResponse
  ): Promise<BatchOrderMainOrderDetailOrderInfoVo> {
    const result = new BatchOrderMainOrderDetailOrderInfoVo()
    result.mainOrderStatus = BatchOrderUtils.getMainOrderStatus(response)
    result.mainOrderNo = response.orderNo ?? ''
    result.batchOrderNo = batchOrderNo ?? ''
    result.mainOrderType = OrderTypeEnum.Collective
    result.saleChannel = response.saleChannel ?? 0
    // TODO 待优化，目前会额外请求一些接口
    const queryRemote = TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchOrderDetail
    const res = await queryRemote.queryBatchOrderDetail(batchOrderNo)
    result.creatorId = res.buyerInfo?.buyerId ?? ''
    result.creatorName = res.buyerInfo?.buyerName ?? ''
    result.creatorAccount = res.buyerInfo?.buyerAccount ?? ''
    return result
  }

  /**
   * 获取购买人信息
   */
  private static async getBuyerInfo(response: OrderResponse): Promise<BatchOrderDetailBuyerInfoVo> {
    const result = new BatchOrderDetailBuyerInfoVo()
    result.buyerId = response.buyer?.userId ?? ''
    // 查询学员信息
    if (result.buyerId) {
      const queryStudent = UserModule.queryUserFactory.queryStudentDetail(result.buyerId)
      const response = await queryStudent.queryDetail()
      if (response.status?.isSuccess()) {
        result.buyerAccount = response.data?.idCard ?? ''
        result.buyerName = response.data?.userName ?? ''
        result.buyerPhone = response.data?.phone ?? ''
      }
    }
    return result
  }

  /**
   * 获取统计信息
   */
  private static getSubOrderListStatistic(response: OrderResponse): BatchOrderMainOrderSubOrderListStatisticVo {
    const result = new BatchOrderMainOrderSubOrderListStatisticVo()
    if (DataResolve.isWeightyArr(response.subOrderItems)) {
      const subOrderItems = response.subOrderItems
      result.totalPeriod = BatchOrderUtils.getMainOrderPeriod(subOrderItems)
    }
    result.payAmount = response.basicData?.amount ?? 0
    result.totalAmount = result.payAmount
    return result
  }
}

export default BatchOrderMainOrderDetailVo
