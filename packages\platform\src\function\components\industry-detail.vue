<template>
  <el-drawer title="行业信息详情" :visible.sync="detailVisible" size="1100px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-tabs v-model="activeName" type="card" class="m-tab-card">
        <el-tab-pane label="地区" name="region">
          <div class="f-mt20" v-for="province in regionTree" :key="province.id">
            <div class="m-tit">
              <span class="tit-txt">{{ province.name }}</span>
            </div>
            <div class="m-attribute">
              <el-collapse v-model="regionActiveNames" accordion v-for="city in province.children" :key="city.id">
                <el-collapse-item :name="city.name">
                  <template slot="title">
                    <div class="m-sub-tit f-align-center">
                      <span class="tit-txt f-mr15">{{ city.name }}</span>
                    </div>
                  </template>
                  <div class="f-plr20 f-pt20">
                    <el-row :gutter="20">
                      <el-col :span="6" v-for="district in city.children" :key="district.id">
                        <div class="item">
                          <div class="item-bd">
                            <div class="name">{{ district.name }}</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-tab-pane>
        <!-- 人社行业 -->
        <el-tab-pane label="人社行业" name="society" v-if="hasSociety">
          <div class="f-mt20">
            <div class="m-tit">
              <span class="tit-txt">职称等级</span>
            </div>
            <div class="m-attribute">
              <el-row :gutter="20">
                <el-col :span="6" v-for="level in leaderPositionLevelList" :key="level.id">
                  <div class="item">
                    <div class="item-bd">
                      <div class="name">{{ level.name }}</div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <div class="m-tit">
              <span class="tit-txt">专业类别</span>
            </div>
            <div class="m-attribute">
              <el-collapse v-model="societyActiveNames" accordion>
                <el-collapse-item
                  v-for="category in societyCategoryAndMajorList"
                  :name="category.propertyId"
                  :key="category.propertyId"
                  @click.native="getMajorList(category)"
                >
                  <template slot="title">
                    <div class="m-sub-tit f-align-center">
                      <span class="tit-txt f-mr15">{{ category.name }}</span>
                    </div>
                  </template>
                  <div class="f-plr20 f-pt20">
                    <el-row :gutter="20">
                      <el-col :span="6" v-for="major in category.trainMajor" :key="major.propertyId">
                        <div class="item">
                          <div class="item-bd">
                            <div class="name">{{ major.name }}</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-tab-pane>
        <!-- 建设行业 -->
        <el-tab-pane label="建设行业" name="build" v-if="hasBuild">
          <div class="f-mt20">
            <div class="m-tit">
              <span class="tit-txt">培训类别-培训专业</span>
            </div>
            <div class="m-attribute">
              <el-collapse v-model="buildActiveNames" accordion>
                <el-collapse-item
                  v-for="category in buildCategoryAndMajorList"
                  :name="category.propertyId"
                  :key="category.propertyId"
                  @click.native="getMajorList(category)"
                >
                  <template slot="title">
                    <div class="m-sub-tit f-align-center">
                      <span class="tit-txt f-mr15">{{ category.name }}</span>
                    </div>
                  </template>
                  <div class="f-plr20 f-pt20" v-if="category.trainMajor.length">
                    <el-row :gutter="20">
                      <el-col :span="6" v-for="major in category.trainMajor" :key="major.propertyId">
                        <div class="item">
                          <div class="item-bd">
                            <div class="name">{{ major.name }}</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                  <div class="f-plr20 f-pt20" v-else>
                    <div class="m-no-date f-ptb30">
                      <img class="img is-small" src="@design/admin/assets/images/no-data-normal.png" alt="" />
                      <div class="date-bd">
                        <p class="f-f15 f-c9">暂无专业信息~</p>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-tab-pane>
        <!--职业卫生行业-->
        <el-tab-pane label="职业卫生行业" name="hygiene" v-if="hasHygiene">
          <div class="f-mt20">
            <div class="m-tit">
              <span class="tit-txt">人员类别--岗位类别</span>
            </div>
            <div class="m-attribute">
              <el-collapse v-model="hygieneNames" accordion>
                <el-collapse-item
                  v-for="category in hygienePersonnel"
                  :name="category.propertyId"
                  :key="category.propertyId"
                  @click.native="getHygieneMajorList(category)"
                >
                  <template slot="title">
                    <div class="m-sub-tit f-align-center">
                      <span class="tit-txt f-mr15">{{ category.name }}</span>
                    </div>
                  </template>
                  <div class="f-plr20 f-pt20">
                    <el-row :gutter="20">
                      <el-col :span="6" v-for="major in hygieneIdCategory" :key="major.propertyId">
                        <div class="item">
                          <div class="item-bd">
                            <div class="name">{{ major.name }}</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-tab-pane>
        <!--工勤行业-->
        <el-tab-pane label="工勤行业" name="workService" v-if="hasWorkService">
          <div class="f-mt20">
            <div class="f-mt20">
              <div class="m-tit">
                <span class="tit-txt">技术等级</span>
              </div>
              <div class="m-attribute">
                <el-row :gutter="20">
                  <el-col :span="6" v-for="level in technicalGrade" :key="level.sort">
                    <div class="item">
                      <div class="item-bd">
                        <div class="name">{{ level.name }}</div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
              <div class="m-tit">
                <span class="tit-txt">工种</span>
              </div>
              <div class="m-attribute">
                <el-row :gutter="20">
                  <el-col :span="6" v-for="level in typeWork" :key="level.sort">
                    <div class="item">
                      <div class="item-bd">
                        <div class="name">{{ level.name }}</div>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>
            </div>
          </div>
        </el-tab-pane>
        <!-- 教师行业 -->
        <el-tab-pane label="教师行业" name="teacherService" v-if="hasTeacher">
          <div class="f-mt20">
            <div class="m-tit">
              <span class="tit-txt">学科-学段</span>
            </div>
            <div class="m-attribute">
              <el-collapse v-model="teacherActiveNames" accordion>
                <el-collapse-item
                  v-for="subject in stageInformationList"
                  :key="subject.propertyId"
                  :name="subject.propertyId"
                  @click.native="getSubjectList(subject)"
                >
                  <template slot="title">
                    <div class="m-sub-tit f-align-center">
                      <span class="tit-txt f-mr15">{{ subject.name }}</span>
                    </div>
                  </template>
                  <div class="f-plr20 f-pt20" v-if="subjectInformationList.length">
                    <el-row :gutter="20">
                      <el-col :span="6" v-for="item in subjectInformationList" :key="item.propertyId">
                        <div class="item">
                          <div class="item-bd">
                            <div class="name">{{ item.name }}</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                  <div class="f-plr20 f-pt20" v-else>
                    <div class="m-no-date f-ptb30">
                      <img class="img is-small" src="@design/admin/assets/images/no-data-normal.png" alt="" />
                      <div class="date-bd">
                        <p class="f-f15 f-c9">暂无学科信息~</p>
                      </div>
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-tab-pane>
        <!-- 药师行业 -->
        <el-tab-pane label="药师行业" name="medicine" v-if="hasMedicine">
          <div class="f-mt20">
            <div class="m-tit">
              <span class="tit-txt">证书类型-执业类别</span>
            </div>
            <div class="m-attribute">
              <el-collapse v-model="societyActiveNames" accordion>
                <el-collapse-item
                  v-for="category in ysCertificateTypeList"
                  :name="category.propertyId"
                  :key="category.propertyId"
                  @click.native="getPracticingType(category)"
                >
                  <template slot="title">
                    <div class="m-sub-tit f-align-center">
                      <span class="tit-txt f-mr15">{{ category.name }}</span>
                    </div>
                  </template>
                  <div class="f-plr20 f-pt20">
                    <el-row :gutter="20">
                      <el-col :span="6" v-for="major in category.trainMajor" :key="major.propertyId">
                        <div class="item">
                          <div class="item-bd">
                            <div class="name">{{ major.name }}</div>
                          </div>
                        </div>
                      </el-col>
                    </el-row>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import QueryIndustryPropertyCategory from '@api/service/common/basic-data-dictionary/query/QueryIndustryPropertyCategory'
  import QueryLeaderPositionLevel from '@api/service/common/basic-data-dictionary/query/QueryLeaderPositionLevel'
  import LeaderPositionLevelVo from '@api/service/common/basic-data-dictionary/query/vo/LeaderPositionLevelVo'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'

  class TrainingCategoryAndMajor extends TrainingCategoryVo {
    trainMajor: Array<TrainingCategoryVo> = new Array<TrainingCategoryVo>()
  }

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    detailDialog: boolean

    activeName = 'region'

    regionActiveNames: Array<string> = []
    societyActiveNames: Array<string> = []
    buildActiveNames: Array<string> = []
    hygieneNames: Array<string> = []
    teacherActiveNames: Array<string> = []

    //行业列表
    industryList: Array<IndustryVo> = new Array<IndustryVo>()

    industryNameList: Array<string> = new Array<string>()

    //地区树
    regionTree: Array<RegionTreeVo> = new Array<RegionTreeVo>()

    //职称等级列表
    leaderPositionLevelList: Array<LeaderPositionLevelVo> = new Array<LeaderPositionLevelVo>()

    //人社培训类别和专业列表
    societyCategoryAndMajorList: Array<TrainingCategoryAndMajor> = new Array<TrainingCategoryAndMajor>()

    //建设培训类别和专业列表
    buildCategoryAndMajorList: Array<TrainingCategoryAndMajor> = new Array<TrainingCategoryAndMajor>()
    //工勤行业技术等级
    technicalGrade: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
    //工勤行业工种
    typeWork: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
    //职业卫生行业人员
    hygienePersonnel: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
    //职业卫生行业岗位类别
    hygieneIdCategory: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
    // 教师学段信息列表
    stageInformationList: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()
    // 教师学科列表
    subjectInformationList: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()

    //药师行业-证书类型-列表
    ysCertificateTypeList: Array<TrainingCategoryAndMajor> = new Array<TrainingCategoryAndMajor>()

    //人社属性id
    societyPropertyId = ''
    societyId = '' //人社行业id
    //建设属性id
    buildPropertyId = ''
    buildId = '' //建设行业id
    //工勤属性id
    workServicePropertyId = ''
    workServiceId = '' //工勤行业id
    //职业卫生属性id
    hygienePropertyId = ''
    hygieneId = '' //职业卫生行业id

    teacherPropertyId = '' //教师属性id
    teacherId = '' //教师行业id

    medicinePropertyId = '' //药师属性id
    medicineId = '' //药师行业id

    detailVisible = false
    hasSociety = false
    hasBuild = false
    //是否有工勤行业
    hasWorkService = false
    //是否有职业卫生行业
    hasHygiene = false
    /**
     * 是否有教师行业
     */
    hasTeacher = false
    /**
     * 是否有药师行业
     */
    hasMedicine = false

    @Watch('detailDialog')
    async changeDialogCtrl() {
      this.detailVisible = this.detailDialog
      await this.getRegionInfo()
      await this.getIndustryList()
      if (this.hasSociety) {
        await this.getSocietyInfo()
      }
      if (this.hasBuild) {
        await this.getBuildInfo()
      }
      if (this.hasWorkService) {
        await this.getWorkServiceInfo()
      }
      if (this.hasHygiene) {
        await this.getHygieneInfo()
      }
      if (this.hasMedicine) {
        await this.getMedicineInfo()
      }
    }

    @Watch('detailVisible')
    changeDialogVisible() {
      this.$emit('update:detailDialog', this.detailVisible)
    }

    async getPracticingType(item: TrainingCategoryAndMajor) {
      if (item.trainMajor?.length === 0) {
        const res = await QueryPersonIndustry.getPractitionerCategory(item.propertyId)
        item.trainMajor = res.map(item => Object.assign(new TrainingCategoryAndMajor(), item))
      }
    }

    async getMajorList(item: TrainingCategoryAndMajor) {
      if (item.trainMajor?.length === 0) {
        const res = await QueryPersonIndustry.getIndustryDetail(item.propertyId)
        item.trainMajor = res.map(item => Object.assign(new TrainingCategoryAndMajor(), item))
      }
    }

    async getHygieneMajorList(item: TrainingCategoryAndMajor | TrainingPropertyResponse) {
      this.hygieneIdCategory = await QueryPersonIndustry.getPositionCategory(item.propertyId)
    }

    async getIndustryList() {
      const industryRes = await QueryIndustry.queryIndustry()
      if (industryRes.isSuccess()) {
        this.hasSociety = false
        this.hasBuild = false
        this.industryList = QueryIndustry.industryList
        this.industryNameList = []
        this.industryList.forEach(item => {
          if (item?.name?.indexOf('人社行业') !== -1) {
            this.hasSociety = true
            this.societyPropertyId = item.propertyId
            this.societyId = item.id
          }
          if (item?.name?.indexOf('建设行业') !== -1) {
            this.hasBuild = true
            this.buildPropertyId = item.propertyId
            this.buildId = item.id
          }
          if (item?.name?.indexOf('工勤行业') !== -1) {
            this.hasWorkService = true
            this.workServicePropertyId = item.propertyId
            this.workServiceId = item.id
          }
          if (item?.name?.indexOf('职业卫生行业') !== -1) {
            this.hasHygiene = true
            this.hygienePropertyId = item.propertyId
            this.hygieneId = item.id
          }
          if (item?.name?.indexOf('教师行业') !== -1) {
            this.hasTeacher = true
            this.teacherPropertyId = item.propertyId
            this.medicineId = item.id
          }
          if (item?.name?.indexOf('药师行业') !== -1) {
            this.hasMedicine = true
            this.medicinePropertyId = item.propertyId
            this.medicineId = item.id
          }
          this.industryNameList.push(item.name)
        })
      }
      await QueryIndustryPropertyCategory.queryIndustryProperty()
    }

    //获取地区属性信息
    async getRegionInfo() {
      this.regionTree = await QueryBusinessRegion.getServiceOrIndustry(0, true)
      //   this.regionTree = QueryPhysicalRegion.regionTree || ([] as RegionTreeVo[])
    }

    // 获取药师行业属性信息
    async getMedicineInfo() {
      const categoryList = await QueryPersonIndustry.getCertificatesType(this.medicineId)
      this.ysCertificateTypeList = categoryList?.map(item => Object.assign(new TrainingCategoryAndMajor(), item))
    }

    //获取人社行业属性信息
    async getSocietyInfo() {
      console.log(QueryIndustryPropertyCategory.industryPropertyCategoryCache)
      //获取职称等级
      const res = await QueryLeaderPositionLevel.queryLeaderPositionLevel()
      if (res.isSuccess()) {
        this.leaderPositionLevelList = QueryLeaderPositionLevel.leaderPositionLevelList
      }
      //获取培训类别
      const categoryList = await QueryPersonIndustry.getOperationTraining(this.societyId)
      this.societyCategoryAndMajorList = categoryList?.map(item => Object.assign(new TrainingCategoryAndMajor(), item))
    }

    //获取建设行业属性信息
    async getBuildInfo() {
      const categoryList = await QueryPersonIndustry.getOperationTraining(this.buildId)
      this.buildCategoryAndMajorList = categoryList?.map(item => Object.assign(new TrainingCategoryAndMajor(), item))
    }

    // //获取教师行业信息属性信息
    async getTeacherInfo() {
      //   学段信息
      const categoryList = await QueryPersonIndustry.getSection(this.teacherId)
      this.stageInformationList = categoryList?.map(item => Object.assign(new TrainingCategoryAndMajor(), item))
    }

    // 学段点击事件
    async getSubjectList(item: TrainingPropertyResponse) {
      this.subjectInformationList = await QueryPersonIndustry.getSubjects(item.propertyId)
    }

    //获取工勤行业行业属性信息
    async getWorkServiceInfo() {
      // 获取技术等级
      this.technicalGrade = await QueryPersonIndustry.getJobLevel(this.workServiceId)
      this.typeWork = await QueryPersonIndustry.getJobCategory(this.workServiceId)
    }

    //获取职业卫生行业行业属性信息
    async getHygieneInfo() {
      // 获取人员类别
      this.hygienePersonnel = await QueryPersonIndustry.getTrainingObject(this.hygieneId)
    }

    async created() {
      this.detailVisible = this.detailDialog
      await this.getRegionInfo()
      await this.getIndustryList()
      if (this.hasSociety) {
        await this.getSocietyInfo()
      }
      if (this.hasBuild) {
        await this.getBuildInfo()
      }
      await this.getTeacherInfo()
    }
  }
</script>
