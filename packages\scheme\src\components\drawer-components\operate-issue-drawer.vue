<template>
  <el-drawer
    :title="`${isCreate ? '添加' : '编辑'}期别`"
    :visible.sync="showDrawer"
    size="960px"
    custom-class="m-drawer scroll-anchor"
    destroy-on-close
    :wrapperClosable="false"
  >
    <div class="drawer-bd" ref="drawerBd">
      <div class="m-side-positioner">
        <div
          class="item"
          :class="{ 'z-cur': activeAnchor == item.id }"
          v-for="item in sideList"
          :key="item.id"
          @click="scrollToSection(item.id)"
        >
          <div class="dot"></div>
          <div class="tit">{{ item.text }}</div>
        </div>
      </div>
      <div style="padding-right: 160px">
        <el-row :gutter="16">
          <el-form
            ref="topForm"
            :model="issueConfigDetail"
            :rules="issueConfigDetailRules"
            label-width="205px"
            class="m-form f-mt10"
          >
            <el-col :span="19">
              <el-form-item label="期别名称：" prop="issueName">
                <el-input v-model="issueConfigDetail.issueName" placeholder="请输入期别" />
              </el-form-item>
              <el-form-item label="期别编号：" prop="issueNo">
                <el-input
                  :disabled="isUpdate"
                  v-model="issueConfigDetail.issueNo"
                  placeholder="请输入2位数字期别编号"
                />
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <div class="m-add-period">
          <div class="section" id="issue-base-info">
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">基础信息</span>
            </div>
            <el-form
              ref="basicForm"
              :model="issueConfigDetail"
              label-width="180px"
              class="m-form f-p20"
              :rules="issueConfigDetailRules"
            >
              <el-form-item label="培训报到时段：" prop="checkDateRange.dateRange">
                <el-date-picker
                  v-model="issueConfigDetail.checkDateRange.dateRange"
                  type="daterange"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  :default-time="['00:00:00', '23:59:59']"
                  range-separator="—"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  :picker-options="checkDatePickerOptions"
                  class="form-l"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="开放报名人数：" prop="openRegistrationNum">
                <el-input
                  v-model="issueConfigDetail.openRegistrationNum"
                  size="small"
                  placeholder="请输入开放报名人数"
                  style="width: 150px"
                  class="input-num f-mr5"
                  @input="
                    (value) => (issueConfigDetail.openRegistrationNum = Math.max(0, Number(value.replace(/\D/g, ''))))
                  "
                />
                人
              </el-form-item>
              <el-form-item label="已报名人数：" prop="registeredNumDisplayType">
                <el-radio-group
                  v-model="issueConfigDetail.registeredNumDisplayType"
                  @change="issueConfigDetail.fixedRegisteredNum = 0"
                >
                  <el-radio :label="RegisteredNumDisplayTypeEnum.read_enrollment">读取实际报名人数</el-radio>
                  <el-radio :label="RegisteredNumDisplayTypeEnum.fixed_value"
                    >固定显示数值
                    <el-input
                      v-model="issueConfigDetail.fixedRegisteredNum"
                      size="small"
                      style="width: 120px"
                      placeholder="请填写整数"
                      :disabled="
                        issueConfigDetail.registeredNumDisplayType !== RegisteredNumDisplayTypeEnum.fixed_value
                      "
                      @input="
                        (value) =>
                          (issueConfigDetail.fixedRegisteredNum = Math.max(0, Number(value.replace(/\D/g, ''))))
                      "
                      class="input-num f-mr5"
                    />
                    人</el-radio
                  >
                </el-radio-group>
              </el-form-item>
              <el-form-item label="培训时段：" prop="issueTrainingDateType">
                <el-radio-group
                  v-model="issueConfigDetail.issueTrainingDateType"
                  :disabled="hasDisabled || disableTrainingPeriod"
                  @input="dateTypeChange"
                >
                  <el-radio v-for="item in trainingDateRange" :label="item.code" :key="item.code">{{
                    item.desc
                  }}</el-radio>
                </el-radio-group>
                <div class="f-mt5" v-if="isShowIssueCourseHours">
                  请选择
                  <el-date-picker
                    v-model="issueConfigDetail.trainingDateRange.dateRange"
                    type="daterange"
                    value-format="yyyy-MM-dd"
                    range-separator="—"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    class="form-l"
                    :picker-options="disabledTrainingPeriod"
                    :disabled="hasDisabled || disableTrainingPeriod"
                  >
                  </el-date-picker>
                </div>
              </el-form-item>
              <el-form-item label="期别学时：" v-if="isShowIssueCourseHours">
                <el-input-number
                  v-model="issueConfigDetail.periods"
                  :step="1"
                  :min="0"
                  label="请输入期别学时"
                  step-strictly
                />
              </el-form-item>
              <el-form-item label="培训地点：" prop="trainingPointId">
                <el-select
                  placeholder="请选择培训地点"
                  v-model="issueConfigDetail.trainingPointId"
                  :disabled="hasDisabled"
                  @change="trainingPointChange"
                >
                  <el-option
                    v-for="item in trainingPlaceManage.list"
                    :key="item.id"
                    :value="item.id"
                    :label="item.trainingPlaceName"
                    :disabled="item.trainingPlaceStatus.equal(CourseStatusEnum.DISABLE)"
                    >{{ item.trainingPlaceName }}</el-option
                  >
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div class="section" id="issue-sign-up-info">
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">报名信息</span>
            </div>
            <el-form
              ref="signUpform"
              :model="issueConfigDetail"
              label-width="180px"
              class="m-form f-p20"
              :rules="issueConfigDetailRules"
            >
              <el-form-item label="展示在门户：" prop="isShowInPortal">
                <el-radio-group v-model="issueConfigDetail.isShowInPortal" @change="showPoralChange">
                  <el-radio :label="true">
                    展示在学员门户
                    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-info m-tooltip-icon f-c9"></i>
                      <div slot="content">当勾选此项时，培训期别在学员门户上进行展示</div>
                    </el-tooltip>
                  </el-radio>
                  <el-radio :label="false">不展示在学员门户</el-radio>
                </el-radio-group>
                <div class="bg-gray f-mt10 f-plr15 f-pt5 f-pb5" v-if="issueConfigDetail.isShowInPortal">
                  <div class="f-flex">
                    <span><i class="f-ci f-mr5">*</i>展示用户：</span>
                    <el-checkbox-group v-model="issueConfigDetail.visibleChannelList">
                      <el-checkbox :label="1">学员门户可见</el-checkbox>
                      <el-checkbox :label="2">集体报名管理员可见</el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="开放学员报名：" prop="registerBeginTimeType">
                <el-checkbox v-model="isEnableStudentEnroll" @change="isEnableStudentEnrollChange">
                  不开放
                  <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9"></i>
                    <div slot="content">
                      勾选时，不显示开启报名时间、关闭报名时间配置项<br />未勾选时，下方显示开启报名时间、关闭报名时间配置项
                    </div>
                  </el-tooltip>
                </el-checkbox>
                <div class="bg-gray f-mt10 f-plr15 f-pt5 f-pb5" v-if="!isEnableStudentEnroll">
                  <div class="f-flex f-mb10">
                    <span><i class="f-ci f-mr5">*</i>开启报名时间：</span>
                    <el-form-item prop="registerBeginTimeType">
                      <el-radio-group
                        v-model="issueConfigDetail.registerBeginTimeType"
                        style="padding-top: 8px"
                        @change="issueConfigDetail.registerBeginTime = ''"
                      >
                        <div>
                          <el-radio :label="BeginTimeTypeEnum.open_now">立即开启</el-radio>
                        </div>
                        <div>
                          <el-radio :label="BeginTimeTypeEnum.assign"
                            >指定开启时间
                            <el-date-picker
                              :disabled="issueConfigDetail.registerBeginTimeType === BeginTimeTypeEnum.open_now"
                              v-model="issueConfigDetail.registerBeginTime"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              type="datetime"
                              placeholder="选择开启时间"
                            >
                            </el-date-picker
                          ></el-radio>
                        </div>
                      </el-radio-group>
                    </el-form-item>
                  </div>
                  <div class="f-flex f-mb10">
                    <span><i class="f-ci f-mr5">*</i>关闭报名时间：</span>

                    <el-form-item prop="registerEndTimeType">
                      <el-radio-group
                        v-model="issueConfigDetail.registerEndTimeType"
                        style="padding-top: 8px"
                        @change="issueConfigDetail.registerEndTime = ''"
                      >
                        <div>
                          <el-radio :label="EndTimeTypeEnum.no_end">无关闭时间</el-radio>
                        </div>
                        <div>
                          <el-radio :label="EndTimeTypeEnum.assign"
                            >指定关闭时间
                            <el-date-picker
                              :disabled="issueConfigDetail.registerEndTimeType === EndTimeTypeEnum.no_end"
                              v-model="issueConfigDetail.registerEndTime"
                              value-format="yyyy-MM-dd HH:mm:ss"
                              type="datetime"
                              placeholder="选择关闭时间"
                            >
                            </el-date-picker
                          ></el-radio>
                        </div>
                      </el-radio-group>
                    </el-form-item>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="是否开启住宿信息采集：" required>
                <el-switch
                  v-model="issueConfigDetail.isOpenAccommodationInfoCollect"
                  active-text="开启"
                  :disabled="hasSignUp || hasDisabled"
                  inactive-text="关闭"
                  class="m-switch"
                />
              </el-form-item>
              <el-form-item label="住宿信息采集须知：">
                <el-input
                  type="textarea"
                  :rows="6"
                  v-model="issueConfigDetail.accommodationInfoCollectNotice"
                  placeholder="请输入住宿信息采集须知内容..."
                >
                </el-input>
              </el-form-item>
            </el-form>
          </div>
          <div class="section" id="issue-assessment-requirements">
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">考核要求</span>
            </div>
            <el-form
              ref="assessmentForm"
              :model="issueConfigDetail"
              label-width="180px"
              class="m-form f-p20"
              :rules="issueConfigDetailRules"
            >
              <el-form-item label="是否开启报到：" required>
                <el-switch
                  v-model="issueConfigDetail.isOpenCheck"
                  active-text="开启"
                  inactive-text="关闭"
                  class="m-switch"
                  :disabled="hasSignUp || hasDisabled"
                />
              </el-form-item>
              <el-form-item label="是否开启结业测试：" required>
                <el-switch
                  v-model="issueConfigDetail.isOpenGraduationTest"
                  active-text="开启"
                  inactive-text="关闭"
                  class="m-switch"
                  :disabled="hasSignUp || hasDisabled || disabledEndTest"
                />
              </el-form-item>
              <el-form-item label="是否开启考勤：" required>
                <el-switch
                  v-model="issueConfigDetail.isOpenAttendance"
                  :disabled="isOpenAttendanceDisabled || hasDisabled"
                  active-text="开启"
                  inactive-text="关闭"
                  class="m-switch"
                  @change="isOpenAttendanceChange"
                />
              </el-form-item>
              <el-form-item prop="attendanceRate" label="考勤考核要求：" v-if="issueConfigDetail.isOpenAttendance">
                考勤率不低于
                <el-input-number
                  :controls="false"
                  :min="0"
                  :max="100"
                  :step="0.01"
                  :disabled="
                    isFirstCourse() ||
                    issueConfigDetail.attendanceAssessRequireType === AttendanceAssessRequireTypeEnum.same_as_scheme ||
                    hasDisabled
                  "
                  v-model="issueConfigDetail.attendanceRate"
                  size="small"
                  placeholder="请输入"
                  class="input-num f-mr5"
                />
                %
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-info m-tooltip-icon f-c9"></i>
                  <div slot="content">
                    考勤说明：<br />
                    考勤率=有效签到签退次数/应签到签退总次数，同一签到/签退时段产生多次签到"签退记录仅记为一次。<br />
                    例如：根据机构填报的课表计算出应签到签退总次数为12次，若考勒率不低于75%，那么学员有效签到签退次数达到9次即考勒合格。<br />
                    注：当根据考勤率计算学员至少应签到/签退次数时，考勤率*应签到/签退次数计算为小数时，按进位取整。<br />
                    例如：85%*13=11.05，此时，学员至少应签到/签退次数为12次
                  </div>
                </el-tooltip>
              </el-form-item>
            </el-form>
          </div>
          <div class="section" id="issue-contact-info">
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">联络信息</span>
            </div>
            <el-form
              ref="contactForm"
              :model="issueConfigDetail"
              label-width="180px"
              class="m-form f-p20"
              :rules="issueConfigDetailRules"
            >
              <el-form-item label="班主任：">
                <el-input v-model="issueConfigDetail.headTeacherName" placeholder="请输入班主任姓名" />
              </el-form-item>
              <el-form-item label="班主任联系电话：">
                <el-input v-model="issueConfigDetail.headTeacherPhone" placeholder="请输入班主任联系电话" />
              </el-form-item>
              <el-form-item label="酒店联系人：">
                <el-input v-model="issueConfigDetail.hotelContactsName" placeholder="请输入此期别酒店联系人姓名" />
              </el-form-item>
              <el-form-item label="酒店联系人电话：">
                <el-input v-model="issueConfigDetail.hotelContactsPhone" placeholder="请输入酒店联系人电话" />
              </el-form-item>
            </el-form>
          </div>
          <div class="section" id="issue-notice">
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">学员须知</span>
            </div>
            <div class="f-p20">
              <el-input type="textarea" :rows="6" v-model="issueConfigDetail.notice" placeholder="请输入学员须知">
              </el-input>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="showDrawer = false">取消</el-button>
      <!--      <el-button type="primary" :loading="saveLoading" v-if="operate === OperateTypeEnum.UPDATE" @click="save()"-->
      <!--        >确定并保存</el-button-->
      <!--      >-->
      <el-button type="primary" :loading="saveLoading" v-if="isShowIssueCourseHours" @click="save()">
        确定并保存
      </el-button>
      <el-button
        type="primary"
        plain
        :loading="saveLoading"
        v-else-if="
          issueConfigDetail.issueCourseList.length && operate === OperateTypeEnum.COPY && !isShowIssueCourseHours
        "
        @click="save(true)"
        >保存并编辑课程</el-button
      >
      <el-button
        type="primary"
        plain
        :loading="saveLoading"
        v-else-if="
          !issueConfigDetail.issueCourseList.length && operate === OperateTypeEnum.COPY && !isShowIssueCourseHours
        "
        @click="save(true)"
        >保存并创建课程</el-button
      >
      <el-button
        type="primary"
        plain
        :loading="saveLoading"
        v-else-if="isCreate && !isShowIssueCourseHours"
        @click="save(true)"
        >保存并创建课程</el-button
      >
      <el-button
        type="primary"
        plain
        :loading="saveLoading"
        v-else-if="
          issueConfigDetail.issueCourseList.length && operate === OperateTypeEnum.UPDATE && !isShowIssueCourseHours
        "
        @click="save(true)"
        >保存并编辑课程</el-button
      >
      <el-button
        type="primary"
        plain
        :loading="saveLoading"
        v-else-if="
          !issueConfigDetail.issueCourseList.length && operate === OperateTypeEnum.UPDATE && !isShowIssueCourseHours
        "
        @click="save(true)"
        >保存并创建课程</el-button
      >
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { OperateTypeEnum } from '@hbfe/jxjy-admin-scheme/src/models/OperateTypeEnum'
  import { AttendanceAssessRequireTypeEnum } from '@api/service/common/scheme/enum/AttendanceAssessRequireType'
  import { BeginTimeTypeEnum } from '@api/service/common/scheme/enum/BeginTimeType'
  import { EndTimeTypeEnum } from '@api/service/common/scheme/enum/EndTimeType'
  import { RegisteredNumDisplayTypeEnum } from '@api/service/common/scheme/enum/RegisteredNumDisplayType'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import { Form } from 'element-ui'
  import { Component, Inject, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import TrainingPlaceManage from '@api/service/management/resource/training-place-manage/TrainingPlaceManage'
  import { UiPage } from '@hbfe/common'
  import CheckIssueNoAvailableParam from '@api/service/management/train-class/offlinePart/model/CheckIssueNoAvailableParam'
  import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import { CourseStatusEnum } from '@api/service/common/enums/course/CourseStatus'
  import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
  import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
  import ServiceTime from '@api/service/common/service-time/ServiceTime'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import IssueTrainingDateType, {
    IssueTrainingDateTypeEnum
  } from '@api/service/common/scheme/enum/IssueTrainingDateType'
  import IssueCourseDetail from '@api/service/common/scheme/model/IssueCourseDetail'
  import DateRange from '@api/service/common/scheme/model/DateRange'

  @Component({
    components: { DoubleDatePicker }
  })
  export default class extends Vue {
    @Ref('scrollableDiv') scrollableDiv: HTMLElement
    @Ref('basicForm') basicForm: Form
    @Ref('signUpform') signUpform: Form
    @Ref('assessmentForm') assessmentForm: Form
    @Ref('contactForm') contactForm: Form
    @PropSync('operateIssueDrawer', { type: Boolean, default: true }) showDrawer: boolean
    @PropSync('selectIssue', {
      type: Object,
      default: () => new IssueConfigDetail()
    })
    issueConfigDetail: IssueConfigDetail
    @Prop({ type: Object, default: () => new TrainClassBaseModel() }) trainClassBaseInfo: TrainClassBaseModel
    @Prop({ type: Boolean, default: false }) hasSignUp: boolean

    @Ref('topForm') topForm: Form
    /**
     * 外部期别列表 用于校验期别名称
     */
    @Prop()
    issueConfigList: IssueConfigDetail[]
    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number
    @Prop(Number) operate: OperateTypeEnum
    /**
     * 是否冲算或智能学习
     */
    @Prop({
      type: Boolean,
      default: false
    })
    hasDisabled: boolean
    /**
     * 初始数据
     */
    @Prop({
      type: LearningType,
      default: () => new LearningType()
    })
    learningTypeModelCopy: LearningType
    @Inject() getLearningTypeModelCopy: () => LearningType
    OperateTypeEnum = OperateTypeEnum
    /**
     * 获取服务器时间响应式数据
     */
    serviceTime = ServiceTime

    /**
     * 表单校验
     */
    issueConfigDetailRules = {
      issueName: [
        { required: true, message: '请输入期别名称', trigger: 'blur' },
        { validator: this.vaNameDuplication, trigger: 'blur' }
      ],
      issueNo: [
        { required: true, message: '请输入2位数期别编号', trigger: 'blur' },
        { validator: this.vaNoDuplication, trigger: 'blur' }
      ],
      'checkDateRange.dateRange': [{ required: true, trigger: 'blur', validator: this.dateRangeValidator }],
      openRegistrationNum: [{ required: true, trigger: 'blur', validator: this.openRegistrationNumValidator }],
      registeredNumDisplayType: [{ required: true, trigger: 'change', validator: this.registeredNumDisplayValidator }],
      trainingPointId: [{ required: true, message: '请选择培训地点', trigger: 'change' }],
      isShowInPortal: [
        { required: true, message: '请选择展示用户', trigger: 'change', validator: this.isShowInPortalValidator }
      ],
      issueTrainingDateType: [{ required: true, trigger: 'change', validator: this.issueTrainingDateTypeValidator }],
      registerBeginTimeType: [{ required: true, trigger: 'change', validator: this.registerBeginTimeTypeValidator }],
      registerEndTimeType: [{ required: true, trigger: 'change', validator: this.registerEndTimeTypeValidator }],
      attendanceRate: [{ required: true, trigger: 'change', validator: this.attendanceRateValidator }],
      headTeacherName: [{ required: true, message: '请输入班主任姓名', trigger: 'blur' }],
      headTeacherPhone: [{ required: true, trigger: 'blur', validator: this.validatePhone }]
    }

    /**
     * 培训时段
     */
    trainingPeriod: string[] = []
    /**
     * 报名人数枚举
     * @type {RegisteredNumDisplayTypeEnum}
     */
    RegisteredNumDisplayTypeEnum = RegisteredNumDisplayTypeEnum
    /**
     * 开放学员报名字段 tips:状态层取反
     */
    isEnableStudentEnroll = false
    /**
     * 报名开启时间枚举
     * @type {BeginTimeTypeEnum}
     */
    BeginTimeTypeEnum = BeginTimeTypeEnum
    CourseStatusEnum = CourseStatusEnum
    /**
     * 报名关闭时间枚举
     * @type {any}
     */
    EndTimeTypeEnum = EndTimeTypeEnum
    /**
     * 考勤考核要求枚举
     * @type {AttendanceAssessRequireTypeEnum}
     */
    AttendanceAssessRequireTypeEnum = AttendanceAssessRequireTypeEnum
    /**
     * 当前激活锚点
     */
    activeAnchor = 'issue-base-info'
    /**
     * 滚动监听key
     *
     */

    scrollKey = true
    /**
     * 保存loading
     */
    saveLoading = false

    /**
     * 禁止切换培训时段
     */
    disableTrainingPeriod = false
    /**
     * 侧边标题
     */
    sideList = [
      { id: 'issue-base-info', text: '基础信息' },
      { id: 'issue-sign-up-info', text: '报名信息' },
      { id: 'issue-assessment-requirements', text: '考核要求' },
      { id: 'issue-contact-info', text: '联络信息' },
      { id: 'issue-notice', text: '学员须知' }
    ]
    trainingPlaceManage = new TrainingPlaceManage()
    checkDatePickerOptions = {}
    nameMap = new Map<string, string>()
    disabledDate(time: Date) {
      if (this.issueConfigDetail.operationType === OperationTypeEnum.update) {
        return time.getTime() < new Date(this.serviceTime.currentServiceTime).setHours(0, 0, 0, 0) // 8.64e7 是一天的毫秒数
      } else return false
    }

    get isOpenAttendanceDisabled() {
      return this.isShowIssueCourseHours
    }

    /**
     * 禁止切换培训时段
     */
    // get disableTrainingPeriod() {
    //   const transferBeginDate =
    //     this.issueConfigDetail.trainingDateRange.startDate &&
    //     this.$moment(this.serviceTime.currentServiceTime).isAfter(
    //       this.$moment(this.issueConfigDetail.trainingDateRange.startDate).startOf('day')
    //     )
    //   return this.isUpdate && transferBeginDate
    // }

    /**
     * 禁用期别时间
     */
    get disabledTrainingPeriod() {
      return {
        disabledDate: (time: any) => {
          // 当前日期
          const myDate = Date.now()
          return time.getTime() < myDate - 86400000
        }
      }
    }

    /**
     * 禁用结业测试
     */
    get disabledEndTest() {
      return this.issueConfigDetail.issueTrainingDateType == IssueTrainingDateTypeEnum.custom
    }
    /**
     * 是否本地初次新建期别
     */
    get isCreate() {
      return this.operate === OperateTypeEnum.CREATE
    }

    /**
     * 是否展示期别学时
     */
    get isShowIssueCourseHours() {
      console.log(this.issueConfigDetail.issueTrainingDateType, 'xxxx')
      return this.issueConfigDetail.issueTrainingDateType === IssueTrainingDateTypeEnum.custom
    }

    /**
     * 获取培训时段
     */
    get trainingDateRange() {
      return IssueTrainingDateType.list()
    }

    /**
     * 修改方案时 是否修改已存在的期别
     */
    get isUpdate() {
      return this.issueConfigDetail.operationType === OperationTypeEnum.update
    }

    get isDisabled() {
      return !!(
        this.isUpdate &&
        this.issueConfigDetail.registerBeginTime &&
        new Date(this.issueConfigDetail.registerBeginTime) < new Date(this.serviceTime.currentServiceTime)
      )
    }

    /**
     * 是否开启考勤变化
     */
    isOpenAttendanceChange(value: boolean) {
      if (!value) {
        this.issueConfigDetail.isOpenGraduationTest = true
      }
    }

    /**
     * 是否首门课开始前两小时内
     */
    isFirstCourse() {
      return (
        this.isUpdate &&
        this.issueConfigDetail.trainingDateRange.startDate &&
        new Date(this.issueConfigDetail.trainingDateRange.startDate).getTime() -
          new Date(this.serviceTime.currentServiceTime).getTime() <
          7200000
      )
    }
    /**
     * 展示门户改变
     */
    showPoralChange(value: boolean) {
      if (!value) {
        this.issueConfigDetail.visibleChannelList = []
      } else {
        this.issueConfigDetail.visibleChannelList = [1, 2]
      }
    }
    /**
     * 考勤考核要求改变
     */
    attendanceAssessRequireTypeChange() {
      if (this.issueConfigDetail.attendanceAssessRequireType === AttendanceAssessRequireTypeEnum.same_as_scheme) {
        this.assessmentForm.clearValidate('attendanceRate')
      }
    }

    /**
     * 培训时段
     */
    issueTrainingDateTypeValidator(rule: any, value: string, callback: Function) {
      if (value) {
        if (value === IssueTrainingDateTypeEnum.by_issue_courses) {
          callback()
        } else {
          if (this.issueConfigDetail.trainingDateRange.endDate && this.issueConfigDetail.trainingDateRange.startDate) {
            callback()
          } else {
            callback(new Error('请填写培训时段'))
          }
        }
      } else {
        callback(new Error('请填写培训时段'))
      }
    }
    /**
     * 开放学员报名时间校验
     */
    registerEndTimeTypeValidator(rule: any, value: string, callback: Function) {
      if (this.issueConfigDetail.registerEndTimeType !== EndTimeTypeEnum.no_end) {
        if (!this.issueConfigDetail.registerEndTime) {
          callback(new Error('请填写关闭报名时间'))
        }
      }
      callback()
    }
    attendanceRateValidator(rule: any, value: number, callback: Function) {
      if (value == 0) {
        callback(new Error('请输入大于0的数值！'))
      } else if (!value) {
        callback(new Error('请输入大于0的数值！'))
      } else {
        callback()
      }
    }
    /**
     * 开放学员报名时间校验
     */
    registerBeginTimeTypeValidator(rule: any, value: string, callback: Function) {
      if (this.issueConfigDetail.registerBeginTimeType !== BeginTimeTypeEnum.open_now) {
        if (!this.issueConfigDetail.registerBeginTime) {
          callback(new Error('请填写开启报名时间'))
        }
      }
      callback()
    }
    /**
     * 时间校验
     */
    dateRangeValidator(rule: any, value: string, callback: Function) {
      if (value[0] === '' || value[1] === '') {
        callback(new Error('时间填写不完整~'))
      } else {
        if (this.issueConfigDetail.operationType === OperationTypeEnum.create) {
          if (
            new Date(`${value[0].substr(0, 10)} 23:59:59`) <
            new Date(new Date(new Date().toLocaleDateString()).getTime() + 24 * 60 * 59 * 1000)
          ) {
            callback(new Error('培训报到时段不可配置过去时间，请修改！'))
          } else {
            callback()
          }
        }
        callback()
      }
    }
    async openRegistrationNumValidator(rule: any, value: number, callback: Function) {
      if (value) {
        if (this.issueConfigDetail.operationType === OperationTypeEnum.update) {
          const res = await new MutationCreateTrainClassCommodity().validateIssueSignUpNum({
            allowSignUpNum: value,
            schemeId: this.trainClassBaseInfo.id,
            issueId: this.issueConfigDetail.id
          })
          if (res.isSuccess()) callback()
          else if (res.code === 80001) callback(new Error('开放报名人数小于当前期别实际报名人数，请修改！'))
          else callback(new Error(res.message?.toString() || '校验失败'))
        }
        callback()
      } else {
        callback(new Error('请输入正整数！'))
      }
    }

    /**
     * 修改期别培训时段
     */
    dateTypeChange(val: IssueTrainingDateTypeEnum) {
      if (val == IssueTrainingDateTypeEnum.custom && this.issueConfigDetail.issueCourseList.length) {
        this.$confirm('修改培训时段选项，当前期别已创建的课表将清空，是否确认修改配置？', '提示', {
          confirmButtonText: '确定',
          showCancelButton: true,
          cancelButtonText: '取消',
          center: true
        })
          .then(() => {
            this.issueConfigDetail.isOpenAttendance = false
            this.issueConfigDetail.attendanceRate = null
            this.issueConfigDetail.isOpenGraduationTest = true
            this.issueConfigDetail.issueCourseList = new Array<IssueCourseDetail>()
            this.issueConfigDetail.trainingDateRange = new DateRange()
          })
          .catch(() => {
            this.issueConfigDetail.issueTrainingDateType = IssueTrainingDateTypeEnum.by_issue_courses
          })
      } else if (val == IssueTrainingDateTypeEnum.by_issue_courses) {
        this.issueConfigDetail.isOpenAttendance = true
      } else {
        if (this.isShowIssueCourseHours) {
          this.issueConfigDetail.isOpenAttendance = false
          this.issueConfigDetail.attendanceRate = null
          this.issueConfigDetail.isOpenGraduationTest = true
        }
      }
    }
    /**
     * 展示门户校验
     */
    isShowInPortalValidator(rule: any, value: number, callback: Function) {
      if (!value) {
        callback()
      } else {
        if (this.issueConfigDetail.visibleChannelList && this.issueConfigDetail.visibleChannelList.length > 0) {
          callback()
        } else {
          callback(new Error('请选择展示用户'))
        }
      }
    }
    /**
     * 已报名人数格式校验
     */
    registeredNumDisplayValidator(rule: any, value: number, callback: Function) {
      if (value === this.RegisteredNumDisplayTypeEnum.fixed_value) {
        if (this.issueConfigDetail.fixedRegisteredNum > 0) {
          callback()
        } else {
          callback(new Error('请输入正整数'))
        }
      }
      if (value === this.RegisteredNumDisplayTypeEnum.read_enrollment) {
        callback()
      }
      if (!value) {
        callback(new Error('请选择已报名人数'))
      }
    }
    /**
     * 验证期别名称是否重复
     */
    vaNameDuplication(rule: any, value: string, callback: Function) {
      if (this.issueConfigList.find((item) => item.issueName === value && item.id !== this.issueConfigDetail.id)) {
        callback(new Error('期别名称已存在，请修改~'))
      } else {
        callback()
      }
    }
    /**
     * 验证期别编号
     */
    async vaNoDuplication(rule: any, value: string, callback: Function) {
      const param = new CheckIssueNoAvailableParam()
      param.issueNo = value
      param.year = this.trainClassBaseInfo.skuProperty.year.skuPropertyValueId
      const regex = /^(0[1-9]|[1-9][0-9])$/
      if (!regex.test(value)) {
        callback(new Error('请输入两位数字的期别编号'))
      } else if (this.issueConfigList.find((item) => item.issueNo === value && item.id !== this.issueConfigDetail.id)) {
        callback(new Error('当前年度期别编号已存在，请重新输入！'))
      } else {
        if (
          this.routerMode === 3 &&
          this.learningTypeModelCopy?.issue.issueConfigList.some(
            (item) => item.issueNo === this.issueConfigDetail.issueNo
          )
        )
          return callback()
        const variety = await new MutationCreateTrainClassCommodity().judgeIssueNumExist(param)
        if (variety.status.isSuccess()) {
          if (variety.data) callback(new Error('当前年度期别编号已存在，请重新输入！'))
          else callback()
        } else {
          const message =
            variety.status.code === 70001
              ? '请配置年度校验期别编号是否重复'
              : variety.status.message?.toString() || '查询期别编号重复失败'
          this.$message.error(message)
          callback(new Error(message))
        }
      }
    }

    /**
     * 手机号校验
     */

    validatePhone(rule: any, headTeacherPhone: string, callback: Function) {
      const phoneRegex = /^1[3-9]\d{9}$/
      if (!headTeacherPhone) {
        callback(new Error('请输入班主任联系电话'))
      } else if (!phoneRegex.test(headTeacherPhone)) {
        callback(new Error('请输入正确的手机号'))
      } else {
        callback()
      }
    }
    async created() {
      if (this.isUpdate) {
        await this.serviceTime.getServiceTime()
        const transferBeginDate =
          this.issueConfigDetail.trainingDateRange.startDate &&
          this.$moment(this.serviceTime.currentServiceTime).isAfter(
            this.$moment(this.issueConfigDetail.trainingDateRange.startDate).startOf('day')
          )
        this.disableTrainingPeriod = this.isUpdate && transferBeginDate
      }
      this.isEnableStudentEnroll = !this.issueConfigDetail.isEnableStudentEnroll
      const startDate = this.issueConfigDetail.checkDateRange.startDate,
        endDate = this.issueConfigDetail.checkDateRange.endDate
      if (startDate) {
        this.issueConfigDetail.checkDateRange.startDate = this.$moment(startDate).format('YYYY-MM-DD') + ' 00:00:00'
      }
      if (endDate) {
        this.issueConfigDetail.checkDateRange.endDate = this.$moment(endDate).format('YYYY-MM-DD') + ' 23:59:59'
      }
      const page = new UiPage()
      page.pageSize = 200
      page.pageNo = 1
      this.trainingPlaceManage.queryList(page)
    }
    mounted() {
      this.checkDatePickerOptions = {
        disabledDate: this.disabledDate
      }
      // 监听滚动事件

      this.$nextTick(() => {
        document.querySelector('.scroll-anchor .el-drawer__body').addEventListener('scroll', this.onScroll)
      })
    }
    destroy() {
      // 必须移除监听器，不然当该vue组件被销毁了，监听器还在就会出错
      document.querySelector('.scroll-anchor .el-drawer__body').removeEventListener('scroll', this.onScroll)
    }
    // 滚动监听器
    onScroll() {
      // 获取所有锚点元素
      const navContents = document.querySelectorAll('.m-add-period .section')
      // 所有锚点元素的 offsetTop
      const offsetTopArr: any = []
      navContents.forEach((item: any) => {
        offsetTopArr.push(item.offsetTop)
      })
      // 获取当前文档流的 scrollTop
      const scrollTop = document.querySelector('.scroll-anchor .el-drawer__body').scrollTop + 80
      // 定义当前点亮的导航下标
      let navIndex = 0
      for (let n = 0; n < offsetTopArr.length; n++) {
        // 如果 scrollTop 大于等于第 n 个元素的 offsetTop 则说明 n-1 的内容已经完全不可见
        // 那么此时导航索引就应该是 n 了
        if (scrollTop >= offsetTopArr[n]) {
          navIndex = n
        }
      }
      // 把下标赋值给 vue 的 data
      if (this.scrollKey) {
        this.activeAnchor = navContents[navIndex].id
      } else {
        this.scrollKey = true
      }
    }
    getFormNameByProp() {
      if (this.nameMap.size) return
      const setNameMap = (form: any) => {
        form?.fields?.forEach((item: any) => {
          item.prop && this.nameMap.set(item.prop, item.label?.replace('：', ''))
        })
      }
      setNameMap(this.topForm)
      setNameMap(this.basicForm)
      setNameMap(this.signUpform)
      setNameMap(this.assessmentForm)
      setNameMap(this.contactForm)
    }
    /**
     * 保存
     */
    save(openCourse = false) {
      this.saveLoading = true
      Promise.all([
        this.topForm.validate(),
        this.basicForm.validate(),
        this.signUpform.validate(),
        this.assessmentForm.validate(),
        this.contactForm.validate()
      ])
        .then(async () => {
          if (!this.issueConfigDetail.isOpenAttendance && !this.issueConfigDetail.isOpenGraduationTest) {
            this.$message.warning('期别需至少开启一项考核（考勤或结业测试）！')
            return
          }
          if (this.isUpdate) {
            await this.serviceTime.getServiceTime()
            const issue = this.getLearningTypeModelCopy().issue.issueConfigList.find(
              (item) => item.id === this.issueConfigDetail.id
            )
            if (
              this.isFirstCourse() &&
              issue.attendanceRate != this.issueConfigDetail.attendanceRate &&
              this.issueConfigDetail.issueTrainingDateType !== IssueTrainingDateTypeEnum.custom
            ) {
              this.$message.warning('当前时间距离期别首门课程授课开始时间2小时内不支持修改')
              //this.scrollToSection('issue-assessment-requirements')
              //this.issueConfigDetail.attendanceRate = issue.attendanceRate
              return
            }
          }
          this.$emit('save', openCourse)
          this.showDrawer = false
        })
        .catch((e) => {
          this.getFormNameByProp()
          const key = Object.keys(e)[0]
          let value: any
          let flag = false
          key.split('.').forEach((item, index) => {
            if (index) {
              value = value?.[item]
            } else {
              value = this.issueConfigDetail[item]
            }
          })
          if (Array.isArray(value)) {
            flag = !!value.filter(Boolean).length
          } else {
            flag = !!value
          }
          if (key) {
            if (flag) this.$message.warning(e?.[key]?.[0]?.message)
            else this.$message.warning(`【${this.nameMap.get(key) || '必须填'}】还未配置，请先完成配置！`)
          } else {
            this.$message.warning('表单校验未通过！')
          }
        })
        .finally(() => {
          this.saveLoading = false
        })
    }

    /**
     * 培训点切换
     */
    trainingPointChange(val: string) {
      const trainingPlace = this.trainingPlaceManage.list.find((item) => item.id === val)
      if (trainingPlace) {
        this.issueConfigDetail.trainingPointName = trainingPlace.trainingPlaceName
        this.issueConfigDetail.trainingPointLng = trainingPlace.longitude.toString()
        this.issueConfigDetail.trainingPointLat = trainingPlace.latitude.toString()
      } else {
        this.issueConfigDetail.trainingPointName = ''
        this.issueConfigDetail.trainingPointLng = ''
        this.issueConfigDetail.trainingPointLat = ''
      }
    }
    scrollToSection(id: string): void {
      const element = document.getElementById(id)
      if (element) {
        // element.scrollIntoView({ behavior: 'smooth' })
        element.scrollIntoView()
      }
      this.scrollKey = false
      this.activeAnchor = id
    }

    isEnableStudentEnrollChange(val: boolean) {
      this.issueConfigDetail.isEnableStudentEnroll = !val
      this.issueConfigDetail.registerBeginTimeType = BeginTimeTypeEnum.open_now
      this.issueConfigDetail.registerBeginTime = ''
      this.issueConfigDetail.registerEndTimeType = EndTimeTypeEnum.no_end
      this.issueConfigDetail.registerEndTime = ''
    }
  }
</script>
