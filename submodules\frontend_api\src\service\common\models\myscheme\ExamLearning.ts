/**
 * 考试学习方式信息
 */
class ExamLearning {
  /**
   * 考试学习方式ID
   */
  learningId: string
  /**
   * 场次ID
   */
  examRoundId = ''
  /**
   * 试卷ID
   */
  examPaperId = ''
  /**
   * 要求考试分数
   */
  passScore: number
  /**
   * 开考时间
   */
  beginTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 考试次数 0表示不限制次数
   */
  examCount: number
  /**
   * 考试时长
   */
  examTimeLength: number
  /**
   * 场次名称
   */
  name: string
  /**
   * 是否存在考试要求分数
   */
  hasPassScore() {
    return !!this.passScore
  }
}

export default ExamLearning
