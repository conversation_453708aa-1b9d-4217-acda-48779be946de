import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 【集体报名订单】子单售后状态枚举
 */
export enum BatchOrderSubOrderAfterSaleStatusEnum {
  // 未发起退款
  Wait_For_Refund = 1,
  // 退款审批中
  Refund_Approval_In_Process,
  // 退款处理中
  Refund_In_Process,
  // 退款成功
  Refund_Success,
  // 退款失败
  Refund_Fail,
  // 取消退款
  Cancel_Refund,
  // 拒绝退款申请
  Rejected
}

class BatchOrderSubOrderAfterSaleStatus extends AbstractEnum<BatchOrderSubOrderAfterSaleStatusEnum> {
  static enum = BatchOrderSubOrderAfterSaleStatusEnum

  constructor(status?: BatchOrderSubOrderAfterSaleStatusEnum) {
    super()
    this.current = status
    this.map.set(BatchOrderSubOrderAfterSaleStatusEnum.Wait_For_Refund, '未发起退款')
    this.map.set(BatchOrderSubOrderAfterSaleStatusEnum.Refund_Approval_In_Process, '退款审批中')
    this.map.set(BatchOrderSubOrderAfterSaleStatusEnum.Refund_In_Process, '退款处理中')
    this.map.set(BatchOrderSubOrderAfterSaleStatusEnum.Refund_Success, '退款成功')
    this.map.set(BatchOrderSubOrderAfterSaleStatusEnum.Refund_Fail, '退款失败')
    this.map.set(BatchOrderSubOrderAfterSaleStatusEnum.Cancel_Refund, '取消退款')
    this.map.set(BatchOrderSubOrderAfterSaleStatusEnum.Rejected, '拒绝退款申请')
  }
}

export default BatchOrderSubOrderAfterSaleStatus
