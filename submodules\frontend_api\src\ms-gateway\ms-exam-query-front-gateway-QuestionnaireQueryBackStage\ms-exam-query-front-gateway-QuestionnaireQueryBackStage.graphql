"""独立部署的微服务,K8S服务名:ms-exam-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询当前网校下调查问卷内试题作答统计
		@param request
		@return
	"""
	getQuestionnaireAnswerStaitsticsInServicer(request:QuestionnaireAnswerStaitsticsRequest):QuestionnaireAnswerStaitsticsResponse
	"""根据用户答卷id查询答卷作答详情"""
	getQuestionnaireDetailInServicer(request:GetQuestionnaireDetailRequest):AnswerPaperResponse
	"""根据问卷模板id获取问卷模板详情
		@param request
		@return
	"""
	getQuestionnaireTemplateDetailInServicer(request:QuestionnaireTemplateDetailRequest):QuestionnairePaperPublishConfigureResponse
	"""分页获取期别下的调查问卷列表"""
	pageIssueQuestionnaireInServicer(page:Page,request:GetQuestionnaireInIssueRequest):SurveyInformationResponsePage @page(for:"SurveyInformationResponse")
	"""分页获取期别下的调查问卷统计"""
	pageIssueQuestionnaireStatisticsInServicer(page:Page,request:GetQuestionnaireInIssueRequest):QuestionnaireInLearningSchemeResponsePage @page(for:"QuestionnaireInLearningSchemeResponse")
	"""分页查询当前网校下调查问卷内试题作答信息
		@param page
		@param request
		@return
	"""
	pageQuestionnaireAnswerContentInServicer(page:Page,request:QuestionnaireAnswerContentRequest):QuestionnaireAnswerContentResponsePage @page(for:"QuestionnaireAnswerContentResponse")
	"""分页查询当前网校下调查问卷的用户作答信息
		@param page
		@param request
		@return
	"""
	pageQuestionnaireAnswerInServicer(page:Page,request:QuestionnaireAnswerRequest):QuestionAnswerResponsePage @page(for:"QuestionAnswerResponse")
	"""分页查询方案下学员调查问卷信息"""
	pageQuestionnaireSchemeInServicer(page:Page,request:QuestionnaireDetailInLearningSchemeRequest):SurveyQuestionnaireResponsePage @page(for:"SurveyQuestionnaireResponse")
	"""分页查询当前网校下的问卷模板信息
		@param request
		@return
	"""
	pageQuestionnaireTemplateInSchool(page:Page,request:QuestionnaireTemplateRequest):QuestionnaireTemplateResponsePage @page(for:"QuestionnaireTemplateResponse")
	"""分页获取学习方案下的调查问卷统计
		@param request
		@return
	"""
	pageSchemeQuestionnaireStatisticsInServicer(page:Page,request:GetQuestionnaireInSchemeRequest):QuestionnaireInLearningSchemeResponsePage @page(for:"QuestionnaireInLearningSchemeResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""根据用户答卷id查询答卷作答详情"""
input GetQuestionnaireDetailRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.GetQuestionnaireDetailRequest") {
	"""用户答卷id"""
	id:String
}
input GetQuestionnaireInIssueRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.GetQuestionnaireInIssueRequest") {
	"""期别id"""
	issueId:String
	"""调查问卷名称（模糊搜索）"""
	questionnaireName:String
	"""问卷类型
		1.普通问卷
		2.量表问卷
	"""
	type:Int
}
input GetQuestionnaireInSchemeRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.GetQuestionnaireInSchemeRequest") {
	"""学习方案id"""
	schemeId:String
	"""调查问卷名称（模糊搜索）"""
	questionnaireName:String
	"""问卷类型
		1.普通问卷
		2.量表问卷
	"""
	type:Int
}
"""分页查询当前网校下调查问卷内试题作答信息"""
input QuestionnaireAnswerContentRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.QuestionnaireAnswerContentRequest") {
	"""调查问卷id"""
	questionnaireId:String
	"""试题id"""
	questionId:String
}
"""分页查询当前网校下调查问卷的用户作答信息"""
input QuestionnaireAnswerRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.QuestionnaireAnswerRequest") {
	"""调查问卷id"""
	questionnaireId:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phoneNum:String
	"""姓名（精确）"""
	name:String
	"""提交时间-起"""
	startSubmitTime:DateTime
	"""提交时间-止"""
	endSubmitTime:DateTime
}
"""分页查询方案下学员调查问卷信息"""
input QuestionnaireAnswerStaitsticsRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.QuestionnaireAnswerStaitsticsRequest") {
	"""调查问卷id"""
	questionnaireId:String
}
"""分页查询方案下学员调查问卷信息"""
input QuestionnaireDetailInLearningSchemeRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.QuestionnaireDetailInLearningSchemeRequest") {
	"""学号"""
	stuNo:String
}
input QuestionnaireTemplateDetailRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.QuestionnaireTemplateDetailRequest") {
	"""模版id"""
	templateId:String
}
input QuestionnaireTemplateRequest @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.request.QuestionnaireTemplateRequest") {
	"""学习方案id"""
	schemeId:String
	"""出卷配置id"""
	ids:[String]
	"""调查问卷名称（模糊搜索）"""
	questionnaireName:String
	"""问卷类型
		1.普通问卷
		2.量表问卷
	"""
	type:Int
	"""创建的开始时间"""
	createStartTime:DateTime
	"""创建的结束时间"""
	createEndTime:DateTime
	"""问卷状态
		1 草稿
		2 已发布
	"""
	answerPaperStatus:Int!
	"""是否被方案引用"""
	isReferenced:Boolean
}
"""试题类型"""
enum QuestionTypeEnum @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.enums.QuestionTypeEnum") {
	"""单选题"""
	RADIO
	"""多选题"""
	MULTIPLE
	"""填空题"""
	FILL
	"""判断题"""
	OPINION
	"""简答题"""
	ASK
	"""父子题"""
	FATHER
	"""量表题"""
	SCALE
}
"""@Description （跟底层返回的一致）答卷信息
	<AUTHOR>
	@Date 15:19 2022/2/24
"""
type AnswerPaperResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.AnswerPaperResponse") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位id"""
	unitId:String
	"""所属服务商id"""
	servicerId:String
	"""id"""
	id:String
	"""答卷状态 发布中 1  取消发布 2  已发布 3"""
	status:Int!
	"""试卷作答状态 未开始作答 -1  作答中0 交卷中 1 已交卷 2"""
	answerStatus:Int!
	"""试卷阅卷状态  未交卷-1 阅卷中 0 阅卷完成 1"""
	markStatus:Int!
	"""用户答卷评定结果常量 未评定 -1  无评定结果 0 合格 1 不合格 2"""
	evaluateResult:Int!
	"""场景类型"""
	sceneType:Int!
	"""场景id"""
	sceneId:String
	qualificationId:String
	userId:String
	"""答题数"""
	answerCount:Int!
	"""开始作答时间"""
	beginAnswerTime:DateTime
	"""创建时间"""
	createdTime:DateTime
	"""发布时间"""
	publishedTime:DateTime
	"""交卷时间"""
	handingTime:DateTime
	"""交卷完成时间"""
	handedTime:DateTime
	"""阅卷开始时间"""
	markingTime:DateTime
	"""阅卷完成时间"""
	markedTime:DateTime
	"""发布时间"""
	pusblishedTime:DateTime
	"""考试时长"""
	timeLength:Int!
	"""阅卷人ID"""
	markUserId:String
	"""得分，-1表示不是为分数评定方式"""
	score:Double!
	"""正确题数，-1表示试题不进行评定"""
	correctCount:Int!
	"""错误题数，-1表示试题不进行评定"""
	incorrectCount:Int!
	studentNo:String
	name:String
	description:String
	"""总分"""
	totalScore:Double!
	cancelReason:String
	createUserId:String
	systemHanded:Boolean!
	groups:[QuestionGroup]
	"""试题"""
	questions:[Question]
	"""答卷时长"""
	answerTimeLength:Int!
}
"""试题作答统计"""
type AnswerStaitsticsResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.AnswerStaitsticsResponse") {
	"""试题id"""
	questionId:String
	"""试题类型
		@see QuestionTypes
	"""
	questionType:Int!
	"""作答总人数"""
	totalCount:Int!
	"""是否为教师评价题"""
	isTeacherQuestion:Boolean
	"""作答统计"""
	statisticsAnswerContents:[StatisticsAnswerContentResponse]
	"""题目标识"""
	tag:String
	"""原试题id"""
	originalQuestionId:String
}
"""出卷配置分类主题模型"""
type PaperPublishConfigureCategoryResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.PaperPublishConfigureCategoryResponse") {
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""出卷配置分类ID"""
	id:String
	"""分类名称"""
	name:String
	"""父级分类id"""
	parentCategory:PaperPublishConfigureCategoryResponse
	"""排序"""
	sort:Int!
	"""创建人id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
}
"""分页查询当前网校下调查问卷的用户作答信息"""
type QuestionAnswerResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.QuestionAnswerResponse") {
	"""调查问卷id"""
	questionnaireId:String
	"""答卷id"""
	answerPaperId:String
	"""期别id(只有期别问卷有"""
	issueId:String
	"""姓名"""
	name:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phoneNum:String
	"""提交时间"""
	submitTime:DateTime
}
"""分页查询当前网校下调查问卷的用户作答信息"""
type QuestionnaireAnswerContentResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.QuestionnaireAnswerContentResponse") {
	"""调查问卷id"""
	questionnaireId:String
	"""试题id"""
	questionId:String
	"""答案文本"""
	answer:String
	"""提交时间"""
	submitTime:DateTime
}
"""查询当前网校下调查问卷内试题作答统计"""
type QuestionnaireAnswerStaitsticsResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.QuestionnaireAnswerStaitsticsResponse") {
	"""调查问卷信息"""
	surveyInformationResponse:SurveyInformationResponse
	"""调查问卷总作答人数"""
	totalAnswerNum:Int!
	"""试题作答统计"""
	answerStaitsticsList:[AnswerStaitsticsResponse]
}
"""调查问卷"""
type QuestionnaireInLearningSchemeResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.QuestionnaireInLearningSchemeResponse") {
	"""调查问卷信息"""
	surveyInformationResponse:SurveyInformationResponse
	"""作答人数"""
	num:Int
}
"""出卷配置主题模型"""
type QuestionnairePaperPublishConfigureResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.QuestionnairePaperPublishConfigureResponse") {
	"""问卷类型
		1 普通问卷
		2 量表问卷
	"""
	type:Int!
	"""是否被引用"""
	isReferenced:Boolean
	"""数据归属信息"""
	dataBelong:DataBelongModel
	"""出卷配置ID"""
	id:String
	"""出卷配置名称"""
	name:String
	"""数据归属信息"""
	paperPublishConfigureCategory:PaperPublishConfigureCategoryResponse
	"""创建人Id"""
	createUserId:String
	"""创建时间"""
	createdTime:DateTime
	"""出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2"""
	paperPublishPatterns:Int
	"""出卷模式"""
	publishPattern:PublishPattern
	"""是否启用 1 启用 2禁用"""
	status:Int
	"""适用范围 用于筛选自定义的分类"""
	usageScope:Int
	"""是否是草稿 1是  2不是"""
	isDraft:Int
}
type QuestionnaireTemplateResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.QuestionnaireTemplateResponse") {
	"""问卷模板id"""
	id:String
	"""问卷名称"""
	name:String
	"""问卷类型
		1普通问卷
		2量表问卷
	"""
	type:Int
	"""创建时间"""
	createdTime:DateTime
	"""是否启用 1 启用 2禁用"""
	status:Int
	"""是否是草稿 1是  2不是（2代表已发布）"""
	isDraft:Int
	"""是否被引用"""
	isReferenced:Boolean
}
type StatisticsAnswerContentResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.StatisticsAnswerContentResponse") {
	"""作答项"""
	answerItem:String
	"""选项选择人数"""
	chooseCount:Int!
}
type SurveyQuestionnaireResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.SurveyQuestionnaireResponse") {
	"""调查问卷信息"""
	surveyInformationResponse:SurveyInformationResponse
	"""用户答卷基本信息"""
	basicInformationofUserResponse:BasicInformationofUserResponse
}
"""数据归属模型"""
type DataBelongModel @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.common.DataBelongModel") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位id"""
	unitId:String
	"""所属服务商id"""
	servicerId:String
}
"""智能卷出卷模式"""
type AutomaticPublishPattern implements PublishPattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.AutomaticPublishPattern") {
	"""建议作答时长"""
	suggestionTimeLength:Int!
	"""抽题规则"""
	questionExtractRule:QuestionExtractRule
	"""评定方式"""
	evaluatePattern:EvaluatePattern
	"""出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2"""
	type:Int
}
"""固定卷出卷模式"""
type FixedPaper implements PublishPattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.FixedPaper") {
	"""试卷id"""
	id:String
	"""试卷名称"""
	name:String
	"""试卷描述"""
	description:String
	"""作答时长"""
	timeLength:Int!
	"""试卷总分"""
	totalScore:Double!
	"""大题集合"""
	groups:[QuestionGroup]
	"""试题集合"""
	questions:[PaperQuestion]
	"""出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2"""
	type:Int
}
"""AB出卷模式"""
type MultipleFixedPaperPublishPattern implements PublishPattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.MultipleFixedPaperPublishPattern") {
	"""固定卷集合"""
	fixedPapers:[FixedPaper]
	"""出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2"""
	type:Int
}
"""<AUTHOR> create 2021/6/3 17:35"""
type PaperQuestion @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.PaperQuestion") {
	"""试题ID"""
	questionId:String
	"""分数，-1表示不为分数评定方式为"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""是否必答"""
	answerRequired:Boolean!
}
"""@Description 初级模式
	<AUTHOR>
	@Date 9:14 2022/3/1
"""
interface PublishPattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.PublishPattern") {
	"""出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2"""
	type:Int
}
"""试卷大题信息
	<AUTHOR>
"""
type QuestionGroup @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.QuestionGroup") {
	sequence:Int!
	questionType:Int!
	groupName:String
	eachQuestionScore:Double!
}
"""正确率评定方式
	<AUTHOR>
"""
type CorrectRateEvaluatePattern implements EvaluatePattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.CorrectRateEvaluatePattern") {
	"""要求答题总数"""
	answerQuestionCount:Int!
	"""合格正确率"""
	qualifiedCorrectRate:Double!
	"""评定方式类型"""
	type:Int
}
"""评定方式基类
	<AUTHOR>
"""
interface EvaluatePattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.EvaluatePattern") {
	"""评定方式类型"""
	type:Int
}
"""无评定方式
	<AUTHOR>
"""
type NoneEvaluatePattern implements EvaluatePattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.NoneEvaluatePattern") {
	"""评定方式类型"""
	type:Int
}
"""@Description试题指定分值设置
	<AUTHOR>
	@Date 15:15 2022/3/3
"""
type QuestionMapScoreSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.QuestionMapScoreSetting") {
	"""试题id"""
	questionId:String
	"""分数"""
	score:Double!
}
"""试题分数设置信息
	<AUTHOR>
"""
type QuestionScoreSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.QuestionScoreSetting") {
	"""大题序号"""
	sequence:Int
	"""试题类型"""
	questionType:Int
	"""每题平均分"""
	eachQuestionScore:Double
	"""具体试题分数"""
	questionScores:[QuestionMapScoreSetting]
}
"""分数评定方式
	<AUTHOR>
"""
type ScoreEvaluatePattern implements EvaluatePattern @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.evaluate.ScoreEvaluatePattern") {
	"""总分"""
	totalScore:Double
	"""合格分数"""
	qualifiedScore:Double
	"""每道试题分数"""
	questionScores:[QuestionScoreSetting]
	"""多选题漏选得分模式"""
	multipleMissScorePattern:Int
	"""评定方式类型"""
	type:Int
}
"""试题抽题规则
	<AUTHOR>
"""
type QuestionExtractRule @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.QuestionExtractRule") {
	"""试题总数"""
	questionCount:Int!
	"""出题范围"""
	questionScopes:[QuestionScopeSetting]
	"""出题描述"""
	questionExtracts:[QuestionExtractSetting]
}
"""试题抽题设置信息
	<AUTHOR>
"""
type QuestionExtractSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.QuestionExtractSetting") {
	"""试题类型"""
	questionType:Int!
	"""大题序号"""
	sequence:Int
	"""大题名称"""
	groupName:String
	"""试题数"""
	questionCount:Int!
	"""出题范围"""
	questionScopes:[QuestionScopeSetting]
}
"""出题范围设置基类
	<AUTHOR>
"""
interface QuestionScopeSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.QuestionScopeSetting") {
	"""出题类型"""
	type:Int
}
"""题库出题配置
	<AUTHOR> create 2021/8/19 11:18
"""
type LibraryFixedQuestionScopeSetting implements QuestionScopeSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.scopes.LibraryFixedQuestionScopeSetting") {
	"""题库对应试题数设置对象"""
	libraryMapQuestionNumSettings:[LibraryMapQuestionNumSetting]
	"""出题类型"""
	type:Int
}
"""@Description 题库对应试题数设置对象
	<AUTHOR>
	@Date 14:20 2022/3/3
"""
type LibraryMapQuestionNumSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.scopes.LibraryMapQuestionNumSetting") {
	"""题库id"""
	libraryId:String
	"""试题数量"""
	questionNum:Int
}
"""题库出题配置
	<AUTHOR> create 2021/8/19 11:18
"""
type LibraryQuestionScopeSetting implements QuestionScopeSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.scopes.LibraryQuestionScopeSetting") {
	"""题库id集合"""
	libraryIds:[String]
	"""出题类型"""
	type:Int
}
"""用户课程抽题维度
	<AUTHOR> create 2021/11/26 13:55
"""
type UserCourseScopeSetting implements QuestionScopeSetting @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.paper.extract.scopes.UserCourseScopeSetting") {
	"""课程来源
		@see UserCourseSources
	"""
	userCourseSource:Int!
	"""要求的组卷信息key.
		当{@link #userCourseSource} = 用户课程题库时需要指定
		@see ExtractionMessageKeys
	"""
	requireKeys:[String]
	"""出题类型"""
	type:Int
}
"""@Description
	<AUTHOR>
	@Date 17:13 2022/3/9
"""
type Answer @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.Answer") {
	key:Int
	"""答案"""
	answer:String
}
"""@Description 问答题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type AskQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.AskQuestion") {
	"""答案"""
	askAnswer:String
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""@Description 父子题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type FatherQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.FatherQuestion") {
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""@Description 填空题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type FillQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.FillQuestion") {
	"""答案"""
	fillAnswer:[Answer]
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""@Description 多选题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type MultipleQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.MultipleQuestion") {
	"""答案"""
	multipleAnswer:[String]
	"""填空的内容"""
	fillContents:[MultipleQuestionFillContent]
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
type MultipleQuestionFillContent @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.MultipleQuestionFillContent") {
	"""选项id"""
	id:String
	"""填空内容"""
	fillContent:String
}
"""@Description 判断题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type OpinionQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.OpinionQuestion") {
	"""答案"""
	opinionAnswer:Boolean
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""@Description 试题
	<AUTHOR>
	@Date 16:07 2022/2/28
"""
interface Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.Question") {
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""@Description 单选题和底层一样
	<AUTHOR>
	@Date 15:40 2022/2/28
"""
type RadioQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.RadioQuestion") {
	"""答案"""
	radioAnswer:String
	"""需要填空的内容"""
	fillContent:String
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""@Description 量表题
	<AUTHOR>
	@Date 15:48 2022/2/28
"""
type ScaleQuestion implements Question @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.question.ScaleQuestion") {
	"""答案"""
	answer:Int!
	"""试题类型"""
	type:QuestionTypeEnum
	"""试题ID"""
	questionId:String
	groupSequence:Int
	"""总分"""
	score:Double
	answeredTime:DateTime
	"""得分"""
	givenScore:Double
	evaluateResult:Int
	evaluateMode:Int
	"""试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int
	"""是否必答
		true 必答
		false 选答
	"""
	answerRequired:Boolean!
	"""题目标签"""
	tag:String
}
"""用户答卷基本信息"""
type BasicInformationofUserResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.questionnaire.BasicInformationofUserResponse") {
	"""答卷id"""
	answerPaperId:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""服务商ID"""
	servicerId:String
	"""单位ID"""
	unitId:String
	"""平台租户id"""
	tenantId:String
	"""作答用户类型 1-学员"""
	userType:Int
	"""作答用户id"""
	userId:String
	"""学员学号"""
	studentNo:String
	"""学员参训资格id"""
	qualificationId:String
	"""学习方案id"""
	schemeId:String
	"""期别id"""
	issueId:String
	"""应用场景类型 1-考试 2-练习 3-课后测验 4-问卷调查"""
	sceneType:Int
	"""场景id"""
	sceneId:String
	"""试卷名称"""
	name:String
	"""试卷描述"""
	description:String
	"""作答时长 单位秒 -1表示没有时长限制"""
	timeLength:Int
	"""试卷总分"""
	totalScore:BigDecimal
	"""大题信息"""
	questionGroups:String
	"""答卷状态 1发布中 2.取消发布 3.已发布 4.已作废"""
	answerPaperStatus:Int
	"""取消发布原因"""
	publicCanceledReason:String
	"""答卷作答状态 -1未开始 0.作答中 1.交卷中 2.已交卷"""
	answerStatus:Int
	"""开始作答时间"""
	beginAnswerTime:DateTime
	"""完成作答时间"""
	endAnswerTime:DateTime
	"""答卷发布时间"""
	publishedTime:DateTime
	"""答卷取消发布时间"""
	cancelTime:DateTime
	"""答卷作废时间"""
	invalidTime:DateTime
	"""阅卷完成时间"""
	markedTime:DateTime
	"""阅卷开始时间"""
	markingTime:DateTime
	"""重新阅卷时间"""
	remarkTime:DateTime
	"""是否系统强制交卷 0否 1是"""
	isSystemCommitted:Int
	"""答卷阅卷状态 -1未交卷 0阅卷中 1阅卷完成"""
	markStatus:Int
	"""阅卷人id"""
	markUserId:String
	"""合格分"""
	qualifiedScore:BigDecimal
	"""得分"""
	score:BigDecimal
	"""评定结果 -1未评定 0无评定结果 1合格 2不合格"""
	evaluateResult:Int
	"""答题总数"""
	answerCount:Int
	"""正确题目数"""
	correctCount:Int
	"""错误题目数"""
	incorrectCount:Int
	"""是否系统评定 0否 1是"""
	IsSystemHanded:Int
	"""是否重新阅卷 0否 1是"""
	isRemark:Int
	"""用户答卷创建时间"""
	createdTime:DateTime
}
"""前置条件"""
type PreconditionResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.questionnaire.PreconditionResponse") {
	"""前置条件名称"""
	name:String
	"""前置条件表达式"""
	expression:String
	"""学习方式id"""
	learningId:String
}
"""调查问卷信息"""
type SurveyInformationResponse @type(value:"com.fjhb.ms.exam.front.gateway.jxjy.gateway.graphql.response.nested.questionnaire.SurveyInformationResponse") {
	"""问卷id"""
	questionnaireId:String
	"""问卷模版id"""
	templateId:String
	"""方案id"""
	schemeId:String
	"""拥有者id"""
	ownerId:String
	"""试卷名称"""
	questionnaireName:String
	"""应用范围
		0-SCHEME 1-ONLINECOURSES 2-TRAININGSTAGE
	"""
	usedRange:Int!
	"""是否纳入考核"""
	includedInAssessment:Boolean!
	"""是否强制完成"""
	forceToComplete:Boolean!
	"""问卷类型"""
	type:Int
	"""创建时间"""
	createTime:DateTime
	"""问卷开放时间 起"""
	questionnaireStartTime:DateTime
	"""问卷开放时间 止"""
	questionnaireEndTime:DateTime
	"""问卷状态
		启用 1
		停用 2
		@see com.fjhb.domain.exam.api.questionnaire.consts.QuestionnaireStatus
	"""
	status:Int
	"""是否被引用"""
	isReferenced:Boolean
	"""问卷描述"""
	description:String
	"""学习方式id"""
	learningId:String
	"""前置条件"""
	precondition:PreconditionResponse
	"""是否开放结果
		true-开放
		false-不开放
	"""
	openResults:Boolean
}

scalar List
type SurveyInformationResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SurveyInformationResponse]}
type QuestionnaireInLearningSchemeResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [QuestionnaireInLearningSchemeResponse]}
type QuestionnaireAnswerContentResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [QuestionnaireAnswerContentResponse]}
type QuestionAnswerResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [QuestionAnswerResponse]}
type SurveyQuestionnaireResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SurveyQuestionnaireResponse]}
type QuestionnaireTemplateResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [QuestionnaireTemplateResponse]}
