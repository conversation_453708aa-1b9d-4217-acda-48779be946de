<route-params content="/:schoolId"></route-params>
<template>
  <div v-if="$hasPermission('modifyQuery')" desc="查询" actions="created">
    <el-main>
      <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb10">
        配置提示：<br />
        1.网校可修改内容，修改后信息会同步影响到当前已应用的网校，若网校已生成相应数据的信息则不影响，自修改后同步更新。<br />
        2.修改网校请慎重。
      </el-alert>
      <!--顶部tab标签-->
      <el-tabs v-model="activeName" class="m-tab-top is-sticky">
        <el-tab-pane label="基础信息" name="first">
          <div class="f-p15">
            <template v-if="$hasPermission('BasicInfo')" desc="修改基础信息" actions="@BasicInfo">
              <basic-info
                :baseData="schoolDetail.schoolBase"
                :id="schoolDetail.id"
                :addData="schoolDetail.addServiceConfig"
              ></basic-info>
            </template>
          </div>
        </el-tab-pane>
        <el-tab-pane label="网校配置" name="second" :lazy="true">
          <div class="f-p15">
            <template v-if="$hasPermission('SchoolConfig')" desc="修改网校配置" actions="@SchoolConfig">
              <school-config :schoolConfigData="schoolDetail.schoolConfig" :id="schoolDetail.id"></school-config>
            </template>
          </div>
        </el-tab-pane>
        <el-tab-pane label="模板配置" name="third">
          <div class="f-p15">
            <template v-if="$hasPermission('TemplateConfig')" desc="修改模板配置" actions="@TemplateConfig">
              <template-config :schoolDetail="schoolDetail" @update="updateTemplateId"></template-config>
            </template>
          </div>
        </el-tab-pane>
        <el-tab-pane label="管理员信息" name="fourth">
          <div class="f-p15">
            <template v-if="$hasPermission('ManageAccount')" desc="修改管理员" actions="@ManageAccount">
              <!-- :administratorList="schoolDetail.administratorList" -->
              <manage-account :schoolId="$route.params.schoolId"></manage-account>
            </template>
          </div>
        </el-tab-pane>
        <el-tab-pane label="增值服务" name="fifth">
          <div class="f-p15">
            <template v-if="$hasPermission('AddService')" desc="增值服务" actions="@AddService">
              <add-service
                :addData="schoolDetail.addServiceConfig"
                :baseData="schoolDetail.schoolBase"
                :id="schoolDetail.id"
              ></add-service>
            </template>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-main>
  </div>
</template>
<script lang="ts">
  import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
  import LearningRuleList from '@api/service/management/learning-rule/LearningRuleList'
  import { AddServiceEnum } from '@api/service/training-institution/online-school/enum/AddServiceEnum'
  import OnlineSchoolModel from '@api/service/training-institution/online-school/models/OnlineSchoolModel'
  import OnlineSchoolModule from '@api/service/training-institution/online-school/OnlineSchoolModule'
  import { isBoolean } from 'lodash'
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import AddService from '@hbfe/jxjy-admin-management/src/components/add-service.vue'
  import BasicInfo from '@hbfe/jxjy-admin-management/src/components/basic-info.vue'
  import ManageAccount from '@hbfe/jxjy-admin-management/src/components/manage-account.vue'
  import SchoolConfig from '@hbfe/jxjy-admin-management/src/components/school-config.vue'
  import TemplateConfig from '@hbfe/jxjy-admin-management/src/components/template-config.vue'
  @Component({
    components: { BasicInfo, SchoolConfig, TemplateConfig, ManageAccount, AddService }
  })
  export default class extends Vue {
    // @Watch('activeName')
    // watchValue(val?: any) {
    //   // 防止基础信息和网校配置修改关联数据变更
    //   if (val == 'third') {
    //     this.handleLoadData()
    //   }
    // }
    activeName = 'first'
    schoolDetail: OnlineSchoolModel = new OnlineSchoolModel()
    IntelligenceLearningModule: IntelligenceLearningModule = new IntelligenceLearningModule()
    LearningRuleModule: LearningRuleList = new LearningRuleList()
    async created() {
      await this.handleLoadData()
    }
    async handleLoadData() {
      const onlineSchoolObj = new OnlineSchoolModule()
      onlineSchoolObj.onlineSchool = new OnlineSchoolModel()
      onlineSchoolObj.onlineSchool.schoolId = this.$route.params.schoolId
      this.schoolDetail = await onlineSchoolObj.queryDetail()
      const IntelligenceLearning = await this.IntelligenceLearningModule.doQueryServiceConfigByServicerId(
        this.$route.params.schoolId
      )

      //   获取学习规则是否开启
      const LearningRule = await this.LearningRuleModule.queryRuleConfigByServicerId(this.$route.params.schoolId)
      //   获取分销服务是否开启
      const fxService = await onlineSchoolObj.queryDistributionService()
      this.schoolDetail.addServiceConfig.distributionServiceType = fxService.data.distributionServiceType

      this.schoolDetail.addServiceConfig.addServiceType = []
      if (IntelligenceLearning == 1) {
        this.schoolDetail.addServiceConfig.addServiceType.push(AddServiceEnum.intelligentlearning)
      }
      if (LearningRule.status && isBoolean(LearningRule.status)) {
        this.schoolDetail.addServiceConfig.addServiceType.push(AddServiceEnum.learningRule)
      }
      if (fxService.data.distributionService) {
        this.schoolDetail.addServiceConfig.addServiceType.push(AddServiceEnum.fxService)
      }
    }
    updateTemplateId(detail: OnlineSchoolModel) {
      this.schoolDetail.schoolConfig.H5PortalTemplateId = detail.schoolConfig.H5PortalTemplateId
      this.schoolDetail.schoolConfig.webPortalTemplateId = detail.schoolConfig.webPortalTemplateId
    }
  }
</script>
