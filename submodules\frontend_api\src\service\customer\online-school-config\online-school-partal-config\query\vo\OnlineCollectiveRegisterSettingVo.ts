import { OnlineCollectiveRegisterConfigResponse } from '@api/ms-gateway/ms-servicer-series-v1'

/**
 * @description 线上集体报名配置
 */
class OnlineCollectiveRegisterSettingVo {
  /**
   * 线上集体报名配置开启状态 停用 启用
   */
  enable: boolean

  /**
   * 是否启用报名入口图片
   */
  enterPhotoEnable: boolean = undefined

  /**
   * 报名入口图片
   */
  enterPhoto = ''

  static from(response: OnlineCollectiveRegisterConfigResponse) {
    const onlineCollectiveRegisterSettingVo = new OnlineCollectiveRegisterSettingVo()
    onlineCollectiveRegisterSettingVo.enable = response?.enabled
    onlineCollectiveRegisterSettingVo.enterPhotoEnable = response?.enabledPicture
    onlineCollectiveRegisterSettingVo.enterPhoto =
      (response?.registerPortalPicture?.length && response?.registerPortalPicture[0].url) || ''
    return onlineCollectiveRegisterSettingVo
  }
}
export default OnlineCollectiveRegisterSettingVo
