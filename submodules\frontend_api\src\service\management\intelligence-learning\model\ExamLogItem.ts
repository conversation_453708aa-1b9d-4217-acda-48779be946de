import LearningResulType, {
  LearningResultEnum
} from '@api/service/management/intelligence-learning/enum/LearningResultEnum'
import { ExamLearningDetailResponse } from '@api/ms-gateway/ms-autolearning-log-v1'

export default class ExamLogItem {
  /**
   * 考试场次名称
   */
  examName = ''
  /**
   * 考试开始时间
   */
  examBeginTime = ''
  /**
   * 考试结束时间
   */
  examEndTime = ''
  /**
   * 考试完成情况
   */
  examResult = new LearningResulType(LearningResultEnum.un_complete)
  /**
   * 考试成绩
   */
  examScore: number = null

  static from(dto: ExamLearningDetailResponse) {
    const vo = new ExamLogItem()
    vo.examName = dto.examSessionName
    vo.examBeginTime = dto.examStartTime
    vo.examEndTime = dto.examEndTime
    vo.examResult = new LearningResulType(dto.completeStatus)
    vo.examScore = dto.examScore
    return vo
  }
}
