import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/Bill-default'

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 批次发票订单查询条件
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class BatchOrderBillQueryRequest {
  /**
   * 批次号
   */
  batchNum?: string
  /**
   * 发票号
   */
  billNo?: string
  /**
   * 收货方式 1：快递 2：自取
   */
  deliveryWayType?: string
  /**
   * 缴费方式
   */
  payWay?: string
  /**
   * 运费收款方式
   */
  freightPaymentWay?: string
  /**
   * 买家ID
   */
  buyerIds?: Array<string>
  /**
   * 业务参数
   */
  bizParam?: string
  /**
   * 发票抬头
   */
  billTitle?: string
  /**
   * 发票抬头类别
   */
  billTitleType?: string
  /**
   * 支付成功检索的开始时间
   */
  payStartTime?: string
  /**
   * 支付成功检索的结束时间
   */
  payEndTime?: string
  /**
   * 项目ID
   */
  projectId?: string
  /**
   * 平台ID
   */
  platformId?: string
  /**
   * 平台版本ID
   */
  platformVersionId?: string
  /**
   * 子项目ID
   */
  subProjectId?: string
  /**
   * 单位ID
   */
  unitId?: string
  /**
   * 组织机构ID
   */
  organizationId?: string
  /**
   * 仓储点id
   */
  storageId?: string
  /**
   * 承运商id
   */
  carrierId?: string
  /**
   * 发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   */
  invoiceType?: string
  /**
   * 索取发票开始时间
   */
  getBillStartTime?: string
  /**
   * 索取发票结束时间
   */
  getBillEndTime?: string
  /**
   * 是否开票
   */
  makeInvoice?: boolean
  /**
   * 开票开始时间
   */
  makeInvoiceStartTime?: string
  /**
   * 开票结束时间
   */
  makeInvoiceEndTime?: string
  /**
   * 业务对象集合列表
   */
  objectList?: Array<InvoiceObjectRequest>
  /**
   * 是否冻结
   */
  frozen?: boolean
  /**
   * 发票代码
   */
  billCode?: string
  /**
   * 发票验证码
   */
  billVeriCode?: string
  /**
   * 是否电子发票
   */
  electron?: boolean
  /**
   * 创建人ID
   */
  creatorId?: string
  /**
   * 发票所有人
   */
  billOwners?: Array<string>
  /**
   * 是否是测试数据
   */
  test?: boolean
  /**
   * 发票状态查询
发票状态| 1：未确认 2：已确认3：等待打印 4：打印中 5：打印成功 6：入库中 7：入库成功 8：入库失败9：打正票中 10 打正票失败
   */
  states?: Array<string>
  /**
   * 是否非税务票
   */
  noTaxBill?: boolean
  /**
   * 批次单创建类型, 1、系统创建 2、买家创建 3、管理员创建 4、历史迁移 5、外部接口
   */
  createType?: string
  /**
   * 发票所属订单的收款账号
   */
  receiptAccount?: string
}

/**
 * 发票特征标记信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class InvoiceObjectRequest {
  objectType?: string
  objectId?: string
}

/**
 * 发票更新信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class InvoiceUpdateRequest {
  /**
   * 发票抬头类型 1：个人 2：企业
该值不传表示不修改
   */
  invoiceTitleType?: number
  /**
   * 发票抬头
该值不传表示不修改，传入&quot;&quot;空字符串表示修改为空
<pre>
当发票抬头为1（个人）时，填写用户姓名
当发票抬头为2（企业）时，填写企业全称
</pre>
   */
  invoiceTitle?: string
  /**
   * 纳税人识别号
该值不传表示不修改，传入&quot;&quot;空字符串表示修改为空
<pre>
当发票抬头为1（个人）时，可以不填
当发票抬头为2（企业）时，必须填写企业纳税人识别号
</pre>
   */
  taxpayerNo?: string
  /**
   * 购货方地址
该值不传表示不修改，传入&quot;&quot;空字符串表示修改为空
   */
  address?: string
  /**
   * 购货方电话
该值不传表示不修改，传入&quot;&quot;空字符串表示修改为空
   */
  phone?: string
  /**
   * 购货方银行开户行
该值不传表示不修改，传入&quot;&quot;空字符串表示修改为空
   */
  bankName?: string
  /**
   * 购货方银行账号
该值不传表示不修改，传入&quot;&quot;空字符串表示修改为空
   */
  bankAccount?: string
  /**
   * 发票票面备注
该值不传表示不修改，传入&quot;&quot;空字符串表示修改为空
   */
  billRemark?: string
}

/**
 * 非批次发票查询条件
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class NonBatchOrderBillQueryRequest {
  /**
   * 主订单号
   */
  orderNos?: Array<string>
  /**
   * 发票号
   */
  billNo?: string
  /**
   * 收货方式 1：快递 2：自取
   */
  deliveryWayType?: string
  /**
   * 缴费方式
   */
  payWay?: string
  /**
   * 运费收款方式
   */
  freightPaymentWay?: string
  /**
   * 买家ID
   */
  buyerIds?: Array<string>
  /**
   * 业务参数
   */
  bizParam?: string
  /**
   * 发票抬头
   */
  billTitle?: string
  /**
   * 发票抬头类别
   */
  billTitleType?: string
  /**
   * 支付成功检索的开始时间
   */
  payStartTime?: string
  /**
   * 支付成功检索的结束时间
   */
  payEndTime?: string
  /**
   * 项目ID
   */
  projectId?: string
  /**
   * 平台ID
   */
  platformId?: string
  /**
   * 平台版本ID
   */
  platformVersionId?: string
  /**
   * 子项目ID
   */
  subProjectId?: string
  /**
   * 单位ID
   */
  unitId?: string
  /**
   * 组织机构ID
   */
  organizationId?: string
  /**
   * 仓储点id
   */
  storageId?: string
  /**
   * 承运商id
   */
  carrierId?: string
  /**
   * 发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   */
  invoiceType?: string
  /**
   * 索取发票开始时间
   */
  getBillStartTime?: string
  /**
   * 索取发票结束时间
   */
  getBillEndTime?: string
  /**
   * 是否开票
   */
  makeInvoice?: boolean
  /**
   * 开票开始时间
   */
  makeInvoiceStartTime?: string
  /**
   * 开票结束时间
   */
  makeInvoiceEndTime?: string
  /**
   * 业务对象集合列表
   */
  objectList?: Array<InvoiceObjectRequest>
  /**
   * 是否冻结
   */
  frozen?: boolean
  /**
   * 发票代码
   */
  billCode?: string
  /**
   * 发票验证码
   */
  billVeriCode?: string
  /**
   * 是否电子发票
   */
  electron?: boolean
  /**
   * 是否测试数据
   */
  test?: boolean
  /**
   * 发票状态查询
发票状态| 1：未确认 2：已确认3：等待打印 4：打印中 5：打印成功 6：入库中 7：入库成功 8：入库失败9：打正票中 10 打正票失败
   */
  states?: Array<string>
  /**
   * 是否非税务发票
   */
  noTaxBill?: boolean
  /**
   * 发票所属订单的收款账号
   */
  receiptAccount?: string
  /**
   * 冲红状态，-1/0/1/2/3，全部/未冲红/冲红中/冲红成功/冲红失败
   */
  redInvoiceStatus: number
}

/**
 * 发票查询条件
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class OrderBillQueryRequest {
  /**
   * 主订单号
   */
  orderNos?: Array<string>
  /**
   * 发票号
   */
  billNo?: string
  /**
   * 收货方式 1：快递 2：自取
   */
  deliveryWayType?: string
  /**
   * 缴费方式
   */
  payWay?: string
  /**
   * 运费收款方式
   */
  freightPaymentWay?: string
  /**
   * 买家ID
   */
  buyerIds?: Array<string>
  /**
   * 业务参数
   */
  bizParam?: string
  /**
   * 发票抬头
   */
  billTitle?: string
  /**
   * 发票抬头类别
   */
  billTitleType?: string
  /**
   * 支付成功检索的开始时间
   */
  payStartTime?: string
  /**
   * 支付成功检索的结束时间
   */
  payEndTime?: string
  /**
   * 项目ID
   */
  projectId?: string
  /**
   * 平台ID
   */
  platformId?: string
  /**
   * 平台版本ID
   */
  platformVersionId?: string
  /**
   * 子项目ID
   */
  subProjectId?: string
  /**
   * 单位ID
   */
  unitId?: string
  /**
   * 组织机构ID
   */
  organizationId?: string
  /**
   * 仓储点id
   */
  storageId?: string
  /**
   * 承运商id
   */
  carrierId?: string
  /**
   * 发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   */
  invoiceType?: string
  /**
   * 索取发票开始时间
   */
  getBillStartTime?: string
  /**
   * 索取发票结束时间
   */
  getBillEndTime?: string
  /**
   * 是否开票
   */
  makeInvoice?: boolean
  /**
   * 开票开始时间
   */
  makeInvoiceStartTime?: string
  /**
   * 开票结束时间
   */
  makeInvoiceEndTime?: string
  /**
   * 业务对象集合列表
   */
  objectList?: Array<InvoiceObjectRequest>
  /**
   * 是否冻结
   */
  frozen?: boolean
  /**
   * 发票代码
   */
  billCode?: string
  /**
   * 发票验证码
   */
  billVeriCode?: string
  /**
   * 是否电子发票
   */
  electron?: boolean
  /**
   * 创建人ID
   */
  creatorId?: string
  /**
   * 发票所有人
   */
  billOwners?: Array<string>
  /**
   * 是否是测试数据
   */
  test?: boolean
  /**
   * 批次号 仅针对批次单查询有效
   */
  batchNum?: string
  /**
   * 主订单创建类型 1、系统创建 2、买家创建 3、管理员创建 4、历史迁移 5、外部接口
如果是查学员侧的发票，则入参传入2，管理员的则传入3
值参照 CreateTypeConstant.MANAGER_CREATE等
   */
  createType?: string
  /**
   * 批次单创建类型,如果有批次号的情况下才存在 1、系统创建 2、买家创建 3、管理员创建 4、历史迁移 5、外部接口
此字段可以查批次发票
值参照 CreateTypeConstant.MANAGER_CREATE等
   */
  batchOrderCreateType?: string
  /**
   * 发票状态查询
发票状态| 1：未确认 2：已确认3：等待打印 4：打印中 5：打印成功 6：入库中 7：入库成功 8：入库失败9：打正票中 10 打正票失败
   */
  states?: Array<string>
}

/**
 * 发票特征标记信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class InvoiceObjectRequest1 {
  objectType: string
  objectId: string
}

/**
 * 批次发票信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class BatchOrderBillResponse {
  /**
   * 批次号
   */
  batchNum: string
  /**
   * 买家ID
   */
  buyerId: string
  /**
   * 主键 发票ID
   */
  id: string
  /**
   * 发票号
   */
  billNo: string
  /**
   * 仓点编号
   */
  storageNo: string
  /**
   * 发票状态 1：未确认 2：已确认3：等待打印 4：打印中 5：打印成功 6：入库中 7：入库成功 8：入库失败
   */
  state: string
  /**
   * objectId
   */
  objectId: string
  /**
   * 是否完善 true：完善 false：未完善
   */
  perfect: boolean
  /**
   * 是否合并开票 true：合并 false：未合并
   */
  combine: boolean
  /**
   * 是否打印 true：打印 false：未打印
   */
  printBill: boolean
  /**
   * 是否配送 true：配送 false：未配送
   */
  delivery: boolean
  /**
   * 收款账户
   */
  receiptAccount: string
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型 1：个人 2：企业
   */
  titleType: string
  /**
   * 发票类别
   */
  type: string
  /**
   * 开票金额
   */
  money: number
  /**
   * 开票内容
   */
  content: string
  /**
   * 导入时间
   */
  importTime: string
  /**
   * 备注
   */
  remark: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人ID
   */
  creatorId: string
  /**
   * 纳税人识别号
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账号
   */
  account: string
  /**
   * 是否电子发票
   */
  electron: boolean
  /**
   * 发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   */
  invoiceType: string
  /**
   * 电子邮件
   */
  email: string
  /**
   * 发票代码
   */
  billCode: string
  /**
   * 发票验证码
   */
  billVeriCode: string
  /**
   * 是否冻结
   */
  frozen: boolean
  /**
   * 承运商id
   */
  carrierId: string
  /**
   * 商品配送方式
   */
  deliverType: string
  /**
   * 发票所有人,非批次单则是购买者ID，批次单则为平台层传入的所有者
   */
  billOwner: string
  /**
   * 是否测试数据
   */
  test: boolean
  /**
   * 税额
   */
  tax: number
  /**
   * 发票备注
   */
  billRemark: string
  /**
   * 是否非税务票
   */
  noTaxBill: boolean
  /**
   * 数据权限Token
   */
  token: string
}

/**
 * 开票结果信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class DrawInvoiceResultResponse {
  /**
   * 发票开具是否成功
   */
  success: boolean
  /**
   * 发票ID
   */
  billId: string
  /**
   * 错误信息
   */
  errorMessage: string
}

/**
 * 发票信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class OrderBillItemResponse {
  /**
   * 主键 主订单号
   */
  orderNo: string
  /**
   * 买家ID
   */
  buyerId: string
  /**
   * 主键 发票ID
   */
  id: string
  /**
   * 发票号
   */
  billNo: string
  /**
   * 仓点编号
   */
  storageNo: string
  /**
   * 发票状态 1：未确认 2：已确认3：等待打印 4：打印中 5：打印成功 6：入库中 7：入库成功 8：入库失败
   */
  state: string
  /**
   * objectId
   */
  objectId: string
  /**
   * 是否完善 true：完善 false：未完善
   */
  perfect: boolean
  /**
   * 是否合并开票 true：合并 false：未合并
   */
  combine: boolean
  /**
   * 是否打印 true：打印 false：未打印
   */
  printBill: boolean
  /**
   * 是否配送 true：配送 false：未配送
   */
  delivery: boolean
  /**
   * 收款账户
   */
  receiptAccount: string
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型 1：个人 2：企业
   */
  titleType: string
  /**
   * 发票类别
   */
  type: string
  /**
   * 开票金额
   */
  money: number
  /**
   * 开票内容
   */
  content: string
  /**
   * 导入时间
   */
  importTime: string
  /**
   * 备注
   */
  remark: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人ID
   */
  creatorId: string
  /**
   * 纳税人识别号
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账号
   */
  account: string
  /**
   * 是否电子发票
   */
  electron: boolean
  /**
   * 发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   */
  invoiceType: string
  /**
   * 电子邮件
   */
  email: string
  /**
   * 发票代码
   */
  billCode: string
  /**
   * 发票验证码
   */
  billVeriCode: string
  /**
   * 是否冻结
   */
  frozen: boolean
  /**
   * 承运商id
   */
  carrierId: string
  /**
   * 商品配送方式
   */
  deliverType: string
  /**
   * 业务对象集合列表
   */
  objectList: Array<InvoiceObjectRequest1>
  /**
   * 是否测试数据
   */
  test: boolean
  /**
   * 税额
   */
  tax: number
  /**
   * 发票备注
   */
  billRemark: string
  /**
   * 冲红状态：0/1/2/3，未冲红/冲红中/冲红成功/冲红失败
   */
  redInvoiceStatus: number
  /**
   * 冲红发票金额
   */
  redMoney: number
  /**
   * 冲红发票税额
   */
  redTaxRate: number
  /**
   * 冲红发票代码
   */
  redBillCode: string
  /**
   * 冲红发票号码
   */
  redBillNo: string
}

/**
 * 纳税人信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class TaxPayerResponse {
  /**
   * 主键ID
   */
  id: string
  /**
   * 名称
   */
  name: string
  /**
   * 纳税人识别号
   */
  taxCode: string
  /**
   * 发票地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bank: string
  /**
   * 账号
   */
  account: string
}

export class BatchOrderBillResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BatchOrderBillResponse>
}

export class OrderBillItemResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<OrderBillItemResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 批次订单发票查询
   * @param batchOrderBillQuery 查询条件
   * @param page                分页信息
   * @return 批次订单发票信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findBatchOrderBillPageList(
    params: { batchOrderBillQuery?: BatchOrderBillQueryRequest; page?: Page },
    query: DocumentNode = GraphqlImporter.findBatchOrderBillPageList,
    operation?: string
  ): Promise<Response<BatchOrderBillResponsePage>> {
    return commonRequestApi<BatchOrderBillResponsePage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取发票的下载地址
   * @param billId      发票编号
   * @param invoiceType 发票类型：1-蓝票，2-红票
   * @return 发票文件对应相对地址，包含mfs路径
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findInvoiceDownLoadUrl(
    params: { billId?: string; invoiceType: number },
    query: DocumentNode = GraphqlImporter.findInvoiceDownLoadUrl,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 非批次订单发票信息查询，不包含批次订单发票
   * @param nonBatchOrderBillQuery 查询条件
   * @param page                   分页信息
   * @return 非批次订单发票
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findNonBatchOrderBillPageList(
    params: { nonBatchOrderBillQuery?: NonBatchOrderBillQueryRequest; page?: Page },
    query: DocumentNode = GraphqlImporter.findNonBatchOrderBillPageList,
    operation?: string
  ): Promise<Response<OrderBillItemResponsePage>> {
    return commonRequestApi<OrderBillItemResponsePage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 查询所有订单发票
   * @param orderBillQuery 查询条件
   * @param page           分页信息
   * @return 订单发票信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findOrderBillPageList(
    params: { orderBillQuery?: OrderBillQueryRequest; page?: Page },
    query: DocumentNode = GraphqlImporter.findOrderBillPageList,
    operation?: string
  ): Promise<Response<OrderBillItemResponsePage>> {
    return commonRequestApi<OrderBillItemResponsePage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 依据收款账号编号获取纳税人信息
   * @param accountId 收款账号编号
   * @return 纳税人信息
   * @param query 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTaxPayerByAccount(
    accountId: string,
    query: DocumentNode = GraphqlImporter.findTaxPayerByAccount,
    operation?: string
  ): Promise<Response<TaxPayerResponse>> {
    return commonRequestApi<TaxPayerResponse>(SERVER_URL, {
      query: query,
      variables: { accountId },
      operation: operation
    })
  }

  /**   * 收款账号绑定纳税人
   * @param accountId  账号ID
   * @param taxPayerId 纳税人ID
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async bindTaxPayer(
    params: { accountId?: string; taxPayerId?: string },
    mutate: DocumentNode = GraphqlImporter.bindTaxPayer,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 解散发票，将已生成的发票标记为不可用且解除与子订单关系
   * @param billId 发票编号
   * @param mutate 查询 graphql 语法文档
   * @param billId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disbandInvoice(
    billId: string,
    mutate: DocumentNode = GraphqlImporter.disbandInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { billId },
      operation: operation
    })
  }

  /**   * 批量开具蓝票
   * @param billIdList 发票编号列表
   * @return 开具发票结果
   * @param mutate 查询 graphql 语法文档
   * @param billIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async drawBatchBlue(
    billIdList: Array<string>,
    mutate: DocumentNode = GraphqlImporter.drawBatchBlue,
    operation?: string
  ): Promise<Response<Array<DrawInvoiceResultResponse>>> {
    return commonRequestApi<Array<DrawInvoiceResultResponse>>(SERVER_URL, {
      query: mutate,
      variables: { billIdList },
      operation: operation
    })
  }

  /**   * 开具蓝票
   * @param billId 发票编号
   * @return 开具结果
   * @param mutate 查询 graphql 语法文档
   * @param billId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async drawBlue(
    billId: string,
    mutate: DocumentNode = GraphqlImporter.drawBlue,
    operation?: string
  ): Promise<Response<DrawInvoiceResultResponse>> {
    return commonRequestApi<DrawInvoiceResultResponse>(SERVER_URL, {
      query: mutate,
      variables: { billId },
      operation: operation
    })
  }

  /**   * 开具红票
   * @param billId 发票编号
   * @return 开具结果
   * @param mutate 查询 graphql 语法文档
   * @param billId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async drawRed(
    billId: string,
    mutate: DocumentNode = GraphqlImporter.drawRed,
    operation?: string
  ): Promise<Response<DrawInvoiceResultResponse>> {
    return commonRequestApi<DrawInvoiceResultResponse>(SERVER_URL, {
      query: mutate,
      variables: { billId },
      operation: operation
    })
  }

  /**   * 冻结发票
   * @param billId 发票编号
   * @param mutate 查询 graphql 语法文档
   * @param billId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async freezeInvoice(
    billId: string,
    mutate: DocumentNode = GraphqlImporter.freezeInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { billId },
      operation: operation
    })
  }

  /**   * 依据给予的子订单号生成发票
   * @param invoiceType    发票类型：1普通发票，2增值税普通发票，3增值税专用发票
   * @param subOrderNoList 指定需要生成发票的子订单编号
   * @return 发票编号
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async generateInvoice(
    params: { invoiceType: number; subOrderNoList?: Array<string> },
    mutate: DocumentNode = GraphqlImporter.generateInvoice,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 依据订单的合并规则生成发票
   * @param orderNo 订单号
   * @return 发票编号
   * @param mutate 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async generateInvoiceByCombineRule(
    orderNo: string,
    mutate: DocumentNode = GraphqlImporter.generateInvoiceByCombineRule,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { orderNo },
      operation: operation
    })
  }

  /**   * 收款账号解绑定纳税人
   * @param accountId  账号ID
   * @param taxPayerId 纳税人ID
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unBindTaxPayer(
    params: { accountId?: string; taxPayerId?: string },
    mutate: DocumentNode = GraphqlImporter.unBindTaxPayer,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 解冻发票
   * @param billId 发票编号
   * @param mutate 查询 graphql 语法文档
   * @param billId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unfreezeInvoice(
    billId: string,
    mutate: DocumentNode = GraphqlImporter.unfreezeInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { billId },
      operation: operation
    })
  }

  /**   * 更新子项目自动开票间隔天数
   * @param subProjectId 子项目编号
   * @param day          天数
   * @param mutate 查询 graphql 语法文档
   * @param day 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateAutoBillDay(
    day: number,
    mutate: DocumentNode = GraphqlImporter.updateAutoBillDay,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { day },
      operation: operation
    })
  }

  /**   * 更新发票信息
   * @param billId           发票编号
   * @param invoiceUpdateDTO 发票信息
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateInvoiceInfo(
    params: { billId?: string; invoiceUpdateDTO?: InvoiceUpdateRequest },
    mutate: DocumentNode = GraphqlImporter.updateInvoiceInfo,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }
}

export default new DataGateway()
