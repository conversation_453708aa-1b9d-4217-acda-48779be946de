import checkCoursePoolReference from './queries/checkCoursePoolReference.graphql'
import courseWareNameExists from './queries/courseWareNameExists.graphql'
import getCourse from './queries/getCourse.graphql'
import getCourseCountBySyllabus from './queries/getCourseCountBySyllabus.graphql'
import getCourseLearningCount from './queries/getCourseLearningCount.graphql'
import getCoursePoolDTO from './queries/getCoursePoolDTO.graphql'
import getCourseWare from './queries/getCourseWare.graphql'
import getCourseWarePage from './queries/getCourseWarePage.graphql'
import getCourseWareSupplier from './queries/getCourseWareSupplier.graphql'
import getTeacherById from './queries/getTeacherById.graphql'
import getUserLastCourseLearning from './queries/getUserLastCourseLearning.graphql'
import listCourse from './queries/listCourse.graphql'
import listCourseCategory from './queries/listCourseCategory.graphql'
import listCourseInPool from './queries/listCourseInPool.graphql'
import listCourseOutline from './queries/listCourseOutline.graphql'
import listCoursePoolInfoByIds from './queries/listCoursePoolInfoByIds.graphql'
import listCourseWareListByIds from './queries/listCourseWareListByIds.graphql'
import listExcellentCourse from './queries/listExcellentCourse.graphql'
import listTeachersByIds from './queries/listTeachersByIds.graphql'
import listUserCourse from './queries/listUserCourse.graphql'
import listUserCourseLearningSchedule from './queries/listUserCourseLearningSchedule.graphql'
import listUserCoursePool from './queries/listUserCoursePool.graphql'
import listUserCoursewareLearningSchedule from './queries/listUserCoursewareLearningSchedule.graphql'
import listUserUnSelectCourse from './queries/listUserUnSelectCourse.graphql'
import pageCourse from './queries/pageCourse.graphql'
import pageCoursePool from './queries/pageCoursePool.graphql'
import pageTeachers from './queries/pageTeachers.graphql'
import pageUserCourseLearning from './queries/pageUserCourseLearning.graphql'
import statisticCourseLearning from './queries/statisticCourseLearning.graphql'
import statisticUserLearningCourseLearningInfo from './queries/statisticUserLearningCourseLearningInfo.graphql'

export {
  checkCoursePoolReference,
  courseWareNameExists,
  getCourse,
  getCourseCountBySyllabus,
  getCourseLearningCount,
  getCoursePoolDTO,
  getCourseWare,
  getCourseWarePage,
  getCourseWareSupplier,
  getTeacherById,
  getUserLastCourseLearning,
  listCourse,
  listCourseCategory,
  listCourseInPool,
  listCourseOutline,
  listCoursePoolInfoByIds,
  listCourseWareListByIds,
  listExcellentCourse,
  listTeachersByIds,
  listUserCourse,
  listUserCourseLearningSchedule,
  listUserCoursePool,
  listUserCoursewareLearningSchedule,
  listUserUnSelectCourse,
  pageCourse,
  pageCoursePool,
  pageTeachers,
  pageUserCourseLearning,
  statisticCourseLearning,
  statisticUserLearningCourseLearningInfo
}
