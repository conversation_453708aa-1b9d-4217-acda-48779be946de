import { Action, getModule, Module, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import WJWStudent from '@api/service/customer/student/models/WJWStudent'
import msAccountGateway, { CurrentUserUpdateRequest } from '@api/gateway/ms-account-v1'

export class MyStudentUpdate {
  /**
   * 唯一性值
   */
  uniqueData: string
  /**
   * 姓名
   */
  name: string
  /**
   * 昵称
   */
  nickName: string
  /**
   * 头像地址
   */
  displayPhotoUrl?: string
  /**
   * 手机号码
   */
  phoneNumber?: string
  /**
   * 人员类别
   */
  userCategories?: Array<string>
  /**
   * 所属地区编码路径
   */
  areaPath: string
  /**
   * 工作单位名称(只存储名称，用于给没有实体工作单位的项目使用)
   */
  workUnitName: string
  /**
   * 联系地址
   */
  address?: string
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerStudentModule', store })
class StudentModule extends VuexModule {
  studentInfo = new WJWStudent()

  /**
   * 修改当前学员信息
   * todo 建议移动到UserModule下改名updateCurrentUser，并删除此module
   * @param update
   */
  @Action
  async updateStudentInfo(update: MyStudentUpdate) {
    const request = new CurrentUserUpdateRequest()
    request.name = update.name
    request.nickName = update.nickName
    // request.idCard = update.uniqueData
    request.photo = update.displayPhotoUrl
    // request.gender = UserConstantsAdapter.getGender(update.sex)
    request.phone = update.phoneNumber
    if (update.areaPath) {
      request.area = update.areaPath
    } else {
      request.area = ''
    }
    request.address = update.address
    request.companyName = update.workUnitName
    if (update.userCategories) {
      request.peoples = update.userCategories.join(',')
    } else {
      request.peoples = ''
    }
    // request.email = update.email

    const response = await msAccountGateway.updateUserByCurrent(request)
    return response.status
  }
}

export default getModule(StudentModule)
