<!--
 * @Description: 描
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-04-24 16:34:36
 * @LastEditors: <PERSON> yt
 * @LastEditTime: 2022-06-14 11:10:08
-->
<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15" @click="aaa">
        <el-dialog
          :visible.sync="dialogVisible1"
          :show-close="false"
          width="450px"
          class="m-dialog"
          :close-on-press-escape="false"
          :before-close="done"
        >
          <div class="m-no-date">
            <img class="img" src="@design/admin/assets/images/no-data-tips.png" alt="" />
            <div class="date-bd f-mt20">
              <p class="f-f16" v-if="id == '1'">该网校已停用，登录失败。请联系运营管理员。</p>
              <p class="f-f16" v-if="id == '2'">该网校服务已到期，登录失败。请联系运营管理员。</p>
              <p class="f-f16" v-if="id == '3'">该帐号已停用，登录失败。请联系运营管理员。</p>
            </div>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import QueryOnlineSchoolStatus from '@api/service/common/basic-data-dictionary/query/QueryOnlineSchoolStatus'

  @Component
  export default class extends Vue {
    dialogVisible1 = true
    id = '' as any
    aaa() {
      console.log(11)
    }

    async created() {
      console.log('11')
      this.id = this.$route.query.type
    }
    async mounted() {
      const res = await QueryOnlineSchoolStatus.queryStatus()
      if (res.code == 200) {
        this.$router.push('/')
      }
    }
    done() {
      this.$message.warning('无法进入到首页')
    }
  }
</script>
