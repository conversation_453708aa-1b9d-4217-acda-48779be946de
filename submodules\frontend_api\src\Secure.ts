interface SecureDesc {
  [key: string]: Array<string>
}

/* eslint-disable @typescript-eslint/no-unused-vars */
export const Secure = (actions?: SecureDesc) => {
  /* eslint-disable @typescript-eslint/no-unused-vars */
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    // console.log(target, propertyKey, descriptor)
  }
}

/* eslint-disable @typescript-eslint/no-unused-vars */
export const Role = (roles?: Array<RoleType>) => {
  /* eslint-disable @typescript-eslint/no-unused-vars */
  return function(target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    // console.log(target, propertyKey, descriptor)
  }
}

/* eslint-disable @typescript-eslint/no-unused-vars */
export const IsRole = (roles?: Array<RoleType>) => {
  /* eslint-disable @typescript-eslint/no-unused-vars */
  return function(constructor: Function) {
    // todo
  }
}

/**
 * 注解了表示不需要鉴权
 * @param target
 * @param key
 * @param descriptor
 */

/* eslint-disable @typescript-eslint/no-unused-vars */
export function UnAuthorize(target: any, key: string, descriptor: PropertyDescriptor) {
  // todo
}

/**
 * 标注是废弃的不解析
 * @param target
 * @param key
 * @param descriptor
 */

/* eslint-disable @typescript-eslint/no-unused-vars */
export function Deprecated(target: any, key: string, descriptor: PropertyDescriptor) {
  // todo
}

export const GroupNames = {}

/**
 * 角色类型
 */
export enum RoleType {
  admin = '管理员',
  user = '学员',
  teacher = '教师'
}
