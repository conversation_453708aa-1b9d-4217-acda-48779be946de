<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">分销服务管理</el-breadcrumb-item>
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <el-tabs v-model="activeName" class="m-tab-top is-sticky m-table-auto">
      <el-tab-pane label="供应商信息" name="first">
        <div class="f-p15">
          <el-card shadow="never" class="m-card">
            <el-row type="flex" justify="center" class="width-limit">
              <el-col :md="20" :lg="16" :xl="13">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="供应商名称：" class="is-text">很会带货的公司</el-form-item>
                  <el-form-item label="供应商类型：" class="is-text">企业</el-form-item>
                  <el-form-item label="统一社会信用代码：" class="is-text">3333333333333</el-form-item>
                  <el-form-item label="负责人：" class="is-text">带货王</el-form-item>
                  <el-form-item label="手机号：" class="is-text">18888888888</el-form-item>
                  <el-form-item label="合作状态：">
                    <el-tag type="success" size="small">合作中</el-tag>
                    <!--<el-tag size="small">未开始</el-tag>-->
                    <!--<el-tag type="danger" size="small">中止合作</el-tag>-->
                    <!--<el-tag type="info" size="small">已结束</el-tag>-->
                    <!--<el-tag type="warning" size="small">即将到期</el-tag>-->
                  </el-form-item>
                  <el-form-item label="合作记录：">
                    <el-table stripe :data="tableData" class="m-table">
                      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                      <el-table-column label="合作状态" min-width="100" align="center">
                        <template slot-scope="scope">
                          <div v-if="scope.$index === 0">
                            <el-tag type="success">合作中</el-tag>
                          </div>
                          <div v-else-if="scope.$index === 1">
                            <el-tag type="primary">未开始</el-tag>
                          </div>
                          <div v-else-if="scope.$index === 2">
                            <el-tag type="danger">中止合作</el-tag>
                          </div>
                          <div v-else-if="scope.$index === 3">
                            <el-tag type="info">已结束</el-tag>
                          </div>
                          <div v-else>
                            <el-tag type="warning">即将到期</el-tag>
                          </div>
                        </template>
                      </el-table-column>
                      <el-table-column label="操作时间" min-width="180">
                        <template>2023-09-04 11:11:11</template>
                      </el-table-column>
                      <el-table-column label="操作人" min-width="200">
                        <template slot-scope="scope">
                          <div v-if="scope.$index === 0">福建众智汇云信息服务有限公司</div>
                          <div v-else>管理员</div>
                        </template>
                      </el-table-column>
                    </el-table>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="可推广产品" name="second">
        <div class="f-p15">
          <el-card shadow="never" class="m-card">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="年度">
                    <el-select v-model="select" clearable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="地区">
                    <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="培训产品">
                    <el-select v-model="select" clearable filterable placeholder="请选择培训产品">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="行业">
                    <el-select v-model="select" clearable placeholder="请选择行业">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <!--选择行业后展示原型上对应的内容-->
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="科目类型">-->
                <!--    <el-select v-model="select" clearable placeholder="请选择行业">-->
                <!--      <el-option value="选项1"></el-option>-->
                <!--      <el-option value="选项2"></el-option>-->
                <!--    </el-select>-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="培训专业">-->
                <!--    <el-select v-model="select" clearable placeholder="请选择培训专业">-->
                <!--      <el-option value="选项1"></el-option>-->
                <!--      <el-option value="选项2"></el-option>-->
                <!--    </el-select>-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="培训类别">-->
                <!--    <el-select v-model="select" clearable placeholder="请选择培训类别">-->
                <!--      <el-option value="选项1"></el-option>-->
                <!--      <el-option value="选项2"></el-option>-->
                <!--    </el-select>-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="培训对象">-->
                <!--    <el-select v-model="select" clearable placeholder="请选择培训对象">-->
                <!--      <el-option value="选项1"></el-option>-->
                <!--      <el-option value="选项2"></el-option>-->
                <!--    </el-select>-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <!--<el-col :sm="12" :md="8" :xl="6">-->
                <!--  <el-form-item label="岗位类别">-->
                <!--    <el-select v-model="select" clearable placeholder="请选择岗位类别">-->
                <!--      <el-option value="选项1"></el-option>-->
                <!--      <el-option value="选项2"></el-option>-->
                <!--    </el-select>-->
                <!--  </el-form-item>-->
                <!--</el-col>-->
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="指导价格">
                    <el-input v-model="input" class="input-num" />
                    -
                    <el-input v-model="input" class="input-num" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="销售有效期">
                    <el-date-picker
                      v-model="form.date1"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="起始时间"
                      end-placeholder="结束时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--操作栏-->
            <div class="f-mb15">
              <el-button type="primary">批量重新授权</el-button>
              <el-button type="primary">批量取消授权</el-button>
            </div>
            <el-alert type="warning" :closable="false" class="m-alert f-mb15 f-clear">
              <div class="f-c6">当前已授权该供应商 <span class="f-fb f-co">8</span> 个培训产品</div>
            </el-alert>
            <!--表格-->
            <el-table stripe :data="tableData1" class="m-table">
              <el-table-column type="selection" align="center" width="55" fixed="left"> </el-table-column>
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="培训产品名称" min-width="280" fixed="left">
                <template>
                  <a href="#" class="f-link f-cb f-underline">培训方案的名称，点击查看详情</a>
                </template>
              </el-table-column>
              <el-table-column label="培训属性" min-width="280">
                <template>
                  <p class="f-mb5">人社行业</p>
                  <div class="f-c9 f-f12">
                    <p>培训年度：读取方案的培训年度</p>
                    <p>地区：读取培训方案的地区属性值</p>
                    <p>科目类别：读取方案的科目属性值</p>
                    <p>培训类别：方案的专业属性值</p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="指导价格" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <p><el-tag type="success" size="mini">最低</el-tag><i class="f-mlr5">¥</i>99</p>
                    <p><el-tag type="danger" size="mini">最高</el-tag><i class="f-mlr5">¥</i>199</p>
                  </div>
                  <div v-else><i class="f-mr5">¥</i>99</div>
                </template>
              </el-table-column>
              <el-table-column label="销售有效期" min-width="210">
                <template> 2020-11-11<i class="f-plr5">至</i>2024-07-15 </template>
              </el-table-column>
              <el-table-column label="操作" width="110" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">取消授权</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}],
        tableData1: [{}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '企业',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
