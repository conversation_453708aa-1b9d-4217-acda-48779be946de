import { Page } from '@hbfe/common'
import CheckAccountParam from '@api/service/management/trade/single/checkAccount/query/vo/CheckAccountParam'
import CheckAccountListResponse from '@api/service/management/trade/single/checkAccount/query/vo/CheckAccountListResponse'
import {
  default as MsTradeQueryFrontGatewayCourseLearningBacktage,
  default as MsTradeQuery,
  OrderRequest,
  OrderSortField,
  OrderSortRequest,
  OrderStatisticResponse,
  ReturnOrderRequest,
  ReturnOrderSortField,
  ReturnSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import RefundCheckAccountParam from '@api/service/management/trade/single/checkAccount/query/vo/RefundCheckAccountParam'
import RefundCheckAccountListResponse from '@api/service/management/trade/single/checkAccount/query/vo/RefundCheckAccountListResponse'
import QueryCheckAccountBase from '@api/service/management/trade/single/checkAccount/query/vo/QueryCheckAccountBase'
import { statisticOrderInTrainingChannel } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage/graphql-importer'
import { ReturnOrderBasicDataRequest } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'

export default class QueryCheckAccountInTrainingChannel extends QueryCheckAccountBase {
  /**
   * 报名订单数
   */
  orderCount = 0
  /**
   * 交易总金额
   */
  amount = 0
  /**
   * 退款订单数
   */
  refundTradeCount = 0
  /**
   * 退款总金额
   */
  refundAmountCount = 0

  /**
   * 报名订单分页查询
   * @param page 页数
   * @param queryCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<CheckAccountListResponse>
   */
  async queryOfRegistrationOrder(
    page: Page,
    checkAccountParam: CheckAccountParam
  ): Promise<Array<CheckAccountListResponse>> {
    const request = CheckAccountParam.to(checkAccountParam)
    const sort = new OrderSortRequest()
    sort.field = OrderSortField.ORDER_NORMAL_TIME
    sort.policy = SortPolicy.DESC
    const { data } = await MsTradeQuery.pageOrderInTrainingChannel({
      page,
      request,
      sortRequest: [sort]
    })
    await this.statisticOrderInServicerRemoveTotalPeriod(request)
    await this.statisticOrderInServicer(request)
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const orderArr = new Array<CheckAccountListResponse>()
    const userIdList = new Array<string>()
    data.currentPageData.map(item => {
      const data = CheckAccountListResponse.from(item)
      userIdList.push(data.userId)
      orderArr.push(data)
    })
    const map = await this.getUserInfo(userIdList)

    orderArr.map(item => {
      const userInfo = map.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
      }
    })
    return orderArr
  }
  async statisticOrderInServicer(orderRequest: OrderRequest) {
    const { data } = await MsTradeQuery.statisticOrderInTrainingChannel(orderRequest)
    this.orderCount = data?.totalOrderCount || 0
    this.amount = data?.totalOrderAmount || 0
  }
  async statisticOrderInServicerRemoveTotalPeriod(orderRequest: OrderRequest) {
    const response = await MsTradeQuery._commonQuery<OrderStatisticResponse>(statisticOrderInTrainingChannel, {
      request: orderRequest
    })
    this.orderCount = response.data?.totalOrderCount || 0
    this.amount = response.data?.totalOrderAmount || 0
  }

  /**
   * 退款订单分页查询
   * @param page 页数
   * @param queryRefundCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<RefundCheckAccountListResponse>
   */
  async queryOfRefundOrder(
    page: Page,
    queryRefundCheckAccountParam: RefundCheckAccountParam
  ): Promise<Array<RefundCheckAccountListResponse>> {
    const request = RefundCheckAccountParam.refurnTo(queryRefundCheckAccountParam)
    const sort = new ReturnSortRequest()
    sort.field = ReturnOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.DESC
    const { data } = await MsTradeQueryFrontGatewayCourseLearningBacktage.pageReturnOrderInTrainingChannel({
      page: page,
      request,
      sort: [sort]
    })
    await this.queryStatisticReturnOrder(request)
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const refundArr = new Array<RefundCheckAccountListResponse>()
    const userIdList = new Array<string>()
    data.currentPageData.map(item => {
      const data = RefundCheckAccountListResponse.from(item)
      userIdList.push(data.userId)
      refundArr.push(data)
    })
    const map = await this.getUserInfo(userIdList)
    refundArr.map(item => {
      const userInfo = map.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
      }
      item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
    })
    return refundArr
  }
  async queryStatisticReturnOrder(request: ReturnOrderRequest) {
    const totalRes = await MsTradeQueryFrontGatewayCourseLearningBacktage.statisticReturnOrderInTrainingChannel(request)
    if (totalRes.status.isSuccess()) {
      if (totalRes.data) {
        this.refundAmountCount = totalRes.data.totalRefundAmount
        this.refundTradeCount = totalRes.data.totalReturnOrderCount
      } else {
        this.refundAmountCount = 0
        this.refundTradeCount = 0
      }
    }
  }
}
