import OnlineCollectiveInfo from '@api/service/customer/thematic-config/model/OnlineCollectiveInfo'
import OfflineCollectiveInfo from '@api/service/customer/thematic-config/model/OfflineCollectiveInfo'
import { ResponseStatus } from '@hbfe/common'
import BasicDataQueryBackstage from '@api/platform-gateway/platform-training-channel-back-gateway'

/**
 * 专题集体报名模型
 */
class ThematicCollectiveConfig {
  /**
   * 专题ID
   */
  topicID = ''
  /**
   * 线上集体报名配置
   */
  onlineCollectiveInfo = new OnlineCollectiveInfo()
  /**
   * 线下集体报名配置
   */
  offlineCollectiveInfo = new OfflineCollectiveInfo()

  /**
   * 获取线上集体报名配置
   */
  async getOnlineCollectiveInfo() {
    const res = await BasicDataQueryBackstage.getOnlineCollectiveByTrainingChannelIdInSubject(this.topicID)
    if (res.status.isSuccess() && res.data) {
      Object.assign(this.onlineCollectiveInfo, OnlineCollectiveInfo.from(res.data))
    }
    return res.status
  }
  /**
   * 获取线下集体报名配置
   */
  async getOfflineCollectiveInfo() {
    const res = await BasicDataQueryBackstage.getOfflineCollectiveByTrainingChannelIdInSubject(this.topicID)
    if (res.status.isSuccess() && res.data) {
      Object.assign(this.offlineCollectiveInfo, OfflineCollectiveInfo.from(res.data))
    }
    return res.status
  }
}
export default new ThematicCollectiveConfig()
