<route-meta>
{
"title": "课件供应商下拉搜索选择器"
}
</route-meta>
<template>
  <el-cascader
    class="biz-common"
    v-model="categoryIdList"
    :clearable="clearable"
    filterable
    @clear="categoryIdList = undefined"
    :props="props"
    :options="options"
    :placeholder="placeholder"
    @change="selectedChange"
    :disabled="disabled"
  />
</template>

<script lang="ts">
  import { Prop, Emit, Component, Vue, Watch } from 'vue-property-decorator'
  import { CascaderOptions } from '@hbfe/jxjy-admin-components/src/models/CascaderOptions'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import QueryCoursewareSupplier from '@api/service/common/basic-data-dictionary/query/QueryCoursewareSupplier'
  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    multiple: boolean

    @Prop({
      type: String,
      default: '请选择课件供应商'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      type: Array,
      default: () => new Array<string>()
    })
    value: Array<string>

    @Prop({
      type: Boolean,
      default: false
    })
    disabled: boolean

    categoryIdList: Array<string> = new Array<string>()
    options: Array<CascaderOptions> = new Array<CascaderOptions>()
    props = {
      multiple: false
    }

    @Watch('value')
    valueChange() {
      this.categoryIdList = this.value
    }

    @Emit('input')
    selectedChange() {
      return this.categoryIdList
    }

    async created() {
      this.setProps()
      const dataList = await QueryCoursewareSupplier.pageQueryCoursewareSupplierNew()
      dataList.forEach((item) => {
        const option = new CascaderOptions()
        option.label = item.name
        option.value = item.id
        this.options.push(option)
      })
      this.categoryIdList = this.value
    }
    setProps() {
      this.props.multiple = this.multiple
    }
  }
</script>
