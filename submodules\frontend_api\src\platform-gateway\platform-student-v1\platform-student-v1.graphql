schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""学员信息查询
		@param request 学员信息查询请求信息（学员信息key）
	"""
	StudentInfoQuery(request:StudentInfoQueryRequest):StudentInfoQueryResponse @optionalLogin
}
"""学员信息查询接口请求类"""
input StudentInfoQueryRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.StudentInfoQueryRequest") {
	"""学员信息key
		由服务平台传给培训机构的培训平台入参， 过期后将取不到数据
	"""
	studentInfoKey:String
	"""初始token"""
	token:String
}
"""学员信息查询接口返回类"""
type StudentInfoQueryResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.StudentInfoQueryResponse") {
	"""状态码"""
	code:String
	"""返回状态信息"""
	message:String
	"""学员账户id"""
	accountId:String
	"""学员姓名"""
	name:String
	"""学员身份证号"""
	idCard:String
	"""单点登入token"""
	loginToken:String
}

scalar List
