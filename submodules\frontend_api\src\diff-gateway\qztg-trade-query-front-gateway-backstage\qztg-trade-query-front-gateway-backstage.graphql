"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取培训班商品详情"""
	getCommoditySkuInServicer(commoditySkuId:String):QZTGCommoditySkuBackstageResponse
	"""当前商品是否被引用"""
	isReferenced(commoditySkuId:String):Boolean!
}
type CommodityBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityBasicDataResponse") {
	saleTitle:String
	price:BigDecimal
	commodityPicturePath:String
}
type CommodityExtInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityExtInfoResponse") {
	resourceProviderResponse:CommodityResourceProviderResponse
}
type CommodityPurchaseChannelConfigResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityPurchaseChannelConfigResponse") {
	customerPurchase:PurchaseChannelConfigResponse
	collectivePurchase:PurchaseChannelConfigResponse
	administratorImport:PurchaseChannelConfigResponse
	collectiveSignUpPersonalPay:PurchaseChannelConfigResponse
}
type CommodityResourceProviderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityResourceProviderResponse") {
	id:String
	name:String
}
type CommoditySkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommoditySkuPropertyResponse") {
	year:SkuPropertyResponse
	province:SkuPropertyResponse
	city:SkuPropertyResponse
	county:SkuPropertyResponse
	industry:SkuPropertyResponse
	subjectType:SkuPropertyResponse
	trainingCategory:SkuPropertyResponse
	trainingProfessional:SkuPropertyResponse
	technicalGrade:SkuPropertyResponse
	trainingObject:SkuPropertyResponse
	positionCategory:SkuPropertyResponse
	jobLevel:SkuPropertyResponse
	jobCategory:SkuPropertyResponse
	grade:SkuPropertyResponse
	subject:SkuPropertyResponse
	learningPhase:SkuPropertyResponse
	discipline:SkuPropertyResponse
	certificatesType:SkuPropertyResponse
	practitionerCategory:SkuPropertyResponse
	qualificationCategory:SkuPropertyResponse
	trainingForm:SkuPropertyResponse
}
type CommodityTrainingChannelResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityTrainingChannelResponse") {
	trainingChannelId:String
	trainingChannelName:String
	sort:Int
}
type OnShelveResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.OnShelveResponse") {
	shelveStatus:Int
	lastOnShelveTime:DateTime
	offShelveTime:DateTime
	onShelvePlanTime:DateTime
	offShelvePlanTime:DateTime
	publishTime:DateTime
}
type PurchaseChannelConfigResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.PurchaseChannelConfigResponse") {
	couldSee:Boolean
	couldBuy:Boolean
}
type SchemeIssueInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.SchemeIssueInfoResponse") {
	issueCount:Int!
}
type SkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.SkuPropertyResponse") {
	skuPropertyValueId:String
	skuPropertyValueName:String
}
type UserPossessionInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.UserPossessionInfoResponse") {
	possessing:Boolean
	sourceType:Int
	sourceId:String
	subOrderDeliveryStatus:Int
}
interface ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.ResourceResponse") {
	resourceType:String
}
type SchemeResourceResponse implements ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.SchemeResourceResponse") {
	schemeId:String
	schemeName:String
	period:BigDecimal
	schemeType:String
	resourceType:String
}
type CommoditySkuResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.response.CommoditySkuResponse") {
	"""商品id"""
	commoditySkuId:String
	"""商品销售标题"""
	saleTitle:String
	"""商品价格"""
	price:BigDecimal
	"""培训方案id"""
	schemeId:String
	"""商品封面图路径"""
	commodityPicturePath:String
	"""用户商品拥有信息"""
	possessionInfo:UserPossessionInfoResponse
	"""商品属性信息"""
	skuProperty:CommoditySkuPropertyResponse
	"""商品所有渠道的配置信息"""
	commodityPurchaseChannelConfig:CommodityPurchaseChannelConfigResponse
	"""上下架信息"""
	onShelve:OnShelveResponse
}
type QZTGCommoditySkuBackstageResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.response.QZTGCommoditySkuBackstageResponse") {
	"""当前商品合并了哪些列表"""
	mergedCommodities:[CommoditySkuResponse]
	"""当前商品被哪些合并的列表"""
	mergedFromCommodities:[CommoditySkuResponse]
	commoditySkuId:String
	commodityBasicData:CommodityBasicDataResponse
	skuProperty:CommoditySkuPropertyResponse
	onShelve:OnShelveResponse
	commodityPurchaseChannelConfig:CommodityPurchaseChannelConfigResponse
	resource:ResourceResponse
	commodityLastEditTime:DateTime
	commodityCreatTime:DateTime
	isResourceEnabled:Boolean
	trainingChannels:[CommodityTrainingChannelResponse]
	tppTypeId:String
	externalTrainingPlatform:String
	learningSupervisionSystem:String
	courseSupplierId:String
	thirdPartyTrainingSchemeId:String
	saleTotalNumber:Long
	issueInfoResponse:SchemeIssueInfoResponse
	unitId:String
	unitName:String
	extInfo:CommodityExtInfoResponse
}

scalar List
