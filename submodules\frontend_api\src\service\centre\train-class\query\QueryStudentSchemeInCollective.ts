import MsSchemeLearningQueryForestage, {
  LearningRegisterRequest,
  RegionSkuPropertyRequest,
  RegionSkuPropertySearchRequest,
  SchemeRequest,
  SchemeSkuPropertyRequest,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import { Page } from '@hbfe/common'
import StudentSchemeItem from '@api/service/centre/train-class/query/vo/StudentSchemeItem'

export default class QueryStudentSchemeInCollective {
  /**
   * 年度
   */
  year = ''
  /**
   * 地区: 省
   */
  province = ''
  /**
   * 地区: 市
   */
  city = ''
  /**
   * 地区: 区县
   */
  county = ''
  /**
   * 方案名称
   */
  schemeName = ''
  /**
   * 行业
   */
  industry = ''
  /**
   * 科目类型
   */
  subjectType = ''
  /**
   * 培训类别
   */
  trainingCategory = ''
  /**
   * 培训专业
   */
  trainingProfessional = ''
  /**
   * 岗位类别
   */
  positionCategory = ''
  /**
   * 培训对象
   */
  trainingObject = ''
  /**
   * 技术等级
   */
  jobLevel = ''
  /**
   * 工种
   */
  jobCategory = ''

  /**
   * 专题
   */
  trainingChannelId = ''

  /**
   * 是专题
   */
  saleChannels: number[] = []

  /**
   * 批量打印列表口
   * @param page
   * @returns {Promise<StudentSchemeItem[]>}
   */
  async queryStudentSchemeListInCollective(page: Page) {
    const request = new StudentSchemeLearningRequest()
    let result = new Array<StudentSchemeItem>()
    request.scheme = new SchemeRequest()
    request.learningRegister = new LearningRegisterRequest()
    request.scheme.skuProperty = new SchemeSkuPropertyRequest()
    request.scheme.schemeName = this.schemeName || undefined
    request.scheme.skuProperty.industry = this.industry ? [this.industry] : undefined
    request.scheme.skuProperty.subjectType = this.subjectType ? [this.subjectType] : undefined
    request.scheme.skuProperty.trainingCategory = this.trainingCategory ? [this.trainingCategory] : undefined
    request.scheme.skuProperty.trainingProfessional = this.trainingProfessional
      ? [this.trainingProfessional]
      : undefined
    request.scheme.skuProperty.trainingObject = this.trainingObject ? [this.trainingObject] : undefined
    request.scheme.skuProperty.jobLevel = this.jobLevel ? [this.jobLevel] : undefined
    request.scheme.skuProperty.jobCategory = this.jobCategory ? [this.jobCategory] : undefined
    request.scheme.skuProperty.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
    request.scheme.skuProperty.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
    request.scheme.skuProperty.regionSkuPropertySearch.region.push({
      province: this.province || undefined,
      city: this.city || undefined,
      county: this.county || undefined
    })
    request.scheme.skuProperty.year = this.year ? [this.year] : undefined
    request.learningRegister.status = [1, 2]
    request.trainingChannelId = this.trainingChannelId || undefined
    request.saleChannels = this.saleChannels?.length ? this.saleChannels : undefined
    const response = await MsSchemeLearningQueryForestage.pageStatisticsStudentSchemeLearningInCollective({
      page,
      request
    })
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    if (response.status.isSuccess() && response?.data?.currentPageData?.length) {
      await Promise.all(response.data.currentPageData.map(StudentSchemeItem.from)).then((res) => {
        result = res
      })
    }
    return result
  }
}
