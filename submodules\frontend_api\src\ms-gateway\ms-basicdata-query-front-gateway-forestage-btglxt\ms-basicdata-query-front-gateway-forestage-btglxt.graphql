"""独立部署的微服务,K8S服务名:ms-basicdata-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""@Description  身份证姓名获取角色id
		<AUTHOR>
		@Date 2023/5/8 15:33
	"""
	findRoleListByIdentity(request:UserRequest):[RoleResponse] @optionalLogin
	"""功能描述：企业-当前登录企业管理员信息
		描述：查询当前登录管理员的信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.EnterpriseUnitAdminInfoResponse
	"""
	getEnterpriseUnitAdminInfoInMyself:EnterpriseUnitAdminInfoResponse
	"""功能描述：人社讲师-查询企业单位信息-详情接口
		描述：人社讲师-查询企业单位信息-详情接口
		@param unitId                  :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse
		@date : 2022年6月9日 10:49:47
	"""
	getEnterpriseUnitInfoInLibraryTeacher(unitId:String):EnterpriseUnitInfoResponse
	"""功能描述：学员-查询企业单位信息-详情接口
		描述：学员-查询企业单位信息-详情接口
		@param unitId                  :
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.unit.EnterpriseUnitInfoResponse
		@date : 2022年6月9日 10:49:47
	"""
	getEnterpriseUnitInfoInStudent(unitId:String):EnterpriseUnitInfoResponse @optionalLogin
	"""功能描述 :学员-查询当前登录学员-详情接口
		描述：查询当前登录学员的详细信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse
		@date : 2022/3/31 16:54
	"""
	getStudentInfoInMyself:StudentInfoResponse
	"""学员-查询当前登录学员的证书信息-列表接口
		@param page
		@param request
		@param dataFetchingEnvironment
		@return
	"""
	pageCertificateInfoInStudentMyself(page:Page,request:CertificateQueryRequest):CertificateResponsePage @page(for:"CertificateResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""功能描述：用户基本查询条件
	@Author： wtl
	@Date： 2022年1月26日 09:30:12
"""
input UserRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.request.UserRequest") {
	"""用户id集合"""
	userIdList:[String]
	"""用户名称"""
	userName:String
	"""用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	userNameMatchType:Int
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
		@see MatchTypeConstant
	"""
	phoneMatchType:Int
}
input CertificateQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.request.certificate.CertificateQueryRequest") {
	"""证书持有人"""
	certificateOwner:CertificateOwnerRequest
	"""证书信息"""
	certificateInfo:CertificateInfoRequest
	"""证书培训信息"""
	certificateTrainingInfo:CertificateTrainingInfoRequest
	"""证书排序"""
	sortList:[CertificateSortRequest]
}
input CertificateInfoRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.request.certificate.nested.CertificateInfoRequest") {
	"""证书id集合"""
	certificateIdList:[String]
	"""证书编号"""
	certificateNo:String
	"""证书编号匹配方式，默认为like(0：完全匹配 1：模糊查询，*certificateNo* 2：左模糊查询，*certificateNo 3:右模糊查询，certificateNo*)
		@see MatchTypeConstant
	"""
	certificateNoMatchType:Int
	"""证书类别id集合"""
	certificateTypeIdList:[String]
	"""证书类型id集合"""
	certificateCategoryIdList:[String]
	"""证书专业id集合"""
	certificateMajorIdList:[String]
	"""证书等级id集合"""
	certificateLevelIdList:[String]
	"""证书职业id集合"""
	certificateProfessionIdList:[String]
	"""证书职业工种id集合"""
	certificateWorkTypeIdList:[String]
	"""证书职业id和证书职业工种id之间查询关系"""
	professionAndWorkTypeRelation:Int
	"""证书类型id集合"""
	trainingTypeIdList:[String]
	"""发证单位编码"""
	grantUnitCode:String
	"""发证单位编码匹配方式，默认为like(0：完全匹配 1：模糊查询，*grantUnitCode* 2：左模糊查询，*grantUnitCode 3:右模糊查询，grantUnitCode*)
		@see MatchTypeConstant
	"""
	grantUnitCodeMatchType:Int
	"""发证单位名称"""
	grantUnitName:String
	"""发证单位名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*grantUnitName* 2：左模糊查询，*grantUnitName 3:右模糊查询，grantUnitName*)
		@see MatchTypeConstant
	"""
	grantUnitNameMatchType:Int
	"""发证地区路径"""
	regionPathList:[String]
	"""发证地区路径匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
		@see MatchTypeConstant
	"""
	regionPathListMatchType:Int
	"""职称"""
	professional:String
	"""证书来源类型"""
	sourceTypeList:[String]
	"""证书发证时间范围"""
	grantDateScope:DateScopeRequest
	"""证书有效期范围"""
	expiredDateScope:DateScopeRequest
	"""证书颁发时间范围"""
	awardDateScope:DateScopeRequest
	"""证书分类
		1、内部证书 2、外部证书
		@see com.fjhb.ms.basicdata.enums.CertificateClassifyEnum
	"""
	certificateClassifyList:[Int]
	"""证书状态集合
		0、未颁发 1、正常 2、注销
		@see com.fjhb.ms.basicdata.enums.CertificateStatusEnum
	"""
	statusList:[Int]
}
"""证书所有人查询条件"""
input CertificateOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.request.certificate.nested.CertificateOwnerRequest") {
	"""学员id集合"""
	studentIdList:[String]
}
input CertificateSortRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.request.certificate.nested.CertificateSortRequest") {
	"""排序字段"""
	sortField:CertificateSortFieldEnum
	"""排序类型"""
	sortType:SortTypeEnum
}
"""证书相关培训信息
	<AUTHOR>
	@date 2022-11-4
"""
input CertificateTrainingInfoRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.request.certificate.nested.CertificateTrainingInfoRequest") {
	"""学习方案id"""
	learningSchemeIdList:[String]
	"""参训资格id"""
	qualificationIdList:[String]
}
input DateScopeRequest @type(value:"com.fjhb.ms.basicdata.repository.DateScopeRequest") {
	beginTime:DateTime
	endTime:DateTime
}
enum SortTypeEnum @type(value:"com.fjhb.ms.basicdata.enums.SortTypeEnum") {
	ASC
	DESC
}
type RegionModel @type(value:"com.fjhb.ms.basicdata.model.RegionModel") {
	regionId:String
	regionPath:String
	provinceId:String
	provinceName:String
	cityId:String
	cityName:String
	countyId:String
	countyName:String
}
"""功能描述：账户信息
	@Author： wtl
	@Date： 2022年5月11日 15:30:56
"""
type AccountResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.AccountResponse") {
	"""账户id"""
	accountId:String
	"""帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
		@see AccountTypes
	"""
	accountType:Int
	"""单位信息"""
	unitInfo:UnitInfoResponse
	"""所属顶级企业帐户Id"""
	rootAccountId:String
	"""帐户状态 1：正常，2：冻结，3：注销
		@see AccountStatus
	"""
	status:Int
	"""注册方式
		0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
		@see AccountRegisterTypes
	"""
	registerType:Int
	"""来源类型
		0内置，1项目主网站，2安卓，3IOS
		@see AccountSourceTypes
	"""
	sourceType:Int
	"""创建时间"""
	createdTime:DateTime
	"""最后更新时间"""
	lastUpdateTime:DateTime
}
"""功能描述：帐户认证信息
	@Author： wtl
	@Date： 2022年5月11日 14:23:18
"""
type AuthenticationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.AuthenticationResponse") {
	"""帐号"""
	identity:String
	"""认证标识类型
		1用户名,2手机,3身份证,4邮箱,5第三方OpenId
	"""
	identityType:Int
	"""认证方式状态 1启用，2禁用
		@see AuthenticationStatusEnum
	"""
	status:Int
}
"""功能描述：角色信息
	@Author： wtl
	@Date： 2022/1/24 20:17
"""
type RoleResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.RoleResponse") {
	"""角色id"""
	roleId:String
	"""角色名称"""
	roleName:String
	"""角色类型
		（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
		@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
	"""
	roleType:String
	"""角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
		@see RoleCategories
	"""
	roleCategory:Int
}
"""功能描述 : 企业单位管理员信息
	@date : 2022/6/18 12:24
"""
type EnterpriseUnitAdminInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.admin.EnterpriseUnitAdminInfoResponse") {
	"""企业单位管理员归属信息"""
	enterpriseUnitAdminOwner:EnterpriseUnitAdminOwnerResponse
	"""账户信息"""
	accountInfo:AccountResponse
	"""管理员用户信息"""
	userInfo:AdminUserInfoResponse
	"""人员信息"""
	personInfo:EnterpriseUnitPersonInfoResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
	"""角色信息集合"""
	roleList:[RoleResponse]
}
"""功能描述：管理员用户信息
	@Author： wtl
	@Date： 2022年1月25日 15:48:48
"""
type AdminUserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.admin.nested.AdminUserInfoResponse") {
	"""管辖地区集合"""
	manageRegionList:[RegionModel]
	"""办公室（所在处/科室）"""
	office:String
	"""岗位/职位"""
	position:String
	"""备注"""
	remark:String
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
}
"""功能描述 : 企业单位管理员归属查询条件
	@date : 2022年9月2日 10:53:02
"""
type EnterpriseUnitAdminOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.admin.nested.EnterpriseUnitAdminOwnerResponse") {
	"""企业单位id"""
	enterpriseUnitIdList:[String]
}
"""人员信息模型"""
type EnterpriseUnitPersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.admin.nested.EnterpriseUnitPersonInfoResponse") {
	"""是否法人帐号"""
	isCorporateAccount:Boolean
	"""业务关系集合"""
	businessRelationshipList:[PersonBusinessRelationshipResponse]
	"""人员实名认证信息"""
	personIdentityVerificationInfo:PersonIdentityVerificationResponse
}
"""功能描述：业务关系模型信息
	@Author： wtl
	@Date： 2024年3月29日 20:51:03
"""
type PersonBusinessRelationshipResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.admin.nested.PersonBusinessRelationshipResponse") {
	"""关系id"""
	relationId:String
	"""业务类型（1：组织关系 2：主要经办  3：主要经办）
		@see PersonUnitRelationshipBusinessTypes
	"""
	businessType:Int
	"""关系类型（标识人与哪个实体产生的关系）
		@see PersonRelationTypeConstant
	"""
	relationType:String
	"""关系值（业务类型为组织关系时，目前该值为单位id或部门id；业务类型为主要经办或经办，该值为单位id）"""
	relationValue:String
	"""关系状态（0：冻结 1：正常）
		@see PersonUnitRelationshipStatus
	"""
	status:Int
}
"""人员实名认证信息模型"""
type PersonIdentityVerificationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.admin.nested.PersonIdentityVerificationResponse") {
	"""是否已认证"""
	identityVerification:Boolean
	"""认证渠道(1:闽政通 2：腾讯)"""
	identityVerificationChannel:Int
	"""认证时间"""
	identityVerificationTime:DateTime
}
"""单位信息模型"""
type UnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.admin.nested.UnitInfoResponse") {
	"""单位ID"""
	unitId:String
}
"""网关证书信息返回值
	<AUTHOR>
	@date 2022-08-18
"""
type CertificateResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.certificate.CertificateResponse") {
	"""证书业务归属信息"""
	businessOwnerInfo:CertificateBusinessOwnerResponse
	"""证书信息"""
	certificateInfo:CertificateInfoResponse
	"""证书培训信息"""
	certificateTrainingInfo:CertificateTrainingInfoResponse
}
type CertificateBusinessOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.certificate.nested.CertificateBusinessOwnerResponse") {
	"""学员用户id"""
	studentId:String
	"""证件类型
		@see com.fjhb.domain.basicdata.api.consts.IdCardTypes
	"""
	idCardType:Int
	"""证件号"""
	idCard:String
	"""姓名"""
	userName:String
}
type CertificateInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.certificate.nested.CertificateInfoResponse") {
	"""证书id"""
	certificateId:String
	"""证书名称"""
	certificateName:String
	"""证书编号"""
	certificateNo:String
	"""证书类别id"""
	certificateTypeId:String
	"""证书类型id"""
	certificateCategoryId:String
	"""证书专业id"""
	certificateMajorId:String
	"""证书等级id"""
	certificateLevelId:String
	"""证书职业id"""
	certificateProfessionId:String
	"""证书职业工种id"""
	certificateWorkTypeId:String
	"""职称"""
	professional:String
	"""培训类型Id"""
	trainingTypeId:String
	"""证书来源类型"""
	sourceType:String
	"""证书来源id"""
	sourceId:String
	"""发证单位id"""
	grantUnitId:String
	"""发证单位编码"""
	grantUnitCode:String
	"""发证单位名称"""
	grantUnitName:String
	"""发证日期"""
	grantTime:DateTime
	"""颁发日期"""
	awardTime:DateTime
	"""证书有效期"""
	expiredTime:DateTime
	"""发证地区"""
	grantRegion:RegionModel
	"""是否纳入省级证书库"""
	intoOfficialLibrary:Boolean
	"""证书状态
		0、未颁发 1、正常 2、注销
		@see com.fjhb.ms.basicdata.enums.CertificateStatusEnum
	"""
	status:Int
	"""备注"""
	remark:String
	"""证书分类
		1、内部证书 2、外部证书 3、鉴定证书
		@see CertificateClassifyEnum
	"""
	certificateClassify:Int
	"""证书信息创建时间"""
	createdTime:DateTime
	"""证书信息更新时间"""
	updateTime:DateTime
	"""鉴定机构id"""
	identificationUnitId:String
	"""鉴定机构code"""
	identificationUnitCode:String
	"""鉴定单位名称"""
	identificationUnitName:String
	"""鉴定机构地区"""
	identificationUnitRegion:RegionModel
}
"""证书相关培训信息
	<AUTHOR>
	@date 2022-11-4
"""
type CertificateTrainingInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.certificate.nested.CertificateTrainingInfoResponse") {
	"""学习方案id"""
	learningSchemeId:String
	"""参训资格id"""
	qualificationId:String
	"""培训单位"""
	trainingUnitId:String
	"""考试日期"""
	examTime:DateTime
}
"""功能描述：学员信息
	@Author： wtl
	@Date： 2022年1月26日 10:38:15
"""
type StudentInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.student.StudentInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""学员用户信息"""
	userInfo:StudentUserInfoResponse
	"""第三方绑定信息"""
	openPlatformBind:OpenPlatformBindResponse
}
"""功能描述：附件信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type AttachmentInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.student.nested.AttachmentInfoResponse") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
"""功能描述：学员绑定信息
	@Author： wtl
	@Date： 2022年5月12日 14:42:51
"""
type OpenPlatformBindResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.student.nested.OpenPlatformBindResponse") {
	"""是否绑定微信"""
	bindWX:Boolean!
	"""微信昵称"""
	nickNameByWX:String
}
"""功能描述：学员证书信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentCertificateResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.student.nested.StudentCertificateResponse") {
	"""证书id"""
	certificateId:String
	"""证书编号"""
	certificateNo:String
	"""证书类别"""
	certificateCategory:String
	"""注册专业"""
	registerProfessional:String
	"""发证日期"""
	releaseStartTime:DateTime
	"""证书有效期"""
	certificateEndTime:DateTime
	"""证书附件信息"""
	attachmentList:[AttachmentInfoResponse]
}
"""功能描述：学员行业信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentIndustryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.student.nested.StudentIndustryResponse") {
	"""用户行业id"""
	userIndustryId:String
	"""行业id"""
	industryId:String
	"""一级专业类别id"""
	firstProfessionalCategory:String
	"""二级专业类别id"""
	secondProfessionalCategory:String
	"""职称等级"""
	professionalQualification:String
	"""学员证书信息集合"""
	userCertificateList:[StudentCertificateResponse]
}
"""功能描述：学员用户信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentUserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.student.nested.StudentUserInfoResponse") {
	"""用户昵称"""
	nickName:String
	"""单位所属地区"""
	region:RegionModel
	"""工作单位名称"""
	companyName:String
	"""头像地址"""
	photo:String
	"""联系地址"""
	address:String
	"""学员行业信息集合"""
	userIndustryList:[StudentIndustryResponse]
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
}
"""功能描述：企业单位信息
	@Author： wtl
	@Date： 2022年6月9日 14:20:55
"""
type EnterpriseUnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.unit.EnterpriseUnitInfoResponse") {
	"""企业单位业务归属信息"""
	businessOwnerInfo:EnterpriseUnitBusinessOwnerResponse
	"""单位基本信息"""
	unitBase:EnterpriseUnitBaseResponse
	"""经营信息"""
	businessInfo:BusinessInfoResponse
	"""单位认证信息"""
	unitIdentityVerificationInfo:UnitIdentityVerificationResponse
}
"""功能描述：附件信息"""
type AttachmentResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.unit.nested.AttachmentResponse") {
	"""附件id"""
	attachmentId:String
	"""附件类型
		1《营业执照 / 民办法人登记证书/非民办企业法人登记证书》
		2《开展短期职业培训承诺书》（加盖公司公章）》
		3《培训单位基本信息表》（加盖公司公章）》
		4《培训单位办学许可证》（加盖公司公章）》
		5《年度年审合格证书》（加盖公司公章）》
		6 开通依据
	"""
	attachmentType:Int
	"""附件名称"""
	attachmentName:String
	"""附件路径"""
	attachmentPath:String
	"""创建时间"""
	createdTime:DateTime
}
"""功能描述：企业经营信息
	@Author： wtl
	@Date： 2022年6月9日 14:31:03
"""
type BusinessInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.unit.nested.BusinessInfoResponse") {
	"""营业期限起始日期"""
	operatingBeginDate:DateTime
	"""营业期限截止日期"""
	operatingEndDBtglxtEnterpriseUnitBackStageQueryResolverate:DateTime
	"""行业信息"""
	industry:IndustryResponse
	"""经营范围,例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。"""
	businessScope:String
	"""主营业务
		例如：隧道工程、铁路工程、公路工程 、土石方工程、市政工程、水利水电工程、 堤防工程、建筑装饰装修工程、房屋建筑工程、机电安装工程的设计施工；房地产开发。
	"""
	mainBusiness:String
}
"""功能描述：单位联系人信息"""
type ContactPersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.unit.nested.ContactPersonInfoResponse") {
	"""联系人"""
	contact:String
	"""联系电话"""
	contactPhone:String
}
"""功能描述：企业单位信息
	@Author： wtl
	@Date： 2022年6月9日 14:23:04
"""
type EnterpriseUnitBaseResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.unit.nested.EnterpriseUnitBaseResponse") {
	"""单位ID"""
	unitId:String
	"""单位名称"""
	unitName:String
	"""单位英文名称"""
	enName:String
	"""统一社会信用代码"""
	code:String
	"""单位简称"""
	shortName:String
	"""单位业务类型
		说明：
		1顶级超管单位,2人社厅、局,3企业,4参训单位,5培训机构,6课件供应商，7渠道商,8集体缴费/报名单位m,9合同供应商,10政策参与者
		11地区管理单位,12行业主管单位,13技工院校,14职业院校.15线上培训机构,10000实名制报表补贴单位,10001评价机构
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitBusinessTypes
		@see UnitBusinessQueryTypes
	"""
	businessType:Int
	"""logo"""
	logo:String
	"""法人信息"""
	legalPersonInfo:LegalPersonResponse
	"""单位类型（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）"""
	unitType:UnitTypeResponse
	"""单位规模（1：微型 2：小型 3：中型 4：大型）
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitScales
	"""
	scale:Int
	"""企业经济类型(国有经济、联营经济、私营企业、股份制、港澳台投资、外商投资、其他经济)
		@see com.fjhb.domain.basicdata.api.unit.consts.UnitEconomicTypes
	"""
	economicTypes:Int
	"""产业类别"""
	industrialCategory:String
	"""成立日期"""
	foundedDate:DateTime
	"""联系电话"""
	phone:String
	"""邮政编码"""
	postCode:String
	"""传真"""
	faxNumber:String
	"""注册地区"""
	region:RegionModel
	"""联系地址"""
	address:String
	"""注册地址"""
	registerAddress:String
	"""登记机关"""
	registeredOrgan:String
	"""注册资金"""
	registeredCapital:String
	"""创建时间"""
	createdTime:DateTime
	"""单位状态
		说明：1正常,2冻结
	"""
	status:Int
	"""工商注册号"""
	businessRegistrationNumber:String
	"""纳税人资质"""
	taxpayerQualification:String
	"""邮箱地址"""
	emailAddress:String
	"""联系人信息"""
	contactPersonInfo:ContactPersonInfoResponse
	"""单位资质附件类型"""
	attachmentList:[AttachmentResponse]
}
"""企业单位业务归属信息"""
type EnterpriseUnitBusinessOwnerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.unit.nested.EnterpriseUnitBusinessOwnerResponse") {
	"""企业归属信息路径
		单位路径（若单位为福州市企业，则该值为:"/福建省企业id/福州市企业id"）
	"""
	unitIdPath:String
}
"""行业信息
	<AUTHOR>
	@date 2022-06-18
"""
type IndustryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.unit.nested.IndustryResponse") {
	"""行业信息ID路径"""
	industryIdPath:String
	"""门类"""
	firstLevelIndustryId:String
	"""大类"""
	secondLevelIndustryId:String
	"""中类"""
	thirdLevelIndustryId:String
	"""小类"""
	fourthLevelIndustryId:String
}
"""功能描述：企业法人信息
	@Author： wtl
	@Date： 2022年6月9日 14:31:03
"""
type LegalPersonResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.unit.nested.LegalPersonResponse") {
	"""法定代表人"""
	legalPerson:String
	"""证件类型"""
	idCardType:String
	"""证件号"""
	idCard:String
}
"""单位认证信息模型"""
type UnitIdentityVerificationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.unit.nested.UnitIdentityVerificationResponse") {
	"""是否已认证"""
	identityVerification:Boolean
	"""认证渠道（单位认证渠道：UnitIdentityVerificationChannels 人员认证渠道：PersonIdentityVerificationChannels）
		@see PersonIdentityVerificationChannels
		@see UnitIdentityVerificationChannels
	"""
	identityVerificationChannel:Int
	"""认证时间"""
	identityVerificationTime:DateTime
}
"""单位类型:（有限责任公司、股份有限公司、股份合作公司、国有企业、集体所有制、个体工商户、独资企业、有限合伙、普通合伙、外商投资企业、港、澳、台商投资企业、联营企业、私营企业）
	<AUTHOR>
	@date : 2022/6/18 14:15
"""
type UnitTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.unit.nested.UnitTypeResponse") {
	"""单位类型ID路径"""
	unitTypeIdPath:String
	"""一级"""
	firstLevelUnitTypeId:String
	"""二级"""
	secondLevelUnitTypeId:String
	"""三级"""
	thirdLevelUnitTypeId:String
}
"""证书排序字段
	<AUTHOR>
	@date 2022-08-18
"""
enum CertificateSortFieldEnum @type(value:"com.fjhb.ms.basicdata.query.kernel.repository.mongo.enums.CertificateSortFieldEnum") {
	"""发证时间"""
	grantTime
	expiredTime
}

scalar List
type CertificateResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CertificateResponse]}
