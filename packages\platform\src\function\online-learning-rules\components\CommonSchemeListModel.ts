/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-12-19 14:52:34
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-12-19 14:57:02
 * @Description: 不包含方案、特殊规则存储
 */
import SchemeSpecialRuleItem from '@api/service/management/online-learning-rule/model/SchemeSpecialRuleItem'
import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'

class CommonSchemeListModel {
  /**
   * 不包含方案列表
   */
  noIncludeSchemeList: RuleSchemeItem[] = []
  /**
   * 特殊规则列表
   */
  schemeSpecialRuleList = new Array<SchemeSpecialRuleItem>()
}
export default new CommonSchemeListModel()
