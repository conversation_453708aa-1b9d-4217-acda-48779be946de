import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 试卷出题范围
 *  0:不得分|1：的全部分数|2：得一半分数|3：每个选项按平均得分
 */
export enum MultipleQuestionMissScorePatterns {
  'NoScore' = 0,
  'FullScores' = 1,
  'HalfScore' = 2,
  'AverageScore' = 3
}

class MultipleQuestionMissScorePatternTypes extends AbstractEnum<MultipleQuestionMissScorePatterns> {
  static enum = MultipleQuestionMissScorePatterns

  constructor(status?: MultipleQuestionMissScorePatterns) {
    super()
    this.current = status
    this.map.set(MultipleQuestionMissScorePatterns.NoScore, '不得分')
    this.map.set(MultipleQuestionMissScorePatterns.FullScores, '得全部分')
    this.map.set(MultipleQuestionMissScorePatterns.HalfScore, '得一半分')
    this.map.set(MultipleQuestionMissScorePatterns.AverageScore, '按平均得分')
  }
}

export default new MultipleQuestionMissScorePatternTypes()
