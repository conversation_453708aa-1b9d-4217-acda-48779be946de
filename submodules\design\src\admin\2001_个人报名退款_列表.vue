<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="收款账号">
                <el-input v-model="input" clearable placeholder="请输入收款账号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="退款申请时间">
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="订单号">
                <el-input v-model="input" clearable placeholder="请输入订单号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="交易流水号">
                <el-input v-model="input" clearable placeholder="请输入交易流水号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="退款状态">
                <el-select v-model="select" clearable filterable placeholder="请选择">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="证件号">
                <el-input v-model="input" clearable placeholder="请输入证件号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="姓名">
                <el-input v-model="input" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="缴费渠道">
                <el-select v-model="select" clearable filterable placeholder="请选择">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训方案">
                <el-select v-model="select" clearable filterable placeholder="请选择">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="期别名称">
                <el-select v-model="select" clearable filterable placeholder="请选择期别">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="审批时间">
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="退款成功时间">
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="供应商">
                <el-select v-model="select" clearable filterable placeholder="请输入或选择供应商">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="分销推广">
                <el-select v-model="select" clearable filterable placeholder="请选择是否是分销订单">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="专题">
                <el-select v-model="select" clearable filterable placeholder="请选择订单是否来源专题">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="专题名称">
                <el-input v-model="input" clearable placeholder="请输入专题进行查询" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="分销商">
                <el-input v-model="input" clearable placeholder="请输入分销商名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="推广门户简称">
                <el-input v-model="input" clearable placeholder="请输入分销商推广门户简称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="退货/款类型">
                <el-select v-model="select" clearable filterable placeholder="请选择退款类型">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">批量同意退款</el-button>
                <el-button type="primary">批量退款确认</el-button>
                <el-button type="primary">查询</el-button>
                <el-button>导出列表数据</el-button>
                <el-button>重置</el-button>
                <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert">
            <div class="f-c6">
              当前共有 <span class="f-fb f-co">5</span> 笔退款订单，退款金额 <span class="f-fb f-co">¥ 999</span>。
            </div>
          </el-alert>
        </div>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
          <el-table-column type="selection" label="" width="50" align="center"></el-table-column>
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="订单号" min-width="220">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <div>
                  2112071509467489926
                  <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
                    <div slot="content">复制订单号</div>
                  </el-tooltip>
                </div>
                <el-tag type="primary" size="small">换班</el-tag>
                <el-tag type="danger" size="small">换期</el-tag>
              </div>
              <div v-else-if="scope.$index === 1">
                <div>
                  2112071509467489926
                  <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
                    <div slot="content">复制订单号</div>
                  </el-tooltip>
                </div>
                <el-tag type="success" size="small">专题</el-tag>
              </div>
              <div v-else>
                <div>
                  2112071509467489926
                  <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
                    <div slot="content">复制订单号</div>
                  </el-tooltip>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="交易流水号" min-width="300">
            <template>WXP20210820175740748958763892081</template>
          </el-table-column>
          <el-table-column label="退款物品" min-width="280">
            <template>
              <p>方案名称方案名称方案名称方案名称方案名称方案名称</p>
              <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
            </template>
          </el-table-column>
          <el-table-column label="数量" min-width="120" align="center">
            <template>15</template>
          </el-table-column>
          <el-table-column label="实付金额(元)" width="140" align="right">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">3.15</div>
              <div v-else-if="scope.$index === 1">52.36</div>
              <div v-else>158.15</div>
            </template>
          </el-table-column>
          <el-table-column label="退款金额(元)" width="140" align="right">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">3.15</div>
              <div v-else-if="scope.$index === 1">52.36</div>
              <div v-else>158.15</div>
            </template>
          </el-table-column>
          <el-table-column label="购买人信息" min-width="240">
            <template>
              <p>姓名：张依依</p>
              <p>证件号：354875965412365896</p>
              <p>手机号：15847412365</p>
            </template>
          </el-table-column>
          <el-table-column label="申请时间 / 审批时间" min-width="220">
            <template>
              <p>申请：2020-11-11 12:20:20</p>
              <p>审批：2020-11-11 12:20:20</p>
            </template>
          </el-table-column>
          <el-table-column label="退款成功时间" min-width="180">
            <template>2020-11-11 12:20:20</template>
          </el-table-column>
          <el-table-column label="退货/款状态" min-width="130">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-badge is-dot type="primary" class="badge-status">退款审批中</el-badge>
              </div>
              <div v-else-if="scope.$index === 1">
                <el-badge is-dot type="danger" class="badge-status">退款失败</el-badge>
              </div>
              <div v-else-if="scope.$index === 2">
                <el-badge is-dot type="info" class="badge-status">退款取消</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="success" class="badge-status">退款成功</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="退货/款类型" min-width="180">
            <template>退货且退款</template>
          </el-table-column>
          <el-table-column label="操作" width="280" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">详情</el-button>
              <el-button type="text" size="mini">取消退款</el-button>
              <el-button type="text" size="mini">拒绝退款</el-button>
              <el-button type="text" size="mini">同意退款</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        >
        </el-pagination>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
