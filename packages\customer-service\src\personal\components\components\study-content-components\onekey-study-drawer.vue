<template>
  <div>
    <el-drawer title="一键学习" :visible.sync="isShowOneKeyStudyDrawer" size="800px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          课程存在测验考核要求，是否确认一键学习？一键学习会同步合格课后测验。
        </el-alert>
        <el-form ref="oneKeyStudyFormRef" :model="oneKeyStudyForm" label-width="auto" class="m-form f-mt40">
          <el-form-item label="设定测验合格分：" required>
            <el-input v-model="oneKeyStudyForm.name" class="input-num" placeholder="请输入" />
            <span class="f-ml5">分</span>
            <span class="f-cb">（及格 / 满分：60 / 100分）</span>
          </el-form-item>
          <el-form-item>
            <span class="f-co">注：请设置合格成绩介于合格分和满分之间。</span>
          </el-form-item>
          <el-form-item class="m-btn-bar">
            <el-button @click="isShowOneKeyStudyDrawer = false">取消</el-button>
            <el-button type="primary">确认</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { ElForm } from 'element-ui/types/form'
  import { Vue, Component, Ref } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    @Ref('oneKeyStudyFormRef')
    oneKeyStudyFormRef: ElForm
    // 抽屉显隐
    isShowOneKeyStudyDrawer = false

    oneKeyStudyForm = {
      name: ''
    }
  }
</script>
