<template>
  <el-form ref="form" label-width="150px" class="m-form">
    <el-form-item label="添加学习资料：" required>
      <autolist-upload-file @submit="addMaterial" :file-type="1" :is-protected="true"></autolist-upload-file>
    </el-form-item>
    <el-form-item label="已添加学习资料：" required>
      <el-table
        stripe
        ref="materialTable"
        :data="tableData"
        max-height="500px"
        class="m-table"
        :row-style="{
          height: '78px'
        }"
      >
        <el-table-column label="附件名称" min-width="100">
          <template v-slot="{ row }">
            <span v-if="row.isEdit">
              <el-input placeholder="请输入" v-model="row.nameCache" class="u-w180"></el-input>
              <span class="el-icon-check f-cb f-csp f-f18 f-ml10" @click="sureAttachmentName(row)"></span>
              <span class="el-icon-close f-ci f-csp f-f18 f-ml10" @click="delAttachmentName(row)"></span>
            </span>
            <div v-else>
              <div class="flex">
                <el-tooltip effect="dark" :content="row.nameCache" placement="top">
                  <div class="text-ellipsis">{{ row.nameCache }}</div>
                </el-tooltip>
                <span @click="editAttachmentName(row)" class="el-icon-edit f-cb f-csp f-ml10"></span>
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="格式" min-width="120" prop="format"></el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template v-slot="{ row }">
            <el-button type="text" @click="delMaterials(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>
  </el-form>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import AutolistUploadFile from '@hbfe/jxjy-admin-scheme/src/components/autolist-upload-file.vue'
  import AnnexInfoDto from '@api/service/management/implement/models/AnnexInfoDto'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'

  @Component({
    components: { AutolistUploadFile }
  })
  export default class extends Vue {
    @Prop({
      type: Array,
      required: true
    })
    tableData: AnnexInfoDto[]

    /**
     * 编辑附件名称
     */
    editAttachmentName(row: AnnexInfoDto) {
      row.isEdit = true
    }

    /**
     * 确认附件名称
     */
    sureAttachmentName(row: AnnexInfoDto) {
      row.name = row.nameCache
      row.isEdit = false
    }

    /**
     * 删除附件名称
     */
    delAttachmentName(row: AnnexInfoDto) {
      row.cancelEdit()
    }

    /**
     * 删除学习资料
     */
    delMaterials(row: AnnexInfoDto) {
      this.$confirm('确定要删除该学习资料吗？', '提示', {
        confirmButtonText: '确认',
        type: 'warning'
      }).then(() => {
        const start = this.tableData.findIndex(item => item.path === row.path)
        this.tableData.splice(start, 1)
      })
    }

    /**
     * 获取上传后拿到的文件对象
     * @param file
     */
    addMaterial(file: HBFileUploadResponse) {
      const newAnnexInfoDto = new AnnexInfoDto()
      const type = file.fileName.split('.').pop()
      const name = file.fileName.substring(0, file.fileName.lastIndexOf('.'))
      newAnnexInfoDto.name = newAnnexInfoDto.nameCache = name
      newAnnexInfoDto.path = file.url
      newAnnexInfoDto.format = type
      this.tableData.unshift(newAnnexInfoDto)
    }
  }
</script>
<style scoped lang="scss">
  .flex {
    display: flex;
    align-items: center;

    .text-ellipsis {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
</style>
