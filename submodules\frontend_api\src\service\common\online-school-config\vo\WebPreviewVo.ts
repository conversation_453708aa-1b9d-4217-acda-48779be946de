import BannerVo from '@api/service/common/online-school-config/vo/BannerVo'
import Column from '@api/service/management/online-school-config/column/query/vo/Column'
import PortalVo from './PortalVo'

class WebPreviewVo {
  /**
   * 唯一标识
   */
  id: string
  /**
   * 门户信息 包含友情链接
   */
  portal: PortalVo = new PortalVo()
  /**
   * 轮播图列表
   */
  banners: Array<BannerVo> = new Array<BannerVo>()
  /**
   * 栏目列表
   */
  menus: Array<Column> = new Array<Column>()
  /**
   * 主题颜色
   */
  themeColor: string
}
export default WebPreviewVo
