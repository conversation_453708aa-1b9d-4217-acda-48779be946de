<template>
  <div class="pure">
    <el-card shadow="never" class="m-card f-mb15">
      <!--条件查询-->
      <hb-search-wrapper @reset="resetCondition">
        <el-form-item label="订单号：">
          <el-input v-model="queryParams.mainOrderNo" clearable placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="身份证号：">
          <el-input v-model="queryParams.buyerAccount" clearable placeholder="请输入购买的学员帐号" />
        </el-form-item>
        <el-form-item label="学员姓名：">
          <el-input v-model="queryParams.buyerName" clearable placeholder="请输入购买的学员姓名" />
        </el-form-item>
        <el-form-item label="售后状态：" v-if="isBatchOrderTradeSuccess">
          <el-select
            v-model="queryParams.afterSaleStatus"
            clearable
            filterable
            placeholder="请选择售后状态"
            @clear="queryParams.afterSaleStatus = null"
          >
            <el-option
              v-for="item in afterSaleStatusList"
              :key="item.code"
              :value="item.code"
              :label="item.desc"
            ></el-option>
          </el-select>
        </el-form-item>
        <template slot="actions">
          <el-button type="primary" @click="searchBase">查询</el-button>
        </template>
      </hb-search-wrapper>
      <el-alert type="warning" :closable="false" class="m-alert">
        <div class="f-c3 f-flex f-align-center">
          <div class="f-flex-sub">
            <span class="f-mr50">报名人次：{{ statistic.signUpPersonTime }}</span>
            <span class="f-mr50">总学时数：{{ statistic.totalPeriod }}</span>
            <span class="f-mr50">退款人次：{{ statistic.refundPersonTime }}</span>
            <span class="f-mr50">商品总金额：{{ statistic.commodityTotalAmount }}</span>
            <span class="f-mr50">实付金额：￥{{ statistic.payAmount }}</span>
            <span class="f-mr50">退款金额：￥{{ statistic.refundAmount }}</span>
          </div>
          <el-button
            type="primary"
            size="small"
            class="f-fr"
            @click="applyBatchRefund"
            :disabled="!isBatchOrderTradeSuccess"
            v-if="!isZtlogin"
            >批量退款</el-button
          >
          <el-tooltip class="item" effect="dark" placement="bottom" popper-class="m-tooltip">
            <i class="el-icon-question m-tooltip-icon f-c9 f-ml10"></i>
            <div slot="content">
              批量退款只有批次内不存在退款处理中的订单才能再次发起退款。退款成功的订单无法再次发起退款。
            </div>
          </el-tooltip>
        </div>
      </el-alert>
      <!--表格-->
      <el-table
        ref="elTableRef"
        stripe
        :data="mainOrderList"
        class="m-table f-mt10"
        @select="selectRowChange"
        @select-all="selectAll"
        row-key="id"
        max-height="500px"
        v-loading="query.loading"
      >
        <el-table-column type="selection" width="55" align="center" :selectable="tableSelect"></el-table-column>
        <el-table-column type="index" label="No." width="60" align="center">
          <template slot-scope="scope">
            <span
              :data-index="scope.$index + 1"
              v-observe-visibility="(isVisible, entry) => visibleCourseList(isVisible, entry)"
              >{{ scope.$index + 1 }}</span
            >
          </template>
        </el-table-column>
        <el-table-column label="订单号" min-width="240">
          <template slot-scope="scope">
            <p>
              <a class="f-link f-underline f-cb" @click="goSubOrderDetail(scope.row)">{{ scope.row.mainOrderNo }}</a>
            </p>
            <el-tag type="danger" size="small" v-if="scope.row.isExchange">换班</el-tag>
            <el-tag type="warning" size="small" v-if="scope.row.isExchangePeriod">换期</el-tag>
            <!-- TODO 是否显示专题 -->
            <el-tag type="success" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="购买学员信息" min-width="250">
          <template slot-scope="scope">
            <p>学员：{{ scope.row.buyerInfo.buyerName }}</p>
            <p>证件号：{{ scope.row.buyerInfo.buyerAccount }}</p>
            <p>手机号：{{ scope.row.buyerInfo.buyerPhone }}</p>
          </template>
        </el-table-column>
        <el-table-column label="学时" min-width="120" align="center">
          <template slot-scope="scope">{{ scope.row.period }}</template>
        </el-table-column>
        <el-table-column label="实付金额(元)" min-width="140" align="right">
          <template slot-scope="scope">{{ scope.row.payAmount }}</template>
        </el-table-column>
        <el-table-column label="状态" min-width="120">
          <template slot-scope="scope">{{ mainOrderStatusName(scope.row) }}</template>
        </el-table-column>
        <el-table-column label="售后" min-width="120" align="center">
          <template slot-scope="scope">
            <span v-if="isAfterStatusVisible(scope.row)">{{ afterSaleStatusName(scope.row) }}</span>
            <span v-if="!isAfterStatusVisible(scope.row)">——</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="goSubOrderDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <apply-order-refund
      ref="applyOrderRefundRef"
      :batch-order-no="batchOrderNo"
      :visible.sync="uiConfig.dialog.applyOrderRefundVisible"
      :refund-type="refundType"
      :invoiceDraftType="invoiceDraftType"
      :refundPersonTime="refundPersonTime"
      :refundAmount="refundAmount"
      :returnSubOrders="returnSubOrders"
      @reloadData="handleReloadData"
    ></apply-order-refund>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import QueryBatchOrderDetail from '@api/service/management/trade/batch/order/query/QueryBatchOrderDetail'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { Query, UiPage } from '@hbfe/common'
  import QueryBatchOrderMainOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderMainOrderListVo'
  import BatchOrderMainOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderListDetailVo'
  import BatchOrderMainOrderListStatisticVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderListStatisticVo'
  import { cloneDeep } from 'lodash'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import BatchOrderMainOrderAfterSaleStatus, {
    BatchOrderMainOrderAfterSaleStatusEnum
  } from '@api/service/management/trade/batch/order/enum/BatchOrderMainOrderAfterSaleStatus'
  import BatchOrderMainOrderStatus, {
    BatchOrderMainOrderStatusEnum
  } from '@api/service/management/trade/batch/order/enum/BatchOrderMainOrderStatus'
  import { bind, debounce } from 'lodash-decorators'
  import BatchOrderDetailModule from '@/store/modules-ui/order/BatchOrderDetailModule'
  import { BatchOrderTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
  import DataResolve from '@api/service/common/utils/DataResolve'
  import ApplyOrderRefund from '@hbfe/jxjy-admin-trade/src/order/collective/components/apply-order-refund.vue'
  import { BatchOrderInvoiceStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderInvoiceStatus'
  import { BatchReturnSubOrderInfo } from '@api/ms-gateway/ms-order-v1'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import SellerApplyBatchOrderReturnRequestVo from '@api/service/management/trade/batch/order/mutation/vo/SellerApplyBatchOrderReturnRequestVo'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'

  @Component({
    components: { ApplyOrderRefund }
  })
  export default class extends Vue {
    @Prop({
      type: String,
      required: true,
      default: () => {
        return () => ''
      }
    })
    batchOrderNo: string

    @Watch('batchOrderNo', {
      immediate: false,
      deep: true
    })
    async batchOrderNoChange() {
      this.queryParams.batchOrderNo = this.batchOrderNo
      await this.searchBase()
    }

    @Ref('elTableRef') elTableRef: any

    @Ref('applyOrderRefundRef') applyOrderRefundRef: ApplyOrderRefund

    /**
     * 远端查询入口
     */
    queryRemote: QueryBatchOrderDetail =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchOrderDetail

    SaleChannelEnum = SaleChannelEnum

    // 分页
    page: UiPage
    // 查询
    query: Query = new Query()
    // 查询参数
    queryParams: QueryBatchOrderMainOrderListVo = new QueryBatchOrderMainOrderListVo()
    // 上一次查询参数
    // tempQueryParams: QueryBatchOrderMainOrderListVo = new QueryBatchOrderMainOrderListVo()
    // 列表
    mainOrderList: BatchOrderMainOrderListDetailVo[] = []
    // 统计数据
    statistic: BatchOrderMainOrderListStatisticVo = new BatchOrderMainOrderListStatisticVo()

    // 主单状态列表
    mainOrderStatusList: EnumOption<BatchOrderMainOrderStatusEnum>[] = BatchOrderMainOrderStatus.list()
    // 售后状态列表
    afterSaleStatusList: EnumOption<BatchOrderMainOrderAfterSaleStatusEnum>[] =
      BatchOrderMainOrderAfterSaleStatus.list()
    // 批量退款订单集合
    batchRefundOrderList: BatchOrderMainOrderListDetailVo[] = []

    // ui控制组
    uiConfig = {
      staticLoading: false,
      dialog: {
        // 申请退款
        applyOrderRefundVisible: false
      }
    }

    /**
     * 退款类型 0：普通 1：存在考核结果 2：存在学习完成100%
     */
    refundType: number[] = []

    /**
     * 开票状态 1：已开票 2：未开票
     */
    invoiceDraftType: number = null

    /**
     * 退款金额
     */
    refundAmount = 0

    /**
     * 退款人次
     */
    refundPersonTime = 0

    /**
     * 退款子单集合
     */
    returnSubOrders: BatchReturnSubOrderInfo[] = []

    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    //是否 专题登录
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    @Watch('afterSaleStatusList', {
      immediate: true,
      deep: true
    })
    afterSaleStatusListChange(val: any) {
      console.log('afterSaleStatusList', val)
    }

    /**
     * 批次单状态 1:待下单、2:下单中、3:待付款、4:支付中、5:开通中、6:交易成功、7:交易关闭中、8:交易关闭
     */
    get batchOrderStatus() {
      return BatchOrderDetailModule.batchOrderStatus
    }

    /**
     * 批次单详情
     */
    get batchOrderDetail() {
      return BatchOrderDetailModule.batchOrderDetail
    }

    /**
     *
     */
    get isBatchOrderTradeSuccess() {
      return this.batchOrderStatus === BatchOrderTradeStatusEnum.Pay_Success
    }

    /**
     * 售后状态
     */
    get afterSaleStatusName() {
      return (item: BatchOrderMainOrderListDetailVo) => {
        console.log(
          'this.afterSaleStatusList',
          this.afterSaleStatusList.find((el) => el.code === item.afterSaleStatus)?.desc
        )
        return this.afterSaleStatusList.find((el) => el.code === item.afterSaleStatus)?.desc || null
      }
    }

    /**
     * 是否展示售后状态【交易成功且处于退款中、退款成功】
     */
    get isAfterStatusVisible() {
      return (item: BatchOrderMainOrderListDetailVo) => {
        console.log(
          this.isBatchOrderTradeSuccess &&
            (item.afterSaleStatus === BatchOrderMainOrderAfterSaleStatusEnum.Refunding ||
              item.afterSaleStatus === BatchOrderMainOrderAfterSaleStatusEnum.Success_Refund),
          'detail'
        )
        return this.isBatchOrderTradeSuccess &&
          (item.afterSaleStatus === BatchOrderMainOrderAfterSaleStatusEnum.Refunding ||
            item.afterSaleStatus === BatchOrderMainOrderAfterSaleStatusEnum.Success_Refund)
          ? true
          : false
      }
    }

    /**
     * 主单状态
     */
    get mainOrderStatusName() {
      return (item: BatchOrderMainOrderListDetailVo) => {
        return this.mainOrderStatusList.find((el) => el.code === item.mainOrderStatus)?.desc || null
      }
    }

    /**
     * 是否允许退款（是否允许批量退款是被勾选）
     */
    get enableRefund() {
      return (item: BatchOrderMainOrderListDetailVo) => {
        let result = false
        // 主单处于退款中、退款成功不能勾选、批次处于退款中、退款成功也不能勾选
        if (
          item.afterSaleStatus &&
          item.afterSaleStatus !== BatchOrderMainOrderAfterSaleStatusEnum.Refunding &&
          item.afterSaleStatus !== BatchOrderMainOrderAfterSaleStatusEnum.Success_Refund &&
          !this.batchOrderDetail.isBatchOrderUnderRefund
        ) {
          result = true
        }
        return result
      }
    }

    constructor() {
      super()
      this.page = new UiPage(this.pageMainOrderList, this.pageMainOrderList)
      // this.page.pageSize = 50
    }

    /**
     * 页面初始化
     */
    async created() {
      // 给表格内滚动条滚动增加监听事件
      // await this.$nextTick(async () => {
      //   const element = this.elTableRef.bodyWrapper
      //   element.addEventListener('scroll', this.infiniteScroll)
      // })
      if (this.$route.query.subOrderNo) {
        this.queryParams.mainOrderNo = this.$route.query.subOrderNo as string
      }
    }

    async visibleCourseList(isVisible: boolean, entry: any) {
      const scopeIndex = entry.target.dataset.index
      if (isVisible) {
        if (parseInt(scopeIndex) >= this.page.totalSize) {
          // 最大值时不请求
          return
        }
        if (parseInt(scopeIndex) == this.mainOrderList.length) {
          this.query.loading = true
          this.page.pageNo++
          await this.loadMoreMainOrder()
          this.query.loading = false
        }
      }
    }

    /**
     * 加载第一页数据标志位
     */
    firstPageLoadFlag = false

    /**
     *
     */
    @bind
    @debounce(200)
    async infiniteScroll() {
      const element = this.elTableRef.bodyWrapper
      const scrollDistance = element.scrollHeight - element.scrollTop - element.clientHeight
      // console.log('scrollInfo', scrollDistance, element.scrollHeight, element.scrollTop, element.clientHeight)
      if (scrollDistance <= 0 && !this.firstPageLoadFlag) {
        if (this.mainOrderList.length >= this.page.totalSize) {
          this.$message.warning('没有更多数据')
        } else {
          this.page.pageNo++
          await this.loadMoreMainOrder()
        }
      }
    }

    /**
     * 查询主单列表
     */
    async searchBase() {
      this.firstPageLoadFlag = true
      this.batchRefundOrderList = new Array<BatchOrderMainOrderListDetailVo>()
      this.page.pageNo = 1
      await this.pageMainOrderList()
      this.firstPageLoadFlag = false
    }

    /**
     * 【主单】加载更多
     */
    async loadMoreMainOrder() {
      this.query.loading = true
      try {
        const origin = this.mainOrderList.slice()
        const result = await this.queryRemote.queryBatchOrderMainOrderList(this.page, this.queryParams)
        this.mainOrderList = [...origin, ...result]
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        this.query.loading = false
      }
    }

    /**
     * 查询主单列表
     */
    async pageMainOrderList() {
      this.query.loading = true
      try {
        // 检查查询参数是否变化
        // if (JSON.stringify(this.queryParams) !== JSON.stringify(this.tempQueryParams)) {
        await this.getMainOrderListStatistic()
        // this.tempQueryParams = cloneDeep(this.queryParams)
        // }
        this.mainOrderList = [] as BatchOrderMainOrderListDetailVo[]
        this.mainOrderList = await this.queryRemote.queryBatchOrderMainOrderList(this.page, this.queryParams)
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        this.query.loading = false
      }
    }

    /**
     * 获取统计数据
     */
    async getMainOrderListStatistic() {
      this.uiConfig.staticLoading = true
      if (this.isFxlogin && this.isHadFxAbility) {
        this.statistic = await this.queryRemote.queryFxMainOrderListStatistic(this.queryParams)
      } else {
        this.statistic = await this.queryRemote.queryMainOrderListStatistic(this.queryParams)
      }
      this.uiConfig.staticLoading = false
    }

    /**
     * 重置条件
     */
    async resetCondition() {
      this.queryParams = new QueryBatchOrderMainOrderListVo()
      this.queryParams.batchOrderNo = this.batchOrderNo
      await this.searchBase()
    }

    /**
     * 查看子单
     */
    goSubOrderDetail(row: BatchOrderMainOrderListDetailVo) {
      this.$router.push('/training/trade/order/collective/sub-detail/' + this.batchOrderNo + '/' + row.mainOrderNo)
    }

    /**
     * 批量退款
     */
    @bind
    @debounce(200)
    async applyBatchRefund() {
      if (!DataResolve.isWeightyArr(this.batchRefundOrderList)) {
        this.$alert('请选择退款订单', '提示', {
          confirmButtonText: '知道了',
          type: 'warning',
          callback: (action) => {
            console.log(action)
          }
        })
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        // 获取批次开票状态
        const invoiceInfoList = this.batchOrderDetail.invoiceInfoList
        if (DataResolve.isWeightyArr(invoiceInfoList)) {
          invoiceInfoList.forEach((item) => {
            if (item.invoiceStatus === BatchOrderInvoiceStatusEnum.Complete) {
              this.invoiceDraftType = 1
            }
          })
        } else {
          this.invoiceDraftType = 2
        }
        // 获取退款子单集合
        this.returnSubOrders = this.batchRefundOrderList.map((item) => {
          return { subOrderNo: item.subOrderNo, orderNo: item.mainOrderNo } as BatchReturnSubOrderInfo
        })
        // 获取退款人次
        this.refundPersonTime = this.returnSubOrders.length ?? 0
        // 获取退款金额
        this.refundAmount =
          this.batchRefundOrderList.reduce((prev, cur) => {
            return CalculatorObj.add(cur.payAmount, prev)
          }, 0) ?? 0
        // 获取退款子单id
        let subOrderNoList = this.batchRefundOrderList.map((item) => {
          return item.subOrderNo ?? ''
        })
        subOrderNoList = [...new Set(subOrderNoList)]
        // 获取退款类型
        this.refundType = [] as number[]
        const subRefundType = await Promise.all(
          subOrderNoList?.map(async (item) => {
            if (this.isHadFxAbility && this.isFxlogin) {
              return await TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchRefound.queryIsForce(
                this.batchOrderNo,
                item
              )
            } else {
              return await TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchRefound.queryIsForce(
                this.batchOrderNo,
                item
              )
            }
          })
        )
        this.refundType = [...new Set(subRefundType.flat(Infinity) as number[])]
        this.applyOrderRefundRef.refundParams = new SellerApplyBatchOrderReturnRequestVo()
        this.uiConfig.dialog.applyOrderRefundVisible = true
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }

    /**
     *
     */
    tableSelect(row: BatchOrderMainOrderListDetailVo): boolean {
      const item = row
      // 处于退款中、退款成功不能勾选
      return this.enableRefund(item)
    }

    /**
     * 表单复选框选中事件
     */
    selectRowChange(selection: BatchOrderMainOrderListDetailVo[], row: BatchOrderMainOrderListDetailVo) {
      const id = row.mainOrderNo
      const index = this.batchRefundOrderList.findIndex((item) => item.mainOrderNo === id)
      if (index > -1) {
        this.batchRefundOrderList.splice(index, 1)
      } else {
        this.batchRefundOrderList.push(row)
      }
    }

    async recursionLoad(currentPageNo: number) {
      const totalSize = this.page.totalSize
      if (this.mainOrderList.length >= totalSize) {
        return
      }
      if (currentPageNo === 1) {
        this.mainOrderList = [] as BatchOrderMainOrderListDetailVo[]
      }
      this.page.pageNo = currentPageNo
      const originData = cloneDeep(this.mainOrderList)
      let pageData
      if (this.isHadFxAbility && this.isFxlogin) {
        pageData = await this.queryRemote.queryFxBatchOrderMainOrderList(this.page, this.queryParams)
      } else {
        pageData = await this.queryRemote.queryBatchOrderMainOrderList(this.page, this.queryParams)
      }
      this.mainOrderList = [...originData, ...pageData]

      // 如果还有数据，继续加载
      if (this.mainOrderList.length < totalSize) {
        this.page.pageNo++
        await this.recursionLoad(this.page.pageNo)
      }
    }

    /**
     * 全选/全不选触发事件
     */
    async selectAll(selection: BatchOrderMainOrderListDetailVo[]) {
      if (selection.length) {
        // 选中
        this.query.loading = true
        await this.recursionLoad(1)
        this.batchRefundOrderList = [] as BatchOrderMainOrderListDetailVo[]
        this.mainOrderList?.forEach((row) => {
          if (this.enableRefund(row)) {
            this.elTableRef.toggleRowSelection(row, true)
            this.selectRowChange([row], row)
          }
        })
        this.query.loading = false
      } else {
        // 取消选中
        this.mainOrderList?.forEach((row) => {
          this.elTableRef.toggleRowSelection(row, false)
          this.selectRowChange([], row)
        })
        this.batchRefundOrderList = [] as BatchOrderMainOrderListDetailVo[]
      }
    }

    /**
     * 响应列表刷新
     */
    async handleReloadData() {
      this.batchRefundOrderList = [] as BatchOrderMainOrderListDetailVo[]
      await this.resetCondition()
    }
  }
</script>
