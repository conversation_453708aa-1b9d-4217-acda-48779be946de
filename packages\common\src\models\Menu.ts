class Router {
  path: string
}

export class Meta {
  openWhenInit = false
  icon = ''
  closeAble = true
  sort = 0
}

export default class Menu {
  activeRouter: string
  title: string
  path: string
  icon?: string
  rootRoute?: string
  ext: string
  id: string
  name: string
  code: string
  router: Router = new Router()
  closeAble = true
  openWhenInit: boolean
  menuIcon: string
  children: Array<Menu> = new Array<Menu>()
  meta: Meta = new Meta()
}
