import msExamgataway, {
  ExaminationAnswerPaperResponse,
  // PracticeAnswerPaperResponse,
  SortTypeEnum,
  AnswerPaperSort
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { Page } from '@hbfe/common'
export default class QueryExamRecordList {
  /**
   * 获取学员考试记录列表
   */
  async pagePracticeRecordInSubProject(
    page: Page,
    qualificationId: string
  ): Promise<Array<ExaminationAnswerPaperResponse>> {
    try {
      console.log('page参数=', page, 'qualificationId参数=', qualificationId)
      const res = await msExamgataway.pageExaminationRecordInSubProject({
        page: page,
        qualificationId: qualificationId,
        answerPaperStatus: 2,
        answerPaperSort: AnswerPaperSort.HANDED_TIME,
        sort: SortTypeEnum.DESC
      })
      let tmpArr: ExaminationAnswerPaperResponse[] = []

      if (res.status.isSuccess()) {
        tmpArr = res.data.currentPageData
        page.totalSize = res.data.totalSize
      }

      console.log('调用了pagePracticeRecordInSubProject方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryExamRecordList.ts所处方法，pagePracticeRecordInSubProject',
        e
      )
    }
  }
}
