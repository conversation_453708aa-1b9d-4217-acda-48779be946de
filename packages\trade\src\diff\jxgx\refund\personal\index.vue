<route-meta>
{
"isMenu": true,
"title": "个人报名退款订单",
"sort": 1,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>
<template>
  <jxgx-refund></jxgx-refund>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import Refund from '@hbfe/jxjy-admin-trade/src/refund/personal/index.vue'
  import TradeExport from '@api/service/diff/management/jxgx/trade/TradeExport'

  @Component
  class JxgxRefund extends Refund {
    tradeExport = new TradeExport()

    /**
     * 获取导出列表
     */
    async exportRefundty() {
      return await this.tradeExport.exportReturnOrderExcelInServicer(
        this.exportQueryParam,
        this.queryRefundOrder.returnOrderStatisic
      )
    }

    /**
     * 获取导出列表分销
     */
    async exportRefundfx() {
      return await this.tradeExport.exportFxReturnOrderExcelInDistributor(
        this.exportQueryParam,
        this.queryRefundOrder.returnOrderStatisic
      )
    }
  }
  @Component({
    components: {
      JxgxRefund
    }
  })
  export default class extends Vue {}
</script>
