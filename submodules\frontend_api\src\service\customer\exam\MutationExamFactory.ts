import { CreatePaperTypeEnum } from '@api/service/customer/exam/Enum/createPaperTypeEnum'
import { MutationCreateExamPaper } from '@api/service/customer/exam/mutation/MutationCreateExamPaper'
import { MutationCreatePracticePaper } from '@api/service/customer/exam/mutation/MutationCreatePracticePaper'
import { MutationCreateCourseQuizPaper } from '@api/service/customer/exam/mutation/MutationCreateCourseQuizPaper'
import { MutationSubmitPaper } from '@api/service/customer/exam/mutation/MutationSubmitPaper'
import { MutationPreviewExamPaper } from '@api/service/customer/exam/mutation/MutationPreviewExamPaper'
import { MutationJudgeCourseQuiz } from '@api/service/customer/exam/mutation/MutationJudgeCourseQuiz'
/**
 * 考试业务工厂类
 */
class MutationExamFactory {
  // region properties

  // endregion
  // region methods
  /**
   * 获取出卷对象
   */
  getMutationCreatePaper(
    createType: CreatePaperTypeEnum = CreatePaperTypeEnum.CreatePaperTypeEnumExam
  ): MutationCreateExamPaper | MutationCreatePracticePaper | MutationCreateCourseQuizPaper | MutationPreviewExamPaper {
    if (createType == CreatePaperTypeEnum.CreatePaperTypeEnumExam) {
      return new MutationCreateExamPaper()
    } else if (createType == CreatePaperTypeEnum.CreatePaperTypeEnumPractice) {
      return new MutationCreatePracticePaper()
    } else if (createType == CreatePaperTypeEnum.CreatePaperTypeEnumQuiz) {
      return new MutationCreateCourseQuizPaper()
    } else {
      return new MutationPreviewExamPaper()
    }
  }
  /**
   * 获取卷子操作对象
   */
  getMutationSubmitPaper() {
    return new MutationSubmitPaper()
  }
  /**
   * 课后测验校验对象
   */
  getMutationJudgeCourseQuiz() {
    return new MutationJudgeCourseQuiz()
  }
  // endregion
}
export default MutationExamFactory
