import { ConfigureElectronicSealRequest } from '@api/ms-gateway/ms-certificate-v1'
import { GenerateTypesEnum } from '@api/service/common/enums/personal-leaning/GenerateTypes'

/**
 * 配置电子章请求
 */
class ConfigureElectronicSealRequestVo extends ConfigureElectronicSealRequest {
  /**
   * 生成形式
   * 【默认 1-图片 】
   */
  generateType = GenerateTypesEnum.IMAGE
  /**
   * 电子章url
   */
  url = ''
  /**
   * 电子章落款
   */
  sign = ''
}

export default ConfigureElectronicSealRequestVo
