<template>
  <el-drawer title="配送纪录" :visible.sync="isShow" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <div class="f-mt20 f-mlr40" v-if="deliveryRecordList.length > 0">
        <el-timeline v-for="(item, index) in deliveryRecordList" :key="index">
          <el-timeline-item>
            <p>发票号【{{ item.invoiceNoList[0] }}】 {{ item.shippingMethod === 1 ? '自取' : '快递' }}信息为：</p>
            <el-card shadow="never" class="bg-gray f-mt10" v-if="item.shippingMethod === 1">
              <p>领取人: {{ item.takeResult.takePerson }}</p>
              <p class="f-mt5">手机号: {{ item.takeResult.phone }}</p>
              <p class="f-mt5">取货时间: {{ item.deliveryTime }}</p>
            </el-card>
            <el-card shadow="never" class="bg-gray f-mt10" v-if="item.shippingMethod === 2">
              <p>快递公司: {{ item.express.expressCompanyName }}</p>
              <p class="f-mt5">运单号: {{ item.express.expressNo }}</p>
              <p class="f-mt5">发货时间: {{ item.deliveryTime }}</p>
            </el-card>
          </el-timeline-item>
          <!-- <el-timeline-item>
            <p>发票号【12387596】配送信息为：</p>
            <el-card shadow="never" class="bg-gray f-mt10">
              <p>快递公司: 中国邮政</p>
              <p class="f-mt5">运单号: 1155194294875</p>
              <p class="f-mt5">发货时间: 2020-02-27 14:22:32</p>
            </el-card>
          </el-timeline-item> -->
        </el-timeline>
      </div>
      <el-alert type="info" :closable="false" class="m-alert" v-if="deliveryRecordList.length <= 0">
        <div class="f-ptb30 f-tc">该发票暂时还没有操作记录！</div>
      </el-alert>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { OfflineDeliveryRecord } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    @Prop({ type: Array, default: [] }) deliveryRecordList: Array<OfflineDeliveryRecord>
    isShow = false
    isShowDialog() {
      this.isShow = !this.isShow
    }
  }
</script>
