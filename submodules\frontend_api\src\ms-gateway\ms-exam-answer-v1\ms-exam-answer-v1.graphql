"""独立部署的微服务,K8S服务名:ms-exam-answer-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""试题id集合获取试题答案(不包含父子题及)
		@param encryptQuestionIds 试题id
		@param answerToken        答卷id
		@return
	"""
	getQuestionToAnswerByQuestionIds(encryptQuestionIds:[String],answerToken:String):QuestionAnswerResponse
	"""查询答卷是否可用
		@param token:
		@return {@link Boolean}
		<AUTHOR> By Cb
		@since 2025/1/20 15:44
	"""
	queryAnswerPaperAvailable(token:String):Boolean
}
type Mutation {
	"""申请试卷作答
		@param token 作答Token
		@return 申请作答结果
	"""
	applyAnswer(token:String):ApplyPaperAnswerResponse
	"""申请交卷
		@param token 作答token
	"""
	applyHanding(token:String):String
	"""@param answerPaperId
		@return int
		@title:获取答卷剩余作答时长（单位秒，-1代表无时长限制）
		@description:
		@author: chenzeyu
		@date: 2024/9/25 9:32
	"""
	getRemainingAnswerTime(answerPaperId:String):Int!
	"""提交问卷答卷"""
	handedQuestionnaireAnswerPaper(request:HandedQuestionnaireAnswerPaperRequest):HandedQuestionnaireAnswerPaperResponse
	"""预提交作答
		@param token   作答Token
		@param answers 试题答案集合
	"""
	preSubmitAnswer(token:String,answers:[AnswerRequest]):Void
}
input QuestionGroup @type(value:"com.fjhb.domain.exam.api.paper.events.entities.QuestionGroup") {
	sequence:Int!
	questionType:Int!
	groupName:String!
	eachQuestionScore:Double!
}
"""<AUTHOR>
	@date 2021/6/8 17:44
	@Description:
"""
input AnswerRequest @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.request.AnswerRequest") {
	"""【必填】试题ID"""
	questionId:String
	"""【必填】 题型
		1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
	"""
	questionType:Int!
}
"""简单题作答信息"""
input AskQuestionAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.request.AnswerRequest$AskQuestionAnswer",implementsInputs:["AnswerRequest"]) {
	"""【必填】答案"""
	answer:String
	"""【必填】试题ID"""
	questionId:String
	"""【必填】 题型
		1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
	"""
	questionType:Int!
}
"""填空题作答信息"""
input FillQuestionAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.request.AnswerRequest$FillQuestionAnswer",implementsInputs:["AnswerRequest"]) {
	"""【必填】答案"""
	answer:[MapDto1]
	"""【必填】试题ID"""
	questionId:String
	"""【必填】 题型
		1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
	"""
	questionType:Int!
}
"""多选题作答信息"""
input MultipleQuestionAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.request.AnswerRequest$MultipleQuestionAnswer",implementsInputs:["AnswerRequest"]) {
	"""【必填】答案"""
	answer:[String]
	"""填空内容 key是答案id，value是填空的内容"""
	fillContentMap:Map
	"""【必填】试题ID"""
	questionId:String
	"""【必填】 题型
		1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
	"""
	questionType:Int!
}
"""判断题作答信息"""
input OpinionQuestionAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.request.AnswerRequest$OpinionQuestionAnswer",implementsInputs:["AnswerRequest"]) {
	"""【必填】答案"""
	answer:Boolean!
	"""【必填】试题ID"""
	questionId:String
	"""【必填】 题型
		1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
	"""
	questionType:Int!
}
"""单选题作答信息"""
input RadioQuestionAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.request.AnswerRequest$RadioQuestionAnswer",implementsInputs:["AnswerRequest"]) {
	"""【必填】答案"""
	answer:String
	"""填空内容"""
	fillContent:String
	"""【必填】试题ID"""
	questionId:String
	"""【必填】 题型
		1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
	"""
	questionType:Int!
}
"""量表题作答信息"""
input ScaleQuestionAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.request.AnswerRequest$ScaleQuestionAnswer",implementsInputs:["AnswerRequest"]) {
	"""【必填】答案"""
	answer:Int
	"""【必填】试题ID"""
	questionId:String
	"""【必填】 题型
		1：单选、2多选 3填空 4判断 5问答 6父子题 7量表题
	"""
	questionType:Int!
}
"""提交问卷答卷
	<AUTHOR>
	@date 2025/4/24 17:36
"""
input HandedQuestionnaireAnswerPaperRequest @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.request.HandedQuestionnaireAnswerPaperRequest") {
	"""作答token"""
	token:String
	"""答卷信息"""
	answerPaperViewRequest:PreviewPaperPublishConfigureResponse
	"""出卷时间"""
	answerExtractionTime:DateTime
}
"""@author: zhengp
	@since 2021/8/27 10:35
"""
input MapDto1 @type(value:"com.fjhb.ms.exam.answer.v1.kernel.support.dto.MapDto") {
	key:Int
	answer:String
}
input PreviewPaperPublishConfigureResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.PreviewPaperPublishConfigureResponse") {
	name:String
	description:String
	timeLength:Int!
	totalScore:Double!
	totalQuestionCount:Int!
	groups:[QuestionGroup]
	paperType:Int!
	questions:[BaseQuestionResponse]
}
input BaseFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.BaseFillAnswerResponse") {
	type:Int!
}
input ChildItemResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.ChildItemResponse") {
	no:Int!
	questionId:String
}
input ChooseAnswerOptionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.ChooseAnswerOptionResponse") {
	id:String
	content:String
	enableFillContent:Boolean
	mustFillContent:Boolean
}
input DisarrayFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.DisarrayFillAnswerResponse",implementsInputs:["BaseFillAnswerResponse"]) {
	disarrayCorrectAnswers:[[String]]
	type:Int!
}
input FillCorrectAnswersResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.FillCorrectAnswersResponse") {
	blankNo:Int!
	answers:[String]
}
input SequenceFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.SequenceFillAnswerResponse",implementsInputs:["BaseFillAnswerResponse"]) {
	sequenceCorrectAnswers:[FillCorrectAnswersResponse]
	type:Int!
}
input SequenceRateFillAnswerResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.answer.SequenceRateFillAnswerResponse",implementsInputs:["BaseFillAnswerResponse"]) {
	sequenceRateCorrectAnswers:[SequenceFillAnswerResponse]
	type:Int!
}
input AskQuestionResponse1 @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.AskQuestionResponse",implementsInputs:["BaseQuestionResponse"]) {
	askAnswer:String
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
input BaseQuestionResponse @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.BaseQuestionResponse") {
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
input FatherQuestionResponse1 @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.FatherQuestionResponse",implementsInputs:["BaseQuestionResponse"]) {
	childQuestions:[ChildItemResponse]
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
input FillQuestionResponse1 @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.FillQuestionResponse",implementsInputs:["BaseQuestionResponse"]) {
	fillCount:Int!
	fillQuestionCorrectAnswer:BaseFillAnswerResponse
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
input MultipleQuestionResponse1 @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.MultipleQuestionResponse",implementsInputs:["BaseQuestionResponse"]) {
	answerOptions:[ChooseAnswerOptionResponse]
	multipleQuestionCorrectAnswerIds:[String]
	multipleAnswer:[String]
	fillContentMap:Map
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
input OpinionQuestionResponse1 @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.OpinionQuestionResponse",implementsInputs:["BaseQuestionResponse"]) {
	correctAnswerText:String
	incorrectAnswerText:String
	opinionQuestionCorrectAnswer:Boolean
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
input RadioQuestionResponse1 @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.RadioQuestionResponse",implementsInputs:["BaseQuestionResponse"]) {
	answerOptions:[ChooseAnswerOptionResponse]
	radioQuestionCorrectAnswerId:String
	radioAnswer:String
	fillContent:String
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
input ScaleQuestionResponse1 @type(value:"com.fjhb.ms.exam.extraction.v1.api.command.response.support.question.ScaleQuestionResponse",implementsInputs:["BaseQuestionResponse"]) {
	scaleType:Int!
	startDegree:String
	endDegree:String
	series:Int!
	initialValue:Int!
	scaleAnswer:Int
	id:String
	groupSequence:Int!
	questionType:Int!
	score:Double!
	topic:String
	isChildQuestion:Boolean!
	parentQuestionId:String
	dissects:String
	relateCourseId:[String]
	questionDifficulty:Int!
	answerRequired:Boolean
	answered:Boolean!
	labelCodeList:[String]
}
"""选择题答案选项
	<AUTHOR>
"""
type ChooseAnswerOption @type(value:"com.fjhb.ms.exam.answer.v1.kernel.entities.ChooseAnswerOption") {
	"""答案ID"""
	id:String
	"""答案内容"""
	content:String
	"""建议分值"""
	suggestionScore:Double
	"""是否允许填空"""
	enableFillContent:Boolean
	"""填空是否必填"""
	mustFillContent:Boolean
}
"""答卷作答控制视图
	<AUTHOR>
"""
type AnswerPaperControlViewResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.AnswerPaperControlViewResponse") {
	"""是否有时长控制"""
	timeLengthRestrict:Boolean!
	"""试题提交答案后是否可以重答"""
	questionAgainAnswer:Boolean!
}
"""答卷UI呈现控制视图
	<AUTHOR>
"""
type AnswerPaperUIDisplayViewResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.AnswerPaperUIDisplayViewResponse") {
	"""试题呈现方式
		1 表示整卷
		2 表示大题
		3 表示单题
	"""
	questionDisplay:Int!
	"""是否展示试题解析"""
	dissectsShow:Boolean!
	"""是否显示分数"""
	scoreShow:Boolean!
	"""是否开放作答结果"""
	openAnswerResults:Boolean
	"""是否提供标签"""
	provideLabel:Boolean!
}
"""答卷信息
	<AUTHOR>
"""
type AnswerPaperViewResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.AnswerPaperViewResponse") {
	"""试卷名称"""
	name:String
	"""试卷描述"""
	description:String
	"""作答时长"""
	timeLength:Int!
	"""试卷总分"""
	totalScore:Double!
	"""剩余作答时长"""
	remainderTimeLength:Int!
	"""大题集合，为空集合表示本答卷没有使用大题展示方式"""
	groups:[QuestionGroupViewResponse]
	"""试题集合"""
	questions:[QuestionResponse]
	"""作答控制"""
	control:AnswerPaperControlViewResponse
	"""UI呈现方式"""
	display:AnswerPaperUIDisplayViewResponse
}
"""申请作答结果
	<AUTHOR>
"""
type ApplyPaperAnswerResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.ApplyPaperAnswerResponse") {
	"""答卷状态
		<p>
		说明：
		0 表示正在出卷中，需要前端主动间隔多次进行申请作答，直到成功获取答卷
		1 表示成功获取答卷，answerPaper将返回结构
		2 表示本次作答Token中描述的答卷已交卷无法继续作答
		</p>
	"""
	state:Int!
	"""结果文本信息"""
	message:String
	"""答卷信息"""
	answerPaper:AnswerPaperViewResponse
}
"""提交问卷答卷返回值
	<AUTHOR>
	@date 2025/4/30 10:32
"""
type HandedQuestionnaireAnswerPaperResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.HandedQuestionnaireAnswerPaperResponse") {
	code:String
	message:String
}
"""试题答案响应
	<AUTHOR>
"""
type QuestionAnswerResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.QuestionAnswerResponse") {
	"""试题答案集合"""
	questionToAnswers:[BaseQuestionToAnswer]
}
"""试卷大题信息
	<AUTHOR>
"""
type QuestionGroupViewResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.QuestionGroupViewResponse") {
	"""大题序号"""
	sequence:Int!
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		</p>
	"""
	questionType:Int!
	"""大题名称"""
	groupName:String
	"""试题数"""
	questionCount:Int!
	"""每题平均分数"""
	eachQuestionScore:Double!
}
"""<AUTHOR>
	@date 2021/6/8 15:06
	@Description: 答卷响应抽象类
"""
interface QuestionResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.QuestionResponse") {
	"""试题ID"""
	id:String
	"""试题题目"""
	topic:String
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""试题分数"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""试题解析"""
	dissects:String
	"""是否已作答"""
	answered:Boolean!
	"""是否必答"""
	answerRequired:Boolean
	"""标签Code集合"""
	labelCodeList:[String]
}
"""试题对应答案基类
	<AUTHOR>
"""
interface BaseQuestionToAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.questionanswer.BaseQuestionToAnswer") {
	"""试题id"""
	questionId:String
	"""试题类型
		1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
		简答题父子题无答案
	"""
	type:Int!
}
"""填空题
	<AUTHOR>
"""
type FillQuestionToAnswer implements BaseQuestionToAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.questionanswer.FillQuestionToAnswer") {
	"""填空题答案"""
	fillCorrectAnswer:FillAnswer
	"""填空数"""
	fillCount:Int!
	"""试题id"""
	questionId:String
	"""试题类型
		1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
		简答题父子题无答案
	"""
	type:Int!
}
"""多选题
	<AUTHOR>
"""
type MultipleQuestionToAnswer implements BaseQuestionToAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.questionanswer.MultipleQuestionToAnswer") {
	"""正确答案集合"""
	multipleCorrectAnswers:[String]
	"""试题id"""
	questionId:String
	"""试题类型
		1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
		简答题父子题无答案
	"""
	type:Int!
}
"""判断题
	<AUTHOR>
"""
type OpinionQuestionToAnswer implements BaseQuestionToAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.questionanswer.OpinionQuestionToAnswer") {
	"""正确答案"""
	opinionCorrectAnswer:Boolean
	"""试题id"""
	questionId:String
	"""试题类型
		1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
		简答题父子题无答案
	"""
	type:Int!
}
"""单选
	<AUTHOR>
"""
type RadioQuestionToAnswer implements BaseQuestionToAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.questionanswer.RadioQuestionToAnswer") {
	"""正确答案id"""
	radioCorrectAnswer:String
	"""试题id"""
	questionId:String
	"""试题类型
		1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
		简答题父子题无答案
	"""
	type:Int!
}
"""散乱无序填空题答案实体
	<pre>
	每空有多种匹配答案，空格答案不存在顺序关系： 比如：
	试题题目：请写出中国四大银行__________、__________、__________、__________。
	每空备选答案：
	1/中国建设银行 建设银行 建行
	2/中国银行 中行
	3/中国工商银行 工商银行 工行
	4/中国农业银行 农业银行 农行
	学员答题答案：农行、工行、中行、建行；评卷为正确并得分；
	</pre>
	<AUTHOR>
"""
type DisarrayFillAnswer implements FillAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.questionanswer.fillanswer.DisarrayFillAnswer") {
	"""正确答案集合"""
	disarrayFillCorrectAnswers:[[String]]
	"""填空题答案类型
		@see FillQuestionFillAnswerTypes
		1-散乱无序填空题答案实体    2-按序填空题答案实体     3-按序关联填空题答案实体
	"""
	type:Int!
}
"""填空题答案基类
	<AUTHOR>
"""
interface FillAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.questionanswer.fillanswer.FillAnswer") {
	"""填空题答案类型
		@see FillQuestionFillAnswerTypes
		1-散乱无序填空题答案实体    2-按序填空题答案实体     3-按序关联填空题答案实体
	"""
	type:Int!
}
"""按序填空题答案实体
	<pre>
	每空有多种匹配答案且空格答案存在顺序关系： 比如：
	试题题目：中国的政治中心是__________；中国的经济中心是__________。
	试题答案： 1/北京北京市、2/上海上海市
	学员答题答案：北京市、上海；评卷为正确并得分；
	</pre>
	<AUTHOR>
"""
type SequenceFillAnswer implements FillAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.questionanswer.fillanswer.SequenceFillAnswer") {
	"""每个填空数答案
		<p>
		KEY:填空序号
		VALUE:填空的答案可匹配列表
		</p>
	"""
	sequenceFillCorrectAnswers:Map
	"""填空题答案类型
		@see FillQuestionFillAnswerTypes
		1-散乱无序填空题答案实体    2-按序填空题答案实体     3-按序关联填空题答案实体
	"""
	type:Int!
}
"""按序关联填空题答案实体
	<pre>
	每空答案精确匹配： 适用于前后空格的答案是有关联的。比如：
	试题题目：请写出中国四大名著之一是__________；作者是__________。
	试题答案： 1/红楼梦曹雪芹、2/西游记吴承恩
	学员答题答案：红楼梦、曹雪芹；评卷为正确并得分；
	红楼梦、吴承恩；评卷则给第一个空得分；
	</pre>
	<AUTHOR>
"""
type SequenceRelateFillAnswer implements FillAnswer @type(value:"com.fjhb.ms.exam.answer.v1.kernel.gateway.graphql.response.questionanswer.fillanswer.SequenceRelateFillAnswer") {
	"""正确答案集合"""
	sequenceRelateFillCorrectAnswers:[SequenceFillAnswer]
	"""填空题答案类型
		@see FillQuestionFillAnswerTypes
		1-散乱无序填空题答案实体    2-按序填空题答案实体     3-按序关联填空题答案实体
	"""
	type:Int!
}
"""@author: zhengp
	@since 2021/8/27 10:35
"""
type MapDto @type(value:"com.fjhb.ms.exam.answer.v1.kernel.support.dto.MapDto") {
	key:Int
	answer:String
}
"""简单题
	@author: zhengp
	@since 2021/8/26 14:53
"""
type AskQuestionResponse implements QuestionResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.support.response.AskQuestionResponse") {
	"""已作答答案"""
	askAnswer:String
	"""试题ID"""
	id:String
	"""试题题目"""
	topic:String
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""试题分数"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""试题解析"""
	dissects:String
	"""是否已作答"""
	answered:Boolean!
	"""是否必答"""
	answerRequired:Boolean
	"""标签Code集合"""
	labelCodeList:[String]
}
"""父子题
	@author: zhengp
	@since 2021/8/26 14:53
"""
type FatherQuestionResponse implements QuestionResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.support.response.FatherQuestionResponse") {
	"""子题集合"""
	childQuestions:[QuestionResponse]
	"""试题ID"""
	id:String
	"""试题题目"""
	topic:String
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""试题分数"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""试题解析"""
	dissects:String
	"""是否已作答"""
	answered:Boolean!
	"""是否必答"""
	answerRequired:Boolean
	"""标签Code集合"""
	labelCodeList:[String]
}
"""填空题
	@author: zhengp
	@since 2021/8/26 14:53
"""
type FillQuestionResponse implements QuestionResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.support.response.FillQuestionResponse") {
	"""填空数"""
	fillCount:Int!
	"""已作答答案"""
	fillAnswer:[MapDto]
	"""试题ID"""
	id:String
	"""试题题目"""
	topic:String
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""试题分数"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""试题解析"""
	dissects:String
	"""是否已作答"""
	answered:Boolean!
	"""是否必答"""
	answerRequired:Boolean
	"""标签Code集合"""
	labelCodeList:[String]
}
"""多选题
	@author: zhengp
	@since 2021/8/26 14:53
"""
type MultipleQuestionResponse implements QuestionResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.support.response.MultipleQuestionResponse") {
	"""可选答案列表"""
	answerOptions:[ChooseAnswerOption]
	"""已答答案"""
	multipleAnswer:[String]
	"""填空内容 key是答案id，value是填空的内容"""
	fillContentMap:Map
	"""试题ID"""
	id:String
	"""试题题目"""
	topic:String
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""试题分数"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""试题解析"""
	dissects:String
	"""是否已作答"""
	answered:Boolean!
	"""是否必答"""
	answerRequired:Boolean
	"""标签Code集合"""
	labelCodeList:[String]
}
"""判断题
	@author: zhengp
	@since 2021/8/26 14:53
"""
type OpinionQuestionResponse implements QuestionResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.support.response.OpinionQuestionResponse") {
	"""正确文本"""
	correctAnswerText:String
	"""错误文本"""
	incorrectAnswerText:String
	"""用户答案"""
	opinionAnswer:Boolean
	"""试题ID"""
	id:String
	"""试题题目"""
	topic:String
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""试题分数"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""试题解析"""
	dissects:String
	"""是否已作答"""
	answered:Boolean!
	"""是否必答"""
	answerRequired:Boolean
	"""标签Code集合"""
	labelCodeList:[String]
}
"""判断题
	@author: zhengp
	@since 2021/8/26 14:53
"""
type RadioQuestionResponse implements QuestionResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.support.response.RadioQuestionResponse") {
	"""可选答案列表"""
	answerOptions:[ChooseAnswerOption]
	"""已作答答案"""
	radioAnswer:String
	"""填空内容"""
	fillContent:String
	"""试题ID"""
	id:String
	"""试题题目"""
	topic:String
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""试题分数"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""试题解析"""
	dissects:String
	"""是否已作答"""
	answered:Boolean!
	"""是否必答"""
	answerRequired:Boolean
	"""标签Code集合"""
	labelCodeList:[String]
}
"""@Author: chenzeyu
	@CreateTime: 2024-08-01  19:42
	@Description: 量表题
"""
type ScaleQuestionResponse implements QuestionResponse @type(value:"com.fjhb.ms.exam.answer.v1.kernel.support.response.ScaleQuestionResponse") {
	"""量表类型
		@see ScaleTypes
	"""
	scaleType:Int!
	"""程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
	"""已作答答案"""
	scaleAnswer:Int
	"""试题ID"""
	id:String
	"""试题题目"""
	topic:String
	"""试题类型
		<p>
		说明：
		0 表示混合题型，即该大题下存在多种试题类型的组合
		1 表示单选题
		2 表示多选题
		3 表示填空题
		4 表示判断题
		5 表示简答题
		6 表示父子题
		7 表示量表题
		</p>
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""试题分数"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""试题解析"""
	dissects:String
	"""是否已作答"""
	answered:Boolean!
	"""是否必答"""
	answerRequired:Boolean
	"""标签Code集合"""
	labelCodeList:[String]
}

scalar List
