import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-training-channel-back-gateway'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-training-channel-back-gateway'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum EnterprisePersonSortFieldEnum {
  createdTime = 'createdTime',
  accountType = 'accountType',
  userNameFirstLetter = 'userNameFirstLetter',
  nature = 'nature'
}
export enum TrainingChannelEnum {
  publishedTime = 'publishedTime'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class ContractProviderInfoAdminOwnerRequest {
  servicerIdList?: Array<string>
}

export class TrainingChannelSortKParam {
  sortField?: TrainingChannelEnum
  sortType?: SortTypeEnum
}

export class DateScopeRequest {
  beginTime?: string
  endTime?: string
}

/**
 * 精品课程查询请求参数
 */
export class TrainingChannelSelectCourseRequest {
  /**
   * 专题ID
   */
  trainingChannelId?: string
  /**
   * 课程分类ID
   */
  selectedCourseCategoryId?: string
  /**
   * 课程id集合
   */
  courseIds?: Array<string>
}

export class ContractProviderAdminQueryRequest {
  /**
   * 合同服务商管理员归属信息
   */
  owner?: ContractProviderInfoAdminOwnerRequest
  /**
   * 专题名称
   */
  trainingChannelName?: string
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: AdminUserRequest
  /**
   * 登录认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 角色信息查询
   */
  role?: RoleRequest
  /**
   * 排序
   */
  sortList?: Array<AdminSortRequest>
}

/**
 * 功能描述：账户信息查询条件
@Author： wtl
@Date： 2022年5月11日 15:30:56
 */
export class AccountRequest {
  /**
   * 账户状态 1：正常，2：冻结
@see AccountStatus
   */
  statusList?: Array<number>
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 创建人用户id
   */
  createdUserId?: string
  /**
   * 账户类型 1：企业帐户，2：企业个人帐户，3：个人帐户
   */
  accountTypeList?: Array<number>
  /**
   * 单位id （原始单位id，不会随着id 人员与单位的关系变化而变化）
   */
  unitIdList?: Array<string>
  /**
   * 单位id匹配方式 默认-1、and匹配 2、or匹配
@see MatchTypeConstant
   */
  unitIdMatchType?: number
  /**
   * 来源类型
0-内置 1-项目主网站 2-安卓 3-IOS 4-后台导入 5-迁移数据
   */
  sourceTypes?: Array<number>
}

/**
 * 功能描述：管理员排序
@Author： wtl
@Date： 2021/12/27 10:32
 */
export class AdminSortRequest {
  /**
   * 管理员排序字段
   */
  sortField?: EnterprisePersonSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述：管理员查询条件
@Author： wtl
@Date： 2022年1月25日 15:24:10
 */
export class AdminUserRequest {
  /**
   * 默认为右模糊(0：完全匹配 1：模糊查询，*manageRegionPath* 2：左模糊查询，*manageRegionPath 3:右模糊查询，manageRegionPath*)
管辖地区路径匹配方式
   */
  manageRegionPathType?: number
  /**
   * 管理地区路径集合
   */
  manageRegionPathList?: Array<string>
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 手机号匹配方式，默认为完全匹配(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  phoneMatchType?: number
  /**
   * 工作单位名称
   */
  companyName?: string
  /**
   * 工作单位统一社会信用代码
   */
  companyCode?: string
}

/**
 * 功能描述：登录认证查询条件
@Author： wtl
@Date： 2022年1月26日 09:30:12
 */
export class AuthenticationRequest {
  /**
   * 帐号
   */
  identity?: string
  /**
   * 用户名
   */
  userName?: string
}

/**
 * 功能描述：角色查询条件
@Author： wtl
@Date： 2022年5月11日 11:46:41
 */
export class RoleRequest {
  /**
   * 角色id集合
   */
  roleIdList?: Array<string>
  /**
   * 角色类型
（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
   */
  roleTypeList?: Array<string>
  /**
   * 角色类别
（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
@see RoleCategories
   */
  roleCategoryList?: Array<number>
  /**
   * 排除的角色类别集合
（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管 10:人社超管
11:人社职建处 12:人社就业局 13:超级管理员 14:合同供应商 15:专家 16:电子劳动合同业务角色 320:地区管理员 410:人社行业管理员
411:人社行业省级管理员 412:人社行业市级管理员 413:人社行业区县级管理员 420:人社地区管理员 430:人社业务管理员 440:省级人社主管 450:市级人社主管
460:区县级人社主管 510:培训机构管理员 520:技工院校管理员 530:职业院校管理员 540:政策参与者 550:线上培训机构管理员 560:课件供应商
5001:学徒制_企业管理员 5101:学徒制_培训机构管理员 5201:学徒制_技工院校管理员 5301:学徒制_职业院校管理员 4011:学徒制_人社_省级管理员
4021:学徒制_人社_市级管理员 4031:学徒制_人社_区/县级管理员 6001:揭榜挂帅_企业管理员 6101:揭榜挂帅_培训机构管理员
6201:揭榜挂帅_技工院校管理员 6301:揭榜挂帅_职业院校管理员 6401:揭榜挂帅_人社_省级管理员 6402:揭榜挂帅_人社_市级管理员 6403:揭榜挂帅_人社_区/县级管理员）
@see RoleCategories
   */
  excludeRoleCategoryList?: Array<number>
  /**
   * 角色应用方Id集合
   */
  applicationMemberIdList?: Array<string>
  /**
   * 授予性质 | 1.系统授予 2.用户授予
   */
  natureList?: Array<number>
  /**
   * 排除的角色Code | JGGLY-机构管理员
   */
  excludeRoleCodeList?: Array<string>
}

/**
 * 简略资讯查询条件
 */
export class TrainingChannelNewsSimpleQueryRequest {
  /**
   * 资讯标题
   */
  title?: string
  /**
   * 资讯分类编号
   */
  necId?: string
  /**
   * 发布地区编码
   */
  areaCodePath?: string
  /**
   * 弹窗状态 [-1-全部，1-弹窗，0-不弹窗 默认全部]
   */
  popUpsStatus: number
  /**
   * 资讯状态 [-1-全部，0-草稿，1-发布 默认全部]
   */
  status: number
  /**
   * 专题ID
   */
  specialSubjectIds?: Array<string>
}

export class TrainingChannelCountRequest {
  /**
   * 专题管理员用户id
   */
  userIds?: Array<string>
}

export class TrainingChannelRequest {
  /**
   * 专题ID集合
   */
  ids?: Array<string>
  /**
   * 专题名称
   */
  name?: string
  /**
   * 专题入口名称
   */
  entryName?: string
  /**
   * 专题类型：
1-地区  2-行业  3-班级
   */
  types?: Array<number>
  /**
   * 行业id
   */
  industryId?: string
  /**
   * 地区路径
   */
  regionPath?: string
  /**
   * 启用状态（0：停用 1：启用）
   */
  enable?: boolean
  /**
   * 是否显示在网校
   */
  showOnNetSchool?: boolean
  /**
   * 排序
   */
  sort?: number
  /**
   * 编辑时间范围
   */
  createdDateScope?: DateScopeRequest
  /**
   * 排序
   */
  sortList?: Array<TrainingChannelSortKParam>
  /**
   * 专题管理员用户id
   */
  userIdList?: Array<string>
  /**
   * 单位名称
   */
  unitName?: string
}

export class RegionModel {
  regionId: string
  regionPath: string
  provinceId: string
  provinceName: string
  cityId: string
  cityName: string
  countyId: string
  countyName: string
}

/**
 * @Description 附件请求
<AUTHOR>
@Date 2024/3/20 9:56
 */
export class Attachment {
  /**
   * 附件名称
   */
  name: string
  /**
   * 附件地址
   */
  url: string
}

/**
 * 专题线下集体报名配置返回值
 */
export class TrainingChannelOfflineCollectiveSignUpSettingResponse {
  /**
   * 专题线下集体报名配置信息
   */
  id: string
  /**
   * 专题线下集体报名入口开关
   */
  openEntrySwitch: boolean
  /**
   * 专题线下集体报名入口图片附件
   */
  entryPictureAttachments: Array<Attachment>
  /**
   * 线下集体报名名称
   */
  name: string
  /**
   * 专题线下集体报名模板地址
   */
  templateAttachment: Attachment
  /**
   * 访问链接
   */
  accessUrl: string
  /**
   * 底部文本说明内容id
   */
  bottomDescriptionId: string
  /**
   * 底部文本说明内容
   */
  bottomDescription: string
  /**
   * 报名步骤信息
   */
  signUpSteps: Array<SignUpStepDto>
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 专题线上集体报名配置返回值
 */
export class TrainingChannelOnlineCollectiveSignUpSettingResponse {
  /**
   * 专题线上集体报名配置信息ID
   */
  id: string
  /**
   * 专题线上集体报名入口开关
   */
  openEntrySwitch: boolean
  /**
   * 专题线上集体报名入口图片开关
   */
  openEntryPictureSwitch: boolean
  /**
   * 专题线上集体报名入口图片附件
   */
  entryPictureAttachments: Array<Attachment>
  /**
   * 专题线上集体报名模版地址 json字符串
   */
  templateAttachment: Attachment
  /**
   * 展示报名班级链接地址
   */
  showSignUpClassUrl: string
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 精品课程返回值
 */
export class TrainingChannelSelectCourseResponse {
  /**
   * 专题精品课程ID
   */
  id: string
  /**
   * 专题ID
   */
  trainingChannelId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 精品课程分类ID
   */
  courseCategoryId: string
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 创建人ID
   */
  createdUserId: string
  /**
   * 修改时间
   */
  updatedTime: string
  /**
   * 课程名称
   */
  courseName: string
  /**
   * 课程分类名称
   */
  categoryName: string
}

export class ContractProviderAdminInfoResponse {
  /**
   * 账户信息
   */
  accountInfo: AccountResponse
  /**
   * 管理员用户信息
   */
  userInfo: AdminUserInfoResponse
  /**
   * 用户登录认证信息
   */
  authenticationList: Array<AuthenticationResponse>
  /**
   * 角色信息集合
   */
  roleList: Array<RoleResponse>
}

/**
 * 功能描述：账户信息
 */
export class AccountResponse {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
@see AccountTypes
   */
  accountType: number
  /**
   * 单位信息
   */
  unitInfo: UnitInfoResponse
  /**
   * 所属顶级企业帐户Id
   */
  rootAccountId: string
  /**
   * 帐户状态 1：正常，2：冻结，3：注销
@see AccountStatus
   */
  status: number
  /**
   * 注册方式
0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
@see AccountRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
0、内置，1、项目主网站，2、安卓，3、IOS，4、后台导入，5、迁移数据，6、分销平台项目主网站，7、专题，8、华医网，9、江西管理平台
@see AccountSourceTypes
   */
  sourceType: number
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 功能描述：管理员用户信息
 */
export class AdminUserInfoResponse {
  /**
   * 管辖地区集合
   */
  manageRegionList: Array<RegionModel>
  /**
   * 办公室（所在处/科室）
   */
  office: string
  /**
   * 头像
   */
  photo: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 性别
-1未知，0女，1男
@see Genders
   */
  gender: number
  /**
   * 证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）
   */
  idCardType: string
  /**
   * 证件号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 邮箱
   */
  email: string
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 工作单位统一社会信用代码
   */
  companyCode: string
}

/**
 * 功能描述：帐户认证信息
@Author： wtl
@Date： 2022年5月11日 14:23:18
 */
export class AuthenticationResponse {
  /**
   * 帐号
   */
  identity: string
  /**
   * 认证标识类型
1用户名,2手机,3身份证,4邮箱,5第三方OpenId
   */
  identityType: number
  /**
   * 认证方式状态 1启用，2禁用
@see AuthenticationStatusEnum
   */
  status: number
}

/**
 * 功能描述：角色信息
@Author： wtl
@Date： 2022/1/24 20:17
 */
export class RoleResponse {
  /**
   * 角色id
   */
  roleId: string
  /**
   * 角色名称
   */
  roleName: string
  /**
   * 角色类型
（由底层决定，目前类型：TRAINING_INSTITUTION_MANAGER：施教机构管理员 COURSEWARE_SUPPLIER_MANAGER：课件供应商管理员 CHANNEL_VENDOR_MANAGER：渠道商管理员 STUDENT：学员 PARTICIPATING_UNIT：参训单位 COLLECTIVE：集体缴费 OPERATOR_MANAGER：运营管理员 REGION_MANAGER：地区管理员）
@see com.fjhb.domain.basicdata.api.role.enums.RoleTypes
   */
  roleType: string
  /**
   * 角色类别（1：学员 2：集体报名管理员 3：管理员 4：人社管理员 5：企业管理员 6:人社审批管理员 7:企业经办 8:企业法人 9:企业超管）
@see RoleCategories
   */
  roleCategory: number
  /**
   * 应用方类型(4:子项目 5：单位 6：服务商)
@see SystemMemberTypes
   */
  applicationMemberType: number
  /**
   * 是否冻结，1代表该账户的角色被冻结，其他情况均为未冻结
@see com.fjhb.ms.basicdata.constants.AccountRoleFrozeStatusConstants
   */
  frozeStatus: number
  /**
   * 应用方ID
   */
  applicationMemberId: string
  /**
   * 角色说明
   */
  description: string
}

/**
 * 单位信息模型
 */
export class UnitInfoResponse {
  /**
   * 单位ID
   */
  unitId: string
}

/**
 * 附件返回值
 */
export class AttachmentResponse {
  /**
   * 附件名称
   */
  name: string
  /**
   * 附件地址
   */
  url: string
}

/**
 * 简略资讯信息
 */
export class NewsSimpleResponse {
  /**
   * 资讯编号
   */
  newId: string
  /**
   * 标题
   */
  title: string
  /**
   * 是否置顶
   */
  isTop: boolean
  /**
   * 是否弹窗公告
   */
  isPopUps: boolean
  /**
   * 摘要
   */
  summary: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 分类id
   */
  categoryId: string
  /**
   * 父级id
   */
  parentCategoryId: string
  /**
   * 资讯状态 0 草稿 1正常
   */
  status: number
  /**
   * 发布人编号
   */
  publishUserId: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 发布地区编码
   */
  areaCodePath: string
  /**
   * 专题Id
   */
  specialSubjectId: string
}

export class IndustryModel {
  /**
   * 行业id
   */
  industryId: string
  /**
   * 行业名称
   */
  industryName: string
}

export class NetSchoolResponse {
  /**
   * 门户类型（1：web端 2：移动端）
   */
  portalType: number
  /**
   * 网校域名
   */
  netSchoolDomainName: string
}

export class TrainingChannelCountResponse {
  /**
   * 用户id (专题管理员用户id)
   */
  userId: string
  /**
   * 专题数量
   */
  trainingChannelCount: number
}

export class TrainingChannelDetailResponse {
  /**
   * 专题id
   */
  id: string
  /**
   * 网校id
   */
  servicerId: string
  /**
   * 网校名称
   */
  netSchoolName: string
  /**
   * 网校域名
   */
  netSchoolDoMain: Array<NetSchoolResponse>
  /**
   * 专题入口名称
   */
  entryName: string
  /**
   * 专题名称
   */
  name: string
  /**
   * 域名类型（1：系统默认域名 2：自定义）
   */
  domainNameType: number
  /**
   * 专题域名名称
   */
  domainName: string
  /**
   * 专题类型：
1-地区  2-行业  3-班级
   */
  types: Array<number>
  /**
   * 是否显示在网校
   */
  showOnNetSchool: boolean
  /**
   * PC端专题模板编号
   */
  pcTemplateNo: string
  /**
   * H5端专题模板编号
   */
  h5TemplateNo: string
  /**
   * 启用状态（0：停用 1：启用）
   */
  enable: boolean
  /**
   * 状态（1：草稿 2：正常）
   */
  status: number
  /**
   * 排序号码
   */
  sort: number
  /**
   * 适用地区
   */
  regions: Array<RegionModel>
  /**
   * 适用行业
   */
  industrys: Array<string>
  /**
   * 专题门户
   */
  topic: TrainingChannelTopicResponse
  /**
   * 已配置方案数
   */
  configuredPlans: number
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 是否允许访问
   */
  allowAccess: boolean
}

export class TrainingChannelPageResponse {
  /**
   * 专题id
   */
  id: string
  /**
   * 专题名称
   */
  name: string
  /**
   * 入口名称
   */
  entryName: string
  /**
   * 专题类型：
1-地区  2-行业  3-班级
   */
  types: Array<number>
  /**
   * 是否展示在网校
   */
  showOnNetSchool: boolean
  /**
   * 是否允许访问
   */
  allowAccess: boolean
  /**
   * 网校id
   */
  netSchoolId: string
  /**
   * 网校名称
   */
  netSchoolName: string
  /**
   * 网校域名
   */
  netSchoolDomainName: Array<NetSchoolResponse>
  /**
   * 专题域名
   */
  domainName: string
  /**
   * PC端专题模板编号
   */
  pcTemplateNo: string
  /**
   * H5端专题模板编号
   */
  h5TemplateNo: string
  /**
   * 已配置方案数
   */
  configuredPlans: number
  /**
   * 启用状态（0：停用 1：启用）
   */
  enable: boolean
  /**
   * 排序
   */
  sort: number
  /**
   * 适用地区
   */
  regions: Array<RegionModel>
  /**
   * 适用行业
   */
  industrys: Array<IndustryModel>
  /**
   * 最后编辑时间
   */
  updatedTime: string
  /**
   * 单位名称
   */
  unitName: string
}

export class TrainingChannelTopicPhotoResponse {
  /**
   * 专题门户轮播图id
   */
  id: string
  /**
   * 专题门户轮播图类型：1 web端、2 H5端
   */
  type: number
  /**
   * 专题门户轮播图地址
   */
  pictureUrl: string
  /**
   * 链接地址
   */
  linkUrl: string
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createdTime: string
}

export class TrainingChannelTopicResponse {
  /**
   * 专题门户id
   */
  id: string
  /**
   * logo类型（1：文字、2：图片）
   */
  logoType: number
  /**
   * 专题门户logo名称（专题门户logo类型为文字时，有值）
   */
  logoName: string
  /**
   * 专题门户logo图片地址（专题门户logo类型为图片时，有值）
   */
  logoPictureUrl: string
  /**
   * 客服电话类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.CustomerServicePhoneTypes
   */
  customerServicePhoneType: number
  /**
   * 客服电话
   */
  customerServicePhone: string
  /**
   * 客服电话图片路径
   */
  customerServicePhonePictureUrl: string
  /**
   * 培训流程类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
@see com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.TrainingProcessTypes
   */
  trainingProcessType: number
  /**
   * 培训流程图片
@see com.fjhb.platform.jxjy.v1.api.trainingchannel.event.trainingchannelonlinecollective.Attachment
   */
  trainingProcessAttachments: Array<AttachmentResponse>
  /**
   * 企业微信客服类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
   */
  enterpriseWechatCustomerType: number
  /**
   * 企业微信客服图片
   */
  enterpriseWechatCustomerAttachments: Array<AttachmentResponse>
  /**
   * 咨询时间
   */
  seekTime: string
  /**
   * 专题门户底部落款类型
NET_SCHOOL&#x3D; 1;同本网校
CUSTOM &#x3D; 2；自定义
com.fjhb.platform.jxjy.v1.api.trainingchannel.enums.PortalBottomShowTypes
   */
  bottomShowType: number
  /**
   * 专题门户底部落款内容（专题门户底部落款类型为自定义时 有值）
   */
  bottomShowContent: string
  /**
   * 轮播图集合
   */
  photos: Array<TrainingChannelTopicPhotoResponse>
}

export class SignUpStepDto {
  /**
   * 步骤内容
   */
  content: string
  /**
   * 序号
   */
  index: number
  /**
   * 步骤标题
   */
  title: string
  /**
   * 步骤内容 Id
   */
  contentId: string
}

export class ContractProviderAdminInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ContractProviderAdminInfoResponse>
}

export class TrainingChannelPageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingChannelPageResponse>
}

export class TrainingChannelSelectCourseResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingChannelSelectCourseResponse>
}

export class NewsSimpleResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<NewsSimpleResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据专题Id查找专题线下集体报名配置
   * @param query 查询 graphql 语法文档
   * @param trainingChannelId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getOfflineCollectiveByTrainingChannelIdInSubject(
    trainingChannelId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOfflineCollectiveByTrainingChannelIdInSubject,
    operation?: string
  ): Promise<Response<TrainingChannelOfflineCollectiveSignUpSettingResponse>> {
    return commonRequestApi<TrainingChannelOfflineCollectiveSignUpSettingResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { trainingChannelId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据专题Id查找专题线上集体报名配置
   * @param query 查询 graphql 语法文档
   * @param trainingChannelId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getOnlineCollectiveByTrainingChannelIdInSubject(
    trainingChannelId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineCollectiveByTrainingChannelIdInSubject,
    operation?: string
  ): Promise<Response<TrainingChannelOnlineCollectiveSignUpSettingResponse>> {
    return commonRequestApi<TrainingChannelOnlineCollectiveSignUpSettingResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { trainingChannelId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 子项目-根据专题id查询专题详情
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTrainingChannelDetailById(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTrainingChannelDetailById,
    operation?: string
  ): Promise<Response<TrainingChannelDetailResponse>> {
    return commonRequestApi<TrainingChannelDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 管理员-查询专题管理员下的专题数量
   * @param query 查询 graphql 语法文档
   * @param trainingChannelCountRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listTrainingChannelCount(
    trainingChannelCountRequest: TrainingChannelCountRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTrainingChannelCount,
    operation?: string
  ): Promise<Response<Array<TrainingChannelCountResponse>>> {
    return commonRequestApi<Array<TrainingChannelCountResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { trainingChannelCountRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 专题管理员列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageOnlineSchoolAdminInfoInServicer(
    params: { page?: Page; request?: ContractProviderAdminQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageOnlineSchoolAdminInfoInServicer,
    operation?: string
  ): Promise<Response<ContractProviderAdminInfoResponsePage>> {
    return commonRequestApi<ContractProviderAdminInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 管理员-分页查询专题信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTrainingChannelInfo(
    params: { page?: Page; request?: TrainingChannelRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageTrainingChannelInfo,
    operation?: string
  ): Promise<Response<TrainingChannelPageResponsePage>> {
    return commonRequestApi<TrainingChannelPageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 专题管理员 - 专题分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTrainingChannelInfoInTrainingChannelAdmin(
    params: { page?: Page; request?: TrainingChannelRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageTrainingChannelInfoInTrainingChannelAdmin,
    operation?: string
  ): Promise<Response<TrainingChannelPageResponsePage>> {
    return commonRequestApi<TrainingChannelPageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询精品课程
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageTrainingChannelSelectCourseInSubject(
    params: { page?: Page; request?: TrainingChannelSelectCourseRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageTrainingChannelSelectCourseInSubject,
    operation?: string
  ): Promise<Response<TrainingChannelSelectCourseResponsePage>> {
    return commonRequestApi<TrainingChannelSelectCourseResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 专题管理员 - 咨询分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTrainingChannelSimpleNewsInTrainingChannelAdmin(
    params: { page?: Page; queryRequest?: TrainingChannelNewsSimpleQueryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageTrainingChannelSimpleNewsInTrainingChannelAdmin,
    operation?: string
  ): Promise<Response<NewsSimpleResponsePage>> {
    return commonRequestApi<NewsSimpleResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
