import { CourseResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { TeacherResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

class DistributionCourseList {
  /**
   * 课程ID
   */
  courseId = ''
  /**
   * 课程名称
   */
  courseName = ''
  /**
   * 讲师ID
   */
  teacherIds: string[] = []
  /**
   * 讲师名称
   */
  teacherNames = ''
  /**
   * 学时
   */
  period: number = null
  /**
   * 创建时间
   */
  createTime = ''

  static from(dto: CourseResponse, teacherMap: TeacherResponse[]) {
    const vo = new DistributionCourseList()
    vo.courseId = dto.id
    vo.courseName = dto.name
    vo.teacherIds = dto.teacherIds
    const name = dto.teacherIds.map(res => teacherMap.find(item => res === item.id)?.name) || ['']
    vo.teacherNames = name.join('、')
    vo.period = dto.period
    vo.createTime = dto.createTime
    return vo
  }
}

export default DistributionCourseList
