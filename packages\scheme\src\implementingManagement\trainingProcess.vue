<template>
  <div class="f-p15">
    <el-tabs v-model="activeName" type="card" class="m-tab-card">
      <el-tab-pane label="心得管理" name="first" v-if="isShowExperienceTab">
        <experience-manage-tab :scheme-id="schemeId" />
      </el-tab-pane>
      <el-tab-pane label="问卷管理" name="second" lazy>
        <question-manage-tab :schemeId="schemeId" />
      </el-tab-pane>
      <el-tab-pane label="期别管理" name="third" v-if="isShowCycleManage">
        <cycle-management :schemeId="schemeId" :trainingMode="trainingMode"></cycle-management>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import ExperienceManageTab from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/eperience-manage-tab.vue'
  import CycleManagement from './__components__/cycleManagement.vue'
  import QuestionManageTab from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/question-manage-tab.vue'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import PeriodImplementBase from '@api/service/management/implement/models/PeriodImplementBase'

  @Component({
    components: {
      CycleManagement,
      QuestionManageTab,
      ExperienceManageTab
    }
  })
  export default class extends Vue {
    /**
     * 培训形式
     */
    @Prop({
      type: String,
      default: ''
    })
    trainingMode: string

    // 方案id
    @Prop({
      type: String,
      required: true
    })
    schemeId: string

    // 激活项
    activeName = 'first'

    /// 培训形式枚举复制
    TrainingModeEnum = TrainingModeEnum

    /**
     * 方案是否配置心得tab
     */
    isShowExperienceTab = false

    // 是否显示期别管理
    get isShowCycleManage() {
      return this.trainingMode === TrainingModeEnum.mixed || this.trainingMode === TrainingModeEnum.offline
    }

    async created() {
      this.isShowExperienceTab = await PeriodImplementBase.getLearningExperienceOpen(this.schemeId)
      if (!this.isShowExperienceTab) {
        this.activeName = 'second'
      }
    }
  }
</script>

<style scoped lang="scss"></style>
