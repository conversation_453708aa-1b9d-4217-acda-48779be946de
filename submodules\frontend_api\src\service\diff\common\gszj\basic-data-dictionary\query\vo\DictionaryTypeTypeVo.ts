import { BusinessDataDictionaryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { IdCardTypeModelRequest, IdCardTypeModelResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import IdCardTypeVo from '@api/service/common/basic-data-dictionary/query/vo/IdCardTypeVo'

export default class DictionaryTypeTypeVo {
  /**
   * id
   */
  id = ''
  /**
   * 码
   */
  code = 0
  /**
   * 类型名称
   */
  name = ''
  /**
   * 排序
   */
  sort = 0
  /**
   * 禁用
   */
  disabled = false
  /**
   * 是否可选
   */
  select = false

  static from(dto: BusinessDataDictionaryResponse) {
    const vo = new DictionaryTypeTypeVo()
    vo.id = dto.id
    vo.code = dto.code
    vo.name = dto.name
    vo.sort = dto.sort
    vo.disabled = dto.available === 0
    return vo
  }
}
