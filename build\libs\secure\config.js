const path = require('path')
const argv = require('yargs-parser')(process.argv.slice(2))
module.exports = {
  projectPath: path.resolve(process.cwd(), 'node_modules/@hbfe/security-toolkit/src/.cache/ui-assigns.json'),
  cachePath: path.resolve(__dirname, '../../../.cache'),
  hbfeModulePath: path.resolve(process.cwd(), 'node_modules/@hbfe'),
  groupFileName: 'group-tree.json',
  permissionFileName: 'permission-tree.json',
  uiAssignsFileName: 'ui-assigns.json',
  apiModuleName: 'security_port/ui-assigns.json',
  uiInvoke: path.resolve(process.cwd(), 'node_modules/@hbfe/security-toolkit/src/.cache/ui-invokes.json'),
  BizComponentPath: path.resolve(process.cwd(), argv.BizComponentPath ?? 'src/components/biz')
}
