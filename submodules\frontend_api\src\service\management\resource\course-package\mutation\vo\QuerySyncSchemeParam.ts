import { CourserPackageSyncSchemeRequest } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
export default class QuerySyncSchemeParam {
  /**
   * 课程包id(必填)
   */
  coursePackageId = ''
  /**
   * 方案集合
   */
  schemeIds: Array<string> = []
  /**
   * 数据状态  1一致 0不一致
   @see
   */
  dataStatus: number = null
  /**
   * 同步状态 0未同步 1同步成功 2同步中 3同步失败
   @see UsedStatus
   */
  syncStatus: number = null

  static to(vo: QuerySyncSchemeParam) {
    const dto = new CourserPackageSyncSchemeRequest()
    dto.schemeIds = vo.schemeIds && vo.schemeIds.length ? vo.schemeIds : undefined
    dto.dataStatus = vo.dataStatus
    dto.syncStatus = vo.syncStatus
    dto.coursePackageId = vo.coursePackageId
    return dto
  }
}
