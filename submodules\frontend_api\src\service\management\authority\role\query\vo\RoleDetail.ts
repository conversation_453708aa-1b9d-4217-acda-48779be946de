import AdminAccount from '@api/service/management/authority/role/query/vo/AdminAccount'
import { UiPage } from '@hbfe/common'
import Mockjs from 'mockjs'

class RoleDetail {
  id = ''
  name = ''
  description = ''
  createTime = ''
  enable = false
  adminAccountList: Array<AdminAccount> = new Array<AdminAccount>()
  /**
   * 根据角色id查询被引用该角色的管理员账号
   * @param page
   */

  async getUsedAdminAccount(page?: UiPage) {
    try {
      console.log('page参数=', page)
      this.adminAccountList = Mockjs.mock({
        [`data|5`]: [
          {
            id: '@uuid',
            name: '@csentence(10)',
            createTime: '@date("yyyy-MM-dd HH:mm:ss")',
            enable: '@boolean',
            account: '@phone'
          }
        ]
      }).data
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/query/vo/RoleDetail.ts所处方法，getUsedAdminAccount',
        e
      )
    }
  }

  async query() {
    try {
      console.log(
        '调用了query方法，返回值=',
        new Promise(resolve => {
          setTimeout(() => {
            const result = Mockjs.mock({
              [`data`]: {
                id: '@uuid',
                name: '@csentence(10)',
                createTime: '@date("yyyy-MM-dd HH:mm:ss")',
                enable: '@boolean',
                description: '@csentence(100)'
              }
            }).data
            this.id = result.id
            this.name = result.name
            this.createTime = result.createTime
            this.enable = result.enable
            this.description = result.description
            resolve({})
          }, 3000)
        })
      )
      return new Promise(resolve => {
        setTimeout(() => {
          const result = Mockjs.mock({
            [`data`]: {
              id: '@uuid',
              name: '@csentence(10)',
              createTime: '@date("yyyy-MM-dd HH:mm:ss")',
              enable: '@boolean',
              description: '@csentence(100)'
            }
          }).data
          this.id = result.id
          this.name = result.name
          this.createTime = result.createTime
          this.enable = result.enable
          this.description = result.description
          resolve({})
        }, 3000)
      })
    } catch (e) {
      console.log('报错了，所处位置/service/management/authority/role/query/vo/RoleDetail.ts所处方法，query', e)
    }
  }
}

export default RoleDetail
