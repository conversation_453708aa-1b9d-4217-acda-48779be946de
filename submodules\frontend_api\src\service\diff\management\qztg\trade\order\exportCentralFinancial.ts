import exportCentralFinancialDataInServicer from '@api/diff-gateway/qztg-data-export-gateway-backstage'
import QueryOrderListVo from '@api/service/management/trade/single/order/query/vo/QueryOrderListVo'
export default class ExportCentralFinancialDataInServicer {
  /**
   * 导出中心财务数据 ----泉州提高差异化
   * @param params 查询参数 OrderRequest
   * @returns
   */
  async export(params: QueryOrderListVo) {
    const param = await params.to(false)
    const res = await exportCentralFinancialDataInServicer.exportCentralFinancialDataInServicer({
      request: param
    })
    return res
  }
}
