<!--方案类型展示文本-->
<template>
  <el-tag type="primary" effect="dark" size="mini">{{ CommonSchemeText }}</el-tag>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import SchemeDetail from '@api/service/common/scheme/model/SchemeDetail'

  @Component({})
  export default class extends Vue {
    @Prop({ type: String, default: '' }) trainingMode: TrainingModeEnum

    @Prop() schemeType: string | number
    @Prop({ type: Boolean, default: true }) isShowTrainingMode: boolean
    /**
     * 名称组合
     */
    get CommonSchemeText() {
      return SchemeDetail.getSchemeTypeShowText({
        trainingMode: this.trainingMode,
        schemeType: this.schemeType,
        isShowTrainingMode: this.isShowTrainingMode
      })
    }
  }
</script>
