/**
 * @description 考试学习方式配置
 */
class ExamLearningConfig {
  /**
   * 考试id
   */
  id: string
  /**
   * 考试名称
   */
  name: string
  /**
   * 考试说明
   */
  description: string
  /**
   * 出卷配置ID
   */
  paperPublishConfigureId: string
  /**
   * 允许考试次数， -1表示不限次数
   */
  allowCount: number
  /**
   * 允许考试的开始时间
   */
  allowStartTime: string
  /**
   * 允许考试的截止时间
   */
  allowEndTime: string
  /**
   * 考试时长，单位秒
   */
  timeLength: number
  /**
   * 及格分
   */
  qualifiedScore: number
  /**
   * 是否开放试题解析
   */
  openDissects: boolean
  /**
   * 试题提交答案后是否可以重答
   */
  questionAgainAnswer: boolean
  /**
   * 试题呈现方式，1整卷，2大题，3单题
   */
  questionDisplay: number
  /**
   * 多选题漏选得分方式，0不得分，1得全部分数，2得一半分数，3每个选项按平均得分
   */
  multipleMissScorePattern: number
  /**
   * 操作类型
   */
  operation: number
  /**
   * 合格后是否允许继续作答
   */
  allowAnswerIfQualified: boolean
  /**
   * 成绩是否公布
   */
  gradesWhetherHide: boolean
  /**
   * 考试方式 0 随考随到，1 固定考试
   */
  examType: number
}

export default ExamLearningConfig
