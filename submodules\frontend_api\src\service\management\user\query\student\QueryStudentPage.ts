import { Page, UiPage } from '@hbfe/common'
import QueryStudentPageParams from '@api/service/management/user/query/student/vo/QueryStudentPageParams'
import StudentListDetail from '@api/service/management/user/query/student/vo/StudentListDetail'
import MsBasicDataQueryBackstageGateway from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

class QueryStudentPage {
  page: Page
  queryParams: QueryStudentPageParams = new QueryStudentPageParams()
  list: Array<StudentListDetail> = new Array<StudentListDetail>()

  constructor() {
    this.page = new UiPage(this.search, this.search)
  }

  /**
   * 查询学员分页列表
   */
  async search() {
    const list = await MsBasicDataQueryBackstageGateway.pageStudentInfoInServicer({
      page: this.page,
      request: this.queryParams.toJSON()
    })
    this.page.totalPageSize = list.data.totalPageSize
    this.page.totalSize = list.data.totalSize
    this.list = list.data.currentPageData.map(StudentListDetail.from)
  }
}

export default QueryStudentPage
