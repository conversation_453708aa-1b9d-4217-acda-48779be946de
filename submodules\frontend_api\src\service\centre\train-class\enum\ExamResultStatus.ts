import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 考试结果枚举
 */
export enum ExamResultStatusEnum {
  // 1：已合格
  Qualified = 1,
  // 2：未合格
  Unqualified,
  // 3：无需考试
  Innate
}

class ExamResultStatus extends AbstractEnum<ExamResultStatusEnum> {
  static enum = ExamResultStatusEnum

  constructor(status?: ExamResultStatusEnum) {
    super()
    this.current = status
    this.map.set(ExamResultStatusEnum.Qualified, '已合格')
    this.map.set(ExamResultStatusEnum.Unqualified, '未合格')
    this.map.set(ExamResultStatusEnum.Innate, '无需考试')
  }
}

export default new ExamResultStatus()
