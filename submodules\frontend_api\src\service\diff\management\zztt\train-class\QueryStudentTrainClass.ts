import CourseLearningBackstage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import MsMySchemeQueryFrontGatewayCourseLearningForeStage, {
  default as MsSchemeLearningQueryBackstage,
  SchemeSkuPropertyResponse,
  StudentSchemeLearningResponse,
  StudentSchemeLearningSortRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import studentCourseLearningQuery, {
  SimulateStudentSchemeLearningRequest,
  StudentTrainingResultSimulateRequest
} from '@api/platform-gateway/student-course-learning-query-back-gateway'
import DataResolve from '@api/service/common/utils/DataResolve'
import QueryStudentTrainClassListVo from '@api/service/management/train-class/query/vo/QueryStudentTrainClassListVo'
import { Page } from '@hbfe/common'
import TradeQueryFront from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import StudentTrainClassDetailVo from '@api/service/diff/management/zztt/train-class/model/StudentTrainClassDetailVo'
import QueryMyTrainClassDetail from '@api/service/management/train-class/query/QueryMyTrainClassDetail'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { getStudentSchemeLearningInServicer } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage/graphql-importer'
import {
  ComplexSkuPropertyResponse,
  SchemeSkuInfo,
  SkuPropertyConvertUtils
} from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import SkuPropertyConvertUtilsV2 from '@api/service/management/train-class/Utils/SkuPropertyConvertUtilsV2'
import ConfigJsonUtil from '@api/service/management/train-class/Utils/ConfigJsonUtil'
import QueryPlatform from '@api/service/diff/common/zztt/dictionary/QueryPlatform'
import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/zztt/trade/enums/SaleChannelType'
import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
import QueryStudentTrainClassMain from '@api/service/management/train-class/query/QueryStudentTrainClass'
import QueryStudentStudy from '@api/service/management/train-class/offlinePart/QueryStudentStudy'
/**
 * @description 查询学员已报班级
 */

class QueryStudentTrainClass extends QueryStudentTrainClassMain {
  /**
   * 查询学员已报培训方案列表
   */
  async queryStudentTrainClassList(
    page: Page,
    queryParams: QueryStudentTrainClassListVo,
    sort?: Array<StudentSchemeLearningSortRequest>
  ): Promise<StudentTrainClassDetailVo[]> {
    let result = [] as StudentTrainClassDetailVo[]
    const response = await MsSchemeLearningQueryBackstage.pageStudentSchemeLearningInServicer({
      page,
      request: queryParams.to(),
      sort: sort
    })
    page.totalSize = response.data?.totalSize ?? 0
    page.totalPageSize = response.data?.totalPageSize ?? 0
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = await QueryStudentTrainClass.ConvertToStudentTrainClassList(response.data.currentPageData)
    }
    console.log('StudentTrainClassList', result)

    // 查询班级模拟数据
    if (result?.length) {
      const studentNoList = new Array<string>()
      const schemeIds = new Array<string>()
      const qualificationIdList = new Array<string>()
      result.map((item) => {
        if (item.studentNo && !studentNoList.includes(item.studentNo)) {
          studentNoList.push(item.studentNo)
        }
        if (item.basicInfo?.schemeId && !schemeIds.includes(item.basicInfo.schemeId)) {
          schemeIds.push(item.basicInfo.schemeId)
        }
        if (item.qualificationId) {
          qualificationIdList.push(item.qualificationId)
        }
      })
      // 查询学习心得参与统计
      const experienceRes = await CourseLearningBackstage.statisticsLearningExperienceParticipatedBySchemeIdInServicer({
        schemeIds,
        userId: queryParams.userId
      })
      result.forEach((item) => {
        const experienceCount = experienceRes.data.find((experience) => experience.schemeId === item.basicInfo.schemeId)
        if (experienceCount) {
          item.assessResult.learningExperienceCount = experienceCount.count
        }
      })
      const querySchemeSimulateRequest = new StudentTrainingResultSimulateRequest()
      querySchemeSimulateRequest.studentNos = studentNoList
      querySchemeSimulateRequest.studentSchemeLearning = new SimulateStudentSchemeLearningRequest()
      querySchemeSimulateRequest.studentSchemeLearning.schemeIds = schemeIds

      const querySchemeSimulateResult =
        await studentCourseLearningQuery.getStudentTrainingResultSimulateResponseInServicer(querySchemeSimulateRequest)

      if (querySchemeSimulateResult?.data?.length) {
        result.map((item) => {
          const findSimulate = querySchemeSimulateResult.data.find(
            (scheme) =>
              scheme.studentNo === item.studentNo && scheme.studentSchemeLearning?.schemeId === item.basicInfo?.schemeId
          )
          if (findSimulate) {
            item.haveSimulate = true
            item.onlyHaveExamSimulate = findSimulate.type == 2
            item.simulatePassTime = findSimulate.studentSchemeLearning?.qualifiedTime
            item.simulateExamScore = findSimulate.studentSchemeLearning.score
            item.simulateExamTime.begin = findSimulate.recordCreatedTime
            item.simulateExamTime.end = findSimulate.recordCreatedTime
          }
        })
      }
      // 是否智能学习配置
      const IntelligenceLearningObj = new IntelligenceLearningModule()
      // const IntelligenceLearningMap = await IntelligenceLearningObj.doBatchCheck(schemeIds)
      // result.map(item => {
      //   const intelligentStatus = IntelligenceLearningMap[item.basicInfo.schemeId]
      //   item.isIntelligentLearning = intelligentStatus === 'true' || false
      // })
      const taskres = await IntelligenceLearningObj.doQuerylearningStatus(qualificationIdList)
      result.map((item) => {
        taskres.map((sitem: any) => {
          // 执行失败返回失败原因
          if (item.qualificationId == sitem.qualificationId) {
            item.isIntelligentLearning = true
            item.failReason = sitem.message
            item.IntelligentLearningResult = sitem.result
          }
        })
      })
    }
    if (result && result.length) {
      await this.supplementStudentChannelInfo(result)
    }
    // 批量查询学员期别学习信息
    await QueryStudentStudy.batchQueryStudentPeriodStudy(result)
    return result
  }

  /**
   * 补充学员渠道信息及第三方信息(上面接口需要补充渠道信息调此方法)
   */
  async supplementStudentChannelInfo(studentTrainClassList: StudentTrainClassDetailVo[]) {
    if (!studentTrainClassList?.length) return
    const orderNos = studentTrainClassList.map((item) => item.learningRegister.orderNo)
    const res = await TradeQueryFront.pageOrderInServicer({
      page: { pageNo: 1, pageSize: orderNos.length },
      request: {
        orderNoList: orderNos
      }
    })
    await QueryPlatform.queryList()
    studentTrainClassList.forEach((item) => {
      const orderInfo = res.data?.currentPageData?.find((ite) => ite.orderNo === item.learningRegister.orderNo)
      if (orderInfo) {
        item.saleChannel = orderInfo.saleChannel
        item.saleChannelName = orderInfo.saleChannelName
        if (orderInfo.saleChannel == SaleChannelEnum.huayi) {
          item.thirdPartyPlatform = SaleChannelType.map.get(orderInfo.saleChannel)
        } else if (orderInfo.subOrderItems?.[0]?.deliveryCommoditySku?.tppTypeId) {
          item.thirdPartyPlatform = QueryPlatform.map.get(
            orderInfo.subOrderItems[0].deliveryCommoditySku.tppTypeId
          )?.name
        }
      }
    })
  }
  static async ConvertToStudentTrainClassList(
    responses: StudentSchemeLearningResponse[]
  ): Promise<StudentTrainClassDetailVo[]> {
    const map = new Map<string, StudentSchemeLearningResponse>()
    const result = [] as StudentTrainClassDetailVo[]
    /** 遍历接口返回值并实例各项 **/
    responses?.forEach((response) => {
      map.set(response.qualificationId, response)
      const detail = new StudentTrainClassDetailVo()
      Object.assign(detail, response)
      result.push(detail)
    })
    /** 构建一个方案信息列表、用于批量查询信息 **/
    const schemeDetailList = result.map((item) => {
      const opt = new QueryMyTrainClassDetail()
      opt.qualificationId = item.qualificationId ?? null
      return opt
    })

    for (const [key, value] of map.entries()) {
      const target = schemeDetailList.find((el) => el.qualificationId === key)
      if (target) {
        target.studentSchemeLearningResponse = value
        target.schemeId = value?.scheme?.schemeId
        target.getInfoFromStudentResponse(value)
      }
    }
    /** 批量查询sku属性 **/
    const schemeSkuInfos = [] as SchemeSkuInfo[]
    // SKU 优化 缓存版本
    const skuInfoByCache = new Array<SchemeSkuPropertyResponse>()
    for (const [key, value] of map.entries()) {
      skuInfoByCache.push(value.scheme.skuProperty)
      const opt = new SchemeSkuInfo(value.scheme.schemeId, value.scheme.skuProperty as ComplexSkuPropertyResponse)
      schemeSkuInfos.push(opt)
    }
    await SkuPropertyConvertUtilsV2.queryAllSku(skuInfoByCache)
    const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(schemeSkuInfos)
    schemeDetailList.forEach((item) => {
      const skuInfo = skuInfos?.find((el) => el.id === item.schemeId)
      if (skuInfo) item.trainClassDetail.trainClassBaseInfo.skuProperty = skuInfo.skuName
    })
    /** 批量请求方案json **/
    const schemeIdList = [...new Set(schemeDetailList.map((el) => el.schemeId)?.filter(Boolean))]
    const configJsonUtil = new ConfigJsonUtil()
    const schemeJSONMap = await configJsonUtil.batchQuerySchemeJsonConfigRespMap(schemeIdList)
    schemeDetailList.forEach((item) => {
      const schemeJSONResp = schemeJSONMap.get(item.schemeId)
      if (schemeJSONResp) {
        item.getInfoFromJSON(schemeJSONResp)
      }
    })
    /** 回填到返回数组 **/
    result.forEach((item) => {
      const target = schemeDetailList.find((el) => el.qualificationId === item.qualificationId)
      item.trainClassDetail = target.trainClassDetail ?? null
      item.trainClassJsonConfig = target?.jsonString ?? null
      StudentTrainClassDetailVo.convertFromTrainClassDetail(item)
      StudentTrainClassDetailVo.fillAssessInfo(item)
    })
    return result
  }
}

export default QueryStudentTrainClass
