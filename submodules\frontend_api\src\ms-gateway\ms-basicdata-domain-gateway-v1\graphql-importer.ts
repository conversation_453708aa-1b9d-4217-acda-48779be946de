import findBuildInRole from './queries/findBuildInRole.graphql'
import findCurrentAssignableRoleList from './queries/findCurrentAssignableRoleList.graphql'
import findCurrentFunctionalAuthorityList from './queries/findCurrentFunctionalAuthorityList.graphql'
import findCurrentRoleList from './queries/findCurrentRoleList.graphql'
import findCurrentRoleListByArgs from './queries/findCurrentRoleListByArgs.graphql'
import findCurrentRoleListNew from './queries/findCurrentRoleListNew.graphql'
import findCurrentRoleListPlus from './queries/findCurrentRoleListPlus.graphql'
import findFunctionalAuthorityByRoleId from './queries/findFunctionalAuthorityByRoleId.graphql'
import findFunctionalAuthorityByRoleIdNew from './queries/findFunctionalAuthorityByRoleIdNew.graphql'
import findFunctionalAuthorityByRoleIds from './queries/findFunctionalAuthorityByRoleIds.graphql'
import findFunctionalAuthorityByRoleIdsNew from './queries/findFunctionalAuthorityByRoleIdsNew.graphql'
import findLoginUserOpenId from './queries/findLoginUserOpenId.graphql'
import findRoleByAccountId from './queries/findRoleByAccountId.graphql'
import findRoleByAccountIdOrUnitIdForPersonAccount from './queries/findRoleByAccountIdOrUnitIdForPersonAccount.graphql'
import findRoleByBusinessDomainUnitId from './queries/findRoleByBusinessDomainUnitId.graphql'
import findRoleByCategory from './queries/findRoleByCategory.graphql'
import findRoleById from './queries/findRoleById.graphql'
import findRoleByOwner from './queries/findRoleByOwner.graphql'
import getCurrentUserServicer from './queries/getCurrentUserServicer.graphql'
import ChangePhoneByAdmin from './mutates/ChangePhoneByAdmin.graphql'
import ResetAdminPasswordWithResponse from './mutates/ResetAdminPasswordWithResponse.graphql'
import ResetPassword from './mutates/ResetPassword.graphql'
import ResetPasswordWithResponse from './mutates/ResetPasswordWithResponse.graphql'
import addStudentCertificateInfo from './mutates/addStudentCertificateInfo.graphql'
import applyBindAndReBindWeChatOpenPlatform from './mutates/applyBindAndReBindWeChatOpenPlatform.graphql'
import applyBindWeChatOpenPlatformAndValidLogin from './mutates/applyBindWeChatOpenPlatformAndValidLogin.graphql'
import applyBindWeChatOpenPlatformLoginAccount from './mutates/applyBindWeChatOpenPlatformLoginAccount.graphql'
import applyCaptcha from './mutates/applyCaptcha.graphql'
import applyChangeEnterpriseTokenList from './mutates/applyChangeEnterpriseTokenList.graphql'
import applyChangePolicyActorUnitAuthorizeTokenList from './mutates/applyChangePolicyActorUnitAuthorizeTokenList.graphql'
import applyCurrentPhoneSmsCode from './mutates/applyCurrentPhoneSmsCode.graphql'
import applyEnterpriseMetadata from './mutates/applyEnterpriseMetadata.graphql'
import applyLoginByOpenId from './mutates/applyLoginByOpenId.graphql'
import applyOpenIdByServicerId from './mutates/applyOpenIdByServicerId.graphql'
import applyScanCodeOpenIdByServicerId from './mutates/applyScanCodeOpenIdByServicerId.graphql'
import applySecureCaptcha from './mutates/applySecureCaptcha.graphql'
import applySmsCode from './mutates/applySmsCode.graphql'
import applySmsCodeForChangingPhone from './mutates/applySmsCodeForChangingPhone.graphql'
import applySmsCodeForLoginDistributor from './mutates/applySmsCodeForLoginDistributor.graphql'
import applySmsCodeForLoginDistributorNeedLogin from './mutates/applySmsCodeForLoginDistributorNeedLogin.graphql'
import applyValidFacialRecognitionV1 from './mutates/applyValidFacialRecognitionV1.graphql'
import applyValidFacialRecognitionV1Web from './mutates/applyValidFacialRecognitionV1Web.graphql'
import applyValidFacialRecognitionVerifyResult from './mutates/applyValidFacialRecognitionVerifyResult.graphql'
import applyValidFacialRecognitionVerifyResultAndBindWeChatOpenPlatform from './mutates/applyValidFacialRecognitionVerifyResultAndBindWeChatOpenPlatform.graphql'
import applyValidFacialRecognitionVerifyResultTokenList from './mutates/applyValidFacialRecognitionVerifyResultTokenList.graphql'
import bindLoginAccountOpenPlatform from './mutates/bindLoginAccountOpenPlatform.graphql'
import bindPhoneForCurrentAccount from './mutates/bindPhoneForCurrentAccount.graphql'
import bindPhoneForCurrentUser from './mutates/bindPhoneForCurrentUser.graphql'
import changeCollectiveRegisterPhone from './mutates/changeCollectiveRegisterPhone.graphql'
import changeNewPhone from './mutates/changeNewPhone.graphql'
import changeNewPhoneForFxs from './mutates/changeNewPhoneForFxs.graphql'
import changeNewPhoneWithResponse from './mutates/changeNewPhoneWithResponse.graphql'
import changePasswordByCurrent from './mutates/changePasswordByCurrent.graphql'
import changePasswordByCurrentWithResponse from './mutates/changePasswordByCurrentWithResponse.graphql'
import changePasswordByForceModifyInitPassword from './mutates/changePasswordByForceModifyInitPassword.graphql'
import changePhone from './mutates/changePhone.graphql'
import checkExistUnitByName from './mutates/checkExistUnitByName.graphql'
import checkOnlineAdminAuthentication from './mutates/checkOnlineAdminAuthentication.graphql'
import checkUserAuthentication from './mutates/checkUserAuthentication.graphql'
import createAdministratorAccount from './mutates/createAdministratorAccount.graphql'
import createEnterprise from './mutates/createEnterprise.graphql'
import createEnterpriseManager from './mutates/createEnterpriseManager.graphql'
import createMOHRSSAdmin from './mutates/createMOHRSSAdmin.graphql'
import createMOHRSSSubordinateAdmin from './mutates/createMOHRSSSubordinateAdmin.graphql'
import createOnlineSchoolStudent from './mutates/createOnlineSchoolStudent.graphql'
import createOnlineSchoolSubAdmin from './mutates/createOnlineSchoolSubAdmin.graphql'
import createOnlineSchoolSubAdminByToken from './mutates/createOnlineSchoolSubAdminByToken.graphql'
import createRole from './mutates/createRole.graphql'
import createRoleByAdminType from './mutates/createRoleByAdminType.graphql'
import createRoleNew from './mutates/createRoleNew.graphql'
import createServiceProviderAccount from './mutates/createServiceProviderAccount.graphql'
import createSubProjectAdministrator from './mutates/createSubProjectAdministrator.graphql'
import createSystemInternalRole from './mutates/createSystemInternalRole.graphql'
import createSystemInternalRoles from './mutates/createSystemInternalRoles.graphql'
import deleteCertificateInfo from './mutates/deleteCertificateInfo.graphql'
import disableEnterpriseManager from './mutates/disableEnterpriseManager.graphql'
import enableEnterpriseManager from './mutates/enableEnterpriseManager.graphql'
import existUser from './mutates/existUser.graphql'
import forgetPassword from './mutates/forgetPassword.graphql'
import freezeAccount from './mutates/freezeAccount.graphql'
import immediateResetPassword from './mutates/immediateResetPassword.graphql'
import initUnitTree from './mutates/initUnitTree.graphql'
import loginAndBindOpenPlatform from './mutates/loginAndBindOpenPlatform.graphql'
import loginAndBindOpenPlatformV2 from './mutates/loginAndBindOpenPlatformV2.graphql'
import registerCollectiveRegisterAdmin from './mutates/registerCollectiveRegisterAdmin.graphql'
import registerEnterPriseManagerForMZT from './mutates/registerEnterPriseManagerForMZT.graphql'
import registerEnterpriseAccount from './mutates/registerEnterpriseAccount.graphql'
import registerEnterpriseManager from './mutates/registerEnterpriseManager.graphql'
import registerStudent from './mutates/registerStudent.graphql'
import registerStudentV2 from './mutates/registerStudentV2.graphql'
import removeRoleWithAdminTypeById from './mutates/removeRoleWithAdminTypeById.graphql'
import removeRoleWithNoAuthRelById from './mutates/removeRoleWithNoAuthRelById.graphql'
import removeSystemInternalRole from './mutates/removeSystemInternalRole.graphql'
import resumeAccount from './mutates/resumeAccount.graphql'
import unbindPhone from './mutates/unbindPhone.graphql'
import unbindPhoneForCurrentUser from './mutates/unbindPhoneForCurrentUser.graphql'
import unbindWeChatOpenPlatform from './mutates/unbindWeChatOpenPlatform.graphql'
import updateAdministratorAccount from './mutates/updateAdministratorAccount.graphql'
import updateCollectiveRegister from './mutates/updateCollectiveRegister.graphql'
import updateCollectiveRegisterAccountInfo from './mutates/updateCollectiveRegisterAccountInfo.graphql'
import updateEnterprise from './mutates/updateEnterprise.graphql'
import updateEnterpriseManager from './mutates/updateEnterpriseManager.graphql'
import updateMOHRSSAdmin from './mutates/updateMOHRSSAdmin.graphql'
import updateMOHRSSSubordinateAdmin from './mutates/updateMOHRSSSubordinateAdmin.graphql'
import updateOnlineAdministratorAccount from './mutates/updateOnlineAdministratorAccount.graphql'
import updateOnlineSchoolSubAdmin from './mutates/updateOnlineSchoolSubAdmin.graphql'
import updateOnlineSchoolSubAdminByToken from './mutates/updateOnlineSchoolSubAdminByToken.graphql'
import updateRole from './mutates/updateRole.graphql'
import updateRoleByAdminType from './mutates/updateRoleByAdminType.graphql'
import updateServiceProviderAccount from './mutates/updateServiceProviderAccount.graphql'
import updateStudent from './mutates/updateStudent.graphql'
import updateStudentBasicInfo from './mutates/updateStudentBasicInfo.graphql'
import updateStudentByAdmin from './mutates/updateStudentByAdmin.graphql'
import updateStudentCertificateInfo from './mutates/updateStudentCertificateInfo.graphql'
import updateSystemInternalRole from './mutates/updateSystemInternalRole.graphql'
import validCaptcha from './mutates/validCaptcha.graphql'
import validIdentity from './mutates/validIdentity.graphql'
import validIdentityForFxs from './mutates/validIdentityForFxs.graphql'
import validIdentityRemoveDuplicates from './mutates/validIdentityRemoveDuplicates.graphql'
import validIsBindWeChatOpenPlatform from './mutates/validIsBindWeChatOpenPlatform.graphql'
import validSmsCode from './mutates/validSmsCode.graphql'

export {
  findBuildInRole,
  findCurrentAssignableRoleList,
  findCurrentFunctionalAuthorityList,
  findCurrentRoleList,
  findCurrentRoleListByArgs,
  findCurrentRoleListNew,
  findCurrentRoleListPlus,
  findFunctionalAuthorityByRoleId,
  findFunctionalAuthorityByRoleIdNew,
  findFunctionalAuthorityByRoleIds,
  findFunctionalAuthorityByRoleIdsNew,
  findLoginUserOpenId,
  findRoleByAccountId,
  findRoleByAccountIdOrUnitIdForPersonAccount,
  findRoleByBusinessDomainUnitId,
  findRoleByCategory,
  findRoleById,
  findRoleByOwner,
  getCurrentUserServicer,
  ChangePhoneByAdmin,
  ResetAdminPasswordWithResponse,
  ResetPassword,
  ResetPasswordWithResponse,
  addStudentCertificateInfo,
  applyBindAndReBindWeChatOpenPlatform,
  applyBindWeChatOpenPlatformAndValidLogin,
  applyBindWeChatOpenPlatformLoginAccount,
  applyCaptcha,
  applyChangeEnterpriseTokenList,
  applyChangePolicyActorUnitAuthorizeTokenList,
  applyCurrentPhoneSmsCode,
  applyEnterpriseMetadata,
  applyLoginByOpenId,
  applyOpenIdByServicerId,
  applyScanCodeOpenIdByServicerId,
  applySecureCaptcha,
  applySmsCode,
  applySmsCodeForChangingPhone,
  applySmsCodeForLoginDistributor,
  applySmsCodeForLoginDistributorNeedLogin,
  applyValidFacialRecognitionV1,
  applyValidFacialRecognitionV1Web,
  applyValidFacialRecognitionVerifyResult,
  applyValidFacialRecognitionVerifyResultAndBindWeChatOpenPlatform,
  applyValidFacialRecognitionVerifyResultTokenList,
  bindLoginAccountOpenPlatform,
  bindPhoneForCurrentAccount,
  bindPhoneForCurrentUser,
  changeCollectiveRegisterPhone,
  changeNewPhone,
  changeNewPhoneForFxs,
  changeNewPhoneWithResponse,
  changePasswordByCurrent,
  changePasswordByCurrentWithResponse,
  changePasswordByForceModifyInitPassword,
  changePhone,
  checkExistUnitByName,
  checkOnlineAdminAuthentication,
  checkUserAuthentication,
  createAdministratorAccount,
  createEnterprise,
  createEnterpriseManager,
  createMOHRSSAdmin,
  createMOHRSSSubordinateAdmin,
  createOnlineSchoolStudent,
  createOnlineSchoolSubAdmin,
  createOnlineSchoolSubAdminByToken,
  createRole,
  createRoleByAdminType,
  createRoleNew,
  createServiceProviderAccount,
  createSubProjectAdministrator,
  createSystemInternalRole,
  createSystemInternalRoles,
  deleteCertificateInfo,
  disableEnterpriseManager,
  enableEnterpriseManager,
  existUser,
  forgetPassword,
  freezeAccount,
  immediateResetPassword,
  initUnitTree,
  loginAndBindOpenPlatform,
  loginAndBindOpenPlatformV2,
  registerCollectiveRegisterAdmin,
  registerEnterPriseManagerForMZT,
  registerEnterpriseAccount,
  registerEnterpriseManager,
  registerStudent,
  registerStudentV2,
  removeRoleWithAdminTypeById,
  removeRoleWithNoAuthRelById,
  removeSystemInternalRole,
  resumeAccount,
  unbindPhone,
  unbindPhoneForCurrentUser,
  unbindWeChatOpenPlatform,
  updateAdministratorAccount,
  updateCollectiveRegister,
  updateCollectiveRegisterAccountInfo,
  updateEnterprise,
  updateEnterpriseManager,
  updateMOHRSSAdmin,
  updateMOHRSSSubordinateAdmin,
  updateOnlineAdministratorAccount,
  updateOnlineSchoolSubAdmin,
  updateOnlineSchoolSubAdminByToken,
  updateRole,
  updateRoleByAdminType,
  updateServiceProviderAccount,
  updateStudent,
  updateStudentBasicInfo,
  updateStudentByAdmin,
  updateStudentCertificateInfo,
  updateSystemInternalRole,
  validCaptcha,
  validIdentity,
  validIdentityForFxs,
  validIdentityRemoveDuplicates,
  validIsBindWeChatOpenPlatform,
  validSmsCode
}
