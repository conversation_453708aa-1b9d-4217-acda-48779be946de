<!--
 * @Author: WRP
 * @Date: 单个属性修改输入框
-->
<template>
  <span>
    <div class="edit-box">
      <!-- <national-region-cascader v-model="regionIds"></national-region-cascader> -->
      <el-cascader
        v-model="regionIds"
        :props="regionProps"
        :options="regionOptions"
        placeholder="请选择地区"
        class="form-l"
        clearable
      />
    </div>
  </span>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import NationalRegionCascader from '@hbfe/jxjy-admin-components/src/national-region/national-region-cascader.vue'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  @Component({
    components: {
      NationalRegionCascader
    }
  })
  export default class extends Vue {
    regionIds = [] as string[]
    // 修改的值
    editInputValue = ''
    // 修改后的值
    newValue = [] as string[]
    /**
     * 地区级联配置
     */
    regionProps = {}

    @Prop({
      type: Array,
      default: () => new Array<string>()
    })
    value: string[]

    /**
     * 地区选择器配置
     */
    @Prop({ type: Array, default: () => new Array<RegionTreeVo>() })
    regionOptions: Array<RegionTreeVo>

    @Watch('value', {
      deep: true
    })
    valChange(val: string[]) {
      if (val?.length) {
        this.regionIds = val
      }
    }

    // 修改的值
    @Emit('input')
    @Watch('regionIds', {
      deep: true
    })
    inputValueChange(val: any) {
      if (val?.length) {
        this.newValue = val
      }
    }
    setProps() {
      this.regionProps = {
        lazy: false,
        value: 'id',
        label: 'name',
        multiple: false,
        checkStrictly: false,
        disabled: false
      }
    }
    created() {
      this.setProps()
    }
  }
</script>
