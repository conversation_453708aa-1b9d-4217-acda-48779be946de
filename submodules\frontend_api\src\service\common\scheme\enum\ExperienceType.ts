import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 心得类型枚举
 * class_experience 班级心得
 * course_experience 课程心得
 */
export enum ExperienceTypeEnum {
  class_experience = 0,
  course_experience = 1
}

/**
 * @description 心得类型
 */
class ExperienceType extends AbstractEnum<ExperienceTypeEnum> {
  static enum = ExperienceTypeEnum

  constructor(status?: ExperienceTypeEnum) {
    super()
    this.current = status
    this.map.set(ExperienceTypeEnum.class_experience, '班级心得')
    this.map.set(ExperienceTypeEnum.course_experience, '课程心得')
  }
}

export default ExperienceType
