/*
 * @Description: 资讯详情
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-09 18:49:34
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 15:46:21
 */
import BasicDataQueryBackstage, {
  NewsDetailResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import NewsDetailVo from './vo/NewsDetail'
import PlatformTrainingChannel from '@api/platform-gateway/platform-training-channel-back-gateway'

class QueryNewsDetail {
  id: string
  constructor(id: string) {
    this.id = id
  }
  /**
   * 获取资讯详情
   */
  async queryNewsDetail(): Promise<NewsDetailVo> {
    const { data } = await BasicDataQueryBackstage.getNewsDetail(this.id)
    // 专题差异化
    if (data.specialSubjectId) {
      const querySpecial = await PlatformTrainingChannel.getTrainingChannelDetailById(data.specialSubjectId)
      if (querySpecial?.data) {
        return NewsDetailVo.from(data, querySpecial.data)
      }
    }
    return NewsDetailVo.from(data)
  }
}

export default QueryNewsDetail
