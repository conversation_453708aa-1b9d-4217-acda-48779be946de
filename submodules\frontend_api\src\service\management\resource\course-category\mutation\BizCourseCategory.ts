import CourseCategoryGateway from '@api/ms-gateway/ms-course-resource-v1'
import { ResponseStatus } from '@hbfe/common'

/**
 * 做业务的对象
 */
class BizCourseCategory {
  id: string

  constructor(id: string) {
    this.id = id
  }

  /**
   * 删除
   */
  async doRemove(): Promise<ResponseStatus> {
    const { status } = await CourseCategoryGateway.removeCourseCategory(this.id)
    return new ResponseStatus(status.code, status.getMessage())
  }
}

export default BizCourseCategory
