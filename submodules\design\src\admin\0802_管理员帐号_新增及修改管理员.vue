<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">运营帐号管理</el-breadcrumb-item>
      <el-breadcrumb-item>新增子项目管理员</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基本信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="120px" class="m-form">
                <el-form-item label="帐号：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入帐号" class="form-m" />
                </el-form-item>
                <el-form-item label="姓名 / 昵称：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入姓名 / 昵称" class="form-m" />
                </el-form-item>
                <el-form-item label="性别：" required>
                  <el-radio-group v-model="form.resource">
                    <el-radio label="男"></el-radio>
                    <el-radio label="女"></el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="密码：" required>
                  <el-input
                    v-model="form.name"
                    clearable
                    show-password
                    placeholder="请输入6-18位由字母、数字和符号两种及以上组合的密码"
                    class="form-l"
                  />
                  <!--密码安全判断-->
                  <div class="psw-tips form-l">
                    <el-progress :percentage="33.33" color="#e93737" :show-text="false"></el-progress>
                    <!--弱：txt-l，中：txt-m，强：txt-h-->
                    <span class="txt txt-l">弱</span>
                  </div>
                  <div class="psw-tips form-l">
                    <el-progress :percentage="66.66" color="#ee9e2d" :show-text="false"></el-progress>
                    <!--弱：txt-l，中：txt-m，强：txt-h-->
                    <span class="txt txt-m">中</span>
                  </div>
                  <div class="psw-tips form-l">
                    <el-progress :percentage="100" color="#49b042" :show-text="false"></el-progress>
                    <!--弱：txt-l，中：txt-m，强：txt-h-->
                    <span class="txt txt-h">强</span>
                  </div>
                </el-form-item>
                <el-form-item label="确认密码：" required>
                  <el-input v-model="form.name" clearable show-password placeholder="请再次输入密码" class="form-l" />
                </el-form-item>
                <el-form-item label="手机号：" required>
                  <el-input v-model="form.name" clearable placeholder="请输入手机号" class="form-m" />
                </el-form-item>
                <el-form-item label="邮箱：">
                  <el-input v-model="form.name" clearable placeholder="请输入邮箱" class="form-m" />
                </el-form-item>
                <el-form-item label="启用状态：" required>
                  <el-radio-group v-model="form.resource">
                    <el-radio label="启用"></el-radio>
                    <el-radio label="禁用"></el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">分配角色</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="120px" class="m-form">
                <el-form-item required>
                  <div slot="label"></div>
                  <el-button type="primary" icon="el-icon-plus" class="f-mb20">添加角色</el-button>
                  <!--表格-->
                  <el-table stripe :data="tableData1" max-height="500px" class="m-table">
                    <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                    <el-table-column label="角色" min-width="180">
                      <template>角色</template>
                    </el-table-column>
                    <el-table-column label="说明" min-width="300">
                      <template>说明说明说明说明说明说明说明说明</template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center" fixed="right">
                      <template>
                        <el-button type="text" size="mini">删除</el-button>
                      </template>
                    </el-table-column>
                    <el-empty slot="empty" :image-size="40" description="暂无数据，请添加角色~" />
                  </el-table>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button>放弃编辑</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
