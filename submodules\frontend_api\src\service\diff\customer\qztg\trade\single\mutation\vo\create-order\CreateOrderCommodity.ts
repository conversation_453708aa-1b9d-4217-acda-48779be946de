export default class CreateOrderCommodity {
  /**
   * 商品sku编号
   */
  skuId?: string

  /**
   * 商品数量
   */
  quantity?: number

  /**
   * 商品授权ID(分销上下文产品分销授权ID)
   */
  commodityAuthId?: string

  /**
   * 商品使用的价格策略类型 1-定价策略 2-优惠策略 (分销)
   */
  policyType?: number

  /**
   * 策略id (分销)
   若使用定价策略则为定价策略id,若使用优惠策略则为优惠策略id
   */
  policyId?: string

  /**
   * 是否被购买
   */
  bought = false

  /**
   * 期别id
   */
  issueId: string
}
