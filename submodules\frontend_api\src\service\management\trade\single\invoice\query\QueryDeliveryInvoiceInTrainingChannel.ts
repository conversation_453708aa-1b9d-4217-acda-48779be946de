import QueryDeliveryInvoiceBase from '@api/service/management/trade/single/invoice/query/vo/QueryDeliveryInvoiceBase'
import { Page } from '@hbfe/common'
import DeliveryInvoiceParamVo from '@api/service/management/trade/single/invoice/query/vo/DeliveryInvoiceParam'
import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
import QueryOffLinePageInvoiceParam from '@api/service/management/trade/single/invoice/query/vo/QueryOffLinePageInvoiceParam'
import UserModule from '@api/service/management/user/UserModule'
import ExportGateWay from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import QueryOffLineInvoiceInTrainingChannel from '@api/service/management/trade/single/invoice/query/QueryOffLineInvoiceInTrainingChannel'

export default class QueryDeliveryInvoiceInTrainingChannel extends QueryDeliveryInvoiceBase {
  /**
   * 分页查询发票配送
   * @param page 页数
   * @param DeliveryInvoiceParamVo 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<InvoiceListResponse>
   */
  async queryPageDeliveryInvoice(
    page: Page,
    deliveryInvoiceParamVo?: DeliveryInvoiceParamVo
  ): Promise<Array<OffLinePageInvoiceVo>> {
    const offlineInvoiceRequest = new QueryOffLinePageInvoiceParam()
    // offlineInvoiceRequest.consignee = deliveryInvoiceParamVo.name
    offlineInvoiceRequest.idCard = deliveryInvoiceParamVo.idCard
    offlineInvoiceRequest.loginAccount = deliveryInvoiceParamVo.loginAccount
    offlineInvoiceRequest.deliveryStatusList = deliveryInvoiceParamVo.deliveryStatus
    offlineInvoiceRequest.orderNoList = deliveryInvoiceParamVo.invoiceNo
    offlineInvoiceRequest.deliveryStartTime = deliveryInvoiceParamVo.startDate
    offlineInvoiceRequest.deliveryEndTime = deliveryInvoiceParamVo.endDate
    offlineInvoiceRequest.shippingMethodList = deliveryInvoiceParamVo.deliveryWay
    offlineInvoiceRequest.expressNo = deliveryInvoiceParamVo.theAwb
    offlineInvoiceRequest.takePerson = deliveryInvoiceParamVo.recipient
    offlineInvoiceRequest.invoiceFreezeStatus = deliveryInvoiceParamVo.frozenState
    offlineInvoiceRequest.invoiceStatusList = 2
    offlineInvoiceRequest.consignee = deliveryInvoiceParamVo.name
    const queryOffLineInvoice = new QueryOffLineInvoiceInTrainingChannel()
    return await queryOffLineInvoice.offLinePageVatspecialplaInvoiceInServicer(page, offlineInvoiceRequest)
  }
  /**
   * 导出发票配送
   */
  async exportPageDeliveryInvoice(deliveryInvoiceParamVo: DeliveryInvoiceParamVo): Promise<boolean> {
    const offlineInvoiceRequest = new QueryOffLinePageInvoiceParam()
    offlineInvoiceRequest.takePerson = deliveryInvoiceParamVo.name
    offlineInvoiceRequest.idCard = deliveryInvoiceParamVo.idCard
    offlineInvoiceRequest.loginAccount = deliveryInvoiceParamVo.loginAccount
    offlineInvoiceRequest.deliveryStatusList = deliveryInvoiceParamVo.deliveryStatus
    offlineInvoiceRequest.orderNoList = deliveryInvoiceParamVo.invoiceNo
    offlineInvoiceRequest.deliveryStartTime = deliveryInvoiceParamVo.startDate
    offlineInvoiceRequest.deliveryEndTime = deliveryInvoiceParamVo.endDate
    offlineInvoiceRequest.shippingMethodList = deliveryInvoiceParamVo.deliveryWay
    offlineInvoiceRequest.expressNo = deliveryInvoiceParamVo.theAwb
    offlineInvoiceRequest.takePerson = deliveryInvoiceParamVo.recipient
    offlineInvoiceRequest.invoiceFreezeStatus = deliveryInvoiceParamVo.frozenState
    offlineInvoiceRequest.consignee = deliveryInvoiceParamVo.name
    const data = await this.offLinePageVatspecialplaInvoiceInExport(offlineInvoiceRequest)
    return data
  }
  /**
   * 个人线下发票配送 - 专票
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  async offLinePageVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean> {
    queryOffLinePageInvoiceParam.invoiceType = 2
    queryOffLinePageInvoiceParam.invoiceCategoryList = [3]
    const request = QueryOffLinePageInvoiceParam.to(queryOffLinePageInvoiceParam)
    if (
      queryOffLinePageInvoiceParam.userName ||
      queryOffLinePageInvoiceParam.idCard ||
      queryOffLinePageInvoiceParam.phone ||
      queryOffLinePageInvoiceParam.loginAccount
    ) {
      //  根据姓名和证件号查询用户ID
      const queryStudentIdList = UserModule.queryUserFactory.queryStudentList
      queryStudentIdList.queryStudentIdParams.userName = queryOffLinePageInvoiceParam.userName
        ? queryOffLinePageInvoiceParam.userName
        : undefined
      queryStudentIdList.queryStudentIdParams.idCard = queryOffLinePageInvoiceParam.idCard
        ? queryOffLinePageInvoiceParam.idCard
        : undefined
      queryStudentIdList.queryStudentIdParams.phone = queryOffLinePageInvoiceParam.phone
        ? queryOffLinePageInvoiceParam.phone
        : undefined
      queryStudentIdList.queryStudentIdParams.loginAccount = queryOffLinePageInvoiceParam.loginAccount
        ? queryOffLinePageInvoiceParam.loginAccount
        : undefined
      const idList = await queryStudentIdList.queryStudentIdList()
      if (idList.status.isSuccess()) {
        request.associationInfo.buyerIdList = idList.data
      }
    }
    const result = await ExportGateWay.exportInvoiceDeliveryInTrainingChannel(request)
    return result.data
  }
}
