import { QuestionMode, QuestionType } from '@api/service/common/models/exam/enums'
import {
  BlankFillingResponse,
  ComprehensiveResponse,
  EssayResponse,
  JudgementResponse,
  MultipleChoiceResponse,
  ScaleResponse,
  SingleChoiceResponse,
  TagDTO
} from '@api/gateway/btpx@GeneralExam-default'
/**
 * <AUTHOR> update 2021/1/28
 */
class Question {
  /**
   * id
   */
  id = ''
  /**
   * 试题应用类型
   @see QuestionApplyType
   */
  applyTypes: Array<number>
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 单位id
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 题库id
   */
  libraryId = ''
  /**
   * 题目
   */
  title = ''
  /**
   * 判断题
   */
  judgement: JudgementResponse
  /**
   * 单选题
   */
  singleChoice: SingleChoiceResponse
  /**
   * 多选
   */
  multipleChoice: MultipleChoiceResponse
  /**
   * 填空
   */
  blankFilling: BlankFillingResponse
  /**
   * 问答题
   */
  essay: EssayResponse
  /**
   * 量表题
   */
  scale: ScaleResponse
  /**
   * 综合题
   */
  comprehensive: ComprehensiveResponse
  /**
   * 试题类型
   */
  questionType: QuestionType
  /**
   * 难度
   */
  mode: QuestionMode
  /**
   * 难度值
   */
  difficulty = 0.0
  /**
   * 试题解析
   */
  description = ''
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 最后修改时间
   */
  lastChangeTime: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 资源记录(数据)的授权源id
   a 授权 b, b 授权 c, c的sourceId是b, c的rootId是a
   */
  rootId: string
  /**
   * 数据授权的Token, 并不需要默认值
   */
  token: string
  /**
   * 试题内容
   * @see #questionType
   */
  questionContent: any

  /**
   * 关联多个课程id
   */
  relateCourseIds: Array<string>

  /**
   * 关联课程id
   */
  relateCourseId: string

  /**
   * 标签
   */
  tags: Array<TagDTO>
  /**
   * 工种分类->工种路径，以/开始,/分隔，只需要填两级
   */
  workTypePaths: Array<string>
}

export default Question
