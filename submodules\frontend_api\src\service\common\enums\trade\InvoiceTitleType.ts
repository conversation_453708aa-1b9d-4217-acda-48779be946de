/**
 * 发票抬头类型
 */
import AbstractEnum from '../AbstractEnum'

enum InvoiceTitleTypeEnum {
  none = 0,
  personal = 1,
  enterprise = 2
}

export { InvoiceTitleTypeEnum }

class InvoiceTitleType extends AbstractEnum<InvoiceTitleTypeEnum> {
  static enum: InvoiceTitleTypeEnum

  constructor() {
    super()
    this.map[InvoiceTitleTypeEnum.none] = '无'
    this.map[InvoiceTitleTypeEnum.personal] = '用户'
    this.map[InvoiceTitleTypeEnum.enterprise] = '单位'
  }
}

export default InvoiceTitleType
