import agreeRefund from './mutates/agreeRefund.graphql'
import agreeRefundApply from './mutates/agreeRefundApply.graphql'
import applyRefund from './mutates/applyRefund.graphql'
import cancelOrder from './mutates/cancelOrder.graphql'
import cancelRefundApply from './mutates/cancelRefundApply.graphql'
import createOrder from './mutates/createOrder.graphql'
import forceCloseOrder from './mutates/forceCloseOrder.graphql'
import hideOrder from './mutates/hideOrder.graphql'
import payOrder from './mutates/payOrder.graphql'
import rejectRefund from './mutates/rejectRefund.graphql'
import rejectRefundApply from './mutates/rejectRefundApply.graphql'
import updateOrderInvoice from './mutates/updateOrderInvoice.graphql'
import jskqCreateOrder from './mutates/jskqCreateOrder.graphql'

export {
  agreeRefund,
  agreeRefundApply,
  applyRefund,
  cancelOrder,
  cancelRefundApply,
  createOrder,
  forceCloseOrder,
  hideOrder,
  payOrder,
  rejectRefund,
  rejectRefundApply,
  updateOrderInvoice,
  jskqCreateOrder
}
