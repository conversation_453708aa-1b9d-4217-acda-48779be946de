import { LogoType } from '../enum/LogoType'
import { PhoneFooterType } from '../enum/PhoneFooterType'
/**
 * 轮播图信息
 */
export class CarouselImage {
  /**
   * ID
   */
  id = ''
  /**
   * 轮播图URL
   */
  url = ''
  /**
   * 排序
   */
  sort = 0
  /**
   * 链接
   */
  link = ''
}

/**
 * 门户信息类
 */
export default class PortalInfo {
  /**
   * 专题门户logo类型
   */
  logoType: LogoType = undefined
  /**
   * 专题门户logo
   */
  logo = ''
  /**
   * 客户电话类型
   */
  phoneType: PhoneFooterType = undefined
  /**
   * 客户电话
   */
  phone = ''
  /**
   * 客服电话图片
   */
  phoneImgUrl = ''
  /**
   * 培训流程类型
   */
  processType: PhoneFooterType = undefined
  /**
   * 培训流程
   */
  process = ''
  /**
   * 企业微信客服类型
   */
  customerServiceType: PhoneFooterType = undefined
  /**
   * 企业微信客服
   */
  customerService = ''
  /**
   * 资讯时间
   */
  informationTime = ''
  /**
   * 底部落款类型
   */
  footerType: PhoneFooterType = undefined
  /**
   * 底部落款
   */
  footer = ''
  /**
   * web轮播图设置
   */
  webCarouselSettings: CarouselImage[] = []
  /**
   * H5轮播图设置
   */
  H5CarouselSettings: CarouselImage[] = []
}
