<template>
  <div>
    <el-form-item label="试题题目：" required>
      <div class="rich-text">
        <!-- <el-input type="textarea" :rows="6" v-model="createQuestion.described" placeholder="请输入试题题目" /> -->
        <hb-tinymce-editor v-model="createQuestion.described" token="" :propId="basicKey"></hb-tinymce-editor>
      </div>
    </el-form-item>
    <el-form-item label="试题选项：" v-if="!ability" required>
      <div class="m-option-btn f-mb20" v-for="(item, index) in createQuestion.options" :key="index">
        <div class="f-flex f-align-center f-mb5">
          <p class="f-flex-sub f-cb">选项 {{ resolverIndexToCharCode(index) }}</p>
          <el-button
            type="primary"
            size="mini"
            plain
            icon="el-icon-top"
            :disabled="index === 0"
            @click="questionUp(index)"
          ></el-button>
          <el-button
            type="primary"
            size="mini"
            plain
            icon="el-icon-bottom"
            :disabled="index === createQuestion.options.length - 1"
            @click="questionDown(index)"
          ></el-button>
          <el-button type="danger" @click="currentDelete(index)" size="mini" plain icon="el-icon-delete"></el-button>
        </div>
        <div class="rich-text">
          <!-- <el-input type="textarea" :rows="6" v-model="item.content" /> -->
          <hb-tinymce-editor
            v-model="item.content"
            :key="`${index}-${basicKey}`"
            :propId="basicKey"
          ></hb-tinymce-editor>
        </div>
        <div>
          <el-checkbox v-model="item.completion" @change="changeCompletion(item)">允许填空</el-checkbox
          ><el-checkbox v-if="item.completion" v-model="item.isRequire">是否必填</el-checkbox>
        </div>
      </div>
      <el-button type="primary" size="small" plain icon="el-icon-plus" @click="addChoiceItem">添加选项</el-button>
    </el-form-item>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Vue, Watch } from 'vue-property-decorator'
  import { ExamUtil } from '@/store/module/exam/common/ExamUtil'
  import DataResolve from './DataResolve'
  import OptionsQuestion from '@api/service/common/question-naire/OptionsQuestion'
  import QuestionSingleOption from '@api/service/common/question-naire/QuestionSingleOption'
  import HbTinyMceEditor from '@hbfe/jxjy-admin-components/src/tinymce-editor/index.vue'
  @Component({
    components: {
      HbTinyMceEditor
    }
  })
  export default class extends Vue {
    @Prop({ type: Object })
    createQuestion: OptionsQuestion //单选入参

    @Prop({ type: Boolean, default: false })
    ability: boolean //教师评价

    currentChoiceIndex = 0 //当前选项的索引

    basicKey = new Date().getTime().toString() //key 解决数据不同步问题
    /**
     * 允许填空 反选后必填项置空
     */
    changeCompletion(item: QuestionSingleOption) {
      if (!item.completion) item.isRequire = false
    }
    /**
     * 基本表单校验
     */
    validForm() {
      if (!this.createQuestion.described) {
        this.$message.warning('请填写试题题目，不可为空。')
        return false
      }

      if (!this.ability) {
        if (this.createQuestion.options.length < 2) {
          this.$message.warning('单选题选项不能少于两个')
          return false
        }
        if (this.createQuestion.options.find((p: QuestionSingleOption) => !p.content)) {
          this.$message.warning('请配置单选题选项内容。')
          return false
        }
      }

      return true
    }
    /**
     * 解析index为字母
     * @param index
     */
    resolverIndexToCharCode(index: number) {
      return ExamUtil.matchCharCode(index)
    }
    /**
     * 新增一个选项
     */
    async addChoiceItem() {
      if (this.createQuestion.options.length >= 20) {
        this.$message.error('选项添加已达到上限，无法继续添加。')
        return
      }
      this.currentChoiceIndex++
      this.createQuestion.options.push(this.productChoiceItem(this.currentChoiceIndex))
    }

    /**
     * 当前Item
     */
    productChoiceItem(index: number) {
      const choice = new QuestionSingleOption()
      choice.id = index + ''
      return choice
    }
    /**
     * 删除当前项
     */
    currentDelete(idx: number) {
      this.$confirm('是否确认删除该选项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.createQuestion.options = DataResolve.doQuestionDelete(this.createQuestion.options, idx)
          this.basicKey = new Date().getTime().toString() + parseInt(`${Math.random() * 10}`) //key 解决数据不同步问题
        })
        .catch(() => {
          console.log('已取消删除')
        })
    }

    /**
     * 试题上移
     */
    questionUp(idx: number) {
      DataResolve.doQuestionUp(this.createQuestion.options, idx)
      this.basicKey = new Date().getTime().toString() //key 解决数据不同步问题
    }
    /**
     * 试题下移
     */
    questionDown(idx: number) {
      DataResolve.doQuestionDown(this.createQuestion.options, idx)
      this.basicKey = new Date().getTime().toString() //key 解决数据不同步问题
    }
    init() {
      // this.createQuestion.options.push(this.productChoiceItem(1))
      // this.createQuestion.options.push(this.productChoiceItem(2))
      this.currentChoiceIndex = 2
    }

    created() {
      this.init()
    }
  }
</script>
