import { ResponseStatus } from '@api/Response'
// import { MessageBox } from 'element-ui'
import Authentication from '@api/service/common/authentication/Authentication'
import { AccountType, AuthTokenLoginParams, Params } from '@api/service/common/authentication/interfaces/LoginParams'

import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import InitOptions from '@hbfe-biz/biz-authentication/dist/models/InitOptions'
import { frontendApplication, ingress } from '../../config/enums/ApolloConfigKeysEnum'
import { loginH5MsByAccountUrl } from '../contant'
import WefunnyReport from '@api/service/common/webfunny/WefunnyReport'
import LoginReportModel from '@api/service/common/webfunny/models/LoginReportModel'
import { LoginInfoEnum } from '@api/service/common/webfunny/enums/LoginEnum'
import { Base64 } from 'js-base64'
import Env from '@api/service/common/utils/Env'

const applyUrl = '/rest/ms2/3rd/apply'
// /web/rest/3rd/login/{token}
const loginUrl = '/web/rest/ms3rd/login/'
const loginUrlV2 = '/web/rest/ms2/3rd/login/'
const connectByTicket = '/web/rest/ms3rd/mzt/connectByTicket'

export enum ThirdPartyType {
  // 微信
  wx = 41,
  // 微信公众号
  wxPublic = 42,
  // 微信小程序
  wxMiniProgram = 43,
  // qq
  qq = 21,
  // 微博
  weibo = 31,
  // 钉钉
  dingDing = 61,
  // 闽政通
  minZhenTong = 52,
  // 补贴管理开放平台
  BTAdmin = 70
}

enum ErrorCode {
  // token 无效
  unavailable = 4303,
  unbind = 4301
}

export enum ConnectIdType {
  unionId = '1',
  openId = '2'
}

export class ApplyParams extends Params {
  resultUrl: string
  thirdPartyType: ThirdPartyType
  connectIdType: ConnectIdType
  // 8.0专技新增字段↓
  needReadServicerSeriesConfig?: boolean
  grantType = 'identity_auth_token'
  token?: string
}

abstract class PartyType {
  abstract logout(redirect: boolean, ssoTicket?: string): any
}

class MztPartyType extends PartyType {
  constructor(baseUrl: string) {
    super()
    this.baseUrl = baseUrl
  }

  baseUrl = ''

  /**
   * 退出登录
   * @param ssoTicket
   * @param redirect
   */
  logout(redirect = true, ssoTicket = '') {
    const returnUrl =
      process.env.NODE_ENV === 'development'
        ? `${location.protocol}//${location.hostname}:8080`
        : `${window.location.origin}${window.location.pathname}`
    let redirectUrl = `${this.baseUrl}/web/rest/logout?ssoTicket=${ssoTicket}&returnUrl=`
    if (redirect) {
      redirectUrl += returnUrl
    }
    window.location.replace(redirectUrl)
    return ''
  }
}

export class ConnectByTicketParams {
  state: string
  trustticket: string
}

export enum ConnectState {
  bind = 1,
  unbind = 2
}

export class ConnectByTicketData {
  // 用户昵称(用户姓名或是法人单位名称)
  nickName: string
  // 1: 已绑定, 2: 未绑定
  connectState: number
  // 认证Token凭证，用于在完成用户绑定、或已绑定状态下进行登录认证（获取根票及服务票）
  token: string
  code: number
}

class ThirdParty {
  options: InitOptions
  wxloginTokenKey = 'wxloginToken'
  context: Authentication
  /**
   * /web/rest/3rd/apply 返回的state值。用于换取token时使用
   */
  applyState = ''
  webEnv = ''

  constructor(options: InitOptions, context: Authentication) {
    this.options = options
    this.context = context
    this.partyTypes[ThirdPartyType.minZhenTong] = new MztPartyType(this.options.thirdAuthUrl)
    if (localStorage) {
      this.applyState = localStorage.getItem('applyState')
    }
  }

  partyTypes: {
    [key: string]: PartyType
  } = {}

  getPartyTypeByKey(type: ThirdPartyType): PartyType {
    if (!type) return
    return this.partyTypes[type]
  }

  /**
   * 埋点系统上报异常错误
   */
  upErrorEvent(e: any, eventType: string, LoginType: LoginInfoEnum) {
    const data = new LoginReportModel()
    data.response = JSON.stringify(e)
    data.scene = LoginType
    data.wxUrl = Base64.encode(e?.data?.authUrl || '')
    data.bizCode = e?.code || ''
    try {
      WefunnyReport.upCommonEvent(eventType, data)
    } catch (e) {
      console.log('埋点上报upErrorRQEvent报错')
    }
  }

  async apply(
    partyType: ThirdPartyType,
    connectIdType: ConnectIdType,
    accountType: AccountType,
    resultUrl: string,
    open: boolean,
    grantType = 'identity_auth_token'
  ): Promise<string> {
    const params = new ApplyParams()
    params.thirdPartyType = partyType
    params.accountType = accountType
    params.connectIdType = connectIdType
    params.resultUrl = resultUrl
    params.service = this.options.service
    params.needReadServicerSeriesConfig = true
    params.grantType = grantType
    params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.wxOpenIdToken)
    let openWindow: Window
    // if (open) {
    //   openWindow = this.open('about:blank')
    // } else {
    //   openWindow = window
    // }

    // 获取当前环境
    this.webEnv = '?env=' + Env.curProxyEnv
    if (Env.isProxyProdEnv) {
      this.webEnv = ''
    }
    // const { data } = await this.options.request.post(`${this.options.thirdAuthUrl}${applyUrl}${this.webEnv}`, params)
    const url = ConfigCenterModule.getFrontendApplication(frontendApplication.apiendpoint)
    const { data } = await this.options.request.post(`${url}${applyUrl}`, params)
    console.log('data', data)
    if (data?.code === 4000) {
      if (openWindow !== window) {
        openWindow.close()
      }
      const message = data.message || ''
      //   await MessageBox.alert(message, {
      //     title: '登录异常',
      //     center: true,
      //     type: 'error',
      //     showClose: false,
      //     confirmButtonText: '返回首页'
      //   })
      window.location.href = window.location.origin
      return Promise.reject(message)
    }
    this.upErrorEvent(data, '通用--学员中心', LoginInfoEnum.student_wx_login)
    // openWindow.location.href = data.data.authUrl
    return data?.data?.authUrl
  }

  async applyCheckLogin(
    partyType: ThirdPartyType,
    connectIdType: ConnectIdType,
    accountType: AccountType,
    resultUrl: string
  ): Promise<string> {
    // `${location.origin}/check-login-mzt`
    const params = new ApplyParams()
    params.thirdPartyType = partyType
    params.accountType = accountType
    params.connectIdType = connectIdType
    params.resultUrl = resultUrl
    params.service = this.options.service
    // const openWindow = this.open('about:blank')
    const { data } = await this.options.request.post(`${this.options.thirdAuthUrl}${applyUrl}`, params)
    if (data.code === 200 && data.data.loginCheckUrl) {
      this.applyState = data.data.state
      if (localStorage) {
        localStorage.setItem('applyState', this.applyState)
      }
      const loginCheckUrl = `${data.data.loginCheckUrl}&checkbackurl=${resultUrl}`
      window.location.replace(loginCheckUrl)
      return loginCheckUrl
    }
    return ''
  }

  async connectByTicket(trustticket: string): Promise<ConnectByTicketData> {
    const params = new ConnectByTicketParams()
    params.trustticket = trustticket
    params.state = this.applyState
    const { data } = await this.options.request.post(`${this.options.thirdAuthUrl}${connectByTicket}`, params)
    const resultData = new ConnectByTicketData()
    if (data.code === 200) {
      resultData.connectState = Number(data.data.connectState)
      resultData.nickName = data.data.nickName
      resultData.token = data.data.token
      resultData.code = data.code
    }
    return resultData
  }

  async login(token: string) {
    const { data } = await this.options.request.get(`${this.options.thirdAuthUrl}${loginUrl}${token}`)
    const code = data.code
    if (code === ErrorCode.unavailable) {
      return Promise.reject(new ResponseStatus(500, data.message))
    } else if (code === ErrorCode.unbind) {
      return Promise.reject(new ResponseStatus(500, data.message))
    }
    // 获取到 ticket ，保存票据，然后做认证
    const { serviceTicketId, ticketGrantTicketId } = data.data
    this.context.setStoreTicket(serviceTicketId, ticketGrantTicketId)
    localStorage.setItem('customer.Account-Type', '1')
    await this.context.auth()
    return data.data
  }

  async loginUrlV2(token: string) {
    // 获取当前环境
    this.webEnv = '?env=' + Env.curProxyEnv
    if (Env.isProxyProdEnv) {
      this.webEnv = ''
    }
    const url = ConfigCenterModule.getIngress(ingress.sso)
    const { data } = await this.options.request.get(`${url}${loginUrlV2}${token}${this.webEnv}`)
    const code = data.code
    if (code === ErrorCode.unavailable) {
      return Promise.reject(new ResponseStatus(500, data.message))
    } else if (code === ErrorCode.unbind) {
      return Promise.reject(new ResponseStatus(500, data.message))
    }
    // 获取到 ticket ，保存票据，然后做认证
    const { serviceTicketId, ticketGrantTicketId } = data.data
    this.context.setStoreTicket(serviceTicketId, ticketGrantTicketId)
    localStorage.setItem('customer.Account-Type', '1')
    await this.context.auth()
    return data.data
  }

  async loginV2(token: string) {
    const loginParams = new AuthTokenLoginParams()
    // 区分学员登录和集体登录
    localStorage.setItem('customer.Account-Type', '1')
    loginParams.authToken = token
    const result = await this.options.request.post(`${this.options.ssoUrl}${loginH5MsByAccountUrl}`, {
      token
    })
    if (typeof result?.status === 'number' && result?.status !== 200) {
      return Promise.reject(result)
    }
    if (result?.data?.code !== 200) {
      return Promise.reject(result)
    }
    this.context.setStoreTicket(result.data.data.serviceTicketId, result.data.data.ticketGrantTicketId)
    await this.context.auth()

    localStorage.setItem('customer.Account-Type', '1')
    await this.context.auth()
    return result
  }

  open(url: string): Window | null {
    return window.open(
      url,
      'newName'
      // 'height=600, width=600, top=200, left=200, menubar=no, scrollbars=no, resizable=no, location=no, status=no'
    )
  }

  openWindow(url: string) {
    const form = document.createElement('form')
    form.action = url
    form.target = '_blank'
    form.method = 'GET'
    document.body.appendChild(form)
    form.submit()
  }

  setWxloginToken(value: string) {
    localStorage.setItem(`${this.options.prefix}.${this.wxloginTokenKey}`, value)
  }

  getWxloginToken() {
    return localStorage.getItem(`${this.options.prefix}.${this.wxloginTokenKey}`)
  }
}

export default ThirdParty
