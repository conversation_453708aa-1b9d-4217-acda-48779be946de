import checkCategoryNameExist from './queries/checkCategoryNameExist.graphql'
import getLastOrNextNewsNotice from './queries/getLastOrNextNewsNotice.graphql'
import getNewsNotice from './queries/getNewsNotice.graphql'
import getPopNewsNotice from './queries/getPopNewsNotice.graphql'
import listAllCategory from './queries/listAllCategory.graphql'
import listSubCategoryByParentId from './queries/listSubCategoryByParentId.graphql'
import pageNewsNotice from './queries/pageNewsNotice.graphql'
import checkPublishAble from './mutates/checkPublishAble.graphql'
import create from './mutates/create.graphql'
import deleteById from './mutates/deleteById.graphql'
import deleteNewsCategory from './mutates/deleteNewsCategory.graphql'
import publish from './mutates/publish.graphql'
import toDraft from './mutates/toDraft.graphql'
import update from './mutates/update.graphql'
import updateNewsCategory from './mutates/updateNewsCategory.graphql'

export {
  checkCategoryNameExist,
  getLastOrNextNewsNotice,
  getNewsNotice,
  getPopNewsNotice,
  listAllCategory,
  listSubCategoryByParentId,
  pageNewsNotice,
  checkPublishAble,
  create,
  deleteById,
  deleteNewsCategory,
  publish,
  toDraft,
  update,
  updateNewsCategory
}
