<template>
  <el-card shadow="never" class="m-card">
    <div slot="header" class="">
      <span class="tit-txt">管理员信息</span>
    </div>
    <!--表格-->
    <el-table stripe :data="administratorList" max-height="500px" class="m-table">
      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
      <el-table-column label="管理员帐号" prop="account" min-width="200"> </el-table-column>
      <el-table-column label="手机号" prop="phone" min-width="150"> </el-table-column>
      <el-table-column label="管理员角色" prop="roleName" min-width="150"> </el-table-column>
      <el-table-column label="状态" prop="status" min-width="120">
        <template slot-scope="{ row }">
          <el-badge v-if="row.status == 1" is-dot type="success" class="badge-status">启用</el-badge>
          <el-badge v-else is-dot type="danger" class="badge-status">停用</el-badge>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="220" align="center" fixed="right">
        <template slot-scope="{ row }">
          <el-button type="text" size="mini" @click="handleChangeAccountDialog(row)">修改帐号</el-button>
          <el-button type="text" size="mini" @click="handleChangeStatusDialog(row)">{{
            row.status == 1 ? '停用' : '启用'
          }}</el-button>
          <el-button type="text" size="mini" @click="handleResetPasswordDialog(row)">重置密码</el-button>
        </template>
      </el-table-column>
    </el-table>
    <hb-pagination :page="page" v-bind="page"></hb-pagination>
    <!-- <el-button @click="handleLoadData">查询</el-button> -->
    <el-drawer title="重置密码" :visible.sync="dialog3" :direction="direction" size="600px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form
              ref="passwordForm"
              :model="passwordData"
              label-width="auto"
              class="m-form f-mt20"
              :rules="passwordRules"
            >
              <el-form-item label="">
                你正在重置<span class="f-fb f-cb">【{{ singleData.account }}】</span>管理员帐号的密码
              </el-form-item>
              <el-form-item label="密码：" class="is-required" prop="password">
                <el-input
                  v-model="passwordData.password"
                  clearable
                  show-password
                  placeholder="请输入8-18位由字母、数字或符号组合的密码"
                  auto-complete="new-password"
                  @input="checkoutPasswordStrength"
                />
                <!--密码安全判断-->
                <div v-if="passwordStrengthLow" class="psw-tips">
                  <el-progress :percentage="33.33" color="#e93737" :show-text="false"></el-progress>
                  <!--弱：txt-l，中：txt-m，强：txt-h-->
                  <span class="txt txt-l">弱</span>
                </div>
                <div v-if="passwordStrengthIntermediate" class="psw-tips">
                  <el-progress :percentage="66.66" color="#ee9e2d" :show-text="false"></el-progress>
                  <!--弱：txt-l，中：txt-m，强：txt-h-->
                  <span class="txt txt-m">中</span>
                </div>
                <div v-if="passwordStrengthHigh" class="psw-tips">
                  <el-progress :percentage="100" color="#49b042" :show-text="false"></el-progress>
                  <!--弱：txt-l，中：txt-m，强：txt-h-->
                  <span class="txt txt-h">强</span>
                </div>
              </el-form-item>
              <el-form-item label="确认密码：" class="is-required" prop="rePassword">
                <el-input
                  v-model="passwordData.rePassword"
                  clearable
                  show-password
                  auto-complete="new-password"
                  placeholder="请再次输入密码"
                />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleResetPassword">确认</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
    <el-dialog title="系统提醒" :visible.sync="dialog2" width="400px" class="m-dialog">
      <div class="dialog-alert">
        <!--警告-->
        <i class="icon el-icon-warning warning"></i>
        <span class="txt"
          >您当前正在{{ singleData.status == 1 ? '停用' : '启用'
          }}<i class="f-cb">【{{ singleData.account }}】</i>管理员帐号，{{
            singleData.status == 1
              ? '停用后该帐号不可登录管理后台，是否确认停用'
              : '启用后该帐号可正常登录，是否确认启用'
          }}？</span
        >
      </div>
      <div slot="footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleChangeStatus">确认</el-button>
      </div>
    </el-dialog>
    <el-drawer title="修改帐号" :visible.sync="dialog1" :direction="direction" size="600px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb10">
          您正在进行管理员帐号修改操作，修改后旧帐号失效，只可使用新帐号登录。
        </el-alert>
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form
              ref="adminAccountForm"
              :model="adminAccountData"
              :rules="rules"
              label-width="auto"
              class="m-form f-mt20"
            >
              <el-form-item label="管理员姓名：" prop="name">
                <el-input v-model="adminAccountData.name" clearable placeholder="请输入管理员姓名" />
              </el-form-item>
              <el-form-item label="管理员帐号：" prop="authenticationIdentity">
                <el-input v-model="adminAccountData.authenticationIdentity" clearable placeholder="请输入管理员账号" />
              </el-form-item>
              <el-form-item label="手机号：">
                <el-input v-model="adminAccountData.phone" clearable placeholder="请输入手机号" />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="handleCancel">取消</el-button>
                <el-button type="primary" @click="handleChangeAccount">确认</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </el-card>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  import { Administrator } from '@api/ms-gateway/ms-servicercontract-v1'
  import AdministratorModel from '@api/service/training-institution/online-school/models/AdministratorModel'
  import OnlineSchoolModule from '@api/service/training-institution/online-school/OnlineSchoolModule'
  import OnlineSchoolModel from '@api/service/training-institution/online-school/models/OnlineSchoolModel'
  import { UiPage } from '@hbfe/common'
  import { debounce, bind } from 'lodash-decorators'
  class PasswordContent {
    password: string
    rePassword: string
  }

  @Component
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.handleLoadData, this.handleLoadData)
    }

    @Ref('adminAccountForm') adminAccountForm: any
    @Ref('passwordForm') passwordForm: any
    // @Prop({
    //   required: true,
    //   default: () => {
    //     return new Array<AdministratorModel>()
    //   }
    // })
    administratorList: Array<AdministratorModel> = []
    @Prop({
      required: true,
      default: () => {
        return ''
      }
    })
    schoolId: string
    onlineSchoolObj: OnlineSchoolModule = new OnlineSchoolModule()
    adminAccountData: Administrator = new Administrator()
    passwordData: PasswordContent = new PasswordContent()
    page = new UiPage()
    dialog1 = false
    dialog2 = false
    dialog3 = false
    singleData: AdministratorModel = new AdministratorModel()
    direction = 'rtl'
    rules = {
      name: [{ required: true, message: '请输入管理员姓名', trigger: 'blur' }],
      authenticationIdentity: [{ required: true, message: '请输入管理员账号', trigger: 'blur' }]
    }
    passwordRules = {
      password: [{ required: true, validator: this.validatePassword, trigger: 'blur' }],
      rePassword: [{ required: true, validator: this.validateAgainPassword, trigger: 'blur' }]
    }
    passwordStrengthLow = false
    passwordStrengthIntermediate = false
    passwordStrengthHigh = false
    reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{8,18}$') // 验证密码的正则是否符合的正则
    created() {
      this.handleLoadData()
    }
    //密码强弱验证
    checkoutPasswordStrength() {
      const reg1 = /^.{1,8}$|^\d{9,}$|^[a-zA-Z]{9,}$|^(?=[\x21-\x7e]+)[^A-Za-z0-9]{9,}$/ //密码低强度正则--纯数字或字母或字符或长度1-8
      const reg2 = /^(?!\d+$)[a-zA-Z0-9]{9,}$|^(?![0-9]+$)[^a-zA-Z]{9,}$|^(?![a-zA-Z]+$)[^0-9]{9,}$/ //密码中强度正则--有两种且长度在9-12
      const reg3 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{13,}$/ //密码高强度正则--三种都有且长度大于12
      const reg4 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{9,12}$/ //密码中强度正则--介于9-12位的三种字符都有的密码
      if (!this.passwordData.password) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = false
      } else if (reg1.test(this.passwordData.password)) {
        this.passwordStrengthLow = true
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = false
      } else if (reg2.test(this.passwordData.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = true
        this.passwordStrengthHigh = false
      } else if (reg3.test(this.passwordData.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = true
      } else if (reg4.test(this.passwordData.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = true
        this.passwordStrengthHigh = false
      }
    }
    //密码验证
    validatePassword(rule: any, value: any, callback: any) {
      if (!this.reg.test(this.passwordData.password)) {
        callback(new Error('请输入8~18位由数字、字母或符号组成的密码'))
      } else {
        callback()
      }
    }
    //再次输入密码验证
    validateAgainPassword(rule: any, value: any, callback: any) {
      if (!value) {
        callback(new Error('请再次输入密码'))
      } else if (value != this.passwordData.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }
    // 加载表格
    async handleLoadData() {
      this.onlineSchoolObj.onlineSchool = new OnlineSchoolModel()
      this.onlineSchoolObj.onlineSchool.schoolId = this.schoolId
      this.administratorList = await this.onlineSchoolObj.queryAdminAccountList(this.page)
      console.log(this.administratorList, 'res administratorList')
    }
    // 修改账号 弹窗
    handleChangeAccountDialog(row: AdministratorModel) {
      this.singleData = row
      // this.adminAccountData.name = this.singleData.administratorName
      // this.adminAccountData.authenticationIdentity = this.singleData.account
      // this.adminAccountData.phone = this.singleData.phone
      this.adminAccountData = {
        name: row.administratorName,
        authenticationIdentity: row.account,
        phone: row.phone
      }
      this.dialog1 = true
    }
    // 停启用 弹窗
    handleChangeStatusDialog(row: AdministratorModel) {
      this.singleData = row
      this.dialog2 = true
    }
    // 重置密码 弹窗
    handleResetPasswordDialog(row: AdministratorModel) {
      this.singleData = row
      this.dialog3 = true
    }
    // 修改账号
    @bind
    @debounce(200)
    async handleChangeAccount() {
      this.adminAccountForm.validate(async (valid: any) => {
        if (valid) {
          const res = await this.singleData.updateAdministrator({
            administratorName: this.adminAccountData.name,
            account: this.adminAccountData.authenticationIdentity,
            phone: this.adminAccountData.phone,
            schoolId: this.schoolId
          })
          if (res.status.code == 200) {
            this.$message.success('操作成功')
            this.handleCancel()
            this.handleLoadData()
          }
        } else {
          return false
        }
      })
    }
    // 重置密码
    @bind
    @debounce(200)
    async handleResetPassword() {
      this.passwordForm.validate(async (valid: any) => {
        if (valid) {
          const res = await this.singleData.resetPassword(this.passwordData.password)
          if (res.status.code == 200) {
            this.$message.success('操作成功')
            this.handleCancel()
            this.handleLoadData()
          }
        } else {
          return false
        }
      })
    }
    // 停启用
    @bind
    @debounce(200)
    async handleChangeStatus() {
      const res = await this.singleData.changeAdministratorStatus()
      if (res.status.code == 200) {
        this.$message.success('操作成功')
        this.handleCancel()
        // 修改状态接口慢
        setTimeout(() => {
          this.handleLoadData()
        }, 1000)
      }
    }
    handleCancel() {
      this.dialog3 = false
      this.dialog2 = false
      this.dialog1 = false
    }
  }
</script>

<style lang="scss" scoped>
  .el-collapse-item__header {
    font-size: 16px;
    font-weight: bold;
    padding-left: 20px;
    padding-right: 10px;
  }
</style>
