<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <template
        v-if="$hasPermission('orderReconciliation,orderReconciliationFx,orderReconciliationZt')"
        desc="orderReconciliation:报名订单对账,orderReconciliationFx:报名订单对账（分销）,orderReconciliationZt:报名订单对账（专题）"
        query
        actions="orderReconciliation:doSearch,@BizPortalSelect,@BizDistributorSelect#orderReconciliationFx:doSearchfx,@BizPortalDistributorSelect#orderReconciliationZt:doSearchzt"
      >
        <!--条件查询-->
        <hb-search-wrapper @reset="reset" class="m-query is-border-bottom">
          <el-form-item label="收款账号" v-if="!isZtlogin">
            <el-input
              id="input"
              v-model="accountName"
              clearable
              placeholder="请选择收款账号"
              @focus="editInvoicePopup()"
            />
          </el-form-item>
          <el-form-item label="订单号">
            <el-input v-model="checkAccountParam.orderId" clearable placeholder="请输入订单号" />
          </el-form-item>
          <el-form-item label="交易流水号">
            <el-input v-model="checkAccountParam.batchId" clearable placeholder="请输入交易流水号" />
          </el-form-item>
          <el-form-item label="交易成功时间">
            <double-date-picker
              :begin-create-time.sync="checkAccountParam.startDate"
              :end-create-time.sync="checkAccountParam.endDate"
            ></double-date-picker>
          </el-form-item>

          <el-form-item label="销售渠道" v-if="!isFxlogin && !isZtlogin">
            <el-select v-model="checkAccountParam.saleSource" clearable filterable placeholder="请选择销售渠道">
              <el-option
                v-for="item in saleChannelList"
                :key="item.code"
                :value="item.code"
                :label="item.desc"
              ></el-option>
            </el-select>
          </el-form-item>

          <!--  v-if="isFXshow" -->
          <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="分销商">
            <biz-distributor-select
              v-model="checkAccountParam.distributorId"
              :name="distributorName"
            ></biz-distributor-select>
          </el-form-item>
          <el-form-item v-if="!isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
            <biz-portal-select
              v-model="checkAccountParam.promotionPortalId"
              :disabled="checkAccountParam.isDistributionExcludePortal"
              :name="promotionPortalName"
            ></biz-portal-select>
          </el-form-item>
          <el-form-item v-if="isFxlogin && isHadFxAbility && !isZtlogin" label="推广门户简称">
            <biz-portal-distributor-select
              v-model="checkAccountParam.promotionPortalId"
              :disabled="checkAccountParam.isDistributionExcludePortal"
              :name="promotionPortalName"
            ></biz-portal-distributor-select>
          </el-form-item>
          <el-form-item v-if="!isZtlogin">
            <el-checkbox
              label="查看非门户推广数据"
              name="type"
              @change="checkAccountParam.promotionPortalId = ''"
              v-model="checkAccountParam.isDistributionExcludePortal"
            ></el-checkbox>
          </el-form-item>

          <el-form-item label="专题名称" v-if="topPicNameFilterShow && !isFxlogin">
            <el-input v-model="checkAccountParam.specialSubjectName" clearable placeholder="请输入专题进行查询" />
          </el-form-item>
          <el-form-item label="培训方案" v-if="!isFxlogin && !isZtlogin">
            <!-- <biz-learning-scheme-select v-model="commoditySkuIdList"></biz-learning-scheme-select> -->
            <learning-scheme-select-diff
              v-model="commoditySkuIdList"
              @clearPeriod="clearPeriod()"
            ></learning-scheme-select-diff>
            <!-- <learning-scheme-select v-model="commoditySkuIdList"></learning-scheme-select> -->
          </el-form-item>
          <el-form-item label="培训方案" v-if="isFxlogin">
            <fx-learning-scheme-select v-model="commoditySkuIdList"></fx-learning-scheme-select>
            <!-- <learning-scheme-select v-model="commoditySkuIdList"></learning-scheme-select> -->
          </el-form-item>
          <el-form-item label="培训方案" v-if="isZtlogin">
            <zt-learning-scheme-select v-model="commoditySkuIdList"></zt-learning-scheme-select>
          </el-form-item>
          <el-form-item label="期别名称" v-if="showPeriodName && !isFxlogin">
            <biz-period-select :scheme-id="commoditySkuIdList[0].id" v-model="checkAccountParam.periodId" />
          </el-form-item>
          <el-form-item label="期别名称" v-if="showPeriodName && isFxlogin">
            <biz-fx-period-select :scheme-id="commoditySkuIdList[0].id" v-model="checkAccountParam.periodId" />
          </el-form-item>

          <template slot="actions">
            <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
            <template
              v-if="$hasPermission('export,exportFx,exportZt')"
              desc="export:导出,exportFx:导出（分销）,exportZt:导出（专题）"
              query
              actions="export:exportDataty#exportFx:exportDatafx#exportZt:exportDatazt"
            >
              <el-button @click="exportData">导出列表数据</el-button>
            </template>
          </template>
        </hb-search-wrapper>
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert">
            <div class="f-c6">
              当前共有
              <span class="f-fb f-co">{{ orderCount }}</span>
              笔订单，交易总额 <span class="f-fb f-co">¥ {{ amount }}</span
              >。
            </div>
          </el-alert>
        </div>
        <!--表格-->
        <el-table
          stripe
          ref="tableRef"
          :data="checkAccountListResponse"
          v-loading="query.loading"
          max-height="500px"
          class="m-table f-mt10"
          @sort-change="sortChange"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="订单号" min-width="220" fixed="left">
            <template #default="scope">
              <hb-copy :content="scope.row.orderId"></hb-copy>
              {{ scope.row.orderId }}
              <p>
                <el-tag type="warning" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.distribution"
                  >分销推广
                </el-tag>
                <el-tag type="success" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag>
                <el-tag type="danger" size="small" v-if="scope.row.thirdPartyPlatform">{{
                  scope.row.thirdPartyPlatform
                }}</el-tag>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="交易流水号" prop="batchId" min-width="300"> </el-table-column>
          <el-table-column label="交易成功时间" prop="startDate" min-width="180" sortable> </el-table-column>
          <el-table-column label="购买人信息" min-width="240">
            <template #default="scope">
              <p>姓名：{{ scope.row.name || '-' }}</p>
              <p v-if="queryShowLoginAccount.isShowLoginAccount">登录账号：{{ scope.row.loginAccount || '-' }}</p>
              <p>证件号：{{ scope.row.idCard || '-' }}</p>
              <p>手机号：{{ scope.row.phone || '-' }}</p>
            </template>
          </el-table-column>
          <!-- <el-table-column label="退货/款类型" width="180">
            <template slot-scope="scope"> {{ OrderRefundStatus.map.get(scope.row.refundType) }} </template>
          </el-table-column> -->
          <el-table-column label="实付金额(元)" width="140" align="right">
            <template #default="scope">
              <div>{{ scope.row.money }}</div>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-个人报名对账</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
          </div>
        </el-dialog>
        <template v-if="$hasPermission('editInvoicePopup')" query desc="选择收款账号" actions="@accountNumber">
          <account-number
            :visible.sync="editInvoiceDialog"
            ref="accountNumberRef"
            :get-data="getData"
            @getAccountNumber="getAccountNumber"
          ></account-number>
        </template>
      </template>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import QueryCheckAccount from '@api/service/diff/management/qztg/trade/order/QueryCheckAccount'
  import CheckAccountParam from '@api/service/diff/management/qztg/trade/order/model/CheckAccountParam'
  import CheckAccountListResponse from '@api/service/diff/management/qztg/trade/order/model/CheckAccountListResponse'
  import ReceiveAccountVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountVo'
  import accountNumber from '@hbfe/jxjy-admin-trade/src/refund/components/account-number.vue'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { OrderSortRequest, SortPolicy } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/qztg/trade/enums/SaleChannelType'
  import LearningSchemeSelectDiff from '@hbfe/jxjy-admin-trade/src/diff/qztg/order/personal/components/learning-scheme-select-diff.vue'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'

  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import BizPortalDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-distributor-select.vue'
  import MutationCheckAccountInTrainingChannel from '@api/service/management/trade/single/checkAccount/mutation/MutationCheckAccountInTrainingChannel'
  import QueryCheckAccountInTrainingChannel from '@api/service/diff/management/qztg/trade/order/QueryQztgCheckAccountInTrainingChannel'
  import { bind, debounce } from 'lodash-decorators'
  import OrderRefundStatus from '@api/service/common/return-order/enums/OrderRefundStatus'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import ZtLearningSchemeSelect from '@hbfe/jxjy-admin-trade/src/order/personal/components/zt-learning-scheme-select.vue'
  import FxLearningSchemeSelect from '@hbfe/fx-manage/src/components/biz/biz-learning-scheme-select.vue'

  @Component({
    components: {
      DoubleDatePicker,
      accountNumber,
      LearningSchemeSelectDiff,
      BizDistributorSelect,
      BizPortalSelect,
      BizPortalDistributorSelect,
      FxLearningSchemeSelect,
      ZtLearningSchemeSelect
    }
  })
  export default class extends Vue {
    @Ref('accountNumberRef') accountNumberRef: any

    select = ''
    input = ''
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    page: UiPage
    query: Query = new Query()
    queryCheckAccount = new QueryCheckAccount()
    //入参
    checkAccountParam = new CheckAccountParam()
    exportParam: CheckAccountParam = new CheckAccountParam()
    sortRequest: Array<OrderSortRequest> = new Array<OrderSortRequest>()
    //查询结果
    checkAccountListResponse = new Array<CheckAccountListResponse>()
    form = {
      data1: ''
    }
    loading = false // 加载中
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    /**
     * 打开-弹窗标识
     */
    editInvoiceDialog = false
    getData = new ReceiveAccountVo()
    accountName = ''
    //导出成功弹窗
    exportSuccessVisible = false
    SaleChannelEnum = SaleChannelEnum
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    mutationZtCheckAccount = new MutationCheckAccountInTrainingChannel()
    queryZtReconciliation = new QueryCheckAccountInTrainingChannel()
    TrainingModeEnum = TrainingModeEnum
    //退货状态
    OrderRefundStatus = OrderRefundStatus
    // 获取销售渠道列表
    saleChannelList = SaleChannelType.list()
    /**
     * 商品id数组
     */
    commoditySkuIdList = new Array<HasSelectSchemeMode>()

    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable

    promotionPortalName = ''
    distributorName = ''

    // 专题名称筛选显示
    get topPicNameFilterShow() {
      return (
        this.checkAccountParam.saleSource === SaleChannelEnum.topic ||
        (!this.checkAccountParam.saleSource && this.checkAccountParam.saleSource !== SaleChannelEnum.self)
      )
    }
    get showPeriodName() {
      return [this.TrainingModeEnum.mixed, this.TrainingModeEnum.offline].includes(
        this.commoditySkuIdList[0]?.trainingMode?.skuPropertyValueId
      )
    }
    @Watch('commoditySkuIdList', { deep: true })
    trainingProgrammeListChange() {
      this.checkAccountParam.periodId = ''
    }
    get orderCount() {
      if (!this.isZtlogin) {
        return this.queryCheckAccount.orderCount
      } else {
        return this.queryZtReconciliation.orderCount
      }
    }
    get amount() {
      if (!this.isZtlogin) {
        return this.queryCheckAccount.amount
      } else {
        return this.queryZtReconciliation.amount
      }
    }
    // 培训方案入参
    @Watch('commoditySkuIdList', {
      deep: true
    })
    changeScheme(val: Array<HasSelectSchemeMode>) {
      if (val?.length && val[0]?.schemeId) {
        this.checkAccountParam.commoditySkuId = val[0].schemeId
      } else {
        this.checkAccountParam.commoditySkuId = undefined
      }
    }

    constructor() {
      super()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.page = new UiPage(this.doSearchfx, this.doSearchfx)
      } else if (this.isZtlogin) {
        this.page = new UiPage(this.doSearchzt, this.doSearchzt)
      } else {
        this.page = new UiPage(this.doSearch, this.doSearch)
      }
    }
    async doSearch() {
      this.query.loading = true
      this.checkAccountListResponse = []
      this.checkAccountParam.trainingProgramId = this.commoditySkuIdList[0]?.schemeId

      try {
        this.checkAccountListResponse = await this.queryCheckAccount.queryOfRegistrationOrder(
          this.page,
          this.checkAccountParam
        )
        this.$nextTick(() => {
          ;(this.$refs['tableRef'] as any)?.doLayout()
        })
      } catch (e) {
        console.log(e, '个人报名对账列表获取失败')
      } finally {
        ;(this.$refs['tableRef'] as any)?.doLayout()
        this.query.loading = false
      }
    }
    async doSearchfx() {
      this.query.loading = true
      this.checkAccountListResponse = []
      this.checkAccountParam.trainingProgramId = this.commoditySkuIdList[0]?.schemeId

      try {
        this.checkAccountListResponse = await this.queryCheckAccount.queryOfFxRegistrationOrder(
          this.page,
          this.checkAccountParam
        )
        this.$nextTick(() => {
          ;(this.$refs['tableRef'] as any)?.doLayout()
        })
      } catch (e) {
        console.log(e, '分销个人报名对账列表获取失败')
      } finally {
        ;(this.$refs['tableRef'] as any)?.doLayout()
        this.query.loading = false
      }
    }

    @bind
    @debounce(200)
    async doSearchzt() {
      this.query.loading = true
      this.checkAccountListResponse = []
      this.checkAccountParam.trainingProgramId = this.commoditySkuIdList[0]?.schemeId

      try {
        this.checkAccountListResponse = await this.queryOfZtRegistrationOrder()
        this.$nextTick(() => {
          ;(this.$refs['tableRef'] as any)?.doLayout()
        })
      } catch (e) {
        console.log(e, '分销个人报名对账列表获取失败')
      } finally {
        ;(this.$refs['tableRef'] as any)?.doLayout()
        this.query.loading = false
      }
    }
    async queryOfZtRegistrationOrder() {
      return await this.queryZtReconciliation.queryQztgOfRegistrationOrder(this.page, this.checkAccountParam)
    }
    async exportDataty() {
      return await this.queryCheckAccount.listExport(this.exportParam)
    }

    async exportDatafx() {
      return await this.queryCheckAccount.listFxExport(this.exportParam)
    }
    async exportDatazt() {
      return await this.mutationZtCheckAccount.listExport(this.exportParam)
    }
    async exportData() {
      try {
        this.exportParam = Object.assign(new CheckAccountParam(), this.checkAccountParam)

        let res

        if (this.isHadFxAbility && this.isFxlogin) {
          res = await this.exportDatafx()
        } else if (this.isZtlogin) {
          res = await this.exportDatazt()
        } else {
          res = await this.exportDataty()
        }
        if (res) {
          //   this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }

    /**
     * 重置
     */
    async reset() {
      // this.questionRequestVo = new QuestionRequestVo()
      this.page.pageNo = 1
      this.accountName = ''
      this.promotionPortalName = ''
      this.distributorName = ''
      this.commoditySkuIdList = new Array<HasSelectSchemeMode>()
      this.checkAccountParam = new CheckAccountParam()
      this.commoditySkuIdList = new Array<HasSelectSchemeMode>()
      if (this.isFxlogin && this.isHadFxAbility) {
        this.doSearchfx()
      } else if (this.isZtlogin) {
        await this.doSearchzt()
      } else {
        this.doSearch()
      }
    }
    /**
     * 排序
     */
    sortChange(column: any) {
      // console.log(column.order, 1)
      this.sortRequest = []
      const item = new OrderSortRequest()
      if (column.order === 'ascending') {
        //正序
        item.policy = 'ASC' as SortPolicy
        this.sortRequest.push(item)
      } else if (column.order === 'descending') {
        item.policy = 'DESC' as SortPolicy
        this.sortRequest.push(item)
      } else {
        this.sortRequest = []
      }
      // console.log(this.sortRequest, ' this.sortRequest')
    }
    async editInvoicePopup() {
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.accountNumberRef.doQueryPagefx()
      } else if (this.isZtlogin) {
        await this.accountNumberRef.doSearchzt()
      } else {
        await this.accountNumberRef.doQueryPage()
      }
      const inputEl = document.getElementById('input')
      inputEl.blur()
      this.editInvoiceDialog = true
    }
    getAccountNumber(idList: ReceiveAccountVo[]) {
      this.getData = idList[0]
      this.checkAccountParam.paymentAccountID = this.getData?.id ? this.getData.id : ''
      this.accountName = this.getData?.accountName
    }

    @Watch('accountName', {
      deep: true,
      immediate: true
    })
    accountNameChange() {
      if (this.accountName === '') {
        this.getAccountNumber([])
      }
    }
    //前往下载
    goDownloadPage() {
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportReconciliation' }
      })

      this.exportSuccessVisible = false
    }
    async created() {
      if (this.isFxlogin && this.isHadFxAbility) {
        this.doSearchfx()
      } else if (this.isZtlogin) {
        await this.doSearchzt()
      } else {
        this.doSearch()
      }
    }

    // 清空期别
    clearPeriod() {
      this.checkAccountParam.periodId = undefined
    }
  }
</script>
