schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	copyQuestionnaire(request:CopyQuestionnaireRequest):String
	"""非教师评价题创建
		@param request
		@return
	"""
	createQuestionnaireQuestion(request:CreateQuestionRequest):String
	"""教师评价多选题创建
		@param request
		@return
	"""
	createTeacherEvaluationMultipleChoiceQuestionnaireQuestion(request:CreateQuestionRequest):String
	"""教师评价单选题创建
		@param request
		@return
	"""
	createTeacherEvaluationSingleChoiceQuestionnaireQuestion(request:CreateQuestionRequest):String
	removeQuestionnaireQuestion(id:String!):Void
	"""非教师评价题更新
		@param request
		@return
	"""
	updateQuestionnaireQuestion(request:UpdateQuestionRequest):String
	"""教师评价多选题更新
		@param request
		@return
	"""
	updateTeacherEvaluationMultipleChoiceQuestionnaireQuestion(request:UpdateQuestionRequest):String
	"""教师评价单选题更新
		@param request
		@return
	"""
	updateTeacherEvaluationSingleChoiceQuestionnaireQuestion(request:UpdateQuestionRequest):String
}
"""正确率评定方式
	<AUTHOR>
"""
input CorrectRateEvaluatePatternRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.evaluatepattern.CorrectRateEvaluatePatternRequest",implementsInputs:["EvaluatePatternRequest"]) {
	"""评定方式类型
		1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
		@see EvaluatePatternTypes
	"""
	type:Int!
}
"""评定方式
	<AUTHOR>
"""
input EvaluatePatternRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.evaluatepattern.EvaluatePatternRequest") {
	"""评定方式类型
		1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
		@see EvaluatePatternTypes
	"""
	type:Int!
}
"""无评定方式
	<AUTHOR>
"""
input NoneEvaluatePatternRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.evaluatepattern.NoneEvaluatePatternRequest",implementsInputs:["EvaluatePatternRequest"]) {
	"""评定方式类型
		1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
		@see EvaluatePatternTypes
	"""
	type:Int!
}
"""计分评定方式
	<AUTHOR>
"""
input OnlyScoringEvaluatePatternRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.evaluatepattern.OnlyScoringEvaluatePatternRequest",implementsInputs:["EvaluatePatternRequest"]) {
	scoringDescribes:[ScoringDescribe]
	"""评定方式类型
		1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
		@see EvaluatePatternTypes
	"""
	type:Int!
}
"""<AUTHOR>
input QuestionScoreRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.evaluatepattern.QuestionScoreRequest") {
	"""试题id"""
	questionId:String
	"""分数"""
	score:Double!
}
"""试题分数设置信息
	<AUTHOR>
"""
input QuestionScoreSettingRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.evaluatepattern.QuestionScoreSettingRequest") {
	"""大题序号"""
	sequence:Int
	"""试题类型
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""每题平均分"""
	eachQuestionScore:Double!
	"""具体试题分数"""
	questionScores:[QuestionScoreRequest]
}
"""分值评定方式
	<AUTHOR>
"""
input ScoreEvaluatePatternRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.evaluatepattern.ScoreEvaluatePatternRequest",implementsInputs:["EvaluatePatternRequest"]) {
	"""总分"""
	totalScore:Double!
	"""合格分数"""
	qualifiedScore:Double!
	"""试题分数"""
	questionScores:[QuestionScoreSettingRequest]
	"""多选提漏选得分模式
		0:不得分|1：的全部分数|2：得一半分数|3：每个选项按平均得分
		@see MultipleQuestionMissScorePatterns
	"""
	multipleMissScorePattern:Int!
	"""评定方式类型
		1-正确率评定方式  2-无评定方式  3-分值评定方式 4-仅分值评定方式
		@see EvaluatePatternTypes
	"""
	type:Int!
}
"""单选题计分描述"""
input RadioScoringDescribe @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.evaluatepattern.onlyscoringevaluatepattern.RadioScoringDescribe",implementsInputs:["ScoringDescribe"]) {
	"""分数来源类型
		@see  ScoreSourceTypes
	"""
	scoreSourceTypes:Int
	type:Int
}
"""计分描述基类
	<AUTHOR>
"""
input ScoringDescribe @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.evaluatepattern.onlyscoringevaluatepattern.ScoringDescribe") {
	type:Int
}
"""选择题答案选项实体
	<AUTHOR>
"""
input ChooseAnswerOptionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.ChooseAnswerOptionRequest") {
	"""答案ID"""
	id:String!
	"""答案内容"""
	content:String!
	"""选项建议分数"""
	suggestionScore:Double
	"""是否允许填空"""
	enableFillContent:Boolean
	"""填空是否必填"""
	mustFillContent:Boolean
}
"""<AUTHOR> create 2021/6/29 14:03"""
input CreateQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.CreateQuestionRequest") {
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""<AUTHOR> create 2021/6/29 14:03"""
input UpdateQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.UpdateQuestionRequest") {
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""<AUTHOR> create 2021/6/28 14:13"""
input CreateAskQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.ask.CreateAskQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""<AUTHOR> create 2021/6/28 14:13"""
input UpdateAskQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.ask.UpdateAskQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""<AUTHOR> create 2021/6/28 14:43"""
input ChildQuestionCreateInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.father.ChildQuestionCreateInfoRequest") {
	"""子题序号"""
	no:Int!
	"""试题内容"""
	question:CreateQuestionRequest
}
"""<AUTHOR> create 2021/6/28 14:43"""
input ChildQuestionUpdateInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.father.ChildQuestionUpdateInfoRequest") {
	"""子题序号"""
	no:Int!
	"""试题内容"""
	question:UpdateQuestionRequest
	"""新增试题"""
	newQuestion:CreateQuestionRequest
}
"""<AUTHOR> create 2021/6/28 14:43"""
input CreateFatherQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.father.CreateFatherQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""子题集合"""
	childQuestions:[ChildQuestionCreateInfoRequest]!
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""<AUTHOR> create 2021/6/28 14:43"""
input UpdateFatherQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.father.UpdateFatherQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""子题集合"""
	childQuestions:[ChildQuestionUpdateInfoRequest]!
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""填空题创建命令
	<AUTHOR> create 2021/6/28 14:09
"""
input CreateFillQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.fill.CreateFillQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""填空数"""
	fillCount:Int!
	"""正确答案"""
	correctAnswer:FillAnswerRequest
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""散乱无序填空题答案实体
	<AUTHOR>
"""
input DisarrayFillAnswerRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.fill.DisarrayFillAnswerRequest",implementsInputs:["FillAnswerRequest"]) {
	"""正确答案集合"""
	correctAnswers:[[String]]!
	"""答案类型"""
	type:FillAnswerType!
}
"""填空题答案基类
	<AUTHOR>
"""
input FillAnswerRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.fill.FillAnswerRequest") {
	"""答案类型"""
	type:FillAnswerType!
}
"""<AUTHOR> create 2021/6/29 15:41"""
input FillCorrectAnswers @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.fill.FillCorrectAnswers") {
	"""空格位置"""
	blankNo:Int!
	"""答案备选项"""
	answers:[String]
}
"""按序填空题答案
	<AUTHOR>
"""
input SequenceFillAnswerRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.fill.SequenceFillAnswerRequest",implementsInputs:["FillAnswerRequest"]) {
	correctAnswers:[FillCorrectAnswers]!
	"""答案类型"""
	type:FillAnswerType!
}
"""按序关联填空题答案实体
	<AUTHOR>
"""
input SequenceRateFillAnswerRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.fill.SequenceRateFillAnswerRequest",implementsInputs:["FillAnswerRequest"]) {
	"""正确答案集合"""
	correctAnswers:[SequenceFillAnswerRequest]
	"""答案类型"""
	type:FillAnswerType!
}
"""填空题创建命令
	<AUTHOR> create 2021/6/28 14:09
"""
input UpdateFillQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.fill.UpdateFillQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""填空数"""
	fillCount:Int!
	"""正确答案"""
	correctAnswer:FillAnswerRequest
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""多选题创建命令
	<AUTHOR> create 2021/6/28 14:07
"""
input CreateMultipleQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.multiple.CreateMultipleQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""可选答案列表【必填】"""
	answerOptions:[ChooseAnswerOptionRequest]!
	"""正确答案ID集合【必填】"""
	correctAnswerIds:[String]!
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""多选题创建命令
	<AUTHOR> create 2021/6/28 14:07
"""
input UpdateMultipleQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.multiple.UpdateMultipleQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""可选答案列表【必填】"""
	answerOptions:[ChooseAnswerOptionRequest]!
	"""正确答案ID集合【必填】"""
	correctAnswerIds:[String]!
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""判断题创建命令
	<AUTHOR> create 2021/6/28 14:05
"""
input CreateOpinionQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.opinion.CreateOpinionQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""正确答案【必填】"""
	correctAnswer:Boolean!
	"""正确文本【必填】"""
	correctAnswerText:String
	"""不正确文本【必填】"""
	incorrectAnswerText:String
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""判断题创建命令
	<AUTHOR> create 2021/6/28 14:05
"""
input UpdateOpinionQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.opinion.UpdateOpinionQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""正确答案【必填】"""
	correctAnswer:Boolean!
	"""正确文本【必填】"""
	correctAnswerText:String!
	"""不正确文本【必填】"""
	incorrectAnswerText:String!
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""单选题创建命令
	<AUTHOR> create 2021/6/28 9:39
"""
input CreateRadioQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.radio.CreateRadioQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""可选答案列表【必填】"""
	answerOptions:[ChooseAnswerOptionRequest]!
	"""正确答案ID【必填】"""
	correctAnswerId:String
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""单选题创建命令
	<AUTHOR> create 2021/6/28 9:39
"""
input UpdateRadioQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.radio.UpdateRadioQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""可选答案列表"""
	answerOptions:[ChooseAnswerOptionRequest]!
	"""正确答案ID"""
	correctAnswerId:String
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""@Author: chenzeyu
	@CreateTime: 2024-07-29  16:12
	@Description: 量表题创建请求
"""
input CreateScaleQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.scale.CreateScaleQuestionRequest",implementsInputs:["CreateQuestionRequest"]) {
	"""量表类型
		@see ScaleTypes
	"""
	scaleType:Int!
	"""程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
	"""试题Id"""
	id:String
	"""试题题目【必填】"""
	topic:String!
	"""试题类型【必填】1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID【必填】"""
	libraryId:String!
	"""是否启用"""
	enabled:Boolean!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
	"""内置试题，用于调查问卷等固定卷场景，内置试题不在试题管理展示、不参与常规智能抽题目（通过保证内置试题是停用状态）。"""
	buildIn:Boolean
}
"""@Author: chenzeyu
	@CreateTime: 2024-07-30  15:40
	@Description: 量表题修改请求
"""
input UpdateScaleQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.scale.UpdateScaleQuestionRequest",implementsInputs:["UpdateQuestionRequest"]) {
	"""量表类型
		@see ScaleTypes
	"""
	scaleType:Int!
	"""程度_始，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleTypes#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
	"""试题id"""
	id:String!
	"""试题题目"""
	topic:String!
	"""试题类型 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题 7-量表题"""
	questionType:Int!
	"""所属题库ID"""
	libraryId:String!
	"""试题解析"""
	dissects:String
	"""关联课程id"""
	relateCourseIds:[String]
	"""试题难度
		@see com.fjhb.domain.exam.api.question.consts.QuestionDifficulty
		1-难度 2-中等难度  3-高难度
	"""
	questionDifficulty:Int!
}
"""<AUTHOR> create 2021/6/3 17:35"""
input CopyQuestionnaireQuestion @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.questionnaire.CopyQuestionnaireQuestion") {
	"""试题ID"""
	questionId:String
	"""分数，-1表示不为分数评定方式为"""
	score:Double!
	"""所属大题序号，-1表示试卷没有使用大题"""
	groupSequence:Int!
	"""试题类型
		@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
	"""
	questionType:Int!
	"""是否必答"""
	answerRequired:Boolean
	"""教师评价题标签code，非教师评价题不用传"""
	teacherEvaluateCode:String
	"""复制的试题内容"""
	copyQuestionContent:CreateQuestionRequest
}
"""出卷模式基类
	<AUTHOR> create 2021/8/20 15:05
"""
input CopyQuestionnaireRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.questionnaire.CopyQuestionnaireRequest") {
	"""出卷配置名称"""
	name:String
	"""适用范围 用于筛选自定义的分类
		@see com.fjhb.domain.exam.api.consts.UsageScopes
	"""
	usageScope:Int!
	"""调查问卷类型
		@see com.fjhb.domain.exam.api.consts.QuestionnairePaperPublishConfigureType
	"""
	questionnaireType:Int
	"""出卷模式，固定卷"""
	publishPattern:QuestionnairePaperRequest
	"""分类id"""
	paperPublishConfigureCategoryId:String
	"""是否是草稿    1-是 2-不是"""
	isDraft:Int!
}
"""<AUTHOR> create 2021/6/3 17:37"""
input QuestionGroupDTO @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.questionnaire.QuestionGroupDTO") {
	"""序号"""
	sequence:Int!
	"""试题类型"""
	questionType:Int!
	"""大题名称"""
	groupName:String
	"""每题平均分数，-1表示不为分数评定方式"""
	eachQuestionScore:Double!
}
"""调查问卷固定卷出卷模式
	<AUTHOR> create 2021/8/20 16:08
"""
input QuestionnairePaperRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.questionnaire.QuestionnairePaperRequest") {
	"""试卷id"""
	id:String
	"""出卷模式类型
		@see PublishPatterns
	"""
	type:Int!
	"""试卷名称"""
	name:String
	"""描述"""
	description:String
	"""作答时长"""
	timeLength:Int!
	"""试卷总分"""
	totalScore:Double!
	"""大题集合"""
	groups:[QuestionGroupDTO]
	"""试题集合"""
	questions:[CopyQuestionnaireQuestion]
	"""评定方式"""
	evaluatePattern:EvaluatePatternRequest
}
"""<AUTHOR> create 2021/6/29 14:23"""
enum FillAnswerType @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.question.FillAnswerType") {
	disarray
	sequence
	sequenceRelate
}

scalar List
