import SimpleCourseDetail from '@api/service/customer/online-school-config/excellent-course/vo/SimpleCourseDetail'
import { ExcellentCoursesCategoryResponse } from '@api/ms-gateway/ms-servicer-series-v1'

class ExcellentCourseCategory {
  id: string
  name: string
  courses: Array<SimpleCourseDetail> = new Array<SimpleCourseDetail>()
  sort: number

  static from(response: ExcellentCoursesCategoryResponse) {
    const detail = new ExcellentCourseCategory()
    detail.id = response.categoryId
    detail.sort = response.sort
    detail.courses = response.courses.map(SimpleCourseDetail.from)
    return detail
  }
}

export default ExcellentCourseCategory
