<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--查看编排详情-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">查看编排详情</el-button>
        <el-drawer
          title="查看编排详情"
          :visible.sync="dialog1"
          :direction="direction"
          size="1400px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-tit"><span class="tit-txt">课程学习</span></div>
            <el-alert type="warning" :closable="false" class="m-alert f-mb10" show-icon
              >期望开始学习时间：2024-10-07</el-alert
            >
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table" max-height="600px">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="课程名称" min-width="220">
                <template>海洋经济</template>
              </el-table-column>
              <el-table-column label="学习开始时间" min-width="220">
                <template>2025-02-01 10:00:00</template>
              </el-table-column>
              <el-table-column label="学习结束时间" min-width="220">
                <template>2025-02-01 10:00:00</template>
              </el-table-column>
              <el-table-column label="智能学习完成进度" min-width="220">
                <template>100%</template>
              </el-table-column>
              <el-table-column label="学习完成情况" min-width="220">
                <template>已完成</template>
              </el-table-column>
              <el-table-column label="测验开始时间" min-width="220">
                <template>2024-09-30 18:53:27</template>
              </el-table-column>
              <el-table-column label="测验结束时间" min-width="220">
                <template>2024-09-30 18:53:27</template>
              </el-table-column>
              <el-table-column label="测验完成情况" min-width="220">
                <template>
                  <p>已完成</p>
                  <p class="f-cr">测验分数：89</p>
                </template>
              </el-table-column>
            </el-table>
            <div class="m-tit"><span class="tit-txt">班级考试</span></div>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table" max-height="600px">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="考试场次" min-width="220">
                <template>模拟考试</template>
              </el-table-column>
              <el-table-column label="考试开始时间" min-width="220">
                <template>2025-02-01 10:00:00</template>
              </el-table-column>
              <el-table-column label="考试结束时间" min-width="220">
                <template>2025-02-01 10:00:00</template>
              </el-table-column>
              <el-table-column label="考试完成情况" min-width="220">
                <template>100%</template>
              </el-table-column>
              <el-table-column label="考试成绩" min-width="220">
                <template>85</template>
              </el-table-column>
            </el-table>
            <div class="m-tit"><span class="tit-txt">调研问卷</span></div>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table" max-height="600px">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="问卷名称" min-width="220">
                <template>班级问卷纳入考核</template>
              </el-table-column>
              <el-table-column label="问卷开始时间" min-width="220">
                <template>2025-02-01 10:00:00</template>
              </el-table-column>
              <el-table-column label="问卷结束时间" min-width="220">
                <template>2025-02-01 10:00:00</template>
              </el-table-column>
              <el-table-column label="问卷完成情况" min-width="220">
                <template>已完成</template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">返 回</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
