<route-meta>
  {
  "isMenu": true,
  "title": "个人报名发票",
  "sort": 1
  }
</route-meta>
<template>
  <el-main
    v-if="$hasPermission('personalBill,personalBillZt')"
    desc="personalBill:个人报名发票,personalBillZt:个人报名发票(专题)"
    actions="personalBill:@ElectronicInvoiceAuto#personalBillZt:@ElectronicInvoiceAuto"
  >
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky" @tab-click="handleClick">
      <template
        v-if="$hasPermission('autoBill,autoBillZt')"
        desc="autoBill:自动开票列表,autoBillZt:自动开票列表(专题)"
        actions="autoBill:@ElectronicInvoiceAuto#autoBillZt:@ElectronicInvoiceAuto"
      >
        <el-tab-pane label="增值税电子普通发票（自动开票）" name="electronic-invoice-auto">
          <electronic-invoice-auto></electronic-invoice-auto>
        </el-tab-pane>
      </template>
      <template
        v-if="$hasPermission('invoiceOffline,invoiceOfflineZt')"
        desc="invoiceOffline:线下开票,invoiceOfflineZt:线下开票(专题)"
        actions="invoiceOffline:@ElectronicInvoiceOffline#invoiceOfflineZt:@ElectronicInvoiceOffline"
      >
        <el-tab-pane label="增值税电子普通发票（线下开票）" name="electronic-invoice-offline">
          <electronic-invoice-offline></electronic-invoice-offline>
        </el-tab-pane>
      </template>
      <template
        v-if="$hasPermission('electronicSpecialInvoice,electronicSpecialInvoiceZt')"
        desc="electronicSpecialInvoice:增值税电子专用发票(线下开票),electronicSpecialInvoiceZt:增值税电子专用发票(专题)"
        actions="electronicSpecialInvoice:@ElectronicSpecialInvoice#electronicSpecialInvoiceZt:@ElectronicSpecialInvoice"
      >
        <el-tab-pane label="增值税电子专用发票(线下开票)" name="electronic-special-invoice">
          <electronic-special-invoice></electronic-special-invoice>
        </el-tab-pane>
      </template>
      <template
        v-if="$hasPermission('specialInvoice,specialInvoiceZt')"
        desc="specialInvoice:专用发票,specialInvoiceZt:专用发票(专题)"
        actions="specialInvoice:@SpecialInvoice#specialInvoiceZt:@SpecialInvoice"
      >
        <el-tab-pane label="增值税专用发票(纸质票)" name="special-invoice">
          <special-invoice></special-invoice>
        </el-tab-pane>
      </template>
      <template
        v-if="$hasPermission('invoiceDistribution,invoiceDistributionZt')"
        desc="invoiceDistribution:发票配送,invoiceDistributionZt:发票配送(专题)"
        actions="invoiceDistribution:@InvoiceDistribution#invoiceDistributionZt:@InvoiceDistribution"
      >
        <el-tab-pane label="发票配送" name="invoice-distribution">
          <invoice-distribution ref="invoice-distribution"></invoice-distribution>
        </el-tab-pane>
      </template>
    </el-tabs>
  </el-main>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import ElectronicInvoiceAuto from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/electronic-invoice-auto.vue'
  import ElectronicInvoiceOffline from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/electronic-invoice-offline.vue'
  import SpecialInvoice from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/special-invoice.vue'
  import InvoiceDistribution from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/invoice-distribution.vue'
  import ElectronicSpecialInvoice from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/electronic-special-invoice.vue'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'

  @Component({
    components: {
      ElectronicInvoiceAuto,
      ElectronicInvoiceOffline,
      SpecialInvoice,
      InvoiceDistribution,
      ElectronicSpecialInvoice
    }
  })
  export default class extends Vue {
    @Ref('invoice-distribution') invoiceDistribution: InvoiceDistribution
    activeName = 'electronic-invoice-auto'
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    async handleClick() {
      if (this.activeName === 'invoice-distribution') {
        if (this.isZtlogin) {
          await this.invoiceDistribution.doQueryPageZt()
        } else {
          await this.invoiceDistribution.doQueryPage()
        }
      }
    }
  }
</script>
