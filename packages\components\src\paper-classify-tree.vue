<route-meta>
{
"title": "试卷分类选择器"
}
</route-meta>
<template>
  <PopTree
    desc="试卷管理-选择试卷分类"
    actions="load"
    placeholder="请选择试卷分类"
    :default-value="defaultNode"
    v-model="selectNode"
    :load-data="load"
    size="large"
    :multiple="false"
    ref="popTreeRef"
    :notOnlyId="notOnlyId"
    :close-after-select="true"
    :clearable="true"
  ></PopTree>
</template>
<script lang="ts">
  import { Component, Emit, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import PopTree from '@hbfe-vue-components/pop-tree'
  import { ExamPaperClassifyUI } from '@hbfe/jxjy-admin-components/src/models/ExamPaperClassifyUI'
  import PaperPublishCategoryResponseVo from '@api/service/management/resource/exam-paper-category/query/vo/PaperPublishCategoryResponseVo'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { Query, UiPage } from '@hbfe/common'

  class HasPaperPublishCategoryResponseVo extends PaperPublishCategoryResponseVo {
    hasChildren = true
    children? = new Array<HasPaperPublishCategoryResponseVo>()
  }
  @Component({
    components: {
      PopTree
    }
  })
  export default class PaperClassifyTree extends Vue {
    visible = false
    categoryName = ''
    currentCategoryId = ''
    selectNode = ''
    queryExamPaperCategory = ResourceModule.queryExamPaperCategoryFactory
    page: UiPage
    query: Query = new Query()
    paperPublishCategoryResponseVo = new Array<HasPaperPublishCategoryResponseVo>()
    @Prop({
      type: ExamPaperClassifyUI,
      default: () => {
        return new ExamPaperClassifyUI()
      }
    })
    defaultNode: ExamPaperClassifyUI
    @Prop({
      type: Boolean,
      default: false
    })
    notOnlyId: boolean
    @Ref('popTreeRef')
    popTreeRef: any
    @Prop() value: string

    @Prop({
      type: String,
      default: '-1'
    })
    unitId: string //机构id

    @Emit('input')
    @Watch('selectNode')
    selectNodeChange(val: any) {
      if (!val) {
        val = ''
      }
      this.$emit('valName', val.name)
      this.$emit('selectNode', val)

      console.log(this.selectNode, 'selectNodeselectNode')

      return val.id
    }

    // @Watch('value')
    // valueChange(val: string) {
    //   this.selectNode = val

    //   console.log(val, '试卷分类名称')

    //   if (!this.value) {
    //     this.clearChoose()
    //   }
    // }
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
      this.page.pageSize = 200
    }
    async created() {
      this.defaultNode
      // this.doQueryPage()
    }
    async doQueryPage() {
      this.paperPublishCategoryResponseVo = new Array<HasPaperPublishCategoryResponseVo>()
      const parentId = '-1'
      this.page.pageSize = 200
      const res = await this.queryExamPaperCategory.queryQueryExamCategory.queryExamPaperCategoryList(
        this.page,
        parentId
      )
      res.data?.forEach((p) => {
        const category = new HasPaperPublishCategoryResponseVo()
        category.parentCategoryId = p.parentCategoryId
        category.name = p.name
        category.id = p.id
        this.paperPublishCategoryResponseVo.push(category)
      })
    }
    handleNodeClick(data: any) {
      this.categoryName = data.name
      this.currentCategoryId = data.id
      this.selectNodeChange(this.currentCategoryId)
      this.visible = false
    }

    clearChoose() {
      this.popTreeRef.clear()
    }
    createRootCategory() {
      const result = new HasPaperPublishCategoryResponseVo()
      result.parentCategoryId = '-1'
      result.name = '试卷分类'
      result.id = '-1'
      return result
    }
    async load(node: any, resolve: any) {
      const categoryUIList = new Array<HasPaperPublishCategoryResponseVo>()
      if (node.data.id == undefined) {
        const rootCategory = this.createRootCategory()
        categoryUIList.push(rootCategory)
        resolve(categoryUIList)
        return
      }
      // if (node.data.parentCategoryId === '-1') {
      //   // 查询根节点
      //   1
      //
      //   this.requestRootPaperClassificationList(node, resolve)
      // } else {
      //   //查询子节点
      //   2
      //
      // this.requestSubPaperClassificationList(node, resolve)
      // }
      const parentId = node.data.id || '-1'
      const res = await this.queryExamPaperCategory.queryQueryExamCategory.queryExamPaperCategoryList(
        this.page,
        parentId
      )
      res.data?.forEach((p) => {
        const category = new HasPaperPublishCategoryResponseVo()
        category.parentCategoryId = p.parentCategoryId
        category.name = p.name
        category.id = p.id
        categoryUIList.push(category)
      })
      resolve(categoryUIList)
    }
    // 查询所有根节点
    async requestRootPaperClassificationList(node: any, resolve: (arr: any) => {}) {
      const categoryUIList = new Array<HasPaperPublishCategoryResponseVo>()
      const parentId = '-1'
      const res = await this.queryExamPaperCategory.queryQueryExamCategory.queryExamPaperCategoryList(
        this.page,
        parentId
      )
      res.data?.forEach((p) => {
        const category = new HasPaperPublishCategoryResponseVo()
        category.parentCategoryId = p.parentCategoryId
        category.name = p.name
        category.id = p.id
        categoryUIList.push(category)
      })
      resolve(categoryUIList)
    }
    // 查询子节点
    async requestSubPaperClassificationList(node: any, resolve: (arr: any) => {}) {
      const categoryUIList = new Array<HasPaperPublishCategoryResponseVo>()

      const res = await this.queryExamPaperCategory.queryQueryExamCategory.queryExamPaperCategoryList(
        this.page,
        node.id
      )
      res.data?.forEach((p) => {
        const category = new HasPaperPublishCategoryResponseVo()
        category.parentCategoryId = p.parentCategoryId
        category.name = p.name
        category.id = p.id
        categoryUIList.push(category)
      })
      resolve(categoryUIList)
    }
  }
</script>
