import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  CourseInfoRequest,
  CourseLearningSortRequest,
  CourseResponse,
  StudentCourseLearningRequest,
  StudentCourseLearningResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import { UiPage, Response, Page } from '@hbfe/common'
import QueryMyInterestCoursePageParam from '@api/service/customer/course/query/vo/QueryMyInterestCoursePageParam'
import MyInterestCourse from '@api/service/customer/course/query/vo/MyInterestCourse'
import QueryCourseList from './QueryCourseList'
import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
import TimeFormat from './vo/TimeFormat'

class QueryMyInterestCourse {
  private studentNo: string

  constructor(studentNo: string) {
    this.studentNo = studentNo
  }

  private async getCoursePageMap(idList: Array<string>): Promise<Map<string, CourseResponse>> {
    if (!idList.length) return
    const queryCourseList = new QueryCourseList()
    const resourceCourse = await queryCourseList.queryCoursePageByIdList(Array.from(idList))
    const resultMap = new Map<string, CourseResponse>()
    resourceCourse.forEach((course: CourseResponse) => {
      resultMap.set(course.id, course)
    })
    return resultMap
  }

  // * 通过学号课程ID获取评价ID
  async getEvaluateId(studentNo: string, courseId: string) {
    const params = new StudentCourseLearningRequest()
    params.course = new CourseInfoRequest()
    params.studentNo = studentNo
    params.course.courseId = courseId
    console.log('params', params)
    let result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfInterestCourseLearningSceneInMyself(
      {
        page: new Page(1, 1),
        request: params
      }
    )
    if (result.status.code !== 200 || !result.status.isSuccess()) {
      console.error('返回报错', result)
      return Promise.reject(result)
    }
    result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfInterestCourseLearningSceneInMyself(
      {
        page: new Page(1, result.data.totalSize),
        request: params
      }
    )
    if (result.status.code !== 200 || !result.status.isSuccess()) {
      console.error('返回报错', result)
      return Promise.reject(result)
    }
    return result.data.currentPageData.find(res => res.course.courseId === courseId)?.studentCourseAppraised
      ?.studentCourseAppraisalId
  }

  /**
   * 查询我的兴趣课课程列表
   * @param page
   * @param queryParams
   */
  async queryPage(
    page: UiPage,
    queryParams: QueryMyInterestCoursePageParam,
    sort?: Array<CourseLearningSortRequest>
  ): Promise<Response<Array<MyInterestCourse>>> {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageStudentCourseOfInterestCourseLearningSceneInMyself(
      {
        page,
        request: queryParams.to(),
        sort
      }
    )
    const response = new Response<Array<MyInterestCourse>>()
    if (!result?.status?.isSuccess()) {
      response.status = result.status
      return response
    }
    // 原课程 id 集合
    const sourceCourseIdList: Set<string> = new Set<string>()
    const resultList = result.data.currentPageData.map((response: StudentCourseLearningResponse) => {
      response.course.courseId && sourceCourseIdList.add(response.course.courseId)
      return MyInterestCourse.from(response)
    })

    /**
     * 返回课程信息
     */
    const courseMap = await this.getCoursePageMap(Array.from(sourceCourseIdList.values()))
    const teacherIdList: Set<string> = new Set<string>()
    resultList.forEach((learningCourse: MyInterestCourse) => {
      const responseCourse = courseMap.get(learningCourse.id)
      learningCourse.id = responseCourse.id
      learningCourse.name = responseCourse.name
      learningCourse.timeLength = responseCourse.courseTimeLength || 0
      learningCourse.timeFormat = new TimeFormat(learningCourse.timeLength)
      learningCourse.teachers = responseCourse.teacherIds.map(SimpleUserInfo.newSimpleUserInfoById)
      responseCourse.teacherIds.forEach((id: string) => {
        teacherIdList.add(id)
      })
    })

    // 加载教师信息
    if (teacherIdList.size) {
      const listTeacher = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.listTeacherInServicer(
        Array.from(teacherIdList.values())
      )
      resultList?.forEach((learningCourse: MyInterestCourse) => {
        learningCourse.teachers?.forEach(teacher => {
          listTeacher.data?.forEach(teacherInfo => {
            if (teacher.id === teacherInfo.id) {
              teacher.from(teacherInfo)
            }
          })
        })
      })
    }
    response.status = result.status
    response.data = resultList
    return response
  }
}

export default QueryMyInterestCourse
