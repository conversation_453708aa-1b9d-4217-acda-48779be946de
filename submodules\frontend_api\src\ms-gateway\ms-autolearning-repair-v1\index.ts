import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-autolearning-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-autolearning-repair-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量立刻执行学员的自动学习任务
   * @param qualificationIdList 学员资格ID列表，用于标识需要执行自动学习任务的学员
   * <AUTHOR> By Cb
   * @since 2024/8/17 17:15
   * @param mutate 查询 graphql 语法文档
   * @param qualificationIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchImmediatelyAutoLearning(
    qualificationIdList: Array<string>,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchImmediatelyAutoLearning,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { qualificationIdList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 立刻执行学员的自动学习任务
   * @param qualificationId:
   * <AUTHOR> By Cb
   * @since 2024/6/7 11:57
   * @param mutate 查询 graphql 语法文档
   * @param qualificationId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async immediatelyAutoLearning(
    qualificationId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.immediatelyAutoLearning,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { qualificationId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
