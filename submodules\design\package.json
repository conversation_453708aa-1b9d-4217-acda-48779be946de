{"name": "@jxjy/jxjy_admin_design", "version": "0.1.0", "scripts": {"collect:git-info": "node build/libs/git-info.js", "serve": "npm run collect:git-info && vue-cli-service serve", "build": "npm run collect:git-info && vue-cli-service build", "lint": "vue-cli-service lint --fix", "stylelint": "stylelint **/*.{scss,sass,css,less,vue,html}"}, "dependencies": {"@ant-design/colors": "^6.0.0", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "@hbfe-vue-components/split-pane": "^0.1.1", "@jiaminghi/data-view": "^2.8.0", "axios": "^0.19.2", "core-js": "^3.6.4", "cross-env": "^7.0.2", "deepmerge": "^4.2.2", "element-ui": "^2.15.7", "eslint-formatter-pretty": "^3.0.1", "express": "^5.1.0", "glob": "^7.1.6", "husky": "^4.2.5", "query-string": "^7.0.1", "resolve-url-loader": "^3.1.1", "sass": "^1.48.0", "simple-git": "~2.42.0", "style-resources-loader": "^1.5.0", "stylelint": "^13.3.2", "stylelint-config-standard": "^20.0.0", "stylelint-formatter-pretty": "^2.0.1", "stylelint-scss": "^3.17.1", "stylelint-webpack-plugin": "^1.2.3", "transliteration": "^2.1.8", "vue": "2.6.11", "vue-class-component": "^7.2.3", "vue-property-decorator": "^8.4.1", "vue-router": "^3.1.6", "vuex": "^3.1.3", "webpack-theme-color-replacer": "^1.3.26", "webpackbar": "^4.0.0"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^2.26.0", "@typescript-eslint/parser": "^2.26.0", "@vue/cli-plugin-babel": "~4.3.0", "@vue/cli-plugin-eslint": "^4.3.1", "@vue/cli-plugin-router": "~4.3.0", "@vue/cli-plugin-typescript": "~4.3.0", "@vue/cli-plugin-vuex": "~4.3.0", "@vue/cli-service": "~4.3.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^5.0.2", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-vue": "^6.2.2", "lint-staged": "^9.5.0", "prettier": "^1.19.1", "sass-loader": "^8.0.2", "typescript": "~3.8.3", "vue-template-compiler": "2.6.11"}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["cross-env NODE_ENV=development vue-cli-service lint", "git add"], "**/*.{scss,sass,css,less,vue,html}": "stylelint"}}