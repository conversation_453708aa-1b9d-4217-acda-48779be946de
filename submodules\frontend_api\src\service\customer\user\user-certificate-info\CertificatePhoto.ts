import Certificate from '@api/ms-gateway/ms-certificate-v1'
import UserInfo from '@api/service/customer/user/query/QueryUserInfo'
export default class CertificatePhoto {
  /**
   * 查询证书照片
   */
  async getCertificatePhoto() {
    const res = await Certificate.findUserIdentificationPhotoByUserId(UserInfo.userInfo.userInfo.userId)
    return res
  }

  /**
   * 上传证书照片
   */
  async uploadCertificatePhoto(photoUrl: string) {
    const res = await Certificate.uploadPhoto({
      userId: UserInfo.userInfo.userInfo.userId,
      photoUrl
    })
    return res.status
  }

  /**
   * 上传照片入口
   */
  async hasUploadPhotoEntry() {
    // 待确定接口
    const { status, data } = await Certificate.determineCertificateTemplateIsNeedPhoto()
    if (status.isSuccess()) {
      return data
    } else {
      return false
    }
  }
}
