/*
 * @Description: 创建资讯VO
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-09 19:01:15
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-06-01 15:48:36
 */
import { SpecialSubjectInfo } from '@api/ms-gateway/ms-news-v1'

export default class NewsDetailVo {
  /**
   * 发布时间
   */
  time: string
  /**
   * 资讯标题
   */
  title: string
  /**
   * 是否弹窗
   */
  isPopup = false
  /**
   * 是否置顶
   */
  top = false
  /**
   * 资讯类别
   */
  categoryType: Array<string>
  /**
   * 资讯来源
   */
  source?: string
  /**
   * 资讯摘要
   */
  abstract?: string
  /**
   * 封面图片
   */
  bgImage?: string
  /**
   * 资讯内容
   */
  content: string
  /**
   * 弹窗起始时间
   */
  popupBeginTime?: string
  /**
   * 弹窗截止时间
   */
  popupEndTime?: string
  /**
   * 发布地区编码
   */
  areaCodeList?: Array<string>

  /**
   * 专题信息
   */
  specialList: Array<SpecialSubjectInfo> = new Array<SpecialSubjectInfo>()

  /**
   * 是否校验时间重复
   */
  verifyPopUps = true
}
