<template>
  <el-drawer title="修改发票信息" :visible.sync="show" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <div class="m-tit">
        <span class="tit-txt">原发票信息</span>
      </div>
      <el-form ref="elForm" :model="InvoiceObject" label-width="150px" class="m-text-form is-column f-mt10">
        <el-form-item label="发票类型：" class="is-text">{{ InvoiceObject.type }}</el-form-item>
        <el-form-item label="发票抬头：" class="is-text">{{ InvoiceObject.name }}</el-form-item>
      </el-form>
      <el-divider class="m-divider"></el-divider>
      <div class="m-tit">
        <span class="tit-txt">修改的发票信息</span>
      </div>

      <el-form ref="form" :model="InvoiceObject" :rules="rules" label-width="150px" class="m-form f-mt10">
        <el-form-item label="发票类型：" class="is-text">{{ InvoiceObject.type }}</el-form-item>
        <el-form-item label="发票抬头：" prop="resourceName">
          <el-radio-group v-model="InvoiceObject.resource">
            <el-radio label="1">个人</el-radio>
            <el-radio label="2">单位</el-radio>
          </el-radio-group>
          <div class="f-mt10">
            <!--选择单位时改为 请输入单位名称-->
            <el-input v-model="InvoiceObject.resourceName" clearable placeholder="请输入个人名字" class="form-l" />
          </div>
        </el-form-item>

        <el-form-item label="统一社会信用代码：" prop="code">
          <el-input clearable v-model="InvoiceObject.code" placeholder="请输入18位统一社会信用代码" class="form-l" />
        </el-form-item>
        <el-form-item label=" " class="is-text">
          <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
        </el-form-item>
        <el-form-item label="手机号：" prop="phone" v-if="invoiceType == 1">
          <el-input clearable v-model="InvoiceObject.phone" placeholder="请输入11位手机号码" class="form-l" />
        </el-form-item>
        <el-form-item label="电子邮箱：" prop="email" v-if="invoiceType == 1">
          <el-input clearable v-model="InvoiceObject.email" placeholder="请输入电子邮箱" class="form-l" />
        </el-form-item>
        <div class="f-pt5 f-pb10">
          <el-divider content-position="left">
            <span class="f-cr">* 以下内容请根据需要填写，请全部填写或者全部不填写。</span>
          </el-divider>
        </div>
        <el-form-item label="开户银行：">
          <el-input clearable placeholder="请输入开户银行" v-model="InvoiceObject.bank" class="form-l" />
        </el-form-item>
        <el-form-item label="开户帐号：">
          <el-input clearable placeholder="请输入开户帐号" v-model="InvoiceObject.account" class="form-l" />
        </el-form-item>
        <el-form-item label="注册电话：">
          <el-input clearable placeholder="请输入单位注册电话" v-model="InvoiceObject.phone1" class="form-m" />
        </el-form-item>
        <el-form-item label="注册地址：">
          <el-input clearable placeholder="请输入单位注册地址" v-model="InvoiceObject.address" />
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="close()">取消</el-button>
      <el-button type="primary" @click="save()">保存发票信息</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Ref } from 'vue-property-decorator'
  import { ElForm } from 'element-ui/types/form'

  import TradeModule from '@api/service/management/trade/TradeModule'

  @Component
  export default class extends Vue {
    @Ref('elForm') elForm: ElForm

    @Prop({
      required: false
    })
    invoiceId: string
    /**
     * 外部传过来打开关闭
     */
    @Prop({
      required: true
    })
    visible: boolean

    @Prop({
      required: true
    })
    invoiceType: number //发票类型   自动 0 --- 线下 1

    InvoiceObject = {
      type: '增值税电子普通发票111',
      name: '【个人】林林一',
      resource: '1',
      resourceName: '',
      code: '',
      phone: '',
      email: '',
      bank: '',
      account: '',
      phone1: '',
      address: ''
    }
    rules = {
      resourceName: [
        {
          required: true,
          message: '请输入抬头',
          trigger: 'blur'
        }
      ],
      code: [
        {
          required: true,
          message: '请输入统一社会信用代码'
        },
        {
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            // this.heckCode(rule, value, callback)
            callback()
          },
          trigger: 'blur'
        }
      ],
      phone: [
        {
          required: true,
          min: 11,
          message: '请输入11位手机号码',
          trigger: 'blur'
        },
        {
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            if (/^1[3456789]\d{9}$/.test(value)) {
              callback()
            } else {
              callback(new Error('手机格式错误'))
            }
          }
        }
      ],
      email: [
        {
          required: true,
          message: '请输入电子邮箱'
        },
        {
          validator: (rule: any, value: any, callback: (error?: Error) => void) => {
            if (/^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(value)) {
              callback()
            } else {
              callback(new Error('电子邮箱错误'))
            }
          }
        }
      ]
    }
    //统一社会信用代码校验
    heckCode(rule: any, value: number, callback: (error?: Error) => void) {
      {
        if (!Number.isInteger(value)) {
          callback(new Error('请输入数字值'))
        } else {
          callback()
        }
      }
    }
    get show() {
      //该抽屉的显隐
      return this.visible
    }
    set show(val: boolean) {
      this.$emit('update:visible', val)
    }
    close() {
      this.show = false
    }
    validOpeningForm() {
      if (
        this.InvoiceObject.bank ||
        this.InvoiceObject.account ||
        this.InvoiceObject.phone1 ||
        this.InvoiceObject.address
      ) {
        if (!this.InvoiceObject.bank) {
          this.$message.warning('开户银行不能为空。')
          return false
        }
        if (!this.InvoiceObject.account) {
          this.$message.warning('开户帐号不能为空。')
          return false
        }
        if (!this.InvoiceObject.phone1) {
          this.$message.warning('注册电话不能为空。')
          return false
        }
        if (!this.InvoiceObject.address) {
          this.$message.warning('注册地址不能为空。')
          return false
        }
      }
    }
    save() {
      const success = this.validOpeningForm()
      if (!success) {
        return
      }

      this.elForm.validate((val) => {
        if (val) {
          if (this.invoiceType == 0) {
            // this.$emit()
          } else {
            //this.$emit()
          }
        }
      })
      this.show = false
    }
    async getDetails(id: string) {
      // const res = await TradeModule.SingleTradeBatchFactor.InvoiceFactor.queryInvoice.getInvoiceInServicer(id)
      // res
    }
    async created() {
      await this.getDetails(this.invoiceId)
    }
  }
</script>
