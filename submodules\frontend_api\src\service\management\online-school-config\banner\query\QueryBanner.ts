import MsBasicDataQueryBackstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import BannerVo from '@api/service/common/online-school-config/vo/BannerVo'

class QueryBanner {
  /**
   * web轮播图
   */
  WebBannerList = new Array<BannerVo>()
  /**
   * H5轮播图 */
  h5BannerList = new Array<BannerVo>()

  /**
   * 查询web端轮播图列表
   * @returns
   */
  async queryWebBannerList() {
    const res = await MsBasicDataQueryBackstage.getBannerListByPortalType(1)
    if (res.status.isSuccess()) {
      this.WebBannerList = res.data?.bannerInfos?.map(BannerVo.from).sort((a, b) => {
        return a.sort - b.sort
      })
    }

    return res.status
  }

  /**
   * 查询h5轮播图列表
   * @returns
   */
  async queryH5BannerList() {
    const res = await MsBasicDataQueryBackstage.getBannerListByPortalType(2)
    if (res.status.isSuccess()) {
      this.h5BannerList = res.data?.bannerInfos?.map(BannerVo.from).sort((a, b) => {
        return a.sort - b.sort
      })
    }
    return res.status
  }
}
export default new QueryBanner()
