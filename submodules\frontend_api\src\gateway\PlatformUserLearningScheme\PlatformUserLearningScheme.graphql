schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取用户已预约期数信息
		todo 单位id（字段暂时没有调整为培训机构服务商id，待方案微服务化后再做调整）:客服咨询-方案列表-方案详情
		@param userId
		@param issueId
		@return
	"""
	getUserIssueReservation(userId:String,issueId:String):UserIssueDetailDTO
	"""批量获取用户购买的学习方案信息"""
	getUserSchemeByUserIds(userIds:[String]):[UserSchemeSimpleDTO]
	"""用户期数预约查询
		todo 单位id（字段暂时没有调整为培训机构服务商id，待方案微服务化后再做调整）:学员端-方案列表
		@param paramDTO
	"""
	listUserIssueReservation(paramDTO:UserIssueReservationParamDTO):[UserIssueDTO]
	"""用户期数分页查询 -数据来源清洗表1
		@param page
		@param request
		@return
	"""
	pageUserIssueReservation(page:Page,request:UserIssueReservationParamDTO):UserIssueSimpleResponsePage @page(for:"UserIssueSimpleResponse")
	"""用户期数预约查询分页实时
		todo 单位id（字段暂时没有调整为培训机构服务商id，待方案微服务化后再做调整）:学员端-方案列表
		@param page
		@param paramDTO
	"""
	pageUserIssueReservationActual(page:Page,paramDTO:UserIssueReservationParamDTO):UserIssueDTOPage @page(for:"UserIssueDTO")
	"""用户学习时长统计 - - 中间表(已实现)
		@return
	"""
	statisticUserSchemeLearningTime:UserSchemeLearningTimeDTO
	"""用户期数校验
		200: 校验通过
		201:期数已过期
		202:期数已失效
		203:期数退款冻结
		204:期数换货冻结 - 预留code
		@param paramDto
		@return
	"""
	validUserIssue(paramDto:UserIssueValidParam):GraphqlOperateResult
}
"""用户期数预约查询
	@author: eleven
	@date: 2020/3/13
"""
input UserIssueReservationParamDTO @type(value:"com.fjhb.btpx.integrative.service.learningscheme.dto.param.UserIssueReservationParamDTO") {
	"""查询的用户id - 运营与参数，学员端的忽略该参数"""
	userId:String
	"""是否合格"""
	qualified:Boolean
	"""期数ID集合"""
	issueIds:[String]
	"""方案名字模糊查询"""
	schemeName:String
	"""培训工种类别/培训工种id path
		用于培训类别联合工种多条件查询
	"""
	workTypeIdPathList:[String]
	"""期数是否过期"""
	isIssueFinish:Boolean
	"""期数是否过期查询策略，默认and"""
	isIssueFinishQueryPolicy:QueryParamOperatePolicyEnum
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""用户期数校验参数
	@author: eleven
	@date: 2020/3/30
"""
input UserIssueValidParam @type(value:"com.fjhb.btpx.integrative.service.learningscheme.dto.param.UserIssueValidParam") {
	"""购买的用户"""
	userId:String
	"""校验的期数id"""
	issueId:String
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""graphql没有泛型的操作结果类
	<AUTHOR> create 2020/3/9 15:18
"""
type GraphqlOperateResult @type(value:"com.fjhb.btpx.integrative.gateway.graphql.dto.GraphqlOperateResult") {
	"""返回的code"""
	code:String
	"""返回的message"""
	message:String
	"""json字段
		存放返回的参数用
	"""
	expandData:Map
}
"""@author: eleven
	@date: 2020/6/5
"""
type IssueClassLSAchieveSettingResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.IssueClassLSAchieveSettingResponse") {
	enabledAssess:Boolean!
	grade:Double
	templateId:String
}
"""课程考核信息
	@author: eleven
	@date: 2020/6/5
"""
type UserCourseLearningAssessResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.UserCourseLearningAssessResponse") {
	"""用户学习进度"""
	userSchedule:Double!
	"""已选学时"""
	hasSelectPeriod:Double!
	"""已经学习的学时"""
	hasStudyPeriod:Double!
	allSelectedComplete:Boolean!
	schedule:Double!
}
"""@author: eleven
	@date: 2020/6/5
"""
type UserExamAssessResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.UserExamAssessResponse") {
	"""用户考试最高成绩"""
	userHigestScore:Double
	"""当前考核状态，-1未考核，0考核不通过，1考核通过
		备注：该值可能被考核计算策略影响
	"""
	assessStatus:Int!
	"""考核通过时间"""
	passedTime:DateTime
	score:Double!
}
"""试题练习
	@author: eleven
	@date: 2020/3/24
"""
type QuestionPracticeDTO @type(value:"com.fjhb.btpx.integrative.service.learningscheme.dto.response.QuestionPracticeDTO") {
	practiceId:String
	fetchWay:Int!
	libraryWaySetting:LibraryWaySetting
	tagsWaySetting:TagWaySetting
}
"""@author: eleven
	@date: 2020/3/24
"""
type UserCourseLearningDTO @type(value:"com.fjhb.btpx.integrative.service.learningscheme.dto.response.UserCourseLearningDTO") {
	"""学习方式ID"""
	learningId:String
	"""选课规则ID"""
	ruleConfigId:String
	"""学习方案要求的最少学时"""
	minTotalPeriod:Double!
	"""课程学习考核，null表示不设置考核"""
	userCourseLearningAssess:UserCourseLearningAssessResponse
}
"""@author: eleven
	@date: 2020/3/24
"""
type UserExamLearningDTO @type(value:"com.fjhb.btpx.integrative.service.learningscheme.dto.response.UserExamLearningDTO") {
	"""学习方式ID"""
	learningId:String
	"""场次ID"""
	examRoundId:String
	"""试卷ID"""
	examPaperId:String
	"""开考时间"""
	beginTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""考试次数 0表示不限制次数"""
	examCount:Int!
	"""考试时长"""
	examTimeLength:Int!
	"""场次名称"""
	name:String
	"""考试考核，null表示不设置考核"""
	userExamAssess:UserExamAssessResponse
}
"""用户期数预约信息
	@author: eleven
	@date: 2020/3/13
"""
type UserIssueDTO @type(value:"com.fjhb.btpx.integrative.service.learningscheme.dto.response.UserIssueDTO") {
	"""方案所属单位id"""
	schemeUnitId:String
	"""学习方案id"""
	schemeId:String
	"""方案名称"""
	scheme:String
	"""方案封面图片地址"""
	picture:String
	"""期数id"""
	issueId:String
	"""期数标题"""
	issueTitle:String
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""期数预约创建时间"""
	createTime:DateTime
	"""课程学习方式"""
	courseLearning:UserCourseLearningDTO
	"""考试学习方式"""
	examLearning:UserExamLearningDTO
	"""练习学习方式"""
	practiceLearning:UserPracticeLearningDTO
	"""培训班成果设置"""
	achieveSetting:IssueClassLSAchieveSettingResponse
	"""兴趣课程包配置"""
	interestCourseSetting:InterestCourseSetting
	"""当前考核状态，-1未考核，0考核不通过，1考核通过
		备注：该值可能被考核计算策略影响
	"""
	assessStatus:Int!
	"""考核通过时间"""
	passedTime:DateTime
	"""来源渠道"""
	channel:PaymentChannelTypeEnum
	"""是否开放打印证明"""
	isOpenPrintCertificate:Boolean
	"""适用人群"""
	suitablePeople:[String]
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""用户期数预约信息
	@author: eleven
	@date: 2020/3/13
"""
type UserIssueDetailDTO @type(value:"com.fjhb.btpx.integrative.service.learningscheme.dto.response.UserIssueDetailDTO") {
	"""课程供应商id"""
	coursewareSupplierId:String
	"""课程供应商名称"""
	coursewareSupplierName:String
	"""方案所属单位id"""
	schemeUnitId:String
	"""学习方案id"""
	schemeId:String
	"""方案名称"""
	scheme:String
	"""方案封面图片地址"""
	picture:String
	"""期数id"""
	issueId:String
	"""期数标题"""
	issueTitle:String
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""期数预约创建时间"""
	createTime:DateTime
	"""课程学习方式"""
	courseLearning:UserCourseLearningDTO
	"""考试学习方式"""
	examLearning:UserExamLearningDTO
	"""练习学习方式"""
	practiceLearning:UserPracticeLearningDTO
	"""培训班成果设置"""
	achieveSetting:IssueClassLSAchieveSettingResponse
	"""兴趣课程包配置"""
	interestCourseSetting:InterestCourseSetting
	"""当前考核状态，-1未考核，0考核不通过，1考核通过
		备注：该值可能被考核计算策略影响
	"""
	assessStatus:Int!
	"""考核通过时间"""
	passedTime:DateTime
	"""来源渠道"""
	channel:PaymentChannelTypeEnum
	"""是否开放打印证明"""
	isOpenPrintCertificate:Boolean
	"""适用人群"""
	suitablePeople:[String]
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""<AUTHOR>
	@date 2020/8/22
	@description
"""
type UserIssueSimpleResponse @type(value:"com.fjhb.btpx.integrative.service.learningscheme.dto.response.UserIssueSimpleResponse") {
	"""用户id"""
	userId:String
	"""学习方案id"""
	schemeId:String
	"""方案名称"""
	scheme:String
	"""期数id"""
	issueId:String
	"""期数标题"""
	issueTitle:String
	"""机构Id"""
	trainingInstitutionId:String
	"""机构名称"""
	trainingInstitutionName:String
	"""期数预约创建时间"""
	createTime:String
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""方案整体是否合格"""
	qualified:Boolean!
	"""物品状态
		@see UserGoodState#getValue()
	"""
	goodState:String
	"""是否有课程学习方式"""
	hasCourseLearning:Boolean!
	"""课程总体学习进度"""
	courseSchedule:Double!
	"""来源渠道"""
	channel:PaymentChannelTypeEnum
	"""适用人群"""
	suitablePeople:[String]
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""@author: eleven
	@date: 2020/3/24
"""
type UserPracticeLearningDTO @type(value:"com.fjhb.btpx.integrative.service.learningscheme.dto.response.UserPracticeLearningDTO") {
	"""学习方式"""
	learningId:String
	"""试题练习"""
	questionPractice:QuestionPracticeDTO
}
"""用户学习时长统计
	@author: eleven
	@date: 2020/3/5
"""
type UserSchemeLearningTimeDTO @type(value:"com.fjhb.btpx.integrative.service.learningscheme.dto.response.UserSchemeLearningTimeDTO") {
	"""学习时长 单位：s
		前端自己换算成  h 或 min
	"""
	learningTimeLength:Double!
	"""学习时长排名"""
	learningTimeRank:Long!
	"""学习时长参与排名人数"""
	totalUserCount:Long!
}
type UserSchemeSimpleDTO @type(value:"com.fjhb.btpx.integrative.service.learningscheme.dto.response.UserSchemeSimpleDTO") {
	"""学习方案ID"""
	schemeId:String
	"""培训班名称"""
	name:String
	"""培训班类别ID"""
	categoryId:String
	"""年度"""
	year:Int!
	"""培训班状态，1表示已发布、2表示配置中、3表示未发布"""
	status:Int!
	"""所属地区 格式：中国/福建省/福州市"""
	region:String
	"""培训班封面图片地址"""
	picture:String
	"""用户ID"""
	userId:String
	"""来源类型"""
	sourceType:String
	"""来源ID"""
	sourceId:String
	"""创建方式 1:系统创建 2:用户创建 3:管理员创建 4:历史迁移 5:外部接口"""
	createType:Int!
	"""用户在培训班学习方案的状态，0有效，1冻结，2失效"""
	userState:Int!
	"""培训结果，-1未进行考核，0考核不通过，1考核通过"""
	trainingResult:Int!
	"""培训开始时间"""
	trainingBeginDate:DateTime
	"""培训结束时间"""
	trainingEndDate:DateTime
	"""培训班完成时间"""
	completedTime:DateTime
	"""用户报名时间"""
	registerTime:DateTime
	"""是否开放打印证明"""
	openPrintCertificate:Boolean!
	"""培训班学习方案平台自定义类型ID"""
	lSCustomerTypeId:String
}
"""Author:FangKunSen
	Time:2021-02-02,15:57
"""
enum QueryParamOperatePolicyEnum @type(value:"com.fjhb.btpx.integrative.service.utils.dto.QueryParamOperatePolicyEnum") {
	"""and查询"""
	AND
	"""or查询"""
	OR
}
"""<AUTHOR>
	@date 2020/8/12
	@description
"""
type InterestCourseSetting @type(value:"com.fjhb.btpx.learningscheme.gateway.graphql.resolver.request.InterestCourseSetting") {
	"""兴趣课程包集合
		前端传递的参数，服务内部转为 interestPoolIdList
	"""
	poolList:[String]
}
"""缴费渠道类型
	<AUTHOR>
"""
enum PaymentChannelTypeEnum @type(value:"com.fjhb.btpx.platform.service.paymentchannel.dto.PaymentChannelTypeEnum") {
	"""web端"""
	WEB
	"""android客户端"""
	ANDROID
	"""ios客户端"""
	IOS
	"""微信公众号（订阅号）"""
	WECHAT_OFFICIAL_ACCOUNTS
	"""微信小程序"""
	WECHAT_MINI_PROGRAMS
	"""管理员现场开通"""
	PRESENT
	"""管理员集体缴费"""
	COLLECTIVE
	"""H5"""
	HTML5
	"""钉钉"""
	DINGDING
	"""渠道开通"""
	CHANNEL_PRESENT_OPEN
	"""外部管理系统"""
	EXTERNAL_SYSTEM_MANAGE
}
type LibraryWaySetting @type(value:"com.fjhb.platform.component.learningscheme.practicelearning.lib.domain.questionlib.entities.LibraryWaySetting") {
	libraryIds:[String]
	recursive:Boolean!
}
type TagWaySetting @type(value:"com.fjhb.platform.component.learningscheme.practicelearning.lib.domain.questionlib.entities.TagWaySetting") {
	tagIds:[String]
}

scalar List
type UserIssueSimpleResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserIssueSimpleResponse]}
type UserIssueDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserIssueDTO]}
