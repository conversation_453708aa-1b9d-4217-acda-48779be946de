import { SectionAndSubjects } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import UserIndustryJsInfo from './UserIndustryJsInfo'
import UserIndustryRsInfo from './UserIndustryRsInfo'
import { UpdateStudentSystemRequest, CreateUserIndustryRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { IdentityCardType } from '@api/service/management/online-school-config/functionality-setting/enum/IdentityCardTypeEnum'

/*
 *修改学员信息
 */
class UpdateStudentRequestVo {
  /**
   * 账户id
   */
  accountId: string = undefined
  /**
   * 用户id
   */
  userId: string = undefined
  /**
   * 【必填】证件号码
   */
  idCard?: string = undefined
  /**
   * 【必填】证件类别
   */
  idCardType: number = undefined
  /**
   * 性别
   */
  gender: number = undefined
  /**
   * 用户名称
   */
  name: string = undefined
  /**
   * 手机号
   */
  phone: string = undefined
  /**
   * 所属区域
   */
  area: string = undefined
  /**
   * 工作单位
   */
  companyName?: string = undefined
  /**
   * 统一社会信用码
   */
  companyCode?: string = undefined
  /**
   * [必填]加密值
   */
  encrypt?: string = undefined
  /**
   * 人社行业信息
   */
  rsUserIndustryInfo = new UserIndustryRsInfo()
  /**
   * 建设行业信息
   */
  jsUserIndustryInfo = new UserIndustryJsInfo()
  /**
   * 卫生行业信息
   */
  wsUserIndustryInfo = new UserIndustryRsInfo()
  /**
   * 工勤行业信息
   */
  gqUserIndustryInfo = new UserIndustryRsInfo()
  /**
   * 教师行业信息
   */
  lsUserIndustryInfo = new UserIndustryRsInfo()
  /**
   * 药师行业信息
   */
  ysUserIndustryInfo = new UserIndustryRsInfo()
  /*
   * 单位地区code
   * */
  companyRegionCode: string = undefined

  toDto() {
    const params = new UpdateStudentSystemRequest()
    params.userId = this.userId
    params.idCard = this.idCard
    params.gender = this.gender
    params.name = this.name
    params.phone = this.phone
    params.area = this.companyRegionCode
    params.companyName = this.companyName
    params.companyCode = this.companyCode
    params.companyRegionCode = this.companyRegionCode
    params.encrypt = this.encrypt
    params.idCardType = this.idCardType
    params.userIndustryInfos = new Array<CreateUserIndustryRequest>()
    if (this.rsUserIndustryInfo?.industryId) {
      const temp = new CreateUserIndustryRequest()
      temp.industryId = this.rsUserIndustryInfo?.industryId
      temp.firstProfessionalCategory = this.rsUserIndustryInfo?.firstProfessionalCategory
      temp.secondProfessionalCategory = this.rsUserIndustryInfo?.secondProfessionalCategory
      temp.professionalQualification = this.rsUserIndustryInfo?.professionalQualification
      params.userIndustryInfos.push(temp)
    }
    if (this.jsUserIndustryInfo?.industryId) {
      const item = new CreateUserIndustryRequest()
      item.industryId = this.jsUserIndustryInfo?.industryId
      if (this.jsUserIndustryInfo?.certificateInfos?.length) {
        item.certificateInfos = this.jsUserIndustryInfo?.certificateInfos
      } else {
        item.certificateInfos = []
      }
      params.userIndustryInfos.push(item)
    }
    if (this.wsUserIndustryInfo?.industryId) {
      const wsIndustryInfo = new CreateUserIndustryRequest()
      wsIndustryInfo.industryId = this.wsUserIndustryInfo.industryId
      wsIndustryInfo.personnelCategory = this.wsUserIndustryInfo.personnelCategory
      wsIndustryInfo.positionCategory = this.wsUserIndustryInfo.positionCategory
      params.userIndustryInfos.push(wsIndustryInfo)
    }
    if (this.gqUserIndustryInfo?.industryId) {
      const gqIndustryInfo = new CreateUserIndustryRequest()
      gqIndustryInfo.industryId = this.gqUserIndustryInfo.industryId
      gqIndustryInfo.professionalLevel = this.gqUserIndustryInfo.professionalLevel
      gqIndustryInfo.jobCategoryId = this.gqUserIndustryInfo.jobCategoryId
      params.userIndustryInfos.push(gqIndustryInfo)
    }
    if (this.lsUserIndustryInfo?.industryId) {
      const lsIndustryInfo = new CreateUserIndustryRequest()
      lsIndustryInfo.industryId = this.lsUserIndustryInfo.industryId
      lsIndustryInfo.sectionAndSubjects = new Array<SectionAndSubjects>()
      lsIndustryInfo.sectionAndSubjects.push({
        section: this.lsUserIndustryInfo.grade,
        subjects: this.lsUserIndustryInfo.subject
      })
      params.userIndustryInfos.push(lsIndustryInfo)
    }
    if (this.ysUserIndustryInfo?.industryId) {
      const ysIndustryInfo = new CreateUserIndustryRequest()
      ysIndustryInfo.industryId = this.ysUserIndustryInfo.industryId
      ysIndustryInfo.certificatesType = this.ysUserIndustryInfo.certificatesType
      ysIndustryInfo.practitionerCategory = this.ysUserIndustryInfo.practitionerCategory

      params.userIndustryInfos.push(ysIndustryInfo)
    }
    return params
  }
}

export default UpdateStudentRequestVo
