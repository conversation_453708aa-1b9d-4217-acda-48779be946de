import { ProviderStatusEnum } from '@api/service/management/authority/service-provider/mutation/vo/ProviderStatusEnum'
import { ServicerAdminInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

class CoursewareSupplierListDetail {
  /**
   * 账户id
   */
  id = ''
  /**
   * 课件供应商名称
   */
  name = ''
  /**
   * 账号
   */
  account = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 创建时间
   */
  createTime = ''
  /**
   * 状态 0:停用 1：正常
   */
  status: ProviderStatusEnum = null
  /**
   * 课件供应商ID
   */
  coursewareId = ''
  /**
   * 用户ID
   */
  userId = ''

  static from(adminInfo: ServicerAdminInfoResponse[]) {
    return adminInfo.map(res => {
      const vo = new CoursewareSupplierListDetail()
      const servicerList =
        res?.businessOwnerInfo?.servicerList?.length &&
        res?.businessOwnerInfo?.servicerList?.filter(item => item.servicerType === 560)
      // const curProvider = providerInfo.find(item => item.servicerBase.servicerId === servicerList[0].servicerId)
      vo.coursewareId = servicerList && servicerList[0]?.servicerId
      vo.userId = res?.userInfo?.userId
      vo.id = res?.accountInfo?.accountId
      vo.name = res?.userInfo?.userName || ''
      vo.account =
        (res?.authenticationList?.length && res?.authenticationList?.find(item => item.identityType === 1)?.identity) ||
        ''
      vo.phone = res?.userInfo?.phone
      vo.createTime = res?.accountInfo?.createdTime
      if (res?.accountInfo?.status === 1) {
        vo.status = ProviderStatusEnum.enable
      }
      if ([2, 3].includes(res?.accountInfo?.status)) {
        vo.status = ProviderStatusEnum.disable
      }
      return vo
    })
  }
}

export default CoursewareSupplierListDetail
