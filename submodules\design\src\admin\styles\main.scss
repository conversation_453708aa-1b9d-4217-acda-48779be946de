@import '../../common/variables';

.u-qr-code {
  width: 160px;
  height: 160px;
}

.f-vm {
  display: inline-block;
  vertical-align: middle;
}

.f-dot {
  width: 6px;
  height: 6px;
  border-radius: 100%;
  background-color: $base;
  display: inline-block;
  vertical-align: 2px;

  &.gray {
    background-color: #ddd;
  }

  &.red {
    background-color: $important;
  }
}

.f-input-num {
  width: 80px;

  .el-input__inner {
    text-align: center;
  }
}

.f-mh100 {
  min-height: 100px;
}
// 2行省略号
.f-to-two {
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
//文字颜色
.f-c4 {
  color: #444;
}
//卡片
.m-card {
  overflow: inherit;

  &.is-header {
    .el-card__body {
      padding: 0;
    }
  }

  &.is-header-sticky {
    .el-card__header {
      position: sticky;
      top: 0;
      z-index: 9;
      background-color: #fff;
      border-radius: 4px 4px 0 0;
    }

    .el-card__body {
      padding: 0;
    }
  }

  &.is-bg {
    border: none;

    .el-card__body {
      padding: 0;
    }
  }

  &.is-overflow-hidden {
    overflow: hidden;
  }

  .el-card__header {
    padding: 14px 20px;

    .tit-txt {
      font-size: 16px;
      font-weight: bold;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 100%;
        background-color: $base;
        margin-right: 8px;
      }
    }
  }

  .sub-tit {
    font-size: 15px;
    font-weight: bold;
    display: flex;
    align-items: center;
    border-bottom: 1px dashed #eee;
    padding: 12px 22px;

    &::before {
      content: '';
      width: 6px;
      height: 6px;
      border-radius: 100%;
      background-color: #bbb;
      margin-right: 10px;
    }
  }
}

//可收缩卡片
.m-collapse-item {
  .el-collapse-item__header {
    font-size: 16px;
    font-weight: bold;
  }

  .el-collapse-item__content {
    border-top: 1px solid #e4e7ed;
  }
}

//标题
.m-tit {
  padding-top: 14px;
  padding-bottom: 14px;
  display: flex;

  &.is-border-bottom {
    padding-left: 20px;
    padding-right: 20px;
    border-bottom: 1px solid #eee;
  }

  &.is-small {
    .tit-txt {
      font-size: 15px;
    }
  }

  &.is-mini {
    padding-top: 0;
    padding-bottom: 0;

    .tit-txt {
      font-size: 15px;
    }
  }

  &.bg-gray {
    background-color: #f8f8f8;
  }

  .tit-txt {
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      width: 8px;
      height: 8px;
      border-radius: 100%;
      background-color: $base;
      margin-right: 8px;
    }
  }

  .ipt {
    width: 200px;
  }
}

//二级标题
.m-sub-tit {
  display: flex;
  align-items: center;
  padding-top: 12px;
  padding-bottom: 12px;

  &.is-border-bottom {
    padding-left: 22px;
    padding-right: 22px;
    border-bottom: 1px dashed #eee;
  }

  &.bg-gray {
    background-color: #f8f8f8;
  }

  .tit-txt {
    font-size: 15px;
    font-weight: bold;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      width: 6px;
      height: 6px;
      border-radius: 100%;
      background-color: #bbb;
      margin-right: 10px;
    }
  }
}

//顶部tab标签
.m-tab-top {
  &.is-sticky {
    .el-tabs__header {
      position: sticky;
      top: 0;
      z-index: 9;
    }

    .el-tabs {
      .el-tabs__header {
        position: relative;
        z-index: 8;
      }
    }
  }

  &.is-border-top {
    border-top: 1px solid #f2f2f2;
  }

  .el-tabs__header {
    background-color: #fff;
    margin-bottom: 0;
  }

  .el-tabs__nav {
    padding-left: 30px;
  }

  .el-tabs__item {
    height: 60px;
    line-height: 60px;
    font-size: 15px;

    &.is-active {
      font-weight: bold;
    }
  }

  .el-tabs__active-bar {
    padding: 0 15px;
    left: 15px;
  }

  .el-tabs {
    .el-tabs__header {
      background-color: transparent;
      margin-bottom: 20px;
    }

    .el-tabs__nav {
      padding-left: inherit;
    }

    .el-tabs__item {
      height: 40px;
      line-height: 40px;
      font-size: 14px;

      &.is-active {
        font-weight: inherit;
      }
    }
  }
}

//自定tab
.m-tab-custom {
  .el-tabs__header {
    background-color: #fff;
    margin-bottom: 0;
  }

  .el-tabs__nav {
    padding-left: 15px !important;
  }

  .el-tabs__item {
    height: 60px;
    line-height: 60px;
    font-size: 15px;

    &.is-active {
      font-weight: bold;
    }
  }

  .el-tabs__active-bar {
    padding: 0 15px;
    left: 0 !important;
  }
}

//卡片标签
.m-tab-card {
  position: relative;

  &.el-tabs {
    .el-tabs__header {
      margin-bottom: 0;
      position: relative;
      top: 1px;

      .el-tabs__nav {
        overflow: hidden;
      }

      .el-tabs__item {
        background-color: #f7f8f9;
        border-bottom: 1px solid #e4e7ed;
        padding: 0 30px !important;
        height: 44px;
        line-height: 44px;

        &.is-active {
          background-color: #fff;
          border-bottom-color: #fff;
          font-weight: bold;
        }
      }
    }
  }

  .tab-tips {
    position: absolute;
    z-index: 9;
    left: 690px;
    top: -30px;
  }

  .tab-tips-2 {
    position: absolute;
    z-index: 9;
    left: 420px;
    top: -1px;
  }

  .tab-tips-3 {
    position: absolute;
    z-index: 1;
    left: 420px;
    top: -1px;
  }

  &.is-margin-bottom {
    .el-tabs__header {
      margin-bottom: 20px;
    }
  }

  &.is-sticky {
    .el-tabs__header {
      position: sticky;
      top: 0;
      z-index: 9;
      margin-bottom: -1px;
      background-color: #f0f2f5;
      padding-top: 15px;
    }

    .fixed-btn {
      position: sticky;
      top: 60px;
      z-index: 9;
      background-color: rgba(#fff, 0.95);
      border-bottom: 1px solid #eee;
      margin-bottom: -1px;
    }
  }

  &.is-badge {
    .el-tabs__header {
      .el-tabs__nav-wrap {
        overflow: initial;

        .el-tabs__nav {
          overflow: initial;
        }

        .el-tabs__item {
          position: relative;

          .el-badge {
            position: absolute;
            top: -7px;
            right: -19px;
            z-index: 100;
          }
        }
      }
    }
  }

  .btn-log {
    position: absolute;
    top: -40px;
    right: 0;
  }
}

//查询条件
.m-query {
  position: relative;

  &.is-border-bottom {
    margin-bottom: 20px;

    .el-form {
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        left: -20px;
        right: -20px;
        bottom: 0;
        height: 1px;
        background-color: #f2f2f2;
      }
    }
  }

  & + .m-collapse {
    margin-top: -20px;
  }

  &.mb0 {
    margin-bottom: 0;
  }

  .el-form-item {
    display: flex;
    margin-bottom: 16px;
    height: 36px;
  }

  .el-col {
    &:last-child {
      .el-form-item {
        height: auto;
      }
    }
  }

  .el-form-item__label {
    line-height: 36px;
  }

  .el-form-item__content {
    flex: 1;
    line-height: 35px;
  }

  .el-select,
  .el-cascader {
    width: 100%;
  }

  .el-date-editor--daterange,
  .el-date-editor--timerange,
  .el-date-editor--datetimerange {
    &.el-input,
    &.el-input__inner {
      width: 100%;
    }

    .el-range__icon,
    .el-range-separator,
    .el-range__close-icon {
      line-height: 28px;
    }
  }

  .el-date-editor .el-range-input {
    font-size: 12px;

    &::placeholder {
      font-size: 14px;
    }
  }

  .el-date-editor.el-input.f-wf {
    width: 100%;
  }

  .range-wrapper {
    height: 34px;
    line-height: 34px;

    .el-date-editor {
      &.el-input {
        .el-input__inner {
          height: 34px;
          line-height: 34px;
        }
      }
    }
  }

  .input-num {
    width: 80px;

    .el-input__inner {
      text-align: center;
    }
  }

  .el-button {
    padding: 10px 7px;
    min-width: 55px;
    margin-bottom: 10px;
    margin-right: 5px;
    font-size: 13px;

    &.el-button--text {
      padding-left: 0;
      padding-right: 0;
      min-width: inherit;

      .el-icon--right {
        margin-left: 2px;
      }
    }
  }

  .el-button + .el-button {
    margin-left: 0;
  }
}

//表格
.m-table {
  width: 100%;
  color: #444;
  line-height: 1.5;

  thead {
    color: #555;
  }

  th {
    &.el-table__cell {
      background-color: #f5f5f5;
      padding: 5px 0;
      height: 50px;

      & > .cell {
        padding-left: 18px;
        padding-right: 18px;
        line-height: 1.2;
      }

      .caret-wrapper {
        margin-top: -7px;
        margin-bottom: -7px;
      }
    }

    &.is-selection {
      .el-checkbox__input {
        &::after {
          content: '全选';
          display: inline-block;
          vertical-align: 2px;
          margin-left: 10px;
        }
      }
    }
  }

  [class^=el-icon-],
  [class*=" el-icon-"] {
    vertical-align: middle;
  }

  .cell {
    padding-left: 14px;
    padding-right: 14px;
    line-height: 1.5;
  }

  .el-table__fixed-right-patch {
    background-color: #f5f5f5;
  }

  &.el-table--border {
    .el-table__cell {
      padding: 8px 0;

      &.is-right {
        padding-right: 0;
      }
    }
  }

  &.is-statistical {
    .el-table__cell {
      padding: 5px 0;
    }

    th.el-table__cell > .cell {
      padding-left: 10px;
      padding-right: 10px;
    }

    .cell {
      padding-left: 10px;
      padding-right: 10px;
    }
  }

  .el-table__cell {
    padding: 10px 0;

    &.is-right {
      padding-right: 20px;
    }
  }

  &.is-header {
    .el-table__empty-block {
      display: none;
    }
  }

  &.is-body {
    .el-table__header-wrapper {
      display: none;
    }

    .el-table__body-wrapper {
      border-top: 1px solid #ebeef5;
    }
  }

  &.is-tree {
    .el-table__cell {
      padding: 12px 0;
    }

    .cell {
      padding-left: 10px;
      padding-right: 10px;
      display: flex;
      align-items: center;
    }
  }

  .el-tag {
    margin-top: 3px;
    margin-right: 5px;
    margin-bottom: 3px;
    padding: 5px 8px 3px;
    white-space: inherit;
    height: auto;
    line-height: 1.3;
    text-align: left;

    .icon {
      font-size: 16px;
      vertical-align: -1px;
      margin-left: 5px;
    }

    &.el-tag--mini {
      padding: 3px 4px 1px;
    }
  }

  .el-button--text {
    padding: 3px 6px;

    & + .el-button--text {
      margin-left: 0;
    }

    &.f-to-three {
      padding: 0;
      width: 100%;
      text-align: left;

      span {
        display: -webkit-box;
        overflow: hidden;
        white-space: normal;
        text-overflow: ellipsis;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        word-break: break-all;
        line-height: 20px;

        &.el-tag {
          display: inline-block;
          margin: 0 5px 0 0;
          padding: 0 3px;
          vertical-align: middle;
        }
      }
    }
  }

  .el-empty {
    flex-direction: row;
    padding: 10px 0 15px;
    line-height: 1.5;

    .el-empty__description {
      margin-top: 3px;
      margin-left: 10px;
    }
  }

  .is-selection {
    .el-checkbox__input {
      &::after {
        content: '选择';
        display: inline-block;
        vertical-align: 2px;
        margin-left: 10px;
      }

      &.is-checked {
        &::after {
          color: $base;
        }
      }
    }
  }

  .op-col {
    .el-button {
      font-size: 18px;
    }

    .cell {
      display: block;
    }
  }

  .tree-cell {
    .el-table__expand-icon {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      opacity: 0;

      &.el-table__expand-icon--expanded {
        transform: rotate(0);

        .el-icon-arrow-right {
          transform: rotate(90deg);
        }

        & + div {
          .arrow-icon {
            transform: rotate(90deg);
          }
        }
      }
    }

    .el-button--text {
      padding: 0;
    }

    .arrow-icon {
      font-size: 16px;
      vertical-align: -1px;
      transition: all 0.4s;
    }
  }

  .el-table__body tr.el-table__row--level-1 {
    td {
      background-color: #f5f7fa;
    }

    &.el-table__row--striped {
      td.el-table__cell {
        background-color: #f5f7fa;
      }
    }
  }
}

//表单
.m-form {
  .el-form-item__label {
    font-weight: bold;
    color: #555;
    line-height: 36px;

    .f-vm {
      vertical-align: 0;
    }
  }

  .is-tag {
    position: absolute;
    left: -13px;
    top: 16px;
  }

  .el-select,
  .el-cascader {
    width: 100%;
  }

  .el-date-editor--daterange,
  .el-date-editor--timerange,
  .el-date-editor--datetimerange {
    .el-range__icon,
    .el-range-separator,
    .el-range__close-icon {
      line-height: 28px;
    }
  }

  .el-switch__label {
    color: #999;

    &.is-active {
      color: $base;
    }
  }

  .form-l {
    width: 60%;
  }

  .form-m {
    width: 40%;
  }

  .form-s {
    width: 30%;
  }

  .input-num {
    width: 80px;

    .el-input__inner {
      text-align: center;
    }
  }

  .el-radio.is-bordered {
    padding-top: 9px;
  }

  .is-column {
    .el-checkbox,
    .el-radio {
      display: block;
    }

    .el-radio {
      line-height: 36px;
    }
  }

  .rich-text {
    width: 100%;
  }

  .is-text {
    .el-form-item__label,
    .el-form-item__content {
      line-height: 1.5;
    }

    .el-form-item__content {
      //margin-top: 2px;
    }
  }

  .el-upload__tip {
    line-height: 1.5;
    font-size: 13px;
    color: #777;
    display: flex;
    margin-top: 10px;

    .el-icon-warning {
      color: #999;
      font-size: 16px;
      margin-right: 3px;
      position: relative;
      top: 1px;
    }

    .f-link {
      color: $base;
      text-decoration: underline;
      display: inline-block;
    }
  }

  .code {
    width: 120px;
    height: 36px;
    cursor: pointer;
    margin-left: 10px;
    text-align: center;

    img {
      width: 100%;
      height: 100%;
      display: block;
      border-radius: 4px;
      border: 1px solid #dcdef6;
      box-sizing: border-box;
    }

    .el-button {
      width: 100%;
      height: 100%;
      padding: 0 10px;
    }
  }

  .bg-gray {
    background-color: #f5f5f5;

    .el-divider__text {
      background-color: #f5f5f5;
    }
  }

  .f-wf.el-date-editor.el-input,
  .f-wf.el-date-editor.el-input__inner {
    width: 100%;
  }

  .label-required {
    .el-form-item__label {
      display: flex;
      align-items: center;
      padding-left: 15px;
    }
  }

  &.is-table {
    .el-form-item {
      margin-top: 15px;
      margin-bottom: 15px;
    }

    .el-form-item__error {
      text-align: center;
      width: 100%;
    }
  }

  &.is-mb {
    .el-form-item {
      margin-bottom: 22px !important;
    }
  }

  .time-item {
    border-radius: 5px;
    border: 1px solid #eee;
    display: flex;
    align-items: center;
    padding: 0 15px;
    box-sizing: border-box;

    .time {
      flex: 1;
      font-size: 15px;
    }
  }

  .wp-95 {
    width: 95%;
  }
}

//表单宽度限制
.width-limit {
  .el-col {
    max-width: 850px;
  }

  &.m-form {
    position: relative;
    left: -40px;
  }
}

//密码安全性判断
.psw-tips {
  margin-top: 5px;
  display: flex;
  align-items: center;

  .el-progress {
    flex: 1;
  }

  .txt {
    font-size: 12px;
    margin-left: 6px;
    line-height: 1;
    padding-right: 5px;
  }

  .txt-l {
    color: #e93737;
  }

  .txt-m {
    color: #ee9e2d;
  }

  .txt-h {
    color: #49b042;
  }
}

//按钮模块
.m-btn-bar {
  padding-bottom: 10px;

  .el-button {
    height: 40px;
    min-width: 120px;
  }

  &.is-sticky {
    position: sticky;
    bottom: 0;
    z-index: 9999;
    padding-bottom: 15px;
    background-color: rgba(#fff, 0.7);
    backdrop-filter: blur(5px);
  }

  &.is-sticky-1 {
    position: sticky;
    bottom: 0;
    z-index: 9;
    padding-bottom: 15px;
    background-color: rgba(#f0f2f5, 0.7);
    backdrop-filter: blur(5px);
    padding-top: 20px;
  }

  &.is-column {
    flex-direction: column;

    .el-button {
      width: 100%;

      & + .el-button {
        margin-left: 0;
        margin-top: 20px;
      }
    }
  }
}

//文字列表
.m-text-form {
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;

  &.is-column {
    flex-direction: column;
  }

  &.is-border-bottom {
    border-bottom: 1px solid #eee;
  }

  &.is-edit {
    overflow: inherit;

    .edit-icon {
      font-size: 16px;
      margin-left: 5px;
      display: inline-block;
      vertical-align: -1px;
      cursor: pointer;

      &.item {
        border-bottom: none;
      }

      &:hover {
        color: $base;
      }
    }

    .edit-box {
      width: 100%;
      display: flex;
      align-items: center;
      margin-right: 20px;
      position: absolute;
      top: -5px;
      left: 0;
      background-color: #fff;
      transition: all 0.3s;
      opacity: 0;

      .el-cascader,
      .el-input {
        flex: 1;
      }
    }

    .is-editing {
      .edit-box {
        opacity: 1;
      }
    }
  }

  .el-form-item {
    margin-bottom: 16px;

    &.is-form {
      .el-form-item__label,
      .el-form-item__content {
        line-height: 36px;
      }
    }
  }

  .el-form-item__label {
    line-height: 1.6;
    color: #555;
    font-weight: bold;
  }

  .el-form-item__content {
    flex: 1;
    line-height: 1.6;
    word-break: break-all;
  }

  &.border-right {
    border-right: 1px dashed #e8e8e8;
  }
}

//图片上传
.m-pic-upload {
  .el-upload--picture-card {
    .el-icon-plus {
      font-size: 20px;
      font-weight: bold;
    }

    &:hover {
      .upload-placeholder,
      .el-icon-plus {
        color: $base;
      }
    }
  }

  .el-upload-list__item {
    overflow: inherit;
  }

  .el-upload-list__item-thumbnail {
    object-fit: contain;
    border-radius: 4px;
  }

  .img-file {
    width: 100%;
    height: 100%;
  }

  .other {
    width: 100%;
    position: absolute;
    left: 100%;
    bottom: 0;
    margin-left: 20px;
  }

  .upload-placeholder {
    line-height: 1.5;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;

    .txt {
      margin-top: 8px;
    }
  }

  .el-upload__tip {
    line-height: 1.5;
    font-size: 13px;
    color: #777;
    display: flex;
    margin-top: 10px;

    .el-icon-warning {
      color: #999;
      font-size: 16px;
      margin-right: 3px;
      position: relative;
      top: 1px;
    }

    .f-link {
      color: $base;
      text-decoration: underline;
      display: inline-block;
      cursor: pointer;
    }
  }

  //资讯或课程图16:9上传
  &.proportion-pic {
    .el-upload--picture-card {
      width: 320px;
      height: 180px;
    }

    .el-upload-list__item {
      width: 320px;
      height: 180px;
      display: block;
      margin-bottom: 15px;
    }

    &.is-small {
      .el-upload--picture-card {
        width: 160px;
        height: 90px;
      }

      .el-upload-list__item {
        width: 160px;
        height: 90px;
      }
    }

    .upload-placeholder {
      flex-direction: row;

      .txt {
        margin-left: 10px;
        margin-top: 0;
      }
    }
  }

  //长图上传
  &.long-pic {
    .el-upload--picture-card {
      width: 48%;
      height: 80px;
    }

    .el-upload-list__item {
      width: 48%;
      height: 80px;
      display: block;
      margin-bottom: 15px;
    }

    .upload-placeholder {
      flex-direction: row;

      .txt {
        margin-left: 10px;
        margin-top: 0;
      }
    }
  }

  //小图上传
  &.small-pic {
    .el-upload--picture-card {
      width: 80px;
      height: 80px;
    }

    .el-upload-list__item {
      width: 80px;
      height: 80px;
    }

    .upload-placeholder {
      line-height: 1.2;

      .txt {
        font-size: 12px;
      }
    }
  }
}

//图片分组上传
.m-upload-item {
  display: flex;
  align-items: flex-end;
  margin-bottom: 20px;

  .m-pic-upload {
    &.long-pic {
      width: 48%;

      .el-upload--picture-card,
      .el-upload-list__item {
        width: 100%;
      }

      .m-pic-upload {
        margin-bottom: 0;
      }
    }
  }

  .other {
    width: 48%;
    padding-left: 20px;
  }
}

//开关
.m-switch {
  overflow: hidden;

  .el-switch__label {
    opacity: 0;
    transition: all 0.3s;

    &.is-active {
      opacity: 1;
    }
  }

  .el-switch__label--left {
    position: absolute;
    left: 50px;
    min-width: 50px;

    &.is-active {
      color: #999;
    }
  }
}

//文字提示
.m-tooltip {
  &.el-tooltip__popper {
    font-size: 14px;
    max-width: 400px;
    line-height: 1.5;
  }

  .f-cb {
    color: lighten($base, 15%);

    &:hover {
      color: lighten($base, 15%);
    }
  }

  &.is-small {
    padding: 5px 8px;
    font-size: 13px;
  }
}

.m-tooltip-icon {
  display: inline-block;
  vertical-align: -3px;
  font-size: 18px;
  line-height: 1;
}

//弹窗
.m-dialog {
  display: flex;
  align-items: center;

  .el-dialog {
    margin-top: -50px !important;
  }

  .el-dialog__body {
    color: #444;
  }

  .dialog-alert {
    display: flex;
    padding-right: 10px;

    &.is-big {
      justify-content: center;
      align-items: center;

      .icon {
        font-size: 60px;
      }
    }

    .icon {
      font-size: 30px;
      margin-right: 16px;

      &.warning {
        color: #de9e3e;
      }

      &.error {
        color: #f56c6c;
      }

      &.success {
        color: #67c23a;
      }

      &.info {
        color: #909399;
      }
    }

    .txt {
      margin-top: 5px;
    }
  }

  &.is-full {
    .el-dialog {
      margin-top: 0 !important;
      display: flex;
      flex-direction: column;
      height: 100%;
    }

    .el-dialog__header {
      padding: 0 20px;
      border-bottom: 1px solid #eee;

      .header-cont {
        height: 60px;
        flex-shrink: 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .txt {
        font-size: 18px;
        font-weight: bold;
      }

      .close {
        font-size: 30px;
        color: #000;
        opacity: 0.5;
        transition: all 0.4s;

        &:hover {
          opacity: 0.7;
        }
      }
    }

    .el-dialog__body {
      padding: 20px 20px 10px;
      flex: 1;
      height: calc(100% - 61px);
      box-sizing: border-box;
    }

    .close-tips {
      width: 240px;
      background-color: rgba(#000, 0.7);
      color: #fff;
      text-align: center;
      padding: 10px;
      border-radius: 5px;
      font-size: 16px;
      position: absolute;
      top: 40px;
      left: 50%;
      margin-left: -120px;
      z-index: 9;
      transition: all 0.4s;

      &.hide {
        opacity: 0;
      }

      .key {
        display: inline-block;
        margin-left: 8px;
        margin-right: 8px;
        border: 1px solid #999;
        padding: 3px 10px;
        border-radius: 5px;
      }
    }
  }
}

//消息提示
.m-message {
  max-width: 450px;
  padding: 25px 25px 25px 30px;
  top: 35% !important;

  .el-message__icon {
    font-size: 32px;
  }

  .el-message__content {
    line-height: 1.5;
  }
}

//图片弹窗
.m-dialog-pic {
  display: flex;
  align-items: center;

  .el-dialog {
    margin-top: -50px !important;
  }

  .el-dialog__body {
    padding-top: 20px;
    padding-bottom: 40px;
  }

  img {
    width: auto;
    max-width: 1000px;
    max-height: 600px;
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
}

//抽屉
.m-drawer {
  .el-drawer__body {
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
  }

  .el-drawer__header {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
  }

  .drawer-bd {
    padding: 0 20px 20px;
    flex: 1;
  }

  .drawer-ft {
    position: sticky;
    bottom: 0;
    z-index: 9;
    padding: 15px 0;
    background-color: rgba(#f8f8f8, 0.9);
    border-top: 1px solid #eee;
    text-align: center;
  }

  .m-btn-bar {
    &.is-sticky {
      padding-top: 10px;
    }
  }
}

//提示
.m-alert {
  padding: 9px 16px;

  &.is-border-bottom {
    &.el-alert--warning {
      border-bottom: 1px solid #ece2d3;
    }
  }

  .el-alert__title {
    font-size: 14px;

    & + .el-alert__description {
      margin-top: 5px;
    }
  }

  .el-alert__description {
    margin-top: 0;
    font-size: 14px;

    .f-c3 {
      color: #333;
    }

    .f-c6 {
      color: #666;
    }
  }

  .el-alert__icon {
    font-size: 20px;
    width: auto;
    margin-right: 8px;
  }

  .el-alert__content {
    padding: 0;
    width: 100%;
  }

  .lh20 {
    line-height: 20px;
  }
}

//栏目设置
.m-web-column {
  width: 930px;
  margin: 0 auto;

  .item {
    border: 1px solid #ebedf1;
    border-radius: 4px;
    margin-bottom: 15px;
    overflow: hidden;
    transition: all 0.3s;
    background-color: #fff;

    &:hover {
      box-shadow: 0 0 15px rgba(#000, 0.1);
    }

    &.disabled {
      background-color: #f5f5f5;

      .item-ft {
        background-color: #eee;
      }
    }

    &.add {
      height: 120px;
      line-height: 120px;
      border: 1px dashed $base;
      color: $base;
      background-color: rgba($base, 0.1);
      text-align: center;
      cursor: pointer;

      &:hover {
        background-color: rgba($base, 0.15);
        box-shadow: none;
      }
    }

    .el-form-item {
      margin-bottom: 0;
    }

    .el-form-item__error {
      top: 54px;
    }
  }

  .item-hd {
    padding: 10px 15px 0;
    color: #bbb;

    & + .item-bd {
      //height: 46px;
    }
  }

  .item-bd {
    display: flex;
    padding: 0 15px;
    height: 56px;
    align-items: center;
    position: relative;

    .name {
      flex: 1;
      line-height: 1.3;
      margin-right: 5px;
    }

    .el-tag {
      border-radius: 100px;
    }

    .ipt {
      position: absolute;
      top: 10px;
      left: 10px;
      right: 10px;
      width: auto;
    }
  }

  .item-ft {
    text-align: right;
    background-color: #f9f9f9;
    line-height: 28px;
    padding: 0 15px;

    .el-button {
      font-size: 12px;
    }
  }
}

//属性值
.m-attribute {
  .item {
    border: 1px solid #ebedf1;
    border-radius: 4px;
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s;
    background-color: #fff;
    display: flex;
    align-items: center;

    &:hover {
      box-shadow: 0 0 15px rgba(#000, 0.1);
    }

    &.disabled {
      background-color: #f5f5f5;

      .item-ft {
        background-color: #eee;
      }
    }
  }

  .item-bd {
    display: flex;
    padding-left: 15px;
    height: 50px;
    align-items: center;
    position: relative;
    flex: 1;
    min-width: 0;

    .name {
      line-height: 1.2;
      font-weight: bold;
    }
  }

  .item-ft {
    padding: 0 15px;
    text-align: right;

    .el-button {
      font-size: 12px;
      padding: 5px 0;
    }
  }

  .external {
    border-radius: 0 100px 100px 100px;
    background-color: lighten($base, 40%);
    font-size: 12px;
    height: 22px;
    display: flex;
    overflow: hidden;
    margin-bottom: 2px;

    .tit {
      width: 60px;
      padding: 3px 0;
      background-color: $base;
      color: #fff;
      text-align: center;
    }

    .txt {
      max-width: 110px;
      padding: 3px 8px;
      color: $base;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .m-tit,
  .m-sub-tit {
    .external {
      .txt {
        width: auto;
        padding: 3px 8px;
      }
    }
  }

  .el-collapse {
    border: none;
  }

  .el-collapse-item__wrap {
    border-bottom: none;
  }

  .el-collapse-item {
    margin-bottom: 10px;
  }

  .el-collapse-item__header {
    background-color: #f5f5f5;
    padding: 0 10px 0 20px;
    border-radius: 4px;
    transition: border 0.3s;
    border: 1px solid transparent;

    &.is-active {
      margin-bottom: 0;
      border-radius: 4px 4px 0 0;
      border-color: #ebedf1;
      border-bottom-color: transparent;
    }
  }

  .el-collapse-item__content {
    border: 1px solid #ebedf1;
    border-top: none;
    border-radius: 0 0 4px 4px;
    padding-bottom: 0;
  }

  .el-cascader-panel {
    &.is-bordered {
      border: none;
    }
  }

  .el-cascader-menu__wrap {
    height: auto;
    overflow: inherit;
  }

  .el-cascader-menu {
    padding: 10px 6px 16px 0;
  }

  .el-cascader-node {
    height: 40px;
    line-height: 40px;
  }
}

//风格设置
.m-style-set {
  width: 800px;
  margin: 0 auto;

  .style-bd {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    border: 1px solid #ebedf1;
    border-radius: 4px;
    padding: 30px 50px;

    .item {
      width: 60px;
      height: 60px;
      line-height: 60px;
      text-align: center;
      border-radius: 4px;
      margin: 10px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: scale(1.1);
      }

      &.selected {
        color: #fff;
        font-size: 30px;
        font-weight: bold;
      }
    }

    .line {
      width: 1px;
      height: 60px;
      background-color: #e6e6e6;
      margin: 10px 20px;
    }
  }
}

//功能设置
.m-function-set {
  display: flex;
  flex-wrap: wrap;
  margin-right: 80px;

  .item {
    padding: 20px;
    display: flex;
    align-items: center;
    border: 1px solid #ebedf1;
    margin-top: -1px;
    margin-left: -1px;

    .name {
      flex: 1;
    }
  }
}

.web-banner {
  width: 400px;

  img {
    border-radius: 4px;
  }
}

.h5-banner {
  height: 90px;

  img {
    border-radius: 4px;
  }
}

.course-pic {
  width: 320px;
  height: 180px;

  img {
    border-radius: 4px;
    width: 100%;
    height: 100%;
  }

  &.is-small {
    width: 160px;
    height: 90px;
  }
}

.img-hover {
  position: relative;
  overflow: hidden;

  &:hover {
    .hover-txt {
      bottom: 0;
    }
  }

  .hover-txt {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: -32px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    text-align: center;
    padding: 2px 10px;
    box-sizing: border-box;
    transition: all 0.4s;
    font-size: 13px;
  }
}

//空数据
.m-no-date {
  width: 100%;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &.is-row {
    flex-direction: row;

    .date-bd {
      margin-top: 10px;
      margin-left: 20px;
      text-align: left;
    }
  }

  .img {
    width: 200px;
    display: block;

    &.is-small {
      width: 150px;
    }

    img {
      width: 100%;
      height: 100%;
    }
  }

  .date-bd {
    margin-bottom: 10px;
    text-align: center;
  }

  .login-txt {
    margin-left: 30px;

    .txt-1 {
      font-size: 30px;
      font-weight: bold;
    }

    .txt-2 {
      font-size: 24px;
    }
  }
}

.m-tree {
  .el-tree-node__content {
    line-height: 36px;
  }
}

.m-course-tree {
  max-height: 500px;
  overflow: auto;
  color: #333;

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .el-tree-node__expand-icon {
    font-size: 18px;
  }

  .is-current {
    &.is-focusable {
      & > .el-tree-node__content {
        background-color: $base;

        .el-tree-node__expand-icon {
          color: #fff;

          &.is-leaf {
            color: transparent;
          }
        }

        .custom-tree-node {
          color: #fff;

          .el-button {
            color: #fff;
          }
        }
      }
    }
  }

  .el-tree-node__content {
    height: auto;
    padding: 8px 4px 8px;
    border-bottom: 1px solid #ebeef5;
    white-space: initial;
  }

  .tit {
    flex: 1;
    min-width: 0;
    padding-right: 10px;
  }

  .op {
    padding-right: 15px;

    .el-button {
      font-size: 18px;
    }
  }
}

//关联媒体文件
.m-file-upload {
  border: 1px solid #ebedf1;
  padding: 10px 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;

  .el-progress {
    width: 240px;
    margin-right: 10px;
    margin-left: 30px;
  }

  .time {
    margin-right: 30px;
  }

  .size {
    flex: 1;
    color: #999;
    margin-left: 5px;
  }

  .icon {
    color: #999;
    font-size: 18px;
    margin-right: 10px;
  }

  .name {
    max-width: 280px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    &.f-flex-sub {
      max-width: inherit;
    }
  }
}

//权限配置
.m-authority {
  .selected-all {
    background-color: #e3f0fe;
    padding: 0 20px;
    margin-bottom: 18px;
    height: 50px;
    line-height: 50px;
    border-radius: 4px;
    position: relative;

    &::after {
      content: '';
      width: 0;
      height: 0;
      border: 8px solid transparent;
      border-top-color: #e3f0fe;
      position: absolute;
      left: 20px;
      bottom: -16px;
    }
  }

  .el-collapse {
    border: none;
  }

  .el-collapse-item__wrap {
    border-bottom: none;
  }

  .el-collapse-item {
    margin-bottom: 10px;
  }

  .el-collapse-item__header {
    background-color: #f5f5f5;
    padding: 0 10px 0 20px;
    border-radius: 4px;
    transition: border 0.3s;
    border: 1px solid transparent;

    &.is-active {
      margin-bottom: 0;
      border-radius: 4px 4px 0 0;
      border-color: #dcdfe6;
      border-bottom-color: transparent;
    }
  }

  .el-collapse-item__content {
    border: 1px solid #dcdfe6;
    border-top: none;
    border-radius: 0 0 4px 4px;
    padding-bottom: 0;
    transition: all 0.3s;
  }

  .el-cascader-panel {
    &.is-bordered {
      border: none;
    }
  }

  .el-cascader-menu__wrap {
    height: auto;
    overflow: inherit;
  }

  .el-cascader-menu {
    padding: 10px 6px 16px 0;
  }

  .el-cascader-node {
    height: 40px;
    line-height: 40px;
  }
}

.m-option-btn {
  .el-button--mini {
    padding: 4px 8px;
  }
}

//步骤
.m-steps {
  .el-step__icon {
    width: 56px;
    height: 56px;
    background-color: #ddd;
    border: 8px solid #fff;
    font-size: 18px;
    color: #ababab;
  }

  .is-horizontal {
    .el-step__line {
      height: 3px;
      background-color: #ddd;
      top: 50%;
      margin-top: -1px;
    }
  }

  .el-step__head {
    position: relative;

    &.is-finish {
      .el-step__icon {
        background-color: $base;
        color: #fff;
      }

      .el-step__line-inner {
        background-color: $base;
      }
    }
  }

  .el-step__title {
    font-size: 14px;

    &.is-process {
      font-weight: normal;
      color: #ababab;
    }

    &.is-wait {
      color: #ababab;
    }
  }
}

//步骤
.m-vertical-steps {
  padding: 30px 0 20px 120px;

  .el-step__icon {
    width: 56px;
    height: 56px;
    background-color: #ddd;
    border: 8px solid #fff;
    font-size: 18px;
    color: #ababab;
  }

  .is-horizontal {
    .el-step__line {
      height: 3px;
      background-color: #ddd;
      top: 50%;
      margin-top: -1px;
    }
  }

  .el-step__head {
    position: relative;

    &.is-finish {
      .el-step__icon {
        background-color: $base;
        color: #fff;
      }
    }
  }

  .el-step__title {
    font-size: 14px;

    &.is-process {
      font-weight: normal;
      color: #444;
    }

    &.is-finish {
      color: #444;
    }
  }

  .is-vertical {
    justify-content: flex-start;
    flex-basis: inherit !important;

    .el-step__line {
      left: 27px;
    }

    .el-step__head {
      width: 56px;
    }

    .el-step__main {
      margin-bottom: 30px;
    }

    .el-step__title {
      line-height: 56px;
    }
  }
}

//分割线
.m-divider {
  background-color: #f1f1f1;

  &.el-divider--horizontal {
    margin: 20px 0;
  }

  &.no-mb {
    margin-bottom: 0;
  }
}

//大题配置
.m-question-set {
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 22px 20px 0;
  border: 1px solid #eee;
  position: relative;

  .is-text .el-form-item__content {
    margin-top: 0;
  }
}

//订单状态
.m-order-state {
  margin-bottom: 10px;

  &.el-card {
    border-top: 4px solid $base;
  }

  .el-card__body {
    display: flex;
  }

  .info {
    min-width: 30%;
    padding: 0 20px;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .state {
    font-size: 20px;
    font-weight: bold;
    margin-top: 15px;
  }

  .process {
    flex: 1;
    border-left: 1px solid #e6e6e6;
    padding: 50px 50px 0;

    .el-step__icon {
      font-size: 26px;
      width: 60px;
      height: 26px;

      .hb-iconfont {
        font-size: 26px;
        color: #ccc;
        font-weight: normal;
      }
    }

    .el-step__title {
      font-size: 14px;
      line-height: 38px;
    }

    .el-step.is-horizontal .el-step__line {
      background-color: #ddd;
      height: 1px;
    }

    .el-step__line-inner {
      border-bottom: 0;
    }

    .el-step__description {
      line-height: 16px;
    }

    .el-step__head.is-process {
      color: #ababab;
      border-color: #ababab;
    }

    .el-step__head.is-wait {
      color: #ababab;
      border-color: #ababab;
    }

    .el-step__title.is-wait {
      color: #ababab;
    }

    .el-step__title.is-process {
      font-weight: normal;
      color: #ababab;
    }

    .el-step__head.is-finish {
      color: $base;
      border-color: $base;

      .hb-iconfont {
        color: $base;
      }
    }

    .el-step__title.is-finish,
    .el-step__description.is-finish {
      color: $base;
    }
  }
}

//订单详情页 订单信息
.m-order-info {
  .el-card__body {
    display: flex;
    flex-wrap: wrap;

    .el-col {
      height: 100%;
    }
  }

  .card-header {
    padding: 0;
  }

  .m-tit {
    .tit-txt {
      font-size: 15px;
    }
  }

  .el-form-item {
    margin-bottom: 12px;
  }

  .el-form-item__label {
    color: #999;
    font-weight: normal;
  }

  .right {
    width: 30%;
    border-left: 1px solid #e6e6e6;
  }
}

//订单-价格汇总
.m-order-sum {
  text-align: right;
  color: #666;
  font-size: 13px;

  .item {
    padding-right: 20px;
    line-height: 2;
  }

  .price {
    width: 80px;
    display: inline-block;
    font-weight: bold;
    color: #333;

    .num {
      font-size: 14px;
    }
  }

  .sum-price {
    background-color: #f8f8f8;
    border-top: 1px solid #eee;
    padding: 8px 20px;
    margin-top: 10px;

    .price {
      color: $danger;
    }

    .num {
      font-size: 18px;
    }
  }

  .el-button {
    min-width: 130px;
    height: 40px;
    font-size: 15px;
    font-weight: bold;
  }
}

//弹窗提示样式
.m-popover {
  padding: 20px;
}

//证书图片列表
.m-certificate-img {
  li {
    float: left;
    margin: 5px;
  }

  .img {
    width: 80px;
    height: 45px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

//折叠面板
.m-collapse {
  &.no-border {
    border: none;

    .el-collapse-item__wrap {
      border-bottom: none;
    }
  }
}

//精品课程分类
.m-course-classify {
  border-right: 1px solid #e6e6e6;
  padding-right: 30px;
  height: 100%;

  .item {
    padding: 12px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
    cursor: pointer;

    &:hover,
    &.current {
      background-color: rgba($base, 0.1);
    }

    &.current {
      background-color: $base;
      color: #fff;

      .icon {
        &:hover {
          color: #fff;
          opacity: 0.8;
        }
      }
    }

    .icon {
      cursor: pointer;
      font-size: 20px;
      margin-left: 10px;

      &:hover {
        color: $base;
      }
    }
  }
}

//已报方案
.m-plan-list {
  max-height: inherit !important;
  overflow: auto;

  .el-table__cell {
    padding: 12px 0;
    cursor: pointer;
  }

  &.is-arrow {
    .el-table__body-wrapper,
    .cell {
      overflow: inherit;
    }

    .cell {
      &::after {
        content: '';
        width: 0;
        height: 0;
        border: 8px solid transparent;
        position: absolute;
        right: 5px;
        top: 50%;
        margin-top: -8px;
        transition: all 0.4s;
      }
    }

    .current-row {
      .cell {
        position: relative;

        &::after {
          border-left-color: #fff;
        }
      }
    }
  }

  .cell {
    padding: 0 10px 0 25px;
  }

  .current-row {
    .el-button--text,
    .el-table__expand-icon {
      color: #fff;
    }
  }

  .el-table__body tr.current-row > td.el-table__cell,
  .el-table__body tr.el-table__row--striped.current-row td.el-table__cell {
    background-color: $base;
    color: #fff;

    .f-c9 {
      color: rgba(#fff, 0.6);
    }

    .label {
      border-color: #fff;
    }
  }

  .label {
    width: 40px;
    height: 40px;
    border-radius: 100%;
    border: 2px solid $base;
    color: $base;
    line-height: 1.2;
    align-self: center;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    background-color: #fff;

    &.is-warning {
      border-color: $warning;
      color: $warning;
    }

    &.is-success {
      border-color: $success;
      color: $success;
    }

    &.is-error {
      border-color: $danger;
      color: $danger;
    }
  }

  .tag {
    position: absolute;
    top: 0;
    left: 0;
    font-size: 12px;
    color: $success;

    &::before {
      content: '';
      width: 0;
      height: 0;
      border-top: 32px solid rgba($success, 0.2);
      border-right: 32px solid transparent;
      position: absolute;
      top: 0;
      left: 0;
    }

    .txt {
      position: relative;
      z-index: 1;
      display: block;
      transform: rotate(-45deg) scale(0.75);
      padding-top: 1px;
    }

    &.is-gray {
      &::before {
        border-top-color: #eee;
      }

      .txt {
        color: #999;
      }
    }
  }
}

//考试分数
.m-score {
  width: 15%;
  min-width: 100px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .num {
    font-size: 36px;
    line-height: 1;
    margin-right: 5px;
    font-weight: bold;
  }
}

//登录/忘记密码
.m-login-wrap {
  height: 100%;
  width: 100%;
  background: url('../assets/images/login_bg.jpg') no-repeat #ddd center top;
  display: flex;
  align-items: center;
  justify-content: center;

  &.bg-forget {
    background-image: url('../assets/images/forget_bg.jpg') ;
    justify-content: center;

    .wrap-bd {
      background: #fff;
      height: auto;
    }
  }

  .wrap-bd {
    width: 1200px;
    min-height: 700px;
    border-radius: 10px;
    overflow: auto;
    background-color: #fff;
    box-shadow: 0 0 15px rgba(#174192, 0.2);
    display: flex;
    flex-direction: column;
    transition: all 0.4s;

    .login-pic {
      width: 730px;
      text-align: right;
      padding-right: 100px;
      box-sizing: border-box;
      margin-top: 40px;

      img {
        width: 540px;
      }
    }

    .logo-txt {
      text-align: center;
      font-size: 38px;
      font-weight: bold;
      padding: 70px 0 30px;
    }
  }

  .login-footer {
    background-color: #f2f2f2;
    padding: 20px 0;
    color: #999;
    font-size: 14px;
    text-align: center;
    max-height: 100px;
    overflow: auto;

    a {
      color: #999;

      &:hover {
        color: $base;
      }
    }
  }

  .m-login {
    width: 340px;

    .m-form {
      margin-top: 30px;
    }

    .el-tabs__item {
      height: 60px;
      line-height: 60px;
      font-size: 18px;
      padding: 0;

      &.is-active {
        font-weight: bold;
      }
    }

    .el-form-item {
      &.op {
        margin-top: -6px;
        margin-bottom: 4px;
      }

      .code {
        height: 48px;
      }
    }

    .el-input__inner {
      height: 48px;
      line-height: 48px;
      padding-left: 40px;
    }

    .el-input__prefix {
      color: #bad3ec;
      left: 14px;
      top: 2px;
    }

    .el-form-item__error {
      left: 42px;
    }

    .m-btn-bar {
      padding-top: 0;

      .el-button {
        width: 100%;
        height: 48px;
        font-size: 15px;
      }

      .el-form-item__content {
        line-height: 1.5;
      }
    }
  }

  .m-forget {
    display: block;
    padding: 30px;
    box-sizing: border-box;
    height: 100%;
    overflow: auto;
  }
}

//封面图片列表
.m-pic-list {
  width: 684px;
  margin: 0 auto;

  .item {
    width: 320px;
    border: 1px solid #eee;
    border-radius: 5px;
    float: left;
    margin-right: 20px;
    margin-bottom: 20px;
    transition: all 0.4s;
    cursor: pointer;

    &:hover {
      box-shadow: 0 0 15px rgba(#000, 0.15);
    }

    .info {
      height: 50px;
      box-sizing: border-box;
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .course-pic {
    display: block;

    img {
      border-radius: 4px 4px 0 0;
    }
  }
}

//网校配置概览
.m-school-set {
  .item {
    border-right: 1px solid #ebeef5;
    position: relative;
    padding: 40px 30px;

    .arrow {
      position: absolute;
      right: -20px;
      top: 50%;
      margin-top: -18px;
      font-size: 36px;
      background-color: #fff;
      color: #d5d9e2;
    }
  }

  .item-hd {
    display: flex;
    align-items: center;

    .name {
      flex: 1;
      font-size: 15px;
      font-weight: bold;
    }
  }

  .item-bd {
    margin-top: 40px;
    margin-left: -3px;

    .el-button {
      border: 3px solid #fff;
      padding: 9px 28px 9px 12px;
      position: relative;

      &::before {
        content: '';
        width: 20px;
        height: 38px;
        position: absolute;
        left: -3px;
        top: -3px;
      }

      & + .el-button {
        margin-left: -25px;
      }

      &:last-child {
        padding-right: 12px;
      }
    }

    .el-button--primary {
      background-color: $base;
      cursor: default;
    }

    .el-button--info {
      background-color: #b7c5da;

      &:hover {
        background-color: darken(#b7c5da, 5%);
      }
    }
  }

  .el-col {
    &:last-child {
      border-right: 0;

      .arrow {
        display: none;
      }
    }
  }

  //.el-col-sm-12 {
  //  &:nth-child(2n) {
  //    border-right: 0;
  //
  //    .arrow {
  //      display: none;
  //    }
  //  }
  //
  //  &:nth-child(-n + 2) {
  //    .item {
  //      border-bottom: 1px solid #ebeef5;
  //    }
  //  }
  //}
}

//首页数据展示
.m-index-data {
  display: flex;
  align-items: center;
  padding: 20px;

  .icon {
    width: 108px;
    height: 108px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;

    .iconfont {
      font-size: 50px;
    }
  }

  .num-item {
    flex: 1.2;
    text-align: center;

    .num {
      font-size: 36px;
      font-weight: bold;
    }

    .tit {
      font-size: 15px;
      color: #999;
    }
  }

  &.data-1 {
    .icon {
      background-color: #f1f9ee;
      color: #8ccc6d;
    }

    .iconfont {
      text-shadow: 0 0 16px rgba(#67c23a, 0.5);
    }

    .important {
      color: #8ccc6d;
    }
  }

  &.data-2 {
    .icon {
      background-color: #fdf8eb;
      color: #f0bc4d;
    }

    .iconfont {
      text-shadow: 0 0 16px rgba(#f0bc4d, 0.5);
    }

    .important {
      color: #f0bc4d;
    }
  }
}

//首页方案排行
.m-index-rank {
  padding: 0 15px;

  .item {
    display: flex;
    min-width: 0;
    line-height: 34px;

    &.hd {
      font-weight: bold;
      font-size: 15px;
      margin-bottom: 5px;

      .rank {
        color: #444;
      }
    }
  }

  .rank {
    width: 30px;
    text-align: center;
    color: #999;

    .rank-num {
      position: relative;
      z-index: 1;
    }

    &.first,
    &.second,
    &.third {
      font-weight: bold;
      position: relative;

      &::before {
        content: '';
        width: 24px;
        height: 24px;
        position: absolute;
        left: 50%;
        top: 50%;
        margin: -12px 0 0 -12px;
        border-radius: 100%;
      }
    }

    &.first {
      color: #e22929;

      &::before {
        background-color: #fadddd;
      }
    }

    &.second {
      color: #f66313;

      &::before {
        background-color: #fee6d9;
      }
    }

    &.third {
      color: #f19c16;

      &::before {
        background-color: #fdefda;
      }
    }
  }

  .name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 15px 0 30px;
  }

  .num {
    width: 50px;
    text-align: right;
  }
}

//首页图表
.m-index-chart {
  height: 380px;
}

//侧边悬浮
.m-layout {
  position: fixed;
  right: 0;
  top: 45%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  .item {
    background-color: $base;
    color: #fff;
    margin-bottom: 5px;
    border-radius: 4px 0 0 4px;
    height: 40px;
    max-width: 40px;
    padding: 0 10px;
    display: flex;
    align-items: center;
    line-height: 1.2;
    overflow: hidden;
    transition: all 0.4s;
    box-sizing: border-box;

    .bd {
      flex: 1;
      opacity: 0;
      transition: all 0.9s;
    }

    .icon {
      font-size: 22px;
      margin-right: 8px;
    }

    &.is-important {
      background-color: $important;
    }

    &.is-warning {
      background-color: $warning;
    }

    &:hover {
      max-width: 200px;

      .bd {
        opacity: 1;
      }
    }
  }
}

//修改标记
.is-tag {
  width: 6px;
  height: 6px;
  background-color: $warning;
  border-radius: 100%;
  display: inline-block;
  vertical-align: 2px;
  margin-right: 8px;
}

//新手引导开关
.m-guide-op {
  position: fixed;
  z-index: 9;
  top: 50%;
  right: -64px;
  opacity: 0.6;
  padding: 8px;
  background: $base;
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;

  .icon {
    width: 18px;
    height: 18px;
    vertical-align: middle;
    margin-right: 5px;
  }

  .txt {
    font-size: 14px;
  }

  &:hover {
    right: 0;
    opacity: 1;
  }
}

//新手引导
.m-guide-wrap {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: rgba(#000, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .wrap-bd {
    width: 100%;
    flex: 1;
    overflow: auto;
  }
}

.m-guide {
  width: 450px;
  margin-left: auto;
  margin-right: auto;
  background-color: #fff;
  position: relative;
  border-radius: 5px;
  text-align: center;
  padding: 30px 0 25px;
  margin-bottom: 20px;

  .tit {
    font-size: 16px;
    margin-bottom: 20px;
    font-weight: bold;
    color: #222;
  }

  .img {
    margin-top: 10px;
  }

  .el-button {
    width: 160px;
  }
}

.m-guide-step {
  width: 350px;
  margin-left: auto;
  margin-right: auto;
  background-color: #fff;
  border-radius: 5px;
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  box-sizing: border-box;
  padding: 15px 20px 10px;

  &.is-big {
    width: 460px;
  }

  .img {
    padding-right: 20px;
    display: block;
  }

  .hd {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .ft {
    margin-top: 10px;
    text-align: right;
  }

  .el-button {
    padding-top: 0;
    padding-bottom: 0;

    &:hover {
      text-decoration: underline;
    }

    &.is-gray {
      color: #bbb;
    }
  }

  .dot {
    position: relative;
    padding-left: 13px;
    margin-bottom: 3px;

    &::before {
      content: '';
      width: 6px;
      height: 6px;
      border-radius: 100%;
      position: absolute;
      left: 0;
      top: 7px;
      background-color: #ccc;
    }
  }
}

//富文本样式
.tox-tinymce {
  border-radius: 4px;
  border-color: #dcdfe6;
}

.tox {
  .tox-statusbar {
    border-color: #dcdfe6;
  }
}

.take-address {
  border-radius: 5px;
  border: 1px solid #e6e6e6;
  position: relative;
  overflow: hidden;
  padding: 20px;
  margin-bottom: 20px;
  cursor: pointer;

  &.is-checked,
  &:hover {
    .label {
      &::before {
        border-right: 50px solid $base;
      }
    }
  }

  .label {
    position: absolute;
    right: 0;
    top: 0;
    padding-top: 2px;
    padding-right: 6px;

    &::before {
      content: ' ';
      width: 0;
      height: 0;
      border-right: 50px solid #e6e6e6;
      border-bottom: 50px solid transparent;
      position: absolute;
      top: 0;
      right: 0;
      transition: all 0.4s;
    }

    .hb-iconfont {
      color: #fff;
      position: relative;
      z-index: 2;
    }
  }
}
//基准照片-大
.u-benchmark-photos-big {
  display: inline-block;
  width: 180px;

  .photo {
    width: 180px;
    height: 252px;
    border-radius: 3px;
    display: block;
  }

  .el-button {
    width: 100%;
  }

  .el-button + .el-button {
    margin-left: 0;
  }
}

//基准照片-中
.u-benchmark-photos-medium {
  width: 116px;

  .photo {
    width: 116px;
    height: 160px;
    border-radius: 3px;
  }
}

//基准照片-小
.u-benchmark-photos-small {
  width: 63px;
  height: 88px;
  display: inline-block;
  border-radius: 3px;
}
//设置宽度180
.u-w180 {
  width: 180px;
}
//设置宽度194
.u-w194 {
  width: 194px;
}
//设置宽度300
.u-w300 {
  width: 300px;
}
//监拍情况
.m-supervision-situation {
  padding-left: 10px;

  .item {
    margin-bottom: 20px;
    display: flex;
    flex-direction: row;

    &:last-child {
      margin-bottom: 0;
    }

    .item-no {
      margin-right: 10px;
      height: 24px;
      line-height: 24px;
      width: 40px;
      text-align: center;
    }
  }
}

//设置背景颜色
.u-bg-important {
  background-color: $important;
}

.bare-nav-wrapper {
  border-bottom: 1px solid #e4e8f2;
}

//左侧有线的树形结构
.m-tree-line {
  .el-tree-node__expand-icon {
    font-size: 18px;
    z-index: 9;
  }

  .el-tree-node__content {
    height: auto;
    padding: 4px;
    white-space: initial;
    border-radius: 4px;
    line-height: 1.5;
  }

  //第二级
  .el-tree-node__children {
    position: relative;
    overflow: inherit;

    &::before {
      content: ' ';
      position: absolute;
      top: -12px;
      bottom: 18px;
      left: 15px;
      border-left: 1px dashed #ddd;
      z-index: 9;
    }

    .el-tree-node {
      position: relative;

      &::before {
        content: ' ';
        position: absolute;
        top: 19px;
        left: 17px;
        width: 18px;
        border-top: 1px dashed #ddd;
        z-index: 9;
      }
    }

    //第三级
    .el-tree-node__children {
      &::before {
        left: 33px;
      }

      .el-tree-node {
        &::before {
          left: 33px;
        }
      }

      //第四级
      .el-tree-node__children {
        &::before {
          left: 51px;
        }

        .el-tree-node {
          &::before {
            left: 51px;
          }
        }

        //第五级
        .el-tree-node__children {
          &::before {
            left: 69px;
          }

          .el-tree-node {
            &::before {
              left: 69px;
            }
          }

          //第六级
          .el-tree-node__children {
            &::before {
              left: 87px;
            }

            .el-tree-node {
              &::before {
                left: 87px;
              }
            }
          }
        }
      }
    }
  }

  &.is-select {
    .is-current {
      &.is-focusable {
        & > .el-tree-node__content {
          background-color: $base;
          color: #fff;

          .el-tree-node__expand-icon {
            color: #fff;

            &.is-leaf {
              color: transparent;
            }
          }
        }
      }
    }
  }

  &.is-bg {
    .el-tree-node__content {
      background-color: #f5f7fa;
      margin-bottom: 2px;
      transition: all 0.4s;

      &:hover {
        background-color: darken(#f5f7fa, 3%);
      }
    }

    .el-tree-node__children {
      .el-tree-node__content {
        background-color: transparent;

        &:hover {
          background-color: #f5f7fa;
        }
      }
    }
  }
}

//定位提示语
.u-tab-tips-new {
  position: sticky;
  top: 27px;
  z-index: 1000;
  margin-left: 960px;
  margin-bottom: -48px;
  margin-top: 20px;
}

.wzxz {
  display: inline-block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 150px;
  margin-bottom: -21px;
}
//生成海报
.m-poster {
  display: flex;
  align-items: flex-start;

  .poster-tab {
    padding-right: 20px;
    position: sticky;
    top: 0;

    .item {
      width: 80px;
      height: 114px;
      margin-bottom: 20px;
      border: 5px solid #f5f5f5;
      position: relative;
      transition: all 0.4s;
      cursor: pointer;

      &::before {
        content: '';
        width: 0;
        height: 0;
        border: 12px solid transparent;
        border-left-color: $base;
        position: absolute;
        top: 20px;
        right: -24px;
        transition: all 0.4s;
        opacity: 0;
      }

      &:hover {
        border-color: $base;
      }

      img {
        width: 100%;
        height: 100%;
      }

      &.current {
        border-color: $base;
        box-shadow: 0 10px 30px -15px rgb(#000, 0.5);

        &::before {
          opacity: 1;
        }
      }
    }
  }

  .poster-box {
    width: 528px;
    height: 750px;
    overflow: hidden;
  }

  .poster {
    width: 1040px;
    height: 1477px;
    transform: scale(0.5);
    transform-origin: top left;
  }

  .poster-01 {
    background: url('../assets/images/poster-01.jpg') no-repeat center top;
    color: #000;

    .name {
      font-size: 60px;
      font-weight: bold;
      text-align: center;
      height: 210px;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1.1;
      padding: 0 30px;
    }

    .tel {
      position: absolute;
      width: 480px;
      border-bottom: 3px solid #000;
      left: 60px;
      bottom: 50px;
      display: flex;
      align-items: center;
      padding-bottom: 10px;

      &::before {
        content: '';
        width: 58px;
        height: 58px;
        background: url('../assets/images/poster-01-star.png') no-repeat;
        background-size: 100%;
        position: absolute;
        right: 0;
        top: -20px;
      }

      .cont {
        margin-left: 20px;
        font-size: 28px;
        line-height: 1;
      }

      .tel-icon {
        width: 94px;
      }

      .tel-num {
        font-size: 50px;
        font-weight: bold;
        padding-top: 5px;
      }
    }

    .qrcode-box {
      display: flex;
      flex-direction: column-reverse;
      text-align: center;
      position: absolute;
      right: 116px;
      bottom: 50px;

      .item {
        margin-top: 30px;
      }

      .code {
        width: 250px;
        height: 250px;
        overflow: hidden;
      }

      img {
        width: 100%;
        height: 100%;
        border-radius: 10px;
        display: block;
      }

      .txt {
        line-height: 56px;
        font-size: 28px;
        margin-top: 15px;
      }

      .el-upload {
        width: 100%;
        height: 100%;
        border: 2px dashed rgba(#333, 0.35);
        background-color: rgba(#333, 0.1);
        border-radius: 10px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        display: block;
        box-sizing: border-box;

        .tips {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          font-size: 26px;
          color: rgba(#fff, 0.8);
        }

        .avatar-uploader-icon {
          font-size: 42px;
        }
      }

      .el-input {
        margin-top: 15px;

        .el-input__inner {
          border: 2px dashed rgba(#333, 0.35);
          background-color: rgba(#333, 0.1);
          font-size: 23px;
          color: #000;
          width: 250px;
          height: 56px;
          line-height: 56px;
          padding: 0 45px 0 10px;

          &::placeholder {
            color: rgba(#fff, 0.8);
          }
        }

        .el-input__count-inner {
          background-color: transparent;
          font-size: 24px;
          color: rgba(#fff, 0.7);
        }
      }
    }

    .custom-text {
      writing-mode: vertical-rl;
      position: absolute;
      left: 480px;
      top: 560px;
      width: 72px;
      height: 500px;
      font-size: 28px;
      display: flex;
      justify-content: center;

      .el-textarea {
        .el-textarea__inner {
          width: 72px;
          height: 560px;
          padding: 10px;
          border: 2px dashed rgba(#333, 0.35);
          background-color: rgba(#333, 0.1);
          font-size: 28px;
          text-align: center;
          line-height: 1;
          color: #000;

          &::placeholder {
            color: rgba(#fff, 0.8);
          }
        }

        .el-input__count {
          writing-mode: horizontal-tb;
          background-color: transparent;
          font-size: 24px;
          color: rgba(#fff, 0.55);
          left: 0;
          right: 0;
          text-align: center;
        }
      }
    }
  }

  .poster-02 {
    background: url('../assets/images/poster-02.jpg') no-repeat center top;
    color: #000;

    .name {
      width: 100%;
      font-size: 50px;
      font-weight: bold;
      text-align: center;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      padding: 0 30px;
      position: absolute;
      left: 0;
      bottom: 80px;
    }

    .tel {
      position: absolute;
      width: 100%;
      left: 0;
      bottom: 30px;
      display: flex;
      align-items: flex-end;
      justify-content: center;

      .cont {
        margin-left: 20px;
        font-size: 28px;
        line-height: 1;
        display: flex;
        align-items: flex-end;
        font-weight: bold;
      }

      .tel-icon {
        width: 38px;
      }

      .tel-num {
        font-size: 50px;
        margin-bottom: -5px;
      }
    }

    .qrcode-box {
      width: 100%;
      display: flex;
      justify-content: center;
      position: absolute;
      left: 0;
      bottom: 240px;

      .item {
        margin-top: 30px;
        text-align: center;

        & + .item {
          margin-left: 50px;
        }
      }

      .code {
        width: 250px;
        height: 250px;
        overflow: hidden;
      }

      img {
        width: 100%;
        height: 100%;
        border-radius: 10px;
        display: block;
      }

      .txt {
        line-height: 56px;
        font-size: 28px;
        margin-top: 15px;
        color: #fff;
      }

      .el-upload {
        width: 100%;
        height: 100%;
        border: 2px dashed rgba(#fff, 0.55);
        background-color: rgba(#fff, 0.2);
        border-radius: 10px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        display: block;
        box-sizing: border-box;

        .tips {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          font-size: 26px;
          color: rgba(#fff, 0.8);
        }

        .avatar-uploader-icon {
          font-size: 42px;
        }
      }

      .el-input {
        margin-top: 15px;

        .el-input__inner {
          border: 2px dashed rgba(#fff, 0.55);
          background-color: rgba(#fff, 0.2);
          font-size: 23px;
          color: #fff;
          width: 250px;
          height: 56px;
          line-height: 56px;
          padding: 0 45px 0 10px;

          &::placeholder {
            color: rgba(#fff, 0.8);
          }
        }

        .el-input__count-inner {
          background-color: transparent;
          font-size: 24px;
          color: rgba(#fff, 0.55);
        }
      }
    }

    .custom-text {
      position: absolute;
      left: 0;
      top: 38px;
      width: 810px;
      height: 68px;
      font-size: 28px;
      display: flex;
      justify-content: center;
      align-items: center;

      .el-input {
        .el-input__inner {
          width: 810px;
          height: 68px;
          line-height: 68px;
          padding: 0 80px 0 15px;
          border: none;
          background-color: transparent;
          font-size: 28px;
          text-align: center;
          color: #fff;

          &::placeholder {
            color: rgba(#fff, 0.8);
          }
        }

        .el-input__count-inner {
          background-color: transparent;
          font-size: 24px;
          color: rgba(#fff, 0.55);
        }
      }
    }
  }

  .poster-03 {
    background: url('../assets/images/poster-03.jpg') no-repeat center top;
    color: #000;

    .name {
      font-size: 60px;
      font-weight: bold;
      text-align: center;
      height: 210px;
      display: flex;
      align-items: center;
      justify-content: center;
      line-height: 1.1;
      padding: 40px 30px 0;
      color: #ffce5b;
    }

    .tel {
      position: absolute;
      width: 100%;
      height: 182px;
      left: 0;
      bottom: 150px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #371181;

      .cont {
        margin-left: 20px;
        font-size: 40px;
        line-height: 1;
        display: flex;
        align-items: flex-end;
        font-weight: bold;
      }

      .tel-icon {
        width: 58px;
        margin-top: 10px;
      }

      .tel-num {
        font-size: 58px;
        margin-bottom: -5px;
      }
    }

    .qrcode-box {
      width: 100%;
      display: flex;
      justify-content: center;
      position: absolute;
      left: 0;
      bottom: 480px;
      transition: all 0.4s;

      .item {
        margin-top: 30px;
        text-align: center;

        & + .item {
          margin-left: 50px;
          transition: all 0.4s;
        }
      }

      .code {
        width: 370px;
        height: 370px;
        overflow: hidden;
        border: 5px solid #ffce5b;
        border-radius: 15px;
        box-sizing: border-box;
        transition: all 0.4s;

        &.is-dashed {
          border-style: dashed;
        }
      }

      img {
        width: 100%;
        height: 100%;
        border-radius: 10px;
        display: block;
      }

      .txt {
        line-height: 56px;
        font-size: 28px;
        margin-top: 15px;
      }

      .el-upload {
        width: 100%;
        height: 100%;
        background-color: rgba(#ffce5b, 0.1);
        border-radius: 10px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        display: block;
        box-sizing: border-box;

        .tips {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          font-size: 26px;
          color: #666;
        }

        .avatar-uploader-icon {
          font-size: 42px;
        }
      }

      .el-input {
        margin-top: 15px;

        .el-input__inner {
          border: 2px dashed #ffce5b;
          background-color: rgba(#ffce5b, 0.1);
          font-size: 23px;
          color: #000;
          width: 370px;
          height: 56px;
          line-height: 56px;
          padding: 0 45px 0 10px;
          transition: all 0.4s;

          &::placeholder {
            color: rgba(#000, 0.8);
          }
        }

        .el-input__count-inner {
          background-color: transparent;
          font-size: 24px;
          color: rgba(#000, 0.45);
        }
      }

      &.is-more {
        bottom: 530px;

        .item {
          & + .item {
            margin-left: 30px;
          }
        }

        .code {
          width: 250px;
          height: 250px;
        }

        .el-input {
          .el-input__inner {
            width: 250px;
          }
        }
      }
    }

    .custom-text {
      position: absolute;
      left: 50%;
      top: 450px;
      width: 790px;
      height: 68px;
      font-size: 28px;
      margin-left: -395px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #666;

      .el-input {
        .el-input__inner {
          border: 2px dashed #ffce5b;
          background-color: rgba(#ffce5b, 0.1);
          font-size: 23px;
          color: #000;
          width: 100%;
          height: 56px;
          line-height: 56px;
          padding: 0 80px 0 15px;
          text-align: center;

          &::placeholder {
            color: #999;
          }
        }

        .el-input__count-inner {
          background-color: transparent;
          font-size: 24px;
          color: #999;
        }
      }
    }
  }
}
//模板配置-模版图片列表
.m-demo-pic {
  li {
    width: 256px;
    float: left;
    margin-right: 100px;
    margin-bottom: 20px;

    &:nth-child(3n) {
      margin-right: 0;
    }

    .demo-pic {
      width: 250px;
      height: 350px;
      background-color: #fff;
      text-align: center;
      position: relative;
      border: 1px solid #dcdcdc;
      margin-bottom: 10px;

      .mask {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background-color: rgba(#000, 0.6);
        text-align: center;
        color: #fff;
        font-size: 30px;
        padding-top: 45%;
        box-sizing: border-box;
        opacity: 0;
        transition: all 0.4s;

        .icon {
          cursor: pointer;
          transition: all 0.4s;

          &:hover {
            opacity: 0.8;
          }
        }
      }

      .el-image {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .pic {
        width: 250px;
        height: 350px;
        overflow: overlay;

        img {
          width: 100%;
          vertical-align: middle;
          height: auto;
        }

        //滚动条样式
        &::-webkit-scrollbar {
          width: 4px;
          height: 1px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 6px;
          background: rgba(0, 0, 0, 0.3);
        }

        &::-webkit-scrollbar-track {
          background-color: #fff;
        }
      }

      .el-checkbox {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 250px;
        height: 80px;
        z-index: 2;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        display: flex;
        background: rgba(0, 0, 0, 0.8);
        transition: all 0.4s;
        opacity: 0;

        .el-checkbox__inner {
          width: 32px;
          height: 32px;
          margin-right: 10px;

          &::after {
            height: 14px;
            left: 10px;
            top: 5px;
            width: 8px;
            border-width: 2px;
          }
        }

        .el-checkbox__label {
          padding-left: 0;
          font-size: 20px;
          color: #fff;
        }

        &.is-checked {
          z-index: 2;
          color: #fff;
          opacity: 1;
        }

        .el-checkbox__input.is-checked + .el-checkbox__label {
          color: #fff;
        }
      }

      .preview {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 100%;
        text-align: center;
        height: 60px;
        background: rgba(#000, 0.8);
        color: #fff;
        line-height: 60px;
        font-size: 16px;
        opacity: 0;
        cursor: pointer;

        .el-icon-zoom-in {
          margin-right: 5px;
          font-size: 18px;
          vertical-align: -2px;
        }
      }

      &:hover {
        .el-checkbox,
        .mask,
        .preview {
          opacity: 1;
        }
      }
    }

    .demo-pic-info {
      height: 124px;
      color: #666;

      p {
        line-height: 18px;
        margin-bottom: 8px;

        .t {
          display: inline-block;
          font-weight: bold;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}
//单选框组标题
.m-radio-group-title {
  width: 98px;
  margin-right: 10px;
  line-height: 36px;
  text-align: right;
}

// 查看专题示例图样式
.m-specialimg-pop {
  position: relative;

  .transparent-pic {
    position: absolute;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
  }
}
// 仿表单
.m-as-form {
  .item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 16px;

    .tit {
      width: 120px;
      text-align: right;
      font-weight: bold;
      line-height: 36px;
      padding: 0 12px 0 0;
      box-sizing: border-box;
      color: #555;
    }

    .con {
      line-height: 36px;
      vertical-align: middle;
    }
  }
}
// 行业规则
.m-industry-rules {
  .item {
    .tit {
      font-size: 14px;
    }

    .con {
      display: flex;
      align-items: flex-start;
      background: #f4f5f7;
      padding: 10px;

      .tt {
        font-size: 14px;

        i {
          color: $important;
        }
      }

      .cc {
        display: flex;
      }
    }
  }
}

//图文简介
.m-intro {
  img {
    display: block;
    margin: 15px auto;
    max-width: 100%;
    border-radius: 5px;
  }
}

//边框
.m-left-divider {
  border: 1px solid #eee;
  border-top: 0;
  margin-top: 24px;

  .el-divider {
    background-color: #eee;
  }

  .el-divider--horizontal {
    margin-top: 0;
  }

  .tit {
    background-color: #f8f8f8;
    font-weight: bold;
    color: $base;
    font-size: 15px;
    padding: 6px 6px 6px 30px;
    box-sizing: border-box;
  }
}

// 资讯列表
.m-info-list {
  width: 100%;
  min-height: 50vh;

  .item {
    box-sizing: border-box;
    height: 160px;
    padding: 24px;
    margin-bottom: 21px;
    cursor: pointer;
    border-radius: 12px;
    border: 1px solid #fff;
    box-shadow: 0 20px 30px -20px rgba(10, 115, 210, 0.2);
    overflow: hidden;
    width: 48%;
    float: left;
    margin-right: 20px;
    background: url('../assets/images/logo-bg.jpg') no-repeat right top;
    display: flex;
    transition: all 0.4s;

    .text {
      align-self: center;

      .tit {
        margin-bottom: 10px;
        font-size: 20px;
        font-weight: bold;
        color: $base;
        //搜索高亮显示
        em {
          font-style: normal;
          color: $base;
        }
      }

      .con {
        height: 42px;
        overflow: hidden;
        font-size: 14px;
        color: #666;
        width: 280px;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        line-clamp: 2;
        -webkit-box-orient: vertical;
        //搜索高亮显示
        em {
          font-style: normal;
          color: $base;
        }
      }
    }

    &:hover {
      border-color: rgba(31, 134, 240, 0.61);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }
}
// 监管单元边框
.m-border {
  border: 1px solid #e6e6e6;
  border-radius: 10px;
  padding-top: 20px;
}
// 专题管理登录
.m-login-special {
  height: 100%;
  width: 100%;
  background: #f5f7fc url('../assets/images/login_special.jpg') no-repeat center top;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-between;
  overflow: auto;

  .wrap-bd {
    width: 1200px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    transition: all 0.4s;
    position: relative;
    margin-bottom: 30px;

    .m-logo {
      font-size: 36px;
      font-weight: bold;
      padding: 180px 0 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
    }
  }

  .m-login-footer {
    width: 100%;
    padding: 20px 0;
    color: #b4bac6;
    font-size: 14px;
    text-align: center;
    height: 100px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    p {
      display: block;
      margin: 0;
      padding: 0;
    }

    a {
      color: #b4bac6;

      &:hover {
        text-decoration: underline;
      }
    }

    .img {
      vertical-align: middle;
      margin-right: 5px;
    }
  }

  .m-login {
    width: 540px;
    margin: 0 auto;
    padding: 50px 100px;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 20px;
    box-shadow: 0 0 35px rgba(118, 136, 192, 0.2);

    .title {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 24px;

      .item {
        font-size: 24px;
        text-align: center;
        position: relative;
        height: 38px;
        cursor: pointer;

        &::before {
          content: "";
          display: none;
          width: 60px;
          position: absolute;
          left: 50%;
          margin-left: -30px;
          bottom: -6px;
          background-color: #174ee6;
          height: 4px;
          border-radius: 2px;
        }

        &.z-cur {
          &::before {
            display: block;
          }
        }
      }

      .item + .item {
        margin-left: 50px;
      }
    }

    .m-form {
      margin-top: 30px;

      .code {
        height: 48px;
      }
    }

    .el-tabs__item {
      height: 60px;
      line-height: 60px;
      font-size: 18px;
      padding: 0;

      &.is-active {
        font-weight: bold;
      }
    }

    .el-form-item {
      margin-bottom: 28px;

      &.op {
        margin-top: -9px;
        margin-bottom: 8px;

        a:hover {
          color: #174ee6;
        }
      }
    }

    .el-input__inner {
      height: 48px;
      line-height: 48px;
      padding-left: 40px;
      border-radius: 10px;
      border-color: #d2d7e4;
      font-size: 16px;
    }

    .el-input__inner:focus {
      border-color: #174ee6;
    }

    .el-input__prefix {
      color: #dcdfe6;
      left: 14px;
      top: 2px;

      .iconfont,
      .hb-iconfont {
        font-size: 18px;
      }
    }

    .el-button {
      border-radius: 10px;
    }

    .el-button--primary {
      background-color: #174ee6;
      border-color: #174ee6;

      &.is-plain {
        color: #4671e7;
        background: #f4f7ff;
        border-color: #c6d4fb;

        &:hover {
          background-color: #174ee6;
          border-color: #174ee6;
          color: #fff;
        }
      }
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      border-color: #174ee6;
      background-color: #174ee6;
    }

    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #174ee6;
    }

    .el-checkbox__inner:hover {
      border-color: #174ee6;
    }

    .el-form-item__error {
      left: 42px;
    }

    .m-btn-bar {
      padding-top: 0;
      margin-bottom: 0;

      .el-button {
        width: 100%;
        height: 48px;
        font-size: 15px;
        border: none;
        background: linear-gradient(to right, #174ee5, #3c4ded);
        box-shadow: 0 8px 16px rgba(23, 78, 229, 0.3);

        &:hover {
          opacity: 0.9;
        }
      }

      .el-form-item__content {
        line-height: 1.5;
      }
    }

    .yzm {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      flex-direction: row;

      .el-input {
        width: 202px;
      }

      .yzm-pic {
        width: 120px;
        height: 52px;
        margin-left: 10px;
        cursor: pointer;

        .img {
          width: 100%;
          height: 100%;
          display: block;
          border-radius: 10px;
          border: 1px solid #dcdef6;
          box-sizing: border-box;
        }
      }
    }
  }

  .m-forget {
    display: block;
    padding: 30px;
    box-sizing: border-box;
    height: 100%;
    overflow: auto;
  }
}
// 退款物品
.m-refund-good {
  padding: 10px 12px;
  border-radius: 4px;
  border: 1px solid #dcdfe6;
  box-sizing: border-box;
  line-height: normal;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  margin-right: 0;

  .el-checkbox__label {
    flex: 1;
    overflow: hidden;
    padding-left: 12px;
  }

  .tit {
    font-size: 15px;

    i {
      color: $base;
    }
  }

  .tag {
    display: flex;
    flex-wrap: wrap;

    .el-tag {
      margin-right: 5px;
      margin-top: 5px;
    }
  }

  .bottom {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-top: 5px;

    .price {
      flex: 1;
    }
  }

  .status {
    position: absolute;
    height: 30px;
    line-height: 30px;
    text-align: center;
    transform: rotate(45deg);
    right: -30px;
    top: 6px;
    background: rgba($important, 0.2);
    color: $important;
    width: 100px;
    font-size: 12px;
  }

  &.is-checked {
    border-color: #1f86f0;

    .tit,
    .bottom {
      color: #606266;
    }
  }

  &.is-disabled {
    .tit {
      i {
        opacity: 0.6;
      }
    }

    .tag {
      opacity: 0.6;
    }
  }
}
// 退款物品组合
.m-refund-good-group {
  display: flex;
  flex-direction: column;
}
// 批量退款
.m-refund-bat {
  display: flex;
  flex-direction: column;
  height: 100%;

  .hd {
    min-height: 0;
  }

  .bd {
    flex: 1;
    overflow: auto;
  }
}

//名单列表
.m-name-list {
  .item {
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    border-bottom: 1px dashed #eee;
    margin-bottom: 10px;

    .num {
      width: 24px;
      height: 24px;
      background-color: rgba($base, 0.1);
      color: $base;
      font-size: 13px;
      border-radius: 5px;
      margin-right: 20px;
      text-align: center;
      line-height: 24px;
    }

    .name {
      margin-right: 20px;
      min-width: 60px;
    }
  }
}
// 显示20字符
.m-online-20 {
  display: flex;
  align-items: center;

  .con {
    display: inline-block;
    width: 20em;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

//按钮定位到右侧
.m-btn-right {
  text-align: right;
  margin-bottom: -35px !important;
  position: relative;
  z-index: 9;

  & + .m-tab-card {
    .el-tabs__nav {
      z-index: 10;
    }
  }
}
// 培训期别
.m-farewell {
  .item {
    border: 1px solid #ebeef5;
    border-radius: 5px;
    margin-bottom: 20px;
    box-sizing: border-box;

    .item-tit {
      background-image: linear-gradient(to right, #c6ddf4, #fff);
      height: 56px;
      position: relative;
      font-size: 18px;
      font-weight: bold;
      display: flex;
      align-items: center;

      .tit {
        padding-right: 65px;
        padding-left: 20px;
        line-height: 20px;
        display: flex;
        align-items: center;

        .txt {
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          flex: 1;
        }

        .el-tag {
          padding: 0 8px;
          height: 20px;
          line-height: 20px;
          margin-left: 8px;
          font-weight: normal;
          border-radius: 0 10px 0 10px;
          font-size: 14px;
          border: 0;

          &.el-tag--success {
            background-color: #5ead37;
            color: #fff;
          }

          &.el-tag--warning {
            background-color: #e89821;
            color: #fff;
          }

          &.el-tag--info {
            background-color: #aaa;
            color: #fff;
          }
        }
      }

      .num {
        position: absolute;
        right: -1px;
        top: -1px;
        width: 54px;
        height: 36px;
        background-color: $base;
        color: #fff;
        border-radius: 0 5px 0 20px;
        text-align: center;
        line-height: 36px;
      }
    }

    .item-hd {
      padding-left: 20px;
      padding-right: 20px;
      height: 220px;
      overflow: hidden;

      .ele {
        box-sizing: border-box;
        padding-left: 16px;
        position: relative;
        padding-top: 15px;

        &::before {
          content: "";
          display: inline-block;
          width: 6px;
          height: 6px;
          border-radius: 6px;
          background-color: rgba($base, 0.8);
          position: absolute;
          left: 0;
          top: 22px;
        }

        &.half {
          width: 50%;
        }

        .ele-t {
          color: #999;
        }

        .ele-info {
          margin-top: 5px;
        }
      }
    }

    .item-bd {
      text-align: center;
      border-top: 1px solid #ebeef5;
      background-image: linear-gradient(to bottom, #fafcff, #f0f4fa);
      height: 56px;
      padding-left: 20px;
      padding-right: 20px;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .btn-icon {
        font-size: 16px;
        cursor: pointer;
        color: $base;

        &.el-icon-delete {
          color: #999;

          &:hover {
            color: $important;
          }
        }
      }

      .p-icon {
        background: url("../assets/images/farewell-icon.png") no-repeat 0 0;
        width: 16px;
        height: 16px;
        display: inline-block;
        vertical-align: middle;

        &.icon-2 {
          background-position: -16px 0;
        }

        &.icon-3 {
          background-position: -32px 0;
        }

        &.icon-4 {
          background-position: -48px 0;
        }
      }

      .btn-icon + .btn-icon {
        margin-left: 15px;
      }
    }
  }

  .add-btn {
    border: 1px dashed #e6e6e6;
    color: #bcc5ce;
    border-radius: 5px;
    width: 100%;
    height: 334px;
    margin-bottom: 20px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .el-icon-plus {
      font-size: 80px;
      font-weight: bold;
    }

    .txt {
      font-size: 16px;
      margin-top: 10px;
    }

    &:hover {
      border: 1px dashed $base;
      color: $base;
      cursor: pointer;
    }
  }
}
//问卷配置
.m-questionnaire-set {
  background-color: #f8f8f8;
  border-radius: 5px;
  padding: 20px;
  border: 1px solid #eee;
  position: relative;

  .is-text .el-form-item__content {
    margin-top: 0;
  }

  .el-radio .el-radio__label,
  .el-checkbox .el-checkbox__label {
    white-space: break-spaces;
    display: inline-block;
    vertical-align: top;
    padding-right: 10px;
  }

  .num {
    background-color: $base;
    color: #fff;
    width: 40px;
    height: 20px;
    line-height: 20px;
    position: absolute;
    left: -1px;
    top: -1px;
    text-align: center;
    font-size: 14px;
    border-radius: 0 0 10px 0;
  }

  .big-label {
    background-color: $important;
    color: #fff;
    width: 56px;
    height: 30px;
    line-height: 30px;
    position: absolute;
    right: -1px;
    top: -1px;
    text-align: center;
    font-size: 14px;
    border-radius: 0 5px 0 20px;
    z-index: 5;
    cursor: pointer;

    &.big-label-2 {
      background-color: #fdebeb;
      color: $important;
    }
  }

  .bottom {
    border-top: 1px dashed #e6e6e6;
    padding-top: 10px;
    text-align: right;
  }
}
// 侧边定位器
.m-side-positioner {
  position: fixed;
  right: 30px;
  top: 50%;
  transform: translateY(-50%);
  border-right: 1px solid #e6e6e6;
  z-index: 1000;

  .item {
    position: relative;
    cursor: pointer;
    padding: 16px 0;

    .dot {
      width: 9px;
      height: 9px;
      border-radius: 5px;
      background: #e6e6e6;
      position: absolute;
      right: -5px;
      top: 50%;
      margin-top: -5px;
    }

    .tit {
      background: #fff;
      border: 1px solid #eaeaea;
      height: 32px;
      line-height: 32px;
      border-radius: 16px;
      text-align: center;
      width: 100px;
      margin-right: 10px;
    }

    &:hover {
      .tit {
        color: $base;
      }
    }

    &.z-cur {
      .dot {
        background: $base;
      }

      .tit {
        background: $base;
        border-color: $base;
        color: #fff;
      }
    }
  }
}
// 增加一行
.m-add-row {
  border: 1px dashed #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f8f8;
  cursor: pointer;
  padding: 10px;
  color: $base;
  transition: all 0.3s ease;

  &:hover {
    border-color: $base;
    background: lighten($base, 45%);
  }
}
// 浅主色背景
.u-bg-light-base {
  background: lighten($base, 45%);
}
// 切换主体
.m-entity-sel {
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: lighten($base, 45%);;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    border: 1px solid transparent;

    .tit {
      flex: 1;
      padding-right: 10px;
    }

    .op {
      border-radius: 5px;
      padding: 2px 8px;
      color: $base;
      cursor: pointer;
      font-size: 12px;

      i {
        display: none;
        margin-right: 3px;
      }
    }

    &:hover {
      border-color: $base;
    }

    &.z-checked {
      border-color: $base;
      background: $base;

      .tit {
        color: #fff;
      }

      .op {
        background: $base;
        color: #fff;

        i {
          display: inline-block;
        }
      }
    }
  }

  .item + .item {
    margin-top: 10px;
  }
}
// 地图选择
.m-map-sel {
  .search {
    margin-bottom: 20px;

    .ipt {
      width: 540px;
      margin-right: 20px;
    }
  }

  .content {
    width: 628px;
    height: 400px;
    overflow: hidden;
    border: 1px solid #dcdfe6;
    background: #fafafa;
    border-radius: 3px;

    img {
      vertical-align: middle;
    }
  }
}
// 线下课程
.m-offline-course {
  .item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    background: #f5f7f9;
    padding: 15px;
    margin-top: 15px;

    .status {
      width: 100px;
    }

    .require {
      flex: 1;
    }

    .rate {
      min-width: 360px;
    }

    .op {
      min-width: 100px;
    }
  }
}
// tab-居中
.m-tab-center {
  position: sticky;
  top: 0;
  z-index: 100;

  .content {
    display: flex;
    align-items: center;
    justify-content: center;
    background: #fff;
    border-bottom: 1px solid #e4e7ed;

    .item {
      height: 48px;
      line-height: 48px;
      margin: 0 30px;
      border-bottom: 2px solid #fff;
      padding-top: 2px;
      cursor: pointer;
      font-size: 16px;

      &:hover {
        color: $base;
      }

      &.z-cur {
        border-color: $base;
        color: $base;
        font-weight: bold;
      }
    }
  }
}
// 查看二维码
.m-view-qrcode {
  .item {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    border: 1px solid #e6e6e6;
    border-radius: 8px;
    padding-bottom: 20px;
    margin-bottom: 15px;

    .content {
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      padding: 20px;
    }

    .tit {
      font-size: 18px;
      font-weight: bold;
      text-align: center;
    }

    .cate {
      font-size: 16px;
    }

    .date {
      text-align: center;
    }

    .code {
      width: 160px;
      height: 160px;

      img {
        width: 100%;
        height: 100%;
        vertical-align: middle;
      }
    }

    .op {
      margin-top: 10px;
    }

    &.z-no-border {
      border: none;
    }
  }
}
// 添加期别
.m-add-period {
  .section {
    border: 5px solid #eff2f5;
    overflow: hidden;
    margin-bottom: 20px;

    .m-tit.bg-gray {
      background: #eff2f5;
    }

    .m-tit.is-border-bottom {
      border-bottom: none;
    }
  }
}
// 教务管理
.m-edu-admin {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .section {
    flex: 1;
    height: 80px;
    border-radius: 5px;
    background: #f8fbfd;
    flex-direction: column;
    cursor: pointer;
    border: 1px solid #f8fbfd;
    position: relative;
    padding: 20px;
    margin: 0 10px;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    .title {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .icon {
        margin-right: 10px;
        font-size: 24px;
        color: $base;
      }

      .text {
        flex: 1;
        font-size: 18px;
        line-height: 32px;
        font-weight: bold;
      }
    }

    .content {
      padding-left: 35px;
      height: 60px;
      display: flex;
      justify-content: center;
      flex-direction: column;

      .item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 2px 0;

        .tit {
          width: 100px;
        }

        .con {
          flex: 1;
        }
      }
    }

    &:hover {
      border-color: lighten($base, 35%);

      .title {
        color: $base;
      }
    }

    &.z-cur {
      background: $base;
      border-color: $base;

      .title {
        .icon {
          color: #fff;
        }

        .text {
          color: #fff;
        }
      }

      .content {
        color: #fff;
      }
    }
  }
}
//问卷
.m-questionnaire {
  background: #1e90f3 url("../assets/images/questionnaire-bg.jpg") no-repeat center top;
  padding: 330px 0 0;

  .content {
    position: relative;
    z-index: 1;
    background-color: #1781e5;
    width: 1000px;
    margin: 0 auto;
    padding-bottom: 30px;
    border-radius: 0 0 5px 5px;
    min-height: calc(100vh - 442px);
    overflow: hidden;

    &::before {
      content: '';
      width: 100%;
      height: 600px;
      background-image: linear-gradient(to bottom, #56b6f8, #1781e5);
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
    }
  }

  .header {
    padding: 0 30px 15px;
    color: #fff;
    text-align: center;
    font-size: 40px;
    line-height: 1.2;
    font-weight: bold;
  }

  .questionnaire-bd {
    position: relative;
    width: 900px;
    margin: 0 auto;
    background-color: #fff;
    padding: 44px 30px 30px;
    box-sizing: border-box;
    border-radius: 0 0 5px 5px;

    &::before {
      content: '';
      height: 44px;
      width: 960px;
      background-image: linear-gradient(to bottom, #ffd54b 50%, #f8c10e 50%);
      position: absolute;
      top: 0;
      left: -30px;
      border-radius: 44px;
    }
  }

  .question {
    margin-top: 30px;

    .tit {
      display: flex;
      background-color: #e9f4fa;
      border-radius: 5px;
      padding: 8px 15px;

      .type {
        background-color: #df7676;
        color: #fff;
        border-radius: 100px;
        padding: 4px 10px;
        font-size: 12px;
        margin-right: 5px;
        vertical-align: 1px;
      }

      .text,
      .num {
        font-size: 15px;
        font-weight: bold;
      }

      .tag {
        color: $danger;
        margin-left: 10px;
        margin-top: 2px;
        font-size: 12px;

        .icon {
          font-size: 18px;
          vertical-align: -1px;
          margin-right: 5px;
        }
      }
    }

    .option {
      padding-left: 20px;
      padding-top: 10px;

      .el-radio,
      .el-checkbox {
        display: block;
        padding-top: 20px;

        .el-radio__label,
        .el-checkbox__label {
          font-size: 16px;
        }
      }

      .el-textarea {
        margin-top: 20px;
      }

      .u-underline {
        padding-bottom: 3px;
        border-bottom: 1px solid #666;
      }
    }
  }

  .submit {
    width: 160px;
    font-size: 16px;
  }

  .question-card {
    width: 100%;
    height: 152px;
    max-height: 452px;
    background: url("../assets/images/questionnaire-footer-bg1.png") no-repeat center top;
    position: sticky;
    bottom: 0;
    z-index: 9;
    margin-top: -70px;
    transition: all 0.4s;
    overflow: hidden;

    &.is-show {
      height: auto;
      min-height: 150px;
    }

    .btn {
      width: 140px;
      height: 60px;
      position: absolute;
      top: 58px;
      left: -98px;
      cursor: pointer;
    }

    .cont {
      width: 900px;
      margin: 0 auto;
      padding-top: 180px;
      padding-bottom: 10px;
      position: relative;

      .tips {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        margin-top: -20px;

        .item {
          margin-left: 30px;
        }

        .type {
          width: 14px;
          height: 14px;
          border-radius: 2px;
          display: inline-block;
          background-color: #fff;
          border: 1px solid #fff;
          margin-right: 5px;
          vertical-align: -2px;

          &.is-answered {
            background-color: $base;
            border-color: $base;
          }

          &.is-choose {
            border-color: $warning;
          }

          &.is-must {
            border-color: $important;
          }
        }
      }

      .nums {
        display: flex;
        flex-wrap: wrap;
        margin-top: 10px;
      }

      .num {
        width: 30px;
        height: 30px;
        text-align: center;
        line-height: 30px;
        margin-right: 10px;
        margin-bottom: 10px;
        background-color: #fff;
        border: 1px solid #fff;
        border-radius: 4px;
        cursor: pointer;

        &:hover {
          color: $base;
        }

        &.is-answered {
          background-color: $base;
          border-color: $base;
          color: #fff;
        }

        &.is-choose {
          border-color: $warning;
        }

        &.is-must {
          border-color: $important;
        }
      }
    }
  }
}
//公共业务属性-选择年度列表
.m-radio-border-list {
  max-height: 150px;
  overflow: hidden;
  overflow-y: scroll;
  padding-left: 10px;
  padding-bottom: 10px;

  .el-radio.is-bordered + .el-radio.is-bordered,
  .el-checkbox.is-bordered + .el-checkbox.is-bordered {
    margin-left: 0;
  }

  .el-radio.is-bordered,
  .el-checkbox.is-bordered {
    margin-right: 10px;
    margin-top: 10px;
    min-width: 101px;
  }
}
//公共业务属性-地区列表
.m-city-btn-list {
  max-height: 138px;
  overflow: hidden;
  overflow-y: scroll;
  padding-left: 10px;
  padding-bottom: 10px;

  .el-button + .el-button {
    margin-left: 0;
  }

  .el-button {
    margin-top: 10px;
    min-width: 101px;
    margin-right: 10px;

    .el-icon-arrow-right {
      margin-left: 3px;
    }
  }
}
//行业属性弹窗-单选控件列表
.m-attribute-select {
  width: 100%;

  .el-radio {
    border: 1px solid #e4e7ed;
    border-radius: 3px;
    padding: 15px;
    margin-top: 20px;
    margin-right: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;

    .el-radio__label {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    .tit {
      width: 400px;
      display: inline-block;
      font-size: 15px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &.is-checked {
      border: 1px solid $base;
    }
  }
}
//行业属性弹窗-树形控件
.m-attribute-tree {
  font-size: 15px;
  overflow: auto;
  max-height: 525px;

  .el-tree-node__content {
    line-height: 35px;
    height: 35px;

    .el-tree-node__expand-icon {
      font-size: 15px;
    }

    .el-tree-node__label {
      font-size: 15px;
      font-weight: bold;
    }
  }

  .el-tree-node__children {
    .el-tree-node__label {
      font-weight: normal;
    }
  }
}
// 公用带标题带边框模块
.m-common-mod {
  .section {
    border: 1px solid #e6ebf1;
    margin-bottom: 20px;
    border-radius: 6px;
    overflow: hidden;

    .title {
      height: 40px;
      line-height: 40px;
      background: #f2f6fb;
      color: $base;
      text-align: center;
      font-weight: bold;
    }
  }
}
// 自定折叠面板
.m-collapse-custom {
  .el-collapse-item__header {
    font-size: 15px;
    font-weight: bold;

    &.is-active {
      color: $base;
    }
  }
}
