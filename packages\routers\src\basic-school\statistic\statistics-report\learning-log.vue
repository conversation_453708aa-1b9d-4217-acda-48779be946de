<route-meta>
{
"isMenu": true,
"hideMenu": true,
"title": "学习日志",
"sort": 99,
"icon": "icon-ribaotongji"
}
</route-meta>

<script lang="ts">
  import LearningLog from '@hbfe/jxjy-admin-statisticsReport/src/learning-log.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import {
    // 施教机构管理员
    WXGLY,
    ZTGLY
  } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    userInfo: [WXGLY,ZTGLY],
    queryInfo: [WXGLY,ZTGLY]
  })
  export default class extends LearningLog {}
</script>
