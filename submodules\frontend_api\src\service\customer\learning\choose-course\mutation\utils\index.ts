import WaitChooseCourseTreeCacheVo from '@api/service/customer/learning/choose-course/mutation/vo/WaitChooseCourseTreeCacheVo'
import ChooseCourseVo from '../vo/ChooseCourseVo'

class Utils {
  /**
   * 根据提供的outlineId、outlineParentId递归查询树节点
   * @param {Array<WaitChooseCourseTreeCacheVo>} tree 本地缓存的树
   * @param {string} nodeId 节点id
   * @param {string} key outlineId、outlineParentId
   * @return
   */
  static getSomeOutlineTreeCourse(courseTree: Array<WaitChooseCourseTreeCacheVo>): Array<ChooseCourseVo> {
    const courseList = new Array<ChooseCourseVo>()
    courseTree?.map(treeItem => {
      courseList.push(...treeItem?.chooseCourseList?.filter(course => course.isSelected))
      if (treeItem.children?.length) {
        courseList.push(...this.getSomeOutlineTreeCourse(treeItem.children))
      }
    })
    if (courseList.length) {
      return courseList
    }
    return courseList
  }

  static getOutlineTreeNode(
    tree: Array<WaitChooseCourseTreeCacheVo>,
    nodeId: string,
    key: string
  ): WaitChooseCourseTreeCacheVo {
    console.log(tree, 'tree')
    let currentTree = new WaitChooseCourseTreeCacheVo()
    tree?.forEach(treeItem => {
      if (treeItem[key] === nodeId) {
        currentTree = treeItem
      } else if (treeItem[key].includes(nodeId)) {
        currentTree = treeItem
      }
      if (!currentTree.outlineId && treeItem.children.length > 0) {
        currentTree = this.getOutlineTreeNode(treeItem.children, nodeId, key)
      }
      if (currentTree.outlineId) {
        return currentTree
      }
    })
    return currentTree
  }

  // static getSpliceList() {
  // todo 剔除数组元素的统一方法
  //
  // }

  // 处理小数点
  static accMul(curr: number, next: number) {
    let m = 0
    const s1 = curr.toString(),
      s2 = next.toString()
    try {
      m += s1.split('.')[1].length
    } catch (e) {
      //
    }
    try {
      m += s2.split('.')[1].length
    } catch (e) {
      //
    }
    return (Number(s1.replace('.', '')) * Number(s2.replace('.', ''))) / Math.pow(10, m)
  }
}

export default Utils
