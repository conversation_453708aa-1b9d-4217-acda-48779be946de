<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--查看补学规则的方案-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">查看补学规则的方案</el-button>
        <el-drawer
          title="查看补学规则的方案"
          :visible.sync="dialog2"
          :direction="direction"
          size="900px"
          :append-to-body="true"
          custom-class="m-drawer m-table-auto"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table">
              <el-table-column label="No." width="160">TradeCode21</el-table-column>
              <el-table-column label="培训方案名称" min-width="300" :show-overflow-tooltip="true">
                <template>任务名称任务名称任务名称任务名称任务名</template>
              </el-table-column>
              <el-table-column label="方案属性" min-width="240" header-align="center" fixed="right">
                <template>
                  <div>行业：人社行业</div>
                  <div>地区：福建省/福州市/鼓楼区</div>
                  <div>科目类型：公需科目</div>
                  <div>培训专业：-</div>
                  <div>培训年度：2023</div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt10 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>关闭</el-button>
          </div>
        </el-drawer>

        <!--查看学习规则的地区-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">查看学习规则的地区</el-button>
        <el-drawer
          title="查看学习规则的地区"
          :visible.sync="dialog1"
          :direction="direction"
          size="900px"
          :append-to-body="true"
          custom-class="m-drawer m-table-auto"
        >
          <div class="drawer-bd">
            <el-row :gutter="0" class="m-query">
              <el-form :inline="true" label-width="auto">
                <el-col :span="12">
                  <el-form-item label="地区">
                    <el-cascader
                      clearable
                      :options="cascader"
                      :props="{ checkStrictly: true }"
                      placeholder="请选择地区"
                    />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <el-alert type="warning" :closable="false" class="m-alert f-mb10">
              <div class="f-c6">包含地区的区县共 <span class="f-fb f-co">5</span> 个</div>
            </el-alert>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table">
              <el-table-column type="index" label="No." width="60"></el-table-column>
              <el-table-column label="省份" min-width="150">
                <template>福建省</template>
              </el-table-column>
              <el-table-column label="地市" min-width="150">
                <template>福州市</template>
              </el-table-column>
              <el-table-column label="区县" min-width="240">
                <template>
                  鼓楼区、马尾区、XX区
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt10 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>关闭</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: true,
        dialog2: false,
        dialog3: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
