<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="待审核" name="first">
        <div class="f-p15">
          <el-tabs v-model="activeName2" type="card" class="m-tab-card">
            <el-tab-pane label="学习心得" name="first">
              <el-card shadow="never" class="m-card f-mb15">
                <el-row :gutter="16" class="m-query is-border-bottom">
                  <el-form :inline="true" label-width="auto">
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="培训方案名称">
                        <el-select v-model="select" clearable filterable placeholder="请选择培训方案名称">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="主题">
                        <el-select v-model="select" clearable filterable placeholder="请选择主题">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="4">
                      <el-form-item label="学习心得类型">
                        <el-select v-model="select" clearable filterable placeholder="全部">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>

                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="提交时间">
                        <el-date-picker
                          v-model="form.date1"
                          type="datetimerange"
                          range-separator="至"
                          start-placeholder="起始时间"
                          end-placeholder="结束时间"
                        >
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="姓名">
                        <el-input v-model="input" clearable placeholder="请输入姓名" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="证件号">
                        <el-input v-model="input" clearable placeholder="请输入证件号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="4">
                      <el-form-item label="作答方式">
                        <el-select v-model="select" clearable filterable placeholder="全部">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="4">
                      <el-form-item label="审核方式">
                        <el-select v-model="select" clearable filterable placeholder="全部">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="4">
                      <el-form-item label="审核结果">
                        <el-select v-model="select" clearable filterable placeholder="全部">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="8" class="f-fr">
                      <el-form-item class="f-tr">
                        <el-button type="primary">查询</el-button>
                        <el-button>导出列表数据</el-button>
                        <el-button>批量导出学习心得</el-button>
                        <el-button>重置</el-button>
                        <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                        <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                  <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                    <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                    <el-table-column label="用户信息" min-width="220" fixed="left">
                      <template
                        >姓名：用户姓名
                        <br />
                        证件号：352203198812290022</template
                      >
                    </el-table-column>
                    <el-table-column label="培训方案名称" min-width="220">
                      <template>培训方案名称培训方案名称培训方案名称</template>
                    </el-table-column>
                    <el-table-column label="主题" min-width="220">
                      <template>课程名称课程名称课程名称课程名称</template>
                    </el-table-column>

                    <el-table-column label="学习心得类型" min-width="100" align="center">
                      <template>班级心得</template>
                    </el-table-column>
                    <el-table-column label="提交时间" min-width="240" align="center">
                      <template>2023-10-23 00:00:00至2023-11-11 23:59:59</template>
                    </el-table-column>
                    <el-table-column label="作答形式" min-width="100" align="center">
                      <template>提交附件</template>
                    </el-table-column>
                    <el-table-column label="审核方式" min-width="100" align="center">
                      <template>提交自动通过</template>
                    </el-table-column>
                    <el-table-column label="审核结果" min-width="60" align="center">
                      <template>60</template>
                    </el-table-column>

                    <el-table-column label="操作" width="120" align="center" fixed="right">
                      <template>
                        <el-button type="text" size="mini">详情</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <!--分页-->
                  <el-pagination
                    background
                    class="f-mt15 f-tr"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage4"
                    :page-sizes="[100, 200, 300, 400]"
                    :page-size="100"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="400"
                  >
                  </el-pagination>
                </el-row>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="研讨活动" name="second">
              <el-card shadow="never" class="m-card f-mb15">详见 0202_基础信息配置_栏目设置.vue</el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      <el-tab-pane label="已审核" name="second">
        <div class="f-p15">详见 0207_基础信息配置_移动学习.vue</div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        switch1: false,
        switch2: false,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          name1: '周一至周五 (09:00 - 12:00 14:00 - 17:30)',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
