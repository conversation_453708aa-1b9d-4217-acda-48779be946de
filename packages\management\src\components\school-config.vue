<template>
  <el-card shadow="never" class="m-card f-mb15 is-header-sticky">
    <div slot="header" class="">
      <span class="tit-txt">网校配置</span>
    </div>
    <!-- <el-collapse v-model="activeNames" accordion>
      <el-collapse-item name="2" class="m-collapse-item"> -->
    <div class="f-plr20 f-pt40">
      <el-row type="flex" justify="center" class="width-limit">
        <el-col :md="20" :lg="16" :xl="13">
          <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
          <el-form ref="schoolConfigForm" :model="schoolConfigData" label-width="auto" class="m-form" :rules="rules">
            <el-form-item label="网校域名：" required prop="domainNameType">
              <el-radio v-model="schoolConfigData.domainNameType" :label="1" border class="f-mr10"
                >华博统一域名</el-radio
              >
              <el-radio v-model="schoolConfigData.domainNameType" :label="2" border class="f-mr10"
                >业主自有域名</el-radio
              >
            </el-form-item>
            <el-form-item v-show="schoolConfigData.domainNameType == 1" label="网校域名：" prop="webDomain">
              <el-input
                v-model="schoolConfigData.webDomain"
                clearable
                placeholder="请输入网校域名（不含协议http或https)，例如www.XXX.com"
                class="form-l f-mr10"
              />
              <div class="f-co">注：域名确认后需技术部配合处理方可生效</div>
            </el-form-item>
            <el-form-item v-show="schoolConfigData.domainNameType == 2" label="网校域名：" prop="webDomain">
              <el-input
                v-model="schoolConfigData.webDomain"
                clearable
                placeholder="请输入网校域名（不含协议http或https)，例如www.XXX.com"
                class="form-l f-mr10"
              />
              <div class="f-co f-mt5 lh20">
                注：域名若使用业主自有的，请注意需完成工信部备案和华为云接入流程，<br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;若是https的域名，请确认是否已购买https证书。
              </div>
            </el-form-item>
            <el-form-item label="提供终端：" class="is-required" prop="provideWebService">
              <el-checkbox v-model="schoolConfigData.provideWebService" border class="f-mr10">PC端</el-checkbox>
              <el-checkbox v-model="schoolConfigData.provideH5Service" border class="f-mr10">移动端（H5）</el-checkbox>
            </el-form-item>
            <el-form-item v-if="schoolConfigData.provideH5Service" label="H5域名：" prop="H5Domain">
              <el-input
                v-model="schoolConfigData.H5Domain"
                clearable
                placeholder="请输入H5网校域名，例如www.XXX.com/h5"
                class="form-l"
              />
            </el-form-item>
            <el-form-item label="短信服务：" required prop="smsService">
              <el-radio-group v-model="schoolConfigData.smsService">
                <el-radio label="1" border class="f-mr10">名商通</el-radio>
                <el-radio label="2" border class="f-mr10">联麓</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="短信帐号：" prop="smsAccount">
              <el-input
                v-model="schoolConfigData.smsAccount"
                clearable
                placeholder="提供短信服务请在此输入短信帐号信息"
                class="form-l"
              />
            </el-form-item>
            <el-form-item label="短信密码：" prop="smsPassword">
              <el-input
                v-model="schoolConfigData.smsPassword"
                clearable
                placeholder="请在此输入短信帐号对应密码"
                class="form-l"
              />
            </el-form-item>
            <el-form-item label="服务期限：" required prop="servicePeriodModel">
              <el-radio v-model="schoolConfigData.servicePeriodModel" :label="1" border class="f-mr10"
                >长期培训</el-radio
              >
              <el-radio v-model="schoolConfigData.servicePeriodModel" :label="2" border class="f-mr10"
                >指定期限</el-radio
              >
            </el-form-item>
            <el-form-item
              v-if="schoolConfigData.servicePeriodModel == 2"
              label="到期时间："
              required
              prop="serviceOverTime"
            >
              <el-date-picker
                v-model="schoolConfigData.serviceOverTime"
                type="datetime"
                placeholder="请选择网校服务到期时间"
                class="form-l"
                value-format="yyyy-MM-dd HH:mm:ss"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label="友盟统计：" prop="cnzzMode">
              <el-radio v-model="schoolConfigData.cnzzMode" :label="0" border class="f-mr10" @change="handleCnzz"
                >默认</el-radio
              >
              <el-radio v-model="schoolConfigData.cnzzMode" :label="1" border class="f-mr10" @change="handleCnzz"
                >业主自有</el-radio
              >
            </el-form-item>
            <el-form-item v-if="schoolConfigData.cnzzMode == 1" label="业主自有统计值" prop="cnzzValue">
              <el-input v-model="schoolConfigData.cnzzValue" placeholder="请输入友盟统计代码（cnzz code )"></el-input>
            </el-form-item>
            <el-form-item required>
              <div slot="label" class="f-vm">
                完善信息页设置<el-tooltip effect="dark" placement="top" popper-class="m-tooltip"
                  ><i class="el-icon-info m-tooltip-icon f-co f-ml5"></i>
                  <div slot="content">
                    1.强制跳过完善信息页可能会导致网校某些业务逻辑无法正常闭环、字段信息缺失等风险，请在使用此功能前仔细评估后再修改！<br />2.当学员信息不全时，会在登录/报名后进入学习/打印培训证书三个环节触发完善信息页。<br />3.若在登录/报名班级环节不想触发完善信息机制，请选择第二个选项“强制跳过完善信息页面”。<br />4.该设置项在web端和移动端生效。
                  </div> </el-tooltip
                >：
              </div>
              <el-radio
                v-model="schoolConfigData.perfectInfoModel"
                :label="perfectInfoEnum.DOPERFECT"
                border
                class="f-mr10"
                >当学员信息不全时，强制触发完善信息页面</el-radio
              >
              <el-radio v-model="schoolConfigData.perfectInfoModel" :label="perfectInfoEnum.SKIPPERFECT" border
                >强制跳过完善信息页面</el-radio
              >
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
    <!-- </el-collapse-item>
    </el-collapse> -->
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="handleReJump">取消</el-button>
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </div>
  </el-card>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  import SchoolConfigModel from '@api/service/training-institution/online-school/base-models/SchoolConfigModel'
  import OnlineSchoolModule from '@api/service/training-institution/online-school/OnlineSchoolModule'
  import OnlineSchoolModel from '@api/service/training-institution/online-school/models/OnlineSchoolModel'
  import { debounce, bind } from 'lodash-decorators'
  import { perfectInfoEnum } from '@api/service/training-institution/online-school/enum/OnlineSchoolEnum'
  @Component
  export default class extends Vue {
    @Ref('schoolConfigForm') schoolConfigForm: any
    @Prop({
      required: true,
      default: () => new SchoolConfigModel()
    })
    schoolConfigData: SchoolConfigModel
    // 合约id
    @Prop({
      required: true,
      default: () => ''
    })
    id: string
    onlineSchoolObj: OnlineSchoolModule = new OnlineSchoolModule()
    activeNames: Array<string> = ['2']
    props = { multiple: true }
    rules = {
      domainNameType: [{ required: true, message: '请选择域名类型', trigger: 'change' }],
      webDomain: [{ required: true, validator: this.validateWebDomain, trigger: 'blur' }],
      // hbDomain: [{ required: true, message: '请输入web域名', trigger: 'blur' }],
      H5Domain: [{ required: true, validator: this.validateH5Domain, trigger: 'blur' }],
      smsAccount: [{ required: true, message: '请输入短信账号', trigger: 'blur' }],
      smsPassword: [{ required: true, message: '请输入短信账号密码', trigger: 'blur' }],
      servicePeriodModel: [{ required: true, message: '请选择服务期限', trigger: 'change' }],
      serviceOverTime: [{ required: true, message: '请选择到期时间', trigger: 'change' }],
      provideWebService: [{ required: true, validator: this.validateService, trigger: 'change' }],
      smsService: [{ required: true, message: '请选择短信服务商', trigger: 'change' }],
      cnzzMode: [{ required: true, message: '请选择友盟统计', trigger: 'change' }],
      cnzzValue: [{ required: true, message: '请输入业主自有统计值', trigger: 'blur' }]
    }
    /**
     * 完善信息页设置枚举
     */
    perfectInfoEnum = perfectInfoEnum
    //PC、H5域名重名校验
    validateWebDomain(rule: any, value: any, callback: any) {
      const str = this.schoolConfigData.webDomain
      // 判断大小写正则
      const regex1 = new RegExp('http', 'i')
      const regex2 = new RegExp('https', 'i')
      if (!value) {
        callback(new Error('请输入web域名'))
      } else if (regex1.test(str) || regex2.test(str)) {
        callback(new Error('网校域名格式有误，且无法开通成功'))
      } else if (this.schoolConfigData.webDomain == this.schoolConfigData.H5Domain) {
        callback(new Error('网校域名和H5域名重复，请调整后再保存'))
      } else {
        callback()
      }
    }
    //PC、H5域名重名校验
    validateH5Domain(rule: any, value: any, callback: any) {
      if (!value) {
        callback(new Error('请输入H5域名'))
      } else if (this.schoolConfigData.webDomain == this.schoolConfigData.H5Domain) {
        callback(new Error('网校域名和H5域名重复，请调整后再保存'))
      } else {
        callback()
      }
    }
    //终端选择验证
    validateService(rule: any, value: any, callback: any) {
      if (!this.schoolConfigData.provideWebService && !this.schoolConfigData.provideH5Service) {
        callback(new Error('请至少选择一个终端'))
      } else {
        callback()
      }
    }

    @bind
    @debounce(200)
    async handleSubmit() {
      const schoolConfigData: any = await this.handleCheck()
      if (!schoolConfigData.status) {
        this.$alert(schoolConfigData.msg, '提示', {
          type: 'warning'
        })
        return false
      }
      this.onlineSchoolObj.onlineSchool = new OnlineSchoolModel()
      this.onlineSchoolObj.onlineSchool.id = this.id
      this.onlineSchoolObj.onlineSchool.schoolConfig = schoolConfigData.data
      if (!this.onlineSchoolObj.onlineSchool.schoolConfig.H5PortalTemplateId) {
        this.$alert('网校模板未选择完整，请检查！', '提示', {
          type: 'warning'
        })
        return
      }
      const res = await this.onlineSchoolObj.updateSchoolConfig()
      if (res.status.code == 200) {
        if (res.data.code && res.data.code != '200') {
          switch (res.data.code) {
            case '3000':
              this.$alert('网校到期时间需大于当前时间', '提示', {
                type: 'warning'
              })
              break
            default:
              this.$alert(res.data.message, '提示', {
                type: 'warning'
              })
              break
          }

          return
        }
        // this.$emit('success')
        this.$message.success('保存成功！')
      }
    }
    handleReJump() {
      this.$router.push('/school-management/management')
    }
    handleCnzz(data: string) {
      if (data == '1') {
        this.schoolConfigData.cnzzValue = ''
      } else if (data == '0') {
        this.schoolConfigData.cnzzValue = this.schoolConfigData.defaultCnzzValue
      }
    }
    handleCheck() {
      return new Promise((resolve) => {
        let result = {
          status: true,
          msg: '',
          data: {}
        }
        this.schoolConfigForm.validate((valid: any) => {
          if (valid) {
            this.schoolConfigData.provideSms = true
            result.data = this.schoolConfigData
          } else {
            result = {
              status: false,
              msg: '网校配置未填写完整，请检查！',
              data: {}
            }
            return false
          }
        })
        resolve(result)
      })
    }
  }
</script>

<style lang="scss" scoped>
  .el-collapse-item__header {
    font-size: 16px;
    font-weight: bold;
    padding-left: 20px;
    padding-right: 10px;
  }
</style>
