<template>
  <ul class="header-nav f-flex-sub" ref="topGuide">
    <li class="current-bg" v-show="menuList.length && hasTopActive" :style="currentSignPositionStyle"></li>
    <li
      class="nav-item"
      v-for="(menu, index) in menuList"
      :id="`top_guide_menu_item_${index}`"
      @click="toggleTopMenu(menu.router.path, index)"
      :key="menu.path"
      :style="menu.code === currentActiveTopMenu && !isStart ? { color: '#d35171' } : {}"
    >
      <i class="iconfont" :class="menu.meta.icon"></i>
      {{ menu.name }}
    </li>

    <!-- 开始指引弹窗 -->
    <div v-if="operationalDomainDialog" style="z-index: 150">
      <div class="m-guide-wrap m-guide-wrap-z-index" v-if="isShowStartDialog">
        <div class="wrap-bd middle-style">
          <div class="m-guide">
            <div class="tit">欢迎访问，开启网校管理</div>
            <img class="img" src="@design/admin/assets/images/guide-1.png" alt=" " />
            <div class="f-mt30">
              <el-button type="primary" @click="driverStartDialog = false">立即查看</el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 结束指引弹窗 -->
      <div class="m-guide-wrap m-guide-wrap-z-index" v-if="driverDoneDialog">
        <div class="wrap-bd middle-style">
          <div class="m-guide">
            <img class="img" src="@design/admin/assets/images/guide-2.png" alt=" " />
            <div class="f-mt30">
              <el-button type="primary" @click="closeDriverDoneDialog">开始使用</el-button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </ul>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import rootModule from '@/store/RootModule'
  import { isUndefined } from 'lodash'
  import Driver from 'driver.js'
  import 'driver.js/dist/driver.min.css'
  import UiModule from '@/store/UiModule'
  import guideData from '@hbfe/jxjy-admin-components/src/base-container/components/top-guide-data/index'
  import kjgysData from '@hbfe/jxjy-admin-components/src/base-container/components/top-guide-data/kjgys'
  import dqglyData from '@hbfe/jxjy-admin-components/src/base-container/components/top-guide-data/dqgly'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import ExecutionListInServicer from '@api/service/management/intelligence-learning/ExecutionListInServicer'
  import moment from 'moment'
  import { TaskStatusEnum } from '@api/service/management/intelligence-learning/enum/TaskStatusEnum'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'

  interface StyleInfo {
    width: number
    left: number
  }

  @Component
  export default class extends Vue {
    @Ref('topGuide')
    topGuide: HTMLDivElement

    widthMap: Array<StyleInfo> = new Array<StyleInfo>()

    get startGuide() {
      return UiModule.startGuide
    }

    //运营域不显示弹窗
    operationalDomainDialog = false
    // 指引弹窗显隐
    driverStartDialog = true
    driverDoneDialog = false
    isShowStartDialog1 = false
    isShowStartDialog2 = false
    isShowStartDialog3 = false
    isShowStartDialog4 = false
    isShowStartDialog5 = false
    isStart = false
    // 是否走完了一遍指引流程
    isFirstGuide = false

    userTypeInfo = AuthorityModule.roleFactory.getQueryCurrentUserRoleList()
    userType = ''
    userTypeNumber: number
    roleCode: string
    roleList: any

    @Watch('startGuide')
    guideWatch() {
      if (UiModule.startGuide) {
        this.driverStartDialog = true
        this.isFirstGuide = true
        UiModule.changeGuideState(false)
      }
    }

    async getUserTpyeInfo() {
      await this.userTypeInfo.getCurrentUserRoleList()
      const res = this.userTypeInfo.roleList
      const type = res.filter((item) => {
        return item.category != 5 && item.isBuiltIn
      })
      if (type.length) {
        this.userTypeNumber = type[0]?.category
        this.roleCode = type[0]?.roleCode
        this.operationalDomainDialog = !['ZXMGLY', 'ZTGLY'].includes(this.roleCode)
      } else {
        this.operationalDomainDialog = false
      }
    }

    async created() {
      this.isUserAlreadyDriver()
      await this.getUserTpyeInfo()
      // 因为现在会存在多角色的情况，超管和供应商使用同一套js模版进行引导
      if (
        this.userTypeNumber == 510 ||
        this.userTypeNumber == CategoryEnums.gys ||
        this.userTypeNumber == CategoryEnums.fxs
      ) {
        this.userType = 'admin'
      } else if (this.userTypeNumber == 1) {
        this.userType = 'student'
      } else if (this.userTypeNumber == 560) {
        this.userType = 'provider'
      } else if (this.userTypeNumber == 320) {
        this.userType = 'dqgly'
      }
      //   console.log(this.userType, '898')
    }

    // 是否展示开始弹窗Dialog
    get isShowStartDialog(): boolean {
      return this.driverStartDialog && this.isFirstGuide
    }

    get menuList() {
      return rootModule.menuList
    }

    get currentActiveTopMenu() {
      return rootModule.currentActiveTopMenu
    }

    get hasTopActive() {
      return isUndefined(rootModule.currentActiveTopMenuIndex) ? undefined : true
    }

    get currentSignPositionStyle() {
      return this.getInfo()
    }

    // 判断用户是否走过一遍指引流程
    isUserAlreadyDriver() {
      // 为true表示已经走完一遍指引
      const temp: boolean = localStorage.getItem('isFirstGuide') !== 'notFirstGuide'
      this.isFirstGuide = temp
    }

    toggleTopMenu(id: string, index: number) {
      rootModule.setCurrentTopMenu({
        id,
        index
      })
    }

    // 关闭结束指引弹窗Dialog
    async closeDriverDoneDialog() {
      this.driverDoneDialog = false
      this.isStart = false
      localStorage.setItem('isFirstGuide', 'notFirstGuide')
      // 判断是否是网校管理员并且存在弹窗权限
      // 硬编码，如果菜单发生变化，需要调整
      if (
        QueryManagerDetail.hasCategory(CategoryEnums.wxgly) &&
        this.$hasPermission('training.intelligent-learning.learning-task-tracking.ZNXXFB', true)
      ) {
        // 用户每次登录，无论上次登录是否处理，弹窗显示数量都为列表“终止”、“执行失败”的任务数量
        const res = await ExecutionListInServicer.queryCountByStatusAndTime({
          status: [TaskStatusEnum.fail, TaskStatusEnum.stop]
        })
        if (res.status.isSuccess() && res.data) {
          this.$showIntelligentLearningTaskFeedback.show({
            count: res.data
          })
        }
      }
    }

    getInfo() {
      const info = this.widthMap[rootModule.currentActiveTopMenuIndex]
      if (!info) {
        return {}
      }
      return {
        left: `${info.left}px`,
        width: `${info.width}px`
      }
    }

    initDriver(idList?: Array<string>) {
      const options = {
        allowClose: false, //禁止点击外部关闭
        keyboardControl: false, //禁止键盘关闭
        doneBtnText: '完成', // 完成按钮标题
        closeBtnText: '跳过', // 关闭按钮标题
        stageBackground: '#fff', // 引导对话的背景色
        nextBtnText: '下一步', // 下一步按钮标题
        prevBtnText: '上一步', // 上一步按钮标题
        onReset: (item: Driver.Element) => {
          setTimeout(() => {
            // 展示最后一步提示弹窗dialog
            this.driverDoneDialog = true
            this.isStart = false
          }, 300)
        }
      }

      const driver = new Driver(options)
      const defineSteps: Array<Driver.Step> = new Array<Driver.Step>()
      //   idList.forEach((id: string) => {
      if (this.userType == 'admin') {
        defineSteps.push(...guideData)
      } else if (this.userType == 'provider') {
        defineSteps.push(...kjgysData)
      } else if (this.userType == 'dqgly') {
        defineSteps.push(...dqglyData)
      }
      //   })
      driver.defineSteps(defineSteps)
      return driver
      // driver.start()
    }

    @Watch('menuList', {
      immediate: true
    })
    someChange() {
      this.$nextTick(() => {
        setTimeout(() => {
          const idList: Array<string> = []
          Array.from(this.topGuide.children).forEach((children: HTMLDivElement, index: number) => {
            idList.push(children.getAttribute('id'))
            if (!index) return
            this.widthMap.push({
              width: children.offsetWidth,
              left: children.offsetLeft
            })
          })
          this.initDriver(idList)
        }, 500)
      })
    }

    // 开始指引弹窗结束，开始新手指引步骤
    @Watch('driverStartDialog', {
      deep: true
    })
    driverChange(val: boolean) {
      if (!val) {
        const startDiver = this.initDriver()
        this.isStart = true
        startDiver.start()
      }
    }
  }
</script>

<style lang="scss">
  .m-guide-step {
    margin-bottom: none !important;
  }

  .m-guide-step .hd {
    color: #404040;
  }

  .m-guide-step .bd {
    color: #3d3d3d;
  }

  .current-bg {
    transition: all 0.3s;
    left: 0;
  }

  .nav-item {
    transition: color 0.5s;
  }

  .middle-style {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .m-guide-wrap-z-index {
    z-index: 1001;
  }

  div#driver-popover-item {
    max-width: 460px;
    padding: 8px;

    .driver-popover-description {
      .m-guide-step {
        margin-bottom: 0 !important;
      }
    }

    .driver-popover-title {
      margin-left: 58px;
    }

    .driver-popover-description {
      display: flex;
      align-items: center;
    }

    div.driver-popover-footer {
      display: flex !important;
      justify-content: right;
      //   flex-direction: row-reverse;
      //   margin-left: 70%;
      button.driver-close-btn {
        color: rgb(180, 180, 189);
        border: 0;
        background-color: #ffffff;
        // text-decoration: underline;
        font-size: 14px !important;
      }

      .driver-btn-group {
        display: flex;
        justify-content: right;

        button.driver-prev-btn {
          display: none !important;
        }

        button.driver-next-btn {
          color: rgb(51, 51, 230);
          border: 0;
          background-color: #ffffff;
          //   text-decoration: underline;
          font-size: 14px !important;
        }
      }
    }

    // 修改网校引导的背景色
    div#driver-highlighted-element-stage {
      background: #1f86f0 !important;
      color: #fff !important;
    }
  }
</style>
