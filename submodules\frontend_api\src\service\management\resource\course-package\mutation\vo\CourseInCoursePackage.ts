class CourseInCoursePackage {
  id = ''
  // 名称
  name = ''
  // 学时
  period: number = null
  // 物理学时
  physicsPeriod: number = null
  // 排序
  sort: number = null
  // 创建时间
  createTime = ''
  // 课程包id
  coursePackageId = ''
  // 课程包名称
  coursePackageName = ''
  // 课件供应商id
  providerId = ''
  // * 课件供应商名称
  providerName = ''
  // 课程被勾选
  isChecked = false
  // 课程分类信息（仅ui使用）
  courseCategoryInfo: string[] = []
}

export default CourseInCoursePackage
