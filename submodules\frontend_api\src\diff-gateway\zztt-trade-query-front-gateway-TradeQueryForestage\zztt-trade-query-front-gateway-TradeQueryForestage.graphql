"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取我购买的订单的退货单分页查询"""
	pageReturnOrderInMyself(page:Page,request:ReturnOrderRequest,sort:[ReturnSortRequest]):ReturnOrderResponsePage @page(for:"ReturnOrderResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input BigDecimalScopeRequest @type(value:"com.fjhb.ms.query.commons.BigDecimalScopeRequest") {
	begin:BigDecimal
	end:BigDecimal
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input CommodityAuthInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommodityAuthInfoRequest") {
	distributorId:String
	distributionLevel:Int
	superiorDistributorId:String
	supplierId:String
	salesmanId:String
}
input CommoditySkuRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommoditySkuRequest") {
	commoditySkuIdList:[String]
	saleTitle:String
	issueInfo:IssueInfo
	skuProperty:SkuPropertyRequest
	externalTrainingPlatform:[String]
	trainingInstitution:[String]
}
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertySearchRequest") {
	regionSearchType:Int
	region:[RegionSkuPropertyRequest]
}
input SkuPropertyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.SkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	trainingObject:[String]
	positionCategory:[String]
	jobLevel:[String]
	jobCategory:[String]
	grade:[String]
	subject:[String]
	learningPhase:[String]
	discipline:[String]
	trainingChannelIds:[String]
	certificatesType:[String]
	practitionerCategory:[String]
	qualificationCategory:[String]
	trainingForm:[String]
}
input IssueInfo @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.IssueInfo") {
	issueId:String
	issueName:String
	issueNum:String
	trainStartTime:DateTime
	trainEndTime:DateTime
	sourceType:String
	sourceId:String
}
input ReturnOrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.ReturnOrderRequest") {
	unitIdList:[String]
	returnOrderNoList:[String]
	basicData:ReturnOrderBasicDataRequest
	approvalInfo:ReturnOrderApprovalInfoRequest
	returnCommoditySkuIdList:[String]
	returnCommodity:CommoditySkuRequest
	refundCommoditySkuIdList:[String]
	refundCommodity:CommoditySkuRequest
	subOrderInfo:SubOrderInfoRequest
	commodityAuthInfo:CommodityAuthInfoRequest
	distributorId:String
	portalId:String
	isDistributionExcludePortal:Boolean
}
input ReturnSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.ReturnSortRequest") {
	field:ReturnOrderSortField
	policy:SortPolicy
}
input OrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.OrderInfoRequest") {
	orderNoList:[String]
	batchOrderNoList:[String]
	buyerIdList:[String]
	receiveAccountIdList:[String]
	flowNoList:[String]
	channelTypesList:[Int]
	terminalCodeList:[String]
	saleChannel:Int
	saleChannels:[Int]
	saleChannelName:String
	saleChannelIds:[String]
	policyTrainingSchemeIdList:[String]
	declarationUnitCodeList:[String]
}
input ReturnCloseReasonRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnCloseReasonRequest") {
	closeTypeList:[Int]
}
input ReturnOrderApprovalInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderApprovalInfoRequest") {
	approveTime:DateScopeRequest
}
input ReturnOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderBasicDataRequest") {
	returnOrderStatus:[Int]
	returnOrderTypes:[Int]
	applySourceType:String
	applySourceIdList:[String]
	returnCloseReason:ReturnCloseReasonRequest
	returnStatusChangeTime:ReturnOrderStatusChangeTimeRequest
	refundAmountScope:BigDecimalScopeRequest
}
input ReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderStatusChangeTimeRequest") {
	applied:DateScopeRequest
	returnCompleted:DateScopeRequest
}
input SubOrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.SubOrderInfoRequest") {
	subOrderNoList:[String]
	orderInfo:OrderInfoRequest
	discountType:Int
	useDiscount:Boolean
}
type DiscountPolicyModel @type(value:"com.fjhb.ms.trade.query.common.model.DiscountPolicyModel") {
	discountPolicyId:String
	discountId:Int
}
type RegionModel @type(value:"com.fjhb.ms.trade.query.common.model.RegionModel") {
	regionId:String
	province:String
	city:String
	county:String
	path:String
}
type UserModel @type(value:"com.fjhb.ms.trade.query.common.model.UserModel") {
	userId:String
}
type BatchReturnOrderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.BatchReturnOrderResponse") {
	batchReturnOrderNo:String
	basicData:BatchReturnOrderBasicDataResponse
	batchOrderInfo:BatchOrderInfoResponse
	needManualApprove:Boolean
	approvalInfo:BatchReturnApprovalInfoResponse
	confirmUser:UserResponse
	refundInfo:RefundInfoResponse
}
type BatchOrderInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchOrderInfoResponse") {
	batchOrderNo:String
	paymentInfo:PaymentInfoResponse
	creator:UserResponse
}
type BatchReturnApprovalInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnApprovalInfoResponse") {
	approveStatus:Int
	approveResult:Int
	approveUser:UserResponse
	approveComment:String
	approveTime:DateTime
}
type BatchReturnOrderApplyInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderApplyInfoResponse") {
	applyUser:UserResponse
	reasonId:String
	reasonContent:String
	description:String
}
type BatchReturnOrderBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderBasicDataResponse") {
	batchReturnOrderType:Int
	refundAmount:BigDecimal
	returnOrderCount:Long
	batchReturnOrderStatus:Int
	batchReturnOrderStatusChangeTime:BatchReturnOrderStatusChangeTimeResponse
	applyInfo:BatchReturnOrderApplyInfoResponse
	closeReason:BatchReturnOrderCloseReasonResponse
	saleChannel:Int
	saleChannelId:String
	saleChannelName:String
	salePathList:[SalePathResponse]
}
type BatchReturnOrderCloseReasonResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderCloseReasonResponse") {
	closeType:Int
	cancelUser:UserResponse
	cancelReason:String
	confirmFailureMessage:String
}
type BatchReturnOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderStatusChangeTimeResponse") {
	created:DateTime
	confirmed:DateTime
	cancelApplying:DateTime
	returning:DateTime
	returnFailed:DateTime
	refundApplying:DateTime
	refundApplied:DateTime
	refunding:DateTime
	refundFailed:DateTime
	returned:DateTime
	refunded:DateTime
	returnedAndRefunded:DateTime
	returnCompleted:DateTime
	closed:DateTime
}
interface ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.ResourceResponse") {
	resourceType:String
}
type SchemeResourceResponse implements ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.SchemeResourceResponse") {
	schemeId:String
	schemeName:String
	period:BigDecimal
	schemeType:String
	resourceType:String
}
enum SortPolicy @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.SortPolicy") {
	ASC
	DESC
}
type CommodityAuthInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.CommodityAuthInfoResponse") {
	commodityAuthId:String
	distributorId:String
	distributorName:String
	distributionLevel:Int
	superiorDistributorId:String
	superiorDistributorName:String
	distributorIdPath:String
	supplierId:String
	supplierName:String
	salesmanId:String
	salesmanName:String
	distributorPhone:String
	distributorUnitCreditCode:String
	distributorPartnerType:Int
	supplierPartnerType:Int
}
type CommoditySkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.CommoditySkuPropertyResponse") {
	year:SkuPropertyResponse
	province:SkuPropertyResponse
	city:SkuPropertyResponse
	county:SkuPropertyResponse
	industry:SkuPropertyResponse
	subjectType:SkuPropertyResponse
	trainingCategory:SkuPropertyResponse
	trainingProfessional:SkuPropertyResponse
	technicalGrade:SkuPropertyResponse
	trainingObject:SkuPropertyResponse
	positionCategory:SkuPropertyResponse
	jobLevel:SkuPropertyResponse
	jobCategory:SkuPropertyResponse
	grade:SkuPropertyResponse
	subject:SkuPropertyResponse
	learningPhase:SkuPropertyResponse
	discipline:SkuPropertyResponse
	certificatesType:SkuPropertyResponse
	practitionerCategory:SkuPropertyResponse
	qualificationCategory:SkuPropertyResponse
	trainingWay:SkuPropertyResponse
}
type CommoditySkuResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.CommoditySkuResponse") {
	commoditySkuId:String
	saleTitle:String
	commodityPicturePath:String
	price:BigDecimal
	originalPrice:BigDecimal
	enableSpecialPrice:Boolean
	showPrice:Boolean
	skuProperty:CommoditySkuPropertyResponse
	resource:ResourceResponse
	tppTypeId:String
	externalTrainingPlatform:String
	trainingInstitution:String
	issueInfo:IssueInfo1
}
type DiscountSchemeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.DiscountSchemeResponse") {
	specialPrice:BigDecimal
	discountPolicyList:[DiscountPolicyModel]
	discountType:Int
	hasEnabled:Boolean
}
type IssueInfo1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.IssueInfo") {
	issueId:String
	issueName:String
	issueNum:String
	trainStartTime:DateTime
	trainEndTime:DateTime
	sourceType:String
	sourceId:String
}
type OwnerInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.OwnerInfoResponse") {
	servicerId:String
	servicerName:String
}
type PaymentInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.PaymentInfoResponse") {
	payAmount:BigDecimal
	flowNo:String
	receiveAccountId:String
	paymentOrderType:Int
}
type PricingPolicyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.PricingPolicyResponse") {
	pricingPolicyId:String
	price:BigDecimal
	hasEnabled:Boolean
}
type RefundInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.RefundInfoResponse") {
	refundOrderNo:String
	refundOrderType:Int
	refundOrderStatus:Int
	refundOrderStatusChangeTime:RefundOrderStatusChangeTimeResponse
	refundFlow:String
	refundAmount:BigDecimal
	refundFailReason:String
	refundConfirmedTime:DateTime
}
type SalePathResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.SalePathResponse") {
	id:String
	fullPath:String
	currentPath:String
	currentPathLastCode:String
	currentPathLastType:Int
	isLast:Boolean
}
type UserResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.UserResponse") {
	userId:String
	userArea:RegionModel
	managementUnitRegionCode:RegionModel
	jobCategoryId:String
	professionalLevel:Int!
	jobCategoryName:String
}
type SkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.nested.SkuPropertyResponse") {
	skuPropertyValueId:String
	skuPropertyValueName:String
	skuPropertyValueShowName:String
}
enum ReturnOrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderSortField") {
	APPLIED_TIME
}
type OrderInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.OrderInfoResponse") {
	orderNo:String
	orderType:Int
	batchOrderNo:String
	channelType:Int
	terminalCode:String
	orderPaymentInfo:PaymentInfoResponse
	buyer:UserResponse
	creator:UserResponse
	saleChannel:Int
	saleChannelId:String
	saleChannelName:String
	policyTrainingSchemeIds:String
	declarationUnitCode:String
}
type RefundCommodityResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.RefundCommodityResponse") {
	quantity:BigDecimal
	commoditySku:CommoditySkuResponse
}
type RefundOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.RefundOrderStatusChangeTimeResponse") {
	waiting:DateTime
	refunding:DateTime
	refunded:DateTime
	failed:DateTime
}
type ReturnApprovalInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnApprovalInfoResponse") {
	approveStatus:Int
	approveResult:Int
	approveUser:UserResponse
	approveComment:String
	approveTime:DateTime
	cancelApproveTime:DateTime
}
type ReturnCloseReasonResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnCloseReasonResponse") {
	closeType:Int
	cancelUser:UserModel
	cancelReason:String
}
type ReturnCommodityResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnCommodityResponse") {
	quantity:BigDecimal
	commoditySku:CommoditySkuResponse
}
type ReturnOrderApplyInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderApplyInfoResponse") {
	applyUser:UserResponse
	reasonId:String
	reasonContent:String
	description:String
}
type ReturnOrderBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderBasicDataResponse") {
	returnOrderType:Int
	refundAmount:BigDecimal
	returnOrderStatus:Int
	returnOrderStatusChangeTime:ReturnOrderStatusChangeTimeResponse
	applyInfo:ReturnOrderApplyInfoResponse
	returnFailReason:String
	returnCloseReason:ReturnCloseReasonResponse
	applySourceType:String
	applySourceId:String
	saleChannel:Int
	saleChannelId:String
	saleChannelName:String
}
type ReturnOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderStatusChangeTimeResponse") {
	applied:DateTime
	cancelApplying:DateTime
	returning:DateTime
	returnFailed:DateTime
	refundApplying:DateTime
	refundApplied:DateTime
	refunding:DateTime
	refundFailed:DateTime
	returned:DateTime
	refunded:DateTime
	returnedAndRefunded:DateTime
	returnCompleted:DateTime
	closed:DateTime
}
type SubOrderInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.SubOrderInfoResponse") {
	subOrderNo:String
	exchanged:Boolean
	orderInfo:OrderInfoResponse
	quantity:BigDecimal
	discountSourceId:String
	discountType:Int
	useDiscount:Boolean
	saleChannel:Int
	discountScheme:DiscountSchemeResponse
	pricingPolicy:PricingPolicyResponse
	salePathList:[SalePathResponse]
	isExchangeIssue:Boolean
	amount:BigDecimal
	finalPrice:BigDecimal
}
"""退货单网关模型"""
type ReturnOrderResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.zztt.v1.kernel.geteway.response.ReturnOrderResponse") {
	"""退货单号"""
	returnOrderNo:String
	"""退货单基本信息"""
	basicData:ReturnOrderBasicDataResponse
	"""退货单是否需要审批"""
	needApprove:Boolean
	"""退货单审批信息"""
	approvalInfo:ReturnApprovalInfoResponse
	"""退款确认人"""
	confirmUser:UserResponse
	"""退货单关联退款单信息"""
	refundInfo:RefundInfoResponse
	"""退货商品信息"""
	returnCommodity:ReturnCommodityResponse
	"""退款商品信息"""
	refundCommodity:RefundCommodityResponse
	"""退货子订单信息"""
	subOrderInfo:SubOrderInfoResponse
	"""来源批次退货单信息"""
	batchReturnOrder:BatchReturnOrderResponse
	"""退货商品分销信息（仅分销订单的退货单有值）"""
	commodityAuthInfo:CommodityAuthInfoResponse
	"""归属信息"""
	ownerInfo:OwnerInfoResponse
	"""是否需要人工确认退款"""
	needConfirmRefund:Boolean
	"""退货单扩展信息
		key:courseType,华医部分退款
		value:1-专业课   2-公需课  3-都退
	"""
	ext:Map
}

scalar List
type ReturnOrderResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ReturnOrderResponse]}
