import { ResponseStatus } from '@hbfe/common'
import UpdateCourseVo from '@api/service/management/resource/course/mutation/vo/UpdateCourse'
import UpdateCourse from '@api/service/management/resource/course/mutation/dto/UpdateCourse'

class MutationUpdateCourse {
  updateCourseVo: UpdateCourseVo = new UpdateCourseVo()

  async doUpdate(): Promise<ResponseStatus> {
    if (!this.updateCourseVo.id) {
      return Promise.reject(new ResponseStatus(500, '请完善信息'))
    }
    UpdateCourse.from(this.updateCourseVo)
    const status = await UpdateCourse.from(this.updateCourseVo).save()
    return new ResponseStatus(status.code, status.message)
  }
}

export default MutationUpdateCourse
