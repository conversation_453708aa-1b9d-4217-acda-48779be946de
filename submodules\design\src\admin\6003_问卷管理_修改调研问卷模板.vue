<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">问卷管理</el-breadcrumb-item>
      <el-breadcrumb-item>修改调研问卷</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header">
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">问卷信息</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="问卷名称：" required>
                <el-input v-model="input" clearable placeholder="请输入问卷名称" />
              </el-form-item>
              <el-form-item label="问卷类型：" required>
                <el-radio-group>
                  <el-radio label="普通问卷"></el-radio>
                  <el-radio label="量表问卷"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="问卷说明：" required>
                <div class="rich-text">
                  <el-input type="textarea" :rows="10" v-model="form.desc" placeholder="请输入" />
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">配置试题<i class="f-ci f-fn f-f14">（鼠标长按试题可拖拽排序）</i></span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :span="20" class="f-pt30 f-pb30">
            <el-upload action="#" list-type="picture-card" class="m-pic-upload f-mb30">
              <div slot="default" class="upload-placeholder">
                <i class="el-icon-plus"></i>
                <p class="txt">添加试题</p>
              </div>
            </el-upload>
            <el-button type="primary" icon="el-icon-plus" plain>添加试题</el-button>
            <el-form label-width="70px" class="m-form">
              <div class="m-questionnaire-set f-clear f-mt20">
                <div class="num">001</div>
                <div class="big-label">必答</div>
                <el-form-item class="is-text">
                  <div slot="label" class="f-f16 f-cb">单选题</div>
                  <div class="f-f16">
                    请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。
                  </div>
                  <el-row :gutter="20">
                    <el-radio-group v-model="radio">
                      <el-col :span="24" class="f-mt10"
                        ><el-radio v-model="radio" label="1"
                          >单选题-备选项1请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。</el-radio
                        ></el-col
                      >
                      <el-col :span="24" class="f-mt10"
                        ><el-radio v-model="radio" label="2">单选题-备选项2</el-radio></el-col
                      >
                      <el-col :span="24" class="f-mt10"
                        ><el-radio v-model="radio" label="3">单选题-备选项3</el-radio></el-col
                      >
                      <el-col :span="24" class="f-mt10"
                        ><el-radio v-model="radio" label="4">单选题-备选项4</el-radio></el-col
                      >
                    </el-radio-group>
                  </el-row>
                </el-form-item>
                <div class="bottom">
                  <el-button icon="el-icon-edit" size="mini" type="primary" plain>编辑</el-button>
                  <el-button icon="el-icon-delete" size="mini" type="primary" plain>删除</el-button>
                  <el-button icon="el-icon-upload2" size="mini" type="primary" plain>最前</el-button>
                  <el-button icon="el-icon-download" size="mini" type="primary" plain>最后</el-button>
                </div>
              </div>
              <div class="m-questionnaire-set f-clear f-mt20">
                <div class="num">002</div>
                <div class="big-label">必答</div>
                <el-form-item class="is-text">
                  <div slot="label" class="f-f16 f-cb">多选题</div>
                  <div class="f-f16">
                    请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。
                  </div>
                  <el-row :gutter="20">
                    <el-col :span="24" class="f-mt10">
                      <el-checkbox
                        >复选框A
                        请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。</el-checkbox
                      >
                    </el-col>
                    <el-col :span="24" class="f-mt10">
                      <el-checkbox>复选框 B</el-checkbox>
                    </el-col>
                    <el-col :span="24" class="f-mt10">
                      <el-checkbox>复选框 C</el-checkbox>
                    </el-col>
                    <el-col :span="24" class="f-mt10">
                      <el-checkbox>复选框 D</el-checkbox>
                    </el-col>
                  </el-row>
                </el-form-item>
                <div class="bottom">
                  <el-button icon="el-icon-edit" size="mini" type="primary" plain>编辑</el-button>
                  <el-button icon="el-icon-delete" size="mini" type="primary" plain>删除</el-button>
                  <el-button icon="el-icon-upload2" size="mini" type="primary" plain>最前</el-button>
                  <el-button icon="el-icon-download" size="mini" type="primary" plain>最后</el-button>
                </div>
              </div>
              <div class="m-questionnaire-set f-clear f-mt20">
                <div class="num">003</div>
                <div class="big-label">必答</div>
                <el-form-item class="is-text">
                  <div slot="label" class="f-f16 f-cb">问答题</div>
                  <div class="f-f16">
                    请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。
                  </div>
                  <el-row :gutter="20">
                    <div class="f-mt10 f-plr10">
                      <el-input type="textarea" :rows="10" v-model="form.desc" placeholder="请输入" />
                    </div>
                  </el-row>
                </el-form-item>
                <div class="bottom">
                  <el-button icon="el-icon-edit" size="mini" type="primary" plain>编辑</el-button>
                  <el-button icon="el-icon-delete" size="mini" type="primary" plain>删除</el-button>
                  <el-button icon="el-icon-upload2" size="mini" type="primary" plain>最前</el-button>
                  <el-button icon="el-icon-download" size="mini" type="primary" plain>最后</el-button>
                </div>
              </div>
              <div class="m-questionnaire-set f-clear f-mt20">
                <div class="num">004</div>
                <div class="big-label">必答</div>
                <el-form-item class="is-text">
                  <div slot="label" class="f-f16 f-cb">量表题</div>
                  <div class="f-f16">
                    请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。
                  </div>
                  <el-row :gutter="20">
                    <el-col :span="24" class="f-mt10">
                      <span class="f-mr30">非常不满意</span>
                      <el-radio v-model="radio" label="1" class="f-mr20">1</el-radio>
                      <el-radio v-model="radio" label="2" class="f-mr20">2</el-radio>
                      <el-radio v-model="radio" label="3" class="f-mr20">3</el-radio>
                      <el-radio v-model="radio" label="4" class="f-mr20">4</el-radio>
                      <el-radio v-model="radio" label="5" class="f-mr20">5</el-radio>
                      <span>非常满意</span>
                    </el-col>
                  </el-row>
                </el-form-item>
                <div class="bottom">
                  <el-button icon="el-icon-edit" size="mini" type="primary" plain>编辑</el-button>
                  <el-button icon="el-icon-delete" size="mini" type="primary" plain>删除</el-button>
                  <el-button icon="el-icon-upload2" size="mini" type="primary" plain>最前</el-button>
                  <el-button icon="el-icon-download" size="mini" type="primary" plain>最后</el-button>
                </div>
              </div>
              <div class="m-questionnaire-set f-clear f-mt20">
                <div class="num">005</div>
                <div class="big-label">必答</div>
                <el-form-item class="is-text">
                  <div slot="label" class="f-f16 f-cb">量表题</div>
                  <div class="f-f16">
                    请输入大题的标题，该信息考生可见。请不要输入题目数量和分数，因为系统会自动显示。
                  </div>
                  <el-row :gutter="20">
                    <el-col :span="24" class="f-mt10">
                      <span class="f-mr30">自定义名称A</span>
                      <el-radio v-model="radio" label="1" class="f-mr20">1</el-radio>
                      <el-radio v-model="radio" label="2" class="f-mr20">2</el-radio>
                      <el-radio v-model="radio" label="3" class="f-mr20">3</el-radio>
                      <el-radio v-model="radio" label="4" class="f-mr20">4</el-radio>
                      <el-radio v-model="radio" label="5" class="f-mr20">5</el-radio>
                      <span>自定义名称B</span>
                    </el-col>
                  </el-row>
                </el-form-item>
                <div class="bottom">
                  <el-button icon="el-icon-edit" size="mini" type="primary" plain>编辑</el-button>
                  <el-button icon="el-icon-delete" size="mini" type="primary" plain>删除</el-button>
                  <el-button icon="el-icon-upload2" size="mini" type="primary" plain>最前</el-button>
                  <el-button icon="el-icon-download" size="mini" type="primary" plain>最后</el-button>
                </div>
              </div>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button>取消</el-button>
        <el-button>预览问卷</el-button>
        <el-button>保存为草稿</el-button>
        <el-button type="primary">发布问卷</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        input1: '对',
        input2: '错',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
