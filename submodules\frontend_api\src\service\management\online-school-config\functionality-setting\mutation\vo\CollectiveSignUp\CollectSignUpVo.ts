import {
  OfflineCollectiveRegisterConfigSaveRequest,
  OnlineCollectiveRegisterConfigSaveRequest
} from '@api/ms-gateway/ms-servicer-series-v1'

class CollectSignUpVo {
  /**
   * 是否启用
   */
  enable = true
  /**
   * 报名模板
   */
  templatePath = ''
  /**
   * 报名模板名称
   */
  templateName = ''
  /**
   * 报名班级链接
   */
  signUpClassUrl = ''
  /**
   * 类型 线上/线下 OfflineCollectPaySignup
   */
  type = ''

  to(): OfflineCollectiveRegisterConfigSaveRequest | OnlineCollectiveRegisterConfigSaveRequest {
    return
  }

  from(res: any) {
    return
  }
}
export default CollectSignUpVo
