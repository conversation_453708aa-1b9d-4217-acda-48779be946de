import { RuleTypeEnum } from '@api/service/management/thematic-management/enum/RuleType'
export default class IndustryRule {
  constructor(industryId: string) {
    this.industryId = industryId
  }
  /**
   * 行业id
   */
  industryId = ''
  /**
   * 行业名称
   */
  industryName = ''
  /**
   * 是否选中
   */
  isChecked = false
  /**
   * 网校是否启用当前行业
   */
  isEnable = false
  /**
   * 标记类型
   */
  type: RuleTypeEnum = null
}
