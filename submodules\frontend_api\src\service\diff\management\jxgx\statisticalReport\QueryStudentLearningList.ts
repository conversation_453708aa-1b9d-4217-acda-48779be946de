import exportMsgatewayDiff from '@api/diff-gateway/jxgx-data-export-gateway-backstage'
import { StudentSchemeLearningRequestVo } from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
import QueryStudentLearningList from '@api/service/management/statisticalReport/query/QueryStudentLearningList'

export default class QueryStudentLearningListJxgx extends QueryStudentLearningList {
  totalSize = 0

  /**
   * 导出
   */
  async exportExcelDetail(param: StudentSchemeLearningRequestVo) {
    return await exportMsgatewayDiff.exportStudentSchemeLearningExcelInServicerForJxjy(param)
  }
}
