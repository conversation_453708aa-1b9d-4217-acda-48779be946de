import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-exam-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-exam-query-front-gateway-ExamQueryBackStage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum AnswerPaperSort {
  CREATE_TIME = 'CREATE_TIME',
  HANDED_TIME = 'HANDED_TIME'
}
export enum FillAnswerType {
  disarray = 'disarray',
  sequence = 'sequence',
  sequenceRelate = 'sequenceRelate'
}
export enum QuestionTypeEnum {
  RADIO = 'RADIO',
  MULTIPLE = 'MULTIPLE',
  FILL = 'FILL',
  OPINION = 'OPINION',
  ASK = 'ASK',
  FATHER = 'FATHER',
  SCALE = 'SCALE'
}
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 功能描述：时间范围查询条件
@Author： wtl
@Date： 2022/1/25 15:30
 */
export class DateScopeRequest {
  /**
   * 开始时间
查询大于等于开始时间的结果
   */
  beginTime?: string
  /**
   * 结束时间
查询小于等于结束时间的结果
   */
  endTime?: string
}

/**
 * 题库查询条件
 */
export class LibraryRequest {
  /**
   * 题库ID集合
   */
  libraryIdList?: Array<string>
  /**
   * 排除的题库ID集合
   */
  excludeLibraryIdList?: Array<string>
  /**
   * 题库名称
   */
  libraryName?: string
  /**
   * 父题库ID
   */
  parentLibraryId?: string
  /**
   * 是否可用
   */
  enabled?: boolean
}

/**
 * 出卷配置分类主题模型
 */
export class PaperPublishConfigureCategoryRequest {
  /**
   * 出卷配置分类ID
   */
  categoryIdList?: Array<string>
  /**
   * 分类名称
   */
  name?: string
  /**
   * 父级分类id
   */
  parentId?: string
}

/**
 * 出卷配置主题模型
 */
export class PaperPublishConfigureRequest {
  /**
   * 出卷配置ID
   */
  configureIdList?: Array<string>
  /**
   * 出卷配置分类ID
   */
  categoryIdList?: Array<string>
  /**
   * 出卷配置名称
   */
  name?: string
  /**
   * 创建时间开始
   */
  createTimeBegin?: string
  /**
   * 创建时间结束
   */
  createTimeEnd?: string
  /**
   * 出卷模式
   */
  paperPublishPatterns?: number
  /**
   * 是否启用 1 启用 2禁用
   */
  status?: number
  /**
   * 是否是草稿 1是  2不是
   */
  isDraft?: number
}

/**
 * 试题查询条件
 */
export class QuestionRequest {
  /**
   * 参训资格id
   */
  qualificationId?: string
  /**
   * 答卷ID
   */
  answerPaperId?: string
  /**
   * 试题ID集合
   */
  questionIdList?: Array<string>
  /**
   * 题库ID集合
   */
  libraryIdList?: Array<string>
  /**
   * 关联课程ID集合
   */
  relateCourseIds?: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes?: Array<string>
  /**
   * 试题题目
   */
  topic?: string
  /**
   * 试题类型（1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7：量表题）
@see QuestionTypeEnum
   */
  questionType?: number
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 是否启用
   */
  isEnabled?: boolean
  /**
   * 课程供应商id
   */
  courseSupplierId?: string
  /**
   * 查询范围（不传默认普通试题，不包含问卷试题）
0-普通试题
1-全部（包含普通试题和问卷试题）
2-问卷试题
   */
  queryScope?: number
}

/**
 * @Description （跟底层返回的一致）答卷信息
<AUTHOR>
@Date 15:19 2022/2/24
 */
export class AnswerPaperResponse {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属服务商id
   */
  servicerId: string
  /**
   * id
   */
  id: string
  /**
   * 答卷状态 发布中 1  取消发布 2  已发布 3
   */
  status: number
  /**
   * 试卷作答状态 未开始作答 -1  作答中0 交卷中 1 已交卷 2
   */
  answerStatus: number
  /**
   * 试卷阅卷状态  未交卷-1 阅卷中 0 阅卷完成 1
   */
  markStatus: number
  /**
   * 用户答卷评定结果常量 未评定 -1  无评定结果 0 合格 1 不合格 2
   */
  evaluateResult: number
  /**
   * 场景类型
   */
  sceneType: number
  /**
   * 场景id
   */
  sceneId: string
  qualificationId: string
  userId: string
  /**
   * 答题数
   */
  answerCount: number
  /**
   * 开始作答时间
   */
  beginAnswerTime: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 交卷时间
   */
  handingTime: string
  /**
   * 交卷完成时间
   */
  handedTime: string
  /**
   * 阅卷开始时间
   */
  markingTime: string
  /**
   * 阅卷完成时间
   */
  markedTime: string
  /**
   * 发布时间
   */
  pusblishedTime: string
  /**
   * 考试时长
   */
  timeLength: number
  /**
   * 阅卷人ID
   */
  markUserId: string
  /**
   * 得分，-1表示不是为分数评定方式
   */
  score: number
  /**
   * 正确题数，-1表示试题不进行评定
   */
  correctCount: number
  /**
   * 错误题数，-1表示试题不进行评定
   */
  incorrectCount: number
  studentNo: string
  name: string
  description: string
  /**
   * 总分
   */
  totalScore: number
  cancelReason: string
  createUserId: string
  systemHanded: boolean
  groups: Array<QuestionGroup>
  /**
   * 试题
   */
  questions: Array<Question>
  /**
   * 答卷时长
   */
  answerTimeLength: number
}

/**
 * 简答题主题模型
 */
export class AskQuestionResponse implements BaseQuestionResponse {
  /**
   * 试题标签，教师评价题专用
   */
  code: Array<string>
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 试题主题模型
 */
export interface BaseQuestionResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 课程试题数量
 */
export class CourseQuestionCountResponse {
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 试题数量
   */
  questionCount: number
}

/**
 * 考试答卷主题模型
 */
export class CourseQuizAnswerPaperResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 答卷ID
   */
  answerPaperId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 答卷基础信息
   */
  answerPaperBasicInfo: CourseQuizAnswerPaperBasicInfo
  /**
   * 答卷状态信息
   */
  answerPaperStateInfo: CourseQuizAnswerPaperStateInfo
  /**
   * 答卷时间信息
   */
  answerPaperTimeInfo: CourseQuizAnswerPaperTimeInfo
  /**
   * 答卷答题信息
   */
  answerPaperAnswerInfo: CourseQuizAnswerPaperAnswerInfo
  /**
   * 答卷评定信息
   */
  answerPaperMarkInfo: CourseQuizAnswerPaperMarkInfo
}

/**
 * 考试答卷主题模型
 */
export class ExaminationAnswerPaperResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 答卷ID
   */
  answerPaperId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 答卷基础信息
   */
  answerPaperBasicInfo: ExaminationAnswerPaperBasicInfo
  /**
   * 答卷状态信息
   */
  answerPaperStateInfo: ExaminationAnswerPaperStateInfo
  /**
   * 答卷时间信息
   */
  answerPaperTimeInfo: ExaminationAnswerPaperTimeInfo
  /**
   * 答卷答题信息
   */
  answerPaperAnswerInfo: ExaminationAnswerPaperAnswerInfo
  /**
   * 答卷评定信息
   */
  answerPaperMarkInfo: ExaminationAnswerPaperMarkInfo
}

/**
 * 父子题主题模型
 */
export class FatherQuestionResponse implements BaseQuestionResponse {
  /**
   * 子题集合
   */
  childQuestions: Array<ChildItem>
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 填空题主题模型
 */
export class FillQuestionResponse implements BaseQuestionResponse {
  /**
   * 填空数
   */
  fillCount: number
  /**
   * 正确答案
   */
  fillCorrectAnswer: FillAnswer
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 题库试题数量
 */
export class LibraryQuestionCountResponse {
  /**
   * 题库ID
   */
  libraryId: string
  /**
   * 试题数量
   */
  questionCount: number
}

/**
 * 题库主题模型
 */
export class LibraryResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 题库ID
   */
  libraryId: string
  /**
   * 题库名称
   */
  libraryName: string
  /**
   * 父题库信息
   */
  parentLibraryInfo: LibraryResponse
  /**
   * 题库描述
   */
  description: string
  /**
   * 是否可用
   */
  enabled: boolean
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 题库来源类型（1：创建 2：导入）
@see LibrarySourceTypes
   */
  sourceType: number
}

/**
 * 多选题主题模型
 */
export class MultipleQuestionResponse implements BaseQuestionResponse {
  /**
   * 可选答案列表
   */
  multipleAnswerOptions: Array<ChooseAnswerOption>
  /**
   * 正确答案ID集合
   */
  correctAnswerIds: Array<string>
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 判断题主题模型
 */
export class OpinionQuestionResponse implements BaseQuestionResponse {
  /**
   * 正确答案
   */
  opinionCorrectAnswer: boolean
  /**
   * 正确文本
   */
  correctAnswerText: string
  /**
   * 不正确文本
   */
  incorrectAnswerText: string
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 出卷配置分类主题模型
 */
export class PaperPublishConfigureCategoryResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 出卷配置分类ID
   */
  id: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 父级分类id
   */
  parentCategory: PaperPublishConfigureCategoryResponse
  /**
   * 排序
   */
  sort: number
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 出卷配置主题模型
 */
export class PaperPublishConfigureResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 出卷配置ID
   */
  id: string
  /**
   * 出卷配置名称
   */
  name: string
  /**
   * 数据归属信息
   */
  paperPublishConfigureCategory: PaperPublishConfigureCategoryResponse
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2
   */
  paperPublishPatterns: number
  /**
   * 出卷模式
   */
  publishPattern: PublishPattern
  /**
   * 是否启用 1 启用 2禁用
   */
  status: number
  /**
   * 适用范围 用于筛选自定义的分类
   */
  usageScope: number
  /**
   * 是否是草稿 1是  2不是
   */
  isDraft: number
}

/**
 * 课后测验答卷主题模型
 */
export class PracticeAnswerPaperResponse {
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 答卷ID
   */
  answerPaperId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 答卷基础信息
   */
  answerPaperBasicInfo: PracticeAnswerPaperBasicInfo
  /**
   * 答卷状态信息
   */
  answerPaperStateInfo: PracticeAnswerPaperStateInfo
  /**
   * 答卷时间信息
   */
  answerPaperTimeInfo: PracticeAnswerPaperTimeInfo
  /**
   * 答卷答题信息
   */
  answerPaperAnswerInfo: PracticeAnswerPaperAnswerInfo
  /**
   * 答卷评定信息
   */
  answerPaperMarkInfo: PracticeAnswerPaperMarkInfo
}

/**
 * 单选题主题模型
 */
export class RadioQuestionResponse implements BaseQuestionResponse {
  /**
   * 可选答案列表
   */
  radioAnswerOptions: Array<ChooseAnswerOption>
  /**
   * 正确答案ID
   */
  correctAnswerId: string
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 量表题主题模型
 */
export class ScaleQuestionResponse implements BaseQuestionResponse {
  /**
   * 量表类型
   */
  scaleType: number
  /**
   * 程度-始
   */
  startDegree: string
  /**
   * 程度-止
   */
  endDegree: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
  /**
   * 数据归属信息
   */
  dataBelong: DataBelongModel
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 父题库信息
   */
  libraryInfo: LibraryResponse
  /**
   * 试题题目
   */
  topic: string
  /**
   * 试题解析
   */
  dissects: string
  /**
   * 是否为子题
   */
  isChildQuestion: boolean
  /**
   * 父题ID（如果为子题）
   */
  parentQuestionId: string
  /**
   * 创建人Id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 试题来源类型（1：创建 2：导入）
@see QuestionSourceTypes
   */
  sourceType: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 试题是否可用
   */
  isEnabled: boolean
  /**
   * 试题难度（1：低难度 2：中等难度 3：高难度）
@see QuestionDifficulty
   */
  questionDifficulty: number
  /**
   * 关联课程ID集合
   */
  relateCourseIds: Array<string>
  /**
   * 关联工种ID集合
   */
  relateTagCodes: Array<string>
  /**
   * 关联课程
relateCourseIds 拓展
   */
  relateCourse: Array<RelateCourse>
}

/**
 * 数据归属模型
 */
export class DataBelongModel {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属服务商id
   */
  servicerId: string
}

/**
 * 智能卷出卷模式
 */
export class AutomaticPublishPattern implements PublishPattern {
  /**
   * 建议作答时长
   */
  suggestionTimeLength: number
  /**
   * 抽题规则
   */
  questionExtractRule: QuestionExtractRule
  /**
   * 评定方式
   */
  evaluatePattern: EvaluatePattern
  /**
   * 出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2
   */
  type: number
}

/**
 * 课后测验答题信息
 */
export class CourseQuizAnswerPaperAnswerInfo {
  /**
   * 作答时长
   */
  answerTimeLength: number
  /**
   * 答题总数
   */
  answerCount: number
  /**
   * 是否系统强制交卷
   */
  systemHanded: boolean
  /**
   * 考试时长，单位：秒，-1表示没有时长限制
   */
  examTimeLength: number
}

/**
 * 课后测验基础信息
 */
export class CourseQuizAnswerPaperBasicInfo {
  /**
   * 课后测验ID
   */
  sceneId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 试题总数
   */
  questionCount: number
  /**
   * 试卷总分 -1表示不以分数方式进行评定
   */
  totalScore: number
  /**
   * 及格分
   */
  qualifiedScore: number
}

/**
 * 课后测验评定信息
 */
export class CourseQuizAnswerPaperMarkInfo {
  /**
   * 阅卷人ID
   */
  markUserId: string
  /**
   * 得分，-1表示不是为分数评定方式
   */
  score: number
  /**
   * 正确题数，-1表示试题不进行评定
   */
  correctCount: number
  /**
   * 错误题数，-1表示试题不进行评定
   */
  incorrectCount: number
}

/**
 * 课后测验状态信息
 */
export class CourseQuizAnswerPaperStateInfo {
  /**
   * 答卷状态
   */
  answerPaperStatus: number
  /**
   * 答卷作答状态
   */
  answerPaperAnswerStatus: number
  /**
   * 答卷阅卷状态
   */
  answerPaperMarkStatus: number
  /**
   * 答卷评定结果
   */
  answerPaperEvaluateResults: number
}

/**
 * 课后测验时间信息
 */
export class CourseQuizAnswerPaperTimeInfo {
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 开始作答时间
   */
  answeringTime: string
  /**
   * 交卷时间
   */
  handingTime: string
  /**
   * 交卷完成时间
   */
  handedTime: string
  /**
   * 阅卷开始时间
   */
  markingTime: string
  /**
   * 阅卷完成时间
   */
  markedTime: string
}

/**
 * 答卷答题信息
 */
export class ExaminationAnswerPaperAnswerInfo {
  /**
   * 作答时长
   */
  answerTimeLength: number
  /**
   * 答题总数
   */
  answerCount: number
}

/**
 * 答卷基础信息
 */
export class ExaminationAnswerPaperBasicInfo {
  /**
   * 考试ID
   */
  sceneId: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 试题总数
   */
  questionCount: number
  /**
   * 试卷总分 -1表示不以分数方式进行评定
   */
  totalScore: number
  /**
   * 及格分
   */
  qualifiedScore: number
  /**
   * 考试场次名称
   */
  examSessionName: string
}

/**
 * 答卷评定信息
 */
export class ExaminationAnswerPaperMarkInfo {
  /**
   * 阅卷人ID
   */
  markUserId: string
  /**
   * 得分，-1表示不是为分数评定方式
   */
  score: number
  /**
   * 正确题数，-1表示试题不进行评定
   */
  correctCount: number
  /**
   * 错误题数，-1表示试题不进行评定
   */
  incorrectCount: number
}

/**
 * 答卷状态信息
 */
export class ExaminationAnswerPaperStateInfo {
  /**
   * 答卷状态
   */
  answerPaperStatus: number
  /**
   * 答卷作答状态
   */
  answerPaperAnswerStatus: number
  /**
   * 答卷阅卷状态
   */
  answerPaperMarkStatus: number
  /**
   * 答卷评定结果
   */
  answerPaperEvaluateResults: number
}

/**
 * 答卷时间信息
 */
export class ExaminationAnswerPaperTimeInfo {
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 开始作答时间
   */
  answeringTime: string
  /**
   * 交卷时间
   */
  handingTime: string
  /**
   * 交卷完成时间
   */
  handedTime: string
  /**
   * 阅卷开始时间
   */
  markingTime: string
  /**
   * 阅卷完成时间
   */
  markedTime: string
}

/**
 * 固定卷出卷模式
 */
export class FixedPaper implements PublishPattern {
  /**
   * 试卷id
   */
  id: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 作答时长
   */
  timeLength: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 大题集合
   */
  groups: Array<QuestionGroup>
  /**
   * 试题集合
   */
  questions: Array<PaperQuestion>
  /**
   * 出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2
   */
  type: number
}

/**
 * AB出卷模式
 */
export class MultipleFixedPaperPublishPattern implements PublishPattern {
  /**
   * 固定卷集合
   */
  fixedPapers: Array<FixedPaper>
  /**
   * 出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2
   */
  type: number
}

/**
 * <AUTHOR> create 2021/6/3 17:35
 */
export class PaperQuestion {
  /**
   * 试题ID
   */
  questionId: string
  /**
   * 分数，-1表示不为分数评定方式为
   */
  score: number
  /**
   * 所属大题序号，-1表示试卷没有使用大题
   */
  groupSequence: number
  /**
   * 是否必答
   */
  answerRequired: boolean
}

/**
 * 练习答题信息
 */
export class PracticeAnswerPaperAnswerInfo {
  /**
   * 作答时长
   */
  answerTimeLength: number
  /**
   * 答题总数
   */
  answerCount: number
}

/**
 * 练习基础信息
 */
export class PracticeAnswerPaperBasicInfo {
  /**
   * 练习ID
   */
  sceneId: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷描述
   */
  description: string
  /**
   * 试题总数
   */
  questionCount: number
}

/**
 * 练习评定信息
 */
export class PracticeAnswerPaperMarkInfo {
  /**
   * 阅卷人ID
   */
  markUserId: string
  /**
   * 得分，-1表示不是为分数评定方式
   */
  score: number
  /**
   * 正确题数，-1表示试题不进行评定
   */
  correctCount: number
  /**
   * 错误题数，-1表示试题不进行评定
   */
  incorrectCount: number
}

/**
 * 练习状态信息
 */
export class PracticeAnswerPaperStateInfo {
  /**
   * 答卷状态
   */
  answerPaperStatus: number
  /**
   * 答卷作答状态
   */
  answerPaperAnswerStatus: number
  /**
   * 答卷阅卷状态
   */
  answerPaperMarkStatus: number
  /**
   * 答卷评定结果
   */
  answerPaperEvaluateResults: number
}

/**
 * 练习时间信息
 */
export class PracticeAnswerPaperTimeInfo {
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 发布时间
   */
  publishedTime: string
  /**
   * 开始作答时间
   */
  answeringTime: string
  /**
   * 交卷时间
   */
  handingTime: string
  /**
   * 交卷完成时间
   */
  handedTime: string
}

/**
 * @Description 初级模式
<AUTHOR>
@Date 9:14 2022/3/1
 */
export interface PublishPattern {
  /**
   * 出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2
   */
  type: number
}

/**
 * 试卷大题信息
<AUTHOR>
 */
export class QuestionGroup {
  sequence: number
  questionType: number
  groupName: string
  eachQuestionScore: number
}

/**
 * 正确率评定方式
<AUTHOR>
 */
export class CorrectRateEvaluatePattern implements EvaluatePattern {
  /**
   * 要求答题总数
   */
  answerQuestionCount: number
  /**
   * 合格正确率
   */
  qualifiedCorrectRate: number
  /**
   * 评定方式类型
   */
  type: number
}

/**
 * 评定方式基类
<AUTHOR>
 */
export interface EvaluatePattern {
  /**
   * 评定方式类型
   */
  type: number
}

/**
 * 无评定方式
<AUTHOR>
 */
export class NoneEvaluatePattern implements EvaluatePattern {
  /**
   * 评定方式类型
   */
  type: number
}

/**
 * @Description试题指定分值设置
<AUTHOR>
@Date 15:15 2022/3/3
 */
export class QuestionMapScoreSetting {
  /**
   * 试题id
   */
  questionId: string
  /**
   * 分数
   */
  score: number
}

/**
 * 试题分数设置信息
<AUTHOR>
 */
export class QuestionScoreSetting {
  /**
   * 大题序号
   */
  sequence: number
  /**
   * 试题类型
   */
  questionType: number
  /**
   * 每题平均分
   */
  eachQuestionScore: number
  /**
   * 具体试题分数
   */
  questionScores: Array<QuestionMapScoreSetting>
}

/**
 * 分数评定方式
<AUTHOR>
 */
export class ScoreEvaluatePattern implements EvaluatePattern {
  /**
   * 总分
   */
  totalScore: number
  /**
   * 合格分数
   */
  qualifiedScore: number
  /**
   * 每道试题分数
   */
  questionScores: Array<QuestionScoreSetting>
  /**
   * 多选题漏选得分模式
   */
  multipleMissScorePattern: number
  /**
   * 评定方式类型
   */
  type: number
}

/**
 * 试题抽题规则
<AUTHOR>
 */
export class QuestionExtractRule {
  /**
   * 试题总数
   */
  questionCount: number
  /**
   * 出题范围
   */
  questionScopes: Array<QuestionScopeSetting>
  /**
   * 出题描述
   */
  questionExtracts: Array<QuestionExtractSetting>
}

/**
 * 试题抽题设置信息
<AUTHOR>
 */
export class QuestionExtractSetting {
  /**
   * 试题类型
   */
  questionType: number
  /**
   * 大题序号
   */
  sequence: number
  /**
   * 大题名称
   */
  groupName: string
  /**
   * 试题数
   */
  questionCount: number
  /**
   * 出题范围
   */
  questionScopes: Array<QuestionScopeSetting>
}

/**
 * 出题范围设置基类
<AUTHOR>
 */
export interface QuestionScopeSetting {
  /**
   * 出题类型
   */
  type: number
}

/**
 * 题库出题配置
<AUTHOR> create 2021/8/19 11:18
 */
export class LibraryFixedQuestionScopeSetting implements QuestionScopeSetting {
  /**
   * 题库对应试题数设置对象
   */
  libraryMapQuestionNumSettings: Array<LibraryMapQuestionNumSetting>
  /**
   * 出题类型
   */
  type: number
}

/**
 * @Description 题库对应试题数设置对象
<AUTHOR>
@Date 14:20 2022/3/3
 */
export class LibraryMapQuestionNumSetting {
  /**
   * 题库id
   */
  libraryId: string
  /**
   * 试题数量
   */
  questionNum: number
}

/**
 * 题库出题配置
<AUTHOR> create 2021/8/19 11:18
 */
export class LibraryQuestionScopeSetting implements QuestionScopeSetting {
  /**
   * 题库id集合
   */
  libraryIds: Array<string>
  /**
   * 出题类型
   */
  type: number
}

/**
 * 用户课程抽题维度
<AUTHOR> create 2021/11/26 13:55
 */
export class UserCourseScopeSetting implements QuestionScopeSetting {
  /**
   * 课程来源
@see UserCourseSources
   */
  userCourseSource: number
  /**
   * 要求的组卷信息key.
当{@link #userCourseSource} &#x3D; 用户课程题库时需要指定
@see ExtractionMessageKeys
   */
  requireKeys: Array<string>
  /**
   * 出题类型
   */
  type: number
}

/**
 * @Description
<AUTHOR>
@Date 17:13 2022/3/9
 */
export class Answer {
  key: number
  /**
   * 答案
   */
  answer: string
}

/**
 * @Description 问答题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class AskQuestion implements Question {
  /**
   * 答案
   */
  askAnswer: string
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * 父子题子题项
 */
export class ChildItem {
  /**
   * 子题序号
   */
  no: number
  /**
   * 子题ID
   */
  questionId: string
}

/**
 * 选择题答案选项
 */
export class ChooseAnswerOption {
  /**
   * 答案ID
   */
  id: string
  /**
   * 答案内容
   */
  content: string
  /**
   * 是否填空题
   */
  enableFillContent: boolean
  /**
   * 是否必填
   */
  mustFillContent: boolean
}

/**
 * 散乱无序填空题答案实体
<pre>
每空有多种匹配答案，空格答案不存在顺序关系： 比如：
试题题目：请写出中国四大银行__________、__________、__________、__________。
每空备选答案：
1/中国建设银行 建设银行 建行
2/中国银行 中行
3/中国工商银行 工商银行 工行
4/中国农业银行 农业银行 农行
学员答题答案：农行、工行、中行、建行；评卷为正确并得分；
</pre>
<AUTHOR>
 */
export class DisarrayFillAnswer implements FillAnswer {
  /**
   * 正确答案集合
   */
  disarrayCorrectAnswers: Array<string>
  /**
   * 填空题答案类型
   */
  type: FillAnswerType
}

/**
 * @Description 父子题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class FatherQuestion implements Question {
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * 填空题答案基类
 */
export interface FillAnswer {
  /**
   * 填空题答案类型
   */
  type: FillAnswerType
}

/**
 * 功能描述：填空
@Author： wtl
@Date： 2022/2/17 17:40
 */
export class FillCorrectAnswers {
  /**
   * 空格位置
   */
  blankNo: number
  /**
   * 答案备选项
   */
  answers: Array<string>
}

/**
 * @Description 填空题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class FillQuestion implements Question {
  /**
   * 答案
   */
  fillAnswer: Array<Answer>
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 多选题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class MultipleQuestion implements Question {
  /**
   * 答案
   */
  multipleAnswer: Array<string>
  /**
   * 填空的内容
   */
  fillContents: Array<MultipleQuestionFillContent>
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

export class MultipleQuestionFillContent {
  /**
   * 选项id
   */
  id: string
  /**
   * 填空内容
   */
  fillContent: string
}

/**
 * @Description 判断题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class OpinionQuestion implements Question {
  /**
   * 答案
   */
  opinionAnswer: boolean
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 试题
<AUTHOR>
@Date 16:07 2022/2/28
 */
export interface Question {
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * @Description 单选题和底层一样
<AUTHOR>
@Date 15:40 2022/2/28
 */
export class RadioQuestion implements Question {
  /**
   * 答案
   */
  radioAnswer: string
  /**
   * 需要填空的内容
   */
  fillContent: string
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * 试题关联课程
 */
export class RelateCourse {
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课程供应商id
   */
  courseSupplierId: string
}

/**
 * @Description 量表题
<AUTHOR>
@Date 15:48 2022/2/28
 */
export class ScaleQuestion implements Question {
  /**
   * 答案
   */
  answer: number
  /**
   * 试题类型
   */
  type: QuestionTypeEnum
  /**
   * 试题ID
   */
  questionId: string
  groupSequence: number
  /**
   * 总分
   */
  score: number
  answeredTime: string
  /**
   * 得分
   */
  givenScore: number
  evaluateResult: number
  evaluateMode: number
  /**
   * 试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题 7:量表题)
@see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   */
  questionType: number
  /**
   * 是否必答
true 必答
false 选答
   */
  answerRequired: boolean
  /**
   * 题目标签
   */
  tag: string
}

/**
 * 按序填空题答案实体
<pre>
每空有多种匹配答案且空格答案存在顺序关系： 比如：
试题题目：中国的政治中心是__________；中国的经济中心是__________。
试题答案： 1/北京北京市、2/上海上海市
学员答题答案：北京市、上海；评卷为正确并得分；
</pre>
<AUTHOR>
 */
export class SequenceFillAnswer implements FillAnswer {
  /**
   * 每个填空数答案
   */
  sequenceCorrectAnswers: Array<FillCorrectAnswers>
  /**
   * 填空题答案类型
   */
  type: FillAnswerType
}

/**
 * 按序关联填空题答案实体
<pre>
每空答案精确匹配： 适用于前后空格的答案是有关联的。比如：
试题题目：请写出中国四大名著之一是__________；作者是__________。
试题答案： 1/红楼梦曹雪芹、2/西游记吴承恩
学员答题答案：红楼梦、曹雪芹；评卷为正确并得分；
红楼梦、吴承恩；评卷则给第一个空得分；
</pre>
<AUTHOR>
 */
export class SequenceRelateFillAnswer implements FillAnswer {
  /**
   * 正确答案集合
   */
  sequenceRelateCorrectAnswers: Array<SequenceFillAnswer>
  /**
   * 填空题答案类型
   */
  type: FillAnswerType
}

export class CourseQuizAnswerPaperResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseQuizAnswerPaperResponse>
}

export class ExaminationAnswerPaperResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ExaminationAnswerPaperResponse>
}

export class LibraryResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LibraryResponse>
}

export class PaperPublishConfigureCategoryResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PaperPublishConfigureCategoryResponse>
}

export class PaperPublishConfigureResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PaperPublishConfigureResponse>
}

export class PracticeAnswerPaperResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PracticeAnswerPaperResponse>
}

export class BaseQuestionResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BaseQuestionResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取答卷详情
   * @param id 答卷id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getAnswerPaperRecordInServicer(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAnswerPaperRecordInServicer,
    operation?: string
  ): Promise<Response<AnswerPaperResponse>> {
    return commonRequestApi<AnswerPaperResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下简答题详情
   * @param questionId 试题ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param questionId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getAskQuestionInServicer(
    questionId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getAskQuestionInServicer,
    operation?: string
  ): Promise<Response<AskQuestionResponse>> {
    return commonRequestApi<AskQuestionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { questionId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下父子题详情
   * @param questionId 试题ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param questionId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getFatherQuestionInServicer(
    questionId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getFatherQuestionInServicer,
    operation?: string
  ): Promise<Response<FatherQuestionResponse>> {
    return commonRequestApi<FatherQuestionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { questionId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下填空题详情
   * @param questionId 试题ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param questionId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getFillQuestionInServicer(
    questionId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getFillQuestionInServicer,
    operation?: string
  ): Promise<Response<FillQuestionResponse>> {
    return commonRequestApi<FillQuestionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { questionId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下题库详情
   * @param libraryId 题库ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param libraryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getLibraryInServicer(
    libraryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getLibraryInServicer,
    operation?: string
  ): Promise<Response<LibraryResponse>> {
    return commonRequestApi<LibraryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { libraryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下多选题详情
   * @param questionId 试题ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param questionId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getMultipleQuestionInServicer(
    questionId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getMultipleQuestionInServicer,
    operation?: string
  ): Promise<Response<MultipleQuestionResponse>> {
    return commonRequestApi<MultipleQuestionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { questionId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下判断题详情
   * @param questionId 试题ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param questionId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getOpinionQuestionInServicer(
    questionId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOpinionQuestionInServicer,
    operation?: string
  ): Promise<Response<OpinionQuestionResponse>> {
    return commonRequestApi<OpinionQuestionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { questionId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下出卷配置分类详情
   * @param categoryId 出卷配置分类ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param categoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPaperPublishConfigureCategoryInServicer(
    categoryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPaperPublishConfigureCategoryInServicer,
    operation?: string
  ): Promise<Response<PaperPublishConfigureCategoryResponse>> {
    return commonRequestApi<PaperPublishConfigureCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { categoryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下智能卷出卷配置详情
   * @param configureId 出卷配置ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param configureId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPaperPublishConfigureInServicer(
    configureId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getPaperPublishConfigureInServicer,
    operation?: string
  ): Promise<Response<PaperPublishConfigureResponse>> {
    return commonRequestApi<PaperPublishConfigureResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { configureId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取网校下指定课程的试题数量
   * @param courseIdList 课程id集合
   * @param enable       试题状态，传入查询指定状态试题数量
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getQuestionCountByRelateCourseInServicer(
    params: { courseIdList?: Array<string>; enable?: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getQuestionCountByRelateCourseInServicer,
    operation?: string
  ): Promise<Response<Array<CourseQuestionCountResponse>>> {
    return commonRequestApi<Array<CourseQuestionCountResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取网校下指定题库的试题数量
   * @param libraryIdList 题库id集合
   * @param enable        试题状态，传入查询指定状态试题数量
   * @param questionType  试题类型(1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题)
   * @return
   * @see com.fjhb.domain.exam.api.question.consts.QuestionTypes
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getQuestionCountInServicer(
    params: { libraryIdList?: Array<string>; enable?: boolean; questionType?: number },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getQuestionCountInServicer,
    operation?: string
  ): Promise<Response<Array<LibraryQuestionCountResponse>>> {
    return commonRequestApi<Array<LibraryQuestionCountResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取子项目下试题数量
   * @param servicerId 网校ID，传入查询指定网校
   * @param enable     试题状态，传入查询指定状态试题数量
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getQuestionCountInSubProject(
    params: { servicerId?: string; enable?: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getQuestionCountInSubProject,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下单选题详情
   * @param questionId 试题ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param questionId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getRadioQuestionInServicer(
    questionId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRadioQuestionInServicer,
    operation?: string
  ): Promise<Response<RadioQuestionResponse>> {
    return commonRequestApi<RadioQuestionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { questionId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取子项目下学员课后测验分页列表
   * @param qualificationId   学员ID
   * @param answerPaperStatus 作答状态
   * @param answerPaperSort 排序类型
   * @param sort answerPaperSort排序方式 正序 逆序
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCourseQuizRecordInSubProject(
    params: {
      page?: Page
      courseId?: string
      qualificationId?: string
      answerPaperStatus?: number
      answerPaperSort?: AnswerPaperSort
      sort?: SortTypeEnum
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourseQuizRecordInSubProject,
    operation?: string
  ): Promise<Response<CourseQuizAnswerPaperResponsePage>> {
    return commonRequestApi<CourseQuizAnswerPaperResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取子项目下学员考试记录分页列表
   * @param qualificationId   学员ID
   * @param answerPaperStatus 作答状态
   * @param answerPaperSort 排序类型
   * @param sort answerPaperSort排序方式 正序 逆序
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageExaminationRecordInSubProject(
    params: {
      page?: Page
      qualificationId?: string
      answerPaperStatus?: number
      answerPaperSort?: AnswerPaperSort
      sort?: SortTypeEnum
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageExaminationRecordInSubProject,
    operation?: string
  ): Promise<Response<ExaminationAnswerPaperResponsePage>> {
    return commonRequestApi<ExaminationAnswerPaperResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下题库分页
   * @param page    分页对象
   * @param request 查询参数对象
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageLibraryInServicer(
    params: { page?: Page; request?: LibraryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageLibraryInServicer,
    operation?: string
  ): Promise<Response<LibraryResponsePage>> {
    return commonRequestApi<LibraryResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下出卷配置分类分页
   * @param request 出卷配置分类查询
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pagePaperPublishConfigureCategoryInServicer(
    params: { page?: Page; request?: PaperPublishConfigureCategoryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pagePaperPublishConfigureCategoryInServicer,
    operation?: string
  ): Promise<Response<PaperPublishConfigureCategoryResponsePage>> {
    return commonRequestApi<PaperPublishConfigureCategoryResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下出卷配置分页
   * @param page    分页对象
   * @param request 查询参数对象
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pagePaperPublishConfigureInServicer(
    params: { page?: Page; request?: PaperPublishConfigureRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pagePaperPublishConfigureInServicer,
    operation?: string
  ): Promise<Response<PaperPublishConfigureResponsePage>> {
    return commonRequestApi<PaperPublishConfigureResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取子项目下学员班级练习分页列表
   * @param qualificationId   学员ID
   * @param answerPaperStatus 作答状态
   * @param answerPaperSort 排序类型
   * @param sort answerPaperSort排序方式 正序 逆序
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pagePracticeRecordInSubProject(
    params: {
      page?: Page
      qualificationId?: string
      answerPaperStatus?: number
      answerPaperSort?: AnswerPaperSort
      sort?: SortTypeEnum
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pagePracticeRecordInSubProject,
    operation?: string
  ): Promise<Response<PracticeAnswerPaperResponsePage>> {
    return commonRequestApi<PracticeAnswerPaperResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下试题分页
   * @param page    分页对象
   * @param request 查询参数对象
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageQuestionInServicer(
    params: { page?: Page; request?: QuestionRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageQuestionInServicer,
    operation?: string
  ): Promise<Response<BaseQuestionResponsePage>> {
    return commonRequestApi<BaseQuestionResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
