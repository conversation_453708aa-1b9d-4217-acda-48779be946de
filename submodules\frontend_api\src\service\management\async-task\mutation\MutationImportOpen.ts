import ImportOpenVo from '@api/service/management/async-task/mutation/vo/ImportOpenVo'
import { Response } from '@hbfe/common'
import MsImportOpen, {
  ImportOpenImportInfoForVerifyRequest,
  ImportOpenResponse
} from '@api/ms-gateway/ms-importopen-v1'
/**
 * @description 导入开通
 */

class MutationImportOpen {
  /**
   * 导入参数
   */
  importOpenQueryParams: ImportOpenVo = new ImportOpenVo()

  /**
   * 获取模板链接
   * @returns
   */
  async queryImportOpenTemplatePath() {
    const response = await MsImportOpen.queryImportOpenTemplatePath()
    return response.data
  }
  /**
   * 获取导入开班并学习模板链接
   * @returns
   */
  async queryImportOpenTemplatePathByCategory() {
    const request = new ImportOpenImportInfoForVerifyRequest()
    request.category = this.importOpenQueryParams.category
    const response = await MsImportOpen.queryImportOpenTemplatePathByCategory(request)
    return response.data
  }
  /**
   * 导入学员并开班
   */
  async doImportOpen(): Promise<Response<ImportOpenResponse>> {
    const result = new Response<ImportOpenResponse>()
    const response = await MsImportOpen.importOpenForVerify(this.importOpenQueryParams)
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
  /**
   * 专题管理员-导入学员并开班
   */
  async doImportOpenByThemeManager(): Promise<Response<ImportOpenResponse>> {
    const result = new Response<ImportOpenResponse>()
    const response = await MsImportOpen.trainingChannelAdministratorImportOpenForVerify(this.importOpenQueryParams)
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
}

export default MutationImportOpen
