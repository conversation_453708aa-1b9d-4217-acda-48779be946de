import WebPreviewVo from '@api/service/common/online-school-config/vo/WebPreviewVo'
import QueryBanner from '@api/service/management/online-school-config/banner/query/QueryBanner'
import QueryColumn from '@api/service/management/online-school-config/column/query/QueryColumn'
import QueryPortal from '@api/service/management/online-school-config/portal/query/QueryPortal'
import MutationTheme from '@api/service/management/online-school-config/theme/mutation/MutationTheme'

class MutationWebPreview {
  async doWebPreview() {
    const webPortal = new WebPreviewVo()
    webPortal.portal = QueryPortal.webPortalInfo
    if (!QueryBanner.WebBannerList.length) {
      await QueryBanner.queryWebBannerList()
    }
    webPortal.banners = QueryBanner.WebBannerList

    if (!MutationTheme.themeColor) {
      await MutationTheme.queryDetail()
    }
    webPortal.themeColor = MutationTheme.themeColor

    const menus = await new QueryColumn().queryList()
    webPortal.menus = menus
    localStorage.setItem('webPortal', JSON.stringify(webPortal))
  }
}
export default new MutationWebPreview()
