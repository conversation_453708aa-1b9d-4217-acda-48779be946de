import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 问卷开放作答时段类型枚举
 * same_as_scheme_training_time 同培训班的学习起止时间
 * assign 指定的开放时间
 */
export enum QuestionnaireOpenDateTypeEnum {
  same_as_scheme_training_time = 'same_as_scheme_training_time',
  assign = 'assign'
}

/**
 * @description 问卷开放作答时段类型
 */
class QuestionnaireOpenDateType extends AbstractEnum<QuestionnaireOpenDateTypeEnum> {
  static enum = QuestionnaireOpenDateTypeEnum

  constructor(status?: QuestionnaireOpenDateTypeEnum) {
    super()
    this.current = status
    this.map.set(QuestionnaireOpenDateTypeEnum.same_as_scheme_training_time, '同培训班的学习起止时间')
    this.map.set(QuestionnaireOpenDateTypeEnum.assign, '指定的开放时间')
  }
}

export default new QuestionnaireOpenDateType()
