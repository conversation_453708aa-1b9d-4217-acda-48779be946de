<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: <PERSON> yt
 * @LastEditTime: 2022-06-10 11:37:21
-->
<route-meta>
{
"isMenu": true,
"title": "角色管理",
"sort": 2
}
</route-meta>

<template>
  <el-main>
    <div class="f-p15" v-if="$hasPermission('query')" desc="查询" actions="activated">
      <div
        class="f-mb15"
        v-if="$hasPermission('create')"
        desc="新建"
        actions="@hbfe/jxjy-admin-account/src/role/create.vue"
      >
        <el-button type="primary" icon="el-icon-plus" @click="$crudJumper.goCreate()">添加角色</el-button>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <el-table stripe :data="rolePage" max-height="500px" class="m-table" v-loading="query.loading">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="角色名称" min-width="180" prop="name"></el-table-column>
          <el-table-column label="所属角色" min-width="180" prop="belongRoleName" v-if="hasFxs"></el-table-column>
          <el-table-column label="角色说明" min-width="300" prop="description"></el-table-column>
          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                v-if="$hasPermission('modify') && !row.isBuiltIn"
                desc="修改"
                actions="@hbfe/jxjy-admin-account/src/role/modify.vue"
                @click="$crudJumper.goModify(row.id)"
              >
                修改
              </el-button>
              <el-popconfirm
                confirm-button-text="确定"
                placement="top"
                title="确定要删除该条数据？"
                v-if="$hasPermission('remove')"
                desc="删除"
                actions="remove"
                @confirm="remove(row)"
              >
                <el-button type="text" size="mini" slot="reference">删除</el-button>
              </el-popconfirm>
              <el-button
                type="text"
                size="mini"
                v-if="$hasPermission('detail')"
                desc="功能权限"
                actions="@hbfe/jxjy-admin-account/src/role/detail.vue"
                @click="$crudJumper.goDetail(row.id)"
              >
                功能权限
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import { Query } from '@hbfe/common'
  import StandardRouteJumper from '@hbfe/jxjy-admin-common/src/StandardRouteJumper'
  import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
  @Component
  export default class extends Vue {
    queryRole = AuthorityModule.roleFactory
    getQueryRole = AuthorityModule.roleFactory.getQueryRoleList()
    rolePage: Array<RoleInfoResponseVo> = new Array<RoleInfoResponseVo>()
    query: Query = new Query()
    $crudJumper: StandardRouteJumper
    RoleQuery = new RoleInfoResponseVo()
    getMutationDeleteRole = AuthorityModule.roleMutationFactory.getMutationDeleteRole()
    /**
     * 当前登录角色含有 分销商，则不展示 “所属角色” 这一列
     */
    get hasFxs() {
      return AuthorityModule.securityFactory.querySecurity.roleInfoList.map((res) => res.category).includes(18)
        ? false
        : true
    }
    constructor() {
      super()
    }

    async created() {
      this.$crudJumper = new StandardRouteJumper(this.$route.meta.group, this.$router)
    }
    //组件激活
    async activated() {
      await this.doSearch()
    }
    async doSearch() {
      this.query.loading = true
      await this.getQueryRole.queryRoleList()
      this.rolePage = this.getQueryRole.roleList

      this.query.loading = false
    }

    async remove(row: RoleInfoResponseVo) {
      try {
        const res: any = await row.removeRoleWithAdminTypeById()
        if (res?.status?.code === 200) {
          if (res?.data?.code == '200') {
            this.$message.success('删除成功')
            await this.doSearch()
          } else {
            this.$message.error(res?.data?.message)
          }
        }
      } catch (e) {
        console.log(e)
        this.$message.error(e.message || '删除出错')
      }

      //
    }
  }
</script>
