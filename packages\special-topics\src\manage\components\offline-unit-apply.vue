<template>
  <div>
    <el-card shadow="never" class="m-card f-mb15 is-bg">
      <div class="f-p15">
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form
              ref="offlineUnitApplyFormRef"
              :model="offlineCollectiveInfo"
              label-width="200px"
              class="m-form f-mt20"
              :rules="rules"
            >
              <el-form-item label="专题线下集体报名入口：" required>
                <el-switch
                  v-model="offlineCollectiveInfo.offlineCollectiveEntry"
                  active-text="开启"
                  inactive-text="关闭"
                  class="m-switch"
                />
              </el-form-item>
              <template v-if="offlineCollectiveInfo.offlineCollectiveEntry">
                <el-form-item label="专题线下集体报名入口图片：">
                  <cropper-img-upload
                    v-model="offlineCollectiveInfo.offlineCollectiveEntryPicture"
                    :dialogStyleOpation="{
                      width: `${picSize[0]}px`,
                      height: `${picSize[1]}px`
                    }"
                    :ratioArr="[`${picSize[0]}:${picSize[1]}`]"
                    :initWidth="picSize[0]"
                    title="上传线下集体报名入口图片"
                    button-text="上传线下集体报名入口图片"
                    :is-long-pic="false"
                    reminder-text="只支持JPG、PNG、GIF"
                    :has-preview="false"
                    :mode="`${picSize[0]}px ${picSize[1]}px`"
                    :full="true"
                  >
                    <div slot="tip" class="el-upload__tip">
                      <i class="el-icon-warning"></i>
                      <span class="txt">
                        上传线上集体报名入口图片，只开放线上入口图片尺寸为{{
                          imgSize('singleOfflineCollectSize', 'width')
                        }}x{{
                          imgSize('singleOfflineCollectSize', 'height')
                        }}
                        px，线上入口和线下入口都开放则图片尺寸为{{ imgSize('doubleOfflineCollectSize', 'width') }}x{{
                          imgSize('doubleOfflineCollectSize', 'height')
                        }}
                        px，不上传则显示模板默认图片
                        <i class="f-link" @click="handlePictureExample">示例图片</i>
                      </span>
                      <!--示例图片弹窗-->
                      <el-dialog :visible.sync="pictureVisible" width="640px" class="m-dialog-pic">
                        <!--分别读取对应的默认图片-->
                        <img :src="exampleImg" alt="" />
                      </el-dialog>
                    </div>
                  </cropper-img-upload>
                </el-form-item>
                <el-form-item label="专题线下集体报名名称：" required prop="offlineCollectiveEntryName">
                  <el-input
                    v-model="offlineCollectiveInfo.offlineCollectiveEntryName"
                    placeholder="单位集体报名入口"
                    clearable
                  />
                </el-form-item>
                <el-form-item label="线下集体报名模板：" required>
                  <min-upload-file v-model="hbFileUploadResponse" :file-type="1">
                    <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                      点击上传模板
                    </el-button>
                    <div class="el-upload__tip f-pt5">
                      <div class="txt"><i class="f-link" @click.stop="downloadTemplate">下载示例模版</i></div>
                    </div>
                  </min-upload-file>
                </el-form-item>
                <el-form-item label="访问链接：" required>
                  {{ offlineCollectiveInfo.signUpClassUrl }}
                  <hb-copy :content="offlineCollectiveInfo.signUpClassUrl"></hb-copy>
                </el-form-item>
                <el-form-item label="底部文本说明：" required prop="bottomText">
                  <el-input
                    v-model="offlineCollectiveInfo.bottomText"
                    clearable
                    placeholder="填写集体报名联系电话，如：如报名过程有问题，请咨询服务热线：968823"
                  />
                </el-form-item>
                <el-form-item label="报名步骤：" required prop="steps">
                  <div v-for="(item, index) in offlineCollectiveInfo.steps" :key="item.no" class="step f-mb20">
                    <div class="f-flex f-align-center">
                      <div class="f-flex-sub">
                        <span class="f-cb f-fb"><i class="f-dot f-mr5"></i>第{{ numberToChinese(index + 1) }}步</span>
                        <el-input v-model="item.title" clearable placeholder="请输入步骤的标题" class="form-l f-ml10" />
                      </div>
                      <el-button
                        size="mini"
                        type="danger"
                        plain
                        class="f-fr"
                        @click="sureDeleteDialog(index)"
                        :disabled="index === 0 && offlineCollectiveInfo.steps.length === 1"
                        >删除</el-button
                      >
                    </div>
                    <div class="rich-text f-mt10">
                      <hb-tinymce-editor v-model="item.content"></hb-tinymce-editor>
                    </div>
                  </div>
                  <el-button type="primary" plain icon="el-icon-plus" @click="handleAddStep">添加步骤</el-button>
                </el-form-item>
              </template>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-btn-bar f-tc is-sticky f-pt15" style="z-index: 8">
          <el-button @click="cancel">取 消</el-button>
          <el-button @click="preview">预览</el-button>
          <el-button @click="validForm" type="primary" :loading="loading">保 存</el-button>
        </div>
      </div>
    </el-card>
    <el-dialog title="提示" :visible.sync="deleteDialog" width="450px" class="m-dialog">
      <div class="dialog-alert">
        <i class="icon el-icon-error error"></i>
        <span class="txt">确定删除该内容吗？删除后不可恢复</span>
      </div>
      <div slot="footer">
        <el-button @click="deleteDialog = false">取 消</el-button>
        <el-button type="primary" @click="deleteStep">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { ElForm } from 'element-ui/types/form'
  import { Component, Vue, Ref, Prop, Watch } from 'vue-property-decorator'
  import MinUploadFile from '@hbfe/jxjy-admin-platform/src//function/components/min-upload-file.vue'
  import { bind, debounce } from 'lodash-decorators'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import CropperImgUpload from '@hbfe/jxjy-admin-platform/src/function/components/cropper-img-upload.vue'
  import OfflineCollectiveInfo from '@api/service/management/thematic-management/model/OfflineCollectiveInfo'
  import PortalInfo from '@api/service/management/thematic-management/model/PortalInfo'
  import UnitApplyPicSizeTypeModel from '@hbfe/jxjy-admin-specialTopics/src/manage/vuex/UnitApplyPicSizeTypeModel'
  import TemplateModule from '@api/service/common/template-school/TopicTemplateModule'
  import TemplateItem from '@api/service/common/template-school/TemplateItem'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'

  @Component({
    components: {
      MinUploadFile,
      CropperImgUpload
    }
  })
  export default class extends Vue {
    @Ref('offlineUnitApplyFormRef') offlineUnitApplyFormRef: ElForm
    /**
     * 表单数据
     */
    offlineCollectiveInfo: OfflineCollectiveInfo = new OfflineCollectiveInfo()
    /**
     * 表单数据
     */
    @Prop({
      type: Object,
      default: () => {
        return new ThematicManagementItem()
      }
    })
    thematicManagementItem: ThematicManagementItem
    @Watch('thematicManagementItem', { deep: true })
    thematicManagementItemChange(val: ThematicManagementItem) {
      this.offlineCollectiveInfo = this.thematicManagementItem.offlineCollectiveInfo
    }

    /**
     * 门户信息类
     */
    @Prop({
      type: Object,
      default: () => {
        return new PortalInfo()
      }
    })
    portalInfo: PortalInfo
    /**
     * 模板webId
     */
    @Prop({
      type: String,
      default: ''
    })
    templateWeb: string
    /**
     * 示例图片显示
     */
    pictureVisible = false
    /**
     * 校验规则
     */
    rules = {
      bottomText: [{ required: true, message: '请输入底部文本说明', trigger: ['change', 'blur'] }],
      steps: [{ required: true, message: '请添加报名步骤', trigger: ['change', 'blur'] }],
      offlineCollectiveEntryName: [{ required: true, message: '请输入线下集体报名名称', trigger: ['change', 'blur'] }]
    }
    /**
     * 文件上传之后的回调参数
     */
    hbFileUploadResponse = new HBFileUploadResponse()
    /**
     * 专题模板
     */
    TemplateItem: TemplateItem
    /**
     * 获取图片尺寸
     */
    get picSize() {
      return UnitApplyPicSizeTypeModel.isSingle
        ? this.TemplateItem.singleOnlineCollectSize
        : this.TemplateItem.doubleOnlineCollectSize
    }
    /**
     * 示例图片
     */
    get exampleImg() {
      const src = UnitApplyPicSizeTypeModel.isSingle
        ? this.TemplateItem.singleOfflineExampleImgSrc
        : this.TemplateItem.doubleOfflineExampleImgSrc
      return require('@design/admin/assets/images/' + src)
    }
    /**
     * 监听入口状态，更改图片尺寸
     */
    @Watch('thematicManagementItem.offlineCollectiveInfo.offlineCollectiveEntry')
    onEntryStatusChange(val: boolean) {
      UnitApplyPicSizeTypeModel.setOfflinePicEnable(val)
    }
    @Watch('templateWeb')
    templateWebChange(val: string) {
      this.TemplateItem = TemplateModule.getTemplate(val)
    }
    /**
     * 下载示例模板
     */
    @bind
    @debounce(1000)
    async downloadTemplate() {
      const link = document.createElement('a')
      const resolver = this.$router.resolve({
        name: '/mfs/resource/file/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/template/offlineCollectiveRegisterTemplate.xls'
      })

      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      link.setAttribute('download', 'xxx单位学员集体报名名单及开票信息表')
      document.body.appendChild(link)
      link.click()
      link.remove()
    }

    /**
     * 示例图片
     */
    handlePictureExample() {
      this.pictureVisible = true
    }

    /********************************************** 底部操作Action ***********************************************************/
    /**
     * 保存loading
     */
    loading = false
    /**
     * @description 置换模板尺寸
     * @param key 尺寸key(参照 templateModule)
     * @param side 尺寸方向 width 宽 height 高
     * @return number
     * */
    get imgSize() {
      return (key: string, side: string) => {
        // todo
        const templateId = this.templateWeb
        const sizeObj = TemplateModule.getTemplate(templateId)
        const sideIndex = side == 'width' ? 0 : 1
        return sizeObj[key][sideIndex]
      }
    }
    /**
     * 取消
     */
    cancel() {
      //TODO 需要删除编辑的数据
      this.$confirm('确定要放弃编辑吗？', '提示', {
        confirmButtonText: '确认',
        showCancelButton: true
      }).then(() => {
        this.$message.success('已取消本次编辑')
        this.$router.push({
          path: '/training/special-topics/manage'
        })
      })
    }
    /**
     * 保存
     */
    validForm() {
      this.offlineUnitApplyFormRef.validate((valid: boolean) => {
        if (valid) {
          this.doSave()
        }
      })
    }

    @debounce(200)
    @bind
    async doSave() {
      if (this.offlineCollectiveInfo.offlineCollectiveEntry) {
        if (this.offlineCollectiveInfo.steps.some((step) => step.title == '')) {
          this.$message.warning('请输入步骤的标题')
          return
        }
        if (!this.hbFileUploadResponse.url || this.hbFileUploadResponse.url == '') {
          this.$message.warning('请上传线下集体报名模板')
          return
        }
      }
      if (this.hbFileUploadResponse.url) {
        const { url, fileName } = this.hbFileUploadResponse
        if (url && fileName) {
          this.offlineCollectiveInfo.offlineCollectiveEntryTemplate = {
            url,
            name: fileName
          }
        }
      }
      this.loading = true
      await this.saveOfflineCollectiveInfo()
      // const change = UnitApplyPicSizeTypeModel.isOfflinePicChange
      this.delayApply(async () => {
        await this.queryOfflineCollectiveInfo()
        if (!this.offlineCollectiveInfo.steps || this.offlineCollectiveInfo.steps.length == 0) {
          this.initStep()
        }
        this.loading = false
      }, 1000)

      // if (change) {
      //   this.$confirm('线上集体报名入口图片尺寸变更，是否立即前往调整？', '提示', {
      //     confirmButtonText: '确定',
      //     cancelButtonText: '取消'
      //   }).then(() => {
      //     this.$emit('update:activeName', 'onlineApply')
      //   })
      // }
    }
    /**
     * 保存线下集体报名配置
     */
    async saveOfflineCollectiveInfo() {
      UnitApplyPicSizeTypeModel.setOfflinePicEnable(
        this.thematicManagementItem.offlineCollectiveInfo.offlineCollectiveEntry
      )
      let status = null
      if (this.thematicManagementItem.offlineCollectiveInfo.id) {
        status = await this.thematicManagementItem.updateOfflineCollectiveInfo()
      } else {
        status = await this.thematicManagementItem.saveOfflineCollectiveInfo()
      }
      if (status.isSuccess()) {
        this.$message.success('保存成功')
      } else {
        this.$message.error('保存失败')
      }
    }
    /**
     * 查询线下集体报名配置
     */
    async queryOfflineCollectiveInfo() {
      const status = await this.thematicManagementItem.queryOfflineCollectiveInfo()
      if (!status.isSuccess()) {
        this.$message.error('查询线下集体报名配置失败')
      }
      //保存后要重新初始化
      UnitApplyPicSizeTypeModel.setOriginOfflinePicEnable(
        this.thematicManagementItem.offlineCollectiveInfo.offlineCollectiveEntry
      )
    }
    /**
     * 预览
     */
    preview() {
      const collectiveSignUp = JSON.stringify(this.offlineCollectiveInfo)
      localStorage.setItem('offlineApply', collectiveSignUp)
      window.open(`/collective-apply-preview/index`, '_blank')
    }
    /********************************************** 步骤 ***********************************************************/
    /**
     * 删除提示
     */
    deleteDialog = false
    /**
     * 当前删除步骤
     */
    curStepIndex = 0
    /**
     * 确认删除步骤弹窗
     */
    sureDeleteDialog(index: number) {
      this.curStepIndex = index
      this.deleteDialog = true
    }
    /**
     * 删除步骤
     */
    deleteStep() {
      this.offlineCollectiveInfo.removeStep(this.curStepIndex + 1)
      this.deleteDialog = false
    }
    /**
     * 添加步骤
     */
    handleAddStep() {
      this.offlineCollectiveInfo.addStep()
    }
    /**
     * 设置默认步骤
     */
    initStep() {
      this.offlineCollectiveInfo.steps = [
        {
          no: 0,
          title: '请下载 “ XXX单位学员报名及开票信息表 ” 模板',
          content:
            '<p>（1）填写学员信息及所学班级；</p>' +
            '<p>（2）填写电子发票相关信息（开具发票使用）。</p>' +
            '<p style="color:red">注：单位集体报名的，发票统一开具</p>'
        },
        {
          no: 1,
          title: '汇款至xxxxx有限公司对公账户',
          content:
            '<p>收款人：xxxx有限公司</p>' +
            '<p>账号：350010024033333333</p>' +
            '<p>开户银行：中国建设银行股份有限公司福建省分行营业部</p>' +
            '<p style="color:red"></p>'
        },
        {
          no: 2,
          title: '发送学员及开票信息表和汇款凭证',
          content:
            '<p>邮箱：<EMAIL></p>' +
            '<p>客服电话：968-823</p>' +
            '<p style="color:red">注：系统将在收到报名开票信息表及汇款凭证后的3个工作日内开通班级。</p>'
        },
        {
          no: 3,
          title: '完成集体报名',
          content:
            '<p>（1）报名联系人将收到学员报名完成的反馈表；</p>' +
            '<p>（2）发送电子发票至报名负责人邮箱</p>' +
            '<p style="color:red">注：电子发票将在开通班级后的七个工作日开具。</p>'
        }
      ]
    }
    /**
     * 将数字转换为中文
     */
    numberToChinese(num: number) {
      const chineseNums = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      const chineseUnits = ['', '十', '百', '千']
      let chineseResult = ''

      let unitCount = 0
      while (num > 0) {
        const digit = num % 10
        if (digit === 0) {
          if (chineseResult.charAt(0) !== chineseNums[digit]) {
            chineseResult = chineseNums[digit] + chineseResult
          }
        } else {
          chineseResult = chineseNums[digit] + chineseUnits[unitCount] + chineseResult
        }
        unitCount++
        num = Math.floor(num / 10)
      }

      return chineseResult
    }
    /**
     * 延时执行
     * @param fn 执行方法
     * @param time 延时时间
     */
    delayApply(fn: Function, time: number) {
      setTimeout(() => {
        fn.call(this)
      }, time)
    }
    created() {
      const pcTemplateId = this.templateWeb
      this.TemplateItem = TemplateModule.getTemplate(pcTemplateId)
      this.offlineCollectiveInfo = this.thematicManagementItem.offlineCollectiveInfo
      if (this.offlineCollectiveInfo && this.offlineCollectiveInfo.offlineCollectiveEntryTemplate) {
        this.hbFileUploadResponse = new HBFileUploadResponse()
        this.hbFileUploadResponse.url = this.offlineCollectiveInfo.offlineCollectiveEntryTemplate.url
        this.hbFileUploadResponse.fileName = this.offlineCollectiveInfo.offlineCollectiveEntryTemplate.name
      }
      if (!this.offlineCollectiveInfo.steps || this.offlineCollectiveInfo.steps.length == 0) {
        this.initStep()
      }
    }
  }
</script>
