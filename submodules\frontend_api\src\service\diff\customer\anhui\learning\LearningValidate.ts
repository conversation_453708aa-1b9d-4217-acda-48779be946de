/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-05-24 17:03:04
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-05-25 18:00:41
 * @Description:
 */
import PlatformJxjypxtyptAhzjSchool from '@api/platform-gateway/platform-jxjypxtypt-ahzj-school'
export default class LearningValidate {
  /**
   * 参训资格ID
   */
  qualificationId = ''
  /**
   * 校验失败code码
   */
  failCode: number
  async validate() {
    if (!this.qualificationId) return false
    const res = await PlatformJxjypxtyptAhzjSchool.validAllowToLearning({
      qualificationId: this.qualificationId
    })
    return res.data
  }
}
