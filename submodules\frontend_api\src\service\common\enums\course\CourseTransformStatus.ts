import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum TransformStatusEnum {
  EMPTY = '0',
  NOT_AVAILABLE_YET = '1',
  AVAILABLE = '2',
  UNAVAILABLE = '3'
}

class CourseTransformStatus extends AbstractEnum<TransformStatusEnum> {
  static enum = TransformStatusEnum

  constructor(status?: TransformStatusEnum) {
    super()
    // 0.转换失败（空课程） 1.转换中 2.转换成功 3.转换失败
    this.current = status
    // this.map.set(TransformStatusEnum.EMPTY, '空课程')
    this.map.set(TransformStatusEnum.NOT_AVAILABLE_YET, '转换中')
    this.map.set(TransformStatusEnum.UNAVAILABLE, '转换失败')
    this.map.set(TransformStatusEnum.AVAILABLE, '转换成功')
  }
}

export default CourseTransformStatus
