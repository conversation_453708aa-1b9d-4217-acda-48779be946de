import {
  AccountRequest,
  DateScopeRequest,
  StudentQueryRequest,
  StudentUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

class QueryStudentPageParams {
  // 姓名
  name = ''
  // 身份证
  idCard = ''
  // 电话号码
  phone = ''
  // 地区
  region = ''
  // 单位名称
  unitName = ''
  // 注册时间区间开始
  registryBeginTime = ''
  // 注册时间区间结束
  registryEndTime = ''

  toJSON() {
    const request = new StudentQueryRequest()
    request.user = new StudentUserRequest()
    request.user.regionPathList = new Array<string>()
    request.account = new AccountRequest()
    request.account.createTimeScope = new DateScopeRequest()
    request.user.idCard = this.idCard
    request.user.phone = this.phone
    request.user.companyName = this.unitName
    ;(request.account.createTimeScope.beginTime = this.registryBeginTime),
      (request.account.createTimeScope.endTime = this.registryEndTime)
    request.user.regionPathList = [this.region]
    return request
  }
}

export default QueryStudentPageParams
