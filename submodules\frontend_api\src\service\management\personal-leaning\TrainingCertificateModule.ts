import store from '@api/store'
import { VuexModule, getModule, Module } from 'vuex-module-decorators'
import QueryTrainingCertificateFactory from '@api/service/management/personal-leaning/QueryTrainingCertificateFactory'
import MutationBatchPrintTrainingFactory from '@api/service/management/personal-leaning/MutationBatchPrintTrainingFactory'

@Module({ namespaced: true, name: 'TrainingCertificateModule', dynamic: true, store })
class TrainingCertificateModule extends VuexModule {
  /* 
    查询培训成果工厂  
  */
  get queryTrainingCertificateFactory() {
    return QueryTrainingCertificateFactory
  }

  /* 
    培训成果业务工厂
  */
  get mutationBatchPrintTrainingFactory() {
    return MutationBatchPrintTrainingFactory
  }
}

export default getModule(TrainingCertificateModule)
