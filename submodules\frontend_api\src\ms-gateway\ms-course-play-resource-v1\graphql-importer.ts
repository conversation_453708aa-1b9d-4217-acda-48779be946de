import applyCourseAudition from './mutates/applyCourseAudition.graphql'
import applyCourseAuditionWithoutValidate from './mutates/applyCourseAuditionWithoutValidate.graphql'
import applyCoursePlayResource from './mutates/applyCoursePlayResource.graphql'
import applyCoursePreview from './mutates/applyCoursePreview.graphql'
import applyCoursewareMediaPlayAntiTheftChainResource from './mutates/applyCoursewareMediaPlayAntiTheftChainResource.graphql'
import applyCoursewareMediaPlayResource from './mutates/applyCoursewareMediaPlayResource.graphql'
import applyCoursewareMediaPreviewAntiTheftChainResource from './mutates/applyCoursewareMediaPreviewAntiTheftChainResource.graphql'
import applyCoursewareMediaPreviewResource from './mutates/applyCoursewareMediaPreviewResource.graphql'
import applyMediaPlayAntiTheftChainResource from './mutates/applyMediaPlayAntiTheftChainResource.graphql'

export {
  applyCourseAudition,
  applyCourseAuditionWithoutValidate,
  applyCoursePlayResource,
  applyCoursePreview,
  applyCoursewareMediaPlayAntiTheftChainResource,
  applyCoursewareMediaPlayResource,
  applyCoursewareMediaPreviewAntiTheftChainResource,
  applyCoursewareMediaPreviewResource,
  applyMediaPlayAntiTheftChainResource
}
