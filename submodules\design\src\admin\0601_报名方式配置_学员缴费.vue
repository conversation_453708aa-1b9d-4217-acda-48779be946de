<template>
  <el-main>
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <p>配置相关说明：</p>
      <p>
        1. 当前系统提供个人缴费、集体报名及教务导入三个报名方式，请针对实际渠道的运用情况配置对应的收款账户及发票信息；
      </p>
      <p>2. 个人缴费渠道、单位缴费渠道可允许添加多个收款账号，导入开通渠道默认收款账号帐号无法修改。</p>
    </el-alert>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="学员缴费" name="first">
        <div class="f-p15">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header">
              <span class="tit-txt">销售渠道配置</span>
            </div>
            <!--销售渠道 1-->
            <div class="f-plr20 f-pb20">
              <div class="m-sub-tit">
                <span class="tit-txt f-flex-sub">销售渠道：Web 端</span>
                <el-button type="primary" size="small">选择收款账号</el-button>
              </div>
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="支付方式" min-width="100" align="center">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-tag type="warning" size="small">线下</el-tag>
                    </div>
                    <div v-else>
                      <el-tag type="primary" size="small">线上</el-tag>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="支付账号类型" min-width="120">
                  <template>微信支付</template>
                </el-table-column>
                <el-table-column label="收款账号别名" min-width="120">
                  <template>别名</template>
                </el-table-column>
                <el-table-column label="开户账户信息" min-width="250">
                  <template>商户号：152</template>
                </el-table-column>
                <el-table-column label="分行号" min-width="180">
                  <template>-</template>
                </el-table-column>
                <el-table-column label="柜台号" min-width="180">
                  <template>-</template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">移除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
            <!--销售渠道 2-->
            <div class="f-plr20 f-pb20">
              <div class="m-sub-tit">
                <span class="tit-txt f-flex-sub">销售渠道：H5</span>
                <el-button type="primary" size="small">选择收款账号</el-button>
              </div>
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="支付方式" min-width="100" align="center">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-tag type="warning" size="small">线下</el-tag>
                    </div>
                    <div v-else>
                      <el-tag type="primary" size="small">线上</el-tag>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="支付账号类型" min-width="120">
                  <template>微信支付</template>
                </el-table-column>
                <el-table-column label="收款账号别名" min-width="120">
                  <template>别名</template>
                </el-table-column>
                <el-table-column label="开户账户信息" min-width="250">
                  <template>商户号：152</template>
                </el-table-column>
                <el-table-column label="分行号" min-width="180">
                  <template>-</template>
                </el-table-column>
                <el-table-column label="柜台号" min-width="180">
                  <template>-</template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">移除</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </el-card>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header" class="f-flex">
              <span class="tit-txt f-flex-sub">渠道发票配置</span>
              <el-button type="primary" size="small" icon="el-icon-edit">编辑</el-button>
            </div>
            <div class="f-p30">
              <el-row type="flex" justify="center" class="width-limit">
                <el-col :md="20" :lg="16" :xl="13">
                  <el-form ref="form" :model="form" label-width="auto" class="m-form">
                    <el-form-item label="是否提供发票：" required>
                      <el-radio-group v-model="form.resource">
                        <el-radio label="是"></el-radio>
                        <el-radio label="否"></el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否开放索要发票：" required>
                      <el-radio-group v-model="form.resource">
                        <el-radio label="开放学员自主选择是否索要发票"></el-radio>
                        <el-radio label="强制向学员提供发票"></el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="是否开放补要发票：" required>
                      <el-radio-group v-model="form.resource">
                        <el-radio label="开放"></el-radio>
                        <el-radio label="不开放"></el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="补要发票截止日期类型：" required>
                      <el-radio-group v-model="form.resource">
                        <el-radio label="当年度"></el-radio>
                        <el-radio label="下一个年度"></el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="补要发票截止日期：" required>
                      <el-date-picker
                        v-model="form.date1"
                        type="date"
                        placeholder="请选择补要发票截止日期"
                        class="form-m"
                      />
                    </el-form-item>
                    <el-form-item label="发票类型：" required>
                      <el-checkbox label="增值税电子普通发票" name="type" class="f-show"></el-checkbox>
                      <!--选择 增值税电子普通发票 后出现-->
                      <div class="f-mb5">
                        <el-form ref="form" :model="form" label-width="120px" class="m-form bg-gray f-pr50 f-pt5">
                          <el-form-item label="开票方式：" required>
                            <el-radio-group v-model="form.resource">
                              <el-radio label="自动开票"></el-radio>
                              <el-radio label="线下开票"></el-radio>
                            </el-radio-group>
                          </el-form-item>
                          <el-form-item label="发票抬头：" required>
                            <el-checkbox-group v-model="form.type">
                              <el-checkbox label="个人" name="type"></el-checkbox>
                              <el-checkbox label="单位" name="type"></el-checkbox>
                            </el-checkbox-group>
                          </el-form-item>
                        </el-form>
                      </div>
                      <el-checkbox name="type" class="f-show"
                        >增值税专用发票<span class="f-co">（默认开票方式为线下开票，抬头为单位）</span></el-checkbox
                      >
                    </el-form-item>
                    <el-form-item class="m-btn-bar">
                      <el-button>放弃编辑</el-button>
                      <el-button type="primary">保存</el-button>
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </div>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="集体报名" name="second">
        <div class="f-p15">
          详见 0602_报名方式配置_集体报名.vue
        </div>
      </el-tab-pane>
      <el-tab-pane label="导入开通" name="third">
        <div class="f-p15">
          详见 0604_报名方式配置_导入开通.vue
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
