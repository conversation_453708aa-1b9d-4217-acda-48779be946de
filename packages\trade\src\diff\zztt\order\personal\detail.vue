<route-params content="/:id"></route-params>
<route-meta>
{
"isMenu":true,
"hideMenu": true,
"onlyShowOnTab":true,
"title": "个人订单详情"
}
</route-meta>
<template>
  <el-main>
    <template v-if="$hasPermission('detail')" desc="详情" query actions="created,activated">
      <!--面包屑-->
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-button type="text" size="mini" class="return-btn" @click="$router.push('/training/trade/order/personal')">
          <i class="iconfont icon-lsh-return"></i>
        </el-button>
        <el-breadcrumb-item :to="{ path: '/training/trade/order/personal' }">个人报名订单</el-breadcrumb-item>
        <el-breadcrumb-item>详情</el-breadcrumb-item>
      </el-breadcrumb>
      <div class="f-p15">
        <!--交易成功-->
        <el-card shadow="never" v-if="orderDetail.orderStatue == 3" class="m-card is-header m-order-state">
          <div class="info">
            <p>订单号：{{ orderDetail.orderNo }}</p>
            <p class="state f-cg">交易成功</p>
          </div>
          <el-steps :active="4" align-center class="process">
            <el-step
              title="提交订单"
              :description="orderDetail.basicData ? orderDetail.basicData.orderStatusChangeTime.normal : ''"
              icon="hb-iconfont icon-s-myorder"
            ></el-step>
            <el-step
              title="付款"
              :description="orderDetail.basicData ? orderDetail.basicData.orderPaymentStatusChangeTime.paying : ''"
              icon="hb-iconfont icon-s-pay"
            ></el-step>
            <el-step
              title="班级开通"
              :description="orderDetail.basicData ? orderDetail.basicData.orderPaymentStatusChangeTime.paid : ''"
              icon="hb-iconfont icon-s-learningcenter"
            ></el-step>
            <el-step
              title="交易成功"
              :description="orderDetail.basicData ? orderDetail.basicData.orderStatusChangeTime.completed : ''"
              icon="hb-iconfont icon-success"
            ></el-step>
          </el-steps>
        </el-card>
        <!--其他状态-->
        <!--等待付款-->
        <el-card shadow="never" v-if="orderDetail.orderStatue == 0" class="m-card is-header m-order-state">
          <div class="info">
            <p>订单号：{{ orderDetail.orderNo }}</p>
            <p class="state f-co">等待付款</p>
          </div>
          <el-steps :active="1" align-center class="process">
            <el-step
              title="提交订单"
              :description="orderDetail.basicData ? orderDetail.basicData.orderStatusChangeTime.normal : ''"
              icon="hb-iconfont icon-s-myorder"
            ></el-step>
            <el-step title="付款" icon="hb-iconfont icon-s-pay"></el-step>
            <el-step title="班级开通" icon="hb-iconfont icon-s-learningcenter"></el-step>
            <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
          </el-steps>
        </el-card>
        <!--支付中-->
        <el-card shadow="never" v-if="orderDetail.orderStatue == 1" class="m-card is-header m-order-state">
          <div class="info">
            <p>订单号：{{ orderDetail.orderNo }}</p>
            <p class="state f-cb">支付中</p>
          </div>
          <el-steps :active="1" align-center class="process">
            <el-step
              title="提交订单"
              :description="orderDetail.basicData ? orderDetail.basicData.orderStatusChangeTime.normal : ''"
              icon="hb-iconfont icon-s-myorder"
            ></el-step>
            <el-step title="付款" icon="hb-iconfont icon-s-pay"></el-step>
            <el-step title="班级开通" icon="hb-iconfont icon-s-learningcenter"></el-step>
            <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
          </el-steps>
        </el-card>
        <!--开通中-->
        <el-card shadow="never" v-if="orderDetail.orderStatue == 2" class="m-card is-header m-order-state">
          <div class="info">
            <p>订单号：{{ orderDetail.orderNo }}</p>
            <p class="state f-cb">开通中</p>
          </div>
          <el-steps :active="3" align-center class="process">
            <el-step
              title="提交订单"
              :description="orderDetail.basicData ? orderDetail.basicData.orderStatusChangeTime.normal : ''"
              icon="hb-iconfont icon-s-myorder"
            ></el-step>
            <el-step
              title="付款"
              :description="orderDetail.basicData ? orderDetail.basicData.orderPaymentStatusChangeTime.paying : ''"
              icon="hb-iconfont icon-s-pay"
            ></el-step>
            <el-step
              title="班级开通"
              :description="orderDetail.basicData ? orderDetail.basicData.orderPaymentStatusChangeTime.paid : ''"
              icon="hb-iconfont icon-s-learningcenter"
            ></el-step>
            <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
          </el-steps>
        </el-card>
        <!--交易关闭-->
        <el-card shadow="never" v-if="orderDetail.orderStatue == 4" class="m-card is-header m-order-state">
          <div class="info">
            <p>订单号：{{ orderDetail.orderNo }}</p>
            <p class="state f-c9">交易关闭</p>
          </div>
          <el-steps :active="2" align-center class="process">
            <el-step
              title="提交订单"
              :description="orderDetail.basicData ? orderDetail.basicData.orderStatusChangeTime.normal : ''"
              icon="hb-iconfont icon-s-myorder"
            ></el-step>
            <el-step
              title="交易关闭"
              :description="orderDetail.basicData ? orderDetail.basicData.orderStatusChangeTime.closed : ''"
              icon="hb-iconfont icon-s-close"
            ></el-step>
          </el-steps>
        </el-card>
        <!--订单信息-->
        <el-card shadow="never" class="m-card is-header m-order-info f-mb15">
          <div class="f-flex-sub f-plr20 f-pt10">
            <div class="m-tit">
              <span class="tit-txt">订单信息</span>
            </div>
            <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
              <el-col :span="24">
                <el-form-item label="订单类型：">{{
                  orderDetail.basicData ? getOrderTypeInfo(orderDetail.basicData.channelType) : '-'
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="订单创建人：">{{
                  orderDetail.creatorDetail && orderDetail.creatorDetail.userName
                    ? orderDetail.creatorDetail.userName
                    : '-'
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="创建人帐号：">{{ hasIdCard }}</el-form-item>
              </el-col>

              <el-col :span="12" v-if="orderDetail.subOrderItems && orderDetail.subOrderItems[0].commodityAuthInfo">
                <el-form-item label="分销商：">{{ getDistribution('distributorName') }}</el-form-item>
              </el-col>
              <el-col :span="12" v-if="subDistributionId">
                <el-form-item label="上级分销商：">{{ getDistribution('superiorDistributorName') }}</el-form-item>
              </el-col>
            </el-form>
            <div class="m-tit">
              <span class="tit-txt">发票信息</span>
            </div>
            <el-form label-width="140px" class="m-text-form f-pt10 f-pb20" v-if="orderDetail.invoiceApplyInfo">
              <el-col :span="24">
                <el-form-item label="发票类型：">{{
                  orderDetail.invoiceIdentification == 1
                    ? '普通发票'
                    : orderDetail.invoiceIdentification == 2
                    ? '增值税电子普通发票'
                    : orderDetail.invoiceIdentification == 3
                    ? '增值税专用发票'
                    : orderDetail.invoiceIdentification == 4
                    ? '增值税电子专用发票（线下开票）'
                    : '-'
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发票状态：">
                  <el-tag type="success" size="small" class="f-mr5" v-if="invoiceStatus == 2">已开票</el-tag>
                  <el-tag type="warning" size="small" class="f-mr5" v-if="invoiceStatus == 0 || invoiceStatus == 1"
                    >待开票</el-tag
                  >
                  <el-tag type="error" size="small" class="f-mr5" v-if="invoiceStatus == 4">冻结中</el-tag>
                  <el-tag type="info" size="small" class="f-mr5" v-if="invoiceStatus == 3">已作废</el-tag>
                  <el-tag type="info" size="small" class="f-mr5" v-if="!invoiceStatus && invoiceStatus != 0">-</el-tag>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发票号码：">{{
                  [0, 1, 3].indexOf(invoiceStatus) >= 0 ? '-' : invoiceOrderNum ? invoiceOrderNum : '-'
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请开票时间：">{{ orderDetail.invoiceApplyInfo.appliedTime }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="发票抬头：">{{ orderDetail.invoiceApplyInfo.title }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="统一社会信用代码：">{{
                  orderDetail.invoiceApplyInfo.taxpayerNo ? orderDetail.invoiceApplyInfo.taxpayerNo : '-'
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开户银行：">{{
                  orderDetail.invoiceApplyInfo.bankName ? orderDetail.invoiceApplyInfo.bankName : '-'
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开户帐号：">{{
                  orderDetail.invoiceApplyInfo.account ? orderDetail.invoiceApplyInfo.account : '-'
                }}</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="注册电话：">{{
                  orderDetail.invoiceApplyInfo.phone ? orderDetail.invoiceApplyInfo.phone : '-'
                }}</el-form-item>
              </el-col>

              <el-col :span="12" v-if="orderDetail.invoiceIdentification == 4">
                <el-form-item label="手机号码："
                  >{{ orderDetail.invoiceApplyInfo.contactPhone ? orderDetail.invoiceApplyInfo.contactPhone : '-' }}
                </el-form-item>
              </el-col>

              <el-col :span="12" v-if="orderDetail.invoiceIdentification == 2 && isPTFPXX">
                <el-form-item label="手机号码：">{{
                  orderDetail.invoiceApplyInfo.contactPhone ? orderDetail.invoiceApplyInfo.contactPhone : '-'
                }}</el-form-item>
              </el-col>

              <el-col :span="12" v-if="orderDetail.invoiceIdentification == 4">
                <el-form-item label="电子邮箱：">{{ orderDetail.email ? orderDetail.email : '-' }}</el-form-item>
              </el-col>

              <el-col :span="12" v-if="orderDetail.invoiceIdentification == 2 && isPTFPXX">
                <el-form-item label="电子邮箱：">{{ orderDetail.email ? orderDetail.email : '-' }}</el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="注册地址：">{{
                  orderDetail.invoiceApplyInfo.address ? orderDetail.invoiceApplyInfo.address : '-'
                }}</el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item label="发票备注："
                  >{{ orderDetail.invoiceApplyInfo.remark ? orderDetail.invoiceApplyInfo.remark : '-' }}
                </el-form-item>
              </el-col>

              <el-col
                :span="24"
                v-if="(invoiceStatus == 2 && orderDetail.invoiceApplyInfo.invoiceType == 1) || invoiceStatus == 4"
              >
                <el-form-item label=" " v-if="orderDetail.invoiceIdentification != 4">
                  <el-button
                    v-for="btnItem in orderDetail.invoiceApplyInfoList"
                    :key="btnItem"
                    type="primary"
                    :disabled="invoiceStatus == 4"
                    @click="downloadInvoice(btnItem)"
                    >下载发票</el-button
                  >
                </el-form-item>
              </el-col>
              <el-col
                :span="24"
                v-if="(invoiceStatus == 2 && orderDetail.invoiceApplyInfo.invoiceType == 2) || invoiceStatus == 4"
              >
                <el-form-item label=" ">
                  <el-alert type="info" show-icon :closable="false" class="m-alert">
                    发票查询地址：
                    <a href="https://inv-veri.chinatax.gov.cn/" target="_blank" class="f-link">
                      https://inv-veri.chinatax.gov.cn/
                    </a>
                  </el-alert>
                </el-form-item>
              </el-col>
            </el-form>
            <!--未填写发票信息-->
            <el-form label-width="140px" class="m-text-form f-pt10 f-pb20" v-if="!orderDetail.invoiceApplyInfo">
              <el-col :span="12">
                <el-form-item label="是否需要发票：">{{ orderDetail.isInvoiceApplied ? '是' : '否' }}</el-form-item>
              </el-col>
            </el-form>
            <div class="m-tit" v-if="orderDetail.invoiceApplyInfo && orderDetail.invoiceApplyInfo.shippingMethod != 0">
              <span class="tit-txt">配送信息</span>
            </div>
            <el-form
              label-width="140px"
              class="m-text-form f-pt10 f-pb20"
              v-if="orderDetail.invoiceApplyInfo && orderDetail.invoiceApplyInfo.shippingMethod != 0"
            >
              <el-col :span="12">
                <el-form-item label="配送方式：">{{
                  orderDetail.invoiceApplyInfo.shippingMethod == 2 ? '邮寄' : '自取'
                }}</el-form-item>
              </el-col>
              <el-col :span="12" v-if="orderDetail.invoiceApplyInfo.shippingMethod == 2">
                <el-form-item label="快递公司：">{{
                  orderDetail.invoiceApplyInfo.deliveryInfo && orderDetail.invoiceApplyInfo.deliveryInfo.express
                    ? orderDetail.invoiceApplyInfo.deliveryInfo.express.expressCompanyName
                    : '-'
                }}</el-form-item>
              </el-col>
              <el-col :span="12" v-if="orderDetail.invoiceApplyInfo.shippingMethod == 2">
                <el-form-item label="收货地址：">{{
                  orderDetail.invoiceApplyInfo.deliveryAddress.address
                }}</el-form-item>
              </el-col>
              <el-col :span="12" v-if="orderDetail.invoiceApplyInfo.shippingMethod == 1">
                <el-form-item label="领取地点">{{
                  orderDetail.invoiceApplyInfo.takePoint.pickupLocation
                }}</el-form-item>
              </el-col>
              <el-col :span="12" v-if="orderDetail.invoiceApplyInfo.shippingMethod == 1">
                <el-form-item label="领取时间：">{{ orderDetail.invoiceApplyInfo.takePoint.pickupTime }}</el-form-item>
              </el-col>
              <el-col :span="12" v-if="orderDetail.invoiceApplyInfo.shippingMethod == 1">
                <el-form-item label="备注：">{{ orderDetail.invoiceApplyInfo.takePoint.remark }}</el-form-item>
              </el-col>
              <el-col :span="12" v-if="orderDetail.invoiceApplyInfo.shippingMethod == 2">
                <el-form-item label="收件人：">{{
                  orderDetail.invoiceApplyInfo.deliveryAddress.consignee
                }}</el-form-item>
              </el-col>
              <el-col :span="12" v-if="orderDetail.invoiceApplyInfo.shippingMethod == 2">
                <el-form-item label="手机号：">{{ orderDetail.invoiceApplyInfo.deliveryAddress.phone }}</el-form-item>
              </el-col>
              <el-col :span="12" v-if="orderDetail.invoiceApplyInfo.shippingMethod == 2">
                <el-form-item label="运单号：">
                  {{
                    orderDetail.invoiceApplyInfo.deliveryInfo && orderDetail.invoiceApplyInfo.deliveryInfo.express
                      ? orderDetail.invoiceApplyInfo.deliveryInfo.express.expressNo
                      : '-'
                  }}
                  <hb-copy
                    :content="
                      orderDetail.invoiceApplyInfo.deliveryInfo && orderDetail.invoiceApplyInfo.deliveryInfo.express
                        ? orderDetail.invoiceApplyInfo.deliveryInfo.express.expressNo
                        : '-'
                    "
                  ></hb-copy>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="orderDetail.invoiceApplyInfo.shippingMethod == 2">
                <el-form-item label="发货时间：">{{
                  orderDetail.invoiceApplyInfo.deliveryInfo &&
                  orderDetail.invoiceApplyInfo.deliveryInfo.deliveryStatusChangeTime
                    ? orderDetail.invoiceApplyInfo.deliveryInfo.deliveryStatusChangeTime.shipped
                    : ''
                }}</el-form-item>
              </el-col>
            </el-form>
          </div>
          <div class="right f-plr20 f-ptb10">
            <div class="m-tit">
              <span class="tit-txt">购买人信息</span>
            </div>
            <el-form label-width="120px" class="m-text-form is-column f-pt10 f-pb20">
              <el-form-item label="购买人：">{{
                orderDetail.buyerDetail && orderDetail.buyerDetail.userName ? orderDetail.buyerDetail.userName : '-'
              }}</el-form-item>
              <el-form-item label="登录账号：" v-if="queryShowLoginAccount.isShowLoginAccount">{{
                orderDetail.buyerDetail && orderDetail.buyerDetail.loginAccount
                  ? orderDetail.buyerDetail.loginAccount
                  : '-'
              }}</el-form-item>
              <el-form-item label="证件号：">{{
                orderDetail.buyerDetail && orderDetail.buyerDetail.idCard ? orderDetail.buyerDetail.idCard : '-'
              }}</el-form-item>
              <el-form-item label="手机号：">{{
                orderDetail.buyerDetail && orderDetail.buyerDetail.phone ? orderDetail.buyerDetail.phone : '-'
              }}</el-form-item>
              <!-- <el-form-item label="工种：">{{
                orderDetail.buyerDetail && orderDetail.buyerDetail.jobCategoryName
                  ? orderDetail.buyerDetail.jobCategoryName
                  : '-'
              }}</el-form-item> -->
            </el-form>
            <div class="m-tit">
              <span class="tit-txt">支付信息</span>
            </div>
            <el-form label-width="120px" class="m-text-form is-column f-pt10 f-pb20">
              <el-form-item label="支付方式：">
                <span
                  v-if="
                    orderDetail.saleChannel &&
                    orderDetail.saleChannel === SaleChannelEnum.huayi &&
                    orderDetail.basicData &&
                    orderDetail.basicData.channelType === 5
                  "
                  >网上报名-华医网</span
                >
                <span v-else>{{ getPaymentWay(orderDetail.basicData, orderDetail.payInfo) }}</span>
                <!--                通过交易流水号判断-->
                <el-tag
                  type="warning"
                  style="margin-left: 10px"
                  v-if="
                    orderDetail.saleChannel &&
                    orderDetail.saleChannel === SaleChannelEnum.huayi &&
                    orderDetail.basicData &&
                    orderDetail.basicData.channelType === 5 &&
                    isThirdParty
                  "
                  >第三方收款</el-tag
                >
              </el-form-item>
              <el-form-item label="交易号：">{{ orderDetail.payInfo ? orderDetail.payInfo.flowNo : '-' }}</el-form-item>
              <el-form-item label="付款时间：">{{
                orderDetail.basicData && orderDetail.basicData.orderPaymentStatusChangeTime
                  ? orderDetail.basicData.orderPaymentStatusChangeTime.paying
                  : '-'
              }}</el-form-item>
              <el-form-item label="付款成功时间：">{{
                orderDetail.basicData && orderDetail.basicData.orderPaymentStatusChangeTime
                  ? orderDetail.basicData.orderPaymentStatusChangeTime.paid
                  : '-'
              }}</el-form-item>
            </el-form>
          </div>
        </el-card>
        <!--购买清单-->
        <el-card shadow="never" class="m-card is-header f-mb15" id="purchaseList">
          <div class="f-plr20 f-pt10">
            <div class="m-tit is-small">
              <span class="tit-txt">购买清单</span>
            </div>
            <el-table class="m-table is-header">
              <el-table-column label="物品名称" min-width="300"></el-table-column>
              <el-table-column label="学时" width="150" align="center"></el-table-column>
              <el-table-column label="数量" width="150" align="center"></el-table-column>
              <el-table-column label="实付金额(元)" width="150" align="right"></el-table-column>
              <el-table-column label="订单状态" width="120"></el-table-column>
              <el-table-column label="售后状态" width="120" align="center"></el-table-column>
              <el-table-column label="售后" width="150" align="center"></el-table-column>
            </el-table>

            <div v-for="item in tableData" :key="item.subOrderNo">
              <el-alert type="info" :closable="false" class="m-alert f-mt10">
                <div class="f-flex">
                  <i class="iconfont icon-fangan f-cb f-mr5"></i>
                  <div class="f-flex-sub">
                    <span class="f-fb f-cb"
                      >培训方案：
                      <el-tag type="primary" effect="dark" size="mini" v-if="commoditySchemeType(item)">
                        {{ commoditySchemeType(item) }}
                      </el-tag>
                      {{ item.deliveryCommoditySku.saleTitle }}
                    </span>
                    <el-tag class="f-ml10" size="small" v-if="item && item.deliveryCommoditySku.skuProperty.year"
                      >{{ item.deliveryCommoditySku.skuProperty.year.skuPropertyValueId }}年</el-tag
                    >
                    <el-tag class="f-ml10" size="small" v-if="item && item.deliveryCommoditySku.skuProperty.industry">{{
                      item.deliveryCommoditySku.skuProperty.industry.skuPropertyValueName
                    }}</el-tag>
                    <!--
                    <el-tag
                    class="f-ml10"
                    size="small"
                    v-if="item && item.deliveryCommoditySku.skuProperty.technicalGrade"
                    >{{ item.deliveryCommoditySku.skuProperty.technicalGrade.skuPropertyValueName }}</el-tag
                  > -->
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item && item.deliveryCommoditySku.skuProperty.province.skuPropertyValueName"
                      >{{
                        '【' +
                        setDistrictSkuName(item.deliveryCommoditySku.skuProperty.province.skuPropertyValueName, 1) +
                        setDistrictSkuName(item.deliveryCommoditySku.skuProperty.city.skuPropertyValueName, 2) +
                        setDistrictSkuName(item.deliveryCommoditySku.skuProperty.county.skuPropertyValueName, 3) +
                        '】'
                      }}</el-tag
                    >
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item && item.deliveryCommoditySku.skuProperty.trainingProfessional"
                      >{{ item.deliveryCommoditySku.skuProperty.trainingProfessional.skuPropertyValueName }}</el-tag
                    >
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item && item.deliveryCommoditySku.skuProperty.subjectType"
                      >{{ item.deliveryCommoditySku.skuProperty.subjectType.skuPropertyValueName }}</el-tag
                    >
                    <!-- 职业卫生行业 -->
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item && item.deliveryCommoditySku.skuProperty.trainingObject"
                      >{{ item.deliveryCommoditySku.skuProperty.trainingObject.skuPropertyValueName }}</el-tag
                    >
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item && item.deliveryCommoditySku.skuProperty.positionCategory"
                      >{{ item.deliveryCommoditySku.skuProperty.positionCategory.skuPropertyValueName }}</el-tag
                    >
                    <!-- 工勤行业 -->
                    <el-tag class="f-ml10" size="small" v-if="item && item.deliveryCommoditySku.skuProperty.jobLevel">{{
                      item.deliveryCommoditySku.skuProperty.jobLevel.skuPropertyValueName
                    }}</el-tag>
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item && item.deliveryCommoditySku.skuProperty.jobCategory"
                      >{{ item.deliveryCommoditySku.skuProperty.jobCategory.skuPropertyValueName }}</el-tag
                    >
                    <!-- 教师行业 -->
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item && item.deliveryCommoditySku.skuProperty.learningPhase"
                      >{{ item.deliveryCommoditySku.skuProperty.learningPhase.skuPropertyValueName }}</el-tag
                    >
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item && item.deliveryCommoditySku.skuProperty.discipline"
                      >{{ item.deliveryCommoditySku.skuProperty.discipline.skuPropertyValueName }}</el-tag
                    >
                    <!-- 药师行业 -->
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item && item.deliveryCommoditySku.skuProperty.certificatesType"
                      >{{ item.deliveryCommoditySku.skuProperty.certificatesType.skuPropertyValueName }}</el-tag
                    >
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item && item.deliveryCommoditySku.skuProperty.practitionerCategory"
                      >{{ item.deliveryCommoditySku.skuProperty.practitionerCategory.skuPropertyValueName }}</el-tag
                    >
                  </div>
                </div>
              </el-alert>
              <el-table :data="[item]" :key="item.subOrderNo" max-height="500px" class="m-table is-body">
                <el-table-column min-width="300">
                  <template slot-scope="scope">
                    <div class="f-flex f-align-start">
                      <div style="display: flex; flex-direction: column">
                        <el-tag size="small" v-if="scope.row.exchangeStatus != 0">换班</el-tag>
                        <el-tag type="warning" size="small" v-if="scope.row.isExchangeIssue">换期</el-tag>
                      </div>
                      <div class="f-flex-sub f-mt5 f-ml5">
                        <p>
                          {{ scope.row.deliveryCommoditySku.saleTitle
                          }}<el-tag type="success" size="small" class="f-ml10" v-if="specialSubject.saleChannel == 2"
                            >专题</el-tag
                          >
                          <el-tag type="success" size="small" class="f-ml10" v-if="huayiTag(scope.row)">华医网</el-tag>
                        </p>
                        <p v-if="specialSubject.saleChannelName">专题名称:{{ specialSubject.saleChannelName }}</p>
                        <p
                          v-if="
                            scope.row.deliveryCommoditySku.issueInfo &&
                            scope.row.deliveryCommoditySku.issueInfo.issueName
                          "
                        >
                          培训期别：{{ scope.row.deliveryCommoditySku.issueInfo.issueName }}
                        </p>
                        <template
                          v-if="$hasPermission('queryRelevancyOrder')"
                          desc="详情-查看关联换班订单"
                          query
                          actions="isShowAssociatedShift,@AssociatedShift"
                        >
                          <a
                            class="f-link f-underline f-cb f-f13"
                            @click="isShowAssociatedShift(scope.row.subOrderNo)"
                            v-if="scope.row.exchangeStatus || item.isExchangeIssue"
                            >查看关联售后订单</a
                          >
                        </template>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column width="150" align="center">
                  <template slot-scope="scope">{{ scope.row.deliveryCommoditySku.resource.period }}</template>
                </el-table-column>
                <el-table-column width="150" align="center">
                  <template slot-scope="scope">{{ scope.row.quantity }}</template>
                </el-table-column>
                <el-table-column width="150" align="center">
                  <template slot-scope="scope"
                    ><span class="f-cr">{{ scope.row.amount }}</span>
                    <el-tag type="warning" size="mini" class="f-ml5" v-if="scope.row.useDiscount"
                      >优惠申请</el-tag
                    ></template
                  >
                </el-table-column>
                <el-table-column width="120">
                  <template>
                    <el-badge v-if="orderDetail.orderStatue == 3" is-dot type="success" class="badge-status"
                      >已发货</el-badge
                    >
                    <el-badge v-if="orderDetail.orderStatue == 1" is-dot type="primary" class="badge-status"
                      >支付中</el-badge
                    >
                    <el-badge v-if="orderDetail.orderStatue == 0" is-dot type="warning" class="badge-status"
                      >等待付款</el-badge
                    >
                    <el-badge v-if="orderDetail.orderStatue == 2" is-dot type="primary" class="badge-status"
                      >开通中</el-badge
                    >
                    <el-badge v-if="orderDetail.orderStatue == 4" is-dot type="info" class="badge-status"
                      >交易关闭</el-badge
                    >
                  </template>
                </el-table-column>
                <el-table-column width="120" align="center">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.exchangeStatus == 3" type="success">换班成功</el-tag>
                    <el-tag type="success" v-if="scope.row.isExchangeIssue">换期成功</el-tag>
                    <el-tag type="warning" v-if="scope.row.exchangeStatus == 1 || scope.row.exchangeStatus == 2"
                      >换班中</el-tag
                    >
                    <el-tag v-if="![1, 2, 3].includes(scope.row.exchangeStatus) && !scope.row.isExchangeIssue"
                      >-</el-tag
                    >

                    <p class="f-mt5" v-if="scope.row.exchangeStatus == 3">
                      <template
                        v-if="$hasPermission('queryChangeClassDetail')"
                        desc="详情-查看换班/换期详情"
                        query
                        actions="isShowChangeShiftsDetailDialog,ChangeShiftsDetail"
                      >
                        <a
                          href="#"
                          class="f-link f-underline f-cb f-f12"
                          @click="isShowChangeShiftsDetailDialog(scope.row.subOrderNo)"
                          >查看详情</a
                        >
                      </template>
                    </p>
                  </template>
                </el-table-column>
                <el-table-column width="150" align="center">
                  <template slot-scope="scope">
                    <template v-if="refundStatusText(scope.row)">
                      <el-tag type="warning">{{ refundStatusText(scope.row) }}</el-tag
                      ><br />
                    </template>

                    <template
                      v-if="$hasPermission('refundOrder')"
                      desc="详情-退款操作"
                      actions="@RefundDialog,@RefundDialogDiff"
                    >
                      <el-button
                        type="text"
                        mutation
                        size="mini"
                        v-if="isShowReturnOrderMethod"
                        @click="toRefund(scope.row)"
                        >发起退货/款</el-button
                      >
                    </template>
                    <el-button type="text" mutation size="mini" @click="seeRecord(scope.row)">查看记录</el-button>
                    <!-- <el-button type="text" size="mini" v-if="orderDetail.orderStatue !== 3">-</el-button>
                    <template
                      v-if="$hasPermission('refundDetail')"
                      desc="详情-查看退款详情"
                      query
                      actions="@hbfe/jxjy-admin-trade/src/refund/personal/detail.vue"
                    >
                      <el-button
                        type="text"
                        size="mini"
                        v-if="isShowReturnOrderDetail(scope.row) && scope.row.returnStatus !== 0"
                        @click="returnOrderDetailClick(scope.row)"
                        >退款详情</el-button
                      >
                    </template> -->
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <div class="m-order-sum f-mtb30">
              <div class="item">
                共 <i class="f-cr">{{ totalPeriod }}</i> 学时，商品总额：
                <span class="price"
                  >¥ <i class="num">{{ totalPrice }}</i></span
                >
              </div>
              <div class="item" v-if="orderDetail.subOrderItems && orderDetail.subOrderItems[0].commodityAuthInfo">
                分销金额：
                <span class="price"
                  >¥
                  <i class="num">{{
                    orderDetail.subOrderItems && orderDetail.subOrderItems.length
                      ? orderDetail.subOrderItems[0].amount
                      : ''
                  }}</i></span
                >
              </div>
              <div
                :class="[
                  orderDetail.returnOrderDetail && orderDetail.returnOrderDetail.basicData ? 'item' : 'sum-price'
                ]"
              >
                实付金额：<span class="price"
                  >¥ <i class="num">{{ orderDetail.basicData ? orderDetail.basicData.amount : '' }}</i></span
                >
              </div>

              <div
                class="sum-price"
                v-if="
                  orderDetail.returnOrderDetail &&
                  orderDetail.returnOrderDetail.basicData &&
                  orderDetail.returnOrderDetail.basicData.returnOrderStatus !== 11
                "
              >
                已退款金额：<span class="price">
                  <i class="num">{{
                    orderDetail.subRefundAmount(orderDetail.subOrderItems[0])
                      ? '¥' + orderDetail.subRefundAmount(orderDetail.subOrderItems[0])
                      : '-'
                  }}</i></span
                >
              </div>
              <div class="sum-price" v-if="returnOrderStatus">
                已退款金额：<span class="price">
                  <i class="num">{{ subRefundAmount }}</i></span
                >
              </div>
            </div>
          </div>
        </el-card>
      </div>
      <associated-shift :exchangeOrderList="exchangeOrderList" ref="AssociatedShift"></associated-shift>
      <!-- <refund-dialog
        :refundDetail="refundDetail"
        :refundReasonList="refundReasonList"
        :isForce="isForce"
        :foreseReasonArr="foreseReasonArr"
        :returnOrderType="returnOrderType"
        :orderDetail="orderDetail"
        @refundClick="refundClick"
        ref="RefundDialog"
      ></refund-dialog> -->
      <change-shifts-detail
        ref="ChangeShiftsDetail"
        :changeShiftsDetailList="changeShiftsDetailList"
      ></change-shifts-detail>
      <invoice-dialog
        ref="InvoiceDialog"
        :paperPdfUrl="curItem.blueFilePath"
        :ofdUrl="curItem.blueFileOfdPath"
        :xmlUrl="curItem.blueFileXmlPath"
      ></invoice-dialog>
      <refund-record ref="refundRecordRef" :isHuaYiWang="isHywSpecialScheme"></refund-record>
      <refund-dialog
        ref="refundDialogRef"
        :selectClassRuleName="getSchemeType(skuDetail)"
        :orderDetail="orderDetail"
        @refresh="refresh"
      ></refund-dialog>
      <refund-dialog-diff
        ref="refundDialogDiffRef"
        :selectClassRuleName="getSchemeType(skuDetail)"
        :orderDetail="orderDetail"
        @refresh="refresh"
      ></refund-dialog-diff>
    </template>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import { Page, UiPage } from '@hbfe/common'
  import AssociatedShift from '@hbfe/jxjy-admin-trade/src/diff/zztt/order/personal/components/associated-shift.vue'
  // import RefundDialog from '@hbfe/jxjy-admin-trade/src/diff/zztt/order/personal/components/refund-dialog.vue'
  import ChangeShiftsDetail from '@hbfe/jxjy-admin-trade/src/diff/zztt/order/personal/components/change-shifts-detail.vue'
  import InvoiceDialog from '@hbfe/jxjy-admin-trade/src/order/personal/components/invoice-dialog.vue'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import QueryOrderDetail from '@api/service/management/trade/single/order/query/QueryOrderDetail'
  import {
    OrderBasicDataResponse,
    PayInfoResponse,
    ReturnReasonInfoResponse,
    SchemeResourceResponse,
    SubOrderResponse
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { ResourceEnum } from '@api/service/common/enums/trade/ResourceTypes'
  import OrderInvoiceApplyInfoResponseVo from '@api/service/customer/trade/single/query/query-customer-user-order/query-order-detail/vo/OrderInvoiceApplyInfoResponseVo'
  import OrderDetailVo from '@api/service/management/trade/single/order/query/vo/UserOrderDetailVo'
  import ExchangeOrderResponseVo from '@api/service/management/trade/single/order/query/vo/ExchangeOrderResponseVo'
  import { ExchangeOrderRecordVo } from '@api/service/management/trade/single/order/query/vo/ExchangeOrderRecordVo'
  import { ForceReasonEnum } from '@api/service/management/trade/single/order/enum/ForceReasonEnum'
  import QueryExchangeCommodity from '@api/service/management/trade/single/order/query/QueryExchangeCommodity'
  import QueryExchangeDetailAccordingOrder from '@api/service/management/trade/single/order/query/QueryExchangeDetailAccordingOrder'
  import QueryRefundReasonList from '@api/service/management/trade/single/order/query/QueryRefundReasonList'
  import MutationCreateReturnOrder from '@api/service/management/trade/single/order/mutation/MutationCreateReturnOrder'
  import { SaleChannelEnum } from '@api/service/diff/management/zztt/trade/enums/SaleChannelType'
  import SchemeType, { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import FileModule from '@api/service/common/file/FileModule'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import RefundRecord from '@hbfe/jxjy-admin-trade/src/diff/zztt/order/components/refund-record.vue'
  import RefundDialogDiff from '@hbfe/jxjy-admin-trade/src/diff/zztt/order/components/refund-dialog.vue'
  import RefundDialog from '@hbfe/jxjy-admin-trade/src/order/components/refund-dialog.vue'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplicationDiff } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import OrderRefundType from '@api/service/common/return-order/enums/OrderRefundType'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import CommodityRefundStatus from '@api/service/common/return-order/enums/CommodityRefundStatus'

  @Component({
    components: {
      AssociatedShift,
      RefundDialog,
      ChangeShiftsDetail,
      InvoiceDialog,
      RefundRecord,
      RefundDialogDiff
    }
  })
  export default class extends Vue {
    @Ref('refundRecordRef') refundRecordRef: RefundRecord
    @Ref('refundDialogRef') refundDialogRef: RefundDialog
    @Ref('refundDialogDiffRef') refundDialogDiffRef: RefundDialogDiff
    @Ref('AssociatedShift') AssociatedShift: AssociatedShift
    @Ref('RefundDialog') RefundDialog: RefundDialog
    @Ref('ChangeShiftsDetail') ChangeShiftsDetail: ChangeShiftsDetail
    tableData = new Array<SubOrderResponse>()
    page = new UiPage()
    @Ref('InvoiceDialog') InvoiceDialog: InvoiceDialog
    skuDetail = new SchemeResourceResponse() // sku详情 方案类型
    orderDetail = new OrderDetailVo()
    invoiceStatus = -1
    invoiceOrderNum = ''
    // 退款类型，1为，2为，3为退货且退款，默认退货且退款
    returnOrderType = 3
    refundDetail = new SubOrderResponse() // 退款弹窗详情
    refundReasonList = new Array<ReturnReasonInfoResponse>() // 退款原因
    changeShiftsDetailList = new Array<ExchangeOrderRecordVo>() // 换班详情列表
    isForce = false // 是否强制退款
    foreseReasonArr = new Array<ForceReasonEnum>()
    curItem = new OrderInvoiceApplyInfoResponseVo()
    SaleChannelEnum = SaleChannelEnum
    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    OrderRefundType = OrderRefundType // 退款类型
    CommodityRefundStatus = CommodityRefundStatus // 退款类型

    paymentHash = [
      { Web: 'Web端' },
      { IOS: 'IOS端' },
      { Android: '安卓端' },
      { WechatMini: '微信小程序' },
      { WechatOfficial: '微信公众号' },
      { ExternalSystemManage: '外部管理系统' },
      { H5: 'H5' }
    ]
    isShowAssociatedShift(id: string) {
      this.queryExchangeOrder(id)
    }

    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    isShowChangeShiftsDetailDialog(id: string) {
      this.queryExchangeDetail(id)
    }
    // 订单详情查询实例对象
    orderDetailObj = new QueryOrderDetail()
    // 关联换班订单实例
    queryExchangeOrderObj = new QueryExchangeCommodity()
    // 换货单详情根据子订单号查询实例
    queryExchangeDetailByOrderObj = new QueryExchangeDetailAccordingOrder()
    // 发起退款实例
    mutationCreateReturnOrderObj = new MutationCreateReturnOrder()
    exchangeOrderList = new Array<ExchangeOrderResponseVo>() // 换班订单列表
    SchemeTypeEnum = SchemeTypeEnum
    //是否 专题登录
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)

    /**
     * 获取华医网特殊方案
     */
    get hywSchemeList(): string[] {
      const list: { zzttSchemeId: string }[] = JSON.parse(
        ConfigCenterModule.getFrontendApplicationDiff(frontendApplicationDiff.hywSchemeId)
      )
      return list.map((item) => item.zzttSchemeId)
    }
    get isThirdParty() {
      const pattern = /THIRD_PARTY_PLATFORM_PAY/
      return pattern.test(this.orderDetail?.payInfo?.flowNo)
    }
    /**
     * 是否是华医网特殊方案
     */
    get isHywSpecialScheme() {
      const schemeIds = this.hywSchemeList
      return schemeIds.includes(this.skuDetail.schemeId)
    }

    get isPTFPXX() {
      return (
        this.orderDetail.invoiceApplyInfo.invoiceMethod == 2 &&
        this.orderDetail.invoiceApplyInfo.invoiceType == 1 &&
        this.orderDetail.invoiceApplyInfo.invoiceCategory == 2
      )
    }
    get returnOrderStatus() {
      if (this.orderDetail.returnOrderDetailList?.length) {
        const data = this.orderDetail.returnOrderDetailList.find((item) => item.basicData.returnOrderStatus !== 11)
        return !!data?.returnOrderNo
      }
      return false
    }

    get subRefundAmount() {
      const { subOrderItems } = this.orderDetail
      if (subOrderItems && subOrderItems.length) {
        const amount = subOrderItems.reduce((pre, cur) => {
          return CalculatorObj.add(cur.amount, pre)
        }, 0)
        const leftAmount = subOrderItems.reduce((pre, cur) => {
          return CalculatorObj.add(cur.leftAmount, pre)
        }, 0)
        return '￥' + CalculatorObj.subtract(amount, leftAmount)
      }
      return '-'
    }
    // 总学时
    get totalPeriod() {
      const { subOrderItems } = this.orderDetail
      if (subOrderItems && subOrderItems.length) {
        return subOrderItems.reduce((pre, cur) => {
          return CalculatorObj.add(Number((cur.deliveryCommoditySku.resource as SchemeResourceResponse).period), pre)
        }, 0)
      }
      return '--'
    }

    /**
     * 商品总金额
     */
    get totalPrice() {
      const { subOrderItems } = this.orderDetail

      if (subOrderItems && subOrderItems.length) {
        return subOrderItems.reduce((pre, cur) => {
          return CalculatorObj.add(Number(cur.deliveryCommoditySku.price), pre)
        }, 0)
      }
      return '--'
    }

    // 是否有上级分销商
    get subDistributionId() {
      return (
        this.orderDetail.subOrderItems &&
        this.orderDetail.subOrderItems[0].commodityAuthInfo &&
        this.orderDetail.subOrderItems[0].commodityAuthInfo.superiorDistributorId
      )
    }

    // 判断是否有身份证（分销商没有身份证只有手机号）
    get hasIdCard() {
      if (this.orderDetail.creatorDetail) {
        if (this.orderDetail.creatorDetail.idCard) {
          return this.orderDetail.creatorDetail.idCard
        } else if (this.orderDetail.creatorDetail.phone) {
          return this.orderDetail.creatorDetail.phone
        } else {
          return '-'
        }
      } else {
        return '-'
      }
    }
    // 封装逻辑到计算属性中
    refundStatusText(item: SubOrderResponse) {
      if (!this.orderDetail || !this.orderDetail.subOrderRefundStatus(item) || !CommodityRefundStatus.map) {
        return null // 确保空值情况下不会报错
      }
      const status = this.orderDetail.subOrderRefundStatus(item)
      const refundMap = CommodityRefundStatus.map

      // 如果 map 中不存在对应值，返回默认值
      return refundMap.has(status) ? refundMap.get(status) : ''
    }
    // 获取分销商
    getDistribution(item: string) {
      const subOrderItems = this.orderDetail.subOrderItems

      // 检查 subOrderItems 是否存在且至少有一个元素
      if (!subOrderItems || subOrderItems.length === 0) {
        return '-'
      }

      const commodityAuthInfo = subOrderItems[0].commodityAuthInfo

      if (!commodityAuthInfo) {
        return '-'
      }

      return item === 'distributorName' ? commodityAuthInfo.distributorName : commodityAuthInfo.superiorDistributorName
    }
    /**
     * 获取培训方案类型
     */
    getSchemeType(row: SchemeResourceResponse) {
      return SchemeType.getSchemeType(row.schemeType, true)
    }

    /**
     * 商品方案类型
     */
    get commoditySchemeType() {
      return (row: SubOrderResponse) => {
        const currentCommoditySku = row.currentCommoditySku
        const schemeType = this.getSchemeType(currentCommoditySku.resource as SchemeResourceResponse)
        if (
          currentCommoditySku.skuProperty?.trainingWay &&
          currentCommoditySku.skuProperty?.trainingWay?.skuPropertyValueName
        ) {
          return `${currentCommoditySku.skuProperty.trainingWay.skuPropertyValueName}-${schemeType}`
        } else {
          return schemeType
        }
      }
    }

    /**
     * 页面初始化
     */
    async created() {
      this.orderDetailObj.orderNo = this.$route.params.id
      // 处理其他页面跳转到订单页锚点处
      const selectedId = localStorage.getItem('jumpToSelectedId')
      if (selectedId) {
        this.$nextTick(() => {
          this.jumpToSelectedId()
        })
      }
      await this.orderDetailObj.queryOrderDetailByOrderNo()
      // 订单详情
      this.orderDetail = this.orderDetailObj.orderDetail

      this.isForce = this.orderDetail.isForce
      this.foreseReasonArr = this.orderDetail.foreseReasonArr
      // 子订单数组
      this.tableData = this.orderDetailObj.orderDetail.subOrderItems

      // 根据资源类型判断模型类型
      const detailVo =
        this.orderDetailObj.orderDetail.subOrderItems &&
        this.orderDetailObj.orderDetail.subOrderItems[0].deliveryCommoditySku.resource
      if (detailVo) {
        const resourseType = detailVo.resourceType
        if (resourseType === ResourceEnum.Scheme) {
          // 方案类型
          this.skuDetail = detailVo as SchemeResourceResponse
        } else {
          this.skuDetail = detailVo as any
        }
      }
      const invoiceApplyInfo = this.orderDetailObj.orderDetail.invoiceApplyInfo

      if (invoiceApplyInfo) {
        this.invoiceStatus = (invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus
        this.invoiceOrderNum = (invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).orderNum
      }
    }
    async activated() {
      this.orderDetailObj.orderNo = this.$route.params.id
      // 处理其他页面跳转到订单页锚点处
      const selectedId = localStorage.getItem('jumpToSelectedId')
      if (selectedId) {
        this.$nextTick(() => {
          this.jumpToSelectedId()
        })
      }
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.orderDetailObj.queryFxOrderDetailByOrderNo()
      } else {
        await this.orderDetailObj.queryOrderDetailByOrderNo()
      }
      // 订单详情
      this.orderDetail = this.orderDetailObj.orderDetail
      this.isForce = this.orderDetail.isForce
      this.foreseReasonArr = this.orderDetail.foreseReasonArr
      // 子订单数组
      this.tableData = this.orderDetailObj.orderDetail.subOrderItems
      // 根据资源类型判断模型类型
      const detailVo =
        this.orderDetailObj.orderDetail.subOrderItems &&
        this.orderDetailObj.orderDetail.subOrderItems[0].deliveryCommoditySku.resource
      const resourseType = detailVo.resourceType
      if (resourseType) {
        if (resourseType === ResourceEnum.Scheme) {
          // 方案类型
          this.skuDetail = detailVo as SchemeResourceResponse
        } else {
          this.skuDetail = detailVo as any
        }
      }
      const invoiceApplyInfo = this.orderDetailObj.orderDetail.invoiceApplyInfo
      if (invoiceApplyInfo) {
        this.invoiceStatus = (invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus
        this.invoiceOrderNum = (invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).orderNum
      }

      this.refundReasonList = await QueryRefundReasonList.queryRefundReasonList()
    }
    async refresh() {
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.orderDetailObj.queryFxOrderDetailByOrderNo()
      } else {
        await this.orderDetailObj.queryOrderDetailByOrderNo()
      }
      // 订单详情
      this.orderDetail = this.orderDetailObj.orderDetail
      this.isForce = this.orderDetail.isForce
      this.foreseReasonArr = this.orderDetail.foreseReasonArr
      // 子订单数组
      this.tableData = this.orderDetailObj.orderDetail.subOrderItems
    }
    /**
     * 是否展示退款按钮 适配新原型
     */
    get isShowReturnOrderMethod() {
      return this.orderDetail.orderStatue == 3 && !this.isZtlogin
    }

    // 是否显示退货详情
    isShowReturnOrderDetail(row?: SubOrderResponse) {
      // 多个订单时判断退款列表中的订单号是否与当前订单号相同
      if (this.orderDetail.subOrderItems.length > 1 && row?.subOrderNo) {
        const returnOrderNo = this.orderDetail.returnOrderDetailList
          .filter((item) => item.UIReturnOrderStatue != 3)
          .map((item) => item.subOrderInfo.subOrderNo)
        if (returnOrderNo.includes(row?.subOrderNo)) {
          return true
        } else {
          return false
        }
      } else {
        if (this.orderDetail.returnOrderDetail) {
          if (
            this.orderDetail.returnOrderDetail.UIReturnOrderStatue != 3 &&
            this.orderDetail.returnOrderDetail.UIReturnOrderStatue != 2
          ) {
            return true
          }
        }
        return false
      }
    }
    /**
     *
     * @param name 地区名称
     * @param type 区域类型：1:省/2:市/3:区
     * @returns str 返回名称
     */
    setDistrictSkuName(name: string, type: number) {
      let str = ''
      if (!name) return str
      if (type === 1) {
        str = name
      } else {
        str = '>' + name
      }

      return str
    }

    // 是否显示专题
    get specialSubject() {
      return this.orderDetailObj.orderDetail
    }

    // 是否显示华医网
    get huayiTag() {
      // return this.specialSubject.saleChannel===3|| this.deliveryCommoditySku.ext?.['tppTypeId']
      return (item: SubOrderResponse) => {
        console.log(item, '华医网标识')
        return item.deliveryCommoditySku.tppTypeId || this.specialSubject.saleChannel === 3
      }
    }

    //支付方式
    getPaymentWay(basicData?: OrderBasicDataResponse, payInfo?: PayInfoResponse) {
      if (basicData && payInfo) {
        const { channelType, terminalCode } = basicData
        const {
          receiveAccount: { payChannelName }
        } = payInfo
        const terminalArr: Array<string> = this.paymentHash.reduce((result, item) => {
          const key = Object.keys(item)['0']
          if (key === terminalCode) {
            result.push(item[key])
          }
          return result
        }, [])
        //自主报名
        if (channelType === 1) {
          this.returnOrderType = 3
          const result: Array<string> = []
          result.push('网上报名')
          result.push(terminalArr['0'])
          // 适配威富通
          if (payChannelName == '威富通支付') {
            result.push('兴业银行(威富通)')
          } else {
            result.push(payChannelName)
          }
          return result.join('-')
        }
        //集体报名暂时没写
        if (channelType === 3) {
          this.returnOrderType = 1
          return '线下报名-导入开通'
        }
        if (channelType === 5) {
          return terminalArr['0']
        }
      } else {
        this.returnOrderType = 2
        return '-'
      }
    }
    //订单类型
    getOrderTypeInfo(code: number) {
      switch (code) {
        case 1:
          return '个人订单'
        case 2:
          return '集体报名'
        case 3:
          return '导入开通'
        case 4:
          return '集体报名-个人缴费'
        case 5:
          return '个人报名'
        default:
          return '-'
      }
    }
    /**
     *  跳转到锚点Id
     */
    jumpToSelectedId() {
      const selectedId = localStorage.getItem('jumpToSelectedId')
      if (selectedId) {
        const toElement = document.getElementById(selectedId)
        if (toElement) {
          toElement.scrollIntoView(true)
        }
        localStorage.removeItem('jumpToSelectedId')
      }
    }

    // 查询换班订单
    async queryExchangeOrder(id: string) {
      this.page.pageNo = 1
      this.page.pageSize = 100
      this.queryExchangeOrderObj.subOrderNo = id
      // try {
      //   let res
      //   if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
      //     res = await this.queryExchangeOrderObj.queryExchangeOrderListInDistributor(this.page)
      //   } else {
      //     res = await this.queryExchangeOrderObj.queryExchangeOrderList(this.page)
      //   }
      //   this.exchangeOrderList = res
      this.AssociatedShift.isShowDialog(id)
      // } catch (error) {
      //   console.log(error, '获取换班列表失败')
      // }
    }

    // 确认退款事件
    async refundClick(res: any) {
      if (res.isSuccess()) {
        this.$message.success('退款申请提交成功')
        await this.orderDetailObj.queryOrderDetailByOrderNo()
        // 订单详情
        this.orderDetail = this.orderDetailObj.orderDetail
        this.isForce = this.orderDetail.isForce
        this.foreseReasonArr = this.orderDetail.foreseReasonArr
        // 子订单数组
        this.tableData = this.orderDetailObj.orderDetail.subOrderItems
      } else {
        const message = res.code == 30010 ? res?.message || '退款失败' : '退款失败'
        this.$message.warning(message)
      }
    }

    // 下载发票
    async downloadInvoice(item: OrderInvoiceApplyInfoResponseVo) {
      await FileModule.applyResourceAccessToken()
      let url = ''
      let baseUrl = ''
      const pathArr: string[] = []
      if (item.blueFilePath) pathArr.push(item.blueFilePath)
      if (item.blueFileOfdPath) pathArr.push(item.blueFileOfdPath)
      if (item.blueFileXmlPath) pathArr.push(item.blueFileXmlPath)
      if (pathArr.length == 0) return this.$message.warning('暂无发票')
      if (pathArr.length == 1) {
        baseUrl = pathArr[0]
        if (baseUrl && baseUrl.substring(0, 4) !== '/mfs') {
          url = this.$util.imgUrlWithToken('/mfs' + baseUrl)
        } else {
          url = this.$util.imgUrlWithToken(baseUrl)
        }
        window.open(url, '_blank')
      } else {
        this.curItem = item
        this.InvoiceDialog.isShowDialog()
      }
    }

    // 换班详情查询
    async queryExchangeDetail(subOrderNo: string) {
      this.queryExchangeDetailByOrderObj.subOrderNo = subOrderNo
      let res
      if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
        res = await this.queryExchangeDetailByOrderObj.queryRefundOrderDetailInDistributor()
      } else {
        res = await this.queryExchangeDetailByOrderObj.queryRefundOrderDetail()
      }
      if (res.isSuccess()) {
        this.changeShiftsDetailList = this.queryExchangeDetailByOrderObj.exchangeOrderDetail?.records
        this.ChangeShiftsDetail.isShowDialog()
      } else {
        this.$message.warning('请求失败')
      }
    }

    // 去到退款详情页
    returnOrderDetailClick(row: SubOrderResponse) {
      let id = ''
      if (this.orderDetail.subOrderItems.length > 1) {
        const findSubRefundList = this.orderDetail.returnOrderDetailList.filter(
          (item) => item.subOrderInfo.subOrderNo == row.subOrderNo
        )

        if (findSubRefundList.length === 1) {
          id = findSubRefundList[0].returnOrderNo
        } else if (findSubRefundList.length > 1) {
          const findLast = findSubRefundList.find((it) => it.basicData.returnOrderStatus != 11)
          if (findLast) {
            id = findLast.returnOrderNo
          }
        }
      } else {
        id = this.orderDetail.returnOrderDetail.returnOrderNo
      }
      this.$router.push('/training/trade/refund/personal/detail/' + id)
    }

    /**
     * 查看记录
     */
    seeRecord(item: SubOrderResponse) {
      this.refundRecordRef.isShowDialog(item, this.orderDetail)
    }

    /**
     * 发起退款
     */
    toRefund(item?: SubOrderResponse) {
      const status = this.orderDetail.subOrderRefundStatus(item)
      if (status === 1) {
        this.$message.error('已退货并全额退款，无法发起售后！')
        return
      } else if (status === 9) {
        this.$message.error('存在未结束的退货/款流程，无法发起售后')
        return
      } else {
        if (this.isHywSpecialScheme) {
          this.refundDialogDiffRef.isShowDialog('single', item)
        } else {
          this.refundDialogRef.isShowDialog('single', item)
        }
      }
    }
  }
</script>

<style scoped></style>
