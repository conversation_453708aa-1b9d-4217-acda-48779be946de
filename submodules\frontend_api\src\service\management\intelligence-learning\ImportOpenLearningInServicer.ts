import MsImportOpen from '@api/ms-gateway/ms-importopen-v1'
import { Response, ResponseStatus } from '@hbfe/common'
import ImportOpenLearningParam from '@api/service/management/intelligence-learning/model/ImportOpenLearningParam'
import ImportOpenLearningBasic from '@api/service/management/intelligence-learning/model/ImportOpenLearningBasic'

export default class ImportOpenLearningInServicer extends ImportOpenLearningBasic {
  /**
   * 导入学员并开班
   */
  async doImportOpen(): Promise<Response<string>> {
    const result = new Response<string>()
    const { status, data } = await MsImportOpen.importOpenForVerify(ImportOpenLearningParam.to(this.params))
    if (status.isSuccess() && data) {
      result.status = new ResponseStatus(Number(data.code), data.message)
      result.data = data.batchOrderNo
    } else {
      result.status = new ResponseStatus(500, '导入失败')
    }
    return result
  }
}
