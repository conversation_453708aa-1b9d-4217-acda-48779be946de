import { BusinessDataDictionaryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'

export default class IdCardTypeVo {
  /**
   * id
   */
  id = ''
  /**
   * 码
   */
  code = 0
  /**
   * 类型名称
   */
  name = ''
  /**
   * 排序
   */
  sort = 0
  /**
   * 禁用
   */
  disabled = false

  static from(dto: BusinessDataDictionaryResponse) {
    const vo = new IdCardTypeVo()
    vo.id = dto.id
    vo.code = dto.code
    vo.name = dto.name
    vo.sort = dto.sort
    vo.disabled = dto.available === 0
    return vo
  }
}
