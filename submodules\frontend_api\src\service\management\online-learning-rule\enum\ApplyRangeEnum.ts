import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum ApplyRangeEnum {
  /**
   * 平台级别
   */
  platform = 1,
  /**
   * 地区级别
   */
  region,
  /**
   * 培训方案级别
   */
  scheme
}
export default class ApplyRangeType extends AbstractEnum<ApplyRangeEnum> {
  static enum = ApplyRangeEnum
  constructor(status?: ApplyRangeEnum) {
    super()
    this.current = status
    this.map.set(ApplyRangeEnum.platform, '平台级别')
    this.map.set(ApplyRangeEnum.region, '地区级别')
    this.map.set(ApplyRangeEnum.scheme, '培训方案级别')
  }
}
