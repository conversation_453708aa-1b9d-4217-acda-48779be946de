<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: Lin yt
 * @LastEditTime: 2022-06-10 11:37:21
-->
<route-meta>
{
"isMenu": true,
"title": "角色管理",
"sort": 2
}
</route-meta>
<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, NZFXS, NZFXSJCB, NZGYS } from '@/models/RoleTypes'
  import Role from '@hbfe/jxjy-admin-account/src/role/index.vue'
  @RoleTypeDecorator({
    query: [WXGLY, NZFXS, NZFXSJCB],
    create: [WXGLY, NZFXS, NZFXSJCB],
    detail: [WXGLY, NZFXS, NZFXSJCB],
    modify: [WXGLY, NZFXS, NZFXSJCB],
    remove: [WXGLY, NZFXS, NZFXSJCB]
  })
  export default class extends Role {}
</script>
