import CreateCoursewareDto from '@api/service/management/resource/courseware/mutation/dto/CreateCoursewareDto'
import MediaVo from '@api/service/management/resource/courseware/mutation/vo/MediaVo'

class CreateCoursewareVo extends CreateCoursewareDto {
  private summaryDuration() {
    this.duration = this.hours * 60 * 60 + this.minutes * 60 + this.seconds
  }

  get hours(): number {
    return this._hours
  }

  set hours(value: number) {
    this._hours = value
    this.summaryDuration()
  }

  get minutes(): number {
    return this._minutes
  }

  set minutes(value: number) {
    this._minutes = value
    this.summaryDuration()
  }

  get seconds() {
    return this._seconds
  }

  set seconds(value: number) {
    this._seconds = value
    this.summaryDuration()
  }

  private _hours: number
  private _minutes: number
  private _seconds: number

  mediaList: Array<MediaVo> = new Array<MediaVo>()

  remove(index: number) {
    this.mediaList.splice(index, 1)
  }

  add(media: MediaVo) {
    this.mediaList.push(media)
  }
}

export default CreateCoursewareVo
