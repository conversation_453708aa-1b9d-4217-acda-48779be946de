"""独立部署的微服务,K8S服务名:ms-scheme-learning-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询证书模板详情
		@param certificateTemplateId 证书模板id
		@return 证书模板对象
	"""
	getCertificateTemplate(certificateTemplateId:String):CertificateTemplateResponse
	"""获取当前网校的电子章
		@return
	"""
	getElectronicSealInServicer:ElectronicSealResponse
	"""查询当前分销商下的期别配置信息（无需鉴权）
		@param issueId 期别id
		@return
	"""
	getIssueConfigureInfoInDistributor(issueId:String):IssueConfigureInfoResponse @optionalLogin
	"""查询当前网校下期别配置信息（无需鉴权）
		@param issueId 期别id
		@return
	"""
	getIssueConfigureInfoInServicer(issueId:String):IssueConfigureInfoResponse @optionalLogin
	"""获取当前子项目下的已获得培训成果学时总数
		@param request
		@return
	"""
	getLearningPeriodStatisticsInSubProject(request:LearningPeriodStatisticsRequest):Long @optionalLogin
	"""获取我的培训方案配置
		<p>
		在学员中心使用，如果学员还在学习中会始终返回最新的方案配置，
		如果学员已培训通过，该接口会返回培训通过之前最新的培训方案配置‘快照’
		fixme：接口正确命名应该是getSchemeConfigInMySelf，之前确定规范过程中漏改了这个接口，前端已经对接并建议暂时不要改名
		@param qualificationId
		@param needField       以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
		返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
		}
		如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
	"""
	getMySchemeConfig(qualificationId:String,needField:[String]):SchemeConfigResponse
	"""获取我的培训方案配置
		<p>
		在学员中心使用，如果学员还在学习中会始终返回最新的方案配置，
		如果学员已培训通过，该接口会返回培训通过之前最新的培训方案配置‘快照’
		fixme：接口正确命名应该是getSchemeConfigInMySelf，之前确定规范过程中漏改了这个接口，前端已经对接并建议暂时不要改名
		@param request
		@param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
		返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
		}
		如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
		8.0通用以及差异化均未使用此接口，以此弃用
		可以替换使用：getMySchemeConfigInMySelf接口
	"""
	getMySchemeConfigByRequest(request:MySchemeConfigRequest,needField:[String]):SchemeConfigResponse
	"""获取期别规则配置信息详情（如果期别没配则则返回方案配置）
		无需鉴权
		todo 报到配置下期做
		@return
	"""
	getMySchemeIssueConfigInMySelf(issueId:String):IssueStudyConfigResponse
	"""获取当前服务商管辖地区下的地区学习统计报表合计
		SKU地区
		@param request
		@return
	"""
	getRegionLearningReportSummeryBySkuRegionInServicerManageRegion(request:LearningReportFormsRequest):LearningStatisticsResponse
	"""获取当前服务商下的地区学习统计报表合计
		@param request
		@return
	"""
	getRegionLearningReportSummeryInServicer(request:LearningReportFormsRequest):LearningStatisticsResponse @optionalLogin
	"""获取当前服务商下的地区学习统计报表合计
		@param request
		@return
	"""
	getRegionLearningReportSummeryInServicerJXJY(request:LearningReportFormsRequest):LearningStatisticsResponse @optionalLogin
	"""获取当前服务商管辖地区下的地区学习统计报表合计
		@param request
		@return
	"""
	getRegionLearningReportSummeryInServicerManageRegion(request:LearningReportFormsRequest):LearningStatisticsResponse
	getSchemeConfigByRequestInServicer(request:SchemeConfigRequest,needField:[String]):SchemeConfigResponse @optionalLogin
	"""获取当前服务商下的培训方案配置
		@param schemeId
		@param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
		返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
		}
		如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
		@return
	"""
	getSchemeConfigInDistributor(schemeId:String,needField:[String]):SchemeConfigResponse @optionalLogin
	"""获取当前服务商下的培训方案配置
		@param schemeId
		@param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
		返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
		}
		如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
		@return
	"""
	getSchemeConfigInServicer(schemeId:String,needField:[String]):SchemeConfigResponse @optionalLogin
	"""获取当前子项目下的培训方案配置
		@param schemeId
		@param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
		返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
		}
		如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
		@return
	"""
	getSchemeConfigInSubject(schemeId:String,needField:[String]):SchemeConfigResponse @optionalLogin
	"""查询当前网校下的期别配置详情
		@return
	"""
	getSchemeIssueConfigInServicer(issueId:String):SchemeIssueConfigResponse @optionalLogin
	"""查询当前网校下的期别下教学计划配置详情
		改为使用 getSchemeIssueRuleConfigInServicer
		@return
	"""
	getSchemeIssuePlanItemConfigInServicer(planId:String):SchemeIssuePlanItemConfigResponse @optionalLogin
	"""分页查询当前分销商指定期别下的教学计划项（无需鉴权）
		@param page
		@return
	"""
	getSchemeIssuePlanItemInDistributor(page:Page,request:SchemeIssuePlanItemRequest):IssuePlanItemResponsePage @page(for:"IssuePlanItemResponse") @optionalLogin
	"""分页查询当前网校指定期别下的教学计划项（无需鉴权）
		@param page
		@return
	"""
	getSchemeIssuePlanItemInServicer(page:Page,request:SchemeIssuePlanItemRequest):IssuePlanItemResponsePage @page(for:"IssuePlanItemResponse") @optionalLogin
	"""查询当前网校下的期别规则配置信息详情（如果期别没配则则返回方案配置）
		todo 报到配置下一期做
	"""
	getSchemeIssueRuleConfigInServicer(issueId:String):IssueStudyConfigResponse @optionalLogin
	"""获取当前服务商下的培训方案学习统计报表合计
		@param request
		@return
	"""
	getSchemeLearningReportSummeryInServicer(request:LearningReportFormsRequest):LearningStatisticsResponse @optionalLogin
	"""功能描述：获取学员培训方案配置
		@param qualificationId :
		@param needField       :
		@return : com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.graphql.response.StudentSchemeConfigSnapshotResponse
		@Author： wtl
		@Date： 2022/3/1 16:38
	"""
	getStudentSchemeConfig(qualificationId:String,needField:[String]):StudentSchemeConfigSnapshotResponse @optionalLogin
	"""获取当前服务商下的学员培训方案配置
		<p>
		如果学员还在学习中会始终返回最新的方案配置，
		如果学员已培训通过，该接口会返回培训通过之前最新的培训方案配置‘快照’
		@param qualificationId
		@param needField       以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
		返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
		}
		如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
		@return
	"""
	getStudentSchemeConfigInServicer(qualificationId:String,needField:[String]):StudentSchemeConfigSnapshotResponse @optionalLogin
	"""获取当前子项目下的学员培训方案配置
		<p>
		如果学员还在学习中会始终返回最新的方案配置，
		如果学员已培训通过，该接口会返回培训通过之前最新的培训方案配置‘快照’
		@param qualificationId
		@param needField       以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
		返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
		}
		如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
		@return
	"""
	getStudentSchemeConfigInSubProject(qualificationId:String,needField:[String]):StudentSchemeConfigSnapshotResponse @optionalLogin
	"""查询网校下学员方案学习详情（含网授+面网授班）
		@return
	"""
	getStudentSchemeLearningDetailInServicer(StudentNo:String):StudentSchemeLearningDetailResponse
	"""获取当前服务商下的学员培训方案学习详情
		@param qualificationId
	"""
	getStudentSchemeLearningInServicer(qualificationId:String):StudentSchemeLearningResponse @optionalLogin
	"""获取当前子项目下的学员培训方案学习详情
		@param qualificationId
	"""
	getStudentSchemeLearningInSubProject(qualificationId:String):StudentSchemeLearningResponse @optionalLogin
	"""分页查询当前网校下方案维度学员学习统计  合计
		@return
	"""
	getStudentSchemeLearningTotalStatisticsInServicer(request:StudentSchemeLearningStatisticsInOnlineRequest):SchemeLearningTotalStatisticsInOnlineResponse
	"""查询当前网校指定期别下教学计划项教师名称列表（按教师名称去重）
		@return
	"""
	getTeacherListInServicer(issueId:String):[String] @optionalLogin
	"""查询当前网校下的培训地点详情
		@param request
		@return
	"""
	getTrainingPointDetailInServicer(request:GetTrainingPointDetailRequest):TrainingPointResponse @optionalLogin
	"""分页获取当前服务商下的地区学习统计报表列表
		@param request
		@return
	"""
	listRegionLearningReportFormsInServicer(request:LearningReportFormsRequest):[RegionLearningStatisticsResponse] @optionalLogin
	"""分页获取当前服务商下的地区学习统计报表列表   jxjy
		@param request
		@return
	"""
	listRegionLearningReportFormsInServicerJXJY(request:LearningReportFormsRequest):[RegionLearningStatisticsResponse] @optionalLogin
	"""分页获取当前服务商管辖地区下的地区学习统计报表列表
		@param request
		@return
	"""
	listRegionLearningReportFormsInServicerManageRegion(request:LearningReportFormsRequest):[RegionLearningStatisticsResponse]
	"""获取当前服务商下的培训方案学习统计报表列表
		todo 第一期只实现入参：方案ID，返回值：报名数、合格数
		@param request
		@return
	"""
	listSchemeLearningReportFormsInServicer(request:LearningReportFormsRequest):[SchemeLearningStatisticsResponse] @optionalLogin
	"""分页获取当前服务商下的地区学习统计报表列表 申报地区/班级sku地区维度  btxpxpt
		@param request
		@return
	"""
	listSkuRegionLearningReportFormsInServicer(request:LearningReportFormsRequest):[RegionLearningStatisticsResponse] @optionalLogin
	"""分页获取当前服务商管辖地区下的地区学习统计报表列表 申报地区/班级sku地区维度  btxpxpt
		@param request
		@return
	"""
	listSkuRegionLearningReportFormsInServicerManageRegion(request:LearningReportFormsRequest):[RegionLearningStatisticsResponse]
	"""分页查询当前网校下方案指定期别下的学员学习情况列表
		@return
	"""
	pageAppointStudentSchemeIssueRegistrationInServicer(page:Page,issueId:String,request:UserInfoRequest):AppointSchemeIssueRegistrationResponsePage @page(for:"AppointSchemeIssueRegistrationResponse")
	"""分页查询证明模板
		@param page 分页对象
		@return 证书模板分页数据
	"""
	pageCertificateTemplate(page:Page):CertificateTemplateResponsePage @page(for:"CertificateTemplateResponse")
	"""分页查询证明模板 分销商查网校模版
		@param page 分页对象
		@return 证书模板分页数据
	"""
	pageCertificateTemplateInDistributor(page:Page):CertificateTemplateResponsePage @page(for:"CertificateTemplateResponse")
	"""查询当前网校下的期别学习配置信息列表
		todo 报到配置下一期做
	"""
	pageIssueStudyConfigInServicer(page:Page,schemeId:String):IssueStudyConfigResponsePage @page(for:"IssueStudyConfigResponse") @optionalLogin
	"""分页获取方案配置
		@param page
		@param request
		@param needField
		@return
	"""
	pageSchemeConfigByRequestInServicer(page:Page,request:SchemeConfigRequest,needField:[String]):SchemeConfigResponsePage @page(for:"SchemeConfigResponse") @optionalLogin
	"""分页获取方案配置
		@param page
		@param schemeIds
		@param needField
		@return
	"""
	pageSchemeConfigInDistributor(page:Page,schemeIds:[String],needField:[String]):SchemeConfigResponsePage @page(for:"SchemeConfigResponse") @optionalLogin
	"""分页获取方案配置
		@param page
		@param schemeIds
		@param needField
		@return
	"""
	pageSchemeConfigInServicer(page:Page,schemeIds:[String],needField:[String]):SchemeConfigResponsePage @page(for:"SchemeConfigResponse") @optionalLogin
	"""分页查询当前分销商下期别配置列表（无需鉴权）
		@param page
		@param request
		@return
	"""
	pageSchemeIssueConfigListInDistributor(page:Page,request:SchemeIssueConfigRequest):SchemeIssueConfigResponsePage @page(for:"SchemeIssueConfigResponse") @optionalLogin
	"""分页查询当前网校下的期别配置列表
		@param page
		@param request
		@return
	"""
	pageSchemeIssueConfigListInServicer(page:Page,request:SchemeIssueConfigRequest):SchemeIssueConfigResponsePage @page(for:"SchemeIssueConfigResponse") @optionalLogin
	"""分页查询当前网校下期别配置列表（无需鉴权）
		@param page
		@param request
		@return
	"""
	pageSchemeIssueConfigListOptionalLoginInServicer(page:Page,request:SchemeIssueConfigRequest):SchemeIssueConfigResponsePage @page(for:"SchemeIssueConfigResponse") @optionalLogin
	"""分页获取当前网校下期别的教学计划项签到点列表
		@return
	"""
	pageSchemeIssuePlanItemInServicer(page:Page,issueId:String,signType:Int):SchemeIssuePlanItemResponsePage @page(for:"SchemeIssuePlanItemResponse") @optionalLogin
	"""分页查询当前网校下学员班级下可换期的期别配置列表
		第一期暂不实现
		@return
	"""
	pageSchemeIssueReplaceableInServicer(page:Page,request:SchemeIssueReplaceableRequest):SchemeIssueReplaceableResponsePage @page(for:"SchemeIssueReplaceableResponse") @optionalLogin
	"""分页查询当前网校下学员换期记录
		@return
	"""
	pageStudentExchangeIssueRecordInServicer(page:Page,request:StudentSchemeLearningInformationRequest):StudentExchangeIssueRecordResponsePage @page(for:"StudentExchangeIssueRecordResponse")
	"""分页获取我的培训方案配置
		<p>
		在学员中心使用，如果学员还在学习中会始终返回最新的方案配置，
		如果学员已培训通过，该接口会返回培训通过之前最新的培训方案配置‘快照’
		@param page      分页参数
		@param request   方案配置查询条件
		@param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
		返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
		}
		如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
		@return
	"""
	pageStudentSchemeConfigInMySelf(page:Page,request:StudentSchemeConfigRequest,needField:[String]):SchemeConfigResponsePage @page(for:"SchemeConfigResponse")
	"""分页获取当前服务商下的学员培训方案配置
		<p>
		如果学员还在学习中会始终返回最新的方案配置，
		如果学员已培训通过，该接口会返回培训通过之前最新的培训方案配置‘快照’
		@param page
		@param request
		@param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
		返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
		}
		如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
		@return
	"""
	pageStudentSchemeConfigInServicer(page:Page,request:StudentSchemeConfigRequest,needField:[String]):SchemeConfigResponsePage @page(for:"SchemeConfigResponse") @optionalLogin
	"""分页获取当前子项目下的学员培训方案配置
		<p>
		如果学员还在学习中会始终返回最新的方案配置，
		如果学员已培训通过，该接口会返回培训通过之前最新的培训方案配置‘快照’
		@param page
		@param request
		@param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
		返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
		}
		如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
		@return
	"""
	pageStudentSchemeConfigInSubProject(page:Page,request:StudentSchemeConfigRequest,needField:[String]):SchemeConfigResponsePage @page(for:"SchemeConfigResponse") @optionalLogin
	"""分页查询当前网校下方案下期别维度的学员报名情况统计列表
		@return
	"""
	pageStudentSchemeIssueRegistrationInServicer(page:Page,schemeId:String,issueName:String,issueNum:String):SchemeIssueRegistrationResponsePage @page(for:"SchemeIssueRegistrationResponse")
	"""网校分页获取集体缴费管理员下的学员id集合
		@param request
		@return
	"""
	pageStudentSchemeLearningByCollectiveUserIdInServicer(page:Page,request:StudentSchemeLearningRequest,collectiveUserIds:[String],sort:[StudentSchemeLearningSortRequest]):UserResponsePage @page(for:"UserResponse")
	"""分页获取当前服务商管辖地区下的学员培训方案学习列表
		sku地区权限控制
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningBySkuRegionInServicerManageRegion(page:Page,request:StudentSchemeLearningRequest,sort:[StudentSchemeLearningSortRequest]):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse")
	"""分页分销商管理员的学习明细数据（面授/面网授）"""
	pageStudentSchemeLearningDetailInDistributor(page:Page,request:StudentSchemeLearningInOnlineRequest,sort:[StudentSchemeLearningSortRequest]):StudentSchemeLearningInOnlineResponsePage @page(for:"StudentSchemeLearningInOnlineResponse")
	"""分页查询超管当前网校下面网授班学员学习明细(面授和面网授学员明细查询)
		@return
	"""
	pageStudentSchemeLearningDetailInServicer(page:Page,request:StudentSchemeLearningInOnlineRequest,sort:[StudentSchemeLearningSortRequest]):StudentSchemeLearningInOnlineResponsePage @page(for:"StudentSchemeLearningInOnlineResponse")
	"""分页地区管理员的学习明细数据（面授/面网授）"""
	pageStudentSchemeLearningDetailInServicerManageRegion(page:Page,request:StudentSchemeLearningInOnlineRequest,sort:[StudentSchemeLearningSortRequest]):StudentSchemeLearningInOnlineResponsePage @page(for:"StudentSchemeLearningInOnlineResponse")
	"""分页获取专题管理员的学习明细数据（面授/面网授）"""
	pageStudentSchemeLearningDetailInTrainingChannel(page:Page,request:StudentSchemeLearningInOnlineRequest,sort:[StudentSchemeLearningSortRequest]):StudentSchemeLearningInOnlineResponsePage @page(for:"StudentSchemeLearningInOnlineResponse")
	"""分页获取当前分销商下的学员培训方案学习列表
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningInDistributor(page:Page,request:StudentSchemeLearningRequest,sort:[StudentSchemeLearningSortRequest]):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse")
	"""分页获取当前服务商下的学员培训方案学习列表
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningInServicer(page:Page,request:StudentSchemeLearningRequest,sort:[StudentSchemeLearningSortRequest]):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse")
	"""分页获取当前服务商管辖地区下的学员培训方案学习列表
		特别用于服务商的学习明细（新打印证明查询学员明细口）
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningInServicer4PrintProof(page:Page,request:StudentSchemeLearningRequest):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse")
	"""分页获取当前分销商管辖地区下的学员培训方案学习列表
		特别用于服务商的学习明细（新打印证明查询学员明细口）
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningInServicer4PrintProofInDistributor(page:Page,request:StudentSchemeLearningRequest):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse")
	"""分页获取当前专题商管辖地区下的学员培训方案学习列表
		特别用于服务商的学习明细（新打印证明查询学员明细口）
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningInServicer4PrintProofInTrainingChannel(page:Page,request:StudentSchemeLearningRequest):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse")
	"""分页获取当前服务商管辖地区下的学员培训方案学习列表
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningInServicerManageRegion(page:Page,request:StudentSchemeLearningRequest,sort:[StudentSchemeLearningSortRequest]):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse")
	"""分页获取当前服务商管辖地区下的学员培训方案学习列表
		特别用于服务商的学习明细
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningInServicerV2(page:Page,request:StudentSchemeLearningRequest):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse")
	"""分页获取当前子项目下的学员培训方案学习列表
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningInSubProject(page:Page,request:StudentSchemeLearningRequest,sort:[StudentSchemeLearningSortRequest]):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse") @optionalLogin
	"""分页获取当前专题管理员管理专题下的学员学习明细
		特别用于服务商的学习明细
		@param page
		@param request
		@return
	"""
	pageStudentSchemeLearningInTrainingChannelV2(page:Page,request:StudentSchemeLearningRequest):StudentSchemeLearningResponsePage @page(for:"StudentSchemeLearningResponse")
	"""分页查询当前网校下学员方案学习信息列表（含网授+面网授班）
		@return
	"""
	pageStudentSchemeLearningInformationInServicer(page:Page,request:StudentSchemeLearningInformationRequest):StudentSchemeLearningInformationResponsePage @page(for:"StudentSchemeLearningInformationResponse")
	"""分页查询当前网校下方案维度学员学习统计
		@return
	"""
	pageStudentSchemeLearningStatisticsInServicer(page:Page,request:StudentSchemeLearningStatisticsInOnlineRequest,sort:[StudentSchemeLearningSortRequest]):SchemeLearningStatisticsInOnlineResponsePage @page(for:"SchemeLearningStatisticsInOnlineResponse")
	"""分页查询当前分销商下的教学地点列表（无需鉴权）
		沟通后弃用该接口
		@param request
		@return
	"""
	pageTrainingClassroomInDistributor(page:Page,request:TrainingPointRequest):TrainingPointResponsePage @page(for:"TrainingPointResponse") @optionalLogin
	"""分页查询当前网校下的教学地点列表
		沟通后弃用该接口
		@param request
		@return
	"""
	pageTrainingClassroomInMyself(page:Page,request:TrainingPointRequest):TrainingPointResponsePage @page(for:"TrainingPointResponse")
	"""分页查询当前分销商下的培训地点列表（无需鉴权）
		@return
	"""
	pageTrainingPointInDistributor(page:Page,request:TrainingPointRequest):TrainingPointResponsePage @page(for:"TrainingPointResponse") @optionalLogin
	"""分页查询当前网校下的培训地点
		@return
	"""
	pageTrainingPointInServicer(page:Page,request:TrainingPointRequest):TrainingPointResponsePage @page(for:"TrainingPointResponse") @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input GetTrainingPointDetailRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.GetTrainingPointDetailRequest") {
	"""培训地点id"""
	id:String
}
"""学习统计查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 14:22
"""
input LearningPeriodStatisticsRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.LearningPeriodStatisticsRequest") {
	"""培训属性"""
	skuProperty:SchemeSkuPropertyRequest
	"""学员报名时间截止条件(用于指定分页截止时间，防止翻页动作会查询到最新数据，选填)"""
	registerDeadlineTime:DateTime
}
"""地区学习统计查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 14:17
"""
input LearningReportFormsRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.LearningReportFormsRequest") {
	"""是否统计暂无地区的学员班级"""
	isStatisticsNullArea:Boolean
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
	"""非分销门户"""
	notDistributionPortal:Boolean
	"""扩展信息"""
	extendedInfo:ExtendedInfoRequest
}
"""获取我的班级请求参数"""
input MySchemeConfigRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.MySchemeConfigRequest") {
	"""参训资格id"""
	qualificationId:String
	"""是否剔除失效大纲id   用于班级修改大纲时  显示失效的大纲信息"""
	isIncludeUnableOutlineId:Boolean!
}
"""获取我的班级请求参数"""
input SchemeConfigRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.SchemeConfigRequest") {
	"""方案id"""
	schemeId:String
	"""方案id集合"""
	schemeIds:[String]
	"""行业"""
	industry:[String]
	"""是否包含失效大纲id   用于班级修改大纲时  显示失效的大纲信息"""
	isIncludeUnableOutlineId:Boolean
	"""是否包含已配置监管方案"""
	isIncludeHasAntiScheme:Boolean
	"""培训形式"""
	trainType:String
	"""方案名称"""
	schemeName:String
	"""监管配置id"""
	antiConfigId:String
}
input SchemeIssueConfigRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.SchemeIssueConfigRequest") {
	"""方案idList"""
	schemeIdList:[String]
	"""期别idList"""
	issueIdList:[String]
	"""期别编号"""
	issueNum:String
	"""期别名称"""
	issueName:String
}
"""查询当前网校指定期别下的教学计划项请求参数"""
input SchemeIssuePlanItemRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.SchemeIssuePlanItemRequest") {
	"""期别id"""
	issueId:String
	"""教学开始时间"""
	startTime:String
	"""教学结束时间"""
	endTime:String
}
input SchemeIssueReplaceableRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.SchemeIssueReplaceableRequest") {
	"""期别编号"""
	issueNum:String
	"""期别名称"""
	issueName:String
	"""报到开始日期"""
	registerStartDate:DateTime
	"""报道结束日期"""
	registerEndDate:DateTime
	"""换期学员方案参训资格ID"""
	SchemeQualificationId:String
}
"""方案配置查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 10:50
"""
input StudentSchemeConfigRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeConfigRequest") {
	"""学员id列表"""
	qualificationIdList:[String]
}
"""学员培训方案学习查询条件
	<AUTHOR>
	@version 1.0user
	@date 2022/1/17 11:40
"""
input StudentSchemeLearningInOnlineRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningInOnlineRequest") {
	"""学号"""
	studentNoList:[String]
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemRequest
	"""扩展信息"""
	extendedInfo:ExtendedInfoRequest
	"""方案是否提供培训证明"""
	openPrintTemplate:Boolean
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
	"""专题Id  用于不同专题域名 查询对应专题的培训班"""
	trainingChannelId:String
	"""非分销门户"""
	notDistributionPortal:Boolean
	"""培训形式
		trainingWay0001：网授
		trainingWay0002：面网授
		trainingWay0003：面授
		如需使用培训形式条件请使用SKU 此参数无效，即将删除
	"""
	trainingType:String
	"""期别id"""
	issueId:String
}
input StudentSchemeLearningInformationRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningInformationRequest") {
	"""方案idList"""
	schemeIdList:[String]
	"""方案名称"""
	schemeName:String
	"""方案SKU"""
	schemeSku:SchemeSkuPropertyRequest
	"""方案类型
		选课学习-chooseCourseLearning
		自主学习-autonomousCourseLearning
	"""
	schemeType:[String]
	"""期别idList"""
	issueIdList:[String]
	"""期别名称"""
	issueName:String
	"""学号List"""
	studentNo:[String]
	"""用户idList"""
	userIdList:[String]
	"""培训结果
		<p>
		-1:未知，培训尚未完成
		1:培训合格
		0:培训不合格
	"""
	trainingResultList:[Int]
}
"""学员培训方案学习查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 11:40
"""
input StudentSchemeLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningRequest") {
	"""学号"""
	studentNoList:[String]
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemRequest
	"""扩展信息"""
	extendedInfo:ExtendedInfoRequest
	"""方案是否提供培训证明"""
	openPrintTemplate:Boolean
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
	"""专题Id  用于不同专题域名 查询对应专题的培训班"""
	trainingChannelId:String
	"""非分销门户"""
	notDistributionPortal:Boolean
	"""培训形式
		trainingWay0001：网授
		trainingWay0002：面网授
		trainingWay0003：面授
		如需使用培训形式条件请使用SKU 此参数无效，即将删除
	"""
	trainingType:String
	"""期别id"""
	issueId:String
}
"""学员方案学习查询排序条件"""
input StudentSchemeLearningSortRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningSortRequest") {
	"""需要排序的字段"""
	field:StudentSchemeLearningSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""学员培训方案学习查询条件
	<AUTHOR>
	@version 1.0user
	@date 2022/1/17 11:40
"""
input StudentSchemeLearningStatisticsInOnlineRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningStatisticsInOnlineRequest") {
	"""培训类型（全部、网授、面网授）"""
	trainingType:String
	"""是否统计暂无地区的学员班级"""
	isStatisticsNullArea:Boolean
	"""学员信息"""
	student:UserRequest
	"""报名信息"""
	learningRegister:LearningRegisterRequest
	"""培训班信息"""
	scheme:SchemeRequest
	"""学习信息"""
	studentLearning:StudentLearningRequest
	"""数据分析信息"""
	dataAnalysis:DataAnalysisRequest
	"""是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1"""
	saleChannels:[Int]
	"""专题名称"""
	trainingChannelName:String
	"""非分销门户"""
	notDistributionPortal:Boolean
	"""扩展信息"""
	extendedInfo:ExtendedInfoRequest
}
input TrainingPointRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.TrainingPointRequest") {
	"""培训地点id"""
	ids:[String]
	"""地点名称"""
	name:String
	"""是否被期别引用状态"""
	isReferencedByIssue:Boolean
	"""是否启用"""
	enabled:Boolean
	"""地点名称查询类型、不传默认EQUALS
		@see SearchPattern
		LIKE 模糊查询
		EQUALS 精确查询
	"""
	nameSearchPattern:String
}
"""@version: 1.0
	@description: 对接管理系统
	@author: sugs
	@create: 2022-11-15 11:27
"""
input ConnectManageSystemRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ConnectManageSystemRequest") {
	"""同步状态
		@see SyncStatus
		0 未同步
		1 已同步
		2 同步失败
		-1 不同步
	"""
	syncStatus:Int
}
"""数据分析信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/20 15:14
"""
input DataAnalysisRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.DataAnalysisRequest") {
	"""成果配置可获得学时"""
	trainingResultPeriod:DoubleScopeRequest
	"""考核要求学时"""
	requirePeriod:DoubleScopeRequest
	"""已获得总学时"""
	acquiredPeriod:DoubleScopeRequest
}
input ExtendedInfoRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ExtendedInfoRequest") {
	"""是否打印
		true 打印 false 未打印
	"""
	whetherToPrint:Boolean
	"""申报单位统一社会信用代码
		@see com.fjhb.ms.scheme.learning.query.constants.ExtendedInfoType#APPLY_COMPANY_CODE
	"""
	applyCompanyCode:String
	"""政策培训方案id
		@see com.fjhb.ms.scheme.learning.query.constants.ExtendedInfoType#POLICY_TRAINING_SCHEME_ID
	"""
	policyTrainingSchemeId:String
	"""政策培训方案名称
		网关层转id再传递到业务层
	"""
	policyTrainingSchemeName:String
}
"""学员学习报名信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:08
"""
input LearningRegisterRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.LearningRegisterRequest") {
	"""报名方式
		<p>
		1:学员自主报名
		2:集体报名
		3:管理员导入
		@see StudentRegisterTypes
	"""
	registerType:Int
	"""报名来源类型(ORDER：订单 SUB_ORDER：子订单 EXCHANGE_ORDER：换货单)
		@see StudentSourceTypes
	"""
	sourceType:String
	"""报名来源ID"""
	sourceId:String
	"""学员状态(1:正常 2：冻结 3：失效)
		@see StudentStatus
	"""
	status:[Int]
	"""报名时间"""
	registerTime:DateScopeRequest
	"""订单销售渠道"""
	saleChannels:[Int]
	"""来源订单号"""
	orderNoList:[String]
	"""来源子订单号"""
	subOrderNoList:[String]
	"""来源批次单号"""
	batchOrderNoList:[String]
	"""分销商id"""
	distributorId:String
	"""分销门户id"""
	portalId:String
}
"""地区模型
	<AUTHOR>
	@version 1.0
	@date 2022/2/27 20:01
"""
input RegionRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.RegionRequest") {
	"""地区：省"""
	province:String
	"""地区：市"""
	city:String
	"""地区：区"""
	county:String
}
"""学员学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
input StudentLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.learning.StudentLearningRequest") {
	"""培训结果
		<p>
		-1:未知，培训尚未完成
		1:培训合格
		0:培训不合格
		@see StudentTrainingResults
	"""
	trainingResultList:[Int]
	"""培训结果时间"""
	trainingResultTime:DateScopeRequest
	"""无需学习的学习方式类型
		<p>
		1: 选课学习方式
		2: 考试学习方式
		3: 练习学习方式
		4：自主学习课程学习方式
		@see LearningTypes
	"""
	notLearningTypeList:[Int]
	"""课程学习状态（0：未学习 1：学习中 2：学习完成）
		@see StudyStatus
	"""
	courseScheduleStatus:Int
	"""考试结果（-1：未考核 0：不合格 1：合格）
		@see AssessCalculateResults
	"""
	examAssessResultList:[Int]
}
"""地区sku属性查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/2/25 10:55
"""
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertyRequest") {
	"""地区: 省"""
	province:String
	"""地区: 市"""
	city:String
	"""地区: 区县"""
	county:String
}
"""地区匹配查询
	<AUTHOR>
	@version 1.0
	@date 2022/2/25 14:19
"""
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertySearchRequest") {
	"""地区"""
	region:[RegionSkuPropertyRequest]
	"""地区匹配条件
		<p>
		ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回，如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
		PART部分匹配：查询结果返回的地区与查询条件有给值的地区就会返回 如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
		@see RegionSearchType
	"""
	regionSearchType:Int
}
"""培训方案信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
input SchemeRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeRequest") {
	"""培训方案id"""
	schemeId:String
	"""培训方案id"""
	schemeIdList:[String]
	"""培训方案类型
		<p>
		chooseCourseLearning: 选课规则
		autonomousCourseLearning: 自主选课
		@see SchemeType
	"""
	schemeType:String
	"""方案名称"""
	schemeName:String
	"""培训属性"""
	skuProperty:SchemeSkuPropertyRequest
}
"""培训属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 10:22
"""
input SchemeSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeSkuPropertyRequest") {
	"""年度"""
	year:[String]
	"""地区"""
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	"""行业"""
	industry:[String]
	"""科目类型"""
	subjectType:[String]
	"""培训类别"""
	trainingCategory:[String]
	"""培训专业"""
	trainingProfessional:[String]
	"""技术等级"""
	technicalGrade:[String]
	"""岗位类别"""
	positionCategory:[String]
	"""培训对象"""
	trainingObject:[String]
	"""技术等级"""
	jobLevel:[String]
	"""工种"""
	jobCategory:[String]
	"""科目"""
	subject:[String]
	"""年级"""
	grade:[String]
	"""学段"""
	learningPhase:[String]
	"""学科"""
	discipline:[String]
	"""资质类别"""
	qualificationCategory:[String]
	"""培训类型 （网授、面授）"""
	trainingWay:[String]
	"""培训机构"""
	trainingInstitution:[String]
	"""主项/增项"""
	mainAdditionalItem:[String]
}
input UserInfoRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserInfoRequest") {
	"""姓名"""
	name:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phoneNumber:String
}
"""用户属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:01
"""
input UserPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserPropertyRequest") {
	"""所属地区路径"""
	regionList:[RegionRequest]
	"""工作单位名称"""
	companyName:String
	"""下单地区"""
	payOrderRegionList:[RegionRequest]
}
"""用户信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:00
"""
input UserRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserRequest") {
	"""用户id"""
	userIdList:[String]
	"""账户id"""
	accountIdList:[String]
	"""用户属性"""
	userProperty:UserPropertyRequest
}
type AppointSchemeIssueRegistrationResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.AppointSchemeIssueRegistrationResponse") {
	"""用户信息"""
	user:UserInfoResponse
	"""期别参训资格信息"""
	issueTrainingQualification:TrainingQualificationResponse
	"""参训资格下学习方式学习情况"""
	learningCondition:TrainingQualificationLearningConditionResponse
	"""培训成果
		（-1：未考核 0：不合格 1：合格）
	"""
	examAssessResult:Int
}
"""证书模板
	<AUTHOR>
"""
type CertificateTemplateResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.CertificateTemplateResponse") {
	"""模板id"""
	id:String
	"""平台id"""
	platformId:String
	"""平台版本id"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位id"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""模板名称"""
	name:String
	"""模板说明"""
	describe:String
	"""所属行业id"""
	belongsIndustryId:String
	"""使用范围"""
	usableRange:String
	"""适用培训方案形式"""
	suitableSchemeType:String
	"""html模板地址"""
	url:String
	"""预览html模板地址"""
	previewUrl:String
	"""打印快照数据源"""
	printSnapShotDataSource:String
	"""是否应用电子章"""
	provideElectronicSeal:Boolean!
	"""电子章数据源"""
	electronicDataSource:String
	"""0不合并 1合并"""
	isMerge:String
	"""模板尺寸"""
	size:String
	"""是否提供防伪二维码"""
	provideAntiBogusQRCode:Boolean!
	"""防伪二维码id"""
	AntiBogusQRCodeId:String
	"""创建人id"""
	createUserId:String
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""是否可用"""
	available:Boolean!
}
"""<AUTHOR>
	对接管理系统
	@date 2022/11/15 14:40
"""
type ConnectManageSystemResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.ConnectManageSystemResponse") {
	"""同步状态
		@see SyncStatus
		0 未同步
		1 已同步
		2 同步失败
		3 待推送
	"""
	syncStatus:Int
	"""同步信息"""
	syncMessage:String
}
"""电子章
	<AUTHOR>
"""
type ElectronicSealResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.ElectronicSealResponse") {
	"""电子章id"""
	id:String
	"""平台id"""
	platformId:String
	"""平台版本id"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位id"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""生成形式"""
	generateType:Int!
	"""电子章url"""
	url:String
	"""电子章落款"""
	sign:String
	"""创建时间"""
	createdTime:DateTime
	"""创建人id"""
	createUserId:String
	"""更新时间"""
	updateTime:DateTime
}
"""期别配置Response"""
type IssueConfigureInfoResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.IssueConfigureInfoResponse") {
	"""期别id"""
	issueId:String
	"""数据归属平台id"""
	platformId:String
	"""数据归属平台版本id"""
	platformVersionId:String
	"""数据归属项目id"""
	projectId:String
	"""数据归属子项目id"""
	subProjectId:String
	"""数据归属单位id"""
	unitId:String
	"""数据归属服务id"""
	servicerId:String
	"""平台租户id"""
	tenantId:String
	"""期别分类ID"""
	issueCategoryId:String
	"""期别编号"""
	issueNum:String
	"""学习方案id"""
	learningSchemeId:String
	"""期别名称"""
	issueName:String
	"""报到开始时间"""
	startReportTimePeriod:String
	"""报到结束时间"""
	endReportTimePeriod:String
	"""报名开始时间"""
	startSignUpTime:String
	"""报名结束时间"""
	endSignUpTime:String
	"""允许报名人数"""
	alowSignUpNum:Int
	"""已报名人数显示方式 0.实际人数 1.固定人数"""
	signUpNumRevealType:Int
	"""固定显示报名人数"""
	fixedSignUpRevealNum:Int
	"""是否门户展示"""
	portalDisplay:Boolean
	"""是否开放报名"""
	openSignUp:Boolean
	"""培训须知"""
	trainingNotice:String
	"""教学地点id"""
	planAddressId:String
	"""创建用户id"""
	createUserId:String
	"""创建时间"""
	createTime:String
	"""更新时间"""
	updateTime:String
	"""培训开始时间"""
	startTrainingTime:String
	"""培训结束时间"""
	endTrainingTime:String
	"""线下期别课表类型（有课表、无课表）"""
	offlineIssueCourseType:Boolean
	"""课程培训学时（冗余）"""
	trainingPeriod:BigDecimal
	"""已报名人数"""
	signUpNum:Int
}
"""学习成果"""
type IssueLearningResultResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.IssueLearningResultResponse") {
	id:String
	"""分数类型
		<p>说明：查询时可以根据相同的分数类型进行累加分数</p>
		@see LearningResultGradeTypes
	"""
	gradeType:String
	"""分数"""
	grade:Double
}
"""分页查询当前网校指定期别下的教学计划项Response"""
type IssuePlanItemResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.IssuePlanItemResponse") {
	"""教学计划项id"""
	id:String
	"""数据归属平台id"""
	platformId:String
	"""数据归属平台版本id"""
	platformVersionId:String
	"""数据归属项目id"""
	projectId:String
	"""数据归属子项目id"""
	subProjectId:String
	"""数据归属单位id"""
	unitId:String
	"""数据归属服务id"""
	servicerId:String
	"""平台租户id"""
	tenantId:String
	"""教学计划id"""
	planId:String
	"""教学计划组Id"""
	planItemGroupId:String
	"""教学计划项id"""
	name:String
	"""简介"""
	abouts:String
	"""教学模式 1 线下教学接话 2 线上教学计划 3 直播教学计划 10 混合/多样教学计划"""
	planMode:Int
	"""相关教学人员集合"""
	teachers:[PlanTeacherResponse]
	"""时长(秒)"""
	timeLength:Int
	"""学时（课时）"""
	period:Double
	"""教学计划项类型 101 专业基础课程  102 操作技能课程"""
	planItemType:Int
	"""教学开始时间"""
	startTime:String
	"""教学结束时间"""
	endTime:String
	"""教学地点"""
	address:String
	"""状态 1 正常"""
	status:Int
	"""状态备注"""
	statusRemark:String
	"""排序"""
	sort:Int
	"""创建时间"""
	createTime:String
	"""创建人id"""
	createdUserId:String
	"""修改时间"""
	updateTime:String
	"""课程名称"""
	courseName:String
}
"""期别学习配置信息"""
type IssueStudyConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.IssueStudyConfigResponse") {
	"""期别配置"""
	schemeIssueResponse:SchemeIssueResponse
	"""参训配置"""
	trainingConfigResponse:TrainingConfigResponse
	"""报到规则"""
	reportRuleResponse:ReportRuleResponse
	"""考勤配置"""
	attendanceConfigResponse:AttendanceConfigResponse
	"""教学资源配置"""
	teachResourceConfigResponse:TeachResourceConfigResponse
}
"""学员培训方案学习统计信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 14:08
"""
type LearningStatisticsResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.LearningStatisticsResponse") {
	"""净报名人次"""
	netRegisterCount:Int
	"""考核通过人次"""
	qualifiedCount:Int
	"""地区人数"""
	regionPeopleCount:Int
	"""课程学习统计"""
	courseLearningStatistic:CourseLearningStatisticsResponse
	"""考试统计"""
	examLearningStatistic:ExamLearningStatisticsResponse
	"""期别合格数"""
	issueQualifiedCount:Int
	"""期别未合格数"""
	issueUnQualifiedCount:Int
	"""问卷未提交的学院方案参训资格数"""
	questionnaireUnSubmitCount:Int
	"""问卷已提交的学院方案参训资格数"""
	questionnaireSubmitCount:Int
}
"""相关教学人员集合"""
type PlanTeacherResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.PlanTeacherResponse") {
	"""教学人员ID"""
	id:String
	"""教学计划项Id"""
	planItemId:String
	"""教学计划项组Id"""
	planItemGroupId:String
	"""教学计划项组Id"""
	planIdGroup:String
	"""教学计划Id"""
	planId:String
	"""教学人员类型
		@see PlanTeacherTypes
	"""
	teacherType:Int
	"""教学人员性质
		@see PlanTheacherNature
	"""
	nature:Int
	"""教学人员名称"""
	teacherName:String
	"""教学人员关联ID"""
	teacherReferenceId:String
	"""单位名称"""
	unitName:String
}
"""地区学习统计
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 14:07
"""
type RegionLearningStatisticsResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.RegionLearningStatisticsResponse") {
	"""学员所属地区路径"""
	region:RegionResponse
	"""统计信息"""
	learningStatistic:LearningStatisticsResponse
}
"""方案配置主题模型
	<AUTHOR>
	@version 1.0
	@date 2022/1/6 19:22
"""
type SchemeConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.SchemeConfigResponse") {
	"""培训方案id"""
	schemeId:String
	"""数据归属信息"""
	owner:OwnerResponse
	"""json结构方案配置"""
	schemeConfig:String
	"""培训属性"""
	skuProperty:SchemeSkuPropertyResponse
	"""学习方案名称"""
	name:String
	"""方案是否有配置反作弊"""
	hasAntiConfig:Boolean
	"""反作弊id"""
	antiConfigId:String
	"""简介"""
	intro:String
}
type SchemeIssueConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.SchemeIssueConfigResponse") {
	"""方案配置"""
	schemeConfig:SchemeConfigResponse
	"""期别配置"""
	issueConfig:[SchemeIssueResponse]
}
type SchemeIssuePlanItemConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.SchemeIssuePlanItemConfigResponse") {
	"""教学计划配置"""
	planItemConfig:PlanItemConfig
	"""考勤规则"""
	attendanceRule:AttendanceConfigResponse
}
type SchemeIssuePlanItemResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.SchemeIssuePlanItemResponse") {
	"""教学计划项"""
	planItemConfig:PlanItemConfig
	"""教学计划对应的签到点"""
	planItemSignPoint:PlanItemSignPoint
}
type SchemeIssueRegistrationResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.SchemeIssueRegistrationResponse") {
	"""期别配置信息"""
	issueConfig:SchemeIssueResponse
	"""已报名数"""
	signUpNum:Int
	"""期别合格数"""
	qualifiedNum:Int
}
type SchemeIssueReplaceableResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.SchemeIssueReplaceableResponse") {
	"""期别配置"""
	issueConfig:SchemeIssueResponse
	"""报名人数"""
	registerCount:Int
}
"""学员方案学习主题模型
	<AUTHOR>
	@version 1.0
	@date 2021/12/14 11:41
"""
type SchemeLearningStatisticsInOnlineResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.SchemeLearningStatisticsInOnlineResponse") {
	"""期别合格数"""
	issueQualifiedCount:Int
	"""期别未合格数"""
	issueUnQualifiedCount:Int
	"""问卷未提交的学院方案参训资格数"""
	questionnaireUnSubmitCount:Int
	"""问卷已提交的学院方案参训资格数"""
	questionnaireSubmitCount:Int
	"""培训班id"""
	schemeId:String
	"""统计信息"""
	learningStatistic:LearningStatisticsResponse
}
"""培训方案学习统计
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 14:07
"""
type SchemeLearningStatisticsResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.SchemeLearningStatisticsResponse") {
	"""培训班id"""
	schemeId:String
	"""统计信息"""
	learningStatistic:LearningStatisticsResponse
}
"""学员方案学习主题模型
	<AUTHOR>
	@version 1.0
	@date 2021/12/14 11:41
"""
type SchemeLearningTotalStatisticsInOnlineResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.SchemeLearningTotalStatisticsInOnlineResponse") {
	"""净报名人次"""
	netRegisterCount:Int
	"""考核通过人次"""
	qualifiedCount:Int
	"""地区人数"""
	regionPeopleCount:Int
	"""课程学习统计"""
	courseLearningStatistic:CourseLearningStatisticsResponse
	"""考试统计"""
	examLearningStatistic:ExamLearningStatisticsResponse
	"""期别合格数"""
	issueQualifiedCount:Int
	"""期别未合格数"""
	issueUnQualifiedCount:Int
	"""问卷未提交的学院方案参训资格数"""
	questionnaireUnSubmitCount:Int
	"""问卷已提交的学院方案参训资格数"""
	questionnaireSubmitCount:Int
}
type StudentExchangeIssueRecordResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.StudentExchangeIssueRecordResponse") {
	"""期别参训资格ID"""
	issueQualificationId:String
	"""学员学号"""
	studentNo:String
	"""关联的学习方案ID"""
	schemeId:String
	"""新期别ID"""
	issueId:String
	"""原参训资格ID"""
	originIssueQualificationId:String
	"""原期别ID"""
	originIssueId:String
	"""换期时间"""
	exchangeTime:DateTime
	"""换期操作人ID"""
	exchangeUserId:String
	"""培训属性"""
	skuProperty:SchemeSkuPropertyResponse
}
"""学员合格方案配置快照主题模型
	<AUTHOR>
	@version 1.0
	@date 2022/1/6 19:22
"""
type StudentSchemeConfigSnapshotResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.StudentSchemeConfigSnapshotResponse") {
	"""参训资格ID"""
	qualificationId:String
	"""学号id"""
	studentNo:String
	"""数据归属信息"""
	owner:OwnerResponse
	"""培训方案id"""
	schemeId:String
	"""json结构方案配置"""
	schemeConfig:String
}
type StudentSchemeLearningDetailResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.StudentSchemeLearningDetailResponse") {
	"""方案配置"""
	schemeConfig:SchemeConfigResponse
	"""期别配置"""
	issueConfig:[SchemeIssueResponse]
	"""学员方案学习信息"""
	studentSchemeWithIssueLearningResponse:StudentSchemeWithIssueLearningResponse
	"""学员期数学习信息"""
	studentIssueLearning:StudentIssueLearningDetailsResponse
}
"""学员方案学习主题模型
	<AUTHOR>
	@version 1.0
	@date 2021/12/14 11:41
"""
type StudentSchemeLearningInOnlineResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.StudentSchemeLearningInOnlineResponse") {
	"""期别学习方式考核要求"""
	trainingRequirements:[String]
	"""学员考核计算结果"""
	userAssessResult:[String]
	"""学员期别id"""
	issueId:String
	"""学员期别参训资格id"""
	issueQualificationId:String
	"""期别学习成果"""
	issueTrainingResult:[IssueLearningResultResponse]
	"""问卷考核要求提交个数（冗余，用于统计）"""
	questionnaireRequirementCount:Int
	"""问卷考核已提交个数（冗余，用于统计）"""
	questionnaireSubmittedCount:Int
	"""问卷不需要考核已提交个数（冗余，用于统计）"""
	questionnaireNoAssessSubmittedCount:Int
	"""方案问卷考核已提交个数（冗余，用于统计）"""
	schemeQuestionnaireSubmittedCount:Int
	"""方案json"""
	schemeConfig:String
	"""参训资格ID"""
	qualificationId:String
	"""学号id"""
	studentNo:String
	"""数据归属信息"""
	owner:OwnerResponse
	"""学员信息"""
	student:UserResponse
	"""学员学习报名信息"""
	learningRegister:LearningRegisterResponse
	"""培训方案信息"""
	scheme:SchemeResponse
	"""学员学习信息"""
	studentLearning:StudentLearningResponse
	"""数据分析信息"""
	dataAnalysis:DataAnalysisResponse
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemResponse
	"""扩展信息"""
	extendedInfo:ExtendedInfoResponse
	"""问卷要求份数（纳入考核的份数）"""
	schemeQuestionnaireRequirementCount:Int
	"""方案问卷不需要考核已提交个数（冗余，用于统计）"""
	schemeQuestionnaireNoAssessSubmittedCount:Int
	"""方案纳入考核问卷数"""
	schemeQuestionnaireSubmittedCount:Int
	"""期别名字"""
	issueName:String
}
type StudentSchemeLearningInformationResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.StudentSchemeLearningInformationResponse") {
	"""方案配置"""
	schemeConfig:SchemeConfigResponse
	"""期别配置"""
	issueConfig:[SchemeIssueResponse]
	"""学员方案学习信息"""
	studentSchemeWithIssueLearningResponse:StudentSchemeWithIssueLearningResponse
	"""学员期数学习信息"""
	studentIssueLearning:StudentIssueLearningDetailsResponse
}
"""学员方案学习主题模型
	<AUTHOR>
	@version 1.0
	@date 2021/12/14 11:41
"""
type StudentSchemeLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.StudentSchemeLearningResponse") {
	"""参训资格ID"""
	qualificationId:String
	"""学号id"""
	studentNo:String
	"""数据归属信息"""
	owner:OwnerResponse
	"""学员信息"""
	student:UserResponse
	"""学员学习报名信息"""
	learningRegister:LearningRegisterResponse
	"""培训方案信息"""
	scheme:SchemeResponse
	"""学员学习信息"""
	studentLearning:StudentLearningResponse
	"""数据分析信息"""
	dataAnalysis:DataAnalysisResponse
	"""对接管理系统"""
	connectManageSystem:ConnectManageSystemResponse
	"""扩展信息"""
	extendedInfo:ExtendedInfoResponse
	"""问卷要求份数（纳入考核的份数）"""
	schemeQuestionnaireRequirementCount:Int
	"""方案问卷不需要考核已提交个数（冗余，用于统计）"""
	schemeQuestionnaireNoAssessSubmittedCount:Int
	"""方案纳入考核问卷数"""
	schemeQuestionnaireSubmittedCount:Int
	"""期别名字"""
	issueName:String
}
type TrainingPointResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.TrainingPointResponse") {
	"""培训地点ID，主键"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""网校服务商ID"""
	servicerId:String
	"""平台租户id"""
	tenantId:String
	"""地点名称"""
	name:String
	"""地点坐标经度"""
	longitude:Double
	"""地点坐标纬度"""
	latitude:Double
	"""地点数据（冗余，供地图工具使用）"""
	specificAddressData:String
	"""培训点所在地区"""
	areaPath:String
	"""培训点所在地区名称"""
	areaName:RegionResponse
	"""培训教室"""
	classRoom:String
	"""拥有者id"""
	ownerId:String
	"""拥有者类型"""
	ownerType:String
	"""是否启用"""
	enabled:Boolean
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""是否被期别引用状态"""
	isReferencedByIssue:Boolean
}
"""考勤规则配置"""
type AttendanceConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.AttendanceConfigResponse") {
	"""方案ID"""
	schemeId:String
	"""考勤规则配置ID"""
	settingId:String
	"""签到配置"""
	attendancesSignInConfig:AttendancesSignInConfig
	"""签退配置"""
	attendancesSignOutConfig:AttendancesSignOutConfig
	"""拥有者类型
		1-学习方案
		3-期别
		4-学习方式
	"""
	ownerType:Int
	"""拥有者ID"""
	ownerId:String
}
"""所属集体缴费信息"""
type BatchOwnerResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.BatchOwnerResponse") {
	"""所属集体缴费单位id"""
	unitId:String
	"""所属集体缴费用户id"""
	userId:String
}
type ExtendedInfoResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.ExtendedInfoResponse") {
	"""是否打印
		true 打印 false 未打印
	"""
	whetherToPrint:Boolean
	"""打印时间"""
	printTime:DateTime
	"""pdf证书地址"""
	pdfUrl:String
	"""证书id"""
	certificateId:String
	"""证书编号"""
	certificateNo:String
}
"""学员学习报名信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:08
"""
type LearningRegisterResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.LearningRegisterResponse") {
	"""报名方式
		<p>
		1:学员自主报名
		2:集体报名
		3:管理员导入
		@see StudentRegisterTypes
	"""
	registerType:Int
	"""报名来源类型
		Order: 订单
		@see StudentSourceTypes
	"""
	sourceType:String
	"""报名来源ID"""
	sourceId:String
	"""学员方案状态
		<p>
		1:正常
		2:冻结
		3:失效
		@see StudentStatus
	"""
	status:Int
	"""学员状态最后变更时间"""
	statusChangeTime:DateTime
	"""报名时间"""
	registerTime:DateTime
	"""订单销售渠道"""
	saleChannel:Int
	"""来源订单号"""
	orderNo:String
	"""来源子订单号"""
	subOrderNo:String
	"""来源批次单号"""
	batchOrderNo:String
	"""失效来源类型
		@see StudentFrozenAndInvalidTypes
	"""
	frozenAndInvalidSourceType:String
	"""失效来源id"""
	frozenAndInvalidSourceId:String
}
"""数据归属信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/8 17:29
"""
type OwnerResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.OwnerResponse") {
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位id"""
	unitId:String
	"""所属服务商类型
		<p>
		1：培训机构
		2：课件供应商
		3：渠道商
		4：参训单位
		@see ServicerTypes
	"""
	servicerType:Int
	"""所属服务商id"""
	servicerId:String
	"""所属集体缴费信息"""
	batchOwner:BatchOwnerResponse
}
"""报到规则"""
type ReportRuleResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.ReportRuleResponse") {
	"""报到规则ID"""
	reportRuleId:String
	"""报到规则集"""
	ruleCollectId:String
	"""学习方案ID"""
	schemeId:String
	"""期别ID"""
	issueId:String
	"""教学计划ID"""
	planId:String
	"""上午上课时间"""
	amClassTime:DateTime
	"""上午上课时间类型
		0-同课程
		1-指定
	"""
	amClassTimeType:Int
	"""下午上课时间"""
	pmClassTime:DateTime
	"""下午上课时间类型
		0-同课程
		1-指定
	"""
	pmClassTimeType:Int
	"""报到形式
		0-证明
		1-签到
	"""
	checkInType:Int
	"""证明模板ID"""
	certificateTemplateId:String
	"""打卡半径范围（X米）"""
	signRadiusRange:Int
	"""拥有者类型
		1-方案
		3-期别
	"""
	ownerType:Int
	"""拥有者ID"""
	ownerId:String
}
type SchemeIssueResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.SchemeIssueResponse") {
	"""期别ID"""
	issueId:String
	"""期别分类ID"""
	issueCategoryId:String
	"""期别编号"""
	issueNum:String
	"""学习方案ID"""
	schemeId:String
	"""期别名称"""
	issueName:String
	"""期别总学时 (目前走教学计划，没有教学计划则为null）"""
	periods:Double
	"""报到开始时间"""
	startReportTimePeriod:DateTime
	"""报到结束时间"""
	endReportTimePeriod:DateTime
	"""报名开始时间"""
	startSignUpTime:DateTime
	"""报名结束时间"""
	endSignUpTime:DateTime
	"""培训开始时间"""
	startTrainTime:DateTime
	"""培训结束时间"""
	endTrainTime:DateTime
	"""线下期别课表类型（有课表、无课表）"""
	offlineIssueCourseType:Boolean
	"""课程培训学时（冗余）"""
	trainingPeriod:BigDecimal
	"""允许报名人数"""
	allowSignUpNum:Int
	"""已报名人数显示方式"""
	signUpNumRevealType:Int
	"""固定显示报名人数"""
	fixedSignUpRevealNum:Int
	"""是否门户展示"""
	portalDisplay:Boolean
	"""是否开放报名"""
	openSignUp:Boolean
	"""教学地点ID"""
	planAddressId:String
}
"""学员期数学习"""
type StudentIssueLearningDetailsResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.StudentIssueLearningDetailsResponse") {
	"""期数参训资格ID"""
	qualificationId:String
	"""学员学号"""
	studentNo:String
	"""业务学号"""
	businessStudentNo:String
	"""关联的用户ID"""
	userId:String
	"""关联的学习方案ID"""
	schemeId:String
	"""关联的期别ID"""
	issueId:String
	"""学员方案培训结果 | -1 -培训中 0-培训未合格 1-培训合格
		@see  StudentTrainingResults
	"""
	trainingResult:Int!
	"""培训合格时间"""
	trainingResultGainTime:DateTime
	"""住宿信息
		0-无需住宿
		1-单住
		2-合住
	"""
	roomInfo:Int
	"""问卷考核要求提交个数（冗余，用于统计）"""
	questionnaireRequirementCount:Int
	"""问卷考核已提交个数（冗余，用于统计）"""
	questionnaireSubmittedCount:Int
	"""成果配置可获得学时（冗余）"""
	trainingResultPeriod:BigDecimal
	"""成果要求学时（冗余）"""
	requirePeriod:BigDecimal
	"""学员方案已获得学时（冗余）"""
	acquiredPeriod:BigDecimal
	"""同步管理系统状态"""
	syncStatus:Int
	"""同步管理系统信息"""
	syncMessage:String
	"""学习方式学习信息"""
	learning:[LearningResponseV2]
	"""学员期别学习考核指标结果"""
	userAssessResult:[String]
	"""学员培训成果"""
	learningResult:[LearningResultResponseV2]
}
"""(学员参训资格信息包含学习情况）（含网授+面网授班）"""
type StudentSchemeWithIssueLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.StudentSchemeWithIssueLearningResponse") {
	"""参训资格ID"""
	qualificationId:String
	"""学员学号"""
	studentNo:String
	"""关联的用户ID"""
	userId:String
	"""关联的学习方案ID"""
	schemeId:String
	"""学员方案培训结果 | -1 -培训中 0-培训未合格 1-培训合格
		@see  StudentTrainingResults
	"""
	trainingResult:Int!
	"""培训合格时间"""
	trainingResultGainTime:DateTime
	"""同步管理系统状态"""
	syncStatus:Int
	"""同步管理系统信息"""
	syncMessage:String
	"""问卷考核要求提交个数（冗余，用于统计）"""
	questionnaireRequirementCount:Int
	"""问卷考核已提交个数（冗余，用于统计）"""
	questionnaireSubmittedCount:Int
	"""成果配置可获得学时（冗余）"""
	trainingResultPeriod:BigDecimal
	"""成果要求学时（冗余）"""
	requirePeriod:BigDecimal
	"""学员方案已获得学时（冗余）"""
	acquiredPeriod:BigDecimal
	"""学习方式学习信息"""
	learning:[LearningResponseV2]
	"""学员方案学习考核指标结果"""
	userAssessResult:[String]
	"""方案下学员培训成果"""
	learningResult:[LearningResultResponseV2]
}
"""教学资源配置"""
type TeachResourceConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.TeachResourceConfigResponse") {
	"""配置ID"""
	settingId:String
	"""学习方案ID"""
	schemeId:String
	"""教学资源列表"""
	teachResourceList:[TeachResourceResponse]
	"""拥有者类型
		1-学习方案
		3-期别
		4-学习方式
	"""
	ownerType:Int
	"""拥有者ID"""
	ownerId:String
}
"""参训配置"""
type TrainingConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.TrainingConfigResponse") {
	"""配置ID"""
	trainingConfigId:String
	"""学习方案ID"""
	schemeId:String
	"""是否开启考勤"""
	openAttendance:Boolean
	"""考勤要求类型
		0-自定义
		1-同方案要求
	"""
	attendanceRequireType:Int
	"""最低考勤率"""
	minAttendanceRate:Double
	"""是否开启报到"""
	openReport:Boolean
	"""是否开启结业测试"""
	openCompletionTest:Boolean
	"""是否开启住宿信息采集"""
	openStayInfoCollect:Boolean
	"""是否开启问卷考核"""
	openQuestionnaireAssessment:Boolean
	"""拥有者类型
		1-学习方案
		3-期别
		4-学习方式
	"""
	ownerType:Int
	"""拥有者ID"""
	ownerId:String
}
"""参训资格下学习方式学习情况"""
type TrainingQualificationLearningConditionResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.TrainingQualificationLearningConditionResponse") {
	"""期别ID"""
	issueId:String
	"""考勤是否开启"""
	isAttendanceOpen:Boolean!
	"""是否开启签到"""
	isSignInOpen:Boolean!
	"""签到次数"""
	signInTimes:Int
	"""是否开启签退"""
	isSignOutOpen:Boolean!
	"""签退次数"""
	signOutTimes:Int
	"""是否开启报到"""
	openReport:Boolean!
	"""是否报到"""
	isReport:Boolean!
	"""报到时间"""
	reportTime:DateTime
}
type TrainingQualificationResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.TrainingQualificationResponse") {
	"""期别参训资格ID"""
	qualificationId:String
	"""学号"""
	studentNo:String
	"""业务学号"""
	businessStudentNo:String
	"""学习方案ID"""
	schemeId:String
	"""期别ID"""
	issueId:String
	"""用户ID"""
	userId:String
	"""培训结果
		<p>
		-1:未知，培训尚未完成
		1:培训合格
		0:培训不合格
		@see StudentTrainingResults
	"""
	trainingResult:Int
	"""学员培训结果取得时间"""
	trainingResultTime:DateTime
	"""学员报名来源类型"""
	sourceType:String
	"""学员报名来源ID"""
	sourceId:String
	"""是否住宿
		0-无需住宿
		1-单住
		2-合住
	"""
	isAccommodation:Int
}
"""考勤签到配置"""
type AttendancesSignInConfig @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.attendance.AttendancesSignInConfig") {
	"""是否启用"""
	enable:Boolean
	"""签到频率类型
		1:半天，2:每节课
	"""
	signInFrequencyType:Int
	"""签到地点范围(xxx米)"""
	signInAddressRadius:Int
	"""预签到时间（第一节课前X分钟）"""
	preSignTime:Int
	"""后签到时间（开始授课后X分钟）"""
	postSignTime:Int
}
"""考勤签退配置"""
type AttendancesSignOutConfig @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.attendance.AttendancesSignOutConfig") {
	"""是否启用"""
	enable:Boolean
	"""签到频率类型
		1:半天，2:每节课
	"""
	signInFrequencyType:Int
	"""签到地点范围(xxx米)"""
	signInAddressRadius:Int
	"""预签到时间（第一节课前X分钟）"""
	preSignTime:Int
	"""后签到时间（开始授课后X分钟）"""
	postSignTime:Int
}
"""课程学习方式学习信息
	<AUTHOR>
	@version 1.0
	true:@date 2022/1/15 14:08
"""
type CourseLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.CourseLearningResponse") {
	"""整体课程学习状态
		<p>
		0:未学习
		1:学习中
		2:学习完成
		@see StudyStatus
	"""
	courseScheduleStatus:Int
	"""整体课程完成学习时间"""
	courseQualifiedTime:DateTime
	"""已选课程数"""
	selectedCourseCount:Int
	"""已选课总学时"""
	selectedCoursePeriod:Double
	"""学习方式id"""
	learningId:String
	"""学习方式类型
		<p>
		1: 选课学习方式
		2: 考试学习方式
		3: 自主学习课程学习方式
		@see LearningTypes
	"""
	learningType:Int
	"""是否启用"""
	enabled:Boolean
	"""学习资源类型
		<p>
		1: 课程
		2: 考试
		@see LearningResourceTypes
	"""
	learningResourceType:Int
	"""学习资源ID
		<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
	"""
	learningResourceId:String
	"""课程学习方式下只提供课程学习方式考核指标结果json结构
		如果是在考试学习方式则只提供考核指标结果
		扩展方案配置json课程或考试学习方式考核指标部分的json结构
		ownerType枚举值
		1：学习方案
		2：期别
		3：期数
		4：学习方式
		5：课程大纲
		json结构如：
		[{
		assessId: 考核指标id,
		ownerType: 拥有者类型,
		ownerId: 拥有者id如方案id学习方式id等,
		name: 考核要求模板如Scheme_Assess_001,
		必修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		},
		选修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		}
		},{
		...(数组结构，允许有过个考核要求配置)
		}]
	"""
	userAssessResult:[String]
	"""学员培训成果"""
	learningResult:[LearningResultResponse]
}
"""数据分析信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/20 15:14
"""
type DataAnalysisResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.DataAnalysisResponse") {
	"""成果配置可获得学时"""
	trainingResultPeriod:Double
	"""考核要求学时"""
	requirePeriod:Double
	"""已获得总学时"""
	acquiredPeriod:Double
}
"""考试学习方式学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:09
"""
type ExamLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.ExamLearningResponse") {
	"""是否已考试"""
	committedExam:Boolean
	"""考试考核计算结果
		-1：未考核
		1：合格
		0：不合格
		@see AssessCalculateResults
	"""
	examAssessResult:Int
	"""考试合格时间"""
	examQualifiedTime:DateTime
	"""考试次数"""
	examCount:Int
	"""最高成绩"""
	maxExamScore:Double
	"""学习方式id"""
	learningId:String
	"""学习方式类型
		<p>
		1: 选课学习方式
		2: 考试学习方式
		3: 自主学习课程学习方式
		@see LearningTypes
	"""
	learningType:Int
	"""是否启用"""
	enabled:Boolean
	"""学习资源类型
		<p>
		1: 课程
		2: 考试
		@see LearningResourceTypes
	"""
	learningResourceType:Int
	"""学习资源ID
		<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
	"""
	learningResourceId:String
	"""课程学习方式下只提供课程学习方式考核指标结果json结构
		如果是在考试学习方式则只提供考核指标结果
		扩展方案配置json课程或考试学习方式考核指标部分的json结构
		ownerType枚举值
		1：学习方案
		2：期别
		3：期数
		4：学习方式
		5：课程大纲
		json结构如：
		[{
		assessId: 考核指标id,
		ownerType: 拥有者类型,
		ownerId: 拥有者id如方案id学习方式id等,
		name: 考核要求模板如Scheme_Assess_001,
		必修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		},
		选修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		}
		},{
		...(数组结构，允许有过个考核要求配置)
		}]
	"""
	userAssessResult:[String]
	"""学员培训成果"""
	learningResult:[LearningResultResponse]
}
"""考试学习方式学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:09
"""
type LearningExperienceLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.LearningExperienceLearningResponse") {
	"""是否已提交学习心得"""
	committedLearningExperience:Boolean
	"""学习心得考核计算结果
		-1：未考核
		1：合格
		0：不合格
		@see AssessCalculateResults
	"""
	learningExperienceAssessResult:Int
	"""学习心得合格时间"""
	learningExperienceQualifiedTime:DateTime
	"""最高成绩"""
	maxLearningExperienceScore:Double
	"""心得通过数量  没有考核时使用这个字段"""
	learningExperiencePassCount:Long
	"""学习方式id"""
	learningId:String
	"""学习方式类型
		<p>
		1: 选课学习方式
		2: 考试学习方式
		3: 自主学习课程学习方式
		@see LearningTypes
	"""
	learningType:Int
	"""是否启用"""
	enabled:Boolean
	"""学习资源类型
		<p>
		1: 课程
		2: 考试
		@see LearningResourceTypes
	"""
	learningResourceType:Int
	"""学习资源ID
		<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
	"""
	learningResourceId:String
	"""课程学习方式下只提供课程学习方式考核指标结果json结构
		如果是在考试学习方式则只提供考核指标结果
		扩展方案配置json课程或考试学习方式考核指标部分的json结构
		ownerType枚举值
		1：学习方案
		2：期别
		3：期数
		4：学习方式
		5：课程大纲
		json结构如：
		[{
		assessId: 考核指标id,
		ownerType: 拥有者类型,
		ownerId: 拥有者id如方案id学习方式id等,
		name: 考核要求模板如Scheme_Assess_001,
		必修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		},
		选修考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		}
		},{
		...(数组结构，允许有过个考核要求配置)
		}]
	"""
	userAssessResult:[String]
	"""学员培训成果"""
	learningResult:[LearningResultResponse]
}
"""学习信息"""
type LearningResponseV2 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.LearningResponseV2") {
	"""参训资格ID"""
	qualificationId:String
	"""学习方式id"""
	learningId:String
	"""学习方式类型
		@see LearningTypes
	"""
	learningType:Int
	"""是否启用"""
	enabled:Boolean
	"""是否已删除"""
	deleted:Boolean
	"""学习资源类型
		@see LearningResourceTypes
	"""
	learningResourceType:Int
	"""学习资源ID
		<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
	"""
	learningResourceId:String
	"""学习方式参与状态 | -1 未参与 0-已参与 1-参与完成
		@see StudentLearningStatus
	"""
	learningStatus:Int
	"""学习考核结果 | -1 未考核 0-未合格 1-已合格
		@see StudentLearningTrainingResults
	"""
	learningAssessResult:Int
	"""考核指标结果"""
	userAssessResult:[String]
	"""学员培训成果"""
	learningResult:[LearningResultResponseV2]
	"""报到信息
		学习方式为-教学计划才会有
	"""
	reportInfo:ReportInfo
}
"""学习成果"""
type LearningResultResponseV2 @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.LearningResultResponseV2") {
	"""学员学习成果id"""
	studentLearningResultId:String
	"""学习成果id"""
	learningResultId:String
	"""成果拥有者类型"""
	ownerType:String
	"""成果拥有者id"""
	ownerId:String
	"""学习成果获得时间"""
	gainedTime:DateTime
	"""培训成果配置"""
	learningResultConfig:LearningResultConfigResponse
}
"""报到信息"""
type ReportInfo @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.ReportInfo") {
	"""是否报到"""
	isReport:Boolean!
	"""报到时间"""
	reportTime:DateTime
}
"""学员学习信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
type StudentLearningResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.StudentLearningResponse") {
	"""培训结果
		<p>
		-1:未知，培训尚未完成
		1:培训合格
		0:培训不合格
		@see StudentTrainingResults
	"""
	trainingResult:Int
	"""取得培训结果时间"""
	trainingResultTime:DateTime
	"""课程学习方式学习信息"""
	courseLearning:CourseLearningResponse
	"""考试学习方式学习信息"""
	examLearning:ExamLearningResponse
	"""学习心得学习方式学习信息"""
	learningExperienceLearning:LearningExperienceLearningResponse
	"""方案考核指标结果，json结构（只提供方案部分配置的考核要求）
		扩展方案配置json考核指标部分的json结构
		ownerType枚举值
		1：学习方案
		2：期别
		3：期数
		4：学习方式
		5：课程大纲
		json结构如：
		[{
		assessId: 考核指标id,
		ownerType: 拥有者类型,
		ownerId: 拥有者id如方案id学习方式id等,
		name: 考核要求模板如Scheme_Assess_001,
		方案考核要求字段名: {
		config: 10,//配置值
		current: 5//学员当前已获得值
		}
		},{
		...(数组结构，允许有过个考核要求配置)
		}]
	"""
	userAssessResult:[String]
	"""学员培训成果"""
	learningResult:[LearningResultResponse]
}
"""证书型型培训成果配置
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:30
"""
type CertificateLearningConfigResultResponse implements LearningResultConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.CertificateLearningConfigResultResponse") {
	"""证书模板ID"""
	certificateTemplateId:String
	"""是否开放打印"""
	openPrintTemplate:Boolean!
	"""成果类型
		<p>
		1：分数型学习成果
		2：证书型学习成果
		@see LearningResultTypes
	"""
	resultType:Int
}
"""分数型培训成果配置
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:30
"""
type GradeLearningConfigResultResponse implements LearningResultConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.GradeLearningConfigResultResponse") {
	"""分数类型
		<p>说明：查询时可以根据相同的分数类型进行累加分数</p>
		<p>
		CREDIT: 学分
		结业测试：IMPORT_GRADE_RESULT
		@see LearningResultGradeTypes
	"""
	gradeType:String
	"""分数
		当type为结业测试时，1-合格，0-不合格
	"""
	grade:Double
	"""成果类型
		<p>
		1：分数型学习成果
		2：证书型学习成果
		@see LearningResultTypes
	"""
	resultType:Int
}
"""培训成果配置
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:30
"""
interface LearningResultConfigResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.LearningResultConfigResponse") {
	"""成果类型
		<p>
		1：分数型学习成果
		2：证书型学习成果
		@see LearningResultTypes
	"""
	resultType:Int
}
"""学员培训成果
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 14:28
"""
type LearningResultResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.result.LearningResultResponse") {
	"""用户成果id"""
	learningResultId:String
	"""取得时间"""
	gainedTime:DateTime
	"""培训成果配置"""
	learningResultConfig:LearningResultConfigResponse
}
"""课程学习统计
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 14:09
"""
type CourseLearningStatisticsResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.statistic.CourseLearningStatisticsResponse") {
	"""未学习人次"""
	waitStudyCount:Int
	"""学习中人次"""
	studyingCount:Int
	"""已学完人次"""
	studyFinishCount:Int
}
"""考试学习统计
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 14:10
"""
type ExamLearningStatisticsResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.learning.statistic.ExamLearningStatisticsResponse") {
	"""未参与考试人次"""
	unCommittedExamCount:Int
	"""已参与考试人次"""
	committedExamCount:Int
	"""考试通过人次"""
	passedExamCount:Int
}
"""教学计划项配置"""
type PlanItemConfig @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.plan.PlanItemConfig") {
	"""教学计划项ID"""
	planItemId:String
	"""教学计划ID"""
	planId:String
	"""教学计划项名称"""
	planItemName:String
	"""教学开始时间"""
	startTime:DateTime
	"""教学结束时间"""
	endTime:DateTime
	"""教学计划组ID"""
	planItemGroupId:String
}
"""计划项对应签到点"""
type PlanItemSignPoint @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.plan.PlanItemSignPoint") {
	"""教学计划项ID"""
	planItemId:String
	"""签到点ID"""
	signPointId:String
	"""签到开始时间"""
	startTime:DateTime
	"""签到结束时间"""
	endTime:DateTime
	"""签到类型
		1-签到
		2-签退
	"""
	signType:Int
}
"""培训方案信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:25
"""
type SchemeResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeResponse") {
	"""培训方案id"""
	schemeId:String
	"""培训方案类型
		<p>
		chooseCourseLearning: 选课规则
		autonomousCourseLearning: 自主选课
		@see SchemeType
	"""
	schemeType:String
	"""培训属性"""
	skuProperty:SchemeSkuPropertyResponse
	"""方案名称"""
	schemeName:String
	"""学习成果"""
	learningResult:[LearningResultConfigResponse]
}
"""培训属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 10:22
"""
type SchemeSkuPropertyResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeSkuPropertyResponse") {
	"""年度"""
	year:SchemeSkuPropertyValueResponse
	"""地区: 省"""
	province:SchemeSkuPropertyValueResponse
	"""地区: 市"""
	city:SchemeSkuPropertyValueResponse
	"""地区: 区县"""
	county:SchemeSkuPropertyValueResponse
	"""行业"""
	industry:SchemeSkuPropertyValueResponse
	"""科目类型"""
	subjectType:SchemeSkuPropertyValueResponse
	"""培训类别"""
	trainingCategory:SchemeSkuPropertyValueResponse
	"""培训专业"""
	trainingProfessional:SchemeSkuPropertyValueResponse
	"""技术等级"""
	technicalGrade:SchemeSkuPropertyValueResponse
	"""岗位类别"""
	positionCategory:SchemeSkuPropertyValueResponse
	"""培训对象"""
	trainingObject:SchemeSkuPropertyValueResponse
	"""技术等级"""
	jobLevel:SchemeSkuPropertyValueResponse
	"""工种"""
	jobCategory:SchemeSkuPropertyValueResponse
	"""科目"""
	subject:SchemeSkuPropertyValueResponse
	"""年级"""
	grade:SchemeSkuPropertyValueResponse
	"""学段"""
	learningPhase:SchemeSkuPropertyValueResponse
	"""学科"""
	discipline:SchemeSkuPropertyValueResponse
	"""证书类型"""
	certificatesType:SchemeSkuPropertyValueResponse
	"""执业类别"""
	practitionerCategory:SchemeSkuPropertyValueResponse
	"""培训机构"""
	trainingInstitution:SchemeSkuPropertyValueResponse
	"""主增项"""
	mainAdditionalItem:SchemeSkuPropertyValueResponse
	"""培训类型 （网授、面授）"""
	trainingWay:SchemeSkuPropertyValueResponse
}
"""培训属性值
	<AUTHOR>
	@version 1.0
	@date 2022/1/17 10:22
"""
type SchemeSkuPropertyValueResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.scheme.SchemeSkuPropertyValueResponse") {
	"""sku属性值id"""
	skuPropertyValueId:String
}
"""教学资源"""
type TeachResourceResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.teachresource.TeachResourceResponse") {
	"""资源名称"""
	resourceName:String
	"""资源类型
		0-附件
	"""
	resourceType:Int
	"""资源内容"""
	resourceContent:String
}
type RegionResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.user.RegionResponse") {
	"""地区：省"""
	province:String
	"""地区：市"""
	city:String
	"""地区：区"""
	county:String
}
"""用户信息"""
type UserInfoResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.user.UserInfoResponse") {
	"""用户ID"""
	userId:String
	"""姓名"""
	name:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phoneNumber:String
	"""工作单位"""
	workUnit:String
}
"""用户属性
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:01
"""
type UserPropertyResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.user.UserPropertyResponse") {
	"""所属地区"""
	region:RegionResponse
	"""下单地区"""
	payOrderRegion:RegionResponse
}
"""用户信息
	<AUTHOR>
	@version 1.0
	@date 2022/1/15 11:00
"""
type UserResponse @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.response.nested.user.UserResponse") {
	"""用户id"""
	userId:String
	"""账户id"""
	accountId:String
	"""用户属性"""
	userProperty:UserPropertyResponse
}
"""排序参数
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum SortPolicy @type(value:"com.fjhb.ms.scheme.learning.query.kernel.constants.SortPolicy") {
	"""正序"""
	ASC
	"""倒序"""
	DESC
}
"""学员方案学习可用于排序的字段
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum StudentSchemeLearningSortField @type(value:"com.fjhb.ms.scheme.learning.query.kernel.constants.StudentSchemeLearningSortField") {
	"""报名时间"""
	REGISTER_TIME
	"""方案年度"""
	SCHEME_YEAR
}

scalar List
type IssuePlanItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [IssuePlanItemResponse]}
type AppointSchemeIssueRegistrationResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [AppointSchemeIssueRegistrationResponse]}
type CertificateTemplateResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CertificateTemplateResponse]}
type IssueStudyConfigResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [IssueStudyConfigResponse]}
type SchemeConfigResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SchemeConfigResponse]}
type SchemeIssueConfigResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SchemeIssueConfigResponse]}
type SchemeIssuePlanItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SchemeIssuePlanItemResponse]}
type SchemeIssueReplaceableResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SchemeIssueReplaceableResponse]}
type StudentExchangeIssueRecordResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentExchangeIssueRecordResponse]}
type SchemeIssueRegistrationResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SchemeIssueRegistrationResponse]}
type UserResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserResponse]}
type StudentSchemeLearningResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentSchemeLearningResponse]}
type StudentSchemeLearningInOnlineResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentSchemeLearningInOnlineResponse]}
type StudentSchemeLearningInformationResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentSchemeLearningInformationResponse]}
type SchemeLearningStatisticsInOnlineResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SchemeLearningStatisticsInOnlineResponse]}
type TrainingPointResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingPointResponse]}
