<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--学习数据异常管理-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">学习数据异常管理</el-button>
        <el-drawer
          title="学习数据异常管理"
          :visible.sync="dialog1"
          :direction="direction"
          size="85%"
          :append-to-body="true"
          custom-class="m-drawer m-table-auto"
        >
          <div class="drawer-bd">
            <el-tabs v-model="activeName1" type="card" class="m-tab-card">
              <el-tab-pane label="学习规则（10）" name="first">
                <el-row :gutter="16" class="m-query f-mt20">
                  <el-form :inline="true" label-width="auto">
                    <el-col :span="5">
                      <el-form-item label="姓名">
                        <el-input v-model="input" clearable placeholder="请输入学员姓名" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="证件号">
                        <el-input v-model="input" clearable placeholder="请输入证件号" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="手机号">
                        <el-input v-model="input" clearable placeholder="请输入手机号" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="培训方案">
                        <el-input v-model="input" clearable placeholder="请输入培训方案" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="4" class="f-fr">
                      <el-form-item class="f-tr">
                        <el-button type="primary">查询</el-button>
                        <el-button>重置</el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <el-table stripe :data="tableData6" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="学员信息" min-width="250" fixed="left">
                    <template>
                      <p>姓名：张依依</p>
                      <p>手机号：15659135119</p>
                      <p>证件号：350103199001011234</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="培训方案名称" min-width="240">
                    <template>培训方案名称培训方案名称</template>
                  </el-table-column>
                  <el-table-column label="属性" min-width="240">
                    <template>
                      <p>行业：行业行业</p>
                      <p>地区：为空，不展示</p>
                      <p>科目类型：科目类型</p>
                      <p>培训专业：培训专业培训专业</p>
                      <p>培训年度：2019</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作时间" min-width="170">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="失败原因" min-width="180">
                    <template>重新计算学习时间无法在指定的培训年度学习区间内</template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">编辑学习规则</el-button>
                      <el-button type="text" size="mini">重新计算</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane label="智能学习（20）" name="second">
                <el-row :gutter="16" class="m-query f-mt20">
                  <el-form :inline="true" label-width="auto">
                    <el-col :span="5">
                      <el-form-item label="姓名">
                        <el-input v-model="input" clearable placeholder="请输入学员姓名" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="证件号">
                        <el-input v-model="input" clearable placeholder="请输入证件号" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="手机号">
                        <el-input v-model="input" clearable placeholder="请输入手机号" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="5">
                      <el-form-item label="培训方案">
                        <el-input v-model="input" clearable placeholder="请输入培训方案" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="4" class="f-fr">
                      <el-form-item class="f-tr">
                        <el-button type="primary">查询</el-button>
                        <el-button>重置</el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <el-table stripe :data="tableData" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="学员信息" min-width="250" fixed="left">
                    <template>
                      <p>姓名：张依依</p>
                      <p>手机号：15659135119</p>
                      <p>证件号：350103199001011234</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="培训方案名称" min-width="240">
                    <template>培训方案名称培训方案名称</template>
                  </el-table-column>
                  <el-table-column label="属性" min-width="240">
                    <template>
                      <p>行业：行业行业</p>
                      <p>地区：为空，不展示</p>
                      <p>科目类型：科目类型</p>
                      <p>培训专业：培训专业培训专业</p>
                      <p>培训年度：2019</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作时间" min-width="170">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="失败原因" min-width="180">
                    <template>重新计算学习时间无法在指定的培训年度学习区间内</template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">编辑智能学习规则</el-button>
                      <el-button type="text" size="mini">重新计算</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }],
        tableData4: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }],
        tableData5: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '4' }],
        tableData6: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' }
        ],
        tableData8: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '4' },
          { field101: '5' }
        ],
        tableData9: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' }
        ],
        tableData12: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' }
        ],
        tableData14: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' }
        ],
        tableData15: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: true,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
