import { ImportStudentBatchPrintingRequest } from '@api/diff-gateway/platform-jxjypxtypt-jxgx-certificate'
import { BatchPrintingRequest, ImportListDataRequest } from '@api/platform-gateway/platform-certificate-v1'
import { ImportStatusEnum } from '@api/service/management/personal-leaning/import-print/enums/ImportStatus'
import { ServiceTypeEnum } from '@api/service/management/personal-leaning/mutation/enums/ServiceType'

export default class QueryStudentParams {
  /**
   * 姓名
   */
  name = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 学习方案名称
   */
  schemeName = ''
  /**
   * 类型
   */
  importStatus: ImportStatusEnum = ImportStatusEnum.success
  /**
   * 打印类型 1-导入 2-正常
   */
  //   importStudentPrint: number
  /**
   * 打印类型
   */
  serviceType: ServiceTypeEnum = ServiceTypeEnum.school

  static to(vo: QueryStudentParams) {
    const dto = new ImportListDataRequest()
    dto.name = vo.name
    dto.idCard = vo.idCard
    dto.schemeName = vo.schemeName
    dto.importStatus = vo.importStatus
    // dto.importStudentPrint = vo.importStudentPrint
    return dto
  }
  static toBatchPrinting(vo: QueryStudentParams) {
    const dto = new BatchPrintingRequest()
    dto.name = vo.name
    dto.idCard = vo.idCard
    dto.schemeName = vo.schemeName
    dto.importStatus = vo.importStatus
    dto.servicerType = vo.serviceType
    // dto.importStudentPrint = vo.importStudentPrint
    return dto
  }
}
