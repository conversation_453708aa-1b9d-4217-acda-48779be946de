import QueryStudentDetail from '@api/service/management/user/query/student/QueryStudentDetail'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
import QueryManager from '@api/service/management/user/query/manager/QueryManager'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import QueryLoginStatus from './query/manager/QueryLoginStatus'
import QueryCollectiveManagerList from '@api/service/management/user/query/manager/QueryCollectiveManagerList'
/**
 * 查询工厂类
 */
class QueryUserFactory {
  /**
   * @description: 查询学员详情
   * @param {String} id 学员id
   */
  get queryStudentDetail() {
    return (id: string) => {
      return new QueryStudentDetail(id)
    }
  }
  /**
   * 获取查询学员列表实例
   */
  get queryStudentList() {
    return new QueryStudentList()
  }

  /**
   * 获取查询集体缴费管理员列表实例
   */
  get queryCollectiveManagerList() {
    return new QueryCollectiveManagerList()
  }

  /**
   * 获取查询管理员实例
   */
  get queryManager() {
    return new QueryManager()
  }
  /**
   * 获取询管理员详情实例
   */
  get queryManagerDetail() {
    return QueryManagerDetail
  }
  /**
   * 获取登录状态
   * @returns
   */
  getLoginStatus() {
    return QueryLoginStatus
  }
}
export default new QueryUserFactory()
