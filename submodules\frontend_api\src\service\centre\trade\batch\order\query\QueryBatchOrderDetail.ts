import { Page } from '@hbfe/common'
import Mockjs from 'mockjs'
import QueryStudentUploadInfoListVo from '@api/service/centre/trade/batch/order/query/vo/QueryStudentUploadInfoListVo'
import StudentInfoListDetailVo from '@api/service/centre/trade/batch/order/query/vo/StudentInfoListDetailVo'
import StudentInfoListStatisticVo from '@api/service/centre/trade/batch/order/query/vo/StudentInfoListStatisticVo'
import BatchOrderDetailVo from '@api/service/centre/trade/batch/order/query/vo/BatchOrderDetailVo'
import SignUpStudentInfoListDetailVo from '@api/service/centre/trade/batch/order/query/vo/SignUpStudentInfoListDetailVo'
import SignUpStudentInfoListStatisticVo from '@api/service/centre/trade/batch/order/query/vo/SignUpStudentInfoListStatisticVo'
import ExchangeTrainClassInfoVo from '@api/service/centre/trade/batch/order/query/vo/ExchangeTrainClassInfoVo'
import msTradeQuery, {
  ExchangeOrderRequest,
  ExchangeOrderSortField,
  ExchangeOrderSortRequest,
  OrderBasicDataRequest,
  OrderRequest,
  OrderSortField,
  OrderSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import DataResolve from '@api/service/common/utils/DataResolve'
import UserModule from '@api/service/centre/user/UserModule'
import BatchOrderUtils from '@api/service/centre/trade/batch/order/query/util/BatchOrderUtils'
import ExchangeTrainClassListDetailVo from '@api/service/centre/trade/batch/order/query/vo/ExchangeTrainClassListDetailVo'
/**
 * @description 【集体缴费管理员】查询批次单详情
 */
class QueryBatchOrderDetail {
  /**
   * 【查看详情】查询批次单详情基本信息
   * @param {string} batchOrderNo - 批次单id
   * @return {Promise<BatchOrderDetailVo>}
   */
  async queryBatchOrderDetail(batchOrderNo: string): Promise<BatchOrderDetailVo> {
    const response = await msTradeQuery.getBatchOrderInMySelf(batchOrderNo)
    const result = response.status.isSuccess() ? await BatchOrderDetailVo.from(response.data) : new BatchOrderDetailVo()
    return result
  }

  /**
   * 【查看详情】查询集体报名订单内报名成功列表
   * @param {Page} page - 分页参数
   * @param {string} batchOrderNo - 查询参数
   * @return {Promise<SignUpStudentInfoListDetailVo[]>}
   */
  async queryBatchOrderDetailSignUpSuccessStudentInfoList(
    batchOrderNo: string,
    page: Page
  ): Promise<SignUpStudentInfoListDetailVo[]> {
    let result = [] as SignUpStudentInfoListDetailVo[]
    if (!batchOrderNo) return result
    // 填充查询条件
    const queryParams = new OrderRequest()
    queryParams.orderBasicData = new OrderBasicDataRequest()
    queryParams.orderBasicData.batchOrderNoList = [batchOrderNo]
    // 查询等待付款（待付款）、支付中、开通中、交易成功、交易关闭的主单
    queryParams.orderBasicData.orderStatusList = [1, 2, 3]
    queryParams.orderBasicData.orderPaymentStatusList = [0, 1, 2]
    // 填充排序条件
    const option = new OrderSortRequest()
    option.field = OrderSortField.ORDER_NORMAL_TIME
    option.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(option) as OrderSortRequest[]
    const response = await msTradeQuery.pageOrderInMyself({
      page,
      request: queryParams,
      sortRequest
    })
    page.totalSize = response.data?.totalSize ?? 0
    page.totalPageSize = response.data?.totalPageSize ?? 0
    result =
      response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)
        ? await Promise.all(
            response.data.currentPageData.map(async (item) => {
              return SignUpStudentInfoListDetailVo.from(item, batchOrderNo)
            })
          )
        : ([] as SignUpStudentInfoListDetailVo[])
    return await this.fillStudentInfo(result)
  }

  /**
   * 【查看详情】查询集体报名订单内报名列表统计信息
   * @param {string} batchOrderNo - 批次单id
   * @return {Promise<SignUpStudentInfoListStatisticVo>}
   */
  async queryBatchOrderDetailSignUpStudentInfoListStatistic(
    batchOrderNo: string
  ): Promise<SignUpStudentInfoListStatisticVo> {
    const result = new SignUpStudentInfoListStatisticVo()
    const orderStatistic = await BatchOrderUtils.getMainOrderStatistic(batchOrderNo)
    const refundedStatistic = await BatchOrderUtils.getBatchOrderRefundedStatistic(batchOrderNo)
    // 报名人次
    result.signUpPersonTime = orderStatistic.totalOrderCount
    // 总金额
    result.totalAmount = orderStatistic.totalOrderAmount
    // 已退款人次
    result.refundPersonTime = refundedStatistic.totalReturnOrderCount
    // 已退款金额
    result.refundAmount = refundedStatistic.totalRefundAmount
    return result
  }

  /**
   * 【查看详情】查看子单换班记录
   * @param {string} subOrderNo - 子单id
   * @param {string} batchOrderNo - 批次单id
   */
  async queryExchangeTrainClassInfo(
    subOrderNo: string,
    batchOrderNo: string,
    page: Page
  ): Promise<ExchangeTrainClassListDetailVo[]> {
    let result = [] as ExchangeTrainClassListDetailVo[]
    const request = new ExchangeOrderRequest()
    request.subOrderNoList = [subOrderNo]
    request.batchOrderNoList = [batchOrderNo]
    const sortOption = new ExchangeOrderSortRequest()
    sortOption.field = ExchangeOrderSortField.APPLIED_TIME
    sortOption.policy = SortPolicy.DESC
    const sort = Array(1).fill(sortOption)
    const response = await msTradeQuery.pageExchangeOrderInMySelf({
      page,
      request,
      sort
    })
    page.totalSize = response.data?.totalSize ?? 0
    page.totalPageSize = response.data?.totalPageSize ?? 0
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = await Promise.all(
        response.data.currentPageData.map(async (item) => {
          return await ExchangeTrainClassListDetailVo.from(item)
        })
      )
    }
    return result
  }

  /**
   * 填充学员信息
   */
  private async fillStudentInfo(list: SignUpStudentInfoListDetailVo[]): Promise<SignUpStudentInfoListDetailVo[]> {
    const userIds = [...new Set(list.map((item) => item.studentId ?? ''))]
    if (DataResolve.isWeightyArr(userIds)) {
      const queryRemote = UserModule.queryUserFactory.queryStudentList
      const response = await queryRemote.queryStudentInfoListById(userIds)
      if (DataResolve.isWeightyArr(response)) {
        list.forEach((item) => {
          const userId = item.studentId
          const target = response.find((item) => item.userId === userId)
          if (target) {
            item.studentName = target.userName ?? ''
            item.studentAccount = target.idCard ?? ''
            item.studentPhone = target.phone ?? ''
            item.idCardType = target.idCardType ?? ''
          }
        })
      }
    }
    return list
  }

  /**
   * 校验汇款凭证是否可以重新上传
   * 此函数用于确定当前用户是否可以重新上传汇款凭证
   * 它通过检查与订单号关联的订单状态来决定
   *
   * @param batchOrderNo 订单批次号，用于标识特定的订单
   * @returns 返回一个布尔值，如果汇款凭证可以重新上传，则为true；否则为false
   */
  async checkRemittanceVoucherUpload(batchOrderNo: string): Promise<boolean> {
    // 获取与给定批次订单号相关的订单信息
    const res = await msTradeQuery.getBatchOrderInMySelf(batchOrderNo)

    // 检查响应是否成功且包含数据
    if (res.status?.isSuccess() && res.data && res.data.basicData) {
      // 如果支付状态为2或订单状态为3，则不允许重新上传汇款凭证
      return !(res.data.basicData.paymentStatus == 2 || res.data.basicData.batchOrderStatus == 3)
    }

    // 默认情况下，不允许重新上传汇款凭证
    return false
  }
}

export default QueryBatchOrderDetail
