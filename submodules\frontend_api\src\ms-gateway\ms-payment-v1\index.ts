import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-payment-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-payment-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 订单线下付款更新凭证命令
<AUTHOR>
@since 2021/1/29
 */
export class OfflinePaymentOrderUpdateVoucherRequest {
  /**
   * 付款单号
   */
  paymentOrderNo: string
  /**
   * 付款凭证列表
   */
  paymentVouchers: Array<PaymentVoucherRequest>
}

/**
 * <AUTHOR>
@since 2021/2/1
 */
export class PaymentVoucherRequest {
  /**
   * 付款凭证文件路径，通常是图片的路径
   */
  path?: string
  /**
   * 凭证上传时间
   */
  uploadTime?: string
}

export class BatchOrderPrepareRepayResponse {
  /**
   * 批次单号
   */
  batchOrderNo: string
  /**
   * 支付状态
0-未支付
1-支付中
2-已支付
   */
  paymentStatus: number
}

/**
 * 准备支付结果
 */
export class BatchPreparePayInfoResponse {
  /**
   * 批次单号
   */
  batchNo: string
  /**
   * 支付状态
0-未支付
1-支付中
2-已支付
   */
  paymentStatus: number
  /**
   * 是否超时
   */
  timeOut: boolean
  /**
   * 支付流水号
   */
  flowNo: string
  /**
   * 支付回调地址
   */
  pageUrl: string
  /**
   * 支付金额
   */
  payMoney: string
  /**
   * 商户名称
   */
  merchantName: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 平台版本id
   */
  platformVersionId: string
  /**
   * 超时时间
   */
  timeOutTime: number
  /**
   * 第三方支付返回参数
用于生成二维码。
   */
  thirdReturnData: string
}

/**
 * 准备支付结果
 */
export class PreparePayInfoResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 支付状态
0-未支付
1-支付中
2-已支付
   */
  paymentStatus: number
  /**
   * 是否超时
   */
  timeOut: boolean
  /**
   * 支付流水号
   */
  flowNo: string
  /**
   * 支付回调地址
   */
  pageUrl: string
  /**
   * 支付金额
   */
  payMoney: string
  /**
   * 商户名称
   */
  merchantName: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 平台版本id
   */
  platformVersionId: string
  /**
   * 超时时间
   */
  timeOutTime: number
  /**
   * 第三方支付返回参数
用于生成二维码。
   */
  thirdReturnData: string
}

export class PrepareRepayResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 支付状态
0-未支付
1-支付中
2-已支付
   */
  paymentStatus: number
}

/**
 * 支付流水超时结果
 */
export class TimeoutPayFlowResponse {
  /**
   * 流水号
   */
  flowNo: string
  /**
   * 是否超时
   */
  timeOut: boolean
  /**
   * 流水支付结果
1-支付处理中 2-支付成功 3-支付失败 4-支付超时 5-支付关闭
   */
  payStatus: number
  /**
   * 超时时间
   */
  timeOutTime: number
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询批次订单的付款单状态
   * @param batchOrderNo 批次单号
   * @return 付款单状态信息
   * @param query 查询 graphql 语法文档
   * @param batchOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async prepareBatchOrderRepay(
    batchOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.prepareBatchOrderRepay,
    operation?: string
  ): Promise<Response<BatchOrderPrepareRepayResponse>> {
    return commonRequestApi<BatchOrderPrepareRepayResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { batchOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询订单的付款单状态
   * @param orderNo 订单号
   * @return 付款单状态信息
   * @param query 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async prepareRepay(
    orderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.prepareRepay,
    operation?: string
  ): Promise<Response<PrepareRepayResponse>> {
    return commonRequestApi<PrepareRepayResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { orderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询流水状态
   * @param flowNo 流水号
   * @return 支付流水结果
   * @param query 查询 graphql 语法文档
   * @param flowNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryPayFlowStatus(
    flowNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryPayFlowStatus,
    operation?: string
  ): Promise<Response<TimeoutPayFlowResponse>> {
    return commonRequestApi<TimeoutPayFlowResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { flowNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据流水号查询对应收款账户扫码引导信息，当返回的信息为空时，则表示没有配置引导信息
   * @param flowNo 流水号
   * @return 对应收款账户扫码引导信息
   * @param query 查询 graphql 语法文档
   * @param flowNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryQrScanPromptByPayFlowNo(
    flowNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryQrScanPromptByPayFlowNo,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { flowNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 管理员确认付款单已线下付款
   * @param paymentOrderNo 付款单号
   * @param mutate 查询 graphql 语法文档
   * @param paymentOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async confirmPaymentOrderOfflinePaid(
    paymentOrderNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.confirmPaymentOrderOfflinePaid,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { paymentOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取批次准备支付信息
   * @param batchNo 批次订单号
   * @return 准备支付信息结果
   * @param mutate 查询 graphql 语法文档
   * @param batchNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBatchPreParePayResult(
    batchNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.getBatchPreParePayResult,
    operation?: string
  ): Promise<Response<BatchPreparePayInfoResponse>> {
    return commonRequestApi<BatchPreparePayInfoResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { batchNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取准备支付信息
   * @param orderNo 订单号
   * @return 准备支付信息结果
   * @param mutate 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getPreParePayResult(
    orderNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.getPreParePayResult,
    operation?: string
  ): Promise<Response<PreparePayInfoResponse>> {
    return commonRequestApi<PreparePayInfoResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { orderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 超时支付流水
   * @param flowNo 流水号
   * @return 超时结果
   * @param mutate 查询 graphql 语法文档
   * @param flowNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async timeoutPayFlow(
    flowNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.timeoutPayFlow,
    operation?: string
  ): Promise<Response<TimeoutPayFlowResponse>> {
    return commonRequestApi<TimeoutPayFlowResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { flowNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 更新线下支付凭证
   * @param request 凭证信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateOfflinePaymentVoucher(
    request: OfflinePaymentOrderUpdateVoucherRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOfflinePaymentVoucher,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
