import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import { Page } from '@hbfe/common'
/**
 * @description
 */
class SchemeUtil {
  /**
   * 批量查询方案配置
   * @param schemeIds 方案id集合
   * {
   *   "方案id":"方案配置信息（已转换为对象）"
   * }
   */
  async batchQuerySchemeConfig(schemeIds: string[]): Promise<Map<string, any>> {
    const result = new Map<string, any>()
    if (!schemeIds || !schemeIds.length) return result
    const reqList = Array(Math.ceil(schemeIds.length / 200)).fill('')
    const respList = await Promise.allSettled(
      reqList.map(async (item, index) => {
        const params = {
          page: new Page(index + 1, 200),
          schemeIds: schemeIds.slice(index * 200, (index + 1) * 200)
        }
        return MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.pageSchemeConfigInServicer(params)
      })
    )
    respList.forEach(tempResp => {
      if (tempResp.status === 'fulfilled') {
        const status = tempResp.value.status
        const data = tempResp.value.data?.currentPageData
        if (status?.isSuccess() && data && data?.length) {
          data.forEach(item => {
            // json字符串需要转换
            let jsonObj = new Object()
            const json = item.schemeConfig
            if (json) {
              jsonObj = JSON.parse(json)
            }
            result.set(item.schemeId, jsonObj)
          })
        }
      }
    })
    return result
  }
}

export default SchemeUtil
