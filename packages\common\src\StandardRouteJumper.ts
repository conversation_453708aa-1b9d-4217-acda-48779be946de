import VueRouter from 'vue-router'

class StandardRouteJumper {
  base: string
  router: VueRouter

  getUrl(sub: string) {
    return `/${this.base.replace(/\./g, '/')}/${sub}`
  }

  goCreate() {
    return this.router.push({
      path: this.getUrl('create')
    })
  }

  goModify(id: string) {
    return this.router.push({
      path: `${this.getUrl('modify')}/${id}`
    })
  }

  goDetail(id: string) {
    return this.router.push({
      path: `${this.getUrl('detail')}/${id}`
    })
  }

  constructor(baseRoute: string, router: VueRouter) {
    this.base = baseRoute
    this.router = router
  }
}

export default StandardRouteJumper
