import BasicDataQueryForestage from '@api/platform-gateway/platform-training-channel-fore-gateway'

import { Page } from '@hbfe/common'

/**
 * 专题列表
 */
export default class ThematiList {
  /**
   * 列表数据
   */
  list: { id: string; name: string; url: string; entryName: string; allowAccess: boolean }[] = []
  // /**
  //  * 已选中培训方案ID
  //  */
  // selectedTrainingPlanID: string[] = []
  /**
   * 获取详情
   * @param id 专题ID
   */
  async getDetail(page: Page, id?: string) {
    this.list = []
    const res = await BasicDataQueryForestage.getPageTrainingChannelInfo({
      page,
      id
    })
    res.data.currentPageData.forEach((item) => {
      this.list.push({
        id: item.id,
        name: item.name,
        entryName: item.entryName,
        url: item.domainName,
        allowAccess: item.allowAccess
      })
    })
  }
}
