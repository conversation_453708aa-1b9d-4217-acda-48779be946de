import { GraduationStatusEnum } from '@api/service/common/scheme/enum/GraduationStatusEnum'
import StudentTrainingRequireBase from '@api/service/common/implement/enums/StudentTrainingRequireBase'
import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryBackstage, {
  LearningResponseV2,
  LearningResultResponseV2,
  StudentSchemeLearningDetailResponse,
  StudentSchemeLearningInformationResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { LearningTypeEnum } from '@api/service/common/implement/enums/LearningTypeEnum'
import AttendanceRequirement from '@api/service/common/implement/models/AttendanceRequirement'
import { GradeLearningConfigResultResponse } from '@api/platform-gateway/fxnl-query-front-gateway-backstage'

import AttendanceConfigDto from '@api/service/common/implement/models/AttendanceConfigDto'

export default class StudentPeriodLog {
  /**
   * 期别id
   */
  periodId: string = undefined

  /**
   * 学号
   */
  studentNo: string = undefined

  /**
   * 业务学号（展示的学号）
   */
  businessStudentNo = ''

  /**
   * 期别参训资格id
   */
  periodQualificationId: string = undefined

  /**
   * 考勤情况
   */
  attendance: StudentTrainingRequireBase = new StudentTrainingRequireBase()

  /**
   * 应打卡次数
   */
  attendanceRequireNum = 0

  /**
   * 要求考勤率
   */
  requireAttendanceRate: number = undefined

  /**
   * 学员考勤率
   */
  studentAttendanceRate: number = undefined

  /**
   * 结业测试合格时间
   */
  graduationQualifiedTime: string = undefined

  /**
   * 结业测试状态
   */
  graduationStatus: GraduationStatusEnum = undefined

  /**
   * 所需调研问卷完成情况（该值显示期别下问卷是否需要考核、需要考核的问卷数已经完成情况）
   */
  questionnaire: StudentTrainingRequireBase = new StudentTrainingRequireBase()

  /**
   * 问卷总数
   */
  questionnaireTotalNum = 0

  /**
   * 学员问卷总提交数
   */
  questionnaireCompletedNum = 0

  /**
   * 签到配置
   */
  signIn: AttendanceConfigDto = new AttendanceConfigDto()

  /**
   * 签退配置
   */
  signOut: AttendanceConfigDto = new AttendanceConfigDto()

  // 报道记录暂时没用上 并且后端在迁移面网授版本至8.0删除了该字段的返回，故这边同步删除报道记录
  // /**
  //  * 是否报道
  //  */
  // reported: boolean = undefined
  //
  // /**
  //  * 报道时间
  //  */
  // reportTime: string = undefined

  /**
   * 方案问卷总数
   */
  schemeQuestionnaireTotalNum = 0

  /**
   * 方案学员问卷总提交数
   */
  schemeQuestionnaireCompletedNum = 0

  /**
   * 方案所需调研问卷完成情况（该值显示期别下问卷是否需要考核、需要考核的问卷数已经完成情况）
   */
  schemeQuestionnaire: StudentTrainingRequireBase = new StudentTrainingRequireBase()

  /**
   * @param periodId 期别id
   */
  constructor(periodId?: string) {
    this.periodId = periodId
  }

  /**
   * 后端枚举转换
   * 0-不合格 1-合格
   * @private
   */
  private static backendToEnumMap: Record<number, GraduationStatusEnum> = {
    0: GraduationStatusEnum.unqualified,
    1: GraduationStatusEnum.qualified
  }

  /**
   * 查询考勤规则
   */
  async queryAttendanceConfig() {
    const { data, status } =
      await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryBackstage.getMySchemeIssueConfigInMySelf(this.periodId)
    if (status.isSuccess()) {
      const { attendanceConfigResponse } = data
      this.signIn.isOpen = attendanceConfigResponse?.attendancesSignInConfig?.enable
      this.signIn.checkInFrequency = attendanceConfigResponse?.attendancesSignInConfig?.signInFrequencyType
      this.signIn.afterCheckInTime = attendanceConfigResponse?.attendancesSignInConfig?.postSignTime / 60
      this.signIn.preCheckInTime = attendanceConfigResponse?.attendancesSignInConfig?.preSignTime / 60
      this.signOut.isOpen = attendanceConfigResponse?.attendancesSignOutConfig?.enable
      this.signOut.checkInFrequency = attendanceConfigResponse?.attendancesSignOutConfig?.signInFrequencyType
      this.signOut.afterCheckInTime = attendanceConfigResponse?.attendancesSignOutConfig?.postSignTime / 60
      this.signOut.preCheckInTime = attendanceConfigResponse?.attendancesSignOutConfig?.preSignTime / 60
    }
    return status
  }

  /**
   * 模型转换 getStudentSchemeLearningDetailInServicer
   */
  static from(DTO: StudentSchemeLearningDetailResponse) {
    // 学员期数学习信息
    const studentIssueLearning = DTO?.studentIssueLearning
    // 学习方案信息
    const studentSchemeInfo = DTO?.studentSchemeWithIssueLearningResponse
    const trainingResult = studentSchemeInfo?.learningResult?.find(
      (item: LearningResultResponseV2) => item.learningResultConfig.resultType === 1
    )
    const trainingResultDetail = trainingResult?.learningResultConfig as GradeLearningConfigResultResponse
    // 考核要求
    const vo = new StudentPeriodLog()

    // 结业测试是否合格
    vo.graduationStatus =
      StudentPeriodLog?.backendToEnumMap[trainingResultDetail?.grade] || GraduationStatusEnum.unqualified
    // 结业测试合格时间
    vo.graduationQualifiedTime = trainingResult?.gainedTime ? trainingResult?.gainedTime : '-'
    // 期别id
    vo.periodId = studentIssueLearning?.issueId
    vo.periodQualificationId = studentIssueLearning?.qualificationId
    vo.businessStudentNo = studentIssueLearning.businessStudentNo

    if (studentSchemeInfo) {
      vo.studentNo = studentSchemeInfo.studentNo
      // 方案问卷
      if (studentSchemeInfo?.learning?.length) {
        // 方案问卷合格情况
        // questionnaireRequirementCount 该字段代表有多少张问卷需要考核，如果没有则代表该期别不需要问卷考核
        vo.schemeQuestionnaire.require = !!studentSchemeInfo?.questionnaireRequirementCount
        vo.schemeQuestionnaire.totalNum = studentSchemeInfo?.questionnaireRequirementCount || 0
        vo.schemeQuestionnaire.completedNum = studentSchemeInfo?.questionnaireSubmittedCount || 0
        // 后端说问卷考核完成只要看考核问卷的提交数和总要求数是否相等即可
        vo.schemeQuestionnaire.qualified =
          studentSchemeInfo?.questionnaireSubmittedCount &&
          studentSchemeInfo?.questionnaireSubmittedCount == studentSchemeInfo?.questionnaireRequirementCount
        // 统计问卷总张数
        const findQuestionnaireList = studentSchemeInfo.learning.filter(
          (item: LearningResponseV2) => item.learningType === LearningTypeEnum.QUESTIONNAIRE
        )
        if (findQuestionnaireList?.length) {
          vo.schemeQuestionnaireTotalNum = findQuestionnaireList.length
          let completedNum = 0
          findQuestionnaireList.map((item: LearningResponseV2) => {
            // 已参与是指不需要考核的卷子交卷，参与完成是指需要考核的卷子交卷，所以二者都是交卷完成
            if ([0, 1].includes(item.learningStatus)) {
              completedNum++
            }
          })
          vo.schemeQuestionnaireCompletedNum = completedNum
        }
      }
    }

    if (studentIssueLearning.learning?.length) {
      // 获取考勤率
      const findFace = studentIssueLearning.learning.find(
        (item: LearningResponseV2) => item.learningType === LearningTypeEnum.TEACH_PLAN_LEARNING
      )
      if (findFace && findFace.userAssessResult?.length) {
        const attendanceLog = JSON.parse(findFace.userAssessResult[0]) as AttendanceRequirement
        vo.attendance.completedNum = attendanceLog.studentSignCount ? Number(attendanceLog.studentSignCount) : 0
        vo.attendance.totalNum = attendanceLog.totalSignCount ? Number(attendanceLog.totalSignCount) : 0
        vo.requireAttendanceRate = attendanceLog.signRate ? Number(attendanceLog.signRate) * 100 : 0
        vo.attendance.require = true
        if (vo.attendance.totalNum && vo.requireAttendanceRate) {
          vo.attendanceRequireNum = Math.ceil((vo.attendance.totalNum * vo.requireAttendanceRate) / 100)
        }
        // 学员考勤率：学员考勤次数除以总需考勤次数向下取证
        vo.studentAttendanceRate = vo.attendance.totalNum
          ? Math.floor((vo.attendance.completedNum / vo.attendance.totalNum) * 100)
          : 0
        vo.attendance.qualified = findFace.learningAssessResult == 1

        // 迁移面网授至8.0时 后端删除了该接口获取报道记录
        // if (findFace.reportInfo) {
        //   vo.reported = findFace.reportInfo.isReport
        //   vo.reportTime = findFace.reportInfo.reportTime
        // }
      } else {
        vo.attendance.require = false
      }

      // 问卷合格情况
      // questionnaireRequirementCount 该字段代表有多少张问卷需要考核，如果没有则代表该期别不需要问卷考核
      vo.questionnaire.require = !!studentIssueLearning?.questionnaireRequirementCount
      vo.questionnaire.totalNum = studentIssueLearning?.questionnaireRequirementCount || 0
      vo.questionnaire.completedNum = studentIssueLearning?.questionnaireSubmittedCount || 0
      // 后端说问卷考核完成只要看考核问卷的提交数和总要求数是否相等即可
      vo.questionnaire.qualified =
        studentIssueLearning?.questionnaireSubmittedCount &&
        studentIssueLearning?.questionnaireSubmittedCount == studentIssueLearning?.questionnaireRequirementCount
      // 统计问卷总张数
      const findQuestionnaireList = studentIssueLearning.learning.filter(
        (item: LearningResponseV2) => item.learningType === LearningTypeEnum.QUESTIONNAIRE
      )
      if (findQuestionnaireList?.length) {
        vo.questionnaireTotalNum = findQuestionnaireList.length
        let completedNum = 0
        findQuestionnaireList.map((item: LearningResponseV2) => {
          // 已参与是指不需要考核的卷子交卷，参与完成是指需要考核的卷子交卷，所以二者都是交卷完成
          if ([0, 1].includes(item.learningStatus)) {
            completedNum++
          }
        })
        vo.questionnaireCompletedNum = completedNum
      }
    }
    return vo
  }

  /**
   * 列表模型转换 pageStudentSchemeLearningInformationInServicer
   * @param dto
   */
  static fromItem(dto: StudentSchemeLearningInformationResponse) {
    // todo 这边写两份的原因是后续这两个对象可能不一样  所以就分开了
    // 学员期数学习信息

    const studentIssueLearning = dto?.studentIssueLearning
    // 学习方案信息
    const studentSchemeInfo = dto?.studentSchemeWithIssueLearningResponse
    const trainingResult = studentSchemeInfo?.learningResult?.find(
      (item: LearningResultResponseV2) => item?.learningResultConfig?.resultType === 1
    )
    const trainingResultDetail = trainingResult?.learningResultConfig as GradeLearningConfigResultResponse
    // 考核要求
    const vo = new StudentPeriodLog()
    // 结业测试是否合格
    vo.graduationStatus =
      StudentPeriodLog?.backendToEnumMap[trainingResultDetail?.grade] || GraduationStatusEnum.unqualified
    // 结业测试合格时间
    vo.graduationQualifiedTime = trainingResult?.gainedTime ? trainingResult?.gainedTime : '-'
    // 期别id
    vo.periodId = studentIssueLearning?.issueId
    vo.periodQualificationId = studentIssueLearning?.qualificationId
    vo.businessStudentNo = studentIssueLearning.businessStudentNo

    if (studentSchemeInfo) {
      vo.studentNo = studentSchemeInfo.studentNo
      // 方案问卷
      if (studentSchemeInfo.learning.length) {
        // 方案问卷合格情况
        // questionnaireRequirementCount 该字段代表有多少张问卷需要考核，如果没有则代表该期别不需要问卷考核
        vo.schemeQuestionnaire.require = !!studentSchemeInfo?.questionnaireRequirementCount
        vo.schemeQuestionnaire.totalNum = studentSchemeInfo?.questionnaireRequirementCount || 0
        vo.schemeQuestionnaire.completedNum = studentSchemeInfo?.questionnaireSubmittedCount || 0
        // 后端说问卷考核完成只要看考核问卷的提交数和总要求数是否相等即可
        vo.schemeQuestionnaire.qualified =
          studentSchemeInfo?.questionnaireSubmittedCount &&
          studentSchemeInfo?.questionnaireSubmittedCount == studentSchemeInfo?.questionnaireRequirementCount
        // 统计问卷总张数
        const findQuestionnaireList = studentSchemeInfo.learning.filter(
          (item: LearningResponseV2) => item.learningType === LearningTypeEnum.QUESTIONNAIRE
        )
        if (findQuestionnaireList?.length) {
          vo.schemeQuestionnaireTotalNum = findQuestionnaireList.length
          let completedNum = 0
          findQuestionnaireList.map((item: LearningResponseV2) => {
            // 已参与是指不需要考核的卷子交卷，参与完成是指需要考核的卷子交卷，所以二者都是交卷完成
            if ([0, 1].includes(item.learningStatus)) {
              completedNum++
            }
          })
          vo.schemeQuestionnaireCompletedNum = completedNum
        }
      }
    }

    if (studentIssueLearning.learning?.length) {
      // 获取考勤率
      const findFace = studentIssueLearning.learning.find(
        (item: LearningResponseV2) => item.learningType === LearningTypeEnum.TEACH_PLAN_LEARNING
      )
      if (findFace && findFace.userAssessResult?.length) {
        const attendanceLog = JSON.parse(findFace.userAssessResult[0]) as AttendanceRequirement
        vo.attendance.completedNum = attendanceLog.studentSignCount ? Number(attendanceLog.studentSignCount) : 0
        vo.attendance.totalNum = attendanceLog.totalSignCount ? Number(attendanceLog.totalSignCount) : 0
        vo.requireAttendanceRate = attendanceLog.signRate ? Number(attendanceLog.signRate) * 100 : 0
        vo.attendance.require = true
        // 学员考勤率：学员考勤次数除以总需考勤次数向下取证
        vo.studentAttendanceRate = vo.attendance.totalNum
          ? Math.floor((vo.attendance.completedNum / vo.attendance.totalNum) * 100)
          : 0
        vo.attendance.qualified = findFace.learningAssessResult == 1

        // 迁移面网授至8.0时 后端删除了该接口获取报道记录
        // if (findFace.reportInfo) {
        //   vo.reported = findFace.reportInfo.isReport
        //   vo.reportTime = findFace.reportInfo.reportTime
        // }
      } else {
        vo.attendance.require = false
      }

      // 问卷合格情况
      // questionnaireRequirementCount 该字段代表有多少张问卷需要考核，如果没有则代表该期别不需要问卷考核
      vo.questionnaire.require = !!studentIssueLearning?.questionnaireRequirementCount
      vo.questionnaire.totalNum = studentIssueLearning?.questionnaireRequirementCount || 0
      vo.questionnaire.completedNum = studentIssueLearning?.questionnaireSubmittedCount || 0
      // 后端说问卷考核完成只要看考核问卷的提交数和总要求数是否相等即可
      vo.questionnaire.qualified =
        studentIssueLearning?.questionnaireSubmittedCount &&
        studentIssueLearning?.questionnaireSubmittedCount == studentIssueLearning?.questionnaireRequirementCount
      // 统计问卷总张数
      const findQuestionnaireList = studentIssueLearning.learning.filter(
        (item: LearningResponseV2) => item.learningType === LearningTypeEnum.QUESTIONNAIRE
      )
      if (findQuestionnaireList?.length) {
        vo.questionnaireTotalNum = findQuestionnaireList.length
        let completedNum = 0
        findQuestionnaireList.map((item: LearningResponseV2) => {
          // 已参与是指不需要考核的卷子交卷，参与完成是指需要考核的卷子交卷，所以二者都是交卷完成
          if ([0, 1].includes(item.learningStatus)) {
            completedNum++
          }
        })
        vo.questionnaireCompletedNum = completedNum
      }
    }

    return vo
  }
}
