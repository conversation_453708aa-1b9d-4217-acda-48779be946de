<template>
  <div class="user-info-page">
    <el-card v-loading="loading">
      <div slot="header" class="card-header">
        <div class="header-left">
          <el-avatar v-if="userInfo.photo" :src="userInfo.photo" :size="50" class="user-avatar"></el-avatar>
          <div class="header-info">
            <span class="user-title">{{ userInfo.userName || '用户详情' }}</span>
            <span class="user-subtitle">{{ userInfo.nickName || userInfo.loginAccount }}</span>
          </div>
        </div>
      </div>

      <!-- 用户基本信息 -->
      <el-descriptions :column="2" border v-if="userInfo.userId">
        <el-descriptions-item label="用户ID">
          {{ userInfo.userId }}
        </el-descriptions-item>
        <el-descriptions-item label="用户名">
          {{ userInfo.userName || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="用户昵称">
          {{ userInfo.nickName || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="身份证号">
          {{ userInfo.idCard || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          {{ userInfo.phone || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="邮箱">
          {{ userInfo.email || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="性别">
          {{ userInfo.genderName || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="登录账号">
          {{ userInfo.loginAccount || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="工作单位">
          {{ userInfo.companyName || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="单位统一社会信用代码">
          {{ userInfo.companyCode || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="联系地址">
          {{ userInfo.address || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="注册时间">
          {{ formatDate(userInfo.createTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="来源类型">
          {{ userInfo.sourceTypeName || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="是否绑定微信">
          <el-tag :type="userInfo.bindWX ? 'success' : 'info'">
            {{ userInfo.bindWX ? '已绑定' : '未绑定' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="微信昵称" v-if="userInfo.bindWX">
          {{ userInfo.nickNameByWX || '未设置' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 行业信息 -->
      <div v-if="hasIndustryInfo" style="margin-top: 30px">
        <h3>行业信息</h3>

        <!-- 人社行业信息 -->
        <el-card v-if="userInfo.rsStudentIndustryInfo" class="industry-card">
          <div slot="header">人社行业信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="专业类别">
              {{ userInfo.rsStudentIndustryInfo.firstProfessionalCategoryName || '无' }}
            </el-descriptions-item>
            <el-descriptions-item label="专业子类">
              {{ userInfo.rsStudentIndustryInfo.secondProfessionalCategoryName || '无' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 建设行业信息 -->
        <el-card v-if="userInfo.jsStudentIndustryInfo" class="industry-card">
          <div slot="header">建设行业信息</div>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="专业类别">
              {{ userInfo.jsStudentIndustryInfo.firstProfessionalCategory || '未设置' }}
            </el-descriptions-item>
            <el-descriptions-item label="专业子类">
              {{ userInfo.jsStudentIndustryInfo.secondProfessionalCategory || '未设置' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </div>

      <!-- 空状态 -->
      <el-empty v-else-if="!loading" description="用户信息不存在">
        <el-button type="primary" @click="$router.go(-1)">返回</el-button>
      </el-empty>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons" v-if="userInfo.accountId">
      <el-button @click="$router.go(-1)">返回</el-button>
    </div>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import moment from 'moment'
  import UserModule from '@api/service/management/user/UserModule'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'

  @Component
  export default class UserInfoPage extends Vue {
    // 数据属性
    private userInfo: UserDetailVo = new UserDetailVo()
    private loading = false

    async mounted() {
      await this.loadUserInfo()
    }

    get userId() {
      return this.$route.params.id
    }

    get hasIndustryInfo() {
      return (
        this.userInfo.rsStudentIndustryInfo ||
        this.userInfo.jsStudentIndustryInfo ||
        this.userInfo.wsStudentIndustryInfo ||
        this.userInfo.gqStudentIndustryInfo ||
        this.userInfo.lsStudentIndustryInfo ||
        this.userInfo.ysStudentIndustryInfo
      )
    }

    private async loadUserInfo() {
      if (!this.userId) {
        this.$message.error('用户ID不能为空')
        this.$router.go(-1)
        return
      }

      this.loading = true
      try {
        const queryStudentDetail = UserModule.queryUserFactory.queryStudentDetail(this.userId)
        const response = await queryStudentDetail.queryDetail()
        if (response.status.isSuccess()) {
          this.userInfo = response.data
        } else {
          this.$message.error(`加载用户信息失败: ${response.status.message}`)
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
        this.$message.error('网络错误，使用模拟数据')
      } finally {
        this.loading = false
      }
    }

    // 格式化日期
    private formatDate(dateString: string) {
      if (!dateString) return ''
      return moment(dateString).format('YYYY-MM-DD HH:mm:ss')
    }

    // 删除用户
    private async handleDelete() {
      try {
        await this.$confirm(`确定要删除用户 "${this.userInfo.realName || this.userInfo.username}" 吗？`, '删除确认', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // TODO: 调用删除API 目前好像没有看到之前的页面有删除用户功能
        this.$message.success('用户删除成功')
        this.$router.push('/user/list')
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除用户失败')
        }
      }
    }
  }
</script>

<style scoped>
  .user-info-page {
    padding: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .header-left {
    display: flex;
    align-items: center;
  }

  .user-avatar {
    margin-right: 15px;
  }

  .header-info {
    display: flex;
    flex-direction: column;
  }

  .user-title {
    font-size: 18px;
    font-weight: bold;
    color: #303133;
    margin-bottom: 4px;
  }

  .user-subtitle {
    font-size: 14px;
    color: #909399;
  }

  .action-buttons {
    margin-top: 20px;
    text-align: center;
  }

  .action-buttons .el-button {
    margin: 0 10px;
  }

  .el-descriptions {
    margin-top: 20px;
  }

  .industry-card {
    margin-bottom: 20px;
  }

  .industry-card:last-child {
    margin-bottom: 0;
  }
</style>
