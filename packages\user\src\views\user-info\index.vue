<template>
  <div class="user-info-page">
    <el-card v-loading="loading">
      <div slot="header" class="card-header">
        <span>用户详细信息</span>
        <el-button
          v-if="$hasPermission('user.edit')"
          style="float: right; padding: 3px 0"
          type="text"
          @click="handleEdit"
        >
          编辑用户
        </el-button>
      </div>

      <!-- 用户基本信息 -->
      <el-descriptions :column="2" border v-if="userInfo.id">
        <el-descriptions-item label="用户ID">
          {{ userInfo.id }}
        </el-descriptions-item>
        <el-descriptions-item label="用户名">
          {{ userInfo.username || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="真实姓名">
          {{ userInfo.realName || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="邮箱">
          {{ userInfo.email || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          {{ userInfo.phone || '未设置' }}
        </el-descriptions-item>
        <el-descriptions-item label="用户状态">
          <el-tag :type="userInfo.status === 'ACTIVE' ? 'success' : 'danger'">
            {{ userInfo.status === 'ACTIVE' ? '激活' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="用户角色">
          <el-tag v-for="role in userInfo.roles" :key="role.id" style="margin-right: 5px">
            {{ role.name }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">
          {{ formatDate(userInfo.createTime) }}
        </el-descriptions-item>
        <el-descriptions-item label="最后登录">
          {{ formatDate(userInfo.lastLoginTime) || '从未登录' }}
        </el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">
          {{ userInfo.remark || '无备注' }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 空状态 -->
      <el-empty v-else-if="!loading" description="用户信息不存在">
        <el-button type="primary" @click="$router.go(-1)">返回</el-button>
      </el-empty>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-buttons" v-if="userInfo.id">
      <el-button @click="$router.go(-1)">返回</el-button>
      <el-button v-if="$hasPermission('user.edit')" type="primary" @click="handleEdit"> 编辑用户 </el-button>
      <el-button v-if="$hasPermission('user.delete')" type="danger" @click="handleDelete"> 删除用户 </el-button>
    </div>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import moment from 'moment'
  import UserModule from '@api/service/management/user/UserModule'

  // 用户信息接口定义
  interface UserInfo {
    id: string
    username: string
    realName: string
    email: string
    phone: string
    status: 'ACTIVE' | 'INACTIVE'
    roles: Array<{
      id: string
      name: string
    }>
    createTime: string
    lastLoginTime: string
    remark: string
  }

  @Component
  export default class UserInfoPage extends Vue {
    // 数据属性
    private userInfo: UserInfo = {} as UserInfo
    private loading = false

    // 生命周期
    async mounted() {
      await this.loadUserInfo()
    }

    // 计算属性
    get userId() {
      return this.$route.params.id
    }

    // 方法
    private async loadUserInfo() {
      if (!this.userId) {
        this.$message.error('用户ID不能为空')
        this.$router.go(-1)
        return
      }

      this.loading = true
      try {
        // 调用真实的API获取用户详情
        const queryStudentDetail = UserModule.queryUserFactory.queryStudentDetail(this.userId)
        const response = await queryStudentDetail.queryDetail()

        if (response.status.isSuccess()) {
          // 将API返回的数据转换为页面需要的格式
          const apiData = response.data
          this.userInfo = {
            id: apiData.userId || this.userId,
            username: apiData.account || '',
            realName: apiData.name || '',
            email: apiData.email || '',
            phone: apiData.phone || '',
            status: apiData.status === 1 ? 'ACTIVE' : 'INACTIVE',
            roles: apiData.roles || [],
            createTime: apiData.createTime || '',
            lastLoginTime: apiData.lastLoginTime || '',
            remark: apiData.remark || ''
          }
        } else {
          this.$message.error(`加载用户信息失败: ${response.status.message}`)
          // 如果API调用失败，使用模拟数据作为后备
          await this.mockLoadUserInfo()
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
        this.$message.error('网络错误，使用模拟数据')
        // 网络错误时使用模拟数据
        await this.mockLoadUserInfo()
      } finally {
        this.loading = false
      }
    }

    // 模拟API调用（作为后备方案）
    private async mockLoadUserInfo() {
      return new Promise((resolve) => {
        setTimeout(() => {
          this.userInfo = {
            id: this.userId,
            username: 'testuser',
            realName: '测试用户',
            email: '<EMAIL>',
            phone: '13800138000',
            status: 'ACTIVE',
            roles: [
              { id: '1', name: '学员' },
              { id: '2', name: '普通用户' }
            ],
            createTime: '2024-01-01T10:00:00Z',
            lastLoginTime: '2024-01-15T15:30:00Z',
            remark: '这是一个测试用户账号'
          }
          resolve(true)
        }, 500) // 模拟网络延迟
      })
    }

    // 格式化日期
    private formatDate(dateString: string) {
      if (!dateString) return ''
      return moment(dateString).format('YYYY-MM-DD HH:mm:ss')
    }

    // 编辑用户
    private handleEdit() {
      this.$router.push(`/user/${this.userId}/edit`)
    }

    // 删除用户
    private async handleDelete() {
      try {
        await this.$confirm(`确定要删除用户 "${this.userInfo.realName || this.userInfo.username}" 吗？`, '删除确认', {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // TODO: 调用删除API
        this.$message.success('用户删除成功')
        this.$router.push('/user/list')
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除用户失败')
        }
      }
    }
  }
</script>

<style scoped>
  .user-info-page {
    padding: 20px;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .action-buttons {
    margin-top: 20px;
    text-align: center;
  }

  .action-buttons .el-button {
    margin: 0 10px;
  }
</style>
