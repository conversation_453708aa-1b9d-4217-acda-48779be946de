import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum CommodityRefundStatusEnum {
  /**
   * 未退货退款
   */
  unRefund = 0,

  /**
   * 已退货，已退款
   */
  returnedFullRefund = 1,

  /**
   * 已退货，未退款
   */
  returnedFull = 2,

  /**
   * 已退款，未退货
   */
  refundedFull = 3,

  /**
   * 已部分退款，未退货
   */
  partialRefund = 4,

  /**
   * 已部分退货，未退款
   */
  partialReturn = 5,

  /**
   * 已退货，已部分退款
   */
  returnedPartialRefund = 6,

  /**
   * 已部分退货，已退款
   */
  partialReturnFullRefund = 7,

  /**
   * 已部分退货，已部分退款
   */
  partialReturnPartialRefund = 8,

  /**
   * 退货/款处理中
   */
  processing = 9
}

// 包含退货的枚举数组
export const CommodityRefundStatusWithGoods = [
  CommodityRefundStatusEnum.returnedFullRefund,
  CommodityRefundStatusEnum.returnedFull,
  CommodityRefundStatusEnum.partialReturn,
  CommodityRefundStatusEnum.returnedPartialRefund,
  CommodityRefundStatusEnum.partialReturnFullRefund,
  CommodityRefundStatusEnum.partialReturnPartialRefund
]

class CommodityRefundStatus extends AbstractEnum<CommodityRefundStatusEnum> {
  static enum = CommodityRefundStatus
  constructor(status?: CommodityRefundStatusEnum) {
    super()
    this.current = status
    this.map.set(CommodityRefundStatusEnum.unRefund, '')
    this.map.set(CommodityRefundStatusEnum.returnedFullRefund, '退货并退款')
    this.map.set(CommodityRefundStatusEnum.returnedFull, '已退货，未退款')
    this.map.set(CommodityRefundStatusEnum.refundedFull, '已退款，未退货')
    this.map.set(CommodityRefundStatusEnum.partialRefund, '已部分退款，未退货')
    this.map.set(CommodityRefundStatusEnum.partialReturn, '已部分退货，未退款')
    this.map.set(CommodityRefundStatusEnum.returnedPartialRefund, '已退货，已部分退款')
    this.map.set(CommodityRefundStatusEnum.partialReturnFullRefund, '已部分退货，已退款')
    this.map.set(CommodityRefundStatusEnum.partialReturnPartialRefund, '已部分退货，已部分退款')
    this.map.set(CommodityRefundStatusEnum.processing, '退货/款处理中')
  }

  /**
   * 转换子单状态为当前枚举状态
   *
   * 子订单退货状态
   * 0：未退货
   * 1：退货申请中
   * 2：退货中
   * 3：退货成功
   * 4：退款中
   * 5：退款成功
   * @param dtoReturnStatus
   *
   * 子订单退款情况
   * 0: 未退款,
   * 1: 已部分退款,
   * 2: 已全部退款
   * @param dtoRefundSchedule
   *
   * 子订单退货情况
   * 0: 未退货,
   * 1: 已部分退货,
   * 2: 已全部退货
   * @param dtoReturnSchedule
   */
  transferSubOrderStatusToCurrent(dtoReturnStatus: number, dtoRefundSchedule: number, dtoReturnSchedule: number) {
    if ([1, 2, 4].includes(dtoReturnStatus)) {
      return CommodityRefundStatusEnum.processing
    }
    if (dtoRefundSchedule == 0 && dtoReturnSchedule == 0) {
      return CommodityRefundStatusEnum.unRefund
    }
    if (dtoRefundSchedule == 0 && dtoReturnSchedule == 1) {
      return CommodityRefundStatusEnum.partialReturn
    }
    if (dtoRefundSchedule == 0 && dtoReturnSchedule == 2) {
      return CommodityRefundStatusEnum.returnedFull
    }

    if (dtoRefundSchedule == 1 && dtoReturnSchedule == 0) {
      return CommodityRefundStatusEnum.partialRefund
    }
    if (dtoRefundSchedule == 1 && dtoReturnSchedule == 1) {
      return CommodityRefundStatusEnum.partialReturnPartialRefund
    }
    if (dtoRefundSchedule == 1 && dtoReturnSchedule == 2) {
      return CommodityRefundStatusEnum.returnedPartialRefund
    }

    if (dtoRefundSchedule == 2 && dtoReturnSchedule == 0) {
      return CommodityRefundStatusEnum.refundedFull
    }
    if (dtoRefundSchedule == 2 && dtoReturnSchedule == 1) {
      return CommodityRefundStatusEnum.partialReturnFullRefund
    }
    if (dtoRefundSchedule == 2 && dtoReturnSchedule == 2) {
      return CommodityRefundStatusEnum.returnedFullRefund
    }
  }
}

export default new CommodityRefundStatus()
