import { Response, ResponseStatus, UiPage } from '@hbfe/common'
import msCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CourserPackageSyncSchemeRequest,
  CourserPackageSyncSchemeStatisticResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import msCourselearningResourceV1 from '@api/ms-gateway/ms-courselearning-resource-v1'
import CourserPackageSyncSchemeVo from './vo/CourserPackageSyncSchemeVo'
import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
import TrainClassConfigJsonManager from '@api/service/management/train-class/Utils/TrainClassConfigJsonManager'
import ConfigJsonUtil from '@api/service/management/train-class/Utils/ConfigJsonUtil'
import QuerySyncSchemeParam from '@api/service/management/resource/course-package/mutation/vo/QuerySyncSchemeParam'

class MutationSyncCoursePackage {
  // 课程包id
  private coursePackageId: string
  constructor(coursePackageId: string) {
    this.coursePackageId = coursePackageId
  }

  async single(schemeId: string): Promise<ResponseStatus> {
    if (!schemeId) {
      return new ResponseStatus(500, '未选择方案')
    }
    const res = await msCourselearningResourceV1.syncCoursePackageUsed([schemeId])
    return res.status
  }

  /**
   * 批量同步
   * @param schemeIdList
   */
  async batch(schemeIdList: Array<string>): Promise<ResponseStatus> {
    console.log(schemeIdList)
    if (!schemeIdList.length) {
      return new ResponseStatus(500, '未选择方案')
    }
    const res = await msCourselearningResourceV1.syncCoursePackageUsed(schemeIdList)
    return res.status
  }

  /**
   * 同步培训方案列表
   */
  async querySyncTrainingSchemePage(
    page: UiPage,
    queryParams: QuerySyncSchemeParam
  ): Promise<Response<Array<CourserPackageSyncSchemeVo>>> {
    const schemeIds: string[] = []
    queryParams.coursePackageId = this.coursePackageId
    const request = QuerySyncSchemeParam.to(queryParams)
    const pageRes = await msCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourserPackageSyncSchemeInServicer(
      {
        page: page,
        request
      }
    )
    page.totalSize = pageRes.data.totalSize
    page.totalPageSize = pageRes.data.totalPageSize
    const response = new Response<Array<CourserPackageSyncSchemeVo>>()
    if (!pageRes.status.isSuccess()) {
      response.status = pageRes.status
      return response
    }
    pageRes.data.currentPageData.map(res => {
      schemeIds.push(res.schemeId)
    })
    const schemeMap = {}
    const queryM = new ConfigJsonUtil()
    const schemeJsonMap = await queryM.batchQuerySchemeJsonConfigMapBySchemeId(
      schemeIds,
      ConfigJsonUtil.defaultNeedFields
    )
    for (const [key, value] of schemeJsonMap.entries()) {
      schemeMap[key] = value
    }
    response.data = CourserPackageSyncSchemeVo.from(pageRes.data.currentPageData, schemeMap)
    response.status = pageRes.status
    return response
  }

  /**
   * 培训方案的统计数据
   */
  async queryCourserPackageSyncSchemeStatistic(
    queryParams: QuerySyncSchemeParam
  ): Promise<Response<CourserPackageSyncSchemeStatisticResponse>> {
    queryParams.coursePackageId = this.coursePackageId
    const request = QuerySyncSchemeParam.to(queryParams)
    const res = await msCourseLearningQueryFrontGatewayCourseLearningBackstage.statisticCourserPackageSyncSchemeInServicer(
      request
    )
    const response = new Response<CourserPackageSyncSchemeStatisticResponse>()
    if (!res.status.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = res.data
    response.status = res.status
    return response
  }

  /**
   * 查询同步日志
   */
  async querySyncLog(queryParams: CourserPackageSyncSchemeRequest) {
    return new ResponseStatus(200, '')
  }
}

export default MutationSyncCoursePackage
