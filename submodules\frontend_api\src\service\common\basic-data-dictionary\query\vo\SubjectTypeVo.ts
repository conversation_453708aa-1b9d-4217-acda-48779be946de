import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

class SubjectTypeVo {
  /**
   * 唯一标识
   */
  propertyId: string
  /**
   * 科目名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
  /**
   * 展示名称
   */
  showName: string

  static from(res: TrainingPropertyResponse) {
    const subjectType = new TrainingPropertyResponse()
    subjectType.name = res.name
    subjectType.propertyId = res.propertyId
    subjectType.sort = res.sort
    subjectType.parentId = res.parentId
    subjectType.showName = res.showName
    return subjectType
  }
}

export default SubjectTypeVo
