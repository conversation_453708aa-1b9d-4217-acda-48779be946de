import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-course-resource-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-course-resource-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * @author: zhengp
@since 2021/10/28 11:50
 */
export class ExtensionResourceDataDto {
  /**
   * 外链视频信息
   */
  extensionVideoInfo?: ExtensionResourceData
}

/**
 * @author: zhengp
@since 2021/10/28 11:50
 */
export class ResourceDataDto {
  /**
   * 课件媒体资源存储路径
   */
  resourcePath?: string
  /**
   * 时长
   */
  timeLength: number
  resourceMD5?: string
  /**
   * 字幕地址
   */
  videoCaptionPath?: string
  /**
   * 视频转码设置
   */
  videoTranscodeSettings?: VideoTranscodeSettings
}

/**
 * 八百里视频转码设置
 */
export class BABAILIVideoTranscodeSetting {
  /**
   * 是否进行转码
   */
  transcode: boolean
  /**
   * 转码清晰度，1代表普屏流畅|2代表普屏标清|3代表普屏高清|4代表宽屏流畅|5代表宽屏标清|6代表宽屏高清
   */
  clarityList?: Array<number>
}

/**
 * 华为云视频转码设置
 */
export class HWYVideoTranscodeSetting {
  /**
   * 是否进行转码
   */
  transcode: boolean
  /**
   * 华为云转码模板名称
   */
  templateName?: string
  /**
   * 是否抽取音频
   */
  extractAudio: boolean
}

/**
 * 保利威视转码设置
 */
export class POLYVVideoTranscodeSetting {
  /**
   * 是否进行转码
   */
  transcode: boolean
}

/**
 * 视频转码设置
 */
export class VideoTranscodeSettings {
  /**
   * 八百里转码配置
   */
  babailiSetting?: BABAILIVideoTranscodeSetting
  /**
   * 保利威视转码配置
   */
  polyvSetting?: POLYVVideoTranscodeSetting
  /**
   * 华为云转码配置
   */
  hwySetting?: HWYVideoTranscodeSetting
}

/**
 * @author: zhengp 2022/5/10 19:36
 */
export class ExportCoursePackageParam {
  /**
   * 主任务id
   */
  mainTaskId?: string
  /**
   * 导出任务状态 默认为全部数据 1为成功 0为失败
   */
  dataType?: number
}

/**
 * @author: zhengp
@since 2021/10/22 16:21
 */
export class ExtensionResourceData {
  videoInfoDtos?: Array<VideoInfoDto>
}

export class VideoInfoDto {
  /**
   * 视频播放路劲，相对路径
   */
  path?: string
  /**
   * 视频清晰度
1:流畅普屏 2:标清普屏 3:高清普屏 4:流畅宽屏 5:标清宽屏 6:高清宽屏 7:超清宽屏 8:流畅 9:标清 10:高清 11:超清
负数是代表手机端对应的清晰度
   */
  clarity: number
}

/**
 * 分页查询课程包导入任务数据请求信息
@author: zhengp 2022/5/12 10:18
 */
export class PageCoursePackageImportTaskRequest {
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 执行时间（起始）
   */
  executeStartTime?: string
  /**
   * 执行时间（终止）
   */
  executeEndTime?: string
}

/**
 * 分页查询课程包导入任务数据请求信息
@author: zhengp 2022/5/12 10:18
 */
export class PageCoursewareImportTaskRequest {
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 执行时间（起始）
   */
  executeStartTime?: string
  /**
   * 执行时间（终止）
   */
  executeEndTime?: string
}

/**
 * 课程校验信息
 */
export class CheckCourseRequest {
  /**
   * 课程id
   */
  id?: string
  /**
   * 课程名称
   */
  name: string
}

/**
 * 课程分类创建请求信息
@author: zhengp 2022/1/13 20:18
 */
export class CourseCategoryCreateRequest {
  /**
   * 课程分类名称
   */
  name: string
  /**
   * 课程分类描述
   */
  description?: string
  /**
   * 父级课程分类 顶级为-1
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
}

/**
 * @author: zhengp 2022/1/15 10:36
 */
export class CourseCategoryUpdateRequest {
  /**
   * 课程分类id
   */
  id: string
  /**
   * 课程分类名称
   */
  name: string
  /**
   * 课程分类描述
   */
  description?: string
  /**
   * 父级课程分类 顶级为-1
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 课程章节
@author: zhengp 2022/1/14 9:19
 */
export class CourseChapterRequest {
  /**
   * 课程章节id
   */
  id?: string
  /**
   * 章节名称
   */
  name: string
  /**
   * 课件id
   */
  coursewareId: string
  /**
   * 排序
   */
  sort: number
  /**
   * 挂在课件是否支持试听 0不可以试听，1可以试听，
   */
  auditionStatus: number
}

/**
 * 课程创建请求信息
@author: zhengp 2022/1/13 20:18
 */
export class CourseCreateRequest {
  /**
   * 课程目录
   */
  courseOutlines?: Array<CourseOutlineWithSubOutlineRequest>
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 课件供应商id
   */
  supplierId?: string
  /**
   * 分类id
   */
  categoryIds: Array<string>
  /**
   * 课程简介
   */
  aboutsContent?: string
}

/**
 * 课程目录创建请求信息
@author: zhengp 2022/1/13 20:26
 */
export class CourseOutlineWithSubOutlineRequest {
  /**
   * 子目录
   */
  subCourseOutlines?: Array<CourseOutlineWithSubOutlineRequest>
  /**
   * 课程目录id
   */
  id?: string
  /**
   * 目录名称
   */
  name?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 课程章节
   */
  courseChapters?: Array<CourseChapterRequest>
}

/**
 * 课程更新请求信息
@author: zhengp 2022/1/14 10:13
 */
export class CourseUpdateRequest {
  /**
   * 课程id
   */
  id: string
  /**
   * 课程目录
   */
  courseOutlines?: Array<CourseOutlineWithSubOutlineRequest>
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 课件供应商id
   */
  supplierId?: string
  /**
   * 分类id
   */
  categoryIds: Array<string>
  /**
   * 课程简介
   */
  aboutsContent?: string
}

/**
 * @author: xucenhao
@time: 2024-08-08
@description: 课程包校验信息
 */
export class CheckCoursePackageRequest {
  /**
   * 课程包id
   */
  id?: string
  /**
   * 课程包名称
   */
  name?: string
  /**
   * 方便拓展
   */
  type: number
}

/**
 * @author: zhengp 2022/1/11 16:55
 */
export class CourseInPackageRequest {
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 课程序号
   */
  sort: number
  /**
   * 课程在课程池中的学时
   */
  period: number
}

/**
 * 课程包创建请求信息
@author: zhengp 2022/1/11 16:51
 */
export class CoursePackageCreateRequest {
  /**
   * 课程包名称
   */
  name: string
  /**
   * 课程包展示名称
   */
  displayName?: string
  /**
   * 包内课程
   */
  courseInPackageList?: Array<CourseInPackageRequest>
}

/**
 * 课程包更新
@author: zhengp 2022/1/11 17:13
 */
export class CoursePackageUpdateRequest {
  /**
   * 课程包id
   */
  id: string
  /**
   * 课程包名称
   */
  name: string
  /**
   * 课程包展示名称
   */
  displayName?: string
  /**
   * 包内课程
   */
  courseInPackageList?: Array<CourseInPackageRequest>
}

/**
 * @BelongsProject: fjhb-microservice-course-resource
@BelongsPackage: com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.courseware
@Author: XuCenHao
@CreateTime: 2023-12-08  14:19
@Description:
 */
export class BatchImportCoursewareCreateRequest {
  /**
   * excel文件路径
   */
  excelFilePath: string
  /**
   * 文件名称 (需要带后缀名)
   */
  excelFileName: string
}

/**
 * @author: xucenhao
@time: 2024-08-08
@description: 课件校验信息
 */
export class CheckCoursewareRequest {
  /**
   * 课件id
   */
  id?: string
  /**
   * 课件名称
   */
  name?: string
}

/**
 * <AUTHOR> 2023/6/12
 */
export class ConfigRequest {
  /**
   * 外链
   */
  extensionResourceDataDto?: ExtensionResourceDataDto
  /**
   * resourceDataDto;
   */
  resourceDataDto?: ResourceDataDto
}

/**
 * 课件分类创建请求信息
@author: zhengp 2022/1/13 20:18
 */
export class CoursewareCategoryCreateRequest {
  /**
   * 课件分类名称
   */
  name: string
  /**
   * 课件分类描述
   */
  description?: string
  /**
   * 父级课件分类 顶级为-1
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
}

/**
 * @author: zhengp 2022/1/15 10:36
 */
export class CoursewareCategoryUpdateRequest {
  /**
   * 课程分类id
   */
  id: string
  /**
   * 课件分类名称
   */
  name: string
  /**
   * 课件分类描述
   */
  description?: string
  /**
   * 父级课件分类 顶级为-1
   */
  parentId: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 课件创建请求
@author: xgh 2022/5/23 17:54
 */
export class CoursewareCreateRequest {
  /**
   * 课件分类Id
   */
  categoryId: string
  /**
   * 课件名称
   */
  coursewareName: string
  /**
   * 课件供应商Id
   */
  supplierId: string
  /**
   * 教师名称
   */
  teacherName?: string
  /**
   * 教师简介
   */
  teacherDescription?: string
  /**
   * 课件简介
   */
  coursewareDescription?: string
  /**
   * 资源类型  VIDEO_SINGLE &#x3D; 1  VIDEO_EXTENSION &#x3D; 2 [必填]
   */
  resourceType: number
  /**
   * 课件媒体资源配置json字符串
@see ExtensionResourceDataDto
@see ResourceDataDto
   */
  configJSON: string
}

/**
 * 课程更新请求
@author: xgh 2022/5/25 14:07
 */
export class CoursewareUpdateRequest {
  /**
   * 课件id
   */
  id: string
  /**
   * 课件分类id
   */
  categoryId: string
  /**
   * 课件名称
   */
  name: string
  /**
   * 课件供应商id
   */
  supplierId: string
  /**
   * 课件教师id
   */
  teacherId?: string
  /**
   * 教师名称
   */
  teacherName?: string
  /**
   * 教师简介
   */
  teacherAboutsContent?: string
  /**
   * 课件简介
   */
  aboutsContent?: string
  /**
   * 课程状态
   */
  enable: boolean
  /**
   * 课件媒体资源配置json字符串 没有修改媒体资源时,为空
   */
  configJSON?: string
  /**
   * 资源类型  VIDEO_SINGLE &#x3D; 1  VIDEO_EXTENSION &#x3D; 2 没有修改媒体资源时,为空
   */
  resourceType?: number
}

/**
 * @author: xucenhao
@time: 2024-08-08
@description:
 */
export class GeneralResponse {
  code: string
  message: string
}

/**
 * @Author: XuCenHao
@CreateTime: 2024-4-2  10:15
@Description: gql响应
 */
export class GeneralMutationResponse {
  /**
   * 请求code
@see GeneralMutationEnum
   */
  code: number
  /**
   * 业务code
@see IBusinessCode
   */
  businessCode: number
  /**
   * 提示
   */
  tip: string
  data: string
}

/**
 * 课程包导入任务数据信息
@author: zhengp 2022/5/12 10:23
 */
export class CoursePackageImportTaskResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 处理时间
   */
  executingTime: string
  /**
   * 结束（完成）时间
   */
  completedTime: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
}

/**
 * 各状态及执行结果对应数量
 */
export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

/**
 * 课程包导入任务数据信息
@author: zhengp 2022/5/12 10:23
 */
export class CoursewareImportTaskResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 处理时间
   */
  executingTime: string
  /**
   * 结束（完成）时间
   */
  completedTime: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount1>
}

/**
 * 各状态及执行结果对应数量
 */
export class EachStateCount1 {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

/**
 * @author: zhengp 2022/5/11 14:59
 */
export class ExportCoursePackageImportResultResponse {
  /**
   * 导入课程包信息执行结果文件地址
   */
  fileUrl: string
}

/**
 * 课程校验信息
 */
export class CheckCourseResponse {
  /**
   * 状态码
3000 课程名称已存在
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

export class CoursePackageImportTaskResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursePackageImportTaskResponse>
}

export class CoursewareImportTaskResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareImportTaskResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出全部课程包导入结果数据
   * @param mainTaskId 主任务id
   * @return 课程包导入结果数据
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportAllCoursePackageImportResult(
    mainTaskId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportAllCoursePackageImportResult,
    operation?: string
  ): Promise<Response<ExportCoursePackageImportResultResponse>> {
    return commonRequestApi<ExportCoursePackageImportResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出错误课程包导入结果数据
   * @param mainTaskId 主任务id
   * @return 课程包导入结果数据
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportErrorCoursePackageImportResult(
    mainTaskId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportErrorCoursePackageImportResult,
    operation?: string
  ): Promise<Response<ExportCoursePackageImportResultResponse>> {
    return commonRequestApi<ExportCoursePackageImportResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询课程包导入任务数据
   * @param page    分页信息
   * @param request 查询条件
   * @return 课程包导入任务数据
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCoursePackageImportTask(
    params: { page?: Page; request?: PageCoursePackageImportTaskRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursePackageImportTask,
    operation?: string
  ): Promise<Response<CoursePackageImportTaskResponsePage>> {
    return commonRequestApi<CoursePackageImportTaskResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询课程包导入任务数据
   * @param page    分页信息
   * @param request 查询条件
   * @return 课程包导入任务数据
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCoursewareImportTask(
    params: { page?: Page; request?: PageCoursewareImportTaskRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursewareImportTask,
    operation?: string
  ): Promise<Response<CoursewareImportTaskResponsePage>> {
    return commonRequestApi<CoursewareImportTaskResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量导入创建课件
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchCreateCourseware(
    request: BatchImportCoursewareCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchCreateCourseware,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量导入课程包
   * @param filePath 课程包文件路径
   * @param mutate 查询 graphql 语法文档
   * @param filePath 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchImportCoursePackage(
    filePath: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchImportCoursePackage,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { filePath },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述 : 校验课程
   * @date : 2024/8/5 14:12
   * @param checkCourseRequest :
   * @return : void
   * @param mutate 查询 graphql 语法文档
   * @param checkCourseRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkCourse(
    checkCourseRequest: CheckCourseRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.checkCourse,
    operation?: string
  ): Promise<Response<CheckCourseResponse>> {
    return commonRequestApi<CheckCourseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { checkCourseRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 检查课程包的名称或显示名称是否重复
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkCoursePackageForName(
    request: CheckCoursePackageRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.checkCoursePackageForName,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验课件名称是否重复
   * @param request 校验信息
   * @return 校验结果
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkCourseware(
    request: CheckCoursewareRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.checkCourseware,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 非业务接口,生成配置类
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async configJSON(
    request: ConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.configJSON,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 复制课程包
   * @param id      复制的课程包
   * @param newName 课程包名称
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async copyCoursePackage(
    params: { id?: string; newName?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.copyCoursePackage,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建课程
   * @param createRequest 课程创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCourse(
    createRequest: CourseCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建课程分类
   * @param createRequest 课程分类创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCourseCategory(
    createRequest: CourseCategoryCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createCourseCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 课程包创建
   * @param createRequest 课程包创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCoursePackage(
    createRequest: CoursePackageCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createCoursePackage,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建课件
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCourseware(
    request: CoursewareCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createCourseware,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建课件分类
   * @param createRequest 课件分类创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCoursewareCategory(
    createRequest: CoursewareCategoryCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createCoursewareCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 停用课程
   * @param courseId 课程id
   * @param mutate 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableCourse(
    courseId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 停用课件
   * @param coursewareId 课件Id
   * @param mutate 查询 graphql 语法文档
   * @param coursewareId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableCourseware(
    coursewareId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableCourseware,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { coursewareId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用课程
   * @param courseId 课程id
   * @param mutate 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableCourse(
    courseId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用课件
   * @param coursewareId 课件Id
   * @param mutate 查询 graphql 语法文档
   * @param coursewareId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableCourseware(
    coursewareId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableCourseware,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { coursewareId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出课程包导入信息执行结果
   * @param param 查询信息
   * @return 导入课程包信息执行结果
   * @param mutate 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCoursewareImportResult(
    param: ExportCoursePackageParam,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.exportCoursewareImportResult,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { param },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除课程
   * @param courseId 课程id
   * @param mutate 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeCourse(
    courseId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除课程分类
   * @param courseCategoryId
   * @param mutate 查询 graphql 语法文档
   * @param courseCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeCourseCategory(
    courseCategoryId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeCourseCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { courseCategoryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 课程包移除
   * @param id 课程包id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeCoursePackage(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeCoursePackage,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除课件
   * @param coursewareId 课件Id
   * @param mutate 查询 graphql 语法文档
   * @param coursewareId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeCourseware(
    coursewareId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeCourseware,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { coursewareId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除课件分类
   * @param coursewareCategoryId 课件分类id
   * @param mutate 查询 graphql 语法文档
   * @param coursewareCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeCoursewareCategory(
    coursewareCategoryId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeCoursewareCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { coursewareCategoryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新课程
   * @param updateRequest 课程更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCourse(
    updateRequest: CourseUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新课程分类
   * @param updateRequest 课程分类更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCourseCategory(
    updateRequest: CourseCategoryUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateCourseCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 课程包更新
   * @param updateRequest 课程包更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCoursePackage(
    updateRequest: CoursePackageUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateCoursePackage,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改课件
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCourseware(
    request: CoursewareUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateCourseware,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新课件分类
   * @param updateRequest 课件分类更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCoursewareCategory(
    updateRequest: CoursewareCategoryUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateCoursewareCategory,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
