import DeleteExamRecordVo from '@api/service/management/train-class/mutation/vo/DeleteExamRecordVo'
import { Response } from '@hbfe/common'
import MsExamination from '@api/ms-gateway/ms-examination-v1'

/**
 * @description
 */
class MutationDeleteExamRecord {
  deleteExamRecordParams: DeleteExamRecordVo = new DeleteExamRecordVo()

  /**
   * 删除考试记录
   */
  async doDeleteExamRecord(): Promise<Response<void>> {
    const result = new Response<void>()
    const response = await MsExamination.invalidStudentExamAnswerPaper(this.deleteExamRecordParams)
    if (!response.status?.isSuccess()) {
      result.status = response.status
      return result
    }
    result.status = response.status
    result.data = response.data
    return result
  }
}

export default MutationDeleteExamRecord
