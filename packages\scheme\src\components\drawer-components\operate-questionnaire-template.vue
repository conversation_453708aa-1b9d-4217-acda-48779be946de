<template>
  <el-drawer title="选择调研问卷模板" :visible.sync="showDrawer" size="800px" custom-class="m-drawer" @open="onOpen">
    <div class="drawer-bd">
      <el-row :gutter="16" class="m-query f-mt10">
        <el-form :inline="true" label-width="auto">
          <el-col :span="10">
            <el-form-item label="问卷名称">
              <el-input v-model="questionnaireList.params.name" clearable placeholder="请输入问卷名称关键字" />
            </el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="问卷类型">
              <el-select v-model="questionnaireList.params.type" clearable filterable placeholder="请选择问卷类型">
                <el-option
                  v-for="item in questionnaireType.list()"
                  :key="item.code"
                  :value="item.code"
                  :label="item.desc"
                  >{{ item.desc }}</el-option
                >
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item>
              <el-button type="primary" :loading="loading" @click="page.currentChange(1)">查询</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table stripe :data="questionnaireList.list" v-loading="loading" max-height="500px" class="m-table f-mt10">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="问卷名称" min-width="300">
          <template v-slot="{ row }">{{ row.name }}</template>
        </el-table-column>
        <el-table-column label="问卷类型" min-width="120">
          <template v-slot="{ row }">{{ questionnaireType.map.get(row.type) }}</template>
        </el-table-column>
        <el-table-column label="查看" min-width="120" align="center">
          <template v-slot="{ row }"
            ><el-link type="primary" :underline="false" :loading="previewLoading" @click="preview(row)"
              >预览</el-link
            ></template
          >
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template v-slot="{ row }">
            <el-checkbox-group v-model="idList" @change="selectedChange($event, row)">
              <el-checkbox :label="row.id">选择</el-checkbox>
            </el-checkbox-group>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"> </hb-pagination>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="cancel">取消</el-button>
      <el-button type="primary" @click="selectTemplate">确定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Prop, PropSync, Emit } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
  import QuestionnaireList from '@api/service/management/resource/question-naire/QuestionnaireList'
  import QuestionnaireType from '@api/service/management/resource/question-naire/enums/QuestionnaireType'
  import QuestionnaireItem from '@api/service/management/resource/question-naire/models/QuestionnaireItem'
  import QuestionnaireQueryParam from '@api/service/management/resource/question-naire/models/QuestionnaireQueryParam'
  import { OperateTypeEnum } from '@hbfe/jxjy-admin-scheme/src/models/OperateTypeEnum'
  import QuestionnaireStatus, {
    QuestionnaireStatusEnum
  } from '@api/service/management/resource/question-naire/enums/QuestionnaireStatus'
  @Component({})
  export default class extends Vue {
    @PropSync('questionnaireTemplateDrawer', { type: Boolean, default: false }) showDrawer: boolean
    @Prop({ type: Object, default: () => new QuestionnaireConfigDetail() })
    questionnaire: QuestionnaireConfigDetail
    @Prop({ type: Number, default: OperateTypeEnum.CREATE }) operateType: OperateTypeEnum
    questionnaireType = new QuestionnaireType()
    /**
     * 问卷查询实例
     * @type {QuestionnaireList}
     */
    questionnaireList = new QuestionnaireList()
    /**
     * 分页
     */
    page: UiPage
    questionnaireStatus = new QuestionnaireStatus()
    /**
     * 加载loading
     */
    loading = false
    previewLoading = false
    /**
     * 选中的模板
     */
    selectedTemplate: QuestionnaireItem = null
    idList: string[] = []
    constructor() {
      super()
      this.page = new UiPage(this.queryList, this.queryList)
    }
    onOpen() {
      if (this.operateType === OperateTypeEnum.UPDATE) {
        this.selectedTemplate = new QuestionnaireItem()
        this.selectedTemplate.id = this.questionnaire.templateId
        this.selectedTemplate.name = this.questionnaire.templateName
        this.idList = [this.questionnaire.templateId]
        this.reset()
      } else {
        this.selectedTemplate = null
        this.idList = []
        this.reset()
      }
    }

    /**
     * 重置
     */
    reset() {
      this.questionnaireList.params = new QuestionnaireQueryParam()
      this.questionnaireList.params.status = QuestionnaireStatusEnum.publish
      this.page.currentChange(1)
    }

    /**
     * 选中改变事件
     */
    selectedChange(value: string[], item: QuestionnaireItem) {
      if (value?.length) {
        this.idList = [item.id]
        this.selectedTemplate = item
      } else {
        this.idList = []
        this.selectedTemplate = null
      }
    }

    /**
     * 查询列表
     */
    async queryList() {
      this.loading = true
      this.questionnaireList.queryList(this.page).finally(() => {
        this.loading = false
      })
    }

    /**
     * 选中
     */
    selectTemplate() {
      if (!this.selectedTemplate || !this.idList.length) return this.$message.error('请选择一个问卷模板')
      this.showDrawer = false
      this.$emit('select-template', this.selectedTemplate)
    }

    /**
     * 取消
     */
    cancel() {
      this.showDrawer = false
    }
    /**
     * 预览
     */
    async preview(item: QuestionnaireItem) {
      this.previewLoading = true
      const res = await this.questionnaireList.copy(item.id)
      if (res.questionList.length === 0) {
        this.$message.error('请至少配置一题试题才可预览。')
      } else {
        window.open(`/admin#/resource/questionnaire/preview?id=${item.id}`, '_blank')
      }
      this.previewLoading = false
    }
  }
</script>
