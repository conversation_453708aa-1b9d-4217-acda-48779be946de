import AbstractEnum from '../AbstractEnum'

enum DeadlineYearTypeEunm {
  CURRENT_YEAR = 1,
  NEXT_YEAR = 2
}

export { DeadlineYearTypeEunm }
class DeadlineYearType extends AbstractEnum<DeadlineYearTypeEunm> {
  static enum = DeadlineYearTypeEunm
  constructor() {
    super()
    this.map[DeadlineYearTypeEunm.CURRENT_YEAR] = '当年度'
    this.map[DeadlineYearTypeEunm.NEXT_YEAR] = '下一个年度'
  }
}

export default new DeadlineYearType()
