import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-studentcourse-appraisal-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-studentcourse-appraisal-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 评价课程信息
<AUTHOR>
@since 2022/5/16
 */
export class AppraisalCourseRequest {
  /**
   * 学员课程评价凭证，不能为空
   */
  courseAppraisalToken: string
  /**
   * 评价内容,不能为空
   */
  comment: string
  /**
   * 课程内容评价值，大于0的整型值
   */
  courseContentValue: number
  /**
   * 课程教师评价值，大于0的整型值
   */
  courseTeacherValue: number
}

/**
 * 课程评价结果
<AUTHOR>
@since 2022/5/16
 */
export class AppraisalCourseResponse {
  /**
   * 代码：
200-成功
50001 - 已评价过，无法评价
   */
  code: string
  /**
   * 信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 评价课程
   * @param request 请求对象
   * @return 评价结果
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async appraisalCourse(
    request: AppraisalCourseRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.appraisalCourse,
    operation?: string
  ): Promise<Response<AppraisalCourseResponse>> {
    return commonRequestApi<AppraisalCourseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除学员课程评价
   * @param studentCourseAppraisalId 课程评价编号
   * @param mutate 查询 graphql 语法文档
   * @param studentCourseAppraisalId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteAppraisalCourse(
    studentCourseAppraisalId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteAppraisalCourse,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentCourseAppraisalId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
