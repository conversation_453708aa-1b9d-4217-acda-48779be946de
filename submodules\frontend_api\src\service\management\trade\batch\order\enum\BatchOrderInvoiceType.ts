import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 【集体报名订单】发票类型枚举
 */
export enum BatchOrderInvoiceTypeEnum {
  /**
   * 增值税电子普通发票（自动开票）
   */
  VAT_ELECTRONIC_INVOICE = 1,

  /**
   * 增值税电子普通发票（线下开票）
   */
  VAT_OFFLINE_INVOICE = 2,

  /**
   * 增值税专用发票
   */
  VAT_SPECIAL_INVOICE = 3
}

/**
 * @description 【集体报名订单】发票类型
 */
class BatchOrderInvoiceType extends AbstractEnum<BatchOrderInvoiceTypeEnum> {
  static enum = BatchOrderInvoiceTypeEnum

  constructor(status?: BatchOrderInvoiceTypeEnum) {
    super()
    this.current = status
    this.map.set(BatchOrderInvoiceTypeEnum.VAT_ELECTRONIC_INVOICE, '增值税电子普通发票（自动开票）')
    this.map.set(BatchOrderInvoiceTypeEnum.VAT_OFFLINE_INVOICE, '增值税电子普通发票（线下开票）')
    this.map.set(BatchOrderInvoiceTypeEnum.VAT_SPECIAL_INVOICE, '增值税专用发票')
  }
}

export default new BatchOrderInvoiceType()
