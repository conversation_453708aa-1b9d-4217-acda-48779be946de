import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 【集体报名订单】交易状态枚举/批次单状态
 */
export enum BatchOrderTradeStatusEnum {
  // 待下单
  Wait_Place_Order = 1,
  // 下单中
  Placing_Order,
  // 待付款
  Wait_Pay,
  // 支付中
  Paying,
  // 开通中
  Opening,
  // 交易成功
  Pay_Success,
  // 交易关闭中
  Closing_Pay,
  // 交易关闭
  Close_Pay
}
/**
 * @description 【集体报名退款订单】退款状态枚举/批次单状态
 */
export enum BatchRefundTradeStatusEnum {
  /**
   * 退款审批中
   */
  REFUNDING = 1,
  /**
   * 退款处理中
   */
  REFUNDDISPOSE,
  /**
   * 退款成功
   */
  REFUNDSUCCESS,
  /**
   * 退款失败
   */
  REFUNDFAIL,
  /**
   * 拒绝退款
   */
  REFUSEDREFUND,
  /**
   * 取消退款
   */
  CANCELREFUND,

  /**
   * 资源回收失败
   */
  RECYLING_FAILED,

  /**
   * 资源回收成功后，退款处理中
   */
  REFUND_PROCESSING
}
/*
 * @description 【集体报名退款订单】退款类型枚举/批次单状态
 */
export enum BatchRefundTradeTypeEnum {
  /**
   * 1: 仅退货
   * 2: 仅退款
   * 3: 退货退款
   */
  RETURN = 1,
  REFUND = 2,
  RETURN_REFUND = 3
}
/*
 * @description 【集体报名退款订单】退款方式枚举/批次单状态
 */
export enum BatchRefundTradeWayEnum {
  /**
   * 线上退款
   */
  ONLINEREFUND = 1,
  /**
   * 线下退款
   */
  OFFLINEREFUND
}

/**
 * @description 【集体报名订单】交易状态
 */
class BatchOrderTradeStatus extends AbstractEnum<BatchOrderTradeStatusEnum> {
  static enum = BatchOrderTradeStatusEnum

  constructor(status?: BatchOrderTradeStatusEnum) {
    super()
    this.current = status
    this.map.set(BatchOrderTradeStatusEnum.Wait_Place_Order, '待下单')
    this.map.set(BatchOrderTradeStatusEnum.Placing_Order, '下单中')
    this.map.set(BatchOrderTradeStatusEnum.Wait_Pay, '待付款')
    this.map.set(BatchOrderTradeStatusEnum.Paying, '支付中')
    this.map.set(BatchOrderTradeStatusEnum.Opening, '开通中')
    this.map.set(BatchOrderTradeStatusEnum.Pay_Success, '交易成功')
    this.map.set(BatchOrderTradeStatusEnum.Closing_Pay, '交易关闭中')
    this.map.set(BatchOrderTradeStatusEnum.Close_Pay, '交易关闭')
  }
}

export default new BatchOrderTradeStatus()
