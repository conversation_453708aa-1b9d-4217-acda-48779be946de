import Authentication from '@api/service/common/authentication/Authentication'
import { UtilClass } from '@/common/util'
import { Numeral } from 'numeral'
import { domToPic } from '@hbfe/jxjy-admin-common/src/DomToPic'
import showIntelligentLearningTaskFeedback from '@hbfe/jxjy-admin-components/src/intelligent-learning-task-feedback/register'

// 1. 确保在声 明补充的类型之前导入 'vue'
// 2. 定制一个文件，设置你想要补充的类型
//    在 types/vue.d.ts 里 Vue 有构造函数类型
declare module 'vue/types/vue' {
  // 3. 声明为 Vue 补充的东西
  interface Vue {
    $authentication: Authentication
    $moment: any
    $numeral: typeof Numeral

    $hasPermission(key: string, isGlobal?: boolean): boolean

    $util: UtilClass
    $domToPic: typeof domToPic
    $showIntelligentLearningTaskFeedback: typeof showIntelligentLearningTaskFeedback
  }
}
