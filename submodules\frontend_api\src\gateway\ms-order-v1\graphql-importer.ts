import applyCancelOrder from './mutates/applyCancelOrder.graphql'
import applyInvoice from './mutates/applyInvoice.graphql'
import batchApplyInvoice from './mutates/batchApplyInvoice.graphql'
import batchOrderCancel from './mutates/batchOrderCancel.graphql'
import commitBatchOrder from './mutates/commitBatchOrder.graphql'
import createOrder from './mutates/createOrder.graphql'
import offlineApplyPayOrder from './mutates/offlineApplyPayOrder.graphql'
import offlinePayBatchOrder from './mutates/offlinePayBatchOrder.graphql'
import onlinePayBatchOrder from './mutates/onlinePayBatchOrder.graphql'
import onlinePayOrder from './mutates/onlinePayOrder.graphql'
import sellerCancelOrder from './mutates/sellerCancelOrder.graphql'

export {
  applyCancelOrder,
  applyInvoice,
  batchApplyInvoice,
  batchOrderCancel,
  commitBatchOrder,
  createOrder,
  offlineApplyPayOrder,
  offlinePayBatchOrder,
  onlinePayBatchOrder,
  onlinePayOrder,
  sellerCancelOrder
}
