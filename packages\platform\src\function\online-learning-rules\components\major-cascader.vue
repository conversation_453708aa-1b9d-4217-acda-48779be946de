<route-meta>
{
"title": "培训类别选择器"
}
</route-meta>
<template>
  <el-select v-model="selectValue" :placeholder="placeholder" class="form-l" filterable clearable multiple>
    <el-option
      v-for="item in trainingCategoryOptions"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
      :disabled="isDisabled && item.name != '全部'"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Mixins, Prop, Watch, Emit } from 'vue-property-decorator'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import QueryTrainingCategory from '@api/service/common/basic-data-dictionary/query/QueryTrainingCategory'
  import CommonSkuMixins from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSkuMixins'

  @Component
  export default class extends Mixins(CommonSkuMixins) {
    // 培训类别选项
    trainingCategoryOptions: Array<TrainingCategoryVo> = new Array<TrainingCategoryVo>()

    @Watch('industryPropertyId', {
      immediate: true,
      deep: true
    })
    async industryPropertyIdChange() {
      await this.getTrainingCategoryOptions()
    }

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: TrainingCategoryVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    /**
     * 获取培训类别
     */
    async getTrainingCategoryOptions() {
      const res = await QueryTrainingCategory.queryTrainingCategory(this.industryPropertyId, this.industryId)
      this.trainingCategoryOptions = res.isSuccess()
        ? QueryTrainingCategory.trainingCategoryList
        : ([] as TrainingCategoryVo[])
      const param = new TrainingCategoryVo()
      param.propertyId = '-1'
      param.name = '全部'
      param.sort = 0
      param.showName = ''
      param.parentId = ''
      this.trainingCategoryOptions.unshift(param)
    }
  }
</script>
