<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 10:09:33
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-04-15 08:56:39
 * @Description: 设置基础配置抽屉
-->
<template>
  <div>
    <el-drawer
      title="设置基础配置"
      :visible.sync="openBasicDrawer"
      direction="rtl"
      size="800px"
      custom-class="m-drawer"
      @close="close"
    >
      <div class="drawer-bd">
        <div class="m-tit">
          <span class="tit-txt">监管功能基础配置</span>
        </div>
        <el-form ref="form" label-width="220px" class="m-form">
          <el-form-item label="监管功能是否开启：">
            <el-switch v-model="baseConfigModule.baseConfig.antiEnable"></el-switch>
          </el-form-item>
        </el-form>
        <div class="m-tit">
          <span class="tit-txt">人脸识别基础配置</span>
        </div>
        <el-form
          ref="baseConfigFormRef"
          :rules="baseConfigRules"
          :model="baseConfigModule.baseConfig.datumConfig"
          label-width="220px"
          class="m-form"
        >
          <el-form-item label="应用状态是否开启：">
            <el-switch v-model="baseConfigModule.baseConfig.datumConfig.enable"></el-switch>
          </el-form-item>
          <!-- <el-form-item label="监管范围：" required>
            <el-radio v-model="radio" label="1">平台级别</el-radio>
          </el-form-item> -->
          <el-form-item label="基准照片数：">{{ collectCount }}</el-form-item>
          <el-form-item label="基准照片更新次数：" prop="updateCount">
            <el-input
              v-model="baseConfigModule.baseConfig.datumConfig.updateCount"
              :disabled="!baseConfigModule.baseConfig.datumConfig.enable"
              class="f-input-num f-mr5"
            ></el-input>
            次
          </el-form-item>
          <el-form-item label="监管协议：" prop="protocolText">
            <!-- <el-input
              type="textarea"
              :rows="10"
              placeholder="请输入内容"
              :disabled="!baseConfigModule.baseConfig.datumConfig.enable"
              v-model="baseConfigModule.baseConfig.datumConfig.protocolText"
            >
            </el-input> -->
            <div class="rich-text">
              <hb-tinymce-editor
                :disabled="!baseConfigModule.baseConfig.datumConfig.enable"
                v-model="baseConfigModule.baseConfig.datumConfig.protocolText"
              ></hb-tinymce-editor>
            </div>
          </el-form-item>
          <el-form-item label="匹配相似度：" prop="comparePhoto">
            <el-input
              v-model="baseConfigModule.baseConfig.datumConfig.comparePhoto"
              :disabled="!baseConfigModule.baseConfig.datumConfig.enable"
              class="f-input-num f-mr5"
            ></el-input>
            %<span class="f-ci f-fl5">（注：匹配系数为1%-100%，系数越大相似度就越高。）</span>
          </el-form-item>
          <el-form-item label="比对过程是否开启活体检测：">
            <el-switch
              v-model="baseConfigModule.baseConfig.datumConfig.liveDetection"
              :disabled="!baseConfigModule.baseConfig.datumConfig.enable"
            ></el-switch>
            <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
              <i class="el-icon-question m-tooltip-icon f-co f-ml10"></i>
              <div slot="content">
                <p>说明：</p>
                <p>1、默认全平台所有采集环节开启活体检测</p>
                <p>
                  2、全平台比对过程的活体检测可统一设置为开启或关闭，比对环节包含登录、学习、考试，是否开启活体检测仅对设置为开启监管规则的环节生效。
                </p>
                <p>3、适用于具体配置的培训方案，若系统也有配置检测规则，则以培训方案的配置为准</p>
              </div>
            </el-tooltip>
          </el-form-item>
        </el-form>
        <div class="m-tit">
          <span class="tit-txt">登录监管规则配置</span>
        </div>
        <el-form
          ref="form"
          :model="baseConfigModule.baseConfig.loginConfig"
          label-width="220px"
          class="m-form"
          disabled
        >
          <el-form-item label="是否开启：" required>
            <el-switch v-model="baseConfigModule.baseConfig.loginConfig.enable"></el-switch>
          </el-form-item>
          <el-form-item label="登录人脸比对：" required>
            <el-radio v-model="baseConfigModule.baseConfig.loginConfig.antiConfigId" label="1" border class="f-mr10"
              >每次登录拍摄对比
            </el-radio>
            <el-radio v-model="baseConfigModule.baseConfig.loginConfig.antiConfigId" label="1" border class="f-mr10"
              >首次登录比对
            </el-radio>
            <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
              <i class="el-icon-question m-tooltip-icon f-co"></i>
              <div slot="content">
                <p>说明：人脸比对若开启则必须有采集基准照片</p>
                <p>
                  1.每次登录需人脸比对，则学员每次登录需要进行人脸拍照比对，比对通过才可登录成功，不通过则无法登录
                </p>
                <p>2.首次登录人脸比对，则学员只需要在首次登录进行人脸拍照比对，后面登录可不比对，直接登录。</p>
              </div>
            </el-tooltip>
          </el-form-item>
        </el-form>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="openBasicDrawer = false">取消</el-button>
        <el-button @click="save" :loading="isLoading" type="primary">保存</el-button>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Vue, Ref } from 'vue-property-decorator'
  import AntiCheatLoginConfigModel from '@hbfe-biz/biz-anticheat/dist/config/base-models/AntiCheatLoginConfigModel'
  import BaseConfig from '@hbfe-biz/biz-anticheat/dist/config/BaseConfig'
  import Context from '@api/service/common/context/Context'

  @Component({})
  export default class extends Vue {
    @Ref('baseConfigFormRef') baseConfigFormRef: any
    // 是否打开抽屉
    @PropSync('setBasicData', {
      type: Boolean,
      default: false
    })
    openBasicDrawer: boolean

    // 基础配置
    @Prop({
      type: Object,
      default: () => new BaseConfig()
    })
    baseConfigModule: BaseConfig

    // 登录配置
    loginConfig = new AntiCheatLoginConfigModel()

    // 采集照片数
    collectCount = 1

    isLoading = false

    updateCountValidate = (rule: any, value: any, callback: (arg?: Error) => void) => {
      const exp = /(^[1-9]\d*$)/
      if (!value) {
        return callback(new Error('请输入基准照片更新次数'))
      } else if (!exp.test(value)) {
        return callback(new Error('请输入正确的数值'))
      } else {
        return callback()
      }
    }
    comparePhotoValidate = (rule: any, value: any, callback: (arg?: Error) => void) => {
      const exp = /(^[1-9]\d*$)/
      const newValue = Number(value)
      if (!newValue || newValue < 0 || newValue > 100) {
        return callback(new Error('请输入正确的数值'))
      } else {
        return callback()
      }
    }
    baseConfigRules = {
      updateCount: [{ validator: this.updateCountValidate, trigger: 'blur' }],
      protocolText: [{ required: true, message: '请输入监管协议', trigger: 'blur' }],
      comparePhoto: [{ validator: this.comparePhotoValidate, trigger: 'blur' }]
    }

    // 保存基础配置
    async save() {
      this.baseConfigFormRef.validate(async (boolean: boolean, value: object) => {
        if (boolean) {
          this.isLoading = true
          // 采集数量暂时默认一张
          this.baseConfigModule.baseConfig.datumConfig.collectCount = this.collectCount
          try {
            // 判断是否第一次设置基础配置，第一次走创建口，否则走更新口
            if (!this.baseConfigModule.baseConfig.antiModuleConfigId) {
              await this.baseConfigModule.createSchoolAntiConfig(
                Context.businessEnvironment.serviceToken.tokenMeta.servicerId
              )
            } else {
              await this.baseConfigModule.updateSchoolAntiConfig()
            }
            if (!this.baseConfigModule.baseConfig.datumConfig.datumConfigId) {
              await this.baseConfigModule.createConfig()
            } else {
              await this.baseConfigModule.updateConfig()
            }
            this.close()
            this.$message.success('保存成功')
          } catch (error) {
            this.$message.error('保存失败')
            console.log(error)
          } finally {
            this.isLoading = false
            this.$emit('updateSuccess')
          }
        }
      })
    }

    // 关闭抽屉
    close() {
      this.openBasicDrawer = false
      this.isLoading = false
    }
  }
</script>
