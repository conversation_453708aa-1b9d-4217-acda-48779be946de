"""独立部署的微服务,K8S服务名:ms-course-play-resource-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""申请课程试听
		@param courseId 课程编号
		@return 课程试听凭证
	"""
	applyCourseAudition(courseId:String!):CourseAuditionTokenResponse @optionalLogin
	"""申请课程试听，不验证是否允许试听
		@param courseId 课程编号
		@return 课程试听凭证
	"""
	applyCourseAuditionWithoutValidate(courseId:String!):CourseAuditionTokenResponse @optionalLogin
	"""申请学习课程播放资源
		@param coursePlayToken 课程学习播放凭证/课程试听凭证/课程预览凭证
		@return 资源信息
	"""
	applyCoursePlayResource(coursePlayToken:String!):CoursePlayResourceResponse @optionalLogin
	"""申请课程预览
		@param courseId 课程编号
		@return 课程预览凭证
	"""
	applyCoursePreview(courseId:String!):CoursePreviewTokenResponse @optionalLogin
	"""申请学习课件播放防盗链
		@param request 课件媒体播放盗源链申请参数
		@return 课件媒体播放防盗链响应
	"""
	applyCoursewareMediaPlayAntiTheftChainResource(request:CoursewareMediaApplyResourceRequest!):CoursewareMediaPlayResourceResponse @optionalLogin
	"""申请学习课件播放资源
		@param request 参数
		@return 媒体资源信息
	"""
	applyCoursewareMediaPlayResource(request:CoursewareMediaApplyResourceRequest!):CoursewareMediaPlayResourceResponse @optionalLogin
	"""申请课件预览防盗链播放资源
		@param coursewareId 课件id
		@return 媒体资源信息
	"""
	applyCoursewareMediaPreviewAntiTheftChainResource(coursewareId:String!):CoursewareMediaPreviewResourceResponse @optionalLogin
	"""申请课件预览
		@param coursewareId 课件id
		@return 媒体资源信息
	"""
	applyCoursewareMediaPreviewResource(coursewareId:String!):CoursewareMediaPreviewResourceResponse @optionalLogin
	"""通过媒体资源ID申请学习课件防盗链播放资源
		@param mediaIdResourceId 课件媒体资源Id
		@return 课件媒体播放防盗链响应
	"""
	applyMediaPlayAntiTheftChainResource(mediaIdResourceId:String!):CoursewareMediaPlayResourceResponse @optionalLogin
}
"""课件媒体资源申请参数"""
input CoursewareMediaApplyResourceRequest @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.request.CoursewareMediaApplyResourceRequest") {
	"""课程学习播放凭证/课程试听凭证/课程预览凭证"""
	coursePlayToken:String
	"""章节编号"""
	chapterId:String
	"""课程编号"""
	coursewareId:String
	"""媒体资源编号"""
	mediaResourceId:String
}
"""课程试听凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type CourseAuditionTokenResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.CourseAuditionTokenResponse") {
	"""申请结果
		200 - 成功
		30000 - 课程不存在
		30001 - 不允许试听
		500 - 内部异常
	"""
	applyResult:TokenResponse
	"""课程试听凭证"""
	token:String
}
type CourseChapterPlayResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.CourseChapterPlayResourceResponse") {
	"""课程章节id"""
	id:String
	"""章节名称"""
	name:String
	"""课程目录id"""
	courseOutlineId:String
	"""课件id"""
	coursewareId:String
	"""课程id"""
	courseId:String
	"""排序"""
	sort:Int
	"""是否允许试听"""
	allowAudition:Boolean!
}
type CourseOutlinePlayResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.CourseOutlinePlayResourceResponse") {
	"""课程目录id"""
	id:String
	"""课程id"""
	cseId:String
	"""课程目录名称"""
	name:String
	"""父级课程目录id"""
	parentId:String
	"""排序"""
	sort:Int
}
"""课程资源信息"""
type CoursePlayResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.CoursePlayResourceResponse") {
	"""课程id"""
	id:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""课程简介内容id"""
	aboutsContentId:String
	"""章节播放资源"""
	courseChapterPlayResourceList:[CourseChapterPlayResourceResponse]
	"""课件资源"""
	coursewarePlayResourceList:[CoursewarePlayResourceResponse]
	"""课程目录播放资源"""
	courseOutlinePlayResourceList:[CourseOutlinePlayResourceResponse]
}
"""课程预览凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type CoursePreviewTokenResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.CoursePreviewTokenResponse") {
	"""申请结果
		200 - 成功
		30000 - 课程不存在
		30001 - 不允许预览
		500 - 内部异常
	"""
	applyResult:TokenResponse
	"""课程预览凭证"""
	token:String
}
type CoursewareMediaPlayConfigResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.CoursewareMediaPlayConfigResponse") {
	"""配置id"""
	configId:String
	"""是否启用防录屏跑马灯
		1-启用  0-不启用
	"""
	enableEd:Int!
	"""是否启用视频贴片
		1-启用 0-不启用
	"""
	enableVideoPatch:Int!
	"""是否启用前置广告
		1-启用 0-不启用
	"""
	enablePreMedia:Int!
	"""是否启用后置广告
		1-启用 0-不启用
	"""
	enableRearMedia:Int!
	"""前置广告媒体地址"""
	preMediaPath:String
	"""后置广告媒体地址"""
	rearMediaPath:String
}
"""课件媒体播放资源"""
type CoursewareMediaPlayResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.CoursewareMediaPlayResourceResponse") {
	"""课件媒体资源ID"""
	id:String
	"""课件媒体资源类型"""
	type:Int!
	"""媒体时长"""
	timeLength:Int!
	"""课件目录集合"""
	outlineList:[CoursewareOutlinePlayResourceResponse]
	"""文档播放资源"""
	documentList:[DocumentPlayResourceResponse]
	"""视频信息"""
	videoPlayResourceList:[VideoPlayResourceResponse]
	"""视频字幕"""
	videoCaptionList:[VideoCaptionPlayResourceResponse]
	"""视频转码信息列表"""
	videoTranscodingList:[VideoResourceResponse]
	"""视频讲义列表"""
	lectureList:[LecturePlayResourceResponse]
	"""视频章节列表"""
	videoChapterList:[VideoChapterPlayResourceResponse]
	"""播放配置"""
	playConfig:CoursewareMediaPlayConfigResponse
	"""播放规则"""
	playRule:CoursewareMediaPlayRuleResponse
}
type CoursewareMediaPlayRuleResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.CoursewareMediaPlayRuleResponse") {
	"""规则id"""
	ruleId:String
	"""试听时长"""
	listenTime:Long!
}
"""课件媒体预览资源"""
type CoursewareMediaPreviewResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.CoursewareMediaPreviewResourceResponse") {
	"""课件媒体资源ID"""
	id:String
	"""课件媒体资源类型"""
	type:Int!
	"""媒体时长"""
	timeLength:Int!
	"""文档播放资源"""
	documentList:[DocumentPlayResourceResponse]
	"""视频信息"""
	videoPlayResourceList:[VideoPlayResourceResponse]
	"""视频字幕"""
	videoCaptionList:[VideoCaptionPlayResourceResponse]
	"""视频转码信息列表"""
	videoTranscodingList:[VideoResourceResponse]
	"""视频讲义列表"""
	lectureList:[LecturePlayResourceResponse]
}
type CoursewareOutlinePlayResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.CoursewareOutlinePlayResourceResponse") {
	"""课件媒体资源目录ID"""
	id:String
	"""课件媒体资源ID"""
	cwmrId:String
	"""课件媒体资源目录名称"""
	name:String
	"""排序"""
	sort:Int!
	"""上级目录ID，0代表顶级"""
	parentId:String
	"""所属模块类型，2代表视频，1代表文档"""
	moduleType:Int!
}
type CoursewarePlayResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.CoursewarePlayResourceResponse") {
	"""课件id"""
	id:String
	"""课件名称"""
	name:String
	"""媒体时长"""
	timeLength:Int
	"""媒体资源id"""
	mediaResourceId:String
	"""课件简介内容id"""
	aboutsContentId:String
	"""课件类型（文档 = 1 视频 = 2 媒体 = 3）"""
	type:Int!
}
type DocumentPlayResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.DocumentPlayResourceResponse") {
	"""文档id"""
	id:String
	"""课件媒体资源ID"""
	cwmrId:String
	"""媒体资源文档类型"""
	type:Int!
	"""媒体资源文档路径"""
	path:String
	"""排序"""
	sort:Int!
	"""媒体资源文档大小，单位kb"""
	size:Int!
}
type LecturePlayResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.LecturePlayResourceResponse") {
	"""讲义ID"""
	id:String
	"""课件媒体资源ID"""
	cwmrId:String
	"""讲义类型，1代表html，2代表image"""
	type:Int!
	"""播放时间点，单位秒"""
	timePoint:Int!
	"""讲义路径"""
	path:String
	"""所属模块类型"""
	moduleType:Int!
	"""所属模块主键"""
	moduleKey:String
}
"""凭证响应基类
	<AUTHOR>
	@since 2022/1/20
"""
type TokenResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.TokenResponse") {
	"""代码：
		200-成功
	"""
	code:String
	"""信息"""
	message:String
}
type VideoCaptionPlayResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.VideoCaptionPlayResourceResponse") {
	"""字幕ID"""
	id:String
	"""课件媒体资源ID"""
	cwmrId:String
	"""视频ID"""
	vdoId:String
	"""字幕文件路径"""
	path:String
	"""字幕类型，1表示SRT, 2表示Webvtt"""
	captionType:Int!
}
type VideoChapterPlayResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.VideoChapterPlayResourceResponse") {
	"""章节ID"""
	id:String
	"""课件媒体资源ID"""
	cwmrId:String
	"""章节名称"""
	name:String
	"""章节打开方式，1代表手动，2代表自动"""
	openMode:Int!
	"""上级章节ID，0代表顶级"""
	parentId:String
	"""排序"""
	sort:Int!
	"""自定义拓展信息"""
	expand:String
	"""所属模块类型,1代表视频模块"""
	moduleType:Int!
	"""所属模块主键"""
	moduleKey:String
	"""所属模块主键下的数据"""
	moduleData:String
}
type VideoPlayResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.VideoPlayResourceResponse") {
	"""视频ID"""
	id:String
	"""课件媒体资源ID"""
	cwmrId:String
	"""视频时长，单位秒"""
	timeLength:Int!
}
"""视频资源信息"""
type VideoResourceResponse @type(value:"com.fjhb.ms.course.play.resource.v1.kernel.gateway.graphql.response.VideoResourceResponse") {
	"""视频转码编号"""
	id:String
	"""视频ID"""
	vdoId:String
	"""课件媒体资源ID"""
	cwmrId:String
	"""视频播放路劲，相对路径"""
	path:String
	"""视频清晰度
		1:流畅普屏 2:标清普屏 3:高清普屏 4:流畅宽屏 5:标清宽屏 6:高清宽屏
		负数是代表手机端对应的清晰度
	"""
	clarity:Int!
	"""视频大小，单位kb"""
	size:Int!
	"""资源类型，0表示八百里，1表示保利威视，2表示华为云，20表示华为云音频 4表示外链地址"""
	resourceType:Int!
}

scalar List
