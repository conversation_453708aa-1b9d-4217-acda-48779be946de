import store from '@api/store'
import { VuexModule, getModule, Module } from 'vuex-module-decorators'
import MutationUserFactory from '@api/service/customer/user/MutationUserFactory'
import QueryUserFactory from '@api/service/customer/user/QueryUserFactory'

@Module({ namespaced: true, name: 'CustomerUserModule', dynamic: true, store })
class UserModule extends VuexModule {
  /*
    查询用户工厂
  */
  queryUserFactory: QueryUserFactory = new QueryUserFactory()

  /*
    用户业务工厂
  */
  mutationUserFactory: MutationUserFactory = new MutationUserFactory()
}

export default getModule(UserModule)
