import { Page, UiPage } from '@hbfe/common'
import ExcellentCourseConfigListDetail from '@api/service/management/online-school-config/excellent-course/query/vo/ExcellentCourseConfigListDetail'
import Mockjs from 'mockjs'

class QueryExcellentCourseConfig {
  page: Page = new UiPage()
  private cacheList: Array<ExcellentCourseConfigListDetail> = new Array<ExcellentCourseConfigListDetail>()

  get list() {
    return this.cacheList
  }

  async queryPage() {
    this.cacheList = Mockjs.mock({
      'data|10': [
        {
          id: '@uuid',
          name: '@csentence(10)',
          sort: '@integer(10)',
          createTime: '@date("yyyy-MM-dd HH:mm:ss")',
          lastModifyTime: '@date("yyyy-MM-dd HH:mm:ss")'
        }
      ]
    }).data.map(ExcellentCourseConfigListDetail.from)
  }
}

export default QueryExcellentCourseConfig
