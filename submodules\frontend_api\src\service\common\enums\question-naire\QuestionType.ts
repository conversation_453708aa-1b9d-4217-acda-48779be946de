import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum QuestionTypeEnum {
  /**
   * 单选
   */
  single = 1,
  /**
   * 多选
   */
  multiple = 2,
  /**
   * 问答
   */
  answer = 3,
  /**
   * 量表题
   */
  gauge = 4
}
class QuestionType extends AbstractEnum<QuestionTypeEnum> {
  static enum = QuestionTypeEnum
  constructor(status?: QuestionTypeEnum) {
    super()
    this.current = status
    this.map.set(QuestionTypeEnum.single, '单选题')
    this.map.set(QuestionTypeEnum.multiple, '多选题')
    this.map.set(QuestionTypeEnum.answer, '问答题')
    this.map.set(QuestionTypeEnum.gauge, '量表题')
  }
}
export default QuestionType
