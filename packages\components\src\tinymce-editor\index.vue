<template>
  <div class="tinymce-editor">
    <editor v-if="activated" :id="id" v-model="myValue" :init="init" :disabled="disabled" @onClick="onClick"></editor>
  </div>
</template>

<script>
  import tinymce from 'tinymce/tinymce'
  import Editor from '@tinymce/tinymce-vue'
  import 'tinymce/themes/silver/theme'
  import 'tinymce/plugins/image'
  import 'tinymce/plugins/media'
  import 'tinymce/plugins/table'
  import 'tinymce/plugins/lists'
  import 'tinymce/plugins/contextmenu'
  import 'tinymce/plugins/wordcount'
  import 'tinymce/plugins/colorpicker'
  import 'tinymce/plugins/textcolor'
  import 'tinymce/plugins/link'
  import 'tinymce/plugins/fullscreen'
  import 'tinymce/plugins/hr'
  import 'tinymce/plugins/preview'
  import 'tinymce/plugins/quickbars'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

  export default {
    components: {
      Editor
    },
    props: {
      // 传入一个value，使组件支持v-model绑定
      value: {
        type: String,
        default: ''
      },
      disabled: {
        type: Boolean,
        default: false
      },
      plugins: {
        type: [String, Array],
        default: 'lists image media table textcolor wordcount contextmenu link fullscreen hr preview quickbars'
      },
      toolbar: {
        type: [String, Array],
        default:
          'undo redo formatpainter |  formatselect fontselect fontsizeselect | forecolor backcolor bold italic underline | alignleft aligncenter alignright alignjustify | bullist numlist outdent indent quickbars link hr | lists image table | blockquote subscript superscript removeformat | bdmap | preview fullscreen'
      }
    },
    data() {
      const prefixPath = process.env.NODE_ENV === 'development' ? '' : '/admin'
      return {
        // 初始化配置
        init: {
          base_url: `${prefixPath}/libs/tinymce`,
          language_url: `${prefixPath}/libs/tinymce/langs/zh_CN.js`,
          language: 'zh_CN',
          skin_url: `${prefixPath}/libs/tinymce/skins/ui/oxide`,
          height: 300,
          // toolbar_sticky: true,
          plugins: this.plugins,
          external_plugins: {
            powerpaste: `${prefixPath}/libs/tinymce/plugins/powerpaste/plugin.min.js`,
            bdmap: `${prefixPath}/libs/tinymce/plugins/bdmap/plugin.min.js`
          },
          fontsize_formats: '12px 14px 16px 18px 24px 36px 48px 56px 72px',
          toolbar_mode: 'sliding',
          font_formats:
            '微软雅黑=Microsoft YaHei,Helvetica Neue,PingFang SC,sans-serif;苹果苹方=PingFang SC,Microsoft YaHei,sans-serif;宋体=simsun,serif;仿宋体=FangSong,serif;黑体=SimHei,sans-serif;Arial=arial,helvetica,sans-serif;Arial Black=arial black,avant garde;Book Antiqua=book antiqua,palatino;',
          toolbar: this.toolbar,
          branding: false,
          menubar: false,
          // 此处为图片上传处理函数，这个直接用了base64的图片形式上传图片，
          // 如需ajax上传可参考https://www.tiny.cloud/docs/configure/file-image-upload/#images_upload_handler
          images_upload_handler: async (blobInfo, success) => {
            const uploadFileUrl = `${ConfigCenterModule.getIngress(ingress.resource)}/auth/uploadBase64ToProtectedFile`
            const { data } = await this.$http.post(uploadFileUrl, {
              base64Data: blobInfo.base64()
            })
            success(`/mfs/${data.url}`)
          },
          file_picker_types: 'file image media',
          file_picker_callback: (callback, value, meta) => {
            console.log(value, meta)
            const input = document.createElement('input')
            const { $message, $http } = this
            input.setAttribute('type', 'file')
            input.setAttribute('accept', this.linkAccept)
            input.onchange = function () {
              const file = this.files[0]
              const reader = new FileReader()
              const param = new FormData()
              param.append('file', file)
              if (file.size > this.linkLimit) {
                return $message.warning('文件大小不能超过 10m')
              }
              reader.onload = async () => {
                const result = await $http.post(
                  `${ConfigCenterModule.getIngress(ingress.resource)}/auth/uploadPublicFile`,
                  param
                )
                const fileInfo = result.data[0]
                callback(`/mfs${fileInfo.url}`, { title: file.name })
              }
              reader.readAsDataURL(file)
            }
            input.click()
          }
        },
        editorInstance: null, // 新增：存储编辑器实例
        myValue: this.value,
        id: this.genId(),
        activated: true
      }
    },
    activated() {
      this.activated = false
      setTimeout(() => {
        this.$nextTick(() => {
          this.activated = true
        })
      }, 100)
    },
    destroyed() {
      // 组件销毁时清理编辑器
      if (this.editorInstance) {
        tinymce.remove(this.editorInstance)
        this.editorInstance = null
      }
    },
    async mounted() {
      this.$nextTick(async () => {
        await tinymce.init({
          base_uri: {
            path: '12313'
          },
          setup: (editor) => {
            this.editorInstance = editor // 存储实例引用
          }
        })
      })
    },
    methods: {
      genId() {
        return 'tiny_mce_editor_' + Math.random().toString(36).substring(2, 11)
      },
      // 添加相关的事件，可用的事件参照文档=> https://github.com/tinymce/tinymce-vue => All available events
      // 需要什么事件可以自己增加
      onClick(e) {
        this.$emit('onClick', e, tinymce)
      },
      // 可以添加一些自己的自定义事件，如清空内容
      clear() {
        this.myValue = ''
      }
    },
    watch: {
      value(newValue) {
        this.myValue = newValue
      },
      myValue(newValue) {
        console.log('dsadasdsa', newValue)
        if (newValue != null) {
          this.$emit('input', newValue.replace(/\/?mfs\/+/g, '/mfs/'))
        }
      }
    }
  }
</script>
