import MsTradeQuery, {
  OrderBasicDataRequest,
  OrderInfoRequest,
  OrderSortField,
  OrderSortRequest,
  OrderStatisticResponse,
  ReturnOrderRequest,
  SortPolicy,
  SubOrderInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { ReturnOrderStatusEnum } from '@api/service/management/trade/single/order/enum/returnOrderStatusEnum'
import { OrderTransaction } from '@api/service/management/trade/single/order/query/enum/OrderTransactionStatus'
import statisticOrderInServicer from '@api/service/management/trade/single/order/query/graphql/statisticOrderInServicer.graphql'
import OrderDetailVo from '@api/service/management/trade/single/order/query/vo/OrderDetailVo'
import OrderStatisticResponseVo from '@api/service/management/trade/single/order/query/vo/OrderStatisticResponseVo'
import QueryOrderListVo from '@api/service/management/trade/single/order/query/vo/QueryOrderListVo'
import { Page } from '@hbfe/common'
import { cloneDeep, uniq } from 'lodash'
import QueryOrderBase from '@api/service/management/trade/single/order/query/vo/QueryOrderBase'

/**
 * @description 订单查询
 */
class QueryOrder extends QueryOrderBase {
  /**
   * 查询订单商品分页
   * @param {Page} page - 分页参数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryOrderList(
    page: Page,
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderDetailVo[]> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName || queryParams.loginAccount)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) {
        page.totalSize = 0
        page.totalPageSize = 0
        return [] as OrderDetailVo[]
      }
    }
    const request = await queryParams.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!request.orderBasicData) request.orderBasicData = new OrderBasicDataRequest()
    // request.orderBasicData.channelTypesList = [1, 3]
    // request.orderBasicData.orderType = 1
    const option = new OrderSortRequest()
    option.field = OrderSortField.ORDER_NORMAL_TIME
    option.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(option) as OrderSortRequest[]
    const response = await MsTradeQuery.pageOrderInServicer({
      page,
      request,
      sortRequest
    })
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    const result =
      response.status.isSuccess() && this.isWeightyArr(response.data?.currentPageData)
        ? response.data?.currentPageData?.map(OrderDetailVo.from)
        : ([] as OrderDetailVo[])
    // console.log('filterOrderList', result, response)
    const res = await this.fillBuyerInfo(result)
    // * 添加退款订单信息
    if (isBusinessConsult && response?.data?.currentPageData?.length) {
      let orderIds = response.data.currentPageData.map((order) => {
        if (order.orderNo) {
          return order.orderNo
        }
      })

      if (orderIds.length) {
        orderIds = uniq(orderIds)
        const request = new ReturnOrderRequest()
        request.subOrderInfo = new SubOrderInfoRequest()
        request.subOrderInfo.orderInfo = new OrderInfoRequest()
        request.subOrderInfo.orderInfo.orderNoList = orderIds
        const newPage = new Page()
        newPage.pageNo = 1
        newPage.pageSize = page.pageSize
        const response = await MsTradeQuery.pageReturnOrderInServicer({
          page: newPage,
          request
        })

        res.map(async (item) => {
          const curOrder = response.data.currentPageData.find(
            (res) => res.subOrderInfo.orderInfo.orderNo === item.orderNo
          )
          if (curOrder?.returnOrderNo) {
            if ([0, 1, 2, 4, 5, 6].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunding
            }
            if ([8, 9, 10].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunded
            }
            if ([3, 7].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refundFailure
            }
          } else {
            item.returnOrderStatus = ReturnOrderStatusEnum.noRefund
          }
          if (item.subOrderItems.length > 1) {
            // 获取所有子订单的退款状态
            const allSubOrdersRefunded = item.subOrderItems?.every((subOrder) => {
              const subOrderRefundStatus = response.data?.currentPageData.find(
                (res) => res.subOrderInfo.subOrderNo === subOrder.subOrderNo
              )?.basicData?.returnOrderStatus

              // 如果子订单没有退款记录，默认未退款成功
              if (subOrderRefundStatus === undefined) {
                return false
              }

              // 判断子订单是否退款成功
              return [8, 9, 10].includes(subOrderRefundStatus)
            })
            if (item.returnOrderStatus === ReturnOrderStatusEnum.refunded) {
              if (!allSubOrdersRefunded) {
                item.returnOrderStatus = ReturnOrderStatusEnum.noRefund
              }
            }
          }
          if (!item.receiveAccountType && item.batchOrderNo) {
            const res = await MsTradeQuery.getBatchOrderInServicer(item.batchOrderNo)
            item.receiveAccountType = res.data.payInfo.receiveAccount.receiveAccountType == 1 ? 0 : 1
            item.payChannelName = res.data.payInfo.receiveAccount.payChannelName
          }
        })
      }
    }
    return res
  }

  /**
   * 查询分销订单商品分页
   * @param {Page} page - 分页参数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryFxOrderList(
    page: Page,
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderDetailVo[]> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return [] as OrderDetailVo[]
    }
    const request = await queryParams.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!request.orderBasicData) request.orderBasicData = new OrderBasicDataRequest()
    // request.orderBasicData.channelTypesList = [1, 3]
    // request.orderBasicData.orderType = 1
    const option = new OrderSortRequest()
    option.field = OrderSortField.ORDER_NORMAL_TIME
    option.policy = SortPolicy.DESC
    const sortRequest = Array(1).fill(option) as OrderSortRequest[]
    const response = await MsTradeQuery.pageOrderInDistributor({
      page,
      request,
      sortRequest
    })
    page.totalSize = response.data?.totalSize
    page.totalPageSize = response.data?.totalPageSize
    const result =
      response.status.isSuccess() && this.isWeightyArr(response.data?.currentPageData)
        ? response.data?.currentPageData?.map(OrderDetailVo.from)
        : ([] as OrderDetailVo[])
    // console.log('filterOrderList', result, response)
    const res = await this.fillBuyerInfo(result)
    // * 添加退款订单信息
    if (isBusinessConsult && response?.data?.currentPageData?.length) {
      let orderIds = response.data.currentPageData.map((order) => {
        if (order.orderNo) {
          return order.orderNo
        }
      })

      if (orderIds.length) {
        orderIds = uniq(orderIds)
        const request = new ReturnOrderRequest()
        request.subOrderInfo = new SubOrderInfoRequest()
        request.subOrderInfo.orderInfo = new OrderInfoRequest()
        request.subOrderInfo.orderInfo.orderNoList = orderIds
        const newPage = new Page()
        newPage.pageNo = 1
        newPage.pageSize = page.pageSize
        const response = await MsTradeQuery.pageReturnOrderInDistributor({
          page: newPage,
          request
        })

        res.map(async (item) => {
          const curOrder = response.data.currentPageData.find(
            (res) => res.subOrderInfo.orderInfo.orderNo === item.orderNo
          )
          if (curOrder?.returnOrderNo) {
            if ([0, 1, 2, 4, 5, 6].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunding
            }
            if ([8, 9, 10].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refunded
            }
            if ([3, 7].includes(curOrder.basicData.returnOrderStatus)) {
              item.returnOrderStatus = ReturnOrderStatusEnum.refundFailure
            }
          } else {
            item.returnOrderStatus = ReturnOrderStatusEnum.noRefund
          }
          if (!item.receiveAccountType && item.batchOrderNo) {
            const res = await MsTradeQuery.getBatchOrderInDistributor(item.batchOrderNo)
            item.receiveAccountType = res.data.payInfo.receiveAccount.receiveAccountType == 1 ? 0 : 1
            item.payChannelName = res.data.payInfo.receiveAccount.payChannelName
          }
        })
      }
    }
    return res
  }

  /**
   * 获取订单总金额、总数量
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryOrderListStatistic(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponseVo> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return new OrderStatisticResponseVo()
    }
    const result = new OrderStatisticResponseVo()
    result.totalOrderCount = await this.getOrderTotalCount(queryParams, isBusinessConsult)
    result.totalOrderAmount = await this.getOrderTotalAmount(queryParams, isBusinessConsult)
    // console.log('orderListStatistic', result)
    return result
  }

  /**
   * 获取订单总金额、总数量
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryFxOrderListStatistic(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponseVo> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return new OrderStatisticResponseVo()
    }
    const result = new OrderStatisticResponseVo()
    result.totalOrderCount = await this.getFxOrderTotalCount(queryParams, isBusinessConsult)
    result.totalOrderAmount = await this.getOrderFxTotalAmount(queryParams, isBusinessConsult)
    // console.log('orderListStatistic', result)
    return result
  }

  /**
   * 获取订单总金额、总数量 去学时
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async queryOrderListStatisticRemoveTotalPeriod(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponseVo> {
    if (!isBusinessConsult && (queryParams.idCard || queryParams.userName || queryParams.loginAccount)) {
      const validateExistUser = await this.validateExistUser(queryParams)
      if (!validateExistUser) return new OrderStatisticResponseVo()
    }
    const result = new OrderStatisticResponseVo()
    // result.totalOrderCount = await this.getOrderTotalCount(queryParams, isBusinessConsult)
    const res = await this.getOrderTotalAmountRemoveTotalPeriod(queryParams, isBusinessConsult)
    result.totalOrderAmount = res.totalOrderAmount
    result.totalOrderCount = res.totalOrderCount
    // console.log('orderListStatistic', result)
    return result
  }

  /**
   * 获取总数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async getOrderTotalCount(queryParams: QueryOrderListVo, isBusinessConsult: boolean): Promise<number> {
    let result = 0
    // 获取下单数
    const params = await queryParams.to(isBusinessConsult)
    const page = new Page()
    // 过滤批次单产生的订单
    if (!params.orderBasicData) params.orderBasicData = new OrderBasicDataRequest()
    // params.orderBasicData.orderType = 1
    const response = await MsTradeQuery.pageOrderInServicer({
      page,
      request: params
    })
    if (response.status?.isSuccess()) {
      result = response.data?.totalSize ?? 0
    }
    return result
  }

  /**
   * 获取总数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async getFxOrderTotalCount(queryParams: QueryOrderListVo, isBusinessConsult: boolean): Promise<number> {
    let result = 0
    // 获取下单数
    const params = await queryParams.to(isBusinessConsult)
    const page = new Page()
    // 过滤批次单产生的订单
    if (!params.orderBasicData) params.orderBasicData = new OrderBasicDataRequest()
    // params.orderBasicData.orderType = 1
    const response = await MsTradeQuery.pageOrderInDistributor({
      page,
      request: params
    })
    if (response.status?.isSuccess()) {
      result = response.data?.totalSize ?? 0
    }
    return result
  }

  /**
   * 获取交易成功总金额
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async getOrderTotalAmount(queryParams: QueryOrderListVo, isBusinessConsult: boolean): Promise<number> {
    let result = 0
    const params = cloneDeep(queryParams)
    params.orderStatus = OrderTransaction.Complete_Transaction
    const remoteParams = await params.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!remoteParams) remoteParams.orderBasicData = new OrderBasicDataRequest()
    // remoteParams.orderBasicData.orderType = 1
    const response = await MsTradeQuery.statisticOrderInServicer(remoteParams)
    if (response.status?.isSuccess()) {
      result = response.data?.totalOrderAmount ?? 0
    }
    return result
  }

  /**
   * 获取交易成功总金额
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async getOrderFxTotalAmount(queryParams: QueryOrderListVo, isBusinessConsult: boolean): Promise<number> {
    let result = 0
    const params = cloneDeep(queryParams)
    params.orderStatus = OrderTransaction.Complete_Transaction
    const remoteParams = await params.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!remoteParams) remoteParams.orderBasicData = new OrderBasicDataRequest()
    // remoteParams.orderBasicData.orderType = 1
    const response = await MsTradeQuery.statisticOrderInDistributor(remoteParams)
    if (response.status?.isSuccess()) {
      result = response.data?.totalOrderAmount ?? 0
    }
    return result
  }

  /**
   * 获取交易成功总金额
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  async getOrderTotalAmountRemoveTotalPeriod(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponse> {
    const params = cloneDeep(queryParams)
    params.orderStatus = OrderTransaction.Complete_Transaction
    const remoteParams = await params.to(isBusinessConsult)
    // 过滤批次单产生的订单
    if (!remoteParams) remoteParams.orderBasicData = new OrderBasicDataRequest()
    // remoteParams.orderBasicData.orderType = 1
    const response = await MsTradeQuery._commonQuery<OrderStatisticResponse>(statisticOrderInServicer, {
      request: remoteParams
    })
    // const response = await MsTradeQuery.statisticOrderInServicer(remoteParams)

    return response.data
  }

  /**
   * 根据订单号查询买家id
   */
  async queryBuyerIdByOrderNo(orderNo: string): Promise<string | undefined> {
    if (!orderNo) return undefined
    const response = await MsTradeQuery.getOrderInServicer(orderNo)
    if (response.status?.isSuccess()) {
      return response.data?.buyer?.userId || undefined
    }
    return undefined
  }
}

export default QueryOrder
