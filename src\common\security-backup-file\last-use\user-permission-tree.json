{"XY": {"name": "学员", "graphql": ["ms-account-v1.mutation.loadBasicValidationData:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.bindPhone:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-account-v1.mutation.loginAndBindOpenPlatform:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.loadRetrievePasswordBasicValidationData:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.resetPasswordWithToken:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.studentIdentify:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.collectiveRegistrationAdminIdentify:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.platformManagementAdminIdentify:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.loadBasicValidationDataWithBindPhone:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-account-v1.mutation.sendSmsCodeByRegister:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.sendSmsCodeByUpdatePhone:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.sendSmsCodeByUpdatePhoneWithSmsValidToken:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.sendSmsCodeByUpdatePwd:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.validSmsCode:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.validCaptcha:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.bindPhoneByAdmin:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-account-v1.mutation.changePasswordByCurrent:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "jsgkypt-wechatopenplatform-v1.mutation.applyOpenIdByServicerId:{\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validIsBindWeChatOpenPlatform:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getStudentInfoInMyself:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applyBindWeChatOpenPlatformAndValidLogin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applyLoginByOpenId:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applyCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validSmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validIdentity:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.forgetPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-config-v1.query.getCurrentFrontendConfig:{\"serviceName\":\"ms-config-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.changeNewPhone:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentUser:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getCurrentServicerInfo:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-servicer-v1.mutation.applyForServiceByDomainName:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-autonomouscourselearningscene-v1.mutation.applyCourseLearning:{\"serviceName\":\"ms-autonomouscourselearningscene-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.applyCourseLearning:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.applyChooseCourse:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}", "ms-interestcourselearningscene-v1.mutation.applyCourseLearning:{\"serviceName\":\"ms-interestcourselearningscene-v1\",\"authorizationRequired\":true}", "ms-studentlearning-v1.mutation.applyStudentLearningToken:{\"serviceName\":\"ms-studentlearning-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-certificate-v1.mutation.batchPrintCertificates:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "jsgkypt-certificate-v1.mutation.printCertificate:{\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageCommoditySkuCustomerCollectivePurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.listSkuPropertyCollectivePurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfo:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchool:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.pageStudentSchemeLearningBatchedInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.listSchemeSkuBatchedInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-account-v1.mutation.updateUser:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-dictionary-v1.mutation.updateShowName:{\"serviceName\":\"ms-dictionary-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listLeaderPositionLevelRoot:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.pageJobCategoryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getJobCategoryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-v1.query.isOnlineSchoolContractExpired:{\"serviceName\":\"ms-basicdata-query-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-shortmessage-v1.query.verifyAuthConfigEnable:{\"serviceName\":\"ms-shortmessage-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getExcellentCourses:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listCourseCategoryInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-offlineinvoice-v1.query.queryShippingMethodsForSchool:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-studentcourselearning-v1.mutation.applyCourseAppraisal:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}", "ms-studentcourse-appraisal-v1.mutation.appraisalCourse:{\"serviceName\":\"ms-studentcourse-appraisal-v1\",\"authorizationRequired\":true}", "ms-studentcourse-appraisal-v1.mutation.deleteAppraisalCourse:{\"serviceName\":\"ms-studentcourse-appraisal-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.getCourseAppraiseInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.getCourseAppraiseStatisticsInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.listCanChooseCourseTrainingOutlineOfChooseCourseLearningSceneInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "jsgkypt-contentprovider-v1.query.listContentProviderRelation:{\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCanChooseCourseOfChooseCourseLearningSceneInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.listTeacherInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCourseInSchemeInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageStudentCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageStudentCourseOfChooseCourseLearningSceneV2InMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.getCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCourseOfAutonomousCourseLearningSceneInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.statisticsCourseLearningSceneChooseCourseByOutlineOfInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageStudentCourseOfChooseCourseLearningSceneInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageStudentCourseOfInterestCourseLearningSceneInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-studentcourselearning-v1.mutation.applyCourseQuizLearning:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}", "ms-examination-v1.mutation.applyExam:{\"serviceName\":\"ms-examination-v1\",\"authorizationRequired\":true}", "ms-exam-answer-v1.mutation.applyAnswer:{\"serviceName\":\"ms-exam-answer-v1\",\"authorizationRequired\":true}", "ms-practice-v1.mutation.applyPractice:{\"serviceName\":\"ms-practice-v1\",\"authorizationRequired\":true}", "ms-exam-answer-v1.query.getQuestionToAnswerByQuestionIds:{\"serviceName\":\"ms-exam-answer-v1\",\"authorizationRequired\":true}", "ms-examextraction-v1.mutation.previewAnswerPaper:{\"serviceName\":\"ms-examextraction-v1\",\"authorizationRequired\":false}", "ms-exam-answer-v1.mutation.preSubmitAnswer:{\"serviceName\":\"ms-exam-answer-v1\",\"authorizationRequired\":true}", "ms-exam-answer-v1.mutation.applyHanding:{\"serviceName\":\"ms-exam-answer-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryForeStage.query.pageMyExaminationRecordInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryForeStage.query.getAnswerPaperRecordInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryForeStage.query.pageQuestionInMySelf:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryForeStage.query.pageMyPracticeRecordInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryForeStage.query.pageMyCourseQuizRecordInMyself:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-media-resource-learning-v1.mutation.prepareCourseLearningTiming:{\"serviceName\":\"ms-media-resource-learning-v1\",\"authorizationRequired\":true}", "ms-media-resource-learning-v1.mutation.keepAliveHeartbeat:{\"serviceName\":\"ms-media-resource-learning-v1\",\"authorizationRequired\":true}", "ms-course-play-resource-v1.mutation.applyCoursePlayResource:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.getCourseLearnStatistics:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCoursewareMediaPlayResource:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCoursewareMediaPreviewResource:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-media-resource-learning-v1.mutation.commitMediaLearningTiming:{\"serviceName\":\"ms-media-resource-learning-v1\",\"authorizationRequired\":true}", "ms-news-v1.mutation.browseNews:{\"serviceName\":\"ms-news-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listTeacherInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getCertificateTemplate:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getMySchemeConfig:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.pageSchemeLearningInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageCommoditySkuCustomerPurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseInSchemeInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageStudentCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareLearningRecordInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-certificate-v1.mutation.getCertificateSnapShot:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "jsgkypt-certificate-v1.mutation.getCertificateSnapshot:{\"authorizationRequired\":false}", "ms-learningscheme-v1.mutation.relearnForStudent:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.pageStudentSchemeConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.listSchemeSkuForStudentInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getSchemeLearningInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage.query.getSchemeLearningBySubOrderInMyself:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.listSkuPropertyCustomerPurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryForestage.query.getCommoditySkuCustomerPurchaseInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.immediateResetPassword:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-account-v1.mutation.registerCollective:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-account-v1.mutation.registerStudent:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.registerStudent:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.changePasswordByCurrent:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.updateStudent:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-choose-course-v1.mutation.prepareChooseCourse:{\"serviceName\":\"ms-choose-course-v1\",\"authorizationRequired\":true}", "jsgkypt-contentprovider-v1.query.listContentProvider:{\"authorizationRequired\":true}", "jsgkypt-choosecourse-v1.mutation.chooseCourse:{\"authorizationRequired\":true}", "ms-studentcourselearning-v1.mutation.applyCourseLearningPlay:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}", "ms-media-resource-learning-v1.mutation.applyCoursewareLearningToken:{\"serviceName\":\"ms-media-resource-learning-v1\",\"authorizationRequired\":true}", "ms-media-resource-learning-v1.mutation.applyMediaLearningTiming:{\"serviceName\":\"ms-media-resource-learning-v1\",\"authorizationRequired\":true}", "ms-course-play-resource-v1.mutation.applyCoursePreview:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCourseAudition:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCourseAuditionWithoutValidate:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listReviewTopNews:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getNewsDetailWithPreviousAndNext:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listPopUpsNews:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listRootNewsCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listChildNewsCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listTopNewsCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.pageSimpleNewsByPublish:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getMenusByPortalType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOnlineCollectiveRegisterConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOfflineCollectiveRegisterConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getTrainingInstitutionPortalInfo:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getBannerListByPortalType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentResisterFormConstraintForConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.getFriendLinkListByPortalType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-order-v1.mutation.applyCancelOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.onlinePayOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageExchangeOrderInMySelf:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageOrderInMyself:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageReturnOrderInMyself:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-payment-v1.query.prepareRepay:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-payment-v1.mutation.timeoutPayFlow:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-order-v1.mutation.preparePay:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-payment-v1.mutation.getPreParePayResult:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-commodity-v1.query.validateCommodity:{\"serviceName\":\"ms-commodity-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.reservingSchemeValidate:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.batchApplyInvoice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findCollectiveSignupMetaSchema:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findCommitCompleteAndSuccessSubTuskSuccessDataByPage:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findCommitCompleteAndFailSubTuskFailDataByPage:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.collectiveSignupDataProcess:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.findBatchOrderBatchPayStatus:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findCountGroupByKey:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.signupByOriginalCollectiveSignup:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.cancelCollectiveSignup:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.deleteCollectiveSignup:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.prepareBatchOrderPay:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-payment-v1.query.prepareBatchOrderRepay:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.onlinePayBatchOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.offlinePayBatchOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.importCollectiveSignup:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.collectiveSignupDataAnalysis:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.clearFailureData:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.exportCollectiveSignupImportFailExcel:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findImportCollectiveSignupCompleteSuccessDataByPage:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.query.findImportCollectiveSignupFailDataByPage:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.updateSignupData:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.deleteSignupData:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-collectivesign-v1.mutation.commitCollectiveSignup:{\"serviceName\":\"ms-collectivesign-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.getBatchOrderInMySelf:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.statisticOrderInMyself:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.statisticReturnOrderInMyself:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageBatchReturnOrderInMySelf:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.pageBatchOrderInMySelf:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningForestage.query.pageCanChooseCourseOfChooseCourseLearningSceneByCourseDeduplicationInMyself:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.prepareApplyInvoice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.applyInvoice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.preparePlaceOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryForestage.query.getOrderInMyself:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.getPaperPublishConfigureInServicer:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageLibraryInServicer:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.getQuestionCountInServicer:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-order-v1.mutation.createOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getAdminInfoInMyself:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursePackageInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageCoursewareSupplierInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageQuestionInServicer:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.getQuestionCountByRelateCourseInServicer:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.getCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageStudentCourseAppraiseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryPayFlowStatus:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}"]}}