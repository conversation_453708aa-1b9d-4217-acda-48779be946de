import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  CourseInSchemeRequest,
  CourseOfCourseTrainingOutlineRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import QueryCourseList from '@api/service/customer/course/query/QueryCourseList'
import ClassOutlineCourse from '@api/service/customer/scheme/models/ClassOutlineCourse'
import { Page } from '@hbfe/common'
/**
 * 远端课程大纲模型
 */
export interface ChildOutlineResp {
  /**
   * 课程包id
   */
  coursePackageId: string
  /**
   * 大纲名称
   */
  name: string
  /**
   * 大纲id
   */
  id: string
  /**
   * 排序
   */
  sort: number
  /**
   * 子节点
   */
  childOutlines: ChildOutlineResp[]
  /**
   * 分类
   */
  category: number
  operation: number
  /**
   * 父节点id，顶级节点是-1
   */
  parentId: string
  /**
   * 层级，顶级节点是1
   */
  level: number
  /**
   * 必学课程id集合，仅当自主选课时有效
   */
  compulsoryCourseIdList: string[]
}

/**
 * @description 课程大纲信息
 */
class ClassOutline {
  /**
   * 课程大纲id
   */
  id = ''
  /**
   * 课程大纲名称
   */
  name = ''
  /**
   * 是否是特殊选项 - 当选项是“全部”时为true
   */
  isSpecifiedOption = false
  /**
   * 当前层级及以下所有课程大纲id集合
   */
  curAndAfterIds: string[] = []
  /**
   * 必学课程Map集合，仅当自主选课时有效
   * {
   *   课程大纲id：必学课程id集合
   * }
   */
  compulsoryCourseMap: Map<string, string[]> = new Map<string, string[]>()
  /**
   * 课程包id
   */
  coursePackageId = ''
  /**
   * 大纲类型，1-必修，2-选修 仅限选课规则时候选用，类型为number
   */
  category = 2
  /**
   * 子节点
   */
  children: ClassOutline[] = []
  /**
   * 层级
   */
  level: number = null
  /**
   * 必学课程id集合，仅当自主选课时有效
   */
  compulsoryCourseIdList: string[]
  /**
   * 父级id
   */
  parentId = ''
  /**
   * 培训方案id
   */
  schemeId = ''
  /**
   * h5 - 是否展开
   */
  expanded = false
  /**
   * h5 - 顶部
   */
  scrollViewTop = 0
  /**
   * h5 - 当前分页
   */
  pageNo = 0
  /**
   * h5 - 总页码数
   */
  totalPageSize = 10
  /**
   * h5 - 加载方式暂定，如果是挂载需要直接请求
   */
  courseList: ClassOutlineCourse[] = []
  /**
   * h5 - 加载loading
   */
  loading = false

  static from(response: ChildOutlineResp, schemeId: string) {
    const opt = new ClassOutline()
    opt.schemeId = schemeId
    opt.id = response.id
    opt.name = response.name
    opt.parentId = response.parentId
    opt.coursePackageId = response.coursePackageId
    opt.category = response.category
    opt.level = response.level
    opt.compulsoryCourseIdList = response.compulsoryCourseIdList || ([] as string[])
    return opt
  }

  /**
   * 查询课程大纲下课程列表
   * @param page 分页参数
   */
  async queryClassOutlineCourseList(page: Page): Promise<ClassOutlineCourse[]> {
    let result = [] as ClassOutlineCourse[]
    const request = new CourseInSchemeRequest()
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.courseOfCourseTrainingOutline.outlineIds = this.curAndAfterIds
    request.schemeId = this.schemeId
    const response = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseInSchemeInServicer({
      page,
      request
    })
    page.totalPageSize = response.data?.totalPageSize
    page.totalSize = response.data?.totalSize
    if (response.status?.isSuccess() && response.data?.currentPageData && response.data?.currentPageData?.length) {
      const courseIdList = [...new Set(response.data.currentPageData.map(item => item.course.courseId))]
      if (courseIdList.length) {
        const queryCourseList = new QueryCourseList()
        const queryResult = await queryCourseList.queryCoursePageByIdList(courseIdList)
        result = response.data.currentPageData.map(item => {
          const opt = new ClassOutlineCourse()
          const courseInfo = queryResult.find(el => el.id === item.course.courseId)
          if (courseInfo) {
            opt.courseName = courseInfo?.name
            opt.enablePreview = courseInfo?.auditionStatus === 1
          }
          opt.courseId = item.course?.courseId
          opt.coursePeriod = item.courseOfCourseTrainingOutline?.period
          opt.classOutlineId = item.courseOfCourseTrainingOutline?.outlineId
          if (item.range?.courseSourceType && Number(item.range?.courseSourceType) === 1) {
            opt.coursePackageId = item.range?.courseSourceId
          }
          opt.isCompulsoryCourse = false
          let compulsoryCourseIdList = [] as string[]
          if (this.isSpecifiedOption) {
            // 是特殊选项
            compulsoryCourseIdList = this.compulsoryCourseMap.get(opt.classOutlineId) || []
          } else {
            // 不是特殊选项
            compulsoryCourseIdList = this.compulsoryCourseIdList
          }
          if (
            compulsoryCourseIdList &&
            compulsoryCourseIdList.length &&
            compulsoryCourseIdList.includes(opt.courseId)
          ) {
            opt.isCompulsoryCourse = true
          }
          return opt
        })
      }
    }
    return result
  }

  /**
   * 加载更多课程
   */
  async loadMore() {
    if (!this.expanded) return
    this.pageNo++
    if (this.pageNo > this.totalPageSize) return
    this.loading = true
    const page = new Page()
    page.pageNo = this.pageNo
    page.pageSize = 20
    const res = await this.queryClassOutlineCourseList(page)
    this.totalPageSize = page.totalPageSize
    const courseList = res || []
    this.courseList.push(...courseList)
    this.loading = false
  }
}

export default ClassOutline
