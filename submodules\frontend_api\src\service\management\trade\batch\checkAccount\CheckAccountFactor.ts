/*
 * @Description: 对账工厂
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-25 14:27:32
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-16 09:48:38
 */
import MutationCheckAccount from './mutation/MutationCheckAccount'
import QueryCheckAccount from './query/QueryCheckAccount'
class InvoiceFactor {
  /**
   * 集体报名对账查询
   */
  get queryCheckAccount() {
    return new QueryCheckAccount()
  }
  /**
   * 集体报名对账业务
   */
  get mutationCheckAccount() {
    return new MutationCheckAccount()
  }
}
export default InvoiceFactor
