<template>
  <div class="m-login-wrap">
    <div class="wrap-bd">
      <div class="logo-txt">福建省专业技术人员继续教育培训管理平台</div>
      <div class="f-flex-sub f-flex">
        <div class="login-pic">
          <img src="./assets/images/login-pic.png" alt=" " />
        </div>
        <div class="f-flex-sub f-mt20">
          <el-tabs v-model="activeName" stretch="true" class="m-login">
            <el-tab-pane label="帐号登录" name="first">
              <!--第一步-->
              <el-form ref="form" :model="form" :rules="rules" label-width="auto" class="m-form f-hide">
                <el-form-item prop="name">
                  <el-select v-model="select" clearable placeholder="请选择">
                    <el-option value="管理员"></el-option>
                    <i slot="prefix" class="el-input__icon iconfont icon-xiangmu"></i>
                  </el-select>
                </el-form-item>
                <el-form-item prop="name">
                  <el-input v-model="form.name" clearable placeholder="请输入帐号/手机号">
                    <i slot="prefix" class="el-input__icon iconfont icon-zhanghao"></i>
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <el-input v-model="form.name" clearable show-password placeholder="请输入密码">
                    <i slot="prefix" class="el-input__icon iconfont icon-mima"></i>
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <div class="f-flex">
                    <el-input v-model="form.name" clearable placeholder="请输入图形验证码" class="f-flex-sub">
                      <i slot="prefix" class="el-input__icon iconfont icon-yanzhengma"></i>
                    </el-input>
                    <div class="code">
                      <img src="./assets/images/code.jpg" title="看不清，点击刷新" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item class="is-text op f-pb10">
                  <el-checkbox>记住密码</el-checkbox>
                  <a href="#" class="f-link f-c9 f-fr">忘记密码？</a>
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <!--<el-alert type="error" show-icon :closable="false" class="m-alert f-mb10">错误提示文案</el-alert>-->
                  <el-button type="primary">立即登录</el-button>
                </el-form-item>
              </el-form>
              <!--第二步：手机验证码-->
              <el-form ref="form" :model="form" :rules="rules" label-width="auto" class="m-form f-mt30">
                <el-form-item>
                  <el-alert type="warning" show-icon :closable="false" class="m-alert"
                    >当前登录用户绑定手机号:185******80</el-alert
                  >
                </el-form-item>
                <el-form-item>
                  <div class="f-flex">
                    <el-input v-model="form.name" clearable placeholder="请输入短信验证码" class="f-flex-sub">
                      <i slot="prefix" class="el-input__icon iconfont icon-yanzhengma"></i>
                    </el-input>
                    <div class="code">
                      <el-button type="primary" plain>获取短信验证码</el-button>
                      <!--<el-button type="info" plain disabled>重新获取（60s）</el-button>-->
                    </div>
                  </div>
                </el-form-item>
                <el-form-item>
                  <div class="f-flex">
                    <el-input v-model="form.name" clearable placeholder="请输入图形验证码" class="f-flex-sub">
                      <i slot="prefix" class="el-input__icon iconfont icon-yanzhengma"></i>
                    </el-input>
                    <div class="code">
                      <img src="./assets/images/code.jpg" title="看不清，点击刷新" />
                    </div>
                  </div>
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button type="primary">继续登录</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
            <el-tab-pane label="验证码登记" name="second">
              <el-form ref="form" :model="form" :rules="rules" label-width="auto" class="m-form">
                <el-form-item prop="name">
                  <el-select v-model="value" placeholder="请选择">
                    <i slot="prefix" class="el-input__icon iconfont icon-xiangmu"></i>
                    <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item prop="name">
                  <el-input v-model="form.name" clearable placeholder="请输入帐号/手机号">
                    <i slot="prefix" class="el-input__icon iconfont icon-zhanghao"></i>
                  </el-input>
                </el-form-item>
                <el-form-item>
                  <el-input v-model="form.name" clearable show-password placeholder="请输入密码">
                    <i slot="prefix" class="el-input__icon iconfont icon-mima"></i>
                  </el-input>
                </el-form-item>
                <!--                <el-form-item>
                  <div class="f-flex">
                    <el-input v-model="form.name" clearable placeholder="请输入图形验证码" class="f-flex-sub">
                      <i slot="prefix" class="el-input__icon iconfont icon-yanzhengma"></i>
                    </el-input>
                    <div class="code">
                      <img src="./assets/images/code.jpg" title="看不清，点击刷新" />
                    </div>
                  </div>
                </el-form-item>-->
                <el-form-item class="is-text op f-pb10">
                  <el-checkbox>记住密码</el-checkbox>
                  <a href="#" class="f-link f-c9 f-fr">忘记密码？</a>
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <!--<el-alert type="error" show-icon :closable="false" class="m-alert f-mb10">错误提示文案</el-alert>-->
                  <el-button type="primary">立即登录</el-button>
                </el-form-item>
              </el-form>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="login-footer">
        <span class="f-mr20">主办单位：福建华博教育科技股份有限公司</span>
        <span class="f-mr20">版权所有：福建华博教育科技股份有限公司</span>
        <a href="http://beian.miit.gov.cn/" target="_blank">闽ICP备08103886号-2</a>
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        switch1: false,
        switch2: false,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          name1: '周一至周五 (09:00 - 12:00 14:00 - 17:30)',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down'],
        rules: {
          name: [{ required: true, message: '请输入帐号/手机号', trigger: 'blur' }]
        }
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
