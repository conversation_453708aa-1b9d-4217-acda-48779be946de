<template>
  <el-main v-if="$hasPermission('queryInfo')" desc="查询学习日志" actions="loadClassInfo,doSearch,getLearningType">
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="goBack">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: parentPage }">{{ parentPageName }}</el-breadcrumb-item>
      <el-breadcrumb-item>学习日志</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <!-- 用户信息 -->
        <div slot="header">
          <span class="tit-txt">用户信息</span>
        </div>
        <template v-if="$hasPermission('userInfo')" desc="用户信息组件" actions="@UserInfo">
          <UserInfo ref="userInfo"></UserInfo>
        </template>

        <!-- 培训班信息 -->
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">培训班信息</span>
        </div>
        <div class="f-p20" v-loading="loading.classInfoLoading">
          <el-row class="no-gutter">
            <el-form :inline="true" label-width="100px" class="m-text-form f-mt10">
              <el-col :span="8">
                <el-form-item label="学时：">{{ classInfo.trainClassDetail.trainClassBaseInfo.period }}</el-form-item>
              </el-col>
              <el-col :span="16">
                <el-form-item label="班级名称：">{{ classInfo.trainClassDetail.trainClassBaseInfo.name }}</el-form-item>
              </el-col>
            </el-form>
          </el-row>
        </div>
        <!-- 学习日志 -->
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">学习日志</span>
        </div>
        <div class="f-p20">
          <template v-if="hasData">
            <el-row :gutter="16" class="m-query">
              <el-form :inline="true" label-width="auto">
                <el-col :span="6">
                  <el-form-item label="课程名称">
                    <el-input v-model="queryStudentLearningLog.courseName" clearable placeholder="请输入课程名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="操作类型">
                    <el-select v-model="queryStudentLearningLog.operateType" clearable placeholder="请选择操作类型">
                      <el-option
                        v-for="item in operateTypeList"
                        :key="item.code"
                        :value="item.code"
                        :label="item.desc"
                        >{{ item.desc }}</el-option
                      >
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="记录时间">
                    <el-date-picker
                      v-model="recordDateTime"
                      @change="recordDateTimeChange"
                      @clear="recordDateTime = null"
                      :value-format="'yyyy-MM-dd HH:mm:ss'"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
                    <el-button @click="resetParam">重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <el-table
              v-loading="loading.listLoading"
              stripe
              :data="tableData"
              max-height="500"
              highlight-current-row
              class="m-table"
            >
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="记录时间" min-width="240">
                <template v-slot="{ row }">{{ row.recordDate }}</template>
              </el-table-column>
              <el-table-column label="操作类型" min-width="240">
                <template v-slot="{ row }">{{ getOperateType(row.operateType) }}</template>
              </el-table-column>
              <el-table-column label="课程名称" min-width="240">
                <template v-slot="{ row }">{{ row.courseName }}</template>
              </el-table-column>
            </el-table>
            <hb-pagination :page="page" v-bind="page" class="f-mt15 f-tr"></hb-pagination>
          </template>
          <!--空数据-->
          <div class="m-no-date f-ptb50" v-else v-loading="loading.listLoading">
            <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
            <div class="date-bd">
              <p class="f-f15 f-c9">暂无数据</p>
            </div>
          </div>
        </div>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import LearningOperateType, {
    LearningOperateTypeEnum
  } from '@api/service/management/train-class/query/enum/LearningOperateType'
  import QueryStudentLearningLog from '@api/service/management/train-class/query/QueryStudentLearningLog'
  import QueryStudentTrainClass from '@api/service/management/train-class/query/QueryStudentTrainClass'
  import StudentLearningLogDetail from '@api/service/management/train-class/query/vo/StudentLearningLogDetail'
  import StudentTrainClassDetailVo from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'
  import { UiPage } from '@hbfe/common'
  import UserInfo from '@hbfe/jxjy-admin-statisticsReport/src/components/user-info.vue'
  import { Component, Ref, Vue } from 'vue-property-decorator'

  @Component({
    components: { UserInfo }
  })
  export default class extends Vue {
    @Ref('userInfo') userInfo: UserInfo //用户信息
    /**
     * 起止时间
     */
    recordDateTime: string[] = null
    // 用户id
    userId = ''
    // 学号
    studentNo = ''
    page: UiPage
    // 上级页面名称
    parentPageName = ''
    /**
     * 查询实例
     */
    queryStudentLearningLog = new QueryStudentLearningLog()
    /**
     * 查询学员班级信息
     */
    queryStudentTrainClass = new QueryStudentTrainClass()
    /**
     * 学员班级信息
     */
    classInfo = new StudentTrainClassDetailVo()
    /**
     * 操作类型选项
     */
    operateTypeList = LearningOperateType.list()
    /**
     * tableData
     */
    tableData: StudentLearningLogDetail[] = []
    loading = {
      classInfoLoading: false,
      listLoading: false
    }

    get parentPage() {
      if (this.$route.query.parentPage === 'businessQuery') {
        this.parentPageName = '业务咨询'
        return '/training/customer-service/personal'
      } else if (this.$route.query.parentPage === 'studyDetail') {
        this.parentPageName = '学员学习明细'
        return '/statistic/learning-statistic'
      } else {
        return ''
      }
    }

    goBack() {
      this.$router.go(-1)
    }

    /**
     * 是否有数据
     */
    hasData = false
    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }
    async created() {
      this.userId = this.$route.query.userId as string
      this.queryStudentLearningLog.qualificationId = this.$route.query.qualificationId as string
      this.studentNo = this.$route.query.studentNo as string
    }

    async mounted() {
      await this.getLearningType()
      this.loadUserInfo()
      this.loadClassInfo()
      this.doSearch().then(() => {
        this.hasData = !!this.page.totalSize
      })
    }

    // 加载用户信息
    loadUserInfo() {
      this.userInfo.loadData(this.userId)
    }

    // 加载培训班信息
    async loadClassInfo() {
      this.loading.classInfoLoading = true
      this.queryStudentTrainClass
        .queryStudentTrainClassDetail(this.queryStudentLearningLog.qualificationId)
        .then((res) => {
          this.classInfo = res
        })
        .finally(() => {
          this.loading.classInfoLoading = false
        })
    }
    async doSearch() {
      this.loading.listLoading = true
      const res = await this.queryStudentLearningLog.queryStudentLearningLogListInSubProject(this.page)
      this.tableData = res
      this.loading.listLoading = false
    }

    /**
     * 重置
     */
    resetParam() {
      const qualificationId = this.queryStudentLearningLog.qualificationId
      this.recordDateTime = null
      this.queryStudentLearningLog = new QueryStudentLearningLog()
      this.queryStudentLearningLog.qualificationId = qualificationId
      this.page.currentChange(1)
    }
    getOperateType(type: LearningOperateTypeEnum) {
      return LearningOperateType.map.get(type)
    }
    /**
     * 日期选择器
     * @param value
     */
    recordDateTimeChange(value: string[]) {
      if (value) {
        this.queryStudentLearningLog.recordDateStartTime = value[0]
        this.queryStudentLearningLog.recordDateEndTime = value[1]
      } else {
        this.queryStudentLearningLog.recordDateStartTime = ''
        this.queryStudentLearningLog.recordDateEndTime = ''
      }
    }

    // 获取操作类型
    async getLearningType() {
      await this.queryStudentLearningLog.getLearningType(this.studentNo)
    }
  }
</script>
