import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { ShowUserRangeEnum } from '@api/service/management/train-class/query/enum/ShowUserRangeEnum'

export default class SaleChanelShowRange {
  /**
   * 销售范围
   */
  saleChannel: SaleChannelEnum = undefined

  /**
   * 用户展示范围
   */
  userShowRange: Array<ShowUserRangeEnum> = new Array<ShowUserRangeEnum>()

  constructor(saleChannel?: SaleChannelEnum, userShowRange?: Array<ShowUserRangeEnum>) {
    if (saleChannel) {
      this.saleChannel = saleChannel
    }
    if (userShowRange) {
      this.userShowRange = userShowRange
    }
  }
}
