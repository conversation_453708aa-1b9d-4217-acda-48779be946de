import IssueDetail from '@api/service/centre/train-class/offlinePart/model/IssueDetail'
import MsTradeQueryFrontGatewayTradeQueryForestage, {
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  IssueCommoditySkuRequest,
  IssueCommoditySkuResponse,
  IssueInfoNestRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { Page } from '@hbfe/common'
import { cloneDeep } from 'lodash'
import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
import LearningType from '@api/service/common/scheme/model/LearningType'

/**
 * @description 查询培训方案期别参数
 */
export class QueryTrainClassIssueParam {
  /**
   * 【必传】方案ID
   */
  schemeId = ''
  /**
   * 期别名称
   */
  issueName = ''
}

/**
 * @description 查询培训方案期别
 */
class QueryTrainClassIssue {
  /**
   * 转换方案期别列表信息
   * @param respDataList 接口数据列表
   * @param schemeId 方案ID
   * @private
   */
  private async convertSchemeIssueList(
    respDataList: IssueCommoditySkuResponse[],
    schemeId: string
  ): Promise<IssueDetail[]> {
    let result = [] as IssueDetail[]
    if (respDataList.length) {
      result = respDataList.map((dto) => {
        const vo = new IssueDetail()
        const { issueResource } = dto
        if (issueResource) {
          vo.periods = issueResource.period
          vo.id = issueResource.issueId
          vo.issueName = issueResource.issueName
          vo.registeredCount = issueResource.registeredCount
          const { issueReportDateScope, issueTrainingDateScope, issueSignUpDateScope } = issueResource
          if (issueReportDateScope) {
            vo.checkDateRange.startDate = issueReportDateScope.begin
            vo.checkDateRange.endDate = issueReportDateScope.end
          }
          if (issueTrainingDateScope) {
            vo.trainingDateRange.startDate = issueTrainingDateScope.begin
            vo.trainingDateRange.endDate = issueTrainingDateScope.end
          }
          if (issueSignUpDateScope) {
            vo.registerBeginTime = issueSignUpDateScope.begin
            vo.registerEndTime = issueSignUpDateScope.end
          }
          vo.trainingPointId = issueResource.trainingPointId
        }
        return vo
      })
      // 填充期别信息
      if (result.length) {
        const schemeJsonResp =
          await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.getSchemeConfigInServicer({
            schemeId
          })
        if (schemeJsonResp.data) {
          const schemeJson = schemeJsonResp.data.schemeConfig
          if (schemeJson) {
            const { issue } = LearningType.configIssueAndQuestionnaire(JSON.parse(schemeJson) as Scheme)
            const issueList = issue.issueConfigList
            result = result.map((issueVo) => {
              const item = new IssueDetail()
              const issueConfig = issueList.find((el) => el.id === issueVo.id)
              if (issueConfig) {
                Object.assign(item, issueConfig, {
                  registeredCount: issueVo.registeredCount
                } as Partial<IssueDetail>)
              } else {
                Object.assign(item, issueVo)
              }
              return item
            })
          }
        }
      }
    }
    return result
  }

  /**
   * 【集体报名管理员】查询培训方案期别列表
   * @param queryParam 查询参数
   */
  async queryCollectiveTrainClassIssueList(queryParam: QueryTrainClassIssueParam): Promise<IssueDetail[]> {
    let result = [] as IssueDetail[]
    const queryRequest = new IssueCommoditySkuRequest()
    const page = new Page(1, 200)
    queryRequest.issueRequest = new IssueInfoNestRequest()
    queryRequest.issueRequest.schemeIdList = [queryParam.schemeId]
    const respDataList = [] as IssueCommoditySkuResponse[]
    const sortRequest = [
      {
        sortField: CommoditySkuSortField.ISSUE_TRAINING_BEGIN_TIME,
        policy: SortPolicy.ASC
      } as CommoditySkuSortRequest
    ]

    const { data } =
      await MsTradeQueryFrontGatewayTradeQueryForestage.pageIssueCommoditySkuCollectivePurchaseInServicer({
        page,
        queryRequest,
        sortRequest
      })
    if (data.currentPageData && data.currentPageData.length) {
      respDataList.push(...data.currentPageData)
    }
    if (data && data.totalPageSize > 200) {
      const requestList = Array(Math.ceil(data.totalSize / 200)).fill('')
      const tmpRespList = await Promise.all(
        requestList.map(async (item, index) => {
          const tmpPage = new Page(index + 2, 200)
          const tmpReq = cloneDeep(queryRequest)
          const tmpSortRequest = cloneDeep(sortRequest)
          return MsTradeQueryFrontGatewayTradeQueryForestage.pageIssueCommoditySkuCollectivePurchaseInServicer({
            page: tmpPage,
            queryRequest: tmpReq,
            sortRequest: tmpSortRequest
          })
        })
      )
      tmpRespList.forEach((tmpResp) => {
        const { status, data } = tmpResp
        if (status && status.isSuccess() && data && data.currentPageData && data.currentPageData.length) {
          respDataList.push(...data.currentPageData)
        }
      })
    }
    if (respDataList.length) {
      result = await this.convertSchemeIssueList(respDataList, queryParam.schemeId)
    }
    if (queryParam.issueName) {
      result = result.filter((item) => item.issueName.includes(queryParam.issueName))
    }
    return result
  }
}

export default QueryTrainClassIssue
