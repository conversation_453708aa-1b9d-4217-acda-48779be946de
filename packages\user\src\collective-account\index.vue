<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: <PERSON> yt
 * @LastEditTime: 2022-06-14 10:14:41
-->
<route-meta>
{
"isMenu": true,
"title": "集体报名账号管理",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main
    v-if="$hasPermission('groupRegistrationManagement')"
    desc="查询"
    actions="created,currentPageChange,queryPageList"
  >
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="姓名">
                <el-input v-model="params.name" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>

            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="手机号">
                <el-input v-model="params.phone" clearable placeholder="请输入账号/手机号" />
              </el-form-item>
            </el-col>

            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="注册时间">
                <el-date-picker
                  v-model="params.time"
                  type="datetimerange"
                  range-separator="至"
                  value-format="yyyy-MM-dd HH:mm:ss"
                  start-placeholder="起始时间"
                  end-placeholder="结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="queryPageList">查询</el-button>
                <el-button @click="restQuery">重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="姓名" min-width="180" fixed="left">
            <template slot-scope="scope">{{ scope.row.userInfo.userName }}</template>
          </el-table-column>
          <el-table-column label="帐号" min-width="280">
            <template slot-scope="scope">{{ scope.row.userInfo.phone }}</template>
          </el-table-column>
          <el-table-column label="注册时间" min-width="280">
            <template slot-scope="scope">{{ scope.row.accountInfo.createdTime }}</template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  import { UiPage } from '@hbfe/common'
  @Component
  export default class extends Vue {
    page: UiPage
    queryCollective = UserModule.queryUserFactory.queryCollectiveManagerList
    input = ''
    params = {
      name: '',
      phone: '',
      time: [] as any
    }
    param = {
      name: '',
      phone: '',
      createStartTime: '',
      createEndTime: ''
    }
    tableData = [] as any
    totalSize = 0
    pageSizeChange() {
      //todo
    }
    constructor() {
      super()
      this.page = new UiPage(this.queryPageList, this.queryPageList)
    }
    async created() {
      this.tableData = await this.queryCollective.queryPageCollectiveList(this.page, this.param)
      this.totalSize = this.page.totalSize
      console.log(this.tableData)
    }
    // 点击查询订单列表
    // 点击查询订单列表
    async queryPageList() {
      if (this.params.time == null) {
        this.param.createStartTime = ''
        this.param.createEndTime = ''
      } else {
        this.param.createStartTime = this.params.time[0]
        this.param.createEndTime = this.params.time[1]
      }
      this.param.name = this.params.name
      this.param.phone = this.params.phone
      // this.page.pageNo = 1
      this.tableData = await this.queryCollective.queryPageCollectiveList(this.page, this.param)
      this.totalSize = this.page.totalSize
      console.log(this.page)
    }

    async restQuery() {
      this.params.time = []
      this.params.name = ''
      this.params.phone = ''
      await this.currentPageChange()
      console.log(this.params)
    }

    async currentPageChange() {
      this.param.name = this.params.name
      this.param.phone = this.params.phone
      this.param.createStartTime = this.params.time[0]
      this.param.createEndTime = this.params.time[1]
      this.page.currentChange(this.page.pageNo)
      this.tableData = await this.queryCollective.queryPageCollectiveList(this.page, this.param)
      //todo
    }
  }
</script>
