import Menu from '@api/service/management/authority/security/query/vo/Menu'
import domainGateWay, {
  RoleToFunctionalAuthorityResponse,
  SecurityObjectNewResponse
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { SecurityGroupTree } from '@api/service/management/authority/security/query/vo/SecurityGroupTree'
import AssembleTree from '@api/service/common/utils/AssembleTree'
import FindByTree from '@api/service/common/utils/FindByTree'
import { concat, uniqBy } from 'lodash'
import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
import systemContext from '@api/service/common/context/Context'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import SchoolServiceIdStrategy from '@api/service/common/diffSchool/SchoolServiceIdStrategy'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'

class QuerySecurity {
  /**
   * 权限数据
   * @private
   */
  private securityRes: RoleToFunctionalAuthorityResponse[] = []
  /**
   * 角色信息列表
   */
  roleInfoList: { id: string; category: CategoryEnums }[] = []

  // 智能学习
  IntelligenceLearningModuleObj = new IntelligenceLearningModule()

  /*
   *  获取当前角色安全对象
   * */
  async getCurrentUserSecurityGroup() {
    return this.getCurrentUserSecurityGroupByCategory(CategoryEnums.all)
  }

  /**
   * 指定角色ID 查该角色安全对象信息
   */
  async getSpecificRoleSecurityGroup(roleId: string, category: CategoryEnums) {
    if (!roleId) {
      throw '未填写查询角色ID'
    } else {
      const securityRes = await domainGateWay.findFunctionalAuthorityByRoleIdNew(roleId)
      let tmpArr: SecurityGroupTree[] = []
      if (securityRes.status.isSuccess()) {
        const transitionData = securityRes.data.securityObjectResponses?.map((data) => SecurityGroupTree.from(data))
        //列表转换为树
        const assembleTree = new AssembleTree<SecurityGroupTree>(transitionData, 'id', 'parentId')
        tmpArr = assembleTree.assembleTree()
      }
      tmpArr = this.initDistributionSecurity(tmpArr, category)
      this.sortTree(tmpArr)
      return tmpArr
    }
  }

  /**
   * 获取当前登录账号的所有角色id集合
   */
  async getAllRoleIds() {
    this.roleInfoList = []
    const roleRes = await domainGateWay.findRoleByAccountId()
    const roleIds = new Array<string>()
    roleRes.data.forEach((item) => {
      roleIds.push(item.id)
      // 暂时存储在内存中的角色信息
      this.roleInfoList.push({
        id: item.id,
        category: item.category
      })
    })
    return roleIds
  }

  /**
   * 获取当前登录角色
   */
  async getCurrentRoleId() {
    let roleId = ''
    const roleRes = await domainGateWay.findRoleByAccountId()
    if (roleRes.status.isSuccess()) {
      roleId = roleRes.data[0].id
    } else {
      console.log('获取角色失败')
    }
    return roleId
  }

  /**
   * 获取当前用户的角色菜单
   */
  async getCurrentUserMenu(menuList: Array<Menu>) {
    let menuTree: Menu[] = []
    const currentMenuList: Menu[] = []
    const roleId = await this.getCurrentRoleId()
    const securityRes = await domainGateWay.findFunctionalAuthorityByRoleIdNew(roleId)
    if (securityRes.status.isSuccess()) {
      const transitionData = securityRes.data.securityObjectResponses
        ?.filter((item) => item.isMenu && item.urlContent !== '/settings')
        .map((data) => SecurityGroupTree.from(data))
      transitionData.forEach((security) => {
        if (security.parentId != '-1') {
          const menuWithConfig = this.getMenuConfigFromDev(security, menuList)
          if (menuWithConfig.name) {
            currentMenuList.push(menuWithConfig)
          }
        }
      })
      //列表转换为树
      const assembleTree = new AssembleTree<Menu>(currentMenuList, 'id', 'parentId')
      menuTree = assembleTree.assembleTree()
    } else {
      console.log('获取安全对象组失败')
    }
    return menuTree
  }

  /**
   * 获取当前用户的合并菜单
   */
  async getMergeUserMenu(menuList: Array<Menu>) {
    const smartLearning = await this.IntelligenceLearningModuleObj.doQueryServiceConfig()
    let menuTree: Menu[] = []
    const currentMenuList: Menu[] = []
    const roleRes = await domainGateWay.findRoleByAccountId()
    const idList: string[] = []
    await systemContext.buildContextByAdmin()
    const currentSchool = SchoolServiceIdStrategy.currentSchool()
    const serviceId = CommonConfigCenter.getFrontendApplication(frontendApplication.trainingInstitutionServicerId)
    //判断是否是运营域
    const isOperation = systemContext.servicerInfo.id === serviceId
    for (const key in roleRes.data) {
      idList.push(roleRes.data[key].id)
    }
    const securityRes = await domainGateWay.findFunctionalAuthorityByRoleIdsNew(idList)
    if (securityRes.status.isSuccess()) {
      let tempArr: SecurityObjectNewResponse[] = []
      for (const key in securityRes.data) {
        tempArr = uniqBy(concat(tempArr, securityRes.data[key].functionalAuthorityList), 'id')
      }
      if (smartLearning !== 1) {
        const znxxId = tempArr.find((item) => {
          return item.content && item.content == 'WXGLY.training.intelligent-learning' && item?.isMenu
        })?.id
        if (znxxId) {
          tempArr = tempArr.filter((item) => !(item?.id == znxxId || item?.parentId == znxxId))
        }
      }
      tempArr = tempArr.map((temp) => {
        if (temp.ext) {
          temp.ext = JSON.parse(temp.ext)
        }
        return temp
      })
      const transitionData = tempArr
        .filter((item) => item.isMenu && item.urlContent !== '/settings')
        .map((data) => SecurityGroupTree.from(data))
        .filter((data) => {
          if (!data.ext) {
            return true
          }
          const { diffSchool, isCommon } = data.ext
          if (!diffSchool) {
            return true
          }
          return !!(diffSchool.includes(currentSchool) || isCommon)
        })
      transitionData.forEach((security) => {
        if (security.parentId != '-1') {
          const menuWithConfig = this.getMenuConfigFromDev(security, menuList)
          if (menuWithConfig.name) {
            if (menuWithConfig.name == '首页' && isOperation) {
              return false
            } else {
              currentMenuList.push(menuWithConfig)
            }
          }
        }
      })
      //列表转换为树
      const assembleTree = new AssembleTree<Menu>(currentMenuList, 'id', 'parentId')
      menuTree = assembleTree.assembleTree()
    } else {
      console.log('获取安全对象组失败')
    }
    return menuTree
  }

  /**
   * 将本地的菜单配置赋值到返回的安全对象中（通过名字匹配）
   */
  getMenuConfigFromDev(security: SecurityGroupTree, menuList: Array<Menu>) {
    const findByTree = new FindByTree<Menu>(menuList)
    const pathArr = findByTree.findPath({ targetValue: security.name, children: 'children', targetKey: 'name' })
    const menu = new Menu()
    for (const item of pathArr) {
      if (item.router.path === security.url) {
        menu.id = security.id
        menu.name = security.name
        menu.openWhenInit = item.openWhenInit
        menu.code = item.code
        menu.closeAble = item.closeAble
        menu.ext = item.ext
        menu.parentId = security.parentId
        menu.router = item.router
        menu.meta = item.meta
        return menu
      }
    }
    return menu
  }

  /**
   * 获取当前用户安全对象信息根据类型
   * @param category
   */
  async getCurrentUserSecurityGroupByCategory(category: CategoryEnums) {
    const currentSchool = SchoolServiceIdStrategy.currentSchool()
    if (!this.roleInfoList?.length) {
      await this.getAllRoleIds()
    }
    let roleIds: string[] = []
    if (category === CategoryEnums.all) {
      roleIds = this.roleInfoList.map((res) => res.id)
    } else {
      roleIds = this.roleInfoList.filter((res) => res.category == category).map((res) => res.id)
    }
    if (!roleIds?.length) {
      return new Array<SecurityGroupTree>()
    }
    const response = await domainGateWay.findFunctionalAuthorityByRoleIdsNew(roleIds)
    let tempArr: SecurityObjectNewResponse[] = []
    const temp = response.data.filter((res) => roleIds.includes(res.roleId))
    for (const key in temp) {
      tempArr = uniqBy(concat(tempArr, temp[key].functionalAuthorityList), 'id')
    }
    tempArr = tempArr.map((temp) => {
      const type = typeof temp.ext
      if (temp.ext && type === 'string') {
        temp.ext = JSON.parse(temp.ext)
      }
      return temp
    })
    let tmpArr: SecurityGroupTree[] = []
    if (response.data?.length) {
      const transitionData = tempArr?.map(SecurityGroupTree.from).filter((data) => {
        if (!data.ext) {
          return true
        }
        const { diffSchool, isCommon } = data.ext
        if (!diffSchool) {
          return true
        }
        return !!(diffSchool.includes(currentSchool) || isCommon)
      })

      //列表转换为树
      const assembleTree = new AssembleTree<SecurityGroupTree>(transitionData, 'id', 'parentId')
      tmpArr = assembleTree.assembleTree()
    }
    // 暂时根据url去判断需要写入的节点信息。后续需要对安全对象生成信息添加ext信息，去锁定节点信息
    this.initDistributionSecurity(tmpArr, category)
    this.sortTree(tmpArr)
    return tmpArr
  }

  /**
   * 根据角色id与角色分类获取安全对象信息
   * @param roleIds 角色ids
   * @param category 角色分类
   */
  async getSecurityGroupByRoleId(roleIds: Array<string>, category: CategoryEnums) {
    const currentSchool = SchoolServiceIdStrategy.currentSchool()
    if (!roleIds?.length) {
      return new Array<SecurityGroupTree>()
    }
    const response = await domainGateWay.findFunctionalAuthorityByRoleIdsNew(roleIds)
    let tempArr: SecurityObjectNewResponse[] = []
    const temp = response.data.filter((res) => roleIds.includes(res.roleId))
    for (const key in temp) {
      tempArr = uniqBy(concat(tempArr, temp[key].functionalAuthorityList), 'id')
    }
    tempArr = tempArr.map((temp) => {
      const type = typeof temp.ext
      if (temp.ext && type === 'string') {
        temp.ext = JSON.parse(temp.ext)
      }
      return temp
    })
    let tmpArr: SecurityGroupTree[] = []
    if (response.data?.length) {
      const transitionData = tempArr?.map(SecurityGroupTree.from).filter((data) => {
        if (!data.ext) {
          return true
        }
        const { diffSchool, isCommon } = data.ext
        if (!diffSchool) {
          return true
        }
        return !!(diffSchool.includes(currentSchool) || isCommon)
      })

      //列表转换为树
      const assembleTree = new AssembleTree<SecurityGroupTree>(transitionData, 'id', 'parentId')
      tmpArr = assembleTree.assembleTree()
    }
    // 暂时根据url去判断需要写入的节点信息。后续需要对安全对象生成信息添加ext信息，去锁定节点信息
    this.initDistributionSecurity(tmpArr, category)
    this.sortTree(tmpArr)
    return tmpArr
  }

  private initDistributionSecurity(tmpArr: SecurityGroupTree[], category: CategoryEnums) {
    // 暂时根据url去判断需要写入的节点信息。后续需要对安全对象生成信息添加ext信息，去锁定节点信息
    if (category === CategoryEnums.gys || category === CategoryEnums.fxs) {
      let count = 0
      const fx = tmpArr[0].children.find((res, index) => {
        count = index
        if (res.url === '/fx/distribution') {
          tmpArr[0].children.splice(count, 1)
        }
        return res.url === '/fx/distribution'
      })
      tmpArr[0].children.map((res) => {
        if (res.url === '/marketing-center') {
          res.children.map((item1) => {
            if (item1.url === '/marketing-center/marketing-center') {
              item1.children.map((item2) => {
                if (item2.url === 'WXGLY.marketing-center.marketing-center.query') {
                  fx.parentId = item2.id
                  item2.children = [fx]
                }
              })
            }
          })
        }
      })
    }
    this.sortTree(tmpArr)
    return tmpArr
  }

  /**
   * 排序权限树列表
   * @param tmpArr
   * @private
   */
  private sortTree(tmpArr: SecurityGroupTree[]) {
    tmpArr.map((item) => {
      if (item.children.length) {
        return this.sortTree(item.children)
      } else {
        return tmpArr.sort((treeA: SecurityGroupTree, treeB: SecurityGroupTree) => treeA.sortNo - treeB.sortNo)
      }
    })
  }
}

export default QuerySecurity
