<template>
  <el-container>
    <el-aside width="240px">
      <!--侧边栏按钮 展开时显示-->
      <a href="#" class="aside-btn el-icon-s-fold"></a>
      <!--侧边栏按钮 收起时显示-->
      <!--<a href="#" class="aside-btn el-icon-s-unfold"></a>-->
      <div class="logo">
        <span class="logo-txt">通用平台运营管理后台</span>
      </div>
      <div class="user-info">
        <img class="photo" src="./assets/images/default-photo.jpg" alt=" " />
        <p class="name">张某某</p>
        <div class="op-btn f-mt10">
          <el-button type="primary" size="mini" round plain>
            <i class="el-icon-s-tools"></i>
            帐号设置
          </el-button>
        </div>
      </div>
      <el-menu default-active="1" class="aside-nav" unique-opened="true" @open="handleOpen" @close="handleClose">
        <el-menu-item index="0">
          <template slot="title">
            <i class="iconfont icon-weiwangxiao"></i>
            <span>开通网校</span>
          </template>
        </el-menu-item>
        <!--网校管理-->
        <el-menu-item index="1">
          <template slot="title">
            <i class="hb-iconfont icon-setup"></i>
            <span>网校管理</span>
          </template>
        </el-menu-item>
      </el-menu>
      <div class="m-company-info">
        <p>福建华博教育科技股份有限公司</p>
        <a class="a-txt" href="http://beian.miit.gov.cn/" target="_blank">闽ICP备2021002737号</a>
      </div>
    </el-aside>
    <el-container>
      <el-header height="100px" class="f-flex">
        <ul class="header-nav f-flex-sub">
          <li class="nav-item current">
            <i class="iconfont icon-weiwangxiao"></i>
            <span class="txt">网校管理</span>
          </li>
          <li class="current-bg" style="min-width: 124px;"></li>
        </ul>
        <ul class="header-nav">
          <li class="nav-item nav-item-1"><i class="iconfont icon-tuichu"></i>退出</li>
        </ul>
      </el-header>
      <div class="tags">
        <span class="prev"><i class="el-icon el-icon-arrow-left"></i></span>
        <div class="tags-bd">
          <div class="tags-items" style="width: 500%;">
            <el-tag class="current" closable>网校管理</el-tag>
          </div>
        </div>
        <span class="next"><i class="el-icon el-icon-arrow-right"></i></span>
        <el-dropdown :hide-on-click="false">
          <span class="more"><i class="el-icon el-icon-more"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>黄金糕</el-dropdown-item>
            <el-dropdown-item>狮子头</el-dropdown-item>
            <el-dropdown-item>螺蛳粉</el-dropdown-item>
            <el-dropdown-item disabled>双皮奶</el-dropdown-item>
            <el-dropdown-item divided>蚵仔煎</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <!--面包屑-->
      <el-breadcrumb separator-class="el-icon-arrow-right">
        <el-button type="text" size="mini" class="return-btn">
          <i class="iconfont icon-lsh-return"></i>
        </el-button>
        <el-breadcrumb-item :to="{ path: '/' }">网校管理</el-breadcrumb-item>
        <el-breadcrumb-item>修改网校</el-breadcrumb-item>
      </el-breadcrumb>
      <el-main>
        <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb10">
          配置提示：<br />
          1.网校可修改内容，修改后信息会同步影响到当前已应用的网校，若网校已生成相应数据的信息则不影响，自修改后同步更新。<br />
          2.修改网校请慎重。
        </el-alert>
        <!--顶部tab标签-->
        <el-tabs v-model="activeName" class="m-tab-top is-sticky">
          <el-tab-pane label="基础信息" name="first">
            <div class="f-p15">
              详见 0203_网校管理_修改网校_基础配置.vue
            </div>
          </el-tab-pane>
          <el-tab-pane label="网校配置" name="second">
            <div class="f-p15">
              <el-card shadow="never" class="m-card">
                <div slot="header" class="">
                  <span class="tit-txt">网校配置</span>
                </div>
                <div class="f-p10">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <!--右侧输入框及选择器默认长度为100%，中长.form-l，短.form-s-->
                      <el-form ref="form" :model="form" label-width="auto" class="m-form">
                        <el-form-item label="网校域名：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">华博统一域名</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">业主自有域名</el-radio>
                        </el-form-item>
                        <el-form-item label="网校域名：" required>
                          <el-input clearable placeholder="请输入网校域名，例如www.XXX.com" class="form-l f-mr10" />
                          <div class="f-co">注：域名确认后需技术部配合处理方可生效</div>
                        </el-form-item>
                        <el-form-item label="网校域名：" required>
                          <el-input
                            clearable
                            placeholder="请输入业主提供的网校完整域名，如www.XXX.com"
                            class="form-l f-mr10"
                          />
                          <div class="f-co f-mt5 lh20">
                            注：域名若使用业主自有的，请注意需完成工信部备案和华为云接入流程，<br />&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;若是https的域名，请确认是否已购买https证书。
                          </div>
                        </el-form-item>
                        <el-form-item label="提供终端：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">PC端</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">移动端（H5）</el-radio>
                        </el-form-item>
                        <el-form-item label="H5域名：" required>
                          <el-input clearable placeholder="https：//zypx.h5.59iedu.com" class="form-l" />
                        </el-form-item>
                        <el-form-item label="短信服务：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">名商通</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">联麓</el-radio>
                        </el-form-item>
                        <el-form-item label="短信帐号：">
                          <el-input clearable placeholder="提供短信服务请在此输入短信帐号信息" class="form-l" />
                        </el-form-item>

                        <el-form-item label="短信密码：">
                          <el-input clearable placeholder="请在此输入短信帐号对应密码" class="form-l" />
                        </el-form-item>
                        <el-form-item label="服务期限：" required>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">长期培训</el-radio>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">指定期限</el-radio>
                        </el-form-item>
                        <el-form-item label="到期时间：" required>
                          <el-date-picker
                            v-model="value1"
                            type="date"
                            placeholder="请选择网校服务到期时间"
                            class="form-l"
                          >
                          </el-date-picker>
                        </el-form-item>
                        <el-form-item label="友盟统计：" required>
                          <el-radio v-model="radio1" label="2" border class="f-mr10">默认</el-radio>
                          <el-radio v-model="radio1" label="1" border class="f-mr10">业主自有</el-radio>
                          <div class="f-co f-mt5 lh20">
                            注：默认使用友盟统计，如需查看网校的具体统计情况，请联系联系运维部门。本网校ID为XXXX。
                          </div>
                          <div class="f-mt10">
                            <el-input clearable placeholder="请输入友盟统计代码（cnzz code )" class="form-l" />
                          </div>
                        </el-form-item>
                        <el-form-item required>
                          <div slot="label" class="f-vm">
                            完善信息页设置<el-tooltip effect="dark" placement="top" popper-class="m-tooltip"
                              ><i class="el-icon-info m-tooltip-icon f-co f-ml5"></i>
                              <div slot="content">
                                1.强制跳过完善信息页可能会导致网校某些业务逻辑无法正常闭环、字段信息缺失等风险，请在使用此功能前仔细评估后再修改！<br />2.当学员信息不全时，会在登录/报名班级/打印培训证书三个环节触发完善信息页。<br />3.若在登录/报名班级环节不想触发完善信息机制，请选择第二个选项“强制跳过完善信息页面”。<br />4.该设置项在web端和移动端生效。
                              </div> </el-tooltip
                            >：
                          </div>
                          <el-radio v-model="radio1" label="1" border class="f-mr10"
                            >当学员信息不全时，强制触发完善信息页面</el-radio
                          >
                          <el-radio v-model="radio1" label="2" border>强制跳过完善信息页面</el-radio>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </div>
          </el-tab-pane>
          <el-tab-pane label="模板配置" name="third">
            <div class="f-p15">详见 0203_网校管理_修改网校_模板配置.vue</div>
          </el-tab-pane>
          <el-tab-pane label="管理员信息" name="fourth">
            <div class="f-p15">详见 0203_网校管理_修改网校_管理员信息.vue</div>
          </el-tab-pane>
        </el-tabs>
        <div class="m-btn-bar f-tc is-sticky-1">
          <el-button>取消</el-button>
          <el-button type="primary">保存</el-button>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'second',
        activeName1: 'first',
        activeName2: 'first',
        activeNames: ['3'],
        props: { multiple: true },
        radio: 3,
        radio1: '1',
        input: '',
        select: '',
        checked: true,
        checked2: false,
        checked3: false,
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        visible: false,
        fits: ['cover'],
        options: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'zujian',
            label: '组件',
            children: [
              {
                value: 'basic',
                label: 'Basic',
                children: [
                  {
                    value: 'layout',
                    label: 'Layout 布局'
                  },
                  {
                    value: 'color',
                    label: 'Color 色彩'
                  },
                  {
                    value: 'typography',
                    label: 'Typography 字体'
                  },
                  {
                    value: 'icon',
                    label: 'Icon 图标'
                  },
                  {
                    value: 'button',
                    label: 'Button 按钮'
                  }
                ]
              },
              {
                value: 'form',
                label: 'Form',
                children: [
                  {
                    value: 'radio',
                    label: 'Radio 单选框'
                  },
                  {
                    value: 'checkbox',
                    label: 'Checkbox 多选框'
                  },
                  {
                    value: 'input',
                    label: 'Input 输入框'
                  },
                  {
                    value: 'input-number',
                    label: 'InputNumber 计数器'
                  },
                  {
                    value: 'select',
                    label: 'Select 选择器'
                  },
                  {
                    value: 'cascader',
                    label: 'Cascader 级联选择器'
                  },
                  {
                    value: 'switch',
                    label: 'Switch 开关'
                  },
                  {
                    value: 'slider',
                    label: 'Slider 滑块'
                  },
                  {
                    value: 'time-picker',
                    label: 'TimePicker 时间选择器'
                  },
                  {
                    value: 'date-picker',
                    label: 'DatePicker 日期选择器'
                  },
                  {
                    value: 'datetime-picker',
                    label: 'DateTimePicker 日期时间选择器'
                  },
                  {
                    value: 'upload',
                    label: 'Upload 上传'
                  },
                  {
                    value: 'rate',
                    label: 'Rate 评分'
                  },
                  {
                    value: 'form',
                    label: 'Form 表单'
                  }
                ]
              },
              {
                value: 'data',
                label: 'Data',
                children: [
                  {
                    value: 'table',
                    label: 'Table 表格'
                  },
                  {
                    value: 'tag',
                    label: 'Tag 标签'
                  },
                  {
                    value: 'progress',
                    label: 'Progress 进度条'
                  },
                  {
                    value: 'tree',
                    label: 'Tree 树形控件'
                  },
                  {
                    value: 'pagination',
                    label: 'Pagination 分页'
                  },
                  {
                    value: 'badge',
                    label: 'Badge 标记'
                  }
                ]
              },
              {
                value: 'notice',
                label: 'Notice',
                children: [
                  {
                    value: 'alert',
                    label: 'Alert 警告'
                  },
                  {
                    value: 'loading',
                    label: 'Loading 加载'
                  },
                  {
                    value: 'message',
                    label: 'Message 消息提示'
                  },
                  {
                    value: 'message-box',
                    label: 'MessageBox 弹框'
                  },
                  {
                    value: 'notification',
                    label: 'Notification 通知'
                  }
                ]
              },
              {
                value: 'navigation',
                label: 'Navigation',
                children: [
                  {
                    value: 'menu',
                    label: 'NavMenu 导航菜单'
                  },
                  {
                    value: 'tabs',
                    label: 'Tabs 标签页'
                  },
                  {
                    value: 'breadcrumb',
                    label: 'Breadcrumb 面包屑'
                  },
                  {
                    value: 'dropdown',
                    label: 'Dropdown 下拉菜单'
                  },
                  {
                    value: 'steps',
                    label: 'Steps 步骤条'
                  }
                ]
              },
              {
                value: 'others',
                label: 'Others',
                children: [
                  {
                    value: 'dialog',
                    label: 'Dialog 对话框'
                  },
                  {
                    value: 'tooltip',
                    label: 'Tooltip 文字提示'
                  },
                  {
                    value: 'popover',
                    label: 'Popover 弹出框'
                  },
                  {
                    value: 'card',
                    label: 'Card 卡片'
                  },
                  {
                    value: 'carousel',
                    label: 'Carousel 走马灯'
                  },
                  {
                    value: 'collapse',
                    label: 'Collapse 折叠面板'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ]
      }
    },
    methods: {
      onPreview() {
        this.$refs.preview.clickHandler()
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
