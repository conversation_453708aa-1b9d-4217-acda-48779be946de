<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="注册登录" name="first">详见 0301_功能设置_注册登录.vue</el-tab-pane>
      <el-tab-pane label="集体报名" name="second">详见 0303_功能设置_集体报名.vue</el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="third">详见 0304_功能设置_增值税发票.vue</el-tab-pane>
      <el-tab-pane label="培训证明" name="fourth">详见 0305_功能设置_培训证明.vue</el-tab-pane>
      <el-tab-pane label="视频播放设置" name="five">详见 0306_功能设置_视频播放设置.vue</el-tab-pane>
      <el-tab-pane label="门户精品课程" name="six">详见 0307_功能设置_门户精品课程.vue</el-tab-pane>
      <el-tab-pane label="培训监管管理" name="seven">
        <div class="f-p15">
          <el-tabs v-model="activeName2" type="card" class="m-tab-card">
            <el-tab-pane label="基础配置" name="first">
              <el-button type="primary" size="medium" class="btn-log">配置日志</el-button>
              <el-card shadow="never" class="m-card is-header">
                <div class="m-tit is-border-bottom">
                  <span class="tit-txt">监管功能基础配置</span>
                </div>
                <el-row type="flex" justify="center" class="width-limit f-mt20">
                  <el-col :md="20" :lg="16" :xl="13">
                    <el-form ref="form" :model="form" label-width="220px" class="m-form">
                      <el-form-item label="监管功能是否开启：">
                        <el-switch v-model="switch1"></el-switch>
                        <div class="f-ci">开启后监管规则生效，请确认监管规则配置情况</div>
                        <!--                        <div class="f-ci">关闭后仍可配置监管规则，但不生效</div>-->
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
                <div class="m-tit is-border-bottom">
                  <span class="tit-txt">人脸识别基础配置</span>
                </div>
                <el-row type="flex" justify="center" class="width-limit f-mt20">
                  <el-col :md="20" :lg="16" :xl="13">
                    <el-form ref="form" :model="form" label-width="220px" class="m-form">
                      <el-form-item label="人脸识别功能是否开启：">
                        <el-switch v-model="switch1"></el-switch>
                        <div class="f-ci">开启后监管规则可选择“人脸识别”作为监管方式，请确认监管规则是否配置</div>
                      </el-form-item>
                      <el-form-item label="基准照片数：">1</el-form-item>
                      <el-form-item label="基准照片更新次数：">
                        <el-input v-model="input" placeholder="3" class="f-input-num f-mr5"></el-input>
                        次
                      </el-form-item>
                      <el-form-item label="监控协议：" required>
                        <el-input type="textarea" :rows="10" placeholder="请输入内容" v-model="textarea"> </el-input>
                      </el-form-item>
                      <el-form-item label="匹配相似度：">
                        <el-input v-model="input" placeholder="50" class="f-input-num f-mr5"></el-input>
                        %<span class="f-ci f-fl5">（注：匹配系数为1%-100%，系数越大相似度就越高。）</span>
                      </el-form-item>
                      <el-form-item label="比对过程是否开启活体检测：">
                        <el-switch v-model="switch1"></el-switch>
                        <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                          <i class="el-icon-question m-tooltip-icon f-co f-ml10"></i>
                          <div slot="content">
                            <p>说明：</p>
                            <p>1、默认全平台所有采集环节开启活体检测</p>
                            <p>
                              2、全平台比对过程的活体检测可统一设置为开启或关闭，比对环节包含登录、学习、考试，是否开启活体检测仅对设置为开启监管规则的环节生效。
                            </p>
                            <p>3、适用于具体配置的培训方案，若系统也有配置检测规则，则以培训方案的配置为准</p>
                          </div>
                        </el-tooltip>
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-card>
              <div class="m-btn-bar f-tc is-sticky-1">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </div>
            </el-tab-pane>
            <el-tab-pane label="监管规则" name="second">
              <el-card shadow="never" class="m-card">
                <div class="m-tit is-border-bottom">
                  <span class="tit-txt">网校级监管规则</span>
                </div>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column label="进入学习" min-width="150">
                    <template>人脸识别</template>
                  </el-table-column>
                  <el-table-column label="学习过程" min-width="250">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        人脸识别/课件知识点答题
                        <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                          <i class="el-icon-info m-tooltip-icon f-co f-ml5"></i>
                          <div slot="content">
                            <p>X分钟监管1次</p>
                            <p>可重试无限次</p>
                            <p>监管不通过进度无</p>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        人脸识别/课件知识点答题
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="状态" min-width="100">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="info" class="badge-status">停用</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="160" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-button type="text">编辑</el-button>
                        <el-button type="text">停用</el-button>
                      </div>
                      <div v-else>
                        <el-button type="text">编辑</el-button>
                        <el-button type="text" disabled>停用</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
                <div class="m-tit is-border-bottom">
                  <span class="tit-txt">方案级监管规则</span>
                </div>
                <div class="f-p15">
                  <el-button type="primary">新建方案级监管</el-button>
                </div>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column label="NO." type="index" width="80" align="center" fixed="left"></el-table-column>
                  <el-table-column label="监管规则名称" min-width="150">
                    <template>这是监管规则名称（<span class="f-cb f-csp f-fb">3</span>）</template>
                  </el-table-column>
                  <el-table-column label="进入学习" min-width="150">
                    <template>人脸识别</template>
                  </el-table-column>
                  <el-table-column label="学习过程" min-width="250">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        人脸识别/课件知识点答题
                        <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                          <i class="el-icon-info m-tooltip-icon f-co f-ml5"></i>
                          <div slot="content">
                            <p>X分钟监管1次</p>
                            <p>可重试无限次</p>
                            <p>监管不通过进度无</p>
                          </div>
                        </el-tooltip>
                      </div>
                      <div v-else>
                        人脸识别/课件知识点答题
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="状态" min-width="100">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="info" class="badge-status">停用</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="180" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-button type="text">编辑</el-button>
                        <el-button type="text">删除</el-button>
                        <el-button type="text">停用</el-button>
                      </div>
                      <div v-else>
                        <el-button type="text">编辑</el-button>
                        <el-button type="text">删除</el-button>
                        <el-button type="text" disabled>停用</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'seven',
        activeName1: 'first',
        activeName2: 'first',
        radio: 3,
        radio1: 6,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
