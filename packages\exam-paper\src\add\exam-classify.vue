<template>
  <div v-loading="!show">
    <el-cascader
      v-model="select"
      :props="props"
      style="width: 100%"
      :options="categoryCache"
      :popper-class="'hb-cascader-exam'"
      placeholder="请选择试卷分类"
      v-bind="$attrs"
      v-if="show"
    >
    </el-cascader>
  </div>
</template>
<script lang="ts">
  import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { UiPage, Query } from '@hbfe/common'
  import LibraryResponseVo from '@api/service/management/resource/question-library/query/vo/LibraryResponseVo'
  import { isEmpty } from '@hbfe/jxjy-admin-common/src/util/util'
  import PaperPublishCategoryResponseVo from '@api/service/management/resource/exam-paper-category/query/vo/PaperPublishCategoryResponseVo'
  class NewLibraryResponseVo extends LibraryResponseVo {
    children? = new Array<any>()
    leaf = false
  }
  class NewPaperPublishCategoryResponseVo extends PaperPublishCategoryResponseVo {
    leaf = false
    children? = new Array<any>()
  }
  @Component
  export default class extends Vue {
    page: UiPage
    query: Query = new Query()
    libraryResponseVo = new LibraryResponseVo()

    @Prop({
      type: Boolean,
      default: false
    })
    multiple: boolean // 是否多选

    // 双向绑定
    @Prop({
      type: Array,
      default: () => {
        return [] as string[]
      }
    })
    value: string[]

    //判断是否更新
    @Prop({
      type: Boolean,
      default: false
    })
    isUpdate: boolean
    @Watch('isUpdate', {
      deep: true,
      immediate: true
    })
    isUpdateChange(val: boolean) {
      if (val) {
        // this.$emit('input', val[0])
        this.getFullPath()
      }
    }

    show = true // 组件显示 回显用

    select: string[] = [] as string[]

    // 存储所有题库数据
    // categoryCache = new Array<LibraryResponseVo>()

    categoryCache = new Array<PaperPublishCategoryResponseVo>()

    // 查询题库实例
    questionLibrary = ResourceModule.queryQuestionLibraryFactory.queryQuestionLibraryMultiton
    queryExamPaperCategory = ResourceModule.queryExamPaperCategoryFactory
    lazyId = '-1'
    //级联组件所需要传参
    props = {
      multiple: false,
      value: 'id',
      label: 'name'
    }

    @Emit('input')
    @Watch('select', {
      deep: true,
      immediate: true
    })
    selectChange() {
      return this.select
    }

    questionDetail = ''

    @Watch('value', {
      immediate: false,
      deep: true
    })
    valueChange(val: Array<string>) {
      this.select = val
    }

    constructor() {
      super()
      this.page = new UiPage(this.lazyLoad, this.lazyLoad)
    }
    async getFullPath() {
      console.log(this.value, 'this.valuevaluevaluevalue')
      const res =
        await ResourceModule.queryQuestionLibraryFactory.queryQuestionLibraryMultiton.queryQuestionBankLibraryDetail(
          this.value[0] || ''
        )
      console.log(res, '1231232123123123123123123123123123')
    }

    async created() {
      // 查询
      await this.lazyLoad()
    }

    async lazyLoad() {
      this.show = false

      const res = await this.queryExamPaperCategory.queryQueryExamCategory.queryExamPaperCategoryList(
        this.page,
        this.lazyId
      )
      // this.categoryCache = res.data
      if (!res?.status?.isSuccess()) {
        return
      }
      // 全部题库数据
      const arr = cloneDeep(res?.data)
      arr?.map((item) => {
        //假数据  根节点parenId为undefined
        if (item.parentCategoryId == undefined) {
          item.parentCategoryId = '-1'
        }
      })

      this.categoryCache = this.recursion('-1', arr)
      // this.categoryCache &&
      //   this.categoryCache.forEach(p => {
      //     this.lazyId = p.id
      //     this.lazyLoad()
      //   })
      this.show = true
    }
    // 递归
    recursion(id: string, arr: Array<PaperPublishCategoryResponseVo>) {
      const parentList = new Array<NewPaperPublishCategoryResponseVo>()
      const childrenList = new Array<PaperPublishCategoryResponseVo>()
      console.log(arr, '试卷递归的数组')

      arr?.forEach((item: PaperPublishCategoryResponseVo) => {
        if (item.parentCategoryId === id) {
          const temp = new NewPaperPublishCategoryResponseVo()
          Object.assign(temp, item)
          temp.children = new Array<any>()
          temp.leaf = false
          parentList.push(temp)
        } else {
          childrenList.push(item)
        }
        // this.lazyId = item.id
        // this.lazyLoad()
      })
      console.log(parentList, 'parentList---------------1')
      console.log(childrenList, 'childrenList=----------1')
      parentList?.forEach((sub: NewPaperPublishCategoryResponseVo) => {
        const res = this.recursion(sub.id, childrenList)
        if (isEmpty(res)) {
          sub.leaf = true
          delete sub.children
        } else {
          sub.children = this.recursion(sub.id, childrenList)
        }
      })

      return parentList
    }
  }
</script>
<style>
  /*css方法：使用css将其他的选项框给干掉。*/
  /*以下样式将单选框隐藏 除了第三个单选框不隐藏*/
  .hb-cascader-exam .el-cascader-panel .el-scrollbar:first-of-type .el-checkbox {
    display: none;
  }
  .hb-cascader-exam .el-cascader-panel .el-scrollbar:nth-of-type(2) .el-checkbox {
    display: none;
  }
</style>
