import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-fjzj-student-learning'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class FJZJApplyStudentLearningTokenRequest {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 学习课程所属类别
1：专业课，2：公需课
   */
  learnType: number
}

export class FJZJValidAllowToLearningRequest {
  qualificationId: string
  /**
   * 学习课程所属类别
1：专业课，2：公需课
   */
  learnType: number
}

export class FJZJApplyStudentLearningTokenResponse {
  token: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请学习
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyStudentLearningToken(
    request: FJZJApplyStudentLearningTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applyStudentLearningToken,
    operation?: string
  ): Promise<Response<FJZJApplyStudentLearningTokenResponse>> {
    return commonRequestApi<FJZJApplyStudentLearningTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 验证是否允许学习
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validAllowToLearning(
    request: FJZJValidAllowToLearningRequest,
    mutate: DocumentNode = GraphqlImporter.validAllowToLearning,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
