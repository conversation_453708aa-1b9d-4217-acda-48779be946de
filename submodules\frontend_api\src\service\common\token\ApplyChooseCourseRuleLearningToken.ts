/*
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024-06-18 15:55:07
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-07-03 09:13:15
 * @Description:
 */
import MsChooseCourseLearningSceneV1, {
  StudentCourseLearningTokenResponse
} from '@api/ms-gateway/ms-choosecourselearningscene-v1'
import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import { Response, ResponseStatus } from '@hbfe/common'

/**
 * 学习课程token
 */
class ApplyChooseCourseRuleLearningToken extends AbstractApplyToken {
  private readonly applyStudentLearningToken: string
  private readonly courseId: string

  /**
   * 申请课程学习token返回值
   */
  response = new Response<StudentCourseLearningTokenResponse>()

  constructor(courseId: string, applyStudentLearningToken: string) {
    super()
    this.applyStudentLearningToken = applyStudentLearningToken
    this.courseId = courseId
  }

  async apply(): Promise<ResponseStatus> {
    // 学习方案场景申请用户 token
    const tokenValue = await MsChooseCourseLearningSceneV1.applyCourseLearning({
      studentLearningToken: this.applyStudentLearningToken,
      studentCourseId: this.courseId
    })
    this.response = tokenValue
    if (!tokenValue.data.token) {
      return Promise.reject(
        new ResponseStatus(parseInt(tokenValue.data.applyResult.code), tokenValue.data.applyResult.message)
      )
    }
    this.token = tokenValue.data.token
    return new ResponseStatus(
      tokenValue.status.code,
      (tokenValue.status.code != 200 && tokenValue?.status?.getMessage()) || null
    )
  }
}

export default ApplyChooseCourseRuleLearningToken
