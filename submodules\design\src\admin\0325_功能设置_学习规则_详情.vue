<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">学习规则</el-breadcrumb-item>
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header">
        <div slot="header" class="">
          <span class="tit-txt">基础设置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-pt30">
              <el-form-item label="适用行业：" class="is-text">建设行业</el-form-item>
              <el-form-item label="适用范围：">
                地区级别
                <el-table :data="tableData" max-height="500px" class="m-table f-mt5" border>
                  <el-table-column type="index" label="No." width="60"></el-table-column>
                  <el-table-column label="省份" min-width="100">
                    <template>福建省</template>
                  </el-table-column>
                  <el-table-column label="地市" min-width="100">
                    <template>福州市</template>
                  </el-table-column>
                  <el-table-column label="区县" min-width="240">
                    <template>
                      鼓楼区、马尾区、XX区
                    </template>
                  </el-table-column>
                </el-table>
                培训方案级别
                <el-table :data="tableData" max-height="500px" class="m-table f-mt5" border>
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="培训方案名称" min-width="220">
                    <template>任务名称任务名称任务名称</template>
                  </el-table-column>
                  <el-table-column label="方案属性" min-width="240">
                    <template>
                      <div>行业：建设行业</div>
                      <div>地区：福建省/福州市/鼓楼区</div>
                      <div>科目类型：科目类型1</div>
                      <div>培训年度：2023年</div>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
              <el-form-item label="指定培训年度学习时间：">
                <div class="f-mb5">
                  <el-tag size="small" class="f-mr20 f-vm">年度：2024</el-tag>
                  <span class="f-mr20">2022-01-01 至 2022-12-31</span>
                </div>
                <div class="f-mb5">
                  <el-tag size="small" class="f-mr20 f-vm">年度：2023</el-tag>
                  <span class="f-mr20">2022-01-01 至 2022-12-31</span>
                </div>
                <div class="f-mb5">
                  <el-tag size="small" class="f-mr20 f-vm">年度：2022</el-tag>
                  <span class="f-mr20">2022-01-01 至 2022-12-31</span>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mt15">
        <div slot="header" class="">
          <span class="tit-txt">规则设置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <p class="f-mtb30">
              <i class="f-fb f-mr10">学员同一个天内报名多个培训班时，是否需要错开开始学习日期：</i>需要错开学习开始日期
            </p>
          </el-col>
        </el-row>

        <!--课程学习规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">课程学习规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
              <el-form-item label="每天学习时间：" class="is-text">08：00 ~ 20:00</el-form-item>
              <el-form-item label="每天不学习时间：" class="is-text">
                <p>12：00 ~ 14:00</p>
                <p>18：00 ~ 20:00</p>
              </el-form-item>
              <el-form-item label="首次开始学习时间：" class="is-text">
                开通班级的
                <i class="f-mlr5 f-cb">8</i> ~ <i class="f-mlr5 f-cb">18</i>
                天内随机开始学习。
              </el-form-item>
              <el-form-item label="每天最多学习时长：" class="is-text">
                每天课程学习最多
                <i class="f-mlr5 f-cb">8</i>
                小时且每次学习时长达到 <i class="f-mlr5 f-cb">8</i>
                小时，随机休息 60~180 分钟。
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <!--测验规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">测验规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="f-mt30 f-flex f-align-center">
              <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
              <span class="f-flex-sub"
                >课程学习结束后进入对应的课程测验，测验开始时间和结束时间随机间隔分钟数15-60分钟。</span
              >
            </div>
          </el-col>
        </el-row>

        <!--考试规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">考试规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="f-mt30 f-flex f-align-center">
              <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
              <span class="f-flex-sub"
                >课程学习结束后进入考试，考试开始时间和结束时间随机间隔分钟数，最少间隔总考试时长的三分之一的时间。</span
              >
            </div>
            <el-alert type="info" :closable="false" class="m-alert f-mt10">
              例：考试总时长60分钟，开始和结束时间至少间隔20分钟。
            </el-alert>
          </el-col>
        </el-row>

        <!--特殊规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">特殊规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
              <el-form-item label=" ">
                若重新计算的学习合格时间还是超过学习区间，则开通时间随机提前
                <i class="f-mlr5 f-cb">8</i>
                <i class="f-mlr5">～</i>
                <i class="f-mlr5 f-cb">8</i>
                天。
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        checkList: ['开通年度与方案年度不一致'],
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
