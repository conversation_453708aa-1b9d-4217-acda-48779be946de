import BasicDataGateway, {
  TrainingPropertyQueryRequest,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { listIndustryPropertyChildByCategoryV2 } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage/graphql-importer'
import Basicdata from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import { IndustryIdEnum } from '@api/service/training-institution/online-school/enum/IndustryEnum'
export default new (class QuerySubject {
  /**
   * 学科列表
   */
  subjectList: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()

  /**
   * 学科Map key: 字典code
   */
  private subjectCodeMap: Map<number, TrainingPropertyResponse> = new Map<number, TrainingPropertyResponse>()

  /**
   * 查询对应行业下学科
   * @param gradePropertyId 学段属性id
   */
  async querySubjectByIndustry(gradePropertyId: string): Promise<Array<TrainingPropertyResponse>> {
    const propertyId = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustry.industryList.find(
      item => item.id == IndustryIdEnum.LS
    )?.propertyId
    const res = await BasicDataGateway.listALLIndustryPropertyRootByCategoryV2({
      industryId: gradePropertyId,
      industryPropertyId: propertyId,
      categoryCode: IndustryPropertyCodeEnum.DISCIPLINE
    })

    return res.data || new Array<TrainingPropertyResponse>()
  }

  /**
   * 查询对应行业下学科V2
   * @param industryPropertyId 行业属性id
   * @param gradePropertyId 学段属性id
   */
  async querySubjectByGradeIdV2(
    industryPropertyId: string,
    gradePropertyId: string
  ): Promise<Array<TrainingPropertyResponse>> {
    const request = new TrainingPropertyQueryRequest()
    request.industryPropertyId = industryPropertyId
    request.propertyId = gradePropertyId
    request.categoryCode = IndustryPropertyCodeEnum.DISCIPLINE
    const res = await BasicDataGateway.listIndustryPropertyChildByCategoryV2(request)
    res.data.map(item => {
      this.subjectCodeMap.set(item.code, item)
    })
    return res.data || new Array<TrainingPropertyResponse>()
  }

  /**
   * 查询对应行业下所有学科
   */
  async queryAllSubject(): Promise<Array<TrainingPropertyResponse>> {
    this.subjectList = new Array<TrainingPropertyResponse>()

    const res = await Basicdata.listBusinessDataDictionaryInSubProject({
      businessDataDictionaryType: IndustryPropertyCodeEnum.DISCIPLINE
    })
    if (res.data?.length) {
      this.subjectList = res.data.map(item => {
        const temp = new TrainingPropertyResponse()
        temp.propertyId = item.id
        temp.parentId = item.parentId
        temp.name = item.name
        temp.code = item.code
        temp.sort = item.sort
        return temp
      })
      this.subjectList.map(item => {
        this.subjectCodeMap.set(item.code, item)
      })
    }
    return this.subjectList
  }

  /**
   * 批量查询对应学段下的学科
   * @param industryPropertyId 行业属性id
   * @param gradePropertyIds 学段属性id数组
   */
  async batchQueryPositionCategoryByGradeId(
    industryPropertyId: string,
    gradePropertyIds: Array<string>
  ): Promise<Array<TrainingPropertyResponse>> {
    const requestList = new Array<TrainingPropertyQueryRequest>()
    gradePropertyIds.map(item => {
      const req = new TrainingPropertyQueryRequest()

      req.industryPropertyId = industryPropertyId
      req.propertyId = item
      req.categoryCode = IndustryPropertyCodeEnum.DISCIPLINE

      requestList.push(req)
    })

    const reWriteGQL = new RewriteGraph<TrainingPropertyResponse, TrainingPropertyQueryRequest>(
      BasicDataGateway._commonQuery,
      listIndustryPropertyChildByCategoryV2
    )
    await reWriteGQL.request(requestList)

    const result = new Array<TrainingPropertyResponse>()

    for (const value of reWriteGQL.itemMap.values()) {
      result.push(value)
    }

    return result
  }
  /**
   * 通过code获取详情
   * @param code 字典code
   */
  getSubjectCodeDetail(code: number): TrainingPropertyResponse {
    return this.subjectCodeMap.get(code)
  }
})()
