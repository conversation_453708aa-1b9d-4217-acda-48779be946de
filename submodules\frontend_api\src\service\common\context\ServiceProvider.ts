import ServiceTypeEnum from './models/ServiceTypeEnum'

class ServiceProvider {
  /*
   单位id
   */
  unitId: string

  /**
   * 服务商类型
   * @see ServiceTypeEnum
   */
  serviceType: ServiceTypeEnum

  /*
   服务商名称
   */
  serviceName: string

  /*
   服务商 id
   */
  serviceId: string

  equal(serviceType: ServiceTypeEnum) {
    return this.serviceType === serviceType
  }

  // 判断是否是培训结构
  isUnit() {
    return this.equal(ServiceTypeEnum.TRAINING_INSTITUTION)
  }

  // 是否是课件供应商
  isCourseSupplier() {
    return this.equal(ServiceTypeEnum.COURSEWARE_SUPPLIER)
  }

  // 判断否渠道商
  isChannelVendor() {
    return this.equal(ServiceTypeEnum.CHANNEL_VENDOR)
  }

  // 判断是否是参训单位
  isParticipatingUnit() {
    return this.equal(ServiceTypeEnum.PARTICIPATING_UNIT)
  }
}

export default ServiceProvider
