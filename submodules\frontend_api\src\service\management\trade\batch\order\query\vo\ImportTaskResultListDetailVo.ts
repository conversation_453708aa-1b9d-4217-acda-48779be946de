import { AsyncTaskExecStatusEnum } from '@api/service/common/enums/async-task/AsyncTaskExecStatusList'
import { AsyncTaskProcessResultListEnum } from '@api/service/common/enums/async-task/AsyncTaskProcessResultList'
import { TaskExecuteResponse } from '@api/ms-gateway/ms-collectivesign-v1'

/**
 * @description 导入任务结果列表详情
 */
class ImportTaskResultListDetailVo {
  /**
   * 任务名称
   */
  taskName = ''

  /**
   * 任务开始时间
   */
  taskBeginTime = ''

  /**
   * 任务结束时间
   */
  taskEndTime = ''

  /**
   * 任务执行状态 1：已执行 2：正在执行 3：执行失败
   */
  taskExecStatus: AsyncTaskExecStatusEnum = null

  /**
   * 任务处理结果 1：未知 2：成功 3：失败
   */
  taskProcessResult: AsyncTaskProcessResultListEnum = null

  /**
   * 处理总条数
   */
  totalAmount: number = null

  /**
   * 成功总条数
   */
  successAmount: number = null

  /**
   * 失败总条数
   */
  failAmount: number = null

  /**
   * 日志信息
   */
  logMessage = ''

  /**
   * 失败数据Excel下载路径
   */
  failExcelDownloadUrl = ''

  static from(response: TaskExecuteResponse): ImportTaskResultListDetailVo {
    const detail = new ImportTaskResultListDetailVo()
    detail.taskName = response.name ?? ''
    detail.taskBeginTime = response.createdTime ?? ''
    detail.taskEndTime = response.alreadyTime ?? ''
    detail.taskExecStatus = ImportTaskResultListDetailVo.getExecStatus(response)
    detail.taskProcessResult = ImportTaskResultListDetailVo.getProcessResult(detail.taskExecStatus)
    detail.totalAmount = response.totalCount ?? 0
    detail.successAmount = response.successCount ?? 0
    detail.failAmount = response.failCount ?? 0
    detail.logMessage = ImportTaskResultListDetailVo.getLogMessage(detail.taskExecStatus)
    // TODO failExcelDownloadUrl 0.0.4版本暂时不做
    return detail
  }

  /**
   * 获取执行状态
   */
  static getExecStatus(response: TaskExecuteResponse): AsyncTaskExecStatusEnum {
    const execStatus = response.state ?? undefined
    // 正在执行
    if (execStatus === 0) {
      return AsyncTaskExecStatusEnum.In_Exec
    }
    // 已执行
    if (execStatus === 1) {
      return AsyncTaskExecStatusEnum.Has_Execed
    }
    return null
  }

  /**
   * 获取处理结果
   */
  static getProcessResult(execStatus: AsyncTaskExecStatusEnum): AsyncTaskProcessResultListEnum {
    // 未知
    if (!execStatus || execStatus === AsyncTaskExecStatusEnum.In_Exec) {
      return AsyncTaskProcessResultListEnum.Unknown
    }
    // 成功
    if (execStatus === AsyncTaskExecStatusEnum.Has_Execed) {
      return AsyncTaskProcessResultListEnum.Success
    }
    // 失败
    if (execStatus === AsyncTaskExecStatusEnum.Fail_Exced) {
      return AsyncTaskProcessResultListEnum.Fail
    }
    return null
  }

  /**
   * 获取日志信息
   */
  static getLogMessage(status: AsyncTaskExecStatusEnum): string {
    let log = ''
    if (status === AsyncTaskExecStatusEnum.In_Exec) {
      log = '任务处理中'
      return log
    }
    if (status === AsyncTaskExecStatusEnum.Has_Execed) {
      log = '任务执行成功'
    }
    if (status === AsyncTaskExecStatusEnum.Fail_Exced) {
      log = '任务执行失败'
    }
    return log
  }
}

export default ImportTaskResultListDetailVo
