<template>
  <el-card shadow="never" class="m-card f-mb15 is-header-sticky">
    <el-collapse v-model="activeNames" accordion>
      <el-collapse-item name="1" class="m-collapse-item">
        <div slot="title" class="f-flex f-align-center f-flex-sub">
          <span class="tit-txt f-flex-sub">增值服务</span>
          <a class="f-link f-cb f-mr10 f-f14 f-fn" @click.stop="handleOpenDrawer">增值服务说明</a>
        </div>
        <div class="f-plr20 f-pt40">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="addForm" :model="addData" label-width="auto" class="m-form f-mt30">
                <el-form-item label="学习规则：">
                  <el-switch
                    v-model="isLearningRule"
                    active-text="开启"
                    inactive-text="关闭"
                    class="m-switch"
                    @change="changeLearningRule"
                  />
                </el-form-item>
                <el-form-item label="智能学习：">
                  <el-switch
                    v-model="isSmartLearning"
                    active-text="开启"
                    inactive-text="关闭"
                    class="m-switch"
                    @change="changeSmartLearning"
                  />
                </el-form-item>
                <el-form-item label="分销服务：">
                  <el-switch
                    v-model="isDistributionService"
                    active-text="开启"
                    inactive-text="关闭"
                    class="m-switch"
                    @change="changeDistributionService"
                  />
                  <template v-if="isDistributionService">
                    <div class="f-ci f-mt10">请选择开通的分销服务类型：</div>
                    <div>
                      <el-radio
                        v-model="addData.distributionServiceType"
                        :label="DistributionServiceTypeEnum.basic"
                        class="f-mr30"
                        >基础版</el-radio
                      >
                      <el-radio
                        v-model="addData.distributionServiceType"
                        :label="DistributionServiceTypeEnum.professional"
                        >专业版</el-radio
                      >
                    </div>
                  </template>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-collapse-item>
    </el-collapse>
    <add-service-drawer ref="AddServiceDrawer"></add-service-drawer>
  </el-card>
</template>
<script lang="ts">
  import AddServiceModel from '@api/service/training-institution/online-school/base-models/AddServiceModel'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import AddServiceDrawer from '@hbfe/jxjy-admin-registerSchool/src/components/add-service-info.vue'
  import { AddServiceEnum } from '@api/service/training-institution/online-school/enum/AddServiceEnum'
  import { DistributionServiceTypeEnum } from '@api/service/common/capability-service-config/enum/DistributionServiceTypeEnum'
  @Component({
    components: { AddServiceDrawer }
  })
  export default class extends Vue {
    @Ref('addForm') addForm: any
    @Ref('AddServiceDrawer') AddServiceDrawer: AddServiceDrawer
    activeNames: Array<string> = ['1']
    addData: AddServiceModel = new AddServiceModel()
    isLearningRule = false
    isSmartLearning = false
    isDistributionService = false
    DistributionServiceTypeEnum = DistributionServiceTypeEnum
    async created() {
      this.$emit('childrenThis', this, 5)
    }
    handleCheck() {
      return new Promise((resolve) => {
        let result = {
          status: true,
          msg: '',
          data: {}
        }
        this.addForm.validate((valid: any) => {
          if (valid) {
            result.data = this.addData
          } else {
            result = {
              status: false,
              msg: '增值服务未填写完整，请检查！',
              data: {}
            }
            return false
          }
        })
        resolve(result)
      })
    }
    handleOpenDrawer() {
      this.AddServiceDrawer.dialog = true
    }
    changeLearningRule() {
      if (this.isLearningRule) {
        this.addData.addServiceType.push(AddServiceEnum.learningRule)
      } else {
        this.addData.addServiceType.splice(this.addData.addServiceType.indexOf(AddServiceEnum.learningRule), 1)
      }
    }
    changeSmartLearning() {
      if (this.isSmartLearning) {
        this.addData.addServiceType.push(AddServiceEnum.intelligentlearning)
      } else {
        this.addData.addServiceType.splice(this.addData.addServiceType.indexOf(AddServiceEnum.intelligentlearning), 1)
      }
    }
    changeDistributionService() {
      if (this.isDistributionService) {
        this.addData.addServiceType.push(AddServiceEnum.fxService)
      } else {
        this.addData.addServiceType.splice(this.addData.addServiceType.indexOf(AddServiceEnum.fxService), 1)
      }
    }
  }
</script>
<style lang="scss" scoped>
  .f-link {
    cursor: pointer;
    font-size: 14px;
    margin-right: 10px;
    font-weight: 500;
  }
  // .f-cb {
  //   color: lighten(#1f86f0, 15%);

  //   &:hover {
  //     color: lighten(#1f86f0, 15%);
  //   }
  // }
  .tit-txt {
    flex: 1;
    // min-width: 0;
    // font-weight: bold;
    display: flex;
    // align-items: center;
  }
</style>
