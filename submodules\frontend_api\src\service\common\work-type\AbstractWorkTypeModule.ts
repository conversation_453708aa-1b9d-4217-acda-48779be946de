import { VuexModule } from 'vuex-module-decorators'
import WorkType from '@api/service/common/models/work-type/WorkType'
import Response from '@api/Response'

abstract class AbstractWorkTypeModule<T extends WorkType> extends VuexModule {
  protected cache: { [key: string]: T } = {}

  abstract getWorkType(id: string): Promise<Response<T>>

  abstract getWorkTypeListByIds(id: Array<string>): Promise<Response<Map<string, T>>>

  abstract get getExistsWorkTypeById(): (id: string) => T
}

export default AbstractWorkTypeModule
