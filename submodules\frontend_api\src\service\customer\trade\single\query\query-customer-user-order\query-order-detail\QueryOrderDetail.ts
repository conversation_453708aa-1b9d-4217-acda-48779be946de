// import { ResponseStatus } from '@api/Response'
import tradeQueryGateway from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
// import OrderDetail from '@api/service/customer/trade/query-action/query-customer-user-order/query-order-detail/models/OrderDetail'
// import commonError from '@api/helpers/decorator/error/common'
// import MutationTradeFacade from '@api/service/customer/trade/MutationTradeFacade'
// export { OrderDetail }
import OrderDetailVo from '@api/service/customer/trade/single/query/vo/OrderDetailVo'
import { Page, ResponseStatus } from '@hbfe/common'
import ThematiList from '@api/service/customer/thematic-config/ThematiList'
import ThematicMap from '@api/service/customer/thematic-config/ThematicMap'
import MsLearningQueryFrontGatewayCourseLearningForestage, {
  LearningRegisterRequest,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
/**
 * 查询订单信息
 */

class QueryOrderDetail {
  // // 订单号
  private orderNo = ''
  //
  // private _orderDetail = new OrderDetail()
  //
  orderDetail = new OrderDetailVo()
  constructor(orderNo: string) {
    this.orderNo = orderNo
  }
  //
  // // 获取订单详情
  // get orderDetail() {
  //   return this._orderDetail
  // }
  //
  // // 获取取消订单业务操作类
  // get getCancelOrderActionInstance() {
  //   return MutationTradeFacade.cancelOrderActionMultiton(this.orderNo)
  // }
  //
  // @commonError
  async queryOrderDetailByOrderNo(): Promise<ResponseStatus> {
    const res = await tradeQueryGateway.getOrderInMyself(this.orderNo)
    if (res.status.isSuccess()) {
      if (!res.data) return new ResponseStatus(30006, '获取订单详情异常，请刷新当前页面重新获取详情')
      const tmpItem = new OrderDetailVo()
      Object.assign(tmpItem, res.data)
      tmpItem.changeStatue()
      await tmpItem.addInvoice()
      await tmpItem.addRefundOrder()
      if (tmpItem.saleChannelId) {
        await ThematicMap.getThematicMap([tmpItem.saleChannelId])
        tmpItem.specialArea = ThematicMap.map.get(tmpItem.saleChannelId)?.region?.[0] ?? ''
        tmpItem.specialType = ThematicMap.map.get(tmpItem.saleChannelId)?.type || null
        tmpItem.specialUnit = ThematicMap.map.get(tmpItem.saleChannelId)?.unitName
      }
      this.orderDetail = tmpItem
    }
    return res.status
  }

  // 通过订单号获取参训资格id
  async getQualificationId(orderId: string) {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.orderNoList = [orderId]
    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      return res.data.currentPageData[0].qualificationId
    }
    return ''
  }
}

export default QueryOrderDetail
