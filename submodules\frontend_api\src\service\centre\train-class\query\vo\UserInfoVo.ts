/**
 * @description 用户信息
 */
class UserInfoVo {
  /**
   * 用户id
   */
  userId = ''

  /**
   * 用户名称
   */
  userName = ''

  /**
   * 用户身份证号
   */
  userAccount = ''

  /**
   * 用户手机号
   */
  userPhone = ''

  /**
   * 获取学员信息
   */
  static async getUserInfoListByIds(userIds: string[]): Promise<UserInfoVo[]> {
    // TODO
    const result = [] as UserInfoVo[]
    return result
  }
}

export default UserInfoVo
