import { Page } from '@hbfe/common'
import SupplierDistributorSalesStatisticsParams from '@api/service/management/statisticalReport/DistributorSalesStatistics/model/SupplierDistributorSalesStatisticsParams'
import SupplierDistributorSalesStatisticsInfo from '@api/service/management/statisticalReport/DistributorSalesStatistics/model/SupplierDistributorSalesStatisticsInfo'
import TotalStatic from '@api/service/management/statisticalReport/models/TotalStatic'
import FxnlQueryFront from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import FxnlDataExport from '@api/platform-gateway/fxnl-data-export-gateway-backstage'

/**
 * 供应商——分销商销售统计
 */
export default class SupplierDistributorSalesStatisticsData {
  /**
   * 查询参数
   */
  param = new SupplierDistributorSalesStatisticsParams()
  /**
   * 列表
   */
  list: SupplierDistributorSalesStatisticsInfo[] = []

  /**
   * 统计数据
   */
  staticData: TotalStatic = new TotalStatic()
  /**
   * 查询列表方法
   * 执行分销产品统计列表查询的逻辑
   */
  async queryList(page: Page) {
    const request = SupplierDistributorSalesStatisticsParams.toStatisticTradeRecordRequest(this.param)
    const response = await FxnlQueryFront.pageDistributorSellStatisticInSupplier({
      page: page,
      request: request
    })
    if (!response.status.isSuccess()) {
      console.error('获取分销商统计列表报错')
      return response
    }
    page.totalPageSize = response.data.totalPageSize
    page.totalSize = response.data.totalSize
    this.list = response.data.currentPageData.map(res =>
      SupplierDistributorSalesStatisticsInfo.fromDistributorSellStatisticIncludedPurchaseResponse(res)
    )
    return response
  }

  /**
   * 导出列表方法
   * 执行分销产品统计列表导出的逻辑
   */
  async exportList() {
    const request = SupplierDistributorSalesStatisticsParams.toStatisticTradeRecordRequestExport(this.param)
    return FxnlDataExport.exportDistributorSalesStatisticsExcelInSupplier(request)
  }
  /**
   * 查询数据统计
   */
  async queryTotalStatic() {
    const request = SupplierDistributorSalesStatisticsParams.toStatisticTradeRecordRequest(this.param)
    const response = await FxnlQueryFront.statisticTradeRecordInSupplier(request)
    if (!response.status.isSuccess()) {
      console.error('获取分销商销售统计数据报错——供应商', response)
      return response
    }
    this.staticData = TotalStatic.TradeStatisticResponseToTotalStatic(response.data)
  }
}
