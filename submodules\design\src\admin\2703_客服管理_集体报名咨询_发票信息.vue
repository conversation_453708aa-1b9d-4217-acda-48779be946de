<template>
  <el-main>
    <el-card shadow="never" class="m-card is-bg is-overflow-hidden">
      <div class="f-plr20 f-pt20">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="姓名">
                <el-input v-model="input" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="管理员帐号">
                <el-input v-model="input" clearable placeholder="请输入管理员帐号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="4" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--人员信息-->
        <el-collapse v-model="activeNames5" @change="handleChange" class="m-collapse no-border">
          <el-collapse-item name="1">
            <div slot="title" class="m-tit">
              <span class="tit-txt">人员信息</span>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="240" highlight-current-row class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="姓名" min-width="160" fixed="left">
                <template>张依依</template>
              </el-table-column>
              <el-table-column label="帐号" min-width="200">
                <template>354875965412365896</template>
              </el-table-column>
              <el-table-column label="注册时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-border-top is-sticky">
      <el-tab-pane label="帐号信息" name="first">
        <div class="f-p15">详见 2701_客服管理_集体报名咨询_帐号信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="批次信息" name="second">
        <div class="f-p15">详见 2702_客服管理_集体报名咨询_批次信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="发票信息" name="third">
        <div class="f-p15">
          <el-tabs v-model="activeName2" type="card" class="m-tab-card">
            <el-tab-pane label="增值税电子普通发票（自动开票）" name="first">
              <el-card shadow="never" class="m-card f-mb15">
                <!--条件查询-->
                <el-row :gutter="16" class="m-query">
                  <el-form :inline="true" label-width="auto">
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="集体报名批次号">
                        <el-input v-model="input" clearable placeholder="请输入集体报名批次号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="开票状态">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item>
                        <el-button type="primary">查询</el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="集体报名批次号" min-width="220" fixed="left">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        2112071509467489926
                        <p>
                          <el-tag type="primary" size="mini">拆票</el-tag>
                          <el-tag type="primary" size="small">分销推广</el-tag>
                        </p>
                      </div>
                      <div v-else>
                        2112071509467489926
                        <p>
                          <el-tag type="primary" size="small">分销推广</el-tag>
                        </p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="退款状态" min-width="130">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="primary" class="badge-status">退款审批中</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="danger" class="badge-status">退款失败</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 2">
                        <el-badge is-dot type="info" class="badge-status">退款取消</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">退款成功</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="付款金额(元)" width="140" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="开票金额(元)" width="140" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="税额(元)" width="140" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="购买人信息" min-width="240">
                    <template>
                      <p>姓名：张依依</p>
                      <p>证件号：354875965412365896</p>
                      <p>手机号：15847412365</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="发票抬头" min-width="300">
                    <template>【单位】福建华博教育教育科技股份有限公司</template>
                  </el-table-column>
                  <el-table-column label="统一社会信用代码" min-width="180">
                    <template>32514568758965542</template>
                  </el-table-column>
                  <el-table-column label="申请开票时间" min-width="180">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="发票状态" min-width="130">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="warning" class="badge-status">待开票</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="danger" class="badge-status">开票失败</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">开票成功</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="开票时间" min-width="180">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="电子票类型" min-width="180">
                    <template>电子发票（纸电票）</template>
                  </el-table-column>
                  <el-table-column label="发票号" min-width="120">
                    <template>26541122</template>
                  </el-table-column>
                  <el-table-column label="是否冻结" min-width="120">
                    <template>是</template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">修改发票信息</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="增值税电子普通发票（线下开票）" name="second">
              <el-card shadow="never" class="m-card f-mb15">
                <!--条件查询-->
                <el-row :gutter="16" class="m-query">
                  <el-form :inline="true" label-width="auto">
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="集体报名批次号">
                        <el-input v-model="input" clearable placeholder="请输入集体报名批次号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="开票状态">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item>
                        <el-button type="primary">查询</el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="集体报名批次号" min-width="220" fixed="left">
                    <template>
                      <p>2112071509467489926</p>
                      <p>
                        <el-tag type="primary" size="small">分销推广</el-tag>
                      </p>
                    </template>
                  </el-table-column>
                  <el-table-column label="退款状态" min-width="130">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="primary" class="badge-status">退款审批中</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="danger" class="badge-status">退款失败</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 2">
                        <el-badge is-dot type="info" class="badge-status">退款取消</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">退款成功</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="付款金额(元)" width="140" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="开票金额(元)" width="140" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="购买人信息" min-width="240">
                    <template>
                      <p>姓名：张依依</p>
                      <p>证件号：354875965412365896</p>
                      <p>手机号：15847412365</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="发票抬头" min-width="300">
                    <template>【单位】福建华博教育教育科技股份有限公司</template>
                  </el-table-column>
                  <el-table-column label="统一社会信用代码" min-width="180">
                    <template>32514568758965542</template>
                  </el-table-column>
                  <el-table-column label="申请开票时间" min-width="180">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="发票状态" min-width="130">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="warning" class="badge-status">待开票</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="danger" class="badge-status">开票失败</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">开票成功</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="开票时间" min-width="180">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="发票号" min-width="120">
                    <template>26541122</template>
                  </el-table-column>
                  <el-table-column label="手机号 / 邮箱" min-width="220">
                    <template>
                      13003831002 /
                      <p><EMAIL></p>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">修改发票信息</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="增值税专用发票" name="third">
              <el-card shadow="never" class="m-card f-mb15">
                <!--条件查询-->
                <el-row :gutter="16" class="m-query">
                  <el-form :inline="true" label-width="auto">
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="集体报名批次号">
                        <el-input v-model="input" clearable placeholder="请输入集体报名批次号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="开票状态">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item>
                        <el-button type="primary">查询</el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="集体报名批次号" min-width="220" fixed="left">
                    <template>
                      <p>2112071509467489926</p>
                      <p>
                        <el-tag type="primary" size="small">分销推广</el-tag>
                      </p>
                    </template>
                  </el-table-column>
                  <el-table-column label="退款状态" min-width="130">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="primary" class="badge-status">退款审批中</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="danger" class="badge-status">退款失败</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 2">
                        <el-badge is-dot type="info" class="badge-status">退款取消</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">退款成功</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="付款金额(元)" width="140" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="开票金额(元)" width="140" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="配送方式" width="120" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-popover placement="right" width="600" trigger="hover" popper-class="m-popover">
                          <el-tag type="success" class="f-mr5" effect="dark">已配送</el-tag>
                          <el-tag type="primary" class="f-mr5" effect="dark">已就绪</el-tag>
                          <el-tag type="info" class="f-mr5" effect="dark">未就绪</el-tag>
                          <el-form :inline="true" label-width="85px" class="m-text-form is-border-bottom f-mt15">
                            <el-col :span="24">
                              <el-form-item label="快递公司：">中国邮政</el-form-item>
                            </el-col>
                            <el-col :span="24">
                              <el-form-item label="运单号：">
                                1155194294875
                                <a href="#" class="f-cb f-link f-underline f-ml10">复制运单号并查询</a>
                              </el-form-item>
                            </el-col>
                            <el-col :span="24">
                              <el-form-item label="发货时间：">2020-02-27 14:22:32</el-form-item>
                            </el-col>
                          </el-form>
                          <el-form :inline="true" label-width="85px" class="m-text-form f-mt15">
                            <el-col :span="24">
                              <el-form-item label="收货地址：">
                                福建省 龙岩市 上杭县 蛟洋工业区瓮福紫金化工股份有限公司 364204
                              </el-form-item>
                            </el-col>
                            <el-col :span="24">
                              <el-form-item label="收货人：">罗成文</el-form-item>
                            </el-col>
                            <el-col :span="24">
                              <el-form-item label="手机号码：">18905973052</el-form-item>
                            </el-col>
                          </el-form>
                          <span slot="reference" class="f-link f-cb">邮寄</span>
                        </el-popover>
                      </div>
                      <div v-else>
                        <el-popover placement="right" width="600" trigger="hover" popper-class="m-popover">
                          <el-tag type="success" class="f-mr5" effect="dark">已自取</el-tag>
                          <el-tag type="info" class="f-mr5" effect="dark">未自取</el-tag>
                          <el-form :inline="true" label-width="85px" class="m-text-form is-border-bottom f-mt15">
                            <el-col :span="24">
                              <el-form-item label="领取人：">伍赠和</el-form-item>
                            </el-col>
                            <el-col :span="24">
                              <el-form-item label="手机号：">13859552508</el-form-item>
                            </el-col>
                            <el-col :span="24">
                              <el-form-item label="发货时间：">2020-02-27 14:22:32</el-form-item>
                            </el-col>
                          </el-form>
                          <el-form :inline="true" label-width="85px" class="m-text-form f-mt15">
                            <el-col :span="24">
                              <el-form-item label="收货人：">罗成文</el-form-item>
                            </el-col>
                            <el-col :span="24">
                              <el-form-item label="手机号码：">18905973052</el-form-item>
                            </el-col>
                            <el-col :span="24">
                              <el-form-item label="证件号：">350121199112025689</el-form-item>
                            </el-col>
                            <el-col :span="24">
                              <el-form-item label="自取地址：">福建省福州市鼓楼区工业路611号</el-form-item>
                            </el-col>
                          </el-form>
                          <span slot="reference" class="f-link f-cb">自取</span>
                        </el-popover>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="购买人信息" min-width="240">
                    <template>
                      <p>姓名：张依依</p>
                      <p>证件号：354875965412365896</p>
                      <p>手机号：15847412365</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="发票抬头" min-width="300">
                    <template>【单位】福建华博教育教育科技股份有限公司</template>
                  </el-table-column>
                  <el-table-column label="统一社会信用代码" min-width="180">
                    <template>32514568758965542</template>
                  </el-table-column>
                  <el-table-column label="增票信息" min-width="360">
                    <template>
                      <p class="f-flex">
                        <span>银行账户：</span>
                        <span class="f-flex-sub">13-560101040006007</span>
                      </p>
                      <p class="f-flex">
                        <span>开户银行：</span>
                        <span class="f-flex-sub">中国农业银行股份有限公司安溪县支行营业部</span>
                      </p>
                    </template>
                  </el-table-column>
                  <el-table-column label="申请开票时间" min-width="180">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="发票状态" min-width="130">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="warning" class="badge-status">待开票</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="danger" class="badge-status">开票失败</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">开票成功</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="开票时间" min-width="180">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="发票号" min-width="120">
                    <template>26541122</template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">修改发票信息</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="发票配送" name="fourth">
              <el-card shadow="never" class="m-card f-mb15">
                <!--条件查询-->
                <el-row :gutter="16" class="m-query">
                  <el-form :inline="true" label-width="auto">
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="配送状态">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="集体报名批次号">
                        <el-input v-model="input" clearable placeholder="请输入集体报名批次号" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="开票状态">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item>
                        <el-button type="primary">查询</el-button>
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="集体报名批次号" min-width="220" fixed="left">
                    <template>
                      <p>2112071509467489926</p>
                      <p>
                        <el-tag type="primary" size="small">分销推广</el-tag>
                      </p>
                    </template>
                  </el-table-column>
                  <el-table-column label="发票号" min-width="120">
                    <template>26541122</template>
                  </el-table-column>
                  <el-table-column label="收件信息" min-width="400">
                    <template>
                      <p class="f-flex">
                        <span>收货地址：</span>
                        <span class="f-flex-sub">福建省南平市光泽县创世达802号 963010</span>
                      </p>
                      <p>收货人：刘毅</p>
                      <p>手机号: 18760422526</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="配送方式" width="120">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">自取</div>
                      <div v-else>邮寄</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="配送状态" min-width="130">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="primary" class="badge-status">就绪</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="info" class="badge-status">未就绪</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="success" class="badge-status">已配送</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="配送信息 / 取件信息" min-width="260">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <p>领取人: 伍赠和</p>
                        <p>手机号: 13859552508</p>
                        <p>取货时间: 2020-03-03 11:41:06</p>
                      </div>
                      <div v-else>
                        <p>快递公司: 中国邮政</p>
                        <p>运单号: 1155194294875</p>
                        <p>发货时间: 2020-02-27 14:22:32</p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="是否冻结" min-width="120">
                    <template>否</template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-button type="text" size="mini">配送记录</el-button>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-button type="text" size="mini">确认自取</el-button>
                        <el-button type="text" size="mini">配送记录</el-button>
                      </div>
                      <div v-else-if="scope.$index === 2">
                        <el-button type="text" size="mini">确认配送</el-button>
                        <el-button type="text" size="mini">配送记录</el-button>
                      </div>
                      <div v-else-if="scope.$index === 3">
                        <el-button type="text" size="mini" disabled>已配送</el-button>
                        <el-button type="text" size="mini">配送记录</el-button>
                      </div>
                      <div v-else>
                        <el-button type="text" size="mini" disabled>已自取</el-button>
                        <el-button type="text" size="mini">配送记录</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      <el-tab-pane label="退款信息" name="fourth">
        <div class="f-p15">详见 2704_客服管理_集体报名咨询_退款信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="学员名单" name="five">
        <div class="f-p15">详见 2705_客服管理_集体报名咨询_学员名单.vue</div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames5: ['1'],
        activeName: 'third',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
