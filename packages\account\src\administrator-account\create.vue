<template>
  <el-main>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/basic-data/account/administrator-account' }">
        管理员号管理
      </el-breadcrumb-item>
      <el-breadcrumb-item>添加账号</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!--基本信息 -->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基本信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form
                ref="formRef"
                :rules="rules"
                :model="createSystemManager.createSubAdminParams"
                label-width="120px"
                class="m-form"
              >
                <el-form-item label="帐号：" prop="identity">
                  <el-input
                    v-model="createSystemManager.createSubAdminParams.identity"
                    clearable
                    placeholder="请输入帐号"
                    class="form-m"
                  />
                </el-form-item>
                <el-form-item label="姓名 / 昵称：" prop="name">
                  <el-input
                    v-model="createSystemManager.createSubAdminParams.name"
                    clearable
                    placeholder="请输入姓名 / 昵称"
                    class="createSystemManager.createSubAdminParams-m"
                  />
                </el-form-item>
                <el-form-item label="性别：" required>
                  <el-radio-group v-model="createSystemManager.createSubAdminParams.gender">
                    <el-radio :label="1">男</el-radio>
                    <el-radio :label="0">女</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="密码：" prop="password">
                  <el-input
                    v-model="createSystemManager.createSubAdminParams.password"
                    clearable
                    show-password
                    placeholder="请输入8~18位由数字、字母或符号组成的密码"
                    class="form-l"
                    @input="checkoutPasswordStrength"
                    auto-complete="new-password"
                  />
                  <!--密码安全判断-->
                  <div class="psw-tips form-l" v-if="passwordStrengthLow">
                    <el-progress :percentage="33.33" color="#e93737" :show-text="false"></el-progress>
                    <!--弱：txt-l，中：txt-m，强：txt-h-->
                    <span class="txt txt-l">弱</span>
                  </div>
                  <div class="psw-tips form-l" v-if="passwordStrengthIntermediate">
                    <el-progress :percentage="66.66" color="#ee9e2d" :show-text="false"></el-progress>
                    <!--弱：txt-l，中：txt-m，强：txt-h-->
                    <span class="txt txt-m">中</span>
                  </div>
                  <div class="psw-tips form-l" v-if="passwordStrengthHigh">
                    <el-progress :percentage="100" color="#49b042" :show-text="false"></el-progress>
                    <!--弱：txt-l，中：txt-m，强：txt-h-->
                    <span class="txt txt-h">强</span>
                  </div>
                </el-form-item>
                <el-form-item label="确认密码：" prop="certainPassword">
                  <el-input
                    v-model="form.certainPassword"
                    clearable
                    show-password
                    placeholder="请再次输入密码"
                    class="form-l"
                    auto-complete="new-password"
                  />
                </el-form-item>
                <el-form-item label="手机号：" prop="phone">
                  <el-input
                    v-model="createSystemManager.createSubAdminParams.phone"
                    clearable
                    placeholder="请输入手机号"
                    class="form-m"
                  />
                </el-form-item>
                <el-form-item label="邮箱：">
                  <el-input
                    v-model="createSystemManager.createSubAdminParams.email"
                    clearable
                    placeholder="请输入邮箱"
                    class="form-m"
                  />
                </el-form-item>
                <el-form-item label="启用状态：" required>
                  <el-radio-group v-model="createSystemManager.createSubAdminParams.status">
                    <el-radio :label="1">启用</el-radio>
                    <el-radio :label="2">禁用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <!--分配角色 -->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">分配角色</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="120px" class="m-form">
                <el-form-item required>
                  <el-button
                    type="primary"
                    icon="el-icon-plus"
                    class="f-mb20"
                    @click="openAddRoleDialog()"
                    :disabled="isLoading"
                    >添加角色
                  </el-button>
                  <!-- 角色列表 -->
                  <el-table stripe :data="roleList" max-height="500px" class="m-table" v-if="roleList.length">
                    <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                    <el-table-column prop="name" label="角色" width="280"></el-table-column>
                    <el-table-column prop="description" label="说明" align="center"></el-table-column>
                    <el-table-column label="操作" width="100" align="center" fixed="right">
                      <template slot-scope="scope">
                        <el-button type="text" size="mini" @click="closeRoleInfo(scope.row.id)">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <el-empty :image-size="40" description="暂无数据，请添加角色~" v-if="!roleList.length" />
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="goBack">放弃编辑</el-button>
        <el-button :loading="isLoading" type="primary" @click="save()">保存</el-button>
      </div>
    </div>
    <choose-list
      :value.sync="showRoleDialog"
      @input="input"
      @confirmDialog="confirmDialog"
      :roleList="roleList"
    ></choose-list>
  </el-main>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  import CreateSubAdminRequestVo from '@api/service/management/user/mutation/manager/system-manager/vo/CreateSubAdminRequestVo'
  import { UiPage } from '@hbfe/common'
  import chooseList from '@hbfe/jxjy-admin-account/src/administrator-account/__components__/choose-role.vue'

  class CreateSubAdminRequest extends CreateSubAdminRequestVo {
    certainPassword: string
  }

  import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
  import { bind, debounce } from 'lodash-decorators'

  class RoleInfoResponse extends RoleInfoResponseVo {
    isChecked: boolean
  }

  @Component({
    components: { chooseList }
  })
  export default class extends Vue {
    @Ref('formRef') formRef: any

    isLoading = false
    page: UiPage
    roleList: Array<RoleInfoResponse> = new Array<RoleInfoResponse>()
    rules = {
      identity: [{ required: true, message: '请选择登录账户', trigger: 'blur' }],
      name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      phone: [{ required: true, validator: this.validatePhone, trigger: 'blur' }],
      password: [{ required: true, validator: this.validatePassword, trigger: 'blur' }],
      certainPassword: [{ required: true, validator: this.validateAgainPassword, trigger: 'blur' }]
    }
    reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{8,18}$') // 验证密码的正则是否符合的正则
    regPhone = new RegExp(/^[1]([3-9])[0-9]{9}$/)
    form = new CreateSubAdminRequest()
    createSystemManager = UserModule.mutationUserFactory.createSystemManager
    // 密码强度低
    passwordStrengthLow = false
    // 密码强度中
    passwordStrengthIntermediate = false
    // 密码强度高
    passwordStrengthHigh = false
    //
    showRoleDialog = false

    created() {
      try {
        this.isLoading = true
        this.$nextTick(() => {
          this.formRef.resetFields()
          this.isLoading = false
        })
      } catch (e) {
        this.isLoading = false
      }
    }

    async save() {
      this.isLoading = true

      this.formRef.validate(async (boolean: boolean, value: object) => {
        if (boolean) {
          // // this.createSystemManager.createSubAdminParams = this.form
          if (this.roleList.length === 0) {
            this.$message.error('角色不能为空')
            this.isLoading = false
            return
          }
          this.createSystemManager.createSubAdminParams.roleIds = this.roleList.map((item) => {
            return item.id
          })

          try {
            const res = await this.createSystemManager.createOnlineSchoolSubAdminByToken()
            if (res.status.isSuccess()) {
              if (res?.data?.code === '200') {
                this.$message.success('发布成功')
                this.isLoading = false
                this.formRef.resetFields()
                await this.$router.push('/basic-data/account/administrator-account')
              } else {
                this.$message.error(res?.data?.message)
                this.isLoading = false
              }
            } else {
              this.$message.warning('创建失败')
              this.isLoading = false
            }
          } catch (e) {
            this.$message.warning('创建失败')
            this.isLoading = false
          }
        } else {
          this.isLoading = false
        }
      })
    }

    //密码强弱验证
    checkoutPasswordStrength() {
      const reg1 = /^.{1,8}$|^\d{9,}$|^[a-zA-Z]{9,}$|^(?=[\x21-\x7e]+)[^A-Za-z0-9]{9,}$/ //密码低强度正则--纯数字或字母或字符或长度1-8
      const reg2 = /^(?!\d+$)[a-zA-Z0-9]{9,}$|^(?![0-9]+$)[^a-zA-Z]{9,}$|^(?![a-zA-Z]+$)[^0-9]{9,}$/ //密码中强度正则--有两种且长度在9-12
      const reg3 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{13,}$/ //密码高强度正则--三种都有且长度大于12
      const reg4 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{9,12}$/ //密码中强度正则--介于9-12位的三种字符都有的密码
      if (!this.createSystemManager.createSubAdminParams.password) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = false
      } else if (reg1.test(this.createSystemManager.createSubAdminParams.password)) {
        this.passwordStrengthLow = true
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = false
      } else if (reg2.test(this.createSystemManager.createSubAdminParams.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = true
        this.passwordStrengthHigh = false
      } else if (reg3.test(this.createSystemManager.createSubAdminParams.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = true
      } else if (reg4.test(this.createSystemManager.createSubAdminParams.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = true
        this.passwordStrengthHigh = false
      }
    }

    //密码验证
    validatePassword(rule: any, value: any, callback: any) {
      if (!this.reg.test(this.createSystemManager.createSubAdminParams.password)) {
        callback(new Error('请输入8~18位由数字、字母或符号组成的密码'))
      } else {
        callback()
      }
    }

    //再次输入密码验证
    validateAgainPassword(rule: any, value: any, callback: any) {
      if (!this.form.certainPassword) {
        callback(new Error('请再次输入密码'))
      } else if (this.form.certainPassword != this.createSystemManager.createSubAdminParams.password) {
        callback(new Error('两次输入的密码不一致'))
      } else {
        callback()
      }
    }

    /**
     * 手机号校验规则
     */
    validatePhone(rule: any, value: any, callback: any) {
      if (!this.createSystemManager.createSubAdminParams.phone) {
        callback(new Error('请输入手机号码'))
      } else if (!this.regPhone.test(this.createSystemManager.createSubAdminParams.phone)) {
        callback(new Error('请输入11位真实有效手机号'))
      } else {
        callback()
      }
    }

    // 返回上一页
    goBack() {
      this.$router.push('/basic-data/account/administrator-account')
    }

    //打开
    @bind
    @debounce(200)
    async openAddRoleDialog() {
      this.showRoleDialog = true
    }

    input(showRoleDialog: boolean) {
      this.showRoleDialog = showRoleDialog
    }

    //确认
    confirmDialog(result: Array<RoleInfoResponse>) {
      this.roleList = result
    }

    //删除列表
    closeRoleInfo(id: string) {
      this.roleList = this.roleList.filter((i) => i.id !== id)
    }
  }
</script>
