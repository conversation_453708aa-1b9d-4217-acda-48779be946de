<!-- 地图回显组件 -->
<template>
  <div id="map" :style="`width:${width};height:${height};`" v-loading="mapLoading"></div>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import QueryRegionInfo from '@api/service/common/tx-map/QueryRegionInfo'

  @Component
  export default class extends Vue {
    @Prop({
      type: Object,
      required: true
    })
    locationDetail: {
      latitude: number
      longitude: number
      workPlace: string
      zoom: number
    }
    @Prop({
      type: String,
      default: '630px'
    })
    width: string
    @Prop({
      type: String,
      default: '630px'
    })
    height: string
    @Prop({
      type: Boolean,
      default: true
    })
    showInfoTitle: boolean
    @Prop({
      type: Boolean,
      default: true
    })
    isMapClickEnabled: boolean
    @Prop({
      type: Boolean,
      default: true
    })
    isUserInput: boolean
    @Watch('locationDetail', {
      deep: true,
      immediate: true
    })
    valueChange() {
      this.drawerOpened()
    }
    map: any //地图实例
    control: any
    rotation: any
    mapLoading = false
    queryRegionInfo = new QueryRegionInfo()
    key = ''
    async mounted() {
      await this.drawerOpened()
    }
    // 引入腾讯地图
    loadTencentMap = async (key: string) => {
      return new Promise((resolve, reject) => {
        const findScript = document.getElementById(`${key}`)
        // 判断是否加载过
        if (findScript || window.TMap) {
          return resolve(true)
        }
        const script = document.createElement('script')
        script.setAttribute('id', key)
        script.crossOrigin = 'anonymous'
        script.onload = () => {
          resolve(true)
        }
        script.onerror = (err) => {
          resolve(false)
        }
        script.src = `https://map.qq.com/api/gljs?v=2.exp&key=${key}`
        document.body.appendChild(script)
      })
    }

    // 绘制地图
    async drawerOpened(): Promise<void> {
      try {
        if (this.map) {
          this.map.destroy()
          this.map = null
        }
        this.$nextTick(async () => {
          this.key = CommonConfigCenter.getFrontendApplication(frontendApplication.txMapKey)
          await this.loadTencentMap(this.key) // 确保脚本加载完成
          const center = await this.getMapCenter()
          this.initializeMap(center)
          const marker = this.addMarker(center)
          this.setupMapClickHandler(marker)
        })
      } catch (err) {
        console.error('地图初始化失败:', err)
        this.mapLoading = false
      }
    }

    // 获取地图中心点
    async getMapCenter(): Promise<any> {
      console.log(this.locationDetail, '11111111111111')
      // 新增培训点的时候用获取的点
      if (this.isMapClickEnabled && !this.isUserInput) {
        try {
          localStorage.removeItem('nowLocation')
          const url = `/ws/location/v1/ip?key=${encodeURIComponent(this.key)}`
          const response: any = await this.queryRegionInfo.queryCurrentIpJsonp(url)
          localStorage.setItem('nowLocation', JSON.stringify(response))
          return new window.TMap.LatLng(response?.lat, response?.lng)
        } catch (error) {
          throw new Error('腾讯地图 API 请求失败')
        }
      } else if (this.isMapClickEnabled && this.isUserInput) {
        const { latitude, longitude } = this.locationDetail
        return new window.TMap.LatLng(latitude, longitude)
      } else {
        // 详情时用传进来的点
        const { latitude, longitude } = this.locationDetail
        return new window.TMap.LatLng(latitude, longitude)
      }
    }

    // 初始化地图
    // 初始化地图
    private initializeMap(center: any) {
      this.map = new window.TMap.Map(document.getElementById('map') as HTMLElement, {
        center: center,
        zoom: this.locationDetail.zoom || 17.2
      })
      // 获取缩放控件实例
      this.control = this.map.getControl(window.TMap.constants.DEFAULT_CONTROL_ID.ZOOM)
      // 获取旋转控件
      this.rotation = this.map.getControl(window.TMap.constants.DEFAULT_CONTROL_ID.ROTATION)
      if (!this.isMapClickEnabled && !this.isUserInput) {
        this.control.dom.remove()
        this.rotation.dom.remove()
      }
    }

    // 添加标记
    private addMarker(center: any) {
      return new window.TMap.MultiMarker({
        map: this.map,
        styles: {
          marker: new window.TMap.MarkerStyle({
            width: 20,
            height: 30,
            anchor: { x: 10, y: 30 }
          })
        },
        geometries: [{ position: center, id: 'marker' }]
      })
    }

    // 设置地图点击事件处理器
    private setupMapClickHandler(marker: any) {
      const info = new window.TMap.InfoWindow({
        map: this.map,
        position: this.map.getCenter()
      }).close()

      // 添加防抖计时器变量
      let clickTimer: any = null

      this.map.on('click', async (evt: any) => {
        if (!this.isMapClickEnabled) return

        // 清除之前的定时器（防抖核心）
        if (clickTimer !== null) {
          clearTimeout(clickTimer)
          clickTimer = null
        }

        // 设置新的定时器（300毫秒延迟）
        clickTimer = setTimeout(async () => {
          console.log(evt, '--------------------')
          const poi = evt.poi as { name: string; latLng: any }
          if (!poi || !poi.latLng) {
            info.close()
            return
          }

          const { lat, lng } = poi.latLng
          const address = await this.fetchAddress(lat, lng, poi.name)
          await this.updateMarkerAndCenter(marker, poi.latLng)

          const point = {
            address,
            latLng: poi.latLng
          }
          this.$emit('point', point)
        }, 600) // 防抖延迟时间（毫秒）
      })
    }

    // 获取地址信息
    private async fetchAddress(lat: number, lng: number, poiName: string): Promise<string> {
      const url = `/ws/geocoder/v1/?location=${lat},${lng}&key=${this.key}`
      const response: { address: string } = await this.queryRegionInfo.queryCurrentAddressJsonp(url)
      return response.address.includes(poiName) ? response.address : response.address + poiName
    }

    // 更新标记点和地图中心
    private updateMarkerAndCenter(marker: any, latLng: any) {
      this.map.setCenter(latLng)
      marker.updateGeometries([{ position: latLng, id: 'marker' }])
    }
    beforeDestroy() {
      if (this.map) {
        this.map.destroy()
      }
    }
  }
</script>

<style>
  /* .info_card {
      display: inline-block;
      margin: 0 auto;
      position: absolute;
      width: 500px;
      height: 100px;
    } */
  .info_card .title {
    width: 300px;
    height: 20px;
  }

  .title span.title_name {
    position: relative;
    top: -15px;
    left: 10px;
    font-size: 16px;
  }

  .info_card .content {
    /* margin-top: 10px; */
    margin-left: 10px;
  }
</style>
