<template>
  <el-container>
    <el-aside width="240px">
      <!--侧边栏按钮 展开时显示-->
      <a href="#" class="aside-btn el-icon-s-fold"></a>
      <!--侧边栏按钮 收起时显示-->
      <!--<a href="#" class="aside-btn el-icon-s-unfold"></a>-->
      <div class="logo">
        <span class="logo-txt">培训管理平台</span>
      </div>
      <div class="user-info">
        <img class="photo" src="./assets/images/default-photo.jpg" alt=" " />
        <p class="name">张某某</p>
        <div class="op-btn f-mt10">
          <el-button type="primary" size="mini" round plain>
            <i class="el-icon-s-tools"></i>
            帐号设置
          </el-button>
        </div>
      </div>
      <el-menu default-active="8" class="aside-nav" unique-opened="true" @open="handleOpen" @close="handleClose">
        <el-menu-item index="0">
          <template slot="title">
            <i class="iconfont icon-shouye1"></i>
            <span>首页</span>
          </template>
        </el-menu-item>
        <!--系统基础配置-->
        <el-submenu index="1">
          <template slot="title">
            <i class="iconfont icon-weiwangxiao"></i>
            <span>网校配置</span>
          </template>
          <el-menu-item index="1-1">基础信息配置</el-menu-item>
          <el-menu-item index="1-2">功能配置</el-menu-item>
          <el-menu-item index="1-2">修改日志</el-menu-item>
        </el-submenu>
        <el-menu-item index="12">
          <template slot="title">
            <i class="iconfont icon-zixun"></i>
            <span>资讯管理</span>
          </template>
        </el-menu-item>
        <el-submenu index="998">
          <template slot="title">
            <i class="iconfont icon-xuanzeleixing"></i>
            <span>交易信息配置</span>
          </template>
          <el-menu-item index="1-1">收款账户管理</el-menu-item>
          <el-menu-item index="1-2">报名方式配置</el-menu-item>
        </el-submenu>
        <el-menu-item index="14">
          <template slot="title">
            <i class="iconfont icon-peisong"></i>
            <span>配送渠道配置</span>
          </template>
        </el-menu-item>
        <el-submenu index="999">
          <template slot="title">
            <i class="iconfont icon-yunyingrenyuan"></i>
            <span>运营帐号管理</span>
          </template>
          <el-menu-item index="1-1">管理员帐号管理</el-menu-item>
          <el-menu-item index="1-2">角色管理</el-menu-item>
          <el-menu-item index="1-3">地区管理员</el-menu-item>
        </el-submenu>
        <!--教学资源管理-->
        <el-menu-item index="4">
          <template slot="title">
            <i class="iconfont icon-kejian"></i>
            <span>课件管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="5">
          <template slot="title">
            <i class="iconfont icon-xuexi"></i>
            <span>课程管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="6">
          <template slot="title">
            <i class="iconfont icon-tiku"></i>
            <span>题库管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="7">
          <template slot="title">
            <i class="iconfont icon-shenqing"></i>
            <span>试题管理</span>
          </template>
        </el-menu-item>
        <el-menu-item index="8">
          <template slot="title">
            <i class="iconfont icon-shijuan"></i>
            <span>试卷管理</span>
          </template>
        </el-menu-item>
        <!--培训管理-->
        <el-menu-item index="9">
          <template slot="title">
            <i class="iconfont icon-dabao"></i>
            <span>课程包管理</span>
          </template>
        </el-menu-item>
        <el-submenu index="10">
          <template slot="title">
            <i class="iconfont icon-fangan"></i>
            <span>培训方案管理</span>
          </template>
          <el-menu-item index="1-1">新建培训方案</el-menu-item>
          <el-menu-item index="1-2">培训方案管理</el-menu-item>
          <el-menu-item index="1-3">培训属性值管理</el-menu-item>
        </el-submenu>
        <el-submenu index="100">
          <template slot="title">
            <i class="iconfont icon-jiaoyi"></i>
            <span>交易管理</span>
          </template>
          <el-submenu index="1-4">
            <template slot="title">订单管理</template>
            <el-menu-item index="1-4-1">个人报名订单</el-menu-item>
            <el-menu-item index="1-4-2">集体报名订单</el-menu-item>
          </el-submenu>
          <el-submenu index="1-5">
            <template slot="title">退款管理</template>
            <el-menu-item index="1-5-1">个人报名退款订单</el-menu-item>
            <el-menu-item index="1-5-2">集体报名退款订单</el-menu-item>
          </el-submenu>
          <el-submenu index="1-6">
            <template slot="title">发票管理</template>
            <el-menu-item index="1-5-1">个人报名发票管理</el-menu-item>
            <el-menu-item index="1-5-2">集体报名发票管理</el-menu-item>
          </el-submenu>
          <el-submenu index="1-7">
            <template slot="title">对账管理</template>
            <el-menu-item index="1-5-1">个人报名对账</el-menu-item>
            <el-menu-item index="1-5-2">集体报名对账</el-menu-item>
          </el-submenu>
          <el-submenu index="1-8">
            <template slot="title">导入开通</template>
            <el-menu-item index="1-5-1">导入学员</el-menu-item>
            <el-menu-item index="1-5-2">导入学员并开班</el-menu-item>
            <el-menu-item index="1-5-2">导入开通结果跟踪</el-menu-item>
          </el-submenu>
          <el-submenu index="1-9">
            <template slot="title">用户管理</template>
            <el-menu-item index="1-5-1">学员管理</el-menu-item>
            <el-menu-item index="1-5-2">集体报名帐号管理</el-menu-item>
          </el-submenu>
          <el-submenu index="1-10">
            <template slot="title">客服管理</template>
            <el-menu-item index="1-5-1">业务咨询</el-menu-item>
            <el-menu-item index="1-5-2">集体报名咨询</el-menu-item>
          </el-submenu>
          <el-menu-item index="1-11">
            <template slot="title">批量打印证明</template>
          </el-menu-item>
          <el-submenu index="1-12">
            <template slot="title">导入导出任务管理</template>
            <el-menu-item index="1-5-1">导入任务管理</el-menu-item>
            <el-menu-item index="1-5-2">导出任务管理</el-menu-item>
          </el-submenu>
        </el-submenu>
        <!--统计报表-->
        <el-menu-item index="13">
          <template slot="title">
            <i class="iconfont icon-ribaotongji"></i>
            <span>方案开通统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="14">
          <template slot="title">
            <i class="iconfont icon-cptj"></i>
            <span>地区开通统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="15">
          <template slot="title">
            <i class="iconfont icon-tongjiyuce"></i>
            <span>方案学习统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="16">
          <template slot="title">
            <i class="iconfont icon-tongjibaobiao"></i>
            <span>地区学习统计</span>
          </template>
        </el-menu-item>
        <el-menu-item index="17">
          <template slot="title">
            <i class="iconfont icon-mingxi"></i>
            <span>学员学习明细</span>
          </template>
        </el-menu-item>
      </el-menu>
      <div class="m-company-info">
        <p>福建华博教育科技股份有限公司</p>
        <a class="a-txt" href="http://beian.miit.gov.cn/" target="_blank">闽ICP备2021002737号</a>
      </div>
    </el-aside>
    <el-container>
      <el-header height="100px" class="f-flex">
        <ul class="header-nav f-flex-sub">
          <li class="nav-item current">
            <i class="iconfont icon-shouye"></i>
            <span class="txt">首页</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-peizhi"></i>
            <span class="txt">系统基础配置</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-kecheng"></i>
            <span class="txt">教学资源管理</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-peixun"></i>
            <span class="txt">培训管理</span>
          </li>
          <li class="nav-item">
            <i class="iconfont icon-shuju"></i>
            <span class="txt">统计报表</span>
          </li>
          <li class="current-bg"></li>
        </ul>
        <ul class="header-nav">
          <li class="nav-item nav-item-1"><i class="iconfont icon-tuichu"></i>退出</li>
        </ul>
      </el-header>
      <div class="tags">
        <span class="prev"><i class="el-icon el-icon-arrow-left"></i></span>
        <div class="tags-bd">
          <div class="tags-items" style="width: 500%;">
            <el-tag class="current" closable>首页</el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
            <el-tag closable>页面页面页面页面1 </el-tag>
          </div>
        </div>
        <span class="next"><i class="el-icon el-icon-arrow-right"></i></span>
        <el-dropdown :hide-on-click="false">
          <span class="more"><i class="el-icon el-icon-more"></i></span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item>黄金糕</el-dropdown-item>
            <el-dropdown-item>狮子头</el-dropdown-item>
            <el-dropdown-item>螺蛳粉</el-dropdown-item>
            <el-dropdown-item disabled>双皮奶</el-dropdown-item>
            <el-dropdown-item divided>蚵仔煎</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
      <el-main>
        <!--面包屑-->
        <el-breadcrumb separator-class="el-icon-arrow-right">
          <el-button type="text" size="mini" class="return-btn">
            <i class="iconfont icon-lsh-return"></i>
          </el-button>
          <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>
          <el-breadcrumb-item>活动管理</el-breadcrumb-item>
        </el-breadcrumb>
        <el-alert type="warning" show-icon :closable="false" class="m-alert is-border-bottom f-ptb10">
          如果有整个页面的提示，在这个位置。如果不需要底部边框，去掉 is-border-bottom
        </el-alert>
        <!--这里是 顶部tab标签-->
        <el-tabs v-model="activeName" class="m-tab-top is-sticky">
          <el-tab-pane label="表格样式说明" name="first">
            <div class="f-p15">
              <el-card shadow="never" class="m-card f-mb15">
                <!--条件查询-->
                <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
                <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
                <el-row :gutter="16" class="m-query is-border-bottom">
                  <el-form :inline="true" label-width="auto">
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="文本框">
                        <el-input v-model="input" clearable placeholder="请输入" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="选择器">
                        <el-select v-model="select" clearable filterable placeholder="请选择">
                          <el-option value="选项1"></el-option>
                          <el-option value="选项2"></el-option>
                        </el-select>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="级联选择器">
                        <el-cascader clearable :options="cascader" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="时间选择">
                        <el-date-picker
                          v-model="form.date1"
                          type="datetimerange"
                          range-separator="至"
                          start-placeholder="起始时间"
                          end-placeholder="结束时间"
                        >
                        </el-date-picker>
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6">
                      <el-form-item label="学员名称">
                        <el-input v-model="input" placeholder="请输入" />
                      </el-form-item>
                    </el-col>
                    <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                      <el-form-item class="f-tr">
                        <el-button type="primary">查询</el-button>
                        <el-button>重置</el-button>
                        <el-button type="text">展开<i class="el-icon-arrow-down el-icon--right"></i></el-button>
                        <!--<el-button type="text">收起<i class="el-icon-arrow-up el-icon&#45;&#45;right"></i></el-button>-->
                      </el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
                <!--操作栏-->
                <div class="f-mt20 f-mb20">
                  <el-button type="primary" icon="el-icon-plus">新增</el-button>
                </div>
                <!--表格-->
                <!--统计表格添加 .is-statistical-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="文本默认左对齐" min-width="300">
                    <template>福建华博教育科技股份有限公司</template>
                  </el-table-column>
                  <el-table-column label="时间" min-width="180">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="状态样式1及颜色说明" min-width="200">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="primary" class="badge-status">XXX中</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-badge is-dot type="success" class="badge-status">XX成功 / XX完成 / 启用</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 2">
                        <el-badge is-dot type="warning" class="badge-status">待XX</el-badge>
                      </div>
                      <div v-else-if="scope.$index === 3">
                        <el-badge is-dot type="danger" class="badge-status">XX失败</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="info" class="badge-status">未XX / XX失效 / 停用</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="状态样式2及颜色说明" min-width="200">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-tag type="primary">XXX中</el-tag>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-tag type="success">XX成功 / XX完成 / 启用</el-tag>
                      </div>
                      <div v-else-if="scope.$index === 2">
                        <el-tag type="warning">待XX</el-tag>
                      </div>
                      <div v-else-if="scope.$index === 3">
                        <el-tag type="danger">XX失败</el-tag>
                      </div>
                      <div v-else>
                        <el-tag type="info">未XX / XX失效 / 停用</el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="数字右对齐" width="200" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">3.15</div>
                      <div v-else-if="scope.$index === 1">52.36</div>
                      <div v-else>158.15</div>
                    </template>
                  </el-table-column>
                  <!--操作列固定在右侧-->
                  <el-table-column label="操作" width="120" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">详情</el-button>
                      <el-button type="text" size="mini">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-card>
            </div>
          </el-tab-pane>
          <el-tab-pane label="表单样式" name="second">
            <div class="f-p15">
              <el-tabs v-model="activeName3" type="card" class="m-tab-card">
                <el-tab-pane label="基础表单" name="first">
                  <el-card shadow="never" class="m-card f-mb15">
                    <div class="f-p30">
                      <!--el-row 控制表单宽度并居中，可根据实际情况调整，全屏表单添加 width-limit-->
                      <el-row type="flex" justify="center" class="width-limit">
                        <el-col :md="20" :lg="16" :xl="13">
                          <!--右侧输入框及选择器默认长度为100%，.form-l：60%，.form-m：40%，.form-s：30%-->
                          <el-form ref="form" :model="form" label-width="auto" class="m-form">
                            <el-form-item label="文本框：" required>
                              <el-input v-model="form.name" clearable placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="选择器：">
                              <!--可搜索 filterable-->
                              <el-select v-model="form.region" clearable placeholder="请选择">
                                <el-option value="选项1"></el-option>
                                <el-option value="选项2"></el-option>
                              </el-select>
                            </el-form-item>
                            <el-form-item label="级联选择器：">
                              <!--可搜索 filterable-->
                              <el-cascader clearable :options="cascader" placeholder="请选择" />
                            </el-form-item>
                            <el-form-item label="多选：">
                              <el-checkbox-group v-model="form.type">
                                <el-checkbox label="美食/餐厅线上活动" name="type"></el-checkbox>
                                <el-checkbox label="地推活动" name="type"></el-checkbox>
                                <el-checkbox label="线下主题活动" name="type"></el-checkbox>
                                <el-checkbox label="单纯品牌曝光" name="type"></el-checkbox>
                              </el-checkbox-group>
                            </el-form-item>
                            <el-form-item label="单选：">
                              <el-radio-group v-model="form.resource">
                                <el-radio label="线上品牌商赞助"></el-radio>
                                <el-radio label="线下场地免费"></el-radio>
                              </el-radio-group>
                            </el-form-item>
                            <el-form-item label="开关模式：">
                              <el-switch v-model="form.delivery" />
                            </el-form-item>
                            <el-form-item label="文本域：">
                              <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="富文本区域：">
                              <div class="rich-text">
                                <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入" />
                              </div>
                            </el-form-item>
                            <el-form-item label="文件/文档上传：">
                              <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                                <el-button size="small" type="primary">点击上传</el-button>
                                <div slot="tip" class="el-upload__tip">
                                  <i class="el-icon-warning"></i>
                                  <span class="txt">只能上传jpg/png文件，且不超过500kb</span>
                                </div>
                              </el-upload>
                            </el-form-item>
                            <el-form-item label="默认图片上传：">
                              <el-upload action="#" list-type="picture-card" :auto-upload="false" class="m-pic-upload">
                                <div slot="default" class="upload-placeholder">
                                  <i class="el-icon-plus"></i>
                                  <p class="txt">上传图片</p>
                                </div>
                                <div slot="file" slot-scope="{ file }" class="img-file">
                                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                                  <div class="el-upload-list__item-actions">
                                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                      v-if="!disabled"
                                      class="el-upload-list__item-delete"
                                      @click="handleRemove(file)"
                                    >
                                      <i class="el-icon-delete"></i>
                                    </span>
                                  </div>
                                </div>
                                <div slot="tip" class="el-upload__tip">
                                  <i class="el-icon-warning"></i>
                                  <div class="txt">
                                    上传的平台名称及LOGO图片，适用于门户顶部。请先设计好后上传，尺寸：850px * 50px。
                                    <i class="f-link" @click="dialog1 = true">查看示例图片</i>
                                  </div>
                                  <!--示例图片弹窗-->
                                  <el-dialog :visible.sync="dialog1" width="1100px" class="m-dialog-pic">
                                    <img src="./assets/images/demo-web-banner.jpg" alt="" />
                                  </el-dialog>
                                </div>
                              </el-upload>
                              <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                                <img :src="dialogImageUrl" alt="" />
                              </el-dialog>
                            </el-form-item>
                            <el-form-item label="多行文本：" class="is-text">
                              这里是文本内容这里是文本内容这里是文本内容这里是文本内容
                            </el-form-item>
                            <el-form-item class="m-btn-bar">
                              <el-button>取消</el-button>
                              <el-button type="primary">保存</el-button>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </div>
                  </el-card>
                </el-tab-pane>
                <el-tab-pane label="各种形式汇总" name="second">
                  <el-card shadow="never" class="m-card f-mb15">
                    <div class="f-p30">
                      <!--全屏表单添加 width-limit-->
                      <el-row type="flex" justify="center" class="width-limit">
                        <el-col :md="20" :lg="16" :xl="13">
                          <!--右侧输入框及选择器默认长度为100%，.form-l：60%，.form-m：40%，.form-s：30%-->
                          <el-form ref="form" :model="form" label-width="auto" class="m-form">
                            <el-form-item label="标签有文字提示：">
                              <div slot="label">
                                <span class="f-cr f-mr5">*</span>
                                <span class="f-vm">提供服务号</span>
                                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                                  <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                                  <div slot="content">
                                    <p>1. 注册微信服务号</p>
                                  </div>
                                </el-tooltip>
                                <span>：</span>
                              </div>
                              <el-radio-group v-model="form.resource">
                                <el-radio label="是"></el-radio>
                                <el-radio label="否"></el-radio>
                              </el-radio-group>
                            </el-form-item>
                            <el-form-item label="默认宽度：" required>
                              <el-input v-model="form.name" clearable placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="form-l 宽度：">
                              <el-input v-model="form.name" clearable class="form-l" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="form-m 宽度：">
                              <el-input v-model="form.name" clearable class="form-m" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="form-s 宽度：">
                              <el-input v-model="form.name" clearable class="form-s" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="数字文本框：">
                              <el-input v-model="form.name" class="input-num" placeholder="请输入" />
                            </el-form-item>
                            <el-form-item label="日期选择器：">
                              <el-date-picker v-model="form.date1" type="date" placeholder="选择日期" class="form-s" />
                            </el-form-item>
                            <el-form-item label="日期选择范围：">
                              <el-date-picker
                                v-model="form.date1"
                                type="daterange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                                class="form-l"
                              >
                              </el-date-picker>
                            </el-form-item>
                            <el-form-item label="日期时间选择器：">
                              <el-date-picker
                                v-model="form.date1"
                                type="datetime"
                                placeholder="选择日期时间"
                                class="form-s"
                              >
                              </el-date-picker>
                            </el-form-item>
                            <el-form-item label="日期时间选择范围：">
                              <el-date-picker
                                v-model="form.date1"
                                type="datetimerange"
                                range-separator="至"
                                start-placeholder="开始日期"
                                end-placeholder="结束日期"
                              >
                              </el-date-picker>
                            </el-form-item>
                            <el-form-item label="开关模式1：">
                              <el-switch v-model="form.delivery" />
                            </el-form-item>
                            <el-form-item label="开关模式2：">
                              <el-switch v-model="form.delivery" active-text="必填" inactive-text="非必填" />
                            </el-form-item>
                            <el-form-item label="开关模式3：">
                              <el-switch
                                v-model="form.delivery"
                                active-text="开启"
                                inactive-text="关闭"
                                class="m-switch"
                              />
                            </el-form-item>
                            <el-form-item label="多选单行显示：">
                              <el-checkbox-group v-model="form.type" class="is-column">
                                <el-checkbox label="美食/餐厅线上活动" name="type"></el-checkbox>
                                <el-checkbox label="地推活动" name="type"></el-checkbox>
                                <el-checkbox label="线下主题活动" name="type"></el-checkbox>
                                <el-checkbox label="单纯品牌曝光" name="type"></el-checkbox>
                              </el-checkbox-group>
                            </el-form-item>
                            <el-form-item label="单选单行显示：">
                              <el-radio-group v-model="form.resource" class="is-column">
                                <el-radio label="线上品牌商赞助"></el-radio>
                                <el-radio label="线下场地免费"></el-radio>
                              </el-radio-group>
                            </el-form-item>
                            <el-form-item label="16:9图片上传：">
                              <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false"
                                class="m-pic-upload proportion-pic"
                              >
                                <div slot="default" class="upload-placeholder">
                                  <i class="el-icon-plus"></i>
                                  <p class="txt">上传图片</p>
                                </div>
                                <div slot="file" slot-scope="{ file }" class="img-file">
                                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                                  <div class="el-upload-list__item-actions">
                                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                      v-if="!disabled"
                                      class="el-upload-list__item-delete"
                                      @click="handleRemove(file)"
                                    >
                                      <i class="el-icon-delete"></i>
                                    </span>
                                  </div>
                                </div>
                                <div slot="tip" class="el-upload__tip">
                                  <i class="el-icon-warning"></i>
                                  <div class="txt">
                                    上传的平台名称及LOGO图片，适用于门户顶部。请先设计好后上传，尺寸：850px * 50px。
                                    <i class="f-link">查看示例图片</i>
                                  </div>
                                </div>
                              </el-upload>
                              <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                                <img :src="dialogImageUrl" alt="" />
                              </el-dialog>
                            </el-form-item>
                            <el-form-item label="小图上传：">
                              <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false"
                                class="m-pic-upload small-pic"
                              >
                                <div slot="default" class="upload-placeholder">
                                  <i class="el-icon-plus"></i>
                                  <p class="txt">上传图片</p>
                                </div>
                                <div slot="file" slot-scope="{ file }" class="img-file">
                                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                                  <div class="el-upload-list__item-actions">
                                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                      v-if="!disabled"
                                      class="el-upload-list__item-delete"
                                      @click="handleRemove(file)"
                                    >
                                      <i class="el-icon-delete"></i>
                                    </span>
                                  </div>
                                </div>
                                <div slot="tip" class="el-upload__tip">
                                  <i class="el-icon-warning"></i>
                                  <div class="txt">
                                    上传的平台名称及LOGO图片，适用于门户顶部。请先设计好后上传，尺寸：850px * 50px。
                                    <i class="f-link">查看示例图片</i>
                                  </div>
                                </div>
                              </el-upload>
                              <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                                <img :src="dialogImageUrl" alt="" />
                              </el-dialog>
                            </el-form-item>
                            <el-form-item label="长图上传：">
                              <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false"
                                class="m-pic-upload long-pic"
                              >
                                <div slot="default" class="upload-placeholder">
                                  <i class="el-icon-plus"></i>
                                  <p class="txt">上传图片</p>
                                </div>
                                <div slot="file" slot-scope="{ file }" class="img-file">
                                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                                  <div class="el-upload-list__item-actions">
                                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                      v-if="!disabled"
                                      class="el-upload-list__item-delete"
                                      @click="handleRemove(file)"
                                    >
                                      <i class="el-icon-delete"></i>
                                    </span>
                                  </div>
                                </div>
                                <div slot="tip" class="el-upload__tip">
                                  <i class="el-icon-warning"></i>
                                  <div class="txt">
                                    上传的平台名称及LOGO图片，适用于门户顶部。请先设计好后上传，尺寸：850px * 50px。
                                    <i class="f-link">查看示例图片</i>
                                  </div>
                                </div>
                              </el-upload>
                              <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                                <img :src="dialogImageUrl" alt="" />
                              </el-dialog>
                            </el-form-item>
                            <el-form-item label="长图上传带链接：">
                              <el-upload
                                action="#"
                                list-type="picture-card"
                                :auto-upload="false"
                                class="m-pic-upload long-pic"
                              >
                                <div slot="default" class="upload-placeholder">
                                  <i class="el-icon-plus"></i>
                                  <p class="txt">上传图片</p>
                                </div>
                                <div slot="file" slot-scope="{ file }" class="img-file">
                                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                                  <div class="el-upload-list__item-actions">
                                    <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                      <i class="el-icon-zoom-in"></i>
                                    </span>
                                    <span
                                      v-if="!disabled"
                                      class="el-upload-list__item-delete"
                                      @click="handleRemove(file)"
                                    >
                                      <i class="el-icon-delete"></i>
                                    </span>
                                  </div>
                                  <div class="other">
                                    <p>链接地址</p>
                                    <el-input v-model="form.name" clearable class="f-wf" placeholder="请输入链接地址" />
                                  </div>
                                </div>
                                <div slot="tip" class="el-upload__tip">
                                  <i class="el-icon-warning"></i>
                                  <div class="txt">
                                    上传的平台名称及LOGO图片，适用于门户顶部。请先设计好后上传，尺寸：850px * 50px。
                                    <i class="f-link">查看示例图片</i>
                                  </div>
                                </div>
                              </el-upload>
                              <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                                <img :src="dialogImageUrl" alt="" />
                              </el-dialog>
                            </el-form-item>
                            <el-form-item label="多行文本：" class="is-text">
                              这里是文本内容这里是文本内容这里是文本内容这里是文本内容
                            </el-form-item>
                            <el-form-item class="m-btn-bar">
                              <el-button>取消</el-button>
                              <el-button type="primary">立即创建</el-button>
                            </el-form-item>
                          </el-form>
                        </el-col>
                      </el-row>
                    </div>
                  </el-card>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-tab-pane>
          <el-tab-pane label="文字列表" name="five">
            <div class="f-p15">
              <el-card shadow="never" class="m-card f-mb15">
                <el-row :gutter="16">
                  <el-form :inline="true" label-width="auto" class="m-text-form">
                    <el-col :md="12" :xl="8">
                      <el-form-item label="用户名：">张三</el-form-item>
                    </el-col>
                    <el-col :md="12" :xl="8">
                      <el-form-item label="手机号：">15865478965</el-form-item>
                    </el-col>
                    <el-col :md="12" :xl="8">
                      <el-form-item label="居住地：">福建省福州市鼓楼区工业路611号海峡技术转移中心13层</el-form-item>
                    </el-col>
                    <el-col :md="12" :xl="8">
                      <el-form-item label="备注：">-</el-form-item>
                    </el-col>
                    <el-col :md="12" :xl="8">
                      <el-form-item label="联系地址：">福建省福州市鼓楼区工业路611号海峡技术转移中心13层</el-form-item>
                    </el-col>
                  </el-form>
                </el-row>
              </el-card>
              <el-card shadow="never" class="m-card f-mb15">
                <el-row :gutter="16">
                  <el-form ref="form" :model="form" label-width="auto" class="m-text-form is-column">
                    <el-form-item label="说明：">这是单行形式</el-form-item>
                    <el-form-item label="手机号：">15865478965</el-form-item>
                    <el-form-item label="用户名：">张三</el-form-item>
                    <el-form-item label="联系地址：">福建省福州市鼓楼区工业路611号海峡技术转移中心13层</el-form-item>
                    <el-form-item label="考核结果：" class="is-form">
                      <span class="f-cg f-mr10">合格</span>
                      <span class="f-cr f-mr10">未合格</span>
                      <el-button type="primary" size="small">一键合格</el-button>
                    </el-form-item>
                  </el-form>
                </el-row>
              </el-card>
            </div>
          </el-tab-pane>
          <el-tab-pane label="卡片及标题样式" name="third">
            <div class="f-p15">
              <!--默认卡片-->
              <el-card shadow="never" class="m-card f-mb15">
                默认卡片，内容区域默认 padding: 20px，若无需 padding 添加 is-header
              </el-card>
              <!--带标题卡片-->
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div slot="header" class="">
                  <span class="tit-txt">带标题卡片</span>
                </div>
                <div class="f-p20">内容</div>
              </el-card>
              <!--自定义标题-->
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-border-bottom">
                  <span class="tit-txt">自定义标题，无需分割线去掉 is-border-bottom</span>
                </div>
                <div class="m-tit is-small is-border-bottom">
                  <span class="tit-txt">自定义标题带下划线（小），无需分割线去掉 is-border-bottom</span>
                </div>
                <div class="m-sub-tit is-border-bottom">
                  <span class="tit-txt">二级自定义标题带下划线，无需分割线去掉 is-border-bottom</span>
                </div>
              </el-card>
              <!--白色背景无边框-->
              <el-card shadow="never" class="m-card is-bg f-mb15">
                <div class="f-p20">白色背景无边框</div>
              </el-card>
            </div>
          </el-tab-pane>
          <el-tab-pane label="Tabs 标签" name="fourth">
            <div class="f-p15">
              <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                <el-tab-pane label="标签一" name="first">
                  <el-card shadow="never" class="m-card f-mb15">
                    这里是第二种标签样式，第一种是上面那种
                  </el-card>
                </el-tab-pane>
                <el-tab-pane name="second">
                  <div slot="label">自定义标签</div>
                  <el-card shadow="never" class="m-card f-mb15">
                    具体内容
                  </el-card>
                </el-tab-pane>
              </el-tabs>
            </div>
          </el-tab-pane>
          <el-tab-pane label="布局" name="six">
            <div class="f-p15">
              <el-row :gutter="15" class="is-height">
                <el-col :span="7">
                  <el-card shadow="never" class="m-card f-mb15">
                    左右等高布局
                  </el-card>
                </el-col>
                <el-col :span="17">
                  <el-card shadow="never" class="m-card f-mb15"> 左<br />右<br />等<br />高<br />布<br />局 </el-card>
                </el-col>
              </el-row>
            </div>
          </el-tab-pane>
          <el-tab-pane label="其他样式" name="seven">
            <div class="f-p15">
              <el-card shadow="never" class="m-card f-mb15">
                <span class="f-mr10">这里是提示说明</span>
                <el-tooltip effect="dark" placement="right" popper-class="m-tooltip">
                  <i class="el-icon-question m-tooltip-icon f-c9"></i>
                  <div slot="content">这里是提示内容这里是提示内容</div>
                </el-tooltip>
                <!--这里是分割线-->
                <el-divider class="m-divider"></el-divider>
                <!--提示样式-->
                <el-alert type="warning" show-icon :closable="false" class="m-alert">
                  这里是提示样式
                </el-alert>
                <!--这里是分割线-->
                <el-divider class="m-divider"></el-divider>
                <!--大图可预览-->
                <el-image
                  src="/assets/images/web-default-banner.jpg"
                  :preview-src-list="['/assets/images/web-default-banner.jpg']"
                  class="course-pic"
                >
                </el-image>
              </el-card>
            </div>
          </el-tab-pane>
        </el-tabs>
      </el-main>
    </el-container>
  </el-container>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'six',
        activeName1: 'first',
        activeName2: 'second',
        activeName3: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
