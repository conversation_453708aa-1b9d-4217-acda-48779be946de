<!--创建试题-单选题-->
<template>
  <div :gutter="15" class="mt30">
    <el-form-item label="试题题目" required>
      <el-input type="textarea" v-if="show" :rows="6" v-model="createQuestion.topic" placeholder="请输入试题题目" />
      <!-- <hb-tiny-mce-editor v-model="createQuestion.topic" v-if="show"></hb-tiny-mce-editor> -->
    </el-form-item>
    <el-form-item label="试题选项" required>
      <div v-for="(item, index) in choiceItems" :key="index" class="m-option-btn f-mb20">
        <div class="f-flex f-align-center f-mb5">
          <p class="f-flex-sub f-cb">选项：{{ resolverIndexToCharCode(index) }}</p>
          <el-button
            :type="index === 0 ? 'info' : 'primary'"
            plain
            size="mini"
            icon="el-icon-top"
            @click="questionUp(index)"
          ></el-button>
          <el-button
            :type="index == choiceItems.length - 1 ? 'info' : 'primary'"
            plain
            size="mini"
            icon="el-icon-bottom"
            @click="questionDown(index)"
          ></el-button>
          <el-button type="danger" icon="el-icon-delete" size="mini" @click="questionDelete(index)"></el-button>
        </div>
        <div class="text-options" style="display:flex">
          <el-input type="textarea" :rows="6" v-if="show" v-model="item.content" />
          <!-- <hb-tiny-mce-editor v-if="show" v-model="item.content" style="width:722px"></hb-tiny-mce-editor> -->
        </div>
      </div>
      <el-button type="primary" class="mt20" @click="addChoiceItem">+ 添加选项</el-button>
    </el-form-item>
    <el-form-item label="正确答案" required>
      <el-radio-group v-model="createQuestion.correctAnswerId" size="medium">
        <el-radio v-for="(item, index) in choiceItems" :key="index" :label="item.id">
          {{ resolverIndexToCharCode(index) }}
        </el-radio>
      </el-radio-group>
    </el-form-item>

    <el-form-item label="题目题析">
      <div class="rich-text">
        <el-input
          type="textarea"
          v-if="show"
          :rows="6"
          v-model="createQuestion.dissects"
          placeholder="请输入试题题目"
        />
      </div>
      <!-- <hb-tiny-mce-editor v-model="createQuestion.dissects" v-if="show"></hb-tiny-mce-editor> -->
    </el-form-item>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Mixins, Watch, Vue, PropSync } from 'vue-property-decorator'
  import { ExamUtil } from '@/store/module/exam/common/ExamUtil'
  import HbTinyMceEditor from '@hbfe/jxjy-admin-components/src/tinymce-editor/index.vue'
  import CreateRadioQuestionDto from '@api/service/management/resource/question/mutation/vo/create/CreateRadioQuestionVo'
  import { ChooseAnswerOptionVo } from '@api/service/management/resource/question/mutation/vo/ChooseAnswerOptionVo'
  import CreateRadioQuestionVo from '@api/service/management/resource/question/mutation/vo/create/CreateRadioQuestionVo'

  @Component({
    components: {
      HbTinyMceEditor
    }
  })
  export default class extends Vue {
    @PropSync('createQuestionInfo', {
      type: Object
    })
    createQuestion: CreateRadioQuestionVo
    @Watch('createQuestion', {
      deep: true
    })
    createQuestionChange(val: any) {
      console.log(val, '这是单选题val')
      // console.log(val, 'val的值val的值val的值')
    }
    @Prop({
      required: true,
      type: String
    })
    questionType: number

    @Prop({
      default: false
    })
    isUpdate: boolean

    /**
     * 对象嵌套太深，vue识别渲染很慢，故单独申明
     */
    choiceItems = new Array<ChooseAnswerOptionVo>()
    /**
     * vue 你炸这么弱啊。嵌套三层就识别不了啊
     */
    correctAnswer = '1'
    /**
     * ui常量
     */
    uiConfig = {
      // 当前选项的索引
      currentChoiceIndex: 0
    }
    show = false
    questionParams: CreateRadioQuestionDto

    @Watch('value', { deep: true })
    changeVal(val: any) {
      console.log(val, '111')
      this.$emit('input', val)
    }

    // created() {
    //   this.deploy()
    // }
    @Watch('choiceItems', {
      deep: true,
      immediate: true
    })
    choiceItemsChange(val: any) {
      if (val) {
        console.log(val, '试题选项')
      }
    }
    /**
     * 初始化加载
     */
    async init() {
      console.log(this.createQuestion, 'aaaaaaaaaaaaaaaaaa')
      this.choiceItems = new Array<ChooseAnswerOptionVo>()
      this.questionParams = this.createQuestion
      // this.questionParams.answerOptions
      this.choiceItems = this.questionParams.answerOptions
      if (!this.isUpdate) {
        // this.choiceItems = []

        // this.choiceItems
        // this.choiceItems = new Array<ChooseAnswerOptionVo>()
        this.choiceItems.push(this.productChoiceItem(1))
        this.choiceItems.push(this.productChoiceItem(2))
      }

      // this.createQuestion.correctAnswerId = '0'
      this.uiConfig.currentChoiceIndex = 2
      // if (this.value.questionContent.choiceItems.length < 1) {
      //   this.choiceItems.push(this.productChoiceItem(1))
      //   this.choiceItems.push(this.productChoiceItem(2))
      //   this.uiConfig.currentChoiceIndex = 2
      // } else {
      //   const length = this.value.questionContent.choiceItems.length
      //   const lastId = this.value.questionContent.choiceItems[length - 1]?.id
      //   if (!lastId) {
      //     throw new Error('>> 试题的选项id无效')
      //   }
      //   this.uiConfig.currentChoiceIndex = parseInt(lastId) + 1
      //   this.choiceItems = this.value.questionContent.choiceItems
      //   this.correctAnswer =
      //     this.value.questionContent.correctAnswer ||
      //     this.value.questionContent.choiceItems[0]?.id ||
      //     '1'
      // }
    }

    /**
     * 解析index为字母
     * @param index
     */
    resolverIndexToCharCode(index: number) {
      return ExamUtil.matchCharCode(index)
    }

    /**
     * 新增一个选项
     */
    async addChoiceItem() {
      if (this.choiceItems.length >= 20) {
        this.$message.error('选项添加已达到上限，无法继续添加。')
        return
      }
      this.uiConfig.currentChoiceIndex++
      this.choiceItems.push(this.productChoiceItem(this.uiConfig.currentChoiceIndex))
    }

    /**
     * 父组件调用
     * 提交最终数据
     */
    commit() {
      // this.value.questionContent.choiceItems = this.choiceItems
      // this.value.questionContent.correctAnswer = this.correctAnswer
    }

    /**
     * 基本表单校验
     */
    validForm() {
      if (!this.questionParams.topic) {
        this.$message.warning('请配置试题题目。')
        return false
      }

      if (this.choiceItems.length < 2) {
        this.$message.warning('单选题选项不能少于两个')
        return false
      }
      if (this.choiceItems.find((p: any) => !p.content)) {
        this.$message.warning('请配置单选题选项内容。')
        return false
      }
      let isCorrectAnswerDel = true
      this.questionParams.answerOptions.map(item => {
        if (item.id == this.questionParams.correctAnswerId) {
          isCorrectAnswerDel = false
        }
      })
      if (!this.questionParams.correctAnswerId || isCorrectAnswerDel) {
        this.$message.warning('正确答案不能为空')
        return false
      }
      return true
    }

    /**
     * 解决富文本问题。 暂时没有好的方法。待改todo
     */
    created() {
      // setTimeout(() => {}, 100)
      this.deploy()
    }

    deploy() {
      this.show = false
      if (!this.isUpdate) {
        // this.value.questionContent = new SingleChoice()
      }
      this.init()
      setTimeout(() => {
        this.show = true
      }, 100)
    }
    productChoiceItem(index: number) {
      const choice = new ChooseAnswerOptionVo()
      choice.id = index + ''
      return choice
    }
    // 试题上移
    questionUp(idx: number) {
      if (this.isUpdate) {
        this.createQuestion.doQuestionUp(this.createQuestion.answerOptions, idx)
      } else {
        this.createQuestion.doQuestionUp(this.choiceItems, idx)
      }
    }
    // 试题下移
    questionDown(idx: number) {
      if (this.isUpdate) {
        this.createQuestion.doQuestionDown(this.createQuestion.answerOptions, idx)
      } else {
        this.createQuestion.doQuestionDown(this.choiceItems, idx)
      }
    }
    // 试题删除
    questionDelete(idx: number) {
      this.createQuestion.answerOptions
      this.$confirm('确定删除该选项？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.deleteItem(idx)
        })
        .catch(() => {
          console.log('已取消删除')
        })
    }
    deleteItem(idx: number) {
      if (this.isUpdate) {
        this.createQuestion.doQuestionDelete(this.createQuestion.answerOptions, idx)
      } else {
        this.createQuestion.doQuestionDelete(this.choiceItems, idx)
      }
    }
  }
</script>
