<template>
  <el-drawer :title="title" :visible.sync="show" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="form" :model="category" label-width="auto" class="m-form f-mt20">
            <el-form-item label="所属节点：" required>
              <biz-courseware-category
                v-if="!isDetailMode && showCategory"
                :multiple="false"
                :check-strictly="true"
                v-model="categoryId"
              ></biz-courseware-category>
              <div v-else-if="category.parentId == '-1'">
                课件分类
              </div>
              <div v-else>
                {{ category.parentName }}
              </div>
            </el-form-item>
            <el-form-item label="分类名称：" required>
              <el-input
                v-model="category.name"
                clearable
                maxlength="30"
                show-word-limit
                v-if="!isDetailMode"
                placeholder="请输入分类名称，不超过30个字"
              />
              <div v-else>
                <el-skeleton animated :rows="1" v-if="!category.id" />
                {{ category.name }}
              </div>
            </el-form-item>
            <el-form-item class="m-btn-bar" v-if="!isDetailMode">
              <el-button @click="cancel">取消</el-button>
              <el-button type="primary" @click="doOperate" :loading="onOperating">保存</el-button>
              <!--              <el-checkbox v-model="createAnother" class="f-ml10">创建另外一个</el-checkbox>-->
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Emit, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { ElForm } from 'element-ui/types/form'
  import MutationUpdateCoursewareCategory from '@api/service/management/resource/courseware-category/mutation/MutationUpdateCoursewareCategory'
  import UpdateCoursewareCategoryDto from '@api/service/management/resource/courseware-category/mutation/dto/UpdateCoursewareCategoryDto'
  import CreateCoursewareCategoryDto from '@api/service/management/resource/courseware-category/mutation/dto/CreateCoursewareCategoryDto'
  import CoursewareCategory from '@api/service/management/resource/courseware-category/query/vo/CoursewareCategory'
  import CoursewareCategoryFactory from '@api/service/management/resource/courseware-category/CoursewareCategoryFactory'
  import MutationCreateCoursewareCategory from '@api/service/management/resource/courseware-category/mutation/MutationCreateCoursewareCategory'

  class Detail {
    parentName: string
    id: string
    name: string
    parentId: string
  }

  export enum Mode {
    detail = 'detail',
    modify = 'modify',
    create = 'create'
  }

  export enum CategoryComponent {
    course = 'biz-course-category',
    courseware = 'biz-courseware-category'
  }

  class Category {
    id: string
    name: string
    parentId = '123'

    constructor(id?: string, name?: string, parentId?: string) {
      this.id = id
      this.name = name
      this.parentId = parentId
    }

    reset() {
      this.id = ''
      this.name = ''
      this.parentId = ''
    }
  }

  @Component({
    components: {}
  })
  export default class Operation extends Vue {
    @Prop({
      type: String,
      default: CategoryComponent.courseware
    })
    switchComponent: CategoryComponent
    createAnother = false
    @Prop({
      type: String,
      default: ''
    })
    title: string
    courseModule = new CoursewareCategoryFactory()

    @Prop({
      type: Function,
      default() {
        return async (id: string) => {
          return Promise.resolve(id)
        }
      }
    })
    loadDetail: (id: string) => Promise<Detail>

    @Prop({
      type: Boolean,
      default: false
    })
    refreshData: boolean

    @Ref('form')
    form: ElForm
    rules = {
      name: [{ required: true, message: '请输入分类名称', trigger: ['change', 'blur'] }],
      parentId: [{ required: true, message: '请选择所属节点', trigger: ['change', 'blur'] }]
    }
    parentId: Array<string> = new Array<string>()

    showCategory = false

    //新建、修改、详情弹窗显隐
    show = false

    category: Category | UpdateCoursewareCategoryDto | CreateCoursewareCategoryDto | CoursewareCategory = new Category()
    mutationUpdate: MutationUpdateCoursewareCategory
    onOperating = false
    // 默认展示详情
    mode = Mode.detail

    get isDetailMode() {
      return this.mode === Mode.detail
    }

    @Watch('categoryId')
    changeCategoryId() {
      if (this.categoryId?.length) {
        this.category.parentId = this.categoryId[this.categoryId.length - 1]
      } else {
        this.category.parentId = null
      }
    }

    categoryId = new Array<string>()

    async open(mode: Mode, id?: string) {
      this.showCategory = false
      this.onOperating = false
      this.mode = mode
      this.show = true
      this.categoryId = new Array<string>()

      if (this.mode === Mode.modify) {
        this.mutationUpdate = await this.courseModule.mutationUpdate(id)
        this.category = this.mutationUpdate.UpdateCoursewareCategoryDto
        this.categoryId = await this.getIdList(this.category.parentId)
        this.showCategory = true
      } else if (this.mode === Mode.detail) {
        this.category = await this.courseModule.query.queryById(id)
      } else if (this.mode === Mode.create) {
        this.category = new Category()
        this.showCategory = true
      }
    }

    async getIdList(id: string) {
      const categoryList = await this.courseModule.query.queryReverserById(id)
      const idList = ['-1']
      categoryList.forEach(item => {
        if (item.id && item.id !== '') {
          idList.push(item.id)
        }
      })
      return idList
    }

    close() {
      this.show = false
      this.showCategory = false
    }

    @Emit('cancel')
    cancel() {
      this.category = new Category()
      this.categoryId = new Array<string>()
      this.close()
    }

    doOperate() {
      switch (this.mode) {
        case Mode.create:
          this.doCreate()
          break
        case Mode.modify:
          this.doModify()
          break
      }
    }

    validateForm() {
      if (!this.category.parentId) {
        this.$message.warning('所属节点不可为空')
        return false
      } else if (!this.category.name) {
        this.$message.warning('课件分类名称不可为空')
        return false
      } else return true
    }

    async doCreate() {
      try {
        if (!this.validateForm()) {
          return
        }
        //await this.form.validate()
        const createDto = new CreateCoursewareCategoryDto()
        createDto.parentId = this.category.parentId
        createDto.name = this.category.name
        this.onOperating = true
        const result = await createDto.save()
        if (result.isSuccess()) {
          this.$message.success('创建成功')
          if (!this.createAnother) {
            this.close()
          } else {
            this.category.reset()
          }
          setTimeout(() => {
            this.$emit('update:refreshData', true)
          }, 1000)
        } else {
          this.$message.error(result.message.toString())
        }
        this.onOperating = false
      } catch (e) {
        console.log(e)
        this.$message.warning('请完善表单')
      }
    }

    async doModify() {
      try {
        if (!this.validateForm()) {
          return
        }
        //await this.form.validate()
        this.onOperating = true
        const result = await this.mutationUpdate.doUpdate()
        if (result.isSuccess()) {
          this.$message.success('修改成功')
          this.close()
          setTimeout(() => {
            this.$emit('update:refreshData', true)
          }, 1000)
        } else {
          this.$message.error(result.message.toString())
        }
        this.onOperating = false
      } catch (e) {
        this.$message.warning('请完善表单')
      }
    }
  }
</script>
