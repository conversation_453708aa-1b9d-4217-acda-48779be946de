/**
 * 性别常量
 */
export enum Sex {
  /**
   * 男
   */
  MALE = 1,
  /**
   * 女
   */
  FEMALE = 2,
  /**
   * 中性
   */
  NEUTRAL = 3,
  /**
   * 未知
   */
  UNKNOWN = 4
}

/**
 * 唯一性值类型
 */
export enum UniqueType {
  /** 用户唯一性标识: 未指定 */
  UNIQUE_TYPE_NONE = -1,
  /** 用户唯一性标识: 居民身份证 */
  UNIQUE_TYPE_ID_CARD = 1,
  /** 用户唯一性标识: 军官证 */
  UNIQUE_TYPE_MILITARY_ID_CARD = 2,
  /** 用户唯一性标识: 港澳台居民居住证 */
  UNIQUE_TYPE_RESIDENT_CARD = 3,
  /** 用户唯一性标识: 护照 */
  UNIQUE_TYPE_PASSPORT_CARD = 4,
  /** 用户唯一性标识: 其他 */
  UNIQUE_TYPE_OTHER_CARD = 99
}

/**
 * 注册方式
 */
export enum RegisterType {
  /**
   * 平台开发注册
   */
  PLATFORM = 11,
  /**
   * 第三方互联之QQ
   */
  QQ = 21,
  /**
   * 第三方互联之新浪微博
   */
  SINA_WEIBO = 31,
  /**
   * 第三方互联之微信
   */
  WINXIN = 41,
  /**
   * 第三方互联之 微信公众号
   */
  WECHAT_OFFICIAL_ACCOUNT = 42,
  /**
   * 第三方互联之 微信小程序
   */
  WECHAT_MINI_PROGRAM = 43,
  /**
   * 第三方互联之 钉钉
   */
  REGISTER_TYPE_DING_TALK = 61,
  /**
   * 第三方互联之 外部来源
   */
  EXTSOR = 51
}

/**
 * 注册来源
 */
export enum SourceType {
  /**
   * 项目主网站
   */
  HOME = 1,
  /**
   * 安卓
   */
  ANDROID,
  /**
   * IOS 苹果
   */
  IOS
}

/**
 * 登录账号类型
 */
export enum LoginType {
  /**
   * 未知
   */
  UNKNOWN = -2,
  /**
   * 普通账号
   */
  NORMAL = 1,
  /**
   * 邮箱
   */
  EMAIL = 2,
  /**
   * 手机号
   */
  PHONE = 3,
  /**
   * 身份证号
   */
  IDENTITYCARD = 4
}
