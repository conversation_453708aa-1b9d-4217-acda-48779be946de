/*
 * @Description: 获取资讯 --- web
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-24 20:04:16
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-16 11:43:40
 */
import { Page } from '@hbfe/common'
import WebTypeNewsVo from '@api/service/customer/news/query/query-type-news-list/vo/WebNews'
import BasicDataQueryForestage, {
  NewsFrontQueryByCodeRequest,
  NewsQueryByAreaCodePathRequest,
  NewsQueryCommonRequest,
  TrainingChannelNewsQueryCommonRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import Context from '@api/service/common/context/Context'

class QueryWebTypeNewsList {
  /**
   * 获取资讯类别 - 一级目录
   */
  async queryNewsRootCategory() {
    const { data } = await BasicDataQueryForestage.listRootNewsCategory(1)
    return data
  }
  /**
   * 获取资讯类别 - 二级目录
   * @param necId 资讯分类ID
   */
  async queryNewsChildrenCategory(necId: string) {
    const { data } = await BasicDataQueryForestage.listChildNewsCategory({ status: 1, necId })
    return data
  }
  /**
   * 查询最多资讯的资讯分类列表
   * @param necId 需要排除的资讯分类ID
   */
  async queryListTopNewsCategory(necId: string) {
    const { data } = await BasicDataQueryForestage.listTopNewsCategory({ topNum: 3, necId })
    return data
  }
  /**
   * 获取资讯列表
   * @param page
   * @param necId 资讯分类ID
   * @returns
   */
  async queryNewsList(page: Page, necId: string, areaCodePath?: string): Promise<Array<WebTypeNewsVo>> {
    const request = new NewsQueryByAreaCodePathRequest()
    request.necId = necId
    request.areaCodePath = areaCodePath || undefined
    const { data } = await BasicDataQueryForestage.pageSimpleNewsByPublishAndAreaCodePath({ page, request })
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const webNewsArr = new Array<WebTypeNewsVo>()
    data.currentPageData.map(item => webNewsArr.push(WebTypeNewsVo.from(item)))
    return webNewsArr
  }

  /**
   * 获取资讯列表
   * @param page
   * @param necId 资讯分类ID
   * @returns
   */
  async queryNewsListByNecId(page: Page, necId: string): Promise<Array<WebTypeNewsVo>> {
    const request = new NewsQueryByAreaCodePathRequest()
    request.necId = necId
    const { data } = await BasicDataQueryForestage.pageCommonSimpleNewsByPublish({ request, page })
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const webNewsArr = new Array<WebTypeNewsVo>()
    data.currentPageData.map(item => webNewsArr.push(WebTypeNewsVo.from(item)))
    return webNewsArr
  }

  /**
   * 根据分类id，专题id获取专题资讯列表
   * @param page
   * @param necId 资讯分类ID
   * @param specialId 专题id
   * @returns
   */
  async querySpecialNewsListByNecIdAndSpecialId(
    page: Page,
    necId: string,
    specialId: string
  ): Promise<Array<WebTypeNewsVo>> {
    const request = new TrainingChannelNewsQueryCommonRequest()
    request.necId = necId
    request.specialSubjectId = specialId
    const { data } = await BasicDataQueryForestage.pageTrainingChannelCommonSimpleNewsByPublish({ request, page })
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const webNewsArr = new Array<WebTypeNewsVo>()
    data.currentPageData.map(item => webNewsArr.push(WebTypeNewsVo.from(item)))
    return webNewsArr
  }

  /**
   * 根据资讯分类codes查询专题首页资讯
   * @param page 分页
   * @param codes 资讯分类code
   * @param specialSubjectId 专题id
   */
  async querySpecialHomeByCodeList(page: Page, codes: Array<string>, specialSubjectId: string) {
    const request = new TrainingChannelNewsQueryCommonRequest()
    request.necIdList = codes
    request.specialSubjectId = specialSubjectId
    const res = await BasicDataQueryForestage.pageTrainingChannelCommonSimpleNewsByPublish({ request: request, page })
    const resultList = new Array<WebTypeNewsVo>()
    if (res?.data?.currentPageData?.length) {
      res.data.currentPageData.map(item => resultList.push(WebTypeNewsVo.from(item)))
    }
    return resultList
  }
}
export default QueryWebTypeNewsList
