import ActivityManangeDetailModel from '@api/service/customer/course/query/vo/ActivityManangeDetail'
import KnowledgeDataGateway, {
  SubmitLearningExperienceRequest,
  SaveLearningExperienceRequest,
  ApplyLearningExperienceRequest,
  SubmitLearningExperienceResponse,
  ApplyLearningExperienceResponse,
  CancelStudentLearningExperienceRequest,
  CheckLearningExperienceRequest,
  VerifyCourseRequest
} from '@api/ms-gateway/ms-knowledge-v1'
import CourseLearningForestage, {
  StudentLearningExperienceRequest,
  CourseInSchemeRequest,
  CourseInfoRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import CourseLearningBackstage, {
  LearningExperienceTopicRequest,
  ParticipateType,
  LearningExperienceTopicResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { AnswerMethodEnum, LearningExperienceEnum } from '@api/service/management/activity/enum/ActivityEnum'
import Page from '@api/service/common/models/page-query/Page'
import { Response } from '@hbfe/common'
export default class ActivityManangeModel {
  /**
   * 学习心得id
   */
  ActivityManangeId = ''
  /**
   * 学习心得详情
   */
  ActivityManange: ActivityManangeDetailModel = new ActivityManangeDetailModel()

  // 查询详情
  async queryDetail() {
    const request = new StudentLearningExperienceRequest()
    request.studentLearningExperienceIds = [this.ActivityManangeId]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    const res = await CourseLearningForestage.pageLearningExperienceInStudent({ page, request })
    if (!res.status.isSuccess() || !res.data.currentPageData.length) {
      return new ActivityManangeDetailModel()
    }
    try {
      const data = res.data.currentPageData[0]
      this.ActivityManange = ActivityManangeDetailModel.from(data)
      this.ActivityManangeId = data.studentLearningExperienceId
      const courseId = data.experienceTypeInfo[0]
      const schemeId = data.studentLearning.schemeId
      const courseRes = await this.queryCourseDetail(courseId, schemeId)
      this.ActivityManange.courseDetail.id = courseId
      this.ActivityManange.courseDetail.name = courseRes?.course?.courseName
      this.ActivityManange.outLineId = courseRes?.courseOfCourseTrainingOutline?.outlineId
    } catch (error) {
      console.error(error, '学习心得列表 error')
    }
    // 提交内容（如果为文本时，存储关联文本id，如果是附件是一个json结构，包含名称和地址） fileName fileUrl
    if (this.ActivityManange.answerMethod == AnswerMethodEnum.UPLOAD) {
      try {
        const res = await CourseLearningBackstage.getLearningExperienceContentInServicer(
          this.ActivityManange.LearningExperience
        )
        this.ActivityManange.LearningExperience = res.data
        const obj = JSON.parse(this.ActivityManange.LearningExperience)
        this.ActivityManange.fileName = obj.fileName
        this.ActivityManange.LearningExperience = obj.fileUrl
      } catch (error) {
        console.log(error, 'fileName fileUrl error')
      }
    } else if (this.ActivityManange.answerMethod == AnswerMethodEnum.EDIT) {
      const res = await CourseLearningBackstage.getLearningExperienceContentInServicer(
        this.ActivityManange.LearningExperience
      )
      this.ActivityManange.LearningExperience = res.data
    }
    return this.ActivityManange
  }
  /**
   * 查询主题配置
   */
  async queryStudentExperienceList(topicId: string) {
    const experienceRequest = new LearningExperienceTopicRequest()
    experienceRequest.topicIds = [topicId]
    const result = await CourseLearningBackstage.listLearningExperienceTopic(experienceRequest)
    if (result.status.isSuccess()) {
      const data = result.data[0]
      this.ActivityManange.theme = data.experienceTopicName
      // const res = await CourseLearningBackstage.getLearningExperienceContentInServicer(data.decriptionContentId)
      this.ActivityManange.content = data.descriptionContent
      this.ActivityManange.joinStartTime = data.startTime
      this.ActivityManange.joinEndTime = data.endTime
      if (data.participateType === ParticipateType.SUBMIT_FILE) {
        this.ActivityManange.answerMethod = AnswerMethodEnum.UPLOAD
        this.ActivityManange.fileSize = data.submitLimitNum
      } else if (data.participateType === ParticipateType.EDIT_ONLINE) {
        this.ActivityManange.answerMethod = AnswerMethodEnum.EDIT
        this.ActivityManange.wordLimit = data.submitLimitNum
      }
      if (data.experienceType === 'COURSE') {
        this.ActivityManange.learningExperienceType = LearningExperienceEnum.COURSE
      } else if (data.experienceType === 'SCHEME') {
        this.ActivityManange.learningExperienceType = LearningExperienceEnum.CLASS
      }
    }
    // if (result.status.isSuccess()) {
    //   return result.data
    // } else {
    //   return [] as Array<LearningExperienceTopicResponse>
    // }
    return this.ActivityManange
  }
  // 提交学习心得
  async submitLearningExperience(learningExperienceToken: string): Promise<Response<SubmitLearningExperienceResponse>> {
    const request = new SubmitLearningExperienceRequest()
    // 文件传json
    let json
    if (this.ActivityManange.answerMethod == AnswerMethodEnum.UPLOAD) {
      json = JSON.stringify({
        fileUrl: this.ActivityManange.LearningExperience,
        fileName: this.ActivityManange.fileName
      })
    } else {
      json = this.ActivityManange.LearningExperience
    }
    const textRes = await this.saveLearningExperienceTopicContent(json)
    if (!textRes.contentId) {
      console.error('获取文本id失败', textRes)
      return
    }
    request.content = textRes.contentId
    request.learningExperienceToken = learningExperienceToken
    request.participateType = this.ActivityManange.answerMethod
    request.learningExperienceTopicType = this.ActivityManange.learningExperienceType
    const result = await KnowledgeDataGateway.submitLearningExperience(request)
    return result
  }
  // 撤回学习心得
  async revokeLearningExperience(studentExperienceId: string, learningExperienceTopicType: number) {
    const request = new CancelStudentLearningExperienceRequest()
    request.studentExperienceId = studentExperienceId
    request.learningExperienceTopicType = learningExperienceTopicType
    const result = await KnowledgeDataGateway.cancelStudentLearningExperience(request)
    return result
  }
  // 申请提交学习心得（立即参加） 提交学习心得草稿 获得学习心得id
  async applyLearningExperience(studentLearningToken: string): Promise<ApplyLearningExperienceResponse> {
    const tokenRequest = new ApplyLearningExperienceRequest()
    tokenRequest.studentLearningToken = studentLearningToken
    tokenRequest.resourceType = this.ActivityManange.learningExperienceType
    tokenRequest.outlineId = this.ActivityManange.outLineId
    // 班级id or 课程id
    if (this.ActivityManange.learningExperienceType === LearningExperienceEnum.CLASS) {
      tokenRequest.resourceId = this.ActivityManange.schemeId
    } else {
      tokenRequest.resourceId = this.ActivityManange.courseDetail.id
    }
    tokenRequest.learningExperienceTopicId = this.ActivityManange.themeId
    const result = await KnowledgeDataGateway.applyLearningExperience(tokenRequest)
    return result.data
  }
  // 保存学习心得草稿
  async saveLearningExperience(learningExperienceToken: string) {
    const request = new SaveLearningExperienceRequest()
    request.learningExperienceToken = learningExperienceToken
    request.participateType = this.ActivityManange.answerMethod
    request.content = this.ActivityManange.content
    request.learningExperienceTopicType = this.ActivityManange.learningExperienceType
    const result = await KnowledgeDataGateway.saveLearningExperience(request)
    return result.data
  }
  //保存心得主题文本信息
  async saveLearningExperienceTopicContent(content: string) {
    const result = await KnowledgeDataGateway.saveLearningExperienceTopicContent(content)
    return result.data
  }
  //更新心得主题文本信息
  async updateLearningExperienceTopicContent(contentId: string, content: string) {
    const result = await KnowledgeDataGateway.updateLearningExperienceTopicContent({ contentId, content })
    return result.data
  }
  // 重新提交校验 learningExperienceTopicId 学员心得主题id
  async reSubmitCheck(learningExperienceToken: string, learningExperienceTopicType: number) {
    if (new Date() > new Date(this.ActivityManange.joinEndTime)) {
      return { code: '99', msg: '已超过参加时间，无法提交' }
    }
    const request = new CheckLearningExperienceRequest()
    request.learningExperienceToken = learningExperienceToken
    request.learningExperienceTopicType = learningExperienceTopicType
    const result = await KnowledgeDataGateway.check(request)
    return result.data
  }
  // 通过课程id获取名称
  async queryCourseDetail(courseId: string, schemeId: string) {
    const request = new CourseInSchemeRequest()
    request.course = new CourseInfoRequest()
    request.course.courseId = courseId
    request.schemeId = schemeId
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    const response = await CourseLearningBackstage.pageCourseInSchemeInServicer({ page, request })
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('获取课程详情失败', response)
      return Promise.reject(response)
    }
    return response.data.currentPageData[0]
  }

  /**
   * 校验课程是否被删除
   */
  async verifyExists() {
    const request = new VerifyCourseRequest()
    request.learningExperienceTopicId = this.ActivityManange.themeId
    request.outlineId = this.ActivityManange.outLineId
    if (this.ActivityManange.learningExperienceType === LearningExperienceEnum.CLASS) {
      request.resourceId = this.ActivityManange.schemeId
    } else {
      request.resourceId = this.ActivityManange.courseDetail.id
    }
    request.resourceType = this.ActivityManange.learningExperienceType
    request.studentNo = this.ActivityManange.studentNo
    const res = await KnowledgeDataGateway.verifyExists(request)
    if (res.status.isSuccess()) {
      return res.data
    } else {
      return { msg: '校验失败', code: -1 }
    }
  }
}
