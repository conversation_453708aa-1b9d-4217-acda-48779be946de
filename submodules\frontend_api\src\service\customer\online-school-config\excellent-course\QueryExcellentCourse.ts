import MsServicerSeriesV1 from '@api/ms-gateway/ms-servicer-series-v1'
import { Page } from '@hbfe/common'
import ExcellentCourseCategory from '@api/service/customer/online-school-config/excellent-course/vo/ExcellentCourseCategory'
import SimpleCourseDetail from '@api/service/customer/online-school-config/excellent-course/vo/SimpleCourseDetail'
import { uniq } from 'lodash'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CourseCategoryRequest,
  CourseRequest,
  CourseSortEnum,
  CourseSortRequest,
  TeacherResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import CategoryTreeItem from '@api/service/customer/online-school-config/excellent-course/vo/CategoryTreeItem'
import PlatformTrainingChannelBackGateway from '@api/platform-gateway/platform-training-channel-back-gateway'

class QueryExcellentCourse {
  hasCategory = false
  categories: Array<ExcellentCourseCategory> = new Array<ExcellentCourseCategory>()
  courses: Array<SimpleCourseDetail> = new Array<SimpleCourseDetail>()
  categoryTree: Array<CategoryTreeItem> = new Array<CategoryTreeItem>()

  /**
   * 查询精品课程结构
   */
  async queryCourses() {
    const { data } = await MsServicerSeriesV1.getExcellentCourses()
    let haveExcellent = (data?.courses?.length && !data.usedCategory) || (data?.categories?.length && data.usedCategory)
    if (data?.categories?.length && data.usedCategory) {
      let flag = false
      data.categories.map(item => {
        if (item?.courses?.length) {
          flag = true
        }
      })
      if (!flag) {
        haveExcellent = false
      }
    }

    if (haveExcellent) {
      this.hasCategory = data.usedCategory
      if (this.hasCategory) {
        this.categories = data.categories.map(ExcellentCourseCategory.from)
        // 获取选中课程分类的 id 集合
        const categoryIdList = data.categories.map(item => item.categoryId)
        const {
          data: result
        } = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listCourseCategoryInServicer({
          categoryIdList: categoryIdList
        })
        this.categories
          .sort((categoryA: ExcellentCourseCategory, categoryB: ExcellentCourseCategory) => {
            return categoryA.sort - categoryB.sort
          })
          .forEach(cat => {
            const id = cat.id
            const courseCat = result.find(courseCategory => {
              return courseCategory.id === id
            })
            if (courseCat) {
              cat.name = courseCat.name
            }
          })
      } else {
        this.courses = data.courses.map(SimpleCourseDetail.from)
      }

      let courseIdList: Array<string> = []
      // 判断是否有分类，获取课程 id 集合
      if (this.hasCategory) {
        this.categories.forEach(item => {
          item.courses.forEach((course: SimpleCourseDetail) => {
            courseIdList.push(course.id)
          })
        })
      } else {
        courseIdList = this.courses.map(course => {
          return course.id
        })
      }
      const page = new Page()
      page.pageSize = courseIdList.length
      if (courseIdList.length) {
        const courseList = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
          page: page,
          request: { courseIdList }
        })

        // const teacherList = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listTeacherInServicer()
        const teachersId = new Array<string>()
        if (this.hasCategory) {
          this.categories.forEach(item => {
            item.courses.forEach((course: SimpleCourseDetail) => {
              const findCourse = courseList.data.currentPageData.find(courseResource => courseResource.id === course.id)
              if (findCourse) {
                findCourse.teacherIds.forEach((teacherId, index) => {
                  if (index > 1) {
                    return
                  }
                  teachersId.push(teacherId)
                })
              }
              // teachersId.push(...findCourse.teacherIds)
              course.from(findCourse)
            })
          })
        } else {
          this.courses.forEach((course: SimpleCourseDetail) => {
            const findCourse = courseList.data.currentPageData.find(courseResource => courseResource.id === course.id)
            if (findCourse) {
              findCourse.teacherIds.forEach((teacherId, index) => {
                if (index > 1) {
                  return
                }
                teachersId.push(teacherId)
              })
            }
            // teachersId.push(...findCourse.teacherIds)
            course.from(findCourse)
          })
        }
        let teacherFindList = new Array<TeacherResponse>()
        if (teachersId.length) {
          const queryTeacherRes = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listTeacherInServicer(
            uniq(teachersId)
          )
          if (queryTeacherRes?.data?.length) {
            teacherFindList = queryTeacherRes.data
          }
        }

        if (this.hasCategory) {
          this.categories.forEach(item => {
            item.courses.forEach((course: SimpleCourseDetail) => {
              course.teachers.forEach(teacher => {
                const findOut = teacherFindList.find(remoteTeacher => remoteTeacher.id === teacher.id)
                if (findOut) {
                  teacher.from(findOut)
                }
              })
            })
          })
        } else {
          this.courses.forEach((course: SimpleCourseDetail) => {
            const findCourse = courseList.data.currentPageData.find(courseResource => courseResource.id === course.id)
            teachersId.push(...findCourse.teacherIds)
            course.from(findCourse)

            course.teachers.forEach(teacher => {
              const findOut = teacherFindList.find(remoteTeacher => remoteTeacher.id === teacher.id)
              if (findOut) {
                teacher.from(findOut)
              }
            })
          })
        }
      }
    } else {
      // 没有配置精品课程就去取最新创建可用课程的前七门
      const page = new Page()
      page.pageSize = 8
      const sortRequest = new Array<CourseSortRequest>()
      const request = new CourseRequest()
      const sort = new CourseSortRequest()
      sort.courseSort = CourseSortEnum.CREATE_TIME
      sort.sortType = 1
      request.enable = 1

      const res = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
        page: page,
        request: request,
        sort: sortRequest
      })
      const resList = (res?.data?.currentPageData?.length && res.data.currentPageData) || []
      const resCourseList = new Array<SimpleCourseDetail>()
      const teacherIds = new Array<string>()
      resList.map(item => {
        const course = new SimpleCourseDetail()
        course.id = item.id
        course.from(item)
        resCourseList.push(course)
        if (item?.teacherIds?.length) {
          teacherIds.push(...item.teacherIds)
        }
      })
      let teacherFindList = new Array<TeacherResponse>()
      if (teacherIds.length) {
        const teacherRes = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listTeacherInServicer(
          teacherIds
        )
        if (teacherRes?.data?.length) {
          teacherFindList = teacherRes.data
        }
      }
      resCourseList.map(course => {
        course.teachers.forEach(teacher => {
          const findOut = teacherFindList.find(remoteTeacher => remoteTeacher.id === teacher.id)
          if (findOut) {
            teacher.from(findOut)
          }
        })
      })
      this.courses = resCourseList
    }
  }

  /**
   * 初始化查询分类树
   */
  async initQueryCategory() {
    this.categoryTree = new Array<CategoryTreeItem>()
    const request = new CourseCategoryRequest()
    request.parentId = '-1'
    const res = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listCourseCategoryInServicer(request)
    const allNode = new CategoryTreeItem()
    allNode.id = 'all_node'
    allNode.name = '全部'
    allNode.haveChildren = false

    this.categoryTree.push(allNode)

    res?.data?.length &&
      res.data.map(item => {
        this.categoryTree.push(CategoryTreeItem.form(item))
      })

    return this.categoryTree
  }

  /**
   * 查询指定分类下的子分类
   * @param categoryTreeItem
   */
  async queryChildrenCategoryById(categoryTreeItem: CategoryTreeItem) {
    const request = new CourseCategoryRequest()
    request.parentId = categoryTreeItem.id
    const res = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listCourseCategoryInServicer(request)

    if (res?.data?.length) {
      categoryTreeItem.children = res.data.map(CategoryTreeItem.form)
    }
    if (!categoryTreeItem.children.length) {
      categoryTreeItem.haveChildren = false
    }

    return categoryTreeItem
  }

  /**
   * 查询全部课程
   * @param page 分页
   * @param courseCategoryId 课程分类id
   * @param sort 排序
   */
  async queryAllCourseByCategory(page: Page, courseCategoryId: string, sort?: Array<CourseSortRequest>) {
    if (!courseCategoryId) {
      return []
    }

    const request = new CourseRequest()
    request.categoryIdList = courseCategoryId === 'all_node' ? [] : [courseCategoryId]
    request.enable = 1

    const res = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
      page: page,
      request: request,
      sort: sort
    })
    page.totalSize = res.data?.totalSize
    page.totalPageSize = res.data?.totalPageSize
    const resList = (res?.data?.currentPageData?.length && res.data.currentPageData) || []
    const resCourseList = new Array<SimpleCourseDetail>()
    const teacherIds = new Array<string>()

    resList.forEach(item => {
      const course = new SimpleCourseDetail()
      course.id = item.id
      course.from(item)
      resCourseList.push(course)
      if (item.teacherIds) {
        item.teacherIds.forEach((teacherId, index) => {
          if (index > 1) {
            return
          }
          teacherIds.push(teacherId)
        })
      }
    })
    let teacherFindList = new Array<TeacherResponse>()
    if (teacherIds.length) {
      const teacherRes = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listTeacherInServicer(
        teacherIds
      )
      if (teacherRes?.data?.length) {
        teacherFindList = teacherRes.data
      }
    }
    resCourseList.map(course => {
      course.teachers.forEach(teacher => {
        const findOut = teacherFindList.find(remoteTeacher => remoteTeacher.id === teacher.id)
        if (findOut) {
          teacher.from(findOut)
        }
      })
    })
    return resCourseList
  }

  /**
   * 查询专题下精品课程
   */
  async queryCourseList(page: Page, trainingChannelId: string, categoryId = '-1') {
    const result: Array<SimpleCourseDetail> = []
    const res = await PlatformTrainingChannelBackGateway.pageTrainingChannelSelectCourseInSubject({
      page: page,
      request: {
        trainingChannelId: trainingChannelId,
        selectedCourseCategoryId: categoryId
      }
    })
    page.totalSize = res.data?.totalSize
    page.totalPageSize = res.data?.totalPageSize
    const courseIds = res?.data?.currentPageData?.map(item => item.courseId) || []
    if (courseIds.length) {
      const request = new CourseRequest()
      request.courseIdList = courseIds
      // 查询课程及教师信息
      const resCourseInfo = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
        page: { pageNo: 1, pageSize: courseIds.length },
        request
      })
      const teacherIds: string[] = []
      courseIds.forEach(item => {
        const course = new SimpleCourseDetail()
        course.id = item
        const courseInfo = resCourseInfo.data?.currentPageData?.find(item => item.id === course.id)
        if (courseInfo) {
          course.from(courseInfo)
          result.push(course)
          if (courseInfo?.teacherIds?.length) {
            teacherIds.push(...courseInfo.teacherIds.slice(0, 2))
          }
        }
      })
      const teacherMap = new Map<string, TeacherResponse>()
      if (teacherIds.length) {
        ;(
          await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.listTeacherInServicer([...new Set(teacherIds)])
        )?.data?.forEach(item => {
          teacherMap.set(item.id, item)
        })
        result.map(course => {
          course.teachers.forEach(teacher => {
            const findOut = teacherMap.get(teacher.id)
            if (findOut) {
              teacher.from(findOut)
            }
          })
        })
      }
    }
    return result
  }
}

export default QueryExcellentCourse
