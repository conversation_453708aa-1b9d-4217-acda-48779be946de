import { CreateReceiveAccountRequest, ReceiveAccountExtProperty } from '@api/ms-gateway/ms-trade-configuration-v1'
import CreateReceiveAccountVo from './CreateReceiveAccountVo'
import MutationReceiveAccountVo from './MutationReceiveAccountVo'

class CreateAliPayReceiveAccountVo extends CreateReceiveAccountVo {
  /**
   * 支付账号类型id
   * 支付宝:ALIPAY
   * 微信：WXPAY
   * 支付宝H5:ALIPAYH5
   * 微信H5：WXPAYH5
   */
  paymentChannelId = ''
  /**
   * 支付宝密钥 -- 原型给的支付宝密钥不需要了
   */

  /**
   * 合作者身份ID
   */
  partner = ''
  /**
   * 支付宝应用私钥
   */
  privateKey = ''
  /**
   * 支付宝公钥
   */
  publicKey = ''
  /**
   * 支付宝应用id
   */
  appId = ''

  constructor(type: string) {
    super()
    this.paymentChannelId = type
  }

  from(mutationReceiveAccountVo: MutationReceiveAccountVo) {
    this.accountType = mutationReceiveAccountVo.accountType
    this.accountNo = mutationReceiveAccountVo.accountNo
    this.accountName = mutationReceiveAccountVo.accountName
    this.qrScanPrompt = mutationReceiveAccountVo.qrScanPrompt
    this.taxPayerId = mutationReceiveAccountVo.taxPayerId
    this.refundWay = mutationReceiveAccountVo.refundWay
    this.paymentChannelId = mutationReceiveAccountVo.paymentChannelId
    this.partner = mutationReceiveAccountVo.partner
    this.privateKey = mutationReceiveAccountVo.privateKey
    this.publicKey = mutationReceiveAccountVo.publicKey
    this.appId = mutationReceiveAccountVo.aliPayAppId
  }

  to() {
    const createReceiveAccountRequest = new CreateReceiveAccountRequest()
    createReceiveAccountRequest.accountType = 1
    createReceiveAccountRequest.accountNo = this.accountNo
    createReceiveAccountRequest.paymentChannelId = this.paymentChannelId
    createReceiveAccountRequest.name = this.accountName
    createReceiveAccountRequest.qrScanPrompt = this.qrScanPrompt
    createReceiveAccountRequest.refundWay = this.refundWay
    createReceiveAccountRequest.taxPayerId = this.taxPayerId
    createReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    createReceiveAccountRequest.properties.push({ name: 'partner', value: this.partner })
    createReceiveAccountRequest.properties.push({ name: 'privateKey', value: this.privateKey })
    createReceiveAccountRequest.properties.push({ name: 'publicKey', value: this.publicKey })
    createReceiveAccountRequest.properties.push({ name: 'appId', value: this.appId })
    return createReceiveAccountRequest
  }
}
export default CreateAliPayReceiveAccountVo
