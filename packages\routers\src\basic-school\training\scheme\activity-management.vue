<!--<route-meta>-->
<!--{-->
<!--"isMenu": true,-->
<!--"title": "活动管理",-->
<!--"sort": 4-->
<!--}-->
<!--</route-meta>-->
<!--<script lang="ts">-->
<!--  import SchemeTrainingRequireField from '@hbfe/jxjy-admin-scheme/src/activity-management.vue'-->
<!--  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'-->
<!--  import { WXGLY } from '@/models/RoleTypes'-->
<!--  @RoleTypeDecorator({-->
<!--    query: [WXGLY],-->
<!--    modify: [WXGLY],-->
<!--    ActivityManage: [WXGLY],-->
<!--    ActivityManageTab: [WXGLY],-->
<!--    handleAction: [WXGLY],-->
<!--    handleSearch: [WXGLY],-->
<!--    handleExport: [WXGLY],-->
<!--    handleAllExport: [WXGLY],-->
<!--    handleAudit: [WXGLY],-->
<!--    DetailDialog: [WXGLY]-->
<!--  })-->
<!--  export default class extends SchemeTrainingRequireField {}-->
<!--</script>-->
