import { Page } from '@hbfe/common'
import CheckAccountParam from '@api/service/management/trade/single/checkAccount/query/vo/CheckAccountParam'
import CheckAccountListResponse from '@api/service/management/trade/single/checkAccount/query/vo/CheckAccountListResponse'
import { OrderRequest, ReturnOrderRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import RefundCheckAccountParam from '@api/service/management/trade/single/checkAccount/query/vo/RefundCheckAccountParam'
import RefundCheckAccountListResponse from '@api/service/management/trade/single/checkAccount/query/vo/RefundCheckAccountListResponse'
import UserModule from '@api/service/management/user/UserModule'
import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'

export default abstract class QueryCheckAccountBase {
  /**
   * 报名订单分页查询
   * @param page 页数
   * @param queryCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<CheckAccountListResponse>
   */
  abstract queryOfRegistrationOrder(
    page: Page,
    checkAccountParam: CheckAccountParam
  ): Promise<Array<CheckAccountListResponse>>

  abstract statisticOrderInServicer(orderRequest: OrderRequest): Promise<void>

  abstract statisticOrderInServicerRemoveTotalPeriod(orderRequest: OrderRequest): Promise<void>

  /**
   * 退款订单分页查询
   * @param page 页数
   * @param queryRefundCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<RefundCheckAccountListResponse>
   */
  abstract queryOfRefundOrder(
    page: Page,
    queryRefundCheckAccountParam: RefundCheckAccountParam
  ): Promise<Array<RefundCheckAccountListResponse>>

  abstract queryStatisticReturnOrder(request: ReturnOrderRequest): Promise<void>

  /**
   * 获取用户信息
   * @param ids id数组
   */
  protected async getUserInfo(ids: Array<string>) {
    // 根据用户ID获取用户信息
    const response = await UserModule.queryUserFactory.queryStudentList.queryStudentListInSubject(ids)
    const userIdMap: Map<string, StudentUserInfoVo> = new Map()
    response.data.forEach(item => {
      userIdMap.set(item.userId, item)
    })
    return userIdMap
  }
}
