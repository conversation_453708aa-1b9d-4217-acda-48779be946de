"""独立部署的微服务,K8S服务名:ms-course-resource-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出全部课程包导入结果数据
		@param mainTaskId 主任务id
		@return 课程包导入结果数据
	"""
	exportAllCoursePackageImportResult(mainTaskId:String!):ExportCoursePackageImportResultResponse
	"""导出错误课程包导入结果数据
		@param mainTaskId 主任务id
		@return 课程包导入结果数据
	"""
	exportErrorCoursePackageImportResult(mainTaskId:String!):ExportCoursePackageImportResultResponse
	"""分页查询课程包导入任务数据
		@param page    分页信息
		@param request 查询条件
		@return 课程包导入任务数据
	"""
	pageCoursePackageImportTask(page:Page,request:PageCoursePackageImportTaskRequest):CoursePackageImportTaskResponsePage @page(for:"CoursePackageImportTaskResponse")
	"""分页查询课程包导入任务数据
		@param page    分页信息
		@param request 查询条件
		@return 课程包导入任务数据
	"""
	pageCoursewareImportTask(page:Page,request:PageCoursewareImportTaskRequest):CoursewareImportTaskResponsePage @page(for:"CoursewareImportTaskResponse")
}
type Mutation {
	"""批量导入创建课件
		@param request
	"""
	batchCreateCourseware(request:BatchImportCoursewareCreateRequest):Void
	"""批量导入课程包
		@param filePath 课程包文件路径
	"""
	batchImportCoursePackage(filePath:String):Void
	"""功能描述 : 校验课程
		@date : 2024/8/5 14:12
		@param checkCourseRequest :
		@return : void
	"""
	checkCourse(checkCourseRequest:CheckCourseRequest):CheckCourseResponse
	"""检查课程包的名称或显示名称是否重复
		@param request
		@return
	"""
	checkCoursePackageForName(request:CheckCoursePackageRequest):GeneralResponse
	"""校验课件名称是否重复
		@param request 校验信息
		@return 校验结果
	"""
	checkCourseware(request:CheckCoursewareRequest):GeneralResponse
	"""非业务接口,生成配置类
		@param request
	"""
	configJSON(request:ConfigRequest):Void
	"""复制课程包
		@param id      复制的课程包
		@param newName 课程包名称
	"""
	copyCoursePackage(id:String,newName:String):Void
	"""创建课程
		@param createRequest 课程创建信息
	"""
	createCourse(createRequest:CourseCreateRequest):Void
	"""创建课程分类
		@param createRequest 课程分类创建信息
	"""
	createCourseCategory(createRequest:CourseCategoryCreateRequest):Void
	"""课程包创建
		@param createRequest 课程包创建信息
	"""
	createCoursePackage(createRequest:CoursePackageCreateRequest):Void
	"""创建课件
		@param request
	"""
	createCourseware(request:CoursewareCreateRequest):GeneralMutationResponse
	"""创建课件分类
		@param createRequest 课件分类创建信息
	"""
	createCoursewareCategory(createRequest:CoursewareCategoryCreateRequest):Void
	"""停用课程
		@param courseId 课程id
	"""
	disableCourse(courseId:String):Void
	"""停用课件
		@param coursewareId 课件Id
	"""
	disableCourseware(coursewareId:String):Void
	"""启用课程
		@param courseId 课程id
	"""
	enableCourse(courseId:String):Void
	"""启用课件
		@param coursewareId 课件Id
	"""
	enableCourseware(coursewareId:String):Void
	"""导出课程包导入信息执行结果
		@param param 查询信息
		@return 导入课程包信息执行结果
	"""
	exportCoursewareImportResult(param:ExportCoursePackageParam):String
	"""删除课程
		@param courseId 课程id
	"""
	removeCourse(courseId:String):Void
	"""删除课程分类
		@param courseCategoryId
	"""
	removeCourseCategory(courseCategoryId:String):Void
	"""课程包移除
		@param id 课程包id
	"""
	removeCoursePackage(id:String):Void
	"""删除课件
		@param coursewareId 课件Id
	"""
	removeCourseware(coursewareId:String):Void
	"""删除课件分类
		@param coursewareCategoryId 课件分类id
	"""
	removeCoursewareCategory(coursewareCategoryId:String):Void
	"""更新课程
		@param updateRequest 课程更新信息
	"""
	updateCourse(updateRequest:CourseUpdateRequest):Void
	"""更新课程分类
		@param updateRequest 课程分类更新信息
	"""
	updateCourseCategory(updateRequest:CourseCategoryUpdateRequest):Void
	"""课程包更新
		@param updateRequest 课程包更新信息
	"""
	updateCoursePackage(updateRequest:CoursePackageUpdateRequest):Void
	"""修改课件
		@param request
	"""
	updateCourseware(request:CoursewareUpdateRequest):GeneralMutationResponse
	"""更新课件分类
		@param updateRequest 课件分类更新信息
	"""
	updateCoursewareCategory(updateRequest:CoursewareCategoryUpdateRequest):Void
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""@author: zhengp
	@since 2021/10/28 11:50
"""
input ExtensionResourceDataDto @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.dto.ExtensionResourceDataDto") {
	"""外链视频信息"""
	extensionVideoInfo:ExtensionResourceData
}
"""@author: zhengp
	@since 2021/10/28 11:50
"""
input ResourceDataDto @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.dto.ResourceDataDto") {
	"""课件媒体资源存储路径"""
	resourcePath:String
	"""时长"""
	timeLength:Int!
	resourceMD5:String
	"""字幕地址"""
	videoCaptionPath:String
	"""视频转码设置"""
	videoTranscodeSettings:VideoTranscodeSettings
}
"""八百里视频转码设置"""
input BABAILIVideoTranscodeSetting @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.dto.argus.BABAILIVideoTranscodeSetting") {
	"""是否进行转码"""
	transcode:Boolean!
	"""转码清晰度，1代表普屏流畅|2代表普屏标清|3代表普屏高清|4代表宽屏流畅|5代表宽屏标清|6代表宽屏高清"""
	clarityList:[Int]
}
"""华为云视频转码设置"""
input HWYVideoTranscodeSetting @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.dto.argus.HWYVideoTranscodeSetting") {
	"""是否进行转码"""
	transcode:Boolean!
	"""华为云转码模板名称"""
	templateName:String
	"""是否抽取音频"""
	extractAudio:Boolean!
}
"""保利威视转码设置"""
input POLYVVideoTranscodeSetting @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.dto.argus.POLYVVideoTranscodeSetting") {
	"""是否进行转码"""
	transcode:Boolean!
}
"""视频转码设置"""
input VideoTranscodeSettings @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.dto.argus.VideoTranscodeSettings") {
	"""八百里转码配置"""
	babailiSetting:BABAILIVideoTranscodeSetting
	"""保利威视转码配置"""
	polyvSetting:POLYVVideoTranscodeSetting
	"""华为云转码配置"""
	hwySetting:HWYVideoTranscodeSetting
}
"""@author: zhengp 2022/5/10 19:36"""
input ExportCoursePackageParam @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.dto.export.ExportCoursePackageParam") {
	"""主任务id"""
	mainTaskId:String
	"""导出任务状态 默认为全部数据 1为成功 0为失败"""
	dataType:Int
}
"""@author: zhengp
	@since 2021/10/22 16:21
"""
input ExtensionResourceData @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.dto.extension.ExtensionResourceData") {
	videoInfoDtos:[VideoInfoDto]
}
input VideoInfoDto @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.dto.extension.ExtensionResourceData$VideoInfoDto") {
	"""视频播放路劲，相对路径"""
	path:String
	"""视频清晰度
		1:流畅普屏 2:标清普屏 3:高清普屏 4:流畅宽屏 5:标清宽屏 6:高清宽屏 7:超清宽屏 8:流畅 9:标清 10:高清 11:超清
		负数是代表手机端对应的清晰度
	"""
	clarity:Int!
}
"""分页查询课程包导入任务数据请求信息
	@author: zhengp 2022/5/12 10:18
"""
input PageCoursePackageImportTaskRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.PageCoursePackageImportTaskRequest") {
	"""任务名称"""
	taskName:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""执行时间（起始）"""
	executeStartTime:DateTime
	"""执行时间（终止）"""
	executeEndTime:DateTime
}
"""分页查询课程包导入任务数据请求信息
	@author: zhengp 2022/5/12 10:18
"""
input PageCoursewareImportTaskRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.PageCoursewareImportTaskRequest") {
	"""任务名称"""
	taskName:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""执行时间（起始）"""
	executeStartTime:DateTime
	"""执行时间（终止）"""
	executeEndTime:DateTime
}
"""课程校验信息"""
input CheckCourseRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.course.CheckCourseRequest") {
	"""课程id"""
	id:String
	"""课程名称"""
	name:String!
}
"""课程分类创建请求信息
	@author: zhengp 2022/1/13 20:18
"""
input CourseCategoryCreateRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.course.CourseCategoryCreateRequest") {
	"""课程分类名称"""
	name:String!
	"""课程分类描述"""
	description:String
	"""父级课程分类 顶级为-1"""
	parentId:String!
	"""排序"""
	sort:Int!
}
"""@author: zhengp 2022/1/15 10:36"""
input CourseCategoryUpdateRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.course.CourseCategoryUpdateRequest") {
	"""课程分类id"""
	id:String!
	"""课程分类名称"""
	name:String!
	"""课程分类描述"""
	description:String
	"""父级课程分类 顶级为-1"""
	parentId:String!
	"""排序"""
	sort:Int!
}
"""课程章节
	@author: zhengp 2022/1/14 9:19
"""
input CourseChapterRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.course.CourseChapterRequest") {
	"""课程章节id"""
	id:String
	"""章节名称"""
	name:String!
	"""课件id"""
	coursewareId:String!
	"""排序"""
	sort:Int!
	"""挂在课件是否支持试听 0不可以试听，1可以试听，"""
	auditionStatus:Int!
}
"""课程创建请求信息
	@author: zhengp 2022/1/13 20:18
"""
input CourseCreateRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.course.CourseCreateRequest") {
	"""课程目录"""
	courseOutlines:[CourseOutlineWithSubOutlineRequest]
	"""课程名称"""
	name:String!
	"""封面图片路径"""
	iconPath:String!
	"""课件供应商id"""
	supplierId:String
	"""分类id"""
	categoryIds:[String]!
	"""课程简介"""
	aboutsContent:String
}
"""课程目录创建请求信息
	@author: zhengp 2022/1/13 20:26
"""
input CourseOutlineWithSubOutlineRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.course.CourseOutlineWithSubOutlineRequest") {
	"""子目录"""
	subCourseOutlines:[CourseOutlineWithSubOutlineRequest]
	"""课程目录id"""
	id:String
	"""目录名称"""
	name:String
	"""排序"""
	sort:Int!
	"""课程章节"""
	courseChapters:[CourseChapterRequest]
}
"""课程更新请求信息
	@author: zhengp 2022/1/14 10:13
"""
input CourseUpdateRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.course.CourseUpdateRequest") {
	"""课程id"""
	id:String!
	"""课程目录"""
	courseOutlines:[CourseOutlineWithSubOutlineRequest]
	"""课程名称"""
	name:String!
	"""封面图片路径"""
	iconPath:String!
	"""课件供应商id"""
	supplierId:String
	"""分类id"""
	categoryIds:[String]!
	"""课程简介"""
	aboutsContent:String
}
"""@author: xucenhao
	@time: 2024-08-08
	@description: 课程包校验信息
"""
input CheckCoursePackageRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.coursepackage.CheckCoursePackageRequest") {
	"""课程包id"""
	id:String
	"""课程包名称"""
	name:String
	"""方便拓展"""
	type:Int!
}
"""@author: zhengp 2022/1/11 16:55"""
input CourseInPackageRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.coursepackage.CourseInPackageRequest") {
	"""课程编号"""
	courseId:String!
	"""课程序号"""
	sort:Int!
	"""课程在课程池中的学时"""
	period:Double!
}
"""课程包创建请求信息
	@author: zhengp 2022/1/11 16:51
"""
input CoursePackageCreateRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.coursepackage.CoursePackageCreateRequest") {
	"""课程包名称"""
	name:String!
	"""课程包展示名称"""
	displayName:String
	"""包内课程"""
	courseInPackageList:[CourseInPackageRequest]
}
"""课程包更新
	@author: zhengp 2022/1/11 17:13
"""
input CoursePackageUpdateRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.coursepackage.CoursePackageUpdateRequest") {
	"""课程包id"""
	id:String!
	"""课程包名称"""
	name:String!
	"""课程包展示名称"""
	displayName:String
	"""包内课程"""
	courseInPackageList:[CourseInPackageRequest]
}
"""@BelongsProject: fjhb-microservice-course-resource
	@BelongsPackage: com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.courseware
	@Author: XuCenHao
	@CreateTime: 2023-12-08  14:19
	@Description:
"""
input BatchImportCoursewareCreateRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.courseware.BatchImportCoursewareCreateRequest") {
	"""excel文件路径"""
	excelFilePath:String!
	"""文件名称 (需要带后缀名)"""
	excelFileName:String!
}
"""@author: xucenhao
	@time: 2024-08-08
	@description: 课件校验信息
"""
input CheckCoursewareRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.courseware.CheckCoursewareRequest") {
	"""课件id"""
	id:String
	"""课件名称"""
	name:String
}
"""<AUTHOR> 2023/6/12"""
input ConfigRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.courseware.ConfigRequest") {
	"""外链"""
	extensionResourceDataDto:ExtensionResourceDataDto
	"""resourceDataDto;"""
	resourceDataDto:ResourceDataDto
}
"""课件分类创建请求信息
	@author: zhengp 2022/1/13 20:18
"""
input CoursewareCategoryCreateRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.courseware.CoursewareCategoryCreateRequest") {
	"""课件分类名称"""
	name:String!
	"""课件分类描述"""
	description:String
	"""父级课件分类 顶级为-1"""
	parentId:String!
	"""排序"""
	sort:Int!
}
"""@author: zhengp 2022/1/15 10:36"""
input CoursewareCategoryUpdateRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.courseware.CoursewareCategoryUpdateRequest") {
	"""课程分类id"""
	id:String!
	"""课件分类名称"""
	name:String!
	"""课件分类描述"""
	description:String
	"""父级课件分类 顶级为-1"""
	parentId:String!
	"""排序"""
	sort:Int!
}
"""课件创建请求
	@author: xgh 2022/5/23 17:54
"""
input CoursewareCreateRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.courseware.CoursewareCreateRequest") {
	"""课件分类Id"""
	categoryId:String!
	"""课件名称"""
	coursewareName:String!
	"""课件供应商Id"""
	supplierId:String!
	"""教师名称"""
	teacherName:String
	"""教师简介"""
	teacherDescription:String
	"""课件简介"""
	coursewareDescription:String
	"""资源类型  VIDEO_SINGLE = 1  VIDEO_EXTENSION = 2 [必填]"""
	resourceType:Int!
	"""课件媒体资源配置json字符串
		@see ExtensionResourceDataDto
		@see ResourceDataDto
	"""
	configJSON:String!
}
"""课程更新请求
	@author: xgh 2022/5/25 14:07
"""
input CoursewareUpdateRequest @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.request.courseware.CoursewareUpdateRequest") {
	"""课件id"""
	id:String!
	"""课件分类id"""
	categoryId:String!
	"""课件名称"""
	name:String!
	"""课件供应商id"""
	supplierId:String!
	"""课件教师id"""
	teacherId:String
	"""教师名称"""
	teacherName:String
	"""教师简介"""
	teacherAboutsContent:String
	"""课件简介"""
	aboutsContent:String
	"""课程状态"""
	enable:Boolean!
	"""课件媒体资源配置json字符串 没有修改媒体资源时,为空"""
	configJSON:String
	"""资源类型  VIDEO_SINGLE = 1  VIDEO_EXTENSION = 2 没有修改媒体资源时,为空"""
	resourceType:Int
}
"""@author: xucenhao
	@time: 2024-08-08
	@description:
"""
type GeneralResponse @type(value:"com.fjhb.ms.course.resource.v1.api.general.response.GeneralResponse") {
	code:String
	message:String
}
"""@Author: XuCenHao
	@CreateTime: 2024-4-2  10:15
	@Description: gql响应
"""
type GeneralMutationResponse @type(value:"com.fjhb.ms.course.resource.v1.api.response.mutation.GeneralMutationResponse") {
	"""请求code
		@see GeneralMutationEnum
	"""
	code:Int!
	"""业务code
		@see IBusinessCode
	"""
	businessCode:Int!
	"""提示"""
	tip:String
	data:String
}
"""课程包导入任务数据信息
	@author: zhengp 2022/5/12 10:23
"""
type CoursePackageImportTaskResponse @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.response.CoursePackageImportTaskResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""任务名称"""
	name:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""处理时间"""
	executingTime:DateTime
	"""结束（完成）时间"""
	completedTime:DateTime
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
}
"""各状态及执行结果对应数量"""
type EachStateCount @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.response.CoursePackageImportTaskResponse$EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Int!
}
"""课程包导入任务数据信息
	@author: zhengp 2022/5/12 10:23
"""
type CoursewareImportTaskResponse @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.response.CoursewareImportTaskResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""任务名称"""
	name:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""处理时间"""
	executingTime:DateTime
	"""结束（完成）时间"""
	completedTime:DateTime
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount1]
}
"""各状态及执行结果对应数量"""
type EachStateCount1 @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.response.CoursewareImportTaskResponse$EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Int!
}
"""@author: zhengp 2022/5/11 14:59"""
type ExportCoursePackageImportResultResponse @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.response.ExportCoursePackageImportResultResponse") {
	"""导入课程包信息执行结果文件地址"""
	fileUrl:String
}
"""课程校验信息"""
type CheckCourseResponse @type(value:"com.fjhb.ms.course.resource.v1.kernel.gateway.graphql.response.course.CheckCourseResponse") {
	"""状态码
		3000 课程名称已存在
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
type CoursePackageImportTaskResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CoursePackageImportTaskResponse]}
type CoursewareImportTaskResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CoursewareImportTaskResponse]}
