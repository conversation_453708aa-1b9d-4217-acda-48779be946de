schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取指定的考前培训方案
		@param schemeId 学习方案ID
	"""
	findById(schemeId:String):BTPXIssueClassLSResponse @NotAuthenticationRequired
	"""获取内容仓储的具体web内容
		@param contentId
		@return
	"""
	getContent(contentId:String):String @NotAuthenticationRequired
	"""批量获取内容仓储的具体web内容
		@param contentIds
		@return
	"""
	getContentByIds(contentIds:[String]):[CommodityContent] @NotAuthenticationRequired
	"""获取学习方案商情
		UI上各种学习方式的tab是否显示条件：对应的学习方式不为空 且 enable为true展示
		todo 单位id:方案管理-方案详情（需要调整为机构id），待方案微服务化后再做调整
		@param schemeId
		@return
	"""
	getLearningSchemeDetail(schemeId:String!):LearningSchemeDetailResponse @NotAuthenticationRequired
	"""获取指定培训方案是否可以下架或删除
		@param schemeId
		@return true/false
	"""
	isCanDownOrDeleteScheme(schemeId:String):Boolean!
	"""获取sku属性及选项值
		@return
	"""
	listAllSkuPropertyAndOption:[SkuPropertyAndOptionResponse] @NotAuthenticationRequired
	"""获取方案下面的兴趣课程
		@param schemeId
		@return
	"""
	listInterestCourse(schemeId:String):[CourseInPoolResponse]
	"""获取学习方案下面的期数商品
		@param schemeId
		@return
	"""
	listIssue(schemeId:String!):[IssueAndCommoditySkuDto]
	"""获取年度的可选值
		@deprecated 目前补贴未用到，暂未提供
	"""
	listYearSkuOption:[SkuPropertyOptionDTO]
	pageIssue(page:Page!,paramDTO:IssueParamDTO!):IssueDTOPage @page(for:"IssueDTO")
	pageLearningScheme(page:Page!,paramDTO:LearningSchemeParamDTO!):LearningSchemeDTOPage @page(for:"LearningSchemeDTO")
}
type Mutation {
	"""创建学习方案
		@param createInfo
		@param issueCreateDTOList
		@return
	"""
	createBTPXLS(createInfo:BTPXIssueClassLSCreateRequest!,issueCreateDTOList:[LSIssueCreateDTO]!):LearningSchemeDTO1
	"""创建学习方案
		@param createInfo
		@param issueCreateDTOList
		@return
	"""
	createLS(createInfo:BTPXIssueClassLSCreateRequest!,issueCreateDTOList:[LSIssueCreateDTO]!):LearningSchemeDTO1
	"""下架指定的学习方案
		@param id 商品id
		@return true/false 是否下架成功
	"""
	offShelfBTPXLS(id:String):Boolean
	"""更新学习方案
		@param updateInfo
		@param issueUpdateDTOList
	"""
	updateBTPXLS(updateInfo:BTPXIssueClassLSUpdateRequest!,issueUpdateDTOList:[LSIssueUpdateDTO]!):LearningSchemeDTO1
	"""更新考前学习方案指定的期数
		@param updateInfo 期数更新信息
	"""
	updateIssue(updateInfo:LSIssueUpdateDTO):IssueAndCommoditySkuDto
	"""更新学习方案
		@param updateInfo
		@param issueUpdateDTOList
	"""
	updateLS(updateInfo:BTPXIssueClassLSUpdateRequest!,issueUpdateDTOList:[LSIssueUpdateDTO]!):LearningSchemeDTO1
}
"""期数相关的查询
	@author: eleven
	@date: 2020/3/11
"""
input IssueParamDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.param.IssueParamDTO") {
	"""期数id"""
	issueId:String
	"""学习方案id"""
	schemeIdList:[String]
	"""方案名称"""
	scheme:String
	"""单位id集合"""
	unitIdList:[String]
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""@author: eleven
	@date: 2020/3/27
"""
input LearningSchemeParamDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.param.LearningSchemeParamDTO") {
	"""学习方案id"""
	schemeIdList:[String]
	"""方案名称"""
	scheme:String
	"""单位id集合"""
	unitIdList:[String]
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""期数销售属性
	@author: eleven
	@date: 2020/3/13
"""
input LSIssueCreateDTO @type(value:"com.fjhb.btpx.learningscheme.appservice.dto.LSIssueCreateDTO") {
	schemeId:String
	title:String
	startTime:DateTime
	endTime:DateTime
	studyTimeConfigType:Int
	openCustomerPurchase:Boolean!
	sort:Int!
	saleIntroduce:String
	price:BigDecimal
	shelvePlain:ShelvePlain1
}
"""期数更新对象
	@author: eleven
	@date: 2020/3/13
"""
input LSIssueUpdateDTO @type(value:"com.fjhb.btpx.learningscheme.appservice.dto.LSIssueUpdateDTO") {
	schemeId:String
	issueId:String
	title:String
	startTime:DateTime
	endTime:DateTime
	studyTimeConfigType:Int
	sort:Int!
	openCustomerPurchase:Boolean!
	price:BigDecimal
	saleIntroduce:String
	shelvePlain:ShelvePlain1
}
"""学习方案创建差异化对象
	<AUTHOR>
	@date 2020/8/11
	@description
"""
input BTPXIssueClassLSCreateRequest @type(value:"com.fjhb.btpx.learningscheme.gateway.graphql.resolver.request.BTPXIssueClassLSCreateRequest") {
	"""培训类别"""
	trainingTypeId:String
	"""培训工种"""
	workTypeId:String
	"""是否开放证明打印"""
	openPrintCertificate:Boolean!
	"""兴趣课程包 - 服务内部使用"""
	interestPoolIdList:String
	"""适用人群"""
	suitablePeople:[String]
	"""兴趣课程包配置"""
	interestCourseSetting:InterestCourseSetting1
	"""课程供应商id"""
	coursewareSupplierId:String!
	name:String
	year:Int!
	picture:String
	unitId:String
	content:String
	mobileContent:String
	achieveSetting:IssueClassLSAchieveSettingDto
	courseLearningSettings:CourseLearningSettingsDto
	examLearningSettings:ExamLearningSettingsDto
	questionPracticeLearningSettings:QuestionPracticeLearningSettingsDto
}
"""<AUTHOR>
	@date 2020/8/11
	@description
"""
input BTPXIssueClassLSUpdateRequest @type(value:"com.fjhb.btpx.learningscheme.gateway.graphql.resolver.request.BTPXIssueClassLSUpdateRequest") {
	"""是否开放证明打印"""
	openPrintCertificate:Boolean!
	"""兴趣课程包 - 服务内部使用"""
	interestPoolIdList:String
	"""适用人群"""
	suitablePeople:[String]
	"""兴趣课程包配置"""
	interestCourseSetting:InterestCourseSetting1
	"""课程供应商id"""
	coursewareSupplierId:String
	schemeId:String
	name:String
	picture:String
	content:String
	mobileContent:String
	achieveSetting:IssueClassLSAchieveSettingDto
	courseLearningSettings:CourseLearningSettingsDto
	examLearningSettings:ExamLearningSettingsDto
	questionPracticeLearningSettings:QuestionPracticeLearningSettingsDto
}
"""<AUTHOR>
	@date 2020/8/12
	@description
"""
input InterestCourseSetting1 @type(value:"com.fjhb.btpx.learningscheme.gateway.graphql.resolver.request.InterestCourseSetting") {
	"""兴趣课程包集合
		前端传递的参数，服务内部转为 interestPoolIdList
	"""
	poolList:[String]
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input CourseLearningSettingsDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.CourseLearningSettingsDto") {
	enabled:Boolean!
	compulsoryPackages:[PackageRuleSettingDto1]
	optionalPackages:[PackageRuleSettingDto1]
	optionalTotalPeriod:Double
	assessSetting:CourseLearningAssessSettingDto
}
input CourseLearningAssessSettingDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.CourseLearningSettingsDto$CourseLearningAssessSettingDto") {
	allSelectedComplete:Boolean!
	schedule:Double!
}
input ExamLearningSettingsDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.ExamLearningSettingsDto") {
	enabled:Boolean!
	name:String
	examPaperId:String
	examTimeLength:Int!
	examCount:Int!
	configExamTime:Boolean!
	beginTime:DateTime
	endTime:DateTime
	passScore:Double!
	openResolvedExam:Boolean!
	minSubmitTimeLength:Int!
	less:Boolean!
	missScorePattern:Int!
	enabledExamLearningPrecondition:Boolean!
	assessSetting:ExamAssessSettingDto
}
input ExamAssessSettingDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.ExamLearningSettingsDto$ExamAssessSettingDto") {
	score:Double!
}
input IssueClassLSAchieveSettingDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.IssueClassLSAchieveSettingDto") {
	enabledAssess:Boolean!
	grade:Double
	templateId:String
}
input LibraryWaySettingDto1 @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.LibraryWaySettingDto") {
	libraryIds:[String]
	recursive:Boolean!
}
input PackageRuleSettingDto1 @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.PackageRuleSettingDto") {
	packageId:String
	limit:Boolean!
	maxPeriod:Double
}
input QuestionPracticeLearningSettingsDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.QuestionPracticeLearningSettingsDto") {
	enabled:Boolean!
	fetchWay:Int!
	libraryWaySetting:LibraryWaySettingDto1
	tagsWaySetting:TagWaySettingDto1
}
input ShelvePlain1 @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.ShelvePlain") {
	onShelve:Boolean!
	onShelvePlanTime:DateTime
	offShelvePlanTime:DateTime
}
input TagWaySettingDto1 @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.TagWaySettingDto") {
	tagIds:[String]
}
"""<AUTHOR>
	@date 2020/8/17
	@description
"""
type CourseInPoolResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.course.CourseInPoolResponse") {
	courseId:String
	poolId:String
	poolName:String
	showName:String
}
"""课程考核信息
	@author: eleven
	@date: 2020/6/5
"""
type CourseLearningAssessSettingResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CourseLearningAssessSettingResponse") {
	allSelectedComplete:Boolean!
	schedule:Double!
}
"""@author: eleven
	@date: 2020/6/6
"""
type CourseLearningResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CourseLearningResponse") {
	"""学习方案要求的最少学时"""
	minTotalPeriod:Double!
	"""课程考核要求"""
	assessSetting:CourseLearningAssessSettingResponse
	learningId:String
	enabled:Boolean!
	compulsoryPackages:[PackageRuleSettingDto]
	optionalPackages:[PackageRuleSettingDto]
}
"""@author: eleven
	@date: 2020/6/5
"""
type ExamAssessSettingResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.ExamAssessSettingResponse") {
	score:Double!
}
"""@author: eleven
	@date: 2020/6/6
"""
type ExamLearningResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.ExamLearningResponse") {
	"""考试考核，null表示不设置考核"""
	assessSetting:ExamAssessSettingResponse
	learningId:String
	enabled:Boolean!
	name:String
	examPaperId:String
	examTimeLength:Int!
	examCount:Int!
	configExamTime:Boolean!
	beginTime:DateTime
	endTime:DateTime
	passScore:Double!
	openResolvedExam:Boolean!
	minSubmitTimeLength:Int!
	less:Boolean!
	missScorePattern:Int!
}
"""@author: eleven
	@date: 2020/6/5
"""
type IssueClassLSAchieveSettingResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.IssueClassLSAchieveSettingResponse") {
	enabledAssess:Boolean!
	grade:Double
	templateId:String
}
"""学习方案详情
	@author: eleven
	@date: 2020/3/12
"""
type LearningSchemeDetailResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.LearningSchemeDetailResponse") {
	"""单位id"""
	unitId:String
	"""学习方案ID"""
	schemeId:String
	"""培训方案名称"""
	name:String
	"""封面图片地址"""
	picture:String
	"""web内容"""
	content:String
	"""手机端内容"""
	mobileContent:String
	"""是否开放证明打印"""
	openPrintCertificate:Boolean!
	"""课程学习方式信息"""
	courseLearning:CourseLearningResponse
	"""考试学习方式信息"""
	examLearning:ExamLearningResponse
	"""试题练习学习方式信息"""
	questionPracticeLearning:QuestionPracticeLearningDto
	"""培训班成果设置"""
	achieveSetting:IssueClassLSAchieveSettingResponse
	"""兴趣课程包配置"""
	interestCourseSetting:InterestCourseSetting
	"""创建人ID"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""适用人群"""
	suitablePeople:[String]
	"""课程供应商id"""
	coursewareSupplierId:String
	"""课程供应商名称"""
	coursewareSupplierName:String
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""@author: eleven
	@date: 2020/6/10
"""
type SkuPropertyAndOptionResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.SkuPropertyAndOptionResponse") {
	"""sku属性id"""
	skuPropertyId:String
	"""属性code"""
	code:String
	"""属性值"""
	optionList:[SkuPropertyOptionDTO]
}
"""商品分页对象
	@author: eleven
	@date: 2020/3/11
"""
type IssueDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.IssueDTO") {
	"""商品skuId"""
	commoditySkuId:String
	"""方案id"""
	schemeId:String
	"""方案名称"""
	scheme:String
	"""期数id"""
	issueId:String
	"""期数名称"""
	issueTitle:String
	"""学习截止时间"""
	learningEndTime:String
	"""学习开始时间"""
	learningStartTime:String
	"""是否开放报名"""
	openEnrolment:Boolean!
	"""排序"""
	sort:Int!
	"""商品sku上下架状态"""
	skuState:CommoditySkuState
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""学习方案分页查询
	@author: eleven
	@date: 2020/3/27
"""
type LearningSchemeDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.LearningSchemeDTO") {
	"""学习方案ID"""
	schemeId:String
	"""培训方案名称"""
	name:String
	"""培训班封面图片地址"""
	picture:String
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""sku可选值
	@author: eleven
	@date: 2020/3/18
"""
type SkuPropertyOptionDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.SkuPropertyOptionDTO") {
	"""主键(可选值id)"""
	id:String
	"""sku属性id"""
	skuPropertyId:String
	"""可选值名称"""
	optionName:String
	"""可选值代码"""
	optionCode:String
	"""排序号码"""
	orderNumber:Int!
}
"""商品内容仓储返回(销售卖点)
	Author:FangKunSen
	Time:2021-03-19,10:42
"""
type CommodityContent @type(value:"com.fjhb.btpx.integrative.service.content.dto.response.CommodityContent") {
	id:String
	content:String
}
"""学习方案对象
	@author: eleven
	@date: 2020/3/13
"""
type LearningSchemeDTO1 @type(value:"com.fjhb.btpx.learningscheme.appservice.dto.LearningSchemeDTO") {
	"""方案对象"""
	preExamLSDto:IssueClassLSDto
	"""期数创建对象"""
	issueAndCommoditySkuDtoList:[IssueAndCommoditySkuDto]
}
"""<AUTHOR>
	@date 2020/8/12
	@description
"""
type InterestCourseSetting @type(value:"com.fjhb.btpx.learningscheme.gateway.graphql.resolver.request.InterestCourseSetting") {
	"""兴趣课程包集合
		前端传递的参数，服务内部转为 interestPoolIdList
	"""
	poolList:[String]
}
"""<AUTHOR>
	@date 2021/5/19 9:34
	@Description:
"""
type BTPXIssueClassLSResponse @type(value:"com.fjhb.btpx.learningscheme.gateway.graphql.resolver.response.BTPXIssueClassLSResponse") {
	"""兴趣课程包 - 服务内部使用"""
	interestPoolIdList:[String]
	"""课程供应商id"""
	coursewareSupplierId:String
	"""学习方案ID"""
	schemeId:String
	"""培训方案名称"""
	name:String
	"""年度"""
	year:Int!
	"""封面图片地址"""
	picture:String
	"""web内容"""
	content:String
	"""手机端内容"""
	mobileContent:String
	"""发布机构"""
	unitId:String
	"""课程学习方式信息"""
	courseLearning:CourseLearningResponse1
	"""考试学习方式信息"""
	examLearning:ExamLearningResponse1
	"""试题练习学习方式信息"""
	questionPracticeLearning:QuestionPracticeLearningResponse
	"""创建人ID"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""适用人群"""
	suitablePeople:String
}
type CourseLearningDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.CourseLearningDto") {
	learningId:String
	enabled:Boolean!
	compulsoryPackages:[PackageRuleSettingDto]
	optionalPackages:[PackageRuleSettingDto]
}
type ExamLearningDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.ExamLearningDto") {
	learningId:String
	enabled:Boolean!
	name:String
	examPaperId:String
	examTimeLength:Int!
	examCount:Int!
	configExamTime:Boolean!
	beginTime:DateTime
	endTime:DateTime
	passScore:Double!
	openResolvedExam:Boolean!
	minSubmitTimeLength:Int!
	less:Boolean!
	missScorePattern:Int!
}
type IssueAndCommoditySkuDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.IssueAndCommoditySkuDto") {
	schemeId:String
	issueId:String
	commoditySkuId:String
	title:String
	startTime:DateTime
	endTime:DateTime
	studyTimeConfigType:Int
	openCustomerPurchase:Boolean!
	price:BigDecimal
	saleIntroduce:String
	shelvePlain:ShelvePlain
}
type IssueClassLSDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.IssueClassLSDto") {
	schemeId:String
	name:String
	year:Int!
	unitId:String
	picture:String
	content:String
	mobileContent:String
	courseLearning:CourseLearningDto
	examLearning:ExamLearningDto
	questionPracticeLearning:QuestionPracticeLearningDto
	createUserId:String
	createTime:DateTime
}
type LibraryWaySettingDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.LibraryWaySettingDto") {
	libraryIds:[String]
	recursive:Boolean!
}
type PackageRuleSettingDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.PackageRuleSettingDto") {
	packageId:String
	limit:Boolean!
	maxPeriod:Double
}
type QuestionPracticeLearningDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.QuestionPracticeLearningDto") {
	learningId:String
	enabled:Boolean!
	fetchWay:Int!
	libraryWaySetting:LibraryWaySettingDto
	tagsWaySetting:TagWaySettingDto
}
type ShelvePlain @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.ShelvePlain") {
	onShelve:Boolean!
	onShelvePlanTime:DateTime
	offShelvePlanTime:DateTime
}
type TagWaySettingDto @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.api.dto.TagWaySettingDto") {
	tagIds:[String]
}
"""课程学习方式信息"""
type CourseLearningResponse1 @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.CourseLearningResponse") {
	"""学习方式ID"""
	learningId:String
	"""是否启用课程学习方式"""
	enabled:Boolean!
	"""必修课程包编号列表"""
	compulsoryPackages:[PackageResponse]
	"""选修课程包集合"""
	optionalPackages:[PackageResponse]
}
"""考试学习方式信息"""
type ExamLearningResponse1 @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.ExamLearningResponse") {
	"""学习方式ID"""
	learningId:String
	"""是否启用考试学习方式"""
	enabled:Boolean!
	"""考试名称"""
	name:String
	"""试卷ID"""
	examPaperId:String
	"""考试时长，单位分钟"""
	examTimeLength:Int!
	"""考试次数 0表示不限制次数"""
	examCount:Int!
	"""及格分数"""
	passScore:Double!
	"""是否开放试题解析"""
	openResolvedExam:Boolean!
	"""最短提交时长"""
	minSubmitTimeLength:Int!
	"""是否配置考试时间"""
	configExamTime:Boolean!
	"""开考时间"""
	beginTime:DateTime
	"""结束时间"""
	endTime:DateTime
	"""多选漏选是否的分"""
	less:Boolean!
	"""漏选得分模式
		0:不得分，适用于漏选不得分情况
		1:全得
		2:得一半
		3:平均得分
	"""
	missScorePattern:Int!
}
"""题库配置
	<AUTHOR>
	@date 2020/6/3
	@since 1.0.0
"""
type LibraryWaySettingResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.LibraryWaySettingResponse") {
	"""题库id集合"""
	libraryIds:[String]
	"""是否递归取题
		如果是题库卷时，才生效。
	"""
	recursive:Boolean!
}
"""课程包规则设置
	<AUTHOR>
	@date 2020/6/1
	@since 1.0.0
"""
type PackageResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.PackageResponse") {
	"""课程包编号"""
	packageId:String
	"""是否限制最大可选学时,如果设置true,则需要填写{@link #maxPeriod}"""
	limit:Boolean!
	"""当前课程包要求最多选课学时，如果不填，表示不设置要求"""
	maxPeriod:Double
}
"""试题练习学习方式信息"""
type QuestionPracticeLearningResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.QuestionPracticeLearningResponse") {
	"""学习方式ID"""
	learningId:String
	"""是否启用试题练习"""
	enabled:Boolean!
	"""抽题方式， 1：自定义（此时需要指定random配置），2：题库，3：考场所属方案下已选课程关联试题，4：标签方式抽题"""
	fetchWay:Int!
	"""题库抽题方式配置"""
	libraryWaySetting:LibraryWaySettingResponse
	"""标签方式"""
	tagsWaySetting:TagWaySettingResponse
}
"""标签方式抽题
	<AUTHOR>
	@date 2020/6/19
	@since
"""
type TagWaySettingResponse @type(value:"com.fjhb.platform.core.learningscheme.v1.normalissueclass.kernel.gateway.graphql.response.TagWaySettingResponse") {
	"""标签编号集合"""
	tagIds:[String]
}
enum CommoditySkuState @type(value:"com.fjhb6.ability.commodity.v1.commons.model.helper.CommoditySkuState") {
	STORED
	UPED
	DOWNED
}

scalar List
type IssueDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [IssueDTO]}
type LearningSchemeDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LearningSchemeDTO]}
