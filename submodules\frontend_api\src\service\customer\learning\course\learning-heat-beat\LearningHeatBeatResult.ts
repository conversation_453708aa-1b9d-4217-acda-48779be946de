import { LearningErrorCodeResponse } from '@api/ms-gateway/ms-media-resource-learning-v1'
import LearningHeatBeatErrorCodeEnum from '@api/service/customer/learning/course/learning-heat-beat/LearningHeatBeatErrorCodeEnum'

class LearningHeatBeatResult extends LearningErrorCodeResponse {
  code: LearningHeatBeatErrorCodeEnum = LearningHeatBeatErrorCodeEnum.success

  isSuccess() {
    return this.code === LearningHeatBeatErrorCodeEnum.success
  }

  isError() {
    return this.code !== LearningHeatBeatErrorCodeEnum.success
  }

  isPlayOnOtherPlace() {
    return [
      LearningHeatBeatErrorCodeEnum.T10001,
      LearningHeatBeatErrorCodeEnum.T10002,
      LearningHeatBeatErrorCodeEnum.T10003
    ].includes(this.code)
  }

  static from(response: LearningErrorCodeResponse) {
    const result = new LearningHeatBeatResult()
    result.code = response.code as LearningHeatBeatErrorCodeEnum
    result.message = response.message
    return result
  }
}

export default LearningHeatBeatResult
