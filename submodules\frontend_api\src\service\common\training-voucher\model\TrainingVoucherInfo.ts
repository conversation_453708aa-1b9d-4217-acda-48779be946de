import { WorkTypeResponse } from '@api/gateway/PlatformWorkType'

/**
 *
 * 培训卷信息
 * @author: puxf
 * @date: 2021/2/23
 */
export class TrainingVoucherInfo {
  /**
   * 培训券编码
   */
  couponCode: string
  /**
   * 身份证号
   */
  idCard: string
  /**
   * 用户姓名
   */
  name: string
  /**
   * 培训券名称
   */
  couponName: string
  /**
   * 用户培训券状态
   <pre>
   1 - 可用
   2 - 激活
   3 - 已使用
   4 - 已结算（补贴）
   -5 - 已作废
   -2 - 已过期
   </pre>
   */
  couponStatus: number
  /**
   * 用户培训券状态说明
   */
  couponStatusName: string
  /**
   * 发券机构（属地人社局）
   */
  publishOrgName: string
  /**
   * 区域编码（行政编码）
   */
  areaCode: string
  /**
   * 培训券面值（单位分）
   */
  amount: number
  /**
   * 使用有效期（年月日时分）
   */
  endTime: string
  /**
   * 适用人群
   */
  personScope: Array<string>
  /**
   * 适用工种范围，工种名称数组
   */
  jobScope: Array<string>
  /**
   * 适用工种范围，工种信息数组
   */
  workTypeScopeList: Array<WorkTypeResponse>
  /**
   * 当前券剩余余额（单位分）
   */
  nowAmount: number
  /**
   * 可用工种
   */
  job: string
  /**
   * 可用工种，工种信息
   */
  usableWorkType: WorkTypeResponse
  /**
   * 适用培训机构范围，机构信息数组
   */
  orgScope: Array<string>
  /**
   * 适用培训模式
   <pre>
   0-不限
   1-线上
   2-线上+线下
   3-线下
   </pre>
   */
  trainMode: number
  /**
   * 机构编码（未兑换时则为空）
   */
  educationCode: string
  /**
   * 培训机构名称（未兑换时则为空）
   */
  educationName: string
  /**
   * 使用模式（未兑换时则为空）
   <pre>
   1-自助使用
   2-扫码使用
   </pre>
   */
  useMode: number
  /**
   * 使用日期（未兑换时则为空）
   */
  useDate: string
  /**
   * 补贴时间（未兑换时则为空）
   */
  checkDate: string
}
