import TimerTask from '@api/service/common/timer/TimerTask'

describe('测试定时器启动', () => {
  it('监听定时器启动', () => {
    const timerTask = new TimerTask()
    console.log('初始化定时器')
    timerTask.on(TimerTask.Events.start, () => {
      console.log('定时器启动')
    })
    timerTask.on(TimerTask.Events.timeupdate, (time: number) => {
      console.log('定时器正在运行:' + time)
    })
    timerTask.on(TimerTask.Events.stop, () => {
      console.log('定时器暂停，被销毁')
    })
    console.log('启动4秒后销毁定时器')
    timerTask.start()
    // setTimeout(() => {
    //   timerTask.stop()
    // }, 4000)
  })
})
