import StudentTrainingRequireBase from '@api/service/common/implement/enums/StudentTrainingRequireBase'
import MsSchemeLearningQueryFront, {
  GradeLearningConfigResultResponse,
  LearningResponseV2,
  LearningResultConfigResponse,
  StudentReportRecordRequest,
  StudentReportRecordResponse,
  StudentSchemeAndIssueLearningResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import { LearningTypeEnum } from '@api/service/common/implement/enums/LearningTypeEnum'
import AttendanceRequirement from '@api/service/common/implement/models/AttendanceRequirement'
import { LearningResultTypeEnum } from '@api/service/customer/implement/enums/LearningResultTypeEnum'
import { GradeTypeEnum } from '@api/service/customer/implement/enums/GradeTypeEnum'
import { LodgingTypeEnum } from '@api/service/common/implement/enums/LodgingTypeEnum'

export default class StudentFaceStudyLog {
  /**
   * 学号
   */
  studentNo: string = undefined

  /**
   * 学员业务学号（展示使用）
   */
  businessStudentNo: string = undefined

  /**
   * 期别id
   */
  periodId = ''

  /**
   * 期别参训资格id
   */
  periodQualificationId: string = undefined

  /**
   * 学员考勤记录
   */
  attendance: StudentTrainingRequireBase = new StudentTrainingRequireBase()

  /**
   * 需要考勤率
   */
  attendanceRequireRate: number = undefined

  /**
   * 应打卡次数
   */
  attendanceRequireNum = 0

  /**
   * 学员考勤率
   */
  studentAttendanceRate: number = undefined

  /**
   * 期别面授所需调研问卷完成情况（该值显示期别下问卷是否需要考核、需要考核的问卷数已经完成情况）
   */
  questionnaire: StudentTrainingRequireBase = new StudentTrainingRequireBase()

  /**
   * 期别面授问卷总数
   */
  questionnaireTotalNum = 0

  /**
   * 期别面授学员问卷总提交数
   */
  questionnaireCompletedNum = 0

  /**
   * 结业测试合格情况
   */
  graduationTestQualified: boolean = undefined

  /**
   * 期别合格情况
   */
  periodQualified: boolean = undefined

  /**
   * 期别合格时间（只有期别合格后才会有）
   */
  periodQualifiedTime = ''

  /**
   * 住宿形式
   */
  lodgingType: LodgingTypeEnum = undefined

  /**
   * 是否报道
   */
  reported: boolean = undefined

  /**
   * 报道时间
   */
  reportTime: string = undefined

  /**
   * 方案问卷总数
   */
  schemeQuestionnaireTotalNum = 0

  /**
   * 方案学员问卷总提交数
   */
  schemeQuestionnaireCompletedNum = 0

  /**
   * 方案所需调研问卷完成情况（该值显示期别下问卷是否需要考核、需要考核的问卷数已经完成情况）
   */
  schemeQuestionnaire: StudentTrainingRequireBase = new StudentTrainingRequireBase()

  /**
   * @param studentNo 学号
   */
  constructor(studentNo: string) {
    this.studentNo = studentNo
  }

  /**
   * @param dto 方案学习情况
   * @param studentReportDtoRecord 报道记录
   */
  from(dto: StudentSchemeAndIssueLearningResponse, studentReportDtoRecord: StudentReportRecordResponse) {
    const { studentSchemeWithIssueLearningResponse, studentIssueLearning } = dto
    if (studentIssueLearning) {
      this.businessStudentNo = studentIssueLearning.businessStudentNo
      this.periodId = studentIssueLearning.issueId
      this.periodQualificationId = studentIssueLearning.qualificationId
      this.lodgingType = Number(studentIssueLearning.roomInfo) as LodgingTypeEnum
      /**
       * 学员考勤情况要通过期别学习方式学习里筛选出来
       * 问卷情况后端直接冗余在表层
       * 结业测试为方案分数学习成果
       * 但这边只获取记录，严格来说判断不了相关要求配置是否已开启
       * 相关配置要求是否已开启需要去读方案信息
       * 报道记录后端有改动，需依赖新接口 listStudentReportRecordInMyself 返回
       */
      if (studentIssueLearning.learning?.length) {
        // 获取考勤率与报道
        const findAttendance = studentIssueLearning.learning.find(
          (item: LearningResponseV2) => item.learningType === LearningTypeEnum.TEACH_PLAN_LEARNING
        )
        if (findAttendance && findAttendance.userAssessResult[0]) {
          const attendanceLog = JSON.parse(findAttendance.userAssessResult[0]) as AttendanceRequirement
          this.attendance.completedNum = attendanceLog.studentSignCount ? Number(attendanceLog.studentSignCount) : 0
          this.attendance.totalNum = attendanceLog.totalSignCount ? Number(attendanceLog.totalSignCount) : 0
          this.attendanceRequireRate = attendanceLog.signRate ? Number(attendanceLog.signRate) * 100 : 0
          if (this.attendance.totalNum && this.attendanceRequireRate) {
            this.attendanceRequireNum = Math.ceil((this.attendance.totalNum * this.attendanceRequireRate) / 100)
          }
          this.attendance.require = true
          // 学员考勤率：学员考勤次数除以总需考勤次数向下取证
          this.studentAttendanceRate = this.attendance.totalNum
            ? Math.floor((this.attendance.completedNum / this.attendance.totalNum) * 100)
            : 0
          this.attendance.qualified = findAttendance.learningAssessResult == 1

          // 报道记录不在这里转了
          // if (findAttendance.reportInfo) {
          //   this.reported = findAttendance.reportInfo.isReport
          //   this.reportTime = findAttendance.reportInfo.reportTime
          // }
        } else {
          this.attendance.require = false
        }

        // 问卷合格情况
        // questionnaireRequirementCount 该字段代表有多少张问卷需要考核，如果没有则代表该期别不需要问卷考核
        this.questionnaire.require = !!studentIssueLearning?.questionnaireRequirementCount
        this.questionnaire.totalNum = studentIssueLearning?.questionnaireRequirementCount || 0
        this.questionnaire.completedNum = studentIssueLearning?.questionnaireSubmittedCount || 0
        // 后端说问卷考核完成只要看考核问卷的提交数和总要求数是否相等即可
        this.questionnaire.qualified =
          studentIssueLearning?.questionnaireSubmittedCount &&
          studentIssueLearning?.questionnaireSubmittedCount == studentIssueLearning?.questionnaireRequirementCount
        // 统计问卷总张数
        const findQuestionnaireList = studentIssueLearning.learning.filter(
          (item: LearningResponseV2) => item.learningType === LearningTypeEnum.QUESTIONNAIRE
        )
        if (findQuestionnaireList?.length) {
          this.questionnaireTotalNum = findQuestionnaireList.length
          let completedNum = 0
          findQuestionnaireList.map((item: LearningResponseV2) => {
            // 已参与是指不需要考核的卷子交卷，参与完成是指需要考核的卷子交卷，所以二者都是交卷完成
            if ([0, 1].includes(item.learningStatus)) {
              completedNum++
            }
          })
          this.questionnaireCompletedNum = completedNum
        }
      }
      // 期别合格状态
      this.periodQualified = studentIssueLearning.trainingResult === 1
      this.periodQualifiedTime = studentIssueLearning.trainingResultGainTime
    }

    if (studentSchemeWithIssueLearningResponse) {
      /**
       * 结业测试
       * 学习成果是个json字符串 要找出里面resultType类型是分数型成可断定改json类型为 GradeLearningConfigResultResponse 类型
       * 再找出下面 gradeType 为结业测试类型，即可断定该条数据是期别的结业测试结果
       */
      if (studentSchemeWithIssueLearningResponse.learningResult?.length) {
        let graduationResult: GradeLearningConfigResultResponse = undefined
        for (let i = 0; i < studentSchemeWithIssueLearningResponse.learningResult.length; i++) {
          const learningResult = studentSchemeWithIssueLearningResponse.learningResult[i]
            .learningResultConfig as LearningResultConfigResponse

          // 定位分数学习成果
          if (learningResult.resultType == LearningResultTypeEnum.grade) {
            const gradeResult = learningResult as unknown as GradeLearningConfigResultResponse
            // 定位到结业测试
            if (gradeResult.gradeType == GradeTypeEnum.graduation) {
              graduationResult = gradeResult
              break
            }
          }
        }

        if (graduationResult) {
          this.graduationTestQualified = graduationResult.grade > 0
        }
      }

      // 方案问卷
      if (studentSchemeWithIssueLearningResponse.learning?.length) {
        // 方案问卷合格情况
        // questionnaireRequirementCount 该字段代表有多少张问卷需要考核，如果没有则代表该期别不需要问卷考核
        this.schemeQuestionnaire.require = !!studentSchemeWithIssueLearningResponse?.questionnaireRequirementCount
        this.schemeQuestionnaire.totalNum = studentSchemeWithIssueLearningResponse?.questionnaireRequirementCount || 0
        this.schemeQuestionnaire.completedNum = studentSchemeWithIssueLearningResponse?.questionnaireSubmittedCount || 0
        // 后端说问卷考核完成只要看考核问卷的提交数和总要求数是否相等即可
        this.schemeQuestionnaire.qualified =
          studentSchemeWithIssueLearningResponse?.questionnaireSubmittedCount &&
          studentSchemeWithIssueLearningResponse?.questionnaireSubmittedCount ==
            studentSchemeWithIssueLearningResponse?.questionnaireRequirementCount
        // 统计问卷总张数
        const findQuestionnaireList = studentSchemeWithIssueLearningResponse.learning.filter(
          (item: LearningResponseV2) => item.learningType === LearningTypeEnum.QUESTIONNAIRE
        )
        if (findQuestionnaireList?.length) {
          this.schemeQuestionnaireTotalNum = findQuestionnaireList.length
          let completedNum = 0
          findQuestionnaireList.map((item: LearningResponseV2) => {
            // 已参与是指不需要考核的卷子交卷，参与完成是指需要考核的卷子交卷，所以二者都是交卷完成
            if ([0, 1].includes(item.learningStatus)) {
              completedNum++
            }
          })
          this.schemeQuestionnaireCompletedNum = completedNum
        }
      }

      // 报道记录
      if (studentReportDtoRecord) {
        this.reported = true
        this.reportTime = studentReportDtoRecord.checkInTime
      } else {
        this.reported = false
      }
    }
  }

  /**
   * 查询学员面授记录
   */
  async queryStudentFaceStudyLog() {
    const res = await MsSchemeLearningQueryFront.getSchemeLearningDetailInMyself(this.studentNo)

    // 报道记录原接口不再返回 需依赖这个新接口
    let reportDtoLog: StudentReportRecordResponse = undefined
    if (res?.data?.studentIssueLearning?.qualificationId) {
      const reportLogReq = new StudentReportRecordRequest()
      reportLogReq.qualificationId = res.data.studentIssueLearning.qualificationId
      const reportRes = await MsSchemeLearningQueryFront.listStudentReportRecordInMyself(reportLogReq)

      reportDtoLog = reportRes?.data[0]
    }

    this.from(res.data, reportDtoLog)
  }
}
