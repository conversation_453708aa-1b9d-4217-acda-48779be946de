<route-meta>
{
"title": "行业选择器"
}
</route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    :disabled="disabled"
    filterable
    clearable
  >
    <el-option v-for="item in industryOptions" :label="item.name" :value="item.id" :key="item.id"></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'

  @Component
  export default class extends Vue {
    selected = ''
    // 工勤行业
    workServiceId = ''
    // 人社行业Id
    societyIndustryId = ''
    // 建设行业Id
    constructionIndustryId = ''
    //职业卫生行业Id
    professionHealthIndustryId = ''
    // 教师行业id
    teacherIndustryId = ''
    industryOptions: Array<IndustryVo> = new Array<IndustryVo>()

    @Prop({ type: String, default: '' }) value: string
    @Prop({ type: Boolean, default: false }) disabled: boolean
    @Prop({ type: String, default: '请选择行业' }) placeholder: string
    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }
    @Emit('input')
    @Watch('selected')
    selectedChange() {
      const checkItem = this.industryOptions?.find(el => el.id === this.selected)
      const industryPropertyId = (checkItem && checkItem.propertyId) || ''
      this.$emit('clearIndustrySelect')
      this.$emit('industryPropertyId', industryPropertyId)
      return this.selected
    }
    async created() {
      await this.getIndustryOptions()
    }

    async getIndustryOptions() {
      const res = await QueryIndustry.getIndustryDICTZT()
      this.industryOptions = res
      this.industryOptions?.forEach((el: IndustryVo) => {
        if (el.name === '人社行业') {
          this.societyIndustryId = el.id
        }
        if (el.name === '建设行业') {
          this.constructionIndustryId = el.id
        }
        if (el.name === '工勤行业') {
          this.workServiceId = el.id
        }
        if (el.name === '职业卫生行业') {
          this.professionHealthIndustryId = el.id
        }
        if (el.name === '教师行业') {
          this.teacherIndustryId = el.id
        }
      })
      this.$emit('industryInfos', {
        societyIndustryId: this.societyIndustryId,
        constructionIndustryId: this.constructionIndustryId,
        workServiceId: this.workServiceId,
        professionHealthIndustryId: this.professionHealthIndustryId,
        // 行业组件返回值修改
        teacherIndustryId: this.teacherIndustryId
      })
    }
  }
</script>
