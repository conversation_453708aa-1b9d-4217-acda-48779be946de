import { TeachResourceResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import LearningResourceJsonDto from '@api/service/common/implement/json-model/LearningResourceJsonDto'

export default class AnnexInfoDto {
  /**
   * 附件名称
   */
  name: string = undefined

  /**
   * 文件路径
   */
  path: string = undefined

  /**
   * 文件格式
   */
  format: string = undefined

  /**
   * 是否编辑状态（UI使用）
   */
  isEdit = false

  /**
   * 附件名称缓存（ui使用）
   */
  nameCache: string = undefined

  static from(dto: TeachResourceResponse) {
    const vo = new AnnexInfoDto()
    vo.name = dto.resourceName
    vo.nameCache = dto.resourceName
    // 这边学习资料是挂方案身上的，所以后端只能给json
    const resource = JSON.parse(dto.resourceContent) as LearningResourceJsonDto
    vo.format = resource.type
    vo.path = resource.path
    return vo
  }

  /**
   * 取消编辑
   */
  cancelEdit() {
    this.isEdit = false
    this.nameCache = this.name
  }
}
