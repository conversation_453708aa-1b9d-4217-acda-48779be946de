/**
 * 我的培训班列表Vo
 */
import MyTrainClassCommoditySchoolVo from '@api/service/customer/train-class/query/vo/MyTrainClassCommodityVo'
import ThirdPartyItem from '@api/service/diff/customer/fjzj/train-class/model/ThirdPartyItem'
import { DropClassExtendedInfoResponse } from '@api/diff-gateway/platform-jxjypxtypt-fjzj-school'
import fjzjTradeGateway from '@api/diff-gateway/platform-jxjypxtypt-fjzj-trade'
import HywSchemeRefundStatus, {
  HywSchemeRefundEnum
} from '@api/service/diff/customer/fjzj/train-class/enums/HywSchemeRefundStatus'
import { Response, ResponseStatus } from '@hbfe/common'
import { CourseType } from '@api/service/diff/customer/fjzj/train-class/enums/CourseType'

class MyTrainClassCommodityVo extends MyTrainClassCommoditySchoolVo {
  /**
   * 退课扩展消息
   */
  dropClassExtendedInfoResponse = new DropClassExtendedInfoResponse()
  /**
   * 第三方平台信息
   */
  thirdPartyInfo: ThirdPartyItem = null

  /**
   * 校验口退款状态
   */
  refundStatus: HywSchemeRefundStatus = new HywSchemeRefundStatus()
  /**
   *前往课程方案退款状态
   * @param orderNo 订单号
   * @param type 退课类型
   */
  async checkPublicRefundStatus(orderNo: string, type: CourseType): Promise<Response<HywSchemeRefundStatus>> {
    const response = new Response<HywSchemeRefundStatus>()
    const res = await fjzjTradeGateway.queryHymSchemeRefundInfo(orderNo)
    if (res.status.isSuccess() && res.data) {
      const publicCourseRefundStatus = res.data.publicCourse
      const professionalCourseRefundStatus = res.data.professionalCourse
      if (publicCourseRefundStatus.refund && publicCourseRefundStatus.completed && type === CourseType.public_course) {
        //公需课退款完成
        this.refundStatus.current = HywSchemeRefundEnum.public_refunded
        response.status = new ResponseStatus(Number(res.data.code), res.data.message)
        response.data = this.refundStatus
        return response
      }
      if (
        professionalCourseRefundStatus.refund &&
        professionalCourseRefundStatus.completed &&
        type === CourseType.professional_course
      ) {
        //专业课退款完成
        this.refundStatus.current = HywSchemeRefundEnum.professional_refunded
        response.status = new ResponseStatus(Number(res.data.code), res.data.message)
        response.data = this.refundStatus
        return response
      }
      if (
        (professionalCourseRefundStatus.refund && !professionalCourseRefundStatus.completed) ||
        (publicCourseRefundStatus.refund && !publicCourseRefundStatus.completed)
      ) {
        //有课程处于退款状态中
        this.refundStatus.current = HywSchemeRefundEnum.scheme_refunding
      }
      response.status = new ResponseStatus(Number(res.data.code), res.data.message)
      response.data = this.refundStatus
    }
    return response
  }
}
export default MyTrainClassCommodityVo
