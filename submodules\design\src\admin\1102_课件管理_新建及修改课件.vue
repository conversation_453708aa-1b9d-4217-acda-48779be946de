<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">课件管理</el-breadcrumb-item>
      <el-breadcrumb-item>新建课件</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="120px" class="m-form">
                <el-form-item label="课件分类：" required>
                  <!-- 不可修改添加disabled属性 -->
                  <el-cascader
                    clearable
                    filterable
                    :options="cascader"
                    :show-all-levels="false"
                    class="form-m"
                    placeholder="请选择课件分类"
                  />
                  <el-button type="text" class="f-ml15">新建分类</el-button>
                </el-form-item>
                <el-form-item label="课件名称：" required>
                  <el-input v-model="form.name" clearable maxlength="50" show-word-limit placeholder="请输入课件名称" />
                </el-form-item>
                <el-form-item label="课件供应商：" required>
                  <!-- 不可修改添加disabled属性 -->
                  <el-select v-model="form.region" clearable filterable placeholder="请选择课件供应商" class="form-l">
                    <el-option value="选项1"></el-option>
                    <el-option value="选项2"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="课件教师：" required>
                  <el-input v-model="form.name" clearable maxlength="50" show-word-limit placeholder="请输入课件教师" />
                </el-form-item>
                <el-form-item label="教师简介：">
                  <el-input
                    type="textarea"
                    :rows="6"
                    v-model="form.desc"
                    maxlength="300"
                    show-word-limit
                    placeholder="请输入教师简介"
                  />
                </el-form-item>
                <el-form-item label="课件简介：">
                  <el-input
                    type="textarea"
                    :rows="6"
                    v-model="form.desc"
                    maxlength="300"
                    show-word-limit
                    placeholder="请输入教师简介"
                  />
                </el-form-item>
                <!-- 新建课件页面不显示该字段，修改时正常显示 -->
                <el-form-item label="课件状态：" required>
                  <el-radio-group v-model="form.resource">
                    <el-radio label="正常"></el-radio>
                    <el-radio label="停用"></el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <!-- 关联媒体-华为云 -->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">关联媒体</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <div class="m-as-form">
                <div class="item">
                  <div class="tit">媒体源：</div>
                  <div class="con">
                    <!-- 不可修改添加disabled属性 -->
                    <el-radio-group v-model="media1">
                      <el-radio label="1">华为云</el-radio>
                      <el-radio label="2">外链地址</el-radio>
                    </el-radio-group>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                      <div slot="content">
                        <p>1.如是媒体上传形式选择华为云，如是外部链接选择外链地址，请根据实际资源情况进行选择。</p>
                        <p>2.媒体支持文件类型与格式：</p>
                        <ul style="padding: 0 25px; margin: 0;">
                          <li>文档格式：doc、 xls、xlsx、pdf</li>
                          <li>视频格式：mp4、 m3u8</li>
                          <li>视频压缩包： rar、zip</li>
                        </ul>
                        <p>
                          3.外部链接形式：提供播放视频.m3u8地址，建议不要带授权或防盗链，采用可以直接播放方式。视频播放地址格式如：https://xxx.com/xxxxx.m3u8。
                        </p>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <el-form ref="form" :model="form" label-width="120px" class="m-form">
                <el-form-item label="媒体文件：">
                  <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false" style="float: left;">
                    <el-button type="primary" plain>选择文件</el-button>
                  </el-upload>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>媒体支持文件类型与格式：</p>
                      <p>1.文档格式：doc、 xls、xlsx、pdf</p>
                      <p>2.视频格式：mp4、 m3u8</p>
                    </div>
                  </el-tooltip>
                  <div class="m-file-upload f-mt15">
                    <!--视频-->
                    <i class="icon el-icon-film"></i>
                    <span class="name">这里是文件名称文件名称文件名称这里是文件名称文件名称文件名称</span>
                    <span class="size">(5MB)</span>
                    <el-progress :percentage="50"></el-progress>
                    <span class="time">00:02:54</span>
                    <el-button type="text">删除</el-button>
                  </div>
                  <div class="m-file-upload f-mt15">
                    <!--压缩包-->
                    <i class="icon el-icon-files"></i>
                    <span class="name">这里是文件名称文件名称文件名称</span>
                    <span class="size">(5MB)</span>
                    <el-progress :percentage="50"></el-progress>
                    <span class="time">00:02:54</span>
                    <el-button type="text">删除</el-button>
                  </div>
                  <div class="m-file-upload f-mt15">
                    <!--文档-->
                    <i class="icon el-icon-document"></i>
                    <span class="name">这里是文件名称</span>
                    <span class="size">(5MB)</span>
                    <el-progress :percentage="50"></el-progress>
                    <el-button type="text">删除</el-button>
                  </div>
                </el-form-item>
                <el-form-item label="媒体时长：" required>
                  <el-input v-model="form.name" class="input-num" />
                  <span class="f-mlr10">时</span>
                  <el-input v-model="form.name" class="input-num" />
                  <span class="f-mlr10">分</span>
                  <el-input v-model="form.name" class="input-num" />
                  <span class="f-mlr10">秒</span>
                </el-form-item>
                <el-form-item label=" " class="is-text">
                  <el-alert type="warning" show-icon :closable="false" class="m-alert">
                    课件未被课程引用可直接替换媒体文件。如课件已被课程引用，如需替换媒体文件，要求替换后的媒体与替换前的媒体文件格式一致、视频的时长需大于等于替换前的媒体文件大小。
                  </el-alert>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <!-- 关联媒体-外链地址 -->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">关联媒体</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <div class="m-as-form">
                <div class="item">
                  <div class="tit">媒体源：</div>
                  <div class="con">
                    <!-- 不可修改添加disabled属性 -->
                    <el-radio-group v-model="media2">
                      <el-radio label="1">华为云</el-radio>
                      <el-radio label="2">外链地址</el-radio>
                    </el-radio-group>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                      <div slot="content">
                        <p>1.如是媒体上传形式选择华为云，如是外部链接选择外链地址，请根据实际资源情况进行选择。</p>
                        <p>2.媒体支持文件类型与格式：</p>
                        <ul style="padding: 0 25px; margin: 0;">
                          <li>文档格式：doc、 xls、xlsx、pdf</li>
                          <li>视频格式：mp4、 m3u8</li>
                          <li>视频压缩包： rar、zip</li>
                        </ul>
                        <p>
                          3.外部链接形式：提供播放视频.m3u8地址，建议不要带授权或防盗链，采用可以直接播放方式。视频播放地址格式如：https://xxx.com/xxxxx.m3u8。
                        </p>
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
              <el-form ref="form" :model="form" label-width="120px" class="m-form">
                <el-form-item label="媒体名称：">
                  <el-input clearable placeholder="请输入媒体名称" />
                </el-form-item>
                <el-form-item label="标清地址：">
                  <el-input
                    clearable
                    placeholder="请输入具体的媒体播放链接地址，视频播放地址格式如: https/lxxxcom/xxxxx.m3u8"
                  />
                </el-form-item>
                <el-form-item label="高清地址：">
                  <el-input
                    clearable
                    placeholder="请输入具体的媒体播放链接地址，视频播放地址格式如: https/lxxxcom/xxxxx.m3u8"
                  />
                </el-form-item>
                <el-form-item label="超清地址：">
                  <el-input
                    clearable
                    placeholder="请输入具体的媒体播放链接地址，视频播放地址格式如: https/lxxxcom/xxxxx.m3u8"
                  />
                </el-form-item>
                <el-form-item label="媒体信息：">
                  <div class="m-file-upload f-mt15">
                    <!--视频-->
                    <i class="icon el-icon-film"></i>
                    <span class="name">这里是文件名称文件名称文件名称这里是文件名称文件名称文件名称</span>
                    <span class="size">(5MB)</span>
                    <span class="time">00:02:54</span>
                    <el-button type="text">预览</el-button>
                  </div>
                </el-form-item>

                <el-form-item label=" " class="is-text">
                  <el-alert type="warning" show-icon :closable="false" class="m-alert">
                    解析完成后显示媒体时长，提供播放视频.m3u8地址，建议不要带授权或防盗链，采用可以直接播放方式。视频播放地址格式如：https://xxx.com/xxxxx.m3u8，以上三种播放地址必须填写一项。
                  </el-alert>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        media1: '1',
        media2: '2',
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
