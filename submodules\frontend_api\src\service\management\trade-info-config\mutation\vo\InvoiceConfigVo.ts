import {
  InvoiceCategoryConfig,
  InvoiceCategoryConfig1,
  InvoiceConfigResponse
} from '@api/ms-gateway/ms-trade-configuration-v1'
import { DeadlineYearTypeEunm } from '@api/service/common/enums/trade-configuration/DeadlineYearType'
import { InvoiceCategoryEunm } from '@api/service/common/enums/trade-configuration/InvoiceCtegory'
import moment from 'moment'
import InvoiceCategoryConfigVo from './InvoiceCategoryConfigVo'
import { InvoiceTitleTypeEnum } from '@api/service/common/enums/trade-configuration/InvoiceTitleType'

class InvoiceConfigVo {
  /**
   * 发票配置编号
   */
  configId = ''
  /**
   * 是否提供发票 openInvoiceType 0 不开放
   */
  allowProvide: boolean = null
  /**
   * 是否开放发票 openInvoiceType 1 自主选择 2 强制提供
   */
  allowOpen = 0
  /**
   * 是否开放补要发票（是否允许索取发票）
   */
  allowAskFor: boolean = null
  /**
   * 索取发票截止日期（格式MM/dd）
   */
  askForInvoiceDeadline?: string = ''
  /**
   * 索取发票年度类型 1-当年度 2-下一个年度
   */
  askForInvoiceYearType?: DeadlineYearTypeEunm = 0
  /**
   * 发票种类勾选checkBox组
   */
  invoiceTypeGroupCheck: Array<InvoiceCategoryEunm> = new Array<InvoiceCategoryEunm>()

  /**
   * 增值税普票信息
   */
  vatOrdinaryInvoice: InvoiceCategoryConfigVo = new InvoiceCategoryConfigVo()

  /**
   * 增值税专票
   */
  vatSpecialInvoice: InvoiceCategoryConfigVo = new InvoiceCategoryConfigVo()

  static from(dto: InvoiceConfigResponse): InvoiceConfigVo {
    const vo = new InvoiceConfigVo()
    // invoiceConfig.configId = res.configId
    vo.allowProvide = dto.openInvoiceType != 0
    vo.allowOpen = dto.openInvoiceType
    vo.allowAskFor = dto.allowAskFor
    vo.askForInvoiceDeadline = vo.dateFormat(dto.askForInvoiceYearType, dto.askForInvoiceDeadline)
    vo.askForInvoiceYearType = dto.askForInvoiceYearType ? dto.askForInvoiceYearType : 0
    dto.allowInvoiceCategoryList?.length &&
      dto.allowInvoiceCategoryList.map(item => {
        vo.invoiceTypeGroupCheck.push(item.invoiceCategory)
        // 增值税普票
        if (item.invoiceCategory === InvoiceCategoryEunm.VAT_GENERAL_INVOICE) {
          vo.vatOrdinaryInvoice.invoiceCategory = InvoiceCategoryEunm.VAT_GENERAL_INVOICE
          vo.vatOrdinaryInvoice.invoiceMethod = item.invoiceMethod
          vo.vatOrdinaryInvoice.invoiceTitleTypes = item.invoiceTitleTypes
          if (item.invoiceRemarksType) {
            vo.vatOrdinaryInvoice.remarkMode = item.invoiceRemarksType
          }
          vo.vatOrdinaryInvoice.remark = item.invoiceRemarksContent
        }
        // 增值税专票
        if (item.invoiceCategory === InvoiceCategoryEunm.VAT_SPECIAL_INVOICE) {
          vo.vatSpecialInvoice.invoiceCategory = InvoiceCategoryEunm.VAT_SPECIAL_INVOICE
          vo.vatSpecialInvoice.invoiceMethodWithSpecial = item.invoiceType
          vo.vatSpecialInvoice.invoiceTitleTypes = item.invoiceTitleTypes
          // vo.vatOrdinaryInvoice.remarkMode = item.invoiceRemarksType
          if (item.invoiceTitleTypes) {
            vo.vatSpecialInvoice.remarkMode = item.invoiceRemarksType
          }
          vo.vatSpecialInvoice.remark = item.invoiceRemarksContent
        }
      })
    return vo
  }

  static to(vo: InvoiceConfigVo): InvoiceConfigResponse {
    const request = new InvoiceConfigResponse()
    request.openInvoiceType = vo.allowProvide ? vo.allowOpen : 0
    request.allowAskFor = vo.allowAskFor
    request.askForInvoiceDeadline = vo.askForInvoiceDeadline ? moment(vo.askForInvoiceDeadline).format('MM/DD') : ''
    request.askForInvoiceYearType = vo.askForInvoiceYearType
    request.allowInvoiceCategoryList = new Array<InvoiceCategoryConfig>()
    vo.invoiceTypeGroupCheck.map(item => {
      // 增值税普票
      if (item === InvoiceCategoryEunm.VAT_GENERAL_INVOICE) {
        const invoice = new InvoiceCategoryConfig()
        invoice.invoiceCategory = InvoiceCategoryEunm.VAT_GENERAL_INVOICE
        invoice.invoiceType = 1 // 普票目前只有电子故写死
        invoice.invoiceTitleTypes = vo.vatOrdinaryInvoice.invoiceTitleTypes
        invoice.invoiceMethod = vo.vatOrdinaryInvoice.invoiceMethod
        invoice.invoiceRemarksType = vo.vatOrdinaryInvoice.remarkMode
        invoice.invoiceRemarksContent =
          vo.vatOrdinaryInvoice.remarkMode == 1 ? null : vo.vatOrdinaryInvoice.remark || null
        request.allowInvoiceCategoryList.push(invoice)
      }
      // 增值税专票
      if (item === InvoiceCategoryEunm.VAT_SPECIAL_INVOICE) {
        const invoice = new InvoiceCategoryConfig()
        invoice.invoiceCategory = InvoiceCategoryEunm.VAT_SPECIAL_INVOICE
        invoice.invoiceType = vo.vatSpecialInvoice.invoiceMethodWithSpecial
        invoice.invoiceTitleTypes = vo.vatSpecialInvoice.invoiceTitleTypes
        invoice.invoiceMethod = 2 // 专票目前只有线下开票故写死
        invoice.invoiceRemarksType = vo.vatSpecialInvoice.remarkMode
        invoice.invoiceRemarksContent =
          vo.vatSpecialInvoice.remarkMode == 1 ? null : vo.vatSpecialInvoice.remark || null
        request.allowInvoiceCategoryList.push(invoice)
      }
    })

    return request
  }

  dateFormat(yearType: number, deadline: string) {
    if (yearType && deadline) {
      if (yearType === DeadlineYearTypeEunm.CURRENT_YEAR) {
        return `${new Date().getFullYear()}-${deadline.replace('/', '-')}`
      } else {
        return `${new Date().getFullYear() + 1}-${deadline.replace('/', '-')}`
      }
    } else {
      return ''
    }
  }
}
export default InvoiceConfigVo
