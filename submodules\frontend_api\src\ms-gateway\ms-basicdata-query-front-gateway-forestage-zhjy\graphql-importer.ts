import findRoleListByIdentity from './queries/findRoleListByIdentity.graphql'
import getCommonNewsDetailWithPreviousAndNext from './queries/getCommonNewsDetailWithPreviousAndNext.graphql'
import getEnterpriseUnitAdminInfoByPublish from './queries/getEnterpriseUnitAdminInfoByPublish.graphql'
import getEnterpriseUnitAdminInfoInMyself from './queries/getEnterpriseUnitAdminInfoInMyself.graphql'
import getEnterpriseUnitInfoInLibraryTeacher from './queries/getEnterpriseUnitInfoInLibraryTeacher.graphql'
import getEnterpriseUnitInfoInStudent from './queries/getEnterpriseUnitInfoInStudent.graphql'
import getNewsCategoryId from './queries/getNewsCategoryId.graphql'
import getNewsDetailWithPreviousAndNext from './queries/getNewsDetailWithPreviousAndNext.graphql'
import getUserInfoInMyself from './queries/getUserInfoInMyself.graphql'
import listChildNewsCategory from './queries/listChildNewsCategory.graphql'
import listPopUpsNews from './queries/listPopUpsNews.graphql'
import listReviewTopNews from './queries/listReviewTopNews.graphql'
import listRootNewsCategory from './queries/listRootNewsCategory.graphql'
import listTopNewsCategory from './queries/listTopNewsCategory.graphql'
import pageCommonSimpleNewsByPublish from './queries/pageCommonSimpleNewsByPublish.graphql'
import pageCompleteNewsByCodeList from './queries/pageCompleteNewsByCodeList.graphql'
import pageCompleteNewsByRootCategoryCode from './queries/pageCompleteNewsByRootCategoryCode.graphql'
import pageSimpleNewsByPublish from './queries/pageSimpleNewsByPublish.graphql'
import pageSimpleNewsByPublishAndAreaCodePath from './queries/pageSimpleNewsByPublishAndAreaCodePath.graphql'
import pageSimpleNewsByPublishForOrder from './queries/pageSimpleNewsByPublishForOrder.graphql'
import pageUserInfoInGeneral from './queries/pageUserInfoInGeneral.graphql'

export {
  findRoleListByIdentity,
  getCommonNewsDetailWithPreviousAndNext,
  getEnterpriseUnitAdminInfoByPublish,
  getEnterpriseUnitAdminInfoInMyself,
  getEnterpriseUnitInfoInLibraryTeacher,
  getEnterpriseUnitInfoInStudent,
  getNewsCategoryId,
  getNewsDetailWithPreviousAndNext,
  getUserInfoInMyself,
  listChildNewsCategory,
  listPopUpsNews,
  listReviewTopNews,
  listRootNewsCategory,
  listTopNewsCategory,
  pageCommonSimpleNewsByPublish,
  pageCompleteNewsByCodeList,
  pageCompleteNewsByRootCategoryCode,
  pageSimpleNewsByPublish,
  pageSimpleNewsByPublishAndAreaCodePath,
  pageSimpleNewsByPublishForOrder,
  pageUserInfoInGeneral
}
