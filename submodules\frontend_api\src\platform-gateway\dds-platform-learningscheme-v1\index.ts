import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/dds-platform-learningscheme-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'dds-platform-learningscheme-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 保存报到规则配置请求体
 */
export class SaveReportRuleSettingRequest {
  /**
   * 学习方案ID
   */
  learningSchemeId?: string
  /**
   * 拥有者类型
   */
  ownerType?: number
  /**
   * 拥有者ID
   */
  ownerId?: string
  /**
   * 期别ID
   */
  issueId?: string
  /**
   * 打卡半径范围(x米)
   */
  signRadiusRange?: number
}

/**
 * <AUTHOR>
@since
 */
export class AttendanceSettingRequest {
  /**
   * 方案id
   */
  learningSchemeId?: string
  /**
   * 所有者类型
方案  1
期别  3
@see OwnerTypes
   */
  ownerTye: number
  /**
   * 方案   方案id
期数  对应期数id
   */
  ownerId?: string
  /**
   * 签到
   */
  signIn?: AttendanceSignRequest
  /**
   * 签退
   */
  signOut?: AttendanceSignRequest
}

/**
 * <AUTHOR>
@since
 */
export class AttendanceSignRequest {
  /**
   * 是否开启
   */
  enable: boolean
  /**
   * 签到频率
半天  1;
每节课  2;
@see SignFrequencyTypes
   */
  frequency: number
  /**
   * 签到半径
   */
  radius: number
  /**
   * 签到开始前
单位:(秒)
   */
  beforeSecond: number
  /**
   * 开始后
单位:(秒)
   */
  afterSecond: number
}

/**
 * <AUTHOR>
@since
 */
export class LearningResourceRequest {
  /**
   * 附件名称
   */
  name?: string
  /**
   * 格式
   */
  format?: string
  /**
   * 路径
   */
  filePath?: string
}

/**
 * <AUTHOR>
@since
 */
export class LearningResourceSettingRequest {
  /**
   * 方案id
   */
  learningSchemeId?: string
  /**
   * 所有者类型
方案  1
期别  3
@see OwnerTypes
   */
  ownerTye: number
  /**
   * 方案   方案id
期数  对应期数id
   */
  ownerId?: string
  /**
   * 已添加的学习资料
   */
  resourceList?: Array<LearningResourceRequest>
}

/**
 * E429 已存在报名学员
<AUTHOR>
@since
 */
export class PreTrainingResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 保存考勤设置
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveAttendanceSetting(
    request: AttendanceSettingRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveAttendanceSetting,
    operation?: string
  ): Promise<Response<PreTrainingResponse>> {
    return commonRequestApi<PreTrainingResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存学习资料设置
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveLearningResourceSetting(
    request: LearningResourceSettingRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveLearningResourceSetting,
    operation?: string
  ): Promise<Response<PreTrainingResponse>> {
    return commonRequestApi<PreTrainingResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存报到规则配置
   * @param request 保存报到规则配置请求体
   * @return 保存结果
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveReportRuleSetting(
    request: SaveReportRuleSettingRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveReportRuleSetting,
    operation?: string
  ): Promise<Response<PreTrainingResponse>> {
    return commonRequestApi<PreTrainingResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
