<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <el-row :gutter="20" type="flex" justify="center">
        <el-col :lg="20" :xl="18">
          <el-form ref="form" :inline="true" :model="form" label-width="7.5rem" class="m-form f-clear f-mt20">
            <el-col :span="16">
              <el-form-item label="电子票服务商：">
                <el-radio-group v-model="elcInvoiceProviderId" @change="handleProviderChange">
                  <el-radio label="7">诺诺发票</el-radio>
                  <el-radio label="6">百旺金赋</el-radio>
                  <el-radio label="9">诺税通</el-radio>
                  <el-radio label="8">诺税通全电票</el-radio>
                </el-radio-group>
                <!-- <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                  <div slot="content">
                    1.平台仅支持诺诺发票的增值税电子普通发票的自动开票对接。请先购买诺诺开票服务，再提供以下的信息。
                    <a class="f-link f-cb f-underline" @click="goNuoNuoInvoice">前往诺诺发票>&gt;</a>
                  </div>
                </el-tooltip> -->
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item class="f-tr">
                <span class="f-link f-cb f-underline" @click="dialog1 = true">查看发票示例图片</span>
                <!--示例图片弹窗-->
                <el-dialog :visible.sync="dialog1" width="1100px" class="m-dialog-pic">
                  <img src="@design/admin/assets/images/demo-invoice.png" alt="" />
                </el-dialog>
              </el-form-item>
            </el-col>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
    <el-card shadow="never" class="m-card f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">基础信息</span>
      </div>
      <el-row :gutter="20" type="flex" justify="center">
        <el-col :lg="20" :xl="18">
          <el-form
            ref="modifyInvoiceBaseForm"
            validate-on-rule-change
            :inline="true"
            :model="mutationInvoiceConfig.eleInvoiceParams"
            :rules="baseRules"
            label-width="9.5rem"
            class="m-form f-clear f-mt20"
          >
            <el-col :span="12">
              <el-form-item label="单位名称：" required prop="name">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.name"
                  clearable
                  placeholder="请输入单位名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="纳税人识别号：" required prop="taxpayerNo">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.taxpayerNo"
                  clearable
                  placeholder="请输入纳税人识别号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="地址：" required prop="address">
                <el-input v-model="mutationInvoiceConfig.eleInvoiceParams.address" clearable placeholder="请输入地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="电话："
                :rules="
                  elcInvoiceProviderId == 7 || elcInvoiceProviderId == 9 || elcInvoiceProviderId == 8
                    ? rules['phone']
                    : [{ required: false }]
                "
                prop="phone"
              >
                <el-input v-model="mutationInvoiceConfig.eleInvoiceParams.phone" clearable placeholder="请输入电话" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户行：" required prop="bankName">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.bankName"
                  clearable
                  placeholder="请输入开户行"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户帐号：" required prop="bankAccount">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.bankAccount"
                  clearable
                  placeholder="请输入开户帐号"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开票最大金额：" required prop="invoiceMaxMoney">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.invoiceMaxMoney"
                  type="number"
                  min="0"
                  clearable
                  @keydown.native="
                    (e) => (e.returnValue = ['e', 'E', '-', '+'].includes(e.key) ? false : e.returnValue)
                  "
                  placeholder="请输入开票最大金额（元）"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="收款人："
                :rules="
                  elcInvoiceProviderId == 7 || elcInvoiceProviderId == 9 || elcInvoiceProviderId == 8
                    ? rules['payee']
                    : [{ required: false }]
                "
                prop="payee"
              >
                <el-input v-model="mutationInvoiceConfig.eleInvoiceParams.payee" clearable placeholder="请输入收款人" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item
                label="复核："
                :rules="
                  elcInvoiceProviderId == 7 || elcInvoiceProviderId == 9 || elcInvoiceProviderId == 8
                    ? rules['reviewer']
                    : [{ required: false }]
                "
                prop="reviewer"
              >
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.reviewer"
                  clearable
                  placeholder="请输入复核"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开票人：" required prop="issuer">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.issuer"
                  clearable
                  placeholder="请输入开票人"
                />
              </el-form-item>
            </el-col>
            <template>
              <el-col v-if="mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId == 7" :span="12">
                <el-form-item label="用户标识：" required prop="invoiceProviderId">
                  <template slot="label">
                    <span class="f-vm">用户标识</span>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                      <div slot="content">
                        <p>用户标识作为唯一性标识,本平台支持诺诺发票两个版本的接口。</p>
                        <p>如采购的是1.0版本服务,请选择授权码。2.0版本服务请选择App</p>
                      </div>
                    </el-tooltip>
                    <span>：</span>
                  </template>
                  <el-select
                    :disabled="isInvoiceProviderId"
                    v-model="mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId"
                    clearable
                    class="mySelect"
                    placeholder="请选择用户标识类型"
                  >
                    <el-option
                      v-for="item in nuonuoCodeList"
                      :label="item.name"
                      :value="item.code"
                      :key="item.code"
                    ></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId === '5'">
                <el-form-item label="授权码：" required prop="secretAssessKey">
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.secretAssessKey"
                    clearable
                    placeholder="请输入授权码"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-show="
                  (elcInvoiceProviderId == 7 && mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId === '7') ||
                  (elcInvoiceProviderId == 9 && mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId === '9') ||
                  (elcInvoiceProviderId == 8 && mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId === '8')
                "
              >
                <el-form-item key="accessKey" label="AppKey：" required prop="accessKey">
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.accessKey"
                    clearable
                    placeholder="请输入AppKey"
                  />
                </el-form-item>
              </el-col>
              <el-col
                :span="12"
                v-show="
                  (elcInvoiceProviderId == 7 && mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId === '7') ||
                  (elcInvoiceProviderId == 9 && mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId === '9') ||
                  (elcInvoiceProviderId == 8 && mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId === '8')
                "
              >
                <el-form-item key="secretAssessKey" label="AppSecret：" required prop="secretAssessKey">
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.secretAssessKey"
                    clearable
                    placeholder="请输入AppSecret"
                  />
                </el-form-item>
              </el-col>
              <el-col
                v-show="elcInvoiceProviderId == 7 || elcInvoiceProviderId == 9 || elcInvoiceProviderId == 8"
                :span="12"
              >
                <el-form-item label="部门ID：">
                  <div slot="label">
                    <span class="f-vm">部门ID</span>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                      <div slot="content">
                        <p>部门ID用于第三方开票平台区分开票的来源，该信息由第三方开票平台提供，请确认后再填写。</p>
                        <p>如网校存在以下任一情况，需要提供部门ID：</p>
                        <p>1.网校需求区分，开票是属于单机版、网络版；</p>
                        <p>2.同一个公司，存在多税盘的情况；</p>
                      </div>
                    </el-tooltip>
                    <span>：</span>
                  </div>
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.deptId"
                    clearable
                    placeholder="请输入部门ID"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="elcInvoiceProviderId == 9 || elcInvoiceProviderId == 8">
                <el-form-item key="extensionNumber" label="分机号：" required prop="extensionNumber">
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.extensionNumber"
                    clearable
                    placeholder="请输入分机号"
                  />
                </el-form-item>
              </el-col>
              <!--              <el-col v-show="elcInvoiceProviderId == 7" :span="12">-->
              <!--                <el-form-item label="备注：" prop="remark">-->
              <!--                  <el-input-->
              <!--                    v-model="mutationInvoiceConfig.eleInvoiceParams.remark"-->
              <!--                    clearable-->
              <!--                    placeholder="请输入备注信息"-->
              <!--                  />-->
              <!--                </el-form-item>-->
              <!--              </el-col>-->
            </template>
            <template v-if="elcInvoiceProviderId == 6">
              <el-col :span="12">
                <el-form-item key="enterpriseCode" label="企业代码：" required prop="enterpriseCode">
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.enterpriseCode"
                    clearable
                    placeholder="请输入企业代码"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item key="secretAssessKey2" label="企业私钥：" required prop="secretAssessKey">
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.secretAssessKey"
                    clearable
                    placeholder="请输入企业私钥"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item key="accessKey2" label="平台公钥：" required prop="accessKey">
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.accessKey"
                    clearable
                    placeholder="请输入平台公钥"
                  />
                </el-form-item>
              </el-col>

              <el-col :span="12">
                <el-form-item key="taxIdCard" label="办税人身份证号：" required prop="taxIdCard">
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.taxIdCard"
                    clearable
                    placeholder="请输入办税人身份证号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item key="taxLoginAccount" label="电子税局登录账号：" required prop="taxLoginAccount">
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.taxLoginAccount"
                    clearable
                    placeholder="请输入电子税局登录账号"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item key="taxLoginPassword" label="电子税局登录密码：" required prop="taxLoginPassword">
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.taxLoginPassword"
                    show-password
                    auto-complete="new-password"
                    type="password"
                    placeholder="请输入电子税局登录密码"
                  />
                </el-form-item>
              </el-col>
            </template>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
    <el-card shadow="never" class="m-card f-mb15">
      <div slot="header" class="">
        <span class="tit-txt">服务类型 / 商品类型信息</span>
      </div>
      <el-row :gutter="20" type="flex" justify="center">
        <el-col :lg="20" :xl="18">
          <el-form
            ref="modifyInvoiceForm"
            :inline="true"
            :model="mutationInvoiceConfig.eleInvoiceParams"
            :rules="rules"
            label-width="10rem"
            class="m-form f-clear f-mt20"
          >
            <el-col :span="12">
              <el-form-item label="税务名称(税务编码)：" required prop="commodityCode">
                <el-select
                  v-model="mutationInvoiceConfig.eleInvoiceParams.commodityCode"
                  clearable
                  placeholder="请选择税务名称和税务编码"
                >
                  <el-option
                    v-for="item in commodityCodeList"
                    :label="item.name + '(' + item.code + ')'"
                    :value="item.code"
                    :key="item.code"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="服务名称：" required prop="serviceTitle">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.serviceTitle"
                  clearable
                  placeholder="请输入网校开票具体服务名称"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="规格型号：">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.specificationMode"
                  clearable
                  placeholder="请输入型号规格"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12" v-if="elcInvoiceProviderId == 7">
              <el-form-item label="单位：">
                <el-input
                  v-model="mutationInvoiceConfig.eleInvoiceParams.unitTitle"
                  clearable
                  placeholder="请输入单位"
                />
              </el-form-item>
            </el-col>
            <template v-else>
              <el-col :span="12">
                <el-form-item label="单位：">
                  <el-select v-model="uiUnitTypeComputed" placeholder="请选择票面单位">
                    <el-option v-for="[key, value] in unitType.map" :key="key" :value="key" :label="value"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12" v-if="uiUnitType === UnitTypeEnum.custom">
                <el-form-item label="自定义单位：" prop="unitTitle">
                  <el-input
                    v-model="mutationInvoiceConfig.eleInvoiceParams.unitTitle"
                    @input="filterSpecialChars"
                    clearable
                    placeholder="请输入自定义单位"
                  />
                </el-form-item>
              </el-col>
            </template>
            <el-col :span="12">
              <el-form-item label="是否显示数量和单价：">
                <el-select
                  v-model="mutationInvoiceConfig.eleInvoiceParams.printQuantity"
                  clearable
                  placeholder="请选择发票上是否显示数量和单价"
                >
                  <el-option label="显示" :value="true"></el-option>
                  <el-option label="不显示" :value="false"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="税务优惠：" v-if="getTaxFavouredShow" required prop="taxFavoured">
                <el-select
                  v-model="mutationInvoiceConfig.eleInvoiceParams.taxFavoured"
                  clearable
                  placeholder="请选择税务优惠"
                >
                  <el-option
                    v-for="item in taxIncentivesList"
                    :label="item.desc"
                    :value="item.code"
                    :key="item.code"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="税率：" required prop="rate">
                <el-input
                  type="number"
                  v-model="mutationInvoiceConfig.eleInvoiceParams.rate"
                  min="0"
                  @keydown.native="
                    (e) => (e.returnValue = ['e', 'E', '-', '+'].includes(e.key) ? false : e.returnValue)
                  "
                  clearable
                  placeholder="请输入税率，如税率6%填写6"
                >
                  <template slot="append">%</template>
                </el-input>
              </el-form-item>
            </el-col>
            <!--            <el-col :span="12">
              <el-form-item label="单价：">
                <el-select
                  v-model="mutationInvoiceConfig.eleInvoiceParams.printPrice"
                  clearable
                  filterable
                  placeholder="请选择发票上是否显示单价"
                >
                  <el-option label="显示" :value="true"></el-option>
                  <el-option label="不显示" :value="false"></el-option>
                </el-select>
              </el-form-item>
            </el-col>-->
          </el-form>
        </el-col>
      </el-row>
    </el-card>
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="doCancel">取消</el-button>
      <el-button type="primary" @click="doSave" :loading="loading">保存</el-button>
    </div>
    <give-up-dialog :give-up-model="uiConfig.giveUpModel" @callBack="resetData"></give-up-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Watch } from 'vue-property-decorator'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import QueryElectronicInvoiceConfig from '@api/service/management/online-school-config/functionality-setting/query/QueryElectronicInvoiceConfig'
  import CommodityCodeVo from '@api/service/management/online-school-config/functionality-setting/query/vo/CommodityCodeVo'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import EleInvoiceVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/common/EleInvoiceVo'
  import ElectronicInvoiceUpdateVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/ElectronicInvoiceUpdateVo'
  import UnitType, {
    UnitTypeEnum
  } from '@api/service/management/online-school-config/functionality-setting/enum/UnitTypeEnum'
  import { bind, debounce } from 'lodash-decorators'

  @Component({
    components: { GiveUpDialog }
  })
  export default class extends Vue {
    @Ref('modifyInvoiceBaseForm') modifyInvoiceBaseForm: any
    @Ref('modifyInvoiceForm') modifyInvoiceForm: any
    baseRules = {
      name: [
        {
          required: true,
          message: '请输入单位名称',
          trigger: ['change', 'blur']
        }
      ],
      taxpayerNo: [
        {
          required: true,
          message: '请输入纳税人识别号',
          trigger: ['change', 'blur']
        }
      ],
      address: [
        {
          required: true,
          message: '请输入地址',
          trigger: ['change', 'blur']
        }
      ],
      phone: [
        {
          required: true,
          message: '请输入电话',
          trigger: ['change', 'blur']
        }
      ],
      bankName: [
        {
          required: true,
          message: '请输入开户行',
          trigger: ['change', 'blur']
        }
      ],
      invoiceProviderId: [
        {
          required: true,
          message: '请选择用户标识',
          trigger: ['change', 'blur']
        }
      ],
      bankAccount: [
        {
          required: true,
          message: '请输入开户账号',
          trigger: ['change', 'blur']
        }
      ],
      invoiceMaxMoney: [
        {
          required: true,
          message: '请输入开票最大金额',
          trigger: ['change', 'blur']
        }
      ],
      payee: [
        {
          required: true,
          message: '请输入收款人',
          trigger: ['change', 'blur']
        }
      ],
      reviewer: [
        {
          required: true,
          message: '请输入复核',
          trigger: ['change', 'blur']
        }
      ],
      issuer: [
        {
          required: true,
          message: '请输入开票人',
          trigger: ['change', 'blur']
        }
      ],
      accessKey: [
        {
          required: true,
          message: '请输入AppKey',
          trigger: ['change', 'blur']
        }
      ],
      secretAssessKey: [
        {
          required: true,
          message: '请输入AppSecret',
          trigger: ['change', 'blur']
        }
      ],
      extensionNumber: [
        {
          required: true,
          message: '请输入分机号',
          trigger: ['change', 'blur']
        }
      ],
      enterpriseCode: [
        {
          required: true,
          message: '请输入企业代码',
          trigger: ['change', 'blur']
        }
      ],
      taxIdCard: [
        {
          required: true,
          message: '请输入办税人身份证号',
          trigger: ['change', 'blur']
        }
      ],
      taxLoginAccount: [
        {
          required: true,
          message: '请输入电子税局登录账号',
          trigger: ['change', 'blur']
        }
      ],
      taxLoginPassword: [
        {
          required: true,
          message: '请输入电子税局登录密码',
          trigger: ['change', 'blur']
        }
      ]
    }
    rules = {
      unitTitle: [
        {
          required: true,
          message: '请输入自定义单位',
          trigger: ['change', 'blur']
        },
        {
          validator: (rule: any, value: any, callback: any) => {
            if (this.mutationInvoiceConfig.eleInvoiceParams.unitTitle?.length > 5) {
              callback(new Error('超过最大字符数限制，请输入1~5个字符'))
            }
            callback()
          },
          trigger: ['change', 'blur']
        }
      ],
      rate: [
        {
          required: true,
          message: '请输入税率',
          trigger: ['change', 'blur']
        }
      ],
      commodityCode: [
        {
          required: true,
          message: '请输入税务名称（税务编码）',
          trigger: ['change', 'blur']
        }
      ],
      serviceTitle: [
        {
          required: true,
          message: '请输入服务名称',
          trigger: ['change', 'blur']
        }
      ],
      taxFavoured: [
        {
          required: true,
          message: '请选择税务优惠',
          trigger: ['change', 'blur']
        }
      ]
    }
    //选择诺诺版本
    // nuonuoCode = '0'
    nuonuoCodeList = [
      {
        name: '请选择用户标识',
        code: '0'
      },
      {
        name: '授权码',
        code: '5'
      },
      {
        name: 'App',
        code: '7'
      }
    ]
    dialog1 = false
    form = {
      resource: '',
      name: ''
    }
    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }
    providerList: Array<string> = new Array<string>()
    provider = ''
    commodityCodeList: Array<CommodityCodeVo> = new Array<CommodityCodeVo>()
    // 税务优惠list
    taxIncentivesList = [
      {
        code: 0,
        desc: '无优惠'
      },
      {
        code: 1,
        desc: '简易征收'
      }
    ]
    isModify = false
    //纳税人编号
    taxpayerNo = ''
    elcInvoiceProviderId = ''
    abledElcInvoiceProviderId = ''
    mutationInvoiceConfig = OnlineSchoolConfigModule.mutationFunctionalitySettingFactory.mutationElectronicInvoice
    /**
     * 是否不允许修改标识
     */
    isInvoiceProviderId = false
    /**
     * 保存配置loading
     */
    loading = false
    // 点击次数
    sum = 0
    /**
     * 单位类型
     */
    unitType = new UnitType()
    /**
     * ps:仅ui使用
     * 单位类型
     */
    uiUnitType: UnitTypeEnum = UnitTypeEnum.null
    get uiUnitTypeComputed() {
      return this.uiUnitType
    }
    set uiUnitTypeComputed(value: UnitTypeEnum) {
      this.uiUnitType = value

      if (this.uiUnitType === UnitTypeEnum.custom) {
        this.mutationInvoiceConfig.eleInvoiceParams.unitTitle = ''
      }
    }
    /**
     * 单位类型枚举
     */
    UnitTypeEnum = UnitTypeEnum
    /**
     * 文本框不可输入特殊字符，填写时不允许输入。例如： # $ % ^ & * 以及空格。
     */
    filterSpecialChars(input: string) {
      this.mutationInvoiceConfig.eleInvoiceParams.unitTitle = input.replace(
        /[`~!@#$%^&*()_\-+=<>?:"{}|,./;'\\[\]·~！@#￥%……&*（）——\-+={}|《》？：“”【】、；‘’，。、 ]+/i,
        ''
      )
    }
    /**
     * 延迟获取配置，为了处理清洗延迟
     */
    delayGetConfig() {
      return new Promise((resolve) => {
        setTimeout(async () => {
          try {
            await this.getInvoiceConfig()
          } catch (e) {
            console.error(e)
          } finally {
            resolve('fullfilled')
          }
        }, 2000)
      })
    }
    async doModify() {
      ;(this.mutationInvoiceConfig.eleInvoiceParams as ElectronicInvoiceUpdateVo).taxpayerId = this.taxpayerNo
      const res = await this.mutationInvoiceConfig.doUpdateElectronicInvoice()
      if (res.isSuccess()) {
        await this.getInvoiceConfig()
        this.$message.success('修改成功')
      } else {
        this.$message.error('修改失败')
      }
    }
    async doCreate() {
      const res = await this.mutationInvoiceConfig.doCreateElectronicInvoice()
      if (res.status.isSuccess()) {
        this.$message.success('保存成功')
        await this.delayGetConfig()
      } else {
        this.$message.error('保存失败')
      }
    }
    @debounce(200)
    @bind
    doSave() {
      if (!this.elcInvoiceProviderId) {
        this.$message.error('请先选择电子票服务商')
        return
      }
      this.modifyInvoiceBaseForm.validate((baseValid: boolean) => {
        this.modifyInvoiceForm.validate(async (valid: boolean) => {
          if (baseValid && valid) {
            this.loading = true
            if (this.mutationInvoiceConfig.eleInvoiceParams.printQuantity) {
              this.mutationInvoiceConfig.eleInvoiceParams.printQuantity = true
              this.mutationInvoiceConfig.eleInvoiceParams.printPrice = true
            } else {
              this.mutationInvoiceConfig.eleInvoiceParams.printQuantity = false
              this.mutationInvoiceConfig.eleInvoiceParams.printPrice = false
            }
            if (this.elcInvoiceProviderId != '7') {
              if (this.uiUnitType !== UnitTypeEnum.custom && this.uiUnitType !== UnitTypeEnum.null) {
                this.mutationInvoiceConfig.eleInvoiceParams.unitTitle = this.unitType.map.get(this.uiUnitType)
              }
              if (this.uiUnitType === UnitTypeEnum.null) {
                this.mutationInvoiceConfig.eleInvoiceParams.unitTitle = null
              }
            }
            try {
              if (this.isModify) {
                await this.doModify()
              } else {
                await this.doCreate()
              }
            } catch (e) {
              console.error(e)
            } finally {
              this.loading = false
            }
          }
        })
      })
    }

    async resetData() {
      await this.getInvoiceConfig()
    }

    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
    }

    goNuoNuoInvoice() {
      window.open('https://fp.nuonuo.com/#/', '_blank')
    }

    async getInvoiceConfig() {
      //获取纳税人编号
      const taxpayerNoRes =
        await OnlineSchoolConfigModule.queryFunctionalitySettingFactory.queryElectronicInvoiceConfig.queryElectronicInvoiceTaxpayerId()
      if (taxpayerNoRes?.status?.isSuccess()) {
        if (taxpayerNoRes?.data === '') {
          this.isModify = false
        } else {
          this.isModify = true
          this.taxpayerNo = taxpayerNoRes.data as string
        }
      }

      //获取发票服务商以及税务编码集合
      const res =
        await OnlineSchoolConfigModule.queryFunctionalitySettingFactory.queryElectronicInvoiceConfig.queryElectronicInvoiceConfig()
      if (res?.status?.code == 200) {
        this.providerList = res.data.providerList
        if (this.providerList.length) {
          // this.provider = this.providerList.includes('6') ? '6' : this.providerList[0]
          // this.provider = InvoiceProviderEnum.NUONUO
        }
        this.commodityCodeList = res.data.commodityCodeList
      }
      if (!this.taxpayerNo) {
        return
      }
      const listRes =
        await OnlineSchoolConfigModule.queryFunctionalitySettingFactory.queryElectronicInvoiceConfig.queryElectronicInvoiceDetail(
          this.taxpayerNo
          // this.provider
        )
      // InvoiceProviderEnum.NUONUOV2
      if (listRes?.status?.code == 200) {
        this.mutationInvoiceConfig.eleInvoiceParams = Object.assign(new EleInvoiceVo(), listRes.data)
        this.mutationInvoiceConfig.eleInvoiceParams.rate = this.mutationInvoiceConfig.eleInvoiceParams.rate * 100

        if (this.mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId != '7') {
          this.unitType.map.forEach((value, key) => {
            if (value == this.mutationInvoiceConfig.eleInvoiceParams?.unitTitle && key !== UnitTypeEnum.null) {
              this.uiUnitType = key
            }
          })
          if (this.uiUnitType === UnitTypeEnum.null && this.mutationInvoiceConfig.eleInvoiceParams?.unitTitle) {
            this.uiUnitType = UnitTypeEnum.custom
          }
          if (!this.mutationInvoiceConfig.eleInvoiceParams?.unitTitle) {
            this.uiUnitType = UnitTypeEnum.null
          }
        }
      }
      this.elcInvoiceProviderId = this.abledElcInvoiceProviderId =
        this.mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId
      this.handleProviderChange(this.elcInvoiceProviderId)
    }

    async created() {
      await this.getInvoiceConfig()
      console.log(this.mutationInvoiceConfig, 'fffffffff')

      if (this.mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId !== '0') {
        this.isInvoiceProviderId = true
      }
    }
    // 服务商变化
    async handleProviderChange(val: string) {
      if (this.abledElcInvoiceProviderId && this.elcInvoiceProviderId != this.abledElcInvoiceProviderId) {
        this.elcInvoiceProviderId = this.mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId
        this.$alert('暂不支持直接切换服务商，请联系平台技术处理。', { type: 'warning' })
        return
      }
      if (val == '6') {
        this.mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId = '6'
        this.baseRules.accessKey[0].message = '请输入平台公钥'
        this.baseRules.secretAssessKey[0].message = '请输入企业私钥'
      } else if (val == '7' || val == '8' || val == '9') {
        this.mutationInvoiceConfig.eleInvoiceParams.invoiceProviderId = val
        this.baseRules.accessKey[0].message = '请输入AppKey'
        this.baseRules.secretAssessKey[0].message = '请输入AppSecret'
      }
      // 第一次不弹校验 不然感觉体验不是很好
      if (this.sum > 0) {
        this.modifyInvoiceBaseForm.validate()
      }
      this.sum++
      // await this.getInvoiceConfig()
    }

    get getTaxFavouredShow() {
      return this.elcInvoiceProviderId == '8' || this.elcInvoiceProviderId == '6'
    }
  }
</script>
<style scoped>
  ::v-deep .el-input input::-webkit-outer-spin-button,
  ::v-deep .el-input input::-webkit-inner-spin-button {
    -webkit-appearance: none !important;
  }
  ::v-deep .el-input input[type='number'] {
    -moz-appearance: textfield;
  }
  ::v-deep .mySelect .el-input.is-disabled .el-input__inner {
    height: 36px !important;
  }
</style>
