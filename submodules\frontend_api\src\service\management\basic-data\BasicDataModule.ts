import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import platformUser, { UserInfoDTO } from '@api/gateway/PlatformUser'
import platformStatisticReportQuery, {
  UserLoginLogRequest,
  UserLoginLogResponse
} from '@api/gateway/PlatformStatisticReportQuery'
import ConfigCenterModule from '@api/service/common/config-center/ConfigCenter'
import platformBasicDataGateway, { JobLogDTO, UserJobParamDTO } from '@api/gateway/PlatformBasicData'
import Vue from 'vue'
import { Page } from '@api/gateway/PreExam-default'
import { RegionInfo } from '@api/service/management/basic-data/models/RegionInfo'
import { ResponseStatus } from '@api/Response'
import { RegionTree } from '@api/service/management/basic-data/models/RegionTree'
import { UnAuthorize } from '@api/Secure'
import msAccountGateway from '@api/gateway/ms-account-v1'

interface BasicDataState {
  defaultProvinceTree: RegionTree
  provinceMap: {
    [key: string]: RegionInfo
  }
  /**
   * 省份与城市对应map,key为省份id，value为城市集合
   */
  cityMap: {
    [key: string]: Array<RegionInfo>
  }
  /**
   * 城市与曲线对应map，key为城市id，value为曲线集合
   */
  countyMap: {
    [key: string]: Array<RegionInfo>
  }
  /**
   * 省市区，树形结构map
   */
  provinceTreeMap: {
    [key: string]: RegionTree
  }
  userInfo: UserInfoDTO

  /**
   * 异步任务处理结果
   */
  userJobList: Array<JobLogDTO>

  /**
   * 异步任务总记录数
   */
  userJobTotalSize: number
  /**
   * 用户登录日志
   */
  loginLogList: Array<UserLoginLogResponse>
  loginLogTotalPageSize: number
  loginLogTotalSize: number
  isUnitRole: boolean
  shortUrl: string
}

@Module({ namespaced: true, dynamic: true, store, name: 'ManagementBasicDataModule' })
class BasicDataModule extends VuexModule implements BasicDataState {
  defaultProvinceTree = new RegionTree()
  provinceMap = {} as { [key: string]: RegionInfo }
  cityMap = {} as { [key: string]: Array<RegionInfo> }
  countyMap = {} as { [key: string]: Array<RegionInfo> }
  provinceTreeMap = {} as { [key: string]: RegionTree }
  userInfo: UserInfoDTO = new UserInfoDTO()
  private inited = false
  private cityMapLoadCase = new Map<string, boolean>()
  private countyMapLoadCase = new Map<string, boolean>()
  private innerCityMap = new Map<string, RegionInfo>()
  private innerCountyMap = new Map<string, RegionInfo>()
  userJobList = new Array<JobLogDTO>()
  userJobTotalSize = 0
  loginLogList = new Array<UserLoginLogResponse>()
  loginLogTotalPageSize = 0
  loginLogTotalSize = 0
  /**
   * 是不是机构管理员
   */
  isUnitRole = false
  /**
   * 短地址
   */
  shortUrl = ''

  //region action

  /**
   * @description:  请求user信息
   * @param relaod 是否重新加载 不走缓存
   * @return {*}
   */

  @Action
  @UnAuthorize
  async queryUserInfo(reload = false) {
    if (this.userInfo?.userId && !reload) {
      return Promise.resolve(new ResponseStatus(200))
    }
    const result = await platformUser.getCurrentUserInfo()
    this.DO_SET_USER_INFO(result.data)
    return result.status
  }

  /**
   * 初始化了项目的地区树，这个方法主要是用来初始化基础数据相关的数据
   */
  @Action
  async init() {
    if (!this.inited) {
      const provinceId = ConfigCenterModule.getApplicationByName('application.provinceId')
      if (provinceId) {
        const response = await platformBasicDataGateway.getRegionTree(provinceId)
        if (response.data) {
          const regionTree = RegionTree.from(response.data)
          this.setDefaultProvince(regionTree)
          this.setCitys({ provinceId: provinceId, citys: regionTree.children })
          if (regionTree.children && Array.isArray(regionTree.children)) {
            regionTree.children.forEach(city => {
              this.setCounties({
                cityId: city.id,
                counties: city.children
              })
            })
          }
        }
      }
      this.setInited(true)
    }
  }

  @Action
  async getRegionByIds(ids: Array<string>) {
    const response = await platformBasicDataGateway.getRegionsByIds(ids)
    return response
  }

  /**
   * 获取所有省份信息
   */
  @Action
  async getProvinces(): Promise<ResponseStatus> {
    const response = await platformBasicDataGateway.getSubRegionListByParentId('0')
    if (response.data) {
      const provinces = response.data.map(dto => {
        const info = new RegionInfo()
        Object.assign(info, dto)
        return info
      })
      this.setProvinces(provinces)
    }
    return response.status
  }

  /**
   * 初始化指定省份的市级列表
   */
  @Action
  async getCitysByProvinceId(provinceId: string): Promise<ResponseStatus> {
    const loaded = this.cityMapLoadCase.get(provinceId)
    if (!loaded) {
      const response = await platformBasicDataGateway.getSubRegionListByParentId(provinceId)
      if (response.status.isSuccess()) {
        const citys = response.data.map(dto => {
          const info = new RegionInfo()
          Object.assign(info, dto)
          return info
        })
        this.setCitys({ provinceId, citys })
      }
      return response.status
    }
    return new ResponseStatus(200)
  }

  /**
   * 获取异步任务处理处理结果
   * @param params
   */
  @Action
  async pageUserJob(params: { page: Page; param: UserJobParamDTO }) {
    const response = await platformBasicDataGateway.pageUserJob(params)
    if (!response.status.isSuccess()) {
      return response.status
    }
    const data = response.data
    this.SET_USER_JOB_TOTAL_SIZE(data.totalSize)
    this.SET_USER_JOB_LIST(data.currentPageData)
    return response.status
  }

  @Action
  async getCountiesByCityId(cityId: string): Promise<ResponseStatus> {
    const loaded = this.countyMapLoadCase.get(cityId)
    if (!loaded) {
      const response = await platformBasicDataGateway.getSubRegionListByParentId(cityId)
      if (response.status.isSuccess()) {
        const counties = response.data.map(dto => {
          const info = new RegionInfo()
          Object.assign(info, dto)
          return info
        })
        this.setCounties({ cityId, counties })
      }
      return response.status
    }
    return new ResponseStatus(200)
  }

  /**
   * 管理员个人密码修改
   * @param params
   */
  @Action
  async changePassword(params: { oldPassword: string; newPassword: string }) {
    const res = await msAccountGateway.changePasswordByCurrent({
      originalPassword: params.oldPassword,
      newPassword: params.newPassword
    })
    return res.status
  }

  /**
   * 用户登录日志
   * @param param
   */
  @Action
  async pageUserLoginLog(param: { page: Page; request: UserLoginLogRequest }) {
    const response = await platformStatisticReportQuery.pageUserLoginLog(param)
    if (response.status.isSuccess()) {
      this.SET_LOGIN_LOG_LIST(response.data.currentPageData)
      this.SET_USER_LOGIN_LOG_PAGE({ totalPageSize: response.data.totalPageSize, totalSize: response.data.totalSize })
    }
    return response.status
  }

  //endregion

  //region mutation
  @Mutation
  SET_LOGIN_LOG_LIST(list: Array<UserLoginLogResponse>) {
    this.loginLogList = list
  }

  @Mutation
  SET_USER_LOGIN_LOG_PAGE(page: { totalPageSize: number; totalSize: number }) {
    this.loginLogTotalPageSize = page.totalPageSize
    this.loginLogTotalSize = page.totalSize
  }

  @Mutation
  DO_SET_USER_INFO(userInfo: UserInfoDTO = new UserInfoDTO()) {
    this.userInfo = userInfo
    // 账户微服务化后不再提供通过unitType识别角色类型
    // const roleList = userInfo.roleList
    // if (roleList) {
    //   const isUnitRoleIndex = roleList.findIndex(role => {
    //     return role.unitType === UnitTypeEnum.UNIT_TYPE_TEACH
    //   })
    //   this.isUnitRole = isUnitRoleIndex > -1
    // }
  }

  @Mutation
  private setInited(inited: boolean) {
    this.inited = inited
  }

  @Mutation
  private setDefaultProvince(province: RegionTree) {
    this.defaultProvinceTree = province
  }

  @Mutation
  private setCitys(payload: { provinceId: string; citys: Array<RegionInfo> }) {
    Vue.set(this.cityMap, payload.provinceId, payload.citys)
    this.cityMapLoadCase.set(payload.provinceId, true)
    payload.citys.forEach(city => this.innerCityMap.set(city.id, city))
  }

  @Mutation
  private setProvinces(provinces: Array<RegionInfo>) {
    provinces.forEach(province => Vue.set(this.provinceMap, province.id, province))
  }

  @Mutation
  private setCounties(payload: { cityId: string; counties: Array<RegionInfo> }) {
    Vue.set(this.countyMap, payload.cityId, payload.counties)
    this.countyMapLoadCase.set(payload.cityId, true)
    if (Array.isArray(payload.counties)) {
      payload.counties.forEach(county => this.innerCountyMap.set(county.id, county))
    }
  }

  /**
   * 变更任务数据
   * @param list
   * @constructor
   */
  @Mutation
  private SET_USER_JOB_LIST(list: Array<JobLogDTO>) {
    this.userJobList = list
  }

  /**
   * 变更任务总记录数
   * @param totalSize
   * @constructor
   */
  @Mutation
  private SET_USER_JOB_TOTAL_SIZE(totalSize: number) {
    this.userJobTotalSize = totalSize
  }

  //endregion

  //region get
  get getProvinceList() {
    return (available?: boolean): Array<RegionInfo> => {
      const array = new Array<RegionInfo>()
      Object.keys(this.provinceMap).forEach(key => {
        const province = this.provinceMap[key]
        if (available) {
          if (province.available) {
            array.push(province)
          }
        } else {
          array.push(province)
        }
      })
      return array
    }
  }

  get getCitiesByProvinceId() {
    return (provinceId: string, available?: boolean): Array<RegionInfo> => {
      const citys = this.cityMap[provinceId]
      if (citys) {
        if (available) {
          return citys.filter(city => city.available)
        } else {
          return citys
        }
      } else {
        return new Array<RegionInfo>()
      }
    }
  }

  /**
   * 获取当前项目省份下的所有城市
   */
  get getCurrentCities(): Array<RegionInfo> {
    const provinceId = this.getCurrentProvinceId
    if (provinceId) {
      const citys = this.cityMap[provinceId]
      if (citys) {
        return citys
      } else {
        return new Array<RegionInfo>()
      }
    }
    return new Array<RegionInfo>()
  }

  /**
   * 获取当前项目的省份
   */
  get getCurrentProvinceId(): string {
    const provinceId = ConfigCenterModule.getApplicationByName('application.provinceId')
    return provinceId
  }

  get getCountyListByCityId() {
    return (cityId: string, available?: boolean): Array<RegionInfo> => {
      const counties = this.countyMap[cityId]
      if (counties) {
        if (available) {
          return counties.filter(county => county.available)
        } else {
          return counties
        }
      } else {
        return new Array<RegionInfo>()
      }
    }
  }

  get getExistsCityById() {
    return (cityId: string): RegionInfo | undefined => {
      return this.innerCityMap.get(cityId)
    }
  }

  get getExistsCountyById() {
    return (countyId: string): RegionInfo | undefined => {
      return this.innerCountyMap.get(countyId)
    }
  }

  // get getENameByRegionPath() {
  //   return (path: string, splitByte?: string): string | undefined => {
  //     let spiltItem = '-'
  //     if (splitByte) {
  //       spiltItem = splitByte
  //     }
  //     const paths = path.split('/')
  //     if (paths.length == 2) {
  //       return this.defaultProvinceTree.name
  //     }
  //     if (paths.length > 2 && this.defaultProvinceTree.children) {
  //       const city = this.defaultProvinceTree.children.find(city => city.id === paths[2]) || new RegionTree()
  //       if (paths.length === 3) {
  //         return this.defaultProvinceTree.name + spiltItem + (city ? city.name : '')
  //       }
  //       if (paths.length === 4 && city.children) {
  //         const country = city.children.find(country => country.id === paths[3])
  //         return (
  //           this.defaultProvinceTree.name +
  //           (spiltItem + (city ? city.name : '') + spiltItem + (country ? country.name : ''))
  //         )
  //       }
  //     }
  //     return ''
  //   }
  // }

  get getPathRegionListByRegionCode() {
    return (regionCode: string): Array<RegionInfo> => {
      const regions = new Array<RegionInfo>()
      let currentRegionCode = regionCode
      if (this.innerCountyMap.has(currentRegionCode)) {
        const region = this.innerCountyMap.get(currentRegionCode)
        if (region) {
          regions.unshift(region)
          currentRegionCode = region.parentId
        }
      }
      if (this.innerCityMap.has(currentRegionCode)) {
        const region = this.innerCityMap.get(currentRegionCode)
        if (region) {
          regions.unshift(region)
          currentRegionCode = region.parentId
        }
      }
      if (this.defaultProvinceTree.id === currentRegionCode) {
        const region = this.defaultProvinceTree.toRegionInfo()
        regions.unshift(region)
      }
      return regions
    }
  }

  /**
   * 根据传入Url获取对应短地址
   * @param url
   */
  @Action
  async getShortUrl(url: string) {
    const response = await platformBasicDataGateway.getShortUrl(url)
    if (response.status.isSuccess()) {
      this.SET_SHORT_URL(response.data)
    }
    return response.status
  }

  @Mutation
  SET_SHORT_URL(shortUrl: string) {
    this.shortUrl = shortUrl
  }

  //endregion
}

export default getModule(BasicDataModule)
