<template>
  <!--提示弹窗-->
  <el-dialog :visible.sync="visibleSync" width="400px" class="m-dialog">
    <div class="dialog-alert is-big">
      <i class="icon el-icon-success success"></i>
      <div class="txt">
        <p class="f-fb">{{ isExport ? '导出成功，是否前往下载数据？' : '导入成功，是否前往下载数据？' }}</p>
        <p class="f-f13 f-mt5">下载入口：{{ isExport ? '导出任务管理' : '导入任务管理' }}-{{ taskName }}</p>
      </div>
    </div>
    <div slot="footer">
      <el-button type="info" @click="visibleSync = false">暂 不</el-button>
      <el-button type="primary" @click="toDownloadPage">前往下载</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Vue } from 'vue-property-decorator'
  import { bind, debounce } from 'lodash-decorators'
  import FullScreen from '@hbfe/jxjy-admin-common/src/util/FullScreen'

  @Component
  export default class extends Vue {
    @PropSync('visible', {
      type: Boolean,
      default: false
    })
    visibleSync: boolean

    @Prop({
      type: Boolean,
      default: true
    })
    isExport: boolean

    @Prop({
      type: String,
      default: ''
    })
    taskName: string

    @Prop({
      type: String,
      default: ''
    })
    taskPath: string

    @Prop({
      type: String,
      default: ''
    })
    type: string

    @bind
    @debounce(200)
    toDownloadPage() {
      FullScreen.closeFull()
      this.visibleSync = false
      this.$router.push({
        path: this.taskPath,
        query: {
          type: this.type
        }
      })
    }
  }
</script>
