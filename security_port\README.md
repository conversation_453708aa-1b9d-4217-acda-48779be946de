## 安全对象插件生成状态层接口检索文件

文件名称：api-invokes.json

文件作用：保存上一次安全对象插件生成的状态层接口检索文件，下一次的检索将以这个文件为基础做检索。

## 安全对象插件生成状态层最新一次提交记录及分支信息

文件名称：apiLastCommitIdPath.json

文件作用：保存上一次生成安全对象状态层最新一次的提交记录id以及生成的所在分支，作为下一次生成安全对象的起始检索数据。

## 客户域特殊接口配置

文件名称：customer_port.json

文件作用：保存原先小程序、客户域需要手动添加的接口，单独给某个角色添加某个接口。会自动将本文件内的接口合并至小程序的安全对象交付物中。

## 安全对象默认权限配置

文件名称：security.json

文件作用：文件内的接口会自动赋予所有角色，存放两种类型的接口：

1. UI无调用但后端需要走postman的接口。
2. 本项目中没有地方调用，但项目中调用了另一个工程中的接口，此类接口也要保存在这。

## 安全对象插件生成UI页面及接口信息

文件名称：ui-assigns.json

文件作用：保存上一次检索生成完的菜单树，包含完整的接口信息。

## 安全对象插件生成UI页面方法调用信息

文件名称：ui-invokes.json

文件作用：保存上一次检索UI页面中的方法调用信息。

## 安全对象生成UI最新一次提交记录

文件名称：lastCommitIdPath

文件作用：保存上一次生成安全对象UI最新的一次提交记录（暂时冗余，后续优化使用）。
