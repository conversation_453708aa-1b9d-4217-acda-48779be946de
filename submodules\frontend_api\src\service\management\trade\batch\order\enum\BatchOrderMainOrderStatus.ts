import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 【集体报名订单】主单状态枚举
 */
export enum BatchOrderMainOrderStatusEnum {
  // 等待付款
  Wait_Pay = 1,
  // 支付中
  Paying,
  // 开通中
  Opening,
  // 交易成功
  Complete_Trade,
  // 交易关闭
  Close_Trade
}

/**
 * @description 【集体报名订单】主单状态
 */
class BatchOrderMainOrderStatus extends AbstractEnum<BatchOrderMainOrderStatusEnum> {
  static enum = BatchOrderMainOrderStatusEnum
  constructor(status?: BatchOrderMainOrderStatusEnum) {
    super()
    this.current = status
    this.map.set(BatchOrderMainOrderStatusEnum.Wait_Pay, '等待付款')
    this.map.set(BatchOrderMainOrderStatusEnum.Paying, '支付中')
    this.map.set(BatchOrderMainOrderStatusEnum.Opening, '开通中')
    this.map.set(BatchOrderMainOrderStatusEnum.Complete_Trade, '交易成功')
    this.map.set(BatchOrderMainOrderStatusEnum.Close_Trade, '交易关闭')
  }
}

export default new BatchOrderMainOrderStatus()
