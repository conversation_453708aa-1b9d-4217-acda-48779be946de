<template>
  <div>
    <el-card shadow="never" class="m-card f-mb15 is-bg">
      <div class="m-tit is-border-bottom f-justify-between">
        <el-button type="primary" @click="showLearningDialog">选择培训方案</el-button>
      </div>
      <div class="f-p15">
        <!--条件查询-->
        <hb-search-wrapper @reset="resetQueryParam" class="m-query is-border-bottom">
          <el-form-item label="年度">
            <biz-year-select v-model="localSkuProperty.year" placeholder="请选择培训年度"></biz-year-select>
          </el-form-item>
          <el-form-item label="地区">
            <biz-national-region
              v-model="localSkuProperty.region"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-national-region>
          </el-form-item>
          <el-form-item label="行业">
            <biz-industry-select
              v-model="localSkuProperty.industry"
              @clearIndustrySelect="handleClearIndustrySelect"
              @industryPropertyId="handleIndustryPropertyId"
              @industryInfos="handleIndustryInfos"
              ref="industrySelect"
            ></biz-industry-select>
          </el-form-item>
          <el-form-item label="是否展示在专题门户">
            <el-select v-model="isShow" clearable placeholder="请选择" @change="handleSelectChange">
              <el-option label="展示" :value="true"></el-option>
              <el-option label="不展示" :value="false"></el-option>
            </el-select>
          </el-form-item>
          <template v-if="localSkuProperty.industry && localSkuProperty.industry === envConfig.teacherIndustryId">
            <el-form-item label="学段">
              <biz-study-period
                v-model="localSkuProperty.studyPeriodId"
                :industry-id="localSkuProperty.industry"
                :industry-property-id="industryPropertyId"
                @updateStudyPeriod="updateStudyPeriod"
                @clearSubject="clearSubject"
              ></biz-study-period>
            </el-form-item>
            <el-form-item label="学科">
              <biz-subject
                v-model="localSkuProperty.subjectId"
                :industry-property-id="industryPropertyId"
                :studyPeriodId="localSkuProperty.studyPeriodId"
                @updateSubject="updateSubject"
              ></biz-subject>
            </el-form-item>
          </template>
          <el-form-item
            label="技术等级"
            v-if="
              skuVisible.jobLevel && localSkuProperty.industry && localSkuProperty.industry === envConfig.workServiceId
            "
          >
            <biz-technical-grade-select
              v-model="localSkuProperty.jobLevel"
              :industry-id="localSkuProperty.industry"
              :industry-property-id="industryPropertyId"
            ></biz-technical-grade-select>
          </el-form-item>
          <el-form-item label="科目类型" v-if="skuVisible.subjectType && localSkuProperty.industry">
            <biz-accounttype-select
              v-model="localSkuProperty.subjectType"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            >
            </biz-accounttype-select>
          </el-form-item>
          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-major-cascader
              v-model="localSkuProperty.societyTrainingMajor"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              (localSkuProperty.industry === envConfig.constructionIndustryId ||
                localSkuProperty.industry === envConfig.occupationalHealthId)
            "
          >
            <biz-training-category-select
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              @updateTrainingCategory="handleUpdateTrainingCategory"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-major-select
              v-model="localSkuProperty.constructionTrainingMajor"
              :industry-property-id="industryPropertyId"
              :training-category-id="trainingCategoryId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训对象"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              localSkuProperty.industry === envConfig.occupationalHealthId
            "
          >
            <biz-training-object-select
              v-model="localSkuProperty.trainingObject"
              placeholder="请选择培训对象"
              :industry-property-id="industryPropertyId"
              :industry-id="localSkuProperty.industry"
              @updateTrainingCategory="updateTrainingCategory"
            />
          </el-form-item>
          <el-form-item
            label="岗位类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              localSkuProperty.industry === envConfig.occupationalHealthId
            "
          >
            <biz-obj-category-select
              v-model="localSkuProperty.positionCategory"
              placeholder="请选择岗位类别"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              :training-object-id="localSkuProperty.trainingObject"
            />
          </el-form-item>

          <el-form-item
            label="执业类别"
            v-if="
              skuVisible.occupationalCategory &&
              localSkuProperty.industry &&
              localSkuProperty.industry === envConfig.yshyId
            "
          >
            <biz-practicing-category-cascader
              v-model="localSkuProperty.pharmacistIndustry"
              :industryId="localSkuProperty.industry"
            ></biz-practicing-category-cascader>
          </el-form-item>
          <el-form-item label="培训形式">
            <biz-training-mode-select v-model="localSkuProperty.trainingMode"></biz-training-mode-select>
          </el-form-item>
          <el-form-item label="培训方案类型">
            <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
          </el-form-item>
          <el-form-item label="培训方案名称">
            <el-input clearable placeholder="请输入培训方案名称" v-model="schemeName" />
          </el-form-item>
          <el-form-item label="销售状态">
            <el-select
              clearable
              @clear="onShelveStatus = undefined"
              placeholder="请选择销售状态"
              v-model="onShelveStatus"
            >
              <el-option
                v-for="item in onShelveStatusOptions"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="searchBase">查询</el-button>
          </template>
        </hb-search-wrapper>
        <el-alert type="warning" :closable="false" class="m-alert f-mb15">
          当前已选中
          <span class="f-fb">{{ count }}</span> 个培训方案
        </el-alert>
        <!--表格-->
        <el-table
          stripe
          :data="concatList"
          class="m-table"
          ref="trainClassCommodityListRef"
          v-loading="loading"
          max-height="800px"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <!-- <el-table-column label="排序" min-width="80" align="center" fixed="left">
            <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
          </el-table-column> -->
          <el-table-column label="培训方案名称" min-width="300" fixed="left">
            <template slot-scope="scope">
              <p>
                <biz-show-scheme-type
                  :scheme-type="scope.row.schemeType"
                  :training-mode="scope.row.skuValueNameProperty.trainingMode.skuPropertyValueId"
                ></biz-show-scheme-type>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="scope.row.commodityBasicData.saleTitle"
                  placement="top"
                >
                  <span>{{ scope.row.commodityBasicData.saleTitle }}</span>
                </el-tooltip>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="报名学时" min-width="110" align="center">
            <template slot-scope="scope"
              ><span :data-index="scope.$index + 1" v-observe-visibility="visibilityConfig"></span
              >{{ scope.row.period }}
            </template>
          </el-table-column>
          <el-table-column label="价格" min-width="100" align="right">
            <template slot-scope="scope">{{ scope.row.commodityBasicData.price }}</template>
          </el-table-column>
          <el-table-column label="培训属性" min-width="240">
            <template slot-scope="scope">
              <p v-if="getSkuPropertyName(scope.row, 'industry')">
                行业：{{ getSkuPropertyName(scope.row, 'industry') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'region')">地区：{{ getSkuPropertyName(scope.row, 'region') }}</p>
              <p v-if="getSkuPropertyName(scope.row, 'jobLevel')">
                技术等级：{{ getSkuPropertyName(scope.row, 'jobLevel') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
                科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
              </p>
              <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
                培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
                培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'trainingObject')">
                培训对象：{{ getSkuPropertyName(scope.row, 'trainingObject') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'positionCategory')">
                岗位类别：{{ getSkuPropertyName(scope.row, 'positionCategory') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'year')">培训年度：{{ getSkuPropertyName(scope.row, 'year') }}</p>
              <p v-if="getSkuPropertyName(scope.row, 'learningPhase')">
                学段：{{ getSkuPropertyName(scope.row, 'learningPhase') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'discipline')">
                学科：{{ getSkuPropertyName(scope.row, 'discipline') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'practitionerCategory')">
                执业类别：{{ getSkuPropertyName(scope.row, 'certificatesType') }}-{{
                  getSkuPropertyName(scope.row, 'practitionerCategory')
                }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="销售状态" min-width="140">
            <template slot-scope="scope">
              <div v-if="scope.row.onShelve.shelveStatus === 0">
                <el-badge is-dot type="info" class="badge-status">已下架</el-badge>
              </div>
              <div v-if="scope.row.onShelve.shelveStatus === 1">
                <el-badge is-dot type="success" class="badge-status">已上架</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学习起止时间" min-width="220">
            <template slot-scope="scope">
              <p v-if="!isValid(scope.row)">起始：{{ scope.row.trainingBeginDate }}</p>
              <p v-if="!isValid(scope.row)">结束：{{ scope.row.trainingEndDate }}</p>
              <p v-if="isValid(scope.row)">长期有效</p>
            </template>
          </el-table-column>
          <el-table-column label="报名起止时间" min-width="220">
            <template slot-scope="scope">
              <p>起始：{{ scope.row.registerBeginDate }}</p>
              <p>结束：{{ scope.row.registerEndDate }}</p>
            </template>
          </el-table-column>
          <el-table-column label="最新修改时间" sortable min-width="180">
            <template slot-scope="scope">{{ scope.row.commodityLastEditTime }}</template>
          </el-table-column>
          <el-table-column label="是否展示在专题门户" min-width="180">
            <template slot-scope="scope">{{ scope.row.isShowSpecialTopic ? '展示' : '不展示' }}</template>
          </el-table-column>
          <el-table-column label="展示用户" min-width="180">
            <template slot-scope="scope">
              <div v-if="scope.row.specialTopicShowRange.length">
                <p v-for="(item, index) in scope.row.specialTopicShowRange" :key="index">{{ item }}</p>
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="editConfig(scope.row)">修改销售配置</el-button>
              <el-button type="text" size="mini" @click="cancelChoose(scope.row, scope.$index)">取消选择</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <learning-scheme
        ref="LearningScheme"
        v-model="commoditySkuIdList"
        @getSchemeValue="getSchemeValue"
        :deleteScheme="deleteScheme"
        :topicID="topicID"
        :count="count"
      ></learning-scheme>
      <edit-config-drawer ref="EditConfigDraweRef" @comfirm="comfirm"></edit-config-drawer>
      <div class="m-btn-bar f-tc is-sticky f-pt15" style="z-index: 8">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="commitDraft" :loading="btLoading">保存</el-button>
      </div>
    </el-card>
  </div>
</template>
<script lang="ts">
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import BizAccounttypeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-accounttype-select.vue'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import BizMajorCascader from '@hbfe/jxjy-admin-components/src/biz/biz-major-cascader.vue'
  import BizMajorSelect from '@hbfe/jxjy-admin-components/src/biz/biz-major-select.vue'
  import BizNationalRegion from '@hbfe/jxjy-admin-components/src/biz/biz-national-region.vue'
  import BizTechnicalGradeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-technical-grade-select.vue'
  import BizTrainingCategorySelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-category-select.vue'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-scheme/src/models/SchemeSkuProperty'
  import LearningScheme from '@hbfe/jxjy-admin-specialTopics/src/add/components/learning-scheme.vue'
  import EditConfigDrawer from '@hbfe/jxjy-admin-specialTopics/src/manage/components/edit-config-drawer.vue'
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'

  import {
    CommoditySkuRequest,
    OnShelveRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest,
    SkuPropertyRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { ShowUserRangeEnum } from '@api/service/management/train-class/query/enum/ShowUserRangeEnum'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import { Page } from '@hbfe/common'
  import UITrainClassCommodityDetail from '@hbfe/jxjy-admin-scheme/src/models/UITrainClassCommodityDetail'

  import { RegionSkuPropertyRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { DeleteScheme, SchemeList } from '@api/platform-gateway/platform-training-channel-v1'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import BizShowSchemeType from '@hbfe/jxjy-admin-components/src/biz/biz-show-scheme-type.vue'
  import BizTrainingModeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-mode-select.vue'
  import { cloneDeep } from 'lodash'

  @Component({
    components: {
      BizTrainingModeSelect,
      BizShowSchemeType,
      BizMajorCascader,
      BizTrainingCategorySelect,
      BizYearSelect,
      BizMajorSelect,
      BizTechnicalGradeSelect,
      BizIndustrySelect,
      BizAccounttypeSelect,
      BizNationalRegion,
      LearningScheme,
      EditConfigDrawer
    }
  })
  export default class extends Vue {
    @Ref('LearningScheme') LearningScheme: any
    @Ref('EditConfigDraweRef') EditConfigDraweRef: EditConfigDrawer
    page = new Page()
    @Prop({
      type: String,
      default: ''
    })
    topicID: string //专题id
    @Prop({
      type: Array,
      default: () => new Array<TrainClassCommodityVo>()
    })
    trainClassList: TrainClassCommodityVo[]
    loading = false
    btLoading = false
    selectedTrainingPlanID: { schemeId: string; sort: number }[] = [] //已选中的培训方案
    commoditySkuIdList = new Array<HasSelectSchemeMode>() // 商品id数组
    addScheme: Array<SchemeList> = new Array<SchemeList>() //新增的培训方案列表
    updateScheme: Array<SchemeList> = new Array<SchemeList>() //更新的培训方案列表
    deleteScheme: Array<DeleteScheme> = new Array<DeleteScheme>() //删除的培训方案列表
    // 编辑销售配置
    trainClassCommodityVo = new TrainClassCommodityVo()
    queryTrainClassCommodityList = new QueryTrainClassCommodityList()
    concatList: TrainClassCommodityVo[] = [] //本地与后端拼接后的培训班商品列表
    trainClassCommodityList: TrainClassCommodityVo[] = [] //本地培训班商品列表
    oldTrainClassCommodityList: TrainClassCommodityVo[] = [] //接口培训班商品列表
    filterCommodity = new CommoditySkuRequest() //培训班商品请求参数
    oldFilterCommodity = new CommoditySkuRequest() //培训班商品请求参数

    onShelveStatus: number = null //报名状态
    schemeName = '' // 培训方案名称
    schemeTypeInfo: Array<string> = new Array<string>() //培训方案类型
    industryPropertyId = '' //行业属性分类Id
    trainingCategoryId = '' //类别id
    count = 0 //选中方案总数
    // 是否展示在门户筛选
    isShow: boolean | null = null
    localSkuProperty: SchemeSkuProperty = {
      /**
       * 年度
       */
      year: '',
      /**
       * 地区
       */
      region: [] as string[],
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训类别
       */
      trainingCategory: '',
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 技术等级 - 工勤行业
       **/
      jobLevel: '',
      /**
       * 培训对象
       */
      trainingObject: '',
      /**
       * 岗位类别
       */
      positionCategory: '',
      /**
       * 学段id
       */
      studyPeriodId: '',
      /**
       * 学科id
       */
      subjectId: '',
      /**
       * 药师id
       */
      pharmacistIndustry: [] as string[]
    } as SchemeSkuProperty
    // thematicManagementItem = new ThematicManagementItem()
    onShelveStatusOptions = [
      { id: 1, label: '已上架' },
      { id: 0, label: '已下架' }
    ]
    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: true,
      // 培训类别
      trainingCategory: true,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      //   岗位类别
      positionCategory: true,
      // 执业类别
      occupationalCategory: true
    }
    /**
     * 当前网校信息
     */
    envConfig = {
      // 工勤行业
      workServiceId: '',
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 职业卫生行业Id
      occupationalHealthId: '',
      //教师行业id
      teacherIndustryId: '',
      // 药师行业id
      yshyId: ''
    }

    /**
     * 判断是否长期有效
     */
    isValid(row: TrainClassCommodityVo) {
      if (row.trainingBeginDate == '1900-01-01 00:00:00' && row.trainingEndDate == '2100-01-01 00:00:00') {
        return true
      } else {
        return false
      }
    }

    handleSelectChange(value: boolean | null | string) {
      if (value === '') {
        this.isShow = null
      }
    }

    /**
     * 获取培训方案类型
     */
    getSchemeType(row: UITrainClassCommodityDetail) {
      return SchemeType.getSchemeType(row.schemeType, true)
    }

    // 保存展示配置
    async comfirm(showType: boolean, showCustomers: Array<ShowUserRangeEnum>) {
      try {
        const res = await this.trainClassCommodityVo.updateTopicSaleChannelRange({
          topicId: this.topicID,
          isShowTopic: showType,
          range: showCustomers
        })
        if (res.code === 200) {
          this.$message.success('修改成功')
        }
        await this.searchBase()
      } catch (error) {
        console.log(error)
        this.$message.error('修改失败')
      }
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: UITrainClassCommodityDetail, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }

    //打开选择培训方案弹窗
    showLearningDialog() {
      this.LearningScheme.showDialog()
    }

    // 培训方案入参
    @Watch('commoditySkuIdList', {
      deep: true
    })
    async changeScheme(val: Array<HasSelectSchemeMode>) {
      if (val?.length > 0) {
        console.warn('123', val)

        this.selectedTrainingPlanID = []
        val.forEach((item, index) => {
          const obj = {
            schemeId: item.id,
            sort: index
          }
          this.selectedTrainingPlanID.push(obj)
        })
      } else {
        this.selectedTrainingPlanID = []
      }
    }

    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      this.initQueryParam()
      this.localSkuProperty = new SchemeSkuProperty()
      this.onShelveStatus = null
      this.schemeName = ''
      //   this.sortPolicy = new Array<CommoditySkuSortRequest>()

      // 移除表格排序
      //   this.elTableRef.clearSort()
      await this.searchBase()
    }

    updateTrainingCategory(val: string) {
      if (val) {
        this.localSkuProperty.positionCategory = ''
      }
    }

    // 修改销售配置
    editConfig(item: TrainClassCommodityVo) {
      this.EditConfigDraweRef.showConfigDialog = true
      this.EditConfigDraweRef.showType = item.isShowSpecialTopic
      this.trainClassCommodityVo.schemeId = item.schemeId
    }

    clearSubject() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      // this.trainSchemeQueryParam.skuPropertyRequest.discipline = new Array<string>()
      // this.trainSchemeQueryParam.skuPropertyRequest.learningPhase = new Array<string>()
    }

    //选择学段
    updateStudyPeriod(val: string) {
      this.localSkuProperty.studyPeriodId = val
      this.localSkuProperty.subjectId = ''
      // this.trainSchemeQueryParam.skuPropertyRequest.discipline = []
    }

    //选择学科
    updateSubject(val: string) {
      this.localSkuProperty.subjectId = val
    }

    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人设行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }

    handleIndustryInfos(values: any) {
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.occupationalHealthId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      this.envConfig.yshyId = values.medicineIndustryId || ''
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.jobLevel = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.studyPeriodId = ''
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.pharmacistIndustry = [] as string[]
    }

    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const positionCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.POSITION_CATEGORY)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.positionCategory = positionCategory > -1
    }

    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.filterCommodity = new CommoditySkuRequest()
      this.filterCommodity.onShelveRequest = new OnShelveRequest()
      this.filterCommodity.schemeRequest = new SchemeRequest()
      this.filterCommodity.schemeRequest.schemeName = ''
      this.filterCommodity.skuPropertyRequest = new SkuPropertyRequest()
      this.filterCommodity.skuPropertyRequest.year = new Array<string>()
      this.filterCommodity.skuPropertyRequest.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.filterCommodity.skuPropertyRequest.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      this.filterCommodity.skuPropertyRequest.industry = new Array<string>()
      this.filterCommodity.skuPropertyRequest.subjectType = new Array<string>()
      this.filterCommodity.skuPropertyRequest.trainingCategory = new Array<string>()
      this.filterCommodity.skuPropertyRequest.trainingProfessional = new Array<string>()
      this.filterCommodity.skuPropertyRequest.trainingObject = new Array<string>()
      this.filterCommodity.skuPropertyRequest.positionCategory = new Array<string>()
      this.filterCommodity.skuPropertyRequest.jobLevel = new Array<string>()
      this.filterCommodity.skuPropertyRequest.learningPhase = new Array<string>()
      this.filterCommodity.skuPropertyRequest.discipline = new Array<string>()
      this.filterCommodity.skuPropertyRequest.practitionerCategory = new Array<string>()
      this.filterCommodity.skuPropertyRequest.trainingForm = new Array<string>()
      this.schemeTypeInfo = new Array<string>()
    }

    /**
     * 获取本地sku选项
     */
    getLocalSkuProperty() {
      const skuProperties = cloneDeep(this.filterCommodity.skuPropertyRequest)
      skuProperties.year = !this.localSkuProperty.year ? ([] as string[]) : [this.localSkuProperty.year]
      skuProperties.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      const localRegion = cloneDeep(this.localSkuProperty.region)
      if (Array.isArray(localRegion) && localRegion.length) {
        const option = new RegionSkuPropertyRequest()
        option.province = localRegion.length >= 1 ? localRegion[0] : undefined
        option.city = localRegion.length >= 2 ? localRegion[1] : undefined
        option.county = localRegion.length >= 3 ? localRegion[2] : undefined
        skuProperties.regionSkuPropertySearch.region.push(option)
        skuProperties.regionSkuPropertySearch.regionSearchType = 1
      } else {
        skuProperties.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      }
      skuProperties.industry = !this.localSkuProperty.industry ? ([] as string[]) : [this.localSkuProperty.industry]
      skuProperties.jobLevel = !this.localSkuProperty.jobLevel ? ([] as string[]) : [this.localSkuProperty.jobLevel]
      skuProperties.subjectType = !this.localSkuProperty.subjectType
        ? ([] as string[])
        : [this.localSkuProperty.subjectType]
      skuProperties.trainingCategory = !this.localSkuProperty.trainingCategory
        ? ([] as string[])
        : [this.localSkuProperty.trainingCategory]
      skuProperties.trainingProfessional = this.getTrainingProfessional()
      skuProperties.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      skuProperties.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      skuProperties.learningPhase = !this.localSkuProperty.studyPeriodId
        ? ([] as string[])
        : [this.localSkuProperty.studyPeriodId]
      skuProperties.discipline = !this.localSkuProperty.subjectId ? ([] as string[]) : [this.localSkuProperty.subjectId]
      skuProperties.certificatesType = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[0]]
        : []
      skuProperties.practitionerCategory = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[1]]
        : []
      skuProperties.trainingForm = !this.localSkuProperty.trainingMode
        ? ([] as string[])
        : [this.localSkuProperty.trainingMode]

      this.filterCommodity.skuPropertyRequest = cloneDeep(skuProperties)
    }

    /**
     * 处理列表查询参数
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.filterCommodity.onShelveRequest.onShelveStatus =
        this.onShelveStatus || this.onShelveStatus === 0 ? this.onShelveStatus : undefined
      this.filterCommodity.schemeRequest.schemeName = this.schemeName || undefined
      this.configureTrainSchemeQueryParam()
    }

    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.filterCommodity.schemeRequest.schemeType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      // if (schemeType === 'chooseCourseLearning' || schemeType === 'autonomousCourseLearning') {
      this.filterCommodity.schemeRequest.schemeType = schemeType
      this.filterCommodity.isShowTrainingChannel = this.isShow
      // } else {
      //   this.filterCommodity.schemeRequest.schemeType = undefined
      // }
    }

    //分页查询已选中的培训方案信息
    async allScheme() {
      this.loading = true
      if (this.commoditySkuIdList.length > 0) {
        this.concatList = this.oldTrainClassCommodityList.filter(
          (item) => !this.deleteScheme?.some((it) => it.schemeId == item.schemeId)
        )
        this.$emit('update:trainClassList', this.concatList)
        this.filterCommodity.commoditySkuIdList = []
        const page = new Page(1, this.commoditySkuIdList.length < 200 ? this.commoditySkuIdList.length : 200)
        this.trainClassCommodityList = []
        const commoditySkuIdList = this.commoditySkuIdList.map((item) => item.schemeId)
        this.filterCommodity.trainingChannelIds = [this.topicID]
        this.filterCommodity.commoditySkuIdList = commoditySkuIdList
        const list = await this.queryTrainClassCommodityList.queryTrainClassCommodityList(page, this.filterCommodity)
        //查询培训方案列表
        this.trainClassCommodityList.push(...list)
        if (page.pageSize > 2) {
          await Promise.all(
            Array.from({ length: page.totalPageSize - 1 }, (v, k) => k + 2).map(async (item) => {
              this.trainClassCommodityList.push(
                ...(await this.queryTrainClassCommodityList.queryTrainClassCommodityList(
                  new Page(item, 200),
                  this.filterCommodity
                ))
              )
            })
          )
        }
        console.log('新增专题培训方案', this.filterCommodity, this.trainClassCommodityList, this.concatList)

        this.concatList.unshift(...this.trainClassCommodityList)
        this.trainClassCommodityList, this.concatList
        this.$emit('update:trainClassList', this.concatList)
        ;(this.$refs['trainClassCommodityListRef'] as any)?.doLayout()
      }
      this.loading = false
    }

    //专题id查询培训课程
    async topicScheme() {
      this.loading = true
      this.concatList = []
      this.$emit('update:trainClassList', this.concatList)
      this.oldFilterCommodity = cloneDeep(this.filterCommodity)
      this.oldFilterCommodity.commoditySkuIdList = []
      this.oldFilterCommodity.trainingChannelIds = [this.topicID]
      this.oldTrainClassCommodityList = await this.queryTrainClassCommodityList.queryTrainClassCommodityList(
        this.page,
        this.oldFilterCommodity
      )
      console.log('oldTrainClassCommodityList', this.oldTrainClassCommodityList, this.deleteScheme)
      const fileList = this.oldTrainClassCommodityList.filter(
        (item) => !this.deleteScheme?.some((it) => it.schemeId == item.schemeId)
      )
      this.concatList.push(...fileList)
      this.$emit('update:trainClassList', this.concatList)
      console.log('oldTrainClassCommodityList', fileList)

      this.loading = false
      ;(this.$refs['trainClassCommodityListRef'] as any)?.doLayout()
    }

    //查询列表
    async searchBase() {
      this.page = new Page()
      this.filterCommodity.commoditySkuIdList = this.commoditySkuIdList.map((item) => item.schemeId)
      //接口传参处理
      this.getPageQueryParams()
      await this.topicScheme()
      await this.allScheme()
    }

    //取消选择
    async cancelChoose(row: TrainClassCommodityVo, index: number) {
      //取消选择,以下同步删除
      //已选择的培训方案id数组
      //绑定弹窗的id数组
      //列表数据
      const isTopicScheme = row.trainingChannels?.some((it) => it.trainingChannelId == this.topicID)
      if (isTopicScheme) {
        this.deleteScheme.push({ schemeId: row.schemeId })
        this.concatList.splice(index, 1)
        this.$emit('update:trainClassList', this.concatList)
        ;(this.$refs['trainClassCommodityListRef'] as any)?.doLayout()
        this.count--
      } else {
        this.concatList.splice(index, 1)
        this.$emit('update:trainClassList', this.concatList)
        this.selectedTrainingPlanID.splice(index, 1)
        this.commoditySkuIdList.splice(index, 1)
        this.count--
      }
      //传回一个列表对象,先判断是哪个数组里的数据
      // 本地数组里的,直接删除
      //后端数据里的,先将要取消的此条数据保存在一个数组中
      //然后删除后端数据中的此条数据,并重新渲染列表

      // 相反,在添加时得判断添加的数据是否在已删除的数组中,
      //存在,则移除删除数组中的该条数据,并在后端数组中加上此条数据,并重新渲染列表
    }

    //取消编辑
    cancel() {
      const that = this as any
      //TODO 需要删除编辑的数据
      this.$confirm('取消后,当前调整的内容不保存,确定取消?', '提示', {
        confirmButtonText: '确认',
        showCancelButton: true
      }).then(() => {
        that.$message.success('已取消本次编辑')
        this.$router.push({
          path: '/training/special-topics/manage'
        })
      })
    }

    /**
     * 提交保存
     */
    commitDraft() {
      this.btLoading = true
      this.addScheme = this.selectedTrainingPlanID
      this.$emit('saveTrainingChannelScheme', this.addScheme, this.updateScheme, this.deleteScheme)
    }

    /**
     * 获取添加方案弹窗内吐出的值
     */
    async getSchemeValue(val: Array<DeleteScheme>, count: number) {
      this.deleteScheme = cloneDeep(val)
      this.count = count
      await this.searchBase()

      console.log('12312312', this.deleteScheme)
    }

    /**
     * 滑动加载 原理使用 data-set和数量进行比较 //瀑布式加载
     */
    async visibilityConfig(isVisible: boolean, entry: any) {
      if (isVisible) {
        if (entry.target.dataset.index >= this.page.totalSize) {
          //   最大值时不请求
          return
        }
        if (parseInt(entry.target.dataset.index) == this.concatList.length) {
          this.loading = true
          this.page.pageNo = this.page.pageNo + 1
          const index = this.concatList.length
          const list: TrainClassCommodityVo[] = await this.queryTrainClassCommodityList.queryTrainClassCommodityList(
            this.page,
            this.oldFilterCommodity
          )
          this.loading = false
          const fileList = list.filter((item) => !this.deleteScheme?.some((it) => it.schemeId == item.schemeId))
          this.concatList.push(...fileList)
          console.log('滚动加载中的', this.concatList)
          this.$emit('update:trainClassList', this.concatList)

          entry.target.dataset.index = index
          //处理切换页数后行数错位问题
          ;(this.$refs['trainClassCommodityListRef'] as any)?.doLayout()
        }
      }
    }

    async created() {
      await this.init()
    }

    async init() {
      this.initQueryParam()
      // this.$nextTick(async () => {
      //   await this.topicScheme()
      // })
      await this.topicScheme()
      this.commoditySkuIdList = []
    }
  }
</script>
