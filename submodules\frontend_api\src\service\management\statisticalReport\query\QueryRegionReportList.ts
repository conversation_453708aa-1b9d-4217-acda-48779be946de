import exportMsgateway, {
  TradeReportRequest as TradeReportReq
} from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import tradeMsGateway, { TradeReportRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { RegionOpenReportFormResponseVo } from '@api/service/management/statisticalReport/query/vo/RegionOpenReportFormResponseVo'
import { ReportSummaryResponse } from '@api/service/management/statisticalReport/query/vo/RegionReportSummaryResponse'
export class QueryRegionReportList {
  statisticsM = new ReportSummaryResponse()

  /**
   * 导出
   */
  async exportExcel(param: TradeReportReq) {
    try {
      console.log('param参数=', param)
      param.containsCollective = true
      const res = await exportMsgateway.exportRegionOpenReportFormsInServier(param)
      console.log('调用了exportExcel方法，返回值=', res)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryRegionReportList.ts所处方法，exportExcel',
        e
      )
    }
  }
  /**
   * 获取指定列表地区开通统计列表
   */

  async listRegionOpenReportFormsInServier(
    filter: TradeReportRequest,
    vendorId = '',
    isPopularize = false
  ): Promise<Array<RegionOpenReportFormResponseVo>> {
    try {
      console.log('filter参数=', filter)
      const res = await tradeMsGateway.listRegionOpenReportFormsInServier(filter)
      const tmpArr = []

      if (res.status.isSuccess()) {
        for (const tmpArrElement of res.data) {
          const reportVo = new RegionOpenReportFormResponseVo()
          Object.assign(reportVo, tmpArrElement)
          await reportVo.fillData()
          tmpArr.push(reportVo)
        }
        // await this.getCommodityReportSummaryInServicer(filter)
      }

      console.log('调用了listRegionOpenReportFormsInServier方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryRegionReportList.ts所处方法，listRegionOpenReportFormsInServier',
        e
      )
    }
  }
  /**
   * 获取报表统计合计数据
   */

  async getCommodityReportSummaryInServicer(filter: TradeReportRequest): Promise<ReportSummaryResponse> {
    try {
      console.log('filter参数=', filter)
      const res = await tradeMsGateway.getCommodityReportSummaryInServicer(filter)
      const statisticsM = new ReportSummaryResponse()
      if (res.status.isSuccess()) {
        Object.assign(statisticsM, res.data)
        await statisticsM.fillData()
      }

      statisticsM.purchaseChannelStatisticInfoList = statisticsM.purchaseChannelStatisticInfoList
        .map(item => {
          item.paymentTypeStatisticInfoList.sort(function(a: any, b: any) {
            return b.paymentType - a.paymentType
          })
          return item
        })
        .sort(function(a: any, b: any) {
          return a.purchaseChannel - b.purchaseChannel
        })
      this.statisticsM = statisticsM
      console.log('调用了getCommodityReportSummaryInServicer方法，返回值=', statisticsM)
      return statisticsM
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryRegionReportList.ts所处方法，getCommodityReportSummaryInServicer',
        e
      )
    }
  }
}
