import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 异步任务处理结果枚举
 */
export enum AsyncTaskProcessResultListEnum {
  // 1：未知
  Unknown = 1,
  // 2：成功
  Success,
  // 3：失败
  Fail
}

/**
 * @description 异步任务处理结果列表
 */
class AsyncTaskProcessResultList extends AbstractEnum<AsyncTaskProcessResultListEnum> {
  static enum = AsyncTaskProcessResultListEnum
  constructor(status?: AsyncTaskProcessResultListEnum) {
    super()
    this.current = status
    this.map.set(AsyncTaskProcessResultListEnum.Unknown, '未知')
    this.map.set(AsyncTaskProcessResultListEnum.Success, '成功')
    this.map.set(AsyncTaskProcessResultListEnum.Fail, '失败')
  }
}

export default new AsyncTaskProcessResultList()
