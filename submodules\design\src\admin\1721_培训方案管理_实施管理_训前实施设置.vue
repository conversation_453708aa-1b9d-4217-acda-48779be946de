<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">培训方案管理</el-breadcrumb-item>
      <el-breadcrumb-item>实施管理</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="m-tab-center">
      <div class="content">
        <div class="item z-cur">训前实施设置</div>
        <div class="item">培训过程管理</div>
        <div class="item">培训成果管理</div>
      </div>
    </div>
    <div class="f-p15">
      <div class="m-side-positioner">
        <div class="item">
          <div class="dot"></div>
          <div class="tit">报到配置</div>
        </div>
        <div class="item z-cur">
          <div class="dot"></div>
          <div class="tit">考勤配置</div>
        </div>
        <div class="item">
          <div class="dot"></div>
          <div class="tit">学习资料配置</div>
        </div>
      </div>
      <div style="padding-right: 160px;">
        <el-card shadow="never" class="m-card f-mb15">
          <div slot="header" class="f-flex">
            <span class="tit-txt f-flex-sub">报到设置</span>
          </div>
          <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
            若有针对指定期别有特殊的报到规则，可选择指定面授期别单独设置。设置完成后期别按指定规则执行。
          </el-alert>
          <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
            <el-table-column label="期别编号" min-width="100" fixed="left">
              <template>001</template>
            </el-table-column>
            <el-table-column label="期别名称" min-width="200">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  AAA
                </div>
                <div v-else-if="scope.$index === 1">
                  AAA<el-tag type="danger" size="mini" class="f-ml5">未开启报到</el-tag>
                </div>
                <div v-else>AAA<el-tag type="danger" size="mini" class="f-ml5">未设置</el-tag></div>
              </template>
            </el-table-column>
            <el-table-column label="培训报到时段" min-width="200">
              <template>
                <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
              </template>
            </el-table-column>
            <el-table-column label="培训时段" min-width="200">
              <template>
                <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
              </template>
            </el-table-column>
            <el-table-column label="报到方式" width="200" align="center">
              <template>听课证</template>
            </el-table-column>
            <el-table-column label="操作" width="180" align="center" fixed="right">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  <el-button type="text" disabled>设置</el-button>
                </div>
                <div v-else>
                  <el-button type="text">设置</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
          <!--通用空数据-->
          <div class="m-no-date">
            <img class="img" src="./assets/images/no-data-normal.png" alt="" />
            <div class="date-bd">
              <p class="f-f15 f-c9">培训方案已开启面授期别报到，请尽快设置报到规则~</p>
              <div class="f-mt10">
                <el-button type="primary" size="small">设置</el-button>
              </div>
            </div>
          </div>
          <!--通用空数据-->
          <div class="m-no-date">
            <img class="img" src="./assets/images/no-data-normal.png" alt="" />
            <div class="date-bd">
              <p class="f-f15 f-c9">班级未开启报到~</p>
            </div>
          </div>
        </el-card>
        <el-card shadow="never" class="m-card f-mb15">
          <div slot="header" class="f-flex">
            <span class="tit-txt f-flex-sub">考勤设置</span>
          </div>
          <div class="m-sub-tit">
            <span class="tit-txt f-flex-sub">指定面授期别报到设置</span>
            <el-button type="primary" size="mini">考勤规则模板</el-button>
          </div>
          <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
            设置期别的考勤规则，可选择指定面授期别单独设置。设置完成后期别按指定规则执行。
          </el-alert>
          <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
            <el-table-column label="期别编号" min-width="100" fixed="left">
              <template>001</template>
            </el-table-column>
            <el-table-column label="期别名称" min-width="200">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  AAA
                </div>
                <div v-else-if="scope.$index === 1">
                  AAA<el-tag type="danger" size="mini" class="f-ml5">未开启考勤</el-tag>
                </div>
                <div v-else>AAA<el-tag type="danger" size="mini" class="f-ml5">未设置</el-tag></div>
              </template>
            </el-table-column>
            <el-table-column label="培训报到时段" min-width="200">
              <template>
                <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
              </template>
            </el-table-column>
            <el-table-column label="培训时段" min-width="200">
              <template>
                <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
              </template>
            </el-table-column>
            <el-table-column label="考勤规则" width="200" align="center">
              <template>
                <p>①开启签到考勤</p>
                <p>②开启签退考勤</p>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" align="center" fixed="right">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  <el-button type="text" disabled>设置</el-button>
                </div>
                <div v-else>
                  <el-button type="text">设置</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
          <!--通用空数据-->
          <div class="m-no-date">
            <img class="img" src="./assets/images/no-data-normal.png" alt="" />
            <div class="date-bd">
              <p class="f-f15 f-c9">培训方案已开启面授期别考勤，请尽快设置考勤规则~</p>
              <div class="f-mt10">
                <el-button type="primary" size="small">设置</el-button>
              </div>
            </div>
          </div>
          <!--通用空数据-->
          <div class="m-no-date">
            <img class="img" src="./assets/images/no-data-normal.png" alt="" />
            <div class="date-bd">
              <p class="f-f15 f-c9">班级未开启考勤~</p>
            </div>
          </div>
        </el-card>
        <el-card shadow="never" class="m-card f-mb15">
          <div slot="header" class="f-flex">
            <span class="tit-txt f-flex-sub">学习资料设置</span>
            <el-button type="primary" icon="el-icon-upload" size="mini">上传资料</el-button>
          </div>
          <div class="m-sub-tit">
            <span class="tit-txt">班级内所有面授期别统一设置</span>
          </div>
          <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
            针对班级内所有面授期别统一设置学习资料。
          </el-alert>
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="150px" class="m-form">
                <el-form-item label="添加学习资料：" required>
                  <el-upload drag action="https://jsonplaceholder.typicode.com/posts/" multiple>
                    <i class="el-icon-upload"></i>
                    <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                    <div class="el-upload__tip" slot="tip">
                      附件资料格式支持：doc、docx、xls、xlsx、ppt、pptx、pdf、txt等格式
                    </div>
                  </el-upload>
                </el-form-item>
                <el-form-item label="已添加学习资料：" required>
                  <el-table stripe :data="tableData" max-height="500px" class="m-table">
                    <el-table-column label="附件名称" min-width="100">
                      <template slot-scope="scope">
                        <div v-if="scope.$index === 0">
                          <el-input placeholder="请输入" class="u-w180"></el-input>
                          <span class="el-icon-check f-cb f-csp f-f18 f-ml10"></span>
                          <span class="el-icon-close f-ci f-csp f-f18 f-ml10"></span>
                        </div>
                        <div v-else>001<span class="el-icon-edit f-cb f-csp f-ml10"></span></div>
                      </template>
                    </el-table-column>
                    <el-table-column label="格式" min-width="120">
                      <template>AAA</template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center" fixed="right">
                      <template>
                        <el-button type="text">删除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <!--分页-->
                  <el-pagination
                    background
                    class="f-mt15 f-tr"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage4"
                    :page-sizes="[100, 200, 300, 400]"
                    :page-size="100"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="400"
                  >
                  </el-pagination>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
          <div class="m-sub-tit">
            <span class="tit-txt">指定面授期别设置学习资料</span>
          </div>
          <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
            针对指定期别上传学习资料。设置完成后报名期别的学员可下载对应的学习资料。
          </el-alert>
          <div>请选择指定期别：<el-button type="primary">选择面授期别</el-button></div>
          <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
            <el-table-column label="期别编号" min-width="100" fixed="left">
              <template>001</template>
            </el-table-column>
            <el-table-column label="期别名称" min-width="200">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  AAA
                </div>
                <div v-else>AAA<el-tag type="danger" size="mini" class="f-ml5">未上传</el-tag></div>
              </template>
            </el-table-column>
            <el-table-column label="培训报到时段" min-width="200">
              <template>
                <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
              </template>
            </el-table-column>
            <el-table-column label="培训时段" min-width="200">
              <template>
                <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
              </template>
            </el-table-column>
            <el-table-column label="资料数量" width="200" align="center">
              <template>3</template>
            </el-table-column>
            <el-table-column label="操作" width="180" align="center" fixed="right">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  <el-button type="text">上传资料</el-button>
                </div>
                <div v-else>
                  <el-button type="text">上传资料</el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
          <!--通用空数据-->
          <div class="m-no-date">
            <img class="img" src="./assets/images/no-data-normal.png" alt="" />
            <div class="date-bd">
              <p class="f-f15 f-c9">暂无学习资料~</p>
            </div>
          </div>
          <div class="m-btn-bar f-tc f-mt30">
            <el-button>取消</el-button>
            <el-button type="primary">保存</el-button>
          </div>
        </el-card>
      </div>
      <!--
      <div class="m-btn-bar is-sticky-1 f-tc">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
      -->
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
