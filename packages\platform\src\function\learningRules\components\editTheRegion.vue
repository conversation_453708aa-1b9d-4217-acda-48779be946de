<template>
  <el-drawer
    title="编辑地区"
    :visible.sync="openDialog"
    size="900px"
    :append-to-body="true"
    custom-class="m-drawer m-table-auto"
  >
    <div class="drawer-bd">
      <el-alert type="warning" :closable="false" class="m-alert f-mb10">
        <div class="f-flex f-justify-between">
          <p class="f-c3">地区：{{ parentName }}</p>
          <div class="f-c3">
            已选择 <span class="f-fb f-co">{{ accont }}</span> 个
          </div>
        </div>
      </el-alert>
      <!--表格-->
      <el-table stripe :data="pagRegionTreeList" class="m-table">
        <el-table-column type="index" label="No." width="60"></el-table-column>
        <el-table-column label="省份" min-width="150">
          <template v-slot="{ row }">{{ diffRegion(row, '省份') }} </template>
        </el-table-column>
        <el-table-column label="地市" min-width="150">
          <template v-slot="{ row }"> {{ diffRegion(row, '地市') }}</template>
        </el-table-column>
        <el-table-column label="区县" min-width="240">
          <template v-slot="{ row }"> {{ diffRegion(row, '区县') }}</template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template v-slot="{ row }">
            <div v-if="row.checked">
              <el-button type="text" size="mini" @click="check(row)">取消选择</el-button>
            </div>
            <div v-else>
              <el-button type="text" size="mini" @click="check(row)">选择</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="openDialog = false">取消</el-button>
      <el-button @click="sure" type="primary">确定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import BasicInfo from '@api/service/management/learning-rule/model/BasicInfo'
  import { UiPage } from '@hbfe/common'
  import RegionTreeItem from '@api/service/management/learning-rule/model/RegionTreeItem'

  @Component
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.pageData, this.pageData)
    }
    // 接收基础信息
    @Prop({
      type: BasicInfo,
      default: () => new BasicInfo()
    })
    basicInfo: BasicInfo
    // 弹窗
    openDialog = false
    //地区树列表
    regionTreeList: Array<RegionTreeItem> = []

    // 分页地区树列表
    pagRegionTreeList: Array<RegionTreeItem> = []

    // 分页
    page: UiPage

    // 父级id
    parentCode = ''
    // 父级name
    parentName = ''
    // 区县总数
    accont = 0
    //初始化
    init(item: RegionTreeItem) {
      // 保存父级id
      this.parentCode = item.code
      // 保存父级名字
      this.parentName = item.name
      // 赋值
      this.regionTreeList = this.basicInfo.getRegionListDialog(item.code)
      this.accont = this.regionTreeList.filter(item => item.checked).length

      this.page.currentChange(1)

      this.openDialog = true
    }

    // 确认方法
    sure() {
      const regionList = this.regionTreeList.filter(item => item.checked).map(item => item.code)
      this.basicInfo.updateRegionTree(this.parentCode, regionList)
      this.$emit('queryRegionList')
      this.openDialog = false
    }

    // 点击选择
    check(row: RegionTreeItem) {
      row.checked = !row.checked
      this.accont = this.regionTreeList.filter(item => item.checked).length
    }

    // ui 分页方法
    async pageData() {
      const filterList =
        this.regionTreeList?.slice(
          this.page.pageSize * (this.page.pageNo - 1),
          this.page.pageSize * this.page.pageNo
        ) || []
      this.page.totalSize = this.regionTreeList.length
      this.page.totalPageSize = Math.ceil(this.regionTreeList.length / this.page.pageSize)
      this.pagRegionTreeList = filterList
    }

    // 差异化地区，北京、上海、天津、重庆、澳门、香港
    diffRegion(item: RegionTreeItem, region: string) {
      if (
        item.names[0] == '北京市' ||
        item.names[0] == '上海市' ||
        item.names[0] == '天津市' ||
        item.names[0] == '重庆市' ||
        item.names[0] == '澳门特别行政区' ||
        item.names[0] == '香港特别行政区'
      ) {
        if (region == '省份') {
          return '-'
        } else if (region == '地市') {
          return item.names[0] || '-'
        } else {
          return item.names[1] || '-'
        }
      } else {
        if (region == '省份') {
          return item.names[0] || '-'
        } else if (region == '地市') {
          return item.names[1] || '-'
        } else {
          return item.names[2] || '-'
        }
      }
    }
  }
</script>
