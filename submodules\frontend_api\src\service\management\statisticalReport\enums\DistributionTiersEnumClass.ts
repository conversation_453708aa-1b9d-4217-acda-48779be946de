import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum DistributionTiersEnum {
  /**
   * 一级
   */
  level_one = 1,
  /**
   * 二级
   */
  level_two
}

export default class DistributionTiersEnumClass extends AbstractEnum<DistributionTiersEnum> {
  constructor(status?: DistributionTiersEnum) {
    super()
    this.current = status
    this.map.set(DistributionTiersEnum.level_one, '一级')
    this.map.set(DistributionTiersEnum.level_two, '二级')
  }
}
