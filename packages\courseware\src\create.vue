<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/courseware')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/courseware' }">课件管理</el-breadcrumb-item>
      <el-breadcrumb-item>新建课件</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form
                ref="createCoursewareForm"
                :model="createCourseware"
                :rules="rules"
                label-width="120px"
                class="m-form"
              >
                <el-form-item label="课件分类：" prop="categoryId">
                  <biz-courseware-category
                    v-model="createCourseware.categoryId"
                    @input="categoryIdChange"
                    :check-strictly="false"
                    :filterable="true"
                    class="form-m"
                  ></biz-courseware-category>
                  <el-button type="text" class="f-ml15" @click="openCreateDialog">新建分类</el-button>
                </el-form-item>
                <el-form-item label="课件名称：" prop="name">
                  <el-input
                    clearable
                    maxlength="50"
                    v-model="createCourseware.name"
                    show-word-limit
                    placeholder="请输入课件名称"
                  />
                </el-form-item>
                <el-form-item label="课件供应商：" prop="providerId">
                  <biz-courseware-supplier
                    v-model="createCourseware.providerId"
                    class="form-l"
                  ></biz-courseware-supplier>
                </el-form-item>
                <el-form-item label="课件教师：" prop="teacherName">
                  <el-input
                    clearable
                    maxlength="50"
                    v-model="createCourseware.teacherName"
                    show-word-limit
                    placeholder="请输入课件教师"
                  />
                </el-form-item>
                <el-form-item label="教师简介：" prop="teacherDescription">
                  <el-input
                    type="textarea"
                    v-model="createCourseware.teacherDescription"
                    :rows="6"
                    maxlength="300"
                    show-word-limit
                    placeholder="请输入教师简介"
                  />
                </el-form-item>
                <el-form-item label="课件简介：" prop="description">
                  <el-input
                    type="textarea"
                    v-model="createCourseware.description"
                    :rows="6"
                    maxlength="300"
                    show-word-limit
                    placeholder="请输入课件简介"
                  />
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">关联媒体</span>
        </div>
        <media v-model="coursewareOutline" :time.sync="documentTime" ref="media"></media>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="doCancel">取消</el-button>
        <el-button type="primary" @click="doSave" :loading="loading">保存</el-button>
      </div>
    </div>
    <give-up-dialog :give-up-model="uiConfig.giveUpModel"></give-up-dialog>
    <el-drawer title="新建课件分类" :visible.sync="createCategoryDialog" size="800px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form ref="form" :model="createCoursewareCategory" label-width="auto" class="m-form f-mt20">
              <el-form-item label="所属节点：" required>
                <biz-courseware-category v-model="everyCategoryId"></biz-courseware-category>
              </el-form-item>
              <el-form-item label="分类名称：" required>
                <el-input
                  v-model="createCoursewareCategory.name"
                  clearable
                  maxlength="30"
                  show-word-limit
                  placeholder="请输入分类名称，不超过30个字"
                />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="createCategoryDialog = false">取消</el-button>
                <el-button type="primary" @click="createCategory">保存</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </el-main>
</template>

<script lang="ts">
  import { Ref, Component, Vue, Watch } from 'vue-property-decorator'
  import CreateCoursewareVo from '@api/service/management/resource/courseware/mutation/vo/CreateCoursewareVo'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import Media from '@hbfe/jxjy-admin-courseware/src/components/media.vue'
  import BizCoursewareCategory from '@hbfe/jxjy-admin-components/src/biz/biz-courseware-category.vue'
  import MutationCreateCourseware from '@api/service/management/resource/courseware/mutation/MutationCreateCourseware'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import CreateCoursewareCategory from '@api/service/management/resource/courseware-category/mutation/dto/CreateCoursewareCategoryDto'
  import { cloneDeep } from 'lodash'
  import { MediaSourceTypeEnum } from '@api/service/management/resource/courseware/enum/MediaSourceType'
  @Component({
    components: {
      BizCoursewareCategory,
      GiveUpDialog,
      Media
    }
  })
  export default class extends Vue {
    @Ref('createCoursewareForm') createCoursewareForm: any
    @Ref('media') media: any
    // 是否禁用
    enable = ''
    rules = {
      categoryId: [
        {
          required: true,
          message: '课件分类不能为空',
          trigger: 'change'
        }
      ],
      name: [
        { required: true, message: '课件名称不能为空', trigger: ['change', 'blur'] },
        {
          validator: async (rule: any, value: any, callback: any) => {
            await this.checkName(rule, value, callback)
          },
          trigger: 'blur'
        }
      ],
      //暂时取消课件供应商的校验
      providerId: [{ required: true, message: '请选择课件供应商', trigger: ['change', 'blur'] }],
      teacherName: [{ required: true, message: '请输入课件教师', trigger: ['change', 'blur'] }],
      enable: [{ required: true, message: '请选择课件状态', trigger: ['change', 'blur'] }]
    }
    createCourseware = new CreateCoursewareVo()
    //课程分类
    createCoursewareCategory = new CreateCoursewareCategory()
    mutationCreateCourseware: MutationCreateCourseware
    everyCategoryId: Array<string> = new Array<string>()
    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }
    createCategoryDialog = false

    coursewareOutline: Array<any> = new Array<any>()
    documentTime = 0
    loading = false
    // TODO: 记得删除
    categoryIds: string[] = ['-1', '2ca4ef587f874f14017f8761c92a0003', '2ca4ef587f874f14017f8761f87f0004']
    constructor() {
      super()
      this.mutationCreateCourseware = ResourceModule.coursewareFactory.createCourseware
    }
    @Watch('enable')
    enableWatch(newVal: string) {
      if (newVal) {
        this.createCourseware.enable = newVal == '正常' ? true : false
      }
    }
    @Watch('everyCategoryId')
    everyCategoryIdChnage(ids: string[]) {
      if (ids.length > 1) {
        this.createCoursewareCategory.parentId = ids.slice(-1).toString()
      } else {
        this.createCoursewareCategory.parentId = ids.toString()
      }
    }

    async checkName(rule: any, value: any, callback: any) {
      const res: any = await ResourceModule.coursewareFactory.getCheckCourseWare(value)
      if (res?.code !== '200') {
        return callback('课件名称重复，请修改')
      }
      return callback()
    }
    // 校验课件分类
    categoryIdChange(arg: Array<string>) {
      this.createCoursewareForm.validateField('categoryId', (errorMessage: Array<any>) => {
        console.log(errorMessage, arg)
        return this.createCourseware.categoryId.length ? true : false
      })
      return JSON.stringify(this.createCourseware.categoryId)
    }
    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
      this.uiConfig.giveUpModel.routerUrl = '/resource/courseware'
    }

    doSave() {
      this.loading = true
      this.createCoursewareForm.validate((valid: boolean) => {
        if (valid) {
          this.documentTime = 0
          this.validateMediaAndSave()
        } else {
          this.loading = false
        }
      })
    }
    async validateMediaAndSave() {
      this.loading = true
      try {
        if (this.media.mediaType == MediaSourceTypeEnum.huawei) {
          if (!this.coursewareOutline.length) {
            this.$message.warning('关联媒体不能为空')
            this.loading = false
            return
          } else {
            if (this.coursewareOutline[0].courseWares[0]?.percen !== 100) {
              this.$message.warning('请等待文件上传完毕')
              this.loading = false
              return
            }
          }
          // 如果是华为云需要走的校验
          const res = await this.media.validateMediae()
          console.log(res, 'res')
          if (!res.status) {
            console.log('华为云校验不通过！')
            this.loading = false
            return
          }
        } else {
          // 获取媒体外部链式调用返回的第一个课程资料对象中的标准地址、高清地址和超清地址
          this.createCourseware.standardAddress = this.media.outsideChainCall().courseWares[0].standardAddress
          this.createCourseware.highAddress = this.media.outsideChainCall().courseWares[0].highAddress
          this.createCourseware.superAddress = this.media.outsideChainCall().courseWares[0].superAddress
          // 新增外链校验
          if (this.media.mediaType == MediaSourceTypeEnum.outer && this.offChainCourse()) {
            this.$confirm('播放地址必须填写一种，请调整后提交。', '提示', {
              confirmButtonText: '我知道了',
              type: 'warning',
              showCancelButton: false
            }).then(async () => {
              console.log('点击了我知道了')
            })
            this.loading = false
            return
          }
          // 如果是外链需要走的校验 因为没有返回值 所以使用Promise传参
          const res = await this.media.validateOffMedia()
          console.log(res, 'res')
          if (res.status) {
            console.log('外链校验不通过！')
            this.loading = false
            return
          }
        }

        this.mutationCreateCourseware.createCoursewareDto = cloneDeep(this.createCourseware)
        this.mutationCreateCourseware.createCoursewareDto.categoryId = this.createCourseware.categoryId[
          this.createCourseware.categoryId.length - 1
        ]
        if (this.createCourseware.providerId[0]) {
          this.mutationCreateCourseware.createCoursewareDto.providerId = this.createCourseware.providerId[0]
        }
        if (this.documentTime) {
          //文档播放时间
          const documentTime = this.documentTime > 0 ? this.documentTime : 0
        }
        console.log(this.coursewareOutline[0], '||||', this.createCourseware)
        if (
          this.coursewareOutline[0] &&
          this.coursewareOutline[0].courseWares[0] &&
          this.coursewareOutline[0].courseWares[0].timeLength
        ) {
          this.mutationCreateCourseware.createCoursewareDto.mediaType = MediaSourceTypeEnum.huawei
          this.mutationCreateCourseware.createCoursewareDto.resourceDataDto.timeLength = this.documentTime
          this.mutationCreateCourseware.createCoursewareDto.resourceDataDto.resourcePath = this.coursewareOutline[0].courseWares[0].coursewareResourcePath
        }
        // 外链传参
        if (this.media.mediaType == MediaSourceTypeEnum.outer) {
          this.mutationCreateCourseware.createCoursewareDto.mediaType = MediaSourceTypeEnum.outer
          this.mutationCreateCourseware.createCoursewareDto.standardAddress = this.createCourseware.standardAddress
          this.mutationCreateCourseware.createCoursewareDto.highAddress = this.createCourseware.highAddress
          this.mutationCreateCourseware.createCoursewareDto.superAddress = this.createCourseware.superAddress
        }

        // 媒体源赋值
        const res = await this.mutationCreateCourseware.doCreate()
        console.log(res, '-----------------------')
        if (res.code === 200) {
          this.$message.success('保存成功')
          this.$router.push('/resource/courseware')
        } else if (res.code === 500 && res.businessCode === 1001) {
          this.loading = false
          this.$message.error('课件名称不允许重复')
        } else if (res.code === 500 && res.businessCode === 1002) {
          this.loading = false
          this.$message.error('目前还在转码中，暂不能修改')
        } else {
          this.loading = false
          this.$message.error(res.tip)
        }
      } catch (e) {
        this.loading = false
        this.$message.warning('保存失败')
        console.log(e)
      }
    }

    openCreateDialog() {
      this.createCategoryDialog = true
    }

    validateForm() {
      if (!this.createCoursewareCategory.parentId) {
        this.$message.warning('请选择课件分类所属节点')
        return false
      } else if (!this.createCoursewareCategory.name) {
        this.$message.warning('课件分类名称不可为空')
        return false
      } else {
        return true
      }
    }

    createCategory() {
      if (!this.validateForm()) {
        return false
      }
      this.createCoursewareCategory
        .save()
        .then(() => {
          this.$message.success('新建成功')
          this.createCoursewareCategory.reset()
          this.everyCategoryId = []
          this.createCategoryDialog = false
        })
        .catch(() => {
          this.$message.error('新建分类失败')
        })
    }

    // 外链课件校验
    offChainCourse() {
      if (
        this.media.outsideChainForm.standardAddress ||
        this.media.outsideChainForm.highAddress ||
        this.media.outsideChainForm.superAddress
      ) {
        return false
      } else {
        return true
      }
    }
  }
</script>
