"""独立部署的微服务,K8S服务名:ms-autolearning-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""批量中止智能学习任务"""
	batchTerminateAutoLearning(request:MainTaskIdsRequest):BatchTerminateAutoLearningResponse
	"""查询主任务对应的首次开始学习时间
		@param request:
		@return {@link QueryLastTimeExpectationStartStudyDateResponse}
		<AUTHOR> By czy
		@since 2025/4/1 15:55
	"""
	queryLastTimeExpectationStartStudyDate(request:QueryLastTimeExpectationStartStudyDateRequest):QueryLastTimeExpectationStartStudyDateResponse
	"""重新编排自动学习主任务
		@param autoLearningMainTaskId:
		<AUTHOR> By Cb
		@since 2024/5/13 11:09
	"""
	rearrangementAutoLearning(autoLearningMainTaskId:String):Void
	"""重启智能学习任务
		@param request:
		@return {@link RestartAutoLearningTaskResponse}
		<AUTHOR> By czy
		@since 2025/4/1 15:55
	"""
	restartAutoLearningTask(request:RestartAutoLearningTaskRequest):RestartAutoLearningTaskResponse
	"""更新网校智能学习服务配置"""
	updateOnlineSchoolSmartLearningServiceConfig(request:OnlineSchoolSmartLearningServiceConfigRequest):Void
	"""校验学习方案列表是否存在自动学习
		@param request:
		@return {@link Boolean}
		<AUTHOR> By Cb
		@since 2024/5/9 9:31
	"""
	validLearningSchemeExistAutoLearning(request:ValidLearningSchemeExistAutoLearningRequest):ValidLearningSchemeExistAutoLearningResponse
}
input IntegerRange @type(value:"com.fjhb.domain.learningscheme.support.IntegerRange") {
	min:Int!
	max:Int!
}
input TimeRange @type(value:"com.fjhb.domain.learningscheme.support.TimeRange") {
	min:String
	max:String
}
"""<AUTHOR>
	@since
"""
input MainTaskIdsRequest @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.request.MainTaskIdsRequest") {
	"""主任务id列表"""
	taskIds:[String]
}
"""网校智能学习服务配置
	<AUTHOR>
	@date 2024/5/10 16:39
"""
input OnlineSchoolSmartLearningServiceConfigRequest @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.request.OnlineSchoolSmartLearningServiceConfigRequest") {
	"""服务商ID(所属网校ID)"""
	servicerId:String
	"""网校智能学习服务ID"""
	onlineSchoolSmartLearningServiceId:String
	"""课程学习配置"""
	courseLearningConfigure:CourseLearningConfigure
	"""课程测验配置"""
	courseQuizConfigure:CourseQuizConfigure
	"""考试配置"""
	examConfigure:ExamConfigure
}
"""@Author: chenzeyu
	@CreateTime: 2025-03-21  14:38
	@Description: TODO
"""
input QueryLastTimeExpectationStartStudyDateRequest @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.request.QueryLastTimeExpectationStartStudyDateRequest") {
	"""智能学习主任务id"""
	autoLearningMainTaskId:String
}
"""@Author: chenzeyu
	@CreateTime: 2025-03-20  10:11
	@Description: 重启智能学习任务请求
"""
input RestartAutoLearningTaskRequest @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.request.RestartAutoLearningTaskRequest") {
	"""学员自动学习任务结果ID"""
	studentAutoLearningTaskResultId:String
	"""重启智能学习任务期望开始学习时间配置类型
		@see RestartAutoLearningTaskStudyStartTimeConfigTypes
	"""
	studyStartTimeConfigTypes:Int!
	"""期望开始学习时间，仅当studyStartTimeConfigTypes为自定义时生效"""
	studyStartTime:DateTime
	"""是否允许重叠"""
	allowOverlap:Boolean!
}
"""校验学习方案是否存在自动学习
	<AUTHOR> By Cb
	@since 2024/02/26 11:55
"""
input ValidLearningSchemeExistAutoLearningRequest @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.request.ValidLearningSchemeExistAutoLearningRequest") {
	"""学习方案ID列表"""
	learningSchemeIdList:[String]!
}
"""课程学习配置
	<AUTHOR> By Cb
	@since 2024/05/10 9:06
"""
input CourseLearningConfigure @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.onlineschoolsmartlearningservice.model.CourseLearningConfigure") {
	"""每天学习时间区间列表"""
	everyDayLearningTimeList:[TimeRange]
	"""每天不学习时间列表"""
	everyDayNotLearningTimeList:[TimeRange]
	"""首次开始学习时间 (报名后的X天 - X天)"""
	firstLearningDayRange:IntegerRange
	"""每天学习时长区间(秒)"""
	everyDayLearningTimeRange:IntegerRange
	"""每次学习时长区间(秒)"""
	everyLearningTimeRange:IntegerRange
	"""休息时长区间(秒) 达到每次学习时长区间后休息-随机休息60~180分钟"""
	restTimeRange:IntegerRange
	"""是否启用
		默认:true
	"""
	enable:Boolean
	"""每天学习学时区间"""
	everyDayLearningPeriodRange:IntegerRange
	"""每次学习学时区间"""
	everyLearningPeriodRange:IntegerRange
	"""规则类型
		0-按课程物理时长 1-按课程学习学时 2-固定时长 3-按学习约束限制
	"""
	ruleType:Int!
}
"""课程测验配置
	<AUTHOR> By Cb
	@since 2024/05/10 9:12
"""
input CourseQuizConfigure @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.onlineschoolsmartlearningservice.model.CourseQuizConfigure") {
	"""答题时长区间(秒)  默认 900s - 3600s (15-60分钟)"""
	answerTimeRange:IntegerRange
	"""是否启用
		默认:true
	"""
	enable:Boolean
}
"""考试配置
	<AUTHOR> By Cb
	@since 2024/05/10 9:14
"""
input ExamConfigure @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.onlineschoolsmartlearningservice.model.ExamConfigure") {
	"""答题时长区间(秒)  (最少考试总时长1/3)"""
	answerTimeRange:IntegerRange
	"""时长占比(占配置时长的比例)"""
	durationRatio:Double
	"""是否启用
		默认:true
	"""
	enable:Boolean
}
"""<AUTHOR>
	@since
"""
type BatchTerminateAutoLearningResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.BatchTerminateAutoLearningResponse") {
	"""批量处理响应"""
	responses:[TerminateAutoLearningResponse]
}
"""@Author: chenzeyu
	@CreateTime: 2025-03-21  14:36
	@Description: TODO
"""
type QueryLastTimeExpectationStartStudyDateResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.QueryLastTimeExpectationStartStudyDateResponse") {
	"""期望开始学习时间(为空则是没有填写)"""
	expectationStartStudyDate:DateTime
}
"""@Author: chenzeyu
	@CreateTime: 2025-03-20  10:49
	@Description: 重启智能学习任务响应
"""
type RestartAutoLearningTaskResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.RestartAutoLearningTaskResponse") {
	"""code"""
	code:String
	"""消息"""
	msg:String
}
"""<AUTHOR>
	@since
"""
type TerminateAutoLearningResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.TerminateAutoLearningResponse") {
	"""code
		200  正常
		501  处理中
		502  已终止
	"""
	code:String
	"""异常信息"""
	msg:String
	"""任务id"""
	taskId:String
}
"""校验学习方案是否存在自动学习
	<AUTHOR> By Cb
	@since 2024/02/26 11:55
"""
type ValidLearningSchemeExistAutoLearningResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.ValidLearningSchemeExistAutoLearningResponse") {
	"""校验结果列表
		key: 学习方案ID
		value: 学习方案是否存在自动学习
	"""
	resultMap:Map
}

scalar List
