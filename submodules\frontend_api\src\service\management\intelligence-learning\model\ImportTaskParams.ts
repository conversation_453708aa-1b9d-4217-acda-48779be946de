import { QuestionImportTaskStatusEnum } from '@api/service/common/enums/async-task/QuestionImportTaskStatus'
import { TaskExecuteParamRequest } from '@api/ms-gateway/ms-importopen-v1'

export default class ImportTaskParams {
  /**
   * 执行状态
   */
  taskState: QuestionImportTaskStatusEnum = null
  /**
   * 开始时间
   */
  executeStartTime = ''
  /**
   * 结束时间
   */
  executeEndTime = ''

  /**
   * 转换方法
   */
  static to(vo: ImportTaskParams) {
    const dto = new TaskExecuteParamRequest()
    dto.taskState = vo.taskState ?? undefined
    dto.executeStartTime = vo.executeStartTime ?? undefined
    dto.executeEndTime = vo.executeEndTime ?? undefined
    dto.taskCategoryList = ['ADMIN_NORMAL_IMPORT_AND_LEARNING']
    return dto
  }
}
