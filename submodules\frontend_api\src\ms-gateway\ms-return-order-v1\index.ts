import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-return-order-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-return-order-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 退货单同意申请请求
<AUTHOR>
 */
export class ReturnOrderAgreeApplyRequest {
  /**
   * 退货单号
   */
  returnOrderNo: string
  /**
   * 审批意见
   */
  approveComment?: string
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 是否自动同意退款
   */
  autoAgreeReturn: boolean
}

/**
 * 退货单批量同意申请请求
<AUTHOR>
 */
export class ReturnOrderAgreeBatchApplyRequest {
  /**
   * 订单和退货单信息列表
   */
  orderReturnPairs: Array<OrderReturnPair>
  /**
   * 审批意见
   */
  approveComment?: string
  /**
   * 是否自动同意退款
   */
  autoAgreeReturn: boolean
}

/**
 * 订单和退货单对应关系
 */
export class OrderReturnPair {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 退货单号
   */
  returnOrderNo: string
}

/**
 * 退货单申请取消请求
<AUTHOR>
 */
export class ReturnOrderCancelApplyRequest {
  returnOrderNo: string
  /**
   * 订单号
   */
  orderNo?: string
  cancelReason?: string
}

/**
 * 退货单批量确认退款请求
<AUTHOR>
 */
export class ReturnOrderConfirmBatchRefundRequest {
  returnOrderNoList: Array<string>
}

/**
 * 退货单确认退款请求
<AUTHOR>
 */
export class ReturnOrderConfirmRefundRequest {
  returnOrderNo?: string
}

/**
 * 退货单拒绝申请请求
<AUTHOR>
 */
export class ReturnOrderRejectApplyRequest {
  returnOrderNo: string
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 审批意见
   */
  approveComment?: string
}

export class ReturnOrderRetryRecycleRequest {
  returnOrderNo: string
}

export class ReturnOrderRetryRefundRequest {
  returnOrderNo: string
}

export class ReturnOrderAgreeApplyResponse {
  code: string
  message: string
  returnOrderNo: string
}

export class ReturnOrderAgreeBatchApplyResponse {
  returnOrderAgreeApplyResponseList: Array<ReturnOrderAgreeApplyResponse>
}

export class ReturnOrderCancelApplyResponse {
  code: string
  message: string
}

export class ReturnOrderConfirmBatchRefundResponse {
  returnOrderConfirmRefundResponseList: Array<ReturnOrderConfirmRefundResponse>
}

export class ReturnOrderConfirmRefundResponse {
  code: string
  message: string
  returnOrderNo: string
}

export class ReturnOrderRejectApplyResponse {
  code: string
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 返回退货的原因id和原因描述的列Map,key为原因id,value为原因描述
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async prepareReturn(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.prepareReturn,
    operation?: string
  ): Promise<Response<Map<string, string>>> {
    return commonRequestApi<Map<string, string>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 同意退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeReturnApply(
    request: ReturnOrderAgreeApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.agreeReturnApply,
    operation?: string
  ): Promise<Response<ReturnOrderAgreeApplyResponse>> {
    return commonRequestApi<ReturnOrderAgreeApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量同意退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeReturnBatchApply(
    request: ReturnOrderAgreeBatchApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.agreeReturnBatchApply,
    operation?: string
  ): Promise<Response<ReturnOrderAgreeBatchApplyResponse>> {
    return commonRequestApi<ReturnOrderAgreeBatchApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量确认退款，前端要求已退款给予300状态码
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async confirmBatchRefund(
    request: ReturnOrderConfirmBatchRefundRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.confirmBatchRefund,
    operation?: string
  ): Promise<Response<ReturnOrderConfirmBatchRefundResponse>> {
    return commonRequestApi<ReturnOrderConfirmBatchRefundResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 确认退款，前端要求已退款给予300状态码
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async confirmRefund(
    request: ReturnOrderConfirmRefundRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.confirmRefund,
    operation?: string
  ): Promise<Response<ReturnOrderConfirmRefundResponse>> {
    return commonRequestApi<ReturnOrderConfirmRefundResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 拒绝退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rejectReturnApply(
    request: ReturnOrderRejectApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.rejectReturnApply,
    operation?: string
  ): Promise<Response<ReturnOrderRejectApplyResponse>> {
    return commonRequestApi<ReturnOrderRejectApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重新回收资源
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async retryRecycleResource(
    request: ReturnOrderRetryRecycleRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.retryRecycleResource,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 继续退款
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async retryRefund(
    request: ReturnOrderRetryRefundRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.retryRefund,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 卖家取消退货申请
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sellerCancelReturnApply(
    request: ReturnOrderCancelApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sellerCancelReturnApply,
    operation?: string
  ): Promise<Response<ReturnOrderCancelApplyResponse>> {
    return commonRequestApi<ReturnOrderCancelApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
