import { ResponseStatus } from '@hbfe/common'
import QualifiedTrainClassVo from '@api/service/management/train-class/mutation/vo/QualifiedTrainClassVo'
import PlatformLearningscheme from '@api/platform-gateway/platform-learningscheme-v1'

/**
 * @description
 */
class MutationQualifiedTrainClass {
  /**
   * 合格参数
   */
  qualifiedParams: QualifiedTrainClassVo = new QualifiedTrainClassVo()

  /**
   * 培训班一键合格
   */
  async doQualifiedTrainClass(): Promise<ResponseStatus> {
    const request = this.qualifiedParams.to()
    const { status, data } = await PlatformLearningscheme.oneKeyPass(request)
    if (status.isSuccess() && data) {
      return new ResponseStatus(Number(data.code), data.message)
    } else {
      return new ResponseStatus(500, data?.message || '操作失败')
    }
  }
}

export default MutationQualifiedTrainClass
