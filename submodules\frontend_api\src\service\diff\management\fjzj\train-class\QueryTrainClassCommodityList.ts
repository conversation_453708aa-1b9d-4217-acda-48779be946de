import { CommoditySkuRequest, SortPolicy } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import FJZJDataExportBackstageGateway, {
  CommoditySkuSortRequest,
  CommoditySkuSortField
} from '@api/diff-gateway/fjzj-data-export-gateway-backstage'
/**
 * 运营域获取培训班商品列表
 * 差异化导出
 */
class QueryTrainClassCommodityList {
  /**
   * 导出方案列表（商品）
   */
  exportCommoditySkuInServicer(filterCommodity: CommoditySkuRequest, sortRequest?: Array<CommoditySkuSortRequest>) {
    const sortRequestM = new CommoditySkuSortRequest()
    sortRequestM.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequestM.policy = SortPolicy.DESC
    !sortRequest && (sortRequest = [])
    sortRequest.push(sortRequestM)
    const res = FJZJDataExportBackstageGateway.exportCommoditySkuInServicer({
      queryRequest: filterCommodity,
      sortRequest: sortRequest || []
    })
    return res
  }
  // endregion
}

export default QueryTrainClassCommodityList
