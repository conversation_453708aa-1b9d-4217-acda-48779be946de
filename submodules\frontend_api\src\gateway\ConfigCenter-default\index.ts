import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/ConfigCenter-default'

// 枚举

// 类

/**
 * 配置
<AUTHOR> create 2019/12/5 18:57
 */
export class PropertyDTO {
  /**
   * 键
   */
  key: string
  /**
   * 值
   */
  value: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取指定key的值
   * @param key
   * @return
   * @param query 查询 graphql 语法文档
   * @param key 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPropertyValue(
    key: string,
    query: DocumentNode = GraphqlImporter.getPropertyValue,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: query,
      variables: { key },
      operation: operation
    })
  }

  /**   * 获取组件的所有业务配置
   * @return
   * @param query 查询 graphql 语法文档
   * @param componentName 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getComponentBusinessProperties(
    componentName: string,
    query: DocumentNode = GraphqlImporter.getComponentBusinessProperties,
    operation?: string
  ): Promise<Response<Array<PropertyDTO>>> {
    return commonRequestApi<Array<PropertyDTO>>(SERVER_URL, {
      query: query,
      variables: { componentName },
      operation: operation
    })
  }

  /**   * 获取指定namespace下的所有业务配置
   * @return
   * @param query 查询 graphql 语法文档
   * @param namespace 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getNamespaceProperties(
    namespace: string,
    query: DocumentNode = GraphqlImporter.getNamespaceProperties,
    operation?: string
  ): Promise<Response<Array<PropertyDTO>>> {
    return commonRequestApi<Array<PropertyDTO>>(SERVER_URL, {
      query: query,
      variables: { namespace },
      operation: operation
    })
  }

  /**   * 获取子项目下所有业务配置
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBusinessProperties(
    query: DocumentNode = GraphqlImporter.getBusinessProperties,
    operation?: string
  ): Promise<Response<Array<PropertyDTO>>> {
    return commonRequestApi<Array<PropertyDTO>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }
}

export default new DataGateway()
