const Sentry = require('./index')
const { getGitInfoFromPath, writeToCache } = require('../common')
require('dotenv').config()

const sentry = new Sentry({
  authToken: process.env.VUE_APP_SENTRY_AUTH_TOKEN,
  host: process.env.VUE_APP_SENTRY_HOST,
  org: process.env.VUE_APP_SENTRY_ORG
})

const gitInfo = getGitInfoFromPath()
;(async() => {
  const projectKey = await sentry.getProjectKeys(`${gitInfo.project}_${gitInfo.slug}`)
  writeToCache('sentry-info.json', projectKey)
})()
