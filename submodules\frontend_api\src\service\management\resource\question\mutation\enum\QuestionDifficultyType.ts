import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 评定方式类型
 */
export enum QuestionDifficulty {
  'Simple' = 1,
  'Medium' = 2,
  'Difficult' = 3
}

class QuestionDifficultyType extends AbstractEnum<QuestionDifficulty> {
  static enum = QuestionDifficulty

  constructor(status?: QuestionDifficulty) {
    super()
    this.current = status
    this.map.set(QuestionDifficulty.Simple, '简单')
    this.map.set(QuestionDifficulty.Medium, '中等')
    this.map.set(QuestionDifficulty.Difficult, '难')
  }
}

export default new QuestionDifficultyType()
