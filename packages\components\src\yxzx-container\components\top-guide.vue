<template>
  <ul class="header-nav f-flex-sub" ref="topGuide">
    <li class="current-bg" v-show="menuList.length && hasTopActive" :style="currentSignPositionStyle"></li>
    <li class="nav-item" :id="`top_guide_menu_item_first`" @click="backHome">
      <i class="iconfont icon-lsh-return"></i>
      <span class="txt">返回</span>
    </li>
    <li
      class="nav-item"
      v-for="(menu, index) in menuList"
      @click="toggleTopMenu(menu.router.path, index)"
      :key="menu.path"
      :style="menu.code === currentActiveTopMenu ? { color: '#d35171' } : {}"
    >
      <i class="iconfont" :class="menu.meta.icon"></i>
      {{ menu.name }}
    </li>
  </ul>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import rootModule from '@/store/RootModule'
  import { isUndefined } from 'lodash'
  import Driver from 'driver.js'
  import 'driver.js/dist/driver.min.css'
  import UiModule from '@/store/UiModule'
  import fxsData from '@hbfe/jxjy-admin-components/src/yxzx-container/components/top-guide-data/fxsIndex'
  import gysData from '@hbfe/jxjy-admin-components/src/yxzx-container/components/top-guide-data/cgIndex'
  import gysJcbData from '@hbfe/jxjy-admin-components/src/yxzx-container/components/top-guide-data/cgJcbIndex'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import { DistributionServiceTypeEnum } from '@api/service/common/capability-service-config/enum/DistributionServiceTypeEnum'
  interface StyleInfo {
    width: number
    left: number
  }

  @Component
  export default class extends Vue {
    @Ref('topGuide')
    topGuide: HTMLDivElement

    widthMap: Array<StyleInfo> = new Array<StyleInfo>()
    @Watch('startGuide')
    guideWatch() {
      if (UiModule.startGuide) {
        const startDiver = this.initDriver()
        startDiver.start()
        UiModule.changeStart(true)
        UiModule.changeGuideState(false)
      }
    }
    // 开始指引弹窗结束，开始新手指引步骤
    @Watch('driverStartDialog', {
      deep: true
    })
    driverChange(val: boolean) {
      if (!val) {
        const startDiver = this.initDriver()
        UiModule.changeStart(true)
        startDiver.start()
      }
    }

    get startGuide() {
      return UiModule.startGuide
    }
    // 指引弹窗显隐
    driverStartDialog = true
    driverDoneDialog = false
    // 是否走完了一遍指引流程
    isFirstGuide = false

    userTypeInfo = AuthorityModule.roleFactory.getQueryCurrentUserRoleList()
    userType = ''
    userTypeNumber: number
    roleCode: string
    roleList: any

    async getUserTpyeInfo() {
      await this.userTypeInfo.getCurrentUserRoleList()
      const res = this.userTypeInfo.roleList
      const type = res.filter((item) => {
        return item.category != 5 && item.isBuiltIn
      })
      if (type.length) {
        this.userTypeNumber = type[0]?.category
        this.roleCode = type[0]?.roleCode
      } else {
        console.error('为获取到对应的角色,请检查接口。')
      }
    }

    async created() {
      await this.getUserTpyeInfo()
      if (this.userTypeNumber == 510) {
        this.userType = 'admin'
      } else if (this.userTypeNumber == 17) {
        this.userType = 'nzgys'
      } else if (this.userTypeNumber == 18) {
        this.userType = 'nzfxs'
      }
      this.isUserAlreadyDriver()
    }

    // 是否展示开始弹窗Dialog
    get isShowStartDialog(): boolean {
      return this.driverStartDialog && this.isFirstGuide
    }

    get menuList() {
      return rootModule.fxMenuList
    }

    get currentActiveTopMenu() {
      return rootModule.currentFxActiveTopMenu
    }

    get hasTopActive() {
      return isUndefined(rootModule.currentFxActiveTopMenuIndex) ? undefined : true
    }
    get currentSignPositionStyle() {
      return this.getInfo()
    }

    // 判断用户是否走过一遍指引流程
    isUserAlreadyDriver() {
      let temp = false
      // 为true表示已经走完一遍指引
      if (this.userType == 'nzgys' || this.userType == 'admin') {
        temp = localStorage.getItem('isYxzxGysFirstGuide') !== 'notYxzxGysFirstGuide'
      } else if (this.userType == 'nzfxs') {
        temp = localStorage.getItem('isYxzxFxsFirstGuide') !== 'notYxzxFxsFirstGuide'
      }
      this.isFirstGuide = temp
    }

    toggleTopMenu(id: string, index: number) {
      rootModule.setFxCurrentTopMenu({
        id,
        index
      })
    }

    // 关闭结束指引弹窗Dialog
    closeDriverDoneDialog() {
      this.driverDoneDialog = false
      if (this.userType == 'nzgys' || this.userType == 'admin') {
        localStorage.setItem('isYxzxGysFirstGuide', 'notYxzxGysFirstGuide')
      } else if (this.userType == 'nzfxs') {
        localStorage.setItem('isYxzxFxsFirstGuide', 'notYxzxFxsFirstGuide')
      }
    }

    getInfo() {
      const info = this.widthMap[rootModule.currentFxActiveTopMenuIndex]
      if (!info) {
        return {}
      }
      return {
        left: `${info.left}px`,
        width: `${info.width}px`
      }
    }

    initDriver(idList?: Array<string>) {
      const options = {
        allowClose: false, //禁止点击外部关闭
        keyboardControl: false, //禁止键盘关闭
        doneBtnText: '开始使用', // 完成按钮标题
        closeBtnText: '跳过', // 关闭按钮标题
        stageBackground: '#fff', // 引导对话的背景色
        nextBtnText: '下一步', // 下一步按钮标题
        prevBtnText: '上一步', // 上一步按钮标题
        onReset: (item: Driver.Element) => {
          UiModule.changeStart(false)
          this.closeDriverDoneDialog()
        }
      }

      const driver = new Driver(options)
      const defineSteps: Array<Driver.Step> = new Array<Driver.Step>()
      //   idList.forEach((id: string) => {
      if (this.userType == 'admin') {
        defineSteps.push(...gysJcbData)
      } else if (this.userType == 'nzgys') {
        defineSteps.push(...gysJcbData)
      } else if (this.userType == 'nzfxs') {
        defineSteps.push(...fxsData)
      }
      //   })
      driver.defineSteps(defineSteps)
      return driver
    }

    @Watch('menuList', {
      immediate: true
    })
    someChange() {
      this.$nextTick(() => {
        setTimeout(() => {
          const idList: Array<string> = []
          Array.from(this.topGuide.children).forEach((children: HTMLDivElement, index: number) => {
            idList.push(children.getAttribute('id'))
            if (!index) return
            this.widthMap.push({
              width: children.offsetWidth,
              left: children.offsetLeft
            })
          })
          const startDiver = this.initDriver(idList)
          if (this.isFirstGuide) {
            startDiver.start()
          }
        }, 500)
      })
    }

    backHome() {
      this.$router.push('/home')
    }
  }
</script>

<style lang="scss">
  div.last-step-popover-class {
    div.driver-popover-footer {
      button.driver-close-btn {
        display: none;
      }
    }
  }
  .m-guide-step {
    margin-bottom: none !important;
  }
  .m-guide-step .hd {
    color: #404040;
  }
  .m-guide-step .bd {
    color: #3d3d3d;
  }
  .current-bg {
    transition: all 0.3s;
    left: 0;
  }

  .nav-item {
    transition: color 0.5s;
  }

  .middle-style {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .m-guide-wrap-z-index {
    z-index: 1001;
  }

  div#driver-popover-item {
    max-width: 460px;
    padding: 8px;
    .driver-popover-description {
      .m-guide-step {
        margin-bottom: 0 !important;
      }
    }
    .driver-popover-title {
      margin-left: 58px;
    }
    .driver-popover-description {
      display: flex;
      align-items: center;
    }
    div.driver-popover-footer {
      display: flex !important;
      justify-content: right;
      //   flex-direction: row-reverse;
      //   margin-left: 70%;
      button.driver-close-btn {
        color: rgb(180, 180, 189);
        border: 0;
        background-color: #ffffff;
        // text-decoration: underline;
        font-size: 14px !important;
      }
      .driver-btn-group {
        display: flex;
        justify-content: right;
        button.driver-prev-btn {
          display: none !important;
        }
        button.driver-next-btn {
          color: rgb(51, 51, 230);
          border: 0;
          background-color: #ffffff;
          //   text-decoration: underline;
          font-size: 14px !important;
        }
      }
    }
  }
  div.last-step-popover-class {
    div.driver-popover-footer {
      button.driver-close-btn {
        display: none !important;
      }
    }
  }
  // 修改营销中心引导的背景色
  div#driver-highlighted-element-stage {
    background: #1f86f0 !important;
    color: #fff !important;
  }
</style>
