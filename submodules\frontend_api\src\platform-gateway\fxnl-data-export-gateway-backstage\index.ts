import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/fxnl-data-export-gateway-backstage'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'fxnl-data-export-gateway-backstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum QueryWayType {
  ONLY_ME = 'ONLY_ME',
  ONLY_MY_DISTRIBUTOR = 'ONLY_MY_DISTRIBUTOR',
  ALL = 'ALL'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class ObsFileMetaData {
  bizType?: string
  owner?: string
  sign?: string
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

/**
 * 异步任务组名返回对象
 */
export class JobGroupRequest {
  /**
   * 任务组key
   */
  group?: string
  /**
   * 任务组名（模糊查询）
   */
  groupName?: string
}

/**
 * 功能描述：任务查询参数
@Author： wtl
@Date： 2022/1/18 15:13
 */
export class JobRequest {
  /**
   * 任务组名（必填）
   */
  group?: string
  /**
   * 任务组名匹配方式（EQ：完全匹配 LIKE：模糊匹配[*group*] LLIKE：左模糊匹配[group*] RLIKE：右模糊匹配[*group]，不传值默认为完全匹配）
   */
  groupOperator?: string
  /**
   * 任务名（模糊查询）
   */
  jobName?: string
  /**
   * 任务状态(executing:运行中 executed:运行完成 fail:运行失败)
@see UserJobState
   */
  jobState?: string
  /**
   * 任务执行时间 yyyy-MM-dd HH:mm:ss
   */
  executeTimeScope?: DateScopeRequest
  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult?: boolean
  /**
   * 分割粒度
null-无 1-单位
   */
  granularity?: number
}

export class PricingPolicyinPortalInDistributorRequest {
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 门户标题
   */
  title?: string
  /**
   * 门户简称
   */
  shortName?: string
  /**
   * 域名
   */
  domainName?: string
  /**
   * 是否已发布 1-开启 2-关闭
   */
  isPublished?: boolean
  jobName?: string
  metaData?: ObsFileMetaData
}

export class PricingPolicyinPortalInSupplierRequest {
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 分销商id
   */
  belongServiceId?: string
  /**
   * 门户展示名称
   */
  title?: string
  /**
   * 门户简称
   */
  shortName?: string
  jobName?: string
  metaData?: ObsFileMetaData
}

/**
 * 商品sku属性查询条件
 */
export class PropertyRequest {
  /**
   * 商品skuKey
   */
  propertyKey?: string
  /**
   * 商品skuValue
   */
  propertyValue?: string
}

/**
 * 供应商授权出的商品
 */
export class DistributorCommodityAndRelationRequest {
  /**
   * 分销商商品名称
   */
  saleTitle?: string
  /**
   * 分销商id集合
   */
  distributorIdList?: Array<string>
  /**
   * 分销商等级
   */
  distributorLevel?: number
  /**
   * 销售状态 1-有效 2-无效
   */
  saleStatus?: number
  /**
   * 分销地区路径
   */
  contractDistributionRegionPathList?: Array<string>
  /**
   * 商品sku属性
   */
  propertyList?: Array<PropertyRequest>
  /**
   * 培训方案类型
   */
  schemeTypeList?: Array<string>
  /**
   * 授权价格类型 1-固定 2-区间
   */
  priceType?: number
  /**
   * 分销价格范围查询-最大价格
   */
  maxPrice?: number
  /**
   * 分销价格范围查询-最小价格
   */
  minPrice?: number
  /**
   * 分销价格-最大价格
   */
  policyMaxPrice?: number
  /**
   * 分销价格-最小价格
   */
  policyMinPrice?: number
  /**
   * 定价方案状态
   */
  statusList?: Array<number>
  /**
   * 分销是否有效
0-有效 1-无效
商品的分销开始时间、结束时间作为判断
   */
  distributionStatus?: number
  /**
   * 分销商商品id集合
   */
  distributorCommodityIdList?: Array<string>
  /**
   * 商品id集合
   */
  commodityIdList?: Array<string>
  /**
   * 网校原始商品id集合
   */
  rootCommodityIdList?: Array<string>
  /**
   * 需要排除的网校原始商品id集合
   */
  excludedRootCommodityIdList?: Array<string>
  /**
   * 网校id
   */
  onlineSchoolId?: string
  /**
   * 培训方案名称
   */
  schemeName?: string
  /**
   * 网校销售状态
0-开启 1-关闭
商品的网校销售开始时间、结束时间作为判断
   */
  onlineSchoolStatus?: number
  /**
   * 授权商品来源类型
   */
  commoditySourceTypeList?: Array<number>
  /**
   * 定价方案id
   */
  productPricingSchemeIdList?: Array<string>
  /**
   * 需要排除的定价方案id
   */
  excludedPricingSchemeIdList?: Array<string>
  /**
   * 是否存在定价方案
   */
  existPricingScheme?: boolean
  /**
   * 是否已启用定价方案
   */
  enablePricingScheme?: boolean
  /**
   * 是否已启用优惠申请
   */
  enableDiscountScheme?: boolean
  /**
   * 推广门户标识id
   */
  portalIdentify?: string
  /**
   * 推广门户展示名称
   */
  showName?: string
  /**
   * 推广门户简称
   */
  shortName?: string
  /**
   * 门户域名
   */
  domainName?: string
  /**
   * 门户状态
0-停用 1-启用
   */
  portalStatus?: number
  /**
   * 门户展示 (0-不展示, 1-展示）
   */
  showPortal?: number
  /**
   * 门户推广 (0-不推广, 1-推广）
   */
  portalPromotion?: number
  /**
   * 商品上架状态
0-下架 1-上架
   */
  shelveStatus?: number
  /**
   * 销售渠道类型
0-自营渠道 1-分销渠道 2-专题渠道 3-华医网 4-推广门户渠道
   */
  saleChannelType?: number
  /**
   * 销售渠道id
   */
  saleChannelId?: string
  /**
   * 优惠申请审批状态
0-待处理 1-通过 2-未通过
   */
  auditStatusList?: Array<number>
  /**
   * 优惠状态
1-开启 2-关闭
与优惠申请内容状态有关、与优惠周期约束、优惠开始时间、优惠结束时间有关
   */
  discountStatusList?: Array<number>
}

/**
 * 学员培训方案学习查询条件
<AUTHOR>
@version 1.0
@date 2022/1/17 11:40
 */
export class StudentSchemeLearningRequestInDistributor {
  /**
   * 学员信息
   */
  student?: UserRequest
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest
  /**
   * 对接管理系统
   */
  connectManageSystem?: ConnectManageSystemRequest
  /**
   * 扩展信息
   */
  extendedInfo?: ExtendedInfoRequest
  /**
   * 方案是否提供培训证明
   */
  openPrintTemplate?: boolean
  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  trainingChannelName?: string
  /**
   * 专题Id  用于不同专题域名 查询对应专题的培训班
   */
  trainingChannelId?: string
  /**
   * 门户id
   */
  portalId?: string
}

/**
 * 学员培训方案学习查询条件
<AUTHOR>
@version 1.0
@date 2022/1/17 11:40
 */
export class StudentSchemeLearningRequestInSupplier {
  /**
   * 学员信息
   */
  student?: UserRequest
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest
  /**
   * 对接管理系统
   */
  connectManageSystem?: ConnectManageSystemRequest
  /**
   * 扩展信息
   */
  extendedInfo?: ExtendedInfoRequest
  /**
   * 方案是否提供培训证明
   */
  openPrintTemplate?: boolean
  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  trainingChannelName?: string
  /**
   * 专题Id  用于不同专题域名 查询对应专题的培训班
   */
  trainingChannelId?: string
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 分销商ID
   */
  distributorId?: string
}

export class ExtendedInfoRequest {
  /**
   * 是否打印
true 打印 false 未打印
   */
  whetherToPrint?: boolean
}

/**
 * 学员学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class StudentLearningRequest {
  /**
   * 培训结果
<p>
-1:未知，培训尚未完成
1:培训合格
0:培训不合格
   */
  trainingResultList?: Array<number>
  /**
   * 培训结果时间
   */
  trainingResultTime?: DateScopeRequest
  /**
   * 无需学习的学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 练习学习方式
4：自主学习课程学习方式
   */
  notLearningTypeList?: Array<number>
  /**
   * 课程学习状态（0：未学习 1：学习中 2：学习完成）
   */
  courseScheduleStatus?: number
  /**
   * 考试结果（-1：未考核 0：不合格 1：合格）
   */
  examAssessResultList?: Array<number>
}

/**
 * 地区sku属性查询条件
<AUTHOR>
@version 1.0
@date 2022/2/25 10:55
 */
export class RegionSkuPropertyRequest {
  /**
   * 地区: 省
   */
  province?: string
  /**
   * 地区: 市
   */
  city?: string
  /**
   * 地区: 区县
   */
  county?: string
}

/**
 * 地区匹配查询
<AUTHOR>
@version 1.0
@date 2022/2/25 14:19
 */
export class RegionSkuPropertySearchRequest {
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest>
  /**
   * 地区匹配条件
<p>
ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回，如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
PART部分匹配：查询结果返回的地区与查询条件有给值的地区就会返回 如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
   */
  regionSearchType?: number
}

/**
 * 培训方案信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class SchemeRequest {
  /**
   * 培训方案id
   */
  schemeId?: string
  /**
   * 培训方案id
   */
  schemeIdList?: Array<string>
  /**
   * 培训方案类型
<p>
chooseCourseLearning: 选课规则
autonomousCourseLearning: 自主选课
   */
  schemeType?: string
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 培训属性
   */
  skuProperty?: SchemeSkuPropertyRequest
}

/**
 * 培训属性
<AUTHOR>
@version 1.0
@date 2022/1/17 10:22
 */
export class SchemeSkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 岗位类别
   */
  positionCategory?: Array<string>
  /**
   * 培训对象
   */
  trainingObject?: Array<string>
  /**
   * 技术等级
   */
  jobLevel?: Array<string>
  /**
   * 工种
   */
  jobCategory?: Array<string>
  /**
   * 科目
   */
  subject?: Array<string>
  /**
   * 年级
   */
  grade?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
}

/**
 * @version: 1.0
@description: 对接管理系统
@author: sugs
@create: 2022-11-15 11:27
 */
export class ConnectManageSystemRequest {
  /**
   * 同步状态
0 未同步
1 已同步
2 同步失败
   */
  syncStatus?: number
}

/**
 * 数据分析信息
<AUTHOR>
@version 1.0
@date 2022/1/20 15:14
 */
export class DataAnalysisRequest {
  /**
   * 成果配置可获得学时
   */
  trainingResultPeriod?: DoubleScopeRequest
  /**
   * 考核要求学时
   */
  requirePeriod?: DoubleScopeRequest
  /**
   * 已获得总学时
   */
  acquiredPeriod?: DoubleScopeRequest
}

/**
 * 学员学习报名信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:08
 */
export class LearningRegisterRequest {
  /**
   * 报名方式
<p>
1:学员自主报名
2:集体报名
3:管理员导入
   */
  registerType?: number
  /**
   * 报名来源类型(ORDER：订单 SUB_ORDER：子订单 EXCHANGE_ORDER：换货单)
   */
  sourceType?: string
  /**
   * 报名来源ID
   */
  sourceId?: string
  /**
   * 学员状态(1:正常 2：冻结 3：失效)
   */
  status?: Array<number>
  /**
   * 报名时间
   */
  registerTime?: DateScopeRequest
  /**
   * 来源订单号
   */
  orderNoList?: Array<string>
  /**
   * 来源子订单号
   */
  subOrderNoList?: Array<string>
  /**
   * 来源批次单号
   */
  batchOrderNoList?: Array<string>
}

/**
 * 地区模型
<AUTHOR>
@version 1.0
@date 2022/2/27 20:01
 */
export class RegionRequest {
  /**
   * 地区：省
   */
  province?: string
  /**
   * 地区：市
   */
  city?: string
  /**
   * 地区：区
   */
  county?: string
}

/**
 * 用户属性
<AUTHOR>
@version 1.0
@date 2022/1/15 11:01
 */
export class UserPropertyRequest {
  /**
   * 所属地区路径
   */
  regionList?: Array<RegionRequest>
  /**
   * 工作单位名称
   */
  companyName?: string
  /**
   * 下单地区
   */
  payOrderRegionList?: Array<RegionRequest>
}

/**
 * 用户信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:00
 */
export class UserRequest {
  /**
   * 用户id
   */
  userIdList?: Array<string>
  /**
   * 账户id
   */
  accountIdList?: Array<string>
  /**
   * 用户属性
   */
  userProperty?: UserPropertyRequest
}

/**
 * 交易统计请求参数
<AUTHOR>
 */
export class StatisticTradeRecordRequest {
  /**
   * 分销商id集合
   */
  distributorIdList?: Array<string>
  /**
   * 供应商id集合
   */
  supplierIdList?: Array<string>
  /**
   * 商品名称 模糊匹配
   */
  commodityName?: string
  /**
   * 网校id集合 List
   */
  onlineSchoolList?: Array<string>
  /**
   * 商品ID集合
   */
  commodityIdList?: Array<string>
  /**
   * 排除商品ID集合
   */
  excludeCommodityIdList?: Array<string>
  /**
   * 商品售价范围
   */
  commodityPriceScope?: DoubleScopeRequest
  /**
   * 查询时间范围
   */
  queryDateScope?: DateScopeRequest
  /**
   * 查询方式
仅查询自己
只查询下级分销商
包含自己和下级分销商
@see QueryWayType
   */
  queryWayType?: QueryWayType
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 是否是推广门户的数据 | true.是 false.否
   */
  isPortalData?: boolean
  jobName?: string
  metaData?: ObsFileMetaData
}

/**
 * 异步任务组名返回对象
 */
export class JobGroupResponse {
  /**
   * 异步任务组key
   */
  group: string
  /**
   * 异步任务组名
   */
  groupName: string
  /**
   * 排序大小
   */
  order: number
  /**
   * 所在域
   */
  domain: Array<string>
}

/**
 * 功能描述：异步任务日志返回对象
@Author： wtl
@Date： 2022/4/11 17:18
 */
export class UserJobLogResponse {
  /**
   * 任务id
   */
  jobId: string
  /**
   * 任务组名
   */
  group: string
  /**
   * 任务名
   */
  jobName: string
  /**
   * 任务开始时间
   */
  beginTime: string
  /**
   * 任务结束时间
   */
  endTime: string
  /**
   * 任务状态(executing:运行中 executed:运行完成 fail:运行失败)
@see UserJobState
   */
  jobState: string
  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult: boolean
  /**
   * 任务执行成功或失败的信息
   */
  message: string
  /**
   * 导出文件路径
   */
  exportFilePath: string
  /**
   * 是否受保护
   */
  isProtected: boolean
  /**
   * 资源id
   */
  fileResourceId: string
  /**
   * 操作人id
   */
  operatorUserId: string
  /**
   * 操作人帐户id
   */
  operatorAccountId: string
}

export class UserJobLogResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserJobLogResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分销商-导出培训商品开通统计报表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommodityDistributorOpenReportInDistributor(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommodityDistributorOpenReportInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出培训商品开通统计明细报表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommodityOpenStatisticsDetailExcelInSupplier(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommodityOpenStatisticsDetailExcelInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商-导出培训商品开通统计报表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommodityOpenStatisticsExcelInSupplier(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommodityOpenStatisticsExcelInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出培训商品开通统计汇总报表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommodityOpenStatisticsSummaryExcelInSupplier(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommodityOpenStatisticsSummaryExcelInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商品管理数据导出
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportDistributionCommodityManageExcelInSupplier(
    request: DistributorCommodityAndRelationRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportDistributionCommodityManageExcelInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出分销商销售统计报表
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportDistributorSalesStatisticsExcelInSupplier(
    request: StatisticTradeRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportDistributorSalesStatisticsExcelInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出分销商的推广门户-分销商
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportPricingPolicyinPortalInDistributor(
    request: PricingPolicyinPortalInDistributorRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportPricingPolicyinPortalInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出分销商的推广门户-供应商
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportPricingPolicyinPortalInSupplier(
    request: PricingPolicyinPortalInSupplierRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportPricingPolicyinPortalInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-定价方案导出
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportProductPricingSchemeInSDistributor(
    request: DistributorCommodityAndRelationRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportProductPricingSchemeInSDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 供应商-定价方案导出
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportProductPricingSchemeInSupplier(
    request: DistributorCommodityAndRelationRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportProductPricingSchemeInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-学习明细报表导出
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentSchemeLearningExcelInSDistributor(
    request: StudentSchemeLearningRequestInDistributor,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInSDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商-学习明细报表导出
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentSchemeLearningExcelInSupplier(
    request: StudentSchemeLearningRequestInSupplier,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询导出任务组(分销商)
   * @param jobGroupRequest : 任务查询条件
   * @return : void
   * @Author： sjm
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param jobGroupRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExportTaskGroupInfoInDistributor(
    jobGroupRequest: JobGroupRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listExportTaskGroupInfoInDistributor,
    operation?: string
  ): Promise<Response<Array<JobGroupResponse>>> {
    return commonRequestApi<Array<JobGroupResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { jobGroupRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询导出任务组(供应商)
   * @param jobGroupRequest : 任务查询条件
   * @return : void
   * @Author： sjm
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param jobGroupRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExportTaskGroupInfoInSupplier(
    jobGroupRequest: JobGroupRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listExportTaskGroupInfoInSupplier,
    operation?: string
  ): Promise<Response<Array<JobGroupResponse>>> {
    return commonRequestApi<Array<JobGroupResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { jobGroupRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageExportTaskInfoInMyself(
    params: { page?: Page; jobRequest?: JobRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageExportTaskInfoInMyself,
    operation?: string
  ): Promise<Response<UserJobLogResponsePage>> {
    return commonRequestApi<UserJobLogResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
