/**
 * 课件信息
 */
import CoursewareType from '@api/service/management/resource/courseware/enum/CoursewareType'
import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CourseChapter
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import TimeFormat from '@api/service/customer/course/query/vo/TimeFormat'
import Mockjs from 'mockjs'
import moment from 'moment'

class Courseware {
  get id(): string {
    return this._id
  }

  set id(value: string) {
    this._id = value
  }

  /**
   * 章节编号，对应标签编号
   */
  private _id: string

  constructor() {
    this._id = Mockjs.Random.guid()
  }

  editMode = false
  cacheName = ''
  sort = 0

  edit() {
    this.editMode = true
  }

  cancel() {
    this.cacheName = this.name
    this.editMode = false
  }

  save() {
    this.name = this.cacheName
    this.editMode = false
  }

  /**
   * 课件名称
   */
  name = ''

  /**
   * 课件时长
   */
  timeLength = 0
  timeLengthFormat: string

  /**
   * 所属课件目录编号
   */
  courseChapterId = ''
  /**
   * 所属课件目录编号
   */
  courseOutlineId = ''

  /**
   * 课件类型，1表示文档，2表示视频，3表示多媒体
   */
  type: CoursewareType

  /**
   * 挂在课件是否支持试听 0不可以试听，1可以试听，
   */
  trialType: number

  selected = false

  static from(courseChapter: CourseChapter) {
    const detail = new Courseware()
    detail.id = courseChapter.coursewareId
    detail.name = courseChapter.coursewareName
    detail.trialType = courseChapter.auditionStatus
    detail.courseOutlineId = courseChapter.courseOutline.id
    detail.courseChapterId = courseChapter.id
    detail.type = courseChapter['type']
    detail.timeLength = courseChapter.timeLength
    const mom = moment.duration(detail.timeLength * 1000)
    const hour = `${mom.get('hours')}`.padStart(2, '0')
    const minutes = `${mom.get('minutes')}`.padStart(2, '0')
    const seconds = `${mom.get('seconds')}`.padStart(2, '0')
    detail.timeLengthFormat = `${hour}:${minutes}:${seconds}`
    // 用来保存的
    detail.cacheName = detail.name
    detail.sort = courseChapter.sort
    return detail
  }
}

export default Courseware
