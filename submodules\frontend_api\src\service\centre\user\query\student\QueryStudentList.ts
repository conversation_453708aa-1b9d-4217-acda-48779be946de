import StudentUserInfoVo from '@api/service/centre/user/query/student/vo/StudentUserInfoVo'
import MsBasicDataQueryBackstageGateway, {
  StudentQueryRequest,
  StudentUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { Page } from '@hbfe/common'
import DataResolve from '@api/service/common/utils/DataResolve'
import QueryStudentUserListVo from '@api/service/centre/user/query/student/vo/QueryStudentUserListVo'
/**
 * @description 查询用户列表
 */

class QueryStudentList {
  request: QueryStudentUserListVo = new QueryStudentUserListVo()
  /**
   * 根据用户id集合查询用户信息
   * @param {string[]} userIds - 用户id集合
   * @param pageSize: 每页显示条数
   */
  async queryStudentInfoListById(userIds: string[], pageSize = 10): Promise<StudentUserInfoVo[]> {
    const result = [] as StudentUserInfoVo[]
    const params = new StudentQueryRequest()
    params.user = new StudentUserRequest()
    params.user.userIdList = [...new Set(userIds)]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = pageSize
    const response = await MsBasicDataQueryBackstageGateway.pageStudentInfoInSubProject({
      page,
      request: params
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      response.data.currentPageData.map(item => {
        const temp = new StudentUserInfoVo()
        temp.from(item)
        result.push(temp)
      })
    }
    return result
  }

  /**
   * 根据用户字段查询用户id集合
   */
  async queryStudentIdList(): Promise<string[]> {
    let result = [] as string[]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 1
    const request = this.request.to()
    const response = await MsBasicDataQueryBackstageGateway.pageStudentInfoInSubProject({
      page,
      request
    })
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      page.pageSize = response.data.totalSize
      const res = await MsBasicDataQueryBackstageGateway.pageStudentInfoInSubProject({
        page,
        request
      })
      if (res.status?.isSuccess() && DataResolve.isWeightyArr(res.data?.currentPageData)) {
        result = [...new Set(res.data.currentPageData.map(item => item.userInfo.userId))]
      }
    }
    return result
  }
}

export default QueryStudentList
