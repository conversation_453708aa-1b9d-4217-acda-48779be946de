/**
 * 账户服务
 */
import MsAccountGateway, {
  BindPhoneByAdminRequest,
  BindPlatformAccountRequest,
  SmsCodeValidRequest
} from '@api/ms-gateway/ms-account-v1'
import { ResponseStatus } from '@hbfe/common'
/**
 * 账户业务对象
 */
class MutationBizAccount {
  /**
   * 账号ID
   */
  accountId = ''
  /**
   * 电话
   */
  phone = ''
  /**
   * 校验码
   */
  captcha = ''
  /**
   * 忘记密码使用，确认身份后的token
   */
  private identityToken = ''
  /**
   * 认证ID
   */
  private authId = ''

  /**
   * 获取基础数据后的token，用于校验校验码
   */
  basicToken = ''
  /**
   * 校验验证码成功后返回的token，用户做后续业务
   */
  caToken = ''
  /**
   * 校验短信验证码成功后返回的token
   */
  phoneToken = ''
  /*********换绑手机流程********** */

  /**
   * 获取手机号
   */
  async getUserPhone() {
    const { data } = await MsAccountGateway.loadBasicValidationData()
    this.phone = data.boundDesensitizationPhone
    this.captcha = data.captcha
    this.basicToken = data.token
    return data
  }

  /**
   * 换绑手机
   * @param phone 电话
   * @param smsCode 短信验证码
   */
  async updateUserBindingPhoneNumber(phone: string, smsCode: string) {
    const { status } = await MsAccountGateway.bindPhone({ phone, smsCode, token: this.phoneToken })
    return status
  }

  /********* 绑定平台账号【微信】********** */

  /**
   * 加载校验码
   * @returns
   */
  async loadBindPlatformAccountBasicValidationData() {
    const { data, status } = await MsAccountGateway.loadBasicValidationData()
    this.captcha = data.captcha
    this.basicToken = data.token
    return status
  }

  /**
   * token为登录后URL带的token
   * @param request
   */
  async bindPlatformAccount(request: BindPlatformAccountRequest) {
    request.token = this.caToken
    const { status } = await MsAccountGateway.loginAndBindOpenPlatform(request)
    return status
  }

  /**
   * 加载忘记密码所需基础验证数据
   * @param accountId 账号ID
   */
  async loadStudentRetrievePasswordBasicValidationData() {
    const { data, status } = await MsAccountGateway.loadRetrievePasswordBasicValidationData({
      token: this.identityToken,
      accountId: this.accountId
    })
    if (status.isSuccess()) {
      this.basicToken = data.token
      this.captcha = data.captcha
      return {
        /**
         * 当前账户已绑定的数据脱敏的手机号
         */
        boundDesensitizationPhone: data.boundDesensitizationPhone,
        status
      }
    } else {
      return {
        /**
         * 当前账户已绑定的数据脱敏的手机号
         */
        boundDesensitizationPhone: '',
        status
      }
    }
  }
  /**
   * 重置密码
   */
  async resetStudentPassword(password: string) {
    const { status } = await MsAccountGateway.resetPasswordWithToken({
      token: this.phoneToken,
      accountId: this.accountId,
      password,
      authId: this.authId
    })
    return status
  }
  /********* 忘记密码 - 学员 ********** */

  /**
   * 学员确认身份
   * @param realName 真实姓名
   * @param identity 账户
   */
  async studentIdentify(realName: string, identity: string) {
    const { data, status } = await MsAccountGateway.studentIdentify({
      realName,
      identity
    })
    const newStatus = new ResponseStatus(200)
    if (data.code == '200') {
      this.accountId = data.accountId
      this.identityToken = data.token
      this.authId = data.authId
    } else {
      newStatus.code = parseInt(data.code)
    }
    return newStatus
  }

  /********* 忘记密码 - 集体 ********** */

  /**
   * 集体确认身份
   * @param realName 真实姓名
   * @param accountId 账户
   */
  async collectiveRegistrationAdministratorIdentify(realName: string, identity: string) {
    const { data, status } = await MsAccountGateway.collectiveRegistrationAdminIdentify({
      realName,
      identity
    })

    const newStatus = new ResponseStatus(200)
    if (data.code == '200') {
      this.accountId = data.accountId
      this.identityToken = data.token
      this.authId = data.authId
    } else {
      newStatus.code = parseInt(data.code)
      newStatus.message = data.msg
    }
    return newStatus
  }
  /********* 忘记密码 - 管理员 ********** */

  /**
   * 管理员确认身份
   * @param realName 真实姓名
   * @param accountId 账户
   */
  async platformManagementAdminIdentify(realName: string, identity: string) {
    const { data, status } = await MsAccountGateway.platformManagementAdminIdentify({
      realName,
      identity
    })

    const newStatus = new ResponseStatus(200)
    if (data.code == '200') {
      this.accountId = data.accountId
      this.identityToken = data.token
      this.authId = data.authId
    } else {
      newStatus.code = parseInt(data.code)
      newStatus.message = data.msg
    }
    return newStatus
  }

  /********* 获取所需基础验证数据（验证码） ********** */

  /**
   * 不需要登录
   * @returns
   */
  async loadBasicValidationData() {
    const { data, status } = await MsAccountGateway.loadBasicValidationData()
    this.captcha = data.captcha
    this.basicToken = data.token
    return status
  }

  /**
   * 需要登录
   * @returns
   */
  async loadBasicValidationDataWithBindPhone() {
    const { data, status } = await MsAccountGateway.loadBasicValidationDataWithBindPhone()
    if (status.isSuccess()) {
      this.captcha = data.captcha
      this.basicToken = data.token
    }
    return status
  }
  /********* 获取短信验证码 ********** */

  /**
   * 发送短信验证码(注册相关业务使用)
   */
  async sendSmsCodeByRegister(phone: string, captcha: string) {
    if (!phone) {
      // NOTE 待修改
      this.phone = undefined
    }
    const response = await MsAccountGateway.sendSmsCodeByRegister({ phone, token: this.caToken, captcha })
    return response
  }
  /**
   * 发送短信验证码(更换手机相关业务使用)
   */
  async sendSmsCodeByUpdatePhone(phone: string, accountId: string, captcha: string) {
    if (!phone) {
      this.phone = undefined
    }
    const response = await MsAccountGateway.sendSmsCodeByUpdatePhone({
      phone,
      token: this.caToken,
      accountId,
      captcha
    })
    return response
  }
  /**
   * 发送短信验证码(更换手机相关业务使用)
   * @param accountId 账户ID
   * @param accountId 账户ID
   */
  async sendSmsCodeByUpdatePhoneWithSmsValidToken(phone: string, accountId?: string) {
    if (!phone) {
      this.phone = undefined
    }
    const response = await MsAccountGateway.sendSmsCodeByUpdatePhoneWithSmsValidToken({
      phone,
      token: this.phoneToken,
      accountId
    })
    return response
  }
  /**
   * 发送短信验证码(修改密码相关业务使用)
   * @param accountId 账户ID
   * @param accountId 账户ID
   */
  async getSmsCode(phone: string, captcha: string, accountId?: string) {
    if (!phone) {
      this.phone = undefined
    }
    const response = await MsAccountGateway.sendSmsCodeByUpdatePwd({
      phone,
      token: this.caToken,
      accountId,
      captcha
    })
    return response
  }

  /**
   * 校验短信验证码
   */
  async checkSmsCode(smsCodeValidRequest: SmsCodeValidRequest) {
    smsCodeValidRequest.token = this.caToken
    const { data } = await MsAccountGateway.validSmsCode(smsCodeValidRequest)
    const status = new ResponseStatus(200, '')
    if (data.code == '200') {
      this.phoneToken = data.token
    } else {
      status.code = parseInt(data.code)
      status.message = data.msg
    }
    return status
  }

  /**
   * 校验验证码
   */
  async checkCaptcha(captcha: string) {
    const { data } = await MsAccountGateway.validCaptcha({ captcha, token: this.basicToken })
    const status = new ResponseStatus(200, '')
    if (data.code == '200') {
      this.caToken = data.token
    } else {
      this.captcha = data.refreshCaptcha
      status.code = parseInt(data.code)
      status.message = data.msg
    }
    return status
  }

  /**
   * 管理端请求换绑手机号
   */
  async bindPhoneByAdmin(phone: string, captcha: string, smsCode: string) {
    const request = new BindPhoneByAdminRequest()
    request.phone = phone
    request.captcha = captcha
    request.smsCode = smsCode
    request.token = this.caToken
    const { status } = await MsAccountGateway.bindPhoneByAdmin(request)
    return status
  }

  /**
   * 修改密码
   * 处理逻辑
   * 1. 新旧密码不能一致
   */
  async modifyPassword(newPassword: string, originalPassword: string) {
    if (newPassword === originalPassword) {
      return new ResponseStatus(500, '新密码不能跟旧密码一样')
    }
    const { status } = await MsAccountGateway.changePasswordByCurrent({
      originalPassword,
      newPassword
    })
    return status
  }
}

export default MutationBizAccount
