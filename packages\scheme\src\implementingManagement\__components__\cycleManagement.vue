<template>
  <el-card shadow="never" class="m-card">
    <!--条件查询-->
    <el-row :gutter="16" class="m-query f-mt10">
      <el-form :inline="true" label-width="auto">
        <el-col :span="6">
          <el-form-item label="期别名称">
            <el-input v-model="periodManageOption.params.name" clearable placeholder="请输入期别名称" />
          </el-form-item>
        </el-col>
        <el-col :span="6">
          <el-form-item label="期别编号">
            <el-input v-model="periodManageOption.params.no" clearable placeholder="请输入期别编号" />
          </el-form-item>
        </el-col>
        <el-col :span="4">
          <el-form-item>
            <el-button type="primary" @click="doQuery()">查询</el-button>
            <el-button @click="resetQuery()">重置</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <!--表格-->
    <el-table :data="periodManageOption.list" max-height="500px" class="m-table f-mt15" v-loading="loading">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="期别名称" min-width="200" show-overflow-tooltip>
        <template v-slot="{ row }">
          <div class="f-to">{{ row.name }}</div>
        </template>
      </el-table-column>
      <el-table-column label="期别编号" min-width="100">
        <template v-slot="{ row }">{{ row.no }}</template>
      </el-table-column>
      <el-table-column label="可报名人数" min-width="100">
        <template v-slot="{ row }">{{ row.applicantsNumber }}</template>
      </el-table-column>
      <el-table-column label="已报名人数" min-width="100">
        <template v-slot="{ row }">{{ row.signedNumber }}</template>
      </el-table-column>
      <el-table-column label="报名起止时间" min-width="200">
        <template v-slot="{ row }">
          <p><el-tag type="info" size="mini">起始</el-tag>{{ row.applicationData.begin || '-' }}</p>
          <p><el-tag type="info" size="mini">结束</el-tag>{{ row.applicationData.end || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="培训起止时间" min-width="200">
        <template v-slot="{ row }">
          <p><el-tag type="info" size="mini">起始</el-tag>{{ row.trainingTime.begin }}</p>
          <p><el-tag type="info" size="mini">结束</el-tag>{{ row.trainingTime.end }}</p>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" align="center" fixed="right">
        <template v-slot="{ row }">
          <el-button type="text" @click="enterAcademicAdministration(row.id)">教务管理</el-button>
          <el-button type="text" @click="reportQRcode(row)">报到二维码</el-button>
          <el-button type="text" @click="clockingQRcode(row, row.id)" v-if="row.isSetAttendanceConfig"
            >考勤二维码</el-button
          >
          <el-button type="text" @click="questionnaireQRcode(row)" v-if="row.hasQuestionnaire">问卷二维码</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"></hb-pagination>
    <ClockingQRcode ref="clockingQRcodeRef" :schemeId="schemeId"></ClockingQRcode>
    <QuestionnaireQRcode ref="questionnaireQRcodeRef"></QuestionnaireQRcode>
    <ReportQRcode ref="reportQRcodeRef"></ReportQRcode>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Prop } from 'vue-property-decorator'
  import ClockingQRcode from './clockingQRcode.vue'
  import QuestionnaireQRcode from './questionnaireQRcode.vue'
  import ReportQRcode from './reportQRcode.vue'
  import PeriodManage from '@api/service/management/implement/PeriodManage'
  import { UiPage } from '@hbfe/common'
  import PeriodProcess from '@api/service/management/implement/models/PeriodProcess'
  import PeriodManageParam from '@api/service/management/implement/models/PeriodManageParam'
  @Component({
    components: {
      ClockingQRcode,
      QuestionnaireQRcode,
      ReportQRcode
    }
  })
  export default class extends Vue {
    @Ref('clockingQRcodeRef') clockingQRcodeRef: ClockingQRcode
    @Ref('questionnaireQRcodeRef') questionnaireQRcodeRef: QuestionnaireQRcode
    @Ref('reportQRcodeRef') reportQRcodeRef: ReportQRcode
    @Prop({
      type: String,
      default: ''
    })
    schemeId: string
    /**
     * 培训形式
     */
    @Prop({
      type: String,
      default: ''
    })
    trainingMode: string
    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }
    // todo
    form = [{}]
    tableData = [{}]
    input = ''
    select = {}
    // 分页
    page = new UiPage(this.doQuery, this.doQuery)
    // 加载动画
    loading = false

    // 期别管理配置模型
    periodManageOption: PeriodManage = new PeriodManage('')

    async created() {
      this.periodManageOption = new PeriodManage(this.schemeId)
      await this.doQuery()
    }

    /**
     * 查询方法
     */
    async doQuery() {
      try {
        this.loading = true
        await this.periodManageOption.queryList(this.page)
        console.info('🚀 ~ file:cycleManagement method:created line:117 -----', this.periodManageOption)

        this.loading = false
      } catch (e) {
        console.log(e)
      } finally {
        //
      }
    }

    resetQuery() {
      this.periodManageOption.params = new PeriodManageParam()
      this.page.currentChange(1)
    }

    /**
     * 报到二维码弹窗
     */
    reportQRcodeDialog(id: string) {
      this.reportQRcodeRef.openDialog = true
    }
    /**
     * 考勤二维码弹窗
     */
    async clockingQRcode(row: PeriodProcess, id: string) {
      if (!row.attendanceSetted) {
        this.$message.warning('线下期别考勤还未设置，请前往训前实施设置！')
        return
      }
      this.clockingQRcodeRef.open(row.qrCodeManage, id)
    }

    async reportQRcode(row: PeriodProcess) {
      if (!row.reportSetted) {
        this.$message.warning('线下期别报到规则还未设置，请前往训前实施设置！')
        return
      }
      await row.getPeriodConfigById()
      console.group(
        '%c%s',
        'padding:3px 60px;color:#6c6c6c;background-image: linear-gradient(#800080, #C71585)',
        '调试输出'
      )
      console.log(row.schemePeriodConfig)
      console.count('输出次数')
      console.groupEnd()
      this.reportQRcodeRef.open(row)
    }

    /**
     * 问卷二维码弹窗
     */
    async questionnaireQRcode(row: PeriodProcess) {
      await row.getQuestionnaireInfo()
      this.questionnaireQRcodeRef.open(row.id, row.questionnaireList, row.schemeId, this.trainingMode)
    }

    /**
     * 进入教务管理
     */
    enterAcademicAdministration(periodId: string) {
      this.$router.push(`/training/scheme/academicAdministration/${this.schemeId}/${periodId}`)
    }
  }
</script>
