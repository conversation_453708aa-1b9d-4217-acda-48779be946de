<template>
  <el-card shadow="never" class="m-card f-mb15 is-bg">
    <div class="f-p15">
      <el-row type="flex" justify="center" class="width-limit">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form ref="onlineApplyForm" :model="collectiveSignUp" label-width="180px" class="m-form f-mt20">
            <el-form-item label="线上集体报名入口：" required>
              <el-switch v-model="collectiveSignUp.enable" active-text="开启" inactive-text="关闭" class="m-switch" />
              <el-tooltip effect="dark" placement="right" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-co f-ml15"></i>
                <div slot="content">当前配置仅针对网校，专题线上集体报名入口配置请前往专题编辑页面。</div>
              </el-tooltip>
            </el-form-item>
            <el-form-item v-if="collectiveSignUp.enable" label="线上集体报名入口图片：" required>
              <el-switch
                active-text="开启"
                v-model="collectiveSignUp.onlineEnterPhotoEnable"
                inactive-text="关闭"
                class="m-switch"
              />
              <el-tooltip effect="dark" placement="right" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-co f-ml15"></i>
                <div slot="content">
                  当前配置仅针对网校门户线上集体报名入口图片的显示，专题线上集体报名请前往专题编辑页面。
                </div>
              </el-tooltip>
            </el-form-item>
            <el-form-item
              v-if="collectiveSignUp.onlineEnterPhotoEnable && collectiveSignUp.enable"
              label="线上集体报名入口图片："
            >
              <cropper-img-upload
                v-model="collectiveSignUp.onlineEnterPhoto.url"
                :dialogStyleOpation="{
                  width: `${picSize[0] + 100}px`,
                  height: '240px'
                }"
                :ratioArr="[`${picSize[0]}:${picSize[1]}`]"
                :initWidth="picSize[0]"
                title="上传线上集体报名入口图片"
                button-text="上传线上集体报名入口图片"
                :is-long-pic="true"
                reminder-text="只支持JPG、PNG、GIF"
                :has-preview="false"
              >
                <div slot="tip" class="el-upload__tip">
                  <i class="el-icon-warning"></i>
                  <span class="txt">
                    上传线上集体报名入口图片，尺寸：宽度 {{ picSize[0] }} px ， 高度
                    {{ picSize[1] }} px，不上传则显示模板默认图片。
                    <i class="f-link" @click="handlePictureExample">示例图片</i>
                  </span>
                  <!--示例图片弹窗-->
                  <el-dialog :visible.sync="pictureVisible" width="1100px" class="m-dialog-pic">
                    <!--分别读取对应的默认图片-->
                    <img :src="exampleImg" alt="" />
                  </el-dialog>
                </div>
              </cropper-img-upload>
            </el-form-item>
            <el-form-item label="线上集体报名模板：" required v-if="collectiveSignUp.enable">
              <min-upload-file v-model="hbFileUploadResponse" :file-type="1">
                <el-button slot="trigger" type="primary" plain>点击上传模板</el-button>
              </min-upload-file>
              <div class="el-upload__tip f-pt5">
                <div class="txt"><i class="f-link" @click.stop="downloadTemplate">下载示例模版</i></div>
              </div>
            </el-form-item>
            <el-form-item label="查看报名班级链接：" required v-if="collectiveSignUp.enable">
              <template>
                {{ collectiveSignUp.signUpClassUrl }}
                <hb-copy :content="collectiveSignUp.signUpClassUrl"></hb-copy>
              </template>
              <!--                  <template>
              <div>
                https:tar.59iedu.com
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
                  <div slot="content">点击复制链接</div>
                </el-tooltip>
              </div>
            </template>-->
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="doCancel">取消</el-button>
              <el-button type="primary" @click="validForm" :loading="loading">保存</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <give-up-dialog :give-up-model="uiConfig.giveUpModel" @callBack="resetData"></give-up-dialog>
    </div>
  </el-card>
</template>

<script lang="ts">
  import { CollectiveSignUpTypeEnum } from '@api/service/common/enums/online-school-config/CollectiveSignUpType'
  import TemplateItem from '@api/service/common/template-school/TemplateItem'
  import MutationCollectiveSignUp from '@api/service/management/online-school-config/functionality-setting/mutation/MutationCollectiveSignUp'
  import OnlineCollectSignUpVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/CollectiveSignUp/OnlineCollectSignUpVo'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import MinUploadFile from '@hbfe/jxjy-admin-components/src/min-upload-file.vue'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import CropperImgUpload from '@hbfe/jxjy-admin-platform/src/function/components/cropper-img-upload.vue'
  import UnitApplyPicSizeTypeModel from '@hbfe/jxjy-admin-platform/src/function/vuex/UnitApplyPicSizeTypeModel'
  import { bind, debounce } from 'lodash-decorators'
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'

  @Component({
    components: { MinUploadFile, GiveUpDialog, CropperImgUpload }
  })
  export default class extends Vue {
    @Ref('onlineApplyForm') onlineApplyForm: any
    //文件上传之后的回调参数
    hbFileUploadResponse = new HBFileUploadResponse()
    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }
    mutationCollectiveSignUp: MutationCollectiveSignUp =
      OnlineSchoolConfigModule.mutationFunctionalitySettingFactory.collectiveSignUp(CollectiveSignUpTypeEnum.ONLINE)
    collectiveSignUp = new OnlineCollectSignUpVo()
    /**
     * 示例图片显示
     */
    pictureVisible = false
    /**
     * 加载状态
     */
    loading = false
    /**
     * 模板
     */
    @Prop({
      required: true,
      default: () => {
        return new TemplateItem()
      }
    })
    TemplateModuleObj: TemplateItem
    /**
     * 获取图片尺寸
     */
    get picSize() {
      return UnitApplyPicSizeTypeModel.isSingle
        ? this.TemplateModuleObj.singleOnlineCollectSize
        : this.TemplateModuleObj.doubleOnlineCollectSize
    }
    /**
     * 示例图片
     */
    get exampleImg() {
      const src = UnitApplyPicSizeTypeModel.isSingle
        ? this.TemplateModuleObj.singleOnlineExampleImgSrc
        : this.TemplateModuleObj.doubleOnlineExampleImgSrc
      return require('@design/admin/assets/images/demo-enter-pic/' + src)
    }

    @Watch('mutationCollectiveSignUp.collectiveSignUp.enable')
    onOnlineCollectiveEntryChange(val: boolean) {
      this.collectiveSignUp.onlineEnterPhotoEnable = val
    }
    /**
     * 监听入口状态，更改图片尺寸
     */
    @Watch('collectiveSignUp.onlineEnterPhotoEnable')
    onEntryStatusChange(val: boolean) {
      UnitApplyPicSizeTypeModel.setOnlinePicEnable(val)
    }
    /**
     * 示例图片
     */
    handlePictureExample() {
      this.pictureVisible = true
    }
    //下载模板
    @debounce(1000)
    @bind
    async downloadTemplate() {
      const link = document.createElement('a')
      const templatePath = await this.mutationCollectiveSignUp.getTemplate()
      const resolver = this.$router.resolve({
        name: templatePath
      })

      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      link.setAttribute('download', '集体报名表模板')
      document.body.appendChild(link)
      link.click()
      link.remove()
    }

    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
    }
    @debounce(200)
    @bind
    async doSave() {
      if (this.mutationCollectiveSignUp.collectiveSignUp.enable) {
        if (
          this.mutationCollectiveSignUp.collectiveSignUp.enable &&
          (!this.hbFileUploadResponse.url || this.hbFileUploadResponse.url == '')
        ) {
          this.$message.warning('请上传线上集体报名模板')
          return
        }
        this.loading = true
      }
      if (this.hbFileUploadResponse.url) {
        this.mutationCollectiveSignUp.collectiveSignUp.templatePath = this.hbFileUploadResponse.url
        this.mutationCollectiveSignUp.collectiveSignUp.templateName = this.hbFileUploadResponse.fileName
      }

      const res = await this.mutationCollectiveSignUp.doSave()
      if (res.isSuccess()) {
        this.$message.success('保存成功')
        UnitApplyPicSizeTypeModel.setOnlinePicEnable(this.collectiveSignUp.onlineEnterPhotoEnable)
        UnitApplyPicSizeTypeModel.setOnlinePicExist(!!this.collectiveSignUp.onlineEnterPhoto.url)
        const change = UnitApplyPicSizeTypeModel.isOnlinePicChange
        await this.getOnlineApplyInfo()
        if (change) {
          this.$confirm('线下集体报名入口图片尺寸变更，是否立即前往调整？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            this.$emit('update:activeName', 'second')
          })
        }
      } else {
        this.$message.error('保存失败')
      }
      this.loading = false
    }

    validForm() {
      this.onlineApplyForm.validate((valid: boolean) => {
        if (valid) {
          this.doSave()
        }
      })
    }

    async resetData() {
      //重新获取数据
      await this.getOnlineApplyInfo()
    }

    async getOnlineApplyInfo() {
      //获取线上报名信息
      const res = await this.mutationCollectiveSignUp.queryDetail()
      if (res.isSuccess()) {
        //console.log(this.mutationCollectiveSignUp.collectiveSignUp)
        this.hbFileUploadResponse = new HBFileUploadResponse()
        this.hbFileUploadResponse.url = this.mutationCollectiveSignUp.collectiveSignUp.templatePath
        this.hbFileUploadResponse.fileName = this.mutationCollectiveSignUp.collectiveSignUp.templateName
        this.collectiveSignUp = this.mutationCollectiveSignUp.collectiveSignUp as OnlineCollectSignUpVo
        UnitApplyPicSizeTypeModel.setOriginOnlinePicEnable(this.collectiveSignUp.onlineEnterPhotoEnable)
        UnitApplyPicSizeTypeModel.setOnlinePicExist(!!this.collectiveSignUp.onlineEnterPhoto.url)
      }
    }

    async created() {
      await this.getOnlineApplyInfo()
    }
  }
</script>
