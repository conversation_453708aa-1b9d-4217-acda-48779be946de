<template>
  <el-drawer title="问卷二维码" :visible.sync="openDialog" size="480px" custom-class="m-drawer">
    <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
      通过使用微信扫一扫二维码打开问卷
    </el-alert>
    <div class="drawer-bd" v-for="(item, index) in questionnaireList" :key="index">
      <div class="m-view-qrcode">
        <div class="item">
          <div class="content" :id="item.id">
            <div class="tit">{{ item.name }}</div>
            <div class="cate">{{ getType(item) }}</div>
            <div class="code"><img :src="item.qrCode" class="u-qr-code" /></div>
          </div>
          <div class="op">
            <el-button type="primary" round size="medium" @click="downloadPoster(item)">保存至本地</el-button>
          </div>
        </div>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button type="primary" @click="openDialog = false">关闭</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import QuestionnaireImplementItem from '@api/service/management/implement/models/QuestionnaireImplementItem'
  import * as htmlToImage from 'html-to-image'
  import CryptUtil from '@api/service/common/crypt/CryptUtil'
  import QRCode from 'qrcode'
  import { EnterTypeEnums } from '@api/service/common/auth-status-tool/enums/EnterTypeEnums'
  import { QuestionnaireTypeEnum } from '@api/service/management/resource/question-naire/enums/QuestionnaireType'
  @Component({})
  export default class extends Vue {
    openDialog = false
    questionnaireList: Array<QuestionnaireImplementItem> = new Array<QuestionnaireImplementItem>()
    qrcode = 'https://www.baidu.com/' // url  el-image  src=qrcode
    // 方案id
    schemeId = ''
    // 二维码枚举赋值
    EnterTypeEnums = EnterTypeEnums
    // 期别id
    periodId = ''
    trainingMode = ''

    async open(id: string, list: Array<QuestionnaireImplementItem>, schemeId: string, trainingMode: string) {
      this.openDialog = true
      this.questionnaireList = list
      this.schemeId = schemeId
      this.periodId = id
      this.trainingMode = trainingMode

      // 循环list 生成二维码
      this.questionnaireList.forEach(async (item) => {
        item.qrCode = await this.getQrcode(item)
      })
    }

    // 下载二维码
    downloadPoster(item: QuestionnaireImplementItem) {
      this.domToPic(item.id, {
        width: 1140,
        height: 1320,
        name: this.qrCodeName(item)
      })
    }
    // 下载方法
    domToPic(domId: string, config = { width: 2105, height: 3035, name: `问卷二维码` }) {
      const node = document.getElementById(domId)
      htmlToImage
        .toPng(node)
        .then((pngUrl: string) => {
          const img = new Image()
          img.src = pngUrl
          img.onload = () => {
            const canvas = document.createElement('canvas')
            const ctx = canvas.getContext('2d')
            canvas.width = config.width
            canvas.height = config.height
            ctx.drawImage(img, 0, 0, canvas.width, canvas.height)
            canvas.toBlob((blob: Blob) => {
              const url = window.URL.createObjectURL(blob)
              const link = document.createElement('a')
              link.href = url
              link.download = `${config.name}.jpeg`
              document.body.appendChild(link)
              link.click()
              window.URL.revokeObjectURL(url)
            })
          }
        })
        .catch(function (error: any) {
          console.error('oops, something went wrong!', error)
        })
    }

    // 获取二维码
    async getQrcode(item: QuestionnaireImplementItem) {
      // 问卷入参 方案id 期别id 目标路径：/pages/training/question-naire
      const origin = window.location.origin
      const accessPath =
        origin + `/h5/#/pages/transfer/qr_transfer?entryType=${EnterTypeEnums.questionnaire}&nextAddress=`
      const storageURL = await CryptUtil.encryptStr(
        `/pages/training/question/cover?schemeId=${this.schemeId}&periodId=${this.periodId}&questionnaireId=${item.id}&isNeedSub=true&listTrainingMode=${this.trainingMode}&learningId=${item.learningId}`
      )
      const qrcode = await QRCode.toDataURL(accessPath + storageURL)
      return qrcode
    }
    // 二维码名称
    qrCodeName(item: QuestionnaireImplementItem) {
      return item.name || '问卷二维码'
    }

    // 获取问卷类型
    get getType() {
      return (item: QuestionnaireImplementItem) => {
        if (item.type === QuestionnaireTypeEnum.all) {
          return '全部问卷'
        } else if (item.type === QuestionnaireTypeEnum.ordinary) {
          return '普通问卷'
        } else if (item.type === QuestionnaireTypeEnum.scale) {
          return '量表问卷'
        } else {
          return '未知问卷'
        }
      }
    }
  }
</script>
