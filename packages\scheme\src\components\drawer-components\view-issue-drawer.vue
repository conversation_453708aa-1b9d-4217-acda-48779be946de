<template>
  <el-drawer
    title="查看期别"
    :visible.sync="showDrawer"
    size="900px"
    custom-class="m-drawer scroll-anchor"
    destroy-on-close
  >
    <div class="drawer-bd">
      <div class="m-side-positioner">
        <div
          class="item"
          :class="{ 'z-cur': activeAnchor == item.id }"
          v-for="item in sideList"
          :key="item.id"
          @click="scrollToSection(item.id)"
        >
          <div class="dot"></div>
          <div class="tit">{{ item.text }}</div>
        </div>
      </div>
      <div style="padding-right: 160px">
        <el-row :gutter="16">
          <el-form ref="form" :model="selectIssue" label-width="180px" class="m-form f-mt10">
            <el-col :span="19">
              <el-form-item label="期别名称：">
                {{ selectIssue.issueName }}
              </el-form-item>
              <el-form-item label="期别编号：">
                {{ selectIssue.issueNo }}
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <div class="m-add-period">
          <div class="section" id="issue-base-info">
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">基础信息</span>
            </div>
            <el-form ref="form" :model="selectIssue" label-width="180px" class="m-form f-mt10">
              <el-form-item label="培训报到时段："
                >{{ dateFormat(selectIssue.checkDateRange.startDate) }} 至
                {{ dateFormat(selectIssue.checkDateRange.endDate) }}
              </el-form-item>
              <el-form-item label="开放报名人数：">{{ selectIssue.openRegistrationNum }}人</el-form-item>
              <el-form-item
                label="已报名人数："
                v-if="selectIssue.registeredNumDisplayType === RegisteredNumDisplayTypeEnum.read_enrollment"
                >读取实际报名人数
              </el-form-item>
              <el-form-item label="已报名人数：" v-else
                >固定显示数值{{ selectIssue.fixedRegisteredNum }}人
              </el-form-item>
              <el-form-item label="培训时段：">
                <template v-if="selectIssue.trainingDateRange.startDate || selectIssue.trainingDateRange.endDate">
                  {{ dateFormat(selectIssue.trainingDateRange.startDate) }} 至
                  {{ dateFormat(selectIssue.trainingDateRange.endDate) }}
                </template>
                <template v-else>——</template>
              </el-form-item>
              <el-form-item label="期别学时：">{{ totlePeriods }}</el-form-item>
              <el-form-item label="培训地点：">{{ selectIssue.trainingPointName }}</el-form-item>
            </el-form>
          </div>
          <div class="section" id="issue-sign-up-info">
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">报名信息</span>
            </div>
            <el-form ref="form" :model="selectIssue" label-width="180px" class="m-form f-mt10">
              <el-form-item label="展示在门户："
                >{{ selectIssue.isShowInPortal ? '是' : '' }}展示在学员门户</el-form-item
              >
              <el-form-item label="展示用户："
                >学员门户{{ selectIssue.visibleChannelList.includes(1) ? '' : '不' }}可见</el-form-item
              >
              <el-form-item label="开放学员报名："
                >{{ selectIssue.isEnableStudentEnroll ? '' : '不' }}开放</el-form-item
              >
              <el-form-item label="开启报名时间：">
                {{ selectIssue.registerBeginTime || '立即开启' }} 至
                {{ selectIssue.registerEndTime || '无关闭时间' }}</el-form-item
              >
              <el-form-item label="开启住宿信息采集：">
                {{ selectIssue.isOpenAccommodationInfoCollect ? '开启' : '不开启' }}
              </el-form-item>
              <el-form-item label="住宿信息采集须知：">
                <el-input
                  type="textarea"
                  :rows="6"
                  disabled
                  v-model="selectIssue.accommodationInfoCollectNotice"
                  placeholder="请输入住宿信息采集须知内容..."
                >
                </el-input>
              </el-form-item>
            </el-form>
          </div>
          <div class="section" id="issue-assessment-requirements">
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">考核要求</span>
            </div>
            <el-form ref="form" :model="selectIssue" label-width="180px" class="m-form f-mt10">
              <el-form-item label="是否开启报到：">{{ selectIssue.isOpenCheck ? '' : '不' }}开启</el-form-item>
              <el-form-item label="是否开启结业测试："
                >{{ selectIssue.isOpenGraduationTest ? '' : '不' }}开启</el-form-item
              >
              <el-form-item label="是否开启考勤：">{{ selectIssue.isOpenAttendance ? '' : '不' }}开启</el-form-item>
              <el-form-item label="考勤考核要求：" v-if="selectIssue.isOpenAttendance"
                >{{ selectIssue.attendanceRate }}%</el-form-item
              >
            </el-form>
          </div>
          <div class="section" id="issue-contact-info">
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">联络信息</span>
            </div>
            <el-form ref="form" :model="selectIssue" label-width="180px" class="m-form f-mt10">
              <el-form-item label="班主任：">{{ selectIssue.headTeacherName }}</el-form-item>
              <el-form-item label="班主任联系电话：">{{ selectIssue.headTeacherPhone }}</el-form-item>
              <el-form-item label="酒店联系人：">{{ selectIssue.hotelContactsName }}</el-form-item>
              <el-form-item label="酒店联系人电话：">{{ selectIssue.hotelContactsPhone }}</el-form-item>
            </el-form>
          </div>
          <div class="section" id="issue-notice">
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">学员须知</span>
            </div>
            <div class="f-p20">
              <el-input type="textarea" :rows="6" v-model="selectIssue.notice" disabled placeholder="请输入学员须知">
              </el-input>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button type="primary" @click="showDrawer = false">取消</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import { RegisteredNumDisplayTypeEnum } from '@api/service/common/scheme/enum/RegisteredNumDisplayType'
  import { IssueTrainingDateTypeEnum } from '@api/service/common/scheme/enum/IssueTrainingDateType'
  @Component({
    computed: {
      RegisteredNumDisplayTypeEnum() {
        return RegisteredNumDisplayTypeEnum
      }
    }
  })
  export default class extends Vue {
    @Ref('scrollableDiv') scrollableDiv: HTMLElement
    @PropSync('viewIssueDrawer', { type: Boolean, default: false }) showDrawer: boolean
    @Prop({ type: Object, default: () => new IssueConfigDetail() }) selectIssue: IssueConfigDetail
    scrollKey = true
    /**
     * 当前激活锚点
     */
    activeAnchor = 'issue-base-info'

    get totlePeriods() {
      if (this.selectIssue.issueTrainingDateType === IssueTrainingDateTypeEnum.custom) {
        return this.selectIssue.periods || '-'
      }
      return (
        this.selectIssue.issueCourseList.reduce((total, item) => {
          return total + (item.coursePeriod || 0)
        }, 0) || '-'
      )
    }
    /**
     * 侧边标题
     */
    sideList = [
      { id: 'issue-base-info', text: '基础信息' },
      { id: 'issue-sign-up-info', text: '报名信息' },
      { id: 'issue-assessment-requirements', text: '考核要求' },
      { id: 'issue-contact-info', text: '联络信息' },
      { id: 'issue-notice', text: '学员须知' }
    ]

    scrollToSection(id: string): void {
      this.activeAnchor = id
      const element = document.getElementById(id)
      if (element) {
        element.scrollIntoView()
      }
    }
    /**
     * 时间转换
     */
    dateFormat(date: string) {
      return date ? this.$moment(date).format('YYYY-MM-DD') : ''
    }
    mounted() {
      // 监听滚动事件
      this.$nextTick(() => {
        document.querySelector('.scroll-anchor .el-drawer__body').addEventListener('scroll', this.onScroll)
      })
    }
    // 滚动监听器
    onScroll() {
      // 获取所有锚点元素
      const navContents = document.querySelectorAll('.m-add-period .section')
      // 所有锚点元素的 offsetTop
      const offsetTopArr: any = []
      navContents.forEach((item: any) => {
        offsetTopArr.push(item.offsetTop)
      })
      // 获取当前文档流的 scrollTop
      const scrollTop = document.querySelector('.scroll-anchor .el-drawer__body').scrollTop + 80
      // 定义当前点亮的导航下标
      let navIndex = 0
      for (let n = 0; n < offsetTopArr.length; n++) {
        // 如果 scrollTop 大于等于第 n 个元素的 offsetTop 则说明 n-1 的内容已经完全不可见
        // 那么此时导航索引就应该是 n 了
        if (scrollTop >= offsetTopArr[n]) {
          navIndex = n
        }
      }
      // 把下标赋值给 vue 的 data
      if (this.scrollKey) {
        this.activeAnchor = navContents[navIndex].id
      } else {
        this.scrollKey = true
      }
    }
  }
</script>
