<template>
  <el-drawer
    :title="title"
    :visible.sync="showDrawer"
    @open="onOpen"
    size="90%"
    custom-class="m-drawer"
    destroy-on-close
    :wrapperClosable="false"
  >
    <div class="drawer-bd">
      <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb10" v-if="isUpdate">
        <p>注：1、课程开始上课后或课程已结束，不允许修改课程授课时间</p>
      </el-alert>
      <div class="f-mb10">
        <el-button type="primary" v-if="!isUpdate" @click="openBatchDrawer">批量创建课程</el-button>
      </div>
      <!--表格-->
      <el-form ref="form" :model="formData" label-width="120px" class="operateFrom" v-loading="loading">
        <el-table
          stripe
          :data="selectIssue.issueCourseList"
          max-height="650px"
          class="m-table f-mt10"
          :header-cell-class-name="addHeaderCellClassName"
        >
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="课程名称" min-width="410">
            <template v-slot="{ row }">
              <el-form-item
                :prop="'name_' + row.id + '.courseName'"
                :rules="[{ required: true, message: '课程名称不能为空', trigger: 'blur' }]"
              >
                <el-input
                  placeholder="请输入课程名称"
                  v-model="row.courseName"
                  style="margin-top: 20px; width: 390px"
                ></el-input>
                <!-- @blur="validateCell(row, 'name')" -->
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="授课教师" min-width="180" align="center">
            <template v-slot="{ row }">
              <el-form-item
                :prop="'name_' + row.id + '.teacherName'"
                :rules="[{ required: true, message: '授课老师不能为空', trigger: 'blur' }]"
              >
                <el-input placeholder="请输入教师姓名" v-model="row.teacherName" style="margin-top: 20px"></el-input>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="教师职称" min-width="180" align="center">
            <template v-slot="{ row }">
              <el-input
                placeholder="请输入教师职称"
                v-model="row.teacherPositionTile"
                style="margin-bottom: 20px; margin-top: 20px"
              ></el-input>
            </template>
          </el-table-column>
          <el-table-column label="教师所在单位" min-width="340" align="center">
            <template v-slot="{ row }">
              <el-input placeholder="请输入教师所在单位" style="width: 320px" v-model="row.teacherUnitName"></el-input>
            </template>
          </el-table-column>
          <el-table-column label="课程学时" min-width="250" align="center">
            <template v-slot="{ row }">
              <el-form-item
                :prop="'name_' + row.id + '.coursePeriod'"
                :rules="[{ required: true, trigger: 'blur', validator: validateCoursePeriod }]"
              >
                <el-input-number
                  :controls="false"
                  placeholder="请输入课程学时"
                  v-model="row.coursePeriod"
                  style="margin-top: 20px; width: 130px"
                ></el-input-number>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="授课日期" min-width="250" align="center">
            <template v-slot="{ row }">
              <el-form-item
                :prop="'name_' + row.id + '.teachingDate'"
                :rules="[
                  { required: true, message: '授课日期不能为空', trigger: 'blur' },
                  { validator: validateTeachingDate, trigger: 'blur' }
                ]"
              >
                <el-date-picker
                  :disabled="(isUnmodifiable(row.id) || hasDisabled) && !skipTime"
                  style="margin-top: 20px"
                  type="date"
                  value-format="yyyy-MM-dd"
                  placeholder="请选择日期"
                  v-model="row.teachingDate"
                  @change="
                    () => {
                      teachingDateTimeChange(row, 'teachingDate')
                    }
                  "
                  :picker-options="{
                    disabledDate: (time) => {
                      if (skipTime) {
                        return
                      }
                      return (
                        time.getTime() < Date.now() - (row.operationType === OperationTypeEnum.update ? 0 : 86400000)
                      ) // 禁止选择今天之前的日期
                    }
                  }"
                >
                </el-date-picker>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="授课开始时间" min-width="250" align="center">
            <template v-slot="{ row }">
              <el-form-item
                :prop="'name_' + row.id + '.courseBeginTime'"
                :rules="[
                  { required: true, message: '授课开始时间不能为空', trigger: 'blur' },
                  { validator: validateBeginTime, trigger: 'blur' }
                ]"
              >
                <el-time-picker
                  style="margin-top: 20px"
                  :value-format="'HH:mm'"
                  :disabled="(isUnmodifiable(row.id) || hasDisabled) && !skipTime"
                  :picker-options="{ format: 'HH:mm' }"
                  placeholder="请选择时间"
                  @change="
                    () => {
                      teachingDateTimeChange(row, 'courseBeginTime')
                    }
                  "
                  v-model="row.courseBeginTime"
                >
                </el-time-picker>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="授课结束时间" min-width="250" align="center">
            <template v-slot="{ row }">
              <el-form-item
                :prop="'name_' + row.id + '.courseEndTime'"
                :rules="[
                  { required: true, message: '授课结束时间不能为空', trigger: 'blur' },
                  { validator: validateEndTime, trigger: 'blur' }
                ]"
              >
                <el-time-picker
                  style="margin-top: 20px"
                  :value-format="'HH:mm'"
                  :picker-options="{ format: 'HH:mm' }"
                  :disabled="(isUnmodifiable(row.id) || hasDisabled) && !skipTime"
                  placeholder="请选择时间"
                  @change="
                    () => {
                      teachingDateTimeChange(row, 'courseEndTime')
                    }
                  "
                  v-model="row.courseEndTime"
                >
                </el-time-picker>
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column label="课程时长(分)" min-width="180" align="center">
            <template v-slot="{ row }">
              {{ row.courseTimeLength / 60 }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template v-slot="{ row, $index }">
              <el-button
                type="text"
                icon="el-icon-remove-outline"
                :disabled="isUnmodifiable(row.id) || hasDisabled"
                @click="deleteCourse($index)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </el-form>
      <div class="m-add-row f-mt10" @click="addCourse"><i class="el-icon-circle-plus-outline f-mr5"></i>新增一行</div>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="showDrawer = false">取消</el-button>
      <el-button type="primary" @click="save" :loading="saveLoading">确定并保存</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import IssueCourseDetail from '@api/service/common/scheme/model/IssueCourseDetail'
  import SchemeBaseInfo from '@api/service/common/scheme/model/SchemeBaseInfo'
  import { Form } from 'element-ui'
  import { Component, Inject, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
  import { cloneDeep } from 'lodash'
  import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
  import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
  import ServiceTime from '@api/service/common/service-time/ServiceTime'
  import GetIssueCourseListUnmodifiableIdsParam from '@api/service/management/train-class/offlinePart/model/GetIssueCourseListUnmodifiableIdsParam'
  import { debounce } from 'lodash-decorators'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  @Component
  export default class extends Vue {
    @Ref('form') form: Form
    @PropSync('operateCourse', { type: Boolean, default: false }) showDrawer: boolean
    @PropSync('batchCourseDrawer', { type: Boolean, default: true }) showBatchDrawer: boolean
    @Prop({ type: Object, default: () => new IssueConfigDetail() }) selectIssue: IssueConfigDetail
    @Prop({ type: Object, default: () => new SchemeBaseInfo() }) trainClassBaseInfo: SchemeBaseInfo
    /**
     * 是否冲算或智能学习
     */
    @Prop({
      type: Boolean,
      default: false
    })
    hasDisabled: boolean
    @Prop(String) title: string
    @Inject() getLearningTypeModelCopy: () => LearningType
    @Prop({
      type: Boolean,
      default: false
    })
    skipTime: boolean

    formData: any = {}
    loading = false
    saveLoading = false
    OperationTypeEnum = OperationTypeEnum

    /**
     * 修改前课程列表
     */
    issueCourseListCopy: IssueCourseDetail[] = []
    /**
     * 首门课程
     * 有打卡记录才有值
     */
    firstCourse: IssueCourseDetail = null
    /**
     * 打卡
     */
    existCheckInRecord: string[] = []
    /**
     * 时间
     */
    existStarted: string[] = []

    /**
     * 获取服务器时间响应式数据
     */
    serviceTime = ServiceTime

    /**
     * 校验口实例对象
     */
    mutationCreateTrainClassCommodity = new MutationCreateTrainClassCommodity()
    /**
     * 不可修改的课程id列表
     */
    get unmodifiableIds() {
      return [...this.existCheckInRecord, ...this.existStarted]
    }

    /**
     * 是否已发布的期别
     */
    get isUpdate() {
      return this.selectIssue.operationType === OperationTypeEnum.update
    }
    /**
     * 课程是否不可操作
     */
    get isUnmodifiable() {
      return (id: string) => this.unmodifiableIds.includes(id)
    }
    /**
     * 校验某一项
     */
    validateCell(row: IssueCourseDetail, field: any) {
      const prop = 'name_' + row.id + '.' + field
      this.form.validateField(prop, (errorMessage) => {
        if (errorMessage) {
          console.log(`校验失败：${field} - ${row.id}`)
        } else {
          console.log(`校验通过：${field} - ${row.id}`)
        }
      })
    }
    teachingDateTimeChange(row: IssueCourseDetail, key: string) {
      if (
        row.operationType === OperationTypeEnum.update &&
        row[key] !== this.issueCourseListCopy.find((item) => item.id === row.id)[key].slice(0, 5)
      ) {
        row.isModified = true
      }
    }
    /**
     * 移除某项校验结果
     */
    removeCellValidate(row: any, field: any) {
      const prop = 'name_' + row.id + '.' + field
      this.form.clearValidate(prop)
    }
    addHeaderCellClassName(tableItem: any) {
      if (
        tableItem.columnIndex !== 0 &&
        tableItem.columnIndex !== 3 &&
        tableItem.columnIndex !== 4 &&
        tableItem.columnIndex !== 9 &&
        tableItem.columnIndex !== 10
      )
        return 'operateCourseTableHeard'
    }
    /**
     * 校验全部
     */
    validateAllRows() {
      let finalValid = true
      this.selectIssue.issueCourseList.forEach((row) => {
        if (!this.validateRow(row)) {
          finalValid = false
        }
      })
      return finalValid
    }

    /**
     * 校验整行
     */
    validateRow(row: any) {
      //需要校验的字段
      const validateName: string[] = [
        'courseName',
        'teacherName',
        'coursePeriod',
        'teachingDate',
        'courseBeginTime',
        'courseEndTime'
      ]
      const validate = validateName.map((item) => {
        let validateItem = true
        this.form.validateField(`name_${row.id}.${item}`, (errorMessage) => {
          if (errorMessage) {
            console.log(`${item}校验失败：${row.id}`)
            validateItem = false
          } else {
            console.log(`${item}校验通过：${row.id}`)
          }
        })
        return validateItem
      })
      return validate.every((item) => item)
    }
    /**
     * 添加课程
     */
    addCourse() {
      if (this.hasDisabled) {
        return
      }
      if (
        this.isUpdate &&
        this.issueCourseListCopy.length &&
        this.selectIssue.issueCourseList.filter(
          (item) =>
            item.operationType === OperationTypeEnum.update &&
            new Date(item.fullBeginDate).getTime() <= new Date(this.serviceTime.currentServiceTime).getTime()
        ).length === this.issueCourseListCopy.length
      ) {
        return this.$message.warning('课程列表不存在未开始的课程，不允许添加课程')
      }
      const row = new IssueCourseDetail()
      this.$set(this.formData, 'name_' + row.id, row)
      this.selectIssue.issueCourseList.push(row)
    }

    /**
     * 删除课程
     */
    deleteCourse(index: number, tip = true) {
      if (tip && this.isUpdate && this.selectIssue.issueCourseList.length === index + 1)
        return this.$confirm(
          '当前删除课程为最后一门课程，如删除当前期别培训将结束，无法追加课程，是否确定删除？',
          '提示',
          {
            confirmButtonText: '继续操作',
            center: true
          }
        ).then(() => {
          this.deleteCourse(index, false)
        })
      const item = this.selectIssue.issueCourseList.splice(index, 1)
      this.formData[item[0].id] = undefined
    }

    async onOpen() {
      try {
        this.loading = true
        this.firstCourse = null
        this.formData = {}
        if (this.selectIssue.issueCourseList.length === 0) {
          this.addCourse()
        } else {
          this.selectIssue.issueCourseList.forEach((row) => {
            this.$set(this.formData, 'name_' + row.id, row)
          })
        }
        if (this.isUpdate) {
          const LearningTypeModelCopy = this.getLearningTypeModelCopy()
          const selectIssueCopy =
            LearningTypeModelCopy.issue.issueConfigList.find((item) => item.id === this.selectIssue.id) ??
            new IssueConfigDetail()
          this.issueCourseListCopy = cloneDeep(selectIssueCopy.issueCourseList)
          await this.reacquireCheckInformation()
        } else {
          this.existCheckInRecord = []
          this.existStarted = []
          this.issueCourseListCopy = []
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 重新获取校验信息
     */
    async reacquireCheckInformation() {
      const issueCourseListIds = this.issueCourseListCopy.map((item) => item.id)
      const param = new GetIssueCourseListUnmodifiableIdsParam()
      param.issueId = this.selectIssue.id
      param.schemeId = this.trainClassBaseInfo.id
      param.issueCourseIds = issueCourseListIds
      const unmodifiableId = (await this.mutationCreateTrainClassCommodity.getIssueCourseListUnmodifiableIds(
        param
      )) ?? {
        existStarted: [],
        existTimeOverlap: [],
        existCheckInRecord: []
      }
      this.existStarted = unmodifiableId.existStarted ?? []
      this.existCheckInRecord = unmodifiableId.existCheckInRecord ?? []
      if (issueCourseListIds.length) {
        const { status, data } = await this.mutationCreateTrainClassCommodity.queryIssueCourseHasSignRecord(
          issueCourseListIds[0]
        )
        if (status.isSuccess() && data.code === 'C601') this.firstCourse = this.issueCourseListCopy[0]
      }
    }

    /**
     * 学时校验
     */
    validateCoursePeriod(rule: any, value: number, callback: Function) {
      if (!value || value < 0) {
        callback(new Error('请输入大于0的学时'))
      } else callback()
    }

    /**
     * 日期校验
     */
    validateTeachingDate(rule: any, value: number, callback: Function) {
      const key = rule.field?.split('.')?.[0]
      if (value && this.formData[key]) {
        const { fullBeginDate, fullEndDate } = this.formData[key] as IssueCourseDetail
        if (fullEndDate) {
          this.validateCell(this.formData[key], 'courseEndTime')
        } else if (fullBeginDate) {
          this.validateCell(this.formData[key], 'courseBeginTime')
        }
      }
      callback()
    }

    /**
     * 开始时间校验
     */
    validateBeginTime(rule: any, value: string, callback: Function) {
      const key = rule.field?.split('.')?.[0]
      if (value && this.formData[key] && !this.skipTime) {
        const { courseEndTime, fullBeginDate, operationType, isModified, id } = this.formData[key] as IssueCourseDetail
        if (
          this.firstCourse &&
          id !== this.firstCourse.id &&
          fullBeginDate &&
          new Date(fullBeginDate).getTime() <= new Date(this.firstCourse.fullEndDate).getTime()
        ) {
          this.warningFirstCourse()
          callback(new Error('开始时间不能早于第一门课程结束时间'))
        } else if (fullBeginDate && !(operationType === OperationTypeEnum.update && !isModified)) {
          const currentDate = Date.now()
          const beginDate = new Date(fullBeginDate).getTime()
          beginDate < currentDate && callback(new Error('请输入未来时间！'))
        }
        if (courseEndTime) {
          this.removeCellValidate(this.formData[key], 'courseEndTime')
          const startTime = new Date(`0000-01-01 ${value}`).getTime()
          const endTime = new Date(`0000-01-01 ${courseEndTime}`).getTime()
          if (endTime < startTime) {
            callback(new Error('授课开始时间不能晚于授课结束时间！'))
          } else if (endTime === startTime) {
            callback(new Error('授课开始时间不能等于授课结束时间！'))
          }
        }
      }
      callback()
    }
    @debounce(100)
    warningFirstCourse() {
      this.$message.warning('第一门课已经存在打卡记录，后续课程开始时间不能早于第一门课程的结束时间！')
    }

    /**
     * 结束时间校验
     */
    validateEndTime(rule: any, value: string, callback: Function) {
      const key = rule.field?.split('.')?.[0]
      if (value && this.formData[key] && !this.skipTime) {
        const { courseBeginTime, fullEndDate, operationType, isModified } = this.formData[key] as IssueCourseDetail
        if (fullEndDate && !(operationType === OperationTypeEnum.update && !isModified)) {
          const currentDate = Date.now()
          const endDate = new Date(fullEndDate).getTime()
          endDate < currentDate && callback(new Error('请输入未来时间！'))
        }
        if (courseBeginTime) {
          this.validateCell(this.formData[key], 'courseBeginTime')
          const startTime = new Date(`0000-01-01 ${courseBeginTime}`).getTime()
          const endTime = new Date(`0000-01-01 ${value}`).getTime()
          if (endTime < startTime) {
            callback(new Error('授课结束时间不能早于授课开始时间！'))
          } else if (endTime === startTime) {
            callback(new Error('授课结束时间不能等于授课开始时间！'))
          }
        }
      }
      callback()
    }

    /**
     * 校验时间重叠
     */
    isOverlap(period1: IssueCourseDetail, period2: IssueCourseDetail): boolean {
      const start1 = new Date(period1.fullBeginDate).getTime()
      const end1 = new Date(period1.fullEndDate).getTime()
      const start2 = new Date(period2.fullBeginDate).getTime()
      const end2 = new Date(period2.fullEndDate).getTime()

      return !(end1 < start2 || end2 < start1)
    }

    /**
     * 打开批量
     */
    openBatchDrawer() {
      if (this.selectIssue.issueCourseList.length) {
        this.$confirm('当前列表已存在课程，如批量创建课程将会覆盖列表数据，是否继续操作？', '提示', {
          confirmButtonText: '继续操作'
        }).then(() => {
          this.showBatchDrawer = true
        })
      } else this.showBatchDrawer = true
    }
    /**
     * 校验列表
     * @param courseList
     * @returns {boolean}
     */
    hasOverlap(courseList: IssueCourseDetail[]): boolean {
      for (let i = 0; i < courseList.length; i++) {
        for (let j = i + 1; j < courseList.length; j++) {
          if (this.isOverlap(courseList[i], courseList[j])) {
            return true
          }
        }
      }
      return false
    }
    /**
     * 保存
     */
    async save() {
      try {
        this.saveLoading = true
        if (this.isUpdate) {
          await this.reacquireCheckInformation()
          const deletedCourses = this.issueCourseListCopy.filter(
            (course) => !this.selectIssue.issueCourseList.some((newCourse) => newCourse.id === course.id)
          )
          const modfiedCourses = this.selectIssue.issueCourseList.filter(
            (course) => course.operationType === OperationTypeEnum.update && course.isModified
          )
          // 还原方法
          const reset = () => {
            modfiedCourses.forEach((item) => {
              const { teachingDate, courseBeginTime, courseEndTime } = this.issueCourseListCopy.find(
                (ite) => ite.id === item.id
              )
              item.teachingDate = teachingDate
              item.courseBeginTime = courseBeginTime
              item.courseEndTime = courseEndTime
              item.isModified = false
            })
            deletedCourses.forEach((item) => {
              this.selectIssue.issueCourseList.unshift(cloneDeep(item))
            })
          }
          if (
            ((deletedCourses.length && deletedCourses.some((item) => this.existStarted.includes(item.id))) ||
              (modfiedCourses.length && modfiedCourses.some((item) => this.existStarted.includes(item.id)))) &&
            !this.skipTime
          ) {
            return this.$confirm('修改的课程授课时间已开始，修改将无效！', '提示', {
              showCancelButton: false
            }).finally(() => {
              reset()
            })
          }
          if (
            ((deletedCourses.length && deletedCourses.some((item) => this.existCheckInRecord.includes(item.id))) ||
              (modfiedCourses.length && modfiedCourses.some((item) => this.existCheckInRecord.includes(item.id)))) &&
            !this.skipTime
          ) {
            return this.$confirm('修改的课程已存在打卡记录，修改将无效！', '提示', {
              showCancelButton: false
            }).finally(() => {
              reset()
            })
          }
        }
        // 先随便写一下
        const isValid = this.selectIssue.issueCourseList.some((item) => {
          if (!item.courseName) {
            this.$message.error(`【课程名称】未填写，请填写后提交！`)
            return true
          } else if (!item.teacherName) {
            this.$message.error(`【授课教师】未填写，请填写后提交！`)
            return true
          } else if (!item.coursePeriod) {
            this.$message.error(`【课程学时】未填写，请填写后提交！`)
            return true
          } else if (!item.teachingDate) {
            this.$message.error(`【授课日期】未填写，请填写后提交！`)
            return true
          } else if (!item.courseBeginTime) {
            this.$message.error(`【授课开始时间】未填写，请填写后提交！`)
            return true
          } else if (!item.courseEndTime) {
            this.$message.error(`【授课结束时间】未填写，请填写后提交！`)
            return true
          }
          return false
        })
        if (isValid) return
        if (this.validateAllRows()) {
          // 是否存在以前时间
          const existStarted = this.selectIssue.issueCourseList.some((item) => {
            const startTime = new Date(item.fullBeginDate)
            // const endTime = new Date(item.fullBeginDate)
            const now = new Date()
            return startTime < now && item.operationType === OperationTypeEnum.create
          })
          if (this.selectIssue.issueCourseList.length > 1 && existStarted && !this.skipTime) {
            return this.$confirm('当前课表存在授课开始时间已过的课程，请检查并调整后再进行操作！', '提示', {
              confirmButtonText: '我知道了'
            })
          }
          if (this.selectIssue.issueCourseList.length > 1 && this.hasOverlap(this.selectIssue.issueCourseList)) {
            return this.$confirm('当前课表存在课程授课时间重叠，请检查并调整后再进行操作！', '提示', {
              confirmButtonText: '我知道了'
            })
          }
          this.$emit('save')
        } else {
          this.$message.error('请完善课程信息')
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.saveLoading = false
      }
    }
  }
</script>
<style scoped>
  .operateFrom >>> .el-form-item__content {
    margin-left: 0px !important;
    margin-bottom: 0px !important;
  }
</style>
<style>
  .operateCourseTableHeard > div.cell::before {
    content: '*';
    color: #f56c6c;
    margin-right: 4px;
  }
</style>
