<route-params content="/:id"></route-params>
<route-meta>
{
"isMenu": true,
"title": "整体报告",
"sort": 2,
"icon": "icon_guanli",
"hideMenu": true
}
</route-meta>
<script lang="ts">
  import HolisticReport from '@hbfe/jxjy-admin-scheme/src/implementingManagement/holisticReport.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    holisticReport: [WXGLY]
  })
  export default class extends HolisticReport {}
</script>
