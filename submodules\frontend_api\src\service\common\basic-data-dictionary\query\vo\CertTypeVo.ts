/*
 * @Author: dong<PERSON><PERSON>
 * @Date: 2024-08-21 10:08:03
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-08-21 10:51:38
 * @Description:
 */
import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'

class CertTypeVo {
  /**
   * 唯一标识
   */
  propertyId: string
  /**
   * 培训类别名称
   */
  name: string
  /**
   * 序号
   */
  sort: number
  /**
   * 展示名称
   */
  showName: string
  /**
   * 父级id
   */
  parentId: string

  static from(res: TrainingPropertyResponse) {
    const CertificatesType = new TrainingPropertyResponse()
    CertificatesType.name = res.name
    CertificatesType.propertyId = res.propertyId
    CertificatesType.sort = res.sort
    CertificatesType.parentId = res.parentId
    CertificatesType.showName = res.showName
    return CertificatesType
  }
}

export default CertTypeVo
