import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * 购买渠道类型
 */
enum PurchaseChannelEnum {
  STUDENT_PURCHASE = 1,
  UNIT_PURCHASE = 2,
  IMPORT = 3
}

export { PurchaseChannelEnum }
class PurchaseChannelType extends AbstractEnum<PurchaseChannelEnum> {
  static enum = PurchaseChannelEnum
  constructor(status?: PurchaseChannelEnum) {
    super()
    this.current = status
    this.map.set(PurchaseChannelEnum.STUDENT_PURCHASE, '学员缴费')
    this.map.set(PurchaseChannelEnum.UNIT_PURCHASE, '单位缴费')
    this.map.set(PurchaseChannelEnum.IMPORT, '导入开通')
  }
}
export default new PurchaseChannelType()
