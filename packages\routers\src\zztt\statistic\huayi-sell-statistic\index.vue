<route-meta>
{
"isMenu": true,
"title": "华医网组合开通统计",
"sort": 15,
"icon": "icon-ribaotongji"
}
</route-meta>

<script lang="ts">
  import HuayiSellStatistic from '@hbfe/jxjy-admin-huayiSellStatistic/src/diff/zztt/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import {
    // 施教机构管理员
    WXGLY
  } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    export: [WXGLY]
  })
  export default class extends HuayiSellStatistic {}
</script>
