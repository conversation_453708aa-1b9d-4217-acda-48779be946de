import getContractStatus from './queries/getContractStatus.graphql'
import getOnlineSchoolServicerProviderByHeader from './queries/getOnlineSchoolServicerProviderByHeader.graphql'
import addSecondEnterpriseServicerMenu from './mutates/addSecondEnterpriseServicerMenu.graphql'
import addSecondMechanicAcademyMenu from './mutates/addSecondMechanicAcademyMenu.graphql'
import applyForService from './mutates/applyForService.graphql'
import applyForServiceByDomainName from './mutates/applyForServiceByDomainName.graphql'
import cancelAllCVendorAuthPromotionTraining from './mutates/cancelAllCVendorAuthPromotionTraining.graphql'
import createBuildInPlate from './mutates/createBuildInPlate.graphql'
import createChannelVendor from './mutates/createChannelVendor.graphql'
import createCoursewareSupplier from './mutates/createCoursewareSupplier.graphql'
import createJxjyCommonMenu from './mutates/createJxjyCommonMenu.graphql'
import createNewsCategoryTrainingInstitutionMenu from './mutates/createNewsCategoryTrainingInstitutionMenu.graphql'
import createParticipatingUnit from './mutates/createParticipatingUnit.graphql'
import createTrainingInstitution from './mutates/createTrainingInstitution.graphql'
import createTrainingInstitutionPortal from './mutates/createTrainingInstitutionPortal.graphql'
import deleteEnterpriseServicerMenu from './mutates/deleteEnterpriseServicerMenu.graphql'
import deleteMechanicAcademyMenu from './mutates/deleteMechanicAcademyMenu.graphql'
import deleteTrainingInstitutionMenu from './mutates/deleteTrainingInstitutionMenu.graphql'
import disableContractProvider from './mutates/disableContractProvider.graphql'
import disableTrainingInstitution from './mutates/disableTrainingInstitution.graphql'
import disableTrainingInstitutionMenu from './mutates/disableTrainingInstitutionMenu.graphql'
import enableContractProvider from './mutates/enableContractProvider.graphql'
import enableEnterpriseServicerMenu from './mutates/enableEnterpriseServicerMenu.graphql'
import enableEnterpriseServicerPlate from './mutates/enableEnterpriseServicerPlate.graphql'
import enableMechanicAcademyMenu from './mutates/enableMechanicAcademyMenu.graphql'
import enableTrainingInstitution from './mutates/enableTrainingInstitution.graphql'
import enableTrainingInstitutionMenu from './mutates/enableTrainingInstitutionMenu.graphql'
import enableTrainingInstitutionPlate from './mutates/enableTrainingInstitutionPlate.graphql'
import getServicerInfoByUnitId from './mutates/getServicerInfoByUnitId.graphql'
import publishEnterprisePortal from './mutates/publishEnterprisePortal.graphql'
import publishTrainingInstitutionPortal from './mutates/publishTrainingInstitutionPortal.graphql'
import removeTrainingInstitutionPortal from './mutates/removeTrainingInstitutionPortal.graphql'
import resumeWithChannelVendorSigned from './mutates/resumeWithChannelVendorSigned.graphql'
import resumeWithCoursewareSupplierSigned from './mutates/resumeWithCoursewareSupplierSigned.graphql'
import resumeWithTrainingInstitutionSigned from './mutates/resumeWithTrainingInstitutionSigned.graphql'
import saveEnterpriseServicerBannerList from './mutates/saveEnterpriseServicerBannerList.graphql'
import saveTrainingInstitutionBannerList from './mutates/saveTrainingInstitutionBannerList.graphql'
import signUpChannelVendor from './mutates/signUpChannelVendor.graphql'
import signUpCoursewareSupplier from './mutates/signUpCoursewareSupplier.graphql'
import signUpTrainingInstitution from './mutates/signUpTrainingInstitution.graphql'
import sortEnterpriseServicerPlate from './mutates/sortEnterpriseServicerPlate.graphql'
import sortTrainingInstitutionPlate from './mutates/sortTrainingInstitutionPlate.graphql'
import suspendWithChannelVendorSigned from './mutates/suspendWithChannelVendorSigned.graphql'
import suspendWithCoursewareSupplierSigned from './mutates/suspendWithCoursewareSupplierSigned.graphql'
import suspendWithTrainingInstitutionSigned from './mutates/suspendWithTrainingInstitutionSigned.graphql'
import unpublishEnterprisePortal from './mutates/unpublishEnterprisePortal.graphql'
import unpublishTrainingInstitutionPortal from './mutates/unpublishTrainingInstitutionPortal.graphql'
import updateChannelVendor from './mutates/updateChannelVendor.graphql'
import updateCoursewareSupplier from './mutates/updateCoursewareSupplier.graphql'
import updateEnterpriseH5Portal from './mutates/updateEnterpriseH5Portal.graphql'
import updateEnterpriseServicerMenu from './mutates/updateEnterpriseServicerMenu.graphql'
import updateEnterpriseServicerPlate from './mutates/updateEnterpriseServicerPlate.graphql'
import updateEnterpriseWebPortal from './mutates/updateEnterpriseWebPortal.graphql'
import updateMechanicAcademyH5Portal from './mutates/updateMechanicAcademyH5Portal.graphql'
import updateMechanicAcademyMenu from './mutates/updateMechanicAcademyMenu.graphql'
import updateMechanicAcademyWebPortal from './mutates/updateMechanicAcademyWebPortal.graphql'
import updateParticipatingUnit from './mutates/updateParticipatingUnit.graphql'
import updateTrainingInstitution from './mutates/updateTrainingInstitution.graphql'
import updateTrainingInstitutionH5Portal from './mutates/updateTrainingInstitutionH5Portal.graphql'
import updateTrainingInstitutionMenu from './mutates/updateTrainingInstitutionMenu.graphql'
import updateTrainingInstitutionPlate from './mutates/updateTrainingInstitutionPlate.graphql'
import updateTrainingInstitutionPortal from './mutates/updateTrainingInstitutionPortal.graphql'
import updateTrainingInstitutionWebPortal from './mutates/updateTrainingInstitutionWebPortal.graphql'
import updateTrainingInstitutionWebPortalThemeColor from './mutates/updateTrainingInstitutionWebPortalThemeColor.graphql'
import verifyAuthCVendorPromotionTrainingEffective from './mutates/verifyAuthCVendorPromotionTrainingEffective.graphql'
import verifyContractEffective from './mutates/verifyContractEffective.graphql'

export {
  getContractStatus,
  getOnlineSchoolServicerProviderByHeader,
  addSecondEnterpriseServicerMenu,
  addSecondMechanicAcademyMenu,
  applyForService,
  applyForServiceByDomainName,
  cancelAllCVendorAuthPromotionTraining,
  createBuildInPlate,
  createChannelVendor,
  createCoursewareSupplier,
  createJxjyCommonMenu,
  createNewsCategoryTrainingInstitutionMenu,
  createParticipatingUnit,
  createTrainingInstitution,
  createTrainingInstitutionPortal,
  deleteEnterpriseServicerMenu,
  deleteMechanicAcademyMenu,
  deleteTrainingInstitutionMenu,
  disableContractProvider,
  disableTrainingInstitution,
  disableTrainingInstitutionMenu,
  enableContractProvider,
  enableEnterpriseServicerMenu,
  enableEnterpriseServicerPlate,
  enableMechanicAcademyMenu,
  enableTrainingInstitution,
  enableTrainingInstitutionMenu,
  enableTrainingInstitutionPlate,
  getServicerInfoByUnitId,
  publishEnterprisePortal,
  publishTrainingInstitutionPortal,
  removeTrainingInstitutionPortal,
  resumeWithChannelVendorSigned,
  resumeWithCoursewareSupplierSigned,
  resumeWithTrainingInstitutionSigned,
  saveEnterpriseServicerBannerList,
  saveTrainingInstitutionBannerList,
  signUpChannelVendor,
  signUpCoursewareSupplier,
  signUpTrainingInstitution,
  sortEnterpriseServicerPlate,
  sortTrainingInstitutionPlate,
  suspendWithChannelVendorSigned,
  suspendWithCoursewareSupplierSigned,
  suspendWithTrainingInstitutionSigned,
  unpublishEnterprisePortal,
  unpublishTrainingInstitutionPortal,
  updateChannelVendor,
  updateCoursewareSupplier,
  updateEnterpriseH5Portal,
  updateEnterpriseServicerMenu,
  updateEnterpriseServicerPlate,
  updateEnterpriseWebPortal,
  updateMechanicAcademyH5Portal,
  updateMechanicAcademyMenu,
  updateMechanicAcademyWebPortal,
  updateParticipatingUnit,
  updateTrainingInstitution,
  updateTrainingInstitutionH5Portal,
  updateTrainingInstitutionMenu,
  updateTrainingInstitutionPlate,
  updateTrainingInstitutionPortal,
  updateTrainingInstitutionWebPortal,
  updateTrainingInstitutionWebPortalThemeColor,
  verifyAuthCVendorPromotionTrainingEffective,
  verifyContractEffective
}
