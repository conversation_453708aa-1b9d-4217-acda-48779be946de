<template>
  <el-drawer title="统计口径说明" :visible.sync="isShow" size="900px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-alert type="warning" show-icon :closable="false" class="m-alert">
        注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
      </el-alert>
      <p class="f-mt20 f-mb10">
        <span class="f-fb f-f15">搜索条件说明</span>
        （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
      </p>
      <el-table stripe :data="searchData" border class="m-table">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="字段" width="150">
          <template slot-scope="scope">
            {{ scope.row.field }}
          </template>
        </el-table-column>
        <el-table-column label="详细说明" min-width="300">
          <template slot-scope="scope">{{ scope.row.des }} </template>
        </el-table-column>
      </el-table>
      <p class="f-mt20 f-mb10">
        <span class="f-fb f-f15">列表字段及详细说明</span>
        （列表下的数据显示受搜索条件的约束，统计单位：人次）
      </p>
      <el-table stripe :data="fieldData" border class="m-table">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="字段" width="150">
          <template slot-scope="scope">
            {{ scope.row.field }}
          </template>
        </el-table-column>
        <el-table-column label="详细说明" min-width="300">
          <template slot-scope="scope">
            {{ scope.row.des }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button type="primary" @click="closeDrawer">确定</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'

  @Component
  export default class StatisticalCaliberDeclarationDrawer extends Vue {
    @Prop({
      required: true,
      type: Array
    })
    searchData: { field: string; des: string }[] // 统计口径搜索说明列表
    @Prop({
      required: true,
      type: Array
    })
    fieldData: { field: string; des: string }[] // 统计口径列表字段说明列表

    // 是否展示
    isShow = false

    /**
     * 打开抽屉
     */
    openDrawer() {
      this.isShow = true
    }

    /**
     * 关闭抽屉
     */
    closeDrawer() {
      this.isShow = false
    }
  }
</script>
