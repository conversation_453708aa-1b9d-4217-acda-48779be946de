<template>
  <el-drawer title="操作记录" :visible.sync="logVisible" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <div class="f-mt20 f-mlr40">
        <el-timeline>
          <el-timeline-item>
            【读取操作者名称】在 【读取操作时间2019-11-07 10:48:55】 开票了【培训发票】。
          </el-timeline-item>
          <el-timeline-item>
            【读取操作者名称】在 【读取操作时间2019-11-07 10:48:55】 开票了【培训发票】。
          </el-timeline-item>
        </el-timeline>
      </div>
      <el-alert type="info" :closable="false" class="m-alert">
        <div class="f-ptb30 f-tc">该发票暂时还没有操作记录！</div>
      </el-alert>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    logDialog: boolean

    logVisible = false

    @Watch('logDialog')
    changeDialogCtrl() {
      this.logVisible = this.logDialog
    }

    @Watch('logVisible')
    changeDialogVisible() {
      this.$emit('update:logDialog', this.logVisible)
    }

    created() {
      this.logVisible = this.logDialog
    }
  }
</script>
