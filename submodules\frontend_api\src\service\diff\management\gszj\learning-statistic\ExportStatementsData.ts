import QueryStudentLearningList from '@api/service/management/statisticalReport/query/QueryStudentLearningList'
import { StudentSchemeLearningRequestVo } from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
import exportMsgateway from '@api/diff-gateway/platform-jxjypxtypt-gszj-school'
import { SyncResultEnmu } from '@api/service/diff/management/gszj/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
class ExportStatementsData extends QueryStudentLearningList {
  syncResult: Record<SyncResultEnmu, number> = {
    [SyncResultEnmu.DisableSynchronized]: -1,
    [SyncResultEnmu.Unsynchronized]: 0,
    [SyncResultEnmu.Synchronized]: 1,
    [SyncResultEnmu.SynchronizationFailure]: 2,
    [SyncResultEnmu.Waitsynchronized]: 3
  }
  /**
   * 导出报盘数据
   */
  async exportStatementsDiff(param: StudentSchemeLearningRequestVo) {
    try {
      param = await this.initExportParams(param)
      const res = await exportMsgateway.exportStudentCourseLearningQuotationInServicer(param)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置submodules/frontend_api/src/service/diff/management/gszj/learning-statistic/ExportStatementsData.ts，exportStatementsDiff',
        e
      )
    }
  }
}
export default ExportStatementsData
