import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformTrainingInstitution'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformTrainingInstitution'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum QueryParamSortEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum PortalTypeEnums {
  WEB = 'WEB',
  MOBILE = 'MOBILE'
}
export enum ServicerResourceTypeEnums {
  PLATFORM = 'PLATFORM',
  COOPERATIVE_AGENCY = 'COOPERATIVE_AGENCY'
}
export enum ServicerStatusEnums {
  ALL = 'ALL',
  NORMAL = 'NORMAL',
  SUSPEND = 'SUSPEND'
}
export enum AddedServicesStatusEnum {
  OPEN = 'OPEN',
  CLOSE = 'CLOSE'
}

// 类

export class AuthCVendorPromotionAllTrainingRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 渠道商编号
   */
  channelVendorId?: string
}

export class AuthCVendorPromotionTrainingExistRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 培训班id，对应schemeId
   */
  trainingId?: string
}

export class AuthCVendorPromotionTrainingRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 渠道商编号
   */
  channelVendorId?: string
  /**
   * 培训班编号集合
   */
  trainingIdList?: Array<string>
}

export class CancelAuthCVendorPromotionTrainingRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 渠道商编号
   */
  channelVendorId?: string
  /**
   * 培训班编号集合
   */
  trainingIdList?: Array<string>
}

/**
 * 培训机构查询信息
 */
export class TrainingInstitutionQueryParams {
  /**
   * 培训机构名称
   */
  name?: string
  /**
   * 机构代码
   */
  code?: string
  /**
   * 服务商状态
@see com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerStatusEnums
   */
  servicerStatus?: ServicerStatusEnums
  /**
   * 服务商简称或名称首字母
   */
  spell?: string
  /**
   * 增值服务状态
   */
  addedServicesStatus?: AddedServicesStatusEnum
  /**
   * 人气（报名人次）排序
   */
  saleTotalNumberSortPolicy?: QueryParamSortEnum
  /**
   * 好评率排序
   */
  averageSortPolicy?: QueryParamSortEnum
  /**
   * 筛选的机构id集合
   */
  trainingInstitutionIds?: Array<string>
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 同步培训机构结果
<AUTHOR> create 2021/10/21 14:12
 */
export class SyncTrainingInstitutionResponse {
  /**
   * 培训机构id
   */
  trainingInstitutionId: string
  /**
   * 培训机构状态,默认是正常  1：正常 2：停用
   */
  trainingInstitutionStatus: number
  /**
   * 单位id
   */
  unitId: string
  /**
   * 企业主账号id
   */
  rootAccountId: string
}

export class ServicerAddedServicesLogDto {
  /**
   * 增值服务开启状态
   */
  addedServicesStatus: AddedServicesStatusEnum
  /**
   * 操作时间
   */
  operationTime: string
  /**
   * 操作人
   */
  operationUserName: string
}

export class SimpleTrainingInstitutionDto {
  /**
   * 机构id
   */
  servicerId: string
  /**
   * 机构名称
   */
  servicerName: string
  /**
   * 机构简称
   */
  abouts: string
  /**
   * 统一信用社会代码
   */
  code: string
}

/**
 * 培训机构详情信息
 */
export class TrainingInstitutionDetailDto {
  /**
   * Id
   */
  id: string
  /**
   * 机构名称
   */
  name: string
  /**
   * 机构代码
   */
  code: string
  /**
   * 机构简介
   */
  abouts: string
  /**
   * 机构logo
   */
  logo: string
  /**
   * 联系人
   */
  contactPerson: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 培训机构门户信息
   */
  portals: Array<PortalDto>
  /**
   * 电子公章文件路径
   */
  electronicSealPath: string
  /**
   * 好评率
   */
  average: number
  /**
   * 学员数(销售总数量，不考虑退款)
   */
  saleTotalNumber: number
  /**
   * 课程数
   */
  commodityCount: number
}

/**
 * 培训机构列表信息
 */
export class TrainingInstitutionListDto {
  /**
   * Id
   */
  id: string
  /**
   * 机构logo
   */
  logo: string
  /**
   * 培训机构来源
@see com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerResourceTypeEnums
   */
  resourceType: ServicerResourceTypeEnums
  /**
   * 机构名称
   */
  name: string
  /**
   * 服务商简介
   */
  abouts: string
  /**
   * WEB域名
   */
  webDomain: string
  /**
   * MOBILE域名
   */
  mobileDomain: string
  /**
   * 地区名称列表，省市区县...
   */
  regionNames: Array<string>
  /**
   * 所在地区路径
   */
  regionPath: string
  /**
   * 机构代码
   */
  code: string
  /**
   * 联系人
   */
  contactPerson: string
  /**
   * 联系电话
   */
  phone: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 服务商状态
@see com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.ServicerStatusEnums
   */
  servicerStatus: ServicerStatusEnums
  /**
   * 好评率
   */
  average: number
  /**
   * 学员数(销售总数量，不考虑退款)
   */
  saleTotalNumber: number
  /**
   * 课程数
   */
  commodityCount: number
  /**
   * 当前是否开通增值服务
   */
  addedServicesStatusEnum: AddedServicesStatusEnum
  /**
   * 增值服务开通时间
   */
  openAddedServiceTime: string
}

/**
 * 培训机构轮播图信息
 */
export class BannerDto {
  /**
   * 轮播图编号
   */
  id: string
  /**
   * 链接地址
   */
  link: string
  /**
   * 图片路径
   */
  path: string
  /**
   * 轮播图排序
   */
  sort: number
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 培训机构门户信息
 */
export class PortalDto {
  /**
   * 门户编号
   */
  id: string
  /**
   * 门户类型
@see com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.PortalTypeEnums
   */
  portalType: PortalTypeEnums
  /**
   * 域名
   */
  domainName: string
  /**
   * 宣传口号
   */
  slogan: string
  /**
   * 门户简介说明内容
   */
  content: string
  /**
   * 轮播图列表
   */
  banners: Array<BannerDto>
  /**
   * 创建时间
   */
  createdTime: string
}

export class TrainingInstitutionListDtoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingInstitutionListDto>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 子项目管理员培训机构详情
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async detail(
    id: string,
    query: DocumentNode = GraphqlImporter.detail,
    operation?: string
  ): Promise<Response<TrainingInstitutionDetailDto>> {
    return commonRequestApi<TrainingInstitutionDetailDto>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出培训机构信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingInstitutionInfo(
    params: TrainingInstitutionQueryParams,
    query: DocumentNode = GraphqlImporter.exportTrainingInstitutionInfo,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { params },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取所有培训机构详情
   * @param query 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findDetailList(
    idList: Array<string>,
    query: DocumentNode = GraphqlImporter.findDetailList,
    operation?: string
  ): Promise<Response<Array<TrainingInstitutionDetailDto>>> {
    return commonRequestApi<Array<TrainingInstitutionDetailDto>>(
      SERVER_URL,
      {
        query: query,
        variables: { idList },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取所有启用的培训机构的名称首字母
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findServicerSpellList(
    query: DocumentNode = GraphqlImporter.findServicerSpellList,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询培训机构增值服务日志
   * @param query 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTrainingInstitutionAddedServicesLog(
    servicerId: string,
    query: DocumentNode = GraphqlImporter.findTrainingInstitutionAddedServicesLog,
    operation?: string
  ): Promise<Response<Array<ServicerAddedServicesLogDto>>> {
    return commonRequestApi<Array<ServicerAddedServicesLogDto>>(
      SERVER_URL,
      {
        query: query,
        variables: { servicerId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取培训机构信息（通过域名）
   * @param query 查询 graphql 语法文档
   * @param domain 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTrainingInstitutionByDomain(
    domain: string,
    query: DocumentNode = GraphqlImporter.findTrainingInstitutionByDomain,
    operation?: string
  ): Promise<Response<TrainingInstitutionDetailDto>> {
    return commonRequestApi<TrainingInstitutionDetailDto>(
      SERVER_URL,
      {
        query: query,
        variables: { domain },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取所有培训机构列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findTrainingInstitutionList(
    params: TrainingInstitutionQueryParams,
    query: DocumentNode = GraphqlImporter.findTrainingInstitutionList,
    operation?: string
  ): Promise<Response<Array<SimpleTrainingInstitutionDto>>> {
    return commonRequestApi<Array<SimpleTrainingInstitutionDto>>(
      SERVER_URL,
      {
        query: query,
        variables: { params },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取培训机构数
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTrainingInstitutionCount(
    query: DocumentNode = GraphqlImporter.getTrainingInstitutionCount,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param existParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async institutionTrainingPromotionAuthExist(
    existParam: AuthCVendorPromotionTrainingExistRequest,
    query: DocumentNode = GraphqlImporter.institutionTrainingPromotionAuthExist,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { existParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取培训机构分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async page(
    params: { page?: Page; params?: TrainingInstitutionQueryParams },
    query: DocumentNode = GraphqlImporter.page,
    operation?: string
  ): Promise<Response<TrainingInstitutionListDtoPage>> {
    return commonRequestApi<TrainingInstitutionListDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构下对已签约的渠道商授权推广所有班级
   * @param authAllParam 授权所有班级参数
   * @param mutate 查询 graphql 语法文档
   * @param authAllParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async authCVendorPromotionAllTraining(
    authAllParam: AuthCVendorPromotionAllTrainingRequest,
    mutate: DocumentNode = GraphqlImporter.authCVendorPromotionAllTraining,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { authAllParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构下对已签约的渠道商授权推广班级
   * @param authParam 授权参数
   * @param mutate 查询 graphql 语法文档
   * @param authParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async authCVendorPromotionTraining(
    authParam: AuthCVendorPromotionTrainingRequest,
    mutate: DocumentNode = GraphqlImporter.authCVendorPromotionTraining,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { authParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 取消培训机构下对已签约的渠道商授权推广班级
   * @param cancelAuthParam 取消授权参数
   * @param mutate 查询 graphql 语法文档
   * @param cancelAuthParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelAuthCVendorPromotionTraining(
    cancelAuthParam: CancelAuthCVendorPromotionTrainingRequest,
    mutate: DocumentNode = GraphqlImporter.cancelAuthCVendorPromotionTraining,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { cancelAuthParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请登录的随机码
   * @return 返回随机码信息
   * @param mutate 查询 graphql 语法文档
   * @param mztToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async syncTrainingInstitution(
    mztToken: string,
    mutate: DocumentNode = GraphqlImporter.syncTrainingInstitution,
    operation?: string
  ): Promise<Response<SyncTrainingInstitutionResponse>> {
    return commonRequestApi<SyncTrainingInstitutionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mztToken },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 同步培训机构信息
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async synchronizeTrainingInstitutions(
    mutate: DocumentNode = GraphqlImporter.synchronizeTrainingInstitutions,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
