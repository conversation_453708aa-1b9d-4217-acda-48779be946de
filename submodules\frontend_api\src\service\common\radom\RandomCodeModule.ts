import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import PlatformRandomCode, {
  AntiResultResponse,
  ApplyAntiCheatCodeRequest,
  CodeInitInfoResponse,
  LoginRandomCodeResponse
} from '@api/gateway/PlatformRandomCode'
import ConfigCenterModule from '@api/service/common/config-center/ConfigCenter'

class FaceDetectionConfig {
  host = ''
  kind = ''

  getConcatString(concatString: Array<string>) {
    return [this.host, this.kind].concat(concatString).join('/')
  }
}

@Module({ namespaced: true, dynamic: true, store, name: 'CommonRandomCodeModule' })
class RandomCodeModule extends VuexModule {
  // 登录随机码
  loginRandomCode = ''

  // 防作弊随机码
  antiCheatRandomCode = ''

  @Action
  async getLoginRandomCode() {
    try {
      const result = await PlatformRandomCode.applyLoginRandomCode()
      if (result.status.isSuccess()) {
        this.SET_LOGIN_RANDOM_CODE(result.data.code)
      }
      return result.status
    } catch (e) {
      // return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }

  @Action
  async getLoginRandomCodeResult(randomCode?: string): Promise<LoginRandomCodeResponse> | undefined {
    try {
      const { status, data } = await PlatformRandomCode.getLoginRandomCodeResult(randomCode || this.loginRandomCode)
      if (status.isSuccess()) {
        if (!data.status) {
          // 随机码失效
          this.SET_LOGIN_RANDOM_CODE('')
        }
      }
      return data
    } catch (e) {
      // todo
      return undefined
    }
  }

  @Action
  async applyAntiCheatCode(applyAntiCheatCodeRequest: ApplyAntiCheatCodeRequest): Promise<CodeInitInfoResponse> {
    try {
      const { data } = await PlatformRandomCode.applyAntiCheatCode(applyAntiCheatCodeRequest)
      return data
    } catch (e) {
      // todo
    }
  }

  @Action
  async getAntiCheatResult(randomCode?: string): Promise<AntiResultResponse> {
    try {
      const { status, data } = await PlatformRandomCode.getAntiCheatResult(randomCode || this.antiCheatRandomCode)
      if (status.isSuccess()) {
        if (data.expire || data.verificationSuccess) {
          this.SET_ANTI_CHEAT_RANDOM_CODE(undefined)
        } else {
          this.SET_ANTI_CHEAT_RANDOM_CODE(data.randomCode)
        }
      }
      return data
    } catch (e) {
      // todo
    }
  }

  @Mutation
  SET_LOGIN_RANDOM_CODE(code: string) {
    this.loginRandomCode = code
  }

  @Mutation
  SET_ANTI_CHEAT_RANDOM_CODE(code: string) {
    this.antiCheatRandomCode = code
  }

  get getFaceDetectionConfig(): FaceDetectionConfig {
    const config = new FaceDetectionConfig()
    try {
      const configString = ConfigCenterModule.getApplicationByName('applicationDiff.faceDetectionConfig')
      const tempConfig = JSON.parse(configString)
      Object.assign(config, tempConfig)
    } catch (e) {
      // nothing
    }
    return config
  }
}

export default getModule(RandomCodeModule)
