import BaseReportModel from '@api/service/common/webfunny/models/BaseReportModel'
import { ExamingEnum } from '@api/service/common/webfunny/enums/ExamingEnum'
import WebfunnyReport from '@api/service/common/webfunny/WefunnyReport'

export default class ExamingReportModel extends BaseReportModel {
  examId?: string // examId | 类型：文本 | 长度：50 |试卷ID
  answerId?: string // answerId | 类型：文本 | 长度：50 |答卷ID
  examRoundId?: string // examRoundId | 类型：文本 | 长度：50 |场次ID
  examTime?: string // examTime | 类型：文本 | 长度：50 |考试时间
  questionId?: string // questionId | 类型：文本 | 长度：50 |试题ID
  examResult?: string // examTime | 类型：文本 | 长度：50 |考试结果
  qualificationId: string // qualificationId | 类型：文本 | 长度：200 |参训资格ID
  requestUrl: string // requestUrl | 类型：文本 | 长度：500 |请求地址
}

export const status_map: any = {
  2: '您已退款！',
  3: '班级已换班或在换班中，无法练习',
  200: '正常',
  50002: '培训未开始',
  50003: '培训已结束',
  70001: '处在换班或者退款中',
  70002: '学员培训资格已失效',
  70003: '学习方式被停用',
  60004: '前置条件未达标',
  60001: '查询不到课后测验配置',
  60002: '课后测验已达标，不可进入',
  60003: '不大于最小学习进度',
  21001: '试题不足，自动合格',
  51001: '重算中',
  500: '其他未定义异常'
}
