<template>
  <el-select v-model="selectItem" :placeholder="placeholder" class="form-l" filterable clearable multiple>
    <el-option
      v-for="item in trainingObjectOptions"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
      :disabled="isDisabled && item.name != '全部'"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Mixins, Watch } from 'vue-property-decorator'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
  import QueryTrainingObject from '@api/service/common/basic-data-dictionary/query/QueryTrainingObject'
  import CommonSkuMixins from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSkuMixins'

  @Component
  export default class extends Mixins(CommonSkuMixins) {
    // 培训对象选项
    trainingObjectOptions: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()

    /**
     * 选项值
     */
    get selectItem() {
      return this.value
    }
    /**
     * 重新设置选项值
     */
    set selectItem(val: string[]) {
      // 当前点击移除的信息
      this.removeValue = this.value.filter((item) => {
        return !val.includes(item)
      })
      if (val.includes('-1')) {
        // 全部直接赋值
        this.$emit('input', ['-1'])
        // 同步查询岗位类别
        this.$emit('getPositionCategory', val)
      } else if (this.value.includes('-1')) {
        if (!this.selectSchemeIndustry.length && !this.specialIndustry.length) {
          // 若全部数据中不存在已设置的行业属性，直接删除
          this.$emit('input', val)
          // 同步查询岗位类别
          this.$emit('getPositionCategory', val)
        } else {
          this.$emit('removeSku', 'removeAll')
        }
      } else if (val.length > this.value.length || (!this.selectSchemeValue && !this.specialSchemeValue)) {
        this.$emit('input', val)
        // 同步查询岗位类别
        this.$emit('getPositionCategory', val)
      } else {
        this.$emit('removeSku', 'removeItem')
      }
    }

    @Watch('industryPropertyId', {
      immediate: true,
      deep: true
    })
    async industryPropertyIdChange() {
      await this.getTrainingCategoryOptions()
    }

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: TrainingCategoryVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    /**
     * 获取培训对象 - 职业卫生行业
     */
    async getTrainingCategoryOptions() {
      this.trainingObjectOptions = await QueryTrainingObject.queryTrainingObjectByIndustry(
        this.industryId,
        this.industryPropertyId
      )
      this.$emit('getAllTrainingObjectIds', this.trainingObjectOptions)
      const param = new TrainingPropertyResponse()
      param.propertyId = '-1'
      param.name = '全部'
      param.sort = 0
      param.showName = ''
      this.trainingObjectOptions.unshift(param)
    }
  }
</script>
