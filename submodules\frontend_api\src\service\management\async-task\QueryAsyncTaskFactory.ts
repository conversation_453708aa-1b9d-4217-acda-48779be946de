import QueryExportAsyncTask from './query/QueryExportAsyncTask'
import QueryImportResult from './query/QueryImportResult'
import QueryImportAsyncTask from '@api/service/management/async-task/query/QueryImportAsyncTask'

/*
 * 异步任务查询工厂
 */
class QueryAsyncTaskFactory {
  /*
    异步任务列表实例
  */
  get queryExportAsyncTask() {
    return new QueryExportAsyncTask()
  }
  /**
   * 导入开通结果跟踪实例
   */
  get queryImportResult() {
    return new QueryImportResult()
  }

  /**
   * 导入任务实例
   */
  get queryImportAsyncTask() {
    return new QueryImportAsyncTask()
  }
}

export default new QueryAsyncTaskFactory()
