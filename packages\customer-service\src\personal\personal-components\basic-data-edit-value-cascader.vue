<!--
 * @Author: WRP
 * @Date: 单个属性修改输入框
-->
<template>
  <span>
    <el-tooltip class="item" effect="dark" placement="top">
      <span class="el-icon-edit-outline f-c9 edit-icon" @click="isShowEditBtn"></span>
      <div slot="content">编辑</div>
    </el-tooltip>
    <div class="edit-box" v-if="isShowEditbox">
      <!-- <national-region-cascader v-model="regionIds"></national-region-cascader> -->
      <el-cascader
        v-model="regionIds"
        :props="regionProps"
        :options="regionOptions"
        placeholder="请选择地区"
        class="form-l"
        clearable
      />
      <div class="op">
        <el-tooltip class="item" effect="dark" placement="top">
          <span class="el-icon-circle-check f-cb edit-icon" @click="saveEdit"></span>
          <div slot="content">保存</div>
        </el-tooltip>
        <el-tooltip class="item" effect="dark" placement="top">
          <span class="el-icon-circle-close f-c9 edit-icon" @click="cancelEdit"></span>
          <div slot="content">取消</div>
        </el-tooltip>
      </div>
    </div>
  </span>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import NationalRegionCascader from '@hbfe/jxjy-admin-components/src/national-region/national-region-cascader.vue'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  @Component({
    components: {
      NationalRegionCascader
    }
  })
  export default class extends Vue {
    regionIds = [] as string[]
    // 修改的值
    editInputValue = ''
    // 输入框显隐
    isShowEditbox = false
    // 修改后的值
    newValue = [] as string[]
    /**
     * 地区级联配置
     */
    regionProps = {
      lazy: false,
      value: 'id',
      label: 'name',
      multiple: false,
      checkStrictly: true
    }

    // 输入框提示语
    @Prop({
      type: String,
      default: '请输入证件号'
    })
    placeholder: string

    @Prop({
      type: Array,
      default: () => new Array<string>()
    })
    value: string[]

    /**
     * 地区选择器配置
     */
    @Prop({ type: Array, default: () => new Array<RegionTreeVo>() })
    regionOptions: Array<RegionTreeVo>

    @Watch('value', {
      deep: true
    })
    valChange(val: string[]) {
      if (val?.length) {
        this.regionIds = val
      }
    }

    // 修改的值
    @Watch('regionIds', {
      deep: true
    })
    inputValueChange(val: any) {
      if (val?.length) {
        this.newValue = val
      }
    }

    isShowEditBtn() {
      this.isShowEditbox = true
      this.regionIds = this.value
    }

    // 确认修改属性值
    saveEdit() {
      if (this.newValue.length < 3) return this.$message.warning('请选择完整地区路径！')
      this.$emit('input', this.newValue)
      this.$emit('saveEdit', true)
      this.isShowEditbox = false
    }

    // 放弃修改属性值
    cancelEdit() {
      this.newValue = []
      this.isShowEditbox = false
    }
  }
</script>
