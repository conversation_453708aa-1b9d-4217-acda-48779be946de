<route-params content="/:id"></route-params>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/exam-paper')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/exam-paper' }">试卷管理</el-breadcrumb-item>
      <el-breadcrumb-item>试卷详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">基本信息</span>
        </div>
        <el-row type="flex" justify="center">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form :inline="true" label-width="150px" class="m-text-form f-mt30">
              <el-col :span="12">
                <el-form-item label="试卷名称：">
                  <span class="b">{{ automaticExamPaperDetail.name }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="试卷分类：">
                  <span class="b">{{ automaticExamPaperDetail.paperCategoryName }}</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="组卷方式：">
                  <span class="b" v-if="automaticExamPaperDetail.paperPublishPatterns === 0">智能组卷</span>
                  <span class="b" v-if="automaticExamPaperDetail.paperPublishPatterns === 1">固定组卷</span>
                  <span class="b" v-if="automaticExamPaperDetail.paperPublishPatterns === 2">AB组卷</span>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="试卷总分："> <span class="b">100 分</span></el-form-item>
              </el-col>
              <!-- 和产品确定，详情不展示及格分 -->
              <!-- <el-col :span="12">
                <el-form-item label="及格分："
                  ><span class="b"
                    ><span>{{ 60 }}</span
                    >分</span
                  ></el-form-item
                >
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="考试时长："
                  ><span class="b"
                    ><span>{{ automaticExamPaperDetail.suggestionTimeLength }}</span
                    >分钟</span
                  ></el-form-item
                >
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="出题范围：">
                  <span>
                    {{ automaticExamPaperDetail.questionScopes == 1 ? '按题库出题' : '按学员课程id出题' }}
                  </span>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="已选题库："
                  ><span v-for="(item, index) in automaticExamPaperDetail.questionLibraryNames" :key="index">{{
                    item
                  }}</span></el-form-item
                >
              </el-col> -->
            </el-form>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">配置试题</span>
        </div>
        <el-row type="flex" justify="center">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form :inline="true" label-width="auto" class="m-text-form f-mt30">
              <el-col :span="12">
                <el-form-item label="大题数量：">
                  <span class="b">{{ automaticExamPaperDetail.questionCount }}</span
                  >大题</el-form-item
                >
              </el-col>
              <el-col :span="12">
                <el-form-item label="试题总数：">
                  <span class="b">{{ automaticExamPaperDetail.testQuestionCount }}</span
                  >题</el-form-item
                >
              </el-col>
              <el-col :span="12">
                <el-form-item label="涵盖题型：">
                  <span class="b" v-for="(item, index) in automaticExamPaperDetail.allTestQuestionTypes" :key="index">
                    {{ item }}</span
                  ></el-form-item
                >
              </el-col>
            </el-form>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="f-mb40">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form label-width="90px" class="m-form">
              <div
                class="m-question-set f-clear f-mt20"
                v-for="(item, index) in automaticExamPaperDetail.questionExtracts"
                :key="index"
              >
                <el-form-item class="is-text">
                  <div slot="label" class="f-f16 f-cb">第{{ stringIndex(index + 1) }}大题</div>
                  <div class="f-f16">
                    {{ item.name }}
                  </div>
                  <el-row :gutter="20" class="f-mt20">
                    <el-col :span="6"
                      >题型： <span class="f-fb">{{ item.questionType }}</span></el-col
                    >
                    <el-col :span="6"
                      >大题总分：<span class="f-fb">{{ item.totalScore }}分</span></el-col
                    >
                    <el-col :span="6"
                      >大题数量：<span class="f-fb">{{ item.questionCount }}道题</span></el-col
                    >
                  </el-row>
                </el-form-item>
              </div>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import ExamPaperUIModule from '@/store/modules-ui/exam/ExamPaperUIModule'
  import { ExamUtil } from '@/store/module/exam/common/ExamUtil'
  import utile from '@hbfe/jxjy-admin-examPaper/src/Utils'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import AutomaticExamPaperDetailVo from '@api/service/management/resource/exam-paper/query/vo/AutomaticExamPaperDetailVo'
  @Component
  export default class extends Vue {
    id = ''
    teachUnitName = '' //机构名称
    automaticExamPaperDetail = new AutomaticExamPaperDetailVo()

    get paperTypeName() {
      return ExamPaperUIModule.paperTypeName
    }

    resolverQuestionType(questionType: number) {
      return ExamUtil.resolverQuestionType(questionType)
    }
    async mounted() {
      await this.init()
    }

    // 下标转中文
    stringIndex(val: number) {
      return utile.numberToChinese(val)
    }
    /**
     * 初始化加载
     */
    async init() {
      this.id = this.$route.params.id as string

      const res = await ResourceModule.queryExamPaperFactory
        .queryExamPaperDetail(this.id)
        .queryAutomaticExamPaperDetail()
      this.automaticExamPaperDetail = res.data
      console.log(res, '试卷详情')
    }
  }
</script>
