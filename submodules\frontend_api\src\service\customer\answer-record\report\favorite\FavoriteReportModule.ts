import store from '@/store'
import { Module, VuexModule, getModule, Action, Mutation } from 'vuex-module-decorators'
import PlatformExamGateway, {
  UserFavoriteQuestionStatisticDTO,
  UserFavoriteQuestionStatisticParamDTO
} from '@api/gateway/PlatformExam'
import Response, { ResponseStatus } from '../../../../../Response'
import { Role, RoleType, Secure } from '../../../../../Secure'
import { DateRange } from '@api/service/customer/answer-record/report/enums/enums'
import SyllabusModule from '@api/service/customer/syllabus/SyllabusModule'
import Chapter from '@api/service/common/models/syllabus/Chapter'
import AnswerRecordUtils from '@api/service/customer/answer-record/report/utils/AnswerRecordUtils'

class StateCache {
  constructor(schemeId: string, issueId: string, majorId: string) {
    this.schemeId = schemeId
    this.issueId = issueId
    this.majorId = majorId
  }

  // 方案id
  schemeId: string
  // 期别id
  issueId: string
  majorId: string
  allCategoryQuestionStatistic: Array<UserFavoriteQuestionStatisticDTO> = new Array<UserFavoriteQuestionStatisticDTO>()
  allTypeQuestionStatistic: Array<UserFavoriteQuestionStatisticDTO> = new Array<UserFavoriteQuestionStatisticDTO>()
  allTypeQuestionStatisticBase: Array<UserFavoriteQuestionStatisticDTO> = new Array<UserFavoriteQuestionStatisticDTO>()
  allTypeQuestionStatisticToCompare: Array<UserFavoriteQuestionStatisticDTO> = new Array<
    UserFavoriteQuestionStatisticDTO
  >()
  // 是否需要重载，在学员触发去作答练习是置为true，在下次取数时先清空数据然后加载新数据
  needReload = true
  // 最新一次加载时间，从apollo配置的超时时间与当前时间判断，在下次取数时清空当前取数数据然后加载新数据
  latestLoadTime: Date = new Date()
}

export interface IState {
  issueStateCacheMap: Array<StateCache>
}

@Module({
  namespaced: true,
  name: 'FavoriteReportModule',
  store,
  dynamic: true
})
class FavoriteReportModule extends VuexModule implements IState {
  issueStateCacheMap = new Array<StateCache>()

  @Role([RoleType.user])
  @Action
  async init(payload: {
    schemeId: string
    issueId: string
    majorId: string
    range: DateRange
    customStartDate: Date
    customEndDate: Date
  }) {
    if (
      !this.issueStateCacheMap.find(p => p.issueId === payload.issueId) ||
      this.issueStateCacheMap.find(p => p.issueId === payload.issueId)?.needReload
    ) {
      if (!payload.schemeId || !payload.issueId || !payload.majorId || payload.range === undefined) {
        console.log('初始化收藏题参数异常' + JSON.stringify(payload))
        return new ResponseStatus(500, '参数为空，请检查')
      }
      if (!this.issueStateCacheMap.find(p => p.issueId === payload.issueId)) {
        const stateCache = new StateCache(payload.schemeId, payload.issueId, payload.majorId)
        this.setStateCacheToIssueStateCacheMap(stateCache)
      }
      // 初始化考纲信息
      const status = await SyllabusModule.init()
      if (!status.isSuccess()) {
        return status
      }
      const leafSyllabus: Array<Chapter> = SyllabusModule.getLeafSyllabusByMajorId(payload.majorId)
      const stateCache = new StateCache(payload.schemeId, payload.issueId, payload.majorId)
      const currentDate = new Date()

      const paramDTO: UserFavoriteQuestionStatisticParamDTO = new UserFavoriteQuestionStatisticParamDTO()
      paramDTO.schemeId = payload.schemeId
      paramDTO.issueId = payload.issueId
      paramDTO.tagIdList = leafSyllabus.map(leaf => leaf.id)
      paramDTO.createTimeStart = AnswerRecordUtils.getStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      paramDTO.validTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      //
      let response: Response<any> = await PlatformExamGateway.statisticUserFavoriteQuestionGroupByQuestionCategory(
        paramDTO
      )
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allCategoryQuestionStatistic = response.data
      paramDTO.createTimeStart = AnswerRecordUtils.getStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      paramDTO.validTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      response = await PlatformExamGateway.statisticUserFavoriteQuestionGroupByQuestionType(paramDTO)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allTypeQuestionStatistic = response.data
      // 基准
      paramDTO.createTimeStart = undefined
      paramDTO.validTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      response = await PlatformExamGateway.statisticUserFavoriteQuestionGroupByQuestionType(paramDTO)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allTypeQuestionStatisticBase = response.data
      // 比较
      paramDTO.createTimeStart = undefined
      paramDTO.validTimeEnd = AnswerRecordUtils.getToCompareQueryEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      response = await PlatformExamGateway.statisticUserFavoriteQuestionGroupByQuestionType(paramDTO)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allTypeQuestionStatisticToCompare = response.data
      this.setStateCacheToIssueStateCacheMap(stateCache)
    } else if (
      (this.issueStateCacheMap.find(p => p.issueId === payload.issueId)?.latestLoadTime?.getTime() || 0) <
      new Date().getTime() - 10000
    ) {
      this.setNeedReload({
        schemeId: payload.schemeId,
        issueId: payload.issueId,
        majorId: payload.majorId
      })
      await this.init(payload)
    }
    return new ResponseStatus(200)
  }

  @Mutation
  setStateCacheToIssueStateCacheMap(payload: StateCache) {
    this.issueStateCacheMap = this.issueStateCacheMap.filter(p => p.issueId !== payload.issueId)
    this.issueStateCacheMap.push(payload)
  }

  @Mutation
  setNeedReload(payload: any) {
    const stateCache = this.issueStateCacheMap.find(p => p.issueId === payload.issueId)
    if (stateCache) {
      stateCache.needReload = true
    }
  }

  /**
   * 获取状态层数据
   * @param state
   */
  get getState() {
    return (schemeId: string, issueId: string) => {
      return this.issueStateCacheMap.find(p => p.issueId === issueId)
    }
  }

  get getAllCategoryQuestionStatistic() {
    return (schemeId: string, issueId: string) => {
      return this.getState(schemeId, issueId)?.allCategoryQuestionStatistic
    }
  }

  get getAllTypeQuestionStatistic() {
    return (schemeId: string, issueId: string) => {
      return this.getState(schemeId, issueId)?.allTypeQuestionStatistic
    }
  }

  get getAllTypeQuestionStatisticBase() {
    return (schemeId: string, issueId: string) => {
      return this.getState(schemeId, issueId)?.allTypeQuestionStatisticBase
    }
  }

  get getAllTypeQuestionStatisticToCompare() {
    return (schemeId: string, issueId: string) => {
      return this.getState(schemeId, issueId)?.allTypeQuestionStatisticToCompare
    }
  }

  get favoriteQuestionCount() {
    return (schemeId: string, issueId: string) => {
      return (
        this.getAllTypeQuestionStatistic(schemeId, issueId)
          ?.map((p: UserFavoriteQuestionStatisticDTO) => p.questionCount)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  get favoriteQuestionCountChange() {
    return (schemeId: string, issueId: string) => {
      const statisticBase =
        this.getAllTypeQuestionStatisticBase(schemeId, issueId)
          ?.map((p: UserFavoriteQuestionStatisticDTO) => p.questionCount)
          .reduce((a: number, b: number) => a + b, 0) || 0
      const statisticToCompare =
        this.getAllTypeQuestionStatisticToCompare(schemeId, issueId)
          ?.map((p: UserFavoriteQuestionStatisticDTO) => p.questionCount)
          .reduce((a: number, b: number) => a + b, 0) || 0
      return statisticBase - statisticToCompare
    }
  }
}

export default getModule(FavoriteReportModule)
