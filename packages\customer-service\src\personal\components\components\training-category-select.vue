<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option
      v-for="item in trainingMajorOptions"
      :label="showLabel(item)"
      :value="item.propertyId"
      :key="item.propertyId"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import QueryPersonIndustry from '@api/service/common/basic-data-dictionary/query/person-dictionary/QueryPersonIndustry'

  @Component
  export default class extends Vue {
    selected = ''
    // 选项
    trainingMajorOptions = new Array<TrainingCategoryVo>()

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Prop({
      type: String,
      default: '请选培训专业'
    })
    placeholder: string

    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string //行业属性id
    @Prop({
      type: String,
      default: ''
    })
    industryId: string //行业id

    @Watch('value', {
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }

    // 行业id
    @Watch('industryPropertyId', {
      immediate: true
    })
    async queryParamsChange(id: string) {
      if (id) {
        await this.getTrainingMajorOptions()
      } else {
        this.trainingMajorOptions = new Array<TrainingCategoryVo>()
      }
    }

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: TrainingCategoryVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    /**
     * 获取培训专业数据
     */
    async getTrainingMajorOptions() {
      this.trainingMajorOptions = await QueryPersonIndustry.getOperationTraining(this.industryId)
    }
  }
</script>
