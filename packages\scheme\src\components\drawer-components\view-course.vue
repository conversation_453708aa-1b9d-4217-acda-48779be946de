<template>
  <el-drawer title="查看课程" :visible.sync="showDrawer" size="90%" custom-class="m-drawer">
    <div class="drawer-bd">
      <!--表格-->
      <el-table stripe :data="selectIssue.issueCourseList" max-height="500px" class="m-table f-mt10">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="课程名称" min-width="300">
          <template v-slot="{ row }">
            {{ row.courseName }}
          </template>
        </el-table-column>
        <el-table-column label="授课教师" min-width="180" align="center">
          <template v-slot="{ row }">
            {{ row.teacherName }}
          </template>
        </el-table-column>
        <el-table-column label="教师职称" min-width="180" align="center">
          <template v-slot="{ row }">
            {{ row.teacherPositionTile }}
          </template>
        </el-table-column>
        <el-table-column label="教师所在单位" min-width="180" align="center">
          <template v-slot="{ row }">
            {{ row.teacherUnitName }}
          </template>
        </el-table-column>
        <el-table-column label="课程学时" min-width="180" align="center">
          <template v-slot="{ row }">
            {{ row.coursePeriod }}
          </template>
        </el-table-column>
        <el-table-column label="授课日期" min-width="250" align="center">
          <template v-slot="{ row }">
            {{ row.teachingDate }}
          </template>
        </el-table-column>
        <el-table-column label="课授课开始时间" min-width="250" align="center">
          <template v-slot="{ row }">
            {{ showTime(row.courseBeginTime) }}
          </template>
        </el-table-column>
        <el-table-column label="课授课结束时间" min-width="250" align="center">
          <template v-slot="{ row }">
            {{ showTime(row.courseEndTime) }}
          </template>
        </el-table-column>
        <el-table-column label="课程时长(分)" min-width="180" align="center">
          <template v-slot="{ row }">
            {{ row.courseTimeLength / 60 }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button type="primary" @click="showDrawer = false">取消</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, PropSync, Vue } from 'vue-property-decorator'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import SchemeBaseInfo from '@api/service/common/scheme/model/SchemeBaseInfo'
  import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
  @Component
  export default class extends Vue {
    @PropSync('viewCourse', { type: Boolean, default: false }) showDrawer: boolean
    @Prop({ type: Object, default: () => new IssueConfigDetail() }) selectIssue: IssueConfigDetail
    @Prop({ type: Object, default: () => new SchemeBaseInfo() }) trainClassBaseInfo: SchemeBaseInfo
    OperationTypeEnum = OperationTypeEnum

    showTime(time: string) {
      return time?.substring(0, 5) ?? ''
    }
  }
</script>
