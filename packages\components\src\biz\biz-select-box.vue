<template>
  <div class="select-box-body" :class="{ disabled: disabled }">
    <div @click="blur" class="select-box-list">
      <template v-if="list.length > 0">
        <!-- 未被隐藏的模块 -->
        <slot name="prepend">
          <el-tag type="info" class="el-tag--small" closable @close.prevent="removeTag(list[0].id)" v-if="multiple">
            <p class="select-box-tag-text" :style="selectBoxTagStyle">{{ list[0].name }}</p>
          </el-tag>
          <p class="select-box-single select-box-tag-text" v-else>{{ truncatedText }}</p>
        </slot>
      </template>
      <template v-else>
        {{ placeholder }}
      </template>
      <template v-if="list.length > 1">
        <!-- 隐藏的折叠模块 -->
        <slot name="append">
          <el-popover placement="top-start" width="200" trigger="hover">
            <el-tag
              type="info"
              v-for="(item, index) in list"
              :key="item.id"
              v-show="index > 0"
              :class="{ mt10: index > 1 }"
              closable
              @close.prevent="removeTag(item.id)"
            >
              <p class="select-box-popover select-box-tag-text">{{ item.name }}</p>
            </el-tag>
            <el-tag type="info" slot="reference" class="ml10"> + {{ list.length - 1 }} </el-tag>
          </el-popover>
        </slot>
      </template>
    </div>
    <div class="circle-close" @click.stop="clear" v-if="list.length > 0">
      <i class="el-icon-circle-close"></i>
    </div>
    <div class="circle-close" v-else @click="blur">
      <i class="el-icon-arrow-down"></i>
    </div>
  </div>
</template>
<script lang="ts">
  import { Component, Inject, Prop, Vue, Watch } from 'vue-property-decorator'

  class Item {
    name: string
    id: string
  }

  @Component
  export default class extends Vue {
    /**
     * 默认选中的参数 + 回传的参数
     */
    @Prop({}) options: any

    /**
     * 分组的ID值
     */
    @Prop({
      type: String,
      required: true,
      default: 'id'
    })
    valueId: string

    /**
     * 分组的组名
     */
    @Prop({
      type: String,
      required: true,
      default: 'name'
    })
    label: string

    @Prop({
      type: String,
      default: '请选择'
    })
    placeholder: string // 占位符

    @Prop({
      type: Boolean,
      default: true
    })
    multiple: boolean // 是否多选

    @Prop({
      type: Boolean,
      default: false
    })
    disabled: boolean // 是否不可点击

    selectBoxTagWidth = 0

    selectBoxTagStyle = {
      maxWidth: this.selectBoxTagWidth ? `${this.selectBoxTagWidth}px` : '100px'
    }

    get list(): Array<Item> {
      return this.getValue(this.options)
    }
    getValue(obj: any): Array<Item> {
      let value = new Array<any>()
      if (this.isObject(obj)) {
        value = [
          {
            id: obj[this.valueId],
            name: obj[this.label]
          }
        ]
      }
      if (this.isArray(obj)) {
        value = obj.map((item: any) => {
          return {
            id: item[this.valueId],
            name: item[this.label]
          }
        })
      }
      return value
    }
    // 是否对象
    isObject(obj: any) {
      return Object.prototype.toString.call(obj) === '[object Object]'
    }
    // 是否数组
    isArray(obj: any) {
      return Object.prototype.toString.call(obj) === '[object Array]'
    }

    //多选模式下移除tag时触发
    removeTag(id: string) {
      if (!this.disabled) this.$emit('removeTag', id)
    }
    // 用户点击清空按钮时触发
    clear() {
      if (!this.disabled) this.$emit('clear')
    }
    //当 input 失去焦点时触发
    blur() {
      if (!this.disabled) this.$emit('blur')
    }

    get truncatedText() {
      // 如果显示的文字超过了最大长度20个字符，则截取并添加省略号
      return this.list[0].name.length > 15 ? this.list[0].name.substring(0, 15) + '...' : this.list[0].name
    }
  }
</script>
<style lang="scss" scoped>
  .select-box-body {
    border: solid 1px #dedfe0;
    border-radius: 4px;
    background-color: #ffffff;
    box-sizing: border-box;
    padding: 0 5px;
    color: #c4cad6;
    display: -webkit-box;
    -webkit-box-orient: horizontal;
    cursor: pointer;
    line-height: 30px;
    display: flex;
    justify-content: space-between;
  }
  .select-box-body :hover {
    border-color: rgb(62, 186, 235);
  }
  .select-box-list {
    width: inherit;
  }
  .select-box-tag-text {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    vertical-align: bottom;
    display: inline-block;
    width: inherit;
  }
  .select-box-tag {
    max-width: 100px;
  }
  .select-box-popover {
    width: 150px;
  }
  .circle-close {
    width: 10%;
    text-align: center;
  }
  .circle-close:hover {
    color: rgb(62, 186, 235);
  }
  .select-box-single {
    color: #606266;
    font-size: inherit;
  }
  .disabled {
    background: #eee;
    cursor: not-allowed;
  }
</style>
