import MsMediaResourceLearningV1, {
  LearningCourseRecordBeTokenResponse,
  LearningMediaRecordResponse
} from '@api/ms-gateway/ms-media-resource-learning-v1'
import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import QueryMyChooseCourseRuleCourse from '@api/service/customer/course/query/QueryMyChooseCourseRuleCourse'
import CourseLearningSchemeConfig from '@api/service/customer/course/query/vo/after-course-config/CourseLearningSchemeConfig'
import AfterCourseTestLastResult from '@api/service/customer/course/query/vo/AfterCourseTestLastResult'
import AntiCheatModule from '@api/service/customer/learning/anti-cheat/AntiCheatModule'
import { AntiCheatEventEnum } from '@api/service/customer/learning/anti-cheat/enums/AntiCheatEventEnum'
import { AntiResultCodeEnum } from '@api/service/customer/learning/anti-cheat/enums/AntiResultCodeEnum'
import AntiResponseHeaderModel from '@api/service/customer/learning/anti-cheat/models/AntiResponseHeaderModel'
import LearningCourseRecord from '@api/service/customer/learning/course/LearningCourseRecord'
import PlayCourseEventTypeEnum from '@api/service/customer/learning/course/play-course/enums/PlayCourseEventTypeEnum'
import RecordUserLearningMediaEventsTypeEnum from '@api/service/customer/learning/course/play-course/enums/RecordUserLearningMediaEventsTypeEnum'
import PlayCourseError from '@api/service/customer/learning/course/play-events/PlayCourseError'
import QueryAfterCourseTest from '@api/service/customer/learning/course/QueryAfterCourseTest'
import QueryStudentCourse from '@api/service/customer/learning/course/QueryStudentCourse'
import UserPlayCourse from '@api/service/customer/learning/course/UserPlayCourse'
import Chapter from '@api/service/customer/learning/course/vo/Chapter'
import Courseware from '@api/service/customer/learning/course/vo/Courseware'
import LearningCourseDetail from '@api/service/customer/learning/course/vo/LearningCourseDetail'
import StudentLearningCourseTypeEnum from '@api/service/customer/learning/scene/gates/enum/StudentLearningCourseTypeEnum'
import StudentLearningCourseScene from '@api/service/customer/learning/scene/StudentLearningCourseScene'
import QuizConfig from '@api/service/customer/train-class/query/vo/QuizConfig'
import { ResponseStatus } from '@hbfe/common'
import { bind, debounce } from 'lodash-decorators'
import moment from 'moment'

class UserLearningCourse extends UserPlayCourse {
  get studentCourseId(): string {
    return this._studentCourseId
  }

  afterCourseTestLastResult: AfterCourseTestLastResult = new AfterCourseTestLastResult()
  queryAfterCourseTest: QueryAfterCourseTest
  courseDetail: LearningCourseDetail
  // 课程学习记录的对象
  learningCourseRecord: LearningCourseRecord
  // 方案 id
  schemeId = ''
  // 参训 id = studentNo
  qualificationId = ''
  // 课程大纲课程 id
  courseOfCourseTrainingOutlineId = ''
  outlineId = ''
  courseLearningSchemeConfig: CourseLearningSchemeConfig = new CourseLearningSchemeConfig()
  queryMyChooseCourseRuleCourse: QueryMyChooseCourseRuleCourse
  // 方案类型
  schemeType: StudentLearningCourseTypeEnum
  private _studentCourseId: string
  courseId: string
  studentLearningCourseScene: StudentLearningCourseScene
  checkStudyResult: LearningCourseRecordBeTokenResponse = new LearningCourseRecordBeTokenResponse()

  /**
   * 启动标识 （这个flag，是为了解决用户快进快出播放页，在Init事件创建计时器前就销毁了页面掉了销毁计时器，到时后续Init再创建出一个计时器无法销毁）
   */
  flag = false

  async init(
    coursePlayToken: string,
    withoutVerify = false,
    schemeId?: string,
    qualificationId?: string,
    courseId?: string,
    schemeType?: StudentLearningCourseTypeEnum,
    outlineId?: string,
    courseIdv2?: string
  ) {
    console.info('🚀 ~ file:UserLearningCourse method:init line:70 -----', courseId)
    this.flag = true
    await super.init(coursePlayToken)
    this.learningCourseRecord = new LearningCourseRecord(coursePlayToken)

    // 开始根据学习 token 获取多媒体学习记录。并将记录填充到课件媒体资源上面
    try {
      await this.learningCourseRecord.queryCoursewareLearningRecords()
      if (this.learningCourseRecord?.learningCode?.code == 'L90001') {
        this.emit(PlayCourseEventTypeEnum.onlineLearningRuleTimeout, this.learningCourseRecord)
      }
    } catch (e) {
      this.emit(PlayCourseEventTypeEnum.error, new PlayCourseError(PlayCourseEventTypeEnum.prepareFail, e.message))
      return
    }

    // 遍历章节
    if (this.learningCourseRecord) {
      this.courseDetail.schedule = this.learningCourseRecord.schedule
      this.courseDetail.chapters.forEach((chapter: Chapter) => {
        chapter.coursewares.forEach((courseware: Courseware) => {
          // 查询当个课件的学习记录
          const coursewareRecord = this.learningCourseRecord.getCoursewareRecordByCoursewareId(courseware.id)
          if (coursewareRecord) {
            const findMediaResourceRecord = coursewareRecord.learningMediaRecords.find(
              (learningMediaRecord: LearningMediaRecordResponse) => {
                return learningMediaRecord.coursewareId === courseware.id
              }
            )
            courseware.multiMediaId = findMediaResourceRecord.multiMediaId
            if (findMediaResourceRecord) {
              courseware.setSchedule(findMediaResourceRecord.schedule)
            }
          }
        })
      })
    }

    // 获取最后一次播放的媒体
    let playMedia
    // 开关启动学习
    if (this.learningCourseRecord.lastPlayMedia) {
      playMedia = this.learningCourseRecord.lastPlayMedia
    } else {
      const firstCourseware = this.courseDetail.chapters[0].coursewares[0]
      playMedia = {
        coursewareId: firstCourseware.id,
        multiMediaId: firstCourseware.multiMediaId
      }
    }

    this.studentLearningCourseScene = new StudentLearningCourseScene(this.learningCourseRecord.token, this)

    try {
      await this.studentLearningCourseScene.enterPrevScene(playMedia.coursewareId, playMedia.multiMediaId)
      // 检测是否有最后一次播放的媒体
      if (playMedia) {
        await this.changeCurrentPlayMedia(
          this.getLastPlayIndexByCoursewareId(playMedia.coursewareId, playMedia.multiMediaId)
        )
      } else {
        // 没有默认播放第一个章节第一个课件
        await this.changeCurrentPlayMedia({
          chapterIndex: 0,
          coursewareIndex: 0
        })
      }
      // 监听播放的提交
      this.studentLearningCourseScene.on(RecordUserLearningMediaEventsTypeEnum.commitSuccess, () => {
        const timingToken = this.studentLearningCourseScene.lastLearningResult
        if (timingToken) {
          this.setCurrentPlayCoursewareSchedule(timingToken.coursewareSchedule)
          this.courseDetail.schedule = timingToken.courseSchedule
        }
      })

      this.emit(PlayCourseEventTypeEnum.init)
      this.schemeId = schemeId
      this.qualificationId = qualificationId
      this.schemeType = schemeType
      this.outlineId = outlineId
      if (schemeType === StudentLearningCourseTypeEnum.ChooseCourseRule) {
        this._studentCourseId = courseId
        this.courseId = courseIdv2
      } else {
        this.courseId = courseIdv2
      }
      try {
        // 非自主选课可以直接拿课程 id 去做查询
        if (this.schemeType !== StudentLearningCourseTypeEnum.ChooseCourseRule) {
          const queryStudentCourse = new QueryStudentCourse(this.qualificationId, courseId, this.schemeType)
          if (!this._studentCourseId) {
            this._studentCourseId = await queryStudentCourse.queryStudentCourseId()
          }
        }

        // 实例化课后测验查询对象
        this.queryAfterCourseTest = new QueryAfterCourseTest(
          this._studentCourseId,
          this.qualificationId,
          this.schemeType,
          this.courseId
        )
        this.afterCourseTestLastResult = await this.queryAfterCourseTest.queryAfterCourseTestLastResult()
      } catch (e) {
        // no
        console.log(e)
      }
      await this.querySchemeConfig()
    } catch (e) {
      console.log(e)
      if (e.code === 10006) {
        this.emit(PlayCourseEventTypeEnum.error, new PlayCourseError(PlayCourseEventTypeEnum.error, e.message))
      } else {
        this.emit(PlayCourseEventTypeEnum.error)
      }
    }
  }

  @bind
  @debounce(100)
  async playTargetMedia(event: { chapterIndex: number; coursewareIndex: number }): Promise<boolean> {
    try {
      const chapter = this.courseDetail.chapters[event.chapterIndex]
      const courseware = chapter.coursewares[event.coursewareIndex]
      this.emit(PlayCourseEventTypeEnum.beforeChangePlayCourseware)
      await this.studentLearningCourseScene.enterPrevScene(courseware.id, courseware.multiMediaId)
      await this.changeCurrentPlayMedia(event)
      return true
    } catch (e) {
      console.log(e, 'playTargetMedia---eee')
      if (e.code === 10006) {
        this.emit(PlayCourseEventTypeEnum.error, new PlayCourseError(PlayCourseEventTypeEnum.error, e.message))
      } else {
        this.emit(PlayCourseEventTypeEnum.error)
      }
    }
  }

  private async querySchemeConfig(): Promise<CourseLearningSchemeConfig> {
    let prefix
    switch (this.schemeType) {
      case StudentLearningCourseTypeEnum.ChooseCourseRule:
        prefix = 'chooseCourseLearning'
        break
      case StudentLearningCourseTypeEnum.AutonomousCourse:
        prefix = 'autonomousCourseLearning'
        break
      case StudentLearningCourseTypeEnum.InterestCourse:
        prefix = 'interestCourseLearning'
        break
    }

    const result = await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.getSchemeConfigInServicer({
      schemeId: this.schemeId,
      needField: ['trainingBeginDate', 'trainingEndDate', `${prefix}.config.courseQuizConfig`, `${prefix}.id`]
    })

    this.courseLearningSchemeConfig.from(JSON.parse(result.data.schemeConfig) as CourseLearningSchemeConfig)
    return this.courseLearningSchemeConfig
  }

  /**
   * 设置当前课件的播放进度
   * @param schedule
   * @private
   */
  setCurrentPlayCoursewareSchedule(schedule: number) {
    const chapter = this.courseDetail.chapters[this.currentPlayIndex.chapterIndex]
    const courseware = chapter.coursewares[this.currentPlayIndex.coursewareIndex]
    courseware.schedule = schedule
  }

  /**
   * 设置当前播放的课件之前已经学习过了
   * @deprecated
   */
  setCurrentPlayCoursewareIsLearnedBefore() {
    console.log(123)
  }

  /**
   * 检验是否是在培训时间范围
   * @private
   */
  private validateIsOnTrainingDateRange(): ResponseStatus {
    const isAfter = moment(this.courseLearningSchemeConfig.trainingBeginDate).isBefore(moment())
    const isBefore = moment(this.courseLearningSchemeConfig.trainingEndDate).isAfter(moment())
    if (!isAfter || !isBefore) {
      return new ResponseStatus(500, '不在培训时间段，无法进行课后测验！')
    }
    return new ResponseStatus(200)
  }

  /**
   * 检验是否允许做课后测验
   */
  async canDoAfterCourseTest() {
    const isOnTrainingDate = this.validateIsOnTrainingDateRange()
    if (isOnTrainingDate.code !== 200) {
      return false
    }

    return !this.afterCourseTestLastResult?.isQualified
  }

  /**
   *
   * // {
   * //   qualificationId: this.userLearningCourse.qualificationId,
   * //   schemeId: this.userLearningCourse.schemeId,
   * //   learningId: this.trainClassDetail.learningTypeModel.courseLearning.learningTypeId,
   * //   courseLearnId: obj.courseLearnId,
   * //   answerConfig: JSON.stringify({
   * //   quizConfigModel: this.trainClassDetail.learningTypeModel.courseLearning.quizConfigModel
   * // })
   * // }
   */
  async goTest(): Promise<{
    status: ResponseStatus
    data?: {
      qualificationId: string
      schemeId: string
      learningId?: string
      courseLearnId?: string
      answerConfig?: string
      studentCourseId?: string
    }
  }> {
    const isOnTrainingDate = this.validateIsOnTrainingDateRange()
    if (isOnTrainingDate.code !== 200) {
      return {
        status: isOnTrainingDate
      }
    }
    const afterCourseTestLastResult = await this.queryAfterCourseTest.queryAfterCourseTestLastResult()
    if (afterCourseTestLastResult.isQualified) {
      return {
        status: new ResponseStatus(500, '课后测验已合格，无需重复测验！')
      }
    }

    return {
      status: new ResponseStatus(200),
      data: this.getTestAnalyseRequiredParams()
    }
  }

  destroy() {
    this.flag = false
    this.studentLearningCourseScene?.destroy()
    super.destroy()
  }

  @bind
  async playNext(): Promise<void> {
    await this.playTargetMedia(this.getNextIndex())
  }

  @bind
  async playPrev(): Promise<void> {
    await this.playTargetMedia(this.getPrevIndex())
  }

  /**
   * 获取去测验解析页面需要的参数
   */
  getTestAnalyseRequiredParams() {
    return {
      qualificationId: this.qualificationId,
      schemeId: this.schemeId,
      courseLearnId: this._studentCourseId,
      learningId: this.courseLearningSchemeConfig.chooseCourseLearning.id,
      answerConfig: JSON.stringify({
        quizConfigModel: QuizConfig.from(this.courseLearningSchemeConfig.afterCourseConfig)
      }),
      outlineId: this.outlineId,
      schemeType: this.schemeType,
      courseId: this.courseId
    }
  }

  isFirstComplete() {
    return this.studentLearningCourseScene.isFirstComplete()
  }

  hasTest() {
    return !!this?.courseLearningSchemeConfig?.afterCourseConfig?.quizConfig?.id
  }

  /**
   * 判断课程的进度是否大于课后测验的配置进度要求
   */
  courseScheduleIsBigThanAfterCourseTestConfig() {
    const courseSchedule = this.courseDetail.schedule
    const minCourseSchedule = this.courseLearningSchemeConfig.afterCourseConfig.precondition.minCourseSchedule
    return courseSchedule >= minCourseSchedule
  }

  /**
   * 挂载对进入学习监管的事件监听
   */
  mountEnterLearningAntiListen() {
    AntiCheatModule.on(AntiCheatEventEnum.ENTER_LEARNING_ANTI, (param: AntiResponseHeaderModel) => {
      if (param.antiCode == AntiResultCodeEnum.CODE_NEED_TRIG) {
        this.emit(PlayCourseEventTypeEnum.needEnterAntiCheat, param)
      } else {
        this.emit(PlayCourseEventTypeEnum.notEnterAntiCheat, param)
      }
    })
  }

  /**
   * 挂载学习过程监管的事件监听
   */
  mountLearningAntiListen() {
    AntiCheatModule.on(AntiCheatEventEnum.LEARNING_ANTI, (param: AntiResponseHeaderModel) => {
      if (param.antiCode == AntiResultCodeEnum.CODE_NEED_TRIG) {
        this.emit(PlayCourseEventTypeEnum.needAntiCheat, param)
      } else if (param.antiCode == AntiResultCodeEnum.CODE_NO_TRIG) {
        this.emit(PlayCourseEventTypeEnum.notAntiCheat, param)
      } else {
        this.emit(PlayCourseEventTypeEnum.antiError, param)
      }
    })
  }

  /**
   * 卸载监管中控所有时间
   */
  unloadLearningAntiListen() {
    AntiCheatModule.removeAllListeners()
    this.removeAllListeners()
  }

  /**
   * 通过获取token口触发进入学习监管的事件
   */
  async verifyEnterLearningAntiCheat(coursePlayToken: string): Promise<boolean> {
    const result = await MsMediaResourceLearningV1.prepareCourseLearningTimingBeToken(coursePlayToken)
    this.checkStudyResult = result.data
    if (result?.data?.courseId) {
      return true
    } else {
      return false
    }
  }
}

export default UserLearningCourse
