"""独立部署的微服务,K8S服务名:ms-learningscheme-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""修改参训资格住宿信息
		@param request:
		<AUTHOR> By Cb
		@since 2025/4/27 17:21
	"""
	updateQualificationProperties(request:ChangeAccommodationInfoRequest):Void
}
"""修改住宿信息
	<AUTHOR>
"""
input ChangeAccommodationInfoRequest @type(value:"com.fjhb.ms.learningscheme.v1.kernel.gateway.graphql.request.ChangeAccommodationInfoRequest") {
	"""参训资格ID"""
	trainingQualificationId:String
	"""住宿类型
		0-无需住宿
		1-单人住宿
		2-合住
	"""
	accommodationType:Int!
}

scalar List
