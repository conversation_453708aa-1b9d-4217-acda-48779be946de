import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-servicer-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-servicer-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * <AUTHOR>
@description:板块
@date 2024/2/27 20:25
 */
export class Plate {
  /**
   * 板块id
   */
  id?: string
  /**
   * 板块名称
   */
  name?: string
  /**
   * 栏目展示名称
   */
  disPlayName?: string
  /**
   * 父板块id
   */
  parentId?: string
  /**
   * 板块类型
@see PlateTypes
   */
  plateType: number
  /**
   * 来源类型
@see PlateSourceTypes
   */
  sourceType: number
  /**
   * 链接
   */
  link?: string
  /**
   * 业务code
   */
  code?: string
  /**
   * 引用id(咨询类型的栏目，引用的是资讯分类id)
   */
  referenceId?: string
  /**
   * 是否允许添加子级
   */
  allowChildren: boolean
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 排序
   */
  sort?: number
  /**
   * 创建时间
   */
  createdTime?: string
}

/**
 * 友情链接请求
<AUTHOR>
 */
export class FriendLinkRequest {
  /**
   * 友情链接标题
   */
  title?: string
  /**
   * 友情链接图片
   */
  picture?: string
  /**
   * 友情链接类型
   */
  friendLinkType: number
  /**
   * 链接
   */
  link?: string
  /**
   * 排序
   */
  sort?: number
}

/**
 * 客户端
<AUTHOR>
 */
export class Client {
  /**
   * 客户端类型
@see ClientTypes
   */
  clientType: number
  /**
   * 门户类型
@see PortalTypes
   */
  portalType: boolean
  /**
   * 域名类型
@see DomainNameTypes
   */
  domainNameType: number
  /**
   * 域名
   */
  domainName?: string
  /**
   * 前端模板id
   */
  portalTemplateId?: string
  /**
   * cnzz信息
   */
  cnzz?: string
  /**
   * 目录名
   */
  dirName?: string
}

/**
 * 友情链接
<AUTHOR>
 */
export class FriendLink {
  /**
   * 友情链接id
   */
  id?: string
  /**
   * 友情链接标题
   */
  title?: string
  /**
   * 友情链接图片
   */
  picture?: string
  /**
   * 友情链接类型
   */
  friendLinkType: number
  /**
   * 链接
   */
  link?: string
  /**
   * 排序
   */
  sort?: number
  /**
   * 创建时间
   */
  createTime?: string
}

export class CVendorForTInstitutionRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 渠道商编号
   */
  channelVendorId?: string
}

export class CancelAllCVendorAuthPromotionTrainingRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 培训班编号
   */
  trainingId?: string
}

/**
 * 渠道商创建信息
<AUTHOR>
@since 2021/7/7
 */
export class ChannelVendorCreateRequest {
  /**
   * 渠道商名称
   */
  name: string
  /**
   * 所属企业账户编号
   */
  accountId: string
  /**
   * 所在地区
   */
  region?: string
  /**
   * 渠道商LOGO
   */
  logo?: string
  /**
   * 联系人
   */
  contactPerson?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 渠道商简介
   */
  abouts?: string
  /**
   * 渠道商介绍
   */
  content?: string
  /**
   * 电子公章文件路径
   */
  electronicSealPath?: string
}

/**
 * 渠道商更新信息
<AUTHOR>
@since 2021/7/7
 */
export class ChannelVendorUpdateRequest {
  /**
   * 渠道商编号
   */
  channelVendorId: string
  /**
   * 渠道商名称，为null，表示不更新
   */
  name?: string
  /**
   * 所在地区，为null，表示不更新
   */
  region?: string
  /**
   * 渠道商Logo，为null，表示不更新
   */
  logo?: string
  /**
   * 联系人，为null，表示不更新
   */
  contactPerson?: string
  /**
   * 手机号，为null，表示不更新
   */
  phone?: string
  /**
   * 渠道商简介，为null，表示不更新
   */
  abouts?: string
  /**
   * 渠道商介绍，为null，表示不更新
   */
  content?: string
  /**
   * 电子公章文件路径，为null，表示不更新
   */
  electronicSealPath?: string
}

/**
 * <AUTHOR> [2023/8/3 16:20]
 */
export class CommonMenuCreateRequest {
  /**
   * 网校id
   */
  servicerId?: string
  /**
   * 单位id
   */
  unitId?: string
  /**
   * 栏目名称
   */
  menuName?: string
  /**
   * 父栏目id
   */
  parentId?: string
  /**
   * 栏目链接
   */
  link?: string
  /**
   * 业务code
   */
  code?: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 合同供应商停用请求
 */
export class ContractProviderDisableRequest {
  /**
   * 合同供应商编号
   */
  contractProviderId: string
}

/**
 * 合同供应商启用请求
 */
export class ContractProviderEnableRequest {
  /**
   * 合同供应商编号
   */
  contractProviderId: string
}

/**
 * 课件供应商信息
<AUTHOR>
@since 2021/7/8
 */
export class CoursewareSupplierCreateRequest {
  /**
   * 所属企业主账号编号
   */
  accountId: string
  /**
   * 课件供应商名称
   */
  name: string
  /**
   * 所在地区
   */
  region?: string
  /**
   * 课件供应商Logo
   */
  logo?: string
  /**
   * 联系人
   */
  contactPerson?: string
  /**
   * 联系电话
   */
  phone?: string
  /**
   * 课件供应商简介
   */
  abouts?: string
  /**
   * 课件供应商介绍
   */
  content?: string
  /**
   * 电子公章文件路径
   */
  electronicSealPath?: string
}

/**
 * 课件供应商信息
<AUTHOR>
@since 2021/7/8
 */
export class CoursewareSupplierUpdateRequest {
  /**
   * 课件供应商编号
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称，为null，表示不更新
   */
  name?: string
  /**
   * 所在地区，为null，表示不更新
   */
  region?: string
  /**
   * 课件供应商Logo，为null，表示不更新
   */
  logo?: string
  /**
   * 联系人，为null，表示不更新
   */
  contactPerson?: string
  /**
   * 联系电话，为null，表示不更新
   */
  phone?: string
  /**
   * 课件供应商简介，为null，表示不更新
   */
  abouts?: string
  /**
   * 课件供应商介绍，为null，表示不更新
   */
  content?: string
  /**
   * 电子公章文件路径，为null，表示不更新
   */
  electronicSealPath?: string
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-03-05  21:10
@Description:
 */
export class EnterpriseH5PortalUpdateRequest {
  /**
   * 院校logo
   */
  logo?: string
  /**
   * 首页底图
   */
  homepageBasemap?: string
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-03-05  21:10
@Description:
 */
export class MechanicAcademyH5PortalUpdateRequest {
  /**
   * 院校logo
   */
  logo?: string
  /**
   * 首页底图
   */
  homepageBasemap?: string
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-02-29  15:30
@Description:
 */
export class MenuCreateRequest {
  /**
   * 栏目名称
   */
  menuDisPlayName: string
  /**
   * 父栏目id
   */
  menuParentId: string
  /**
   * 栏目类型
   */
  menuType: number
  /**
   * 栏目链接
   */
  menuLink: string
  /**
   * 业务code
   */
  code: string
  /**
   * 排序
   */
  sort: number
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-02-29  15:30
@Description:
 */
export class MenuEnableRequest {
  /**
   * 栏目id
   */
  menuId: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 客户端类型
@see ClientTypes
   */
  clientType: number
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-03-05  19:23
@Description:
 */
export class MenuUpdateRequest {
  /**
   * 栏目id
   */
  menuId: string
  /**
   * 栏目名称
   */
  menuDisPlayName: string
  /**
   * 客户端类型
@see ClientTypes
   */
  clientType: number
  /**
   * 父栏目id
   */
  menuParentId?: string
  /**
   * 栏目类型
   */
  menuType: number
  /**
   * 栏目链接
   */
  menuLink?: string
  /**
   * 业务code
   */
  code?: string
  /**
   * 分类
   */
  sort: number
}

/**
 * 参训单位信息
 */
export class ParticipatingUnitCreateRequest {
  /**
   * 所属企业主账号编号
   */
  accountId: string
  /**
   * 参训单位名称
   */
  name: string
  /**
   * 所在地区
   */
  region?: string
  /**
   * 参训单位Logo
   */
  logo?: string
  /**
   * 联系人
   */
  contactPerson?: string
  /**
   * 联系电话
   */
  phone?: string
  /**
   * 参训单位简介
   */
  abouts?: string
  /**
   * 参训单位介绍
   */
  content?: string
  /**
   * 电子公章文件路径
   */
  electronicSealPath?: string
}

/**
 * 参训单位信息
 */
export class ParticipatingUnitUpdateRequest {
  /**
   * 参训单位编号
   */
  participatingUnitId: string
  /**
   * 参训单位名称，为null，表示不更新
   */
  name?: string
  /**
   * 所在地区，为null，表示不更新
   */
  region?: string
  /**
   * 参训单位Logo，为null，表示不更新
   */
  logo?: string
  /**
   * 联系人，为null，表示不更新
   */
  contactPerson?: string
  /**
   * 联系电话，为null，表示不更新
   */
  phone?: string
  /**
   * 参训单位简介，为null，表示不更新
   */
  abouts?: string
  /**
   * 参训单位介绍，为null，表示不更新
   */
  content?: string
  /**
   * 电子公章文件路径，为null，表示不更新
   */
  electronicSealPath?: string
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-02-29  15:30
@Description:
 */
export class PlateEnableRequest {
  /**
   * 板块id
   */
  plateId: string
  /**
   * 是否启用
   */
  enabled: boolean
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-02-29  16:16
@Description:
 */
export class PlateSortRequest {
  plateList: Array<Plate>
}

/**
 * 更新栏目请求
<AUTHOR>
 */
export class PlateUpdateRequest {
  /**
   * 板块id
   */
  plateId: string
  /**
   * 板块名称
   */
  plateDisPlayName: string
}

/**
 * 门户轮播图
<AUTHOR>
@since 2021/7/13
 */
export class PortalBanner {
  /**
   * 轮播图编号，新建时可以不填
   */
  id?: string
  /**
   * 轮播图名称
   */
  name?: string
  /**
   * 轮播图路径
   */
  path?: string
  /**
   * 链接地址
   */
  link?: string
  /**
   * 轮播图排序
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-03-15  17:58
@Description:
 */
export class PublishEnterprisePortalRequest {
  /**
   * 企业编号
   */
  enterpriseId: string
  /**
   * 门户类型
<p>
1-WEB
2-移动端
   */
  portalType: number
}

/**
 * 发布培训机构门户请求
<AUTHOR>
 */
export class PublishTrainingInstitutionPortalRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
  /**
   * 门户类型
<p>
1-WEB
2-移动端
   */
  portalType: number
}

/**
 * 培训机构恢复与课件供应商合作信息
<AUTHOR>
@since 2021/7/8
 */
export class ResumeSignedCSupplierForTInstitutionRequest {
  /**
   * 培训机构与课件供应商签约编号
   */
  servicerContractId?: string
}

/**
 * 培训机构恢复与渠道商合作信息
<AUTHOR>
@since 2021/7/8
 */
export class ResumeSignedCVendorForTInstitutionRequest {
  /**
   * 培训机构与渠道商签约编号
   */
  servicerContractId?: string
}

/**
 * 参训单位恢复与培训机构合作信息
 */
export class ResumeSignedTInstitutionForPUnitRequest {
  /**
   * 参训单位与培训机构签约编号
   */
  servicerContractId?: string
}

/**
 * 签约成为培训机构的课件供应商合作
<AUTHOR>
@since 2021/7/8
 */
export class SignUpCSupplierForTInstitutionRequest {
  /**
   * 课件供应商编号
   */
  coursewareSupplierId?: string
  /**
   * 合约内容
   */
  content?: string
}

/**
 * 签约成为培训机构的渠道商合作
<AUTHOR>
@since 2021/7/8
 */
export class SignUpCVendorForTInstitutionRequest {
  /**
   * 渠道商编号
   */
  channelVendorId?: string
  /**
   * 合约内容
   */
  content?: string
}

export class SignUpTInstitutionForPUnitRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 合约内容
   */
  content?: string
}

/**
 * 培训机构中止与课件供应商合作信息
<AUTHOR>
@since 2021/7/8
 */
export class SuspendSignedCSupplierForTInstitutionRequest {
  /**
   * 培训机构与课件供应商签约编号
   */
  servicerContractId?: string
}

/**
 * 培训机构中止与渠道商合作信息
<AUTHOR>
@since 2021/7/8
 */
export class SuspendSignedCVendorForTInstitutionRequest {
  /**
   * 培训机构与渠道商签约编号
   */
  servicerContractId?: string
}

/**
 * 参训单位中止与培训机构合作信息
 */
export class SuspendSignedTInstitutionForPUnitRequest {
  /**
   * 参训单位与培训机构签约编号
   */
  servicerContractId?: string
}

/**
 * 培训机构轮播图列表保存请求
<AUTHOR>
 */
export class TrainingInstitutionBannerListSaveRequest {
  /**
   * 门户类型
   */
  portalType: number
  /**
   * 培训机构轮播图保存请求
   */
  bannerSaveRequestList?: Array<TrainingInstitutionBannerSaveRequest>
}

/**
 * 培训机构轮播图保存请求
<AUTHOR>
 */
export class TrainingInstitutionBannerSaveRequest {
  /**
   * 轮播图名称
   */
  bannerName?: string
  /**
   * 轮播图链接
   */
  bannerLink?: string
  /**
   * 轮播图路径
   */
  bannerPath?: string
  /**
   * 分类
   */
  sort: number
}

/**
 * 培训机构创建信息
<AUTHOR>
@since 2021/7/7
 */
export class TrainingInstitutionCreateRequest {
  /**
   * 培训机构名称
   */
  name: string
  /**
   * 所属企业账户编号
   */
  accountId: string
  /**
   * 所在地区
   */
  region?: string
  /**
   * 联系人
   */
  contactPerson?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 培训机构LOGO
   */
  logo?: string
  /**
   * 培训机构简介
   */
  abouts?: string
  /**
   * 培训机构介绍
   */
  content?: string
  /**
   * 电子公章文件路径
   */
  electronicSealPath?: string
}

/**
 * 培训机构禁用请求
 */
export class TrainingInstitutionDisableRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
}

/**
 * 培训机构启用请求
 */
export class TrainingInstitutionEnableRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
}

/**
 * 培训机构H门户更新请求
<AUTHOR>
 */
export class TrainingInstitutionH5PortalUpdateRequest {
  /**
   * 培训机构id
   */
  trainingInstitutionId?: string
  /**
   * 是否提供服务号
   */
  idProvideServiceAccount: boolean
}

/**
 * 新增咨询类别栏目栏目请求
<AUTHOR>
 */
export class TrainingInstitutionMenuCreateRequest {
  /**
   * 栏目名称
   */
  menuName?: string
  /**
   * 父栏目id
   */
  parentId?: string
  /**
   * 栏目链接
   */
  link?: string
  /**
   * 业务code
   */
  code?: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 更新栏目请求
<AUTHOR>
 */
export class TrainingInstitutionMenuUpdateRequest {
  /**
   * 栏目id
   */
  menuId?: string
  /**
   * 栏目名称
   */
  menuName?: string
  /**
   * 父栏目id
   */
  menuParentId?: string
  /**
   * 栏目类型
   */
  menuType: number
  /**
   * 栏目链接
   */
  menuLink?: string
  /**
   * 业务code
   */
  code?: string
  /**
   * 分类
   */
  sort: number
}

/**
 * 培训机构门户创建信息
<AUTHOR>
@since 2021/7/13
 */
export class TrainingInstitutionPortalCreateRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
  /**
   * 门户类型
<p>
1-WEB
2-移动端
   */
  portalType: number
  /**
   * 门户标题
   */
  title?: string
  /**
   * 门户logo
   */
  logo?: string
  /**
   * 浏览器图标
   */
  icon?: string
  /**
   * 移动二维码
   */
  mobileQRCode?: string
  /**
   * 客服电话图片
   */
  CSPhonePicture?: string
  /**
   * 客服电话
   */
  CSPhone?: string
  /**
   * 客服咨询时间
   */
  CSCallTime?: string
  /**
   * 企业客服微信
   */
  CSWechat?: string
  /**
   * 在线客服代码内容id
   */
  CSOnlineCodeId?: string
  /**
   * 培训流程图片
   */
  trainingFlowPicture?: string
  /**
   * 底部内容(底部落款)
   */
  footContent?: string
  /**
   * 友情链接类型
   */
  friendLinkTypes: number
  /**
   * 友情链接集合
   */
  friendLinks?: Array<FriendLink>
  /**
   * 主题颜色
   */
  themeColor?: string
  /**
   * 宣传口号
   */
  slogan?: string
  /**
   * 门户简介说明内容
   */
  content?: string
  /**
   * 域名
   */
  domainName?: string
  /**
   * 轮播图列表
   */
  banners?: Array<PortalBanner>
  /**
   * cnzz信息
   */
  cnzz?: string
  /**
   * 目录名
   */
  dirName?: string
}

/**
 * 培训机构门户创建信息
<AUTHOR>
@since 2021/7/13
 */
export class TrainingInstitutionPortalRemoveRequest {
  /**
   * 门户编号
   */
  id: string
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
}

/**
 * 更新门户（web+h5）主题颜色请求
<AUTHOR>
 */
export class TrainingInstitutionPortalThemeColorUpdateRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
  /**
   * 主题颜色
   */
  themeColor?: string
}

/**
 * 培训机构门户创建信息
<AUTHOR>
@since 2021/7/13
 */
export class TrainingInstitutionPortalUpdateRequest {
  /**
   * 门户编号
   */
  id: string
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
  /**
   * 门户标题
   */
  title?: string
  /**
   * 宣传口号
   */
  slogan?: string
  /**
   * 门户简介说明内容
   */
  content?: string
  /**
   * 域名
   */
  domainName?: string
  /**
   * 轮播图列表
   */
  banners?: Array<PortalBanner>
  /**
   * cnzz信息
   */
  cnzz?: string
  /**
   * 目录名
   */
  dirName?: string
}

/**
 * 培训机构创建信息
<AUTHOR>
@since 2021/7/7
 */
export class TrainingInstitutionUpdateRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
  /**
   * 培训机构名称，为null，表示不更新
   */
  name?: string
  /**
   * 所在地区，为null，表示不更新
   */
  region?: string
  /**
   * 联系人，为null，表示不更新
   */
  contactPerson?: string
  /**
   * 手机号，为null，表示不更新
   */
  phone?: string
  /**
   * 培训机构LOGO，为null，表示不更新
   */
  logo?: string
  /**
   * 培训机构简介，为null，表示不更新
   */
  abouts?: string
  /**
   * 培训机构介绍，为null，表示不更新
   */
  content?: string
  /**
   * 电子公章文件路径，为null，表示不更新
   */
  electronicSealPath?: string
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-02-26  15:05
@Description: 底部落款
 */
export class TrainingInstitutionWebPortalFootContent {
  /**
   * 主办单位
   */
  organizers?: string
  /**
   * 主管单位
   */
  sponsor?: string
  /**
   * 运营支持
   */
  operationSupport?: string
  /**
   * 备案号
   */
  recordNumber?: string
  /**
   * 许可证
   */
  permit?: string
  /**
   * 网安备案号
   */
  networkSecurityRegistrationNumber?: string
}

/**
 * 培训机构web门户修改请求
<AUTHOR>
 */
export class TrainingInstitutionWebPortalUpdateRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
  /**
   * 客户端
   */
  clients?: Array<Client>
  /**
   * 门户标题
   */
  title?: string
  /**
   * 门户logo
   */
  logo?: string
  /**
   * 浏览器图标
   */
  icon?: string
  /**
   * 移动二维码
   */
  mobileQRCode?: string
  /**
   * 移动二维码来源标识
@see com.fjhb.domain.basicdata.api.servicer.consts.MobileQRCodeSign
   */
  mobileQRCodeSign?: number
  /**
   * 客服电话图片
   */
  csPhonePicture?: string
  /**
   * 客服电话
   */
  csPhone?: string
  /**
   * 客服咨询时间
   */
  csCallTime?: string
  /**
   * 企业客服微信
   */
  csWechat?: string
  /**
   * 在线客服代码内容id
   */
  csOnlineCodeId?: string
  /**
   * 培训流程图片
   */
  trainingFlowPicture?: string
  /**
   * 底部内容id(底部落款id)
   */
  footContentId?: string
  /**
   * 底部内容(底部落款)
   */
  footContent?: string
  /**
   * 底部落款(新)
   */
  trainingInstitutionWebPortalFootContent?: TrainingInstitutionWebPortalFootContent
  /**
   * 友情链接类型
@see com.fjhb.domain.basicdata.api.servicer.consts.FriendLinkTypes
1-文本  2-图片
   */
  friendLinkTypes: number
  /**
   * 友情链接集合
   */
  friendLinks?: Array<FriendLinkRequest>
  /**
   * cnzz信息
   */
  cnzz?: string
  /**
   * 目录名
   */
  dirName?: string
  /**
   * 强制更新（前端传什么，后端就更新什么）
   */
  compulsionUpdate: boolean
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-03-15  17:58
@Description:
 */
export class UnpublishEnterprisePortalRequest {
  /**
   * 企业编号
   */
  enterpriseId: string
  /**
   * 门户类型
<p>
1-WEB
2-移动端
   */
  portalType: number
}

/**
 * 取消发布培训机构门户请求
<AUTHOR>
 */
export class UnpublishTrainingInstitutionPortalRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
  /**
   * 门户类型
<p>
1-WEB
2-移动端
   */
  portalType: number
}

export class VerifyAuthCVendorPromotionTrainingRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 渠道商编号
   */
  channelVendorId?: string
  /**
   * 培训班编号
   */
  trainingId?: string
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-02-28  15:05
@Description:
 */
export class WebPortalUpdateRequest {
  /**
   * 门户logo
   */
  logo?: string
  /**
   * 门户标题
   */
  title?: string
  /**
   * 移动二维码
   */
  mobileQRCode?: string
  /**
   * 底部落款(新)
   */
  trainingInstitutionWebPortalFootContent?: TrainingInstitutionWebPortalFootContent
  /**
   * 友情链接类型
@see com.fjhb.domain.basicdata.api.servicer.consts.FriendLinkTypes
1-文本  2-图片
   */
  friendLinkTypes: number
  /**
   * 友情链接集合
   */
  friendLinks?: Array<FriendLinkRequest>
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request
@Author: XuCenHao
@CreateTime: 2024-04-10  11:30
@Description:
 */
export class BuildInPlateCreateRequest {
  /**
   * key为服务商id，value为板块,
如果是一级，必须传name和allowChildren
如果是子级的话parentId必须传。必须传name，
用法：如果要内置好多级，先内置一级，拿到一级的id，放到下一级的parentId中请求
   */
  list?: Array<BuildInPlateCreateRequestDto>
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.api.command.traininginstitution.buildin
@Author: XuCenHao
@CreateTime: 2024-04-10  19:46
@Description:
 */
export class BuildInPlateCreateRequestDto {
  servicerId?: string
}

/**
 * 服务商信息查询返回类
 */
export class ServicerInfoByQueryResponse {
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 服务商名称
   */
  servicerName: string
  /**
   * 服务商类别
   */
  servicerType: number
  /**
   * 服务凭证token
   */
  token: string
}

/**
 * 服务方信息元数据
<AUTHOR>
@since 2021/7/27
 */
export class ServicerTokenMetaResponse {
  /**
   * 服务商编号
   */
  servicerId: string
  /**
   * 服务商类型
   */
  servicerType: number
  /**
   * 服务商状态
   */
  status: number
  /**
   * 服务商所属单位编号
   */
  unitId: string
}

/**
 * 服务凭证
<AUTHOR>
@since 2021/7/27
 */
export class ServicerTokenResponse {
  /**
   * 服务凭证
   */
  token: string
  /**
   * 服务凭证元数据
   */
  tokenMeta: ServicerTokenMetaResponse
}

/**
 * @BelongsProject: fjhb-microservice-servicer
@BelongsPackage: com.fjhb.ms.servicer.v1.kernel.gateway.graphql.response
@Author: XuCenHao
@CreateTime: 2024-03-07  16:31
@Description:
 */
export class GeneralMutationResponse {
  /**
   * @see GeneralMutationEnum
   */
  code: number
  /**
   * @see IBusinessCode
   */
  businessCode: number
  errorMessage: string
  payload: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询签约状态
   * @param request
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getContractStatus(
    request: CVendorForTInstitutionRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getContractStatus,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据当前请求头中的分销商的ServicerProvider来获取网校的ServicerProvider
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getOnlineSchoolServicerProviderByHeader(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getOnlineSchoolServicerProviderByHeader,
    operation?: string
  ): Promise<Response<ServicerTokenResponse>> {
    return commonRequestApi<ServicerTokenResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 企业新增 二级栏目
   * 栏目名称已存在 705
   * 该一级栏目已发布过资讯，请将资讯移除后再添加二级栏目通过校验即可成功添加二级栏目！ 701
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addSecondEnterpriseServicerMenu(
    request: MenuCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.addSecondEnterpriseServicerMenu,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 院校新增 二级栏目
   * 栏目名称已存在 705
   * 该一级栏目已发布过资讯，请将资讯移除后再添加二级栏目通过校验即可成功添加二级栏目！ 701
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addSecondMechanicAcademyMenu(
    request: MenuCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.addSecondMechanicAcademyMenu,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 服务商申请服务凭证（获取服务商对应服务提供商信息）
   * @param servicerId 服务商编号
   * @return 服务凭证
   * @param mutate 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyForService(
    servicerId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyForService,
    operation?: string
  ): Promise<Response<ServicerTokenResponse>> {
    return commonRequestApi<ServicerTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { servicerId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 域名申请服务商凭证
   * @param domainName 域名
   * @return 服务凭证
   * @param mutate 查询 graphql 语法文档
   * @param domainName 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async applyForServiceByDomainName(
    domainName: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyForServiceByDomainName,
    operation?: string
  ): Promise<Response<ServicerTokenResponse>> {
    return commonRequestApi<ServicerTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { domainName },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 取消培训机构下的某个培训班的所有渠道商授权
   * @param cancelAllCVendorAuthParam 取消授权参数
   * @param mutate 查询 graphql 语法文档
   * @param cancelAllCVendorAuthParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelAllCVendorAuthPromotionTraining(
    cancelAllCVendorAuthParam: CancelAllCVendorAuthPromotionTrainingRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.cancelAllCVendorAuthPromotionTraining,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { cancelAllCVendorAuthParam },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 网校创建内置的栏目（自用）
   * @param
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createBuildInPlate(
    request: BuildInPlateCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createBuildInPlate,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建平台下渠道商
   * <p>
   * 该方法仅创建平台下渠道商类型的服务商
   * @param createRequest 渠道商信息
   * @return 渠道商编号
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createChannelVendor(
    createRequest: ChannelVendorCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createChannelVendor,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建平台下课件供应商
   * <p>
   * 该方法仅创建平台下课件供应商类型的服务商
   * @param createRequest 课件供应商信息
   * @return 课件供应商编号
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCoursewareSupplier(
    createRequest: CoursewareSupplierCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createCoursewareSupplier,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 网校新增一级栏目（自用）
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createJxjyCommonMenu(
    request: CommonMenuCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createJxjyCommonMenu,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建咨询分类栏目
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createNewsCategoryTrainingInstitutionMenu(
    request: TrainingInstitutionMenuCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createNewsCategoryTrainingInstitutionMenu,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建平台下参训单位
   * <p>
   * 该方法仅创建平台下参训单位类型的服务商
   * @param createRequest 参训单位信息
   * @return 参训单位编号
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createParticipatingUnit(
    createRequest: ParticipatingUnitCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createParticipatingUnit,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建平台下培训机构
   * <p>
   * 该方法仅创建平台下培训机构类型的服务商
   * @param request 培训机构信息
   * @return 培训机构编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createTrainingInstitution(
    request: TrainingInstitutionCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createTrainingInstitution,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建培训机构门户
   * @param request 门户信息
   * @return 门户编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createTrainingInstitutionPortal(
    request: TrainingInstitutionPortalCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createTrainingInstitutionPortal,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 企业删除 栏目
   * 该栏目的资讯分类下已存在相关资讯，无法删除！ 700
   * 该栏目下含有子级，不能删除 1004
   * @param mutate 查询 graphql 语法文档
   * @param menuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteEnterpriseServicerMenu(
    menuId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteEnterpriseServicerMenu,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { menuId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 院校删除 栏目
   * 该栏目的资讯分类下已存在相关资讯，无法删除！ 700
   * 该栏目下含有子级，不能删除 1004
   * @param mutate 查询 graphql 语法文档
   * @param menuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteMechanicAcademyMenu(
    menuId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteMechanicAcademyMenu,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { menuId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除栏目
   * @param menuId
   * @param mutate 查询 graphql 语法文档
   * @param menuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteTrainingInstitutionMenu(
    menuId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteTrainingInstitutionMenu,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { menuId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 停用合同供应商
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableContractProvider(
    request: ContractProviderDisableRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableContractProvider,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 禁用培训机构
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableTrainingInstitution(
    request: TrainingInstitutionDisableRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableTrainingInstitution,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 停用栏目
   * @param menuId
   * @param mutate 查询 graphql 语法文档
   * @param menuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableTrainingInstitutionMenu(
    menuId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disableTrainingInstitutionMenu,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { menuId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用合同供应商
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableContractProvider(
    request: ContractProviderEnableRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableContractProvider,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 企业启用 禁用栏目
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableEnterpriseServicerMenu(
    request: MenuEnableRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableEnterpriseServicerMenu,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 企业启用 禁用板块
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableEnterpriseServicerPlate(
    request: PlateEnableRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableEnterpriseServicerPlate,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 院校启用 禁用栏目
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableMechanicAcademyMenu(
    request: MenuEnableRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableMechanicAcademyMenu,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用培训机构
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableTrainingInstitution(
    request: TrainingInstitutionEnableRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableTrainingInstitution,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用栏目
   * @param menuId
   * @param mutate 查询 graphql 语法文档
   * @param menuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableTrainingInstitutionMenu(
    menuId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableTrainingInstitutionMenu,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { menuId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 院校启用 禁用板块
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableTrainingInstitutionPlate(
    request: PlateEnableRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableTrainingInstitutionPlate,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据单位id查询服务商信息
   * @param mutate 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getServicerInfoByUnitId(
    unitId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.getServicerInfoByUnitId,
    operation?: string
  ): Promise<Response<Array<ServicerInfoByQueryResponse>>> {
    return commonRequestApi<Array<ServicerInfoByQueryResponse>>(
      SERVER_URL,
      {
        query: mutate,
        variables: { unitId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 发布培训机构门户 企业
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async publishEnterprisePortal(
    request: PublishEnterprisePortalRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.publishEnterprisePortal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 发布培训机构门户
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async publishTrainingInstitutionPortal(
    request: PublishTrainingInstitutionPortalRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.publishTrainingInstitutionPortal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 移除培训机构门户
   * @param request 门户信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeTrainingInstitutionPortal(
    request: TrainingInstitutionPortalRemoveRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeTrainingInstitutionPortal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 培训机构恢复与渠道商合作
   * @param request 恢复信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resumeWithChannelVendorSigned(
    request: ResumeSignedCVendorForTInstitutionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.resumeWithChannelVendorSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 培训机构恢复与课件供应商合作
   * @param request 恢复信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resumeWithCoursewareSupplierSigned(
    request: ResumeSignedCSupplierForTInstitutionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.resumeWithCoursewareSupplierSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 参训单位恢复与培训机构合作
   * @param request 恢复信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resumeWithTrainingInstitutionSigned(
    request: ResumeSignedTInstitutionForPUnitRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.resumeWithTrainingInstitutionSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存轮播图集合（请求的轮播图列表将完全覆盖原先的所有轮播图） 企业
   * @param request 保存轮播图集合请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveEnterpriseServicerBannerList(
    request: TrainingInstitutionBannerListSaveRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveEnterpriseServicerBannerList,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存轮播图集合（请求的轮播图列表将完全覆盖原先的所有轮播图）
   * @param request 保存轮播图集合请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveTrainingInstitutionBannerList(
    request: TrainingInstitutionBannerListSaveRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveTrainingInstitutionBannerList,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 培训机构签约自己的渠道商
   * <p>
   * 签约培训机构（甲方）与渠道商关系（乙方）
   * @param request 签约信息
   * @return 签约编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async signUpChannelVendor(
    request: SignUpCVendorForTInstitutionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.signUpChannelVendor,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 培训机构签约自己的课件供应商
   * <p>
   * 签约培训机构（甲方）与课件供应商关系（乙方）
   * @param request 签约信息
   * @return 签约编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async signUpCoursewareSupplier(
    request: SignUpCSupplierForTInstitutionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.signUpCoursewareSupplier,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 参训单位签约培训机构(参训单位角色操作)
   * <p>
   * 培训机构（乙方）与参训单位关系（甲方）
   * @param request 签约信息
   * @return 签约编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async signUpTrainingInstitution(
    request: SignUpTInstitutionForPUnitRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.signUpTrainingInstitution,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 企业 排序板块
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sortEnterpriseServicerPlate(
    request: PlateSortRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sortEnterpriseServicerPlate,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 院校 排序板块
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sortTrainingInstitutionPlate(
    request: PlateSortRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sortTrainingInstitutionPlate,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 培训机构中止与渠道商合作
   * @param request 中止信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async suspendWithChannelVendorSigned(
    request: SuspendSignedCVendorForTInstitutionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.suspendWithChannelVendorSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 培训机构中止与课件供应商合作
   * @param request 中止信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async suspendWithCoursewareSupplierSigned(
    request: SuspendSignedCSupplierForTInstitutionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.suspendWithCoursewareSupplierSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 参训单位中止与培训机构合作
   * @param request 中止信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async suspendWithTrainingInstitutionSigned(
    request: SuspendSignedTInstitutionForPUnitRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.suspendWithTrainingInstitutionSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 取消发布培训机构门户 企业
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unpublishEnterprisePortal(
    request: UnpublishEnterprisePortalRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.unpublishEnterprisePortal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 取消发布培训机构门户
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unpublishTrainingInstitutionPortal(
    request: UnpublishTrainingInstitutionPortalRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.unpublishTrainingInstitutionPortal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新平台下渠道商
   * <p>
   * 该方法仅更新平台下渠道商类型服务商的信息
   * @param updateRequest 渠道商信息
   * @param mutate 查询 graphql 语法文档
   * @param updateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateChannelVendor(
    updateRequest: ChannelVendorUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateChannelVendor,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新平台下课件供应商
   * <p>
   * 该方法仅更新平台下课件供应商类型服务商的信息
   * @param updateRequest 课件供应商信息
   * @param mutate 查询 graphql 语法文档
   * @param updateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCoursewareSupplier(
    updateRequest: CoursewareSupplierUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateCoursewareSupplier,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新企业H5门户基本配置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateEnterpriseH5Portal(
    request: EnterpriseH5PortalUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateEnterpriseH5Portal,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 企业更新栏目
   * 栏目名称已存在 705
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateEnterpriseServicerMenu(
    request: MenuUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateEnterpriseServicerMenu,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 企业更新板块
   * 板块名称已存在 705
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateEnterpriseServicerPlate(
    request: PlateUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateEnterpriseServicerPlate,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新企业web门户
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateEnterpriseWebPortal(
    request: WebPortalUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateEnterpriseWebPortal,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新院校H5门户基本配置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateMechanicAcademyH5Portal(
    request: MechanicAcademyH5PortalUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateMechanicAcademyH5Portal,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 院校更新栏目
   * 栏目名称已存在 705
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateMechanicAcademyMenu(
    request: MenuUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateMechanicAcademyMenu,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新培训机构web门户
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateMechanicAcademyWebPortal(
    request: WebPortalUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateMechanicAcademyWebPortal,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新平台下参训单位
   * <p>
   * 该方法仅更新平台下参训单位类型服务商的信息
   * @param updateRequest 参训单位信息
   * @param mutate 查询 graphql 语法文档
   * @param updateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateParticipatingUnit(
    updateRequest: ParticipatingUnitUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateParticipatingUnit,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新平台下培训机构
   * <p>
   * 该方法仅更新平台下培训机构类型服务商的信息
   * @param request 培训机构信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingInstitution(
    request: TrainingInstitutionUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingInstitution,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新培训机构H5门户 开关
   * @param request 更新培训机构H5门户请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingInstitutionH5Portal(
    request: TrainingInstitutionH5PortalUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingInstitutionH5Portal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新栏目
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingInstitutionMenu(
    request: TrainingInstitutionMenuUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingInstitutionMenu,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 院校更新板块
   * 板块名称已存在 705
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingInstitutionPlate(
    request: PlateUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingInstitutionPlate,
    operation?: string
  ): Promise<Response<GeneralMutationResponse>> {
    return commonRequestApi<GeneralMutationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新培训机构门户
   * @param request 门户信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingInstitutionPortal(
    request: TrainingInstitutionPortalUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingInstitutionPortal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新培训机构web门户
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingInstitutionWebPortal(
    request: TrainingInstitutionWebPortalUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingInstitutionWebPortal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新门户主题颜色请求
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingInstitutionWebPortalThemeColor(
    request: TrainingInstitutionPortalThemeColorUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingInstitutionWebPortalThemeColor,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 验证培训机构下对已签约的渠道商授权推广班级是否有效
   * @param verifyAuthParam 验证授权参数
   * @param mutate 查询 graphql 语法文档
   * @param verifyAuthParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifyAuthCVendorPromotionTrainingEffective(
    verifyAuthParam: VerifyAuthCVendorPromotionTrainingRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.verifyAuthCVendorPromotionTrainingEffective,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { verifyAuthParam },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 验证培训机构与渠道商签约是否有效
   * @param request 参数
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async verifyContractEffective(
    request: CVendorForTInstitutionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.verifyContractEffective,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
