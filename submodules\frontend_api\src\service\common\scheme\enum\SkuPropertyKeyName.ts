/*
 * @Author: chenweinian chenweinian
 * @Date: 2025-04-17 17:12:41
 * @LastEditors: chenweinian chenweinian
 * @LastEditTime: 2025-04-17 17:23:24
 * @FilePath: \jxjyv2_frontend_web_customer\submodules\frontend_api\src\service\common\scheme\enum\SkuPropertyKeyName.ts
 * @Description: sku名称枚举-请求部分（返回值不使用）
 */
import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description sku名称枚举
 */
export enum SkuPropertyKeyNameEnum {
  industry = 'industry',
  trainingForm = 'trainingForm',
  year = 'year',
  subjectType = 'subjectType',
  trainingCategory = 'trainingCategory',
  trainingProfessional = 'trainingProfessional',
  belongIndustryForPortal = 'belongIndustryForPortal',
  jobLevel = 'jobLevel',
  trainingObject = 'trainingObject',
  positionCategory = 'positionCategory',
  learningPhase = 'learningPhase',
  discipline = 'discipline',
  region = 'region',
  practitionerCategory = 'practitionerCategory',
  period = 'period'
}

/**
 * @description 培训形式
 */
class SkuPropertyKeyName extends AbstractEnum<SkuPropertyKeyNameEnum> {
  static enum = SkuPropertyKeyNameEnum

  constructor(status?: SkuPropertyKeyNameEnum) {
    super()
    this.current = status
    this.map.set(SkuPropertyKeyNameEnum.industry, '行业')
    this.map.set(SkuPropertyKeyNameEnum.trainingForm, '培训形式')
    this.map.set(SkuPropertyKeyNameEnum.year, '年度')
    this.map.set(SkuPropertyKeyNameEnum.subjectType, '科目类型')
    this.map.set(SkuPropertyKeyNameEnum.trainingCategory, '培训类别')
    this.map.set(SkuPropertyKeyNameEnum.trainingProfessional, '培训专业')
    this.map.set(SkuPropertyKeyNameEnum.belongIndustryForPortal, '培训行业') // 专题用
    this.map.set(SkuPropertyKeyNameEnum.jobLevel, '技术等级')
    this.map.set(SkuPropertyKeyNameEnum.trainingObject, '培训对象')
    this.map.set(SkuPropertyKeyNameEnum.positionCategory, '岗位类别')
    this.map.set(SkuPropertyKeyNameEnum.learningPhase, '学段')
    this.map.set(SkuPropertyKeyNameEnum.discipline, '学科')
    this.map.set(SkuPropertyKeyNameEnum.region, '培训地区')
    this.map.set(SkuPropertyKeyNameEnum.practitionerCategory, '执业类别')
    this.map.set(SkuPropertyKeyNameEnum.period, '学时')
  }
}

export default new SkuPropertyKeyName()
