import findAllChildChapterByMajor from './queries/findAllChildChapterByMajor.graphql'
import findAllChildChapterByMajorList from './queries/findAllChildChapterByMajorList.graphql'
import findAllIndustryRelationList from './queries/findAllIndustryRelationList.graphql'
import createChapter from './mutates/createChapter.graphql'
import deleteChapter from './mutates/deleteChapter.graphql'
import importBatchSyllabus from './mutates/importBatchSyllabus.graphql'
import updateChapter from './mutates/updateChapter.graphql'

export {
  findAllChildChapterByMajor,
  findAllChildChapterByMajorList,
  findAllIndustryRelationList,
  createChapter,
  deleteChapter,
  importBatchSyllabus,
  updateChapter
}
