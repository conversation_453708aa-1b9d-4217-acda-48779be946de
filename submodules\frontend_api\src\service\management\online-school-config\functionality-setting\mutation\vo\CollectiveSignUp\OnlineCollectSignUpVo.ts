import {
  AttachmentRequest,
  OnlineCollectiveRegisterConfigResponse,
  OnlineCollectiveRegisterConfigSaveRequest
} from '@api/ms-gateway/ms-servicer-series-v1'
import CollectSignUpVo from './CollectSignUpVo'

class OnlineCollectSignUpVo extends CollectSignUpVo {
  /**
   * 线上集体报名入口图片启用
   */
  onlineEnterPhotoEnable = false
  /**
   * 线上集体报名入口图片
   */
  onlineEnterPhoto: AttachmentRequest = new AttachmentRequest()

  from(res: OnlineCollectiveRegisterConfigResponse) {
    this.enable = res.enabled
    this.templatePath = res.templatePath
    this.templateName = res.templateFileName
    this.signUpClassUrl = `${location.protocol}//${location.host}/manage/view-train-class`
    this.onlineEnterPhotoEnable = res.enabledPicture
    if (res.registerPortalPicture?.length) {
      this.onlineEnterPhoto = res.registerPortalPicture[0]
    }
  }

  to() {
    const request = new OnlineCollectiveRegisterConfigSaveRequest()
    request.enabled = this.enable
    request.templatePath = this.templatePath
    request.templateFileName = this.templateName
    request.enabledPicture = this.onlineEnterPhotoEnable
    request.registerPortalPicture = this.onlineEnterPhoto.url ? [this.onlineEnterPhoto] : []
    return request
  }
}
export default OnlineCollectSignUpVo
