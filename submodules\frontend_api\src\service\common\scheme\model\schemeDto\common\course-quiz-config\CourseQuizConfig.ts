import CourseQuizPrecondition from '@api/service/common/scheme/model/schemeDto/common/course-quiz-config/precondition/CourseQuizPrecondition'
import CourseQuizConfigQuizConfig from '@api/service/common/scheme/model/schemeDto/common/course-quiz-config/quiz-config/CourseQuizConfigQuizConfig'

/**
 * @description 课程测验配置
 */
class CourseQuizConfig {
  /**
   * 课后测验配置id
   */
  id = ''
  /**
   * 课后测验进入前置条件
   */
  precondition: CourseQuizPrecondition
  /**
   * 课后测验出卷配置
   */
  quizConfig: CourseQuizConfigQuizConfig
  /**
   * 操作类型
   */
  operation: number
}

export default CourseQuizConfig
