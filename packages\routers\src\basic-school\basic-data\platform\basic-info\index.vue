<route-meta>
{
"isMenu": true,
"title": "基础信息配置",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  import BasicInfo from '@hbfe/jxjy-admin-platform/src/basic-info/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    setAccess: [WXGLY],
    webSet: [WXGLY],
    webPreview: [WXGLY],
    portal: [WXGLY],
    editPortal: [WXGLY],
    column: [WXGLY],
    editColumn: [WXGLY],
    banner: [WXGLY],
    editBanner: [WXGLY],
    style: [WXGLY],
    editStyle: [WXGLY],
    H5Set: [WXGLY],
    editH5: [WXGLY],
    h5Preview: [WXGLY],
    setSeo: [WXGLY]
  })
  export default class extends BasicInfo {}
</script>
