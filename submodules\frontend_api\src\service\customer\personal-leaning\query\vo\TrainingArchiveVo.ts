import { StudentSchemeLearningResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import SkuPropertyResponseVo from '@api/service/customer/train-class/query/vo/SkuPropertyResponseVo'

class TrainingArchiveVo extends StudentSchemeLearningResponse {
  // region properties
  /**
   *班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuValueNameProperty = new SkuPropertyResponseVo()
  /**
   *培训方案类型1: 选课规则2: 自主选课，类型为number
   */
  schemeType = 0
  /**
   *报名开始时间，类型为string
   */
  registerBeginDate = ''
  /**
   *报名结束时间，类型为string
   */
  registerEndDate = ''
  /**
   *培训开始时间，类型为string
   */
  trainingBeginDate = ''
  /**
   *培训结束时间，类型为string
   */
  trainingEndDate = ''
  /**
   * 学时，类型为number
   */
  period = 0
  /**
   *培训班名称，类型为string
   */
  trainClassName = ''
  /**
   * 培训班图片
   */
  picture = ''
  /**
   * 是否有配置培训模板
   */
  hasLearnResult = false
  /**
   * 培训模板Id
   */
  learningResultId = ''

  /**
   * 是否开放打印（学习档案）
   */
  openPrintTemplate = false
  // endregion
  // region methods
  // endregion
}
export default TrainingArchiveVo
