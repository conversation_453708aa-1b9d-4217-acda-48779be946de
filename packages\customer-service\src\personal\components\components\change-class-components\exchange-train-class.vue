<template>
  <div class="pure">
    <el-drawer title="更换班级" :visible.sync="show" size="1000px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          <span class="f-c9">当前更换的班级为：</span>
          <span class="f-fb">{{ schemeName }}</span>
          <span class="f-c9" v-if="isSpecial"
            >，当前订单来源于专题【{{
              specialName
            }}】，可更换班级在本专题范围内，请在以下列表中选择更换的目标班级！</span
          >
          <span class="f-c9" v-else>，请在以下列表中选择更换的目标班级！</span>
        </el-alert>
        <hb-search-wrapper @reset="resetCondition" class="m-query f-mt20">
          <el-form-item label="班级：">
            <el-input
              v-model="queryParams.schemeName"
              clearable
              placeholder="请输入班级名称"
              @clear="queryParams.schemeName = ''"
            />
          </el-form-item>
          <el-form-item label="年度：">
            <biz-year-select v-model="localSkuProperty.year" placeholder="请选择培训年度"></biz-year-select>
          </el-form-item>
          <el-form-item label="地区：">
            <biz-national-region
              v-model="localSkuProperty.region"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-national-region>
          </el-form-item>
          <el-form-item label="行业：">
            <biz-industry-select
              v-model="localSkuProperty.industry"
              @clearIndustrySelect="handleClearIndustrySelect"
              @industryPropertyId="handleIndustryPropertyId"
              @industryInfos="handleIndustryInfos"
              placeholder="请选择行业"
            >
            </biz-industry-select>
          </el-form-item>
          <template v-if="localSkuProperty.industry && localSkuProperty.industry === envConfig.teacherIndustryId">
            <el-form-item label="学段">
              <biz-study-period
                v-model="localSkuProperty.studyPeriodId"
                :industry-id="localSkuProperty.industry"
                :industry-property-id="industryPropertyId"
                @updateStudyPeriod="updateStudyPeriod"
                @clearSubject="clearSubject"
              ></biz-study-period>
            </el-form-item>
            <el-form-item label="学科">
              <biz-subject
                v-model="localSkuProperty.subjectId"
                :industry-property-id="industryPropertyId"
                :study-period-id="localSkuProperty.studyPeriodId"
                @updateSubject="updateSubject"
              ></biz-subject>
            </el-form-item>
          </template>
          <el-form-item label="科目类型：" v-if="skuVisible.subjectType">
            <biz-accounttype-select
              v-model="localSkuProperty.subjectType"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              placeholder="请选择科目类型"
              ref="subjectTypeRef"
            ></biz-accounttype-select>
          </el-form-item>
          <el-form-item
            label="培训专业："
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-major-cascader
              v-model="localSkuProperty.societyTrainingMajor"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              placeholder="请选择培训专业"
            >
            </biz-major-cascader>
          </el-form-item>
          <el-form-item
            label="培训类别："
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-training-category-select
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              @updateTrainingCategory="handleUpdateTrainingCategory"
              placeholder="请选择培训类别"
            ></biz-training-category-select>
          </el-form-item>
          <el-form-item
            label="培训专业："
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-major-select
              v-model="localSkuProperty.constructionTrainingMajor"
              :industry-property-id="industryPropertyId"
              :training-category-id="trainingCategoryId"
              placeholder="请选择培训专业"
            ></biz-major-select>
          </el-form-item>
          <el-form-item
            label="培训对象："
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              localSkuProperty.industry === envConfig.occupationalHealthId
            "
          >
            <biz-training-object-select
              v-model="localSkuProperty.trainingObject"
              placeholder="请选择培训对象"
              :industry-property-id="industryPropertyId"
              :industry-id="localSkuProperty.industry"
              @updateTrainingCategory="updateTrainingCategory"
            />
          </el-form-item>
          <el-form-item
            label="岗位类别："
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              localSkuProperty.industry === envConfig.occupationalHealthId
            "
          >
            <biz-obj-category-select
              v-model="localSkuProperty.positionCategory"
              placeholder="请选择岗位类别"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              :trainingObjectId="trainingObjectId"
            />
          </el-form-item>
          <el-form-item
            label="执业类别："
            v-if="
              skuVisible.occupationalCategory &&
              localSkuProperty.industry &&
              localSkuProperty.industry === envConfig.yshyId
            "
          >
            <biz-practicing-category-cascader
              v-model="localSkuProperty.pharmacistIndustry"
              :industryId="localSkuProperty.industry"
            ></biz-practicing-category-cascader>
          </el-form-item>
          <el-form-item
            label="技术等级："
            v-if="
              skuVisible.jobLevel && localSkuProperty.industry && localSkuProperty.industry === envConfig.workServiceId
            "
          >
            <biz-technical-grade-select
              v-model="localSkuProperty.jobLevel"
              :industry-id="localSkuProperty.industry"
              :industry-property-id="industryPropertyId"
            ></biz-technical-grade-select>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="searchBase">查询</el-button>
          </template>
        </hb-search-wrapper>
        <!--表格-->
        <el-table stripe :data="replaceableTrainClassList" max-height="500px" class="m-table" v-loading="query.loading">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="可更换的目标培训班" min-width="300">
            <template slot-scope="scope">
              <p>
                <el-tag type="primary" effect="dark" size="mini">{{ schemeType(scope.row) }}</el-tag
                >{{ scope.row.schemeName }}
              </p>
              <p class="f-c9 f-f13">
                {{ getSchemeProperty(scope.row.skuValueNameProperty) }}
                <!-- {{ scope.row.skuValueNameProperty.industry.skuPropertyName }} /
                {{ scope.row.skuValueNameProperty.year.skuPropertyName }}年 /
                {{ scope.row.skuValueNameProperty.subjectType.skuPropertyName }} -->
                <!-- 科目 -->
              </p>
            </template>
          </el-table-column>
          <el-table-column label="单价(元)" min-width="140" align="right">
            <template slot-scope="scope">{{ scope.row.price }}</template>
          </el-table-column>
          <el-table-column label="学时" min-width="100" align="center">
            <template slot-scope="scope">{{ scope.row.period }}</template>
          </el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template slot-scope="scope">
              <el-popconfirm
                confirm-button-text="确认"
                :title="
                  '是否确认将【' +
                  `${schemeName}` +
                  '】替换【' +
                  `${scope.row.schemeName}` +
                  '】，更换后原班级的学习进度及相关考核全部清零，新班级重新开始学习和考核！'
                "
                @confirm="confirmExchangeTrainClass(scope.row)"
              >
                <el-button type="text" size="mini" slot="reference">确认换班</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch, PropSync } from 'vue-property-decorator'
  import { MutationCreateExchangeOrder } from '@api/service/management/trade/single/order/mutation/MutationCreateExchangeOrder'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { Query, UiPage } from '@hbfe/common'
  import QueryReplaceableTrainClassListVo from '@api/service/management/train-class/query/vo/QueryReplaceableTrainClassListVo'
  import ReplaceableTrainClassDetailVo from '@api/service/management/train-class/query/vo/ReplaceableTrainClassDetailVo'
  import QueryExchangeTrainClass from '@api/service/management/train-class/query/QueryExchangeTrainClass'
  import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
  import { bind, debounce } from 'lodash-decorators'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import TrainClassSchemeType, {
    TrainClassSchemeEnum
  } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import BizNationalRegion from '@hbfe/jxjy-admin-components/src/biz/biz-national-region.vue'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import BizAccounttypeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-accounttype-select.vue'
  import BizMajorCascader from '@hbfe/jxjy-admin-components/src/biz/biz-major-cascader.vue'
  import BizTrainingCategorySelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-category-select.vue'
  import BizMajorSelect from '@hbfe/jxjy-admin-components/src/biz/biz-major-select.vue'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import CreateExchangeOrderModel from '@hbfe/jxjy-admin-customerService/src/personal/components/model/CreateExchangeOrderModel'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
  import SchemeType, { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-scheme/src/models/SchemeSkuProperty'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import BizTechnicalGradeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-technical-grade-select.vue'
  import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  @Component({
    components: {
      BizTechnicalGradeSelect,
      BizMajorSelect,
      BizTrainingCategorySelect,
      BizMajorCascader,
      BizAccounttypeSelect,
      BizIndustrySelect,
      BizNationalRegion,
      BizYearSelect
    }
  })
  export default class extends Vue {
    @PropSync('visible', { type: Boolean }) show!: boolean

    // 查询接口入口
    queryRequestEntrance: QueryExchangeTrainClass =
      TrainClassManagerModule.queryTrainClassFactory.getQueryExchangeTrainClass()

    // 业务接口入口（发起换货）
    mutationRequestEntrance: MutationCreateExchangeOrder =
      TradeModule.singleTradeBatchFactor.mutationFactory.getMutationCreateExchangeOrder()

    // 分页 - 可换班列表
    page: UiPage
    // 查询 - 可换班列表
    query: Query = new Query()
    // 查询参数 - 可换班列表
    queryParams: QueryReplaceableTrainClassListVo = new QueryReplaceableTrainClassListVo()
    // 列表 - 可换班列表
    replaceableTrainClassList: ReplaceableTrainClassDetailVo[] = []

    // 培训方案类型列表
    trainClassSchemeTypeList: EnumOption<TrainClassSchemeEnum>[] = TrainClassSchemeType.list()

    // 剔除培训班商品id集合
    excludeCommoditySkuIdList: string[] = []
    /**
     * 培训对象Id
     */
    trainingObjectId = ''
    /**
     * 【培训班列表】价格
     */
    price: number = null

    /**
     * 培训方案名称
     */
    schemeName = ''

    //region sku

    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = {
      /**
       * 年度
       */
      year: '',
      /**
       * 地区
       */
      region: [] as string[],
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训类别
       */
      trainingCategory: '',
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 技术等级 - 工勤行业
       **/
      jobLevel: '',
      /**
       * 培训对象
       */
      trainingObject: '',
      /**
       * 岗位类别
       */
      positionCategory: '',
      /**
       * 学段id
       */
      studyPeriodId: '',
      /**
       * 学科id
       */
      subjectId: '',
      /**
       * 药师id
       */
      pharmacistIndustry: [] as string[]
    } as SchemeSkuProperty

    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''

    /**
     * 是否是专题
     */
    isSpecial = false

    /**
     * 专题名称
     */
    specialName = ''
    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: false,
      // 培训类别
      trainingCategory: false,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      //   岗位类别
      positionCategory: true,
      //执业类别
      occupationalCategory: true
    }
    /**
     * 当前网校信息
     */
    envConfig = {
      // 工勤行业
      workServiceId: '',
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 职业卫生行业Id
      occupationalHealthId: '',
      //教师行业id
      teacherIndustryId: '',
      //药师行业id
      yshyId: ''
    }

    /**
     * 专题id
     */
    trainingChannelId = ''
    /**
     * 分销商id
     */
    distributorId = ''

    //endregion

    /**
     * 培训方案类型
     */
    get schemeType() {
      return (item: ReplaceableTrainClassDetailVo) => {
        return SchemeType.getSchemeType(item.schemeType, true)
      }
    }

    constructor() {
      super()
      this.page = new UiPage(this.pageReplaceableTrainClass, this.pageReplaceableTrainClass)
    }
    isTrainCooperate(type: number) {
      return type == SchemeTypeEnum.trainingCooperation
    }

    // 行业属性
    getSchemeProperty(val: SkuPropertyResponseVo) {
      const arrList = Array<string>()

      if (val.industry && val.industry.skuPropertyName) arrList.push(val.industry.skuPropertyName)
      if (val.year && val.year.skuPropertyName) arrList.push(val.year.skuPropertyName)
      if (val.technicalGrade && val.technicalGrade.skuPropertyName) arrList.push(val.technicalGrade.skuPropertyName)
      if (val.subjectType && val.subjectType.skuPropertyName) arrList.push(val.subjectType.skuPropertyName)
      if (val.trainingCategory && val.trainingCategory.skuPropertyName)
        arrList.push(val.trainingCategory.skuPropertyName)
      if (val.trainingObject && val.trainingObject.skuPropertyName) arrList.push(val.trainingObject.skuPropertyName)
      if (val.positionCategory && val.positionCategory.skuPropertyName)
        arrList.push(val.positionCategory.skuPropertyName)
      if (val.jobLevel && val.jobLevel.skuPropertyName) arrList.push(val.jobLevel.skuPropertyName)
      if (val.learningPhase && val.learningPhase.skuPropertyName) arrList.push(val.learningPhase.skuPropertyName)
      if (val.discipline && val.discipline.skuPropertyName) arrList.push(val.discipline.skuPropertyName)

      if (!arrList.length) return '-'
      return arrList.join(' / ')
    }

    /**
     * 查询（由外部触发）
     */
    async searchByExternal(params: CreateExchangeOrderModel) {
      // console.log('createExchangeOrder', params)
      this.price = QueryReplaceableTrainClassListVo.validateIsEmpty(params.price) ? params.price : null
      this.schemeName = params.schemeName || ''
      this.excludeCommoditySkuIdList = [] as string[]
      this.excludeCommoditySkuIdList = params.excludeCommoditySkuIdList || ([] as string[])
      // 清空页面绑定属性
      this.localSkuProperty = new SchemeSkuProperty()
      // 清空接口查询属性
      this.queryParams = new QueryReplaceableTrainClassListVo()
      this.queryParams.price = this.price
      this.mutationRequestEntrance = new MutationCreateExchangeOrder()
      this.mutationRequestEntrance.orderNo = params.orderNo || ''
      this.mutationRequestEntrance.subOrderNo = params.subOrderNo || ''
      this.isSpecial = params.isSpecialOrder
      this.specialName = params.specialOrderName
      if (this.isSpecial) {
        this.trainingChannelId = params.saleChannelId
      } else {
        this.trainingChannelId = ''
      }
      this.distributorId = params.distributorId
      await this.searchBase()
    }

    /**
     * 查询分页
     */
    async searchBase() {
      this.page.pageNo = 1
      await this.pageReplaceableTrainClass()
    }

    /**
     * 查询分页
     */
    async pageReplaceableTrainClass() {
      this.query.loading = true
      try {
        this.queryParams = this.getQueryParams()
        if (this.localSkuProperty.studyPeriodId) this.queryParams.learningPhase = [this.localSkuProperty.studyPeriodId]
        if (this.localSkuProperty.subjectId) this.queryParams.discipline = [this.localSkuProperty.subjectId]
        //网授班接口查询
        this.queryParams.trainingMode = TrainingModeEnum.online
        this.replaceableTrainClassList = await this.queryRequestEntrance.queryReplaceableTrainClassList(
          this.page,
          this.queryParams
        )
        // console.log(
        //   'replaceableTrainClassList',
        //   this.replaceableTrainClassList,
        //   '==========================================='
        // )
      } catch (e) {
        console.log('获取可更换列表失败！', e)
      } finally {
        this.query.loading = false
      }
    }

    /**
     * 获取查询参数
     */
    getQueryParams(): QueryReplaceableTrainClassListVo {
      const schemeName = this.queryParams.schemeName
      const price = this.queryParams.price
      const queryParams = new QueryReplaceableTrainClassListVo()
      queryParams.schemeName = schemeName || ''
      queryParams.price = price || null
      queryParams.price = this.price
      queryParams.year = this.localSkuProperty.year ? [this.localSkuProperty.year] : ([] as string[])
      queryParams.region = this.localSkuProperty.region || ([] as string[])
      queryParams.industry = this.localSkuProperty.industry ? [this.localSkuProperty.industry] : ([] as string[])
      queryParams.jobLevel = !this.localSkuProperty.jobLevel ? ([] as string[]) : [this.localSkuProperty.jobLevel]
      queryParams.subjectType = this.localSkuProperty.subjectType
        ? [this.localSkuProperty.subjectType]
        : ([] as string[])
      queryParams.trainingCategory = this.localSkuProperty.trainingCategory
        ? [this.localSkuProperty.trainingCategory]
        : ([] as string[])
      queryParams.trainingProfessional = this.getTrainingProfessional()
      queryParams.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      queryParams.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      queryParams.learningPhase = !this.localSkuProperty.studyPeriodId
        ? ([] as string[])
        : [this.localSkuProperty.studyPeriodId]
      queryParams.discipline = !this.localSkuProperty.subjectId ? ([] as string[]) : [this.localSkuProperty.subjectId]
      //执业类别id TODO

      queryParams.certificatesType = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[0]]
        : []
      queryParams.practitionerCategory = !this.localSkuProperty.pharmacistIndustry
        ? ([] as string[])
        : Array.isArray(this.localSkuProperty.pharmacistIndustry) && this.localSkuProperty.pharmacistIndustry.length
        ? [this.localSkuProperty.pharmacistIndustry[1]]
        : []
      queryParams.excludeCommoditySkuIdList = this.excludeCommoditySkuIdList
      if (this.trainingChannelId) {
        queryParams.trainingChannelIds = [this.trainingChannelId]
      }
      queryParams.distributorId = this.distributorId
      return queryParams
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人社行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }

    /**
     * 重置条件
     */
    async resetCondition() {
      this.localSkuProperty = new SchemeSkuProperty()
      this.queryParams = new QueryReplaceableTrainClassListVo()
      this.queryParams.price = this.price
      this.clearSubject()
      await this.searchBase()
    }

    //选择学段
    updateStudyPeriod(val: string) {
      this.localSkuProperty.studyPeriodId = val
      this.localSkuProperty.subjectId = ''
      this.queryParams.discipline = []
    }
    //选择学科
    updateSubject(val: string) {
      this.localSkuProperty.subjectId = val
    }
    clearSubject() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      this.queryParams.discipline = new Array<string>()
      this.queryParams.learningPhase = new Array<string>()
    }

    /**
     * 确认换班
     */
    @bind()
    @debounce(200)
    async confirmExchangeTrainClass(row: ReplaceableTrainClassDetailVo) {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      this.mutationRequestEntrance.commoditySkuId = row.commoditySkuId
      try {
        const status = await this.mutationRequestEntrance.sellerApplyExchange()
        if (status.isSuccess()) {
          this.$message.success('操作成功')
          this.$emit('reloadData')
          this.show = false
        } else if (status.code == 3002) {
          this.$message.error('定价方案已停用，无法更换班级')
        } else if (status.code == 3006) {
          this.$message.error('分销商品周期已失效，无法更换班级')
        } else if (status.code == 3005) {
          this.$message.error('培训方案已下架，无法更换班级')
        } else if (status.code == 3007) {
          this.$message.error('该班级已经有存在报名的订单，无法换班。')
        } else {
          this.$message.error((status?.message as string) || '操作失败')
        }
      } catch (e) {
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }

    //region sku-Correlation

    @Watch('localSkuProperty', {
      immediate: true,
      deep: true
    })
    localSkuPropertyChange(val: any) {
      // console.log('localSkuProperty', val)
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.jobLevel = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.studyPeriodId = ''
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.pharmacistIndustry = [] as string[]
    }

    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const positionCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.POSITION_CATEGORY)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.positionCategory = positionCategory > -1
    }

    /**
     * 响应组件行业Id集合传参
     */
    async handleIndustryInfos(values: any) {
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.occupationalHealthId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      this.envConfig.yshyId = values.medicineIndustryId || ''
      // 仅当单个行业，需要默认选中
      const isSingleIndustry =
        (this.envConfig.societyIndustryId && !this.envConfig.constructionIndustryId) ||
        (!this.envConfig.societyIndustryId && this.envConfig.constructionIndustryId)
      if (isSingleIndustry) {
        this.localSkuProperty.industry = this.envConfig.societyIndustryId
          ? this.envConfig.societyIndustryId
          : this.envConfig.constructionIndustryId
      }
      await this.searchBase()
    }

    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
      this.localSkuProperty.subjectType = ''
    }
    updateTrainingCategory(val: string) {
      if (val) {
        this.trainingObjectId = val
        this.localSkuProperty.positionCategory = ''
      }
    }
    //endregion
  }
</script>
<style scoped lang="scss">
  .pure {
    margin: 0;
    padding: 0;
  }
</style>
