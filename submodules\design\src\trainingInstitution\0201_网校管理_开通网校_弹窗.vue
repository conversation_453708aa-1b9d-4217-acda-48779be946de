<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">选择网校开展培训的行业属性弹窗</el-button>
        <el-drawer
          title="选择网校开展培训的行业属性"
          :visible.sync="dialog1"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-tabs v-model="activeName" type="card" class="m-tab-card is-badge">
              <el-tab-pane label="人社行业" name="first">
                <el-radio-group v-model="radio" class="m-attribute-select">
                  <el-radio :label="3">
                    <span class="tit">人社通用的行业培训属性1</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">人社通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="专业类别" name="second">
                          <div class="f-pt10 u-mh100">
                            <el-tree
                              :data="data"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                  <el-radio :label="6">
                    <span class="tit">人社通用的行业培训属性2</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">人社通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="专业类别" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                  <el-radio :label="9">
                    <span class="tit">人社通用的行业培训属性3</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">人社通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="专业类别" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                </el-radio-group>
              </el-tab-pane>
              <el-tab-pane label="建设行业" name="second">
                <el-radio-group v-model="radio" class="m-attribute-select">
                  <el-radio :label="3">
                    <span class="tit">建设通用的行业培训属性1</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">建设通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="培训类别" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data2"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                  <el-radio :label="6">
                    <span class="tit">建设通用的行业培训属性2</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">建设通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="培训类别" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data2"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                  <el-radio :label="9">
                    <span class="tit">建设通用的行业培训属性3</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">建设通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="培训类别" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data2"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                </el-radio-group>
              </el-tab-pane>
              <el-tab-pane label="" name="third">
                <div slot="label">职业卫生行业<el-badge value="未选" class="u-badge"></el-badge></div>
                <el-radio-group v-model="radio" class="m-attribute-select">
                  <el-radio :label="3">
                    <span class="tit">职业卫生通用的行业培训属性值</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">培训类别</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">新训</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">副训</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="培训对象" name="second">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">新训2</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">副训2</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="岗位类别" name="third">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">新训3</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">副训3</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                </el-radio-group>
              </el-tab-pane>
              <el-tab-pane label="" name="fourth">
                <div slot="label">工勤行业<el-badge value="已选" class="u-badge" type="success"></el-badge></div>
                <el-radio-group v-model="radio" class="m-attribute-select">
                  <el-radio :label="3">
                    <span class="tit">工勤通用的行业培训属性值</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">工勤通用的行业培训属性值</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="技术等级" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">普通工</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">初级工</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">中级工</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">高级工</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">技师</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">高级技师</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="工种" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data2"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                </el-radio-group>
              </el-tab-pane>
              <el-tab-pane label="" name="fifth">
                <div slot="label">教师行业<el-badge value="未选" class="u-badge"></el-badge></div>
                <el-radio-group v-model="radio" class="m-attribute-select">
                  <el-radio :label="3">
                    <span class="tit">教师通用行业培训属性值</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">教师通用行业培训属性值</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="学段" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学段信息1</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学段信息2</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学段信息3</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="学科" name="second">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学科信息1</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学科信息2</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学科信息3</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                </el-radio-group>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确认</el-button>
          </div>
        </el-drawer>
        <!--开通中-->
        <el-button type="primary" @click="dialog2 = true" class="f-mr20">开通中-弹窗</el-button>
        <el-dialog :visible.sync="dialog2" width="450px" class="m-dialog no-close" :show-close="false">
          <div class="m-loading">
            <img class="img" src="./assets/images/waiting.gif" alt="" />
            <p class="f-mb5 f-f15">网校正在努力中开通中，稍等片刻！</p>
            <p class="f-c9">如需操作其他功能，可前往具体模块操作。</p>
          </div>
          <div slot="footer">
            <el-button type="primary">返回网校管理</el-button>
          </div>
        </el-dialog>
        <el-button @click="dialog3 = true" type="primary" class="f-mr20">选择人员行业属性弹窗</el-button>
        <el-drawer
          title="选择人员行业属性"
          :visible.sync="dialog3"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-tabs v-model="activeName" type="card" class="m-tab-card is-badge">
              <el-tab-pane label="人社行业" name="first">
                <el-radio-group v-model="radio" class="m-attribute-select">
                  <el-radio :label="3">
                    <span class="tit">人社通用的行业培训属性1</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">人社通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="专业类别" name="second">
                          <div class="f-pt10 u-mh100">
                            <el-tree
                              :data="data"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                  <el-radio :label="6">
                    <span class="tit">人社通用的行业培训属性2</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">人社通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="专业类别" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                  <el-radio :label="9">
                    <span class="tit">人社通用的行业培训属性3</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">人社通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="专业类别" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                </el-radio-group>
              </el-tab-pane>
              <el-tab-pane label="建设行业" name="second">
                <el-radio-group v-model="radio" class="m-attribute-select">
                  <el-radio :label="3">
                    <span class="tit">建设通用的行业培训属性1</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">建设通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="培训类别" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data2"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                  <el-radio :label="6">
                    <span class="tit">建设通用的行业培训属性2</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">建设通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="培训类别" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data2"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                  <el-radio :label="9">
                    <span class="tit">建设通用的行业培训属性3</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">建设通用的行业培训属性</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">专业科目</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">公需+专业科目</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="培训类别" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data2"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                </el-radio-group>
              </el-tab-pane>
              <el-tab-pane label="" name="third">
                <div slot="label">职业卫生行业<el-badge value="未选" class="u-badge"></el-badge></div>
                <el-radio-group v-model="radio" class="m-attribute-select">
                  <el-radio :label="3">
                    <span class="tit">职业卫生通用的行业培训属性值</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">培训类别</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="科目类型" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">新训</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">副训</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="培训对象" name="second">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">新训2</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">副训2</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="岗位类别" name="third">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">新训3</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">副训3</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                </el-radio-group>
              </el-tab-pane>
              <el-tab-pane label="" name="fourth">
                <div slot="label">工勤行业<el-badge value="已选" class="u-badge" type="success"></el-badge></div>
                <el-radio-group v-model="radio" class="m-attribute-select">
                  <el-radio :label="3">
                    <span class="tit">工勤通用的行业培训属性值</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">工勤通用的行业培训属性值</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="技术等级" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">普通工</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">初级工</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">中级工</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">高级工</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">技师</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">高级技师</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="工种" name="second">
                          <div class="f-pt10">
                            <el-tree
                              :data="data2"
                              :props="defaultProps"
                              @node-click="handleNodeClick"
                              class="m-attribute-tree"
                            ></el-tree>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                </el-radio-group>
              </el-tab-pane>
              <el-tab-pane label="" name="fifth">
                <div slot="label">教师行业<el-badge value="未选" class="u-badge"></el-badge></div>
                <el-radio-group v-model="radio" class="m-attribute-select">
                  <el-radio :label="3">
                    <span class="tit">教师通用行业培训属性值</span>
                    <el-popover placement="right" width="400" trigger="click">
                      <div class="f-fb f-f16 f-mb10">教师通用行业培训属性值</div>
                      <el-tabs v-model="activeName1" type="card" class="m-tab-card">
                        <el-tab-pane label="学段" name="first">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学段信息1</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学段信息2</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学段信息3</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                        <el-tab-pane label="学科" name="second">
                          <div class="f-pt10 f-f15 f-pl10">
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学科信息1</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学科信息2</el-badge>
                            </div>
                            <div class="f-mb10">
                              <el-badge is-dot type="info" class="badge-status f-mr30">学科信息3</el-badge>
                            </div>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                      <el-button type="text" class="btn" slot="reference"
                        >查看详情<i class="f-ml5 el-icon-arrow-right"></i
                      ></el-button>
                    </el-popover>
                  </el-radio>
                </el-radio-group>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确认</el-button>
          </div>
        </el-drawer>
        <br />
        <!--增值服务提示-->
        <el-button @click="dialog7 = true" type="primary" class="f-mr20 f-mt20">增值服务提示</el-button>
        <el-dialog title="增值服务" :visible.sync="dialog7" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">确认开启该增值服务吗？</span>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认</el-button>
          </div>
        </el-dialog>

        <!--增值服务说明-->
        <el-button @click="dialog8 = true" type="primary" class="f-mr20 f-mt20">增值服务说明</el-button>
        <el-drawer
          title="增值服务说明"
          :visible.sync="dialog8"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-table stripe border :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column label="增值服务内容" min-width="118" align="center">
                <template>学习规则</template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">学习规则</div>
                  <div v-else-if="scope.$index === 1">智能学习</div>
                  <div v-else-if="scope.$index === 2">分销服务（专业版）</div>
                  <div v-else>分销服务（基础版）</div>
                </template>
              </el-table-column>
              <el-table-column label="规则说明" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    指提供网校学习规则配置功能，当学员的学习数据不符合学习规则，系统将按照学习规则生成另外的同步/模拟数据供网校使用。
                  </div>
                  <div v-else-if="scope.$index === 1">
                    指在提供完成课程学习、完成测验、完成考试的规则下，按照指定的开始学习时间和合格时间，模拟真实用户登录平台进行培训的过程。同时为学员提供一键选课功能。
                  </div>
                  <div v-else-if="scope.$index === 2">
                    指网校分销能力，开启网校分销服务后，新增供应商和分销商角色，供应商角色相关功能赋予超管，分销商角色不会默认授予，支持创建分销单位和分销商管理员，支持分销商管理员登录网校管理域开展分销业务。网校新增一级功能菜单营销中心-分销管理，新增分销商销售统计、分销商品销售统计报表。
                  </div>
                  <div v-else>
                    指网校分销能力，开启网校分销服务后，新增供应商和分销商角色，供应商角色相关功能赋予超管，分销商角色不会默认授予，支持创建分销单位和分销商管理员，网校新增一级功能菜单营销中心-分销管理，新增分销商销售统计、分销商品销售统计报表。开启基础版服务后，分销商不支持管理分销业务，仅支持查看交易数据和报表。
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-drawer>
        <!--增值服务提示2-->
        <el-button @click="dialog9 = true" type="primary" class="f-mr20 f-mt20">增值服务提示2</el-button>
        <el-dialog title="增值服务" :visible.sync="dialog9" width="460px" class="m-dialog">
          <div>请选择开通的分销服务类型，保存成功后不支持修改，请谨慎操作：</div>
          <div class="f-mt15">
            <el-radio v-model="radio1" label="1" class="f-mr30">基础版</el-radio
            ><el-radio v-model="radio2" label="2">专业版</el-radio>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认</el-button>
          </div>
        </el-dialog>
        <!--增值服务提示3-->
        <el-button @click="dialog10 = true" type="primary" class="f-mr20 f-mt20">增值服务提示3</el-button>
        <el-dialog title="增值服务" :visible.sync="dialog10" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">确认关闭该增值服务吗？</span>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        tabPosition: 'left',
        props: { multiple: true },
        radio: 3,
        radio2: 3,
        input: '',
        select: '',
        options: [
          {
            value: 'kemu',
            label: '专业类别',
            children: [
              {
                value: 'jiaoshi',
                label: '教师系列',
                children: [
                  {
                    value: 'jiaoshi-1',
                    label: '高等学校教师（思想政治教育专职教师）'
                  },
                  {
                    value: 'jiaoshi-2',
                    label: '党校教师'
                  },
                  {
                    value: 'jiaoshi-3',
                    label: '技工学校教师'
                  },
                  {
                    value: 'jiaoshi-4',
                    label: '中学教师'
                  },
                  {
                    value: 'jiaoshi-5',
                    label: '小学教师'
                  },
                  {
                    value: 'jiaoshi-6',
                    label: '幼儿园教师'
                  },
                  {
                    value: 'jiaoshi-7',
                    label: '高校实验专业'
                  },
                  {
                    value: 'jiaoshi-8',
                    label: '中等职业技术学校教师'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '工程系列',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'zhuanye',
            label: '科目类型',
            children: [
              {
                value: 'basic',
                label: '教师系列',
                children: [
                  {
                    value: 'layout',
                    label: '高等学校教师（思想政治教育专职教师）'
                  },
                  {
                    value: 'color',
                    label: '党校教师'
                  },
                  {
                    value: 'typography',
                    label: '技工学校教师'
                  },
                  {
                    value: 'icon',
                    label: '技工学校教师'
                  },
                  {
                    value: 'button',
                    label: '中学教师'
                  },
                  {
                    value: 'button',
                    label: '小学教师'
                  },
                  {
                    value: 'button',
                    label: '幼儿园教师'
                  },
                  {
                    value: 'button',
                    label: '高校实验专业'
                  },
                  {
                    value: 'button',
                    label: '中等职业技术学校教师'
                  }
                ]
              },
              {
                value: 'form',
                label: '工程系列',
                children: [
                  {
                    value: 'radio',
                    label: 'Radio 单选框'
                  },
                  {
                    value: 'checkbox',
                    label: 'Checkbox 多选框'
                  },
                  {
                    value: 'input',
                    label: 'Input 输入框'
                  },
                  {
                    value: 'input-number',
                    label: 'InputNumber 计数器'
                  },
                  {
                    value: 'select',
                    label: 'Select 选择器'
                  },
                  {
                    value: 'cascader',
                    label: 'Cascader 级联选择器'
                  },
                  {
                    value: 'switch',
                    label: 'Switch 开关'
                  },
                  {
                    value: 'slider',
                    label: 'Slider 滑块'
                  },
                  {
                    value: 'time-picker',
                    label: 'TimePicker 时间选择器'
                  },
                  {
                    value: 'date-picker',
                    label: 'DatePicker 日期选择器'
                  },
                  {
                    value: 'datetime-picker',
                    label: 'DateTimePicker 日期时间选择器'
                  },
                  {
                    value: 'upload',
                    label: 'Upload 上传'
                  },
                  {
                    value: 'rate',
                    label: 'Rate 评分'
                  },
                  {
                    value: 'form',
                    label: 'Form 表单'
                  }
                ]
              },
              {
                value: 'data',
                label: '农业系列',
                children: [
                  {
                    value: 'table',
                    label: 'Table 表格'
                  },
                  {
                    value: 'tag',
                    label: 'Tag 标签'
                  },
                  {
                    value: 'progress',
                    label: 'Progress 进度条'
                  },
                  {
                    value: 'tree',
                    label: 'Tree 树形控件'
                  },
                  {
                    value: 'pagination',
                    label: 'Pagination 分页'
                  },
                  {
                    value: 'badge',
                    label: 'Badge 标记'
                  }
                ]
              },
              {
                value: 'notice',
                label: 'Notice',
                children: [
                  {
                    value: 'alert',
                    label: 'Alert 警告'
                  },
                  {
                    value: 'loading',
                    label: 'Loading 加载'
                  },
                  {
                    value: 'message',
                    label: 'Message 消息提示'
                  },
                  {
                    value: 'message-box',
                    label: 'MessageBox 弹框'
                  },
                  {
                    value: 'notification',
                    label: 'Notification 通知'
                  }
                ]
              },
              {
                value: 'others',
                label: 'Others',
                children: [
                  {
                    value: 'dialog',
                    label: 'Dialog 对话框'
                  },
                  {
                    value: 'tooltip',
                    label: 'Tooltip 文字提示'
                  },
                  {
                    value: 'popover',
                    label: 'Popover 弹出框'
                  },
                  {
                    value: 'card',
                    label: 'Card 卡片'
                  },
                  {
                    value: 'carousel',
                    label: 'Carousel 走马灯'
                  },
                  {
                    value: 'collapse',
                    label: 'Collapse 折叠面板'
                  }
                ]
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible1: false,
        dialogVisible2: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down'],
        data: [
          {
            label: '教师系列',
            children: [
              {
                label: '高等学校教师（思想政治教育专职教师）'
              },
              {
                label: '党校教师'
              },
              {
                label: '技工学校教师'
              },
              {
                label: '中学教师'
              },
              {
                label: '小学教师'
              },
              {
                label: '幼儿园教师'
              },
              {
                label: '高校实验专业'
              },
              {
                label: '中等职业技术学校教师'
              }
            ]
          },
          {
            label: '工程系列'
          },
          {
            label: '农业系列',
            children: [
              {
                label: '农业系列2-1'
              },
              {
                label: '农业系列2-2'
              },
              {
                label: '农业系列2-3'
              }
            ]
          }
        ],
        data2: [
          {
            label: '专业技术人员培训'
          },
          {
            label: '建筑施工特种作业人员',
            children: [
              {
                label: '桩工机械操作工'
              },
              {
                label: '建筑起重机械维修工'
              },
              {
                label: '建筑起重机械安装拆卸工（T）'
              },
              {
                label: '建筑起重机械安装拆卸工（T、S）'
              },
              {
                label: '高处作业吊篮安装拆卸工'
              },
              {
                label: '建筑起重机械司机（T）'
              },
              {
                label: '建筑起重机械司机（S）'
              },
              {
                label: '建筑起重机械司机（T、S）'
              },
              {
                label: '建筑起重信号司索工'
              },
              {
                label: '建筑架子工（P）'
              },
              {
                label: '建筑电工'
              }
            ]
          },
          {
            label: '施工现场专业人员换证培训',
            children: [
              {
                label: '劳务员'
              },
              {
                label: '机械员'
              },
              {
                label: '安全员'
              },
              {
                label: '土建质量员'
              },
              {
                label: '市政工程施工员'
              },
              {
                label: '土建施工员'
              }
            ]
          },
          {
            label: '施工现场专业人员',
            children: [
              {
                label: '资料员'
              },
              {
                label: '劳务员'
              },
              {
                label: '机械员'
              },
              {
                label: '材料员'
              },
              {
                label: '安全员'
              },
              {
                label: '市政工程质量员'
              },
              {
                label: '装饰装修质量员'
              },
              {
                label: '土建质量员'
              },
              {
                label: '设备安装施工员'
              },
              {
                label: '装饰装修施工员'
              },
              {
                label: '土建施工员'
              }
            ]
          },
          {
            label: '安全生产管理人员'
          },
          {
            label: '注册结构工程师'
          },
          {
            label: '注册建筑师'
          },
          {
            label: '注册土木工程师（岩土）'
          },
          {
            label: '注册监理工程师'
          },
          {
            label: '注册造价工程师'
          },
          {
            label: '注册城市规划师'
          },
          {
            label: '注册房地产估价师'
          },
          {
            label: '临时二级注册建造师'
          },
          {
            label: '二级注册建造师',
            children: [
              {
                label: '高处作业吊篮安装拆卸工'
              },
              {
                label: '矿业工程'
              },
              {
                label: '公路工程'
              },
              {
                label: '机电工程'
              },
              {
                label: '水利水电工程'
              },
              {
                label: '市政公用工程'
              },
              {
                label: '建筑工程'
              }
            ]
          },
          {
            label: '一级注册建造师',
            children: [
              {
                label: '机电工程'
              },
              {
                label: '通信与广电工程'
              },
              {
                label: '市政公用工程'
              },
              {
                label: '港口与航道工程'
              },
              {
                label: '建筑工程'
              }
            ]
          }
        ],
        defaultProps: {
          children: 'children',
          label: 'label'
        }
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
