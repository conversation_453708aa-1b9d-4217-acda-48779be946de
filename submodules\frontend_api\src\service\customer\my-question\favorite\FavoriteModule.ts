import store from '../../../../store'
import { Module as Mod } from 'vuex'
import { Module, VuexModule, getModule, Action, Mutation } from 'vuex-module-decorators'
import PlatformExamGateway, {
  UserFavoriteQuestionStatisticDTO,
  UserFavoriteQuestionStatisticParamDTO
} from '@api/gateway/PlatformExam'
import Response, { ResponseStatus } from '../../../../Response'
import ChapterTree from '../../../common/syllabus/model/ChapterTree'
import { Role, RoleType } from '../../../../Secure'
import FavoritePracticePaper from '@api/service/customer/my-question/favorite/models/FavoritePracticePaper'
import FavoritePracticeAnswer from '@api/service/customer/my-question/favorite/models/FavoritePracticeAnswer'
import Chapter from '@api/service/common/models/syllabus/Chapter'
import SyllabusModule from '@api/service/customer/syllabus/SyllabusModule'

class StateCache {
  constructor(schemeId: string, issueId: string, majorId: string) {
    this.schemeId = schemeId
    this.issueId = issueId
    this.majorId = majorId
  }

  // 方案id
  schemeId: string
  // 方式id
  issueId: string
  // 专业id
  majorId: string
  // 收藏题卷
  favoritePracticePaper: FavoritePracticePaper = new FavoritePracticePaper()
  // 答卷列表
  latestFavoritePracticeAnswer: Array<FavoritePracticeAnswer> = new Array<FavoritePracticeAnswer>()
  // 各章节试题统计
  allChapterQuestionStatistic: Array<UserFavoriteQuestionStatisticDTO> = new Array<UserFavoriteQuestionStatisticDTO>()
  // 各题型试题统计
  allQuestionTypeQuestionStatistic: Array<UserFavoriteQuestionStatisticDTO> = new Array<
    UserFavoriteQuestionStatisticDTO
  >()
  // 各试题类型试题统计
  allQuestionCategoryQuestionStatistic: Array<UserFavoriteQuestionStatisticDTO> = new Array<
    UserFavoriteQuestionStatisticDTO
  >()
  // 各题型7天前试题统计
  allQuestionTypeQuestion7DaysBeforeStatistic: Array<UserFavoriteQuestionStatisticDTO> = new Array<
    UserFavoriteQuestionStatisticDTO
  >()
  // 是否需要重载，在学员触发去作答练习是置为true，在下次取数时先清空数据然后加载新数据
  needReload = false
  // 最新一次加载时间，从apollo配置的超时时间与当前时间判断，在下次取数时清空当前取数数据然后加载新数据
  latestLoadTime: Date = new Date()
}

export interface IState {
  /**
   * 各学习方式收藏题数据
   */
  learningFavoritePracticeListMap: Array<StateCache>
  currentAnswerId: string
}

export class FavoriteQuestionSyllabusTree {
  /**
   * 章节编号，对应标签编号
   */
  id: string
  /**
   * 章节名称，对应标签名称
   */
  name: string
  /**
   * 当前节点关系编号
   * @description 由于同一个标签可能存在在同一个关系中的不同节点位置,
   * 只有节点关系编号才能定位一个章节的位置
   *
   */
  relationId: string
  /**
   * 同一级序号
   */
  sequence: number
  /**
   * 子章节
   */
  children: Array<FavoriteQuestionSyllabusTree> = new Array<FavoriteQuestionSyllabusTree>()

  /**
   * 是否正在作答
   */
  answering: boolean

  /**
   * 是否作答完毕
   */
  answered: boolean

  /**
   * 试题数量
   */
  questionCount: number

  /**
   * 作答id
   */
  answerId: string
  /**
   * 作答记录id
   */
  answerRecordId: string

  hasChild() {
    return this.children && this.children.length > 0
  }
}

export class FavoriteQuestionTypeItem {
  /**
   * 试题题型 - 按题型分组统计，该字段才生效
   */
  questionType: string
  /**
   * 试题数量
   */
  questionCount: number
  /**
   * 是否正在作答
   */
  answering: boolean

  /**
   * 是否作答完毕
   */
  answered: boolean
  /**
   * 作答id
   */
  answerId: string
  /**
   * 作答记录id
   */
  answerRecordId: string
}

@Module({
  namespaced: true,
  name: 'CustomerFavoriteModule',
  store,
  dynamic: true
})
class FavoriteModule extends VuexModule implements IState {
  /**
   * 各学习方式收藏题数据
   */
  learningFavoritePracticeListMap = new Array<StateCache>()
  currentAnswerId = ''

  constructor(module: Mod<ThisType<any>, any>) {
    super(module)
    store.subscribe(mutation => {
      if (mutation.type?.indexOf('ALERT_EXAM_RELOAD') !== -1) {
        this.processSubmitPaperMessage(mutation.payload)
      }
    })
  }
  // 去做提前需要先init
  @Role([RoleType.user])
  @Action
  async init(payload: any) {
    if (
      !this.learningFavoritePracticeListMap.find(p => p.issueId === payload.issueId) ||
      this.learningFavoritePracticeListMap.find(p => p.issueId === payload.issueId)?.needReload
    ) {
      if (!this.learningFavoritePracticeListMap.find(p => p.issueId === payload.issueId)) {
        const stateCache = new StateCache(payload.schemeId, payload.issueId, payload.majorId)
        this.setStateCacheToLearningFavoritePracticeListMap(stateCache)
      }
      // 获取收藏题卷子
      const stateCache = new StateCache(payload.schemeId, payload.issueId, payload.majorId)
      let response: Response<any>
      // let response: Response<any> = await PreExamGateway.getOrCreateFavoritePracticePaper({
      //   schemeId: payload.schemeId,
      //   issueId: payload.issueId
      // })
      // if (!response.status.isSuccess()) {
      //   return response.status
      // }
      // stateCache.favoritePracticePaper = response.data
      //
      // // 获取收藏题各章节或题型最新一份答卷
      // response = await PreExamGateway.getPreExamFavoritePracticeList(stateCache.favoritePracticePaper.id)
      // if (!response.status.isSuccess()) {
      //   return response.status
      // }
      // stateCache.latestFavoritePracticeAnswer = response.data

      // 初始化考纲信息
      const status = await SyllabusModule.init()
      if (!status.isSuccess()) {
        return status
      }
      const leafSyllabus: Array<Chapter> = SyllabusModule.getLeafSyllabusByMajorId(payload.majorId)

      const paramDTO: UserFavoriteQuestionStatisticParamDTO = new UserFavoriteQuestionStatisticParamDTO()
      paramDTO.schemeId = payload.schemeId
      paramDTO.issueId = payload.issueId
      paramDTO.tagIdList = leafSyllabus.map(leaf => leaf.id)
      // 获取各章节试题统计
      response = await PlatformExamGateway.statisticUserFavoriteQuestionGroupByTag(paramDTO)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allChapterQuestionStatistic = response.data
      // 获取各题型试题统计
      response = await PlatformExamGateway.statisticUserFavoriteQuestionGroupByQuestionType(paramDTO)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionTypeQuestionStatistic = response.data
      // 获取试题类型试题统计
      response = await PlatformExamGateway.statisticUserFavoriteQuestionGroupByQuestionCategory(paramDTO)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionCategoryQuestionStatistic = response.data

      const beforeParamDTO: UserFavoriteQuestionStatisticParamDTO = new UserFavoriteQuestionStatisticParamDTO()
      beforeParamDTO.schemeId = payload.schemeId
      beforeParamDTO.issueId = payload.issueId
      beforeParamDTO.tagIdList = leafSyllabus.map(leaf => leaf.id)
      // beforeParamDTO.beforeDays = 7
      // 获取各题型7天前试题统计
      response = await PlatformExamGateway.statisticUserFavoriteQuestionGroupByQuestionType(beforeParamDTO)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allQuestionTypeQuestion7DaysBeforeStatistic = response.data
      this.setStateCacheToLearningFavoritePracticeListMap(stateCache)
    }
    return new ResponseStatus(200)
  }

  /**
   * 去做指定章节的练习
   * @param payload
   */
  @Role([RoleType.user])
  async goExamOutlinePractice(payload: {
    userId: string
    schemeId: string
    issueId: string
    chapterId: string
    totalQuestionSize: number
  }) {
    console.log(payload)
    // const init: FavoriteAnswerInitRequest = new FavoriteAnswerInitRequest()
    // const leafSyllabus: Array<Chapter> = SyllabusModule.getLeafSyllabusByChapterId(payload.chapterId)
    // init.examOutlineId = payload.chapterId
    // init.fetchWay = 1
    // init.paperId = this.getFavoritePracticePaper(payload.schemeId, payload.issueId)?.id || ''
    // init.totalQuestionSize = payload.totalQuestionSize
    // init.questionType = -1
    // init.tagIds = leafSyllabus.map(leaf => leaf.id)
    // const response = await PreExamGateway.goFavoritePractice(init)
    // if (!response.status.isSuccess()) {
    //   return response.status
    // }
    // this.setAnswerId(response.data)
    // return response.status
  }

  /**
   * 去做指定章节的练习
   * @param ctx
   */
  @Role([RoleType.user])
  async goQuestionTypePractice(payload: {
    userId: string
    schemeId: string
    issueId: string
    majorId: string
    questionType: number
    totalQuestionSize: number
  }) {
    console.log(payload)
    // const init: FavoriteAnswerInitRequest = new FavoriteAnswerInitRequest()
    // const leafSyllabus: Array<Chapter> = SyllabusModule.getLeafSyllabusByMajorId(payload.majorId)
    // init.fetchWay = 3
    // init.paperId = this.getFavoritePracticePaper(payload.schemeId, payload.issueId)?.id || ''
    // init.totalQuestionSize = payload.totalQuestionSize
    // init.questionType = payload.questionType
    // init.tagIds = leafSyllabus.map(leaf => leaf.id)
    // const response = await PreExamGateway.goFavoritePractice(init)
    // if (!response.status.isSuccess()) {
    //   return response.status
    // }
    // this.setAnswerId(response.data)
    // return response.status
  }

  /**
   * 处理交卷消息
   * @param payload
   */
  async processSubmitPaperMessage(payload: { schemeId: string; issueId: string; answersId: string }) {
    this.learningFavoritePracticeListMap.forEach((stateCache: StateCache) => {
      if (stateCache?.issueId === payload.issueId) {
        console.log('收藏题匹配到，needReload置为true' + JSON.stringify(payload))
        this.setNeedReload({
          schemeId: stateCache.schemeId,
          issueId: stateCache.issueId,
          majorId: stateCache.majorId
        })
      }
    })
  }

  @Mutation
  setStateCacheToLearningFavoritePracticeListMap(payload: StateCache) {
    this.learningFavoritePracticeListMap = this.learningFavoritePracticeListMap.filter(
      p => p.issueId !== payload.issueId
    )
    this.learningFavoritePracticeListMap.push(payload)
  }

  @Mutation
  setNeedReload(payload: any) {
    const stateCache = this.learningFavoritePracticeListMap.find(p => p.issueId === payload.issueId)
    if (stateCache) {
      stateCache.needReload = true
    }
  }

  @Mutation
  setAnswerId(answerId: string) {
    this.currentAnswerId = answerId
  }

  /**
   * 获取收藏题试卷
   */
  get getFavoritePracticePaper() {
    return (schemeId: string, issueId: string) => {
      return this.learningFavoritePracticeListMap.find(p => p.issueId === issueId)?.favoritePracticePaper
    }
  }

  /**
   * 获取收藏题试卷最后作答信息
   */
  get getLatestFavoritePracticeAnswer() {
    return (schemeId: string, issueId: string) => {
      return this.learningFavoritePracticeListMap.find(p => p.issueId === issueId)?.latestFavoritePracticeAnswer
    }
  }

  /**
   * 获取收藏题试卷最后作答信息
   * @param state
   */
  get getAllQuestionTypeQuestionStatistic() {
    return (schemeId: string, issueId: string) => {
      return this.learningFavoritePracticeListMap.find(p => p.issueId === issueId)?.allQuestionTypeQuestionStatistic
    }
  }

  /**
   * 获取收藏题试卷最后作答信息
   * @param state
   */
  get getAllQuestionTypeQuestion7DaysBeforeStatistic() {
    return (schemeId: string, issueId: string) => {
      return this.learningFavoritePracticeListMap.find(p => p.issueId === issueId)
        ?.allQuestionTypeQuestion7DaysBeforeStatistic
    }
  }

  /**
   * 获取收藏题试卷最后作答信息
   * @param state
   */
  get getAllChapterQuestionStatistic() {
    return (schemeId: string, issueId: string) => {
      return this.learningFavoritePracticeListMap.find(p => p.issueId === issueId)?.allChapterQuestionStatistic
    }
  }

  // 当前章节或题型答卷是否正在作答
  get isAnswering() {
    return (
      schemeId: string,
      issueId: string,
      fetchWay: number,
      examinationOutlineId: string | null,
      questionType: string
    ) => {
      const practiceAnswer = this.getLatestFavoritePracticeAnswer(schemeId, issueId)?.find(
        (p: FavoritePracticeAnswer) => {
          if (fetchWay === 3) {
            return p.fetchWay === fetchWay && p.questionType === questionType
          } else {
            return p.fetchWay === fetchWay && p.examinationOutlineId === examinationOutlineId
          }
        }
      )
      return practiceAnswer ? !practiceAnswer.complete : false
    }
  }

  // 当前章节或题型答卷是否已交卷
  get isAnswered() {
    return (
      schemeId: string,
      issueId: string,
      fetchWay: number,
      examinationOutlineId: string | null,
      questionType: string
    ) => {
      const practiceAnswer = this.getLatestFavoritePracticeAnswer(schemeId, issueId)?.find(
        (p: FavoritePracticeAnswer) => {
          if (fetchWay === 3) {
            return p.fetchWay === fetchWay && p.questionType === questionType
          } else {
            return p.fetchWay === fetchWay && p.examinationOutlineId === examinationOutlineId
          }
        }
      )
      return practiceAnswer?.complete || false
    }
  }

  // 当前章节或题型答卷
  get answerPaper() {
    return (
      schemeId: string,
      issueId: string,
      fetchWay: number,
      examinationOutlineId: string | null,
      questionType: string
    ) => {
      const practiceAnswer = this.getLatestFavoritePracticeAnswer(schemeId, issueId)?.find(
        (p: FavoritePracticeAnswer) => {
          if (fetchWay === 3) {
            return p.fetchWay === fetchWay && p.questionType === questionType
          } else {
            return p.fetchWay === fetchWay && p.examinationOutlineId === examinationOutlineId
          }
        }
      )
      return practiceAnswer
    }
  }

  // 各题型收藏统计
  get allQuestionTypeQuestionStatistic() {
    return (schemeId: string, issueId: string) => {
      return this.getAllQuestionTypeQuestionStatistic(schemeId, issueId)?.map((p: UserFavoriteQuestionStatisticDTO) => {
        const item: FavoriteQuestionTypeItem = new FavoriteQuestionTypeItem()
        item.questionType = p.questionType
        item.questionCount = p.questionCount
        item.answering = this.isAnswering(schemeId, issueId, 3, null, p.questionType)
        item.answered = this.isAnswered(schemeId, issueId, 3, null, p.questionType)
        item.answerId = this.answerPaper(schemeId, issueId, 3, null, p.questionType)?.answerId || ''
        item.answerRecordId = this.answerPaper(schemeId, issueId, 3, null, p.questionType)?.id || ''
        return item
      })
    }
  }

  // 指定章节的收藏题总数
  get specifyExaminationOutlineQuestionCount() {
    return (schemeId: string, issueId: string, leafSyllabus: Array<string>) => {
      return (
        this.getAllChapterQuestionStatistic(schemeId, issueId)
          ?.filter(
            (p: UserFavoriteQuestionStatisticDTO) =>
              p.tags && p.tags.some((tagId: string) => leafSyllabus.includes(tagId))
          )
          .map((p: UserFavoriteQuestionStatisticDTO) => p.questionCount)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  // 根据考纲树获取试题及作答统计信息
  get allChapterQuestionStatisticWithSyllabus() {
    return (schemeId: string, issueId: string, syllabusTree: Array<ChapterTree>) => {
      // 自定义函数，填充已答试题信息
      const fillQuestionAnswerStatistic = (syllabusTree: Array<ChapterTree>) => {
        const questionPracticeSyllabusTree: Array<FavoriteQuestionSyllabusTree> = new Array<
          FavoriteQuestionSyllabusTree
        >()
        syllabusTree.forEach((syllabus: ChapterTree) => {
          const questionPracticeSyllabus: FavoriteQuestionSyllabusTree = new FavoriteQuestionSyllabusTree()
          questionPracticeSyllabus.id = syllabus.id
          questionPracticeSyllabus.name = syllabus.name
          questionPracticeSyllabus.relationId = syllabus.relationId
          questionPracticeSyllabus.sequence = syllabus.sequence
          questionPracticeSyllabus.answering = this.isAnswering(schemeId, issueId, 1, syllabus.id, '')
          questionPracticeSyllabus.answered = this.isAnswered(schemeId, issueId, 1, syllabus.id, '')
          questionPracticeSyllabus.answerId = this.answerPaper(schemeId, issueId, 1, syllabus.id, '')?.answerId || ''
          questionPracticeSyllabus.answerRecordId = this.answerPaper(schemeId, issueId, 1, syllabus.id, '')?.id || ''
          questionPracticeSyllabus.questionCount = this.specifyExaminationOutlineQuestionCount(
            schemeId,
            issueId,
            syllabus.getLeafChapterIds()
          )
          if (syllabus.children) {
            questionPracticeSyllabus.children = fillQuestionAnswerStatistic(syllabus.children)
          }
          questionPracticeSyllabusTree.push(questionPracticeSyllabus)
        })
        return questionPracticeSyllabusTree
      }
      return fillQuestionAnswerStatistic(syllabusTree)
    }
  }

  // 收藏题总数
  get totalFavoriteQuestionCount() {
    return (schemeId: string, issueId: string) => {
      return (
        this.getAllQuestionTypeQuestionStatistic(schemeId, issueId)
          ?.map((p: UserFavoriteQuestionStatisticDTO) => p.questionCount)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }

  // 7日前收藏题总数
  get totalFavoriteQuestion7DaysBeforeCount() {
    return (schemeId: string, issueId: string) => {
      return (
        this.getAllQuestionTypeQuestion7DaysBeforeStatistic(schemeId, issueId)
          ?.map((p: UserFavoriteQuestionStatisticDTO) => p.questionCount)
          .reduce((a: number, b: number) => a + b, 0) || 0
      )
    }
  }
}

export default getModule(FavoriteModule)
