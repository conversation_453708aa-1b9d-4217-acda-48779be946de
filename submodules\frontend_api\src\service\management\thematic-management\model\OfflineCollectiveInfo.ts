import {
  SaveTrainingChannelOfflineCollectiveSignUpSettingsRequest,
  SignUpStep,
  UpdateTrainingChannelOfflineCollectiveSignUpSettingsRequest
} from '@api/platform-gateway/platform-training-channel-v1'
import { TrainingChannelOfflineCollectiveSignUpSettingResponse } from '@api/platform-gateway/platform-training-channel-back-gateway'

export default class OfflineCollectiveInfo {
  /**
   * id
   */
  id = ''
  /**
   * 线下集体报名入口是否
   */
  offlineCollectiveEntry = false
  /**
   * 线下集体报名入口图片
   */
  offlineCollectiveEntryPicture = ''
  /**
   * 线下集体报名名称
   */
  offlineCollectiveEntryName = ''
  /**
   * 线下集体报名模板
   */
  offlineCollectiveEntryTemplate: { name?: string; url?: string } = null
  /**
   * 底部文本说明
   */
  bottomText = ''
  /**
   * 报名步骤
   */
  steps: { no?: number; title?: string; content?: string; id?: string }[] = []
  /**
   * 班级报名链接
   */
  signUpClassUrl = `${window.location.origin}/collective-registry`

  static to(
    vo: OfflineCollectiveInfo,
    dto:
      | SaveTrainingChannelOfflineCollectiveSignUpSettingsRequest
      | UpdateTrainingChannelOfflineCollectiveSignUpSettingsRequest
  ) {
    dto.name = vo.offlineCollectiveEntryName
    dto.templateAttachment = vo.offlineCollectiveEntryTemplate
    dto.entryPictureAttachments = [
      {
        url: vo.offlineCollectiveEntryPicture
      }
    ]
    dto.openEntrySwitch = vo.offlineCollectiveEntry
    dto.accessUrl = vo.signUpClassUrl
    dto.signUpSteps = vo.steps.map(step => {
      const item = new SignUpStep()
      item.index = step.no || undefined
      item.contentId = step.id || undefined
      item.content = step.content || undefined
      item.title = step.title || undefined
      return item
    })
    dto.bottomDescription = vo.bottomText
    return dto
  }

  static from(dto: TrainingChannelOfflineCollectiveSignUpSettingResponse) {
    const vo = new OfflineCollectiveInfo()
    vo.id = dto.id
    vo.offlineCollectiveEntry = dto.openEntrySwitch
    vo.offlineCollectiveEntryPicture = dto.entryPictureAttachments[0]?.url || ''
    vo.offlineCollectiveEntryName = dto.name
    vo.offlineCollectiveEntryTemplate = dto.templateAttachment
    vo.bottomText = dto.bottomDescription
    vo.steps = dto.signUpSteps.map(step => {
      const item = {
        no: step.index,
        title: step.title,
        content: step.content,
        id: step.contentId
      }
      return item
    })
    return vo
  }
  /**
   * 新增
   */
  addStep() {
    this.steps.push({ no: this.steps.length + 1, title: '', content: '' })
  }

  /**
   * 删除
   * @param no 序号
   */
  removeStep(no: number) {
    this.steps.splice(no - 1, 1)
    this.steps?.forEach((step, index) => {
      step.no = index + 1
    })
  }

  /**
   * 下载示例模板
   * @param id 专题id
   * @return {string}
   */
  downloadTemplate(id?: string) {
    return ''
  }
}
