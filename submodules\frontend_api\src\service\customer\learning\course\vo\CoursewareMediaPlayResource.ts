import { CoursewareMediaPreviewResourceResponse } from '@api/ms-gateway/ms-course-play-resource-v1'
import MediaResource from '@api/service/customer/learning/course/vo/MediaResource'

class MediaPlayResource extends MediaResource {
  /**
   * 类型转换
   * @param remoteResponse
   */
  static from(remoteResponse: CoursewareMediaPreviewResourceResponse) {
    const mediaPlayResource = new MediaPlayResource()
    mediaPlayResource.id = remoteResponse.id
    mediaPlayResource.type = remoteResponse.type
    mediaPlayResource.timeLength = remoteResponse.timeLength
    mediaPlayResource.documentList = remoteResponse.documentList
    mediaPlayResource.videoPlayResourceList = remoteResponse.videoPlayResourceList
    mediaPlayResource.videoCaptionList = remoteResponse.videoCaptionList
    mediaPlayResource.videoTranscodingList = remoteResponse.videoTranscodingList
    mediaPlayResource.lectureList = remoteResponse.lectureList
    return mediaPlayResource
  }
}

export default MediaPlayResource
