import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-teachingplan-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-teachingplan-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Property {
  name: string
  value?: string
}

/**
 * [教学地点]
<AUTHOR> [xwj]
@createTime : [2022/8/26 11:01]
 */
export class PlanAddress {
  /**
   * 教学地点Id
   */
  id?: string
  /**
   * 地点名称
   */
  addressName?: string
  /**
   * 教学地点性质
   */
  nature: number
  /**
   * 维度
   */
  lat: number
  /**
   * 经度
   */
  lng: number
  /**
   * 有效半径
   */
  diameter?: number
  /**
   * 地点数据（冗余，供地图工具使用）
   */
  addressData?: string
  /**
   * 是否启用
   */
  enabled?: boolean
  /**
   * 创建人Id
   */
  createdUserId?: string
  /**
   * 创建时间
   */
  createdTime?: string
  /**
   * 更新时间
   */
  updatedTime?: string
  /**
   * 培训点ID
   */
  trainingPointId?: string
}

/**
 * [教学计划资源]
<AUTHOR> [xwj]
@createTime : [2022/8/26 11:40]
 */
export class PlanResource {
  /**
   * 资源id
   */
  id?: string
  /**
   * 资源名称
   */
  name?: string
  /**
   * 教学计划id
   */
  planId?: string
  /**
   * 教学计划项组id
   */
  planItemGroupId?: string
  /**
   * 教学计划项id
   */
  planItemId?: string
  /**
   * 教学资源类型
@see com.fjhb.domain.teachingplan.api.plan.consts.PlanResourceTypes
   */
  resourceType: number
  /**
   * 教学资源内容
   */
  resourceContent?: string
}

/**
 * [教学人员]
<AUTHOR> [xwj]
@createTime : [2022/8/19 10:08]
 */
export class PlanTeacher {
  /**
   * 教学人员id
   */
  id?: string
  /**
   * 教学人员类型
1-讲师 2-助教
@see com.fjhb.domain.teachingplan.api.plan.consts.PlanTeacherTypes
   */
  teacherType: number
  /**
   * 教学人员性质
@see com.fjhb.domain.teachingplan.api.plan.consts.PlanTeacherNature
   */
  nature: number
  /**
   * 教学人员名称
   */
  teacherName?: string
  /**
   * 教学人员关联id
   */
  teacherReferenceId?: string
  /**
   * 教学人员来源类型
@see PlanTeacherSourceTypes
   */
  sourceType?: number
  /**
   * 单位名称
   */
  unitName?: string
  /**
   * 扩展属性集合
   */
  properties?: Array<Property>
}

/**
 * 创建教学计划请求
<AUTHOR>
@date 2024/8/24 13:06
 */
export class CreatePlanItemRequest {
  /**
   * 六个字段通用
   */
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  unitId?: string
  servicerId?: string
  /**
   * 创建人Id
   */
  createdUserId?: string
  /**
   * 教学计划项id
   */
  id?: string
  /**
   * 教学计划Id
   */
  planId?: string
  /**
   * 教学计划组Id
   */
  planItemGroupId?: string
  /**
   * 教学计划项名称
   */
  name?: string
  /**
   * 相关教学人员
   */
  teachers?: Array<PlanTeacher>
  /**
   * 简介
   */
  abouts?: string
  /**
   * 教学模式
@see com.fjhb.domain.teachingplan.api.plan.consts.PlanModes
   */
  planMode: number
  /**
   * 时长（秒）
   */
  timeLength?: number
  /**
   * 教学资源
   */
  resources?: Array<PlanResource>
  /**
   * 学时（课时）
   */
  period?: number
  /**
   * 教学计划项类型
@see com.fjhb.domain.teachingplan.api.plan.consts.PlanItemTypes
   */
  planItemType: number
  /**
   * 教学开始时间
   */
  startTime?: string
  /**
   * 教学结束时间
   */
  endTime?: string
  /**
   * 教学地址
   */
  address?: PlanAddress
  /**
   * 排序
   */
  sort?: number
  /**
   * 创建时间
   */
  createdTime?: string
}

/**
 * <AUTHOR> [2023/4/14 14:29]
 */
export class CustomSignInRequest {
  /**
   * 学习token
   */
  token?: string
  /**
   * 签到时间
   */
  signTime?: string
  /**
   * 签到Key
   */
  pointKey?: string
  /**
   * 维度
   */
  lat: number
  /**
   * 经度
   */
  lng: number
}

/**
 * 重新触发学时计算接口
<AUTHOR> [2023/7/14 11:58]
 */
export class RecalculatePeriodByPlanItemIdAndStudentNoRequest {
  /**
   * 学号
   */
  studentNos?: Array<string>
  /**
   * 教学计划项id
   */
  planItemId?: string
  /**
   * 教学计划id
   */
  planId?: string
}

/**
 * [签到请求]
<AUTHOR> [xwj]
@createTime : [2022/10/20 10:33]
 */
export class SignInRequest {
  /**
   * 签到Key
   */
  pointKey?: string
  /**
   * 维度
   */
  lat: number
  /**
   * 经度
   */
  lng: number
}

/**
 * 学员打印听课证请求
 */
export class StudentPrintCourseCardRequest {
  /**
   * 用户ID
   */
  studentId?: string
  /**
   * 听课证模板ID
   */
  templateId?: string
  /**
   * 学习方案ID
   */
  learnSchemeId?: string
  /**
   * 期别ID
   */
  issueId?: string
}

/**
 * <AUTHOR>
@since 安徽建设
 */
export class TrainingPointCreateRequest {
  /**
   * 培训点名称
   */
  name?: string
  /**
   * 经度
   */
  longitude: number
  /**
   * 纬度
   */
  latitude: number
  /**
   * 选中的培训地址
   */
  specificAddress?: string
  /**
   * 所在地区
   */
  areaPath?: string
  /**
   * 培训教室
   */
  classRoom?: string
  /**
   * 单位id
   */
  ownerId?: string
}

/**
 * 培训点删除
<AUTHOR>
@since 安徽建设
 */
export class TrainingPointDeleteRequest {
  /**
   * id
聚合id
   */
  id?: string
}

/**
 * 培训点状态调整
<AUTHOR>
@since 安徽建设
 */
export class TrainingPointStatusRequest {
  /**
   * id
聚合id
   */
  id?: string
  /**
   * 状态
0&#x3D;停用
1&#x3D;启用
@see TrainingPointStatus
   */
  status: number
}

/**
 * 培训点更新
<AUTHOR>
@since 安徽建设
 */
export class TrainingPointUpdateRequest {
  /**
   * id
聚合id
   */
  id?: string
  /**
   * 培训点名称
   */
  name?: string
  /**
   * 所在地区
   */
  areaPath?: string
  /**
   * 培训教室
   */
  classRoom?: string
}

/**
 * [签到结果]
<AUTHOR> [xwj]
@createTime : [2022/9/1 14:37]
 */
export class SignResultResponse {
  /**
   * 学员签到id
学号+教学计划id
   */
  studentSignId: string
  /**
   * 签到结果
200 签到成功
100 迟到
300 早退
500 无效打卡时间
501 无效地点
502 已签到
   */
  code: number
  /**
   * 返回信息
   */
  message: string
}

/**
 * <AUTHOR>
@since
 */
export class TrainingPointResponse {
  /**
   * 200 正常
E500 名称重复
E501 已被引用不可删除
   */
  code: string
  /**
   * 培训点id
   */
  id: string
}

/**
 * 查询听课证模板列表响应体
 */
export class CourseCardTemplateResponse {
  /**
   * 听课证模板ID
   */
  templateId: string
  /**
   * 听课证模板名称
   */
  templateName: string
  /**
   * 听课证模板描述
   */
  templateDescription: string
  /**
   * 听课证模板使用范围
   */
  templateScope: string
  /**
   * 听课证模板创建时间
   */
  createTime: string
}

/**
 * 教学计划项返回值
<AUTHOR>
@date 2024/12/11 10:18
 */
export class PlanItemResponse {
  /**
   * 200 正常
C600 课程是否已上课
C601 是否有打卡记录
C602 课程授课时间重叠
@see PlanItemCodes
   */
  code: string
  /**
   * 教学计划项id
   */
  planItemId: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训点名称重复校验
   * @param name
   * @param excludeId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkTrainingPointName(
    params: { name?: string; excludeId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.checkTrainingPointName,
    operation?: string
  ): Promise<Response<TrainingPointResponse>> {
    return commonRequestApi<TrainingPointResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 传入的课程id是否有签到记录
   * @param query 查询 graphql 语法文档
   * @param planItemId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async hasSignRecord(
    planItemId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.hasSignRecord,
    operation?: string
  ): Promise<Response<PlanItemResponse>> {
    return commonRequestApi<PlanItemResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { planItemId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询听课程模版列表
   * @return 听课程模版列表 {@link CourseCardTemplateResponse}
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listCourseCardTemplate(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listCourseCardTemplate,
    operation?: string
  ): Promise<Response<Array<CourseCardTemplateResponse>>> {
    return commonRequestApi<Array<CourseCardTemplateResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 打印听课证模板预览
   * @param templateId 模板ID
   * @return 打印结果的文件路径
   * @param query 查询 graphql 语法文档
   * @param templateId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async printCourseCardTemplate(
    templateId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.printCourseCardTemplate,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { templateId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员打印听课证
   * @param request 学员打印听课证请求 {@link StudentPrintCourseCardRequest}
   * @return 听课证文件路径
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async studentPrintCourseCard(
    request: StudentPrintCourseCardRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.studentPrintCourseCard,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async tes(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.tes,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 教学计划项签到
   * @param token 学员学习token
   * @return 学生签到结果
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applySign(
    params: { token?: string; request?: SignInRequest },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applySign,
    operation?: string
  ): Promise<Response<SignResultResponse>> {
    return commonRequestApi<SignResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更改培训点状态
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async changeTrainingPointStatus(
    request: TrainingPointStatusRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.changeTrainingPointStatus,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 课程校验
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkPlanItem(
    params: { planItemIds?: Array<string>; schemeId?: string; issueId?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.checkPlanItem,
    operation?: string
  ): Promise<Response<Array<PlanItemResponse>>> {
    return commonRequestApi<Array<PlanItemResponse>>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建教学计划项台账接口
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createPlanItemRepair(
    request: CreatePlanItemRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createPlanItemRepair,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建培训点
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createTrainingPoint(
    request: TrainingPointCreateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createTrainingPoint,
    operation?: string
  ): Promise<Response<TrainingPointResponse>> {
    return commonRequestApi<TrainingPointResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 自定义时间签到
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async customSignIn(
    request: CustomSignInRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.customSignIn,
    operation?: string
  ): Promise<Response<SignResultResponse>> {
    return commonRequestApi<SignResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除培训点
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteTrainingPoints(
    request: TrainingPointDeleteRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteTrainingPoints,
    operation?: string
  ): Promise<Response<TrainingPointResponse>> {
    return commonRequestApi<TrainingPointResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async recalculatePeriodByPlanItemIdAndStudentNo(
    request: RecalculatePeriodByPlanItemIdAndStudentNoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.recalculatePeriodByPlanItemIdAndStudentNo,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 清空一个教学计划项之下的所有签到记录及其通过签到获得的学时
   * @param planItemId 教学计划项id
   * @param mutate 查询 graphql 语法文档
   * @param planItemId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeSignRecordAndStudyResultByPlanItemId(
    planItemId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeSignRecordAndStudyResultByPlanItemId,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { planItemId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员签到
   * @param token 学员学习token
   * @return 学生签到结果
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async studentsSign(
    params: { token?: string; request?: SignInRequest },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.studentsSign,
    operation?: string
  ): Promise<Response<SignResultResponse>> {
    return commonRequestApi<SignResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新培训点
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingPoint(
    request: TrainingPointUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateTrainingPoint,
    operation?: string
  ): Promise<Response<TrainingPointResponse>> {
    return commonRequestApi<TrainingPointResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
