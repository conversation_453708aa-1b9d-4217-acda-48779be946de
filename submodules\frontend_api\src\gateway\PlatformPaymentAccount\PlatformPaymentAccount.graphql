schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""是否允许修改收款账号
		@param id
		@return
	"""
	allowUpdate(id:String):Boolean!
	"""是否支持真正电子票
		@param
		@return
	"""
	enableElectronInvoice:Boolean!
	"""获取收款账号
		@param id 收款账号Id
		@return
	"""
	get(id:String):PaymentAccountInfo
	page(page:Page):PaymentAccountInfoPage @page(for:"PaymentAccountInfo")
	pageByarams(page:Page,queryParams:MerchantAccountSearchCondition):PaymentAccountInfoPage @page(for:"PaymentAccountInfo")
	pageTaxPayer(page:Page,request:TaxPayerRequest):TaxPayerResponsePage @page(for:"TaxPayerResponse")
	"""验证收款账号别名
		@return
	"""
	validateName(id:String,name:String):Boolean!
}
type Mutation {
	create(request:PaymentAccountAddRequest):Boolean!
	"""删除收款账号
		@param id
		@return
	"""
	remove(id:String):Boolean!
	update(request:PaymentAccountAddRequest):Boolean!
	"""停用/启用收款账号
		@param id
		@return
	"""
	updateEnable(id:String,enable:Boolean!):Boolean!
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""纳税人识别号查询
	@author: eleven
	@date: 2020/5/19
"""
input TaxPayerRequest @type(value:"com.fjhb.fjszyws.integrative.gateway.graphql.dto.request.TaxPayerRequest") {
	"""名称"""
	name:String
	"""纳税人识别号"""
	taxCode:String
	"""发票地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bank:String
	"""账号"""
	account:String
}
"""@author: eleven
	@date: 2020/2/13
	@description:
"""
input PaymentAccountAddRequest @type(value:"com.fjhb.fjszyws.platform.gateway.web.admin.dto.request.PaymentAccountAddRequest") {
	"""收款账户ID"""
	id:String
	"""收款账号（微信为商户号，支付宝为支付宝账号，建设银行为开户号）"""
	accountNo:String
	"""账户别名"""
	accountAlias:String
	"""(只在线上收款账号有使用到) 1支付宝；2微信；3建行"""
	createType:Int!
	"""证书密匙"""
	merchantKey:String
	"""支付宝为合作者ID:微信为公众号Id"""
	appId:String
	"""文件上传成功后的文件名称"""
	privateKeyFileName:String
	"""微信证书密匙"""
	privateKeyPwd:String
	"""微信证书地址"""
	privateKeyPath:String
	"""建行接口需要的POSID(商户柜台代码)"""
	posId:String
	"""建行接口需要的BRANCHID(分行代码)"""
	branchBankId:String
	"""建行网银支付接口的公钥"""
	publicKey:String
	"""建行的操作员账号不能为空"""
	operator:String
	"""建行操作员的登陆密码"""
	password:String
	"""账户一级类型代表的是支付方式(1:线上,2:线下)
		只能是1或2
	"""
	firstType:Int!
	"""企业名称（开户户名）"""
	merchantName:String
	"""开户银行"""
	depositBank:String
	"""开户银行的行号（开户号）"""
	bankNumber:String
	"""柜台号"""
	counterNumber:String
	"""账户类别 1.对私 2.对公"""
	accountType:Int!
	"""支付宝应用私钥"""
	alipayAppPrivateKey:String
	"""支付宝公钥"""
	alipayPublicKey:String
	"""支付宝应用id"""
	alipayAppId:String
	"""纳税人识别号"""
	taxPayerId:String
}
input MerchantAccountSearchCondition @type(value:"com.fjhb6.ability.orderPay.v1.south.api.info.MerchantAccountSearchCondition") {
	projectId:String
	platformId:String
	platformVersionId:String
	subProjectId:String
	unitId:String
	organizationId:String
	accountAlias:String
	accountNo:String
	firstType:Int
	secondType:String
	tradeChannelCode:String
	createStartTime:DateTime
	createEndTime:DateTime
	certificateStartTime:DateTime
	certificateEndTime:DateTime
	status:MerchantAccountStatusForSearch
	createType:MerchantAccountCreateTypeForSearch
}
"""纳税人信息表
	@author: eleven
	@date: 2020/5/19
"""
type TaxPayerResponse @type(value:"com.fjhb.fjszyws.integrative.gateway.graphql.dto.response.TaxPayerResponse") {
	"""主键ID"""
	id:String
	"""名称"""
	name:String
	"""纳税人识别号"""
	taxCode:String
	"""发票地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bank:String
	"""账号"""
	account:String
}
"""@author: eleven
	@date: 2020/2/13
	@description:
"""
type PaymentAccountInfo @type(value:"com.fjhb.fjszyws.platform.service.paymentAccount.south.dto.PaymentAccountInfo") {
	"""主键ID"""
	id:String
	"""收款账户别名"""
	accountAlias:String
	"""收款账户的第三方接口的账号"""
	accountNo:String
	"""账户一级类型代表的是支付方式(1:线上,2:线下)
		只能是1或2
	"""
	firstType:Int
	"""交易渠道代码"""
	tradeChannelCode:String
	"""二级类型(在一级类型的基础上拓展的描述在线下支付方式的情况下可能有"对汇"等)
		如果没有填写为空
	"""
	secondType:String
	"""开户银行"""
	depositBank:String
	"""开户银行的行号"""
	bankNumber:String
	"""柜台号"""
	counterNumber:String
	"""企业名称"""
	merchantName:String
	"""企业联系电话"""
	merchantPhone:String
	"""商户第三方平台分配的签名时用到的密钥(不同接口可能没有)"""
	merchantKey:String
	"""状态0:停用,1:启用"""
	status:Int
	"""收款账号创建方式 1:支付宝2:微信,3建设银行，4微信订阅号5微信小程序"""
	createType:Int
	"""账户类别，createType==3 建设银行时有效 1.对私 2.对公"""
	accountType:Int!
	"""文件上传成功后的文件名称"""
	privateKeyFileName:String
	"""微信证书密匙"""
	privateKeyPwd:String
	"""微信证书地址"""
	privateKeyPath:String
	"""支付宝为合作者ID:微信为公众号Id"""
	appId:String
	"""支付网关名称"""
	tradeChannelName:String
	"""建行接口需要的POSID(商户柜台代码)"""
	posId:String
	"""建行接口需要的BRANCHID(分行代码)"""
	branchBankId:String
	"""建行网银支付接口的公钥"""
	publicKey:String
	"""建行的操作员账号不能为空"""
	operator:String
	"""支付宝应用私钥"""
	alipayAppPrivateKey:String
	"""支付宝公钥"""
	alipayPublicKey:String
	"""支付宝应用id"""
	alipayAppId:String
	"""纳税人识别号id"""
	taxPayerId:String
	"""纳税人识别号名字"""
	taxPayerName:String
	"""创建单位"""
	createUnitName:String
	"""是否为授权账号"""
	fromAuthorize:Boolean!
	"""授权状态"""
	authorizationState:AuthorizationStateEnum
	"""是否授权"""
	hasAuthorize:Boolean!
	"""数据token"""
	dataToken:String
	"""原收款账号id"""
	rootId:String
}
enum AuthorizationStateEnum @type(value:"com.fjhb.platformstandard.common.utils.dataauthorized.enumeration.AuthorizationStateEnum") {
	AUTHORIZATION
	CANCEL_AUTHORIZATION
}
enum MerchantAccountCreateTypeForSearch @type(value:"com.fjhb6.ability.orderPay.v1.south.api.enums.MerchantAccountCreateTypeForSearch") {
	ALL
	INTERNAL
	NORMAL
}
enum MerchantAccountStatusForSearch @type(value:"com.fjhb6.ability.orderPay.v1.south.api.enums.MerchantAccountStatusForSearch") {
	ALL
	ENABLE
	DISABLE
}

scalar List
type PaymentAccountInfoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PaymentAccountInfo]}
type TaxPayerResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TaxPayerResponse]}
