import UpdateRoleDto from '@api/service/management/authority/role/dto/UpdateRoleDto'

class UpdateRole {
  updateRoleDto: UpdateRoleDto

  async doUpdate(): Promise<boolean> {
    try {
      console.log('调用了doUpdate方法，返回值=', false)
      return false
    } catch (e) {
      console.log('报错了，所处位置/service/management/authority/role/mutation/UpdateRole.ts所处方法，doUpdate', e)
    }
  }
}

export default UpdateRole
