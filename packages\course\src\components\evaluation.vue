<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <div class="f-flex f-align-center">
        <span class="f-mr5">综合评价：</span>
        <el-rate v-model="totalAppraise" disabled show-score text-color="#ff9900" />
      </div>
      <el-table stripe :data="courseEvaluationList" v-loading="query.loading" max-height="500px" class="m-table f-mt20">
        <el-table-column label="评价人" min-width="80" fixed="left" prop="userName">
          <template slot-scope="{ row }">{{ row.studentName }}</template>
        </el-table-column>
        <!-- <el-table-column label="技术等级" min-width="100" align="center" fixed="left">
          <template slot-scope="{ row }">{{ row.studentProfessionalLevelName }}</template>
        </el-table-column> -->
        <el-table-column label="课程内容评价" min-width="180" align="center" fixed="left">
          <template slot-scope="{ row }">
            <el-rate v-model="row.courseScore" disabled show-score text-color="#ff9900" />
          </template>
        </el-table-column>
        <el-table-column label="教师授课评价" min-width="180" align="center" fixed="left">
          <template slot-scope="{ row }">
            <el-rate v-model="row.teacherScore" disabled show-score text-color="#ff9900" />
          </template>
        </el-table-column>
        <el-table-column label="评价内容" show-overflow-tooltip min-width="500" prop="contents">
          <template slot-scope="{ row }">
            {{ row.contents }}
          </template>
        </el-table-column>
        <el-table-column label="评价时间" min-width="180" align="center" prop="createTime"></el-table-column>
      </el-table>
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import CourseEvaluateListDetail from '@api/service/management/resource/course/query/vo/CourseEvaluateListDetail'
  import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
  import CourseDetail from '@api/service/management/resource/course/query/vo/CourseDetail'

  @Component
  export default class extends Vue {
    query: Query = new Query()
    page: UiPage = new UiPage()
    courseEvaluationList: Array<CourseEvaluateListDetail> = new Array<CourseEvaluateListDetail>()

    totalAppraise = 0

    courseDetail: CourseDetail

    constructor() {
      super()
      this.page = new UiPage(this.queryEvaluation, this.queryEvaluation)
    }
    // 加载课程评价
    async queryEvaluation() {
      this.query.loading = true
      const queryCourse = new QueryCourse()

      try {
        this.courseDetail = await queryCourse.queryCourseById(this.$route.params.id)

        this.totalAppraise =
          (this.courseDetail?.courseAppraiseInfo?.totalAppraise &&
            parseFloat((this.courseDetail?.courseAppraiseInfo?.totalAppraise / 100).toFixed(1))) ||
          0
      } catch (e) {
        console.log(e)
        this.$message.warning('加载课程详情失败')
      }

      try {
        this.courseEvaluationList = await queryCourse.queryCourseEvaluateById(this.page, this.$route.params.id)
      } catch (e) {
        console.log(e)
        this.$message.warning('加载评价失败')
        this.query.loading = false
      } finally {
        this.query.loading = false
      }
    }
    async created() {
      await this.queryEvaluation()
    }
  }
</script>
