import UpdateCourseCategoryVo from '@api/service/management/resource/course-category/mutation/vo/UpdateCourseCategory'
import CourseResourceGateway from '@api/ms-gateway/ms-course-resource-v1'
import { ResponseStatus } from '@hbfe/common'

class UpdateCourseCategory {
  updateCourseCategory: UpdateCourseCategoryVo

  async doCreate(): Promise<ResponseStatus> {
    const { status } = await CourseResourceGateway.updateCourseCategory(this.updateCourseCategory)
    return new ResponseStatus(status.code, status.getMessage())
  }
}

export default UpdateCourseCategory
