import MsCertificateGateway, { ScanQrCodeGenerateCertificateRequest } from '@api/ms-gateway/ms-certificate-v1'
import { StudentCourseLearningRequest } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import QueryCourseModule from '@api/service/customer/course/query/QueryCourseModule'
import { Response, UiPage } from '@hbfe/common'
import StudentCourseVo from '../../course/query/vo/StudentCourseVo'
import CertificateModelVo from './vo/certificate/CertificateModelVo'
import TrainingCertificateDetailVo from './vo/certificate/TrainingCertificateDetailVo'
import UserInfoVo from './vo/certificate/UserInfoVo'

import Certificate from '@api/platform-gateway/platform-certificate-v1'
import CertificatePice from '@api/ms-gateway/ms-certificate-v1'
import StudentSchemeLearningQueryGateway from '@api/platform-gateway/platform-student-scheme-learning-query-front-gateway'
import HistoryTrainingArchivesVo from '@api/service/customer/personal-leaning/query/vo/HistoryTrainingArchivesVo'
import CourseDetailVo from '@api/service/customer/personal-leaning/query/vo/certificate/CourseDetailVo'

/**
 * 查询培训明细
 */
class QueryTrainingDetail {
  /**
   * 子级映射父级
   */
  treeMap: Map<string, string> = new Map()
  // 顺序
  sortMap: Map<string, number>
  // 分类顺序
  orderSort: Map<string, { level: number; sort: number; parentId: string }>
  /**
   * 分支ID映射名称
   */
  branchIdMap = new Map<string, string>()

  /**
   * @description: 查询培训证明详情
   * @param {string} studentNo  学号
   * @param {number} courseLearningResourceType 1.选课规则课程学习场景 2.自主选课课程学习场景 3.兴趣课课程学习场景
   * @param schemeId 方案ID
   */
  async queryDetail(id: string, snapshotId?: string): Promise<Response<TrainingCertificateDetailVo>> {
    const response = new Response<TrainingCertificateDetailVo>()
    const res = await Certificate.getCertificateSnapshot({
      certificateId: id,
      snapshotId: snapshotId || undefined
    })
    if (res.status.code == 200) {
      const result = JSON.parse(res.data)
      console.log(result.data, '=======================')
      const detail = new TrainingCertificateDetailVo()
      detail.dataType = result.data.certificate.dataType // 数据类型
      detail.userName = result.data.certificate?.userName?.replace(/\s+/g, '') // 姓名
      detail.idCard = result.data.certificate.idCard // 身份证
      // detail.userName ??= result.data.printData.name
      if (!detail.userName) detail.userName = result.data.printData?.userName
      if (!detail.idCard) detail.idCard = result.data.printData?.certificateNo
      // detail.jobCategoryName = result.data.printData.userInfo.jobCategoryName // 证书技术工种
      // detail.professionalLevelName = result.data.printData.userInfo.professionalLevelName // 证书技术等级
      detail.trainingYear = result.data.printData.schemeYear // 年度
      detail.grade = result.data.printData.totalGrade // 总学时
      // detail.issueTime = result.data.certificate.issueTime // 合格时间
      detail.subjectType = result.data.certificate.subjectType // 科目类型
      detail.trainingCategory = result.data.certificate.trainingCategory // * 培训类别
      detail.trainingProfession = result.data.certificate.trainingProfession // * 培训专业
      detail.trainingType = result.data.certificate.trainingType // * 培训专业
      detail.jobType = result.data.certificate.jobType // * 培训专业
      detail.declarationLevel = result.data.certificate.declarationLevel // * 培训专业
      detail.profession = result.data.certificate.profession // * 培训专业
      detail.unitName = result.data.certificate?.unitName?.replace(/\s+/g, '')
      detail.type = result.data.certificate.type //证明类型 1人设 2建设
      detail.certificateNo = result.data.certificate.certificateNo // 证书编号
      detail.phone = result.data.certificate.phone // 联系方式
      // 解析课程
      const studentCourseLearningInfoList = result.data.printData.studentCourseLearningInfoList
      // * 排序
      studentCourseLearningInfoList.sort((a: any, b: any) => {
        return a.learningResultTimeStamp - b.learningResultTimeStamp
      })
      // console.log('studentCourseLearningInfoList', studentCourseLearningInfoList)
      // * 新版
      studentCourseLearningInfoList.map((res: any) => {
        const temp = new CourseDetailVo()
        temp.name = res.courseName
        temp.period = res.period
        temp.learningResultTimeStr = res.learningResultTimeStr
        temp.courseType = res.courseType
        detail.trainingCourseList.push(temp)
      })
      // * 旧版
      // 分支映射课程
      // const map = new Map<
      //   string,
      //   { id: string; name: string; grade: number; type: number; sort?: number; parentSort?: number }[]
      // >()
      // //   分支映射名称
      // const nameMap = new Map<string, string>()
      // // 排序Map
      // this.sortMap = new Map<string, number>()
      // this.orderSort = new Map<string, { level: number; sort: number; parentId: string }>()
      // this.getSortMap(result.data.printData.courseOutlineTree)
      // studentCourseLearningInfoList.forEach((item: any) => {
      //   let result
      //   let id
      //   let name
      //   switch (item.courseOutlineInfoLink.length) {
      //     case 1:
      //       // 课程隶属于一级分类
      //       result = map.get(item.courseOutlineInfoLink[0].id as string)
      //         ? map.get(item.courseOutlineInfoLink[0].id as string)
      //         : []
      //       nameMap.set(item.courseOutlineInfoLink[0].id, item.courseOutlineInfoLink[0].name)
      //       result.push({
      //         id: item.courseId,
      //         name: item.courseName,
      //         grade: item.period,
      //         type: item.courseType,
      //         sort: this.sortMap.get(item.courseId)
      //       })
      //       map.set(item.courseOutlineInfoLink[0].id, result)
      //       nameMap.set(item.courseOutlineInfoLink[0].id, item.courseOutlineInfoLink[0].name)
      //       break
      //     case 2:
      //       // 课程隶属于二级分类
      //       id = item.courseOutlineInfoLink.sort((a: any, b: any) => b.sort - a.sort)[0].id
      //       name = item.courseOutlineInfoLink.sort((a: any, b: any) => b.sort - a.sort)[0].name
      //       result = map.get(id as string) ? map.get(id as string) : []
      //       result.push({
      //         id: item.courseId,
      //         name: item.courseName,
      //         grade: item.period,
      //         type: item.courseType,
      //         sort: this.sortMap.get(item.courseId)
      //       })
      //       map.set(id, result)
      //       nameMap.set(id, name)
      //       break
      //     case 3:
      //       // 课程隶属于三级分类
      //       id = item.courseOutlineInfoLink.sort((a: any, b: any) => b.sort - a.sort)[1].id
      //       name = item.courseOutlineInfoLink.sort((a: any, b: any) => b.sort - a.sort)[1].name
      //       result = map.get(id as string) ? map.get(id as string) : []
      //       result.push({
      //         id: item.courseId,
      //         name: item.courseName,
      //         grade: item.period,
      //         type: item.courseType,
      //         sort: this.sortMap.get(item.courseId),
      //         parentSort: this.sortMap.get(item.outlineId)
      //       })
      //       map.set(id, result)
      //       nameMap.set(id, name)
      //       break
      //   }
      // })
      // const keys = [...new Set(map.keys())]
      // keys.forEach(element => {
      //   const result = map.get(element)
      //   detail.courseList.push({
      //     id: element,
      //     name: nameMap.get(element),
      //     data: result
      //   })
      // })
      // const compulsoryId = result.data.printData.courseOutlineTree?.find((id: any) => id.sort == 0)?.id
      // const electiveCourseId = result.data.printData.courseOutlineTree?.find((id: any) => id.sort == 1)?.id
      // const newDetailCourseList = [] as any
      // const newDetailCourseLists = cloneDeep(detail.courseList) as {
      //   id: string
      //   name: string
      //   data: { id: string; name: string; grade: number; type: number; sort?: number; parentSort?: number }[]
      // }[]
      // const compulsoryList = newDetailCourseLists.filter(
      //   ite => ite.id === compulsoryId || this.orderSort.get(ite.id)?.parentId === compulsoryId
      // )
      // if (result.data.printData.courseOutlineTree?.find((id: any) => id.sort == 0)?.subOutline?.length) {
      //   compulsoryList.sort((a, b) => this.orderSort.get(a.id).sort - this.orderSort.get(b.id).sort)
      // }
      // const electiveCourseList = newDetailCourseLists.filter(
      //   ite => ite.id === electiveCourseId || this.orderSort.get(ite.id)?.parentId === electiveCourseId
      // )
      // if (result.data.printData.courseOutlineTree?.find((id: any) => id.sort == 1)?.subOutline?.length) {
      //   electiveCourseList.sort((a, b) => this.orderSort.get(a.id).sort - this.orderSort.get(b.id).sort)
      // }
      // if (compulsoryId) newDetailCourseList.push(...compulsoryList)
      // if (electiveCourseId) newDetailCourseList.push(...electiveCourseList)
      // detail.courseList = newDetailCourseList
      // detail.courseList.forEach(course => {
      //   const parentSortList = [...new Set(course.data)].map(item => item.parentSort).sort((a, b) => a - b)
      //   const arentSortMap = new Map<
      //     number,
      //     { id: string; name: string; grade: number; type: number; sort?: number; parentSort?: number }[]
      //   >()
      //   parentSortList.map(ite => {
      //     arentSortMap.set(
      //       ite,
      //       course.data.filter(it => it.parentSort === ite).sort((a, b) => a.sort - b.sort)
      //     )
      //   })
      //   const newData = [] as any
      //   parentSortList.map(value => {
      //     newData.push(...arentSortMap.get(value))
      //   })
      //   course.data = [...new Set(newData)] as {
      //     id: string
      //     name: string
      //     grade: number
      //     type: number
      //     sort?: number
      //     parentSort?: number
      //   }[]
      // })
      response.data = detail
    }
    return response
  }

  getSortMap(courseOutlineTree: any[], level = 0) {
    ;(courseOutlineTree as Array<any>)?.forEach((item: any) => {
      this.orderSort.set(item.id, { level: level, sort: item.sort, parentId: item.parentId })
      if (item.subOutline.length) {
        item.subOutline.forEach((val: any) => {
          this.sortMap.set(val.id, val.sort)
        })
        this.getSortMap(item.subOutline, 1)
      }
      if (item.courseOnOutlineList.length) {
        item.courseOnOutlineList.forEach((val: any) => {
          this.sortMap.set(val.id, val.sort)
        })
      }
    })
    return
  }

  /**
   * 查询证书
   */
  async queryCertificateDetail(id: string) {
    const response = new Response<TrainingCertificateDetailVo>()
    const res = await Certificate.getCertificateSnapshot({
      certificateId: id
    })
    if (res.status.code == 200) {
      const result = JSON.parse(res.data)
      const detail = new TrainingCertificateDetailVo()
      detail.userName = result.data.printData.userInfo.name // 姓名
      detail.idCard = result.data.printData.userInfo.idCard // 身份证
      detail.jobCategoryName = result.data.printData.userInfo.jobCategoryName // 证书技术工种
      detail.professionalLevelName = result.data.printData.userInfo.professionalLevelName // 证书技术等级
      detail.trainingYear = result.data.printData.trainingAchievement.trainingYear // 年度
      detail.grade = result.data.certificate.grade // 总学时
      detail.issueTime = result.data.certificate.issueTime // 合格时间
      detail.h5OutlineDataList = new Array<{
        name: string
        h5CourseDataList: { name: string; period: number; sort: number }[]
        sort: number
      }>()
      result.data?.printData?.h5OutlineDataList?.map(
        (ite: { name: string; h5CourseDataList: { name: string; period: number; sort: number }[]; sort: number }) => {
          detail.h5OutlineDataList.push(ite)
        }
      )
      response.data = detail
    }
    return response
  }

  /**
   * 漳州专技证明接口
   * @param id
   * @returns {Promise<Response<TrainingCertificateDetailVo>>}
   */
  async queryZhangZhouDetail(id: string) {
    const response = new Response<TrainingCertificateDetailVo>()
    const res = await Certificate.getCertificateSnapshot({
      certificateId: id
    })
    const result = JSON.parse(res.data)
    if (res.status.code == 200) {
      const detail = new TrainingCertificateDetailVo()
      detail.userName = result.data.certificate?.userName // 姓名
      detail.idCard = result.data.certificate?.idCard // 身份证
      detail.unitName = result.data.certificate?.unitName // 单位
      detail.trainingInstitutions = result.data.certificate?.trainingInstitution // 施训机构
      detail.grade = result.data.certificate?.totalGrade // 总学时
      detail.learningType = result.data.certificate?.learningForm // 学习形式
      detail.examinationResult = result.data.certificate?.examinationResult // 考核结果
      response.data = detail
    }
    return response
  }

  async otherProveDetail(id: string, snapshotId?: string): Promise<Response<TrainingCertificateDetailVo>> {
    const response = new Response<TrainingCertificateDetailVo>()
    // response.snapshotId = snapshotId ? snapshotId : undefined
    const res = await Certificate.getCertificateSnapshot({
      certificateId: id,
      snapshotId: snapshotId || undefined
    })
    if (res.status.code == 200) {
      const result = JSON.parse(res.data)
      const detail = new TrainingCertificateDetailVo()
      detail.userName = result?.data?.certificate?.userName?.replace(/\s+/g, '')
      detail.idCard = result?.data?.certificate?.idCard
      detail.subjectType = result?.data?.certificate?.subjectType
      detail.trainingCategory = result?.data?.certificate?.trainingCategory
      detail.trainingProfession = result?.data?.certificate?.trainingProfession
      detail.trainingType = result?.data?.certificate?.trainingType
      detail.jobType = result?.data?.certificate?.jobType
      detail.declarationLevel = result?.data?.certificate?.declarationLevel
      detail.profession = result?.data?.certificate?.profession
      detail.unitName = result?.data?.certificate?.unitName?.replace(/\s+/g, '')
      detail.type = result?.data?.certificate?.type
      detail.trainingYear = result.data.printData.schemeYear // 年度
      detail.grade = result?.data?.certificate?.totalGrade // 培训学时
      detail.schemeName = result?.data?.certificate?.schemeName // 培训班名称
      detail.certificateNo = result.data.certificate.certificateNo // 证书编号
      detail.phone = result.data.certificate.phone // 联系方式
      detail.localArea = result?.data?.certificate?.localArea // 个人地区
      detail.name = result.data.certificate.name // 姓名
      detail.issueTime = result.data.certificate.issueTime // 发证时间
      detail.electronicSeal = result.data.electronicSeal.imageUrl //电子章
      detail.photo = result.data.certificate.photo
      detail.sign = result?.data?.electronicSeal?.sign //培训单位
      // 解析课程
      const studentCourseLearningInfoList = result?.data?.certificate?.studentCourseLearningInfoList
      // * 排序
      studentCourseLearningInfoList?.sort((a: any, b: any) => {
        return a.learningResultTimeStamp - b.learningResultTimeStamp
      })
      // * 新版
      studentCourseLearningInfoList?.map((res: any) => {
        const temp = new CourseDetailVo()
        temp.name = res.courseName
        temp.period = res.period
        temp.learningResultTimeStr = res.learningResultTimeStr
        temp.courseType = res.courseType
        detail.trainingCourseList.push(temp)
      })
      response.data = detail
    }
    return response
  }
  async newPrintCert(
    id: string,
    snapshotId?: string,
    sort = 'learningResultTimeStr'
  ): Promise<Response<TrainingCertificateDetailVo>> {
    const response = new Response<TrainingCertificateDetailVo>()
    // response.snapshotId = snapshotId ? snapshotId : undefined
    const res = await Certificate.getCertificateSnapshot({
      certificateId: id,
      snapshotId: snapshotId || undefined
    })
    if (res.status.code == 200) {
      const result = JSON.parse(res.data)
      const detail = new TrainingCertificateDetailVo()
      detail.userName = result?.data?.certificate?.userInfo?.name?.replace(/\s+/g, '')
      detail.idCard = result?.data?.certificate?.userInfo?.idCard
      detail.subjectType = result?.data?.certificate?.schemeInfo?.subjectType
      detail.trainingCategory = result?.data?.certificate?.schemeInfo?.trainingCategory
      detail.trainingProfession = result?.data?.certificate?.schemeInfo?.trainingProfession
      // detail.trainingType = result?.data?.certificate?.trainingType
      // detail.jobType = result?.data?.certificate?.jobType
      // detail.declarationLevel = result?.data?.certificate?.declarationLevel
      // detail.profession = result?.data?.certificate?.profession
      detail.unitName = result?.data?.certificate?.userInfo?.unitName?.replace(/\s+/g, '')
      // detail.type = result?.data?.certificate?.type
      detail.trainingYear = result?.data?.certificate?.schemeInfo?.schemeYear // 年度
      detail.grade = result?.data?.certificate?.studentInfo?.totalPeriod // 培训学时
      detail.schemeName = result?.data?.certificate?.schemeInfo?.schemeName // 培训班名称
      detail.certificateNo = result?.data?.certificate?.certificateNo // 证书编号
      detail.unitArea = result?.data?.certificate?.userInfo?.unitArea //单位地区
      detail.localArea = result?.data?.certificate?.userInfo?.localArea // 个人地区
      // detail.phone = result.data.certificate.phone // 联系方式
      // detail.name = result.data.certificate.name // 姓名
      detail.issueTime = result?.data?.certificate?.certificateInfo?.issueCertificateTime // 发证时间
      // detail.electronicSeal = result.data.electronicSeal.imageUrl //电子章
      // detail.photo = result.data.certificate.photo
      detail.sign = result?.data?.electronicSeal?.sign //培训单位
      // 解析课程
      if (result?.data?.certificate?.courseInfo?.studentCourseLearningInfoList) {
        //筛选出必修课程并排序
        const requireCourse = result?.data?.certificate?.courseInfo?.studentCourseLearningInfoList
          .filter((item: any) => item.courseType == 1)
          .sort((a: any, b: any) => {
            return a[sort] - b[sort]
          })
        //筛选出选修课程并排序
        const electiveCourses = result?.data?.certificate?.courseInfo?.studentCourseLearningInfoList
          .filter((item: any) => item.courseType == 2)
          .sort((a: any, b: any) => {
            return a[sort] - b[sort]
          })
        // * 排序
        const studentCourseLearningInfoList = requireCourse.concat(electiveCourses)
        // * 新版
        studentCourseLearningInfoList?.map((res: any) => {
          const temp = new CourseDetailVo()
          temp.name = res.courseName
          temp.period = res.period
          temp.learningResultTimeStr = res.learningResultTimeStr
          temp.courseType = res.courseType
          temp.selectCourseTime = res?.selectCourseTimeStr
          temp.sort = res?.sort
          detail.trainingCourseList.push(temp)
        })
      }

      response.data = detail
    }
    return response
  }
  async otherProveDetailAsce(id: string, snapshotId?: string): Promise<Response<TrainingCertificateDetailVo>> {
    const response = new Response<TrainingCertificateDetailVo>()
    // response.snapshotId = snapshotId ? snapshotId : undefined
    const res = await Certificate.getCertificateSnapshot({
      certificateId: id,
      snapshotId: snapshotId || undefined
    })
    if (res.status.code == 200) {
      const result = JSON.parse(res.data)
      const detail = new TrainingCertificateDetailVo()
      detail.userName = result?.data?.certificate?.userName?.replace(/\s+/g, '')
      detail.idCard = result?.data?.certificate?.idCard
      detail.subjectType = result?.data?.certificate?.subjectType
      detail.trainingCategory = result?.data?.certificate?.trainingCategory
      detail.trainingProfession = result?.data?.certificate?.trainingProfession
      detail.trainingType = result?.data?.certificate?.trainingType
      detail.jobType = result?.data?.certificate?.jobType
      detail.declarationLevel = result?.data?.certificate?.declarationLevel
      detail.profession = result?.data?.certificate?.profession
      detail.unitName = result?.data?.certificate?.unitName?.replace(/\s+/g, '')
      detail.type = result?.data?.certificate?.type
      detail.trainingYear = result.data.printData.schemeYear // 年度
      detail.grade = result?.data?.certificate?.totalGrade // 培训学时
      detail.schemeName = result?.data?.certificate?.schemeName // 培训班名称
      detail.certificateNo = result.data.certificate.certificateNo // 证书编号
      detail.phone = result.data.certificate.phone // 联系方式
      detail.name = result.data.certificate?.name?.replace(/\s+/g, '') // 姓名
      detail.issueTime = result.data.certificate.issueTime // 发证时间
      detail.electronicSeal = result.data.electronicSeal.imageUrl //电子章
      detail.photo = result.data.certificate.photo
      // 解析课程
      const studentCourseLearningInfoList = result?.data?.certificate?.studentCourseLearningInfoList
      // * 排序
      studentCourseLearningInfoList?.sort((a: any, b: any) => {
        return b.learningResultTimeStamp - a.learningResultTimeStamp
      })
      // * 新版
      studentCourseLearningInfoList?.map((res: any) => {
        const temp = new CourseDetailVo()
        temp.name = res.courseName
        temp.period = res.period
        temp.learningResultTimeStr = res.learningResultTimeStr
        temp.courseType = res.courseType
        detail.trainingCourseList.push(temp)
      })
      response.data = detail
    }
    return response
  }

  /**
   * @description: 查询旧平台证书(历史培训证明)详情
   * @date: 2024/02/02 14:58:03
   * @param id 历史培训证明id
   */
  async queryHistoryCertificateDetail(id: string) {
    const { data, status } = await StudentSchemeLearningQueryGateway.getHistoryStudentTrainingInfoResponseByIdInMyself(
      id
    )
    const response = new Response<HistoryTrainingArchivesVo>()
    if (data && status?.isSuccess()) {
      response.data = HistoryTrainingArchivesVo.fromDetail(data)
    }
    return response
  }
  /**
   * @description: 查询甘肃专技模板证书(历史培训证明)详情
   * @param snapshotId 快照id
   * @returns
   */
  async queryNewHistoryCertificateDetail(snapshotId?: string): Promise<Response<TrainingCertificateDetailVo>> {
    const response = new Response<TrainingCertificateDetailVo>()
    const res = await Certificate.getCertificateSnapshot({
      snapshotId: snapshotId || undefined
    })
    if (res.status.code == 200) {
      const result = JSON.parse(res.data)
      const detail = new TrainingCertificateDetailVo()
      detail.userName = result?.data?.certificate?.userName //姓名
      detail.idCard = result?.data?.certificate?.idCard //身份证号
      detail.trainingYear = result?.data?.certificate?.schemeYear //培训年度
      detail.schemeName = result?.data?.certificate?.schemeName //培训方案名称
      detail.unitName = result?.data?.certificate?.unitName //工作单位
      detail.sign = result?.data?.electronicSeal?.sign //培训单位
      response.data = detail
    }
    return response
  }
  //江西工信（很急先临时这样搞）
  async JxgxPrintCert(id: string, snapshotId?: string): Promise<Response<TrainingCertificateDetailVo>> {
    const response = new Response<TrainingCertificateDetailVo>()
    // response.snapshotId = snapshotId ? snapshotId : undefined
    const res = await Certificate.getCertificateSnapshot({
      certificateId: id,
      snapshotId: snapshotId || undefined
    })
    if (res.status.code == 200) {
      const result = JSON.parse(res.data)
      const detail = new TrainingCertificateDetailVo()
      detail.userName = result?.data?.certificate?.expandInfoMap?.userName?.replace(/\s+/g, '')
      detail.idCard = result?.data?.certificate?.expandInfoMap?.idCard
      detail.subjectType = result?.data?.certificate?.schemeInfo?.subjectType
      detail.trainingCategory = result?.data?.certificate?.schemeInfo?.trainingCategory
      detail.trainingProfession = result?.data?.certificate?.schemeInfo?.trainingProfession
      // detail.trainingType = result?.data?.certificate?.trainingType
      // detail.jobType = result?.data?.certificate?.jobType
      // detail.declarationLevel = result?.data?.certificate?.declarationLevel
      // detail.profession = result?.data?.certificate?.profession
      detail.unitName = result?.data?.certificate?.userInfo?.unitName?.replace(/\s+/g, '')
      // detail.type = result?.data?.certificate?.type
      detail.trainingYear = result?.data?.certificate?.schemeInfo?.schemeYear // 年度
      detail.grade = result?.data?.certificate?.studentInfo?.totalPeriod // 培训学时
      detail.schemeName = result?.data?.certificate?.schemeInfo?.schemeName // 培训班名称
      detail.certificateNo = result?.data?.certificate?.certificateNo // 证书编号
      detail.unitArea = result?.data?.certificate?.userInfo?.unitArea //单位地区
      detail.localArea = result?.data?.certificate?.userInfo?.localArea // 个人地区
      // detail.phone = result.data.certificate.phone // 联系方式
      // detail.name = result.data.certificate.name // 姓名
      detail.issueTime = result?.data?.certificate?.certificateInfo?.issueCertificateTime // 发证时间
      // detail.electronicSeal = result.data.electronicSeal.imageUrl //电子章
      // detail.photo = result.data.certificate.photo
      // detail.sign = result?.data?.electronicSeal?.sign //培训单位
      // 解析课程
      //筛选出必修课程并排序
      const requireCourse = result?.data?.certificate?.courseInfo?.studentCourseLearningInfoList
        .filter((item: any) => item.courseType == 1)
        .sort((a: any, b: any) => {
          return a.learningResultTimeStr - b.learningResultTimeStr
        })
      //筛选出选修课程并排序
      const electiveCourses = result?.data?.certificate?.courseInfo?.studentCourseLearningInfoList
        .filter((item: any) => item.courseType == 2)
        .sort((a: any, b: any) => {
          return a.learningResultTimeStr - b.learningResultTimeStr
        })
      // * 排序
      const studentCourseLearningInfoList = requireCourse.concat(electiveCourses)
      // * 新版
      studentCourseLearningInfoList?.map((res: any) => {
        const temp = new CourseDetailVo()
        temp.name = res.courseName
        temp.period = res.period
        temp.learningResultTimeStr = res.learningResultTimeStr
        temp.courseType = res.courseType
        detail.trainingCourseList.push(temp)
      })
      response.data = detail
    }
    return response
  }

  /**
   * 扫描证书二维码生成证明图片
   */
  async PrintCertPicure(snapshotId: string, fileType?: number): Promise<Response<string>> {
    const request = new ScanQrCodeGenerateCertificateRequest()
    request.snapshotId = snapshotId
    request.fileType = fileType || undefined
    const res = await MsCertificateGateway.scanQrCodeGenerateCertificate(request)
    const response = new Response<string>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    response.data = res.data
    response.status = res.status
    return response
  }

  /**
   * 查询树节点最后一级数据
   */
  private findLastTreeNode(arr: any[], result: any[] = []) {
    //
    for (let i = 0; i < arr.length; i++) {
      const element = arr[i]
      this.branchIdMap.set(element.id, element.name)
      if (element.childOutlines.length === 0) {
        //
        result.push(element)
      } else {
        element.childOutlines.forEach((item: any) => {
          this.treeMap.set(item.id, element.id)
        })
        this.findLastTreeNode(element.childOutlines, result)
      }
    }
  }

  /**
   * @description: 查询培训课程信息
   * @param {string} sNo
   * @param {number} courseLearningResourceType
   */
  private async queryTrainingCourse(sNo: string, schemeType: string): Promise<Array<StudentCourseVo>> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 200
    const param = new StudentCourseLearningRequest()
    param.studentNo = sNo
    const res = await QueryCourseModule.queryCourse.queryStudentCoursePage(page, sNo, schemeType)
    if (res.length) {
      return res
    }
    return new Array<StudentCourseVo>()
  }

  /**
   * @description: 查询人员信息（快照）
   * @param {string} studentNo
   */
  private async queryUserDetail(studentNo: string): Promise<Response<UserInfoVo>> {
    const res = await MsCertificateGateway.getCertificateSnapShot(studentNo)
    const response = new Response<UserInfoVo>()
    if (!res.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    const data = JSON.parse(res.data)
    const detail = data?.data?.certificate as CertificateModelVo
    response.data = new UserInfoVo()
    response.data.from(detail)
    response.data.userId = data.userDetail.userId
    response.status = res.status
    return response
  }
}

export default QueryTrainingDetail
