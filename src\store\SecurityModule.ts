import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store/index'
import RoleQueryFactory from '@api/service/management/authority/role/RoleQueryFactory'
import UnitTypeEnum from '@/router/models/UnitTypeEnum'
import RootModule from '@/store/RootModule'
import { unitTypeMap } from '@/router/RouteAdapter'
import runtimeContext from '@/RuntimeContext'
import { RoleDetailVo } from '@api/service/management/authority/role/query/vo/RoleDetailVo'

@Module({ namespaced: true, store, dynamic: true, name: 'Security' })
class SecurityModule extends VuexModule {
  /**
   * 角色类型
   */
  roleType = new Array<number>()
  /**
   * 目标角色
   */
  targetRole = ''
  /**
   * 是否是服务提供商
   */
  isServiceProvider = false
  /**
   * 服务商类型
   */
  serviceType: UnitTypeEnum = null

  /**
   * 初始化角色信息
   */
  @Action
  async roleInit() {
    const roleQuery = new RoleQueryFactory().getQueryCurrentUserRoleList()
    await roleQuery.getCurrentUserRoleList()
    this.SET_ROLE_INFO(roleQuery.roleList)
  }

  /**
   * 设置角色信息
   * @param roleList
   * @constructor
   */
  @Mutation
  SET_ROLE_INFO(roleList: Array<RoleDetailVo>) {
    roleList.forEach(role => {
      this.roleType.push(role.roleApplicationMemberType)
    })
    if (this.roleType.includes(6)) {
      this.isServiceProvider = true
      this.targetRole = 'WXGLY'
    } else {
      this.targetRole = 'super'
    }
    RootModule.SET_UNIT_ROLE(this.targetRole)
    this.serviceType = unitTypeMap.get(runtimeContext.businessContext.serviceProvider.serviceType)
  }
}

export default getModule(SecurityModule)
