<template>
  <div class="f-p15" v-if="$hasPermission('query')" desc="查询" actions="created">
    <el-card shadow="never" class="m-card f-mb15">
      <div class="f-mt10">
        <span class="f-mr10">请选择精品课程包展示方式：</span>
        <template v-if="$hasPermission('change')" desc="切换展示方式" actions="hasCategoryChange">
          <el-radio-group :value="excellentCourseConfig.hasCategory" @change="hasCategoryChange">
            <el-radio :label="false">
              无分类
              <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-c9"></i>
                <div slot="content">
                  支持不按照分类添加课程，最多8门。若无精品课程配置，门户将默认展示最新发布的8门课程。
                </div>
              </el-tooltip>
            </el-radio>
            <el-radio :label="true">
              有分类
              <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-c9"></i>
                <div slot="content">
                  支持按不同的课程分类添加课程，每一个分类最多添加8门。若无精品课程配置，门户将默认展示最新发布的8门课程。
                </div>
              </el-tooltip>
            </el-radio>
          </el-radio-group>
        </template>
      </div>
      <el-divider class="m-divider"></el-divider>
      <el-row type="flex" :gutter="20">
        <el-col :sm="7" :lg="6" :xl="5" v-if="excellentCourseConfig.hasCategory">
          <div class="m-course-classify">
            <div class="m-tit is-mini">
              <span class="tit-txt">精品课程分类</span>
            </div>
            <div class="f-pl15 f-mt20">
              <el-button type="primary" icon="el-icon-plus" @click="openCourseCategoryDialog">添加分类</el-button>
              <div class="f-mt20" ref="excellentCategoryList" id="excellentCategoryList">
                <template v-if="excellentCourseConfig.categories.length">
                  <div
                    class="item"
                    :class="{ true: 'category-selected' }[currentSelectCategory.id === category.id]"
                    style="cursor: pointer"
                    v-for="category in excellentCourseConfig.categories"
                    :key="category.id"
                    @click="currentSelectCategory = category"
                  >
                    {{ category.name }}（{{ category.courses.length }}）
                    <template v-if="$hasPermission('remove')" desc="删除分类" actions="doRemove">
                      <i class="icon el-icon-delete" @click="doRemove(category)"></i>
                    </template>
                  </div>
                </template>
                <el-empty v-else></el-empty>
              </div>
            </div>
          </div>
        </el-col>
        <el-col
          :sm="excellentCourseConfig.hasCategory ? 17 : 24"
          :lg="excellentCourseConfig.hasCategory ? 18 : 24"
          :xl="excellentCourseConfig.hasCategory ? 19 : 24"
        >
          <div class="m-tit is-mini" v-if="excellentCourseConfig.hasCategory">
            <span class="tit-txt">精品课程列表</span>
          </div>
          <div class="f-pl15 f-mtb20">
            <el-button
              type="primary"
              :disabled="isAddCourseDisable"
              icon="el-icon-plus"
              class="f-mb20"
              @click="showSelectCourse = true"
            >
              添加精品课程
            </el-button>
            <!--表格-->
            <el-table stripe v-if="reloadTable" :data="courseList" max-height="500px" class="m-table" ref="tableList">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="排序" min-width="70" align="center">
                <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
              </el-table-column>
              <el-table-column label="课程名称" min-width="300" prop="name" show-overflow-tooltip></el-table-column>
              <el-table-column label="课程分类" min-width="180" prop="categoryName"></el-table-column>
              <el-table-column label="操作时间" min-width="180" prop="createTime"></el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template slot-scope="{ row }">
                  <template v-if="$hasPermission('remove')" desc="取消展示" actions="cancelShow">
                    <el-button type="text" size="mini" @click="cancelShow(row)">取消展示</el-button>
                  </template>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <el-drawer
      title="选择课程"
      :visible.sync="showSelectCourse"
      size="800px"
      custom-class="m-drawer"
      :append-to-body="true"
    >
      <!-- <biz-course-select
        :selected="selectedCourseIdList"
        @cancel="showSelectCourse = false"
        @confirm="doAddCourse"
      ></biz-course-select> -->
      <template
        v-if="$hasPermission('courseSelectComponent')"
        desc="课程选择组件"
        actions="@CourseSelect,@hbfe/jxjy-admin-course/src/detail.vue"
      >
        <course-select
          :selected="selectedCourseIdList"
          :categoryId="currentSelectCategory.id"
          :excellentCourseConfig="excellentCourseConfig"
          @cancel="showSelectCourse = false"
          @confirm="doAddCourse"
          @remove="doRemoveCourse"
          @closeDrawer="closeCourseSelect"
          v-if="showSelectCourse"
        ></course-select>
      </template>
    </el-drawer>

    <el-dialog title="新增精品课程分类" :visible.sync="showSelectCourseCategory" width="30%">
      <biz-course-category-tree
        @confirm="chooseCategory"
        @cancel="showSelectCourseCategory = false"
      ></biz-course-category-tree>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
  .category-selected {
    &.item {
      color: white;
      background: #1f86f0;
    }
  }

  .icon-drag {
    cursor: move;
  }

  .category-item {
    margin-bottom: 0 !important;
    padding: 5px 10px;
  }
</style>

<script lang="ts">
  import SortAble from 'sortablejs'
  import { ElTable } from 'element-ui/types/table'
  import { Component, Provide, Vue, Watch } from 'vue-property-decorator'
  import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'
  import ExcellentCourseConfig from '@api/service/management/online-school-config/excellent-course/query/ExcellentCourseConfig'
  import ExcellentCourseCategoryConfig from '@api/service/management/online-school-config/excellent-course/query/vo/ExcellentCourseCategoryConfig'
  import ExcellentCourseConfigDetail from '@api/service/management/online-school-config/excellent-course/query/vo/ExcellentCourseConfigDetail'
  import CourseCategoryListDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryListDetail'
  import CourseSelect from '@hbfe/jxjy-admin-platform/src/function/components/course-select.vue'
  @Component({
    components: {
      CourseSelect
    }
  })
  export default class extends Vue {
    hasCategory = false

    showSelectCourse = false
    showSelectCourseCategory = false
    excellentCourseConfig: ExcellentCourseConfig = new ExcellentCourseConfig()
    reloadTable = true

    currentCourseList = new Array<ExcellentCourseConfigDetail>()

    hasCategoryChange() {
      this.$confirm('修改精品课程展示方式将清空原来的精品课程设置，是否确定修改？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.excellentCourseConfig.categories = new Array<ExcellentCourseCategoryConfig>()
        this.excellentCourseConfig.courses = new Array<ExcellentCourseConfigDetail>()
        this.currentSelectCategory.id = ''
        this.currentCourseList = new Array<ExcellentCourseConfigDetail>()
        this.currentSelectCategory.courses = new Array<ExcellentCourseConfigDetail>()

        this.excellentCourseConfig.hasCategory = !this.excellentCourseConfig.hasCategory
        await this.excellentCourseConfig.doSaveConfig() //如果只是切换分类不做课程配置，也生效操作
      })
    }

    @Provide('validateChooseCategory')
    validateChooseCategory(category: CourseCategoryListDetail) {
      if (this.excellentCourseConfig.includeCategory(category.id)) {
        this.$message.warning('选中分类已在精品课程分类中')
        return false
      }
      return true
    }

    @Provide('validateChooseCourse')
    validateChooseCourse(chooseCourses: Array<CourseListDetail>, removeCourse: Array<CourseListDetail>) {
      if (this.excellentCourseConfig.hasCategory) {
        if (this.currentSelectCategory.courses.length + chooseCourses.length - removeCourse.length > 8) {
          this.$message.warning('选中的精品课程已超过 8 门')
          return false
        }
      } else {
        if (this.excellentCourseConfig.courses.length + chooseCourses.length - removeCourse.length > 8) {
          this.$message.warning('选中的精品课程已超过 8 门')
          return false
        }
      }
      return true
    }

    get isAddCourseDisable() {
      const courses = this.currentSelectCategory.courses
      return this.excellentCourseConfig.hasCategory
        ? (courses && courses.length >= 8) || !this.excellentCourseConfig.categories.length
        : courses.length >= 8
    }

    openCourseCategoryDialog() {
      this.showSelectCourseCategory = !this.showSelectCourseCategory
    }

    currentSelectCategory: ExcellentCourseCategoryConfig = new ExcellentCourseCategoryConfig()

    async doAddCourse(courseList: Array<CourseListDetail>) {
      const addItems = courseList.map(course => {
        const configCourse = new ExcellentCourseConfigDetail()
        configCourse.id = course.id
        configCourse.name = course.name
        configCourse.categoryName = course.categories.map(category => category.name).join('、')
        return configCourse
      })
      if (this.excellentCourseConfig.hasCategory) {
        this.currentSelectCategory.addCourse(addItems)
      } else {
        this.excellentCourseConfig.addCourse(addItems)
      }
      const res = await this.excellentCourseConfig.doSaveConfig()
      if (res?.isSuccess()) {
        await this.excellentCourseConfig.queryConfig()
        if (this.excellentCourseConfig.hasCategory) {
          this.currentSelectCategory = this.excellentCourseConfig.categories.find(
            item => item.id == this.currentSelectCategory.id
          )
        }
        this.currentCourseList = this.excellentCourseConfig.courses
      }
    }

    // 删除列表
    async doRemoveCourse(courseList: Array<CourseListDetail>) {
      // todo
      const removeItems = courseList.map(course => {
        const configCourse = new ExcellentCourseConfigDetail()
        configCourse.id = course.id
        configCourse.name = course.name
        configCourse.categoryName = course.categories.map(category => category.name).join('、')
        return configCourse
      })
      // 有分类删除
      if (this.excellentCourseConfig.hasCategory) {
        this.currentSelectCategory.removeCourse(removeItems)
      } else {
        // 无分类删除
        removeItems.filter((item: ExcellentCourseConfigDetail) => {
          this.excellentCourseConfig.removeCourse(null, item)
        })
      }
      await this.excellentCourseConfig.doSaveConfig()
      this.currentCourseList = this.excellentCourseConfig.courses
    }

    doRemove(category: ExcellentCourseCategoryConfig) {
      this.$confirm('删除分类将同步清空该分类下的精品课程。确认要删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.excellentCourseConfig.doRemove(category)
        if (this.currentSelectCategory.id == category.id) {
          this.currentSelectCategory = this.excellentCourseConfig.categories[0] || new ExcellentCourseCategoryConfig()
        }
      })
    }

    cancelShow(course: ExcellentCourseConfigDetail) {
      console.log(course, '====course')
      this.$confirm('确定取消？取消后不展示在门户精品课程栏目下！', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        if (this.excellentCourseConfig.hasCategory) {
          this.excellentCourseConfig.removeCourse(this.currentSelectCategory, course)
        } else {
          this.excellentCourseConfig.removeCourse(null, course)
          this.currentCourseList = this.excellentCourseConfig.courses
        }
      })
    }

    async created() {
      await this.excellentCourseConfig.queryConfig()
      if (this.excellentCourseConfig.hasCategory && this.excellentCourseConfig.categories.length) {
        this.currentSelectCategory = this.excellentCourseConfig.categories[0]
      }
      this.hasCategory = this.excellentCourseConfig.hasCategory
      this.currentCourseList = this.excellentCourseConfig.courses
    }

    chooseCategory(category: CourseCategoryListDetail) {
      this.excellentCourseConfig.addCategory(category)
    }

    @Watch('excellentCourseConfig.categories')
    async sortAbleCategory() {
      this.$nextTick(() => {
        SortAble.create(this.$refs.excellentCategoryList as HTMLElement, {
          onEnd: async ({ newIndex, oldIndex }) => {
            this.excellentCourseConfig.changeCategoryPosition(oldIndex, newIndex)
            await this.excellentCourseConfig.doSaveConfig()
          }
        })
      })
    }

    makeTableSortable() {
      const refTable = this.$refs.tableList as ElTable
      SortAble.create(refTable.$el.querySelector('tbody'), {
        handle: '.icon-drag',
        onEnd: async ({ newIndex, oldIndex }) => {
          if (this.excellentCourseConfig.hasCategory) {
            this.currentSelectCategory.changeCoursePosition(oldIndex, newIndex)
          } else {
            this.excellentCourseConfig.changeCoursePosition(oldIndex, newIndex)
          }

          this.reloadTable = false
          this.$nextTick(async () => {
            this.reloadTable = true
            setTimeout(() => {
              this.makeTableSortable()
            })
            refTable.doLayout()
            await this.excellentCourseConfig.doSaveConfig()
          })
        }
      })
    }

    get courseList(): Array<ExcellentCourseConfigDetail> {
      if (this.excellentCourseConfig.hasCategory) {
        return this.currentSelectCategory.courses
      }
      this.$nextTick(() => {
        this.makeTableSortable()
      })
      return this.currentCourseList
    }

    get selectedCourseIdList() {
      return this.courseList.map(course => course.id)
    }

    // 关闭课程选择弹窗
    closeCourseSelect() {
      this.showSelectCourse = false
    }
  }
</script>
