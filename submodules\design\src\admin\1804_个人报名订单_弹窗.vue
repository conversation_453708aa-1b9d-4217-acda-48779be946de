<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--选择收款账号-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20">选择收款账号</el-button>
        <el-drawer
          title="选择收款账号"
          :visible.sync="dialog3"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="收款账号别名" min-width="180">
                <template>收款账号别名</template>
              </el-table-column>
              <el-table-column label="操作" width="140" align="center">
                <template>
                  <el-radio v-model="radio" label="1">选择</el-radio>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-drawer>
        <!--关联售后信息-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">查看关联售后信息</el-button>
        <el-drawer
          title="关联售后信息"
          :visible.sync="dialog2"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
              <el-tab-pane label="换班" name="first">
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column label="操作时间" min-width="180" fixed="left">
                    <template>2021-10-15 00:21:21</template>
                  </el-table-column>
                  <el-table-column label="初始班级" min-width="300">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <p>培训班名称培训班名称培训班名称培训班名称培训班名称培训班名称培训班名称</p>
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <p><el-tag type="danger" size="mini">初始物品</el-tag></p>
                      </div>
                      <div v-else>
                        <p>培训班名称培训班名称培训班名称培训班名称培训班名称培训班名称培训班名称</p>
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <p><el-tag type="danger" size="mini">初始物品</el-tag></p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="新班级" min-width="300">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <p>培训班名称培训班名称培训班名称培训班名称培训班名称培训班名称培训班名称</p>
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <p><el-tag type="danger" size="mini">最新物品</el-tag></p>
                      </div>
                      <div v-else>
                        <p>培训班名称培训班名称培训班名称培训班名称培训班名称培训班名称培训班名称</p>
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <p><el-tag type="danger" size="mini">最新物品</el-tag></p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="单价(元)" min-width="120" align="right">
                    <template>0.01</template>
                  </el-table-column>
                  <el-table-column label="售后状态" min-width="120" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-tag type="success">换班成功</el-tag>
                        <p class="f-mt5"><a href="#" class="f-link f-underline f-cb f-f12">查看详情</a></p>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-tag type="success">换班成功</el-tag>
                        <p class="f-mt5"><a href="#" class="f-link f-underline f-cb f-f12">查看详情</a></p>
                      </div>
                      <div v-else>
                        <el-tag type="warning">换班中</el-tag>
                        <p class="f-mt5"><a href="#" class="f-link f-underline f-cb f-f12">查看详情</a></p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作人" min-width="110">
                    <template>林林一</template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane label="换期" name="second">
                <el-table stripe :data="tableData" max-height="500px" class="m-table">
                  <el-table-column label="操作时间" min-width="180" fixed="left">
                    <template>2021-10-15 00:21:21</template>
                  </el-table-column>
                  <el-table-column label="初始期别" min-width="300">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <p>
                          <el-tag type="info" size="mini">所属方案</el-tag
                          >方案名称方案名称方案名称方案名称方案名称方案名称方案名称方案名称
                        </p>
                        <p><el-tag type="danger" size="mini">初始物品</el-tag></p>
                      </div>
                      <div v-else>
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <p>
                          <el-tag type="info" size="mini">所属方案</el-tag
                          >方案名称方案名称方案名称方案名称方案名称方案名称方案名称方案名称
                        </p>
                        <p><el-tag type="danger" size="mini">初始物品</el-tag></p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="新期别" min-width="300">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <p>
                          <el-tag type="info" size="mini">所属方案</el-tag
                          >方案名称方案名称方案名称方案名称方案名称方案名称方案名称方案名称
                        </p>
                        <p><el-tag type="danger" size="mini">最新物品</el-tag></p>
                      </div>
                      <div v-else>
                        <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                        <p>
                          <el-tag type="info" size="mini">所属方案</el-tag
                          >方案名称方案名称方案名称方案名称方案名称方案名称方案名称方案名称
                        </p>
                        <p><el-tag type="danger" size="mini">最新物品</el-tag></p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作类型" min-width="120" align="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        换班换期
                      </div>
                      <div v-else>
                        换班换期
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="售后状态" min-width="120" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-tag type="success">换期成功</el-tag>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <el-tag type="success">换期成功</el-tag>
                      </div>
                      <div v-else>
                        <el-tag type="warning">换期中</el-tag>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作人" min-width="110">
                    <template>林林一</template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-drawer>
        <!--退款-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">退款</el-button>
        <el-drawer title="退款" :visible.sync="dialog1" :direction="direction" size="900px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              退款后，原有的学习记录将清空，请确认是否继续。
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-form-item label="退款金额：" class="is-text">
                    <span class="f-cr f-fb f-f16">¥ 10</span>
                    <span class="f-cr">（换班订单退款退的是初始物品的订单价格，退物品是最新换入成功的物品。）</span>
                  </el-form-item>
                  <el-form-item label="退款提示：" class="is-text">
                    班级已考核通过，是否强制退款？
                  </el-form-item>
                  <el-form-item label="退款理由：" required>
                    <el-select v-model="form.region" clearable placeholder="请选择退款理由">
                      <el-option label="帮助中心" value="shanghai"></el-option>
                      <el-option label="消息" value="beijing"></el-option>
                    </el-select>
                  </el-form-item>
                  <el-form-item label="退款说明：" required>
                    <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入退款说明" />
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认退款</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--查看换班详情-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20">查看详情（换班）</el-button>
        <el-drawer title="查看详情" :visible.sync="dialog4" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发起换班</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">退班处理中</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">退班失败</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">申请发货</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发货处理中</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换班成功</span></p>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
        <!--查看换期详情-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">查看详情（换期）</el-button>
        <el-drawer title="查看详情" :visible.sync="dialog5" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发起换期</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换期处理中</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换期失败</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">发起换期</span></p>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">换期成功</span></p>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
