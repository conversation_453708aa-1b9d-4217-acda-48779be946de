import LearningTypeBase from '@api/service/common/scheme/model/LearningTypeBase'
import issueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'

/**
 * @description 期别学习方式
 */
class IssueLearningType extends LearningTypeBase {
  /**
   * 期别配置列表
   */
  issueConfigList: issueConfigDetail[] = []
  /**
   * 期别数量
   * @description 仅适用于未获取期别配置列表但又有展示需求的场景
   */
  issueCount = 0
}

export default IssueLearningType
