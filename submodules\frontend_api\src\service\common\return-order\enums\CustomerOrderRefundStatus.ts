import AbstractEnum from '@api/service/common/enums/AbstractEnum'
import { OrderRefundStatusEnum } from '@api/service/common/return-order/enums/OrderRefundStatus'

// 此类只拿来做customer状态映射

class CustomerOrderRefundStatus extends AbstractEnum<OrderRefundStatusEnum> {
  static enum = OrderRefundStatusEnum
  constructor(status?: OrderRefundStatusEnum) {
    super()
    this.current = status
    this.map.set(OrderRefundStatusEnum.pendingAudit, '审核中')
    this.map.set(OrderRefundStatusEnum.cancelled, '已取消')
    this.map.set(OrderRefundStatusEnum.rejected, '已拒绝')
    this.map.set(OrderRefundStatusEnum.pendingRefundOrReturn, '退货/款处理中')
    this.map.set(OrderRefundStatusEnum.pendingReturn, '退货/款处理中')
    this.map.set(OrderRefundStatusEnum.returned, '退货/款处理中')
    this.map.set(OrderRefundStatusEnum.returnFailed, '退货/款处理中')
    this.map.set(OrderRefundStatusEnum.refundFailed, '退货/款处理中')
    this.map.set(OrderRefundStatusEnum.pendingConfirmRefund, '退货/款处理中')
    this.map.set(OrderRefundStatusEnum.pendingRefund, '退货/款处理中')
    this.map.set(OrderRefundStatusEnum.refunded, '退货/款处理中')
    this.map.set(OrderRefundStatusEnum.refundSuccess, '退货/款成功')
  }
}

export default new CustomerOrderRefundStatus()
