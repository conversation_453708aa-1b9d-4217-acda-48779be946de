import { CollectiveSignUpTypeEnum } from '@api/service/common/enums/online-school-config/CollectiveSignUpType'
import { ResponseStatus } from '@hbfe/common'
import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'
import CollectSignUpVo from './vo/CollectionSignUp/CollectSignUpVo'
import OnlineCollectSignUpVo from './vo/CollectionSignUp/OnlineCollectSignUpVo'
import OfflineCollectSignUpVo from './vo/CollectionSignUp/OfflineCollectSignUpVo'

class QueryCollectiveSignUp {
  type = CollectiveSignUpTypeEnum.ONLINE
  collectiveSignUp = new CollectSignUpVo()

  constructor(type?: CollectiveSignUpTypeEnum) {
    this.type = type ? type : CollectiveSignUpTypeEnum.ONLINE
  }

  /**
   * 保存集体报名配置
   * @returns
   */
  async queryDetail(): Promise<ResponseStatus> {
    if (this.type === CollectiveSignUpTypeEnum.ONLINE) {
      const msRes = await ServicerSeriesV1Gateway.getOnlineCollectiveRegisterConfig()
      if (msRes.status.isSuccess()) {
        this.collectiveSignUp = new OnlineCollectSignUpVo()
        this.collectiveSignUp.from(msRes.data)
      }
      return msRes.status
    } else {
      const msRes = await ServicerSeriesV1Gateway.getOfflineCollectiveRegisterConfig()
      if (msRes.status.isSuccess()) {
        this.collectiveSignUp = new OfflineCollectSignUpVo()
        this.collectiveSignUp.from(msRes.data)
      }
      return msRes.status
    }
  }
}
export default new QueryCollectiveSignUp()
