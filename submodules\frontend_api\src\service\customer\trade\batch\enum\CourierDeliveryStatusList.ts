import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 快递配送状态枚举
 */
export enum CourierDeliveryStatusEnum {
  // 1：未发货
  Wait_For_Courier = 1,
  // 2：已经发货
  Complete_Courier
}

/**
 * @description 快递配送状态枚举
 */
class CourierDeliveryStatusList extends AbstractEnum<CourierDeliveryStatusEnum> {
  static enum = CourierDeliveryStatusEnum
  constructor(status?: CourierDeliveryStatusEnum) {
    super()
    this.map.set(CourierDeliveryStatusEnum.Wait_For_Courier, '未发货')
    this.map.set(CourierDeliveryStatusEnum.Complete_Courier, '已经发货')
  }
}

export default new CourierDeliveryStatusList()
