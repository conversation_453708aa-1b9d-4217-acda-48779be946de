import BasicDataGateway from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import BasicData, { BusinessDataDictionaryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import BasicDataForestage from '@api/ms-gateway/ms-basicdata-query-front-gateway-forestage'
import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'
import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
import { ResponseStatus } from '@hbfe/common'

/**
 * @description 查询行业类
 */
class QueryIndustry {
  /**
   * 行业列表（带有行业属性编号）
   */
  industryList: Array<IndustryVo> = []
  /**
   * 行业字典列表（回显名称用）
   */
  industryDICTList: Array<BusinessDataDictionaryResponse> = []
  industryDICTNewList: Array<BusinessDataDictionaryResponse> = []
  /**
   * 专题行业map
   */
  industryMap = new Map<string, BusinessDataDictionaryResponse>()

  /**
   * 行业列表id集合
   */
  private industryListIds = new Array<string>()

  /**
   * 查询行业列表 ----------- 数据参与业务使用
   * resIndustry 获取网校配置的行业列表（缺少名称）
   * resInfo 带有名称的行业详细信息
   * industryVo 返回给UI展示
   * @return status
   */
  async queryIndustry() {
    if (this.industryListIds.length) {
      return new ResponseStatus(200, '行业信息使用状态层缓存')
    } else {
      const resIndustry = await ServicerSeriesV1Gateway.getIndustries()
      if (resIndustry.status.isSuccess()) {
        const industryIdList = new Array<string>()
        resIndustry.data?.forEach(item => {
          industryIdList.push(item.id)
        })
        const resInfo = await BasicDataGateway.listIndustryInfoV2(industryIdList)
        this.industryListIds = industryIdList
        if (resInfo.status.isSuccess()) {
          const industryList = new Array<IndustryVo>()
          resIndustry.data?.map(industry => {
            const industryVo = new IndustryVo()
            Object.assign(industryVo, industry)
            // 进行拼接name
            resInfo.data.map(info => {
              if (industryVo.id === info.id) {
                industryVo.name = info.name
                industryVo.sort = info.sort
              }
            })
            industryList.push(industryVo)
          })
          // this.industryList = industryList
          this.industryList = []
          if (industryList.filter(item => item.name == '人社行业').length) {
            // 原型排序需求 人社》建设》卫生》工勤》教师
            this.industryList[0] = industryList.filter(item => item.name == '人社行业')[0]
            industryList.map(item => {
              if (item.name != '人社行业') {
                this.industryList.push(item)
              }
            })
          } else {
            this.industryList = industryList
          }
        }
        return resIndustry.status
      }
    }
  }

  /**
   * 查询行业信息通过行业id列表----------- 数据参与业务使用
   * @param {Array<string>} industryIdList 行业id列表
   * @return
   */
  async queryIndustryByIdList(industryIdList: Array<string>) {
    const response = await BasicDataGateway.listIndustryInfoV2(industryIdList)
    if (response.status.isSuccess()) {
      return response.data
    }
    return new Array<IndustryVo>()
  }

  /**
   * 【本地】获取行业信息根据行业id----------- 数据参与业务使用
   * @param {Array<string>} industryIdList 行业id列表
   * @return
   */
  async getIndustryByIdList(industryIdList: Array<string>) {
    const industryList = new Array<IndustryVo>()
    if (!this.industryList?.length) {
      // 存在行业列表为空时 先加载一遍
      await this.queryIndustry()
    }
    industryIdList?.forEach(id => {
      this.industryList?.forEach(industry => {
        if (id === industry.id || id === industry.propertyId) {
          industryList.push(industry)
        }
      })
    })
    return industryList.sort((a, b) => {
      return a.sort - b.sort
    })
  }

  /**
   * 行业字典 ---- 只提供ID映射名称使用(后台用)
   */
  async getIndustryDICT() {
    if (this.industryDICTList?.length) return this.industryDICTList
    const response = await BasicData.listBusinessDataDictionaryInSubProject({ businessDataDictionaryType: 'INDUSTRY' })
    this.industryDICTList = response?.data || []
    return response.data
  }
  async getIndustryDICTZT() {
    if (this.industryDICTNewList?.length) return this.industryDICTNewList
    const response = await BasicData.listBusinessDataDictionaryInSubProject({
      businessDataDictionaryType: 'TRAINING_CHANNEL_INDUSTRY'
    })
    this.industryDICTNewList = response?.data || []
    return response.data
  }
  /**
   * 行业字典 ---- 只提供ID映射名称使用(前台用)
   */
  async getIndustryForestage() {
    if (this.industryDICTList?.length) return this.industryDICTList
    const response = await BasicDataForestage.listBusinessDataDictionaryInSubProject({
      businessDataDictionaryType: 'INDUSTRY'
    })
    this.industryDICTList = response?.data || []
    return response.data
  }
  async getIndustryDICTZTForestage(idList: Array<string>) {
    const cacheIds = [...this.industryMap.keys()]
    const noCacheIds = idList.filter(id => !cacheIds.includes(id))
    if (noCacheIds.length) {
      const response = await BasicDataForestage.listBusinessDataDictionaryByIdInSubProject(idList)
      response.data?.forEach(item => {
        this.industryMap.set(item.id, item)
      })
    }
  }
}

export default new QueryIndustry()
