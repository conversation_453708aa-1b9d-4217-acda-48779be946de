import msPractice from '@api/ms-gateway/ms-practice-v1'
import { MutationCreatePaper } from '@api/service/customer/exam/mutation/MutationCreatePaper'
import {
  MultipleQuestionResponse,
  OpinionQuestionResponse,
  QuestionGroupViewResponse,
  RadioQuestionResponse
} from '@api/service/customer/exam/mutation/vo/AnswerPaper'
import { cloneDeep } from 'lodash'
import msPaperAnswer, {
  MultipleQuestionAnswer,
  MultipleQuestionToAnswer,
  OpinionQuestionAnswer,
  OpinionQuestionToAnswer,
  RadioQuestionAnswer,
  RadioQuestionToAnswer
} from '@api/ms-gateway/ms-exam-answer-v1'
import { QuestionKeyValue, QuestionTypeEnum } from '@api/service/customer/exam/utils/Constant'

export class MutationCreatePracticePaper extends MutationCreatePaper {
  /**
   * 抽题数目，number 仅练习时候有效
   */
  requireQuestionCount = 0

  /**
   * 获取作答token
   */
  async getAnswerToken() {
    const res = await msPractice.applyPractice({
      studentToken: this.studentToken,
      requireQuestionCount: this.requireQuestionCount
    })
    if (res.status.isSuccess()) {
      this.answerToken = res.data
    }
    return res.status
  }
  /**
   * 获取作答token 练习配置为同考试时不传题目数
   */
  async getAnswerTokenWithoutNum() {
    const res = await msPractice.applyPractice({
      studentToken: this.studentToken
    })
    if (res.status.isSuccess()) {
      this.answerToken = res.data
    }
    return res.status
  }
  async calPaper() {
    if (!this.answerPaper.groups) {
      this.answerPaper.groups = []
    }

    this.answerPaper.questions.forEach(item => {
      const questionGroup = this.answerPaper.groups.find(tmpItem => tmpItem.questionType == item.questionType)
      if (questionGroup) {
        questionGroup.questionCount++
      } else {
        const insertGroup = new QuestionGroupViewResponse()
        insertGroup.questionCount = 1
        insertGroup.questionType = item.questionType
        insertGroup.groupName = QuestionKeyValue.getQuestionName(insertGroup.questionType)
        insertGroup.sequence = this.answerPaper.groups.length
        this.answerPaper.groups.push(insertGroup)
      }
    })
    const questionIdList = this.answerPaper.questions
      .filter(
        item =>
          [
            QuestionTypeEnum.QuestionTypeEnumRadio,
            QuestionTypeEnum.QuestionTypeEnumMul,
            QuestionTypeEnum.QuestionTypeEnumOpi
          ].indexOf(item.questionType) != -1
      )
      .map(tmpItem => {
        return tmpItem.id
      })
    try {
      const answers = await msPaperAnswer.getQuestionToAnswerByQuestionIds({
        encryptQuestionIds: questionIdList,
        answerToken: this.answerToken
      })
      this.answerPaper.questions.forEach(item => {
        const answerM = answers.data.questionToAnswers.find(tmpI => tmpI.questionId == item.id)
        if (answerM) {
          if (item.questionType == QuestionTypeEnum.QuestionTypeEnumRadio) {
            ;(item as RadioQuestionResponse).trueRadioAnswer = (answerM as RadioQuestionToAnswer).radioCorrectAnswer
          }
          // 1-单选题  2-多选题  3-填空题  4-判断题  5-简答题  6-父子题
          if (item.questionType == QuestionTypeEnum.QuestionTypeEnumMul) {
            ;(item as MultipleQuestionResponse).trueMultipleAnswer = (answerM as MultipleQuestionToAnswer).multipleCorrectAnswers
          }
          if (item.questionType == QuestionTypeEnum.QuestionTypeEnumOpi) {
            ;(item as OpinionQuestionResponse).trueOpinionAnswer = (answerM as OpinionQuestionToAnswer).opinionCorrectAnswer
            ;(item as OpinionQuestionResponse).opinionCorrectAnswer = (answerM as OpinionQuestionToAnswer).opinionCorrectAnswer
          }
        }
      })
    } catch (e) {
      console.log(e)
    }

    this.answerPaper = cloneDeep(this.answerPaper)
  }
}
