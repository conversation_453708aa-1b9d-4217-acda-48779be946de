import { FieldConstraintResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import FieldConstraintVo from './FieldConstraintVo'

class PharmacistIndustryRegisterVo {
  /**
   * 证书类型
   */
  certificatesType: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 执业类别
   */
  practitionerCategory: FieldConstraintVo = new FieldConstraintVo()

  from(res: Array<FieldConstraintResponse>) {
    const resMap = new Map()
    res?.forEach(item => {
      resMap.set(item.field, item)
    })
    this.certificatesType = FieldConstraintVo.from(resMap.get('certificatesType'))
    this.practitionerCategory = FieldConstraintVo.from(resMap.get('practitionerCategory'))
  }
}
export default PharmacistIndustryRegisterVo
