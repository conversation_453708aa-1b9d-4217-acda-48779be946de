<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--请选择导出方式-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">选择导出方式</el-button>
        <el-drawer title="提示" :visible.sync="dialog1" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="请选择导出方式：">
                <el-radio-group v-model="form.resource">
                  <el-radio label="导出列表详细数据"></el-radio>
                  <el-radio label="导出学员学习日志"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">确定</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <!--导出弹窗提示-->
        <el-button type="primary" @click="dialog2 = true" class="f-mr20">导出弹窗提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog2" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb f-f16">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务查看，选择学员学习日志！</p>
            </div>
          </div>
          <div slot="footer">
            <el-button>暂 不</el-button>
            <el-button type="primary">前往下载</el-button>
          </div>
        </el-dialog>
        <!--查看考勤详情-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20 f-mb20">查看考勤详情</el-button>
        <el-drawer
          title="查看考勤详情"
          :visible.sync="dialog3"
          :direction="direction"
          size="1000px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" :closable="false" class="m-alert">
              <p><b>考勤规则</b></p>
              <p>1、开启签到：每半天签到一次，第一节课开始授课前X分钟和开始授课后Y分钟之间，需签到1次。</p>
              <p>2、开启答退：每半天签退一次，第一节课开始授课前X分钟和开始授课后Y分钟之间，需签退1次。</p>
            </el-alert>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="课程名称" min-width="240">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    课程名称课程名称课程名称课程名称课程名称课程名称
                  </div>
                  <div v-else>
                    课程名称课程名称课程名称课程名称课程名称课程名称
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="授课时间" min-width="180" align="center">
                <template>
                  <p><el-tag type="info" size="mini">开始</el-tag>xxxx-xx-xx</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>xxxx-xx-xx</p>
                </template>
              </el-table-column>
              <el-table-column label="要求签到/签退情况" min-width="150" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    要求签到/不要求签退
                  </div>
                  <div v-else>
                    要求签到/不要求签退
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="签到时间" min-width="120" align="center">
                <template>
                  xxxx-xx-xx
                </template>
              </el-table-column>
              <el-table-column label="签退时间" min-width="120" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    xxxx-xx-xx
                  </div>
                  <div v-else>
                    <span class="f-ci">未签退</span>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">返回</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }],
        tableData4: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }],
        tableData5: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '4' }],
        tableData6: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' }
        ],
        tableData8: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '4' },
          { field101: '5' }
        ],
        tableData9: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' }
        ],
        tableData12: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' }
        ],
        tableData14: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' }
        ],
        tableData15: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
