<template>
  <div class="auth-demo-page">
    <el-card class="demo-card">
      <div slot="header">
        <span>认证和权限系统演示</span>
      </div>

      <!-- 当前用户信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="info-card">
            <div slot="header">当前用户信息</div>
            <el-descriptions :column="1" border>
              <el-descriptions-item label="用户ID">
                {{ currentUser.userId || '未获取' }}
              </el-descriptions-item>
              <el-descriptions-item label="用户名">
                {{ currentUser.userName || '未获取' }}
              </el-descriptions-item>
              <el-descriptions-item label="角色类型">
                {{ currentUser.roleType || '未获取' }}
              </el-descriptions-item>
              <el-descriptions-item label="登录状态">
                <el-tag :type="isLogin ? 'success' : 'danger'">
                  {{ isLogin ? '已登录' : '未登录' }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="Token状态">
                <el-tag :type="hasToken ? 'success' : 'warning'">
                  {{ hasToken ? '有效' : '无效' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </el-card>
        </el-col>

        <el-col :span="12">
          <el-card class="info-card">
            <div slot="header">权限检查演示</div>
            <div class="permission-demo">
              <div class="permission-item">
                <span>用户查看权限：</span>
                <el-tag :type="$hasPermission('user.view') ? 'success' : 'danger'">
                  {{ $hasPermission('user.view') ? '有权限' : '无权限' }}
                </el-tag>
              </div>
              <div class="permission-item">
                <span>用户编辑权限：</span>
                <el-tag :type="$hasPermission('user.edit') ? 'success' : 'danger'">
                  {{ $hasPermission('user.edit') ? '有权限' : '无权限' }}
                </el-tag>
              </div>
              <div class="permission-item">
                <span>用户删除权限：</span>
                <el-tag :type="$hasPermission('user.delete') ? 'success' : 'danger'">
                  {{ $hasPermission('user.delete') ? '有权限' : '无权限' }}
                </el-tag>
              </div>
              <div class="permission-item">
                <span>用户导出权限：</span>
                <el-tag :type="$hasPermission('user.export') ? 'success' : 'danger'">
                  {{ $hasPermission('user.export') ? '有权限' : '无权限' }}
                </el-tag>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <!-- 权限控制演示 -->
      <el-card class="demo-card" style="margin-top: 20px;">
        <div slot="header">权限控制按钮演示</div>
        <div class="button-demo">
          <el-button v-if="$hasPermission('user.view')" type="primary">
            查看用户（需要user.view权限）
          </el-button>
          <el-button v-if="$hasPermission('user.edit')" type="warning">
            编辑用户（需要user.edit权限）
          </el-button>
          <el-button v-if="$hasPermission('user.delete')" type="danger">
            删除用户（需要user.delete权限）
          </el-button>
          <el-button v-if="$hasPermission('user.export')" type="success">
            导出用户（需要user.export权限）
          </el-button>
          <el-button v-if="$hasPermission('admin.system')" type="info">
            系统管理（需要admin.system权限）
          </el-button>
        </div>
      </el-card>

      <!-- 开发工具 -->
      <el-card class="demo-card" style="margin-top: 20px;">
        <div slot="header">开发工具</div>
        <div class="dev-tools">
          <el-button @click="togglePermissionControl">
            {{ permissionControlEnabled ? '关闭' : '开启' }}权限控制
          </el-button>
          <el-button @click="refreshUserInfo">刷新用户信息</el-button>
          <el-button @click="showSecurityList">查看权限列表</el-button>
          <el-button @click="showTokenInfo">查看Token信息</el-button>
        </div>
      </el-card>

      <!-- 权限列表显示 -->
      <el-card v-if="showPermissions" class="demo-card" style="margin-top: 20px;">
        <div slot="header">当前用户权限列表</div>
        <div class="permissions-list">
          <el-tag 
            v-for="permission in userPermissions" 
            :key="permission" 
            style="margin: 5px;"
            size="small"
          >
            {{ permission }}
          </el-tag>
          <div v-if="userPermissions.length === 0" class="no-permissions">
            暂无权限数据或权限控制已关闭
          </div>
        </div>
      </el-card>

      <!-- Token信息显示 -->
      <el-card v-if="showToken" class="demo-card" style="margin-top: 20px;">
        <div slot="header">Token信息</div>
        <el-descriptions :column="1" border>
          <el-descriptions-item label="Access Token">
            <div class="token-display">{{ accessToken || '未获取' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="Refresh Token">
            <div class="token-display">{{ refreshToken || '未获取' }}</div>
          </el-descriptions-item>
          <el-descriptions-item label="Token过期时间">
            {{ tokenExpireTime || '未获取' }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>
    </el-card>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import RootModule from '@/store/RootModule'
import DevToolsModule from '@/store/devtools/DevToolsModule'
import UserModule from '@api/service/management/user/UserModule'

@Component
export default class AuthDemoPage extends Vue {
  // 数据属性
  private currentUser: any = {}
  private showPermissions = false
  private showToken = false
  private userPermissions: string[] = []
  private accessToken = ''
  private refreshToken = ''
  private tokenExpireTime = ''

  // 计算属性
  get isLogin() {
    return RootModule.isLogin
  }

  get hasToken() {
    return !!localStorage.getItem('admin.Access-Token')
  }

  get permissionControlEnabled() {
    return DevToolsModule.developmentSettings.permissionControl
  }

  // 生命周期
  async mounted() {
    await this.loadUserInfo()
    this.loadTokenInfo()
  }

  // 方法
  private async loadUserInfo() {
    try {
      // 获取当前用户信息
      await UserModule.queryManagerDetail()
      this.currentUser = {
        userId: UserModule.adminInfo?.userInfo?.userId,
        userName: UserModule.adminInfo?.userInfo?.userName,
        roleType: UserModule.adminInfo?.roleType
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      this.$message.error('获取用户信息失败')
    }
  }

  private loadTokenInfo() {
    this.accessToken = localStorage.getItem('admin.Access-Token') || ''
    this.refreshToken = localStorage.getItem('admin.Refresh-Token') || ''
    const expiresIn = localStorage.getItem('admin.ExpiresIn')
    if (expiresIn) {
      this.tokenExpireTime = new Date(Number(expiresIn)).toLocaleString()
    }
  }

  private togglePermissionControl() {
    const newValue = !this.permissionControlEnabled
    DevToolsModule.updateDevelopmentSettings({
      permissionControl: newValue
    })
    this.$message.success(`权限控制已${newValue ? '开启' : '关闭'}`)
  }

  private async refreshUserInfo() {
    await this.loadUserInfo()
    this.loadTokenInfo()
    this.$message.success('用户信息已刷新')
  }

  private showSecurityList() {
    this.userPermissions = RootModule.securityList || []
    this.showPermissions = !this.showPermissions
  }

  private showTokenInfo() {
    this.loadTokenInfo()
    this.showToken = !this.showToken
  }
}
</script>

<style scoped>
.auth-demo-page {
  padding: 20px;
}

.demo-card {
  margin-bottom: 20px;
}

.info-card {
  height: 100%;
}

.permission-demo {
  padding: 10px 0;
}

.permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding: 8px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.button-demo {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.dev-tools {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.permissions-list {
  max-height: 300px;
  overflow-y: auto;
}

.no-permissions {
  text-align: center;
  color: #909399;
  padding: 20px;
}

.token-display {
  word-break: break-all;
  font-family: monospace;
  font-size: 12px;
  background-color: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
  max-height: 100px;
  overflow-y: auto;
}
</style>
