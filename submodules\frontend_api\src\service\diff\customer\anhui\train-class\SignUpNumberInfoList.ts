import PlatformJxjypxtyptAhzjSchool from '@api/platform-gateway/platform-jxjypxtypt-ahzj-school'
import SignUpNumberInfoItem from '@api/service/diff/customer/anhui/train-class/model/SignUpNumberInfoItem'
import { ResponseStatus } from '@hbfe/common'
import { OrderStatusEnum } from '@api/service/diff/customer/anhui/train-class/enums/OrderStatus'
export default class SignUpNumberInfoList {
  /**
   * 用户身份证
   */
  idCard = ''
  /**
   * 列表
   */
  list: Array<SignUpNumberInfoItem> = []
  /**
   * 查询列表
   */
  async queryList() {
    this.list = []
    if (!this.idCard) return new ResponseStatus(10001, '证件号不能为空')
    const res = await PlatformJxjypxtyptAhzjSchool.listSignUpNumberInfo({
      certificateNumber: this.idCard
    })
    if (res.status.isSuccess()) {
      this.list = (res.data?.map(item => Object.assign(new SignUpNumberInfoItem(), item)) || []).filter(
        item => item.orderStatus !== OrderStatusEnum.paid
      )
    }
    return res.status
  }
}
