import MsTradeQueryFrontGatewayCourseLearningBacktage, {
  Page,
  ReturnOrderRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import ReturnOrderResponseVo from '@api/service/management/trade/single/order/query/vo/ReturnOrderResponseVo'
import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import { ResponseStatus } from '@hbfe/common'
import ExchangeDetailVo from '@api/service/management/trade/single/order/query/vo/ExchangeDetailVo'
export default class QueryExchangeDetail {
  //换货单号
  exchangeNo = ''
  //取换货单详情对象
  exchangeOrderDetail = new ExchangeDetailVo()
  /**
   * 获取换货单详情
   *
   */
  async queryRefundOrderDetail(): Promise<ResponseStatus> {
    if (!this.exchangeNo) {
      return new ResponseStatus(8006, '换货单号不能为空')
    }
    const res = await MsTradeQueryFrontGatewayCourseLearningBacktage.getExchangeOrderInServicer(this.exchangeNo)
    // const
    if (res.status.isSuccess()) {
      const returnVo = new ExchangeDetailVo()
      Object.assign(returnVo, res.data)
      returnVo.changeStatue()
      returnVo.addRecords()
      // const newData = res.data as ReturnOrderResponseVo
      //  if ()
      this.exchangeOrderDetail = returnVo
    }
    return res.status
  }
  /**
   * 获取换货单详情-分销商
   *
   */
  async queryRefundOrderDetailInDistributor(): Promise<ResponseStatus> {
    if (!this.exchangeNo) {
      return new ResponseStatus(8006, '换货单号不能为空')
    }
    const res = await MsTradeQueryFrontGatewayCourseLearningBacktage.getExchangeOrderInDistributor(this.exchangeNo)
    // const
    if (res.status.isSuccess()) {
      const returnVo = new ExchangeDetailVo()
      Object.assign(returnVo, res.data)
      returnVo.changeStatue()
      returnVo.addRecords()
      // const newData = res.data as ReturnOrderResponseVo
      //  if ()
      this.exchangeOrderDetail = returnVo
    }
    return res.status
  }
}
