import { RefundProcessTypeEnum } from '@api/service/management/trade/single/order/enum/RefundProcessTypeEnum'
import {
  RefundInfoResponse,
  ReturnApprovalInfoResponse,
  ReturnOrderResponse,
  ReturnOrderStatusChangeTimeResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { OrderRefundStatusEnum } from '@api/service/common/return-order/enums/OrderRefundStatus'

export default class RefundProcess {
  /**
   * 节点类型
   */
  type: RefundProcessTypeEnum = undefined

  /**
   * 步骤描述
   */
  description = ''

  /**
   * 步骤时间
   */
  finishTime = ''

  /**
   * 是否结束
   */
  processEnd = false

  /**
   * 模型转化
   * @param type 节点类型
   * @param dto 后端退款元数据（因为不同节点类型要取对的子对象不同，故这边把整个dto传进来了）
   * @param refundStatus 退款状态
   */
  static from(type: RefundProcessTypeEnum, dto: ReturnOrderResponse, refundStatus: OrderRefundStatusEnum) {
    const vo = new RefundProcess()
    vo.type = type
    const changeTime = dto?.basicData?.returnOrderStatusChangeTime || new ReturnOrderStatusChangeTimeResponse()
    const approveInfo = dto?.approvalInfo || new ReturnApprovalInfoResponse()
    const refundInfo = dto?.refundInfo || new RefundInfoResponse()

    if (type == RefundProcessTypeEnum.submitApplication) {
      vo.description = `${changeTime.applied}`
      vo.finishTime = changeTime.applied
      vo.processEnd = true
    }
    if (type == RefundProcessTypeEnum.approveApplication) {
      // 已取消 (取消状态需要借助退货单状态去判断)
      if (refundStatus == OrderRefundStatusEnum.cancelled) {
        vo.description = ` ${approveInfo.cancelApproveTime}`
        vo.finishTime = approveInfo.cancelApproveTime
        vo.processEnd = false
      }
      // 已审批
      if (approveInfo.approveStatus == 1) {
        if (approveInfo.approveResult === 0) {
          vo.description = `${approveInfo.approveTime}`
          vo.finishTime = approveInfo.approveTime
          vo.processEnd = false
        } else if (approveInfo.approveResult === 1) {
          vo.description = `${approveInfo.approveTime}`
          vo.finishTime = approveInfo.approveTime
          vo.processEnd = true
        }
      }
    }
    if (type == RefundProcessTypeEnum.handleReturn) {
      if (changeTime.returned) {
        vo.description = `${changeTime.returned}`
        vo.finishTime = changeTime.returned
        vo.processEnd = true
      } else if (changeTime.returnFailed) {
        vo.description = `${changeTime.returnFailed}`
        vo.finishTime = changeTime.returnFailed
        vo.processEnd = true
      }
    }
    if (type == RefundProcessTypeEnum.refundConfirmation) {
      if (refundInfo.refundConfirmedTime) {
        vo.finishTime = refundInfo.refundConfirmedTime
        vo.description = `${refundInfo.refundConfirmedTime}`
        vo.processEnd = true
      }
    }
    if (type == RefundProcessTypeEnum.processRefund) {
      if (changeTime.refunded) {
        vo.description = `${changeTime.refunded}`
        vo.finishTime = changeTime.refunded
        vo.processEnd = true
      } else if (changeTime.refundFailed) {
        vo.description = `${changeTime.refundFailed}`
        vo.finishTime = changeTime.refundFailed
        vo.processEnd = true
      }
    }
    if (type == RefundProcessTypeEnum.completeProcess) {
      if (changeTime.returnCompleted) {
        vo.description = `${changeTime.returnCompleted}`
        vo.finishTime = changeTime.returnCompleted
        vo.processEnd = true
      }
    }

    return vo
  }
}
