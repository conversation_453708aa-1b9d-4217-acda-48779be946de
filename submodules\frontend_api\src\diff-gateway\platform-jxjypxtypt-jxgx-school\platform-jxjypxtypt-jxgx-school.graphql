"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""单点登录入口"""
	enterIndex(request:EnterIndexRequest!):EnterIndexResponse @optionalLogin
	"""获取学员学时数"""
	getStudyCourseNum(request:GetStudyCourseNumRequest):StudyCourseNumResponse
	"""批量作废学员课程"""
	invalidStudentCourses(request:StudentCoursesInvalidRequest):StudentCoursesInvalidResponse
	"""验证是否有开课权限"""
	verifyPermissionsToCourse(request:VerifyPermissionsToCourseRequest):VerifyPermissionsToCourseResponse
	"""验证学员职称和商品上的是否一致
		@param studentId 学员ID
		@param schemeId  方案ID
	"""
	verifyPersonZcInfo(studentId:String,schemeId:String):VerifyPersonZcInfoResponse
	"""验证是否正在其他平台学习"""
	verifyStudyStatus(studentId:String):VerifyStudyStatusResponse
}
"""单点登录请求
	<AUTHOR>
	@since 2024/8/20
"""
input EnterIndexRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.EnterIndexRequest") {
	"""学员信息key"""
	studentInfoKey:String!
}
"""获取学员学时数请求条件"""
input GetStudyCourseNumRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.GetStudyCourseNumRequest") {
	"""学员ID"""
	studentId:String
	"""年度"""
	year:String
	"""培训类别（职称系列）"""
	trainingCategoryId:String
	"""培训专业（职称专业）"""
	trainingProfessionId:String
}
"""学员课程批量作废"""
input StudentCoursesInvalidRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.StudentCoursesInvalidRequest") {
	"""学号"""
	studentNo:String
}
input VerifyPermissionsToCourseRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.VerifyPermissionsToCourseRequest") {
	"""年度"""
	year:String
	"""人员ID"""
	id:String
	"""系列代码"""
	zcxlCode:String
	"""系列名称"""
	zcxlName:String
	"""专业代码"""
	zczyCode:String
	"""专业名称"""
	zczyName:String
}
"""单点响应
	<AUTHOR>
	@since 2024/5/24
"""
type EnterIndexResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.response.EnterIndexResponse") {
	"""登录token"""
	token:String
	"""用户id"""
	userId:String
	"""平台对应培训类别id"""
	trainingCategory:String
	"""平台对应培训专业Id"""
	trainingProfessional:String
	"""管理平台课程id"""
	commodityId:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""学员课程批量作废"""
type StudentCoursesInvalidResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.response.StudentCoursesInvalidResponse") {
	"""作废失败学员课程id"""
	studentCourseIdList:[String]
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""获取学员学时数"""
type StudyCourseNumResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.response.StudyCourseNumResponse") {
	"""学员ID"""
	id:String
	"""年度"""
	year:String
	"""职称系列对应的培训类别"""
	trainingCategoryId:String
	"""职称专业对应的培训专业"""
	trainingProfessionId:String
	"""公需课学时数"""
	publicHours:BigDecimal
	"""专业课学时数"""
	professionHours:BigDecimal
	"""第三方接口信息"""
	msg:String
}
type VerifyPermissionsToCourseResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.response.VerifyPermissionsToCourseResponse") {
	"""是否可以学习"""
	flag:Boolean!
	"""管理系统方的msg"""
	msg:String
}
"""验证职称"""
type VerifyPersonZcInfoResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.response.VerifyPersonZcInfoResponse") {
	"""学员ID"""
	id:String
	"""是否一致，方案上的系列、专业和学员一致的时候返回true"""
	validResult:Boolean!
	"""错误信息"""
	msg:String
	"""学员上的职称信息"""
	studentProfessionInfo:StudentProfessionInfoResponse
	"""商品上的职称信息"""
	commodityProfessionInfo:CommodityProfessionInfoResponse
}
"""<AUTHOR>
	@since 2024/12/21
"""
type VerifyStudyStatusResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.response.VerifyStudyStatusResponse") {
	id:String
	"""1：没有在学课程，<0:有
		在学课程，具体信息见
		MSG
	"""
	code:Int!
	msg:String
	"""平台名称"""
	platformName:String
	"""管理平台课程id"""
	commodityId:String
	"""课程名称"""
	CommodityName:String
}
"""商品职称职称信息"""
type CommodityProfessionInfoResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.response.nested.CommodityProfessionInfoResponse") {
	"""培训类别"""
	trainingCategoryId:String
	"""培训专业"""
	trainingProfessionId:String
}
"""学员职称信息"""
type StudentProfessionInfoResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.response.nested.StudentProfessionInfoResponse") {
	"""培训类别"""
	trainingCategoryId:String
	"""培训专业"""
	trainingProfessionId:String
}

scalar List
