//服务商管理类-洪锦
import AuthModule from '@api/service/common/auth/AuthModule'
import { ServicerDto } from '@api/gateway/PlatformServicer'
import ServicerModule from '@api/service/common/servicer-query/ServicerModule'
import runtimeCon from '@/RuntimeContext/index'
import UserModule from '@api/service/management/user/UserModule'
import FxConfig from '@hbfe/fx-manage/src/config'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'

export interface ServiceTokenInitModel {
  prefix?: string
}

export default class ServiceTokenMixin {
  static servicerTokenKey = 'servicerTokenKey'
  static selectedServiceKey = 'selectedServiceLey'
  private serviceDetail: ServicerDto = new ServicerDto()
  private static _instance: ServiceTokenMixin
  private initM: ServiceTokenInitModel = {
    prefix: ''
  }

  public static shareInstance() {
    if (!this._instance) {
      this._instance = new ServiceTokenMixin()
    }
    return this._instance
  }

  //
  async refreshServiceToken(serviceId: string) {
    try {
      //ingress.apiendpoint
      await AuthModule.applyForService(serviceId)
      const servicerToken = AuthModule.servicerToken
      this.setServiceToken(servicerToken.token)
      this.setSelectedService(serviceId)
      await this.getServiceDetail(serviceId)
      return true
    } catch (e) {
      return false
    }
  }

  init(initM: ServiceTokenInitModel) {
    initM && (this.initM = initM)
  }

  async getServiceDetail(serviceId: string) {
    try {
      await ServicerModule.servicerListByIds([serviceId])
      this.serviceDetail = ServicerModule.servicerList[0]
      await this.loadCon()
    } catch (e) {
      console.log(e)
    }
  }

  async loadCon() {
    await AuthModule.getCurrentMicroContext()
    const context = AuthModule.currentContext
    if (context) {
      //内外 字段不一样，没法使用assing
      runtimeCon.businessContext.platformVersionId = context.platformVersionId
      runtimeCon.businessContext.platformId = context.platformId
      runtimeCon.businessContext.projectId = context.projectId
      runtimeCon.businessContext.subProjectId = context.subProjectId
      runtimeCon.businessContext.serviceProvider.serviceName = this.serviceDetail?.name
      runtimeCon.businessContext.serviceProvider.serviceId = context.servicerProvider?.servicerId
      runtimeCon.businessContext.serviceProvider.serviceType = context.servicerProvider?.servicerType
      runtimeCon.businessContext.serviceProvider.unitId = context.servicerProvider?.unitId
      runtimeCon.businessContext.user.accountId = context.userIdentity?.accountId
      runtimeCon.businessContext.user.accountType = context.userIdentity?.accountType
      runtimeCon.businessContext.user.rootAccountId = context.userIdentity?.rootAccountId
      runtimeCon.businessContext.user.userId = context.userIdentity?.userId
      runtimeCon.businessContext.dataRouter.dataPlatformVersionId = context.dataRouterIdentity?.dataPlatformVersionId
      runtimeCon.businessContext.dataRouter.dataProjectId = context.dataRouterIdentity?.dataProjectId
    }
  }

  async switchService(serviceId: string) {
    try {
      const nowServiceId = this.getSelectedService()
      if (!nowServiceId || serviceId == nowServiceId) {
        return false
      }
      await this.refreshServiceToken(serviceId)
      location.reload()
      return false
    } catch (e) {
      return false
    }
  }

  async firstLoad() {
    try {
      //   await AuthModule.getCurrentUserServicer()
      const result = await UserModule.queryUserFactory.queryManagerDetail.queryManagerDetail()
      FxConfig.userId = QueryManagerDetail.adminInfo.userInfo.userId
      const userServiceList = AuthModule.currentUserServicerList
      if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
        await QueryManagerDetail.changeAuthorizationUnitInfoList()
        FxConfig.distributorId = QueryManagerDetail.currentUnitInfo?.applicationMemberId
      }
      console.log('FxConfig', FxConfig)
      // const filterServiceList  = AuthModule.getStructuredCurrentUserServicerGroup
      let initSelectedServiceId = ''
      if (userServiceList.length) {
        initSelectedServiceId = userServiceList[0].id
        if (this.getSelectedService()) {
          const selectedServiceid = this.getSelectedService()
          const serviceModel = userServiceList.find((item: ServicerDto) => {
            return item.id == selectedServiceid
          })
          serviceModel && (initSelectedServiceId = serviceModel.id)
        }
        this.setSelectedService(initSelectedServiceId)
        await this.refreshServiceToken(initSelectedServiceId)
      } else {
        await this.loadCon()
      }
    } catch (e) {
      console.log(e)
    }
  }

  get ServiceDetail() {
    return this.serviceDetail
  }

  clearService() {
    try {
      this.removeStorage(ServiceTokenMixin.servicerTokenKey)
      this.removeStorage(ServiceTokenMixin.selectedServiceKey)
      this.serviceDetail = new ServicerDto()
    } catch (e) {
      console.log(e)
    }
  }

  getRealKey(key: string) {
    return this.initM.prefix + key
  }

  setStorage(key: string, value: string) {
    localStorage.setItem(this.getRealKey(key), value)
  }

  getStorage(key: string) {
    return localStorage.getItem(this.getRealKey(key)) || ''
  }

  removeStorage(key: string) {
    localStorage.removeItem(this.getRealKey(key))
  }

  setServiceToken(servicerToken: string) {
    this.setStorage(ServiceTokenMixin.servicerTokenKey, servicerToken)
  }

  getServiceToken() {
    return this.getStorage(ServiceTokenMixin.servicerTokenKey)
  }

  setSelectedService(serviceId: string) {
    this.setStorage(ServiceTokenMixin.selectedServiceKey, serviceId)
  }

  getSelectedService() {
    return this.getStorage(ServiceTokenMixin.selectedServiceKey)
  }
}
