import store from '@/store'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import QuestionCorrectionItem from './models/QuestionCorrectionItem'
import QuestionCorrectionGateway from '@api/gateway/QuestionCorrection-default'
import { Role, RoleType, Secure } from '@api/Secure'
import Attachment from './models/Attachment'
import QuestionCorrectionResult from './models/QuestionCorrectionResult'
import Response, { ResponseStatus } from '../../../Response'
import $http from '@packages/request'
import ConfigCenterModule from '@api/service/common/config-center/ConfigCenter'
import Question from '@api/service/common/models/exam/question/Question'

export class QuestionCorrectionQuery {
  /**
   * 试题题目
   */
  title?: string
  /**
   * 试题类型
   @see QuestionType
   */
  questionType: number
  /**
   * 分类id
   */
  categoryId?: string
  /**
   * 创建时间起
   */
  createTimeBegin?: string
  /**
   * 创建时间止
   */
  createTimeEnd?: string
}

export class QuestionCorrectionAdd {
  /**
   * 试题id
   */
  questionId?: string
  /**
   * 纠错分类id
   */
  categoryId?: string
  /**
   * 描述
   */
  describe?: string
  /**
   * 附件
   */
  attachments?: Array<Attachment>
}

interface IState {
  pageNo: number
  pageSize: number
  totalPageSize: number
  dataList: Array<QuestionCorrectionItem>
  correctionQuestionInfo: Array<Question>
  myCorrectionResults: Array<QuestionCorrectionResult> //试题纠错结果数组
  myCorrectionResultsNeedLoad: boolean
  questionCorrectionResults: Array<QuestionCorrectionResult>
}

@Module({ namespaced: true, store, dynamic: true, name: 'CustomerQuestionCorrectionModule' })
class QuestionCorrectionModule extends VuexModule implements IState {
  pageNo = 1
  pageSize = 10
  totalPageSize = 0
  dataList = new Array<QuestionCorrectionItem>()
  correctionQuestionInfo = new Array<any>()
  myCorrectionResults = new Array<QuestionCorrectionResult>()
  myCorrectionResultsNeedLoad = true
  questionCorrectionResults = new Array<QuestionCorrectionResult>()

  // 获取试题纠错分页信息
  @Action
  @Role([RoleType.user])
  async loadQuestionCorrectionPage(param: { pageNo: number; pageSize: number; query: QuestionCorrectionQuery }) {
    const response = await QuestionCorrectionGateway.findQuestionCorrectionPage({
      page: {
        pageNo: param.pageNo,
        pageSize: param.pageSize
      },
      query: param.query
    })
    if (response.status.isSuccess()) {
      this.setQuestionCorrectionPage(response.data)
    }

    return response.status
  }

  // 创建试题纠错信息
  @Action
  @Role([RoleType.user])
  async createQuestionCorrection(param: QuestionCorrectionAdd) {
    const response = await QuestionCorrectionGateway.createQuestionCorrection(param)
    this.setMyCorrectionResultsNeedLoad()
    await this.findMyQuestionCorrectionResult()
    return response.status
  }

  // 获取试题纠错分页信息
  @Action
  @Role([RoleType.user])
  async findMyQuestionCorrectionResult() {
    if (!this.myCorrectionResultsNeedLoad) {
      return new ResponseStatus(200)
    }
    const response = await QuestionCorrectionGateway.findMyQuestionCorrectionResult()

    if (response.status.isSuccess()) {
      this.setMyQuestionCorrectionResult(response.data)
    }

    return response.status
  }

  //获取试题纠错完成的试题结果
  @Action
  @Role([RoleType.user])
  async findQuestionCorrectionDone(questionIds: Array<string>) {
    const response = await QuestionCorrectionGateway.findQuestionCorrectionDone(questionIds)

    if (response.status.isSuccess()) {
      this.setQuestionCorrectionResult(response.data)
    }

    return response.status
  }

  // 获取试题纠错完毕后的内容
  @Action
  async loadCorrectionQuestionInfo(questionId: string) {
    const response: Response<any> = await $http.get(
      ConfigCenterModule.getIngressByName('ingress.exam') + '/gateway/question/v1/' + questionId
    )
    if (!response.status.isSuccess() || response.data.status === false) {
      return new ResponseStatus(500, response.data?.info || '获取试题失败')
    } else {
      this.replaceCorrectionQuestionInfo(response.data?.info)
    }
    return response.status
  }

  @Mutation
  private setQuestionCorrectionPage(payload: {
    pageSize: number
    pageNo: number
    totalPageSize: number
    totalSize: number
    currentPageData: Array<any>
  }) {
    this.pageNo = payload.pageNo
    this.pageSize = payload.pageSize
    this.totalPageSize = payload.totalPageSize
    this.dataList = payload.currentPageData
  }

  @Mutation
  private setMyQuestionCorrectionResult(results: Array<QuestionCorrectionResult>) {
    this.myCorrectionResults = results
    this.myCorrectionResultsNeedLoad = false
  }

  @Mutation
  private setMyCorrectionResultsNeedLoad() {
    this.myCorrectionResultsNeedLoad = true
  }

  @Mutation
  private setQuestionCorrectionResult(results: Array<QuestionCorrectionResult>) {
    this.questionCorrectionResults.filter(p => !results.some(result => result.questionId === p.questionId))
    this.questionCorrectionResults.push(...results)
  }

  @Mutation
  private replaceCorrectionQuestionInfo(question: any) {
    if (question.subQuestion) {
      question.subQuestionBase = question.subQuestion.map((p: any) => {
        return JSON.parse(p.questionJson)
      })
    }

    this.correctionQuestionInfo = this.correctionQuestionInfo.filter(p => p.id !== question.id)
    this.correctionQuestionInfo.push(question)
  }

  get getCorrectionQuestionInfo() {
    return (questionId: string) => {
      return this.correctionQuestionInfo.find(p => p.id === questionId)
    }
  }

  /**
   * 0：未纠错，1:我已纠错，2：试题已纠错
   */
  get getQuestionState() {
    return (questionId: string, onlyMe?: boolean) => {
      const myCorrection = this.myCorrectionResults.find(p => p.questionId === questionId)
      if (onlyMe === true) {
        return myCorrection?.status || 0
      }
      const correctionDone = this.questionCorrectionResults.find(p => p.questionId === questionId)
      return correctionDone?.status === 2 ? 2 : myCorrection ? 1 : 0
    }
  }
}

export default getModule(QuestionCorrectionModule)
