<route-meta>
{
"title": "学科选择器"
}
</route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="form-l"
    filterable
    clearable
  >
    <el-option
      v-for="item in studyPeriodOptions"
      :label="item.name"
      :value="item.propertyId"
      :key="item.propertyId"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import QuerySubject from '@api/service/common/basic-data-dictionary/query/QuerySubject'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

  @Component
  export default class extends Vue {
    selected = ''
    // 培训类别选项
    studyPeriodOptions = new Array<TrainingPropertyResponse>()

    @Prop({
      type: String,
      default: '请选培训类别'
    })
    placeholder: string

    //行业属性分类id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    // 学段id
    @Prop({
      type: String,
      default: ''
    })
    studyPeriodId: string

    @Prop({
      type: String,
      default: ''
    })
    value: string

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      this.$emit('updateSubject', this.selected)
      return this.selected
    }

    @Watch('studyPeriodId', {
      immediate: true,
      deep: true
    })
    async watchStudyPeriodId() {
      this.studyPeriodOptions = new Array<TrainingPropertyResponse>()
      await this.getTrainingCategoryOptions()
    }

    /**
     * 获取展示名称
     */
    get showLabel() {
      return (item: TrainingCategoryVo) => {
        return item.showName ? `${item.name}（${item.showName}）` : item.name
      }
    }

    /**
     * 获取培训类别
     */
    async getTrainingCategoryOptions() {
      if (!this.studyPeriodId) {
        this.studyPeriodOptions = []
        return
      }
      const res = await QuerySubject.querySubjectByIndustry(this.studyPeriodId)
      this.studyPeriodOptions = res
    }
  }
</script>
