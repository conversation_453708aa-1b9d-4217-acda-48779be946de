import {
  CommoditySkuResponse,
  SchemeResourceResponse,
  SubOrderInfoResponse1
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'

export default class RefundCommodity {
  /**
   * 商品id
   */
  commodityId = ''

  /**
   * 商品数量 （暂时废弃，给后续多商品使用）
   */
  quantity = 1

  /**
   * 退货数量 （外部转换）
   */
  refundQuantity = 0

  /**
   * 商品名称
   */
  commodityName = ''

  /**
   * 期别id
   */
  trainingPeriodId = ''

  /**
   * 期别名称
   */
  trainingPeriodName = ''

  /**
   * 期数
   */
  trainingPeriodNo = ''

  /**
   * 商品售价
   */
  price = 0

  /**
   * 商品原价
   */
  originalPrice = 0

  /**
   * 学时
   */
  period = 0

  /**
   * 方案id
   */
  schemeId = ''

  /**
   * 方案名称
   */
  schemeName = ''

  /**
   * 方案类型
   */
  schemeType: SchemeTypeEnum = undefined

  /**
   * sku
   */
  sku: SkuPropertyResponseVo = new SkuPropertyResponseVo()

  /**
   * 销售渠道
   */
  saleChannel: SaleChannelEnum = undefined

  /**
   * 销售渠道id
   */
  saleChannelId = ''

  /**
   * 销售渠道名称 （专题即为专题名称）
   */
  saleChannelName: string = undefined

  /**
   * 是否换班
   */
  exchanged = false
  /**
   * 是否换期
   */
  isExchangeIssue = false

  /**
   * 原商品id (换班信息通过外部转换)
   */
  originCommodityId = ''

  /**
   * 原商品名称 (换班信息通过外部转换)
   */
  originCommodityName = ''

  /**
   * 原期别id (换班信息通过外部转换)
   */
  originTrainingPeriodId = ''

  /**
   * 原期别名称 (换班信息通过外部转换)
   */
  originTrainingPeriodName = ''

  /**
   * 原期数 (换班信息通过外部转换)
   */
  originTrainingPeriodNo = ''

  /**
   * 是否使用优惠
   */
  useDiscount = false

  /**
   * 实付金额
   */
  payAmount = 0

  static from(dtoCommodity: CommoditySkuResponse, subOrderInfo: SubOrderInfoResponse1, sku: SkuPropertyResponseVo) {
    const commodity = new RefundCommodity()
    if (subOrderInfo.orderInfo) {
      commodity.saleChannel = subOrderInfo.orderInfo.saleChannel
      commodity.saleChannelName = subOrderInfo.orderInfo.saleChannelName
      commodity.saleChannelId = subOrderInfo.orderInfo.saleChannelId
      commodity.quantity = subOrderInfo.quantity
    }

    commodity.useDiscount = subOrderInfo.useDiscount
    commodity.exchanged = subOrderInfo.exchanged
    commodity.isExchangeIssue = subOrderInfo.isExchangeIssue
    commodity.payAmount = subOrderInfo.finalPrice
    commodity.commodityId = dtoCommodity.commoditySkuId
    commodity.commodityName = dtoCommodity.saleTitle
    commodity.price = dtoCommodity.price
    commodity.originalPrice = dtoCommodity.originalPrice

    if (dtoCommodity.issueInfo) {
      commodity.trainingPeriodId = dtoCommodity.issueInfo.issueId
      commodity.trainingPeriodName = dtoCommodity.issueInfo.issueName
      commodity.trainingPeriodNo = dtoCommodity.issueInfo.issueNum
    }

    const schemeResource = dtoCommodity.resource as SchemeResourceResponse
    commodity.period = schemeResource.period
    commodity.schemeId = schemeResource.schemeId
    commodity.schemeName = schemeResource.schemeName
    if (schemeResource.schemeType == 'chooseCourseLearning') {
      commodity.schemeType = SchemeTypeEnum.chooseCourseLearning
    } else if (schemeResource.schemeType == 'autonomousCourseLearning') {
      commodity.schemeType = SchemeTypeEnum.autonomousCourseLearning
    } else if (schemeResource.schemeType == 'trainingCooperation') {
      commodity.schemeType = SchemeTypeEnum.trainingCooperation
    }
    commodity.sku = sku || new SkuPropertyResponseVo()

    return commodity
  }
}
