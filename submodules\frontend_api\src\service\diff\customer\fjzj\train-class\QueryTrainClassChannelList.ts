import MsTradeQueryFrontGatewayCourseLearningForestage, {
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  Page,
  SchemeResourceResponse,
  CommoditySkuRequest as SkuCommodityRequest,
  SkuPropertyRequest,
  SortPolicy,
  UserPossessionInfoResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import SkuPropertyConvertUtils, {
  ComplexSkuPropertyListResponse,
  ComplexSkuPropertyResponse,
  SchemeSkuInfo
} from '@api/service/customer/train-class/Utils/SkuPropertyConvertUtils'
import { CommoditySkuRequest } from '@api/service/customer/train-class/query/vo/CommodityRequestVo'
import SkuPropertyVo from '@api/service/customer/train-class/query/vo/SkuPropertyVo'
import SkuVo from '@api/service/customer/train-class/query/vo/SkuVo'
import TrainClassCommodityVo from '@api/service/diff/customer/fjzj/train-class/model/TrainClassCommodityVo'
import { ResponseStatus } from '@hbfe/common'
import { cloneDeep } from 'lodash'
import MsLearningQueryFrontGatewayCourseLearningForestage, {
  LearningRegisterRequest,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import ThirdPartyItem from '@api/service/diff/customer/fjzj/train-class/model/ThirdPartyItem'
import QueryPlatformForestage from '@api/service/diff/common/fjzj/dictionary/QueryPlatformForestage'
import QueryTrainClassCommodityList from '@api/service/diff/customer/fjzj/train-class/QueryTrainClassCommodityList'
/**
 * 用户域专题获取培训班商品列表
 */
class QueryTrainClassChannelList {
  // region properties

  /**
   *总数目，类型为number
   */
  totalSize = 0
  // /**
  //  *sku过滤条件，类型为SkuPropertyVo
  //  */
  // filterSkuVo = new SkuPropertyVo()
  /**
   *培训班商品列表，类型为TrainClassCommodityVo[]
   */
  trainClassCommodityList: TrainClassCommodityVo[] = []
  /**
   *刷选条件数组，类型为SkuPropertyVo
   */
  skuProperties = new SkuPropertyVo()
  //当前选中的行业id
  currentInId = ''
  // endregion
  // region methods

  /**
   * 获取培训班列表
   */
  async queryTrainClassCommodityList(
    page: Page,
    filterCommodity: CommoditySkuRequest
  ): Promise<Array<TrainClassCommodityVo>> {
    const sortRequest = new CommoditySkuSortRequest()
    sortRequest.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequest.policy = SortPolicy.DESC
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageCommoditySkuTrainingChannelInServicer({
      page,
      queryRequest: filterCommodity,
      sortRequest: [sortRequest]
    })
    await QueryPlatformForestage.queryList()
    if (res.status.isSuccess()) {
      const tmpArr = []
      for (const item of res.data.currentPageData) {
        const tmpItem = new TrainClassCommodityVo()
        Object.assign(tmpItem, item)
        tmpItem.schemeId = (tmpItem.resource as SchemeResourceResponse).schemeId
        tmpItem.schemeType = (tmpItem.resource as SchemeResourceResponse).schemeType
        const tppTypeId = item?.tppTypeId
        if (tmpItem.schemeType == SchemeTypeEnum[SchemeTypeEnum.trainingCooperation] && tppTypeId) {
          tmpItem.thirdPartyInfo = new ThirdPartyItem()
          tmpItem.thirdPartyInfo.id = tppTypeId
          QueryPlatformForestage.list
          tmpItem.thirdPartyInfo.name = QueryPlatformForestage.map.get(tppTypeId)?.name
        }
        tmpArr.push(tmpItem)
      }
      const skuRequest = [] as SchemeSkuInfo[]
      tmpArr?.forEach((item) => {
        skuRequest.push(new SchemeSkuInfo(item.schemeId, item.skuProperty as ComplexSkuPropertyResponse))
      })
      const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(skuRequest)
      tmpArr?.forEach((item) => {
        const skuInfo = skuInfos.find((el) => el.id === item.schemeId)
        if (skuInfo) item.skuValueNameProperty = skuInfo.skuName
      })
      this.trainClassCommodityList = tmpArr
      this.totalSize = res.data.totalSize
    }
    return this.trainClassCommodityList
  }
  switchTrainCate(cate: SkuVo) {
    // if (cate.skuPropertyValueId) {
    this.skuProperties.filterTrainMajor(cate)
    // }
  }

  /**
   * 获取订单号，仅在已报名状态才能获取
   */
  async getOrderNo(possessionInfo: UserPossessionInfoResponse) {
    const request = new StudentSchemeLearningRequest()
    request.learningRegister = new LearningRegisterRequest()
    request.learningRegister.sourceType = possessionInfo.sourceType == 0 ? 'SUB_ORDER' : 'EXCHANGE_ORDER'
    request.learningRegister.sourceId = possessionInfo.sourceId
    const res = await MsLearningQueryFrontGatewayCourseLearningForestage.pageSchemeLearningInMyself({
      page: {
        pageSize: 1,
        pageNo: 1
      },
      request: request
    })
    if (res.status.isSuccess() && res.data.currentPageData && res.data.currentPageData.length) {
      return res.data.currentPageData[0].learningRegister.orderNo
    }
    return ''
  }

  /**
   * 获取培训班属性
   */
  async querySku(
    sku: SkuPropertyRequest = new SkuPropertyRequest(),
    isWeb?: boolean,
    needQuerySkuPropertyList?: Array<string>
  ): Promise<ResponseStatus> {
    if (sku.industry && sku.industry.length) {
      this.currentInId = sku.industry[0]
    }
    const sortRequest = new CommoditySkuSortRequest()
    sortRequest.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequest.policy = SortPolicy.DESC
    const queryParams = new SkuCommodityRequest()
    queryParams.skuPropertyRequest = cloneDeep(sku)
    delete queryParams.skuPropertyRequest.trainingChannelIds
    queryParams.trainingChannelIds = sku.trainingChannelIds
    queryParams.needQuerySkuPropertyList = needQuerySkuPropertyList
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.listSkuPropertyTrainingChannelInServicer(
      queryParams
    )
    if (res.status.isSuccess()) {
      try {
        if (res.data.industry && res.data.industry.length && res.data.industry.length == 1) {
          this.currentInId = res.data.industry[0].skuPropertyValueId
        }
        this.skuProperties = await SkuPropertyConvertUtils.convertToSkuPropertyVo(
          res.data as ComplexSkuPropertyListResponse,
          this.currentInId,
          isWeb
        )
      } catch (e) {
        console.log(e)
      }
    }
    return res.status
  }
}
export default QueryTrainClassChannelList
