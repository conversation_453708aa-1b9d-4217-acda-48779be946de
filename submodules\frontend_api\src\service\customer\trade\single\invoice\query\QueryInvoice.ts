/*
 * @Description: 发票查询
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:32:55
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-24 11:59:23
 */
import TradeQueryBackstage, {
  InvoiceAssociationInfoRequest,
  OfflineInvoiceRequest,
  OnlineInvoiceRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import InvoiceListResponse from '@api/service/customer/trade/single/invoice/query/vo/InvoiceListResponse'

import { Page } from '@hbfe/common'
import { OfflineInvoiceSortRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
import QueryOffLinePageInvoiceParam from './vo/QueryOffLinePageInvoiceParam'
import { InvoiceTypeEnum } from '../enum/InvoiceEnum'
export default class QueryInvoice {
  /**
   *  发票ID
   */
  private invoiceId: Array<string>
  private way: InvoiceTypeEnum
  //   page = new Page(1, 10)
  constructor(invoiceId: Array<string>, way: InvoiceTypeEnum) {
    this.invoiceId = invoiceId
    // this.page = typeof page === 'undefined' ? this.page : page
    this.way = way
  }
  /**
   * 查询发票详情
   * @returns InvoiceListResponse
   */
  async onLineGetInvoiceInServicer() {
    // if (typeof this.invoiceId === 'string') {
    if (this.way == InvoiceTypeEnum.OFFLINE) {
      const promiseArr = []
      const data: OffLinePageInvoiceVo[] = []
      for (let i = 0; i < this.invoiceId.length; i++) {
        const element = this.invoiceId[i]
        promiseArr.push(TradeQueryBackstage.getOfflineInvoiceInServicer(element))
      }
      await Promise.all(promiseArr).then(res => {
        for (let i = 0; i < res.length; i++) {
          const element = res[i]
          data.push(OffLinePageInvoiceVo.from(element.data))
        }
      })
      return data
    } else {
      const promiseArr = []
      const data: InvoiceListResponse[] = []
      for (let i = 0; i < this.invoiceId.length; i++) {
        const element = this.invoiceId[i]
        promiseArr.push(TradeQueryBackstage.getOnlineInvoiceInServicer(element))
      }
      await Promise.all(promiseArr).then(res => {
        for (let i = 0; i < res.length; i++) {
          const element = res[i]
          data.push(InvoiceListResponse.from(element.data))
        }
      })
      return data
    }
    // } else {
    //   this.page.pageSize = this.invoiceId.length
    //   if (this.way === InvoiceTypeEnum.ONLINE) {
    //     return await this.onLinePageInvoiceInServicer()
    //   } else {
    //     return await this.offLinePageInvoiceInServicer()
    //   }
    // }
  }
  //   /**
  //    * 分页查询发票 --- 线上 个人
  //    * @returns  Array<InvoiceListResponse>
  //    */
  //   private async onLinePageInvoiceInServicer(): Promise<Array<InvoiceListResponse>> {
  //     if (typeof this.invoiceId == 'string') {
  //       return
  //     }
  //     const request = new OnlineInvoiceRequest()
  //     request.basicData.invoiceType = 1 // 线上（电子发票）
  //     request.associationInfoList[0] = new InvoiceAssociationInfoRequest()
  //     request.associationInfoList[0].associationType = 0 // 关联订单类型：订单号
  //     request.invoiceIdList = this.invoiceId
  //     const params = {
  //       page: this.page,
  //       request
  //     }
  //     const result = await TradeQueryBackstage.pageOnlineInvoiceInServicer(params)
  //     this.page.totalSize = result.data.totalSize
  //     this.page.totalPageSize = result.data.totalPageSize
  //     const data = new Array<InvoiceListResponse>()
  //     for (let i = 0; i < result.data.currentPageData.length; i++) {
  //       const element = result.data.currentPageData[i]
  //       data.push(InvoiceListResponse.from(element))
  //     }
  //     return data
  //   }
  //   /**
  //    * 分页电子查询发票 --- 线下 个人
  //    * @param page 页数
  //    * @param QueryOffLinePageInvoiceParam 查询参数
  //    * @param sort 根据创建时间进行排序
  //    * @returns  Array<OffLinePageInvoiceVo>
  //    */
  //   private async offLinePageInvoiceInServicer(): Promise<Array<OffLinePageInvoiceVo>> {
  //     if (typeof this.invoiceId == 'string') {
  //       return
  //     }
  //     const request = new OfflineInvoiceRequest()
  //     request.invoiceIdList = this.invoiceId
  //     request.associationInfo.associationType = 0
  //     request.basicData.invoiceTypeList = [2]
  //     const params = {
  //       page: this.page,
  //       request
  //     }
  //     const result = await TradeQueryBackstage.pageOfflineInvoiceInServicer(params)
  //     this.page.totalSize = result.data.totalSize
  //     this.page.totalPageSize = result.data.totalPageSize
  //     const data = new Array<OffLinePageInvoiceVo>()
  //     result.data.currentPageData.forEach(item => {
  //       data.push(OffLinePageInvoiceVo.from(item))
  //     })
  //     return data
  //   }
}
