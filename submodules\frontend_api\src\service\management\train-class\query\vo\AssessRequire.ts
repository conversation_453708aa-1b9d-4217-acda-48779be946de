/**
 * @description 考核要求
 */
class AssessRequire {
  /**
   * 是否配置课程
   */
  hasConfigCourse = false

  /**
   * 整体考核要求学时
   */
  totalPeriod = 0

  /**
   * 是否配置课程测验
   */
  hasConfigCourseQuiz = false

  /**
   * 课程测验是否纳入考核
   */
  courseQuizPagerStandard = false

  /**
   * 最低参加课程验要求课程学习进度，仅当配置课程测验且课程测验纳入考核时有效
   */
  courseQuizMinCourseSchedule = 0

  /**
   * 课程测验及格分，仅当配置课程测验且课程测验纳入考核时有效
   */
  courseQuizPassScore = 0

  /**
   * 是否开放课程评价
   */
  enableAppraisal = false

  /**
   * 是否强制要求评价，仅当配置课程且开放课程评价时有效
   */
  enableCompulsoryAppraisal = false

  /**
   * 是否配置考试
   */
  hasConfigExam = false

  /**
   * 考试合格要求分数，仅当配置考试时有效
   */
  examPassScore = 0
  /**
   * 考试是否纳入考核
   */
  isExamAssessed = false
}

export default AssessRequire
