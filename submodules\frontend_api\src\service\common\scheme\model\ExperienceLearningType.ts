import LearningTypeBase from '@api/service/common/scheme/model/LearningTypeBase'
import ExperienceDetail from '@api/service/common/scheme/model/ExperienceDetail'
import { ExperienceTypeEnum } from '@api/service/common/scheme/enum/ExperienceType'

/**
 * @description 学习心得学习方式
 */
class ExperienceLearningType extends LearningTypeBase {
  /**
   * 心得名称
   */
  experienceName = ''
  /**
   * 课程心得前置条件 true直接参加
   */
  courseCondition = true
  /**
   * 班级心得前置条件 true直接参加
   */
  classCondition = true
  /**
   * 整体要求
   */
  joinCount = 0
  /**
   * 成绩要求
   */
  score = 0
  /**
   * 是否纳入考核
   */
  isExamine = false
  /**
   * 心得列表
   */
  experienceList: ExperienceDetail[] = []
  /**
   * 心得列表-备份
   * @description 操作必选使用，使用前init
   */
  experienceListClone: ExperienceDetail[] = []
  /**
   * 班级心得列表
   */
  get classExperienceList() {
    return this.experienceList.filter((item) => item.experienceType.equal(ExperienceTypeEnum.class_experience))
  }
  /**
   * 课程心得列表
   */
  get courseExperienceList() {
    return this.experienceList.filter((item) => item.experienceType.equal(ExperienceTypeEnum.course_experience))
  }
  /**
   * 必选数量
   */
  get requireCount() {
    return this.experienceList.filter((item) => item.isRequired).length
  }
}

export default ExperienceLearningType
