<template>
  <jxgx-invoice-distribution ref="invoiceDistribution"></jxgx-invoice-distribution>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import InvoiceDistribution from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/invoice-distribution.vue'
  import TradeExport from '@api/service/diff/management/jxgx/trade/TradeExport'

  @Component
  class JxgxInvoiceDistribution extends InvoiceDistribution {
    tradeExport = new TradeExport()
    // 导出信息
    async exportFile() {
      this.exportLoading = true
      let res
      if (this.isZtlogin) {
        res = await this.queryZtInvoice.exportPageDeliveryInvoice(this.deliveryInvoiceParam)
      } else {
        res = await this.tradeExport.exportPageDeliveryInvoice(this.deliveryInvoiceParam)
      }
      if (res) {
        this.$message.success('导出成功')
        this.exportSuccessVisible = true
      } else {
        this.$message.warning('导出失败')
      }
      this.exportLoading = false
    }
  }
  @Component({
    components: {
      JxgxInvoiceDistribution
    }
  })
  export default class extends Vue {
    @Ref('invoice-distribution') invoiceDistribution: JxgxInvoiceDistribution
    // 列表请求
    doQueryPageZt() {
      this.invoiceDistribution.doQueryPageZt()
    }
    // 列表请求
    doQueryPage() {
      this.invoiceDistribution.doQueryPage()
    }
  }
</script>
<style scoped></style>
