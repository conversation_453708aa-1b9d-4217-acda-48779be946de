import axios from 'axios'

export interface HeaderObj {
  [key: string]: string
}

/**
 * 请求文件微服务时，如果是保护类型文件，需要带上部分请求头参数
 * @description 下载器 - 以文件流形式下载
 */
class Downloader {
  url: string
  fileName: string
  header: HeaderObj

  /**
   * @param url 下载地址
   * @param fileName 下载的文件名
   * @param header 请求头配置
   */
  constructor(url: string, fileName: string, header: HeaderObj) {
    this.url = url
    this.fileName = fileName
    this.header = header
  }

  /**
   * 文件流形式下载
   */
  async download() {
    const request = await axios({
      method: 'get',
      url: this.url,
      headers: this.header,
      responseType: 'blob'
    })
    if (!request.data) {
      // 下载文件失败，不存在文件
      return false
    }
    const type = this.getMimeType(this.url)
    const blob = new Blob([request.data], {
      type: type
    })
    const downloadElement = document.createElement('a')
    const href = window.URL.createObjectURL(blob)
    downloadElement.style.display = 'none'
    downloadElement.href = href
    downloadElement.download = this.fileName //下载后文件名
    document.body.appendChild(downloadElement)
    downloadElement.click() //点击下载
    document.body.removeChild(downloadElement) //下载完成移除元素
    window.URL.revokeObjectURL(href) //释放掉blob对象
    return true
  }

  private getMimeType(filename: string) {
    const ext = filename
      .split('.')
      .pop()
      .toLowerCase()
    switch (ext) {
      case 'doc':
      case 'docx':
        return 'application/vnd.openxmlformats-officedocument.wordprocessingml.document;charset=utf-8'
      case 'xlsx':
      case 'xls':
        return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8'
      case 'pdf':
        return 'application/pdf;charset=UTF-8'
      case 'xml':
        return 'application/xml'
      default:
        return 'application/octet-stream'
    }
  }
}

export default Downloader
