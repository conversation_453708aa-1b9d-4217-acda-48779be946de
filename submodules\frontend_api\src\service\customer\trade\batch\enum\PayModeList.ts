import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description
 */
export enum PayModeEnum {
  // 1：支付宝
  ALI_PAY = 1,
  // 2：微信
  WX_PAY,
  // 3：线下转账汇款
  OFFLINE
}

/**
 * @description
 */
class PayModeList extends AbstractEnum<PayModeEnum> {
  static enum = PayModeEnum
  constructor(status?: PayModeEnum) {
    super()
    this.current = status
    this.map.set(PayModeEnum.ALI_PAY, '支付宝')
    this.map.set(PayModeEnum.WX_PAY, '微信')
    this.map.set(PayModeEnum.OFFLINE, '线下转账汇款')
  }
}

export default new PayModeList()
