import { Response } from '@hbfe/common'
import MsLearningscheme from '@api/ms-gateway/ms-learningscheme-v1'

export default class BatchUpdateScheme {
  /**
   * 模板地址
   */
  templateUrl = ''
  /**
   * 文件路径
   */
  filePath = ''
  /**
   * 文件名
   */
  fileName = ''

  /**
   * 获取模板地址
   */
  async getTemplateUrl() {
    const { status, data } = await MsLearningscheme.getImportUpdateTemplateUrl()
    if (status.isSuccess()) {
      this.templateUrl = data
    }
    return status
  }

  /**
   * 上传批量修改表格
   */
  async batchUpdateScheme() {
    const result = new Response<string>()
    const { status, data } = await MsLearningscheme.batchUpdateLearningScheme({
      filePath: this.filePath,
      fileName: this.fileName || undefined
    })
    if (status.isSuccess()) {
      result.data = data
    }
    return status
  }
}
