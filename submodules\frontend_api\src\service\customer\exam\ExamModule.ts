import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import QueryExamFactory from '@api/service/customer/exam/QueryExamFactory'
import MutationExamFactory from '@api/service/customer/exam/MutationExamFactory'

/**
 * 考试中控层
 */
@Module({
  name: 'ExamModule',
  dynamic: true,
  namespaced: true,
  store
})
class ExamModule extends VuexModule {
  // region properties

  /**
   *考试查询工厂类，类型为QueryExamFactory
   */
  queryExamFactory = new QueryExamFactory()
  /**
   *考试业务工厂类，类型为MutationExamFactory
   */
  mutationExamFactory = new MutationExamFactory()
  // endregion
  // region methods

  // endregion
}
export default getModule(ExamModule)
