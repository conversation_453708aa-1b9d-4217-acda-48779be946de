import { CertificateModelEnum } from '@api/service/common/enums/personal-leaning/CertificateModelTypes'
import CertificateModelVo from './CertificateModelVo'
import JsCertificateModelVo from './JsCertificateModelVo'
import RsCertificateModelVo from './RsCertificateModelVo'

/*
 * 人员信息
 */
class UserInfoVo {
  /**
   * 用户ID
   */
  userId = ''
  /* 
    姓名
  */
  userName = ''
  /**
   *  证书技术工种：
   */
  jobCategoryName = ''
  /**
   * 证书技术等级：
   */
  professionalLevelName = ''
  /**
   * 年度：
   */
  trainingYear = ''
  /* 
    证件号
  */
  idCard = ''
  /* 
    培训专业
  */
  trainingProfession = ''
  /* 
    证明类型
  */
  type: CertificateModelEnum = undefined
  /* 
    科目类型
  */
  subjectType: string = undefined
  /* 
    培训类别
  */
  trainingCategory: string = undefined

  from(item: CertificateModelVo) {
    if (item.type === CertificateModelEnum.JXJY_JS) {
      // 建设
      const jsTemp = item as JsCertificateModelVo
      this.trainingCategory = jsTemp.trainingCategory
      this.userName = jsTemp.userName
      this.idCard = jsTemp.idCard
      this.trainingProfession = jsTemp.trainingProfession
      this.type = jsTemp.type
    } else {
      // 人设
      const rsTemp = item as RsCertificateModelVo
      this.subjectType = rsTemp.subjectType
      this.userName = rsTemp.userName
      this.idCard = rsTemp.idCard
      this.trainingProfession = rsTemp.trainingProfession
      this.type = rsTemp.type
    }
  }
}

export default UserInfoVo
