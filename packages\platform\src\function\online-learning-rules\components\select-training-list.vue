<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 16:12:23
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2025-05-12 10:42:58
 * @Description: 选择培训方案-添加特殊规则专用
-->
<template>
  <el-drawer
    title="选择培训方案"
    :wrapperClosable="false"
    :close-on-press-escape="false"
    :visible.sync="openDialog"
    direction="rtl"
    size="1000px"
    custom-class="m-drawer"
  >
    <div class="drawer-bd">
      <el-row :gutter="16" class="m-query f-mt10">
        <el-form :inline="true" label-width="100px">
          <el-col :span="8">
            <el-form-item label="培训方案形式">
              <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="培训方案名称">
              <el-input clearable placeholder="请输入培训方案名称" v-model="queryParams.schemeName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button @click="search" type="primary">查询</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table stripe :data="allSchemeList" class="m-table" ref="schemeList" v-loading="loading">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="培训方案名称" min-width="280">
          <template slot-scope="scope">{{ scope.row.schemeName }}</template>
        </el-table-column>
        <el-table-column label="属性" min-width="220">
          <template slot-scope="scope">
            <sku-display :sku-item="scope.row"></sku-display>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="changeCheckStatus(scope.row)">
              {{ isChecked(scope.row) ? '取消选择' : '选择' }}
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
      <div class="m-btn-bar f-tc f-mt20">
        <el-button @click="openDialog = false">取消</el-button>
        <el-button @click="confirm" type="primary">确定</el-button>
      </div>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Prop } from 'vue-property-decorator'
  import BasicInfo from '@api/service/management/online-learning-rule/model/BasicInfo'
  import RuleSchemeParams from '@api/service/management/train-class/query/vo/RuleSchemeParams'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import { UiPage } from '@hbfe/common'
  import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
  import SkuDisplay from '@hbfe/jxjy-admin-platform/src/function/components/skuDisplay.vue'
  import { cloneDeep } from 'lodash'
  import {
    CommoditySkuRequest,
    SchemeRequest,
    SkuPropertyRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import CommonSchemeListModel from '@hbfe/jxjy-admin-platform/src/function/online-learning-rules/components/CommonSchemeListModel'
  import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'

  @Component({ components: { SkuDisplay } })
  export default class extends Vue {
    /**
     * 基础配置信息（获取当前sku相关内容）
     */
    @Prop({
      type: BasicInfo,
      default: () => new BasicInfo()
    })
    basicInfo: BasicInfo

    @Prop({
      type: Array,
      default: () => new Array<RuleSchemeItem>()
    })
    infoSelectList: Array<RuleSchemeItem>
    /**
     * 打开弹窗
     */
    openDialog = false

    /**
     * 所有方案列表
     */
    allSchemeList = new Array<RuleSchemeItem>()

    /**
     * 选中的方案列表
     */
    selectSchemeList = new Array<RuleSchemeItem>()

    /**
     * 培训方案类型（形式）
     */
    schemeTypeInfo: Array<string> = new Array<string>()

    /**
     * 查询参数
     */
    queryParams = new RuleSchemeParams()

    /**
     * 查询方案列表方法
     */
    queryTrainClassCommodityList = new QueryTrainClassCommodityList()

    /**
     * 方案存储
     */
    commonSchemeListModel = CommonSchemeListModel

    loading = false

    /**
     * 分页
     */
    page: UiPage

    // 行业枚举
    IndustryIdEnum = IndustryIdEnum

    // 是否选中
    get isChecked() {
      return (item: RuleSchemeItem) => {
        const res = this.selectSchemeList.map((scheme) => scheme.schemeId).indexOf(item.schemeId) > -1
        return res
      }
    }
    constructor() {
      super()
      this.page = new UiPage(this.querySchemeList, this.querySchemeList)
    }
    /**
     * 初始化抽屉
     */
    async init() {
      this.openDialog = true
      this.page.pageNo = 1
      this.selectSchemeList = cloneDeep(this.infoSelectList)
      await this.querySchemeList()
    }

    /**
     * 查询培训方案
     */
    async querySchemeList() {
      this.loading = true
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.queryParams.trainType = undefined
      }
      this.queryParams.trainType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      const commoditySkuRequest = new CommoditySkuRequest()
      commoditySkuRequest.schemeRequest = new SchemeRequest()
      commoditySkuRequest.schemeRequest.schemeName = this.queryParams?.schemeName || ''
      commoditySkuRequest.schemeRequest.schemeType = this.queryParams.trainType
      commoditySkuRequest.fixSkuPropertyRequest = new Array<SkuPropertyRequest>()
      // 处理sku入参，根据对应行业存入数据
      commoditySkuRequest.fixSkuPropertyRequest
      const rsSkuPropertyRequest = new SkuPropertyRequest()
      const jsSkuPropertyRequest = new SkuPropertyRequest()
      const wsSkuPropertyRequest = new SkuPropertyRequest()
      const gqSkuPropertyRequest = new SkuPropertyRequest()
      const lsSkuPropertyRequest = new SkuPropertyRequest()
      const ysSkuPropertyRequest = new SkuPropertyRequest()
      const industryConfig = {
        RS: {
          properties: ['year', 'subjectType', 'trainingProfessional'],
          requests: rsSkuPropertyRequest
        },
        JS: {
          properties: ['year', 'subjectType', 'trainingCategory'],
          requests: jsSkuPropertyRequest
        },
        WS: {
          properties: ['year', 'trainingCategory', 'trainingObject', 'positionCategory'],
          requests: wsSkuPropertyRequest
        },
        GQ: {
          properties: ['year', 'jobLevel'],
          requests: gqSkuPropertyRequest
        },
        LS: {
          properties: ['year', 'learningPhase', 'discipline'],
          requests: lsSkuPropertyRequest
        },
        YS: {
          properties: ['year', 'subjectType'],
          requests: ysSkuPropertyRequest
        }
      }
      for (const [industryKey, config] of Object.entries(industryConfig)) {
        const industryId = IndustryIdEnum[industryKey as keyof typeof IndustryIdEnum]
        if (this.hasIndustry(industryId)) {
          config.requests.industry = [industryId]
          for (const property of config.properties) {
            if (this.basicInfo[industryKey + 'Property'][property][0] !== '-1') {
              config.requests[property] = this.basicInfo[industryKey + 'Property'][property]
            }
          }
          commoditySkuRequest.fixSkuPropertyRequest.push(config.requests)
        }
      }
      // 不包含方案id列表
      const noIncludeSchemeIds = this.commonSchemeListModel.noIncludeSchemeList.map((item) => item.schemeId)
      // 特殊规则已选方案id
      const specialSchemeIds = this.commonSchemeListModel.schemeSpecialRuleList
        .map((item) => item.schemeList.map((ite) => ite.schemeId))
        .flat(1)
      // 需要排除的方案id
      commoditySkuRequest.schemeRequest.excludedSchemeIdList = [...noIncludeSchemeIds, ...specialSchemeIds]
      try {
        const res = await this.queryTrainClassCommodityList.queryTrainClassCommodityList(this.page, commoditySkuRequest)
        this.allSchemeList = res.map((item) => {
          return this.queryTrainClassCommodityList.fromRuleSchemeItem(item)
        })
        ;(this.$refs['schemeList'] as any)?.doLayout()
      } catch (error) {
        this.$message.error('系统异常')
      }
      this.loading = false
    }

    /**
     * 查询
     */
    async search() {
      this.page.pageNo = 1
      this.querySchemeList()
    }

    /**
     * 选择方案
     */
    changeCheckStatus(item: RuleSchemeItem) {
      let index: number
      this.selectSchemeList.map((ite, id) => {
        if (ite.schemeId == item.schemeId) {
          index = id
        }
      })

      if (index > -1) {
        this.selectSchemeList.splice(index, 1)
      } else {
        this.selectSchemeList.push(item)
      }
    }

    /**
     * 确认选择
     */
    confirm() {
      this.$emit('selectList', this.selectSchemeList)
      this.openDialog = false
    }

    // 查找行业数组里是否包含对应行业
    get hasIndustry() {
      return (id: IndustryIdEnum) => {
        return this.basicInfo.industryIdList.find((industry) => industry.indexOf(id) != -1)
      }
    }
  }
</script>
