"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""安徽住建综合平台单点登录演示
		@param request
		@return
	"""
	studentLogin(request:StudentLoginRequest):StudentLoginResponse @optionalLogin
}
input StudentLoginRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.ahzjwx.v1.kernel.geteway.request.StudentLoginRequest") {
	"""学员信息key【必填】"""
	studentInfoKey:String
}
type StudentLoginResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.ahzjwx.v1.kernel.geteway.response.StudentLoginResponse") {
	"""状态码"""
	code:String
	"""响应信息"""
	message:String
	"""单点登录token"""
	loginToken:String
	"""用户id【8.0继续教育通用平台】"""
	userId:String
	accountId:String
}

scalar List
