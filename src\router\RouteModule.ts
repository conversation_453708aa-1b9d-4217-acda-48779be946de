/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-10-19 09:50:42
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-10-19 11:58:39
 * @Description:
 */
import { getModule, Module, VuexModule, Mutation, Action } from 'vuex-module-decorators'
import store from '@/store'
import { Route } from 'vue-router'

type RouteObject = {
  [key: string]: Route[]
}
@Module({
  name: 'RouteModule',
  dynamic: true,
  namespaced: true,
  store
})
/**
 * ui交互用的状态层
 */
class RouteModule extends VuexModule {
  /**
   * 通用页面面包屑内容，如果存在数据面包屑先渲染该数据
   */
  breadCrumbList: RouteObject = {}
  /**
   * 设置breadCrumbList值
   * @param key key值 路由的name属性
   * @param value 路由信息
   */
  @Mutation
  setBreadCrumbList(params: { key: string; value: Route[] }) {
    this.breadCrumbList[params.key] = params.value
    const temp2 = this.breadCrumbList[params.key]
    const newBread: Route[] = []
    for (let i = 0; i < temp2.length; i++) {
      const element = temp2[i]
      console.log(element.meta.title, 'title')
      newBread.push({
        path: element.path,
        params: element.params,
        query: element.query,
        meta: {
          title: element.meta.title ? element.meta.title : '返回上一个页面'
        },
        hash: element.hash,
        fullPath: element.fullPath,
        matched: []
      })
    }
    localStorage.setItem('breadCrumbList-' + params.key, JSON.stringify(newBread))
  }
  /**
   * 清除breadCrumbList数据
   * @param key key值 路由的name属性
   */
  @Mutation
  removeBreadCrumbList(key: string) {
    this.breadCrumbList[key] = []
    localStorage.removeItem('breadCrumbList-' + key)
  }
}

export default getModule(RouteModule)
