<route-params content="/:id"></route-params>
<template>
  <el-main>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/resource/exam-paper')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/resource/exam-paper' }">试卷管理</el-breadcrumb-item>
      <el-breadcrumb-item>修改试卷</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!--第一步-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <el-row type="flex" justify="center">
          <el-col :sm="20" :lg="12">
            <el-steps :active="currentStep" align-center class="m-steps f-pt40 f-pb10">
              <el-step title="填写基础信息"></el-step>
              <el-step title="配置试题"></el-step>
              <el-step title="创建成功"></el-step>
            </el-steps>
          </el-col>
        </el-row>
        <el-divider v-show="currentStep != 2" class="m-divider"></el-divider>

        <step-1
          v-if="currentStep == 1"
          :create-exampaper-info.sync="updateExamPaperObject.examPaperParams"
          :is-modify="true"
          @getCurrentStep="getCurrentStep"
          @toFirstCategoryName="toFirstCategoryName"
          @commitDraft="commitDraft"
        ></step-1>
        <step-2
          v-if="currentStep == 2"
          :create-exampaper-info.sync="updateExamPaperObject.examPaperParams"
          :is-modify="true"
          @BackFirstStep="BackFirstStep"
          @thirdCommit="thirdCommit"
          @commitDraft="commitDraft"
        ></step-2>
        <step-3 v-if="currentStep == 3"></step-3>
      </el-card>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import Step1 from '@hbfe/jxjy-admin-examPaper/src/add/step-1.vue'
  import Step2 from '@hbfe/jxjy-admin-examPaper/src/add/step-2.vue'
  import Step3 from '@hbfe/jxjy-admin-examPaper/src/add/step-3.vue'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import UpdateExamPaper from '@api/service/management/resource/exam-paper/mutation/UpdateExamPaper'
  import { cloneDeep } from 'lodash'
  import UpdateExamPaperVo from '@api/service/management/resource/exam-paper/mutation/vo/update/UpdateExamPaperVo'

  @Component({
    components: { Step1, Step2, Step3 }
  })
  export default class extends Vue {
    currentStep = 0
    id = ''
    //获取修改试卷的实例对象
    updateExamPaperObject: UpdateExamPaper = new UpdateExamPaper('')
    //修改试卷的入参

    categoryName = ''

    saveExam() {
      this.currentStep = 3
    }
    async created() {
      this.id = this.$route.params.id
      this.updateExamPaperObject = ResourceModule.mutationExamPaperFactory.getUpdateExamPaper(this.id)
      const res = await this.updateExamPaperObject.doQueryExamPaper()
      this.updateExamPaperObject.examPaperParams

      console.log(cloneDeep(this.updateExamPaperObject.examPaperParams), '1122')
      this.currentStep = 1
    }
    getCurrentStep(value: any) {
      this.currentStep = value
    }
    BackFirstStep(value: any) {
      this.currentStep = value
    }
    async toFirstCategoryName(value: any) {
      if (value) {
        this.categoryName = value
        console.log(this.categoryName, '123123123')
      }
    }
    async thirdCommit(value: any) {
      if (value) {
        this.updateExamPaperObject.examPaperParams.isDraft =2
        this.updateExamPaperObject.examPaperParams.status =1
        const res = await this.updateExamPaperObject.doUpdateExamPaper()
        if (res.isSuccess()) {
          this.currentStep = 3
        } else {
          this.$message.error(res.errors[0].message)
        }
      }
    }
    async commitDraft(value: any) {
      if (value) {
        this.updateExamPaperObject.examPaperParams.isDraft =1
        this.updateExamPaperObject.examPaperParams.status =2




        const res = await this.updateExamPaperObject.doUpdateExamPaper()
        if (res.isSuccess()) {
          this.$message.success('保存草稿成功')
          setTimeout(() => {
            this.$router.push('/resource/exam-paper')
          }, 200)
        }
      }
    }
  }
</script>
