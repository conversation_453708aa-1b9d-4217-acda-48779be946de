import ChooseCourseGateway, { ChooseCourseRequest } from '@api/ms-gateway/ms-choose-course-v1'
import { ChooseCourseInfoRequest } from '@api/ms-gateway/ms-choose-course-v1'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import { ResponseStatus } from '@hbfe/common'
import CourseDetail from './module/CourseDetail'
import TreeItem from './module/TreeItem'
import { Description, upMyLog } from '@hbfe/jxjy-customer-common/src/monitor/WebfunnyUpMyLog'
export class SelectCourse {
  /**
   * 节点ID
   */
  outlineId: string = undefined
  /**
   * 节点名称
   */
  outlineName: string = undefined
  /**
   * 父节点ID
   */
  fatherOutlineId: string = undefined
  /**
   * 父节点名称
   */
  fatherOutlineName: string = undefined
  /**
   * 已选课程
   */
  courseList: CourseDetail[] = []
  /**
   * 节点要求学时
   */
  configPeriod = 0
  /**
   * 学中课程的学时
   */
  get coursePeriodCount() {
    let count = 0
    this.courseList.map(item => (count = CalculatorObj.add(count, item.period)))
    count = Math.min(this.configPeriod, count)
    return count
  }
  get residuePeriod() {
    return CalculatorObj.subtract(this.configPeriod, this.coursePeriodCount)
  }
}
/**
 * 二级分类学时模型
 */
export class LevelTwoPeriod {
  /**
   * 节点ID
   */
  outlineId: string = undefined
  /**
   * 节点名称
   */
  outlineName: string = undefined
  /**
   * 节点要求学时
   */
  coursePeriod = 0
  /**
   * 节点已选学时--已提交
   */
  selectCourse = 0
  /**
   * 节点已选学时--未提交
   */
  selectCourseNoSumbit = 0
  /**
   * 剩余学时
   */
  get coursePeriodCount() {
    const count = CalculatorObj.subtract(this.coursePeriod, this.selectCourse)
    return CalculatorObj.subtract(count, this.selectCourseNoSumbit)
  }
}
/**
 * 选中数据
 */
export default class SelectModule {
  /**
   * 节点映射选中数组
   */
  private outlineIdToCourseMap: Map<string, SelectCourse> = new Map()
  /**
   * 选中的数组 -- 列表展示
   */
  isSelect: SelectCourse[] = []
  /**
   * 选课清单
   */
  selectCheckList: SelectCourse[] = []
  /**
   * 是否不计算二级学时
   */
  isSecondElectiveMaxPeriod: boolean = undefined
  /**
   * 学中课程的总学时
   */
  get coursePeriodCount() {
    let count = 0
    this.selectCheckList.forEach(item => {
      count = CalculatorObj.add(count, item.coursePeriodCount)
    })
    return count
  }
  /**
   * 节点映射的信息
   */
  treeItemMap: Map<string, TreeItem>
  /**
   * 选课token
   */
  token = ''
  /**
   * 本次总需要学时
   */
  needPeriod = 0
  /**
   * 节点映射的信息
   */
  outlineIdTimeMap: Map<string, number>
  /**
   * @param treeItemMap
   * @param token 选课token
   * @param isSecondElectiveMaxPeriod 不计算二级学时
   */
  constructor(
    treeItemMap: Map<string, TreeItem>,
    token: string,
    isSecondElectiveMaxPeriod?: boolean,
    outlineIdTimeMap?: Map<string, number>
  ) {
    this.treeItemMap = treeItemMap
    this.token = token
    this.isSecondElectiveMaxPeriod = isSecondElectiveMaxPeriod
    this.outlineIdTimeMap = outlineIdTimeMap
    this.initSelectCheckList()
  }

  /**
   * 二级分类要求学时
   */
  secondElectiveMaxPeriod(): LevelTwoPeriod[] {
    // 先提取二级分类和他的孩子节点
    const keys = [...this.treeItemMap.keys()]
    const levelTwoMap: Map<string, TreeItem> = new Map()
    const result: Map<string, LevelTwoPeriod> = new Map()
    keys.forEach(item => {
      if (this.treeItemMap.get(item).level === 2) {
        levelTwoMap.set(item, this.treeItemMap.get(item))
        const levelTwoPeriod = new LevelTwoPeriod()
        levelTwoPeriod.outlineId = this.treeItemMap.get(item).outlineId
        levelTwoPeriod.outlineName = this.treeItemMap.get(item).name
        if (
          this.outlineIdTimeMap?.get(this.treeItemMap.get(item).outlineId) ||
          this.outlineIdTimeMap?.get(this.treeItemMap.get(item).outlineId) == 0
        ) {
          levelTwoPeriod.coursePeriod = this.outlineIdTimeMap?.get(this.treeItemMap.get(item).outlineId)
        } else {
          levelTwoPeriod.coursePeriod = this.treeItemMap.get(item).courseConfig
        }
        // levelTwoPeriod.coursePeriod = this.treeItemMap.get(item).courseConfig
        levelTwoPeriod.selectCourse = this.treeItemMap.get(item).selectCourseSumbit
        levelTwoPeriod.selectCourseNoSumbit = 0
        result.set(item, levelTwoPeriod)
      }
    })

    this.selectCheckList.forEach(item => {
      const temp = levelTwoMap.get(item.outlineId)
      if (temp) {
        // 如果存在说明是在二级目录
        const levelTwoPeriod = new LevelTwoPeriod()
        levelTwoPeriod.outlineId = temp.outlineId
        levelTwoPeriod.outlineName = temp.name
        if (this.outlineIdTimeMap?.get(temp.outlineId) || this.outlineIdTimeMap?.get(temp.outlineId) == 0) {
          levelTwoPeriod.coursePeriod = this.outlineIdTimeMap?.get(temp.outlineId)
        } else {
          levelTwoPeriod.coursePeriod = temp.courseConfig
        }
        // levelTwoPeriod.coursePeriod = temp.courseConfig
        levelTwoPeriod.selectCourse = temp.selectCourseSumbit
        levelTwoPeriod.selectCourseNoSumbit = item.coursePeriodCount
        result.set(item.outlineId, levelTwoPeriod)
      } else {
        // 如果不存在说明是三级目录 要找父级设置 然后要经过父去算子集集合学时
        // 解决报错
        if (!item.fatherOutlineId) return
        const temp = levelTwoMap.get(item.fatherOutlineId)
        const resultItem = result.get(item.fatherOutlineId) ? result.get(item.fatherOutlineId) : new LevelTwoPeriod()
        const childrenTree = result.get(temp.outlineId)
        const tree = this.treeItemMap.get(item.fatherOutlineId)
        let count = 0

        tree.childrenTreeItem.forEach(vo => {
          if (vo.outlineId.includes('otherCourse') && tree.outlineId === vo.fatherOutlineId) {
            vo.gatherTreeItem.forEach(vo => {
              count = CalculatorObj.add(count, vo.selectCourseSumbit)
            })
          }
          count += vo.selectCourseSumbit
        })
        // const levelTwoPeriod = new LevelTwoPeriod()
        // levelTwoPeriod.outlineId = temp.outlineId
        // levelTwoPeriod.outlineName = temp.name
        // levelTwoPeriod.coursePeriod = CalculatorObj.add(temp.courseConfig, resultItem.coursePeriod)
        // levelTwoPeriod.coursePeriod =
        //   childrenTree.coursePeriod === 0
        //     ? CalculatorObj.add(temp.courseConfig, resultItem.coursePeriod)
        //     : childrenTree.coursePeriod
        // const firstCount = CalculatorObj.add(temp.selectCourseSumbit, resultItem.selectCourse)
        // levelTwoPeriod.selectCourse = childrenTree.selectCourse

        childrenTree.selectCourseNoSumbit = CalculatorObj.add(item.coursePeriodCount, resultItem.selectCourseNoSumbit)
        result.set(temp.outlineId, childrenTree)
      }
    })
    const backData = [...result.keys()].map(item => result.get(item))
    return backData
  }

  /**
   * 选择课程
   * @param course 选中的课程
   */
  push(course: CourseDetail) {
    const temp = this.outlineIdToCourseMap.get(course.outlineId)
      ? this.outlineIdToCourseMap.get(course.outlineId)
      : new SelectCourse()
    temp.outlineId = course.outlineId
    temp.outlineName = course.outlineName
    temp.fatherOutlineId = course.fatherOutlineId
    temp.fatherOutlineName = course.fatherOutlineName
    temp.courseList.push(course)
    this.outlineIdToCourseMap.set(course.outlineId, temp)
    const ids = [...this.outlineIdToCourseMap.keys()]
    this.isSelect = []
    ids.forEach(item => {
      this.isSelect.push(this.outlineIdToCourseMap.get(item))
    })

    if (this.isSecondElectiveMaxPeriod) {
      if (this.selectCheckList.length) {
        const findRootSelectTree = this.selectCheckList[0]
        findRootSelectTree.courseList.push(course)
      }
    } else {
      const findTwoSelectTree = this.selectCheckList.find(
        it => it.outlineId === course.outlineId || it.outlineId === course.fatherOutlineId
      )
      if (findTwoSelectTree) {
        findTwoSelectTree.courseList.push(course)
      }
    }
  }
  /**
   * 移除课程
   */
  delectCourse(course: CourseDetail) {
    const temp = this.outlineIdToCourseMap.get(course.outlineId)
    temp.outlineId = course.outlineId
    temp.outlineName = course.outlineName
    temp.fatherOutlineId = course.fatherOutlineId
    temp.fatherOutlineName = course.fatherOutlineName
    let key = -1
    // temp.courseList.push(course)
    temp.courseList.forEach((item, index) => {
      if (item.courseId === course.courseId) {
        key = index
      }
    })
    temp.courseList.splice(key, 1)
    if (temp.courseList.length === 0) {
      this.outlineIdToCourseMap.delete(course.outlineId)
    } else {
      this.outlineIdToCourseMap.set(course.outlineId, temp)
    }
    const ids = [...this.outlineIdToCourseMap.keys()]
    this.isSelect = []
    ids.forEach(item => {
      this.isSelect.push(this.outlineIdToCourseMap.get(item))
    })
    if (this.isSecondElectiveMaxPeriod) {
      if (this.selectCheckList.length) {
        const findRootSelectTree = this.selectCheckList[0]
        const findCourseIndex = findRootSelectTree.courseList.findIndex(
          it => it.courseId === course.courseId && it.outlineId === course.outlineId
        )
        if (findCourseIndex != -1) {
          findRootSelectTree.courseList.splice(findCourseIndex, 1)
        }
      }
    } else {
      const findTwoSelectTree = this.selectCheckList.find(
        it => it.outlineId === course.outlineId || it.outlineId === course.fatherOutlineId
      )
      if (findTwoSelectTree) {
        const findCourseIndex = findTwoSelectTree.courseList.findIndex(it => it.courseId === course.courseId)
        if (findCourseIndex != -1) {
          findTwoSelectTree.courseList.splice(findCourseIndex, 1)
        }
      }
    }
  }
  /**
   * 提交选课内容
   * @return {ResponseStatus}
   * @param courseIdList 智能选课使用,处理智能选课排序问题
   */
  async doChooseCourse(courseIdList?: Array<string>): Promise<ResponseStatus> {
    // 选课内容真正提交，提交成功同时联动可选课程列表、学时重算 -LWF
    const param = new ChooseCourseRequest()
    param.chooseToken = this.token
    param.courseList = []
    this.selectCheckList.forEach(item => {
      item.courseList.forEach(item => {
        const dto = new ChooseCourseInfoRequest()
        dto.outlineId = item.outlineIdInBackstage
        dto.courseId = item.courseId
        param.courseList.push(dto)
      })
    })
    if (courseIdList && courseIdList?.length) {
      param.courseList.sort((a, b) => {
        const indexA = courseIdList.indexOf(a.courseId)
        const indexB = courseIdList.indexOf(b.courseId)
        // 如果两个元素在 order 数组中的位置相同，则维持原有的顺序
        if (indexA === indexB) return 0
        // 如果 A 在 B 之前，则返回负数；反之则返回正数
        return indexA - indexB
      })
    }
    const res = await ChooseCourseGateway.chooseCourse(param)
    const description = new Description()
    description.response = res?.data
    description.params = param

    // if (!res.status.isSuccess() && res.data.code != '200') {
    if (res.data.code != '200') {
      // 可能是因为勾选课程被方案剔除 是否需要重新获取新的课程列表
      if (res.data.code === '51001') {
        description.message = '课程大纲内不存在该课程'
        upMyLog(description, 'chooseCourse')
        // this.removeErrorCourse(res.data.chooseErrorResult)
        return new ResponseStatus(500, '课程大纲内不存在该课程')
      }
      if (res.data.code === '51002') {
        description.message = '重复选课'
        upMyLog(description, 'chooseCourse')
        return new ResponseStatus(500, '重复选课')
      }
      if (res.data.code === '41005') {
        description.message = '选课失败，选课范围不满足选课规则要求选课'
        upMyLog(description, 'chooseCourse')
        return new ResponseStatus(500, '选课失败，选课范围不满足选课规则要求选课')
      }
      if (res.data.code === '51004') {
        description.message = '三级大纲不符合工种要求'
        upMyLog(description, 'chooseCourse')
        return new ResponseStatus(500, '三级大纲不符合工种要求')
      }
      description.message = '部分课程发生变化，请重新选课'
      upMyLog(description, 'chooseCourse')
      return new ResponseStatus(500, '部分课程发生变化，请重新选课')
    } else {
      // 清空已选清单
      const needPeriod = CalculatorObj.subtract(this.needPeriod, this.coursePeriodCount)
      if (needPeriod === 0) {
        return new ResponseStatus(200, '选课成功，您已完成选课，请尽快学习')
      } else {
        return new ResponseStatus(200, `选课成功，您还需选课${needPeriod}学时`)
      }
    }
  }

  /**
   * 初始化选课清单
   */
  private initSelectCheckList() {
    const keys = [...this.treeItemMap.keys()]
    const newSelectCourseList = new Array<SelectCourse>()
    if (this.isSecondElectiveMaxPeriod) {
      // 找根选修节点
      keys?.length &&
        keys.map(item => {
          const findItem = this.treeItemMap.get(item)
          if (findItem.level === 1) {
            const selectCourseNode = new SelectCourse()
            selectCourseNode.outlineId = findItem.outlineId
            selectCourseNode.outlineName = findItem.name
            selectCourseNode.fatherOutlineId = findItem.fatherOutlineId
            selectCourseNode.fatherOutlineName = findItem.fatherOutlineName
            selectCourseNode.courseList = []
            if (this.outlineIdTimeMap?.get(findItem.outlineId)) {
              selectCourseNode.configPeriod = this.outlineIdTimeMap?.get(findItem.outlineId)
            } else {
              selectCourseNode.configPeriod = CalculatorObj.subtract(findItem.courseConfig, findItem.selectCourseSumbit)
            }
            newSelectCourseList.push(selectCourseNode)
          }
        })
    } else {
      // 找出所有二级节点
      keys?.length &&
        keys.map(item => {
          const findItem = this.treeItemMap.get(item)
          if (findItem.level === 2) {
            const selectCourseNode = new SelectCourse()
            selectCourseNode.outlineId = findItem.outlineId
            selectCourseNode.outlineName = findItem.name
            selectCourseNode.fatherOutlineId = findItem.fatherOutlineId
            selectCourseNode.fatherOutlineName = findItem.fatherOutlineName
            selectCourseNode.courseList = []

            if (this.outlineIdTimeMap?.get(findItem.outlineId) || this.outlineIdTimeMap?.get(findItem.outlineId) == 0) {
              selectCourseNode.configPeriod = this.outlineIdTimeMap?.get(findItem.outlineId)
            } else {
              selectCourseNode.configPeriod = CalculatorObj.subtract(findItem.courseConfig, findItem.selectCourseSumbit)
            }
            newSelectCourseList.push(selectCourseNode)
          }
        })
    }

    this.selectCheckList = newSelectCourseList
  }
}
