<route-meta>
{
"isMenu": true,
"title": "资讯管理",
"sort": 2,
"icon": "icon-zixun"
}
</route-meta>

<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  import info from '@hbfe/jxjy-admin-info/src/index.vue'
  @RoleTypeDecorator({
    query: [WXGLY],
    create: [WXGLY],
    category: [WXGLY],
    copy: [WXGLY],
    publish: [WXGLY],
    modify: [WXGLY],
    remove: [WXGLY],
    draft: [WXGLY]
  })
  export default class extends info {}
</script>

<style scoped></style>
