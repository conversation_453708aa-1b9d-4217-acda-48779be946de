import { QuestionnaireOpenDateTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireOpenDateType'
import DateRange from '@api/service/common/scheme/model/DateRange'
import { QuestionnaireAppliedRangeTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'
import { QuestionnairePreconditionTypeEnum } from '@api/service/common/scheme/enum/QuestionnairePreconditionType'
import { QuestionnaireTriggerTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireTriggerType'
import Mockjs from 'mockjs'
import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
import MultipleAssessLearningTypeInfo from '@api/service/common/scheme/model/MultipleAssessLearningTypeInfo'
import { QuestionnaireAssessTypeEnum } from '@api/service/common/scheme/enum/AssessType'
import { QuestionnaireStatusEnum } from '@api/service/common/scheme/enum/QuestionnaireStatus'

/**
 * @description 问卷配置详情
 */
class QuestionnaireConfigDetail extends MultipleAssessLearningTypeInfo<QuestionnaireAssessTypeEnum> {
  /**
   * 前端唯一标识
   * @description 创建时需要前端自行占位
   */
  static feUniquekeyPrefix = 'uniquekey'

  /**
   * 允许作答次数，-1无限
   */
  allowCount = 1
  /**
   * 问卷状态,1-正常 2-停用
   */
  status: QuestionnaireStatusEnum = QuestionnaireStatusEnum.enabled
  /**
   * 操作类型
   */
  operationType: OperationTypeEnum = OperationTypeEnum.create
  /**
   * 问卷配置id
   */
  id = ''
  /**
   * 问卷模板id
   */
  templateId = ''
  /**
   * 问卷模板名称
   */
  templateName = ''
  /**
   * 问卷名称
   */
  questionnaireName = ''
  /**
   * 问卷开放作答时段类型
   */
  openDateType: QuestionnaireOpenDateTypeEnum = QuestionnaireOpenDateTypeEnum.assign
  /**
   * 问卷开放时段
   */
  openDateRange: DateRange = new DateRange()
  /**
   * 是否开启结果
   * @description 配置学员在提交问卷后，是否开放问卷统计结果给学员查看。
   */
  isOpenStatistic = false
  /**
   * 问卷应用范围类型, 0-培训方案 1-线上课程 2-指定期别 3-全期别
   */
  appliedRangeType: QuestionnaireAppliedRangeTypeEnum = QuestionnaireAppliedRangeTypeEnum.scheme
  /**
   * 指定期别编号，当应用范围类型为指定培训期别时，必填
   * @description 供ui使用，因为创建时期别id还没生成，状态层需要手动关联至期别id
   */
  curIssueNo = ''
  /**
   * 指定期别Id，当应用范围类型为指定培训期别时，必填
   */
  curIssueId = ''
  /**
   * 指定期别名称，当应用范围类型为指定培训期别且查看详情时有值
   */
  curIssueName = ''
  /**
   * 期别问卷前置条件id
   * @description UI无关，仅用作与后端数据流转
   */
  preconditionId = ''
  /**
   * 期别问卷前置条件名称
   * @description UI无关，仅用作与后端数据流转
   */
  preconditionName = ''
  /**
   * 期别问卷前置条件类型
   */
  preconditionType?: QuestionnairePreconditionTypeEnum = QuestionnairePreconditionTypeEnum.none
  /**
   * 是否纳入考核
   */
  isAssessed = false
  /**
   * 是否强制问卷，适用培训方案/线上课程
   * @description 不纳入考核时，显示此配置；应用范围为全部期别、指定培训期别时，不显示此项
   */
  isForced: boolean = null
  /**
   * 问卷触发类型 1-考试前 2-打印证明前
   * @description 选择不纳入考核且强制问卷时，显示此配置
   */
  triggerType: QuestionnaireTriggerTypeEnum = null
  /**
   * 全期别问卷唯一标识
   * @description 仅当应用类型为全期别时有值
   */
  uniqueKey = ''

  constructor() {
    super()
    this.id = Mockjs.Random.guid()
    this.uniqueKey = QuestionnaireConfigDetail.feUniquekeyPrefix + Mockjs.Random.guid()
  }
}

export default QuestionnaireConfigDetail
