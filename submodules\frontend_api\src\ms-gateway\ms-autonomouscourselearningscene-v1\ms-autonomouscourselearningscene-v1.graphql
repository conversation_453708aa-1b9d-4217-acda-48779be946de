"""独立部署的微服务,K8S服务名:ms-autonomouscourselearningscene-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""申请课程一键学习token"""
	applyCourseImmediatelyLearning(studentLearningToken:String!,studentCourseId:String!,quizScore:Double):CourseImdlyLearningTokenResponse
	"""申请课程学习
		200-成功
		60001-学习token解析异常
		60002-学习场景不存在
		60003-该大纲下课程不存在
		60004-选课失败
		60005-必修课暂未完全合格，无法学习选修
		60006-查询必修课学习情况异常
		@param studentLearningToken 学员学习凭证
		@param outlineId            所属课程学习大纲编号
		@param courseId             课程编号
		@return 课程学习凭证
	"""
	applyCourseLearning(studentLearningToken:String!,outlineId:String!,courseId:String!):StudentCourseLearningTokenResponse
	"""申请课件一键学习token"""
	applyCoursewareImmediatelyLearning(studentLearningToken:String!,studentCourseId:String!,coursewareId:String!):CoursewareImdlyLearningTokenResponse
	invalidStudentCourse(studentReLearnToken:String!,studentCourseId:String!):Void
	relearnStudentCourse(studentReLearnToken:String!,studentCourseId:String!):Void
	"""操作场景-测试接口"""
	sceneDelete(preCommand:AutonomousCourseLearningScenePreRemoveCommand,commitCommand:AutonomousCourseLearningSceneCommitRemoveCommand):NeedRecalculateProcessResult
	"""操作场景-测试接口"""
	sceneOperate(command:AutonomousCourseLearningScenePreCreateCommand):NeedRecalculateProcessResult
	"""操作场景-测试接口"""
	sceneUpdate(preCommand:AutonomousCourseLearningScenePreUpdateCommand,commitCommand:AutonomousCourseLearningSceneCommitUpdateCommand):NeedRecalculateProcessResult
}
input LSContext @type(value:"com.fjhb.domain.learningscheme.support.resource.consts.LSContext") {
	lifeCycle:LifeCycle
}
"""自主学习场景提交删除命令
	<AUTHOR>
	@since 2023/7/3
"""
input AutonomousCourseLearningSceneCommitRemoveCommand @type(value:"com.fjhb.ms.clscene.autonomous.v1.api.command.AutonomousCourseLearningSceneCommitRemoveCommand") {
	"""自主学习场景，配置json体
		@see com.fjhb.ms.clscene.autonomous.v1.api.config.AutonomousCourseLearningSceneConfig
	"""
	config:String!
	"""方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String!
	"""平台ID"""
	platformId:String!
	"""平台版本ID"""
	platformVersionId:String!
	"""项目ID"""
	projectId:String!
	"""子项目ID"""
	subProjectId:String!
	"""单位ID"""
	unitId:String!
	"""服务商ID"""
	servicerId:String!
	"""删除人id"""
	deleteUserId:String!
	"""删除时间"""
	deleteTime:DateTime!
	lsContext:LSContext
	currentTxId:String
}
"""自主学习场景提交更新命令
	<AUTHOR>
	@since 2023/7/3
"""
input AutonomousCourseLearningSceneCommitUpdateCommand @type(value:"com.fjhb.ms.clscene.autonomous.v1.api.command.AutonomousCourseLearningSceneCommitUpdateCommand") {
	"""对应资源的学习方案配置json体
		@see com.fjhb.ms.clscene.autonomous.v1.api.config.AutonomousCourseLearningSceneConfig
	"""
	config:String!
	"""方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String!
	"""平台ID"""
	platformId:String!
	"""平台版本ID"""
	platformVersionId:String!
	"""项目ID"""
	projectId:String!
	"""子项目ID"""
	subProjectId:String!
	"""单位ID"""
	unitId:String!
	"""服务商ID"""
	servicerId:String!
	"""更新人id"""
	updateUserId:String!
	"""更新时间"""
	updateTime:DateTime!
	lsContext:LSContext
	currentTxId:String
}
"""自主学习场景预创建命令
	<AUTHOR>
	@since 2023/7/3
"""
input AutonomousCourseLearningScenePreCreateCommand @type(value:"com.fjhb.ms.clscene.autonomous.v1.api.command.AutonomousCourseLearningScenePreCreateCommand") {
	"""自主学习场景，配置json体
		@see com.fjhb.ms.clscene.autonomous.v1.api.config.AutonomousCourseLearningSceneConfig
	"""
	config:String!
	"""方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String!
	"""平台ID"""
	platformId:String!
	"""平台版本ID"""
	platformVersionId:String!
	"""项目ID"""
	projectId:String!
	"""子项目ID"""
	subProjectId:String!
	"""单位ID"""
	unitId:String!
	"""服务商ID"""
	servicerId:String!
	"""创建人id"""
	createUserId:String!
	"""预创建时间"""
	preCreateTime:DateTime!
	lsContext:LSContext
	currentTxId:String
}
"""自主学习场景预删除命令
	<AUTHOR>
	@since 2023/7/3
"""
input AutonomousCourseLearningScenePreRemoveCommand @type(value:"com.fjhb.ms.clscene.autonomous.v1.api.command.AutonomousCourseLearningScenePreRemoveCommand") {
	"""自主学习场景，配置json体
		@see com.fjhb.ms.clscene.autonomous.v1.api.config.AutonomousCourseLearningSceneConfig
	"""
	config:String!
	"""方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String!
	"""平台ID"""
	platformId:String!
	"""平台版本ID"""
	platformVersionId:String!
	"""项目ID"""
	projectId:String!
	"""子项目ID"""
	subProjectId:String!
	"""单位ID"""
	unitId:String!
	"""服务商ID"""
	servicerId:String!
	"""删除人id"""
	deleteUserId:String!
	"""预删除时间"""
	preDeleteTime:DateTime!
	lsContext:LSContext
	currentTxId:String
}
"""自主学习场景预更新命令
	<AUTHOR>
	@since 2023/7/3
"""
input AutonomousCourseLearningScenePreUpdateCommand @type(value:"com.fjhb.ms.clscene.autonomous.v1.api.command.AutonomousCourseLearningScenePreUpdateCommand") {
	"""对应资源的学习方案配置json体
		@see com.fjhb.ms.clscene.autonomous.v1.api.config.AutonomousCourseLearningSceneConfig
	"""
	config:String!
	"""方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String!
	"""平台ID"""
	platformId:String!
	"""平台版本ID"""
	platformVersionId:String!
	"""项目ID"""
	projectId:String!
	"""子项目ID"""
	subProjectId:String!
	"""单位ID"""
	unitId:String!
	"""服务商ID"""
	servicerId:String!
	"""更新人id"""
	updateUserId:String!
	"""预更新时间"""
	preUpdateTime:DateTime!
	lsContext:LSContext
	currentTxId:String
}
enum LifeCycle @type(value:"com.fjhb.domain.learningscheme.support.resource.consts.LSContext$LifeCycle") {
	create
	update
	delete
}
type NeedRecalculateProcessResult @type(value:"com.fjhb.domain.learningscheme.support.resource.response.NeedRecalculateProcessResult") {
	learningId:String
	learningResourceId:String
	recalculateRequests:[RecalculateRequest]
	code:String
	message:String
	configJson:String
	resourceId:String
	asyncExecute:Boolean!
	resultType:Int!
}
type RecalculateRequest @type(value:"com.fjhb.domain.learningscheme.support.resource.response.RecalculateRequest") {
	dataType:String
	priority:Int!
	recalculate:Boolean
	updatedCode:String
	metadata:Map
}
"""课程一键学习凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type CourseImdlyLearningTokenResponse @type(value:"com.fjhb.ms.clscene.autonomous.v1.kernel.gateway.graphql.response.CourseImdlyLearningTokenResponse") {
	"""申请结果
		200-成功
		500-内部异常
		501001-学员课程不存在
		501002-学员课程已失效
		501003-课后测验分数不可为空
	"""
	applyResult:TokenResponse
	"""课程学习凭证"""
	token:String
}
"""课件一键学习凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type CoursewareImdlyLearningTokenResponse @type(value:"com.fjhb.ms.clscene.autonomous.v1.kernel.gateway.graphql.response.CoursewareImdlyLearningTokenResponse") {
	"""申请结果
		200-成功
		500-内部异常
		501001-学员课程不存在
		501002-学员课程已失效
	"""
	applyResult:TokenResponse
	"""课程学习凭证"""
	token:String
}
"""课程学习凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type StudentCourseLearningTokenResponse @type(value:"com.fjhb.ms.clscene.autonomous.v1.kernel.gateway.graphql.response.StudentCourseLearningTokenResponse") {
	"""申请结果"""
	applyResult:TokenResponse
	"""课程学习凭证"""
	token:String
	"""applyResult.code==L90001 有这个值
		已学习(时长/课时)
	"""
	timeLength:Double
	"""applyResult.code==L90001 有这个值
		学习规则类型1=时长(秒) 2=课时
	"""
	ruleType:Int
}
"""凭证响应基类
	<AUTHOR>
	@since 2022/1/20
"""
type TokenResponse @type(value:"com.fjhb.ms.clscene.autonomous.v1.kernel.gateway.graphql.response.TokenResponse") {
	"""代码：
		200-成功
	"""
	code:String
	"""信息"""
	message:String
}

scalar List
