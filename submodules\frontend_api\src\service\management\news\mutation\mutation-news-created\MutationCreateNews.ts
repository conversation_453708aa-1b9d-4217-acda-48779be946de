/*
 * @Description: 资讯详情
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-09 18:49:34
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 16:11:31
 */

import { ResponseStatus, Response } from '@hbfe/common'
import CreateDraftNewsVo from '@api/service/management/news/mutation/mutation-news-created/vo/CreateDraftNewsVo'
import CreateDraftNewsDto from '@api/service/management/news/mutation/mutation-news-created/dto/NewsCreateDto'
import MutationNewsChangeStatus from '@api/service/management/news/mutation/mutation-news-change-status/MutationNewsChangeStatus'
import NewsSpecialCreateDto from '@api/service/management/news/mutation/mutation-news-created/dto/NewsSpecialCreateDto'
class CreateNews {
  createDraftNews = new CreateDraftNewsVo()
  /**
   * 创建资讯草稿 草稿状态转换成发布使用
   * @returns ResponseStatus
   */
  async doCreateDraftNews(verifyPopUps = false): Promise<string> {
    const { data } = await CreateDraftNewsDto.from(this.createDraftNews).toDraft(verifyPopUps)
    return data
  }

  /**
   * 创建专题资讯草稿 草稿状态转换成发布使用
   * @returns id
   */
  async doCreateSpecialDraftNews(verifyPopUps = false): Promise<string[]> {
    const { data } = await NewsSpecialCreateDto.from(this.createDraftNews).toDraft(verifyPopUps)

    return data
  }

  /**
   * 发布: 先创建资讯草稿 ==》 发布草稿
   * @returns ResponseStatus
   */
  async doPublishNews(): Promise<ResponseStatus> {
    const id = await this.doCreateDraftNews(true)
    if (id) {
      const mutationNewsChangeStatus = new MutationNewsChangeStatus(id)
      const status = await mutationNewsChangeStatus.doPublishNews()
      return status
    } else {
      return new ResponseStatus(500, '此时间段已有弹窗消息,无法添加')
    }
  }

  /**
   * 发布专题资讯: 先创建资讯草稿 ==》 发布草稿
   * @returns ResponseStatus
   */
  async doPublishSpecialNews() {
    const ids = await this.doCreateSpecialDraftNews(true)
    if (ids?.length) {
      const mutationNewsChangeStatus = new MutationNewsChangeStatus()
      const status = await mutationNewsChangeStatus.batchPublishNews(ids)
      return new ResponseStatus(200, '')
    } else {
      return new ResponseStatus(500, '此时间段已有弹窗消息,无法添加')
    }
  }
}

export default CreateNews
