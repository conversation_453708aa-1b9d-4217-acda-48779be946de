import detail from './queries/detail.graphql'
import page from './queries/page.graphql'
import pageForTrainingInstitution from './queries/pageForTrainingInstitution.graphql'
import createCoursewareSupplier from './mutates/createCoursewareSupplier.graphql'
import createCoursewareSupplierByAccount from './mutates/createCoursewareSupplierByAccount.graphql'

export { detail, page, pageForTrainingInstitution, createCoursewareSupplier, createCoursewareSupplierByAccount }
