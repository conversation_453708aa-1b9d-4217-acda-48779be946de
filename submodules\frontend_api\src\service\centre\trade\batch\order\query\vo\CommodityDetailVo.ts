import SkuPropertyVo from '@api/service/centre/trade/batch/order/query/vo/SkuPropertyVo'
import { ExchangeGoodsStatusEnum } from '@api/service/common/enums/train-class/ExchangeGoodsStatusList'

/**
 * @description
 */
class CommodityDetailVo {
  /**
   * 培训方案id
   */
  schemeId = ''

  /**
   * 培训班商品id
   */
  commoditySkuId = ''

  /**
   * 培训方案名称
   */
  schemeName = ''

  /**
   * sku属性
   */
  skuProperty: SkuPropertyVo = new SkuPropertyVo()

  /**
   * 学时
   */
  period = 0

  /**
   * 价格
   */
  price = 0
}

export default CommodityDetailVo
