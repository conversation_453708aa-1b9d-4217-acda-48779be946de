import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
import AssessSettingInfo from '@api/service/common/scheme/model/AssessSettingInfo'

/**
 * @description 多考核学习方式信息
 */
class MultipleAssessLearningTypeInfo<T = string> {
  /**
   * 学习方式id
   */
  id = ''
  /**
   * 考核项列表
   */
  assessSettings = [] as AssessSettingInfo<T>[]
  /**
   * 配置项id
   */
  configId = ''
  /**
   * 操作类型
   */
  operationType: OperationTypeEnum = OperationTypeEnum.create
}

export default MultipleAssessLearningTypeInfo
