import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import QueryTrainClassFactory from '@api/service/centre/train-class/QueryTrainClassFactory'
import MutationTrainClassFactory from '@api/service/centre/train-class/MutationTrainClassFactory'

/**
 * @description 培训方案状态层
 */
@Module({ namespaced: true, dynamic: true, name: 'TrainClassModule', store })
class TrainClassModule extends VuexModule {
  /**
   *  【培训方案】查询工厂
   */
  queryTrainClassFactory = new QueryTrainClassFactory()

  /**
   * 【培训方案】业务工厂
   */
  mutationTrainClassFactory = new MutationTrainClassFactory()
}

export default getModule(TrainClassModule)
