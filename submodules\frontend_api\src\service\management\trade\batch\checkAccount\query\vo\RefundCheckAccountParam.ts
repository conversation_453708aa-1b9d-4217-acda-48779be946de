/*
 * @Description: 退款订单对账参数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-06 09:59:49
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-17 19:59:26
 */
import {
  BatchOrderInfoRequest,
  BatchReturnOrderBasicDataRequest,
  BatchReturnOrderRequest,
  BatchReturnOrderStatusChangeTimeRequest,
  BigDecimalScopeRequest,
  DateScopeRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import SaleChannelType, { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import CheckAccountParam from './CheckAccountParam'

export default class RefundCheckAccountParam extends CheckAccountParam {
  /**
   * 退款单号
   */
  refundId?: string
  static refurnTo(refundCheckAccountParam: RefundCheckAccountParam) {
    //
    const { paymentAccountID, orderId, batchId, startDate, refundId, endDate } = refundCheckAccountParam
    const returnOrderRequest = new BatchReturnOrderRequest()
    returnOrderRequest.batchReturnOrderList = refundId ? [refundId] : undefined
    returnOrderRequest.basicData = new BatchReturnOrderBasicDataRequest()
    returnOrderRequest.basicData.batchReturnOrderStatus = [9, 10, 11]
    returnOrderRequest.basicData.batchReturnStatusChangeTime = new BatchReturnOrderStatusChangeTimeRequest()
    returnOrderRequest.basicData.batchReturnStatusChangeTime.returnCompleted = new DateScopeRequest()
    returnOrderRequest.basicData.batchReturnStatusChangeTime.returnCompleted.begin = startDate
    returnOrderRequest.basicData.batchReturnStatusChangeTime.returnCompleted.end = endDate
    returnOrderRequest.batchOrderInfo = new BatchOrderInfoRequest()
    returnOrderRequest.batchOrderInfo.paymentOrderTypeList = [1, 2]
    returnOrderRequest.basicData.refundAmountScope = new BigDecimalScopeRequest()
    returnOrderRequest.basicData.refundAmountScope.begin = 0.01
    returnOrderRequest.batchOrderInfo.batchOrderNoList = orderId ? [orderId] : undefined
    returnOrderRequest.batchOrderInfo.receiveAccountIdList = paymentAccountID ? [paymentAccountID] : undefined
    returnOrderRequest.batchOrderInfo.flowNoList = batchId ? [batchId] : undefined
    if (refundCheckAccountParam.saleSource || refundCheckAccountParam.saleSource === SaleChannelEnum.self) {
      returnOrderRequest.basicData.saleChannels = [refundCheckAccountParam.saleSource]
    } else {
      returnOrderRequest.basicData.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic
      ]
    }
    // returnOrderRequest.basicData.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(
    //   refundCheckAccountParam.isFromSpecialSubject
    // )
    returnOrderRequest.basicData.saleChannelName = returnOrderRequest.basicData.saleChannels.includes(
      SaleChannelEnum.topic
    )
      ? refundCheckAccountParam.specialSubjectName
      : ''

    if (refundCheckAccountParam.distributorId) {
      returnOrderRequest.distributorId = refundCheckAccountParam.distributorId
    }
    if (refundCheckAccountParam.promotionPortalId) {
      returnOrderRequest.portalId = refundCheckAccountParam.promotionPortalId
    }
    if (refundCheckAccountParam.paymentMethod) {
      returnOrderRequest.batchOrderInfo.paymentOrderTypeList = [refundCheckAccountParam.paymentMethod]
    } else {
      returnOrderRequest.batchOrderInfo.paymentOrderTypeList = null
    }
    return returnOrderRequest
  }
}
