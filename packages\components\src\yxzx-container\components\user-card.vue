<template>
  <div class="user-info">
    <img class="photo" :src="userPhoto" />
    <p class="name">{{ userName }}</p>
    <div class="op-btn f-mt10">
      <el-button type="primary" size="mini" round plain @click="openPersonCenter">
        <i class="el-icon-s-tools"></i>
        帐号设置
      </el-button>
    </div>
    <div class="op-btn company f-mt10" v-if="isFxlogin">
      <el-dropdown placement="bottom" trigger="click" @command="selectUnit" style="z-index: 1000">
        <el-button type="primary" size="mini" round plain>
          <span class="txt">{{ unitModelName || '这里是单位名称' }}</span>
          <i class="el-icon-caret-bottom"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown" class="user-info-dropdown">
          <el-dropdown-item
            v-for="unit in queryManagerDetail.distributionUnitInformationList"
            :key="unit.unitId"
            :command="unit"
          >
            {{ unit.name }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <el-dropdown placement="bottom" size="small" trigger="click" @command="roleChange" v-if="filterServiceList.length">
      <div class="role-switch">
        {{ serviceName || '当前账号角色' }}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item
          class="user-info-dropdown"
          v-for="serviceModel in filterServiceList"
          :key="serviceModel.servicerType"
          :command="serviceModel"
          :class="{ true: 'activated' }[currentSelectServiceTypeModel.servicerType === serviceModel.servicerType]"
        >
          {{ nameMap[serviceModel.servicerType] }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>

    <el-dropdown placement="bottom" size="small" trigger="click" v-if="serviceList.length > 1" @command="switchService">
      <div class="role-switch">
        {{ currentSelectServiceModel.name }}
        <i class="el-icon-arrow-down el-icon--right"></i>
      </div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="serviceModel in serviceList" :key="serviceModel.id" :command="serviceModel">
          {{ serviceModel.name }}
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<style lang="scss">
  @import '~element-ui/packages/theme-chalk/src/common/var.scss';

  .user-info-dropdown {
    .el-dropdown-menu__item {
      &.activated {
        background: $--color-primary;
        color: $--color-white;
      }
    }
  }
  .user-info-dropdown {
    max-height: 300px; /* 根据需要调整最大高度 */
    overflow-y: auto;
  }
  .side-collapsed {
    .user-info {
      .photo {
        width: 30px;
        height: 30px;
      }
    }

    .name,
    .role-pop,
    .role {
      opacity: 0;
      display: none;
    }
  }
</style>

<script lang="ts">
  import Context from '@/RuntimeContext'
  import ServiceTokenMixin from '@hbfe/jxjy-admin-common/src/mixins/ServiceTokenMixin'
  import RootModule from '@/store/RootModule'
  import { ServicerDto } from '@api/gateway/PlatformServicer'
  import AuthModule from '@api/service/common/auth/AuthModule'
  import { CurrentUserServicerGroup } from '@api/service/common/auth/models/CurrentUserServicerGroup'
  import Authentication from '@api/service/common/authentication/Authentication'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import basicDataModule from '@api/service/management/basic-data/BasicDataModule'
  import UserModule from '@api/service/management/user/UserModule'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import DistributionUnitInformation from '@api/service/management/user/query/manager/vo/DistributionUnitInformation'
  import { Component, Vue } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    $authentication: Authentication

    // 选中的服务商类型
    currentSelectServiceTypeModel: CurrentUserServicerGroup = new CurrentUserServicerGroup()

    // 选中的具体服务商
    currentSelectServiceModel: ServicerDto = new ServicerDto()

    queryManagerDetail = UserModule.queryUserFactory.queryManagerDetail

    // 判断当前登录角色是否是分销商
    isFxlogin = QueryManagerDetail?.hasCategory(CategoryEnums.fxs)

    get userInfo() {
      //   console.log(basicDataModule, '4545')
      return basicDataModule.userInfo
    }
    get userName() {
      return this.queryManagerDetail.adminInfo.userInfo?.userName
    }

    // 登录成功后保存的分销单位
    get unitModelName() {
      // return localStorage.getItem('unitModelName') || QueryManagerDetail.currentUnitInfo?.name
      return QueryManagerDetail.currentUnitInfo?.name
    }

    created() {
      const selectServiceId = ServiceTokenMixin.shareInstance().getSelectedService()
      if (selectServiceId) {
        this.filterServiceList.forEach((ServiceTypeModel: CurrentUserServicerGroup) => {
          const serviceList = ServiceTypeModel.currentUserServicerList
          serviceList.forEach((ServiceModel: ServicerDto) => {
            if (ServiceModel.id == selectServiceId) {
              this.currentSelectServiceTypeModel = ServiceTypeModel
              this.currentSelectServiceModel = ServiceModel
              return
            }
          })
        })
      }
      if (this.isFxlogin) {
        this.changeAuthorizationUnitInfoList()
      }
    }

    async switchService(service: ServicerDto) {
      try {
        await ServiceTokenMixin.shareInstance().switchService(service.id)
      } catch (e) {
        return this.$message.error('切换角色失败')
      }
    }

    openPersonCenter() {
      this.$router.push('/home/<USER>')
    }

    get serviceName() {
      return this.nameMap[this.currentSelectServiceTypeModel.servicerType] || ''
    }

    nameMap = {
      1: '培训机构',
      2: '课件供应商',
      3: '渠道商',
      4: '参训单位'
    }

    get serviceList(): any[] {
      try {
        const item = this.filterServiceList.find((item: CurrentUserServicerGroup) => {
          return item.servicerType == this.currentSelectServiceTypeModel.servicerType
        })
        return item.currentUserServicerList
      } catch (e) {
        return []
      }
    }

    get filterServiceList() {
      return AuthModule.getCurrentUserServicerGroup || []
    }

    get userPhoto() {
      return this.userInfo?.photo
        ? `/mfs${this.userInfo?.photo}`
        : require('@design/admin/assets/images/default-photo.jpg')
    }

    currentUserServiceGroup: CurrentUserServicerGroup

    // 切换角色时触发的事件
    async roleChange(type: CurrentUserServicerGroup) {
      this.currentUserServiceGroup = type
      if (this.currentUserServiceGroup.servicerType === Context.businessContext?.serviceProvider?.serviceType) {
        return
      }
      // 2021-08-13调整：切换角色返回首页而不是欢迎页
      const home = this.$router.resolve('/')
      window.location.replace(home.href)
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        await this.switchService(type?.currentUserServicerList[0])
        await RootModule.doSetFxNavList([])
      } catch (e) {
        this.$message.error('切换失败！')
        loading.close()
      }
    }

    // 查询当前单位角色列表
    async changeAuthorizationUnitInfoList() {
      try {
        await QueryManagerDetail.changeAuthorizationUnitInfoList()
      } catch (e) {
        console.log(e)
      }
    }

    // 申请（业务域）身份凭证切换
    async selectUnit(unit: DistributionUnitInformation) {
      try {
        if (!unit) {
          return
        }
        if (unit?.unitId == QueryManagerDetail.currentUnitInfo?.unitId) {
          this.$message.warning('当前单位已为切换单位')
          return
        }

        // 用户信息身份认证
        try {
          const res = await this.$authentication.selectUnit(unit)
          if (res?.data.code !== 200) {
            this.$authentication.ssoLogout()
            return this.$message.warning('会话已过期')
          } else {
            localStorage.setItem('currentUnitId', unit.unitId)
            this.$message.success('切换单位成功')
          }
        } catch (e) {
          return this.$message.warning('身份认证过程中出错: ' + e)
        }
        setTimeout(() => {
          location.reload()
        }, 500)
      } catch (e) {
        console.log(e)
      }
    }
  }
</script>
