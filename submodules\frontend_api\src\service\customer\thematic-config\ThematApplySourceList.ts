import MsTradeQueryForestage, {
  BatchRegistrationSourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
class SourceData {
  /**
   * 销售渠道id
   */
  id: string
  /**
   * 销售渠道名称
   */
  name: string
  /**
   * 销售渠道
   * 0-自营 1-分销 2专题
   */
  saleChannel: number
}
export default class ThematApplySourceList {
  /**
   * 销售渠道列表
   */
  list = new Array<SourceData>()

  /**
   * 查询当前集体管理员的报名批次来源列表
   */
  async queryList() {
    const res = await MsTradeQueryForestage.listBatchRegistrationSources()
    if (res.status.isSuccess() && res.data) {
      this.list = res.data.map((item) => {
        return {
          id: item.saleChannelId,
          name: item.saleName,
          saleChannel: item.saleChannel
        }
      })
    } else {
      this.list = []
    }
  }
}
