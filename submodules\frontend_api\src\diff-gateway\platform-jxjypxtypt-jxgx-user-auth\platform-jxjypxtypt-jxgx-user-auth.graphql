"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""用户认证信息校验接口
		@return  true 正确；false 错误
	"""
	checkUserAuthInfo(request:CheckUserAuthInfoRequest!):Boolean!
}
"""校验用户认证信息
	<AUTHOR>
"""
input CheckUserAuthInfoRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.CheckUserAuthInfoRequest") {
	"""身份证"""
	idCard:String!
}

scalar List
