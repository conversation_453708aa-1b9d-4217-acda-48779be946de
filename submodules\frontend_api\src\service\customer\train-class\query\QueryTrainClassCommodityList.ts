import SchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import MsTradeQueryFrontGatewayCourseLearningForestage, {
  CommoditySkuSortField,
  CommoditySkuSortRequest,
  Page,
  PortalCommoditySkuPropertyRequest,
  SchemeResourceResponse,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import ThematicMap from '@api/service/customer/thematic-config/ThematicMap'
import SkuPropertyConvertUtils, {
  ComplexSkuPropertyListResponse,
  ComplexSkuPropertyResponse,
  SchemeSkuInfo
} from '@api/service/customer/train-class/Utils/SkuPropertyConvertUtils'
import { SourceEnum } from '@api/service/customer/train-class/query/Enum/SourceEnum'
import { PortalCommoditySkuRequest } from '@api/service/customer/train-class/query/vo/CommodityRequestVo'
import SkuPropertyVo from '@api/service/customer/train-class/query/vo/SkuPropertyVo'
import SkuVo from '@api/service/customer/train-class/query/vo/SkuVo'
import TrainClassCommodityVo from '@api/service/customer/train-class/query/vo/TrainClassCommodityVo'
import { ResponseStatus } from '@hbfe/common'

/**
 * 用户域获取培训班商品列表
 */
class QueryTrainClassCommodityList {
  // region properties

  /**
   *总数目，类型为number
   */
  totalSize = 0
  // /**
  //  *sku过滤条件，类型为SkuPropertyVo
  //  */
  // filterSkuVo = new SkuPropertyVo()
  /**
   *培训班商品列表，类型为TrainClassCommodityVo[]
   */
  trainClassCommodityList: TrainClassCommodityVo[] = []
  /**
   *刷选条件数组，类型为SkuPropertyVo
   */
  skuProperties = new SkuPropertyVo()
  //当前选中的行业id
  currentInId = ''
  // endregion
  // region methods

  /**
   * 获取培训班列表
   */
  async queryTrainClassCommodityList(
    page: Page,
    filterCommodity: PortalCommoditySkuRequest
  ): Promise<Array<TrainClassCommodityVo>> {
    // const filter = new CommoditySkuRequest()
    // filter.skuPropertyRequest = this.filterSkuVo.convertToDto()
    const sortRequest = new CommoditySkuSortRequest()
    sortRequest.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequest.policy = SortPolicy.DESC
    filterCommodity.portalCommoditySkuSourceType =
      filterCommodity.portalCommoditySkuPropertyRequest?.portalCommoditySkuSourceType
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pagePortalCommoditySkuCustomerPurchaseInServicer({
      page,
      queryRequest: filterCommodity,
      sortRequest: [sortRequest]
    })

    if (res.status.isSuccess()) {
      const currentPageDataList = res.data.currentPageData
      if (res?.data?.totalPageSize && res?.data?.totalPageSize > 1 && page.pageSize === 200) {
        for (let i = 2; i <= res?.data?.totalPageSize; i++) {
          page.pageNo = i
          const temp =
            await MsTradeQueryFrontGatewayCourseLearningForestage.pagePortalCommoditySkuCustomerPurchaseInServicer({
              page: page,
              queryRequest: filterCommodity,
              sortRequest: [sortRequest]
            })
          if (temp?.data?.currentPageData?.length) {
            currentPageDataList.push(...temp?.data?.currentPageData)
          }
        }
      }
      // this.trainClassCommodityList = res.data.currentPageData as TrainClassCommodityVo[]
      const tmpArr = []
      const specialIds: Set<string> = new Set()
      for (const item of currentPageDataList) {
        const tmpItem = new TrainClassCommodityVo()
        Object.assign(tmpItem, item.originCommodityInfo)
        if (item.portalCommoditySkuForestageResponse) {
          Object.assign(tmpItem.skuProperty, item.portalCommoditySkuForestageResponse.skuProperty)
          tmpItem.portalCommoditySkuId = item.portalCommoditySkuForestageResponse.portalCommoditySkuId
          tmpItem.portalCommoditySkuSourceType = item.portalCommoditySkuForestageResponse.portalCommoditySkuSourceType
          tmpItem.portalCommoditySkuSourceId = item.portalCommoditySkuForestageResponse.portalCommoditySkuSourceId
          if (item.portalCommoditySkuForestageResponse.portalCommoditySkuSourceType === SourceEnum.topic) {
            specialIds.add(item.portalCommoditySkuForestageResponse.portalCommoditySkuSourceId)
          }
        }
        tmpItem.schemeId = (tmpItem.resource as SchemeResourceResponse).schemeId
        tmpItem.schemeType = (tmpItem.resource as SchemeResourceResponse).schemeType
        tmpArr.push(tmpItem)
      }

      if (specialIds.size) {
        await ThematicMap.getThematicMap([...specialIds])
      }
      const skuRequest: SchemeSkuInfo[] = []
      tmpArr?.forEach((item) => {
        skuRequest.push(
          new SchemeSkuInfo(item.schemeId, item.skuProperty as ComplexSkuPropertyResponse, item.portalCommoditySkuId)
        )
      })
      const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(skuRequest)
      tmpArr?.forEach((item) => {
        const skuInfo = skuInfos.find(
          (el) => el.id === item.schemeId && el.portalCommodityId == item.portalCommoditySkuId
        )
        if (skuInfo) item.skuValueNameProperty = skuInfo.skuName
        if (item.portalCommoditySkuSourceType === SourceEnum.topic) {
          item.specialType = ThematicMap.map.get(item.portalCommoditySkuSourceId)?.type
          item.specialIndustry = ThematicMap.map.get(item.portalCommoditySkuSourceId)?.industry
          item.specialUnit = ThematicMap.map.get(item.portalCommoditySkuSourceId)?.unitName
        }
      })
      this.trainClassCommodityList = tmpArr
      this.totalSize = res.data.totalSize
    }
    return this.trainClassCommodityList
  }
  switchTrainCate(cate: SkuVo) {
    // if (cate.skuPropertyValueId) {
    this.skuProperties.filterTrainMajor(cate)
    // }
  }

  /**
   * 获取培训班属性
   */
  async querySku(
    sku: PortalCommoditySkuPropertyRequest = new PortalCommoditySkuPropertyRequest(),
    isWeb?: boolean
  ): Promise<ResponseStatus> {
    if (sku.industry && sku.industry.length) {
      this.currentInId = sku.industry[0]
    }
    const sortRequest = new CommoditySkuSortRequest()
    sortRequest.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequest.policy = SortPolicy.DESC
    const res =
      await MsTradeQueryFrontGatewayCourseLearningForestage.listPortalCommoditySkuPropertyCustomerPurchaseInServicer(
        sku
      )
    if (res.status.isSuccess() && res.data) {
      // 优化点：如果只有一个行业，就直接赋值给currentInId
      const { industry } = res.data
      if (industry && industry.length === 1 && industry[0].skuPropertyValueId) {
        this.currentInId = res.data.industry[0].skuPropertyValueId
      }
      try {
        // 获取sku选项
        this.skuProperties = await SkuPropertyConvertUtils.convertToSkuPropertyVo(
          res.data as ComplexSkuPropertyListResponse,
          this.currentInId,
          isWeb
        )
      } catch (e) {
        console.log(e)
      }
    }
    return res.status
  }

  /**
   * 单独查询商品列表接口,先查询再处理其他参数
   */
  async pageCommoditySkuCustomer(page: Page, filterCommodity: PortalCommoditySkuRequest) {
    const sortRequest = new CommoditySkuSortRequest()
    sortRequest.sortField = CommoditySkuSortField.ON_SHELVE_TIME
    sortRequest.policy = SortPolicy.DESC
    filterCommodity.portalCommoditySkuSourceType =
      filterCommodity.portalCommoditySkuPropertyRequest?.portalCommoditySkuSourceType
    return await MsTradeQueryFrontGatewayCourseLearningForestage.pagePortalCommoditySkuCustomerPurchaseInServicer({
      page,
      queryRequest: filterCommodity
    })
  }

  static async getSchemeConfig(schemeIds: string[]) {
    const schemeRes = await SchemeLearningQueryForestage.pageSchemeConfigInServicer({
      page: {
        pageNo: 1,
        pageSize: schemeIds.length
      },
      schemeIds,
      needField: ['extendProperties', 'notice']
    })
    const schemeMap = new Map<string, { notice: string; showNoticeDialog: boolean }>()
    schemeRes.data.currentPageData.forEach((item) => {
      try {
        const schemeConfig = JSON.parse(item.schemeConfig)
        const getExtendProperties = (key: string) =>
          schemeConfig.extendProperties.find((item: any) => item.name == key)?.value
        schemeMap.set(item.schemeId, {
          notice: schemeConfig.notice,
          showNoticeDialog: getExtendProperties('showNoticeDialog')
        })
      } catch (e) {
        console.log(e)
      }
    })
    return schemeMap
  }
  // endregion
}
export default QueryTrainClassCommodityList
