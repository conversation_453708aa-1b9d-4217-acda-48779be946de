<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: <PERSON> yt
 * @LastEditTime: 2022-06-10 11:21:38
-->
<route-meta>{"title": "创建角色"}</route-meta>
<template>
  <el-main>
    <hb-bread-crumb></hb-bread-crumb>
    <div class="f-p15">
      <el-row :gutter="15" class="is-height">
        <el-col :md="11" :xl="10">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header">
              <span class="tit-txt">角色信息</span>
            </div>
            <div class="f-p30">
              <el-form
                :model="createRoles"
                :rules="rules"
                ref="createForm"
                label-width="auto"
                class="m-form"
                @submit.native.prevent
              >
                <el-form-item
                  v-if="hasDistributionService || haswxglyCategory || hasztglyCategory || hasgysCategory"
                  label="管理员类型："
                  prop="category"
                >
                  <el-select
                    v-model="createRoles.category"
                    clearable
                    placeholder="请选择管理员类型"
                    @change="categoryChange"
                    class="form-m"
                  >
                    <el-option v-if="haswxglyCategory" :value="CategoryEnums.wxgly" label="网校管理员"></el-option>
                    <el-option v-if="hasgysCategory" :value="CategoryEnums.gys" label="供应商管理员"></el-option>
                    <el-option v-if="hasztglyCategory" :value="CategoryEnums.ztgly" label="专题管理员"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="角色名称：" prop="name">
                  <el-input v-model="createRoles.name" clearable placeholder="请输入角色名称" class="form-m" />
                </el-form-item>
                <el-form-item label="角色说明：">
                  <el-input
                    clearable
                    type="textarea"
                    :rows="6"
                    v-model="createRoles.description"
                    placeholder="请输入角色说明"
                  />
                </el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-col>
        <el-col :md="13" :xl="14">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div slot="header">
              <span class="tit-txt">角色权限</span>
            </div>
            <permission-config
              v-if="createRoles.category"
              :category="createRoles.category"
              :editable="true"
              @getCheckList="getCheckList"
              ref="tree"
            ></permission-config>
            <div v-else class="m-no-date f-mt50">
              <img class="img f-mt20" src="@design/admin/assets/images/no-data-news.png" alt="" />
              <div class="date-bd">
                <p class="f-f15 f-c9">请先选择管理员类型~</p>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="goBack">取消</el-button>
        <el-button :loading="isLoading" type="primary" @click="creatRole">提交</el-button>
      </div>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import PermissionConfig from '@hbfe/jxjy-admin-account/src/role/__components__/permission-config.vue'
  import { RoleCreateDto } from '@api/gateway/PlatformBasicData'
  import CreateRole from '@api/service/management/authority/role/CreateRole'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import QuerySecurity from '@api/service/management/authority/security/query/QuerySecurity'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'

  @Component({
    components: {
      PermissionConfig
    }
  })
  export default class extends Vue {
    @Ref('createForm') createForm: any
    @Ref('tree') tree: PermissionConfig
    getQueryAllPermission = AuthorityModule.roleFactory.getQueryAllPermission()
    getCreateOrUpdateRole = AuthorityModule.roleMutationFactory.getCreateOrUpdateRole()
    roleCreateDto = new RoleCreateDto()
    createRoles = new CreateRole()
    isLoading = false
    CategoryEnums = CategoryEnums
    rules = {
      category: [{ required: true, message: '请选择管理员类型', trigger: 'blur' }],
      name: [{ required: true, message: '请输入角色名称', trigger: 'blur' }]
    }
    checkList = [] as any
    hasgysCategory = false
    haswxglyCategory = false
    hasztglyCategory = false
    /**
     * 查询实例
     */
    querySecurity: QuerySecurity = new QuerySecurity()
    /**
     * 增值服务开启
     */
    capabilityServiceConfig = CapabilityServiceConfig

    /**
     * 是否展示管理员类型选项
     */
    get hasDistributionService() {
      return this.capabilityServiceConfig.fxCapabilityEnable && this.createRoles.category !== CategoryEnums.fxs
    }
    getCheckList(checkList: any) {
      this.checkList = checkList
    }
    async created() {
      this.createRoles.category = null
      if (QueryManagerDetail.hasCategory(CategoryEnums.gys)) {
        this.hasgysCategory = true
      }
      if (QueryManagerDetail.hasCategory(CategoryEnums.wxgly)) {
        this.haswxglyCategory = true
      }
      if (QueryManagerDetail.hasCategory(CategoryEnums.ztgly) || QueryManagerDetail.hasCategory(CategoryEnums.wxgly)) {
        this.hasztglyCategory = true
      }
      if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
        this.createRoles.category = CategoryEnums.fxs
      } else if (this.capabilityServiceConfig.fxCapabilityEnable) {
        this.createRoles.category = null
      }
    }

    async creatRole() {
      this.isLoading = true
      try {
        await this.createForm.validate()
        this.createRoles.functionalAuthorityIds = this.checkList
        const tree: PermissionConfig = this.$refs.tree as PermissionConfig
        if (!tree.isAllSelected) {
          const permission = tree.permission
          const result = permission?.find((item: any) => {
            return item.name === '查询(必选)'
          })
          if (!result?.id) {
            this.isLoading = false
            this.$message.error('请勾选查询(必选)')
            return
          }
        }

        this.createRoles
          .createRoleByAdminType()
          .then((res) => {
            if (res?.status?.isSuccess() && res.data) {
              this.$message.success('创建成功')
              this.isLoading = false
              this.$router.push({
                path: '/basic-data/account/role'
              })
            } else {
              this.$message.error('创建出错')
              this.isLoading = false
            }
          })
          .catch((e) => {
            console.log(e)
            this.$message.error('创建出错')
            this.isLoading = false
          })
      } catch (e) {
        console.log(e)
        this.$message.error('创建出错')
        this.isLoading = false
        // 需要输入表单才能正常保存
      }
    }

    /**
     * 角色改变
     */
    categoryChange(category: CategoryEnums) {
      this.checkList = []
      if (category) {
        this.tree?.queryPermission(category)
      }
    }
    // 返回上一页
    goBack() {
      this.$router.back()
    }
  }
</script>
