import store from '@api/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import MutationFjzjCertificateFactory from '@api/service/diff/customer/fjzj/personal-leaning/MutationFjzjCertificateFactory'

@Module({ namespaced: true, dynamic: true, name: 'FjzjCertificateModule', store })
class FjzjCertificateModule extends VuexModule {
  /**
   *培训成果业务工厂
   */
  get mutationFjzjCertificateFactory() {
    return MutationFjzjCertificateFactory
  }
}

export default getModule(FjzjCertificateModule)
