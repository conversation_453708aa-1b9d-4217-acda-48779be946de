import UserLearningLog from '@hbfe-biz/biz-anticheat/dist/log/UserLearningLog'
import UserLearningCourse from '@hbfe-biz/biz-anticheat/dist/log/UserLearningCourse'
import { SupervisionTypeEnum, SceneTypeEnum } from '@hbfe-biz/biz-anticheat/dist/log/enums/SupervisionEnum'
import { Page } from '@hbfe/common'
import UserLearningLogItem from '@api/service/management/anticheat/models/UserLearningLogItem'
import UserLogInfo from '@api/service/management/anticheat/models/UserLogInfo'

export default class UserLearningList {
  /**
   * 方案类型 1,选课规则 2,自主选课
   */
  schemeType: number
  /**
   * 用户id
   */
  userId: string
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 交互类型 | 1、登录  2、线上课程学习 3、线上考试
   */
  type: SupervisionTypeEnum
  /**
   * 交互类型场景|1、进入场景前2、退出场是后3、场景过程中
   */
  sceneType: SceneTypeEnum
  /**
   * 是否剔除不匹配数据
   */
  isIncludeNotMatch: boolean
  /**
   * 学习方案id
   */
  schemeIds?: Array<string>
  /**
   * 课程id
   */
  courseIds?: Array<string>
  /**
   * 课件id
   */
  courseWareIds?: Array<string>

  /**
   * 进入学习
   * @param page
   */
  async queryBeforeCourseLogList(page: Page) {
    const courseList = new Array<UserLearningLogItem>()
    const courseLogList = new Array<UserLearningLogItem>()
    // 查询有监管记录的课程
    const userLearningCourse = new UserLearningCourse()
    userLearningCourse.studentNos = [this.studentNo]
    userLearningCourse.type = SupervisionTypeEnum.COURSE_LEARNING
    userLearningCourse.sceneType = SceneTypeEnum.BEFORE_SCENE
    userLearningCourse.isIncludeNotMatch = true
    userLearningCourse.pageNo = page.pageNo
    userLearningCourse.pageSize = page.pageSize
    const res = await userLearningCourse.queryList()
    page.totalSize = userLearningCourse.totalSize
    page.totalPageSize = userLearningCourse.totalPageSize
    res.map(item => {
      const course = new UserLearningLogItem()
      course.courseId = item.courseId
      course.courseName = item.courseName
      courseList.push(course)
    })
    if (courseList?.length) {
      // 查询监管记录
      const userLearningLog = new UserLearningLog()
      userLearningLog.studentNos = [this.studentNo]
      userLearningLog.type = SupervisionTypeEnum.COURSE_LEARNING
      userLearningLog.sceneType = SceneTypeEnum.BEFORE_SCENE
      userLearningLog.isIncludeNotMatch = true
      userLearningLog.courseIds = courseList.map(item => item.courseId)
      const logList = await userLearningLog.listLearningSupervisionBehaviorInServicer()
      courseList.forEach(item => {
        item.userLogInfo = logList
          .filter(ite => ite?.sceneTypeMessage?.length && ite?.sceneTypeMessage[0].courseId === item.courseId)
          .map(ite => UserLogInfo.from(ite))
      })
      let index = 0
      let prevCourseId = ''
      courseList.forEach(item => {
        if (item.userLogInfo?.length) {
          item.userLogInfo?.forEach(ite => {
            if (prevCourseId !== item.courseId) index++
            prevCourseId = item.courseId
            const courseLog = new UserLearningLogItem()
            courseLog.index = index
            courseLog.courseName = item.courseName
            courseLog.courseId = item.courseId
            courseLog.userLogInfo = new Array<UserLogInfo>()
            courseLog.userLogInfo.push(ite)
            courseLogList.push(courseLog)
          })
        } else {
          if (prevCourseId !== item.courseId) index++
          prevCourseId = item.courseId
          const courseLog = new UserLearningLogItem()
          courseLog.index = index
          courseLog.courseName = item.courseName
          courseLog.courseId = item.courseId
          courseLog.userLogInfo = new Array<UserLogInfo>()
          courseLogList.push(courseLog)
        }
      })
    }
    console.log(page)
    return courseLogList
  }
  /**
   * 学习过程中
   * @param page
   * @return {Promise<UserLearningLogItem[]>}
   */
  async queryCourseLogList(page: Page) {
    const courseList = new Array<UserLearningLogItem>()
    // 查询有监管记录的课件
    const userLearningCourse = new UserLearningCourse()
    userLearningCourse.studentNos = [this.studentNo]
    userLearningCourse.type = SupervisionTypeEnum.COURSE_LEARNING
    userLearningCourse.sceneType = SceneTypeEnum.SCENE
    userLearningCourse.pageNo = page.pageNo
    userLearningCourse.pageSize = page.pageSize
    const res = await userLearningCourse.queryList()
    page.totalSize = userLearningCourse.totalSize
    page.totalPageSize = userLearningCourse.totalPageSize
    // 查询监管日志
    const userLearningLog = new UserLearningLog()
    userLearningLog.studentNos = [this.studentNo]
    userLearningLog.type = SupervisionTypeEnum.COURSE_LEARNING
    userLearningLog.sceneType = SceneTypeEnum.SCENE
    userLearningLog.courseIds = res?.length ? [...new Set(res?.map(item => item.courseId))] : []
    userLearningLog.courseWareIds = res?.length ? [...new Set(res?.map(item => item.courseWareId))] : []
    if (userLearningLog.courseIds?.length) {
      const logList = await userLearningLog.listLearningSupervisionBehaviorInServicer()
      let index = 0
      let prevWareId = ''
      userLearningLog.courseIds.forEach(item => {
        const course = new UserLearningLogItem()
        course.courseId = item
        course.courseName = res?.find(ite => ite.courseId === course.courseId)?.courseName
        res
          ?.filter(ite => ite.courseId === course.courseId)
          .forEach(ite => {
            prevWareId = ''
            const userLogInfo = logList
              ?.filter(
                val =>
                  val?.sceneTypeMessage?.length &&
                  val?.sceneTypeMessage[0].courseId === ite.courseId &&
                  val?.sceneTypeMessage[0].courseWareId === ite.courseWareId
              )
              .map(val => UserLogInfo.from(val))
            if (userLogInfo.length) {
              if (prevWareId !== ite.courseWareId) index++
              prevWareId = ite.courseWareId
              userLogInfo.forEach(val => {
                const courseWare = new UserLearningLogItem()
                courseWare.courseId = course.courseId
                courseWare.courseName = course.courseName
                courseWare.courseWareId = ite.courseWareId
                courseWare.courseWareName = ite.courseWareName
                courseWare.index = index
                courseWare.userLogInfo = new Array<UserLogInfo>()
                courseWare.userLogInfo.push(val)
                course.courseWareUserLearningLog.push(courseWare)
              })
            } else {
              index++
              const courseWare = new UserLearningLogItem()
              courseWare.courseId = course.courseId
              courseWare.courseName = course.courseName
              courseWare.courseWareId = ite.courseWareId
              courseWare.courseWareName = ite.courseWareName
              courseWare.index = index
              courseWare.userLogInfo = new Array<UserLogInfo>()
              const userLogInfo = new UserLogInfo()
              courseWare.userLogInfo.push(userLogInfo)
              course.courseWareUserLearningLog.push(courseWare)
            }
          })
        courseList.push(course)
      })
    }
    return courseList
  }
}
