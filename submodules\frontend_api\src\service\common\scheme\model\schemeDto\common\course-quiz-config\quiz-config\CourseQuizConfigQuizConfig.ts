import UserCoursePaperPublishConfigure from '@api/service/common/scheme/model/schemeDto/common/course-quiz-config/quiz-config/user-course-paper-publish-configure/UserCoursePaperPublishConfigure'

/**
 * @description 课后测验出卷配置
 */
class CourseQuizConfigQuizConfig {
  /**
   * 课后测验id
   */
  id: string
  /**
   * 课程测验名称
   */
  name: string
  /**
   * 操作类型
   */
  operation: number
  /**
   * 出卷模式,0 智能卷
   */
  publishPattern: number
  /**
   * 是否开放题析
   */
  openDissects: boolean
  /**
   * 是否限制测验次数
   */
  limitCourseQuizNum: boolean
  /**
   * 允许测验次数
   */
  allowCourseQuizNum: number
  /**
   * 多选题漏选得分模式，0 不得分 1 得全部分数 2 得一半分数 3 每个选项按平均得分
   */
  multipleMissScorePattern: number
  /**
   * 及格分
   */
  passScore: number
  /**
   * 用户测试发布配置
   */
  userCoursePaperPublishConfigure: UserCoursePaperPublishConfigure
}

export default CourseQuizConfigQuizConfig
