<template>
  <div>
    <!-- 学习心得 -->
    <el-card shadow="never" class="m-card f-mb15">
      <el-row :gutter="16" class="m-query is-border-bottom">
        <!--条件查询-->
        <el-form :inline="true">
          <el-col :sm="12" :md="4" :xl="5">
            <el-form-item label="主题">
              <el-select
                v-model="queryParams.theme"
                @focus="getThemeList"
                clearable
                filterable
                placeholder="请选择主题"
              >
                <template v-if="themeList.length">
                  <el-option
                    v-for="item in themeList"
                    :key="item.topicId"
                    :label="item.experienceTopicName"
                    :value="item.topicId"
                  ></el-option>
                </template>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="7" :xl="5">
            <el-form-item label="学习心得类型">
              <el-select v-model="queryParams.learningExperienceType" clearable filterable placeholder="全部">
                <el-option label="全部" value=""></el-option>
                <el-option label="班级心得" :value="LearningExperienceEnum.CLASS"></el-option>
                <el-option label="课程心得" :value="LearningExperienceEnum.COURSE"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="4" :xl="5">
            <el-form-item label="审核状态">
              <el-select v-model="queryParams.auditStatus" clearable filterable placeholder="全部">
                <el-option label="全部" :value="[]"></el-option>
                <el-option
                  label="已审核"
                  :value="[StudentLearningExperienceStatus.PASS, StudentLearningExperienceStatus.RETURNED]"
                ></el-option>
                <el-option label="待审核" :value="[StudentLearningExperienceStatus.SUBMITTED]"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="9" :xl="9" class="f-fr">
            <el-form-item class="f-tr">
              <el-button type="primary" @click="handleSearch">查询</el-button>
              <el-button @click="handleExport">导出列表数据</el-button>
              <el-button @click="handleAllExport">批量导出学习心得</el-button>
              <el-button @click="handleReset">重置</el-button>
            </el-form-item>
          </el-col>
        </el-form>
        <el-table
          ref="experienceTable"
          stripe
          :data="tableData"
          max-height="500px"
          class="m-table f-mt15"
          v-loading="loading"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="用户信息" min-width="230" fixed="left" align="center">
            <template slot-scope="{ row }"
              >姓名：{{ row.name }}
              <br />
              证件号：{{ row.idCard }}
            </template>
          </el-table-column>
          <el-table-column label="培训方案名称" min-width="220" align="center">
            <template slot-scope="{ row }">{{ row.schemeName }}</template>
          </el-table-column>
          <el-table-column label="主题" min-width="220" align="center">
            <template slot-scope="{ row }">{{ row.theme }}</template>
          </el-table-column>

          <el-table-column label="学习心得类型" min-width="120" align="center">
            <template slot-scope="{ row }">
              <span v-if="row.learningExperienceType === LearningExperienceEnum.CLASS">班级心得</span>
              <span v-if="row.learningExperienceType === LearningExperienceEnum.COURSE">课程心得</span>
            </template>
          </el-table-column>
          <el-table-column label="提交时间" min-width="240" align="center">
            <template slot-scope="{ row }">
              <span>{{ row.detail.submitTime }}</span>
            </template>
          </el-table-column>
          <el-table-column label="作答形式" min-width="100" align="center">
            <template slot-scope="{ row }">
              <span v-if="row.answerMethod === AnswerMethodEnum.UPLOAD">提交附件</span>
              <span v-if="row.answerMethod === AnswerMethodEnum.EDIT">在线编辑</span>
            </template>
          </el-table-column>
          <el-table-column label="审核方式" min-width="120" align="center">
            <template slot-scope="{ row }">
              <span v-if="row.approveMethod === ApproveMethodEnum.AUTO">提交自动通过</span>
              <span v-if="row.approveMethod === ApproveMethodEnum.ARTIFICIAL">人工审核</span>
            </template>
          </el-table-column>
          <el-table-column label="审核结果" width="130" align="center">
            <template slot-scope="{ row }">
              <template v-if="row.approveResult">
                <span v-if="row.approveResult === ApproveResultEnum.SUCCESS">通过</span>
                <span v-else-if="row.approveResult === ApproveResultEnum.FAIL">不通过</span>
                （{{ row.approveResultScore }}）
              </template>
              <span v-else>--</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template #default="{ row }">
              <el-button type="text" size="mini" @click="toDetail(row)"
                >{{ row.approveResult === ApproveStatusEnum.SUCCESS ? '详情' : '审核' }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-row>
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </el-card>
    <!-- 导出任务查看 -->
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务管理-学习心得{{ isAllExport ? '明细' : '列表' }}数据导出</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>

    <detail-dialog
      ref="detailDialogRef"
      :learningExperienceDetail="learningExperienceDetail"
      :learningExperienceId="learningExperienceId"
      @handleReLoad="handleReLoad"
    ></detail-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Prop } from 'vue-property-decorator'
  import ActivityManangerList from '@api/service/management/activity/ActivityManangerList'
  import ActivityManangeFilterModel from '@api/service/management/activity/models/ActivityManangeFilterModel'
  import ActivityManangeItemModel from '@api/service/management/activity/models/ActivityManangeItemModel'
  import {
    LearningExperienceEnum,
    AnswerMethodEnum,
    ApproveMethodEnum,
    ApproveResultEnum,
    ApproveStatusEnum
  } from '@api/service/management/activity/enum/ActivityEnum'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/news-date-picker.vue'
  import { UiPage } from '@hbfe/common'
  import {
    LearningExperienceTopicResponse,
    StudentLearningExperienceStatus
  } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
  import Constant from '@api/service/common/models/constant'
  import ActivityManangeDetailModel from '@api/service/management/activity/models/ActivityManangeDetailModel'
  import ActivityManangeModel from '@api/service/management/activity/ActivityManangeModel'
  import DetailDialog from '@hbfe/jxjy-admin-scheme/src/components/detail-dialog.vue'
  import { ElTable } from 'element-ui/types/table'

  @Component({
    computed: {
      StudentLearningExperienceStatus() {
        return StudentLearningExperienceStatus
      },
      ApproveStatusEnum() {
        return ApproveStatusEnum
      }
    },
    components: {
      DoubleDatePicker,
      DetailDialog
    }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.handleLoadData, this.handleLoadData)
    }

    @Prop({
      type: String,
      required: true
    })
    schemeId: string

    @Ref('detailDialogRef') detailDialogRef: DetailDialog
    @Ref('experienceTable') experienceTable: ElTable

    LearningExperienceEnum = LearningExperienceEnum
    AnswerMethodEnum = AnswerMethodEnum
    ApproveMethodEnum = ApproveMethodEnum
    ApproveResultEnum = ApproveResultEnum
    queryParams: ActivityManangeFilterModel = new ActivityManangeFilterModel()
    tableData: Array<ActivityManangeItemModel> = []
    ActivityManangerObj: ActivityManangerList = new ActivityManangerList()
    loading = false
    page: UiPage
    themeList: Array<LearningExperienceTopicResponse> = []
    exportSuccessVisible = false
    Constant = Constant
    isAllExport = false

    learningExperienceId = ''
    // 查看详情
    learningExperienceDetail = new ActivityManangeDetailModel()
    ActivityManangeModelObj = new ActivityManangeModel()

    async created() {
      await this.handleLoadData()
    }

    // 渲染列表
    async handleLoadData() {
      this.loading = true
      this.queryParams.schemeIds = [this.schemeId]
      this.ActivityManangerObj.filterItem = this.queryParams
      this.ActivityManangerObj.filterItem.themeId = this.queryParams.theme
      try {
        this.tableData = await this.ActivityManangerObj.queryList(this.page)
        console.log(this.tableData, 'dsadsa')
      } catch (error) {
        console.log(error, 'error')
        this.$message.error('experienceTable error')
      } finally {
        this.$nextTick(() => {
          ;(this.$refs['experienceTable'] as any)?.doLayout()
        })
      }

      this.loading = false
    }

    // 查询
    handleSearch() {
      this.page.pageNo = 1
      this.handleLoadData()
    }

    // 重置
    handleReset() {
      this.queryParams = new ActivityManangeFilterModel()
      this.ActivityManangerObj.filterItem = this.queryParams
      this.handleLoadData()
    }

    // 导出列表数据
    async handleExport() {
      if (!this.queryParams.schemeIds.length) {
        this.$alert('提示', {
          message: '请选择一个培训方案',
          confirmButtonText: '我知道了',
          type: 'warning'
        })
        return
      } else if (!this.queryParams.theme) {
        this.$alert('提示', {
          message: '请选择一个主题',
          confirmButtonText: '我知道了',
          type: 'warning'
        })
        return
      }
      this.ActivityManangerObj.filterItem.themeId = this.queryParams.theme
      const res = await this.ActivityManangerObj.exportList()
      if (res.status.code == 200 && res.data) {
        this.isAllExport = false
        this.exportSuccessVisible = true
        // this.$message.success('导出成功')
      } else {
        this.$message.warning('导出失败')
      }
    }

    /**
     * 导出任务下载
     */
    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: !this.isAllExport ? 'exportStudentLearningExperience' : 'exportStudentLearningExperienceDetail' }
      })
    }

    // 批量导出学习心得
    async handleAllExport() {
      if (!this.queryParams.schemeIds.length) {
        this.$alert('提示', {
          message: '请选择一个培训方案',
          confirmButtonText: '确定',
          type: 'warning'
        })
        return
      } else if (!this.queryParams.theme) {
        this.$alert('提示', {
          message: '请选择一个主题',
          confirmButtonText: '确定',
          type: 'warning'
        })
        return
      }
      this.ActivityManangerObj.filterItem.themeId = this.queryParams.theme
      const res = await this.ActivityManangerObj.exportLearningExpreList()

      if (res.status.code == 200 && res.data) {
        this.isAllExport = true
        this.exportSuccessVisible = true
      } else {
        this.$message.warning('导出失败')
      }
    }

    async toDetail(row: ActivityManangeItemModel) {
      console.log(row, '点击进入详情')
      this.learningExperienceDetail = row.detail
      this.learningExperienceId = row.id
      this.learningExperienceDetail.passScore = await this.ActivityManangeModelObj.requestClassConfig(row.schemeId)

      this.detailDialogRef.init()
    }

    // 获取主题列表
    async getThemeList() {
      this.queryParams.schemeIds = [this.schemeId]
      this.ActivityManangerObj.filterItem = this.queryParams
      this.themeList = await this.ActivityManangerObj.queryThemeList()
      // this.themeList = []
      const temp: string[] = []
      const indexList: number[] = []
      this.themeList = (this.themeList || []).filter((item, index) => {
        const topicId = item.topicId
        if (!temp.includes(topicId) && topicId) {
          temp.push(topicId)
          indexList.push(index)
        }
        return indexList.includes(index)
      })
    }

    handleReLoad() {
      setTimeout(() => {
        this.handleLoadData()
      }, 1000)
    }
  }
</script>
