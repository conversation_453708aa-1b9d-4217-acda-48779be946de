"""独立部署的微服务,K8S服务名:ms-autolearning-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""通过servicerId查询网校智能学习服务配置（状态正常）"""
	queryOnlineSchoolSmartLearningServiceConfig(servicerId:String):OnlineSchoolSmartLearningServiceConfigResponse
	"""通过servicerId查询网校智能学习服务配置(查询是否开通)"""
	queryOnlineSchoolSmartLearningServiceConfigByServicerId(servicerId:String):Int
	"""查询网校智能学习服务配置(查询是否开通)"""
	queryOnlineSchoolSmartLearningServiceConfigExist:Int
}
type IntegerRange @type(value:"com.fjhb.domain.learningscheme.support.IntegerRange") {
	min:Int!
	max:Int!
}
type TimeRange @type(value:"com.fjhb.domain.learningscheme.support.TimeRange") {
	min:String
	max:String
}
"""网校智能学习服务配置返回
	<AUTHOR>
	@date 2024/5/10 16:41
"""
type OnlineSchoolSmartLearningServiceConfigResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.OnlineSchoolSmartLearningServiceConfigResponse") {
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商ID(所属网校ID)"""
	servicerId:String
	"""网校智能学习服务ID"""
	onlineSchoolSmartLearningServiceId:String
	"""课程学习配置"""
	courseLearningConfigure:CourseLearningConfigure
	"""课程测验配置"""
	courseQuizConfigure:CourseQuizConfigure
	"""考试配置"""
	examConfigure:ExamConfigure
	"""服务状态
		@see ServiceStatus
	"""
	status:Int
	"""创建用户ID"""
	createUserId:String
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
"""课程学习配置
	<AUTHOR> By Cb
	@since 2024/05/10 9:06
"""
type CourseLearningConfigure @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.onlineschoolsmartlearningservice.model.CourseLearningConfigure") {
	"""每天学习时间区间列表"""
	everyDayLearningTimeList:[TimeRange]
	"""每天不学习时间列表"""
	everyDayNotLearningTimeList:[TimeRange]
	"""首次开始学习时间 (报名后的X天 - X天)"""
	firstLearningDayRange:IntegerRange
	"""每天学习时长区间(秒)"""
	everyDayLearningTimeRange:IntegerRange
	"""每次学习时长区间(秒)"""
	everyLearningTimeRange:IntegerRange
	"""休息时长区间(秒) 达到每次学习时长区间后休息-随机休息60~180分钟"""
	restTimeRange:IntegerRange
	"""是否启用
		默认:true
	"""
	enable:Boolean
	"""每天学习学时区间"""
	everyDayLearningPeriodRange:IntegerRange
	"""每次学习学时区间"""
	everyLearningPeriodRange:IntegerRange
	"""规则类型
		0-按课程物理时长 1-按课程学习学时 2-固定时长 3-按学习约束限制
	"""
	ruleType:Int!
}
"""课程测验配置
	<AUTHOR> By Cb
	@since 2024/05/10 9:12
"""
type CourseQuizConfigure @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.onlineschoolsmartlearningservice.model.CourseQuizConfigure") {
	"""答题时长区间(秒)  默认 900s - 3600s (15-60分钟)"""
	answerTimeRange:IntegerRange
	"""是否启用
		默认:true
	"""
	enable:Boolean
}
"""考试配置
	<AUTHOR> By Cb
	@since 2024/05/10 9:14
"""
type ExamConfigure @type(value:"com.fjhb.ms.autolearning.v1.kernel.repository.onlineschoolsmartlearningservice.model.ExamConfigure") {
	"""答题时长区间(秒)  (最少考试总时长1/3)"""
	answerTimeRange:IntegerRange
	"""时长占比(占配置时长的比例)"""
	durationRatio:Double
	"""是否启用
		默认:true
	"""
	enable:Boolean
}

scalar List
