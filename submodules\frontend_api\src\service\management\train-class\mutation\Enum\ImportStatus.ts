import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 导入状态枚举
 * not_yet 未导入
 * importing 导入中
 * success 导入成功
 * fail 导入失败
 */
export enum ImportStatusEnum {
  not_yet = 'not_yet',
  importing = 'importing',
  success = 'success',
  fail = 'fail'
}

/**
 * @description 导入状态
 */
class ImportStatus extends AbstractEnum<ImportStatusEnum> {
  static enum = ImportStatusEnum

  constructor(status?: ImportStatusEnum) {
    super()
    this.current = status
    this.map.set(ImportStatusEnum.not_yet, '未导入')
    this.map.set(ImportStatusEnum.importing, '导入中')
    this.map.set(ImportStatusEnum.success, '导入成功')
    this.map.set(ImportStatusEnum.fail, '导入失败')
  }
}

export default new ImportStatus()
