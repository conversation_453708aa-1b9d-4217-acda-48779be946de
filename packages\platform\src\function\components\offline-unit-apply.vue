<template>
  <div>
    <el-card shadow="never" class="m-card f-mb15">
      <el-row type="flex" justify="center" class="width-limit">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form
            ref="offlineApplyForm"
            :model="collectiveSignUp"
            :rules="rules"
            label-width="180px"
            class="m-form f-mt20"
          >
            <el-form-item label="线下集体报名入口：" required prop="enable">
              <el-switch v-model="collectiveSignUp.enable" active-text="开启" inactive-text="关闭" class="m-switch" />
              <el-tooltip effect="dark" placement="right" popper-class="m-tooltip">
                <i class="el-icon-info m-tooltip-icon f-co f-ml15"></i>
                <div slot="content">当前配置仅针对网校，专题线下集体报名入口配置请前往专题编辑页面。</div>
              </el-tooltip>
            </el-form-item>
            <template v-if="collectiveSignUp.enable">
              <el-form-item label="线下集体报名入口图片：">
                <cropper-img-upload
                  v-model="collectiveSignUp.offlineEnterPhoto.url"
                  :dialogStyleOpation="{
                    width: `${picSize[0] + 100}px`,
                    height: '240px'
                  }"
                  :ratioArr="[`${picSize[0]}:${picSize[1]}`]"
                  :initWidth="picSize[0]"
                  title="上传线下集体报名入口图片"
                  button-text="上传线下集体报名入口图片"
                  :is-long-pic="true"
                  reminder-text="只支持JPG、PNG、GIF"
                  :has-preview="false"
                >
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt">
                      上传线下集体报名入口图片，尺寸：宽度 {{ picSize[0] }} px ， 高度
                      {{ picSize[1] }} px，不上传则显示模板默认图片。
                      <i class="f-link" @click="handlePictureExample">示例图片</i>
                    </span>
                    <!--示例图片弹窗-->
                    <el-dialog :visible.sync="pictureVisible" width="1100px" class="m-dialog-pic">
                      <!--分别读取对应的默认图片-->
                      <img :src="exampleImg" alt="" />
                    </el-dialog>
                  </div>
                </cropper-img-upload>
              </el-form-item>
              <el-form-item label="线下集体报名名称：" prop="title">
                <el-input v-model="collectiveSignUp.title" clearable placeholder="请输入线下集体报名名称" />
              </el-form-item>
              <el-form-item label="线下集体报名模板：" required>
                <min-upload-file v-model="hbFileUploadResponse" :file-type="1">
                  <el-button slot="trigger" type="primary" plain>点击上传模板</el-button>
                </min-upload-file>
                <div class="el-upload__tip f-pt5">
                  <div class="txt"><i class="f-link" @click="downloadTemplate">下载示例模版</i></div>
                </div>
              </el-form-item>
              <el-form-item label="访问链接：" required>
                {{ mutationCollectiveSignUp.collectiveSignUp.signUpClassUrl }}
                <hb-copy :content="mutationCollectiveSignUp.collectiveSignUp.signUpClassUrl"></hb-copy>
              </el-form-item>
              <el-form-item label="底部文本说明：" prop="footContent">
                <el-input
                  v-model.number="collectiveSignUp.footContent"
                  clearable
                  placeholder="填写集体报名联系电话，如：如报名过程有问题，请咨询服务热线：968823"
                />
              </el-form-item>
              <el-form-item label="报名步骤：" required>
                <div class="step f-mb20" v-for="(item, index) in collectiveSignUp.steps" :key="index">
                  <div class="f-flex f-align-center">
                    <div class="f-flex-sub">
                      <span class="f-cb f-fb"><i class="f-dot f-mr5"></i>第{{ numToChinaNum(index + 1) }}步</span>
                      <el-input v-model="item.name" clearable placeholder="请输入步骤的标题" class="form-l f-ml10" />
                    </div>
                    <el-button
                      size="mini"
                      type="danger"
                      :disabled="collectiveSignUp.steps.length == 1"
                      plain
                      class="f-fr"
                      @click="sureDeleteDialog(item.no)"
                      >删除</el-button
                    >
                  </div>
                  <div class="rich-text f-mt10">
                    <hb-tinymce-editor v-model="item.content" v-if="show"></hb-tinymce-editor>
                  </div>
                </div>
                <el-button type="primary" plain icon="el-icon-plus" @click="addStep">添加步骤</el-button>
              </el-form-item>
            </template>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
    <div class="m-btn-bar f-tc is-sticky-1">
      <el-button @click="doCancel">取消</el-button>
      <el-button @click="preview">预览</el-button>
      <el-button type="primary" @click="validForm" :loading="loading">保存</el-button>
    </div>
    <el-dialog title="提示" :visible.sync="deleteDialog" width="450px" class="m-dialog">
      <div class="dialog-alert">
        <i class="icon el-icon-error error"></i>
        <span class="txt">确定删除该内容吗？删除后不可恢复</span>
      </div>
      <div slot="footer">
        <el-button @click="deleteDialog = false">取 消</el-button>
        <el-button type="primary" @click="deleteStep">确 定</el-button>
      </div>
    </el-dialog>
    <give-up-dialog :give-up-model="uiConfig.giveUpModel" @callBack="resetData"></give-up-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Prop, Watch } from 'vue-property-decorator'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import MinUploadFile from '@hbfe/jxjy-admin-components/src/min-upload-file.vue'
  import GiveUpDialog from '@hbfe/jxjy-admin-components/src/give-up-operation.vue'
  import GiveUpCommonModel from '@hbfe/jxjy-admin-components/src/models/GiveUpCommonModel'
  import MutationCollectiveSignUp from '@api/service/management/online-school-config/functionality-setting/mutation/MutationCollectiveSignUp'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import { CollectiveSignUpTypeEnum } from '@api/service/common/enums/online-school-config/CollectiveSignUpType'
  import OfflineCollectSignUpVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/CollectiveSignUp/OfflineCollectSignUpVo'
  import StepVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/CollectiveSignUp/StepVo'
  import Context from '@api/service/common/context/Context'
  import CropperImgUpload from '@hbfe/jxjy-admin-platform/src/function/components/cropper-img-upload.vue'
  import TemplateItem from '@api/service/common/template-school/TemplateItem'
  import UnitApplyPicSizeTypeModel from '@hbfe/jxjy-admin-platform/src/function/vuex/UnitApplyPicSizeTypeModel'
  import { bind, debounce } from 'lodash-decorators'
  import { ElForm } from 'element-ui/types/form'

  export class ApplyStep {
    title: string
    content: string
  }

  @Component({
    components: { MinUploadFile, GiveUpDialog, CropperImgUpload }
  })
  export default class extends Vue {
    @Ref('offlineApplyForm') offlineApplyForm: ElForm
    //文件上传之后的回调参数
    hbFileUploadResponse = new HBFileUploadResponse()
    uiConfig = {
      giveUpModel: new GiveUpCommonModel()
    }
    mutationCollectiveSignUp: MutationCollectiveSignUp =
      OnlineSchoolConfigModule.mutationFunctionalitySettingFactory.collectiveSignUp(CollectiveSignUpTypeEnum.OFFLINE)
    collectiveSignUp = new OfflineCollectSignUpVo()

    rules = {
      title: [
        {
          required: true,
          message: '请输入线下集体报名名称',
          trigger: ['change', 'blur']
        }
      ],
      footContent: [
        {
          required: true,
          message: '请输入底部文本说明',
          trigger: ['change', 'blur']
        }
      ]
    }
    /**
     * 示例图片显示
     */
    pictureVisible = false
    // 富文本有时候显示不出来
    show = false

    //删除弹窗
    deleteDialog = false

    //当前删除步骤
    curStepIndex = 0
    /**
     * 保存加载
     */
    loading = false

    //报名步骤
    //applyStepList = new Array<StepVo>()

    /**
     * 模板
     */
    @Prop({
      required: true,
      default: () => {
        return new TemplateItem()
      }
    })
    TemplateModuleObj: TemplateItem
    /**
     * 获取图片尺寸
     */
    get picSize() {
      return UnitApplyPicSizeTypeModel.isSingle
        ? this.TemplateModuleObj.singleOfflineCollectSize
        : this.TemplateModuleObj.doubleOfflineCollectSize
    }
    /**
     * 示例图片
     */
    get exampleImg() {
      const src = UnitApplyPicSizeTypeModel.isSingle
        ? this.TemplateModuleObj.singleOfflineExampleImgSrc
        : this.TemplateModuleObj.doubleOfflineExampleImgSrc
      return require('@design/admin/assets/images/demo-enter-pic/' + src)
    }
    /**
     * 监听入口状态，更改图片尺寸
     */
    @Watch('collectiveSignUp.enable')
    onEntryStatusChange(val: boolean) {
      UnitApplyPicSizeTypeModel.setOfflinePicEnable(val)
    }
    /**
     * 示例图片
     */
    handlePictureExample() {
      this.pictureVisible = true
    }
    doCancel() {
      this.uiConfig.giveUpModel.giveUpCtrl = true
    }
    @debounce(200)
    @bind
    async doSave() {
      if (this.mutationCollectiveSignUp.collectiveSignUp.enable) {
        if (
          (this.mutationCollectiveSignUp.collectiveSignUp as OfflineCollectSignUpVo).steps.some(
            (step) => step.name == ''
          )
        ) {
          this.$message.warning('请输入步骤的标题')
          return
        }
        if (!this.hbFileUploadResponse.url || this.hbFileUploadResponse.url == '') {
          this.$message.warning('请上传线下集体报名模板')
          return
        }
        this.loading = true
      }
      if (this.hbFileUploadResponse.url) {
        this.mutationCollectiveSignUp.collectiveSignUp.templatePath = this.hbFileUploadResponse.url
        this.mutationCollectiveSignUp.collectiveSignUp.templateName = this.hbFileUploadResponse.fileName
      }

      const res = await this.mutationCollectiveSignUp.doSave()
      if (res.isSuccess()) {
        this.$message.success('保存成功')
        UnitApplyPicSizeTypeModel.setOfflinePicEnable(this.mutationCollectiveSignUp.collectiveSignUp.enable)
        UnitApplyPicSizeTypeModel.setOfflinePicExist(
          !!(this.mutationCollectiveSignUp.collectiveSignUp as OfflineCollectSignUpVo).offlineEnterPhoto.url
        )
        const change = UnitApplyPicSizeTypeModel.isOfflinePicChange
        await this.getOfflineApplyInfo()
        this.loading = false
        if (change) {
          this.$confirm('线上集体报名入口图片尺寸变更，是否立即前往调整？', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          }).then(() => {
            this.$emit('update:activeName', 'first')
          })
        }
      } else {
        this.$message.error('保存失败')
      }
    }

    validForm() {
      this.offlineApplyForm.validate((valid: boolean) => {
        if (valid) {
          this.doSave()
        }
      })
    }

    //预览
    preview() {
      const collectiveSignUp = JSON.stringify(this.mutationCollectiveSignUp.collectiveSignUp)
      localStorage.setItem('offlineApply', collectiveSignUp)
      window.open(`/collective-apply-preview/index`, '_blank')
    }

    numToChinaNum(num: number) {
      //将数字（整数）转为汉字，从零到一亿亿
      const arr1 = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
      const arr2 = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿'] //可继续追加更高位转换值
      if (!num || isNaN(num)) {
        return '零'
      }
      const english = num.toString().split('')
      let result = ''
      for (let i = 0; i < english.length; i++) {
        const desI = english.length - 1 - i //倒序排列设值
        result = arr2[i] + result
        const arr1Index = english[desI]
        result = arr1[arr1Index] + result
      }
      //将【零千、零百】换成【零】 【十零】换成【十】
      result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十')
      //合并中间多个零为一个零
      result = result.replace(/零+/g, '零')
      //将【零亿】换成【亿】【零万】换成【万】
      result = result.replace(/零亿/g, '亿').replace(/零万/g, '万')
      //将【亿万】换成【亿】
      result = result.replace(/亿万/g, '亿')
      //移除末尾的零
      result = result.replace(/零+$/, '')
      //将【零一十】换成【零十】
      //result = result.replace(/零一十/g, '零十');//貌似正规读法是零一十
      //将【一十】换成【十】
      result = result.replace(/^一十/g, '十')
      return result
    }
    //下载模板
    downloadTemplate() {
      const link = document.createElement('a')
      const resolver = this.$router.resolve({
        name: '/mfs/resource/file/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/template/offlineCollectiveRegisterTemplate.xls'
      })

      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      link.setAttribute('download', 'xxx单位学员集体报名名单及开票信息表')
      document.body.appendChild(link)
      link.click()
      link.remove()
    }

    async resetData() {
      //重新获取数据
      await this.getOfflineApplyInfo()
    }
    //获取线下报名信息
    async getOfflineApplyInfo() {
      await Context.queryCurrentServicerInfo()
      const res = await this.mutationCollectiveSignUp.queryDetail()
      if (res.isSuccess()) {
        //console.log(this.mutationCollectiveSignUp.collectiveSignUp)
        this.hbFileUploadResponse = new HBFileUploadResponse()
        this.hbFileUploadResponse.url = this.mutationCollectiveSignUp.collectiveSignUp.templatePath
        this.hbFileUploadResponse.fileName = this.mutationCollectiveSignUp.collectiveSignUp.templateName
        //;(this.mutationCollectiveSignUp.collectiveSignUp as OfflineCollectSignUpVo).title = ''
        if ((this.mutationCollectiveSignUp?.collectiveSignUp as OfflineCollectSignUpVo)?.title === '') {
          ;(this.mutationCollectiveSignUp.collectiveSignUp as OfflineCollectSignUpVo).title =
            (Context?.servicerInfo?.name || '') + '集体报名说明'
        }
        if (!(this.mutationCollectiveSignUp?.collectiveSignUp as OfflineCollectSignUpVo)?.steps?.length) {
          //如果没拿到步骤数据，使用默认步骤内容
          this.initStep()
        }
        this.collectiveSignUp = this.mutationCollectiveSignUp.collectiveSignUp as OfflineCollectSignUpVo
        UnitApplyPicSizeTypeModel.setOriginOfflinePicEnable(this.mutationCollectiveSignUp.collectiveSignUp.enable)
        UnitApplyPicSizeTypeModel.setOfflinePicExist(
          !!(this.mutationCollectiveSignUp.collectiveSignUp as OfflineCollectSignUpVo).offlineEnterPhoto.url
        )
      }
    }

    //新增步骤
    addStep() {
      //const step = new StepVo()
      //this.applyStepList.push(step)
      ;(this.mutationCollectiveSignUp.collectiveSignUp as OfflineCollectSignUpVo).addStep()
    }
    //确认删除步骤弹窗
    sureDeleteDialog(index: number) {
      this.curStepIndex = index
      this.deleteDialog = true
    }
    //删除步骤
    deleteStep() {
      ;(this.mutationCollectiveSignUp.collectiveSignUp as OfflineCollectSignUpVo).removeStep(this.curStepIndex)
      //this.applyStepList.splice(this.curStepIndex, 1)
      this.deleteDialog = false
    }

    //设置默认步骤
    initStep() {
      ;(this.mutationCollectiveSignUp.collectiveSignUp as OfflineCollectSignUpVo).steps = [
        {
          no: 0,
          name: '请下载 “ XXX单位学员报名及开票信息表 ” 模板',
          content:
            '<p>（1）填写学员信息及所学班级；</p>' +
            '<p>（2）填写电子发票相关信息（开具发票使用）。</p>' +
            '<p style="color:red">注：单位集体报名的，发票统一开具</p>'
        },
        {
          no: 1,
          name: '汇款至xxxxx有限公司对公账户',
          content:
            '<p>收款人：xxxx有限公司</p>' +
            '<p>账号：350010024033333333</p>' +
            '<p>开户银行：中国建设银行股份有限公司福建省分行营业部</p>' +
            '<p style="color:red"></p>'
        },
        {
          no: 2,
          name: '发送学员及开票信息表和汇款凭证',
          content:
            '<p>邮箱：<EMAIL></p>' +
            '<p>客服电话：968-823</p>' +
            '<p style="color:red">注：系统将在收到报名开票信息表及汇款凭证后的3个工作日内开通班级。</p>'
        },
        {
          no: 3,
          name: '完成集体报名',
          content:
            '<p>（1）报名联系人将收到学员报名完成的反馈表；</p>' +
            '<p>（2）发送电子发票至报名负责人邮箱</p>' +
            '<p style="color:red">注：电子发票将在开通班级后的七个工作日开具。</p>'
        }
      ]
    }

    async init() {
      this.show = false
      setTimeout(() => {
        this.show = true
      }, 300)
    }

    async created() {
      await this.init()
      await this.getOfflineApplyInfo()
    }
  }
</script>
