<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2023-06-14 14:20:46
 * @LastEditors: <PERSON>
 * @LastEditTime: 2023-07-27 15:37:11
-->
<template>
  <router-view></router-view>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import systemContext from '@api/service/common/context/Context'
  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication, ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { TaskStatusEnum } from '@api/service/management/intelligence-learning/enum/TaskStatusEnum'
  import ExecutionListInServicer from '@api/service/management/intelligence-learning/ExecutionListInServicer'
  import moment from 'moment/moment'
  import { Events } from '@api/service/common/timer/TimerTask'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryLoginStatus from '@api/service/management/user/query/manager/QueryLoginStatus'

  @Component
  export default class extends Vue {
    created() {
      const _this = this as any
      if (this.$util?.channel) {
        this.$util.channel.onmessage = function (event) {
          // 白名单，不做权限控制，直接跳转
          const whiteList = [
            'errorPage',
            'loginPage',
            'forgetPage',
            'bindingMzt',
            'autoMzt',
            'binding3rd',
            'onlineSchoolReminder',
            'welcome'
          ]
          if (
            event.data.message === 'login-success' &&
            event.data.type === 'admin' &&
            _this.$authentication.getAccessToken() &&
            !whiteList.includes(_this.$route.name)
          )
            _this
              .$confirm('您已登录其他账号，前一个登录状态自动登出，请刷新页面。', {
                showClose: false,
                closeOnClickModal: false,
                closeOnHashChange: false,
                closeOnPressEscape: false,
                showCancelButton: false,
                confirmButtonText: '刷新页面'
              })
              .then(() => {
                location.reload()
              })
        }
      }
    }

    get isLogin() {
      return QueryLoginStatus.isLogin
    }

    async event() {
      const timerTask = this.$showIntelligentLearningTaskFeedback.getTimerTask()
      timerTask.stop()
      const isLoginPage =
        window.location.href.includes('/login') || window.location.href.includes('/specialSubjectLogin')
      if (isLoginPage) {
        return
      }
      const lastFeedBackTime = localStorage.getItem('lastFeedBackTime')

      //获取当前时间戳
      const now = moment().format('x')
      localStorage.setItem('lastFeedBackTime', now)
      const res = await ExecutionListInServicer.queryCountByStatusAndTime({
        // startTime: lastFeedBackTime ? moment(Number(lastFeedBackTime)).format('YYYY-MM-DD HH:mm:ss') : undefined,
        // endTime: lastFeedBackTime ? moment(Number(now)).format('YYYY-MM-DD HH:mm:ss') : undefined,
        status: [TaskStatusEnum.fail, TaskStatusEnum.stop]
      })
      if (res.status.isSuccess() && res.data) {
        this.$showIntelligentLearningTaskFeedback.show({
          // isAdditional: !!lastFeedBackTime,
          count: res.data
        })
      } else if (res.data === 0) {
        timerTask.start()
      }
    }

    async handleVisibilityChange() {
      console.log('handleVisibilityChange')

      if (!this.isLogin) {
        return
      }

      if (this.$showIntelligentLearningTaskFeedback.isMounted()) {
        return
      }
      const lastFeedBackTime = localStorage.getItem('lastFeedBackTime')
      // 已经点击过去处理，则本次登录不再弹窗
      if (lastFeedBackTime && lastFeedBackTime === 'Feedback_Handled') {
        return
      }
      // 还未引导过也不触发
      if (!localStorage.getItem('isFirstGuide')) {
        return
      }

      const now = moment().format('x')
      const diff = moment(Number(now)).diff(moment(Number(lastFeedBackTime)), 'milliseconds')

      console.log('diff', diff)
      const timerTask = this.$showIntelligentLearningTaskFeedback.getTimerTask()
      if (timerTask) {
        timerTask.off(Events.timeupdate, this.event)
        timerTask.once(Events.timeupdate, this.event)
      }

      const isLoginPage =
        window.location.href.includes('/login') || window.location.href.includes('/specialSubjectLogin')
      if (isLoginPage) {
        return
      }

      // 已经引导过，并且lastFeedBackTime不存在时，弹窗
      if (
        !lastFeedBackTime &&
        localStorage.getItem('isFirstGuide') &&
        localStorage.getItem('isFirstGuide') === 'notFirstGuide'
      ) {
        const res = await ExecutionListInServicer.queryCountByStatusAndTime({
          status: [TaskStatusEnum.fail, TaskStatusEnum.stop]
        })
        localStorage.setItem('lastFeedBackTime', moment().format('x'))
        if (res.status.isSuccess() && res.data > 0) {
          this.$showIntelligentLearningTaskFeedback.show({
            // isAdditional: !!lastFeedBackTime,
            count: res.data
          })
        } else if (res.data === 0) {
          timerTask.start()
        }

        return
      }

      // 如果diff大于15分钟
      if (diff >= this.$showIntelligentLearningTaskFeedback.duration) {
        const res = await ExecutionListInServicer.queryCountByStatusAndTime({
          // startTime: moment(Number(lastFeedBackTime)).format('YYYY-MM-DD HH:mm:ss'),
          // endTime: moment(Number(now)).format('YYYY-MM-DD HH:mm:ss'),
          status: [TaskStatusEnum.fail, TaskStatusEnum.stop]
        })
        if (res.status.isSuccess()) {
          localStorage.setItem('lastFeedBackTime', now)
          if (res.data > 0) {
            // 如果有新增的失败任务，则弹窗
            this.$showIntelligentLearningTaskFeedback.show({
              // isAdditional: !!lastFeedBackTime,
              count: res.data
            })
          } else {
            // 不存在新增失败任务，则定时检查
            if (!timerTask) {
              console.error('定时器timerTask is null:', timerTask)
              return
            }
            timerTask.start()
          }
        }
      } else {
        // 如果diff小于15分钟，则继续倒计时
        if (!timerTask) {
          console.error('定时器timerTask is null:', timerTask)
          return
        }
        timerTask.start(diff)
      }
    }

    async mounted() {
      try {
        // 判断是否是网校管理员并且存在弹窗权限
        // 硬编码，如果菜单发生变化，需要调整
        const isLoginPage =
          window.location.href.includes('/login') || window.location.href.includes('/specialSubjectLogin')
        console.log('isLoginPage', isLoginPage)
        if (
          QueryManagerDetail.hasCategory(CategoryEnums.wxgly) &&
          this.$hasPermission('training.intelligent-learning.learning-task-tracking.ZNXXFB', true) &&
          !isLoginPage
        ) {
          this.$showIntelligentLearningTaskFeedback.on('goHandle', () => {
            if (
              this.$route?.path === '/training/intelligent-learning/learning-task-tracking' &&
              this.$route?.query?.active === 'execution'
            ) {
              return
            }
            this.$router
              ?.push({
                path: '/training/intelligent-learning/learning-task-tracking',
                query: { active: 'execution' }
              })
              .catch((err) => {
                console.log(err)
              })
          })
          // 组件挂载时立即执行一次
          this.handleVisibilityChange()

          // 添加可见性变化监听（仅在标签页变为可见时触发）
          document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible') {
              this.handleVisibilityChange()
            } else {
              console.log('标签页变为不可见关闭定时器')
              const timerTask = this.$showIntelligentLearningTaskFeedback.getTimerTask()
              timerTask.stop()
            }
          })
        }

        const res = await OnlineSchoolConfigModule.queryPortal.queryDetail()
        // await systemContext.buildContext()
        // 服务商id
        const serviceId = CommonConfigCenter.getFrontendApplication(frontendApplication.trainingInstitutionServicerId)
        const isOperation = systemContext.servicerInfo.id === serviceId
        if (res.isSuccess()) {
          const link = document.querySelector('link[rel="icon"]') as any
          const title = document.getElementsByTagName('title')[0] as any
          title.innerHTML = isOperation
            ? '8.0通用平台运营管理后台'
            : OnlineSchoolConfigModule.queryPortal?.webPortalInfo?.title
          link.href = OnlineSchoolConfigModule.queryPortal?.webPortalInfo?.icon
        }
      } catch (e) {
        console.log(e)
      }
    }

    beforeDestroy() {
      document.removeEventListener('visibilitychange', this.handleVisibilityChange)
    }
  }
</script>
<style>
  .tox-dialog {
    z-index: 20005 !important;
  }
  .tox-tinymce-aux {
    z-index: 20005 !important;
  }
</style>
