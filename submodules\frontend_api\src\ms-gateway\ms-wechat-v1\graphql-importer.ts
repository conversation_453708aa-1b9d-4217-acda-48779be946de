import applyFacialRecognitionVerify from './queries/applyFacialRecognitionVerify.graphql'
import getAccessToken from './queries/getAccessToken.graphql'
import getAppletCode from './queries/getAppletCode.graphql'
import getDecodeTelephone from './queries/getDecodeTelephone.graphql'
import getUserInfo from './queries/getUserInfo.graphql'
import getWXAppletUserInfo from './queries/getWXAppletUserInfo.graphql'
import validFacialRecognitionVerifyResult from './queries/validFacialRecognitionVerifyResult.graphql'
import applyJsSDKSignature from './mutates/applyJsSDKSignature.graphql'

export {
  applyFacialRecognitionVerify,
  getAccessToken,
  getAppletCode,
  getDecodeTelephone,
  getUserInfo,
  getWXAppletUserInfo,
  validFacialRecognitionVerifyResult,
  applyJsSDKSignature
}
