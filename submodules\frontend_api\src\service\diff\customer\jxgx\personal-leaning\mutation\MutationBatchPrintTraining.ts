import { Response, ResponseStatus } from '@hbfe/common'
import PrintCertificateRequestVo from './vo/PrintCertificateRequestVo'
import PrintCertResponse from '@api/service/diff/customer/jxgx/personal-leaning/mutation/vo/PrintCertResponse'
import {
  default as certificateDiff,
  Extend,
  SinglePrintCertificateReponse,
  SinglePrintCertificateRequest
} from '@api/diff-gateway/platform-jxjypxtypt-jxgx-certificate'
import PlatformJxjypxtyptJxgxUserAuth, {
  CheckUserAuthInfoRequest
} from '@api/diff-gateway/platform-jxjypxtypt-jxgx-user-auth'

class MutationBatchPrintTraining {
  // 打印证明请求参数
  printCertificateParams = new PrintCertificateRequestVo()

  /**
   * @description: 打印/预览 单个证明
   * @param {String} param 学号
   */
  async doBatchPrintTraining(): Promise<Response<PrintCertResponse>> {
    const request = new SinglePrintCertificateRequest()
    request.qualificationId = this.printCertificateParams.qualificationId
    request.studentNo = this.printCertificateParams.qualificationId
    request.fileType = this.printCertificateParams.fileType
    request.schemeId = this.printCertificateParams.schemeId
    request.data = new Array<Extend>()
    if (this.printCertificateParams.idCard) {
      const requestExtend = new Extend()
      requestExtend.key = 'idCard'
      requestExtend.value = this.printCertificateParams.idCard
      request.data.push(requestExtend)
    }
    if (this.printCertificateParams.name) {
      const requestExtend = new Extend()
      requestExtend.key = 'userName'
      requestExtend.value = this.printCertificateParams.name
      request.data.push(requestExtend)
    }
    if (this.printCertificateParams.idCard && this.printCertificateParams.name) {
      const requestExtend = new Extend()
      requestExtend.key = 'isStudentPrint'
      requestExtend.value = 'true'
      request.data.push(requestExtend)
    }
    const result = new Response<PrintCertResponse>()
    result.status = new ResponseStatus(200)
    result.data = new PrintCertResponse()
    const { status, data } = await certificateDiff.studentPrintCertificate(request)
    result.status = status
    if (data) {
      result.data = PrintCertResponse.from(data)
    }
    return result
  }

  /**
   * 判断是否允许打印
   */
  async isAllowPrint(): Promise<boolean> {
    const checkUserAuthInfoRequest = new CheckUserAuthInfoRequest()
    checkUserAuthInfoRequest.idCard = this.printCertificateParams.idCard
    const res = await PlatformJxjypxtyptJxgxUserAuth.checkUserAuthInfo(checkUserAuthInfoRequest)
    if (res.status.isSuccess()) {
      return res.data
    }
    return false
  }
}

export default MutationBatchPrintTraining
