<template>
  <el-card
    shadow="never"
    class="m-card f-mb15"
    v-if="$hasPermission('Delivery')"
    desc="查询快递备注信息"
    actions="activated"
  >
    <div class="f-ptb20">
      <el-row type="flex" justify="center">
        <el-col :span="12">
          <el-card shadow="never" class="m-card is-header">
            <div class="m-tit is-border-bottom bg-gray">
              <div class="tit-txt f-flex-sub">备注信息</div>
              <a
                href="#"
                class="f-link f-underline f-cb"
                v-if="$hasPermission('attemptModify')"
                desc="添加自取点"
                actions="attemptModify,changeRemark"
                @click="attemptModify"
                >修改</a
              >
            </div>
            <el-empty v-if="!queryExpress.detail.remark" description="暂无备注"></el-empty>
            <p v-else class="f-p20 f-mh100">{{ queryExpress.detail.remark }}</p>
          </el-card>
        </el-col>
      </el-row>
    </div>
    <div class="f-p15">
      <el-drawer title="修改备注" :visible.sync="isShowDialog" size="700px" custom-class="m-drawer">
        <div class="drawer-bd">
          <el-row type="flex" justify="center">
            <el-col :span="22">
              <el-form ref="form" label-width="auto" class="m-form f-mt20">
                <el-form-item label="备注：" required>
                  <el-input type="textarea" v-model="remark" :rows="20" placeholder="请输入备注信息" />
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button @click="isShowDialog = false">取消</el-button>
                  <el-button type="primary" @click="changeRemark">确定</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-drawer>
    </div>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import QueryExpress from '@api/service/management/online-school-config/distribution-channels-config/query/QueryExpress'
  import MutationUpdateExpressRemark from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationUpdateExpressRemark'
  import MutationCreateExpressRemark from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationCreateExpressRemark'

  @Component
  export default class extends Vue {
    isShowDialog = false
    queryExpress: QueryExpress = new QueryExpress()

    remark = ''

    // 生命周期

    async activated() {
      await this.queryExpress.queryExpressDetail()
    }

    updateExpressRemark: MutationUpdateExpressRemark

    attemptModify() {
      this.isShowDialog = true
      this.updateExpressRemark = OnlineSchoolConfigModule.mutationDistributionChannelsConfigFactory.updateExpressRemark(
        this.queryExpress.detail.id,
        this.queryExpress.detail.remark
      )
      this.remark = this.queryExpress.detail.remark
    }

    // 修改快递备注
    async changeRemark() {
      if (this.queryExpress.hasExpressRemark()) {
        this.updateExpressRemark.updateExpressDetail.remark = this.remark
        const status = await this.updateExpressRemark.doUpdate()
        if (status.isSuccess()) {
          this.queryExpress.detail.remark = this.remark
          this.$message({
            message: '修改成功',
            type: 'success'
          })
          await this.queryExpress.queryExpressDetail()
        } else {
          this.$message({
            message: '修改失败,请刷新重试！',
            type: 'error'
          })
          const mutationCreate = new MutationCreateExpressRemark(this.remark)
          await mutationCreate.doCreate()
        }
      } else {
        const mutationCreate = new MutationCreateExpressRemark(this.remark)
        const status = await mutationCreate.doCreate()
        if (status.isSuccess()) {
          this.queryExpress.detail.remark = this.remark
          this.$message({
            message: '修改成功',
            type: 'success'
          })
        }
        await this.queryExpress.queryExpressDetail()
      }

      this.isShowDialog = false
    }
  }
</script>
