/*
 * @Description: 分页请求参数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:49:36
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-11 08:35:16
 */
import {
  BillStatusChangeTimeRequest,
  OnlineInvoiceRequest,
  OnlineInvoiceBasicDataRequest,
  OrderInfoRequest,
  OnlineInvoiceItemRequest,
  InvoiceAssociationInfoRequest,
  DateScopeRequest,
  InvoiceStatusChangeTimeRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

import { TitleTypeEnum, InvoiceStatusEnum, InvoiceCategoryEnum, InvoiceTypeEnum } from '../../enum/InvoiceEnum'
import SaleChannelType from '@api/service/common/enums/trade/SaleChannelType'
export default class QueryPageInvoiceParam {
  //*****************发票基础信息查询参数 basicData********************/
  /**
   * 发票id集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票类型 1:电子发票 2:纸质发票
   */
  invoiceType = 1
  /**
   * 发票种类 1:普通发票 2:增值税普通发票 3:增值税专用发票
   */
  invoiceCategoryList = [2]
  /**
   * 商品id集合-培训方案
   */
  commoditySkuIdList?: string
  /**
   * 蓝票票据开具状态 0:未开具 1：开票中 2：开票成功 3：开票失败   --- 蓝票的发票状态筛选
   */
  blueInvoiceItemBillStatusList?: InvoiceStatusEnum
  /**
   * 红票票据开具状态 0:未开具 1：开票中 2：开票成功 3：开票失败   --- 红票的发票状态筛选
   */
  redInvoiceItemBillStatusList?: InvoiceStatusEnum
  /**
   * 发票是否已冲红
   */
  flushed?: boolean
  /**
   * 冻结状态
   */
  invoiceFreezeStatus?: boolean
  //*****************发票关联订单查询参数 orderInfo********************/
  /**
   * 批次单集合
   */
  orderNoList?: string
  /**
   * 买家信息 NOTE:0.0.4添加（根据姓名、证件号、手机号获取用户ID）
   */
  userName?: string
  idCard?: string
  phone?: string
  userId?: string = undefined
  //*****************蓝票票据查询参数 blueInvoiceItem********************/
  /**
   * 蓝票票据开具状态变更时间  billing对应申请开票时间   success对应开票时间
   */
  blueBillStatusChangeTime?: BillStatusChangeTimeRequest
  /**
   * 蓝票发票号码
   */
  blueBillNo?: string
  //*****************红票票据查询参数 redInvoiceItem********************/
  /**
   * 红票票据开具状态变更时间 billing对应申请开票时间   success对应开票时间
   */
  redBillStatusChangeTime?: BillStatusChangeTimeRequest
  /**
   * 红票发票号码
   */
  redBillNo?: string

  /*专题查询入参*/
  /**
   * 专题名称
   */
  specialSubjectName = ''

  /**
   * 是否来源专题
   */
  isFromSpecialSubject: boolean = undefined

  /**
   * 期别ID
   */
  periodId: string = undefined

  static to(queryPageInvoiceParam: QueryPageInvoiceParam) {
    const onlineinvoiceRequest = new OnlineInvoiceRequest()
    onlineinvoiceRequest.invoiceIdList = queryPageInvoiceParam.invoiceIdList
    onlineinvoiceRequest.issueId = queryPageInvoiceParam.periodId ? [queryPageInvoiceParam.periodId] : []
    onlineinvoiceRequest.basicData = new OnlineInvoiceBasicDataRequest()
    onlineinvoiceRequest.basicData.freeze = queryPageInvoiceParam.invoiceFreezeStatus
    if (
      queryPageInvoiceParam.blueInvoiceItemBillStatusList === 0 ||
      queryPageInvoiceParam.blueInvoiceItemBillStatusList
    ) {
      onlineinvoiceRequest.basicData.blueInvoiceItemBillStatusList = [
        queryPageInvoiceParam.blueInvoiceItemBillStatusList
      ]
    } else {
      onlineinvoiceRequest.basicData.blueInvoiceItemBillStatusList = undefined
    }

    onlineinvoiceRequest.basicData.redInvoiceItemBillStatusList = queryPageInvoiceParam.redInvoiceItemBillStatusList
      ? [queryPageInvoiceParam.redInvoiceItemBillStatusList]
      : undefined
    onlineinvoiceRequest.basicData.flushed = queryPageInvoiceParam.flushed
    onlineinvoiceRequest.basicData.commoditySkuIdList = queryPageInvoiceParam.commoditySkuIdList
      ? [queryPageInvoiceParam.commoditySkuIdList]
      : undefined

    onlineinvoiceRequest.associationInfoList = new Array<InvoiceAssociationInfoRequest>()
    onlineinvoiceRequest.associationInfoList[0] = new InvoiceAssociationInfoRequest()
    onlineinvoiceRequest.associationInfoList[0].associationType = 1
    if (queryPageInvoiceParam.orderNoList) {
      onlineinvoiceRequest.associationInfoList[0].associationIdList = [queryPageInvoiceParam.orderNoList]
    }
    // onlineinvoiceRequest.associationInfoList[0].buyerIdList = [queryPageInvoiceParam.buyerIdList]
    onlineinvoiceRequest.associationInfoList[0].saleChannelName = queryPageInvoiceParam.specialSubjectName
    onlineinvoiceRequest.associationInfoList[0].saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(
      queryPageInvoiceParam.isFromSpecialSubject
    )
    onlineinvoiceRequest.basicData.invoiceStatusChangeTime = new InvoiceStatusChangeTimeRequest()

    onlineinvoiceRequest.blueInvoiceItem = new OnlineInvoiceItemRequest()
    onlineinvoiceRequest.blueInvoiceItem.billStatusChangeTime = new BillStatusChangeTimeRequest()
    onlineinvoiceRequest.blueInvoiceItem.billStatusChangeTime.success = new DateScopeRequest()
    onlineinvoiceRequest.basicData.invoiceStatusChangeTime.normal = new DateScopeRequest()
    onlineinvoiceRequest.basicData.invoiceStatusChangeTime.invalid = new DateScopeRequest()
    if (queryPageInvoiceParam.blueBillStatusChangeTime?.billing?.begin)
      onlineinvoiceRequest.basicData.invoiceStatusChangeTime.normal.begin =
        queryPageInvoiceParam.blueBillStatusChangeTime?.billing.begin
    if (queryPageInvoiceParam.blueBillStatusChangeTime?.billing?.end)
      onlineinvoiceRequest.basicData.invoiceStatusChangeTime.normal.end =
        queryPageInvoiceParam.blueBillStatusChangeTime?.billing.end
    if (queryPageInvoiceParam.blueBillStatusChangeTime?.success?.begin)
      onlineinvoiceRequest.blueInvoiceItem.billStatusChangeTime.success.begin =
        queryPageInvoiceParam.blueBillStatusChangeTime?.success.begin
    if (queryPageInvoiceParam.blueBillStatusChangeTime?.success?.end) {
      onlineinvoiceRequest.blueInvoiceItem.billStatusChangeTime.success.end =
        queryPageInvoiceParam.blueBillStatusChangeTime?.success.end
    }
    onlineinvoiceRequest.blueInvoiceItem.billNoList = queryPageInvoiceParam.blueBillNo
      ? [queryPageInvoiceParam.blueBillNo]
      : undefined
    onlineinvoiceRequest.redInvoiceItem = new OnlineInvoiceItemRequest()
    onlineinvoiceRequest.redInvoiceItem.billStatusChangeTime = new BillStatusChangeTimeRequest()
    onlineinvoiceRequest.redInvoiceItem.billStatusChangeTime.billing = new DateScopeRequest()
    onlineinvoiceRequest.redInvoiceItem.billStatusChangeTime.success = new DateScopeRequest()
    if (queryPageInvoiceParam.redBillStatusChangeTime?.billing?.begin)
      onlineinvoiceRequest.redInvoiceItem.billStatusChangeTime.billing.begin =
        queryPageInvoiceParam.redBillStatusChangeTime?.billing.begin
    if (queryPageInvoiceParam.redBillStatusChangeTime?.billing?.end)
      onlineinvoiceRequest.redInvoiceItem.billStatusChangeTime.billing.end =
        queryPageInvoiceParam.redBillStatusChangeTime?.billing.end
    if (queryPageInvoiceParam.redBillStatusChangeTime?.success?.begin)
      onlineinvoiceRequest.redInvoiceItem.billStatusChangeTime.success.begin =
        queryPageInvoiceParam.redBillStatusChangeTime?.success.begin
    if (queryPageInvoiceParam.redBillStatusChangeTime?.success?.end)
      onlineinvoiceRequest.redInvoiceItem.billStatusChangeTime.success.end =
        queryPageInvoiceParam.redBillStatusChangeTime?.success.end
    onlineinvoiceRequest.redInvoiceItem.billNoList = queryPageInvoiceParam.redBillNo
      ? [queryPageInvoiceParam.redBillNo]
      : undefined
    return onlineinvoiceRequest
  }
}
