import { ResponseStatus, Response } from '@hbfe/common'
import Collectivesign from '@api/ms-gateway/ms-collectivesign-v1'
import MsOrder from '@api/ms-gateway/ms-order-v1'
class MutationBatchOrder {
  /**
   * 重新报名
   * @returns 新生成的集体缴费报名编号
   */
  async resignUpCollectivePaySignUp(batchOrderNo: string): Promise<Response<string>> {
    const response = await Collectivesign.signupByOriginalCollectiveSignup(batchOrderNo)
    return response
  }

  /**
   * 取消报名
   * @returns
   */
  async cancelCollectivePaySignUp(param: {
    batchNo?: string
    reasonId?: string
    reason?: string
  }): Promise<ResponseStatus> {
    const { status } = await MsOrder.cancelCollectiveSignup(param)
    return status
  }

  /**   * 删除集体报名
   * @param collectiveSignupNo 集体报名编号
   */
  async deleteCollectiveSignup(collectiveSignupNo: string) {
    const { status } = await Collectivesign.deleteCollectiveSignup(collectiveSignupNo)
    return status
  }
}
export default MutationBatchOrder
