import { CreateReceiveAccountRequest, ReceiveAccountExtProperty } from '@api/ms-gateway/ms-trade-configuration-v1'
import CreateReceiveAccountVo from './CreateReceiveAccountVo'
import MutationReceiveAccountVo from './MutationReceiveAccountVo'

class CreateWXPayReceiveAccountVo extends CreateReceiveAccountVo {
  /**
   * 支付账号类型id
   * 支付宝:ALIPAY
   * 微信：WXPAY
   * 支付宝H5:ALIPAYH5
   * 微信H5：WXPAYH5
   */
  paymentChannelId = ''
  /**
   * API密钥
   */
  privateKeyPWD = ''
  /**
   * 公众账号ID
   */
  appId = ''
  /**
   * 微信证书密钥
   */
  merchantKey = ''
  /**
   * 微信证书文件名称
   */
  privateKeyFileName = ''
  /**
   * 微信证书路径
   */
  privateKeyPath = ''

  constructor(type: string) {
    super()
    this.paymentChannelId = type
  }

  from(mutationReceiveAccountVo: MutationReceiveAccountVo) {
    this.accountType = mutationReceiveAccountVo.accountType
    this.accountNo = mutationReceiveAccountVo.accountNo
    this.paymentChannelId = mutationReceiveAccountVo.paymentChannelId
    this.accountName = mutationReceiveAccountVo.accountName
    this.qrScanPrompt = mutationReceiveAccountVo.qrScanPrompt
    this.refundWay = mutationReceiveAccountVo.refundWay
    this.taxPayerId = mutationReceiveAccountVo.taxPayerId
    this.appId = mutationReceiveAccountVo.wxPayAppId
    this.merchantKey = mutationReceiveAccountVo.merchantKey
    this.privateKeyPWD = mutationReceiveAccountVo.privateKeyPWD
    this.privateKeyFileName = mutationReceiveAccountVo.privateKeyFileName
    this.privateKeyPath = mutationReceiveAccountVo.privateKeyPath
  }

  to() {
    const createReceiveAccountRequest = new CreateReceiveAccountRequest()
    createReceiveAccountRequest.accountType = 1
    createReceiveAccountRequest.paymentChannelId = this.paymentChannelId
    createReceiveAccountRequest.accountNo = this.accountNo
    createReceiveAccountRequest.name = this.accountName
    createReceiveAccountRequest.qrScanPrompt = this.qrScanPrompt
    createReceiveAccountRequest.refundWay = this.refundWay
    createReceiveAccountRequest.taxPayerId = this.taxPayerId
    createReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    createReceiveAccountRequest.properties.push({ name: 'appId', value: this.appId })
    createReceiveAccountRequest.properties.push({ name: 'merchantKey', value: this.merchantKey })
    createReceiveAccountRequest.properties.push({ name: 'privateKeyPWD', value: this.privateKeyPWD })
    createReceiveAccountRequest.properties.push({ name: 'privateKeyFileName', value: this.privateKeyFileName })
    createReceiveAccountRequest.properties.push({ name: 'privateKeyPath', value: this.privateKeyPath })
    return createReceiveAccountRequest
  }
}
export default CreateWXPayReceiveAccountVo
