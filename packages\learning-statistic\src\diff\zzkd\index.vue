<route-meta>
{
"isMenu": true,
"title": "学员学习明细",
"sort": 5,
"icon": "icon-mingxi"
}
</route-meta>
<template>
  <div>
    <ZzkdLearningStatistic ref="learningStatisticRef"> </ZzkdLearningStatistic>
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-learningStatistic/src/models/index'
  import QueryStudentLearningListDiff from '@api/service/diff/management/zzkd/statisticalReport/query/QueryStudentLearningList'
  import QueryStudentLearningManagerRegionListDiff from '@api/service/diff/management/zzkd/statisticalReport/query/QueryStudentLearningManagerRegionList'
  import LearningStatistic from '@hbfe/jxjy-admin-learningStatistic/src/index.vue'
  import OnlineClassTable from '@hbfe/jxjy-admin-learningStatistic/src/diff/zzkd/__components__/online-class-table.vue'
  import { StudentLearningStaticsVoDiff } from '@api/service/diff/management/zzkd/statisticalReport/vo/StudentLearningStaticsVo'

  class NewSchemeSkuProperty extends SchemeSkuProperty {
    tradeChannels: number = null
  }
  @Component({
    components: {
      OnlineClassTable
    }
  })
  class ZzkdLearningStatistic extends LearningStatistic {
    /**
     * 列表数据
     */
    tableData = new Array<StudentLearningStaticsVoDiff>()
    /**
     * 差异化学员学习对象
     */
    queryStudentLearningListDiff = new QueryStudentLearningListDiff()

    /**
     * 差异化地区管理员导出对象
     */
    queryStudentLearningManagerRegionListDiff = new QueryStudentLearningManagerRegionListDiff()
    /**
     * 地区管理员导出
     */
    async exportExcelRegion() {
      return await this.queryStudentLearningManagerRegionListDiff.exportExcel(this.filter)
    }
    /**
     * 分销管理员导出
     */
    async exportStudentSchemeLearningExcelInSDistributor() {
      return await this.queryStudentLearningListDiff.exportStudentSchemeLearningExcelInSDistributor(this.filter)
    }
    /**
     * 专题管理员导出
     */
    async exportExcelTrainingChannel() {
      return await this.queryStudentLearningListDiff.exportExcelTrainingChannel(this.filter)
    }
    /**
     * 超级管理员导出
     */
    async exportExcel() {
      return await this.queryStudentLearningListDiff.exportExcel(this.filter)
    }

    /**
     * 地区管理员查询
     */
    async listRegionLearningReportFormsInServicerRegion() {
      return await this.queryStudentLearningManagerRegionListDiff.listRegionLearningReportFormsInServicerDiff(
        this.page,
        this.filter
      )
    }
    /**
     * 分销管理员查询
     */
    async pageStudentSchemeLearningInDistributor() {
      return await this.queryStudentLearningListDiff.listRegionLearningReportFormsInTrainingChannelDiff(
        this.page,
        this.filter
      )
    }
    /**
     * 专题管理员查询
     */
    async listRegionLearningReportFormsInTrainingChannel() {
      return await this.queryStudentLearningListDiff.listRegionLearningReportFormsInTrainingChannelDiff(
        this.page,
        this.filter
      )
    }
    /**
     * 网校管理员查询
     */
    async listRegionLearningReportFormsInServicer() {
      return await this.queryStudentLearningListDiff.listRegionLearningReportFormsInServicerDiff(this.page, this.filter)
    }
  }
  @Component({
    components: {
      ZzkdLearningStatistic
    }
  })
  export default class extends Vue {
    @Ref('learningStatisticRef') learningStatisticRef: ZzkdLearningStatistic
    mounted() {
      /**
       * 差异化统计口径说明（在第十四个的位置塞入证书编号）
       */
      this.learningStatisticRef.listFields.splice(14, 0, {
        field: '证书编号',
        description: '学员合格后生成的证书编号，为空显示 -'
      })
    }
  }
</script>
