import msReturn, {
  OrderReturnPair,
  ReturnOrderAgreeBatchApplyResponse,
  ReturnOrderConfirmBatchRefundResponse
} from '@api/ms-gateway/ms-return-order-v1'
import { Response, ResponseStatus } from '@hbfe/common'
import { CalReturnOrderUtil } from '@api/service/management/trade/single/order/utils/CalReturnOrderUtil'
export default class MutationReturnOrder {
  //退货单号
  returnOrderNo = ''
  //备注
  note = ''
  //订单号
  orderNo = ''
  /**
   * 退货单号列表
   */
  returnOrderNoList: Array<string> = []
  /**
   * 是否自动同意退款
   */
  autoAgreeReturn = false

  /*
   *  同意退货审批
   * */
  async agreeReturnApply() {
    const res = await msReturn.agreeReturnApply({
      returnOrderNo: this.returnOrderNo,
      approveComment: this.note,
      autoAgreeReturn: false,
      orderNo: this.orderNo
    })
    return this.filterRes(res).status
  }

  /*
   *  取消退货审批
   * */
  async cancelReturnApply() {
    const res = await msReturn.sellerCancelReturnApply({
      returnOrderNo: this.returnOrderNo,
      cancelReason: this.note,
      orderNo: this.orderNo
    })
    return this.filterRes(res).status
  }
  /*
   *  确认退款
   * */
  async confirmRefund() {
    const res = await msReturn.confirmRefund({
      returnOrderNo: this.returnOrderNo
    })
    return this.filterRes(res).status
  }
  /*
   *  继续退款
   * */
  async retryRefund() {
    const res = await msReturn.retryRefund({
      returnOrderNo: this.returnOrderNo
    })
    return this.filterRes(res).status
  }
  /*
   *  重新回收做资源
   * */
  async retryRecycleResource() {
    const res = await msReturn.retryRecycleResource({
      returnOrderNo: this.returnOrderNo
    })
    return this.filterRes(res).status
  }
  /*
   *  拒绝退货
   * */
  async rejectReturnApply() {
    const res = await msReturn.rejectReturnApply({
      returnOrderNo: this.returnOrderNo,
      approveComment: this.note,
      orderNo: this.orderNo
    })
    return this.filterRes(res).status
  }
  filterRes(res: Response<any>) {
    return CalReturnOrderUtil.filterRes(res)
  }
  /**
   * 批量同意退款
   * @param batchReturnOrderNoList 订单号集
   */
  async batchAgreeReturnApply(
    batchReturnOrderNoList: OrderReturnPair[]
  ): Promise<Response<ReturnOrderAgreeBatchApplyResponse>> {
    const res = await msReturn.agreeReturnBatchApply({
      orderReturnPairs: batchReturnOrderNoList,
      approveComment: this.note,
      autoAgreeReturn: false
    })
    return res
  }
  /**
   *  批量确认退款
   * @param batchReturnOrderNoList 订单号集
   */
  async batchConfirmRefund(batchReturnOrderNoList: string[]): Promise<Response<ReturnOrderConfirmBatchRefundResponse>> {
    const res = await msReturn.confirmBatchRefund({
      returnOrderNoList: batchReturnOrderNoList
    })
    return res
  }
}
