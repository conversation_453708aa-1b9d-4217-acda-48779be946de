<template>
  <el-drawer :title="title" :visible.sync="visible" size="1400px" custom-class="m-drawer" :append-to-body="true">
    <div class="drawer-bd">
      <div class="f-mb15">
        <div class="m-tit">
          <span class="tit-txt">课程学习</span>
        </div>
        <el-alert type="warning" :closable="false" show-icon class="m-alert f-mb10">
          <div class="f-c6" v-loading="studyLoading">期望开始学习时间：{{ formatterData }}</div>
        </el-alert>
        <!--表格-->
        <el-table stripe :data="studyTable" highlight-current-row class="m-table" v-loading="studyLoading">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="课程名称" min-width="160">
            <template slot-scope="scope">{{ scope.row.courseName || '-' }}</template>
          </el-table-column>
          <el-table-column label="学习开始时间" min-width="160">
            <template slot-scope="scope">{{ scope.row.startTime || '-' }}</template>
          </el-table-column>
          <el-table-column label="学习结束时间" min-width="160">
            <template slot-scope="scope">{{ scope.row.endTime || '-' }}</template>
          </el-table-column>
          <el-table-column label="智能学习完成进度" min-width="160">
            <template slot-scope="scope">{{ scope.row.learningSchedule || '-' }}</template>
          </el-table-column>
          <el-table-column label="学习完成情况" min-width="140">
            <template slot-scope="scope">{{ scope.row.learningResult.toString() || '-' }}</template>
          </el-table-column>
          <el-table-column label="测验开始时间" min-width="160">
            <template slot-scope="scope">{{ scope.row.quizStartTime || '-' }}</template>
          </el-table-column>
          <el-table-column label="测验结束时间" min-width="160">
            <template slot-scope="scope">{{ scope.row.quizEndTime || '-' }}</template>
          </el-table-column>
          <el-table-column label="测验完成情况" min-width="140">
            <template slot-scope="scope">
              <div v-if="scope.row.quizResult.equal(LearningResultEnum.complete)">
                <p>已完成</p>
                <p class="f-cr">测验分数：{{ scope.row.quizScore }}</p>
              </div>
              <!--              <div v-else-if="scope.row.quizResult.equal(LearningResultEnum.stop)">-->
              <!--                <p>自主学习插入</p>-->
              <!--              </div>-->
              <!--              <div v-else-if="scope.row.quizResult.equal(LearningResultEnum.shorten_failed)">-->
              <!--                <p>培训时间缩短执行失败</p>-->
              <!--              </div>-->
              <!--              <div v-else-if="scope.row.quizResult.equal(LearningResultEnum.un_complete)">-->
              <!--                <p>-</p>-->
              <!--              </div>-->
              <!--              <div v-else>-->
              <!--                <p>-</p>-->
              <!--              </div>-->
              <div v-else>
                <p>{{ scope.row.quizResult.toString() || '-' }}</p>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <div class="f-mb15">
        <div class="m-tit">
          <span class="tit-txt">班级考试</span>
        </div>
        <!--表格-->
        <el-table stripe :data="examinationTable" highlight-current-row class="m-table" v-loading="examinationLoading">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="考试场次" min-width="160">
            <template slot-scope="scope">{{ scope.row.examName }}</template>
          </el-table-column>
          <el-table-column label="考试开始时间" min-width="160">
            <template slot-scope="scope">{{ scope.row.examBeginTime || '-' }}</template>
          </el-table-column>
          <el-table-column label="考试结束时间" min-width="160">
            <template slot-scope="scope">{{ scope.row.examEndTime || '-' }}</template>
          </el-table-column>
          <el-table-column label="当前完成情况" min-width="160">
            <template slot-scope="scope">{{ scope.row.examResult.toString() || '-' }}</template>
          </el-table-column>
          <el-table-column label="考试成绩" min-width="140">
            <template slot-scope="scope">{{ formatterScore(scope.row.examScore) }}</template>
          </el-table-column>
        </el-table>
      </div>
      <!--      <div class="f-mb15">-->
      <!--        <div class="m-tit"><span class="tit-txt">调研问卷</span></div>-->
      <!--        &lt;!&ndash;表格&ndash;&gt;-->
      <!--        <el-table stripe :data="tableData" class="m-table" max-height="600px">-->
      <!--          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>-->
      <!--          <el-table-column label="问卷名称" min-width="220">-->
      <!--            <template>班级问卷纳入考核</template>-->
      <!--          </el-table-column>-->
      <!--          <el-table-column label="问卷开始时间" min-width="220">-->
      <!--            <template>2025-02-01 10:00:00</template>-->
      <!--          </el-table-column>-->
      <!--          <el-table-column label="问卷结束时间" min-width="220">-->
      <!--            <template>2025-02-01 10:00:00</template>-->
      <!--          </el-table-column>-->
      <!--          <el-table-column label="问卷完成情况" min-width="220">-->
      <!--            <template>已完成</template>-->
      <!--          </el-table-column>-->
      <!--        </el-table>-->
      <!--      </div>-->
    </div>

    <div class="m-btn-bar drawer-ft">
      <el-button type="primary" @click="callBack">返 回</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Vue, Component, Prop } from 'vue-property-decorator'
  import LogDetail from '@api/service/management/intelligence-learning/model/LogDetail'
  import CourseLogItem from '@api/service/management/intelligence-learning/model/CourseLogItem'
  import ExamLogItem from '@api/service/management/intelligence-learning/model/ExamLogItem'
  import { LearningResultEnum } from '@api/service/management/intelligence-learning/enum/LearningResultEnum'
  import { LogTypeEnum } from '@api/service/management/intelligence-learning/enum/LogType'

  @Component({})
  export default class extends Vue {
    @Prop({ type: String, default: '' }) title: string
    /**
     * 显隐
     */
    visible = false

    /**
     * 课程学习表格数据
     */
    studyTable = new Array<CourseLogItem>()

    /**
     * 枚举
     */
    LearningResultEnum = LearningResultEnum

    /**
     * 课程学习加载状态
     */
    studyLoading = false

    /**
     * 考试表格数据
     */
    examinationTable = new Array<ExamLogItem>()

    /**
     * 考试加载状态
     */
    examinationLoading = false

    expectStartLearningTime = ''

    /**
     * 获取分数显示
     */
    get formatterScore() {
      return (value: number) => {
        if (!value && value != 0) {
          return '-'
        } else {
          return value
        }
      }
    }

    get formatterData() {
      if (!this.expectStartLearningTime) {
        return '-'
      } else {
        return this.expectStartLearningTime.substring(0, 10)
      }
    }

    /**
     * 打开弹框
     */
    async open(logId: string, logType: LogTypeEnum) {
      this.studyTable = new Array<CourseLogItem>()
      this.examinationTable = new Array<ExamLogItem>()
      this.expectStartLearningTime = ''
      this.visible = true
      await this.queryList(logId, logType)
    }

    /**

     * 查询数据

     */

    async queryList(logId: string, logType: LogTypeEnum) {
      try {
        this.examinationLoading = true
        this.studyLoading = true
        const logDetail = new LogDetail(logType)
        logDetail.logId = logId
        await logDetail.queryCourseLogDetail()
        await logDetail.queryExamLogDetail()
        this.studyTable = logDetail.courseLogList
        this.examinationTable = logDetail.examLogList
        this.expectStartLearningTime = logDetail.expectStartLearningTime
      } catch (e) {
        console.log(e)
      } finally {
        this.examinationLoading = false
        this.studyLoading = false
      }
    }

    /**
     * 返回
     */

    callBack() {
      this.visible = false
    }
  }
</script>
