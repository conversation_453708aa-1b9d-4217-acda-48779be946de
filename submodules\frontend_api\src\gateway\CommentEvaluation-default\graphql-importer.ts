import getReversionDetailPage from './queries/getReversionDetailPage.graphql'
import getSimpleCommentPage from './queries/getSimpleCommentPage.graphql'
import loadCommentary from './queries/loadCommentary.graphql'
import loadCourseAppraisalList from './queries/loadCourseAppraisalList.graphql'
import loadCourseAppraisalRecordList from './queries/loadCourseAppraisalRecordList.graphql'
import loadCourseAppraisalRecordListByIdList from './queries/loadCourseAppraisalRecordListByIdList.graphql'
import loadCourseAppraisalUnShieldRecordList from './queries/loadCourseAppraisalUnShieldRecordList.graphql'
import loadLastMonthNetSchoolAppraisalList from './queries/loadLastMonthNetSchoolAppraisalList.graphql'
import loadNetSchoolAppraisalList from './queries/loadNetSchoolAppraisalList.graphql'
import loadNetSchoolAppraisalRecordList from './queries/loadNetSchoolAppraisalRecordList.graphql'
import loadReversion from './queries/loadReversion.graphql'
import loadUserCourseAppraisal from './queries/loadUserCourseAppraisal.graphql'
import loadUserNetSchoolAppraisal from './queries/loadUserNetSchoolAppraisal.graphql'
import addCommentary from './mutates/addCommentary.graphql'
import addReversion from './mutates/addReversion.graphql'
import appraise from './mutates/appraise.graphql'
import appraiseNetSchool from './mutates/appraiseNetSchool.graphql'
import auditCommentaryFail from './mutates/auditCommentaryFail.graphql'
import auditCommentaryPass from './mutates/auditCommentaryPass.graphql'
import cancelShieldCourseUserAppraisal from './mutates/cancelShieldCourseUserAppraisal.graphql'
import deleteBatchCommentary from './mutates/deleteBatchCommentary.graphql'
import deleteCommentary from './mutates/deleteCommentary.graphql'
import deleteReversion from './mutates/deleteReversion.graphql'
import deleteReversionByComId from './mutates/deleteReversionByComId.graphql'
import pinCommentary from './mutates/pinCommentary.graphql'
import pinReversion from './mutates/pinReversion.graphql'
import shieldCommentary from './mutates/shieldCommentary.graphql'
import shieldCourseUserAppraisal from './mutates/shieldCourseUserAppraisal.graphql'
import shieldReversion from './mutates/shieldReversion.graphql'
import unPinCommentary from './mutates/unPinCommentary.graphql'
import unPinReversion from './mutates/unPinReversion.graphql'
import unshieldedCommentary from './mutates/unshieldedCommentary.graphql'
import unshieldedReversion from './mutates/unshieldedReversion.graphql'

export {
  getReversionDetailPage,
  getSimpleCommentPage,
  loadCommentary,
  loadCourseAppraisalList,
  loadCourseAppraisalRecordList,
  loadCourseAppraisalRecordListByIdList,
  loadCourseAppraisalUnShieldRecordList,
  loadLastMonthNetSchoolAppraisalList,
  loadNetSchoolAppraisalList,
  loadNetSchoolAppraisalRecordList,
  loadReversion,
  loadUserCourseAppraisal,
  loadUserNetSchoolAppraisal,
  addCommentary,
  addReversion,
  appraise,
  appraiseNetSchool,
  auditCommentaryFail,
  auditCommentaryPass,
  cancelShieldCourseUserAppraisal,
  deleteBatchCommentary,
  deleteCommentary,
  deleteReversion,
  deleteReversionByComId,
  pinCommentary,
  pinReversion,
  shieldCommentary,
  shieldCourseUserAppraisal,
  shieldReversion,
  unPinCommentary,
  unPinReversion,
  unshieldedCommentary,
  unshieldedReversion
}
