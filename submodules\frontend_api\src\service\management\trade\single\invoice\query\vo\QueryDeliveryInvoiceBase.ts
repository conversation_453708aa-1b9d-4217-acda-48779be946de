import { Page } from '@hbfe/common'
import DeliveryInvoiceParamVo from '@api/service/management/trade/single/invoice/query/vo/DeliveryInvoiceParam'
import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
import QueryOffLinePageInvoiceParam from '@api/service/management/trade/single/invoice/query/vo/QueryOffLinePageInvoiceParam'

export default abstract class QueryDeliveryInvoiceBase {
  /**
   * 分页查询发票配送
   * @param page 页数
   * @param DeliveryInvoiceParamVo 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<InvoiceListResponse>
   */
  abstract queryPageDeliveryInvoice(
    page: Page,
    deliveryInvoiceParamVo?: DeliveryInvoiceParamVo
  ): Promise<Array<OffLinePageInvoiceVo>>
  /**
   * 导出发票配送
   */
  abstract exportPageDeliveryInvoice(deliveryInvoiceParamVo: DeliveryInvoiceParamVo): Promise<boolean>
  /**
   * 个人线下发票配送 - 专票
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  abstract offLinePageVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean>
}
