import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjy-distributor-admin-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjy-distributor-admin-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Attach {
  fileName?: string
  filePath?: string
}

export class FriendLinkRequest {
  title?: string
  picture?: string
  friendLinkType: number
  link?: string
  sort?: number
}

/**
 * 导入批量授权分销商品
<AUTHOR>
@date 2022/11/7 14:12
 */
export class ImportPricingSchemeBatchRequest {
  /**
   * excel文件路径
   */
  filePath?: string
  /**
   * 文件名称
   */
  fileName?: string
}

/**
 * 导入批量设置定价方案
 */
export class ImportPricingSchemeSetupBatchRequest {
  /**
   * excel文件路径
   */
  filePath?: string
  /**
   * 文件名称
   */
  fileName?: string
}

/**
 * @author: xucenhao
@time: 2024-12-09
@description:
 */
export class CheckMiniDistributorRequest {
  /**
   * 社会统一信用代码
   */
  unitCode?: string
  /**
   * 证件号码
   */
  idCard?: string
}

/**
 * @author: xucenhao
@time: 2024-09-10
@description:
 */
export class CreateDistributorAdminRequest {
  /**
   * 初始token【必传】
   */
  token: string
  /**
   * 网校id
   */
  onlineSchoolId?: string
  /**
   * 登录账户【必填】
   */
  identity: string
  /**
   * 姓名【必填】
   */
  name: string
  /**
   * 性别  女: 0 男:1【必填】
   */
  gender: number
  /**
   * 手机
   */
  phone?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 启用状态  正常: 1 禁用: 2【必填】
   */
  status: number
  /**
   * 密码【必填】
   */
  password: string
  /**
   * 添加的角色id集合【必填】
   */
  addRoleIds: Array<string>
}

/**
 * 创建分销商请求
 */
export class CreateDistributorNewRequest {
  /**
   * 统一社会信用代码（类型为企业时必填）
   */
  unitCode?: string
  /**
   * 身份证号（类型为个人时必填）
   */
  idCard?: string
  /**
   * 登录账号（认证方式-用户名）
   */
  loginAccount?: string
  /**
   * 【单位管理员】
分销商管理员   姓名
   */
  name?: string
  /**
   * 【单位管理员】
分销商管理员   手机号
   */
  phone?: string
  /**
   * 分销商类型【必填】
@see PartnerType
   */
  distributorType?: number
  /**
   * 分销商姓名
   */
  distributorName?: string
  /**
   * 附件信息
   */
  attachList?: Array<Attach>
}

/**
 * 创建分销商请求
 */
export class CreateDistributorRequest {
  /**
   * 统一社会信用代码（类型为企业时必填）
   */
  unitCode?: string
  /**
   * 身份证号（类型为个人时必填）
   */
  idCard?: string
  /**
   * 登录账号（认证方式-用户名）
   */
  loginAccount: string
  /**
   * 【单位管理员】
分销商管理员   姓名
   */
  name: string
  /**
   * 【单位管理员】
分销商管理员   手机号
   */
  phone: string
  /**
   * 分销商类型【必填】
@see com.fjhb.domain.basicdata.api.supplier.consts.PartnerType
   */
  distributorType: number
  /**
   * 附件信息
   */
  attachList?: Array<Attach>
  /**
   * 分销商姓名
   */
  distributorName?: string
  /**
   * 域名类型
系统默认域名 1
自有域名 2
@see com.fjhb.domain.basicdata.api.servicer.consts.DomainNameTypeConsts
   */
  domainNameType: number
  /**
   * 分销商推广门户域名
   */
  domainName: string
}

/**
 * 分销商轮播图保存请求
 */
export class DistributorBannerBannerSaveRequest {
  /**
   * 轮播图名称
   */
  bannerName?: string
  /**
   * 轮播图链接
   */
  bannerLink?: string
  /**
   * 轮播图路径
   */
  bannerPath?: string
  /**
   * 分类
   */
  sort: number
}

export class DistributorBannerListSaveByPortalTypeRequest {
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户标识
   */
  identifier?: string
  /**
   * 门户类型
   */
  portalType: number
  /**
   * 分销商轮播图保存请求
   */
  bannerSaveRequestList?: Array<DistributorBannerBannerSaveRequest>
}

export class DistributorBannerListSaveRequest {
  /**
   * 门户类型
   */
  portalType: number
  /**
   * 分销商轮播图保存请求
   */
  bannerSaveRequestList?: Array<DistributorBannerBannerSaveRequest>
}

export class EnablePromotionPortalRequest {
  /**
   * 分销商编号
   */
  distributorId: string
  /**
   * 门户标识
   */
  identifier?: string
  /**
   * 状态 1启用 0禁用
   */
  type: number
}

/**
 * @author: xucenhao
@time: 2024-09-04
@description:
 */
export class FindPublishedInfoByIdentifierRequest {
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户id
   */
  promotionId?: string
  /**
   * 门户类型
   */
  portalType?: number
}

export class SavePortalBasicMessageUpdateRequest {
  /**
   * 域名类型
使用系统域名 1
自有域名 2
使用单位域名自动生成 4
@see com.fjhb.domain.basicdata.api.servicer.consts.DomainNameTypeConsts
   */
  domainNameType: number
  /**
   * 推广门户域名
   */
  domainName?: string
  /**
   * 分销商编号
   */
  distributorId: string
  /**
   * 门户标识
   */
  identifier?: string
  /**
   * 门户标题
   */
  title?: string
  /**
   * 门户简称
   */
  shortName?: string
  /**
   * 客服电话
   */
  csPhone?: string
  /**
   * 客服咨询时间
   */
  csCallTime?: string
  /**
   * 底部内容(底部落款)
   */
  footContent?: string
  /**
   * 底部内容id(底部落款id)
   */
  footContentId?: string
  /**
   * 轮播图
   */
  bannerListSaveRequestList?: Array<DistributorBannerListSaveRequest>
  /**
   * web端图片
   */
  webPortMessageRequest?: SaveWebPortMessageRequest
  /**
   * 线下集体报名配置入口状态 1启用 0禁用
   */
  offlineCollectiveRegisterType: number
}

/**
 * @author: xucenhao
@time: 2024-08-26
@description: 保存海报
 */
export class SavePosterConfigurationRequest {
  /**
   * 海报配置id
   */
  id?: string
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 门户标识
   */
  identifier?: string
  /**
   * 海报标题
   */
  title?: string
  /**
   * 宣传文案
   */
  promotionalCopy?: string
  /**
   * ui模版id
   */
  templateId?: string
  /**
   * 客服电话
   */
  csPhone?: string
  /**
   * 二维码相关列表
   */
  qrcodeList?: Array<QRCode>
}

/**
 * @author: xucenhao
@time: 2024-09-12
@description: 禁用分销商管理员
 */
export class SaveStatusDistributorAdminRequest {
  /**
   * 网校id
   */
  onlineSchoolId?: string
  /**
   * 账户id
   */
  accountId?: string
  /**
   * 角色id集合
   */
  roleIds?: Array<string>
  /**
   * 状态 0 禁用 1 启用
   */
  status: number
}

export class SaveWebPortMessageRequest {
  /**
   * 门户logo
   */
  logo?: string
  /**
   * 浏览器图标
   */
  icon?: string
  /**
   * 移动二维码
   */
  mobileQRCode?: string
  /**
   * 客服电话图片
   */
  csPhonePicture?: string
  /**
   * 在线客服代码内容id
   */
  csOnlineCodeId?: string
  /**
   * 培训流程图片
   */
  trainingFlowPicture?: string
  /**
   * 友情链接类型
@see com.fjhb.domain.basicdata.api.servicer.consts.FriendLinkTypes
1-文本  2-图片
   */
  friendLinkTypes: number
  /**
   * 友情链接集合
   */
  friendLinks?: Array<FriendLinkRequest>
  /**
   * cnzz信息
   */
  cnzz?: string
  /**
   * 目录名
   */
  dirName?: string
}

/**
 * @author: xucenhao
@time: 2024-09-10
@description:
 */
export class UpdateDistributorAdminRequest {
  /**
   * 初始token【必传】
   */
  token: string
  /**
   * 网校id
   */
  onlineSchoolId?: string
  /**
   * 被修改的管理员账户ID【必填】
   */
  accountId: string
  /**
   * 登录账户【为null，表示不更新】
   */
  identity?: string
  /**
   * 姓名【为null，表示不更新】
   */
  name?: string
  /**
   * 性别【为null，表示不更新】
   */
  gender?: number
  /**
   * 手机【为null，表示不更新】
   */
  phone?: string
  /**
   * 邮箱【为null，表示不更新】
   */
  email?: string
  /**
   * 启用状态  正常: 1 禁用: 2【必填】
   */
  status: number
  /**
   * 添加的角色id集合
   */
  addRoleIds?: Array<string>
  /**
   * 移除的角色id集合
   */
  removeRoleIds?: Array<string>
}

/**
 * @author: linxiquan
@Date: 2023/12/8 13:54
@Description: 修改分销商请求
 */
export class UpdateDistributorNewRequest {
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 分销商类型【必填】
@see PartnerType
   */
  distributorType?: number
  /**
   * 分销商姓名
   */
  distributorName?: string
  /**
   * 附件信息
   */
  attachList?: Array<Attach>
}

/**
 * @author: linxiquan
@Date: 2023/12/8 13:54
@Description: 修改分销商请求
 */
export class UpdateDistributorRequest {
  /**
   * 分销商id
   */
  distributorId: string
  /**
   * 分销商类型【必填】
@see com.fjhb.domain.basicdata.api.supplier.consts.PartnerType
   */
  distributorType: number
  /**
   * 附件信息
   */
  attachList?: Array<Attach>
  /**
   * 分销商姓名
   */
  distributorName?: string
  /**
   * 域名类型
系统默认域名 1
自有域名 2
@see com.fjhb.domain.basicdata.api.servicer.consts.DomainNameTypeConsts
   */
  domainNameType: number
  /**
   * 分销商推广门户域名
   */
  domainName: string
}

/**
 * @Description 分销商推广门户校验入参请求
<AUTHOR>
@Date 2025/3/31 8:56
 */
export class ValidDistributorPortalRequest {
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户id
   */
  promotionId?: string
  /**
   * 门户类型
   */
  portalType?: number
}

export class ValidDistributorRequest {
  /**
   * 服务商类型【必填】
@see  com.fjhb.domain.basicdata.api.supplier.consts.PartnerType
   */
  servicerType?: number
  /**
   * 统一社会信用代码（类型为企业时必填）
   */
  unitCode?: string
  /**
   * 身份证号（类型为个人时必填）
   */
  idCard?: string
}

/**
 * @author: xucenhao
@time: 2024-09-04
@description:
 */
export class WhetherBelongDistributorRequest {
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户id
   */
  promotionId?: string
}

export class QRCode {
  /**
   * 二维码图片
   */
  mobileQrcode?: string
  /**
   * 二维码操作提示
   */
  qrcodeTip?: string
}

/**
 * <AUTHOR> [2023/7/11 20:54]
 */
export class GeneralNewResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * <AUTHOR> [2023/7/11 20:54]
 */
export class GeneralResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * @Description
<AUTHOR>
@Date 2024/3/6 10:42
 */
export class ImportPricingSchemeResponse {
  /**
   * 批次号
   */
  batchNo: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * @author: xucenhao
@time: 2024-08-23
@description:
 */
export class SavePortalBasicMessageUpdateResponse {
  webPortalId: string
  identifier: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * @author: xucenhao
@time: 2024-12-09
@description:
 */
export class CheckMiniDistributorResponse {
  /**
   * 渠道商id
   */
  id: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

export class CreateDistributorNewResponse {
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

export class CreateDistributorResponse {
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * @Description 根据域名查询响应
<AUTHOR>
@Date 2025/3/31 10:06
 */
export class FindInfoByDomainResponse {
  /**
   * 域名类型(1-网校 2-分销商 3-专题)
@See com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.consts.DomainTypeEnum
   */
  domainType: number
}

/**
 * @author: xucenhao
@time: 2024-09-04
@description:
 */
export class FindPublishedInfoByIdentifierResponse {
  /**
   * 是否发布
   */
  published: boolean
}

/**
 * @Description 分销商推广门户校验
<AUTHOR>
@Date 2025/3/31 8:53
 */
export class ValidDistributorPortalResponse {
  code: string
  message: string
  /**
   * 是否发布
   */
  published: boolean
  /**
   * 可用状态
   */
  status: boolean
}

export class ValidDistributorResponse {
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 1007:统一社会信用代码不满足18位
   * 1009:统一社会信用代码不满足只有数字和字母
   * 1008:身份证号不满足18位
   * 1010:身份证号不满足只有数字和字母
   * 2001:社会统一信用代码填了，但是身份证号也填了，或者都没填
   * 3001:已存在
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkMiniDistributor(
    request: CheckMiniDistributorRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.checkMiniDistributor,
    operation?: string
  ): Promise<Response<CheckMiniDistributorResponse>> {
    return commonRequestApi<CheckMiniDistributorResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 根据域名查找域名类型
   * @return 域名类型(1-网校 2-分销商 3-专题)
   * @param query 查询 graphql 语法文档
   * @param domainName 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async findInfoByDomainName(
    domainName: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findInfoByDomainName,
    operation?: string
  ): Promise<Response<FindInfoByDomainResponse>> {
    return commonRequestApi<FindInfoByDomainResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { domainName },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 学员端
   * 根据推广门户标识与门户类型查询推广门户是否已发布
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async findPublishedInfoByIdentifier(
    request: FindPublishedInfoByIdentifierRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findPublishedInfoByIdentifier,
    operation?: string
  ): Promise<Response<FindPublishedInfoByIdentifierResponse>> {
    return commonRequestApi<FindPublishedInfoByIdentifierResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 当前上下文为分销上下文 判断该 分销商 对应的网校是否过期
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async isOnlineSchoolContractExpired(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.isOnlineSchoolContractExpired,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 校验分销商是否已存在
   * 100003 统一社会信用代码格式不合法
   * 100005 18位身份证格式异常
   * 100007 分销商已存在
   * 200002 单位不存在
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validDistributor(
    request: ValidDistributorRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.validDistributor,
    operation?: string
  ): Promise<Response<ValidDistributorResponse>> {
    return commonRequestApi<ValidDistributorResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分销商推广门户校验
   * @param request
   * @return published（是否发布）、status(可用状态)
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async validDistributorPortal(
    request: ValidDistributorPortalRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.validDistributorPortal,
    operation?: string
  ): Promise<Response<ValidDistributorPortalResponse>> {
    return commonRequestApi<ValidDistributorPortalResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 学员端
   * 查询该推广门户是否属于该分销商
   * @param request
   * @return 500 否 200 是
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async whetherBelongDistributor(
    request: WhetherBelongDistributorRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.whetherBelongDistributor,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建分销商
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createDistributor(
    request: CreateDistributorRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createDistributor,
    operation?: string
  ): Promise<Response<CreateDistributorResponse>> {
    return commonRequestApi<CreateDistributorResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建分销管理员
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createDistributorAdmin(
    request: CreateDistributorAdminRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createDistributorAdmin,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建渠道商
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createMiniDistributor(
    request: CreateDistributorNewRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createMiniDistributor,
    operation?: string
  ): Promise<Response<CreateDistributorNewResponse>> {
    return commonRequestApi<CreateDistributorNewResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启停用推广门户
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enablePortal(
    request: EnablePromotionPortalRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enablePortal,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量导入定价方案
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importSupplyPricingSchemeBatch(
    request: ImportPricingSchemeBatchRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.importSupplyPricingSchemeBatch,
    operation?: string
  ): Promise<Response<ImportPricingSchemeResponse>> {
    return commonRequestApi<ImportPricingSchemeResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量导入定价方案设置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async importSupplyPricingSchemeSetupBatch(
    request: ImportPricingSchemeSetupBatchRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.importSupplyPricingSchemeSetupBatch,
    operation?: string
  ): Promise<Response<ImportPricingSchemeResponse>> {
    return commonRequestApi<ImportPricingSchemeResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存分销商banner列表
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveDistributorBannerList(
    request: DistributorBannerListSaveByPortalTypeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveDistributorBannerList,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存门户基础信息设置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async savePortalBasicMessage(
    request: SavePortalBasicMessageUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.savePortalBasicMessage,
    operation?: string
  ): Promise<Response<SavePortalBasicMessageUpdateResponse>> {
    return commonRequestApi<SavePortalBasicMessageUpdateResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 保存海报配置
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async savePosterConfiguration(
    request: SavePosterConfigurationRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.savePosterConfiguration,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 启用或停用分销商管理员
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async saveStatusDistributorAdmin(
    request: SaveStatusDistributorAdminRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.saveStatusDistributorAdmin,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新分销商
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateDistributor(
    request: UpdateDistributorRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateDistributor,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改分销管理员
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateDistributorAdmin(
    request: UpdateDistributorAdminRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateDistributorAdmin,
    operation?: string
  ): Promise<Response<GeneralResponse>> {
    return commonRequestApi<GeneralResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新迷你分销商
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateMiniDistributor(
    request: UpdateDistributorNewRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateMiniDistributor,
    operation?: string
  ): Promise<Response<GeneralNewResponse>> {
    return commonRequestApi<GeneralNewResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
