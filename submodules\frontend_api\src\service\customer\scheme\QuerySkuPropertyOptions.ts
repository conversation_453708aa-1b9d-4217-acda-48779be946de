import KeyValue from '@api/service/common/models/KeyValue'
import SchemeSkuPropertyRequest from '@api/service/customer/scheme/models/SchemeSkuPropertyRequest'
import MsTradeQueryFrontGatewayTradeQueryForestage, {
  SkuPropertyListResponse,
  SkuPropertyRequest,
  SkuPropertyResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
import QueryTrainingMajor from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
/**
 * @description 查询方案sku
 */
class QuerySkuPropertyOptions {
  /**
   * 行业选项
   */
  industryOptions: KeyValue[] = []
  /**
   * 年度选项
   */
  yearOptions: KeyValue[] = []
  /**
   * 科目类型选项
   */
  subjectTypeOptions: KeyValue[] = []
  /**
   * 培训类别选项
   */
  trainingCategoryOptions: KeyValue[] = []
  /**
   * 培训专业选项
   */
  trainingProfessionalOptions: KeyValue[] = []
  /**
   * 地区选项
   */
  regionOptions: RegionTreeVo[] = []

  /**
   * 1-获取行业选项
   */
  async queryIndustryOptions() {
    const request = new SkuPropertyRequest()
    const response = await MsTradeQueryFrontGatewayTradeQueryForestage.listSkuPropertyCustomerPurchaseInServicer(
      request
    )
    const industryOptions = response.data?.industry || ([] as SkuPropertyResponse[])
    // 行业选项额外获取
    this.industryOptions = this.getCommonSkuPropertyOptions(industryOptions)
  }

  /**
   * 2-查询sku属性选项
   * @param skuQuery sku属性请求--UI页面绑定（行业id一定是有的）
   */
  async querySkuPropertyOptions(skuQuery: SchemeSkuPropertyRequest) {
    const request = skuQuery.toSkuPropertyRequest()
    const response = await MsTradeQueryFrontGatewayTradeQueryForestage.listSkuPropertyCustomerPurchaseInServicer(
      request
    )
    // 年度选项
    const yearOptions = response.data?.year || ([] as SkuPropertyResponse[])
    this.yearOptions = this.getCommonSkuPropertyOptions(yearOptions)
    // 科目类型选项
    const subjectTypeOptions = response.data?.subjectType || ([] as SkuPropertyResponse[])
    this.subjectTypeOptions = this.getCommonSkuPropertyOptions(subjectTypeOptions)
    // 培训类别选项
    const trainingCategoryOptions = response.data?.trainingCategory || ([] as SkuPropertyResponse[])
    this.trainingCategoryOptions = this.getCommonSkuPropertyOptions(trainingCategoryOptions)
    // 培训专业选项
    const trainingProfessionalOptions = response.data?.trainingProfessional || ([] as SkuPropertyResponse[])
    this.trainingProfessionalOptions = await this.getTrainingProfessionalOptions(
      trainingProfessionalOptions,
      skuQuery.industryId
    )
    // 地区选项
    this.regionOptions = await this.getRegionSkuPropertyOptions(response.data)
  }

  /**
   * 转换通用sku选项
   * @param isYear 是否是年度选项
   * @private
   */
  private getCommonSkuPropertyOptions(skuResp: SkuPropertyResponse[], isYear = false): KeyValue[] {
    let list = skuResp
    if (isYear) {
      // 如果是年度选项，需要排序
      list = list?.sort((a, b) => {
        return Number(a.skuPropertyValueId) - Number(b.skuPropertyValueId)
      })
    }
    const result = list?.map(sku => {
      const opt = new KeyValue()
      opt.key = sku.skuPropertyValueId
      opt.value = sku.skuPropertyValueName
      return opt
    })
    return result
  }

  /**
   * 转换地区sku选项
   * @private
   */
  private async getRegionSkuPropertyOptions(resp: SkuPropertyListResponse): Promise<RegionTreeVo[]> {
    let regionIds = [] as string[]
    let regionList = [] as RegionTreeVo[]
    let regionTree = [] as RegionTreeVo[]
    if (resp.province && resp.province.length) {
      regionIds = [...regionIds, ...new Set(resp.province.map(item => item.skuPropertyValueId).filter(Boolean))]
    }
    if (resp.city && resp.city.length) {
      regionIds = [...regionIds, ...new Set(resp.city.map(item => item.skuPropertyValueId).filter(Boolean))]
    }
    if (resp.county && resp.county.length) {
      regionIds = [...regionIds, ...new Set(resp.county.map(item => item.skuPropertyValueId).filter(Boolean))]
    }
    if (regionIds) {
      regionList = await QueryBusinessRegion.queryBusinessRegionByIdList(regionIds)
      regionTree = this.getRegionTreeByList(regionList)
    }
    return regionTree
  }

  /**
   * 根据列表获取地区树
   * @param parentId 父节点id，默认顶级是0
   */
  private getRegionTreeByList(list: RegionTreeVo[], parentId = '0'): RegionTreeVo[] {
    const nodeList = list.filter(item => item.parentId === parentId)
    nodeList.forEach(item => {
      item.children = this.getRegionTreeByList(list, item.id)
    })
    return nodeList
  }

  /**
   * 获取培训专业选项
   * @param industryId 行业skuId
   */
  private async getTrainingProfessionalOptions(
    skuResp: SkuPropertyResponse[],
    industryId: string
  ): Promise<KeyValue[]> {
    const result = [] as KeyValue[]
    const ids = [...new Set(skuResp?.map(item => item.skuPropertyValueId).filter(Boolean))]
    if (ids.length) {
      const dataList = await QueryTrainingMajor.getTrainingMajorByIdList({
        industryId,
        trainingMajorIdList: ids
      })
      dataList?.forEach(item => {
        const opt = new KeyValue()
        opt.key = item.propertyId
        opt.value = item.showName ? item.showName : item.name
        result.push(opt)
      })
    }
    return result
  }
}

export default QuerySkuPropertyOptions
