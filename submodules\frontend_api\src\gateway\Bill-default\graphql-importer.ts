import findBatchOrderBillPageList from './queries/findBatchOrderBillPageList.graphql'
import findInvoiceDownLoadUrl from './queries/findInvoiceDownLoadUrl.graphql'
import findNonBatchOrderBillPageList from './queries/findNonBatchOrderBillPageList.graphql'
import findOrderBillPageList from './queries/findOrderBillPageList.graphql'
import findTaxPayerByAccount from './queries/findTaxPayerByAccount.graphql'
import bindTaxPayer from './mutates/bindTaxPayer.graphql'
import disbandInvoice from './mutates/disbandInvoice.graphql'
import drawBatchBlue from './mutates/drawBatchBlue.graphql'
import drawBlue from './mutates/drawBlue.graphql'
import drawRed from './mutates/drawRed.graphql'
import freezeInvoice from './mutates/freezeInvoice.graphql'
import generateInvoice from './mutates/generateInvoice.graphql'
import generateInvoiceByCombineRule from './mutates/generateInvoiceByCombineRule.graphql'
import unBindTaxPayer from './mutates/unBindTaxPayer.graphql'
import unfreezeInvoice from './mutates/unfreezeInvoice.graphql'
import updateAutoBillDay from './mutates/updateAutoBillDay.graphql'
import updateInvoiceInfo from './mutates/updateInvoiceInfo.graphql'

export {
  findBatchOrderBillPageList,
  findInvoiceDownLoadUrl,
  findNonBatchOrderBillPageList,
  findOrderBillPageList,
  findTaxPayerByAccount,
  bindTaxPayer,
  disbandInvoice,
  drawBatchBlue,
  drawBlue,
  drawRed,
  freezeInvoice,
  generateInvoice,
  generateInvoiceByCombineRule,
  unBindTaxPayer,
  unfreezeInvoice,
  updateAutoBillDay,
  updateInvoiceInfo
}
