import CreateOrUpdateRole from '@api/service/management/authority/role/mutation/CreateOrUpdateRole'
import { MutationAddAccountRoles } from '@api/service/management/authority/role/mutation/MutationAddAccountRoles'
import { MutationDeleteRole } from '@api/service/management/authority/role/mutation/MutationDeleteRole'
import { MutationRemoveAccountRoles } from '@api/service/management/authority/role/mutation/MutationRemoveAccountRoles'

class RoleMutationFactory {
  /*
   *  创建Or更新
   * */
  getCreateOrUpdateRole() {
    return new CreateOrUpdateRole()
  }
  /*
   *  给账号添加角色
   * */
  getMutationAddAccountRoles() {
    return new MutationAddAccountRoles()
  }
  /*
   *  删除角色
   * */
  getMutationDeleteRole() {
    return new MutationDeleteRole()
  }
  /*
   *  给账号移除角色
   * */
  getMutationRemoveAccountRoles() {
    return new MutationRemoveAccountRoles()
  }
}

export default RoleMutationFactory
