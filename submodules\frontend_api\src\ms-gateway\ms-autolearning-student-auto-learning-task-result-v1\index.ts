import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-autolearning-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-autolearning-student-auto-learning-task-result-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * @Author: chenzeyu
@CreateTime: 2025-03-21  10:21
@Description: TODO
 */
export class QueryStudentAutoLearningTaskResultRequest {
  /**
   * 查询开始时间 beginDate和 endDate都不传则查全部
   */
  beginDate?: string
  /**
   * 查询结束时间
   */
  endDate?: string
  /**
   * 任务结果
@see StudentAutoLearningTaskResultResults
   */
  taskResult?: Array<number>
}

/**
 * 学员自动学习任务结果分页查询请求
<AUTHOR> By Cb
@since 2024/05/15 17:28
 */
export class StudentAutoLearningTaskResultQueryPageRequest {
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 每页数量
   */
  pageSize?: number
  /**
   * 姓名
   */
  name?: string
  /**
   * 身份证号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 学习方案名称
   */
  learningSchemeName?: string
  /**
   * 参训资格ID集合
   */
  qualificationIdList?: Array<string>
  /**
   * 结果集合
0 - 待执行
1 - 编排失败
2 - 执行中
3 - 执行成功
4 - 执行失败
5 - 已取消
6 - 终止
   */
  resultList?: Array<number>
}

/**
 * 学员任务结果请求
<AUTHOR>
@date 2025/3/26 16:36
 */
export class TaskResultByPageRequest {
  /**
   * 页码
   */
  pageNo?: number
  /**
   * 每页数量
   */
  pageSize?: number
  /**
   * 主任务ID
   */
  mainTaskIdList?: Array<string>
  /**
   * 姓名
   */
  name?: string
  /**
   * 身份证号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 学习方案名称
   */
  learningSchemeName?: string
  /**
   * 结果集合
0 - 待执行
1 - 编排失败
2 - 执行中
3 - 执行成功
4 - 执行失败
5 - 已取消
6 - 终止
   */
  resultList?: Array<number>
}

export class Range {
  key: string
  value: string
}

export class BusinessDomainIdentity {
  unitId: string
  servicerId: string
  applicationMemberType: number
  applicationMemberId: string
  applicationType: number
}

export class DataRouterIdentity {
  dataPlatformVersionId: string
  dataProjectId: string
}

export class HttpIdentity {
  ip: string
  domain: string
  requestUrl: string
}

export class MicroContext {
  sequenceNo: string
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  businessDomain: BusinessDomainIdentity
  servicerProvider: ServicerProvider
  userIdentity: UserIdentity
  dataRouterIdentity: DataRouterIdentity
  httpIdentity: HttpIdentity
}

export class ServicerProvider {
  unitId: string
  servicerType: number
  servicerId: string
}

export class UserIdentity {
  sessionId: string
  accountId: string
  rootAccountId: string
  accountType: number
  userId: string
}

/**
 * @Author: chenzeyu
@CreateTime: 2025-03-21  10:20
@Description: TODO
 */
export class QueryStudentAutoLearningTaskResultResponse {
  code: string
  message: string
  count: number
}

/**
 * 学员自动学习任务结果
<AUTHOR> By Cb
@since 2024/05/10 17:13
 */
export class StudentAutoLearningTaskResult {
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 学员自动学习任务结果ID
   */
  studentAutoLearningTaskResultId: string
  /**
   * 自动学习主任务ID
   */
  mainTaskId: string
  /**
   * 当前自动学习主任务ID
   */
  currentMainTaskId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 身份证号
   */
  idCard: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 学习方案ID
   */
  learningSchemeId: string
  /**
   * 学习方案名称
   */
  learningSchemeName: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 结果
@see StudentAutoLearningTaskResultResults
   */
  result: number
  /**
   * 状态码
   */
  code: number
  /**
   * 信息
   */
  message: string
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 来源ID
   */
  sourceId: string
  /**
   * 来源类型
   */
  sourceType: number
  properties: Array<StudentAutoLearningTaskResultExtendProperty>
}

/**
 * 学员自动学习任务结果拓展属性
<AUTHOR> By Cb
@since 2025/03/26 14:13
 */
export class StudentAutoLearningTaskResultExtendProperty {
  /**
   * 主键
   */
  id: string
  /**
   * 学员自动学习任务结果ID
   */
  studentAutoLearningTaskResultId: string
  /**
   * 键
   */
  name: string
  /**
   * 值
   */
  value: string
}

/**
 * 自动学习子任务
<AUTHOR>
 */
export class AutoLearningSubTask {
  /**
   * 子任务ID
   */
  subTaskId: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 微服务上下文
   */
  microContext: MicroContext
  /**
   * 主任务ID
   */
  mainTaskId: string
  /**
   * 子任务顺序
   */
  seq: number
  /**
   * 学习方案ID
   */
  learningSchemeId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 学习资源ID
   */
  learningResourceId: string
  /**
   * 期望开始时间
   */
  expectStartTime: string
  /**
   * 期望结束时间
   */
  expectEndTime: string
  /**
   * 任务数据
   */
  taskData: SubTaskData
  /**
   * 任务类型
@see AutoLearningSubTaskTypes
   */
  taskType: number
  /**
   * 任务状态
@see AutoLearningSubTaskStatus
   */
  status: number
  /**
   * 任务状态原因
@see StatusReason
   */
  statusReason: string
  /**
   * 重试次数
   */
  retries: number
  /**
   * 错误信息
   */
  errorMsg: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 所属的自动学习类型
@see com.fjhb.ms.autolearning.v1.api.constant.AutoLearningSupportedLearningType
   */
  belongedLearningType: number
}

/**
 * <AUTHOR>
@since
 */
export class ChooseCourseExecuteSettings implements SubTaskData {
  /**
   * 选课列表
   */
  chooseCourseList: Array<ChooseCourseParam>
  /**
   * 用户id
   */
  userId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 选课数据范围
   */
  ranges: Array<Range>
  /**
   * 选课时间
   */
  chooseCourseDate: string
  /**
   * 参训资格编号
   */
  qualificationId: string
  /**
   * 推荐时间，选课时回传，用于校验，该时间后学员有无自主选课
   */
  recommandDate: string
  /**
   * 任务类型
@see AutoLearningSubTaskTypes
   */
  type: number
}

/**
 * 课程学习方式自动学习执行配置
<AUTHOR>
 */
export class CourseAutoLearningExecuteSetting implements SubTaskData {
  /**
   * 学习进度(1% - 100%)
   */
  learningProgress: number
  /**
   * 本次学习进度
   */
  currentLearningProgress: number
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 学员ID
   */
  studentCourseId: string
  /**
   * 课件ID
   */
  coursewareId: string
  /**
   * 媒体资源ID
   */
  mediaId: string
  /**
   * 学习开始时间
   */
  studyStartTime: string
  /**
   * 学习结束时间
   */
  studyEndTime: string
  /**
   * 任务类型
@see AutoLearningSubTaskTypes
   */
  type: number
}

/**
 * 课后练习自动学习执行配置
<AUTHOR>
 */
export class CourseQuizAutoLearningExecuteSetting implements SubTaskData {
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 学员课程ID
   */
  studentCourseId: string
  /**
   * 课后测验ID
   */
  courseQuizId: string
  /**
   * 获得分数
   */
  score: number
  /**
   * 作答开始时间
   */
  answerStartTime: string
  /**
   * 作答结束时间
   */
  answerEndTime: string
  /**
   * 任务类型
@see AutoLearningSubTaskTypes
   */
  type: number
}

/**
 * 考试学习方式自动学习执行配置
<AUTHOR>
 */
export class ExamAutoLearningExecuteSetting implements SubTaskData {
  /**
   * 获得分数
   */
  score: number
  /**
   * 作答开始时间
   */
  answerStartTime: string
  /**
   * 作答结束时间
   */
  answerEndTime: string
  /**
   * 任务类型
@see AutoLearningSubTaskTypes
   */
  type: number
}

/**
 * 子任务数据
<AUTHOR>
 */
export interface SubTaskData {
  /**
   * 任务类型
@see AutoLearningSubTaskTypes
   */
  type: number
}

/**
 * <AUTHOR>
@since
 */
export class ChooseCourseParam {
  /**
   * 学习大纲编号
   */
  outlineId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 学员课程ID
若不提供 则默认使用UUIDUtils.generate()生成
   */
  studentCourseId: string
  /**
   * 课程类型
1-必修，2-选修
   */
  courseType: number
  /**
   * 学时
   */
  period: number
}

export class StudentAutoLearningTaskResultPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentAutoLearningTaskResult>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询最近一次失败的子任务
   * @param mainTaskId:
   * @return {@link List<AutoLearningSubTask>}
   * <AUTHOR> By Cb
   * @since 2024/8/8 16:13
   * @param query 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findLastFailSubTaskByMainTaskIdList(
    mainTaskId: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.findLastFailSubTaskByMainTaskIdList,
    operation?: string
  ): Promise<Response<Array<AutoLearningSubTask>>> {
    return commonRequestApi<Array<AutoLearningSubTask>>(
      SERVER_URL,
      {
        query: query,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员自动学习任务结果分页查询请求处理
   * @param request:
   * @return {@link StudentAutoLearningTaskResultQueryPageResponse}
   * <AUTHOR> By Cb
   * @since 2024/5/15 20:31
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryByPage(
    request: StudentAutoLearningTaskResultQueryPageRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryByPage,
    operation?: string
  ): Promise<Response<StudentAutoLearningTaskResultPage>> {
    return commonRequestApi<StudentAutoLearningTaskResultPage>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员自动学习任务结果分页查询请求处理
   * @param request:
   * @return {@link StudentAutoLearningTaskResultQueryPageResponse}
   * <AUTHOR> By Cb
   * @since 2024/5/15 20:31
   * @param query 查询 graphql 语法文档
   * @param qualificationIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryNewestTaskResultByQualificationIds(
    qualificationIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryNewestTaskResultByQualificationIds,
    operation?: string
  ): Promise<Response<Array<StudentAutoLearningTaskResult>>> {
    return commonRequestApi<Array<StudentAutoLearningTaskResult>>(
      SERVER_URL,
      {
        query: query,
        variables: { qualificationIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 用于统计指定状态列表下自动学习任务结果数量
   * @param request:
   * @return {@link QueryStudentAutoLearningTaskResultResponse}
   * <AUTHOR> By zcy
   * @since 2025/4/1 9:50
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryStudentAutoLearningTaskResult(
    request: QueryStudentAutoLearningTaskResultRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryStudentAutoLearningTaskResult,
    operation?: string
  ): Promise<Response<QueryStudentAutoLearningTaskResultResponse>> {
    return commonRequestApi<QueryStudentAutoLearningTaskResultResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员自动学习任务结果列表分页查询
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryStudentAutoLearningTaskResultByPage(
    request: TaskResultByPageRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryStudentAutoLearningTaskResultByPage,
    operation?: string
  ): Promise<Response<StudentAutoLearningTaskResultPage>> {
    return commonRequestApi<StudentAutoLearningTaskResultPage>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员正常自动学习任务结果查询请求处理
   * @param qualificationId:
   * @return {@link StudentAutoLearningTaskResult}
   * <AUTHOR> By Cb
   * @since 2024/7/24 10:49
   * @param query 查询 graphql 语法文档
   * @param qualificationId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryStudentNormalAutoLearningTaskResult(
    qualificationId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryStudentNormalAutoLearningTaskResult,
    operation?: string
  ): Promise<Response<StudentAutoLearningTaskResult>> {
    return commonRequestApi<StudentAutoLearningTaskResult>(
      SERVER_URL,
      {
        query: query,
        variables: { qualificationId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
