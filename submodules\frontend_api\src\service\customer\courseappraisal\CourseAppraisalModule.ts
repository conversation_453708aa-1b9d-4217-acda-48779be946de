import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ResponseStatus } from '../../../Response'
import CourseAppraisalGateway, { CourseAppraisalCreateRequest } from '@api/gateway/CommentEvaluation-default'
import { Role, RoleType, UnAuthorize } from '@api/Secure'
import { Page } from '@api/service/common/models/Page'
import platformUserGateway from '@api/gateway/PlatformUser'
import { CourseAppraisalRecordListDTO } from '@api/service/common/course-appraisal/CourseAppraisalRecordListDTO'
import LearningSchemeModule from '@api/service/customer/myscheme/LearningSchemeModule'
import BTPXCourseDefaultGateway from '@api/gateway/btpx@Course-default'
import CourseModule from '@api/service/customer/course/CourseModule'
import CourseAppraiseStatisticModule from '@api/service/customer/statistic/course-appraise-statistic/CourseAppraiseStatisticModule'
import { CourseAppraiseStatistic } from '@api/service/common/models/statistic/course-appraise-statistic/CourseAppraiseStatisticPage'
import UserConstantsAdapter from '@api/service/common/common-user/UserConstantsAdapter'
import CourseUserAppraisal from '@api/service/common/models/courseappraisal/CourseUserAppraisal'
import CourseAppraisal from '@api/service/common/models/courseappraisal/CourseAppraisal'

/**
 * 课程评价数据状态
 */
export interface IState {
  /**
   * 当前用户的课程评价集合
   */
  courseUserAppraisals: Array<CourseUserAppraisal>
  /**
   * 课程评价集合
   */
  courseAppraisals: Array<CourseAppraisal>
  /**
   * 课程评价记录分页数据
   */
  courseAppraisalRecordPageData: Array<CourseAppraisalRecordListDTO>
  /**
   * 课程评价记录分页总条数
   */
  courseAppraisalRecordPageTotalSize: number
  /**
   * 方案内所有课程的评价
   */
  schemeCourseAppraiseRecord: Array<CourseAppraisalRecordListDTO>
  /**
   * 方案内所有课程评价分页总条数
   */
  schemeCourseAppraiseRecordTotalSize: number
}

@Module({
  namespaced: true,
  store,
  name: 'CustomerCourseAppraisalModule',
  dynamic: true
})
class CourseAppraisalModule extends VuexModule implements IState {
  courseAppraisals = new Array<CourseAppraisal>()
  courseUserAppraisals = new Array<CourseUserAppraisal>()
  courseAppraisalRecordPageData: Array<CourseAppraisalRecordListDTO> = new Array<CourseAppraisalRecordListDTO>()
  courseAppraisalRecordPageTotalSize = 0
  schemeCourseMap = new Array<SchemeCourses>()
  schemeCourseAppraiseRecord = new Array<CourseAppraisalRecordListDTO>()
  schemeCourseAppraiseRecordTotalSize = 0

  @Mutation
  SET_COURSE_APPRAISAL(p: CourseAppraisal) {
    const m = this.courseAppraisals.find(e => e.courseId === p.courseId)
    m ? Object.assign(m, p) : this.courseAppraisals.push(p)
  }

  @Mutation
  SET_COURSE_USER_APPRAISAL(p: CourseUserAppraisal) {
    const m = this.courseUserAppraisals.find(e => e.courseId === p.courseId)
    m ? Object.assign(m, p) : this.courseUserAppraisals.push(p)
  }

  @Mutation
  private SET_COURSE_APPRAISAL_RECORD(courseAppraisalRecordPageData: Array<CourseAppraisalRecordListDTO>) {
    this.courseAppraisalRecordPageData = courseAppraisalRecordPageData
  }

  @Mutation
  private PUSH_COURSE_APPRAISAL_RECORD(courseAppraisalRecordPageData: Array<CourseAppraisalRecordListDTO>) {
    if (this.courseAppraisalRecordPageData) {
      this.courseAppraisalRecordPageData.push(...courseAppraisalRecordPageData)
    } else {
      this.courseAppraisalRecordPageData = courseAppraisalRecordPageData
    }
  }

  @Mutation
  private SET_COURSE_APPRAISAL_RECORD_TOTAL_SIZE(courseAppraisalRecordPageTotalSize: number) {
    this.courseAppraisalRecordPageTotalSize = courseAppraisalRecordPageTotalSize
  }

  @Mutation
  private PUSH_SCHEME_COURSES(schemeCourses: SchemeCourses) {
    this.schemeCourseMap.push(schemeCourses)
  }

  @Mutation
  private SET_SCHEME_COURSE_APPRAISE_RECORD(schemeCourseAppraiseRecord: Array<CourseAppraisalRecordListDTO>) {
    this.schemeCourseAppraiseRecord = schemeCourseAppraiseRecord
  }

  @Mutation
  private PUSH_SCHEME_COURSE_APPRAISE_RECORD(schemeCourseAppraiseRecord: Array<CourseAppraisalRecordListDTO>) {
    this.schemeCourseAppraiseRecord.push(...schemeCourseAppraiseRecord)
  }

  @Mutation
  private SET_SCHEME_COURSE_APPRAISAL_RECORD_TOTAL_SIZE(schemeCourseAppraiseRecordTotalSize: number) {
    this.schemeCourseAppraiseRecordTotalSize = schemeCourseAppraiseRecordTotalSize
  }

  /**
   * 初始化课程评价
   * @param courseIds 课程ID集合
   */
  @Action
  @UnAuthorize
  async initCourseAppraisal(courseIds: Array<string>): Promise<ResponseStatus> {
    // const courseIdList = courseIds.filter(e => !this.courseAppraisals.find(m => m.courseId === e))
    // if (courseIdList.length === 0) {
    //   return Promise.resolve(new ResponseStatus(200))
    // }
    //
    // const response = await CourseAppraisalGateway.loadCourseAppraisalList(courseIdList)
    // if (response.status.isSuccess()) {
    //   courseIdList.forEach(e => {
    //     const data = response.data.find(m => m.courseId === e)
    //     const m: CourseAppraisal = new CourseAppraisal()
    //
    //     if (data) {
    //       Object.assign(m, data)
    //     } else {
    //       m.courseId = e
    //       m.comprehensiveStars = 10
    //       m.contentQualityStars = 10
    //       m.teachingQualityStars = 10
    //     }
    //
    //     this.SET_COURSE_APPRAISAL(m)
    //   })
    // }
    // 走评价统计状态层取隔天数据
    return await CourseAppraiseStatisticModule.loadCourseAppraiseStatistic(courseIds)
  }

  /**
   * 加载用户课程评价
   * @param courseId 课程ID
   */
  @Action
  @Role([RoleType.user])
  async loadCourseUserAppraisal(courseId: string): Promise<ResponseStatus> {
    const appraisal = this.courseUserAppraisals.find(e => e.courseId === courseId)
    if (!appraisal) {
      const response = await CourseAppraisalGateway.loadUserCourseAppraisal(courseId)
      if (response.status.isSuccess()) {
        const data = response.data
        const m: CourseUserAppraisal = new CourseUserAppraisal()

        if (data) {
          m.isAppraised = true
          Object.assign(m, data)
        } else {
          m.isAppraised = false
          m.courseId = courseId
        }
        this.SET_COURSE_USER_APPRAISAL(m)
      }
      return response.status
    }
    return Promise.resolve(new ResponseStatus(200))
  }

  /**
   * 加载课程评价
   * @param courseId 课程ID
   */
  @Action
  @UnAuthorize
  async loadCourseAppraisal(courseId: string): Promise<ResponseStatus> {
    // const appraisal = this.courseAppraisals.find(e => e.courseId === courseId)
    // if (!appraisal) {
    //   const courseIds = new Array(courseId)
    //   const response = await CourseAppraisalGateway.loadCourseAppraisalList(courseIds)
    //   if (response.status.isSuccess()) {
    //     const data = response.data[0]
    //     const m: CourseAppraisal = new CourseAppraisal()
    //
    //     if (data) {
    //       Object.assign(m, data)
    //     } else {
    //       m.courseId = courseId
    //       m.comprehensiveStars = 10
    //       m.contentQualityStars = 10
    //       m.teachingQualityStars = 10
    //     }
    //
    //     this.SET_COURSE_APPRAISAL(m)
    //   }
    //   return response.status
    // }
    // return Promise.resolve(new ResponseStatus(200))
    // 走评价统计状态层取隔天数据
    return await CourseAppraiseStatisticModule.loadCourseAppraiseStatistic([courseId])
  }

  /**
   * 获取课程评价记录
   * @param param
   */
  @Action
  async loadCourseAppraisalRecordList(param: { page: Page; courseId: string; append: boolean }) {
    const response = await CourseAppraisalGateway.loadCourseAppraisalRecordListByIdList({
      pageIndex: param.page.pageNo,
      pageSize: param.page.pageSize,
      courseIdList: [param.courseId],
      shield: 0
    })
    if (response.status.isSuccess()) {
      const userIds = response.data.currentPageData.map(r => r.userId)
      const { status, data } = await platformUserGateway.listUserInfo({ userIdList: userIds })
      const resultList = new Array<CourseAppraisalRecordListDTO>()
      if (status.isSuccess()) {
        response.data.currentPageData.forEach(u => {
          const dto = new CourseAppraisalRecordListDTO()
          Object.assign(dto, u)
          const user = data.find(d => d.userId === dto.userId)
          dto.userName = user?.name
          dto.sex = UserConstantsAdapter.getSex(user?.gender)
          dto.displayPhotoUrl = user?.photo
          resultList.push(dto)
        })
      }
      if (param.append) {
        this.PUSH_COURSE_APPRAISAL_RECORD(resultList)
      } else {
        this.SET_COURSE_APPRAISAL_RECORD(resultList)
      }
      this.SET_COURSE_APPRAISAL_RECORD_TOTAL_SIZE(response.data.totalSize)
    }
    return response.status
  }

  /**
   * 评价一个课程
   * @param info 评价信息
   */
  @Action
  @Role([RoleType.user])
  async doAppraiseCourse(info: AppraiseCourseInfo): Promise<ResponseStatus> {
    const appraisal = this.courseUserAppraisals.find(e => e.courseId === info.courseId)
    if (appraisal && appraisal.isAppraised) {
      return Promise.reject(new ResponseStatus(500, '已评价不能再次评价'))
    }

    const m: CourseAppraisalCreateRequest = new CourseAppraisalCreateRequest()
    Object.assign(m, info)

    const response = await CourseAppraisalGateway.appraise(m)
    if (response.status.isSuccess()) {
      const data = response.data
      const m: CourseUserAppraisal = new CourseUserAppraisal()

      if (data) {
        m.isAppraised = true
        Object.assign(m, data)
      } else {
        m.isAppraised = false
        m.courseId = info.courseId
      }
      this.SET_COURSE_USER_APPRAISAL(m)
    }
    return response.status
  }

  /**
   * 查询某个方案下所有课程评价分页（本接口请在方案详情加载好后再请求）
   * @param param
   */
  @Action
  @UnAuthorize
  async pageCourseAppraiseByScheme(param: { schemeId: string; page: Page; append: boolean }) {
    // 查询状态schemeCourseMap里是否已经有本方案的课程id集合缓存，如果有，不请求，反之请求
    // 查询是否加载了该方案的详情，正常是加载好的，如果没有直接抛出异常
    // 拿出必要的课程id集合，存进状态PUSH_SCHEME_COURSES
    // 拿课程id集合请求分页loadCourseAppraisalRecordListByIdList
    if (!this.schemeCourseMap.find(c => c.schemeId === param.schemeId)) {
      const schemeDetail = LearningSchemeModule.getSchemeDetailInfo(param.schemeId)
      if (!schemeDetail) {
        throw new Error('请先加载方案详情LearningSchemeModule.loadSchemeDetailInfo')
      }
      const optionalPackageIds = schemeDetail.courseLearning.optionalPackages.map(c => c.packageId)
      const compulsoryPackageIds = schemeDetail.courseLearning.compulsoryPackages.map(c => c.packageId)
      const mySchemeDetail = LearningSchemeModule.getScheme(param.schemeId)
      if (!mySchemeDetail) {
        throw new Error('请先加载用户报名的方案信息LearningSchemeModule.pageMyScheme或init')
      }
      const interestPackageIds = mySchemeDetail.interestCourseSetting?.poolList
      const poolIds = new Array<string>()
      poolIds.push(...optionalPackageIds, ...compulsoryPackageIds)
      if (interestPackageIds) {
        poolIds.push(...interestPackageIds)
      }
      console.log('需要查询的课程包数量为' + poolIds.length)
      const coursesRes = await BTPXCourseDefaultGateway.getCourseInPoolList({
        poolIdList: poolIds
      })
      if (!coursesRes.status.isSuccess()) {
        return coursesRes.status
      }
      console.log('查出课程数为' + coursesRes.data.length)
      // 整合课程id
      const courseIds = coursesRes.data.map(c => c.courseId)
      this.PUSH_SCHEME_COURSES({
        schemeId: param.schemeId,
        courseIds: courseIds
      })
      await CourseModule.loadCourseToCaches({ courseIds: courseIds, reload: false })
    }
    const map = this.schemeCourseMap.find(c => c.schemeId === param.schemeId)
    // 请求分页
    const res = await CourseAppraisalGateway.loadCourseAppraisalRecordListByIdList({
      pageIndex: param.page.pageNo,
      pageSize: param.page.pageSize,
      courseIdList: map.courseIds,
      shield: 0
    })
    if (res.status.isSuccess()) {
      const userIds = res.data.currentPageData.map(r => r.userId)
      const { status, data } = await platformUserGateway.listUserInfo({ userIdList: userIds })
      const resultList = new Array<CourseAppraisalRecordListDTO>()
      if (status.isSuccess()) {
        res.data.currentPageData.forEach(u => {
          const dto = new CourseAppraisalRecordListDTO()
          Object.assign(dto, u)
          const user = data.find(d => d.userId === dto.userId)
          dto.userName = user?.name
          dto.sex = UserConstantsAdapter.getSex(user?.gender)
          dto.displayPhotoUrl = user?.photo
          const course = CourseModule.getCourseInfo(u.courseId)
          dto.courseName = course?.name
          resultList.push(dto)
        })
      }
      if (param.append) {
        this.PUSH_SCHEME_COURSE_APPRAISE_RECORD(resultList)
      } else {
        this.SET_SCHEME_COURSE_APPRAISE_RECORD(resultList)
      }
      this.SET_SCHEME_COURSE_APPRAISAL_RECORD_TOTAL_SIZE(res.data.totalSize)
    }
    return res.status
  }

  /**
   * 获取用户课程评价
   */
  get getCourseUserAppraisal() {
    return (courseId: string): CourseUserAppraisal | undefined => {
      return this.courseUserAppraisals.find(e => e.courseId === courseId)
    }
  }

  /**
   * 获取课程评价
   */
  get getCourseAppraisal() {
    return (courseId: string): CourseAppraisal | undefined => {
      // 走评价状态层取隔天数据
      let appraise = CourseAppraiseStatisticModule.getCourseAppraiseStatistic(courseId)
      if (!appraise) {
        appraise = new CourseAppraiseStatistic().construct()
        appraise.courseId = courseId
        appraise.averageComprehensive.average = 10
      }
      return {
        courseId: appraise?.courseId,
        comprehensiveStars: appraise?.averageComprehensive.average
      }
      // return this.courseAppraisals.find(e => e.courseId === courseId)
    }
  }

  /**
   * 获取课程评价记录分页
   * @param excludeUserId 要排除的用户id，不传则返回全部
   */
  get getCourseAppraisalRecordPage() {
    return (excludeUserId?: string): Array<CourseAppraisalRecordListDTO> => {
      if (excludeUserId) {
        return this.courseAppraisalRecordPageData.filter(c => c.userId !== excludeUserId)
      }
      return this.courseAppraisalRecordPageData
    }
  }

  /**
   * 获取课程评价记录分页总条数
   */
  get getCourseAppraisalRecordPageTotalSize() {
    return (excludeUserId?: string): number => {
      if (this.courseAppraisalRecordPageData.find(c => c.userId === excludeUserId)) {
        return this.courseAppraisalRecordPageTotalSize - 1
      }
      return this.courseAppraisalRecordPageTotalSize
    }
  }

  /**
   * 获取方案下课程评价记录分页数据
   */
  get getSchemeCourseAppraiseRecordPageList() {
    return this.schemeCourseAppraiseRecord
  }

  /**
   * 获取方案下课程评价记录分页总条数
   */
  get getSchemeCourseAppraiseRecordPageTotalSize() {
    return this.schemeCourseAppraiseRecordTotalSize
  }
}

/**
 * 评价课程信息
 */
export class AppraiseCourseInfo {
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 课程名称
   */
  courseName: string
  /**
   * 教学水平 1~10
   */
  teachingQualityStars: number
  /**
   * 上课内容 1~10
   */
  contentQualityStars: number
  /**
   * 评价内容
   */
  contents: string
}

/**
 * 方案下的选修必修兴趣课id缓存
 */
export class SchemeCourses {
  schemeId: string
  courseIds: Array<string>
}

export default getModule(CourseAppraisalModule)
