import { SignUpStatusEnum } from '@api/service/centre/trade/batch/order/enum/SignUpStatusList'
import {
  BatchOrderBasicDataRequest,
  BatchOrderRequest,
  BatchOrderStatusChangeTimeRequest,
  DateScopeRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import DataResolve from '@api/service/common/utils/DataResolve'
import SaleChannelType from '@api/service/common/enums/trade/SaleChannelType'
/**
 * @description 【集体缴费管理员】集体订单列表查询参数
 */
class QueryBatchOrderListVo {
  /**
   * 集体报名批次号
   */
  batchOrderNo = ''

  /**
   * 报名时间
   */
  signUpTime: string[] = null

  /**
   * 报名状态 1：未完成 2：已完成 3：已失效
   */
  signUpStatus: SignUpStatusEnum = null

  /**
   * 是否已经申请发票
   */
  isInvoiceApplied: boolean = null
  /*专题查询入参*/

  /**
   * 是否来源专题
   */
  isFromSpecialSubject: boolean = undefined

  /**
   * 专题id
   */
  saleChannelIds? = new Array<string>()

  /**
   * 转换本地vo模型为远端模型
   */
  to(): BatchOrderRequest {
    const to = new BatchOrderRequest()
    to.batchOrderNoList = this.batchOrderNo ? [this.batchOrderNo] : undefined
    to.basicData = new BatchOrderBasicDataRequest()
    if (DataResolve.isWeightyArr(this.signUpTime)) {
      to.basicData.batchOrderStatusChangeTime = new BatchOrderStatusChangeTimeRequest()
      to.basicData.batchOrderStatusChangeTime.unConfirmed = new DateScopeRequest()
      to.basicData.batchOrderStatusChangeTime.unConfirmed.begin = this.signUpTime[0] || undefined
      to.basicData.batchOrderStatusChangeTime.unConfirmed.end = this.signUpTime[1] || undefined
    } else {
      to.basicData.batchOrderStatusChangeTime = undefined
    }
    to.isInvoiceApplied = this.isInvoiceApplied ?? undefined
    // TODO 汇款凭证审核中1.0.0版本才提供
    if ((this.signUpStatus ?? null) !== null) {
      // 未完成
      if (this.signUpStatus === SignUpStatusEnum.Unfinished) {
        to.basicData.batchOrderStatusList = [0, 1, 4]
        to.basicData.batchOrderPaymentStatusList = [0, 1]
      }
      // 已完成
      if (this.signUpStatus === SignUpStatusEnum.Finished) {
        to.basicData.batchOrderStatusList = [1, 2]
        to.basicData.batchOrderPaymentStatusList = [2]
      }
      // 已失效
      if (this.signUpStatus === SignUpStatusEnum.Invalided) {
        to.basicData.batchOrderStatusList = [3]
      }
    } else {
      to.basicData.batchOrderStatusList = undefined
      to.basicData.batchOrderPaymentStatusList = undefined
    }
    to.basicData.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(this.isFromSpecialSubject)
    to.basicData.saleChannelIds = this.saleChannelIds
    return to
  }
}

export default QueryBatchOrderListVo
