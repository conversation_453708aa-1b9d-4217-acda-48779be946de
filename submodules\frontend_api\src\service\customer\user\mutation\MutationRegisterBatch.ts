import CollectiveRegister, {
  RegisterCollectiveRegisterAdminRequest
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { ResponseStatus } from '@hbfe/common'
import CreateBatchRequestVo from './vo/create/CreateBatchRequestVo'

/**
 * 集体账号注册
 */
class MutationRegisterBatch {
  createBatchParams = new CreateBatchRequestVo()

  async doRegister() {
    const createCollectiveRequest = new RegisterCollectiveRegisterAdminRequest()
    createCollectiveRequest.name = this.createBatchParams.name
    createCollectiveRequest.phone = this.createBatchParams.phone
    createCollectiveRequest.password = this.createBatchParams.password
    createCollectiveRequest.smsCode = this.createBatchParams.smsCode
    createCollectiveRequest.captcha = this.createBatchParams.captcha
    createCollectiveRequest.token = this.createBatchParams.token
    const response = await CollectiveRegister.registerCollectiveRegisterAdmin(createCollectiveRequest)
    return response
  }
}
export default MutationRegisterBatch
