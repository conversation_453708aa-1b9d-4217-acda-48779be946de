import findById from './queries/findById.graphql'
import applyDailyPracticeLearningToken from './mutates/applyDailyPracticeLearningToken.graphql'
import applyExamLearningToken from './mutates/applyExamLearningToken.graphql'
import applyExamOutlineErrorPronePracticeLearningToken from './mutates/applyExamOutlineErrorPronePracticeLearningToken.graphql'
import applyExamOutlinePracticeLearningToken from './mutates/applyExamOutlinePracticeLearningToken.graphql'
import applyQuestionTypeErrorPronePracticeLearningToken from './mutates/applyQuestionTypeErrorPronePracticeLearningToken.graphql'
import applyRandomPracticeLearningToken from './mutates/applyRandomPracticeLearningToken.graphql'
import applySingleCourseLearningToken from './mutates/applySingleCourseLearningToken.graphql'
import createIssue from './mutates/createIssue.graphql'
import createLS from './mutates/createLS.graphql'
import publishLS from './mutates/publishLS.graphql'
import removeIssue from './mutates/removeIssue.graphql'
import removeLS from './mutates/removeLS.graphql'
import updateIssue from './mutates/updateIssue.graphql'
import updateLS from './mutates/updateLS.graphql'

export {
  findById,
  applyDailyPracticeLearningToken,
  applyExamLearningToken,
  applyExamOutlineErrorPronePracticeLearningToken,
  applyExamOutlinePracticeLearningToken,
  applyQuestionTypeErrorPronePracticeLearningToken,
  applyRandomPracticeLearningToken,
  applySingleCourseLearningToken,
  createIssue,
  createLS,
  publishLS,
  removeIssue,
  removeLS,
  updateIssue,
  updateLS
}
