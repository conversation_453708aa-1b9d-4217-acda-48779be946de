import BatchOrderDetailBatchInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailBatchInfoVo'
import BatchOrderDetailBuyerInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailBuyerInfoVo'
import BatchOrderDetailPaymentInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailPaymentInfoVo'
import BatchOrderDetailInvoiceInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailInvoiceInfoVo'
import BatchOrderDetailDeliveryInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailDeliveryInfoVo'
import { BatchOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { BatchOrderTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
import BatchOrderUtils from '@api/service/management/trade/batch/order/query/utils/BatchOrderUtils'
import UserModule from '@api/service/management/user/UserModule'
import CollectiveManagerInfoVo from '@api/service/management/user/query/manager/vo/CollectiveManagerInfoVo'
import { BatchOrderPayModeEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderPayMode'
import DataResolve from '@api/service/common/utils/DataResolve'
import { BatchOrderInvoiceTypeEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderInvoiceType'

/**
 * @description 【集体报名订单】详情
 */

class BatchOrderDetailVo extends BatchOrderResponse {
  /**
   * 批次单号
   */
  batchOrderNo = ''

  /**
   * 提交批次时间
   */
  applyTime = ''

  /**
   * 付款时间
   */
  payTime = ''

  /**
   * 发货时间
   */
  deliveryTime = ''

  /**
   * 交易成功时间
   */
  tradeSuccessTime = ''

  /**
   * 交易关闭时间
   */
  tradeCloseTime = ''

  /**
   *  批次信息
   */
  batchInfo: BatchOrderDetailBatchInfoVo = new BatchOrderDetailBatchInfoVo()

  /**
   * 购买人信息
   */
  buyerInfo: BatchOrderDetailBuyerInfoVo = new BatchOrderDetailBuyerInfoVo()

  /**
   * 支付信息
   */
  paymentInfo: BatchOrderDetailPaymentInfoVo = new BatchOrderDetailPaymentInfoVo()

  /**
   * 发票信息
   */
  invoiceInfoList: BatchOrderDetailInvoiceInfoVo[] = []

  /**
   * 是否需要发票
   */
  requireInvoice: boolean = null

  /**
   * 是否展示配送信息
   */
  deliveryInfoVisible = false

  /**
   * 批次单是否处于退款中、退款成功
   */
  isBatchOrderUnderRefund: boolean = null

  /**
   * 配送信息
   */
  deliveryInfo: BatchOrderDetailDeliveryInfoVo = new BatchOrderDetailDeliveryInfoVo()

  static async from(response: BatchOrderResponse): Promise<BatchOrderDetailVo> {
    const detail = new BatchOrderDetailVo()
    Object.assign(detail, response)
    detail.batchOrderNo = response.batchOrderNo ?? ''
    detail.applyTime = response.basicData?.batchOrderStatusChangeTime?.committing ?? ''
    detail.payTime = response.basicData?.paymentStatusChangeTime?.paid ?? ''
    detail.deliveryTime = response.basicData?.deliveryStatusChangeTime?.delivered ?? ''
    detail.tradeSuccessTime = response.basicData?.batchOrderStatusChangeTime?.completed ?? ''
    detail.tradeCloseTime = response.basicData?.batchOrderStatusChangeTime?.closed ?? ''
    // 订单信息
    detail.batchInfo = await this.getBatchInfo(response)
    // 购买人信息
    detail.buyerInfo = await this.getBuyerInfo(response)
    // 支付信息
    detail.paymentInfo = this.getPaymentInfo(response)
    // 发票信息
    detail.invoiceInfoList = await BatchOrderUtils.getBatchOrderInvoiceInfoList(response)
    detail.requireInvoice = DataResolve.isWeightyArr(detail.invoiceInfoList) ? true : false
    // 配送信息
    const { deliveryInfoVisible, deliveryInfo } = this.getDeliveryInfo(detail.invoiceInfoList)
    detail.deliveryInfoVisible = deliveryInfoVisible
    detail.deliveryInfo = deliveryInfo
    const batchOrderRefundStatus = response.basicData?.batchOrderRefundStatus ?? undefined
    detail.isBatchOrderUnderRefund = batchOrderRefundStatus
      ? false
      : [1, 3].includes(batchOrderRefundStatus)
      ? true
      : false
    return detail
  }

  /**
   * 获取订单信息
   */
  private static async getBatchInfo(response: BatchOrderResponse): Promise<BatchOrderDetailBatchInfoVo> {
    const batchInfo = new BatchOrderDetailBatchInfoVo()
    batchInfo.batchOrderNo = response.batchOrderNo ?? ''
    batchInfo.saleChannel = response.basicData?.saleChannel ?? 0
    batchInfo.orderStatus = BatchOrderUtils.getBatchOrderStatus(response)
    if (batchInfo.orderStatus === BatchOrderTradeStatusEnum.Wait_Place_Order) {
      batchInfo.payAmount = await BatchOrderUtils.getUploadSignUpTotalAmountByBatchOrderNo(batchInfo.batchOrderNo)
      batchInfo.payPersonTime = await BatchOrderUtils.getUploadSignUpTotalCount(batchInfo.batchOrderNo)
    } else {
      batchInfo.payAmount = response.basicData?.amount ?? 0
      batchInfo.payPersonTime = response.basicData?.orderForBatchCount ?? 0
    }
    return batchInfo
  }

  /**
   * 获取购买人信息
   */
  private static async getBuyerInfo(response: BatchOrderResponse): Promise<BatchOrderDetailBuyerInfoVo> {
    const buyerInfo = new BatchOrderDetailBuyerInfoVo()
    const userId = response.creator?.userId ?? ''
    if (!userId) return buyerInfo
    const userIds = [userId]
    const queryRemote = UserModule.queryUserFactory.queryCollectiveManagerList
    const res = await queryRemote.queryCollectiveManagerInfoList(userIds)
    res?.forEach((item: CollectiveManagerInfoVo) => {
      if (item.userId === userId) {
        buyerInfo.buyerId = item.userId ?? ''
        buyerInfo.buyerName = item.userName ?? ''
        buyerInfo.buyerPhone = item.phone ?? ''
        buyerInfo.buyerAccount = item.phone ?? ''
      }
    })
    return buyerInfo
  }

  /**
   * 获取支付信息
   */
  private static getPaymentInfo(response: BatchOrderResponse): BatchOrderDetailPaymentInfoVo {
    const paymentInfo = new BatchOrderDetailPaymentInfoVo()
    paymentInfo.payMode =
      response.payInfo?.paymentOrderType === 1
        ? BatchOrderPayModeEnum.Online_Pay
        : response.payInfo?.paymentOrderType === 2
        ? BatchOrderPayModeEnum.Offline_Pay
        : null
    paymentInfo.terminalCode = BatchOrderUtils.getTerminalCode(response)
    paymentInfo.payChannelName = response.payInfo?.receiveAccount?.payChannelName
    paymentInfo.tradeNo = response.payInfo?.flowNo ?? ''
    paymentInfo.applyPayDate = response.basicData?.paymentStatusChangeTime?.paying
    paymentInfo.paySuccessDate = response.basicData?.paymentStatusChangeTime?.paid
    if (DataResolve.isWeightyArr(response.payInfo?.paymentVoucherList)) {
      paymentInfo.remittanceVoucherList = BatchOrderUtils.getRemittanceVoucherInfoList(
        response.payInfo.paymentVoucherList
      )
    }
    return paymentInfo
  }

  /**
   * 获取配送信息
   */
  private static getDeliveryInfo(invoiceInfoList: BatchOrderDetailInvoiceInfoVo[]) {
    const result = {
      deliveryInfoVisible: false,
      deliveryInfo: new BatchOrderDetailDeliveryInfoVo()
    }
    if (DataResolve.isWeightyArr(invoiceInfoList)) {
      const enableDeliveryOptions = [
        BatchOrderInvoiceTypeEnum.VAT_OFFLINE_INVOICE,
        BatchOrderInvoiceTypeEnum.VAT_SPECIAL_INVOICE
      ]
      const invoiceInfo = invoiceInfoList.find(item => {
        return enableDeliveryOptions.includes(item.invoiceType)
      })
      if (invoiceInfo) {
        result.deliveryInfoVisible = true
        result.deliveryInfo = invoiceInfo.deliveryInfo
      }
    }
    return result
  }
}

export default BatchOrderDetailVo
