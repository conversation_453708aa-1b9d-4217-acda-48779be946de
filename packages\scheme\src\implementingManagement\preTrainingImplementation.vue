<template>
  <div class="f-p15" id="container">
    <div class="m-side-positioner">
      <div class="item" :class="{ 'z-cur': activeTabName == 'section1' }" @click="crollToSection('section1')">
        <div class="dot"></div>
        <div class="tit">报到设置</div>
      </div>

      <div class="item" :class="{ 'z-cur': activeTabName == 'section2' }" @click="crollToSection('section2')">
        <div class="dot"></div>
        <div class="tit">考勤设置</div>
      </div>
      <div class="item" :class="{ 'z-cur': activeTabName == 'section3' }" @click="crollToSection('section3')">
        <div class="dot"></div>
        <div class="tit">学习资料设置</div>
      </div>
    </div>
    <div style="padding-right: 160px;">
      <registration-option
        class="scroll-target"
        id="section1"
        data-scroll="section"
        ref="registrationRef"
        :reportConfig="implementConfig.reportConfig"
      ></registration-option>
      <attendance-option
        class="scroll-target"
        data-scroll="section"
        ref="attendanceRef"
        id="section2"
        :implementConfig="implementConfig"
        :attendanceConfig="implementConfig.attendanceConfig"
      ></attendance-option>
      <learning-materials-option
        class="scroll-target"
        data-scroll="section"
        ref="learningMaterialsRef"
        id="section3"
        :implementConfig="implementConfig"
      ></learning-materials-option>
    </div>
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Prop, Provide } from 'vue-property-decorator'
  import AttendanceOption from './__components__/attendanceOption.vue'
  import LearningMaterialsOption from './__components__/learningMaterialsOption.vue'
  import RegistrationOption from './__components__/registrationOption.vue'
  import ImplementConfig from '@api/service/management/implement/ImplementConfig'
  import { bind, debounce } from 'lodash-decorators'
  import SchemeStepProcess from '@api/service/management/train-class/Utils/SchemeStepProcess'

  class Anchor {
    id: string
    offsetTop: number
  }

  @Component({
    components: {
      AttendanceOption,
      LearningMaterialsOption,
      RegistrationOption
    }
  })
  export default class extends Vue {
    @Ref('attendanceRef') attendanceRef: AttendanceOption
    @Ref('learningMaterialsRef') learningMaterialsRef: LearningMaterialsOption
    @Ref('registrationRef') registrationRef: RegistrationOption

    /**
     * 方案信息
     */
    @Prop({
      type: String,
      default: ''
    })
    schemeId: string
    // todo
    form = {}
    tableData = [{}]

    // 右侧激活项
    activeTabName = 'section1'

    // 获取训前实施配置
    implementConfig: ImplementConfig = new ImplementConfig('')

    originTop = 0

    setTimeOutId: any = undefined

    @Provide('SchemeStepProcess')
    SchemeStepProcess = new SchemeStepProcess()

    created() {
      // 获取训前实施配置
      this.implementConfig = new ImplementConfig(this.schemeId)
      // 获取报道配置
      this.implementConfig.reportConfig.getReportConfig()
      // 获取考勤设置
      this.implementConfig.attendanceConfig.getAttendanceConfig()
      // 获取学习资料配置
      this.implementConfig.learningMaterialsConfig.getLearningMaterialsConfig()
    }

    /**
     * 点击按钮 滚动下滑
     */
    crollToSection(sectionId: string) {
      window.removeEventListener('scroll', this.onScroll, true)

      const section = document.getElementById(sectionId)
      if (section) {
        section.scrollIntoView({
          behavior: 'smooth' // 平滑滚动效果
        })

        this.activeTabName = sectionId
      }

      if (this.setTimeOutId) {
        clearTimeout(this.setTimeOutId)
      }

      this.setTimeOutId = setTimeout(() => {
        window.addEventListener('scroll', this.onScroll, true)
        clearTimeout(this.setTimeOutId)
        this.setTimeOutId = undefined
      }, 1000)
    }

    @debounce(50)
    @bind
    onScroll() {
      const anchors = document.querySelectorAll('[data-scroll="section"]') // 获取所有ElCollapse的类名
      const container = document.querySelector('#container')
      if (!container || !anchors) return
      // 获取当前滚动的top
      const top = container.getBoundingClientRect().top

      const getCurrentAnchor = (top: number, elementList: Anchor[]) => {
        for (const anchor of elementList) {
          if (this.originTop - top < anchor.offsetTop) {
            return anchor
          }
        }
      }

      const elementList: Anchor[] = Array.from(anchors).reduce((acc: Anchor[], item: HTMLElement, index: number) => {
        const rect = item.getBoundingClientRect()
        const offsetTop = index > 0 ? acc[index - 1].offsetTop + rect.height : rect.height
        acc.push({ id: item.id, offsetTop })
        return acc
      }, [])
      this.activeTabName = getCurrentAnchor(top, elementList).id
    }

    mounted() {
      this.originTop = document.querySelector('#container').getBoundingClientRect().top
      window.addEventListener('scroll', this.onScroll, true)
    }

    unmounted() {
      window.removeEventListener('scroll', this.onScroll, true)
      this.SchemeStepProcess.stopPolling()
    }
  }
</script>
<style scoped lang="scss">
  .scroll-target {
    scroll-margin-top: 53px; /* 根据需要调整偏移量 */
  }
</style>
