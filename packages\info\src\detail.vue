<route-params content="/:id"></route-params>
<route-meta>
{
"title": "资讯详情"
}
</route-meta>
<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/basic-data/info' }">资讯管理</el-breadcrumb-item>
      <el-breadcrumb-item>资讯详情</el-breadcrumb-item>
    </el-breadcrumb>
    我是资讯id为{{ id }}的资讯详情页
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    id = ''
    created() {
      this.init()
    }
    init() {
      this.id = this.$route.params?.id
    }
  }
</script>

<style scoped></style>
