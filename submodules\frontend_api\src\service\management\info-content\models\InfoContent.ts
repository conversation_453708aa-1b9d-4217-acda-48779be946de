/**
 *
 * 资讯内容
 * @author: eleven
 * @date: 2020/4/18
 */
import { NewsDTO } from '@api/gateway/PlatformNewNotice'
import { AssociateUnit } from './AssociateUnit'

export class InfoContent {
  /**
   * id
   */
  id: string
  /**
   * 封面图片
   */
  imageUrl: string
  /**
   * 资讯标题
   */
  title: string
  /**
   * 资讯正文
   */
  content: string
  /**
   * 资讯状态
   * 0、草稿
   * 1、发布
   */
  status: number
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 是否弹窗公告
   */
  pop: boolean
  /**
   * 弹窗开始时间
   */
  popStartTime: string
  /**
   * 弹窗结束时间
   */
  popEndTime: string
  /**
   * 资讯来源
   */
  origin: string
  /**
   * 分类id
   */
  categoryId?: string
  /**
   * 关联展示施教机构
   */
  associateUnitList: Array<AssociateUnit>
  /**
   * 是否为重要咨询
   */
  isImportance = 0
  /**
   * 发布者Id
   */
  publishPersonId: string
  /**
   * 在发布的地区
   */
  regionIds?: Array<string>

  /**
   * 转成远端参数
   */
  toRemote() {
    const remote = new NewsDTO()
    remote.id = this.id
    remote.title = this.title
    remote.photoUrl = this.imageUrl
    remote.content = this.content
    remote.origin = this.origin
    remote.publishTime = this.publishTime
    remote.type = this.pop ? 2 : 1
    remote.status = this.status
    remote.popStart = this.popStartTime
    remote.popOver = this.popEndTime
    remote.isImportance = this.isImportance
    remote.categoryId = this.categoryId
    remote.regionIds = this.regionIds
    // if (this.associateUnitList) {
    //   remote.associateUnitList = this.associateUnitList.map(p => {
    //     const item = new NewsAssociateUnitDTO1()
    //     item.associateUnitId = p.associateUnitId
    //     item.categoryId = p.categoryId
    //     return item
    //   })
    // }
    return remote
  }
}
