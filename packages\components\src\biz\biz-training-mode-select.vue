<!--培训形式下拉选-->
<template>
  <el-select
    v-model="selectedValue"
    :placeholder="placeholder"
    class="el-input"
    @change="selectChange"
    :disabled="disabled"
    filterable
    clearable
  >
    <el-option v-for="item in list" :label="item.desc" :value="item.code" :key="item.code"></el-option>
  </el-select>
</template>

<script lang="ts">
  import { Vue, Component, Prop, Watch } from 'vue-property-decorator'
  import TrainingMode from '@api/service/common/scheme/enum/TrainingMode'

  @Component({})
  export default class extends Vue {
    /**
     * 绑定的值
     */
    @Prop({ type: String, default: '' }) value: string

    /**
     * 提示语
     */
    @Prop({ type: String, default: '请选培训形式' }) placeholder: string

    /**
     * 是否禁用
     */
    @Prop({ type: Boolean, default: false }) disabled: boolean
    /**
     * 选中的值
     */
    selectedValue = ''

    @Watch('value')
    valueChange() {
      this.selectedValue = this.value
    }

    /**
     * 列表
     */
    get list() {
      return TrainingMode.list()
    }

    /**
     * 选中期别
     */
    selectChange() {
      this.$emit('input', this.selectedValue)
    }
  }
</script>
