zhu
<route-meta>
{
"title": "资讯类别选择器"
}
</route-meta>
<template>
  <el-cascader
    v-if="show"
    :props="props"
    v-model="selctValue"
    :options="options"
    :placeholder="placeholder"
    :clearable="clearable"
    collapse-tags
    @change="onInput"
    v-bind="$attrs"
    :disabled="disabled"
    :show-all-levels="false"
  ></el-cascader>
</template>

<script lang="ts">
  import { NewsCategoryResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import NewsModule from '@api/service/management/news/NewsModule'
  import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
  class Category extends NewsCategoryResponse {
    children?: Array<Category>
    fatherId?: string
  }
  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    disabled: boolean

    @Prop({
      default: true
    })
    clearable: boolean

    @Prop({
      default: false
    })
    multiple: boolean

    @Prop({
      type: Array
    })
    value: Array<string>

    @Prop({
      default: '请选择资讯分类',
      type: String
    })
    placeholder: string

    @Prop({
      default: false
    })
    checkStrictly: boolean

    // 初始内容 无回显的情况 string[]
    // 回显的数据结构参见 https://element.eleme.cn/#/zh-CN/component/cascader
    options = new Array<Category>()
    // 当前选中的值
    selctValue: string[] = []
    show = true
    props = {}
    informationClassificationOptions = new Array<Category>()
    @Emit('input')
    onInput(values: string) {
      this.$emit('getNewsCode', this.informationClassificationOptions, values)
      return values
    }
    @Watch('value', { immediate: true, deep: true })
    watchValue(val: Array<string>) {
      this.selctValue = val
    }

    async created() {
      this.setProps()
      // 下面注释的方法、逻辑在父组件执行了
      //   await this.getInformationClassificationStair()
      //   this.echo()
      //   if (this.value) {
      //     let secondCategoryType: Category
      //     this.informationClassificationOptions.map(item => {
      //       const secondCategoryTypeChild = item.children?.find(ite => ite.newsCategoryId === this.value[0])
      //       if (secondCategoryTypeChild) secondCategoryType = secondCategoryTypeChild
      //     })
      //     if (secondCategoryType) {
      //       this.selctValue = [secondCategoryType.fatherId, ...this.value]
      //     }
      //   }
    }

    // 获取一级资讯分类
    async getInformationClassificationStair() {
      this.informationClassificationOptions = await NewsModule.queryNewsFactory
        .getQueryNewsList()
        .queryNewsRootCategory()
      this.$emit('getParentOptions', this.informationClassificationOptions)
      for (let i = 0; i < this.informationClassificationOptions.length; i++) {
        const element = this.informationClassificationOptions[i]
        const children = (await NewsModule.queryNewsFactory
          .getQueryNewsList()
          .queryNewsChildrenCategory(element.newsCategoryId)) as Array<Category>
        if (children?.length) {
          this.$emit('getChildOptions', children)
        }
        for (let j = 0; j < children.length; j++) {
          const childrenElement = children[j]
          childrenElement.fatherId = element.newsCategoryId
        }
        element.children = children
      }
    }

    setProps() {
      this.props = {
        // lazy: false,
        value: 'newsCategoryId',
        label: 'categoryName',
        multiple: this.multiple,
        checkStrictly: this.checkStrictly
      }
    }

    /**
     * 数据回显（回显需要把options的路径拼全，具体看源码吧）
     * 这边回显只考虑单选。多选需要另外封装。本项目暂无
     * 原理类似
     */
    echo() {
      this.options = this.informationClassificationOptions

      this.treeData(this.options)
      this.show = true
    }
    /**
     * 地区去掉最末级children为空
     */
    treeData(data: Category[]) {
      for (let i = 0; i < data?.length; i++) {
        if (data[i].children?.length < 1) {
          delete data[i].children
        } else {
          this.treeData(data[i].children)
        }
      }
      return data
    }

    getCategory(id: string, category: Category): Category {
      if (id === category.newsCategoryId) {
        return category
      }
      for (let i = 0; i < category.children?.length; i++) {
        const element = category.children[i]
        if (element.newsCategoryId === id) {
          return element
        } else {
          const temp = this.getCategory(id, element)
          if (temp) {
            return temp
          }
        }
      }
    }
  }
</script>
