import Mockjs from 'mockjs'
import TypeEnum from '@api/service/management/online-school-config/column/query/enum/TypeEnum'
import { MenuSettingInfo } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import DtoColumn from '@api/service/management/online-school-config/column/query/dto/Column'
import { ResponseStatus } from '@hbfe/common'
import MsServicerV1, {
  TrainingInstitutionMenuCreateRequest,
  TrainingInstitutionMenuUpdateRequest
} from '@api/ms-gateway/ms-servicer-v1'

class Column extends DtoColumn {
  // ui
  tempoName = ''
  onEditMode = false
  onSaving = false
  onShowChildren = false
  status = 1
  sort: number = undefined

  toggleShowChildren() {
    this.onShowChildren = !this.onShowChildren
  }

  get hasChildren() {
    return this.children.length
  }

  get isBuildIn() {
    return this.sourceType === 1
  }

  /**
   * 本地添加子节点
   */
  addChildren() {
    const newColumn = new Column(Mockjs.Random.id())
    newColumn.onEditMode = true
    newColumn.status = 0
    newColumn.parentId = this.id
    this.children.push(newColumn)
  }

  enableEdit() {
    this.onEditMode = true
  }

  cancelEdit() {
    this.tempoName = this.name
    this.onEditMode = false
  }

  removeChildren(id: string) {
    const index = this.children.findIndex(column => column.id === id)
    // 如果是本地缓存状态，则直接删除掉
    this.children.splice(index, 1)
  }

  get isInformation() {
    return this.type === TypeEnum.information
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name
    }
  }

  static from(response: MenuSettingInfo) {
    const detail = new Column()
    detail.id = response.id
    detail.name = response.name
    detail.tempoName = response.name
    detail.allowChildrenConfig = response.allowChildren
    detail.type = response.menuType
    detail.parentId = response.parentId || '-1'
    detail.link = response.link
    detail.enable = response.enable
    detail.code = response.code
    detail.sourceType = response.sourceType
    detail.sort = response.sort
    return detail
  }

  resetStatus() {
    this.onSaving = false
    this.onEditMode = false
  }

  isInCache() {
    return this.status === 0
  }
  async doEnable(): Promise<ResponseStatus> {
    this.onSaving = true
    const { status } = await MsServicerV1.enableTrainingInstitutionMenu(this.id)
    this.enable = true
    this.onSaving = false
    return status
  }

  async doDisable(): Promise<ResponseStatus> {
    this.onSaving = true
    const { status } = await MsServicerV1.disableTrainingInstitutionMenu(this.id)
    this.enable = false
    this.onSaving = false
    return status
  }

  async doModifyName() {
    if (this.name === this.tempoName) {
      return Promise.reject(new ResponseStatus(501, '无任何修改'))
    }
    this.onSaving = true
    const request = new TrainingInstitutionMenuUpdateRequest()
    request.menuName = this.tempoName
    request.menuId = this.id
    request.menuType = this.type
    request.menuLink = this.link
    request.menuParentId = this.parentId
    request.code = this.code
    request.sort = this.sort
    const { status } = await MsServicerV1.updateTrainingInstitutionMenu(request)
    if (status.isSuccess()) {
      this.name = this.tempoName
      this.resetStatus()
      return status
    }
    this.onSaving = false
    return Promise.reject(new ResponseStatus(500, '修改失败'))
  }

  async doCreate() {
    this.onSaving = true
    const request = new TrainingInstitutionMenuCreateRequest()
    request.menuName = this.tempoName
    request.sort = 0
    request.link = ''
    request.parentId = this.parentId
    const { status } = await MsServicerV1.createNewsCategoryTrainingInstitutionMenu(request)
    if (status.isSuccess()) {
      this.name = this.tempoName
      this.resetStatus()
      return status
    }
    this.onSaving = false
    return Promise.reject(new ResponseStatus(500, '创建成功'))
  }
}

export default Column
