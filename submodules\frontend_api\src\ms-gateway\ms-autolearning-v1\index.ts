import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-autolearning-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-autolearning-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class IntegerRange {
  min: number
  max: number
}

export class TimeRange {
  min?: string
  max?: string
}

/**
 * <AUTHOR>
@since
 */
export class MainTaskIdsRequest {
  /**
   * 主任务id列表
   */
  taskIds?: Array<string>
}

/**
 * 网校智能学习服务配置
<AUTHOR>
@date 2024/5/10 16:39
 */
export class OnlineSchoolSmartLearningServiceConfigRequest {
  /**
   * 服务商ID(所属网校ID)
   */
  servicerId?: string
  /**
   * 网校智能学习服务ID
   */
  onlineSchoolSmartLearningServiceId?: string
  /**
   * 课程学习配置
   */
  courseLearningConfigure?: CourseLearningConfigure
  /**
   * 课程测验配置
   */
  courseQuizConfigure?: CourseQuizConfigure
  /**
   * 考试配置
   */
  examConfigure?: ExamConfigure
}

/**
 * @Author: chenzeyu
@CreateTime: 2025-03-21  14:38
@Description: TODO
 */
export class QueryLastTimeExpectationStartStudyDateRequest {
  /**
   * 智能学习主任务id
   */
  autoLearningMainTaskId?: string
}

/**
 * @Author: chenzeyu
@CreateTime: 2025-03-20  10:11
@Description: 重启智能学习任务请求
 */
export class RestartAutoLearningTaskRequest {
  /**
   * 学员自动学习任务结果ID
   */
  studentAutoLearningTaskResultId?: string
  /**
   * 重启智能学习任务期望开始学习时间配置类型
@see RestartAutoLearningTaskStudyStartTimeConfigTypes
   */
  studyStartTimeConfigTypes: number
  /**
   * 期望开始学习时间，仅当studyStartTimeConfigTypes为自定义时生效
   */
  studyStartTime?: string
  /**
   * 是否允许重叠
   */
  allowOverlap: boolean
}

/**
 * 校验学习方案是否存在自动学习
<AUTHOR> By Cb
@since 2024/02/26 11:55
 */
export class ValidLearningSchemeExistAutoLearningRequest {
  /**
   * 学习方案ID列表
   */
  learningSchemeIdList: Array<string>
}

/**
 * 课程学习配置
<AUTHOR> By Cb
@since 2024/05/10 9:06
 */
export class CourseLearningConfigure {
  /**
   * 每天学习时间区间列表
   */
  everyDayLearningTimeList?: Array<TimeRange>
  /**
   * 每天不学习时间列表
   */
  everyDayNotLearningTimeList?: Array<TimeRange>
  /**
   * 首次开始学习时间 (报名后的X天 - X天)
   */
  firstLearningDayRange?: IntegerRange
  /**
   * 每天学习时长区间(秒)
   */
  everyDayLearningTimeRange?: IntegerRange
  /**
   * 每次学习时长区间(秒)
   */
  everyLearningTimeRange?: IntegerRange
  /**
   * 休息时长区间(秒) 达到每次学习时长区间后休息-随机休息60~180分钟
   */
  restTimeRange?: IntegerRange
  /**
   * 是否启用
默认:true
   */
  enable?: boolean
  /**
   * 每天学习学时区间
   */
  everyDayLearningPeriodRange?: IntegerRange
  /**
   * 每次学习学时区间
   */
  everyLearningPeriodRange?: IntegerRange
  /**
   * 规则类型
0-按课程物理时长 1-按课程学习学时 2-固定时长 3-按学习约束限制
   */
  ruleType: number
}

/**
 * 课程测验配置
<AUTHOR> By Cb
@since 2024/05/10 9:12
 */
export class CourseQuizConfigure {
  /**
   * 答题时长区间(秒)  默认 900s - 3600s (15-60分钟)
   */
  answerTimeRange?: IntegerRange
  /**
   * 是否启用
默认:true
   */
  enable?: boolean
}

/**
 * 考试配置
<AUTHOR> By Cb
@since 2024/05/10 9:14
 */
export class ExamConfigure {
  /**
   * 答题时长区间(秒)  (最少考试总时长1/3)
   */
  answerTimeRange?: IntegerRange
  /**
   * 时长占比(占配置时长的比例)
   */
  durationRatio?: number
  /**
   * 是否启用
默认:true
   */
  enable?: boolean
}

/**
 * <AUTHOR>
@since
 */
export class BatchTerminateAutoLearningResponse {
  /**
   * 批量处理响应
   */
  responses: Array<TerminateAutoLearningResponse>
}

/**
 * @Author: chenzeyu
@CreateTime: 2025-03-21  14:36
@Description: TODO
 */
export class QueryLastTimeExpectationStartStudyDateResponse {
  /**
   * 期望开始学习时间(为空则是没有填写)
   */
  expectationStartStudyDate: string
}

/**
 * @Author: chenzeyu
@CreateTime: 2025-03-20  10:49
@Description: 重启智能学习任务响应
 */
export class RestartAutoLearningTaskResponse {
  /**
   * code
   */
  code: string
  /**
   * 消息
   */
  msg: string
}

/**
 * <AUTHOR>
@since
 */
export class TerminateAutoLearningResponse {
  /**
   * code
200  正常
501  处理中
502  已终止
   */
  code: string
  /**
   * 异常信息
   */
  msg: string
  /**
   * 任务id
   */
  taskId: string
}

/**
 * 校验学习方案是否存在自动学习
<AUTHOR> By Cb
@since 2024/02/26 11:55
 */
export class ValidLearningSchemeExistAutoLearningResponse {
  /**
   * 校验结果列表
key: 学习方案ID
value: 学习方案是否存在自动学习
   */
  resultMap: Map<string, string>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量中止智能学习任务
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchTerminateAutoLearning(
    request: MainTaskIdsRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchTerminateAutoLearning,
    operation?: string
  ): Promise<Response<BatchTerminateAutoLearningResponse>> {
    return commonRequestApi<BatchTerminateAutoLearningResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询主任务对应的首次开始学习时间
   * @param request:
   * @return {@link QueryLastTimeExpectationStartStudyDateResponse}
   * <AUTHOR> By czy
   * @since 2025/4/1 15:55
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryLastTimeExpectationStartStudyDate(
    request: QueryLastTimeExpectationStartStudyDateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.queryLastTimeExpectationStartStudyDate,
    operation?: string
  ): Promise<Response<QueryLastTimeExpectationStartStudyDateResponse>> {
    return commonRequestApi<QueryLastTimeExpectationStartStudyDateResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重新编排自动学习主任务
   * @param autoLearningMainTaskId:
   * <AUTHOR> By Cb
   * @since 2024/5/13 11:09
   * @param mutate 查询 graphql 语法文档
   * @param autoLearningMainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rearrangementAutoLearning(
    autoLearningMainTaskId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.rearrangementAutoLearning,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { autoLearningMainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重启智能学习任务
   * @param request:
   * @return {@link RestartAutoLearningTaskResponse}
   * <AUTHOR> By czy
   * @since 2025/4/1 15:55
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async restartAutoLearningTask(
    request: RestartAutoLearningTaskRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.restartAutoLearningTask,
    operation?: string
  ): Promise<Response<RestartAutoLearningTaskResponse>> {
    return commonRequestApi<RestartAutoLearningTaskResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新网校智能学习服务配置
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateOnlineSchoolSmartLearningServiceConfig(
    request: OnlineSchoolSmartLearningServiceConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOnlineSchoolSmartLearningServiceConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验学习方案列表是否存在自动学习
   * @param request:
   * @return {@link Boolean}
   * <AUTHOR> By Cb
   * @since 2024/5/9 9:31
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validLearningSchemeExistAutoLearning(
    request: ValidLearningSchemeExistAutoLearningRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validLearningSchemeExistAutoLearning,
    operation?: string
  ): Promise<Response<ValidLearningSchemeExistAutoLearningResponse>> {
    return commonRequestApi<ValidLearningSchemeExistAutoLearningResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
