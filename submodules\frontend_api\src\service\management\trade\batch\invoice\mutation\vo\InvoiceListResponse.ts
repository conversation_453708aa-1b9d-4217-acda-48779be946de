/*
 * @Description: 列表转换数据
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-14 17:52:50
 */

import {
  OnlineInvoiceAssociationInfo,
  InvoiceFaceInfoResponse,
  OnlineInvoiceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { TitleTypeEnum, InvoiceStatusEnum, InvoiceCategoryEnum } from '../../enum/InvoiceEnum'
import InvoiceDetail from '../dto/InvoiceDetail'
export default class InvoiceListResponse {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 发票类型 PLAININVOICE:普通发票 VATPLAININVOICE:增值税普通发票 VATSPECIALPLAININVOICE:增值税专用发票
   */
  invoiceCategory?: InvoiceCategoryEnum
  /**
   * NOTE：退款状态字段 后端待定
   */
  /**
   * 付款金额
   */
  payAmount?: number
  /**
   * 蓝票开票金额
   */
  blueTotalAmount?: number
  /**
   * 红票开票金额
   */
  redTotalAmount?: number
  /**
   * 税额
   */
  totalTax?: number
  /**
   * 姓名ID
   */
  userId?: string
  /**
   * 姓名
   */
  name?: string
  /**
   * 身份证
   */
  idCard?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 发票抬头类型  1：个人  2：企业
   */
  titleType?: TitleTypeEnum
  /**
   * 发票抬头
   */
  title?: string
  /**
   * 统一社会信用代码
   */
  taxpayerNo?: string
  /**
   * 申请开票时间
   */
  applyForDate?: string
  /**
   * 发票状态 0:未开具 1：开票中 2：开票成功 3：开票失败
   */
  invoiceStatus?: InvoiceStatusEnum
  /**
   * 开票时间
   */
  invoiceDate?: string
  /**
   * 蓝票发票号
   */
  blueInvoiceNo?: string
  /**
   * 红票发票号
   */
  redInvoiceNo?: string
  /**
   * 红票开据状态 0:未开具 1：开票中 2：开票成功 3：开票失败
   */
  redStatus?: InvoiceStatusEnum
  /**
   * 发票冻结状态
   */
  invoiceFreezeStatus?: boolean
  /**
   * 发票票面信息
   */
  invoiceFaceInfo?: InvoiceFaceInfoResponse
  /**
   * 购买方地址
   */
  address?: string
  /**
   * 购买方开户行名称
   */
  bankName?: string
  /**
   * 购买方注册电话
   */
  rePhone?: string
  /**
   * 购买方银行账户
   */
  account?: string
  /**
   * 购买方营业执照url
   */
  businessLicenseUrl?: string
  /**
   * 购买方许可证url
   */
  permitUrl?: string
  /**
   * 发票票面备注
   */
  remark?: string
  /**
   * 蓝票下载地址
   */
  blueFilePath?: string
  /**
   * 红票下载地址
   */
  redFilePath?: string

  static from(OnlineInvoiceResponse: OnlineInvoiceResponse) {
    const invoiceListResponse = new InvoiceListResponse()
    invoiceListResponse.orderNo = OnlineInvoiceResponse.associationInfoList[0].associationId
    invoiceListResponse.payAmount = OnlineInvoiceResponse.associationInfoList[0].payAmount
    invoiceListResponse.blueTotalAmount = OnlineInvoiceResponse.blueInvoiceItem.totalAmount
    invoiceListResponse.redTotalAmount = OnlineInvoiceResponse.redInvoiceItem.totalAmount
    invoiceListResponse.totalTax = OnlineInvoiceResponse.blueInvoiceItem.totalTax
    invoiceListResponse.userId = OnlineInvoiceResponse.associationInfoList[0].buyer.userId
    invoiceListResponse.titleType = OnlineInvoiceResponse.basicData.invoiceFaceInfo.titleType
    invoiceListResponse.title = OnlineInvoiceResponse.basicData.invoiceFaceInfo.title
    invoiceListResponse.taxpayerNo = OnlineInvoiceResponse.basicData.invoiceFaceInfo.taxpayerNo
    invoiceListResponse.applyForDate = OnlineInvoiceResponse.blueInvoiceItem.billStatusChangeTime.billing
    invoiceListResponse.invoiceStatus = OnlineInvoiceResponse.blueInvoiceItem.billStatus
    invoiceListResponse.invoiceDate = OnlineInvoiceResponse.blueInvoiceItem.billStatusChangeTime.success
    invoiceListResponse.blueInvoiceNo = OnlineInvoiceResponse.blueInvoiceItem.billNo
    invoiceListResponse.redInvoiceNo = OnlineInvoiceResponse.redInvoiceItem.billNo
    invoiceListResponse.invoiceFreezeStatus = OnlineInvoiceResponse.basicData.freeze
    invoiceListResponse.invoiceFaceInfo = OnlineInvoiceResponse.basicData.invoiceFaceInfo
    invoiceListResponse.redStatus = OnlineInvoiceResponse.basicData.redInvoiceItemBillStatus
    invoiceListResponse.blueFilePath = OnlineInvoiceResponse.blueInvoiceItem.filePath
    invoiceListResponse.redFilePath = OnlineInvoiceResponse.redInvoiceItem.filePath
    invoiceListResponse.invoiceCategory = OnlineInvoiceResponse.basicData.invoiceCategory
    invoiceListResponse.address = OnlineInvoiceResponse.basicData.invoiceFaceInfo.address
    invoiceListResponse.bankName = OnlineInvoiceResponse.basicData.invoiceFaceInfo.bankName
    invoiceListResponse.account = OnlineInvoiceResponse.basicData.invoiceFaceInfo.account
    invoiceListResponse.businessLicenseUrl = OnlineInvoiceResponse.basicData.invoiceFaceInfo.businessLicensePath
    invoiceListResponse.permitUrl = OnlineInvoiceResponse.basicData.invoiceFaceInfo.accountOpeningLicensePath
    invoiceListResponse.remark = OnlineInvoiceResponse.basicData.invoiceFaceInfo.remark
    invoiceListResponse.rePhone = OnlineInvoiceResponse.basicData.invoiceFaceInfo.phone
    return invoiceListResponse
  }
  setUserInfo(name: string, idCard: string, phone: string, email: string) {
    this.name = name
    this.idCard = idCard
    this.phone = phone
    this.email = email
  }
}
