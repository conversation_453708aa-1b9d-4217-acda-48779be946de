<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 09:08:37
 * @LastEditors: chenweinian
 * @LastEditTime: 2023-07-27 09:02:03
 * @Description: 
-->
<template>
  <div>
    <!--表格-->
    <el-table stripe :data="tableData" max-height="500px" class="m-table">
      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
      <el-table-column label="基准照片更新次数" min-width="150">
        <template slot-scope="scope"
          ><div>{{ scope.row.baseConfig.datumConfig.updateCount }}</div></template
        >
      </el-table-column>
      <el-table-column label="匹配相似度" min-width="100">
        <template slot-scope="scope"
          ><div>{{ scope.row.baseConfig.datumConfig.comparePhoto }}%</div></template
        >
      </el-table-column>
      <!-- <el-table-column label="登录监管配置" min-width="200">
        <template>人脸登录比对：每次登录拍摄对比</template>
      </el-table-column> -->
      <el-table-column label="比对过程是否开启活体检测" min-width="180">
        <template slot-scope="scope">
          <el-badge v-if="scope.row.baseConfig.datumConfig.liveDetection" type="success" is-dot class="badge-status"
            >开启</el-badge
          >
          <el-badge v-else type="warning" is-dot class="badge-status">关闭</el-badge>
        </template>
      </el-table-column>
      <el-table-column label="应用状态" width="100" align="center" fixed="right">
        <template slot-scope="scope">
          <el-badge v-if="scope.row.baseConfig.datumConfig.enable" type="success" is-dot class="badge-status"
            >开启</el-badge
          >
          <el-badge v-else type="warning" is-dot class="badge-status">关闭</el-badge>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import BaseConfig from '@hbfe-biz/biz-anticheat/dist/config/BaseConfig'
  @Component({})
  export default class extends Vue {
    // 基础配置
    @Prop({
      type: Object,
      default: () => new BaseConfig()
    })
    baseConfigModule: BaseConfig
    tableData: Array<BaseConfig> = new Array<BaseConfig>()

    @Watch('baseConfigModule', { deep: true })
    watch() {
      this.tableData = new Array<BaseConfig>()
      this.tableData.push(this.baseConfigModule)
    }
  }
</script>
