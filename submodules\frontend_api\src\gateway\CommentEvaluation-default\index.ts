import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/CommentEvaluation-default'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'CommentEvaluation-default'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 取消屏蔽用户对课程的评价
<AUTHOR>
@since 2021/4/22
 */
export class CancelShieldUserCourseAppraisalRequest {
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 评价人编号
   */
  userId: string
}

/**
 * 评论创建信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class CommentCreateRequest {
  /**
   * 业务实体编号
   */
  entityId: string
  /**
   * 业务实体名称
   */
  entityName?: string
  /**
   * 评论用户
   */
  usrId?: string
  /**
   * 评论标题
   */
  title?: string
  /**
   * 评论内容
   */
  content?: string
  /**
   * 评论特征标记列表
   */
  markers?: Array<MarkerRequest1>
}

/**
 * 新增课程评价信息
 */
export class CourseAppraisalCreateRequest {
  /**
   * 课程ID
   */
  courseId?: string
  /**
   * 课程名称
   */
  courseName?: string
  /**
   * 教学水平评价星数，1~5星
   */
  teachingQualityStars: number
  /**
   * 内容质量评价星数，1~5星
   */
  contentQualityStars: number
  /**
   * 评价内容
   */
  contents?: string
}

/**
 * 特征标记信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class MarkerRequest1 {
  key?: string
  value?: string
}

/**
 * 新增网校评价信息
 */
export class NetSchoolAppraisalCreateRequest {
  /**
   * 网校ID
   */
  schoolId?: string
  /**
   * 网校名称
   */
  schoolName?: string
  /**
   * 服务满意度评价星数，0~10
   */
  serviceSatisfactionStars: number
  /**
   * 内容质量评价星数，0~10
   */
  contentQualityStars: number
  /**
   * 评价内容
   */
  contents?: string
}

/**
 * 评论回复信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class ReversionCreateRequest {
  /**
   * 评论编号
   */
  comId?: string
  /**
   * 回复内容
   */
  content?: string
  /**
   * 被回复用户编号
   */
  targetUserId?: string
  /**
   * 回复人编号
   */
  usrId?: string
  /**
   * 被回复的回复编号
   */
  parentId?: string
  /**
   * 特征标记列表
   */
  markers?: Array<MarkerRequest1>
}

/**
 * 回复详细信息查询条件
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class ReversionDetailQueryRequest {
  /**
   * 被回复的回复编号
   */
  parentId?: string
  /**
   * 审核状态，-1/0/1/2，全部/未审核/审核通过/审核不通过
   */
  audit: number
  /**
   * 屏蔽状态，-1/0/1，全部/未屏蔽/已屏蔽
   */
  shield: number
}

/**
 * 屏蔽用户对课程的评价
<AUTHOR>
@since 2021/4/22
 */
export class ShieldUserCourseAppraisalRequest {
  /**
   * 课程编号
   */
  courseId: string
  /**
   * 评价人编号
   */
  userId: string
}

/**
 * 特征标记信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class MarkerRequest {
  key: string
  value: string
}

/**
 * 评论信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class CommentResponse {
  /**
   * 评论编号
   */
  comId: string
  /**
   * 评论用户
   */
  usrId: string
  /**
   * 评论用户名称
   */
  userName: string
  /**
   * 评论标题
   */
  title: string
  /**
   * 评论内容
   */
  content: string
  /**
   * 审核状态，0/1/2，未审核/审核通过/审核不通过
   */
  audit: number
  /**
   * 置顶状态，0/1，不置顶/置顶
   */
  pin: number
  /**
   * 屏蔽状态，0/1，不屏蔽/屏蔽
   */
  shield: number
  /**
   * 特征标记列表
   */
  markers: Array<MarkerRequest>
}

/**
 * 课程评价记录
 */
export class CourseAppraisalRecordResponse {
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 综合水平评价星数，0~10
   */
  comprehensiveStars: number
  /**
   * 教学水平评价星数，0~10
   */
  teachingQualityStars: number
  /**
   * 内容质量评价星数，0~10
   */
  contentQualityStars: number
  /**
   * 评价内容
   */
  contents: string
  /**
   * 评价时间
   */
  createTime: string
  /**
   * 是否屏蔽
   */
  shield: boolean
}

export class CourseAppraisalResponse {
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 综合水平评价星数，0~10
   */
  comprehensiveStars: number
  /**
   * 教学水平评价星数，0~10
   */
  teachingQualityStars: number
  /**
   * 内容质量评价星数，0~10
   */
  contentQualityStars: number
}

/**
 * 课程评价信息
 */
export class CourseUserAppraisalResponse {
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 课程名称
   */
  courseName: string
  /**
   * 教学水平评价星数，0~10
   */
  teachingQualityStars: number
  /**
   * 内容质量评价星数，0~10
   */
  contentQualityStars: number
  /**
   * 评价内容
   */
  contents: string
  /**
   * 评价时间
   */
  createTime: string
}

/**
 * 网校评价记录
 */
export class NetSchoolAppraisalRecordResponse {
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 网校ID
   */
  schoolId: string
  /**
   * 综合水平评价星数，0~10
   */
  comprehensiveStars: number
  /**
   * 服务满意度评价星数，0~10
   */
  serviceSatisfactionStars: number
  /**
   * 内容质量评价星数，0~10
   */
  contentQualityStars: number
  /**
   * 评价内容
   */
  contents: string
  /**
   * 评价时间
   */
  createTime: string
}

export class NetSchoolAppraisalResponse {
  /**
   * 网校ID
   */
  schoolId: string
  /**
   * 综合水平评价星数，0~10
   */
  comprehensiveStars: number
  /**
   * 服务满意度评价星数，0~10
   */
  serviceSatisfactionStars: number
  /**
   * 内容质量评价星数，0~10
   */
  contentQualityStars: number
}

/**
 * 网校评价信息
 */
export class NetSchoolUserAppraisalResponse {
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 网校ID
   */
  schoolId: string
  /**
   * 网校名称
   */
  schoolName: string
  /**
   * 服务满意度评价星数，0~10
   */
  serviceSatisfactionStars: number
  /**
   * 内容质量评价星数，0~10
   */
  contentQualityStars: number
  /**
   * 评价内容
   */
  contents: string
  /**
   * 评价时间
   */
  createTime: string
}

/**
 * 回复详细信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class ReversionDetailResponse {
  /**
   * 回复编号
   */
  revId: string
  /**
   * 评论编号
   */
  comId: string
  /**
   * 回复内容
   */
  content: string
  /**
   * 被回复用户编号
   */
  targetUserId: string
  /**
   * 回复人编号
   */
  usrId: string
  /**
   * 被回复的回复编号
   */
  parentId: string
  /**
   * 审核状态，0/1/2，未审核/审核通过/审核不通过
   */
  audit: number
  /**
   * 置顶状态，0/1，不置顶/置顶
   */
  pin: number
  /**
   * 屏蔽状态，0/1，不屏蔽/屏蔽
   */
  shield: number
  /**
   * 特征标记列表
   */
  markers: Array<MarkerRequest>
  /**
   * 回复时间
   */
  createTime: string
  /**
   * 用户名称
   */
  userName: string
  /**
   * 被回复用户名称
   */
  targetUserName: string
}

/**
 * 回复信息
<AUTHOR>
@date 2020/4/20
@since 1.0.0
 */
export class ReversionResponse {
  /**
   * 回复编号
   */
  revId: string
  /**
   * 评论编号
   */
  comId: string
  /**
   * 回复内容
   */
  content: string
  /**
   * 被回复用户编号
   */
  targetUserId: string
  /**
   * 回复人编号
   */
  usrId: string
  /**
   * 被回复的回复编号
   */
  parentId: string
  /**
   * 审核状态，0/1/2，未审核/审核通过/审核不通过
   */
  audit: number
  /**
   * 置顶状态，0/1，不置顶/置顶
   */
  pin: number
  /**
   * 屏蔽状态，0/1，不屏蔽/屏蔽
   */
  shield: number
  /**
   * 特征标记列表
   */
  markers: Array<MarkerRequest>
  /**
   * 回复时间
   */
  createTime: string
  /**
   * 用户名称
   */
  userName: string
}

export class ReversionDetailResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ReversionDetailResponse>
}

export class CommentResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CommentResponse>
}

export class CourseAppraisalRecordResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseAppraisalRecordResponse>
}

export class NetSchoolAppraisalRecordResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<NetSchoolAppraisalRecordResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 查询指定评论下所有回复的详细信息
   * @param page  分页信息
   * @param comId 评论编号
   * @param query 查询条件，可为空
   * @return 回复分页列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getReversionDetailPage(
    params: { page?: Page; comId?: string; query?: ReversionDetailQueryRequest },
    query: DocumentNode = GraphqlImporter.getReversionDetailPage,
    operation?: string
  ): Promise<Response<ReversionDetailResponsePage>> {
    return commonRequestApi<ReversionDetailResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询指定实体编号的评论列表
   * @param page     分页信息
   * @param entityId 实体编号
   * @return 评论分页信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSimpleCommentPage(
    params: { page?: Page; entityId?: string },
    query: DocumentNode = GraphqlImporter.getSimpleCommentPage,
    operation?: string
  ): Promise<Response<CommentResponsePage>> {
    return commonRequestApi<CommentResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 加载评论信息
   * @param id 评论编号
   * @return 评论信息
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadCommentary(
    id: string,
    query: DocumentNode = GraphqlImporter.loadCommentary,
    operation?: string
  ): Promise<Response<CommentResponse>> {
    return commonRequestApi<CommentResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程评价信息列表
   * @param courseIds 课程ID集合
   * @param query 查询 graphql 语法文档
   * @param courseIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadCourseAppraisalList(
    courseIds: Array<string>,
    query: DocumentNode = GraphqlImporter.loadCourseAppraisalList,
    operation?: string
  ): Promise<Response<Array<CourseAppraisalResponse>>> {
    return commonRequestApi<Array<CourseAppraisalResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { courseIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程的评价记录信息
   * @param pageIndex 当前页码
   * @param pageSize 分页大小
   * @param courseId 课程ID
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadCourseAppraisalRecordList(
    params: { pageIndex: number; pageSize: number; courseId?: string },
    query: DocumentNode = GraphqlImporter.loadCourseAppraisalRecordList,
    operation?: string
  ): Promise<Response<CourseAppraisalRecordResponsePage>> {
    return commonRequestApi<CourseAppraisalRecordResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程的评价记录信息
   * @param pageIndex    当前页码
   * @param pageSize     分页大小
   * @param courseIdList 课程ID集合
   * @param shield       屏蔽状态，-1/0/1，全部/未屏蔽/已屏蔽，可为空，为空时默认-1
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadCourseAppraisalRecordListByIdList(
    params: { pageIndex: number; pageSize: number; courseIdList?: Array<string>; shield?: number },
    query: DocumentNode = GraphqlImporter.loadCourseAppraisalRecordListByIdList,
    operation?: string
  ): Promise<Response<CourseAppraisalRecordResponsePage>> {
    return commonRequestApi<CourseAppraisalRecordResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取课程的未屏蔽评价记录信息
   * @param pageIndex 当前页码
   * @param pageSize  分页大小
   * @param courseId  课程ID
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadCourseAppraisalUnShieldRecordList(
    params: { pageIndex: number; pageSize: number; courseId?: string },
    query: DocumentNode = GraphqlImporter.loadCourseAppraisalUnShieldRecordList,
    operation?: string
  ): Promise<Response<CourseAppraisalRecordResponsePage>> {
    return commonRequestApi<CourseAppraisalRecordResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取截止上一个月月末网校评价信息列表
   * @param schoolIds 网校ID集合
   * @param query 查询 graphql 语法文档
   * @param schoolIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadLastMonthNetSchoolAppraisalList(
    schoolIds: Array<string>,
    query: DocumentNode = GraphqlImporter.loadLastMonthNetSchoolAppraisalList,
    operation?: string
  ): Promise<Response<Array<NetSchoolAppraisalResponse>>> {
    return commonRequestApi<Array<NetSchoolAppraisalResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { schoolIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取网校评价信息列表
   * @param schoolIds 网校ID集合
   * @param query 查询 graphql 语法文档
   * @param schoolIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadNetSchoolAppraisalList(
    schoolIds: Array<string>,
    query: DocumentNode = GraphqlImporter.loadNetSchoolAppraisalList,
    operation?: string
  ): Promise<Response<Array<NetSchoolAppraisalResponse>>> {
    return commonRequestApi<Array<NetSchoolAppraisalResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { schoolIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取网校的评价记录信息
   * @param pageIndex 当前页码
   * @param pageSize  分页大小
   * @param schoolId  网校ID
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadNetSchoolAppraisalRecordList(
    params: { pageIndex: number; pageSize: number; schoolId?: string },
    query: DocumentNode = GraphqlImporter.loadNetSchoolAppraisalRecordList,
    operation?: string
  ): Promise<Response<NetSchoolAppraisalRecordResponsePage>> {
    return commonRequestApi<NetSchoolAppraisalRecordResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 加载回复信息
   * @param id 评论编号
   * @return 评论信息
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadReversion(
    id: string,
    query: DocumentNode = GraphqlImporter.loadReversion,
    operation?: string
  ): Promise<Response<ReversionResponse>> {
    return commonRequestApi<ReversionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取指定用户指定课程的星级评价
   * @param courseId 课程ID
   * @param query 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadUserCourseAppraisal(
    courseId: string,
    query: DocumentNode = GraphqlImporter.loadUserCourseAppraisal,
    operation?: string
  ): Promise<Response<CourseUserAppraisalResponse>> {
    return commonRequestApi<CourseUserAppraisalResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { courseId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取指定用户指定网校星级评价
   * @param schoolId 网校Id
   * @param query 查询 graphql 语法文档
   * @param schoolId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadUserNetSchoolAppraisal(
    schoolId: string,
    query: DocumentNode = GraphqlImporter.loadUserNetSchoolAppraisal,
    operation?: string
  ): Promise<Response<NetSchoolAppraisalResponse>> {
    return commonRequestApi<NetSchoolAppraisalResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { schoolId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建评论
   * @param commentCreateInfo 评论信息
   * @return 评论信息
   * @param mutate 查询 graphql 语法文档
   * @param commentCreateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addCommentary(
    commentCreateInfo: CommentCreateRequest,
    mutate: DocumentNode = GraphqlImporter.addCommentary,
    operation?: string
  ): Promise<Response<CommentResponse>> {
    return commonRequestApi<CommentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { commentCreateInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建回复
   * @param createDTO 回复信息
   * @return 回复信息
   * @param mutate 查询 graphql 语法文档
   * @param createDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addReversion(
    createDTO: ReversionCreateRequest,
    mutate: DocumentNode = GraphqlImporter.addReversion,
    operation?: string
  ): Promise<Response<ReversionResponse>> {
    return commonRequestApi<ReversionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 评价一个课程
   * @param createInfo 评价信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async appraise(
    createInfo: CourseAppraisalCreateRequest,
    mutate: DocumentNode = GraphqlImporter.appraise,
    operation?: string
  ): Promise<Response<CourseUserAppraisalResponse>> {
    return commonRequestApi<CourseUserAppraisalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 评价一个网校
   * @param createInfo 评价信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async appraiseNetSchool(
    createInfo: NetSchoolAppraisalCreateRequest,
    mutate: DocumentNode = GraphqlImporter.appraiseNetSchool,
    operation?: string
  ): Promise<Response<NetSchoolUserAppraisalResponse>> {
    return commonRequestApi<NetSchoolUserAppraisalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 审核评论不通过
   * @param id 评论编号
   * @return 评论信息
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async auditCommentaryFail(
    id: string,
    mutate: DocumentNode = GraphqlImporter.auditCommentaryFail,
    operation?: string
  ): Promise<Response<CommentResponse>> {
    return commonRequestApi<CommentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 审核评论通过
   * @param id 评论编号
   * @return 评论信息
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async auditCommentaryPass(
    id: string,
    mutate: DocumentNode = GraphqlImporter.auditCommentaryPass,
    operation?: string
  ): Promise<Response<CommentResponse>> {
    return commonRequestApi<CommentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 取消屏蔽用户对课程的评价
   * @param request 信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelShieldCourseUserAppraisal(
    request: CancelShieldUserCourseAppraisalRequest,
    mutate: DocumentNode = GraphqlImporter.cancelShieldCourseUserAppraisal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量删除评论，同时删除评论下的回复
   * @param comIdList 评论编号列表
   * @param mutate 查询 graphql 语法文档
   * @param comIdList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteBatchCommentary(
    comIdList: Array<string>,
    mutate: DocumentNode = GraphqlImporter.deleteBatchCommentary,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { comIdList },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除评论
   * @param id 评论编号
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteCommentary(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteCommentary,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除回复
   * @param id 回复编号
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteReversion(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteReversion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量删除回复
   * @param comId 评论编号
   * @param mutate 查询 graphql 语法文档
   * @param comId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteReversionByComId(
    comId: string,
    mutate: DocumentNode = GraphqlImporter.deleteReversionByComId,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { comId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 置顶评论
   * @param id 评论编号
   * @return 评论信息
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pinCommentary(
    id: string,
    mutate: DocumentNode = GraphqlImporter.pinCommentary,
    operation?: string
  ): Promise<Response<CommentResponse>> {
    return commonRequestApi<CommentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 置顶回复
   * @param id 回复编号
   * @return 回复信息
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pinReversion(
    id: string,
    mutate: DocumentNode = GraphqlImporter.pinReversion,
    operation?: string
  ): Promise<Response<ReversionResponse>> {
    return commonRequestApi<ReversionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 屏蔽评论
   * @param id 评论编号
   * @return 评论信息
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async shieldCommentary(
    id: string,
    mutate: DocumentNode = GraphqlImporter.shieldCommentary,
    operation?: string
  ): Promise<Response<CommentResponse>> {
    return commonRequestApi<CommentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 屏蔽用户对课程的评价
   * @param request 信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async shieldCourseUserAppraisal(
    request: ShieldUserCourseAppraisalRequest,
    mutate: DocumentNode = GraphqlImporter.shieldCourseUserAppraisal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 屏蔽回复
   * @param id 回复编号
   * @return 回复信息
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async shieldReversion(
    id: string,
    mutate: DocumentNode = GraphqlImporter.shieldReversion,
    operation?: string
  ): Promise<Response<ReversionResponse>> {
    return commonRequestApi<ReversionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 取消评论置顶
   * @param id 评论编号
   * @return 评论信息
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unPinCommentary(
    id: string,
    mutate: DocumentNode = GraphqlImporter.unPinCommentary,
    operation?: string
  ): Promise<Response<CommentResponse>> {
    return commonRequestApi<CommentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 取消回复置顶
   * @param id 回复编号
   * @return 回复信息
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unPinReversion(
    id: string,
    mutate: DocumentNode = GraphqlImporter.unPinReversion,
    operation?: string
  ): Promise<Response<ReversionResponse>> {
    return commonRequestApi<ReversionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 取消屏蔽评论
   * @param id 评论编号
   * @return 评论信息
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unshieldedCommentary(
    id: string,
    mutate: DocumentNode = GraphqlImporter.unshieldedCommentary,
    operation?: string
  ): Promise<Response<CommentResponse>> {
    return commonRequestApi<CommentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 取消屏蔽回复
   * @param id 回复编号
   * @return 回复信息
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unshieldedReversion(
    id: string,
    mutate: DocumentNode = GraphqlImporter.unshieldedReversion,
    operation?: string
  ): Promise<Response<ReversionResponse>> {
    return commonRequestApi<ReversionResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
