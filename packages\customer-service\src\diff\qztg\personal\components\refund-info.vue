<template>
  <div class="f-p15" v-if="$hasPermission('refundInfo')" desc="退款信息" actions="userIdChange">
    <el-card shadow="never" class="m-card f-mb15">
      <hb-search-wrapper @reset="reset" class="m-query is-border-bottom">
        <el-form-item label="订单号">
          <el-input v-model="orderNo" clearable placeholder="请输入订单号" />
        </el-form-item>
        <el-form-item label="退款状态">
          <el-select v-model="returnOrderRequestVo.returnStatusVo" clearable placeholder="请选择">
            <el-option
              v-for="status in ReturnStatusFilterOptions"
              :key="status"
              :label="OrderRefundStatus.map.get(status)"
              :value="status"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="退款申请时间">
          <double-date-picker
            :begin-create-time.sync="returnOrderRequestVo.basicData.returnStatusChangeTime.applied.begin"
            :end-create-time.sync="returnOrderRequestVo.basicData.returnStatusChangeTime.applied.end"
          ></double-date-picker>
        </el-form-item>
        <template slot="actions">
          <template v-if="$hasPermission('query')" desc="查询" actions="doQueryPage">
            <el-button type="primary" @click="doQueryPage">查询</el-button>
          </template>
        </template>
      </hb-search-wrapper>
      <!--表格-->
      <el-table stripe :data="returnOrderResponseVo" v-loading="query.loading" max-height="500px" class="m-table">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
          <template slot-scope="scope">
            <span :data-index="scope.$index + 1" v-observe-visibility="visibilityConfig">{{ scope.$index + 1 }}</span>
          </template>
        </el-table-column>
        <el-table-column label="订单号" min-width="240" fixed="left">
          <template slot-scope="scope">
            {{ scope.row.returnOrderNo }}
            <p v-if="scope.row.subOrderInfo.orderInfo.channelType === 1">
              <el-tag type="warning" class="f-mr10">个人报名</el-tag>
              <!-- 分销推广判断 -->
              <el-tag
                type="warning"
                class="f-mr10"
                v-if="scope.row.basicData.saleChannel == SaleChannelEnum.distribution"
                >分销推广</el-tag
              >
              <el-tag
                type="success"
                size="small"
                class="f-mr10"
                v-show="scope.row.basicData.saleChannel === SaleChannelEnum.topic"
                >专题</el-tag
              >
              <el-tag type="danger" size="mini" v-if="scope.row.thirdPartyPlatform">{{
                scope.row.thirdPartyPlatform
              }}</el-tag>
            </p>
            <p v-else>
              <el-tag class="f-mr10">{{ orderSource(scope.row.subOrderInfo.orderInfo.channelType) }}</el-tag>
              <el-tag type="warning" class="f-mr10" v-if="scope.row.saleChannel == SaleChannelEnum.distribution"
                >分销推广</el-tag
              >
              <el-tag
                type="success"
                size="small"
                class="f-mr10"
                v-show="scope.row.saleChannel === SaleChannelEnum.topic"
                >专题</el-tag
              >
              <el-tag type="danger" size="mini" v-if="scope.row.thirdPartyPlatform">{{
                scope.row.thirdPartyPlatform
              }}</el-tag>
            </p>
            <p>
              <el-tag size="small" v-if="scope.row.changeOrderStatus.includes(ChangeOrderType.CLASS_TYPE)">换班</el-tag>
            </p>
            <p>
              <el-tag
                type="warning"
                size="small"
                v-if="scope.row.changeOrderStatus.includes(ChangeOrderType.PERIOD_TYPE)"
                >换期</el-tag
              >
            </p>
          </template>
        </el-table-column>
        <el-table-column label="退款物品" min-width="240">
          <template slot-scope="{ row }">
            <p v-text="row.returnCommodity.commoditySku.saleTitle"></p>
            <p v-if="isMixedClass(row.trainingFormId)">培训期别：{{ row.trainingPeriodName || '-' }}</p>
          </template>
        </el-table-column>
        <el-table-column label="退货数量" min-width="120" prop="returnCommodity.quantity" align="center">
        </el-table-column>
        <el-table-column label="实付金额(元)" prop="subOrderInfo.amount" width="140" align="right"> </el-table-column>
        <el-table-column label="退款金额(元)" prop="basicData.refundAmount" width="140" align="right">
        </el-table-column>
        <el-table-column label="购买人信息" min-width="240">
          <template slot-scope="scope">
            <p>姓名：{{ scope.row.buyer.userName }}</p>
            <p>证件号：{{ scope.row.buyer.idCard }}</p>
            <p>手机号：{{ scope.row.buyer.phone }}</p>
          </template>
        </el-table-column>
        <el-table-column label="申请时间" min-width="180">
          <template slot-scope="scope">
            <p>申请：{{ scope.row.basicData.returnOrderStatusChangeTime.applied }}</p>
          </template>
        </el-table-column>
        <el-table-column label="退货/款类型" min-width="180">
          <template slot-scope="scope">
            {{ OrderRefundType.map.get(scope.row.basicData.returnOrderType) }}
          </template>
        </el-table-column>
        <el-table-column label="退货/款状态" min-width="130">
          <template slot-scope="scope">
            <el-badge
              is-dot
              :type="returnOrderStatusClassName(scope.row).type"
              class="badge-status"
              v-if="OrderRefundStatus.map.get(returnOrderStatusClassName(scope.row).status)"
            >
              {{ OrderRefundStatus.map.get(returnOrderStatusClassName(scope.row).status) }}
            </el-badge>
          </template>
        </el-table-column>
        <el-table-column label="退款时间" min-width="180">
          <template slot-scope="scope">{{
            scope.row.basicData.returnOrderStatusChangeTime.returnedAndRefunded
          }}</template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center" fixed="right">
          <template slot-scope="scope">
            <template
              v-if="$hasPermission('refundDetail')"
              desc="退款详情详情"
              actions="@hbfe/jxjy-admin-trade/src/refund/personal/detail.vue"
            >
              <el-button type="text" size="mini" @click="toDetail(scope.row.returnOrderNo)">详情</el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import QueryRefundList from '@api/service/diff/management/qztg/trade/order/QueryRefundList'
  import ReturnOrderResponseVo from '@api/service/diff/management/qztg/trade/order/model/ReturnOrderResponseVo'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import UserModules from '@api/service/management/user/UserModule'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'

  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import OrderRefundType from '@api/service/common/return-order/enums/OrderRefundType'
  import OrderRefundStatus, {
    ReturnStatusFilterOptions
  } from '@api/service/common/return-order/enums/OrderRefundStatus'
  import { ChangeOrderType } from '@api/service/common/trade/ChangeOrderType'
  @Component({
    components: { DoubleDatePicker }
  })
  export default class extends Vue {
    page: UiPage
    query: Query = new Query()
    params = {
      order: '',
      status: '',
      time: ''
    }
    // 学员信息
    userInfo = new UserDetailVo()
    //接口查询参数
    returnOrderRequestVo: ReturnOrderRequestVo = new ReturnOrderRequestVo()
    //查询接口请求
    queryRefundOrder: QueryRefundList = new QueryRefundList()
    // 查询模型
    returnOrderResponseVo: Array<ReturnOrderResponseVo> = new Array<ReturnOrderResponseVo>()
    //退款状态
    returnOrderStatus = ''
    //推广类型枚举
    SaleChannelEnum = SaleChannelEnum
    //订单号
    orderNo = ''
    //退货状态
    OrderRefundStatus = OrderRefundStatus
    // 退款类型
    OrderRefundType = OrderRefundType
    ReturnStatusFilterOptions = ReturnStatusFilterOptions
    ChangeOrderType = ChangeOrderType
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    // 学员id 由主文件ref传入
    userId = ''
    // @Watch('userId', {
    //   immediate: true,
    //   deep: true
    // })
    /**
     * 订单来源
     */
    get orderSource() {
      return (orderSource: number) => {
        if (orderSource !== 1)
          if (orderSource === 2) {
            return '集体报名'
          } else if (orderSource === 3) {
            return '导入开通'
          } else {
            return '集体报名个人缴费'
          }
      }
    }
    /**
     * 判断是否面授网班/面授班
     */
    get isMixedClass() {
      return (id: string) => {
        return id == TrainingModeEnum.mixed || id == TrainingModeEnum.offline
      }
    }
    /**
     * 退货状态样式
     */
    get returnOrderStatusClassName() {
      return (item: ReturnOrderResponseVo) => {
        let type = ''
        const status = OrderRefundStatus.transferDtoToCurrentEnum(
          item.basicData.returnOrderStatus,
          item.basicData.returnCloseReason.closeType
        )
        if ([1, 4, 5, 8, 9].includes(status)) {
          type = 'primary'
        } else if ([6, 10, 12].includes(status)) {
          type = 'success'
        } else if ([2, 3, 7, 11].includes(status)) {
          type = 'danger'
        } else {
          type = '' // 或者其他默认值
        }
        return { type, status }
      }
    }
    async userIdChange(val: string) {
      this.userId = val
      // 根据学员id查询用户信息
      await this.getUserInfo()
      this.returnOrderRequestVo.name = this.userInfo.userName
      this.returnOrderRequestVo.idCard = this.userInfo.idCard
      this.returnOrderRequestVo.loginAccount = this.userInfo.loginAccount
      if (this.userId) {
        await this.doQueryPage()
      } else {
        this.returnOrderResponseVo = new Array<ReturnOrderResponseVo>()
      }
    }
    // 查询用户信息
    async getUserInfo() {
      const res = await UserModules.queryUserFactory.queryStudentDetail(this.userId).queryDetail()
      if (res.status.isSuccess()) {
        this.userInfo = res.data
      } else {
        this.userInfo = new UserDetailVo()
      }
    }

    /**
     * 滑动加载 原理使用 data-set和数量进行比较
     */
    async visibilityConfig(isVisible: boolean, entry: any) {
      if (isVisible) {
        if (entry.target.dataset.index >= this.page.totalSize) {
          //   最大值时不请求
          return
        }
        if (parseInt(entry.target.dataset.index) == this.returnOrderResponseVo.length) {
          this.page.pageNo = this.page.pageNo + 1
          const list = await this.queryRefundOrder.queryRefundOrderList(this.page, this.returnOrderRequestVo)
          this.returnOrderResponseVo = this.returnOrderResponseVo.concat(list)
          //处理切换页数后行数错位问题
          ;(this.$refs['tableRef'] as any)?.doLayout()
        }
      }
    }
    async doQueryPage() {
      this.returnOrderRequestVo.basicData.returnCloseReason.closeTypeList = []
      if (this.returnOrderStatus === '0') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [0, 1]
      } else if (this.returnOrderStatus === '1') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [11]
        this.returnOrderRequestVo.basicData.returnCloseReason.closeTypeList = [3]
      } else if (this.returnOrderStatus === '2') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [2, 3, 4, 5, 6]
      } else if (this.returnOrderStatus === '3') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [8, 9, 10]
      } else if (this.returnOrderStatus === '4') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [11]
        this.returnOrderRequestVo.basicData.returnCloseReason.closeTypeList = [1, 2]
      } else if (this.returnOrderStatus === '5') {
        this.returnOrderRequestVo.basicData.returnOrderStatus = [7]
      } else {
        this.returnOrderRequestVo.basicData.returnOrderStatus = []
        // this.returnOrderRequestVo.basicData.returnCloseReason.closeTypeList = []
      }
      if (this.orderNo) {
        this.returnOrderRequestVo.returnOrderNoList = [this.orderNo]
      } else {
        this.returnOrderRequestVo.returnOrderNoList = []
      }
      if (!this.userId) {
        return
      }
      this.page.pageNo = 1
      this.query.loading = true
      try {
        if (this.returnOrderRequestVo.name || this.returnOrderRequestVo.idCard) {
          this.returnOrderResponseVo = [] as ReturnOrderResponseVo[]
          this.returnOrderRequestVo.basicData.applySourceType = 'SUB_ORDER'
          this.returnOrderResponseVo = await this.queryRefundOrder.queryRefundOrderList(
            this.page,
            this.returnOrderRequestVo
          )
        }
      } catch (e) {
        console.log(e, '加载个人报名退款订单失败')
      } finally {
        // console.log(this.returnOrderResponseVo, 'this.returnOrderResponseVo')
        this.query.loading = false
      }
    }
    async reset() {
      this.orderNo = ''
      this.returnOrderStatus = ''
      // 重置时间选择器
      this.returnOrderRequestVo.basicData.returnStatusChangeTime.applied.begin = undefined
      this.returnOrderRequestVo.basicData.returnStatusChangeTime.applied.end = undefined

      this.returnOrderRequestVo.returnStatusVo = undefined
      await this.doQueryPage()
    }

    toDetail(id: string) {
      this.$router.push('/training/trade/refund/personal/detail/' + id)
    }
  }
</script>
