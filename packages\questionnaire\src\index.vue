<template slot-scope="scope">
  <el-main>
    <div class="f-p15" v-if="$hasPermission('questionnaireQuery')" desc="问卷列表查询" actions="doQueryPage,activated">
      <div class="f-mb15">
        <template
          desc="新建调研问卷模板"
          v-if="$hasPermission('questionnaireCreate')"
          actions="@hbfe/jxjy-admin-questionnaire/src/modify.vue"
        >
          <el-button type="primary" icon="el-icon-plus" @click="toUrl('add')">新建调研问卷模板</el-button>
        </template>
      </div>

      <el-card shadow="never" class="m-card f-mb15">
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="问卷名称">
                <el-input v-model="questionnaireList.params.name" clearable placeholder="请输入问卷名称关键字" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="问卷类型">
                <el-select v-model="questionnaireList.params.type" clearable filterable placeholder="请选择问卷类型">
                  <el-option
                    v-for="item in QuestionnaireType.list()"
                    :label="item.desc"
                    :value="item.code"
                    :key="item.code"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="更新时间">
                <el-date-picker
                  v-model="questionnaireTime"
                  type="datetimerange"
                  range-separator="-"
                  :value-format="'yyyy-MM-dd HH:mm:ss'"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="问卷状态">
                <el-select v-model="questionnaireList.params.status" clearable placeholder="请选择问卷状态">
                  <el-option
                    v-for="item in QuestionnaireStatus.list()"
                    :label="item.desc"
                    :value="item.code"
                    :key="item.code"
                  ></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="方案使用情况">
                <el-select v-model="questionnaireList.params.schemeUseStatus" clearable placeholder="是否被方案引用">
                  <el-option label="是" :value="true"></el-option>
                  <el-option label="否" :value="false"></el-option
                ></el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
                <el-button @click="restValue">重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <el-table
          stripe
          :data="tableData"
          v-loading="query.loading"
          ref="elTableRef"
          max-height="500px"
          class="m-table"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="问卷名称" min-width="240" fixed="left">
            <template slot-scope="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column label="问卷类型" min-width="160">
            <template slot-scope="scope">{{ QuestionnaireType.map.get(scope.row.type) }}</template>
          </el-table-column>
          <el-table-column label="更新时间" min-width="160">
            <template slot-scope="scope">{{ scope.row.createTime }}</template>
          </el-table-column>
          <el-table-column label="状态" min-width="120">
            <template slot-scope="scope">
              <el-badge is-dot :type="QuestionnaireStatusObj(scope.row.status).type" class="badge-status">{{
                QuestionnaireStatusObj(scope.row.status).name
              }}</el-badge>
            </template>
          </el-table-column>
          <el-table-column label="是否被方案引用" min-width="160">
            <template slot-scope="scope">{{ scope.row.schemeUseStatus ? '是' : '否' }}</template>
          </el-table-column>
          <el-table-column label="操作" width="300" align="center" fixed="right">
            <template slot-scope="scope">
              <template
                v-if="$hasPermission('questionnairePreview')"
                desc="问卷预览"
                actions="@hbfe/jxjy-admin-questionnaire/src/preview.vue"
              >
                <el-button type="text" :loading="loading" @click="preview(scope.row)">预览</el-button>
              </template>
              <template
                v-if="$hasPermission('questionnaireEdit')"
                desc="问卷修改"
                actions="@hbfe/jxjy-admin-questionnaire/src/modify.vue"
              >
                <el-button type="text" :loading="loading" @click="toUrl('edit', scope.row)">修改</el-button>
              </template>
              <template v-if="$hasPermission('questionnaireCopy')" desc="问卷复制" actions="copy">
                <el-button type="text" :loading="loading" @click="copy(scope.row)">复制</el-button>
              </template>
              <template v-if="$hasPermission('questionnaireDelete')" desc="问卷删除" actions="sureDelete">
                <el-button
                  type="text"
                  :loading="loading"
                  v-if="!scope.row.schemeUseStatus"
                  @click="sureDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </template>
          </el-table-column>
        </el-table>
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import QuestionnaireList from '@api/service/management/resource/question-naire/QuestionnaireList'
  import QuestionnaireItem from '@api/service/management/resource/question-naire/models/QuestionnaireItem'
  import QuestionnaireType from '@api/service/management/resource/question-naire/enums/QuestionnaireType'
  import QuestionnaireStatus, {
    QuestionnaireStatusEnum
  } from '@api/service/management/resource/question-naire/enums/QuestionnaireStatus'
  import QuestionnaireQueryParam from '@api/service/management/resource/question-naire/models/QuestionnaireQueryParam'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import DateScope from '@api/service/common/models/DateScope'
  @Component({
    components: {
      DoubleDatePicker
    }
  })
  export default class extends Vue {
    @Ref('elTableRef') elTableRef: any
    page: UiPage
    query: Query = new Query()
    questionnaireList = new QuestionnaireList() //查询请求
    tableData: QuestionnaireItem[] = [] //返回结果
    QuestionnaireType = new QuestionnaireType() //问卷类型枚举
    QuestionnaireStatus = new QuestionnaireStatus() //状态枚举
    questionnaireTime = [] as string[] //查询时间数组
    loading = false
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    /**
     * 状态
     */
    get QuestionnaireStatusObj() {
      return (row: QuestionnaireStatusEnum) => {
        return {
          name: this.QuestionnaireStatus.map.get(row),
          type: row === 2 ? 'success' : 'info'
        }
      }
    }

    /**
     * 预览
     */
    async preview(item: QuestionnaireItem) {
      this.loading = true
      const res = await this.questionnaireList.copy(item.id)
      if (res.questionList.length === 0) {
        this.$message.error('请至少配置一题试题才可预览。')
      } else {
        window.open(`/admin#/resource/questionnaire/preview?id=${item.id}`, '_blank')
      }
      this.loading = false
    }
    /**
     * 修改
     */
    toUrl(type: string, item?: QuestionnaireItem) {
      if (item) {
        if (item.schemeUseStatus) {
          this.$router.push(`/resource/questionnaire/modify/${type}?id=${item.id}&isDiabled=${true}`)
        } else {
          this.$router.push(`/resource/questionnaire/modify/${type}?id=${item.id}`)
        }
      } else {
        this.$router.push(`/resource/questionnaire/modify/${type}`)
      }
    }
    /**
     * 复制
     */
    copy(item: QuestionnaireItem) {
      this.$confirm(`确认复制该调研问卷？`, {
        confirmButtonText: '确定复制',
        cancelButtonText: '取消',
        distinguishCancelAndClose: true, // 重要，设置为true才会把右上角X和取消区分开来
        center: false,
        type: 'warning'
      })
        .then(async () => {
          this.$router.push(`/resource/questionnaire/modify/add?id=${item.id}&isCopy=true`)
        })
        .catch(() => {
          //
        })
    }
    /**
     * 删除
     */
    sureDelete(item: QuestionnaireItem) {
      this.$confirm(`删除后该问卷需要重新创建，是否确认删除？`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        distinguishCancelAndClose: true, // 重要，设置为true才会把右上角X和取消区分开来
        center: false,
        type: 'warning'
      })
        .then(async () => {
          const res = await this.questionnaireList.delete(item.id)
          if (res.status.isSuccess) {
            this.$message.success('问卷删除成功')
            await this.doQueryPage()
          } else {
            this.$message.error(`问卷删除失败${res.status.getMessage()}`)
          }
        })
        .catch(() => {
          //
        })
    }
    /**
     * 查询
     */
    async doQueryPage() {
      try {
        this.query.loading = true
        if (Array.isArray(this.questionnaireTime) && this.questionnaireTime.length) {
          this.questionnaireList.params.createTimeScope.begin = this.questionnaireTime[0]
          this.questionnaireList.params.createTimeScope.end = this.questionnaireTime[1]
        } else {
          this.questionnaireList.params.createTimeScope = new DateScope()
        }
        await this.questionnaireList.queryList(this.page)
        this.tableData = this.questionnaireList.list
        this.$nextTick(() => {
          this.elTableRef && this.elTableRef.doLayout()
        })
      } catch (e) {
        //
      } finally {
        this.query.loading = false
      }
    }
    /**
     * 重置
     */
    async restValue() {
      this.questionnaireList.params = new QuestionnaireQueryParam()
      this.questionnaireTime = [] as string[]
      await this.doQueryPage()
    }

    async activated() {
      await this.doQueryPage()
    }
  }
</script>
