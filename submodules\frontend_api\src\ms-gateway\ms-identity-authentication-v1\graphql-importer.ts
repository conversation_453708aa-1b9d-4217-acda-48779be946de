import getAccessTokenValue from './queries/getAccessTokenValue.graphql'
import getBoundAccountsForReAuthenticate from './queries/getBoundAccountsForReAuthenticate.graphql'
import getBoundProxyBusinessDomains from './queries/getBoundProxyBusinessDomains.graphql'
import applyAuthenticateByAccountId from './mutates/applyAuthenticateByAccountId.graphql'
import applyAuthenticateByAccountPwd from './mutates/applyAuthenticateByAccountPwd.graphql'
import applyAuthenticateByAccountPwdCaptcha from './mutates/applyAuthenticateByAccountPwdCaptcha.graphql'
import applyAuthenticateByAccountPwdCaptchaForFXS from './mutates/applyAuthenticateByAccountPwdCaptchaForFXS.graphql'
import applyAuthenticateByAccountPwdCaptchaTempForFXSAdmin from './mutates/applyAuthenticateByAccountPwdCaptchaTempForFXSAdmin.graphql'
import applyAuthenticateByAccountPwdCaptchaTempForFXSPromotionPortal from './mutates/applyAuthenticateByAccountPwdCaptchaTempForFXSPromotionPortal.graphql'
import applyAuthenticateBySmsCode from './mutates/applyAuthenticateBySmsCode.graphql'
import applyAuthenticateBySmsCodeForFXS from './mutates/applyAuthenticateBySmsCodeForFXS.graphql'
import applyChangeIdentityAuthentication from './mutates/applyChangeIdentityAuthentication.graphql'
import applyProxyIdentity from './mutates/applyProxyIdentity.graphql'
import applyProxyIdentityAccessSecure from './mutates/applyProxyIdentityAccessSecure.graphql'
import applyProxyIdentityUnifiedSubject from './mutates/applyProxyIdentityUnifiedSubject.graphql'
import applyReAuthenticate from './mutates/applyReAuthenticate.graphql'
import applyReAuthenticateBasic from './mutates/applyReAuthenticateBasic.graphql'
import applyThirdPartyIdentity from './mutates/applyThirdPartyIdentity.graphql'

export {
  getAccessTokenValue,
  getBoundAccountsForReAuthenticate,
  getBoundProxyBusinessDomains,
  applyAuthenticateByAccountId,
  applyAuthenticateByAccountPwd,
  applyAuthenticateByAccountPwdCaptcha,
  applyAuthenticateByAccountPwdCaptchaForFXS,
  applyAuthenticateByAccountPwdCaptchaTempForFXSAdmin,
  applyAuthenticateByAccountPwdCaptchaTempForFXSPromotionPortal,
  applyAuthenticateBySmsCode,
  applyAuthenticateBySmsCodeForFXS,
  applyChangeIdentityAuthentication,
  applyProxyIdentity,
  applyProxyIdentityAccessSecure,
  applyProxyIdentityUnifiedSubject,
  applyReAuthenticate,
  applyReAuthenticateBasic,
  applyThirdPartyIdentity
}
