import { BatchOrderInvoiceTypeEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderInvoiceType'
import { InvoiceTitleTypeEnum } from '@api/service/common/enums/trade-configuration/InvoiceTitleType'
import { BatchOrderInvoiceStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderInvoiceStatus'
import OffLinePageInvoiceResponseVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'
import InvoiceListResponse from '@api/service/management/trade/batch/invoice/query/vo/InvoiceListResponse'
import { InvoiceCategoryEnum, TitleTypeEnum } from '@api/service/management/trade/batch/invoice/enum/InvoiceEnum'
import BatchOrderUtils from '@api/service/management/trade/batch/order/query/utils/BatchOrderUtils'
import BatchOrderDetailDeliveryInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailDeliveryInfoVo'

/**
 * @description 【集体报名订单】订单详情-发票信息
 */
class BatchOrderDetailInvoiceInfoVo {
  /**
   * 发票类型
   */
  invoiceType: BatchOrderInvoiceTypeEnum = null

  /**
   * 发票状态
   */
  invoiceStatus: BatchOrderInvoiceStatusEnum = null

  /**
   * 申请开票时间
   */
  applyInvoiceDate = ''

  /**
   * 发票抬头类型
   */
  invoiceTitleType: InvoiceTitleTypeEnum = null

  /**
   * 发票抬头
   */
  invoiceTitle = ''

  /**
   * 统一社会信用代码
   */
  taxpayerNo = ''

  /**
   * 开户银行
   */
  bankName = ''

  /**
   * 开户账号
   */
  accountNo = ''

  /**
   * 注册电话
   */
  registerPhone = ''

  /**
   * 注册地址
   */
  registerAddress = ''

  /**
   * 配送信息
   */
  deliveryInfo: BatchOrderDetailDeliveryInfoVo = new BatchOrderDetailDeliveryInfoVo()

  /**
   * 发票备注
   */
  remark = ''

  /**
   * 【线上】批次单发票信息
   */
  static fromOnline(response: InvoiceListResponse): BatchOrderDetailInvoiceInfoVo {
    const detail = new BatchOrderDetailInvoiceInfoVo()
    const invoiceCategory = response.invoiceCategory
    if (invoiceCategory === InvoiceCategoryEnum.VATPLAININVOICE) {
      detail.invoiceType = BatchOrderInvoiceTypeEnum.VAT_ELECTRONIC_INVOICE
    }
    if (invoiceCategory === InvoiceCategoryEnum.VATSPECIALPLAININVOICE) {
      detail.invoiceType = BatchOrderInvoiceTypeEnum.VAT_SPECIAL_INVOICE
    }

    detail.invoiceStatus = BatchOrderUtils.getInvoiceStatus(response)
    detail.applyInvoiceDate = response.applyForDate ?? ''
    detail.invoiceTitleType =
      response.titleType === TitleTypeEnum.PERSONAL
        ? InvoiceTitleTypeEnum.INDIVIDUAL
        : response.titleType === TitleTypeEnum.UNIT
        ? InvoiceTitleTypeEnum.UNIT
        : null
    detail.invoiceTitle = response.title ?? ''
    detail.taxpayerNo = response.taxpayerNo ?? ''
    detail.bankName = response.bankName ?? ''
    detail.accountNo = response.account ?? ''
    detail.registerPhone = response.rePhone ?? ''
    detail.registerAddress = response.address ?? ''
    detail.remark = response.remark ?? ''
    return detail
  }

  /**
   * 【线下】批次单发票信息
   */
  static fromOffLine(response: OffLinePageInvoiceResponseVo): BatchOrderDetailInvoiceInfoVo {
    const detail = new BatchOrderDetailInvoiceInfoVo()
    const invoiceCategory = response.invoiceCategory
    if (invoiceCategory === InvoiceCategoryEnum.VATPLAININVOICE) {
      detail.invoiceType = BatchOrderInvoiceTypeEnum.VAT_OFFLINE_INVOICE
    }
    if (invoiceCategory === InvoiceCategoryEnum.VATSPECIALPLAININVOICE) {
      detail.invoiceType = BatchOrderInvoiceTypeEnum.VAT_SPECIAL_INVOICE
    }
    detail.invoiceStatus = BatchOrderUtils.getInvoiceStatus(response)
    detail.applyInvoiceDate = response.applyForDate ?? ''
    detail.invoiceTitleType =
      response.titleType === TitleTypeEnum.PERSONAL
        ? InvoiceTitleTypeEnum.INDIVIDUAL
        : response.titleType === TitleTypeEnum.UNIT
        ? InvoiceTitleTypeEnum.UNIT
        : null
    detail.invoiceTitle = response.title ?? ''
    detail.taxpayerNo = response.taxpayerNo ?? ''
    detail.bankName = response.bankName ?? ''
    detail.accountNo = response.account ?? ''
    detail.registerPhone = response.rePhone ?? ''
    detail.registerAddress = response.address ?? ''
    detail.remark = response.remark ?? ''
    detail.deliveryInfo = BatchOrderUtils.getDeliveryInfo(response.deliveryInfo)

    return detail
  }
}

export default BatchOrderDetailInvoiceInfoVo
