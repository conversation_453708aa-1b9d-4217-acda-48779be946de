import { CourseLoadModeEnum } from '@api/service/common/scheme/enum/CourseLoadMode'
import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
import CompulsoryCourseInfo from '@api/service/common/scheme/model/CompulsoryCourseInfo'
import ClassificationAssess from '@api/service/common/scheme/model/ClassificationAssess'

/**
 * @description 分类详情
 */
class Classification {
  /**
   * 课程加载模式（仅修改时有效）
   */
  courseLoadMode = CourseLoadModeEnum.by_course_package
  /**
   * 默认id前缀
   */
  static classificationIdPre = 'classificationIdPre'
  /**
   * 是否处在编辑状态
   * @description 为了ui界面方便根据状态展示不同ui
   */
  isEditing = false
  /**
   * 课程数组
   * @description 暂不限制类型
   */
  courseList: any[] = []
  /**
   * 父id
   */
  parentId = ''
  /**
   * 课程总数
   */
  courseTotal = 0
  /**
   * 课程学时总数
   */
  coursePeriodTotal = 0
  /**
   * id
   */
  id = ''
  /**
   * 课程学习大纲名称
   */
  name = ''
  /**
   * 课程包id
   */
  coursePackageId = ''
  /**
   * 当前课程学习大纲下的必学课程列表
   * @description 仅当没有childOutlines时生效
   */
  compulsoryCourseIdList: string[] = []
  /**
   * 必学课程学时总数
   */
  compulsoryCoursePeriodTotal = 0
  /**
   * 必学课程总数-UI
   */
  compulsoryCourseTotal = 0
  /**
   * 课程学习大纲序号，同级序号
   */
  sort = 0
  /**
   * 大纲类型，1-必修，2-选修 仅限选课规则时候选用
   */
  category = 0
  /**
   * 分类考核对象
   */
  assessSetting = new ClassificationAssess()
  /**
   * 子节点
   */
  childOutlines: Classification[] = []
  /**
   * 操作类型
   */
  operation: OperationTypeEnum = undefined
  /**
   * 必学课程信息数组，自主选课用
   */
  compulsoryCourseInfoList: CompulsoryCourseInfo[] = []
  /**
   * id,自主选课复制用
   */
  idCopy = ''

  /**
   * id，自主选课复制用
   */
  constructor(id = '') {
    this.id = id
  }
}

export default Classification
