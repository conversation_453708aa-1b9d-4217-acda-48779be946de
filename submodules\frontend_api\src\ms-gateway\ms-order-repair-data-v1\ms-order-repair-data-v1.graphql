"""独立部署的微服务,K8S服务名:ms-order-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	batchOrderMigration(request:DistributionBatchOrderMigratedRequest):MigrationResponse
	orderMigration(request:DistributionOrderMigrationRequest):MigrationResponse
}
"""订单数据迁移"""
input OrderMigrationDto @type(value:"com.fjhb.ms.order.v1.kernel.repairdata.gateway.graphql.dto.OrderMigrationDto") {
	"""订单号"""
	orderNo:String
	"""旧的定价方案ID"""
	oldPricingSchemeId:String
	"""定价策略ID"""
	pricingPolicyId:String
	"""旧的优惠申请id"""
	oldDiscountApplyId:String
	"""优惠策略id"""
	discountPolicyId:String
}
"""批次提交命令
	<AUTHOR>
	@since 2021/10/9
"""
input DistributionBatchOrderMigratedRequest @type(value:"com.fjhb.ms.order.v1.kernel.repairdata.gateway.graphql.request.DistributionBatchOrderMigratedRequest") {
	"""批次单号集合"""
	batchOrderNoList:[String]
	"""分销商ID"""
	distributionId:String
}
"""订单数据迁移"""
input DistributionOrderMigrationRequest @type(value:"com.fjhb.ms.order.v1.kernel.repairdata.gateway.graphql.request.DistributionOrderMigrationRequest") {
	orderMigrationDtoList:[OrderMigrationDto]
	"""分销商ID"""
	distributionId:String
}
"""@Description
	<AUTHOR>
	@Date 2024/9/26 11:26
"""
type MigrationResponse @type(value:"com.fjhb.ms.order.v1.kernel.repairdata.gateway.graphql.response.MigrationResponse") {
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}

scalar List
