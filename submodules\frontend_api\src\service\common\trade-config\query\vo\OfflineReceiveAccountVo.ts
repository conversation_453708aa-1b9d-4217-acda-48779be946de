import {
  OfflineEncryptionKeyDataResponse,
  ReceiveAccountConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { AccountTypeEunm } from '@api/service/common/enums/trade-configuration/AccountType'

class OfflineReceiveAccountVo {
  id = ''
  /**
   * 支付账号类型id
   * 培训券，对接众智汇云培训券:TRAINING_VOUCHER
   * 支付宝:ALIPAY
   * 微信：WXPAY
   * 导入开通：NO_PAYMENT
   */
  paymentChannelId = ''
  /**
   * 支付方式 1-线上 2-线下
   */
  accountType: AccountTypeEunm = AccountTypeEunm.ONLINE
  /**
   * 收款账号(开户号、商户号、支付宝账号)
   */
  accountNo = ''
  /**
   * 收款账号别名
   */
  accountName = ''
  /**
   * 纳税人识别号
   */
  taxPayerId = ''
  /**
   * 退款方式  1-线上 2-线下
   */
  refundWay = -1
  /**
   * 开户银行
   */
  depositBank = ''
  /**
   * 开户户名
   */
  merchantName = ''
  /**
   * 柜台号
   */
  counterNumber = ''

  static from(res: ReceiveAccountConfigResponse) {
    const offlineData = new OfflineReceiveAccountVo()
    offlineData.id = res.id
    offlineData.accountName = res.name
    offlineData.accountNo = res.accountNo
    offlineData.accountType = res.accountType
    // this.merchantName = res.merchantName
    offlineData.refundWay = 1
    offlineData.taxPayerId = res.taxPayerId
    offlineData.paymentChannelId = res.paymentChannelId
    if (res.encryptionKeyData.encryptionKeyType === 'offlinePay') {
      const temp = res.encryptionKeyData as OfflineEncryptionKeyDataResponse
      offlineData.depositBank = temp.depositBank
      offlineData.counterNumber = temp.counterNumber
      offlineData.merchantName = temp.merchantName
    }
    return offlineData
  }
}
export default OfflineReceiveAccountVo
