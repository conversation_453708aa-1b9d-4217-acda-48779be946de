<route-meta>
  {
  "isMenu": true,
  "title": "个人报名发票",
  "sort": 1
  }
</route-meta>
<script lang="ts">
  import InvoicePersonalIndex from '@hbfe/jxjy-admin-trade/src/diff/xmlg/invoice/personal/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS, ZTGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    personalBill: [WXGLY, FXS, GYS],
    autoBill: [WXGLY, FXS, GYS],
    autoInvoice: [WXGLY, FXS, GYS],
    autoInvoiceExport: [WXGLY, FXS, GYS],
    autoInvoiceBatch: [WXGLY, FXS, GYS],
    autoInvoiceTime: [WXGLY, FXS, GYS],
    editBill: [WXGLY, FXS, GYS],
    reOpenInvoice: [WXGLY, FXS, GYS],
    reBatchOpenInvoice: [WXGLY, FXS, GYS],
    operationRecord: [WXGLY, FXS, GYS, ZTGLY],
    redInvoice: [WXGLY, FXS, GYS],
    invoiceOffline: [WXGLY, FXS, GYS],
    specialInvoice: [WXGLY, FXS, GYS],
    invoiceDistribution: [WXGLY, FXS, GYS],
    electronicSpecialInvoice: [WXGLY, FXS, GYS],
    personalBillZt: [ZTGLY],
    autoBillZt: [ZTGLY],
    autoInvoiceZt: [ZTGLY],
    autoInvoiceExportZt: [ZTGLY],
    autoInvoiceBatchZt: [ZTGLY],
    autoInvoiceTimeZt: [ZTGLY],
    redInvoiceZt: [ZTGLY],
    invoiceOfflineZt: [ZTGLY],
    specialInvoiceZt: [ZTGLY],
    invoiceDistributionZt: [ZTGLY],
    electronicSpecialInvoiceZt: [ZTGLY]
  })
  export default class extends InvoicePersonalIndex {}
</script>
