import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-order-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-order-repair-data-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 订单数据迁移
 */
export class OrderMigrationDto {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 旧的定价方案ID
   */
  oldPricingSchemeId?: string
  /**
   * 定价策略ID
   */
  pricingPolicyId?: string
  /**
   * 旧的优惠申请id
   */
  oldDiscountApplyId?: string
  /**
   * 优惠策略id
   */
  discountPolicyId?: string
}

/**
 * 批次提交命令
<AUTHOR>
@since 2021/10/9
 */
export class DistributionBatchOrderMigratedRequest {
  /**
   * 批次单号集合
   */
  batchOrderNoList?: Array<string>
  /**
   * 分销商ID
   */
  distributionId?: string
}

/**
 * 订单数据迁移
 */
export class DistributionOrderMigrationRequest {
  orderMigrationDtoList?: Array<OrderMigrationDto>
  /**
   * 分销商ID
   */
  distributionId?: string
}

/**
 * @Description
<AUTHOR>
@Date 2024/9/26 11:26
 */
export class MigrationResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchOrderMigration(
    request: DistributionBatchOrderMigratedRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchOrderMigration,
    operation?: string
  ): Promise<Response<MigrationResponse>> {
    return commonRequestApi<MigrationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async orderMigration(
    request: DistributionOrderMigrationRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.orderMigration,
    operation?: string
  ): Promise<Response<MigrationResponse>> {
    return commonRequestApi<MigrationResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
