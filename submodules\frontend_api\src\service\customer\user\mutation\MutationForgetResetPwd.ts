import { ResponseStatus } from '@hbfe/common'
import ForgetResetPasswordVo from './vo/password/ForgetResetPasswordVo'
import MsAccountGateway from '@api/ms-gateway/ms-account-v1'

/**
 * 忘记密码-重置密码
 */
class MutationForgetResetPwd {
  resetPasswordParams = new ForgetResetPasswordVo()

  /**
   * 重置密码
   */
  async doResetPwd(): Promise<ResponseStatus> {
    const { status } = await MsAccountGateway.immediateResetPassword(this.resetPasswordParams)
    return status
  }
}
export default MutationForgetResetPwd
