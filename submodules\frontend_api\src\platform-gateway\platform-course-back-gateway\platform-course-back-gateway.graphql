schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取当前服务商下课程分页  并判断是否被当前专题引用
		@param page    分页对象
		@param request 查询参数对象
		@return
	"""
	pageCourseInTrainingChannelInServicer(page:Page,request:CourseRequest,sort:[CourseSortRequest]):CourseResponsePage @page(for:"CourseResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""课程查询条件"""
input CourseRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.CourseRequest") {
	"""分类ID集合"""
	categoryIdList:[String]
	"""课程名称"""
	name:String
	"""专题id"""
	trainingChannelId:String
}
"""@description: 课程排序请求参数
	@author: sugs
	@create: 2022-03-14 09:30
"""
input CourseSortRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.sort.CourseSortRequest") {
	"""课程排序枚举"""
	courseSort:CourseSortEnum
	"""排序方式 1降序 0升序"""
	sortType:Int
}
"""@description:课程排序枚举
	@author: sugs
	@create: 2022-03-14 09:28
"""
enum CourseSortEnum @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.consts.CourseSortEnum") {
	"""创建时间"""
	CREATE_TIME
}
"""课程"""
type CourseResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.CourseResponse") {
	"""课程ID"""
	id:String
	"""课程名称"""
	name:String
	"""学时"""
	period:Double!
	"""是否被专题引用"""
	isReferencedByTrainingChannel:Boolean
}

scalar List
type CourseResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseResponse]}
