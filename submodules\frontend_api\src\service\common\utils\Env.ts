/**
 * 代理环境枚举
 * dev 开发环境
 * test1 测试1环境
 * test2 测试2环境
 * prod 生产环境
 */
export enum ProxyEnvEnum {
  dev = 'dev',
  test1 = 'test1',
  test2 = 'test2',
  prod = 'production'
}
/**
 * 环境相关
 */
class Env {
  /**
   * 代理环境字符串映射
   * @private
   * @type {Map<ProxyEnvEnum, string>}
   */
  private _proxyEnvStrMap = new Map<ProxyEnvEnum, string>()
    .set(ProxyEnvEnum.dev, '.dev.')
    .set(ProxyEnvEnum.test1, '.test1.')
    .set(ProxyEnvEnum.test2, '.test2.')
    .set(ProxyEnvEnum.prod, '.')

  /**
   * 代理环境数组
   * 索引映射
   * [开发， 测试1， 测试2， 生产]
   */
  private _proxyEnvArr: ProxyEnvEnum[] = Array.from(this._proxyEnvStrMap.keys())
  /**
   * 当前代理环境
   * @description 区分代理环境(开发环境、测试1环境、测试2环境、生产环境)
   */
  get curProxyEnv(): ProxyEnvEnum {
    let env: ProxyEnvEnum
    const specialHost = 'www.fjhb.cn'
    const curHost = location.host
    if (curHost === specialHost) {
      const specialPathName = ['/jxjy-dev/', '/jxjy-test1/', '/jxjy-test2/']
      const curPathName = location.pathname
      const findIndex = specialPathName.findIndex((pathName: string) => {
        return curPathName.startsWith(pathName)
      })
      if (findIndex > -1) {
        env = this._proxyEnvArr[findIndex]
      }
    } else {
      this._proxyEnvStrMap.forEach((value, key) => {
        if (new RegExp(value).test(location.hostname) && key !== ProxyEnvEnum.prod) {
          env = key
        }
      })
    }
    return env ?? ProxyEnvEnum.prod
  }

  /**
   * 是否是代理-开发环境
   * @returns {boolean}
   */
  get isProxyDevEnv(): boolean {
    return this._checkProxyEnv(ProxyEnvEnum.dev)
  }

  /**
   * 是否是代理-测试1环境
   * @returns {boolean}
   */
  get isProxyTest1Env(): boolean {
    return this._checkProxyEnv(ProxyEnvEnum.test1)
  }

  /**
   * 是否是代理测试2环境
   * @returns {boolean}
   */
  get isProxyTest2Env(): boolean {
    return this._checkProxyEnv(ProxyEnvEnum.test2)
  }

  /**
   * 是否是代理-内网环境
   * @returns {boolean}
   */
  get isProxyInnerNetworkEnv(): boolean {
    return this.isProxyDevEnv || this.isProxyTest1Env || this.isProxyTest2Env
  }

  /**
   * 是否是代理-生产环境
   * @returns {boolean}
   */
  get isProxyProdEnv(): boolean {
    return this._checkProxyEnv(ProxyEnvEnum.prod)
  }

  /**
   * 获取端口号
   */
  get proxyPort(): number {
    let port: number
    if (this.isProxyInnerNetworkEnv) {
      port = 9443
    }
    return port
  }

  /**
   * 获取端口号(字符串类型)
   */
  get proxyPortStr(): string {
    return this.proxyPort ? `:${this.proxyPort}` : ''
  }

  /**
   * 获取环境字符串
   * @return {string} 环境字符串
   */
  get proxyEnvStr(): string {
    return this._proxyEnvStrMap.get(this.curProxyEnv)
  }

  /**
   * 统一代理环境判断出口
   * @private
   * @param {ProxyEnvEnum} env 环境
   * @returns {boolean}
   */
  private _checkProxyEnv(env: ProxyEnvEnum): boolean {
    return this.curProxyEnv === env
  }
}

export default new Env()
