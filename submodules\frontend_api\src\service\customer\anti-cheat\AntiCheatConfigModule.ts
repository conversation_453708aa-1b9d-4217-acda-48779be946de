import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import LearningScenesAntiConfig from '@api/service/common/models/anticheat/LearningScenesAntiConfig'
import { ResponseStatus } from '@api/Response'
import AntiCheatGateway, {
  ApplyCoursewareRandomPointRequest,
  CoursewareRandomPointResponse,
  FaceSupervisionSettingResponse
} from '@api/gateway/AntiCheat-default'
import moment from 'moment'
import { Constants } from '@api/service/common/models/common/Constants'
import ProcessBehavior from '@api/service/common/models/anticheat/ProcessBehavior'
import FaceRecognitionShape from '@api/service/common/models/anticheat/FaceRecognitionShape'
import LoginScenesAntiConfig from '@api/service/common/models/anticheat/LoginScenesAntiConfig'
import ExamScenesAntiConfig from '@api/service/common/models/anticheat/ExamScenesAntiConfig'
import { Role, RoleType } from '@api/Secure'

/**
 * 本地状态数据
 */
interface AntiCheatConfigState {
  /**
   * 登录防作弊配置
   */
  loginScenesAntiConfigList: Array<LoginConfigCache>
  /**
   * 学习防作弊配置
   */
  learningScenesAntiConfigList: Array<LearningConfigCache>
  /**
   * 考试防作弊配置
   */
  examScenesAntiConfigList: Array<ExamConfigCache>
  /**
   * 最后一次加载的登录防作弊配置
   */
  lastedLoginScenesAntiConfig: LoginConfigCache | undefined
  /**
   * 最后一次加载的学习防作弊配置
   */
  lastedLearningScenesAntiConfig: LearningConfigCache | undefined
  /**
   * 最后一次加载的考试防作弊配置
   */
  lastedExamScenesAntiConfig: ExamConfigCache | undefined
  /**
   * 课件防作弊随机拍摄点信息
   */
  coursewareRandomPointResponse: CoursewareRandomPointResponse
}

const utils = {
  generateLearningConfigKey(param: LearningConfigParameter) {
    return `${param.organizationId}${param.unitId}${param.schemeId}${param.courseId}`
  },
  generateLoginConfigKey(param: LoginConfigParameter) {
    return `${param.organizationId}${param.unitId}`
  },
  generateExamConfigKey(param: ExamConfigParameter) {
    return `${param.organizationId}${param.unitId}${param.schemeId}${param.examRoundId}`
  },
  exists(list: Array<ICache>, param: ICache) {
    return !!list.find(x => x.key === param.key)
  },
  setValue(list: Array<ICache>, value: ICache) {
    if (utils.exists(list, value)) {
      const item = list.find(x => x.key === value.key)
      Object.assign(item, value)
    } else {
      list.push(value)
    }
  }
}

export class LoginConfigParameter {
  /**
   * 应用组织机构编号
   */
  organizationId = '-1'
  /**
   * 应用单位编号
   */
  unitId = '-1'
}

export class LearningConfigParameter {
  /**
   * 应用组织机构编号
   */
  organizationId = '-1'
  /**
   * 应用单位编号
   */
  unitId = '-1'
  /**
   * 学习方案编号
   */
  schemeId: string
  /**
   * 课程编号
   */
  courseId: string
}

export class ExamConfigParameter {
  /**
   * 应用组织机构编号
   */
  organizationId = '-1'
  /**
   * 应用单位编号
   */
  unitId = '-1'
  /**
   * 学习方案编号
   */
  schemeId: string
  /**
   * 考试场次编号
   */
  examRoundId: string
}

class LoginConfigCache extends LoginScenesAntiConfig implements ICache {
  key: string
}

class LearningConfigCache extends LearningScenesAntiConfig implements ICache {
  key: string
}

class ExamConfigCache extends ExamScenesAntiConfig implements ICache {
  key: string
}

interface ICache {
  key: string
}

/**
 * 防作弊配置模块
 */
@Module({ namespaced: true, dynamic: true, name: 'CustomerAntiCheatConfigModule', store })
class AntiCheatConfigModule extends VuexModule implements AntiCheatConfigState {
  /**
   * 登录防作弊配置
   */
  public loginScenesAntiConfigList: Array<LoginConfigCache> = new Array<LoginConfigCache>()
  /**
   * 学习防作弊配置
   */
  public learningScenesAntiConfigList: Array<LearningConfigCache> = new Array<LearningConfigCache>()
  /**
   * 考试防作弊配置
   */
  public examScenesAntiConfigList: Array<ExamConfigCache> = new Array<ExamConfigCache>()
  /**
   * 最后一次加载的登录防作弊配置
   */
  public lastedLoginScenesAntiConfig: LoginConfigCache | undefined
  /**
   * 最后一次加载的学习防作弊配置
   */
  public lastedLearningScenesAntiConfig: LearningConfigCache | undefined
  /**
   * 最后一次加载的考试防作弊配置
   */
  public lastedExamScenesAntiConfig: ExamConfigCache | undefined

  public faceSupervisionSetting: FaceSupervisionSettingResponse = new FaceSupervisionSettingResponse()
  /**
   * 课件防作弊随机拍摄点信息
   */
  public coursewareRandomPointResponse: CoursewareRandomPointResponse = new CoursewareRandomPointResponse()

  @Action
  async initDatumConfig() {
    try {
      const { data } = await AntiCheatGateway.findDatumConfig()
      return data
    } catch (e) {
      // todo
    }
    return
  }

  //region action
  /**
   * 初始化平台下学习场景防作弊配置
   */
  @Action
  @Role([RoleType.user])
  public async initLearningConfig(param: LearningConfigParameter): Promise<ResponseStatus> {
    const key = utils.generateLearningConfigKey(param)
    const o = this.learningScenesAntiConfigList.find(e => e.key === key)
    if (!o) {
      param.organizationId = '-1'
      param.unitId = '-1'
      const { status, data } = await AntiCheatGateway.findLearningScenesConfig(param)
      if (status.isSuccess()) {
        if (data) {
          const config: LearningConfigCache = new LearningConfigCache()
          config.id = data.id
          config.afterLearningBehavior = data.afterLearningBehavior
          config.beforeLearningBehavior = data.beforeLearningBehavior
          config.effectiveIfNoMatch = data.effectiveIfNoMatch
          if (data.createTime) {
            config.createTime = moment(data.createTime, Constants.DATE_PATTERN).toDate()
          }
          if (data.updateTime) {
            config.updateTime = moment(data.updateTime, Constants.DATE_PATTERN).toDate()
          }
          config.enable = data.enable
          config.createUserId = data.createUserId
          config.updateUserId = data.updateUserId
          config.useRange = data.useRange
          if (data.processLearningBehavior) {
            const behavior: ProcessBehavior = new ProcessBehavior()
            behavior.dimensions = data.processLearningBehavior.dimensions
            behavior.recordPoints = data.processLearningBehavior.recordPoints
            behavior.traceMode = data.processLearningBehavior.traceMode
            config.processLearningBehavior = behavior
          }
          if (data.shapeModel) {
            const shape: FaceRecognitionShape = new FaceRecognitionShape()
            shape.id = data.shapeModel.id
            shape.verification = data.shapeModel.verification
            if (data.shapeModel.createTime) {
              shape.createTime = moment(data.shapeModel.createTime, Constants.DATE_PATTERN).toDate()
            }
            shape.promptText = data.shapeModel.promptText
            shape.protocolText = data.shapeModel.protocolText
            shape.similarity = data.shapeModel.similarity
            shape.verificationTimes = data.shapeModel.verificationTimes
            config.shapeModel = shape
          }
          config.key = key
          this.SET_LEARNING_ANTI_CONFIG(config)
          this.SET_LASTED_LEARNING_ANTI_CONFIG(config)
          return Promise.resolve(status)
        }
      }
      this.SET_LASTED_LEARNING_ANTI_CONFIG(undefined)
      return Promise.resolve(status)
    } else this.SET_LASTED_LEARNING_ANTI_CONFIG(o)
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 重新加载学习防作弊
   */
  @Action
  @Role([RoleType.user])
  public async doReloadLearningConfig(param: LearningConfigParameter): Promise<ResponseStatus> {
    const key = utils.generateLearningConfigKey(param)
    this.REMOVE_LEARNING_ANTI_CONFIG(key)
    return this.initLearningConfig(param)
  }

  /**
   * 初始化登录配置
   * @param param 参数
   */
  @Action
  @Role([RoleType.user])
  public async initLoginConfig(param: LoginConfigParameter): Promise<ResponseStatus> {
    const key = utils.generateLoginConfigKey(param)
    const o = this.loginScenesAntiConfigList.find(e => e.key === key)
    if (!o) {
      const { data, status } = await AntiCheatGateway.findLoginScenesConfig(param)
      if (status.isSuccess()) {
        if (data) {
          const config: LoginConfigCache = new LoginConfigCache()
          config.id = data.id
          config.beforeLoginBehavior = data.beforeLoginBehavior
          if (data.createTime) {
            config.createTime = moment(data.createTime, Constants.DATE_PATTERN).toDate()
          }
          if (data.updateTime) {
            config.updateTime = moment(data.updateTime, Constants.DATE_PATTERN).toDate()
          }
          config.enable = data.enable
          config.createUserId = data.createUserId
          config.updateUserId = data.updateUserId
          config.useRange = data.useRange
          if (data.shapeModel) {
            const shape: FaceRecognitionShape = new FaceRecognitionShape()
            shape.id = data.shapeModel.id
            shape.verification = data.shapeModel.verification
            if (data.shapeModel.createTime) {
              shape.createTime = moment(data.shapeModel.createTime, Constants.DATE_PATTERN).toDate()
            }
            shape.promptText = data.shapeModel.promptText
            shape.protocolText = data.shapeModel.protocolText
            shape.similarity = data.shapeModel.similarity
            shape.verificationTimes = data.shapeModel.verificationTimes
            config.shapeModel = shape
          }
          config.key = key
          this.SET_LOGIN_ANTI_CONFIG(config)
          this.SET_LASTED_LOGIN_ANTI_CONFIG(config)
          return Promise.resolve(status)
        }
      }

      this.SET_LASTED_LOGIN_ANTI_CONFIG(undefined)
      return Promise.resolve(status)
    } else this.SET_LASTED_LOGIN_ANTI_CONFIG(o)
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 重新加载登录防作弊
   */
  @Action
  @Role([RoleType.user])
  public async doReloadLoginConfig(param: LoginConfigParameter): Promise<ResponseStatus> {
    const key = utils.generateLoginConfigKey(param)
    this.REMOVE_LOGIN_ANTI_CONFIG(key)
    return this.initLoginConfig(param)
  }

  /**
   * 初始化考试配置
   * @param param 参数
   */
  @Action
  @Role([RoleType.user])
  public async initExamConfig(param: ExamConfigParameter): Promise<ResponseStatus> {
    const key = utils.generateExamConfigKey(param)
    const o = this.examScenesAntiConfigList.find(e => e.key === key)
    param.organizationId = '-1'
    param.unitId = '-1'
    if (!o) {
      const { data, status } = await AntiCheatGateway.findExamScenesConfig(param)
      if (status.isSuccess()) {
        if (data) {
          const config: ExamConfigCache = new ExamConfigCache()
          config.id = data.id
          config.afterExamBehavior = data.afterExamBehavior
          config.beforeExamBehavior = data.beforeExamBehavior
          if (data.createTime) {
            config.createTime = moment(data.createTime, Constants.DATE_PATTERN).toDate()
          }
          if (data.updateTime) {
            config.updateTime = moment(data.updateTime, Constants.DATE_PATTERN).toDate()
          }
          config.enable = data.enable
          config.createUserId = data.createUserId
          config.updateUserId = data.updateUserId
          config.useRange = data.useRange
          if (data.processExamBehavior) {
            const behavior: ProcessBehavior = new ProcessBehavior()
            behavior.dimensions = data.processExamBehavior.dimensions
            behavior.recordPoints = data.processExamBehavior.recordPoints
            behavior.traceMode = data.processExamBehavior.traceMode
            config.processExamBehavior = behavior
          }
          if (data.shapeModel) {
            const shape: FaceRecognitionShape = new FaceRecognitionShape()
            shape.id = data.shapeModel.id
            shape.verification = data.shapeModel.verification
            if (data.shapeModel.createTime) {
              shape.createTime = moment(data.shapeModel.createTime, Constants.DATE_PATTERN).toDate()
            }
            shape.promptText = data.shapeModel.promptText
            shape.protocolText = data.shapeModel.protocolText
            shape.similarity = data.shapeModel.similarity
            shape.verificationTimes = data.shapeModel.verificationTimes
            config.shapeModel = shape
          }
          config.key = key
          this.SET_EXAM_ANTI_CONFIG(config)
          this.SET_LASTED_EXAM_ANTI_CONFIG(config)
          return Promise.resolve(status)
        }
      }
      this.SET_LASTED_EXAM_ANTI_CONFIG(undefined)
      return Promise.resolve(status)
    } else this.SET_LASTED_EXAM_ANTI_CONFIG(o)
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 重新加载考试防作弊
   */
  @Action
  @Role([RoleType.user])
  public async doReloadExamConfig(param: ExamConfigParameter): Promise<ResponseStatus> {
    const key = utils.generateExamConfigKey(param)
    this.REMOVE_EXAM_ANTI_CONFIG(key)
    return this.initExamConfig(param)
  }

  /**
   * 加载子项目级别的人脸识别配置
   */
  @Action
  @Role([RoleType.user])
  public async doLoadSubProjectFaceSupervisionSetting(reload = false): Promise<ResponseStatus> {
    if (reload) {
      const { data, status } = await AntiCheatGateway.findSupervisionConfigBySubProject()
      if (status.isSuccess() && data) {
        this.SET_SUPERVISION_CONFIG(data)
      }
      return Promise.resolve(status)
    }
    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 申请课件防作弊随机拍摄点信息
   */
  @Action
  public async applyCoursewareRandomFacePoints(applyInfo: ApplyCoursewareRandomPointRequest): Promise<ResponseStatus> {
    const response = await AntiCheatGateway.applyCoursewareRandomFacePoints(applyInfo)
    if (response.status.isSuccess()) {
      this.SET_COURSEWARE_RANDOM_POINT(response.data)
    }
    return response.status
  }

  /**
   * 设置平台级别的防作弊配置
   * @constructor
   */
  @Mutation
  private SET_SUPERVISION_CONFIG(item: FaceSupervisionSettingResponse) {
    this.faceSupervisionSetting = item
  }

  //endregion

  //region mutation
  /**
   * 设置学习防作弊配置
   * @param params 学习防作弊配置
   * @constructor
   */
  @Mutation
  private SET_LEARNING_ANTI_CONFIG(params: LearningConfigCache) {
    utils.setValue(this.learningScenesAntiConfigList, params)
  }

  /**
   * 设置学习防作弊配置
   * @param params 学习防作弊配置
   * @constructor
   */
  @Mutation
  private SET_LASTED_LEARNING_ANTI_CONFIG(params: LearningConfigCache | undefined) {
    this.lastedLearningScenesAntiConfig = params
  }

  /**
   * 移除学习配置
   * @param key 存储key
   * @constructor
   */
  @Mutation
  private REMOVE_LEARNING_ANTI_CONFIG(key: string) {
    const index = this.learningScenesAntiConfigList.findIndex(x => x.key === key)
    if (index >= 0) {
      this.learningScenesAntiConfigList.slice(index, index + 1)
    }
  }

  /**
   * 设置登录防作弊配置
   * @param params 学习防作弊配置
   * @constructor
   */
  @Mutation
  private SET_LOGIN_ANTI_CONFIG(params: LoginConfigCache) {
    utils.setValue(this.loginScenesAntiConfigList, params)
  }

  /**
   * 设置登录防作弊配置
   * @param params 学习防作弊配置
   * @constructor
   */
  @Mutation
  private SET_LASTED_LOGIN_ANTI_CONFIG(params: LoginConfigCache | undefined) {
    this.lastedLoginScenesAntiConfig = params
  }

  /**
   * 移除登录配置
   * @param key 存储key
   * @constructor
   */
  @Mutation
  private REMOVE_LOGIN_ANTI_CONFIG(key: string) {
    const index = this.loginScenesAntiConfigList.findIndex(x => x.key === key)
    if (index >= 0) {
      this.loginScenesAntiConfigList.slice(index, index + 1)
    }
  }

  /**
   * 设置登录防作弊配置
   * @param params 学习防作弊配置
   * @constructor
   */
  @Mutation
  private SET_EXAM_ANTI_CONFIG(params: ExamConfigCache) {
    utils.setValue(this.examScenesAntiConfigList, params)
  }

  /**
   * 设置登录防作弊配置
   * @param params 学习防作弊配置
   * @constructor
   */
  @Mutation
  private SET_LASTED_EXAM_ANTI_CONFIG(params: ExamConfigCache | undefined) {
    this.lastedExamScenesAntiConfig = params
  }

  /**
   * 移除登录配置
   * @param key 存储key
   * @constructor
   */
  @Mutation
  private REMOVE_EXAM_ANTI_CONFIG(key: string) {
    const index = this.examScenesAntiConfigList.findIndex(x => x.key === key)
    if (index >= 0) {
      this.examScenesAntiConfigList.slice(index, index + 1)
    }
  }

  /**
   * 设置课件防作弊随机拍摄点信息
   * @param data
   * @constructor
   * @private
   */
  @Mutation
  private SET_COURSEWARE_RANDOM_POINT(data: CoursewareRandomPointResponse) {
    this.coursewareRandomPointResponse = data
  }

  //endregion

  //region getter
  /**
   * 获取学习场景配置的防作弊信息
   */
  get getLearningAntiConfig() {
    return (param: LearningConfigParameter): LearningScenesAntiConfig | undefined => {
      return this.learningScenesAntiConfigList.find(x => x.key === utils.generateLearningConfigKey(param))
    }
  }

  /**
   * 获取登录场景防作弊配置
   */
  get getLoginAntiConfig() {
    return (param: LoginConfigParameter): LoginScenesAntiConfig | undefined => {
      return this.loginScenesAntiConfigList.find(x => x.key === utils.generateLoginConfigKey(param))
    }
  }

  /**
   * 获取考试场景防作弊配置
   */
  get getExamAntiConfig() {
    return (param: ExamConfigParameter): ExamScenesAntiConfig | undefined => {
      return this.examScenesAntiConfigList.find(x => x.key === utils.generateExamConfigKey(param))
    }
  }

  /**
   * 获取比对效果
   */
  get getSimilarity() {
    return (): number => {
      return (
        this.faceSupervisionSetting?.examinationConfigSetting?.shapeMode?.similarity |
        this.faceSupervisionSetting?.learningConfigSetting?.shapeMode?.similarity |
        this.faceSupervisionSetting?.loginConfigSetting?.shapeMode?.similarity
      )
    }
  }

  /**
   * 获取最后一次加载的学习反作弊配置
   */
  get getLastedLearningAntiConfig() {
    return (): LearningScenesAntiConfig | undefined => {
      return this.lastedLearningScenesAntiConfig
    }
  }

  /**
   * 获取最后一次加载的登录反作弊配置
   */
  get getLastedLoginScenesAntiConfig() {
    return (): LoginScenesAntiConfig | undefined => {
      return this.lastedLoginScenesAntiConfig
    }
  }

  /**
   * 获取最后一次加载的考试反作弊配置
   */
  get getLastedExamScenesAntiConfig() {
    return (): ExamScenesAntiConfig | undefined => {
      return this.lastedExamScenesAntiConfig
    }
  }

  //endregion
}

export default getModule(AntiCheatConfigModule)
