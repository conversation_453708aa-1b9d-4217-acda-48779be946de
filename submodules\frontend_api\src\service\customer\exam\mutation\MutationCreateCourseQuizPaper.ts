import msCourseLearning from '@api/ms-gateway/ms-studentcourselearning-v1'
import { MutationCreatePaper } from '@api/service/customer/exam/mutation/MutationCreatePaper'
import { QuestionGroupViewResponse } from '@api/service/customer/exam/mutation/vo/AnswerPaper'
import { cloneDeep } from 'lodash'
import { QuestionKeyValue } from '@api/service/customer/exam/utils/Constant'
export class MutationCreateCourseQuizPaper extends MutationCreatePaper {
  /**
   * 获取作答token
   */
  async getAnswerToken() {
    const res = await msCourseLearning.applyCourseQuizLearning(this.studentToken)
    if (res.status.isSuccess()) {
      this.answerToken = res.data.token
    }
    return res.status
  }
  async calPaper() {
    if (!this.answerPaper.groups) {
      this.answerPaper.groups = []
    }
    this.answerPaper.questions.forEach(item => {
      const questionGroup = this.answerPaper.groups.find(tmpItem => tmpItem.questionType == item.questionType)
      if (questionGroup) {
        questionGroup.questionCount++
      } else {
        const insertGroup = new QuestionGroupViewResponse()
        insertGroup.questionCount = 1
        insertGroup.questionType = item.questionType
        insertGroup.groupName = QuestionKeyValue.getQuestionName(insertGroup.questionType)
        insertGroup.sequence = this.answerPaper.groups.length
        this.answerPaper.groups.push(insertGroup)
      }
    })
    this.answerPaper = cloneDeep(this.answerPaper)
  }
}
