// import MutationRegisterAndLogin from '@api/service/management/online-school-config/functionality-setting/mutation/MutationRegisterAndLogin'
// import jsgkyptAccount from '@api/platform-gateway/jsgkypt-account-v1'
import {
  ApplyOpenIdRequest,
  ApplyOpenIdResponse,
  default as Basicdata,
  default as BasicdataDomain,
  BindPlatformAccountRequest,
  CaptchaApplyRequest,
  CaptchaValidRequest,
  ChangePhoneRequest,
  CurrentAccountChangePasswordCauseForgetRequest,
  SmsCodeApplyRequest,
  SmsCodeValidRequest,
  ValidIdentityRequest,
  default as basicDataDomain
} from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import PlatformStudent from '@api/platform-gateway/platform-student-v1'
import ThirdParty, { ThirdPartyType } from '@api/service/common/authentication/plugins/third-party'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import Context from '@api/service/common/context/Context'
import UserModule from '@api/service/customer/user/UserModule'
import OnlineSchoolConfigModuleH5 from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
import { Response, ResponseStatus } from '@hbfe/common'
import axios, { AxiosRequestConfig } from 'axios'
import { EventEmitter } from 'events'
import Vue from 'vue'
import {
  applyCaptchaUrl,
  authTokenLoginUrl,
  authUrl,
  loginByPhoneUrl,
  loginH5MsByAccountUrl,
  loginMsByAccountUrl,
  loginUrl,
  loginWithoutCaptchaUrl,
  logoutMztUrl,
  logoutUrl,
  sendShortMessageCodeUrl,
  sendShortMessageCodeUrlApply,
  sendShortMessageCodeWithoutCaptchaUrl,
  shortMessageLoginUrl,
  thirdTokenLoginUrl,
  validateCaptchaUrl,
  validateShortMessageCaptchaUrl
} from './contant'
import LogAble from './interfaces/LogAble'
import {
  AccountType,
  ApplyDingDingLoginQRCodeData,
  ApplyDingDingLoginQRCodeParams,
  AuthTokenLoginParams,
  LoginParams,
  RoleTypeEnum,
  SendMessageParams,
  ShortMessageLoginParams
} from './interfaces/LoginParams'
import PhoneLogAble from './interfaces/PhoneLogAble'
import Env from '@api/service/common/utils/Env'

const Qs = require('querystring')

export { AccountType, LoginParams, SendMessageParams, ShortMessageLoginParams }

export enum GrantType {
  // 登录票类
  casTicket = 'cas_ticket',
  // 刷新票类
  refreshToken = 'refresh_token'
}

export class InitOptions {
  appKey: string
  authUrl: string
  ssoUrl: string
  service?: string
  prefix: string
  request: any
  thirdAuthUrl?: string
}

enum RequestMethod {
  POST = 'post',
  GET = 'get'
}

const grantType = 'grant_type'

/**
 * 授权需要的参数对象
 */
class AuthParams {
  // 登录票
  ticket: string;
  // 判别类型
  [grantType]: string
}

const refreshTokenKey = 'refresh_token'

/**
 * 刷新AToken的参数对象
 */
export class RefreshParams {
  [grantType]: string;
  [refreshTokenKey]: string
}

const request = async function (method: RequestMethod, url: string, config?: any) {
  return new Promise<Response<any>>((resolve, reject) => {
    const requestConfig = {
      url,
      method
    }
    Object.assign(requestConfig, config)
    axios.request(requestConfig).then((data) => {
      const response = new Response()
      response.status = new ResponseStatus(data.data.code, data.data.message)
      response.data = data.data.data
      resolve(response)
    }, reject)
  })
}

class Authentication extends EventEmitter implements LogAble, PhoneLogAble {
  constructor(options: InitOptions) {
    super()

    this.options = options
    // 如果没有提供 appKey 则报警
    this.service = options.service || `${location.protocol}//${location.hostname}`
    this.options.service = this.service

    localStorage.setItem(this.appAuthenticationStr, this.options.appKey)
    axios.interceptors.request.use((config: AxiosRequestConfig) => {
      const accessToken = this.getKey(Authentication.accessTokenKeyName)
      if (accessToken) {
        // config.headers.Authorization = `Mship ${accessToken}`
      }
      config.headers[this.appAuthenticationStr] = `Basic ${options.appKey}`
      return config
    })
    this.setRememberLoginInfo()
    this.enableThirdParty()
  }

  static refreshTokenKeyName = 'Refresh-Token'
  static accessTokenKeyName = 'Access-Token'
  static ticketGrantTicketIdKeyName = 'Grant-Ticket'
  static serviceTicketKIdKeyName = 'Service-Ticket'
  static rememberLoginInfoKeyName = 'Login-Info'
  static accountTypeKeyName = 'Account-Type'
  options: InitOptions = new InitOptions()
  appAuthenticationStr = 'App-Authentication'
  service: string
  captchaToken: string
  captchaTicket: string
  shortMessageCaptchaToken: string
  shortMessageCaptchaTicket: string
  isCaptchaValidate = false
  isShortMessageCaptchaValidate = false
  validatedCaptchaValue = ''
  validatedShortMessageCaptchaValue = ''
  ticketGrantTicketId?: string = ''
  serviceTicket?: string = ''
  accessToken?: string = ''
  refreshToken?: string = ''
  expiresIn?: string = ''
  rememberLoginInfo: LoginParams
  applyDingDingLoginQRCodeData: ApplyDingDingLoginQRCodeData
  thirdParty: ThirdParty
  validIdentityToken = ''

  /**
   * 允许第三方
   */
  enableThirdParty() {
    // this.thirdParty = new ThirdParty(this.options, this)
  }

  static hasToken(prefix: string) {
    return localStorage.getItem(`${prefix}.${this.accessTokenKeyName}`)
  }

  setKey(key: string, value: string) {
    localStorage.setItem(`${this.options.prefix}.${key}`, value)
  }

  getKey(key: string): string {
    return localStorage.getItem(`${this.options.prefix}.${key}`) || ''
  }

  removeKey(key: string) {
    return localStorage.removeItem(`${this.options.prefix}.${key}`)
  }

  private setRememberLoginInfo() {
    const loginParams = new LoginParams()
    try {
      const result = JSON.parse(this.getKey(Authentication.rememberLoginInfoKeyName) || '{}')
      loginParams.password = result.password
      loginParams.account = result.account
      loginParams.longTerm = !!(result.password && result.account)
      this.rememberLoginInfo = loginParams
    } catch (e) {
      this.rememberLoginInfo = new LoginParams()
    }
  }

  /**
   * 执行登录操作
   * @param loginParams
   * @param url
   */
  private async doLogin(loginParams: LoginParams, url: string): Promise<Response<any>> {
    loginParams.service = this.service
    const result = await request(RequestMethod.POST, `${this.options.ssoUrl}${url}`, {
      data: loginParams
    })
    if (result.status.code !== 200) {
      return Promise.reject(result.status)
    }
    this.doRememberLoginInfo(loginParams)
    this.emit('login', true)
    this.storeTicket(result.data.serviceTicketId, result.data.ticketGrantTicketId)
    return result
  }

  storeTicket(serviceTicketId: string, ticketGrantTicketId: string) {
    this.serviceTicket = serviceTicketId
    this.ticketGrantTicketId = ticketGrantTicketId
    if (this.ticketGrantTicketId) {
      this.setKey(Authentication.ticketGrantTicketIdKeyName, this.ticketGrantTicketId)
    }
    if (this.serviceTicket) {
      this.setKey(Authentication.serviceTicketKIdKeyName, this.serviceTicket)
    }
  }

  /**
   * 通用登录，没有默认accountType
   * @param loginParams
   */
  async login(loginParams: LoginParams): Promise<Response<any>> {
    loginParams.captchaToken = this.captchaToken
    return this.doLogin(loginParams, loginUrl)
  }

  async loginWithoutCaptcha(loginParams: LoginParams): Promise<Response<any>> {
    loginParams.accountType = AccountType.customer
    return this.doLogin(loginParams, loginWithoutCaptchaUrl)
  }

  /**
   * 使用培训平台后台返回的authToken进行登录认证
   * @param token
   */
  async loginWithToken(token: string) {
    const loginParams = new AuthTokenLoginParams()
    loginParams.authToken = token
    const result = await this.options.request.post(`${this.options.ssoUrl}${authTokenLoginUrl}`, loginParams)
    if (result?.status !== 200) {
      return Promise.reject(result)
    }
    if (result?.data?.code !== 200) {
      return Promise.reject(result)
    }
    this.storeTicket(result.data.data.serviceTicketId, result.data.data.ticketGrantTicketId)
    return result
  }

  /**
   * 微信内置游览器授权登录
   */
  async doLoginWxH5() {
    // const webPortal = OnlineSchoolConfigModule.queryOnlineSchoolConfigFactory.queryWebPortalConfig.webPortal
    // 阿波罗拿配置，appid和回调
    const register = OnlineSchoolConfigModuleH5.mutationFunctionalitySettingFactory.registerAndLogin
    const registerConfigRes = await register.queryLoginSetting()
    const APP_ID = register?.loginSetting?.authSeetingVo?.appId
    const serviceId = Context.servicerInfo.id + 'h5' //服务商ID拼接H5
    const isDemo = true
    const webEnv = Env.curProxyEnv

    // H5授权重定向域名
    let REDIRECT_URI = ''
    if (Env.isProxyInnerNetworkEnv) {
      REDIRECT_URI = 'http://www.fjhb.cn/' + serviceId + webEnv
    } else {
      if (isDemo && APP_ID == 'wx1f30614281c34264') {
        REDIRECT_URI = window.location.protocol + '//jxjy.59iedu.com/' + serviceId
      } else {
        REDIRECT_URI = window.location.protocol + '//' + window.location.hostname + '/' + serviceId
      }
    }
    // const REDIRECT_URI = ConfigCenterModule.getFrontendApplication(frontendApplication.redirectUrl)
    const url =
      'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
      APP_ID +
      '&redirect_uri=' +
      REDIRECT_URI +
      '&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'
    // window.open(url)
    location.href = url
  }

  /**
   * 申请获取微信平台Openid ---- H5使用
   */
  async applyOpenIdByServicerId(code: string): Promise<ApplyOpenIdResponse> {
    const request = new ApplyOpenIdRequest()
    request.code = code
    request.servicerId = Context.servicerInfo.id
    const res = await basicDataDomain.applyOpenIdByServicerId(request)
    if (res.data.code == '200') {
      localStorage.setItem('customer.WX-openId', res.data.openId)
      localStorage.setItem('customer.WX-unionId', res.data.unionId)
      localStorage.setItem('customer.WX-nickname', res.data.nickname)
    }
    return res.data
  }
  /**
   * 申请获取微信平台Openid ---- Web使用
   */
  async applyScanCodeOpenIdByServicerId(code: string): Promise<ApplyOpenIdResponse> {
    const request = new ApplyOpenIdRequest()
    request.code = code
    request.servicerId = Context.servicerInfo.id
    const res = await basicDataDomain.applyScanCodeOpenIdByServicerId(request)
    if (res.data.code == '200') {
      localStorage.setItem('customer.WX-openId', res.data.openId)
      localStorage.setItem('customer.WX-unionId', res.data.unionId)
      localStorage.setItem('customer.WX-nickname', res.data.nickname)
    }
    return res.data
  }
  /**
   * 校验是否绑定微信开放平台
   */
  async validIsBindWeChatOpenPlatform(openId: string) {
    const res = await Basicdata.validIsBindWeChatOpenPlatform({
      token: ConfigCenterModule.getFrontendApplication(frontendApplication.wxOpenIdToken),
      roleCategory: 1,
      openId
    })
    return res.data.isBind
  }
  /**
   * 申请绑定微信开放平台并返回账户ID登陆token
   */
  async applyBindWeChatOpenPlatformAndValidLogin(account: string, password: string, openId: string, nickname: string) {
    const res = await Basicdata.applyBindWeChatOpenPlatformAndValidLogin({
      token: ConfigCenterModule.getFrontendApplication(frontendApplication.wxOpenIdToken),
      roleCategory: 1,
      account,
      password,
      openId,
      nickname
    })
    if (res.data.code == '200') {
      await this.loginWithWxH5Token(res.data.token)
    }
    return res.data
  }
  /**
   * 申请微信开放平台账户ID登陆token
   */
  async applyLoginByOpenId(openId: string) {
    const res = await Basicdata.applyLoginByOpenId({
      token: ConfigCenterModule.getFrontendApplication(frontendApplication.wxOpenIdToken),
      roleCategory: 1,
      openId
    })
    if (res.data.code == '200') {
      await this.loginWithWxH5Token(res.data.token)
    }
    // 调用单点登录 /rest/ms2/accountid/login
    return res.data.code
  }
  /**
   * 微信H5登录
   * @param token
   */
  async loginWithWxH5Token(token: string) {
    const loginParams = new AuthTokenLoginParams()
    // 区分学员登录和集体登录
    localStorage.setItem('customer.Account-Type', '1')
    loginParams.authToken = token
    const result = await this.options.request.post(`${this.options.ssoUrl}${loginH5MsByAccountUrl}`, {
      token
    })
    if (typeof result?.status === 'number' && result?.status !== 200) {
      return Promise.reject(result)
    }
    if (result?.data?.code !== 200) {
      return Promise.reject(result)
    }
    this.storeTicket(result.data.data.serviceTicketId, result.data.data.ticketGrantTicketId)
    await this.auth()
    await UserModule.queryUserFactory.getQueryUserInfo().getCurrenUserInfo()
    return result
  }

  /**
   * 管理员登录accountType = 2
   * @param loginParams
   */
  async adminLogin(loginParams: LoginParams) {
    loginParams.accountType = AccountType.admin
    this.setKey(Authentication.accountTypeKeyName, loginParams.accountType)
    return this.login(loginParams)
  }

  /**
   * 客户域登录，默认accountType=1
   * @param loginParams
   */
  async customerLogin(loginParams: LoginParams) {
    loginParams.accountType = AccountType.customer
    this.setKey(Authentication.accountTypeKeyName, loginParams.accountType)
    return this.login(loginParams)
  }

  private doRememberLoginInfo(params: LoginParams) {
    if (params.longTerm) {
      this.rememberLoginInfo = params
      this.setKey(
        Authentication.rememberLoginInfoKeyName,
        JSON.stringify({
          account: params.identity,
          password: params.password,
          longTerm: true
        })
      )
    } else {
      this.rememberLoginInfo = new LoginParams()
      this.removeKey(Authentication.rememberLoginInfoKeyName)
    }
  }

  /**
   * 登出
   */
  async logout() {
    const result = await axios.get(`${this.options.ssoUrl}${logoutUrl}/${this.getTicketGrantTicketId()}`)
    const isLogOutSuccess = result.data.code === 200
    if (isLogOutSuccess) {
      this.emit('logout', isLogOutSuccess)
      const keys = [
        Authentication.ticketGrantTicketIdKeyName,
        Authentication.serviceTicketKIdKeyName,
        Authentication.accessTokenKeyName,
        Authentication.refreshTokenKeyName,
        Authentication.accountTypeKeyName
      ]
      keys.forEach((key) => {
        this.removeKey(key)
      })
    }
    return isLogOutSuccess
  }

  /**
   * 退出闽政通登录
   * @param ssoTicket
   * @param redirect
   */
  logoutMzt(redirect = true, ssoTicket = '') {
    const returnUrl =
      process.env.NODE_ENV === 'development'
        ? `${location.protocol}//${location.hostname}:8080`
        : `${window.location.origin}${window.location.pathname}`
    let redirectUrl = `${this.options.ssoUrl}${logoutMztUrl}?ssoTicket=${ssoTicket}&returnUrl=`
    if (redirect) {
      redirectUrl += returnUrl
    }
    window.location.replace(redirectUrl)
    return ''
  }

  /**
   * 获取验证码
   * // 时效性30秒？
   */
  async applyCaptcha() {
    const result = await request(RequestMethod.GET, `${this.options.authUrl}${applyCaptchaUrl}`)
    this.captchaTicket = result.data.captchaTicket
    return result
  }

  async applyDingDingLoginQRCodeGoto(resultUrl: string) {
    if (window.addEventListener) {
      window.addEventListener('message', this.handleMessageForDingDing, false)
    } else if ((window as any).attachEvent) {
      ;(window as any).attachEvent('onmessage', this.handleMessageForDingDing)
    }

    const params = new ApplyDingDingLoginQRCodeParams()
    params.service = this.service
    params.thirdPartyType = ThirdPartyType.dingDing
    params.resultUrl = resultUrl
    params.accountType = AccountType.admin
    params.connectIdType = 1
    // todo
    // const thirdLoginRedirectHost = ConfigCenterModule.getApplicationByName('applicationDiff.thirdLoginRedirectHost')
    // const result = await request(RequestMethod.POST, `${thirdLoginRedirectHost}${applyDdWebQRCodeUrl}`, {
    //   data: params
    // })
    // this.applyDingDingLoginQRCodeData = result.data
    return Promise.resolve({})
  }

  getDingDingLoginGotoParam() {
    const data = this?.applyDingDingLoginQRCodeData
    if (!data) {
      console.warn('gotoParam empty')
      return ''
    }
    let gotoUrl = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${
      data.clientId
    }&response_type=code&scope=snsapi_login&state=${data?.state}&redirect_uri=${encodeURIComponent(data.redirectUrl)}`
    console.log('gotoUrl:', gotoUrl)
    gotoUrl = encodeURIComponent(gotoUrl)
    return gotoUrl as string
  }

  async handleMessageForDingDing(event: any) {
    const origin = event.origin
    console.log('origin', event.origin)
    if (origin == 'https://login.dingtalk.com') {
      //判断是否来自ddLogin扫码事件。
      const loginTmpCode = event.data
      //获取到loginTmpCode后就可以在这里构造跳转链接进行跳转了
      console.log('loginTmpCode：', loginTmpCode)
      const data = Vue.prototype.$authentication?.applyDingDingLoginQRCodeData
      const url = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${
        data?.clientId
      }&response_type=code&scope=snsapi_login&state=${data?.state}&redirect_uri=${encodeURIComponent(
        data?.redirectUrl as string
      )}&loginTmpCode=${loginTmpCode}`
      window.location.href = url
    }
  }

  /**
   * 验证验证码
   * @param captchaValue
   */
  async validateCaptcha(captchaValue: string) {
    if (captchaValue === this.validatedCaptchaValue) {
      return this.isCaptchaValidate
    }
    const result = await request(
      RequestMethod.GET,
      `${this.options.authUrl}${validateCaptchaUrl}/${this.captchaTicket}/${captchaValue}`
    )
    this.captchaToken = result.data.captchaToken
    this.isCaptchaValidate = result.data.pass
    if (!this.isCaptchaValidate) {
      return Promise.reject(result.data)
    }
    this.validatedCaptchaValue = captchaValue
    return result
  }

  /**
   * 验证短信验证码
   * @param captchaValue
   */
  async validateShortMessageCaptcha(captchaValue: string) {
    if (captchaValue === this.validatedShortMessageCaptchaValue) {
      return this.isShortMessageCaptchaValidate
    }
    const result = await request(
      RequestMethod.GET,
      `${this.options.authUrl}${validateShortMessageCaptchaUrl}/${this.shortMessageCaptchaTicket}/${captchaValue}`
    )
    this.shortMessageCaptchaToken = result.data.captchaToken
    this.isShortMessageCaptchaValidate = result.data.pass
    if (!this.isShortMessageCaptchaValidate) {
      return Promise.reject(result.data)
    }
    this.validatedShortMessageCaptchaValue = captchaValue
    return result
  }

  /**
   * 发送短信验证码
   * @param sendMessageParams
   */
  async sendMessage(sendMessageParams: SendMessageParams): Promise<any> {
    sendMessageParams.service = this.service
    sendMessageParams.captchaToken = this.captchaToken
    return request(RequestMethod.POST, `${this.options.ssoUrl}${sendShortMessageCodeUrl}`, {
      data: sendMessageParams
    })
  }
  /**
   * 发送短信验证码（不需要图形验证码）
   * @param sendMessageParams
   */
  async sendMessageWithoutCaptcha(sendMessageParams: SendMessageParams): Promise<any> {
    sendMessageParams.service = this.service
    return request(RequestMethod.POST, `${this.options.ssoUrl}${sendShortMessageCodeWithoutCaptchaUrl}`, {
      data: sendMessageParams
    })
  }

  /**
   * 发送短信验证码
   * @param sendMessageParams
   */
  async sendMessageNoValidate(sendMessageParams: SendMessageParams): Promise<any> {
    sendMessageParams.service = this.service
    sendMessageParams.captchaToken = this.captchaToken
    const result = await request(RequestMethod.POST, `${this.options.authUrl}${sendShortMessageCodeUrlApply}`, {
      data: sendMessageParams
    })
    this.shortMessageCaptchaTicket = result.data.captchaTicket
    return result
  }

  /**
   * 短信验证码登录
   * @param shortMessageLoginParams
   */
  async shortMessageLogin(shortMessageLoginParams: ShortMessageLoginParams) {
    const result = await request(RequestMethod.POST, `${this.options.authUrl}${shortMessageLoginUrl}`, {
      data: shortMessageLoginParams
    })
    if (!result.status.isSuccess()) {
      return Promise.reject(result.status)
    }
    this.storeTicket(result.data.serviceTicketId, result.data.ticketGrantTicketId)
    return result
  }

  /**
   * 根据认证服务的TOKEN值，进行登录
   * @param
   */
  async ddLoginWithToken(token: string) {
    const result = await request(RequestMethod.GET, `${this.options.authUrl}${thirdTokenLoginUrl}${token}`)
    if (!result.status.isSuccess()) {
      return Promise.reject(result.status)
    }
    // this.storeTicket(result.data.data.serviceTicketId, result.data.data.ticketGrantTicketId)
    this.storeTicket(result.data.serviceTicketId, result.data.ticketGrantTicketId)
    return result
  }

  /**
   * 登录完成后需要调用的授权接口
   * @param params
   */
  async auth(params?: AuthParams) {
    const result = await axios.post(
      `${this.options.authUrl}${authUrl}`,
      Qs.stringify(
        Object.assign(
          {
            [grantType]: GrantType.casTicket,
            ticket: this.getKey(Authentication.serviceTicketKIdKeyName)
          },
          params
        )
      ),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    )
    this.accessToken = result?.data?.data?.access_token
    if (!this.accessToken) {
      return Promise.reject({ code: 201, message: '认证失败' })
    }
    this.refreshToken = result.data.data.refresh_token
    this.expiresIn = result.data.data.expires_in
    if (this.refreshToken) {
      this.setRefreshToken(this.refreshToken)
    }
    if (this.accessToken) {
      this.setAccessToken(this.accessToken)
    }
    return result
  }

  /**
   * 当系统发现AT过期后，需要使用RT去获取新的AT
   */
  async refresh(params?: RefreshParams) {
    const result = await axios.post(
      `${this.options.authUrl}${authUrl}`,
      Qs.stringify(
        Object.assign(
          {
            [grantType]: GrantType.refreshToken,
            [refreshTokenKey]: this.getRefreshToken()
          },
          params
        )
      ),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    )
    if (result.data.code === 200) {
      this.refreshToken = result.data.data.access_token
      if (this.refreshToken) {
        this.setRefreshToken(result.data.data.access_token)
      }
    } else {
      return Promise.reject(result.data)
    }
    return result
  }

  async authTokenLogin(token: string) {
    const result = await this.options.request.post(`${this.options.thirdAuthUrl}${authTokenLoginUrl}`, {
      authToken: token
    })
    console.log(result)
  }

  /**
   * 重新生成服务票
   */
  async applyServiceTicket() {
    const result = await axios.post(
      `${this.options.authUrl}/web/rest/applyServiceTicket`,
      {
        service: this.service,
        ticketGrantTicketId: this.getKey(Authentication.ticketGrantTicketIdKeyName)
      },
      {
        headers: {
          'Content-Type': 'application/json;charset=utf-8'
        }
      }
    )
    if (result.data.code !== 200) {
      return Promise.reject(result.data)
    }
    this.setKey(Authentication.serviceTicketKIdKeyName, result.data.data.serviceTicketId)
    return result
  }

  getRequestHeader() {
    return {
      Authorization: `Mship ${this.getAccessToken()}`,
      [this.appAuthenticationStr]: `Basic ${this.options.appKey}`
    }
  }

  async wxTokenLogin(token: string) {
    const result = await this.thirdParty.login(token)
    this.storeTicket(result.serviceTicketId, result.ticketGrantTicketId)
    await this.auth()
  }

  /**
   * 获取集体报名管理员登录验证码
   */
  async applyGroupAdminCaptcha() {
    const params = new CaptchaApplyRequest()
    params.businessType = 10000
    params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.collectiveRegistrationLoginToken)
    const response = await basicDataDomain.applyCaptcha(params)
    this.captchaTicket = response.data.token

    return response
  }

  /**
   * 获取学员登录验证码
   */
  async applyStudentCaptcha() {
    const params = new CaptchaApplyRequest()
    params.businessType = 10000
    params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.studentLoginToken)
    const response = await basicDataDomain.applyCaptcha(params)
    this.captchaTicket = response.data.token
    return response
  }
  /**
   * 根据Token绑定微信号
   */
  async bindPlatformAccount(request: BindPlatformAccountRequest) {
    request.token = this.captchaToken
    // const { status, data } = await basicDataDomain.loginAndBindOpenPlatform(request)
    const { data } = await basicDataDomain.loginAndBindOpenPlatform(request)
    return data
  }
  /**
   * 微服务验证登录验证码
   */
  async msValidateCaptcha(code: string) {
    const params = new CaptchaValidRequest()
    params.token = this.captchaTicket
    params.captchaCode = code
    const response = await basicDataDomain.validCaptcha(params)
    if (response.data.code !== 200) {
      if (response.data.code === 400) {
        console.error('错误的请求 通常是token错误或者入参错误')
      }
      if (response.data.code === 408) {
        console.error('图形验证码过期')
      }
      if (response.data.code === 500) {
        console.error('验证码不匹配')
      }
      return Promise.reject(response)
    }
    this.validatedCaptchaValue = code
    this.captchaToken = response.data.token
    return response
  }

  /**
   * 学员登录--单点登录
   */
  async studentSingleLogin(studentInfoKey: string) {
    const res = await PlatformStudent.StudentInfoQuery({
      studentInfoKey,
      token: ConfigCenterModule.getFrontendApplication(frontendApplication.singleLoginToken)
    })
    const params = new LoginParams()
    params.token = res.data.loginToken
    return await this.doLogin(params, loginH5MsByAccountUrl)
  }

  /**
   * 学员登录
   */
  async studentLogin(params: LoginParams) {
    params.accountType = AccountType.customer
    params.token = this.captchaToken
    return await this.doLogin(params, loginMsByAccountUrl)
  }

  /**
   * 集体报名管理域登录
   */
  async groupAdminLogin(params: LoginParams) {
    params.accountType = AccountType.customer
    params.token = this.captchaToken
    return await this.doLogin(params, loginMsByAccountUrl)
  }

  /**
   * 获取超管登录验证码
   */
  async applyAdminCaptcha() {
    const params = new CaptchaApplyRequest()
    params.businessType = 10000
    params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superLoginToken)
    const response = await basicDataDomain.applyCaptcha(params)
    this.captchaTicket = response.data.token
    return response
  }
  /**
   * 超管登录
   */
  async superAdminLogin(params: LoginParams) {
    params.accountType = AccountType.admin
    params.token = this.captchaToken
    return await this.doLogin(params, loginMsByAccountUrl)
  }

  /**
   * 获取课件供应商登录验证码
   */
  async applyProviderCaptcha() {
    const params = new CaptchaApplyRequest()
    params.businessType = 10000
    const response = await basicDataDomain.applyCaptcha(params)
    this.captchaTicket = response.data.token
    return response
  }

  /**
   * 课件供应商登录
   */
  async providerLogin(params: LoginParams) {
    params.accountType = AccountType.admin
    params.token = this.captchaToken
    return await this.doLogin(params, loginMsByAccountUrl)
  }

  /**
   * 获取地区管理员登录验证码
   */
  async applyRegionCaptcha() {
    const params = new CaptchaApplyRequest()
    params.businessType = 10000
    params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionAdminLoginToken)
    const response = await basicDataDomain.applyCaptcha(params)
    this.captchaTicket = response.data.token
    return response
  }

  /**
   * 地区管理员登录
   */
  async regionAdminLogin(params: LoginParams) {
    params.accountType = AccountType.admin
    params.token = this.captchaToken
    return await this.doLogin(params, loginMsByAccountUrl)
  }

  /**
   * 获取运营域管理员登录验证码
   */
  async applyOperationAdminCaptcha() {
    const params = new CaptchaApplyRequest()
    params.businessType = 10000
    params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.sunProjectSuperLoginToken)
    const response = await basicDataDomain.applyCaptcha(params)
    this.captchaTicket = response.data.token
    return response
  }

  /**
   * 运营域管理员登录
   */
  async operationAdminLogin(params: LoginParams) {
    params.accountType = AccountType.admin
    params.token = this.captchaToken
    return await this.doLogin(params, loginMsByAccountUrl)
  }

  /**
   * 注册获取图形验证码
   */
  async applyRegistryCaptchCode() {
    const params = new CaptchaApplyRequest()
    params.businessType = 20000
    params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.registerToken)
    const response = await basicDataDomain.applyCaptcha(params)
    this.captchaTicket = response.data.token
    return response
  }
  /**
   * 完善获取图形验证码
   */
  async studentImproveInfoCaptchCode() {
    const params = new CaptchaApplyRequest()
    params.businessType = 20001
    params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.studentImproveInfoToken)
    const response = await basicDataDomain.applyCaptcha(params)
    this.captchaTicket = response.data.token
    return response
  }
  /**
   * 注册获取图形验证码——集体报名注册
   */
  async applyGroupRegistryCaptchCode() {
    const params = new CaptchaApplyRequest()
    params.businessType = 20000
    params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.collectiveRegisterToken)
    const response = await basicDataDomain.applyCaptcha(params)
    this.captchaTicket = response.data.token
    return response
  }
  /**
   * 获取注册手机短信验证码
   * @param phone
   */
  async getSmsCode(phone: string) {
    const params = new SmsCodeApplyRequest()
    params.token = this.captchaToken
    params.businessType = 20000
    params.phone = phone
    const response = await basicDataDomain.applySmsCode(params)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('获取短信验证码失败')
      return Promise.reject(response)
    }
    return response
  }

  /**
   * * 通用发送验证码
   * @param phone
   * @param type
   */
  async msSendSmsCode(phone: string, type: number) {
    const params = new SmsCodeApplyRequest()
    params.token = this.captchaToken
    params.businessType = type
    params.phone = phone
    const response = await basicDataDomain.applySmsCode(params)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('发送验证码失败', response)
      return Promise.reject(response)
    }
    this.shortMessageCaptchaTicket = response.data.token
    return response
  }

  /**
   * * 通用验证手机验证码
   * @param code
   */
  async msValidSmsCode(code: string, phone: string) {
    const params = new SmsCodeValidRequest()
    params.smsCode = code
    params.phone = phone
    params.token = this.shortMessageCaptchaTicket
    const response = await basicDataDomain.validSmsCode(params)
    if ((response.status.code !== 200 && !response.status.isSuccess()) || response.data.code !== 200) {
      console.error('验证短信验证码失败', response)
      response.status.message = '验证码不正确'
      return Promise.reject(response)
    }
    this.shortMessageCaptchaToken = response.data.token
    return response
  }

  // * 手机登录流程 --start

  /**
   * *手机登录-发送手机登录验证码
   * @param phone
   */
  async sendLogingSmsCode(phone: string, type: string) {
    const params = new SmsCodeApplyRequest()
    if (type === RoleTypeEnum.STUDENT) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.studentPhoneNumLoginToken)
    }
    if (type === RoleTypeEnum.ADMIN) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superPhoneNumLoginToken)
    }
    // if (type === 'provider') {
    //   params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.coursewareSupplierPhoneNumLoginToken)
    // }
    if (type === RoleTypeEnum.REGION) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionLoginToken)
    }
    if (type === RoleTypeEnum.OPERATION) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.sunProjectPhoneNumLoginToken)
    }
    params.businessType = 10000
    params.phone = phone
    const response = await basicDataDomain.applySmsCode(params)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('发送验证码失败', response)
      return Promise.reject(response)
    }
    this.shortMessageCaptchaTicket = response.data.token
    return response
  }

  /**
   * * 手机登录-手机验证码登录
   * @param params
   */
  async doPhoneLogin(params: ShortMessageLoginParams) {
    params.token = this.shortMessageCaptchaToken
    const result = await request(RequestMethod.POST, `${this.options.ssoUrl}${loginByPhoneUrl}`, {
      data: params
    })
    if (result.status.code !== 200) {
      return Promise.reject(result.status)
    }
    this.emit('login', true)
    this.storeTicket(result.data.serviceTicketId, result.data.ticketGrantTicketId)
    return result
  }

  // * 手机登录流程 --end

  // * 忘记密码 --start
  /**
   * * 忘记密码-验证身份
   * @param name
   * @param identity
   */
  async validIdentity(name: string, identity: string, type: string) {
    const params = new ValidIdentityRequest()
    if (type === RoleTypeEnum.STUDENT) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.studentForgotPasswordToken)
    }
    if (type === RoleTypeEnum.ADMIN) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superForgotPasswordToken)
    }
    if (type === RoleTypeEnum.PROVIDER) {
      // params.token = ConfigCenterModule.getFrontendApplication(
      //   frontendApplication.coursewareSupplierForgotPasswordToken
      // )
    }
    if (type === RoleTypeEnum.REGION) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionForgotPasswordToken)
    }
    if (type === RoleTypeEnum.OPERATION) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.sunProjectForgotPasswordToken)
    }
    if (type === RoleTypeEnum.GROUPADMIN) {
      params.token = ConfigCenterModule.getFrontendApplication(
        frontendApplication.collectiveRegistrationForgotPasswordToken
      )
    }
    params.name = name
    params.identity = identity
    params.businessType = 30000
    const response = await basicDataDomain.validIdentity(params)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('身份验证失败', response)
      return Promise.reject(response)
    }
    this.validIdentityToken = response.data.token
    return response
  }

  /**
   * * 忘记密码-获取图形验证码
   */
  async getForgetPasswordApplyCaptcha() {
    const params = new CaptchaApplyRequest()
    params.token = this.validIdentityToken
    params.businessType = 30000
    const response = await basicDataDomain.applyCaptcha(params)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('忘记密码获取图形验证码失败', response)
      return Promise.reject(response)
    }
    this.captchaTicket = response.data.token
    return response
  }

  /**
   * *忘记密码-发送手机验证码
   * @param phone
   */
  async sendForgetSmsCode(phone: string) {
    return await this.msSendSmsCode(phone, 30000)
  }

  /**
   * * 忘记密码-修改密码
   * @param newPassword
   */
  async modifyPassword(newPassword: string) {
    const params = new CurrentAccountChangePasswordCauseForgetRequest()
    params.token = this.shortMessageCaptchaToken
    params.password = newPassword
    const response = await basicDataDomain.forgetPassword(params)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('修改密码失败', response)
      return Promise.reject(response)
    }
    return response
  }
  // * 忘记密码 --end

  // * 换绑手机 --start
  /**
   * * 换绑手机-获取图形验证码
   * @param type
   */
  async getChangePhoneApplyCaptcha(type: string) {
    const params = new CaptchaApplyRequest()
    if (type === RoleTypeEnum.STUDENT) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.studentChangePhoneNumToken)
    }
    if (type === RoleTypeEnum.ADMIN) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.superChangePhoneNumToken)
    }
    if (type === RoleTypeEnum.PROVIDER) {
      // params.token = ConfigCenterModule.getFrontendApplication(
      //   frontendApplication.coursewareSupplierChangePhoneNumToken
      // )
    }
    if (type === RoleTypeEnum.REGION) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.regionChangePhoneNumToken)
    }
    if (type === RoleTypeEnum.OPERATION) {
      params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.sunProjectChangePhoneNumToken)
    }
    if (type === RoleTypeEnum.GROUPADMIN) {
      params.token = ConfigCenterModule.getFrontendApplication(
        frontendApplication.collectiveRegistrationChangePhoneNumToken
      )
    }
    params.businessType = 40000
    const response = await basicDataDomain.applyCaptcha(params)
    this.captchaTicket = response.data.token
    return response
  }

  /**
   * * 换绑手机-发送短信验证码
   * @param phone
   */
  async sendChangePhoneMsmCode(phone: string) {
    return await this.msSendSmsCode(phone, 40000)
  }
  /**
   * 换绑手机-集体报名帐号
   * @param phone 电话
   * @param smsCode 短信验证码
   */
  async updateUserBindingPhoneNumber(phone: string, smsCode: string) {
    const response = await BasicdataDomain.changeCollectiveRegisterPhone({
      newPhone: phone,
      smsCode,
      token: this.shortMessageCaptchaTicket
    })
    return response
  }
  /**
   * * 换绑手机-换班新手机student
   */
  async studentChangeNewPhone(oldToken: string, newToken: string) {
    const changePhoneRequest = new ChangePhoneRequest()
    changePhoneRequest.newPhoneToken = newToken
    changePhoneRequest.oldPhoneToken = oldToken
    const response = await basicDataDomain.changePhone(changePhoneRequest)
    return response
  }
  // /**
  //  * * 绑定手机student
  //  */
  // async studentBindPhoneForCurrentUser() {
  //   const token = this.shortMessageCaptchaToken
  //   const response = await basicDataDomain.bindPhoneForCurrentUser(token)
  //   return response
  // }
  /**
   * * 换绑手机-换班新手机
   */
  async changeNewPhone() {
    const token = this.shortMessageCaptchaToken
    const response = await basicDataDomain.changeNewPhone(token)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('换绑失败', response)
      return Promise.reject(response)
    }
    return response
  }
  // * 换绑手机 --end

  // * 绑定手机 --start
  async bindPhoneForCurrentUser() {
    const token = this.shortMessageCaptchaToken
    const response = await basicDataDomain.bindPhoneForCurrentUser(token)
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('绑定手机号', response)
      return Promise.reject(response)
    }
    if (response.data.code == '30001') {
      console.error('token错误', response.data.message)
      return Promise.reject(response)
    }
    return response
  }
  // * 绑定手机 --end

  /**
   * 获取accessToken
   */
  getAccessToken() {
    return this.getKey(Authentication.accessTokenKeyName)
  }

  /**
   * 获取refreshToken
   */
  getRefreshToken() {
    return this.getKey(Authentication.refreshTokenKeyName)
  }

  /**
   * 设置refreshToken
   * @param value
   */
  setRefreshToken(value: string) {
    this.setKey(Authentication.refreshTokenKeyName, value)
  }

  /**
   * 设置accessToken
   * @param value
   */
  setAccessToken(value: string) {
    this.setKey(Authentication.accessTokenKeyName, value)
  }

  getTicketGrantTicketId() {
    return this.getKey(Authentication.ticketGrantTicketIdKeyName)
  }
}

export default Authentication
