import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 性别
 */
enum GendersEnum {
  UN_KNOW = -1,
  GIRL = 0,
  BOY = 1
}

export { GendersEnum }
class GendersTypes extends AbstractEnum<GendersEnum> {
  static enum = GendersEnum

  constructor(status?: GendersEnum) {
    super()
    this.current = status
    this.map.set(GendersEnum.UN_KNOW, '未知')
    this.map.set(GendersEnum.BOY, '男')
    this.map.set(GendersEnum.GIRL, '女')
  }
}

export default new GendersTypes()
