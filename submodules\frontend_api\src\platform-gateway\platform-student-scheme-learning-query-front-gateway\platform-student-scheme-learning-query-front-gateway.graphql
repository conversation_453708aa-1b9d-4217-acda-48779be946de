schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""根据历史数据id获取历史学习数据
		@return
	"""
	getHistoryStudentTrainingInfoResponseByIdInMyself(id:String):HistoryStudentTrainingInfoResponse @optionalLogin
	"""根据用户id获取历史学习数据
		@return
	"""
	pageHistoryStudentTrainingInfoResponseInMyself(page:Page,sort:[HistoryStudentTrainingInfoSrotRequest]):HistoryStudentTrainingInfoResponsePage @page(for:"HistoryStudentTrainingInfoResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input HistoryStudentTrainingInfoSrotRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.sort.HistoryStudentTrainingInfoSrotRequest") {
	"""排序枚举"""
	historyStudentTrainingInfoSortEnum:HistoryStudentTrainingInfoSortEnum
	"""正序或倒序"""
	policy:Direction
}
enum Direction @type(value:"com.fjhb.commons.dao.page.SortPolicy$Direction") {
	ASC
	DESC
}
"""@description:课程排序枚举
	@author: sugs
	@create: 2022-03-14 09:28
"""
enum HistoryStudentTrainingInfoSortEnum @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.consts.HistoryStudentTrainingInfoSortEnum") {
	"""年度"""
	TRAIN_YEAR
}
"""历史学习档案响应"""
type HistoryStudentTrainingInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.HistoryStudentTrainingInfoResponse") {
	"""历史记录id"""
	id:String
	"""培训平台标识
		福建专技：fjzjkey
		福州人事：fjrskey
		厦门人才：xmzjkey
		福州建设专技：fzjszjkey
		厦门建设专技：xmjszjkey
		莆田专技：ptzjkey
		漳州开发：zzkdkey
		南平专技：npzjkey
		泉州提高：qztgkey
	"""
	trainPlatformKey:String
	"""用户姓名"""
	userName:String
	"""用户身份证"""
	userIdCard:String
	"""单位名称"""
	unitName:String
	"""培训班名称"""
	trainName:String
	"""培训年度"""
	trainYear:String
	"""培训学时"""
	trainPeriod:Double
	"""科目类型: 0公需、1专业、2公需+专业"""
	subjectType:Int
	"""考试成绩"""
	score:Double
	"""考试开始时间"""
	examStartTime:DateTime
	"""考试结束时间"""
	examEndTime:DateTime
	"""培训开始时间"""
	trainBeginTime:DateTime
	"""培训结束时间"""
	trainEndTime:DateTime
	"""培训合格时间"""
	qualifiedTime:DateTime
	"""培训内容集合（课程集合）"""
	trainingContentList:[TrainingContentResponse]
}
type TrainingContentResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.nested.TrainingContentResponse") {
	"""课程名称"""
	courseName:String
	"""课程学时"""
	period:Double
	"""开始学习时间"""
	startLearningTime:DateTime
	"""结束学习时间"""
	endLearningTime:DateTime
	"""课件信息"""
	trainingCoursewareList:[TrainingCoursewareResponse]
}
type TrainingCoursewareResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.nested.TrainingCoursewareResponse") {
	"""课件名称"""
	coursewareName:String
}

scalar List
type HistoryStudentTrainingInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [HistoryStudentTrainingInfoResponse]}
