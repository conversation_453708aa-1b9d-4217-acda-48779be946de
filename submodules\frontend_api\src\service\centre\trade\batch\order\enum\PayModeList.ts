import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description
 */
export enum PayModeEnum {
  // 1：线上支付
  ONLINE = 1,
  // 2：线下支付
  OFFLINE
}

/**
 * @description
 */
class PayModeList extends AbstractEnum<PayModeEnum> {
  static enum = PayModeEnum
  constructor(status?: PayModeEnum) {
    super()
    this.current = status
    this.map.set(PayModeEnum.ONLINE, '线上支付')
    this.map.set(PayModeEnum.OFFLINE, '线下支付')
  }
}

export default new PayModeList()
