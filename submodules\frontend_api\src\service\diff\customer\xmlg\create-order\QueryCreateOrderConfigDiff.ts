import QueryCreateOrderConfig from '@api/service/customer/trade/single/query/query-create-order-config/QueryCreateOrderConfig'
import CreateOrderMethods from '@api/service/diff/customer/xmlg/create-order/CreateOrder'
// 加载创建订单/支付订单需要加载的配置 如培训券 发票 商品信息等
export default class QueryCreateOrderConfigDiff extends QueryCreateOrderConfig {
  /**
   * @description: 拿到创建订单对象
   */
  getCreateOrderAction() {
    return new CreateOrderMethods()
  }
}
