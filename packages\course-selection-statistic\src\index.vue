<route-meta>
  {
  "isMenu": true,
  "title": "选课统计",
  "sort": 8,
  "icon": "icon-ribaotongji"
  }
</route-meta>

<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="created,doSearch">
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
        <hb-search-wrapper @reset="resetPageQuery" class="m-query is-border-bottom">
          <el-form-item label="课件供应商" v-if="!isServerProvider">
            <biz-courseware-supplier v-model="provriderId" placeholder="全部"></biz-courseware-supplier>
          </el-form-item>
          <el-form-item label="技术等级">
            <biz-technical-grade-select v-model="technicalGradeId"></biz-technical-grade-select>
          </el-form-item>
          <el-form-item label="课程名称">
            <el-input v-model="pageQueryParam.courseName" clearable placeholder="请输入课程名称" />
          </el-form-item>
          <el-form-item label="查询时间">
            <el-date-picker
              v-model="pageQueryTime"
              type="datetimerange"
              range-separator="至"
              start-placeholder="起始时间"
              end-placeholder="结束时间"
              value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="doSearch">查询</el-button>
            <el-button @click="batchExport" v-if="$hasPermission('batchexport')" desc="批量导出" actions="batchExport"
              >批量导出</el-button
            >
            <el-button
              @click="batchExportGys"
              v-if="$hasPermission('batchExportGys')"
              desc="批量导出"
              actions="batchExportGys"
              >批量导出</el-button
            >
          </template>
        </hb-search-wrapper>
        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert f-clear">
            <div class="f-c6 f-fl">
              搜索结果合计：技术等级：{{ technicalGradeName ? technicalGradeName : '全部' }} ，累计选课人次：<span
                class="f-fb f-co"
                >{{
                  chooseCourseStatisticsInfoTotal.sumChooseCourseCount
                    ? chooseCourseStatisticsInfoTotal.sumChooseCourseCount
                    : 0
                }}</span
              >，累计退课人次：<span class="f-fb f-co">{{
                chooseCourseStatisticsInfoTotal.totalWithdrawalCount
                  ? chooseCourseStatisticsInfoTotal.totalWithdrawalCount
                  : 0
              }}</span
              >， 净选课人次：<span class="f-fb f-co">{{
                chooseCourseStatisticsInfoTotal.totalChooseCourseCount
                  ? chooseCourseStatisticsInfoTotal.totalChooseCourseCount
                  : 0
              }}</span>
            </div>
            <div class="f-fr f-ml20 f-c6">单位：人次</div>
            <div class="f-fr f-csp f-flex f-align-center" @click="tipdialog = true">
              <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
            </div>
          </el-alert>
        </div>
        <!--表格-->
        <el-table
          stripe
          :data="getChooseCourseStatisticList"
          border
          max-height="500px"
          class="m-table is-statistical f-mt10"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="课程名称" min-width="200" fixed="left">
            <template slot-scope="scope">{{ scope.row.courseName }}</template>
          </el-table-column>
          <el-table-column label="课件供应商" min-width="180">
            <template slot-scope="scope">{{ scope.row.providerName }}</template>
          </el-table-column>
          <el-table-column label="技术等级" min-width="100" align="center">
            <template slot-scope="scope">{{ scope.row.technicalGradeName }}</template>
          </el-table-column>
          <el-table-column label="选课学时" min-width="90" align="center">
            <template slot-scope="scope">{{ scope.row.period }}</template>
          </el-table-column>
          <el-table-column label="综合评分" min-width="90" align="center">
            <template slot-scope="scope">{{ (scope.row.totalAppraise / 100).toFixed(1) }}</template>
          </el-table-column>
          <el-table-column label="累计选课人次" min-width="120" align="right">
            <template slot-scope="scope">{{ scope.row.sumChooseCourseCount }}</template>
          </el-table-column>
          <el-table-column label="累计退课人次" min-width="120" align="right">
            <template slot-scope="scope">{{ scope.row.totalWithdrawalCount }}</template>
          </el-table-column>
          <el-table-column label="净选课人次" min-width="120" align="right">
            <template slot-scope="scope">{{ scope.row.totalChooseCourseCount }}</template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                @click="goToEval(scope.row.courseId)"
                v-if="$hasPermission('detail')"
                desc="查看课程评价"
                actions="@hbfe/jxjy-admin-course/src/detail.vue"
                >查看课程评价</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </el-card>
    </div>
    <!-- 统计口径说明弹窗 -->

    <el-drawer title="统计口径说明" :visible.sync="tipdialog" size="900px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
        </el-alert>
        <p class="f-mt20 f-mb10">
          <span class="f-fb f-f15">搜索条件说明</span>
          （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
        </p>
        <el-table stripe :data="tableData4" border class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="字段" width="150">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">课件供应商</div>
              <div v-else-if="scope.$index === 1">技术等级</div>
              <div v-else-if="scope.$index === 2">课程名称</div>
              <div v-else>查询时间</div>
            </template>
          </el-table-column>
          <el-table-column label="详细说明" min-width="300">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">支持按课件供应商查询旗下所有课程选课记录</div>
              <div v-else-if="scope.$index === 1">支持按照技术等级查询</div>
              <div v-else-if="scope.$index === 2">支持按课程名称模糊查询</div>
              <div v-else>查询日期段内的选课数据</div>
            </template>
          </el-table-column>
        </el-table>
        <p class="f-mt20 f-mb10">
          <span class="f-fb f-f15">列表字段及详细说明</span>
          （列表下的数据显示受搜索条件的约束，统计单位：人次）
        </p>
        <el-table stripe :data="tableData7" border class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="字段" width="150">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">课程名称</div>
              <div v-else-if="scope.$index === 1">课件供应商</div>
              <div v-else-if="scope.$index === 2">选课学时</div>
              <div v-else-if="scope.$index === 3">综合评分</div>
              <div v-else-if="scope.$index === 4">累计选课人次</div>
              <div v-else-if="scope.$index === 5">累计退课人次</div>
              <div v-else>净选课人次</div>
            </template>
          </el-table-column>
          <el-table-column label="详细说明" min-width="300">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">按照课程的名称进行查询</div>
              <div v-else-if="scope.$index === 1">培训方案名称，默认显示全部已发布的方案</div>
              <div v-else-if="scope.$index === 2">展示课程的选课学时信息</div>
              <div v-else-if="scope.$index === 3">显示课程的评分数</div>
              <div v-else-if="scope.$index === 4">在查询日期段内，选课成功的累积人次</div>
              <div v-else-if="scope.$index === 5">在查询日期段内，选课成功的累积人次</div>
              <div v-else>净选课=累积选课人次-累计退课人次</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
    <!-- 导出任务查看 -->
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：数据统计-导出任务查看，查询条件选择选课统计！</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button v-if="!isGys" type="primary" @click="goDownloadPage">前往下载</el-button>
        <el-button v-else type="primary" @click="goDownloadPageGys">前往下载</el-button>
      </div>
    </el-dialog>
  </el-main>
</template>
<script lang="ts">
  import { Component, Watch, Vue } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import {
    ChooseCourseStatisticsInfoTotal,
    ChooseCourseStatisticsRequest
  } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
  import ChooseCourseDetail from '@api/service/management/statisticalReport/query/vo/ChooseCourseDetail'
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'
  import { ChooseCourseStatisticsParams } from '@api/service/management/statisticalReport/query/QueryChooseCourseStatistic'
  import { TechnologyLevelVo } from '@api/service/common/basic-data-dictionary/query/QueryTechnologyLevel'
  import QueryTechnologyLevel from '@api/service/common/basic-data-dictionary/QueryBasicdataDictionaryFactory'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import UserModule from '@api/service/management/user/UserModule'

  @Component
  export default class extends Vue {
    //选课统计
    getQueryChooseCourseStatisticList =
      StaticticalReportManagerModule.queryStaticticalReportFactory.getQueryChooseCourseStatisticList()
    query: Query = new Query()
    // 分页
    page = new UiPage()
    // 课件供应商id列表
    provriderId: Array<string> = new Array<string>()
    // 查询条件
    // pageQueryParam =   new ChooseCourseStatisticsRequest()
    pageQueryParam = new ChooseCourseStatisticsParams()
    pageQueryTime = [] as any
    // table表格
    getChooseCourseStatisticList: Array<ChooseCourseDetail> = Array<ChooseCourseDetail>()
    // 总数统计
    chooseCourseStatisticsInfoTotal = new ChooseCourseStatisticsInfoTotal()
    isGys = false
    technicalGradeId = ''
    // 说明弹窗
    tipdialog = false
    tableData4 = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }]
    tableData7 = [
      { field101: '1' },
      { field101: '2' },
      { field101: '3' },
      { field101: '4' },
      { field101: '5' },
      { field101: '2' },
      { field101: '2' }
    ]
    // 导出任务查看
    exportSuccessVisible = false
    // 课件供应商角色列表
    getQueryCurrentUserRoleList = AuthorityModule.roleFactory.getQueryCurrentUserRoleList()
    // 供应商
    queryServiceProvider = AuthorityModule.serviceProviderFactory.queryServiceProvider
    isServerProvider = false
    serviceId = ''
    // 技术等级展示
    technicalGradeName = ''
    technologyOptions: Array<TechnologyLevelVo> = new Array<TechnologyLevelVo>()
    @Watch('provriderId')
    changeProvriderId() {
      if (this.provriderId?.length) {
        this.pageQueryParam.supplierId = [this.provriderId[this.provriderId.length - 1]]
      } else {
        this.pageQueryParam.supplierId = []
      }
    }
    @Watch('pageQueryTime')
    changePageQueryTime() {
      if (this.pageQueryTime?.length) {
        // 开始时间
        this.pageQueryParam.chooseCourseDateStart = this.pageQueryTime[0]
        // 结束时间
        this.pageQueryParam.chooseCourseDateEnd = this.pageQueryTime[1]
      } else {
        // 开始时间
        this.pageQueryParam.chooseCourseDateStart = ''
        // 结束时间
        this.pageQueryParam.chooseCourseDateEnd = ''
      }
    }
    @Watch('technicalGradeId')
    changeTechnicalGradeId() {
      if (this.technicalGradeId?.length) {
        this.pageQueryParam.technicalGrade = [this.technicalGradeId]
      } else {
        this.pageQueryParam.technicalGrade = []
      }
    }
    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    async created() {
      await this.getQueryCurrentUserRoleList.getCurrentUserRoleList()
      const roleList = this.getQueryCurrentUserRoleList.roleList
      if (roleList.length && roleList.length > 0) {
        const flag = roleList.find((item) => item.category === 560)
        this.isServerProvider = !!flag
      }

      if (this.isServerProvider) {
        const res = await this.queryServiceProvider.serchProviderByUserId(
          UserModule.queryUserFactory.queryManagerDetail.adminInfo.userInfo.userId
        )
        this.serviceId = res.coursewareId
      }
      const queryTechnologyLevel = new QueryTechnologyLevel()
      const data = await queryTechnologyLevel.queryTechnologyLevel.query()
      this.technologyOptions = data
      if (this.$route.query.coursewareSupplierId) {
        this.provriderId = [this.$route.query.coursewareSupplierId as string]
        this.pageQueryParam.supplierId = [this.provriderId[this.provriderId.length - 1]]
      }
      await this.doSearch()
    }

    /**
     * 批量导出任务
     */
    async batchExport() {
      //是否为课件供应商
      this.isGys = false
      const res = await this.getQueryChooseCourseStatisticList.exportChooseCourseStatisticList(this.pageQueryParam)
      if (res) {
        this.exportSuccessVisible = true
      }
    }
    /**
     * 批量导出任务 课件供应商
     */
    async batchExportGys() {
      this.isGys = true
      const res = await this.getQueryChooseCourseStatisticList.exportChooseCourseStatisticList(this.pageQueryParam)
      if (res) {
        this.exportSuccessVisible = true
      }
    }
    /**
     * 查询table数据
     */
    async doSearch() {
      this.query.loading = true
      try {
        // if (this.pageQueryTime?.length) {
        //   // 开始时间
        //   this.pageQueryParam.chooseCourseDateStart = this.pageQueryTime[0]
        //   // 结束时间
        //   this.pageQueryParam.chooseCourseDateEnd = this.pageQueryTime[1]
        // } else {
        //   // 开始时间
        //   this.pageQueryParam.chooseCourseDateStart = ''
        //   // 结束时间
        //   this.pageQueryParam.chooseCourseDateEnd = ''
        // }
        // if (this.provriderId?.length) {
        //   this.pageQueryParam.supplierId = [this.provriderId[this.provriderId.length - 1]]
        // } else {
        //   this.pageQueryParam.supplierId = []
        // }
        if (this.isServerProvider) {
          this.pageQueryParam.supplierId = [this.serviceId]
        } else if (this.provriderId?.length) {
          this.pageQueryParam.supplierId = [this.provriderId[this.provriderId.length - 1]]
        }
        this.getChooseCourseStatisticList = await this.getQueryChooseCourseStatisticList.getChooseCourseStatisticList(
          this.page,
          this.pageQueryParam
        )
        this.technicalGradeName =
          this.technologyOptions[this.technologyOptions.findIndex((e) => e.id == this.technicalGradeId)]?.showName ||
          '全部'
        this.chooseCourseStatisticsInfoTotal = this.getQueryChooseCourseStatisticList.chooseCourseStatisticsInfoTotal
        // console.log(this.getChooseCourseStatisticList, this.chooseCourseStatisticsInfoTotal, '11111111111111111111')
      } catch (e) {
        console.log(e)
        // nothing
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['courseSalesTable'] as any)?.doLayout()
        this.query.loading = false
      }
    }
    /**
     * 前往查看课程评价
     */
    goToEval(courseId: string) {
      //console.log(courseId)
      this.$router.push({
        path: `/resource/course/detail/${courseId}`,
        query: {
          detail: 'evaluation'
        }
      })
      //
    }
    /**
     * 重置查询条件
     */
    async resetPageQuery() {
      this.provriderId = []
      this.pageQueryTime = []
      this.technicalGradeId = ''
      for (const key in this.pageQueryParam) {
        if (typeof this.pageQueryParam[key] === 'string') {
          this.pageQueryParam[key] = ''
        }
        if (typeof this.pageQueryParam[key] === 'object') {
          this.pageQueryParam[key] = []
        }
      }
      await this.doSearch()
    }
    /**
     * 导出任务下载查看
     */
    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportChooseCourseStatistical' }
      })
    }
    /**
     * 导出任务下载查看课件供应商
     */
    goDownloadPageGys() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/statistic/export-task',
        query: { type: 'exportChooseCourseStatistical' }
      })
    }
  }
</script>
