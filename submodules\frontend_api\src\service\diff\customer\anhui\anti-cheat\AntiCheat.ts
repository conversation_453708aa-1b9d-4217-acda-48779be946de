import AntiCheatInitModel from '@api/service/diff/customer/anhui/anti-cheat/models/AntiCheatInitModel'
import PlatformJxjypxtyptAhzjSchool, {
  GetVerificationCodeForAHZJRequest,
  SendCodeAnswerForAHZJRequest
} from '@api/platform-gateway/platform-jxjypxtypt-ahzj-school'
import { ResponseStatus } from '@hbfe/common'
import { AntiErrorCodeEnum } from '@api/service/diff/customer/anhui/anti-cheat/enums/AntiErrorCodeEnum'
/**
 * 安徽防作弊
 */
export default class AntiCheat {
  /**
   * 基础参数
   */
  param: AntiCheatInitModel

  /**
   * 当前剩余监管次数（-1表示不限制）
   */
  currentValidateNum: number = undefined

  /**
   * 验证码图片
   */
  validateCodePic: string = undefined

  /**
   * 是否处在校验中
   */
  private _checking = false

  /**
   * 上一次监管点时间
   */
  private _prePointTime: number = undefined

  /**
   * 所需监管次数
   */
  private _checkNum = 0

  /**
   * 监管点列表
   */
  private _checkPoint: Array<number> = new Array<number>()

  /**
   * 历史监管点（已被后端记录的监管点）
   */
  private _historyPoint: Array<number> = new Array<number>()

  /**
   * 当前验证码codeID
   */
  private _codeId: string = undefined

  /**
   * 当前时间点
   */
  private _currentTime: number = undefined

  /**
   * 播放key
   */
  private _configKey = ''

  /**
   * 是否需要监管
   */
  get needAntiCheat() {
    return !!this._checkNum && this._checkNum > 0
  }

  /**
   * 邻近的监管点
   */
  get nearCheckPoint() {
    return (this._checkPoint.length && this._checkPoint[0]) || null
  }

  /**
   * 是否处在校验中
   */
  get checking() {
    return this._checking
  }

  constructor(param: AntiCheatInitModel) {
    this.param = param
  }

  /**
   * 初始化
   */
  async init() {
    if (this.param.schedule === 100) {
      this._checkNum = 0
      this._checkPoint = []
      return
    }

    // 初始化配置
    await this.getAntiConfig()

    //生成监管点
    await this.generateCheckPoint()
  }

  /**
   * 校验当前时间点是否需要监管 返回值为true表示需要
   * @param time
   */
  playingCheckNeedAnti(time: number): boolean {
    if (!this._checking) {
      this._currentTime = time
    }
    if (this.needAntiCheat && this._checkPoint.includes(this._currentTime)) {
      this._checking = true
      return true
    } else {
      this._checking = false
      return false
    }
  }

  /**
   * 获取监管配置
   */
  async getAntiConfig(): Promise<ResponseStatus> {
    const { data, status } = await PlatformJxjypxtyptAhzjSchool.getTrainSupervisionForAHZJ(this.param.to())
    if (status.isSuccess() && data.code == '200') {
      this._checkNum = data.antiCheatConfigDto.allVerifyCount
      this._configKey = data.antiCheatConfigDto.configKey
      // 该课程所有有验证过的课程播放时间点，如果没有验证过的时间点，则返回空数组
      if (data?.antiCheatConfigDto?.coursePlayTimeList?.length) {
        data.antiCheatConfigDto.coursePlayTimeList.map(item => {
          this._historyPoint.push(item.playTime)
          if (item.isVerify) {
            this._checkNum--
          } else {
            this._prePointTime = item.playTime
          }
        })
      }
      return Promise.resolve(new ResponseStatus(200, ''))
    }
    return Promise.reject(new ResponseStatus(AntiErrorCodeEnum.getConfigError, status.getMessage() || data.message))
  }

  /**
   * 生成验证码
   */
  async generateValidateCode(): Promise<ResponseStatus> {
    const request = new GetVerificationCodeForAHZJRequest()
    request.configKey = this._configKey
    // 存在上一次的时间点用上一次，没有则使用当前
    request.playTime = this._prePointTime || this._currentTime
    const { data, status } = await PlatformJxjypxtyptAhzjSchool.getVerificationCodeForAHZJ(request)
    if (status.isSuccess() && data.code == '200') {
      this._codeId = data.antiCheatCodeDto.id
      this.currentValidateNum = data.antiCheatCodeDto.remainAnswerCount
      console.log(this.currentValidateNum, '剩余次数')
      this.validateCodePic = 'data:image/png;base64,' + data.antiCheatCodeDto.content
      return Promise.resolve(new ResponseStatus(200, ''))
    }
    return Promise.reject(
      new ResponseStatus(AntiErrorCodeEnum.generateCodePicError, status.getMessage() || data.message)
    )
  }

  /**
   * 提交验证码结果
   * @param result 验证码结果
   */
  async submitValidateResults(result: string): Promise<boolean> {
    if (this.currentValidateNum === 0) {
      return Promise.reject(new ResponseStatus(AntiErrorCodeEnum.submitCodeAnswerError, '没有验证次数了'))
    }
    const request = new SendCodeAnswerForAHZJRequest()
    request.codeId = this._codeId
    request.codeAnswer = result
    const { data, status } = await PlatformJxjypxtyptAhzjSchool.sendCodeAnswerForAHZJ(request)

    if (status.isSuccess() && data.code == '200') {
      if (data.antiCheatCodeAnswerPushResult.verifyResult === 1) {
        // 监管通过移除当前邻近监管点
        this._checkNum--
        this._checkPoint.shift()
        // 如果存在上一次的点 因为已经校验完了 所以将上一次的点置空
        if (this._prePointTime) {
          this._prePointTime = null
        }
        this._checking = false
      } else {
        if (this.currentValidateNum > 0) {
          if (this.currentValidateNum - 1 === 0) {
            console.log(this.currentValidateNum, '没有次数了')
            // 监管不通过用完次数移除当前邻近监管点
            this.currentValidateNum--
            this._checkNum--
            this._checkPoint.shift()
            // 如果存在上一次的点 因为已经校验完了 所以将上一次的点置空
            if (this._prePointTime) {
              this._prePointTime = null
            }
            this._checking = false
          } else {
            // 有次数则刷新验证码
            try {
              await this.generateValidateCode()
            } catch (e) {
              return Promise.reject(e)
            }
          }
        } else {
          // 无限次数
          try {
            await this.generateValidateCode()
          } catch (e) {
            return Promise.reject(e)
          }
        }
      }
      return data.antiCheatCodeAnswerPushResult.verifyResult === 1
    }
    return Promise.reject(
      new ResponseStatus(AntiErrorCodeEnum.submitCodeAnswerError, status.getMessage() || data.message)
    )
  }

  /**
   * 生成监管点
   */
  private async generateCheckPoint(): Promise<ResponseStatus> {
    this._checkPoint = []
    // 加一减一掐头去尾
    const minTime = Math.floor(this.param.initPlayTime) + 1
    const maxTime = Math.floor(this.param.courseTimeLength) - 1
    const range = maxTime - minTime // 生成点数量

    // 允许生成的点数量
    let allowPointNum = range
    this._historyPoint.map(item => {
      // 若历史监管点处于当前生成点区间，则允许生成点数量减少
      if (item >= minTime && item <= maxTime) {
        allowPointNum--
      }
    })

    if (allowPointNum < 1) {
      // return Promise.reject(new ResponseStatus(AntiErrorCodeEnum.generatePointError, '监管点初始化失败'))
      // 当没有生成监管点的范围时，先这么处理（不抛错，也不生成）
      return Promise.resolve(new ResponseStatus(200, ''))
    }

    let repeatFlag = false // 可重复标识
    if (allowPointNum < this._checkNum) {
      // 如果剩余秒数不够次数去分配，则让监管点落点可重复
      repeatFlag = true
    }
    for (let i = 0; i < this._checkNum; i++) {
      let randomNumber
      do {
        randomNumber = this.getRandomNumber(minTime, maxTime)
      } while ((!repeatFlag && this._checkPoint.includes(randomNumber)) || this._historyPoint.includes(randomNumber))

      this._checkPoint.push(randomNumber)
    }
    this._checkPoint = [...new Set(this._checkPoint)]
    this._checkPoint.sort((a, b) => {
      return a - b
    })

    return Promise.resolve(new ResponseStatus(200, ''))
  }

  /**
   * 取min与max之间的随机数
   * @param min
   * @param max
   */
  private getRandomNumber(min: number, max: number) {
    return Math.floor(Math.random() * (max - min + 1)) + min
  }
}
