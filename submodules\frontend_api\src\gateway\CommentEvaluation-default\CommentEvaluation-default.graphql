schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询指定评论下所有回复的详细信息
		@param page  分页信息
		@param comId 评论编号
		@param query 查询条件，可为空
		@return 回复分页列表
	"""
	getReversionDetailPage(page:Page,comId:String,query:ReversionDetailQueryRequest):ReversionDetailResponsePage @page(for:"ReversionDetailResponse")
	"""查询指定实体编号的评论列表
		@param page     分页信息
		@param entityId 实体编号
		@return 评论分页信息
	"""
	getSimpleCommentPage(page:Page,entityId:String):CommentResponsePage @page(for:"CommentResponse")
	"""加载评论信息
		@param id 评论编号
		@return 评论信息
	"""
	loadCommentary(id:String):CommentResponse
	"""获取课程评价信息列表
		@param courseIds 课程ID集合
	"""
	loadCourseAppraisalList(courseIds:[String]):[CourseAppraisalResponse] @NotAuthenticationRequired
	"""获取课程的评价记录信息
		@param pageIndex 当前页码
		@param pageSize 分页大小
		@param courseId 课程ID
	"""
	loadCourseAppraisalRecordList(pageIndex:Int!,pageSize:Int!,courseId:String):CourseAppraisalRecordResponsePage @page(for:"CourseAppraisalRecordResponse") @NotAuthenticationRequired
	"""获取课程的评价记录信息
		@param pageIndex    当前页码
		@param pageSize     分页大小
		@param courseIdList 课程ID集合
		@param shield       屏蔽状态，-1/0/1，全部/未屏蔽/已屏蔽，可为空，为空时默认-1
	"""
	loadCourseAppraisalRecordListByIdList(pageIndex:Int!,pageSize:Int!,courseIdList:[String],shield:Int):CourseAppraisalRecordResponsePage @page(for:"CourseAppraisalRecordResponse") @NotAuthenticationRequired
	"""获取课程的未屏蔽评价记录信息
		@param pageIndex 当前页码
		@param pageSize  分页大小
		@param courseId  课程ID
	"""
	loadCourseAppraisalUnShieldRecordList(pageIndex:Int!,pageSize:Int!,courseId:String):CourseAppraisalRecordResponsePage @page(for:"CourseAppraisalRecordResponse")
	"""获取截止上一个月月末网校评价信息列表
		@param schoolIds 网校ID集合
	"""
	loadLastMonthNetSchoolAppraisalList(schoolIds:[String]):[NetSchoolAppraisalResponse]
	"""获取网校评价信息列表
		@param schoolIds 网校ID集合
	"""
	loadNetSchoolAppraisalList(schoolIds:[String]):[NetSchoolAppraisalResponse]
	"""获取网校的评价记录信息
		@param pageIndex 当前页码
		@param pageSize  分页大小
		@param schoolId  网校ID
	"""
	loadNetSchoolAppraisalRecordList(pageIndex:Int!,pageSize:Int!,schoolId:String):NetSchoolAppraisalRecordResponsePage @page(for:"NetSchoolAppraisalRecordResponse")
	"""加载回复信息
		@param id 评论编号
		@return 评论信息
	"""
	loadReversion(id:String):ReversionResponse
	"""获取指定用户指定课程的星级评价
		@param courseId 课程ID
	"""
	loadUserCourseAppraisal(courseId:String):CourseUserAppraisalResponse
	"""获取指定用户指定网校星级评价
		@param schoolId 网校Id
	"""
	loadUserNetSchoolAppraisal(schoolId:String):NetSchoolAppraisalResponse
}
type Mutation {
	"""创建评论
		@param commentCreateInfo 评论信息
		@return 评论信息
	"""
	addCommentary(commentCreateInfo:CommentCreateRequest):CommentResponse
	"""创建回复
		@param createDTO 回复信息
		@return 回复信息
	"""
	addReversion(createDTO:ReversionCreateRequest):ReversionResponse
	"""评价一个课程
		@param createInfo 评价信息
	"""
	appraise(createInfo:CourseAppraisalCreateRequest):CourseUserAppraisalResponse
	"""评价一个网校
		@param createInfo 评价信息
	"""
	appraiseNetSchool(createInfo:NetSchoolAppraisalCreateRequest):NetSchoolUserAppraisalResponse
	"""审核评论不通过
		@param id 评论编号
		@return 评论信息
	"""
	auditCommentaryFail(id:String):CommentResponse
	"""审核评论通过
		@param id 评论编号
		@return 评论信息
	"""
	auditCommentaryPass(id:String):CommentResponse
	"""取消屏蔽用户对课程的评价
		@param request 信息
	"""
	cancelShieldCourseUserAppraisal(request:CancelShieldUserCourseAppraisalRequest):Void
	"""批量删除评论，同时删除评论下的回复
		@param comIdList 评论编号列表
	"""
	deleteBatchCommentary(comIdList:[String]):Void
	"""删除评论
		@param id 评论编号
	"""
	deleteCommentary(id:String):Void
	"""删除回复
		@param id 回复编号
	"""
	deleteReversion(id:String):Void
	"""批量删除回复
		@param comId 评论编号
	"""
	deleteReversionByComId(comId:String):Void
	"""置顶评论
		@param id 评论编号
		@return 评论信息
	"""
	pinCommentary(id:String):CommentResponse
	"""置顶回复
		@param id 回复编号
		@return 回复信息
	"""
	pinReversion(id:String):ReversionResponse
	"""屏蔽评论
		@param id 评论编号
		@return 评论信息
	"""
	shieldCommentary(id:String):CommentResponse
	"""屏蔽用户对课程的评价
		@param request 信息
	"""
	shieldCourseUserAppraisal(request:ShieldUserCourseAppraisalRequest):Void
	"""屏蔽回复
		@param id 回复编号
		@return 回复信息
	"""
	shieldReversion(id:String):ReversionResponse
	"""取消评论置顶
		@param id 评论编号
		@return 评论信息
	"""
	unPinCommentary(id:String):CommentResponse
	"""取消回复置顶
		@param id 回复编号
		@return 回复信息
	"""
	unPinReversion(id:String):ReversionResponse
	"""取消屏蔽评论
		@param id 评论编号
		@return 评论信息
	"""
	unshieldedCommentary(id:String):CommentResponse
	"""取消屏蔽回复
		@param id 回复编号
		@return 回复信息
	"""
	unshieldedReversion(id:String):ReversionResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""取消屏蔽用户对课程的评价
	<AUTHOR>
	@since 2021/4/22
"""
input CancelShieldUserCourseAppraisalRequest @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.request.CancelShieldUserCourseAppraisalRequest") {
	"""课程编号"""
	courseId:String!
	"""评价人编号"""
	userId:String!
}
"""评论创建信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
input CommentCreateRequest @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.request.CommentCreateRequest") {
	"""业务实体编号"""
	entityId:String!
	"""业务实体名称"""
	entityName:String
	"""评论用户"""
	usrId:String
	"""评论标题"""
	title:String
	"""评论内容"""
	content:String
	"""评论特征标记列表"""
	markers:[MarkerRequest1]
}
"""新增课程评价信息"""
input CourseAppraisalCreateRequest @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.request.CourseAppraisalCreateRequest") {
	"""课程ID"""
	courseId:String
	"""课程名称"""
	courseName:String
	"""教学水平评价星数，1~5星"""
	teachingQualityStars:Double!
	"""内容质量评价星数，1~5星"""
	contentQualityStars:Double!
	"""评价内容"""
	contents:String
}
"""特征标记信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
input MarkerRequest1 @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.request.MarkerRequest") {
	key:String
	value:String
}
"""新增网校评价信息"""
input NetSchoolAppraisalCreateRequest @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.request.NetSchoolAppraisalCreateRequest") {
	"""网校ID"""
	schoolId:String
	"""网校名称"""
	schoolName:String
	"""服务满意度评价星数，0~10"""
	serviceSatisfactionStars:Double!
	"""内容质量评价星数，0~10"""
	contentQualityStars:Double!
	"""评价内容"""
	contents:String
}
"""评论回复信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
input ReversionCreateRequest @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.request.ReversionCreateRequest") {
	"""评论编号"""
	comId:String
	"""回复内容"""
	content:String
	"""被回复用户编号"""
	targetUserId:String
	"""回复人编号"""
	usrId:String
	"""被回复的回复编号"""
	parentId:String
	"""特征标记列表"""
	markers:[MarkerRequest1]
}
"""回复详细信息查询条件
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
input ReversionDetailQueryRequest @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.request.ReversionDetailQueryRequest") {
	"""被回复的回复编号"""
	parentId:String
	"""审核状态，-1/0/1/2，全部/未审核/审核通过/审核不通过"""
	audit:Int!
	"""屏蔽状态，-1/0/1，全部/未屏蔽/已屏蔽"""
	shield:Int!
}
"""屏蔽用户对课程的评价
	<AUTHOR>
	@since 2021/4/22
"""
input ShieldUserCourseAppraisalRequest @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.request.ShieldUserCourseAppraisalRequest") {
	"""课程编号"""
	courseId:String!
	"""评价人编号"""
	userId:String!
}
"""特征标记信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type MarkerRequest @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.request.MarkerRequest") {
	key:String
	value:String
}
"""评论信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type CommentResponse @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.response.CommentResponse") {
	"""评论编号"""
	comId:String
	"""评论用户"""
	usrId:String
	"""评论用户名称"""
	userName:String
	"""评论标题"""
	title:String
	"""评论内容"""
	content:String
	"""审核状态，0/1/2，未审核/审核通过/审核不通过"""
	audit:Int!
	"""置顶状态，0/1，不置顶/置顶"""
	pin:Int!
	"""屏蔽状态，0/1，不屏蔽/屏蔽"""
	shield:Int!
	"""特征标记列表"""
	markers:[MarkerRequest]
}
"""课程评价记录"""
type CourseAppraisalRecordResponse @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.response.CourseAppraisalRecordResponse") {
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""用户ID"""
	userId:String
	"""课程ID"""
	courseId:String
	"""综合水平评价星数，0~10"""
	comprehensiveStars:Double!
	"""教学水平评价星数，0~10"""
	teachingQualityStars:Double!
	"""内容质量评价星数，0~10"""
	contentQualityStars:Double!
	"""评价内容"""
	contents:String
	"""评价时间"""
	createTime:DateTime
	"""是否屏蔽"""
	shield:Boolean!
}
type CourseAppraisalResponse @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.response.CourseAppraisalResponse") {
	"""课程ID"""
	courseId:String
	"""综合水平评价星数，0~10"""
	comprehensiveStars:Double!
	"""教学水平评价星数，0~10"""
	teachingQualityStars:Double!
	"""内容质量评价星数，0~10"""
	contentQualityStars:Double!
}
"""课程评价信息"""
type CourseUserAppraisalResponse @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.response.CourseUserAppraisalResponse") {
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""用户ID"""
	userId:String
	"""课程ID"""
	courseId:String
	"""课程名称"""
	courseName:String
	"""教学水平评价星数，0~10"""
	teachingQualityStars:Double!
	"""内容质量评价星数，0~10"""
	contentQualityStars:Double!
	"""评价内容"""
	contents:String
	"""评价时间"""
	createTime:DateTime
}
"""网校评价记录"""
type NetSchoolAppraisalRecordResponse @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.response.NetSchoolAppraisalRecordResponse") {
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""用户ID"""
	userId:String
	"""网校ID"""
	schoolId:String
	"""综合水平评价星数，0~10"""
	comprehensiveStars:Double!
	"""服务满意度评价星数，0~10"""
	serviceSatisfactionStars:Double!
	"""内容质量评价星数，0~10"""
	contentQualityStars:Double!
	"""评价内容"""
	contents:String
	"""评价时间"""
	createTime:DateTime
}
type NetSchoolAppraisalResponse @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.response.NetSchoolAppraisalResponse") {
	"""网校ID"""
	schoolId:String
	"""综合水平评价星数，0~10"""
	comprehensiveStars:Double!
	"""服务满意度评价星数，0~10"""
	serviceSatisfactionStars:Double!
	"""内容质量评价星数，0~10"""
	contentQualityStars:Double!
}
"""网校评价信息"""
type NetSchoolUserAppraisalResponse @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.response.NetSchoolUserAppraisalResponse") {
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""用户ID"""
	userId:String
	"""网校ID"""
	schoolId:String
	"""网校名称"""
	schoolName:String
	"""服务满意度评价星数，0~10"""
	serviceSatisfactionStars:Double!
	"""内容质量评价星数，0~10"""
	contentQualityStars:Double!
	"""评价内容"""
	contents:String
	"""评价时间"""
	createTime:DateTime
}
"""回复详细信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type ReversionDetailResponse @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.response.ReversionDetailResponse") {
	"""回复编号"""
	revId:String
	"""评论编号"""
	comId:String
	"""回复内容"""
	content:String
	"""被回复用户编号"""
	targetUserId:String
	"""回复人编号"""
	usrId:String
	"""被回复的回复编号"""
	parentId:String
	"""审核状态，0/1/2，未审核/审核通过/审核不通过"""
	audit:Int!
	"""置顶状态，0/1，不置顶/置顶"""
	pin:Int!
	"""屏蔽状态，0/1，不屏蔽/屏蔽"""
	shield:Int!
	"""特征标记列表"""
	markers:[MarkerRequest]
	"""回复时间"""
	createTime:DateTime
	"""用户名称"""
	userName:String
	"""被回复用户名称"""
	targetUserName:String
}
"""回复信息
	<AUTHOR>
	@date 2020/4/20
	@since 1.0.0
"""
type ReversionResponse @type(value:"com.fjhb.platform.core.v1.evaluation.kernel.gateway.graphql.response.ReversionResponse") {
	"""回复编号"""
	revId:String
	"""评论编号"""
	comId:String
	"""回复内容"""
	content:String
	"""被回复用户编号"""
	targetUserId:String
	"""回复人编号"""
	usrId:String
	"""被回复的回复编号"""
	parentId:String
	"""审核状态，0/1/2，未审核/审核通过/审核不通过"""
	audit:Int!
	"""置顶状态，0/1，不置顶/置顶"""
	pin:Int!
	"""屏蔽状态，0/1，不屏蔽/屏蔽"""
	shield:Int!
	"""特征标记列表"""
	markers:[MarkerRequest]
	"""回复时间"""
	createTime:DateTime
	"""用户名称"""
	userName:String
}

scalar List
type ReversionDetailResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ReversionDetailResponse]}
type CommentResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CommentResponse]}
type CourseAppraisalRecordResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseAppraisalRecordResponse]}
type NetSchoolAppraisalRecordResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [NetSchoolAppraisalRecordResponse]}
