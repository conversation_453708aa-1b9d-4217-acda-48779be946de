import { CreateOfflineInvoiceDeliveryChannelRequest } from '@api/ms-gateway/ms-offlineinvoice-v1'

/**
 * @description 创建自取点
 */
class CreateTakePlaceVo {
  id? = ''
  /**
   * 自取点名称
   */
  name = ''
  /**
   * 领取地点
   */
  address = ''
  /**
   * 开放领取时间
   */
  openTakeTime = ''
  /**
   * 备注
   */
  remark = ''
  /**
   * 状态
   */
  status = true

  toJSON(): CreateOfflineInvoiceDeliveryChannelRequest {
    const dto = new CreateOfflineInvoiceDeliveryChannelRequest()
    dto.channelName = this.name
    dto.address = this.address
    dto.shippingMethod = 1
    dto.remark = this.remark
    dto.enable = this.status
    dto.deliveryDate = this.openTakeTime
    return dto
  }
}

export default CreateTakePlaceVo
