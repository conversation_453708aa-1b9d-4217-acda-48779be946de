import {
  CoursewareUpdateRequest,
  ExtensionResourceData,
  ExtensionResourceDataDto,
  ResourceDataDto,
  VideoTranscodeSettings
} from '@api/ms-gateway/ms-course-resource-v1'
import CoursewareTransformStatus from '../../enum/CoursewareTransformStatus'
import CoursewareType from '../../enum/CoursewareType'

class UpdateCoursewareVo extends CoursewareUpdateRequest {
  categoryIds: string[] = []

  isBeingUsed = false
  /**
   * 是否外链
   */
  isOuter = false
  /**
   * 课件媒体资源
   */
  resourceDataDto?: ResourceDataDto = new ResourceDataDto()
  /**
   *标清地址
   */
  standardAddress: string = undefined

  /**
   *高清地址
   */
  highAddress: string = undefined

  /**
   *超清地址
   */
  superAddress: string = undefined
  /**
   * 课件类型，1表示文档，2表示视频，3表示多媒体
   */
  type: CoursewareType
  /*
   课件转换状态
   */
  status: CoursewareTransformStatus
  from(dto: UpdateCoursewareVo) {
    const vo = new CoursewareUpdateRequest()
    vo.id = dto.id
    vo.name = dto.name
    vo.teacherName = dto.teacherName
    vo.enable = dto.enable
    vo.aboutsContent = dto.aboutsContent
    vo.teacherId = dto.teacherId
    vo.teacherAboutsContent = dto.teacherAboutsContent
    // vo.resourceType = dto.resourceType
    vo.resourceType = dto.isOuter ? 2 : 1
    // vo.configJSON = dto.configJSON
    vo.supplierId = dto.supplierId.toString()

    if (dto?.categoryIds) {
      vo.categoryId = dto.categoryIds[dto.categoryIds.length - 1]
    }
    const resourceDataDto = dto.resourceDataDto || new ResourceDataDto()
    resourceDataDto.videoTranscodeSettings = new VideoTranscodeSettings()
    resourceDataDto.videoTranscodeSettings.hwySetting = {
      extractAudio: false,
      transcode: true
    }
    const extensionResourceDataDto = new ExtensionResourceDataDto()
    extensionResourceDataDto.extensionVideoInfo = new ExtensionResourceData()
    extensionResourceDataDto.extensionVideoInfo.videoInfoDtos = []
    if (dto.standardAddress) {
      extensionResourceDataDto.extensionVideoInfo.videoInfoDtos.push({ path: dto.standardAddress, clarity: 9 })
    }
    if (dto.highAddress) {
      extensionResourceDataDto.extensionVideoInfo.videoInfoDtos.push({ path: dto.highAddress, clarity: 10 })
    }
    if (dto.superAddress) {
      extensionResourceDataDto.extensionVideoInfo.videoInfoDtos.push({ path: dto.superAddress, clarity: 11 })
    }
    vo.configJSON = dto.isOuter ? JSON.stringify(extensionResourceDataDto) : JSON.stringify(resourceDataDto)
    // 媒体文件不修改时不需要传地址
    console.log(dto, '这个dto')

    if (
      (!dto.isOuter && !dto.resourceDataDto.resourcePath) ||
      (dto.isOuter && !dto.standardAddress && !dto.highAddress && !dto.superAddress)
    ) {
      vo.configJSON = null
      vo.resourceType = null
    }
    return vo
  }

  async update(): Promise<boolean> {
    return false
  }
}

export default UpdateCoursewareVo
