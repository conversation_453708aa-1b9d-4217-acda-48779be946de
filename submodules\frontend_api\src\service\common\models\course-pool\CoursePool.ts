import { UnitResponse } from '@api/gateway/btpx@Course-default'

/**
 * 课程包
 * <AUTHOR> update 2021/1/28  TODO
 */
class CoursePool {
  /**
   * 课程供应商id
   */
  coursewareSupplierId: string
  /**
   * 课程供应商名称
   */
  coursewareSupplierName: string
  /**
   * 课程包id
   */
  id: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 必填，所属平台版本ID
   */
  platformVersionId: string
  /**
   * 必填，所属项目ID
   */
  projectId: string
  /**
   * 必填，所属子项目ID
   */
  subProjectId: string
  /**
   * 必填，所属服务单位ID
   * trainingInstitutionId 培训机构
   */
  unitId: string
  /**
   * 代表trainingInstitutionName 培训机构名称
   */
  unitName: string
  /**
   * 单位信息
   */
  unit: UnitResponse
  /**
   * 必填，所属组织机构ID
   */
  organizationId: string
  /**
   * 必填，课程池名称
   */
  poolName: string
  /**
   * 排序序号，默认1
   */
  sequence: number
  /**
   * 必填，创建人编号
   */
  createUsrId: string
  /**
   * 过期时间,null表示不设置过期
   */
  expireTime: string
  /**
   * 课程池描述
   */
  poolDescription: string
  /**
   * 展示名称
   */
  showName: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 课程池内课程数量
   */
  courseCount: number
  /**
   * 课程池内课程学时或学分总和
   */
  totalPeriod: number
}

export default CoursePool
