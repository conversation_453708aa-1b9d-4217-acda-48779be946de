import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum TaskStatusEnum {
  /**
   * 未开始
   */
  un_start,
  /**
   * 编排失败
   */
  fail_arrange,
  /**
   * 执行中
   */
  execution,
  /**
   * 已完成
   */
  complete,
  /**
   * 执行失败
   */
  fail,
  /**
   * 已取消
   */
  cancel,
  /**
   * 终止
   */
  stop
}
export default class TaskStatusType extends AbstractEnum<TaskStatusEnum> {
  static enum = TaskStatusEnum
  constructor(status?: TaskStatusEnum) {
    super()
    this.current = status === TaskStatusEnum.fail_arrange ? TaskStatusEnum.fail : status
    this.map.set(TaskStatusEnum.un_start, '未开始')
    this.map.set(TaskStatusEnum.execution, '执行中')
    this.map.set(TaskStatusEnum.fail, '执行失败')
    this.map.set(TaskStatusEnum.stop, '终止')
    this.map.set(TaskStatusEnum.complete, '已完成')
  }
}
