import { OfflineInvoiceResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
import QueryPlatform from '@api/service/diff/common/qztg/dictionary/QueryPlatform'
import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/qztg/trade/enums/SaleChannelType'

export default class OffLinePageInvoiceResponseVo extends OffLinePageInvoiceVo {
  /**
   * 对接第三方平台
   */
  thirdPartyPlatform = ''

  /*********配送打平***********/
  /**
   * 转换
   * @param offlineInvoiceResponse
   * @returns
   */
  static from(offlineInvoiceResponse: OfflineInvoiceResponse) {
    const offLinePageInvoiceResponseVo = Object.assign(
      new OffLinePageInvoiceResponseVo(),
      OffLinePageInvoiceVo.from(offlineInvoiceResponse)
    )
    if (offLinePageInvoiceResponseVo.saleChannel == SaleChannelEnum.huayi) {
      offLinePageInvoiceResponseVo.thirdPartyPlatform = SaleChannelType.map.get(
        offLinePageInvoiceResponseVo.saleChannel
      )
    } else if (offlineInvoiceResponse.associationInfo?.tppTypeId) {
      offLinePageInvoiceResponseVo.thirdPartyPlatform = QueryPlatform.map.get(
        offlineInvoiceResponse.associationInfo.tppTypeId
      )?.name
    }
    return offLinePageInvoiceResponseVo
  }
}
