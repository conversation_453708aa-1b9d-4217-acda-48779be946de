import RegionAdministratorItem from '@api/service/management/authority/region-administrator/RegionAdministratorItem'
import MsBasicDataQueryBackstageGateway, {
  AdminQueryRequest,
  AdminUserRequest,
  AccountRequest,
  AuthenticationRequest,
  RoleRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

import { Page } from '@hbfe/common'
export default class RegionAdministratorList {
  /**
   * 地区管理员账号
   */
  account: string = undefined
  /**
   * 地区管理员名称
   */
  name: string = undefined
  /**
   * 启停用状态 | 1.启用 | 2.禁用
   */
  status: number = undefined
  /**
   * 管辖地区
   */
  region: Array<string> = undefined
  /**
   * 查询
   */
  async queryList(page: Page): Promise<RegionAdministratorItem[]> {
    // TODO
    const request = new AdminQueryRequest()
    request.user = new AdminUserRequest()
    request.user.userName = this.name
    request.user.userNameMatchType = 1
    request.account = new AccountRequest()
    request.role = new RoleRequest()
    request.authentication = new AuthenticationRequest()
    request.authentication.identity = this.account
    request.account.statusList = this.status ? [this.status] : undefined
    let region = ''
    this.region?.forEach(item => {
      region = region + `/${item}`
    })
    request.user.manageRegionPathList = region ? [region] : undefined
    // request.role.roleTypeList = ['REGION_MANAGER']
    request.role.roleCategoryList = [320]
    const res = await MsBasicDataQueryBackstageGateway.pageOnlineSchoolAdminInfoInServicer({ page, request })
    if (res.status.isSuccess()) {
      page.totalSize = res.data.totalSize
      page.totalPageSize = res.data.totalPageSize
      return res.data.currentPageData.map(item => RegionAdministratorItem.from(item))
    } else {
      return []
    }
  }
}
