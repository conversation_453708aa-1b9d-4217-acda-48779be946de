import PageSearch from '@api/service/common/page-search/PageSearch'
import { Response, UiPage } from '@hbfe/common'
import MsExamQueryBackStageGateway, {
  PaperPublishConfigureResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import PaperPublishConfigureRequestVo from './vo/PaperPublishConfigureRequestVo'
import PaperPublishConfigureResponseVo from './vo/PaperPublishConfigureResponseVo'

import MsBasicDataQueryBackGateway, {
  AdminInfoResponse,
  AdminQueryRequest,
  AdminUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

class QueryExamPaperList extends PageSearch {
  // 创建人id集合
  private createUserIdList = new Array<string>()

  /**
   * @description: 获取试卷列表
   * @param {*}
   * @return {*}
   */
  async queryExamPaperList(
    page: UiPage,
    queryExamPaperListParams: PaperPublishConfigureRequestVo
  ): Promise<Response<Array<PaperPublishConfigureResponseVo>>> {
    const result = await MsExamQueryBackStageGateway.pagePaperPublishConfigureInServicer({
      page: page,
      request: queryExamPaperListParams
    })
    const response = new Response<Array<PaperPublishConfigureResponseVo>>()
    if (!result?.status?.isSuccess()) {
      response.status = result.status
      return response
    }
    page.totalSize = result.data.totalSize
    page.totalPageSize = result.data.totalPageSize

    // 试卷列表
    const paperList = Array<PaperPublishConfigureResponseVo>()
    result?.data?.currentPageData?.forEach((item: PaperPublishConfigureResponse) => {
      const paperItem = PaperPublishConfigureResponseVo.from(item)
      this.createUserIdList.push(item.createUserId)
      paperList.push(paperItem)
    })
    response.data = paperList

    // 获取创建人名称
    this.queryCreateUserNames(page, this.createUserIdList)
      .then(createNameResponse => {
        paperList.map(res => {
          const curCreate = createNameResponse.data.find(item => item.userId === res.createUserId)
          if (curCreate) {
            res.createUserName = curCreate.name
          }
        })
      })
      .catch(error => {
        response.status = error.status
        return response
      })

    response.status = result.status
    response.data = paperList
    return response
  }

  /**
   * @description: 根据创建人id集合查找创建人名称
   * @param {UiPage} page
   * @param {Array} idList
   */
  private async queryCreateUserNames(
    page: UiPage,
    idList: Array<string>
  ): Promise<Response<Array<{ name: string; userId: string }>>> {
    const params = new AdminQueryRequest()
    params.user = new AdminUserRequest()
    params.user.userIdList = [...new Set(idList)]
    const res = await MsBasicDataQueryBackGateway.pageAdminInfoInServicer({
      page: page,
      request: params
    })
    const response = new Response<Array<{ name: string; userId: string }>>()
    if (!res?.status?.isSuccess()) {
      response.status = res.status
      return response
    }
    const adminUserNameList: Array<{ name: string; userId: string }> =
      res?.data?.currentPageData?.map((item: AdminInfoResponse) => {
        return {
          name: item.userInfo?.userName,
          userId: item.userInfo?.userId
        }
      }) || []
    response.status = res.status
    response.data = adminUserNameList
    return response
  }
}

export default QueryExamPaperList
