<route-meta>
{
"isMenu": true,
"title": "分销管理员账号管理",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  import DistributionAdministratorAccount from '@hbfe/jxjy-admin-account/src/distribution-administrator-account/index.vue'
  import { NZFXS, NZFXSJCB, WXGLY } from '@/models/RoleTypes'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  @RoleTypeDecorator({
    query: [NZFXS, NZFXSJCB],
    create: [NZFXS, NZFXSJCB],
    resetPassword: [NZFXS, NZFXSJCB],
    detail: [NZFXS, NZFXSJCB],
    modify: [NZFXS, NZFXSJCB],
    enable: [NZFXS, NZFXSJCB],
    deactivate: [NZFXS, NZFXSJCB]
  })
  export default class extends DistributionAdministratorAccount {}
</script>
