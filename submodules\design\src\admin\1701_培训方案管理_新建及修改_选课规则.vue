<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">培训方案管理</el-breadcrumb-item>
      <el-breadcrumb-item>修改</el-breadcrumb-item>
    </el-breadcrumb>
    <!--修改时的提示-->
    <!--<el-alert type="warning" :closable="false" class="m-alert is-border-bottom">-->
    <!--  <span class="f-fb">修改提示：</span>-->
    <!--  <span class="f-c6">-->
    <!--    方案编辑时系统自动标识本次修改内容（显示为<i class="is-tag f-ml5"></i>），保存后可前往修改日志查看编辑内容。-->
    <!--  </span>-->
    <!--</el-alert>-->
    <!--处于考核重算场景下，提示语-->
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <span class="f-fb">修改提示：</span>
      <span class="f-c6">系统正在处理上一次修改方案的重算任务，处理过程中仅支持修改培训方案基础信息。 </span>
    </el-alert>
    <!--处于智能学习场景下，提示语-->
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <span class="f-fb">修改提示：</span>
      <span class="f-c6">系统在为本方案下指定已报名学员进行智能学习，处理过程中仅支持修改培训方案基础信息。</span>
    </el-alert>
    <div class="f-p15">
      <el-card shadow="never" class="m-card">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="150px" class="m-form">
              <el-form-item label="培训形式：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="网授"></el-radio>
                  <el-radio label="面授"></el-radio>
                  <el-radio label="面网授"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="方案类型：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="培训班"></el-radio>
                  <el-radio>
                    合作办学
                    <el-tooltip effect="dark" placement="right" popper-class="m-tooltip">
                      <i class="el-icon-question m-tooltip-icon f-c9 f-mlr5"></i>
                      <div slot="content">
                        <p>合作办学配置说明：</p>
                        <p>方案如需开启指定平台的培训合作，请选择此方案类型。</p>
                      </div>
                    </el-tooltip>
                  </el-radio>
                  <el-radio label="课程超市"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="网授选课方式：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="选课规则"></el-radio>
                  <el-radio label="自主选课"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="方案封面：">
                <!--有新增或者修改的添加 <i class="is-tag"></i>-->
                <i class="is-tag"></i>
                <el-upload action="#" list-type="picture-card" :auto-upload="false" class="m-pic-upload proportion-pic">
                  <div slot="default" class="upload-placeholder">
                    <i class="el-icon-plus"></i>
                    <p class="txt">上传图片</p>
                  </div>
                  <div slot="file" slot-scope="{ file }" class="img-file">
                    <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                    <div class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                        <i class="el-icon-zoom-in"></i>
                      </span>
                      <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                        <i class="el-icon-delete"></i>
                      </span>
                    </div>
                  </div>
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning f-mt5"></i>
                    <div class="txt f-mt5">
                      封面图片比例为16:9，建议尺寸：不小于400px * 225px。
                    </div>
                    <el-button type="primary" size="small" plain class="f-ml10">选择图片</el-button>
                  </div>
                </el-upload>
                <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                  <img :src="dialogImageUrl" alt="" />
                </el-dialog>
              </el-form-item>
              <el-form-item label="方案名称：" required>
                <!--有新增或者修改的添加 <i class="is-tag"></i>-->
                <i class="is-tag"></i>
                <el-input v-model="form.name" clearable placeholder="请输入方案名称" />
              </el-form-item>
              <el-form-item label="年度：" required>
                <!--可搜索 filterable-->
                <el-select v-model="form.region" clearable placeholder="请选择" class="form-s">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="地区：" required>
                <!--可搜索 filterable-->
                <el-cascader clearable filterable :options="cascader" placeholder="请选择" class="form-l" />
              </el-form-item>
              <el-form-item label="行业：" required>
                <!--可搜索 filterable-->
                <el-select v-model="form.region" clearable filterable placeholder="请选择" class="form-l">
                  <el-option value="人社行业"></el-option>
                  <el-option value="建设行业"></el-option>
                  <el-option value="职业卫生行业"></el-option>
                  <el-option value="工勤行业"></el-option>
                  <el-option value="教师行业"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="学段：">
                <el-select placeholder="请选择学段" class="form-l">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="学科：">
                <el-select placeholder="请选择学科" class="form-l">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="培训类别：" required>
                <!--可搜索 filterable-->
                <el-select v-model="form.region" clearable filterable placeholder="请选择培训类别" class="form-l">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="培训对象：" required>
                <!--可搜索 filterable-->
                <el-select v-model="form.region" clearable filterable placeholder="请选择培训对象" class="form-l">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="岗位类型：" required>
                <!--可搜索 filterable-->
                <el-cascader clearable filterable :options="cascader" placeholder="请选择岗位类型" class="form-l" />
              </el-form-item>
              <el-form-item label="技术等级：" required>
                <!--可搜索 filterable-->
                <el-cascader clearable filterable :options="cascader" placeholder="请选择技术等级" class="form-l" />
              </el-form-item>
              <el-form-item label="培训须知：">
                <el-input
                  type="textarea"
                  :rows="6"
                  v-model="form.desc"
                  maxlength="200"
                  show-word-limit
                  placeholder="培训须知会在培训班详情内展示"
                />
              </el-form-item>
              <el-form-item label="培训方案简介：">
                <el-input
                  type="textarea"
                  :rows="10"
                  v-model="form.desc"
                  show-word-limit
                  placeholder="请输入培训方案简介"
                />
              </el-form-item>
              <el-form-item label="成果是否同步：">
                <div slot="label">
                  <span class="f-cr f-mr5">*</span>
                  <span class="f-vm">成果是否同步</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>这里是提示语</p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <el-radio-group v-model="form.resource">
                  <el-radio label="同步"></el-radio>
                  <el-radio label="不同步"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="指定平台：">
                <div slot="label">
                  <span class="f-cr f-mr5">*</span>
                  <span class="f-vm">指定平台</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>指定平台配置说明：</p>
                      <p>
                        如本培训方案类型选择培训合作，请选择指定的平台名称。请注意培训方案一经过发布该配置不支持修改。
                      </p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <el-select v-model="form.region" clearable placeholder="请选择指定的平台名称" class="form-l">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="指定平台：">读取对接的平台名称</el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="u-tab-tips-new">
        <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
          <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
          <div slot="content">请先勾选学习内容模块，再配置对应内容。</div>
        </el-tooltip>
      </div>
      <el-tabs v-model="activeName1" type="card" class="m-tab-card is-sticky">
        <el-tab-pane name="first">
          <div slot="label">
            <el-checkbox checked="false" class="f-mr5"></el-checkbox>
            <i class="f-pl5">线上课程</i>
          </div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20">
              <div class="f-ptb20 fixed-btn">
                <el-button type="primary" icon="el-icon-plus">添加分类</el-button>
                <el-button type="primary" icon="el-icon-plus">添加课程</el-button>
              </div>
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom f-align-center">
                  <!--点击修改后隐藏-->
                  <span class="tit-txt">必修课</span>
                  <a href="#" class="f-link f-cb f-ml10 m-tooltip-icon"><i class="el-icon-edit-outline f-f18"></i></a>
                  <!-- /点击修改后隐藏-->
                  <!--点击修改后出现-->
                  <!--<el-input v-model="input" size="mini" clearable placeholder="请输入名称" class="ipt" />-->
                  <!--<a href="#" class="f-link f-cb f-ml10 m-tooltip-icon"><i class="el-icon-circle-check f-f18"></i></a>-->
                  <!--<a href="#" class="f-link f-cb f-ml10 m-tooltip-icon"><i class="el-icon-circle-close f-f18"></i></a>-->
                  <!--/点击修改后出现-->
                  <el-tag type="danger" effect="dark" size="mini" class="f-ml10">必修</el-tag>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                    <div slot="content">
                      本分类的课程为必修课程，报名成功后直接推送给学员，无需选课。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。
                    </div>
                  </el-tooltip>
                  <span class="f-fb f-ml20 f-flex-sub f-tr">
                    一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时
                  </span>
                </div>
                <el-row class="is-height">
                  <el-col :sm="8" :xl="6" class="is-border-right">
                    <div class="f-p20">
                      <!--第一级-->
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <!--有新增或者修改的在 添加 <i class="is-tag"></i>-->
                          <div class="tit"><i class="is-tag"></i>必修课</div>
                          <div class="op">
                            <el-button type="text" size="mini"><i class="el-icon-edit-outline"></i></el-button>
                          </div>
                        </div>
                      </el-tree>
                      <!--第二级-->
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <div class="tit">分类名称</div>
                          <div class="op">
                            <el-button type="text" size="mini"><i class="el-icon-edit-outline"></i></el-button>
                            <el-button type="text" size="mini"><i class="el-icon-delete"></i></el-button>
                          </div>
                        </div>
                      </el-tree>
                      <!--修改样式-->
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <div class="tit"><el-input v-model="input" size="mini" clearable placeholder="请输入" /></div>
                          <div class="op">
                            <el-button type="text" size="mini"><i class="el-icon-circle-check"></i></el-button>
                            <el-button type="text" size="mini"><i class="el-icon-circle-close"></i></el-button>
                          </div>
                        </div>
                      </el-tree>
                    </div>
                  </el-col>
                  <el-col :sm="16" :xl="18">
                    <div class="f-p20">
                      <div class="f-flex f-align-center">
                        <div class="m-tit is-mini">
                          <span class="tit-txt">分类名称</span>
                        </div>
                        <div class="f-fb f-flex-sub">（一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时）</div>
                        <div class="f-tr">
                          <el-button type="text" size="small">编辑课程包</el-button>
                          <el-button type="primary" size="small" plain>一键移除课程</el-button>
                          <el-button type="primary" size="small" icon="el-icon-plus" disabled>添加课程</el-button>
                        </div>
                      </div>
                      <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
                        </el-table-column>
                        <el-table-column label="课程名称" min-width="240" fixed="left">
                          <template>课程名称课程名称课程名称课程名称课程名称课程名称课程名称课程名称</template>
                        </el-table-column>
                        <el-table-column label="分类信息" min-width="200">
                          <template>一级分类>二级分类>三级分类</template>
                        </el-table-column>
                        <el-table-column label="所属课程包名称" min-width="200">
                          <template>所属课程包名称课程包名称</template>
                        </el-table-column>
                        <el-table-column label="课程学时数" width="120" align="center">
                          <template>10</template>
                        </el-table-column>
                      </el-table>
                      <!--分页-->
                      <el-pagination
                        background
                        class="f-mt15 f-tr"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage4"
                        :page-sizes="[100, 200, 300, 400]"
                        :page-size="100"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="400"
                      >
                      </el-pagination>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom f-align-center">
                  <!--点击修改后隐藏-->
                  <!--<span class="tit-txt">选修课</span>-->
                  <!--<a href="#" class="f-link f-cb f-ml10 m-tooltip-icon"><i class="el-icon-edit-outline f-f18"></i></a>-->
                  <!-- /点击修改后隐藏-->
                  <!--点击修改后出现-->
                  <el-input v-model="input" size="mini" clearable placeholder="请输入名称" class="ipt" />
                  <a href="#" class="f-link f-cb f-ml10 m-tooltip-icon"><i class="el-icon-circle-check f-f18"></i></a>
                  <a href="#" class="f-link f-cb f-ml10 m-tooltip-icon"><i class="el-icon-circle-close f-f18"></i></a>
                  <!--/点击修改后出现-->
                  <el-tag type="danger" effect="dark" size="mini" class="f-ml10">选修</el-tag>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                    <div slot="content">
                      此分类下的课程为选修课程，支持配置选课要求学时。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。
                    </div>
                  </el-tooltip>
                  <span class="f-fb f-ml20 f-flex-sub f-tr">
                    一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时
                  </span>
                </div>
                <el-row class="is-height">
                  <el-col :sm="8" :xl="6" class="is-border-right">
                    <div class="f-p20">
                      <!--第一级-->
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <!--有新增或者修改的在 添加 <i class="is-tag"></i>-->
                          <div class="tit"><i class="is-tag"></i>必修课</div>
                          <div class="op">
                            <el-button type="text" size="mini"><i class="el-icon-edit-outline"></i></el-button>
                          </div>
                        </div>
                      </el-tree>
                      <!--第二级-->
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <div class="tit">分类名称</div>
                          <div class="op">
                            <el-button type="text" size="mini"><i class="el-icon-edit-outline"></i></el-button>
                            <el-button type="text" size="mini"><i class="el-icon-delete"></i></el-button>
                          </div>
                        </div>
                      </el-tree>
                      <!--修改样式-->
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <div class="tit"><el-input v-model="input" size="mini" clearable placeholder="请输入" /></div>
                          <div class="op">
                            <el-button type="text" size="mini"><i class="el-icon-circle-check"></i></el-button>
                            <el-button type="text" size="mini"><i class="el-icon-circle-close"></i></el-button>
                          </div>
                        </div>
                      </el-tree>
                    </div>
                  </el-col>
                  <el-col :sm="16" :xl="18">
                    <div class="f-p20">
                      <div class="f-flex f-align-center">
                        <div class="m-tit is-mini">
                          <span class="tit-txt">分类名称</span>
                        </div>
                        <div class="f-fb f-flex-sub">（一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时）</div>
                        <div class="f-tr">
                          <el-button type="primary" size="small" plain>一键移除课程</el-button>
                          <el-button type="primary" size="small" icon="el-icon-plus" disabled>添加课程</el-button>
                        </div>
                      </div>
                      <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
                        </el-table-column>
                        <el-table-column label="课程名称" min-width="240" fixed="left">
                          <template>课程名称课程名称课程名称课程名称</template>
                        </el-table-column>
                        <el-table-column label="分类信息" min-width="200">
                          <template>一级分类>二级分类>三级分类</template>
                        </el-table-column>
                        <el-table-column label="所属课程包名称" min-width="200">
                          <template>所属课程包名称课程包名称</template>
                        </el-table-column>
                        <el-table-column label="课程学时数" width="120" align="center">
                          <template>10</template>
                        </el-table-column>
                      </el-table>
                      <!--分页-->
                      <el-pagination
                        background
                        class="f-mt15 f-tr"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage4"
                        :page-sizes="[100, 200, 300, 400]"
                        :page-size="100"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="400"
                      >
                      </el-pagination>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom f-align-center">
                  <span class="tit-txt">课程测验</span>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                    <div slot="content">
                      课程测验的组卷范围为已选择课程关联的试题。请先添加课程后，再添加测验。课程测验可选配。
                    </div>
                  </el-tooltip>
                </div>
                <div class="f-p20">
                  <el-button type="primary" icon="el-icon-plus" class="f-mb15">添加课程测验</el-button>
                  <el-table stripe :data="tableData" max-height="500px" class="m-table">
                    <el-table-column width="30" align="center" fixed="left">
                      <template slot-scope="scope">
                        <!--有新增或者修改的添加 <i class="is-tag"></i>-->
                        <div v-if="scope.$index === 0"><i class="is-tag"></i></div>
                        <div v-else></div>
                      </template>
                    </el-table-column>
                    <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                    <el-table-column label="总分/及格分" min-width="140" align="center" fixed="left">
                      <template>100 / 60</template>
                    </el-table-column>
                    <el-table-column label="组卷方式" min-width="120" align="center">
                      <template>智能组卷</template>
                    </el-table-column>
                    <el-table-column label="测验题数" min-width="120" align="center">
                      <template>100</template>
                    </el-table-column>
                    <el-table-column label="作答次数" min-width="120" align="center">
                      <template>不限次</template>
                    </el-table-column>
                    <el-table-column label="操作" width="120" align="center" fixed="right">
                      <template>
                        <el-button type="text" size="mini">编辑</el-button>
                        <el-button type="text" size="mini">移除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <!--分页-->
                  <el-pagination
                    background
                    class="f-mt15 f-tr"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage4"
                    :page-sizes="[100, 200, 300, 400]"
                    :page-size="100"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="400"
                  >
                  </el-pagination>
                </div>
              </el-card>
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom">
                  <span class="tit-txt">学习要求</span>
                </div>
                <div class="f-p20">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt10">
                        <el-form-item label="选修课选修要求：" required>
                          <el-input-number v-model="num" size="small" /><span class="f-ml10">学时</span>
                        </el-form-item>
                        <el-form-item label="课程测验纳入考核：" required>
                          <el-radio-group v-model="form.resource">
                            <el-radio label="是"></el-radio>
                            <el-radio label="否"></el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item label="开放课程评价：" required>
                          <el-radio-group v-model="form.resource">
                            <el-radio label="是"></el-radio>
                            <el-radio label="否"></el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item label="评价条件：" required>
                          每门课程学习进度达
                          <el-input v-model="form.name" size="small" class="input-num f-mlr5" />
                          %可以进行课程评价
                        </el-form-item>
                        <el-form-item label="是否强制学员评价：" required>
                          <el-radio-group v-model="form.resource">
                            <el-radio label="是"></el-radio>
                            <el-radio label="否"></el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item label="考核要求：">
                          <p>1. 必修 <i class="f-cr">0</i> 学时， 选修 <i class="f-cr">0</i> 学时，学习进度 100%</p>
                          <p>
                            2. 课程测验纳入考核，每门课程学习进度达
                            <i class="f-cr">30%</i>
                            可参加，测验及格分
                            <i class="f-cr">60</i>
                            分，次数不限次。
                          </p>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="second">
          <div slot="label">
            <el-checkbox checked="false" class="f-mr5"></el-checkbox>
            <i class="f-pl5">班级考试</i>
          </div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20">
              <el-button type="primary" icon="el-icon-plus" class="f-mb15 f-mt20">添加模拟卷</el-button>
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column width="30" align="center" fixed="left">
                  <template slot-scope="scope">
                    <!--有新增或者修改的添加 <i class="is-tag"></i>-->
                    <div v-if="scope.$index === 0"><i class="is-tag"></i></div>
                    <div v-else></div>
                  </template>
                </el-table-column>
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="场次名称" min-width="240" fixed="left">
                  <template>场次名称场次名称场次名称</template>
                </el-table-column>
                <el-table-column label="试卷名称" min-width="240">
                  <template>试卷名称试卷名称试卷名称</template>
                </el-table-column>
                <el-table-column label="组卷方式" min-width="120">
                  <template>智能组卷</template>
                </el-table-column>
                <el-table-column label="考试时长 / 总分 / 及格分" min-width="190" align="center">
                  <template>60 / 100 / 60</template>
                </el-table-column>
                <el-table-column label="作答次数" min-width="120" align="center">
                  <template>不限次</template>
                </el-table-column>
                <el-table-column label="操作" width="120" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">编辑</el-button>
                    <el-button type="text" size="mini">移除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
              <el-form ref="form" :model="form" label-width="auto" class="m-text-form is-column f-mt30">
                <el-form-item label="前置条件：">完成课程学习考核</el-form-item>
                <el-form-item label="培训要求：">以班级考核配置为准</el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="third">
          <div slot="label">
            <el-checkbox checked="false" class="f-mr5"></el-checkbox>
            <i class="f-pl5">班级练习</i>
          </div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20">
              <div class="f-pt5 f-mtb20">
                <!--有修改的添加 <i class="is-tag"></i>-->
                <i class="is-tag"></i>
                <span class="f-mr10">请选择练习来源：</span>
                <el-radio-group v-model="radio">
                  <el-radio :label="3">题库</el-radio>
                  <el-radio :label="6">按照学员课程ID出题</el-radio>
                  <el-radio :label="9">同考试</el-radio>
                </el-radio-group>
              </div>
              <el-button type="primary" icon="el-icon-plus" class="f-mb15">添加题库</el-button>
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column width="30" align="center" fixed="left">
                  <template slot-scope="scope">
                    <!--有新增或者修改的添加 <i class="is-tag"></i>-->
                    <div v-if="scope.$index === 0"><i class="is-tag"></i></div>
                    <div v-else></div>
                  </template>
                </el-table-column>
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="题库名称" min-width="240" fixed="left">
                  <template>场次名称场次名称场次名称</template>
                </el-table-column>
                <el-table-column label="已启用的试题数量" min-width="190" align="center">
                  <template>60 / 100 / 60</template>
                </el-table-column>
                <el-table-column label="操作" width="120" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">移除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
              <el-form ref="form" :model="form" label-width="auto" class="m-text-form is-column f-mt30">
                <el-form-item label="前置条件：">无</el-form-item>
                <el-form-item label="培训要求：">无</el-form-item>
              </el-form>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="fourth">
          <div slot="label">
            <el-checkbox checked="false" class="f-mr5"></el-checkbox>
            <i class="f-pl5">兴趣课程</i>
          </div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20">
              <div class="f-ptb20 fixed-btn">
                <el-button type="primary" icon="el-icon-plus">添加分类</el-button>
                <el-button type="primary" icon="el-icon-plus">添加课程</el-button>
              </div>
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom f-align-center">
                  <span class="tit-txt">兴趣课程</span>
                  <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                    <div slot="content">
                      本分类的课程为必修课程，报名成功后直接推送给学员，无需选课。如需调整分类的展示顺序，可长按具体分类模块拖拽至想要的位置。
                    </div>
                  </el-tooltip>
                  <span class="f-fb f-ml20 f-flex-sub f-tr">
                    一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时
                  </span>
                </div>
                <el-row class="is-height">
                  <el-col :sm="8" :xl="6" class="is-border-right">
                    <div class="f-p20">
                      <!--第一级-->
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <!--有新增或者修改的在 添加 <i class="is-tag"></i>-->
                          <div class="tit"><i class="is-tag"></i>必修课</div>
                          <div class="op">
                            <el-button type="text" size="mini"><i class="el-icon-edit-outline"></i></el-button>
                          </div>
                        </div>
                      </el-tree>
                      <!--第二级-->
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <div class="tit">分类名称</div>
                          <div class="op">
                            <el-button type="text" size="mini"><i class="el-icon-edit-outline"></i></el-button>
                            <el-button type="text" size="mini"><i class="el-icon-delete"></i></el-button>
                          </div>
                        </div>
                      </el-tree>
                      <!--修改样式-->
                      <el-tree :data="data" node-key="id" :expand-on-click-node="false" class="m-course-tree">
                        <div class="custom-tree-node">
                          <div class="tit"><el-input v-model="input" size="mini" clearable placeholder="请输入" /></div>
                          <div class="op">
                            <el-button type="text" size="mini"><i class="el-icon-circle-check"></i></el-button>
                            <el-button type="text" size="mini"><i class="el-icon-circle-close"></i></el-button>
                          </div>
                        </div>
                      </el-tree>
                    </div>
                  </el-col>
                  <el-col :sm="16" :xl="18">
                    <div class="f-p20">
                      <div class="f-flex f-align-center">
                        <div class="m-tit is-mini">
                          <span class="tit-txt">分类名称</span>
                        </div>
                        <div class="f-fb f-flex-sub">（一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时）</div>
                        <div class="f-tr">
                          <el-button type="text" size="small">编辑课程包</el-button>
                          <el-button type="primary" size="small" plain>一键移除课程</el-button>
                          <el-button type="primary" size="small" icon="el-icon-plus" disabled>添加课程</el-button>
                        </div>
                      </div>
                      <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                        <el-table-column type="index" label="No." width="60" align="center" fixed="left">
                        </el-table-column>
                        <el-table-column label="课程名称" min-width="240" fixed="left">
                          <template>课程名称课程名称课程名称课程名称</template>
                        </el-table-column>
                        <el-table-column label="分类信息" min-width="200">
                          <template>一级分类>二级分类>三级分类</template>
                        </el-table-column>
                        <el-table-column label="所属课程包名称" min-width="200">
                          <template>所属课程包名称课程包名称</template>
                        </el-table-column>
                        <el-table-column label="课程学时数" width="120" align="center">
                          <template>10</template>
                        </el-table-column>
                      </el-table>
                      <!--分页-->
                      <el-pagination
                        background
                        class="f-mt15 f-tr"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage4"
                        :page-sizes="[100, 200, 300, 400]"
                        :page-size="100"
                        layout="total, sizes, prev, pager, next, jumper"
                        :total="400"
                      >
                      </el-pagination>
                    </div>
                  </el-col>
                </el-row>
              </el-card>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="fifth">
          <div slot="label">
            <el-checkbox checked="false" class="f-mr5"></el-checkbox>
            <i class="f-pl5">学习心得</i>
          </div>

          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20">
              <el-row type="flex" justify="left" class="width-limit">
                <el-col :md="20" :lg="16" :xl="13">
                  <el-form ref="form" :model="form" label-width="170px" class="m-form f-mt20">
                    <el-form-item label="学习心得对外展示名称：">
                      <el-input v-model="form.name" size="small" class="form-l f-mr5" placeholder="请输入展示名称" />
                      <div class="tab-tips-3">
                        <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                          <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                          <div slot="content">如有配置对外展示名称，学员查看学习方式显示为此名称</div>
                        </el-tooltip>
                      </div>
                      <div class="f-ci">对外展示名称不可超过6个字符</div>
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>

              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="f-p20">
                  <div class="f-flex f-align-center">
                    <div class="f-mr10">
                      <el-button type="primary" size="small" icon="el-icon-plus">新增</el-button>
                      <el-button type="primary" size="small" plain>一键移除</el-button>
                      <el-button type="primary" size="small" plain>必选管理</el-button>
                    </div>
                    <div class="f-fb f-flex-sub">一共 <i class="f-cr">0</i> 个，必选<i class="f-cr">0</i>个</div>
                  </div>
                  <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                    <el-table-column width="30" align="center" fixed="left">
                      <template>
                        <el-checkbox checked="false" class="f-mr5"></el-checkbox>
                      </template>
                    </el-table-column>
                    <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                    <el-table-column label="主题" min-width="240" fixed="left">
                      <template
                        ><el-tag type="primary" effect="dark" size="mini">必选</el-tag
                        >课程名称课程名称课程名称课程名称</template
                      >
                    </el-table-column>
                    <el-table-column label="参加时间" min-width="240" align="center">
                      <template>2023-10-23 00:00:00至2023-11-11 23:59:59</template>
                    </el-table-column>
                    <el-table-column label="学习心得类型" min-width="100" align="center">
                      <template>班级心得</template>
                    </el-table-column>
                    <el-table-column label="作答形式" min-width="100" align="center">
                      <template>提交附件</template>
                    </el-table-column>
                    <el-table-column label="审核方式" min-width="100" align="center">
                      <template>提交自动通过</template>
                    </el-table-column>
                    <el-table-column label="总分" min-width="60" align="center">
                      <template>60</template>
                    </el-table-column>
                    <el-table-column label="未通过作答次数" width="160" align="center">
                      <template>3次</template>
                    </el-table-column>
                    <el-table-column label="操作" width="120" align="center" fixed="right">
                      <template>
                        <el-button type="text" size="mini">移除</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <!--分页-->
                  <el-pagination
                    background
                    class="f-mt15 f-tr"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage4"
                    :page-sizes="[100, 200, 300, 400]"
                    :page-size="100"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="400"
                  >
                  </el-pagination>
                </div>
              </el-card>

              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="m-tit is-small bg-gray is-border-bottom">
                  <span class="tit-txt">学习心得要求</span>
                </div>
                <div class="f-p20">
                  <el-row type="flex" justify="center" class="width-limit">
                    <el-col :md="20" :lg="16" :xl="13">
                      <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt10">
                        <el-form-item label="班级心得前置条件：" required>
                          <el-radio-group v-model="form.resource">
                            <el-radio label="可直接参加"></el-radio>
                            <el-radio label="完成班级所有课程学习方可参加"></el-radio>
                          </el-radio-group>
                        </el-form-item>
                        <el-form-item label="课程心得前置条件：" required>
                          <el-radio-group v-model="form.resource">
                            <el-radio label="可直接参加"></el-radio>
                            <el-radio label="完成班级指定课程学习方可参加"></el-radio>
                          </el-radio-group>
                        </el-form-item>

                        <el-form-item label="整体要求：" required>
                          至少需要参加 <el-input-number v-model="num" size="small" class="f-mlr5" /> 个学习心得
                          <p>
                            <i class="f-cr">至少需要参加的学习心得不可为空</i>
                          </p>
                        </el-form-item>

                        <el-form-item label="成绩要求：" required>
                          成绩 ≥
                          <el-input-number v-model="num" size="small" class="f-mlr5" /> 分（总分：100分）视为通过。
                        </el-form-item>

                        <el-form-item label="是否纳入考核：" required>
                          <el-radio-group v-model="form.resource">
                            <el-radio label="是"></el-radio>
                            <el-radio label="否"></el-radio>
                          </el-radio-group>
                        </el-form-item>

                        <el-form-item label="考核要求：">
                          <p>1. 各项学习心得要求以具体配置为准</p>
                          <p>
                            2. 学习心得纳入考核，至少参加
                            <i class="f-cr">Z</i>
                            个心得，且每项心得均为通过。
                          </p>
                        </el-form-item>
                      </el-form>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="sixth">
          <div slot="label">
            <el-checkbox checked="false" class="f-mr5"></el-checkbox>
            <i class="f-pl5">培训期别</i>
          </div>
          <el-card shadow="never" class="m-card f-mb15">
            <el-row :gutter="20" class="m-farewell">
              <el-col :sm="8" :md="6">
                <div class="item">
                  <div class="item-tit">
                    <div class="tit">
                      <span class="txt" title="xx">这里显示期别名称</span><el-tag type="warning">未开始</el-tag>
                    </div>
                    <div class="num">01</div>
                  </div>
                  <div class="item-hd">
                    <div class="f-flex f-justify-between">
                      <div class="ele">
                        <div class="ele-t">培训时段</div>
                        <div class="ele-info f-to">204-10-15至204-10-19</div>
                      </div>
                      <div class="ele">
                        <div class="ele-t">开放报名人数</div>
                        <div class="ele-info f-to">15人</div>
                      </div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">开放报名时间</div>
                      <div class="ele-info f-to">2024-10-11 10:00:00 至 2024-10-21 11:00:00</div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">培训地点</div>
                      <div class="ele-info f-to-two">
                        安徽水利和顺大酒店（合肥市肥东经济开发区龙脊山路1号，长江东路与龙脊山路交口）
                      </div>
                    </div>
                  </div>
                  <div class="item-bd">
                    <div class="bd-l">
                      <el-tooltip effect="dark" placement="top" content="复制">
                        <span class="btn-icon el-icon-document-copy"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="编辑期别">
                        <span class="btn-icon el-icon-edit-outline"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="查看详情">
                        <span class="btn-icon p-icon icon-1"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="添加课程">
                        <span class="btn-icon p-icon icon-2"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="编辑课程">
                        <span class="btn-icon p-icon icon-3"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="查看课程">
                        <span class="btn-icon p-icon icon-4"></span>
                      </el-tooltip>
                    </div>
                    <div class="bd-r">
                      <el-tooltip effect="dark" placement="top" content="删除">
                        <span class="btn-icon el-icon-delete"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :sm="8" :md="6">
                <div class="item">
                  <div class="item-tit">
                    <div class="tit">
                      <span class="txt" title="xx">这里显示期别名称</span><el-tag type="success">进行中</el-tag>
                    </div>
                    <div class="num">02</div>
                  </div>
                  <div class="item-hd">
                    <div class="f-flex f-justify-between">
                      <div class="ele">
                        <div class="ele-t">培训时段</div>
                        <div class="ele-info f-to">204-10-15至204-10-19</div>
                      </div>
                      <div class="ele">
                        <div class="ele-t">开放报名人数</div>
                        <div class="ele-info f-to">15人</div>
                      </div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">开放报名时间</div>
                      <div class="ele-info f-to">2024-10-11 10:00:00 至 2024-10-21 11:00:00</div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">培训地点</div>
                      <div class="ele-info f-to-two">
                        安徽水利和顺大酒店（合肥市肥东经济开发区龙脊山路1号，长江东路与龙脊山路交口）
                      </div>
                    </div>
                  </div>
                  <div class="item-bd">
                    <div class="bd-l">
                      <el-tooltip effect="dark" placement="top" content="复制">
                        <span class="btn-icon el-icon-document-copy"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="编辑期别">
                        <span class="btn-icon el-icon-edit-outline"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="查看详情">
                        <span class="btn-icon p-icon icon-1"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="添加课程">
                        <span class="btn-icon p-icon icon-2"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="编辑课程">
                        <span class="btn-icon p-icon icon-3"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="查看课程">
                        <span class="btn-icon p-icon icon-4"></span>
                      </el-tooltip>
                    </div>
                    <div class="bd-r">
                      <el-tooltip effect="dark" placement="top" content="删除">
                        <span class="btn-icon el-icon-delete"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :sm="8" :md="6">
                <div class="item">
                  <div class="item-tit">
                    <div class="tit">
                      <span class="txt" title="xx"
                        >这里显示期别名称这里显示期别名称这里显示期别名称这里显示期别名称</span
                      ><el-tag type="info">已结束</el-tag>
                    </div>
                    <div class="num">03</div>
                  </div>
                  <div class="item-hd">
                    <div class="f-flex f-justify-between">
                      <div class="ele">
                        <div class="ele-t">培训时段</div>
                        <div class="ele-info f-to">204-10-15至204-10-19</div>
                      </div>
                      <div class="ele">
                        <div class="ele-t">开放报名人数</div>
                        <div class="ele-info f-to">15人</div>
                      </div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">开放报名时间</div>
                      <div class="ele-info f-to">2024-10-11 10:00:00 至 2024-10-21 11:00:00</div>
                    </div>
                    <div class="ele">
                      <div class="ele-t">培训地点</div>
                      <div class="ele-info f-to-two">
                        安徽水利和顺大酒店（合肥市肥东经济开发区龙脊山路1号，长江东路与龙脊山路交口）
                      </div>
                    </div>
                  </div>
                  <div class="item-bd">
                    <div class="bd-l">
                      <el-tooltip effect="dark" placement="top" content="复制">
                        <span class="btn-icon el-icon-document-copy"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="编辑期别">
                        <span class="btn-icon el-icon-edit-outline"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="查看详情">
                        <span class="btn-icon p-icon icon-1"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="添加课程">
                        <span class="btn-icon p-icon icon-2"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="编辑课程">
                        <span class="btn-icon p-icon icon-3"></span>
                      </el-tooltip>
                      <el-tooltip effect="dark" placement="top" content="查看课程">
                        <span class="btn-icon p-icon icon-4"></span>
                      </el-tooltip>
                    </div>
                    <div class="bd-r">
                      <el-tooltip effect="dark" placement="top" content="删除">
                        <span class="btn-icon el-icon-delete"></span>
                      </el-tooltip>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :sm="8" :md="6">
                <div class="add-btn">
                  <i class="el-icon-plus"></i>
                  <p class="txt">添加期别</p>
                </div>
              </el-col>
            </el-row>
            <el-card shadow="never" class="m-card is-header">
              <div class="m-tit is-small bg-gray is-border-bottom">
                <span class="tit-txt">学习要求</span>
              </div>
              <div class="f-p20">
                <el-row type="flex" justify="center" class="width-limit">
                  <el-col :md="20" :lg="16" :xl="13">
                    <el-form ref="form" label-width="180px" class="m-form f-mt10">
                      <!--
                      <el-form-item label="是否开启报到：" required>
                        <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                        <div class="bg-gray f-mt10 f-plr15 f-pt5 f-pb5">
                          <el-form ref="form" label-width="auto" class="m-form">
                            <el-form-item label="报到配置生效范围：" required>
                              <el-radio v-model="radio" label="1">全期别生效 </el-radio>
                              <el-radio v-model="radio" label="2">
                                指定期别生效
                              </el-radio>
                            </el-form-item>
                          </el-form>
                        </div>
                      </el-form-item>
                      -->
                      <el-form-item label="考勤考核要求：" required>
                        考勤率不低于<el-input placeholder="请输入" class="input-num f-mlr5" />%
                        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                          <i class="el-icon-info m-tooltip-icon f-c9"></i>
                          <div slot="content">
                            <p><b>考勤说明：</b></p>
                            <p>
                              考勤率=有效签到签退次数/应签到签退总次数，同一签到/签退时段产生多次签到"签退记录仅记为一次。
                            </p>
                            <p>
                              例如：根据机构填报的课表计算出应签到签退总次数为12次，若考勒率不低于75%，那么学员有效签到签退次数达到9次即考勒合格。
                            </p>
                          </div>
                        </el-tooltip>
                        <div class="f-ci">
                          注：此处为班级默认考勤考核要求，如需为期别配置独立考勤考核要求时，可在对应期别里单独配置
                        </div>
                      </el-form-item>
                      <el-form-item label="学员须知：">
                        <el-input
                          type="textarea"
                          placeholder="请输入学员须知内容..."
                          rows="5"
                          show-word-limit
                        ></el-input>
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </div>
            </el-card>
          </el-card>
          <el-card shadow="never" class="m-card f-mb15">
            <!--通用空数据-->
            <div class="m-no-date">
              <img class="img" src="./assets/images/no-data-normal.png" alt="" />
              <div class="date-bd">
                <p class="f-f15 f-c9">您当前还未添加培训期别，请点击下方按钮进行添加~</p>
                <div class="f-mt10">
                  <el-button type="primary" size="small">添加期别</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="seventh">
          <div slot="label">
            <el-checkbox checked="false" class="f-mr5"></el-checkbox>
            <i class="f-pl5">调研问卷</i>
          </div>

          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20">
              <el-button type="primary" icon="el-icon-plus" class="f-mb15 f-mt20">添加调研问卷</el-button>
              <el-table stripe :data="tableData" max-height="500px" class="m-table">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="问卷名称" min-width="240" fixed="left">
                  <template>读取问卷名称</template>
                </el-table-column>
                <el-table-column label="问卷开放时间" min-width="240">
                  <template>2024-10-01 08:08:08至2024-10-01 08:08:08</template>
                </el-table-column>
                <el-table-column label="问卷结果" min-width="120">
                  <template>开放</template>
                </el-table-column>
                <el-table-column label="问卷状态" min-width="120" align="center">
                  <template>-</template>
                </el-table-column>
                <el-table-column label="应用范围" min-width="120" align="center">
                  <template>培训方案</template>
                </el-table-column>
                <el-table-column label="前置条件" min-width="120" align="center">
                  <template>完成线上课程考核</template>
                </el-table-column>
                <el-table-column label="考核要求" min-width="120" align="center">
                  <template>纳入考核</template>
                </el-table-column>
                <el-table-column label="操作" width="200" align="center" fixed="right">
                  <template slot-scope="scope">
                    <div v-if="scope.$index === 0">
                      <el-button type="text">预览</el-button>
                      <el-button type="text">编辑</el-button>
                      <el-button type="text">停用</el-button>
                      <el-button type="text">删除</el-button>
                    </div>
                    <div v-else-if="scope.$index === 2">
                      <el-button type="text">预览</el-button>
                      <el-button type="text">编辑</el-button>
                      <el-button type="text">启用</el-button>
                      <el-button type="text">删除</el-button>
                    </div>
                    <div v-else>
                      <el-button type="text">预览</el-button>
                      <el-button type="text">编辑</el-button>
                      <el-button type="text">删除</el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">培训期别学员须知</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt10">
              <el-form-item label="学员须知：">
                <el-input type="textarea" placeholder="请输入学员须知内容..." rows="5" show-word-limit></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">培训要求</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="150px" class="m-form f-mt10">
              <el-form-item label="考核规则：" required>
                <p>1. 课程学习要求不低于 <i class="f-cr">0</i> 学时。</p>
                <div class="f-ml15">
                  <p>① 每门课程学习进度=100%</p>
                  <p>
                    ② 课程测验纳入考核，每门课程学习进度达
                    <i class="f-cr">30%</i>
                    可参加，测验及格分 ≥
                    <i class="f-cr">60</i>
                    分，次数不限次。
                  </p>
                </div>
                <p class="f-mt10">
                  2. 班级考试考核成绩 ≥
                  <el-input v-model="form.name" size="small" class="input-num f-mlr5" />
                  分（总分100分，考试次数：<i class="f-cr">60</i> 次）
                </p>
                <p>3. 学习心得纳入考核</p>
                <div class="f-ml15">
                  <p>① 各项学习心得要求以具体配置为准</p>
                  <p>②学习心得纳入考核，至少参加 <i class="f-cr">Z</i> 个心得，且每项心得均为通过。</p>
                </div>
                <p>
                  4. 调研问卷纳入考核，按具体问卷要求提交。<el-button type="text" class="f-ml10">[查看详情]</el-button>
                </p>
                <p>
                  5. 培训期别：至少完成一个期别并考核通过。<el-button type="text" class="f-ml10">[查看详情]</el-button>
                </p>
              </el-form-item>
              <el-form-item>
                <div slot="label">
                  <span class="f-cr f-mr5">*</span>
                  <span class="f-vm">整班重学</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>提供：勾选提供则考试次数用完且考核未通过，由学员发起整班重学（清空学习及考试记录）</p>
                      <p>不提供：则考试次数用完即产生考核结果</p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <el-radio-group v-model="form.resource">
                  <el-radio label="提供"></el-radio>
                  <el-radio label="不提供"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="获得学时：" required>
                考核通过后将获得 <el-input v-model="form.name" size="small" class="input-num f-mlr5" /> 学时
              </el-form-item>
              <el-form-item label="培训成果：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="提供培训证明，达到培训要求后可打印"></el-radio>
                  <el-radio label="不提供培训证明"></el-radio>
                </el-radio-group>
              </el-form-item>
              <!--证明模板配置-->
              <!--
              <el-form-item label="证明模板配置：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="按班级统一模板"></el-radio>
                  <el-radio label="按期别指定模板"></el-radio>
                </el-radio-group>
              </el-form-item>
              -->
              <!--选择提供证明后显示-->
              <el-form-item label="培训证明模板：">
                <!--选择后 隐藏-->
                <el-button size="small" type="primary" plain>选择模板</el-button>
                <!--选择后 出现-->
                <p>岗位技能培训证明模板<el-button type="text" class="f-ml20">替换模板</el-button></p>
                <el-checkbox class="f-mt5 f-show">不开放学员和集体报名人员打印，仅管理员可打印</el-checkbox>
              </el-form-item>
              <!--
              <el-form-item label="培训证明模板：" required>
                <span class="f-ci">请先配置期别！</span>
              </el-form-item>
              <el-form-item label="培训证明模板：" required>
                <div>1.期别001：<el-button size="small" type="primary" plain>选择模板</el-button></div>
                <div class="f-mt5">2.期别002：<el-button size="small" type="primary" plain>选择模板</el-button></div>
              </el-form-item>
              -->
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">学习有效期配置</span>
        </div>
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">学习时间配置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt20">
              <el-form-item label="学习起止时间：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="指定培训时间"></el-radio>
                  <el-radio label="长期有效"></el-radio>
                </el-radio-group>
              </el-form-item>
              <!--选择 指定培训时间 后出现-->
              <el-form-item label="选择时间：" required>
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  class="form-l"
                >
                </el-date-picker>
                <div class="f-ci f-mt5">
                  注：此项为网授部分学习起止时间，面授部分学习时间以不同期别配置的培训时段为准
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">销售配置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt20">
              <el-form-item label="方案上架状态：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio>立即上架</el-radio>
                  <el-radio>暂不上架</el-radio>
                </el-radio-group>
                <div class="bg-gray f-mt10 f-plr15 f-pt5 f-pb5">
                  <div class="f-mb5">
                    方案计划上架时间：<el-date-picker v-model="form.date1" type="datetime" placeholder="请选择时间">
                    </el-date-picker>
                  </div>
                  <div class="f-mb5">
                    方案计划下架时间：<el-date-picker v-model="form.date1" type="datetime" placeholder="请选择时间">
                    </el-date-picker>
                  </div>
                </div>
              </el-form-item>
              <el-form-item label="展示在门户：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio>
                    展示
                    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-info m-tooltip-icon f-c9"></i>
                      <div slot="content">
                        配置培训方案是否展示在网校报名培训列表。展示门户分为学员门户报名列表和集体报名管理员查看培训班，设置为集体报名管理员可见，只生效于有开启线上集体报名的网校。<br />
                        不展示门户的情况下，如该方案有纳入专题，则会显示在专题门户上。默认为专题学员门户报名列表和集体报名管理员查看专题内培训班。集体报名管理员仅生效有开启线上集体报名的网校。
                      </div>
                    </el-tooltip>
                  </el-radio>
                  <el-radio>不展示门户</el-radio>
                </el-radio-group>
                <div class="bg-gray f-mt10 f-plr15 f-pt5 f-pb5">
                  <div class="f-flex">
                    <span><i class="f-ci f-mr5">*</i>展示用户：</span>
                    <el-checkbox-group v-model="form.type" style="display: inline-block;">
                      <el-checkbox label="学员门户可见" name="type"></el-checkbox>
                      <el-checkbox label="集体报名管理员可见" name="type"></el-checkbox>
                    </el-checkbox-group>
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div slot="label">
                  开放学员报名<el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>这里是文字提示文字提示</p>
                    </div> </el-tooltip
                  >：
                </div>
                <el-checkbox-group v-model="form.type">
                  <el-checkbox>
                    不开放
                    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-info m-tooltip-icon f-c9"></i>
                      <div slot="content">不开放学员报名，仅支持导入开通的方式报班。</div>
                    </el-tooltip>
                  </el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item>
                <div slot="label">
                  <span class="f-cr f-mr5">*</span>
                  <span class="f-vm">方案上架状态</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>合并报名的任意方案下架或不在报名时段内或关闭学员报名将导致学员无法合并报名，请谨慎操作。</p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <el-radio-group v-model="form.resource">
                  <el-radio label="立即上架"></el-radio>
                  <el-radio label="暂不上架"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="方案计划下架时间：">
                <el-date-picker
                  v-model="form.date1"
                  type="datetime"
                  placeholder="请输入方案计划下架时间"
                  class="f-ml10"
                >
                </el-date-picker>
              </el-form-item>
              <!--              <el-form-item label="开启方案时间：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="立即开启"></el-radio>
                  <el-radio label="暂不开启"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="方案计划开启时间：" required>
                <el-date-picker v-model="form.date1" type="datetime" placeholder="请输入计划的开启时间" class="f-ml10">
                </el-date-picker>
              </el-form-item>-->
              <!--              <el-form-item label="配置开启报名时间：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="立即开启"></el-radio>
                  <el-radio>
                    指定开启时间
                    &lt;!&ndash;选中后出现输入框&ndash;&gt;
                    <el-date-picker
                      v-model="form.date1"
                      type="datetime"
                      placeholder="请输入指定开启时间"
                      class="f-ml10"
                    >
                    </el-date-picker>
                  </el-radio>
                </el-radio-group>
              </el-form-item>-->
              <el-form-item>
                <div slot="label">
                  <span class="f-cr f-mr5">*</span>
                  <span class="f-vm">配置开启报名时间</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>合并报名的任意方案下架或不在报名时段内或关闭学员报名将导致学员无法合并报名，请谨慎操作。</p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <el-radio-group v-model="form.resource">
                  <el-radio label="立即开启"></el-radio>
                  <el-radio>
                    指定开启时间
                    <!--选中后出现输入框-->
                    <el-date-picker
                      v-model="form.date1"
                      type="datetime"
                      placeholder="请输入指定开启时间"
                      class="f-ml10"
                    >
                    </el-date-picker>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item>
                <div slot="label">
                  <span class="f-cr f-mr5">*</span>
                  <span class="f-vm">关闭报名时间</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>合并报名的任意方案下架或不在报名时段内或关闭学员报名将导致学员无法合并报名，请谨慎操作。</p>
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <el-radio-group v-model="form.resource">
                  <el-radio label="无关闭时间"></el-radio>
                  <el-radio>
                    指定关闭时间
                    <!--选中后出现输入框-->
                    <el-date-picker
                      v-model="form.date1"
                      type="datetime"
                      placeholder="请输入指定关闭时间"
                      class="f-ml10"
                    >
                    </el-date-picker>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="期别报名信息：" required>
                本方案下已添加3个期别<el-button type="text" class="f-ml10">[查看详情]</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">报名费用</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt20">
              <el-form-item label="培训费：">
                <el-input-number
                  v-model="num"
                  @change="handleChange"
                  :min="1"
                  :max="10"
                  label="描述文字"
                ></el-input-number>
                元 / 人
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar is-sticky-1 f-tc">
        <el-button>取消</el-button>
        <el-button type="primary">发布</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      const data = [
        {
          id: 1,
          label: '一级 1',
          children: [
            {
              id: 4,
              label: '二级 1-1'
            }
          ]
        }
      ]
      return {
        num: 1,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        input1: '1',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        tableData1: [
          {
            id: 1,
            field01: '这里是分类名称',
            hasChildren: true
          },
          {
            id: 2,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 3,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 4,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 5,
            field01: '分类名称1',
            hasChildren: true
          }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down'],
        data
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      },
      load(tree, treeNode, resolve) {
        setTimeout(() => {
          resolve([
            {
              id: 11,
              field01: '分类名称1-1'
            },
            {
              id: 12,
              field01: '分类名称1-2'
            }
          ])
        }, 1000)
      }
    }
  }
</script>
