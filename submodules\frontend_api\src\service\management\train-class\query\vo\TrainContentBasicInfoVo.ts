import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'

/**
 * 培训内容基本信息
 */
class TrainContentBasicInfoVo {
  /**
   * 培训方案id
   */
  schemeId = ''

  /**
   * 培训班商品id
   */
  commoditySkuId = ''

  /**
   * 培训方案名称
   */
  schemeName = ''

  /**
   * 培训方案类型 1：选课规则 2：自主选课
   */
  schemeType: TrainClassSchemeEnum = null

  /**
   * 班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuValueNameProperty: SkuPropertyResponseVo = new SkuPropertyResponseVo()

  /**
   * 开通方式 1:学员自主报名 2:集体报名 3:管理员导入
   */
  openType: number = null

  /**
   * 开通时间
   */
  openTime = ''

  /**
   * 考核成绩
   */
  assessScore: number = null

  /**
   * 考核学时
   */
  assessPeriod: number = null

  /**
   * 考核结果  -1:未知，培训尚未完成 1:培训合格 0:培训不合格
   */
  assessResult: number = null
}

export default TrainContentBasicInfoVo
