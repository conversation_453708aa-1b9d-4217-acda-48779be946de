import { LoginAccountInfo } from '@api/service/common/models/user/LoginAccountInfo'
// import { TeachAdministratorResponse } from '@api/gateway/btpx@BasicData-default'

export class TeachUnitManagerInfo {
  /**
   * 用户id
   */
  userId: string

  /**
   * 单位id
   */
  unitId: string

  /**
   * 姓名
   */
  name: string

  /**
   * 昵称
   */
  nickName: string

  /**
   * 唯一性类型,-1：未知，1：身份证
   */
  uniqueType: number

  /**
   * 唯一性值-
   */
  uniqueData: string

  /**
   * 状态，1：正常，2：冻结，3：注销
   */
  status: number
  /**
   * 登陆账号集合
   */
  loginAccount = new LoginAccountInfo()
  /**
   * 手机号
   */
  phoneNumber: string

  // static from(dto: TeachAdministratorResponse): TeachUnitManagerInfo {
  //   const info = new TeachUnitManagerInfo()
  //   info.userId = dto.userId
  //   info.unitId = dto.unitId
  //   info.name = dto.name
  //   info.nickName = dto.nickName
  //   info.uniqueType = dto.uniqueType
  //   info.uniqueData = dto.uniqueData
  //   info.status = dto.status
  //   if (dto.loginAccounts && dto.loginAccounts.length > 0) {
  //     // info.loginAccount.mergeFrom(dto.loginAccounts[0])
  //   }
  //   info.phoneNumber = dto.phoneNumber
  //   return info
  // }

  getStatusDesc(): string {
    switch (this.status) {
      case 1:
        return '正常'
      case 2:
        return '冻结'
      case 3:
        return '注销'
      default:
        return '未知'
    }
  }
}
