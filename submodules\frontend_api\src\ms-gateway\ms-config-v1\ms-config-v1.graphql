"""独立部署的微服务,K8S服务名:ms-config-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
type Query {
	"""根据header中的App-Authentication得到当前项目信息
		获取当前项目的前端配置信息,返回namespace对应的配置信息
		数据格式为
		{
		"ingress":[{"key":"",value:""}],//域名等配置
		"frontend":[{"key":"",value:""}]//前端专用的配置
		}
	"""
	getCurrentFrontendConfig:Map @optionalLogin
}

scalar List
