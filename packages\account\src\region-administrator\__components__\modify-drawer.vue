<template>
  <div>
    <el-drawer
      :title="drawerName"
      :visible.sync="drawerConfig.isShowDrawer"
      size="600px"
      custom-class="m-drawer"
      :close-on-press-escape="false"
      :wrapper-closable="false"
      :destroy-on-close="true"
      @closeDrawer="cancleAddUser"
    >
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form
              ref="drawerForm"
              :rules="rules"
              :model="regionAdministrator"
              label-width="auto"
              class="m-form f-mt20"
            >
              <el-form-item v-if="drawerConfig.isAdd" label="帐号：" prop="account">
                <el-input v-model="regionAdministrator.account" clearable placeholder="请输入帐号" />
              </el-form-item>
              <el-form-item v-if="!drawerConfig.isAdd" label="帐号：">
                {{ regionAdministrator.account }}
              </el-form-item>
              <el-form-item label="姓名：" prop="name">
                <el-input v-model="regionAdministrator.name" clearable placeholder="请输入姓名" />
              </el-form-item>
              <el-form-item label="管辖地区：" prop="region">
                <el-cascader
                  ref="elCascaderRef"
                  :props="props"
                  v-model="regionAdministrator.region"
                  :options="regionOptions"
                  :style="{ width: '100%' }"
                  collapse-tags
                  clearable
                  v-bind="$attrs"
                ></el-cascader>
              </el-form-item>
              <el-form-item label="手机号：" prop="phone">
                <el-input v-model="regionAdministrator.phone" clearable placeholder="请输入手机号" />
              </el-form-item>
              <el-form-item class="text f-co">注：初始密码为“dqgly123”</el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="cancleAddUser">取消</el-button>
                <el-button type="primary" @click="save">保存</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { Component, Vue, Ref, Prop, Emit } from 'vue-property-decorator'
  import DetailDrawer from '@hbfe/jxjy-admin-account/src/region-administrator/__components__/detail-drawer.vue'
  import RegionAdministratorList from '@api/service/management/authority/region-administrator/RegionAdministratorList'
  import RegionAdministrator from '@api/service/management/authority/region-administrator/RegionAdministratorItem'
  import { cloneDeep } from 'lodash'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import { ElForm } from 'element-ui/types/form'
  import MutationServiceProvider from '@api/service/management/authority/service-provider/mutation/MutationServiceProvider'
  import { UnbindPhoneRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
  import { bind, debounce } from 'lodash-decorators'
  @Component({
    components: {
      DetailDrawer
    }
  })
  export default class extends Vue {
    @Ref('drawerForm')
    drawerFormRef: ElForm
    /**
     * 地区选择器配置
     */
    @Prop({ type: Array, default: () => new Array<RegionTreeVo>() })
    regionOptions: Array<RegionTreeVo>
    /**
     * 查询方法
     */
    regionAdministratorList = new RegionAdministratorList()
    /**
     * 账号模型
     */
    regionAdministrator = new RegionAdministrator()
    /**
     * 修改初始数据
     */
    initItem = new RegionAdministrator()
    mutationServiceProvider = new MutationServiceProvider()
    /**
     * 地区选择器配置
     */
    props = {
      lazy: false,
      value: 'id',
      label: 'name',
      disabled: 'disabled',
      checkStrictly: true
    }
    /**
     * 抽屉配置
     */
    drawerConfig = {
      isShowDrawer: false,
      isAdd: true
    }
    /**
     * 新增/修改校验
     */
    rules = {
      account: [{ required: true, message: '请输入账号', trigger: 'blur' }],
      name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
      phone: [{ required: true, validator: this.validatePhone, trigger: 'blur' }],
      region: [{ required: true, message: '请选择管辖地区', trigger: 'change' }]
    }
    //校验手机号格式
    regPhone = new RegExp(/^[1]([3-9])[0-9]{9}$/)
    get drawerName() {
      return this.drawerConfig.isAdd ? '新增地区管理员' : '修改地区管理员'
    }

    /**
     * 手机号校验规则
     */
    validatePhone(rule: any, value: any, callback: any) {
      if (!this.regionAdministrator.phone) {
        callback(new Error('请输入手机号码'))
      } else if (!this.regPhone.test(this.regionAdministrator.phone)) {
        callback(new Error('请输入11位真实有效手机号'))
      } else {
        callback()
      }
    }
    /**
     * 打开新增抽屉
     */
    showAddDrawer() {
      this.drawerConfig.isShowDrawer = true
      this.drawerConfig.isAdd = true
      this.regionAdministrator = new RegionAdministrator()
    }
    /**
     * 打开修改抽屉
     */
    showModifyDrawer(item: RegionAdministrator) {
      this.drawerConfig.isAdd = false
      this.regionAdministrator = cloneDeep(item)
      this.initItem = cloneDeep(item)
      this.drawerConfig.isShowDrawer = true
    }

    /**
     * 取消新增/修改管理员
     */
    cancleAddUser() {
      this.drawerConfig.isShowDrawer = false
      this.regionAdministrator = new RegionAdministrator()
      this.initItem = new RegionAdministrator()
    }
    /**
     * 保存
     */
    @bind
    @debounce(200)
    async save() {
      this.drawerFormRef.validate(async (boolean: boolean, value: object) => {
        if (boolean) {
          // const regTel = /^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/
          // if (this.regionAdministrator.phone && !regTel.test(this.regionAdministrator.phone)) {
          //   this.$confirm('请输入11位真实有效手机号', '提示', {
          //     confirmButtonText: '我知道了',
          //     showCancelButton: false
          //   })
          //   return
          // }
          if (!this.drawerConfig.isAdd && this.initItem.phone && this.regionAdministrator.phone === '') {
            const unbindPhoneParam = new UnbindPhoneRequest()
            unbindPhoneParam.phone = this.initItem.phone
            unbindPhoneParam.userId = this.initItem.userId
            const unbindRes = await this.mutationServiceProvider.unbindPhone(unbindPhoneParam)
            if (!unbindRes?.status?.isSuccess()) {
              this.$message.error('解绑手机失败')
              return
            }
          }
          const res = this.drawerConfig.isAdd
            ? await this.regionAdministrator.addRegionAdministrator()
            : await this.regionAdministrator.updateRegionAdministrator(this.initItem)
          if (res.status.isSuccess() && res.data?.code === '200') {
            this.$emit('is-success', this.drawerConfig.isAdd)
            setTimeout(() => {
              this.drawerConfig.isAdd ? this.$message.success('创建成功') : this.$message.success('修改成功')
              this.cancleAddUser()
            }, 800)
          } else {
            if (res?.data?.code == '20001') {
              this.$message.error('当前手机号已被使用')
            } else if (res?.data?.code == '10001') {
              this.$message.error('手机号不能为空')
            } else {
              const errMsg = res.data?.message || res.status?.errors[0]?.message || '保存失败'
              this.$message.error(errMsg)
            }
          }
        }
      })
    }
  }
</script>
