<template>
  <el-main>
    <div class="m-questionnaire">
      <div class="content">
        <div class="header">问卷调查名称问卷调查名称</div>
        <div class="questionnaire-bd">
          <!--单选题-->
          <div class="question">
            <div class="tit">
              <span class="num">1、</span>
              <div class="f-flex-sub">
                <span class="type">单选题</span>
                <span class="text">您的教龄是</span>
              </div>
              <div class="tag"><i class="icon el-icon-warning-outline"></i>本题必选</div>
            </div>
            <div class="option">
              <el-radio-group v-model="radio2">
                <el-radio label="1">A. 5年及以下</el-radio>
                <el-radio label="2">B. 6-10年</el-radio>
                <el-radio label="3">C. 11-15年</el-radio>
                <el-radio label="4">D. 16-20年</el-radio>
                <el-radio label="5">E. 21年及以上</el-radio>
                <el-radio label="5">F. 其他（选填）<span class="u-underline">用户填写的内容</span></el-radio>
                <el-radio label="5">F. 其他（必填）<span class="u-underline">用户填写的内容</span></el-radio>
              </el-radio-group>
            </div>
          </div>
          <!--单选题-教师评价-->
          <div class="question">
            <div class="tit">
              <span class="num">1、</span>
              <div class="f-flex-sub">
                <span class="type">单选题</span>
                <span class="text">本次培训您最喜欢的教师是？</span>
              </div>
            </div>
            <div class="option">
              当前试题为教师评价试题，选项将读取引用此问卷模板的方案或期别中，课程授课教师名称，无需进行配置。
            </div>
          </div>
          <!--多选题-->
          <div class="question">
            <div class="tit">
              <span class="num">2、</span>
              <div class="f-flex-sub">
                <span class="type">多选题</span>
                <span class="text">您的教龄是 <i class="f-cr">*</i></span>
              </div>
              <div class="tag"><i class="icon el-icon-warning-outline"></i>本题必选</div>
            </div>
            <div class="option">
              <el-checkbox-group v-model="checkList">
                <el-checkbox label="A">A. 企业法人和非企业法人备选项</el-checkbox>
                <el-checkbox label="B">B. 机关法人、企业法人和社团法人</el-checkbox>
                <el-checkbox label="C">C. 一般法人和特别法人</el-checkbox>
                <el-checkbox label="D">D. 营利法人、非营利法人和特别法人</el-checkbox>
              </el-checkbox-group>
            </div>
          </div>
          <!--简答题-->
          <div class="question">
            <div class="tit">
              <span class="num">3、</span>
              <div class="f-flex-sub">
                <span class="type">简答题</span>
                <span class="text">您的教龄是 <i class="f-cr">*</i></span>
              </div>
              <div class="tag"><i class="icon el-icon-warning-outline"></i>本题必选</div>
            </div>
            <div class="option">
              <el-input type="textarea" :rows="8" placeholder="请输入内容" v-model="textarea2"></el-input>
            </div>
          </div>
          <!--量表题-->
          <div class="question">
            <div class="tit">
              <span class="num">3、</span>
              <div class="f-flex-sub">
                <span class="type">量表题</span>
                <span class="text">标题标题标题 <i class="f-cr">*</i></span>
              </div>
              <div class="tag"><i class="icon el-icon-warning-outline"></i>本题必选</div>
            </div>
            <div class="f-p20">
              <span class="f-mr30">自定义名称A</span>
              <el-radio v-model="radio" label="1" class="f-mr20">1</el-radio>
              <el-radio v-model="radio" label="2" class="f-mr20">2</el-radio>
              <el-radio v-model="radio" label="3" class="f-mr20">3</el-radio>
              <el-radio v-model="radio" label="4" class="f-mr20">4</el-radio>
              <el-radio v-model="radio" label="5" class="f-mr20">5</el-radio>
              <span>自定义名称B</span>
            </div>
          </div>
          <div class="f-tc f-mt25">
            <el-button type="primary" round class="submit">提交问卷</el-button>
          </div>
        </div>
      </div>
      <!--答题卡 打开时添加 is-show-->
      <div class="question-card is-show">
        <div class="cont">
          <!--打开按钮-->
          <div class="btn"></div>
          <div class="tips">
            <div class="item"><span class="type"></span>未答</div>
            <div class="item"><span class="type is-answered"></span>已答</div>
            <div class="item"><span class="type is-choose"></span>选答</div>
            <div class="item"><span class="type is-must"></span>必答</div>
            <div class="item"><el-button type="primary" size="mini">收起答题卡</el-button></div>
          </div>
          <div class="nums">
            <span class="num is-answered">1</span>
            <span class="num is-answered">2</span>
            <span class="num is-choose">3</span>
            <span class="num is-must">4</span>
            <span class="num">5</span>
          </div>
        </div>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        input1: '对',
        input2: '错',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
