<template>
  <el-drawer title="批量更新方案" :visible.sync="visibleSync" size="600px" custom-class="m-drawer" @close="close">
    <div class="drawer-bd">
      <el-row type="flex" justify="center">
        <el-col :span="24">
          <el-alert type="warning" :closable="false" class="m-alert">
            <p>温馨提示：</p>
            <p>1. 如需批量导入培训班，请先下载 <span class="f-ci">更新培训方案模板</span>。</p>
            <p>
              2.
              【方案上架状态】【方案计划上架时间】【方案计划下架时间】【学习开始时间】、【学习结束时间】、【报名开始时间】、【报名结束时间】、【是否展示在门户】、【是否仅导入开通】支持批量更新；
            </p>
          </el-alert>
          <el-form label-width="auto" class="m-form f-mt20">
            <el-steps direction="vertical" :active="4" class="m-vertical-steps" style="padding: 20px 0">
              <el-step title="下载更新培训方案模板，按表格中提示说明填写信息。">
                <div slot="description">
                  <el-button
                    @click="downloadTemplate"
                    type="primary"
                    size="small"
                    plain
                    class="f-mt5"
                    icon="el-icon-download"
                  >
                    下载模板
                  </el-button>
                </div>
              </el-step>
              <el-step title="上传填写好的更新方案表格">
                <div slot="description">
                  <min-upload-file
                    style="width: unset"
                    v-model="hbFileUploadResponse"
                    :file-type="1"
                    :is-protected="true"
                  >
                    <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                      选择文件
                    </el-button>
                  </min-upload-file>
                  <div slot="tip" class="el-upload__tip">
                    <i class="el-icon-warning"></i>
                    <span class="txt">导入的文件格式必须为xls,xlsx文件</span>
                  </div>
                </div>
              </el-step>
            </el-steps>
            <el-form-item class="m-btn-bar f-tc">
              <el-button @click="visibleSync = false">取消</el-button>
              <el-button type="primary" :loading="loading" @click="sure">确定</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, PropSync } from 'vue-property-decorator'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import MinUploadFile from '@hbfe/jxjy-admin-components/src/min-upload-file.vue'
  import BatchUpdateScheme from '@api/service/management/train-class/mutation/BatchUpdateScheme'
  import { bind, debounce } from 'lodash-decorators'

  @Component({
    components: { MinUploadFile }
  })
  export default class extends Vue {
    @PropSync('visible', {
      type: Boolean,
      default: false
    })
    visibleSync: boolean

    loading = false

    /**
     * 文件上传之后的回调参数
     */
    hbFileUploadResponse = new HBFileUploadResponse()

    /**
     * 上传批量更新方案表格充血模型
     */
    batchUpdateScheme = new BatchUpdateScheme()

    /**
     * 下载导入开班模板
     */
    @bind
    @debounce(1000)
    async downloadTemplate() {
      const link = document.createElement('a')
      await this.batchUpdateScheme.getTemplateUrl()
      const url = this.batchUpdateScheme.templateUrl
      const resolver = this.$router.resolve({
        name: '/mfs' + url
      })
      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      const fileName = '更新培训方案模板'
      link.setAttribute('download', fileName)
      document.body.appendChild(link)
      link.click()
      link.remove()
    }

    async sure() {
      if (!this.hbFileUploadResponse.url) {
        this.$message.warning('请先上传文件')
        return
      }
      this.loading = true
      this.batchUpdateScheme.fileName = this.hbFileUploadResponse.fileName
      this.batchUpdateScheme.filePath = this.hbFileUploadResponse.url
      await this.batchUpdateScheme.batchUpdateScheme()
      this.loading = false
      this.visibleSync = false

      this.$emit('showImportBatchUpdateSchemeDialog')
    }

    close() {
      this.visibleSync = false
      this.hbFileUploadResponse = new HBFileUploadResponse()
    }
  }
</script>
