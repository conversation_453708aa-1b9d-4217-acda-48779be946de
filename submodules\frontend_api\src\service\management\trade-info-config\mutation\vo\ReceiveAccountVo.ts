import { ReceiveAccountConfigResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

class ReceiveAccountVo {
  // 收款账号id
  id = ''
  // 支付方式 1-线上 2-线下
  accountType = 0
  // 支付账号类型 支付宝 微信 支付宝H5 微信H5
  paymentChannelId = ''
  // 收款账号别名
  name = ''
  // 开户账号信息 微信-商户号 支付宝-支付宝账号  线下-开户行
  merchantName = ''
  // 是否被选中
  isSelected = true
  // 是否禁用选中 用于已配置的销售渠道中的列表展示
  disableSelect = true

  static form(res: ReceiveAccountConfigResponse) {
    const receiveAccount = new ReceiveAccountVo()
    receiveAccount.id = res.id
    receiveAccount.accountType = res.accountType
    receiveAccount.paymentChannelId = res.paymentChannelId
    receiveAccount.name = res.name
    receiveAccount.merchantName = res.accountNo
    return receiveAccount
  }
}
export default ReceiveAccountVo
