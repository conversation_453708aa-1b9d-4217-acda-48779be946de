import UserInfo from './models/UserInfo'
import platformUserGateway, { LoginType, UserBindedThirdAccountDTO, WXInfoQueryParam } from '@api/gateway/PlatformUser'
import msAccountGateway, { CurrentUserUpdateRequest, UserUpdateRequest } from '@api/gateway/ms-account-v1'
import { WaitToUpdateUserInfo } from './models/WaitToUpdateUserInfo'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import WXInfo from './models/WXInfo'
import WJWStudentCreate from '@api/service/customer/user/query-user/models/WJWStudentCreate'
import { Module as Mod } from 'vuex'
import UserConstantsAdapter from '@api/service/common/common-user/UserConstantsAdapter'
import weChatMS, {
  GetDecodeTelephoneRequest,
  GetUserInfoRequest,
  TelephoneData,
  WXAppletInfoQueryParam
} from '@api/gateway/ms-wechat-v1'
import { UnAuthorize } from '@api/Secure'
import Vue from 'vue'
import ConfigCenter from '@api/service/common/config-center/ConfigCenter'
import Response, { ResponseStatus } from '../../../../Response'

const defaultDeadImg = 'portal/jskq/assets/images/head-default.jpg'

interface UserState {
  /**
   * 用户信息。存放当前登录帐号的信息
   */
  userInfo: UserInfo
  /**
   * 是否登陆
   */
  isLogin: boolean
  /**
   * 累计登录天数
   */
  userLoginDay: string
  /**
   * 用户第三方账号信息
   */
  thirdAccount: Array<UserBindedThirdAccountDTO>
  wxInfo: WXInfo
  /**
   * 用户数
   */
  userCount: { [key: string]: number }
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerUserModule', store })
class UserModule extends VuexModule implements UserState {
  userCount: { [p: string]: number }
  userInfo = new UserInfo()
  isLogin = false
  userLoginDay = ''
  thirdAccount = new Array<UserBindedThirdAccountDTO>()
  wxInfo = new WXInfo('', '')
  /**
   * 钉钉用户ID
   */
  ddUserId = ''
  WXPurephone = new TelephoneData()

  isUserPerfectedPhone = false

  constructor(module: Mod<ThisType<any>, any>) {
    super(module)
    store.subscribe(mutation => {
      if (mutation.type?.indexOf('CURRENT_USER_INFO_CHANGE') !== -1) {
        this.queryUserInfo(true)
      }
    })
  }

  //region action

  /* 查询用户是否完善手机号 */
  @Action
  async isUserPerfectedPhoneNumber() {
    const response = await platformUserGateway.isUserPerfectedPhoneNumber()
    if (response.status.isSuccess()) {
      this.SET_USER_PERFECTED_PHONE(response?.data)
    }
    return response.status
  }

  /* 用户完善手机号接口 */
  @Action
  async userPerfectPhoneNumber(captchaToken: string): Promise<ResponseStatus> {
    const { status } = await platformUserGateway.userPerfectPhoneNumber(captchaToken)
    return status
  }

  /* 用户绑定微信小程序 */
  @Action
  async bindUserWXMiniProgramForOpenAPI(params: {
    openId: string
    wxNickname: string
    headImgUrl: string
    unionId?: string
  }): Promise<ResponseStatus> {
    const { status } = await platformUserGateway.bindUserWXMiniProgramForOpenAPI(params)
    return status
  }

  /* 微信小程序用户完善手机号接口 */
  @Action
  async xwMiniProgramUserPerfectPhoneNumber(phoneNum: string): Promise<ResponseStatus> {
    const { status } = await platformUserGateway.xwMiniProgramUserPerfectPhoneNumber(phoneNum)
    if (status.isSuccess()) {
      this.SET_USER_PHONE_NUMBER(phoneNum)
    }
    return status
  }

  /**
   * 请求加载用户信息
   */
  @Action
  async queryUserInfo(reload?: boolean): Promise<ResponseStatus> {
    if (!reload && this.userInfo.userId) {
      return new ResponseStatus(200)
    }
    const response = await platformUserGateway.getCurrentUserInfo()
    if (response.status.code === 401) {
      // 登录过期
      this.setUserInfo(new UserInfo())
      this.setIsLogin(false)
    }
    if (response.status.code === 200 && response.data) {
      // 请求成功
      const user = UserInfo.from(response.data)
      this.setUserInfo(user)
      if (user?.userId) {
        this.setIsLogin(true)
      } else {
        this.setIsLogin(false)
      }
    }
    return response.status
  }

  /**
   * 统计用户的登录天数
   * @param ctx
   */
  @Action
  async queryCountUserLoginDay() {
    // todo 平台目前没用到，暂未对接
    // const response = await platformUserGateway.countUserLoginDay()
    // if (response.status.isSuccess()) {
    //   if (this.userInfo?.userId) {
    //     this.SET_USER_LOGIN_DAY({ userId: this.userInfo?.userId, day: response.data })
    //   }
    // }
    // return response.status
  }

  // todo 接口请改用updateCurrentPhoneWithPhoneCaptchaToken
  /**
   * 修改用户手机号
   */
  @Action
  async doModifyUserInfoTel(phoneNumber: string): Promise<ResponseStatus> {
    const dto = new UserUpdateRequest()
    dto.phone = phoneNumber
    const response = await msAccountGateway.updateUser(dto)
    if (response.status.isSuccess()) {
      this.setUserPhoneNumber(phoneNumber)
    }
    return response.status
  }

  /**
   * 修改当前用户手机号
   */
  @Action
  async updateCurrentPhoneWithPhoneCaptchaToken(captchaToken: string) {
    const { status } = await platformUserGateway.updateCurrentPhoneWithPhoneCaptchaToken(captchaToken)
    return status
  }

  /**
   * 请求获取微信电话
   * @param param
   */
  @Action
  async getWxPurePhoneNumber(param: GetDecodeTelephoneRequest): Promise<ResponseStatus> {
    //const response = await platformUserGateway.getWXUserInfo(param)
    const response = await weChatMS.getDecodeTelephone(param)
    this.SET_WEIXIN_PHONE(response.data)
    return response.status
  }

  /**
   * 请求获取微信信息
   * @param param
   */
  @Action
  @UnAuthorize
  async getWXUserInfo(param: WXInfoQueryParam): Promise<ResponseStatus> {
    //const response = await platformUserGateway.getWXUserInfo(param)
    if (param.type == 0) {
      const requestParams = new GetUserInfoRequest()
      requestParams.appId = param.appId
      requestParams.code = param.code
      requestParams.lang = param.lang
      const response = await weChatMS.getUserInfo(requestParams)
      if (response.status.isSuccess()) {
        this.SET_WXINFO(new WXInfo(response.data.unionId, response.data.openId))
      }
      return response.status
    } else if (param.type == 1) {
      const requestParams = new WXAppletInfoQueryParam()
      requestParams.appId = param.appId
      requestParams.code = param.code
      requestParams.encryptedData = param.encryptedData
      requestParams.iv = param.iv
      const response = await weChatMS.getWXAppletUserInfo(requestParams)
      if (response.status.isSuccess()) {
        this.SET_WXINFO(new WXInfo(response.data.unionId, response.data.openId))
      }
      return response.status
    } else {
      return new ResponseStatus(500, '未适配的类型')
    }
  }

  /**
   * 请求获取钉钉信息
   * @param param
   */
  @Action
  async getDingDingInfo(code: string) {
    // todo 平台目前没用到，暂未对接
    // const response = await DataGateway.getDingTalkUserIdByTempAuthCode(code)
    // if (response.status.isSuccess()) {
    //   this.SET_DD_USERID(response.data)
    // }
    // return response.status
  }

  /**
   * 确定用户开启小程序通知权限
   * @param userId
   */
  @Action
  async bindingUserUniAppMessageAuthority(userId: string): Promise<ResponseStatus> {
    const res = await msAccountGateway.enableNoticeForWebChatApplet()
    if (!res.status.isSuccess()) {
      return res.status
    }
    const sendMessageResponse = await platformUserGateway.sendBindingSuccessMessage()
    return sendMessageResponse.status
  }

  /**
   * 关闭用户开启小程序通知权限
   * @param userId
   */
  @Action
  async unBindingUserUniAppMessageAuthority(userId: string): Promise<ResponseStatus> {
    const res = await msAccountGateway.disableNoticeForWebChatApplet()
    return res.status
  }

  @Action
  setWXInfo(param: WXInfo) {
    this.SET_WXINFO(param)
  }

  /**
   * 修改用户信息
   */
  @Action
  async doModifyUserInfo(update: WaitToUpdateUserInfo): Promise<ResponseStatus> {
    const request = new CurrentUserUpdateRequest()
    request.name = update.name
    request.nickName = update.nickName
    // request.idCard = update.uniqueData
    request.photo = update.displayPhotoUrl
    request.gender = UserConstantsAdapter.getGender(update.sex)
    request.phone = update.phoneNumber
    if (update.areaPath) {
      request.area = update.areaPath
    } else {
      request.area = ''
    }
    request.address = update.address
    request.companyName = update.workUnitName
    if (update.userCategories) {
      request.peoples = update.userCategories.join(',')
    } else {
      request.peoples = ''
    }
    // request.email = update.email

    const response = await msAccountGateway.updateUserByCurrent(request)
    return response.status
  }

  /**
   * 学员注册
   * @param payload
   */
  @Action
  async doRegisterUser(payload: WJWStudentCreate) {
    // todo 目前平台未用到、后端未提供接口
    //   const param: BTPXStudentCreateRequest = new BTPXStudentCreateRequest()
    //   Object.assign(param, payload)
    //   const response = await platformUserGateway.btpxCreateStudent(param)
    //   return response.status
    return new ResponseStatus(500)
  }

  /**
   * 微信公众号openId和unionId绑定当前账号
   * @param params
   */
  @Action
  async bindWXOfficialAccountForOpenAPI(params: {
    openId: string
    unionId: string
    wxNickname: string
    dontLoad?: boolean
  }): Promise<ResponseStatus> {
    const response = await platformUserGateway.bindWXOfficialAccountForOpenAPI(params)
    if (response.status.isSuccess() && !params.dontLoad) {
      await this.loadThirdAccount()
    }
    return response.status
  }

  /**
   * 微信小程序openId和unionId绑定当前账号
   * 不用密码
   * @param params
   */
  @Action
  async bindWXMiniProgramForOpenAPI(params: {
    openId: string
    unionId: string
    wxNickname: string
    dontLoad?: boolean
  }): Promise<ResponseStatus> {
    const response = await platformUserGateway.bindWXMiniProgramForOpenAPI(params)
    if (response.status.isSuccess() && !params.dontLoad) {
      await this.loadThirdAccount()
    }
    return response.status
  }

  /**
   * 解绑微信
   * @param ctx
   */
  @Action
  async unBindWX(): Promise<ResponseStatus> {
    const response = await platformUserGateway.unBindCurrentUserWX()
    if (response.status.isSuccess()) {
      await this.loadThirdAccount()
    }
    return response.status
  }

  /**
   * 验证帐号是否存在，验证手机号是否存在。
   */
  @Action
  async isPhoneNumberAccountExists(phoneNumber: string): Promise<Response<boolean>> {
    return platformUserGateway.identityExists({
      identityType: LoginType.PHONE,
      identity: phoneNumber
    })
  }

  /**
   * 手机号是否被学员使用
   */
  @Action
  async isPhoneNumberExistsInStudent(phoneNumber: string): Promise<Response<boolean>> {
    return platformUserGateway.isPhoneNumberExists({ phoneNumber: phoneNumber })
  }

  /**
   * 手机号是否被学员使用，可以排除指定用户
   */
  @Action
  async isPhoneNumberExistsInStudentExcept(params: {
    phoneNumber: string
    exceptUserId?: string
  }): Promise<Response<boolean>> {
    return platformUserGateway.isPhoneNumberExists(params)
  }

  /**
   * 验证帐号是否存在，验证身份证号是否存在。
   */
  @Action
  async isIdentityCardAccountExists(identityCard: string): Promise<Response<boolean>> {
    return platformUserGateway.identityExists({
      identityType: LoginType.IDENTITYCARD,
      identity: identityCard
    })
  }

  /**
   * 验证是否绑定了指定类型的第三方账号
   * @param ctx
   * @param bindType；平台开放注册:11 ， QQ :21 ,微博 :31, 微信WEB:41，微信公众号:42，微信小程序:43，微信APP:44，其他:51
   */
  @Action
  async isBindThirdAccount(bindType: number): Promise<Response<boolean>> {
    const response = await platformUserGateway.getCurrentUserBindedThirdAccounts()
    const list: Array<UserBindedThirdAccountDTO> = response.data
    const result = new Response<boolean>()
    result.status = response.status
    if (response.status.isSuccess()) {
      result.data = !!list.find(dto => UserConstantsAdapter.getConnectType(dto.openPlatformType) === bindType)
    }
    return result
  }

  /**
   * 加载当前用户的第三方账号
   */
  @Action
  async loadThirdAccount() {
    const response = await platformUserGateway.getCurrentUserBindedThirdAccounts()
    const list: Array<UserBindedThirdAccountDTO> = response.data
    if (response.status.isSuccess()) {
      this.SET_THIRD_ACCOUNT(list)
    }
    return response.status
  }

  /**
   * 验证指定微信token是否绑定账号
   * @param wxToken
   */
  @Action
  async haveBindWXByTokenForOpenAPI(wxToken: string): Promise<Response<boolean>> {
    // todo 平台目前没用到，后端未提供接口
    // const response = await platformUserGateway.haveBindWXByTokenForOpenAPI(wxToken)
    // return response
    return new Response<boolean>()
  }

  /**
   * 是否绑定微信第三方账号
   * @param params
   */
  @Action
  async haveBindWXByOpenIdAndUnionIdForOpenAPI(params: {
    openId?: string
    unionId?: string
    connectType: number
    wxNickname?: string
  }): Promise<Response<boolean>> {
    return await platformUserGateway.haveBindWXByOpenIdAndUnionIdForOpenAPI({
      openId: params.openId,
      unionId: params.unionId,
      connectType: UserConstantsAdapter.getOpenPlatformType(params.connectType),
      wxNickname: params.wxNickname
    })
  }

  /**
   * 找回密码，传入验证码校验通过后获得的token和新密码
   */
  @Action
  async doRetrievePassword(payload: { captchaToken: string; newPassword: string }): Promise<ResponseStatus> {
    // todo 平台目前没用到，后端未提供接口
    // const response = await platformUserGateway.resetPasswordWithPhoneCaptchaToken(payload)
    // return response.status
    return new ResponseStatus(500)
  }

  /**
   * 修改密码
   */
  @Action
  async doModifyPassword(payload: { oldPassword: string; newPassword: string }): Promise<ResponseStatus> {
    // todo 平台目前没用到，后端未提供接口
    // const response = await platformUserGateway.changePassword(payload)
    // return response.status
    return new ResponseStatus(500)
  }

  /**
   * 获取是否登录
   */
  @Action
  async getIsLogin() {
    const response = await platformUserGateway.getCurrentUserInfo()
    const isLogin = response.data !== null
    this.setIsLogin(isLogin)
  }

  /**
   * 退出登录
   */
  @Action
  doEXitLogin() {
    // 销毁token
    if (localStorage) {
      localStorage.removeItem('Access-Token')
      localStorage.removeItem('Refresh-Token')
    }
    this.setUserInfo(new UserInfo())
    this.setIsLogin(false)
  }

  /**
   * 登陆成功
   */
  @Action
  doLoginSuccess(userInfo: UserInfo) {
    this.setUserInfo(userInfo)
    this.setIsLogin(true)
  }

  //endregion

  //region mutation
  /*
   用户完善手机号成功，将手机号设置到用户个人信息
  */
  @Mutation
  private SET_USER_PHONE_NUMBER(phone: string) {
    Vue.set(this.userInfo, 'phoneNumber', phone)
  }

  /*
  设置用户头像地址
  */
  @Mutation
  SET_USER_DISPLAY_PHOTO_URL(photoUrl: string) {
    Vue.set(this.userInfo, 'displayPhotoUrl', photoUrl)
  }

  @Mutation
  private SET_USER_PERFECTED_PHONE(isPerfectPhone: boolean) {
    this.isUserPerfectedPhone = isPerfectPhone
  }

  @Mutation
  private SET_WXINFO(param: WXInfo) {
    if (!param) {
      param = new WXInfo('', '')
    }
    this.wxInfo = param
  }

  @Mutation
  private SET_DD_USERID(ddUserId: string) {
    if (!ddUserId) {
      ddUserId = ''
    }
    this.ddUserId = ddUserId
  }

  @Mutation
  private setUserPhoneNumber(phoneNumber: string) {
    this.userInfo.phoneNumber = phoneNumber
  }

  @Mutation
  SET_CURRENT_USER_PHOTO(photo: string) {
    this.userInfo.displayPhotoUrl = photo
  }

  @Mutation
  SET_USER_LOGIN_DAY(params: { userId: string; day: number }) {
    this.userLoginDay = `${params.userId}#${params.day}`
  }

  @Mutation
  private setUserPhotoUrl(displayPhotoUrl: string) {
    this.userInfo.displayPhotoUrl = displayPhotoUrl
  }

  @Mutation
  SET_THIRD_ACCOUNT(thirdAccount: Array<UserBindedThirdAccountDTO>) {
    this.thirdAccount = thirdAccount?.filter(p => p.available) || []
  }

  @Mutation
  private setUserInfo(userInfo: UserInfo) {
    this.userInfo = userInfo
  }

  @Mutation
  private setIsLogin(isLogin: boolean) {
    this.isLogin = isLogin
  }

  //endregion

  //region get
  get getUserPhotoUrl() {
    const prefix = ConfigCenter.getApplicationByName('application.resourcesPrefix')
    return this.userInfo.displayPhotoUrl ? `${prefix}${this.userInfo.displayPhotoUrl}` : defaultDeadImg
  }

  get getUserLoginDay() {
    const userId = this.userInfo?.userId
    const userLoginDay = this.userLoginDay
    if (userLoginDay) {
      const array = userLoginDay.split('#')
      if (array?.length > 0 && array[0] === userId) {
        return array[1]
      }
    }
    return 0
  }

  /**
   * 是否绑定指定类型的第三方账号
   * @param connectType 41：微信登录，42：微信公众号，43：微信小程序
   */
  get isBind() {
    return (connectType: number) => {
      return !!this.thirdAccount?.find(dto => UserConstantsAdapter.getConnectType(dto.openPlatformType) === connectType)
    }
  }

  /**
   * 绑定指定类型的第三方账号信息
   * @param connectType 41：微信登录，42：微信公众号，43：微信小程序
   */
  get getBindInfo() {
    return (connectType: number) => {
      return this.thirdAccount?.find(dto => UserConstantsAdapter.getConnectType(dto.openPlatformType) === connectType)
    }
  }

  /**
   * 是否是集体缴费管理员
   */
  get isCollectiveAdmin() {
    const normalCollectiveAdminRoleId = ConfigCenter.getApplicationByName(
      'applicationDiff.BasicData.normalCollectiveAdminRoleId'
    )
    const tempCollectiveAdminRoleId = ConfigCenter.getApplicationByName(
      'applicationDiff.BasicData.tempCollectiveAdminRoleId'
    )
    return this.userInfo?.roles?.some(x => x.id === normalCollectiveAdminRoleId || x.id === tempCollectiveAdminRoleId)
  }

  /**
   * 是否是学员
   */
  get isStudent() {
    const normalStudentRoleId = ConfigCenter.getApplicationByName('applicationDiff.BasicData.normalStudentRoleId')
    const tempStudentRoleId = ConfigCenter.getApplicationByName('applicationDiff.BasicData.tempStudentRoleId')
    return this.userInfo?.roles?.some(x => x.id === normalStudentRoleId || x.id === tempStudentRoleId)
  }

  //endregion
  @Mutation
  private SET_WEIXIN_PHONE(phoneNumberObj: TelephoneData) {
    this.WXPurephone = phoneNumberObj
  }
}

export default getModule(UserModule)
