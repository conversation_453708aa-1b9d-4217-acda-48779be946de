<template>
  <div>
    <div v-if="getSkuPropertyName('industry')">行业：{{ getSkuPropertyName('industry') }}</div>
    <div v-if="getSkuPropertyName('region')">地区：{{ getSkuPropertyName('region') }}</div>
    <div v-if="getSkuPropertyName('jobLevel')">技术等级：{{ getSkuPropertyName('jobLevel') }}</div>
    <div v-if="getSkuPropertyName('subjectType')">科目类型：{{ getSkuPropertyName('subjectType') }}</div>
    <div v-if="getSkuPropertyName('trainingCategory')">培训类别：{{ getSkuPropertyName('trainingCategory') }}</div>
    <div v-if="getSkuPropertyName('trainingMajor')">培训专业：{{ getSkuPropertyName('trainingMajor') }}</div>
    <div v-if="getSkuPropertyName('trainingObject')">培训对象：{{ getSkuPropertyName('trainingObject') }}</div>
    <div v-if="getSkuPropertyName('positionCategory')">岗位类别：{{ getSkuPropertyName('positionCategory') }}</div>
    <div v-if="getSkuPropertyName('learningPhase')">学段：{{ getSkuPropertyName('learningPhase') }}</div>
    <div v-if="getSkuPropertyName('discipline')">学科：{{ getSkuPropertyName('discipline') }}</div>
    <div v-if="getSkuPropertyName('year')">培训年度：{{ getSkuPropertyName('year') }}</div>
    <div v-if="getSkuPropertyName('practitionerCategory') && getSkuPropertyName('certificatesType')">
      执业类别：{{ getSkuPropertyName('certificatesType') }}-{{ getSkuPropertyName('practitionerCategory') }}
    </div>
  </div>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import AntiSchemeItem from '@api/service/management/train-class/query/vo/AntiSchemeItem'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'

  @Component({})
  export default class extends Vue {
    // 传入sku列表
    @Prop({
      type: Object,
      default: () => {
        return new AntiSchemeItem('', '', new SkuPropertyResponseVo(), 0, false)
      }
    })
    skuItem: AntiSchemeItem

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(type: string): string {
      if (this.skuItem.sku[type]?.skuPropertyName) {
        return this.skuItem.sku[type].skuPropertyName
      }
      return ''
    }
  }
</script>
