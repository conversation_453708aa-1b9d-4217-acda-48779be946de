import StudentTrainClassDetailVo from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryBackstage, {
  StudentSchemeLearningInformationRequest,
  StudentSchemeLearningInformationResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { Page } from '@hbfe/common'
import StudentPeriodLog from '@api/service/management/train-class/offlinePart/model/StudentPeriodLog'
import MsExamQueryFrontGatewayQuestionnaireQueryBackStage, {
  QuestionnaireDetailInLearningSchemeRequest
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'
import StudentQuestionnaire from '@api/service/management/train-class/offlinePart/model/StudentQuestionnaire'
import StudentQuestionnaireItem from '@api/service/management/train-class/offlinePart/model/StudentQuestionnaireItem'
import { QuestionnaireAppliedRangeTypeEnum } from '@api/service/common/scheme/enum/QuestionnaireAppliedRangeType'

/**
 * @description 查询学员期别学习
 */
class QueryStudentStudy {
  /**
   * 批量查询学员期别学习
   * @param list 学员方案学习信息列表
   */
  async batchQueryStudentPeriodStudy(list: StudentTrainClassDetailVo[]): Promise<void> {
    const havePeriodStudentNo = list.map((item) => item.studentNo).filter(Boolean)
    // list.forEach((item) => {
    //   const trainingMode = item.basicInfo.skuValueNameProperty.trainingMode.skuPropertyValueId
    //   if (trainingMode !== TrainingModeEnum.online) {
    //     havePeriodStudentNo.push(item.studentNo)
    //   }
    // })
    if (havePeriodStudentNo.length) {
      const request = new StudentSchemeLearningInformationRequest()
      request.studentNo = havePeriodStudentNo
      const page = new Page()
      page.pageSize = havePeriodStudentNo.length
      page.pageNo = 1
      const res =
        await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryBackstage.pageStudentSchemeLearningInformationInServicer(
          {
            page,
            request
          }
        )

      const logList = res?.data?.currentPageData?.length
        ? res.data.currentPageData
        : new Array<StudentSchemeLearningInformationResponse>()

      if (logList.length) {
        list.forEach((item: StudentTrainClassDetailVo) => {
          const findLog = logList.find(
            (it: StudentSchemeLearningInformationResponse) =>
              it.studentIssueLearning.studentNo == item.studentNo ||
              it.studentSchemeWithIssueLearningResponse.studentNo == item.studentNo
          )
          if (findLog) {
            item.periodStudy = StudentPeriodLog.fromItem(findLog)
          }
        })
      }
    }
  }

  /**
   * 查询学员期别完成情况
   * @param studentNo 学号
   */
  async queryPeriodStudyLog(studentNo: string): Promise<StudentPeriodLog> {
    let result = new StudentPeriodLog()
    const { data, status } =
      await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryBackstage.getStudentSchemeLearningDetailInServicer(
        studentNo
      )
    if (status.isSuccess()) {
      result = StudentPeriodLog.from(data)
    }
    return result
  }

  /**
   * 查询学员问卷完成情况
   * @param studentNo 学号
   * @param page 分页参数
   */
  async queryStudentQuestionnaire(studentNo: string, page: Page) {
    const result = new StudentQuestionnaire()
    const params = new QuestionnaireDetailInLearningSchemeRequest()
    params.stuNo = studentNo
    const { data, status } = await MsExamQueryFrontGatewayQuestionnaireQueryBackStage.pageQuestionnaireSchemeInServicer(
      {
        page,
        request: params
      }
    )
    if (status.isSuccess()) {
      page.totalPageSize = data?.totalPageSize
      page.totalSize = data?.totalSize
      result.questionnaireContentList = data?.currentPageData.map(StudentQuestionnaireItem.from)
    }
    return result
  }
}

export default new QueryStudentStudy()
