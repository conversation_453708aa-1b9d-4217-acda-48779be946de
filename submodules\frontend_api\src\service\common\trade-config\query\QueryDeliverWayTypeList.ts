import MsTradeQueryFrontGatewayTradeQueryBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import TakePlaceDetailVo from '@api/service/management/online-school-config/distribution-channels-config/query/vo/TakePlaceDetailVo'
import { DeliverWayTypeEnum } from '@api/service/common/trade-config/query/enums/DeliverWayType'

/**
 * 查询自取点、快递备注等
 */
class QueryDeliverWayTypeList {
  expressList: Array<TakePlaceDetailVo> = new Array<TakePlaceDetailVo>()
  pickUpList: Array<TakePlaceDetailVo> = new Array<TakePlaceDetailVo>()

  async query() {
    const { data } = await MsTradeQueryFrontGatewayTradeQueryBackstage.getDeliveryChannelListInServicer()
    this.expressList = data
      .filter(deliver => deliver.shippingMethod === 0 && deliver.enable)
      .map(TakePlaceDetailVo.from)
    this.pickUpList = data.filter(deliver => deliver.shippingMethod === 1 && deliver.enable).map(TakePlaceDetailVo.from)
  }

  get list() {
    return (type: DeliverWayTypeEnum) => {
      if (type === DeliverWayTypeEnum.deliver) {
        return this.expressList
      }
      return this.pickUpList
    }
  }
}

export default QueryDeliverWayTypeList
