/*
 * @Description: 删除资讯 资讯列表使用
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-10 09:36:59
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-02-17 10:37:12
 */
import { ResponseStatus } from '@hbfe/common'
import MsBusinessNews from '@api/ms-gateway/ms-news-v1'

export default class MutitionNewsDelete {
  /**
   * 删除资讯
   * @returns ResponseStatus
   */
  async doDeleteNews(newsId: string): Promise<ResponseStatus> {
    const { status } = await MsBusinessNews.deleteNews(newsId)
    return status
  }
}
