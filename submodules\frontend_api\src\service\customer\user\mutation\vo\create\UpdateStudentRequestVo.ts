import { CreateUserIndustryRequest, UpdateStudentRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { GendersEnum } from '@api/service/common/enums/user/GenderTypes'

/*
 *创建学员信息
 */
class UpdateStudentRequestVo extends UpdateStudentRequest {
  /**
   * 【必填】用户名称
   */
  name = ''
  /**
   * 【必填】手机号
   */
  phone = ''
  /**
   * 【必填】所属区域
   */
  area = ''
  /**
   * 工作单位
   */
  companyName = ''
  /**
   * 统一社会信用码
   */
  companyCode = ''
  /**
   * [必填]加密值
   */
  encrypt = ''
  /**
   * 性别
   */
  gender: GendersEnum = GendersEnum.UN_KNOW
  /**
   * 行业信息
   */
  userIndustryInfos? = new Array<CreateUserIndustryRequest>()
}

export default UpdateStudentRequestVo
