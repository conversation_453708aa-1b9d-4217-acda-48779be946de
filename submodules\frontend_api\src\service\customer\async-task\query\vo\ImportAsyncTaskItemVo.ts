/**
 * @description
 */
class ImportAsyncTaskItemVo {
  /**
   * 任务id
   */
  jobId = ''

  /**
   * 任务名称
   */
  jobName = ''

  /**
   * 任务创建时间
   */
  createTime = ''

  /**
   * 任务结束时间
   */
  endTime = ''

  /**
   * 任务状态
   * (executing:运行中 executed:运行完成 fail:运行失败)
   */
  jobState = ''

  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult: boolean = null

  /**
   * 处理总条数
   */
  totalCount = 0

  /**
   * 成功条数
   */
  successCount = 0

  /**
   * 失败条数
   */
  failCount = 0

  /**
   * 导入全部文件路径
   */
  importFullFilePath = ''

  /**
   * 导入失败文件路径
   */
  importFailFilePath = ''

  /**
   * 任务执行成功或失败的信息
   */
  message = ''
}

export default ImportAsyncTaskItemVo
