<route-meta>
{
"isMenu": true,
"title": "开通网校",
"sort": 1,
"icon": "icon-daoru"
}
</route-meta>
<script lang="ts">
  import PlatformOpen from '@hbfe/jxjy-admin-registerSchool/src/index.vue'
  import { ZXMGLY } from '@/models/RoleTypes'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  @RoleTypeDecorator({
    query: [ZXMGLY],
    BasicInfo: [ZXMGLY],
    SchoolConfig: [ZXMGLY],
    TemplateConfig: [ZXMGLY],
    ManageAccount: [ZXMGLY],
    AddService: [ZXMGLY],
    cancel: [ZXMGLY],
    submit: [ZXMGLY]
  })
  export default class extends PlatformOpen {}
</script>
