<template>
  <el-dialog
    class="slider_dialog"
    :visible.sync="visible"
    :modal-append-to-body="false"
    :show-close="false"
    :before-close="destroyWindow"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    width="360px"
  >
    <div :id="mockId + 'slider'"></div>
  </el-dialog>
</template>

<script lang="ts">
  import { Vue, Component, Prop } from 'vue-property-decorator'
  import { mock } from 'mockjs'
  import { SecureCaptchaApplyRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
  import { BusinessTypeEnum } from '@hbfe-biz/biz-authentication/dist/enums/BusinessTypeEnum'
  import Authentication from '@api/service/common/authentication/Authentication'

  @Component({})
  export default class extends Vue {
    @Prop({ type: String, default: '' }) token: string
    $authentication: Authentication

    visible = false

    mockId = 'id'
    captcha: any

    created() {
      require('@hbfe/behavior-validator/dist/tac.min')
      require('@hbfe/behavior-validator/dist/styles/tac.css')
      this.mockId = mock('id')
    }

    init() {
      this.visible = true
      setTimeout(() => {
        const config = {
          requestCaptchaDataUrl: 'http://*************:8080/gen?type=WORD_IMAGE_CLICK',
          validCaptchaUrl: 'http://*************:8080/check',
          bindEl: `#${this.mockId}slider`
        }
        const style = {
          logoUrl: '',
          isHideBtnClose: false,
          isHideRefresh: false
        }
        style.logoUrl = null
        this.captcha = new (window as any).TAC(config, style)

        this.captcha.requestCaptchaData = async () => {
          const params = new SecureCaptchaApplyRequest()
          params.token = this.token
          params.captchaType = 'SLIDER'
          params.businessType = BusinessTypeEnum.login_account
          const res = await this.$authentication.verify.applySecureCaptcha(params)
          if (res.status.isSuccess() && res.data.code == '200') {
            return Promise.resolve({
              captcha: res.data.displayCaptchaData,
              id: res.data.token
            })
          } else {
            return Promise.resolve({
              captcha: '',
              id: ''
            })
          }
        }

        this.captcha.destroyWindow = async () => {
          if (this.captcha.domTemplate) {
            this.captcha.domTemplate.remove()
            this.visible = false
          }
        }

        this.captcha.validCaptcha = async (id: string, data: any) => {
          const start = new Date(data.startSlidingTime).getTime()
          const end = new Date(data.endSlidingTime).getTime()
          const returnData = Object.assign(data, {
            startSlidingTime: start,
            endSlidingTime: end
          })
          this.$emit('validCaptcha', { id, data: returnData })
        }
        this.captcha.init()
      }, 500)
    }
    /**
     * 关闭滑块组件
     */
    destroyWindow() {
      this.captcha.destroyWindow()
      this.visible = false
    }
    /**
     * 刷新
     */
    reloadCaptcha() {
      this.visible = true
      this.captcha.reloadCaptcha()
    }
  }
</script>
<style lang="scss" scoped>
  ::v-deep .el-dialog {
    background: none !important;
    box-shadow: none !important;
  }
  ::v-deep .slider-bottom {
    position: relative !important;
  }
  ::v-deep .close-btn {
    position: absolute;
  }
  ::v-deep .refresh-btn {
    position: absolute;
    margin-left: 30px;
  }
</style>
