import { OfflineInvoiceDeliveryChannelResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

/**
 * @description 快递方式的详情
 */
class ExpressDetailVo {
  /**
   * 唯一标识
   */
  id = ''
  /**
   * 备注
   */
  remark = ''

  static from(res: OfflineInvoiceDeliveryChannelResponse = new OfflineInvoiceDeliveryChannelResponse()) {
    const expressDetail = new ExpressDetailVo()
    expressDetail.id = res.channelId
    expressDetail.remark = res.remark
    return expressDetail
  }
}

export default ExpressDetailVo
