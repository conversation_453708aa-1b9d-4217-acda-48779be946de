import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import MutationPicCaptchaFactory from '@api/service/common/captcha/mutation/MutationPicCaptchaFactory'
import PicCaptchaInfo from '@api/service/common/captcha/mutation/pic-captcha/PicCaptchaInfo'

/**
 * @description 人机验证中控层
 */
@Module({
  name: 'CaptchaModule',
  dynamic: true,
  namespaced: true,
  store
})
class CaptchaModule extends VuexModule {
  /**
   * 图形验证变异业务工厂
   */
  mutationPicCaptchaFactory: MutationPicCaptchaFactory = new MutationPicCaptchaFactory()

  /**
   * 获取人机验证信任凭证
   */
  get captchaToken() {
    return PicCaptchaInfo.token
  }
}

export default getModule(CaptchaModule)
