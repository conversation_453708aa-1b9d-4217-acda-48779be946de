import LsCommodityWithTeacher from './LsCommodityWithTeacher'
import IssueCommodity from './IssueCommodity'
import LsCourseWithTeacher from './LsCourseWithTeacher'
import { PreExamLSInfoDTO } from '@api/gateway/PlatformTrade'
import moment from 'moment'
import TeacherInfo from './TeacherInfo'
import { Constants } from '@api/service/common/models/common/Constants'

class LsCommodityDetail extends LsCommodityWithTeacher {
  /**
   * 课程学习要求总学时
   */
  maxPeriod: number
  /**
   * 课程要求学习进度
   */
  schedule: number
  /**
   * 必修要求学时
   */
  compulsoryPeriod: number
  /**
   * 选修要求学时
   */
  optionalPeriod: number
  /**
   * 要求考试分数
   */
  passScore: number
  /**
   * 是否有考试学习方式
   */
  hasExamLearning = false
  /**
   * 是否有课程学习方式
   */
  hasCourseLearning = false
  /**
   * 期别商品列表
   */
  issueCommodityList = new Array<IssueCommodity>()
  /**
   * 课程信息列表
   */
  courseList = new Array<LsCourseWithTeacher>()

  /**
   * 类别名
   */
  trainingCategoryName = ''

  /**
   * 工种名
   */
  workTypeName = ''

  static from(dto: PreExamLSInfoDTO): LsCommodityDetail {
    const commodity = new LsCommodityDetail()
    commodity.schemeId = dto.schemeId
    commodity.name = dto.name
    commodity.year = dto.year
    commodity.trainingTypeId = dto.trainingTypeId
    commodity.workTypeId = dto.workTypeId
    // 后端有返回来 但是GQL 没有生成
    commodity.workTypeName = (dto as any).workTypeName
    commodity.trainingCategoryName = (dto as any).trainingCategoryName
    // commodity.unitCategoryId = dto.unitCategoryId
    // commodity.jobCategoryId = dto.jobCategoryId
    commodity.picture = dto.picture ? '/mfs' + dto.picture : ''
    commodity.publishTime = moment(dto.publishTime, Constants.DATE_PATTERN).toDate()
    commodity.createUserId = dto.createUserId
    commodity.createTime = moment(dto.createTime, Constants.DATE_PATTERN).toDate()
    commodity.recommendIndex = dto.recommendIndex
    if (dto.achieveSetting?.grade) {
      commodity.grade = dto.achieveSetting.grade
    }
    if (dto.courseLearning) {
      commodity.maxPeriod = dto.courseLearning.minTotalPeriod
      commodity.schedule = dto.courseLearning.assessSetting.schedule
      commodity.hasCourseLearning = true
    }
    if (dto.examLearning?.assessSetting) {
      commodity.passScore = dto.examLearning.assessSetting.score
      commodity.hasExamLearning = true
    }
    commodity.teachers = new Array<TeacherInfo>()
    if (dto.teachers) {
      dto.teachers.forEach(teacher => commodity.teachers.push(TeacherInfo.from(teacher)))
    }
    return commodity
  }

  hasRequireCourse() {
    return this.courseList.filter(p => p.coursePoolType === 1).length > 0
  }

  hasOptionCourse() {
    return this.courseList.filter(p => p.coursePoolType === 2).length > 0
  }
}

export default LsCommodityDetail
