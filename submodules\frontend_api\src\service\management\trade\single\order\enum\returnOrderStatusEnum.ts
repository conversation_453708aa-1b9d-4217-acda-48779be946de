import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum ReturnOrderStatusEnum {
  noRefund = 1, // * 未退款
  refunding, // * 退款中
  refunded, // * 退款成功
  refundFailure // * 退款失败
}

class ReturnOrderStatusEnumClass extends AbstractEnum<ReturnOrderStatusEnum> {
  constructor(status?: ReturnOrderStatusEnum) {
    super()
    this.current = status
    this.map.set(ReturnOrderStatusEnum.noRefund, '未退款')
    this.map.set(ReturnOrderStatusEnum.refunding, '退款中')
    this.map.set(ReturnOrderStatusEnum.refunded, '退款成功')
    this.map.set(ReturnOrderStatusEnum.refundFailure, '退款失败')
  }
}

export default new ReturnOrderStatusEnumClass()
