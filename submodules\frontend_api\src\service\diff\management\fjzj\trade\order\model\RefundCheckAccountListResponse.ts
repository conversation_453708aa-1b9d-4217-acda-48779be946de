/*
 * @Description: 列表转换数据
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-29 16:34:50
 */

import { ReturnOrderResponse as ReturnOrderResponseDiff } from '@api/diff-gateway/fjzj-trade-query-front-gateway-TradeQueryBackstage'
import { ReturnOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

import RefundCheckAccountList from '@api/service/management/trade/single/checkAccount/query/vo/RefundCheckAccountListResponse'
import QueryPlatform from '@api/service/diff/common/fjzj/dictionary/QueryPlatform'
import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/fjzj/trade/enums/SaleChannelType'

export default class RefundCheckAccountListResponse extends RefundCheckAccountList {
  /**
   * 第三方平台
   */
  thirdPartyPlatform = ''
  static diffFrom(returnOrderResponse: ReturnOrderResponseDiff) {
    const returnOrder = Object.assign(new ReturnOrderResponse(), returnOrderResponse)
    const refundCheckAccountListResponse = Object.assign(
      new RefundCheckAccountListResponse(),
      RefundCheckAccountList.from(returnOrder)
    )
    if (returnOrderResponse.subOrderInfo.saleChannel == SaleChannelEnum.huayi) {
      refundCheckAccountListResponse.thirdPartyPlatform = SaleChannelType.map.get(
        returnOrderResponse.subOrderInfo.saleChannel
      )
    } else if (returnOrderResponse.returnCommodity?.commoditySku?.tppTypeId) {
      refundCheckAccountListResponse.thirdPartyPlatform = QueryPlatform.map.get(
        returnOrderResponse.returnCommodity.commoditySku.tppTypeId
      )?.name
    }
    return refundCheckAccountListResponse
  }
}
