import {
  CommodityOpenReportFormResponse,
  PaymentTypeStatisticDto,
  PurchaseChannelStatisticDto
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
import QueryTrainClassDetailClass from '@api/service/management/train-class/query/QueryTrainClassDetailClass'
/**
 * 子订单变更记录统计情况
 <AUTHOR>
 @date 2022/05/10
 */
export class SubOrderStatisticDto {
  /**
   * 交易成功数量
   */
  tradeSuccessCount = 0
  /**
   * 退货数量
   */
  returnCount = 0
  /**
   * 换入数量
   */
  exchangeInCount = 0
  /**
   * 换出数量
   */
  exchangeOutCount = 0
  /**
   * 净交易成功数量
   <p> (交易成功数量 + 换入数量) - (退货数量 + 换出数量)
   */
  netTradeSuccessCount = 0
}
export class CommodityOpenReportFormResponseVo extends CommodityOpenReportFormResponse {
  //  培训班详情信息
  trainClassDetail = new TrainClassDetailClassVo()

  async addTrainClass() {
    try {
      const queryClassDetail = new QueryTrainClassDetailClass()
      queryClassDetail.commodityId = this.commoditySkuId
      const res = await queryClassDetail.queryTrainClassDetail()

      if (res.isSuccess()) {
        this.trainClassDetail = queryClassDetail.trainClassDetail
      }
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/vo/CommodityOpenReportFormResponseVo.ts所处方法，addTrainClass',
        e
      )
    }
  }
  async fillData() {
    if (!this.summaryInfo) {
      this.summaryInfo = new SubOrderStatisticDto()
    }
    if (!this.purchaseChannelStatisticInfoList) {
      this.purchaseChannelStatisticInfoList = []
    }
    const channels = [1, 2, 3]
    for (const channel of channels) {
      this.fillPurchaseChannelStatisticInfoList(channel)
    }
  }
  /**
   * 购买渠道 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
   @see PurchaseChannelTypes
   */
  private fillPurchaseChannelStatisticInfoList(purchaseChannel: number) {
    let findChannel = this.purchaseChannelStatisticInfoList.find(item => item.purchaseChannel == purchaseChannel)
    if (!findChannel) {
      findChannel = new PurchaseChannelStatisticDto()
      findChannel.purchaseChannel = purchaseChannel
      findChannel.paymentTypeStatisticInfoList = []
    }
    if (purchaseChannel == 1) {
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 1)) {
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(1))
      }
    } else if (findChannel.purchaseChannel == 2) {
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 1)) {
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(1))
      }
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 2)) {
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(2))
      }
    } else if (findChannel.purchaseChannel == 3) {
      if (!findChannel.paymentTypeStatisticInfoList.find(item => item.paymentType == 2)) {
        findChannel.paymentTypeStatisticInfoList.push(this.addPayment(2))
      }
    }
    this.purchaseChannelStatisticInfoList.push(findChannel)
  }
  private addPayment(paymentType: number) {
    const paymentModel = new PaymentTypeStatisticDto()
    paymentModel.paymentType = paymentType
    paymentModel.statisticInfo = new SubOrderStatisticDto()
    return paymentModel
  }
}
