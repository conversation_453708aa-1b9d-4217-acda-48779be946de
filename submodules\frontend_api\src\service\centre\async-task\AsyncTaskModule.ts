import store from '@api/store'
import { VuexModule, getModule, Module } from 'vuex-module-decorators'
import QueryAsyncTaskFactory from './QueryAsyncTaskFactory'
/**
 * @description
 */
@Module({ namespaced: true, name: 'CentreAsyncTaskModule', dynamic: true, store })
class AsyncTaskModule extends VuexModule {
  /**
   * 异步任务查询工厂
   */
  get queryAsyncTaskFactory() {
    return new QueryAsyncTaskFactory()
  }
}

export default getModule(AsyncTaskModule)
