schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	questionnaireDeleteVerify(request:AhjsQuestionnaireDeleteVerifyRequest):AhjsQuestionnaireDeleteVerifyResponse
}
"""@Author: chenzeyu
	@CreateTime: 2024-11-20  19:33
	@Description: TODO
"""
input AhjsQuestionnaireDeleteVerifyRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.AhjsQuestionnaireDeleteVerifyRequest") {
	questionnaireId:String
}
"""@Author: chenzeyu
	@CreateTime: 2024-11-20  19:33
	@Description: TODO
"""
type AhjsQuestionnaireDeleteVerifyResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.AhjsQuestionnaireDeleteVerifyResponse") {
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}

scalar List
