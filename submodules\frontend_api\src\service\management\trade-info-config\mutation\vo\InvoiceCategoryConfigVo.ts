import { InvoiceCategoryConfig } from '@api/ms-gateway/ms-trade-configuration-v1'
import { InvoiceCategoryEunm } from '@api/service/common/enums/trade-configuration/InvoiceCtegory'
import {
  InvoiceMethodTypeEnum,
  InvoiceMethodWithSpecialEnum
} from '@api/service/common/enums/trade-configuration/InvoiceMethodType'
import { InvoiceTitleTypeEnum } from '@api/service/common/enums/trade-configuration/InvoiceTitleType'
import { InvoiceTypeEnum } from '@api/service/common/enums/trade-configuration/InvoiceTypeEnum'
import { RemarkTypeEnum } from '@api/service/common/enums/trade-configuration/RemarkType'

class InvoiceCategoryConfigVo {
  /**
   * 允许开具发票种类
   */
  invoiceCategory: InvoiceCategoryEunm = -1
  /**
   * 发票抬头类型 1-个人 2-企业
   */
  invoiceTitleTypes = new Array<InvoiceTitleTypeEnum>()
  /**
   * 开票方式 1-自动开票 2-线下开票 （普票使用）
   */
  invoiceMethod: InvoiceMethodTypeEnum = -1

  /**
   * 开票方式 1-线下电子 2-线下纸质 （专票使用）
   */
  invoiceMethodWithSpecial: InvoiceMethodWithSpecialEnum = -1

  /**
   * 发票备注形式
   */
  remarkMode: RemarkTypeEnum = undefined

  /**
   * 发票备注
   */
  remark: string = undefined
}
export default InvoiceCategoryConfigVo
