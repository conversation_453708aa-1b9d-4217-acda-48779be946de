"""独立部署的微服务,K8S服务名:ms-trade-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""分销商--获取批次单详情"""
	getBatchOrderInDistributor(batchOrderNo:String!):BatchOrderResponse
	"""获取批次单详情"""
	getBatchOrderInServicer(batchOrderNo:String!):BatchOrderResponse
	"""分销商-  获取批次单退货详情"""
	getBatchReturnOrderInDistributor(batchReturnOrderNo:String):BatchReturnOrderResponse
	"""获取批次单退货详情"""
	getBatchReturnOrderInServicer(batchReturnOrderNo:String):BatchReturnOrderResponse
	"""获取报表统计合计数据，适用于当前网校下的课程供应商"""
	getCommodityReportSummaryInCourseSupplier(request:TradeReportRequest):ReportSummaryResponse @optionalLogin
	"""获取报表统计合计数据"""
	getCommodityReportSummaryInServicer(request:TradeReportRequest):ReportSummaryResponse @optionalLogin
	"""获取培训班商品详情"""
	getCommoditySkuInServicer(commoditySkuId:String):CommoditySkuBackstageResponse
	"""获取培训班商品详情,适用于课件供应商"""
	getCommoditySkuInServicerInCourseVendor(commoditySkuId:String):CommoditySkuBackstageResponse
	"""根据渠道id 获取配送渠道
		@param channelId 渠道id
		@return
	"""
	getDeliveryChannelInServicer(channelId:String):OfflineInvoiceDeliveryChannelResponse
	"""获取网校提供的配送渠道
		@return
	"""
	getDeliveryChannelListInServicer:[OfflineInvoiceDeliveryChannelResponse]
	"""获取网校提供的配送渠道
		@return
	"""
	getDeliveryChannelListV2InServicer(deliveryChannelRequest:DeliveryChannelRequest):[OfflineInvoiceDeliveryChannelResponse]
	"""分销商获取换货单详情"""
	getExchangeOrderInDistributor(exchangeOrderNo:String!):ExchangeOrderResponse
	"""获取换货单详情"""
	getExchangeOrderInServicer(exchangeOrderNo:String!):ExchangeOrderResponse
	"""获取发票自动开票配置
		@return
	"""
	getInvoiceAutoBillPolicyInServicer(queryRequest:InvoiceAutoBillPolicyRequest):InvoiceAutoBillPolicyResponse @optionalLogin
	"""获取发票详情"""
	getOfflineInvoiceInServicer(invoiceId:String):OfflineInvoiceResponse
	"""获取发票详情"""
	getOnlineInvoiceInServicer(invoiceId:String):OnlineInvoiceResponse
	"""分销商 - 获取订单详情"""
	getOrderInDistributor(orderNo:String!):OrderResponse
	"""获取订单详情"""
	getOrderInServicer(orderNo:String!):OrderResponse @optionalLogin
	"""根据购买渠道编号获取购买渠道信息
		@param purchaseChannelId 购买渠道编号
		@return
	"""
	getPurchaseChannel(purchaseChannelId:String):PurchaseChannelResponse @optionalLogin
	"""获取当前服务商下收款账户配置
		@param receiveAccountId
		@return
	"""
	getReceiveAccountInServicer(receiveAccountId:String!):ReceiveAccountConfigResponse @optionalLogin
	"""获取当前子项目下收款账户配置"""
	getReceiveAccountInSubProject(receiveAccountId:String!):ReceiveAccountConfigResponse @optionalLogin
	"""获取当前服务商下收款账户配置
		@param request
		@return
	"""
	getReceiveAccountV2InServicer(request:ReceiveAccountRequest!):ReceiveAccountConfigResponse @optionalLogin
	"""分销商- 获取退货单详情"""
	getReturnOrderInDistributor(returnOrderNo:String):ReturnOrderResponse
	"""获取退货单详情
		@param returnOrderNo : 退货单号
		@return 退货单信息
	"""
	getReturnOrderInServicer(returnOrderNo:String):ReturnOrderResponse @optionalLogin
	"""获取当前服务商下纳税人信息
		@param taxpayerId
		@return
	"""
	getTaxpayerInServicer(taxpayerId:String!):TaxpayerResponse @optionalLogin
	"""获取当前服务商下纳税人信息
		@param taxpayerId
		@return
	"""
	getTaxpayerV2InServicer(taxpayerId:String!):TaxpayerResponse @optionalLogin
	"""获取时间间隔交易记录统计直方图"""
	getTradeStatisticDateHistogramInServicer(request:TradeStatisticDateHistogramRequest):TradeStatisticDateHistogramResponse @optionalLogin
	"""查询买家所拥有商品
		<br> 查询用户所有不处于退货完成状态的子订单的现有商品
	"""
	listBuyerAllCommodityInSerivicer(request:BuyerValidCommodityRequest):[BuyerValidCommodityResponse] @optionalLogin
	"""查询买家有效商品
		<br> 查询用户所有发货完成并且不处于换货中、退货状态为未退货的子订单的现有商品
	"""
	listBuyerValidCommodityInSerivicer(request:BuyerValidCommodityRequest):[BuyerValidCommodityResponse] @optionalLogin
	"""分页获取商品开通统计列表（只能获取到有销售过的商品）"""
	listCommodityOpenReportFormsBeSoldInServicer(limit:Long,request:TradeReportRequest,sortRequest:CommodityOpenReportSortRequest):[CommodityOpenReportFormResponse] @optionalLogin
	"""获取指定列表地区开通统计列表，以商品地区为聚合主键"""
	listCommoditySkuRegionOpenReportFormsInServicer(request:TradeReportRequest):[RegionOpenReportFormResponse] @optionalLogin
	"""查询漏清洗交易记录的换货订单号
		@return 漏清洗的换货订单号集合
	"""
	listMissExchangeOrderByPage(request:TradeReportMissDataRequest):[String] @optionalLogin
	"""查询漏清洗交易记录的子订单号
		@return 漏清洗的子订单号集合
	"""
	listMissOrderByPage(request:TradeReportMissDataRequest):[String] @optionalLogin
	"""查询漏清洗交易记录的退货订单号
		@return 漏清洗的退货订单号集合
	"""
	listMissReturnOrderByPage(request:TradeReportMissDataRequest):[String] @optionalLogin
	"""获取发票操作记录"""
	listOfflineInvoiceOperationRecord(offlineInvoiceId:String):[OfflineInvoiceOperationResponse]
	"""获取发票操作记录"""
	listOnlineInvoiceOperationRecord(onlineInvoiceId:String):[OnlineInvoiceOperationResponse]
	"""获取当前服务商下收款账户配置
		@param receiveAccountIdList
		@return
	"""
	listReceiveAccountInServicer(receiveAccountIdList:[String]!):[ReceiveAccountConfigResponse] @optionalLogin
	"""获取当前子项目下收款账户配置"""
	listReceiveAccountInSubProject(receiveAccountIdList:[String]!):[ReceiveAccountConfigResponse] @optionalLogin
	"""获取当前服务商下收款账户配置
		@param request
		@return
	"""
	listReceiveAccountV2InServicer(request:ReceiveAccountRequest):[ReceiveAccountConfigResponse]
	"""获取指定列表地区开通统计列表"""
	listRegionOpenReportFormsInServier(request:TradeReportRequest):[RegionOpenReportFormResponse] @optionalLogin
	"""获取子项目下退货原因列表
		@return 退货原因列表
	"""
	listReturnReasonInfoInSubProject:[ReturnReasonInfoResponse] @optionalLogin
	"""获取当前服务商下纳税人信息列表
		@return
	"""
	listTaxpayerInServicer:[TaxpayerResponse] @optionalLogin
	"""获取当前服务商下纳税人信息列表
		@return
	"""
	listTaxpayerV2InService(taxpayerIRequest:TaxpayerIRequest):[TaxpayerResponse] @optionalLogin
	"""获取当前网校下的商品包含的地区列表"""
	listUniqueRegionFromCommodityInServicer:[RegionResponse] @optionalLogin
	"""分销商-  集体报名订单列表"""
	pageBatchOrderInDistributor(page:Page,request:BatchOrderRequestInDistributor,sortRequest:[BatchOrderSortRequest]):BatchOrderResponsePage @page(for:"BatchOrderResponse")
	"""分页获取批次单"""
	pageBatchOrderInServicer(page:Page,request:BatchOrderRequest,sortRequest:[BatchOrderSortRequest]):BatchOrderResponsePage @page(for:"BatchOrderResponse")
	"""专题管理员 - 分页获取批次单"""
	pageBatchOrderInTrainingChannel(page:Page,request:BatchOrderRequest,sortRequest:[BatchOrderSortRequest]):BatchOrderResponsePage @page(for:"BatchOrderResponse")
	"""分销商-   分页获取批次退货单"""
	pageBatchReturnOrderInDistributor(page:Page,request:BatchReturnOrderRequestInDistributor,sortRequest:[BatchReturnOrderSortRequest]):BatchReturnOrderResponsePage @page(for:"BatchReturnOrderResponse")
	"""分页获取批次退货单"""
	pageBatchReturnOrderInServicer(page:Page,request:BatchReturnOrderRequest,sortRequest:[BatchReturnOrderSortRequest]):BatchReturnOrderResponsePage @page(for:"BatchReturnOrderResponse")
	"""专题管理员  - 分页获取批次退货单"""
	pageBatchReturnOrderInTrainingChannel(page:Page,request:BatchReturnOrderRequest,sortRequest:[BatchReturnOrderSortRequest]):BatchReturnOrderResponsePage @page(for:"BatchReturnOrderResponse")
	"""分页获取商品开通统计列表（含所有商品），适用于当前网校下的课程供应商"""
	pageCommodityOpenReportFormsInCourseSupplier(page:Page,request:TradeReportRequest):CommodityOpenReportFormResponsePage @page(for:"CommodityOpenReportFormResponse") @optionalLogin
	"""分页获取商品开通统计列表（含所有商品）"""
	pageCommodityOpenReportFormsInServicer(page:Page,request:TradeReportRequest):CommodityOpenReportFormResponsePage @page(for:"CommodityOpenReportFormResponse") @optionalLogin
	"""网校创建的培训班商品分页查询"""
	pageCommoditySkuInServicer(page:Page,queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):CommoditySkuBackstageResponsePage @page(for:"CommoditySkuBackstageResponse")
	"""网校创建的培训班商品分页查询,适用于课件供应商"""
	pageCommoditySkuInServicerInCourseVendor(page:Page,queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):CommoditySkuBackstageResponsePage @page(for:"CommoditySkuBackstageResponse")
	"""专题管理员 - 商品分页"""
	pageCommoditySkuInTrainingChannel(page:Page,queryRequest:CommoditySkuRequest,sortRequest:[CommoditySkuSortRequest]):CommoditySkuBackstageResponsePage @page(for:"CommoditySkuBackstageResponse")
	"""分销商分页换货查询"""
	pageExchangeOrderInDistributor(page:Page,request:ExchangeOrderRequest,sort:[ExchangeOrderSortRequest]):ExchangeOrderResponsePage @page(for:"ExchangeOrderResponse")
	"""换货单分页查询"""
	pageExchangeOrderInServicer(page:Page,request:ExchangeOrderRequest,sort:[ExchangeOrderSortRequest]):ExchangeOrderResponsePage @page(for:"ExchangeOrderResponse")
	"""分页获取培训班商品期别信息"""
	pageIssueCommoditySkuInServicer(page:Page,request:IssueCommoditySkuBackStageRequest):IssueCommoditySkuBackStageResponsePage @page(for:"IssueCommoditySkuBackStageResponse")
	"""分销商-查询期别变更记录"""
	pageIssueLogInDistributor(page:Page,request:IssueLogRequest):IssueLogResponsePage @page(for:"IssueLogResponse")
	"""查询期别变更记录"""
	pageIssueLogInServicer(page:Page,request:IssueLogRequest):IssueLogResponsePage @page(for:"IssueLogResponse") @optionalLogin
	"""分页查询线下发票"""
	pageOfflineInvoiceInServicer(page:Page,request:OfflineInvoiceRequest,sort:[OfflineInvoiceSortRequest]):OfflineInvoiceResponsePage @page(for:"OfflineInvoiceResponse")
	"""专题管理员 -  分页查询线下发票"""
	pageOfflineInvoiceInTrainingChannel(page:Page,request:OfflineInvoiceRequest,sort:[OfflineInvoiceSortRequest]):OfflineInvoiceResponsePage @page(for:"OfflineInvoiceResponse")
	"""分页查询线上发票"""
	pageOnlineInvoiceInServicer(page:Page,request:OnlineInvoiceRequest,sort:[OnlineInvoiceSortRequest]):OnlineInvoiceResponsePage @page(for:"OnlineInvoiceResponse")
	"""专题管理员- 分页查询线上发票"""
	pageOnlineInvoiceInTrainingChannel(page:Page,request:OnlineInvoiceRequest,sort:[OnlineInvoiceSortRequest]):OnlineInvoiceResponsePage @page(for:"OnlineInvoiceResponse")
	"""分销商-分页查询订单信息"""
	pageOrderInDistributor(page:Page,request:OrderRequestInDistributor,sortRequest:[OrderSortRequest]):OrderResponsePage @page(for:"OrderResponse")
	"""分页获取订单"""
	pageOrderInServicer(page:Page,request:OrderRequest,sortRequest:[OrderSortRequest]):OrderResponsePage @page(for:"OrderResponse") @optionalLogin
	"""专题管理员 - 分页获取订单"""
	pageOrderInTrainingChannel(page:Page,request:OrderRequest,sortRequest:[OrderSortRequest]):OrderResponsePage @page(for:"OrderResponse")
	"""分页获取当前服务商下收款账户配置
		@param page
		@return
	"""
	pageReceiveAccountInDistribution(page:Page,request:ReceiveAccountConfigRequest):ReceiveAccountConfigResponsePage @page(for:"ReceiveAccountConfigResponse")
	"""分页获取当前服务商下收款账户配置（分销商）
		@param page
		@return
	"""
	pageReceiveAccountInServicer(page:Page,request:ReceiveAccountConfigRequest):ReceiveAccountConfigResponsePage @page(for:"ReceiveAccountConfigResponse") @optionalLogin
	pageReceiveAccountInSubProject(page:Page,request:ReceiveAccountConfigRequest):ReceiveAccountConfigResponsePage @page(for:"ReceiveAccountConfigResponse") @optionalLogin
	"""分销商-  退货单分页查询"""
	pageReturnOrderInDistributor(page:Page,request:ReturnOrderRequestInDistributor,sort:[ReturnSortRequest]):ReturnOrderResponsePage @page(for:"ReturnOrderResponse")
	"""退货单分页查询"""
	pageReturnOrderInServicer(page:Page,request:ReturnOrderRequest,sort:[ReturnSortRequest]):ReturnOrderResponsePage @page(for:"ReturnOrderResponse") @optionalLogin
	"""专题管理员 - 退货单分页查询"""
	pageReturnOrderInTrainingChannel(page:Page,request:ReturnOrderRequest,sort:[ReturnSortRequest]):ReturnOrderResponsePage @page(for:"ReturnOrderResponse")
	"""分销商-  获取批次单总数量、总金额"""
	statisticBatchOrderInDistributor(request:BatchOrderRequestInDistributor):BatchOrderStatisticResponse
	"""获取批次单总数量、总金额"""
	statisticBatchOrderInServicer(request:BatchOrderRequest):BatchOrderStatisticResponse
	"""专题管理员 - 获取批次单总数量、总金额"""
	statisticBatchOrderInTrainingChannel(request:BatchOrderRequest):BatchOrderStatisticResponse
	"""分销商-  获取批次退货单总数量、退款总金额"""
	statisticBatchReturnOrderInDistributor(request:BatchReturnOrderRequestInDistributor):BatchReturnOrderStatisticResponse
	"""获取批次退货单总数量、退款总金额"""
	statisticBatchReturnOrderInServicer(request:BatchReturnOrderRequest):BatchReturnOrderStatisticResponse
	"""专题管理员  - 获取批次退货单总数量、退款总金额"""
	statisticBatchReturnOrderInTrainingChannel(request:BatchReturnOrderRequest):BatchReturnOrderStatisticResponse
	"""获取发票开票总金额、发票总税额"""
	statisticOnlineInvoiceInServicer(request:OnlineInvoiceRequest):OnlineInvoiceStatisticResponse
	"""专题管理员- 获取发票开票总金额、发票总税额"""
	statisticOnlineInvoiceInTrainingChannel(request:OnlineInvoiceRequest):OnlineInvoiceStatisticResponse
	"""分销商-订单信息统计"""
	statisticOrderInDistributor(request:OrderRequestInDistributor):OrderStatisticResponse
	"""获取订单总金额、总数量"""
	statisticOrderInServicer(request:OrderRequest):OrderStatisticResponse @optionalLogin
	"""专题管理员 - 获取订单总金额、总数量"""
	statisticOrderInTrainingChannel(request:OrderRequest):OrderStatisticResponse
	"""分销商-  获取退货单总数量、退款总金额"""
	statisticReturnOrderInDistributor(request:ReturnOrderRequestInDistributor):ReturnOrderStatisticResponse
	"""获取退货单总数量、退款总金额"""
	statisticReturnOrderInServicer(request:ReturnOrderRequest):ReturnOrderStatisticResponse @optionalLogin
	"""专题管理员  - 获取退货单总数量、退款总金额"""
	statisticReturnOrderInTrainingChannel(request:ReturnOrderRequest):ReturnOrderStatisticResponse
	temporaryOrderUpdateOrderInfoInSubject(page:Page,request:OrderRequest,sortRequest:[OrderSortRequest]):OrderResponsePage @page(for:"OrderResponse") @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input BigDecimalScopeRequest @type(value:"com.fjhb.ms.query.commons.BigDecimalScopeRequest") {
	begin:BigDecimal
	end:BigDecimal
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input BatchOrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.BatchOrderRequest") {
	"""批次单号集合"""
	batchOrderNoList:[String]
	"""批次单基本信息查询参数"""
	basicData:BatchOrderBasicDataRequest
	"""批次单支付信息查询参数"""
	payInfo:OrderPayInfoRequest
	"""批次单创建人查询参数"""
	creatorIdList:[String]
	"""是否已经申请发票"""
	isInvoiceApplied:Boolean
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input BatchOrderRequestInDistributor @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.BatchOrderRequestInDistributor") {
	"""批次单号集合"""
	batchOrderNoList:[String]
	"""批次单基本信息查询参数"""
	basicData:BatchOrderBasicDataRequestInDistributor
	"""批次单支付信息查询参数"""
	payInfo:OrderPayInfoRequest
	"""批次单创建人查询参数"""
	creatorIdList:[String]
	"""是否已经申请发票"""
	isInvoiceApplied:Boolean
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""批次单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input BatchOrderSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.BatchOrderSortRequest") {
	"""需要排序的字段"""
	field:BatchOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""批次单基本信息查询参数
	<AUTHOR>
	@date 2022/04/17
"""
input BatchOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.nested.BatchOrderBasicDataRequest") {
	"""批次单状态
		0: 未确认，批次单初始状态
		1: 正常
		2: 交易完成
		3: 交易关闭
		4: 提交处理中 提交处理完成后，变更为NORMAl
		5: 取消处理中
		@see BatchOrderStatus
	"""
	batchOrderStatusList:[Int]
	"""批次单状态变更时间"""
	batchOrderStatusChangeTime:BatchOrderStatusChangeTimeRequest
	"""批次单支付状态
		<p>
		0：未支付
		1：支付中
		2：已支付
		@see BatchOrderPaymentStatus
	"""
	batchOrderPaymentStatusList:[Int]
	"""批次单发货状态
		0: 未发货
		1: 发货中
		2: 已发货
		@see BatchOrderDeliveryStatus
	"""
	batchOrderDeliveryStatusList:[Int]
	"""批次单价格范围
		<p> 查询非0元批次单 begin填0.01
	"""
	batchOrderAmountScope:BigDecimalScopeRequest
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""批次单基本信息查询参数
	<AUTHOR>
	@date 2022/04/17
"""
input BatchOrderBasicDataRequestInDistributor @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.nested.BatchOrderBasicDataRequestInDistributor") {
	"""批次单状态
		0: 未确认，批次单初始状态
		1: 正常
		2: 交易完成
		3: 交易关闭
		4: 提交处理中 提交处理完成后，变更为NORMAl
		5: 取消处理中
		@see BatchOrderStatus
	"""
	batchOrderStatusList:[Int]
	"""批次单状态变更时间"""
	batchOrderStatusChangeTime:BatchOrderStatusChangeTimeRequest
	"""批次单支付状态
		<p>
		0：未支付
		1：支付中
		2：已支付
		@see BatchOrderPaymentStatus
	"""
	batchOrderPaymentStatusList:[Int]
	"""批次单发货状态
		0: 未发货
		1: 发货中
		2: 已发货
		@see BatchOrderDeliveryStatus
	"""
	batchOrderDeliveryStatusList:[Int]
	"""批次单价格范围
		<p> 查询非0元批次单 begin填0.01
	"""
	batchOrderAmountScope:BigDecimalScopeRequest
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
	"""门店id"""
	portalId:String
}
"""批次单状态变更时间查询参数
	<AUTHOR>
	@date 2022/04/17
"""
input BatchOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.nested.BatchOrderStatusChangeTimeRequest") {
	"""未确认"""
	unConfirmed:DateScopeRequest
	"""正常"""
	normal:DateScopeRequest
	"""交易成功"""
	completed:DateScopeRequest
	"""已关闭"""
	closed:DateScopeRequest
	"""提交中"""
	committing:DateScopeRequest
	"""取消处理中"""
	canceling:DateScopeRequest
}
"""批次退货单审批信息查询参数
	<AUTHOR>
	@date 2022/03/18
"""
input BatchReturnOrderApprovalInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.nested.BatchReturnOrderApprovalInfoRequest") {
	"""审批时间"""
	approveTime:DateScopeRequest
}
"""批次退货单查询参数
	<AUTHOR>
	@date 2022/04/19
"""
input BatchReturnOrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.BatchReturnOrderRequest") {
	"""批次退货单号集合"""
	batchReturnOrderList:[String]
	"""基本信息"""
	basicData:BatchReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:BatchReturnOrderApprovalInfoRequest
	"""批次退货单关联批次单"""
	batchOrderInfo:BatchOrderInfoRequest
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""批次退货单查询参数
	<AUTHOR>
	@date 2022/04/19
"""
input BatchReturnOrderRequestInDistributor @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.BatchReturnOrderRequestInDistributor") {
	"""批次退货单号集合"""
	batchReturnOrderList:[String]
	"""基本信息"""
	basicData:BatchReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:BatchReturnOrderApprovalInfoRequest
	"""批次退货单关联批次单"""
	batchOrderInfo:BatchOrderInfoRequest
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""批次退货单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input BatchReturnOrderSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.BatchReturnOrderSortRequest") {
	"""需要排序的字段"""
	field:BatchReturnOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""批次退货单关联批次单查询参数
	<AUTHOR>
	@date 2022/04/19
"""
input BatchOrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.nested.BatchOrderInfoRequest") {
	"""批次单号集合"""
	batchOrderNoList:[String]
	"""批次单创建人id集合"""
	creatorIdList:[String]
	"""收款账号ID集合"""
	receiveAccountIdList:[String]
	"""交易流水号集合"""
	flowNoList:[String]
	"""付款类型
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
	"""
	paymentOrderTypeList:[Int]
}
"""批次退货单关闭信息
	<AUTHOR>
	@date 2022/4/19
"""
input BatchReturnCloseReasonRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.nested.BatchReturnCloseReasonRequest") {
	"""批次退货单关闭类型（1：卖家取消 2：卖家拒绝退货 3：买家取消 4：确认失败取消）
		@see BatchReturnCloseTypes
	"""
	closeTypeList:[Int]
}
"""批次退货单基本信息查询参数
	<AUTHOR>
	@date 2022/4/19
"""
input BatchReturnOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.nested.BatchReturnOrderBasicDataRequest") {
	"""批次退货单状态
		0: 已创建
		1: 已确认
		2: 取消申请中
		3: 退货处理中
		4: 退货失败
		5: 正在申请退款
		6: 已申请退款
		7: 退款处理中
		8: 退款失败
		9: 退货完成
		10: 退款完成
		11: 退货退款完成
		12: 已关闭
		@see BatchReturnOrderStatus
	"""
	batchReturnOrderStatus:[Int]
	"""批次退货单关闭信息"""
	batchReturnCloseReason:BatchReturnCloseReasonRequest
	"""批次退货单状态变更时间"""
	batchReturnStatusChangeTime:BatchReturnOrderStatusChangeTimeRequest
	"""退款金额范围
		<br> 查询非0元  begin填0.01
	"""
	refundAmountScope:BigDecimalScopeRequest
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""批次退货单状态变更时间查询参数
	<AUTHOR>
	@date 2022/4/19
"""
input BatchReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.nested.BatchReturnOrderStatusChangeTimeRequest") {
	"""申请退货时间"""
	applied:DateScopeRequest
	"""批次退货完成时间
		<p> 这个参数包含了退货退款完成（批次退货单类型为退货退款）、仅退货完成（批次退货单类型为仅退货）、仅退款完成（批次退货单类型为仅退款）时间，三个时间之间用or匹配
	"""
	returnCompleted:DateScopeRequest
}
"""商品查询条件
	<AUTHOR>
	@date 2022/01/25
"""
input CommoditySkuRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.request.CommoditySkuRequest") {
	"""指定需要查询的sku属性（不指定的话默认查全部）"""
	needQuerySkuPropertyList:[String]
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品名称（精确匹配）"""
	saleTitleList:[String]
	"""商品名称（模糊查询）"""
	saleTitleMatchLike:String
	"""要从查询结果中剔除的商品ID集合"""
	notShowCommoditySkuIdList:[String]
	"""商品售价"""
	price:Double
	"""商品上下架信息"""
	onShelveRequest:OnShelveRequest
	"""培训方案信息"""
	schemeRequest:SchemeRequest
	"""商品sku属性查询"""
	skuPropertyRequest:SkuPropertyRequest
	"""商品sku属性查询(组合查询)
		！！ 由于mongodb限制 不可与 {skuPropertyRequest}同时使用 ！！
	"""
	fixSkuPropertyRequest:[SkuPropertyRequest]
	"""是否展示资源不可用的商品"""
	isDisabledResourceShow:Boolean
	"""是否展示所有资源
		（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
	"""
	isShowAll:Boolean
	"""是否存在专题"""
	existTrainingChannel:Boolean
	"""专题"""
	trainingChannelName:String
	"""专题id"""
	trainingChannelIds:[String]
	"""第三方平台类型ids"""
	tppTypeIds:[String]
	"""分销商id"""
	distributorId:String
	"""门户id"""
	portalId:String
	"""管理系统平台"""
	externalTrainingPlatform:[String]
	"""所属单位ID"""
	unitIdList:[String]
	"""收款主体id集合"""
	payeeIdList:[String]
	"""商品拓展信息请求入参"""
	extInfoRequest:CommoditySkuExtInfoRequest
	"""购买渠道列表"""
	purchaseChannelList:[PurchaseChannelRequest]
	"""是否展示在专题销售渠道"""
	isShowTrainingChannel:Boolean
}
"""商品排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input CommoditySkuSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.request.CommoditySkuSortRequest") {
	"""用来排序的字段"""
	sortField:CommoditySkuSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""<AUTHOR> linq
	@date : 2024-12-10 14:03
	@description：期别商品查询请求入参
"""
input IssueCommoditySkuBackStageRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.request.IssueCommoditySkuBackStageRequest") {
	"""商品id"""
	commoditySkuId:String
	"""剔除的期别id集合"""
	excludedIssueIdList:[String]
	"""期别名称(精确查询)"""
	issueNameExact:String
	"""期别名称(模糊查询)"""
	issueNameMatchLike:String
	"""期别报名开始时间"""
	issueSignUpBeginDate:DateScopeRequest
	"""期别报名结束时间"""
	issueSignUpEndDate:DateScopeRequest
	"""期别培训开始时间"""
	issueTrainingBeginTime:DateScopeRequest
	"""期别培训结束时间"""
	issueTrainingEndTime:DateScopeRequest
}
"""<AUTHOR> linq
	@date : 2025-01-07 14:47
	@description：商品拓展信息请求入参
"""
input CommoditySkuExtInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.request.nested.CommoditySkuExtInfoRequest") {
	"""资源供应商id集合"""
	resourceProviderIdList:[String]
}
input PurchaseChannelRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.request.nested.PurchaseChannelRequest") {
	"""渠道类型 | 1、用户web自主购买 2、用户h5自主购买 3、用户小程序自主购买 4、用户公众号自主购买 5、集体缴费 6、管理员导入 7、集体报名个人缴费渠道
		@see PurchaseChannelType
	"""
	purchaseChannelType:Int
	"""是否可见"""
	couldSee:Boolean
	"""是否可购买"""
	couldBuy:Boolean
}
"""商品上下架相关查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input OnShelveRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.request.nested.onshevl.OnShelveRequest") {
	"""商品上下架状态
		<br> 0:已下架 1：已上架
	"""
	onShelveStatus:Int
}
"""培训方案相关查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input SchemeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.request.nested.scheme.SchemeRequest") {
	"""培训方案ID"""
	schemeIdList:[String]
	"""排除的培训方案ID"""
	excludedSchemeIdList:[String]
	"""培训方案类型
		<br> chooseCourseLearning:选课规则 autonomousCourseLearning:自主学习 trainingCooperation:培训合作
	"""
	schemeType:String
	"""培训方案名称(模糊查询)"""
	schemeName:String
	"""培训开始时间"""
	trainingBeginDate:DateScopeRequest
	"""培训结束时间"""
	trainingEndDate:DateScopeRequest
	"""方案学时"""
	period:DoubleScopeRequest
}
"""<AUTHOR> linq
	@date : 2023-12-25 12:08
	@description：商品分销授权信息请求参数
"""
input CommodityAuthInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommodityAuthInfoRequest") {
	"""商品授权分销商ID"""
	distributorId:String
	"""分销级别
		@see DistriButionLevel
	"""
	distributionLevel:Int
	"""上级分销商ID（仅二级分销商有该值，分销合同内的上级分销商ID）"""
	superiorDistributorId:String
	"""商品授权供应商ID"""
	supplierId:String
	"""供应商对接业务员ID（仅订单创建时有该分销商+网校有业务员时才有值）"""
	salesmanId:String
}
"""商品查询条件
	<AUTHOR>
	@date 2022/01/25
"""
input CommoditySkuRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.CommoditySkuRequest") {
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品Sku名称"""
	saleTitle:String
	"""期别信息"""
	issueInfo:IssueInfo
	"""商品sku属性查询"""
	skuProperty:SkuPropertyRequest
	"""管理系统平台"""
	externalTrainingPlatform:[String]
	"""培训机构"""
	trainingInstitution:[String]
}
"""发票关联订单查询参数
	<AUTHOR>
	@date 2022/3/18
"""
input InvoiceAssociationInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.InvoiceAssociationInfoRequest") {
	"""关联订单类型
		0:订单号
		1:批次单号
		@see AssociationTypes
	"""
	associationType:Int
	"""订单号 | 批次单号"""
	associationIdList:[String]
	"""买家信息"""
	buyerIdList:[String]
	"""收款账号"""
	receiveAccountIdList:[String]
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""地区查询参数
	<AUTHOR>
	@date 2022/02/25
"""
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertyRequest") {
	"""地区: 省"""
	province:String
	"""地区: 市"""
	city:String
	"""地区: 区县"""
	county:String
}
"""地区查询请求参数
	<AUTHOR>
	@date 2022/02/25
"""
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.RegionSkuPropertySearchRequest") {
	"""地区匹配方式
		<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
		<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
		@see RegionSearchType
	"""
	regionSearchType:Int
	"""地区"""
	region:[RegionSkuPropertyRequest]
}
"""商品sku属性查询参数
	<AUTHOR>
	@date 2022/01/25
"""
input SkuPropertyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.skuProperty.SkuPropertyRequest") {
	"""年度"""
	year:[String]
	"""地区"""
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	"""行业"""
	industry:[String]
	"""科目类型"""
	subjectType:[String]
	"""培训类别"""
	trainingCategory:[String]
	"""培训专业"""
	trainingProfessional:[String]
	"""技术等级"""
	technicalGrade:[String]
	"""卫生行业-培训对象"""
	trainingObject:[String]
	"""卫生行业-岗位类别"""
	positionCategory:[String]
	"""工勤行业-技术等级"""
	jobLevel:[String]
	"""工勤行业-工种"""
	jobCategory:[String]
	"""年级"""
	grade:[String]
	"""科目"""
	subject:[String]
	"""学段"""
	learningPhase:[String]
	"""学科"""
	discipline:[String]
	"""专题id"""
	trainingChannelIds:[String]
	"""黑龙江药师-证书类型"""
	certificatesType:[String]
	"""黑龙江药师-执业类别"""
	practitionerCategory:[String]
	"""资质类别"""
	qualificationCategory:[String]
	"""培训形式"""
	trainingForm:[String]
}
input IssueInfo @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.IssueInfo") {
	"""期别id"""
	issueId:String
	"""期别名称"""
	issueName:String
	"""培训编号"""
	issueNum:String
	"""培训开始时间"""
	trainStartTime:DateTime
	"""培训结束时间"""
	trainEndTime:DateTime
	"""当前期别来源类型。下单：SUB_ORDER 换班：EXCHANGE_ORDER 换期:CHANGE_ISSUE"""
	sourceType:String
	"""期别来源类型id 1:子订单号：2：换货单号：3：旧的期别参训资格D"""
	sourceId:String
}
"""获取网校提供的配送渠道查询"""
input DeliveryChannelRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.deliverychannel.gateway.graphql.request.DeliveryChannelRequest") {
	"""单位ID"""
	unit:String
}
"""换货单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ExchangeOrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.request.ExchangeOrderRequest") {
	"""换货单号集合"""
	exchangeOrderNoList:[String]
	"""换货单关联子订单号集合"""
	subOrderNoList:[String]
	"""换货单关联批次单号"""
	batchOrderNoList:[String]
	"""换货单关联子订单买家ID集合"""
	buyerIdList:[String]
	"""商品信息: 任一匹配原始商品和新商品"""
	commodity:CommoditySkuRequest1
	"""换货单状态
		<p>
		0: 申请换货
		1: 取消中
		2: 退货中
		3: 退货失败
		4: 申请发货
		5: 发货中
		6: 发货失败
		7: 换货完成
		8: 已关闭
		@see ExchangeOrderStatus
	"""
	statusList:[Int]
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
}
"""换货单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input ExchangeOrderSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.request.ExchangeOrderSortRequest") {
	"""需要排序的字段"""
	field:ExchangeOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""线下发票查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.request.OfflineInvoiceRequest") {
	"""发票ID集合"""
	invoiceIdList:[String]
	"""发票基本信息"""
	basicData:OfflineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfo:InvoiceAssociationInfoRequest
	"""发票配送信息"""
	invoiceDeliveryInfo:OfflineInvoiceDeliveryInfoRequest
	"""所属单位id集合"""
	unitIds:[String]
	"""收款账号id"""
	receiveAccountId:[String]
	"""期别名称"""
	issueId:[String]
}
"""发票排序条件
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.request.OfflineInvoiceSortRequest") {
	"""用于排序的字段"""
	field:OfflineInvoiceSortField
	"""正序或倒序"""
	policy:SortPolicy1
}
"""配送地址信息
	<AUTHOR>
	@date 2022/05/07
"""
input DeliveryAddressRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.request.nested.DeliveryAddressRequest") {
	"""收件人"""
	consignee:String
}
"""配送状态变更时间查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input DeliveryStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.request.nested.DeliveryStatusChangeTimeRequest") {
	"""未就绪"""
	unReady:DateScopeRequest
	"""已就绪"""
	ready:DateScopeRequest
	"""已配送"""
	shipped:DateScopeRequest
	"""已自取"""
	taken:DateScopeRequest
}
"""快递信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input ExpressRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.request.nested.ExpressRequest") {
	"""快递单号"""
	expressNo:String
}
"""发票开票状态变更时间记录查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input InvoiceBillStatusChangTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.request.nested.InvoiceBillStatusChangTimeRequest") {
	"""发票申请开票时间"""
	unBillDateScope:DateScopeRequest
	"""发票开票时间"""
	successDateScope:DateScopeRequest
}
"""线下发票基本信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.request.nested.OfflineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceTypeList:[Int]
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategory:[Int]
	"""发票状态
		1:正常
		2:作废
		@see InvoiceStatus
	"""
	invoiceStatus:Int
	"""发票状态变更时间记录"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeRequest
	"""发票开票状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	billStatusList:[Int]
	"""发票开票状态变更时间记录"""
	billStatusChangTime:InvoiceBillStatusChangTimeRequest
	"""发票是否冻结"""
	freeze:Boolean
	"""发票号集合"""
	invoiceNoList:[String]
	"""商品id集合"""
	commoditySkuIdList:[String]
}
"""线下发票配送信息
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceDeliveryInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.request.nested.OfflineInvoiceDeliveryInfoRequest") {
	"""配送状态
		0:未就绪 1：已就绪 2：已自取 3：已配送
		@see OfflineDeliveryStatus
	"""
	deliveryStatusList:[Int]
	"""配送状态变更时间记录 or 匹配
		0:未就绪 1：已就绪 2：已自取 3：已配送
		key值 {@link OfflineDeliveryStatus}
	"""
	deliveryStatusChangeTime:DeliveryStatusChangeTimeRequest
	"""配送方式
		0:无 1：自取 2：快递
		@see OfflineShippingMethods
	"""
	shippingMethodList:[Int]
	"""快递信息"""
	express:ExpressRequest
	"""自取信息"""
	takeResult:TakeResultRequest
	"""配送地址信息"""
	deliveryAddress:DeliveryAddressRequest
}
"""取件信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input TakeResultRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.request.nested.TakeResultRequest") {
	"""领取人"""
	takePerson:String
}
"""发票查询参数
	<AUTHOR>
	@date 2022/03/23
"""
input OnlineInvoiceRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.request.OnlineInvoiceRequest") {
	"""发票id集合"""
	invoiceIdList:[String]
	"""发票基础信息查询参数"""
	basicData:OnlineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfoList:[InvoiceAssociationInfoRequest]
	"""蓝票票据查询参数"""
	blueInvoiceItem:OnlineInvoiceItemRequest
	"""红票票据查询参数"""
	redInvoiceItem:OnlineInvoiceItemRequest
	"""所属单位id集合"""
	unitIds:[String]
	"""收款账号id"""
	receiveAccountId:[String]
	"""期别名称"""
	issueId:[String]
}
"""发票排序参数
	<AUTHOR>
	@date 2022/03/23
"""
input OnlineInvoiceSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.request.OnlineInvoiceSortRequest") {
	"""需要排序的字段"""
	field:OnlineInvoiceSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""发票开具状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input BillStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.request.nested.BillStatusChangeTimeRequest") {
	"""未开具"""
	unBill:DateScopeRequest
	"""开票中"""
	billing:DateScopeRequest
	"""开票成功"""
	success:DateScopeRequest
	"""开票失败"""
	failure:DateScopeRequest
}
"""发票状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input InvoiceStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.request.nested.InvoiceStatusChangeTimeRequest") {
	"""正常"""
	normal:DateScopeRequest
	"""作废"""
	invalid:DateScopeRequest
}
"""发票基础信息查询参数
	<AUTHOR>
	@date 2022/03/23
"""
input OnlineInvoiceBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.request.nested.OnlineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceType:Int
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategoryList:[Int]
	"""发票状态变更时间
		@see InvoiceStatus
	"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeRequest
	"""发票状态
		1：正常 2：作废
		@see InvoiceStatus
	"""
	invoiceStatusList:[Int]
	"""蓝票票据开具状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	blueInvoiceItemBillStatusList:[Int]
	"""红票票据开具状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	redInvoiceItemBillStatusList:[Int]
	"""发票是否已冲红"""
	flushed:Boolean
	"""发票是否已生成红票票据"""
	redInvoiceItemExist:Boolean
	"""商品id集合"""
	commoditySkuIdList:[String]
	"""发票是否冻结"""
	freeze:Boolean
}
"""发票票据
	<AUTHOR>
	@date 2022/03/18
"""
input OnlineInvoiceItemRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.request.nested.OnlineInvoiceItemRequest") {
	"""票据开具状态变更时间"""
	billStatusChangeTime:BillStatusChangeTimeRequest
	"""发票号码"""
	billNoList:[String]
}
"""用户有效商品查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input BuyerValidCommodityRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.BuyerValidCommodityRequest") {
	"""买家id"""
	buyerId:String
	"""商品sku查询"""
	skuProperty:SkuPropertyRequest
	"""商品名称（模糊查询）"""
	saleTitle:String
	"""期别名称（模糊查询）"""
	issueName:String
}
input IssueLogRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.IssueLogRequest") {
	"""子订单号"""
	subOrderNoList:[String]
	"""买家id"""
	buyerId:String
	"""商品sku查询"""
	skuProperty:SkuPropertyRequest
	"""商品名称（模糊查询）"""
	saleTitle:String
	"""期别名称（模糊查询）"""
	issueName:String
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input OrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.OrderRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""子订单号集合"""
	subOrderNoList:[String]
	"""子订单退货状态
		0: 未退货
		1: 退货申请中
		2: 退货中
		3: 退货成功
		4: 退款中
		5: 退款成功
		@see SubOrderReturnStatus
	"""
	subOrderReturnStatus:[Int]
	"""订单基本信息查询参数"""
	orderBasicData:OrderBasicDataRequest
	"""子订单基本信息查询参数"""
	subOrderBasicData:SubOrderBasicDataRequest
	"""订单支付信息查询"""
	payInfo:OrderPayInfoRequest
	"""买家查询参数"""
	buyerIdList:[String]
	"""发货商品信息"""
	deliveryCommodity:CommoditySkuRequest1
	"""现有商品信息"""
	currentCommodity:CommoditySkuRequest1
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannel:Int
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""需要排除的销售渠道
		0-自营 1-分销 2专题
	"""
	excludeSaleChannels:[Int]
	"""专题id"""
	saleChannelIds:[String]
	"""专题名称"""
	saleChannelName:String
	"""华医网卡类型id"""
	cardTypeId:String
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""订单属性组合查询"""
	orderFixQuery:OrderFixQueryRequest
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
	"""管理系统平台"""
	externalTrainingPlatform:[String]
	"""单位id集合"""
	unitIds:[String]
	"""期别id"""
	issueId:[String]
	"""培训计划ID，例如补贴性培训平台和补贴管理系统对接"""
	policyTrainingSchemeIdList:[String]
	"""申报单位统一信用代码，精确匹配"""
	declarationUnitCodeList:[String]
	"""结算状态
		@see com.fjhb.ms.trade.query.common.constants.SettlementStatusConstants
	"""
	settlementStatus:Int
	"""结算时间"""
	settlementDate:DateScopeRequest
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input OrderRequestInDistributor @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.OrderRequestInDistributor") {
	"""订单号集合"""
	orderNoList:[String]
	"""子订单号集合"""
	subOrderNoList:[String]
	"""子订单退货状态
		0: 未退货
		1: 退货申请中
		2: 退货中
		3: 退货成功
		4: 退款中
		5: 退款成功
		@see SubOrderReturnStatus
	"""
	subOrderReturnStatus:[Int]
	"""订单基本信息查询参数"""
	orderBasicData:OrderBasicDataRequest
	"""子订单基本信息查询参数"""
	subOrderBasicData:SubOrderBasicDataRequest
	"""订单支付信息查询"""
	payInfo:OrderPayInfoRequest
	"""买家查询参数"""
	buyerIdList:[String]
	"""发货商品信息"""
	deliveryCommodity:CommoditySkuRequest1
	"""现有商品信息"""
	currentCommodity:CommoditySkuRequest1
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannel:Int
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题id"""
	saleChannelIds:[String]
	"""专题名称"""
	saleChannelName:String
	"""华医网卡类型id"""
	cardTypeId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.OrderSortRequest") {
	"""需要排序的字段"""
	field:OrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""订单基本信息查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input OrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderBasicDataRequest") {
	"""订单类型
		1:常规订单 2:批次关联订单
		@see com.fjhb.domain.trade.api.order.consts.OrderTypes
	"""
	orderType:Int
	"""批次单号"""
	batchOrderNoList:[String]
	"""订单状态
		<br> 1:正常 2：交易完成 3：交易关闭
		@see OrderStatus
	"""
	orderStatusList:[Int]
	"""订单支付状态
		<br> 0:未支付 1：支付中 2：已支付
		@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
	"""
	orderPaymentStatusList:[Int]
	"""订单发货状态
		<br> 0:未发货 1：发货中 2：已发货
		@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
	"""
	orderDeliveryStatusList:[Int]
	"""订单状态变更时间"""
	orderStatusChangeTime:OrderStatusChangeTimeRequest
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelTypesList:[Int]
	"""需要排除的购买渠道
		<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	excludeChannelTypesList:[Int]
	"""终端
		<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see PurchaseChannelTerminalCodes
	"""
	terminalCodeList:[String]
	"""订单价格范围
		<br> 查询非0元订单 begin填0.01
	"""
	orderAmountScope:BigDecimalScopeRequest
}
"""<AUTHOR> linq
	@date : 2024-09-05 18:18
	@description：订单属性组合查询
"""
input OrderFixQueryRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderFixQueryRequest") {
	"""需要排除的购买渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	excludeChannelTypesList:[Int]
	"""需要排除的销售渠道"""
	excludeSaleChannels:[Int]
}
"""订单支付信息相关查询参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderPayInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderPayInfoRequest") {
	"""收款账号ID"""
	receiveAccountIdList:[String]
	"""交易流水号"""
	flowNoList:[String]
	"""付款类型
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
	"""
	paymentOrderTypeList:[Int]
}
"""订单状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input OrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderStatusChangeTimeRequest") {
	"""订单处于正常状态时间范围(创建时间范围)"""
	normalDateScope:DateScopeRequest
	"""订单创建时间范围"""
	completedDatesScope:DateScopeRequest
}
"""子订单基本信息查询参数
	<AUTHOR>
	@date 2023/12/25
"""
input SubOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.SubOrderBasicDataRequest") {
	"""优惠类型
		@see DiscountType
	"""
	discountType:Int
	"""优惠来源ID | 优惠申请单ID"""
	discountSourceId:String
	"""是否使用优惠"""
	useDiscount:Boolean
	"""商品分销授权信息"""
	commodityAuthInfo:CommodityAuthInfoRequest
}
"""退货单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.ReturnOrderRequest") {
	"""单位id集合"""
	unitIdList:[String]
	"""退货单号"""
	returnOrderNoList:[String]
	"""基本信息"""
	basicData:ReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:ReturnOrderApprovalInfoRequest
	"""退货商品id集合"""
	returnCommoditySkuIdList:[String]
	"""退货商品查询条件"""
	returnCommodity:CommoditySkuRequest1
	"""退款商品id集合"""
	refundCommoditySkuIdList:[String]
	"""退款商品查询条件"""
	refundCommodity:CommoditySkuRequest1
	"""退货单关联子订单查询参数"""
	subOrderInfo:SubOrderInfoRequest
	"""商品分销授权信息"""
	commodityAuthInfo:CommodityAuthInfoRequest
	"""分销商id"""
	distributorId:String
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
}
"""退货单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderRequestInDistributor @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.ReturnOrderRequestInDistributor") {
	"""退货单号"""
	returnOrderNoList:[String]
	"""基本信息"""
	basicData:ReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:ReturnOrderApprovalInfoRequest
	"""退货商品id集合"""
	returnCommoditySkuIdList:[String]
	"""退款商品id集合"""
	refundCommoditySkuIdList:[String]
	"""退货单关联子订单查询参数"""
	subOrderInfo:SubOrderInfoRequest
	"""商品分销授权信息"""
	commodityAuthInfo:CommodityAuthInfoRequest
	"""推广门户id"""
	portalId:String
	"""是否开启分销商下排除推广门户的订单"""
	isDistributionExcludePortal:Boolean
	"""退货商品查询条件"""
	returnCommodity:CommoditySkuRequest1
	"""退款商品查询条件"""
	refundCommodity:CommoditySkuRequest1
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input ReturnSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.ReturnSortRequest") {
	"""需要排序的字段"""
	field:ReturnOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""退货单关联子订单的主订单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input OrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.OrderInfoRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""关联批次单号"""
	batchOrderNoList:[String]
	"""买家id集合"""
	buyerIdList:[String]
	"""收款账号ID"""
	receiveAccountIdList:[String]
	"""原始订单交易流水号"""
	flowNoList:[String]
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelTypesList:[Int]
	"""终端
		<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see PurchaseChannelTerminalCodes
	"""
	terminalCodeList:[String]
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	"""销售渠道
		0-自营 1-分销 2专题 不传则查全部
	"""
	saleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
	"""培训计划ID，例如补贴性培训平台和补贴管理系统对接"""
	policyTrainingSchemeIdList:[String]
	"""申报单位统一信用代码，精确匹配"""
	declarationUnitCodeList:[String]
}
"""退货单关闭信息
	<AUTHOR>
	@date 2022年4月11日 11:33:35
"""
input ReturnCloseReasonRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnCloseReasonRequest") {
	"""退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
		@see ReturnOrderCloseTypes
	"""
	closeTypeList:[Int]
}
"""退货单审批信息查询参数
	<AUTHOR>
	@date 2022/03/18
"""
input ReturnOrderApprovalInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderApprovalInfoRequest") {
	"""审批时间"""
	approveTime:DateScopeRequest
}
"""退货单基本信息查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderBasicDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderBasicDataRequest") {
	"""退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)"""
	returnOrderStatus:[Int]
	"""退货单类型
		1-仅退货
		2-仅退款
		3-退货并退款
		4-部分退货
		5-部分退款
		6-部分退货并部分退款
		7-部分退货并全额退款
		8-全部退货并部分退款
	"""
	returnOrderTypes:[Int]
	"""退货单申请来源类型
		SUB_ORDER
		BATCH_RETURN_ORDER
		@see ReturnOrderApplySourceTypes
	"""
	applySourceType:String
	"""来源ID集合"""
	applySourceIdList:[String]
	"""退货单关闭信息"""
	returnCloseReason:ReturnCloseReasonRequest
	"""退货单状态变更时间"""
	returnStatusChangeTime:ReturnOrderStatusChangeTimeRequest
	"""退款金额范围
		<br> 查询非0元  begin填0.01
	"""
	refundAmountScope:BigDecimalScopeRequest
}
"""退货单状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderStatusChangeTimeRequest") {
	"""申请退货时间"""
	applied:DateScopeRequest
	"""退货单完成时间
		<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
	"""
	returnCompleted:DateScopeRequest
}
"""<AUTHOR>
	@date 2022/03/24
"""
input SubOrderInfoRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.SubOrderInfoRequest") {
	"""子订单号集合"""
	subOrderNoList:[String]
	"""订单查询参数"""
	orderInfo:OrderInfoRequest
	"""子订单优惠类型
		@see DiscountType
	"""
	discountType:Int
	"""子订单是否使用优惠"""
	useDiscount:Boolean
}
"""发票开票策略查询条件
	<AUTHOR>
"""
input InvoiceAutoBillPolicyRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.request.InvoiceAutoBillPolicyRequest") {
	"""自动配置类型
		<br> 1:个人发票 2：集体发票
	"""
	configType:Int
}
"""收款账号查询条件"""
input ReceiveAccountConfigRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.request.ReceiveAccountConfigRequest") {
	"""收款账户类型
		<p>
		1：线上收款帐号
		2：线下收款帐号
	"""
	accountType:Int
	"""收款帐号状态
		<p>
		0:停用
		1:可用
	"""
	status:Int
	"""服务商ID"""
	servicerId:String
	"""单位Id"""
	unitId:String
}
"""获取收款账号列表"""
input ReceiveAccountRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.request.ReceiveAccountRequest") {
	"""收款账户"""
	receiveAccountIdList:[String]
	"""单位Id"""
	unitId:String
}
"""获取纳税人去请求"""
input TaxpayerIRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.request.TaxpayerIRequest") {
	"""单位id"""
	unitId:String
}
"""商品开通统计排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input CommodityOpenReportSortRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.request.CommodityOpenReportSortRequest") {
	"""需要排序的字段"""
	field:CommodityOpenReportSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""<AUTHOR> linq
	@date : 2024-10-10 10:25
	@description：交易记录缺失数据比对request
"""
input TradeReportMissDataRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.request.TradeReportMissDataRequest") {
	"""本次需要收集的订单数量"""
	collectNum:Int
	"""交易时间范围"""
	tradeTime:DateScopeRequest
}
"""商品开通统计报表查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input TradeReportRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.request.TradeReportRequest") {
	"""交易时间范围"""
	tradeTime:DateScopeRequest
	"""买家所在地区路径"""
	buyerAreaPath:[String]
	"""商品地区路径"""
	commodityAreaPath:[String]
	"""商品查询条件"""
	commoditySku:CommoditySkuRequest12
	"""销售渠道
		0-自营 1-分销 不传则查全部
	"""
	saleChannel:Int
	"""销售渠道
		0-自营 1-分销 2专题 3-华医网 不传则查全部
	"""
	saleChannels:[Int]
	"""排除的销售渠道
		0-自营 1-分销 2-专题 3-华医网
	"""
	excludedSaleChannels:[Int]
	"""专题名称"""
	saleChannelName:String
	"""专题id"""
	saleChannelIds:[String]
	"""分销商id"""
	distributorId:String
	"""门户id"""
	portalId:String
	"""查看非推广门户数据 | true 为勾选效果"""
	notDistributionPortal:Boolean
	"""收款账户"""
	receiveAccountIdList:[String]
	"""期别id"""
	issueId:[String]
	"""机构ID集合"""
	institutionIdList:[String]
}
"""交易时间段统计信息直方图参数
	<AUTHOR>
"""
input TradeStatisticDateHistogramRequest @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.request.TradeStatisticDateHistogramRequest") {
	"""时间单位  1 周 、2 年、3 季度、 4 月、 5 天、6 小时、7 分钟、8 秒
		@see DateHistogramTimeUnit
	"""
	timeUnit:Int
	"""时间单位间隔"""
	interval:Int
	"""统计日期"""
	statisticTime:DateScopeRequest
}
"""商品查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input CommoditySkuRequest12 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.request.nested.CommoditySkuRequest") {
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyRequest
	"""学习方案查询参数"""
	scheme:SchemeRequest1
	"""排除的商品List"""
	excludeCommodityIdlist:[String]
}
"""方案查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input SchemeRequest1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.request.nested.SchemeRequest") {
	"""方案类型
		@see SchemeType
	"""
	schemeType:String
	"""方案学时"""
	schemePeriodScope:DoubleScopeRequest
}
type UserEntity @type(value:"com.fjhb.ms.trade.query.common.entity.UserEntity") {
	userId:String
}
type DiscountPolicyModel @type(value:"com.fjhb.ms.trade.query.common.model.DiscountPolicyModel") {
	discountPolicyId:String
	discountId:Int
}
type RegionModel @type(value:"com.fjhb.ms.trade.query.common.model.RegionModel") {
	regionId:String
	province:String
	city:String
	county:String
	path:String
}
type UserModel @type(value:"com.fjhb.ms.trade.query.common.model.UserModel") {
	userId:String
}
"""批次单可用于排序的字段
	<AUTHOR>
	@date 2022/04/17
"""
enum BatchOrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.request.nested.BatchOrderSortField") {
	"""批次单未确认时间（批次单创建）"""
	BATCH_ORDER_UN_CONFIRMED_TIME
	"""批次单提交时间"""
	BATCH_ORDER_COMMIT_TIME
}
"""批次单返回模型
	<AUTHOR>
	@date 2022/4/16
"""
type BatchOrderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.response.BatchOrderResponse") {
	"""批次单号"""
	batchOrderNo:String
	"""批次单基本信息"""
	basicData:BatchOrderBasicDataResponse
	"""支付信息"""
	payInfo:PayInfoResponse
	"""创建人"""
	creator:UserResponse
	"""是否已经申请发票"""
	isInvoiceApplied:Boolean
	"""发票申请信息"""
	invoiceApplyInfo:InvoiceApplyInfoResponse
}
"""批次单统计信息返回值
	<AUTHOR>
	@date 2022/01/26
"""
type BatchOrderStatisticResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.response.BatchOrderStatisticResponse") {
	"""批次单总数量"""
	totalBatchOrderCount:Long
	"""批次单总金额"""
	totalBatchOrderAmount:BigDecimal
}
"""批次单基本信息
	<AUTHOR>
	@date 2022/4/17
"""
type BatchOrderBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.response.nested.BatchOrderBasicDataResponse") {
	"""批次单类型
		<p>
		0；批次缴费批次单
		1；个人缴费批次单
		2；无需缴费批次单
		@see BatchOrderTypes
	"""
	batchOrderType:Int
	"""终端类型
		<p>
		Web: Web端
		H5: H5端
		IOS: IOS端
		Android: 安卓端
		WechatMini: 微信小程序
		WechatOfficial: 微信公众号
		ExternalSystemManage: 外部管理系统
		@see PurchaseChannelTerminalCodes
	"""
	terminalCode:String
	"""批次单金额"""
	amount:BigDecimal
	"""批次单的批次订单数量"""
	orderForBatchCount:Long
	"""批次单状态
		0: 未确认，批次单初始状态
		1: 正常
		2: 交易完成
		3: 交易关闭
		4: 提交处理中 提交处理完成后，变更为NORMAl
		5: 取消处理中
		@see BatchOrderStatus
	"""
	batchOrderStatus:Int
	"""批次单状态变更时间记录"""
	batchOrderStatusChangeTime:BatchOrderStatusChangeTimeResponse
	"""批次单支付状态
		<p>
		0：未支付
		1：支付中
		2：已支付
		@see BatchOrderPaymentStatus
	"""
	paymentStatus:Int
	"""批次单支付状态变更时间"""
	paymentStatusChangeTime:BatchOrderPaymentStatusChangeTimeResponse
	"""批次单发货状态
		0: 未发货
		1: 发货中
		2: 已发货
		@see BatchOrderDeliveryStatus
	"""
	deliveryStatus:Int
	"""批次单发货状态变更时间"""
	deliveryStatusChangeTime:BatchOrderDeliveryStatusChangeTimeResponse
	"""批次单退货状态
		0: 未退货
		1: 退货中
		2: 已部分退货
		3: 退货完成
		@see BatchOrderReturnStatus
	"""
	batchOrderReturnStatus:Int
	"""批次单退款状态
		0: 未退款
		1: 退款中
		2: 已部分退货
		3: 退货完成
		@see BatchOrderRefundStatus
	"""
	batchOrderRefundStatus:Int
	"""批次单关闭原因"""
	batchOrderCloseReason:BatchOrderCloseReasonResponse
	"""销售渠道
		@see SaleChannel
	"""
	saleChannel:Int
	"""销售渠道id"""
	saleChannelId:String
	"""专题名称"""
	saleChannelName:String
	"""销售全路径"""
	salePathList:[SalePathResponse]
}
"""批次单交易关闭原因
	<AUTHOR>
	@date 2022/2/14
"""
type BatchOrderCloseReasonResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.response.nested.BatchOrderCloseReasonResponse") {
	"""交易关闭类型
		<p>
		0：未关闭
		1：买家关闭
		2：系统关闭
		@see BatchOrderCloseTypes
	"""
	closedType:Int
	"""交易关闭原因ID"""
	reasonId:String
	"""交易关闭原因说明"""
	reason:String
	"""取消操作人"""
	cancelUser:UserResponse
}
"""批次单发货状态变更时间
	<AUTHOR>
	@date 2022/04/17
"""
type BatchOrderDeliveryStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.response.nested.BatchOrderDeliveryStatusChangeTimeResponse") {
	"""未确认"""
	none:DateTime
	"""发货中"""
	delivering:DateTime
	"""已发货"""
	delivered:DateTime
}
"""批次单支付状态变更时间
	<AUTHOR>
	@date 2022/04/17
"""
type BatchOrderPaymentStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.response.nested.BatchOrderPaymentStatusChangeTimeResponse") {
	"""未确认"""
	none:DateTime
	"""支付中"""
	paying:DateTime
	"""已支付"""
	paid:DateTime
}
"""批次单状态变更时间
	<AUTHOR>
	@date 2022/04/17
"""
type BatchOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchorder.gateway.graphql.response.nested.BatchOrderStatusChangeTimeResponse") {
	"""未确认"""
	unConfirmed:DateTime
	"""正常"""
	normal:DateTime
	"""交易成功"""
	completed:DateTime
	"""已关闭"""
	closed:DateTime
	"""提交中"""
	committing:DateTime
	canceling:DateTime
}
"""批次退货单可用于排序的字段
	<AUTHOR>
	@date 2022/04/19
"""
enum BatchReturnOrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.request.nested.BatchReturnOrderSortField") {
	"""批次退货单创建时间"""
	CREATED_TIME
}
"""批次退货单
	<AUTHOR>
	@date 2022/4/19
"""
type BatchReturnOrderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.BatchReturnOrderResponse") {
	"""批次退货单号"""
	batchReturnOrderNo:String
	"""批次退货单基本信息"""
	basicData:BatchReturnOrderBasicDataResponse
	"""批次退货单关联批次单信息"""
	batchOrderInfo:BatchOrderInfoResponse
	"""批次退货单是否需要人工审批"""
	needManualApprove:Boolean
	"""批次退货单审批信息"""
	approvalInfo:BatchReturnApprovalInfoResponse
	"""退款确认人"""
	confirmUser:UserResponse
	"""批次退货单关联退款单信息"""
	refundInfo:RefundInfoResponse
}
"""批次退货单统计信息返回值
	<AUTHOR>
	@date 2022/01/26
"""
type BatchReturnOrderStatisticResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.BatchReturnOrderStatisticResponse") {
	"""批次退货单总数量"""
	totalBatchReturnOrderCount:Long
	"""批次退款单退款总金额"""
	totalBatchReturnOrderRefundAmount:BigDecimal
}
"""批次退货单关联批次单信息
	<AUTHOR>
	@date 2022/04/19
"""
type BatchOrderInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchOrderInfoResponse") {
	"""批次单号"""
	batchOrderNo:String
	"""批次单支付信息"""
	paymentInfo:PaymentInfoResponse
	"""批次单创建人"""
	creator:UserResponse
}
"""批次退货审批信息
	<AUTHOR>
	@date 2022/03/18
"""
type BatchReturnApprovalInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnApprovalInfoResponse") {
	"""审批状态（0：未审批 1：已审批）
		@see BatchReturnApprovalReportStatus
	"""
	approveStatus:Int
	"""审批结果（-1：无 0：拒绝 1：同意）
		@see BatchReturnApprovalReportResults
	"""
	approveResult:Int
	"""审批人"""
	approveUser:UserResponse
	"""审批意见"""
	approveComment:String
	"""审批时间"""
	approveTime:DateTime
}
"""批次退货申请信息返回值
	<AUTHOR>
	@date 2022/03/24
"""
type BatchReturnOrderApplyInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderApplyInfoResponse") {
	"""申请人"""
	applyUser:UserResponse
	"""申请原因内容id"""
	reasonId:String
	"""申请原因内容"""
	reasonContent:String
	"""申请描述"""
	description:String
}
"""批次退货单基本信息
	<AUTHOR>
	@date 2022/04/19
"""
type BatchReturnOrderBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderBasicDataResponse") {
	"""批次退货单类型
		1: 仅退货
		2: 仅退款
		3: 退货退款
		@see BatchReturnOrderTypes
	"""
	batchReturnOrderType:Int
	"""退款总额"""
	refundAmount:BigDecimal
	"""批次退货单的退货单数量"""
	returnOrderCount:Long
	"""批次退货单状态
		0: 已创建
		1: 已确认
		2: 取消申请中
		3: 退货处理中
		4: 退货失败
		5: 正在申请退款
		6: 已申请退款
		7: 退款处理中
		8: 退款失败
		9: 退货完成
		10: 退款完成
		11: 退货退款完成
		12: 已关闭
		@see BatchReturnOrderStatus
	"""
	batchReturnOrderStatus:Int
	"""批次退货单状态变更时间"""
	batchReturnOrderStatusChangeTime:BatchReturnOrderStatusChangeTimeResponse
	"""批次退货单申请信息"""
	applyInfo:BatchReturnOrderApplyInfoResponse
	"""批次退货单关闭信息"""
	closeReason:BatchReturnOrderCloseReasonResponse
	"""销售渠道
		@see SaleChannel
	"""
	saleChannel:Int
	"""销售渠道id"""
	saleChannelId:String
	"""专题名称"""
	saleChannelName:String
	salePathList:[SalePathResponse]
}
"""批次退货单关闭原因
	<AUTHOR>
	@date 2022/03/29
"""
type BatchReturnOrderCloseReasonResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderCloseReasonResponse") {
	"""批次退货单关闭类型（（1：卖家取消 2：卖家拒绝退货 3：买家取消 4：确认失败取消））
		@see BatchReturnCloseTypes
	"""
	closeType:Int
	"""退货单取消人"""
	cancelUser:UserResponse
	"""取消原因"""
	cancelReason:String
	"""确认失败信息"""
	confirmFailureMessage:String
}
"""退货单状态变更时间
	<AUTHOR>
	@date 2022/03/23
"""
type BatchReturnOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.batchreturnorder.gateway.graphql.response.nested.BatchReturnOrderStatusChangeTimeResponse") {
	"""已创建"""
	created:DateTime
	"""已确认"""
	confirmed:DateTime
	"""申请退货取消处理中时间"""
	cancelApplying:DateTime
	"""退货处理中时间"""
	returning:DateTime
	"""退货失败时间"""
	returnFailed:DateTime
	"""正在申请退款时间"""
	refundApplying:DateTime
	"""已申请退款时间"""
	refundApplied:DateTime
	"""退款处理中时间"""
	refunding:DateTime
	"""退款失败"""
	refundFailed:DateTime
	"""退货完成时间"""
	returned:DateTime
	"""退款完成时间"""
	refunded:DateTime
	"""退货退款完成时间"""
	returnedAndRefunded:DateTime
	"""退货单完成时间"""
	returnCompleted:DateTime
	"""已关闭时间"""
	closed:DateTime
}
"""商品可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum CommoditySkuSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.request.nested.CommoditySkuSortField") {
	"""上架时间"""
	ON_SHELVE_TIME
	"""期别培训开始时间"""
	ISSUE_TRAINING_BEGIN_TIME
	"""期别报名开始时间"""
	ISSUE_SIGN_UP_BEGIN_TIME
	"""商品创建时间"""
	COMMODITY_CREATED_TIME
	"""最新编辑时间"""
	LAST_EDIT_TIME
	"""商品销售数"""
	SALE_TOTAL_NUMBER
	"""商品sku属性-年度"""
	SKU_PROPERTY_YEAR
	"""专题排序"""
	TRAINING_CHANNEL
}
type CommoditySaleChannelResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.CommoditySaleChannelResponse") {
	"""商品的销售渠道，0.网校 1.分销 2.专题 3.华医网"""
	saleChannel:Int
	"""销售渠道是否可用"""
	available:Boolean
	"""销售渠道下的购买渠道列表"""
	purchaseSubjectList:[CommoditySaleChannelPurchaseSubjectResponse]
}
"""后台商品返回值模型
	<AUTHOR>
	@date 2022/01/25
"""
type CommoditySkuBackstageResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.CommoditySkuBackstageResponse") {
	"""商品id"""
	commoditySkuId:String
	"""培训班商品基本信息"""
	commodityBasicData:CommodityBasicDataResponse
	"""商品属性信息"""
	skuProperty:CommoditySkuPropertyResponse
	"""上下架信息"""
	onShelve:OnShelveResponse
	"""商品所有渠道的配置信息"""
	commodityPurchaseChannelConfig:CommodityPurchaseChannelConfigResponse
	"""商品关联资源信息"""
	resource:ResourceResponse
	"""最后编辑时间"""
	commodityLastEditTime:DateTime
	"""商品创建时间"""
	commodityCreatTime:DateTime
	"""商品资源是否可用"""
	isResourceEnabled:Boolean
	"""专题"""
	trainingChannels:[CommodityTrainingChannelResponse]
	"""第三方平台类型"""
	tppTypeId:String
	"""管理系统平台"""
	externalTrainingPlatform:String
	"""学习监管系统"""
	learningSupervisionSystem:String
	"""课程供应商单位ID"""
	courseSupplierId:String
	"""第三方培训方案id"""
	thirdPartyTrainingSchemeId:String
	"""开通数"""
	saleTotalNumber:Long
	"""方案期数信息"""
	issueInfoResponse:SchemeIssueInfoResponse
	"""所属单位id"""
	unitId:String
	"""所属单位名称"""
	unitName:String
	"""商品拓展信息"""
	extInfo:CommodityExtInfoResponse
	"""商品销售渠道信息"""
	commoditySaleChannel:[CommoditySaleChannelResponse]
}
"""<AUTHOR> linq
	@date : 2024-12-10 13:38
	@description：培训班商品期别信息response
"""
type IssueCommoditySkuBackStageResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.IssueCommoditySkuBackStageResponse") {
	"""商品信息"""
	commoditySkuInfo:IssueSourceCommoditySkuInfoBackStageResponse
	"""期别资源信息"""
	issueResourceInfo:SchemeIssueResourceBackStageResponse
	"""剩余报名人数"""
	remainingRegisterNumber:Int!
}
"""地区响应"""
type RegionResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.RegionResponse") {
	"""省"""
	province:String
	"""市"""
	city:String
	"""区县"""
	county:String
	"""路径"""
	path:String
}
"""商品基本信息返回值
	<AUTHOR>
	@date 2022/1/25
"""
type CommodityBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityBasicDataResponse") {
	"""商品销售标题"""
	saleTitle:String
	"""商品价格"""
	price:BigDecimal
	"""商品封面图路径"""
	commodityPicturePath:String
}
"""<AUTHOR> linq
	@date : 2025-01-07 14:50
	@description：商品拓展信息返回值
"""
type CommodityExtInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityExtInfoResponse") {
	"""资源供应商信息"""
	resourceProviderResponse:CommodityResourceProviderResponse
}
"""商品渠道配置
	<AUTHOR>
	@date 2022/01/26
"""
type CommodityPurchaseChannelConfigResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityPurchaseChannelConfigResponse") {
	"""用户自主购买"""
	customerPurchase:PurchaseChannelConfigResponse
	"""集体缴费"""
	collectivePurchase:PurchaseChannelConfigResponse
	"""管理员导入"""
	administratorImport:PurchaseChannelConfigResponse
	"""集体报名个人缴费渠道"""
	collectiveSignUpPersonalPay:PurchaseChannelConfigResponse
}
"""<AUTHOR> linq
	@date : 2025-01-07 14:54
	@description：商品资源供应商信息
"""
type CommodityResourceProviderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityResourceProviderResponse") {
	"""商品资源供应商id"""
	id:String
	"""商品资源供应商名称"""
	name:String
}
type CommoditySaleChannelPurchaseSubjectResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommoditySaleChannelPurchaseSubjectResponse") {
	"""购买主体类型,  1.学员  2.集体报名"""
	subjectType:Int
	"""是否可见"""
	isShow:Boolean
}
"""商品sku属性返回值
	<AUTHOR>
	@date 2022/01/25
"""
type CommoditySkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommoditySkuPropertyResponse") {
	"""年度"""
	year:SkuPropertyResponse
	"""地区 - 省"""
	province:SkuPropertyResponse
	"""地区 - 市"""
	city:SkuPropertyResponse
	"""地区 - 县"""
	county:SkuPropertyResponse
	"""行业"""
	industry:SkuPropertyResponse
	"""科目类型"""
	subjectType:SkuPropertyResponse
	"""培训类别"""
	trainingCategory:SkuPropertyResponse
	"""培训专业"""
	trainingProfessional:SkuPropertyResponse
	"""技术等级"""
	technicalGrade:SkuPropertyResponse
	"""卫生行业-培训对象"""
	trainingObject:SkuPropertyResponse
	"""卫生行业-岗位类别"""
	positionCategory:SkuPropertyResponse
	"""工勤行业-技术等级"""
	jobLevel:SkuPropertyResponse
	"""工勤行业-工种"""
	jobCategory:SkuPropertyResponse
	"""年级"""
	grade:SkuPropertyResponse
	"""科目"""
	subject:SkuPropertyResponse
	"""学段"""
	learningPhase:SkuPropertyResponse
	"""学科"""
	discipline:SkuPropertyResponse
	"""黑龙江药师-证书类型"""
	certificatesType:SkuPropertyResponse
	"""黑龙江药师-执业类别"""
	practitionerCategory:SkuPropertyResponse
	"""资质类别"""
	qualificationCategory:SkuPropertyResponse
	"""培训形式"""
	trainingForm:SkuPropertyResponse
}
type CommodityTrainingChannelResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityTrainingChannelResponse") {
	"""专题id"""
	trainingChannelId:String
	"""专题名称"""
	trainingChannelName:String
	"""排序"""
	sort:Int
}
"""<AUTHOR> linq
	@date : 2024-12-10 13:40
	@description：期别原商品信息
"""
type IssueSourceCommoditySkuInfoBackStageResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.IssueSourceCommoditySkuInfoBackStageResponse") {
	"""商品SkuID"""
	commoditySkuId:String
	"""Sku属性"""
	skuPropertyResponse:CommoditySkuPropertyResponse
	"""商品所有渠道的配置信息"""
	commodityPurchaseChannelConfig:CommodityPurchaseChannelConfigResponse
}
"""商品上下架信息
	<AUTHOR>
	@date 2022/1/25
"""
type OnShelveResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.OnShelveResponse") {
	"""商品当前上架状态
		<br> 0:已下架 1：已上架
		@see CommoditySkuShelveStatus
	"""
	shelveStatus:Int
	"""最新上架时间"""
	lastOnShelveTime:DateTime
	"""最新下架时间"""
	offShelveTime:DateTime
	"""最新计划上架时间"""
	onShelvePlanTime:DateTime
	"""最新计划下架时间"""
	offShelvePlanTime:DateTime
	"""发布时间"""
	publishTime:DateTime
}
"""购买渠道配置
	<AUTHOR>
	@date 2022/01/26
"""
type PurchaseChannelConfigResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.PurchaseChannelConfigResponse") {
	"""是否开启可见"""
	couldSee:Boolean
	"""是否开启可购买"""
	couldBuy:Boolean
}
"""<AUTHOR> linq
	@date : 2024-11-08 19:35
	@description：方案期数信息
"""
type SchemeIssueInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.SchemeIssueInfoResponse") {
	"""期别数量"""
	issueCount:Int!
}
"""<AUTHOR> linq
	@date : 2024-12-10 13:51
	@description：方案期别资源信息
"""
type SchemeIssueResourceBackStageResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.SchemeIssueResourceBackStageResponse") {
	"""期别id"""
	issueId:String
	"""期别编号"""
	issueNum:String
	"""期别名称"""
	issueName:String
	"""期别报名开始时间"""
	issueSignUpBeginDate:DateTime
	"""期别报名结束时间"""
	issueSignUpEndDate:DateTime
	"""期别培训开始时间"""
	issueTrainingBeginDate:DateTime
	"""期别培训结束时间"""
	issueTrainingEndDate:DateTime
}
"""商品sku属性
	<AUTHOR>
	@date 2022/01/25
"""
type SkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.SkuPropertyResponse") {
	"""sku属性值id"""
	skuPropertyValueId:String
	"""sku属性值名称"""
	skuPropertyValueName:String
}
"""商品资源
	<AUTHOR>
	@date 2022/03/02
"""
interface ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.ResourceResponse") {
	"""资源类型
		<br> Scheme:方案 ,Issue:期数
		@see com.fjhb.domain.trade.api.commodity.consts.CommoditySaleResourceTypes
	"""
	resourceType:String
}
"""培训方案信息
	<AUTHOR>
	@date 2022/03/02
"""
type SchemeResourceResponse implements ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.SchemeResourceResponse") {
	"""学习方案ID"""
	schemeId:String
	"""方案名"""
	schemeName:String
	"""学时"""
	period:BigDecimal
	"""培训方案类型
		<p>
		chooseCourseLearning：选课学习
		autonomousCourseLearning：自主学习
		@see com.fjhb.domain.learningscheme.api.scheme.consts.SchemeType
	"""
	schemeType:String
	"""资源类型
		<br> Scheme:方案 ,Issue:期数
		@see com.fjhb.domain.trade.api.commodity.consts.CommoditySaleResourceTypes
	"""
	resourceType:String
}
"""排序参数
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum SortPolicy @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.SortPolicy") {
	"""正序"""
	ASC
	"""倒序"""
	DESC
}
type Accommodation @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.Accommodation") {
	"""是否安排住宿"""
	IsAccommodation:Boolean
	"""住宿方式
		0-无需住宿
		1-1单人住宿
		2-合住
	"""
	accommodationType:Int
}
"""<AUTHOR> linq
	@date : 2023-12-20 19:09
	@description：商品分销授权信息
"""
type CommodityAuthInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.CommodityAuthInfoResponse") {
	"""商品授权ID（产品分销授权ID）"""
	commodityAuthId:String
	"""商品授权分销商ID"""
	distributorId:String
	"""商品授权分销商 名称"""
	distributorName:String
	"""分销级别
		@see DistriButionLevel
	"""
	distributionLevel:Int
	"""上级分销商ID（仅二级分销商有该值，分销合同内的上级分销商ID）"""
	superiorDistributorId:String
	"""上级分销商 名称"""
	superiorDistributorName:String
	"""商品授权分销商ID路径（格式为 /供应商ID/一级分销商ID/二级分销商ID）"""
	distributorIdPath:String
	"""商品授权供应商ID"""
	supplierId:String
	"""商品授权供应商名称"""
	supplierName:String
	"""供应商对接业务员ID（仅订单创建时有该分销商+网校有业务员时才有值）"""
	salesmanId:String
	"""供应商对接业务员名称"""
	salesmanName:String
	"""分销商电话"""
	distributorPhone:String
	"""分销商单位统一社会信用代码"""
	distributorUnitCreditCode:String
	"""分销商类型 1-个人 2-企业"""
	distributorPartnerType:Int
	"""供应商类型 1-个人 2-企业"""
	supplierPartnerType:Int
}
"""订单商品sku属性信息
	<AUTHOR>
	@date 2022/01/26
"""
type CommoditySkuPropertyResponse1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.CommoditySkuPropertyResponse") {
	"""年度"""
	year:SkuPropertyResponse1
	"""地区 - 省"""
	province:SkuPropertyResponse1
	"""地区 - 市"""
	city:SkuPropertyResponse1
	"""地区 - 区县"""
	county:SkuPropertyResponse1
	"""行业"""
	industry:SkuPropertyResponse1
	"""科目类型"""
	subjectType:SkuPropertyResponse1
	"""培训类别"""
	trainingCategory:SkuPropertyResponse1
	"""培训专业"""
	trainingProfessional:SkuPropertyResponse1
	"""技术等级"""
	technicalGrade:SkuPropertyResponse1
	"""卫生行业-培训对象"""
	trainingObject:SkuPropertyResponse1
	"""卫生行业-岗位类别"""
	positionCategory:SkuPropertyResponse1
	"""工勤行业-技术等级"""
	jobLevel:SkuPropertyResponse1
	"""工勤行业-工种"""
	jobCategory:SkuPropertyResponse1
	"""年级"""
	grade:SkuPropertyResponse1
	"""科目"""
	subject:SkuPropertyResponse1
	"""学段"""
	learningPhase:SkuPropertyResponse1
	"""学科"""
	discipline:SkuPropertyResponse1
	"""黑龙江药师-证书类型"""
	certificatesType:SkuPropertyResponse1
	"""黑龙江药师-执业类别"""
	practitionerCategory:SkuPropertyResponse1
	"""资质类别-主项增项"""
	qualificationCategory:SkuPropertyResponse1
	"""培训形式 网授班,面授"""
	trainingWay:SkuPropertyResponse1
}
"""订单商品信息返回值
	<AUTHOR>
	@date 2022/1/26
"""
type CommoditySkuResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.CommoditySkuResponse") {
	"""商品id"""
	commoditySkuId:String
	"""商品Sku名称"""
	saleTitle:String
	"""商品封面图路径"""
	commodityPicturePath:String
	"""商品售价"""
	price:BigDecimal
	"""商品原始单价"""
	originalPrice:BigDecimal
	"""是否使用优惠价"""
	enableSpecialPrice:Boolean
	"""是否展示原价"""
	showPrice:Boolean
	"""商品sku 配置"""
	skuProperty:CommoditySkuPropertyResponse1
	"""商品关联资源"""
	resource:ResourceResponse
	"""第三方平台类型"""
	tppTypeId:String
	"""管理系统平台"""
	externalTrainingPlatform:String
	"""培训机构名称"""
	trainingInstitution:String
	"""期别信息"""
	issueInfo:IssueInfo1
}
"""优惠策略"""
type DiscountSchemeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.DiscountSchemeResponse") {
	"""优惠价"""
	specialPrice:BigDecimal
	"""优惠策略"""
	discountPolicyList:[DiscountPolicyModel]
	"""优惠类型
		1-单项优惠 2-聚合优惠
	"""
	discountType:Int
	"""是否启用"""
	hasEnabled:Boolean
}
"""发票申请信息
	<AUTHOR>
	@date 2022/03/17
"""
type InvoiceApplyInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.InvoiceApplyInfoResponse") {
	"""开票方式
		1: 线上开票
		2: 线下开票
		@see InvoiceMethods
	"""
	invoiceMethod:Int
	"""发票类型
		1：电子发票
		2：纸质发票
		@see InvoiceTypes
	"""
	invoiceType:Int
	"""发票种类
		1：普通发票
		2：增值税普通发票
		3：增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategory:Int
	"""发票抬头"""
	title:String
	"""发票抬头类型
		1：个人
		2：企业
		@see InvoiceTitleTypes
	"""
	titleType:Int
	"""纳税人识别号（统一社会信用代码）"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""联系邮箱"""
	email:String
	"""联系邮箱"""
	contactEmail:String
	"""联系电话"""
	contactPhone:String
	"""发票票面备注"""
	remark:String
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""配送方式
		0：无
		1：自取
		2：快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:DeliveryAddressResponse
	"""自取点信息"""
	takePoint:TakePointResponse
	"""发票申请时间"""
	appliedTime:DateTime
	"""关联发票id集合"""
	invoiceIdList:[String]
}
"""发票票面信息
	<AUTHOR>
	@date 2022/3/18
"""
type InvoiceFaceInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.InvoiceFaceInfoResponse") {
	"""发票抬头"""
	title:String
	"""发票抬头类型
		@see InvoiceTitleTypes
	"""
	titleType:Int
	"""购买方纳税人识别号"""
	taxpayerNo:String
	"""购买方地址"""
	address:String
	"""购买方电话号码"""
	phone:String
	"""购买方开户行名称"""
	bankName:String
	"""购买方银行账户"""
	account:String
	"""购买方电子邮箱"""
	email:String
	"""购买方营业执照"""
	businessLicensePath:String
	"""购买方开户许可"""
	accountOpeningLicensePath:String
	"""联系电子邮箱"""
	contactEmail:String
	"""联系电话"""
	contactPhone:String
	"""发票票面备注"""
	remark:String
}
type IssueInfo1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.IssueInfo") {
	"""期别id"""
	issueId:String
	"""期别名称"""
	issueName:String
	"""培训编号"""
	issueNum:String
	"""培训开始时间"""
	trainStartTime:DateTime
	"""培训结束时间"""
	trainEndTime:DateTime
	"""当前期别来源类型。下单：SUB_ORDER 换班：EXCHANGE_ORDER 换期:CHANGE_ISSUE"""
	sourceType:String
	"""期别来源类型id 1:子订单号：2：换货单号：3：旧的期别参训资格D"""
	sourceId:String
}
"""<AUTHOR> linq
	@date : 2023-12-25 14:16
	@description：归属信息
"""
type OwnerInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.OwnerInfoResponse") {
	"""所属服务商ID"""
	servicerId:String
	"""所属服务商名称"""
	servicerName:String
}
"""退货单/批次退货单的原订单支付信息
	<AUTHOR>
	@date 2022/03/18
"""
type PaymentInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.PaymentInfoResponse") {
	"""支付金额"""
	payAmount:BigDecimal
	"""支付流水号"""
	flowNo:String
	"""收款账号"""
	receiveAccountId:String
	"""付款单类型
		<p>
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
		@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
	"""
	paymentOrderType:Int
}
"""定价策略"""
type PricingPolicyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.PricingPolicyResponse") {
	"""定价策略id"""
	pricingPolicyId:String
	"""定价价格"""
	price:BigDecimal
	"""是否启用"""
	hasEnabled:Boolean
}
"""收款账号返回值
	<AUTHOR>
	@date 2022/01/26
"""
type ReceiveAccountResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.ReceiveAccountResponse") {
	"""收款账号id"""
	receiveAccountId:String
	"""收款账号类型（1:线上收款账号 2:线下收款账号）
		@see ReceiveAccountTypes
	"""
	receiveAccountType:Int
	"""支付渠道id"""
	payChannelId:String
	"""支付渠道名称"""
	payChannelName:String
	"""收款账户的单位id"""
	receiveAccountUnitId:String
}
"""退货单退款信息
	<AUTHOR>
	@date 2022/03/18
"""
type RefundInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.RefundInfoResponse") {
	"""退款单号"""
	refundOrderNo:String
	"""退款单类型（1：线上退款 2：线下退款）
		@see RefundOrderTypes
	"""
	refundOrderType:Int
	"""退款单状态（0：等待退款 1：退款中 2：已退款 3：退款失败）
		@see com.fjhb.domain.trade.api.payment.consts.RefundOrderStatus
	"""
	refundOrderStatus:Int
	"""退款单状态变更时间"""
	refundOrderStatusChangeTime:RefundOrderStatusChangeTimeResponse
	"""退款流水"""
	refundFlow:String
	"""退款金额"""
	refundAmount:BigDecimal
	"""退款失败原因"""
	refundFailReason:String
	"""已确认退款时间"""
	refundConfirmedTime:DateTime
}
type SalePathResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.SalePathResponse") {
	"""主键id,  当前销售路径_销售全路径"""
	id:String
	"""销售全路径"""
	fullPath:String
	"""当前销售路径"""
	currentPath:String
	"""当前销售路径末级code"""
	currentPathLastCode:String
	"""当前销售路径末级类型"""
	currentPathLastType:Int
	"""是否为全路径的最后一级
		@see CurrentSalePathType
	"""
	isLast:Boolean
}
type UserInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.UserInfoResponse") {
	"""用户ID"""
	userId:String
	"""用户名"""
	name:String
}
"""用户相关返回值
	<AUTHOR>
	@date 2022/01/26
"""
type UserResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.response.UserResponse") {
	"""用户ID"""
	userId:String
	"""用户所在地区"""
	userArea:RegionModel
	"""江苏工考 - 用户所在地区"""
	managementUnitRegionCode:RegionModel
	"""江苏工考 - 工种"""
	jobCategoryId:String
	"""江苏工考 - 工种等级"""
	professionalLevel:Int!
	"""江苏工考 - 证书技术工种名称"""
	jobCategoryName:String
}
"""@Description 配送渠道
	<AUTHOR>
	@Date 16:23 2022/4/21
"""
type OfflineInvoiceDeliveryChannelResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.deliverychannel.gateway.graphql.response.OfflineInvoiceDeliveryChannelResponse") {
	"""配送渠道编号，必填"""
	channelId:String
	"""配送方式，0/1，快递/自取"""
	shippingMethod:Int!
	"""渠道名称"""
	channelName:String
	"""配送地点"""
	address:String
	"""配送时间"""
	deliveryDate:String
	"""是否启用"""
	enable:Boolean!
	"""备注"""
	remark:String
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商ID"""
	servicerId:String
}
"""换货单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum ExchangeOrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.request.nested.ExchangeOrderSortField") {
	"""换货单申请时间"""
	APPLIED_TIME
}
"""换货单主题模型
	<AUTHOR>
	@date 2022/03/23
"""
type ExchangeOrderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.response.ExchangeOrderResponse") {
	"""换货单号"""
	exchangeOrderNo:String
	"""换货单基本信息"""
	basicData:ExchangeOrderBasicDataResponse
	"""换货单审批信息"""
	approvalInfo:ExchangeOrderApprovalInfoResponse
	"""原始商品信息"""
	originalCommodity:OriginalCommodityResponse
	"""换货商品信息"""
	exchangeCommodity:ExchangeCommodityResponse
	"""子订单发货商品分销授权信息（仅分销订单有值）"""
	commodityAuthInfo:CommodityAuthInfoResponse
	"""换货单关联子订单信息"""
	subOrderInfo:SubOrderInfoResponse
}
"""换货商品模型
	<AUTHOR>
	@date 2022/03/31
"""
type ExchangeCommodityResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.response.nested.ExchangeCommodityResponse") {
	"""商品数量"""
	quantity:BigDecimal
	"""商品信息"""
	commoditySku:CommoditySkuResponse
}
"""退货申请信息返回值
	<AUTHOR>
	@date 2022/03/24
"""
type ExchangeOrderApplyInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.response.nested.ExchangeOrderApplyInfoResponse") {
	"""申请人"""
	applyUser:UserResponse
	"""申请原因内容id"""
	reasonId:String
	"""申请原因内容"""
	reasonContent:String
	"""申请描述"""
	description:String
}
"""换货单审批信息
	<AUTHOR>
	@date 2022/03/18
"""
type ExchangeOrderApprovalInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.response.nested.ExchangeOrderApprovalInfoResponse") {
	"""审批状态
		<p>
		0: 未审批
		1: 已审批
		@see ExchangeApprovalReportStatus
	"""
	approveStatus:Int
	"""审批结果
		<p>
		-1：无
		0：拒绝
		1：同意
		@see ExchangeApprovalReportResults
	"""
	approveResult:Int
	"""审批人"""
	approveUser:UserModel
	"""审批意见"""
	approveComment:String
	"""审批时间"""
	approveTime:DateTime
}
"""换货单基本信息
	<AUTHOR>
	@date 2022/03/23
"""
type ExchangeOrderBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.response.nested.ExchangeOrderBasicDataResponse") {
	"""换货单状态
		<p>
		0: 申请换货
		1: 取消中
		2: 退货中
		3: 退货失败
		4: 申请发货
		5: 发货中
		6: 发货失败
		7: 换货完成
		8: 已关闭
		@see ExchangeOrderStatus
	"""
	status:Int
	"""换货单状态变更时间"""
	statusChangeTime:ExchangeOrderStatusChangeTimeResponse
	"""换货单申请信息"""
	applyInfo:ExchangeOrderApplyInfoResponse
	"""换货单退货失败信息"""
	exchangeReturnFailReason:String
	"""换货单发货失败信息"""
	exchangeDeliveryFailReason:String
	"""换货单关闭信息"""
	exchangeOrderCloseReason:ExchangeOrderCloseReasonResponse
}
"""换货单交易关闭信息
	<AUTHOR>
	@date 2022/2/14
"""
type ExchangeOrderCloseReasonResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.response.nested.ExchangeOrderCloseReasonResponse") {
	"""换货单关闭类型
		<p>
		1：买家关闭
		2：卖家关闭
		@see ExchangeOrderCloseTypes
	"""
	closeType:Int
	"""换货单取消人"""
	cancelUser:UserResponse
	"""换货单取消原因"""
	cancelReason:String
}
"""换货单状态变更时间
	<AUTHOR>
	@date 2022/03/24
"""
type ExchangeOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.response.nested.ExchangeOrderStatusChangeTimeResponse") {
	"""申请换货"""
	applied:DateTime
	"""取消中"""
	cancelling:DateTime
	"""退货中"""
	returning:DateTime
	"""退货失败"""
	returnFailed:DateTime
	"""申请发货"""
	deliveryApplied:DateTime
	"""发货中"""
	delivering:DateTime
	"""换货完成"""
	exchanged:DateTime
	"""已关闭"""
	closed:DateTime
}
"""主订单信息
	<AUTHOR>
	@date 2022/03/23
"""
type OrderInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.response.nested.OrderInfoResponse") {
	"""订单类型
		@see OrderTypes
	"""
	orderType:String
	"""订单号"""
	orderNo:String
	"""批次单号"""
	batchOrderNo:String
	"""买家"""
	buyer:UserResponse
	"""创建人"""
	creator:UserResponse
	"""销售渠道
		@see SaleChannel
	"""
	saleChannel:Int
	"""销售渠道id"""
	saleChannelId:String
	"""专题名称"""
	saleChannelName:String
}
"""原商品模型
	<AUTHOR>
	@date 2022/03/31
"""
type OriginalCommodityResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.response.nested.OriginalCommodityResponse") {
	"""商品数量"""
	quantity:BigDecimal
	"""商品信息"""
	commoditySku:CommoditySkuResponse
}
"""子订单信息
	<AUTHOR>
	@date 2022/03/23
"""
type SubOrderInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.exchangorder.gateway.graphql.response.nested.SubOrderInfoResponse") {
	"""子订单号"""
	subOrderNo:String
	"""子订单总额"""
	amount:BigDecimal
	"""订单信息"""
	orderInfo:OrderInfoResponse
	"""子订单是否使用优惠"""
	useDiscount:Boolean
	"""子订单价格来源 0.自营 1.分销"""
	discountType:Int
	"""子订单优惠来源ID"""
	discountSourceId:String
}
"""线下发票可用于排序的字段
	<AUTHOR> xmj
	@date : 2022/04/06
"""
enum OfflineInvoiceSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.request.nested.OfflineInvoiceSortField") {
	"""发票创建时间"""
	INVOICE_CREAT_TIME
}
"""线下发票操作记录
	<AUTHOR>
"""
type OfflineInvoiceOperationResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.OfflineInvoiceOperationResponse") {
	"""操作类型
		<br> 1:发票已开具 2:更新发票 3:发票已冻结 4:发票取消冻结 5:发票已作废
	"""
	operationType:Int
	"""操作人用户ID"""
	operatorUserId:String
	"""操作人用户名"""
	operatorUserName:String
	"""操作时间"""
	operateTime:DateTime
	"""操作附带信息"""
	operationMessage:String
}
"""线下发票网关模型
	<AUTHOR>
	@date 2022/3/18
"""
type OfflineInvoiceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.OfflineInvoiceResponse") {
	"""发票id"""
	offlineInvoiceId:String
	"""发票基本信息"""
	basicData:OfflineInvoiceBasicDataResponse
	"""发票关联订单信息"""
	associationInfo:OfflineInvoiceAssociationInfoResponse
	"""发票配送信息"""
	deliveryInfo:OfflineInvoiceDeliveryInfoResponse
	"""发票配送记录"""
	deliveryRecordList:[OfflineDeliveryRecord]
}
"""发票开票状态变更时间记录
	<AUTHOR>
	@date 2022/04/06
"""
type BillStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.nested.BillStatusChangeTimeResponse") {
	"""未开票"""
	unBill:DateTime
	"""已开票"""
	success:DateTime
}
"""配送地址信息
	<AUTHOR>
	@date 2022/04/06
"""
type DeliveryAddressResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.nested.DeliveryAddressResponse") {
	"""收件人"""
	consignee:String
	"""手机号"""
	phone:String
	"""所在物理地区"""
	region:String
	"""详细地址"""
	address:String
}
"""发票配送状态变更时间记录
	<AUTHOR>
	@date 2022/04/06
"""
type DeliveryStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.nested.DeliveryStatusChangeTimeResponse") {
	"""未就绪"""
	unReady:DateTime
	"""已就绪"""
	ready:DateTime
	"""已配送"""
	shipped:DateTime
	"""已自取"""
	taken:DateTime
}
"""快递信息
	<AUTHOR>
	@date 2022/04/06
"""
type ExpressResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.nested.ExpressResponse") {
	"""快递公司名称"""
	expressCompanyName:String
	"""快递单号"""
	expressNo:String
}
"""发票状态变更时间记录
	<AUTHOR>
	@date 2022/04/06
"""
type InvoiceStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.nested.InvoiceStatusChangeTimeResponse") {
	"""正常"""
	normal:DateTime
	"""作废"""
	invalid:DateTime
}
"""发票配送记录
	<AUTHOR>
	@date 2022/05/17
"""
type OfflineDeliveryRecord @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.nested.OfflineDeliveryRecord") {
	"""发票号"""
	invoiceNoList:[String]
	"""配送方式
		<p>
		0:无 1:自取 2：快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""自取结果"""
	takeResult:TakeResultResponse
	"""快递信息"""
	express:ExpressResponse
	"""配送时间"""
	deliveryTime:DateTime
}
"""发票关联订单信息
	<AUTHOR>
	@date 2022/3/18
"""
type OfflineInvoiceAssociationInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.nested.OfflineInvoiceAssociationInfoResponse") {
	"""关联订单类型 | 批次单、普通订单
		@see AssociationTypes
	"""
	associationType:Int
	"""订单号 | 批次单号"""
	associationId:String
	"""付款金额"""
	payAmount:BigDecimal
	"""买家信息"""
	buyer:UserModel
	"""收款账号ID"""
	receiveAccountId:String
	"""订单退货状态
		0:未退货 1：退货中 2：退货成功
	"""
	orderReturnStatus:Int
	"""销售渠道
		@see SaleChannel
	"""
	saleChannel:Int
	"""专题id"""
	saleChannelId:String
	"""专题名称"""
	saleChannelName:String
	"""订单发货商品分销信息（仅分销订单的发票有值）"""
	commodityAuthInfoSet:[CommodityAuthInfoResponse]
	"""第三方平台类型"""
	tppTypeId:String
}
"""发票基本信息
	<AUTHOR>
	@date 2022/3/18
"""
type OfflineInvoiceBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.nested.OfflineInvoiceBasicDataResponse") {
	"""发票类型
		@see InvoiceTypes
	"""
	invoiceType:Int
	"""发票种类
		@see InvoiceCategories
	"""
	invoiceCategory:Int
	"""发票状态
		@see InvoiceStatus
	"""
	invoiceStatus:Int
	"""发票状态变更时间记录"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeResponse
	"""发票开票状态
		@see InvoiceBillStatus
	"""
	billStatus:Int
	"""发票开票状态变更时间记录"""
	billStatusChangeTime:BillStatusChangeTimeResponse
	"""发票是否冻结"""
	freeze:Boolean
	"""冻结来源类型
		@see InvoiceFrozenSourceTypes
	"""
	freezeSourceType:Int
	"""冻结来源编号"""
	freezeSourceId:String
	"""发票票面信息"""
	invoiceFaceInfo:InvoiceFaceInfoResponse
	"""发票号集合"""
	invoiceNoList:[String]
	"""总金额（开票金额）"""
	amount:BigDecimal
}
"""线下发票配送信息
	<AUTHOR>
	@date 2022/04/06
"""
type OfflineInvoiceDeliveryInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.nested.OfflineInvoiceDeliveryInfoResponse") {
	"""配送状态
		<p>
		0:未就绪 1：已就绪 2：已自取 3：已配送
		@see OfflineDeliveryStatus
	"""
	deliveryStatus:Int
	"""配送状态变更时间记录"""
	deliveryStatusChangeTime:DeliveryStatusChangeTimeResponse
	"""配送方式
		<p>
		0:无 1:自取 2：快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:DeliveryAddressResponse
	"""自取点信息"""
	takePoint:TakePointResponse
	"""快递信息"""
	express:ExpressResponse
	"""自取信息"""
	takeResult:TakeResultResponse
}
"""自取点信息
	<AUTHOR>
	@date 2022/04/06
"""
type TakePointResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.nested.TakePointResponse") {
	"""领取地点"""
	pickupLocation:String
	"""领取时间"""
	pickupTime:String
	"""备注"""
	remark:String
}
"""取件信息
	<AUTHOR>
	@date 2022/04/06
"""
type TakeResultResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.offlineinvoice.gateway.graphql.response.nested.TakeResultResponse") {
	"""领取人"""
	takePerson:String
	"""手机号"""
	phone:String
}
"""发票可用于排序的字段
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum OnlineInvoiceSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.request.nested.OnlineInvoiceSortField") {
	"""发票创建时间"""
	INVOICE_CREAT_TIME
}
"""线上发票操作记录
	<AUTHOR>
"""
type OnlineInvoiceOperationResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.response.OnlineInvoiceOperationResponse") {
	"""操作类型
		<br> 1:发票已开具 2:更新发票 3:发票已冲红 4:发票开具失败 5:发票冲红失败
	"""
	operationType:Int
	"""操作人用户ID"""
	operatorUserId:String
	"""操作人用户名"""
	operatorUserName:String
	"""操作时间"""
	operateTime:DateTime
	"""操作附带信息"""
	operationMessage:String
}
"""发票网关模型
	<AUTHOR>
	@date 2022/3/18
"""
type OnlineInvoiceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.response.OnlineInvoiceResponse") {
	"""发票id"""
	invoiceId:String
	"""发票基本信息"""
	basicData:OnlineInvoiceBasicDataResponse
	"""发票关联订单信息"""
	associationInfoList:[OnlineInvoiceAssociationInfo]
	"""蓝票票据"""
	blueInvoiceItem:OnlineInvoiceItemResponse
	"""红票票据"""
	redInvoiceItem:OnlineInvoiceItemResponse
}
"""发票统计信息返回值
	<AUTHOR>
	@date 2022/01/26
"""
type OnlineInvoiceStatisticResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.response.OnlineInvoiceStatisticResponse") {
	"""发票开票总金额"""
	totalAmount:BigDecimal
	"""发票总税额"""
	totalTax:BigDecimal
}
"""发票开具状态变更时间
	<AUTHOR>
	@date 2022/03/22
"""
type BillStatusChangeTimeResponse1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.response.nested.BillStatusChangeTimeResponse") {
	"""未开具"""
	unBill:DateTime
	"""开票中"""
	billing:DateTime
	"""开票成功"""
	success:DateTime
	"""开票失败"""
	failure:DateTime
}
"""发票关联订单信息
	<AUTHOR>
	@date 2022/3/18
"""
type OnlineInvoiceAssociationInfo @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.response.nested.OnlineInvoiceAssociationInfo") {
	"""关联订单类型
		0:订单号
		1:批次单号
		@see AssociationTypes
	"""
	associationType:Int
	"""订单号 | 批次单号"""
	associationId:String
	"""订单付款金额"""
	payAmount:BigDecimal
	"""买家信息"""
	buyer:UserEntity
	"""收款账号"""
	receiveAccountId:String
	"""订单退货状态
		0:未退货 1：退货中 2：退货成功
	"""
	orderReturnStatus:Int
	"""销售渠道
		@see SaleChannel
	"""
	saleChannel:Int
	"""订单发货商品分销信息（仅分销订单的发票有值）"""
	commodityAuthInfoSet:[CommodityAuthInfoResponse]
	"""专题id"""
	saleChannelId:String
	"""专题名称"""
	saleChannelName:String
	"""第三方平台类型"""
	tppTypeId:String
}
"""发票基本信息
	<AUTHOR>
	@date 2022/3/18
"""
type OnlineInvoiceBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.response.nested.OnlineInvoiceBasicDataResponse") {
	"""是否为拆票后的发票"""
	spilt:Boolean
	"""发票类型
		1:电子发票
		2:纸质发票
		@see InvoiceTypes
	"""
	invoiceType:Int
	"""发票种类
		1:普通发票
		2:增值税普通发票
		3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategory:Int
	"""发票状态
		1:正常
		2:作废
		@see InvoiceStatus
	"""
	invoiceStatus:Int
	"""发票状态变更时间记录"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeResponse
	"""发票是否冻结"""
	freeze:Boolean
	"""冻结来源类型
		@see InvoiceFrozenSourceTypes
	"""
	freezeSourceType:Int
	"""冻结来源编号"""
	freezeSourceId:String
	"""蓝票票据开具状态
		0:未开具
		1:开票中
		2:开票成功
		3:开票失败
		@see InvoiceBillStatus
	"""
	blueInvoiceItemBillStatus:Int
	"""红票票据开具状态
		0:未开具
		1:开票中
		2:开票成功
		3:开票失败
		@see InvoiceBillStatus
	"""
	redInvoiceItemBillStatus:Int
	"""发票票面信息"""
	invoiceFaceInfo:InvoiceFaceInfoResponse
	"""纳税人编号"""
	taxpayerId:String
	"""开票总金额
		<br> 计算方式： 累加开票项目中的总价
	"""
	amount:BigDecimal
}
"""发票票据
	<AUTHOR>
	@date 2022/03/18
"""
type OnlineInvoiceItemResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.onlineinvoice.gateway.graphql.response.nested.OnlineInvoiceItemResponse") {
	"""票据id"""
	invoiceItemId:String
	"""开票流水号，由系统生成"""
	flowNo:String
	"""票据开具状态
		@see InvoiceBillStatus
	"""
	billStatus:Int
	"""票据开具状态变更时间"""
	billStatusChangeTime:BillStatusChangeTimeResponse1
	"""价税合计（开票金额）
		价税合计=合计金额(不含税)+合计税额
	"""
	totalAmount:BigDecimal
	"""不含税总金额"""
	totalExcludingTaxAmount:BigDecimal
	"""总税额"""
	totalTax:BigDecimal
	"""发票代码"""
	billCode:String
	"""发票号码"""
	billNo:String
	"""校验码"""
	checkCode:String
	"""票据存储文件路径"""
	filePath:String
	"""ofd票据存储文件路径"""
	ofdFilePath:String
	"""xml票据存储文件路径"""
	xmlFilePath:String
	"""开票失败信息"""
	failMessage:String
}
"""订单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum OrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.request.nested.OrderSortField") {
	"""订单创建时间"""
	ORDER_NORMAL_TIME
	"""订单交易完成时间"""
	ORDER_COMPLETED_TIME
}
"""买家有效商品返回值
	<AUTHOR>
	@date 2022/03/24
"""
type BuyerValidCommodityResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.BuyerValidCommodityResponse") {
	"""订单号"""
	orderNo:String
	"""子订单号"""
	subOrderNo:String
	"""订单信息"""
	order:OrderResponse
	"""商品信息"""
	commoditySku:CommoditySkuResponse
	"""商品所属订单购买渠道
		<br> 1:用户自主购买 2:集体缴费 3：管理员导入 4：集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelType:Int
	"""商品是否来自换货单"""
	formExchangeOrder:Boolean
	"""销售渠道
		@see SaleChannel
	"""
	saleChannel:Int
	"""销售渠道id"""
	saleChannelId:String
	"""专题名称"""
	saleChannelName:String
	"""分销商id"""
	distributorId:String
}
type IssueLogResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.IssueLogResponse") {
	"""子订单号"""
	subOrder:String
	"""换出商品"""
	originalCommodity:CommoditySkuResponse
	"""换入商品"""
	exchangeCommodity:CommoditySkuResponse
	"""创建时间"""
	createTime:DateTime
	"""操作人信息"""
	creator:UserInfoResponse
	"""换期类型 ：  0：换班换期   1： 班内换期"""
	exchangeType:Int
}
"""订单返回值
	<AUTHOR>
	@date 2022/1/25
"""
type OrderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.OrderResponse") {
	"""订单号"""
	orderNo:String
	"""基本信息"""
	basicData:OrderBasicDataResponse
	"""支付信息"""
	payInfo:PayInfoResponse
	"""子订单信息"""
	subOrderItems:[SubOrderResponse]
	"""买家信息"""
	buyer:UserResponse
	"""订单创建人"""
	creator:UserResponse
	"""订单是否已经申请发票"""
	isInvoiceApplied:Boolean
	"""销售渠道
		@see SaleChannel
	"""
	saleChannel:Int
	"""销售渠道id"""
	saleChannelId:String
	"""专题名称"""
	saleChannelName:String
	"""发票申请信息"""
	invoiceApplyInfo:InvoiceApplyInfoResponse
	"""购买来源，门户-GATEWAY、专题-TRAINING"""
	purchaseSourceType:String
	"""销售全路径"""
	salePathList:[SalePathResponse]
	"""推广门户标识"""
	portalIdentifier:String
	"""培训计划ID，例如补贴性培训平台和补贴管理系统对接"""
	policyTrainingSchemeIds:String
	"""申报单位统一信用代码"""
	declarationUnitCode:String
	"""结算状态
		@see com.fjhb.ms.trade.query.common.constants.SettlementStatusConstants
	"""
	settlementStatus:Int
	"""结算金额"""
	settlementAmount:BigDecimal
	"""结算时间"""
	settlementDate:DateTime
	"""兑付状态
		@see com.fjhb.ms.trade.query.common.constants.RedeemedStatusConstants
	"""
	redeemedStatus:Int
	"""兑付金额"""
	redeemedAmount:BigDecimal
	"""兑付时间"""
	redeemedDate:DateTime
	"""参训资格ID"""
	registrationId:String
}
"""订单统计信息返回值
	<AUTHOR>
	@date 2022/01/26
"""
type OrderStatisticResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.OrderStatisticResponse") {
	"""订单总数量"""
	totalOrderCount:Long
	"""订单总金额"""
	totalOrderAmount:BigDecimal
	"""订单培训方案商品总学时数"""
	totalPeriod:BigDecimal
}
"""订单基本信息返回值
	<AUTHOR>
	@date 2022/01/26
"""
type OrderBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.nested.OrderBasicDataResponse") {
	"""订单类型
		<br> 1：常规订单 2：批次关联订单
		@see com.fjhb.domain.trade.api.order.consts.OrderTypes
	"""
	orderType:Int
	"""关联批次单号"""
	batchOrderNo:String
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3：管理员导入 4：集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelType:Int
	"""终端
		<br> Web:Web端 H5:H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTerminalCodes
	"""
	terminalCode:String
	"""订单总金额"""
	amount:BigDecimal
	"""订单状态
		0:未确认，批次单订单初始状态
		1:正常
		2:交易完成
		3:交易关闭
		<p>
		ui订单状态：数据微服务订单状态
		等待付款：订单状态正常&支付状态未支付
		支付中：订单状态正常&支付状态支付中
		开通中：订单状态正常&支付状态已支付
		交易成功：订单状态交易成功
		交易关闭：订单状态交易关闭
		@see OrderStatus
	"""
	orderStatus:Int
	"""订单支付状态
		0:未支付
		1:支付中
		2:已支付
		@see OrderPaymentStatus
	"""
	orderPaymentStatus:Int
	"""订单发货状态
		0：未发货
		1：发货中
		2：已发货
		@see OrderDeliveryStatus
	"""
	orderDeliveryStatus:Int
	"""订单状态变更时间"""
	orderStatusChangeTime:OrderStatusChangeTimeResponse
	"""订单支付状态变更时间"""
	orderPaymentStatusChangeTime:OrderPaymentStatusChangeTimeResponse
	"""自动关闭时间"""
	autoCloseTime:DateTime
	"""交易关闭原因"""
	closeReason:OrderCloseReasonResponse
	"""单位id"""
	unitId:String
}
"""订单交易关闭返回值
	<AUTHOR>
	@date 2022/01/26
"""
type OrderCloseReasonResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.nested.OrderCloseReasonResponse") {
	"""交易关闭类型
		<br> 1:买家取消 2:卖家取消 3：超时取消 4：批次关联取消
		@see com.fjhb.domain.trade.api.order.consts.OrderClosedTypes
	"""
	closedType:Int
	"""交易关闭原因ID"""
	reasonId:String
	"""交易关闭原因说明"""
	reason:String
}
"""订单支付状态变更时间
	<AUTHOR>
	@date 2022/01/26
"""
type OrderPaymentStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.nested.OrderPaymentStatusChangeTimeResponse") {
	"""支付中"""
	paying:DateTime
	"""已支付"""
	paid:DateTime
}
"""订单状态变更时间
	<AUTHOR>
	@date 2022/01/26
"""
type OrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.nested.OrderStatusChangeTimeResponse") {
	"""订单处于正常状态的时间（订单创建时间）"""
	normal:DateTime
	"""订单交易完成时间"""
	completed:DateTime
	"""订单交易关闭时间"""
	closed:DateTime
}
"""订单支付信息
	<AUTHOR>
	@date 2022/1/26
"""
type PayInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.nested.PayInfoResponse") {
	"""付款单号"""
	paymentOrderNo:String
	"""付款单类型
		<p>
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
		@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderTypes
	"""
	paymentOrderType:Int
	"""付款单状态
		0:待付款
		1:付款中
		2:已支付
		3:已取消
		@see com.fjhb.domain.trade.api.payment.consts.PaymentOrderStatus
	"""
	paymentOrderStatus:Int
	"""支付流水号"""
	flowNo:String
	"""支付金额"""
	payAmount:BigDecimal
	"""支付金额类型
		<br> 0:人民币现金支付（线下人民币现金、微信支付、支付宝） 1:华博电子钱包虚拟币 2:消费券(培训券)
		@see CurrencyType
	"""
	currencyType:Int
	"""收款账号"""
	receiveAccount:ReceiveAccountResponse
	"""汇款凭证url"""
	paymentVoucherList:[PaymentVoucherResponse]
	"""汇款凭证确认人"""
	paymentVoucherConfirmUser:UserModel
}
"""付款凭证
	<AUTHOR>
	@date 2022/03/07
"""
type PaymentVoucherResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.nested.PaymentVoucherResponse") {
	"""凭证ID"""
	id:String
	"""凭证文件路径"""
	path:String
	"""创建人"""
	createUserId:UserModel
	"""创建时间"""
	createdTime:DateTime
}
"""订单商品sku属性
	<AUTHOR>
	@date 2022/1/25
"""
type SkuPropertyResponse1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.nested.SkuPropertyResponse") {
	"""sku属性值id"""
	skuPropertyValueId:String
	"""sku属性值名"""
	skuPropertyValueName:String
	"""sku属性值展示名"""
	skuPropertyValueShowName:String
}
"""子订单发货状态变更时间
	<AUTHOR>
	@date 2022/01/26
"""
type SubOrderDeliveryStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.nested.SubOrderDeliveryStatusChangeTimeResponse") {
	"""等待发货"""
	waiting:DateTime
	"""发货中"""
	delivering:DateTime
	"""发货成功"""
	successDelivered:DateTime
	"""发货失败"""
	failDelivered:DateTime
}
"""子订单返回值
	<AUTHOR>
	@date 2022/1/26
"""
type SubOrderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.order.gateway.graphql.response.nested.SubOrderResponse") {
	"""子订单号"""
	subOrderNo:String
	"""订单号"""
	orderNo:String
	"""子订单发货状态
		0:等待发货 100:发货中 200:发货成功 400:发货失败
		@see DeliveryOrderSkuStatus
	"""
	deliveryStatus:Int
	"""子订单退货情况
		0: 未退货,
		1: 已部分退货,
		2: 已全部退货
	"""
	returnSchedule:Int
	"""子订单退款情况
		0: 未退款,
		1: 已部分退款,
		2: 已全部退款
	"""
	refundSchedule:Int
	"""子订单换货状态
		<p>
		0:未换货
		1:换货申请中
		2:换货中
		3:已换货
		@see com.fjhb.domain.trade.api.order.consts.SubOrderExchangeStatus
	"""
	exchangeStatus:Int
	"""子订单最新退货单号"""
	returnOrderNo:String
	"""子订单退货状态
		0：未退货
		1：退货申请中
		2：退货中
		3：退货成功
		4：退款中
		5：退款成功
		@see SubOrderReturnStatus
	"""
	returnStatus:Int
	"""子订单发货状态变更时间
		0: 等待发货
		100: 发货中
		200: 发货成功
		400: 发货失败
		<br> key值 {@link DeliveryOrderSkuStatus}
	"""
	deliveryStatusChangeTime:SubOrderDeliveryStatusChangeTimeResponse
	"""发货失败信息"""
	deliverFailMessage:String
	"""子订单商品数量"""
	quantity:BigDecimal
	"""子订单剩余商品数量"""
	leftQuantity:BigDecimal
	"""商品单价"""
	price:BigDecimal
	"""子订单总金额"""
	amount:BigDecimal
	"""子订单剩余金额"""
	leftAmount:BigDecimal
	"""子订单优惠价（优惠的减免额度）"""
	specialPrice:BigDecimal
	"""商品分销授权信息（仅分销订单有值）"""
	commodityAuthInfo:CommodityAuthInfoResponse
	"""优惠类型
		@see DiscountType
	"""
	discountType:Int
	"""优惠来源ID | 优惠申请单ID"""
	discountSourceId:String
	"""是否使用优惠"""
	useDiscount:Boolean
	"""发货商品信息"""
	deliveryCommoditySku:CommoditySkuResponse
	"""当前商品信息"""
	currentCommoditySku:CommoditySkuResponse
	"""当前商品来源类型
		@see CommoditySkuSourceType
		子订单发货: 0
		换货单换货：1
	"""
	currentCommoditySourceType:Int
	"""当前商品来源ID
		<p>
		如果来源类型是子订单，那么来源ID是子订单号
		如果来源类型是换货单，那么来源ID是换货单
	"""
	currentCommoditySourceId:String
	"""最终成交单价"""
	finalPrice:BigDecimal
	"""优惠方案"""
	discountScheme:DiscountSchemeResponse
	"""定价方案"""
	pricingPolicy:PricingPolicyResponse
	"""住宿信息"""
	accommodation:Accommodation
	"""是否换期"""
	isExchangeIssue:Boolean
}
"""退货单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum ReturnOrderSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.request.nested.ReturnOrderSortField") {
	"""退货单申请时间"""
	APPLIED_TIME
}
"""退货单网关模型"""
type ReturnOrderResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.ReturnOrderResponse") {
	"""退货单号"""
	returnOrderNo:String
	"""退货单基本信息"""
	basicData:ReturnOrderBasicDataResponse
	"""退货单是否需要审批"""
	needApprove:Boolean
	"""退货单审批信息"""
	approvalInfo:ReturnApprovalInfoResponse
	"""退款确认人"""
	confirmUser:UserResponse
	"""退货单关联退款单信息"""
	refundInfo:RefundInfoResponse
	"""退货商品信息"""
	returnCommodity:ReturnCommodityResponse
	"""退款商品信息"""
	refundCommodity:RefundCommodityResponse
	"""退货子订单信息"""
	subOrderInfo:SubOrderInfoResponse1
	"""来源批次退货单信息"""
	batchReturnOrder:BatchReturnOrderResponse
	"""退货商品分销信息（仅分销订单的退货单有值）"""
	commodityAuthInfo:CommodityAuthInfoResponse
	"""归属信息"""
	ownerInfo:OwnerInfoResponse
	"""是否需要人工确认退款"""
	needConfirmRefund:Boolean
}
"""退货单统计信息返回值
	<AUTHOR>
	@date 2022/01/26
"""
type ReturnOrderStatisticResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.ReturnOrderStatisticResponse") {
	"""退货单总数"""
	totalReturnOrderCount:Long
	"""退货单退款总额"""
	totalRefundAmount:BigDecimal
}
"""功能描述：退货原因信息
	@Author： wtl
	@Date： 2022/4/1 10:45
"""
type ReturnReasonInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.ReturnReasonInfoResponse") {
	"""退货原因id"""
	reasonId:String
	"""退货原因内容"""
	reasonContent:String
}
"""退货单关联订单信息
	<AUTHOR>
	@date 2022/3/23
"""
type OrderInfoResponse1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.OrderInfoResponse") {
	"""订单号"""
	orderNo:String
	"""订单类型（1：常规订单 2：批次关联订单）
		@see com.fjhb.domain.trade.api.order.consts.OrderTypes
	"""
	orderType:Int
	"""关联批次单号"""
	batchOrderNo:String
	"""购买渠道（1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道）
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelType:Int
	"""终端（Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统）
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTerminalCodes
	"""
	terminalCode:String
	"""订单支付信息"""
	orderPaymentInfo:PaymentInfoResponse
	"""买家信息"""
	buyer:UserResponse
	"""创建人"""
	creator:UserResponse
	"""销售渠道
		@see SaleChannel
	"""
	saleChannel:Int
	"""专题id"""
	saleChannelId:String
	"""专题名称"""
	saleChannelName:String
	"""培训计划ID，例如补贴性培训平台和补贴管理系统对接"""
	policyTrainingSchemeIds:String
	"""申报单位统一信用代码"""
	declarationUnitCode:String
}
"""退款商品信息
	<AUTHOR>
	@date 2022/03/28
"""
type RefundCommodityResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.RefundCommodityResponse") {
	"""商品数量"""
	quantity:BigDecimal
	"""商品信息"""
	commoditySku:CommoditySkuResponse
}
"""退款单状态变更时间
	<AUTHOR>
	@date 2022/03/23
"""
type RefundOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.RefundOrderStatusChangeTimeResponse") {
	"""等待退款"""
	waiting:DateTime
	"""退款中"""
	refunding:DateTime
	"""已退款"""
	refunded:DateTime
	"""退款失败"""
	failed:DateTime
}
"""退货审批信息
	<AUTHOR>
	@date 2022/03/18
"""
type ReturnApprovalInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnApprovalInfoResponse") {
	"""审批状态（0：未审批 1：已审批）
		@see ReturnApprovalReportStatus
	"""
	approveStatus:Int
	"""审批结果（-1：无 0：拒绝 1：同意）
		@see ReturnApprovalReportResults
	"""
	approveResult:Int
	"""审批人"""
	approveUser:UserResponse
	"""审批意见"""
	approveComment:String
	"""审批时间"""
	approveTime:DateTime
	"""审批取消时间"""
	cancelApproveTime:DateTime
}
"""退货单关闭信息
	<AUTHOR>
	@date 2022年3月29日 15:48:33
"""
type ReturnCloseReasonResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnCloseReasonResponse") {
	"""退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
		@see ReturnOrderCloseTypes
	"""
	closeType:Int
	"""退货单取消人"""
	cancelUser:UserModel
	"""取消原因"""
	cancelReason:String
}
"""退货商品信息
	<AUTHOR>
	@date 2022/03/28
"""
type ReturnCommodityResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnCommodityResponse") {
	"""商品数量"""
	quantity:BigDecimal
	"""商品信息"""
	commoditySku:CommoditySkuResponse
}
"""退货申请信息返回值
	<AUTHOR>
	@date 2022/03/24
"""
type ReturnOrderApplyInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderApplyInfoResponse") {
	"""申请人"""
	applyUser:UserResponse
	"""申请原因内容id"""
	reasonId:String
	"""申请原因内容"""
	reasonContent:String
	"""申请描述"""
	description:String
}
"""退货单基本信息
	<AUTHOR>
	@date 2022/3/18
"""
type ReturnOrderBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderBasicDataResponse") {
	"""退货单类型
		1-仅退货
		2-仅退款
		3-退货并退款
		4-部分退货
		5-部分退款
		6-部分退货并部分退款
		7-部分退货并全额退款
		8-全部退货并部分退款
	"""
	returnOrderType:Int
	"""退款总额"""
	refundAmount:BigDecimal
	"""退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
		@see ReturnOrderStatus
	"""
	returnOrderStatus:Int
	"""退货单状态变更时间"""
	returnOrderStatusChangeTime:ReturnOrderStatusChangeTimeResponse
	"""退货单申请信息"""
	applyInfo:ReturnOrderApplyInfoResponse
	"""退货单退货失败信息"""
	returnFailReason:String
	"""退货单关闭信息"""
	returnCloseReason:ReturnCloseReasonResponse
	"""退货单申请来源类型
		SUB_ORDER
		BATCH_RETURN_ORDER
		@see ReturnOrderApplySourceTypes
	"""
	applySourceType:String
	"""退货单申请来源id
		当来源类型为子订单时,该申请来源id为子订单号,为批次退货单申请来源的,该申请来源id为批次退货单号
	"""
	applySourceId:String
	"""销售渠道
		@see SaleChannel
	"""
	saleChannel:Int
	"""专题id"""
	saleChannelId:String
	"""专题名称"""
	saleChannelName:String
}
"""退货单状态变更时间
	<AUTHOR>
	@date 2022/03/23
"""
type ReturnOrderStatusChangeTimeResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.ReturnOrderStatusChangeTimeResponse") {
	"""申请退货时间"""
	applied:DateTime
	"""申请退货取消处理中时间"""
	cancelApplying:DateTime
	"""退货处理中时间"""
	returning:DateTime
	"""退货失败时间"""
	returnFailed:DateTime
	"""正在申请退款时间"""
	refundApplying:DateTime
	"""已申请退款时间"""
	refundApplied:DateTime
	"""退款处理中时间"""
	refunding:DateTime
	"""退款失败"""
	refundFailed:DateTime
	"""退货完成时间"""
	returned:DateTime
	"""退款完成时间"""
	refunded:DateTime
	"""退货退款完成时间"""
	returnedAndRefunded:DateTime
	"""退货单完成时间"""
	returnCompleted:DateTime
	"""已关闭时间"""
	closed:DateTime
}
"""退货子订单信息
	<AUTHOR>
	@date 2022/3/18
"""
type SubOrderInfoResponse1 @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.returnorder.gateway.graphql.response.nested.SubOrderInfoResponse") {
	"""子订单号"""
	subOrderNo:String
	"""子订单有换货"""
	exchanged:Boolean
	"""主订单信息"""
	orderInfo:OrderInfoResponse1
	"""子订单商品数量"""
	quantity:BigDecimal
	"""子订单优惠来源ID"""
	discountSourceId:String
	"""子订单优惠类型
		@see DiscountType
	"""
	discountType:Int
	"""子订单是否使用优惠"""
	useDiscount:Boolean
	"""销售渠道"""
	saleChannel:Int
	"""优惠方案"""
	discountScheme:DiscountSchemeResponse
	"""定价方案"""
	pricingPolicy:PricingPolicyResponse
	salePathList:[SalePathResponse]
	"""是否换期"""
	isExchangeIssue:Boolean
	"""子订单总金额"""
	amount:BigDecimal
	"""子订单最终成交单价"""
	finalPrice:BigDecimal
}
"""购买渠道信息"""
type PurchaseChannelResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.PurchaseChannelResponse") {
	"""购买渠道id"""
	id:String
	"""平台id"""
	platformId:String
	"""平台版本id"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位id"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""购买渠道类型 1-自主购买 2-集体缴费 3-管理员导入 4-集体报名个人缴费"""
	channelType:Int
	"""购买渠道名称"""
	name:String
	"""购买渠道状态"""
	status:Int
	"""终端列表"""
	terminalList:[TerminalResponse]
	"""发票配置"""
	invoiceConfig:InvoiceConfigResponse
	"""创建时间"""
	createdTime:DateTime
}
"""收款账户配置"""
type ReceiveAccountConfigResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.ReceiveAccountConfigResponse") {
	"""收款账号id"""
	id:String
	"""收款账户类型
		<p>
		1：线上收款帐号
		2：线下收款帐号
	"""
	accountType:Int
	"""支付渠道id
		<p>
		TRAINING_VOUCHER：培训券，对接众智汇云培训券
		TRAINING_VOUCHER_CHECK_PAY：培训券验证支付，对接众智汇云培训券
		ALIPAY：支付宝
		WXPAY：微信支付
		HAOBAO_PAY：号百支付
		QM：快钱支付
		TL：通联支付
		ZERO_PAY：0元订单支付
		WXPAY_V3：微信支付
	"""
	paymentChannelId:String
	"""账号"""
	accountNo:String
	"""收款账号名称"""
	name:String
	"""所属商户名称"""
	merchantName:String
	"""所属商户电话"""
	merchantPhone:String
	"""收款帐号对应支付渠道的密钥数据"""
	encryptionKeyData:EncryptionKeyDataResponse
	"""收款帐号状态
		0:停用
		1:可用
	"""
	status:Int
	"""创建人ID"""
	createUserId:String
	"""创建时间"""
	createdTime:DateTime
	"""退款方式，1-线上退款，2-线下退款"""
	returnType:Int
	"""纳税人id"""
	taxPayerId:String
	"""主体类别
		单位：4；服务商：5
	"""
	subjectType:Int!
	"""主体id"""
	subjectId:String
	"""主体名称"""
	subjectName:String
	"""付款扫码引导语"""
	qrScanPrompt:String
}
"""纳税人信息"""
type TaxpayerResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.TaxpayerResponse") {
	id:String
	"""纳税人名称"""
	name:String
	"""纳税人识别号"""
	taxpayerNo:String
	"""发票地址"""
	address:String
	"""电话"""
	phone:String
	"""银行开户行"""
	bankName:String
	"""银行账号"""
	bankAccount:String
	"""开票人"""
	issuer:String
	"""收款人"""
	payee:String
	"""复核人"""
	reviewer:String
}
"""支付宝 密钥数据
	<AUTHOR> create 2021/3/9 15:24
"""
type AlipayEncryptionKeyDataResponse implements EncryptionKeyDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.AlipayEncryptionKeyDataResponse") {
	"""支付宝的Appid"""
	appId:String
	"""支付宝合作者身份ID(2088*)"""
	partner:String
	"""支付宝的应用RSA私钥"""
	privateKey:String
	"""支付宝的RSA公钥"""
	publicKey:String
	"""秘钥类型
		Alipay
		WechatPay
		OfflinePay
		CIBPay
		CCB_PAY
		SWIFT_PASS_PAY
		@see EncryptionKeyType
	"""
	encryptionKeyType:String
}
"""<AUTHOR> linq
	@date : 2024-02-20 16:50
	@description：建设银行支付 密钥数据
"""
type CCBPayEncryptionKeyDataResponse implements EncryptionKeyDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.CCBPayEncryptionKeyDataResponse") {
	"""商户柜台代码"""
	POS_ID:String
	"""分行代码"""
	BRANCH_ID:String
	"""建行网银支付接口的公钥"""
	PUBLIC_KEY:String
	"""建行的操作员账号不能为空"""
	OPERATOR:String
	"""建行操作员的登陆密码"""
	PASSWORD:String
	"""是否使用防钓鱼,如果1表示使用防钓鱼接口,其他则不使用"""
	PHISHING:String
	"""小程序/公众号的 APPID 当前调起支付的小程序/公众号 APPID"""
	SUB_APP_ID:String
	"""文件证书路径"""
	CERT_FILE_PATH:String
	"""文件证书密码"""
	CERT_PASS_WORD:String
	"""秘钥类型
		Alipay
		WechatPay
		OfflinePay
		CIBPay
		CCB_PAY
		SWIFT_PASS_PAY
		@see EncryptionKeyType
	"""
	encryptionKeyType:String
}
"""<AUTHOR> linq
	@date : 2023-08-07 15:06
	@description：兴业银行支付 密钥数据
"""
type CIBPayEncryptionKeyDataResponse implements EncryptionKeyDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.CIBPayEncryptionKeyDataResponse") {
	"""终端编号(兴业银行提供)"""
	terminalId:String
	"""应用ID，为KY字母开头的字符串，区分大小写"""
	appId:String
	"""请求报文签名私钥(SM2签名私钥)"""
	requestPrivateKey:String
	"""响应报文验签公钥"""
	responsePublicKey:String
	"""请求字段加密密钥"""
	requestParamEncryptKey:String
	"""wx公众账号或小程序ID"""
	subAppid:String
	"""秘钥类型
		Alipay
		WechatPay
		OfflinePay
		CIBPay
		CCB_PAY
		SWIFT_PASS_PAY
		@see EncryptionKeyType
	"""
	encryptionKeyType:String
}
interface EncryptionKeyDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.EncryptionKeyDataResponse") {
	"""秘钥类型
		Alipay
		WechatPay
		OfflinePay
		CIBPay
		CCB_PAY
		SWIFT_PASS_PAY
		@see EncryptionKeyType
	"""
	encryptionKeyType:String
}
"""发票自动开票配置"""
type InvoiceAutoBillPolicyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.InvoiceAutoBillPolicyResponse") {
	"""配置ID"""
	id:String
	"""平台id"""
	platformId:String
	"""平台版本id"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位id"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""是否自动开票"""
	autoBill:Boolean
	"""间隔小时|自生成发票日期算起多少小时后自动开票，默认7*24"""
	intervalHours:Int
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
"""允许开具发票种类配置"""
type InvoiceCategoryConfigResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.InvoiceCategoryConfigResponse") {
	"""允许开具发票种类 1-普通发票 2-增值税普通发票 3-增值税专用发票"""
	invoiceCategory:Int
	"""开票方式 1-线上开票 2-线下"""
	invoiceMethod:Int
	"""发票抬头  1-个人 2-企业"""
	invoiceTitleList:[Int]
}
"""发票配置"""
type InvoiceConfigResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.InvoiceConfigResponse") {
	"""发票配置编号"""
	configId:String
	"""开放发票类型 0-不开放 1-自主选择 2-强制提供"""
	openInvoiceType:Int
	"""是否允许索取发票"""
	allowAskFor:Boolean
	"""允许开具发票种类配置"""
	allowInvoiceCategoryList:[InvoiceCategoryConfigResponse]
	"""索取发票年度类型 1-当年度 2-下一个年度"""
	askForInvoiceYearType:Int
	"""索取发票截止日期"""
	askForInvoiceDeadline:String
	"""开票方式 1-线上开票 2-线下"""
	invoiceMethod:Int
	"""发票抬头  1-个人 2-企业"""
	invoiceTitleList:[Int]
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
"""<AUTHOR> linq
	@date : 2025-03-25 10:08
	@description :新大陆支付 密钥数据
"""
type NewLandEncryptionKeyDataResponse implements EncryptionKeyDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.NewLandEncryptionKeyDataResponse") {
	"""代理商发起交易必填，签名时使用代理商密钥参与签名"""
	proxyNo:String
	"""门店号，门店收款必填"""
	storeNo:String
	"""密钥"""
	privateKey:String
	"""秘钥类型
		Alipay
		WechatPay
		OfflinePay
		CIBPay
		CCB_PAY
		SWIFT_PASS_PAY
		@see EncryptionKeyType
	"""
	encryptionKeyType:String
}
"""线下收款账户信息"""
type OfflineEncryptionKeyDataResponse implements EncryptionKeyDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.OfflineEncryptionKeyDataResponse") {
	"""企业名称（开户户名）"""
	merchantName:String
	"""开户银行"""
	depositBank:String
	"""柜台号"""
	counterNumber:String
	"""秘钥类型
		Alipay
		WechatPay
		OfflinePay
		CIBPay
		CCB_PAY
		SWIFT_PASS_PAY
		@see EncryptionKeyType
	"""
	encryptionKeyType:String
}
"""<AUTHOR> linq
	@date : 2024-04-24 09:19
	@description : 威富通支付 密钥数据
"""
type SwiftPassPayEncryptionKeyDataResponse implements EncryptionKeyDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.SwiftPassPayEncryptionKeyDataResponse") {
	"""商户私钥"""
	mchPrivateKey:String
	"""平台公钥"""
	platPublicKey:String
	"""秘钥类型
		Alipay
		WechatPay
		OfflinePay
		CIBPay
		CCB_PAY
		SWIFT_PASS_PAY
		@see EncryptionKeyType
	"""
	encryptionKeyType:String
}
"""终端信息"""
type TerminalResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.TerminalResponse") {
	"""终端代码"""
	terminalCode:String
	"""是否关闭"""
	isClosed:Boolean
	"""收款账号编号列表"""
	receiveAccountIdList:[String]
}
"""微信支付 密钥数据
	<AUTHOR> create 2021/3/9 15:24
"""
type WechatPayEncryptionKeyDataResponse implements EncryptionKeyDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradeconfig.gateway.graphql.response.nested.WechatPayEncryptionKeyDataResponse") {
	"""Appid"""
	appId:String
	"""商户秘钥"""
	merchantKey:String
	"""私钥文件名"""
	privateKeyFileName:String
	"""私钥地址"""
	privateKeyPath:String
	"""私钥密码"""
	privateKeyPWD:String
	"""秘钥类型
		Alipay
		WechatPay
		OfflinePay
		CIBPay
		CCB_PAY
		SWIFT_PASS_PAY
		@see EncryptionKeyType
	"""
	encryptionKeyType:String
}
"""商品开通统计可用于排序的字段
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum CommodityOpenReportSortField @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.request.nested.CommodityOpenReportSortField") {
	"""商品订单交易成功总数"""
	TRADE_SUCCESS_COUNT
}
"""商品开通统计信息
	<AUTHOR>
	@date 2022/05/10
"""
type CommodityOpenReportFormResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.CommodityOpenReportFormResponse") {
	"""商品ID"""
	commoditySkuId:String
	"""学习方案ID"""
	schemeId:String
	"""学习方案名称"""
	schemeName:String
	"""学习方案学时"""
	period:String
	"""合计数据"""
	summaryInfo:SubOrderStatisticDto
	"""各渠道统计信息"""
	purchaseChannelStatisticInfoList:[PurchaseChannelStatisticDto]
}
"""地区开通统计信息
	<AUTHOR>
	@date 2022/05/10
"""
type RegionOpenReportFormResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.RegionOpenReportFormResponse") {
	"""地区id"""
	regionId:String
	"""合计数据"""
	summaryInfo:SubOrderStatisticDto
	"""各渠道统计信息"""
	purchaseChannelStatisticInfoList:[PurchaseChannelStatisticDto]
}
"""统计报表合计数据
	<AUTHOR>
	@date 2022/05/10
"""
type ReportSummaryResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.ReportSummaryResponse") {
	"""净成交总额
		<p> 交易成功订单总金额 - 已退款订单总额
	"""
	totalNetAmount:BigDecimal
	"""交易次数合计数据"""
	tradeCountSummaryInfo:SubOrderStatisticDto
	"""各渠道统计信息"""
	purchaseChannelStatisticInfoList:[PurchaseChannelStatisticDto]
}
"""交易时间段统计直方图
	<AUTHOR>
"""
type TradeStatisticDateHistogramResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.TradeStatisticDateHistogramResponse") {
	"""直方图数据"""
	histogramData:[TradeStatisticDateHistogramBucketResponse]
}
"""支付方式统计信息
	<AUTHOR>
	@date 2022/05/10
"""
type PaymentTypeStatisticDto @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.nested.PaymentTypeStatisticDto") {
	"""支付方式 	1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
		@see PaymentOrderTypes
	"""
	paymentType:Int
	"""统计信息"""
	statisticInfo:SubOrderStatisticDto
}
"""购买渠道统计信息
	<AUTHOR>
	@date 2022/05/10
"""
type PurchaseChannelStatisticDto @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.nested.PurchaseChannelStatisticDto") {
	"""购买渠道 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see PurchaseChannelTypes
	"""
	purchaseChannel:Int
	"""各支付方式统计信息"""
	paymentTypeStatisticInfoList:[PaymentTypeStatisticDto]
}
"""子订单变更记录统计情况
	<AUTHOR>
	@date 2022/05/10
"""
type SubOrderStatisticDto @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.nested.SubOrderStatisticDto") {
	"""交易成功数量"""
	tradeSuccessCount:Long
	"""退货数量"""
	returnCount:Long
	"""换入数量"""
	exchangeInCount:Long
	"""换出数量"""
	exchangeOutCount:Long
	"""净交易成功数量
		<p> (交易成功数量 + 换入数量) - (退货数量 + 换出数量)
	"""
	netTradeSuccessCount:Long
}
"""交易记录时间单位统计信息
	<AUTHOR>
"""
type TradeStatisticDateHistogramBucketResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.tradereport.gateway.graphql.response.nested.TradeStatisticDateHistogramBucketResponse") {
	"""时间"""
	time:String
	"""交易成功金额"""
	tradeSuccessAmount:BigDecimal
}
"""排序参数"""
enum SortPolicy1 @type(value:"com.fjhb.ms.trade.queryv2.kernel.service.common.param.SortPolicy") {
	"""正序"""
	ASC
	"""倒序"""
	DESC
}

scalar List
type BatchOrderResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BatchOrderResponse]}
type BatchReturnOrderResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BatchReturnOrderResponse]}
type CommodityOpenReportFormResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CommodityOpenReportFormResponse]}
type CommoditySkuBackstageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CommoditySkuBackstageResponse]}
type ExchangeOrderResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ExchangeOrderResponse]}
type IssueCommoditySkuBackStageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [IssueCommoditySkuBackStageResponse]}
type IssueLogResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [IssueLogResponse]}
type OfflineInvoiceResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [OfflineInvoiceResponse]}
type OnlineInvoiceResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [OnlineInvoiceResponse]}
type OrderResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [OrderResponse]}
type ReceiveAccountConfigResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ReceiveAccountConfigResponse]}
type ReturnOrderResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [ReturnOrderResponse]}
