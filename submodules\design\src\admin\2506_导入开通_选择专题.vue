<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--选择专题-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">选择专题</el-button>
        <el-drawer
          title="选择专题"
          :visible.sync="dialog1"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--显示5个，超出部分隐藏-->
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="8">
                  <el-form-item label="专题名称">
                    <el-input v-model="input" clearable placeholder="请输入专题名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="专题入口名称">
                    <el-input v-model="input" clearable placeholder="请输入专题入口名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="专题类型">
                    <el-select clearable placeholder="请选择专题类型">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="地区">
                    <el-cascader clearable filterable :options="cascader" placeholder="请选择地区" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="行业">
                    <el-select v-model="select" clearable placeholder="请选择行业">
                      <el-option value="人社"></el-option>
                      <el-option value="建设"></el-option>
                      <el-option value="职业卫生"></el-option>
                      <el-option value="工勤"></el-option>
                      <el-option value="教师"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="所属单位">
                    <el-input v-model="input" clearable placeholder="请输入单位名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="专题状态">
                    <el-select clearable placeholder="请选择专题状态">
                      <el-option label="选项1" value=""></el-option>
                      <el-option label="选项2" value=""></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="显示在网校门户">
                    <el-select v-model="select" clearable placeholder="请选择是否显示在网校门户">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="编辑时间">
                    <el-date-picker
                      v-model="value1"
                      type="daterange"
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :span="8" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                    <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                    <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="专题名称" min-width="150">
                <template>福州地区专题页</template>
              </el-table-column>
              <el-table-column label="专题入口名称" min-width="150">
                <template>专题入口名称</template>
              </el-table-column>
              <el-table-column label="专题类型" min-width="150">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        >
                      </div>
                      <el-button type="text" class="f-to-three"
                        ><i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        ></el-button
                      >
                    </el-tooltip>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <p><el-tag size="mini" class="f-mt5">行业</el-tag>工艺美术</p>
                  </div>
                  <div v-else-if="scope.$index === 2">
                    <p><el-tag type="success" size="mini" class="f-mt5">单位</el-tag>华博教育</p>
                  </div>
                  <div v-else>
                    <el-tooltip placement="top" effect="light">
                      <div slot="content">
                        <i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        >
                      </div>
                      <el-button type="text" class="f-to-three"
                        ><i class="f-c4"
                          ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag
                          >福建省-福州市-鼓楼区-福建省-福州市-鼓楼区福建省-福州市-鼓楼区-福建省-福州市-鼓楼区</i
                        ></el-button
                      >
                    </el-tooltip>
                    <p><el-tag size="mini" class="f-mt5">行业</el-tag>工艺美术</p>
                    <p><el-tag type="success" size="mini" class="f-mt5">单位</el-tag>华博教育</p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="显示在网校" min-width="120" align="center">
                <template>显示</template>
              </el-table-column>
              <el-table-column label="状态" min-width="100">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="最新编辑时间" min-width="180">
                <template>2021-10-15 00:21:21</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">选择</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: true,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
