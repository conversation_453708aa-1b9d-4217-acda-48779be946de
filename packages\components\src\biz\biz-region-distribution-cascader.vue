<route-meta>
{
"title": "地区管理员报表级联选择器"
}
</route-meta>
<template>
  <el-cascader
    ref="elCascaderRef"
    v-if="show"
    :props="props"
    v-model="selctValue"
    :options="options"
    :placeholder="placeholder"
    :clearable="clearable"
    :style="{ width: '100%' }"
    collapse-tags
    @change="onInput"
    v-bind="$attrs"
    :disabled="disabled"
  ></el-cascader>
</template>

<script lang="ts">
  import { Component, Vue, Prop, Emit, Watch, Ref } from 'vue-property-decorator'
  import { ElCascader } from 'element-ui/types/cascader'
  import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegionInDistribution'
  import QueryUserFactory from '@api/service/management/user/QueryUserFactory'
  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    disabled: boolean

    @Prop({
      default: true
    })
    clearable: boolean

    @Prop({
      default: false
    })
    multiple: boolean

    // 传入的必须是数组
    @Prop({
      type: [Array, String]
    })
    value: string[]

    @Prop({
      default: '请选择地区',
      type: String
    })
    placeholder: string

    /**
     * 省份id、用于过滤省份
     */
    @Prop({
      default: '0',
      type: String
    })
    provinceId: string

    @Prop({
      default: false
    })
    checkStrictly: boolean

    @Ref('elCascaderRef') elCascaderRef: ElCascader

    // 当前省份
    // provinces = new Array<RegionInfo>()
    provinces: Array<RegionTreeVo> = new Array<RegionTreeVo>()
    // 初始内容 无回显的情况 string[]
    // 回显的数据结构参见 https://element.eleme.cn/#/zh-CN/component/cascader
    options = new Array<RegionTreeVo>()
    // 当前选中的值
    selctValue: string[] = []
    show = true
    props = {}
    toParent = {
      id: '',
      name: ''
    }
    @Watch('value')
    setValue() {
      this.selctValue = this.value
    }
    @Emit('input')
    onInput(values: any) {
      const id = '/' + values?.join('/')
      this.$emit('toParent', id)
    }

    getName(currentnode: any) {
      const result = [] as string[]
      const getParent = (currentnode: any, result: string[]) => {
        result.push(currentnode.label)
        if (currentnode.parent) {
          getParent(currentnode.parent, result)
        }
      }
      getParent(currentnode, result)
      return result
    }

    async created() {
      this.setProps()
      const nationTree = await QueryBusinessRegion.getServiceOrIndustry(1)

      const serveIds = await QueryBusinessRegion.getServiceRegionIds()

      this.options = QueryBusinessRegion.filterRegionTree(nationTree, serveIds)
    }

    setProps() {
      this.props = {
        lazy: false,
        value: 'id',
        label: 'name',
        disabled: 'disabled',
        multiple: this.multiple,
        checkStrictly: this.checkStrictly
      }
    }

    /**
     * 地区去掉最末级children为空
     */
    treeData(data: any) {
      for (let i = 0; i < data?.length; i++) {
        if (data[i].children?.length < 1) {
          delete data[i].children
        } else {
          this.treeData(data[i].children)
        }
      }
      return data
    }
  }
</script>
