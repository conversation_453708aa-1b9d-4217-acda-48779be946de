import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'qztg-trade-query-front-gateway-backstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class CommodityBasicDataResponse {
  saleTitle: string
  price: number
  commodityPicturePath: string
}

export class CommodityExtInfoResponse {
  resourceProviderResponse: CommodityResourceProviderResponse
}

export class CommodityPurchaseChannelConfigResponse {
  customerPurchase: PurchaseChannelConfigResponse
  collectivePurchase: PurchaseChannelConfigResponse
  administratorImport: PurchaseChannelConfigResponse
  collectiveSignUpPersonalPay: PurchaseChannelConfigResponse
}

export class CommodityResourceProviderResponse {
  id: string
  name: string
}

export class CommoditySkuPropertyResponse {
  year: SkuPropertyResponse
  province: SkuPropertyResponse
  city: SkuPropertyResponse
  county: SkuPropertyResponse
  industry: SkuPropertyResponse
  subjectType: SkuPropertyResponse
  trainingCategory: SkuPropertyResponse
  trainingProfessional: SkuPropertyResponse
  technicalGrade: SkuPropertyResponse
  trainingObject: SkuPropertyResponse
  positionCategory: SkuPropertyResponse
  jobLevel: SkuPropertyResponse
  jobCategory: SkuPropertyResponse
  grade: SkuPropertyResponse
  subject: SkuPropertyResponse
  learningPhase: SkuPropertyResponse
  discipline: SkuPropertyResponse
  certificatesType: SkuPropertyResponse
  practitionerCategory: SkuPropertyResponse
  qualificationCategory: SkuPropertyResponse
  trainingForm: SkuPropertyResponse
}

export class CommodityTrainingChannelResponse {
  trainingChannelId: string
  trainingChannelName: string
  sort: number
}

export class OnShelveResponse {
  shelveStatus: number
  lastOnShelveTime: string
  offShelveTime: string
  onShelvePlanTime: string
  offShelvePlanTime: string
  publishTime: string
}

export class PurchaseChannelConfigResponse {
  couldSee: boolean
  couldBuy: boolean
}

export class SchemeIssueInfoResponse {
  issueCount: number
}

export class SkuPropertyResponse {
  skuPropertyValueId: string
  skuPropertyValueName: string
}

export class UserPossessionInfoResponse {
  possessing: boolean
  sourceType: number
  sourceId: string
  subOrderDeliveryStatus: number
}

export interface ResourceResponse {
  resourceType: string
}

export class SchemeResourceResponse implements ResourceResponse {
  schemeId: string
  schemeName: string
  period: number
  schemeType: string
  resourceType: string
}

export class CommoditySkuResponse {
  /**
   * 商品id
   */
  commoditySkuId: string
  /**
   * 商品销售标题
   */
  saleTitle: string
  /**
   * 商品价格
   */
  price: number
  /**
   * 培训方案id
   */
  schemeId: string
  /**
   * 商品封面图路径
   */
  commodityPicturePath: string
  /**
   * 用户商品拥有信息
   */
  possessionInfo: UserPossessionInfoResponse
  /**
   * 商品属性信息
   */
  skuProperty: CommoditySkuPropertyResponse
  /**
   * 商品所有渠道的配置信息
   */
  commodityPurchaseChannelConfig: CommodityPurchaseChannelConfigResponse
  /**
   * 上下架信息
   */
  onShelve: OnShelveResponse
}

export class QZTGCommoditySkuBackstageResponse {
  /**
   * 当前商品合并了哪些列表
   */
  mergedCommodities: Array<CommoditySkuResponse>
  /**
   * 当前商品被哪些合并的列表
   */
  mergedFromCommodities: Array<CommoditySkuResponse>
  commoditySkuId: string
  commodityBasicData: CommodityBasicDataResponse
  skuProperty: CommoditySkuPropertyResponse
  onShelve: OnShelveResponse
  commodityPurchaseChannelConfig: CommodityPurchaseChannelConfigResponse
  resource: ResourceResponse
  commodityLastEditTime: string
  commodityCreatTime: string
  isResourceEnabled: boolean
  trainingChannels: Array<CommodityTrainingChannelResponse>
  tppTypeId: string
  externalTrainingPlatform: string
  learningSupervisionSystem: string
  courseSupplierId: string
  thirdPartyTrainingSchemeId: string
  saleTotalNumber: number
  issueInfoResponse: SchemeIssueInfoResponse
  unitId: string
  unitName: string
  extInfo: CommodityExtInfoResponse
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取培训班商品详情
   * @param query 查询 graphql 语法文档
   * @param commoditySkuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCommoditySkuInServicer(
    commoditySkuId: string,
    query: DocumentNode = GraphqlImporter.getCommoditySkuInServicer,
    operation?: string
  ): Promise<Response<QZTGCommoditySkuBackstageResponse>> {
    return commonRequestApi<QZTGCommoditySkuBackstageResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { commoditySkuId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 当前商品是否被引用
   * @param query 查询 graphql 语法文档
   * @param commoditySkuId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async isReferenced(
    commoditySkuId: string,
    query: DocumentNode = GraphqlImporter.isReferenced,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { commoditySkuId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
