import DateScope from '@api/service/common/models/DateScope'
import { QueryWayType, StatisticTradeRecordRequest } from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import {
  QueryWayType as QueryWayTypeExport,
  StatisticTradeRecordRequest as StatisticTradeRecordRequestExport
} from '@api/platform-gateway/fxnl-data-export-gateway-backstage'
import Context from '@api/service/common/context/Context'

export default class SupplierDistributorSalesStatisticsParams {
  /**
   * 分销商品名称
   */
  distributedTradeName = ''
  /**
   * 分销商名称
   */
  distributorName = ''
  /**
   * 分销商Id
   */
  distributorId = ''
  /**
   * 推广门户名称
   */
  portalPromoteTheName = ''
  /**
   * 推广门户Id
   */
  portalPromoteId = ''
  /**
   * 报名时间
   */
  registrationPeriod: DateScope = new DateScope()
  /**
   * 查看非门户推广数据
   */
  isPortalData = false

  static toStatisticTradeRecordRequest(dto: SupplierDistributorSalesStatisticsParams) {
    const vo = new StatisticTradeRecordRequest()
    vo.onlineSchoolList = [Context.businessEnvironment?.serviceToken?.tokenMeta?.servicerId]
    vo.commodityName = dto.distributedTradeName
    if (dto.distributorId) {
      vo.distributorIdList = [dto.distributorId]
    }
    // 查看非推广门户时，传false
    if (dto.isPortalData) {
      vo.isPortalData = !dto.isPortalData
    }
    if (dto.portalPromoteId && !dto.isPortalData) {
      vo.portalId = dto.portalPromoteId
    }
    vo.queryDateScope = dto.registrationPeriod
    vo.queryWayType = QueryWayType.ALL
    return vo
  }

  static toStatisticTradeRecordRequestExport(dto: SupplierDistributorSalesStatisticsParams) {
    const vo = new StatisticTradeRecordRequestExport()
    vo.commodityName = dto.distributedTradeName
    if (dto.distributorId) {
      vo.distributorIdList = [dto.distributorId]
    }
    // 查看非推广门户时，传false
    if (dto.isPortalData) {
      vo.isPortalData = !dto.isPortalData
    }
    if (dto.portalPromoteId && !dto.isPortalData) {
      vo.portalId = dto.portalPromoteId
    }
    vo.queryDateScope = dto.registrationPeriod
    vo.queryWayType = QueryWayTypeExport.ALL
    return vo
  }
}
