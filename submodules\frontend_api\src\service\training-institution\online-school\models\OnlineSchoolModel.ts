import {
  Add<PERSON><PERSON>ue<PERSON>er<PERSON>,
  Administrator,
  Client,
  CreateOSContractByStatusRequest,
  NewCreateOnlineSchoolRequest,
  PersonIndustry,
  SmsConfig,
  UpdateOnlineSchoolRequest,
  UpdateOnlineSchoolTemplateRequest,
  UpdateOSBasicInfoRequest
} from '@api/ms-gateway/ms-servicercontract-v1'
import {
  ClientTypesEnum,
  CNZZModeEnum,
  perfectInfoEnum,
  ServicePeriodEnum,
  SmsProviderEnum,
  TemplateIdEnum
} from '@api/service/training-institution/online-school/enum/OnlineSchoolEnum'
import SchoolBaseModel from '@api/service/training-institution/online-school/base-models/SchoolBaseModel'
import SchoolConfigModel from '@api/service/training-institution/online-school/base-models/SchoolConfigModel'
import AddServiceModel from '@api/service/training-institution/online-school/base-models/AddServiceModel'
import BusinessAttributesModel from '@api/service/training-institution/online-school/base-models/BusinessAttributesModel'
import AdministratorModel from '@api/service/training-institution/online-school/models/AdministratorModel'
import { OnlineSchoolInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { IndustryIdEnum } from '@api/service/training-institution/online-school/enum/IndustryEnum'
import { AddServiceEnum } from '@api/service/training-institution/online-school/enum/AddServiceEnum'

/**
 * 创建网校模型
 */
export default class OnlineSchoolModel {
  /**
   * 合约id
   */
  id: string = undefined

  /**
   * 网校id
   */
  schoolId: string = undefined

  /**
   * 网校基础信息
   */
  schoolBase: SchoolBaseModel = new SchoolBaseModel()

  /**
   * 网校配置
   */
  schoolConfig: SchoolConfigModel = new SchoolConfigModel()

  /****** 网校管理员创建时是单一对象，但查询回来是数组的形式故开两个类型的字段让UI好处理 ******/
  /**
   * 管理员账号（供给创建时绑定）
   */
  administrator: Administrator = new Administrator()
  /**
   * 管理员账号（供给显示详情时绑定）
   */
  administratorList: Array<AdministratorModel> = new Array<AdministratorModel>()
  /**
   * 增值服务
   */
  addServiceConfig: AddServiceModel = new AddServiceModel()
  /**
   * 合约状态
   */
  status: number = undefined

  /**
   * 是否可用
   */
  enable: boolean = undefined

  /**
   * 是否到期
   */
  isExpired: boolean = undefined

  /**
   * 是否续期
   */
  isRenewed: boolean = undefined

  /**
   * 模型转化
   */
  static from(dto: OnlineSchoolInfoResponse) {
    const vo = new OnlineSchoolModel()

    vo.id = dto?.basicInfo?.contractId
    vo.schoolId = dto?.basicInfo?.onlineSchoolId
    vo.status = dto?.basicInfo?.status
    vo.enable = dto?.basicInfo?.isEnable
    vo.isExpired = dto?.basicInfo?.isExpired
    vo.isRenewed = dto?.basicInfo?.isRenewed
    vo.schoolBase.schoolName = dto?.basicInfo?.name
    vo.schoolBase.serviceRegionCodes = dto?.basicInfo?.serviceAreas
    vo.schoolBase.industryYears = dto?.basicInfo?.trainingProperties?.years
    vo.schoolBase.industryRegionCodes = dto?.basicInfo?.trainingProperties?.areas
    let findRSIndustries,
      findJSIndustries,
      findWSIndustries,
      findGQIndustries,
      findLSIndustries,
      findYSIndustries,
      findRSPersonIndustries,
      findJSPersonIndustries,
      findWSPersonIndustries,
      findGQPersonIndustries,
      findLSPersonIndustries,
      findYSPersonIndustries = undefined
    if (dto?.basicInfo?.trainingProperties.industries?.length) {
      findRSIndustries =
        dto.basicInfo.trainingProperties.industries.find(item => item.id === IndustryIdEnum.RS) || undefined
      findJSIndustries =
        dto.basicInfo.trainingProperties.industries.find(item => item.id === IndustryIdEnum.JS) || undefined
      findWSIndustries =
        dto.basicInfo.trainingProperties.industries.find(item => item.id === IndustryIdEnum.WS) || undefined
      findGQIndustries =
        dto.basicInfo.trainingProperties.industries.find(item => item.id === IndustryIdEnum.GQ) || undefined
      findLSIndustries =
        dto.basicInfo.trainingProperties.industries.find(item => item.id === IndustryIdEnum.LS) || undefined
      findYSIndustries =
        dto.basicInfo.trainingProperties.industries.find(item => item.id === IndustryIdEnum.YS) || undefined
    }
    if (dto.basicInfo?.personIndustries?.length) {
      findRSPersonIndustries = dto.basicInfo.personIndustries.find(item => item.id === IndustryIdEnum.RS) || undefined
      findJSPersonIndustries = dto.basicInfo.personIndustries.find(item => item.id === IndustryIdEnum.JS) || undefined
      findWSPersonIndustries = dto.basicInfo.personIndustries.find(item => item.id === IndustryIdEnum.WS) || undefined
      findGQPersonIndustries = dto.basicInfo.personIndustries.find(item => item.id === IndustryIdEnum.GQ) || undefined
      findLSPersonIndustries = dto.basicInfo.personIndustries.find(item => item.id === IndustryIdEnum.LS) || undefined
      findYSPersonIndustries = dto.basicInfo.personIndustries.find(item => item.id === IndustryIdEnum.YS) || undefined
    }
    vo.schoolBase.haveRSIndustry = !!findRSIndustries
    vo.schoolBase.haveJSIndustry = !!findJSIndustries
    vo.schoolBase.haveGQIndustry = !!findGQIndustries
    vo.schoolBase.haveWSIndustry = !!findWSIndustries
    vo.schoolBase.haveLSIndustry = !!findLSIndustries
    vo.schoolBase.haveYSIndustry = !!findYSIndustries
    vo.schoolBase.RSIndustry = findRSIndustries
    vo.schoolBase.JSIndustry = findJSIndustries
    vo.schoolBase.WSIndustry = findWSIndustries
    vo.schoolBase.GQIndustry = findGQIndustries
    vo.schoolBase.LSIndustry = findLSIndustries
    vo.schoolBase.YSIndustry = findYSIndustries
    vo.schoolBase.personIndustriesProperties.JSIndustry = findJSPersonIndustries
    vo.schoolBase.personIndustriesProperties.RSIndustry = findRSPersonIndustries
    vo.schoolBase.personIndustriesProperties.WSIndustry = findWSPersonIndustries
    vo.schoolBase.personIndustriesProperties.GQIndustry = findGQPersonIndustries
    vo.schoolBase.personIndustriesProperties.LSIndustry = findLSPersonIndustries
    vo.schoolBase.personIndustriesProperties.YSIndustry = findYSPersonIndustries
    vo.schoolBase.ownerUnitName = dto?.basicInfo?.unitName
    vo.schoolBase.ownerUnitSingleName = dto?.basicInfo?.unitShotName
    vo.schoolBase.ownerCharge = dto?.basicInfo?.transactor
    vo.schoolBase.phone = dto?.basicInfo?.phone
    vo.schoolBase.schoolModel = dto?.basicInfo?.onlineSchoolModes
    vo.schoolBase.contractSigning = dto?.basicInfo?.isOfflineContractSigned
    vo.schoolBase.signingTime = dto?.basicInfo?.offlineContractSignedDate
    vo.schoolBase.belongMarket = dto?.basicInfo?.marketTransactor
    vo.schoolBase.description = dto?.basicInfo?.description

    dto?.configInfo?.clients?.length &&
      dto.configInfo.clients.map(item => {
        if (item.clientType === ClientTypesEnum.WEB) {
          vo.schoolConfig.webDomain = item.domainName
          vo.schoolConfig.provideWebService = true
          vo.schoolConfig.webPortalTemplateId = item.portalTemplateId
        }
        if (item.clientType === ClientTypesEnum.H5) {
          vo.schoolConfig.H5Domain = item.domainName
          vo.schoolConfig.provideH5Service = true
          vo.schoolConfig.domainNameType = item.domainNameType
          vo.schoolConfig.H5PortalTemplateId = item.portalTemplateId
        }
        vo.schoolConfig.domainNameType = item.domainNameType
        vo.schoolConfig.cnzzValue = item.cnzz
        if (item.cnzz === vo.schoolConfig.defaultCnzzValue) {
          vo.schoolConfig.cnzzMode = CNZZModeEnum.DEFAULT
        } else {
          vo.schoolConfig.cnzzMode = CNZZModeEnum.CUSTOMER
        }
      })

    vo.schoolConfig.provideSms = dto?.configInfo?.offerSmsService
    vo.schoolConfig.smsAccount = dto?.configInfo?.smsConfig?.smsAccount
    vo.schoolConfig.smsPassword = dto?.configInfo?.smsConfig?.smsPassword
    vo.schoolConfig.smsService = dto?.configInfo?.smsConfig?.providerId as SmsProviderEnum
    vo.schoolConfig.servicePeriodModel = dto?.configInfo?.trainingPeriodModes
    vo.schoolConfig.serviceOverTime = dto?.configInfo?.expireDate
    // 模板id暂时写死

    if (dto?.configInfo?.enabledForceCompleteInfo || dto?.configInfo?.enabledForceCompleteInfo == null) {
      vo.schoolConfig.perfectInfoModel = perfectInfoEnum.DOPERFECT
    } else {
      vo.schoolConfig.perfectInfoModel = perfectInfoEnum.SKIPPERFECT
    }
    // vo.schoolConfig.perfectInfoModel = dto?.configInfo?.enabledForceCompleteInfo
    //   ? perfectInfoEnum.SKIPPERFECT
    //   : perfectInfoEnum.DOPERFECT
    return vo
  }

  /**
   * 转化为后端创建请求request
   */
  toCreateRequest(): CreateOSContractByStatusRequest {
    const request = new CreateOSContractByStatusRequest()

    request.id = this.id
    request.onlineSchool = new NewCreateOnlineSchoolRequest()
    request.onlineSchool.id = this.schoolId
    request.onlineSchool.smsConfig = new SmsConfig()
    request.onlineSchool.clients = new Array<Client>()

    request.administrator = this.administrator
    request.unitName = this.schoolBase.ownerUnitName
    request.unitShotName = this.schoolBase.ownerUnitSingleName
    request.transactor = this.schoolBase.ownerCharge
    request.phone = this.schoolBase.phone
    request.serviceAreas = this.schoolBase.serviceRegionCodes
    request.marketTransactor = this.schoolBase.belongMarket
    request.description = this.schoolBase.description
    request.onlineSchoolModes = this.schoolBase.schoolModel
    request.isOfflineContractSigned = this.schoolBase.contractSigning
    request.offlineContractSignedDate = this.schoolBase.signingTime
    request.onlineSchool.name = this.schoolBase.schoolName
    request.onlineSchool.trainingProperties = new BusinessAttributesModel()
    request.onlineSchool.personIndustries = new Array<PersonIndustry>()
    request.onlineSchool.trainingProperties.areas = this.schoolBase.industryRegionCodes
    request.onlineSchool.trainingProperties.years = this.schoolBase.industryYears
    if (this.schoolBase.haveJSIndustry) {
      if (this.schoolBase.JSIndustry) {
        request.onlineSchool.trainingProperties.industries.push(this.schoolBase.JSIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.JSIndustry) {
        request.onlineSchool.personIndustries.push(this.schoolBase.personIndustriesProperties.JSIndustry)
      }
    }
    if (this.schoolBase.haveRSIndustry) {
      if (this.schoolBase.RSIndustry) {
        request.onlineSchool.trainingProperties.industries.push(this.schoolBase.RSIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.RSIndustry) {
        request.onlineSchool.personIndustries.push(this.schoolBase.personIndustriesProperties.RSIndustry)
      }
    }
    if (this.schoolBase.haveWSIndustry) {
      if (this.schoolBase.WSIndustry) {
        request.onlineSchool.trainingProperties.industries.push(this.schoolBase.WSIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.WSIndustry) {
        request.onlineSchool.personIndustries.push(this.schoolBase.personIndustriesProperties.WSIndustry)
      }
    }
    if (this.schoolBase.haveGQIndustry) {
      if (this.schoolBase.GQIndustry) {
        request.onlineSchool.trainingProperties.industries.push(this.schoolBase.GQIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.GQIndustry) {
        request.onlineSchool.personIndustries.push(this.schoolBase.personIndustriesProperties.GQIndustry)
      }
    }
    if (this.schoolBase.haveLSIndustry) {
      if (this.schoolBase.LSIndustry) {
        request.onlineSchool.trainingProperties.industries.push(this.schoolBase.LSIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.LSIndustry) {
        request.onlineSchool.personIndustries.push(this.schoolBase.personIndustriesProperties.LSIndustry)
      }
    }
    if (this.schoolBase.haveYSIndustry) {
      if (this.schoolBase.YSIndustry) {
        request.onlineSchool.trainingProperties.industries.push(this.schoolBase.YSIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.YSIndustry) {
        request.onlineSchool.personIndustries.push(this.schoolBase.personIndustriesProperties.YSIndustry)
      }
    }
    request.trainingPeriodModes = this.schoolConfig.servicePeriodModel
    if (this.schoolConfig.servicePeriodModel === ServicePeriodEnum.SHORT) {
      request.expireDate = this.schoolConfig.serviceOverTime
    } else {
      request.expireDate = '9999-01-01 23:59:59'
    }
    request.onlineSchool.offerSmsService = this.schoolConfig.provideSms
    if (this.schoolConfig.provideSms) {
      request.onlineSchool.smsConfig.smsAccount = this.schoolConfig.smsAccount
      request.onlineSchool.smsConfig.smsPassword = this.schoolConfig.smsPassword
      request.onlineSchool.smsConfig.providerId = this.schoolConfig.smsService
    }
    if (this.schoolConfig.provideWebService) {
      const webClient = new Client()
      webClient.clientType = ClientTypesEnum.WEB
      webClient.domainName = this.schoolConfig.webDomain
      webClient.domainNameType = this.schoolConfig.domainNameType
      webClient.portalTemplateId = this.schoolConfig.webPortalTemplateId
      if (this.schoolConfig.cnzzMode === CNZZModeEnum.DEFAULT) {
        // 默认CNZZ值  来源于网校内置数据
        webClient.cnzz = this.schoolConfig.defaultCnzzValue
      } else {
        webClient.cnzz = this.schoolConfig.cnzzValue
      }
      request.onlineSchool.clients.push(webClient)
    }
    if (this.schoolConfig.provideH5Service) {
      const webClient = new Client()
      webClient.clientType = ClientTypesEnum.H5
      webClient.domainName = this.schoolConfig.H5Domain
      webClient.domainNameType = this.schoolConfig.domainNameType
      webClient.portalTemplateId = this.schoolConfig.H5PortalTemplateId
      if (this.schoolConfig.cnzzMode === CNZZModeEnum.DEFAULT) {
        // 默认CNZZ值  来源于网校内置数据
        webClient.cnzz = this.schoolConfig.defaultCnzzValue
      } else {
        webClient.cnzz = this.schoolConfig.cnzzValue
      }
      request.onlineSchool.clients.push(webClient)
    }
    // 增值服务
    request.addValueService = new AddValueService()
    if (this.addServiceConfig.addServiceType.includes(AddServiceEnum.learningRule)) {
      request.addValueService.learnRule = true
    } else {
      request.addValueService.learnRule = false
    }
    if (this.addServiceConfig.addServiceType.includes(AddServiceEnum.intelligentlearning)) {
      request.addValueService.intelligentlearning = true
    } else {
      request.addValueService.intelligentlearning = false
    }
    request.onlineSchool.enabledForceCompleteInfo = this.schoolConfig.perfectInfoModel === 0
    if (this.addServiceConfig.addServiceType.includes(AddServiceEnum.fxService)) {
      request.addValueService.distributionService = true
      request.addValueService.distributionServiceType = this.addServiceConfig.distributionServiceType
    } else {
      request.addValueService.distributionService = false
    }
    return request
  }

  /**
   * 转化为更新网校基础信息请求request
   */
  toUpdateBaseRequest(): UpdateOSBasicInfoRequest {
    const request = new UpdateOSBasicInfoRequest()
    request.id = this.id
    request.name = this.schoolBase.schoolName
    request.unitName = this.schoolBase.ownerUnitName
    request.unitShotName = this.schoolBase.ownerUnitSingleName
    request.phone = this.schoolBase.phone
    request.serviceAreas = this.schoolBase.serviceRegionCodes
    request.onlineSchoolModes = this.schoolBase.schoolModel
    request.isOfflineContractSigned = this.schoolBase.contractSigning
    request.offlineContractSignedDate = this.schoolBase.signingTime
    request.transactor = this.schoolBase.ownerCharge
    request.trainingPeriodModes = this.schoolConfig.servicePeriodModel
    request.description = this.schoolBase.description
    request.marketTransactor = this.schoolBase.belongMarket
    request.trainingProperties = new BusinessAttributesModel()
    request.personIndustries = new Array<PersonIndustry>()
    request.expireDate = this.schoolConfig.serviceOverTime
    if (this.schoolBase.haveJSIndustry) {
      if (this.schoolBase.JSIndustry) {
        request.trainingProperties.industries.push(this.schoolBase.JSIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.JSIndustry) {
        request.personIndustries.push(this.schoolBase.personIndustriesProperties.JSIndustry)
      }
    }
    if (this.schoolBase.haveRSIndustry) {
      if (this.schoolBase.RSIndustry) {
        request.trainingProperties.industries.push(this.schoolBase.RSIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.RSIndustry) {
        request.personIndustries.push(this.schoolBase.personIndustriesProperties.RSIndustry)
      }
    }
    if (this.schoolBase.haveWSIndustry) {
      if (this.schoolBase.WSIndustry) {
        request.trainingProperties.industries.push(this.schoolBase.WSIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.WSIndustry) {
        request.personIndustries.push(this.schoolBase.personIndustriesProperties.WSIndustry)
      }
    }
    if (this.schoolBase.haveGQIndustry) {
      if (this.schoolBase.GQIndustry) {
        request.trainingProperties.industries.push(this.schoolBase.GQIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.GQIndustry) {
        request.personIndustries.push(this.schoolBase.personIndustriesProperties.GQIndustry)
      }
    }
    if (this.schoolBase.haveLSIndustry) {
      if (this.schoolBase.LSIndustry) {
        request.trainingProperties.industries.push(this.schoolBase.LSIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.LSIndustry) {
        request.personIndustries.push(this.schoolBase.personIndustriesProperties.LSIndustry)
      }
    }
    if (this.schoolBase.haveYSIndustry) {
      if (this.schoolBase.YSIndustry) {
        request.trainingProperties.industries.push(this.schoolBase.YSIndustry)
      }
      if (this.schoolBase.personIndustriesProperties.YSIndustry) {
        request.personIndustries.push(this.schoolBase.personIndustriesProperties.YSIndustry)
      }
    }
    request.trainingProperties.areas = this.schoolBase.serviceRegionCodes
    if (this.schoolBase.industryYears) {
      request.trainingProperties.years = this.schoolBase.industryYears
    }
    // 增值服务
    request.addValueService = new AddValueService()
    if (this.addServiceConfig.addServiceType.includes(AddServiceEnum.learningRule)) {
      request.addValueService.learnRule = true
    } else {
      request.addValueService.learnRule = false
    }
    if (this.addServiceConfig.addServiceType.includes(AddServiceEnum.intelligentlearning)) {
      request.addValueService.intelligentlearning = true
    } else {
      request.addValueService.intelligentlearning = false
    }
    if (this.addServiceConfig.addServiceType.includes(AddServiceEnum.fxService)) {
      request.addValueService.distributionService = true
      request.addValueService.distributionServiceType = this.addServiceConfig.distributionServiceType
    } else {
      request.addValueService.distributionService = false
    }
    return request
  }

  /**
   * 转化为更新网校配置请求request
   */
  toUpdateConfigRequest(): UpdateOnlineSchoolRequest {
    const request = new UpdateOnlineSchoolRequest()
    request.id = this.id
    request.clients = new Array<Client>()
    request.smsConfig = new SmsConfig()

    request.offerSmsService = this.schoolConfig.provideSms
    request.trainingPeriodModes = this.schoolConfig.servicePeriodModel
    if (this.schoolConfig.servicePeriodModel === ServicePeriodEnum.SHORT) {
      request.expireDate = this.schoolConfig.serviceOverTime
    } else {
      request.expireDate = '9999-01-01 23:59:59'
    }
    if (this.schoolConfig.provideSms) {
      request.smsConfig.smsAccount = this.schoolConfig.smsAccount
      request.smsConfig.smsPassword = this.schoolConfig.smsPassword
      request.smsConfig.providerId = this.schoolConfig.smsService
    }
    if (this.schoolConfig.provideWebService) {
      const webClient = new Client()
      webClient.clientType = ClientTypesEnum.WEB
      webClient.domainName = this.schoolConfig.webDomain
      webClient.domainNameType = this.schoolConfig.domainNameType
      webClient.portalTemplateId = this.schoolConfig.webPortalTemplateId
      if (this.schoolConfig.cnzzMode === CNZZModeEnum.DEFAULT) {
        // 默认CNZZ值  来源于网校内置数据
        webClient.cnzz = this.schoolConfig.defaultCnzzValue
      } else {
        webClient.cnzz = this.schoolConfig.cnzzValue
      }
      request.clients.push(webClient)
    }
    if (this.schoolConfig.provideH5Service) {
      const webClient = new Client()
      webClient.clientType = ClientTypesEnum.H5
      webClient.domainName = this.schoolConfig.H5Domain
      webClient.domainNameType = this.schoolConfig.domainNameType
      webClient.portalTemplateId = this.schoolConfig.H5PortalTemplateId
      request.clients.push(webClient)
      if (this.schoolConfig.cnzzMode === CNZZModeEnum.DEFAULT) {
        // 默认CNZZ值  来源于网校内置数据
        webClient.cnzz = this.schoolConfig.defaultCnzzValue
      } else {
        webClient.cnzz = this.schoolConfig.cnzzValue
      }
    }
    request.enabledForceCompleteInfo = this.schoolConfig.perfectInfoModel === 0
    return request
  }

  /**
   * 转化为更新模板请求request
   */
  toUpdateTemplateRequest(): UpdateOnlineSchoolTemplateRequest {
    const request = new UpdateOnlineSchoolTemplateRequest()
    request.id = this.id
    request.portalTemplateId = this.schoolConfig.webPortalTemplateId
    request.portalH5TemplateId = this.schoolConfig.H5PortalTemplateId

    return request
  }
}
