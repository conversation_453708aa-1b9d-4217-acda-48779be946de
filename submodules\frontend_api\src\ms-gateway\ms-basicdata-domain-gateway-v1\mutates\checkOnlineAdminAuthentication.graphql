mutation checkOnlineAdminAuthentication(
  $identity: String
  $applicationMemberType: Int
  $currentUserId: String
  $categoryTypeList: [Int]
  $identityType: Int
  $serviceId: String
) {
  checkOnlineAdminAuthentication(
    identity: $identity
    applicationMemberType: $applicationMemberType
    currentUserId: $currentUserId
    categoryTypeList: $categoryTypeList
    identityType: $identityType
    serviceId: $serviceId
  )
}
