schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""统计在线人数  不会重复
		@param param
		@return
	"""
	countUserOnlineCount(param:DashboardParam):Long!
	"""批量导出学员学习日志
		@param request
		@return
	"""
	createBatchExportLearningLogTask(request:LearningStatisticRequest):Boolean!
	"""批量导出学员学习日志
		为渠道商提供接口
		@param request
		@return
	"""
	createBatchExportLearningLogTaskForChannelVendor(request:LearningStatisticRequest):Boolean!
	"""批量导出学员学习日志
		为参训单位提供接口
		@param request
		@return
	"""
	createBatchExportLearningLogTaskForParticipatingUnit(request:LearningStatisticRequest):Boolean!
	"""批量导出学员学习日志
		为机构提供接口
		@param request
		@return
	"""
	createBatchExportLearningLogTaskForTrainingInstitution(request:LearningStatisticRequest):Boolean!
	"""导出单个学员学习日志
		@param request
		@return
	"""
	createExportLearningLogTask(request:LearningStatisticRequest):Boolean!
	"""导出单个学员学习日志
		为渠道商提供接口
		@param request
		@return
	"""
	createExportLearningLogTaskForChannelVendor(request:LearningStatisticRequest):Boolean!
	"""导出单个学员学习日志
		为参训单位提供接口
		@param request
		@return
	"""
	createExportLearningLogTaskForParticipatingUnit(request:LearningStatisticRequest):Boolean!
	"""导出单个学员学习日志
		为机构提供接口
		@param request
		@return
	"""
	createExportLearningLogTaskForTrainingInstitution(request:LearningStatisticRequest):Boolean!
	"""导出答题统计
		@param answerQuestionStatisticQueryParamsDTO
		@return
	"""
	exportAnswerQuestionStatistic(answerQuestionStatisticQueryParamsDTO:AnswerQuestionStatisticQueryParamsDTO):Boolean!
	"""导出课程维度评价统计
		@param request
		@return
	"""
	exportCourseAppraiseStatistic(request:CourseAppraiseStatisticRequest):Boolean!
	"""导出课程维度评价统计
		为课件供应商提供接口
		@param request
		@return
	"""
	exportCourseAppraiseStatisticForCoursewareSupplier(request:CourseAppraiseStatisticRequest):Boolean!
	"""导出课程维度评价统计
		为机构提供接口
		@param request
		@return
	"""
	exportCourseAppraiseStatisticForTrainingInstitution(request:CourseAppraiseStatisticRequest):Boolean!
	"""导出学习统计时间直方图
		为渠道商提供接口
		@return
	"""
	exportLearningDateHistogramForChannelVendor(dateHistogramParam:LearningStatisticDateHistogramParamDTO):Boolean!
	"""导出学习统计时间直方图
		为课件供应商提供接口
		@return
	"""
	exportLearningDateHistogramForCoursewareSupplier(dateHistogramParam:LearningStatisticDateHistogramParamDTO):Boolean!
	"""导出学习统计时间直方图
		为参训单位提交接口
		@return
	"""
	exportLearningDateHistogramForParticipatingUnit(dateHistogramParam:LearningStatisticDateHistogramParamDTO):Boolean!
	"""导出学习统计时间直方图
		为机构提供接口
		@return
	"""
	exportLearningDateHistogramForTrainingInstitution(dateHistogramParam:LearningStatisticDateHistogramParamDTO):Boolean!
	"""导出开通统计时间直方图
		为渠道商提供接口
		@return
	"""
	exportOpenDateHistogramForChannelVendor(dateHistogramParam:OpenStatisticDateHistogramParamDTO):Boolean!
	"""导出开通统计时间直方图
		为课件供应商提供接口
		@return
	"""
	exportOpenDateHistogramForCoursewareSupplier(dateHistogramParam:OpenStatisticDateHistogramParamDTO):Boolean!
	"""导出开通统计时间直方图
		为机构提供接口
		@return
	"""
	exportOpenDateHistogramForTrainingInstitution(dateHistogramParam:OpenStatisticDateHistogramParamDTO):Boolean!
	"""导出地区学习统计
		@param params
		@return
	"""
	exportRegionLearningStatistic(params:LearningStatisticRequest):Boolean!
	"""导出地区学习统计
		为机构提供接口
		@param params
		@return
	"""
	exportRegionLearningStatisticForTrainingInstitution(params:LearningStatisticRequest):Boolean!
	"""导出方案纬度学习统计
		@return
	"""
	exportSchemeLearningStatistic(params:LearningStatisticRequest):Boolean!
	"""导出方案纬度学习统计
		为机构提供接口
		@return
	"""
	exportSchemeLearningStatisticForTrainingInstitution(params:LearningStatisticRequest):Boolean!
	"""导出方案开通统计
		@param openStatisticQueryParamsDTO
		@return
	"""
	exportSchemeOpenStatistic(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):Boolean!
	"""导出方案开通统计
		为渠道供应商提供接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	exportSchemeOpenStatisticForChannelVendor(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):Boolean!
	"""导出方案开通统计
		为课件供应商提供接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	exportSchemeOpenStatisticForCoursewareSupplier(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):Boolean!
	"""导出方案开通统计
		为机构提供接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	exportSchemeOpenStatisticForTrainingInstitution(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):Boolean!
	"""导出机构维度课程评价统计
		@param request
		@return
	"""
	exportTrainingInstitutionCourseAppraiseStatistic(request:TrainingInstitutionCourseAppraiseStatisticRequest):Boolean!
	"""导出机构纬度学习统计
		@return
	"""
	exportTrainingInstitutionLearningStatistic(params:LearningStatisticRequest):Boolean!
	"""导出开通统计
		@param openStatisticQueryParamsDTO
		@return
	"""
	exportTrainingInstitutionOpenStatistic(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):Boolean!
	"""导出机构开通统计
		为渠道商提供接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	exportTrainingInstitutionOpenStatisticForChannelVendor(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):Boolean!
	"""导出机构开通统计
		为机构提供接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	exportTrainingInstitutionOpenStatisticForTrainingInstitution(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):Boolean!
	"""导出学习统计
		@param request
		@return
	"""
	exportUserLearningStatistic(request:LearningStatisticRequest):Boolean!
	"""导出学习统计
		为渠道商提供接口
		@param request
		@return
	"""
	exportUserLearningStatisticForChannelVendor(request:LearningStatisticRequest):Boolean!
	"""导出学习统计
		为参训单位提供接口
		@param request
		@return
	"""
	exportUserLearningStatisticForParticipatingUnit(request:LearningStatisticRequest):Boolean!
	"""导出学习统计
		为机构提供接口
		@param request
		@return
	"""
	exportUserLearningStatisticForTrainingInstitution(request:LearningStatisticRequest):Boolean!
	"""导出工种学习统计
		@param request
		@return
	"""
	exportWorkTypeLearningStatistic(request:LearningStatisticRequest):Boolean!
	"""导出工种学习统计
		为课件供应商提供接口
		@param request
		@return
	"""
	exportWorkTypeLearningStatisticForCoursewareSupplier(request:LearningStatisticRequest):Boolean!
	"""导出工种学习统计
		为参训单位提供接口
		@param request
		@return
	"""
	exportWorkTypeLearningStatisticForParticipatingUnit(request:LearningStatisticRequest):Boolean!
	"""导出工种学习统计
		为机构提供接口
		@param request
		@return
	"""
	exportWorkTypeLearningStatisticForTrainingInstitution(request:LearningStatisticRequest):Boolean!
	"""获取学习过程日志记录
		@return
	"""
	findLearningProcessLog(page:Page,userId:String,schemeId:String):LearningProcessCourseInfoDtoPage @page(for:"LearningProcessCourseInfoDto")
	"""获取用户方案学习详情
		@param userId
		@param schemeId
		@return
	"""
	getLearningStatisticDetail(userId:String,schemeId:String):LearningStatisticDTO
	"""根据条件获取答题统计合计结果
		@param answerQuestionStatisticQueryParamsDTO
		@return
	"""
	getTotalAnswerQuestionStatistic(answerQuestionStatisticQueryParamsDTO:AnswerQuestionStatisticQueryParamsDTO):TotalAnswerQuestionStatisticDTO
	"""获取开通数量统计，忽略金额
		@param openStatisticQueryParamsDTO
		@return
	"""
	getTotalOpenCount(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):TotalOpenCountStatisticDTO @NotAuthenticationRequired
	"""获取开通统计合计结果
		@param openStatisticQueryParamsDTO
		@return
	"""
	getTotalOpenStatistic(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):TotalOpenStatisticDTO @NotAuthenticationRequired
	"""获取开通统计合计结果
		为渠道商提供接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	getTotalOpenStatisticForChannelVendor(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):TotalOpenStatisticDTO
	"""获取开通统计合计结果
		为渠道商提供不强制设置上下文接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	getTotalOpenStatisticForChannelVendorWithoutContext(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):TotalOpenStatisticDTO
	"""获取开通统计合计结果
		为课件供应商提供接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	getTotalOpenStatisticForCoursewareSupplier(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):TotalOpenStatisticDTO
	"""获取开通统计合计结果
		为机构提供接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	getTotalOpenStatisticForTrainingInstitution(openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):TotalOpenStatisticDTO
	"""获取机构维度课程评价统计
		@param trainingInstitutionId
		@return
	"""
	getTrainingInstitutionCourseAppraiseStatistic(trainingInstitutionId:String):TrainingInstitutionCourseAppraiseStatisticDTO
	"""获取用户练习作答统计
		@param userId
		@param schemeId
		@return
	"""
	getUserPracticeChart(userId:String,schemeId:String,practiceLearningId:String):LearningStatisticUserPracticeChartDTO
	"""获取学习统计时间直方图
		为渠道商提供接口
		@return
	"""
	learningDateHistogramForChannelVendor(dateHistogramParam:LearningStatisticDateHistogramParamDTO):DateHistogramDTO
	"""获取学习统计时间直方图
		为课件供应商提供接口
		@return
	"""
	learningDateHistogramForCoursewareSupplier(dateHistogramParam:LearningStatisticDateHistogramParamDTO):DateHistogramDTO
	"""获取学习统计时间直方图
		为课件供应商提供接口
		@return
	"""
	learningDateHistogramForParticipatingUnit(dateHistogramParam:LearningStatisticDateHistogramParamDTO):DateHistogramDTO
	"""获取学习统计时间直方图
		为机构提供接口
		@return
	"""
	learningDateHistogramForTrainingInstitution(dateHistogramParam:LearningStatisticDateHistogramParamDTO):DateHistogramDTO
	"""获取用户方案内所有课程的学习进度
		@param userId
		@param schemeId
		@return
	"""
	listUserCourseLearningSchedule(userId:String,schemeId:String,courseLearningId:String):[LearningStatisticUserCourseLearningScheduleDTO]
	"""加载系统最后一次执行评价定时器的时间
		数据来源：评价定时器执行日志表
		@return
	"""
	loadStatisticDataUpdateTime:String
	"""根据学习统计查询条件督学符合条件的用户
		@param request
		@param monitorType
		@return
	"""
	monitorStudy(request:LearningStatisticRequest,monitorType:MonitorTypeEnum):Boolean!
	"""根据学习统计查询条件督学符合条件的用户
		渠道商
		@param request
		@param monitorType
		@return
	"""
	monitorStudyForChannelVendor(request:LearningStatisticRequest,monitorType:MonitorTypeEnum):Boolean!
	"""根据学习统计查询条件督学符合条件的用户
		参训单位
		@param request
		@param monitorType
		@return
	"""
	monitorStudyForParticipatingUnit(request:LearningStatisticRequest,monitorType:MonitorTypeEnum):Boolean!
	"""根据学习统计查询条件督学符合条件的用户
		机构
		@param request
		@param monitorType
		@return
	"""
	monitorStudyForTrainingInstitution(request:LearningStatisticRequest,monitorType:MonitorTypeEnum):Boolean!
	"""获取开通统计时间直方图
		为渠道商提供接口
		@return
	"""
	openDateHistogramForChannelVendor(dateHistogramParam:OpenStatisticDateHistogramParamDTO):DateHistogramDTO
	"""获取开通统计时间直方图
		为课件供应商提供接口
		@return
	"""
	openDateHistogramForCoursewareSupplier(dateHistogramParam:OpenStatisticDateHistogramParamDTO):DateHistogramDTO
	"""获取开通统计时间直方图
		为机构提供接口
		@return
	"""
	openDateHistogramForTrainingInstitution(dateHistogramParam:OpenStatisticDateHistogramParamDTO):DateHistogramDTO
	"""根据条件获取答题统计分页
		@param page
		@param answerQuestionStatisticQueryParamsDTO
		@return
	"""
	pageAnswerQuestionStatistic(page:Page,answerQuestionStatisticQueryParamsDTO:AnswerQuestionStatisticQueryParamsDTO):AnswerQuestionStatisticDTOPage @page(for:"AnswerQuestionStatisticDTO")
	"""分页获取课程维度评价统计
		@param page
		@param request
		@return
	"""
	pageCourseAppraiseStatistic(page:Page,request:CourseAppraiseStatisticRequest):CourseAppraiseStatisticDTOPage @page(for:"CourseAppraiseStatisticDTO") @NotAuthenticationRequired
	"""分页获取课程维度评价统计
		为课件供应商提供接口
		@param page
		@param request
		@return
	"""
	pageCourseAppraiseStatisticForCoursewareSupplier(page:Page,request:CourseAppraiseStatisticRequest):CourseAppraiseStatisticDTOPage @page(for:"CourseAppraiseStatisticDTO")
	"""分页获取课程维度评价统计
		为机构提供接口
		@param page
		@param request
		@return
	"""
	pageCourseAppraiseStatisticForTrainingInstitution(page:Page,request:CourseAppraiseStatisticRequest):CourseAppraiseStatisticDTOPage @page(for:"CourseAppraiseStatisticDTO")
	"""课程人气统计
		@param page
		@param paramsDTO
		@return
	"""
	pageHotCourseChooseStatistic(page:Page,paramsDTO:HotCourseChooseStatisticQueryParamsDTO):HotCourseChooseStatisticDTOPage @page(for:"HotCourseChooseStatisticDTO") @NotAuthenticationRequired
	"""地区学习统计
		@param request
		@return
	"""
	pageRegionLearningStatistic(page:Page,request:LearningStatisticRequest):RegionLearningStatisticResponsePage @page(for:"RegionLearningStatisticResponse")
	"""地区学习统计
		为机构提供接口
		@param request
		@return
	"""
	pageRegionLearningStatisticForTrainingInstitution(page:Page,request:LearningStatisticRequest):RegionLearningStatisticResponsePage @page(for:"RegionLearningStatisticResponse")
	"""方案维度课程评价统计
		@param page
		@param request
		@return
	"""
	pageSchemeCourseAppraiseStatistic(page:Page,request:SchemeCourseAppraiseStatisticRequest):SchemeCourseAppraiseStatisticDTOPage @page(for:"SchemeCourseAppraiseStatisticDTO") @NotAuthenticationRequired
	"""方案维度课程评价统计
		为渠道商提供接口
		@param page
		@param request
		@return
	"""
	pageSchemeCourseAppraiseStatisticForChannelVendor(page:Page,request:SchemeCourseAppraiseStatisticRequest):SchemeCourseAppraiseStatisticDTOPage @page(for:"SchemeCourseAppraiseStatisticDTO")
	"""方案维度课程评价统计
		为机构提供接口
		@param page
		@param request
		@return
	"""
	pageSchemeCourseAppraiseStatisticForTrainingInstitution(page:Page,request:SchemeCourseAppraiseStatisticRequest):SchemeCourseAppraiseStatisticDTOPage @page(for:"SchemeCourseAppraiseStatisticDTO")
	"""方案学习统计，带合计行（方案维度）
		@param request
		@return
	"""
	pageSchemeLearningStatistic(page:Page,request:LearningStatisticRequest):SchemeLearningStatisticResponsePage @page(for:"SchemeLearningStatisticResponse")
	"""方案学习统计，带合计行（方案维度）
		为机构提供接口
		@param request
		@return
	"""
	pageSchemeLearningStatisticForTrainingInstitution(page:Page,request:LearningStatisticRequest):SchemeLearningStatisticResponsePage @page(for:"SchemeLearningStatisticResponse")
	"""获取开通统计分页
		@param page
		@param openStatisticQueryParamsDTO
		@return
	"""
	pageSchemeOpenStatistic(page:Page,openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):SchemeOpenStatisticDTOPage @page(for:"SchemeOpenStatisticDTO")
	"""获取班级开通统计分页
		为渠道供应商提供接口
		@param page
		@param openStatisticQueryParamsDTO
		@return
	"""
	pageSchemeOpenStatisticForChannelVendor(page:Page,openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):SchemeOpenStatisticDTOPage @page(for:"SchemeOpenStatisticDTO")
	"""获取开通统计分页
		为课件供应商提供接口
		@param page
		@param openStatisticQueryParamsDTO
		@return
	"""
	pageSchemeOpenStatisticForCoursewareSupplier(page:Page,openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):SchemeOpenStatisticDTOPage @page(for:"SchemeOpenStatisticDTO")
	"""获取开通统计分页
		为机构提供接口
		@param page
		@param openStatisticQueryParamsDTO
		@return
	"""
	pageSchemeOpenStatisticForTrainingInstitution(page:Page,openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):SchemeOpenStatisticDTOPage @page(for:"SchemeOpenStatisticDTO")
	"""分页获取机构维度课程评价统计
		@param page
		@param request
		@return
	"""
	pageTrainingInstitutionCourseAppraiseStatistic(page:Page,request:TrainingInstitutionCourseAppraiseStatisticRequest):TrainingInstitutionCourseAppraiseStatisticDTOPage @page(for:"TrainingInstitutionCourseAppraiseStatisticDTO") @NotAuthenticationRequired
	"""机构学习统计
		@param request
		@return
	"""
	pageTrainingInstitutionLearningStatistic(page:Page,request:LearningStatisticRequest):TrainingInstitutionLearningStatisticResponsePage @page(for:"TrainingInstitutionLearningStatisticResponse") @NotAuthenticationRequired
	"""获取机构开通统计分页
		@param page
		@param openStatisticQueryParamsDTO
		@return
	"""
	pageTrainingInstitutionOpenStatistic(page:Page,openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):TrainingInstitutionOpenStatisticDTOPage @page(for:"TrainingInstitutionOpenStatisticDTO")
	"""获取机构开通统计分页
		为渠道商提供接口
		@param page
		@param openStatisticQueryParamsDTO
		@return
	"""
	pageTrainingInstitutionOpenStatisticForChannelVendor(page:Page,openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):TrainingInstitutionOpenStatisticDTOPage @page(for:"TrainingInstitutionOpenStatisticDTO")
	"""获取机构开通统计分页
		为机构提供接口
		@param page
		@param openStatisticQueryParamsDTO
		@return
	"""
	pageTrainingInstitutionOpenStatisticForTrainingInstitution(page:Page,openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):TrainingInstitutionOpenStatisticDTOPage @page(for:"TrainingInstitutionOpenStatisticDTO")
	"""获取学习统计分页
		@param page
		@param request
		@return
	"""
	pageUserLearningStatistic(page:Page,request:LearningStatisticRequest):LearningStatisticDTOPage @page(for:"LearningStatisticDTO")
	"""获取学习统计分页
		为渠道商提供接口
		@param page
		@param request
		@return
	"""
	pageUserLearningStatisticForChannelVendor(page:Page,request:LearningStatisticRequest):LearningStatisticDTOPage @page(for:"LearningStatisticDTO")
	"""
		获取学习统计分页
		为参训单位提供接口
		@param page
		@param request
		@return
	"""
	pageUserLearningStatisticForParticipatingUnit(page:Page,request:LearningStatisticRequest):LearningStatisticDTOPage @page(for:"LearningStatisticDTO")
	"""获取学习统计分页
		为机构提供接口
		@param page
		@param request
		@return
	"""
	pageUserLearningStatisticForTrainingInstitution(page:Page,request:LearningStatisticRequest):LearningStatisticDTOPage @page(for:"LearningStatisticDTO")
	"""用户登录日志
		@param page
		@param request
		@return
	"""
	pageUserLoginLog(page:Page,request:UserLoginLogRequest):UserLoginLogResponsePage @page(for:"UserLoginLogResponse")
	"""工种学习统计（工种维度）
		@param request
		@return
	"""
	pageWorkTypeLearningStatistic(page:Page,request:LearningStatisticRequest):WorkTypeLearningStatisticDTOPage @page(for:"WorkTypeLearningStatisticDTO")
	"""工种学习统计（工种维度）
		为课件供应商提供接口
		@param request
		@return
	"""
	pageWorkTypeLearningStatisticForCoursewareSupplier(page:Page,request:LearningStatisticRequest):WorkTypeLearningStatisticDTOPage @page(for:"WorkTypeLearningStatisticDTO")
	"""工种学习统计（工种维度）
		为参训单位提供接口
		@param request
		@return
	"""
	pageWorkTypeLearningStatisticForParticipatingUnit(page:Page,request:LearningStatisticRequest):WorkTypeLearningStatisticDTOPage @page(for:"WorkTypeLearningStatisticDTO")
	"""工种学习统计（工种维度）
		为机构提供接口
		@param request
		@return
	"""
	pageWorkTypeLearningStatisticForTrainingInstitution(page:Page,request:LearningStatisticRequest):WorkTypeLearningStatisticDTOPage @page(for:"WorkTypeLearningStatisticDTO")
	"""分页查询每个工种报名数
		为渠道商提供接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	pageWorkTypeOpenStatisticForChannelVendor(page:Page,openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):WorkTypeOpenStatisticDTOPage @page(for:"WorkTypeOpenStatisticDTO")
	"""分页查询工种报名数
		为课件供应商提供接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	pageWorkTypeOpenStatisticForCoursewareSupplier(page:Page,openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):WorkTypeOpenStatisticDTOPage @page(for:"WorkTypeOpenStatisticDTO")
	"""分页查询每个工种报名数
		为机构提供接口
		@param openStatisticQueryParamsDTO
		@return
	"""
	pageWorkTypeOpenStatisticForTrainingInstitution(page:Page,openStatisticQueryParamsDTO:OpenStatisticQueryParamsDTO):WorkTypeOpenStatisticDTOPage @page(for:"WorkTypeOpenStatisticDTO")
	"""方案sku学习统计
		@param request
		@return
	"""
	schemeSkuLearningStatistic(request:SchemeSkuLearningStatisticRequest):[SchemeSkuLearniningStatisticsResponse]
	"""统计在线学习人数专业分布
		@param param
		@return
	"""
	statisticUserOnlineMajorDistribute(param:DashboardParam):[UserOnLineMajorDistributeResponse]
	"""统计在线学习人数终端分布
		@param param
		@return
	"""
	statisticUserOnlineTerminalDistribute(param:DashboardParam):[UserOnLineTerminalDistributeResponse]
	"""统计在线学习人数单位分布
		@param param
		@return
	"""
	statisticUserOnlineUnitDistribute(param:DashboardParam):[UserOnLineUnitDistributeResponse]
	"""课程维度评价统计合计行，综合得分
		@param request
		@return
	"""
	totalCourseAverage(request:CourseAppraiseStatisticRequest):CourseAppraiseStatisticTotalRowDTO
}
"""课程维度评价统计查询条件
	Author:FangKunSen
	Time:2021-05-07,10:55
"""
input CourseAppraiseStatisticRequest @type(value:"com.fjhb.btpx.integrative.gateway.graphql.request.CourseAppraiseStatisticRequest") {
	"""课程id集合"""
	courseIds:[String]
	"""工种id路径集合"""
	workTypeIdPaths:[String]
	"""课程名"""
	courseName:String
	"""机构Id集合"""
	trainingInstitutionIdList:[String]
	"""课件供应商id"""
	coursewareSupplierIdList:[String]
	"""微服务上下文"""
	microContext:MicroContext
	"""排序字段"""
	sortField:String
}
"""方案维度课程评价统计
	Author:FangKunSen
	Time:2021-05-07,15:12
"""
input SchemeCourseAppraiseStatisticRequest @type(value:"com.fjhb.btpx.integrative.gateway.graphql.request.SchemeCourseAppraiseStatisticRequest") {
	"""方案id"""
	schemeIds:[String]
	"""方案名"""
	schemeName:String
	"""工种id路径集合"""
	workTypeIdPaths:[String]
	"""机构Id集合"""
	trainingInstitutionIdList:[String]
	"""渠道商ID集合"""
	channelVendorIdList:[String]
	"""课件供应商id"""
	coursewareSupplierIdList:[String]
	"""综合评分排序
		默认不排序
	"""
	comprehensiveAppraiseSoft:QueryParamSortEnum
	"""排序字段"""
	sortField:String
}
"""机构维度的课程统计查询条件
	Author:FangKunSen
	Time:2021-05-07,14:46
"""
input TrainingInstitutionCourseAppraiseStatisticRequest @type(value:"com.fjhb.btpx.integrative.gateway.graphql.request.TrainingInstitutionCourseAppraiseStatisticRequest") {
	"""机构Id集合"""
	trainingInstitutionIdList:[String]
	"""渠道商ID集合"""
	channelVendorIdList:[String]
	"""课件供应商id"""
	coursewareSupplierIdList:[String]
}
"""@author: eleven
	@date: 2020/6/11
"""
input UserLoginLogRequest @type(value:"com.fjhb.btpx.integrative.gateway.graphql.request.UserLoginLogRequest") {
	"""用户id"""
	userId:String
}
"""首页统计数据统计参数
	@author: eleven
	@date: 2020/4/18
"""
input DashboardParam @type(value:"com.fjhb.btpx.integrative.service.exam.dto.param.DashboardParam") {
	"""作答提交时间起 >="""
	submitAnswerTimeBegin:DateTime
	"""作答提交时间止 <="""
	submitAnswerTimeEnd:DateTime
	"""机构id"""
	trainingInstitutionId:String
	"""用户id集合"""
	userIdList:[String]
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""答题统计查询参数
	Author:FangKunSen
	Time:2020-03-04,14:13
"""
input AnswerQuestionStatisticQueryParamsDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.param.AnswerQuestionStatisticQueryParamsDTO") {
	"""试题题目关键字"""
	questionContent:String
	"""试题类型
		@see PreExamQuestionCategory
	"""
	type:String
	"""试题题型标签id"""
	questionType:QuestionType
	"""考纲标签id"""
	knowledgeTagIds:[String]
	"""对题率 起"""
	rightRateStart:String
	"""对题率 止"""
	rightRateEnd:String
	"""创建时间 起"""
	createTimeStart:DateTime
	"""创建时间 止"""
	createTimeEnd:DateTime
}
"""热门课程查询条件dto
	Author:FangKunSen
	Time:2020-06-10,15:48
"""
input HotCourseChooseStatisticQueryParamsDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.param.HotCourseChooseStatisticQueryParamsDTO") {
	"""课程id"""
	courseId:String
	"""网校机构id"""
	schoolUnitId:String
	"""各地区的单位id
		例如福州市人设单位：2ca4cd0772967f5d0172a347ee0e1hs1
		错误入参：/350100
	"""
	regionId:String
	"""年度集合"""
	years:[Int]
	"""排序依据"""
	sort:SortByEnum
	"""排序：0不排序，1升序，2降序"""
	sortOrder:Int!
	"""子排序依据（子排序会在主排序依据字段相等的情况下，对相等的若干个记录进行二次排序）"""
	subSort:SortByEnum
	"""子排序：0不排序，1升序，2降序"""
	subSortOrder:Int!
}
"""学习直方图统计查询条件
	<AUTHOR>
	@version 1.0
	@date 2021/7/17 14:51
"""
input LearningStatisticDateHistogramParamDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.param.LearningStatisticDateHistogramParamDTO") {
	"""合格时间  查询时间范围"""
	qualifiedTime:TimeRegionRequest
	"""机构ID"""
	trainingInstitutionIdList:[String]
	"""课件供应商id"""
	coursewareSupplierIdList:[String]
	"""渠道商id"""
	channelVendorIdList:[String]
	"""时间单位枚举
		YEARS,
		<p>
		MONTHS,
		<p>
		DAYS,
		<p>
		HOURS
	"""
	timeUnit:TimeUnit
}
"""培训学习统计查询参数
	Author:FangKunSen
	Time:2020-03-13,10:21
"""
input LearningStatisticRequest @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.param.LearningStatisticRequest") {
	"""机构ID"""
	trainingInstitutionId:String
	"""机构ID集合"""
	trainingInstitutionIdList:[String]
	"""机构名称"""
	trainingInstitutionName:String
	"""课件供应商id集合"""
	coursewareSupplierIdList:[String]
	"""渠道商id集合"""
	channelVendorIdList:[String]
	"""参训单位id集合"""
	participatingUnitIdList:[String]
	"""用户id"""
	userId:String
	"""用户id"""
	userIdList:[String]
	"""手机号码"""
	phone:String
	"""用户名"""
	userName:String
	"""身份证号"""
	uniqueData:String
	"""地区路径"""
	regionPath:String
	"""人员类别"""
	userCategory:String
	"""培训方案id"""
	schemeIds:[String]
	"""方案名称，当该字段不为空时，前端入参方案id无效，反之有效"""
	schemeName:String
	"""适用人群"""
	suitableCrowNames:[String]
	"""期数ids"""
	issueIds:[String]
	"""开通成功时间 起"""
	openTimeStart:DateTime
	"""开通成功时间 止"""
	openTimeEnd:DateTime
	"""合格时间 起"""
	passTimeStart:DateTime
	"""合格时间 止"""
	passTimeEnd:DateTime
	"""考试是否合格"""
	examQualified:Boolean
	"""是否已考试"""
	hasDoExam:Boolean
	"""班级总体考核结果"""
	qualified:Boolean
	"""学习状态"""
	studyState:StudyState
	"""学习进度（起）"""
	scheduleStart:String
	"""学习进度（止）"""
	scheduleEnd:String
	"""报名方式
		PRESENT为导入开通；COLLECTIVE为集体缴费；其他为自主报名
	"""
	registrationWay:PaymentChannelTypeEnum
	"""培训工种类别/培训工种id path
		用于培训类别联合工种多条件查询
	"""
	workTypeIdPathList:[String]
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""开通直方图统计查询条件
	<AUTHOR>
	@version 1.0
	@date 2021/7/17 14:51
"""
input OpenStatisticDateHistogramParamDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.param.OpenStatisticDateHistogramParamDTO") {
	"""订单状态查询条件"""
	orderStatusList:[OrderStatus]
	"""订单创建时间 查询时间范围"""
	orderCreateTime:TimeRegionRequest
	"""订单完成时间 查询时间范围"""
	orderCompleteTime:TimeRegionRequest
	"""机构ID"""
	trainingInstitutionIdList:[String]
	"""课件供应商id"""
	coursewareSupplierIdList:[String]
	"""渠道商id"""
	channelVendorIdList:[String]
	"""时间单位枚举
		YEARS,
		<p>
		MONTHS,
		<p>
		DAYS,
		<p>
		HOURS
	"""
	timeUnit:TimeUnit
}
"""开通统计查询参数
	Author:FangKunSen
	Time:2020-03-04,14:05
"""
input OpenStatisticQueryParamsDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.param.OpenStatisticQueryParamsDTO") {
	"""机构ID"""
	trainingInstitutionIdList:[String]
	"""机构名称"""
	trainingInstitutionName:String
	"""课件供应商id"""
	coursewareSupplierIdList:[String]
	"""渠道商id"""
	channelVendorIdList:[String]
	"""参训单位ID"""
	participatingUnitIdList:[String]
	"""培训方案名称"""
	schemeName:String
	"""方案id"""
	schemeIds:[String]
	"""期数id"""
	issueId:String
	"""期数id集合"""
	issueIds:[String]
	"""适用人群"""
	suitableCrowNames:[String]
	"""期别学习时间"""
	stageLearningTime:TimeRegionRequest
	"""开通成功时间"""
	openTime:TimeRegionRequest
	"""退款成功时间"""
	refundTime:TimeRegionRequest
	"""工种id list"""
	workTypeIdList:[String]
	"""培训工种类别/培训工种id path
		用于培训类别联合工种多条件查询
	"""
	workTypeIdPathList:[String]
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""培训学习统计查询参数
	Author:FangKunSen
	Time:2020-03-13,10:21
"""
input SchemeSkuLearningStatisticRequest @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.param.SchemeSkuLearningStatisticRequest") {
	"""单位id"""
	unitId:String
	"""机构ID"""
	trainingInstitutionId:String
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""时间范围
	<AUTHOR>
	@date 2020/5/3116:42
"""
input TimeRegionRequest @type(value:"com.fjhb.btpx.support.dto.TimeRegionRequest") {
	"""开始时间"""
	startTime:DateTime
	"""结束时间"""
	endTime:DateTime
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input DataRouterIdentity @type(value:"com.fjhb.micro.context.v1.DataRouterIdentity") {
	dataPlatformVersionId:String
	dataProjectId:String
}
input HttpIdentity @type(value:"com.fjhb.micro.context.v1.HttpIdentity") {
	ip:String
	domain:String
	requestUrl:String
}
input MicroContext @type(value:"com.fjhb.micro.context.v1.MicroContext") {
	sequenceNo:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	servicerProvider:ServicerProvider
	userIdentity:UserIdentity
	dataRouterIdentity:DataRouterIdentity
	httpIdentity:HttpIdentity
}
input ServicerProvider @type(value:"com.fjhb.micro.context.v1.ServicerProvider") {
	unitId:String
	servicerType:Int!
	servicerId:String
}
input UserIdentity @type(value:"com.fjhb.micro.context.v1.UserIdentity") {
	accountId:String
	rootAccountId:String
	accountType:Int
	userId:String
}
"""课程维度评价统计
	Author:FangKunSen
	Time:2021-05-07,10:52
"""
type CourseAppraiseStatisticDTO @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CourseAppraiseStatisticDTO") {
	"""课程id"""
	id:String
	"""课程名称"""
	courseName:String
	"""课程关联工种路径：/类别/类别/工种"""
	workTypeIdPath:[String]
	"""综合评分"""
	average:Double
	"""综合评分1星命中数"""
	one:Long
	"""综合评分2星命中数"""
	two:Long
	"""综合评分3星命中数"""
	three:Long
	"""综合评分4星命中数"""
	four:Long
	"""综合评分5星命中数"""
	five:Long
	"""课件供应商Id
		v1.7.0.0
	"""
	coursewareSupplierId:String
	"""课件供应商名称
		v1.7.0.0
	"""
	coursewareSupplierName:String
	"""学时数
		v1.7.0.0
	"""
	period:BigDecimal
}
"""课程维度评价统计合计行
	Author:FangKunSen
	Time:2021-05-07,15:54
"""
type CourseAppraiseStatisticTotalRowDTO @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CourseAppraiseStatisticTotalRowDTO") {
	"""综合得分"""
	average:Double!
}
"""方案维度课程评价统计
	Author:FangKunSen
	Time:2021-05-07,15:08
"""
type SchemeCourseAppraiseStatisticDTO @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.SchemeCourseAppraiseStatisticDTO") {
	"""方案id"""
	schemeId:String
	"""方案名称"""
	schemeName:String
	"""机构ID"""
	trainingInstitutionId:String
	"""机构名称"""
	trainingInstitutionName:String
	"""课件供应商id"""
	coursewareSupplierId:String
	"""课件供应商名称"""
	coursewareSupplierName:String
	"""综合评分"""
	average:Double
	"""综合评分1星命中数"""
	one:Long
	"""综合评分2星命中数"""
	two:Long
	"""综合评分3星命中数"""
	three:Long
	"""综合评分4星命中数"""
	four:Long
	"""综合评分5星命中数"""
	five:Long
}
"""学习统计（方案维度）dto
	Author:FangKunSen
	Time:2020-05-30,11:24
"""
type SchemeLearningStatisticResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.SchemeLearningStatisticResponse") {
	"""方案id"""
	schemeId:String
	"""方案名称"""
	schemeName:String
	"""适用人群"""
	suitableCrowNames:[String]
	"""培训工种类别名称"""
	trainingTypeNamePath:String
	"""培训工种名称"""
	workTypeName:String
	"""机构Id v1.7.0.0"""
	trainingInstitutionId:String
	"""机构名称 v1.7.0.0"""
	trainingInstitutionName:String
	"""课件供应商Id v1.7.0.0"""
	coursewareSupplierId:String
	"""课件供应商名称 v1.7.0.0"""
	coursewareSupplierName:String
	"""渠道商ID v1.7.0.0"""
	channelVendorId:String
	"""渠道商名称 v1.7.0.0"""
	channelVendorName:String
	"""净报名数量 (总数-退班的)"""
	openUserCount:Int!
	"""未学习人数（对应培训班学习进度=0）"""
	waitStudyCount:Int!
	"""学习中人数（0<学习进度<=100之间）"""
	studyingCount:Int!
	"""已学完人数（对应培训人员在对应班级学习进度=100%人员）"""
	studyFinishCount:Int!
	"""已考试人数。"""
	examedUserCount:Int!
	"""合格人数
		没有考核就直接合格
	"""
	qualifiedCount:Int!
	"""是否有课程学习方式"""
	hasCourseLearning:Boolean!
	"""是否有考试学习方式"""
	hasExamLearning:Boolean!
	"""已考核通过获得学时数"""
	finishAssessGrade:BigDecimal
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""单位维度课程评价统计
	Author:FangKunSen
	Time:2021-05-07,14:59
"""
type TrainingInstitutionCourseAppraiseStatisticDTO @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.TrainingInstitutionCourseAppraiseStatisticDTO") {
	"""机构ID"""
	trainingInstitutionId:String
	"""机构名称"""
	trainingInstitutionName:String
	"""综合评分"""
	average:Double
	"""综合评分1星命中数"""
	one:Long
	"""综合评分2星命中数"""
	two:Long
	"""综合评分3星命中数"""
	three:Long
	"""综合评分4星命中数"""
	four:Long
	"""综合评分5星命中数"""
	five:Long
}
"""@author: eleven
	@date: 2020/6/11
"""
type UserLoginLogResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.UserLoginLogResponse") {
	"""用户id"""
	userId:String
	"""登录时间"""
	loginTime:String
	"""登录ip"""
	loginIP:String
}
"""课程信息
	<AUTHOR>
"""
type LearningProcessCourseInfoDto @type(value:"com.fjhb.btpx.integrative.service.anticheat.dto.learningProcessLog.LearningProcessCourseInfoDto") {
	"""用户ID"""
	userId:String
	"""课程ID"""
	courseId:String
	"""课程"""
	courseName:String
	"""课程下的课件分组"""
	coursewareList:[LearningProcessCoursewareInfoDto]
}
"""课件信息
	<AUTHOR>
"""
type LearningProcessCoursewareInfoDto @type(value:"com.fjhb.btpx.integrative.service.anticheat.dto.learningProcessLog.LearningProcessCoursewareInfoDto") {
	"""课程ID"""
	courseId:String
	"""课件ID"""
	coursewareId:String
	"""课件"""
	coursewareName:String
	"""进入学习时间"""
	enterTime:String
	"""播放时长"""
	playTimeLength:Int!
	"""播放时长"""
	playTime:String
	"""学习进度"""
	schedule:Double!
	"""具体的拍摄记录"""
	captureList:[LearningProcessFsCaptureDto]
}
"""拍摄信息
	<AUTHOR>
"""
type LearningProcessFsCaptureDto @type(value:"com.fjhb.btpx.integrative.service.anticheat.dto.learningProcessLog.LearningProcessFsCaptureDto") {
	"""拍摄时间"""
	captureTimeLength:Long!
	"""拍摄时间"""
	captureTime:String
	"""认证结果"""
	result:Boolean
	"""是否已操作"""
	operation:Boolean
}
"""sku属性的专用类，所有需要sku属性字段的统一继承该类，无需到处散落属性字段
	进行数据转换提取
	@author: eleven
	@date: 2020/3/27
"""
type SkuProperty @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.SkuProperty") {
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""Author:FangKunSen
	Time:2020-08-18,15:25
"""
type SkuValue @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.SkuValue") {
	"""年度"""
	year:Int
	"""培训类别 id路径"""
	trainingTypeIdPath:String
	"""培训类别"""
	trainingTypeName:String
	"""培训类别 名称路径"""
	trainingTypeNamePath:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeName:String
}
"""一键督学通知类型
	Author:FangKunSen
	Time:2021-04-21,14:50
"""
enum MonitorTypeEnum @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.MonitorTypeEnum") {
	"""根据学习情况动态通知"""
	STATIC
	"""学习通知"""
	STUDY
	"""考试通知"""
	EXAM
	"""购买通知"""
	BUY
}
"""排序依据
	Author:FangKunSen
	Time:2020-06-10,15:52
"""
enum SortByEnum @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.SortByEnum") {
	"""选课次数（人气）"""
	CHOOSE_TIMES
	"""建课时间"""
	CREATE_TIME
}
"""试题作答统计dto
	Author:FangKunSen
	Time:2020-03-04,14:42
"""
type AnswerQuestionStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.AnswerQuestionStatisticDTO") {
	"""试题id"""
	questionId:String
	"""试题题目"""
	questionContent:String
	"""考试类别"""
	examCategory:String
	"""专业"""
	major:String
	"""所属考纲id"""
	belongKnowledgeIds:[String]
	"""试题类型：真题、练习题、模拟题"""
	type:String
	"""试题题型"""
	questionType:String
	"""被作答次数"""
	answerTimes:Long!
	"""对题率"""
	rightRate:Double!
}
"""时间直方图统计结果
	<AUTHOR>
	@version 1.0
	@date 2021/7/17 14:52
"""
type DateHistogramDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.DateHistogramDTO") {
	"""时间单位"""
	timeUnit:TimeUnit
	"""统计结果元素"""
	histogram:[DateHistogramElementDTO]
	"""总计"""
	totalCount:Int
}
"""时间直方图单项结果
	<AUTHOR>
	@version 1.0
	@date 2021/7/17 14:52
"""
type DateHistogramElementDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.DateHistogramElementDTO") {
	"""时间
		switch (timeUnit) {
		case 'YEARS':
		result.date = year + '-01-01 00:00';
		break;
		case 'MONTHS':
		result.date = year + '-' + month + '-01 00:00';
		break;
		case 'DAYS':
		result.date = year + "-" + month + "-" + day + " 00:00";
		break;
		case 'HOURS':
		result.date = year + '-' + month + '-' + day + ' ' + hours + ":00";
		break;
		default:result.date=currentThis.qualifiedTime;
		}
	"""
	date:String
	"""总计"""
	count:Int
}
"""人气课程dto
	Author:FangKunSen
	Time:2020-06-10,15:40
"""
type HotCourseChooseStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.HotCourseChooseStatisticDTO") {
	"""课程id"""
	courseId:String
	"""课程名字"""
	courseName:String
	"""图片路径"""
	courseImagePath:String
	"""供应商id"""
	supplierId:String
	"""课时"""
	period:Double!
	"""教师id"""
	teacherId:String
	"""课程创建时间"""
	createTime:DateTime
	"""被成功选课次数"""
	beChooseTimes:Double!
	"""评分"""
	evaluate:Double!
}
"""培训学习统计dto
	Author:FangKunSen
	Time:2020-03-13,10:34
"""
type LearningStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.LearningStatisticDTO") {
	"""本条记录来源子订单号"""
	subOrderNo:String
	"""机构ID"""
	trainingInstitutionId:String
	"""机构名称"""
	trainingInstitutionName:String
	"""用户id"""
	userId:String
	"""用户名"""
	userName:String
	"""手机号"""
	phone:String
	"""用户唯一性值"""
	uniqueData:String
	"""用户的单位的地区"""
	region:Region
	"""工作单位名称 v1.7.0.0"""
	companyName:String
	"""所属人群 v1.7.0.0"""
	peoples:[String]
	"""方案id"""
	schemeId:String
	"""方案名字"""
	schemeName:String
	"""期数id"""
	issueId:String
	"""适用人群"""
	suitableCrowNames:[String]
	"""培训学时"""
	period:String
	"""报名成功时间"""
	reportSuccessTime:String
	"""商品购买金额"""
	commodityDealAmount:BigDecimal
	"""报名方式"""
	registrationWay:String
	"""物品状态
		valid 有效 freeze 冻结 invalid 无效
		@see UserGoodState#getValue()
	"""
	goodState:String
	"""是否启用培训班考核"""
	enabledAssess:Boolean!
	"""学习状态"""
	studyState:StudyState
	"""方案是否合格"""
	qualified:Boolean!
	"""方案合格时间"""
	qualifiedTime:String
	"""是否有课程学习方式"""
	hasCourseLearning:Boolean!
	"""课程学习方式id"""
	courseLearningId:String
	"""是否要求已选课程全部完成"""
	courseAssessSettingAllSelectedComplete:Boolean!
	"""课程学习考核要求完成进度"""
	courseAssessSettingCourseSchedule:Double!
	"""课程-课程数"""
	courseNumber:Int!
	"""课程-学习进度"""
	courseLearningProgress:Double!
	"""课程合格时间
		达到考核学时的最后一门课程完成时间
	"""
	coursePassTime:String
	"""是否有考试学习方式"""
	hasExamLearning:Boolean!
	"""考试学习方式id"""
	examLearningId:String
	"""练习学习方式id"""
	practiceLearningId:String
	"""考试考核要求成绩"""
	examAssessSettingScore:Double!
	"""考试次数"""
	examCount:Int!
	"""考试是否合格"""
	examQualified:Boolean!
	"""模拟卷-最高成绩"""
	analogExamHighestScore:Double!
	"""首次进入考试时间"""
	firstJoinExamTime:String
	"""数据最后更新时间"""
	updateTime:String
	"""课件供应商Id"""
	coursewareSupplierId:String
	"""课件供应商名称"""
	coursewareSupplierName:String
	"""渠道商ID"""
	channelVendorId:String
	"""渠道商名称"""
	channelVendorName:String
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""用户方案内课程的学习进度
	Author:FangKunSen
	Time:2020-03-18,10:54
"""
type LearningStatisticUserCourseLearningScheduleDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.LearningStatisticUserCourseLearningScheduleDTO") {
	"""用户id"""
	userId:String
	"""课程名字"""
	courseName:String
	"""总讲数"""
	totalLectureNumber:Int!
	"""已更新讲数"""
	replaceLectureNumber:Int!
	"""学习方案id"""
	schemeId:String
	"""课程id"""
	courseId:String
	"""学习进度"""
	schedule:Double!
	"""学习状态
		0/1/2，未学习/学习中/学习完成
		@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
	"""
	studyState:Int!
	"""最新学习时间"""
	lastStudyTime:DateTime
}
"""学习统计详情-用户练习作答数据统计图（包括练习与每日一练）
	Author:FangKunSen
	Time:2020-03-18,15:28
"""
type LearningStatisticUserPracticeChartDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.LearningStatisticUserPracticeChartDTO") {
	"""用户od"""
	userId:String
	"""方案id"""
	schemeId:String
	"""练习学习方式id"""
	practiceLearningId:String
	"""整体答题次数总和"""
	totalAnswerTimes:Double!
	"""整体对题率"""
	totalRightRate:Double!
	"""真题项"""
	realQuestion:UserPracticeChartSubItemDTO
	"""模拟题项"""
	simulationQuestion:UserPracticeChartSubItemDTO
	"""练习题项"""
	practiceQuestion:UserPracticeChartSubItemDTO
}
"""开通统计聚合对象
	Author:FangKunSen
	Time:2020-03-10,10:43
"""
type OpenStatisticAggregationDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.OpenStatisticAggregationDTO") {
	"""报名数"""
	openNumber:Long!
	"""取消报名数"""
	cancelOpenNumber:Long!
	"""净报名数"""
	realOpenNumber:Long!
	"""开通总金额"""
	totalAmount:Double!
	"""退款总金额"""
	refundTotalAmount:Double!
	"""净成交总金额"""
	realTotalAmount:Double!
}
"""学习统计（地区维度）dto
	Author:FangKunSen
	Time:2020-05-30,10:48
"""
type RegionLearningStatisticResponse @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.RegionLearningStatisticResponse") {
	"""地区code"""
	regionCode:String
	"""分组id"""
	id:String
	"""净报名数量 (总数-退班的)"""
	openUserCount:Int!
	"""未学习人数（对应培训班学习进度=0）"""
	waitStudyCount:Int!
	"""学习中人数（0<学习进度<=100之间）"""
	studyingCount:Int!
	"""已学完人数（对应培训人员在对应班级学习进度=100%人员）"""
	studyFinishCount:Int!
	"""已考试人数。"""
	examedUserCount:Int!
	"""合格人数
		没有考核就直接合格
	"""
	qualifiedCount:Int!
	"""是否有课程学习方式"""
	hasCourseLearning:Boolean!
	"""是否有考试学习方式"""
	hasExamLearning:Boolean!
	"""已考核通过获得学时数"""
	finishAssessGrade:BigDecimal
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""开通统计dto
	Author:FangKunSen
	Time:2020-03-09,14:03
"""
type SchemeOpenStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.SchemeOpenStatisticDTO") {
	"""方案id"""
	schemeId:String
	"""方案名字"""
	schemeName:String
	"""课件供应商id"""
	coursewareSupplierId:String
	"""课件供应商名称"""
	coursewareSupplierName:String
	"""渠道供应商id
		add by wtl 2021年7月21日 09:19:42 v1.7.0.0
	"""
	channelVendorId:String
	"""渠道供应商名称
		add by wtl 2021年7月21日 09:19:42 v1.7.0.0
	"""
	channelVendorName:String
	"""sku属性id"""
	skuProperty:SkuProperty
	"""sku属性值"""
	skuValue:SkuValue
	"""期数id"""
	issueId:String
	"""期数名字"""
	issueName:String
	"""商品id"""
	commoditySkuId:String
	"""适用人群"""
	suitableCrowNames:[String]
	"""购买时商品标价"""
	subOrderLabelPrice:BigDecimal
	"""机构ID"""
	trainingInstitutionId:String
	"""机构名称"""
	trainingInstitutionName:String
	"""自主报名"""
	selfOpen:OpenStatisticAggregationDTO
	"""导入报名"""
	importOpen:OpenStatisticAggregationDTO
	"""集体缴费线上支付"""
	batchOpenOnline:OpenStatisticAggregationDTO
	"""集体缴费线下支付"""
	batchOpenOffline:OpenStatisticAggregationDTO
	"""合计"""
	totalOpen:OpenStatisticAggregationDTO
}
"""学习统计基类
	<AUTHOR> create 2020/7/2 16:45
"""
type SchemeSkuLearniningStatisticsResponse @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.SchemeSkuLearniningStatisticsResponse") {
	"""学习中人数（学习进度<100）"""
	studyingCount:Int!
	"""已学完人数（对应培训人员在对应班级学习进度=100%人员）"""
	studyFinishCount:Int!
	"""学习总人数"""
	studyCount:Int!
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""答题统计总计
	Author:FangKunSen
	Time:2020-03-04,14:50
"""
type TotalAnswerQuestionStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.TotalAnswerQuestionStatisticDTO") {
	"""试题总数"""
	totalQuestionNumber:Long!
	"""累计被作答次数"""
	totalAnswerTimes:Long!
	"""对题率"""
	totalRightRate:Double!
}
"""开通统计合计dto
	Author:FangKunSen
	Time:2020-03-09,14:03
"""
type TotalOpenCountStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.TotalOpenCountStatisticDTO") {
	"""总开通数"""
	openNumber:Long!
	"""净开通（单位：人/次）"""
	realOpenNumber:Long!
	"""净开通学员数（一个学员多次报名仅算一个人数）"""
	realUserOpenNumber:Long!
}
"""开通统计合计dto
	Author:FangKunSen
	Time:2020-03-09,14:03
"""
type TotalOpenStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.TotalOpenStatisticDTO") {
	"""年"""
	year:Int!
	"""月"""
	month:Int!
	"""日"""
	day:Int!
	"""总开通数"""
	openNumber:Long!
	"""退款数"""
	refundNumber:Long!
	"""净开通（单位：人/次）"""
	realOpenNumber:Long!
	"""净开通学员数（一个学员多次报名仅算一个人数）"""
	realUserOpenNumber:Long!
	"""开通总金额（单位：元）"""
	totalAmount:Double!
	"""退款总金额（单位：元）"""
	refundTotalAmount:Double!
	"""净开通总金额（单位：元）"""
	realTotalAmount:Double!
}
"""学习统计（单位维度）dto
	Author:FangKunSen
	Time:2020-05-30,10:48
"""
type TrainingInstitutionLearningStatisticResponse @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.TrainingInstitutionLearningStatisticResponse") {
	"""机构ID"""
	trainingInstitutionId:String
	"""机构名称"""
	trainingInstitutionName:String
	"""属于该单位的人数"""
	unitUserCount:Int!
	"""净报名数量 (总数-退班的)"""
	openUserCount:Int!
	"""未学习人数（对应培训班学习进度=0）"""
	waitStudyCount:Int!
	"""学习中人数（0<学习进度<=100之间）"""
	studyingCount:Int!
	"""已学完人数（对应培训人员在对应班级学习进度=100%人员）"""
	studyFinishCount:Int!
	"""已考试人数。"""
	examedUserCount:Int!
	"""合格人数
		没有考核就直接合格
	"""
	qualifiedCount:Int!
	"""是否有课程学习方式"""
	hasCourseLearning:Boolean!
	"""是否有考试学习方式"""
	hasExamLearning:Boolean!
	"""已考核通过获得学时数"""
	finishAssessGrade:BigDecimal
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""开通统计dto
	Author:FangKunSen
	Time:2020-03-09,14:03
"""
type TrainingInstitutionOpenStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.TrainingInstitutionOpenStatisticDTO") {
	"""机构ID"""
	trainingInstitutionId:String
	"""机构名称"""
	trainingInstitutionName:String
	"""自主报名"""
	selfOpen:OpenStatisticAggregationDTO
	"""导入报名"""
	importOpen:OpenStatisticAggregationDTO
	"""集体缴费线上支付"""
	batchOpenOnline:OpenStatisticAggregationDTO
	"""集体缴费线下支付"""
	batchOpenOffline:OpenStatisticAggregationDTO
	"""合计"""
	totalOpen:OpenStatisticAggregationDTO
}
"""在线学习人数专业分布
	@author: eleven
	@date: 2020/4/24
"""
type UserOnLineMajorDistributeResponse @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.UserOnLineMajorDistributeResponse") {
	"""人数"""
	userCount:Int!
	"""年度"""
	year:Int
	"""培训类别"""
	trainingTypeId:String
	"""培训工种（有原来字段：培训对象-traineesId替换而来）"""
	workTypeId:String
}
"""在线学习人数终端分布
	@author: eleven
	@date: 2020/4/24
"""
type UserOnLineTerminalDistributeResponse @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.UserOnLineTerminalDistributeResponse") {
	"""终端类型"""
	client:ClientType
	"""人数"""
	userCount:Int!
}
"""在线学习人数施教机构分布
	@author: eleven
	@date: 2020/4/24
"""
type UserOnLineUnitDistributeResponse @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.UserOnLineUnitDistributeResponse") {
	"""单位id"""
	unitId:String
	"""人数"""
	userCount:Int!
	"""单位名称"""
	unitName:String
}
"""学习统计-用户练习作答数据统计图-子项
	Author:FangKunSen
	Time:2020-03-18,15:35
"""
type UserPracticeChartSubItemDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.UserPracticeChartSubItemDTO") {
	"""试题类型"""
	practiceType:String
	"""该题型库内总题数"""
	totalNumber:Long!
	"""答题总次数"""
	answerTimes:Double!
	"""答对次数"""
	answerRightTimes:Double!
	"""答对次数占比"""
	proportion:Double!
}
"""工种学习统计
	Author:FangKunSen
	Time:2021-04-21,09:25
"""
type WorkTypeLearningStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.WorkTypeLearningStatisticDTO") {
	"""工种id路径"""
	workTypeIdPath:String
	"""工种路径名"""
	workTypeIdPathName:String
	"""净报名数量 (总数-退班的)"""
	openUserCount:Int!
	"""未学习人数（对应培训班学习进度=0）"""
	waitStudyCount:Int!
	"""学习中人数（0<学习进度<=100之间）"""
	studyingCount:Int!
	"""已学完人数（对应培训人员在对应班级学习进度=100%人员）"""
	studyFinishCount:Int!
	"""合格人数
		没有考核就直接合格
	"""
	qualifiedCount:Int!
}
"""开通统计dto
	Author:FangKunSen
	Time:2020-03-09,14:03
"""
type WorkTypeOpenStatisticDTO @type(value:"com.fjhb.btpx.integrative.service.statistic.dto.response.WorkTypeOpenStatisticDTO") {
	"""培训工种类别id"""
	trainingTypeId:String
	"""培训工种类别名称"""
	trainingTypeName:String
	"""培训工种id"""
	workTypeId:String
	"""培训工种名称"""
	workTypeName:String
	"""报名数"""
	openNumber:Long!
}
"""Author:FangKunSen
	Time:2020-10-27,15:55
"""
enum QueryParamSortEnum @type(value:"com.fjhb.btpx.integrative.service.utils.dto.QueryParamSortEnum") {
	"""升序"""
	ASC
	"""降序"""
	DESC
}
"""用户课程学习状态
	@author: eleven
	@date: 2020/3/7
"""
enum StudyState @type(value:"com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState") {
	WAIT_STUDY
	STUDY
	STUDY_FINISH
}
"""在线终端类型
	@author: eleven
	@date: 2020/4/27
"""
enum ClientType @type(value:"com.fjhb.btpx.platform.dao.mongo.enums.ClientType") {
	"""WEB页面"""
	WEB
	"""微信"""
	WX
	"""微信小程序"""
	WXMP
	"""微信订阅号（公众号）"""
	WXSB
}
"""<AUTHOR>
	@date 2020/8/1010:43
"""
type Region @type(value:"com.fjhb.btpx.platform.dao.mongo.model.Region") {
	"""地区路径"""
	regionPath:String
	"""省份id"""
	provinceId:String
	"""城市id"""
	cityId:String
	"""区、县id"""
	countyId:String
}
"""缴费渠道类型
	<AUTHOR>
"""
enum PaymentChannelTypeEnum @type(value:"com.fjhb.btpx.platform.service.paymentchannel.dto.PaymentChannelTypeEnum") {
	"""web端"""
	WEB
	"""android客户端"""
	ANDROID
	"""ios客户端"""
	IOS
	"""微信公众号（订阅号）"""
	WECHAT_OFFICIAL_ACCOUNTS
	"""微信小程序"""
	WECHAT_MINI_PROGRAMS
	"""管理员现场开通"""
	PRESENT
	"""管理员集体缴费"""
	COLLECTIVE
	"""H5"""
	HTML5
	"""钉钉"""
	DINGDING
	"""渠道开通"""
	CHANNEL_PRESENT_OPEN
}
"""时间单位枚举
	<AUTHOR>
	@version 1.0
	@date 2021/7/17 15:00
"""
enum TimeUnit @type(value:"com.fjhb.btpx.support.constants.TimeUnit") {
	YEARS
	MONTHS
	DAYS
	HOURS
}
"""订单状态"""
enum OrderStatus @type(value:"com.fjhb.btpx.support.constants.trade.OrderStatus") {
	"""等待付款"""
	WAITING_FOR_PAYMENT
	"""等待卖家确认款项"""
	WAITING_FOR_SELLER_AFFIRM
	"""支付中"""
	PAY_IN
	"""支付成功"""
	PAY_SUCCESS
	"""部分发货 发货中"""
	DELIVERING
	"""部分发货失败"""
	DELIVERY_PART_FAIL
	"""发货失败"""
	DELIVERY_FAIL
	"""发货已完成"""
	DELIVER_COMPLETE
	"""交易成功"""
	TRADE_SUCCESS
	"""交易关闭"""
	TRADE_CLOSE
}
enum QuestionType @type(value:"com.fjhb.exam.lang.enums.QuestionType") {
	SINGLE_SELECTION
	MULTIPLE_SELECTION
	JUDGEMENT
	BLANK_FILLING
	ESSAY
	COMPREHENSIVE
	SCALE
}

scalar List
type LearningProcessCourseInfoDtoPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LearningProcessCourseInfoDto]}
type AnswerQuestionStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [AnswerQuestionStatisticDTO]}
type CourseAppraiseStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseAppraiseStatisticDTO]}
type HotCourseChooseStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [HotCourseChooseStatisticDTO]}
type RegionLearningStatisticResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [RegionLearningStatisticResponse]}
type SchemeCourseAppraiseStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SchemeCourseAppraiseStatisticDTO]}
type SchemeLearningStatisticResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SchemeLearningStatisticResponse]}
type SchemeOpenStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SchemeOpenStatisticDTO]}
type TrainingInstitutionCourseAppraiseStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingInstitutionCourseAppraiseStatisticDTO]}
type TrainingInstitutionLearningStatisticResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingInstitutionLearningStatisticResponse]}
type TrainingInstitutionOpenStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingInstitutionOpenStatisticDTO]}
type LearningStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LearningStatisticDTO]}
type UserLoginLogResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [UserLoginLogResponse]}
type WorkTypeLearningStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [WorkTypeLearningStatisticDTO]}
type WorkTypeOpenStatisticDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [WorkTypeOpenStatisticDTO]}
