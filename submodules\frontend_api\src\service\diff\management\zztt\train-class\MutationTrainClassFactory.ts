import { CreateTrainClassTypeEnum } from '@api/service/management/train-class/mutation/Enum/CreateTrainClassTypeEnum'
import QueryTrainClassDetailClass from '@api/service/diff/management/zztt/train-class/QueryTrainClassDetailClass'
import MutationCreateTrainClassCommodity from '@api/service/diff/management/zztt/train-class/MutationCreateTrainClassCommodity'
import MutationTrainClassFactory from '@api/service/management/train-class/MutationTrainClassFactory'

/**
 * 培训班业务工厂类
 */
class MutationTrainClassFactoryDiff extends MutationTrainClassFactory {
  /**
   * 获取创建|更新|复制培训班商品类
   */
  async getMutationCreateTrainClassCommodity(
    createType = CreateTrainClassTypeEnum.CreateTrainClassTypeEnumCreate,
    commodityId?: string
  ): Promise<MutationCreateTrainClassCommodity> {
    if (createType == CreateTrainClassTypeEnum.CreateTrainClassTypeEnumCreate) {
      return new MutationCreateTrainClassCommodity()
    } else {
      const queryDetail = new QueryTrainClassDetailClass()
      queryDetail.commodityId = commodityId
      await queryDetail.queryTrainClassDetail()
      if (createType == CreateTrainClassTypeEnum.CreateTrainClassTypeEnumUpdate) {
        const mutationCreateTrainClassCommodity = new MutationCreateTrainClassCommodity()
        Object.assign(mutationCreateTrainClassCommodity, await queryDetail.getUpdateClass())
        return mutationCreateTrainClassCommodity
      } else {
        const mutationCreateTrainClassCommodity = new MutationCreateTrainClassCommodity()
        Object.assign(mutationCreateTrainClassCommodity, await queryDetail.getCopyClass())
        return mutationCreateTrainClassCommodity
      }
    }
  }

  // endregion
}
export default MutationTrainClassFactoryDiff
