import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjypxtypt-ahzj-school'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-ahzj-school'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class DeliveryAddress {
  consignee: string
  phone: string
  region: string
  address: string
}

export class TakePoint {
  pickupLocation: string
  pickupTime: string
  remark?: string
}

/**
 * 请求创建订单
<AUTHOR>
@since 2021/1/22
 */
export class CreateOrderRequest {
  /**
   * 买家编号
   */
  buyerId: string
  /**
   * 商品列表
   */
  commodities: Array<Commodity>
  /**
   * 购买渠道类型
1-用户自主购买
2-集体缴费
3-管理员导入
   */
  purchaseChannelType: number
  /**
   * 终端类型
<p>
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  terminalCode: string
  /**
   * 渠道商编号
   */
  channelVendorId?: string
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceInfoRequest
  /**
   * 参训单位id
   */
  participatingUnitId?: string
  /**
   * 销售渠道Id（自营渠道为空，专题渠道时必填）
   */
  saleChannelId?: string
  /**
   * 销售渠道类型
0-自营渠道
2-专题渠道
   */
  saleChannel: number
  /**
   * 购买来源类型，1-门户，2-专题
@see com.fjhb.ms.order.v1.api.consts.PurchaseSourceType
   */
  purchaseSourceType?: number
  /**
   * 身份证
   */
  idCard?: string
}

/**
 * 商品描述
 */
export class Commodity {
  /**
   * 商品sku编号
   */
  skuId?: string
  /**
   * 商品数量
   */
  quantity?: number
  /**
   * 面授班时有值
   */
  issueInfo?: IssueInfo
}

export class IssueInfo {
  /**
   * 期别id
   */
  issueId?: string
  /**
   * 住宿类型
住宿类型 0-无需住宿 1-单人住宿 2-合住
   */
  accommodationType?: number
}

/**
 * 发票信息
<AUTHOR>
@since 2021/3/23
 */
export class InvoiceInfoRequest {
  /**
   * 发票抬头
   */
  title?: string
  /**
   * 发票抬头类型
<pre>
1-个人
2-企业
</pre>
   */
  titleType?: number
  /**
   * 发票类型
<pre>
1-电子发票
2-纸质发票
</pre>
   */
  invoiceType?: number
  /**
   * 发票种类
<pre>
1-普通发票
2-增值税普通发票
3-增值税专用发票
</pre>
   */
  invoiceCategory?: number
  /**
   * 购买方纳税人识别号
   */
  taxpayerNo?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bankName?: string
  /**
   * 账户
   */
  account?: string
  /**
   * 发票票面备注
   */
  remark?: string
  /**
   * 开票方式
1 - 线上开票
2 - 线下开票
   */
  invoiceMethod?: number
  /**
   * 联系电子邮箱
   */
  email?: string
  /**
   * 联系电话
   */
  contactPhone?: string
  /**
   * 营业执照
   */
  businessLicensePath?: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath?: string
  /**
   * 配送方式
0/1/2,无/自取/快递
@see OfflineShippingMethods
   */
  shippingMethod?: number
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddress
  /**
   * 自取点信息
   */
  takePoint?: TakePoint
}

/**
 * 申请学员学习Token
<AUTHOR>
@since 2024/5/24
 */
export class ApplyStudentLearningTokenRequest {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学习方式ID
   */
  learningId: string
}

/**
 * 进入首页请求
<AUTHOR>
@since 2024/5/24
 */
export class EnterIndexRequest {
  /**
   * 学员信息key
   */
  studentInfoKey?: string
}

/**
 * <AUTHOR>
 */
export class GetTrainSupervisionForAHZJRequest {
  /**
   * 订单id，将在统一网关转为报名序号
由于清洗延迟问题，后续使用子订单
   */
  orderId?: string
  /**
   * 子订单号
   */
  subOrderNo?: string
  /**
   * 用户id，将在同一网关转为用户开发id
   */
  userId?: string
  /**
   * 培训平台的课程唯一标识
   */
  courseId?: string
  /**
   * 培训平台的课程名
   */
  courseName?: string
}

/**
 * <AUTHOR>
@since 2024/5/24
 */
export class GetVerificationCodeForAHZJRequest {
  configKey?: string
  playTime?: number
}

/**
 * <AUTHOR>
 */
export class SendCodeAnswerForAHZJRequest {
  /**
   * 验证码ID
   */
  codeId?: string
  /**
   * 验证码答案
   */
  codeAnswer?: string
}

/**
 * <AUTHOR>
 */
export class SignUpNumberRequest {
  /**
   * 用户身份证
   */
  certificateNumber?: string
}

/**
 * 校验是否允许创建订单
<AUTHOR>
@since 2024/5/24
 */
export class ValidAllowToCreateOrderRequest {
  /**
   * 商品sku
   */
  skuId: string
  /**
   * 身份证
   */
  idCard: string
}

/**
 * 校验是否允许学习
<AUTHOR>
@since 2024/5/24
 */
export class ValidAllowToLearningRequest {
  /**
   * 参训资格ID
   */
  qualificationId: string
}

export class AntiCheatCodeAnswerPushResult {
  id: string
  verifyResult: number
  goNext: boolean
}

export class AntiCheatCodeDto {
  id: string
  content: string
  couldAnswerCount: number
  remainAnswerCount: number
}

export class AntiCheatConfigDto {
  configKey: string
  allVerifyCount: number
  coursePlayTimeList: Array<PlayTimePointDto>
}

export class PlayTimePointDto {
  playTime: number
  isVerify: number
}

/**
 * 创建订单结果
<AUTHOR> create 2021/1/29 17:27
 */
export class CreateOrderResultResponse {
  /**
   * 是否创建成功
   */
  success: boolean
  /**
   * 订单号，仅当{@link #success}为{@code true}时有值
   */
  orderNo: string
  /**
   * 订单创建时间，仅当{@link #success}为{@code true}时有值
   */
  createTime: string
  /**
   * 下单结果信息
   */
  message: string
  /**
   * 商品验证结果信息
   */
  resultList: Array<VerifyResultResponse>
}

/**
 * 校验结果返回
<AUTHOR> create 2021/2/3 10:53
 */
export class VerifyResultResponse {
  /**
   * 校验结果
   */
  message: string
  /**
   * 校验code
   */
  code: string
  /**
   * 订单内的商品skuId
   */
  skuId: string
}

/**
 * 进入首页响应
<AUTHOR>
@since 2024/5/24
 */
export class EnterIndexResponse {
  /**
   * 登录token
   */
  token: string
  /**
   * 页面中转key
@see http://192.168.1.225:8090/pages/viewpage.action?pageId&#x3D;284295407
   */
  key: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 参训资格id
   */
  qualificationId: string
  /**
   * 商品skuId
   */
  commoditySkuId: string
  /**
   * 培训方案id
   */
  schemeId: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * <AUTHOR>
@since 2024/5/24
 */
export class GetTrainSupervisionForAHZJResponse {
  antiCheatConfigDto: AntiCheatConfigDto
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * <AUTHOR>
@since 2024/5/24
 */
export class GetVerificationCodeForAHZJResponse {
  antiCheatCodeDto: AntiCheatCodeDto
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * <AUTHOR>
@since 2024/5/24
 */
export class SendCodeAnswerForAHZJResponse {
  antiCheatCodeAnswerPushResult: AntiCheatCodeAnswerPushResult
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 报名序列号信息
<AUTHOR>
 */
export class SignUpNumberInfoResponse {
  /**
   * 报名序号，培训平台报名成功保存用于后续使用
   */
  signUpNumber: string
  /**
   * 培训班名称
   */
  trainingClassName: string
  /**
   * 培训形式
1：网授-培训班
   */
  trainingType: number
  /**
   * 报名年度
   */
  trainingYear: number
  /**
   * 科目类型
0：公需课
   */
  subjectType: number
  /**
   * 科目名称
   */
  subject: string
  /**
   * 公需学时，班级科目类型为专业，该值为空
   */
  publicPeriod: number
  /**
   * 培训开始时间
   */
  trainingStartTime: string
  /**
   * 培训结束时间
   */
  trainingEndTime: string
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 对应订单状态
   */
  orderStatus: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 通过学员身份证获取管理系统报名信息
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listSignUpNumberInfo(
    request: SignUpNumberRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listSignUpNumberInfo,
    operation?: string
  ): Promise<Response<Array<SignUpNumberInfoResponse>>> {
    return commonRequestApi<Array<SignUpNumberInfoResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请学员学习Token
   * @param request:
   * @return {@link String}
   * <AUTHOR> By Cb
   * @since 2024/5/24 21:05
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyStudentLearningToken(
    request: ApplyStudentLearningTokenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyStudentLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建订单
   * @param mutate 查询 graphql 语法文档
   * @param createOrderInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOrder(
    createOrderInfo: CreateOrderRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createOrder,
    operation?: string
  ): Promise<Response<CreateOrderResultResponse>> {
    return commonRequestApi<CreateOrderResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createOrderInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 单点登录入口
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async enterIndex(
    request: EnterIndexRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enterIndex,
    operation?: string
  ): Promise<Response<EnterIndexResponse>> {
    return commonRequestApi<EnterIndexResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 从管理平台获取监管规则
   * @see AntiCheatConfigDto
   * @param mutate 查询 graphql 语法文档
   * @param antiCheatConfigParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTrainSupervisionForAHZJ(
    antiCheatConfigParam: GetTrainSupervisionForAHZJRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.getTrainSupervisionForAHZJ,
    operation?: string
  ): Promise<Response<GetTrainSupervisionForAHZJResponse>> {
    return commonRequestApi<GetTrainSupervisionForAHZJResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { antiCheatConfigParam },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 从管理平台获取验证码
   * @param mutate 查询 graphql 语法文档
   * @param antiCheatCodeAnswerRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getVerificationCodeForAHZJ(
    antiCheatCodeAnswerRequest: GetVerificationCodeForAHZJRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.getVerificationCodeForAHZJ,
    operation?: string
  ): Promise<Response<GetVerificationCodeForAHZJResponse>> {
    return commonRequestApi<GetVerificationCodeForAHZJResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { antiCheatCodeAnswerRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 机构培训平台向管理平台发送验证结果
   * @see AntiCheatCodeAnswerPushResult
   * @param mutate 查询 graphql 语法文档
   * @param antiCheatCodeAnswerRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sendCodeAnswerForAHZJ(
    antiCheatCodeAnswerRequest: SendCodeAnswerForAHZJRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sendCodeAnswerForAHZJ,
    operation?: string
  ): Promise<Response<SendCodeAnswerForAHZJResponse>> {
    return commonRequestApi<SendCodeAnswerForAHZJResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { antiCheatCodeAnswerRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验是否允许创建订单
   * @param request:
   * @return {@link Boolean}
   * <AUTHOR> By Cb
   * @since 2024/5/24 21:28
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validAllowToCreateOrder(
    request: ValidAllowToCreateOrderRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validAllowToCreateOrder,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验是否允许学习
   * @param request:
   * @return {@link Boolean}
   * <AUTHOR> By Cb
   * @since 2024/5/24 21:28
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validAllowToLearning(
    request: ValidAllowToLearningRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validAllowToLearning,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
