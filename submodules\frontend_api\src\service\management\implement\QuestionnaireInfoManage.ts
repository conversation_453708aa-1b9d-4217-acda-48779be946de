import QuestionnaireImplementItem from '@api/service/management/implement/models/QuestionnaireImplementItem'
import QuestionnaireQueryParam from '@api/service/management/resource/question-naire/models/QuestionnaireQueryParam'
import { Page } from '@hbfe/common'
import MsExamQueryFrontGateway, {
  QuestionnaireInLearningSchemeResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-QuestionnaireQueryBackStage'

/**
 * 问卷管理
 */
export default class QuestionnaireInfoManage {
  /**
   * 方案id
   */
  private schemeId: string = undefined

  /**
   * 期别id
   */
  private periodId: string = undefined

  /**
   * 查询参数
   */
  params: QuestionnaireQueryParam = new QuestionnaireQueryParam()

  /**
   * 问卷列表
   */
  list: Array<QuestionnaireImplementItem> = new Array<QuestionnaireImplementItem>()

  /**
   * @param schemeId 方案id
   * @param periodId 期别id（需要查询期别下的问卷要传入）
   */
  constructor(schemeId: string, periodId?: string) {
    this.schemeId = schemeId
    if (periodId) {
      this.periodId = periodId
    }
  }

  /**
   * 查询方案问卷列表
   * @param page 分页
   */
  async queryListInScheme(page: Page) {
    const request = this.params.toSchemeQuestionnaireQuery()
    request.schemeId = this.schemeId
    const res = await MsExamQueryFrontGateway.pageSchemeQuestionnaireStatisticsInServicer({ page, request })

    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize

    if (res?.data?.currentPageData?.length) {
      this.list = res.data.currentPageData.map((item: QuestionnaireInLearningSchemeResponse) => {
        return QuestionnaireImplementItem.fromPeriod(item)
      })
    } else {
      this.list = new Array<QuestionnaireImplementItem>()
    }
  }

  /**
   * 查询期别问卷列表
   * @param page 分页
   */
  async queryListInPeriod(page: Page) {
    const request = this.params.toPeriodQuestionnaireQuery()
    request.issueId = this.periodId
    const res = await MsExamQueryFrontGateway.pageIssueQuestionnaireStatisticsInServicer({
      page,
      request
    })
    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize

    if (res?.data?.currentPageData?.length) {
      this.list = res.data.currentPageData.map((item: QuestionnaireInLearningSchemeResponse) => {
        return QuestionnaireImplementItem.fromPeriod(item)
      })
    } else {
      this.list = new Array<QuestionnaireImplementItem>()
    }
  }
}
