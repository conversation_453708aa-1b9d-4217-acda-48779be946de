import MsExamQueryBackStageGateway, {
  AutomaticPublishPattern,
  LibraryFixedQuestionScopeSetting,
  LibraryQuestionScopeSetting,
  LibraryRequest,
  LibraryResponse,
  PaperPublishConfigureResponse,
  QuestionExtractSetting,
  QuestionScoreSetting,
  ScoreEvaluatePattern
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'

import { UiPage, Response } from '@hbfe/common'
import { PublishPatternTypes } from '../../enum/ExamPaperPublishPatternTypes'
import ExamQuestionTypes, { QuestionTypes } from '../../enum/ExamQuestionTypes'
import { QuestionScopeSettingTypes } from '../../enum/ExamScopeSettingTypes'
import AutomaticExamPaperQuestionVo from './AutomaticExamPaperQuestionVo'

/*
  智能卷详情
*/
class AutomaticExamPaperDetailVo {
  /**
   * 出卷配置ID (试卷id)
   */
  id = ''
  /**
   * 出卷配置名称 (试卷名称)
   */
  name = ''
  /*
    试卷分类名称
  */
  paperCategoryName = ''
  /*
    试卷分类id
  */
  paperCategoryId = ''

  /**
   * 出卷模式类型 智能卷 0 ，固定卷 1   ，AB卷 2
   */
  paperPublishPatterns: number = PublishPatternTypes.AutomaticPublishPattern

  /**
   * 是否启用 1 启用 2禁用
   */
  status = 1

  /**
   * 是否是草稿 1是  2不是
   */
  isDraft = 1

  /*
   大题数量
  */
  questionCount = 0
  /*
    试题总数
  */
  testQuestionCount = 0
  /*
    涵盖题型
  */
  allTestQuestionTypes: Array<string> = []

  /* 
    出题范围
  */
  questionScopes = QuestionScopeSettingTypes.LibraryQuestionScopeSetting

  /**
   * 试题题型 [1单选  2多选  3判断]
   */
  testQuestionType: QuestionTypes = QuestionTypes.RadioQuestion

  /**
   * 大题描述集合
   */
  questionExtracts: Array<AutomaticExamPaperQuestionVo> = []

  /*
    考试时长 【分钟】
  */
  suggestionTimeLength = 0

  /*
    已选题库id集合
  */
  questionLibraryIds: Array<string> = []

  /*
    已选题库名称集合
  */
  questionLibraryNames: Array<string> = []

  // 模型转换vo
  from(item: PaperPublishConfigureResponse, list?: Array<string>) {
    const detail = new AutomaticExamPaperDetailVo()
    detail.id = item.id
    detail.name = item.name
    detail.paperCategoryId = item.createUserId
    detail.paperCategoryId = item.paperPublishConfigureCategory?.id
    detail.paperCategoryName = item.paperPublishConfigureCategory?.name
    detail.paperPublishPatterns = item.paperPublishPatterns
    detail.status = item.status
    detail.isDraft = item.isDraft
    const qExtractRule = (item.publishPattern as AutomaticPublishPattern)?.questionExtractRule
    detail.testQuestionCount = qExtractRule?.questionCount
    detail.questionCount = qExtractRule?.questionExtracts?.length || 0
    if (qExtractRule.questionScopes?.length) {
      detail.questionScopes = qExtractRule.questionScopes[0]?.type
    }

    const pPattern = item.publishPattern as AutomaticPublishPattern
    detail.suggestionTimeLength = pPattern?.suggestionTimeLength / 60 || 0
    detail.questionLibraryNames = list

    // 按分数评定,大题集合
    const eScoreQuestionList = (pPattern.evaluatePattern as ScoreEvaluatePattern)?.questionScores

    // 大题数据map集合
    const questionMap = new Map<number, AutomaticExamPaperQuestionVo>()
    // 存储大题序号，大题平均分集合
    eScoreQuestionList?.forEach((item: QuestionScoreSetting) => {
      const mapItem = new AutomaticExamPaperQuestionVo()
      mapItem.sequence = item.sequence
      mapItem.averageScore = item.eachQuestionScore
      questionMap.set(mapItem.sequence, mapItem)
    })
    // 存储每大题中的试题总数
    qExtractRule?.questionExtracts?.forEach((count: QuestionExtractSetting) => {
      // 根据大题序号匹配大题
      const countItem = questionMap.get(count?.sequence)
      if (!countItem) {
        console.error('找不到匹配的大题，请检查大题数组的大题序号与评分规则内的大题序号是否匹配')
        return
      }
      countItem.name = count?.groupName
      countItem.questionCount = count?.questionCount
      countItem.questionType = ExamQuestionTypes.map.get(count?.questionType)
      detail.allTestQuestionTypes.push(countItem.questionType)
    })

    // 涵盖题型去重
    detail.allTestQuestionTypes = [...new Set(detail.allTestQuestionTypes)]

    // map转数组
    for (const value of questionMap.values()) {
      // 给大题总分赋值
      value.totalScore = value.questionCount * value.averageScore
      detail.questionExtracts.push(value)
    }
    return detail
  }
}

export default AutomaticExamPaperDetailVo
