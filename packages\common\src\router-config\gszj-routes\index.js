function Statistic() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/statistic" */ '@hbfe/jxjy-admin-routers/src/gszj/statistic.vue'
  )
}
function StatisticLearningStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/statistic-learning-statistic-index" */ '@hbfe/jxjy-admin-routers/src/gszj/statistic/learning-statistic/index.vue'
  )
}
function StatisticRegionalSituationStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/statistic-regional-situation-statistic-index" */ '@hbfe/jxjy-admin-routers/src/gszj/statistic/regional-situation-statistic/index.vue'
  )
}
function Training() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/training" */ '@hbfe/jxjy-admin-routers/src/gszj/training.vue'
  )
}
function TrainingCustomerService() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/training-customer-service" */ '@hbfe/jxjy-admin-routers/src/gszj/training/customer-service.vue'
  )
}
function TrainingCustomerServicePersonalIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/training-customer-service-personal-index" */ '@hbfe/jxjy-admin-routers/src/gszj/training/customer-service/personal/index.vue'
  )
}
function TrainingScheme() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/training-scheme" */ '@hbfe/jxjy-admin-routers/src/gszj/training/scheme.vue'
  )
}
function TrainingSchemeCourseMaintenance() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/training-scheme-course-maintenance" */ '@hbfe/jxjy-admin-routers/src/gszj/training/scheme/course-maintenance.vue'
  )
}
function TrainingTask() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/training-task" */ '@hbfe/jxjy-admin-routers/src/gszj/training/task.vue'
  )
}
function TrainingTaskImporttaskIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/training-task-importtask-index" */ '@hbfe/jxjy-admin-routers/src/gszj/training/task/importtask/index.vue'
  )
}
function TrainingUser() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/training-user" */ '@hbfe/jxjy-admin-routers/src/gszj/training/user.vue'
  )
}
function TrainingUserStudentDetail() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/gszj/training-user-student-detail" */ '@hbfe/jxjy-admin-routers/src/gszj/training/user/student/detail.vue'
  )
}

export default [
  {
    name: 'statistic',
    path: '/statistic',
    component: Statistic,
    meta: {
      permissionMap: {},
      openWhenInit: false,
      closeAble: false,
      isMenu: true,
      title: '统计报表',
      sort: 5,
      icon: 'icon-shuju',
      ownerGroup: [],
      group: 'statistic',
    },
    children: [
      {
        name: 'statistic-learning-statistic-index',
        path: 'learning-statistic',
        component: StatisticLearningStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '学员学习明细',
          sort: 5,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.learning-statistic',
        },
      },
      {
        name: 'statistic-regional-situation-statistic-index',
        path: 'regional-situation-statistic',
        component: StatisticRegionalSituationStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '地区学情统计',
          sort: 4,
          icon: 'icon-ribaotongji',
          ownerGroup: [],
          group: 'statistic.regional-situation-statistic',
        },
      },
    ],
  },
  {
    name: 'training',
    path: '/training',
    component: Training,
    meta: {
      permissionMap: {},
      openWhenInit: false,
      closeAble: false,
      isMenu: true,
      title: '培训管理',
      sort: 4,
      icon: 'icon-peixun',
      ownerGroup: [],
      group: 'training',
    },
    children: [
      {
        name: 'training-customer-service',
        path: 'customer-service',
        component: TrainingCustomerService,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '客服管理',
          sort: 6,
          icon: 'icon-kefu',
          ownerGroup: [],
          group: 'training.customer-service',
        },
        children: [
          {
            name: 'training-customer-service-personal-index',
            path: 'personal',
            component: TrainingCustomerServicePersonalIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '业务咨询',
              sort: 1,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.customer-service.personal',
            },
          },
        ],
      },
      {
        name: 'training-scheme',
        path: 'scheme',
        component: TrainingScheme,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '培训方案管理',
          sort: 2,
          icon: 'icon-fangan',
          ownerGroup: [],
          group: 'training.scheme',
        },
        children: [
          {
            name: 'training-scheme-course-maintenance',
            path: 'course-maintenance',
            component: TrainingSchemeCourseMaintenance,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '公需课课程维护',
              sort: 5,
              ownerGroup: [],
              group: 'training.scheme.course-maintenance',
            },
          },
        ],
      },
      {
        name: 'training-task',
        path: 'task',
        component: TrainingTask,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '导入导出任务管理',
          sort: 11,
          icon: 'icon-dingdan',
          ownerGroup: [],
          group: 'training.task',
        },
        children: [
          {
            name: 'training-task-importtask-index',
            path: 'importtask',
            component: TrainingTaskImporttaskIndex,
            meta: {
              permissionMap: {},
              isMenu: true,
              title: '导入任务管理',
              sort: 1,
              icon: 'icon_menhuxinxiguanli',
              ownerGroup: [],
              group: 'training.task.importtask',
            },
          },
        ],
      },
      {
        name: 'training-user',
        path: 'user',
        component: TrainingUser,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '用户管理',
          sort: 5,
          icon: 'icon-guanliyuan',
          ownerGroup: [],
          group: 'training.user',
        },
        children: [
          {
            name: 'training-user-student-detail',
            path: 'student/detail/:id',
            component: TrainingUserStudentDetail,
            meta: {
              permissionMap: {},
              title: '学员详情',
              ownerGroup: [],
              group: 'training.user.student.detail',
            },
          },
        ],
      },
    ],
  },
]
