<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="课件供应商">
                <el-cascader clearable filterable :options="cascader" placeholder="请选择" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="查询时间">
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item>
                <el-button type="primary">查询</el-button>
                <el-button>批量导出</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert f-clear">
            <div class="f-c6 f-fl">
              搜索结果合计：累计选课人次：<span class="f-fb f-co">8</span>，累计换出人次：<span class="f-fb f-co"
                >8</span
              >， 净选课人次：<span class="f-fb f-co">25</span>
            </div>
            <div class="f-fr f-ml20 f-c6">单位：人次</div>
            <div class="f-fr f-csp f-flex f-align-center"><i class="el-icon-info f-f16 f-mr5"></i>统计口径说明</div>
          </el-alert>
        </div>
        <!--表格-->
        <el-table stripe :data="tableData1" border max-height="500px" class="m-table is-statistical f-mt10">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="课件供应商" prop="field01" min-width="200" fixed="left">
            <template>课件供应商课件供应商</template>
          </el-table-column>
          <el-table-column label="累计选课人次" min-width="90" align="right">
            <template>26252</template>
          </el-table-column>
          <el-table-column label="累计换出人次" min-width="90" align="right">
            <template>26</template>
          </el-table-column>
          <el-table-column label="净选课人次" min-width="90" align="right">
            <template>26</template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">查看详情</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        >
        </el-pagination>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        tableData1: [
          {
            id: 1,
            field01: '福建省',
            hasChildren: true
          },
          {
            id: 2,
            field01: '江西省',
            hasChildren: true
          },
          {
            id: 3,
            field01: '河南省',
            hasChildren: true
          },
          {
            id: 4,
            field01: '广东省',
            hasChildren: true
          },
          {
            id: 5,
            field01: '湖南省',
            hasChildren: true
          }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      },
      load(tree, treeNode, resolve) {
        setTimeout(() => {
          resolve([
            {
              id: 11,
              field01: '市区'
            },
            {
              id: 12,
              field01: '市区'
            }
          ])
        }, 1000)
      }
    }
  }
</script>
