import PreviewCourseSceneGate from '@api/service/customer/learning/scene/gates/PreviewCourseSceneGate'
import { Component, Vue } from 'vue-property-decorator'

@Component
class PreviewCourseMixins extends Vue {
  async previewCourse(id: string, evaluation: number) {
    const previewCourseGate = new PreviewCourseSceneGate({ courseId: id })
    const ticket = await previewCourseGate.applyEnterTicket()
    window.open(
      [
        `/play/previewCourse/${ticket}`,
        `?prevUrl=${encodeURIComponent(window.location.href)}`,
        `&evaluation=${evaluation}`
      ].join('')
    )
  }
}

export default PreviewCourseMixins
