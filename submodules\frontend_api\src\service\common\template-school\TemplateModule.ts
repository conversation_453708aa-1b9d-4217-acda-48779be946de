import TemplateItem from '@api/service/common/template-school/TemplateItem'
import { ClientTypeEnum } from '@api/service/common/template-school/enums/ClientTypeEnum'
import { ColorEnum } from '@api/service/common/template-school/enums/ColorEnum'
import { IndustryTypeEnum } from '@api/service/common/template-school/enums/IndustryTypeEnum'

class TemplateModule {
  /**
   * 模板列表-人社PC
   * 与最初设计不符 故废弃
   */
  private _templateRSPCList: Array<TemplateItem> = [
    {
      id: 'TestTemplateId-1',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.RS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      logoSize: [900, 80],
      iconSize: [16, 16],
      customerPhoneSize: [280, 50],
      bannerSize: [1920, 400],
      H5QRCodeSize: [160, 160],
      trainingProcessSize: [1200, 120],
      linksSize: [400, 90],
      groupRegistrationSize: [200, 360],
      singleOnlineCollectSize: [1186, 150],
      doubleOnlineCollectSize: [589, 150],
      singleOfflineCollectSize: [1186, 150],
      doubleOfflineCollectSize: [589, 150],
      wechatCustomerServiceSize: [160, 160],
      doubleOnlineExampleImgSrc: 'demo-default-xs-double.jpg',
      singleOnlineExampleImgSrc: 'demo-default-xs-single.jpg',
      doubleOfflineExampleImgSrc: 'demo-default-xx-double.jpg',
      singleOfflineExampleImgSrc: 'demo-default-xx-single.jpg',
      reviewPath: 's-default'
    },
    {
      id: 'rs-pc-001',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.RS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED, ColorEnum.ORANGE],
      logoSize: [900, 60],
      iconSize: [16, 16],
      customerPhoneSize: [280, 50],
      bannerSize: [860, 330],
      H5QRCodeSize: [100, 100],
      trainingProcessSize: [1050, 72],
      linksSize: [269, 59],
      groupRegistrationSize: [200, 360],
      singleOnlineCollectSize: [1200, 100],
      doubleOnlineCollectSize: [860, 100],
      singleOfflineCollectSize: [1200, 100],
      doubleOfflineCollectSize: [324, 100],
      wechatCustomerServiceSize: [160, 160],
      doubleOnlineExampleImgSrc: 'demo-template01-xs-double.jpg',
      singleOnlineExampleImgSrc: 'demo-template01-xs-single.jpg',
      doubleOfflineExampleImgSrc: 'demo-template01-xx-double.jpg',
      singleOfflineExampleImgSrc: 'demo-template01-xx-single.jpg',
      reviewPath: 's-template01'
    },
    {
      id: 'rs-pc-002',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.RS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      logoSize: [900, 50],
      iconSize: [16, 16],
      customerPhoneSize: [280, 48],
      bannerSize: [630, 200],
      H5QRCodeSize: [100, 100],
      trainingProcessSize: [1050, 72],
      linksSize: [280, 48],
      groupRegistrationSize: [200, 360],
      singleOnlineCollectSize: [1200, 100],
      doubleOnlineCollectSize: [590, 100],
      singleOfflineCollectSize: [1200, 100],
      doubleOfflineCollectSize: [590, 100],
      wechatCustomerServiceSize: [160, 160],
      doubleOnlineExampleImgSrc: 'demo-template02-xs-double.jpg',
      singleOnlineExampleImgSrc: 'demo-template02-xs-single.jpg',
      doubleOfflineExampleImgSrc: 'demo-template02-xx-double.jpg',
      singleOfflineExampleImgSrc: 'demo-template02-xx-single.jpg',
      reviewPath: 's-template02'
    },
    {
      id: 'rs-pc-003',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.RS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      logoSize: [830, 90],
      iconSize: [16, 16],
      customerPhoneSize: [292, 112],
      bannerSize: [1200, 360],
      H5QRCodeSize: [100, 100],
      trainingProcessSize: [1200, 160],
      linksSize: [285, 60],
      groupRegistrationSize: [],
      singleOnlineCollectSize: [1200, 100],
      doubleOnlineCollectSize: [590, 100],
      singleOfflineCollectSize: [1200, 100],
      doubleOfflineCollectSize: [590, 100],
      wechatCustomerServiceSize: [160, 160],
      doubleOnlineExampleImgSrc: 'demo-template02-xs-double.jpg',
      singleOnlineExampleImgSrc: 'demo-template02-xs-single.jpg',
      doubleOfflineExampleImgSrc: 'demo-template02-xx-double.jpg',
      singleOfflineExampleImgSrc: 'demo-template02-xx-single.jpg',
      reviewPath: 's-template04'
    }
  ]
  /**
   * 模板列表-人社H5
   * 与最初设计不符 故废弃
   */
  private _templateRSH5List: Array<TemplateItem> = [
    {
      id: 'TestH5TemplateId-1',
      clientType: ClientTypeEnum.H5,
      industryType: IndustryTypeEnum.RS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      bannerSize: [710, 240],
      groupRegistrationSize: [200, 360],
      reviewPath: 'demo-h5-homepage-1'
    },
    {
      id: 'rs-h5-001',
      clientType: ClientTypeEnum.H5,
      industryType: IndustryTypeEnum.RS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      bannerSize: [750, 320],
      groupRegistrationSize: [200, 360],
      reviewPath: 'demo-h5-homepage-2'
    }
  ]
  /**
   * 模板列表-建社PC
   */
  private _templateJSPCList: Array<TemplateItem> = [
    {
      id: 'TestTemplateId-1',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.JS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED, ColorEnum.ORANGE],
      logoSize: [900, 80],
      iconSize: [16, 16],
      customerPhoneSize: [280, 50],
      bannerSize: [1920, 400],
      H5QRCodeSize: [160, 160],
      trainingProcessSize: [1200, 120],
      linksSize: [400, 90],
      groupRegistrationSize: [200, 360],
      singleOnlineCollectSize: [1186, 150],
      doubleOnlineCollectSize: [589, 150],
      singleOfflineCollectSize: [1186, 150],
      doubleOfflineCollectSize: [589, 150],
      wechatCustomerServiceSize: [160, 160],

      reviewPath: 's-default',
      doubleOnlineExampleImgSrc: 'demo-default-xs-double.jpg',
      singleOnlineExampleImgSrc: 'demo-default-xs-single.jpg',
      doubleOfflineExampleImgSrc: 'demo-default-xx-double.jpg',
      singleOfflineExampleImgSrc: 'demo-default-xx-single.jpg'
    },
    {
      id: 'js-pc-001',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.JS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED, ColorEnum.ORANGE],
      logoSize: [900, 60],
      iconSize: [16, 16],
      customerPhoneSize: [280, 50],
      bannerSize: [860, 330],
      H5QRCodeSize: [100, 100],
      trainingProcessSize: [1050, 72],
      linksSize: [269, 59],
      groupRegistrationSize: [200, 360],
      singleOnlineCollectSize: [1200, 100],
      doubleOnlineCollectSize: [860, 100],
      singleOfflineCollectSize: [1200, 100],
      doubleOfflineCollectSize: [324, 100],
      wechatCustomerServiceSize: [160, 160],
      reviewPath: 's-template01',
      doubleOnlineExampleImgSrc: 'demo-template01-xs-double.jpg',
      singleOnlineExampleImgSrc: 'demo-template01-xs-single.jpg',
      doubleOfflineExampleImgSrc: 'demo-template01-xx-double.jpg',
      singleOfflineExampleImgSrc: 'demo-template01-xx-single.jpg'
    },
    {
      id: 'js-pc-002',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.JS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      logoSize: [900, 50],
      iconSize: [16, 16],
      customerPhoneSize: [200, 48],
      bannerSize: [630, 200],
      H5QRCodeSize: [100, 100],
      trainingProcessSize: [864, 50],
      linksSize: [280, 48],
      groupRegistrationSize: [200, 360],
      singleOnlineCollectSize: [1200, 100],
      doubleOnlineCollectSize: [590, 100],
      singleOfflineCollectSize: [1200, 100],
      doubleOfflineCollectSize: [590, 100],
      wechatCustomerServiceSize: [160, 160],
      reviewPath: 's-template02',
      doubleOnlineExampleImgSrc: 'demo-template02-xs-double.jpg',
      singleOnlineExampleImgSrc: 'demo-template02-xs-single.jpg',
      doubleOfflineExampleImgSrc: 'demo-template02-xx-double.jpg',
      singleOfflineExampleImgSrc: 'demo-template02-xx-single.jpg'
    },
    {
      id: 'js-pc-003',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.JS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      logoSize: [830, 90],
      iconSize: [16, 16],
      customerPhoneSize: [292, 112],
      bannerSize: [1200, 360],
      H5QRCodeSize: [100, 100],
      trainingProcessSize: [1200, 160],
      linksSize: [285, 60],
      groupRegistrationSize: [],
      singleOnlineCollectSize: [1200, 100],
      doubleOnlineCollectSize: [590, 100],
      singleOfflineCollectSize: [1200, 100],
      doubleOfflineCollectSize: [590, 100],
      wechatCustomerServiceSize: [160, 160],
      doubleOnlineExampleImgSrc: 'demo-template02-xs-double.jpg',
      singleOnlineExampleImgSrc: 'demo-template02-xs-single.jpg',
      doubleOfflineExampleImgSrc: 'demo-template02-xx-double.jpg',
      singleOfflineExampleImgSrc: 'demo-template02-xx-single.jpg',
      reviewPath: 's-template04'
    },
    {
      id: 'js-pc-004',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.JS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      logoSize: [560, 60],
      iconSize: [16, 16],
      customerPhoneSize: [220, 50],
      bannerSize: [1920, 480],
      H5QRCodeSize: [100, 100],
      trainingProcessSize: [1200, 160],
      linksSize: [400, 90],
      groupRegistrationSize: [],
      singleOnlineCollectSize: [1200, 100],
      doubleOnlineCollectSize: [590, 100],
      singleOfflineCollectSize: [1200, 100],
      doubleOfflineCollectSize: [590, 100],
      wechatCustomerServiceSize: [160, 160],
      doubleOnlineExampleImgSrc: 'demo-template02-xs-double.jpg',
      singleOnlineExampleImgSrc: 'demo-template02-xs-single.jpg',
      doubleOfflineExampleImgSrc: 'demo-template02-xx-double.jpg',
      singleOfflineExampleImgSrc: 'demo-template02-xx-single.jpg',
      reviewPath: 's-template05'
    },
    {
      id: 'js-pc-005',
      clientType: ClientTypeEnum.PC,
      industryType: IndustryTypeEnum.JS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      logoSize: [740, 70],
      iconSize: [16, 16],
      customerPhoneSize: [300, 60],
      bannerSize: [1920, 420],
      H5QRCodeSize: [100, 100],
      trainingProcessSize: [1200, 100],
      linksSize: [290, 52],
      groupRegistrationSize: [],
      singleOnlineCollectSize: [1200, 100],
      doubleOnlineCollectSize: [590, 100],
      singleOfflineCollectSize: [1200, 100],
      doubleOfflineCollectSize: [590, 100],
      wechatCustomerServiceSize: [160, 160],
      doubleOnlineExampleImgSrc: 'demo-template02-xs-double.jpg',
      singleOnlineExampleImgSrc: 'demo-template02-xs-single.jpg',
      doubleOfflineExampleImgSrc: 'demo-template02-xx-double.jpg',
      singleOfflineExampleImgSrc: 'demo-template02-xx-single.jpg',
      reviewPath: 's-template06'
    }
  ]
  /**
   * 模板列表-建设H5
   */
  private _templateJSH5List: Array<TemplateItem> = [
    {
      id: 'TestH5TemplateId-1',
      clientType: ClientTypeEnum.H5,
      industryType: IndustryTypeEnum.RS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      bannerSize: [710, 240],
      groupRegistrationSize: [200, 360],
      reviewPath: 'demo-h5-homepage-1'
    },
    {
      id: 'js-h5-001',
      clientType: ClientTypeEnum.H5,
      industryType: IndustryTypeEnum.JS,
      colorSchemes: [ColorEnum.PURPLE, ColorEnum.BLUE, ColorEnum.RED],
      bannerSize: [750, 320],
      groupRegistrationSize: [200, 360],
      reviewPath: 'demo-h5-homepage-2'
    }
  ]
  get templateJSH5List(): Array<TemplateItem> {
    return this._templateJSH5List
  }

  get templateJSPCList(): Array<TemplateItem> {
    return this._templateJSPCList
  }

  get templateRSH5List(): Array<TemplateItem> {
    return this._templateRSH5List
  }

  get templateRSPCList(): Array<TemplateItem> {
    return this._templateRSPCList
  }

  /**
   * 所有模板列表
   * @returns {TemplateItem[]}
   */
  get templateList(): Array<TemplateItem> {
    return [...this._templateRSPCList, ...this._templateRSH5List, ...this._templateJSPCList, ...this._templateJSH5List]
  }

  /**
   * 根据id获取模板
   * @param templateId
   * @returns {TemplateItem}
   */
  getTemplate(templateId: string): TemplateItem {
    return this.templateList.find((item) => item.id === templateId) || new TemplateItem()
  }
}

export default new TemplateModule()
