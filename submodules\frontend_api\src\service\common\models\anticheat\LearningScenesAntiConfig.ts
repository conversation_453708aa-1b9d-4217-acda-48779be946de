import FaceRecognitionShape from '@api/service/common/models/anticheat/FaceRecognitionShape'
import ProcessBehavior from '@api/service/common/models/anticheat/ProcessBehavior'

/**
 * 防作弊配置
 */
class LearningScenesAntiConfig {
  /**
   * 防作弊配置编号
   */
  id: string
  /**
   * 应用范围，0/1/2/3/4，子项目/单位/组织机构/具体资源/平台
   */
  useRange: number
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 当前只支持人脸识别模式
   */
  shapeModel: FaceRecognitionShape
  /**
   * 学习前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLearningBehavior: number
  /**
   * 学习中监管行为
   */
  processLearningBehavior: ProcessBehavior
  /**
   * 学习后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterLearningBehavior: number
  /**
   * 配置时间
   */
  createTime: Date
  /**
   * 创建人编号
   */
  createUserId: string
  /**
   * 最后更新人
   */
  updateUserId: string
  /**
   * 最后更新时间
   */
  updateTime: Date

  /**
   * 不匹配进度是否有效
   */
  effectiveIfNoMatch: boolean
}

export default LearningScenesAntiConfig
