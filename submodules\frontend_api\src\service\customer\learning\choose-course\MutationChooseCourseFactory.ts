import MutationSelectedCourse from '@api/service/customer/learning/choose-course/mutation/MutationSelectedCourse'
import MutationChooseCourseRule from '@api/service/customer/learning/choose-course/mutation/MutationChooseCourseRule'
import MutationCourseTrainingOutline from '@api/service/customer/learning/choose-course/mutation/MutationCourseTrainingOutline'
import MutationWaitChooseCourse from '@api/service/customer/learning/choose-course/mutation/MutationWaitChooseCourse'
import MutationEnterChooseCourse from '@api/service/customer/learning/choose-course/mutation/MutationEnterChooseCourse'

/**
 * @description 选课业务工厂
 */
class MutationChooseCourseFactory {
  /**
   * 已选课程业务类
   */
  get selectedCourse() {
    return new MutationSelectedCourse()
  }

  /**
   * 选课规则业务类
   */
  get chooseCourseRule() {
    return MutationChooseCourseRule
  }

  /**
   * 课程学习大纲业务类
   */
  get courseTrainingOutline() {
    return new MutationCourseTrainingOutline()
  }

  /**
   * 可选待选课程列表业务类
   */
  get waitChooseCourse() {
    return MutationWaitChooseCourse
  }

  /**
   * 申请选课业务
   */
  get enterChooseCourse() {
    return MutationEnterChooseCourse
  }
}

export default MutationChooseCourseFactory
