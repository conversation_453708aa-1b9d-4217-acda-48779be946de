import { AttendanceTypeEnum } from '@api/service/management/implement/enums/AttendanceTypeEnum'
import DateScope from '@api/service/common/models/DateScope'
import { SchemeIssuePlanItemResponse } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

export default class AttendanceQRCodeDto {
  /**
   * 教学计划id
   */
  planId: string = undefined

  /**
   * 教学计划项id
   */
  planItemId: string = undefined

  /**
   * 教学计划组id
   */
  planItemGroupId: string = undefined

  /**
   * 日期
   */
  date: string = undefined

  /**
   * 课程名称
   */
  courseName: string = undefined

  /**
   * 考勤类型
   */
  attendanceType: AttendanceTypeEnum = undefined

  /**
   * 签到/签退时段
   */
  checkTime: DateScope = new DateScope()

  /**
   * 考勤二维码（UI自持）
   */
  qrCode: string = undefined

  static from(dto: SchemeIssuePlanItemResponse) {
    const vo = new AttendanceQRCodeDto()
    const { planItemConfig, planItemSignPoint } = dto
    if (planItemConfig) {
      vo.planId = planItemConfig.planId
      vo.planItemId = planItemConfig.planItemId
      vo.courseName = planItemConfig.planItemName
      vo.planItemGroupId = planItemConfig.planItemGroupId
    }
    if (planItemSignPoint) {
      if (planItemSignPoint.startTime) {
        vo.date = planItemSignPoint.startTime.split(' ')[0]
        vo.attendanceType = planItemSignPoint.signType
        vo.checkTime.begin = planItemSignPoint.startTime.split(' ')[1]
      }
      if (planItemSignPoint.endTime) {
        vo.checkTime.end = planItemSignPoint.endTime.split(' ')[1]
      }
    }

    return vo
  }
}
