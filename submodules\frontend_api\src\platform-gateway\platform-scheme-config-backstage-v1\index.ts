import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-scheme-config-backstage-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-scheme-config-backstage-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 获取我的班级请求参数
 */
export class PlatformSchemeConfigRequest {
  /**
   * 方案id集合
   */
  schemeIds?: Array<string>
  /**
   * 是否包含已配置学习规则
   */
  isIncludeHasStudyRule?: boolean
  /**
   * 培训形式
   */
  trainType?: string
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 方案归属行业
   */
  industryId?: string
}

export class SchemeConfigResponse {
  schemeId: string
  owner: OwnerResponse
  schemeConfig: string
  skuProperty: SchemeSkuPropertyResponse
  name: string
  hasAntiConfig: boolean
  antiConfigId: string
  intro: string
}

export class BatchOwnerResponse {
  unitId: string
  userId: string
}

export class OwnerResponse {
  platformId: string
  platformVersionId: string
  projectId: string
  subProjectId: string
  unitId: string
  servicerType: number
  servicerId: string
  batchOwner: BatchOwnerResponse
}

export class SchemeSkuPropertyResponse {
  year: SchemeSkuPropertyValueResponse
  province: SchemeSkuPropertyValueResponse
  city: SchemeSkuPropertyValueResponse
  county: SchemeSkuPropertyValueResponse
  industry: SchemeSkuPropertyValueResponse
  subjectType: SchemeSkuPropertyValueResponse
  trainingCategory: SchemeSkuPropertyValueResponse
  trainingProfessional: SchemeSkuPropertyValueResponse
  technicalGrade: SchemeSkuPropertyValueResponse
  positionCategory: SchemeSkuPropertyValueResponse
  trainingObject: SchemeSkuPropertyValueResponse
  jobLevel: SchemeSkuPropertyValueResponse
  jobCategory: SchemeSkuPropertyValueResponse
  subject: SchemeSkuPropertyValueResponse
  grade: SchemeSkuPropertyValueResponse
  learningPhase: SchemeSkuPropertyValueResponse
  discipline: SchemeSkuPropertyValueResponse
  certificatesType: SchemeSkuPropertyValueResponse
  practitionerCategory: SchemeSkuPropertyValueResponse
  trainingInstitution: SchemeSkuPropertyValueResponse
  mainAdditionalItem: SchemeSkuPropertyValueResponse
  trainingWay: SchemeSkuPropertyValueResponse
}

export class SchemeSkuPropertyValueResponse {
  skuPropertyValueId: string
}

/**
 * 考勤配置模板
 */
export class TrainingConfigTemplateResponse {
  /**
   * 考勤模板id
   */
  attendanceTemplateId: string
  /**
   * 签到点信息
   */
  attendanceSignIn: AttendanceSignResponse
  /**
   * 签退点信息
   */
  attendanceSignOut: AttendanceSignResponse
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
}

/**
 * 签到点信息
 */
export class AttendanceSignResponse {
  /**
   * 是否开启
   */
  enable: boolean
  /**
   * 签到频率
半天  1;
每节课  2;
@see SignFrequencyTypes
   */
  frequency: number
  /**
   * 签到半径
   */
  radius: number
  /**
   * 签到开始前
单位:(秒)
   */
  beforeSecond: number
  /**
   * 开始后
单位:(秒)
   */
  afterSecond: number
}

export class SchemeConfigResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SchemeConfigResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询当前网校下的训前规则配置模板
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTrainingConfigTemplateInServicer(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTrainingConfigTemplateInServicer,
    operation?: string
  ): Promise<Response<TrainingConfigTemplateResponse>> {
    return commonRequestApi<TrainingConfigTemplateResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取方案配置
   * @param page
   * @param request
   * @param needField
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSchemeConfigByRequestInServicer(
    params: { page?: Page; request?: PlatformSchemeConfigRequest; needField?: Array<string> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSchemeConfigByRequestInServicer,
    operation?: string
  ): Promise<Response<SchemeConfigResponsePage>> {
    return commonRequestApi<SchemeConfigResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
