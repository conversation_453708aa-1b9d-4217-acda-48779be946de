<!--
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2023-07-10 16:12:23
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-04-15 09:05:54
 * @Description: 选择培训方案
-->
<template>
  <el-drawer
    title="选择培训方案"
    :visible.sync="openTrainSchemeDrawer"
    direction="rtl"
    size="1000px"
    custom-class="m-drawer"
  >
    <div class="drawer-bd">
      <el-row :gutter="16" class="m-query f-mt10">
        <el-form :inline="true" label-width="100px">
          <el-col :span="8">
            <el-form-item label="培训方案形式">
              <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="培训方案名称">
              <el-input clearable placeholder="请输入培训方案名称" v-model="queryParams.schemeName" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item>
              <el-button @click="search" type="primary">查询</el-button>
              <el-checkbox v-model="removeExistScheme" class="f-ml20">剔除已配置监管规则的方案</el-checkbox>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table stripe :data="allSchemeList" class="m-table" ref="schemeList">
        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
        <el-table-column label="培训方案名称" min-width="280">
          <template slot-scope="scope">{{ scope.row.schemeName }}</template>
        </el-table-column>
        <el-table-column label="属性" min-width="220">
          <template slot-scope="scope">
            <p v-if="getSkuPropertyName(scope.row, 'industry')">
              行业：{{ getSkuPropertyName(scope.row, 'industry') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'region')">地区：{{ getSkuPropertyName(scope.row, 'region') }}</p>
            <p v-if="getSkuPropertyName(scope.row, 'technicalGrade')">
              技术等级：{{ getSkuPropertyName(scope.row, 'technicalGrade') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
              科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
            </p>
            <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
              培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
              培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
            </p>
            <p v-if="getSkuPropertyName(scope.row, 'year')">培训年度：{{ getSkuPropertyName(scope.row, 'year') }}</p>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="changeCheckStatus(scope.row)" v-if="!scope.row.hasAntiConfig">
              {{ isChecked(scope.row) ? '取消选择' : '选择' }}
            </el-button>
            <el-button v-else type="text" size="mini" disabled style="color:gray">
              已配置其他方案监管规则
            </el-button>
            <!-- <el-checkbox-group v-model="schemeIdList">
              <el-checkbox :checked="changeCheckStatus(scope.row)">选择</el-checkbox>
            </el-checkbox-group> -->
            <!-- <el-checkbox-group v-model="idList" @change="changeShow">
              <el-checkbox :label="scope.row.id" :checked="itemIsSelected(scope.row.id)">选择</el-checkbox>
            </el-checkbox-group> -->
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
      <div class="m-btn-bar f-tc f-mt20">
        <el-button @click="openTrainSchemeDrawer = false">取消</el-button>
        <el-button @click="save" type="primary">确定</el-button>
      </div>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Prop, PropSync } from 'vue-property-decorator'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import AntiSchemeParams from '@api/service/management/train-class/query/vo/AntiSchemeParams'
  import AntiSchemeItem from '@api/service/management/train-class/query/vo/AntiSchemeItem'

  import { UiPage } from '@hbfe/common'
  import { cloneDeep } from 'lodash'
  @Component({})
  export default class extends Vue {
    // 是否打开抽屉
    // @PropSync('addTrainScheme', {
    //   type: Boolean,
    //   default: false
    // })
    openTrainSchemeDrawer = false

    // 学习方案列表(传入已选)
    @PropSync('schemeList', {
      type: Array,
      default: () => Array<AntiSchemeItem>()
    })
    schemeItemList: Array<AntiSchemeItem>
    /**
     * 培训方案类型（形式）
     */
    schemeTypeInfo: Array<string> = new Array<string>()
    // 查询方案列表方法
    queryTrainClassCommodityList = new QueryTrainClassCommodityList()
    // 方案名称
    schemeName = ''
    // 是否剔除已存在方案
    removeExistScheme = false
    // 方案列表
    allSchemeList = new Array<AntiSchemeItem>()
    // 选中的方案
    newSchemeItemList = new Array<AntiSchemeItem>()
    // 查询参数
    queryParams = new AntiSchemeParams()
    // 是否选择方案
    isCheck = false
    // 分页
    page: UiPage
    constructor() {
      super()
      this.page = new UiPage(this.querySchemeList, this.querySchemeList)
    }

    // async created() {
    //   this.querySchemeList()
    // }
    async init() {
      this.openTrainSchemeDrawer = true
      this.schemeTypeInfo = new Array<string>()
      this.querySchemeList()
      this.newSchemeItemList = cloneDeep(this.schemeItemList)
    }

    // 查询方案列表
    async querySchemeList() {
      this.configureTrainSchemeQueryParam()
      this.queryParams.isIncludeHasAntiScheme = !this.removeExistScheme
      this.allSchemeList = await this.queryTrainClassCommodityList.pageAntiSchemeList(this.page, this.queryParams)
      ;(this.$refs['schemeList'] as any)?.doLayout()
    }

    // 查询
    async search() {
      this.page.pageNo = 1
      this.querySchemeList()
    }

    // 提交
    save() {
      this.$emit('isCheckIdList', this.newSchemeItemList)
      this.openTrainSchemeDrawer = false
    }

    // 是否选中
    get isChecked() {
      return (item: AntiSchemeItem) => {
        const res = this.newSchemeItemList.map(scheme => scheme.schemeId).indexOf(item.schemeId) > -1
        return res
      }
    }
    // 选中状态
    changeCheckStatus(item: AntiSchemeItem) {
      const index = this.newSchemeItemList.indexOf(item)
      if (index > -1) {
        this.newSchemeItemList.splice(index, 1)
      } else {
        this.newSchemeItemList.push(item)
      }
    }
    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: AntiSchemeItem, type: string): string {
      if (row.sku[type]?.skuPropertyName) {
        return row.sku[type].skuPropertyName

        // const valuesArr = value.split('/'),
        //   lastIndex = valuesArr.length - 1
        // return type === 'trainingMajor' ? valuesArr[lastIndex] : value
      }
      return ''
    }

    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.queryParams.trainType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      // if (schemeType === 'chooseCourseLearning' || schemeType === 'autonomousCourseLearning') {
      this.queryParams.trainType = schemeType
      // } else {
      //   this.queryParams.trainType = undefined
      // }
    }
  }
</script>
