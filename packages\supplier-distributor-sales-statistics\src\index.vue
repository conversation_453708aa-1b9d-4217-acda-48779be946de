<template>
  <div
    class="f-p15"
    v-if="$hasPermission('queryDistributorSalesStatisticsData')"
    desc="查询分销商品销售统计"
    query
    actions="search,@BizPortalSelect,@BizDistributorSelect"
  >
    <el-card shadow="never" class="m-card f-mb15">
      <hb-search-wrapper class="m-query is-border-bottom" @reset="resetSearch">
        <el-form-item label="分销商品">
          <el-input
            v-model="supplierDistributorSalesStatisticsData.param.distributedTradeName"
            clearable
            placeholder="请输入分销商品名称"
          />
        </el-form-item>
        <el-form-item label="分销商">
          <biz-distributor-select
            v-model="supplierDistributorSalesStatisticsData.param.distributorId"
            :name="supplierDistributorSalesStatisticsData.param.distributorName"
          ></biz-distributor-select>
        </el-form-item>
        <el-form-item label="推广门户简称">
          <biz-portal-select
            :disabled="supplierDistributorSalesStatisticsData.param.isPortalData"
            v-model="supplierDistributorSalesStatisticsData.param.portalPromoteId"
            :name="supplierDistributorSalesStatisticsData.param.portalPromoteTheName"
          ></biz-portal-select>
        </el-form-item>
        <el-form-item label="报名时间">
          <el-date-picker
            v-model="dates"
            type="datetimerange"
            range-separator="~"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="报名成功时间"
            end-placeholder="报名成功时间"
            :default-time="['00:00:00', '23:59:59']"
          >
          </el-date-picker>
        </el-form-item>
        <slot
          name="remove-training-plan"
          :supplierDistributorSalesStatisticsData="supplierDistributorSalesStatisticsData"
        >
        </slot>
        <el-form-item>
          <el-checkbox
            label="查看非门户推广数据"
            name="type"
            v-model="supplierDistributorSalesStatisticsData.param.isPortalData"
          ></el-checkbox>
        </el-form-item>
        <template slot="actions">
          <el-button
            type="primary"
            @click="exportData"
            v-if="$hasPermission('exportDataDistributorSalesStatisticsData')"
            desc="查询分销商品销售统计导出"
            mutation
            actions="exportData"
            >导出</el-button
          >
          <el-button type="primary" @click="init">查询</el-button>
        </template>
      </hb-search-wrapper>
      <!--操作栏-->
      <div class="f-mt20">
        <el-alert type="warning" :closable="false" class="m-alert f-clear">
          <div class="f-c6 f-fl">
            搜索结果合计：当前分销推广净开通
            <span class="f-fb f-co">{{ supplierDistributorSalesStatisticsData.staticData.netTurnOn }}</span>
            人次，成交总额
            <span class="f-fb f-co">¥ {{ supplierDistributorSalesStatisticsData.staticData.transactionTotal }}</span>
          </div>
          <div class="f-fr f-csp f-flex f-align-center" @click="statisticalCaliberDeclarationDrawer.openDrawer()">
            <i class="el-icon-info f-f16 f-mr5"></i>统计说明
          </div>
        </el-alert>
      </div>
      <!--表格-->
      <el-table
        stripe
        ref="supplierDistributorSalesStatisticsRef"
        :data="supplierDistributorSalesStatisticsData.list"
        max-height="600"
        class="m-table f-mt10"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="分销商" min-width="180" fixed="left">
          <template v-slot="{ row }">{{ row.distributorName }}</template>
        </el-table-column>
        <el-table-column label="合计" header-align="center">
          <el-table-column label="开通" min-width="90" align="right">
            <template v-slot="{ row }">{{ row.total.open }}</template>
          </el-table-column>
          <el-table-column label="退班" min-width="90" align="right">
            <template v-slot="{ row }">{{ row.total.return }}</template>
          </el-table-column>
          <el-table-column label="净开通" min-width="90" align="right">
            <template v-slot="{ row }">{{ row.total.count }}</template>
          </el-table-column>
          <el-table-column label="分销总额" min-width="100" align="right">
            <template v-slot="{ row }">{{ row.total.distributionAmount }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="个人缴费" header-align="center">
          <el-table-column label="线上支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.individualOnline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.individualOnline.return }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.individualOnline.count }}</template>
            </el-table-column>
            <el-table-column label="分销金额" min-width="100" align="right">
              <template v-slot="{ row }">{{ row.individualOnline.distributionAmount }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="集体报名" header-align="center">
          <el-table-column label="线上支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.collectivelyOnline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.collectivelyOnline.return }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.collectivelyOnline.count }}</template>
            </el-table-column>
            <el-table-column label="分销金额" min-width="100" align="right">
              <template v-slot="{ row }">{{ row.collectivelyOnline.distributionAmount }}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="线下支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.collectivelyOffline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.collectivelyOffline.return }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.collectivelyOffline.count }}</template>
            </el-table-column>
            <el-table-column label="分销金额" min-width="100" align="right">
              <template v-slot="{ row }">{{ row.collectivelyOffline.distributionAmount }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="导入开通" header-align="center">
          <el-table-column label="线下支付" header-align="center">
            <el-table-column label="开通" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.importOffline.open }}</template>
            </el-table-column>
            <el-table-column label="退班" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.importOffline.return }}</template>
            </el-table-column>
            <el-table-column label="净开通" min-width="90" align="right">
              <template v-slot="{ row }">{{ row.importOffline.count }}</template>
            </el-table-column>
            <el-table-column label="分销金额" min-width="100" align="right">
              <template v-slot="{ row }">{{ row.importOffline.distributionAmount }}</template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
      <hb-pagination :page="page" v-bind="page" />
    </el-card>
    <statistical-caliber-declaration-drawer
      ref="statisticalCaliberDeclarationDrawer"
      :search-data="supplierDistributorSalesStatisticsSearchData"
      :field-data="supplierDistributorSalesStatisticsFieldData"
    />
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务查看，分销商销售统计！</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import StatisticalCaliberDeclarationDrawer from '@hbfe/jxjy-admin-components/src/statistical-caliber-declaration-drawer/index.vue'
  import {
    supplierDistributorSalesStatisticsFieldData,
    supplierDistributorSalesStatisticsSearchData
  } from '@hbfe/jxjy-admin-components/src/statistical-caliber-declaration-drawer/statisticalExplanatoryData'
  import SupplierDistributorSalesStatisticsData from '@api/service/management/statisticalReport/DistributorSalesStatistics/SupplierDistributorSalesStatistics'
  import SupplierDistributorSalesStatisticsParams from '@api/service/management/statisticalReport/DistributorSalesStatistics/model/SupplierDistributorSalesStatisticsParams'
  import { UiPage } from '@hbfe/common'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import { ElTable } from 'element-ui/types/table'

  @Component({
    components: { StatisticalCaliberDeclarationDrawer, BizDistributorSelect, BizPortalSelect }
  })
  export default class SupplierDistributorSalesStatistics extends Vue {
    @Ref('supplierDistributorSalesStatisticsRef') supplierDistributorSalesStatisticsRef: ElTable
    constructor() {
      super()
      this.page = new UiPage(this.search, this.search)
    }
    // 统计口径说明抽屉ref
    @Ref('statisticalCaliberDeclarationDrawer') statisticalCaliberDeclarationDrawer: StatisticalCaliberDeclarationDrawer
    // 供应商分销商销售统计——搜索字段说明
    get supplierDistributorSalesStatisticsSearchData() {
      return supplierDistributorSalesStatisticsSearchData
    }
    // 供应商分销商销售统计——列表字段说明
    get supplierDistributorSalesStatisticsFieldData() {
      return supplierDistributorSalesStatisticsFieldData
    }
    page: UiPage // 分页
    // 供应商——分销商销售统计模型
    supplierDistributorSalesStatisticsData = new SupplierDistributorSalesStatisticsData()

    async created() {
      await this.init()
    }

    /**
     * 初始化
     */
    async init() {
      this.page.pageNo = 1
      await this.search()
    }

    dates: Array<string> = []
    /**
     * 搜索列表
     */
    async search() {
      if (this.dates?.length) {
        this.supplierDistributorSalesStatisticsData.param.registrationPeriod.begin = this.dates[0]
        this.supplierDistributorSalesStatisticsData.param.registrationPeriod.end = this.dates[1]
      } else {
        this.supplierDistributorSalesStatisticsData.param.registrationPeriod.begin = ''
        this.supplierDistributorSalesStatisticsData.param.registrationPeriod.end = ''
      }
      await this.supplierDistributorSalesStatisticsData.queryList(this.page)
      await this.supplierDistributorSalesStatisticsData.queryTotalStatic()
      this.$nextTick(() => {
        this.supplierDistributorSalesStatisticsRef.doLayout()
      })
    }
    exportSuccessVisible = false
    /**
     * 导出数据
     */
    async exportData() {
      try {
        if (this.dates?.length) {
          this.supplierDistributorSalesStatisticsData.param.registrationPeriod.begin = this.dates[0]
          this.supplierDistributorSalesStatisticsData.param.registrationPeriod.end = this.dates[1]
        } else {
          this.supplierDistributorSalesStatisticsData.param.registrationPeriod.begin = ''
          this.supplierDistributorSalesStatisticsData.param.registrationPeriod.end = ''
        }

        const res = await await this.supplierDistributorSalesStatisticsData.exportList()
        if (res.status.code == 200 && res.data) {
          //   this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        //console.log(e)
      } finally {
        //todo
      }
    }

    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportDistributorSales' }
      })
    }

    /**
     * 重置
     */
    async resetSearch() {
      this.supplierDistributorSalesStatisticsData.param = new SupplierDistributorSalesStatisticsParams()
      this.supplierDistributorSalesStatisticsData.param.isPortalData = null
      this.dates = []
      await this.init()
    }
  }
</script>
