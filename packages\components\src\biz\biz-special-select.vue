<route-meta>
{
"title": "专题类别选择器"
}
</route-meta>
<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="selected = undefined"
    class="el-input"
    filterable
    clearable
  >
    <el-option v-for="item in options" :label="item.label" :value="item.value" :key="item.value"></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    selected: boolean = null

    options = [
      {
        label: '是',
        value: true
      },
      {
        label: '否',
        value: false
      }
    ]

    @Prop({
      type: String,
      default: '请选择订单是否来源专题'
    })
    placeholder: string
    @Prop({
      type: Boolean,
      default: null
    })
    value: boolean

    /**
     * 监听参数变化
     * @param val
     */
    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: boolean) {
      this.selected = val
    }

    /**
     * 监听选择值
     */
    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }
  }
</script>
