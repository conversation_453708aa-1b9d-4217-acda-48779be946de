schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""课程分类名称是否存在
		@param name
		@param id
		@return
	"""
	courseCategoryNameExists(parentId:String,name:String,id:String):Boolean!
	"""课程名称是否存在
		@param name
		@param id
		@return
	"""
	courseNameExists(name:String,id:String):Boolean!
	"""课程包名称是否存在
		@param name
		@param id
		@return
	"""
	coursePoolNameExists(name:String,id:String):Boolean!
	"""课件分类名称是否存在
		@param parentId
		@param name
		@param id
		@return
	"""
	courseWareCategoryNameExists(parentId:String,name:String,id:String):Boolean!
	"""课件名称是否存在
		@param name
		@param id
		@return
	"""
	courseWareNameExists(name:String,id:String):Boolean!
	"""获取课程信息
		@param id
		@return
	"""
	getCourse(id:String):CourseResponse
	"""获取课程分类
		@param id
		@return
	"""
	getCourseCategory(id:String):CourseCategoryResponse @NotAuthenticationRequired
	"""获取课程池内课程列表
		@param courseInPoolQueryDTO
		@return
	"""
	getCourseInPoolList(courseInPoolQueryDTO:CourseInPoolQueryDTO):[CourseInPoolItemResponse] @NotAuthenticationRequired
	"""获取课程池内课程分页
		@param page
		@param courseInPoolQueryDTO
		@return
	"""
	getCourseInPoolPage(page:Page,courseInPoolQueryDTO:CourseInPoolQueryDTO):CourseInPoolItemResponsePage @page(for:"CourseInPoolItemResponse")
	"""获取课程分页
		@param page
		@param query
		@return
	"""
	getCoursePage(page:Page,query:CourseQueryDTO):CourseItemResponsePage @page(for:"CourseItemResponse") @NotAuthenticationRequired
	"""查询课程池dto
		@param poolId
		@return
	"""
	getCoursePoolDTO(poolId:String):CoursePoolResponse
	"""分页查询课程池
		@param page
		@param query
		@return
	"""
	getCoursePoolPage(page:Page,query:CoursePoolQueryDTO,authorizedQuery:ResAuthorizedQuery):CoursePoolItemResponsePage @page(for:"CoursePoolItemResponse")
	"""获取课件
		@param id
		@return
	"""
	getCourseWare(id:String):CourseWareResponse
	"""获取课件分类详情
		@param id
		@return
	"""
	getCourseWareCategory(id:String):CourseWareCategoryResponse
	"""获取课件分类列表
		@param parentId
		@param unitId
		@return
	"""
	getCourseWareCategoryListByParentId(parentId:String,unitId:String):[CourseWareCategoryListResponse]
	getCourseWarePage(page:Page,query:CourseWareQueryDTO,authorizedQuery:ResAuthorizedQuery):CourseWareItemResponsePage @page(for:"CourseWareItemResponse")
	"""获取课件提供商
		@return
	"""
	getCourseWareSupplier:[SupplierResponse]
	"""获取弹窗题
		@param id
		@return
	"""
	getPopQuestion(id:String):PopQuestionResponse
	"""获取子课程分类
		@param parentId
		@param unitId
		@return
	"""
	getSubCourseCategory(parentId:String,unitId:String):[CourseCategoryListResponse] @NotAuthenticationRequired
	"""获取教师
		@param id
		@return
	"""
	getTeacher(id:String):TeacherResponse
	"""分页查询教师
		@param page
		@param query
		@return
	"""
	getTeacherPage(page:Page,query:TeacherQueryDTO):TeacherItemResponsePage @page(for:"TeacherItemResponse")
	"""获取课件下所有弹窗题
		@param courseWareId
		@return
	"""
	listAllCourseWarePopQuestions(courseWareId:String):[PopQuestionResponse]
	"""是否被当作必修包使用"""
	usedAsCompulsoryPackage(poolId:String):Boolean!
}
type Mutation {
	"""添加课程进课程包
		@param poolId
		@param creates
	"""
	addCourseIntoPool(poolId:String,creates:[CourseInPoolCreateRequest]):Void
	"""新建课程目录
		@param creates
	"""
	addCourseOutline(courseId:String,creates:[CourseOutLineCreateRequest]):[CourseOutLineResponse]
	"""复制课程包
		@param id
		@param newName
		@return
	"""
	copyCoursePool(id:String,newName:String):CoursePoolDTO
	"""创建课程
		@param create
		@return
	"""
	createCourse(create:CourseCreateRequest):CourseResponse
	"""创建课程分类
		@param create
		@return
	"""
	createCourseCategory(create:CourseCategoryCreateRequest):CourseCategoryResponse
	"""新建课程包
		@param create
		@return
	"""
	createCoursePool(create:CoursePoolCreateRequest):CoursePoolResponse
	"""创建课件
		@param create
		@return
	"""
	createCourseWare(create:CourseWareCreateRequest):CourseWareResponse
	"""创建课件分类
		@param create
		@return
	"""
	createCourseWareCategory(create:CourseWareCategoryCreateRequest):CourseWareCategoryResponse
	"""创建弹窗题
		@param create
		@return
	"""
	createPopQuestion(create:PopQuestionCreateRequest):PopQuestionResponse
	"""创建教师
		@return
	"""
	createTeacher(create:TeacherCreateRequest):TeacherResponse
	"""删除课程
		@param id
	"""
	deleteCourse(id:String):Void
	"""删除课程分类
		@param id
	"""
	deleteCourseCategory(id:String):Void
	"""删除课程包内课程
		@param poolId
		@param deleteIds
	"""
	deleteCourseIntoPool(poolId:String,deleteIds:[String]):Void
	"""删除课程目录
		@param outlineIds
	"""
	deleteCourseOutline(courseId:String,outlineIds:[String]):Void
	"""删除课程包
		@param poolId
	"""
	deleteCoursePool(poolId:String):Void
	"""删除课件
		@param id
	"""
	deleteCourseWare(id:String):Void
	"""删除课件分类
		@param id
	"""
	deleteCourseWareCategory(id:String):Void
	"""删除教师
		@param id
	"""
	deleteTeacher(id:String):Void
	"""停用课程
		@param courseId
	"""
	disableCourse(courseId:String):Void
	"""停用课件
		@param id
	"""
	disableCourseWare(id:String):Void
	"""启用课程
		@param courseId
	"""
	enableCourse(courseId:String):Void
	"""启用课件
		@param id
	"""
	enableCourseWare(id:String):Void
	"""交换课程目录
		@param courseId
		@param firstOutlineId
		@param secondOutlineId
	"""
	exchangeCourseOutlineSort(courseId:String,firstOutlineId:String,secondOutlineId:String):Void
	"""移动课程包内课程
		@param poolId
		@param courseId
		@param direction
	"""
	moveCourseInPool(poolId:String,courseId:String,direction:Int):Void
	"""移除弹窗题
		@param id
		@return
	"""
	removePopQuestion(id:String):Void
	"""修改课程
		@param update
		@return
	"""
	updateCourse(update:CourseUpdateRequest):CourseResponse
	"""更新课程分类
		@param update
		@return
	"""
	updateCourseCategory(update:CourseCategoryUpdateRequest):CourseCategoryResponse
	"""修改课程包内课程
		@param poolId
		@param updates
	"""
	updateCourseIntoPool(poolId:String,updates:[CourseInPoolUpdateRequest]):Void
	"""修改课程目录
		@param updates
	"""
	updateCourseOutline(courseId:String,updates:[CourseUpdateRequest]):[CourseOutLineResponse]
	"""修改课程包
		@param update
		@return
	"""
	updateCoursePool(update:CoursePoolUpdateRequest):CoursePoolResponse
	"""修改课件信息
		@param update
		@return
	"""
	updateCourseWare(update:CourseWareUpdateRequest):CourseWareResponse
	"""修改课件分类
		@param update
		@return
	"""
	updateCourseWareCategory(update:CourseWareCategoryUpdateRequest):CourseWareCategoryResponse
	"""修改弹窗题
		@param update
		@return
	"""
	updatePopQuestion(update:PopQuestionUpdateRequest):PopQuestionResponse
	"""修改教师信息
		@param update
		@return
	"""
	updateTeacher(update:TeacherUpdateRequest):TeacherResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input MarkerDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.MarkerDTO") {
	key:String
	value:String
}
input Sort @type(value:"com.fjhb.platform.core.course.v1.api.dto.Sort") {
	field:String
	dir:String
}
input CourseQueryDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.course.CourseQueryDTO") {
	name:String
	status:Int!
	enable:Int!
	resourceUploadType:Int!
	startCreateTime:String
	endCreateTime:String
	categoryId:String
	categoryIdList:[String]
	periodBegin:Double
	periodEnd:Double
	excludeCourseIdList:[String]
	includeCourseIdList:[String]
}
input CoursePoolQueryDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.coursepool.CoursePoolQueryDTO") {
	poolName:String
	exact:Boolean!
	poolState:Int!
	createStartTime:DateTime
	createEndTime:DateTime
	orderByField:Int!
	descending:Boolean!
	poolIdList:[String]
	excludePoolIdList:[String]
}
input CourseInPoolQueryDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.coursepool.courseinpool.CourseInPoolQueryDTO") {
	poolIdList:[String]
	courseName:String
	courseCatalogIdList:[String]
}
input CourseWareQueryDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.courseware.CourseWareQueryDTO") {
	name:String
	type:Int!
	categoryId:String
	supplierId:String
	needHasQuestion:Int!
	isUsable:Int!
	status:Int!
	startCreateTime:String
	endCreateTime:String
	statusList:[Int]
	sort:[Sort]
	unitId:String
	courseId:String
}
input TeacherQueryDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.teacher.TeacherQueryDTO") {
	name:String
	createUserId:String
}
"""<AUTHOR> create 2020/4/21 7:34"""
input CourseCreateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.course.CourseCreateRequest") {
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""分类id"""
	categoryIds:[String]
	"""课程简介"""
	abouts:String
	"""是否启用"""
	enabled:Boolean!
	"""课程详情"""
	contents:String
	"""课程目录"""
	courseOutline:[CourseOutLineWithSubOutlineCreateRequest]
	"""计划授课讲数"""
	plannedLecturesNum:Int!
	"""教师ids"""
	teacherIds:[String]
}
"""
	<AUTHOR> create 2020/4/21 7:36
"""
input CourseOutLineCreateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.course.CourseOutLineCreateRequest") {
	"""课程id"""
	courseId:String
	"""挂载课件id"""
	courseWareId:String
	"""课件"""
	courseWare:CourseWareCreateRequest
	"""目录名称"""
	name:String
	"""上级目录ID"""
	parentId:String
	"""排序"""
	sort:Int!
	"""挂在课件是否支持试听 0不可以试听，1可以试听，"""
	customeStatus:Int!
}
"""
	<AUTHOR> create 2020/4/21 7:36
"""
input CourseOutLineUpdateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.course.CourseOutLineUpdateRequest") {
	"""目录id"""
	id:String
	"""课程id"""
	courseId:String
	"""挂载课件id"""
	courseWareId:String
	"""课件"""
	courseWare:CourseWareCreateRequest
	"""目录名称"""
	name:String
	"""上级目录ID"""
	parentId:String
	"""排序"""
	sort:Int!
	"""挂在课件是否支持试听 0不可以试听，1可以试听，"""
	customeStatus:Int!
}
"""
	<AUTHOR> create 2020/4/21 7:37
"""
input CourseOutLineWithSubOutlineCreateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.course.CourseOutLineWithSubOutlineCreateRequest") {
	"""子目录"""
	subCourseOutlines:[CourseOutLineCreateRequest]
	"""课程id"""
	courseId:String
	"""挂载课件id"""
	courseWareId:String
	"""课件"""
	courseWare:CourseWareCreateRequest
	"""目录名称"""
	name:String
	"""上级目录ID"""
	parentId:String
	"""排序"""
	sort:Int!
	"""挂在课件是否支持试听 0不可以试听，1可以试听，"""
	customeStatus:Int!
}
"""<AUTHOR> create 2020/4/21 7:37"""
input CourseOutLineWithSubOutlineUpdateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.course.CourseOutLineWithSubOutlineUpdateRequest") {
	"""子目录"""
	subCourseOutlines:[CourseOutLineUpdateRequest]
	"""目录id"""
	id:String
	"""课程id"""
	courseId:String
	"""挂载课件id"""
	courseWareId:String
	"""课件"""
	courseWare:CourseWareCreateRequest
	"""目录名称"""
	name:String
	"""上级目录ID"""
	parentId:String
	"""排序"""
	sort:Int!
	"""挂在课件是否支持试听 0不可以试听，1可以试听，"""
	customeStatus:Int!
}
"""
	<AUTHOR> create 2020/4/21 7:38
"""
input CourseUpdateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.course.CourseUpdateRequest") {
	"""课程id"""
	id:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""课程简介"""
	abouts:String
	"""权重,表示学时,学分等"""
	period:Double
	"""是否启用"""
	enabled:Boolean!
	"""课程详情"""
	contents:String
	"""所属分类的编号集合"""
	categoryIds:[String]
	"""课程目录"""
	courseOutline:[CourseOutLineWithSubOutlineUpdateRequest]
	"""计划授课讲数"""
	plannedLecturesNum:Int!
	"""教师ids"""
	teacherIds:[String]
}
"""
	<AUTHOR> create 2020/4/21 7:24
"""
input CourseCategoryCreateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.course.category.CourseCategoryCreateRequest") {
	"""课件分类名称"""
	name:String
	"""备注"""
	remarks:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enabled:Boolean!
	"""父分类ID"""
	parentId:String
	"""课程分类归属类型，{@link CourseCategoryBelongsType}"""
	belongsType:Int!
}
"""
	<AUTHOR> create 2020/4/21 7:27
"""
input CourseCategoryUpdateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.course.category.CourseCategoryUpdateRequest") {
	"""分类编号"""
	id:String
	"""课件分类名称"""
	name:String
	"""备注"""
	remarks:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enabled:Boolean!
	"""父分类ID"""
	parentId:String
}
"""
	<AUTHOR> create 2020/4/21 7:42
"""
input CourseInPoolCreateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.coursepool.CourseInPoolCreateRequest") {
	"""课程编号"""
	courseId:String
	"""课程序号"""
	sequence:Int!
	"""课程标量值|在课程池规则中，课程在课程池中权重值"""
	quantitative:Double!
	"""课程学时|课程在课程池中的学时"""
	period:Double!
	"""课程过期时间|课程在课程池中到期时间，null表示该课程在课程池中无期限限制"""
	courseExpireTime:DateTime
}
"""
	<AUTHOR> create 2020/4/21 7:45
"""
input CourseInPoolUpdateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.coursepool.CourseInPoolUpdateRequest") {
	"""id"""
	id:String
	"""课程编号"""
	courseId:String
	"""课程序号"""
	sequence:Int!
	"""课程标量值|在课程池规则中，课程在课程池中权重值"""
	quantitative:Double!
	"""课程学时|课程在课程池中的学时"""
	period:Double!
	"""课程过期时间|课程在课程池中到期时间，null表示该课程在课程池中无期限限制"""
	courseExpireTime:DateTime
	"""特征标记列表"""
	markers:[MarkerDTO]
}
"""
	<AUTHOR> create 2020/4/21 7:42
"""
input CoursePoolCreateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.coursepool.CoursePoolCreateRequest") {
	"""必填，课程池名称"""
	poolName:String
	"""过期时间,null表示不设置过期"""
	expireTime:DateTime
	"""课程池描述"""
	poolDescription:String
	"""展示名称"""
	showName:String
	"""包内课程"""
	courseInPoolList:[CourseInPoolCreateRequest]
	"""课程包所属单位id"""
	unitId:String
}
"""
	<AUTHOR> create 2020/4/21 7:44
"""
input CoursePoolUpdateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.coursepool.CoursePoolUpdateRequest") {
	"""课程包id"""
	id:String
	"""必填，课程池名称"""
	poolName:String
	"""排序序号"""
	sequence:Int!
	"""课程池描述"""
	poolDescription:String
	"""展示名称"""
	showName:String
	"""包内课程"""
	courseInPoolList:[CourseInPoolUpdateRequest]
}
"""
	<AUTHOR> create 2020/4/21 8:02
"""
input BABAILIVideoTranscodeSettingRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.courseware.BABAILIVideoTranscodeSettingRequest") {
	"""是否进行转码"""
	transcode:Boolean!
	"""转码清晰度，1代表普屏流畅|2代表普屏标清|3代表普屏高清|4代表宽屏流畅|5代表宽屏标清|6代表宽屏高清"""
	clarityList:[Int]
}
"""<AUTHOR> create 2020/4/21 8:01"""
input CourseWareCreateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.courseware.CourseWareCreateRequest") {
	"""课件名称"""
	name:String
	"""课件时长，该属性只限文档型课件设置"""
	timeLength:Int!
	"""教师id"""
	teacherId:String
	"""课件简介"""
	abouts:String
	"""课件分类ID"""
	cwyId:String
	"""课件解析资源存放的路径"""
	coursewareResourcePath:String
	"""视频转码配置"""
	videoTranscodeSettings:VideoTranscodeSettingsRequest
	"""若是大文件上传，需要异步合并时，必须填文件的MD5"""
	resourceMD5:String
	"""供应商ID"""
	supplierId:String
	"""是否可用"""
	usable:Boolean!
	"""自定义拓展信息"""
	expandData:String
	"""创建类型，0表示自建,1表示内置,2表示共享，3表示迁移，4表示购买"""
	createType:Int!
	"""外部链接资源"""
	extensionResourceInfo:ExtensionResourceInfo
}
input ExtensionResourceInfo @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.courseware.CourseWareCreateRequest$ExtensionResourceInfo") {
	videoInfoDtos:[VideoInfoDto]
}
input VideoInfoDto @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.courseware.CourseWareCreateRequest$ExtensionResourceInfo$VideoInfoDto") {
	"""视频播放路劲，相对路径"""
	path:String
	"""视频清晰度
		1:流畅普屏 2:标清普屏 3:高清普屏 4:流畅宽屏 5:标清宽屏 6:高清宽屏
		负数是代表手机端对应的清晰度
	"""
	clarity:Int!
}
"""
	<AUTHOR> create 2020/4/21 8:06
"""
input CourseWareUpdateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.courseware.CourseWareUpdateRequest") {
	"""课件ID"""
	id:String
	"""课件名称"""
	name:String
	"""课件时长，该属性只限文档型课件设置"""
	timeLength:Int!
	"""教师id"""
	teacherId:String
	"""课件简介"""
	abouts:String
	"""课件分类ID"""
	cwyId:String
	"""供应商ID"""
	supplierId:String
	"""是否可用"""
	usable:Boolean!
	"""自定义拓展信息"""
	expandData:String
}
"""
	<AUTHOR> create 2020/4/21 8:04
"""
input HWYVideoTranscodeSettingRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.courseware.HWYVideoTranscodeSettingRequest") {
	"""是否进行转码"""
	transcode:Boolean!
	"""华为云转码模板名称"""
	templateName:String
	"""是否抽取音频"""
	extractAudio:Boolean!
}
"""
	<AUTHOR> create 2020/4/21 8:03
"""
input POLYVVideoTranscodeSettingRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.courseware.POLYVVideoTranscodeSettingRequest") {
	"""是否进行转码"""
	transcode:Boolean!
}
"""
	<AUTHOR> create 2020/4/21 8:02
"""
input VideoTranscodeSettingsRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.courseware.VideoTranscodeSettingsRequest") {
	"""八百里转码配置"""
	babailiSetting:BABAILIVideoTranscodeSettingRequest
	"""保利威视转码配置"""
	polyvSetting:POLYVVideoTranscodeSettingRequest
	"""华为云转码配置"""
	hwySetting:HWYVideoTranscodeSettingRequest
}
"""
	<AUTHOR> create 2020/4/21 7:55
"""
input CourseWareCategoryCreateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.courseware.category.CourseWareCategoryCreateRequest") {
	"""分类名称"""
	name:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enabled:Boolean!
	"""备注"""
	remarks:String
	"""父分类id"""
	parentId:String
}
"""
	<AUTHOR> create 2020/4/21 7:56
"""
input CourseWareCategoryUpdateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.courseware.category.CourseWareCategoryUpdateRequest") {
	"""分类id"""
	id:String
	"""分类名称"""
	name:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enabled:Boolean!
	"""备注"""
	remarks:String
	"""父分类id"""
	parentId:String
}
"""
	<AUTHOR> create 2020/4/20 17:01
"""
input BlankFillingRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.popquestion.BlankFillingRequest") {
	"""答案数量"""
	answerCount:Int!
	"""当填空题类型为 精确匹配时，最外层集合为答案组（也就是每一组都是一道填空题的答案，满足当中的任意一组表示回答正确）第二层集合为空
		当填空题类似为 每空多答案时，最外层的集合为每个空的答案，第二层集合为每个空的备选答案
	"""
	answersGroup:[[String]]
	"""答案项分值
		当填空题类型为 精确匹配时此项值无效
	"""
	answersItemScore:[Double]
	"""答案类型
		@see BlankFillingAnswerType
	"""
	answerType:Int!
	"""答案是否有顺序.当{@link #answerType } = {@link BlankFillingAnswerType#MULTIPLE_PER_BLANK} 时，
		即每空多答案的情况下，答案是否是按照填空顺序排列。
	"""
	sequence:Boolean!
	"""评分标准"""
	standard:String
}
"""
	<AUTHOR> create 2020/4/20 16:58
"""
input ChoiceItemRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.popquestion.ChoiceItemRequest") {
	"""选项ID"""
	id:String
	"""选项内容"""
	content:String
}
"""
	<AUTHOR> create 2020/4/20 17:06
"""
input ComprehensiveChildQuestionRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.popquestion.ComprehensiveChildQuestionRequest") {
	"""子试题id"""
	questionId:String
	"""题目"""
	title:String
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""判断题"""
	judgement:JudgementRequest
	"""单选题"""
	singleChoice:SingleChoiceRequest
	"""多选"""
	multipleChoice:MultipleChoiceRequest
	"""填空"""
	blankFilling:BlankFillingRequest
	"""问答题"""
	essay:EssayRequest
	"""量表题"""
	scale:ScaleRequest
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficultyValue:Double!
	"""试题解析"""
	description:String
}
"""
	<AUTHOR> create 2020/4/20 17:05
"""
input ComprehensiveRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.popquestion.ComprehensiveRequest") {
	"""子题"""
	children:[ComprehensiveChildQuestionRequest]
}
"""
	<AUTHOR> create 2020/4/20 17:04
"""
input EssayRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.popquestion.EssayRequest") {
	"""参考答案"""
	referenceAnswer:String
	"""评分标准"""
	standard:String
	"""是否限制作答长度"""
	limitAnswerLength:Boolean!
	"""允许作答的文本字符最少长度"""
	permitAnswerLengthMin:Int!
	"""允许作答的文本字符最大长度"""
	permitAnswerLengthMax:Int!
}
"""
	<AUTHOR> create 2020/4/20 16:57
"""
input JudgementRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.popquestion.JudgementRequest") {
	"""正确答案"""
	correctAnswer:Boolean!
}
"""
	<AUTHOR> create 2020/4/20 17:00
"""
input MultipleChoiceRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.popquestion.MultipleChoiceRequest") {
	"""选项"""
	choiceItems:[ChoiceItemRequest]
	"""正确答案"""
	correctAnswers:[String]
}
"""
	<AUTHOR> create 2020/4/20 17:09
"""
input PopQuestionCreateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.popquestion.PopQuestionCreateRequest") {
	"""试题应用类型
		@see QuestionApplyType
	"""
	applyTypes:[Int]
	"""题库ID"""
	libraryId:String
	"""题目"""
	title:String
	"""判断题"""
	judgement:JudgementRequest
	"""单选题"""
	singleChoice:SingleChoiceRequest
	"""多选"""
	multipleChoice:MultipleChoiceRequest
	"""填空"""
	blankFilling:BlankFillingRequest
	"""问答题"""
	essay:ScaleRequest
	"""量表题"""
	scale:ScaleRequest
	"""综合题"""
	comprehensive:ComprehensiveRequest
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficulty:Double!
	"""试题解析"""
	description:String
	"""是否启用"""
	enabled:Boolean!
	"""弹窗时间：秒"""
	timePoint:Int!
	"""课件id"""
	courseWareId:String
}
"""
	<AUTHOR> create 2020/4/20 17:15
"""
input PopQuestionUpdateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.popquestion.PopQuestionUpdateRequest") {
	"""试题id"""
	id:String
	"""试题应用类型
		@see QuestionApplyType
	"""
	applyTypes:[Int]
	"""题库ID"""
	libraryId:String
	"""题目"""
	title:String
	"""判断题"""
	judgement:JudgementRequest
	"""单选题"""
	singleChoice:SingleChoiceRequest
	"""多选"""
	multipleChoice:MultipleChoiceRequest
	"""填空"""
	blankFilling:BlankFillingRequest
	"""问答题"""
	essay:EssayRequest
	"""量表题"""
	scale:ScaleRequest
	"""综合题"""
	comprehensive:ComprehensiveRequest
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficulty:Double!
	"""试题解析"""
	description:String
	"""是否启用"""
	enabled:Boolean!
	"""弹窗题id、主键id"""
	popQuestionId:String
	"""弹窗时间：秒"""
	timePoint:Int!
}
"""
	<AUTHOR> create 2020/4/20 17:05
"""
input ScaleRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.popquestion.ScaleRequest") {
	"""量表类型"""
	scaleType:ScaleType
	"""程度_始，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
}
"""
	<AUTHOR> create 2020/4/20 16:58
"""
input SingleChoiceRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.popquestion.SingleChoiceRequest") {
	"""选项"""
	choiceItems:[ChoiceItemRequest]
	"""标准答案"""
	correctAnswer:String
}
"""
	<AUTHOR> create 2020/4/21 8:12
"""
input TeacherCreateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.teacher.TeacherCreateRequest") {
	"""教师名称"""
	name:String
	"""教师头像"""
	photo:String
	"""教师简介"""
	abouts:String
	"""教师详情"""
	contents:String
	"""性别，-1表示未知，0表示女，1表示男"""
	gender:Int!
	"""职称"""
	professionalTitle:String
}
"""
	<AUTHOR> create 2020/4/21 8:13
"""
input TeacherUpdateRequest @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.request.teacher.TeacherUpdateRequest") {
	"""教师ID"""
	id:String
	"""教师名称"""
	name:String
	"""教师头像"""
	photo:String
	"""教师简介"""
	abouts:String
	"""教师详情"""
	contents:String
	"""性别，-1表示未知，0表示女，1表示男"""
	gender:Int!
	"""职称"""
	professionalTitle:String
}
input ResAuthorizedQuery @type(value:"com.fjhb.platformstandard.common.utils.dataauthorized.ResAuthorizedQuery") {
	authorizedState:Int!
	hasAuthorize:Boolean
	forbidAuthorize:Boolean!
	rangeType:String
	belongsType:String
	authorizeToUnitId:String
	authorizedFromUnitId:String
	objectId:String
	useType:String
	targetUnitId:String
}
enum ScaleType @type(value:"com.fjhb.platform.component.exam.commons.api.consts.question.ScaleType") {
	CUSTOM
	SATISFACTION
	RECOGNITION
	IMPORTANCE
	WILLING
	CONFORMITY
}
type MarkerDTO1 @type(value:"com.fjhb.platform.core.course.v1.api.dto.MarkerDTO") {
	key:String
	value:String
}
type OperatorDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.OperatorDTO") {
	userId:String
	name:String
	uniqueData:String
}
type UnitDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.UnitDTO") {
	id:String
	name:String
}
type CoursePoolDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.coursepool.CoursePoolDTO") {
	id:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	unit:UnitDTO
	organizationId:String
	poolName:String
	sequence:Int!
	markers:[MarkerDTO1]
	createUsrId:String
	expireTime:DateTime
	poolDescription:String
	showName:String
	creator:OperatorDTO
	createTime:DateTime
	courseCount:Int!
	totalPeriod:Double!
}
"""
	<AUTHOR> create 2020/4/21 7:26
"""
type OperatorResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.OperatorResponse") {
	"""用户id"""
	userId:String
	"""姓名"""
	name:String
	"""唯一性值"""
	uniqueData:String
}
"""
	<AUTHOR> create 2020/4/21 7:31
"""
type SupplierResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.SupplierResponse") {
	"""主键 课件提供商ID"""
	id:String
	"""提供商名称"""
	name:String
	"""创建人id"""
	creator:String
	"""创建时间"""
	createTime:DateTime
}
"""
	<AUTHOR> create 2020/4/21 7:48
"""
type UnitResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.UnitResponse") {
	"""单位id"""
	id:String
	"""单位名称"""
	name:String
}
"""
	<AUTHOR> create 2020/4/21 7:52
"""
type CourseItemResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.course.CourseItemResponse") {
	"""课程编号"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属单位"""
	unit:UnitResponse
	"""所属组织机构ID"""
	organizationId:String
	"""课程分类"""
	categoryList:[CourseCategoryResponse]
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""权重,表示学时,学分等"""
	period:Double!
	"""课程简介"""
	abouts:String
	"""创建类型,0表示自建,1表示内置,2表示共享，3表示迁移，4表示授权"""
	createType:Int!
	"""是否授权"""
	hasAuthorize:Boolean!
	"""是否启用"""
	enabled:Boolean!
	"""是否被逻辑删除"""
	isDelete:Boolean!
	"""课程状态，-1暂无课件，0解析中，1解析成功，2解析失败"""
	status:Int!
	"""课程详情"""
	contents:String
	"""教师信息"""
	teacherInfo:String
	"""创建时间"""
	createTime:String
	"""创建者编号"""
	createUsrId:String
	"""共享来源id"""
	shareSourceId:String
	"""是否试听"""
	customStatus:Int!
	"""计划授课讲数"""
	plannedLecturesNum:Int!
}
"""<AUTHOR> create 2020/4/21 7:32"""
type CourseOutLineResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.course.CourseOutLineResponse") {
	"""目录id"""
	id:String
	"""课程id"""
	courseId:String
	"""挂载课件id"""
	courseWareId:String
	"""课件"""
	courseWare:CourseWareResponse
	"""目录名称"""
	name:String
	"""上级目录ID"""
	parentId:String
	"""排序"""
	sort:Int!
	"""挂在课件是否支持试听 0不可以试听，1可以试听，"""
	customeStatus:Int!
	"""外部链接资源"""
	extensionResourceResponse:ExtensionResourceResponse1
}
type ExtensionResourceResponse1 @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.course.CourseOutLineResponse$ExtensionResourceResponse") {
	videoInfoDtos:[VideoInfoDto1]
}
type VideoInfoDto1 @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.course.CourseOutLineResponse$ExtensionResourceResponse$VideoInfoDto") {
	"""视频播放路劲，相对路径"""
	path:String
	"""视频清晰度
		1:流畅普屏 2:标清普屏 3:高清普屏 4:流畅宽屏 5:标清宽屏 6:高清宽屏
		负数是代表手机端对应的清晰度
	"""
	clarity:Int!
}
"""
	<AUTHOR> create 2020/4/21 7:32
"""
type CourseOutLineWithSubOutlineResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.course.CourseOutLineWithSubOutlineResponse") {
	"""子目录"""
	subCourseOutlines:[CourseOutLineResponse]
	"""目录id"""
	id:String
	"""课程id"""
	courseId:String
	"""挂载课件id"""
	courseWareId:String
	"""课件"""
	courseWare:CourseWareResponse
	"""目录名称"""
	name:String
	"""上级目录ID"""
	parentId:String
	"""排序"""
	sort:Int!
	"""挂在课件是否支持试听 0不可以试听，1可以试听，"""
	customeStatus:Int!
	"""外部链接资源"""
	extensionResourceResponse:ExtensionResourceResponse1
}
"""
	<AUTHOR> create 2020/4/21 7:23
"""
type CourseResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.course.CourseResponse") {
	"""课程ID"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""权重,表示学时,学分等"""
	period:Double!
	"""供应商"""
	supplier:SupplierResponse
	"""课程目录"""
	courseOutline:[CourseOutLineWithSubOutlineResponse]
	"""课程分类"""
	categoryList:[CourseCategoryResponse]
	"""教师信息"""
	teachers:[TeacherResponse]
	"""课程简介"""
	abouts:String
	"""创建类型,0表示自建,1表示内置,2表示共享，3表示迁移"""
	createType:Int!
	"""是否启用"""
	enabled:Boolean!
	"""是否被逻辑删除"""
	delete:Boolean!
	"""课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败"""
	status:Int!
	"""完成时间"""
	completedTime:DateTime
	"""课程详情"""
	contents:String
	"""创建时间"""
	createTime:DateTime
	"""创建者编号"""
	createUsrId:String
	"""创建人"""
	creator:OperatorResponse
	"""计划授课讲数"""
	plannedLecturesNum:Int!
}
"""
	<AUTHOR> create 2020/4/21 7:28
"""
type CourseCategoryListResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.course.category.CourseCategoryListResponse") {
	"""分类编号"""
	id:String
	"""课件分类名称"""
	name:String
	"""备注"""
	remarks:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enabled:Boolean!
	"""父分类ID"""
	parentId:String
	"""课程分类归属类型，{@link CourseCategoryBelongsType}"""
	belongsType:Int!
	"""创建时间"""
	createTime:DateTime
}
"""
	<AUTHOR> create 2020/4/21 7:25
"""
type CourseCategoryResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.course.category.CourseCategoryResponse") {
	"""分类编号"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属服务单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""课件分类名称"""
	name:String
	"""备注"""
	remarks:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enabled:Boolean!
	"""父分类ID"""
	parentId:String
	"""创建者ID"""
	createUsrId:String
	"""创建人"""
	creator:OperatorResponse
	"""创建时间"""
	createTime:DateTime
	"""课程分类归属类型，{@link CourseCategoryBelongsType}"""
	belongsType:Int!
}
"""
	<AUTHOR> create 2020/4/21 7:50
"""
type CourseInPoolItemResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.coursepool.CourseInPoolItemResponse") {
	"""课程池与课程关系编号"""
	id:String
	"""课程编号"""
	courseId:String
	"""课程名称"""
	courseName:String
	"""课程序号"""
	sequence:Int!
	"""课程标量值|在课程池规则中，课程在课程池中权重值"""
	quantitative:Double!
	"""课程学时|课程在课程池中的学时"""
	period:Double!
	"""课程过期时间|课程在课程池中到期时间，null表示该课程在课程池中无期限限制"""
	courseExpireTime:DateTime
	"""特征标记列表"""
	markers:[MarkerDTO1]
	"""创建人编号"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""课程学时|课程中的学时"""
	coursePeriod:Double!
	"""课程分类编号"""
	categoryIdList:[String]
	"""课程分类集合"""
	categoryList:[CourseCategoryResponse]
	"""所属课程池编号"""
	ccpId:String
	"""创建类型,0表示自建,1表示内置,2表示共享，3表示迁移，4表示授权"""
	createType:Int!
}
"""
	<AUTHOR> create 2020/4/21 7:49
"""
type CoursePoolItemResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.coursepool.CoursePoolItemResponse") {
	"""课程池编号"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属服务单位ID"""
	unitId:String
	"""单位信息"""
	unit:UnitResponse
	"""所属组织机构ID"""
	organizationId:String
	"""课程池名称"""
	poolName:String
	"""展示名称"""
	showName:String
	"""课程池状态|0/1/2，正常/无效/过期"""
	poolState:Int!
	"""排序序号"""
	sequence:Int!
	"""创建人编号"""
	createUserId:String
	"""创建人"""
	creator:OperatorResponse
	"""创建时间"""
	createTime:String
	"""更新时间"""
	updateTime:DateTime
	"""过期时间,null表示不设置过期"""
	expireTime:DateTime
	"""课程池描述"""
	poolDescription:String
	"""课程池内课程数量"""
	courseCount:Int!
	"""课程池内课程权重值总和"""
	totalQuantitative:Double!
	"""课程池内课程学时或学分总和"""
	totalPeriod:Double!
}
"""
	<AUTHOR> create 2020/4/21 7:47
"""
type CoursePoolResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.coursepool.CoursePoolResponse") {
	"""课程包id"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""必填，所属平台版本ID"""
	platformVersionId:String
	"""必填，所属项目ID"""
	projectId:String
	"""必填，所属子项目ID"""
	subProjectId:String
	"""必填，所属服务单位ID"""
	unitId:String
	"""单位信息"""
	unit:UnitResponse
	"""必填，所属组织机构ID"""
	organizationId:String
	"""必填，课程池名称"""
	poolName:String
	"""排序序号，默认1"""
	sequence:Int!
	"""必填，创建人编号"""
	createUsrId:String
	"""过期时间,null表示不设置过期"""
	expireTime:DateTime
	"""课程池描述"""
	poolDescription:String
	"""展示名称"""
	showName:String
	"""创建人"""
	creator:OperatorResponse
	"""创建时间"""
	createTime:DateTime
	"""课程池内课程数量"""
	courseCount:Int!
	"""课程池内课程学时或学分总和"""
	totalPeriod:Double!
}
"""
	<AUTHOR> create 2020/4/21 8:08
"""
type CourseWareItemResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.courseware.CourseWareItemResponse") {
	"""课件ID"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""课件名称"""
	name:String
	"""课件类型，1表示文档，2表示视频，3表示多媒体"""
	type:Int!
	"""课件原始类型, 1表示文档，2表示单视频，3表示串流大师，4表示汉博尔，5表示会计靠前，6表示Power+，7表示网视宝，8表示新华网，9表示地税网络学院，10表示中经网"""
	originalType:Int!
	"""媒体时长，单位秒"""
	timeLength:Int!
	"""教师名称"""
	teacherName:String
	"""教师简介"""
	teacherAbouts:String
	"""课件分类ID"""
	cwyId:String
	"""课件分类"""
	category:CourseWareCategoryResponse
	"""供应商ID"""
	supplierId:String
	"""供应商"""
	supplier:SupplierResponse
	"""课件状态 0解析中，1解析成功，2解析失败"""
	status:Int!
	"""是否可用"""
	usable:Boolean!
	"""创建者ID"""
	createUsrId:String
	"""创建人"""
	creator:OperatorResponse
	"""创建时间"""
	createTime:String
	"""自定义拓展信息"""
	expandData:String
	"""创建类型，0表示自建,1表示内置,2表示共享，3表示迁移，4表示购买"""
	createType:Int!
	"""创建类型相应时间"""
	createTypeTime:String
	"""是否被引用"""
	hasReference:Boolean
	"""弹窗题数量"""
	popCount:Int
}
"""<AUTHOR> create 2020/4/21 7:58"""
type CourseWareResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.courseware.CourseWareResponse") {
	"""课件ID"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""课件名称"""
	name:String
	"""课件类型，1表示文档，2表示视频，3表示多媒体"""
	type:Int!
	"""课件原始类型, 1表示文档，2表示单视频，3表示串流大师，4表示汉博尔，5表示会计靠前，6表示Power+，7表示网视宝，8表示新华网，9表示地税网络学院，10表示中经网"""
	originalType:Int!
	"""课件时长，该属性只限文档型课件设置"""
	timeLength:Int!
	"""教师id"""
	teacherId:String
	"""教师"""
	teacher:TeacherResponse
	"""课件简介"""
	abouts:String
	"""课件分类ID"""
	cwyId:String
	"""课件分类"""
	category:CourseWareCategoryResponse
	"""课件解析资源存放的路径"""
	coursewareResourcePath:String
	"""供应商ID"""
	supplierId:String
	"""供应商"""
	supplier:SupplierResponse
	"""是否可用"""
	usable:Boolean!
	"""课件状态 0解析中，1解析成功，2解析失败"""
	status:Int!
	"""创建者ID"""
	createUsrId:String
	"""创建人"""
	creator:OperatorResponse
	"""创建时间"""
	createTime:DateTime
	"""自定义拓展信息"""
	expandData:String
	"""创建类型，0表示自建,1表示内置,2表示共享，3表示迁移，4表示购买"""
	createType:Int!
	"""创建类型相应时间"""
	createTypeTime:DateTime
	"""外部链接资源"""
	extensionResourceResponse:ExtensionResourceResponse
}
type ExtensionResourceResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.courseware.CourseWareResponse$ExtensionResourceResponse") {
	videoInfoDtos:[VideoInfoDto1]
}
"""
	<AUTHOR> create 2020/4/21 7:57
"""
type CourseWareCategoryListResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.courseware.category.CourseWareCategoryListResponse") {
	"""分类编号
	"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属服务单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""分类名称"""
	name:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enabled:Boolean!
	"""备注"""
	remarks:String
	"""父分类id"""
	parentId:String
	"""创建者ID"""
	createUsrId:String
	"""创建时间"""
	createTime:DateTime
}
"""
	<AUTHOR> create 2020/4/21 7:54
"""
type CourseWareCategoryResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.courseware.category.CourseWareCategoryResponse") {
	"""分类编号
	"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属服务单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""分类名称"""
	name:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enabled:Boolean!
	"""备注"""
	remarks:String
	"""父分类id"""
	parentId:String
	"""创建者ID"""
	createUsrId:String
	"""创建人"""
	creator:OperatorResponse
	"""创建时间"""
	createTime:DateTime
}
"""
	<AUTHOR> create 2020/4/20 17:01
"""
type BlankFillingResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.popquestion.BlankFillingResponse") {
	"""答案数量"""
	answerCount:Int!
	"""当填空题类型为 精确匹配时，最外层集合为答案组（也就是每一组都是一道填空题的答案，满足当中的任意一组表示回答正确）第二层集合为空
		当填空题类似为 每空多答案时，最外层的集合为每个空的答案，第二层集合为每个空的备选答案
	"""
	answersGroup:[[String]]
	"""答案项分值
		当填空题类型为 精确匹配时此项值无效
	"""
	answersItemScore:[Double]
	"""答案类型
		@see BlankFillingAnswerType
	"""
	answerType:Int!
	"""答案是否有顺序.当{@link #answerType } = {@link BlankFillingAnswerType#MULTIPLE_PER_BLANK} 时，
		即每空多答案的情况下，答案是否是按照填空顺序排列。
	"""
	sequence:Boolean!
	"""评分标准"""
	standard:String
}
"""
	<AUTHOR> create 2020/4/20 16:58
"""
type ChoiceItemResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.popquestion.ChoiceItemResponse") {
	"""选项ID"""
	id:String
	"""选项内容"""
	content:String
}
"""
	<AUTHOR> create 2020/4/20 17:06
"""
type ComprehensiveChildQuestionResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.popquestion.ComprehensiveChildQuestionResponse") {
	"""子试题id"""
	questionId:String
	"""题目"""
	title:String
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""判断题"""
	judgement:JudgementResponse
	"""单选题"""
	singleChoice:SingleChoiceResponse
	"""多选"""
	multipleChoice:MultipleChoiceResponse
	"""填空"""
	blankFilling:BlankFillingResponse
	"""问答题"""
	essay:EssayResponse
	"""量表题"""
	scale:ScaleResponse
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficultyValue:Double!
	"""试题解析"""
	description:String
}
"""
	<AUTHOR> create 2020/4/20 17:05
"""
type ComprehensiveResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.popquestion.ComprehensiveResponse") {
	"""子题"""
	children:[ComprehensiveChildQuestionResponse]
}
"""
	<AUTHOR> create 2020/4/20 17:04
"""
type EssayResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.popquestion.EssayResponse") {
	"""参考答案"""
	referenceAnswer:String
	"""评分标准"""
	standard:String
	"""是否限制作答长度"""
	limitAnswerLength:Boolean!
	"""允许作答的文本字符最少长度"""
	permitAnswerLengthMin:Int!
	"""允许作答的文本字符最大长度"""
	permitAnswerLengthMax:Int!
}
"""
	<AUTHOR> create 2020/4/20 16:57
"""
type JudgementResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.popquestion.JudgementResponse") {
	"""正确答案"""
	correctAnswer:Boolean!
}
"""
	<AUTHOR> create 2020/4/20 17:00
"""
type MultipleChoiceResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.popquestion.MultipleChoiceResponse") {
	"""选项"""
	choiceItems:[ChoiceItemResponse]
	"""正确答案"""
	correctAnswers:[String]
}
"""
	<AUTHOR> create 2020/4/20 16:56
"""
type PopQuestionResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.popquestion.PopQuestionResponse") {
	"""试题id"""
	id:String
	"""试题应用类型
		@see QuestionApplyType
	"""
	applyTypes:[Int]
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""题库ID"""
	libraryId:String
	"""题目"""
	title:String
	"""判断题"""
	judgement:JudgementResponse
	"""单选题"""
	singleChoice:SingleChoiceResponse
	"""多选"""
	multipleChoice:MultipleChoiceResponse
	"""填空"""
	blankFilling:BlankFillingResponse
	"""问答题"""
	essay:EssayResponse
	"""量表题"""
	scale:ScaleResponse
	"""综合题"""
	comprehensive:ComprehensiveResponse
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficulty:Double!
	"""试题解析"""
	description:String
	"""创建人id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""最后修改时间"""
	lastChangeTime:DateTime
	"""是否启用"""
	enabled:Boolean!
	"""资源记录(数据)的授权源id
		a 授权 b, b 授权 c, c的sourceId是b, c的rootId是a
	"""
	rootId:String
	"""数据授权的Token, 并不需要默认值"""
	token:String
	"""弹窗题id、主键id"""
	popQuestionId:String
	"""弹窗时间：秒"""
	timePoint:Int!
	"""课件id"""
	courseWareId:String
}
"""
	<AUTHOR> create 2020/4/20 17:05
"""
type ScaleResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.popquestion.ScaleResponse") {
	"""量表类型"""
	scaleType:ScaleType
	"""程度_始，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
}
"""
	<AUTHOR> create 2020/4/20 16:58
"""
type SingleChoiceResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.popquestion.SingleChoiceResponse") {
	"""选项"""
	choiceItems:[ChoiceItemResponse]
	"""标准答案"""
	correctAnswer:String
}
"""
	<AUTHOR> create 2020/4/21 8:09
"""
type TeacherItemResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.teacher.TeacherItemResponse") {
	"""教师ID"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""教师名称"""
	name:String
	"""教师头像"""
	photo:String
	"""教师简介"""
	abouts:String
	"""性别，-1表示未知，0表示女，1表示男"""
	gender:Int!
	"""职称"""
	professionalTitle:String
	"""创建者ID"""
	createUsrId:String
	"""创建时间"""
	createTime:DateTime
}
"""
	<AUTHOR> create 2020/4/21 7:59
"""
type TeacherResponse @type(value:"com.fjhb.platform.core.course.v1.kernel.gateway.graphql.response.teacher.TeacherResponse") {
	"""教师ID"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""教师名称"""
	name:String
	"""教师头像"""
	photo:String
	"""教师简介"""
	abouts:String
	"""教师详情"""
	contents:String
	"""性别，-1表示未知，0表示女，1表示男"""
	gender:Int!
	"""职称"""
	professionalTitle:String
	"""创建者ID"""
	createUsrId:String
	"""创建人"""
	creator:OperatorResponse
	"""创建时间"""
	createTime:DateTime
}


"""<AUTHOR>
	@date 2021/6/29 16:28
	@Description:
"""
input BTPXCoursePoolQueryDTO @type(value:"com.fjhb.btpx.course.appservice.dto.BTPXCoursePoolQueryDTO") {
	"""课程供应商id列表"""
	coursewareSupplierIds:[String]
	poolName:String
	exact:Boolean!
	poolState:Int!
	createStartTime:DateTime
	createEndTime:DateTime
	orderByField:Int!
	descending:Boolean!
	poolIdList:[String]
	excludePoolIdList:[String]
}
"""分配课程请求
	<AUTHOR> create 2021/1/29 14:55
"""
input ArrangeCourseRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.ArrangeCourseRequest") {
	"""文件路径"""
	filePath:String
	"""文件名"""
	fileName:String
}
"""补贴培训分配课程请求
	<AUTHOR> create 2021/3/24 15:27
"""
input BTPXCourseArrangeRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.course.BTPXCourseArrangeRequest") {
	"""课程id"""
	courseId:String
	"""课件供应商id"""
	coursewareSupplierId:String
	"""开放使用平台"""
	openUsedPlatforms:[String]
	"""工种分类->工种路径，以/开始,/分隔，只需要填两级"""
	workTypePaths:[String]
	"""工种"""
	workTypes:[TagDTO]
}
"""资源平台课程查询请求
	<AUTHOR> create 2020/6/10 10:18
"""
input BTPXCourseQueryRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.course.BTPXCourseQueryRequest") {
	"""包含课程编号集合"""
	includeCourseIdList:[String]
	"""课程名称"""
	name:String
	"""转码状态 -1表示不进行查询，0解析中，1解析成功，2解析失败"""
	status:Int
	"""资源上传类型， -1表示不查询，0媒体资源，1外链地址"""
	resourceUploadType:Int!
	"""工种分类->工种路径，以/开始,/分隔，只需要填两级"""
	workTypePaths:[String]
	"""工种"""
	workTypes:[String]
	"""标签
		<p>资源平台标签关键字查询，添哥说按照选标签模式做，这里使用标签id集合</p>
	"""
	tagIds:[String]
	"""开发资源使用平台"""
	openUsedPlatforms:[String]
	"""学分起"""
	periodBegin:Double
	"""学分止"""
	periodEnd:Double
	"""创建的资源供应商id"""
	createResourceVendorId:String
	"""是否已分配"""
	arrangeUsedPlatform:Boolean!
	"""是否启用， -1表示不查询，0表示不启用，1表示启用"""
	enable:Int!
	"""创建时间查询的起始时间"""
	startCreateTime:String
	"""创建时间查询的截止时间"""
	endCreateTime:String
	"""课件供应商id集合"""
	coursewareSupplierIds:[String]
	"""培训机构id集合"""
	trainingInstitutionIds:[String]
}
"""资源平台课程修改请求
	<AUTHOR> create 2020/6/8 10:33
"""
input BTPXCourseUpdateRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.course.BTPXCourseUpdateRequest") {
	"""工种分类->工种路径，以/开始,/分隔，只需要填两级"""
	workTypePaths:[String]
	"""工种"""
	workTypes:[TagDTO]
	"""开放使用的平台"""
	openUsedPlatforms:[String]
	"""课程id"""
	id:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""课程简介"""
	abouts:String
	"""权重,表示学时,学分等"""
	period:Double
	"""是否启用"""
	enabled:Boolean!
	"""课程详情"""
	contents:String
	"""所属分类的编号集合"""
	categoryIds:[String]
	"""课程目录"""
	courseOutline:[CourseOutLineWithSubOutlineUpdateRequest]
	"""计划授课讲数"""
	plannedLecturesNum:Int!
	"""教师ids"""
	teacherIds:[String]
}
"""供应商课程查询请求
	<AUTHOR> create 2020/6/10 10:18
"""
input CoursewareSupplierCourseQueryRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.course.CoursewareSupplierCourseQueryRequest") {
	"""包含课程编号集合"""
	includeCourseIdList:[String]
	"""课程名称"""
	name:String
	"""转码状态 -1表示不进行查询，0解析中，1解析成功，2解析失败"""
	status:Int
	"""资源上传类型， -1表示不查询，0媒体资源，1外链地址"""
	resourceUploadType:Int!
	"""工种分类->工种路径，以/开始,/分隔，只需要填两级"""
	workTypePaths:[String]
	"""工种"""
	workTypes:[String]
	"""标签
		<p>资源平台标签关键字查询，添哥说按照选标签模式做，这里使用标签id集合</p>
	"""
	tagIds:[String]
	"""学分起"""
	periodBegin:Double
	"""学分止"""
	periodEnd:Double
	"""创建的资源供应商id"""
	createResourceVendorId:String
	"""是否已分配"""
	arrangeUsedPlatform:Boolean!
	"""是否启用， -1表示不查询，0表示不启用，1表示启用"""
	enable:Int!
	"""创建时间查询的起始时间"""
	startCreateTime:String
	"""创建时间查询的截止时间"""
	endCreateTime:String
	"""培训机构id集合"""
	trainingInstitutionIds:[String]
}
"""培训机构课程查询请求
	<AUTHOR> create 2020/6/10 10:18
"""
input TrainingInstitutionCourseQueryRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.course.TrainingInstitutionCourseQueryRequest") {
	"""包含课程编号集合"""
	includeCourseIdList:[String]
	"""课程名称"""
	name:String
	"""转码状态 -1表示不进行查询，0解析中，1解析成功，2解析失败"""
	status:Int
	"""资源上传类型， -1表示不查询，0媒体资源，1外链地址"""
	resourceUploadType:Int!
	"""工种分类->工种路径，以/开始,/分隔，只需要填两级"""
	workTypePaths:[String]
	"""工种"""
	workTypes:[String]
	"""标签
		<p>资源平台标签关键字查询，添哥说按照选标签模式做，这里使用标签id集合</p>
	"""
	tagIds:[String]
	"""开发资源使用平台"""
	openUsedPlatforms:[String]
	"""学分起"""
	periodBegin:Double
	"""学分止"""
	periodEnd:Double
	"""创建的资源供应商id"""
	createResourceVendorId:String
	"""是否已分配"""
	arrangeUsedPlatform:Boolean!
	"""是否启用， -1表示不查询，0表示不启用，1表示启用"""
	enable:Int!
	"""创建时间查询的起始时间"""
	startCreateTime:String
	"""创建时间查询的截止时间"""
	endCreateTime:String
	"""课件供应商id集合"""
	coursewareSupplierIds:[String]
}
"""<AUTHOR>
	@date 2021/6/29 14:14
	@Description:
"""
input BTPXCoursePoolCreateRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.coursepool.BTPXCoursePoolCreateRequest") {
	"""课程供应商id【必填】"""
	coursewareSupplierId:String
	"""必填，课程池名称"""
	poolName:String
	"""过期时间,null表示不设置过期"""
	expireTime:DateTime
	"""课程池描述"""
	poolDescription:String
	"""展示名称"""
	showName:String
	"""包内课程"""
	courseInPoolList:[CourseInPoolCreateRequest]
	"""课程包所属单位id"""
	unitId:String
}
"""<AUTHOR>
	@date 2021/6/29 14:17
	@Description:
"""
input BTPXCoursePoolUpdateRequest @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.request.coursepool.BTPXCoursePoolUpdateRequest") {
	"""课程供应商id【必填】"""
	coursewareSupplierId:String
	"""课程包id"""
	id:String
	"""必填，课程池名称"""
	poolName:String
	"""排序序号"""
	sequence:Int!
	"""课程池描述"""
	poolDescription:String
	"""展示名称"""
	showName:String
	"""包内课程"""
	courseInPoolList:[CourseInPoolUpdateRequest]
}
input TagDTO @type(value:"com.fjhb.platform.core.course.v1.api.dto.TagDTO") {
	id:String
	code:String
	tag:String
}
"""资源平台课程分页项
	<AUTHOR> create 2020/6/10 9:58
"""
type BTPXCourseItemResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.course.BTPXCourseItemResponse") {
	"""课程编号"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属单位"""
	unit:UnitResponse
	"""所属组织机构ID"""
	organizationId:String
	"""课程分类"""
	categoryList:[CourseCategoryResponse]
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""权重,表示学时,学分等"""
	period:Double!
	"""课程简介"""
	abouts:String
	"""创建类型,0表示自建,1表示内置,2表示共享，3表示迁移，4表示授权"""
	createType:Int!
	"""是否启用"""
	enabled:Boolean!
	"""课程状态，-1暂无课件，0解析中，1解析成功，2解析失败"""
	status:Int!
	"""课程详情"""
	contents:String
	"""创建时间"""
	createTime:String
	"""创建者编号"""
	createUsrId:String
	"""共享来源id"""
	shareSourceId:String
	"""工种"""
	workTypes:[TagDTO1]
	"""工种分类->工种路径，以/开始,/分隔，只需要填两级"""
	workTypePaths:[String]
	"""标签（除工种外的标签）"""
	otherTags:[TagDTO1]
	"""开放使用的平台"""
	openUsedPlatforms:[String]
	"""教师集合"""
	teachers:[TeacherInfoResponse]
	"""资源供应商id"""
	resourceVendorId:String
	"""总时长"""
	timeLength:Long!
	"""课件供应商id"""
	coursewareSupplierId:String
}
"""资源平台课程响应
	<AUTHOR> create 2020/6/8 10:34
"""
type BTPXCourseResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.course.BTPXCourseResponse") {
	"""课程ID"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""课程名称"""
	name:String
	"""封面图片路径"""
	iconPath:String
	"""权重,表示学时,学分等"""
	period:Double!
	"""课程目录"""
	courseOutline:[CourseOutLineWithSubOutlineResponse]
	"""课程分类"""
	categoryList:[CourseCategoryResponse]
	"""课程简介"""
	abouts:String
	"""创建类型,0表示自建,1表示内置,2表示共享，3表示迁移"""
	createType:Int!
	"""是否启用"""
	enabled:Boolean!
	"""是否被逻辑删除"""
	delete:Boolean!
	"""课程的课件状态，0表示解析中，1表示解析成功，2表示解析失败"""
	status:Int!
	"""完成时间"""
	completedTime:DateTime
	"""课程详情"""
	contents:String
	"""创建时间"""
	createTime:DateTime
	"""创建者编号"""
	createUsrId:String
	"""计划授课讲数"""
	plannedLecturesNum:Int!
	"""工种"""
	workTypes:[TagDTO1]
	"""工种分类->工种路径，以/开始,/分隔，只需要填两级"""
	workTypePaths:[String]
	"""标签（除工种外的标签）"""
	otherTags:[TagDTO1]
	"""开放使用的平台"""
	openUsedPlatforms:[String]
	"""教师集合"""
	teacherIds:[String]
	"""资源供应商id"""
	resourceVendorId:String
	"""课件供应商id"""
	coursewareSupplierId:String
}
"""资源平台课程统计
	<AUTHOR>
	@date 2020/6/17 12:37
"""
type BTPXCourseStatisticResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.course.BTPXCourseStatisticResponse") {
	"""课程数量"""
	count:Long!
	"""总学时数"""
	totalPeriod:Double!
}
"""教师信息响应
	<AUTHOR> create 2020/6/10 11:04
"""
type TeacherInfoResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.course.TeacherInfoResponse") {
	"""教师ID"""
	id:String
	"""教师名称"""
	name:String
	"""教师头像"""
	photo:String
	"""教师简介"""
	abouts:String
}
"""<AUTHOR>
	@date 2021/6/29 16:57
	@Description:
"""
type BTPXCoursePoolItemResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.coursepool.BTPXCoursePoolItemResponse") {
	"""课程供应商id"""
	courseSupplierId:String
	"""课程供应商名称"""
	courseSupplierName:String
	"""课程池编号"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属服务单位ID"""
	unitId:String
	"""单位信息"""
	unit:UnitResponse
	"""所属组织机构ID"""
	organizationId:String
	"""课程池名称"""
	poolName:String
	"""展示名称"""
	showName:String
	"""课程池状态|0/1/2，正常/无效/过期"""
	poolState:Int!
	"""排序序号"""
	sequence:Int!
	"""创建人编号"""
	createUserId:String
	"""创建人"""
	creator:OperatorResponse
	"""创建时间"""
	createTime:String
	"""更新时间"""
	updateTime:DateTime
	"""过期时间,null表示不设置过期"""
	expireTime:DateTime
	"""课程池描述"""
	poolDescription:String
	"""课程池内课程数量"""
	courseCount:Int!
	"""课程池内课程权重值总和"""
	totalQuantitative:Double!
	"""课程池内课程学时或学分总和"""
	totalPeriod:Double!
}
"""<AUTHOR>
	@date 2021/6/29 15:30
	@Description:
"""
type BTPXCoursePoolResponse @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.response.coursepool.BTPXCoursePoolResponse") {
	"""课程供应商id"""
	coursewareSupplierId:String
	"""课程供应商名称"""
	coursewareSupplierName:String
	"""课程包id"""
	id:String
	"""所属平台ID"""
	platformId:String
	"""必填，所属平台版本ID"""
	platformVersionId:String
	"""必填，所属项目ID"""
	projectId:String
	"""必填，所属子项目ID"""
	subProjectId:String
	"""必填，所属服务单位ID"""
	unitId:String
	"""单位信息"""
	unit:UnitResponse
	"""必填，所属组织机构ID"""
	organizationId:String
	"""必填，课程池名称"""
	poolName:String
	"""排序序号，默认1"""
	sequence:Int!
	"""必填，创建人编号"""
	createUsrId:String
	"""过期时间,null表示不设置过期"""
	expireTime:DateTime
	"""课程池描述"""
	poolDescription:String
	"""展示名称"""
	showName:String
	"""创建人"""
	creator:OperatorResponse
	"""创建时间"""
	createTime:DateTime
	"""课程池内课程数量"""
	courseCount:Int!
	"""课程池内课程学时或学分总和"""
	totalPeriod:Double!
}
type TagDTO1 @type(value:"com.fjhb.platform.core.course.v1.api.dto.TagDTO") {
	id:String
	code:String
	tag:String
}
extend type Mutation {
	"""分配课程"""
	arrangeCourse(courseArrange:BTPXCourseArrangeRequest):Void
	"""修改课程
		@param update
		@return
	"""
	updateBTPXCourse(update:BTPXCourseUpdateRequest):BTPXCourseResponse
	"""批量分配课程
		@param request
	"""
	batchArrangeCourse(request:ArrangeCourseRequest):Void
	"""导出课程分页成excel文档
		@param query
	"""
	exportCoursePageInfo(query:BTPXCourseQueryRequest):Void
	"""导出课程分页成excel文档
		@param query
	"""
	exportNotArrangeCoursePageInfo(query:BTPXCourseQueryRequest):Void
	"""新建课程包
		@param create
		@return
	"""
	createBTPXCoursePool(create:BTPXCoursePoolCreateRequest):BTPXCoursePoolResponse
	"""修改课程包
		@param update
		@return
	"""
	updateBTPXCoursePool(update:BTPXCoursePoolUpdateRequest):BTPXCoursePoolResponse
}
extend type Query {
	"""查询课程池dto
		@param poolId
		@return
	"""
	getBTPXCoursePoolDTO(poolId:String):BTPXCoursePoolResponse
	"""分页查询课程池
		@param page
		@param query
		@return
	"""
	getBTPXCoursePoolPage(page:Page,query:BTPXCoursePoolQueryDTO,authorizedQuery:ResAuthorizedQuery):BTPXCoursePoolItemResponsePage @page(for:"BTPXCoursePoolItemResponse")
	"""查询供应商课程分页
		@param page
		@param query
		@return
	"""
	findCoursePageForCoursewareSupplier(page:Page!,query:CoursewareSupplierCourseQueryRequest!):BTPXCourseItemResponsePage @page(for:"BTPXCourseItemResponse")
	"""查询机构课程分页
		@param page
		@param query
		@return
	"""
	findCoursePageForTrainingInstitution(page:Page!,query:TrainingInstitutionCourseQueryRequest!):BTPXCourseItemResponsePage @page(for:"BTPXCourseItemResponse")
	"""查询资源平台课程分页
		@param page
		@param query
		@return
	"""
	findCoursePage(page:Page!,query:BTPXCourseQueryRequest!):BTPXCourseItemResponsePage @page(for:"BTPXCourseItemResponse")
	"""获取资源平台课程
		@param id
		@return
	"""
	findCourse(id:String!):BTPXCourseResponse
	"""统计资源平台课程信息 ， 资源供应商查看自身资源库下数据， 其余单位查看共享池内数据
		@return
	"""
	getCourseStatistic:BTPXCourseStatisticResponse
	"""门户用只展示可用课程，统计资源平台课程信息 ， 资源供应商查看自身资源库下数据， 其余单位查看共享池内数据
		@return
	"""
	getCourseStatisticForPortal:BTPXCourseStatisticResponse @NotAuthenticationRequired
	"""门户用只展示可用课程，查询资源平台课程分页
		@param page
		@param query
		@return
	"""
	getCoursePageForPortal(page:Page!,query:BTPXCourseQueryRequest!):BTPXCourseItemResponsePage @page(for:"BTPXCourseItemResponse") @NotAuthenticationRequired
	"""获取资源平台课程
		@param id
		@return
	"""
	getCourseForPortal(id:String!):BTPXCourseResponse @NotAuthenticationRequired
}

scalar List
type CourseInPoolItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseInPoolItemResponse]}
type CourseItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseItemResponse]}
type CoursePoolItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CoursePoolItemResponse]}
type CourseWareItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CourseWareItemResponse]}
type TeacherItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TeacherItemResponse]}
type BTPXCoursePoolItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BTPXCoursePoolItemResponse]}
type BTPXCourseItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [BTPXCourseItemResponse]}
