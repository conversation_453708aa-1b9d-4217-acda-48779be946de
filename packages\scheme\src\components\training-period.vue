<template>
  <el-card shadow="never" class="m-card f-mb15">
    <el-row :gutter="20" class="m-farewell" v-if="issue.issueConfigList.length">
      <el-col :sm="8" :md="6" v-for="(item, index) in issue.issueConfigList" :key="index">
        <div class="item">
          <div class="item-tit">
            <div class="tit">
              <span class="txt">{{ item.issueName }}</span>
              <el-tag
                :type="trainIssueStatus.getTagType(getTrainIssueStatus(item))"
                v-show="showTrainIssueStatus(item)"
              >
                {{ trainIssueStatus.map.get(getTrainIssueStatus(item)) }}
              </el-tag>
            </div>

            <div class="num">{{ item.issueNo }}</div>
          </div>
          <div class="item-hd">
            <div class="f-flex f-justify-between">
              <div class="ele">
                <div class="ele-t">培训时段</div>
                <!--                v-if="item.issueCourseList.length"-->
                <div class="ele-info f-to" v-if="item.trainingDateRange.startDate || item.trainingDateRange.endDate">
                  {{ dateFormat(item.trainingDateRange.startDate) }} 至 {{ dateFormat(item.trainingDateRange.endDate) }}
                </div>
                <div class="ele-info f-to" v-else>——</div>
              </div>
              <div class="ele">
                <div class="ele-t">开放报名人数</div>
                <div class="ele-info f-to">{{ item.openRegistrationNum }}人</div>
              </div>
            </div>

            <div class="ele">
              <div class="ele-t">开放报名时间</div>
              <div class="ele-info f-to">
                {{ item.registerBeginTime || '立即开启' }} 至 {{ item.registerEndTime || '无关闭时间' }}
              </div>
            </div>
            <div class="ele">
              <div class="ele-t">培训地点</div>
              <div class="ele-info f-to-two">
                {{ item.trainingPointName }}
              </div>
            </div>
          </div>
          <div class="item-bd">
            <div class="bd-l">
              <el-tooltip effect="dark" placement="top" content="复制期别" v-if="!type">
                <span class="btn-icon el-icon-document-copy" @click="copyIssue(item)"></span>
              </el-tooltip>
              <el-tooltip effect="dark" placement="top" content="编辑期别" v-if="!type">
                <span class="btn-icon el-icon-edit-outline" @click="operateIssue(OperateTypeEnum.UPDATE, item)"></span>
              </el-tooltip>
              <el-tooltip effect="dark" placement="top" content="查看详情">
                <span class="btn-icon p-icon icon-1" @click="viewIssue(item)"></span>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                placement="top"
                content="添加课程"
                v-if="!item.issueCourseList.length && !type && showCourse(item)"
              >
                <span class="btn-icon p-icon icon-2" @click="editCourse(item)"></span>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                placement="top"
                content="编辑课程"
                v-if="item.issueCourseList.length && !type && showCourse(item)"
              >
                <span class="btn-icon p-icon icon-3" @click="editCourse(item)"></span>
              </el-tooltip>
              <el-tooltip effect="dark" placement="top" content="查看课程" v-if="showCourse(item)">
                <span class="btn-icon p-icon icon-4" @click="viewCourse(item)"></span>
              </el-tooltip>
            </div>
            <div class="bd-r" v-if="!type">
              <el-tooltip effect="dark" placement="top" content="删除">
                <span class="btn-icon el-icon-delete" @click="removeIssue(item)"></span>
              </el-tooltip>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :sm="8" :md="6" v-if="!type">
        <div class="add-btn" @click="operateIssue(OperateTypeEnum.CREATE)">
          <i class="el-icon-plus"></i>
          <p class="txt">添加期别</p>
        </div>
      </el-col>
    </el-row>
    <div class="m-no-date" v-else>
      <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
      <div class="date-bd">
        <p class="f-f15 f-c9">您当前还未添加培训期别，请点击下方按钮进行添加~</p>
        <div class="f-mt10">
          <el-button type="primary" size="small" @click="operateIssue(OperateTypeEnum.CREATE)">添加期别</el-button>
        </div>
      </div>
    </div>
    <el-card shadow="never" class="m-card is-header">
      <div class="m-tit is-small bg-gray is-border-bottom">
        <span class="tit-txt">培训期别学员须知</span>
      </div>
      <div class="f-p20">
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" label-width="180px" class="m-form f-mt10">
              <el-form-item label="学员须知：">
                <el-input
                  type="textarea"
                  :disabled="!this.issue.isSelected || !!type"
                  v-model="trainClassBaseInfo.issueNotice"
                  placeholder="请输入学员须知内容..."
                  rows="5"
                  show-word-limit
                ></el-input>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-card>
    <operate-issue-drawer
      ref="issueRef"
      :operateIssueDrawer.sync="operateIssueConfig.drawer"
      :selectIssue.sync="operateIssueConfig.selectIssue"
      :operate="operateIssueConfig.operate"
      :issueConfigList="issue.issueConfigList"
      :trainClassBaseInfo="trainClassBaseInfo"
      :learningTypeModelCopy="learningTypeModelCopy"
      :routerMode="routerMode"
      :hasSignUp="
        operateIssueConfig.selectIssue.operationType === OperationTypeEnum.update && operateIssueConfig.hasSignUp
      "
      v-if="operateIssueConfig.drawer"
      :has-disabled="isDisabled"
      @save="saveIssue"
    ></operate-issue-drawer>
    <view-issue-drawer
      :viewIssueDrawer.sync="operateIssueConfig.viewDrawer"
      :selectIssue="operateIssueConfig.selectIssue"
      v-if="operateIssueConfig.viewDrawer"
    ></view-issue-drawer>
    <operate-course
      ref="operateCourseRef"
      :operateCourse.sync="operateIssueConfig.courseDrawer"
      :selectIssue="operateIssueConfig.selectIssue"
      :trainClassBaseInfo="trainClassBaseInfo"
      :title="operateIssueConfig.courseDrawerTitle"
      :hasSignUp="
        operateIssueConfig.selectIssue.operationType === OperationTypeEnum.update && operateIssueConfig.hasSignUp
      "
      :batchCourseDrawer.sync="operateIssueConfig.batchCourseDrawer"
      :has-disabled="isDisabled"
      :skipTime="skipTime"
      @save="saveCourse"
    ></operate-course>
    <view-course
      :viewCourse.sync="operateIssueConfig.viewCourseDrawer"
      :selectIssue="operateIssueConfig.selectIssue"
      :trainClassBaseInfo="trainClassBaseInfo"
    ></view-course>
    <batch-import-course
      :batchCourseDrawer.sync="operateIssueConfig.batchCourseDrawer"
      :selectIssue="operateIssueConfig.selectIssue"
      @importSuccess="importSuccess"
    ></batch-import-course>
  </el-card>
</template>
<script lang="ts">
  import OperateCourse from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/operate-course.vue'
  import OperateIssueDrawer from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/operate-issue-drawer.vue'
  import ViewCourse from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/view-course.vue'
  import ViewIssueDrawer from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/view-issue-drawer.vue'
  import { OperateTypeEnum } from '@hbfe/jxjy-admin-scheme/src/models/OperateTypeEnum'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import issueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import IssueLearningType from '@api/service/common/scheme/model/IssueLearningType'
  import SchemeBaseInfo from '@api/service/common/scheme/model/SchemeBaseInfo'
  import { cloneDeep } from 'lodash'
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
  import ServiceTime from '@api/service/common/service-time/ServiceTime'
  import TrainIssueStatus, { TrainIssueStatusEnum } from '@hbfe/jxjy-admin-scheme/src/models/TrainIssueStatus'
  import MutationCreateTrainClassCommodity from '@api/service/management/train-class/mutation/MutationCreateTrainClassCommodity'
  import { OperationTypeEnum } from '@api/service/common/scheme/enum/OperationType'
  import Mockjs from 'mockjs'
  import BatchImportCourse from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/batch-import-course.vue'
  import IssueCourseDetail from '@api/service/common/scheme/model/IssueCourseDetail'
  import QuestionnaireConfigDetail from '@api/service/common/scheme/model/QuestionnaireConfigDetail'
  import AssessSettingInfo from '@api/service/common/scheme/model/AssessSettingInfo'
  import MultipleAssessLearningTypeInfo from '@api/service/common/scheme/model/MultipleAssessLearningTypeInfo'
  import { TeachPlanAssessTypeEnum } from '@api/service/common/scheme/enum/AssessType'
  import { IssueTrainingDateTypeEnum } from '@api/service/common/scheme/enum/IssueTrainingDateType'
  import TeachingPlanItemsGroup from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/teaching-plan-items-groups/TeachingPlanItemsGroup'
  import TeachingPlanItemGroupInfo from '@api/service/common/scheme/model/TeachingPlanItemGroupInfo'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import DateRange from '@api/service/common/scheme/model/DateRange'

  @Component({
    components: { BatchImportCourse, ViewCourse, ViewIssueDrawer, OperateCourse, OperateIssueDrawer }
  })
  export default class extends Vue {
    @Ref('operateCourseRef') operateCourseRef: OperateCourse

    @Ref('issueRef') issueRef: OperateIssueDrawer
    /**
     * 期别
     */
    @Prop({ type: Object, default: () => new IssueLearningType() }) issue: IssueLearningType
    @Prop({ type: Object, default: () => new SchemeBaseInfo() }) trainClassBaseInfo: TrainClassBaseModel
    /**
     * 是否重算中
     */
    @Prop({ type: Boolean, default: false })
    recalculating: boolean
    /**
     * 是否智能学习中
     */
    @Prop({ type: Boolean, default: false })
    isIntelligenceLearning: boolean
    /**
     * 初始数据
     */
    @Prop({
      type: LearningType,
      default: () => new LearningType()
    })
    learningTypeModelCopy: LearningType
    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number
    /**
     * 类型
     * 详情
     */
    @Prop({ type: String, default: '' }) type: string
    /**
     * 期别操作配置
     */
    operateIssueConfig = {
      operate: OperateTypeEnum.CREATE,
      drawer: false,
      viewDrawer: false,
      courseDrawer: false,
      courseDrawerTitle: '添加课程',
      viewCourseDrawer: false,
      batchCourseDrawer: false,
      selectIssue: new IssueConfigDetail(),
      hasSignUp: false
    }
    /**
     * 获取服务器时间响应式数据
     */
    serviceTime = ServiceTime

    /**
     * 操作类型枚举
     * @type {OperateTypeEnum}
     */
    OperateTypeEnum = OperateTypeEnum
    OperationTypeEnum = OperationTypeEnum

    /**
     * 获取培训状态类型
     */
    trainIssueStatus = new TrainIssueStatus()

    skipTime = false
    /**
     * 是否禁用
     */
    get isDisabled() {
      return this.recalculating || this.isIntelligenceLearning
    }
    /**
     * 是否展示课程
     */
    get showCourse() {
      return (item: issueConfigDetail) => {
        return item.issueTrainingDateType == IssueTrainingDateTypeEnum.by_issue_courses
      }
    }

    /**
     * 是否展示培训状态
     */
    get showTrainIssueStatus() {
      return (item: issueConfigDetail) => {
        return item.trainingDateRange.startDate
      }
    }

    /**
     * 是否创建课表
     */
    get isEditCourse() {
      return (item: issueConfigDetail) => {
        if (
          item.operationType == OperationTypeEnum.update &&
          item.issueTrainingDateType == IssueTrainingDateTypeEnum.by_issue_courses
        ) {
          return true
        }
        return item.issueCourseList.length && !this.type && this.showCourse(item)
      }
    }
    /**
     * 培训状态
     */
    get getTrainIssueStatus() {
      return (row: issueConfigDetail) => {
        if (
          row.trainingDateRange.endDate &&
          this.$moment(this.serviceTime.currentServiceTime).isAfter(
            this.$moment(row.trainingDateRange.endDate).endOf('day')
          )
        ) {
          return TrainIssueStatusEnum.ended
        } else if (
          row.trainingDateRange.startDate &&
          this.$moment(this.serviceTime.currentServiceTime).isAfter(
            this.$moment(row.trainingDateRange.startDate).startOf('day')
          )
        ) {
          return TrainIssueStatusEnum.ongoing
        } else {
          return TrainIssueStatusEnum.notStarted
        }
      }
    }
    created() {
      this.serviceTime.getServiceTime()
      this.skipTime = ConfigCenterModule.getFrontendApplication(frontendApplication.skipTimeVerification) == 'true'
    }
    /**
     * 新增期别
     */
    async operateIssue(operateType: OperateTypeEnum, row?: IssueConfigDetail) {
      if (this.canOperate()) return
      if (!this.trainClassBaseInfo.skuProperty.year.skuPropertyValueId) {
        this.$message.error('请先配置培训方案年度！')
        return
      }
      // if (
      //   operateType == OperateTypeEnum.CREATE &&
      //   !this.trainClassBaseInfo.skuProperty.trainingCategory.skuPropertyValueId
      // )
      //   return this.$confirm('方案培训类别未配置，请先进行配置！')
      this.operateIssueConfig.operate = operateType
      this.operateIssueConfig.selectIssue = cloneDeep(row) ?? new IssueConfigDetail()
      console.log(this.operateIssueConfig.selectIssue, 'xxxxxxx')
      if (this.operateIssueConfig.selectIssue.operationType === OperationTypeEnum.update) {
        this.serviceTime.getServiceTime()
        const res = await this.checkIssueSignUpData(this.operateIssueConfig.selectIssue.id)
        if (typeof res === 'boolean') this.operateIssueConfig.hasSignUp = res
        else return this.$message.error(res)
      }
      //if (operateType == OperateTypeEnum.CREATE) this.operateIssueConfig.selectIssue.issueNo = this.addIssueNo()
      this.operateIssueConfig.drawer = true
    }

    /**
     * 复制期别
     */
    copyIssue(row: IssueConfigDetail) {
      this.operateIssueConfig.operate = OperateTypeEnum.COPY
      this.operateIssueConfig.selectIssue = cloneDeep(row)
      this.operateIssueConfig.selectIssue.id = Mockjs.Random.guid()
      this.operateIssueConfig.selectIssue.issueNo = ''
      this.operateIssueConfig.selectIssue.issueName += '_复制'
      this.operateIssueConfig.selectIssue.configId = ''
      this.operateIssueConfig.selectIssue.assessSettings = []
      this.operateIssueConfig.selectIssue.trainingConfigConfigureId = ''
      if (this.operateIssueConfig.selectIssue.issueTrainingDateType === IssueTrainingDateTypeEnum.by_issue_courses) {
        this.operateIssueConfig.selectIssue.trainingDateRange = new DateRange()
      }
      this.operateIssueConfig.selectIssue.relateTeachPlanLearning =
        new MultipleAssessLearningTypeInfo<TeachPlanAssessTypeEnum>()
      this.operateIssueConfig.selectIssue.teachingPlanItemsGroupsBackup = [] as TeachingPlanItemsGroup[]
      this.operateIssueConfig.selectIssue.perQuestionnaireBackupMap = new Map<string, QuestionnaireConfigDetail>()
      this.operateIssueConfig.selectIssue.operationType = OperationTypeEnum.create
      this.operateIssueConfig.selectIssue.issueCourseList.forEach((item) => {
        item.id = IssueCourseDetail.customPrefixId + Mockjs.Random.guid()
        item.belongGroupInfo = new TeachingPlanItemGroupInfo()
        item.configId = ''
        item.assessSettings = [] as AssessSettingInfo[]
        item.teacherId = ''
        item.operationType = OperationTypeEnum.create
        item.teachingDate = ''
        item.courseBeginTime = ''
        item.courseEndTime = ''
      })
      this.operateIssueConfig.drawer = true
    }

    /**
     * 保存期别
     * @param openCourse 是否打开编辑课程
     */
    saveIssue(openCourse: boolean) {
      if (this.operateIssueConfig.operate == OperateTypeEnum.CREATE) {
        this.issue.issueConfigList.push(cloneDeep(this.operateIssueConfig.selectIssue))
      } else if (this.operateIssueConfig.operate == OperateTypeEnum.COPY) {
        this.issue.issueConfigList.push(this.operateIssueConfig.selectIssue)
      } else {
        const item = this.issue.issueConfigList.find((item) => item.id === this.operateIssueConfig.selectIssue.id)
        item && Object.assign(item, this.operateIssueConfig.selectIssue)
        this.operateIssueConfig.selectIssue = cloneDeep(item)
      }
      this.sortIssue()
      if (openCourse) this.operateIssueConfig.courseDrawer = true
    }

    /**
     * 期别排序
     */
    sortIssue() {
      this.issue.issueConfigList.sort((a, b) => {
        if (a.trainingDateRange.startDate && b.trainingDateRange.startDate) {
          return new Date(a.trainingDateRange.startDate).getTime() - new Date(b.trainingDateRange.startDate).getTime()
        } else {
          return 0
        }
      })
    }

    /**
     * 补充期别
     */
    addIssueNo() {
      let newPeriod = 1
      while (this.issue.issueConfigList.map((item) => item.issueNo).includes(newPeriod.toString().padStart(2, '0'))) {
        newPeriod++
      }
      return newPeriod.toString().padStart(2, '0')
    }
    /**
     * 查看期别
     */
    viewIssue(row: IssueConfigDetail) {
      this.operateIssueConfig.selectIssue = row
      this.operateIssueConfig.viewDrawer = true
    }

    /**
     * 移除期别
     */
    async removeIssue(row: IssueConfigDetail) {
      if (this.isDisabled) {
        return
      }
      this.operateIssueConfig.selectIssue = row
      this.operateIssueConfig.operate = OperateTypeEnum.REMOVE
      if (this.operateIssueConfig.selectIssue.operationType === OperationTypeEnum.update) {
        const res = await this.checkIssueSignUpData(this.operateIssueConfig.selectIssue.id)
        if (typeof res === 'boolean' && res) {
          return this.$confirm('当前班级培训期别已存在交易记录，无法删除。', '提示', {
            confirmButtonText: '确定',
            showCancelButton: false
          })
        } else if (typeof res === 'string') return this.$message.error(res)
      }
      this.$confirm('删除后已配置信息不可恢复，是否确定操作？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(() => {
        const index = this.issue.issueConfigList.findIndex((item) => item.id === row.id)
        index >= 0 && this.issue.issueConfigList.splice(index, 1)
        this.$emit('removeIssue', row.id)
        this.$message.success('删除成功！')
      })
    }

    /**
     * 编辑课程
     */
    async editCourse(row: IssueConfigDetail) {
      if (this.operateIssueConfig.selectIssue.operationType === OperationTypeEnum.update) {
        this.serviceTime.getServiceTime()
      }
      this.operateIssueConfig.courseDrawerTitle = row.issueCourseList.length ? '编辑课程' : '添加课程'
      this.operateIssueConfig.selectIssue = cloneDeep(row)
      this.operateIssueConfig.courseDrawer = true
      // this.issueRef
    }

    /**
     * 导入成功
     */
    importSuccess() {
      this.operateCourseRef?.onOpen()
    }
    /**
     * 保存课程
     */
    saveCourse() {
      const item = this.issue.issueConfigList.find((item) => item.id === this.operateIssueConfig.selectIssue.id)
      if (item) {
        if (this.operateIssueConfig.selectIssue.issueCourseList.length) {
          item.issueCourseList = this.operateIssueConfig.selectIssue.issueCourseList.sort(
            (a, b) => new Date(a.fullBeginDate).getTime() - new Date(b.fullBeginDate).getTime()
          )
          if (this.showCourse(item)) {
            item.trainingDateRange.startDate = item.issueCourseList[0].fullBeginDate
            item.trainingDateRange.endDate = item.issueCourseList[item.issueCourseList.length - 1].fullEndDate
          }
        } else {
          item.issueCourseList = []
          item.trainingDateRange.dateRange = null
        }
      }
      this.sortIssue()
      this.$message.success('保存成功！')
      this.operateIssueConfig.courseDrawer = false
    }

    /**
     * 查看课程
     */
    viewCourse(row: IssueConfigDetail) {
      this.operateIssueConfig.selectIssue = row
      this.operateIssueConfig.viewCourseDrawer = true
    }

    /**
     * 是否可操作
     * @returns {boolean}
     */
    canOperate() {
      if (!this.issue.isSelected) {
        this.$message.error('请先勾选“培训期别”的学习内容！')
        return true
      }
      return false
    }

    /**
     * 时间转换
     */
    dateFormat(date: string) {
      return date ? this.$moment(date).format('YYYY-MM-DD') : ''
    }

    /**
     * 校验期别是否存在报名数据
     */
    async checkIssueSignUpData(issueId: string) {
      const res = await new MutationCreateTrainClassCommodity().validateIssueSignUpData(issueId)
      if (res.isSuccess()) return false
      else if (res?.code === 80002) return true
      return '校验期别是否存在报名数据请求错误'
    }
  }
</script>

<style scoped lang="scss"></style>
