import { CreateStudentRequest } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import CreateUserIndustryRequestVo from './CreateUserIndustryRequestVo'

/*
 *创建学员信息
 */
class CreateStudentRequestVo extends CreateStudentRequest {
  /**
   * 证件号码
   */
  idCard = ''
  /**
   * 用户名称
   */
  name = ''
  /**
   * 手机号
   */
  phone = ''
  /*
    手机验证码
  */
  smsCode = ''
  /**
   * 【必填】用户输入的图片验证码
   */
  captcha = ''
  /**
   * 【必填】图片验证码校验通过生成的token
   */
  token = ''
  /**
   * 单点登录Token
   */
  loginToken?: string = ''
  /**
   * 所属区域
   */
  area = ''
  /**
   * 工作单位
   */
  companyName?: string = ''
  /**
   * 统一社会信用码
   */
  companyCode?: string = ''
  /**
   * 密码
   */
  password = ''
  /*
   确认密码
  */
  confirmPassword = ''
  /**
   * 行业信息
   */
  userIndustryInfos: Array<CreateUserIndustryRequestVo> = new Array<CreateUserIndustryRequestVo>()
  /**
   * [必填]加密值
   */
  encrypt = ''
  /**
   * 性别
   */
  gender: number = null
  // 给证件类型赋初始值
  constructor() {
    super()
    this.idCardType = null
  }
}

export default CreateStudentRequestVo
