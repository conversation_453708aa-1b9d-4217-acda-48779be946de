<template>
  <div class="m-login-wrap">
    <div class="wrap-bd">
      <div class="m-logo"><img src="./assets/images/huabo-logo.png" class="logo-pic" />通用平台运营管理后台</div>
      <div class="m-login">
        <div class="m-login-tit">帐号登录</div>
        <el-form ref="form" :model="form" :rules="rules" label-width="auto" class="m-form">
          <el-form-item prop="name">
            <el-input v-model="form.name" clearable placeholder="请输入帐号/手机号登录">
              <i slot="prefix" class="el-input__icon hb-iconfont icon-s-name"></i>
            </el-input>
          </el-form-item>
          <el-form-item>
            <el-input v-model="form.name" clearable show-password placeholder="请输入密码">
              <i slot="prefix" class="el-input__icon hb-iconfont icon-s-pwd"></i>
            </el-input>
          </el-form-item>
          <el-form-item>
            <div class="yzm">
              <el-input v-model="form.name" clearable show-password placeholder="请输入图形验证码">
                <i slot="prefix" class="el-input__icon iconfont icon-yanzhengma"></i>
              </el-input>
              <div class="yzm-pic"><img src="./assets/images/yzm-pic.jpg" class="img" /></div>
            </div>
          </el-form-item>
          <el-form-item class="is-text op f-pb10">
            <el-checkbox>记住密码</el-checkbox>
            <a href="#" class="f-link f-c9 f-fr">忘记密码？</a>
          </el-form-item>
          <el-form-item class="m-btn-bar">
            <!--<el-alert type="error" show-icon :closable="false" class="m-alert f-mb10">错误提示文案</el-alert>-->
            <el-button type="primary">立即登录</el-button>
          </el-form-item>
        </el-form>
      </div>
    </div>
    <div class="m-login-footer">
      <div>版权所有：福建华博教育科技股份有限公司</div>
      <div>
        <a href="https://beian.miit.gov.cn/#/Integrated/index" target="_blank">闽ICP备08103886号-2</a>
        <span class="f-ml20"> 增值电信业务经营许可证 <a href="###" @click="dialog1 = true">闽B2-20180687</a> </span>
        <el-dialog :visible.sync="dialog1" width="1200px" class="m-dialog">
          <img width="1160px" src="./assets/images/dianxin_xuke.jpg" alt="" />
        </el-dialog>
      </div>
      <div>
        <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=%2035010202001216" target="_blank">
          <img class="img" src="./assets/images/beian-icon.png" alt=" " />闽公网安备 35010202001216号</a
        >
      </div>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        switch1: false,
        switch2: false,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          name1: '周一至周五 (09:00 - 12:00 14:00 - 17:30)',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down'],
        rules: {
          name: [{ required: true, message: '请输入帐号/手机号', trigger: 'blur' }]
        }
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
