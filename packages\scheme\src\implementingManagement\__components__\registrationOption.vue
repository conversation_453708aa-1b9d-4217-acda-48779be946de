<template>
  <el-card shadow="never" class="m-card f-mb15">
    <div slot="header">
      <span class="tit-txt">报到设置</span>
    </div>
    <template v-if="reportConfig.isOpenReportConfig">
      <div class="m-sub-tit">
        <span class="tit-txt">指定面授期别报到设置</span>
      </div>
      <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20">
        若有针对指定期别有特殊的报到规则，可选择指定面授期别单独设置。设置完成后期别按指定规则执行。
      </el-alert>
      <el-table
        ref="tableRef"
        stripe
        :data="reportConfig.periodInfoList"
        max-height="500px"
        class="m-table f-mt15"
        v-loading="uiLoading.tableLoading"
      >
        <el-table-column label="期别编号" min-width="100" fixed="left">
          <template v-slot="{ row }">{{ row.no }}</template>
        </el-table-column>
        <el-table-column label="期别名称" width="200">
          <template v-slot="{ row }">
            <div class="flex">
              <el-tooltip effect="dark" :content="row.name" placement="top">
                <div class="text-ellipsis">{{ row.name }}</div>
              </el-tooltip>
              <el-tag
                v-if="!row.reportSetted && row.isSetReportConfig"
                type="danger"
                size="mini"
                class="f-ml5"
                style="min-width: 46px"
                >未设置
              </el-tag>
              <el-tag v-if="!row.isSetReportConfig" type="danger" size="mini" class="f-ml5" style="min-width: 70px"
                >未开启报到
              </el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="培训报到时段" min-width="200">
          <template v-slot="{ row }">
            <p>
              <el-tag type="info" size="mini">起始</el-tag>
              {{ row.checkInTime.begin || '-' }}
            </p>
            <p>
              <el-tag type="info" size="mini">结束</el-tag>
              {{ row.checkInTime.end || '-' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="培训时段" min-width="200">
          <template v-slot="{ row }">
            <p>
              <el-tag type="info" size="mini">起始</el-tag>
              {{ row.trainingTime.begin || '-' }}
            </p>
            <p>
              <el-tag type="info" size="mini">结束</el-tag>
              {{ row.trainingTime.end || '-' }}
            </p>
          </template>
        </el-table-column>
        <el-table-column label="报到方式" width="200" align="center">
          <template v-slot="{ row }">{{ row.reportConfig.reportingType == 1 ? '扫码报到' : '-' }}</template>
        </el-table-column>
        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template v-slot="{ row }">
            <!-- <el-button type="text" @click="preview(row)">预览听课证</el-button> -->
            <el-button type="text" @click="reportSetDialog(row)" :disabled="!row.isSetReportConfig">设置</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </template>
    <!--通用空数据-->
    <div class="m-no-date" v-else>
      <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
      <div class="date-bd">
        <p class="f-f15 f-c9">班级未开启报到~</p>
      </div>
    </div>
    <stage-report-dialog ref="stageReportDialogRef" @updateSuccess="doRefresh"></stage-report-dialog>
    <temDialog ref="templateDialogRef"></temDialog>
  </el-card>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Inject } from 'vue-property-decorator'
  import TemDialog from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/templateDialog.vue'
  import StageReportDialog from '@hbfe/jxjy-admin-scheme/src/implementingManagement/__components__/stageReportDialog.vue'
  import ReportConfig from '@api/service/management/implement/ReportConfig'
  import { TimeTypeEnum } from '@api/service/management/implement/enums/TimeTypeEnum'
  import { UiPage } from '@hbfe/common'
  import { ReportingTypeEnum } from '@api/service/common/implement/enums/ReportingTypeEnum'
  import PeriodConfig from '@api/service/management/implement/models/PeriodConfig'
  import { cloneDeep } from 'lodash'
  import { bind, debounce } from 'lodash-decorators'
  import SchemeStepProcess from '@api/service/management/train-class/Utils/SchemeStepProcess'
  import { SchemeProcessStatusEnum } from '@api/service/management/train-class/query/enum/SchemeProcessStatusEnum'

  @Component({
    components: {
      StageReportDialog,
      TemDialog
    }
  })
  export default class extends Vue {
    @Ref('stageReportDialogRef') stageReportDialogRef: StageReportDialog
    @Ref('templateDialogRef') templateDialogRef: TemDialog
    @Inject('SchemeStepProcess')
    SchemeStepProcess: SchemeStepProcess

    // 接收报道设置
    @Prop({
      required: true,
      default: () => {
        return new ReportConfig()
      }
    })
    reportConfig: ReportConfig

    constructor() {
      super()
      this.page = new UiPage(this.pageSearch, this.pageSearch)
    }

    // todo
    form = {}
    tableData = [{}]
    uiLoading = {
      tableLoading: false,
      periodLoading: false,
      saveLoading: false
    }
    // 报道设置
    isBaoDao = true
    // 右侧激活项
    activeTabName = 'section1'
    // 报道设置枚举赋值
    ReportingTypeEnum = ReportingTypeEnum
    TimeTypeEnum = TimeTypeEnum

    // 分页
    page: UiPage

    created() {
      this.pageSearch()
    }

    /**
     * 报道设置列表查询
     */
    @bind
    @debounce(200)
    async pageSearch() {
      this.uiLoading.tableLoading = true
      try {
        await this.reportConfig.pageSchemeReportIssueConfig(this.page)
      } catch (e) {
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['tableRef'] as any)?.doLayout()
        this.uiLoading.tableLoading = false
      }
    }

    /**
     * 报到轮询
     */
    async doRefresh() {
      const schemeId = this.$route.params.schemeId
      const loading = this.$loading({
        background: 'rgba(0, 0, 0, 0.8)'
      })
      const res = await this.SchemeStepProcess.getSchemeTaskStatus(schemeId)

      try {
        if (res.code === SchemeProcessStatusEnum.finish) {
          this.page.pageNo = 1
          await this.pageSearch()
          this.$message.success('设置报到成功。')
        } else {
          this.$message.error(res.message as string)
        }
      } catch (e) {
        console.error(e)
        this.$message.error((e.message as string) || '设置报到失败。')
      } finally {
        loading.close()
      }
    }

    /**
     * 报道设置
     */
    reportSetDialog(row: PeriodConfig) {
      this.stageReportDialogRef.periodConfig = cloneDeep(row)
      this.stageReportDialogRef.periodConfig.reportConfig.reportingType = ReportingTypeEnum.scanQr
      this.stageReportDialogRef.openDialog = true
    }

    /**
     * 听课证模版
     */
    openTemplate(row: any) {
      this.templateDialogRef.openDialog = true
    }

    /**
     * 预览
     */
    preview(row: any) {
      window.open('/mfs/Cooper/templates/item.previewUrl', '_blank')
    }
  }
</script>
<style lang="scss" scoped>
  .flex {
    display: flex;
    align-items: center;

    .text-ellipsis {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
</style>
