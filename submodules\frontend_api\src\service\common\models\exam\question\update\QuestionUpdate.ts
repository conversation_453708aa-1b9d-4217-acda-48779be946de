import { QuestionMode, QuestionType } from '@api/service/common/models/exam/enums'
import { TagDTO1 } from '@api/gateway/btpx@GeneralExam-default'
/**
 * <AUTHOR> update 2021/1/28  TODO
 */
class QuestionUpdate {
  /**
   * 试题id
   */
  id: string

  /**
   * 题库id
   */
  libraryId: string
  /**
   * 题目
   */
  title: string
  /**
   * 试题类型
   */
  questionType: QuestionType
  /**
   * 难度
   */
  mode: QuestionMode
  /**
   * 难度值
   */
  difficulty = 0.0
  /**
   * 试题解析
   */
  description = ''

  /**
   * 试题内容
   * @see #questionType
   */
  questionContent: any

  /**
   * 是否启用
   */
  enabled: boolean

  /**
   * 关联课程id
   */
  relateCourseId: string

  /**
   * 标签
   */
  tags?: Array<TagDTO1>
  /**
   * 工种分类->工种路径，以/开始,/分隔，只需要填两级
   */
  workTypePaths?: Array<string>
}

export default QuestionUpdate
