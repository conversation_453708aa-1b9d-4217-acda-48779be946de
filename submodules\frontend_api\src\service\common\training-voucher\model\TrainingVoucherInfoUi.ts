import { TrainingVoucherInfo } from './TrainingVoucherInfo'
import CommonModule from '@api/service/common/common/CommonModule'

export class TrainingVoucherInfoUi extends TrainingVoucherInfo {
  /**不可用原因 */
  reason = ''
  /**ui属性 点击角标扩展信息 */
  expand = false

  getWorkTypes() {
    return this.jobScope?.join(',')
  }

  hasWork() {
    return this.jobScope?.length
  }

  getPeoples() {
    return this.personScope?.join(',') || CommonModule.SUITABLE_ALL
  }

  hasPeople() {
    return this.personScope?.length
  }

  /**
   * 通用的
   */
  isUniversal() {
    return this.personScope.findIndex(el => el === CommonModule.SUITABLE_ALL) > -1 || this.personScope?.length === 0
  }
}
