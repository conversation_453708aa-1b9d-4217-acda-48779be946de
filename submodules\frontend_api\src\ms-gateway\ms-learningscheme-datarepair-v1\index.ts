import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-learningscheme-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-learningscheme-datarepair-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 学员考试修复请求
<AUTHOR>
 */
export class StudentExamRepairRequest {
  schemeId: string
  qualificationIds: Array<string>
  score: number
}

/**
 * 参训资格状态修改为冻结请求
 */
export class TrainingQualificationRepairToFrozenRequest {
  qualificationId?: string
  userId?: string
  schemeId?: string
  sourceType?: string
  sourceId?: string
}

/**
 * TODO
<AUTHOR>
 */
export class TrainingQualificationRepairToNormalRequest {
  qualificationId?: string
  userId?: string
  schemeId?: string
  sourceType?: string
  sourceId?: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 方案配置资源修复接口
   * @param configureId 配置id
   * @param mutate 查询 graphql 语法文档
   * @param configureId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async schemeConfigureResourceRepair(
    configureId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.schemeConfigureResourceRepair,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { configureId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async studentExamRepair(
    request: StudentExamRepairRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.studentExamRepair,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 参训资格修复为冻结状态
   * @param request:
   * <AUTHOR> By Cb
   * @since 2024/7/22 14:53
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async trainingQualificationRepairToFrozen(
    request: TrainingQualificationRepairToFrozenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.trainingQualificationRepairToFrozen,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async trainingQualificationRepairToNormal(
    request: TrainingQualificationRepairToNormalRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.trainingQualificationRepairToNormal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
