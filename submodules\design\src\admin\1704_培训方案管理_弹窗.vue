<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <el-button @click="dialog21 = true" type="primary" class="f-mr20">导出列表数据</el-button>
        <el-dialog title="提示" :visible.sync="dialog21" width="400px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb f-f16">导出成功，是否前往下载数据?</p>
              <p class="f-f13 f-mt5">下载入口：导出任务查看- 批量更新方案</p>
            </div>
          </div>
          <div slot="footer">
            <el-button>暂不</el-button>
            <el-button type="primary">前往下载</el-button>
          </div>
        </el-dialog>

        <!--批量更新方案-->
        <el-button @click="dialog22 = true" type="primary" class="f-mr20 f-mb20">批量更新方案</el-button>
        <el-drawer
          title="批量更新方案"
          :visible.sync="dialog22"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="24">
                <el-alert type="warning" :closable="false" class="m-alert">
                  <p>温馨提示：</p>
                  <p>1. 如需批量导入培训班，请先下载 <span class="f-ci">更新培训方案模板</span>。</p>
                  <p>
                    2.
                    【方案上架状态】【方案计划上架时间】【方案计划下架时间】【学习开始时间】、【学习结束时间】、【报名开始时间】、【报名结束时间】、【是否展示在门户】、【是否仅导入开通】支持批量更新；
                  </p>
                </el-alert>
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                  <el-steps direction="vertical" :active="4" class="m-vertical-steps" style="padding: 20px 0;">
                    <el-step title="下载更新培训方案模板，按表格中提示说明填写信息。">
                      <div slot="description">
                        <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-download">
                          下载模板
                        </el-button>
                      </div>
                    </el-step>
                    <el-step title="上传填写好的更新方案表格">
                      <div slot="description">
                        <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                          <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                            选择文件
                          </el-button>
                        </el-upload>
                        <div slot="tip" class="el-upload__tip">
                          <i class="el-icon-warning"></i>
                          <span class="txt">导入的文件格式必须为xls,xlsx文件</span>
                        </div>
                      </div>
                    </el-step>
                  </el-steps>
                  <el-form-item class="m-btn-bar f-tc">
                    <el-button>取消</el-button>
                    <el-button type="primary">确定</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>
        <!--考核重算提示-->
        <el-button type="primary" class="f-mr20" @click="open3">考核重算提示</el-button>
        <!--日志详情-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">日志详情</el-button>
        <el-drawer title="日志详情" :visible.sync="dialog1" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">操作人姓名</span></p>
                <div class="f-c6">
                  <div class="f-mt5">修改课程包名称【福建专技培训平台专业课包】改为【福州专技培训平台专业课】</div>
                </div>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">林林一</span></p>
                <div class="f-c6">
                  <div class="f-mt5">将【选择课程学习方式】改为【取消展示课程学习方式】；</div>
                  <div class="f-mt5">修改内容名称【原属性值】改为【新属性值】；</div>
                  <div class="f-mt5">修改封面</div>
                  <div class="f-mt5">
                    <span class="f-co">方案重算记录：共处理学员 10 人。</span>
                    <el-button type="warning" size="mini" class="f-ml5">查看详情</el-button>
                  </div>
                </div>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">林林二</span></p>
                <div class="f-c6">
                  <div class="f-mt5">修改课程包名称【福建专技培训平台专业课包】改为【福州专技培训平台专业课】</div>
                </div>
              </el-timeline-item>
              <el-timeline-item>
                <p class="f-mb10 f-fb f-f15">2020-11-11 12:20:20 <span class="f-ml30">林林三</span></p>
                <div class="f-c6">
                  <div class="f-mt5">修改课程包名称【福建专技培训平台专业课包】改为【福州专技培训平台专业课】</div>
                </div>
              </el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>
        <!--选择封面图片-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20 f-mb20">选择封面图片</el-button>
        <el-drawer title="封面图片" :visible.sync="dialog3" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <div class="f-ml20">
              <div class="m-pic-list f-clear">
                <div class="item">
                  <img src="./assets/images/default-cover-pic-01.png" class="course-pic" />
                  <div class="info">
                    <div class="f-flex-sub">图片一</div>
                    <el-badge is-dot type="primary" class="badge-status f-cb">已选择</el-badge>
                  </div>
                </div>
                <div class="item">
                  <img src="./assets/images/default-cover-pic-02.png" class="course-pic" />
                  <div class="info">
                    <div class="f-flex-sub">图片二</div>
                    <el-button type="primary" size="mini">选择</el-button>
                  </div>
                </div>
                <div class="item">
                  <img src="./assets/images/default-cover-pic-03.png" class="course-pic" />
                  <div class="info">
                    <div class="f-flex-sub">图片三</div>
                    <el-button type="primary" size="mini">选择</el-button>
                  </div>
                </div>
                <div class="item">
                  <img src="./assets/images/default-cover-pic-04.png" class="course-pic" />
                  <div class="info">
                    <div class="f-flex-sub">图片四</div>
                    <el-button type="primary" size="mini">选择</el-button>
                  </div>
                </div>
                <div class="item">
                  <img src="./assets/images/default-cover-pic-05.png" class="course-pic" />
                  <div class="info">
                    <div class="f-flex-sub">图片五</div>
                    <el-button type="primary" size="mini">选择</el-button>
                  </div>
                </div>
                <div class="item">
                  <img src="./assets/images/default-cover-pic-06.png" class="course-pic" />
                  <div class="info">
                    <div class="f-flex-sub">图片六</div>
                    <el-button type="primary" size="mini">选择</el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--新建分类-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20 f-mb20">新建分类</el-button>
        <el-drawer title="新建分类" :visible.sync="dialog2" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="分类名称：" required>
                <el-input
                  v-model="form.name"
                  clearable
                  maxlength="15"
                  show-word-limit
                  placeholder="请输入分类名称，不能超过15字"
                />
              </el-form-item>
              <el-form-item label="上级分类：" required>
                <el-cascader clearable :options="cascader" placeholder="请选择上级分类" />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">保存</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <!--添加课程-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20 f-mb20">添加课程</el-button>
        <el-drawer
          title="添加课程"
          :visible.sync="dialog4"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              提示：课程只能添加在末级分类下，一个分类只能添加一个课程包。
            </el-alert>-->
            <!--            <div class="m-tit">
              <span class="tit-txt">选择课程包</span>
            </div>-->
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="10">
                  <el-form-item label="分类名称：">
                    <el-cascader clearable :options="cascader" placeholder="请选择分类" />
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="课程名称">
                    <el-input v-model="input" clearable placeholder="请输入课程名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="课程名称" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-tag type="danger" size="mini" class="f-mr5">必修</el-tag>
                    课程包名称课程包名称课程包名称课程包名称
                  </div>
                  <div v-else>课程包名称课程包名称课程包名称课程包名称</div>
                </template>
              </el-table-column>
              <el-table-column label="学时" min-width="120" align="center">
                <template>10</template>
              </el-table-column>
              <el-table-column label="查看" width="80" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">详情</el-button>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-radio v-model="radio" label="1">选择</el-radio>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--查看课程包内课程详情-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">查看课程包内课程详情</el-button>
        <el-drawer
          title="查看课程包内课程详情"
          :visible.sync="dialog5"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="课程名称" min-width="300">
                <template>课程名称课程名称课程名称课程名称</template>
              </el-table-column>
              <el-table-column label="学时" min-width="100" align="center">
                <template>13</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
        </el-drawer>
        <!--发布课程测验-->
        <el-button @click="dialog6 = true" type="primary" class="f-mr20 f-mb20">发布 / 编辑课程测验</el-button>
        <el-drawer
          title="发布课程测验"
          :visible.sync="dialog6"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="参加测验要求：" required>
                每门课程学习进度到达 <el-input v-model="form.name" size="small" class="input-num f-mlr5" /> %
              </el-form-item>
              <el-form-item label="测验组卷试题范围：">按课程ID出题</el-form-item>
              <el-form-item label="试卷总分：">100 分</el-form-item>
              <el-form-item label="测验试题数量：" required>
                <el-input-number v-model="num" size="small" /><span class="f-ml10">题</span>
                <div class="f-co el-upload__tip">
                  说明：测验关联的课程若试题数达不到要求，则抽取关联课程所有的试题。试题每题分值按照实际抽取题数平均分。
                </div>
              </el-form-item>
              <el-form-item label="测验及格分：" required>
                <el-input v-model="form.name" size="small" class="input-num f-mr5" /> 分
              </el-form-item>
              <el-form-item label="作答次数：">不限次，合格后不能作答</el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button>取消</el-button>
                <el-button type="primary">确定</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <!--发布试卷-->
        <el-button @click="dialog8 = true" type="primary" class="f-mr20 f-mb20">发布 / 修改试卷</el-button>
        <el-drawer title="发布试卷" :visible.sync="dialog8" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="考试方式：">线上网页考试</el-form-item>
              <el-form-item label="考试场次名称：">
                <el-input v-model="form.name" placeholder="请输入本场次考试名次（开放学员可见）" />
              </el-form-item>
              <el-form-item label="考试模板：">
                华博试卷
                <el-button type="text" class="f-ml10">预览</el-button>
                <el-button type="text" class="f-ml10">替换</el-button>
              </el-form-item>
              <el-form-item label="考试方式：">
                <el-radio-group v-model="form.resource">
                  <el-radio>随到随考</el-radio>
                  <el-radio>
                    固定考试
                    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-info m-tooltip-icon f-c9"></i>
                      <div slot="content">
                        考试需在指定考试时间开始后进入考试，在指定考试时间结束时停止作答。
                      </div>
                    </el-tooltip>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="考试起止时间：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="同培训班的学习起止时间"></el-radio>
                  <el-radio label="指定考试时间"></el-radio>
                </el-radio-group>
              </el-form-item>
              <!--选择 指定考试时间 后出现-->
              <el-form-item label="选择时间：" required>
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  class="form-l"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="考试时长：">
                <el-input v-model="form.name" size="small" class="input-num f-mr5" /> 分钟
              </el-form-item>
              <el-form-item label="作答次数：">
                <el-radio-group v-model="form.resource">
                  <el-radio>不限次</el-radio>
                  <el-radio>
                    限定作答 <el-input v-model="form.name" size="small" class="input-num f-mr5" /> 次
                  </el-radio>
                  <el-radio>
                    限定作答1次
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="合格后是否允许继续作答：">
                <el-radio-group v-model="form.resource">
                  <el-radio>不能继续作答</el-radio>
                  <el-radio>
                    允许继续作答
                    <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-info m-tooltip-icon f-c9"></i>
                      <div slot="content">
                        考试合格后允许继续作答，合格时间取首次合格时间。作答次数还有剩余且在培训结束时间前允许继续作答，并取当前最高分作为考试成绩。
                      </div>
                    </el-tooltip>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="多选题漏选得分：">
                <el-radio-group v-model="form.resource">
                  <el-radio label="不得分"></el-radio>
                  <el-radio label="全得"></el-radio>
                  <el-radio label="得一半"></el-radio>
                  <el-radio label="平均得分"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="成绩公布：">交卷后立即公布（仅针对不含非主观题试卷）</el-form-item>
              <el-form-item label="开放题析：">
                <el-checkbox-group v-model="form.type">
                  <el-checkbox>查看考试结果，开放题析</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
              <el-form-item class="m-btn-bar is-sticky">
                <el-button>放弃编辑</el-button>
                <el-button type="primary">发布考试</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <!--选择试卷模板-->
        <el-button @click="dialog9 = true" type="primary" class="f-mr20 f-mb20">选择试卷模板</el-button>
        <el-drawer
          title="选择试卷模板"
          :visible.sync="dialog9"
          :direction="direction"
          size="1000px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="10">
                  <el-form-item label="试卷名称">
                    <el-input v-model="input" clearable placeholder="请输入试卷名称关键字" />
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="试卷分类">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="试卷名称" min-width="300">
                <template>试卷名称试卷名称试卷名称试卷名称试卷名称</template>
              </el-table-column>
              <el-table-column label="组卷方式" min-width="120" align="center">
                <template>智能组卷</template>
              </el-table-column>
              <el-table-column label="试卷分类" min-width="120" align="center">
                <template>分类属性值</template>
              </el-table-column>
              <el-table-column label="查看" width="80" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">预览</el-button>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-radio v-model="radio" label="1">选择</el-radio>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--选择题库-->
        <el-button @click="dialog10 = true" type="primary" class="f-mr20 f-mb20">选择题库</el-button>
        <el-drawer
          title="选择题库"
          :visible.sync="dialog10"
          :direction="direction"
          size="1000px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="10">
                  <el-form-item label="题库名称">
                    <el-input v-model="input" clearable placeholder="请输入题库名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="题库名称" min-width="300">
                <template>试卷名称试卷名称试卷名称试卷名称试卷名称</template>
              </el-table-column>
              <el-table-column label="已启用的试题数量" width="180" align="center">
                <template>23</template>
              </el-table-column>
              <el-table-column label="创建人" min-width="120">
                <template>创建人</template>
              </el-table-column>
              <el-table-column label="创建时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-checkbox v-model="checked" label="选择"></el-checkbox>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--选择培训证明-->
        <el-button @click="dialog11 = true" type="primary" class="f-mr20 f-mb20">选择培训证明</el-button>
        <el-drawer
          title="选择培训证明"
          :visible.sync="dialog11"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="模板名称" min-width="240">
                <template>模板名称模板名称模板名称</template>
              </el-table-column>
              <el-table-column label="所属行业" width="180" align="center">
                <template>建设</template>
              </el-table-column>
              <el-table-column label="模板说明" min-width="240">
                <template>模板说明模板说明模板说明模板说明模板说明</template>
              </el-table-column>
              <el-table-column label="创建时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column label="查看" width="80" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">预览</el-button>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-radio v-model="radio" label="1">选择</el-radio>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--必学课程管理-->
        <el-button @click="dialog12 = true" type="primary" class="f-mr20 f-mb20">必学课程管理</el-button>
        <el-drawer
          title="必学课程管理"
          :visible.sync="dialog12"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-tit">
              <span class="tit-txt">待选必学课程</span>
            </div>
            <div class="f-ml20">
              <el-row :gutter="16" class="m-query f-mt10">
                <el-form :inline="true" label-width="auto">
                  <el-col :span="10">
                    <el-form-item label="课程名称">
                      <el-input v-model="input" clearable placeholder="请输入课程名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item>
                      <el-button type="primary">查询</el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <div class="f-mb15"><i class="f-fb">所属分类：</i>一级分类 &gt; 二级分类 &gt; 三级分类</div>
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="课程名称" min-width="300">
                  <template>课程名称课程名称课程名称课程名称</template>
                </el-table-column>
                <el-table-column label="学时" min-width="120" align="center">
                  <template>10</template>
                </el-table-column>
                <el-table-column label="查看" width="80" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">查看</el-button>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="100" align="center" fixed="right">
                  <template>
                    <el-checkbox v-model="checked" label="选择"></el-checkbox>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
            <div class="m-tit f-mt20">
              <span class="tit-txt">已选必学课程</span>
            </div>
            <div class="f-ml20">
              <el-row :gutter="16" class="m-query f-mt10">
                <el-form :inline="true" label-width="auto">
                  <el-col :span="10">
                    <el-form-item label="课程名称">
                      <el-input v-model="input" clearable placeholder="请输入课程名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="14">
                    <el-form-item>
                      <el-button type="primary">查询</el-button>
                      <span class="f-ml10">一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时</span>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <div class="f-mb15"><i class="f-fb">所属分类：</i>一级分类 &gt; 二级分类 &gt; 三级分类</div>
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="课程名称" min-width="300">
                  <template>课程名称课程名称课程名称课程名称</template>
                </el-table-column>
                <el-table-column label="学时" min-width="120" align="center">
                  <template>10</template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">查看</el-button>
                    <el-button type="text" size="mini">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <el-pagination
                background
                class="f-mt15 f-tr"
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="currentPage4"
                :page-sizes="[100, 200, 300, 400]"
                :page-size="100"
                layout="total, sizes, prev, pager, next, jumper"
                :total="400"
              >
              </el-pagination>
            </div>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--查看必学课程-->
        <el-button @click="dialog13 = true" type="primary" class="f-mr20 f-mb20">查看必学课程</el-button>
        <el-drawer
          title="已选必学课程"
          :visible.sync="dialog13"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="10">
                  <el-form-item label="课程名称">
                    <el-input v-model="input" clearable placeholder="请输入课程名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="课程分类">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <p class="f-mt15">一共 <i class="f-cr">0</i> 门，<i class="f-cr">0</i> 学时</p>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="课程名称" min-width="300">
                <template>课程名称课程名称课程名称课程名称</template>
              </el-table-column>
              <el-table-column label="所属分类" min-width="240">
                <template>一级分类>二级分类>三级分类</template>
              </el-table-column>
              <el-table-column label="学时" min-width="140" align="center">
                <template>20</template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">查看</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
        </el-drawer>
        <!--重算学员名单详情-->
        <el-button @click="dialog14 = true" type="primary" class="f-mr20 f-mb20">重算学员名单详情</el-button>
        <el-drawer
          title="重算学员名单详情"
          :visible.sync="dialog14"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="姓名" min-width="100">
                <template>林晓</template>
              </el-table-column>
              <el-table-column label="登录帐号" min-width="240">
                <template>350128199909096129</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[10, 20, 30, 40]"
              :page-size="10"
              layout="total, sizes, prev, pager, next, jumper"
              :total="20"
            >
            </el-pagination>
          </div>
        </el-drawer>

        <!--必选管理-->
        <el-button @click="dialog15 = true" type="primary" class="f-mr20 f-mb20">必选管理</el-button>
        <el-drawer
          title="必选管理"
          :visible.sync="dialog15"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              提示：必选的心得学员需优先完成。
            </el-alert>
            <div class="m-tit">
              <span class="tit-txt">待选心得</span>
            </div>
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="10">
                  <el-form-item label="学习心得主题">
                    <el-input v-model="input" clearable placeholder="请输入学习心得主题名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="主题" min-width="220">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称
                  </div>
                  <div v-else>主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称</div>
                </template>
              </el-table-column>
              <el-table-column label="参加时间" min-width="160" align="center">
                <template>2023-10-23 00:00:00至<br />2023-11-11 23:59:59</template>
              </el-table-column>
              <el-table-column label="学习心得类型" min-width="120" align="center">
                <template>班级心得</template>
              </el-table-column>
              <el-table-column label="作答形式" min-width="80" align="center">
                <template>提交附件</template>
              </el-table-column>
              <el-table-column label="审核方式" width="100" align="center" fixed="right">
                <template>提交自动通过</template>
              </el-table-column>
              <el-table-column label="总分" width="80" align="center" fixed="right">
                <template>100</template>
              </el-table-column>
              <el-table-column label="未通过作答次数" width="140" align="center" fixed="right">
                <template>不限次</template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">选择</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-bd">
            <div class="m-tit">
              <span class="tit-txt">已选的必选心得</span>
              <span class="f-ml20">共<span class="f-ci">Y</span>个</span>
            </div>
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="10">
                  <el-form-item label="学习心得主题">
                    <el-input v-model="input" clearable placeholder="请输入学习心得主题名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="主题" min-width="220">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称
                  </div>
                  <div v-else>主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称主题名称</div>
                </template>
              </el-table-column>
              <el-table-column label="参加时间" min-width="160" align="center">
                <template>2023-10-23 00:00:00至<br />2023-11-11 23:59:59</template>
              </el-table-column>
              <el-table-column label="学习心得类型" min-width="120" align="center">
                <template>班级心得</template>
              </el-table-column>
              <el-table-column label="作答形式" min-width="80" align="center">
                <template>提交附件</template>
              </el-table-column>
              <el-table-column label="审核方式" width="100" align="center" fixed="right">
                <template>提交自动通过</template>
              </el-table-column>
              <el-table-column label="总分" width="80" align="center" fixed="right">
                <template>100</template>
              </el-table-column>
              <el-table-column label="未通过作答次数" width="140" align="center" fixed="right">
                <template>不限次</template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">选择</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>

        <!--新增学习心得-->
        <el-button @click="dialog16 = true" type="primary" class="f-mr20 f-mb20">新增学习心得</el-button>
        <el-drawer
          title="新增学习心得"
          :visible.sync="dialog16"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="学习心得类型：" required>
                <el-cascader clearable :options="cascader" placeholder="班级心得" />
              </el-form-item>
              <el-form-item label="指定课程：" required>
                <el-button type="primary">添加课程</el-button>
              </el-form-item>
              <!--表格-->
              <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10 f-mb20">
                <el-table-column label="课程名称" min-width="300">
                  <template>课程名称课程名称课程名称课程名称</template>
                </el-table-column>
                <el-table-column label="学时" min-width="120" align="center">
                  <template>10</template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template>
                    <el-button type="text" size="mini">移除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-form-item label="主题：" required>
                <el-input v-model="form.name" placeholder="" />
              </el-form-item>

              <el-form-item label="内容：" required>
                <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="" />
              </el-form-item>
              <el-form-item label="参加时间：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="同培训班的学习起止时间"></el-radio>
                  <el-radio label="指定考试时间"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="选择时间：" required>
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  class="form-l"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="作答形式：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio>
                    提交附件
                    <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                      <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                      <div slot="content">
                        文件格式默认支持.doc .docx .pdf .ppt .pptx
                        .jpg等，不可上传视频的格式，且附件类型暂不支持批量导出记录。
                      </div>
                    </el-tooltip>
                  </el-radio>
                  <el-radio>
                    在线编辑
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="提交要求：" required>
                文件大小不超过 <el-input v-model="form.name" size="small" class="input-num f-mr5" /> M，提交内容至少
                <el-input v-model="form.name" size="small" class="input-num f-mr5" /> 字
              </el-form-item>
              <el-form-item label="审核方式：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio>
                    提交自动通过
                    <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                      <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                      <div slot="content">
                        提交自动通过：学员提交内容后无需人工审核，<span class="f-cb">默认学员分值100分</span>
                      </div>
                    </el-tooltip>
                  </el-radio>
                  <el-radio>
                    人工审核
                    <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                      <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                      <div slot="content">
                        学员提交内容后会在活动管理的页面显示，审核状态为待审核，需人工审核后变更审核状态。
                      </div>
                    </el-tooltip>
                  </el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="提交次数：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio>不限次</el-radio>
                  <el-radio>限制提交 <el-input v-model="form.name" size="small" class="input-num f-mr5" /> 次</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item label="心得总分：">100分</el-form-item>
              <el-form-item class="m-btn-bar is-sticky">
                <el-button>取消</el-button>
                <el-button type="primary">确定</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>

        <!--学习心得-详情-->
        <el-button @click="dialog17 = true" type="primary" class="f-mr20 f-mb20">学习心得-详情</el-button>
        <el-drawer
          title="学习心得详情"
          :visible.sync="dialog17"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="用户姓名：">姓名（352203198812290022）</el-form-item>
              <el-form-item label="培训方案名称：">方案名称方案名称方案名称方案名称方案名称</el-form-item>
              <el-form-item label="学习心得类型：">班级心得班级心得班级心得</el-form-item>
              <el-form-item label="主题：">主题名称</el-form-item>
              <el-form-item label="内容：">--</el-form-item>
              <el-form-item label="参加时间：">--</el-form-item>
              <el-form-item label="作答形式：">--</el-form-item>
              <el-form-item label="审核方式：">--</el-form-item>
              <el-form-item label="学习心得："
                >--
                <el-button type="text" class="f-ml10">查看</el-button>
                <el-button type="text" class="f-ml10">下载</el-button>
              </el-form-item>
              <el-form-item label="提交时间：">--</el-form-item>
              <el-form-item label="审核状态：">--</el-form-item>
              <el-form-item label="审核结果：">--</el-form-item>
              <el-form-item label="审核时间：">--</el-form-item>
              <el-form-item label="审核意见：">--</el-form-item>

              <el-form-item class="m-btn-bar is-sticky">
                <el-button>关闭</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>

        <!--审核学习心得-->
        <el-button @click="dialog18 = true" type="primary" class="f-mr20 f-mb20">审核学习心得</el-button>
        <el-drawer
          title="审核学习心得"
          :visible.sync="dialog18"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="用户姓名：">姓名（352203198812290022）</el-form-item>
              <el-form-item label="培训方案名称：">方案名称方案名称方案名称方案名称方案名称</el-form-item>
              <el-form-item label="学习心得类型：">班级心得班级心得班级心得</el-form-item>
              <el-form-item label="主题：">主题名称</el-form-item>
              <el-form-item label="内容：">--</el-form-item>
              <el-form-item label="参加时间：">--</el-form-item>
              <el-form-item label="作答形式：">--</el-form-item>
              <el-form-item label="审核方式：">--</el-form-item>
              <el-form-item label="学习心得："
                >--
                <el-button type="text" class="f-ml10">查看</el-button>
                <el-button type="text" class="f-ml10">下载</el-button>
              </el-form-item>
              <el-form-item label="提交时间：">--</el-form-item>
              <el-form-item label="审核状态：">--</el-form-item>
              <el-form-item label="审核结果："
                ><el-input v-model="form.name" size="small" class="input-num f-mr5" /> 分（总分：100分，通过分：
                <i class="f-cr">60</i> 分）</el-form-item
              >
              <el-form-item label="审核意见：">
                <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="不合格时需填写审核意见" />
              </el-form-item>

              <el-form-item class="m-btn-bar is-sticky">
                <el-button>关闭</el-button>
                <el-button type="primary">确定</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>

        <!--移除后配置的心得数不足考核要求，需要重新添加心得或减少考核数，是否确认移除？-->
        <el-button @click="dialog19 = true" type="primary" class="f-mr20 f-mb20">移除</el-button>
        <el-dialog title="提示" :visible.sync="dialog19" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">移除后配置的心得数不足考核要求，需要重新添加心得或减少考核数，是否确认移除？</span>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确认移除</el-button>
          </div>
        </el-dialog>

        <!--当前学员已完成学习心得考核，是否继续审核当前心得？若选择不审核，则打开下一条心得记录。-->
        <el-button @click="dialog20 = true" type="primary" class="f-mr20 f-mb20">当前学员已完成学习心得考核</el-button>
        <el-dialog title="提示" :visible.sync="dialog20" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt"
              >当前学员已完成学习心得考核，是否继续审核当前心得？若选择不审核，则打开下一条心得记录。</span
            >
          </div>
          <div slot="footer">
            <el-button>不审核</el-button>
            <el-button type="primary">继续审核</el-button>
          </div>
        </el-dialog>

        <!--当前操作将清空-->
        <el-button @click="dialog21 = true" type="primary" class="f-mr20 f-mb20">当前操作将清空期别</el-button>
        <el-dialog title="提示" :visible.sync="dialog21" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">当前操作将清空培训期别配置信息，包括期别引用的问卷，是否确定继续操作？</span>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">继续操作</el-button>
          </div>
        </el-dialog>

        <!--当前操作将清空-->
        <el-button @click="dialog22 = true" type="primary" class="f-mr20 f-mb20">当前操作将清空网授</el-button>
        <el-dialog title="提示" :visible.sync="dialog22" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">当前操作将清空网授部分学习方式配置信息，是否确定继续操作？</span>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">继续操作</el-button>
          </div>
        </el-dialog>

        <!--删除期别-->
        <el-button @click="dialog23 = true" type="primary" class="f-mr20 f-mb20">删除期别确认</el-button>
        <el-dialog title="提示" :visible.sync="dialog23" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">删除后已配置信息不可恢复，是否确认删除？</span>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">调研问卷</span>
        </div>
        <!--删除提示-->
        <el-button @click="dialog001 = true" type="primary" class="f-mr20 f-mb20">删除提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog001" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">是否确认删除该问卷？</span>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
        <!--添加调研问卷：第1步选择问卷模板-->
        <el-button @click="dialog002 = true" type="primary" class="f-mr20 f-mb20"
          >添加调研问卷第1步选择问卷模板</el-button
        >
        <el-drawer
          title="选择调研问卷模板"
          :visible.sync="dialog002"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="10">
                  <el-form-item label="问卷名称">
                    <el-input v-model="input" clearable placeholder="请输入问卷名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="问卷类型">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="问卷名称" min-width="300">
                <template>问卷名称问卷名称问卷名称问卷名称</template>
              </el-table-column>
              <el-table-column label="问卷类型" min-width="120">
                <template>量表问卷</template>
              </el-table-column>
              <el-table-column label="查看" min-width="120" align="center">
                <template><el-link type="primary" :underline="false">预览</el-link></template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center" fixed="right">
                <template>
                  <el-checkbox>选择</el-checkbox>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--添加调研问卷：第2步配置信息-->
        <el-button @click="dialog003 = true" type="primary" class="f-mr20 f-mb20">添加调研问卷第2步配置信息</el-button>
        <el-drawer
          title="添加调研问卷"
          :visible.sync="dialog003"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="问卷模板：" required>
                选择的问卷模板名称
                <el-button type="text" class="f-ml10">预览</el-button>
                <el-button type="text" class="f-ml10">替换</el-button>
              </el-form-item>
              <el-form-item label="问卷名称：" required>
                <el-input v-model="form.name" placeholder="请输入问卷名称" />
              </el-form-item>
              <el-form-item label="问卷开放时间：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="同培训班的学习起止时间"></el-radio>
                  <el-radio label="指定的开放时间"></el-radio>
                </el-radio-group>
              </el-form-item>
              <!--选择 指定考试时间 后出现-->
              <el-form-item label="选择时间：" required>
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  class="form-l"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="是否开放结果：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="开放"></el-radio>
                  <el-radio label="不开放"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="问卷答题规则：" required>
                一个帐号只允许问卷一次，不可重复问卷
              </el-form-item>
              <el-form-item label="应用范围：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="培训方案"></el-radio>
                  <el-radio label="线上课程"></el-radio>
                  <el-radio label="培训期别"></el-radio>
                  <el-radio label="指定培训期别"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="选择期别：" required>
                <el-select placeholder="请选择"></el-select>
                <el-alert title="请先配置期别" type="error" :closable="false" style="padding: 0;" class="f-mt5">
                </el-alert>
              </el-form-item>

              <el-form-item label="前置条件：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio>完成线上课程考核才可作答</el-radio>
                  <el-radio>无</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">问卷要求</span>
            </div>
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="是否纳入考核：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="是"></el-radio>
                  <el-radio label="否"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="是否强制问卷：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="是"></el-radio>
                  <el-radio label="否"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="触发问卷环节：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="进入考试前"></el-radio>
                  <el-radio label="打印证明前"></el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
        <!--编辑调研问卷-->
        <el-button @click="dialog004 = true" type="primary" class="f-mr20 f-mb20">编辑问卷(方案未发布)</el-button>
        <el-drawer
          title="编辑调研问卷"
          :visible.sync="dialog004"
          :direction="direction"
          size="800px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="问卷模板：" required>
                选择的问卷模板名称
                <el-button type="text" class="f-ml10">预览</el-button>
              </el-form-item>
              <el-form-item label="问卷名称：" required>
                <el-input v-model="form.name" placeholder="请输入问卷名称" />
              </el-form-item>
              <el-form-item label="问卷开放时间：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="同培训班的学习起止时间"></el-radio>
                  <el-radio label="指定的开放时间"></el-radio>
                </el-radio-group>
              </el-form-item>
              <!--选择 指定考试时间 后出现-->
              <el-form-item label="选择时间：" required>
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  class="form-l"
                >
                </el-date-picker>
              </el-form-item>
              <el-form-item label="是否开放结果：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="开放"></el-radio>
                  <el-radio label="不开放"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="问卷答题规则：" required>
                一个帐号只允许问卷一次，不可重复问卷
              </el-form-item>
              <el-form-item label="应用范围：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="培训方案"></el-radio>
                  <el-radio label="线上课程"></el-radio>
                  <el-radio label="培训期别"></el-radio>
                  <el-radio label="指定培训期别"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="选择期别：" required>
                <el-select placeholder="请选择"></el-select>
                <el-alert title="请先配置期别" type="error" :closable="false" style="padding: 0;" class="f-mt5">
                </el-alert>
              </el-form-item>

              <el-form-item label="前置条件：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio>完成线上课程考核才可作答</el-radio>
                  <el-radio>无</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
            <div class="m-tit is-border-bottom bg-gray">
              <span class="tit-txt">问卷要求</span>
            </div>
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="是否纳入考核：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="是"></el-radio>
                  <el-radio label="否"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="是否强制问卷：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="是"></el-radio>
                  <el-radio label="否"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="触发问卷环节：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="进入考试前"></el-radio>
                  <el-radio label="打印证明前"></el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">培训期别</span>
        </div>
        <!--删除提示-->
        <el-button @click="dialog0001 = true" type="primary" class="f-mr20 f-mb20">删除期别</el-button>
        <el-dialog title="提示" :visible.sync="dialog0001" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">删除后已配置信息不可恢复，是否确认删除？</span>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
        <!--添加期别-->
        <el-button @click="dialog0002 = true" type="primary" class="f-mr20 f-mb20">添加期别</el-button>
        <el-drawer
          title="添加期别"
          :visible.sync="dialog0002"
          :direction="direction"
          size="960px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-side-positioner">
              <div class="item">
                <div class="dot"></div>
                <div class="tit">基础信息</div>
              </div>
              <div class="item z-cur">
                <div class="dot"></div>
                <div class="tit">报名信息</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别要求</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">联络信息</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">学员须知</div>
              </div>
            </div>
            <div style="padding-right: 160px;">
              <el-row :gutter="16">
                <el-form ref="form" :model="form" label-width="205px" class="m-form f-mt10">
                  <el-col span="19">
                    <el-form-item label="期别名称：" required>
                      <el-input v-model="form.name" placeholder="请输入期别" />
                    </el-form-item>
                    <el-form-item label="期别编号：" required>
                      <el-input v-model="form.name" placeholder="请输入期别编号" />
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <div class="m-add-period">
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">基础信息</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-p20">
                    <el-form-item label="培训报到时段：" required>
                      <el-date-picker
                        v-model="form.date1"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        class="form-l"
                      >
                      </el-date-picker>
                    </el-form-item>
                    <el-form-item label="开放报名人数：" required>
                      <el-input v-model="form.name" size="small" placeholder="请输入" class="input-num f-mr5" /> 人
                    </el-form-item>
                    <el-form-item label="已报名人数：" required>
                      <el-radio-group v-model="form.resource">
                        <el-radio label="读取实际报名人数"></el-radio>
                        <el-radio label=""
                          >固定显示数值
                          <el-input v-model="form.name" size="small" placeholder="填整数" class="input-num f-mr5" />
                          人</el-radio
                        >
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="培训时段：" required>
                      <el-radio-group v-model="form.resource">
                        <el-radio label="创建课表根据课表读取"></el-radio>
                        <el-radio label="不创建课表自定义培训时段"></el-radio>
                      </el-radio-group>
                      <div class="f-mt5">
                        请选择
                        <el-date-picker
                          v-model="form.date1"
                          type="datetimerange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          class="form-l"
                        >
                        </el-date-picker>
                      </div>
                    </el-form-item>
                    <el-form-item label="培训地点：" required>
                      <el-select placeholder="请选择"></el-select>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">报名信息</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-p20">
                    <el-form-item label="展示在门户：" required>
                      <el-radio-group v-model="form.resource">
                        <el-radio>
                          展示
                          <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-info m-tooltip-icon f-c9"></i>
                            <div slot="content">当勾选此项时，培训期别在学员门户上进行展示</div>
                          </el-tooltip>
                        </el-radio>
                        <el-radio>不展示门户</el-radio>
                      </el-radio-group>
                      <div class="bg-gray f-mt10 f-plr15 f-pt5 f-pb5">
                        <div class="f-flex">
                          <span><i class="f-ci f-mr5">*</i>展示用户：</span>
                          <el-checkbox-group v-model="form.type">
                            <el-checkbox>学员门户可见</el-checkbox>
                            <el-checkbox>集体报名管理员可见</el-checkbox>
                          </el-checkbox-group>
                        </div>
                      </div>
                    </el-form-item>
                    <el-form-item label="开放学员报名：">
                      <el-radio-group v-model="form.resource">
                        <el-checkbox>
                          不开放
                          <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-info m-tooltip-icon f-c9"></i>
                            <div slot="content">
                              勾选时，不显示开启报名时间、关闭报名时间配置项<br />未勾选时，下方显示开启报名时间、关闭报名时间配置项
                            </div>
                          </el-tooltip>
                        </el-checkbox>
                      </el-radio-group>
                      <div class="bg-gray f-mt10 f-plr15 f-pt5 f-pb5">
                        <div class="f-flex f-mb10">
                          <span><i class="f-ci f-mr5">*</i>开启报名时间：</span>
                          <el-radio-group v-model="form.type" style="padding-top: 8px;">
                            <div>
                              <el-radio label="立即开启"></el-radio>
                            </div>
                            <div>
                              <el-radio label=""
                                >指定开启时间
                                <el-date-picker v-model="form.date1" type="date" placeholder="选择开启时间">
                                </el-date-picker
                              ></el-radio>
                            </div>
                          </el-radio-group>
                        </div>
                        <div class="f-flex f-mb10">
                          <span><i class="f-ci f-mr5">*</i>关闭报名时间：</span>
                          <el-radio-group v-model="form.type" style="padding-top: 8px;">
                            <div>
                              <el-radio label="无关闭时间"></el-radio>
                            </div>
                            <div>
                              <el-radio label=""
                                >指定关闭时间
                                <el-date-picker v-model="form.date1" type="date" placeholder="选择关闭时间">
                                </el-date-picker
                              ></el-radio>
                            </div>
                          </el-radio-group>
                        </div>
                      </div>
                    </el-form-item>
                    <el-form-item label="是否开启住宿信息采集：" required>
                      <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                    </el-form-item>
                    <el-form-item label="住宿信息采集须知：">
                      <el-input type="textarea" :rows="5" placeholder="请输入住宿信息采集须知内容..."></el-input>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">期别要求</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-p20">
                    <el-form-item label="是否开启报到：" required>
                      <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                    </el-form-item>
                    <el-form-item label="是否开启结业测试：" required>
                      <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                    </el-form-item>
                    <el-form-item label="是否开启考勤：" required>
                      <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                    </el-form-item>
                    <el-form-item label="考勤考核要求：" required>
                      考勤率不低于
                      <el-input v-model="form.name" size="small" placeholder="请输入" class="input-num f-mr5" /> %
                      <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-info m-tooltip-icon f-c9"></i>
                        <div slot="content">
                          考勤说明：<br />
                          考勤率=有效签到签退次数/应签到签退总次数，同一签到/签退时段产生多次签到"签退记录仅记为一次。<br />
                          例如：根据机构填报的课表计算出应签到签退总次数为12次，若考勒率不低于75%，那么学员有效签到签退次数达到9次即考勒合格。<br />
                          注：当根据考勤率计算学员至少应签到/签退次数时，考勤率*应签到/签退次数计算为小数时，按进位取整。<br />
                          例如：85%*13=11.05，此时，学员至少应签到/签退次数为12次
                        </div>
                      </el-tooltip>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">联络信息</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-p20">
                    <el-form-item label="班主任：">
                      <el-input v-model="form.name" placeholder="请输入班主任姓名" />
                    </el-form-item>
                    <el-form-item label="班主任联系电话：">
                      <el-input v-model="form.name" placeholder="请输入班主任联系电话" />
                    </el-form-item>
                    <el-form-item label="酒店联系人：">
                      <el-input v-model="form.name" placeholder="请输入此期别酒店联系人姓名" />
                    </el-form-item>
                    <el-form-item label="酒店联系人电话：">
                      <el-input v-model="form.name" placeholder="请输入酒店联系人电话" />
                    </el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">学员须知</span>
                  </div>
                  <div class="f-p20">
                    <el-input type="textarea" :rows="6" v-model="form.name" placeholder="请输入学员须知"> </el-input>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>返回</el-button>
            <el-button type="primary">确定并保存</el-button>
            <el-button type="primary" plain>保存并创建课程</el-button>
            <el-button type="primary" plain>保存并编辑课程</el-button>
          </div>
        </el-drawer>
        <!--添加课程-->
        <el-button @click="dialog0003 = true" type="primary" class="f-mr20 f-mb20">添加课程</el-button>
        <el-drawer
          title="添加课程"
          :visible.sync="dialog0003"
          :direction="direction"
          size="90%"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="f-mb10"><el-button type="primary">批量创建课程</el-button></div>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="" min-width="410">
                <template slot="header"> <i class="f-cr">*</i>课程名称 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    课程名称课程名称课程名称课程名称课程名称课程名称
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入课程名称" style="width: 390px;"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="180" align="center">
                <template slot="header"> <i class="f-cr">*</i>授课教师 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    授课教师姓名
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入教师姓名"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="教师职称" min-width="180" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    教师职称
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入教师职称"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="教师所在单位" min-width="340" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    教师所在单位
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入教师所在单位" style="width: 320px;"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="180" align="center">
                <template slot="header"> <i class="f-cr">*</i>课程学时 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    课程学时
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入课程学时"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="250" align="center">
                <template slot="header"> <i class="f-cr">*</i>授课日期 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    2024-10-21
                  </div>
                  <div v-else>
                    <el-date-picker type="date" placeholder="选择日期"> </el-date-picker>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="250" align="center">
                <template slot="header"> <i class="f-cr">*</i>授课开始时间 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    08:30
                  </div>
                  <div v-else>
                    <el-date-picker type="date" placeholder="选择时间"> </el-date-picker>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="250" align="center">
                <template slot="header"> <i class="f-cr">*</i>授课结束时间 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    11:00
                  </div>
                  <div v-else>
                    <el-date-picker type="date" placeholder="选择时间"> </el-date-picker>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="课程时长(分)" min-width="180" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    150
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入课程时长"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-button type="text" icon="el-icon-remove-outline">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="m-add-row f-mt10"><i class="el-icon-circle-plus-outline f-mr5"></i>新增一行</div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">保存</el-button>
          </div>
        </el-drawer>
        <!--编辑课程-->
        <el-button @click="dialog00031 = true" type="primary" class="f-mr20 f-mb20">编辑课程</el-button>
        <el-drawer
          title="编辑课程"
          :visible.sync="dialog00031"
          :direction="direction"
          size="90%"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：课程开始上课后或课程已结束，不允许修改课程授课时间
            </el-alert>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="" min-width="410">
                <template slot="header"> <i class="f-cr">*</i>课程名称 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    课程名称课程名称课程名称课程名称课程名称课程名称
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入课程名称" style="width: 390px;"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="180" align="center">
                <template slot="header"> <i class="f-cr">*</i>授课教师 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    授课教师姓名
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入教师姓名"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="教师职称" min-width="180" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    教师职称
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入教师职称"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="教师所在单位" min-width="340" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    教师所在单位
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入教师所在单位" style="width: 320px;"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="180" align="center">
                <template slot="header"> <i class="f-cr">*</i>课程学时 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    课程学时
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入课程学时"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="250" align="center">
                <template slot="header"> <i class="f-cr">*</i>授课日期 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    2024-10-21
                  </div>
                  <div v-else>
                    <el-date-picker type="date" placeholder="选择日期"> </el-date-picker>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="250" align="center">
                <template slot="header"> <i class="f-cr">*</i>授课开始时间 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    08:30
                  </div>
                  <div v-else>
                    <el-date-picker type="date" placeholder="选择时间"> </el-date-picker>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="" min-width="250" align="center">
                <template slot="header"> <i class="f-cr">*</i>授课结束时间 </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    11:00
                  </div>
                  <div v-else>
                    <el-date-picker type="date" placeholder="选择时间"> </el-date-picker>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="课程时长(分)" min-width="180" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    150
                  </div>
                  <div v-else>
                    <el-input placeholder="请输入课程时长"></el-input>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-button type="text" icon="el-icon-remove-outline">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div class="m-add-row f-mt10"><i class="el-icon-circle-plus-outline f-mr5"></i>新增一行</div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">保存</el-button>
          </div>
        </el-drawer>
        <!--查看期别报名信息-->
        <el-button @click="dialog0004 = true" type="primary" class="f-mr20 f-mb20">查看期别报名信息</el-button>
        <el-drawer
          title="期别报名信息"
          :visible.sync="dialog0004"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-side-positioner">
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别01</div>
              </div>
              <div class="item z-cur">
                <div class="dot"></div>
                <div class="tit">期别02</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别03</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别04</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别05</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别06</div>
              </div>
            </div>
            <div style="padding-right: 160px;">
              <div class="m-tit is-border-bottom bg-gray">
                <span class="tit-txt">这里显示期别名称（这里显示期别编号）</span>
              </div>
              <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                <el-form-item label="展示在门户：">
                  展示门户
                </el-form-item>
                <el-form-item label="开放学员报名时间：">
                  2024-10-19 8:00:00至2024-10-19 8:00:00
                </el-form-item>
              </el-form>
              <div class="m-tit is-border-bottom bg-gray">
                <span class="tit-txt">这里显示期别名称（这里显示期别编号）</span>
              </div>
              <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                <el-form-item label="展示在门户：">
                  展示门户
                </el-form-item>
                <el-form-item label="开放学员报名时间：">
                  2024-10-19 8:00:00至2024-10-19 8:00:00
                </el-form-item>
              </el-form>
              <div class="m-tit is-border-bottom bg-gray">
                <span class="tit-txt">这里显示期别名称（这里显示期别编号）</span>
              </div>
              <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                <el-form-item label="展示在门户：">
                  展示门户
                </el-form-item>
                <el-form-item label="开放学员报名时间：">
                  2024-10-19 8:00:00至2024-10-19 8:00:00
                </el-form-item>
              </el-form>
              <div class="m-tit is-border-bottom bg-gray">
                <span class="tit-txt">这里显示期别名称（这里显示期别编号）</span>
              </div>
              <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                <el-form-item label="展示在门户：">
                  展示门户
                </el-form-item>
                <el-form-item label="开放学员报名时间：">
                  2024-10-19 8:00:00至2024-10-19 8:00:00
                </el-form-item>
              </el-form>
              <div class="m-tit is-border-bottom bg-gray">
                <span class="tit-txt">这里显示期别名称（这里显示期别编号）</span>
              </div>
              <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                <el-form-item label="展示在门户：">
                  展示门户
                </el-form-item>
                <el-form-item label="开放学员报名时间：">
                  2024-10-19 8:00:00至2024-10-19 8:00:00
                </el-form-item>
              </el-form>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">返回</el-button>
          </div>
        </el-drawer>
        <!--复制期别-->
        <el-button @click="dialog0005 = true" type="primary" class="f-mr20 f-mb20">复制期别</el-button>
        <el-drawer
          title="添加期别"
          :visible.sync="dialog0005"
          :direction="direction"
          size="960px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-side-positioner">
              <div class="item">
                <div class="dot"></div>
                <div class="tit">基础信息</div>
              </div>
              <div class="item z-cur">
                <div class="dot"></div>
                <div class="tit">报名信息</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别要求</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">联络信息</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">学员须知</div>
              </div>
            </div>
            <div style="padding-right: 160px;">
              <el-row :gutter="16">
                <el-form ref="form" :model="form" label-width="205px" class="m-form f-mt10">
                  <el-col span="19">
                    <el-form-item label="期别名称：" required>
                      <el-input v-model="form.name" placeholder="请输入期别" />
                    </el-form-item>
                    <el-form-item label="期别编号：" required>
                      <el-input v-model="form.name" placeholder="请输入期别编号" />
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <div class="m-add-period">
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">基础信息</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-p20">
                    <el-form-item label="培训报到时段：" required>
                      <el-date-picker
                        v-model="form.date1"
                        type="datetimerange"
                        range-separator="至"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        class="form-l"
                      >
                      </el-date-picker>
                    </el-form-item>
                    <el-form-item label="开放报名人数：" required>
                      <el-input v-model="form.name" size="small" placeholder="请输入" class="input-num f-mr5" /> 人
                    </el-form-item>
                    <el-form-item label="已报名人数：" required>
                      <el-radio-group v-model="form.resource">
                        <el-radio label="读取实际报名人数"></el-radio>
                        <el-radio label=""
                          >固定显示数值
                          <el-input v-model="form.name" size="small" placeholder="填整数" class="input-num f-mr5" />
                          人</el-radio
                        >
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item label="培训时段：" required>
                      <el-radio-group v-model="form.resource">
                        <el-radio label="创建课表根据课表读取"></el-radio>
                        <el-radio label="不创建课表自定义培训时段"></el-radio>
                      </el-radio-group>
                      <div class="f-mt5">
                        请选择
                        <el-date-picker
                          v-model="form.date1"
                          type="datetimerange"
                          range-separator="至"
                          start-placeholder="开始日期"
                          end-placeholder="结束日期"
                          class="form-l"
                        >
                        </el-date-picker>
                      </div>
                    </el-form-item>
                    <el-form-item label="培训地点：" required>
                      <el-select placeholder="请选择"></el-select>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">报名信息</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-p20">
                    <el-form-item label="展示在门户：" required>
                      <el-radio-group v-model="form.resource">
                        <el-radio>
                          展示
                          <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-info m-tooltip-icon f-c9"></i>
                            <div slot="content">当勾选此项时，培训期别在学员门户上进行展示</div>
                          </el-tooltip>
                        </el-radio>
                        <el-radio>不展示门户</el-radio>
                      </el-radio-group>
                      <div class="bg-gray f-mt10 f-plr15 f-pt5 f-pb5">
                        <div class="f-flex">
                          <span><i class="f-ci f-mr5">*</i>展示用户：</span>
                          <el-checkbox-group v-model="form.type">
                            <el-checkbox>学员门户可见</el-checkbox>
                            <el-checkbox>集体报名管理员可见</el-checkbox>
                          </el-checkbox-group>
                        </div>
                      </div>
                    </el-form-item>
                    <el-form-item label="开放学员报名：">
                      <el-radio-group v-model="form.resource">
                        <el-checkbox>
                          不开放
                          <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-info m-tooltip-icon f-c9"></i>
                            <div slot="content">
                              勾选时，不显示开启报名时间、关闭报名时间配置项<br />未勾选时，下方显示开启报名时间、关闭报名时间配置项
                            </div>
                          </el-tooltip>
                        </el-checkbox>
                      </el-radio-group>
                      <div class="bg-gray f-mt10 f-plr15 f-pt5 f-pb5">
                        <div class="f-flex f-mb10">
                          <span><i class="f-ci f-mr5">*</i>开启报名时间：</span>
                          <el-radio-group v-model="form.type" style="padding-top: 8px;">
                            <div>
                              <el-radio label="立即开启"></el-radio>
                            </div>
                            <div>
                              <el-radio label=""
                                >指定开启时间
                                <el-date-picker v-model="form.date1" type="date" placeholder="选择开启时间">
                                </el-date-picker
                              ></el-radio>
                            </div>
                          </el-radio-group>
                        </div>
                        <div class="f-flex f-mb10">
                          <span><i class="f-ci f-mr5">*</i>关闭报名时间：</span>
                          <el-radio-group v-model="form.type" style="padding-top: 8px;">
                            <div>
                              <el-radio label="无关闭时间"></el-radio>
                            </div>
                            <div>
                              <el-radio label=""
                                >指定关闭时间
                                <el-date-picker v-model="form.date1" type="date" placeholder="选择关闭时间">
                                </el-date-picker
                              ></el-radio>
                            </div>
                          </el-radio-group>
                        </div>
                      </div>
                    </el-form-item>
                    <el-form-item label="是否开启住宿信息采集：" required>
                      <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                    </el-form-item>
                    <el-form-item label="住宿信息采集须知：">
                      <el-input type="textarea" :rows="5" placeholder="请输入住宿信息采集须知内容..."></el-input>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">期别要求</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-p20">
                    <el-form-item label="是否开启报到：" required>
                      <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                    </el-form-item>
                    <el-form-item label="是否开启结业测试：" required>
                      <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                    </el-form-item>
                    <el-form-item label="是否开启考勤：" required>
                      <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                    </el-form-item>
                    <el-form-item label="考勤考核要求：" required>
                      考勤率不低于
                      <el-input v-model="form.name" size="small" placeholder="请输入" class="input-num f-mr5" /> %
                      <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-info m-tooltip-icon f-c9"></i>
                        <div slot="content">
                          考勤说明：<br />
                          考勤率=有效签到签退次数/应签到签退总次数，同一签到/签退时段产生多次签到"签退记录仅记为一次。<br />
                          例如：根据机构填报的课表计算出应签到签退总次数为12次，若考勒率不低于75%，那么学员有效签到签退次数达到9次即考勒合格。<br />
                          注：当根据考勤率计算学员至少应签到/签退次数时，考勤率*应签到/签退次数计算为小数时，按进位取整。<br />
                          例如：85%*13=11.05，此时，学员至少应签到/签退次数为12次
                        </div>
                      </el-tooltip>
                    </el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">联络信息</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-p20">
                    <el-form-item label="班主任：">
                      <el-input v-model="form.name" placeholder="请输入班主任姓名" />
                    </el-form-item>
                    <el-form-item label="班主任联系电话：">
                      <el-input v-model="form.name" placeholder="请输入班主任联系电话" />
                    </el-form-item>
                    <el-form-item label="酒店联系人：">
                      <el-input v-model="form.name" placeholder="请输入此期别酒店联系人姓名" />
                    </el-form-item>
                    <el-form-item label="酒店联系人电话：">
                      <el-input v-model="form.name" placeholder="请输入酒店联系人电话" />
                    </el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">学员须知</span>
                  </div>
                  <div class="f-p20">
                    <el-input type="textarea" :rows="6" v-model="form.name" placeholder="请输入学员须知"> </el-input>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>返回</el-button>
            <el-button type="primary">确定并保存</el-button>
            <el-button type="primary" plain>保存并创建课程</el-button>
            <el-button type="primary" plain>保存并编辑课程</el-button>
          </div>
        </el-drawer>
        <!--批量创建课程-->
        <el-button @click="dialog0006 = true" type="primary" class="f-mr20 f-mb20">批量创建课程</el-button>
        <el-drawer
          title="批量创建课程"
          :visible.sync="dialog0006"
          :direction="direction"
          size="1000px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-alert type="warning" :closable="false" class="m-alert f-mb20">
              <p class="f-fb">温馨提示：</p>
              <p>1.导入课程，需选择下载对应课程模板；</p>
              <p>2.填写的信息，请按下载的模板填写要求，严格填写。</p>
            </el-alert>
            <el-row type="flex" justify="center">
              <el-col :sm="15" :lg="15">
                <el-steps direction="vertical" :active="4" class="m-vertical-steps" style="height: auto;">
                  <el-step title="下载批量创建课程模板,填写要求信息">
                    <div slot="description">
                      <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-download">
                        批量创建课程模板
                      </el-button>
                    </div>
                  </el-step>
                  <el-step title="上传填写好的表格">
                    <div slot="description">
                      <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false">
                        <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                          选择文件
                        </el-button>
                      </el-upload>
                    </div>
                  </el-step>
                  <el-step title="导入结果">
                    <div slot="description">
                      <div class="f-c9">未导入</div>
                      <div class="f-cb">导入中<i class="el-icon-loading"></i></div>
                      <div class="f-cr">导入失败：请修改数据后重新导入！</div>
                      <el-link type="primary" :underline="true">查看失败数据.xlsx</el-link>
                    </div>
                  </el-step>
                </el-steps>
              </el-col>
            </el-row>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">导入</el-button>
          </div>
        </el-drawer>
        <!--编辑课程提示-->
        <el-button @click="dialog0007 = true" type="primary" class="f-mr20 f-mb20"
          >编辑课程(当前期别产生报名数据)</el-button
        >
        <el-dialog title="提示" :visible.sync="dialog0007" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <span class="txt">当前时间距离首门课程开始时间不足2小时，不支持修改授课日期以及授课开始/结束时间！</span>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
        <!--导入提示-->
        <el-button @click="dialog0008 = true" type="primary" class="f-mr20 f-mb20">导入提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog0008" width="450px" class="m-dialog">
          <div class="dialog-alert">
            授课时间与培训时段不符，请检查后重新导入！<a href="#" class="f-cb">下载失败数据</a>
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
        <!--导入结果-->
        <el-button @click="dialog0009 = true" type="primary" class="f-mr20 f-mb20">导入结果列表</el-button>
        <el-drawer
          title="批量创建课程结果"
          :visible.sync="dialog0009"
          :direction="direction"
          size="90%"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-tabs v-model="activeName" type="card" @tab-click="handleClick">
              <el-tab-pane label="校验通过" name="first">
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="课程名称" min-width="300">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        课程名称课程名称课程名称课程名称课程名称课程名称
                      </div>
                      <div v-else>
                        课程名称课程名称课程名称课程名称课程名称课程名称
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="授课教师" min-width="180" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        授课教师姓名
                      </div>
                      <div v-else>
                        授课教师姓名
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="教师职称" min-width="180" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        教师职称
                      </div>
                      <div v-else>
                        教师职称
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="教师所在单位" min-width="180" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        教师所在单位
                      </div>
                      <div v-else>
                        教师所在单位
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="课程学时" min-width="180" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        课程学时
                      </div>
                      <div v-else>
                        课程学时
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="授课日期" min-width="250" align="center">
                    <template>
                      xxxx-xx-xx
                    </template>
                  </el-table-column>
                  <el-table-column label="授课开始时间" min-width="250" align="center">
                    <template>
                      xxxx-xx-xx
                    </template>
                  </el-table-column>
                  <el-table-column label="授课结束时间" min-width="250" align="center">
                    <template>
                      xxxx-xx-xx
                    </template>
                  </el-table-column>
                  <el-table-column label="课程时长(分)" min-width="180" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        课程时长
                      </div>
                      <div v-else>
                        课程时长
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="120" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-button type="text">修改</el-button>
                        <el-button type="text">删除</el-button>
                      </div>
                      <div v-else>
                        <el-button type="text">修改</el-button>
                        <el-button type="text">删除</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-tab-pane>
              <el-tab-pane label="校验失败" name="second">
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="课程名称" min-width="300">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        课程名称课程名称课程名称课程名称课程名称课程名称
                      </div>
                      <div v-else>
                        课程名称课程名称课程名称课程名称课程名称课程名称
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="授课教师" min-width="180" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        授课教师姓名
                      </div>
                      <div v-else>
                        授课教师姓名
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="授课日期" min-width="250" align="center">
                    <template>
                      xxxx-xx-xx
                    </template>
                  </el-table-column>
                  <el-table-column label="授课开始时间" min-width="250" align="center">
                    <template>
                      xxxx-xx-xx
                    </template>
                  </el-table-column>
                  <el-table-column label="授课结束时间" min-width="250" align="center">
                    <template>
                      xxxx-xx-xx
                    </template>
                  </el-table-column>
                  <el-table-column label="失败原因" min-width="180" align="center">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        课程授课时间已结束
                      </div>
                      <div v-else>
                        课程授课时间已结束
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="120" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-button type="text">修改</el-button>
                        <el-button type="text">删除</el-button>
                      </div>
                      <div v-else>
                        <el-button type="text">修改</el-button>
                        <el-button type="text">删除</el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </el-tab-pane>
            </el-tabs>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">返回</el-button>
          </div>
        </el-drawer>
        <!--批量创建课程提示-->
        <el-button @click="dialog0010 = true" type="primary" class="f-mr20 f-mb20">批量创建课程提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog0010" width="450px" class="m-dialog">
          <div class="dialog-alert">
            当前列表已存在课程，如批量创建课程将会覆盖列表数据，是否继续操作？
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">继续操作</el-button>
          </div>
        </el-dialog>
        <!--批量创建课程取消提示-->
        <el-button @click="dialog0011 = true" type="primary" class="f-mr20 f-mb20">批量创建课程取消提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog0011" width="450px" class="m-dialog">
          <div class="dialog-alert">
            当前正在操作批量创建课程，是否确定取消操作？
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">培训要求</span>
        </div>
        <!--查看问卷-->
        <el-button @click="dialog00001 = true" type="primary" class="f-mr20 f-mb20">查看问卷</el-button>
        <el-drawer
          title="查看问卷"
          :visible.sync="dialog00001"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="问卷名称" min-width="200">
                <template>读取问卷名称</template>
              </el-table-column>
              <el-table-column label="问卷开放时间" min-width="200" align="center">
                <template>
                  <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
                </template>
              </el-table-column>
              <el-table-column label="问卷状态" min-width="100" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">启用</div>
                  <div v-else>停用</div>
                </template>
              </el-table-column>
              <el-table-column label="应用范围" min-width="120" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">当前培训方案</div>
                  <div v-else>线上课程</div>
                </template>
              </el-table-column>
              <el-table-column label="前置条件" min-width="120" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">无</div>
                  <div v-else>完成线上课程考核</div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">返回</el-button>
          </div>
        </el-drawer>
        <!--查看培训期别考核内容-->
        <el-button @click="dialog00002 = true" type="primary" class="f-mr20 f-mb20">查看培训期别考核内容</el-button>
        <el-drawer
          title="查看培训期别考核内容"
          :visible.sync="dialog00002"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-collapse v-model="activeNames" @change="handleChange" class="m-collapse-custom">
              <el-collapse-item title="10002期别名称（比如这个是指定的期别名称）" name="1">
                <div class="u-bg-light-base f-p10">
                  <p>①考勤考核要求考勤率>75%</p>
                  <p>②需完成结业测试</p>
                  <p>③需完成调研问卷</p>
                </div>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="问卷名称" min-width="200">
                    <template>读取问卷名称</template>
                  </el-table-column>
                  <el-table-column label="问卷开放时间" min-width="200" align="center">
                    <template>
                      <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                      <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="问卷状态" min-width="100" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">启用</div>
                      <div v-else>停用</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="应用范围" min-width="120" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">当前培训方案</div>
                      <div v-else>线上课程</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="前置条件" min-width="120" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">无</div>
                      <div v-else>完成线上课程考核</div>
                    </template>
                  </el-table-column>
                </el-table>
              </el-collapse-item>
              <el-collapse-item title="10002期别名称（比如这个是指定的期别名称）" name="2">
                <div class="u-bg-light-base f-p10">
                  <p>①考勤考核要求考勤率>75%</p>
                  <p>②需完成结业测试</p>
                  <p>③需完成调研问卷</p>
                </div>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="问卷名称" min-width="200">
                    <template>读取问卷名称</template>
                  </el-table-column>
                  <el-table-column label="问卷开放时间" min-width="200" align="center">
                    <template>
                      <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                      <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="问卷状态" min-width="100" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">启用</div>
                      <div v-else>停用</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="应用范围" min-width="120" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">当前培训方案</div>
                      <div v-else>线上课程</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="前置条件" min-width="120" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">无</div>
                      <div v-else>完成线上课程考核</div>
                    </template>
                  </el-table-column>
                </el-table>
              </el-collapse-item>
              <el-collapse-item title="10002期别名称（比如这个是指定的期别名称）" name="3">
                <div class="u-bg-light-base f-p10">
                  <p>①考勤考核要求考勤率>75%</p>
                  <p>②需完成结业测试</p>
                  <p>③需完成调研问卷</p>
                </div>
                <!--表格-->
                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="问卷名称" min-width="200">
                    <template>读取问卷名称</template>
                  </el-table-column>
                  <el-table-column label="问卷开放时间" min-width="200" align="center">
                    <template>
                      <p><el-tag type="info" size="mini">起始</el-tag>2024-10-01 08:08:08</p>
                      <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="问卷状态" min-width="100" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">启用</div>
                      <div v-else>停用</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="应用范围" min-width="120" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">当前培训方案</div>
                      <div v-else>线上课程</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="前置条件" min-width="120" align="center" fixed="right">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">无</div>
                      <div v-else>完成线上课程考核</div>
                    </template>
                  </el-table-column>
                </el-table>
              </el-collapse-item>
            </el-collapse>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">返回</el-button>
          </div>
        </el-drawer>
        <!--选择培训证明-->
        <el-button @click="dialog00003 = true" type="primary" class="f-mr20 f-mb20">选择培训证明</el-button>
        <el-drawer
          title="选择培训证明"
          :visible.sync="dialog00003"
          :direction="direction"
          size="1200px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="模板名称" min-width="240">
                <template>模板名称模板名称模板名称</template>
              </el-table-column>
              <el-table-column label="所属行业" width="180" align="center">
                <template>建设</template>
              </el-table-column>
              <el-table-column label="模板说明" min-width="240">
                <template>模板说明模板说明模板说明模板说明模板说明</template>
              </el-table-column>
              <el-table-column label="创建时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column label="查看" width="80" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">预览</el-button>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-radio v-model="radio" label="1">选择</el-radio>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">功能按钮</span>
        </div>
        <!--当前方案已配置培训期别-->
        <el-button @click="dialog000001 = true" type="primary" class="f-mr20 f-mb20">发布-已配置期别</el-button>
        <el-dialog title="提示" :visible.sync="dialog000001" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">当前方案已配置培训期别，需前往实施管理-训前实施模块进行考核相关的配置！</span>
          </div>
          <div slot="footer">
            <el-button>暂不前往</el-button>
            <el-button type="primary">立即前往</el-button>
          </div>
        </el-dialog>
        <!--选修课学时不等-->
        <el-button @click="dialog000002 = true" type="primary" class="f-mr20 f-mb20">发布-选修课学时不等</el-button>
        <el-dialog title="提示" :visible.sync="dialog000002" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">选修课末级分类下课程总学时与选修课选修要求学时不等，请调整后再发布！</span>
          </div>
          <div slot="footer">
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>

        <!--当前方案已发布-->
        <el-button @click="dialog000003 = true" type="primary" class="f-mr20 f-mb20">发布-当前方案已发布</el-button>
        <el-dialog title="提示" :visible.sync="dialog000003" width="450px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">当前方案已发布，您还需前往【实施管理-训前实施】模块完善培训相关配置！</span>
          </div>
          <div slot="footer">
            <el-button>暂不前往</el-button>
            <el-button type="primary">立即前往</el-button>
          </div>
        </el-dialog>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">培训方案管理</span>
        </div>
        <!--查看期别报名信息-->
        <el-button @click="dialog0000001 = true" type="primary" class="f-mr20 f-mb20">查看期别报名信息</el-button>
        <el-drawer
          title="期别报名信息"
          :visible.sync="dialog0000001"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-side-positioner">
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别01</div>
              </div>
              <div class="item z-cur">
                <div class="dot"></div>
                <div class="tit">期别02</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别03</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别04</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别05</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别06</div>
              </div>
            </div>
            <div style="padding-right: 160px;">
              <div class="m-tit is-border-bottom bg-gray">
                <span class="tit-txt">这里显示期别名称（这里显示期别编号）</span>
              </div>
              <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                <el-form-item label="展示在门户：">
                  展示门户
                </el-form-item>
                <el-form-item label="开放学员报名时间：">
                  2024-10-19 8:00:00至2024-10-19 8:00:00
                </el-form-item>
              </el-form>
              <div class="m-tit is-border-bottom bg-gray">
                <span class="tit-txt">这里显示期别名称（这里显示期别编号）</span>
              </div>
              <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                <el-form-item label="展示在门户：">
                  展示门户
                </el-form-item>
                <el-form-item label="开放学员报名时间：">
                  2024-10-19 8:00:00至2024-10-19 8:00:00
                </el-form-item>
              </el-form>
              <div class="m-tit is-border-bottom bg-gray">
                <span class="tit-txt">这里显示期别名称（这里显示期别编号）</span>
              </div>
              <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                <el-form-item label="展示在门户：">
                  展示门户
                </el-form-item>
                <el-form-item label="开放学员报名时间：">
                  2024-10-19 8:00:00至2024-10-19 8:00:00
                </el-form-item>
              </el-form>
              <div class="m-tit is-border-bottom bg-gray">
                <span class="tit-txt">这里显示期别名称（这里显示期别编号）</span>
              </div>
              <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                <el-form-item label="展示在门户：">
                  展示门户
                </el-form-item>
                <el-form-item label="开放学员报名时间：">
                  2024-10-19 8:00:00至2024-10-19 8:00:00
                </el-form-item>
              </el-form>
              <div class="m-tit is-border-bottom bg-gray">
                <span class="tit-txt">这里显示期别名称（这里显示期别编号）</span>
              </div>
              <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                <el-form-item label="展示在门户：">
                  展示门户
                </el-form-item>
                <el-form-item label="开放学员报名时间：">
                  2024-10-19 8:00:00至2024-10-19 8:00:00
                </el-form-item>
              </el-form>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">返回</el-button>
          </div>
        </el-drawer>
        <!--上架提示-->
        <el-button type="primary" @click="dialog01 = true" class="f-mr20">上架提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog01" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">确定立即上架该培训方案？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确定上架</el-button>
          </div>
        </el-dialog>

        <!--下架提示-->
        <el-button type="primary" @click="dialog02 = true" class="f-mr20">下架提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog02" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">确定立即下架该培训方案？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确定下架</el-button>
          </div>
        </el-dialog>

        <!--复制提示-->
        <el-button type="primary" @click="dialog03 = true" class="f-mr20">复制提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog03" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">确认复制改培训方案？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确定复制</el-button>
          </div>
        </el-dialog>

        <!--导出列表数据-->
        <el-button @click="dialog04 = true" type="primary" class="f-mr20 f-mb20">导出列表数据</el-button>
        <el-drawer title="提示" :visible.sync="dialog04" :direction="direction" size="720px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10 f-mlr20">
              <el-form-item label="请选择导出方式：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="导出方案列表数据"></el-radio>
                  <el-radio label="导出方案期别明细数据"></el-radio>
                </el-radio-group>
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="info">取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>

        <!--导出培训方案成功提示-->
        <el-button type="primary" @click="dialog05 = true" class="f-mr20">导出培训方案成功提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog05" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-success success"></i>
            <span class="txt">导出成功，是否前往下载数据？<br />下载入口：【导出任务查看-培训方案】</span>
          </div>
          <div slot="footer">
            <el-button>暂 不</el-button>
            <el-button type="primary">前往下载</el-button>
          </div>
        </el-dialog>

        <!--导出培训方案期别明细成功提示-->
        <el-button type="primary" @click="dialog06 = true" class="f-mr20">导出培训方案期别明细成功提示</el-button>
        <el-dialog title="提示" :visible.sync="dialog06" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-success success"></i>
            <span class="txt">导出成功，是否前往下载数据？<br />下载入口：【导出任务查看-培训方案期别明细】</span>
          </div>
          <div slot="footer">
            <el-button>暂 不</el-button>
            <el-button type="primary">前往下载</el-button>
          </div>
        </el-dialog>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">培训方案详情</span>
        </div>
        <!--查看期别-->
        <el-button @click="dialog00000001 = true" type="primary" class="f-mr20 f-mb20">查看期别</el-button>
        <el-drawer
          title="查看期别"
          :visible.sync="dialog00000001"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <div class="m-side-positioner">
              <div class="item">
                <div class="dot"></div>
                <div class="tit">基础信息</div>
              </div>
              <div class="item z-cur">
                <div class="dot"></div>
                <div class="tit">报名信息</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">期别要求</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">联络信息</div>
              </div>
              <div class="item">
                <div class="dot"></div>
                <div class="tit">学员须知</div>
              </div>
            </div>
            <div style="padding-right: 160px;">
              <el-row :gutter="16">
                <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                  <el-col span="19">
                    <el-form-item label="期别名称：">
                      读取数据
                    </el-form-item>
                    <el-form-item label="期别编号：">
                      读取数据
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <div class="m-add-period">
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">基础信息</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                    <el-form-item label="培训报到时段：">xxxx-xx-xx至xxxx-xx-xx </el-form-item>
                    <el-form-item label="开放报名人数：">10人</el-form-item>
                    <el-form-item label="已报名人数：">10人 </el-form-item>
                    <el-form-item label="培训时段：">xxxx-xx-xx至xxxx-xx-xx</el-form-item>
                    <el-form-item label="培训地点：">培训地点</el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">报名信息</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                    <el-form-item label="展示在门户：">展示在学员门户</el-form-item>
                    <el-form-item label="展示用户：">学员门户可见</el-form-item>
                    <el-form-item label="开放学员报名：">不开放</el-form-item>
                    <el-form-item label="开启报名时间：">xxxx-xx-xx</el-form-item>
                    <el-form-item label="开启住宿信息采集：">开启</el-form-item>
                    <el-form-item label="住宿信息采集须知：">xxxx</el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">期别要求</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                    <el-form-item label="是否开启报到：">开启</el-form-item>
                    <el-form-item label="是否开启结业测试：">开启</el-form-item>
                    <el-form-item label="是否开启考勤：">开启</el-form-item>
                    <el-form-item label="考勤考核要求：">不开放</el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">联络信息</span>
                  </div>
                  <el-form ref="form" :model="form" label-width="180px" class="m-form f-mt10">
                    <el-form-item label="班主任：">xxx</el-form-item>
                    <el-form-item label="班主任联系电话：">13xxxxxxxxx</el-form-item>
                    <el-form-item label="酒店联系人：">xxxx</el-form-item>
                    <el-form-item label="酒店联系人电话：">13xxxxxxxx</el-form-item>
                  </el-form>
                </div>
                <div class="section">
                  <div class="m-tit is-border-bottom bg-gray">
                    <span class="tit-txt">学员须知</span>
                  </div>
                  <div class="f-p20">
                    学员须知学员须知学员须知学员须知学员须知
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">返回</el-button>
          </div>
        </el-drawer>
        <!--查看课程-->
        <el-button @click="dialog00000002 = true" type="primary" class="f-mr20 f-mb20">查看课程</el-button>
        <el-drawer
          title="查看课程"
          :visible.sync="dialog00000002"
          :direction="direction"
          size="90%"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="课程名称" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    课程名称课程名称课程名称课程名称课程名称课程名称
                  </div>
                  <div v-else>
                    课程名称课程名称课程名称课程名称课程名称课程名称
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="授课教师" min-width="180" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    授课教师姓名
                  </div>
                  <div v-else>
                    授课教师姓名
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="教师职称" min-width="180" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    教师职称
                  </div>
                  <div v-else>
                    教师职称
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="教师所在单位" min-width="180" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    教师所在单位
                  </div>
                  <div v-else>
                    教师所在单位
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="课程学时" min-width="180" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    课程学时
                  </div>
                  <div v-else>
                    课程学时
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="授课日期" min-width="250" align="center">
                <template>
                  xxxx-xx-xx
                </template>
              </el-table-column>
              <el-table-column label="授课开始时间" min-width="250" align="center">
                <template>
                  xxxx-xx-xx
                </template>
              </el-table-column>
              <el-table-column label="授课结束时间" min-width="250" align="center">
                <template>
                  xxxx-xx-xx
                </template>
              </el-table-column>
              <el-table-column label="课程时长(分)" min-width="180" align="center">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    课程时长
                  </div>
                  <div v-else>
                    课程时长
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button type="primary">返回</el-button>
          </div>
        </el-drawer>
        <!--查看二维码-->
        <el-button @click="dialog00000003 = true" type="primary" class="f-mr20 f-mb20">查看二维码</el-button>
        <el-dialog title="" :visible.sync="dialog00000003" width="360px" class="m-dialog">
          <div class="f-flex f-align-center f-justify-center">
            <img src="./assets/images/qr-code.png" class="u-qr-code" />
          </div>
          <div slot="footer">
            <el-button>取消</el-button>
            <el-button type="primary">下载二维码</el-button>
          </div>
        </el-dialog>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        num: 100,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        checked: false,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        dialog12: false,
        dialog13: false,
        dialog14: false,
        dialog15: false,
        dialog16: false,
        dialog17: false,
        dialog18: false,
        dialog19: false,
        dialog20: false,
        dialog21: false,
        dialog22: false,
        dialog23: false,
        dialog24: false,
        dialog25: false,
        dialog26: false,
        dialog27: false,
        dialog01: false,
        dialog02: false,
        dialog03: false,
        dialog04: false,
        dialog05: false,
        dialog06: false,
        dialog07: false,
        dialog08: false,
        dialog09: false,
        dialog001: false,
        dialog002: false,
        dialog003: false,
        dialog004: false,
        dialog005: false,
        dialog006: false,
        dialog0001: false,
        dialog0002: false,
        dialog0003: false,
        dialog00031: false,
        dialog0004: false,
        dialog0005: false,
        dialog0006: false,
        dialog0007: false,
        dialog0008: false,
        dialog0009: false,
        dialog0010: false,
        dialog0011: false,
        dialog0012: false,
        dialog0013: false,
        dialog0014: false,
        dialog00001: false,
        dialog00002: false,
        dialog00003: false,
        dialog00004: false,
        dialog00005: false,
        dialog000001: false,
        dialog000002: false,
        dialog000003: false,
        dialog000004: false,
        dialog000005: false,
        dialog0000001: false,
        dialog0000002: false,
        dialog0000003: false,
        dialog0000004: false,
        dialog0000005: false,
        dialog00000001: false,
        dialog00000002: false,
        dialog00000003: false,
        dialog00000004: false,
        dialog00000005: false,
        dialog000000001: false,
        dialog000000002: false,
        dialog000000003: false,
        dialog000000004: false,
        dialog000000005: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      open3() {
        this.$message({
          message: '本次修改内容如涉及到考核重算，重算任务于程序后台自动执行，即将自动为您跳转到方案管理列表页。',
          type: 'warning',
          duration: 5000,
          customClass: 'm-message'
        })
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
