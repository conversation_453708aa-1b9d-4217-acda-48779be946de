import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformNewNotice'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformNewNotice'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举

// 类

/**
 * @author: eleven
@date: 2020/2/12
@description:
 */
export class NewsNoticeCondition {
  /**
   * 资讯标题
   */
  title?: string
  /**
   * 资讯模糊查询（标题或内容）
   */
  titleOrContent?: string
  /**
   * 关联展示的施教机构
   */
  associateUnitId?: string
  /**
   * 所挂的施教机构分类id
   */
  categoryId?: string
  /**
   * 资讯分类类别
@See com.fjhb.btpx.platform.service.news.NoticeCategoryEnum
   */
  categoryType?: string
  /**
   * 资讯状态 -1 不查 0-草稿 1-发布
如果是查询启用的接口，该参数无效
   */
  status: number
  /**
   * 类型 1-资讯 2-公告
   */
  type: number
  /**
   * 资讯所属的单位id （培训机构id）
   */
  unitId?: string
  /**
   * 地区
   */
  regionId?: string
  /**
   * 排序方式 默认1 1按发布时间倒序 2浏览量倒序
   */
  sortType?: number
}

/**
 * @author: eleven
@date: 2020/2/12
@description:
 */
export class NewsDTO {
  /**
   * 主键id 修改时有值 新增时没值
   */
  id?: string
  /**
   * 标题
   */
  title?: string
  /**
   * 资讯、公告类型（1-资讯 2-公告）
   */
  type: number
  /**
   * 封面图片
   */
  photoUrl?: string
  /**
   * 内容
   */
  content?: string
  /**
   * 分类id
   */
  categoryId?: string
  /**
   * 发布状态 0-草稿 1-发布 2-定时发布
   */
  status: number
  /**
   * 发布时间
   */
  publishTime?: string
  /**
   * 资讯来源
   */
  origin?: string
  /**
   * 资讯作者
   */
  author?: string
  /**
   * 弹窗起始时间
   */
  popStart?: string
  /**
   * 弹窗结束时间
   */
  popOver?: string
  /**
   * 关联的单位集合
   */
  associateUnitList?: Array<NewsAssociateUnitDTO1>
  /**
   * 是否为重要咨询
   */
  isImportance: number
  /**
   * 在发布的地区
   */
  regionIds?: Array<string>
}

/**
 * @author: eleven
@date: 2020/4/21
 */
export class NewsAssociateUnitDTO1 {
  /**
   * 关联展示的施教机构
   */
  associateUnitId?: string
  /**
   * 所挂的施教机构分类id
   */
  categoryId?: string
}

/**
 * 资讯更新/创建dto
Author:FangKunSen
Time:2020-06-09,17:23
 */
export class NewsCategoryUpdateDTO {
  /**
   * 当id为空时执行创建
   */
  id?: string
  /**
   * 父分类id，0为顶级分类
   */
  parentId?: string
  /**
   * 分类名称
   */
  name?: string
  /**
   * 分类说明
   */
  description?: string
  /**
   * 状态 0-停用 1-启用
   */
  status: number
  /**
   * 是否内置
   */
  internal: boolean
  /**
   * 资讯类别类型
@see com.fjhb.btpx.platform.service.news.NoticeCategoryEnum
   */
  categoryType?: string
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * @author: eleven
@date: 2020/2/13
@description:
 */
export class CategoryDetailDTO {
  /**
   * id
   */
  id: string
  /**
   * 父分类id
   */
  parentId: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 分类说明
   */
  description: string
  /**
   * 状态 0-停用 1-启用
   */
  status: number
  /**
   * 是否内置
   */
  internal: boolean
  /**
   * 资讯类别类型
@see com.fjhb.btpx.platform.service.news.NoticeCategoryEnum
   */
  categoryType: string
}

/**
 * @author: eleven
@date: 2020/2/12
@description:
 */
export class NewsNoticeDTO {
  /**
   * 资讯id
   */
  id: string
  /**
   * 标题
   */
  title: string
  /**
   * 封面图片路径
   */
  photoUrl: string
  /**
   * 公告、资讯发布状态（0-草稿 1-已发布）
   */
  status: number
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 发布者Id
   */
  publishPersonId: string
  /**
   * 精简资讯内容
分页返回默认10个字
   */
  content: string
  /**
   * 被阅读次数
   */
  readCount: number
  /**
   * 是否为重要咨询
   */
  isImportance: number
  /**
   * 关联的单位集合
   */
  associateUnitList: Array<NewsAssociateUnitDTO>
  /**
   * 在发布的地区
   */
  regionIds: Array<string>
  /**
   * 是否弹窗公告
   */
  pop: boolean
}

/**
 * @author: eleven
@date: 2020/2/13
@description:
 */
export class NewsNoticeDetailDTO {
  /**
   * 资讯、公告类型（1-资讯 2-公告）
   */
  type: number
  /**
   * 一级分类id
   */
  firstLevelCategoryId: string
  /**
   * 二级分类id
   */
  secondLevelCategoryId: string
  /**
   * 资讯来源
   */
  origin: string
  /**
   * 弹窗起始时间
   */
  popStart: string
  /**
   * 弹窗结束时间
   */
  popOver: string
  /**
   * 资讯id
   */
  id: string
  /**
   * 标题
   */
  title: string
  /**
   * 封面图片路径
   */
  photoUrl: string
  /**
   * 公告、资讯发布状态（0-草稿 1-已发布）
   */
  status: number
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 发布者Id
   */
  publishPersonId: string
  /**
   * 精简资讯内容
分页返回默认10个字
   */
  content: string
  /**
   * 被阅读次数
   */
  readCount: number
  /**
   * 是否为重要咨询
   */
  isImportance: number
  /**
   * 关联的单位集合
   */
  associateUnitList: Array<NewsAssociateUnitDTO>
  /**
   * 在发布的地区
   */
  regionIds: Array<string>
  /**
   * 是否弹窗公告
   */
  pop: boolean
}

/**
 * 弹窗公告
Author:FangKunSen
Time:2020-06-26,10:56
 */
export class PopNewsNoticeDTO {
  /**
   * 当前时间段是否存在弹窗公告
   */
  hasPopNewsNoticeNow: boolean
  /**
   * 弹窗公告主体
当不存在弹窗公告时，该实体为空
   */
  newsNoticeDTO: NewsNoticeDTO
}

/**
 * @author: eleven
@date: 2020/4/21
 */
export class NewsAssociateUnitDTO {
  /**
   * 关联展示的施教机构
   */
  associateUnitId: string
  /**
   * 所挂的施教机构分类id
   */
  categoryId: string
}

export class NewsNoticeDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<NewsNoticeDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 检查分类名称是否重复
   * @param name
   * @return true:重复；false:不重复
   * @param query 查询 graphql 语法文档
   * @param name 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkCategoryNameExist(
    name: string,
    query: DocumentNode = GraphqlImporter.checkCategoryNameExist,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { name },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据资讯id查询上一条或下一条资讯信息
   * @param id             当前资讯id
   * @param queryParameter 查询条件
   * @param location       查询位置 0 上一条 1下一条
   * @return 资讯信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLastOrNextNewsNotice(
    params: { id?: string; queryParameter?: NewsNoticeCondition; location: number },
    query: DocumentNode = GraphqlImporter.getLastOrNextNewsNotice,
    operation?: string
  ): Promise<Response<NewsNoticeDetailDTO>> {
    return commonRequestApi<NewsNoticeDetailDTO>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据咨询id查询资讯详情
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getNewsNotice(
    id: string,
    query: DocumentNode = GraphqlImporter.getNewsNotice,
    operation?: string
  ): Promise<Response<NewsNoticeDetailDTO>> {
    return commonRequestApi<NewsNoticeDetailDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取当前时间的弹窗公告
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPopNewsNotice(
    query: DocumentNode = GraphqlImporter.getPopNewsNotice,
    operation?: string
  ): Promise<Response<PopNewsNoticeDTO>> {
    return commonRequestApi<PopNewsNoticeDTO>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取资讯分类详情
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listAllCategory(
    query: DocumentNode = GraphqlImporter.listAllCategory,
    operation?: string
  ): Promise<Response<Array<CategoryDetailDTO>>> {
    return commonRequestApi<Array<CategoryDetailDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取子分类
   * @return
   * @param query 查询 graphql 语法文档
   * @param parentId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listSubCategoryByParentId(
    parentId: string,
    query: DocumentNode = GraphqlImporter.listSubCategoryByParentId,
    operation?: string
  ): Promise<Response<Array<CategoryDetailDTO>>> {
    return commonRequestApi<Array<CategoryDetailDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { parentId },
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageNewsNotice(
    params: { page?: Page; queryParam?: NewsNoticeCondition },
    query: DocumentNode = GraphqlImporter.pageNewsNotice,
    operation?: string
  ): Promise<Response<NewsNoticeDTOPage>> {
    return commonRequestApi<NewsNoticeDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 校验草稿状态的资讯是否可以发布
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async checkPublishAble(
    id: string,
    mutate: DocumentNode = GraphqlImporter.checkPublishAble,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建资讯
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async create(
    request: NewsDTO,
    mutate: DocumentNode = GraphqlImporter.create,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除资讯
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteById(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteById,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 删除资讯类别
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteNewsCategory(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteNewsCategory,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 发布草稿状态的资讯
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async publish(
    id: string,
    mutate: DocumentNode = GraphqlImporter.publish,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 置为草稿
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async toDraft(
    id: string,
    mutate: DocumentNode = GraphqlImporter.toDraft,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 编辑资讯
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async update(
    request: NewsDTO,
    mutate: DocumentNode = GraphqlImporter.update,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建、更新资讯类别，当id为空时创建
   * @param newsCategoryUpdateDTO
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param newsCategoryUpdateDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateNewsCategory(
    newsCategoryUpdateDTO: NewsCategoryUpdateDTO,
    mutate: DocumentNode = GraphqlImporter.updateNewsCategory,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newsCategoryUpdateDTO },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
