import MsBasicdataQueryFrontGatewayBasicDataQueryForestage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import Basicdata, {
  BusinessDataDictionaryResponse,
  RegionCodeRequest,
  RegionRequest,
  RegionResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import * as GraphqlImporter from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage/graphql-importer'
import Region from '@api/service/management/common/Region'
import QueryUserFactory from '@api/service/management/user/QueryUserFactory'
import { cloneDeep } from 'lodash'
import AssembleTree from '../../utils/AssembleTree'
import { RewriteGraph } from '../../utils/RewriteGraph'
import RegionTreeVo from './vo/RegionTreeVo'

/**
 *
 */
class QueryBusinessRegion {
  private businessId = 'TRAINING_SCHEME_REGION'

  /**
   * 运营域全国地区缓存
   */
  private regionCache: Array<RegionTreeVo> = new Array<RegionTreeVo>()

  /**
   * 地区树
   */
  private regionTree: Array<RegionTreeVo> = new Array<RegionTreeVo>()

  /**
   * 地区名缓存
   */
  private regionNameMap = new Map<string, string>()

  /**
   * 下级地区
   */
  private regionChildMap = new Map<string, Array<RegionTreeVo>>()

  /**
   * 业务地区缓存
   */
  private businessRegionMap = new Map<string, RegionTreeVo>()
  /**
   * 物理地区缓存
   */
  private countrywideRegionMap = new Map<string, RegionTreeVo>()
  /**
 * 所有地区Name+parentId做Map映射
 * 地区名称转code使用
 */
  regionNametoCodeMap = new Map<string, RegionTreeVo>()
  /**
   * 服务地区id缓存
   */
  private serviceRegionMap = new Map<string, Array<string>>()
  /**
   * 查询网校下的业务地区 只存在市级 --- 做业务使用的数据 ----------- 旧口
   * @return {ResponseStatus}
   */
  async queryBusinessRegion() {
    const req = new RegionRequest()
    req.businessId = this.businessId
    req.code = '350000'
    const response = await Basicdata.listChildRegionInSubProject(req)
    this.regionTree = response.data.map(item => RegionTreeVo.fromBusiness(item))
    return this.regionTree
  }
  /**
   * 查询网校服务地区 ------------  新口
   * @param type 0 行业地区 1服务地区
   * @param isRegistered 是否注册使用
   */
  async getServiceOrIndustry(type: number, isRegistered?: boolean) {
    const res = await Region.getServiceOrIndustryRegion(type)
    const regionArr = res.data.map(item => RegionTreeVo.fromBusiness(item))
    const assembleTree = new AssembleTree<RegionTreeVo>(regionArr, 'id', 'parentId', 'children')
    const tree = assembleTree.assembleTree()
    if (isRegistered) {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === '100000') {
          // 删除全国的数据
          tree.splice(i, 1)
          i--
        }
      }
    }
    return tree
  }
  /**
   * 查询业务地区通过idList
   * @param idList regionId集合
   * @return RegionTreeVo[] 地区列表
   */
  async queryBusinessRegionByIdList(idList: string[]) {
    const queryIds = new Array<string>()
    const result = new Array<RegionTreeVo>()
    idList.forEach(id => {
      if (!this.businessRegionMap.get(id)) {
        queryIds.push(id)
      } else {
        result.push(this.businessRegionMap.get(id))
      }
    })
    if (queryIds.length > 0) {
      const response = await MsBasicdataQueryFrontGatewayBasicDataQueryForestage.listBusinessRegionListById(queryIds)
      if (response.status?.isSuccess()) {
        response.data?.map(item => {
          const regionTree = new RegionTreeVo()
          regionTree.id = item.id
          regionTree.name = item.name
          regionTree.parentId = item.parentId
          regionTree.regionPath = item.regionPath
          regionTree.enable = item.enable
          regionTree.sort = item.sort
          this.businessRegionMap.set(item.id, regionTree)
          return regionTree
        })
      }
    }
    return result
  }

  /**
   * 获取服务地区id
   */
  async getServiceRegionIds() {
    if (!this.serviceRegionMap.has('serviceRegion')) {
      const res = await Region.getServiceOrIndustryRegion(1)
      const ids = new Array<string>()
      if (res.status.isSuccess()) {
        res.data.forEach(el => {
          ids.push(el.code)
        })
      }
      this.serviceRegionMap.set('serviceRegion', ids)
    }
    return this.serviceRegionMap.get('serviceRegion')
  }

  /**
   * 运营域获取地区名称（批量）
   */
  async queryRegionsNameByIds(ids: string[]) {
    const queryIds = new Array<string>()
    ids.forEach(id => {
      if (!this.regionNameMap.get(id)) {
        queryIds.push(id)
      }
    })
    if (queryIds.length > 0) {
      const params = new Array<RegionCodeRequest>()
      queryIds.forEach(id => {
        const param = new RegionCodeRequest()
        param.code = id
        param.businessId = 'PLATFORM_BUSINESS_REGION'
        params.push(param)
      })
      const request = new RewriteGraph<BusinessDataDictionaryResponse, RegionCodeRequest>(
        Basicdata._commonQuery,
        GraphqlImporter.getRegionInSubProject
      )
      const { status } = await request.request(params)
      if (status.code === 200) {
        for (const [key, value] of request.itemMap.entries()) {
          if (value) {
            this.regionNameMap.set(key?.code, value?.name)
          }
        }
      }
    }
    const regionMap = new Map<string, string>()
    ids.forEach(id => {
      regionMap.set(id, this.regionNameMap.get(id))
    })
    return regionMap
    // return this.regionNameMap
  }

  /**
   * 运营域超管查询下一级业务地区
   * @param id
   */
  async queryChildRegionById(id: string) {
    if (!this.regionChildMap.get(id)) {
      const request = new RegionCodeRequest()
      request.businessId = 'PLATFORM_BUSINESS_REGION'
      request.code = id
      let treeArr = new Array<RegionTreeVo>()
      const response = await Basicdata.listChildRegionInSubProject(request)
      if (response.status.isSuccess()) {
        treeArr = response.data.map(item => RegionTreeVo.fromBusiness(item))
      }
      this.regionChildMap.set(id, treeArr)
    }
    return this.regionChildMap.get(id)
  }

  /**
   * 运营域超管查询下一级业务地区（批量）
   */
  async queryChildRegionByIds(ids: string[]) {
    const queryIds = new Array<string>()
    ids.forEach(id => {
      if (!this.regionChildMap.get(id)) {
        queryIds.push(id)
      }
    })
    if (queryIds.length > 0) {
      const params = new Array<RegionCodeRequest>()
      queryIds.forEach(id => {
        const param = new RegionCodeRequest()
        param.businessId = 'PLATFORM_BUSINESS_REGION'
        param.code = id
        params.push(param)
      })
      const request = new RewriteGraph<RegionResponse[], RegionCodeRequest>(
        Basicdata._commonQuery,
        GraphqlImporter.listChildRegionInSubProject
      )
      const { status } = await request.request(params)
      if (status.code === 200) {
        for (const [key, value] of request.itemMap.entries()) {
          if (value && value.length) {
            const target = value.map(item => RegionTreeVo.fromBusiness(item))
            this.regionChildMap.set(key.code, target)
          }
        }
      }
    }
    return this.regionChildMap
  }

  /**
   * 运营域超管查询物理地区
   */
  async queryOperationServiceArea() {
    if (this.regionCache.length) return this.regionCache
    const request = new RegionCodeRequest()
    request.businessId = 'PLATFORM_BUSINESS_REGION'
    request.code = '0'
    const totalTree = new Array<RegionTreeVo>()
    const response = await Basicdata.listChildRegionInSubProject(request)
    let provinces = new Array<RegionTreeVo>()
    if (response.status.isSuccess()) {
      provinces = response.data.map(item => RegionTreeVo.fromBusiness(item))
      const provincesIds = new Array<string>()
      provinces.forEach(province => {
        provincesIds.push(province.id)
      })
      totalTree.push(...provinces)
      const cities = await this.queryChildRegionByIds(provincesIds)
      const cityIds = new Array<string>()
      cities.forEach(city => {
        totalTree.push(...city)
        city.forEach(el => {
          cityIds.push(el.id)
        })
      })
      const counties = await this.queryChildRegionByIds(cityIds)
      counties.forEach(county => {
        totalTree.push(...county)
      })
      const assembleTree = new AssembleTree<RegionTreeVo>(totalTree, 'id', 'parentId', 'children')
      this.regionCache = assembleTree.assembleTree()
      return this.regionCache
    }
  }

  /**
   * UI 展示地区树使用
   * @param tree 完整的地区
   * @param regionCodes 需要展示的地区code
   * @param isCustomer 是否在学员域展示
   */
  filterRegionTree(
    tree: RegionTreeVo[],
    regionCodes: string[],
    isCustomer?: boolean,
    isMultiple = false
  ): RegionTreeVo[] {
    const copyTree = cloneDeep(tree)
    const filteredTree: RegionTreeVo[] = []
    for (const node of copyTree) {
      const isMatched = regionCodes.includes(node.id)
      if (!isCustomer && !isMultiple) {
        node.disabled = !(regionCodes.includes(node.id) || regionCodes.includes(node.parentId))
      }

      if (isMatched) {
        // 当前节点匹配指定地区代码，保留该节点及其子节点
        filteredTree.push(node)
      } else if (node.children) {
        // 当前节点不匹配指定地区代码，递归处理子节点
        const filteredChildren = this.filterRegionTree(node.children, regionCodes, isCustomer, isMultiple)

        if (filteredChildren.length > 0) {
          // 子节点有匹配的节点，保留当前节点及其子节点
          const newNode = { ...node }
          newNode.children = filteredChildren
          filteredTree.push(newNode)
        }
      }
    }

    return filteredTree
  }

  /**
   * 通过管理员信息获取地区管理员所在服务地区
   */
  getRegionAdminArea() {
    let serveArea = new Array<string>()
    const { userInfo, roleList } = QueryUserFactory.queryManagerDetail.adminInfo
    const isRegionAdmin = roleList.filter(role => {
      return role.roleCategory === 320
    })
    const { manageRegionList } = userInfo
    if (isRegionAdmin && manageRegionList && manageRegionList.length) {
      const regionPath = manageRegionList[0].regionPath.split('/')
      serveArea = [regionPath.pop()]
    }
    return serveArea
  }

  /**
   * 网校、运营域获取全国物理地区
   */
  async getCountrywideRegion() {
    if (this.regionCache.length) return this.regionCache
    let tree = new Array<RegionTreeVo>()
    const businessId = 'PLATFORM_BUSINESS_REGION'
    const result = await Basicdata.listAllRegionInSubProject(businessId)
    if (result.status.isSuccess()) {
      tree = result.data.map(item => {
        this.countrywideRegionMap.set(item.code, RegionTreeVo.fromBusiness(item))
        this.regionNametoCodeMap.set(item.name + item.parentCode, RegionTreeVo.fromBusiness(item))
        return RegionTreeVo.fromBusiness(item)
      })
    }
    const assembleTree = new AssembleTree<RegionTreeVo>(tree, 'id', 'parentId', 'children')
    this.regionCache = assembleTree.assembleTree()
    return this.regionCache
  }

  /**
   * 通过code获取详情
   * @param code 字典code
   */
  getCountrywideRegionCodeDetail(code: string): RegionTreeVo {
    return this.countrywideRegionMap.get(code)
  }

  /**
   * 批量通过code获取详情
   * @param codeList 字典codeList
   */
  getCountrywideRegionList(codeList: Array<string>): Array<RegionTreeVo> {
    if (!codeList.length) return
    const list = codeList.map(item => {
      return this.getCountrywideRegionCodeDetail(item)
    })
    return list
  }
}
export default new QueryBusinessRegion()
