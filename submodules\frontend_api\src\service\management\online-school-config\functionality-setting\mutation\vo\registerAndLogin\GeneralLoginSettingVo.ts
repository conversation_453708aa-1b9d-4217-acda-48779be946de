import {
  FailedAuthConfig,
  FailedAuthConfigData,
  GetSSOSecureConfigResponse,
  PasswordChangeConfig,
  PasswordChangeConfigData,
  SaveSSOSecureConfigRequest,
  SmsCodeAuthConfig,
  SmsCodeAuthConfigData
} from '@api/ms-gateway/ms-identity-authentication-secure-config-v1'
import ChangePwdCycleRule from '@api/service/management/online-school-config/functionality-setting/mutation/vo/registerAndLogin/ChangePwdCycleRule'
import LimitLoginNumRule from '@api/service/management/online-school-config/functionality-setting/mutation/vo/registerAndLogin/LimitLoginNumRule'
import LoginWithSMSRule from '@api/service/management/online-school-config/functionality-setting/mutation/vo/registerAndLogin/LoginWithSMSRule'

/**
 * 通用登录规则
 */
class GneralLoginSettingVo {
  /**
   * 强制初始密码修改
   */
  forcePwdChange = false
  /**
   * 更换密码配置
   */
  changePwdCycleRule = new ChangePwdCycleRule()
  /**
   * 限制非法登录次数配置
   */
  limitLoginNumRule = new LimitLoginNumRule()
  /**
   * 登录时短信验证配置
   */
  loginWithSMSRule = new LoginWithSMSRule()

  static to(generalLogin: GneralLoginSettingVo) {
    const request = new SaveSSOSecureConfigRequest()
    request.smsCodeAuthConfig = new SmsCodeAuthConfig()
    request.smsCodeAuthConfig.id = generalLogin.loginWithSMSRule.id
    request.smsCodeAuthConfig.credentialTypes = [1000, 1001]
    request.smsCodeAuthConfig.applicationDomains = [{ applicationType: 0, applicationMemberType: 6 }]
    request.smsCodeAuthConfig.enabled = generalLogin.loginWithSMSRule.enable

    request.passwordChangeConfig = new PasswordChangeConfig()
    request.passwordChangeConfig.id = generalLogin.changePwdCycleRule.id
    request.passwordChangeConfig.enabled = generalLogin.changePwdCycleRule.enable
    request.passwordChangeConfig.credentialTypes = [1000, 1001]
    request.passwordChangeConfig.applicationDomains = [{ applicationType: 0, applicationMemberType: 6 }]
    request.passwordChangeConfig.securityLevel = 3
    request.passwordChangeConfig.passwordChangeCycle = generalLogin.changePwdCycleRule.changePwdCycle

    request.failedAuthConfig = new FailedAuthConfig()
    request.failedAuthConfig.enabled = generalLogin.limitLoginNumRule.enable
    request.failedAuthConfig.id = generalLogin.limitLoginNumRule.id
    request.failedAuthConfig.credentialTypes = [1000, 1001]
    request.failedAuthConfig.applicationDomains = [{ applicationType: 0, applicationMemberType: 6 }]
    request.failedAuthConfig.securityLevel = 3
    request.failedAuthConfig.failedAuthAttemptDuration = 60
    request.failedAuthConfig.accountLockDuration = generalLogin.limitLoginNumRule.lockTime
    request.failedAuthConfig.failedAuthAttemptUpperLimit = generalLogin.limitLoginNumRule.limitLoginNum
    return request
  }

  static from(dto: GetSSOSecureConfigResponse) {
    const vo = new GneralLoginSettingVo()
    const passwordChangeConfigs = dto.passwordChangeConfigData?.length
      ? dto.passwordChangeConfigData[0]
      : new PasswordChangeConfigData()
    const failedAuthConfigs = dto.failedAuthConfigData?.length
      ? dto.failedAuthConfigData[0]
      : new FailedAuthConfigData()
    const smsCodeAuthConfigs = dto.smsCodeAuthConfigData?.length
      ? dto.smsCodeAuthConfigData[0]
      : new SmsCodeAuthConfigData()
    vo.changePwdCycleRule.id = passwordChangeConfigs.id
    vo.changePwdCycleRule.enable = passwordChangeConfigs.enabled
    vo.changePwdCycleRule.changePwdCycle = passwordChangeConfigs.passwordChangeCycle || 30

    vo.limitLoginNumRule.id = failedAuthConfigs.id
    vo.limitLoginNumRule.enable = failedAuthConfigs.enabled
    vo.limitLoginNumRule.limitLoginNum = failedAuthConfigs.failedAuthAttemptUpperLimit || 5
    vo.limitLoginNumRule.lockTime = failedAuthConfigs.accountLockDuration || 60

    vo.loginWithSMSRule.id = smsCodeAuthConfigs.id
    vo.loginWithSMSRule.enable = smsCodeAuthConfigs.enabled
    return vo
  }
}
export default GneralLoginSettingVo
