import {
  DateScopeRequest,
  IssueCommoditySkuBackStageRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

export default class QueryCanChangeIssueParam {
  /**
   * 商品id
   */
  commoditySkuId = ''

  /**
   * 期别名称
   */
  issueName = ''

  /**
   * 报名开始时间范围
   */
  registerStartTimeScope = ''

  /**
   * 报名结束时间
   */
  registerEndTime = ''

  /**
   * 剔除的期别id
   */
  excludedIssueIdList: Array<string> = new Array<string>()

  /**
   * 转化为查询入参
   */
  toRequest() {
    const request = new IssueCommoditySkuBackStageRequest()
    request.commoditySkuId = this.commoditySkuId
    if (this.issueName) {
      request.issueNameExact = this.issueName
    }
    request.issueSignUpBeginDate = new DateScopeRequest()
    if (this.registerStartTimeScope) {
      request.issueSignUpBeginDate.begin = this.registerStartTimeScope
    }
    request.issueSignUpEndDate = new DateScopeRequest()
    if (this.registerEndTime) {
      request.issueSignUpEndDate.end = this.registerEndTime
    }
    request.issueTrainingBeginTime = new DateScopeRequest()
    request.issueTrainingEndTime = new DateScopeRequest()
    if (this.excludedIssueIdList.length) {
      request.excludedIssueIdList = this.excludedIssueIdList
    }

    return request
  }
}
