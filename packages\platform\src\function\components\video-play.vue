<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <el-row type="flex" justify="center" class="width-limit">
        <el-col :md="20" :lg="16" :xl="13">
          <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
            <el-form-item label="全网校的防录屏跑马灯：">
              <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
            </el-form-item>
            <el-form-item label="视频贴片：">
              <el-upload action="#" list-type="picture-card" :auto-upload="false" class="m-pic-upload long-pic">
                <div slot="default" class="upload-placeholder">
                  <i class="el-icon-plus"></i>
                  <p class="txt">上传图片</p>
                </div>
                <div slot="file" slot-scope="{ file }" class="img-file">
                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                  <div class="el-upload-list__item-actions">
                    <span class="el-upload-list__item-preview">
                      <i class="el-icon-zoom-in"></i>
                    </span>
                    <span v-if="!disabled" class="el-upload-list__item-delete">
                      <i class="el-icon-delete"></i>
                    </span>
                  </div>
                </div>
                <div slot="tip" class="el-upload__tip">
                  <i class="el-icon-warning"></i>
                  <span class="txt">
                    视频贴片是视频播放器在播放前展示的图片。请先设计好后上传，尺寸：700px * 394px。
                  </span>
                </div>
              </el-upload>
              <el-dialog width="1100px" class="m-dialog-pic">
                <img alt="" />
              </el-dialog>
            </el-form-item>
            <el-form-item label="贴片播放时长：">
              <el-input v-model="form.name" class="input-num" />
              <span class="f-mlr10">时</span>
              <el-input v-model="form.name" class="input-num" />
              <span class="f-mlr10">分</span>
              <el-input v-model="form.name" class="input-num" />
              <span class="f-mlr10">秒</span>
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button>取消</el-button>
              <el-button type="primary">保存</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    disabled = true
    form = {
      name: '',
      delivery: ''
    }
  }
</script>
