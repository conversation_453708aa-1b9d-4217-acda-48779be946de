<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <!--条件查询-->
      <el-row :gutter="16" class="m-query is-border-bottom mb0">
        <el-form :inline="true" label-width="auto">
          <el-col :sm="12" :md="6">
            <el-form-item label="方案名称">
              <el-input
                clearable
                placeholder="请输入培训方案名称"
                v-model="courserPackageSyncSchemeRequest.schemeName"
              />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="6">
            <el-form-item label="数据状态">
              <el-select
                clearable
                v-model="courserPackageSyncSchemeRequest.dataStatus"
                placeholder="方案和课程包课程是否一致"
              >
                <el-option label="一致" value="1"></el-option>
                <el-option label="不一致" value="0"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="6">
            <el-form-item>
              <el-select
                clearable
                placeholder="请选择同步状态"
                v-model="courserPackageSyncSchemeRequest.SyncStatus"
                label="同步状态"
              >
                <el-option label="未同步" value="0"></el-option>
                <el-option label="同步成功" value="1"></el-option>
                <el-option label="同步中" value="2"></el-option>
                <el-option label="同步失败" value="3"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="6" class="f-fr">
            <el-form-item class="f-tr">
              <el-button @click="clickQuery" type="primary">查询</el-button>
              <el-button @click="resetParam">重置</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <div class="m-tit is-small">
        <span class="tit-txt">
          同步培训方案（共{{ summaryList.schemeSyncCount || 0 }}个方案使用，已同步{{
            summaryList.syncedCount || 0
          }}
          个，未同步{{ summaryList.noSyncedCount || 0 }}个，同步中{{ summaryList.syncingCount || 0 }}个，同步失败{{
            summaryList.syncFailedCount || 0
          }}个）
        </span>
      </div>
      <!--表格-->
      <el-table stripe :data="asyncCoursepackageList" max-height="500px" class="m-table">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="培训方案名称" prop="name" min-width="300" fixed="left">
          <!-- <template >培训方案名称培训方案名称培训方案名称</template> -->
        </el-table-column>
        <el-table-column label="数据状态" prop="dataStatus" min-width="140" align="center">
          <template slot-scope="scope">
            <div v-if="scope.row.dataStatus == '不一致'">
              <el-tag type="danger">不一致</el-tag>
            </div>
            <div v-if="scope.row.dataStatus == '一致的'">
              <el-tag type="success">一致</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="同步状态" prop="syncStatus" min-width="140">
          <template slot-scope="scope">
            <!-- {{ scope.row.syncStatus }} -->
            <div v-if="scope.row.syncStatus == '未同步'">
              <el-badge is-dot type="info" class="badge-status">未同步</el-badge>
            </div>
            <div v-else-if="scope.row.syncStatus == '同步中'">
              <el-badge is-dot type="primary" class="badge-status">同步中</el-badge>
            </div>
            <div v-else-if="scope.row.syncStatus == '同步失败'">
              <el-badge is-dot type="danger" class="badge-status">同步失败</el-badge>
              <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                <i class="el-icon-warning m-tooltip-icon f-cr f-ml5"></i>
                <div slot="content">失败原因：{{ scope.row.message }}</div>
              </el-tooltip>
            </div>
            <div v-else>
              <el-badge is-dot type="success" class="badge-status">已同步</el-badge>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="最新同步时间" prop="syncTime" align="center" min-width="180">
          <template slot-scope="scope">
            {{ scope.row.syncTime || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="synchronizationLog(scope.row.id)">同步日志</el-button>
            <el-button type="text" size="mini" @click="viewSchene(scope.row)">查看方案</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!-- <el-drawer title="日志详情" :visible.sync="syncLogDetailDialog" size="800px" custom-class="m-drawer">
        <div class="drawer-bd f-mt20 f-mlr40">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in logDetailList" :key="index">
              <p class="f-mb10 f-fb f-f15">
                {{ item.time }} <span class="f-ml30">{{ item.name }}</span>
              </p>
              <div class="f-c6" v-for="(itx, idx) in item.describeList" :key="idx">
                <div class="f-mt5">{{ itx }}</div>
                <el-button type="text" v-show="idx >= 2">查看更多 +</el-button>
                <el-button type="text" v-show="idx >= 2">收起更多 -</el-button>
              </div>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-drawer> -->

      <!--分页-->
      <!-- <hb-pagination
        :total-size="400"
        :page="page"
        class="f-mt15 f-tr"
        @size-change="pageSizeChange"
        @current-change="currentPageChange"
      >
      </hb-pagination> -->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </el-card>
    <el-drawer title="日志详情" :visible.sync="syncLogDetailDialog" size="800px" custom-class="m-drawer">
      <div class="drawer-bd f-mt20 f-mlr40">
        <el-timeline>
          <el-timeline-item v-for="(item, index) in logDetailList" :key="index">
            <p class="f-mb10 f-fb f-f15">
              {{ item.logTime }}<span class="f-ml30">{{ item.creator.name }}</span>
            </p>
            <div class="f-c6">
              <div class="f-mt5" v-if="item.status == 1">发起同步，同步结果【成功】</div>
              <div class="f-mt5" v-if="item.status !== 1">发起同步，同步结果【失败】，失败原因：{{ item.message }}</div>
            </div>
            <!-- <p class="f-mb10 f-fb f-f15">
              {{ item.logTime }}
              <span class="f-ml30"
                >{{ item.creator.name }}发起同步，同步结果【{{ item.status == 1 ? '成功' : '失败' }}】</span
              >
              <span v-if="!item.isSuccess()">{{ item.message }}</span>
            </p> -->
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { UiPage, Query } from '@hbfe/common'
  import { CourserPackageSyncSchemeRequest } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
  import CoursePackageSyncSchemeInfo from '@api/service/management/resource/course-package/query/vo/CoursePackageSyncSchemeInfo'
  import CoursePackageSyncLog from '@api/service/management/resource/course-package/query/vo/CoursePackageSyncLog'
  import BizTrainingCategorySelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-category-select.vue'
  @Component({
    components: { BizTrainingCategorySelect }
  })
  export default class extends Vue {
    industryPropertyId = '11'
    categoryCode = '22'
    propertyId = '33'
    uiPublic = {
      year: '',
      industry: '',
      professional: '',
      accounttype: '',
      trainingtype: '',
      major: '',
      region: '',
      qgRegion: '',
      category: Array<string>()
    }
    id = ''
    loading = false
    // 查询参数
    page: UiPage
    courserPackageSyncSchemeRequest: CourserPackageSyncSchemeRequest = new CourserPackageSyncSchemeRequest()
    asyncCoursepackageList: Array<CoursePackageSyncSchemeInfo> = new Array<CoursePackageSyncSchemeInfo>()
    syncLogDetailDialog = false
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    totalSize = 0
    logDetailList = new Array<CoursePackageSyncLog>()
    summaryList = {}

    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }

    async doQueryPage() {
      this.loading = true
      try {
        this.courserPackageSyncSchemeRequest.coursePackageId = this.id
        this.asyncCoursepackageList =
          await ResourceModule.coursePackageFactory.queryCoursePackage.queryCoursePackageSyncList(
            this.page,
            this.courserPackageSyncSchemeRequest
          )
      } catch (e) {
        console.log(e, '获取查询数据失败')
      } finally {
        this.totalSize = this.page.totalSize
        this.loading = false
      }
    }
    clickQuery() {
      this.page.pageNo = 1
      this.doQueryPage()
    }
    // 重置按钮
    resetParam() {
      this.page.pageNo = 1
      this.courserPackageSyncSchemeRequest = new CourserPackageSyncSchemeRequest()
      this.doQueryPage()
    }
    async created() {
      this.id = this.$route.params.id
      await this.doQueryPage()
      await this.getSyncSummary()
    }
    async synchronizationLog(id: string) {
      try {
        this.logDetailList = await ResourceModule.coursePackageFactory.queryCoursePackage.queryCoursePackageSyncLog(id)
      } catch (e) {
        console.log(e, '同步日志获取失败')
      } finally {
        this.syncLogDetailDialog = true
      }
    }
    async getSyncSummary() {
      try {
        this.summaryList = await ResourceModule.coursePackageFactory.queryCoursePackage.queryCoursePackageSyncSummary(
          this.courserPackageSyncSchemeRequest
        )
      } catch (e) {
        console.log(e, '获取统计数据失败')
      }
    }
    /*
     *查看方案
     */

    viewSchene(item: CoursePackageSyncSchemeInfo) {
      let url = ''
      url = '/training/scheme/manage'
      this.$router.push({
        path: url,
        query: { schemeName: item.name }
      })
    }
  }
</script>
