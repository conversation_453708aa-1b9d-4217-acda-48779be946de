import PeriodManageParam from '@api/service/management/implement/models/PeriodManageParam'
import { Page } from '@hbfe/common'
import PeriodProcess from '@api/service/management/implement/models/PeriodProcess'
import MsSchemeLearning, {
  IssueStudyConfigResponse,
  SchemeIssueRegistrationResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { getSchemeIssueRuleConfigInServicer } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage/graphql-importer'

/**
 * 期别管理
 */
export default class PeriodManage {
  /**
   * 方案id
   */
  private schemeId: string = undefined

  /**
   * 筛选参数
   */
  params: PeriodManageParam = new PeriodManageParam()

  /**
   * 期别列表
   */
  list: Array<PeriodProcess> = new Array<PeriodProcess>()

  /**
   * @param schemeId 方案id
   */
  constructor(schemeId: string) {
    this.schemeId = schemeId
  }

  /**
   * 查询期别列表
   * @param page 分页
   */
  async queryList(page: Page) {
    const res = await MsSchemeLearning.pageStudentSchemeIssueRegistrationInServicer({
      page: page,
      schemeId: this.schemeId,
      issueName: this.params.name || undefined,
      issueNum: this.params.no || undefined
    })

    if (res?.data?.currentPageData?.length) {
      // 需要聚合期别配置
      const periodIds = res.data.currentPageData.map((item: SchemeIssueRegistrationResponse) => {
        if (item?.issueConfig?.issueId) {
          return item.issueConfig.issueId
        }
      })
      // 去重
      const uniqueIds = [...new Set(periodIds)]

      const reWriteGQL = new RewriteGraph<IssueStudyConfigResponse, string>(
        MsSchemeLearning._commonQuery,
        getSchemeIssueRuleConfigInServicer
      )
      await reWriteGQL.request(uniqueIds)

      this.list = res.data.currentPageData.map((item: SchemeIssueRegistrationResponse) => {
        return PeriodProcess.from(item, reWriteGQL.itemMap)
      })
    } else {
      this.list = new Array<PeriodProcess>()
    }

    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize

    return
  }
}
