<template>
  <div>
    <el-drawer
      title="查看地区管理员"
      :visible.sync="isShowDetailDrawer"
      size="600px"
      custom-class="m-drawer"
      :before-close="close"
    >
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form ref="form" :model="regionAdministrator" label-width="auto" class="m-text-form is-column f-mt20">
              <el-form-item label="帐号：">{{ regionAdministrator.account }}</el-form-item>
              <el-form-item label="姓名：">{{ regionAdministrator.name }}</el-form-item>
              <el-form-item label="管辖地区：">{{ regionAdministrator.regionName }}</el-form-item>
              <el-form-item label="手机号：">{{ regionAdministrator.phone }}</el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import RegionAdministrator from '@api/service/management/authority/region-administrator/RegionAdministratorItem'
  import { cloneDeep } from 'lodash'
  @Component
  export default class extends Vue {
    isShowDetailDrawer = false
    regionAdministrator = new RegionAdministrator()

    openGrawer(item: RegionAdministrator) {
      this.regionAdministrator = cloneDeep(item)
      this.isShowDetailDrawer = true
    }
    close() {
      this.isShowDetailDrawer = false
      this.regionAdministrator = new RegionAdministrator()
    }
  }
</script>
