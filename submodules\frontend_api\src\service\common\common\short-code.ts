import MsShortcodeV1, { ApplyShortLinkRequest, ShortCodeResponse } from '@api/ms-gateway/ms-shortcode-v1'

class ShortCode {
  /**
   * 获取短码信息
   * @param code
   */
  async getShortCode(code: string) {
    return await MsShortcodeV1.getShortCode(code)
  }

  /**
   * 申请短链
   * @param params
   */
  async applyShortLink(params: ApplyShortLinkRequest) {
    return await MsShortcodeV1.applyShortLink(params)
  }

  /**
   * 根据短码获取重定向的分销门户url
   * @param code
   */
  async getUrlByShortCode(code: string) {
    return await MsShortcodeV1.getUrlByShortCode(code)
  }
}

export default new ShortCode()
