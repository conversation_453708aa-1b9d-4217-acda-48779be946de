import exportMsgateway, {
  TradeReportRequest as TradeReportReq
} from '@api/platform-gateway/jxjy-data-export-gateway-backstage'
import {
  CommoditySkuRequest,
  default as MsTradeQueryFrontGatewayCourseLearningBackstage,
  SchemeResourceResponse,
  TradeReportRequest,
  default as tradeMsGateway
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { CommodityOpenReportFormResponseVo } from '@api/service/management/statisticalReport/query/vo/CommodityOpenReportFormResponseVo'
import { ReportSummaryResponse } from '@api/service/management/statisticalReport/query/vo/ReportSummaryResponse'
import ConfigJsonUtil from '@api/service/management/train-class/Utils/ConfigJsonUtil'
import { SkuPropertyConvertUtils } from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import TrainClassConfigJsonManager from '@api/service/management/train-class/Utils/TrainClassConfigJsonManager'
import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
import { Page } from '@hbfe/common'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
export class QueryTrainClassReportList {
  statisticsM = new ReportSummaryResponse()

  /**
   * 批量查询培训方案详情
   * @param commoditySkuIdList 培训方案商品id集合
   * @private
   */
  private async batchQueryTrainClassDetailMap(
    commoditySkuIdList: string[]
  ): Promise<Map<string, TrainClassDetailClassVo>> {
    const result = new Map<string, TrainClassDetailClassVo>()
    const queryRequest = new CommoditySkuRequest()
    queryRequest.commoditySkuIdList = commoditySkuIdList
    const commoditySkuResp = await MsTradeQueryFrontGatewayCourseLearningBackstage.pageCommoditySkuInServicer({
      page: new Page(1, commoditySkuIdList.length),
      queryRequest
    })
    if (
      commoditySkuResp.status?.isSuccess() &&
      commoditySkuResp.data?.currentPageData &&
      commoditySkuResp.data?.currentPageData?.length
    ) {
      const metadata = commoditySkuResp.data.currentPageData
      const list: TrainClassDetailClassVo[] = metadata.map((item) => {
        const option = new TrainClassDetailClassVo()
        // 转换后端字段为前端状态层字段
        option.trainClassBaseInfo.skuProperty = SkuPropertyConvertUtils.convertSkuValueNameProperty(item.skuProperty)
        option.trainClassBaseInfo.picture = item.commodityBasicData.commodityPicturePath
        option.price = item.commodityBasicData.price
        option.saleTitle = item.commodityBasicData.saleTitle
        option.trainClassBaseInfo.name = item.commodityBasicData.saleTitle
        option.trainClassBaseInfo.id = (item.resource as SchemeResourceResponse).schemeId
        option.commoditySkuId = item.commoditySkuId
        return option
      })
      list.forEach((item) => {
        result.set(item.commoditySkuId, item)
      })
    }
    return result
  }

  /**
   * 填充方案信息
   * @description 没啥用，主要是与原来转换对齐
   * @param trainClassDetail
   * @param configJson
   * @private
   */
  private addTrainClass(trainClassDetail: TrainClassDetailClassVo, configJson: Record<string, any>) {
    //   console.log('获取到的配置jsonstring=', commodityDetail.schemeConfig)
    const configStr = JSON.stringify(configJson)
    const classConfigJson: any = TrainClassConfigJsonManager.calculatorSchemeConfigJson(configStr)
    trainClassDetail.trainClassBaseInfo.provideRelearn = classConfigJson.provideRelearn
    trainClassDetail.trainClassBaseInfo.registerBeginDate = classConfigJson.registerBeginDate
    trainClassDetail.trainClassBaseInfo.registerEndDate = classConfigJson.registerEndDate
    trainClassDetail.trainClassBaseInfo.trainingBeginDate = classConfigJson.trainingBeginDate
    trainClassDetail.trainClassBaseInfo.trainingEndDate = classConfigJson.trainingEndDate
    //获取培训成果中学分
    const creditLearningResult =
      classConfigJson.assessSetting.learningResults?.find((learnResult: any) => learnResult.type == 1) || {}
    trainClassDetail.trainClassBaseInfo.period = creditLearningResult.grade || 0
    trainClassDetail.trainClassBaseInfo.assessSettingId = classConfigJson.assessSetting.id
    trainClassDetail.trainClassBaseInfo.assessSettingName = classConfigJson.assessSetting.name
    trainClassDetail.trainClassBaseInfo.creditId = creditLearningResult.id
    trainClassDetail.taxCode = classConfigJson.commoditySale.taxCode
    trainClassDetail.categoryId = classConfigJson.commoditySale.categoryId
    trainClassDetail.offShelvesPlanTime = classConfigJson.commoditySale.onOrOffShelvesPlan.offShelvePlanTime
    trainClassDetail.onShelvesPlanTime = classConfigJson.commoditySale.onOrOffShelvesPlan.onShelvePlanTime
    trainClassDetail.onShelves = classConfigJson.commoditySale.onOrOffShelvesPlan.onShelve
    trainClassDetail.visibleChannelList = classConfigJson.commoditySale.visibleChannelList
    //配置培训模板
    const templateResult =
      classConfigJson.assessSetting.learningResults?.find((learnResult: any) => learnResult.type == 2) || {}
    if (templateResult) {
      trainClassDetail.trainClassBaseInfo.hasLearningResult = true
      trainClassDetail.trainClassBaseInfo.learningResultId = templateResult.certificateTemplateId
      trainClassDetail.trainClassBaseInfo.learningResultAchievementsId = templateResult.id

      trainClassDetail.trainClassBaseInfo.openPrintTemplate = templateResult.openPrintTemplate
    }
    trainClassDetail.trainClassBaseInfo.schemeType = SchemeTypeEnum[classConfigJson.type as string]
    trainClassDetail.learningTypeModel = TrainClassConfigJsonManager.jsonConfigConvertToLearningType(
      configStr,
      trainClassDetail.trainClassBaseInfo.schemeType
    )
    //   console.log('this.trainClassDetail.learningTypeModel', this.trainClassDetail.learningTypeModel)
  }

  /**
   * 获取商品开通统计列表
   */

  async pageCommodityOpenReportFormsInServicer(
    page: Page,
    filter: TradeReportRequest,
    vendorId = '',
    isPopularize = false
  ): Promise<Array<CommodityOpenReportFormResponseVo>> {
    try {
      console.log('page参数=', page, 'filter参数=', filter)
      const res = await tradeMsGateway.pageCommodityOpenReportFormsInServicer({
        page,
        request: filter
      })
      page.totalSize = res.data.totalSize
      page.totalPageSize = res.data.totalPageSize
      const tmpArr: CommodityOpenReportFormResponseVo[] = []

      if (res.status.isSuccess()) {
        for (const tmpArrElement of res.data.currentPageData) {
          const reportVo = new CommodityOpenReportFormResponseVo()
          Object.assign(reportVo, tmpArrElement)
          await reportVo.fillData()
          // await reportVo.addTrainClass()
          tmpArr.push(reportVo)
        }
        // 构建培训方案商品id集合
        const commoditySkuIdList = [...new Set(tmpArr.map((item) => item.commoditySkuId)?.filter(Boolean))]
        if (commoditySkuIdList.length) {
          const res = await this.batchQueryTrainClassDetailMap(commoditySkuIdList)
          tmpArr.forEach((item) => {
            item.trainClassDetail = res.get(item.commoditySkuId) || new TrainClassDetailClassVo()
          })
        }
        // 构建方案id集合
        const schemeIdList = [
          ...new Set(tmpArr.map((item) => item.trainClassDetail.trainClassBaseInfo?.id).filter(Boolean))
        ]
        if (schemeIdList.length) {
          const queryM = new ConfigJsonUtil()
          const res = await queryM.batchQuerySchemeJsonConfigMapBySchemeId(schemeIdList)
          tmpArr.forEach((item) => {
            const configJson = res.get(item.trainClassDetail.trainClassBaseInfo?.id)
            const trainClassDetail = item.trainClassDetail
            if (configJson) {
              this.addTrainClass(trainClassDetail, configJson)
            }
          })
        }
        // 获取方案统计信息
        this.getCommodityReportSummaryInServicer(filter)
      }

      console.log('调用了pageCommodityOpenReportFormsInServicer方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryTrainClassReportList.ts所处方法，pageCommodityOpenReportFormsInServicer',
        e
      )
    }
  }
  /**
   * 导出
   */

  async exportExcel(param: TradeReportReq) {
    try {
      console.log('param参数=', param)
      param.containsCollective = true
      const res = await exportMsgateway.exportCommodityOpenReportFormsInServicer(param)
      console.log('调用了exportExcel方法，返回值=', res)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryTrainClassReportList.ts所处方法，exportExcel',
        e
      )
    }
  }
  /**
   * 获取报表统计合计数据
   */

  async getCommodityReportSummaryInServicer(filter: TradeReportRequest) {
    try {
      console.log('filter参数=', filter)
      const res = await tradeMsGateway.getCommodityReportSummaryInServicer(filter)

      if (res.status.isSuccess()) {
        res.data.purchaseChannelStatisticInfoList
          .sort((a, b) => a.purchaseChannel - b.purchaseChannel)
          .forEach((item) => {
            item.paymentTypeStatisticInfoList.sort((a, b) => b.paymentType - a.paymentType)
          })
        Object.assign(this.statisticsM, res.data)
        await this.statisticsM.fillData()
      }
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryTrainClassReportList.ts所处方法，getCommodityReportSummaryInServicer',
        e
      )
    }
  }
}
