import store from '@/store'
import { getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import MutationChooseCourseFactory from '@api/service/customer/learning/choose-course/MutationChooseCourseFactory'
import SceneFactory from '@api/service/customer/learning/scene/SceneFactory'
import UserLearningCourse from '@api/service/customer/learning/course/UserLearningCourse'
import UserTrialViewCourse from '@api/service/customer/learning/course/UserTrialViewCourse'
import UserPreviewCourse from '@api/service/customer/learning/course/UserPreviewCourse'

/**
 * @description 课程学习中控层
 */
@Module({
  name: 'LearningModule',
  dynamic: true,
  namespaced: true,
  store
})
class LearningModule extends VuexModule {
  /**
   * 选课业务工厂
   */
  mutationChooseCourseFactory: MutationChooseCourseFactory = new MutationChooseCourseFactory()
  /**
   * 场景工厂
   */
  sceneFactory: SceneFactory = new SceneFactory()

  userLearningCourse: UserLearningCourse = new UserLearningCourse()

  @Mutation
  rebuildUserLearningCourse() {
    this.userLearningCourse.destroy()
    this.userLearningCourse = new UserLearningCourse()
  }

  @Mutation
  rebuildUserTrialViewCourse() {
    this.userTrialViewCourse.destroy()
    this.userTrialViewCourse = new UserTrialViewCourse()
  }

  userTrialViewCourse: UserTrialViewCourse = new UserTrialViewCourse()

  @Mutation
  rebuildUserPreviewCourse() {
    this.userPreviewCourse.destroy()
    this.userPreviewCourse = new UserPreviewCourse()
  }

  userPreviewCourse: UserPreviewCourse = new UserPreviewCourse()
}

export default getModule(LearningModule)
