<template>
  <el-main>
    <el-card shadow="never" class="m-card is-bg is-overflow-hidden">
      <div class="f-plr20 f-pt20">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="姓名">
                <el-input v-model="input" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="证件号">
                <el-input v-model="input" clearable placeholder="请输入证件号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="手机号">
                <el-input v-model="input" clearable placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="订单号">
                <el-input v-model="input" clearable placeholder="请输入订单号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="4" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--人员信息-->
        <el-collapse v-model="activeNames5" @change="handleChange" class="m-collapse no-border">
          <el-collapse-item name="1">
            <div slot="title" class="m-tit">
              <span class="tit-txt">人员信息</span>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="240" highlight-current-row class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="姓名" min-width="160" fixed="left">
                <template>张依依</template>
              </el-table-column>
              <el-table-column label="证件号" min-width="200">
                <template>354875965412365896</template>
              </el-table-column>
              <el-table-column label="手机号" min-width="200">
                <template>13003831002</template>
              </el-table-column>
              <el-table-column label="单位地区" min-width="200">
                <template>福建省-福州市-鼓楼区</template>
              </el-table-column>
              <el-table-column label="注册时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-border-top is-sticky">
      <el-tab-pane label="学员信息" name="first">
        <div class="f-p15">详见 2601_客服管理_业务咨询_学员信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="学习内容" name="second">
        <div class="f-p15">详见 2602_客服管理_业务咨询_学习内容.vue</div>
      </el-tab-pane>
      <el-tab-pane label="换班信息" name="third">
        <div class="f-p15">详见 2603_客服管理_业务咨询_换班信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="订单信息" name="fourth">
        <div class="f-p15">
          <el-card shadow="never" class="m-card f-mb15">
            <el-row :gutter="16" class="m-query">
              <el-form :inline="true" label-width="auto">
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="订单号">
                    <el-input v-model="input" clearable placeholder="请输入订单号" />
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="订单状态">
                    <el-select v-model="select" clearable filterable placeholder="请选择">
                      <el-option value="选项1"></el-option>
                      <el-option value="选项2"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6">
                  <el-form-item label="创建时间">
                    <el-date-picker
                      v-model="form.date1"
                      type="datetimerange"
                      range-separator="至"
                      start-placeholder="起始时间"
                      end-placeholder="结束时间"
                    >
                    </el-date-picker>
                  </el-form-item>
                </el-col>
                <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                  <el-form-item class="f-tr">
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="订单号" min-width="240">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    210906155454748958730082
                    <p>
                      <el-tag type="danger" class="f-mr10">换班</el-tag>
                      <el-tag type="warning" class="f-mr10">换期</el-tag>
                      <el-tag class="f-mr10">集体报名</el-tag>
                    </p>
                  </div>
                  <div v-else-if="scope.$index === 1">
                    210906155454748958730082
                    <p>
                      <el-tag type="warning" class="f-mr10">分销推广：分销商A</el-tag>
                      <el-tag type="success" class="f-mr10">个人报名</el-tag>
                    </p>
                  </div>
                  <div v-else-if="scope.$index === 2">
                    210906155454748958730082
                    <p>
                      <el-tag type="warning" class="f-mr10">个人报名</el-tag>
                      <el-tag type="success" size="small" class="f-mr10">专题</el-tag>
                    </p>
                  </div>
                  <div v-else>
                    210906155454748958730082
                    <p>
                      <el-tag class="f-mr10">集体报名</el-tag>
                    </p>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="购买内容" min-width="240">
                <template>
                  <p>方案名称方案名称方案名称方案名称方案名称方案名称</p>
                  <p><el-tag type="info" size="mini">培训期别</el-tag>2023年xx专业培训（第一期）</p>
                </template>
              </el-table-column>
              <el-table-column label="购买人信息" min-width="240">
                <template>
                  <p>姓名：张依依</p>
                  <p>证件号：354875965412365896</p>
                  <p>手机号：15847412365</p>
                </template>
              </el-table-column>
              <el-table-column label="购买学时" min-width="120" align="center">
                <template>20</template>
              </el-table-column>
              <el-table-column label="缴费方式" min-width="240">
                <template>在线报名-WEB端-支付宝</template>
              </el-table-column>
              <el-table-column label="创建时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column label="实付金额(元)" min-width="140" align="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">3.15</div>
                  <div v-else-if="scope.$index === 1">52.36</div>
                  <div v-else>158.15</div>
                </template>
              </el-table-column>
              <el-table-column label="交易状态" min-width="120">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="primary" class="badge-status">交易失败</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">交易成功</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="操作" min-width="200" align="center" fixed="right">
                <template>
                  <el-button type="text">发起退货/款</el-button>
                  <el-button type="text">详情</el-button>
                  <el-button type="text">补要发票</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-tab-pane>
      <el-tab-pane label="发票信息" name="five">
        <div class="f-p15">详见 2605_客服管理_业务咨询_发票信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="退款信息" name="six">
        <div class="f-p15">详见 2606_客服管理_业务咨询_退款信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="学习档案" name="seven">
        <div class="f-p15">详见 2607_客服管理_业务咨询_培训档案.vue</div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames5: ['1'],
        activeName: 'fourth',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
