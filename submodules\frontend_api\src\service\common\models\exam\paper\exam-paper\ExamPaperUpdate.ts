import { ExamRandomFetchWay, PaperTimeType } from '@api/service/common/models/exam/enums'
import LibraryWay from '@api/service/common/models/exam/paper/exam-paper/fetch-way/LibraryWay'
import Random from '@api/service/common/models/exam/paper/section/configuration/random/Random'

class ExamPaperUpdate {
  /**
   * id
   */
  id: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷分类id
   */
  paperTypeId: string

  /**
   * 试卷总分
   */
  totalScore: number

  /**
   * 试卷及格分
   */
  passScore: number

  /**
   * 试卷总提数
   */
  totalQuestionCount: number

  /**
   * 计时方式
   *
   * @see PaperTimeType
   */
  timeType: PaperTimeType = 0

  /**
   * 建议考试时长，分钟
   */
  timeLength: number

  /**
   * 描述
   */
  description: string

  /**
   * 是否草稿
   */
  draft: boolean

  /**
   * 是否启用
   */
  enabled: boolean

  /**
   * 随机卷抽题类型
   * @see ExamRandomFetchWay
   */
  fetchWay: ExamRandomFetchWay = 0

  /**
   * 题库方式
   */
  libraryWay: LibraryWay

  /**
   * 随机抽题配置
   */
  random: Random
}

export default ExamPaperUpdate
