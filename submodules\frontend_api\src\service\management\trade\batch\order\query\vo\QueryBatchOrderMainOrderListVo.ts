import { BatchOrderMainOrderAfterSaleStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderMainOrderAfterSaleStatus'
import {
  OrderBasicDataRequest,
  OrderInfoRequest,
  OrderRequest,
  ReturnOrderBasicDataRequest,
  ReturnOrderRequest,
  SubOrderInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
import UserModule from '@api/service/management/user/UserModule'
import DataResolve from '@api/service/common/utils/DataResolve'

/**
 * @description 【集体报名订单】批次单主单列表查询参数
 */
class QueryBatchOrderMainOrderListVo {
  /**
   * 批次单id
   */
  batchOrderNo = ''

  /**
   * 主单id
   */
  mainOrderNo = ''

  /**
   * 身份证号
   */
  buyerAccount = ''

  /**
   * 学员姓名
   */
  buyerName = ''

  /**
   * 售后状态
   */
  afterSaleStatus: BatchOrderMainOrderAfterSaleStatusEnum = null

  /**
   * 转换为个人订单查询条件
   */
  async toQuerySignUpParams(): Promise<OrderRequest> {
    const to = new OrderRequest()
    to.orderBasicData = new OrderBasicDataRequest()
    to.orderBasicData.batchOrderNoList = this.batchOrderNo ? [this.batchOrderNo] : undefined
    to.orderNoList = this.mainOrderNo ? [this.mainOrderNo] : undefined
    to.buyerIdList = await this.setBuyerIdList()
    // 查询等待付款（待付款）、支付中、开通中、交易成功、交易关闭的主单
    to.orderBasicData.orderStatusList = [1, 2, 3]
    to.orderBasicData.orderPaymentStatusList = [0, 1, 2]
    // 售后状态
    if (this.afterSaleStatus) {
      // 未退款
      if (this.afterSaleStatus === BatchOrderMainOrderAfterSaleStatusEnum.Wait_For_Refund) {
        to.subOrderReturnStatus = [0]
      }
      // 退款中
      else if (this.afterSaleStatus === BatchOrderMainOrderAfterSaleStatusEnum.Refunding) {
        to.subOrderReturnStatus = [1, 2, 4]
      }
      // 退款成功
      else if (this.afterSaleStatus === BatchOrderMainOrderAfterSaleStatusEnum.Success_Refund) {
        to.subOrderReturnStatus = [3, 5]
      } else {
        to.subOrderReturnStatus = undefined
      }
    } else {
      to.subOrderReturnStatus = undefined
    }
    return to
  }

  /**
   * 转换为个人退款查询条件
   */
  async toQueryRefundParams(): Promise<ReturnOrderRequest> {
    const to = new ReturnOrderRequest()
    // 查询批次单id
    to.subOrderInfo = new SubOrderInfoRequest()
    to.subOrderInfo.orderInfo = new OrderInfoRequest()
    to.subOrderInfo.orderInfo.batchOrderNoList = this.batchOrderNo ? [this.batchOrderNo] : undefined
    // 查询主单id
    to.subOrderInfo.orderInfo.orderNoList = this.mainOrderNo ? [this.mainOrderNo] : undefined
    // 查询买家id
    to.subOrderInfo.orderInfo.buyerIdList = await this.setBuyerIdList()
    to.basicData = new ReturnOrderBasicDataRequest()
    // 售后状态
    if (this.afterSaleStatus) {
      // 未退款
      if (this.afterSaleStatus === BatchOrderMainOrderAfterSaleStatusEnum.Wait_For_Refund) {
        to.basicData.returnOrderStatus = [7, 11]
      }
      // 退款中
      else if (this.afterSaleStatus === BatchOrderMainOrderAfterSaleStatusEnum.Refunding) {
        to.basicData.returnOrderStatus = [0, 1, 2, 3, 4, 5, 6]
      }
      // 退款成功
      else if (this.afterSaleStatus === BatchOrderMainOrderAfterSaleStatusEnum.Success_Refund) {
        to.basicData.returnOrderStatus = [8, 9, 10]
      } else {
        to.basicData.returnOrderStatus = undefined
      }
    } else {
      to.basicData.returnOrderStatus = undefined
    }
    return to
  }

  /**
   * 设置买家id集合
   */
  async setBuyerIdList(): Promise<string[]> {
    if (!this.buyerAccount && !this.buyerName) return undefined
    const queryRemote: QueryStudentList = UserModule.queryUserFactory.queryStudentList
    queryRemote.queryStudentIdParams.idCard = this.buyerAccount ?? undefined
    queryRemote.queryStudentIdParams.userName = this.buyerName ?? undefined
    const response = await queryRemote.queryStudentIdList()
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data)) {
      return response.data
    }
  }
}

export default QueryBatchOrderMainOrderListVo
