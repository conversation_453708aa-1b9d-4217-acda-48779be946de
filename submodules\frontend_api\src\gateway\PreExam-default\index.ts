import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PreExam-default'

// 枚举
export enum ScaleType {
  CUSTOM = 'CUSTOM',
  SATISFACTION = 'SATISFACTION',
  RECOGNITION = 'RECOGNITION',
  IMPORTANCE = 'IMPORTANCE',
  WILLING = 'WILLING',
  CONFORMITY = 'CONFORMITY'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class FixedQuestion {
  questionId?: string
  answerConfiguration?: QuestionAnswerConfiguration
  score: number
}

export class QuestionAnswerConfiguration {
  required: boolean
}

export class Fixed {
  configurationItems?: Array<FixedConfigurationItem>
  configType?: number
}

export class Group {
  configurationItems?: Array<GroupConfigurationItem>
  configType?: number
}

export class Random {
  configurationItems?: Array<RandomConfigurationItem>
  randomType: number
  recursive: boolean
  randomWay: number
  ratio: boolean
  takeObjectConfigurationItems?: Array<RandomTakeObjectConfigurationItem>
  configType?: number
}

export class FixedConfigurationItem {
  totalScore: number
  questions?: Array<FixedQuestion>
  id?: string
  name?: string
  count: number
  type: number
  scoreWay: number
  singleAnswerableTimeLength: number
}

export class GroupConfigurationItem {
  key?: string
  fixedConfigurationItems?: Array<FixedConfigurationItem>
}

export class RandomConfigurationItem {
  totalScore: number
  ratio: boolean
  takeObjectConfigurationItems?: Array<RandomTakeObjectConfigurationItem>
  id?: string
  name?: string
  count: number
  type: number
  scoreWay: number
  singleAnswerableTimeLength: number
}

export class RandomTakeObjectConfigurationItem {
  objectId?: string
  type?: string
  ratioValue: number
  tags?: Array<string>
  extractCount: number
}

export class TagDTO1 {
  id?: string
  code?: string
  tag?: string
}
/** 
<AUTHOR> update 2021/1/28  TODO
 */
export class PreExamMockExaminationPaperUpdateDTO {
  fixed?: Fixed
  group?: Group
  /**
   * 随机试题配置信息
   */
  random?: Random
  id?: string
  /**
   * 试卷名称
   */
  name?: string
  /**
   * 试卷分类id
   */
  paperTypeId?: string
  /**
   * 配置类型，来至ConfigType
1：固定卷 2：AB卷 3：智能卷
   */
  configType: number
  /**
   * 试卷分数
   */
  totalScore: number
  /**
   * 建议及格分数
   */
  passScore: number
  /**
   * 试卷总题数
   */
  totalQuestionCount: number
  /**
   * 计时方式
@see PaperTimeType
   */
  timeType: number
  /**
   * 建议考试时长
   */
  timeLength: number
  /**
   * 描述
   */
  description?: string
  /**
   * 是否草稿
   */
  draft: boolean
  /**
   * 是否启用
   */
  enabled: boolean
}

export class PreExamQuestionParamDTO {
  questionCategory?: string
  errorProne?: boolean
  tagIdSearchPattern?: number
  topic?: string
  librarys?: Array<string>
  questionTypes?: Array<number>
  enable: number
  modes?: Array<number>
  beginCreateTime?: string
  endCreateTime?: string
  tagIds?: Array<string>
}

/**
 * 试卷分类创建请求
<AUTHOR> create 2020/4/20 11:42
 */
export class PaperClassificationCreateRequest {
  /**
   * 试卷分类名称
   */
  name?: string
  /**
   * 父试卷分类id
   */
  parentId?: string
  /**
   * 描述
   */
  description?: string
}

/**
 * 试卷分类更新请求
<AUTHOR> create 2020/4/20 11:58
 */
export class PaperClassificationUpdateRequest {
  /**
   * 分类id
   */
  id?: string
  /**
   * 试卷分类名称
   */
  name?: string
  /**
   * 父试卷分类id
   */
  parentId?: string
  /**
   * 描述
   */
  description?: string
}

/**
 * 模拟考试卷创建请求 TODO
<AUTHOR> create 2020/4/20 13:35
<AUTHOR> update 2021/1/28  TODO
 */
export class PreExamMockExaminationPaperCreateRequest {
  /**
   * 试卷名称
   */
  name?: string
  /**
   * 试卷分类id
   */
  paperTypeId?: string
  /**
   * 配置类型，来至ConfigType
1：固定卷 2：AB卷 3：智能卷
   */
  configType: number
  /**
   * 试卷分数
   */
  totalScore: number
  /**
   * 建议及格分数
   */
  passScore: number
  /**
   * 试卷总题数
   */
  totalQuestionCount: number
  /**
   * 计时方式
@see PaperTimeType
   */
  timeType: number
  /**
   * 建议考试时长
   */
  timeLength: number
  /**
   * 描述
   */
  description?: string
  /**
   * 是否草稿
   */
  draft: boolean
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 随机试题配置信息
   */
  random?: RandomRequest
}

/**
 * <AUTHOR> create 2020/4/20 13:39
 */
export class RandomConfigurationItemRequest {
  /**
   * 配置项名称,可能是大题的名称
   */
  name?: string
  /**
   * 试题数量
   */
  count: number
  /**
   * 试题类型，0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
智能卷不支持混合题型
   */
  type: number
  /**
   * 分数分配类型
1：平均分配
2：独立分配
   */
  scoreWay: number
  /**
   * 单题用时, 单位：分
如果试卷的计时方式是[单题计时]，则控制的是大题下每道题可作答的时间长度
@since 1.23.0
   */
  singleAnswerableTimeLength: number
  /**
   * 配置项总分
   */
  totalScore: number
  /**
   * 是否比率
如果随机出题方式按试卷抽取则本项成员无效
   */
  ratio: boolean
  /**
   * 随机抽取试题对象配置
如果随机出题方式按试卷抽取则本项成员无效
   */
  takeObjectConfigurationItems?: Array<RandomTakeObjectConfigurationItemRequest>
}

/**
 * 随机卷请求
<AUTHOR> create 2020/4/20 13:36
 */
export class RandomRequest {
  /**
   * 大题项配置
   */
  configurationItems?: Array<RandomConfigurationItemRequest>
  /**
   * 随机卷类型
   */
  randomType: number
  /**
   * 是否递归取题
如果是题库卷时，才生效。
   */
  recursive: boolean
  /**
   * 随机抽取试卷方式
ExtractionType.EXAMPAPER&#x3D;1：按试卷抽取 ExtractionType.BIGQUESTION&#x3D;2：按配置项（大题）抽取
   */
  randomWay: number
  /**
   * 是否比率
如果随机出题方式按大题抽取则本项成员无效
   */
  ratio: boolean
  /**
   * 随机抽取试题对象配置
如果随机出题方式按大题抽取则本项成员无效
   */
  takeObjectConfigurationItems?: Array<RandomTakeObjectConfigurationItemRequest>
}

/**
 * <AUTHOR> create 2020/4/20 13:38
 */
export class RandomTakeObjectConfigurationItemRequest {
  /**
   * 对象id：当为题库卷时存题库id
当为对象卷时存储试题的对象id（一般为课程id）
   */
  objectId?: string
  /**
   * 对象类型  ，当为对象卷时此项必须赋值
   */
  type?: string
  /**
   * 比率值,最大值不大于100,最小不得小于1
   */
  ratioValue: number
  /**
   * 标签id集合，对应类型为{@link RandomExamPaperType#NEW_TAG}时生效
   */
  tags?: Array<string>
  /**
   * 题库卷按具体数量抽题
@see RandomExamPaper#ratio 为false生效
   */
  extractCount: number
}

/**
 * <AUTHOR> create 2020/4/20 13:54
 */
export class CorrectionAnswerInitRequest {
  /**
   * 练习卷id
   */
  paperId: string
  /**
   * 错题类型 -1 全部，1 从未答对 2 有答对
   */
  wrongType: number
  /**
   * 至少答对次数> ，错题类型 &#x3D; 2是 有效
   */
  correctTimesAtLeast: number
  /**
   * 抽题方式，1：指定考纲方式，2：按试题题型及抽题比例及试题数,3：固定题型
   */
  fetchWay: number
  /**
   * 考纲id抽题方式 &#x3D; 1：指定考纲方式 生效
   */
  examOutlineId?: string
  /**
   * 题型，抽题方式 &#x3D; 3：固定题型 生效
   */
  questionType: number
  /**
   * 参与抽题的标签列表
   */
  tagIds?: Array<string>
  /**
   * 总题数
   */
  totalQuestionSize: number
}

/**
 * <AUTHOR> create 2020/4/20 13:53
 */
export class FavoriteAnswerInitRequest {
  /**
   * 练习卷id
   */
  paperId: string
  /**
   * 抽题方式，1：指定考纲方式，2：按试题题型及抽题比例及试题数,3：固定题型
   */
  fetchWay: number
  /**
   * 考纲id抽题方式 &#x3D; 1：指定考纲方式 生效
   */
  examOutlineId?: string
  /**
   * 题型，抽题方式 &#x3D; 3：固定题型 生效
   */
  questionType: number
  /**
   * 参与抽题的标签列表
   */
  tagIds?: Array<string>
  /**
   * 总题数
   */
  totalQuestionSize: number
}

/**
 * <AUTHOR> create 2020/4/20 15:21
 */
export class PreExamMockExaminationPaperParamRequest {
  /**
   * 试卷名称
   */
  name?: string
  /**
   * 试卷分类id
   */
  examTypeIds?: Array<string>
  /**
   * 配置类型， -1:不查 1:固定卷 2:AB卷 3:智能卷 默认 -1
   */
  configType: number
  /**
   * 是否可用 -1/0/1 不查/可用/不可用
   */
  enable: number
  /**
   * 是否草稿  -1:不查  0:是 1:否
   */
  draft?: number
  /**
   * 创建时间起
   */
  beginCreateTime?: string
  /**
   * 创建时间止
   */
  endCreateTime?: string
}

/**
 * <AUTHOR> create 2020/4/20 17:01
 */
export class BlankFillingRequest {
  /**
   * 答案数量
   */
  answerCount: number
  /**
   * 当填空题类型为 精确匹配时，最外层集合为答案组（也就是每一组都是一道填空题的答案，满足当中的任意一组表示回答正确）第二层集合为空
当填空题类似为 每空多答案时，最外层的集合为每个空的答案，第二层集合为每个空的备选答案
   */
  answersGroup?: Array<string>
  /**
   * 答案项分值
当填空题类型为 精确匹配时此项值无效
   */
  answersItemScore?: Array<number>
  /**
   * 答案类型
@see BlankFillingAnswerType
   */
  answerType: number
  /**
   * 答案是否有顺序.当{@link #answerType } &#x3D; {@link BlankFillingAnswerType#MULTIPLE_PER_BLANK} 时，
即每空多答案的情况下，答案是否是按照填空顺序排列。
   */
  sequence: boolean
  /**
   * 评分标准
   */
  standard?: string
}

/**
 * <AUTHOR> create 2020/4/20 16:58
 */
export class ChoiceItemRequest {
  /**
   * 选项ID
   */
  id?: string
  /**
   * 选项内容
   */
  content?: string
}

/**
 * <AUTHOR> create 2020/4/20 17:06
 */
export class ComprehensiveChildQuestionRequest {
  /**
   * 子试题id
   */
  questionId?: string
  /**
   * 题目
   */
  title?: string
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 判断题
   */
  judgement?: JudgementRequest
  /**
   * 单选题
   */
  singleChoice?: SingleChoiceRequest
  /**
   * 多选
   */
  multipleChoice?: MultipleChoiceRequest
  /**
   * 填空
   */
  blankFilling?: BlankFillingRequest
  /**
   * 问答题
   */
  essay?: EssayRequest
  /**
   * 量表题
   */
  scale?: ScaleRequest
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficultyValue: number
  /**
   * 试题解析
   */
  description?: string
}

/**
 * <AUTHOR> create 2020/4/20 17:05
 */
export class ComprehensiveRequest {
  /**
   * 子题
   */
  children?: Array<ComprehensiveChildQuestionRequest>
}

/**
 * <AUTHOR> create 2020/4/20 17:04
 */
export class EssayRequest {
  /**
   * 参考答案
   */
  referenceAnswer?: string
  /**
   * 评分标准
   */
  standard?: string
  /**
   * 是否限制作答长度
   */
  limitAnswerLength: boolean
  /**
   * 允许作答的文本字符最少长度
   */
  permitAnswerLengthMin: number
  /**
   * 允许作答的文本字符最大长度
   */
  permitAnswerLengthMax: number
}

/**
 * <AUTHOR> create 2020/4/20 16:57
 */
export class JudgementRequest {
  /**
   * 正确答案
   */
  correctAnswer: boolean
  /**
   * 正确文本
   */
  correctText?: string
  /**
   * 错误文本
   */
  incorrectText?: string
}

/**
 * <AUTHOR> create 2020/4/20 17:00
 */
export class MultipleChoiceRequest {
  /**
   * 选项
   */
  choiceItems?: Array<ChoiceItemRequest>
  /**
   * 正确答案
   */
  correctAnswers?: Array<string>
}

/**
 * <AUTHOR> create 2020/4/20 17:19
 */
export class PreExamFavoriteQuestionRequest {
  /**
   * 试题id
   */
  questionId?: string
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 学习方式id
   */
  issueId?: string
}

/**
 * <AUTHOR> create 2020/4/20 17:09
 */
export class PreExamQuestionCreateRequest {
  /**
   * 试题应用类型
@see QuestionApplyType
   */
  applyTypes?: Array<number>
  /**
   * 题库ID
   */
  libraryId?: string
  /**
   * 题目
   */
  title?: string
  /**
   * 判断题
   */
  judgement?: JudgementRequest
  /**
   * 单选题
   */
  singleChoice?: SingleChoiceRequest
  /**
   * 多选
   */
  multipleChoice?: MultipleChoiceRequest
  /**
   * 填空
   */
  blankFilling?: BlankFillingRequest
  /**
   * 问答题
   */
  essay?: ScaleRequest
  /**
   * 量表题
   */
  scale?: ScaleRequest
  /**
   * 综合题
   */
  comprehensive?: ComprehensiveRequest
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficulty: number
  /**
   * 试题解析
   */
  description?: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 章节
   */
  chapters?: Array<TagDTO1>
  /**
   * 题类
   */
  questionCategory?: string
  /**
   * 是否易错题
   */
  errorProne: boolean
}

/**
 * <AUTHOR> create 2020/4/20 17:18
 */
export class PreExamQuestionImportRequest {
  /**
   * 导入成功的资源路径
   */
  filePath?: string
}

/**
 * <AUTHOR> create 2020/4/20 17:15
 */
export class PreExamQuestionUpdateRequest {
  /**
   * 试题id
   */
  id?: string
  /**
   * 试题应用类型
@see QuestionApplyType
   */
  applyTypes?: Array<number>
  /**
   * 题库ID
   */
  libraryId?: string
  /**
   * 题目
   */
  title?: string
  /**
   * 判断题
   */
  judgement?: JudgementRequest
  /**
   * 单选题
   */
  singleChoice?: SingleChoiceRequest
  /**
   * 多选
   */
  multipleChoice?: MultipleChoiceRequest
  /**
   * 填空
   */
  blankFilling?: BlankFillingRequest
  /**
   * 问答题
   */
  essay?: EssayRequest
  /**
   * 量表题
   */
  scale?: ScaleRequest
  /**
   * 综合题
   */
  comprehensive?: ComprehensiveRequest
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficulty: number
  /**
   * 试题解析
   */
  description?: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 章节
   */
  chapters?: Array<TagDTO1>
  /**
   * 题类
   */
  questionCategory?: string
  /**
   * 是否易错题
   */
  errorProne: boolean
}

/**
 * <AUTHOR> create 2020/4/20 16:51
 */
export class QuestionLibraryCreateRequest {
  /**
   * 名称
   */
  name?: string
  /**
   * 描述
   */
  description?: string
  /**
   * 父试题库id 如果没有父题库为-1
   */
  parentId?: string
  /**
   * 是否可用
   */
  enabled: boolean
}

/**
 * <AUTHOR> create 2020/4/20 16:52
 */
export class QuestionLibraryUpdateRequest {
  /**
   * 题库id
   */
  id?: string
  /**
   * 题库名称
   */
  name?: string
  /**
   * 描述
   */
  description?: string
  /**
   * 父试题库id 如果没有父题库为-1
   */
  parentId?: string
  /**
   * 是否可用
   */
  enabled: boolean
}

/**
 * <AUTHOR> create 2020/4/20 17:05
 */
export class ScaleRequest {
  /**
   * 量表类型
   */
  scaleType?: ScaleType
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写
   */
  startDegree?: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写
   */
  endDegree?: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
}

/**
 * <AUTHOR> create 2020/4/20 16:58
 */
export class SingleChoiceRequest {
  /**
   * 选项
   */
  choiceItems?: Array<ChoiceItemRequest>
  /**
   * 标准答案
   */
  correctAnswer?: string
}

export class Range {
  key: string
  value: string
}

export class TagDTO {
  id: string
  code: string
  tag: string
}

/**
 * 试卷分类列表响应
<AUTHOR> create 2020/4/20 11:39
 */
export class PaperClassificationListResponse {
  /**
   * 分类id
   */
  id: string
  /**
   * 试卷分类名称
   */
  name: string
  /**
   * 父试卷分类id
   */
  parentId: string
  /**
   * 描述
   */
  description: string
}

/**
 * 试卷分类响应
<AUTHOR> create 2020/4/20 11:43
 */
export class PaperClassificationResponse {
  /**
   * 分类id
   */
  id: string
  /**
   * 试卷分类名称
   */
  name: string
  /**
   * 父试卷分类id
   */
  parentId: string
  /**
   * 描述
   */
  description: string
}

/**
 * 考试答卷信息
<AUTHOR> create 2020/4/20 13:32
 */
export class ExamAnswerPaperResponse {
  /**
   * 答卷id
   */
  id: string
  /**
   * 作答id
   */
  answerId: string
  /**
   * 场次id
   */
  examRoundId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 进入考试时间
   */
  enterTime: string
  /**
   * 是否交卷
   */
  complete: boolean
  /**
   * 是否自动提交
   */
  mandatorySubmission: boolean
  /**
   * 考试的提交时间
   */
  completeTime: string
  /**
   * 是否阅卷完成
   */
  markComplete: boolean
  /**
   * 阅卷完成时间
   */
  markCompleteTime: string
  /**
   * 阅卷人Id
   */
  markUserId: string
  /**
   * 评语
   */
  comment: string
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 考试分数
   */
  score: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 是否及格
   */
  passed: boolean
  /**
   * 总的试题数
   */
  totalCount: number
  /**
   * 总的试题数(包括子题)
   */
  totalQuestionCount: number
  /**
   * 已作答的试题数
   */
  answeredCount: number
  /**
   * 已作答的试题数(包括子题)
   */
  answeredQuestionCount: number
  /**
   * 作答率(包括子题)
   */
  answeredPercentage: number
  /**
   * 作答正确的试题数
   */
  correctCount: number
  /**
   * 作答正确的试题数(包括子题)
   */
  correctQuestionCount: number
  /**
   * 作答正确率(包括子题)
   */
  correctPercentage: number
}

/**
 * <AUTHOR> create 2020/4/20 13:49
 */
export class PreExamMockExaminationPaperItemResponse {
  /**
   * 试卷id
   */
  id: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 配置类型，来至ConfigType
1：固定卷 2：AB卷 3：智能卷
   */
  configType: number
  /**
   * 试卷分类id
   */
  paperTypeId: string
  /**
   * 试卷分数
   */
  totalScore: number
  /**
   * 建议及格分数
   */
  passScore: number
  /**
   * 试卷总题数
   */
  totalQuestionCount: number
  /**
   * 计时方式
@see PaperTimeType
   */
  timeType: number
  /**
   * 建议考试时长
   */
  timeLength: number
  /**
   * 描述
   */
  description: string
  /**
   * 是否草稿
   */
  draft: boolean
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 最后修改时间
   */
  lastChangeTime: string
}

/**
 * <AUTHOR> create 2020/4/20 13:43
 * <AUTHOR> update 2021/1/28  TODO
 */
export class PreExamMockExaminationPaperResponse {
  /**
   * 试卷id
   */
  id: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷分类id
   */
  paperTypeId: string
  /**
   * 配置类型，来至ConfigType
1：固定卷 2：AB卷 3：智能卷
   */
  configType: number
  /**
   * 试卷分数
   */
  totalScore: number
  /**
   * 建议及格分数
   */
  passScore: number
  /**
   * 试卷总题数
   */
  totalQuestionCount: number
  /**
   * 计时方式
@see PaperTimeType
   */
  timeType: number
  /**
   * 建议考试时长
   */
  timeLength: number
  /**
   * 描述
   */
  description: string
  /**
   * 是否草稿
   */
  draft: boolean
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人
   */
  createUserId: string
  /**
   * 最后修改时间
   */
  lastChangeTime: string
  random: RandomResponse
}

/**
 * <AUTHOR> create 2020/4/20 13:39
 */
export class RandomConfigurationItemResponse {
  /**
   * 配置项id
   */
  id: string
  /**
   * 配置项名称,可能是大题的名称
   */
  name: string
  /**
   * 试题数量
   */
  count: number
  /**
   * 试题类型，0/1/2/3/4/5/6/7 未知/判断/单选/多选/填空/简答/父子/混合
智能卷不支持混合题型
   */
  type: number
  /**
   * 分数分配类型
1：平均分配
2：独立分配
   */
  scoreWay: number
  /**
   * 单题用时, 单位：分
如果试卷的计时方式是[单题计时]，则控制的是大题下每道题可作答的时间长度
@since 1.23.0
   */
  singleAnswerableTimeLength: number
  /**
   * 配置项总分
   */
  totalScore: number
  /**
   * 是否比率
如果随机出题方式按试卷抽取则本项成员无效
   */
  ratio: boolean
  /**
   * 随机抽取试题对象配置
如果随机出题方式按试卷抽取则本项成员无效
   */
  takeObjectConfigurationItems: Array<RandomTakeObjectConfigurationItemResponse>
}

/**
 * 随机卷请求
<AUTHOR> create 2020/4/20 13:36
 */
export class RandomResponse {
  /**
   * 配置类型
   */
  configType: number
  /**
   * 大题项配置
   */
  configurationItems: Array<RandomConfigurationItemResponse>
  /**
   * 随机卷类型
   */
  randomType: number
  /**
   * 是否递归取题
如果是题库卷时，才生效。
   */
  recursive: boolean
  /**
   * 随机抽取试卷方式
ExtractionType.EXAMPAPER&#x3D;1：按试卷抽取 ExtractionType.BIGQUESTION&#x3D;2：按配置项（大题）抽取
   */
  randomWay: number
  /**
   * 是否比率
如果随机出题方式按大题抽取则本项成员无效
   */
  ratio: boolean
  /**
   * 随机抽取试题对象配置
如果随机出题方式按大题抽取则本项成员无效
   */
  takeObjectConfigurationItems: Array<RandomTakeObjectConfigurationItemResponse>
}

/**
 * <AUTHOR> create 2020/4/20 13:38
 */
export class RandomTakeObjectConfigurationItemResponse {
  /**
   * 对象id：当为题库卷时存题库id
当为对象卷时存储试题的对象id（一般为课程id）
   */
  objectId: string
  /**
   * 对象类型  ，当为对象卷时此项必须赋值
   */
  type: string
  /**
   * 比率值,最大值不大于100,最小不得小于1
   */
  ratioValue: number
  /**
   * 标签id集合，对应类型为{@link RandomExamPaperType#NEW_TAG}时生效
   */
  tags: Array<string>
  /**
   * 题库卷按具体数量抽题
@see RandomExamPaper#ratio 为false生效
   */
  extractCount: number
}

/**
 * <AUTHOR> create 2020/4/24 8:08
 */
export class PreExamCorrectionPracticeAnswerPaperResponse {
  /**
   * 答卷id
   */
  id: string
  /**
   * 作答id
   */
  answerId: string
  /**
   * 练习卷id
   */
  practicePaperId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 进入时间
   */
  enterTime: string
  /**
   * 是否已完成
   */
  complete: boolean
  /**
   * 完成时间
   */
  completeTime: string
  /**
   * 是否已阅卷完成
   */
  markComplete: boolean
  /**
   * 阅卷完成时间
   */
  markCompleteTime: string
  /**
   * 总题数
   */
  totalNum: number
  /**
   * 已答题数
   */
  answeredNum: number
  /**
   * 对题数
   */
  correctNum: number
  /**
   * 错题数
   */
  failNum: number
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 考试分数
   */
  score: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 抽题方式，1：指定考纲方式，2：按试题题型及抽题比例,3指定题型
   */
  fetchWay: number
  /**
   * 试题类别
   */
  questionCategory: string
  /**
   * 考纲id（答卷所属考纲id）
   */
  examinationOutlineId: string
  /**
   * 题型
   */
  questionType: string
}

/**
 * <AUTHOR> create 2020/4/20 16:46
 */
export class PreExamCorrectionPracticePaperResponse {
  /**
   * 试卷id
   */
  id: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 允许作答的起始时间
   */
  enterStartTime: string
  /**
   * 允许作答的终止时间
   */
  enterEndTime: string
}

/**
 * <AUTHOR> create 2020/4/20 14:01
 */
export class PreExamDailyPracticeAnswerPaperResponse {
  /**
   * 答卷id
   */
  id: string
  /**
   * 作答id
   */
  answerId: string
  /**
   * 练习卷id
   */
  practicePaperId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 进入时间
   */
  enterTime: string
  /**
   * 是否已完成
   */
  complete: boolean
  /**
   * 完成时间
   */
  completeTime: string
  /**
   * 是否已阅卷完成
   */
  markComplete: boolean
  /**
   * 阅卷完成时间
   */
  markCompleteTime: string
  /**
   * 总题数
   */
  totalNum: number
  /**
   * 已答题数
   */
  answeredNum: number
  /**
   * 对题数
   */
  correctNum: number
  /**
   * 错题数
   */
  failNum: number
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 考试分数
   */
  score: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 抽题方式，1：指定考纲方式，2：按试题题型及抽题比例,3指定题型
   */
  fetchWay: number
}

/**
 * <AUTHOR> create 2020/4/20 16:43
 */
export class PreExamDailyPracticePaperResponse {
  /**
   * 试卷id
   */
  id: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 试题数
   */
  questionCount: number
  /**
   * 允许作答的起始时间
   */
  enterStartTime: string
  /**
   * 允许作答的终止时间
   */
  enterEndTime: string
}

/**
 * <AUTHOR> create 2020/4/20 14:02
 */
export class PreExamErrorPronePracticeAnswerPaperResponse {
  /**
   * 答卷id
   */
  id: string
  /**
   * 作答id
   */
  answerId: string
  /**
   * 练习卷id
   */
  practicePaperId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 进入时间
   */
  enterTime: string
  /**
   * 是否已完成
   */
  complete: boolean
  /**
   * 完成时间
   */
  completeTime: string
  /**
   * 是否已阅卷完成
   */
  markComplete: boolean
  /**
   * 阅卷完成时间
   */
  markCompleteTime: string
  /**
   * 总题数
   */
  totalNum: number
  /**
   * 已答题数
   */
  answeredNum: number
  /**
   * 对题数
   */
  correctNum: number
  /**
   * 错题数
   */
  failNum: number
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 考试分数
   */
  score: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 抽题方式，1：指定考纲方式，2：按试题题型及抽题比例,3指定题型
   */
  fetchWay: number
  /**
   * 试题类别
   */
  questionCategory: string
  /**
   * 考纲id（答卷所属考纲id）
   */
  examinationOutlineId: string
  /**
   * 题型
   */
  questionType: string
}

/**
 * <AUTHOR> create 2020/4/20 16:45
 */
export class PreExamErrorPronePracticePaperResponse {
  /**
   * 试卷id
   */
  id: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 允许作答的起始时间
   */
  enterStartTime: string
  /**
   * 允许作答的终止时间
   */
  enterEndTime: string
}

/**
 * <AUTHOR> create 2020/4/20 14:04
 */
export class PreExamFavoritePracticeAnswerPaperResponse {
  /**
   * 答卷id
   */
  id: string
  /**
   * 作答id
   */
  answerId: string
  /**
   * 练习卷id
   */
  practicePaperId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 进入时间
   */
  enterTime: string
  /**
   * 是否已完成
   */
  complete: boolean
  /**
   * 完成时间
   */
  completeTime: string
  /**
   * 是否已阅卷完成
   */
  markComplete: boolean
  /**
   * 阅卷完成时间
   */
  markCompleteTime: string
  /**
   * 总题数
   */
  totalNum: number
  /**
   * 已答题数
   */
  answeredNum: number
  /**
   * 对题数
   */
  correctNum: number
  /**
   * 错题数
   */
  failNum: number
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 考试分数
   */
  score: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 抽题方式，1：指定考纲方式，2：按试题题型及抽题比例,3指定题型
   */
  fetchWay: number
  /**
   * 试题类别
   */
  questionCategory: string
  /**
   * 考纲id（答卷所属考纲id）
   */
  examinationOutlineId: string
  /**
   * 题型
   */
  questionType: string
}

/**
 * <AUTHOR> create 2020/4/20 16:44
 */
export class PreExamFavoritePracticePaperResponse {
  /**
   * 试卷id
   */
  id: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  issueId: string
  /**
   * 允许作答的起始时间
   */
  enterStartTime: string
  /**
   * 允许作答的终止时间
   */
  enterEndTime: string
}

/**
 * 考前练习卷
<AUTHOR> create 2020/4/20 15:26
 */
export class PreExamPracticePaperResponse {
  /**
   * 试卷id
   */
  id: string
  /**
   * 试卷名称
   */
  name: string
  /**
   * 试卷分类id
   */
  paperTypeId: string
  /**
   * 配置类型，来至ConfigType
1：固定卷 2：AB卷 3：智能卷
   */
  configType: number
  /**
   * 试卷分数
   */
  totalScore: number
  /**
   * 建议及格分数
   */
  passScore: number
  /**
   * 试卷总题数
   */
  totalQuestionCount: number
  /**
   * 计时方式
@see PaperTimeType
   */
  timeType: number
  /**
   * 建议考试时长
   */
  timeLength: number
  /**
   * 描述
   */
  description: string
  /**
   * 是否草稿
   */
  draft: boolean
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人
   */
  createUserId: string
  /**
   * 最后修改时间
   */
  lastChangeTime: string
}

/**
 * <AUTHOR> create 2020/4/20 13:56
 */
export class PreExamQuestionPracticeAnswerPaperResponse {
  /**
   * 答卷id
   */
  id: string
  /**
   * 作答id
   */
  answerId: string
  /**
   * 练习卷id
   */
  practicePaperId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 进入时间
   */
  enterTime: string
  /**
   * 是否已完成
   */
  complete: boolean
  /**
   * 完成时间
   */
  completeTime: string
  /**
   * 是否已阅卷完成
   */
  markComplete: boolean
  /**
   * 阅卷完成时间
   */
  markCompleteTime: string
  /**
   * 总题数
   */
  totalNum: number
  /**
   * 已答题数
   */
  answeredNum: number
  /**
   * 对题数
   */
  correctNum: number
  /**
   * 错题数
   */
  failNum: number
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 考试分数
   */
  score: number
  /**
   * 试卷总分
   */
  totalScore: number
  /**
   * 抽题方式，1：指定考纲方式，2：按试题题型及抽题比例
   */
  fetchWay: number
  /**
   * 试题类别
   */
  questionCategory: string
  /**
   * 考纲id（答卷所属考纲id）
   */
  examinationOutlineId: string
  /**
   * 应用程序标识
<p>该属性要生效，必须{@link #namespaceIdentity}也要有值</p>
   */
  applicationIdentity: string
  /**
   * 应用程序的命名空间标识, 在子项目级别唯一
<p>该属性要生效，必须{@link #applicationIdentity}也要有值</p>
   */
  namespaceIdentity: string
  /**
   * 考核范围
   */
  ranges: Array<Range>
  /**
   * 卷子的生成配置
   */
  generateConfiguration: PaperGenerateConfigurationResponse
  /**
   * 作答模式
@see AnswerMode
   */
  answerMode: number
  /**
   * 严格作答模式的配置
   */
  strictAnswerMode: StrictAnswerModeConfigResponse
  /**
   * 宽松作答模式的配置
   */
  looseAnswerMode: LooseAnswerModeConfigResponse
  /**
   * 卷子的作答配置
   */
  answerConfiguration: PaperAnswerConfigurationResponse
  /**
   * 卷子的阅卷配置
   */
  markConfiguration: MarkConfigurationResponse
  /**
   * 是否发布
   */
  released: boolean
  /**
   * 培训类别
   */
  trainCategory: TagDTO
  /**
   * 培训专业
   */
  trainProfession: TagDTO
}

/**
 * <AUTHOR> create 2020/4/20 16:40
 */
export class PreExamQuestionPracticePaperResponse {
  /**
   * 试卷id
   */
  id: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 抽题方式，1：指定考纲方式，2：按试题题型及抽题比例
   */
  fetchWay: number
  /**
   * 指定考纲方式
   */
  designatedExaminationOutlineWay: SupportDesignatedExaminationOutlineWayResponse
  /**
   * 按试题题型及抽题比例
   */
  questionCategoryRatioWay: SupportQuestionCategoryRatioWayResponse
  /**
   * 允许作答的起始时间
   */
  enterStartTime: string
  /**
   * 允许作答的终止时间
   */
  enterEndTime: string
}

/**
 * <AUTHOR> create 2020/4/20 16:41
 */
export class SupportDesignatedExaminationOutlineWayResponse {
  /**
   * 是否开启历年真题
   */
  openReal: boolean
  /**
   * 是否开启练习题
   */
  openPractice: boolean
  /**
   * 是否开启模拟试题
   */
  openSimulation: boolean
}

/**
 * <AUTHOR> create 2020/4/20 16:42
 */
export class SupportQuestionCategoryRatioWayResponse {
  /**
   * 真题比率
   */
  realRatioValue: number
  /**
   * 练习题比率
   */
  practiceRatioValue: number
  /**
   * 模拟题比率
   */
  simulationRatioValue: number
}

/**
 * <AUTHOR> create 2020/4/20 16:19
 */
export class LooseAnswerModeConfigResponse {
  /**
   * 是否限制作答次数
   */
  limitAnswerCount: boolean
  /**
   * 允许作答次数
   */
  permitAnswerCount: number
  /**
   * 及格后是否允许继续作答
<p>如果允许，作答次数也要控制在限制作答次数内</p>
   */
  continueIfPassed: boolean
  /**
   * 允许入场的起始时间
   */
  enterStartTime: string
  /**
   * 允许入场的终止时间
   */
  enterEndTime: string
  /**
   * <p>允许的作答时长， 单位: 分；0表示无限制,
   */
  answerTimeLength: number
}

/**
 * <AUTHOR> create 2020/4/20 16:36
 */
export class MarkConfigurationResponse {
  /**
   * 及格分数
   */
  passedScore: number
  /**
   * 多选题的多选漏选是否得分
   */
  scoredForMissChoice: boolean
  /**
   * 是否自动批阅填空题
   */
  autoMarkBlankQuestion: boolean
  /**
   * 成绩等级的评定配置
   */
  scoreGradeConfiguration: ScoreGradeConfigurationResponse
  /**
   * 交卷成功后是否在答题页面同步显示作答结果
   */
  showAnswerResult: boolean
  /**
   * 卷子结果公布类型
@see ScoreAnnounceType
   */
  announceType: number
  /**
   * <p>卷子结果公布时间.
<p>{@link #announceType 卷子结果公布类型}是{@link ScoreAnnounceType#LIMIT 限时公布}且当前时间大于等于公布时间，交卷则显示成绩
   */
  announceTime: string
}

/**
 * <AUTHOR> create 2020/4/20 16:35
 */
export class PaperAnswerConfigurationResponse {
  /**
   * <p>学员提前交卷的最少作答时长，单位: 分；
<p>0表示不限制
<p>交卷时, 参与服务端的逻辑校验
   */
  minimumAnswerTimeLength: number
  /**
   * 是否显示试题的正确答案, 有个【显示答案】按钮，切换显示答案。
   */
  showQuestionCorrectResult: boolean
  /**
   * 试卷作答时试题的显示模式
@see PaperQuestionDisplayMode
   */
  displayMode: number
  /**
   * 是否允许回退作答
<p>{@link #displayMode 试卷作答时试题的显示模式}是{@link PaperQuestionDisplayMode#SINGLE 单题作答}
   */
  back: boolean
  /**
   * 是否启用剪贴版.
禁用则不允许用户在[答题]时执行[剪切/复制]的操作
   */
  enableClipboard: boolean
  /**
   * <p>是否显示及格的分数
<p>若开放这个选项, 必须设置{@link PaperMarkConfigurationDTO#showAnswerResult 是否在答题页面同步显示作答结果}为<code>true</code>
   */
  showPassedScore: boolean
}

/**
 * <AUTHOR> create 2020/4/20 16:02
 */
export class PaperGenerateConfigurationResponse {
  /**
   * 预生成的答卷数量
<p>提前生成若干张答卷，学员进入场次，可从这些答卷当中随机抽取
<p>如果试卷的生成方式是 &quot;对象卷&quot; 此项配置无效。（因为对象卷是在学员进入场次时才确定抽题的对象）
   */
  preGenerateCount: number
  /**
   * 是否打乱试题题序, 当前仅在为固定卷时有效
   */
  confuseQuestion: boolean
  /**
   * 是否打乱选项题的选择项
   */
  confuseChoiceItem: boolean
}

/**
 * <AUTHOR> create 2020/4/20 16:37
 */
export class ScoreGradeConfigurationResponse {
  /**
   * 总分
   */
  score: number
  /**
   * 评定规则
   */
  rules: Array<ScoreGradeRuleResponse>
}

/**
 * <AUTHOR> create 2020/4/20 16:37
 */
export class ScoreGradeRuleResponse {
  /**
   * <p>区间值，作为一种构建考核表达式的原料</p>
<p>考试能力服务未限定或提供常量供使用者使用，建议使用大于0的数值，且等级越高数值越大，便于构建考核表达式</p>
<p>该值在阅卷完成后，会通过MQ的方式传达至上层应用，使用者请自行维护。</p>
<p> -1 是服务内部使用的值，供未配置评分模板的卷子阅卷后的成绩评定使用</p>
   */
  value: number
  /**
   * 成绩等级名称, 例如：优、良、及格、不及格
   */
  title: string
  /**
   * <p>成绩等级的评定分数</p>
   */
  gradeScore: number
  /**
   * 评级成绩的匹配模式
@see GradeScoreComparisonOperator
   */
  comparisonOperator: number
}

/**
 * <AUTHOR> create 2020/4/20 16:06
 */
export class StrictAnswerModeConfigResponse {
  /**
   * 是否限制作答次数
   */
  limitAnswerCount: boolean
  /**
   * 允许作答次数
   */
  permitAnswerCount: number
  /**
   * 及格后是否允许继续作答
<p>如果允许，作答次数也要控制在限制作答次数内</p>
   */
  continueIfPassed: boolean
  /**
   * 作答开始时间
   */
  startTime: string
  /**
   * 作答结束时间
   */
  endTime: string
  /**
   * 允许延迟开始作答的时长, 单位: 分
   */
  lateTimeLength: number
}

/**
 * <AUTHOR> create 2020/4/20 17:01
 */
export class BlankFillingResponse {
  /**
   * 答案数量
   */
  answerCount: number
  /**
   * 当填空题类型为 精确匹配时，最外层集合为答案组（也就是每一组都是一道填空题的答案，满足当中的任意一组表示回答正确）第二层集合为空
当填空题类似为 每空多答案时，最外层的集合为每个空的答案，第二层集合为每个空的备选答案
   */
  answersGroup: Array<string>
  /**
   * 答案项分值
当填空题类型为 精确匹配时此项值无效
   */
  answersItemScore: Array<number>
  /**
   * 答案类型
@see BlankFillingAnswerType
   */
  answerType: number
  /**
   * 答案是否有顺序.当{@link #answerType } &#x3D; {@link BlankFillingAnswerType#MULTIPLE_PER_BLANK} 时，
即每空多答案的情况下，答案是否是按照填空顺序排列。
   */
  sequence: boolean
  /**
   * 评分标准
   */
  standard: string
}

/**
 * <AUTHOR> create 2020/4/20 16:58
 */
export class ChoiceItemResponse {
  /**
   * 选项ID
   */
  id: string
  /**
   * 选项内容
   */
  content: string
}

/**
 * <AUTHOR> create 2020/4/20 17:06
 */
export class ComprehensiveChildQuestionResponse {
  /**
   * 子试题id
   */
  questionId: string
  /**
   * 题目
   */
  title: string
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 判断题
   */
  judgement: JudgementResponse
  /**
   * 单选题
   */
  singleChoice: SingleChoiceResponse
  /**
   * 多选
   */
  multipleChoice: MultipleChoiceResponse
  /**
   * 填空
   */
  blankFilling: BlankFillingResponse
  /**
   * 问答题
   */
  essay: EssayResponse
  /**
   * 量表题
   */
  scale: ScaleResponse
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficultyValue: number
  /**
   * 试题解析
   */
  description: string
}

/**
 * <AUTHOR> create 2020/4/20 17:05
 */
export class ComprehensiveResponse {
  /**
   * 子题
   */
  children: Array<ComprehensiveChildQuestionResponse>
}

/**
 * <AUTHOR> create 2020/4/20 17:04
 */
export class EssayResponse {
  /**
   * 参考答案
   */
  referenceAnswer: string
  /**
   * 评分标准
   */
  standard: string
  /**
   * 是否限制作答长度
   */
  limitAnswerLength: boolean
  /**
   * 允许作答的文本字符最少长度
   */
  permitAnswerLengthMin: number
  /**
   * 允许作答的文本字符最大长度
   */
  permitAnswerLengthMax: number
}

/**
 * <AUTHOR> create 2020/4/20 16:57
 */
export class JudgementResponse {
  /**
   * 正确答案
   */
  correctAnswer: boolean
  /**
   * 正确文本
   */
  correctText: string
  /**
   * 错误文本
   */
  incorrectText: string
}

/**
 * <AUTHOR> create 2020/4/20 17:00
 */
export class MultipleChoiceResponse {
  /**
   * 选项
   */
  choiceItems: Array<ChoiceItemResponse>
  /**
   * 正确答案
   */
  correctAnswers: Array<string>
}

/**
 * <AUTHOR> create 2020/5/19 10:31
 */
export class PreExamFavoriteQuestionValidateResponse {
  /**
   * 试题id
   */
  questionId: string
  /**
   * 是否收藏题
   */
  favorite: boolean
}

/**
 * <AUTHOR> create 2020/4/20 16:54
 */
export class PreExamQuestionItemResponse {
  /**
   * 试题id
   */
  id: string
  /**
   * 试题应用类型
@see QuestionApplyType
   */
  applyTypes: Array<number>
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 题库ID
   */
  libraryId: string
  /**
   * 题库信息
   */
  questionLibrary: QuestionLibraryResponse
  /**
   * 关联课程id
   */
  relateCourseId: string
  /**
   * 题目
   */
  title: string
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficulty: number
  /**
   * 试题解析
   */
  description: string
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 最后修改时间
   */
  lastChangeTime: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 资源记录(数据)的授权源id
a 授权 b, b 授权 c, c的sourceId是b, c的rootId是a
   */
  rootId: string
  /**
   * 数据授权的Token, 并不需要默认值
   */
  token: string
  /**
   * 标签
   */
  tags: Array<TagDTO>
  /**
   * 章节
   */
  chapters: Array<TagDTO>
  /**
   * 题类
   */
  questionCategory: string
  /**
   * 是否易错题
   */
  errorProne: boolean
}

/**
 * <AUTHOR> create 2020/4/20 16:56
 */
export class PreExamQuestionResponse {
  /**
   * 试题id
   */
  id: string
  /**
   * 试题应用类型
@see QuestionApplyType
   */
  applyTypes: Array<number>
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位ID
   */
  unitId: string
  /**
   * 所属组织机构ID
   */
  organizationId: string
  /**
   * 题库ID
   */
  libraryId: string
  /**
   * 题目
   */
  title: string
  /**
   * 判断题
   */
  judgement: JudgementResponse
  /**
   * 单选题
   */
  singleChoice: SingleChoiceResponse
  /**
   * 多选
   */
  multipleChoice: MultipleChoiceResponse
  /**
   * 填空
   */
  blankFilling: BlankFillingResponse
  /**
   * 问答题
   */
  essay: EssayResponse
  /**
   * 量表题
   */
  scale: ScaleResponse
  /**
   * 综合题
   */
  comprehensive: ComprehensiveResponse
  /**
   * 试题类型
@see QuestionType
   */
  questionType: number
  /**
   * 试题难度
@see QuestionMode
   */
  mode: number
  /**
   * 难度值
   */
  difficulty: number
  /**
   * 试题解析
   */
  description: string
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 最后修改时间
   */
  lastChangeTime: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 资源记录(数据)的授权源id
a 授权 b, b 授权 c, c的sourceId是b, c的rootId是a
   */
  rootId: string
  /**
   * 数据授权的Token, 并不需要默认值
   */
  token: string
  /**
   * 章节
   */
  chapters: Array<TagDTO>
  /**
   * 题类
   */
  questionCategory: string
  /**
   * 是否易错题
   */
  errorProne: boolean
}

/**
 * <AUTHOR> create 2020/4/20 16:49
 */
export class QuestionLibraryListResponse {
  /**
   * 试题库ID
   */
  id: string
  /**
   * 父试题库id 如果没有父题库为-1
   */
  parentId: string
  /**
   * 名称
   */
  name: string
  /**
   * 描述
   */
  description: string
  /**
   * 是否可用
   */
  enabled: boolean
  /**
   * 非内置根节点
   */
  notBuildInRootNode: boolean
  /**
   * 授权归属类型
@see AuthorizedBelongsTypeEnum
   */
  belongsType: string
  /**
   * 试题数量
   */
  questionCount: number
  /**
   * 是否有子题库
   */
  hasChildren: boolean
}

/**
 * <AUTHOR> create 2020/4/20 16:50
 */
export class QuestionLibraryResponse {
  /**
   * 试题库ID
   */
  id: string
  /**
   * 父试题库id 如果没有父题库为-1
   */
  parentId: string
  /**
   * 名称
   */
  name: string
  /**
   * 描述
   */
  description: string
  /**
   * 是否可用
   */
  enabled: boolean
  /**
   * 非内置根节点
   */
  notBuildInRootNode: boolean
  /**
   * 授权归属类型
@see AuthorizedBelongsTypeEnum
   */
  belongsType: string
}

/**
 * <AUTHOR> create 2020/4/20 17:05
 */
export class ScaleResponse {
  /**
   * 量表类型
   */
  scaleType: ScaleType
  /**
   * 程度_始，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写
   */
  startDegree: string
  /**
   * 程度_止，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写
   */
  endDegree: string
  /**
   * 级数
   */
  series: number
  /**
   * 初始值
   */
  initialValue: number
}

/**
 * <AUTHOR> create 2020/4/20 16:58
 */
export class SingleChoiceResponse {
  /**
   * 选项
   */
  choiceItems: Array<ChoiceItemResponse>
  /**
   * 标准答案
   */
  correctAnswer: string
}

export class PreExamMockExaminationPaperItemResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PreExamMockExaminationPaperItemResponse>
}

export class PreExamQuestionItemResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PreExamQuestionItemResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取试卷分页
   * @param page
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findExamPaperPage(
    params: { page?: Page; param?: PreExamMockExaminationPaperParamRequest },
    query: DocumentNode = GraphqlImporter.findExamPaperPage,
    operation?: string
  ): Promise<Response<PreExamMockExaminationPaperItemResponsePage>> {
    return commonRequestApi<PreExamMockExaminationPaperItemResponsePage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 查询考试试题分页
   * @param page
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findExamQuestionPage(
    params: { page?: Page; param?: PreExamQuestionParamDTO },
    query: DocumentNode = GraphqlImporter.findExamQuestionPage,
    operation?: string
  ): Promise<Response<PreExamQuestionItemResponsePage>> {
    return commonRequestApi<PreExamQuestionItemResponsePage>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 查询试题
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findQuestion(
    id: string,
    query: DocumentNode = GraphqlImporter.findQuestion,
    operation?: string
  ): Promise<Response<PreExamQuestionResponse>> {
    return commonRequestApi<PreExamQuestionResponse>(SERVER_URL, {
      query: query,
      variables: { id },
      operation: operation
    })
  }

  /**   * 获取每日一练
   * @param learningId
   * @return
   * @param query 查询 graphql 语法文档
   * @param learningId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getDailyPracticePaper(
    learningId: string,
    query: DocumentNode = GraphqlImporter.getDailyPracticePaper,
    operation?: string
  ): Promise<Response<PreExamDailyPracticePaperResponse>> {
    return commonRequestApi<PreExamDailyPracticePaperResponse>(SERVER_URL, {
      query: query,
      variables: { learningId },
      operation: operation
    })
  }

  /**   * 获取易錯題练习卷
   * @param learningId
   * @return
   * @param query 查询 graphql 语法文档
   * @param learningId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getErrorPronePracticePaper(
    learningId: string,
    query: DocumentNode = GraphqlImporter.getErrorPronePracticePaper,
    operation?: string
  ): Promise<Response<PreExamErrorPronePracticePaperResponse>> {
    return commonRequestApi<PreExamErrorPronePracticePaperResponse>(SERVER_URL, {
      query: query,
      variables: { learningId },
      operation: operation
    })
  }

  /**   * 获取模拟考答卷
   * @param examRoundId
   * @return
   * @param query 查询 graphql 语法文档
   * @param examRoundId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getExamAnswerPaper(
    examRoundId: string,
    query: DocumentNode = GraphqlImporter.getExamAnswerPaper,
    operation?: string
  ): Promise<Response<ExamAnswerPaperResponse>> {
    return commonRequestApi<ExamAnswerPaperResponse>(SERVER_URL, {
      query: query,
      variables: { examRoundId },
      operation: operation
    })
  }

  /**   * 获取模拟考答卷
   * @param schemeId
   * @param learningId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getExamAnswerPaperByLearningId(
    params: { schemeId?: string; learningId?: string },
    query: DocumentNode = GraphqlImporter.getExamAnswerPaperByLearningId,
    operation?: string
  ): Promise<Response<ExamAnswerPaperResponse>> {
    return commonRequestApi<ExamAnswerPaperResponse>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取试卷详情
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getExamPaper(
    id: string,
    query: DocumentNode = GraphqlImporter.getExamPaper,
    operation?: string
  ): Promise<Response<PreExamMockExaminationPaperResponse>> {
    return commonRequestApi<PreExamMockExaminationPaperResponse>(SERVER_URL, {
      query: query,
      variables: { id },
      operation: operation
    })
  }

  /**   * 获取试卷详情
   * @param schemeId
   * @param learningId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getExamPaperByLearningId(
    params: { schemeId?: string; learningId?: string },
    query: DocumentNode = GraphqlImporter.getExamPaperByLearningId,
    operation?: string
  ): Promise<Response<PreExamMockExaminationPaperResponse>> {
    return commonRequestApi<PreExamMockExaminationPaperResponse>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取错题重练,若没有则新增
   * @param schemeId
   * @return
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOrCreateCorrectionPracticePaper(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.getOrCreateCorrectionPracticePaper,
    operation?: string
  ): Promise<Response<PreExamCorrectionPracticePaperResponse>> {
    return commonRequestApi<PreExamCorrectionPracticePaperResponse>(SERVER_URL, {
      query: query,
      variables: { schemeId },
      operation: operation
    })
  }

  /**   * 获取收藏题练习,若没有则新增
   * @param schemeId
   * @param issueId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getOrCreateFavoritePracticePaper(
    params: { schemeId?: string; issueId?: string },
    query: DocumentNode = GraphqlImporter.getOrCreateFavoritePracticePaper,
    operation?: string
  ): Promise<Response<PreExamFavoritePracticePaperResponse>> {
    return commonRequestApi<PreExamFavoritePracticePaperResponse>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 根据学习方式id查询用户考前错题重练答卷
   * @param learningId
   * @return
   * @param query 查询 graphql 语法文档
   * @param learningId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPreExamCorrectionPracticeList(
    learningId: string,
    query: DocumentNode = GraphqlImporter.getPreExamCorrectionPracticeList,
    operation?: string
  ): Promise<Response<Array<PreExamCorrectionPracticeAnswerPaperResponse>>> {
    return commonRequestApi<Array<PreExamCorrectionPracticeAnswerPaperResponse>>(SERVER_URL, {
      query: query,
      variables: { learningId },
      operation: operation
    })
  }

  /**   * 根据学习方式id查询用户每日一练
   * @param learningId
   * @return
   * @param query 查询 graphql 语法文档
   * @param learningId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPreExamDailyPracticeList(
    learningId: string,
    query: DocumentNode = GraphqlImporter.getPreExamDailyPracticeList,
    operation?: string
  ): Promise<Response<Array<PreExamDailyPracticeAnswerPaperResponse>>> {
    return commonRequestApi<Array<PreExamDailyPracticeAnswerPaperResponse>>(SERVER_URL, {
      query: query,
      variables: { learningId },
      operation: operation
    })
  }

  /**   * 根据学习方式id查询用户考前易错题答卷
   * @param learningId
   * @return
   * @param query 查询 graphql 语法文档
   * @param learningId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPreExamErrorPronePracticeList(
    learningId: string,
    query: DocumentNode = GraphqlImporter.getPreExamErrorPronePracticeList,
    operation?: string
  ): Promise<Response<Array<PreExamErrorPronePracticeAnswerPaperResponse>>> {
    return commonRequestApi<Array<PreExamErrorPronePracticeAnswerPaperResponse>>(SERVER_URL, {
      query: query,
      variables: { learningId },
      operation: operation
    })
  }

  /**   * 根据试卷idid查询用户考前收藏试题答卷
   * @param paperId
   * @return
   * @param query 查询 graphql 语法文档
   * @param paperId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPreExamFavoritePracticeList(
    paperId: string,
    query: DocumentNode = GraphqlImporter.getPreExamFavoritePracticeList,
    operation?: string
  ): Promise<Response<Array<PreExamFavoritePracticeAnswerPaperResponse>>> {
    return commonRequestApi<Array<PreExamFavoritePracticeAnswerPaperResponse>>(SERVER_URL, {
      query: query,
      variables: { paperId },
      operation: operation
    })
  }

  /**   * 获取考前练习卷
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPreExamPracticePaper(
    id: string,
    query: DocumentNode = GraphqlImporter.getPreExamPracticePaper,
    operation?: string
  ): Promise<Response<PreExamPracticePaperResponse>> {
    return commonRequestApi<PreExamPracticePaperResponse>(SERVER_URL, {
      query: query,
      variables: { id },
      operation: operation
    })
  }

  /**   * 根据学习方式id查询用户考前试题练习答卷
   * @param learningId
   * @return
   * @param query 查询 graphql 语法文档
   * @param learningId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPreExamQuestionPracticeList(
    learningId: string,
    query: DocumentNode = GraphqlImporter.getPreExamQuestionPracticeList,
    operation?: string
  ): Promise<Response<Array<PreExamQuestionPracticeAnswerPaperResponse>>> {
    return commonRequestApi<Array<PreExamQuestionPracticeAnswerPaperResponse>>(SERVER_URL, {
      query: query,
      variables: { learningId },
      operation: operation
    })
  }

  /**   * 获取试题练习
   * @param learningId 学习方式id
   * @return
   * @param query 查询 graphql 语法文档
   * @param learningId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getQuestionPracticePaper(
    learningId: string,
    query: DocumentNode = GraphqlImporter.getQuestionPracticePaper,
    operation?: string
  ): Promise<Response<PreExamQuestionPracticePaperResponse>> {
    return commonRequestApi<PreExamQuestionPracticePaperResponse>(SERVER_URL, {
      query: query,
      variables: { learningId },
      operation: operation
    })
  }

  /**   * 获取子试卷分类
   * @param parentId
   * @return
   * @param query 查询 graphql 语法文档
   * @param parentId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSubPaperClassificationList(
    parentId: string,
    query: DocumentNode = GraphqlImporter.getSubPaperClassificationList,
    operation?: string
  ): Promise<Response<Array<PaperClassificationListResponse>>> {
    return commonRequestApi<Array<PaperClassificationListResponse>>(SERVER_URL, {
      query: query,
      variables: { parentId },
      operation: operation
    })
  }

  /**   * 查询子题库
   * @param parentId
   * @return
   * @param query 查询 graphql 语法文档
   * @param parentId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSubQuestionLibraryList(
    parentId: string,
    query: DocumentNode = GraphqlImporter.getSubQuestionLibraryList,
    operation?: string
  ): Promise<Response<Array<QuestionLibraryListResponse>>> {
    return commonRequestApi<Array<QuestionLibraryListResponse>>(SERVER_URL, {
      query: query,
      variables: { parentId },
      operation: operation
    })
  }

  /**   * 试卷是否被引用
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async hasReference(
    id: string,
    query: DocumentNode = GraphqlImporter.hasReference,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: { id },
      operation: operation
    })
  }

  /**   * 是否是收藏试题
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async isFavoriteQuestion(
    param: PreExamFavoriteQuestionRequest,
    query: DocumentNode = GraphqlImporter.isFavoriteQuestion,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: { param },
      operation: operation
    })
  }

  /**   * 试卷名称是否已存在
   * @param unitId
   * @param name
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async isNameExists(
    params: { unitId?: string; name?: string; id?: string },
    query: DocumentNode = GraphqlImporter.isNameExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 是否有子分类
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async paperClassificationHasChild(
    id: string,
    query: DocumentNode = GraphqlImporter.paperClassificationHasChild,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: { id },
      operation: operation
    })
  }

  /**   * 是否被引用
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async paperClassificationHasReference(
    id: string,
    query: DocumentNode = GraphqlImporter.paperClassificationHasReference,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: { id },
      operation: operation
    })
  }

  /**   * 指定分类下的子分类名称是否重复
   * @param name
   * @param parentId
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async paperClassificationNameExists(
    params: { name?: string; parentId?: string; id?: string },
    query: DocumentNode = GraphqlImporter.paperClassificationNameExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 是否有子题库
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async questionLibraryHasChild(
    id: string,
    query: DocumentNode = GraphqlImporter.questionLibraryHasChild,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: { id },
      operation: operation
    })
  }

  /**   * 是否被试题引用
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async questionLibraryHasReference(
    id: string,
    query: DocumentNode = GraphqlImporter.questionLibraryHasReference,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: { id },
      operation: operation
    })
  }

  /**   * 名称是否存在
   * @param name
   * @param parentId
   * @param id
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async questionLibraryNameExists(
    params: { name?: string; parentId?: string; id?: string },
    query: DocumentNode = GraphqlImporter.questionLibraryNameExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 是否是收藏试题
   * @param schemeId
   * @param issueId
   * @param questionIds
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validateFavorite(
    params: { schemeId?: string; issueId?: string; questionIds?: Array<string> },
    query: DocumentNode = GraphqlImporter.validateFavorite,
    operation?: string
  ): Promise<Response<Array<PreExamFavoriteQuestionValidateResponse>>> {
    return commonRequestApi<Array<PreExamFavoriteQuestionValidateResponse>>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 添加收藏试题
   * @param param
   * @param mutate 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addUserFavoriteQuestion(
    param: PreExamFavoriteQuestionRequest,
    mutate: DocumentNode = GraphqlImporter.addUserFavoriteQuestion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { param },
      operation: operation
    })
  }

  /**   * 批量迁移试题到目标题库
   * @param originLibraryId
   * @param targetLibraryId
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchMoveQuestionToAnotherLibrary(
    params: { originLibraryId?: string; targetLibraryId?: string },
    mutate: DocumentNode = GraphqlImporter.batchMoveQuestionToAnotherLibrary,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 拷贝试卷
   * @param id
   * @param newName
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async copyExamPaper(
    params: { id?: string; newName?: string },
    mutate: DocumentNode = GraphqlImporter.copyExamPaper,
    operation?: string
  ): Promise<Response<PreExamMockExaminationPaperResponse>> {
    return commonRequestApi<PreExamMockExaminationPaperResponse>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 创建考试试卷
   * @param create
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createExamPaper(
    create: PreExamMockExaminationPaperCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createExamPaper,
    operation?: string
  ): Promise<Response<PreExamMockExaminationPaperResponse>> {
    return commonRequestApi<PreExamMockExaminationPaperResponse>(SERVER_URL, {
      query: mutate,
      variables: { create },
      operation: operation
    })
  }

  /**   * 创建试卷分类
   * @param create
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createPaperClassification(
    create: PaperClassificationCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createPaperClassification,
    operation?: string
  ): Promise<Response<PaperClassificationResponse>> {
    return commonRequestApi<PaperClassificationResponse>(SERVER_URL, {
      query: mutate,
      variables: { create },
      operation: operation
    })
  }

  /**   * 创建试题
   * @param create
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createQuestion(
    create: PreExamQuestionCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createQuestion,
    operation?: string
  ): Promise<Response<PreExamQuestionResponse>> {
    return commonRequestApi<PreExamQuestionResponse>(SERVER_URL, {
      query: mutate,
      variables: { create },
      operation: operation
    })
  }

  /**   * 创建题库
   * @param create
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param create 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createQuestionLibrary(
    create: QuestionLibraryCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createQuestionLibrary,
    operation?: string
  ): Promise<Response<QuestionLibraryResponse>> {
    return commonRequestApi<QuestionLibraryResponse>(SERVER_URL, {
      query: mutate,
      variables: { create },
      operation: operation
    })
  }

  /**   * 删除考试试卷
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteExamPaper(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteExamPaper,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { id },
      operation: operation
    })
  }

  /**   * 删除试卷分类
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deletePaperClassification(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deletePaperClassification,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { id },
      operation: operation
    })
  }

  /**   * 删除试题
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteQuestion(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteQuestion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { id },
      operation: operation
    })
  }

  /**   * 删除试题库
   * @param id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteQuestionLibrary(
    id: string,
    mutate: DocumentNode = GraphqlImporter.deleteQuestionLibrary,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { id },
      operation: operation
    })
  }

  /**   * 停用试卷
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableExamPaper(
    id: string,
    mutate: DocumentNode = GraphqlImporter.disableExamPaper,
    operation?: string
  ): Promise<Response<PreExamMockExaminationPaperResponse>> {
    return commonRequestApi<PreExamMockExaminationPaperResponse>(SERVER_URL, {
      query: mutate,
      variables: { id },
      operation: operation
    })
  }

  /**   * 停用试题
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableQuestion(
    id: string,
    mutate: DocumentNode = GraphqlImporter.disableQuestion,
    operation?: string
  ): Promise<Response<PreExamQuestionResponse>> {
    return commonRequestApi<PreExamQuestionResponse>(SERVER_URL, {
      query: mutate,
      variables: { id },
      operation: operation
    })
  }

  /**   * 停用题库
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableQuestionLibrary(
    id: string,
    mutate: DocumentNode = GraphqlImporter.disableQuestionLibrary,
    operation?: string
  ): Promise<Response<QuestionLibraryResponse>> {
    return commonRequestApi<QuestionLibraryResponse>(SERVER_URL, {
      query: mutate,
      variables: { id },
      operation: operation
    })
  }

  /**   * 启用试卷
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableExamPaper(
    id: string,
    mutate: DocumentNode = GraphqlImporter.enableExamPaper,
    operation?: string
  ): Promise<Response<PreExamMockExaminationPaperResponse>> {
    return commonRequestApi<PreExamMockExaminationPaperResponse>(SERVER_URL, {
      query: mutate,
      variables: { id },
      operation: operation
    })
  }

  /**   * 启用试题
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableQuestion(
    id: string,
    mutate: DocumentNode = GraphqlImporter.enableQuestion,
    operation?: string
  ): Promise<Response<PreExamQuestionResponse>> {
    return commonRequestApi<PreExamQuestionResponse>(SERVER_URL, {
      query: mutate,
      variables: { id },
      operation: operation
    })
  }

  /**   * 启用题库
   * @param id
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableQuestionLibrary(
    id: string,
    mutate: DocumentNode = GraphqlImporter.enableQuestionLibrary,
    operation?: string
  ): Promise<Response<QuestionLibraryResponse>> {
    return commonRequestApi<QuestionLibraryResponse>(SERVER_URL, {
      query: mutate,
      variables: { id },
      operation: operation
    })
  }

  /**   * 根据专业和试卷id获取预览的token
   * @param paperId
   * @param professionId
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPreviewTokenByPaperAndProfession(
    params: { paperId?: string; professionId?: string },
    mutate: DocumentNode = GraphqlImporter.getPreviewTokenByPaperAndProfession,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 去做错题重练练习
   * @param init
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param init 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async goCorrectionPractice(
    init: CorrectionAnswerInitRequest,
    mutate: DocumentNode = GraphqlImporter.goCorrectionPractice,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { init },
      operation: operation
    })
  }

  /**   * 去做收藏题练习
   * @param init
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param init 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async goFavoritePractice(
    init: FavoriteAnswerInitRequest,
    mutate: DocumentNode = GraphqlImporter.goFavoritePractice,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { init },
      operation: operation
    })
  }

  /**   * 去做模拟考试
   * @param examRoundId
   * @return 考场id
   * @param mutate 查询 graphql 语法文档
   * @param examRoundId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async goMockExamination(
    examRoundId: string,
    mutate: DocumentNode = GraphqlImporter.goMockExamination,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { examRoundId },
      operation: operation
    })
  }

  /**   * 去做某专业的模拟考试
   * @param token
   * @return 考场id
   * @param mutate 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async goMockExaminationByToken(
    token: string,
    mutate: DocumentNode = GraphqlImporter.goMockExaminationByToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { token },
      operation: operation
    })
  }

  /**   * 去做练习
   * @param paperId
   * @return 答卷id
   * @param mutate 查询 graphql 语法文档
   * @param paperId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async goPractice(
    paperId: string,
    mutate: DocumentNode = GraphqlImporter.goPractice,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { paperId },
      operation: operation
    })
  }

  /**   * 去做练习
   * @param token
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async goPracticeByToken(
    token: string,
    mutate: DocumentNode = GraphqlImporter.goPracticeByToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { token },
      operation: operation
    })
  }

  /**   * 去做练习
   * @param token
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async goPracticeByTokenAndSpecifyQuestionCount(
    params: { token?: string; questionCount: number },
    mutate: DocumentNode = GraphqlImporter.goPracticeByTokenAndSpecifyQuestionCount,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 试题导入
   * @param mutate 查询 graphql 语法文档
   * @param questionImport 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async questionImport(
    questionImport: PreExamQuestionImportRequest,
    mutate: DocumentNode = GraphqlImporter.questionImport,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { questionImport },
      operation: operation
    })
  }

  /**   * 重做某场考试
   * @param answersId
   * @param answerRecordId
   * @return 考场id
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async redoMockExamination(
    params: { answersId?: string; answerRecordId?: string },
    mutate: DocumentNode = GraphqlImporter.redoMockExamination,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 重做练习
   * @param answersId
   * @param answerRecordId
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async redoPractice(
    params: { answersId?: string; answerRecordId?: string },
    mutate: DocumentNode = GraphqlImporter.redoPractice,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 移除收藏试题
   * @param param
   * @param mutate 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeUserFavoriteQuestion(
    param: PreExamFavoriteQuestionRequest,
    mutate: DocumentNode = GraphqlImporter.removeUserFavoriteQuestion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { param },
      operation: operation
    })
  }

  /**   * 修改考试试卷
   * @param update
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param update 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateExamPaper(
    update: PreExamMockExaminationPaperUpdateDTO,
    mutate: DocumentNode = GraphqlImporter.updateExamPaper,
    operation?: string
  ): Promise<Response<PreExamMockExaminationPaperResponse>> {
    return commonRequestApi<PreExamMockExaminationPaperResponse>(SERVER_URL, {
      query: mutate,
      variables: { update },
      operation: operation
    })
  }

  /**   * 更新试卷分类
   * @param update
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param update 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updatePaperClassification(
    update: PaperClassificationUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updatePaperClassification,
    operation?: string
  ): Promise<Response<PaperClassificationResponse>> {
    return commonRequestApi<PaperClassificationResponse>(SERVER_URL, {
      query: mutate,
      variables: { update },
      operation: operation
    })
  }

  /**   * 更新试题
   * @param update
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param update 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateQuestion(
    update: PreExamQuestionUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateQuestion,
    operation?: string
  ): Promise<Response<PreExamQuestionResponse>> {
    return commonRequestApi<PreExamQuestionResponse>(SERVER_URL, {
      query: mutate,
      variables: { update },
      operation: operation
    })
  }

  /**   * 修改题库
   * @param update
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param update 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateQuestionLibrary(
    update: QuestionLibraryUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateQuestionLibrary,
    operation?: string
  ): Promise<Response<QuestionLibraryResponse>> {
    return commonRequestApi<QuestionLibraryResponse>(SERVER_URL, {
      query: mutate,
      variables: { update },
      operation: operation
    })
  }
}

export default new DataGateway()
