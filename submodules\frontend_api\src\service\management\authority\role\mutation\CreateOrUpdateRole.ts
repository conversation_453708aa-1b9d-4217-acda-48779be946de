import roleGateWay from '@api/ms-gateway/ms-role-v1'
import { RolePermissionDto } from '@api/service/management/authority/role/mutation/dto/RolePermissionDto'

class CreateOrUpdateRole {
  createRoleDto: RolePermissionDto // 创建或更新 区别是是否带有id

  async doCreateOrUpdate() {
    try {
      const res = await roleGateWay.saveRole(this.createRoleDto)
      console.log('调用了doCreateOrUpdate方法，返回值=', res.status)
      return res.status
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/mutation/CreateOrUpdateRole.ts所处方法，doCreateOrUpdate',
        e
      )
    }
  }
}

export default CreateOrUpdateRole
