<template>
  <!--找回密码-确认身份-->

  <div>
    <!--密码默认状态-->
    <div
      class="cu-form-group m-warm-pwd"
      v-if="!passwordStrengthLow && !passwordStrengthIntermediate && !passwordStrengthHigh"
    >
      <div class="item bg-gray"></div>
      <div class="item bg-gray"></div>
      <div class="item bg-gray"></div>
    </div>
    <!--密码高风险状态-->
    <div class="psw-tips" v-if="passwordStrengthLow">
      <el-progress :percentage="33.33" color="#e93737" :show-text="false"></el-progress>
      <!--弱：txt-l，中：txt-m，强：txt-h-->
      <span class="txt txt-l">弱</span>
    </div>

    <!--密码中风险状态-->
    <div class="psw-tips" v-if="passwordStrengthIntermediate">
      <el-progress :percentage="66.66" color="#ee9e2d" :show-text="false"></el-progress>
      <!--弱：txt-l，中：txt-m，强：txt-h-->
      <span class="txt txt-m">中</span>
    </div>
    <!--密码安全状态-->
    <div class="psw-tips" v-if="passwordStrengthHigh">
      <el-progress :percentage="100" color="#49b042" :show-text="false"></el-progress>
      <!--弱：txt-l，中：txt-m，强：txt-h-->
      <span class="txt txt-h">强</span>
    </div>
  </div>
  <!-- </div> -->
</template>

<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    @Prop(String) readonly password: string
    // 密码强度低
    passwordStrengthLow = false
    // 密码强度中
    passwordStrengthIntermediate = false
    // 密码强度高
    passwordStrengthHigh = false

    @Watch('password')
    checkoutPasswordStrength() {
      const reg1 = /^.{1,8}$|^\d{9,}$|^[a-zA-Z]{9,}$|^(?=[\x21-\x7e]+)[^A-Za-z0-9]{9,}$/ //密码低强度正则--纯数字或字母或字符或长度1-6
      const reg2 = /^(?!\d+$)[a-zA-Z0-9]{9,}$|^(?![0-9]+$)[^a-zA-Z]{9,}$|^(?![a-zA-Z]+$)[^0-9]{9,}$/ //密码中强度正则--有两种且长度在7-10
      const reg3 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{13,}$/ //密码高强度正则--三种都有且长度大于10
      const reg4 = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[^0-9a-zA-Z]).{9,12}$/ //密码中强度正则--介于7-10位的三种字符都有的密码
      if (!this.password) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = false
      } else if (reg1.test(this.password)) {
        this.passwordStrengthLow = true
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = false
      } else if (reg2.test(this.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = true
        this.passwordStrengthHigh = false
      } else if (reg3.test(this.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = false
        this.passwordStrengthHigh = true
      } else if (reg4.test(this.password)) {
        this.passwordStrengthLow = false
        this.passwordStrengthIntermediate = true
        this.passwordStrengthHigh = false
      }
    }
  }
</script>
