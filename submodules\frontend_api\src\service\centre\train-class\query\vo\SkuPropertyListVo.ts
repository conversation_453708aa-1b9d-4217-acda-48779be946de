import SkuVo from '@api/service/centre/train-class/query/vo/SkuVo'
import RegionTreeVo from '@api/service/centre/train-class/query/vo/RegionTreeVo'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * @description
 */
class SkuPropertyListVo {
  /**
   * 年度
   */
  year: SkuVo[] = []

  /**
   * 地区
   */
  region: SkuVo[] = []

  /**
   * 地区（树形）
   */
  regionTree: RegionTreeVo[] = []

  /**
   * 行业
   */
  industry: SkuVo[] = []

  /**
   * 科目类型
   */
  subjectType: SkuVo[] = []

  /**
   * 培训类别
   */
  trainingCategory: SkuVo[] = []

  /**
   * 培训专业
   */
  trainingMajor: SkuVo[] = []
  /**
   * 技术等级
   */
  jobLevel: SkuVo[] = []
  /**
   * 岗位类别
   */
  positionCategory: SkuVo[] = []
  /**
   * 培训对象
   */
  trainingObject: SkuVo[] = []
  /**
   * 学段
   */
  learningPhase: SkuVo[] = []
  /**
   * 科目
   */
  discipline: SkuVo[] = []
  /**
   * 证书类型
   */
  certificatesType: SkuVo[] = []
  /**
   * 执业类别
   */
  practitionerCategory: SkuVo[] = []
  /**
   * 培训形式
   */
  trainingMode: SkuVo[] = []
}

export default SkuPropertyListVo
