import { CourseSalesStatisticsInfo } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { CoursewareSupplierResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

class ChooseCourseStatisticDetail extends CourseSalesStatisticsInfo {
  providerName = ''

  static from(dto: CourseSalesStatisticsInfo[]): ChooseCourseStatisticDetail[] {
    return dto.map(res => {
      let vo = new ChooseCourseStatisticDetail()
      vo = Object.assign(vo, res)
      return vo
    })
  }

  static addProviderInfo(dto: ChooseCourseStatisticDetail, providerInfo: CoursewareSupplierResponse[]) {
    const curProvider = providerInfo.find(res => res.servicerBase.servicerId === dto.coursewareSupplierId)
    if (curProvider) {
      dto.providerName = curProvider.servicerBase.servicerName
    }
    return dto
  }
}

export default ChooseCourseStatisticDetail
