import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/WorkType-default'

// 枚举

// 类

export class BigCategoryCreateRequest {
  name: string
  /**
   * 排序，-1为自动排序
   */
  sort: number
}

export class BigCategoryTreeCreateRequest {
  mediumCategories?: Array<MediumCategoryTreeCreateRequest>
  name: string
  /**
   * 排序，-1为自动排序
   */
  sort: number
}

export class BigCategoryUpdateRequest {
  id: string
  name: string
}

export class MediumCategoryCreateRequest {
  name: string
  bigCategoryId: string
  /**
   * 排序，-1为自动排序
   */
  sort: number
}

export class MediumCategoryTreeCreateRequest {
  smallCategories?: Array<SmallCategoryTreeCreateRequest>
  name: string
  bigCategoryId: string
  /**
   * 排序，-1为自动排序
   */
  sort: number
}

export class MediumCategoryUpdateRequest {
  id: string
  name: string
}

export class ProfessionCreateRequest {
  name: string
  smallCategoryId: string
  /**
   * 排序，-1为自动排序
   */
  sort: number
}

export class ProfessionUpdateRequest {
  id: string
  name: string
}

export class SmallCategoryCreateRequest {
  name: string
  mediumCategoryId: string
  /**
   * 排序，-1为自动排序
   */
  sort: number
}

export class SmallCategoryTreeCreateRequest {
  professions?: Array<ProfessionCreateRequest>
  name: string
  mediumCategoryId: string
  /**
   * 排序，-1为自动排序
   */
  sort: number
}

export class SmallCategoryUpdateRequest {
  id: string
  name: string
}

/**
 * 工种大类响应信息
<AUTHOR>
 */
export class BigCategoryResponse {
  id: string
  name: string
  sort: number
  enabled: boolean
  createUserId: string
  createTime: string
  creatorId: string
  code: string
}

export class BigCategoryTreeResponse {
  children: Array<MediumCategoryTreeResponse>
  id: string
  name: string
  sort: number
  enabled: boolean
  createUserId: string
  createTime: string
  creatorId: string
  code: string
}

export class MediumCategoryResponse {
  /**
   * 所属大类id
   */
  bigCategoryId: string
  id: string
  name: string
  sort: number
  enabled: boolean
  createUserId: string
  createTime: string
  creatorId: string
  code: string
}

export class MediumCategoryTreeResponse {
  /**
   * 所属大类id
   */
  bigCategoryId: string
  children: Array<SmallCategoryTreeResponse>
  id: string
  name: string
  sort: number
  enabled: boolean
  createUserId: string
  createTime: string
  creatorId: string
  code: string
}

export class ProfessionResponse {
  smallCategoryId: string
  id: string
  name: string
  sort: number
  enabled: boolean
  createUserId: string
  createTime: string
  creatorId: string
  code: string
}

export class SmallCategoryResponse {
  /**
   * 所属的中类id
   */
  mediumCategoryId: string
  id: string
  name: string
  sort: number
  enabled: boolean
  createUserId: string
  createTime: string
  creatorId: string
  code: string
}

export class SmallCategoryTreeResponse {
  /**
   * 所属的中类id
   */
  mediumCategoryId: string
  children: Array<ProfessionResponse>
  id: string
  name: string
  sort: number
  enabled: boolean
  createUserId: string
  createTime: string
  creatorId: string
  code: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取工种所有大类信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllBigCategoryList(
    query: DocumentNode = GraphqlImporter.getAllBigCategoryList,
    operation?: string
  ): Promise<Response<Array<BigCategoryResponse>>> {
    return commonRequestApi<Array<BigCategoryResponse>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 获取所有工种大类树
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllBigCategoryTreeList(
    query: DocumentNode = GraphqlImporter.getAllBigCategoryTreeList,
    operation?: string
  ): Promise<Response<Array<BigCategoryTreeResponse>>> {
    return commonRequestApi<Array<BigCategoryTreeResponse>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 获取工种所有中类信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllMediumCategoryList(
    query: DocumentNode = GraphqlImporter.getAllMediumCategoryList,
    operation?: string
  ): Promise<Response<Array<MediumCategoryResponse>>> {
    return commonRequestApi<Array<MediumCategoryResponse>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 获取工种所有职业信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllProfessionList(
    query: DocumentNode = GraphqlImporter.getAllProfessionList,
    operation?: string
  ): Promise<Response<Array<ProfessionResponse>>> {
    return commonRequestApi<Array<ProfessionResponse>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 获取工种所有小类信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllSmallCategoryList(
    query: DocumentNode = GraphqlImporter.getAllSmallCategoryList,
    operation?: string
  ): Promise<Response<Array<SmallCategoryResponse>>> {
    return commonRequestApi<Array<SmallCategoryResponse>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 根据大类id，获取大类的关系树
   * @param query 查询 graphql 语法文档
   * @param bigCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBigCategoryTree(
    bigCategoryId: string,
    query: DocumentNode = GraphqlImporter.getBigCategoryTree,
    operation?: string
  ): Promise<Response<BigCategoryTreeResponse>> {
    return commonRequestApi<BigCategoryTreeResponse>(SERVER_URL, {
      query: query,
      variables: { bigCategoryId },
      operation: operation
    })
  }

  /**   * 根据大类id，获取指定大类下的中类信息
   * @param query 查询 graphql 语法文档
   * @param bigCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getMediumCategoryListByBigCategoryId(
    bigCategoryId: string,
    query: DocumentNode = GraphqlImporter.getMediumCategoryListByBigCategoryId,
    operation?: string
  ): Promise<Response<Array<MediumCategoryResponse>>> {
    return commonRequestApi<Array<MediumCategoryResponse>>(SERVER_URL, {
      query: query,
      variables: { bigCategoryId },
      operation: operation
    })
  }

  /**   * 根据中类id，获取中类的关系树
   * @param query 查询 graphql 语法文档
   * @param mediumCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getMediumCategoryTree(
    mediumCategoryId: string,
    query: DocumentNode = GraphqlImporter.getMediumCategoryTree,
    operation?: string
  ): Promise<Response<MediumCategoryTreeResponse>> {
    return commonRequestApi<MediumCategoryTreeResponse>(SERVER_URL, {
      query: query,
      variables: { mediumCategoryId },
      operation: operation
    })
  }

  /**   * 根据小类id，获取指定小类下的职业信息
   * @param query 查询 graphql 语法文档
   * @param smallCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getProfessionListBySmallCategoryId(
    smallCategoryId: string,
    query: DocumentNode = GraphqlImporter.getProfessionListBySmallCategoryId,
    operation?: string
  ): Promise<Response<Array<ProfessionResponse>>> {
    return commonRequestApi<Array<ProfessionResponse>>(SERVER_URL, {
      query: query,
      variables: { smallCategoryId },
      operation: operation
    })
  }

  /**   * 根据中类id，获取指定中类下的小类信息
   * @param query 查询 graphql 语法文档
   * @param mediumCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSmallCategoryListByMediumCategoryId(
    mediumCategoryId: string,
    query: DocumentNode = GraphqlImporter.getSmallCategoryListByMediumCategoryId,
    operation?: string
  ): Promise<Response<Array<SmallCategoryResponse>>> {
    return commonRequestApi<Array<SmallCategoryResponse>>(SERVER_URL, {
      query: query,
      variables: { mediumCategoryId },
      operation: operation
    })
  }

  /**   * 根据小类id，获取小类的关系树
   * @param query 查询 graphql 语法文档
   * @param smallCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSmallCategoryTree(
    smallCategoryId: string,
    query: DocumentNode = GraphqlImporter.getSmallCategoryTree,
    operation?: string
  ): Promise<Response<SmallCategoryTreeResponse>> {
    return commonRequestApi<SmallCategoryTreeResponse>(SERVER_URL, {
      query: query,
      variables: { smallCategoryId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createBigCategory(
    request: BigCategoryCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createBigCategory,
    operation?: string
  ): Promise<Response<BigCategoryResponse>> {
    return commonRequestApi<BigCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**   * 根据一个工种大类树，创建一个大类以及底下的中类，小类，职业等信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createBigCategoryTree(
    request: BigCategoryTreeCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createBigCategoryTree,
    operation?: string
  ): Promise<Response<BigCategoryTreeResponse>> {
    return commonRequestApi<BigCategoryTreeResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createMediumCategory(
    request: MediumCategoryCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createMediumCategory,
    operation?: string
  ): Promise<Response<MediumCategoryResponse>> {
    return commonRequestApi<MediumCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**   * 根据一个工种中类树，创建一个中类以及底下的小类，职业等信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createMediumCategoryTree(
    request: MediumCategoryCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createMediumCategoryTree,
    operation?: string
  ): Promise<Response<MediumCategoryTreeResponse>> {
    return commonRequestApi<MediumCategoryTreeResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createProfession(
    request: ProfessionCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createProfession,
    operation?: string
  ): Promise<Response<ProfessionResponse>> {
    return commonRequestApi<ProfessionResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createSmallCategory(
    request: SmallCategoryCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createSmallCategory,
    operation?: string
  ): Promise<Response<SmallCategoryResponse>> {
    return commonRequestApi<SmallCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**   * 根据一个工种小类树，创建一个小类以及底下的职业等信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createSmallCategoryTree(
    request: SmallCategoryCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createSmallCategoryTree,
    operation?: string
  ): Promise<Response<SmallCategoryTreeResponse>> {
    return commonRequestApi<SmallCategoryTreeResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param bigCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async diableBigCategory(
    bigCategoryId: string,
    mutate: DocumentNode = GraphqlImporter.diableBigCategory,
    operation?: string
  ): Promise<Response<BigCategoryResponse>> {
    return commonRequestApi<BigCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { bigCategoryId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param professionId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async diableProfession(
    professionId: string,
    mutate: DocumentNode = GraphqlImporter.diableProfession,
    operation?: string
  ): Promise<Response<ProfessionResponse>> {
    return commonRequestApi<ProfessionResponse>(SERVER_URL, {
      query: mutate,
      variables: { professionId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param mediumCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableMediumCategory(
    mediumCategoryId: string,
    mutate: DocumentNode = GraphqlImporter.disableMediumCategory,
    operation?: string
  ): Promise<Response<MediumCategoryResponse>> {
    return commonRequestApi<MediumCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { mediumCategoryId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param smallCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableSmallCategory(
    smallCategoryId: string,
    mutate: DocumentNode = GraphqlImporter.disableSmallCategory,
    operation?: string
  ): Promise<Response<SmallCategoryResponse>> {
    return commonRequestApi<SmallCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { smallCategoryId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param smallCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableSmallCategory(
    smallCategoryId: string,
    mutate: DocumentNode = GraphqlImporter.enableSmallCategory,
    operation?: string
  ): Promise<Response<SmallCategoryResponse>> {
    return commonRequestApi<SmallCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { smallCategoryId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param bigCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableBigCategory(
    bigCategoryId: string,
    mutate: DocumentNode = GraphqlImporter.enableBigCategory,
    operation?: string
  ): Promise<Response<BigCategoryResponse>> {
    return commonRequestApi<BigCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { bigCategoryId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param mediumCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableMediumCategory(
    mediumCategoryId: string,
    mutate: DocumentNode = GraphqlImporter.enableMediumCategory,
    operation?: string
  ): Promise<Response<MediumCategoryResponse>> {
    return commonRequestApi<MediumCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { mediumCategoryId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param professionId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableProfession(
    professionId: string,
    mutate: DocumentNode = GraphqlImporter.enableProfession,
    operation?: string
  ): Promise<Response<ProfessionResponse>> {
    return commonRequestApi<ProfessionResponse>(SERVER_URL, {
      query: mutate,
      variables: { professionId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param bigCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeBigCategory(
    bigCategoryId: string,
    mutate: DocumentNode = GraphqlImporter.removeBigCategory,
    operation?: string
  ): Promise<Response<BigCategoryResponse>> {
    return commonRequestApi<BigCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { bigCategoryId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param mediumCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeMediumCategory(
    mediumCategoryId: string,
    mutate: DocumentNode = GraphqlImporter.removeMediumCategory,
    operation?: string
  ): Promise<Response<MediumCategoryResponse>> {
    return commonRequestApi<MediumCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { mediumCategoryId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param professionId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeProfession(
    professionId: string,
    mutate: DocumentNode = GraphqlImporter.removeProfession,
    operation?: string
  ): Promise<Response<ProfessionResponse>> {
    return commonRequestApi<ProfessionResponse>(SERVER_URL, {
      query: mutate,
      variables: { professionId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param smallCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeSmallCategory(
    smallCategoryId: string,
    mutate: DocumentNode = GraphqlImporter.removeSmallCategory,
    operation?: string
  ): Promise<Response<SmallCategoryResponse>> {
    return commonRequestApi<SmallCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { smallCategoryId },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateBigCategory(
    request: BigCategoryUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateBigCategory,
    operation?: string
  ): Promise<Response<BigCategoryResponse>> {
    return commonRequestApi<BigCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateMediumCategory(
    request: MediumCategoryUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateMediumCategory,
    operation?: string
  ): Promise<Response<MediumCategoryResponse>> {
    return commonRequestApi<MediumCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateProfession(
    request: ProfessionUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateProfession,
    operation?: string
  ): Promise<Response<ProfessionResponse>> {
    return commonRequestApi<ProfessionResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateSmallCategory(
    request: SmallCategoryUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateSmallCategory,
    operation?: string
  ): Promise<Response<SmallCategoryResponse>> {
    return commonRequestApi<SmallCategoryResponse>(SERVER_URL, {
      query: mutate,
      variables: { request },
      operation: operation
    })
  }
}

export default new DataGateway()
