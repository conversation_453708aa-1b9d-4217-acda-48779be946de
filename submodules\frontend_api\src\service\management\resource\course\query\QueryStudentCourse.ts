import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CourseRequest,
  CourseResponse,
  StudentCourseLearningCommonRequest,
  StudentCourseLearningRangeRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import DataResolve from '@api/service/common/utils/DataResolve'
import { Page, UiPage } from '@hbfe/common'
import { uniq } from 'lodash'
import StudentCourseVo from './vo/StudentCourseVo'
class QueryStudentCourse {
  private async getCoursePageMap(idList: Array<string>): Promise<Map<string, CourseResponse>> {
    const resourceCourse = await this.queryStudentCoursePageByIdList(Array.from(idList))
    const resultMap = new Map<string, CourseResponse>()
    resourceCourse.forEach((course: CourseResponse) => {
      resultMap.set(course.id, course)
    })
    return resultMap
  }

  async queryStudentCoursePageByIdList(idList: Array<string>): Promise<Array<CourseResponse>> {
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const request = new CourseRequest()
    request.courseIdList = uniq(idList)
    let result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
      page,
      request
    })
    if (result.data.totalSize) {
      page.pageSize = result.data.totalSize
      result = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageCourseInServicer({
        page,
        request
      })
    }
    return result.data.currentPageData
  }
  /**
   * 查询学员培训班课程分页（选课规则）
   * @param {Page} page - 分页参数
   * @param {string} studentNo - 学号
   */
  async queryStudentCoursePage(
    page: Page,
    studentNo: string,
    courseLearningResourceType = 1
  ): Promise<Array<StudentCourseVo>> {
    const request = new StudentCourseLearningCommonRequest()
    let result = [] as StudentCourseVo[]
    request.studentNo = studentNo
    request.studentCourseLearningRange = new StudentCourseLearningRangeRequest()
    request.studentCourseLearningRange.courseLearningResourceType = courseLearningResourceType
    const response = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.pageStudentCourseInServicer({
      page,
      request
    })
    page.totalSize = response.data.totalSize ?? 0
    page.totalPageSize = response.data.totalPageSize ?? 0
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = response.data.currentPageData.map(StudentCourseVo.from)
    }
    return result
  }
}
export default QueryStudentCourse
