<template>
  <div>
    <el-table
      :data="updateItem.uiBindCourseList"
      row-key="id"
      ref="dragWebTable"
      max-height="400px"
      class="m-table"
      stripe
      v-loading="loading"
    >
      <el-table-column prop="index" label="No." align="center" width="60">
        <template slot-scope="scope">
          <span :data-index="scope.$index + 1" v-observe-visibility="visibleCourseList">
            {{ scope.$index + 1 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="排序" width="70" align="center">
        <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
      </el-table-column>
      <!-- <el-table-column label="排序" width="70" align="center" class-name="hb-iconfont"></el-table-column> -->

      <el-table-column prop="name" label="课程名称" min-width="300"></el-table-column>

      <el-table-column prop="physicsPeriod" label="物理学时" min-width="100" align="center"></el-table-column>
      <el-table-column prop="period" label="学习学时" min-width="100" align="center">
        <template slot-scope="scope">
          <el-input-number
            :min="0.1"
            class="f-input-num"
            :precision="1"
            style="width: 70%"
            v-model="scope.row.period"
            :max="99999"
            size="mini"
            :controls="false"
            @focus="getPeriod(scope.row)"
            @blur="updatePeriod(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column label="操作" width="140" align="center">
        <template slot-scope="scope">
          <el-button size="mini" @click="previewCourse(scope.row.id)" type="text">预览</el-button>
          <el-button v-if="showOperation" size="mini" @click="cancelChoose(scope.row)" type="text"> 取消 </el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="调整学时" :visible.sync="dialogChangePeriod">
      <el-row :gutter="15">
        <el-form
          size="medium"
          ref="changePeriodForm"
          label-width="140px"
          :model="changePeriodModel"
          :rules="periodRule"
        >
          <el-col :span="20">
            <el-form-item label="学习学时">
              <span class="ci fs16">{{ changePeriodModel.originPeriodValue }}</span
              >学时
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="选课学时" prop="changePeriodValue">
              <el-input
                v-model="changePeriodModel.changePeriodValue"
                placeholder="请输入调整学时数"
                clearable
                :style="{ width: '100%' }"
              >
              </el-input>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="saveChangePeriod(true)">保 存</el-button>
        <el-button @click="saveChangePeriod(false)">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style lang="scss">
  td.hb-iconfont {
    cursor: move;

    &:hover {
      background: #bfbfbf !important;
      color: white;
    }

    &::after {
      content: '::';
    }
  }
</style>
<script lang="ts">
  import { Component, Emit, Prop, Watch, Ref, Mixins, PropSync } from 'vue-property-decorator'
  import { ResponseStatus } from '@hbfe/common'
  import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'
  import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
  import Sortable from 'sortablejs'
  import { ElTable } from 'element-ui/types/table'
  import PreviewCourseMixins from '@hbfe/jxjy-admin-common/src/mixins/PreviewCourseMixins'
  import UpdateCoursePackageByPageVo from '@api/service/management/resource/course-package/mutation/vo/UpdateCoursePackageByPageVo'

  /**
   * 调整学时使用的model
   */
  class ChangePeriodModel {
    changePeriodValue = 0
    originPeriodValue = 0
    index = 0
    id = ''
  }

  @Component
  export default class ModifyChooseCourse extends Mixins(PreviewCourseMixins) {
    // 保存学时的方法

    @Ref('dragWebTable')
    dragWebTable: any
    /**
     * 初始学时
     */
    initialValue = 0

    @Watch('updateItem.uiBindCourseList', {
      immediate: true,
      deep: true
    })
    uiBindCourseListChange(val: any) {
      if (val) {
        this.$nextTick(() => {
          this.dragElement()
        })
        console.log('uiBindCourseListChange', val, this.updateItem.uiBindCourseList)
      }
    }

    dragElement() {
      const el = this.dragWebTable.$el.querySelector('.el-table__body-wrapper > table > tbody') as HTMLElement
      return new Sortable(el, {
        // Class name for the drop placeholder,// 抓取的元素的类名，简单来说就是抓取元素的时候给他加一个类名blue-background-class
        ghostClass: 'blue-background-class',
        handle: '.hb-iconfont',
        onEnd: ({ newIndex, oldIndex }) => {
          // 找到要移动的项
          const curRow = this.updateItem.uiBindCourseList.splice(oldIndex, 1)[0]
          //
          this.updateItem.uiBindCourseList.splice(newIndex, 0, curRow)
          // 下面这段代码没啥用，会导致拖拽排序后返回第一项，如果有bug可以尝试开启这段代码
          /*const newArray = this.createCoursePackage.addedList.slice(0)
          this.createCoursePackage.addedList = []
          this.$nextTick(function() {
            this.createCoursePackage.addedList = newArray
          })*/
        }
      })
    }

    @PropSync('updateCoursePackage', {
      type: UpdateCoursePackageByPageVo
    })
    updateItem!: UpdateCoursePackageByPageVo

    @Prop({
      type: Function,
      required: false,
      default: function () {
        return new ResponseStatus(200, '')
      }
    })
    savePeriod: Function
    // 是否展示操作相关的按钮

    @Prop({
      type: Boolean,
      required: false,
      default: true
    })
    showOperation: boolean

    @Prop({
      type: Array,
      required: false
    })
    chooseData: Array<CourseListDetail>

    @Watch('chooseData', {
      immediate: false,
      deep: true
    })
    chooseDataChange(val: any) {
      if (val) {
        console.log(val, '已选待确认课程')
      }
    }

    periodRule = {
      changePeriodValue: [
        { required: true, message: '学时不能为空' },
        {
          pattern: /^(\d){1,}(\.5)?$/,
          message: '学时最小一位小数,0.5的倍数',
          trigger: ['change', 'blur']
        },
        {
          validator: this.checkPeriod,
          trigger: 'change'
        }
      ]
    }

    // 调整学时弹窗控制
    dialogChangePeriod = false
    // 调整学时的model
    changePeriodModel = new ChangePeriodModel()

    // 动画加载
    loading = false

    checkPeriod(rule: any, value: number, callback: any) {
      if (value <= 0) {
        callback(new Error('学时必须大于0'))
      }
      callback()
    }

    /**
     * 保存调整学时
     * @param operation
     */
    saveChangePeriod(operation: boolean) {
      if (operation) {
        const el: any = this.$refs.changePeriodForm
        el.validate(async (valid: any) => {
          if (valid) {
            // const courses = new Array<CourseInPoolOperate>()
            // const course = new CourseInPoolOperate()
            // course.courseId = this.changePeriodModel.id
            // course.quantitative = -1
            // course.sequence = this.changePeriodModel.index
            // course.period = this.changePeriodModel.changePeriodValue
            // courses.push(course)
            // const status = await this.savePeriod(courses)
            // if (status.isSuccess()) {
            // CoursePoolUIModule.CHANGE_COURSE_PERIOD_BY_INDEX({
            //   index: this.changePeriodModel.index,
            //   period: this.changePeriodModel.changePeriodValue
            // })
            //   if (status.message) {
            //     this.$message.success(status.message)
            //   }
            // } else {
            //   this.$message.error('修改学时失败！')
            // }
            this.dialogChangePeriod = false
          }
        })
      } else {
        this.dialogChangePeriod = false
      }
    }

    /**
     * 取消选择
     * @param item
     */
    cancelChoose(item: CourseInCoursePackage) {
      // CoursePoolUIModule.SPLICE_COURSE_BY_ID(data.courseId)
      // // 调用取消选择事件发送，通知父组件
      // this.sendSpliceWaitChooseCourseEvent(data)
      // this.updateItem.cancelChoose(item)

      // const index = this.updateItem.uiBindCourseList.findIndex(itm => {
      //   if (itm.id == item.id) {
      //     this.updateItem.cancelChoose(item)
      //     this.$emit('packageChange')
      //   }
      // })

      const target = this.updateItem.uiBindCourseList.find((itm) => itm.id === item.id)
      if (target) {
        this.updateItem.cancelChoose(item)
        this.$emit('packageChange')
        this.$emit('reductionPeriod', item.period)
      }
    }

    /**
     * 滚动加载课程列表
     * @param isVisible
     * @param entry
     */
    async visibleCourseList(isVisible: boolean, entry: any) {
      const scopeIndex = entry.target.dataset.index
      // console.log('scopeIndex', scopeIndex)
      if (isVisible) {
        if (parseInt(scopeIndex) >= this.updateItem.totalSize) {
          // 最大值时不请求
          return
        }
        if (parseInt(scopeIndex) === this.updateItem.uiBindCourseList.length) {
          this.loading = true
          await this.updateItem.loadMore()
          // 用于解决滚动加载后列表重绘异常问题
          this.dragWebTable && this.dragWebTable.doLayout()
          this.loading = false
        }
      }
    }

    // 修复period清空回显问题
    updatePeriod(row: CourseInCoursePackage) {
      if (isNaN(row?.period)) row.period = 0.1
      if (row.period === this.initialValue) {
        this.$emit('updatePeriod', 0)
      } else {
        const emitPeriod = row.period - this.initialValue
        this.$emit('updatePeriod', emitPeriod)
      }
    }
    /**
     * 获取初始学时
     */
    getPeriod(row: CourseInCoursePackage) {
      this.initialValue = row.period
    }
  }
</script>
