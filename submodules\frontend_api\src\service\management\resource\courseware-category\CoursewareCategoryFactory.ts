import MutationBizCoursewareCategory from '@api/service/management/resource/courseware-category/mutation/MutationBizCoursewareCategory'
import MutationCreateCoursewareCategory from '@api/service/management/resource/courseware-category/mutation/MutationCreateCoursewareCategory'
import MutationUpdateCoursewareCategory from '@api/service/management/resource/courseware-category/mutation/MutationUpdateCoursewareCategory'
import QueryCoursewareCategory from '@api/service/management/resource/courseware-category/query/QueryCoursewareCategory'
import AbstractStandardFactory from '@api/service/management/resource/AbstractStandardFactory'

class CoursewareCategoryFactory extends AbstractStandardFactory<
  QueryCoursewareCategory,
  MutationBizCoursewareCategory,
  MutationCreateCoursewareCategory,
  MutationUpdateCoursewareCategory
> {
  mutationCreate(): MutationCreateCoursewareCategory {
    return new MutationCreateCoursewareCategory()
  }

  mutationBiz(id: string): MutationBizCoursewareCategory {
    return new MutationBizCoursewareCategory(id)
  }

  query: QueryCoursewareCategory = new QueryCoursewareCategory()

  async mutationUpdate(id: string): Promise<MutationUpdateCoursewareCategory> {
    const detail = await this.query.queryById(id)
    const mutation = new MutationUpdateCoursewareCategory()
    mutation.UpdateCoursewareCategoryDto.from(detail)
    return mutation
  }
}

export default CoursewareCategoryFactory
