import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-administrator-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-administrator-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 添加地区管理员
<AUTHOR> [2023/3/20 16:38]
 */
export class RegionAdministratorAddRequest {
  /**
   * 姓名
   */
  name: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 认证标识【必填】默认为行政区划代码
   */
  area: string
  /**
   * 账号
   */
  account: string
}

/**
 * 修改地区管理员
<AUTHOR> [2023/3/20 16:41]
 */
export class RegionAdministratorUpdateRequest {
  /**
   * 账号
   */
  name: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 行政区划
   */
  theirArea: string
  /**
   * 账号Id
   */
  accountId: string
}

/**
 * <AUTHOR> [2023/7/11 20:54]
 */
export class GenernalResponse {
  /**
   * 状态码
@see CommonStatusEnum
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 添加地区管理员
   * @param request 添加地区管理员请求
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addRegionAdministrator(
    request: RegionAdministratorAddRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.addRegionAdministrator,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改地区管理员
   * @param request 修改地区管理员请求
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateRegionAdministrator(
    request: RegionAdministratorUpdateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateRegionAdministrator,
    operation?: string
  ): Promise<Response<GenernalResponse>> {
    return commonRequestApi<GenernalResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
