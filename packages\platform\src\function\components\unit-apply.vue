<template>
  <div class="f-p15">
    <el-tabs v-model="activeName2" type="card" class="m-tab-card">
      <el-tab-pane label="线上集体报名" name="first">
        <online-unit-apply :activeName.sync="activeName2" :TemplateModuleObj="TemplateModuleObj"></online-unit-apply>
      </el-tab-pane>
      <el-tab-pane label="线下集体报名" name="second">
        <offline-unit-apply :activeName.sync="activeName2" :TemplateModuleObj="TemplateModuleObj"></offline-unit-apply>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import HbUploadFile from '@hbfe/jxjy-admin-components/src/hb-upload-file.vue'
  import OnlineUnitApply from '@hbfe/jxjy-admin-platform/src/function/components/online-unit-apply.vue'
  import OfflineUnitApply from '@hbfe/jxjy-admin-platform/src/function/components/offline-unit-apply.vue'
  import TemplateModule from '@api/service/common/template-school/TemplateModule'
  import TemplateItem from '@api/service/common/template-school/TemplateItem'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'

  @Component({
    components: { HbUploadFile, OnlineUnitApply, OfflineUnitApply }
  })
  export default class extends Vue {
    /**
     * 模板
     */
    TemplateModuleObj = new TemplateItem()
    //文件上传之后的回调参数
    hbFileUploadResponse = new HBFileUploadResponse()

    activeName2 = 'first'
    created() {
      const webPortalTemplateId = OnlineSchoolConfigModule.queryPortal.webPortalInfo.webTemplateId
      // PC
      this.TemplateModuleObj = TemplateModule.getTemplate(webPortalTemplateId)
    }
  }
</script>
