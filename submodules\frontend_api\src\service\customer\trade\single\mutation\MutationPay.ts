import msOrder, {
  // Commodity,
  KeyValueDataRequest,
  OrderOnlinePaymentRequest
  //   PreparePayRequest,
  //   PreparePayResponse
} from '@api/ms-gateway/ms-order-v1'
import XYPayExtendParam from '@api/service/customer/trade/single/mutation/customer-order/vo/pay-order/XYPayExtendParam'
import JSPayExtendParam from '@api/service/customer/trade/single/mutation/customer-order/vo/pay-order/JSPayExtendParam'
// import axios from 'axios'
// 支付工具

// 微信H5支付（外部浏览器支付）
// ---------------------------------------------------------------------------------------------------

class WeiXinWapH5PayParamH5Info {
  type: string
  wap_url: string
  wap_name: string

  constructor(type: string, wap_url: string, wap_name: string) {
    this.type = type
    this.wap_name = wap_name
    this.wap_url = wap_url
  }
}

class WeiXinWapH5PayParamSceneInfo {
  h5_info: WeiXinWapH5PayParamH5Info

  constructor(type: string, wap_url: string, wap_name: string) {
    this.h5_info = new WeiXinWapH5PayParamH5Info(type, wap_url, wap_name)
  }
}

class WeiXinWapH5PayParam {
  clientIp: string
  scene_info: WeiXinWapH5PayParamSceneInfo
  // clientIp: string,
  constructor(type: string, wap_url: string, wap_name: string) {
    // this.clientIp = clientIp
    this.scene_info = new WeiXinWapH5PayParamSceneInfo(type, wap_url, wap_name)
  }
}

class ClientDomainInfo {
  getClientDomainInfo() {
    const href = window.location.href
    let index = href.indexOf('#/')
    let end = 0
    const start = href.indexOf('//') + 2
    let domain = ''
    let rootPath = ''
    if (index >= 0) {
      end = index
      domain = href.substring(start, end)
      rootPath = href.substring(0, end + 2)
    } else {
      index = href.indexOf('?')
      if (index >= 0) {
        end = index
        domain = href.substring(start, end)
        rootPath = href.substring(0, end)
      } else {
        end = href.length
        domain = href.substring(start, end)
        rootPath = href.substring(0, end)
      }
    }
    return {
      domain: domain,
      rootPath: rootPath
    }
  }
}
export default class MutationPay {
  /*
   *  web支付
   * */
  async pay(onlinePaymentRequest: OrderOnlinePaymentRequest) {
    onlinePaymentRequest.paymentProperties = []
    const key = new KeyValueDataRequest()
    key.key = 'method'
    if (onlinePaymentRequest.paymentChannelId == 'ALIPAY') {
      key.value = 'alipay.trade.page.pay'
    } else if (onlinePaymentRequest.paymentChannelId == 'WXPAY') {
      key.value = 'native'
    } else if (onlinePaymentRequest.paymentChannelId == 'CIB_PAY') {
      key.value = 'unifiedTradeNative'
    } else if (onlinePaymentRequest.paymentChannelId == 'CCB_PAY') {
      key.value = 'unifiedTradeNative'
    } else if (onlinePaymentRequest.paymentChannelId == 'SWIFT_PASS_PAY') {
      key.value = 'unifiedTradeNative'
      onlinePaymentRequest.paymentProperties.push({ key: 'mch_create_ip', value: '1' })
    } else if (onlinePaymentRequest.paymentChannelId == 'NEW_LAND_PAY') {
      key.value = 'unifiedTradeNative'
    }
    onlinePaymentRequest.paymentProperties.push(key)
    const res = await msOrder.onlinePayOrder(onlinePaymentRequest)
    return res
  }
  /*
   *  移动端支付
   * */
  async wxPay(
    onlinePaymentRequest: OrderOnlinePaymentRequest,
    isWeiXin: boolean,
    openId: string,
    xyExtend?: XYPayExtendParam
  ) {
    onlinePaymentRequest.paymentProperties = []
    const method = new KeyValueDataRequest()
    method.key = 'method'
    method.value = 'h5'
    if (onlinePaymentRequest.paymentChannelId == 'ALIPAY') {
      method.value = 'alipay.trade.wap.pay'
      onlinePaymentRequest.paymentProperties.push(method)
      const res = await msOrder.onlinePayOrder(onlinePaymentRequest)
      return res
    }
    if (onlinePaymentRequest.paymentChannelId == 'CIB_PAY') {
      if (openId) {
        method.value = 'payWeixinJspay'
        const sub_openid = new KeyValueDataRequest()
        const is_raw = new KeyValueDataRequest()
        const is_minipg = new KeyValueDataRequest()
        sub_openid.key = 'sub_openid'
        sub_openid.value = openId

        is_raw.key = 'is_raw'
        is_raw.value = xyExtend.is_raw

        is_minipg.key = 'is_minipg'
        is_minipg.value = xyExtend.is_minipg
        onlinePaymentRequest.paymentProperties.push(sub_openid)
        onlinePaymentRequest.paymentProperties.push(is_raw)
        onlinePaymentRequest.paymentProperties.push(is_minipg)
      } else {
        method.value = 'unifiedTradeNative'
      }
      onlinePaymentRequest.paymentProperties.push(method)
      const res = await msOrder.onlinePayOrder(onlinePaymentRequest)
      return res
    }
    if (onlinePaymentRequest.paymentChannelId == 'CCB_PAY') {
      method.value = 'unifiedTradeNative'
      onlinePaymentRequest.paymentProperties.push(method)
      const res = await msOrder.onlinePayOrder(onlinePaymentRequest)
      return res
    }
    if (onlinePaymentRequest.paymentChannelId == 'SWIFT_PASS_PAY') {
      method.value = 'unifiedTradeNative'
      onlinePaymentRequest.paymentProperties.push(method)
      onlinePaymentRequest.paymentProperties.push({ key: 'mch_create_ip', value: '1' })
      const res = await msOrder.onlinePayOrder(onlinePaymentRequest)
      return res
    }
    if (onlinePaymentRequest.paymentChannelId == 'NEW_LAND_PAY') {
      method.value = 'unifiedTradeNative'
      onlinePaymentRequest.paymentProperties.push(method)
      const res = await msOrder.onlinePayOrder(onlinePaymentRequest)
      return res
    }
    if (!isWeiXin) {
      onlinePaymentRequest.paymentProperties.push(method)
      const clientDomainInfo = new ClientDomainInfo().getClientDomainInfo()
      //Wx支付ip适配
      // const config = {
      //   headers: {
      //     Accept: 'application/json',
      //     'Content-Type': 'application/json;charset=UTF-8'
      //   }
      // }
      // const ipInfo = await axios.get('/apiIcanhazip?ie=utf-8', config)
      // const ipKey = new KeyValueDataRequest()
      // const ip = '**************'
      // let ip = ipInfo?.data || '**************'
      // ip = ip.replace('\n', '')
      // ipKey.key = 'clientIp'
      // ipKey.value = ip
      // onlinePaymentRequest.paymentProperties.push(ipKey)
      // if (ipInfo) {
      const extraParam = new KeyValueDataRequest()
      extraParam.key = 'scene_info'
      extraParam.value = JSON.stringify(
        // ip,
        new WeiXinWapH5PayParam('Wap', clientDomainInfo.domain, process.env.VUE_APP_WX_APPNAME)
      )
      onlinePaymentRequest.paymentProperties.push(extraParam)
      // }
    } else {
      if (openId) {
        const method = new KeyValueDataRequest()
        method.key = 'method'
        method.value = 'jsapi'
        onlinePaymentRequest.paymentProperties.push(method)
        const openid = new KeyValueDataRequest()
        openid.key = 'openId'
        openid.value = openId
        onlinePaymentRequest.paymentProperties.push(openid)
      }
    }

    const res = await msOrder.onlinePayOrder(onlinePaymentRequest)
    return res
  }
}
