import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum AchievementExhibitionEnum {
  /**
   * 以订单开通时间为准
   */
  signUpTime = 'signUpTime',
  /**
   * 以首次开始学习时间为准
   */
  trainingStartTime = 'trainingStartTime'
}
class AchievementExhibition extends AbstractEnum<AchievementExhibitionEnum> {
  static enum = AchievementExhibitionEnum
  constructor(status?: AchievementExhibitionEnum) {
    super()
    this.current = status
    this.map.set(AchievementExhibitionEnum.signUpTime, '以订单开通时间为准')
    this.map.set(AchievementExhibitionEnum.trainingStartTime, '以首次开始学习时间为准')
  }
}

export default new AchievementExhibition()
