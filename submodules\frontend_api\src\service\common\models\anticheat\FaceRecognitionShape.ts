/**
 * 人脸识别模式
 */
class FaceRecognitionShape {
  /**
   * 模式配置编号
   */
  id: string
  /**
   * 验证方式，0/1，成功为止/验证次数
   */
  verification: number
  /**
   * 验证次数，当verification=1时有效
   * @see verification
   */
  verificationTimes: number
  /**
   * 匹配相似度
   */
  similarity: number
  /**
   * 协议文本
   */
  protocolText: string
  /**
   * 提示信息文本
   */
  promptText: string
  /**
   * 创建时间
   */
  createTime: Date
}

export default FaceRecognitionShape
