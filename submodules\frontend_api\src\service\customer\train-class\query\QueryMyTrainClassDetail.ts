import CourseLearningForestage, {
  CourseTrainingOutlineResponse,
  LearningRuleSnapshotResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage, {
  CertificateLearningConfigResultResponse,
  default as MsMySchemeQueryFrontGatewayCourseLearningForeStage,
  default as MsSchemeQueryFrontGatewayCourseLearningForestage,
  GradeLearningConfigResultResponse,
  LearningResultResponse,
  MySchemeConfigRequest,
  SchemeConfigResponse,
  StudentSchemeLearningResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import DataResolve from '@api/service/common/utils/DataResolve'
import BatchCourseQueryUtil, {
  OutlineCompulsoryCourseLearningInfo
} from '@api/service/customer/course/query/utils/BatchCourseQueryUtil'
import CalculatorObj from '@api/service/customer/train-class/Utils/CalculatorObj'
import SkuPropertyConvertUtils, {
  ComplexSkuPropertyResponse
} from '@api/service/customer/train-class/Utils/SkuPropertyConvertUtils'
import TrainClassConfigJsonManager from '@api/service/customer/train-class/Utils/TrainClassConfigJsonManager'
import { ClassFicationEnum } from '@api/service/customer/train-class/query/Enum/ClassFicationEnum'
import { LearningProcessEnum } from '@api/service/customer/train-class/query/Enum/LearningProcess'
import { OperationEnum } from '@api/service/customer/train-class/query/Enum/OperationEnum'
import Classification from '@api/service/customer/train-class/query/vo/Classification'
import { Constants } from '@api/service/customer/train-class/query/vo/Constants'
import InterestCourseType from '@api/service/customer/train-class/query/vo/InterestCourseType'
import LearningType from '@api/service/customer/train-class/query/vo/LearningType'
import MyTrainClassDetailClassVo from '@api/service/customer/train-class/query/vo/MyTrainClassDetailClassVo'
import { ResponseStatus } from '@hbfe/common'
import { clone, cloneDeep } from 'lodash'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
import StudentFaceStudyLog from '@api/service/customer/implement/StudentFaceStudyLog'

/**
 * 查询方案培训结果参数
 */
export class QuerySchemeTrainingResultParam {
  /**
   * 培训形式
   */
  trainingMode: TrainingModeEnum = null
  /**
   * 参训资格id
   */
  qualificationId = ''
  /**
   * 学号
   */
  studentNo = ''
}
/**
 * 用户域获取我的培训班详情
 */
class QueryMyTrainClassDetail {
  // region 查询参数
  /**
   * 【必传】培训班参训资格id
   * @description
   */
  qualificationId = ''
  /**
   * 学号
   * @description 培训形式为面网授班、面授班时必传
   */
  studentNo = ''

  // endregion

  // region properties
  jsonString = ''
  /**
   *培训班详情，类型为MyTrainClassDetailClassVo
   */
  trainClassDetail = new MyTrainClassDetailClassVo()

  /**
   * 学员方案学习返回值
   * @description 调用网授班查询接口返回的值
   */
  studentSchemeLearningResponse = new StudentSchemeLearningResponse()
  /**
   * 期别学习详情
   */
  issueLearningDetail = new StudentFaceStudyLog('')

  // endregion
  // region methods

  /**
   * 获取培训班详情 由详情和配置信息组合而成
   */
  async queryTrainClassDetail(): Promise<ResponseStatus> {
    //获取班级详情
    let status = await this.requestClassDetail()
    if (status.isSuccess()) {
      // 获取最新配置
      const res = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getSchemeConfigInServicer({
        schemeId: this.trainClassDetail.trainClassBaseInfo.id
      })
      const schemeConfig: any = TrainClassConfigJsonManager.calculatorSchemeConfigJson(res.data.schemeConfig)
      //获取班级模板配置信息
      // 合格传schemeConfig提供最新配置的大纲
      status =
        this.trainClassDetail.trainClassBaseInfo.trainingResult > 0
          ? await this.requestClassConfig(schemeConfig)
          : await this.requestClassConfig()
      // 读取最新名称
      this.trainClassDetail.trainClassBaseInfo.currentName = schemeConfig.name
      await this.getCurrentConfig(schemeConfig)
    }
    return status
  }

  /**
   * 已合格获取最新配置
   */
  async getCurrentConfig(schemeConfig: any) {
    console.log(this.trainClassDetail.trainClassBaseInfo.trainingResult > 0, '是否合格')
    if (this.trainClassDetail.trainClassBaseInfo.trainingResult > 0) {
      // 获取管理域方案json判断学习方式是否移除/新增
      const courseLearningId = schemeConfig[schemeConfig.type]?.id
      const examLearningId = schemeConfig?.examLearning?.id
      const practiceLearningId = schemeConfig?.practiceLearning?.id
      const interestCourseLearningId = schemeConfig?.interestCourseLearning?.id
      if (this.trainClassDetail.learningTypeModel.courseLearning.learningTypeId !== '' && !courseLearningId)
        this.trainClassDetail.learningTypeModel.courseLearning.operation = OperationEnum.REMOVE
      if (this.trainClassDetail.learningTypeModel.exam.learningTypeId !== '' && !examLearningId)
        this.trainClassDetail.learningTypeModel.exam.operation = OperationEnum.REMOVE
      if (this.trainClassDetail.learningTypeModel.practiceLearning.learningTypeId !== '' && !practiceLearningId)
        this.trainClassDetail.learningTypeModel.practiceLearning.operation = OperationEnum.REMOVE
      if (this.trainClassDetail.learningTypeModel.interestCourse.learningTypeId !== '' && !interestCourseLearningId)
        this.trainClassDetail.learningTypeModel.interestCourse.operation = OperationEnum.REMOVE

      const { schemeAssessItem, issueAssessItem } = Scheme.parseTrainClassAssess(schemeConfig)
      if (schemeAssessItem) {
        if (schemeAssessItem.learningResults) {
          //获取培训成果中学分
          const creditLearningResult = schemeAssessItem.learningResults.find(
            (learnResult: any) => learnResult.type == 1
          )
          this.trainClassDetail.trainClassBaseInfo.period =
            creditLearningResult.grade || this.trainClassDetail.trainClassBaseInfo.period
          this.trainClassDetail.trainClassBaseInfo.creditId =
            creditLearningResult.id || this.trainClassDetail.trainClassBaseInfo.creditId
          //配置最新证明模板
          const templateResult = schemeAssessItem.learningResults.find((learnResult: any) => learnResult.type == 2)
          if (templateResult) {
            this.trainClassDetail.trainClassBaseInfo.hasLearningResult = true
            this.trainClassDetail.trainClassBaseInfo.learningResultId = templateResult.certificateTemplateId
            this.trainClassDetail.trainClassBaseInfo.learningResultAchievementsId = templateResult.id
            this.trainClassDetail.trainClassBaseInfo.openPrintTemplate = templateResult.openPrintTemplate
          } else {
            this.trainClassDetail.trainClassBaseInfo.hasLearningResult = false
          }
        }
      }
      if (issueAssessItem) {
        this.trainClassDetail.learningTypeModel.issue.assessId = issueAssessItem.id
        this.trainClassDetail.learningTypeModel.issue.assessName = issueAssessItem.name
      }

      // 课程评价
      if (schemeConfig[schemeConfig.type]?.config?.courseAppraisalConfig) {
        const courseAppraisalConfig = schemeConfig[schemeConfig.type].config.courseAppraisalConfig
        this.trainClassDetail.learningTypeModel.courseLearning.enableAppraisal =
          courseAppraisalConfig?.enableAppraisal ||
          this.trainClassDetail.learningTypeModel.courseLearning.enableAppraisal
        this.trainClassDetail.learningTypeModel.courseLearning.enableCompulsoryAppraisal =
          courseAppraisalConfig?.enableCompulsoryAppraisal ||
          this.trainClassDetail.learningTypeModel.courseLearning.enableCompulsoryAppraisal
        this.trainClassDetail.learningTypeModel.courseLearning.preconditionCourseSchedule =
          courseAppraisalConfig?.preconditionCourseSchedule ||
          this.trainClassDetail.learningTypeModel.courseLearning.preconditionCourseSchedule
      }
      /**
       * 已合格读取管理端最新兴趣课程
       */
      this.trainClassDetail.learningTypeModel.interestCourse = new InterestCourseType()
      TrainClassConfigJsonManager.configInterest(this.trainClassDetail.learningTypeModel, schemeConfig)
      // 心得
      if (!schemeConfig.learningExperienceLearning) {
        this.trainClassDetail.learningTypeModel.learningExperience.isSelected = false
      }
      if (schemeConfig.practiceLearning) {
        TrainClassConfigJsonManager.configPractice(this.trainClassDetail.learningTypeModel, schemeConfig)
      }
      // /**
      //  * 已合格读取最新分类（自主选课）
      //  */
      // if (
      //   this.studentSchemeLearningResponse.scheme.schemeType === 'autonomousCourseLearning' &&
      //   schemeConfig?.autonomousCourseLearning
      // ) {
      //   this.trainClassDetail.learningTypeModel.courseLearning.classification = new Classification()
      //   const courseTrainingOutlines = schemeConfig.autonomousCourseLearning.config.courseTrainingOutlines
      //   TrainClassConfigJsonManager.updateClassification(
      //     this.trainClassDetail.learningTypeModel.courseLearning,
      //     courseTrainingOutlines
      //   )
      // }
      /**
       * 考试是否开放题析读取最新
       */
      const exam = schemeConfig.examLearning
      if (exam) {
        if (exam.config.gradesWhetherHide === undefined) {
          this.trainClassDetail.learningTypeModel.exam.gradesWhetherHide = true
        } else {
          this.trainClassDetail.learningTypeModel.exam.gradesWhetherHide = exam.config.gradesWhetherHide
        }
        // 考试方式 和 考试固定时间
        if (exam.config.examType === undefined) {
          this.trainClassDetail.learningTypeModel.exam.examPattern = 0
        } else {
          this.trainClassDetail.learningTypeModel.exam.examPattern = exam.config.examType
          this.trainClassDetail.learningTypeModel.exam.allowStartTime = exam.config.allowStartTime
          this.trainClassDetail.learningTypeModel.exam.allowEndTime = exam.config.allowEndTime
        }

        this.trainClassDetail.learningTypeModel.exam.openDissects = exam.config.openDissects
      }
    }
  }
  //获取班级模板配置信息
  async requestClassConfig(schemeConfig?: any) {
    //获取培训班配置模板jsonString
    const request = new MySchemeConfigRequest()
    request.qualificationId = this.qualificationId
    request.isIncludeUnableOutlineId = this.studentSchemeLearningResponse?.studentLearning?.trainingResult !== 1
    // const res = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getMySchemeConfigByRequest({ request })
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getMySchemeConfig({
      qualificationId: this.qualificationId
    })
    // console.log('班级详情内的配置json', res.data.schemeConfig)
    if (res.status.isSuccess()) {
      const trainClassDetail = this.trainClassDetail
      const commodityDetail: SchemeConfigResponse = res.data
      this.jsonString = commodityDetail.schemeConfig
      const classConfigJson: any = TrainClassConfigJsonManager.calculatorSchemeConfigJson(commodityDetail.schemeConfig)
      if (classConfigJson) {
        const commodityJson = classConfigJson.commoditySale
        this.trainClassDetail.trainClassBaseInfo.picture = classConfigJson.picture
        this.trainClassDetail.price = commodityJson.price
        this.trainClassDetail.trainClassBaseInfo.name = commodityJson.saleTitle
        this.trainClassDetail.commoditySkuId = commodityJson.id
        trainClassDetail.trainClassBaseInfo.provideRelearn = classConfigJson.provideRelearn
        trainClassDetail.trainClassBaseInfo.registerBeginDate = classConfigJson.registerBeginDate
        trainClassDetail.trainClassBaseInfo.registerEndDate = classConfigJson.registerEndDate
        trainClassDetail.trainClassBaseInfo.trainingBeginDate = classConfigJson.trainingBeginDate
        trainClassDetail.trainClassBaseInfo.trainingEndDate = classConfigJson.trainingEndDate
        trainClassDetail.trainClassBaseInfo.introContent = classConfigJson.introContent
        trainClassDetail.trainClassBaseInfo.introId = classConfigJson.introId
        trainClassDetail.trainClassBaseInfo.notice = classConfigJson.notice
        trainClassDetail.trainClassBaseInfo.issueNotice = classConfigJson.issueNotice
        const getExtendProperties = (key: string) =>
          classConfigJson.extendProperties.find((item: any) => item.name == key)?.value
        trainClassDetail.trainClassBaseInfo.showNoticeDialog = getExtendProperties('showNoticeDialog')

        this.trainClassDetail.taxCode = classConfigJson.commoditySale.taxCode
        this.trainClassDetail.categoryId = classConfigJson.commoditySale.categoryId

        const { schemeAssessItem, issueAssessItem } = Scheme.parseTrainClassAssess(classConfigJson)
        if (schemeAssessItem) {
          //配置培训模板
          if (schemeAssessItem.learningResults) {
            const templateResult = schemeAssessItem.learningResults.find((learnResult: any) => learnResult.type == 2)
            if (templateResult) {
              this.trainClassDetail.trainClassBaseInfo.hasLearningResult = true
              this.trainClassDetail.trainClassBaseInfo.learningResultId = templateResult.certificateTemplateId
              this.trainClassDetail.trainClassBaseInfo.learningResultAchievementsId = templateResult.id

              this.trainClassDetail.trainClassBaseInfo.openPrintTemplate = templateResult.openPrintTemplate
            }
            const creditResult = schemeAssessItem.learningResults.find((learnResult: any) => learnResult.type == 1)
            if (creditResult) {
              this.trainClassDetail.trainClassBaseInfo.period = creditResult.grade
            }
          }
        }
        if (issueAssessItem) {
          this.trainClassDetail.learningTypeModel.issue.assessId = issueAssessItem.id
          this.trainClassDetail.learningTypeModel.issue.assessName = issueAssessItem.name
        }

        //classConfigJson.type == 'autonomousCourseLearning'
        if (classConfigJson.type == 'chooseCourseLearning') {
          this.trainClassDetail.trainClassBaseInfo.schemeType = 1
        } else {
          this.trainClassDetail.trainClassBaseInfo.schemeType = 2
        }
        const config = schemeConfig ? JSON.stringify(schemeConfig) : this.jsonString
        const jsonObj = JSON.parse(config) as Scheme
        this.trainClassDetail.trainingType = jsonObj.trainingType
        this.trainClassDetail.learningTypeModel = TrainClassConfigJsonManager.jsonConfigConvertToLearningType(
          this.jsonString,
          this.trainClassDetail.trainClassBaseInfo.schemeType,
          schemeConfig
        )
        if (classConfigJson.learningExperienceLearning?.config && this.trainClassDetail.userGetLearning) {
          this.trainClassDetail.learningTypeModel.learningExperience.isPassed =
            this.trainClassDetail.userGetLearning?.experienceQualified
        }
        if (this.studentSchemeLearningResponse?.studentLearning?.learningExperienceLearning?.userAssessResult?.length) {
          const config = JSON.parse(
            this.studentSchemeLearningResponse.studentLearning.learningExperienceLearning.userAssessResult[0]
          )
          this.trainClassDetail.learningTypeModel.learningExperience.learningExperiencePassNum =
            config.lessParticipateLearningExperienceTopicCount?.current || 0
        }
        if (classConfigJson.practiceLearning?.config?.type) {
          this.trainClassDetail.trainClassBaseInfo.practiceLearningType = classConfigJson.practiceLearning?.config?.type
        }
      }

      //设置学员当前获取的学习数据
      let totalRequirePeriod = 0
      if (this.trainClassDetail.trainClassBaseInfo.schemeType == 1) {
        const courseLearn = this.trainClassDetail.learningTypeModel.courseLearning
        totalRequirePeriod = CalculatorObj.add(courseLearn.compulsoryRequirePeriod, courseLearn.electiveRequirePeriod)
        console.log(
          totalRequirePeriod,
          'this.trainClassDetail.userGetLearning.selectedCoursePeriod',
          this.trainClassDetail.userGetLearning.selectedCoursePeriod
        )
        this.trainClassDetail.userGetLearning.needSelectedCoursePeriod = CalculatorObj.subtract(
          totalRequirePeriod,
          this.trainClassDetail.userGetLearning.selectedCoursePeriod
        )
      } else if (this.trainClassDetail.trainClassBaseInfo.schemeType == 2) {
        totalRequirePeriod = this.trainClassDetail.learningTypeModel.courseLearning.requirePeriod
      }
      this.trainClassDetail.userGetLearning.requirePeriod = totalRequirePeriod
      if (this.trainClassDetail.learningTypeModel.courseLearning.isAssess) {
        if (this.studentSchemeLearningResponse.studentLearning?.courseLearning?.userAssessResult) {
          const courseAssessResultJson = JSON.parse(
            this.studentSchemeLearningResponse.studentLearning.courseLearning.userAssessResult[0]
          )
          if (this.trainClassDetail.trainClassBaseInfo.schemeType == 1) {
            this.trainClassDetail.userGetLearning.currentPeriod = CalculatorObj.add(
              courseAssessResultJson.compulsoryRequirePeriod.current,
              courseAssessResultJson.electiveRequirePeriod.current
            )
            // this.trainClassDetail.userGetLearning.courseQualified =
            //   this.trainClassDetail.userGetLearning.currentPeriod >= this.trainClassDetail.userGetLearning.requirePeriod
          } else {
            this.trainClassDetail.userGetLearning.currentPeriod = courseAssessResultJson.requirePeriod.current

            // const classficationAssess = []
            let allClassficationsAssess = true
            /** 处理大纲下必学课程信息（要求学时和已完成学时） **/
            let compulsoryCourseList: OutlineCompulsoryCourseLearningInfo[] = []
            // 1-根据学员学号获取必学课程信息
            if (this.trainClassDetail.trainClassBaseInfo.studentNo) {
              const queryM = new BatchCourseQueryUtil()
              compulsoryCourseList = await queryM.queryCompulsoryCourseListByStudentNo(
                this.trainClassDetail.trainClassBaseInfo.studentNo
              )
            }
            // 2-循环遍历填充树内必学课程要求学时和已学学时
            compulsoryCourseList?.forEach((item) => {
              const target = this.findClassficationById(
                item.outlineId,
                this.trainClassDetail.learningTypeModel.courseLearning.classification
              )
              if (target) {
                target.assessSetting.compulsoryCompletedPeriod = item.learnedTotalPeriod
                target.assessSetting.compulsoryRequirePeriod = item.allTotalPeriod
              }
            })
            // 3-重算上一级大纲内的必学课程要求学时和已学学时
            this.reCalCompulsoryInScheme(this.trainClassDetail.learningTypeModel.courseLearning.classification)
            console.log(
              '###reCalOutline',
              compulsoryCourseList,
              this.trainClassDetail.learningTypeModel.courseLearning.classification
            )
            this.studentSchemeLearningResponse.studentLearning.courseLearning.userAssessResult.forEach((item) => {
              const assesModel = JSON.parse(item)
              //代表是大纲考核
              if (assesModel.name && assesModel.name == 'Course_Assess_003') {
                const classification = this.findClassficationById(
                  assesModel.ownerId,
                  this.trainClassDetail.learningTypeModel.courseLearning.classification
                )
                if (classification) {
                  classification.assessSetting.completePeriod = assesModel.requirePeriod.current
                }
                if (assesModel.requirePeriod.current < assesModel.requirePeriod.config) {
                  allClassficationsAssess = false
                }
              }
            })
            // if (
            //   allClassficationsAssess &&
            //   this.trainClassDetail.userGetLearning.currentPeriod >= this.trainClassDetail.userGetLearning.requirePeriod
            // ) {
            //   this.trainClassDetail.userGetLearning.courseQualified = true
            // }
          }
        }
      }
      if (this.trainClassDetail.learningTypeModel.exam.isAssess) {
        if (this.trainClassDetail.learningTypeModel.exam.allowCount == -1) {
          this.trainClassDetail.userGetLearning.surplusExamCount = -1
        } else {
          this.trainClassDetail.userGetLearning.surplusExamCount = CalculatorObj.subtract(
            this.trainClassDetail.learningTypeModel.exam.allowCount,
            this.trainClassDetail.userGetLearning.examCount
          )
          this.trainClassDetail.userGetLearning.surplusExamCount < 0 &&
            (this.trainClassDetail.userGetLearning.surplusExamCount = 0)
        }
      }
      // console.log('转换的详情对=', this.trainClassDetail)
    }
    return res.status
  }
  findClassficationById(id: string, classification: Classification): Classification | undefined {
    if (classification.id == id) {
      return classification
    }
    if (classification.childOutlines && classification.childOutlines.length) {
      for (let i = 0; i < classification.childOutlines.length; i++) {
        const findResult = this.findClassficationById(id, classification.childOutlines[i])
        if (findResult) return findResult
      }
    }
    return undefined
  }
  //获取所有分类
  async getAllClassFi(
    qualificationId: string,
    classEnum: ClassFicationEnum = ClassFicationEnum.ClassFicationEnumChooseLearning
  ) {
    //获取培训班配置模板jsonString
    let fileString = []
    if (classEnum == ClassFicationEnum.ClassFicationEnumChooseLearning) {
      fileString = ['chooseCourseLearning']
    } else if (classEnum == ClassFicationEnum.ClassFicationEnumAutoLearning) {
      fileString = ['autonomousCourseLearning']
    } else {
      fileString = ['interestCourseLearning']
    }
    const res = await MsSchemeQueryFrontGatewayCourseLearningForestage.getMySchemeConfig({
      qualificationId: qualificationId,
      needField: fileString
    })
    if (res.status.isSuccess()) {
      const commodityDetail: SchemeConfigResponse = res.data
      const classConfigJson: any = TrainClassConfigJsonManager.calculatorSchemeConfigJson(commodityDetail.schemeConfig)
      const learningType = new LearningType()
      if (
        classEnum == ClassFicationEnum.ClassFicationEnumChooseLearning ||
        classEnum == ClassFicationEnum.ClassFicationEnumAutoLearning
      ) {
        TrainClassConfigJsonManager.configCourseLearning(learningType, classConfigJson)
        return learningType.courseLearning.classification.childOutlines
      } else {
        TrainClassConfigJsonManager.configInterest(learningType, classConfigJson)
        return learningType.interestCourse.classification.childOutlines
      }
    }
    return []
  }
  //获取班级详情
  async requestClassDetail() {
    // const queryGql = gql`
    //   query getSchemeLearningInMyself($qualificationId: String) {
    //     getSchemeLearningInMyself(qualificationId: $qualificationId) {
    //       status
    //     }
    //   }
    // `
    const res = await MsMySchemeQueryFrontGatewayCourseLearningForeStage.getSchemeLearningInMyself(
      this.qualificationId
      // queryGql
    )
    if (res.status.isSuccess()) {
      const commodityDetail: StudentSchemeLearningResponse = res.data
      this.studentSchemeLearningResponse = commodityDetail
      await this.convertToTrainClassDetailClassVo(commodityDetail)
      this.issueLearningDetail = new StudentFaceStudyLog(this.trainClassDetail.trainClassBaseInfo.studentNo)
      await this.issueLearningDetail.queryStudentFaceStudyLog()
    }
    return res.status
  }
  //转换成Vo对象
  async convertToTrainClassDetailClassVo(commodityDetail: StudentSchemeLearningResponse) {
    try {
      this.trainClassDetail.trainClassBaseInfo.skuProperty =
        await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
          commodityDetail.scheme.skuProperty as ComplexSkuPropertyResponse
        )
    } catch (e) {
      console.log(e)
    }

    this.trainClassDetail.trainClassBaseInfo.schemeType = Constants.getSchemeType(commodityDetail.scheme.schemeType)
    this.trainClassDetail.trainClassBaseInfo.id = commodityDetail.scheme.schemeId
    this.trainClassDetail.trainClassBaseInfo.qualificationId = commodityDetail.qualificationId
    this.trainClassDetail.trainClassBaseInfo.studentNo = commodityDetail.studentNo
    this.trainClassDetail.trainClassBaseInfo.trainingResult = commodityDetail.studentLearning.trainingResult
    this.trainClassDetail.trainClassBaseInfo.trainingResultTime = commodityDetail.studentLearning.trainingResultTime
    if (commodityDetail.studentLearning.courseLearning) {
      this.trainClassDetail.userGetLearning.selectedCoursePeriod =
        commodityDetail.studentLearning.courseLearning.selectedCoursePeriod
      this.trainClassDetail.userGetLearning.courseQualified =
        commodityDetail.studentLearning.courseLearning.courseScheduleStatus == 2
    }
    if (commodityDetail.studentLearning.examLearning) {
      const examLearnRes = commodityDetail.studentLearning.examLearning
      this.trainClassDetail.userGetLearning.committedExam = examLearnRes.committedExam
      this.trainClassDetail.userGetLearning.examQualified = examLearnRes.examAssessResult == 1 ? true : false
      this.trainClassDetail.userGetLearning.examQualifiedTime = examLearnRes.examQualifiedTime
      this.trainClassDetail.userGetLearning.maxExamScore = examLearnRes.maxExamScore || 0
      this.trainClassDetail.userGetLearning.examCount = examLearnRes.examCount
    }
    if (commodityDetail.studentLearning.learningResult) {
      //配置培训证明
      const templateResult = commodityDetail.studentLearning.learningResult.find(
        (learnResult: LearningResultResponse) => learnResult.learningResultConfig.resultType == 2
      )
      if (templateResult) {
        // this.trainClassDetail.trainClassBaseInfo.hasLearningResult = true
        // this.trainClassDetail.trainClassBaseInfo.learningResultId = (templateResult.learningResultConfig as CertificateLearningConfigResultResponse).certificateTemplateId
        // this.trainClassDetail.trainClassBaseInfo.openPrintTemplate = true
        this.trainClassDetail.userGetLearning.learningResult.learningResultId = (
          templateResult.learningResultConfig as CertificateLearningConfigResultResponse
        ).certificateTemplateId
        this.trainClassDetail.userGetLearning.learningResult.learningResultName = ''
        //  TODO  去获取培训证明的详情
      }

      //获取培训成果中学分
      const creditLearningResult = commodityDetail.studentLearning.learningResult.find(
        (learnResult: LearningResultResponse) => learnResult.learningResultConfig.resultType == 1
      )
      if (creditLearningResult) {
        // this.trainClassDetail.trainClassBaseInfo.period = (creditLearningResult.learningResultConfig as GradeLearningConfigResultResponse).grade
        this.trainClassDetail.userGetLearning.credit = (
          creditLearningResult.learningResultConfig as GradeLearningConfigResultResponse
        ).grade
      }
    }
    // 学习心得
    if (commodityDetail.studentLearning.learningExperienceLearning) {
      this.trainClassDetail.userGetLearning.experienceQualified =
        commodityDetail.studentLearning.learningExperienceLearning.learningExperienceAssessResult === 1
    }
  }

  // 大纲重算必学
  private reCalCompulsoryInScheme(classification: Classification): void {
    this.reShapeNode(classification)
    const leaves = this.findAllLeaves(classification)
    leaves.forEach((item) => {
      this.reCalCompulsoryInNode(item.parentId, classification)
    })
  }

  // 节点重算必学
  private reCalCompulsoryInNode(id: string, classification: Classification) {
    const target = this.findClassficationById(id, classification)
    if (target) {
      target.assessSetting.compulsoryRequirePeriod =
        target.childOutlines?.reduce((prev, cur) => {
          return CalculatorObj.add(prev, cur.assessSetting.compulsoryRequirePeriod || 0)
        }, 0) || 0
      target.assessSetting.compulsoryCompletedPeriod =
        target.childOutlines?.reduce((prev, cur) => {
          return CalculatorObj.add(prev, cur.assessSetting.compulsoryCompletedPeriod || 0)
        }, 0) || 0
    }
    const parentNode = this.findClassficationById(target?.parentId, classification)
    if (parentNode) {
      this.reCalCompulsoryInNode(target.parentId, classification)
    }
  }

  /**
   * 重塑树结构
   * @param classification
   */
  reShapeNode(classification: Classification, parentId = '0') {
    classification.parentId = parentId
    if (classification.childOutlines && classification.childOutlines.length) {
      classification.childOutlines.forEach((node) => {
        this.reShapeNode(node, classification.id)
      })
    }
  }

  /**
   * 获取所有叶子节点
   * @param classification
   * @private
   */
  private findAllLeaves(classification: Classification): Classification[] {
    const nodeList = [] as Classification[]
    const getLeaves = (treeArr: Classification[]) => {
      treeArr.forEach((item) => {
        if (!item.childOutlines || !item.childOutlines.length) {
          nodeList.push(item)
        } else {
          getLeaves(item.childOutlines)
        }
      })
    }
    if (!classification.childOutlines || !classification.childOutlines.length) {
      return [classification]
    } else {
      getLeaves(classification.childOutlines)
      return nodeList
    }
  }

  /**
   * 获取特殊的大纲
   * @param learningProcess 学习进度
   */
  async specialOutlineTree(learningProcess: LearningProcessEnum) {
    await this.queryTrainClassDetail()
    if (learningProcess === LearningProcessEnum.ALL) {
      return this.trainClassDetail.learningTypeModel.courseLearning.classification
    } else {
      return this.getClassificationByLearningProcess(learningProcess)
    }
  }

  /**
   * 根据学习进度获取树
   * @param learningProcess 学习进度
   * @private
   */
  private getClassificationByLearningProcess(learningProcess: LearningProcessEnum) {
    let firstFilterList: Classification[]
    // 平铺的列表
    const flatList = this.treeToList(
      this.trainClassDetail.learningTypeModel.courseLearning.classification?.childOutlines
    )
    // 不符合条件的列表
    const notConformList: Classification[] = []
    if (learningProcess === LearningProcessEnum.LEARNING) {
      // 学习中 => 要求学时>0 && 完成学时>0 && 完成学时<要求学时
      firstFilterList = flatList.filter((el) => {
        if (el.childOutlines?.length) return true
        if (!el.childOutlines || !el.childOutlines.length) {
          if (
            el.assessSetting.completePeriod > 0 &&
            el.assessSetting.requirePeriod > 0 &&
            el.assessSetting.completePeriod < el.assessSetting.requirePeriod
          ) {
            return true
          } else {
            notConformList.push(el)
            return false
          }
        }
      })
    }
    if (learningProcess === LearningProcessEnum.UNLEARNED) {
      // 未学习 => 要求学时>0 && 完成学时===0
      firstFilterList = flatList.filter((el) => {
        if (el.childOutlines?.length) return true
        if (!el.childOutlines || !el.childOutlines.length) {
          if (el.assessSetting.requirePeriod > 0 && el.assessSetting.completePeriod === 0) {
            return true
          } else {
            notConformList.push(el)
            return false
          }
        }
      })
    }
    if (learningProcess === LearningProcessEnum.COMPLETED) {
      // 已完成 => 要求学时>0 && 要求学时===完成学时
      firstFilterList = flatList.filter((el) => {
        if (el.childOutlines?.length) return true
        if (!el.childOutlines || !el.childOutlines.length) {
          if (
            el.assessSetting.requirePeriod > 0 &&
            el.assessSetting.requirePeriod === el.assessSetting.completePeriod
          ) {
            return true
          } else {
            notConformList.push(el)
            return false
          }
        }
      })
    }
    const secondFilterList: Classification[] = this.getSecondFilterList(flatList, firstFilterList, notConformList)
    const newTree = this.listToTree(secondFilterList)
    console.log(`${learningProcess}-flatList`, flatList)
    console.log(`${learningProcess}-firstFilterList`, firstFilterList)
    console.log(`${learningProcess}-notConformList`, notConformList)
    console.log(`${learningProcess}-secondFilterList`, secondFilterList)
    console.log(`${learningProcess}-newTree`, newTree)
    const cloneTree = clone(this.trainClassDetail.learningTypeModel.courseLearning.classification)
    if (newTree && newTree.length) {
      cloneTree.childOutlines = newTree
    } else {
      cloneTree.childOutlines = undefined
    }
    console.log(`${learningProcess}-cloneTree`, cloneTree)
    return cloneTree
  }

  /**
   * 树形结构转为列表
   * @param treeArr
   * @private
   */
  private treeToList(treeArr: Classification[]): Classification[] {
    let result = [] as Classification[]
    treeArr?.forEach((node) => {
      result.push(node)
      if (node.childOutlines && node.childOutlines.length) {
        const res = this.treeToList(node.childOutlines)
        result = result.concat(res)
      }
    })
    return result
  }

  /**
   * 数组转树形结构
   */
  private listToTree(treeArr: Classification[], parentId = '9999'): Classification[] {
    const nodeList = treeArr.filter((node) => node.parentId === parentId)
    nodeList?.forEach((node) => {
      const subRes = this.listToTree(treeArr, node.id)
      if (!subRes || !subRes.length) {
        node.childOutlines = undefined
      } else {
        node.childOutlines = subRes
      }
    })
    return nodeList
  }

  /**
   * 获取第二次过滤的数组
   * @param flatList 打平的数组
   * @param firstFilterList 第一次过滤的数组
   * @param notConformList 不符合的数组
   * @private
   */
  private getSecondFilterList(
    flatList: Classification[],
    firstFilterList: Classification[],
    notConformList: Classification[]
  ) {
    const result = clone(firstFilterList) as Classification[]
    // 1-将不符合的项分组：父节点id下的哪些节点id不符合
    const groupArr =
      notConformList.reduce((prev, cur) => {
        const target = prev.find((el) => el.parentId === cur.parentId)
        if (target) {
          if (!target.ids.includes(cur.id)) {
            target.ids.push(cur.id)
          }
        } else {
          if (cur.id) {
            prev.push({
              parentId: cur.parentId,
              ids: [cur.id]
            })
          }
        }
        return prev
      }, [] as { parentId: string; ids: string[] }[]) || []
    // 2-遍历判断是否父节点下的子节点id集合是否与分组一致，一致则删除该项
    groupArr?.forEach((el) => {
      const parentNode = flatList.find((itm) => itm.id === el.parentId)
      if (parentNode) {
        const childIds = parentNode.childOutlines?.map((itm) => itm.id)?.filter(Boolean)
        if (DataResolve.compareEquals(childIds, el.ids)) {
          const spliceIndex = result.findIndex((itm) => itm.id === el.parentId)
          if (spliceIndex >= 0) {
            result.splice(spliceIndex, 1)
          }
        }
      }
    })
    return result
  }

  /**
   * 查找节点
   */
  treeFind(tree: Array<Classification>, func: any, childKey = 'childOutlines'): Classification | null {
    for (const item of tree) {
      if (func(item)) return item
      if (item[childKey] && item[childKey].length) {
        const res: Classification | null = this.treeFind(item[childKey], func, childKey)
        if (res) return res
      }
    }
    return null
  }
  /**
   * 获取当前学习状态大纲
   */
  async getClassificationByLearning(studentNo: string, schemeId: string, learningProcess: LearningProcessEnum) {
    await this.queryTrainClassDetail()
    if (learningProcess === LearningProcessEnum.ALL || learningProcess === LearningProcessEnum.UNLEARNED) {
      return this.trainClassDetail.learningTypeModel.courseLearning.classification
    }
    const res = await CourseLearningForestage.statisticsCourseInSchemeInMyself({ studentNo, schemeId })
    if (!res.status.isSuccess()) return new Classification()
    const outlineClassification = cloneDeep(this.trainClassDetail.learningTypeModel.courseLearning.classification)
    const outlineLeaf = this.findAllLeaves(outlineClassification)
    // 更新父节点
    const updateOutline = (outline: Classification) => {
      if (!outline) return
      if (!outline.childOutlines.length) {
        const outlineParent = this.treeFind([outlineClassification], (node: Classification) => {
          return node.id === outline.parentId
        })
        const outlineIndex = outlineParent?.childOutlines?.findIndex((ite) => ite.id === outline.id)
        if (outlineIndex >= 0) outlineParent?.childOutlines?.splice(outlineIndex, 1)
        updateOutline(outlineParent)
      }
    }
    outlineLeaf.forEach((item) => {
      let outlineInfo: CourseTrainingOutlineResponse
      if (learningProcess === LearningProcessEnum.LEARNING) {
        outlineInfo = res.data.find(
          (ite) => ite.outlineId === item.id && !ite.courseStatisticOfCourseTrainingOutline?.learningTotalPeriod
        )
      }
      if (learningProcess === LearningProcessEnum.COMPLETED) {
        outlineInfo = res.data.find(
          (ite) => ite.outlineId === item.id && !ite.courseStatisticOfCourseTrainingOutline?.completeTotalPeriod
        )
      }
      if (outlineInfo) {
        const outlineParent = this.treeFind([outlineClassification], (node: Classification) => {
          return node.id === item.parentId
        })
        const outlineIndex = outlineParent?.childOutlines?.findIndex((ite) => ite.id === item.id)
        if (outlineIndex >= 0) outlineParent?.childOutlines?.splice(outlineIndex, 1)
        updateOutline(outlineParent)
      }
    })
    return outlineClassification || new Classification()
  }
  /**
   * 获取班级合格状态
   */
  async getSchemeResultInMyself(params: QuerySchemeTrainingResultParam): Promise<boolean> {
    let result = false
    const { trainingMode, studentNo, qualificationId } = params
    if (trainingMode === TrainingModeEnum.mixed || trainingMode === TrainingModeEnum.offline) {
      // 面网授班 || 面授班
      const { data } =
        await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.getSchemeLearningDetailInMyself(studentNo)
      if (data) {
        const { studentSchemeWithIssueLearningResponse: studentSchemeLearning, studentIssueLearning } = data
        if (studentSchemeLearning && studentIssueLearning) {
          result = studentSchemeLearning.trainingResult > 0 && studentIssueLearning.trainingResult > 0
        }
      }
    }
    if (params.trainingMode === TrainingModeEnum.online) {
      // 网授班
      const res = await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.getSchemeLearningInMyself(
        qualificationId
      )
      if (res.data) {
        const { studentLearning } = res.data
        if (studentLearning) {
          result = studentLearning.trainingResult > 0
        }
      }
    }
    return result
  }
  /**
   * 获取在线学习规则
   */
  async getSchemeLearningRule(schemeId: string) {
    const res = await CourseLearningForestage.getSchemeLearningRule(schemeId)
    if (!res.status.isSuccess()) return new LearningRuleSnapshotResponse()
    return res.data
  }

  /**
   * 根据期别id查询期别信息
   * @param issueId 期别id
   */
  queryIssueConfigDetailByIssueId(issueId: string) {
    const issueList = this.trainClassDetail.learningTypeModel.issue.issueConfigList
    return issueList.find((itm) => itm.id === issueId) || new IssueConfigDetail()
  }

  // endregion
}
export default QueryMyTrainClassDetail
