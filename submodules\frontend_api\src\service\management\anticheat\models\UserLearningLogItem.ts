import UserLogInfo from '@api/service/management/anticheat/models/UserLogInfo'

export default class UserLearningLogItem {
  /**
   * 模块索引
   */
  index = 0
  /**
   * 课程名称
   */
  courseName = ''
  /**
   * 课程id
   */
  courseId = ''
  /**
   * 课件id
   */
  courseWareId = ''
  /**
   * 课件名称
   */
  courseWareName = ''
  /**
   * 监管信息
   */
  userLogInfo: Array<UserLogInfo> = new Array<UserLogInfo>()
  /**
   * 课件监管信息
   */
  courseWareUserLearningLog: Array<UserLearningLogItem> = new Array<UserLearningLogItem>()
}
