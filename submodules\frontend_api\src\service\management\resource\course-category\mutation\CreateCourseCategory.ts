import CreateCourseCategoryVo from '@api/service/management/resource/course-category/mutation/vo/CreateCourseCategory'
import { ResponseStatus } from '@hbfe/common'
import CreateCourseCategoryDto from '@api/service/management/resource/course-category/mutation/dto/CreateCourseCategoryDto'

class CreateCourseCategory {
  createCourseCategory: CreateCourseCategoryVo

  constructor() {
    this.createCourseCategory = new CreateCourseCategoryVo()
  }

  async doCreate(): Promise<ResponseStatus> {
    const { status } = await CreateCourseCategoryDto.from(this.createCourseCategory).save()
    return new ResponseStatus(status.code, status.getMessage())
  }
}

export default CreateCourseCategory
