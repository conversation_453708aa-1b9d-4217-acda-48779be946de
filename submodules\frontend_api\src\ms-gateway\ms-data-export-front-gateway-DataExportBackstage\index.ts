import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-data-export-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-data-export-front-gateway-DataExportBackstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum AuditType {
  AUTO_AUDIT = 'AUTO_AUDIT',
  MANUAL_AUDIT = 'MANUAL_AUDIT'
}
export enum ExperienceType {
  SCHEME = 'SCHEME',
  COURSE = 'COURSE'
}
export enum ParticipateType {
  SUBMIT_FILE = 'SUBMIT_FILE',
  EDIT_ONLINE = 'EDIT_ONLINE'
}
export enum StudentLearningExperienceStatus {
  SUBMITING = 'SUBMITING',
  SUBMITTED = 'SUBMITTED',
  PASS = 'PASS',
  RETURNED = 'RETURNED'
}
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum StudentSortFieldEnum {
  createdTime = 'createdTime'
}
export enum BatchOrderSortField {
  BATCH_ORDER_UN_CONFIRMED_TIME = 'BATCH_ORDER_UN_CONFIRMED_TIME',
  BATCH_ORDER_COMMIT_TIME = 'BATCH_ORDER_COMMIT_TIME'
}
export enum BatchReturnOrderSortField {
  CREATED_TIME = 'CREATED_TIME'
}
export enum CommoditySkuSortField {
  ON_SHELVE_TIME = 'ON_SHELVE_TIME',
  LAST_EDIT_TIME = 'LAST_EDIT_TIME',
  SALE_TOTAL_NUMBER = 'SALE_TOTAL_NUMBER',
  SKU_PROPERTY_YEAR = 'SKU_PROPERTY_YEAR',
  TRAINING_CHANNEL = 'TRAINING_CHANNEL'
}
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum OrderSortField {
  ORDER_NORMAL_TIME = 'ORDER_NORMAL_TIME',
  ORDER_COMPLETED_TIME = 'ORDER_COMPLETED_TIME'
}
export enum ReturnOrderSortField {
  APPLIED_TIME = 'APPLIED_TIME'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class OwnerInfoModel {
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  unitId?: string
  servicerId?: string
}

/**
 * @Description 范围查询条件
<AUTHOR>
@Date 8:51 2022/5/23
 */
export class BigDecimalScopeExcelRequest {
  /**
   * result >&#x3D; begin
   */
  begin?: number
  /**
   * result <&#x3D; end
   */
  end?: number
}

/**
 * 范围查询条件
<AUTHOR>
@version 1.0
@date 2022/5/7 15:34
 */
export class DateScopeExcelRequest {
  /**
   * result >&#x3D; begin
   */
  begin?: string
  /**
   * result <&#x3D; end
   */
  end?: string
}

/**
 * 异步任务组名返回对象
 */
export class JobGroupRequest {
  /**
   * 任务组key
   */
  group?: string
  /**
   * 任务组名（模糊查询）
   */
  groupName?: string
}

/**
 * 功能描述：任务查询参数
@Author： wtl
@Date： 2022/1/18 15:13
 */
export class JobRequest {
  /**
   * 任务组名（必填）
   */
  group?: string
  /**
   * 任务组名匹配方式（EQ：完全匹配 LIKE：模糊匹配[*group*] LLIKE：左模糊匹配[group*] RLIKE：右模糊匹配[*group]，不传值默认为完全匹配）
   */
  groupOperator?: string
  /**
   * 任务名（模糊查询）
   */
  jobName?: string
  /**
   * 任务状态(executing:运行中 executed:运行完成 fail:运行失败)
@see UserJobState
   */
  jobState?: string
  /**
   * 任务执行时间 yyyy-MM-dd HH:mm:ss
   */
  executeTimeScope?: DateScopeRequest
  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult?: boolean
}

/**
 * 功能描述：学员查询条件
@Author： wtl
@Date： 2022年1月26日 10:10:33
 */
export class StudentQueryRequest {
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: StudentUserRequest
  /**
   * 用户认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 排序
   */
  sortList?: Array<StudentSortRequest>
}

export class AccountRequest {
  /**
   * 账户状态 1：正常，2：冻结，3：注销
@see AccountStatus
   */
  statusList?: Array<number>
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 创建人用户id
   */
  createdUserId?: string
}

/**
 * 功能描述：账户认证查询条件
@Author： wtl
@Date： 2022年1月26日 09:30:12
 */
export class AuthenticationRequest {
  /**
   * 帐号
   */
  identity?: string
}

/**
 * 功能描述：学员集体缴费信息
@Author： wtl
@Date： 2022年4月21日 08:58:49
 */
export class CollectiveRequest {
  /**
   * 集体缴费管理员用户id集合
   */
  collectiveUserIdList?: Array<string>
}

/**
 * 功能描述 : 学员排序参数
@date : 2022/4/1 17:15
 */
export class StudentSortRequest {
  /**
   * 学员排序字段
   */
  studentSortField?: StudentSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

export class StudentUserRequest {
  /**
   * 工作单位名称（模糊）
   */
  companyName?: string
  /**
   * 用户所属地区路径集合（模糊，右like）
   */
  regionPathList?: Array<string>
  /**
   * 集体缴费信息
   */
  collective?: CollectiveRequest
  /**
   * 单位所属地区路径集合（模糊，右like）
   */
  companyRegionPathList?: Array<string>
  /**
   * 单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  companyRegionPathListMatchType?: number
  /**
   * 是否工勤人员  (0:非工勤人员  1:工勤人员)
   */
  isWorker?: string
  /**
   * 是否退休   (0:非退休人员 1:退休人员)
   */
  isRetire?: string
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
}

/**
 * <AUTHOR>
 */
export class ChooseCourseStatisticsRequest {
  /**
   * 培训班级上的年度
   */
  year?: string
  /**
   * 课件供应商ID
   */
  supplierId?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 课程Id
   */
  courseId?: Array<string>
  /**
   * 选课时间开始
   */
  chooseCourseDateStart?: string
  /**
   * 选课时间结束
   */
  chooseCourseDateEnd?: string
}

/**
 * <AUTHOR>
 */
export class CourseSalesStatisticsRequest {
  /**
   * 课件供应商ID
   */
  supplierId?: Array<string>
  /**
   * 选课时间开始
   */
  chooseCourseDateStart?: string
  /**
   * 选课时间结束
   */
  chooseCourseDateEnd?: string
}

/**
 * 课程查询条件
<AUTHOR>
 */
export class UnauthorizedCourseRequest {
  /**
   * 课程名称
   */
  name?: string
  /**
   * 创建时间开始
   */
  createTimeBegin?: string
  /**
   * 创建时间结束
   */
  createTimeEnd?: string
}

/**
 * 地区学习统计查询条件
<AUTHOR>
@version 1.0
@date 2022/1/17 14:17
 */
export class LearningReportFormsRequest {
  /**
   * 学员信息
   */
  student?: UserRequest
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest
  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  trainingChannelName?: string
}

export class StudentLearningExperienceRequest {
  /**
   * 学员学习心得ID
   */
  studentLearningExperienceIds?: Array<string>
  /**
   * 数据归属
   */
  owner?: OwnerInfoModel
  /**
   * 学习心得主题
   */
  learningExperienceTopic?: LearningExperienceTopicRequest
  /**
   * 学员方案学习
   */
  studentLearning?: StudentExperienceSchemeLearningRequest
  /**
   * 用户信息
   */
  user?: UserRequest
  /**
   * 学习心得类型（班级心得，课程心得）
   */
  experienceType?: ExperienceType
  /**
   * 状态
@see StudentLearningExperienceStatus
   */
  status?: Array<StudentLearningExperienceStatus>
  /**
   * 不传值默认全查  是否要心得被删除的数据  true只要被删除的 false 只要未被删除的  null全查
   */
  isDelete?: boolean
}

/**
 * 学员培训方案学习查询条件
<AUTHOR>
@version 1.0
@date 2022/1/17 11:40
 */
export class StudentSchemeLearningRequest {
  /**
   * 学员信息
   */
  student?: UserRequest
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest
  /**
   * 对接管理系统
   */
  connectManageSystem?: ConnectManageSystemRequest
  /**
   * 扩展信息
   */
  extendedInfo?: ExtendedInfoRequest
  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  trainingChannelName?: string
}

/**
 * @version: 1.0
@description: 对接管理系统
@author: sugs
@create: 2022-11-15 11:27
 */
export class ConnectManageSystemRequest {
  /**
   * 同步状态
@see SyncStatus
0 未同步
1 已同步
2 同步失败
   */
  syncStatus?: number
}

/**
 * 数据分析信息
<AUTHOR>
@version 1.0
@date 2022/1/20 15:14
 */
export class DataAnalysisRequest {
  /**
   * 成果配置可获得学时
   */
  trainingResultPeriod?: DoubleScopeRequest
  /**
   * 考核要求学时
   */
  requirePeriod?: DoubleScopeRequest
  /**
   * 已获得总学时
   */
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest {
  /**
   * 是否打印
true 打印 false 未打印
   */
  whetherToPrint?: boolean
}

/**
 * 学员学习报名信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:08
 */
export class LearningRegisterRequest {
  /**
   * 报名方式
<p>
1:学员自主报名
2:集体报名
3:管理员导入
@see StudentRegisterTypes
   */
  registerType?: number
  /**
   * 报名来源类型(ORDER：订单 SUB_ORDER：子订单 EXCHANGE_ORDER：换货单)
@see StudentSourceTypes
   */
  sourceType?: string
  /**
   * 报名来源ID
   */
  sourceId?: string
  /**
   * 学员状态(1:正常 2：冻结 3：失效)
@see StudentStatus
   */
  status?: Array<number>
  /**
   * 报名时间
   */
  registerTime?: DateScopeRequest
  /**
   * 订单销售渠道
   */
  saleChannels?: Array<number>
  /**
   * 来源订单号
   */
  orderNoList?: Array<string>
  /**
   * 来源子订单号
   */
  subOrderNoList?: Array<string>
  /**
   * 来源批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 分销门户id
   */
  portalId?: string
}

/**
 * 地区模型
<AUTHOR>
@version 1.0
@date 2022/2/27 20:01
 */
export class RegionRequest {
  /**
   * 地区：省
   */
  province?: string
  /**
   * 地区：市
   */
  city?: string
  /**
   * 地区：区
   */
  county?: string
}

/**
 * 学员学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class StudentLearningRequest {
  /**
   * 培训结果
<p>
-1:未知，培训尚未完成
1:培训合格
0:培训不合格
@see StudentTrainingResults
   */
  trainingResultList?: Array<number>
  /**
   * 培训结果时间
   */
  trainingResultTime?: DateScopeRequest
  /**
   * 无需学习的学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 练习学习方式
4：自主学习课程学习方式
@see LearningTypes
   */
  notLearningTypeList?: Array<number>
  /**
   * 课程学习状态（0：未学习 1：学习中 2：学习完成）
   */
  courseScheduleStatus?: number
  /**
   * 考试结果（-1：未考核 0：不合格 1：合格）
@see AssessCalculateResults
   */
  examAssessResultList?: Array<number>
}

/**
 * 地区sku属性查询条件
<AUTHOR>
@version 1.0
@date 2022/2/25 10:55
 */
export class RegionSkuPropertyRequest {
  /**
   * 地区: 省
   */
  province?: string
  /**
   * 地区: 市
   */
  city?: string
  /**
   * 地区: 区县
   */
  county?: string
}

/**
 * 地区匹配查询
<AUTHOR>
@version 1.0
@date 2022/2/25 14:19
 */
export class RegionSkuPropertySearchRequest {
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest>
  /**
   * 地区匹配条件
<p>
ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回，如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
PART部分匹配：查询结果返回的地区与查询条件有给值的地区就会返回 如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
@see com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.common.request.skuProperty.RegionSkuPropertySearchRequest.RegionSearchType
   */
  regionSearchType?: number
}

/**
 * 培训方案信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class SchemeRequest {
  /**
   * 培训方案id
   */
  schemeId?: string
  /**
   * 培训方案id
   */
  schemeIdList?: Array<string>
  /**
   * 培训方案类型
<p>
chooseCourseLearning: 选课规则
autonomousCourseLearning: 自主选课
@see SchemeType
   */
  schemeType?: string
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 培训属性
   */
  skuProperty?: SchemeSkuPropertyRequest
}

/**
 * 培训属性
<AUTHOR>
@version 1.0
@date 2022/1/17 10:22
 */
export class SchemeSkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 岗位类别
   */
  positionCategory?: Array<string>
  /**
   * 培训对象
   */
  trainingObject?: Array<string>
  /**
   * 技术等级
   */
  jobLevel?: Array<string>
  /**
   * 工种
   */
  jobCategory?: Array<string>
  /**
   * 科目
   */
  subject?: Array<string>
  /**
   * 年级
   */
  grade?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
}

/**
 * 学习心得主题
 */
export class LearningExperienceTopicRequest {
  /**
   * 心得主题id
   */
  topicIds?: Array<string>
  /**
   * 参与活动时间
   */
  dateScopeRequest?: DateScopeRequest
  /**
   * 审核方式
   */
  auditType?: AuditType
  /**
   * 心得参与形式
   */
  participateType?: ParticipateType
  /**
   * 是否要心得被删除的数据 默认要
   */
  isDelete?: boolean
}

export class StudentExperienceSchemeLearningRequest {
  /**
   * 参训资格ID
   */
  qualificationIds?: Array<string>
  /**
   * 学号
   */
  studentNos?: Array<string>
  /**
   * 方案id
   */
  schemeIds?: Array<string>
  /**
   * 学习方式id
   */
  learningIds?: Array<string>
}

/**
 * 用户属性
<AUTHOR>
@version 1.0
@date 2022/1/15 11:01
 */
export class UserPropertyRequest {
  /**
   * 所属地区路径
   */
  regionList?: Array<RegionRequest>
  /**
   * 工作单位名称
   */
  companyName?: string
}

/**
 * 用户信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:00
 */
export class UserRequest {
  /**
   * 用户id
   */
  userIdList?: Array<string>
  /**
   * 账户id
   */
  accountIdList?: Array<string>
  /**
   * 用户属性
   */
  userProperty?: UserPropertyRequest
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class BatchOrderRequest {
  /**
   * 批次单号集合
   */
  batchOrderNoList?: Array<string>
  /**
   * 批次单基本信息查询参数
   */
  basicData?: BatchOrderBasicDataRequest
  /**
   * 批次单支付信息查询参数
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 批次单创建人查询参数
   */
  creatorIdList?: Array<string>
  /**
   * 是否已经申请发票
   */
  isInvoiceApplied?: boolean
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 批次单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class BatchOrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: BatchOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 批次单基本信息查询参数
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderBasicDataRequest {
  /**
   * 批次单状态
0: 未确认，批次单初始状态
1: 正常
2: 交易完成
3: 交易关闭
4: 提交处理中 提交处理完成后，变更为NORMAl
5: 取消处理中
@see BatchOrderStatus
   */
  batchOrderStatusList?: Array<number>
  /**
   * 批次单状态变更时间
   */
  batchOrderStatusChangeTime?: BatchOrderStatusChangeTimeRequest
  /**
   * 批次单支付状态
<p>
0：未支付
1：支付中
2：已支付
@see BatchOrderPaymentStatus
   */
  batchOrderPaymentStatusList?: Array<number>
  /**
   * 批次单发货状态
0: 未发货
1: 发货中
2: 已发货
@see BatchOrderDeliveryStatus
   */
  batchOrderDeliveryStatusList?: Array<number>
  /**
   * 批次单价格范围
<p> 查询非0元批次单 begin填0.01
   */
  batchOrderAmountScope?: BigDecimalScopeExcelRequest
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 批次单状态变更时间查询参数
<AUTHOR>
@date 2022/04/17
 */
export class BatchOrderStatusChangeTimeRequest {
  /**
   * 未确认
   */
  unConfirmed?: DateScopeExcelRequest
  /**
   * 正常
   */
  normal?: DateScopeExcelRequest
  /**
   * 交易成功
   */
  completed?: DateScopeExcelRequest
  /**
   * 已关闭
   */
  closed?: DateScopeExcelRequest
  /**
   * 提交中
   */
  committing?: DateScopeExcelRequest
  /**
   * 取消处理中
   */
  canceling?: DateScopeExcelRequest
}

/**
 * 批次退货单查询参数
<AUTHOR>
@date 2022/04/19
 */
export class BatchReturnOrderRequest {
  /**
   * 批次退货单号集合
   */
  batchReturnOrderList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: BatchReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: BatchReturnOrderApprovalInfoRequest
  /**
   * 批次退货单关联批次单
   */
  batchOrderInfo?: BatchOrderInfoRequest
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 批次退货单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class BatchReturnOrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: BatchReturnOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 批次退货单关联批次单查询参数
<AUTHOR>
@date 2022/04/19
 */
export class BatchOrderInfoRequest {
  /**
   * 批次单号集合
   */
  batchOrderNoList?: Array<string>
  /**
   * 批次单创建人id集合
   */
  creatorIdList?: Array<string>
  /**
   * 收款账号ID集合
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号集合
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

/**
 * 批次退货单关闭信息
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnCloseReasonRequest {
  /**
   * 批次退货单关闭类型（1：卖家取消 2：卖家拒绝退货 3：买家取消 4：确认失败取消）
@see BatchReturnCloseTypes
   */
  closeTypeList?: Array<number>
}

/**
 * 批次退货单审批信息查询参数
<AUTHOR>
@date 2022/03/18
 */
export class BatchReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime?: DateScopeExcelRequest
}

/**
 * 批次退货单基本信息查询参数
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnOrderBasicDataRequest {
  /**
   * 批次退货单状态
0: 已创建
1: 已确认
2: 取消申请中
3: 退货处理中
4: 退货失败
5: 正在申请退款
6: 已申请退款
7: 退款处理中
8: 退款失败
9: 退货完成
10: 退款完成
11: 退货退款完成
12: 已关闭
@see BatchReturnOrderStatus
   */
  batchReturnOrderStatus?: Array<number>
  /**
   * 批次退货单关闭信息
   */
  batchReturnCloseReason?: BatchReturnCloseReasonRequest
  /**
   * 批次退货单状态变更时间
   */
  batchReturnStatusChangeTime?: BatchReturnOrderStatusChangeTimeRequest
  /**
   * 退款金额范围
<br> 查询非0元  begin填0.01
   */
  refundAmountScope?: BigDecimalScopeExcelRequest
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 批次退货单状态变更时间查询参数
<AUTHOR>
@date 2022/4/19
 */
export class BatchReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied?: DateScopeExcelRequest
  /**
   * 批次退货完成时间
<p> 这个参数包含了退货退款完成（批次退货单类型为退货退款）、仅退货完成（批次退货单类型为仅退货）、仅退款完成（批次退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted?: DateScopeExcelRequest
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuRequest {
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品名称（精确匹配）
   */
  saleTitleList?: Array<string>
  /**
   * 商品名称（模糊查询）
   */
  saleTitleMatchLike?: string
  /**
   * 要从查询结果中剔除的商品ID集合
   */
  notShowCommoditySkuIdList?: Array<string>
  /**
   * 商品售价
   */
  price?: number
  /**
   * 商品上下架信息
   */
  onShelveRequest?: OnShelveRequest
  /**
   * 培训方案信息
   */
  schemeRequest?: SchemeRequest1
  /**
   * 商品sku属性查询
   */
  skuPropertyRequest?: SkuPropertyRequest
  /**
   * 是否展示资源不可用的商品
   */
  isDisabledResourceShow?: boolean
  /**
   * 是否展示所有资源
（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
   */
  isShowAll?: boolean
  /**
   * 专题id
   */
  trainingChannelIds?: Array<string>
  /**
   * 是否存在专题
   */
  existTrainingChannel?: boolean
  /**
   * 管理系统平台
   */
  externalTrainingPlatform?: Array<string>
}

/**
 * 商品排序参数
<AUTHOR>
@date 2022/01/27
 */
export class CommoditySkuSortRequest {
  /**
   * 用来排序的字段
   */
  sortField?: CommoditySkuSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 商品上下架相关查询参数
<AUTHOR>
@date 2022/01/25
 */
export class OnShelveRequest {
  /**
   * 商品上下架状态
<br> 0:已下架 1：已上架
   */
  onShelveStatus?: number
}

/**
 * 培训方案相关查询参数
<AUTHOR>
@date 2022/01/25
 */
export class SchemeRequest1 {
  /**
   * 培训方案ID
   */
  schemeIdList?: Array<string>
  /**
   * 培训方案类型
<br> chooseCourseLearning:选课规则 autonomousCourseLearning:自主学习
   */
  schemeType?: string
  /**
   * 培训方案名称(模糊查询)
   */
  schemeName?: string
  /**
   * 培训开始时间
   */
  trainingBeginDate?: DateScopeRequest
  /**
   * 培训结束时间
   */
  trainingEndDate?: DateScopeRequest
}

/**
 * 商品查询条件
<AUTHOR>
@date 2022/01/25
 */
export class CommoditySkuRequest1 {
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyRequest
}

/**
 * 地区查询参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertyRequest1 {
  /**
   * 地区: 省
   */
  province?: string
  /**
   * 地区: 市
   */
  city?: string
  /**
   * 地区: 区县
   */
  county?: string
}

/**
 * 地区查询请求参数
<AUTHOR>
@date 2022/02/25
 */
export class RegionSkuPropertySearchRequest1 {
  /**
   * 地区匹配方式
<p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
<p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
@see RegionSearchType
   */
  regionSearchType?: number
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest1>
}

/**
 * 商品sku属性查询参数
<AUTHOR>
@date 2022/01/25
 */
export class SkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest1
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
  /**
   * 黑龙江药师-证书类型
   */
  certificatesType?: Array<string>
  /**
   * 黑龙江药师-执业类别
   */
  practitionerCategory?: Array<string>
  /**
   * 工勤行业-工种
   */
  jobCategory?: Array<string>
  /**
   * 卫生行业-培训对象
   */
  trainingObject?: Array<string>
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class OrderRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 子订单退货状态
0: 未退货
1: 退货申请中
2: 退货中
3: 退货成功
4: 退款中
5: 退款成功
@see SubOrderReturnStatus
   */
  subOrderReturnStatus?: Array<number>
  /**
   * 订单基本信息查询参数
   */
  orderBasicData?: OrderBasicDataRequest
  /**
   * 订单支付信息查询
   */
  payInfo?: OrderPayInfoRequest
  /**
   * 买家查询参数
   */
  buyerIdList?: Array<string>
  /**
   * 发货商品信息
   */
  deliveryCommodity?: CommoditySkuRequest1
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 管理系统平台
   */
  externalTrainingPlatform?: Array<string>
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderSortRequest {
  /**
   * 需要排序的字段
   */
  field?: OrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 订单基本信息查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderBasicDataRequest {
  /**
   * 订单类型
1:常规订单 2:批次关联订单
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType?: number
  /**
   * 批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 订单状态
<br> 1:正常 2：交易完成 3：交易关闭
@see OrderStatus
   */
  orderStatusList?: Array<number>
  /**
   * 订单支付状态
<br> 0:未支付 1：支付中 2：已支付
@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
   */
  orderPaymentStatusList?: Array<number>
  /**
   * 订单发货状态
<br> 0:未发货 1：发货中 2：已发货
@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
   */
  orderDeliveryStatusList?: Array<number>
  /**
   * 订单状态变更时间
   */
  orderStatusChangeTime?: OrderStatusChangeTimeRequest
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
  /**
   * 订单价格范围
<br> 查询非0元订单 begin填0.01
   */
  orderAmountScope?: BigDecimalScopeExcelRequest
}

/**
 * 订单支付信息相关查询参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderPayInfoRequest {
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

/**
 * 订单状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderStatusChangeTimeRequest {
  /**
   * 订单处于正常状态时间范围(创建时间范围)
   */
  normalDateScope?: DateScopeExcelRequest
  /**
   * 订单创建时间范围
   */
  completedDatesScope?: DateScopeExcelRequest
}

/**
 * 退货单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderRequest {
  /**
   * 退货单号
   */
  returnOrderNoList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: ReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: ReturnOrderApprovalInfoRequest
  /**
   * 退货商品id集合
   */
  returnCommoditySkuIdList?: Array<string>
  /**
   * 退款商品id集合
   */
  refundCommoditySkuIdList?: Array<string>
  /**
   * 退货单关联子订单查询参数
   */
  subOrderInfo?: SubOrderInfoRequest
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 退货商品
   */
  returnCommodity?: CommoditySkuRequest1
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class ReturnSortRequest {
  /**
   * 需要排序的字段
   */
  field?: ReturnOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

/**
 * 退货单关联子订单的主订单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class OrderInfoRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 关联批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 买家id集合
   */
  buyerIdList?: Array<string>
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 原始订单交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 专题名称
   */
  saleChannelName?: string
}

/**
 * 退货单关闭信息
<AUTHOR>
@date 2022年4月11日 11:33:35
 */
export class ReturnCloseReasonRequest {
  /**
   * 退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
@see ReturnOrderCloseTypes
   */
  closeTypeList?: Array<number>
}

/**
 * 退货单审批信息查询参数
<AUTHOR>
@date 2022/03/18
 */
export class ReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime?: DateScopeExcelRequest
}

/**
 * 退货单基本信息查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderBasicDataRequest {
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   */
  returnOrderStatus?: Array<number>
  /**
   * 退货单申请来源类型
SUB_ORDER
BATCH_RETURN_ORDER
@see ReturnOrderApplySourceTypes
   */
  applySourceType?: string
  /**
   * 来源ID集合
   */
  applySourceIdList?: Array<string>
  /**
   * 退货单关闭信息
   */
  returnCloseReason?: ReturnCloseReasonRequest
  /**
   * 退货单状态变更时间
   */
  returnStatusChangeTime?: ReturnOrderStatusChangeTimeRequest
  /**
   * 退款金额范围
<br> 查询非0元  begin填0.01
   */
  refundAmountScope?: BigDecimalScopeExcelRequest
}

/**
 * 退货单状态变更时间查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied?: DateScopeExcelRequest
  /**
   * 退货单完成时间
<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted?: DateScopeExcelRequest
}

/**
 * <AUTHOR>
@date 2022/03/24
 */
export class SubOrderInfoRequest {
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 订单查询参数
   */
  orderInfo?: OrderInfoRequest
}

/**
 * 商品开通统计报表查询参数
<AUTHOR>
@date 2022/05/11
 */
export class TradeReportRequest {
  /**
   * 交易时间范围
   */
  tradeTime?: DateScopeExcelRequest
  /**
   * 买家所在地区路径
   */
  buyerAreaPath?: Array<string>
  /**
   * 商品查询条件
   */
  commoditySku?: CommoditySkuRequest12
  /**
   * 是否包含集体缴费信息
   */
  containsCollective: boolean
  /**
   * 买家所在地区是否需要包含全部下级地区数据
   */
  isAllCotained?: boolean
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 排除的销售渠道
0-自营 1-分销 2-专题 3-华医网
   */
  excludedSaleChannels?: Array<number>
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户id
   */
  portalId?: string
  jobName?: string
}

/**
 * 商品查询参数
<AUTHOR>
@date 2022/05/11
 */
export class CommoditySkuRequest12 {
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 学习方案查询参数
   */
  scheme?: SchemeRequest12
}

/**
 * 方案查询参数
<AUTHOR>
@date 2022/05/11
 */
export class SchemeRequest12 {
  /**
   * 方案类型
@see SchemeType
   */
  schemeType?: string
  /**
   * 方案学时
   */
  schemePeriodScope?: DoubleScopeRequest
}

/**
 * 发票关联订单查询参数
<AUTHOR>
@date 2022/3/18
 */
export class InvoiceAssociationInfoRequest {
  /**
   * 关联订单类型
0:订单号
1:批次单号
@see AssociationTypes
   */
  associationType?: number
  /**
   * 订单号 | 批次单号
   */
  associationIdList?: Array<string>
  /**
   * 买家信息
   */
  buyerIdList?: Array<string>
  /**
   * 企业id
   */
  unitBuyerUnitIdList?: Array<string>
  /**
   * 收款账号
   */
  receiveAccountIdList?: Array<string>
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 发票状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class InvoiceStatusChangeTimeRequest1 {
  /**
   * 正常
   */
  normal?: DateScopeExcelRequest
  /**
   * 作废
   */
  invalid?: DateScopeExcelRequest
}

/**
 * 线下发票查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceExportRequest {
  /**
   * 发票ID集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基本信息
   */
  basicData?: OfflineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfo?: InvoiceAssociationInfoRequest
  /**
   * 发票配送信息
   */
  invoiceDeliveryInfo?: OfflineInvoiceDeliveryInfoRequest
  jobName?: string
}

/**
 * 配送地址信息
<AUTHOR>
@date 2022/05/07
 */
export class DeliveryAddressRequest {
  /**
   * 收件人
   */
  consignee?: string
}

/**
 * 配送状态变更时间查询参数
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryStatusChangeTimeRequest {
  /**
   * 未就绪
   */
  unReady?: DateScopeExcelRequest
  /**
   * 已就绪
   */
  ready?: DateScopeExcelRequest
  /**
   * 已配送
   */
  shipped?: DateScopeExcelRequest
  /**
   * 已自取
   */
  taken?: DateScopeExcelRequest
}

/**
 * 快递信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class ExpressRequest {
  /**
   * 快递单号
   */
  expressNo?: string
}

/**
 * 发票开票状态变更时间记录查询参数
<AUTHOR>
@date 2022/04/06
 */
export class InvoiceBillStatusChangTimeRequest {
  /**
   * 发票申请开票时间
   */
  unBillDateScope?: DateScopeExcelRequest
  /**
   * 发票开票时间
   */
  successDateScope?: DateScopeExcelRequest
}

/**
 * 线下发票基本信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceTypeList?: Array<number>
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory?: Array<number>
  /**
   * 发票状态
1:正常
2:作废
@see InvoiceStatus
   */
  invoiceStatus?: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime?: InvoiceStatusChangeTimeRequest1
  /**
   * 发票开票状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  billStatusList?: Array<number>
  /**
   * 发票开票状态变更时间记录
   */
  billStatusChangTime?: InvoiceBillStatusChangTimeRequest
  /**
   * 发票是否冻结
   */
  freeze?: boolean
  /**
   * 发票号集合
   */
  invoiceNoList?: Array<string>
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
}

/**
 * 线下发票配送信息
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceDeliveryInfoRequest {
  /**
   * 配送状态
0:未就绪 1：已就绪 2：已自取 3：已配送
@see OfflineDeliveryStatus
   */
  deliveryStatusList?: Array<number>
  /**
   * 配送状态变更时间记录
0:未就绪 1：已就绪 2：已自取 3：已配送
key值 {@link OfflineDeliveryStatus}
   */
  deliveryStatusChangeTime?: DeliveryStatusChangeTimeRequest
  /**
   * 配送方式
0:无 1：自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethodList?: Array<number>
  /**
   * 快递信息
   */
  express?: ExpressRequest
  /**
   * 自取信息
   */
  takeResult?: TakeResultRequest
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddressRequest
}

/**
 * 取件信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class TakeResultRequest {
  /**
   * 领取人
   */
  takePerson?: string
}

/**
 * 发票查询参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceRequest {
  /**
   * 发票id集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基础信息查询参数
   */
  basicData?: OnlineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfoList?: Array<InvoiceAssociationInfoRequest>
  /**
   * 蓝票票据查询参数
   */
  blueInvoiceItem?: OnlineInvoiceItemRequest
  /**
   * 红票票据查询参数
   */
  redInvoiceItem?: OnlineInvoiceItemRequest
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  jobName?: string
}

/**
 * 发票开具状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class BillStatusChangeTimeRequest {
  /**
   * 未开具
   */
  unBill?: DateScopeExcelRequest
  /**
   * 开票中
   */
  billing?: DateScopeExcelRequest
  /**
   * 开票成功
   */
  success?: DateScopeExcelRequest
  /**
   * 开票失败
   */
  failure?: DateScopeExcelRequest
}

/**
 * 发票状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class InvoiceStatusChangeTimeRequest {
  /**
   * 正常
   */
  normal?: DateScopeExcelRequest
  /**
   * 作废
   */
  invalid?: DateScopeExcelRequest
}

/**
 * 发票基础信息查询参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceType?: number
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategoryList?: Array<number>
  /**
   * 发票状态变更时间
@see InvoiceStatus
   */
  invoiceStatusChangeTime?: InvoiceStatusChangeTimeRequest
  /**
   * 发票状态
1：正常 2：作废
@see InvoiceStatus
   */
  invoiceStatusList?: Array<number>
  /**
   * 蓝票票据开具状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  blueInvoiceItemBillStatusList?: Array<number>
  /**
   * 红票票据开具状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  redInvoiceItemBillStatusList?: Array<number>
  /**
   * 发票是否已冲红
   */
  flushed?: boolean
  /**
   * 发票是否已生成红票票据
   */
  redInvoiceItemExist?: boolean
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
  /**
   * 发票是否冻结
   */
  freeze?: boolean
}

/**
 * 发票票据
<AUTHOR>
@date 2022/03/18
 */
export class OnlineInvoiceItemRequest {
  /**
   * 票据开具状态变更时间
   */
  billStatusChangeTime?: BillStatusChangeTimeRequest
  /**
   * 发票号码
   */
  billNoList?: Array<string>
}

/**
 * 试题查询条件
 */
export class QuestionRequest {
  /**
   * 试题ID集合
   */
  questionIdList?: Array<string>
  /**
   * 题库ID集合
   */
  libraryIdList?: Array<string>
  /**
   * 关联课程ID集合
   */
  relateCourseIds?: Array<string>
  /**
   * 试题题目
   */
  topic?: string
  /**
   * 试题类型（1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题）
   */
  questionType?: number
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest1
  /**
   * 是否启用
   */
  isEnabled?: boolean
  jobName?: string
}

/**
 * 功能描述：时间范围查询条件
@Author： wtl
@Date： 2022/1/25 15:30
 */
export class DateScopeRequest1 {
  /**
   * 开始时间
查询大于等于开始时间的结果
   */
  beginTime?: string
  /**
   * 结束时间
查询小于等于结束时间的结果
   */
  endTime?: string
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

/**
 * 异步任务组名返回对象
 */
export class JobGroupResponse {
  /**
   * 异步任务组key
   */
  group: string
  /**
   * 异步任务组名
   */
  groupName: string
  /**
   * 排序大小
   */
  order: number
  /**
   * 所在域
   */
  domain: Array<string>
}

/**
 * 功能描述：异步任务日志返回对象
@Author： wtl
@Date： 2022/4/11 17:18
 */
export class UserJobLogResponse {
  /**
   * 任务id
   */
  jobId: string
  /**
   * 任务组名
   */
  group: string
  /**
   * 任务名
   */
  jobName: string
  /**
   * 任务开始时间
   */
  beginTime: string
  /**
   * 任务结束时间
   */
  endTime: string
  /**
   * 任务状态(executing:运行中 executed:运行完成 fail:运行失败)
@see UserJobState
   */
  jobState: string
  /**
   * 异步任务处理结果（true:成功 false:失败）
   */
  jobResult: boolean
  /**
   * 任务执行成功或失败的信息
   */
  message: string
  /**
   * 导出文件路径
   */
  exportFilePath: string
  /**
   * 是否受保护
   */
  isProtected: boolean
  /**
   * 资源id
   */
  fileResourceId: string
  /**
   * 操作人id
   */
  operatorUserId: string
  /**
   * 操作人帐户id
   */
  operatorAccountId: string
}

export class UserJobLogResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserJobLogResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量生成学员学习心得pdf
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async BatchStudentLearningExperienceExportPdf(
    request: StudentLearningExperienceRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.BatchStudentLearningExperienceExportPdf,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次单明细
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchOrderDetailInServicer(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchOrderDetailInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次单
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchOrderInServicer(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchOrderInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次对账
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReconciliationInServicer(
    params: { request?: BatchOrderRequest; sort?: Array<BatchOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReconciliationInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次退货单明细
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnOrderDetailExcelInServicer(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnOrderDetailExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnOrderExcelInServicer(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnOrderExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出批次退货报名对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportBatchReturnReconciliationExcelInServicer(
    params: { request?: BatchReturnOrderRequest; sort?: Array<BatchReturnOrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportBatchReturnReconciliationExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出中心财务数据表
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCentralFinancialDataInServicer(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCentralFinancialDataInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出学员选课统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportChooseCourseStatistic(
    request: ChooseCourseStatisticsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportChooseCourseStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出学员选课统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportChooseCourseStatisticsInServicer(
    request: ChooseCourseStatisticsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportChooseCourseStatisticsInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出商品开通统计列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommodityOpenReportFormsInServicer(
    request: TradeReportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommodityOpenReportFormsInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 商品导出
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommoditySkuInServicer(
    params: { queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommoditySkuInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出方案配置专题信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommoditySkuWithTrainingChannelInServicer(
    params: { queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCommoditySkuWithTrainingChannelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：当前服务商下课程导出
   * @return : void
   * @Author： lishn
   * @Date： 2022/11/25 15:14
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCourseInServicer(
    request: UnauthorizedCourseRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCourseInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出培训单位销售统计报表   课件供应商维度
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCourseSalesStatistics(
    request: CourseSalesStatisticsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCourseSalesStatistics,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出培训单位销售统计报表   课件供应商+职业等级维度
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCourseSalesStatisticsDetail(
    request: CourseSalesStatisticsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportCourseSalesStatisticsDetail,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 发票配送导出
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportInvoiceDeliveryInServicer(
    request: OfflineInvoiceExportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportInvoiceDeliveryInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 线下发票导出
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOfflineInvoiceInServicer(
    request: OfflineInvoiceExportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOfflineInvoiceInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 线下发票导出-继续教育
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOfflineInvoiceInServicerForJxjy(
    request: OfflineInvoiceExportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOfflineInvoiceInServicerForJxjy,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 线上发票导出
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOnlineInvoiceInServicer(
    request: OnlineInvoiceRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOnlineInvoiceInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 线上发票导出-继续教育
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOnlineInvoiceInServicerForJxjy(
    request: OnlineInvoiceRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOnlineInvoiceInServicerForJxjy,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人订单
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrderExcelInServicer(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportOrderExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出试题
   * @param request 查询参数对象
   * @return true 成功 false 失败
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportQuestionExcelInServicer(
    request: QuestionRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportQuestionExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人对账
   * @param request
   * @param sort
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliationExcelInServicer(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReconciliationExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前服务商下地区学习统计详情
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportRegionLearningReportFormsDetailExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionLearningReportFormsDetailExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商地区管理员下地区学习统计详情
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportRegionLearningReportFormsDetailExcelInServicerManageRegion(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionLearningReportFormsDetailExcelInServicerManageRegion,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前服务商下地区学习统计
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportRegionLearningReportFormsExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionLearningReportFormsExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商地区管理员下地区学习统计
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportRegionLearningReportFormsExcelInServicerManageRegion(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionLearningReportFormsExcelInServicerManageRegion,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前服务商下地区学习统计-未报名人员
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportRegionLearningReportFormsNoRegisterDetailExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionLearningReportFormsNoRegisterDetailExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出地区开通统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportRegionOpenReportFormsInServier(
    request: TradeReportRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionOpenReportFormsInServier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前服务商下地区学习统计详情（无考试相关信息）
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportRegionStudyingReportFormsDetailExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionStudyingReportFormsDetailExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商下地区学习统计(新增地区人数，无考试相关信息)
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportRegionStudyingReportFormsExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportRegionStudyingReportFormsExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出个人退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnOrderExcelInServicer(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnOrderExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人报名退货对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnReconciliationExcelInServicer(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportReturnReconciliationExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前服务商下方案学习统计详情
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportSchemeLearningReportFormsDetailExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportSchemeLearningReportFormsDetailExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商下方案学习统计
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportSchemeLearningReportFormsExcelInServicer(
    request: LearningReportFormsRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportSchemeLearningReportFormsExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：学员导出
   * @return : void
   * @Author： wtl
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentExcelInServicer(
    request: StudentQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：项目级-学员导出
   * @return : void
   * @Author： wtl
   * @Date： 2022年11月17日 15:25:00
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentExcelInSubProject(
    request: StudentQueryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentExcelInSubProject,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出学员学习心得
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentLearningExperienceInServicer(
    request: StudentLearningExperienceRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentLearningExperienceInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出当前服务商下学员方案学习
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportStudentSchemeLearningExcelInDistributor(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商下学员方案学习
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportStudentSchemeLearningExcelInServicer(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 导出当前服务商地区管理员下学员方案学习
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentSchemeLearningExcelInServicerManageRegion(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInServicerManageRegion,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出专题的方案
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingChannelCommoditySkuInServicer(
    params: { queryRequest?: CommoditySkuRequest; sortRequest?: Array<CommoditySkuSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportTrainingChannelCommoditySkuInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询导出任务组
   * @param jobGroupRequest : 任务查询条件
   * @return : void
   * @Author： sjm
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param jobGroupRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExportTaskGroupInfoInServicer(
    jobGroupRequest: JobGroupRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listExportTaskGroupInfoInServicer,
    operation?: string
  ): Promise<Response<Array<JobGroupResponse>>> {
    return commonRequestApi<Array<JobGroupResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { jobGroupRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询导出任务组
   * @param jobGroupRequest : 任务查询条件
   * @return : void
   * @Author： sjm
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param jobGroupRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listExportTaskGroupInfoInServicerMangeRegion(
    jobGroupRequest: JobGroupRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listExportTaskGroupInfoInServicerMangeRegion,
    operation?: string
  ): Promise<Response<Array<JobGroupResponse>>> {
    return commonRequestApi<Array<JobGroupResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { jobGroupRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 功能描述：分页查询我的导出任务日志信息列表
   * @param jobRequest : 任务查询条件
   * @return : void
   * @Author： wtl
   * @Date： 2022/1/18 15:14
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageExportTaskInfoInMyself(
    params: { page?: Page; jobRequest?: JobRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageExportTaskInfoInMyself,
    operation?: string
  ): Promise<Response<UserJobLogResponsePage>> {
    return commonRequestApi<UserJobLogResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
