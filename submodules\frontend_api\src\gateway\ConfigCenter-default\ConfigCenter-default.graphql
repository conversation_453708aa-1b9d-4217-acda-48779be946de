schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取指定key的值
		@param key
		@return
	"""
	getPropertyValue(key:String):String @NotAuthenticationRequired
	"""获取组件的所有业务配置
		@return
	"""
	getComponentBusinessProperties(componentName:String):[PropertyDTO] @NotAuthenticationRequired
	"""获取指定namespace下的所有业务配置
		@return
	"""
	getNamespaceProperties(namespace:String):[PropertyDTO] @NotAuthenticationRequired
	"""获取子项目下所有业务配置
		@return
	"""
	getBusinessProperties:[PropertyDTO] @NotAuthenticationRequired
}
"""配置
	<AUTHOR> create 2019/12/5 18:57
"""
type PropertyDTO @type(value:"com.fjhb.platform.core.configcenter.v1.api.dto.PropertyDTO") {
	"""键"""
	key:String
	"""值"""
	value:String
}

scalar List
