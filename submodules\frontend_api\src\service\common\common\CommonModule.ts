import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import PlatformLearningSchemeGateway from '@api/gateway/PlatformLearningScheme'
import { UnAuthorize } from '@api/Secure'
import { SkuPropertyAndOption } from '@api/service/common/models/common/SkuPropertyAndOption'
import { ResponseStatus } from '@api/Response'
import { SkuPropertyOption } from '@api/service/common/models/common/SkuPropertyOption'
import { SkuValueAndName } from '@api/service/common/models/common/SkuValueAndName'

/**
 * 通用模块数据层
 */
export interface ICommonState {
  /**
   * 系统提供的sku属性
   */
  skuPropertyAndOptionList: Array<SkuPropertyAndOption>
  /**
   * 年度属性是否已经加载过
   */
  skuLoad: boolean
}

@Module({ namespaced: true, dynamic: true, name: 'CommonModule', store })
class CommonModule extends VuexModule implements ICommonState {
  /**
   * 年度属性是否已经加载过
   */
  skuLoad: boolean
  /**
   * 系统提供的sku属性1
   */
  skuPropertyAndOptionList: Array<SkuPropertyAndOption> = new Array<SkuPropertyAndOption>()
  /**
   * 适用人群常量——适用所有人群
   */
  SUITABLE_ALL = '所有人群'
  /**
   * 知识型技能工种类别id
   */
  KNOWLEDGE_WORK_TYPE_ID = '2ca4aabc7770e5470177768a50840017'
  /**
   * 操作型技能工种类别id
   */
  OPERATE_WORK_TYPE_ID = '2ca4aabc7770e5470177768a63c90018'
  /**
   * 紧缺急需工种类别id
   */
  URGENT_NEED_WORK_TYPE_ID = '2ca4aabc7770e5470177768a73c10019'

  @UnAuthorize
  @Action
  async init() {
    if (!this.skuLoad) {
      const response = await PlatformLearningSchemeGateway.listAllSkuPropertyAndOption()
      if (response.status.isSuccess()) {
        this.SET_SKU_LIST(response.data)
        this.skuLoad = true
      }
      console.log('sku属性加载完成')
      return response.status
    } else {
      console.log('sku属性已经加载过，忽略加载')
      return new ResponseStatus(200)
    }
  }

  /**
   * 获取适用人群列表
   */
  get getSuitablePeopleList() {
    const list = this.skuPropertyAndOptionList?.filter(p => p.code === 'suitablePeople')
    return list && list.length > 0 ? list[0].optionList : []
  }

  /**
   * 培训类别可选项
   */
  get getTrainingTypeList(): Array<SkuPropertyOption> {
    const property = this.skuPropertyAndOptionList.filter(p => p.code === 'trainingType')
    return property && property.length ? property[0].optionList : []
  }

  /**
   * 培训对象可选项
   */
  get getTraineesList(): Array<SkuPropertyOption> {
    const property = this.skuPropertyAndOptionList.filter(p => p.code === 'trainees')
    return property && property.length ? property[0].optionList : []
  }

  /**
   * 岗位类别可选项
   */
  get getJobCategoryList(): Array<SkuPropertyOption> {
    const property = this.skuPropertyAndOptionList.filter(p => p.code === 'jobCategory')
    return property && property.length ? property[0].optionList : []
  }

  /**
   * 单位类别可选项
   */
  get getUnitCategoryList(): Array<SkuPropertyOption> {
    const property = this.skuPropertyAndOptionList.filter(p => p.code === 'unitCategory')
    return property && property.length ? property[0].optionList : []
  }

  /**
   * 年度可选项
   */
  get getYearList(): Array<SkuPropertyOption> {
    const property = this.skuPropertyAndOptionList.filter(p => p.code === 'year')
    return property && property.length ? property[0].optionList : []
  }

  /**
   * 通过选项值id填充对应的选项显示名称
   */
  get appendSkuNameByValue() {
    return (skuPropertyAndValue: SkuValueAndName): SkuValueAndName => {
      for (const skuPropertyAndOption of this.skuPropertyAndOptionList) {
        switch (skuPropertyAndOption.code) {
          case 'trainingType': {
            const item = skuPropertyAndOption.optionList.find(p => p.id === skuPropertyAndValue.trainingTypeId)
            skuPropertyAndValue.trainingTypeName = item?.optionName || ''
            break
          }
          case 'workTypeId': {
            const item = skuPropertyAndOption.optionList.find(p => p.id === skuPropertyAndValue.workTypeId)
            skuPropertyAndValue.workTypeName = item?.optionName || ''
            break
          }
        }
      }
      return skuPropertyAndValue
    }
  }

  @Mutation
  SET_SKU_LIST(list: Array<any>) {
    this.skuPropertyAndOptionList = new Array<SkuPropertyAndOption>()
    this.skuPropertyAndOptionList.push(...list)
  }
}

export default getModule(CommonModule)
