<route-meta>
{
"isMenu": true,
"title": "管理员帐号管理",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="activated" query>
    <div class="f-p15">
      <div class="f-mb15">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="toAddUserPage"
          mutation
          v-if="$hasPermission('create')"
          desc="创建"
          actions="@hbfe/jxjy-admin-account/src/distribution-administrator-account/create.vue"
          >新增管理员</el-button
        >
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <hb-search-wrapper @reset="reset" class="m-query is-border-bottom">
          <el-form-item label="管理员名称">
            <el-input clearable placeholder="请输入管理员名称" v-model="userParam.name" />
          </el-form-item>
          <el-form-item label="管理员帐号">
            <el-input clearable placeholder="请输入管理员帐号" v-model="userParam.account" />
          </el-form-item>
          <el-form-item label="帐号状态">
            <el-select v-model="userParam.status" clearable filterable placeholder="请选择帐号状态">
              <el-option label="正常" :value="1">正常</el-option>
              <el-option label="冻结" :value="2">冻结</el-option>
              <el-option label="注销" :value="3">注销</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="角色">
            <el-select v-model="userParam.roleId" clearable filterable placeholder="请选择角色">
              <el-option v-for="item in roleList" :key="item.id" :value="item.id" :label="item.name"></el-option>
            </el-select>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="search">查询</el-button>
          </template>
        </hb-search-wrapper>
        <!--表格-->
        <el-table stripe :data="tableData" ref="adminTableRef" max-height="500px" class="m-table" v-loading="loading">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="姓名" min-width="100" fixed="left" prop="userName"> </el-table-column>
          <el-table-column label="帐号" min-width="150" prop="adminAccount"> </el-table-column>
          <el-table-column label="赋予的角色" min-width="240">
            <template slot-scope="scope">
              <span v-for="val in scope.row.currentDistributorRoles" :key="val.roleId">{{ val.roleName }} </span>
            </template>
          </el-table-column>
          <el-table-column label="状态" min-width="100">
            <template slot-scope="scope">
              <div v-if="scope.row.currentDistributorStatus">
                <el-badge is-dot type="success" class="badge-status">正常</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="info" class="badge-status">冻结</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" align="center" fixed="right">
            <template slot-scope="scope">
              <div v-if="accountId == scope.row.accountId">
                <span class="f-co"><i class="el-icon-star-on"></i>当前帐号</span>
              </div>
              <div v-else>
                <el-button
                  type="text"
                  size="mini"
                  @click="lookUserDetail(scope.row.userId)"
                  v-if="$hasPermission('detail')"
                  desc="详情"
                  query
                  actions="lookUserDetail"
                  >详情</el-button
                >
                <el-button
                  type="text"
                  size="mini"
                  @click="toModifyPage(scope.row.userId)"
                  v-if="$hasPermission('modify')"
                  desc="修改"
                  mutation
                  actions="@hbfe/jxjy-admin-account/src/distribution-administrator-account/modify.vue"
                  >修改</el-button
                >
                <template v-if="$hasPermission('resetPassword')" desc="重置密码" actions="resetPassword">
                  <hb-popconfirm
                    placement="top"
                    title="确认重置该账号的密码为gly123吗？"
                    mutation
                    @confirm="resetPassword(scope.row.accountId)"
                  >
                    <el-button slot="reference" type="text" size="mini">重置密码</el-button>
                  </hb-popconfirm></template
                >
                <template v-if="$hasPermission('deactivate')" desc="停用" mutation actions="doDeactivate">
                  <hb-popconfirm
                    placement="top"
                    v-if="scope.row.currentDistributorStatus"
                    title="是否停用该管理员账号"
                    @confirm="doDeactivate(scope.row)"
                  >
                    <el-button slot="reference" type="text" size="mini">停用</el-button>
                  </hb-popconfirm>
                </template>
                <template v-if="$hasPermission('enable')" mutation desc="启用" actions="doEnable">
                  <hb-popconfirm
                    placement="top"
                    v-if="!scope.row.currentDistributorStatus"
                    title="是否启用该管理员账号"
                    @confirm="doEnable(scope.row)"
                  >
                    <el-button slot="reference" type="text" size="mini">启用</el-button>
                  </hb-popconfirm>
                </template>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </el-card>
    </div>
    <administrator-detail ref="detailRef"></administrator-detail>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  import { UiPage } from '@hbfe/common'
  import PageAdminInfoResponse from '@api/service/management/user/query/manager/vo/PageAdminInfoResponse'
  import PageAdminListRequest from '@api/service/management/user/query/manager/vo/PageAdminListRequest'
  import ResetPwdBusinessVo from '@api/service/management/user/mutation/vo/ResetPwdBusinessVo'
  import AuthorityModule from '@api/service/management/authority/AuthorityModule'
  import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
  import administratorDetail from '@hbfe/jxjy-admin-account/src/distribution-administrator-account/__components__/administrator-detail.vue'
  import { QueryRoleList } from '@api/service/management/authority/role/query/QueryRoleList'
  import { ElTable } from 'element-ui/types/table'
  import RoleVo from '@api/service/management/user/query/manager/vo/RoleVo'
  @Component({
    components: { administratorDetail }
  })
  export default class extends Vue {
    @Ref('detailRef') detailRef: administratorDetail
    @Ref('adminTableRef') adminTableRef: ElTable
    page: UiPage
    loading = false
    getQueryRoleList: QueryRoleList = AuthorityModule.roleFactory.getQueryRoleList()
    userParam: PageAdminListRequest = new PageAdminListRequest()
    tableData: Array<PageAdminInfoResponse> = new Array<PageAdminInfoResponse>()
    resetParam: ResetPwdBusinessVo = new ResetPwdBusinessVo()
    roleList: Array<RoleInfoResponseVo> = new Array<RoleInfoResponseVo>()
    accountId = ''
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    // 前往创建管理员账号页面
    toAddUserPage(): void {
      this.$router.push('/basic-data/account/distribution-administrator-account/create')
    }

    // 查看管理员详情
    async lookUserDetail(id: string) {
      // 根据id获取详情
      this.detailRef.userDetail = await UserModule.queryUserFactory.queryManager.queryAdminInfo(id)
      this.detailRef.getUserRoleInfoList()
      this.detailRef.isShowDrawer = true
    }

    // 前往修改账号信息页面
    toModifyPage(id: string): void {
      this.$router.push(`/basic-data/account/distribution-administrator-account/modify/${id}`)
    }
    //重置密码
    async resetPassword(id: string) {
      const res = await UserModule.mutationUserFactory.resetPwdBussiness.doResetAdminPwd(id, 'gly123')
      if (res?.status?.isSuccess()) {
        this.$message.success('重置成功')
      } else {
        this.$message.warning('重置失败')
      }
    }
    // 查询
    async search() {
      this.page.pageNo = 1
      await this.doQueryPage()
    }
    async activated() {
      const adminInfo = await UserModule.queryUserFactory.queryManagerDetail.adminInfo
      this.accountId = adminInfo.accountInfo.accountId
      await this.getQueryRoleList.queryRoleList()
      this.roleList = this.getQueryRoleList.roleList
      await this.doQueryPage()
    }

    // 请求列表
    async doQueryPage() {
      try {
        this.loading = true
        // 分销商管理员
        this.userParam.type = 4
        this.userParam.roleCategory = this.roleList.find((item) => item.id === this.userParam.roleId)?.category
        this.tableData = await UserModule.queryUserFactory.queryManager.queryPageDistributorAdminList(
          this.page,
          this.userParam
        )
        this.loading = false
      } catch (e) {
        console.log(e)
        this.$message.error('请求失败')
        this.loading = false
      } finally {
        this.adminTableRef?.doLayout()
        this.loading = false
      }
    }
    //停用
    async doDeactivate(row: PageAdminInfoResponse) {
      const res = await UserModule.mutationUserFactory
        .mutationManagerBusiness(row.accountId)
        .doDeactivateDistributor(row.currentDistributorRoles?.map((item) => item.roleId))
      if (res?.status?.isSuccess()) {
        await this.doQueryPage()
        this.$message.success('停用成功')
      } else {
        this.$message.warning('停用失败')
      }
    }
    //启用
    async doEnable(row: PageAdminInfoResponse) {
      const res = await UserModule.mutationUserFactory
        .mutationManagerBusiness(row.accountId)
        .doEnableDistributor(row.currentDistributorRoles?.map((item) => item.roleId))
      if (res?.status?.isSuccess()) {
        await this.doQueryPage()
        this.$message.success('启用成功')
      } else {
        this.$message.warning('启用失败')
      }
    }
    async reset() {
      this.userParam = new PageAdminListRequest()
      await this.doQueryPage()
    }
  }
</script>
