<template>
  <div class="m-login-wrap bg-forget">
    <div class="wrap-bd m-forget">
      <el-button type="primary" size="mini" plain><i class="iconfont icon-lsh-return f-mr5"></i>返回登录</el-button>
      <!--第一步-->
      <el-steps :active="1" align-center class="m-steps f-ptb50 f-mt30">
        <el-step title="验证身份"></el-step>
        <el-step title="设置新密码"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
      <el-row type="flex" justify="center">
        <el-col :span="8">
          <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
            <el-form-item label="姓名：" required>
              <el-input v-model="form.name" clearable placeholder="请输入姓名" />
            </el-form-item>
            <el-form-item label="帐号：" required>
              <el-input v-model="form.name" clearable placeholder="请输入帐号" />
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button type="primary">下一步</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row type="flex" justify="center">
        <el-col :span="10">
          <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
            <el-form-item label=" " class="is-text">
              <el-alert type="warning" show-icon :closable="false" class="m-alert">
                重置密码需要短信验证您的安全手机
              </el-alert>
              <p class="f-mt15">点击获取验证码，短信将发送至安全手机 <span class="f-fb">158****2228</span></p>
            </el-form-item>
            <el-form-item label="图形验证码：" required>
              <div class="f-flex">
                <el-input v-model="form.name" clearable placeholder="请输入图形验证码" class="f-flex-sub" />
                <div class="code">
                  <img src="../trainingInstitution/assets/images/code.jpg" title="看不清，点击刷新" />
                </div>
              </div>
            </el-form-item>
            <el-form-item label="短信校验码：" required>
              <div class="f-flex">
                <el-input v-model="form.name" clearable placeholder="请输入短信校验码" class="f-flex-sub" />
                <div class="code">
                  <el-button type="primary" plain>获取短信验证码</el-button>
                  <!--<el-button type="info" plain disabled>重新获取（60s）</el-button>-->
                </div>
              </div>
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button type="primary">下一步</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <!--第二步-->
      <el-steps :active="2" align-center class="m-steps f-ptb50 f-mt30">
        <el-step title="验证身份"></el-step>
        <el-step title="设置新密码"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
      <el-row type="flex" justify="center">
        <el-col :span="11">
          <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
            <el-form-item label="密码：" required>
              <el-input
                v-model="form.name"
                clearable
                show-password
                placeholder="请输入6-18位由字母、数字和符号两种及以上组合的密码"
              />
              <!--密码安全判断-->
              <div class="psw-tips">
                <el-progress :percentage="33.33" color="#e93737" :show-text="false"></el-progress>
                <!--弱：txt-l，中：txt-m，强：txt-h-->
                <span class="txt txt-l">弱</span>
              </div>
              <div class="psw-tips">
                <el-progress :percentage="66.66" color="#ee9e2d" :show-text="false"></el-progress>
                <!--弱：txt-l，中：txt-m，强：txt-h-->
                <span class="txt txt-m">中</span>
              </div>
              <div class="psw-tips">
                <el-progress :percentage="100" color="#49b042" :show-text="false"></el-progress>
                <!--弱：txt-l，中：txt-m，强：txt-h-->
                <span class="txt txt-h">强</span>
              </div>
            </el-form-item>
            <el-form-item label="确认密码：" required>
              <el-input v-model="form.name" clearable show-password placeholder="请再次输入密码" />
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button type="primary">下一步</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <!--第三步-->
      <el-steps :active="3" align-center class="m-steps f-ptb50 f-mt30">
        <el-step title="验证身份"></el-step>
        <el-step title="设置新密码"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
      <el-result icon="success" title="新密码设置成功，请前往登录！">
        <template slot="extra">
          <el-button type="primary" size="medium">立即前往</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>
<script>
  export default {
    data() {
      return {
        switch1: false,
        switch2: false,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          name1: '周一至周五 (09:00 - 12:00 14:00 - 17:30)',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down'],
        rules: {
          name: [{ required: true, message: '请输入帐号/手机号', trigger: 'blur' }]
        }
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
