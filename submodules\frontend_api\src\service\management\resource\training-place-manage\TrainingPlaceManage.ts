import TrainingPlaceInfo from '@api/service/management/resource/training-place-manage/TrainingPlaceInfo'
import TrainingPointManageParams from '@api/service/management/resource/training-place-manage/models/TrainingPlaceManageParams'
import { Page } from '@hbfe/common'
import SchemeLearningQueryBackstage, {
  GetTrainingPointDetailRequest,
  TrainingPointResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { getTrainingPointDetailInServicer } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage/graphql-importer'
import TrainingPlaceInfoDto from '@api/service/management/resource/training-place-manage/models/TrainingPlaceInfoDto'

/**
 * 培训点管理
 */
export default class TrainingPlaceManage {
  /**
   * 培训点列表
   */
  list: TrainingPlaceInfo[] = []
  /**
   * 培训点管理入参
   */
  params: TrainingPointManageParams = new TrainingPointManageParams()

  /**
   * 获取培训点列表
   * @param page
   */
  async queryList(page: Page) {
    const request = TrainingPointManageParams.toTrainingPointRequest(this.params)
    request.nameSearchPattern = 'LIKE'
    const response = await SchemeLearningQueryBackstage.pageTrainingPointInServicer({ page, request })
    if (!response.status.isSuccess()) {
      console.error('获取培训点报错', response)
      return response
    }
    page.totalPageSize = response.data.totalPageSize
    page.totalSize = response.data.totalSize

    if (response.data?.currentPageData?.length) {
      this.list = response.data.currentPageData.map((res) =>
        Object.assign(new TrainingPlaceInfo(), TrainingPlaceInfo.toTrainingPlaceInfo(res))
      )
      const tempRegionIds = this.list.map((res) => res.trainingPlaceRegionIds)
      let regionIds = tempRegionIds.flat()
      // 去重
      regionIds = Array.from(new Set(regionIds))
      const regionList = await QueryBusinessRegion.queryRegionsNameByIds(regionIds)
      if (regionList.size) {
        // 赋值地区
        this.list.map((res) => {
          if (res.trainingPlaceRegionIds?.length) {
            res.trainingPlaceRegionNames = res.toRegionName(res.trainingPlaceRegionIds, regionList)
          }
        })
      }
    } else {
      this.list = []
    }
    return response
  }

  /**
   * 获取培训点信息by 培训点集合
   * @param ids
   */
  async queryTrainingPlacesByIds(ids: string[]) {
    ids = Array.from(new Set(ids.filter((id) => id && id.trim() !== '')))
    const params = ids.map((res) => {
      const temp = new GetTrainingPointDetailRequest()
      temp.id = res
      return temp
    })
    const rewriteGraph = new RewriteGraph<TrainingPointResponse, GetTrainingPointDetailRequest>(
      SchemeLearningQueryBackstage._commonQuery,
      getTrainingPointDetailInServicer
    )
    await rewriteGraph.request(params)
    const trainingPlacesMap = new Map<string, TrainingPlaceInfoDto>()
    rewriteGraph.itemMap.forEach((res) => {
      if (res) {
        trainingPlacesMap.set(
          res.id,
          Object.assign(new TrainingPlaceInfoDto(), TrainingPlaceInfoDto.toTrainingPlaceInfo(res))
        )
      }
    })
    return trainingPlacesMap
  }
}
