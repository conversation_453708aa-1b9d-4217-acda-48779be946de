import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 文件类型
 */
enum FileTypesEnum {
  PDF = 1,
  IMAGE = 2
}

export { FileTypesEnum }
class FileTypes extends AbstractEnum<FileTypesEnum> {
  static enum = FileTypesEnum

  constructor(status?: FileTypesEnum) {
    super()
    this.current = status
    this.map.set(FileTypesEnum.PDF, 'PDF')
    this.map.set(FileTypesEnum.IMAGE, 'IMAGE')
  }
}

export default new FileTypes()
