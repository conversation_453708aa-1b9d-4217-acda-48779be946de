import TrackingParam from '@api/service/management/intelligence-learning/model/TrackingParam'
import { Page, Response } from '@hbfe/common'
import TaskTrackingList from '@api/service/management/intelligence-learning/model/TaskTrackingList'
import ExecutionItem from '@api/service/management/intelligence-learning/model/ExecutionItem'
import MsAutoLearningResult, {
  TaskResultByPageRequest
} from '@api/ms-gateway/ms-autolearning-student-auto-learning-task-result-v1'
import { TaskStatusEnum } from '@api/service/management/intelligence-learning/enum/TaskStatusEnum'
import QueryStudentTrainClass from '@api/service/management/train-class/query/QueryStudentTrainClass'
import { ConditionEnum, SortEnum } from '@api/service/management/intelligence-learning/enum/SortEnum'
import MsAutoLearning, { MainTaskIdsRequest } from '@api/ms-gateway/ms-autolearning-v1'
/**
 * 这些情况-服务商
 */
export default class ExecutionListInServicer extends TaskTrackingList<TrackingParam, ExecutionItem> {
  constructor() {
    super(new TrackingParam())
  }

  /**
   * 排序参数
   */
  sortParams: { type: SortEnum; condition: ConditionEnum } = null

  /**
   * 执行情况列表查询
   */
  async queryList(page: Page) {
    const request = TrackingParam.toTaskStatus(this.queryParam)
    request.pageNo = page.pageNo
    request.pageSize = page.pageSize
    const { status, data } = await MsAutoLearningResult.queryStudentAutoLearningTaskResultByPage(request)
    if (status.isSuccess()) {
      page.totalPageSize = data.totalPageSize
      page.totalSize = data.totalSize
      this.list = data.currentPageData?.map(ExecutionItem.from) ?? []
      await this.fillOrderNo()
    } else {
      this.list = []
      page.totalPageSize = 0
      page.totalSize = 0
    }
    return status
  }

  /**
   * 填充订单号
   */
  async fillOrderNo() {
    if (this.list.length) {
      const orderNoMap = await QueryStudentTrainClass.getOrderNoByStudentNoList(this.list.map((item) => item.studentNo))
      this.list.forEach((item) => {
        item.orderNo = orderNoMap.get(item.studentNo)
      })
    }
  }

  /**
   * 查询指定状态、时间段内的智能学习任务结果数量
   * @param params 不传时间段查所有
   * @return {Promise<Response<number>>}
   */
  static async queryCountByStatusAndTime(params: { status: TaskStatusEnum[] }) {
    const request = new TaskResultByPageRequest()
    request.pageNo = 1
    request.pageSize = 1

    if (params.status?.length) {
      request.resultList = [...params.status]
      request.resultList.includes(TaskStatusEnum.fail) && request.resultList.push(TaskStatusEnum.fail_arrange)
    }
    const result = new Response<number>()
    const { status, data } = await MsAutoLearningResult.queryStudentAutoLearningTaskResultByPage(request)
    if (status.isSuccess()) {
      result.data = data?.totalSize ?? 0
    }
    result.status = status
    return result
  }

  /**
   * 网校批量终止任务
   */
  async terminateTaskListInServicer(ids: string[]) {
    const param = new MainTaskIdsRequest()
    param.taskIds = ids
    const res = await MsAutoLearning.batchTerminateAutoLearning(param)
    return res
  }
}
