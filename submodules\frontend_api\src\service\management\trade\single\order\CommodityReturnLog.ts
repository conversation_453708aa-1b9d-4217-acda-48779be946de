import RefundRecordItem from '@api/service/common/return-order/models/RefundRecordItem'
import MsTradeQueryFrontGatewayCourseLearningBacktage, {
  ReturnOrderRequest,
  SubOrderInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { Page } from '@hbfe/common'

export default class CommodityReturnLog {
  /**
   * 子订单号
   */
  subOrderNo = ''

  /**
   * 退款记录列表
   */
  records: Array<RefundRecordItem> = new Array<RefundRecordItem>()

  /**
   * @param subOrderNo 子订单号
   */
  constructor(subOrderNo?: string) {
    this.subOrderNo = subOrderNo
  }

  /**
   * 查询子单退款记录（网校管理员）
   */
  async queryReturnLogDetail() {
    if (!this.subOrderNo) {
      this.records = new Array<RefundRecordItem>()
      return
    }
    const request = new ReturnOrderRequest()
    request.subOrderInfo = new SubOrderInfoRequest()
    request.subOrderInfo.subOrderNoList = [this.subOrderNo]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    const res = await MsTradeQueryFrontGatewayCourseLearningBacktage.pageReturnOrderInServicer({
      page,
      request
    })

    if (res?.data?.currentPageData?.length) {
      this.records = res.data.currentPageData.map(RefundRecordItem.from)
    } else {
      this.records = new Array<RefundRecordItem>()
    }
  }

  /**
   * 查询子单退款记录（分销商）
   */
  async queryReturnLogDetailInDistributor() {
    if (!this.subOrderNo) {
      this.records = new Array<RefundRecordItem>()
      return
    }
    const request = new ReturnOrderRequest()
    request.subOrderInfo = new SubOrderInfoRequest()
    request.subOrderInfo.subOrderNoList = [this.subOrderNo]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    const res = await MsTradeQueryFrontGatewayCourseLearningBacktage.pageReturnOrderInDistributor({
      page,
      request
    })

    if (res?.data?.currentPageData?.length) {
      this.records = res.data.currentPageData.map(RefundRecordItem.from)
    } else {
      this.records = new Array<RefundRecordItem>()
    }
  }

  /**
   * 查询子单退款记录（专题管理员）
   */
  async queryReturnLogDetailInTrainingChannel() {
    if (!this.subOrderNo) {
      this.records = new Array<RefundRecordItem>()
      return
    }
    const request = new ReturnOrderRequest()
    request.subOrderInfo = new SubOrderInfoRequest()
    request.subOrderInfo.subOrderNoList = [this.subOrderNo]
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    const res = await MsTradeQueryFrontGatewayCourseLearningBacktage.pageReturnOrderInTrainingChannel({
      page,
      request
    })

    if (res?.data?.currentPageData?.length) {
      this.records = res.data.currentPageData.map(RefundRecordItem.from)
    } else {
      this.records = new Array<RefundRecordItem>()
    }
  }
}
