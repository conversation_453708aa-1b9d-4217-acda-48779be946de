import findExamPaperPage from './queries/findExamPaperPage.graphql'
import findExamQuestionPage from './queries/findExamQuestionPage.graphql'
import findQuestion from './queries/findQuestion.graphql'
import getDailyPracticePaper from './queries/getDailyPracticePaper.graphql'
import getErrorPronePracticePaper from './queries/getErrorPronePracticePaper.graphql'
import getExamAnswerPaper from './queries/getExamAnswerPaper.graphql'
import getExamAnswerPaperByLearningId from './queries/getExamAnswerPaperByLearningId.graphql'
import getExamPaper from './queries/getExamPaper.graphql'
import getExamPaperByLearningId from './queries/getExamPaperByLearningId.graphql'
import getOrCreateCorrectionPracticePaper from './queries/getOrCreateCorrectionPracticePaper.graphql'
import getOrCreateFavoritePracticePaper from './queries/getOrCreateFavoritePracticePaper.graphql'
import getPreExamCorrectionPracticeList from './queries/getPreExamCorrectionPracticeList.graphql'
import getPreExamDailyPracticeList from './queries/getPreExamDailyPracticeList.graphql'
import getPreExamErrorPronePracticeList from './queries/getPreExamErrorPronePracticeList.graphql'
import getPreExamFavoritePracticeList from './queries/getPreExamFavoritePracticeList.graphql'
import getPreExamPracticePaper from './queries/getPreExamPracticePaper.graphql'
import getPreExamQuestionPracticeList from './queries/getPreExamQuestionPracticeList.graphql'
import getQuestionPracticePaper from './queries/getQuestionPracticePaper.graphql'
import getSubPaperClassificationList from './queries/getSubPaperClassificationList.graphql'
import getSubQuestionLibraryList from './queries/getSubQuestionLibraryList.graphql'
import hasReference from './queries/hasReference.graphql'
import isFavoriteQuestion from './queries/isFavoriteQuestion.graphql'
import isNameExists from './queries/isNameExists.graphql'
import paperClassificationHasChild from './queries/paperClassificationHasChild.graphql'
import paperClassificationHasReference from './queries/paperClassificationHasReference.graphql'
import paperClassificationNameExists from './queries/paperClassificationNameExists.graphql'
import questionLibraryHasChild from './queries/questionLibraryHasChild.graphql'
import questionLibraryHasReference from './queries/questionLibraryHasReference.graphql'
import questionLibraryNameExists from './queries/questionLibraryNameExists.graphql'
import validateFavorite from './queries/validateFavorite.graphql'
import addUserFavoriteQuestion from './mutates/addUserFavoriteQuestion.graphql'
import batchMoveQuestionToAnotherLibrary from './mutates/batchMoveQuestionToAnotherLibrary.graphql'
import copyExamPaper from './mutates/copyExamPaper.graphql'
import createExamPaper from './mutates/createExamPaper.graphql'
import createPaperClassification from './mutates/createPaperClassification.graphql'
import createQuestion from './mutates/createQuestion.graphql'
import createQuestionLibrary from './mutates/createQuestionLibrary.graphql'
import deleteExamPaper from './mutates/deleteExamPaper.graphql'
import deletePaperClassification from './mutates/deletePaperClassification.graphql'
import deleteQuestion from './mutates/deleteQuestion.graphql'
import deleteQuestionLibrary from './mutates/deleteQuestionLibrary.graphql'
import disableExamPaper from './mutates/disableExamPaper.graphql'
import disableQuestion from './mutates/disableQuestion.graphql'
import disableQuestionLibrary from './mutates/disableQuestionLibrary.graphql'
import enableExamPaper from './mutates/enableExamPaper.graphql'
import enableQuestion from './mutates/enableQuestion.graphql'
import enableQuestionLibrary from './mutates/enableQuestionLibrary.graphql'
import getPreviewTokenByPaperAndProfession from './mutates/getPreviewTokenByPaperAndProfession.graphql'
import goCorrectionPractice from './mutates/goCorrectionPractice.graphql'
import goFavoritePractice from './mutates/goFavoritePractice.graphql'
import goMockExamination from './mutates/goMockExamination.graphql'
import goMockExaminationByToken from './mutates/goMockExaminationByToken.graphql'
import goPractice from './mutates/goPractice.graphql'
import goPracticeByToken from './mutates/goPracticeByToken.graphql'
import goPracticeByTokenAndSpecifyQuestionCount from './mutates/goPracticeByTokenAndSpecifyQuestionCount.graphql'
import questionImport from './mutates/questionImport.graphql'
import redoMockExamination from './mutates/redoMockExamination.graphql'
import redoPractice from './mutates/redoPractice.graphql'
import removeUserFavoriteQuestion from './mutates/removeUserFavoriteQuestion.graphql'
import updateExamPaper from './mutates/updateExamPaper.graphql'
import updatePaperClassification from './mutates/updatePaperClassification.graphql'
import updateQuestion from './mutates/updateQuestion.graphql'
import updateQuestionLibrary from './mutates/updateQuestionLibrary.graphql'

export {
  findExamPaperPage,
  findExamQuestionPage,
  findQuestion,
  getDailyPracticePaper,
  getErrorPronePracticePaper,
  getExamAnswerPaper,
  getExamAnswerPaperByLearningId,
  getExamPaper,
  getExamPaperByLearningId,
  getOrCreateCorrectionPracticePaper,
  getOrCreateFavoritePracticePaper,
  getPreExamCorrectionPracticeList,
  getPreExamDailyPracticeList,
  getPreExamErrorPronePracticeList,
  getPreExamFavoritePracticeList,
  getPreExamPracticePaper,
  getPreExamQuestionPracticeList,
  getQuestionPracticePaper,
  getSubPaperClassificationList,
  getSubQuestionLibraryList,
  hasReference,
  isFavoriteQuestion,
  isNameExists,
  paperClassificationHasChild,
  paperClassificationHasReference,
  paperClassificationNameExists,
  questionLibraryHasChild,
  questionLibraryHasReference,
  questionLibraryNameExists,
  validateFavorite,
  addUserFavoriteQuestion,
  batchMoveQuestionToAnotherLibrary,
  copyExamPaper,
  createExamPaper,
  createPaperClassification,
  createQuestion,
  createQuestionLibrary,
  deleteExamPaper,
  deletePaperClassification,
  deleteQuestion,
  deleteQuestionLibrary,
  disableExamPaper,
  disableQuestion,
  disableQuestionLibrary,
  enableExamPaper,
  enableQuestion,
  enableQuestionLibrary,
  getPreviewTokenByPaperAndProfession,
  goCorrectionPractice,
  goFavoritePractice,
  goMockExamination,
  goMockExaminationByToken,
  goPractice,
  goPracticeByToken,
  goPracticeByTokenAndSpecifyQuestionCount,
  questionImport,
  redoMockExamination,
  redoPractice,
  removeUserFavoriteQuestion,
  updateExamPaper,
  updatePaperClassification,
  updateQuestion,
  updateQuestionLibrary
}
