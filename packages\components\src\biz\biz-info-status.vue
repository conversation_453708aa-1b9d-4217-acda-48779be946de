<route-meta>
{
"title": "资讯状态下拉选择器"
}
</route-meta>
<template>
  <el-select v-model="selected" :clearable="clearable" @clear="selected = undefined" :placeholder="placeholder">
    <el-option v-for="item in options" :label="item.label" :value="item.value" :key="item.value"> </el-option>
  </el-select>
</template>

<script lang="ts">
  import { Prop, Emit, Watch, Component, Vue } from 'vue-property-decorator'

  @Component
  export default class extends Vue {
    @Prop({
      type: String,
      default: '资讯状态'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      type: String,
      default: ''
    })
    value: string

    selected = ''
    options = [
      { label: '全部', value: null },
      { label: '草稿', value: '1' },
      { label: '发布', value: '2' }
    ]

    @Watch('value')
    valueChange() {
      this.selected = this.value
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }
  }
</script>
