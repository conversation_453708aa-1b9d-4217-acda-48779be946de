import TrackingParam from '@api/service/management/intelligence-learning/model/TrackingParam'
import ArrangementResultItem from '@api/service/management/intelligence-learning/model/ArrangementResultItem'
import importopen from '@api/ms-gateway/ms-importopen-v1'
import { Page } from '@hbfe/common'
import TaskTrackingList from '@api/service/management/intelligence-learning/model/TaskTrackingList'

/**
 * 编排列表-专题管理员
 */
export default class ArrangementResultListInTrainingChannel extends TaskTrackingList<
  TrackingParam,
  ArrangementResultItem
> {
  constructor() {
    super(new TrackingParam())
  }
  /**
   * 编排结果列表查询
   */
  async queryList(page: Page) {
    const { status, data } = await importopen.findImportDataWithSelfByPage({
      page,
      request: TrackingParam.to(this.queryParam)
    })
    if (status.isSuccess()) {
      page.totalPageSize = data.totalPageSize
      page.totalSize = data.totalSize
      this.list = data.currentPageData.map(ArrangementResultItem.from)
    } else {
      this.list = []
      page.totalPageSize = 0
      page.totalSize = 0
    }
    return status
  }
}
