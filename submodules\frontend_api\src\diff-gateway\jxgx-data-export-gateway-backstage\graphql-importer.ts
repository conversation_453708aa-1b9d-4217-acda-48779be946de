import exportInvoiceDeliveryInServicer from './queries/exportInvoiceDeliveryInServicer.graphql'
import exportOfflineInvoiceInServicerForJxjy from './queries/exportOfflineInvoiceInServicerForJxjy.graphql'
import exportOnlineInvoiceInServicerForJxjy from './queries/exportOnlineInvoiceInServicerForJxjy.graphql'
import exportOrderExcelInDistributor from './queries/exportOrderExcelInDistributor.graphql'
import exportOrderExcelInServicer from './queries/exportOrderExcelInServicer.graphql'
import exportReconciliationExcelInDistributor from './queries/exportReconciliationExcelInDistributor.graphql'
import exportReconciliationExcelInServicer from './queries/exportReconciliationExcelInServicer.graphql'
import exportReturnOrderExcelInDistributor from './queries/exportReturnOrderExcelInDistributor.graphql'
import exportReturnOrderExcelInServicer from './queries/exportReturnOrderExcelInServicer.graphql'
import exportReturnReconciliationExcelInDistributor from './queries/exportReturnReconciliationExcelInDistributor.graphql'
import exportReturnReconciliationExcelInServicer from './queries/exportReturnReconciliationExcelInServicer.graphql'
import exportStudentExcelInServicer from './queries/exportStudentExcelInServicer.graphql'
import exportStudentExcelInSubProject from './queries/exportStudentExcelInSubProject.graphql'
import exportStudentSchemeLearningExcelInServicerForJxjy from './queries/exportStudentSchemeLearningExcelInServicerForJxjy.graphql'
import exportStudentSchemeLearningExcelInServicerManageRegion from './queries/exportStudentSchemeLearningExcelInServicerManageRegion.graphql'

export {
  exportInvoiceDeliveryInServicer,
  exportOfflineInvoiceInServicerForJxjy,
  exportOnlineInvoiceInServicerForJxjy,
  exportOrderExcelInDistributor,
  exportOrderExcelInServicer,
  exportReconciliationExcelInDistributor,
  exportReconciliationExcelInServicer,
  exportReturnOrderExcelInDistributor,
  exportReturnOrderExcelInServicer,
  exportReturnReconciliationExcelInDistributor,
  exportReturnReconciliationExcelInServicer,
  exportStudentExcelInServicer,
  exportStudentExcelInSubProject,
  exportStudentSchemeLearningExcelInServicerForJxjy,
  exportStudentSchemeLearningExcelInServicerManageRegion
}
