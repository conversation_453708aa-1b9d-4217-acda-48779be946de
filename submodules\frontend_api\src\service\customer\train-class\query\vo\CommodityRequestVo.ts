import { PortalCommoditySkuPropertyRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'

/**
 * 地区查询参数
 <AUTHOR>
 @date 2022/02/25
 */
export class RegionSkuPropertyRequest {
  /**
   * 地区: 省
   */
  province = ''
  /**
   * 地区: 市
   */
  city = ''
  /**
   * 地区: 区县
   */
  county = ''
}
/**
 * 地区查询请求参数
 <AUTHOR>
 @date 2022/02/25
 */
export class RegionSkuPropertySearchRequest {
  /**
   * 地区匹配方式
   <p> 1:ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回
   <p> 2:PART部分匹配：查询结果返回的地区与查询条件给出的地区
   @see RegionSearchType
   */
  regionSearchType = 1
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest> = []
}
/**
 * 商品上下架相关查询参数
 <AUTHOR>
 @date 2022/01/25
 */
export class OnShelveRequest {
  /**
   * 商品上下架状态
   <br> 0:已下架 1：已上架
   */
  onShelveStatus = 1
}

/**
 * 培训方案相关查询参数
 <AUTHOR>
 @date 2022/01/25
 */
export class SchemeRequest {
  /**
   * 培训方案类型
   <br> chooseCourseLearning:选课规则 autonomousCourseLearning:自主学习
   */
  schemeType = ''
  /**
   * 培训方案名称(模糊查询)
   */
  schemeName = ''
}
/**
 * 商品sku属性查询参数
 <AUTHOR>
 @date 2022/01/25
 */
export class SkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string> = []
  /**
   * 地区
   */
  regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
  /**
   * 行业
   */
  industry?: Array<string> = []
  /**
   * 科目类型
   */
  subjectType?: Array<string> = []
  /**
   * 培训类别
   */
  trainingCategory?: Array<string> = []
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string> = []
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>

  /**
   * 培训对象
   */
  trainingObject?: Array<string> = []
  /**
   * 岗位类别
   */
  positionCategory?: Array<string> = []
  /**
   * 技术等级
   */
  jobLevel?: Array<string> = []
  /**
   * 学段
   */
  learningPhase?: Array<string> = []
  /**
   * 学科
   */
  discipline?: Array<string> = []
  /**
   * 学段
   */
  certificatesType?: Array<string> = []
  /**
   * 学科
   */
  practitionerCategory?: Array<string> = []
  /**
   * 学时
   */
  period?: number = null
}
/**
 * 门户商品sku属性查询参数
 <AUTHOR>
 @date 2024/04/23
 */
export class CommoditySkuPropertyRequest extends PortalCommoditySkuPropertyRequest {
  certificatesType: string[] = []
  practitionerCategory: string[] = []
  constructor() {
    super()
    this.year = []
    this.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
    this.regionSkuPropertySearchForPortal = new RegionSkuPropertySearchRequest()
    this.industry = []
    this.subjectType = []
    this.trainingCategory = []
    this.trainingProfessional = []
    this.technicalGrade = []
    this.trainingObject = []
    this.positionCategory = []
    this.jobLevel = []
    this.learningPhase = []
    this.discipline = []
    this.certificatesType = []
    this.practitionerCategory = []
    this.period = null
  }
  /**
   * 专题ids
   */
  trainingChannelIds?: Array<string>
}
/**
 * 商品查询条件
 <AUTHOR>
 @date 2022/01/25
 */
export class CommoditySkuRequest {
  /**
   * 商品名称（模糊查询）
   */
  saleTitleMatchLike?: string = ''
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string> = []
  /**
   * 商品上下架信息
   */
  onShelveRequest?: OnShelveRequest = new OnShelveRequest()
  /**
   * 培训方案信息
   */
  schemeRequest?: SchemeRequest = new SchemeRequest()
  /**
   * 商品sku属性查询
   */
  skuPropertyRequest?: SkuPropertyRequest = new SkuPropertyRequest()

  /**
   * 专题id
   */
  trainingChannelIds?: Array<string> = []
}
export class PortalCommoditySkuRequest extends CommoditySkuRequest {
  /**
   * 门户商品 - 来源类型
1-网校商品 2-专题商品
   */
  portalCommoditySkuSourceType?: number
  /**
   * 商品门户sku属性查询
   */
  portalCommoditySkuPropertyRequest?: PortalCommoditySkuPropertyRequest = new PortalCommoditySkuPropertyRequest()
  /**
   * 学时
   */
  // period?: number = null
}
