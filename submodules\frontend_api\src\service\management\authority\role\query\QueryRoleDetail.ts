import RoleInfoResponseVo from '@api/service/management/authority/role/query/vo/RoleInfoResponseVo'
import roleGateWay from '@api/ms-gateway/ms-role-v1'
export class QueryRoleDetail {
  //角色id
  roleId = ''
  /**
   * 获取角色详情
   */

  roleDetail = new RoleInfoResponseVo()

  async queryRoleDetail() {
    try {
      const res = await roleGateWay.getRoleById(this.roleId)

      if (res.status.isSuccess()) {
        this.roleDetail = res.data as any
      }

      console.log('调用了queryRoleDetail方法，返回值=', res.status)
      return res.status
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/query/QueryRoleDetail.ts所处方法，queryRoleDetail',
        e
      )
    }
  }
}
