import { CourseCategoryResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

/**
 * 课程分类
 */
class CourseCategory {
  id: string
  // 分类名称
  name: string
  // 课程分类子类
  children: Array<CourseCategory> = new Array<CourseCategory>()

  static from(response: CourseCategoryResponse) {
    const detail = new CourseCategory()
    detail.id = response.id
    detail.name = response.name
    detail.children = new Array<CourseCategory>()
    return detail
  }
}

export default CourseCategory
