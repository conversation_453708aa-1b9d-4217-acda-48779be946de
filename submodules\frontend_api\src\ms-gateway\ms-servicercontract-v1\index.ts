import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-servicercontract-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-servicercontract-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 管理人
<AUTHOR>
 */
export class Administrator {
  /**
   * 姓名
   */
  name?: string
  /**
   * 管理员账号
   */
  authenticationIdentity?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 密码
   */
  password?: string
}

/**
 * @author: linxiquan
@Date: 2024/5/17 17:24
@Description: 增值服务
 */
export class AddValueService {
  /**
   * 学习规则
   */
  learnRule?: boolean
  /**
   * 智能学习
   */
  intelligentlearning?: boolean
  /**
   * 分销服务
   */
  distributionService?: boolean
  /**
   * 分销服务类型
// * @see com.fjhb.domain.basicdata.api.servicercontract.consts.DistributionServiceType
基础版 0 专业版 1
   */
  distributionServiceType?: number
}

/**
 * 客户端
<AUTHOR>
 */
export class Client {
  /**
   * 客户端类型
@see ClientTypes
   */
  clientType: number
  /**
   * 门户类型
@see PortalTypes
   */
  portalType: number
  /**
   * 域名类型
@see DomainNameTypes
   */
  domainNameType: number
  /**
   * 域名
   */
  domainName?: string
  /**
   * 前端模板id
   */
  portalTemplateId?: string
  /**
   * cnzz信息
   */
  cnzz?: string
  /**
   * 目录名
   */
  dirName?: string
}

/**
 * 行业培训属性
<AUTHOR>
 */
export class Industry {
  /**
   * 行业id
   */
  id?: string
  /**
   * 行业属性编号
   */
  properties?: string
  /**
   * 网校业务行业属性id
   */
  onlineSchoolProperty?: string
}

/**
 * 网校实体类
<AUTHOR>
 */
export class OnlineSchool {
  /**
   * id
   */
  id?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 培训属性
   */
  trainingProperties?: TrainingProperties
  /**
   * 人员行业培训属性
   */
  personIndustries?: Array<PersonIndustry>
  /**
   * 客户端
   */
  clients?: Array<Client>
  /**
   * 是否提供短信服务
   */
  offerSmsService: boolean
  /**
   * 短信配置
   */
  smsConfig?: SmsConfig
  /**
   * 强制完善信息：
true：当学员信息不全时，强制触发完善信息页面
false：当学员信息不全时，强制跳过完善信息页面
   */
  enabledForceCompleteInfo?: boolean
}

/**
 * 人员行业属性
<AUTHOR>
 */
export class PersonIndustry {
  /**
   * 行业id
   */
  id?: string
  /**
   * 行业属性编号
   */
  properties?: string
  /**
   * 网校业务行业属性id
   */
  onlineSchoolProperty?: string
}

/**
 * 短信配置
<AUTHOR>
 */
export class SmsConfig {
  /**
   * 短信账号
   */
  smsAccount?: string
  /**
   * 短信密码
   */
  smsPassword?: string
  /**
   * 提供商id
   */
  providerId?: string
  /**
   * 短信网关授权配置名称
   */
  ispName?: string
  /**
   * 短信网关appId(非必填)
   */
  ispAppId?: string
  /**
   * 短信授权仅账号扩展信息，存放第三方额外信息，例如证书(非必填)
   */
  extension?: string
  /**
   * 短信模板列表
   */
  messageTemplateList?: Array<MessageTemplate>
}

/**
 * 培训属性
<AUTHOR>
 */
export class TrainingProperties {
  /**
   * 年份
   */
  years?: Array<string>
  /**
   * 地区
   */
  areas?: Array<string>
  /**
   * 补贴培训：培训类别
   */
  trainingCategoryIds?: Array<string>
  /**
   * 行业培训属性
   */
  industries?: Array<Industry>
}

/**
 * 创建合同供应商合约请求
<AUTHOR>
 */
export class CreateContractProviderContractRequest {
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  unitId?: string
  servicerId?: string
  userId?: string
  /**
   * 业主单位全称
   */
  unitName?: string
  /**
   * 业主单位简称
   */
  unitShotName?: string
  /**
   * 统一社会信用代码
   */
  code?: string
  /**
   * 所属地区编号
   */
  region?: string
  /**
   * 合同供应商id
   */
  contractProviderId?: string
  /**
   * 是否已线下签署合约
   */
  isOfflineContractSigned: boolean
  /**
   * 线下签署合约日期
   */
  offlineContractSignedDate?: string
  /**
   * 到期时间
   */
  expireDate?: string
  /**
   * 管理人
   */
  administrator?: Administrator
}

/**
 * <AUTHOR> [2023/6/6 17:03]
 */
export class CreateOSContractByStatusRequest {
  /**
   * 服务商合约id
   */
  id?: string
  /**
   * 0-保存草稿
1-正式创建
   */
  status?: number
  /**
   * 业主单位全称
   */
  unitName?: string
  /**
   * 业主单位简称
   */
  unitShotName?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 服务域名
   */
  serviceAreas?: Array<string>
  /**
   * 网校模式   1-正式实施  2-demo
   */
  onlineSchoolModes: number
  /**
   * 是否已线下签署合约
   */
  isOfflineContractSigned?: boolean
  /**
   * 线下签署合约日期
   */
  offlineContractSignedDate?: string
  /**
   * 办理人（业主负责人）
   */
  transactor?: string
  /**
   * 培训周期模式  1-长期  2-短期
   */
  trainingPeriodModes?: number
  /**
   * 到期时间
   */
  expireDate?: string
  /**
   * 网校实体
   */
  onlineSchool?: NewCreateOnlineSchoolRequest
  /**
   * 管理员
   */
  administrator?: Administrator
  /**
   * 网校背景描述
   */
  description?: string
  /**
   * 归属市场经办
   */
  marketTransactor?: string
  /**
   * 增值服务
   */
  addValueService?: AddValueService
  /**
   * 培训机构类型
@see PartnerType  ：1-个人  2-企业
   */
  partnerType?: number
  /**
   * 统一社会信用代码
   */
  code?: string
  /**
   * 培训机构logo
   */
  logo?: string
}

/**
 * 网校合约通用请求对象
<AUTHOR>
 */
export class CreateOSContractRequest {
  /**
   * 业主单位全称
   */
  unitName?: string
  /**
   * 业主单位简称
   */
  unitShotName?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 服务域名
   */
  serviceAreas?: Array<string>
  /**
   * 网校模式   1-正式实施  2-demo
   */
  onlineSchoolModes: number
  /**
   * 是否已线下签署合约
   */
  isOfflineContractSigned?: boolean
  /**
   * 线下签署合约日期
   */
  offlineContractSignedDate?: string
  /**
   * 办理人（业主负责人）
   */
  transactor?: string
  /**
   * 培训周期模式  1-长期  2-短期
   */
  trainingPeriodModes?: number
  /**
   * 到期时间
   */
  expireDate?: string
  /**
   * 网校实体
   */
  onlineSchool?: CreateOnlineSchoolRequest
  /**
   * 管理员
   */
  administrator?: Administrator
  /**
   * 网校背景描述
   */
  description?: string
}

/**
 * 创建请求网校模型
<AUTHOR>
 */
export class CreateOnlineSchoolRequest {
  /**
   * 名称
   */
  name?: string
  /**
   * 培训属性
   */
  trainingProperties?: TrainingProperties
  /**
   * 客户端
   */
  clients?: Array<Client>
  /**
   * 是否提供短信服务
   */
  offerSmsService: boolean
  /**
   * 短信配置
   */
  smsConfig?: SmsConfig
}

/**
 * 网校合约通用请求对象(新)
<AUTHOR> [2023/5/24 9:28]
 */
export class NewCreateOSContractRequest {
  /**
   * 业主单位全称
   */
  unitName?: string
  /**
   * 业主单位简称
   */
  unitShotName?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 服务域名
   */
  serviceAreas?: Array<string>
  /**
   * 网校模式   1-正式实施  2-demo
   */
  onlineSchoolModes: number
  /**
   * 是否已线下签署合约
   */
  isOfflineContractSigned?: boolean
  /**
   * 线下签署合约日期
   */
  offlineContractSignedDate?: string
  /**
   * 办理人（业主负责人）
   */
  transactor?: string
  /**
   * 培训周期模式  1-长期  2-短期
   */
  trainingPeriodModes?: number
  /**
   * 到期时间
   */
  expireDate?: string
  /**
   * 网校实体
   */
  onlineSchool?: NewCreateOnlineSchoolRequest
  /**
   * 管理员
   */
  administrator?: Administrator
  /**
   * 网校背景描述
   */
  description?: string
  /**
   * 归属市场经办
   */
  marketTransactor?: string
  /**
   * 增值服务
   */
  addValueService?: AddValueService
  /**
   * 培训机构类型
@see PartnerType  ：1-个人  2-企业
   */
  partnerType?: number
  /**
   * 统一社会信用代码
   */
  code?: string
  /**
   * 培训机构logo
   */
  logo?: string
}

/**
 * <AUTHOR> [2023/5/24 9:47]
 */
export class NewCreateOnlineSchoolRequest {
  /**
   * 网校id
   */
  id?: string
  /**
   * 名称
   */
  name?: string
  /**
   * 培训属性
   */
  trainingProperties?: TrainingProperties
  /**
   * 人员行业培训属性
   */
  personIndustries?: Array<PersonIndustry>
  /**
   * 客户端
   */
  clients?: Array<Client>
  /**
   * 是否提供短信服务
   */
  offerSmsService: boolean
  /**
   * 短信配置
   */
  smsConfig?: SmsConfig
  /**
   * cnzz信息
   */
  cnzz?: string
  /**
   * 目录名
   */
  dirName?: string
  /**
   * 强制完善信息：默认为true
true：当学员信息不全时，强制触发完善信息页面
false：当学员信息不全时，强制跳过完善信息页面
   */
  enabledForceCompleteInfo?: boolean
}

/**
 * 网校合约续期请求
<AUTHOR>
 */
export class RenewedOSContractRequest {
  /**
   * id
   */
  id: string
  /**
   * 续期后的到期时间
   */
  expiredDate: string
}

/**
 * <AUTHOR> [2023/6/28 14:51]
 */
export class UpdateOSBasicInfoRequest {
  /**
   * id 【必填】
   */
  id?: string
  /**
   * 业主单位全称
   */
  unitName?: string
  /**
   * 业主单位简称
   */
  unitShotName?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 服务地区
   */
  serviceAreas?: Array<string>
  /**
   * 网校模式   1-正式实施  2-demo
   */
  onlineSchoolModes: number
  /**
   * 是否已线下签署合约
   */
  isOfflineContractSigned?: boolean
  /**
   * 线下签署合约日期
   */
  offlineContractSignedDate?: string
  /**
   * 办理人（业主负责人）
   */
  transactor?: string
  /**
   * 培训周期模式  1-长期  2-短期 （下个版本去除，原型上没有这个字段）
   */
  trainingPeriodModes: number
  /**
   * 培训属性
   */
  trainingProperties?: TrainingProperties
  /**
   * 人员行业培训属性
   */
  personIndustries?: Array<PersonIndustry>
  /**
   * 到期时间
   */
  expireDate?: string
  /**
   * 归属市场经办
   */
  marketTransactor?: string
  /**
   * 网校背景描述
   */
  description?: string
  /**
   * 网校名称
   */
  name?: string
  /**
   * 增值服务
   */
  addValueService?: AddValueService
  /**
   * 培训机构类型
@see PartnerType  ：1-个人  2-企业
   */
  partnerType?: number
  /**
   * 统一社会信用代码
   */
  code?: string
  /**
   * 培训机构logo
   */
  logo?: string
}

/**
 * 更新网校开通合约请求
<AUTHOR>
 */
export class UpdateOSContractRequest {
  /**
   * id 【必填】
   */
  id?: string
  /**
   * 业主单位全称
   */
  unitName?: string
  /**
   * 业主单位简称
   */
  unitShotName?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 服务地区
   */
  serviceAreas?: Array<string>
  /**
   * 网校模式   1-正式实施  2-demo
   */
  onlineSchoolModes: number
  /**
   * 是否已线下签署合约
   */
  isOfflineContractSigned?: boolean
  /**
   * 线下签署合约日期
   */
  offlineContractSignedDate?: string
  /**
   * 办理人（业主负责人）
   */
  transactor?: string
  /**
   * 培训周期模式  1-长期  2-短期
   */
  trainingPeriodModes: number
  /**
   * 到期时间
   */
  expireDate?: string
  /**
   * 网校是否已签约【必填】
   */
  signed: boolean
  /**
   * 网校实体
   */
  onlineSchool?: OnlineSchool
  /**
   * 管理员
   */
  administrator?: Administrator
  /**
   * 网校背景描述
   */
  description?: string
}

/**
 * <AUTHOR> [2023/6/28 17:39]
 */
export class UpdateOnlineSchoolRequest {
  /**
   * 服务商合约id
   */
  id?: string
  /**
   * 客户端
   */
  clients?: Array<Client>
  /**
   * 是否提供短信服务
   */
  offerSmsService: boolean
  /**
   * 短信配置
   */
  smsConfig?: SmsConfig
  /**
   * 培训周期模式  1-长期  2-短期
   */
  trainingPeriodModes?: number
  /**
   * 到期时间
   */
  expireDate?: string
  /**
   * 强制完善信息：默认为true
true：当学员信息不全时，强制触发完善信息页面
false：当学员信息不全时，强制跳过完善信息页面
   */
  enabledForceCompleteInfo?: boolean
}

/**
 * <AUTHOR> [2023/7/6 14:06]
 */
export class UpdateOnlineSchoolTemplateRequest {
  /**
   * 服务商合约id
   */
  id: string
  /**
   * 前端模板id（Web）
   */
  portalTemplateId: string
  /**
   * 前端模板id（H5）
   */
  portalH5TemplateId: string
}

/**
 * 短信模板
<AUTHOR>
 */
export class MessageTemplate {
  /**
   * 模板名称
   */
  templateName?: string
  /**
   * 模板内容
   */
  content?: string
  /**
   * 模板分类
   */
  category?: string
}

/**
 * <AUTHOR> [2023/7/18 9:07]
 */
export class OnlineSchoolContractResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * Ui主题颜色
<AUTHOR> [2023/6/6 16:13]
 */
export class UiThemeColorResponse {
  /**
   * 颜色id
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 帐户ID
   */
  servicerId: string
  /**
   * 颜色值
   */
  colorRef: string
  /**
   * 创建时间
   */
  createdTime: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建合同供应商合约并开通
   * @param request 创建合同供应商合约请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createAndOpenContractProvider(
    request: CreateContractProviderContractRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createAndOpenContractProvider,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建并开通网校开通合约
   * @param request 网校合约通用请求对象
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createAndOpenOSContract(
    request: CreateOSContractRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createAndOpenOSContract,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建网校开通合约
   * @param request 网校合约通用请求对象
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createOSContractDraft(
    request: CreateOSContractRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createOSContractDraft,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建网校草稿，根据状态决定是否开通
   * 校验同一个行业集合是否多个相同行业id，有异常抛出
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createOnlineSchoolContract(
    request: CreateOSContractByStatusRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createOnlineSchoolContract,
    operation?: string
  ): Promise<Response<OnlineSchoolContractResponse>> {
    return commonRequestApi<OnlineSchoolContractResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 完成交付
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async deliveredOSContract(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deliveredOSContract,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 停用网校开通合约
   * @param id 网校开通合约id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async disbaleOSContract(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.disbaleOSContract,
    operation?: string
  ): Promise<Response<OnlineSchoolContractResponse>> {
    return commonRequestApi<OnlineSchoolContractResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 启用网校开通合约
   * @param id 网校开通合约id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async enableOSContract(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.enableOSContract,
    operation?: string
  ): Promise<Response<OnlineSchoolContractResponse>> {
    return commonRequestApi<OnlineSchoolContractResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取ui主题颜色列表
   * @return 主题颜色列表
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findUiThemeColorList(
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.findUiThemeColorList,
    operation?: string
  ): Promise<Response<Array<UiThemeColorResponse>>> {
    return commonRequestApi<Array<UiThemeColorResponse>>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 当前服务商合约是否到期 -（新使用到期时间比较目前时间，进行比较。作为延期补丁使用）
   * 【注意】调用前需要调用applyForServiceByDomainName获取服务商上下文信息
   * @return 服务商合约是否已到期
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async isOnlineSchoolContractExpired(
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.isOnlineSchoolContractExpired,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建并开通网校开通合约并开通网校(新，传入网校id、)
   * @param request 新网校合约通用请求对象
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async newCreateAndOpenOSContract(
    request: NewCreateOSContractRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.newCreateAndOpenOSContract,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 开通网校开通合约
   * @param id 网校开通合约id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async openOSContract(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.openOSContract,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 删除网校开通合约
   * @param id 网校开通合约id
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async removeOSContract(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeOSContract,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 续期网校开通合约
   * @param request 网校合约续期请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async renewOSContract(
    request: RenewedOSContractRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.renewOSContract,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 更新服务商合约基础信息 信息
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async updateOSBasicInfo(
    request: UpdateOSBasicInfoRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOSBasicInfo,
    operation?: string
  ): Promise<Response<OnlineSchoolContractResponse>> {
    return commonRequestApi<OnlineSchoolContractResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 更新网校开通合约
   * @param request 更新网校开通合约请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async updateOSContract(
    request: UpdateOSContractRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOSContract,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 更新模板id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async updateOSTemplate(
    request: UpdateOnlineSchoolTemplateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOSTemplate,
    operation?: string
  ): Promise<Response<OnlineSchoolContractResponse>> {
    return commonRequestApi<OnlineSchoolContractResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 更新网校信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async updateOnlineSchool(
    request: UpdateOnlineSchoolRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateOnlineSchool,
    operation?: string
  ): Promise<Response<OnlineSchoolContractResponse>> {
    return commonRequestApi<OnlineSchoolContractResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 创建网校时，根据网校名称和网校类型查询网校名称是否重复
   * @param
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validOnlineSchoolNameHasResponse(
    params: { name?: string; type: number },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validOnlineSchoolNameHasResponse,
    operation?: string
  ): Promise<Response<OnlineSchoolContractResponse>> {
    return commonRequestApi<OnlineSchoolContractResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改网校时，根据传入的网校名称和网校类型查询网校名称是否重复,不包含当前网校
   * @param
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async validUpdateOnlineSchoolNameHasResponse(
    params: { name?: string; type: number; servicerId?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.validUpdateOnlineSchoolNameHasResponse,
    operation?: string
  ): Promise<Response<OnlineSchoolContractResponse>> {
    return commonRequestApi<OnlineSchoolContractResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
