import { StudentInfoResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

class StudentListDetail {
  id: string
  name: string
  identity: string
  sex: number
  phoneNumber: string
  region: string
  unitName: string
  registryTime: string

  static from(response: StudentInfoResponse) {
    const detail = new StudentListDetail()
    detail.id = response.userInfo.userId
    detail.name = response.userInfo.userName
    detail.sex = response.userInfo.gender
    detail.unitName = response.userInfo.companyName
    detail.registryTime = response.accountInfo.createdTime
    detail.phoneNumber = response.userInfo.phone
    detail.region = [
      response.userInfo.region.provinceName,
      response.userInfo.region.countyName,
      response.userInfo.region.cityName
    ].join('-')
    return detail
  }
}

export default StudentListDetail
