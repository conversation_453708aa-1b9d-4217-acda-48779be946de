<route-meta>
{
"isMenu": true,
"title": "公需课课程维护",
"sort": 5,
}
</route-meta>
<template>
  <el-main>
    <template v-if="$hasPermission('query')" desc="查询" actions="searchBase,@NationalRegion">
      <div class="f-p15 m-table-auto">
        <el-card shadow="never" class="m-card f-mb15">
          <!--条件查询-->
          <hb-search-wrapper expand @reset="resetQueryParam" class="m-query is-border-bottom">
            <el-form-item label="年度">
              <biz-year-select v-model="courseMaintenanceQueryVo.year" placeholder="请选择培训年度"></biz-year-select>
            </el-form-item>
            <el-form-item label="地区">
              <national-region
                v-model="courseMaintenanceQueryVo.region"
                :check-strictly="true"
                placeholder="请选择地区"
              ></national-region>
            </el-form-item>
            <el-form-item label="课程名称">
              <el-input clearable placeholder="请输入课程名称" v-model="courseMaintenanceQueryVo.name" />
            </el-form-item>
            <template slot="actions">
              <el-button
                type="primary"
                v-if="$hasPermission('import')"
                desc="批量导入公需课程"
                actions="importPublicCourses,handleImport"
                @click="importPublicCourses"
                >批量导入公需课程</el-button
              >
              <el-button type="primary" @click="searchBase">查询</el-button>
            </template>
          </hb-search-wrapper>
          <!--表格-->
          <el-table stripe :data="tableData" v-loading="loading" class="m-table">
            <el-table-column label="年度" min-width="100" align="center">
              <template slot-scope="scope">{{ scope.row.year }}</template>
            </el-table-column>
            <el-table-column label="地区" min-width="160" align="center">
              <template slot-scope="scope">{{ scope.row.region }}</template>
            </el-table-column>
            <el-table-column label="课程名称" min-width="300" align="center">
              <template slot-scope="scope">{{ scope.row.courseName }}</template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template slot-scope="scope">
                <template v-if="$hasPermission('remove')" desc="删除" actions="handleDelete">
                  <el-popconfirm
                    confirm-button-text="确定"
                    cancel-button-text="取消"
                    icon="el-icon-info"
                    icon-color="red"
                    title="是否删除此记录？"
                    @confirm="handleDelete(scope.row)"
                  >
                    <el-button type="text" slot="reference">删除</el-button>
                  </el-popconfirm>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"></hb-pagination>

          <!-- 导入公需课课程弹窗 -->
          <el-drawer
            title="导入公需课课程"
            :visible.sync="importDialog"
            direction="rtl"
            size="600px"
            custom-class="m-drawer"
            :close-on-press-escape="false"
            :before-close="handleClose"
          >
            <div class="drawer-bd">
              <el-alert type="warning" :closable="false" class="m-alert">
                <p>温馨提示：</p>
                <p>
                  1. 在批量导入公需课课程前请下载<span class="f-cb f-csp" @click="downloadModule"
                    >[导入公需课课程模板]</span
                  >，并严格根据表格内容填写保存后再导入系统；
                </p>
                <p>2. 导入表格一次最多支持1000条记录，若超过1000条则不能正常导入；</p>
                <p>3. 导入后可以通过“导入任务管理” 查看并确认导入结果。</p>
              </el-alert>
              <el-row type="flex" justify="center">
                <el-col :span="18">
                  <el-form ref="form" label-width="auto" class="m-form f-mt50">
                    <el-form-item label="导入文件：">
                      <hb-upload-file
                        v-model="hbFileUploadResponse"
                        :file-type="1"
                        class="uploadFile"
                        ref="uploadFileRef"
                      >
                        <div class="left">
                          <el-button type="primary" plain>点击上传</el-button>
                          <div class="el-upload__tip">
                            <i class="el-icon-warning"></i>
                            <span class="txt">导入的文件格式必须为xls,xlsx文件</span>
                          </div>
                        </div>
                      </hb-upload-file>
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
            </div>
            <div class="drawer-ft m-btn-bar">
              <el-button @click="handleClose">取消</el-button>
              <el-button type="primary" :loading="importLoading" @click="handleImport">导入</el-button>
            </div>
          </el-drawer>

          <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
            <div class="dialog-alert is-big">
              <i class="icon el-icon-success success"></i>
              <div class="txt">
                <p class="f-fb">导入成功，是否前往下载数据？</p>
                <p class="f-f13 f-mt5">下载入口：导入任务查看-批量导入公需课课程</p>
              </div>
            </div>
            <div slot="footer">
              <el-button @click="exportSuccessVisible = false">暂 不</el-button>
              <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
            </div>
          </el-dialog>
        </el-card>
      </div>
    </template>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue, Watch } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import { bind, debounce } from 'lodash-decorators'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import hbUploadFile from '@hbfe/jxjy-admin-components/src/hb-upload-file.vue'
  import CourseMaintenanceQueryVo from '@api/service/diff/management/gszj/train-class/model/CourseMaintenanceQueryVo'
  import QueryCourseMaintenance from '@api/service/diff/management/gszj/train-class/QueryCourseMaintenance'
  import CourseMaintenanceInfoVo from '@api/service/diff/management/gszj/train-class/model/CourseMaintenanceInfoVo'
  import NationalRegion from '@hbfe/jxjy-admin-scheme/src/diff/components/national-region.vue'

  @Component({
    components: {
      hbUploadFile,
      NationalRegion
    }
  })
  export default class extends Vue {
    @Ref('uploadFileRef') uploadFileRef: hbUploadFile
    constructor() {
      super()
      this.page = new UiPage(this.pageScheme, this.pageScheme)
    }

    courseMaintenanceQueryVo = new CourseMaintenanceQueryVo()

    // 分页参数
    page: UiPage

    queryCourseMaintenance = new QueryCourseMaintenance()

    tableData: Array<CourseMaintenanceInfoVo> = []

    loading = false

    importDialog = false

    /**
     * 文件上传之后的回调参数
     */
    hbFileUploadResponse = new HBFileUploadResponse()

    exportSuccessVisible = false

    importLoading = false

    async created() {
      await this.searchBase()
    }

    /**
     * 批量导入公需课程
     */
    importPublicCourses() {
      console.log('批量导入公需课程')
      this.importDialog = true
    }

    handleClose() {
      if (this.importDialog) {
        this.importDialog = false
        this.hbFileUploadResponse = new HBFileUploadResponse()
        this.uploadFileRef.fileList = []
      }
    }

    /**
     * 导入公需课程
     */
    @bind
    @debounce(200)
    async handleImport() {
      console.log('导入', this.hbFileUploadResponse)
      const filePath = this.hbFileUploadResponse.url
      if (!this.hbFileUploadResponse.url) {
        this.$message.warning('请选择上传的文件')
        return
      }
      this.importLoading = true
      try {
        const res = await this.queryCourseMaintenance.importPublicCourses(filePath, this.hbFileUploadResponse.fileName)
        if (res.isSuccess()) {
          this.$message.success('上传成功')
          this.handleClose()
          this.exportSuccessVisible = true
        } else {
          if (res.message) {
            this.$message.error(res.message as string)
          } else {
            this.$message.error('上传失败')
          }
        }
      } catch (e) {
        console.log('e', e)
      } finally {
        this.importLoading = false
      }
    }

    goDownloadPage() {
      this.exportSuccessVisible = false
      this.handleClose()
      this.$router.push({
        path: '/training/task/importtask',
        query: { type: '批量导入公需课课程' }
      })
    }

    // 下载公需课课程模板
    async downloadModule() {
      const link = document.createElement('a')
      const resolver = this.$router.resolve({
        name: '/mfs/ms-file/public/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/导入公需课课程模板.xls'
      })
      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = resolver.location.name
      const urlArr = link.href.split('.'),
        typeName = urlArr.pop()
      link.setAttribute('download', '导入公需课课程模板')
      document.body.appendChild(link)
      link.click()
      link.remove()
    }
    /**
     * 加载第一页
     */
    async searchBase() {
      this.page.pageNo = 1
      await this.pageScheme()
    }

    /**
     * 分页查询
     */
    async pageScheme() {
      try {
        this.loading = true
        const res = await this.queryCourseMaintenance.queryMaintenanceList(this.page, this.courseMaintenanceQueryVo)
        console.log(res, '分页查询')
        this.tableData = res || []
      } catch (e) {
        console.log('获取列表失败！', e)
      } finally {
        this.loading = false
      }
    }

    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.courseMaintenanceQueryVo = new CourseMaintenanceQueryVo()
      await this.searchBase()
    }

    /**
     * 删除
     */
    @bind
    @debounce(200)
    async handleDelete(row: CourseMaintenanceInfoVo) {
      console.log('删除', row)
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        const res = await row.deleteCourse()
        if (res.isSuccess()) {
          this.$message.success('删除成功')
          setTimeout(() => {
            this.searchBase()
          }, 200)
        } else {
          if (res.message) {
            this.$message.error(res.message as string)
          } else {
            this.$message.error('删除失败')
          }
        }
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        loading?.close()
      }
    }
  }
</script>

<style lang="scss" scoped>
  .uploadFile {
    width: 400px !important;
  }
  .left {
    text-align: left;
  }
</style>
