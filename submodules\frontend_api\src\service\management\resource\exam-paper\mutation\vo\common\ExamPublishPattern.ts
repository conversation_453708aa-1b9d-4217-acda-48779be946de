import { PublishPattern } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { PublishPatternRequest } from '@api/ms-gateway/ms-examextraction-v1'
import { PublishPatternTypes } from '../../../enum/ExamPaperPublishPatternTypes'
/**
 * @description: 试卷出题方式
 */
class ExamPublishPattern extends PublishPatternRequest {
  /**
   * 出卷模式类型
   */
  type = PublishPatternTypes.AutomaticPublishPattern

  // 试卷总分
  totalScore = 100

  // 合格分数
  qualifiedScore = 60

  /**
   * @description: 模型转换为Vo
   * @params 试卷模式， 题库集合数组
   */
  from(pattern: PublishPattern, list?: Array<any>) {
    // 子类实现该方法
    return
  }

  // 模型转换dto
  toDto(pattern: PublishPatternRequest): PublishPatternRequest {
    // 子类实现该方法
    return
  }
}

export default ExamPublishPattern
