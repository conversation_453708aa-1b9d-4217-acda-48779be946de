import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 审核方式枚举
 * auto 提交自动通过
 * artificial 人工审核
 */
export enum CheckTypeEnum {
  auto = 1,
  artificial = 2
}

/**
 * @description 审核方式
 */
class CheckType extends AbstractEnum<CheckTypeEnum> {
  static enum = CheckTypeEnum

  constructor(status?: CheckTypeEnum) {
    super()
    this.current = status
    this.map.set(CheckTypeEnum.auto, '提交自动通过')
    this.map.set(CheckTypeEnum.artificial, '人工审核')
  }
}

export default CheckType
