import { ResponseStatus } from '@hbfe/common'
import CourseResource from '@api/ms-gateway/ms-course-resource-v1'

class MutationBizCourseware {
  private coursewareId: string

  constructor(coursewareId: string) {
    this.coursewareId = coursewareId
  }

  /**
   * 修改状态是否可用
   * @param enable 可用/停用
   * @private
   */
  private async changeStatus(enable: boolean): Promise<ResponseStatus> {
    const messageType = enable ? '启动' : '停用'
    return new ResponseStatus(200, `${messageType}`)
  }

  /**
   * 启用
   */
  async doEnable(): Promise<ResponseStatus> {
    const response = await CourseResource.enableCourseware(this.coursewareId)
    if (!response.status.isSuccess() && response.status.code !== 200) {
      console.error('停用失败')
      return Promise.reject(response)
    }
    return await this.changeStatus(true)
  }

  /**
   * 停用
   */
  async doDisable(): Promise<ResponseStatus> {
    const response = await CourseResource.disableCourseware(this.coursewareId)
    if (!response.status.isSuccess() && response.status.code !== 200) {
      console.error('停用失败')
      return Promise.reject(response)
    }
    return await this.changeStatus(false)
  }

  /**
   * 删除课件
   */
  async doRemove(): Promise<ResponseStatus> {
    const response = await CourseResource.removeCourseware(this.coursewareId)
    if (!response.status.isSuccess() && response.status.code !== 200) {
      console.error('删除课件失败')
      return Promise.reject(response)
    }
    return response.status
  }
}

export default MutationBizCourseware
