<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">课程排序配置</el-breadcrumb-item>
      <el-breadcrumb-item>查看课程排序配置</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="f-flex">
          <span class="tit-txt f-flex-sub">基础信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="auto" class="m-text-form is-column">
                <el-form-item label="规则名称："
                  >按指定课件供应商的课程数量排（前20门课程）<el-tooltip
                    effect="dark"
                    placement="right"
                    popper-class="m-tooltip"
                  >
                    <i class="hb-iconfont icon-s-warm f-co"></i>
                    <div slot="content">
                      规则详情：<br />
                      1.方案内末级节点的分类下课程，20门课程展示指定课程供应商的课程和数量。抽取的课程乱序排列。凑不满的情况，以实际抽取的课程展示。<br />
                      2.包内剩余的课程进行乱序排列。<br />
                      3.自动排序的周期，每天的00:00。<br />
                      4.本规则只作用于未结束培训的方案，保留课程包调整排序同步方案的功能。
                    </div>
                  </el-tooltip></el-form-item
                >
                <el-form-item label="是否乱序：">是</el-form-item>
                <el-form-item label="排序更新周期：">每天00:00</el-form-item>
                <el-form-item label="使用范围：">全平台</el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="f-flex">
          <span class="tit-txt f-flex-sub">已选待确认课件供应商</span>
        </div>
        <div class="f-p15">
          <!--表格-->
          <el-table stripe :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="课件供应商名称" min-width="240">
              <template>课件供应商名称课件供应商名称</template>
            </el-table-column>
            <el-table-column label="内容提供方" min-width="180">
              <template>zhangyiyi</template>
            </el-table-column>
            <el-table-column label="创建时间" min-width="180">
              <template>2020-11-11 12:20:20</template>
            </el-table-column>
            <el-table-column label="课程显示数量配置" min-width="120">
              <template>20</template>
            </el-table-column>
            <el-table-column label="操作" width="200" align="center" fixed="right">
              <template>
                <el-button type="text" size="mini">查看课分布情况</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
        </div>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button type="primary">返回</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
