"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""八冶专技批量导入公需课下载全部数据
		@param mainTaskId
		@return
	"""
	batchExportAllChooseDataInServicer(mainTaskId:String):String
	"""八冶专技批量导入公需课查看失败数据
		@param mainTaskId
		@return
	"""
	batchExportFailChooseDataInServicer(mainTaskId:String):String
	"""八冶专技删除当前服务商下的公需课
		@param id
	"""
	deleteCourseSubjectByIdInServicer(id:String):Void
	"""导出学员学习明细报盘数据（甘肃欣宇云锦+八冶+天亿博） 按人课维度
		@param request
		@return
	"""
	exportStudentCourseLearningQuotationInServicer(request:StudentSchemeLearningRequest):Boolean! @optionalLogin
	"""八冶专技批量导入公需课任务分页查询查询
		@param request
		@param page
		@return
	"""
	findBatchImportByPageInServicer(request:BatchImportQueryRequest,page:Page):FindBatchImportByPageResponsePage @page(for:"FindBatchImportByPageResponse")
	"""八冶专技批量导入公需课课程
		@param request
		@return Boolean
	"""
	importCourseSubjectByExcelInServicer(request:ImportCourseSubjectRequest):CourseSubjectQuestionResponse
	"""八冶专技分页查询当前服务商下的公需课
		@param page
		@param request
		@return
	"""
	pageCourseSubjectInServicer(page:Page,request:CreCourseSubjectRequest):CreCourseSubjectResponsePage @page(for:"CreCourseSubjectResponse")
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input StudentSchemeLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningRequest") {
	student:UserRequest
	learningRegister:LearningRegisterRequest
	scheme:SchemeRequest
	studentLearning:StudentLearningRequest
	dataAnalysis:DataAnalysisRequest
	connectManageSystem:ConnectManageSystemRequest
	extendedInfo:ExtendedInfoRequest
	openPrintTemplate:Boolean
	saleChannels:[Int]
	trainingChannelName:String
	trainingChannelId:String
	notDistributionPortal:Boolean
	trainingType:String
}
input ConnectManageSystemRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ConnectManageSystemRequest") {
	syncStatus:Int
}
input DataAnalysisRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.DataAnalysisRequest") {
	trainingResultPeriod:DoubleScopeRequest
	requirePeriod:DoubleScopeRequest
	acquiredPeriod:DoubleScopeRequest
}
input ExtendedInfoRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ExtendedInfoRequest") {
	whetherToPrint:Boolean
	applyCompanyCode:String
	policyTrainingSchemeId:String
	policyTrainingSchemeName:String
}
input LearningRegisterRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.LearningRegisterRequest") {
	registerType:Int
	sourceType:String
	sourceId:String
	status:[Int]
	registerTime:DateScopeRequest
	saleChannels:[Int]
	orderNoList:[String]
	subOrderNoList:[String]
	batchOrderNoList:[String]
	distributorId:String
	portalId:String
}
input RegionRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.RegionRequest") {
	province:String
	city:String
	county:String
}
input StudentLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.learning.StudentLearningRequest") {
	trainingResultList:[Int]
	trainingResultTime:DateScopeRequest
	notLearningTypeList:[Int]
	courseScheduleStatus:Int
	examAssessResultList:[Int]
}
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertySearchRequest") {
	region:[RegionSkuPropertyRequest]
	regionSearchType:Int
}
input SchemeRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeRequest") {
	schemeId:String
	schemeIdList:[String]
	schemeType:String
	schemeName:String
	skuProperty:SchemeSkuPropertyRequest
}
input SchemeSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeSkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	positionCategory:[String]
	trainingObject:[String]
	jobLevel:[String]
	jobCategory:[String]
	subject:[String]
	grade:[String]
	learningPhase:[String]
	discipline:[String]
	qualificationCategory:[String]
	trainingWay:[String]
	trainingInstitution:[String]
	mainAdditionalItem:[String]
}
input UserPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserPropertyRequest") {
	regionList:[RegionRequest]
	companyName:String
	payOrderRegionList:[RegionRequest]
}
input UserRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserRequest") {
	userIdList:[String]
	accountIdList:[String]
	userProperty:UserPropertyRequest
}
"""批量导入试题查询请求
	<AUTHOR>
"""
input BatchImportQueryRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.request.BatchImportQueryRequest") {
	"""任务名称"""
	taskName:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""执行时间（起始）"""
	executeStartTime:DateTime
	"""执行时间（终止）"""
	executeEndTime:DateTime
}
input CreCourseSubjectRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.request.CreCourseSubjectRequest") {
	"""课程Id"""
	courseId:String
	"""年度sku属性"""
	year:String
	"""地区sku属性"""
	regional:String
	"""科目类型sku属性"""
	subjectType:String
}
"""@Description
	@Date 2025/5/23
"""
input ImportCourseSubjectRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.request.ImportCourseSubjectRequest") {
	"""excel文件路径"""
	filePath:String
	"""文件名"""
	fileName:String
}
"""导入公需课返回值
	<AUTHOR>
	@date 2024/9/10 10:24
"""
type CourseSubjectQuestionResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.response.CourseSubjectQuestionResponse") {
	code:String
	message:String
	"""任务编号"""
	importTaskResult:ImportTaskResult
}
"""@Description
	@Date 2025/5/22
"""
type CreCourseSubjectResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.response.CreCourseSubjectResponse") {
	"""Id"""
	id:String
	"""课程Id"""
	courseId:String
	"""年度sku属性"""
	year:String
	"""地区sku属性"""
	regional:String
	"""科目类型sku属性"""
	subjectType:String
}
"""各状态及执行结果对应数量
	<AUTHOR>
"""
type EachStateCount @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.response.EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Int!
}
"""批量导入查询响应
	<AUTHOR>
"""
type FindBatchImportByPageResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.service.response.FindBatchImportByPageResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""【必填】服务商id"""
	servicerId:String
	"""任务名称"""
	name:String
	"""任务分类"""
	category:String
	"""所属批次单编号"""
	batchNo:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""创建时间"""
	createdTime:DateTime
	"""就绪时间"""
	alreadyTime:DateTime
	"""执行时间"""
	executingTime:DateTime
	"""完成时间"""
	completedTime:DateTime
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
}
"""导入任务结果
	<AUTHOR>
	@date 2024/9/10 10:25
"""
type ImportTaskResult @type(value:"com.fjhb.platform.jxjypxtypt.dif.common.v1.kernel.taskservice.data.ImportTaskResult") {
	"""任务id"""
	taskId:String
}

scalar List
type FindBatchImportByPageResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [FindBatchImportByPageResponse]}
type CreCourseSubjectResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CreCourseSubjectResponse]}
