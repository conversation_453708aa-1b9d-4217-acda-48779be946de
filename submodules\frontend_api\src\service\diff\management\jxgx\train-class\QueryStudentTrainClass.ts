import CourseLearningBackstage, {
  LearningExperienceTopicRequest,
  StudentLearningExperienceLastedResponse,
  StudentLearningExperienceRequest,
  StudentLearningExperienceStatus,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import MsExamQueryBackstage, {
  AnswerPaperSort,
  SortTypeEnum
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import {
  default as MsSchemeLearningQueryBackstage,
  default as MsSchemeQueryFrontGatewayCourseLearningBackstage,
  StudentSchemeLearningSortRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import studentCourseLearningQuery, {
  SimulateStudentSchemeLearningRequest,
  StudentTrainingResultSimulateRequest
} from '@api/platform-gateway/student-course-learning-query-back-gateway'
import DataResolve from '@api/service/common/utils/DataResolve'
import QueryStudentCourse from '@api/service/management/resource/course/query/QueryStudentCourse'
import QueryStudentCourseware from '@api/service/management/resource/courseware/query/QueryStudentCourseware'
import QueryStudentCoursewareRecordListVo from '@api/service/management/resource/courseware/query/vo/QueryStudentCoursewareRecordListVo'
import BatchQueryUtil from '@api/service/diff/management/jxgx/train-class/BatchQueryUtil'
import TemplateNameManager from '@api/service/management/train-class/mutation/dto/TemplateNameManager'
import ChildOutlinesClass from '@api/service/management/train-class/query/vo/ChildOutlinesClass'
import CourseQuizRecordDetailVo from '@api/service/management/train-class/query/vo/CourseQuizRecordDetailVo'
import ExamRecordVo from '@api/service/management/train-class/query/vo/ExamRecordVo'
import QueryCourseQuizRecordListVo from '@api/service/management/train-class/query/vo/QueryCourseQuizRecordListVo'
import QueryStudentTrainClassListVo from '@api/service/management/train-class/query/vo/QueryStudentTrainClassListVo'
import SchemeLearningInfoVo from '@api/service/management/train-class/query/vo/SchemeLearningInfoVo'
// import StudentTrainClassDetailVo from '@api/service/management/train-class/query/vo/StudentTrainClassDetailVo'
import StudentTrainClassDetailVo from '@api/service/diff/management/jxgx/train-class/StudentTrainClassDetailVo'
import { Page } from '@hbfe/common'
import ExperienceItem from '@api/service/management/train-class/query/vo/ExperienceItem'
import TradeQueryFront from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
import QueryStudentStudy from '@api/service/management/train-class/offlinePart/QueryStudentStudy'
/**
 * @description 查询学员已报班级
 */

class QueryStudentTrainClass {
  /**
   * 查询学员已报培训方案列表
   */
  async queryStudentTrainClassList(
    page: Page,
    queryParams: QueryStudentTrainClassListVo,
    sort?: Array<StudentSchemeLearningSortRequest>
  ): Promise<StudentTrainClassDetailVo[]> {
    let result = [] as StudentTrainClassDetailVo[]
    const response = await MsSchemeLearningQueryBackstage.pageStudentSchemeLearningInServicer({
      page,
      request: queryParams.to(),
      sort: sort
    })
    page.totalSize = response.data?.totalSize ?? 0
    page.totalPageSize = response.data?.totalPageSize ?? 0
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      // result = await Promise.all(
      //   response.data.currentPageData.map(async item => {
      //     return await StudentTrainClassDetailVo.from(item)
      //   })
      // )
      result = await BatchQueryUtil.ConvertToStudentTrainClassList(response.data.currentPageData)
    }
    console.log('StudentTrainClassList', result)

    // 查询班级模拟数据
    if (result?.length) {
      const studentNoList = new Array<string>()
      const schemeIds = new Array<string>()
      const qualificationIdList = new Array<string>()
      result.map((item) => {
        if (item.studentNo && !studentNoList.includes(item.studentNo)) {
          studentNoList.push(item.studentNo)
        }
        if (item.basicInfo?.schemeId && !schemeIds.includes(item.basicInfo.schemeId)) {
          schemeIds.push(item.basicInfo.schemeId)
        }
        if (item.qualificationId) {
          qualificationIdList.push(item.qualificationId)
        }
      })
      // 查询学习心得参与统计
      const experienceRes = await CourseLearningBackstage.statisticsLearningExperienceParticipatedBySchemeIdInServicer({
        schemeIds,
        userId: queryParams.userId
      })
      result.forEach((item) => {
        const experienceCount = experienceRes.data.find((experience) => experience.schemeId === item.basicInfo.schemeId)
        if (experienceCount) {
          item.assessResult.learningExperienceCount = experienceCount.count
        }
      })
      const querySchemeSimulateRequest = new StudentTrainingResultSimulateRequest()
      querySchemeSimulateRequest.studentNos = studentNoList
      querySchemeSimulateRequest.studentSchemeLearning = new SimulateStudentSchemeLearningRequest()
      querySchemeSimulateRequest.studentSchemeLearning.schemeIds = schemeIds

      const querySchemeSimulateResult =
        await studentCourseLearningQuery.getStudentTrainingResultSimulateResponseInServicer(querySchemeSimulateRequest)

      if (querySchemeSimulateResult?.data?.length) {
        result.map((item) => {
          const findSimulate = querySchemeSimulateResult.data.find(
            (scheme) =>
              scheme.studentNo === item.studentNo && scheme.studentSchemeLearning?.schemeId === item.basicInfo?.schemeId
          )
          if (findSimulate) {
            item.haveSimulate = true
            item.onlyHaveExamSimulate = findSimulate.type == 2
            item.simulatePassTime = findSimulate.studentSchemeLearning?.qualifiedTime
            item.simulateExamScore = findSimulate.studentSchemeLearning.score
            item.simulateExamTime.begin = findSimulate.recordCreatedTime
            item.simulateExamTime.end = findSimulate.recordCreatedTime
          }
        })
      }
      // 是否智能学习配置
      const IntelligenceLearningObj = new IntelligenceLearningModule()
      const IntelligenceLearningMap = await IntelligenceLearningObj.doBatchCheck(schemeIds)
      result.map((item) => {
        const intelligentStatus = IntelligenceLearningMap[item.basicInfo.schemeId]
        item.isIntelligentLearning = intelligentStatus === 'true' || false
      })
      const taskres = await IntelligenceLearningObj.doQuerylearningStatus(qualificationIdList)
      result.map((item) => {
        taskres.map((sitem: any) => {
          // 执行失败返回失败原因
          if (item.qualificationId == sitem.qualificationId) {
            item.failReason = sitem.message
            item.IntelligentLearningResult = sitem.result
          }
        })
      })
    }
    // 批量查询学员期别学习信息
    await QueryStudentStudy.batchQueryStudentPeriodStudy(result)
    return result
  }

  /**
   * 补充学员渠道信息(上面接口需要补充渠道信息调此方法)
   */
  async supplementStudentChannelInfo(studentTrainClassList: StudentTrainClassDetailVo[]) {
    if (!studentTrainClassList?.length) return
    const orderNos = studentTrainClassList.map((item) => item.learningRegister.orderNo)
    const res = await TradeQueryFront.pageOrderInServicer({
      page: { pageNo: 1, pageSize: orderNos.length },
      request: {
        orderNoList: orderNos
      }
    })
    studentTrainClassList.forEach((item) => {
      const orderInfo = res.data?.currentPageData?.find((ite) => ite.orderNo === item.learningRegister.orderNo)
      item.saleChannel = orderInfo?.saleChannel
      item.saleChannelName = orderInfo?.saleChannelName
    })
  }

  /**
   * 查询学员培训方案详情-基本信息
   * @param {string} qualificationId - 参训资格id
   */
  async queryStudentTrainClassDetail(qualificationId: string): Promise<StudentTrainClassDetailVo> {
    let result = new StudentTrainClassDetailVo()
    const response = await MsSchemeLearningQueryBackstage.getStudentSchemeLearningInServicer(qualificationId)
    if (response.status?.isSuccess()) {
      result = await StudentTrainClassDetailVo.from(response.data)

      // 查询填充期别学习记录
      result.periodStudy = await QueryStudentStudy.queryPeriodStudyLog(result.studentNo)
    }

    return result
  }

  /**
   * 分页查询课程测验记录
   */
  async queryStudentCourseQuizList(
    page: Page,
    queryParams: QueryCourseQuizRecordListVo
  ): Promise<CourseQuizRecordDetailVo[]> {
    let result = [] as CourseQuizRecordDetailVo[]
    const response = await MsExamQueryBackstage.pageCourseQuizRecordInSubProject({
      page,
      courseId: queryParams.courseId,
      qualificationId: queryParams.qualificationId,
      answerPaperStatus: 2
    })
    page.totalSize = response.data.totalSize ?? 0
    page.totalPageSize = response.data.totalPageSize ?? 0
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = response.data.currentPageData.map(CourseQuizRecordDetailVo.from)
    }
    return result
  }

  /**
   * 查询学员考试记录列表
   * @param {Page} page - 分页
   * @param {string} qualificationId - 参训资格id
   */
  async queryStudentExamRecordList(page: Page, qualificationId: string): Promise<ExamRecordVo[]> {
    let result = [] as ExamRecordVo[]
    const response = await MsExamQueryBackstage.pageExaminationRecordInSubProject({
      page,
      qualificationId,
      answerPaperStatus: 2,
      answerPaperSort: AnswerPaperSort.CREATE_TIME,
      sort: SortTypeEnum.DESC
    })
    page.totalSize = response.data?.totalSize ?? 0
    page.totalPageSize = response.data?.totalPageSize ?? 0
    if (response.status?.isSuccess() && DataResolve.isWeightyArr(response.data?.currentPageData)) {
      result = response.data.currentPageData.map(ExamRecordVo.from)
    }
    return result
  }

  /**
   * 查询学员学习信息-培训方案下
   */
  async queryStudentLearningInfoList(
    page: Page,
    studentNo: string,
    schemeId: string,
    schemaType?: string
  ): Promise<SchemeLearningInfoVo[]> {
    let result = [] as SchemeLearningInfoVo[]
    const outlineIdMap = new Map<string, { name: string; id: string; level: number }[]>()
    try {
      const outlineRes = await MsSchemeQueryFrontGatewayCourseLearningBackstage.getSchemeConfigInServicer({
        schemeId: schemeId,
        needField: ['name', schemaType + '.config.courseTrainingOutlines']
      })
      const outlineList = outlineRes?.data?.schemeConfig
        ? (JSON.parse(outlineRes.data.schemeConfig)[schemaType]?.config.courseTrainingOutlines as ChildOutlinesClass[])
        : ([] as ChildOutlinesClass[])
      if (outlineList && outlineList.length) {
        const getOutlineIdMap = (
          outlineList: ChildOutlinesClass[],
          level: number,
          classifyList: { name: string; id: string; level: number }[]
        ) => {
          outlineList.map((item) => {
            const levelList = []
            levelList.push(...classifyList, { name: item.name, id: item.id, level })
            outlineIdMap.set(item.id, levelList)
            if (item.childOutlines.length) getOutlineIdMap(item.childOutlines, level + 1, levelList)
          })
        }
        getOutlineIdMap(outlineList, 0, [])
      }
    } catch (e) {
      console.log(e, '报错信息')
    }

    const queryStudentCourse = new QueryStudentCourse()
    const schemaTypeId = schemaType === 'chooseCourseLearning' ? 1 : 2
    const courseRes = await queryStudentCourse.queryStudentCoursePage(page, studentNo, schemaTypeId)
    if (DataResolve.isWeightyArr(courseRes)) {
      result = await Promise.all(
        courseRes?.map(async (item) => {
          const option = SchemeLearningInfoVo.fromCourse(item)
          const queryParams = new QueryStudentCoursewareRecordListVo()
          queryParams.studentNo = studentNo
          queryParams.studentCourseId = item.studentCourseId
          if (queryParams.studentCourseId) {
            const queryStudentCourseware = new QueryStudentCourseware()
            const coursewareRes = await queryStudentCourseware.queryStudentAllCoursewareRecord(queryParams)
            coursewareRes?.forEach((item) => {
              const courseware = SchemeLearningInfoVo.formCourseware(item)
              courseware.parentId = queryParams.studentCourseId
              option.children.push(courseware)
            })
          }
          return option
        })
      )
    }
    // 查询班级模拟数据
    const querySchemeSimulateRequest = new StudentTrainingResultSimulateRequest()
    querySchemeSimulateRequest.studentNos = [studentNo]
    querySchemeSimulateRequest.studentSchemeLearning = new SimulateStudentSchemeLearningRequest()
    querySchemeSimulateRequest.studentSchemeLearning.schemeIds = [schemeId]

    const querySchemeSimulateResult =
      await studentCourseLearningQuery.getStudentTrainingResultSimulateResponseInServicer(querySchemeSimulateRequest)
    const schemeSimulate =
      (querySchemeSimulateResult?.data?.length &&
        querySchemeSimulateResult.data.find(
          (it) => it.studentNo === studentNo && it.studentSchemeLearning?.schemeId === schemeId
        )) ||
      undefined

    result.forEach((item) => {
      item.classifyInfo = outlineIdMap
        .get(item.outlineId)
        ?.map((ite) => {
          if (ite.name !== TemplateNameManager.NOCourseTrainingOutlinesName) return ite.name
          else return '-'
        })
        ?.join('>')
      const findCourseSimulate =
        (schemeSimulate?.trainingContentList?.length &&
          schemeSimulate.trainingContentList.find((course) => course.courseId === item.id)) ||
        undefined
      if (findCourseSimulate) {
        item.haveSimulate = true
        item.onlyHaveExamSimulate = schemeSimulate.type == 2
        item.simulateCourseStartTime = findCourseSimulate.startTime
        item.simulateCourseEndTime = findCourseSimulate.endTime
      }
    })
    return result
  }

  /**
   * 查询学员学习心得
   */
  async queryStudentExperienceList(studentNo: string): Promise<ExperienceItem[]> {
    const response = [] as StudentLearningExperienceLastedResponse[]
    const page = new Page(1, 200)
    const request = new StudentLearningExperienceRequest()
    request.status = [
      StudentLearningExperienceStatus.SUBMITTED,
      StudentLearningExperienceStatus.PASS,
      StudentLearningExperienceStatus.RETURNED
    ]
    request.studentLearning = new StudentSchemeLearningRequest()
    request.studentLearning.studentNos = [studentNo]
    const res = await CourseLearningBackstage.pageLearningExperienceParticipatedInServicer({ page, request })
    if (!res.status.isSuccess()) {
      return [] as ExperienceItem[]
    }
    response.push(...res.data.currentPageData)
    if (res.data.totalPageSize > 1) {
      const num = Array.from({ length: res.data.totalPageSize - 1 }, (v, k) => k + 2)
      await Promise.all(
        num.map(async (ite) => {
          const red = await CourseLearningBackstage.pageLearningExperienceParticipatedInServicer({
            page: { pageNo: ite, pageSize: 200 },
            request
          })
          return red
        })
      ).then((res) => {
        res.map((re) => {
          response.push(...re.data.currentPageData)
        })
      })
    }
    if (response.length) {
      const experienceRequest = new LearningExperienceTopicRequest()
      experienceRequest.topicIds = response.map(
        (item) => item.studentLearningExperience.learningExperienceTopic.topicId
      )
      const experienceList = await CourseLearningBackstage.listLearningExperienceTopic(experienceRequest)
      const result = response.map((item) => ExperienceItem.from(item, experienceList.data))
      return result
    } else {
      return [] as ExperienceItem[]
    }
  }
}

export default QueryStudentTrainClass
