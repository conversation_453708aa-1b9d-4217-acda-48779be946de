import PreExamGateway, {
  PaperClassificationCreateRequest,
  PaperClassificationListResponse,
  PaperClassificationUpdateRequest
} from '@api/gateway/PreExam-default'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ExamPaperClassify } from '@/store/module/exam/mode/exam-paper-classify/ExamPaperClassify'

/**
 * 试卷分裂全局的state
 */
export interface IExamPaperClassifyState {
  /**
   * 子试卷分类
   */
  subPaperClassifyList: Array<ExamPaperClassify>
  /**
   * 根节点
   */
  rootCategoryList: Array<ExamPaperClassify>
}

@Module({ namespaced: true, store, dynamic: true, name: 'ExamPaperClassifyModule' })
class ExamPaperClassifyModule extends VuexModule implements IExamPaperClassifyState {
  /**
   * 子试卷分类 - 树的结构使用
   */
  subPaperClassifyList: Array<ExamPaperClassify> = new Array<ExamPaperClassify>()
  /**
   * 根节点
   */
  rootCategoryList = new Array<ExamPaperClassify>()

  /**
   * 查询子分类
   * @param parentId
   */
  @Action
  async requestSubPaperClassificationList(parentId: string) {
    const response = await PreExamGateway.getSubPaperClassificationList(parentId)
    if (response.status.isSuccess()) {
      const subList = response.data
      if (response.status.isSuccess()) {
        this.SET_SUB_PAPER_CLASSIFY_LIST(subList)
      }
    }
    return response.status
  }

  /**
   * 请求查询根节点分类
   * @param parentId
   */
  @Action
  async requestRootPaperClassificationList() {
    const response = await PreExamGateway.getSubPaperClassificationList('-1')
    if (response.status.isSuccess()) {
      const subList = response.data
      if (response.status.isSuccess()) {
        this.SET_ROOT_PAPER_CLASSIFY_LIST(subList)
      }
    }
    return response.status
  }

  /**
   * 创建试卷分类
   * @param create
   */
  @Action
  async createPaperClassification(create: PaperClassificationCreateRequest) {
    const res = await PreExamGateway.createPaperClassification(create)
    return res.status
  }

  /**
   * 更新试卷分类
   * @param update
   */
  @Action
  async updatePaperClassification(update: PaperClassificationUpdateRequest) {
    const res = await PreExamGateway.updatePaperClassification(update)
    return res.status
  }

  /**
   * 删除试卷分类
   * @param id
   */
  @Action
  async deletePaperClassification(id: string) {
    const res = await PreExamGateway.deletePaperClassification(id)
    return res.status
  }

  /**
   * 设置只节点分类
   * @param subList
   * @constructor
   */
  @Mutation
  private SET_SUB_PAPER_CLASSIFY_LIST(subList: Array<PaperClassificationListResponse>) {
    this.subPaperClassifyList = new Array<ExamPaperClassify>()
    subList.map(p => {
      const subItem = new ExamPaperClassify()
      subItem.name = p.name
      subItem.id = p.id
      subItem.parentId = p.parentId
      subItem.description = p.description
      this.subPaperClassifyList.push(subItem)
    })
  }

  /**
   * 设置业务状态层的根节点
   * @param subList
   * @constructor
   */
  @Mutation
  private SET_ROOT_PAPER_CLASSIFY_LIST(subList: Array<PaperClassificationListResponse>) {
    this.rootCategoryList = new Array<ExamPaperClassify>()
    subList.map(p => {
      const subItem = new ExamPaperClassify()
      subItem.name = p.name
      subItem.id = p.id
      subItem.parentId = p.parentId
      subItem.description = p.description
      this.rootCategoryList.push(subItem)
    })
  }
}

export default getModule(ExamPaperClassifyModule)
