schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""批量授权分销商品"""
	importSupplyDistributionAuthBatch(request:ImportDistributionAuthBatchRequest):String
}
"""导入批量授权分销商品
	<AUTHOR>
	@date 2022/11/7 14:12
"""
input ImportDistributionAuthBatchRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ImportDistributionAuthBatchRequest") {
	"""excel文件路径"""
	filePath:String
	"""文件名称"""
	fileName:String
}

scalar List
