import roleGateWay from '@api/ms-gateway/ms-role-v1'
import { ResponseStatus } from '@hbfe/common'
export class MutationDeleteRole {
  /*
   *  角色id
   * */
  roleId = ''

  async doDelete() {
    try {
      if (!this.roleId) {
        return new ResponseStatus(500, '角色id不可为空')
      }

      const res = await roleGateWay.deleteRole(this.roleId)
      console.log('调用了doDelete方法，返回值=', res.status)
      return res.status
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/mutation/MutationDeleteRole.ts所处方法，doDelete',
        e
      )
    }
  }
}
