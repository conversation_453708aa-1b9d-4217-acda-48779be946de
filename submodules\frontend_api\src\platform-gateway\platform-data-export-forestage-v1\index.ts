import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-data-export-forestage-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-data-export-forestage-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

/**
 * 学员培训方案学习查询条件
<AUTHOR>
@version 1.0
@date 2022/1/17 11:40
 */
export class StudentSchemeLearningRequest {
  /**
   * 学员信息
   */
  student?: UserRequest
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest
  /**
   * 对接管理系统
   */
  connectManageSystem?: ConnectManageSystemRequest
  /**
   * 扩展信息
   */
  extendedInfo?: ExtendedInfoRequest
  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  trainingChannelName?: string
  /**
   * 非分销门户
   */
  notDistributionPortal?: boolean
}

/**
 * @version: 1.0
@description: 对接管理系统
@author: sugs
@create: 2022-11-15 11:27
 */
export class ConnectManageSystemRequest {
  /**
   * 同步状态
@see SyncStatus
0 未同步
1 已同步
2 同步失败
   */
  syncStatus?: number
}

/**
 * 数据分析信息
<AUTHOR>
@version 1.0
@date 2022/1/20 15:14
 */
export class DataAnalysisRequest {
  /**
   * 成果配置可获得学时
   */
  trainingResultPeriod?: DoubleScopeRequest
  /**
   * 考核要求学时
   */
  requirePeriod?: DoubleScopeRequest
  /**
   * 已获得总学时
   */
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest {
  /**
   * 是否打印
true 打印 false 未打印
   */
  whetherToPrint?: boolean
}

/**
 * 学员学习报名信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:08
 */
export class LearningRegisterRequest {
  /**
   * 报名方式
<p>
1:学员自主报名
2:集体报名
3:管理员导入
@see StudentRegisterTypes
   */
  registerType?: number
  /**
   * 报名来源类型(ORDER：订单 SUB_ORDER：子订单 EXCHANGE_ORDER：换货单)
@see StudentSourceTypes
   */
  sourceType?: string
  /**
   * 报名来源ID
   */
  sourceId?: string
  /**
   * 学员状态(1:正常 2：冻结 3：失效)
@see StudentStatus
   */
  status?: Array<number>
  /**
   * 报名时间
   */
  registerTime?: DateScopeRequest
  /**
   * 订单销售渠道
   */
  saleChannels?: Array<number>
  /**
   * 来源订单号
   */
  orderNoList?: Array<string>
  /**
   * 来源子订单号
   */
  subOrderNoList?: Array<string>
  /**
   * 来源批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 分销门户id
   */
  portalId?: string
}

/**
 * 地区模型
<AUTHOR>
@version 1.0
@date 2022/2/27 20:01
 */
export class RegionRequest {
  /**
   * 地区：省
   */
  province?: string
  /**
   * 地区：市
   */
  city?: string
  /**
   * 地区：区
   */
  county?: string
}

/**
 * 学员学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class StudentLearningRequest {
  /**
   * 培训结果
<p>
-1:未知，培训尚未完成
1:培训合格
0:培训不合格
@see StudentTrainingResults
   */
  trainingResultList?: Array<number>
  /**
   * 培训结果时间
   */
  trainingResultTime?: DateScopeRequest
  /**
   * 无需学习的学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 练习学习方式
4：自主学习课程学习方式
@see LearningTypes
   */
  notLearningTypeList?: Array<number>
  /**
   * 课程学习状态（0：未学习 1：学习中 2：学习完成）
   */
  courseScheduleStatus?: number
  /**
   * 考试结果（-1：未考核 0：不合格 1：合格）
@see AssessCalculateResults
   */
  examAssessResultList?: Array<number>
}

/**
 * 地区sku属性查询条件
<AUTHOR>
@version 1.0
@date 2022/2/25 10:55
 */
export class RegionSkuPropertyRequest {
  /**
   * 地区: 省
   */
  province?: string
  /**
   * 地区: 市
   */
  city?: string
  /**
   * 地区: 区县
   */
  county?: string
}

/**
 * 地区匹配查询
<AUTHOR>
@version 1.0
@date 2022/2/25 14:19
 */
export class RegionSkuPropertySearchRequest {
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest>
  /**
   * 地区匹配条件
<p>
ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回，如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
PART部分匹配：查询结果返回的地区与查询条件有给值的地区就会返回 如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
@see com.fjhb.ms.data.export.front.gateway.jxjy.graphql.trade.common.request.skuProperty.RegionSkuPropertySearchRequest.RegionSearchType
   */
  regionSearchType?: number
}

/**
 * 培训方案信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class SchemeRequest {
  /**
   * 培训方案id
   */
  schemeId?: string
  /**
   * 培训方案id
   */
  schemeIdList?: Array<string>
  /**
   * 培训方案类型
<p>
chooseCourseLearning: 选课规则
autonomousCourseLearning: 自主选课
@see SchemeType
   */
  schemeType?: string
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 培训属性
   */
  skuProperty?: SchemeSkuPropertyRequest
}

/**
 * 培训属性
<AUTHOR>
@version 1.0
@date 2022/1/17 10:22
 */
export class SchemeSkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 岗位类别
   */
  positionCategory?: Array<string>
  /**
   * 培训对象
   */
  trainingObject?: Array<string>
  /**
   * 技术等级
   */
  jobLevel?: Array<string>
  /**
   * 工种
   */
  jobCategory?: Array<string>
  /**
   * 科目
   */
  subject?: Array<string>
  /**
   * 年级
   */
  grade?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
}

/**
 * 用户属性
<AUTHOR>
@version 1.0
@date 2022/1/15 11:01
 */
export class UserPropertyRequest {
  /**
   * 所属地区路径
   */
  regionList?: Array<RegionRequest>
  /**
   * 工作单位名称
   */
  companyName?: string
}

/**
 * 用户信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:00
 */
export class UserRequest {
  /**
   * 用户id
   */
  userIdList?: Array<string>
  /**
   * 账户id
   */
  accountIdList?: Array<string>
  /**
   * 用户属性
   */
  userProperty?: UserPropertyRequest
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async exportStudentSchemeLearningExcelInCollective(
    request: StudentSchemeLearningRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.exportStudentSchemeLearningExcelInCollective,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
