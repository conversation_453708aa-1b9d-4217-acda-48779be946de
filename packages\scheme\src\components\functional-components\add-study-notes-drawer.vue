<template>
  <div>
    <!--新增学习心得-->
    <el-drawer
      title="新增学习心得"
      :visible.sync="addStudyNotesDialog"
      size="800px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      :show-close="false"
      @open="openDrawer()"
    >
      <div class="drawer-bd">
        <el-form ref="form" label-width="130px" class="m-form f-mt10 f-mlr20">
          <el-form-item label="学习心得类型：" required>
            <el-select
              clearable
              placeholder="请选择学习心得类型"
              v-model="feelItem.experienceType.current"
              :disabled="routerMode == 3 && clickEdit"
            >
              <el-option v-for="item in experienceTypeList" :key="item.code" :label="item.desc" :value="item.code">
              </el-option>
            </el-select>
          </el-form-item>
          <template v-if="feelItem.experienceType.equal(LearningExperienceEnum.course_experience)">
            <el-form-item label="指定课程：" required>
              <el-button type="primary" @click="showAddCourseDialog()" :disabled="!hasCourse">添加课程</el-button>
              <p style="color: #f56c6c" v-if="!hasCourse">请先添加课程</p>
            </el-form-item>
            <!--表格-->
            <el-table
              stripe
              max-height="500px"
              class="m-table f-mt10 f-mb20"
              :data="feelItem.hasCourse ? feelItem.courseList : []"
            >
              <el-table-column label="课程名称" min-width="300" prop="name"></el-table-column>
              <el-table-column label="学时" min-width="120" align="center" prop="period"></el-table-column>
              <el-table-column label="操作" width="80" align="center" fixed="right">
                <template v-slot="{ row }">
                  <el-button type="text" size="mini" @click="removeCourse(row)">移除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>

          <el-form-item label="主题：" required>
            <el-input v-model="feelItem.theme" placeholder="请填写主题" />
          </el-form-item>
          <el-form-item label="内容：">
            <hb-tinymce-editor v-model="feelItem.content" :toolbar="''"></hb-tinymce-editor>
          </el-form-item>
          <el-form-item label="参加时间：" required>
            <el-radio-group v-model="feelItem.joinTimeType.current" @change="changeTimeType">
              <el-radio v-for="item in joinTimeList" :label="item.code" :key="item.code">{{ item.desc }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="选择时间：" required v-if="feelItem.joinTimeType.equal(JoinTimeTypeEnum.designate_time)">
            <el-date-picker
              v-model="feelItem.joinTime"
              value-format="yyyy-MM-dd HH:mm:ss"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              class="form-l"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item label="作答形式：" required>
            <el-radio-group v-model="feelItem.answerType.current" @change="changeAnswerType()">
              <el-radio v-for="item in answerTypeList" :label="item.code" :key="item.code">
                {{ item.desc }}
                <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip" v-if="item.code == 1">
                  <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                  <div slot="content" v-html="answerText"></div>
                </el-tooltip>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="提交要求：" required>
            <span v-if="feelItem.answerType.equal(AnswerTypeEnum.attachments)">
              文件大小不超过
              <el-input-number
                :controls="false"
                v-model="feelItem.submitLimitNum"
                :precision="1"
                size="small"
                :min="0"
                class="input-num f-mr5"
              />
              M
            </span>

            <span v-else>
              提交内容至少
              <el-input-number
                :controls="false"
                :precision="0"
                type="number"
                v-model="feelItem.submitLimitNum"
                size="small"
                class="input-num f-mr5"
              />
              字
            </span>
          </el-form-item>
          <el-form-item label="审核方式：" required>
            <el-radio-group v-model="feelItem.checkType.current">
              <el-radio v-for="item in checkTypeList" :label="item.code" :key="item.code"
                >{{ item.desc }}
                <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
                  <i class="el-icon-info m-tooltip-icon f-c9 f-ml10"></i>
                  <div slot="content" v-html="checkText(item)"></div>
                </el-tooltip>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="提交次数：" v-if="feelItem.checkType.equal(CheckTypeEnum.artificial)" required>
            <el-radio-group v-model="feelItem.submitCountType">
              <el-radio :label="false">不限次</el-radio>
              <el-radio :label="true"
                >限制提交
                <span v-if="feelItem.submitCountType">
                  <el-input-number
                    :precision="0"
                    :controls="false"
                    size="small"
                    class="input-num f-mr5"
                    v-model="feelItem.submitCount"
                  />
                  次
                </span>
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="心得总分：">100分</el-form-item>
          <el-form-item class="m-btn-bar is-sticky">
            <el-button @click="cencel()">取消</el-button>
            <el-button @click="sureLearningFeel" type="primary">确定</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-drawer>
    <!--添加课程-->
    <el-drawer
      title="添加课程"
      :visible.sync="addCourseDialog"
      size="1200px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
      :destroy-on-close="true"
    >
      <div class="drawer-bd">
        <el-row :gutter="16" class="m-query f-mt10">
          <el-form :inline="true" label-width="auto">
            <el-col :span="10" v-if="isNoClassification">
              <el-form-item label="分类名称：">
                <el-cascader
                  ref="outlinesRef"
                  clearable
                  lazy
                  :options="outlinesTree"
                  :props="defaultProps"
                  v-model="selectedOutlinePath"
                  placeholder="请选择分类"
                  class="form-m"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="课程名称">
                <el-input clearable placeholder="请输入课程名称" v-model="searchCourseName" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-button type="primary" @click="search()">查询</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table
          stripe
          max-height="500px"
          class="m-table f-mt10"
          :data="courseList"
          ref="courseListRef"
          v-loading="loading"
        >
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="课程名称" min-width="300">
            <template v-slot="{ row }">
              <div>
                <span v-if="row.isBeingUsedAsCompulsory">
                  <el-tag type="danger" size="mini" class="f-mr5">必修</el-tag> {{ row.name }}
                </span>
                <span v-else class="f-mr5">{{ row.name }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学时" min-width="120" align="center" prop="period"></el-table-column>
          <el-table-column label="操作" width="100" align="center" fixed="right">
            <template v-slot="{ row }">
              <el-checkbox
                v-model="row.isChecked"
                label="选择"
                @change="
                  (value) => {
                    return handleCourseCheckChange(value, row)
                  }
                "
              >
                选择
              </el-checkbox>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="coursePage" v-bind="coursePage"></hb-pagination>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="addCourseDialog = false">取消</el-button>
        <el-button @click="sureAddCourse" type="primary">确定</el-button>
      </div>
    </el-drawer>
  </div>
</template>
<style lang="less" scoped>
  ::v-deep input[type='number'] {
    -moz-appearance: textfield;
  }

  ::v-deep input[type='number']::-webkit-inner-spin-button,
  ::v-deep input[type='number']::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
</style>
<script lang="ts">
  import { Component, Prop, PropSync, Ref, Vue } from 'vue-property-decorator'
  import AnswerType, { AnswerTypeEnum } from '@api/service/management/train-class/mutation/Enum/AnswerTypeEnum'
  import CheckType, { CheckTypeEnum } from '@api/service/management/train-class/mutation/Enum/CheckTypeEnum'
  import ExperienceType, {
    LearningExperienceEnum
  } from '@api/service/management/train-class/mutation/Enum/LearningExperienceEnum'
  import JoinTimeType, { JoinTimeTypeEnum } from '@api/service/management/train-class/mutation/Enum/JoinTimeTypeEnum'
  import ExperienceItem from '@api/service/management/train-class/mutation/vo/ExperienceItem'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import LearningExperience from '@api/service/management/train-class/mutation/vo/LearningExperience'
  import { cloneDeep } from 'lodash'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import { UiPage } from '@hbfe/common'
  import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
  import { bind, debounce } from 'lodash-decorators'
  import { CourseLoadModeEnum } from '@api/service/management/train-class/mutation/Enum/CourseLoadMode'
  import QueryCoursePackage from '@api/service/management/resource/course-package/query/QueryCoursePackage'
  import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
  import TrainClassBaseModel from '@api/service/management/train-class/mutation/vo/TrainClassBaseModel'
  import { ElCascader } from 'element-ui/types/cascader'
  import CourseLearningLearningType from '@api/service/management/train-class/mutation/vo/CourseLearningLearningType'

  @Component
  export default class extends Vue {
    /**
     * 学习心得配置 - 双向绑定
     */
    @Prop({
      type: LearningExperience,
      default: () => {
        return new LearningExperience()
      }
    })
    learningFeelInfo!: LearningExperience

    @Prop({
      type: Object,
      default: () => new Classification()
    })
    classification: Classification

    /**
     * 课程学习信息
     */
    @Prop({
      required: true,
      type: CourseLearningLearningType
    })
    courseLearning: CourseLearningLearningType

    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: TrainClassBaseModel }) schemeDetail!: TrainClassBaseModel

    /**
     * 路由模式（1-创建，2-复制，3-编辑，默认：创建）
     */
    @Prop({
      type: Number,
      default: 1
    })
    routerMode: number

    @Ref('outlinesRef') outlinesRef: ElCascader
    // 级联选择器默认配置
    defaultProps = {
      label: 'name',
      children: 'childOutlines',
      value: 'id'
    }
    // 分页参数 - 课程
    coursePage: UiPage
    // 课程列表
    courseList: Array<CourseInCoursePackage> = new Array<CourseInCoursePackage>()
    // 选择课程对象
    selectedCourseObject: CourseInCoursePackage = new CourseInCoursePackage()
    // 作答方式列表
    answerTypeList = new AnswerType().list()
    // 审核方式列表
    checkTypeList = new CheckType().list()
    // 学习心得类型列表
    experienceTypeList = new ExperienceType().list()
    // 参与时间列表
    joinTimeList = new JoinTimeType().list()
    // 查询课程名称
    searchCourseName = ''
    // 当前课程大纲
    currentClassification = new Classification()
    // 已选大纲树路径
    selectedOutlinePath: Array<string> = new Array<string>()
    // 大纲树
    outlinesTree = new Array<Classification>()
    //抽屉定义
    addStudyNotesDialog = false
    addCourseDialog = false
    // 添加课程添加loading动画
    loading = false
    // 枚举赋值
    JoinTimeTypeEnum = JoinTimeTypeEnum
    AnswerTypeEnum = AnswerTypeEnum
    LearningExperienceEnum = LearningExperienceEnum
    CheckTypeEnum = CheckTypeEnum
    // 新增学习心得
    feelItem = new ExperienceItem()
    // 查询课程包总控
    queryCoursePackageM: QueryCoursePackage = new QueryCoursePackage()
    // 查询课程总控
    queryCourseM: QueryCourse = new QueryCourse()
    // 心得时间存储
    feelTime: string[] = []
    // 编辑按钮点击
    clickEdit = false
    /**
     * 培训方案id
     */
    get schemeId() {
      return this.schemeDetail?.id || this.schemeDetail?.idCopy || ''
    }

    /**
     * 获取所有的课程大纲叶子节点
     */
    get outlineTreeLeaves() {
      return this.outlineTreeFindAllLeaves([this.classification])
    }

    /**
     * 判断是不是有分类的大纲
     */
    get isNoClassification() {
      return this.classification && this.classification.childOutlines && this.classification.childOutlines.length
    }
    constructor() {
      super()
      this.coursePage = new UiPage(this.queryCourseList, this.queryCourseList)
    }

    /**
     * 查询课程
     */
    async search() {
      if (this.isNoClassification) {
        this.currentClassification = (this.outlinesRef as any).getCheckedNodes(true)[0].data
      }
      if (this.currentClassification) {
        await this.queryCourseList()
      }
      this.coursePage.currentChange(1)
    }

    /**
     * 查询课程列表
     */
    async queryCourseList() {
      this.loading = true
      const result = [] as CourseInCoursePackage[]
      /** 加载课程包下课程 */
      if (this.currentClassification.courseLoadMode === CourseLoadModeEnum.BY_COURSE_PACKAGE_ID) {
        const coursePackageId = this.currentClassification.coursePackageId
        const respList = await this.queryCoursePackageM.pageQueryCourseListInCoursePackage(
          this.coursePage,
          coursePackageId,
          this.searchCourseName
        )
        respList.forEach((item) => {
          item.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
            return node.coursePackageId === coursePackageId
          }, 'name')
          result.push(item)
        })
      }
      /** 加载课程大纲节点下课程 */
      if (this.currentClassification.courseLoadMode === CourseLoadModeEnum.BY_OUTLINE_ID) {
        const leaves = this.outlineTreeFindAllLeaves([this.currentClassification])?.filter(
          (item) => item.coursePackageId
        )
        const outlineIdList = this.$route.params.schemeId
          ? leaves?.map((item) => item.idCopy)
          : leaves?.map((item) => item.id)
        if (outlineIdList.length) {
          const respList = await this.queryCourseM.queryCourseListInSchemeByOutline(
            this.coursePage,
            outlineIdList,
            this.schemeId,
            this.searchCourseName
          )
          respList?.forEach((item) => {
            const coursePackageName = item.sourceCoursePackageName
            const opt = new CourseInCoursePackage()
            Object.assign(opt, item)
            opt.coursePackageName = coursePackageName
            opt.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
              return node.id === item.outlineId
            }, 'name')
            result.push(opt)
          })
        }
      }
      this.courseList = result
      ;(this.$refs['courseListRef'] as any)?.doLayout()
      this.loading = false
      this.defaultClick()
    }

    /**
     * 查找所有叶子节点 - 课程大纲树通用
     */
    outlineTreeFindAllLeaves(tree: Array<Classification>) {
      return CreateSchemeUtils.treeFindAllLeaves<Classification>(tree, 'childOutlines')
    }
    /**
     * 查找节点路径 - 课程大纲树通用
     */
    outlineTreeFindPath(func: any, key: string) {
      return CreateSchemeUtils.treeFindPath<Classification>(this.classification as any, func, key, 'childOutlines')
    }

    /**
     * 课程选中状态切换响应事件
     */
    handleCourseCheckChange(value: boolean, row: any) {
      this.courseList?.forEach((el) => {
        el.isChecked = false
      })
      if (value) {
        row.isChecked = true
        this.selectedCourseObject = row
      } else {
        this.selectedCourseObject = new CourseInCoursePackage()
      }
    }

    /**
     * 确定添加课程按钮
     */
    @bind
    @debounce(200)
    sureAddCourse() {
      if (!this.selectedCourseObject.id) {
        this.$message.warning('请选择要勾选的课程')
        return
      } else {
        this.feelItem.addCourse(this.selectedCourseObject, this.currentClassification)
        this.$message.success('保存课程成功')
        this.addCourseDialog = false
      }
    }
    /**
     * 移除课程
     */
    removeCourse(item: any) {
      this.feelItem.removeCourse(item.courseId)
    }

    /**
     * 打开新增学习心得弹窗
     */
    showAddStudyNotesDialog(item?: ExperienceItem, target?: boolean) {
      if (item) {
        this.feelItem = cloneDeep(item)
      } else {
        this.feelItem = new ExperienceItem()
      }
      if (target) {
        this.clickEdit = true
      }
      this.addStudyNotesDialog = true
    }

    /**
     * 打开课程添加弹窗
     */
    showAddCourseDialog() {
      if (this.feelItem.courseList.length == 0) {
        this.courseList?.forEach((el) => {
          el.isChecked = false
        })
        this.selectedCourseObject = new CourseInCoursePackage()
      }
      if (this.selectedOutlinePath) {
        this.defaultClick()
      }
      // 如果课程已经被移除
      if (!this.feelItem.hasCourse) {
        this.courseList?.forEach((el) => {
          el.isChecked = false
        })
      }
      this.addCourseDialog = true
    }

    /**
     * 获取子节点的id
     */
    get hasChildOutlineIds() {
      const target = this.classification.childOutlines[0]
      return target?.childOutlines?.[0]
        ? target.childOutlines[0].childOutlines?.[0]
          ? [target.id, target.childOutlines[0].id, target.childOutlines[0].childOutlines[0].id]
          : [target.id, target.childOutlines[0].id]
        : [target.id]
    }

    /**
     * 获取子节点
     */
    get hasChildOutline() {
      const target = this.classification.childOutlines[0]
      return target?.childOutlines?.[0]
        ? target.childOutlines[0].childOutlines?.[0]
          ? target.childOutlines[0].childOutlines[0]
          : target.childOutlines[0]
        : target
    }

    /**
     * 校验学习心得开始时间是否在培训方案时间内
     */
    convertTimeFormat() {
      // 转换时间为时间戳
      const trainingBeginDate = this.convertToTimestamp(this.schemeDetail.trainingBeginDate)
      const trainingEndDate = this.convertToTimestamp(this.schemeDetail.trainingEndDate)
      const experienceBeginDate = this.convertToTimestamp(this.feelItem.joinTime[0])
      const experienceEndDate = this.convertToTimestamp(this.feelItem.joinTime[1])
      //   培训方案开始时间 < 心得开始时间 < 心得结束时间 < 培训方案结束时间
      if (trainingBeginDate && trainingEndDate) {
        if (
          experienceBeginDate > trainingBeginDate &&
          experienceBeginDate < trainingEndDate &&
          experienceEndDate < trainingEndDate &&
          experienceEndDate > trainingBeginDate
        ) {
          return false
        } else {
          return true
        }
      } else {
        return false
      }
    }

    /**
     * 转换时间格式
     */
    convertToTimestamp(timeString: string) {
      const date = new Date(timeString)
      const timestamp = date.getTime()
      return timestamp
    }

    /**
     * 默认选中
     */
    defaultClick() {
      this.courseList?.forEach((el) => {
        el.isChecked = false
        if (el.id == this.feelItem.courseList[0]?.courseId) {
          el.isChecked = true
        }
      })
    }

    /**
     * 改变时间
     */
    changeTimeType() {
      if (this.feelItem.joinTimeType.equal(JoinTimeTypeEnum.designate_time)) {
        this.feelItem.joinTime = this.feelTime
      } else {
        this.feelTime = this.feelItem.joinTime
        this.feelItem.joinTime = []
      }
    }

    /**
     * 显示审核方式内标签文字
     */
    checkText(item: EnumOption<CheckTypeEnum>) {
      return item.code == CheckTypeEnum.auto
        ? `提交自动通过：学员提交内容后无需人工审核，<span class="f-cb">默认学员分值100分</span>`
        : `学员提交内容后会在活动管理的页面显示，审核状态为待审核，需人工审核后变更审核状态。`
    }

    /**
     * 显示作答形式内标签文字
     */
    get answerText() {
      return `文件格式默认支持.doc .docx .pdf .ppt .pptx jpg等，不可上传视频的格式，且附件类型暂不支持批量导出记录`
    }

    /**
     * 添加学习心得时校验规则
     */
    verifyExperience() {
      const item = this.feelItem
      if (item.experienceType.equal(LearningExperienceEnum.course_experience)) {
        if (!item.courseList.length) {
          this.$message.warning('指定课程未添加，请先添加指定课程。')
          return true
        }
      }
      if (!item.theme) {
        this.$message.warning('主题未填写，请先填写主题。')
        return true
      }
      if (item.joinTimeType.equal(JoinTimeTypeEnum.designate_time)) {
        if (!item.joinTime.length) {
          this.$message.warning('未填写参加时间，请先添加参加时间。')
          return true
        }
        if (this.convertTimeFormat()) {
          this.$message.warning('参加时间需在培训方案的时间范围内，请重新选择。')
          return true
        }
      }
      if (item.answerType.equal(AnswerTypeEnum.attachments) && item.submitLimitNum == 0) {
        this.$message.warning('请认真填写文件大小。')
        return true
      }
      if (item.checkType.equal(CheckTypeEnum.artificial) && item.submitCountType && !item.submitCount) {
        this.$message.warning('提交次数未填写，请先填写提交次数。')
        return true
      }

      return false
    }

    /**
     *  更改作答形式
     */
    changeAnswerType() {
      this.feelItem.submitLimitNum = 0
    }

    /**
     * 更改时间类型
     */
    changeJoinTimeType() {
      if (
        this.feelItem.joinTimeType.equal(JoinTimeTypeEnum.class_time) &&
        this.schemeDetail.trainingEndDate &&
        this.schemeDetail.trainingBeginDate
      ) {
        this.feelItem.joinTime = [this.schemeDetail.trainingBeginDate, this.schemeDetail.trainingEndDate]
      }
    }

    // 取消
    cencel() {
      this.clickEdit = false
      this.addStudyNotesDialog = false
    }

    /**
     * 学习心得确认添加
     */
    @bind
    @debounce(200)
    async sureLearningFeel() {
      if (this.verifyExperience()) {
        return
      }
      this.changeJoinTimeType()
      const res = await this.learningFeelInfo.edit(this.feelItem)
      if (res.isSuccess()) {
        this.$emit('refreshPage')
        this.addStudyNotesDialog = false
        this.clickEdit = false
        this.$message.success('保存学习心得成功')
      } else {
        this.$message.error('保存学习心得失败')
      }
    }

    /**
     * 打开添加课程抽屉
     */
    openDrawer() {
      // 有分类
      if (this.classification?.childOutlines?.length) {
        this.outlinesTree = this.classification.childOutlines
        this.selectedOutlinePath = this.hasChildOutlineIds
        this.currentClassification = this.hasChildOutline
      }
      // 无分类
      else {
        this.outlinesTree = [this.classification]
        this.currentClassification = this.classification
      }
      this.searchCourseName = ''
      this.coursePage.currentChange(1)
    }

    /**
     * 是否有课程，没有的时候禁用该按钮
     */
    get hasCourse() {
      return this.outlineTreeFindAllLeaves([this.classification]).some((item) => {
        return item.coursePackageId
      })
    }
  }
</script>
