<route-meta>
{
"isMenu": true,
"title": "导入学员并开班",
"sort": 2
}
</route-meta>

<script lang="ts">
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, ZTGLY } from '@/models/RoleTypes'
  import Scheme from '@hbfe/jxjy-admin-import/src/scheme/index.vue'
  @RoleTypeDecorator({
    import: [WXGLY, ZTGLY],
    WXImport: [WXGLY],
    ZTImport: [ZTGLY],
    querySchemeZt: [ZTGLY],
    querySchemeWx: [WXGLY],
    queryPeriodWx: [WXGLY],
    queryPeriodZt: [ZTGLY]
  })
  export default class extends Scheme {}
</script>
