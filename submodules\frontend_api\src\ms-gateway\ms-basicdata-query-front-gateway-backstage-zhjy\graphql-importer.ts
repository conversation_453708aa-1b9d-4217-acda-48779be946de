import getAdminInfoInSubProject from './queries/getAdminInfoInSubProject.graphql'
import getBankInSubProject from './queries/getBankInSubProject.graphql'
import getBusinessDataDictionaryInSubProject from './queries/getBusinessDataDictionaryInSubProject.graphql'
import getEnterpriseUnitAdminInfoInMyself from './queries/getEnterpriseUnitAdminInfoInMyself.graphql'
import getEnterpriseUnitAdminInfoInSubProject from './queries/getEnterpriseUnitAdminInfoInSubProject.graphql'
import getEnterpriseUnitCountInGovernmentUnit from './queries/getEnterpriseUnitCountInGovernmentUnit.graphql'
import getEnterpriseUnitCountInSubProject from './queries/getEnterpriseUnitCountInSubProject.graphql'
import getEnterpriseUnitInfoInMyself from './queries/getEnterpriseUnitInfoInMyself.graphql'
import getEnterpriseUnitInfoInSubProject from './queries/getEnterpriseUnitInfoInSubProject.graphql'
import getGovernmentUnitAdminInfoInMyself from './queries/getGovernmentUnitAdminInfoInMyself.graphql'
import getGovernmentUnitAdminInfoInSubProject from './queries/getGovernmentUnitAdminInfoInSubProject.graphql'
import getGovernmentUnitInfoInMyself from './queries/getGovernmentUnitInfoInMyself.graphql'
import getGovernmentUnitInfoInSubProject from './queries/getGovernmentUnitInfoInSubProject.graphql'
import getNewsCategoryId from './queries/getNewsCategoryId.graphql'
import getNewsDetail from './queries/getNewsDetail.graphql'
import getRegionInSubProject from './queries/getRegionInSubProject.graphql'
import getRootNewsCategory from './queries/getRootNewsCategory.graphql'
import getRootNewsCategoryById from './queries/getRootNewsCategoryById.graphql'
import getUserInSubProject from './queries/getUserInSubProject.graphql'
import listBusinessDataDictionaryInSubProject from './queries/listBusinessDataDictionaryInSubProject.graphql'
import listChildNewsCategory from './queries/listChildNewsCategory.graphql'
import listChildRegionInSubProject from './queries/listChildRegionInSubProject.graphql'
import listRegionByCodeInSubProject from './queries/listRegionByCodeInSubProject.graphql'
import listRegionByCodePathInSubProject from './queries/listRegionByCodePathInSubProject.graphql'
import listRegionInSubProject from './queries/listRegionInSubProject.graphql'
import listRootNewsCategory from './queries/listRootNewsCategory.graphql'
import pageBankInSubProject from './queries/pageBankInSubProject.graphql'
import pageBusinessDataDictionaryInSubProject from './queries/pageBusinessDataDictionaryInSubProject.graphql'
import pageCompleteNews from './queries/pageCompleteNews.graphql'
import pageEnterpriseUnitAdminInfoInGovernmentUnit from './queries/pageEnterpriseUnitAdminInfoInGovernmentUnit.graphql'
import pageEnterpriseUnitAdminInfoInMyself from './queries/pageEnterpriseUnitAdminInfoInMyself.graphql'
import pageEnterpriseUnitAdminInfoInSubProject from './queries/pageEnterpriseUnitAdminInfoInSubProject.graphql'
import pageEnterpriseUnitInfoInGovernmentUnit from './queries/pageEnterpriseUnitInfoInGovernmentUnit.graphql'
import pageEnterpriseUnitInfoInServicer from './queries/pageEnterpriseUnitInfoInServicer.graphql'
import pageEnterpriseUnitInfoInSubProject from './queries/pageEnterpriseUnitInfoInSubProject.graphql'
import pageGovernmentUnitAdminInfoInGovernmentUnit from './queries/pageGovernmentUnitAdminInfoInGovernmentUnit.graphql'
import pageGovernmentUnitAdminInfoInMyself from './queries/pageGovernmentUnitAdminInfoInMyself.graphql'
import pageGovernmentUnitAdminInfoInSubProject from './queries/pageGovernmentUnitAdminInfoInSubProject.graphql'
import pageGovernmentUnitInfoInEnterpriseUnit from './queries/pageGovernmentUnitInfoInEnterpriseUnit.graphql'
import pageGovernmentUnitInfoInGovernmentUnit from './queries/pageGovernmentUnitInfoInGovernmentUnit.graphql'
import pageGovernmentUnitInfoInSubProject from './queries/pageGovernmentUnitInfoInSubProject.graphql'
import pageRatingAgenciesUnitInfoInGovernmentUnit from './queries/pageRatingAgenciesUnitInfoInGovernmentUnit.graphql'
import pageSimpleNews from './queries/pageSimpleNews.graphql'
import pageUserInfoInGeneral from './queries/pageUserInfoInGeneral.graphql'
import statisticEnterpriseUnitGroupByTimeInGovernmentUnit from './queries/statisticEnterpriseUnitGroupByTimeInGovernmentUnit.graphql'
import statisticEnterpriseUnitIndustryInGovernmentUnit from './queries/statisticEnterpriseUnitIndustryInGovernmentUnit.graphql'
import statisticEnterpriseUnitRegionInGovernmentUnit from './queries/statisticEnterpriseUnitRegionInGovernmentUnit.graphql'
import statisticEnterpriseUnitTypeInGovernmentUnit from './queries/statisticEnterpriseUnitTypeInGovernmentUnit.graphql'

export {
  getAdminInfoInSubProject,
  getBankInSubProject,
  getBusinessDataDictionaryInSubProject,
  getEnterpriseUnitAdminInfoInMyself,
  getEnterpriseUnitAdminInfoInSubProject,
  getEnterpriseUnitCountInGovernmentUnit,
  getEnterpriseUnitCountInSubProject,
  getEnterpriseUnitInfoInMyself,
  getEnterpriseUnitInfoInSubProject,
  getGovernmentUnitAdminInfoInMyself,
  getGovernmentUnitAdminInfoInSubProject,
  getGovernmentUnitInfoInMyself,
  getGovernmentUnitInfoInSubProject,
  getNewsCategoryId,
  getNewsDetail,
  getRegionInSubProject,
  getRootNewsCategory,
  getRootNewsCategoryById,
  getUserInSubProject,
  listBusinessDataDictionaryInSubProject,
  listChildNewsCategory,
  listChildRegionInSubProject,
  listRegionByCodeInSubProject,
  listRegionByCodePathInSubProject,
  listRegionInSubProject,
  listRootNewsCategory,
  pageBankInSubProject,
  pageBusinessDataDictionaryInSubProject,
  pageCompleteNews,
  pageEnterpriseUnitAdminInfoInGovernmentUnit,
  pageEnterpriseUnitAdminInfoInMyself,
  pageEnterpriseUnitAdminInfoInSubProject,
  pageEnterpriseUnitInfoInGovernmentUnit,
  pageEnterpriseUnitInfoInServicer,
  pageEnterpriseUnitInfoInSubProject,
  pageGovernmentUnitAdminInfoInGovernmentUnit,
  pageGovernmentUnitAdminInfoInMyself,
  pageGovernmentUnitAdminInfoInSubProject,
  pageGovernmentUnitInfoInEnterpriseUnit,
  pageGovernmentUnitInfoInGovernmentUnit,
  pageGovernmentUnitInfoInSubProject,
  pageRatingAgenciesUnitInfoInGovernmentUnit,
  pageSimpleNews,
  pageUserInfoInGeneral,
  statisticEnterpriseUnitGroupByTimeInGovernmentUnit,
  statisticEnterpriseUnitIndustryInGovernmentUnit,
  statisticEnterpriseUnitRegionInGovernmentUnit,
  statisticEnterpriseUnitTypeInGovernmentUnit
}
