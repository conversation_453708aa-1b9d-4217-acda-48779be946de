"""独立部署的微服务,K8S服务名:ms-data-export-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""@description 合同物品使用情况——超管导出
		<AUTHOR>
		@date 11:54 2022/7/19
		@param request
		@return {@link }
	"""
	adminEexportEpelcpContractProductStatistic(request:EpelcpContractProductRequest):Boolean!
	"""导出企业下合同签订附件-合同文件
		@param request
		@return
	"""
	exportContractFileInEnterprise(request:ElectronicLaborContractRecordRequest):Boolean!
	"""导出企业下合同签订记录列表
		@param request
		@return
	"""
	exportContractRecordInEnterprise(request:ElectronicLaborContractRecordRequest):Boolean!
	"""导出服务商下合同签订记录列表
		@param request
		@return
	"""
	exportContractRecordInServer(request:ElectronicLaborContractRecordRequest):Boolean!
	"""导出子项目下合同签订记录列表
		@param request
		@return
	"""
	exportContractRecordInSubProject(request:ElectronicLaborContractRecordRequest):Boolean!
	"""发票导出"""
	exportOfflineInvoiceInSubProject(request:OfflineInvoiceExportRequest):Boolean!
	"""发票导出——服务商导出"""
	exportOfflineInvoiceInSubProjectByService(request:OfflineInvoiceExportParm):Boolean!
	"""导出个人订单"""
	exportOrderExcelInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出个人订单"""
	exportOrderExcelInSubProject(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出个人对账"""
	exportReconciliationExcelInServicer(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出个人对账"""
	exportReconciliationExcelInSubProject(request:OrderRequest,sort:[OrderSortRequest]):Boolean!
	"""导出个人退货单"""
	exportReturnOrderExcelInServicer(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出个人退货单"""
	exportReturnOrderExcelInSubProject(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出个人报名退货对账"""
	exportReturnReconciliationExcelInServicer(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出个人报名退货对账"""
	exportReturnReconciliationExcelInSubProject(request:ReturnOrderRequest,sort:[ReturnSortRequest]):Boolean!
	"""导出服务商收益统计报表"""
	exportServicerProfitReportExcelInSubProject(request:TradeReportRequest):Boolean!
	"""@description 合同物品使用情况——服务商导出
		<AUTHOR>
		@date 11:54 2022/7/19
		@param request
		@return {@link }
	"""
	servicEexportEpelcpContractProductStatistic(request:EpelcpContractProductRequest):Boolean!
}
"""范围查询条件
	<AUTHOR>
	@version 1.0
	@date 2022/5/7 15:34
"""
input DateScopeExcelRequest @type(value:"com.fjhb.ms.data.export.common.request.DateScopeExcelRequest") {
	"""result >= begin"""
	begin:DateTime
	"""result <= end"""
	end:DateTime
}
"""合同签订记录Request"""
input ElectronicLaborContractRecordRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.contract.executors.request.ElectronicLaborContractRecordRequest") {
	"""合同信息"""
	contractInfo:ContractInfoRequest
	"""甲方信息"""
	partyA:PartyAInfoRequest
	"""乙方信息"""
	partyB:PartyBInfoRequest
	"""来源信息"""
	sourceInfo:SourceInfoRequest
	"""合同id"""
	contractId:[String]
	"""签订信息"""
	contractSigningInfo:ContractSigningInfoRequest
	"""合同服务状态
		@see ContractServiceStatus
		1.待履约
		2.履约中
		3.已终止
	"""
	contractServiceStatus:[Int]
	jobName:String
}
"""合同信息Request"""
input ContractInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.contract.executors.request.nested.ContractInfoRequest") {
	"""合同名称"""
	contractName:String
	"""第三方合同唯一标识"""
	contractThirdId:String
	"""合同发起时间Start"""
	contractSignStartTimeStart:DateTime
	"""合同发起时间End"""
	contractSignStartTimeEnd:DateTime
	"""合同完成时间Start"""
	contractSignEndTimeStart:DateTime
	"""合同完成时间End"""
	contractSignEndTimeEnd:DateTime
	"""合同生效时间Start"""
	contractServerStartTimeStart:DateTime
	"""合同生效时间End"""
	contractServerStartTimeEnd:DateTime
	"""合同截止时间Start"""
	contractServerEndTimeStart:DateTime
	"""合同截止时间End"""
	contractServerEndTimeEnd:DateTime
	"""合同服务提供商id"""
	contractServerId:String
	"""合同类型
		@see ContractTypes
		0.固定期限劳动合同
		1.无固定期限劳动合同
		2.以完成一定任务为期限劳动合同
	"""
	contractType:[Int]
	"""距离服务到期时间 单位/天"""
	rangeServerEndDay:Int
}
"""合同签订信息Request"""
input ContractSigningInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.contract.executors.request.nested.ContractSigningInfoRequest") {
	"""签订状态
		@see SignedStatus
		1.新订
		2.续订
	"""
	signedStatus:Int
	"""是否续签"""
	whetherToRenew:Boolean
}
"""甲方信息Request"""
input PartyAInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.contract.executors.request.nested.PartyAInfoRequest") {
	"""单位id"""
	unitId:String
	"""发起人id"""
	initiatorId:String
	"""地区路径"""
	regionPath:[String]
	"""签署人姓名"""
	signatoryName:String
}
"""乙方用户信息Request"""
input PartyBInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.contract.executors.request.nested.PartyBInfoRequest") {
	"""用户名称"""
	userName:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
}
"""来源信息Request"""
input SourceInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.contract.executors.request.nested.SourceInfoRequest") {
	"""订单号"""
	orderNo:String
	"""商品id"""
	productId:String
}
input EpelcpContractProductRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.epelcpcontractproduct.request.EpelcpContractProductRequest") {
	"""商品id"""
	commoditySkuId:[String]
	"""套餐类型
		single单份  combo 套餐
	"""
	comboType:String
	"""交易成功开始时间"""
	tradeBeginTime:DateTime
	"""交易结束时间"""
	tradeEndTime:DateTime
	"""企业id"""
	enterpriseIds:[String]
	"""企业地区"""
	region:[String]
	"""服务提供商id"""
	contractServerIds:[String]
}
"""线下发票查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceExportParm @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.invoice.executors.request.OfflineInvoiceExportParm") {
	"""发票ID集合"""
	invoiceIdList:[String]
	"""发票基本信息"""
	basicData:OfflineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfo:InvoiceAssociationInfoRequest
	"""发票配送信息"""
	invoiceDeliveryInfo:OfflineInvoiceDeliveryInfoRequest
	"""归属信息"""
	owner:OwnerKParam
	jobName:String
}
"""配送地址信息
	<AUTHOR>
	@date 2022/05/07
"""
input DeliveryAddressRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.invoice.executors.request.nested.DeliveryAddressRequest") {
	"""收件人"""
	consignee:String
}
"""配送状态变更时间查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input DeliveryStatusChangeTimeRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.invoice.executors.request.nested.DeliveryStatusChangeTimeRequest") {
	"""未就绪"""
	unReady:DateScopeExcelRequest
	"""已就绪"""
	ready:DateScopeExcelRequest
	"""已配送"""
	shipped:DateScopeExcelRequest
	"""已自取"""
	taken:DateScopeExcelRequest
}
"""快递信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input ExpressRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.invoice.executors.request.nested.ExpressRequest") {
	"""快递单号"""
	expressNo:String
}
"""发票开票状态变更时间记录查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input InvoiceBillStatusChangTimeRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.invoice.executors.request.nested.InvoiceBillStatusChangTimeRequest") {
	"""发票申请开票时间"""
	unBillDateScope:DateScopeExcelRequest
	"""发票开票时间"""
	successDateScope:DateScopeExcelRequest
}
"""线下发票基本信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceBasicDataRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.invoice.executors.request.nested.OfflineInvoiceBasicDataRequest") {
	"""发票类型
		1:电子发票 2:纸质发票
		@see InvoiceTypes
	"""
	invoiceTypeList:[Int]
	"""发票种类
		1:普通发票 2:增值税普通发票 3:增值税专用发票
		@see InvoiceCategories
	"""
	invoiceCategory:[Int]
	"""发票状态
		1:正常
		2:作废
		@see InvoiceStatus
	"""
	invoiceStatus:Int
	"""发票状态变更时间记录"""
	invoiceStatusChangeTime:InvoiceStatusChangeTimeRequest
	"""发票开票状态
		0:未开具 1：开票中 2：开票成功 3：开票失败
		@see InvoiceBillStatus
	"""
	billStatusList:[Int]
	"""发票开票状态变更时间记录"""
	billStatusChangTime:InvoiceBillStatusChangTimeRequest
	"""发票是否冻结"""
	freeze:Boolean
	"""发票号集合"""
	invoiceNoList:[String]
	"""商品id集合"""
	commoditySkuIdList:[String]
}
"""线下发票配送信息
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceDeliveryInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.invoice.executors.request.nested.OfflineInvoiceDeliveryInfoRequest") {
	"""配送状态
		0:未就绪 1：已就绪 2：已自取 3：已配送
		@see OfflineDeliveryStatus
	"""
	deliveryStatusList:[Int]
	"""配送状态变更时间记录
		0:未就绪 1：已就绪 2：已自取 3：已配送
		key值 {@link OfflineDeliveryStatus}
	"""
	deliveryStatusChangeTime:DeliveryStatusChangeTimeRequest
	"""配送方式
		0:无 1：自取 2：快递
		@see OfflineShippingMethods
	"""
	shippingMethodList:[Int]
	"""快递信息"""
	express:ExpressRequest
	"""自取信息"""
	takeResult:TakeResultRequest
	"""配送地址信息"""
	deliveryAddress:DeliveryAddressRequest
}
"""取件信息查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input TakeResultRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.invoice.executors.request.nested.TakeResultRequest") {
	"""领取人"""
	takePerson:String
}
"""线下发票查询参数
	<AUTHOR>
	@date 2022/04/06
"""
input OfflineInvoiceExportRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.invoice.request.OfflineInvoiceExportRequest") {
	"""发票ID集合"""
	invoiceIdList:[String]
	"""发票基本信息"""
	basicData:OfflineInvoiceBasicDataRequest
	"""发票关联订单查询参数"""
	associationInfo:InvoiceAssociationInfoRequest
	"""发票配送信息"""
	invoiceDeliveryInfo:OfflineInvoiceDeliveryInfoRequest
	jobName:String
}
"""商品查询条件
	<AUTHOR>
	@date 2022/01/25
"""
input CommoditySkuRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.common.request.CommoditySkuRequest") {
	"""商品id"""
	commoditySkuIdList:[String]
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyRequest
}
"""商品sku属性查询参数
	<AUTHOR>
"""
input SkuPropertyRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.common.request.skuProperty.SkuPropertyRequest") {
	"""套餐类型
		<p> single：单份 combo：套餐
	"""
	comboType:[String]
}
"""订单查询参数
	<AUTHOR>
	@date 2022/01/26
"""
input OrderRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.order.request.OrderRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""子订单号集合"""
	subOrderNoList:[String]
	"""子订单退货状态
		0: 未退货
		1: 退货申请中
		2: 退货中
		3: 退货成功
		4: 退款中
		5: 退款成功
		@see SubOrderReturnStatus
	"""
	subOrderReturnStatus:[Int]
	"""订单基本信息查询参数"""
	orderBasicData:OrderBasicDataRequest
	"""订单支付信息查询"""
	payInfo:OrderPayInfoRequest
	"""单位买家单位ID"""
	unitBuyerUnitIdList:[String]
	"""发货商品信息"""
	deliveryCommodity:CommoditySkuRequest
	"""订单服务商信息"""
	orderServicers:OrderServicersRequest
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderSortRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.order.request.OrderSortRequest") {
	"""需要排序的字段"""
	field:OrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""订单基本信息查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input OrderBasicDataRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.order.request.nested.OrderBasicDataRequest") {
	"""订单类型
		1:常规订单 2:批次关联订单
		@see com.fjhb.domain.trade.api.order.consts.OrderTypes
	"""
	orderType:Int
	"""批次单号"""
	batchOrderNoList:[String]
	"""订单状态
		<br> 1:正常 2：交易完成 3：交易关闭
		@see OrderStatus
	"""
	orderStatusList:[Int]
	"""订单支付状态
		<br> 0:未支付 1：支付中 2：已支付
		@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
	"""
	orderPaymentStatusList:[Int]
	"""订单发货状态
		<br> 0:未发货 1：发货中 2：已发货
		@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
	"""
	orderDeliveryStatusList:[Int]
	"""订单状态变更时间"""
	orderStatusChangeTime:OrderStatusChangeTimeRequest
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelTypesList:[Int]
	"""终端
		<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see PurchaseChannelTerminalCodes
	"""
	terminalCodeList:[String]
	"""订单价格范围
		<br> 查询非0元订单 begin填0.01
	"""
	orderAmountScope:BigDecimalScopeRequest
}
"""订单支付信息相关查询参数
	<AUTHOR>
	@date 2022/01/27
"""
input OrderPayInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.order.request.nested.OrderPayInfoRequest") {
	"""收款账号ID"""
	receiveAccountIdList:[String]
	"""交易流水号"""
	flowNoList:[String]
	"""付款类型
		1: 线上付款单
		2: 线下付款单
		3: 无需付款的付款单
	"""
	paymentOrderTypeList:[Int]
}
"""订单服务商查询参数
	<AUTHOR>
"""
input OrderServicersRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.order.request.nested.OrderServicersRequest") {
	"""合同签订服务商ID"""
	contractProviderIdList:[String]
}
"""订单状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input OrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.order.request.nested.OrderStatusChangeTimeRequest") {
	"""订单处于正常状态时间范围(创建时间范围)"""
	normalDateScope:DateScopeRequest
	"""订单创建时间范围"""
	completedDatesScope:DateScopeRequest
}
"""退货单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.returnorder.request.ReturnOrderRequest") {
	"""退货单号"""
	returnOrderNoList:[String]
	"""基本信息"""
	basicData:ReturnOrderBasicDataRequest
	"""审批信息"""
	approvalInfo:ReturnOrderApprovalInfoRequest
	"""退货商品id集合"""
	returnCommoditySkuIdList:[String]
	"""退货商品sku属性"""
	skuProperty:SkuPropertyRequest
	"""退款商品id集合"""
	refundCommoditySkuIdList:[String]
	"""退货单关联子订单查询参数"""
	subOrderInfo:SubOrderInfoRequest
}
"""订单排序参数
	<AUTHOR>
	@date 2022/01/27
"""
input ReturnSortRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.returnorder.request.ReturnSortRequest") {
	"""需要排序的字段"""
	field:ReturnOrderSortField
	"""正序或倒序"""
	policy:SortPolicy
}
"""退货单关联子订单的主订单查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input OrderInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.returnorder.request.nested.OrderInfoRequest") {
	"""订单号集合"""
	orderNoList:[String]
	"""关联批次单号"""
	batchOrderNoList:[String]
	"""单位买家单位ID集合"""
	unitBuyerUnitIdList:[String]
	"""收款账号ID"""
	receiveAccountIdList:[String]
	"""原始订单交易流水号"""
	flowNoList:[String]
	"""购买渠道
		<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
		@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
	"""
	channelTypesList:[Int]
	"""终端
		<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
		@see PurchaseChannelTerminalCodes
	"""
	terminalCodeList:[String]
}
"""退货单关闭信息
	<AUTHOR>
	@date 2022年4月11日 11:33:35
"""
input ReturnCloseReasonRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.returnorder.request.nested.ReturnCloseReasonRequest") {
	"""退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
		@see ReturnOrderCloseTypes
	"""
	closeTypeList:[Int]
}
"""退货单审批信息查询参数
	<AUTHOR>
	@date 2022/03/18
"""
input ReturnOrderApprovalInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.returnorder.request.nested.ReturnOrderApprovalInfoRequest") {
	"""审批时间"""
	approveTime:DateScopeRequest
}
"""退货单基本信息查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderBasicDataRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.returnorder.request.nested.ReturnOrderBasicDataRequest") {
	"""退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)"""
	returnOrderStatus:[Int]
	"""退货单申请来源类型
		SUB_ORDER
		BATCH_RETURN_ORDER
		@see ReturnOrderApplySourceTypes
	"""
	applySourceType:String
	"""来源ID集合"""
	applySourceIdList:[String]
	"""退货单关闭信息"""
	returnCloseReason:ReturnCloseReasonRequest
	"""退货单状态变更时间"""
	returnStatusChangeTime:ReturnOrderStatusChangeTimeRequest
	"""退款金额范围
		<br> 查询非0元  begin填0.01
	"""
	refundAmountScope:BigDecimalScopeRequest
}
"""退货单状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/24
"""
input ReturnOrderStatusChangeTimeRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.returnorder.request.nested.ReturnOrderStatusChangeTimeRequest") {
	"""申请退货时间"""
	applied:DateScopeRequest
	"""退货单完成时间
		<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
	"""
	returnCompleted:DateScopeRequest
}
"""<AUTHOR>
	@date 2022/03/24
"""
input SubOrderInfoRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.returnorder.request.nested.SubOrderInfoRequest") {
	"""子订单号集合"""
	subOrderNoList:[String]
	"""订单查询参数"""
	orderInfo:OrderInfoRequest
}
"""商品开通统计报表查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input TradeReportRequest @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.tradereport.request.TradeReportRequest") {
	"""交易时间范围"""
	tradeTime:DateScopeRequest
	"""买家所在地区路径"""
	buyerAreaPath:[String]
	"""商品查询条件"""
	commoditySku:CommoditySkuRequest1
}
"""商品查询参数
	<AUTHOR>
	@date 2022/05/11
"""
input CommoditySkuRequest1 @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.tradereport.request.nested.CommoditySkuRequest") {
	"""商品Sku名称"""
	saleTitle:String
	"""商品sku属性查询"""
	skuProperty:SkuPropertyRequest
}
"""发票关联订单查询参数
	<AUTHOR>
	@date 2022/3/18
"""
input InvoiceAssociationInfoRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.InvoiceAssociationInfoRequest") {
	"""关联订单类型
		0:订单号
		1:批次单号
		@see AssociationTypes
	"""
	associationType:Int
	"""订单号 | 批次单号"""
	associationIdList:[String]
	"""买家信息"""
	buyerIdList:[String]
	"""企业id"""
	unitBuyerUnitIdList:[String]
	"""收款账号"""
	receiveAccountIdList:[String]
}
"""发票状态变更时间查询参数
	<AUTHOR>
	@date 2022/03/22
"""
input InvoiceStatusChangeTimeRequest @type(value:"com.fjhb.ms.data.export.service.invoice.queryRequest.InvoiceStatusChangeTimeRequest") {
	"""正常"""
	normal:DateScopeExcelRequest
	"""作废"""
	invalid:DateScopeExcelRequest
}
input BigDecimalScopeRequest @type(value:"com.fjhb.ms.query.commons.BigDecimalScopeRequest") {
	begin:BigDecimal
	end:BigDecimal
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input OwnerKParam @type(value:"com.fjhb.ms.trade.query.kernel.service.common.param.OwnerKParam") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	rootAccountId:String
	servicerType:Int
	servicerId:String
}
"""排序参数
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum SortPolicy @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.common.request.SortPolicy") {
	"""正序"""
	ASC
	"""倒序"""
	DESC
}
"""订单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum OrderSortField @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.order.request.nested.OrderSortField") {
	"""订单创建时间"""
	ORDER_NORMAL_TIME
	"""订单交易完成时间"""
	ORDER_COMPLETED_TIME
}
"""退货单可用于排序的属性
	<AUTHOR> xmj
	@date : 2022/01/27
"""
enum ReturnOrderSortField @type(value:"com.fjhb.ms.data.export.front.gateway.ldhtqdpt.trade.returnorder.request.nested.ReturnOrderSortField") {
	"""退货单申请时间"""
	APPLIED_TIME
}

scalar List
