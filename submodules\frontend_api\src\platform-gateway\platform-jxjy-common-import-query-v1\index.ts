import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-jxjy-common-import-query-v1'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjy-common-import-query-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 导出任务数据地址请求
 */
export class ExportTaskDataRequest {
  /**
   * 任务编号
   */
  taskId: string
}

/**
 * <AUTHOR>
@date 2022/12/29 14:02
 */
export class QueryImportTemplateRequest {
  taskCategory?: string
}

/**
 * 查询批量任务的执行情况请求
<AUTHOR>
@since 2022/5/19
 */
export class QueryForTaskInfoRequest {
  /**
   * 代理账户TOKEN
   */
  token?: string
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 任务创建起始时间
   */
  createStartTime?: string
  /**
   * 任务创建截止时间
   */
  createEndTime?: string
  /**
   * 起始执行时间
   */
  executeStartTime?: string
  /**
   * 结束执行时间
   */
  executeEndTime?: string
  /**
   * 任务类型
   */
  taskCategory?: string
}

/**
 * 各状态及执行结果对应数量
<AUTHOR>
 */
export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

/**
 * 导出任务数据地址响应
<AUTHOR>
@date 2022/11/7 14:12
 */
export class ExportTaskDataResponse {
  /**
   * 文件名称
   */
  fileName: string
  /**
   * 文件路径
   */
  filePath: string
}

/**
 * 执行情况信息请求响应体
<AUTHOR>
@since 2022/5/19
 */
export class QueryTaskInfoResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务分类
   */
  category: string
  /**
   * 所属批次单编号
   */
  batchNo: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 就绪时间
   */
  alreadyTime: string
  /**
   * 执行时间
   */
  executingTime: string
  /**
   * 完成时间
   */
  completedTime: string
  /**
   * 创建人id（操作人编号）
   */
  createUserId: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
  /**
   * 处理总条数
   */
  totalCount: number
  /**
   * 成功条数
   */
  successCount: number
  /**
   * 失败条数
   */
  failCount: number
  /**
   * 导入进度 1-导入成功 2-导入中 3-导入失败 4-部分成功
   */
  importProgress: number
}

export class QueryTaskInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<QueryTaskInfoResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询导入/导出任务执行情况
   * @param request 任务查询参数
   * @param page    分页数据
   * @return 任务执行情况分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryForTask(
    params: { request?: QueryForTaskInfoRequest; page?: Page },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryForTask,
    operation?: string
  ): Promise<Response<QueryTaskInfoResponsePage>> {
    return commonRequestApi<QueryTaskInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询导入模板
   * @param request 任务类型
   * @return 模板地址
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryImportTemplate(
    request: QueryImportTemplateRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.queryImportTemplate,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出全部数据
   * @param request 导出异常数据请求
   * @return 导出异常数据响应
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportAll(
    request: ExportTaskDataRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.exportAll,
    operation?: string
  ): Promise<Response<ExportTaskDataResponse>> {
    return commonRequestApi<ExportTaskDataResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出异常数据
   * @param request 导出异常数据请求
   * @return 导出异常数据响应
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportFail(
    request: ExportTaskDataRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.exportFail,
    operation?: string
  ): Promise<Response<ExportTaskDataResponse>> {
    return commonRequestApi<ExportTaskDataResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
