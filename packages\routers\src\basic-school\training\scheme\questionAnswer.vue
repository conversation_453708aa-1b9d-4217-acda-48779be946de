<route-meta>
{
"isMenu": true,
"hideMenu": true,
"title": "问卷答题情况",
"sort": 2,
"icon": "icon-zixun"
}
</route-meta>
<script lang="ts">
  import QuestionAnswer from '@hbfe/jxjy-admin-scheme/src/implementingManagement/questionAnswer.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    questionAnswer: [WXGLY]
  })
  export default class extends QuestionAnswer {}
</script>
