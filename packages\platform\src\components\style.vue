<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: chenweinian
 * @LastEditTime: 2024-07-30 10:55:12
-->
<template>
  <el-card shadow="never" class="m-card f-mb15" v-if="$hasPermission('style')" desc="风格设置" actions="created">
    <div class="f-pb30 m-style-set">
      <div class="m-tit">
        <span class="tit-txt">主题色设置</span>
      </div>
      <div></div>
      <div class="style-bd">
        <div class="f-flex" v-for="item in appList" :key="item" @click="choice(item)">
          <div class="item selected" :style="{ background: item }">
            <i class="el-icon-check" v-if="mark == item"></i>
          </div>
          <!--选中样式-->
        </div>
      </div>
      <template v-if="$hasPermission('editStyle')" desc="风格设置（编辑）" actions="save">
        <div class="m-btn-bar f-mt10 f-tc">
          <el-button @click="canle">取消</el-button>
          <el-button type="primary" @click="save">保存</el-button>
        </div>
      </template>
    </div>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import { ThemeColorEnum } from '@api/service/common/template-school/enums/ThemeColorEnum'

  @Component
  export default class extends Vue {
    mutationTheme = OnlineSchoolConfigModule.mutationTheme
    // appList = ['#f53c3c', '#008a6c', '#1f5cc7'] as any
    appList = [] as any
    colorList = ['#f53c3c ', '#008a6c', '#f0d51f', '#2ed65e', '#1fcbf0', '#1f86f0', '#9a1ff0']
    mark = ''

    async created() {
      await this.mutationTheme.queryList()
      this.appList = this.mutationTheme.themeColorList
      await this.mutationTheme.queryDetail()
      this.mark = this.mutationTheme.themeColor
      // 适配旧红色颜色值
      if (this.mutationTheme.themeColor === ThemeColorEnum.OLDRED) {
        this.mark = ThemeColorEnum.REDTHEME
      }
    }

    choice(item: any) {
      console.log(item)
      this.mark = item
    }

    canle() {
      this.$confirm(`确定放弃编辑吗？`, '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        showCancelButton: true,
        type: 'warning'
      })
        .then(async () => {
          this.mark = this.mutationTheme.themeColor
          // 适配旧红色颜色值
          if (this.mutationTheme.themeColor === ThemeColorEnum.OLDRED) {
            this.mark = ThemeColorEnum.REDTHEME
          }
        })
        .catch(() => {
          //
        })
    }

    save() {
      this.$confirm(`更改颜色将同步生效于门户、学员中心移动端页面。确认更改？`, '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确定',
        showCancelButton: true,
        type: 'warning'
      })
        .then(async () => {
          console.log(11)
          this.mutationTheme.themeColor = this.mark
          const res = (await this.mutationTheme.doSave()) as any
          if (res.code == 200) {
            this.$message.success('成功！')
          } else this.$message.warning(res.message)
        })
        .catch(() => {
          //
        })
    }
  }
</script>
