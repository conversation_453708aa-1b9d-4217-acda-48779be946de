<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="注册登录" name="first">详见 0301_功能设置_注册登录.vue</el-tab-pane>
      <el-tab-pane label="集体报名" name="second">详见 0303_功能设置_集体报名.vue</el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="third">详见 0304_功能设置_增值税发票.vue</el-tab-pane>
      <el-tab-pane label="培训证明" name="fourth">详见 0305_功能设置_培训证明.vue</el-tab-pane>
      <el-tab-pane label="门户精品课程" name="five">详见 0307_功能设置_门户精品课程.vue</el-tab-pane>
      <el-tab-pane label="学习规则" name="sixth">
        <div class="f-p15">
          <el-button type="primary" class="f-mb15">添加补学规则</el-button>
          <el-card shadow="never" class="m-card">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb15 f-mb15">
              开启学习规则后，学员合格时会判断培训班是否有是学习规则，若有配置规则并且学习记录不符合要求时，会根据配置内容重新计算学习记录。
            </el-alert>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="140">
                <template>TradeCode21</template>
              </el-table-column>
              <el-table-column label="适用范围" min-width="150">
                <template>培训方案级别<a href="#" class="f-link f-cb">（1）</a></template>
              </el-table-column>
              <el-table-column label="适用行业" min-width="180">
                <template>人社行业、建设行业</template>
              </el-table-column>
              <el-table-column label="指定年度" min-width="180">
                <template>2019、2020、2021</template>
              </el-table-column>
              <el-table-column label="状态" min-width="100">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-badge is-dot type="info" class="badge-status">停用</el-badge>
                  </div>
                  <div v-else>
                    <el-badge is-dot type="success" class="badge-status">启用</el-badge>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="更新时间" min-width="150">
                <template>2016-09-21 08:50:08</template>
              </el-table-column>
              <el-table-column label="操作人" min-width="100">
                <template>admin</template>
              </el-table-column>
              <el-table-column label="操作" min-width="130" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text" size="mini">启用</el-button>
                    <el-button type="text" size="mini">详情</el-button>
                    <el-button type="text" size="mini">编辑</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text" size="mini">停用</el-button>
                    <el-button type="text" size="mini">详情</el-button>
                    <el-button type="text" size="mini">编辑</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'sixth',
        activeName1: 'first',
        activeName2: 'first',
        radio: 3,
        radio1: 6,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
