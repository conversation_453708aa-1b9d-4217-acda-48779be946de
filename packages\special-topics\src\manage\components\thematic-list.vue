<template>
  <el-card shadow="never" class="m-card f-mb15">
    <!--条件查询-->
    <hb-search-wrapper :model="thematicManagementList.queryParam" @reset="reset">
      <el-form-item label="专题名称">
        <el-input v-model="thematicManagementList.queryParam.subjectName" clearable placeholder="请输入专题名称" />
      </el-form-item>
      <el-form-item label="专题入口名称">
        <el-input v-model="thematicManagementList.queryParam.entryName" clearable placeholder="请输入专题入口名称" />
      </el-form-item>

      <el-form-item label="专题类型">
        <el-select
          v-model="thematicManagementList.queryParam.subjectType"
          multiple
          clearable
          placeholder="请选择专题类型"
        >
          <el-option label="地区" :value="1"></el-option>
          <el-option label="行业" :value="2"></el-option>
          <el-option label="单位" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="isUnitSelected" label="所属单位">
        <el-input v-model="thematicManagementList.queryParam.unitName" clearable placeholder="请输入单位名称" />
      </el-form-item>
      <el-form-item v-if="isIndustrySelected" label="行业：">
        <industry-select
          v-model="thematicManagementList.queryParam.suiteIndustry"
          @industryInfos="handleIndustryInfos"
        ></industry-select>
      </el-form-item>
      <el-form-item v-if="isRegionSelected" label="地区">
        <biz-national-region
          v-model="thematicManagementList.queryParam.suiteAreaLists"
          :check-strictly="true"
          placeholder="请选择地区"
        ></biz-national-region>
      </el-form-item>

      <el-form-item label="专题状态">
        <el-select v-model="thematicManagementList.queryParam.enable" clearable placeholder="请选择专题状态">
          <el-option :value="false" label="停用"></el-option>
          <el-option :value="true" label="启用"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="显示在网校">
        <el-select
          v-model="thematicManagementList.queryParam.displayInSchool"
          clearable
          placeholder="请选择是否显示在网校"
        >
          <el-option label="显示" :value="true"></el-option>
          <el-option label="不显示" :value="false"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="编辑时间">
        <double-date-picker
          :begin-create-time.sync="thematicManagementList.queryParam.modifyStartTime"
          :end-create-time.sync="thematicManagementList.queryParam.modifyEndTime"
          begin-time-placeholder="开始时间"
          end-time-placeholder="结束时间"
        ></double-date-picker>
      </el-form-item>
      <template slot="actions">
        <el-button type="primary" :loading="thematicLoading" @click="searchList">查询</el-button>
        <el-button type="primary" :loading="exportListLoaing" @click="exportList">导出</el-button>
      </template>
    </hb-search-wrapper>

    <!--表格-->
    <el-table stripe ref="topicsListRef" :data="topicsList" class="m-table is-statistical" v-loading="loading">
      <el-table-column type="index" label="No." width="60" align="center">
        <!-- <template slot-scope="scope">
                  <span :data-index="scope.$index + 1" v-observe-visibility="visibilityConfig">{{
                    scope.$index + 1
                  }}</span>
                </template> -->
      </el-table-column>
      <template v-if="$hasPermission('sortWx')" desc="网校排序" actions="dragElement">
        <template v-if="!isZtGly">
          <el-table-column label="排序" min-width="65" align="center">
            <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template> </el-table-column
        ></template>
      </template>
      <el-table-column label="专题名称" min-width="220">
        <template slot-scope="scope"
          ><template v-if="$hasPermission('visibilityConfig')" desc="瀑布加载" actions="visibilityConfig"
            ><span :data-index="scope.$index + 1" v-observe-visibility="visibilityConfig"></span
            >{{ scope.row.basicInfo.subjectName }}</template
          ></template
        >
      </el-table-column>
      <el-table-column label="专题入口名称" min-width="140">
        <template slot-scope="scope">
          <el-tooltip placement="top" effect="light">
            <div slot="content">{{ scope.row.basicInfo.entryName }}</div>
            <el-button type="text" class="f-to-three"
              ><i class="f-c4">{{ scope.row.basicInfo.entryName }}</i></el-button
            >
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="专题类型" min-width="160">
        <template slot-scope="scope">
          <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.suiteIndustry">
            <div slot="content">
              <i class="f-c4">
                <el-tag type="warning" size="mini" class="f-mr5">行业</el-tag>{{ scope.row.basicInfo.suiteIndustry }}</i
              >
            </div>
            <el-button type="text" class="f-to-three">
              <i class="f-c4">
                <el-tag type="warning" size="mini" class="f-mt5"> 行业 </el-tag>
                {{ scope.row.basicInfo.suiteIndustry }}</i
              >
            </el-button>
          </el-tooltip>
          <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.suiteArea">
            <div slot="content">
              <i class="f-c4"
                ><el-tag type="warning" size="mini" class="f-mr5">地区</el-tag>{{ scope.row.basicInfo.suiteArea }}</i
              >
            </div>
            <el-button type="text" class="f-to-three"
              ><i class="f-c4"
                ><el-tag type="warning" size="mini" class="f-mt5">地区</el-tag>{{ scope.row.basicInfo.suiteArea }}</i
              ></el-button
            >
          </el-tooltip>
          <el-tooltip placement="top" effect="light" v-if="scope.row.basicInfo.unitName">
            <div slot="content">
              <i class="f-c4">
                <el-tag type="success" size="mini" class="f-mr5">单位</el-tag>{{ scope.row.basicInfo.unitName }}</i
              >
            </div>
            <el-button type="text" class="f-to-three">
              <i class="f-c4">
                <el-tag type="success" size="mini" class="f-mt5"> 单位 </el-tag>
                {{ scope.row.basicInfo.unitName }}</i
              >
            </el-button>
          </el-tooltip>
        </template>
      </el-table-column>
      <el-table-column label="显示在网校" min-width="110" align="center">
        <template slot-scope="scope">{{ scope.row.basicInfo.displayInSchool ? '显示' : '不显示' }}</template>
      </el-table-column>
      <el-table-column label="已配置方案数" min-width="120" align="center">
        <template slot-scope="scope"
          ><el-button type="text" size="mini" @click="checkHasTraining(scope.row.topicID)">{{
            scope.row.selectedTrainingPlanCount
          }}</el-button></template
        >
      </el-table-column>
      <el-table-column label="状态" min-width="90">
        <template slot-scope="scope">
          <div v-if="scope.row.enable">
            <el-badge is-dot type="success" class="badge-status">启用</el-badge>
          </div>
          <div v-else>
            <el-badge is-dot type="danger" class="badge-status">停用</el-badge>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="最新编辑时间" min-width="160">
        <template slot-scope="scope">{{ scope.row.lastEditTime ? scope.row.lastEditTime : '---' }}</template>
      </el-table-column>
      <el-table-column label="操作" width="380" align="center">
        <template slot-scope="scope">
          <template v-if="$hasPermission('operation')" desc="操作" actions="turnOn,turnOff,goEdit">
            <el-button type="text" size="mini" @click="turnOn(scope.row)" v-if="!scope.row.enable">启用</el-button>
            <el-button type="text" size="mini" @click="turnOff(scope.row)" v-if="scope.row.enable">停用</el-button>
            <el-button type="text" size="mini" @click="goEdit(scope.row.topicID)">编辑</el-button>
            <el-button disabled type="text" size="mini">日志</el-button></template
          >

          <el-button type="text" size="mini" @click="webLink(scope.row.basicInfo.subjectDomain)">web端链接</el-button>
          <el-button type="text" size="mini" @click="openH5Paper(scope.row.basicInfo.subjectDomain, scope.row.topicID)"
            >H5二维码</el-button
          >
          <el-button type="text" size="mini" :loading="exportThematicLoading" @click="exportCurrent(scope.row)"
            >导出专题方案</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <!--          <hb-pagination :page="page" v-bind="page"></hb-pagination>-->
    <template v-if="$hasPermission('h5Paper')" desc="h5组件" actions="@H5Paper">
      <h5-paper ref="h5PaperRef"></h5-paper
    ></template>
    <template v-if="$hasPermission('hasTraining')" desc="专题选中的方案" actions="@HasTraining">
      <has-training ref="hasTrainingRef"></has-training
    ></template>
  </el-card>
</template>

<script lang="ts">
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import ThematicManagementList from '@api/service/management/thematic-management/ThematicManagementList'
  import ThematicManagementQueryParam from '@api/service/management/thematic-management/model/ThematicManagementQueryParam'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { Page } from '@hbfe/common'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import IndustrySelect from '@hbfe/jxjy-admin-specialTopics/src/add/components/industry-select.vue'
  import H5Paper from '@hbfe/jxjy-admin-specialTopics/src/manage/components/h5-paper.vue'
  import HasTraining from '@hbfe/jxjy-admin-specialTopics/src/manage/components/has-training.vue'
  import { ElTable } from 'element-ui/types/table'
  import Sortable from 'sortablejs'
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import Env from '@api/service/common/utils/Env'
  class UpdateParam {
    id = ''
    sort = 0
  }
  @Component({
    components: { DoubleDatePicker, IndustrySelect, HasTraining, H5Paper }
  })
  export default class extends Vue {
    @Ref('h5PaperRef') h5PaperRef: H5Paper
    @Ref('hasTrainingRef') hasTrainingRef: HasTraining
    @Ref('topicsListRef') topicsListRef: any
    //列表数据
    @Prop({ type: Array, default: () => new Array<ThematicManagementItem>() }) topicsList: Array<ThematicManagementItem>
    @Prop({ type: Page, default: () => new Page() }) page: Page
    @Prop({ type: Boolean, default: false }) thematicLoading: boolean
    thematicManagementList = new ThematicManagementList()
    // page = new Page()
    loading = false
    exportDialog = false //导出成功弹窗
    exportMessage = '专题列表'
    exportEnter = 'exportTrainingChannel' //导出下载入口
    exportListLoaing = false
    exportThematicLoading = false

    isZtGly = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    flag = 0
    //环境单例
    env = Env

    @Watch('thematicManagementList.queryParam', { immediate: true, deep: true })
    paramsWatch(newVal: ThematicManagementQueryParam) {
      if (newVal) {
        this.$emit('queryParamChange', newVal)
      }
    }

    get isRegionSelected() {
      if (this.thematicManagementList.queryParam.subjectType?.includes(1)) {
        return true
      } else {
        this.thematicManagementList.queryParam.suiteAreaLists = new Array<any>()
        return false
      }
    }

    get isIndustrySelected() {
      if (this.thematicManagementList.queryParam.subjectType?.includes(2)) {
        return true
      } else {
        this.thematicManagementList.queryParam.suiteIndustry = null
        return false
      }
    }

    get isUnitSelected() {
      if (this.thematicManagementList.queryParam.subjectType?.includes(3)) {
        return true
      } else {
        this.thematicManagementList.queryParam.unitName = null
        return false
      }
    }

    //查看已选择的培训方案
    checkHasTraining(topicID: string) {
      this.hasTrainingRef.showDialog = true
      this.hasTrainingRef.topicID = topicID
    }

    /**
     * 滑动加载 原理使用 data-set和数量进行比较 //瀑布式加载
     */
    async visibilityConfig(isVisible: boolean, entry: any) {
      if (isVisible) {
        if (entry.target.dataset.index >= this.page.totalSize) {
          //   最大值时不请求
          return
        }
        if (parseInt(entry.target.dataset.index) == this.topicsList.length) {
          this.page.pageNo = this.page.pageNo + 1
          await this.thematicManagementList.queryList(this.page)
          const list = this.thematicManagementList.list
          this.$emit('listChange', this.topicsList.concat(list))
          //   this.topicsList = this.topicsList.concat(list)
          //处理切换页数后行数错位问题
          ;(this.$refs['topicsListRef'] as any)?.doLayout()
        }
      }
    }

    //拖动排序
    async dragElement(table: ElTable) {
      const el = table.$el.querySelector('.el-table__body-wrapper > table > tbody') as HTMLElement
      const mySortable = new Sortable(el, {
        ghostClass: 'blue-background-class',
        handle: '.hb-iconfont',
        onEnd: ({ newIndex, oldIndex }) => {
          const curRow = this.topicsList.splice(oldIndex, 1)[0]
          console.log('列表新旧索引', newIndex, oldIndex)

          this.topicsList.splice(newIndex, 0, curRow)
          let newArray = this.topicsList.slice(0)
          this.topicsList = []
          console.log('newArray', newArray)
          this.$nextTick(async () => {
            newArray = newArray.map((res, index) => {
              res.sort = index + 1
              return res
            })
            this.topicsList = newArray
            this.flag += 1
            //判断当前行的新旧位置,以最大值进行截取,赋sort值(避免出现将考前的数据往下移时sort不生效的问题)
            const underIndex = (oldIndex > newIndex ? oldIndex : newIndex) + 1
            const arr = this.topicsList.slice(0, underIndex)
            const update = new Array<UpdateParam>()
            console.log(arr, '截取的数组')
            arr.forEach((element, index) => {
              update.push({
                id: element.topicID,
                sort: index + 1
              })
            })
            console.log('update更新传参', update)
            this.loading = true
            mySortable.option('disabled', true)
            await this.thematicManagementList.updateSort(update)
            mySortable.option('disabled', false)
            this.loading = false
          })
        }
      })
      return mySortable
    }

    activated() {
      this.$nextTick(async () => {
        console.log('拖动,专题列表ref', this.topicsListRef)
        this.dragElement(this.topicsListRef)
      })
    }

    //启用
    async turnOn(item: ThematicManagementItem) {
      this.$confirm('启用后该专题将展示在网校门户，对应的推广链接和二维码将可正常使用，是否确认启用？', '提示', {
        confirmButtonText: '确认',
        showCancelButton: true,
        type: 'warning'
      }).then(async () => {
        this.loading = true
        await item.enableTopic()
        this.loading = false
      })
    }
    //停用
    async turnOff(item: ThematicManagementItem) {
      this.$confirm('停用后该专题将不显示在网校门户，对应的推广链接和二维码将自动失效，是否确认停用？', '提示', {
        confirmButtonText: '确认',
        showCancelButton: true,
        type: 'warning'
      }).then(async () => {
        this.loading = true
        await item.disableTopic()
        this.loading = false
      })
    }

    //编辑专题
    goEdit(id: string) {
      console.log('去编辑,当前专题id', id)

      this.$router.push({
        path: `/training/special-topics/manage/edit/${id}`
      })
    }

    //web端访问链接
    webLink(url: string) {
      this.$confirm(`${url}${this.env.proxyPortStr}`, 'web端访问专题', {
        confirmButtonText: '复制',
        showCancelButton: true
      }).then(() => {
        const copyUrl = url + this.env.proxyPortStr
        this.copy(copyUrl)
      })
    }

    copy(url: string) {
      const _input = document.createElement('input')
      _input.value = url
      document.body.appendChild(_input)
      _input.select()
      document.execCommand('Copy')
      document.body.removeChild(_input)
      this.$message.success('复制成功')
    }

    // 判断是否需要添加端口号

    //打开h5模板弹窗
    openH5Paper(url: string, topicID: string) {
      this.h5PaperRef.dialog6 = true
      this.h5PaperRef.domainUrl = url
      this.h5PaperRef.topicID = topicID
      this.h5PaperRef.search()
    }

    //导出当前专题
    async exportCurrent(row: ThematicManagementItem) {
      this.$emit('exportCurrent', row)
    }

    //reset重置
    reset() {
      this.$emit('reset')
    }

    handleIndustryInfos(val: any) {
      this.$emit('handleIndustryInfos', val)
    }

    searchList() {
      this.$emit('searchList')
    }

    exportList() {
      this.$emit('exportList')
    }
  }
</script>
