import msTradeConfigurationV1, { InvoiceCategoryConfig1 } from '@api/ms-gateway/ms-trade-configuration-v1'
import msTradeQueryFrontGatewayTradeQueryBackstage, {
  InvoiceConfigResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { PurchaseChannelEnum } from '@api/service/common/enums/trade-configuration/PurchaseChannelType'
import PurchaseChannelTypeVo from '@api/service/management/trade-info-config/mutation/vo/PurchaseChannelTypeVo'
import { ResponseStatus } from '@hbfe/common'
import { cloneDeep } from 'lodash'
import { TerminalTypeEnum } from '../../enums/trade-configuration/TerminalType'
import OfflineReceiveAccountVo from './vo/OfflineReceiveAccountVo'
import orderGraphl from '@api/ms-gateway/ms-order-v1'

class QueryTradeConfig {
  // 购买渠道类型列表
  purchaseChannelList = new Array<PurchaseChannelTypeVo>()

  /**
   * 专票电子票
   * @param type 渠道类型
   * 用户自主购买:1,集体缴费:2,管理员导入:3,集体报名个人缴费渠道:4
   * @returns purchaseChannelList
   */
  dedicatedInvoice(type: number) {
    const res = cloneDeep(this.purchaseChannelList.find(item => item.type == type))
    res.invoiceConfig.allowInvoiceCategoryList = res.invoiceConfig.allowInvoiceCategoryList.filter(
      (item: InvoiceCategoryConfig1) => {
        return item.invoiceCategory == 3
      }
    )
    return res
  }
  /**
   * 普通电子票
   * @param type 渠道类型
   * 用户自主购买:1,集体缴费:2,管理员导入:3,集体报名个人缴费渠道:4
   * @returns purchaseChannelList
   */
  tickets(type: number) {
    const res = cloneDeep(this.purchaseChannelList.find(item => item.type == type))
    res.invoiceConfig.allowInvoiceCategoryList = res.invoiceConfig.allowInvoiceCategoryList.filter(
      (item: InvoiceCategoryConfig1) => {
        return item.invoiceCategory == 2
      }
    )
    return res
  }
  /**
   * 获取当前网校配置的渠道列表
   * @returns purchaseChannelList
   */
  async preparePurchaseChannel(): Promise<ResponseStatus> {
    const msRes = await msTradeConfigurationV1.preparePurchaseChannel()
    if (msRes.status.isSuccess()) {
      this.purchaseChannelList = msRes.data?.purchaseChannelList?.map(PurchaseChannelTypeVo.from)
    }
    return msRes.status
  }

  /**
   * 是否开启补要发票
   * @returns
   */
  async hasOpenInvoice() {
    if (!this.purchaseChannelList.length) {
      await this.preparePurchaseChannel()
    }
    const purchaseChannelId = this.purchaseChannelList?.find(
      purchaseChannel => purchaseChannel.type === PurchaseChannelEnum.STUDENT_PURCHASE
    )?.id
    if (purchaseChannelId) {
      const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.getPurchaseChannel(purchaseChannelId)
      if (msRes.status.isSuccess()) {
        return msRes.data?.invoiceConfig?.openInvoiceType && msRes.data?.invoiceConfig?.allowAskFor
      }
    } else {
      return false
    }
  }

  /**
   * 集体补要发票配置
   * @returns InvoiceConfigResponse
   */
  async hasBatchOpenInvoice(): Promise<InvoiceConfigResponse> {
    if (!this.purchaseChannelList.length) {
      await this.preparePurchaseChannel()
    }
    const purchaseChannelId = this.purchaseChannelList?.find(
      purchaseChannel => purchaseChannel.type === PurchaseChannelEnum.UNIT_PURCHASE
    )?.id
    const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.getPurchaseChannel(purchaseChannelId)
    if (msRes.status.isSuccess()) {
      return msRes.data?.invoiceConfig?.openInvoiceType && msRes.data?.invoiceConfig
    }
  }

  async checkInvoice(batchOrderNo: string) {
    const res = await orderGraphl.batchApplyInvoiceValidate({
      batchOrderNo
    })
    if (res.status.isSuccess() && res.data) {
      return {
        code: res.data.isAllow ? 200 : Number(res.data.code),
        message: res.data.message ?? '校验失败'
      }
    } else {
      return {
        code: 500,
        message: '校验失败'
      }
    }
  }

  /**
   * 集体报名是否开启补要发票
   * @returns
   */
  async hasBatchOpenInvoiceConfig() {
    if (!this.purchaseChannelList.length) {
      await this.preparePurchaseChannel()
    }
    const purchaseChannelId = this.purchaseChannelList?.find(
      purchaseChannel => purchaseChannel.type === PurchaseChannelEnum.UNIT_PURCHASE
    )?.id
    if (purchaseChannelId) {
      const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.getPurchaseChannel(purchaseChannelId)
      if (msRes.status.isSuccess()) {
        return msRes.data?.invoiceConfig?.openInvoiceType && msRes.data?.invoiceConfig?.allowAskFor
      }
    } else {
      return false
    }
  }

  /**
   * 学员补要发票配置
   * @returns InvoiceConfigResponse
   */
  async hasStudentOpenInvoice(): Promise<InvoiceConfigResponse> {
    if (!this.purchaseChannelList.length) {
      await this.preparePurchaseChannel()
    }
    const purchaseChannelId = this.purchaseChannelList?.find(
      purchaseChannel => purchaseChannel.type === PurchaseChannelEnum.STUDENT_PURCHASE
    )?.id
    const msRes = await msTradeQueryFrontGatewayTradeQueryBackstage.getPurchaseChannel(purchaseChannelId)
    if (msRes.status.isSuccess()) {
      return msRes.data?.invoiceConfig
    }
  }

  /**
   * 集体报名已配置的线下收款账户
   * @returns
   */
  async getBatchOfflineAccount() {
    if (!this.purchaseChannelList.length) {
      await this.preparePurchaseChannel()
    }
    const batchPurchase = this.purchaseChannelList.find(
      purchaseChannel => purchaseChannel.type === PurchaseChannelEnum.UNIT_PURCHASE
    )
    if (batchPurchase) {
      const terminal = batchPurchase?.terminalList.find(terminal => terminal.terminalCode === TerminalTypeEnum.WEB)
      const res = await msTradeQueryFrontGatewayTradeQueryBackstage.listReceiveAccountInServicer(
        terminal.receiveAccountIdList
      )
      const list = res?.data.filter(account => account.accountType === 2)
      return list?.map(account => OfflineReceiveAccountVo.from(account))
    }
  }
}

export default QueryTradeConfig
