import BasicDataGateway, {
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'

export default new (class QueryJobLevel {
  /**
   * 技术等级
   */
  jobLevelList: Array<TrainingPropertyResponse> = new Array<TrainingPropertyResponse>()

  /**
   * 技术等级map key: 属性id
   */
  private jobLevelMap: Map<string, TrainingPropertyResponse> = new Map<string, TrainingPropertyResponse>()

  /**
   * 查询对应行业下技术等级
   * @param industryId 行业id
   * @param industryPropertyId 行业属性id
   */
  async QueryJobLevelByIndustry(industryId: string, industryPropertyId: string) {
    this.jobLevelList = new Array<TrainingPropertyResponse>()
    this.jobLevelMap = new Map<string, TrainingPropertyResponse>()

    const res = await BasicDataGateway.listIndustryPropertyRootByCategoryV2({
      industryPropertyId,
      industryId,
      categoryCode: IndustryPropertyCodeEnum.JOB_LEVEL
    })
    if (res.data?.length) {
      this.jobLevelList = res.data
      res.data.map(item => {
        this.jobLevelMap.set(item.propertyId, item)
      })
    }

    return this.jobLevelList
  }

  /**
   * 获取详情表
   * @param propertyId 属性id
   */
  getJobLevelDetail(propertyId: string): TrainingPropertyResponse {
    return this.jobLevelMap.get(propertyId)
  }
})()
