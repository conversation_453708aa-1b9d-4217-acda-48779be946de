import AbstractEnum from '@hbfe-biz/biz-anticheat/dist/common/enums/AbstractEnum'

export enum RefundOrderStatusEnum {
  /**
   * 退款审批
   */
  RefundOrderStatusEnumApproving,

  /**
   * 退款处理中
   */
  RefundOrderStatusEnumHandling,

  /**
   * 退款拒绝
   */
  RefundOrderStatusEnumRefuse,

  /**
   * 退款取消
   */
  RefundOrderStatusEnumCancel,

  /**
   * 退款成功
   */
  RefundOrderStatusEnumSuccess,

  /**
   * 退款失败
   */
  RefundOrderStatusEnumFail
}
class RefundOrderStatusMapType extends AbstractEnum<RefundOrderStatusEnum> {
  static enum = RefundOrderStatusEnum
  constructor(status?: RefundOrderStatusEnum) {
    super()
    this.current = status
    this.map.set(RefundOrderStatusEnum.RefundOrderStatusEnumApproving, '退款审批')
    this.map.set(RefundOrderStatusEnum.RefundOrderStatusEnumHandling, '退款处理中')
    this.map.set(RefundOrderStatusEnum.RefundOrderStatusEnumRefuse, '退款拒绝')
    this.map.set(RefundOrderStatusEnum.RefundOrderStatusEnumCancel, '退款取消')
    this.map.set(RefundOrderStatusEnum.RefundOrderStatusEnumSuccess, '退款成功')
    this.map.set(RefundOrderStatusEnum.RefundOrderStatusEnumFail, '退款失败')
  }

  /**
   * 后端状态转换为UI展示状态
   * @param dtoStatus 后端退货单状态 退货单状态 (0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   * @param closeType 退货单关闭类型  （1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
   */
  transferDtoToUiOperationStatus(dtoStatus: number, closeType: number) {
    const approveStates = [0, 1],
      handlingStates = [2, 3, 4, 5, 6],
      successStatues = [8, 9, 10]
    if (approveStates.indexOf(dtoStatus) != -1) {
      return RefundOrderStatusEnum.RefundOrderStatusEnumApproving
    } else if (handlingStates.indexOf(dtoStatus) != -1) {
      return RefundOrderStatusEnum.RefundOrderStatusEnumHandling
    } else if (successStatues.indexOf(dtoStatus) != -1) {
      return RefundOrderStatusEnum.RefundOrderStatusEnumSuccess
    } else if (dtoStatus == 11 && closeType == 3) {
      return RefundOrderStatusEnum.RefundOrderStatusEnumRefuse
    } else if (dtoStatus == 11 && [1, 2].indexOf(closeType) != -1) {
      return RefundOrderStatusEnum.RefundOrderStatusEnumCancel
    } else if (dtoStatus == 7) {
      return RefundOrderStatusEnum.RefundOrderStatusEnumFail
    } else {
      return undefined
    }
  }
}

export default new RefundOrderStatusMapType()
