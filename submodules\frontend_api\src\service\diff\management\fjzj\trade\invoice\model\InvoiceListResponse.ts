/*
 * @Description: 列表转换数据
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-19 15:06:05
 */

import { OnlineInvoiceResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import InvoiceList from '@api/service/management/trade/single/invoice/query/vo/InvoiceListResponse'
import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/fjzj/trade/enums/SaleChannelType'
import QueryPlatform from '@api/service/diff/common/fjzj/dictionary/QueryPlatform'

export default class InvoiceListResponse extends InvoiceList {
  /**
   * 对接第三方平台
   */
  thirdPartyPlatform = ''
  static from(onlineInvoiceResponse: OnlineInvoiceResponse) {
    const invoiceListResponse = Object.assign(new InvoiceListResponse(), InvoiceList.from(onlineInvoiceResponse))
    if (invoiceListResponse.saleChannel == SaleChannelEnum.huayi) {
      invoiceListResponse.thirdPartyPlatform = SaleChannelType.map.get(invoiceListResponse.saleChannel)
    } else if (onlineInvoiceResponse.associationInfoList[0]?.tppTypeId) {
      invoiceListResponse.thirdPartyPlatform = QueryPlatform.map.get(
        onlineInvoiceResponse.associationInfoList[0].tppTypeId
      )?.name
    }
    return invoiceListResponse
  }
}
