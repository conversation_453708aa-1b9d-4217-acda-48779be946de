/**
 * @description 课后测验配置
 */
class QuizConfig {
  /**
   * 课后测验id
   */
  id = ''
  /**
   * 课后测验配置id
   */
  configId = ''
  /**
   * 课后测验配置name
   */
  configName = ''
  /**
   * 要求进度
   * @description 值-1 => 不限制
   */
  minCourseSchedule = 0
  /**
   * 要求课后测验合格情况
   * @description 值-1 => 不限制
   */
  courseQuizPaperStandard = 0
  /**
   * 名称
   */
  name = ''
  /**
   * 出卷模式
   * @description 0 智能卷
   */
  publishPattern = 0
  /**
   * 试题总数
   */
  questionCount = 0
  /**
   * 总分
   */
  totalScore = 0
  /**
   * 及格分
   */
  passScore = 0
  /**
   * 是否开放题析
   */
  openDissects = false
  /**
   * 是否限制测验次数
   */
  limitCourseQuizNum = false
  /**
   * 允许测验次数
   */
  allowCourseQuizNum = 0
  /**
   * 多选题漏选得分模式
   * @description 0 不得分
   */
  multipleMissScorePattern = 0
  /**
   * 测试试题数量配置
   * @description 0 固定题数  1 按课程学时数计算
   */
  questionCountConfigureType = 0
  /**
   * 每学时测试试题数量配置
   */
  questionCountPerPeriod = 0
  /**
   * 每道测验题答题时长
   */
  timeLengthPerQuestion = 0
}

export default QuizConfig
