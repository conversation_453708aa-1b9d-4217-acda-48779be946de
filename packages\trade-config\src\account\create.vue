<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/basic-data/trade/account')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/basic-data/trade/account' }">收款账户管理</el-breadcrumb-item>
      <el-breadcrumb-item>新建收款账户</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="ruleForm" :rules="rules" :model="payment" label-width="140px" class="m-form">
                <el-form-item label="支付方式：">
                  <el-radio-group v-model="payment.accountType" @change="switchAccountType">
                    <el-radio :label="1">线上</el-radio>
                    <el-radio :label="2">线下</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="支付账号类型：" v-if="payment.accountType == 1">
                  <el-radio-group
                    v-model="payment.paymentChannelId"
                    v-for="(payType, index) in payTypeList"
                    :key="index"
                  >
                    <el-radio :label="payType.paymentChannelId" @change="switchAPayType" class="pay-style"
                      >{{ payType.describe }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item label="账户别名：" prop="accountName" v-if="payment.accountType == 1">
                  <el-input v-model="payment.accountName" clearable placeholder="请输入账户别名" class="form-l" />
                </el-form-item>
                <!--微信-->
                <div
                  v-if="
                    payment.accountType == 1 &&
                    (payment.paymentChannelId == 'WXPAY' || payment.paymentChannelId == 'WXPAYH5')
                  "
                >
                  <el-form-item label="商户号：" prop="accountNo">
                    <el-input v-model="payment.accountNo" clearable placeholder="请输入商户号" class="form-l" />
                  </el-form-item>
                  <el-form-item label="API密钥：" prop="merchantKey">
                    <el-input v-model="payment.merchantKey" clearable placeholder="请输入API密钥" class="form-l" />
                  </el-form-item>
                  <el-form-item label="公众帐号ID：" prop="wxPayAppId">
                    <el-input v-model="payment.wxPayAppId" clearable placeholder="请输入公众帐号ID" class="form-l" />
                  </el-form-item>
                  <el-form-item label="微信证书密钥：" prop="privateKeyPWD">
                    <el-input
                      v-model="payment.privateKeyPWD"
                      clearable
                      placeholder="请输入微信证书密钥"
                      class="form-l"
                    />
                    <span class="f-ml10 f-co">注：默认使用商户号</span>
                  </el-form-item>
                  <el-form-item label="微信证书：" prop="hbFileUploadResponse">
                    <hb-upload-file v-model="hbFileUploadResponse" :file-type="2">
                      <el-button type="primary" plain>点击上传</el-button>
                    </hb-upload-file>
                  </el-form-item>
                </div>
                <!--支付宝-->
                <div
                  v-if="
                    payment.accountType == 1 &&
                    (payment.paymentChannelId == 'ALIPAY' || payment.paymentChannelId == 'ALIPAYH5')
                  "
                >
                  <el-form-item label="支付宝帐号：" prop="accountNo">
                    <el-input v-model="payment.accountNo" clearable placeholder="请输入支付宝帐号" class="form-l" />
                  </el-form-item>
                  <!-- todo 可能不要，后端未给答复 -->
                  <!-- <el-form-item label="支付宝密钥：">
                    <el-input v-model="alipayKey" clearable placeholder="请输入支付宝密钥" class="form-l" />
                  </el-form-item> -->
                  <el-form-item label="合作者身份ID：" prop="partner">
                    <el-input v-model="payment.partner" clearable placeholder="请输入合作者身份ID" class="form-l" />
                  </el-form-item>
                  <el-form-item label="支付宝应用私钥：" prop="privateKey">
                    <el-input
                      v-model="payment.privateKey"
                      clearable
                      placeholder="请输入支付宝应用私钥"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="支付宝公钥：" prop="publicKey">
                    <el-input v-model="payment.publicKey" clearable placeholder="请输入支付宝公钥" class="form-l" />
                  </el-form-item>
                  <el-form-item label="支付宝应用ID：" prop="aliPayAppId">
                    <el-input v-model="payment.aliPayAppId" clearable placeholder="请输入支付宝应用ID" class="form-l" />
                  </el-form-item>
                </div>

                <!-- 兴业银行-聚合支付 -->
                <div
                  v-if="
                    payment.accountType == 1 &&
                    (payment.paymentChannelId == 'CIB_PAY' || payment.paymentChannelId == 'CIB_PAYH5')
                  "
                >
                  <el-form-item label="商户号：" prop="accountNo">
                    <el-input v-model="payment.accountNo" clearable placeholder="请输入商户号" class="form-l" />
                  </el-form-item>
                  <el-form-item label="应用ID：" prop="xyPayAppId">
                    <el-input v-model="payment.xyPayAppId" clearable placeholder="请输入应用ID" class="form-l" />
                  </el-form-item>
                  <el-form-item label="终端编号(收款APP终端编号)：" prop="terminalId">
                    <el-input
                      v-model="payment.terminalId"
                      clearable
                      placeholder="请输入终端编号(收款APP终端编号)"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="请求报文签名私钥(SM2签名私钥)：" prop="sm2key">
                    <el-input
                      v-model="payment.sm2key"
                      clearable
                      placeholder="请输入请求报文签名私钥(SM2签名私钥)"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="响应报文验证公钥：" prop="resPublicKey">
                    <el-input
                      v-model="payment.resPublicKey"
                      clearable
                      placeholder="请输入响应报文验证公钥"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="请求字段加密私钥(字段加密密钥)：" prop="reqKey">
                    <el-input
                      v-model="payment.reqKey"
                      clearable
                      placeholder="请输入请求字段加密私钥(字段加密密钥)"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="微信公众号id：">
                    <el-input
                      v-model="payment.xyPaySubAppId"
                      clearable
                      placeholder="请输入微信公众号id："
                      class="form-l"
                    />
                    <span class="f-ml10 f-co">注：若未配置公众号ID,学员支付完成后无法返回网校页面</span>
                  </el-form-item>
                </div>

                <!-- 建设银行聚合支付 -->
                <div
                  v-if="
                    payment.accountType == 1 &&
                    (payment.paymentChannelId == 'CCB_PAY' || payment.paymentChannelId == 'CCB_PAYH5')
                  "
                >
                  <el-form-item label="商户号：" prop="accountNo">
                    <el-input v-model="payment.accountNo" clearable placeholder="请输入商户号" class="form-l" />
                  </el-form-item>
                  <el-form-item label="商户柜台代码：" prop="posId">
                    <el-input v-model="payment.posId" clearable placeholder="请输入商户柜台代码" class="form-l" />
                  </el-form-item>
                  <el-form-item label="分行代码：" prop="branchId">
                    <el-input v-model="payment.branchId" clearable placeholder="请输入分行代码" class="form-l" />
                  </el-form-item>
                  <el-form-item label="网银支付接口公钥：" prop="jsPublicKey">
                    <el-input
                      v-model="payment.jsPublicKey"
                      clearable
                      placeholder="请输入网银支付接口公钥"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="操作员账号：" prop="operator">
                    <el-input v-model="payment.operator" clearable placeholder="请输入操作员账号" class="form-l" />
                  </el-form-item>
                  <el-form-item label="操作员交易密码：" prop="password">
                    <el-input v-model="payment.password" clearable placeholder="请输入操作员交易密码" class="form-l" />
                  </el-form-item>
                  <el-form-item label="是否使用防钓鱼" prop="phishing">
                    <el-radio-group v-model="payment.phishing">
                      <el-radio :label="1">使用</el-radio>
                      <el-radio :label="0">不使用</el-radio>
                    </el-radio-group>
                  </el-form-item>

                  <el-form-item label="文件证书：" prop="hbFileUploadResponse">
                    <hb-upload-file v-model="hbFileUploadResponse" :file-type="2">
                      <el-button type="primary" class="ml20 mt20" icon="el-icon-upload2"> 选择文件 </el-button>
                    </hb-upload-file>
                  </el-form-item>
                  <el-form-item label="文件证书密码：" prop="certPassword">
                    <el-input
                      v-model="payment.certPassword"
                      clearable
                      placeholder="请输入文件证书密码"
                      class="form-l"
                    />
                  </el-form-item>
                  <el-form-item label="微信公众号id：">
                    <el-input v-model="payment.jsSubAppid" clearable placeholder="请输入微信公众号id" class="form-l" />
                    <span class="f-ml10 f-co">注：若未配置公众号ID,学员支付完成后无法返回网校页面</span>
                  </el-form-item>
                </div>

                <!-- 兴业银行-聚合支付-威富通 -->
                <div
                  v-if="
                    payment.accountType == 1 &&
                    (payment.paymentChannelId == 'SWIFT_PASS_PAY' || payment.paymentChannelId == 'SWIFT_PASS_PAYH5')
                  "
                >
                  <el-form-item label="商户号：" prop="accountNo">
                    <el-input v-model="payment.accountNo" clearable placeholder="请输入商户号" class="form-l" />
                  </el-form-item>
                  <el-form-item label="商户私钥：" prop="mchPrivateKey">
                    <el-input v-model="payment.mchPrivateKey" clearable placeholder="请输入商户私钥" class="form-l" />
                  </el-form-item>
                  <el-form-item label="平台公钥：" prop="platPublicKey">
                    <el-input v-model="payment.platPublicKey" clearable placeholder="请输入平台公钥" class="form-l" />
                  </el-form-item>
                </div>

                <!-- 新大陆-聚合支付-->
                <div v-if="payment.accountType == 1 && payment.paymentChannelId == 'NEW_LAND_PAY'">
                  <el-form-item label="支付商户号：" prop="payMerchantId">
                    <el-input v-model="payment.payMerchantId" clearable placeholder="请输入支付商户号" class="form-l" />
                  </el-form-item>
                  <el-form-item label="代理商户号：">
                    <el-input v-model="payment.proxyId" clearable placeholder="请输入代理商户号" class="form-l" />
                  </el-form-item>
                  <el-form-item label="秘钥：" prop="xdlPrivateKey">
                    <el-input v-model="payment.xdlPrivateKey" clearable placeholder="请输入秘钥" class="form-l" />
                  </el-form-item>
                </div>

                <!--线下-->
                <div v-if="payment.accountType == 2">
                  <el-form-item label="账户别名：" prop="accountName">
                    <el-input v-model="payment.accountName" clearable placeholder="请输入账户别名" class="form-l" />
                  </el-form-item>
                  <el-form-item label="开户号：" prop="accountNo">
                    <el-input v-model="payment.accountNo" clearable placeholder="请输入开户号" class="form-l" />
                  </el-form-item>
                  <el-form-item label="开户银行：" prop="depositBank">
                    <el-input v-model="payment.depositBank" clearable placeholder="请输入开户银行" class="form-l" />
                  </el-form-item>
                  <el-form-item label="开户户名：" prop="merchantName">
                    <el-input v-model="payment.merchantName" clearable placeholder="请输入开户户名" class="form-l" />
                  </el-form-item>
                  <el-form-item label="柜台号：" prop="counterNumber">
                    <el-input v-model="payment.counterNumber" clearable placeholder="请输入柜台号" class="form-l" />
                  </el-form-item>
                  <el-form-item label="纳税人识别号：" prop="taxPayerId">
                    <el-select v-model="payment.taxPayerId" clearable placeholder="请选择纳税人识别号" class="form-m">
                      <el-option
                        :value="item.id"
                        v-for="item in taxPayerIdList"
                        :label="item.accountName"
                        :key="item.id"
                      ></el-option>
                    </el-select>
                  </el-form-item>
                </div>
                <el-form-item label="纳税人识别号：" prop="taxPayerId" v-if="payment.accountType == 1">
                  <el-select v-model="payment.taxPayerId" clearable placeholder="请选择纳税人识别号" class="form-m">
                    <el-option
                      :value="item.id"
                      v-for="item in taxPayerIdList"
                      :label="item.accountName"
                      :key="item.id"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item>
                  <div slot="label">
                    <span class="f-vm"><span style="color: red">*</span> 退款方式</span>
                    <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                      <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                      <div slot="content">
                        <p>退款方式说明：</p>
                        <p>线下退款，方式需要登录微信/支付宝商户后台退款，系统只记录退款状态；</p>
                        <p>线上退款，确认退款后系统会将款项返回原账户。</p>
                      </div>
                    </el-tooltip>
                    <span>：</span>
                  </div>
                  <el-radio-group v-model="payment.refundWay">
                    <el-radio :label="1" :disabled="payment.accountType == 2">线上退款</el-radio>
                    <el-radio :label="2">线下退款</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-form-item
                  label="扫码引导提示语："
                  v-show="
                    payment.paymentChannelId === PayAccountTypeEnum.CIB_PAY ||
                    payment.paymentChannelId === PayAccountTypeEnum.CCB_PAY ||
                    payment.paymentChannelId === PayAccountTypeEnum.SWIFT_PASS_PAY ||
                    payment.paymentChannelId === PayAccountTypeEnum.NEW_LAND_PAY
                  "
                >
                  <el-input
                    type="textarea"
                    v-model="payment.qrScanPrompt"
                    clearable
                    placeholder="请输入提示语"
                    class="form-l"
                    maxlength="50"
                    show-word-limit
                  />
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-warning-outline m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      <p>默认提示语为：请使用微信、支付宝、银联APP</p>
                      <p>扫描二维码支付</p>
                    </div>
                  </el-tooltip>
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button @click="cancel">取消</el-button>
                  <el-button type="primary" @click="save" :loading="loading">保存</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import HbUploadFile from '@hbfe/jxjy-admin-components/src/hb-upload-file.vue'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import TradeInfoConfigModule from '@api/service/management/trade-info-config/TradeInfoConfigModule'
  import { PaymentChannelEnum } from '@api/service/common/enums/trade-configuration/PaymentChannelType'
  import { AccountTypeEunm } from '@api/service/common/enums/trade-configuration/AccountType'
  import { PaymentChannelIdResponse } from '@api/ms-gateway/ms-trade-configuration-v1'
  import TaxPayer from '@api/service/management/trade-info-config/mutation/vo/TaxPayer'
  import MutationReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/MutationReceiveAccountVo'
  import { PayAccountTypeEnum } from '@api/service/management/trade-info-config/enums/PayAccountTypeEnum'

  @Component({
    components: { HbUploadFile }
  })
  export default class extends Vue {
    @Ref('ruleForm') ruleForm: any
    // 支付宝密钥
    alipayKey = ''
    //文件上传之后的回调参数
    hbFileUploadResponse = new HBFileUploadResponse()
    // 支付账号类型
    // paymentChannel: PaymentChannelEnum
    // 支付方式
    // accountTypeParam = 1
    // accountType = AccountTypeEunm.ONLINE
    // 填写的信息详情
    payment = new MutationReceiveAccountVo()
    rules = {
      accountName: [{ type: 'string', required: true, message: '请输入账户别名', trigger: 'blur' }],
      qrScanPrompt: [{ type: 'string', required: true, message: '扫码引导提示语不能为空', trigger: 'blur' }],
      accountNo: [{ required: true, message: '请输入账号', trigger: 'blur' }],
      privateKeyPWD: [{ required: true, message: '请输入API密钥', trigger: 'blur' }],
      wxPayAppId: [{ required: true, message: '请输入公众帐号ID', trigger: 'blur' }],
      aliPayAppId: [{ required: true, message: '请输入支付宝应用ID', trigger: 'blur' }],
      merchantKey: [{ required: true, message: '请输入微信证书密钥', trigger: 'blur' }],
      partner: [{ required: true, message: '请输入合作者身份ID', trigger: 'blur' }],
      privateKey: [{ required: true, message: '请输入支付宝应用私钥', trigger: 'blur' }],
      publicKey: [{ required: true, message: '请输入支付宝应用公钥', trigger: 'blur' }],
      // depositBank: [{ required: true, message: '请输入开户银行', trigger: 'blur' }],
      // merchantName: [{ required: true, message: '请输入开户户名', trigger: 'blur' }],
      // counterNumber: [{ required: true, message: '请输入柜台号', trigger: 'blur' }],
      xyPayAppId: [{ required: true, message: '请输入应用ID', trigger: 'blur' }],
      terminalId: [{ required: true, message: '请输入终端编号(收款APP终端编号)', trigger: 'blur' }],
      sm2key: [{ required: true, message: '请输入请求报文签名私钥(SM2签名私钥)', trigger: 'blur' }],
      resPublicKey: [{ required: true, message: '请输入响应报文验证公钥', trigger: 'blur' }],
      reqKey: [{ required: true, message: '请输入请求字段加密私钥(字段加密密钥)', trigger: 'blur' }],
      taxPayerId: [{ required: true, message: '请选择纳税人识别号', trigger: ['blur', 'change'] }],
      refundWay: [{ required: true, message: '请选择退款方式', trigger: ['blur', 'change'] }],
      posId: [{ required: true, message: '请输入商户柜台代码', trigger: 'blur' }],
      branchId: [{ required: true, message: '请输入分行代码', trigger: 'blur' }],
      jsPublicKey: [{ required: true, message: '建行网银支付接口的公钥', trigger: 'blur' }],
      operator: [{ required: true, message: '请输入操作员账号', trigger: 'blur' }],
      password: [{ required: true, message: '请输入操作员交易密码', trigger: 'blur' }],
      phishing: [{ required: true, message: '请选择是否使用防钓鱼', trigger: ['blur', 'change'] }],
      certFilePath: [{ required: true, message: '请选择文件证书路径', trigger: ['blur', 'change'] }],
      certPassword: [{ required: true, message: '请选择文件证书密码', trigger: ['blur', 'change'] }],
      mchPrivateKey: [{ required: true, message: '请输入商户私钥', trigger: ['blur', 'change'] }],
      platPublicKey: [{ required: true, message: '请输入平台公钥', trigger: ['blur', 'change'] }],
      payMerchantId: [{ required: true, message: '请输入支付商户号', trigger: ['blur', 'change'] }],
      proxyId: [{ required: true, message: '请输入代理商户号', trigger: ['blur', 'change'] }],
      xdlPrivateKey: [{ required: true, message: '请输入秘钥', trigger: ['blur', 'change'] }]
    }
    // 账号类型列表
    payTypeList = new Array<PaymentChannelIdResponse>()
    // 纳税人识别号数组
    taxPayerIdList = new Array<TaxPayer>()
    getCreateReceiveAccount = TradeInfoConfigModule.mutationTradeInfoConfigFactory.getCreateReceiveAccount()
    /**
     * 保存加载效果
     */
    loading = false
    PayAccountTypeEnum = PayAccountTypeEnum

    // 切换支付方式
    async switchAccountType(e: any) {
      this.ruleForm.resetFields()
      if (e == 1) {
        this.payment.accountType = AccountTypeEunm.ONLINE
      } else {
        this.payment.accountType = AccountTypeEunm.OFFLINE
      }
      // this.getCreateReceiveAccount.createVoByPaymentChannelType(this.payment.accountType, this.payment.paymentChannelId)
      console.log(this.payment, 'payment')
    }

    // 切换支付账号类型
    async switchAPayType(e: any) {
      this.ruleForm.resetFields()
      this.payment.paymentChannelId = e
      // this.getCreateReceiveAccount.createVoByPaymentChannelType(this.payment.accountType, this.payment.paymentChannelId)
      console.log(this.payment, 'payment')
    }

    async created() {
      const res = await this.getCreateReceiveAccount.getPaymentChannelIdList()
      console.log('res++++++++++++', res)

      this.payTypeList = res
      // 暂时添加对应渠道
      // this.payTypeList.push({ paymentChannelId: 'XDL_PAY', describe: '新大陆聚合支付' })
      this.payment.paymentChannelId = this.payTypeList[0]?.paymentChannelId
      // this.getCreateReceiveAccount.createVoByPaymentChannelType(this.payment.accountType, res[0].paymentChannelId)
      this.taxPayerIdList = await this.getCreateReceiveAccount.getTaxPayerIdList()
      console.log(this.payment, 'payment', this.taxPayerIdList)
    }

    // 取消事件
    cancel() {
      this.$router.push('/basic-data/trade/account')
    }
    // 保存事件
    async save() {
      this.payment.privateKeyFileName = this.hbFileUploadResponse.fileName
      this.payment.privateKeyPath = this.hbFileUploadResponse.url
      if (this.payment.paymentChannelId == 'CCB_PAY' || this.payment.paymentChannelId == 'CCB_PAYH5') {
        this.payment.certFilePath = this.hbFileUploadResponse.url
      }
      console.log(this.payment, 'payment.accountType')
      this.ruleForm.validate(async (boolean: boolean, value: object) => {
        console.log('boolean*****', boolean, 'value****', value)
        if (this.payment.accountType === 1) {
          if (this.payment.paymentChannelId == 'WXPAY' || this.payment.paymentChannelId == 'WXPAYH5') {
            if (!this.hbFileUploadResponse.url || this.hbFileUploadResponse.url === '') {
              return this.$message.warning('请选择上传文件')
            }
          } else if (this.payment.paymentChannelId == 'CCB_PAY' || this.payment.paymentChannelId == 'CCB_PAYH5') {
            if (!this.hbFileUploadResponse.url || this.hbFileUploadResponse.url === '') {
              return this.$message.warning('请选择上传文件')
            }
          }
        }
        if (this.payment.refundWay === -1) {
          return this.$message.warning('请选择退款方式')
        }

        if (boolean) {
          //   debugger
          // if (this.accountTypeParam === 1) {
          //   if (this.payment.paymentChannelId === 'WXPAY') {
          //     this.paymentChannel = PaymentChannelEnum.WXPAY
          //   } else if (this.payment.paymentChannelId === 'WXPAY_H5') {
          //     this.paymentChannel = PaymentChannelEnum.WXPAY_H5
          //   } else if (this.payment.paymentChannelId === 'ALIPAY') {
          //     this.paymentChannel = PaymentChannelEnum.ALIPAY
          //   } else if (this.payment.paymentChannelId === 'ALIPAY_H5') {
          //     this.paymentChannel = PaymentChannelEnum.ALIPAY_H5
          //   }
          // }
          this.loading = true
          if (this.payment.accountType === 1) {
            // debugger
            this.getCreateReceiveAccount.createVoByPaymentChannelType(
              this.payment.accountType,
              this.payment.paymentChannelId
              //   PayAccountTypeEnum.CIB_PAY
            )
          } else {
            this.getCreateReceiveAccount.createVoByPaymentChannelType(this.payment.accountType, 'OFFLINE_PAY')
          }
          this.getCreateReceiveAccount.createReceiveAccount.from(this.payment)
          // if (this.payment.accountType === AccountTypeEunm.ONLINE) {
          //   if (
          //     [PaymentChannelEnum.WXPAY, PaymentChannelEnum.WXPAY_H5].includes(
          //       PaymentChannelEnum[this.payment.paymentChannelId]
          //     )
          //   ) {
          //     this.getCreateReceiveAccount.createReceiveAccount.from(this.payment)
          //   } else if (
          //     [PaymentChannelEnum.ALIPAY, PaymentChannelEnum.ALIPAY_H5].includes(
          //       PaymentChannelEnum[this.payment.paymentChannelId]
          //     )
          //   ) {
          //     this.getCreateReceiveAccount.createReceiveAccount.from(this.payment)
          //   }
          // } else {
          //   this.getCreateReceiveAccount.createReceiveAccount.from(this.payment)
          // }
          // this.paymentChannel = PaymentChannelEnum[this.payment.paymentChannelId]
          console.log(this.getCreateReceiveAccount.createReceiveAccount, '入参===', this.payment)
          try {
            const res = await this.getCreateReceiveAccount.doCreate()
            console.log(res, 'res')

            if (res.status.code === 200) {
              this.$router.push('/basic-data/trade/account')
              this.$message.success('保存成功')
            } else {
              this.$message.warning('创建失败')
              this.loading = false
            }
          } catch (e) {
            console.error(e)
            this.loading = false
          }
        }
      })
    }
  }
</script>

<style scoped>
  .pay-style {
    padding-right: 20px;
  }
</style>
