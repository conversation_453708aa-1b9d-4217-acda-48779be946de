import { StudentCourseMediaLearningRecord } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

class CourseMediaLearningRecord {
  /**
   * 课程学习记录id
   */
  courseLearningRecordId: string
  /**
   * 课程媒体学习进度状态（0：未评定 1：未合格 2：合格）
   */
  courseScheduleStatus: number
  /**
   * 课程学习进度百分比
   */
  schedule: number
  /**
   * 开始学习时间
   */
  startLearningTime: string
  /**
   * 最后一次学习时间
   */
  lastLearningTime: string
  /**
   * 媒体课程学习取得结果时间
   */
  learningResultTime: string

  static from(response: StudentCourseMediaLearningRecord) {
    const record = new CourseMediaLearningRecord()
    Object.assign(record, response)
    record.schedule = response.schedule || 0
    return record
  }
}

export default CourseMediaLearningRecord
