import CreateQuestion from './mutation/CreateQuestion'
import UpdateQuestion from './mutation/UpdateQuestion'
import QuestionAction from './mutation/QuestionAction'
import { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'
import MutationQuestionAsynTask from './mutation/MutationQuestionAsynTask'
class MutationQuestionFactory {
  /**
   * @description: 获取创建试题实例
   * @param {QuestionTypeEnum} 试题类型
   */
  getCreateQuestion(questionType: QuestionTypeEnum = QuestionTypeEnum.radio) {
    return new CreateQuestion(questionType)
  }

  /**
   * @description: 获取试题基础操作实例
   * @param {string} id 试题id
   */
  getQuestionAction(id: string) {
    return new QuestionAction(id)
  }

  /**
   * @description: 获取修改试题实例
   * @param {string} id 试题id
   * @param {QuestionTypeEnum} questionType 试题类型
   */
  getUpdateQuestion(id: string, questionType: QuestionTypeEnum) {
    return new UpdateQuestion(id, questionType)
  }

  /**
   * @description: 试题导入实例
   */
  get mutationQuestionAsynTask() {
    return new MutationQuestionAsynTask()
  }
}
export default new MutationQuestionFactory()
