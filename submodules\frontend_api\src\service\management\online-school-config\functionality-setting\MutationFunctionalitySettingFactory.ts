import { CollectiveSignUpTypeEnum } from '@api/service/common/enums/online-school-config/CollectiveSignUpType'
import MutationCollectiveSignUp from '@api/service/management/online-school-config/functionality-setting/mutation/MutationCollectiveSignUp'
import MutationElectronicInvoice from '@api/service/management/online-school-config/functionality-setting/mutation/MutationElectronicInvoice'
import MutationRegisterAndLogin from '@api/service/management/online-school-config/functionality-setting/mutation/MutationRegisterAndLogin'

/*
 ** 业务工厂
 */
class MutationFunctionalitySettingFactory {
  /* 
    增值税电子普通发票实例
  */
  get mutationElectronicInvoice() {
    return new MutationElectronicInvoice()
  }

  /*
     集体报名配置实例
   */
  get collectiveSignUp() {
    return (type?: CollectiveSignUpTypeEnum) => {
      return new MutationCollectiveSignUp(type)
    }
  }

  /**
   * 注册登录实例
   */
  get registerAndLogin() {
    return new MutationRegisterAndLogin()
  }
}

export default new MutationFunctionalitySettingFactory()
