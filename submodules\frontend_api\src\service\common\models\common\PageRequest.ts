/**
 * 分页请求数据结构
 */
export default class PageRequest {
  pageNo: number
  pageSize: number

  constructor(pageNo?: number, pageSIze?: number) {
    this.pageNo = pageNo || 1
    this.pageSize = pageSIze || 10
  }

  equals(pageRequest: any): boolean {
    if (!pageRequest) {
      return false
    }
    if (this.pageNo !== pageRequest.pageNo) {
      return false
    }
    if (this.pageSize !== pageRequest.pageSize) {
      return false
    }
    return true
  }
}
