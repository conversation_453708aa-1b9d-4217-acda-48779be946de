import { PlatformSchemeConfigRequest } from '@api/platform-gateway/platform-scheme-config-backstage-v1'
import { CommoditySkuRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

class RuleSchemeParams {
  /**
   * 方案id集合
   */
  schemeIds?: Array<string>
  /**
   * 是否包含已配置学习规则
   */
  isIncludeHasStudyRule?: boolean
  /**
   * 培训形式
   */
  trainType?: string
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 行业id
   */
  industryId?: string
  /**
   * 行业ids
   */
  industryIds?: string[]
  static to(vo: RuleSchemeParams) {
    const dto = new PlatformSchemeConfigRequest()
    dto.schemeIds = vo?.schemeIds
    dto.isIncludeHasStudyRule = vo?.isIncludeHasStudyRule
    dto.trainType = vo?.trainType
    dto.schemeName = vo?.schemeName
    dto.industryId = vo?.industryId
    return dto
  }

  static toOnline(vo: RuleSchemeParams) {
    const dto = new CommoditySkuRequest()
    // dto.schemeIds = vo?.schemeIds
    dto.schemeRequest.schemeType = vo?.trainType
    dto.schemeRequest.schemeName = vo?.schemeName
    dto.skuPropertyRequest.industry = vo?.industryIds
    return dto
  }
}

export default RuleSchemeParams
