import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 开票提供商类型
 */
enum InvoiceProviderEnum {
  NOCONFIG = '0',
  NUONUO = '5',
  NUONUOV2 = '7',
  BWJF = '6',
  NST = '9',
  NSTQDP = '8'
}

export { InvoiceProviderEnum }
class InvoiceProviderTypes extends AbstractEnum<InvoiceProviderEnum> {
  static enum = InvoiceProviderEnum

  constructor(status?: InvoiceProviderEnum) {
    super()
    this.current = status
    this.map.set(InvoiceProviderEnum.NUONUO, '诺诺')
    this.map.set(InvoiceProviderEnum.NUONUOV2, '诺诺V2')
    this.map.set(InvoiceProviderEnum.BWJF, '百旺金赋')
  }
}

export default new InvoiceProviderTypes()
