import ClassOutline, { ChildOutlineResp } from '@api/service/customer/scheme/models/ClassOutline'
/**
 * @description 课程大纲工具类
 */
class ClassOutlineUtil {
  /**
   * 重塑树形数组
   * @param parentId 父级id，顶级默认-1
   * @param level 层级，顶级默认1
   */
  reShapeTree(treeArr: ChildOutlineResp[], schemeId: string, parentId = '-1', level = 1): ClassOutline[] {
    const result = [] as ClassOutline[]
    if (treeArr && treeArr.length) {
      for (const node of treeArr) {
        node.parentId = parentId
        node.level = level
        const opt = ClassOutline.from(node, schemeId)
        if (node.childOutlines && node.childOutlines.length) {
          opt.children = this.reShapeTree(node.childOutlines, schemeId, node.id, level + 1)
        }
        result.push(opt)
      }
    }
    return result
  }

  /**
   * 获取指定条件的树形数组
   * @param tree 树形数组
   * @param condition 需要满足的条件
   */
  filterTreeByCondition(treeArr: ClassOutline[], condition: (node: ClassOutline) => boolean) {
    let result = [] as ClassOutline[]
    if (treeArr && treeArr.length) {
      for (const node of treeArr) {
        if (condition(node)) {
          result.push(node)
        }
        if (node.children && node.children.length) {
          const res = this.filterTreeByCondition(node.children, condition)
          if (res && res.length) {
            result = result.concat(res)
          }
        }
      }
    }
    return result
  }

  /**
   * 获取树上key对应的值集合
   * @param propKey 属性key
   */
  getValuesByNodeKey<T = any>(treeArr: ClassOutline[], propKey = 'id') {
    let result = [] as T[]
    if (treeArr && treeArr.length) {
      treeArr.forEach(item => {
        if (item) {
          for (const [key, value] of Object.entries(item)) {
            if (key === propKey) result.push(value)
          }
        }
        if (item.children && item.children.length) {
          const res = this.getValuesByNodeKey<T>(item.children, propKey)
          result = result.concat(res)
        }
      })
    }
    return result
  }

  /**
   * 获取树上键值对集合
   * @param propKey 属性key
   * @param propValue 属性值
   */
  getMapByNode<T = any>(treeArr: ClassOutline[], propKey: string, propValue: string) {
    let result = new Map<string, T>()
    if (treeArr && treeArr.length) {
      treeArr.forEach(item => {
        if (item) {
          for (const [key, value] of Object.entries(item)) {
            if (key === propKey) result.set(value, item[propValue])
          }
        }
        if (item.children && item.children.length) {
          const res = this.getMapByNode(item.children, propKey, propValue)
          result = new Map([...result, ...res])
        }
      })
    }
    return result
  }

  /**
   * 给树形数组插入选项
   * @param schemeId 方案id
   */
  insertOptionToTree(treeArr: ClassOutline[], hasAll = true) {
    if (treeArr && treeArr.length) {
      for (const item of treeArr) {
        item.curAndAfterIds = this.getValuesByNodeKey<string>([item]).filter(Boolean)
        if (item.children && item.children.length) {
          this.insertOptionToTree(item.children)
        }
      }
      if (hasAll && treeArr && treeArr.length && treeArr[0].level !== 1) {
        const opt = new ClassOutline()
        opt.isSpecifiedOption = true
        opt.curAndAfterIds = this.getValuesByNodeKey<string>(treeArr).filter(Boolean)
        opt.compulsoryCourseMap = this.getMapByNode<string[]>(treeArr, 'id', 'compulsoryCourseIdList')
        opt.name = '全部'
        opt.schemeId = treeArr[0].schemeId
        opt.parentId = treeArr[0].parentId
        opt.level = treeArr[0].level
        treeArr.unshift(opt)
      }
    }
  }

  /**
   * 树形结构转为数组
   */
  treeToList(treeArr: ClassOutline[]): ClassOutline[] {
    let result = [] as ClassOutline[]
    treeArr?.forEach(node => {
      result.push(node)
      if (node.children && node.children.length) {
        const res = this.treeToList(node.children)
        result = result.concat(res)
      }
    })
    return result
  }
}

export default ClassOutlineUtil
