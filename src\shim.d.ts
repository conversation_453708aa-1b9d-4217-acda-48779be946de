declare module '@hbfe-commons/authorization/dist/web'
declare module '@hbfe-commons/has-permission'
declare module '@/project.config.json'
declare module '@hbfe/jxjy-admin-common/src/router-config/boilerplate-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/basic-school-routes'
declare module '@hbfe/jxjy-admin-components/src/yxzx-container/components/top-guide-data/cgIndex'
declare module '@hbfe/jxjy-admin-components/src/yxzx-container/components/top-guide-data/cgJcbIndex'
declare module '@hbfe/jxjy-admin-components/src/yxzx-container/components/top-guide-data/fxsIndex'
declare module 'org-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/super-routes'
declare module 'provider-routes'
declare module 'channel-routes'
declare module 'participating-unit-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/scjzs-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/gszj-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/fjzj-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/yzzj-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/xmlg-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/byzj-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/jxgx-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/hljysxh-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/nyyz-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/zzzj-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/gyms-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/mqzj-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/qztg-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/zzkd-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/zjzj-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/gstyb-routes'
declare module '@hbfe/jxjy-admin-common/src/router-config/zztt-routes'
declare module '@hbfe-vue-components/pop-tree'
declare module '@jiaminghi/data-view'
declare module '@hbfe-vue-components/image-cropper'
declare module '@design/trainingInstitution/styles/webstyle.scss'
declare module '@design/admin/styles/webstyle.scss'

declare module '*.graphql' {
  import { DocumentNode } from 'graphql'

  const value: DocumentNode
  export = value
}

declare namespace JSX {
  interface IntrinsicElements {
    [elemName: string]: any
  }
}

// global.d.ts
interface Window {
  TMap: any // 根据需要替换 `any` 为实际的类型
}

declare module '*.vue' {
  import Vue from 'vue'
  export = Vue
}
