import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { Secure } from '@api/Secure'
import { Page } from '@api/service/common/models/Page'
import PlatformStatisticReportQuery, {
  HotCourseChooseStatisticDTO,
  HotCourseChooseStatisticQueryParamsDTO
} from '@api/gateway/PlatformStatisticReportQuery'

interface CourseLearningStatisticModuleInterface {
  hotCourseList: Array<HotCourseChooseStatisticDTO>
  hotCourseTotalSize: number
}

@Module({ namespaced: true, dynamic: true, store, name: 'CustomerCourseLearningStatisticModule' })
class CourseLearningStatisticModule extends VuexModule implements CourseLearningStatisticModuleInterface {
  hotCourseList: Array<HotCourseChooseStatisticDTO> = new Array<HotCourseChooseStatisticDTO>()
  hotCourseTotalSize = 0

  @Mutation
  SET_HOT_COURSE_LIST(hotCourseList: Array<HotCourseChooseStatisticDTO>) {
    this.hotCourseList = hotCourseList
  }

  @Mutation
  SET_HOT_COURSE_TOTAL_SIZE(hotCourseTotalSize: number) {
    this.hotCourseTotalSize = hotCourseTotalSize
  }

  /**
   * 人气课程 精品课程
   *
   * @param params
   */
  @Action
  async findHotCourse(params: { page?: Page; paramsDTO?: HotCourseChooseStatisticQueryParamsDTO }) {
    const response = await PlatformStatisticReportQuery.pageHotCourseChooseStatistic(params)
    if (!response.status.isSuccess()) {
      return response.status
    }
    this.SET_HOT_COURSE_LIST(response.data.currentPageData)
    this.SET_HOT_COURSE_TOTAL_SIZE(response.data.totalSize)
  }
}

export default getModule(CourseLearningStatisticModule)
