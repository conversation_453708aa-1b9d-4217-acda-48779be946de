import runtimeContext from '@/RuntimeContext'
import BaseContainer from '@hbfe/jxjy-admin-components/src/base-container/index.vue'
import error404 from '@hbfe/jxjy-admin-components/src/errors/404/index.vue'
import YxzxContainer from '@hbfe/jxjy-admin-components/src/yxzx-container/index.vue'
import Menu from '@/models/Menu'
import UnitTypeEnum from '@/router/models/UnitTypeEnum'
import RootModule from '@/store/RootModule'
import DevToolsModule from '@/store/devtools/DevToolsModule'
import Forget from '@hbfe/jxjy-admin-authentication/src/forget/index.vue'
import SpecialSubjectLogin from '@hbfe/jxjy-admin-authentication/src/special-subject-login/index.vue'
import SpecialSubjectForget from '@hbfe/jxjy-admin-authentication/src/special-subject-forget/index.vue'

import AutoMinZhenTong from '@hbfe/jxjy-admin-authentication/src/login/min-zhen-tong-auto.vue'
import BindingMinZhenTong from '@hbfe/jxjy-admin-authentication/src/login/min-zhen-tong.vue'
import OnlineSchoolReminder from '@hbfe/jxjy-admin-authentication/src/login/onlineSchoolReminder.vue'
import ThirdPartyLogin from '@hbfe/jxjy-admin-authentication/src/login/third-party-login.vue'
import QueryOnlineSchoolStatus from '@api/service/common/basic-data-dictionary/query/QueryOnlineSchoolStatus'
import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import Context from '@api/service/common/context/Context'
import QueryUserFactory from '@api/service/management/user/QueryUserFactory'
import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
import SchoolServiceIdStrategy from '@api/service/common/diffSchool/SchoolServiceIdStrategy'
import progress from 'nprogress'
import Vue from 'vue'
import VueRouter, { Route } from 'vue-router'
import routeAdapter, { unitMap } from './RouteAdapter'
Vue.use(VueRouter)

let router: VueRouter
/**
 * 因为菜单没有的路由不能注册
 * @param remoteMenuList
 */

class CoreRouteConfig {
  init(remoteMenuList?: Array<Menu>) {
    if (router) return router
    let serviceType = runtimeContext.businessContext.serviceProvider.serviceType
    if (process.env.NODE_ENV === 'development' && DevToolsModule.developmentSettings.enableProviderMock) {
      serviceType = DevToolsModule.developmentSettings.provider
    }
    // 获取当前登录角色做路由合并（继续教育V2）
    const unit = RootModule.unitRole
    const unitType: UnitTypeEnum = unitMap[unit]
    const resultRouteConfig = routeAdapter.mergeRouteByUnitType(unitType)
    let fxRouter
    if (CapabilityServiceConfig.distributionType || CapabilityServiceConfig.distributionType === 0) {
      fxRouter = routeAdapter.initFxRouter()
    }
    resultRouteConfig.push({
      path: '/welcome',
      component: () => import('@hbfe/jxjy-admin-components/src/welcome.vue'),
      name: 'welcome',
      meta: { title: '欢迎页', keepAlive: true }
    })
    router = new VueRouter({
      // mode: 'history',
      routes: [
        {
          path: '/',
          redirect: '/home',
          component: BaseContainer,
          children: resultRouteConfig
        },
        {
          path: '/fx',
          redirect: RootModule?.firstNode?.router?.path,
          component: YxzxContainer,
          children: fxRouter
        },
        {
          path: '/login',
          name: 'loginPage',
          component: () => {
            // 判断当前是否是运营域环境
            if (RootModule.isOperation) {
              return import('@hbfe/jxjy-admin-authentication/src/operation-login/index.vue')
            } else if (SchoolServiceIdStrategy.currentSchool() === 'sjdb') {
              //三级等保
              return import('@hbfe/jxjy-admin-authentication/src/login-sign/index.vue')
            } else {
              return import('@hbfe/jxjy-admin-authentication/src/login/index.vue')
            }
          }
        },
        {
          path: '/specialSubjectLogin',
          name: 'specialSubjectLogin',
          component: SpecialSubjectLogin
        },
        {
          path: '/specialSubjectForget',
          name: 'specialSubjectForget',
          component: SpecialSubjectForget
        },
        {
          path: '/forget',
          name: 'forgetPage',
          component: Forget
        },
        {
          path: '/onlineSchoolReminder',
          name: 'onlineSchoolReminder',
          component: OnlineSchoolReminder
        },
        {
          path: '/binding-mzt',
          name: 'bindingMzt',
          component: BindingMinZhenTong
        },
        {
          path: '/binding-3rd',
          name: 'binding3rd',
          component: ThirdPartyLogin
        },
        {
          path: '/auto-mzt',
          name: 'autoMzt',
          component: AutoMinZhenTong
        },
        {
          path: '*',
          name: 'errorPage',
          component: error404
        }
      ]
    })

    // 白名单，不做权限控制，直接跳转
    const whiteList = [
      'errorPage',
      'specialSubjectLogin',
      'specialSubjectForget',
      'loginPage',
      'forgetPage',
      'bindingMzt',
      'autoMzt',
      'binding3rd',
      'onlineSchoolReminder',
      'welcome'
    ]
    router.beforeEach(async (to: Route, from: Route, next: (path?: any) => void) => {
      progress.start()
      RootModule.addKeepAliveWhiteList(to.meta.name)
      if (!whiteList.includes(to.name)) {
        const result = await QueryManagerDetail.queryManagerDetail()
        if (!result.isSuccess()) {
          next()
          return
        }
        try {
          const serviceId = CommonConfigCenter.getFrontendApplication(frontendApplication.trainingInstitutionServicerId)
          const isOperation = Context.servicerInfo.id === serviceId
          if (isOperation) {
            return next()
          }
          const res = await QueryOnlineSchoolStatus.queryStatus()
          if (res.code != 200) {
            progress.done()
            if (res.code == 5001) {
              return next({ path: '/onlineSchoolReminder', query: { type: 1 } })
            } else if (res.code == 5002) {
              return next({ path: '/onlineSchoolReminder', query: { type: 2 } })
            }
          }
          if (QueryUserFactory?.queryManagerDetail?.adminInfo?.accountInfo?.status == 2) {
            progress.done()
            return next({ path: '/onlineSchoolReminder', query: { type: 3 } })
          }
          if (RootModule.menuList.length == 0) {
            progress.done()
            return next('/welcome')
          }
          if (!result.isSuccess()) {
            progress.done()
            return next('/login')
          }
          next()
        } catch (e) {
          console.log('e=', e)
          next('/login')
          progress.done()
        } finally {
          progress.done()
        }
      } else {
        next()
        progress.done()
      }
    })
    router.afterEach((to: Route, from: Route) => {
      //分销路由
      if (to.path.indexOf('/fx') != -1) {
        // 支持左侧菜单不展示、tab选项卡保留展示
        if ((to.meta.isMenu && !to.meta.hideMenu) || to.meta.onlyShowOnTab) {
          RootModule.doAddFxNav(to)
        } else {
          RootModule.setFxNavActiveRouter(to)
        }
        // 判断路由是否在 tab 打开的状态
        if (!RootModule.fxRouteOnTab(from.path, from.meta.isMenu)) {
          // from.meta.keepAlive = false
          RootModule.REMOVE_KEEP_ALIVE_WHITE_LIST_BY_NAME(from.meta.name)
        }
      } else {
        // 网校路由
        if ((to.meta.isMenu && !to.meta.hideMenu) || to.meta.onlyShowOnTab) {
          RootModule.doAddNav(to)
        } else {
          RootModule.setNavActiveRouter(to)
        }
        // 判断路由是否在 tab 打开的状态
        if (!RootModule.routeOnTab(from.path, from.meta.isMenu)) {
          // from.meta.keepAlive = false
          RootModule.REMOVE_KEEP_ALIVE_WHITE_LIST_BY_NAME(from.meta.name)
        }
      }
    })
    return router
  }
}
export default new CoreRouteConfig().init
