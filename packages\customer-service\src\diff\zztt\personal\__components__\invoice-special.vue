<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import invoiceSpecial from '@hbfe/jxjy-admin-customerService/src/personal/components/invoice-special.vue'
  import QueryOffLineInvoice from '@api/service/diff/management/zztt/trade/invoice/QueryOffLineInvoice'

  @Component
  export default class extends invoiceSpecial {
    //接口请求
    queryOffLineInvoice = new QueryOffLineInvoice()
    /**
     * 查询发票分页
     */
    async doQueryPage() {
      if (!this.userId) {
        return
      }
      this.query.loading = true
      try {
        this.pageData = await this.queryOffLineInvoice.offLinePageElectVatspecialplaInvoiceInServicer(
          this.page,
          this.pageQueryParam
        )
      } catch (e) {
        console.log(e, '加载发票列表失败')
      } finally {
        this.query.loading = false
      }
    }
  }
</script>
