//布局样式
@import "../../common/variables";

//本项目公用样式 element 改写
.el-breadcrumb {
  padding: 10px 15px;
  color: #aaa;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-bottom: 1px solid #f2f2f2;

  &.bor {
    border-bottom: 1px solid #eee;
  }

  .el-breadcrumb__inner {
    color: #aaa !important;
  }

  .el-breadcrumb__inner.is-link,
  .el-breadcrumb__inner a {
    color: #aaa;
    font-weight: normal;

    &:hover {
      color: $base !important;
    }
  }

  .return-btn {
    padding: 4px 0;
    color: #999;

    &:hover {
      color: $base;
    }

    .iconfont {
      font-size: 12px;
      margin-right: 5px;
    }
  }

  & + .m-tab-top {
    height: calc(100% - 45px);
  }

  & + .m-card {
    &.is-sticky {
      top: 45px;
    }

    & + .m-tab-top {
      &.is-sticky {
        .el-tabs__header {
          top: 45px;
        }
      }
    }
  }

  & + div {
    .m-tab-top {
      height: calc(100% - 45px);
    }

    .m-card {
      &.is-sticky {
        top: 45px;
      }

      & + .m-tab-top {
        &.is-sticky {
          .el-tabs__header {
            top: 45px;
          }
        }
      }
    }
  }
}

//分页
.el-pagination {
  &.is-background {
    .btn-prev,
    .btn-next,
    .el-pager li {
      background-color: #fff;
      border: 1px solid #e6e6e6;
      border-radius: 3px;
    }
  }
}

//选择器
.el-form-item__content {
  line-height: 36px;
}

//tab标签
.el-tabs {
  .el-tabs__content {
    overflow: inherit;
  }

  .tab-right {
    position: absolute;
    z-index: 9;
    right: -15px;
    top: -44px;
  }

  &.no-margin {
    .el-tabs__header {
      margin-bottom: 0;
    }
  }
}

//按钮
.el-button {
  .hb-iconfont,
  .iconfont {
    font-size: 14px;
  }
}

//表单
.el-form--inline {
  .el-form-item {
    display: flex;
  }

  .el-form-item__content {
    flex: 1;
  }
}

.el-row {
  &.is-height {
    display: flex;
    flex-wrap: wrap;

    .el-card {
      height: 100%;
    }
  }

  &.no-gutter {
    .el-col {
      margin: 0 !important;
    }
  }

  .is-border-right {
    border-right: 1px solid #eee;
  }
}

.el-rate {
  .el-rate__icon {
    margin-right: 2px;
  }
}

.el-badge {
  &.badge-status {
    padding-left: 16px;

    .el-badge__content {
      left: 0;
      top: 6px;

      &.is-dot {
        width: 7px;
        height: 7px;
        transform: none;
      }
    }
  }
}

//气泡确认框
.el-popconfirm__main {
  padding: 5px 0 10px;
  max-width: 300px;
  align-items: flex-start;

  .el-popconfirm__icon {
    font-size: 16px;
    margin-top: 1px;
  }
}

.el-tooltip__popper {
  max-width: 500px;
  font-size: 13px;
  line-height: 1.5;
}

.el-input,
.el-textarea {
  .el-input__count {
    color: #bbb;
  }
}

.el-radio {
  line-height: 1.5;
}

.el-card {
  &.bg-gray {
    background-color: #f8f8f8;
  }
}

.el-radio-group {
  &.default-psw {
    display: grid;
    grid-template-columns: 200px auto;

    .el-radio {
      line-height: 36px;
    }

    .el-input {
      width: 200px;
    }
  }
}
// 表格鼠标悬停高亮
.el-table__body tr.hover-row > td.el-table__cell,
.el-table__body tr.hover-row.current-row > td.el-table__cell,
.el-table__body tr.hover-row.el-table__row--striped > td.el-table__cell,
.el-table__body tr.hover-row.el-table__row--striped.current-row > td.el-table__cell {
  background: #d3e7ff;
}
// 表格鼠标悬停高亮
.el-table--enable-row-hover .el-table__body tr:hover > td.el-table__cell {
  background: #d3e7ff;
}
