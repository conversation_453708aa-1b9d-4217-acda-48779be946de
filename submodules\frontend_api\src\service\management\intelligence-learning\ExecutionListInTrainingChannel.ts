import TrackingParam from '@api/service/management/intelligence-learning/model/TrackingParam'
import { Page, ResponseStatus } from '@hbfe/common'
import TaskTrackingList from '@api/service/management/intelligence-learning/model/TaskTrackingList'
import ExecutionItem from '@api/service/management/intelligence-learning/model/ExecutionItem'

/**
 * 执行情况-专题管理员
 */
export default class ExecutionListInTrainingChannel extends TaskTrackingList<TrackingParam, ExecutionItem> {
  constructor() {
    super(new TrackingParam())
  }
  /**
   * 执行情况列表查询
   */
  async queryList(page: Page) {
    return new ResponseStatus(200)
  }
}
