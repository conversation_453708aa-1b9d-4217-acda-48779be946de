import JxjypxtyptIdentityAuth, {
  ApplyDistributorReAuthenticateRequest
} from '@api/platform-gateway/platform-jxjypxtypt-identityAuth-service-v1'
import Account from '@api/service/common/authentication/Account'
import AuthToken from '@api/service/common/authentication/AuthToken'
import Verify from '@api/service/common/authentication/Verify'
import {
  authTokenLoginUrl,
  loginH5MsByAccountUrl,
  thirdTokenLoginUrl,
  weiXinUnionIdLoginUrl
} from '@api/service/common/authentication/contant'
import {
  AuthTokenLoginParams,
  WeiXinUnionIdLoginParams
} from '@api/service/common/authentication/interfaces/LoginParams'
import ThirdParty from '@api/service/common/authentication/plugins/third-party'
import Context from '@api/service/common/context/Context'
import { ContextTypeEnums } from '@api/service/common/enums/ContextTypeEnums'
import Cfetch from '@api/service/common/utils/fetch-interceptors'
import UserModule from '@api/service/customer/user/UserModule'
import QueryUserInfo from '@api/service/customer/user/query/QueryUserInfo'
import OnlineSchoolConfigModuleH5 from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
import BizAuthentication from '@hbfe-biz/biz-authentication/dist/Authentication'
import { identityAuthUrl, identityLogoutUrl } from '@hbfe-biz/biz-authentication/dist/common/ApiPaths'
import { grantType, refreshTokenKey } from '@hbfe-biz/biz-authentication/dist/common/Constant'
import GrantType from '@hbfe-biz/biz-authentication/dist/enums/GrantType'
import { IdentityType } from '@hbfe-biz/biz-authentication/dist/enums/IdentityType'
import { AuthResponse } from '@hbfe-biz/biz-authentication/src/models/AuthResponse'
import InitOptions from '@hbfe-biz/biz-authentication/src/models/InitOptions'
import { Response } from '@hbfe/request'
import { Response as ResponseRes } from '@hbfe/common'
import DistributionUnitInformation from '@api/service/management/user/query/manager/vo/DistributionUnitInformation'
import ChangeIdentityAuthenticationParams from '@hbfe-biz/biz-authentication/dist/models/ChangeIdentityAuthenticationParams'
import identityAuthentication, {
  AccountPwdCaptchaCredentialForFXSRequest,
  AccountPwdCaptchaCredentialRequest,
  AccountPwdCredentialRequest,
  IdentityAuthenticationTokenResponse,
  SmsCodeCredentialForFXSRequest,
  SmsCodeCredentialRequest
} from '@api/ms-gateway/ms-identity-authentication-v1'
import { DomainTypeEnum } from '@api/service/common/context/enums/DomainTypeEnum'
import BusinessClientTypeEnum from '@hbfe-biz/biz-authentication/src/enums/BusinessClientTypeEnum'
import LoginParams from '@hbfe-biz/biz-authentication/src/models/LoginParams'
import IdentityAuthenticationTokenModel from '@hbfe-biz/biz-authentication/src/models/IdentityAuthenticationTokenModel'
import QueryWebPortalConfig from '@api/service/customer/online-school-config/online-school-partal-config/query/QueryWebPortalConfig'
import UrlConvertParam, { EnvInfo } from '@api/service/common/url-transfer/model/UrlConvertParam'
import { DomainTypeEnumEnum } from '@api/service/common/url-transfer/enum/DomainTypeEnum'
import CryptUtil from '@api/service/common/crypt/CryptUtil'
import NewRedirectSwitchConfig from '@api/service/common/url-transfer/model/NewRedirectSwitchConfig'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

import Env from '@api/service/common/utils/Env'
const ContentType = 'application/x-www-form-urlencoded'
const identityAuthenticationToken = 'identity_authentication_token'

export default class Authentication extends BizAuthentication {
  constructor(options: InitOptions) {
    super(options)
    this.account = new Account(this)
    this.verify = new Verify(this)
    this.token = new AuthToken(this)
    this.thirdParty = new ThirdParty(this.options, this)
    this.account.setRememberLoginInfo()
  }
  Qs = require('query-string')
  account: Account
  verify: Verify
  token: AuthToken
  thirdParty: ThirdParty
  appAuthenticationStr = 'App-Authentication'
  getRequestHeader(sessionToken?: string) {
    let token = this.getAccessToken()
    if (sessionToken) {
      token = sessionToken
    }
    return {
      Authorization: `Mship ${token}`,
      [this.appAuthenticationStr]: `Basic ${this.options.appKey}`
    }
  }
  async doLoginWxH5() {
    let isAllowedByFrontend = false
    const configStr = ConfigCenterModule.getFrontendApplication(frontendApplication.newRedirectSwitch)
    if (configStr) {
      const newRedirectSwitchConfig = JSON.parse(configStr) as NewRedirectSwitchConfig
      const curServicerId = Context.servicerInfo.id
      if (newRedirectSwitchConfig.switch === 'true') {
        isAllowedByFrontend = true
      }
      if (newRedirectSwitchConfig.switch === 'false') {
        const { includeServierId } = newRedirectSwitchConfig
        if (includeServierId && includeServierId.length) {
          if (includeServierId.includes(curServicerId)) {
            isAllowedByFrontend = true
          }
        }
      }
    }
    if (isAllowedByFrontend) {
      await this.getRedirectUrlByFrontend()
    } else {
      await this.getRedirectUrlByNginx()
    }
  }

  /**
   * 获取重定向地址
   * @description 通过前端
   */
  async getRedirectUrlByFrontend() {
    // const webPortal = OnlineSchoolConfigModule.queryOnlineSchoolConfigFactory.queryWebPortalConfig.webPortal
    // 阿波罗拿配置，appid和回调
    const register = OnlineSchoolConfigModuleH5.mutationFunctionalitySettingFactory.registerAndLogin
    if (!register?.loginSetting?.authSeetingVo?.appId) {
      await register.queryLoginSetting()
    }
    const APP_ID = register?.loginSetting?.authSeetingVo?.appId
    const serviceId = Context.servicerInfo.id + 'h5' //服务商ID拼接H5
    const isDemo = true
    const webEnv = Env.curProxyEnv

    // 转换短码参数
    const convertShortParam = new UrlConvertParam()
    convertShortParam.envInfo = new EnvInfo()

    const innerNetworkRedirectDomain = 'http://www.fjhb.cn' //'http://www.fjhb.cn:2457'
    const redirectRouterPath = '/url-transfer'
    const platformFlag = '/jxjy'
    let jxjyPrefix = ''
    let shortCode = ''

    // H5授权重定向域名
    let REDIRECT_URI = ''
    const specialTopicId = Context.businessEnvironment.specialTopicsInfo?.id
    if (Env.isProxyInnerNetworkEnv) {
      jxjyPrefix = `${platformFlag}-${webEnv}`
      convertShortParam.envInfo.isInnerNetwork = true
      convertShortParam.envInfo.curEnv = webEnv
      if (specialTopicId) {
        // 有专题ID说明是专题来的
        convertShortParam.domainType = DomainTypeEnumEnum.zt
        convertShortParam.id = specialTopicId
        convertShortParam.callbackUrl = `${window.location.origin}/h5`
        shortCode = await CryptUtil.encrypt<UrlConvertParam>(convertShortParam)
      } else {
        convertShortParam.domainType = DomainTypeEnumEnum.wx
        convertShortParam.id = serviceId
        convertShortParam.callbackUrl = `${window.location.origin}/h5`
        shortCode = await CryptUtil.encrypt<UrlConvertParam>(convertShortParam)
      }
      if (Context.currentDomain === DomainTypeEnum.distribution) {
        convertShortParam.domainType = DomainTypeEnumEnum.fx
        convertShortParam.id = Context.fxPortalInfo.portalId
        convertShortParam.callbackUrl = `${window.location.origin}/h5/#/pages/distribution/home/<USER>
        shortCode = await CryptUtil.encrypt<UrlConvertParam>(convertShortParam)
      }
      REDIRECT_URI = `${innerNetworkRedirectDomain}${jxjyPrefix}${redirectRouterPath}/${shortCode}`
    } else {
      convertShortParam.envInfo.isInnerNetwork = false
      if (isDemo && APP_ID == 'wx1f30614281c34264') {
        if (specialTopicId) {
          // 有专题ID说明是专题来的
          convertShortParam.domainType = DomainTypeEnumEnum.zt
          convertShortParam.id = specialTopicId
          convertShortParam.callbackUrl = `${window.location.origin}/h5`
          shortCode = await CryptUtil.encrypt<UrlConvertParam>(convertShortParam)
        } else {
          convertShortParam.domainType = DomainTypeEnumEnum.wx
          convertShortParam.id = serviceId
          convertShortParam.callbackUrl = `${window.location.origin}/h5`
          shortCode = await CryptUtil.encrypt<UrlConvertParam>(convertShortParam)
        }
        if (Context.currentDomain === DomainTypeEnum.distribution) {
          convertShortParam.domainType = DomainTypeEnumEnum.fx
          convertShortParam.id = Context.fxPortalInfo.portalId
          convertShortParam.callbackUrl = `${window.location.origin}/h5/#/pages/distribution/home/<USER>
          shortCode = await CryptUtil.encrypt<UrlConvertParam>(convertShortParam)
        }
        REDIRECT_URI = `${window.location.protocol}//jxjy.59iedu.com${redirectRouterPath}/${shortCode}`
      } else {
        if (specialTopicId) {
          // 有专题ID说明是专题来的
          let specialTopicName = ' '
          Context.businessEnvironment.specialTopicsInfo.netSchoolDomainName.forEach(item => {
            if (item.portalType === 1) {
              specialTopicName = item.netSchoolDomainName
            }
          })
          convertShortParam.domainType = DomainTypeEnumEnum.zt
          convertShortParam.id = specialTopicId
          convertShortParam.callbackUrl = `${window.location.origin}/h5`
          shortCode = await CryptUtil.encrypt<UrlConvertParam>(convertShortParam)
          REDIRECT_URI = `${window.location.protocol}//${specialTopicName}${redirectRouterPath}/${shortCode}`
        } else {
          convertShortParam.domainType = DomainTypeEnumEnum.wx
          convertShortParam.id = serviceId
          convertShortParam.callbackUrl = `${window.location.origin}/h5`
          shortCode = await CryptUtil.encrypt<UrlConvertParam>(convertShortParam)
          REDIRECT_URI = `${window.location.origin}${redirectRouterPath}/${shortCode}`
        }
        if (Context.currentDomain === DomainTypeEnum.distribution) {
          await QueryWebPortalConfig.queryWebPortalConfig()
          convertShortParam.domainType = DomainTypeEnumEnum.fx
          convertShortParam.id = Context.fxPortalInfo.portalId
          convertShortParam.callbackUrl = `${window.location.origin}/h5/#/pages/distribution/home/<USER>
          shortCode = await CryptUtil.encrypt<UrlConvertParam>(convertShortParam)
          const currentDomain = QueryWebPortalConfig.webPortal.domainName || ''
          REDIRECT_URI = `${window.location.protocol}//${currentDomain}${redirectRouterPath}/${shortCode}`
        }
      }
    }
    // const REDIRECT_URI = ConfigCenterModule.getFrontendApplication(frontendApplication.redirectUrl)
    console.log(REDIRECT_URI, 'REDIRECT_URI')
    REDIRECT_URI = encodeURIComponent(REDIRECT_URI)
    const url =
      'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
      APP_ID +
      '&redirect_uri=' +
      REDIRECT_URI +
      '&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'
    // window.open(url)
    location.href = url
  }

  /**
   * 获取重定向地址
   * @description 通过Nginx
   */
  async getRedirectUrlByNginx() {
    // const webPortal = OnlineSchoolConfigModule.queryOnlineSchoolConfigFactory.queryWebPortalConfig.webPortal
    // 阿波罗拿配置，appid和回调
    const register = OnlineSchoolConfigModuleH5.mutationFunctionalitySettingFactory.registerAndLogin
    if (!register?.loginSetting?.authSeetingVo?.appId) {
      await register.queryLoginSetting()
    }
    const APP_ID = register?.loginSetting?.authSeetingVo?.appId
    const serviceId = Context.servicerInfo.id + 'h5' //服务商ID拼接H5
    const isDemo = true
    const webEnv = Env.curProxyEnv

    // H5授权重定向域名
    let REDIRECT_URI = ''
    const specialTopicId = Context.businessEnvironment.specialTopicsInfo?.id
    if (Env.isProxyInnerNetworkEnv) {
      if (specialTopicId) {
        // 有专题ID说明是专题来的
        REDIRECT_URI = 'http://www.fjhb.cn/' + specialTopicId + 'h5' + webEnv
      } else {
        REDIRECT_URI = 'http://www.fjhb.cn/' + serviceId + webEnv
      }
      if (Context.currentDomain === DomainTypeEnum.distribution) {
        REDIRECT_URI = `http://www.fjhb.cn/${Context.fxPortalInfo.portalId}h5${webEnv}`
      }
    } else {
      if (isDemo && APP_ID == 'wx1f30614281c34264') {
        if (specialTopicId) {
          // 有专题ID说明是专题来的
          REDIRECT_URI = window.location.protocol + '//jxjy.59iedu.com/' + specialTopicId + 'h5'
        } else {
          REDIRECT_URI = window.location.protocol + '//jxjy.59iedu.com/' + serviceId
        }
        if (Context.currentDomain === DomainTypeEnum.distribution) {
          REDIRECT_URI = `${window.location.protocol}//jxjy.59iedu.com/${Context.fxPortalInfo.portalId}`
        }
      } else {
        if (specialTopicId) {
          // 有专题ID说明是专题来的
          let specialTopicName = ' '
          Context.businessEnvironment.specialTopicsInfo.netSchoolDomainName.forEach(item => {
            if (item.portalType === 1) {
              specialTopicName = item.netSchoolDomainName
            }
          })
          REDIRECT_URI = window.location.protocol + '//' + specialTopicName + '/' + specialTopicId + 'h5'
        } else {
          REDIRECT_URI = window.location.protocol + '//' + window.location.hostname + '/' + serviceId
        }
        if (Context.currentDomain === DomainTypeEnum.distribution) {
          await QueryWebPortalConfig.queryWebPortalConfig()
          const currentDomain = QueryWebPortalConfig.webPortal.domainName || ''
          REDIRECT_URI = `${window.location.protocol}//${currentDomain}/${Context.fxPortalInfo.portalId}`
        }
      }
    }
    // const REDIRECT_URI = ConfigCenterModule.getFrontendApplication(frontendApplication.redirectUrl)
    console.log(REDIRECT_URI, 'REDIRECT_URI')
    const url =
      'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
      APP_ID +
      '&redirect_uri=' +
      REDIRECT_URI +
      '&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'
    // window.open(url)
    location.href = url
  }

  /**
   * 微信H5登录
   * @param token
   */
  async loginWithWxH5Token(token: string) {
    const loginParams = new AuthTokenLoginParams()
    // 区分学员登录和集体登录
    localStorage.setItem('customer.Account-Type', '1')
    loginParams.authToken = token
    const result = await this.options.request.post(`${this.options.ssoUrl}${loginH5MsByAccountUrl}`, {
      token
    })
    if (typeof result?.status === 'number' && result?.status !== 200) {
      return Promise.reject(result)
    }
    if (result?.data?.code !== 200) {
      return Promise.reject(result)
    }
    this.setStoreTicket(result.data.data.serviceTicketId, result.data.data.ticketGrantTicketId)
    await this.auth()
    await UserModule.queryUserFactory.getQueryUserInfo().getCurrenUserInfo()
    return result
  }

  /**
   * 根据认证服务的TOKEN值，进行登录
   * @param
   */
  async ddLoginWithToken(token: string) {
    const result = await this.options.request.get(`${this.options.authUrl}${thirdTokenLoginUrl}${token}`)
    if (!result.status.isSuccess()) {
      return Promise.reject(result.status)
    }
    // this.storeTicket(result.data.data.serviceTicketId, result.data.data.ticketGrantTicketId)
    this.setStoreTicket(result.data.serviceTicketId, result.data.ticketGrantTicketId)
    return result
  }

  /**
   * 使用培训平台后台返回的authToken进行登录认证
   * @param token
   */
  async loginWithToken(token: string) {
    const loginParams = new AuthTokenLoginParams()
    loginParams.authToken = token
    const result = await this.options.request.post(`${this.options.ssoUrl}${authTokenLoginUrl}`, loginParams)
    if (result?.status !== 200) {
      return Promise.reject(result)
    }
    if (result?.data?.code !== 200) {
      return Promise.reject(result)
    }
    this.setStoreTicket(result.data.data.serviceTicketId, result.data.data.ticketGrantTicketId)
    return result
  }

  /**
   * 基于微信的unionId登录
   * @param shortMessageLoginParams
   */
  async weiXinUnionIdLogin(loginParams: WeiXinUnionIdLoginParams) {
    loginParams.service = this.options.service
    const result = await this.options.request.post(`${this.options.ssoUrl}${weiXinUnionIdLoginUrl}`, loginParams)
    if (!result.status.isSuccess()) {
      return Promise.reject(result)
    }
    this.setStoreTicket(result.data.data.serviceTicketId, result.data.data.ticketGrantTicketId)
    return result
  }

  // * 身份认证换取token
  async ssoAuth(needSetStorage = true) {
    const requestParams = this.Qs.stringify({
      [grantType]: identityAuthenticationToken,
      [identityAuthenticationToken]: this.token.identityAuthenticationToken
    })
    const url = `${this.options.ssoUrl}${identityAuthUrl}`
    const result = (await Cfetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': ContentType
      },
      credentials: 'include',
      body: requestParams
    })) as any
    const response = await result.json()
    if (needSetStorage) {
      this.ssoSetStorage(response)
    }
    return response
  }

  /**
   * fetch单点刷新token
   */
  async ssoRefreshToken(needSetStorage = true) {
    const toParsed = {
      [grantType]: GrantType.refreshToken,
      [refreshTokenKey]: this.token.getRefreshToken()
    }
    this.identityType = IdentityType.refresh
    const requestParams = this.Qs.stringify(toParsed)
    const url = `${this.options.ssoUrl}${identityAuthUrl}`
    const result = (await Cfetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': ContentType
      },
      credentials: 'include',
      body: requestParams
    })) as any
    const response = await result.json()
    if (needSetStorage) {
      this.ssoSetStorage(response)
    }
    return response
  }

  /**
   * fetch单点刷新token
   */
  async ssoRefreshFxToken(needSetStorage = true) {
    const toParsed = {
      [grantType]: GrantType.refreshToken,
      [refreshTokenKey]: localStorage.getItem('customer.Fx-Refresh-Token')
    }
    this.identityType = IdentityType.refresh
    const requestParams = this.Qs.stringify(toParsed)
    const url = `${this.options.ssoUrl}${identityAuthUrl}`
    const result = (await Cfetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': ContentType
      },
      serviceCapability: 'fx',
      credentials: 'include',
      body: requestParams
    })) as any
    const response = await result.json()
    const formattingResponse = this.formattingFetchResponse(response)

    localStorage.setItem('customer.Fx-Access-Token', formattingResponse.data.access_token)
    localStorage.setItem('customer.Fx-Refresh-Token', formattingResponse.data.refresh_token)
    return response
  }

  /**
   * fetch单点切换token
   */
  async ssoTransformToken(needSetStorage = true) {
    const requestParams = this.Qs.stringify({
      [grantType]: GrantType.identityTransformToken,
      identity_authentication_token: this.token.identityAuthenticationToken
    })
    const url = `${this.options.ssoUrl}${identityAuthUrl}`
    const result = (await Cfetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': ContentType
      },
      credentials: 'include',
      body: requestParams
    })) as any
    const response = await result.json()
    if (needSetStorage) {
      this.ssoSetStorage(response)
    }
    return response
  }

  /**
   * fetch单点代理token
   */
  async proxyAuth() {
    const requestParams = this.Qs.stringify({
      [grantType]: GrantType.identityAuthenticationToken,
      [GrantType.identityAuthenticationToken]: this.token.identityAuthenticationToken
    })
    const url = `${this.options.ssoUrl}${identityAuthUrl}`
    const result = (await Cfetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': ContentType
      },
      credentials: 'include',
      body: requestParams
    })) as any
    const response = await result.json()
    return response
  }

  ssoSetStorage(result: any) {
    const accessToken = result.data?.access_token
    const refreshToken = result.data?.refresh_token
    if (accessToken) {
      this.token.setAccessToken(accessToken)
    }
    if (refreshToken) {
      this.token.setRefreshToken(refreshToken)
    }
    // * 塞前缀
    if (result.data?.token_type) {
      const token_type = result.data?.token_type
      localStorage.setItem('tokenType', `${token_type.substring(0, 1).toUpperCase() + token_type.substring(1)}`)
    }
    // * 设置过期时间
    if (result.data?.expires_in) {
      const expires_in = result.data?.expires_in * 1000
      const time = new Date().getTime() + expires_in
      localStorage.setItem(`${this.options.prefix}.ExpiresIn`, String(time))
    }
    return result
  }

  /**
   * fetch单点退出
   */
  async ssoLogout() {
    const url = `${this.options.ssoUrl}${identityLogoutUrl}`
    const result = (await Cfetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': ContentType
      },
      credentials: 'include'
    })) as any
    this.removeToken()
    console.log('resetUserInfo1')
    QueryUserInfo.resetUserInfo(this.options.prefix as BusinessClientTypeEnum)
    return result
  }

  /**
   * 分销再认证——学员使用
   */
  async applyDistributorReAuthenticateAndLogin() {
    const accessToken = this.token.getAccessToken()
    const params = new ApplyDistributorReAuthenticateRequest()
    params.accessToken = accessToken
    const applyDistributorReAuthenticateResponse = await JxjypxtyptIdentityAuth.applyDistributorReAuthenticate(
      params,
      ContextTypeEnums.fx
    )
    if (
      applyDistributorReAuthenticateResponse.data.code != '200' ||
      !applyDistributorReAuthenticateResponse.status.isSuccess()
    ) {
      console.error('获取分销再认证身份凭证失败', applyDistributorReAuthenticateResponse)
      return applyDistributorReAuthenticateResponse
    }
    this.token.identityAuthenticationToken = applyDistributorReAuthenticateResponse.data.identityAuthenticationToken
    const response = this.formattingFetchResponse(await this.ssoAuth(false))
    if (!response.data.access_token) {
      console.error('获取分销访问令牌失败', response)
      return response
    }
    // 分销令牌信息
    localStorage.setItem('customer.Fx-Access-Token', response.data.access_token)
    localStorage.setItem('customer.Fx-Refresh-Token', response.data.refresh_token)
    return response
  }

  /**
   * 格式化ssoAuth返回值
   * @param authResponse
   * @param fetchResponse
   * @param time
   * @private
   */
  private formattingFetchResponse(authResponse: any, fetchResponse?: any, time?: number): Response<AuthResponse> {
    const response = new Response<AuthResponse>()
    response.status.code = authResponse?.code || fetchResponse?.status
    response.status.setHttpCode(authResponse?.code || fetchResponse?.status)
    response.status.message = authResponse?.message || fetchResponse?.statusText
    response.data = authResponse?.data
    if (!response.data?.code && response.data) {
      response.data['code'] = authResponse?.code || fetchResponse?.status
    } else {
      response.data = {} as AuthResponse
      response.data['code'] = authResponse?.code || fetchResponse?.status
    }
    if (time && response.data?.time) {
      response.data['time'] = time
    }
    return response
  }

  /**
   * 获取分销访问令牌——学员使用
   */
  get fxAccessToken() {
    return localStorage.getItem('customer.Fx-Access-Token')
  }

  /**
   * 获取分销刷新令牌——学员使用
   */
  get fxRefreshToken() {
    return localStorage.getItem('customer.Fx-Refresh-Token')
  }

  /**
   * 选择单位
   * @param item 分销单位信息
   */
  async selectUnit(item: DistributionUnitInformation) {
    const params = new ChangeIdentityAuthenticationParams()
    params.applicationMemberType = item.applicationMemberType
    params.applicationMemberId = item.applicationMemberId
    const response = await this.getIdentityAuthentication(IdentityType.change_identity, params)
    if (response.data.code != 200 || !response.data.identityAuthenticationToken) {
      console.error('获取分销身份凭证失败', response)
      return response
    }
    const authResponse = this.formattingFetchResponse(await this.ssoTransformToken())
    if (!authResponse.data.accessToken) {
      console.error('获取访问令牌失败', authResponse)
      return authResponse
    }
    localStorage.setItem('currentUnitId', item.unitId)
    return authResponse
  }

  /**
   * 获取访问令牌配后的值
   * @param token
   */
  async getAccessTokenValue(token?: string) {
    if (!token) {
      token = this.token.getAccessToken()
    }
    return identityAuthentication.getAccessTokenValue(token)
  }

  /**
   * 分销登录
   * @param identityType 登录类型
   * @param basicLoginParams 参数
   */
  async doFxLogin(
    identityType: IdentityType,
    basicLoginParams: LoginParams
  ): Promise<ResponseRes<IdentityAuthenticationTokenModel>> {
    let params
    let identityAuthenticationRes = new ResponseRes<IdentityAuthenticationTokenResponse>()
    let response = new ResponseRes<IdentityAuthenticationTokenModel>()
    response.data = new IdentityAuthenticationTokenModel()
    this.identityType = identityType
    switch (identityType) {
      case IdentityType.account_pwd_captcha: {
        // * 账户密码验证码
        params = new AccountPwdCaptchaCredentialForFXSRequest()
        params.identity = basicLoginParams.identity || basicLoginParams.account || basicLoginParams.phoneNumber
        params.password = basicLoginParams.password
        params.captchaValue = basicLoginParams.captchaValue
        params.token = basicLoginParams.token || this.verify.captchaToken
        params.verifyCaptchaData = basicLoginParams.sliderCaptchaValue || undefined
        if (basicLoginParams.grantType) {
          params.grantType = basicLoginParams.grantType
        }

        identityAuthenticationRes = await identityAuthentication.applyAuthenticateByAccountPwdCaptchaForFXS(
          params,
          ContextTypeEnums.fx
        )
        break
      }
      case IdentityType.sms_code: {
        // * 短信验证码
        params = new SmsCodeCredentialForFXSRequest()
        params.token = basicLoginParams.token || this.verify.shortMessageCaptchaToken
        params.smsCode = basicLoginParams.smsCode
        if (!basicLoginParams.grantType) {
          params.phone = basicLoginParams.identity || basicLoginParams.account || basicLoginParams.phoneNumber
        }
        identityAuthenticationRes = await identityAuthentication.applyAuthenticateBySmsCodeForFXS(
          params,
          ContextTypeEnums.fx
        )
        break
      }
      default: {
        response = new ResponseRes<IdentityAuthenticationTokenModel>()
        break
      }
    }
    // * 判断身份认证是否成功
    response.status = identityAuthenticationRes.status
    response.data.addIdentityAuthenticationResponse(identityAuthenticationRes.data as any)
    if (!identityAuthenticationRes.data?.identityAuthenticationToken) {
      console.error('身份认证失败', identityAuthenticationRes)
      return response
    }
    this.token.identityAuthenticationToken = identityAuthenticationRes.data?.identityAuthenticationToken
    if (!this.isSeparateLogin) {
      // * 认证流程合并
      if (identityType != IdentityType.old) {
        const authResponse = await this.fetchLogin.ssoAuth()
        response.status = authResponse.status
        response.data.addAuthResponse(authResponse)
        if (!authResponse.data?.access_token || authResponse.data?.code != 200) {
          console.error('获取代理令牌报错 ssoAuth', authResponse)
        }
      }
    }
    return response
  }

  /**
   * 账号密码图形验证码认证
   * @param authenticationParams 参数
   */
  async doAuthentication(authenticationParams: LoginParams): Promise<ResponseRes<IdentityAuthenticationTokenResponse>> {
    const params = new AccountPwdCaptchaCredentialRequest()
    let identityAuthenticationRes = new ResponseRes<IdentityAuthenticationTokenResponse>()
    const response = new ResponseRes<IdentityAuthenticationTokenResponse>()
    response.data = new IdentityAuthenticationTokenResponse()
    params.identity = authenticationParams.identity || authenticationParams.account || authenticationParams.phoneNumber
    params.password = authenticationParams.password
    params.captchaValue = authenticationParams.captchaValue
    params.token = authenticationParams.token || this.verify.captchaToken
    params.grantType = 'identity_chain_auth_token'
    params.verifyCaptchaData = authenticationParams.sliderCaptchaValue
    identityAuthenticationRes = await identityAuthentication.applyAuthenticateByAccountPwdCaptcha(params)
    response.status = identityAuthenticationRes.status
    response.data = identityAuthenticationRes.data
    return response
  }

  /**
   * 账号密码认证
   * @param authenticationParams 参数
   */
  async doAuthenticationPwd(
    authenticationParams: LoginParams
  ): Promise<ResponseRes<IdentityAuthenticationTokenResponse>> {
    const params = new AccountPwdCredentialRequest()
    let identityAuthenticationRes = new ResponseRes<IdentityAuthenticationTokenResponse>()
    const response = new ResponseRes<IdentityAuthenticationTokenResponse>()
    response.data = new IdentityAuthenticationTokenResponse()
    params.identity = authenticationParams.identity || authenticationParams.account || authenticationParams.phoneNumber
    params.password = authenticationParams.password
    params.token = authenticationParams.token || this.verify.captchaToken
    params.grantType = 'identity_chain_auth_token'
    identityAuthenticationRes = await identityAuthentication.applyAuthenticateByAccountPwd(params)
    response.status = identityAuthenticationRes.status
    response.data = identityAuthenticationRes.data
    return response
  }

  /**
   * 手机短信验证认证
   * @param token 参数
   */
  async doApplyAuthenticateBySmsCode(
    smsParams: SmsCodeCredentialRequest
  ): Promise<ResponseRes<IdentityAuthenticationTokenResponse>> {
    let identityAuthenticationRes = new ResponseRes<IdentityAuthenticationTokenResponse>()
    const response = new ResponseRes<IdentityAuthenticationTokenResponse>()
    response.data = new IdentityAuthenticationTokenResponse()
    identityAuthenticationRes = await identityAuthentication.applyAuthenticateBySmsCode(smsParams)
    response.status = identityAuthenticationRes.status
    response.data = identityAuthenticationRes.data
    if (!identityAuthenticationRes.data?.identityAuthenticationToken) {
      console.error('身份认证失败', identityAuthenticationRes)
      return response
    }
    this.token.identityAuthenticationToken = identityAuthenticationRes.data?.identityAuthenticationToken
    return response
  }
}
