import { SubOrderDTO } from '@api/gateway/PlatformTrade'
/**
 * @description: 培训方案商品信息
 */
export default class SchemeCommodity {
  /**
   * 购买数量
   */
  subOrderPurchaseQuantity: number
  /**
   * 商品id
   */
  productId: string
  /**
   * 商品标价
   */
  subOrderLabelPrice: number
  /**
   * 所属培训方案id
   */
  schemeId: string
  /**
   * 方案展示图片
   */
  schemePicture: string
  /**
   * 所属培训方案名称
   */
  schemeName: string

  /**
   * 培训学时
   */
  trainingHours: string
  /**
   * 适用人群
   */
  suitableCrowNames: Array<string>
  /**
   * 培训类别Id path
   */
  trainingTypeIdPath: string
  /**
   * 培训类别
   */
  trainingTypeName: string
  /**
   * 培训类别名称/name1/name2/name3
   */
  trainingTypeNamePath: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeName: string
  /**
   * 培训工种类别/培训工种id path
     用于培训类别联合工种多条件查询
    */
  workTypeIdPath: string
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
  /**
   * 所属期数id
   */
  issueId: string
}

/**
 * @description: 转换SchemeCommodity对象工具类
 */
export class SchemeCommodityParse {
  private constructor() {
    // noop
  }

  static parseSchemeCommodity(subOrderDto: SubOrderDTO) {
    const schemeCommodity = new SchemeCommodity()
    this.parsePrimitiveType(schemeCommodity, subOrderDto)
    // .....
    return schemeCommodity
  }

  /**
   * @description: 解析基本类型
   */

  private static parsePrimitiveType(schemeCommodity: SchemeCommodity, subOrderDto: SubOrderDTO): void {
    schemeCommodity.productId = subOrderDto.productId
    schemeCommodity.schemeId = subOrderDto.schemeId
    schemeCommodity.schemeName = subOrderDto.schemeName
    schemeCommodity.schemePicture = subOrderDto.schemePicture
    schemeCommodity.subOrderLabelPrice = subOrderDto.subOrderLabelPrice
    schemeCommodity.subOrderPurchaseQuantity = subOrderDto.subOrderPurchaseQuantity
    schemeCommodity.suitableCrowNames = subOrderDto.suitableCrowNames
    schemeCommodity.trainingHours = subOrderDto.trainingHours
    schemeCommodity.trainingTypeId = subOrderDto.trainingTypeId
    schemeCommodity.trainingTypeIdPath = subOrderDto.trainingTypeIdPath
    schemeCommodity.trainingTypeName = subOrderDto.trainingTypeName
    schemeCommodity.trainingTypeNamePath = subOrderDto.trainingTypeNamePath
    schemeCommodity.workTypeId = subOrderDto.workTypeId
    schemeCommodity.workTypeIdPath = subOrderDto.workTypeIdPath
    schemeCommodity.workTypeName = subOrderDto.workTypeName
    schemeCommodity.issueId = subOrderDto.issueId
  }
}
