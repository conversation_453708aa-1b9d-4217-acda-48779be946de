"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""批量打印接口(包含校验完善信息)"""
	batchPrintCertificate(request:CertificateBatchPrintRequest):String
	"""单个打印接口(包含校验完善信息)"""
	singlePrintCertificate(request:SinglePrintCertificateRequest):SinglePrintCertificateReponse
	"""学员端打印证书(需要判断是否强制调查问卷)"""
	studentPrintCertificate(request:SinglePrintCertificateRequest):SinglePrintCertificateReponse
}
input DateScopeRequest @type(value:"com.fjhb.ms.query.commons.DateScopeRequest") {
	begin:DateTime
	end:DateTime
}
input DoubleScopeRequest @type(value:"com.fjhb.ms.query.commons.DoubleScopeRequest") {
	begin:Double
	end:Double
}
input StudentSchemeLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningRequest") {
	studentNoList:[String]
	student:UserRequest
	learningRegister:LearningRegisterRequest
	scheme:SchemeRequest
	studentLearning:StudentLearningRequest
	dataAnalysis:DataAnalysisRequest
	connectManageSystem:ConnectManageSystemRequest
	extendedInfo:ExtendedInfoRequest
	openPrintTemplate:Boolean
	saleChannels:[Int]
	trainingChannelName:String
	trainingChannelId:String
	notDistributionPortal:Boolean
	trainingType:String
	issueId:String
}
input StudentSchemeLearningSortRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.StudentSchemeLearningSortRequest") {
	field:StudentSchemeLearningSortField
	policy:SortPolicy
}
input ConnectManageSystemRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ConnectManageSystemRequest") {
	syncStatus:Int
}
input DataAnalysisRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.DataAnalysisRequest") {
	trainingResultPeriod:DoubleScopeRequest
	requirePeriod:DoubleScopeRequest
	acquiredPeriod:DoubleScopeRequest
}
input ExtendedInfoRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.ExtendedInfoRequest") {
	whetherToPrint:Boolean
	applyCompanyCode:String
	policyTrainingSchemeId:String
	policyTrainingSchemeName:String
}
input LearningRegisterRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.LearningRegisterRequest") {
	registerType:Int
	sourceType:String
	sourceId:String
	status:[Int]
	registerTime:DateScopeRequest
	saleChannels:[Int]
	orderNoList:[String]
	subOrderNoList:[String]
	batchOrderNoList:[String]
	distributorId:String
	portalId:String
}
input RegionRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.RegionRequest") {
	province:String
	city:String
	county:String
}
input StudentLearningRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.learning.StudentLearningRequest") {
	trainingResultList:[Int]
	trainingResultTime:DateScopeRequest
	notLearningTypeList:[Int]
	courseScheduleStatus:Int
	examAssessResultList:[Int]
}
input RegionSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertyRequest") {
	province:String
	city:String
	county:String
}
input RegionSkuPropertySearchRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.RegionSkuPropertySearchRequest") {
	region:[RegionSkuPropertyRequest]
	regionSearchType:Int
}
input SchemeRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeRequest") {
	schemeId:String
	schemeIdList:[String]
	schemeType:String
	schemeName:String
	skuProperty:SchemeSkuPropertyRequest
}
input SchemeSkuPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.scheme.SchemeSkuPropertyRequest") {
	year:[String]
	regionSkuPropertySearch:RegionSkuPropertySearchRequest
	industry:[String]
	subjectType:[String]
	trainingCategory:[String]
	trainingProfessional:[String]
	technicalGrade:[String]
	positionCategory:[String]
	trainingObject:[String]
	jobLevel:[String]
	jobCategory:[String]
	subject:[String]
	grade:[String]
	learningPhase:[String]
	discipline:[String]
	qualificationCategory:[String]
	trainingWay:[String]
	trainingInstitution:[String]
	mainAdditionalItem:[String]
}
input UserPropertyRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserPropertyRequest") {
	regionList:[RegionRequest]
	companyName:String
	payOrderRegionList:[RegionRequest]
}
input UserRequest @type(value:"com.fjhb.ms.scheme.learning.query.front.gateway.jxjy.gateway.request.nested.user.UserRequest") {
	userIdList:[String]
	accountIdList:[String]
	userProperty:UserPropertyRequest
}
input Extend @type(value:"com.fjhb.platform.jxjy.v1.api.certificate.request.Extend") {
	key:String
	value:String
}
input ImportStudentBatchPrintingRequest @type(value:"com.fjhb.platform.jxjy.v1.api.certificate.request.ImportStudentBatchPrintingRequest") {
	name:String
	idCard:String
	schemeName:String
	importStatus:Int!
}
input JXJYJXCertificatePrintRequest @type(value:"com.fjhb.platform.jxjy.v1.api.certificate.request.JXJYJXCertificatePrintRequest") {
	templateId:String
	fileType:Int!
	printType:Int!
	merge:String
	printSource:Int!
	printPort:Int!
}
input CertificateBatchPrintRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.CertificateBatchPrintRequest") {
	"""打印请求"""
	jxjyjxCertificatePrintRequest:JXJYJXCertificatePrintRequest
	"""查询条件"""
	studentSchemeLearningRequest:StudentSchemeLearningRequest
	"""查询排序条件"""
	sort:[StudentSchemeLearningSortRequest]
	"""打印类型 1-导入 2-正常"""
	importStudentPrint:Int!
	importStudentBatchPrintingRequest:ImportStudentBatchPrintingRequest
}
"""江西单个打印接口请求
	<AUTHOR>
	@date 2024/8/29 8:43
"""
input SinglePrintCertificateRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.request.SinglePrintCertificateRequest") {
	"""参训资格id"""
	qualificationId:String
	"""学号"""
	studentNo:String
	"""文件类型 1-PDF   2-IMAGE
		@see FileTypes
	"""
	fileType:Int!
	"""扩展数据
		key 对应courseId
	"""
	data:[Extend]
	"""方案id"""
	schemeId:String
}
enum SortPolicy @type(value:"com.fjhb.ms.scheme.learning.query.kernel.constants.SortPolicy") {
	ASC
	DESC
}
enum StudentSchemeLearningSortField @type(value:"com.fjhb.ms.scheme.learning.query.kernel.constants.StudentSchemeLearningSortField") {
	REGISTER_TIME
	SCHEME_YEAR
}
"""江西单个打印返回值
	<AUTHOR>
	@date 2024/8/29 9:34
"""
type SinglePrintCertificateReponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.response.SinglePrintCertificateReponse") {
	"""code"""
	code:String
	"""message"""
	message:String
	"""data"""
	data:String
	"""是否需要强制完成调查问卷"""
	needForceQuestionnaire:Boolean!
	"""需要强制完成的调查问卷信息"""
	unaccomplishedQuestionnaire:[UnaccomplishedQuestionnaire]
}
"""未完成问卷信息
	<AUTHOR>
	@date 2025/4/15 10:15
"""
type UnaccomplishedQuestionnaire @type(value:"com.fjhb.platform.jxjypxtypt.dif.jxgx.v1.kernel.geteway.response.dto.UnaccomplishedQuestionnaire") {
	"""未完成问卷id"""
	unaccomplishedQuestionnaireId:String
	"""学习方式id"""
	learningId:String
	"""允许开始时间"""
	allowStartTime:DateTime
	"""允许结束时间"""
	allowEndTime:DateTime
}

scalar List
