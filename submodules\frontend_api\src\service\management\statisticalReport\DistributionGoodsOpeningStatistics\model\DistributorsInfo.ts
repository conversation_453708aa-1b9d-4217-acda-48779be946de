import TrainingAttributes from '@api/service/management/statisticalReport/DistributionGoodsOpeningStatistics/model/TrainingAttributes'
import DistributionTiersEnumClass from '@api/service/management/statisticalReport/enums/DistributionTiersEnumClass'

export default class DistributorsInfo {
  /**
   * 分销商品名称
   */
  goodsName = ''
  /**
   * 分销商ID
   */
  distributorsInfoId = ''
  /**
   * 分销商名称
   */
  distributorsInfoIdName = ''
  /**
   * 分销商等级
   */
  distributorRegistration = new DistributionTiersEnumClass()
  /**
   * 培训属性
   */
  trainingAttributes: string[] = []
}
