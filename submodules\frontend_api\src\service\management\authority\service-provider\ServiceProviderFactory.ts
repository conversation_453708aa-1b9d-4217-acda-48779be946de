import QueryServiceProvider from '@api/service/management/authority/service-provider/query/QueryServiceProvider'
import MutationServiceProvider from '@api/service/management/authority/service-provider/mutation/MutationServiceProvider'

class ServiceProviderFactory {
  queryServiceProvider: QueryServiceProvider = new QueryServiceProvider()

  mutationServiceProvider: MutationServiceProvider = new MutationServiceProvider()
}

export default ServiceProviderFactory
