import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 问卷状态枚举
 * enabled 启用
 * disabled 停用
 */
export enum QuestionnaireStatusEnum {
  enabled = 1,
  disabled = 2
}

/**
 * @description 问卷状态
 */
class QuestionnaireStatus extends AbstractEnum<QuestionnaireStatusEnum> {
  static enum = QuestionnaireStatusEnum

  constructor(status?: QuestionnaireStatusEnum) {
    super()
    this.current = status
    this.map.set(QuestionnaireStatusEnum.enabled, '启用')
    this.map.set(QuestionnaireStatusEnum.disabled, '停用')
  }
}

export default new QuestionnaireStatus()
