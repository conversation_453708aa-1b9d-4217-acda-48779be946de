/**
 * @description 图形验证信息
 */
class PicCaptchaInfo {
  /**
   * 票据
   */
  private _ticket: string

  /**
   * 信任凭证
   */
  private _token: string

  /**
   * 设置票据
   * @param {string} value - 票据
   * @return
   */
  set ticket(value: string) {
    this._ticket = value
  }

  /**
   * 获取票据
   */
  get ticket(): string {
    return this._ticket
  }

  /**
   * 设置信任凭证
   * @param {string} value - 信任凭证
   * @return
   */
  set token(value: string) {
    this._token = value
  }

  /**
   * 获取信任凭证
   */
  get token(): string {
    return this._token
  }
}

export default new PicCaptchaInfo()
