import { ResponseStatus } from '@hbfe/common'
import TimeLength from './vo/videoPlay/TimeLength'

class MutationVideoPlay {
  /**
   * 全网校的防录屏跑马灯
   */
  horseRaceLampEnable = true
  /**
   * 视频贴片
   */
  imgUrl = ''
  /**
   * 贴片播放时长
   */
  timeLength = new TimeLength()

  queryDetail(): Promise<ResponseStatus> {
    return Promise.resolve(new ResponseStatus(100, ''))
  }

  doSave(): Promise<ResponseStatus> {
    return Promise.resolve(new ResponseStatus(100, ''))
  }
}
export default MutationVideoPlay
