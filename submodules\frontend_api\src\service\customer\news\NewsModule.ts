/*
 * @Description: 资讯
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-24 17:29:29
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-02-14 13:54:14
 */
import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import QueryNewsFactory from '@api/service/customer/news/query/QueryNewsFactory'
/**
 * @description 资讯中控层
 */
@Module({
  name: 'NewsModule',
  dynamic: true,
  namespaced: true,
  store
})
class NewsModule extends VuexModule {
  //   NOTE
  queryNewsFactory: QueryNewsFactory = new QueryNewsFactory()
}

export default getModule(NewsModule)
