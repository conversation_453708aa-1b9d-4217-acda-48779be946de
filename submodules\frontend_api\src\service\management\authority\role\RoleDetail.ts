import MsBasicdataDomainGatewayV1 from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import RoleBaseInfo from '@api/service/management/authority/role/RoleBaseInfo'

class RoleDetail extends RoleBaseInfo {
  get id(): string {
    return this._id
  }

  loadedSelected = false

  private _id: string

  constructor(id?: string) {
    super()
    this._id = id
  }

  async query() {
    const result = await MsBasicdataDomainGatewayV1.findRoleById(this.id)
    this._id = result.data.id
    this.name = result.data.name
    this.description = result.data.description
    this.category = result.data.category
    const { data } = await MsBasicdataDomainGatewayV1.findFunctionalAuthorityByRoleIdNew(this.id)
    this.loadedSelected = true
    if (data) {
      this.functionalAuthorityIds = data.securityObjectResponses?.map(remote => remote.id)
    }
  }
}

export default RoleDetail
