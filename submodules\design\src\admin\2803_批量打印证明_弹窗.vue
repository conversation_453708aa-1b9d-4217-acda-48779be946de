<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--培训成果未推送名单-->
        <el-button type="primary" @click="dialog9 = true" class="f-mr20">培训成果未推送名单</el-button>
        <el-dialog
          title="以下人员培训成果未推送，无法打印培训证明"
          :visible.sync="dialog9"
          width="450px"
          class="m-dialog"
        >
          <div class="m-name-list">
            <div class="item">
              <span class="num">01</span><span class="name">李黎里</span><span class="id-num">35965478963254125</span>
            </div>
            <div class="item">
              <span class="num">02</span><span class="name">李黎</span><span class="id-num">35965478963254125</span>
            </div>
            <div class="item">
              <span class="num">03</span><span class="name">李黎里</span><span class="id-num">35965478963254125</span>
            </div>
            <div class="item">
              <span class="num">04</span><span class="name">李黎里</span><span class="id-num">35965478963254125</span>
            </div>
          </div>
        </el-dialog>

        <!--系统异常提醒-->
        <el-button type="primary" @click="dialog8 = true" class="f-mr20">系统异常提醒</el-button>
        <el-dialog :show-close="false" :visible.sync="dialog8" width="450px" class="m-dialog">
          <div class="dialog-alert is-big f-pb30">
            <i class="icon el-icon-warning warning"></i>
            <div class="txt">
              <p class="f-fb f-f16">系统异常，请联系平台技术支撑方。</p>
            </div>
          </div>
        </el-dialog>

        <!--批量导入-->
        <el-button type="primary" @click="dialog6 = true" class="f-mr20">批量导入</el-button>
        <el-dialog title="批量导入" :visible.sync="dialog6" width="500px" class="m-dialog">
          <div class="f-mb20 f-mlr50">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              操作提示：单次导入上限200条，如超过请分次导入，否则无法操作。
            </el-alert>
          </div>
          <div class="f-tc">
            <el-upload drag multiple>
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
            </el-upload>
            <div class="f-mt10 f-mlr50">
              <el-alert type="info" :closable="false" class="m-alert">
                或者您需要 <a href="#" class="f-link f-cb f-underline">下载模板</a>
              </el-alert>
            </div>
          </div>
          <div slot="footer">
            <el-button>下载模板</el-button>
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>

        <!--选择打印方式-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20">选择打印方式</el-button>
        <el-drawer
          title="请选择打印方式"
          :visible.sync="dialog4"
          :direction="direction"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt50">
                  <el-form-item>
                    <div slot="label">
                      <span class="f-vm">文件类型</span>
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                        <div slot="content">
                          <p>支持配置批量打印的类型：</p>
                          <p>1.连贯打印：本次批量打印的所有合格记录，是一份PDF，命名方式同导出文件名。</p>
                          <p>2.单个文件：每个学员对应的一条合格记录，是一份PDF，且以学员的姓名、证件号命名。</p>
                        </div>
                      </el-tooltip>
                      <span>：</span>
                    </div>
                    <el-radio-group v-model="form.resource">
                      <el-radio label="连贯打印"></el-radio>
                      <el-radio label="单个文件"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item>
                    <div slot="label">
                      <span class="f-vm">是否合并打印</span>
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                        <div slot="content">
                          <p>
                            连贯打印模式下，如打印的证明尺寸为四分之一的A4纸张或二分之一的A4纸，如需合并在一张A4纸上进行打印，是否合并打印请选择“是”
                          </p>
                          <p class="f-mt5">
                            <i class="f-co">注意：</i
                            >如本次需要打印的证明尺寸超过四分之一的A4纸张或二分之一的A4纸，合并打印不生效，打印出来的文件仍然是一张证明一张A4纸。
                          </p>
                        </div>
                      </el-tooltip>
                      <span>：</span>
                    </div>
                    <el-radio-group v-model="form.resource">
                      <el-radio label="是"></el-radio>
                      <el-radio label="否"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>

        <!--选择培训证明进行打印-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20">选择培训证明进行打印</el-button>
        <el-drawer
          title="选择培训证明进行打印"
          :visible.sync="dialog2"
          :direction="direction"
          size="700px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-row type="flex" justify="center">
              <el-col :span="18">
                <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt50">
                  <el-form-item label="模板类型：" required>
                    <el-radio-group v-model="form.resource">
                      <el-radio label="班级配置模板"></el-radio>
                      <el-radio label="其他模板"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item label="培训证明模板：" required>
                    <el-button type="primary" plain>选择模板</el-button>
                  </el-form-item>
                  <el-form-item label="培训证明模板：" required>
                    <i class="f-mr20">这里是选择的模板名称</i>
                    <el-button type="text">替换模板</el-button>
                  </el-form-item>
                  <el-form-item>
                    <div slot="label">
                      <span class="f-cr f-mr5">*</span>
                      <span class="f-vm">文件类型</span>
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                        <div slot="content">
                          <p>支持配置批量打印的类型：</p>
                          <p>1.连贯打印：本次批量打印的所有合格记录，是一份PDF，命名方式同导出文件名。</p>
                          <p>2.单个文件：每个学员对应的一条合格记录，是一份PDF，且以学员的姓名、证件号命名。</p>
                        </div>
                      </el-tooltip>
                      <span>：</span>
                    </div>
                    <el-radio-group v-model="form.resource">
                      <el-radio label="连贯打印"></el-radio>
                      <el-radio label="单个文件"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item>
                    <div slot="label">
                      <span class="f-cr f-mr5">*</span>
                      <span class="f-vm">是否合并打印</span>
                      <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                        <div slot="content">
                          <p>
                            连贯打印模式下，如打印的证明尺寸为四分之一的A4纸张或二分之一的A4纸，如需合并在一张A4纸上进行打印，是否合并打印请选择“是”；若模板类型为“班级配置模板”时则不支持合并打印。
                          </p>
                          <p class="f-mt5">
                            <i class="f-co">注意：</i
                            >如本次需要打印的证明尺寸超过四分之一的A4纸张或二分之一的A4纸，合并打印不生效，打印出来的文件仍然是一张证明一张A4纸。
                          </p>
                        </div>
                      </el-tooltip>
                      <span>：</span>
                    </div>
                    <el-radio-group v-model="form.resource">
                      <el-radio label="是"></el-radio>
                      <el-radio label="否"></el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item class="m-btn-bar">
                    <el-button>取消</el-button>
                    <el-button type="primary">确认</el-button>
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </div>
        </el-drawer>

        <!--选择培训证明进行打印-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20">选择培训证明模板</el-button>
        <el-drawer
          title="选择培训证明进行打印"
          :visible.sync="dialog1"
          :direction="direction"
          size="1000px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="模板名称" min-width="160" fixed="left">
                <template>人社行业培训模板</template>
              </el-table-column>
              <el-table-column label="所属行业" min-width="100">
                <template>人社</template>
              </el-table-column>
              <el-table-column label="模板说明" min-width="140">
                <template>人社行业培训模板</template>
              </el-table-column>
              <el-table-column label="创建时间" min-width="170">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column label="查看" width="80" align="center">
                <template>
                  <el-button type="text" size="mini">预览</el-button>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-radio v-model="radio" label="1">选择</el-radio>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>

        <!--异步任务执行中-->
        <el-button type="primary" @click="dialog7 = true" class="f-mr20">异步任务执行中</el-button>
        <el-dialog title="" :show-close="false" :visible.sync="dialog7" width="500px" class="m-dialog">
          <p class="f-tc f-mb20">xxxx 条人员信息上传中，请稍等…</p>
          <el-progress :percentage="50" class="f-mlr50 f-pb40"></el-progress>
        </el-dialog>

        <!--导出成功弹窗-->
        <el-button type="primary" @click="dialog3 = true" class="f-mr20">导出成功弹窗</el-button>
        <el-dialog title="提示" :visible.sync="dialog3" width="400px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb f-f16">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务查看-证明批量打印</p>
            </div>
          </div>
          <div slot="footer">
            <el-button>暂不</el-button>
            <el-button type="primary">前往下载</el-button>
          </div>
        </el-dialog>

        <!--打印记录-->
        <el-button @click="dialog5 = true" type="primary" class="f-mr20">打印记录</el-button>
        <el-drawer title="打印记录" :visible.sync="dialog5" :direction="direction" size="600px" custom-class="m-drawer">
          <div class="drawer-bd f-mt20 f-mlr40">
            <el-timeline>
              <el-timeline-item>子项目管理员 在 2021-02-02 12:14:15 打印了该证明</el-timeline-item>
              <el-timeline-item>子项目管理员 在 2021-02-02 12:14:15 打印了该证明</el-timeline-item>
              <el-timeline-item>子项目管理员 在 2021-02-02 12:14:15 打印了该证明</el-timeline-item>
              <el-timeline-item>子项目管理员 在 2021-02-02 12:14:15 打印了该证明</el-timeline-item>
            </el-timeline>
          </div>
        </el-drawer>

        <!--选择分销商-->
        <el-button @click="dialog10 = true" type="primary" class="f-mr20">选择分销商</el-button>
        <el-drawer
          title="选择分销商"
          :visible.sync="dialog10"
          :direction="direction"
          size="900px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <!--条件查询-->
            <el-row :gutter="0" class="m-query">
              <el-form :inline="true" label-width="auto">
                <el-col :span="12">
                  <el-form-item label="分销商名称">
                    <el-input v-model="input" clearable placeholder="请输入分销商名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                    <el-button>重置</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column label="NO." type="index" width="80" align="center" fixed="left"></el-table-column>
              <el-table-column label="分销商" min-width="160">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0"><el-tag type="primary">企业</el-tag>分销商A</div>
                  <div v-else><el-tag type="success">个人</el-tag>分销商A</div>
                </template>
              </el-table-column>
              <el-table-column label="分销商单位管理员" min-width="160">
                <template>张三 / 13599999999</template>
              </el-table-column>
              <el-table-column label="操作" width="120" align="center">
                <template> </template>
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0"><el-button type="text">取消选择</el-button></div>
                  <div v-else><el-button type="text">选择</el-button></div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
            <div class="m-no-date">
              <img class="img" src="./assets/images/no-data-normal.png" alt="" />
              <div class="date-bd">
                <p class="f-f15 f-c9">暂时还没有内容~</p>
              </div>
            </div>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
