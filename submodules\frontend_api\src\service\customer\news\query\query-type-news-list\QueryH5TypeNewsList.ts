/*
 * @Description: 获取资讯 --- h5
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-24 20:04:16
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-12 14:52:28
 */
import BasicDataQueryForestage, {
  NewsDetailWithPreviousAndNextCommonResponse,
  NewsQueryByAreaCodePathRequest,
  NewsWithPreviousAndNextCommonRequest,
  TrainingChannelNewsQueryCommonRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import NewsDetail from '@api/service/customer/news/query/query-news-detail/QueryDetailNews'
import WebTypeNewsVo from '@api/service/customer/news/query/query-type-news-list/vo/WebNews'
import { Page } from '@hbfe/common'
import { getCommonNewsDetailWithPreviousAndNext } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage/graphql-importer'

import QueryNewsDetailVo from '@api/service/customer/news/query/query-news-detail/vo/NewsDetail'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import Context from '@api/service/common/context/Context'

class QueryH5TypeNewsList {
  /**
   * 获取资讯类别 - 一级目录
   */
  async queryNewsRootCategory() {
    const { data } = await BasicDataQueryForestage.listRootNewsCategory(1)
    return data
  }
  /**
   * 获取资讯类别 - 二级目录
   * @param necId 资讯分类ID
   */
  async queryNewsChildrenCategory(necId: string) {
    const { data } = await BasicDataQueryForestage.listChildNewsCategory({ status: 1, necId })
    return data
  }
  /**
   * 查询最多资讯的资讯分类列表
   * @param necId 需要排除的资讯分类ID
   */
  async queryListTopNewsCategory(necId: string) {
    const { data } = await BasicDataQueryForestage.listTopNewsCategory({ topNum: 3, necId })
    return data
  }
  /**
   * 获取资讯列表
   * @param page
   * @param necId 资讯分类ID
   * @returns
   */
  async queryNewsList(page: Page, necId: string, areaCodePath?: string): Promise<Array<WebTypeNewsVo>> {
    const request = new NewsQueryByAreaCodePathRequest()
    request.necId = necId
    request.areaCodePath = areaCodePath || undefined
    const { data } = await BasicDataQueryForestage.pageSimpleNewsByPublishAndAreaCodePath({ page, request })
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const webNewsArr = new Array<WebTypeNewsVo>()
    data.currentPageData.map(item => webNewsArr.push(WebTypeNewsVo.from(item)))
    const map = new Map<string, WebTypeNewsVo>()
    /**
     * 如果没有摘要，则去获取内容
     */
    const idList: string[] = []
    webNewsArr.forEach(item => {
      if (!item.abstract) {
        idList.push(item.id)
        map.set(item.id, item)
      }
    })
    if (idList.length !== 0) {
      const requestArr: any[] = []
      idList.forEach(item => {
        requestArr.push(new NewsDetail(item).queryNewsDetail(true))
      })
      const response: Array<QueryNewsDetailVo> = await Promise.all(requestArr)
      for (let i = 0; i < response.length; i++) {
        const element = response[i]
        map.get(element.id).setReviewCount(element.number)
        map.get(element.id).setContent(element.content)
      }
    }
    return webNewsArr
  }

  /**
   * 获取资讯列表 资讯分类ID
   * @param page
   * @param necId 资讯分类ID
   * @returns
   */
  async queryNewsListByNecId(page: Page, necId: string, areaCodePath?: string): Promise<Array<WebTypeNewsVo>> {
    const request = new NewsQueryByAreaCodePathRequest()
    request.necId = necId
    request.areaCodePath = areaCodePath || undefined
    const { data } = await BasicDataQueryForestage.pageCommonSimpleNewsByPublish({ request, page })
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const webNewsArr = new Array<WebTypeNewsVo>()
    data.currentPageData.map(item => webNewsArr.push(WebTypeNewsVo.from(item)))
    /**
     * 如果没有摘要，则去获取内容
     */
    const idList: string[] = []
    webNewsArr.map(item => {
      idList.push(item.id)
    })
    if (idList.length !== 0) {
      const rewriteRequest = new RewriteGraph<
        NewsDetailWithPreviousAndNextCommonResponse,
        NewsWithPreviousAndNextCommonRequest
      >(BasicDataQueryForestage._commonQuery, getCommonNewsDetailWithPreviousAndNext)

      const batchRq = new Array<NewsWithPreviousAndNextCommonRequest>()

      idList.map(item => {
        const request = new NewsWithPreviousAndNextCommonRequest()
        request.newId = item
        batchRq.push(request)
      })

      await rewriteRequest.request(batchRq)

      const details = new Array<NewsDetailWithPreviousAndNextCommonResponse>()

      for (const value of rewriteRequest.itemMap.values()) {
        details.push(value)
      }

      webNewsArr.forEach(item => {
        const findDetail = details.find(it => it.newsDetail.newId === item.id)
        if (findDetail) {
          item.setContent(findDetail.newsDetail?.content)
          item.setReviewCount(findDetail.newsDetail?.reviewCount)
        }
      })
    }
    return webNewsArr
  }

  /**
   * 获取专题资讯列表 资讯分类ID
   * @param page
   * @param necId 资讯分类ID
   * @param specialId 专题id
   * @returns
   */
  async querySpecialNewsListByNecIdAndSpecialId(
    page: Page,
    necId: string,
    specialId: string,
    areaCodePath?: string
  ): Promise<Array<WebTypeNewsVo>> {
    const request = new TrainingChannelNewsQueryCommonRequest()
    request.necId = necId
    request.areaCodePath = areaCodePath || undefined
    request.specialSubjectId = specialId
    const { data } = await BasicDataQueryForestage.pageTrainingChannelCommonSimpleNewsByPublish({ request, page })
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const webNewsArr = new Array<WebTypeNewsVo>()
    data.currentPageData.map(item => webNewsArr.push(WebTypeNewsVo.from(item)))
    /**
     * 如果没有摘要，则去获取内容
     */
    const idList: string[] = []
    webNewsArr.map(item => {
      idList.push(item.id)
    })
    if (idList.length !== 0) {
      const rewriteRequest = new RewriteGraph<
        NewsDetailWithPreviousAndNextCommonResponse,
        NewsWithPreviousAndNextCommonRequest
      >(BasicDataQueryForestage._commonQuery, getCommonNewsDetailWithPreviousAndNext)

      const batchRq = new Array<NewsWithPreviousAndNextCommonRequest>()

      idList.map(item => {
        const request = new NewsWithPreviousAndNextCommonRequest()
        request.newId = item
        batchRq.push(request)
      })

      await rewriteRequest.request(batchRq)

      const details = new Array<NewsDetailWithPreviousAndNextCommonResponse>()

      for (const value of rewriteRequest.itemMap.values()) {
        details.push(value)
      }

      webNewsArr.forEach(item => {
        const findDetail = details.find(it => it.newsDetail.newId === item.id)
        if (findDetail) {
          item.setContent(findDetail.newsDetail?.content)
          item.setReviewCount(findDetail.newsDetail?.reviewCount)
        }
      })
    }
    return webNewsArr
  }
  /**
   * 获取资讯列表 资讯分类ID 无内容 无次数
   * @param page
   * @param necId 资讯分类ID
   * @returns
   */
  async queryNewsListByNecIdClear(page: Page, necId: string, areaCodePath?: string): Promise<Array<WebTypeNewsVo>> {
    const request = new NewsQueryByAreaCodePathRequest()
    request.necId = necId
    request.areaCodePath = areaCodePath || undefined
    const { data } = await BasicDataQueryForestage.pageCommonSimpleNewsByPublish({ request, page })
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const webNewsArr = new Array<WebTypeNewsVo>()
    data.currentPageData.map(item => webNewsArr.push(WebTypeNewsVo.fromWithReviewCount(item)))
    return webNewsArr
  }
  /**
   * 获取资讯列表 资讯分类ID 有内容 无次数
   * @param page
   * @param necId 资讯分类ID
   * @returns
   */
  async queryNewsListByNeContent(page: Page, necId: string, areaCodePath?: string): Promise<Array<WebTypeNewsVo>> {
    const request = new NewsQueryByAreaCodePathRequest()
    request.necId = necId
    request.areaCodePath = areaCodePath || undefined
    const { data } = await BasicDataQueryForestage.pageCommonSimpleNewsByPublish({ request, page })
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const webNewsArr = new Array<WebTypeNewsVo>()
    data.currentPageData.map(item => webNewsArr.push(WebTypeNewsVo.from(item)))
    const map = new Map<string, WebTypeNewsVo>()
    /**
     * 如果没有摘要，则去获取内容
     */
    const idList: string[] = []
    webNewsArr.forEach(item => {
      idList.push(item.id)
      map.set(item.id, item)
    })
    if (idList.length !== 0) {
      const map = await new NewsDetail('').queryNewsDetailNoPreAndNoNextByH5(idList)
      webNewsArr.forEach(item => {
        const temp = map.get(item.id)
        if (temp) {
          item.content = temp.newsDetail.content

          item.reviewCount = temp.newsDetail.reviewCount
        } else {
          console.log(item.id)
        }
      })
    }
    console.log(webNewsArr)

    return webNewsArr
  }
}
export default QueryH5TypeNewsList
