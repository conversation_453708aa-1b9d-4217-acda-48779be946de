import SectionPaper from '@api/service/common/models/exam/paper/section/SectionPaper'
import LibraryWay from '@api/service/common/models/exam/paper/exam-paper/fetch-way/LibraryWay'
import Random from '@api/service/common/models/exam/paper/section/configuration/random/Random'

class ExamPaper extends SectionPaper {
  /**
   * 随机卷抽题类型
   * @see ExamRandomFetchWay
   */
  fetchWay: number

  /**
   * 题库方式
   */
  libraryWay: LibraryWay

  /**
   * 随机抽题配置
   */
  random: Random
  /**
   * 构建试卷模板，1：按平台建，2：按机构建
   */
  buildType: number
  /**
   * 单位id
   */
  unitId: string
  /**
   * 单位name
   */
  unitName: string
}

export default ExamPaper
