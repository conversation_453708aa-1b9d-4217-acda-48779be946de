import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum LogTypeEnum {
  /**
   * 编排日志
   */
  arrangement = 1,
  /**
   * 执行日志
   */
  execution
}
export default class LogTypeType extends AbstractEnum<LogTypeEnum> {
  static enum = LogTypeEnum
  constructor(status?: LogTypeEnum) {
    super()
    this.current = status
    this.map.set(LogTypeEnum.arrangement, '编排日志')
    this.map.set(LogTypeEnum.execution, '执行日志')
  }
}
