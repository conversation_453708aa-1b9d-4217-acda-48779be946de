import { Page } from '@hbfe/common'
import CourseLearningBackstage, {
  ChooseCourseStatisticsInfoTotal,
  ChooseCourseStatisticsRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import ChooseCourseDetail from '@api/service/management/statisticalReport/query/vo/ChooseCourseDetail'
import CourseLearningForestage, {
  CourseRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import ExportDataExportBackstage, { TradeReportRequest } from '@api/platform-gateway/jxjy-data-export-gateway-backstage'

export class ChooseCourseStatisticsParams extends ChooseCourseStatisticsRequest {
  // * 课程名称
  courseName = ''
}

class QueryChooseCourseStatistic {
  // * 选课统计汇总
  chooseCourseStatisticsInfoTotal = new ChooseCourseStatisticsInfoTotal()
  /**
   * 获取选课统计列表
   * @param page
   * @param request
   * @param courseChoose 是否是选课维度
   */
  async getChooseCourseStatisticList(page: Page, request: ChooseCourseStatisticsParams, courseChoose?: boolean) {
    const params = new ChooseCourseStatisticsRequest()
    let includeCourseNameIDs = new Array<string>()
    if (request.courseName) {
      includeCourseNameIDs = await this.batchQueryCourseId(request.courseName)
    }
    if (request?.courseId?.length) {
      if (includeCourseNameIDs?.length) {
        params.courseId = includeCourseNameIDs.filter(id => request.courseId.includes(id))
      } else {
        params.courseId = request.courseId
      }
    } else {
      params.courseId = includeCourseNameIDs
    }
    params.chooseCourseDateStart = request.chooseCourseDateStart
    params.chooseCourseDateEnd = request.chooseCourseDateEnd
    params.eliminate = request.eliminate
    params.supplierId = request.supplierId
    params.technicalGrade = request.technicalGrade
    let response
    if (courseChoose) {
      response = await CourseLearningBackstage.pageChooseCourseStatisticsInServicer({ page, request: params })
    } else {
      response = await CourseLearningBackstage.pageChooseCourseStatistics({ page, request: params })
    }
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('获取选课统计接口报错', response)
      return Promise.reject(response)
    }
    this.chooseCourseStatisticsInfoTotal = response.data.chooseCourseStatisticsInfoTotal
    // * 添加课件供应商名称数据 todo 业务层没有加暂时没写
    page.totalPageSize = response.data.courseStatisticsInfoPage.totalPageSize
    page.totalSize = response.data.courseStatisticsInfoPage.totalSize
    return await ChooseCourseDetail.from(response.data.courseStatisticsInfoPage.currentPageData)
  }

  /**
   * 导出选课统计
   * @param request
   */
  async exportChooseCourseStatisticList(request: ChooseCourseStatisticsRequest) {
    const response = await ExportDataExportBackstage.exportChooseCourseStatistic(request)
    if (response.status.code !== 200 || !response.status.isSuccess()) {
      console.error('导出选课统计报错', response)
      return response
    }
    return true
  }

  // * 搜索课程Id
  async batchQueryCourseId(name: string) {
    const request = new CourseRequest()
    request.name = name
    const page = new Page(1, 1)
    let response = await CourseLearningForestage.pageCourseInServicer({ page, request })
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('获取课程Id失败')
      return Promise.reject(response)
    }
    page.pageSize = response.data.totalPageSize
    response = await CourseLearningForestage.pageCourseInServicer({ page, request })
    if (response.status.code !== 200 && !response.status.isSuccess()) {
      console.error('获取课程Id失败')
      return Promise.reject(response)
    }
    return response.data.currentPageData.map(res => res.id)
  }
}

export default QueryChooseCourseStatistic
