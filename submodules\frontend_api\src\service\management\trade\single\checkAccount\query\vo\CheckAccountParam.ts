/*
 * @Description: 查询列表请求参数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: Zhu<PERSON>ong
 * @LastEditTime: 2022-11-30 15:18:25
 */

import {
  OrderBasicDataRequest,
  OrderPayInfoRequest,
  OrderRequest,
  OrderStatusChangeTimeRequest,
  DateScopeRequest,
  ReturnOrderRequest,
  SubOrderInfoRequest,
  OrderInfoRequest,
  ReturnOrderBasicDataRequest,
  ReturnOrderStatusChangeTimeRequest,
  BigDecimalScopeRequest,
  CommoditySkuRequest1,
  IssueInfo1
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import SaleChannelType, { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { OrderRefundTypeEnum } from '@api/service/common/return-order/enums/OrderRefundType'

export default class CheckAccountParam {
  /**
   * 收款账号ID
   */
  paymentAccountID?: string
  /**
   * 订单号
   */
  orderId?: string
  /**
   * 交易流水号
   */
  batchId?: string
  /**
   * 开始时间
   */
  startDate?: string
  /**
   * 结束时间
   */
  endDate?: string
  /**
   * 退款单号
   */
  returnNo?: string
  /**
   * 专题名称
   */
  specialSubjectName = ''

  /**
   * 是否来源专题
   */
  isFromSpecialSubject: boolean = undefined

  /**
   * 销售渠道
   */
  saleSource: SaleChannelEnum = null

  /**
   * 分销商Id
   */
  distributorId = ''

  /**
   * 推广门户Id
   */
  promotionPortalId = ''

  /**
   * 是否非门户推广数据
   */
  isDistributionExcludePortal = false

  /**
   * 退款类型
   */
  refundType: OrderRefundTypeEnum = undefined

  /**
   * 期别id
   */
  periodId: string = undefined

  /**
   * 培训方案id
   */
  trainingProgramId: string = undefined

  static to(checkAccountParam: CheckAccountParam) {
    const {
      paymentAccountID,
      orderId,
      batchId,
      startDate,
      endDate,
      specialSubjectName,
      saleSource,
      trainingProgramId,
      periodId
    } = checkAccountParam
    const orderRequest = new OrderRequest()
    orderRequest.orderNoList = orderId ? [orderId] : undefined
    orderRequest.orderNoList = orderId ? [orderId] : undefined
    orderRequest.payInfo = new OrderPayInfoRequest()
    orderRequest.payInfo.receiveAccountIdList = paymentAccountID ? [paymentAccountID] : undefined
    orderRequest.payInfo.flowNoList = batchId ? [batchId] : undefined
    orderRequest.orderBasicData = new OrderBasicDataRequest()
    orderRequest.orderBasicData.orderStatusList = [2]
    orderRequest.orderBasicData.channelTypesList = [1]
    orderRequest.orderBasicData.orderAmountScope = new BigDecimalScopeRequest()
    orderRequest.orderBasicData.orderAmountScope.begin = 0.01
    // orderRequest.subOrderReturnStatus = [0]
    orderRequest.orderBasicData.orderStatusChangeTime = new OrderStatusChangeTimeRequest()
    orderRequest.orderBasicData.orderStatusChangeTime.completedDatesScope = new DateScopeRequest()
    orderRequest.orderBasicData.orderStatusChangeTime.completedDatesScope.begin = startDate
    orderRequest.orderBasicData.orderStatusChangeTime.completedDatesScope.end = endDate
    if (saleSource || saleSource === SaleChannelEnum.self) {
      orderRequest.saleChannels = [saleSource]
    } else {
      orderRequest.saleChannels = [SaleChannelEnum.self, SaleChannelEnum.distribution, SaleChannelEnum.topic]
    }
    // orderRequest.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(isFromSpecialSubject)
    orderRequest.saleChannelName = orderRequest.saleChannels.includes(SaleChannelEnum.topic) ? specialSubjectName : ''

    if (checkAccountParam.distributorId) {
      orderRequest.distributorId = checkAccountParam.distributorId
    }
    if (checkAccountParam.promotionPortalId && !checkAccountParam.isDistributionExcludePortal) {
      orderRequest.portalId = checkAccountParam.promotionPortalId
    }
    orderRequest.isDistributionExcludePortal = checkAccountParam.isDistributionExcludePortal

    orderRequest.deliveryCommodity = new CommoditySkuRequest1()
    orderRequest.deliveryCommodity.issueInfo = new IssueInfo1()
    orderRequest.deliveryCommodity.issueInfo.issueId = periodId
    orderRequest.deliveryCommodity.commoditySkuIdList = trainingProgramId ? [trainingProgramId] : []
    return orderRequest
  }
  static toReturn(checkAccountParam: CheckAccountParam) {
    const { paymentAccountID, orderId, batchId, startDate, endDate, returnNo, trainingProgramId, periodId } =
      checkAccountParam
    const orderRequest = new ReturnOrderRequest()
    orderRequest.returnOrderNoList = returnNo ? [returnNo] : undefined
    orderRequest.subOrderInfo = new SubOrderInfoRequest()
    orderRequest.subOrderInfo.orderInfo = new OrderInfoRequest()
    orderRequest.subOrderInfo.orderInfo.flowNoList = batchId ? [batchId] : undefined
    orderRequest.subOrderInfo.orderInfo.orderNoList = orderId ? [orderId] : undefined
    orderRequest.subOrderInfo.orderInfo.channelTypesList = [1]
    orderRequest.subOrderInfo.orderInfo.receiveAccountIdList = paymentAccountID ? [paymentAccountID] : undefined
    orderRequest.basicData = new ReturnOrderBasicDataRequest()
    orderRequest.basicData.returnOrderStatus = [8, 9, 10]
    orderRequest.basicData.returnStatusChangeTime = new ReturnOrderStatusChangeTimeRequest()
    orderRequest.basicData.returnStatusChangeTime.returnCompleted = new DateScopeRequest()
    orderRequest.basicData.returnStatusChangeTime.returnCompleted.begin = startDate
    orderRequest.basicData.returnStatusChangeTime.returnCompleted.end = endDate
    orderRequest.basicData.refundAmountScope = new BigDecimalScopeRequest()
    orderRequest.basicData.refundAmountScope.begin = 0.01
    if (checkAccountParam.saleSource || checkAccountParam.saleSource === SaleChannelEnum.self) {
      orderRequest.subOrderInfo.orderInfo.saleChannels = [checkAccountParam.saleSource]
    } else {
      orderRequest.subOrderInfo.orderInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic
      ]
    }
    orderRequest.subOrderInfo.orderInfo.saleChannelName = orderRequest.subOrderInfo.orderInfo.saleChannels.includes(
      SaleChannelEnum.topic
    )
      ? checkAccountParam.specialSubjectName
      : ''

    if (checkAccountParam.distributorId) {
      orderRequest.distributorId = checkAccountParam.distributorId
    }
    if (checkAccountParam.promotionPortalId && !checkAccountParam.isDistributionExcludePortal) {
      orderRequest.portalId = checkAccountParam.promotionPortalId
    }
    orderRequest.isDistributionExcludePortal = checkAccountParam.isDistributionExcludePortal

    // orderRequest.payInfo.receiveAccountIdList = paymentAccountID ? [paymentAccountID] : undefined
    // orderRequest.payInfo.flowNoList = batchId ? [batchId] : undefined
    // orderRequest.orderBasicData = new OrderBasicDataRequest()
    // orderRequest.orderBasicData.orderStatusList = [2]
    // orderRequest.orderBasicData.orderType = 1
    // orderRequest.orderBasicData.orderStatusChangeTime = new OrderStatusChangeTimeRequest()
    // orderRequest.orderBasicData.orderStatusChangeTime.completedDatesScope = new DateScopeRequest()
    // orderRequest.orderBasicData.orderStatusChangeTime.completedDatesScope.begin = startDate
    // orderRequest.orderBasicData.orderStatusChangeTime.completedDatesScope.end = endDate
    if (checkAccountParam.refundType) {
      orderRequest.basicData.returnOrderTypes = [checkAccountParam.refundType]
    }

    orderRequest.returnCommodity = new CommoditySkuRequest1()
    orderRequest.returnCommodity.issueInfo = new IssueInfo1()
    orderRequest.returnCommodity.issueInfo.issueId = periodId
    orderRequest.returnCommoditySkuIdList = trainingProgramId ? [trainingProgramId] : []
    return orderRequest
  }
}
