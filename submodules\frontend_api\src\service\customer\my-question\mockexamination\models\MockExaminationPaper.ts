class MockExaminationPaper {
  /**
   * 试卷id
   */
  id: string
  /**
   * 试卷名称
   */
  name: string

  /**
   * 配置类型
   * 1：固定卷 2：AB卷 3：智能卷
   **/
  configType: number
  /**
   * 总题数
   */
  totalQuestionCount: number
  /**
   * 各题型试题数
   * 试题类型，1/2/3/4/5/6 判断/单选/多选/填空/简答/案例
   */
  allTypeQuestionCount: { [key: number]: number }
  /**
   * 试卷总分
   */
  totalScore: number
}

export default MockExaminationPaper
