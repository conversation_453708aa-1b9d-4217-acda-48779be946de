import MutationExportAsyncTask from './mutation/MutationExportAsyncTask'
import MutationImportOpen from '@api/service/management/async-task/mutation/MutationImportOpen'
import MutationImportUser from '@api/service/management/async-task/mutation/MutationImportUser'

/*
 * 异步任务查询工厂
 */
class MutationAsyncTaskFactory {
  /*
    异步任务导出实例
  */
  get mutationExportAsyncTask() {
    return new MutationExportAsyncTask()
  }

  /**
   * 导入开通业务实例
   */
  get mutationImportOpen() {
    return new MutationImportOpen()
  }

  /**
   * 导入学员业务实例
   */
  get mutationImportUser() {
    return new MutationImportUser()
  }
}

export default new MutationAsyncTaskFactory()
