/*
 * @Description: 分页请求参数(线下)
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 08:49:36
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-28 14:33:21
 */
import {
  BillStatusChangeTimeRequest,
  OfflineInvoiceRequest,
  OfflineInvoiceBasicDataRequest,
  InvoiceBillStatusChangTimeRequest,
  DeliveryStatusChangeTimeRequest,
  OfflineInvoiceDeliveryInfoRequest,
  ExpressRequest,
  TakeResultRequest,
  DateScopeRequest,
  InvoiceAssociationInfoRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { DeliveryStatusEnum, DeliveryWayEnum } from '../../enum/DeliveryInvoiceEnum'

import { TitleTypeEnum, InvoiceStatusEnum, InvoiceCategoryEnum, InvoiceTypeEnum } from '../../enum/InvoiceEnum'
export default class QueryPageInvoiceParam {
  //*****************发票基础信息查询参数 basicData********************/
  /**
   * 商品id集合-培训方案
   */
  commoditySkuIdList?: string
  /**
   * 订单号集合
   */
  orderNoList?: string
  /**
   * 买家信息
   */
  userName?: string
  idCard?: string
  phone?: string
  /**
   * 票据开具状态 0:未开具 1：开票中 2：开票成功 3：开票失败
   */
  invoiceStatusList?: InvoiceStatusEnum
  /**
   * 冻结状态
   */
  invoiceFreezeStatus?: boolean
  /**
   * 发票Id集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票号集合
   */
  invoiceNoList?: string
  /**
   *票据开具状态变更时间  billing对应申请开票时间   success对应开票时间
   */
  billStatusChangeTime?: BillStatusChangeTimeRequest
  /**
   * 发票类型 1:电子发票 2:纸质发票 写死
   */
  invoiceType = 2
  /**
   * 发票种类 1:普通发票 2:增值税普通发票 3:增值税专用发票
   */
  invoiceCategoryList: Array<string>
  /**
   * 配送方式
   */
  shippingMethodList?: DeliveryWayEnum
  /**
   * 配送状态 0:未就绪 1：已就绪 2：已自取 3：已配送
   */
  deliveryStatusList?: DeliveryStatusEnum
  /**
   *  * 配送状态变更时间记录
   */
  deliveryStartTime?: string
  deliveryEndTime?: string
  /**
   * 快递单号
   */
  expressNo?: string
  /**
   * 领取人
   */
  takePerson?: string
  static to(queryPageInvoiceParam: QueryPageInvoiceParam) {
    const offlineInvoiceRequest = new OfflineInvoiceRequest()
    offlineInvoiceRequest.basicData = new OfflineInvoiceBasicDataRequest()
    offlineInvoiceRequest.basicData.billStatusChangTime = new InvoiceBillStatusChangTimeRequest()
    offlineInvoiceRequest.basicData.commoditySkuIdList = queryPageInvoiceParam.commoditySkuIdList
      ? [queryPageInvoiceParam.commoditySkuIdList]
      : undefined
    // 缺少订单字段
    // 缺少用户ID字段
    offlineInvoiceRequest.basicData.billStatusList =
      queryPageInvoiceParam.invoiceStatusList === 0 || queryPageInvoiceParam.invoiceStatusList
        ? [queryPageInvoiceParam.invoiceStatusList]
        : undefined
    offlineInvoiceRequest.basicData.freeze = queryPageInvoiceParam.invoiceFreezeStatus
    offlineInvoiceRequest.basicData.invoiceNoList = queryPageInvoiceParam.invoiceNoList
      ? [queryPageInvoiceParam.invoiceNoList]
      : undefined
    offlineInvoiceRequest.basicData.billStatusChangTime.unBillDateScope =
      queryPageInvoiceParam.billStatusChangeTime?.billing
    offlineInvoiceRequest.basicData.billStatusChangTime.successDateScope =
      queryPageInvoiceParam.billStatusChangeTime?.success
    offlineInvoiceRequest.invoiceIdList = queryPageInvoiceParam.invoiceIdList
    offlineInvoiceRequest.invoiceDeliveryInfo = new OfflineInvoiceDeliveryInfoRequest()
    offlineInvoiceRequest.invoiceDeliveryInfo.shippingMethodList = queryPageInvoiceParam.shippingMethodList
      ? [queryPageInvoiceParam.shippingMethodList]
      : undefined
    offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusList =
      queryPageInvoiceParam.deliveryStatusList === 0 || queryPageInvoiceParam.deliveryStatusList
        ? [queryPageInvoiceParam.deliveryStatusList]
        : undefined
    offlineInvoiceRequest.invoiceDeliveryInfo.express = new ExpressRequest()
    offlineInvoiceRequest.invoiceDeliveryInfo.express.expressNo = queryPageInvoiceParam.expressNo
      ? queryPageInvoiceParam.expressNo
      : undefined
    offlineInvoiceRequest.associationInfo = new InvoiceAssociationInfoRequest()
    offlineInvoiceRequest.associationInfo.associationType = 0
    offlineInvoiceRequest.invoiceDeliveryInfo.takeResult = new TakeResultRequest()
    offlineInvoiceRequest.invoiceDeliveryInfo.takeResult.takePerson = queryPageInvoiceParam.takePerson
      ? queryPageInvoiceParam.takePerson
      : undefined
    offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime = new DeliveryStatusChangeTimeRequest()
    switch (queryPageInvoiceParam.deliveryStatusList) {
      case 0:
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.unReady = new DateScopeRequest()
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.unReady.begin =
          queryPageInvoiceParam.deliveryStartTime
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.unReady.end =
          queryPageInvoiceParam.deliveryEndTime
        break
      case 1:
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.ready = new DateScopeRequest()
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.ready.begin =
          queryPageInvoiceParam.deliveryStartTime
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.ready.end =
          queryPageInvoiceParam.deliveryEndTime
        break
      case 2:
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.shipped = new DateScopeRequest()
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.shipped.begin =
          queryPageInvoiceParam.deliveryStartTime
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.shipped.end =
          queryPageInvoiceParam.deliveryEndTime
        break
      case 3:
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.taken = new DateScopeRequest()
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.taken.begin =
          queryPageInvoiceParam.deliveryStartTime
        offlineInvoiceRequest.invoiceDeliveryInfo.deliveryStatusChangeTime.taken.end =
          queryPageInvoiceParam.deliveryEndTime
        break
    }
    return offlineInvoiceRequest
  }
}
