<route-meta>
{
"isMenu": true,
"title": "专题管理",
"sort": 2,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>
<template>
  <el-main>
    <template
      v-if="$hasPermission('topicsManage,topicsManageZt')"
      desc="topicsManage:专题管理,topicsManageZt:专题管理-专题管理员"
      actions="topicsManage:@BasicSettingDialog,@BizNationalRegion,@HasTraining,@BizIndustrySelect,@ThematicList,queryList,exportCurrent,exportTrainingChannel,turnOn,turnOff,created,dragElement,exportListWx,@SchemeSpecialList,@SchemeList
      #topicsManageZt:@BizNationalRegion,@HasTraining,@BizIndustrySelect,queryListInTrainingChannel,@ThematicList,exportCurrentZt,exportTrainingChannel,dragElement,exportListZt,@SchemeSpecialList"
    >
      <div class="f-p15 m-table-auto">
        <div shadow="never" class="f-mb15 m-btn-right">
          <template
            v-if="$hasPermission('batchUpadteScheme')"
            desc="批量更新方案展示"
            actions="@BatchUpdateSchemeDrawer"
          >
            <el-button type="primary" @click="batchUpdateDialog">批量更新方案展示</el-button>
          </template>
          <template v-if="$hasPermission('configuration')" desc="基础配置" actions="@BasicSettingDialog">
            <el-button type="primary" @click="basicDialog">基础配置</el-button>
          </template>
        </div>
        <el-tabs v-model="activeName" type="card" class="m-tab-card" v-if="!isZtGly">
          <el-tab-pane label="按专题" name="first">
            <thematic-list
              ref="thematicListRef"
              :topicsList="topicsList"
              :page="page"
              :thematicLoading="loading"
              @exportList="exportList"
              @searchList="searchList"
              @exportCurrent="exportCurrent"
              @handleIndustryInfos="handleIndustryInfos"
              @reset="reset"
              @queryParamChange="queryParamChange"
              @checkHasTraining="checkHasTraining"
              @listChange="listChange"
            ></thematic-list>
          </el-tab-pane>
          <el-tab-pane label="按方案" name="second" lazy v-if="!isZtGly">
            <scheme-list></scheme-list>
          </el-tab-pane>
        </el-tabs>
        <thematic-list
          v-if="isZtGly"
          ref="thematicListRef"
          :topicsList="topicsList"
          :page="page"
          :thematicLoading="loading"
          @exportList="exportList"
          @searchList="searchList"
          @exportCurrent="exportCurrentZt"
          @handleIndustryInfos="handleIndustryInfos"
          @reset="reset"
          @queryParamChange="queryParamChange"
          @listChange="listChange"
          @checkHasTraining="checkHasTraining"
        ></thematic-list>
      </div>
      <el-dialog :visible.sync="exportDialog" width="400px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb">导出成功，是否前往下载数据？</p>
            <p class="f-f13 f-mt5">下载入口：导出任务管理-{{ exportMessage }}</p>
          </div>
        </div>
        <div slot="footer">
          <el-button type="info" @click="exportDialog = false">暂 不</el-button>
          <el-button type="primary" @click="toDownloadPage">前往下载</el-button>
        </div>
      </el-dialog>
      <el-dialog :visible.sync="importDialog" width="400px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb">导入成功，是否前往下载数据？</p>
            <p class="f-f13 f-mt5">下载入口：导入任务管理-{{ importMessage }}</p>
          </div>
        </div>
        <div slot="footer">
          <el-button type="info" @click="importDialog = false">暂 不</el-button>
          <el-button type="primary" @click="toimportDownload">前往下载</el-button>
        </div>
      </el-dialog>
    </template>
    <basic-setting-dialog ref="basicSettingDialog"></basic-setting-dialog>
    <batch-update-scheme-drawer
      ref="batchUpdateSchemeDrawerRef"
      @save="saveBatchUpdateScheme"
    ></batch-update-scheme-drawer>
  </el-main>
</template>
<script lang="ts">
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import Env from '@api/service/common/utils/Env'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import SubjectType from '@api/service/management/thematic-management/enum/SubjectType'
  import ThematicManagementQueryParam from '@api/service/management/thematic-management/model/ThematicManagementQueryParam'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import ThematicManagementList from '@api/service/management/thematic-management/ThematicManagementList'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { Page } from '@hbfe/common'
  import BizNationalRegion from '@hbfe/jxjy-admin-components/src/biz/biz-national-region.vue'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import IndustrySelect from '@hbfe/jxjy-admin-specialTopics/src/add/components/industry-select.vue'
  import BasicSettingDialog from '@hbfe/jxjy-admin-specialTopics/src/manage/components/basic-setting-dialog.vue'
  import BatchUpdateSchemeDrawer from '@hbfe/jxjy-admin-specialTopics/src/manage/components/batch-update-scheme-drawer.vue'
  import H5Paper from '@hbfe/jxjy-admin-specialTopics/src/manage/components/h5-paper.vue'
  import HasTraining from '@hbfe/jxjy-admin-specialTopics/src/manage/components/has-training.vue'
  import SchemeList from '@hbfe/jxjy-admin-specialTopics/src/manage/components/scheme-list.vue'
  import ThematicList from '@hbfe/jxjy-admin-specialTopics/src/manage/components/thematic-list.vue'
  import { cloneDeep } from 'lodash'
  import { Component, Ref, Vue } from 'vue-property-decorator'

  class UpdateParam {
    id = ''
    sort = 0
  }

  @Component({
    components: {
      SchemeList,
      BizNationalRegion,
      DoubleDatePicker,
      BasicSettingDialog,
      H5Paper,
      HasTraining,
      IndustrySelect,
      ThematicList,
      BatchUpdateSchemeDrawer
    }
  })
  export default class extends Vue {
    flag = 0
    loading = false
    page = new Page()
    webUrl = 'www.baidu.com'
    activeName = 'first'
    thematicManagementList = new ThematicManagementList()
    subjectType = new SubjectType() //专题状态枚举
    topicsList: Array<ThematicManagementItem> = [] //列表数据
    exportDialog = false //导出成功弹窗
    exportMessage = '专题列表'
    exportEnter = 'exportTrainingChannel' //导出下载入口
    // 导入成功弹窗
    importDialog = false
    // 导入成功弹窗提示
    importMessage = ''
    // 导入成功列表的key
    importEnter = ''
    industryList: Array<IndustryVo> = new Array<IndustryVo>()
    /**
     * 当前网校信息
     */
    envConfig = {
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      //工勤行业
      workServiceId: '',
      // 职业卫生行业Id
      professionHealthIndustryId: '',
      //教师行业
      teacherIndustryId: ''
    }
    //环境单例
    env = Env

    @Ref('basicSettingDialog') basicSettingDialog: any
    @Ref('h5PaperRef') h5PaperRef: any
    @Ref('thematicListRef') thematicListRef: ThematicList
    @Ref('hasTrainingRef') hasTrainingRef: any
    @Ref('topicsListRef') topicsListRef: any
    @Ref('batchUpdateSchemeDrawerRef') batchUpdateSchemeDrawerRef: BatchUpdateSchemeDrawer

    isZtGly = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)

    get isIndustryOrRegionSelected() {
      return (
        this.thematicManagementList.queryParam.subjectType.includes(1) ||
        this.thematicManagementList.queryParam.subjectType.includes(2)
      )
    }

    get isUnitSelected() {
      return this.thematicManagementList.queryParam.subjectType.includes(3)
    }

    copy(url: string) {
      const _input = document.createElement('input')
      _input.value = url
      document.body.appendChild(_input)
      _input.select()
      document.execCommand('Copy')
      document.body.removeChild(_input)
      this.$message.success('复制成功')
    }

    //获取专题类型
    // subject(row: ThematicManagementItem, type: string): string {
    //   if (row.basicInfo[type]) {
    //     const value = row.basicInfo[type]
    //     // const valuesArr = value.split('/'),
    //     //   lastIndex = valuesArr.length - 1
    //     if (type == 'subjectType') {
    //       return row.basicInfo[type] == 1 ? '地区' : '行业'
    //     } else if (type == 'subjectTypeValue') {
    //       return value
    //     }
    //   }
    //   return ''
    // }

    /**
     * 响应组件行业Id集合传参
     */
    handleIndustryInfos(values: any) {
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.professionHealthIndustryId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
    }

    //基础配置弹窗
    basicDialog() {
      this.basicSettingDialog.showBasicDialog = true
    }

    // 批量更新方案展示
    batchUpdateDialog() {
      this.batchUpdateSchemeDrawerRef.open()
    }

    // 导入成功
    saveBatchUpdateScheme() {
      this.importDialog = true
      this.importMessage = '批量更新方案展示'
      this.importEnter = 'UPDATE_SCHEME_SHOW_IMPORT'
    }

    //编辑专题
    goEdit(id: string) {
      console.log('去编辑,当前专题id', id)

      this.$router.push({
        path: `/training/special-topics/manage/edit/${id}`
      })
    }

    //启用
    async turnOn(item: ThematicManagementItem) {
      this.$confirm('启用后该专题将展示在网校门户，对应的推广链接和二维码将可正常使用，是否确认启用？', '提示', {
        confirmButtonText: '确认',
        showCancelButton: true,
        type: 'warning'
      }).then(async () => {
        this.loading = true
        await item.enableTopic()
        this.loading = false
      })
    }

    //停用
    async turnOff(item: ThematicManagementItem) {
      this.$confirm('停用后该专题将不显示在网校门户，对应的推广链接和二维码将自动失效，是否确认停用？', '提示', {
        confirmButtonText: '确认',
        showCancelButton: true,
        type: 'warning'
      }).then(async () => {
        this.loading = true
        await item.disableTopic()
        this.loading = false
      })
    }

    //web端访问链接
    webLink(url: string) {
      this.$confirm(`${url}${this.env.proxyPortStr}`, 'web端访问专题', {
        confirmButtonText: '复制',
        showCancelButton: true
      }).then(() => {
        const copyUrl = url + this.env.proxyPortStr
        this.copy(copyUrl)
      })
    }

    //打开h5模板弹窗
    openH5Paper(url: string, topicID: string) {
      this.h5PaperRef.dialog6 = true
      this.h5PaperRef.domainUrl = url
      this.h5PaperRef.topicID = topicID
      this.h5PaperRef.search()
    }

    //查看已选择的培训方案
    checkHasTraining(topicID: string) {
      this.hasTrainingRef.showDialog = true
      this.hasTrainingRef.topicID = topicID
    }

    listChange(val: Array<ThematicManagementItem>) {
      this.topicsList = cloneDeep(val)
    }

    //查询列表
    async searchList() {
      this.loading = true
      this.page = new Page()
      this.topicsList = new Array<ThematicManagementItem>()
      try {
        if (this.isZtGly) {
          await this.queryListInTrainingChannel()
        } else {
          await this.queryList()
        }
        ;(this.$refs['topicsListRef'] as any)?.doLayout()
      } catch (error) {
        console.log(error)
        this.$message.error('系统错误')
      } finally {
        this.loading = false
      }
    }

    // 专题管理员查询
    async queryListInTrainingChannel() {
      await this.thematicManagementList.queryListInTrainingChannel(this.page)
      this.topicsList = this.thematicManagementList.list
    }

    // 网校管理员查询
    async queryList() {
      await this.thematicManagementList.queryList(this.page)
      this.topicsList = this.thematicManagementList.list
    }

    //导出当前专题
    async exportCurrent(row: ThematicManagementItem) {
      try {
        this.thematicListRef.exportThematicLoading = true
        const status = await row.export()
        if (status.status.code == 200) {
          this.$message.success('导出成功')
          this.exportDialog = true
          this.exportMessage = '专题明细'
          this.exportEnter = 'exportTrainingChannelDetail'
        } else {
          this.$message.error('导出失败,系统异常')
        }
      } catch (error) {
        console.log(error)
        this.$message.error('导出失败,系统异常')
      } finally {
        this.thematicListRef.exportThematicLoading = false
      }
    }

    //导出当前专题方案列表-专题管理员
    async exportCurrentZt(row: ThematicManagementItem) {
      try {
        this.thematicListRef.exportThematicLoading = true
        const status = await row.exportInTrainingChannel()
        if (status.status.code == 200) {
          this.$message.success('导出成功')
          this.exportDialog = true
          this.exportMessage = '专题明细'
          this.exportEnter = 'exportTrainingChannelDetail'
        } else {
          this.$message.error('导出失败,系统异常')
        }
      } catch (error) {
        console.log(error)
        this.$message.error('导出失败,系统异常')
      } finally {
        this.thematicListRef.exportThematicLoading = false
      }
    }

    queryParamChange(val: ThematicManagementQueryParam) {
      this.thematicManagementList.queryParam = cloneDeep(val)
    }

    //导出
    async exportList() {
      try {
        this.thematicListRef.exportListLoaing = true
        let res
        if (this.isZtGly) {
          res = await this.exportListZt()
        } else {
          res = await this.exportListWx()
        }
        if (res.status.isSuccess() && res.data == true) {
          this.$message.success('导出成功')
          this.exportDialog = true
          this.exportMessage = '专题列表'
          this.exportEnter = 'exportTrainingChannel'
        } else {
          this.$message.error('导出失败,系统异常')
        }
      } catch (error) {
        console.log(error)
        this.$message.error('导出失败,系统异常')
      } finally {
        this.thematicListRef.exportListLoaing = false
      }
    }

    //网校管理员导出
    async exportListWx() {
      return await this.thematicManagementList.exportList()
    }

    //专题管理员导出
    async exportListZt() {
      return await this.thematicManagementList.exportListInTrainingChannel()
    }

    //前往下载
    toDownloadPage() {
      this.exportDialog = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: {
          type: this.exportEnter
        }
      })
    }

    //前往导入成功的下载
    toimportDownload() {
      this.importDialog = false
      this.$router.push({
        path: '/training/task/importtask',
        query: {
          type: this.importEnter
        }
      })
    }

    //reset重置
    reset() {
      this.thematicListRef.thematicManagementList.queryParam = new ThematicManagementQueryParam()
      this.thematicManagementList.queryParam = new ThematicManagementQueryParam()
      this.page.pageNo = 1
      this.searchList()
    }

    activated() {
      console.warn(this.subjectType.list())
      this.searchList()
    }
  }
</script>
