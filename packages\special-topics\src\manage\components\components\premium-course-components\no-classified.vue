<!--
 * @Author: l<PERSON><PERSON><PERSON> <EMAIL>
 * @Date: 2024-03-20 15:51:59
 * @LastEditors: lixinye <EMAIL>
 * @LastEditTime: 2024-04-07 09:46:06
 * @FilePath: \jxjyv2_frontend_web_admin\src\unit-share\network-school\training\special-topics\manage\components\__components__\premium-course-components\no-classified.vue
 * @Description: 无分类表格
-->
<template>
  <div v-if="$hasPermission('query')" desc="查询精品课程列表" actions="doQuery">
    <template v-if="$hasPermission('add')" desc="添加精品课程" actions="saveCourse">
      <el-button @click="openAddDrawer" type="primary" icon="el-icon-plus" class="f-mb20">添加精品课程</el-button>
    </template>
    <!--表格-->
    <el-table
      stripe
      ref="elTableRef"
      :data="tableData"
      max-height="500px"
      class="m-table"
      v-loading="loadingConfig.doQueryLoading || loadingConfig.cancelDisplayLoading || loadingConfig.dragRequireLoading"
    >
      <el-table-column type="index" label="No." width="60" align="center">
        <template slot-scope="scope">
          <span :data-index="scope.$index + 1" v-observe-visibility="visibleCourseList">
            {{ scope.$index + 1 }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="排序" min-width="70" align="center">
        <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
      </el-table-column>
      <!-- 删除 -->
      <!-- <el-table-column label="sort" min-width="300">
        <template slot-scope="{ row }">{{ row.sort || '-' }}</template>
      </el-table-column> -->
      <el-table-column label="课程名称" min-width="300">
        <template slot-scope="{ row }">{{ row.name || '-' }}</template>
      </el-table-column>
      <el-table-column label="课程分类" min-width="180">
        <template slot-scope="{ row }">
          <span>{{ row.categoriesName }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作时间" min-width="180">
        <template slot-scope="{ row }">{{ row.createTime || '-' }}</template>
      </el-table-column>
      <el-table-column label="操作" width="100" align="center" fixed="right">
        <template slot-scope="{ row }">
          <template v-if="$hasPermission('remove')" desc="取消展示精品课程" actions="doCancelDisplay">
            <el-button type="text" size="mini" @click="prevCancelDisplay(row)">取消展示</el-button>
          </template>
        </template>
      </el-table-column>
    </el-table>
    <!-- 添加精品课程弹窗 -->
    <template v-if="$hasPermission('addDrawer')" desc="添加精品课程展示弹窗" actions="@AddCourseDrawer,saveCourse">
      <add-course-drawer ref="addCourseDrawerRef" @saved="saveCourse"></add-course-drawer>
    </template>
    <!-- 二次确认弹窗 -- 取消展示 -->
    <el-dialog title="提示" :visible.sync="showSecondConfirm" width="500px" class="m-dialog">
      <div class="dialog-alert">
        <!--警告-->
        <i class="icon el-icon-warning warning"></i>
        <span class="txt">确定取消？取消后不展示在专题门户精品课程栏目下！</span>
      </div>
      <div slot="footer">
        <el-button @click="showSecondConfirm = false">取 消</el-button>
        <el-button type="primary" :loading="loadingConfig.cancelDisplayLoading" @click="doCancelDisplay"
          >确 定</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import AddCourseDrawer from '@hbfe/jxjy-admin-specialTopics/src/manage/components/components/premium-course-components/add-course-drawer.vue'
  import { ElTable } from 'element-ui/types/table'
  import { cloneDeep } from 'lodash'
  import Sortable from 'sortablejs'
  import SavePremium, {
    UpdatePremium,
    MyTools as ModelChanger
  } from '@hbfe/jxjy-admin-specialTopics/src/manage/components/components/premium-course-components/model/SavePremium'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import { SelectedCourse } from '@api/platform-gateway/platform-training-channel-v1'
  import PremiumCourseStore from '@hbfe/jxjy-admin-specialTopics/src/manage/vuex/PremiumCourseStore'
  import QueryTopicCourses from '@api/service/management/thematic-management/ThematicMangementCourseList'
  import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
  import { UiPage } from '@hbfe/common'
  import ThematicMangementCourseItem from '@api/service/management/thematic-management/model/ThematicMangementCourseItem'
  import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'

  @Component({
    components: { AddCourseDrawer }
  })
  export default class extends Vue {
    // ========================= 变量 =========================
    tableData = Array<ThematicMangementCourseItem>()

    /**
     * @description 列表更新类
     * */
    updateObj = new ThematicManagementItem()

    /**
     * @description 查询课程对应老师图片等信息
     */
    queryCourse = new QueryCourse()

    /**
     * @description 分页
     */
    page = new UiPage()

    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }

    /**
     * @description 添加精品课程组件
     * @mark ref
     * */
    @Ref('addCourseDrawerRef') addCourseDrawerRef: AddCourseDrawer

    /**
     * @description el-table
     * @mark ref
     * */
    @Ref('elTableRef') elTableRef: ElTable

    /**
     * @description 加载状态
     * */
    loadingConfig = {
      // 查询列表加载
      doQueryLoading: false,
      // 取消展示加载
      cancelDisplayLoading: false,
      // 拖拽排序加载
      dragRequireLoading: false
    }

    /**
     * @description 选中待取消展示项
     * */
    cancleDisplayItem: ThematicMangementCourseItem = new ThematicMangementCourseItem()

    /**
     * @description 取消展示二次确认弹窗
     * */
    showSecondConfirm = false

    /**
     * @description 请求变更接口入参
     * */
    requireParams: SavePremium = new SavePremium()

    /**
     * @description 模型转换
     * */
    modelChanger = new ModelChanger()

    // ========================= 生命周期 =========================

    async created() {
      await this.doQuery()
      this.$nextTick(async () => {
        console.log('拖动,专题列表ref', this.elTableRef)
        this.dragElement(this.elTableRef)
      })
    }

    // ========================= 计算熟悉 =========================

    // ========================= 方法 =========================

    /**
     * @description 查询数据
     * */
    async doQuery() {
      try {
        this.loadingConfig.doQueryLoading = true
        // todo
        const topicID = this.$route.params.id as string
        if (!topicID) throw new Error('func[doQuery] : topicId不存在')
        const queryList = new QueryTopicCourses()
        queryList.trainingChannelId = topicID
        this.tableData = await queryList.queryTopicCourseList(this.page)

        console.log(this.tableData, '===================tableData')

        this.showSecondConfirm = false
      } catch (error) {
        this.$message.error('查询列表请求失败！')
        console.log('查询列表请求失败！', error)
      } finally {
        this.loadingConfig.doQueryLoading = false
      }
      this.savePreviewData(this.tableData)
    }

    /**
     * @description 查询更多数据
     * */
    async doMoreQuery() {
      try {
        this.loadingConfig.doQueryLoading = true
        // todo
        const topicID = this.$route.params.id as string
        if (!topicID) throw new Error('func[doQuery] : topicId不存在')
        const queryList = new QueryTopicCourses()
        queryList.trainingChannelId = topicID
        this.page.pageNo++
        const list = await queryList.queryTopicCourseList(this.page)
        this.tableData = this.tableData.concat(list)
        console.log(this.tableData, '===================tableData')

        this.showSecondConfirm = false
      } catch (error) {
        this.$message.error('查询列表请求失败！')
        console.log('查询列表请求失败！', error)
      } finally {
        this.loadingConfig.doQueryLoading = false
      }
      this.savePreviewData(this.tableData)
    }

    /**
     * @description 保存精品课程需要预览的数据
     */
    async savePreviewData(tableList: Array<ThematicMangementCourseItem>) {
      const courseIds = tableList.slice(0, 4).map(item => item.courseId)
      const CourseDetailList = await this.queryCourse.queryCourseByIdList(courseIds)
      const previewTableList = await this.queryCourse.queryTeacherByIdList(CourseDetailList)
      const previewTableListMap = previewTableList.reduce((preResult, cur) => {
        preResult[cur.id] = cur
        return preResult
      }, {} as Record<string, CourseListDetail>)
      const courseListDetailList: CourseListDetail[] = courseIds.map(id => previewTableListMap[id])

      PremiumCourseStore.SET_COURSE_LIST(courseListDetailList)
    }

    /**
     * @description 打开添加精品课程抽屉
     * */
    openAddDrawer() {
      // todo
      const index = this.tableData && this.tableData.length ? this.tableData[0].sort : 0
      this.addCourseDrawerRef.drawerOpration(true, index)
    }

    /**
     * @description 取消展示精品课程
     * @mark 前置。存选中，展示二次确认弹窗
     * */
    prevCancelDisplay(row: ThematicMangementCourseItem) {
      this.cancleDisplayItem = row
      this.showSecondConfirm = true
    }

    /**
     * @description 取消展示精品课程
     * @mark 后置。执行取消操作
     * */
    async doCancelDisplay() {
      try {
        this.loadingConfig.cancelDisplayLoading = true
        const index = this.tableData.findIndex(item => item.courseId == this.cancleDisplayItem.courseId)
        if (index == -1) throw new Error('找不到courseId')
        // do update
        this.updateObj.topicID = this.$route.params.id as string
        const param = new UpdatePremium()
        param.courseCategoryId = '-1'
        param.deleteCourse = this.modelChanger.toDeleteSelectedCourse([this.cancleDisplayItem])
        const res = await this.updateObj.saveTrainingChannelCourse([param])
        if (res.isSuccess()) {
          this.tableData.splice(index, 1)
          this.page.pageNo = 1
          this.delayToDo(this.doQuery, 1000)
          this.$message.success('取消展示成功！')
        } else {
          throw new Error('接口请求失败')
        }
        this.showSecondConfirm = false
      } catch (error) {
        this.$message.error('取消展示请求失败！')
        console.log('取消展示请求失败！', error)
      } finally {
        this.loadingConfig.cancelDisplayLoading = false
      }
    }

    /**
     * 滚动加载
     * @param isVisible
     * @param entry
     */
    async visibleCourseList(isVisible: boolean, entry: any) {
      const scopeIndex = entry.target.dataset.index
      if (isVisible) {
        if (parseInt(scopeIndex) >= this.page.totalSize) {
          // 最大值时不请求
          return
        }
        if (parseInt(scopeIndex) === this.tableData.length) {
          await this.doMoreQuery()
          // 用于解决滚动加载后列表重绘异常问题
          this.elTableRef && this.elTableRef.doLayout()
        }
      }
    }

    //拖动排序
    async dragElement(table: ElTable) {
      const el = table.$el.querySelector('.el-table__body-wrapper > table > tbody') as HTMLElement
      const mySortable = new Sortable(el, {
        ghostClass: 'blue-background-class',
        handle: '.hb-iconfont',
        onEnd: ({ newIndex, oldIndex }) => {
          const tempTableData = cloneDeep(this.tableData)
          const newItemSort = this.tableData[newIndex].sort
          const curRow = this.tableData.splice(oldIndex, 1)[0]
          console.log('列表新旧索引', newIndex, oldIndex)
          this.tableData.splice(newIndex, 0, curRow)
          const newArray = this.tableData.slice(0)
          this.tableData = []
          console.log('newArray', newArray)
          this.$nextTick(async () => {
            // newArray = newArray.map((res, index) => {
            //   res.sort = newArray.length - index
            //   return res
            // })
            this.tableData = newArray
            //判断当前行的新旧位置,以最大值进行截取,赋sort值(避免出现将考前的数据往下移时sort不生效的问题)
            const underIndex = (oldIndex > newIndex ? oldIndex : newIndex) + 1
            const arr = this.tableData.slice(0, underIndex)
            const update = new Array<SelectedCourse>()
            console.log(arr, '截取的数组')
            if (oldIndex > newIndex) {
              // 向上排序
              arr.forEach((element, index) => {
                // 情况1 未拖拽影响导最高sort 情况2 拖拽至最高sort
                update.push({
                  courseId: element.courseId,
                  sort: arr[0].sort > newItemSort ? arr[0].sort - index : newItemSort - index
                })
              })
            } else {
              // 向下排序
              arr.forEach((element, index) => {
                // 情况1 未拖拽影响导最低sort 情况2 拖拽至最低sort
                update.push({
                  courseId: element.courseId,
                  sort: newItemSort + underIndex - index - 1
                })
              })
            }
            console.log('update更新传参', update)
            this.loadingConfig.dragRequireLoading = true
            mySortable.option('disabled', true)
            try {
              // 进行拖拽排序请求
              this.updateObj.topicID = this.$route.params.id as string
              const param = new UpdatePremium()
              param.updateCourse = update
              param.courseCategoryId = '-1'
              const res = await this.updateObj.saveTrainingChannelCourse([param])
              if (!res.isSuccess()) throw new Error('接口请求失败')
              this.page.pageNo = 1
              this.delayToDo(this.doQuery, 1000)
            } catch (error) {
              this.$message.error('拖拽排序请求失败！')
              // 还原数据
              this.tableData = tempTableData
              console.log('拖拽排序请求失败！', error)
            } finally {
              mySortable.option('disabled', false)
              this.loadingConfig.dragRequireLoading = false
            }
          })
        }
      })
      return mySortable
    }

    /**
     * @description 保存选中的精品课程
     * @mark 来源：emit
     * */
    saveCourse(bool: boolean) {
      if (bool) {
        setTimeout(async () => {
          this.page.pageNo = 1
          await this.doQuery()
        }, 1000)
      }
    }

    /**
     * @description 延时加载
     * */
    delayToDo(fn: Function, time: number) {
      setTimeout(() => {
        fn()
      }, time)
    }
  }
</script>
