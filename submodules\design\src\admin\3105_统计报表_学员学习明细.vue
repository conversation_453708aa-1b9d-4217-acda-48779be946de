<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="姓名">
                <el-input v-model="input" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="证件号">
                <el-input v-model="input" clearable placeholder="请输入证件号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="手机号">
                <el-input v-model="input" clearable placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="单位所在地">
                <el-cascader clearable filterable :options="cascader" placeholder="请选择工作单位所在地区" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="工作单位">
                <el-input v-model="input" clearable placeholder="请输入工作单位" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="方案属性">
                <el-select v-model="select" clearable filterable placeholder="请选择">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训方案类型">
                <el-select v-model="select" clearable filterable placeholder="请选择">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训方案名称">
                <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="期别名称">
                <el-select v-model="select" clearable filterable placeholder="请选择期别名称">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="行业">
                <el-select v-model="select" clearable filterable placeholder="请选择">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="科目类型">
                <el-select v-model="select" clearable filterable placeholder="请选择">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训类别">
                <el-select v-model="select" clearable filterable placeholder="请选择">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训专业">
                <el-select v-model="select" clearable filterable placeholder="请选择">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="报名学时">
                <el-input v-model="input" class="input-num" />
                -
                <el-input v-model="input" class="input-num" />
                <span class="f-ml5">学时</span>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="要求学时">
                <el-input v-model="input" class="input-num" />
                -
                <el-input v-model="input" class="input-num" />
                <span class="f-ml5">学时</span>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="获得学时">
                <el-input v-model="input" class="input-num" />
                -
                <el-input v-model="input" class="input-num" />
                <span class="f-ml5">学时</span>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训结果">
                <el-select v-model="select" clearable filterable placeholder="请选择">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="报名时间">
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="培训通过时间">
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="供应商">
                <el-select v-model="select" clearable filterable placeholder="请输入或选择供应商">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="分销商">
                <el-input v-model="input" clearable placeholder="请输入分销商名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="推广门户简称">
                <el-input v-model="input" clearable placeholder="请输入分销商推广门户简称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="剔除培训方案">
                <el-select v-model="select" clearable placeholder="请选择不纳入统计的培训方案">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>导出列表数据</el-button>
                <el-button>重置</el-button>
                <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <el-tabs v-model="activeName2" type="card" class="m-tab-card">
          <el-tab-pane label="网授" name="first">
            <el-card shadow="never" class="m-card is-header f-mb15">
              <div class="f-p15">
                <el-alert type="warning" :closable="false" class="m-alert f-clear">
                  <div class="f-flex f-align-center">
                    <div class="f-flex-sub"><el-button type="warning" size="small">学习数据异常管理（1）</el-button></div>
                    <div class="f-fr f-csp f-flex f-align-center">
                      <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
                    </div>
                  </div>
                </el-alert>
                <div class="f-mt10"><el-button type="primary">批量推送</el-button></div>
                <!--表格-->
                <el-table stripe :data="tableData" border max-height="500px" class="m-table is-statistical f-mt10">
                  <el-table-column type="selection" width="50" align="center" fixed="left"></el-table-column>
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="用户信息" min-width="240" fixed="left">
                    <template>
                      <p>姓名：张依依</p>
                      <p>证件号：354875965412365896</p>
                      <p>手机号：15847412365</p>
                      <p>学段：小学</p>
                      <p>学科：语文</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="工作单位信息" min-width="280">
                    <template>
                      <p>工作单位：三明数码科技时代</p>
                      <p>单位所在地区：福建省-三明市-尤溪县</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="方案名称" min-width="200">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <p>方案名称方案名称方案名称方案名称</p>
                        <p>（2025年公需课已退）</p>
                      </div>
                      <div v-else-if="scope.$index === 1">
                        <p>方案名称方案名称方案名称方案名称</p>
                        <p><el-tag type="info" size="mini">培训期别</el-tag>>2023年xx专业培训（第一期）</p>
                        <p>（华医网2025年专业课已退）</p>
                      </div>
                      <div v-else>
                        <p>方案名称方案名称方案名称方案名称</p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="培训机构" min-width="240">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        读取培训机构名称
                      </div>
                      <div v-else>
                        <p><el-tag type="info" size="mini">培训机构</el-tag>读取培训机构名称</p>
                        <p><el-tag type="info" size="mini">资源供应商</el-tag>读取资源供应商名称</p>
                        <p><el-tag type="info" size="mini">报名点机构</el-tag>读取报名点机构名称</p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="方案属性" min-width="200">
                    <template>
                      <p>行业：建设从业人员</p>
                      <p>培训年度：2024</p>
                      <p>地区：安徽/黄山市</p>
                      <p>培训类别：注册二级建造师</p>
                      <p>培训专业：XXX</p>
                      <p>岗位类别：xx</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="报名成功时间" min-width="150" align="center">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="报名学时" min-width="80" align="center">
                    <template>50</template>
                  </el-table-column>
                  <el-table-column label="线上课程" header-align="center">
                    <el-table-column label="要求学时/已获得" min-width="160" align="center">
                      <template>10 / 10</template>
                    </el-table-column>
                    <el-table-column label="课程合格时间" min-width="160">
                      <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="f-dot red f-mr5"></i>
                        <div slot="content">同步数据/模拟数据: 2021.07.23 11:11:11</div>
                      </el-tooltip>
                      <template>2020-11-11 12:20:20</template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="班级考试" header-align="center">
                    <el-table-column label="考试次数/最高成绩" min-width="160" align="center">
                      <template>22/26</template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="学习心得" header-align="center">
                    <el-table-column label="心得要求/已通过" min-width="160" align="center">
                      <template>22/26</template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="调研问卷" header-align="center">
                    <el-table-column label="问卷要求/已提交" min-width="160" align="center">
                      <template>
                        <el-tooltip effect="dark" placement="top">
                          <span class="f-cb">22/26</span>
                          <div slot="content">
                            <p>纳入考核问卷已提交：0</p>
                            <p>不纳入考核问卷已提交：0</p>
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="培训结果" min-width="80">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="success" class="badge-status">已合格</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="danger" class="badge-status">未合格</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="培训通过时间" min-width="150" align="center">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="成果是否同步" min-width="150" align="center">
                    <template>是</template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" align="center" fixed="right">
                    <template>
                      <el-button type="text">考试详情</el-button>
                      <el-button type="text">测验详情</el-button>
                      <el-button type="text">查阅学习日志</el-button>
                      <el-button type="text">导出学习日志</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </div>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="面授" name="second">
            <el-card shadow="never" class="m-card is-header f-mb15">
              <div class="f-p15">
                <el-alert type="warning" :closable="false" class="m-alert f-clear">
                  <div class="f-flex f-align-center">
                    <div class="f-flex-sub"></div>
                    <div class="f-fr f-csp f-flex f-align-center">
                      <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
                    </div>
                  </div>
                </el-alert>
                <div class="f-mt10"><el-button type="primary">批量推送</el-button></div>
                <!--表格-->
                <el-table stripe :data="tableData" border max-height="500px" class="m-table is-statistical f-mt10">
                  <el-table-column type="selection" width="50" align="center" fixed="left"></el-table-column>
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="用户信息" min-width="240" fixed="left">
                    <template>
                      <p>姓名：张依依</p>
                      <p>证件号：354875965412365896</p>
                      <p>手机号：15847412365</p>
                      <p>学段：小学</p>
                      <p>学科：语文</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="工作单位信息" min-width="280">
                    <template>
                      <p>工作单位：三明数码科技时代</p>
                      <p>单位所在地区：福建省-三明市-尤溪县</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="方案名称" min-width="200">
                    <template>
                      <p>方案名称方案名称方案名称方案名称</p>
                      <p><el-tag type="info" size="mini">培训期别</el-tag>>2023年xx专业培训（第一期）</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="培训机构" min-width="240">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        读取培训机构名称
                      </div>
                      <div v-else>
                        <p><el-tag type="info" size="mini">培训机构</el-tag>读取培训机构名称</p>
                        <p><el-tag type="info" size="mini">资源供应商</el-tag>读取资源供应商名称</p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="方案属性" min-width="200">
                    <template>
                      <p>行业：行业行业</p>
                      <p>地区：为空，不展示</p>
                      <p>科目类型：科目类型</p>
                      <p>培训类别：培训类别</p>
                      <p>培训专业：培训专业专业</p>
                      <p>培训年度：2019</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="报名成功时间" min-width="150" align="center">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="报名学时" min-width="80" align="center">
                    <template>50</template>
                  </el-table-column>
                  <el-table-column label="线上课程" header-align="center">
                    <el-table-column label="要求学时/已获得" min-width="160" align="center">
                      <template>10 / 10</template>
                    </el-table-column>
                    <el-table-column label="课程合格时间" min-width="160">
                      <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="f-dot red f-mr5"></i>
                        <div slot="content">同步数据/模拟数据: 2021.07.23 11:11:11</div>
                      </el-tooltip>
                      <template>2020-11-11 12:20:20</template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="班级考试" header-align="center">
                    <el-table-column label="考试次数/最高成绩" min-width="160" align="center">
                      <template>22/26</template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="学习心得" header-align="center">
                    <el-table-column label="心得要求/已通过" min-width="160" align="center">
                      <template>22/26</template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="培训期别" header-align="center">
                    <el-table-column label="结业测试" min-width="160" align="center">
                      <template>已合格</template>
                    </el-table-column>
                    <el-table-column label="考勤签到（总次数/实际次数）" min-width="160" align="center">
                      <template>22/26</template>
                    </el-table-column>
                    <el-table-column label="调研问卷（问卷要求/已提交）" min-width="160" align="center">
                      <template>
                        <el-tooltip effect="dark" placement="top">
                          <span class="f-cb">22/26</span>
                          <div slot="content">
                            <p>纳入考核问卷已提交：0</p>
                            <p>不纳入考核问卷已提交：0</p>
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="培训结果" min-width="80">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="success" class="badge-status">已合格</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="danger" class="badge-status">未合格</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="培训通过时间" min-width="150" align="center">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" align="center" fixed="right">
                    <template>
                      <el-button type="text">查看考勤详情</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </div>
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="面网授" name="third">
            <el-card shadow="never" class="m-card is-header f-mb15">
              <div class="f-p15">
                <el-alert type="warning" :closable="false" class="m-alert f-clear">
                  <div class="f-flex f-align-center">
                    <div class="f-flex-sub"></div>
                    <div class="f-fr f-csp f-flex f-align-center">
                      <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
                    </div>
                  </div>
                </el-alert>
                <div class="f-mt10"><el-button type="primary">批量推送</el-button></div>
                <!--表格-->
                <el-table stripe :data="tableData" border max-height="500px" class="m-table is-statistical f-mt10">
                  <el-table-column type="selection" width="50" align="center" fixed="left"></el-table-column>
                  <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                  <el-table-column label="用户信息" min-width="240" fixed="left">
                    <template>
                      <p>姓名：张依依</p>
                      <p>证件号：354875965412365896</p>
                      <p>手机号：15847412365</p>
                      <p>学段：小学</p>
                      <p>学科：语文</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="工作单位信息" min-width="280">
                    <template>
                      <p>工作单位：三明数码科技时代</p>
                      <p>单位所在地区：福建省-三明市-尤溪县</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="方案名称" min-width="200">
                    <template>
                      <p>方案名称方案名称方案名称方案名称</p>
                      <p><el-tag type="info" size="mini">培训期别</el-tag>>2023年xx专业培训（第一期）</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="培训机构" min-width="240">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        读取培训机构名称
                      </div>
                      <div v-else>
                        <p><el-tag type="info" size="mini">培训机构</el-tag>读取培训机构名称</p>
                        <p><el-tag type="info" size="mini">资源供应商</el-tag>读取资源供应商名称</p>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="方案属性" min-width="200">
                    <template>
                      <p>行业：行业行业</p>
                      <p>地区：为空，不展示</p>
                      <p>科目类型：科目类型</p>
                      <p>培训类别：培训类别</p>
                      <p>培训专业：培训专业专业</p>
                      <p>培训年度：2019</p>
                    </template>
                  </el-table-column>
                  <el-table-column label="报名成功时间" min-width="150" align="center">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="报名学时" min-width="80" align="center">
                    <template>50</template>
                  </el-table-column>
                  <el-table-column label="线上课程" header-align="center">
                    <el-table-column label="要求学时/已获得" min-width="160" align="center">
                      <template>10 / 10</template>
                    </el-table-column>
                    <el-table-column label="课程合格时间" min-width="160">
                      <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="f-dot red f-mr5"></i>
                        <div slot="content">同步数据/模拟数据: 2021.07.23 11:11:11</div>
                      </el-tooltip>
                      <template>2020-11-11 12:20:20</template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="班级考试" header-align="center">
                    <el-table-column label="考试次数/最高成绩" min-width="160" align="center">
                      <template>22/26</template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="学习心得" header-align="center">
                    <el-table-column label="心得要求/已通过" min-width="160" align="center">
                      <template>22/26</template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="培训期别" header-align="center">
                    <el-table-column label="结业测试" min-width="160" align="center">
                      <template>已合格</template>
                    </el-table-column>
                    <el-table-column label="考勤签到（总次数/实际次数）" min-width="160" align="center">
                      <template>22/26</template>
                    </el-table-column>
                    <el-table-column label="调研问卷（问卷要求/已提交）" min-width="160" align="center">
                      <template>
                        <el-tooltip effect="dark" placement="top">
                          <span class="f-cb">22/26</span>
                          <div slot="content">
                            <p>纳入考核问卷已提交：0</p>
                            <p>不纳入考核问卷已提交：0</p>
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="调研问卷" header-align="center">
                    <template slot="header">
                      <span>调研问卷</span>
                      <el-tooltip
                        class="item"
                        effect="dark"
                        content="仅统计班级维度的问卷，期别问卷含在培训期别的考核中，不在此统计。"
                        placement="top"
                      >
                        <i class="el-icon-warning f-c9"></i>
                      </el-tooltip>
                    </template>
                    <el-table-column label="问卷要求/已提交" min-width="160" align="center">
                      <template>
                        <el-tooltip effect="dark" placement="top">
                          <span class="f-cb">22/26</span>
                          <div slot="content">
                            <p>纳入考核问卷已提交：0</p>
                            <p>不纳入考核问卷已提交：0</p>
                          </div>
                        </el-tooltip>
                      </template>
                    </el-table-column>
                  </el-table-column>
                  <el-table-column label="培训结果" min-width="80">
                    <template slot-scope="scope">
                      <div v-if="scope.$index === 0">
                        <el-badge is-dot type="success" class="badge-status">已合格</el-badge>
                      </div>
                      <div v-else>
                        <el-badge is-dot type="danger" class="badge-status">未合格</el-badge>
                      </div>
                    </template>
                  </el-table-column>
                  <el-table-column label="培训通过时间" min-width="150" align="center">
                    <template>2020-11-11 12:20:20</template>
                  </el-table-column>
                  <el-table-column label="操作" width="140" align="center" fixed="right">
                    <template>
                      <el-button type="text">查看考勤详情</el-button>
                      <el-button type="text">考试详情</el-button>
                      <el-button type="text">测验详情</el-button>
                      <el-button type="text">查阅监管日志</el-button>
                    </template>
                  </el-table-column>
                </el-table>
                <!--分页-->
                <el-pagination
                  background
                  class="f-mt15 f-tr"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="currentPage4"
                  :page-sizes="[100, 200, 300, 400]"
                  :page-size="100"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="400"
                >
                </el-pagination>
              </div>
            </el-card>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
