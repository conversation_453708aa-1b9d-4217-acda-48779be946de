import {
  FieldConstraintRequest,
  FieldConstraintResponse,
  FieldConstraintValidatorRequest,
  StudentRegisterFormConstraintConfigRequest
} from '@api/ms-gateway/ms-servicer-series-v1'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import { IdentityCardType } from '@api/service/management/online-school-config/functionality-setting/enum/IdentityCardTypeEnum'
import { RegisterSettingEnum } from '@api/service/management/online-school-config/functionality-setting/mutation/MutationRegisterAndLogin'
import OccupationalHealthIndustryRegisterVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/registerAndLogin/OccupationalHealthIndustryRegisterVo'
import PharmacistIndustryRegisterVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/registerAndLogin/PharmacistIndustryRegisterVo'
import TeacherIndustryRegisterVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/registerAndLogin/TeacherIndustryRegisterVo '
import UnitRegisterVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/registerAndLogin/UnitRegisterVo'
import WorkServiceIndustryRegisterVo from '@api/service/management/online-school-config/functionality-setting/mutation/vo/registerAndLogin/WorkServiceIndustryRegisterVo'
import ConstructionIndustryRegisterVo from './ConstructionIndustryRegisterVo'
import PersonIndustryRegisterVo from './PersonIndustryRegisterVo'
import StudentRegisterVo from './StudentRegisterVo'
class RegisterSettingVo {
  /**
   * 开放学员注册入口
   */
  enable = true

  /**
   * 身份证类型列表(默认全选)
   */
  identityCardType: IdentityCardType[] = [
    IdentityCardType.HK_FOREIGNER_RESIDENCE_CARD,
    IdentityCardType.TAIWAN_FOREIGNER_RESIDENCE_CARD,
    IdentityCardType.TAIWAN_RESIDENCE_CARD,
    IdentityCardType.HK_RESIDENCE_CARD,
    IdentityCardType.ID_CARD,
    IdentityCardType.PASSPORT,
    IdentityCardType.OFFICER_CARD,
    IdentityCardType.FOREIGNER_RESIDENCE_CARD
  ]

  /**
   * 学员账号注册配置
   */
  studentRegister: StudentRegisterVo = new StudentRegisterVo()
  /**
   * 人设行业注册配置
   */
  personIndustryRegister: PersonIndustryRegisterVo = new PersonIndustryRegisterVo()
  /**
   * 建设行业注册配置
   */
  constructionIndustryRegister: ConstructionIndustryRegisterVo = new ConstructionIndustryRegisterVo()
  /**
   * 工勤行业注册配置
   */
  workServiceIndustryRegister: WorkServiceIndustryRegisterVo = new WorkServiceIndustryRegisterVo()
  /**
   * 职业卫生行业注册配置
   * @param res
   */
  occupationalHealthIndustryRegister: OccupationalHealthIndustryRegisterVo = new OccupationalHealthIndustryRegisterVo()
  /**
   * 教师行业注册配置
   */
  teacherIndustryRegister: TeacherIndustryRegisterVo = new TeacherIndustryRegisterVo()
  /**
   * 药师行业注册配置
   */
  pharmacistIndustryRegister: PharmacistIndustryRegisterVo = new PharmacistIndustryRegisterVo()
  /**
   * 单位配置
   */
  unitRegister: UnitRegisterVo = new UnitRegisterVo()

  from(res: Array<FieldConstraintResponse>) {
    this.studentRegister.from(res)
    this.personIndustryRegister.from(res)
    this.constructionIndustryRegister.from(res)
    this.workServiceIndustryRegister.from(res)
    this.occupationalHealthIndustryRegister.from(res)
    this.teacherIndustryRegister.from(res)
    this.pharmacistIndustryRegister.from(res)
  }
  to(type?: RegisterSettingEnum[]): StudentRegisterFormConstraintConfigRequest {
    const request = new StudentRegisterFormConstraintConfigRequest()
    // request.identityCardType = this.identityCardType
    request.enabled = this.enable
    request.fieldConstraints = new Array<FieldConstraintRequest>()
    request.fieldConstraints.push(...this.fieldToRequest(this.studentRegister))
    if (type.includes(RegisterSettingEnum.PERSON))
      request.fieldConstraints.push(...this.fieldToRequest(this.personIndustryRegister, true, IndustryIdEnum.RS))
    if (type.includes(RegisterSettingEnum.CONSTRUCT))
      request.fieldConstraints.push(...this.fieldToRequest(this.constructionIndustryRegister, true, IndustryIdEnum.JS))
    if (type.includes(RegisterSettingEnum.HYGIENE))
      request.fieldConstraints.push(
        ...this.fieldToRequest(this.occupationalHealthIndustryRegister, true, IndustryIdEnum.WS)
      )
    if (type.includes(RegisterSettingEnum.DILIGENCE))
      request.fieldConstraints.push(...this.fieldToRequest(this.workServiceIndustryRegister, true, IndustryIdEnum.GQ))
    if (type.includes(RegisterSettingEnum.TEACHER))
      request.fieldConstraints.push(...this.fieldToRequest(this.teacherIndustryRegister, true, IndustryIdEnum.LS))
    if (type.includes(RegisterSettingEnum.PHARMACIST))
      request.fieldConstraints.push(...this.fieldToRequest(this.pharmacistIndustryRegister, true, IndustryIdEnum.YS))
    return request
  }

  private fieldToRequest(
    data:
      | StudentRegisterVo
      | PersonIndustryRegisterVo
      | ConstructionIndustryRegisterVo
      | OccupationalHealthIndustryRegisterVo
      | WorkServiceIndustryRegisterVo
      | TeacherIndustryRegisterVo
      | PharmacistIndustryRegisterVo,
    relate = false,
    industryId?: string
  ): Array<FieldConstraintRequest> {
    const list = new Array<FieldConstraintRequest>()
    for (const skey in data) {
      const fieldRequest = new FieldConstraintRequest()
      fieldRequest.field = skey
      fieldRequest.relate = relate
      fieldRequest.validators = new Array<FieldConstraintValidatorRequest>()
      const validator = new FieldConstraintValidatorRequest()
      validator.type = data[skey]?.isRequire ? 'require' : 'unRequire'
      if (industryId) {
        validator.relateField = 'industries'
        validator.values = [industryId]
      }
      fieldRequest.validators.push(validator)
      list.push(fieldRequest)
    }
    return list
  }
}
export default RegisterSettingVo
