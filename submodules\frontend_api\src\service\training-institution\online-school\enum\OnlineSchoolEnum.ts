export enum OnlineSchoolEnum {
  /**
   * 正式版
   */
  OFFICIAL = 1,

  /**
   * DEMO版本
   */
  DEMO = 2
}

/**
 * 客户端类型
 */
export enum ClientTypesEnum {
  WEB = 1,

  H5 = 2
}

/**
 * 域名类型
 */
export enum DomainNameTypesEnum {
  /**
   * 华博域名
   */
  PUBLIC = 1,

  /**
   * 业主域名
   */
  CUSTOMER = 2
}

/**
 * 服务期限
 */
export enum ServicePeriodEnum {
  /**
   * 长期
   */
  LONG = 1,

  /**
   * 短期（指定）
   */
  SHORT = 2
}

/**
 * 创建类型
 */
export enum CreateStatusEnum {
  /**
   * 草稿
   */
  DRAFT = 0,

  /**
   * 创建
   */
  CREATE = 1
}

/**
 * 网校状态
 */
export enum SchoolStatusEnum {
  /**
   * 停用
   */
  DISABLE = 0,

  /**
   * 在服
   */
  OPERATE = 1,

  /**
   * 过期
   */
  OVER = 2,

  /**
   * 开通中
   */
  OPENING = 3
}

/**
 * 友盟统计类型
 */
export enum CNZZModeEnum {
  /**
   * 默认
   */
  DEFAULT = 0,

  /**
   * 业主自有
   */
  CUSTOMER = 1
}

/**
 * 短信提供方类型
 */
export enum SmsProviderEnum {
  /**
   * 名商通
   */
  MINGSHANGTONG = '1',

  /**
   * 联麓
   */
  LIANLU = '2'
}

/**
 * 前端模板id值
 */
export enum TemplateIdEnum {
  DEFAULT = 'TestTemplateId-1'
}

export enum perfectInfoEnum {
  /**
   * 当学员信息不全时，强制触发完善信息页面
   */
  DOPERFECT = 0,
  /**
   * 强制跳过完善信息页面
   */
  SKIPPERFECT = 1
}
