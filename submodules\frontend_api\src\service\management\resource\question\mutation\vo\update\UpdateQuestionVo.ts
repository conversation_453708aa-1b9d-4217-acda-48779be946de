import { UpdateQuestionRequest } from '@api/ms-gateway/ms-examquestion-v1'
import {
  MultipleQuestionResponse,
  OpinionQuestionResponse,
  RadioQuestionResponse
} from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import QuestionType from '@api/service/common/enums/question/QuestionType'
import QuestionOperation from '../../../helper/QuestionOperation'
import { QuestionDifficulty } from '../../enum/QuestionDifficultyType'
class UpdateQuestionRequestDto extends QuestionOperation {
  id = ''
  /**
   * 试题题目【必填】
   */
  topic = ''
  /**
   * 试题类型【必填】
   */
  questionType: number = QuestionType.enum.radio
  /**
   * 所属题库ID【必填】
   */
  libraryId = ''
  /**
   * 试题解析
   */
  dissects?: string = undefined
  /**
   * 关联课程id
   */
  relateCourseId?: string = ''

  /* 
    试题难度
    1简单 2 中等 3难
  */
  questionDifficulty: number = QuestionDifficulty.Simple

  // 模型转换为Vo
  from(data: RadioQuestionResponse | MultipleQuestionResponse | OpinionQuestionResponse): void {
    // 子类模型实现该方法
  }

  toDto(): UpdateQuestionRequest {
    // 子类模型实现该方法
    return
  }
}

export default UpdateQuestionRequestDto
