<template>
  <el-drawer title="查看详细信息" :visible.sync="show" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-table stripe :data="answerList" max-height="500px" class="m-table f-mt15" v-loading="tableLoading">
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="提交时间" min-width="160" align="center">
          <template v-slot="{ row }">{{ row.submitTime }}</template>
        </el-table-column>
        <el-table-column label="答案文本" min-width="300">
          <template v-slot="{ row }">{{ row.text }}</template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="answerPage" v-bind="answerPage"></hb-pagination>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="show = false">关闭</el-button>
      <el-button type="primary" @click="show = false">确定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
  import QuestionnaireDetail from '@api/service/management/resource/question-naire/QuestionnaireDetail'
  import AskQuestionDetailItem from '@api/service/common/question-naire/models/AskQuestionDetailItem'
  import QuetionReport from '@api/service/common/question-naire/QuetionReport'
  import { Page, UiPage } from '@hbfe/common'

  @Component
  export default class extends Vue {
    activeName1 = 'first'
    questionnaireDetail = new QuestionnaireDetail()
    answerList = new Array<AskQuestionDetailItem>()
    answerPage: UiPage
    tableLoading = false
    show = false

    @Prop({ type: String, default: '' }) questionId: string
    @Prop({ type: Object, default: new QuetionReport() }) answer: QuetionReport

    constructor() {
      super()
      this.answerPage = new UiPage(this.queryAskQuestionDetail, this.queryAskQuestionDetail)
    }
    async queryAskQuestionDetail() {
      this.tableLoading = true
      this.answerList = await this.answer.queryAskQuestionDetail(this.answerPage, this.questionId)
      this.tableLoading = false
    }
    @Watch('show')
    async currentChange() {
      if (this.show) {
        await this.queryAskQuestionDetail()
      }
    }
  }
</script>
