import getHasPlatformSupplementStudyRuleSettingIndustrysInServicer from './queries/getHasPlatformSupplementStudyRuleSettingIndustrysInServicer.graphql'
import getSupplementStudyRuleSettingByServiceId from './queries/getSupplementStudyRuleSettingByServiceId.graphql'
import getSupplementStudyRuleSettingInServicer from './queries/getSupplementStudyRuleSettingInServicer.graphql'
import getUseSupplementStudyRuleSettingRegionInServicer from './queries/getUseSupplementStudyRuleSettingRegionInServicer.graphql'
import pageSupplementStudyRuleSettingInServicer from './queries/pageSupplementStudyRuleSettingInServicer.graphql'
import disAbleSupplementStudyRuleSetting from './mutates/disAbleSupplementStudyRuleSetting.graphql'
import enableSupplementStudyRuleSetting from './mutates/enableSupplementStudyRuleSetting.graphql'
import saveSupplementStudyRuleSetting from './mutates/saveSupplementStudyRuleSetting.graphql'
import updateSupplementStudyRuleSetting from './mutates/updateSupplementStudyRuleSetting.graphql'

export {
  getHasPlatformSupplementStudyRuleSettingIndustrysInServicer,
  getSupplementStudyRuleSettingByServiceId,
  getSupplementStudyRuleSettingInServicer,
  getUseSupplementStudyRuleSettingRegionInServicer,
  pageSupplementStudyRuleSettingInServicer,
  disAbleSupplementStudyRuleSetting,
  enableSupplementStudyRuleSetting,
  saveSupplementStudyRuleSetting,
  updateSupplementStudyRuleSetting
}
