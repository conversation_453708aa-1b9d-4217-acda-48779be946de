import { QuerySchemeLearningList } from '@api/service/management/statisticalReport/query/QuerySchemeLearningList'
import { CommoditySkuRequestVo } from '@api/service/diff/management/xmlg/statistical-report/model/CommoditySkuRequestVo'
import { CommoditySkuRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { LearningReportFormsRequest } from '@api/platform-gateway/diff-ms-data-export-front-gateway-DataExportBackstage'

export default class QuerySchemeLearningListDiff extends QuerySchemeLearningList {
  /**
   * 报表查询口入参
   */
  queryFrom(commodityFilter: CommoditySkuRequestVo, commodityFilterTmp: CommoditySkuRequest, classIdS: string[]) {
    const schemeLearningFilter = super.queryFrom(commodityFilter, commodityFilterTmp, classIdS)
    schemeLearningFilter.learningRegister.saleChannels = commodityFilter.saleChannels
    return schemeLearningFilter
  }

  /**
   * 报表统计口入参
   */
  statisticsFrom(commodityFilter: CommoditySkuRequestVo, schemeLearningFilter: LearningReportFormsRequest) {
    const request = super.statisticsFrom(commodityFilter, schemeLearningFilter)
    request.learningRegister.saleChannels = commodityFilter.saleChannels
    return request
  }
  /**
   * 报表导出口入参
   */
  exportfrom(commodityFilter: CommoditySkuRequestVo) {
    const request = super.exportfrom(commodityFilter)
    request.learningRegister.saleChannels = commodityFilter.saleChannels
    return request
  }
}
