import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/gql/ms-servicer-v1'

// 是否微服务
const isMicroService = true

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-servicer-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: 'ms-servicer-v1'
}

// 枚举

// 类

export class CVendorForTInstitutionRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 渠道商编号
   */
  channelVendorId?: string
}

export class CancelAllCVendorAuthPromotionTrainingRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 培训班编号
   */
  trainingId?: string
}

/**
 * 渠道商创建信息
<AUTHOR>
@since 2021/7/7
 */
export class ChannelVendorCreateRequest {
  /**
   * 渠道商名称
   */
  name: string
  /**
   * 所属企业账户编号
   */
  accountId: string
  /**
   * 所在地区
   */
  region?: string
  /**
   * 渠道商LOGO
   */
  logo?: string
  /**
   * 联系人
   */
  contactPerson?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 渠道商简介
   */
  abouts?: string
  /**
   * 渠道商介绍
   */
  content?: string
  /**
   * 电子公章文件路径
   */
  electronicSealPath?: string
}

/**
 * 渠道商更新信息
<AUTHOR>
@since 2021/7/7
 */
export class ChannelVendorUpdateRequest {
  /**
   * 渠道商编号
   */
  channelVendorId: string
  /**
   * 渠道商名称，为null，表示不更新
   */
  name?: string
  /**
   * 所在地区，为null，表示不更新
   */
  region?: string
  /**
   * 渠道商Logo，为null，表示不更新
   */
  logo?: string
  /**
   * 联系人，为null，表示不更新
   */
  contactPerson?: string
  /**
   * 手机号，为null，表示不更新
   */
  phone?: string
  /**
   * 渠道商简介，为null，表示不更新
   */
  abouts?: string
  /**
   * 渠道商介绍，为null，表示不更新
   */
  content?: string
  /**
   * 电子公章文件路径，为null，表示不更新
   */
  electronicSealPath?: string
}

/**
 * 课件供应商信息
<AUTHOR>
@since 2021/7/8
 */
export class CoursewareSupplierCreateRequest {
  /**
   * 所属企业主账号编号
   */
  accountId: string
  /**
   * 课件供应商名称
   */
  name: string
  /**
   * 所在地区
   */
  region?: string
  /**
   * 课件供应商Logo
   */
  logo?: string
  /**
   * 联系人
   */
  contactPerson?: string
  /**
   * 联系电话
   */
  phone?: string
  /**
   * 课件供应商简介
   */
  abouts?: string
  /**
   * 课件供应商介绍
   */
  content?: string
  /**
   * 电子公章文件路径
   */
  electronicSealPath?: string
}

/**
 * 课件供应商信息
<AUTHOR>
@since 2021/7/8
 */
export class CoursewareSupplierUpdateRequest {
  /**
   * 课件供应商编号
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称，为null，表示不更新
   */
  name?: string
  /**
   * 所在地区，为null，表示不更新
   */
  region?: string
  /**
   * 课件供应商Logo，为null，表示不更新
   */
  logo?: string
  /**
   * 联系人，为null，表示不更新
   */
  contactPerson?: string
  /**
   * 联系电话，为null，表示不更新
   */
  phone?: string
  /**
   * 课件供应商简介，为null，表示不更新
   */
  abouts?: string
  /**
   * 课件供应商介绍，为null，表示不更新
   */
  content?: string
  /**
   * 电子公章文件路径，为null，表示不更新
   */
  electronicSealPath?: string
}

/**
 * 参训单位信息
 */
export class ParticipatingUnitCreateRequest {
  /**
   * 所属企业主账号编号
   */
  accountId: string
  /**
   * 参训单位名称
   */
  name: string
  /**
   * 所在地区
   */
  region?: string
  /**
   * 参训单位Logo
   */
  logo?: string
  /**
   * 联系人
   */
  contactPerson?: string
  /**
   * 联系电话
   */
  phone?: string
  /**
   * 参训单位简介
   */
  abouts?: string
  /**
   * 参训单位介绍
   */
  content?: string
  /**
   * 电子公章文件路径
   */
  electronicSealPath?: string
}

/**
 * 参训单位信息
 */
export class ParticipatingUnitUpdateRequest {
  /**
   * 参训单位编号
   */
  participatingUnitId: string
  /**
   * 参训单位名称，为null，表示不更新
   */
  name?: string
  /**
   * 所在地区，为null，表示不更新
   */
  region?: string
  /**
   * 参训单位Logo，为null，表示不更新
   */
  logo?: string
  /**
   * 联系人，为null，表示不更新
   */
  contactPerson?: string
  /**
   * 联系电话，为null，表示不更新
   */
  phone?: string
  /**
   * 参训单位简介，为null，表示不更新
   */
  abouts?: string
  /**
   * 参训单位介绍，为null，表示不更新
   */
  content?: string
  /**
   * 电子公章文件路径，为null，表示不更新
   */
  electronicSealPath?: string
}

/**
 * 门户轮播图
<AUTHOR>
@since 2021/7/13
 */
export class PortalBanner {
  /**
   * 轮播图编号，新建时可以不填
   */
  id?: string
  /**
   * 轮播图名称
   */
  name?: string
  /**
   * 轮播图路径
   */
  path?: string
  /**
   * 链接地址
   */
  link?: string
  /**
   * 轮播图排序
   */
  sort: number
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * 培训机构恢复与课件供应商合作信息
<AUTHOR>
@since 2021/7/8
 */
export class ResumeSignedCSupplierForTInstitutionRequest {
  /**
   * 培训机构与课件供应商签约编号
   */
  servicerContractId?: string
}

/**
 * 培训机构恢复与渠道商合作信息
<AUTHOR>
@since 2021/7/8
 */
export class ResumeSignedCVendorForTInstitutionRequest {
  /**
   * 培训机构与渠道商签约编号
   */
  servicerContractId?: string
}

/**
 * 参训单位恢复与培训机构合作信息
 */
export class ResumeSignedTInstitutionForPUnitRequest {
  /**
   * 参训单位与培训机构签约编号
   */
  servicerContractId?: string
}

/**
 * 签约成为培训机构的课件供应商合作
<AUTHOR>
@since 2021/7/8
 */
export class SignUpCSupplierForTInstitutionRequest {
  /**
   * 课件供应商编号
   */
  coursewareSupplierId?: string
  /**
   * 合约内容
   */
  content?: string
}

/**
 * 签约成为培训机构的渠道商合作
<AUTHOR>
@since 2021/7/8
 */
export class SignUpCVendorForTInstitutionRequest {
  /**
   * 渠道商编号
   */
  channelVendorId?: string
  /**
   * 合约内容
   */
  content?: string
}

export class SignUpTInstitutionForPUnitRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 合约内容
   */
  content?: string
}

/**
 * 培训机构中止与课件供应商合作信息
<AUTHOR>
@since 2021/7/8
 */
export class SuspendSignedCSupplierForTInstitutionRequest {
  /**
   * 培训机构与课件供应商签约编号
   */
  servicerContractId?: string
}

/**
 * 培训机构中止与渠道商合作信息
<AUTHOR>
@since 2021/7/8
 */
export class SuspendSignedCVendorForTInstitutionRequest {
  /**
   * 培训机构与渠道商签约编号
   */
  servicerContractId?: string
}

/**
 * 参训单位中止与培训机构合作信息
 */
export class SuspendSignedTInstitutionForPUnitRequest {
  /**
   * 参训单位与培训机构签约编号
   */
  servicerContractId?: string
}

/**
 * 培训机构创建信息
<AUTHOR>
@since 2021/7/7
 */
export class TrainingInstitutionCreateRequest {
  /**
   * 培训机构名称
   */
  name: string
  /**
   * 所属企业账户编号
   */
  accountId: string
  /**
   * 所在地区
   */
  region?: string
  /**
   * 联系人
   */
  contactPerson?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 培训机构LOGO
   */
  logo?: string
  /**
   * 培训机构简介
   */
  abouts?: string
  /**
   * 培训机构介绍
   */
  content?: string
  /**
   * 电子公章文件路径
   */
  electronicSealPath?: string
}

/**
 * 培训机构禁用请求
 */
export class TrainingInstitutionDisableRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
}

/**
 * 培训机构启用请求
 */
export class TrainingInstitutionEnableRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
}

/**
 * 培训机构门户创建信息
<AUTHOR>
@since 2021/7/13
 */
export class TrainingInstitutionPortalCreateRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
  /**
   * 门户类型
<p>
1-WEB
2-移动端
   */
  portalType: number
  /**
   * 门户标题
   */
  title?: string
  /**
   * 宣传口号
   */
  slogan?: string
  /**
   * 门户简介说明内容
   */
  content?: string
  /**
   * 域名
   */
  domainName?: string
  /**
   * 轮播图列表
   */
  banners?: Array<PortalBanner>
}

/**
 * 培训机构门户创建信息
<AUTHOR>
@since 2021/7/13
 */
export class TrainingInstitutionPortalRemoveRequest {
  /**
   * 门户编号
   */
  id: string
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
}

/**
 * 培训机构门户创建信息
<AUTHOR>
@since 2021/7/13
 */
export class TrainingInstitutionPortalUpdateRequest {
  /**
   * 门户编号
   */
  id: string
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
  /**
   * 门户标题
   */
  title?: string
  /**
   * 宣传口号
   */
  slogan?: string
  /**
   * 门户简介说明内容
   */
  content?: string
  /**
   * 域名
   */
  domainName?: string
  /**
   * 轮播图列表
   */
  banners?: Array<PortalBanner>
}

/**
 * 培训机构创建信息
<AUTHOR>
@since 2021/7/7
 */
export class TrainingInstitutionUpdateRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId: string
  /**
   * 培训机构名称，为null，表示不更新
   */
  name?: string
  /**
   * 所在地区，为null，表示不更新
   */
  region?: string
  /**
   * 联系人，为null，表示不更新
   */
  contactPerson?: string
  /**
   * 手机号，为null，表示不更新
   */
  phone?: string
  /**
   * 培训机构LOGO，为null，表示不更新
   */
  logo?: string
  /**
   * 培训机构简介，为null，表示不更新
   */
  abouts?: string
  /**
   * 培训机构介绍，为null，表示不更新
   */
  content?: string
  /**
   * 电子公章文件路径，为null，表示不更新
   */
  electronicSealPath?: string
}

export class VerifyAuthCVendorPromotionTrainingRequest {
  /**
   * 培训机构编号
   */
  trainingInstitutionId?: string
  /**
   * 渠道商编号
   */
  channelVendorId?: string
  /**
   * 培训班编号
   */
  trainingId?: string
}

/**
 * 服务方信息元数据
<AUTHOR>
@since 2021/7/27
 */
export class ServicerTokenMetaResponse {
  /**
   * 服务商编号
   */
  servicerId: string
  /**
   * 服务商类型
   */
  servicerType: number
  /**
   * 服务商状态
   */
  status: number
  /**
   * 服务商所属单位编号
   */
  unitId: string
}

/**
 * 服务凭证
<AUTHOR>
@since 2021/7/27
 */
export class ServicerTokenResponse {
  /**
   * 服务凭证
   */
  token: string
  /**
   * 服务凭证元数据
   */
  tokenMeta: ServicerTokenMetaResponse
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 查询签约状态
   * @param request
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getContractStatus(
    request: CVendorForTInstitutionRequest,
    query: DocumentNode = GraphqlImporter.getContractStatus,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 服务商申请服务凭证（获取服务商对应服务提供商信息）
   * @param servicerId 服务商编号
   * @return 服务凭证
   * @param mutate 查询 graphql 语法文档
   * @param servicerId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyForService(
    servicerId: string,
    mutate: DocumentNode = GraphqlImporter.applyForService,
    operation?: string
  ): Promise<Response<ServicerTokenResponse>> {
    return commonRequestApi<ServicerTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { servicerId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 取消培训机构下的某个培训班的所有渠道商授权
   * @param cancelAllCVendorAuthParam 取消授权参数
   * @param mutate 查询 graphql 语法文档
   * @param cancelAllCVendorAuthParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelAllCVendorAuthPromotionTraining(
    cancelAllCVendorAuthParam: CancelAllCVendorAuthPromotionTrainingRequest,
    mutate: DocumentNode = GraphqlImporter.cancelAllCVendorAuthPromotionTraining,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { cancelAllCVendorAuthParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建平台下渠道商
   * <p>
   * 该方法仅创建平台下渠道商类型的服务商
   * @param createRequest 渠道商信息
   * @return 渠道商编号
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createChannelVendor(
    createRequest: ChannelVendorCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createChannelVendor,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建平台下课件供应商
   * <p>
   * 该方法仅创建平台下课件供应商类型的服务商
   * @param createRequest 课件供应商信息
   * @return 课件供应商编号
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createCoursewareSupplier(
    createRequest: CoursewareSupplierCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createCoursewareSupplier,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建平台下参训单位
   * <p>
   * 该方法仅创建平台下参训单位类型的服务商
   * @param createRequest 参训单位信息
   * @return 参训单位编号
   * @param mutate 查询 graphql 语法文档
   * @param createRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createParticipatingUnit(
    createRequest: ParticipatingUnitCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createParticipatingUnit,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createRequest },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建平台下培训机构
   * <p>
   * 该方法仅创建平台下培训机构类型的服务商
   * @param request 培训机构信息
   * @return 培训机构编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createTrainingInstitution(
    request: TrainingInstitutionCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createTrainingInstitution,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建培训机构门户
   * @param request 门户信息
   * @return 门户编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createTrainingInstitutionPortal(
    request: TrainingInstitutionPortalCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createTrainingInstitutionPortal,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 禁用培训机构
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async disableTrainingInstitution(
    request: TrainingInstitutionDisableRequest,
    mutate: DocumentNode = GraphqlImporter.disableTrainingInstitution,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 启用培训机构
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async enableTrainingInstitution(
    request: TrainingInstitutionEnableRequest,
    mutate: DocumentNode = GraphqlImporter.enableTrainingInstitution,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 移除培训机构门户
   * @param request 门户信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeTrainingInstitutionPortal(
    request: TrainingInstitutionPortalRemoveRequest,
    mutate: DocumentNode = GraphqlImporter.removeTrainingInstitutionPortal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构恢复与渠道商合作
   * @param request 恢复信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resumeWithChannelVendorSigned(
    request: ResumeSignedCVendorForTInstitutionRequest,
    mutate: DocumentNode = GraphqlImporter.resumeWithChannelVendorSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构恢复与课件供应商合作
   * @param request 恢复信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resumeWithCoursewareSupplierSigned(
    request: ResumeSignedCSupplierForTInstitutionRequest,
    mutate: DocumentNode = GraphqlImporter.resumeWithCoursewareSupplierSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 参训单位恢复与培训机构合作
   * @param request 恢复信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resumeWithTrainingInstitutionSigned(
    request: ResumeSignedTInstitutionForPUnitRequest,
    mutate: DocumentNode = GraphqlImporter.resumeWithTrainingInstitutionSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构签约自己的渠道商
   * <p>
   * 签约培训机构（甲方）与渠道商关系（乙方）
   * @param request 签约信息
   * @return 签约编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async signUpChannelVendor(
    request: SignUpCVendorForTInstitutionRequest,
    mutate: DocumentNode = GraphqlImporter.signUpChannelVendor,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构签约自己的课件供应商
   * <p>
   * 签约培训机构（甲方）与课件供应商关系（乙方）
   * @param request 签约信息
   * @return 签约编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async signUpCoursewareSupplier(
    request: SignUpCSupplierForTInstitutionRequest,
    mutate: DocumentNode = GraphqlImporter.signUpCoursewareSupplier,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 参训单位签约培训机构(参训单位角色操作)
   * <p>
   * 培训机构（乙方）与参训单位关系（甲方）
   * @param request 签约信息
   * @return 签约编号
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async signUpTrainingInstitution(
    request: SignUpTInstitutionForPUnitRequest,
    mutate: DocumentNode = GraphqlImporter.signUpTrainingInstitution,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构中止与渠道商合作
   * @param request 中止信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async suspendWithChannelVendorSigned(
    request: SuspendSignedCVendorForTInstitutionRequest,
    mutate: DocumentNode = GraphqlImporter.suspendWithChannelVendorSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 培训机构中止与课件供应商合作
   * @param request 中止信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async suspendWithCoursewareSupplierSigned(
    request: SuspendSignedCSupplierForTInstitutionRequest,
    mutate: DocumentNode = GraphqlImporter.suspendWithCoursewareSupplierSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 参训单位中止与培训机构合作
   * @param request 中止信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async suspendWithTrainingInstitutionSigned(
    request: SuspendSignedTInstitutionForPUnitRequest,
    mutate: DocumentNode = GraphqlImporter.suspendWithTrainingInstitutionSigned,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新平台下渠道商
   * <p>
   * 该方法仅更新平台下渠道商类型服务商的信息
   * @param updateRequest 渠道商信息
   * @param mutate 查询 graphql 语法文档
   * @param updateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateChannelVendor(
    updateRequest: ChannelVendorUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateChannelVendor,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateRequest },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新平台下课件供应商
   * <p>
   * 该方法仅更新平台下课件供应商类型服务商的信息
   * @param updateRequest 课件供应商信息
   * @param mutate 查询 graphql 语法文档
   * @param updateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCoursewareSupplier(
    updateRequest: CoursewareSupplierUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateCoursewareSupplier,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateRequest },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新平台下参训单位
   * <p>
   * 该方法仅更新平台下参训单位类型服务商的信息
   * @param updateRequest 参训单位信息
   * @param mutate 查询 graphql 语法文档
   * @param updateRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateParticipatingUnit(
    updateRequest: ParticipatingUnitUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateParticipatingUnit,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { updateRequest },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新平台下培训机构
   * <p>
   * 该方法仅更新平台下培训机构类型服务商的信息
   * @param request 培训机构信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingInstitution(
    request: TrainingInstitutionUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateTrainingInstitution,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新培训机构门户
   * @param request 门户信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateTrainingInstitutionPortal(
    request: TrainingInstitutionPortalUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateTrainingInstitutionPortal,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 验证培训机构下对已签约的渠道商授权推广班级是否有效
   * @param verifyAuthParam 验证授权参数
   * @param mutate 查询 graphql 语法文档
   * @param verifyAuthParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifyAuthCVendorPromotionTrainingEffective(
    verifyAuthParam: VerifyAuthCVendorPromotionTrainingRequest,
    mutate: DocumentNode = GraphqlImporter.verifyAuthCVendorPromotionTrainingEffective,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { verifyAuthParam },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
