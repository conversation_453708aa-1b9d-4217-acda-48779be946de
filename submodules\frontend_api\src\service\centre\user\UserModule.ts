import QueryUserFactory from '@api/service/centre/user/QueryUserFactory'
import MutationUserFactory from '@api/service/centre/user/mutation/MutationUpdateUserInfo'
import store from '@api/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
/**
 * @description
 */
@Module({ namespaced: true, name: 'CentreUserModule', dynamic: true, store })
class UserModule extends VuexModule {
  /**
   * 用户查询工厂
   */
  queryUserFactory: QueryUserFactory = new QueryUserFactory()
  /*
    用户业务工厂
  */
  mutationUserFactory: MutationUserFactory = new MutationUserFactory()
}

export default getModule(UserModule)
