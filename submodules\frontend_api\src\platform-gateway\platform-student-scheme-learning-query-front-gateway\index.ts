import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/platform-student-scheme-learning-query-front-gateway'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-student-scheme-learning-query-front-gateway'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum Direction {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum HistoryStudentTrainingInfoSortEnum {
  TRAIN_YEAR = 'TRAIN_YEAR'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class HistoryStudentTrainingInfoSrotRequest {
  /**
   * 排序枚举
   */
  historyStudentTrainingInfoSortEnum?: HistoryStudentTrainingInfoSortEnum
  /**
   * 正序或倒序
   */
  policy?: Direction
}

/**
 * 历史学习档案响应
 */
export class HistoryStudentTrainingInfoResponse {
  /**
   * 历史记录id
   */
  id: string
  /**
   * 培训平台标识
福建专技：fjzjkey
福州人事：fjrskey
厦门人才：xmzjkey
福州建设专技：fzjszjkey
厦门建设专技：xmjszjkey
莆田专技：ptzjkey
漳州开发：zzkdkey
南平专技：npzjkey
泉州提高：qztgkey
   */
  trainPlatformKey: string
  /**
   * 用户姓名
   */
  userName: string
  /**
   * 用户身份证
   */
  userIdCard: string
  /**
   * 单位名称
   */
  unitName: string
  /**
   * 培训班名称
   */
  trainName: string
  /**
   * 培训年度
   */
  trainYear: string
  /**
   * 培训学时
   */
  trainPeriod: number
  /**
   * 科目类型: 0公需、1专业、2公需+专业
   */
  subjectType: number
  /**
   * 考试成绩
   */
  score: number
  /**
   * 考试开始时间
   */
  examStartTime: string
  /**
   * 考试结束时间
   */
  examEndTime: string
  /**
   * 培训开始时间
   */
  trainBeginTime: string
  /**
   * 培训结束时间
   */
  trainEndTime: string
  /**
   * 培训合格时间
   */
  qualifiedTime: string
  /**
   * 培训内容集合（课程集合）
   */
  trainingContentList: Array<TrainingContentResponse>
}

export class TrainingContentResponse {
  /**
   * 课程名称
   */
  courseName: string
  /**
   * 课程学时
   */
  period: number
  /**
   * 开始学习时间
   */
  startLearningTime: string
  /**
   * 结束学习时间
   */
  endLearningTime: string
  /**
   * 课件信息
   */
  trainingCoursewareList: Array<TrainingCoursewareResponse>
}

export class TrainingCoursewareResponse {
  /**
   * 课件名称
   */
  coursewareName: string
}

export class HistoryStudentTrainingInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<HistoryStudentTrainingInfoResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据历史数据id获取历史学习数据
   * @return
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getHistoryStudentTrainingInfoResponseByIdInMyself(
    id: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getHistoryStudentTrainingInfoResponseByIdInMyself,
    operation?: string
  ): Promise<Response<HistoryStudentTrainingInfoResponse>> {
    return commonRequestApi<HistoryStudentTrainingInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据用户id获取历史学习数据
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageHistoryStudentTrainingInfoResponseInMyself(
    params: { page?: Page; sort?: Array<HistoryStudentTrainingInfoSrotRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageHistoryStudentTrainingInfoResponseInMyself,
    operation?: string
  ): Promise<Response<HistoryStudentTrainingInfoResponsePage>> {
    return commonRequestApi<HistoryStudentTrainingInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
