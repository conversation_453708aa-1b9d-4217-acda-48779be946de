body {
  background: #f0f3fa;
}

/* Start the loader code, first, let's align it the center of screen */
.loader {
  position: absolute;
  top: 50%;
  left: 47%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -mos-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  text-align: center;

  /* disable selection and cursor changes */
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  cursor: default;

  .circle {
    position: absolute;
    top: 50%;
    left: 50%;
    text-align: center;
  }

  .circle:nth-child(1) {
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: -125px;
    -webkit-border-radius: 100%;
    -moz-border-radius: 100%;
    border-radius: 100%;
    background: #db2f00;
    z-index: 2;
    font-size: 2.5em;
    line-height: 2.6em;
    color: #fff;
    -webkit-box-shadow: 0 0 25px 0 rgba(50, 50, 50, 0.75);
    -moz-box-shadow: 0 0 25px 0 rgba(50, 50, 50, 0.75);
    box-shadow: 0 0 25px 0 rgba(50, 50, 50, 0.75);
  }

  .circle:nth-child(2) {
    width: 150px;
    height: 150px;
    margin-top: -75px;
    margin-left: -50px;
    -webkit-border-radius: 100%;
    -moz-border-radius: 100%;
    border-radius: 100%;
    background: #f2f2ef;
    z-index: 1;
    font-family: 'Uni Sans', Helvetica, sans-serif;
    font-size: 7em;
    font-weight: 300;
    color: #303030;
    padding-left: 5px;
    line-height: 1.55em;
    -webkit-animation: circle 1.5s infinite ease-in-out;
    -moz-animation: circle 1.5s infinite ease-in-out;
    -mos-animation: circle 1.5s infinite ease-in-out;
    -o-animation: circle 1.5s infinite ease-in-out;
    animation: circle 1.5s infinite ease-in-out;
  }

  .circle:nth-child(3) {
    width: 100px;
    height: 100px;
    margin-top: -50px;
    margin-left: 75px;
    -webkit-border-radius: 100%;
    -moz-border-radius: 100%;
    border-radius: 100%;
    background: #13a3a5;
    z-index: 2;
    font-size: 2.5em;
    line-height: 2.6em;
    color: #fff;
    -webkit-box-shadow: 0 0 25px 0 rgba(50, 50, 50, 0.75);
    -moz-box-shadow: 0 0 25px 0 rgba(50, 50, 50, 0.75);
    box-shadow: 0 0 25px 0 rgba(50, 50, 50, 0.75);
  }

  .scircle {
    position: absolute;
    top: -25px;
    left: -23px;
    width: 200px;
    height: 200px;
    -webkit-animation: scircle 1.5s ease-in-out infinite;
    -moz-animation: scircle 1.5s ease-in-out infinite;
    -o-animation: scircle 1.5s ease-in-out infinite;
  }

  .circle:nth-child(1) .scircle {
    top: -12px;
    left: -12px;
    width: 125px;
    height: 125px;
  }

  .circle:nth-child(1) .scircle::after {
    top: 120px;
  }

  .circle:nth-child(3) .scircle::after {
    top: 120px;
  }

  .circle:nth-child(3) .scircle {
    top: -12px;
    left: -12px;
    width: 125px;
    height: 125px;
  }

  .scircle:nth-child(2) {
    -webkit-animation: scircle2 1.5s ease-in-out infinite;
    -moz-animation: scircle2 1.5s ease-in-out infinite;
    -o-animation: scircle2 1.5s ease-in-out infinite;
  }

  .scircle::before {
    content: '';
    position: absolute;
    width: 0.1em;
    height: 0.1em;
    -webkit-border-radius: 100%;
    -moz-border-radius: 100%;
    -ms-border-radius: 100%;
    -o-border-radius: 100%;
    border-radius: 100%;

    /* Pick a color 1 */
    background: #fff;
  }

  .scircle::after {
    content: '';
    position: absolute;
    top: 190px;
    width: 0.1em;
    height: 0.1em;
    -webkit-border-radius: 100%;
    -moz-border-radius: 100%;
    -ms-border-radius: 100%;
    -o-border-radius: 100%;
    border-radius: 100%;

    /* Pick a color 1 */
    background: #fff;
  }
}

@-webkit-keyframes circle {
  0% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
  }

  45%,
  50%,
  55% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    filter: alpha(opacity=100);
    -moz-opacity: 1;
    -khtml-opacity: 1;
    opacity: 1;
  }

  100% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
  }
}

@-moz-keyframes circle {
  0% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
  }

  45%,
  50%,
  55% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    filter: alpha(opacity=100);
    -moz-opacity: 1;
    -khtml-opacity: 1;
    opacity: 1;
  }

  100% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
  }
}

@-moz-keyframes circle {
  0% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
  }

  45%,
  50%,
  55% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    filter: alpha(opacity=100);
    -moz-opacity: 1;
    -khtml-opacity: 1;
    opacity: 1;
  }

  100% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
  }
}

@-o-keyframes circle {
  0% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
  }

  45%,
  50%,
  55% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    filter: alpha(opacity=100);
    -moz-opacity: 1;
    -khtml-opacity: 1;
    opacity: 1;
  }

  100% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
  }
}

@keyframes circle {
  0% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
  }

  45%,
  50%,
  55% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    filter: alpha(opacity=100);
    -moz-opacity: 1;
    -khtml-opacity: 1;
    opacity: 1;
  }

  100% {
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=50)";
    filter: alpha(opacity=50);
    -moz-opacity: 0.5;
    -khtml-opacity: 0.5;
    opacity: 0.5;
  }
}

@-webkit-keyframes scircle {
  0% {
    -webkit-transform: rotate(0deg);
  }

  100% {
    -webkit-transform: rotate(360deg);
  }
}

@-moz-keyframes scircle {
  0% {
    -moz-transform: rotate(0deg);
  }

  100% {
    -moz-transform: rotate(360deg);
  }
}

@-moz-keyframes scircle {
  0% {
    -mos-transform: rotate(0deg);
  }

  100% {
    -mos-transform: rotate(360deg);
  }
}

@-o-keyframes scircle {
  0% {
    -o-transform: rotate(0deg);
  }

  100% {
    -o-transform: rotate(360deg);
  }
}

@keyframes scircle {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@-webkit-keyframes scircle2 {
  0% {
    -webkit-transform: rotate(90deg);
  }

  100% {
    -webkit-transform: rotate(450deg);
  }
}

@-moz-keyframes scircle2 {
  0% {
    -moz-transform: rotate(90deg);
  }

  100% {
    -moz-transform: rotate(450deg);
  }
}

@-moz-keyframes scircle2 {
  0% {
    -mos-transform: rotate(90deg);
  }

  100% {
    -mos-transform: rotate(450deg);
  }
}

@-o-keyframes scircle2 {
  0% {
    -o-transform: rotate(90deg);
  }

  100% {
    -o-transform: rotate(450deg);
  }
}

@keyframes scircle2 {
  0% {
    transform: rotate(90deg);
  }

  100% {
    transform: rotate(450deg);
  }
}
