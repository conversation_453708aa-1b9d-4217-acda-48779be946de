import Address from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/address/Address'
import TeachingPlanItemsGroup from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/teaching-plan-items-groups/TeachingPlanItemsGroup'
import TeachingPlanLearningConfigExtendProperty from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/extend-properties/TeachingPlanLearningConfigExtendProperty'

/**
 * @description  教学计划-学习方式配置
 */
class TeachPlanLearningConfig {
  /**
   * 教学计划学习方式配置id
   */
  id: string
  /**
   * 资源id
   */
  resourceId: string
  /**
   * 计划模式
   */
  planMode: number
  /**
   * 培训开始时间
   * @description 取教学计划项最早一门课开始时间
   */
  startTime: string
  /**
   * 培训结束时间
   * @description 取教学计划项最晚一门课结束时间
   */
  endTime: string
  /**
   * 培训点信息
   */
  address: Address
  /**
   * 扩展信息
   * {
   *   name: 'headTeacherName'
   *   value: '班主任姓名'
   * },
   * {
   *   name: 'headTeacherPhone'
   *   value: '班主任联系电话'
   * },
   * {
   *   name: 'hotelLiaisonName'
   *   value: '酒店联系人姓名'
   * },
   * {
   *   name: 'hotelLiaisonPhone'
   *   value: '酒店联系人电话'
   * }
   */
  extendProperties: TeachingPlanLearningConfigExtendProperty[]
  /**
   * 教学计划组
   */
  teachingPlanItemsGroups: TeachingPlanItemsGroup[]
}

export default TeachPlanLearningConfig
