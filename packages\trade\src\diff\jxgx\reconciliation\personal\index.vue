<route-meta>
{
"isMenu": true,
"title": "个人报名对账",
"sort": 1,
"icon": "icon_menhuxinxiguanli"
}
</route-meta>
<template>
  <jxgx-reconciliation ref="reconciliationRef"></jxgx-reconciliation>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import OrderReconciliation from '@hbfe/jxjy-admin-trade/src/diff/jxgx/reconciliation/personal/components/order-reconciliation.vue'
  import RefundReconciliation from '@hbfe/jxjy-admin-trade/src/diff/jxgx/reconciliation/personal/components/refund-reconciliation.vue'
  import Reconciliation from '@hbfe/jxjy-admin-trade/src/reconciliation/personal/index.vue'

  @Component({
    components: { OrderReconciliation, RefundReconciliation }
  })
  class JxgxReconciliation extends Reconciliation {}

  @Component({
    components: {
      JxgxReconciliation
    }
  })
  export default class extends Vue {
    @Ref('reconciliationRef') reconciliationRef: JxgxReconciliation
  }
</script>
