<route-meta>
{
"isMenu": true,
"title": "导出任务查看",
"sort":6,
"icon": "icon-chakan"
}
</route-meta>
<script lang="ts">
  import ExportTask from '@hbfe/jxjy-admin-exportTask/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, DQGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    queryTask: [DQGLY],
    location: [DQGLY],
    viewLog: [DQGLY],
    query: [DQGLY],
    export: [DQGLY]
  })
  export default class extends ExportTask {}
</script>
