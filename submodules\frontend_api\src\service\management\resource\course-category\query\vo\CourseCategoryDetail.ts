import MsCourseLearningQueryFrontGatewayCourseLearningBackstage, {
  CourseCategoryResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class CourseCategoryDetail {
  id: string
  name: string
  parentId: string
  parentName: string
  sort: number

  static from(courseCategoryDetailResponse: CourseCategoryResponse) {
    const detail = new CourseCategoryDetail()
    detail.id = courseCategoryDetailResponse.id
    detail.name = courseCategoryDetailResponse.name
    detail.parentId = courseCategoryDetailResponse.parentId
    detail.sort = courseCategoryDetailResponse.sort
    return detail
  }

  async queryParent() {
    const parentResult = await MsCourseLearningQueryFrontGatewayCourseLearningBackstage.getCourseCategoryInServicer(
      this.parentId
    )
    this.parentName = CourseCategoryDetail.from(parentResult.data).name
  }
}

export default CourseCategoryDetail
