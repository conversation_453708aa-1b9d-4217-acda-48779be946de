import YearVo from './vo/YearVo'
import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'
import BasicDataGateway from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import MsBasicdataQueryFrontGatewayBackstage, {
  BusinessDataDictionaryRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { ResponseStatus } from '@hbfe/common'

/**
 * @description 查询年度类
 */
class QueryYear {
  /**
   * 年度列表
   */
  yearList = new Array<YearVo>()

  /**
   * 运营域年度缓存
   */
  private yearCache = new Array<YearVo>()

  /**
   * 查询年度列表
   * @param 无需入参
   * @return
   */
  async queryYearList() {
    if (!this.yearList.length) {
      const res = await ServicerSeriesV1Gateway.getYears()
      if (res.status.isSuccess()) {
        this.yearList = res.data.map(item => {
          return {
            id: item.id,
            year: item.id,
            sort: item.sort,
            enable: true
          }
        })
        //   this.yearList = await this.queryYearListByIdList(yearIdList)
      }
      return res.status
    } else {
      return new ResponseStatus(200, '')
    }
  }

  /**
   * 查询年度信息通过idList
   * @param
   * @return
   */
  //   async queryYearListByIdList(yearIdList: Array<string>) {
  //     const response = await BasicDataGateway.listYearListById(yearIdList)
  //     if (response.status.isSuccess()) {
  //       return response.data
  //     }
  //     return new Array<YearVo>()
  //   }

  /**
   * 【本地】获取年度信息根据年度id
   * @param {Array<string>} yearIdList 年度id列表
   * @return
   */
  async getYearByIdList(yearIdList: Array<string>) {
    if (!this.yearList?.length) {
      await this.queryYearList()
    }
    const yearList = new Array<YearVo>()
    yearIdList?.forEach(id => {
      this.yearList?.forEach(year => {
        if (id === year.id) {
          yearList.push(year)
        }
      })
    })
    return yearList
  }

  /**
   * 运营域超管查询年度列表
   */
  async getOperationYearList() {
    if (!this.yearCache.length) {
      const request = new BusinessDataDictionaryRequest()
      request.businessDataDictionaryType = 'YEAR'
      const response = await MsBasicdataQueryFrontGatewayBackstage.listBusinessDataDictionaryInSubProject(request)
      if (response.status.isSuccess()) {
        const yearList = response.data
          .map(item => {
            return {
              id: item.id,
              year: item.id,
              sort: item.sort,
              enable: true
            }
          })
          .sort()
        this.yearCache = yearList.sort((a, b) => {
          return a.sort - b.sort
        })
      }
    }
    return this.yearCache
  }
}

export default new QueryYear()
