import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformUser'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformUser'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum LoginType {
  UNKNOWN = 'UNKNOWN',
  NORMAL = 'NORMAL',
  EMAIL = 'EMAIL',
  PHONE = 'PHONE',
  IDENTITYCARD = 'IDENTITYCARD',
  COLLECTIVE = 'COLLECTIVE'
}

// 类

/**
 * <AUTHOR> create 2021/7/26 11:02
 */
export class AdministratorCreateDTO {
  /**
   * 姓名
   */
  name?: string
  /**
   * 昵称
   */
  nickName?: string
  /**
   * 头像地址
   */
  photo?: string
  /**
   * 性别
   */
  gender?: number
  /**
   * 手机
   */
  phone?: string
  /**
   * 邮箱
   */
  email?: string
  /**
   * 启用状态
   */
  status: number
  /**
   * 简介
   */
  remark?: string
  /**
   * 登录方式（默认账号密码登录）
用户名 1
手机 2
身份证 3
邮箱 4
第三方OpenId 5
   */
  identityType: number
  /**
   * 登录账户
   */
  identity?: string
  /**
   * 密码
   */
  password?: string
  /**
   * 角色id集合
   */
  roleIds?: Array<string>
}

export class AdministratorQueryDTO {
  /**
   * 主账户id
   */
  rootAccountId?: string
  /**
   * 认证标识（登录账号）
   */
  identity?: string
  /**
   * 姓名
   */
  name?: string
  /**
   * 账户状态，1：正常，2：停用
   */
  status?: number
  /**
   * 所属角色id
   */
  roleIds?: Array<string>
}

/**
 * 用户管理查询条件
<AUTHOR>
@Date 2020/8/11/0011 9:20
 */
export class StudentQueryDTO {
  /**
   * 用户id集合
   */
  userIds?: Array<string>
  /**
   * 姓名
   */
  name?: string
  /**
   * 身份证号
   */
  idCard?: string
  /**
   * 地区路径
   */
  areaPath?: string
  /**
   * 工作单位名称，模糊搜索
   */
  companyName?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 激活时间（起）
   */
  activeTimeStart?: string
  /**
   * 激活时间（止）
   */
  activeTimeEnd?: string
}

/**
 * 用户绑定微信信息DTO
<AUTHOR>
 */
export class UserBindWXDto {
  /**
   * 用户的唯一标识
   */
  openId?: string
  /**
   * 只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段
   */
  unionId?: string
  /**
   * 用户昵称
   */
  wxNickname?: string
  /**
   * 用户头像路径
   */
  headImgUrl?: string
}

/**
 * 用户查询条件
@author: eleven
@date: 2020/4/19
 */
export class UserParamDTO {
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
}

/**
 * 获取微信opneiId和unionId等信息的入参
Author:FangKunSen
Time:2020-05-18,18:55
 */
export class WXInfoQueryParam {
  /**
   * @see com.fjhb.support.WXConstant
type: 0公众号；1小程序
   */
  type: number
  /**
   * 微信appId，不填表示当前项目默认只有一个微信开发平台程序，自动从配置中获取
   */
  appId?: string
  /**
   * 授权临时票据code
   */
  code?: string
  /**
   * 国家地区语言版本，默认为中文简体
   */
  lang?: string
  encryptedData?: string
  iv?: string
}

export class Page {
  pageNo?: number
  pageSize?: number
}

export class RoleDTO {
  /**
   * 角色ID
   */
  id: string
  /**
   * 角色级别值
   */
  levelValue: number
  /**
   * 角色名称
   */
  name: string
  /**
   * 角色描述
   */
  description: string
  /**
   * 创建方式 | 1:自建; 2:导入
   */
  createType: number
  /**
   * 数据类型 | 1:普通; 2:内置
   */
  dataType: number
  /**
   * 创建人ID
   */
  creatorId: string
  /**
   * 创建时间
   */
  createDate: string
  /**
   * 是否可用
   */
  available: boolean
}

export class AdministratorDTO {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 昵称
   */
  nickName: string
  /**
   * 电话
   */
  phone: string
  /**
   * 邮件
   */
  email: string
  /**
   * 性别:，1：男，2：女，3：中性，4：未知
   */
  gender: number
  /**
   * 头像url
   */
  photo: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人id
   */
  creatorId: string
  /**
   * 所拥有的角色集合
   */
  roles: Array<RoleDTO>
  /**
   * 账户状态，1：正常，2：停用
   */
  status: number
  /**
   * 登陆账号集合
   */
  loginAuthentications: Array<LoginAuthenticationDTO>
}

/**
 * 用户登录账号对象
 */
export class LoginAuthenticationDTO {
  /**
   * 认证方式ID
   */
  id: string
  /**
   * 认证方式类型
帐号密码认证方式 1
【第三方】微信认证方式 2
【第三方】补贴管理系统 3
@see AuthenticationPatternTypes
   */
  patternType: number
  /**
   * 认证标识类型
用户名 1
手机 2
身份证 3
邮箱 4
第三方OpenId 5
@see com.fjhb.domain.basicdata.api.account.consts.AuthenticationIdentityTypes
   */
  identityType: number
  /**
   * 认证标识
   */
  identity: string
  /**
   * 创建时间
   */
  createdTime: string
}

/**
 * 用户信息
<AUTHOR>
@Date 2020/8/11/0011 9:20
 */
export class StudentDTO {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 昵称
   */
  nickName: string
  /**
   * 性别
男: 1; 女: 2; 中性: 3; 未知: 4;
   */
  gender: number
  /**
   * 头像地址
   */
  photo: string
  /**
   * 身份证号
   */
  idCard: string
  /**
   * 联系地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 激活时间
   */
  createTime: string
  /**
   * 注册方式
@see com.fjhb.domain.basicdata.api.user.consts.UserRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
@see com.fjhb.domain.basicdata.api.user.consts.UserSourceTypes
   */
  sourceType: number
  /**
   * 所在地区路径
   */
  areaPath: string
  /**
   * 所属地区名称路径
例：福建省-福州市-鼓楼区
   */
  areaPathName: string
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 所属人群
   */
  peoples: Array<string>
}

/**
 * 用户信息
<AUTHOR>
@Date 2020/8/11/0011 9:20
 */
export class StudentItemDTO {
  /**
   * 用户id
   */
  userId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 昵称
   */
  nickName: string
  /**
   * 性别
男: 1; 女: 2; 中性: 3; 未知: 4;
   */
  gender: number
  /**
   * 头像地址
   */
  photo: string
  /**
   * 身份证号
   */
  idCard: string
  /**
   * 联系地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 激活时间
   */
  createTime: string
  /**
   * 注册方式
@see com.fjhb.domain.basicdata.api.user.consts.UserRegisterTypes
   */
  registerType: number
  /**
   * 来源类型
@see com.fjhb.domain.basicdata.api.user.consts.UserSourceTypes
   */
  sourceType: number
  /**
   * 所在地区路径
   */
  areaPath: string
  /**
   * 所属地区名称路径
例：福建省-福州市-鼓楼区
   */
  areaPathName: string
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 所属人群
   */
  peoples: Array<string>
}

/**
 * 用户绑定的第三方登陆账号信息
<AUTHOR>
 */
export class UserBindedThirdAccountDTO {
  id: string
  /**
   * 账户id
   */
  accountId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 互联方式,21:微信小程序
@see OpenPlatformTypes
   */
  openPlatformType: number
  /**
   * 用户唯一标识
   */
  openId: string
  /**
   * 授权时间
   */
  authorizeTime: string
  /**
   * 是否可用
   */
  available: boolean
}

/**
 * 用户信息对象,其中6个字段用与前端描述当前用户上下文信息
 */
export class UserInfoDTO {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 账户类型
&quot;企业帐户&quot;, 1
&quot;企业个人帐户&quot;, 2
&quot;个人帐户&quot;, 3
@see com.fjhb.btpx.platform.dao.mongo.model.servicer.enums.AccountTypeEnums
   */
  accountType: number
  /**
   * 用户id
   */
  userId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 昵称
   */
  nickName: string
  /**
   * 身份证号
   */
  idCard: string
  /**
   * 头像地址
   */
  photo: string
  /**
   * 性别
   */
  gender: number
  /**
   * 手机号码
   */
  phone: string
  /**
   * 所属地区编码
   */
  areaPath: string
  /**
   * 所属地区编码
   */
  areaPathName: string
  /**
   * 联系地址
   */
  address: string
  /**
   * 用户创建时间
   */
  createTime: string
  /**
   * 工作单位名称
   */
  companyName: string
  /**
   * 注册方式
@see com.fjhb.domain.basicdata.api.user.consts.UserRegisterTypes
   */
  registerType: number
  /**
   * 注册来源
@see com.fjhb.domain.basicdata.api.user.consts.UserSourceTypes
   */
  sourceType: number
  /**
   * 当前登录用户所拥有的角色，可同时拥有多种角色
   */
  roleList: Array<RoleDTO>
  /**
   * 登陆账号集合
   */
  loginAuthentications: Array<LoginAuthenticationDTO>
  /**
   * 所属人群
   */
  peoples: string
}

/**
 * 用户对象
<AUTHOR> create 2020/3/13 9:42
 */
export class UserSimpleInfoDTO {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 昵称
   */
  nickName: string
  /**
   * 身份证号
   */
  idCard: string
  /**
   * 头像地址
   */
  photo: string
  /**
   * 性别
   */
  gender: number
  /**
   * 手机号码
   */
  phone: string
  /**
   * 所属地区编码
   */
  areaPath: string
}

/**
 * 获取微信必要信息
 */
export class WXIdInfo {
  openId: string
  unionId: string
  accessToken: string
  nickname: string
  refreshToken: string
  sex: string
  headimgurl: string
  purePhoneNumber: string
}

export class AdministratorDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<AdministratorDTO>
}

export class StudentItemDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentItemDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 导出学员
   * @param queryDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param queryDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudent(
    queryDTO: StudentQueryDTO,
    query: DocumentNode = GraphqlImporter.exportStudent,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { queryDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取管理员分页信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAdminPage(
    params: { page?: Page; query?: AdministratorQueryDTO },
    query: DocumentNode = GraphqlImporter.findAdminPage,
    operation?: string
  ): Promise<Response<AdministratorDTOPage>> {
    return commonRequestApi<AdministratorDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取管理员分页信息
   * 为渠道商提供
   * @param page
   * @param query
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAdminPageForChannelVendor(
    params: { page?: Page; query?: AdministratorQueryDTO },
    query: DocumentNode = GraphqlImporter.findAdminPageForChannelVendor,
    operation?: string
  ): Promise<Response<AdministratorDTOPage>> {
    return commonRequestApi<AdministratorDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取管理员分页信息
   * 为课件供应商提供
   * @param page
   * @param query
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAdminPageForCoursewareSupplier(
    params: { page?: Page; query?: AdministratorQueryDTO },
    query: DocumentNode = GraphqlImporter.findAdminPageForCoursewareSupplier,
    operation?: string
  ): Promise<Response<AdministratorDTOPage>> {
    return commonRequestApi<AdministratorDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取管理员分页信息
   * 为机构提供
   * @param page
   * @param query
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAdminPageForTrainingInstitution(
    params: { page?: Page; query?: AdministratorQueryDTO },
    query: DocumentNode = GraphqlImporter.findAdminPageForTrainingInstitution,
    operation?: string
  ): Promise<Response<AdministratorDTOPage>> {
    return commonRequestApi<AdministratorDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询单位下的所有学员（含子单位学员）
   * @param page
   * @param query
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findStudentPage(
    params: { page?: Page; query?: StudentQueryDTO },
    query: DocumentNode = GraphqlImporter.findStudentPage,
    operation?: string
  ): Promise<Response<StudentItemDTOPage>> {
    return commonRequestApi<StudentItemDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取管理员
   * @param userId
   * @return
   * @param query 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAdminInfo(
    userId: string,
    query: DocumentNode = GraphqlImporter.getAdminInfo,
    operation?: string
  ): Promise<Response<AdministratorDTO>> {
    return commonRequestApi<AdministratorDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { userId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取当前用户绑定的账号信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCurrentUserBindedThirdAccounts(
    query: DocumentNode = GraphqlImporter.getCurrentUserBindedThirdAccounts,
    operation?: string
  ): Promise<Response<Array<UserBindedThirdAccountDTO>>> {
    return commonRequestApi<Array<UserBindedThirdAccountDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取当前用户的用户信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCurrentUserInfo(
    query: DocumentNode = GraphqlImporter.getCurrentUserInfo,
    operation?: string
  ): Promise<Response<UserInfoDTO>> {
    return commonRequestApi<UserInfoDTO>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学员详情
   * @return
   * @param query 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudent(
    userId: string,
    query: DocumentNode = GraphqlImporter.getStudent,
    operation?: string
  ): Promise<Response<StudentDTO>> {
    return commonRequestApi<StudentDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { userId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 统计用户数量
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentCount(
    query: DocumentNode = GraphqlImporter.getStudentCount,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取指定用户的第三方绑定账号信息
   * @param query 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserBindedThirdAccounts(
    userId: string,
    query: DocumentNode = GraphqlImporter.getUserBindedThirdAccounts,
    operation?: string
  ): Promise<Response<Array<UserBindedThirdAccountDTO>>> {
    return commonRequestApi<Array<UserBindedThirdAccountDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { userId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据微信授权码解析openId和unionId
   * ps:接口弃用，状态层直接调用微服务接口
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getWXUserInfo(
    param: WXInfoQueryParam,
    query: DocumentNode = GraphqlImporter.getWXUserInfo,
    operation?: string
  ): Promise<Response<WXIdInfo>> {
    return commonRequestApi<WXIdInfo>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 是否绑定微信第三方账号
   * @param openId
   * @param unionId
   * @param connectType 2：微信登录，21：微信小程序
   * @param wxNickname  微信昵称，若其他类型已绑定将使用这个微信昵称自动绑定，为空则使用用户昵称
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async haveBindWXByOpenIdAndUnionIdForOpenAPI(
    params: { openId?: string; unionId?: string; connectType: number; wxNickname?: string },
    query: DocumentNode = GraphqlImporter.haveBindWXByOpenIdAndUnionIdForOpenAPI,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async identityExists(
    params: { identityType?: LoginType; identity?: string; exceptUserId?: string },
    query: DocumentNode = GraphqlImporter.identityExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 手机号是否已有学员使用
   * @param phoneNumber
   * @param exceptUserId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async isPhoneNumberExists(
    params: { phoneNumber: string; exceptUserId?: string },
    query: DocumentNode = GraphqlImporter.isPhoneNumberExists,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 当前用户是否已完善手机号
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async isUserPerfectedPhoneNumber(
    query: DocumentNode = GraphqlImporter.isUserPerfectedPhoneNumber,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询用户信息
   * 上限userId:300
   * <p>
   * todo 暂无需鉴权，用户评价记录用到，需调整
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserInfo(
    param: UserParamDTO,
    query: DocumentNode = GraphqlImporter.listUserInfo,
    operation?: string
  ): Promise<Response<Array<UserSimpleInfoDTO>>> {
    return commonRequestApi<Array<UserSimpleInfoDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 服务商登录校验
   * @param query 查询 graphql 语法文档
   * @param identity 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async preValidServicerLogin(
    identity: string,
    query: DocumentNode = GraphqlImporter.preValidServicerLogin,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: query,
        variables: { identity },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 用户是否存在
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async userExistsWithIdentityAndName(
    params: { identity: string; name: string },
    query: DocumentNode = GraphqlImporter.userExistsWithIdentityAndName,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 当前用户绑定微信小程序(新增用户头像路径字段)
   * @param userBindWXDto
   * @param mutate 查询 graphql 语法文档
   * @param userBindWXDto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async bindUserWXMiniProgramForOpenAPI(
    userBindWXDto: UserBindWXDto,
    mutate: DocumentNode = GraphqlImporter.bindUserWXMiniProgramForOpenAPI,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { userBindWXDto },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 当前用户绑定微信小程序
   * @param openId
   * @param unionId
   * @param wxNickname
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async bindWXMiniProgramForOpenAPI(
    params: { openId?: string; unionId?: string; wxNickname?: string },
    mutate: DocumentNode = GraphqlImporter.bindWXMiniProgramForOpenAPI,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 当前用户绑定微信公众号
   * @param openId
   * @param unionId
   * @param wxNickname
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async bindWXOfficialAccountForOpenAPI(
    params: { openId?: string; unionId?: string; wxNickname?: string },
    mutate: DocumentNode = GraphqlImporter.bindWXOfficialAccountForOpenAPI,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建服务商子管理员
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createServicerSubAdmin(
    request: AdministratorCreateDTO,
    mutate: DocumentNode = GraphqlImporter.createServicerSubAdmin,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建平台管理员
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createSubAdmin(
    request: AdministratorCreateDTO,
    mutate: DocumentNode = GraphqlImporter.createSubAdmin,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 发送绑定通知小程序的通知
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sendBindingSuccessMessage(
    mutate: DocumentNode = GraphqlImporter.sendBindingSuccessMessage,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 解绑微信
   * @param mutate 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unBindCurrentUserWX(
    mutate: DocumentNode = GraphqlImporter.unBindCurrentUserWX,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据账户id解绑微信
   * @param mutate 查询 graphql 语法文档
   * @param accountId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async unBindStudentWX(
    accountId: string,
    mutate: DocumentNode = GraphqlImporter.unBindStudentWX,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { accountId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 当前用户换绑手机号
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param captchaToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCurrentPhoneWithPhoneCaptchaToken(
    captchaToken: string,
    mutate: DocumentNode = GraphqlImporter.updateCurrentPhoneWithPhoneCaptchaToken,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { captchaToken },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 当前服务商主账户换绑手机号
   * 渠道商
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param captchaToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCurrentServicerAdminPhoneForChannelVendor(
    captchaToken: string,
    mutate: DocumentNode = GraphqlImporter.updateCurrentServicerAdminPhoneForChannelVendor,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { captchaToken },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 当前服务商主账户换绑手机号
   * 课件供应商
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param captchaToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateCurrentServicerAdminPhoneForCoursewareSupplier(
    captchaToken: string,
    mutate: DocumentNode = GraphqlImporter.updateCurrentServicerAdminPhoneForCoursewareSupplier,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { captchaToken },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 用户完善手机号
   * @param captchaToken
   * @param mutate 查询 graphql 语法文档
   * @param captchaToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async userPerfectPhoneNumber(
    captchaToken: string,
    mutate: DocumentNode = GraphqlImporter.userPerfectPhoneNumber,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { captchaToken },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 微信小程序用户完善手机号接口
   * @param phoneNum
   * @param mutate 查询 graphql 语法文档
   * @param phoneNum 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async xwMiniProgramUserPerfectPhoneNumber(
    phoneNum: string,
    mutate: DocumentNode = GraphqlImporter.xwMiniProgramUserPerfectPhoneNumber,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { phoneNum },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
