// import studentCourseLearningQuery from '@api/platform-gateway/platform-jxjypxtypt-ahzj-student-learning-backstage'
import studentCourseLearningQuery from '@api/platform-gateway/platform-jxjypxtypt-gszj-student-learning-backstage'
import studentCourseLearningQueryDiff from '@api/platform-gateway/platform-jxjypxtypt-student-learning-backstage'
export default class GSZJStudentTrainClassDetailVo {
  /**
   * 重新推送 甘肃网校
   */
  async rePush(studentNoList: string[]) {
    const res = await studentCourseLearningQueryDiff.rePushStudentTrainingResultToGatewayInServicerV2(studentNoList)
    return res
  }
}
