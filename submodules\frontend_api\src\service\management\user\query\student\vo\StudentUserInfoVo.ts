/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2022-10-17 09:30:32
 * @LastEditors: <PERSON><PERSON><PERSON>
 * @LastEditTime: 2022-11-16 16:27:29
 * @Description:
 */
import {
  RegionModel,
  StudentInfoResponse,
  StudentUserInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { GendersEnum } from '@api/service/common/enums/user/GenderTypes'
import { IdentityTypeEnum } from '@api/service/common/enums/user/IdentityType'

class StudentUserInfoVo extends StudentUserInfoResponse {
  /**
   * 用户昵称
   */
  nickName = ''
  /**
   * 单位所属地区
   */
  region: RegionModel = new RegionModel()
  /**
   * 工作单位名称
   */
  companyName = ''
  /**
   * 工作单位统一社会信用代码
   */
  companyCode = ''
  /**
   * 头像地址
   */
  photo = ''
  /**
   * 联系地址
   */
  address = ''
  /**
   * 是否绑定微信
   */
  bindWX: boolean = null
  /**
   * 微信昵称
   */
  nickNameByWX = ''
  /**
   * 用户id
   */
  userId = ''
  /**
   * 用户名称
   */
  userName = ''
  /**
   * 登录账号
   */
  loginAccount = ''
  /**
   * 证件号
   */
  idCard = ''
  /**
   * 手机号
   */
  phone = ''
  /**
   * 注册时间
   */
  createTime = ''
  /**
   * 邮箱
   */
  email = ''
  /**
   * 性别
     -1未知，0女，1男
   */
  gender: GendersEnum = -1

  from(item: StudentInfoResponse) {
    this.nickName = item.userInfo?.nickName
    this.region = item.userInfo?.companyRegion
    this.companyName = item.userInfo?.companyName
    this.companyCode = item.userInfo?.companyCode
    this.photo = item.userInfo?.photo
    this.address = item.userInfo?.address
    this.bindWX = item.openPlatformBind?.bindWX
    this.nickNameByWX = item.openPlatformBind?.nickNameByWX
    this.userId = item.userInfo?.userId
    this.userName = item.userInfo?.userName
    this.idCard = item.userInfo?.idCard
    this.phone = item.userInfo?.phone
    this.createTime = item.accountInfo?.createdTime
    this.email = item.userInfo?.email
    this.gender = item.userInfo?.gender
    this.loginAccount = item.authenticationList?.find(
      item => item.identityType === IdentityTypeEnum.user_name
    )?.identity
  }
}

export default StudentUserInfoVo
