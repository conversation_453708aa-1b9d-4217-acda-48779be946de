const got = require('got')
const apiPrefix = '/api/0'

class Request {
  constructor() {
    this.org = ''
    this.authToken = ''
    this.host = ''
  }

  request(url) {
    console.log('请求地址：' + `${this.host}${apiPrefix}/${url}`)
    return got.get(`${this.host}${apiPrefix}/${url}`, {
      headers: {
        Authorization: `Mship ${this.authToken}`
      },
      responseType: 'json'
    })
  }
}

class Sentry extends Request {
  constructor(options) {
    super()
    this.org = options.org
    this.authToken = options.authToken
    this.host = options.host
  }

  async getProjectKeys(projectSlug, name = 'Default') {
    const url = `projects/${this.org}/${projectSlug}/keys/`
    const { body } = await this.request(url)
    return body.find(keyItem => {
      return keyItem.name === name
    })
  }
}

module.exports = Sentry
