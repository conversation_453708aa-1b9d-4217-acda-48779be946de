<template>
  <div>
    <div class="f-p15">
      <div class="m-tit is-border-bottom f-justify-between">
        <el-button type="primary" @click="showLearningDialog">选择培训方案</el-button>
        <el-link type="primary" :underline="false" class="m-specialimg-pop">
          <i class="el-icon-picture f-f20 f-mr5 f-vm"></i>查看专题示例
          <el-image class="transparent-pic" :src="elImage" :preview-src-list="reviewImage" />
        </el-link>
      </div>
      <div class="f-p15">
        <!--条件查询-->
        <hb-search-wrapper @reset="resetQueryParam" class="m-query is-border-bottom">
          <el-form-item label="年度">
            <biz-year-select v-model="localSkuProperty.year" placeholder="请选择培训年度"></biz-year-select>
          </el-form-item>
          <el-form-item label="地区">
            <biz-national-region
              v-model="localSkuProperty.region"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-national-region>
          </el-form-item>
          <el-form-item label="行业">
            <biz-industry-select
              v-model="localSkuProperty.industry"
              @clearIndustrySelect="handleClearIndustrySelect"
              @industryPropertyId="handleIndustryPropertyId"
              @industryInfos="handleIndustryInfos"
              ref="industrySelect"
            ></biz-industry-select>
          </el-form-item>
          <template v-if="localSkuProperty.industry && localSkuProperty.industry === envConfig.teacherIndustryId">
            <el-form-item label="学段">
              <biz-study-period
                v-model="localSkuProperty.studyPeriodId"
                :industry-id="localSkuProperty.industry"
                :industry-property-id="industryPropertyId"
                @updateStudyPeriod="updateStudyPeriod"
                @clearSubject="clearSubject"
              ></biz-study-period>
            </el-form-item>
            <el-form-item label="学科">
              <biz-subject
                v-model="localSkuProperty.subjectId"
                :industry-property-id="industryPropertyId"
                :studyPeriodId="localSkuProperty.studyPeriodId"
                @updateSubject="updateSubject"
              ></biz-subject>
            </el-form-item>
          </template>
          <el-form-item
            label="技术等级"
            v-if="
              skuVisible.jobLevel && localSkuProperty.industry && localSkuProperty.industry === envConfig.workServiceId
            "
          >
            <biz-technical-grade-select
              v-model="localSkuProperty.jobLevel"
              :industry-id="localSkuProperty.industry"
              :industry-property-id="industryPropertyId"
            ></biz-technical-grade-select>
          </el-form-item>
          <el-form-item label="科目类型" v-if="skuVisible.subjectType && localSkuProperty.industry">
            <biz-accounttype-select
              v-model="localSkuProperty.subjectType"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            >
            </biz-accounttype-select>
          </el-form-item>

          <!-- 药师行业-执业类别 -->
          <el-form-item
            label="执业类别"
            v-if="
              skuVisible.practiceCategory &&
              localSkuProperty.industry &&
              localSkuProperty.industry === envConfig.medicineIndustryId
            "
          >
            <biz-practicing-category-cascader
              v-model="localSkuProperty.pharmacistIndustry"
              :industryId="localSkuProperty.industry"
            ></biz-practicing-category-cascader>
          </el-form-item>

          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-major-cascader
              v-model="localSkuProperty.societyTrainingMajor"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              (localSkuProperty.industry === envConfig.constructionIndustryId ||
                localSkuProperty.industry === envConfig.occupationalHealthId)
            "
          >
            <biz-training-category-select
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              @updateTrainingCategory="handleUpdateTrainingCategory"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-major-select
              v-model="localSkuProperty.constructionTrainingMajor"
              :industry-property-id="industryPropertyId"
              :training-category-id="trainingCategoryId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训对象"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              localSkuProperty.industry === envConfig.occupationalHealthId
            "
          >
            <biz-training-object-select
              v-model="localSkuProperty.trainingObject"
              placeholder="请选择培训对象"
              :industry-property-id="industryPropertyId"
              :industry-id="localSkuProperty.industry"
              @updateTrainingCategory="updateTrainingCategory"
            />
          </el-form-item>
          <el-form-item
            label="岗位类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              localSkuProperty.industry === envConfig.occupationalHealthId
            "
          >
            <biz-obj-category-select
              v-model="localSkuProperty.positionCategory"
              placeholder="请选择岗位类别"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              :training-object-id="localSkuProperty.trainingObject"
            />
          </el-form-item>
          <el-form-item label="培训形式">
            <biz-training-mode-select v-model="localSkuProperty.trainingMode"></biz-training-mode-select>
          </el-form-item>
          <el-form-item label="培训方案类型">
            <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
          </el-form-item>
          <el-form-item label="培训方案名称">
            <el-input clearable placeholder="请输入培训方案名称" v-model="schemeName" />
          </el-form-item>
          <el-form-item label="销售状态">
            <el-select
              clearable
              @clear="onShelveStatus = undefined"
              placeholder="请选择销售状态"
              v-model="onShelveStatus"
            >
              <el-option
                v-for="item in onShelveStatusOptions"
                :key="item.id"
                :label="item.label"
                :value="item.id"
              ></el-option>
            </el-select>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="searchBase">查询</el-button>
          </template>
        </hb-search-wrapper>
        <el-alert type="warning" :closable="false" class="m-alert f-mb15">
          当前已选中 <span class="f-fb">{{ count }}</span> 个培训方案
        </el-alert>
        <!--表格-->
        <el-table
          stripe
          :data="trainClassCommodityList"
          class="m-table"
          ref="trainClassCommodityListRef"
          v-loading="loading"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <!-- <el-table-column label="排序" min-width="80" align="center" fixed="left">
            <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
          </el-table-column> -->
          <el-table-column label="培训方案名称" min-width="300" fixed="left">
            <template slot-scope="scope">
              <p>
                <biz-show-scheme-type
                  :scheme-type="scope.row.schemeType"
                  :training-mode="scope.row.skuValueNameProperty.trainingMode.skuPropertyValueId"
                ></biz-show-scheme-type>
                <el-tooltip
                  class="item"
                  effect="dark"
                  :content="scope.row.commodityBasicData.saleTitle"
                  placement="top"
                >
                  <span>{{ scope.row.commodityBasicData.saleTitle }}</span>
                </el-tooltip>
              </p>
            </template>
          </el-table-column>
          <el-table-column label="报名学时" min-width="110" align="center">
            <template slot-scope="scope">{{ scope.row.period }}</template>
          </el-table-column>
          <el-table-column label="价格" min-width="100" align="right">
            <template slot-scope="scope">{{ scope.row.commodityBasicData.price }}</template>
          </el-table-column>
          <el-table-column label="培训属性" min-width="240">
            <template slot-scope="scope">
              <p v-if="getSkuPropertyName(scope.row, 'industry')">
                行业：{{ getSkuPropertyName(scope.row, 'industry') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'region')">地区：{{ getSkuPropertyName(scope.row, 'region') }}</p>
              <p v-if="getSkuPropertyName(scope.row, 'jobLevel')">
                技术等级：{{ getSkuPropertyName(scope.row, 'jobLevel') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'subjectType')">
                科目类型：{{ getSkuPropertyName(scope.row, 'subjectType') }}
              </p>
              <p v-if="!scope.row.isSocietyIndustry && getSkuPropertyName(scope.row, 'trainingCategory')">
                培训类别：{{ getSkuPropertyName(scope.row, 'trainingCategory') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'trainingMajor')">
                培训专业：{{ getSkuPropertyName(scope.row, 'trainingMajor') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'trainingObject')">
                培训对象：{{ getSkuPropertyName(scope.row, 'trainingObject') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'positionCategory')">
                岗位类别：{{ getSkuPropertyName(scope.row, 'positionCategory') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'year')">培训年度：{{ getSkuPropertyName(scope.row, 'year') }}</p>
              <p v-if="getSkuPropertyName(scope.row, 'learningPhase')">
                学段：{{ getSkuPropertyName(scope.row, 'learningPhase') }}
              </p>
              <p v-if="getSkuPropertyName(scope.row, 'discipline')">
                学科：{{ getSkuPropertyName(scope.row, 'discipline') }}
              </p>
            </template>
          </el-table-column>
          <el-table-column label="销售状态" min-width="140">
            <template slot-scope="scope">
              <div v-if="scope.row.onShelve.shelveStatus === 0">
                <el-badge is-dot type="info" class="badge-status">已下架</el-badge>
              </div>
              <div v-if="scope.row.onShelve.shelveStatus === 1">
                <el-badge is-dot type="success" class="badge-status">已上架</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学习起止时间" min-width="220">
            <template slot-scope="scope">
              <p v-if="!isValid(scope.row)">起始：{{ scope.row.trainingBeginDate }}</p>
              <p v-if="!isValid(scope.row)">结束：{{ scope.row.trainingEndDate }}</p>
              <p v-if="isValid(scope.row)">长期有效</p>
            </template>
          </el-table-column>
          <el-table-column label="报名起止时间" min-width="220">
            <template slot-scope="scope">
              <p>起始：{{ scope.row.registerBeginDate }}</p>
              <p>结束：{{ scope.row.registerEndDate }}</p>
            </template>
          </el-table-column>
          <el-table-column label="最新修改时间" sortable min-width="180">
            <template slot-scope="scope">{{ scope.row.commodityLastEditTime }}</template>
          </el-table-column>
          <el-table-column label="是否展示在专题门户" min-width="180">
            <template>展示</template>
          </el-table-column>
          <el-table-column label="展示用户" min-width="180">
            <template>
              <p>学员门户可见</p>
              <p>集体报名管理员可见</p>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="cancelChoose(scope.$index)">取消选择</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <learning-scheme ref="LearningScheme" v-model="commoditySkuIdList" :count="count"></learning-scheme>

    <div class="m-btn-bar f-tc is-sticky f-pt15" style="z-index: 8">
      <el-button @click.stop="cancel">取 消</el-button>
      <el-button @click.stop="BackStep">返回上一步</el-button>
      <el-button type="primary" @click.stop="commitDraft" :loading="btLoading" :disabled="isDisabled">
        保 存
      </el-button>
    </div>
  </div>
</template>
<script lang="ts">
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import { IndustryPropertyCodeEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryPropertyCodeEnum'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import BizAccounttypeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-accounttype-select.vue'
  import BizIndustrySelect from '@hbfe/jxjy-admin-components/src/biz/biz-industry-select.vue'
  import BizMajorCascader from '@hbfe/jxjy-admin-components/src/biz/biz-major-cascader.vue'
  import BizMajorSelect from '@hbfe/jxjy-admin-components/src/biz/biz-major-select.vue'
  import BizNationalRegion from '@hbfe/jxjy-admin-components/src/biz/biz-national-region.vue'
  import BizTechnicalGradeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-technical-grade-select.vue'
  import BizTrainingCategorySelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-category-select.vue'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-scheme/src/models/SchemeSkuProperty'
  import LearningScheme from '@hbfe/jxjy-admin-specialTopics/src/add/components/learning-scheme.vue'
  import { cloneDeep } from 'lodash'
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'

  import {
    CommoditySkuRequest,
    OnShelveRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest,
    SkuPropertyRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import { Page } from '@hbfe/common'
  import UITrainClassCommodityDetail from '@hbfe/jxjy-admin-scheme/src/models/UITrainClassCommodityDetail'

  import { DeleteScheme, SchemeList } from '@api/platform-gateway/platform-training-channel-v1'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'

  import { RegionSkuPropertyRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import BizShowSchemeType from '@hbfe/jxjy-admin-components/src/biz/biz-show-scheme-type.vue'
  import BizTrainingModeSelect from '@hbfe/jxjy-admin-components/src/biz/biz-training-mode-select.vue'

  @Component({
    components: {
      BizTrainingModeSelect,
      BizMajorCascader,
      BizTrainingCategorySelect,
      BizYearSelect,
      BizMajorSelect,
      BizTechnicalGradeSelect,
      BizIndustrySelect,
      BizAccounttypeSelect,
      BizNationalRegion,
      LearningScheme,
      BizShowSchemeType
    }
  })
  export default class extends Vue {
    //TODO

    @Ref('LearningScheme') LearningScheme: any
    count = 0 //当前已选中的方案数
    loading = false
    btLoading = false

    elImage = require('@design/admin/assets/images/transparent-pic.png')
    reviewImage = [require('@design/admin/assets/images/demo-special-web-001.png')]
    selectedTrainingPlanID: { schemeId: string; sort: number }[] = []
    // 商品id数组
    commoditySkuIdList = new Array<HasSelectSchemeMode>()
    addScheme: Array<SchemeList>
    updateScheme: Array<SchemeList>
    deleteScheme: Array<DeleteScheme>
    queryTrainClassCommodityList = new QueryTrainClassCommodityList()
    //培训班商品请求参数
    filterCommodity: CommoditySkuRequest
    onShelveStatusOptions = [
      { id: 1, label: '已上架' },
      { id: 0, label: '已下架' }
    ]
    onShelveStatus: number = null //报名状态
    schemeName = '' // 培训方案名称
    schemeTypeInfo: Array<string> = new Array<string>() //培训方案类型
    industryPropertyId = '' //行业属性分类Id
    trainingCategoryId = '' //类别id

    localSkuProperty: SchemeSkuProperty = {
      /**
       * 年度
       */
      year: '',
      /**
       * 地区
       */
      region: [] as string[],
      /**
       * 行业
       */
      industry: '',
      /**
       * 科目类型
       */
      subjectType: '',
      /**
       * 培训类别
       */
      trainingCategory: '',
      /**
       * 培训专业 - 建设行业
       */
      constructionTrainingMajor: '',
      /**
       * 培训专业 - 人社行业
       */
      societyTrainingMajor: [] as string[],
      /**
       * 技术等级 - 工勤行业
       **/
      jobLevel: '',
      /**
       * 培训对象
       */
      trainingObject: '',
      /**
       * 岗位类别
       */
      positionCategory: '',
      /**
       * 学段id
       */
      studyPeriodId: '',
      /**
       * 学科id
       */
      subjectId: ''
    } as SchemeSkuProperty
    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: true,
      // 培训类别
      trainingCategory: true,
      // 技术等级
      jobLevel: true,
      // 培训对象
      trainingObject: true,
      //   岗位类别
      positionCategory: true,
      //   执业类别
      practiceCategory: true
    }
    /**
     * 当前网校信息
     */
    envConfig = {
      // 工勤行业
      workServiceId: '',
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 职业卫生行业Id
      occupationalHealthId: '',
      //教师行业id
      teacherIndustryId: '',
      // 药师行业id
      medicineIndustryId: ''
    }
    /**
     * 培训班商品列表
     */
    trainClassCommodityList: TrainClassCommodityVo[] = []

    /**
     * 培训班商品列表传递给父组件，预览时，存本地的数据
     */
    @Prop({
      type: Array,
      default: () => new Array<TrainClassCommodityVo>()
    })
    trainClassList: TrainClassCommodityVo[] = []

    /**
     * 灰显下一步按钮
     */
    get isDisabled() {
      return !(this.selectedTrainingPlanID.length > 0)
    }

    // 培训方案入参
    @Watch('commoditySkuIdList', {
      deep: true
    })
    async changeScheme(val: Array<HasSelectSchemeMode>) {
      if (val?.length > 0) {
        this.selectedTrainingPlanID = []
        val.forEach((item, index) => {
          const obj = {
            schemeId: item.id,
            sort: index
          }
          this.selectedTrainingPlanID.push(obj)
          this.count = this.selectedTrainingPlanID.length
        })
        await this.allScheme()
      } else {
        this.selectedTrainingPlanID = []
      }
    }

    /**
     * 获取培训方案类型
     */
    getSchemeType(row: UITrainClassCommodityDetail) {
      return SchemeType.getSchemeType(row.schemeType, true)
    }

    /**
     * 判断是否长期有效
     */
    isValid(row: TrainClassCommodityVo) {
      if (row.trainingBeginDate == '1900-01-01 00:00:00' && row.trainingEndDate == '2100-01-01 00:00:00') {
        return true
      } else {
        return false
      }
    }

    /**
     * 获取培训方案sku属性值
     */
    getSkuPropertyName(row: UITrainClassCommodityDetail, type: string): string {
      if (row.skuValueNameProperty[type]?.skuPropertyName) {
        const value = row.skuValueNameProperty[type].skuPropertyName
        const valuesArr = value.split('/'),
          lastIndex = valuesArr.length - 1
        return type === 'trainingMajor' && !row.isSocietyIndustry ? valuesArr[lastIndex] : value
      }
      return ''
    }

    //打开选择培训方案弹窗
    showLearningDialog() {
      this.LearningScheme.showDialog()
    }

    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      this.initQueryParam()
      this.localSkuProperty = new SchemeSkuProperty()
      this.onShelveStatus = null
      this.schemeName = ''
      //   this.sortPolicy = new Array<CommoditySkuSortRequest>()

      // 移除表格排序
      //   this.elTableRef.clearSort()
      await this.searchBase()
    }

    updateTrainingCategory(val: string) {
      if (val) {
        this.localSkuProperty.positionCategory = ''
      }
    }

    clearSubject() {
      this.localSkuProperty.subjectId = ''
      this.localSkuProperty.studyPeriodId = ''
      // this.filterCommodity.skuPropertyRequest.discipline = new Array<string>()
      // this.filterCommodity.skuPropertyRequest.learningPhase = new Array<string>()
    }

    //选择学段
    updateStudyPeriod(val: string) {
      this.localSkuProperty.studyPeriodId = val
      this.localSkuProperty.subjectId = ''
      // this.filterCommodity.skuPropertyRequest.discipline = []
    }

    //选择学科
    updateSubject(val: string) {
      this.localSkuProperty.subjectId = val
    }

    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      // console.log('envConfig', this.envConfig)
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人设行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }

    handleIndustryInfos(values: any) {
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.occupationalHealthId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
      this.envConfig.medicineIndustryId = values.medicineIndustryId || ''
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.jobLevel = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.studyPeriodId = ''
      this.localSkuProperty.subjectId = ''
    }

    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.SUBJECT_TYPE)
      const configTrainingCategory = configList.findIndex(
        (el) => el.code === IndustryPropertyCodeEnum.TRAINING_CATEGORY
      )
      const jobLevel = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.JOB_LEVEL)
      const trainingObject = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.TRAINING_OBJECT)
      const positionCategory = configList.findIndex((el) => el.code === IndustryPropertyCodeEnum.POSITION_CATEGORY)
      this.skuVisible.subjectType = configSubjectType > -1
      this.skuVisible.trainingCategory = configTrainingCategory > -1
      this.skuVisible.jobLevel = jobLevel > -1
      this.skuVisible.trainingObject = trainingObject > -1
      this.skuVisible.positionCategory = positionCategory > -1
    }

    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.filterCommodity = new CommoditySkuRequest()
      this.filterCommodity.onShelveRequest = new OnShelveRequest()
      this.filterCommodity.schemeRequest = new SchemeRequest()
      this.filterCommodity.schemeRequest.schemeName = ''
      this.filterCommodity.skuPropertyRequest = new SkuPropertyRequest()
      this.filterCommodity.skuPropertyRequest.year = new Array<string>()
      this.filterCommodity.skuPropertyRequest.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.filterCommodity.skuPropertyRequest.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      this.filterCommodity.skuPropertyRequest.industry = new Array<string>()
      this.filterCommodity.skuPropertyRequest.subjectType = new Array<string>()
      this.filterCommodity.skuPropertyRequest.trainingCategory = new Array<string>()
      this.filterCommodity.skuPropertyRequest.trainingProfessional = new Array<string>()
      this.filterCommodity.skuPropertyRequest.trainingObject = new Array<string>()
      this.filterCommodity.skuPropertyRequest.positionCategory = new Array<string>()
      this.filterCommodity.skuPropertyRequest.jobLevel = new Array<string>()
      this.filterCommodity.skuPropertyRequest.learningPhase = new Array<string>()
      this.filterCommodity.skuPropertyRequest.discipline = new Array<string>()
      this.filterCommodity.skuPropertyRequest.trainingForm = new Array<string>()
      //   this.sortPolicy = new Array<CommoditySkuSortRequest>()
      this.schemeTypeInfo = new Array<string>()
    }

    /**
     * 获取本地sku选项
     */
    getLocalSkuProperty() {
      const skuProperties = cloneDeep(this.filterCommodity.skuPropertyRequest)
      console.warn('666', this.localSkuProperty, skuProperties)

      skuProperties.year = !this.localSkuProperty.year ? ([] as string[]) : [this.localSkuProperty.year]
      skuProperties.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      const localRegion = cloneDeep(this.localSkuProperty.region)
      if (Array.isArray(localRegion) && localRegion.length) {
        const option = new RegionSkuPropertyRequest()
        option.province = localRegion.length >= 1 ? localRegion[0] : undefined
        option.city = localRegion.length >= 2 ? localRegion[1] : undefined
        option.county = localRegion.length >= 3 ? localRegion[2] : undefined
        skuProperties.regionSkuPropertySearch.region.push(option)
        skuProperties.regionSkuPropertySearch.regionSearchType = 1
      } else {
        skuProperties.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      }
      skuProperties.industry = !this.localSkuProperty.industry ? ([] as string[]) : [this.localSkuProperty.industry]
      skuProperties.jobLevel = !this.localSkuProperty.jobLevel ? ([] as string[]) : [this.localSkuProperty.jobLevel]
      skuProperties.subjectType = !this.localSkuProperty.subjectType
        ? ([] as string[])
        : [this.localSkuProperty.subjectType]
      skuProperties.trainingCategory = !this.localSkuProperty.trainingCategory
        ? ([] as string[])
        : [this.localSkuProperty.trainingCategory]
      skuProperties.trainingProfessional = this.getTrainingProfessional()
      skuProperties.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      skuProperties.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      skuProperties.learningPhase = !this.localSkuProperty.studyPeriodId
        ? ([] as string[])
        : [this.localSkuProperty.studyPeriodId]
      skuProperties.discipline = !this.localSkuProperty.subjectId ? ([] as string[]) : [this.localSkuProperty.subjectId]
      skuProperties.trainingForm = !this.localSkuProperty.trainingMode
        ? ([] as string[])
        : [this.localSkuProperty.trainingMode]

      this.filterCommodity.skuPropertyRequest = cloneDeep(skuProperties)
      //   console.log('selectedSkuProperties', JSON.stringify(this.filterCommodity.skuPropertyRequest))
    }

    /**
     * 处理列表查询参数
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.filterCommodity.onShelveRequest.onShelveStatus =
        this.onShelveStatus || this.onShelveStatus === 0 ? this.onShelveStatus : undefined
      this.filterCommodity.schemeRequest.schemeName = this.schemeName || undefined
      this.configureTrainSchemeQueryParam()
    }

    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.filterCommodity.schemeRequest.schemeType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      // if (schemeType === 'chooseCourseLearning' || schemeType === 'autonomousCourseLearning') {
      this.filterCommodity.schemeRequest.schemeType = schemeType
      // } else {
      //   this.filterCommodity.schemeRequest.schemeType = undefined
      // }
    }

    //分页查询已选中的培训方案信息
    async allScheme() {
      this.loading = true
      this.filterCommodity.commoditySkuIdList = []
      if (this.commoditySkuIdList.length > 0) {
        const page = new Page(1, this.commoditySkuIdList.length < 200 ? this.commoditySkuIdList.length : 200)
        this.trainClassCommodityList = []
        const commoditySkuIdList = this.commoditySkuIdList.map((item) => item.schemeId)
        this.filterCommodity.commoditySkuIdList = commoditySkuIdList
        //查询培训方案列表
        this.trainClassCommodityList.push(
          ...(await this.queryTrainClassCommodityList.queryTrainClassCommodityList(page, this.filterCommodity))
        )
        if (page.pageSize > 2) {
          await Promise.all(
            Array.from({ length: page.totalPageSize - 1 }, (v, k) => k + 2).map(async (item) => {
              this.trainClassCommodityList.push(
                ...(await this.queryTrainClassCommodityList.queryTrainClassCommodityList(
                  new Page(item, 200),
                  this.filterCommodity
                ))
              )
            })
          )
        }
        this.$emit('update:trainClassList', this.trainClassCommodityList)
        ;(this.$refs['trainClassCommodityListRef'] as any)?.doLayout()
        console.warn('查询培训方案列表', this.trainClassCommodityList)
        console.warn('查询培训方案参数', this.filterCommodity)
      }
      this.loading = false
    }

    //查询列表
    async searchBase() {
      //TODO
      this.filterCommodity.commoditySkuIdList = this.commoditySkuIdList.map((item) => item.schemeId)
      this.getPageQueryParams()
      this.filterCommodity
      await this.allScheme()
    }

    //取消选择
    async cancelChoose(index: number) {
      //取消选择,以下同步删除
      //已选择的培训方案id数组
      //绑定弹窗的id数组
      //列表数据
      this.trainClassCommodityList.splice(index, 1)
      this.$emit('update:trainClassList', this.trainClassCommodityList)

      this.selectedTrainingPlanID.splice(index, 1)
      this.commoditySkuIdList.splice(index, 1)
      this.count--
    }

    //返回上一部
    BackStep() {
      this.$emit('BackFirstStep', 2)
    }

    //提交保存
    commitDraft() {
      this.btLoading = true
      if (this.selectedTrainingPlanID.length > 0) {
        this.addScheme = this.selectedTrainingPlanID
        this.$emit('saveTrainingChannelScheme', this.addScheme, this.updateScheme, this.deleteScheme, 4)
      } else {
        this.btLoading = false
        this.$message.error('请至少选择一条培训方案')
      }
    }

    //取消新建
    cancel() {
      this.$emit('cancel', 1)
    }

    //清除数据
    clear() {
      this.commoditySkuIdList = new Array<HasSelectSchemeMode>()
    }

    created() {
      this.initQueryParam()
    }
  }
</script>
