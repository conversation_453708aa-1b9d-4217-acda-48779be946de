<route-params content="/:id/:subId"></route-params>
<route-meta>
{
"isMenu": false,
"onlyShowOnTab": true,
"title": "集体订单详情"
}
</route-meta>
<template>
  <div>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-breadcrumb-item :to="{ path: '/training/trade/order/collective' }">集体报名订单</el-breadcrumb-item>
      <el-breadcrumb-item :to="{ path: '/training/trade/order/collective/detail/' + id }">订单信息</el-breadcrumb-item>
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!--交易成功-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>订单号：{{ batchOrderMainOrderDetailVo.orderInfo.mainOrderNo }}</p>
          <p class="state f-co" v-if="batchOrderMainOrderDetailVo.orderInfo.mainOrderStatus === 1">等待付款</p>
          <p class="state f-cb" v-if="batchOrderMainOrderDetailVo.orderInfo.mainOrderStatus === 2">支付中</p>
          <p class="state f-cb" v-if="batchOrderMainOrderDetailVo.orderInfo.mainOrderStatus === 3">开通中</p>
          <p class="state f-cg" v-if="batchOrderMainOrderDetailVo.orderInfo.mainOrderStatus === 4">交易成功</p>
          <p class="state f-c9" v-if="batchOrderMainOrderDetailVo.orderInfo.mainOrderStatus === 5">交易关闭</p>
        </div>
        <!-- 交易成功 -->
        <el-steps
          :active="4"
          v-if="batchOrderMainOrderDetailVo.orderInfo.mainOrderStatus === 4"
          align-center
          class="process"
        >
          <el-step
            title="提交订单"
            :description="batchOrderMainOrderDetailVo.applyTime"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="付款"
            :description="batchOrderMainOrderDetailVo.payTime"
            icon="hb-iconfont icon-s-pay"
          ></el-step>
          <el-step
            title="班级开通"
            :description="batchOrderMainOrderDetailVo.deliveryTime"
            icon="hb-iconfont icon-s-learningcenter"
          ></el-step>
          <el-step
            title="交易成功"
            :description="batchOrderMainOrderDetailVo.tradeSuccessTime"
            icon="hb-iconfont icon-success"
          ></el-step>
        </el-steps>
        <!-- 等待付款 -->
        <el-steps
          :active="1"
          v-if="batchOrderMainOrderDetailVo.orderInfo.mainOrderStatus === 1"
          align-center
          class="process"
        >
          <el-step
            title="提交订单"
            :description="batchOrderMainOrderDetailVo.applyTime"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step title="付款" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step title="班级开通" icon="hb-iconfont icon-s-learningcenter"></el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
        <!--支付中-->
        <el-steps
          :active="1"
          v-if="batchOrderMainOrderDetailVo.orderInfo.mainOrderStatus === 2"
          align-center
          class="process"
        >
          <el-step
            title="提交订单"
            :description="batchOrderMainOrderDetailVo.applyTime"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step title="付款" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step title="班级开通" icon="hb-iconfont icon-s-learningcenter"></el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
        <!--开通中-->

        <el-steps
          :active="batchOrderMainOrderDetailVo.deliveryTime ? 3 : 2"
          v-if="batchOrderMainOrderDetailVo.orderInfo.mainOrderStatus === 3"
          align-center
          class="process"
        >
          <el-step
            title="提交订单"
            :description="batchOrderMainOrderDetailVo.applyTime"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="付款"
            :description="batchOrderMainOrderDetailVo.payTime"
            icon="hb-iconfont icon-s-pay"
          ></el-step>
          <el-step
            title="班级开通"
            :description="batchOrderMainOrderDetailVo.deliveryTime"
            icon="hb-iconfont icon-s-learningcenter"
          ></el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
        <!--交易关闭-->

        <el-steps
          :active="2"
          v-if="batchOrderMainOrderDetailVo.orderInfo.mainOrderStatus === 5"
          align-center
          class="process"
        >
          <el-step
            title="提交订单"
            :description="batchOrderMainOrderDetailVo.applyTime"
            icon="hb-iconfont icon-s-myorder"
          ></el-step>
          <el-step
            title="交易关闭"
            :description="batchOrderMainOrderDetailVo.tradeCloseTime"
            icon="hb-iconfont icon-s-close"
          ></el-step>
        </el-steps>
      </el-card>
      <!--订单信息-->
      <el-card shadow="never" class="m-card is-header m-order-info f-mb15">
        <div class="f-flex-sub f-plr20 f-pt10">
          <div class="m-tit">
            <span class="tit-txt">订单信息</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="12">
              <el-form-item label="订单号：">{{
                batchOrderMainOrderDetailVo.orderInfo.mainOrderNo || '-'
              }}</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="订单状态：">{{
                BatchOrderMainOrderStatusEnum[batchOrderMainOrderDetailVo.mainOrderStatus]
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单类型：">集体报名</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="订单创建人：">{{
                batchOrderMainOrderDetailVo.orderInfo.creatorName || '-'
              }}</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="创建人帐号：">{{
                batchOrderMainOrderDetailVo.orderInfo.creatorAccount || '-'
              }}</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="由集体报名批次单：">
                {{ batchOrderMainOrderDetailVo.orderInfo.batchOrderNo || '-' }} 产生</el-form-item
              >
            </el-col>
          </el-form>
        </div>
        <div class="right f-plr20 f-ptb10">
          <div class="m-tit">
            <span class="tit-txt">购买人信息</span>
          </div>
          <el-form label-width="120px" class="m-text-form is-column f-pt10 f-pb20">
            <el-form-item label="购买人：">{{ batchOrderMainOrderDetailVo.buyerInfo.buyerName || '-' }}</el-form-item>
            <el-form-item label="帐号：">{{ batchOrderMainOrderDetailVo.buyerInfo.buyerAccount || '-' }}</el-form-item>
            <el-form-item label="手机号：">{{ batchOrderMainOrderDetailVo.buyerInfo.buyerPhone || '-' }}</el-form-item>
          </el-form>
        </div>
      </el-card>
      <!--购买清单-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="f-plr20 f-pt10">
          <div class="m-tit is-small">
            <span class="tit-txt">购买清单</span>
          </div>
          <div>
            <el-table class="m-table is-header" ref="elTableRef">
              <el-table-column label="物品名称" min-width="300"></el-table-column>
              <el-table-column label="学时" width="150" align="center"></el-table-column>
              <el-table-column label="数量" width="150" align="center"></el-table-column>
              <el-table-column label="实付金额(元)" width="150" align="right"></el-table-column>
              <el-table-column label="订单状态" width="120"></el-table-column>
              <el-table-column label="售后状态" width="120" align="center"></el-table-column>
              <el-table-column label="售后" width="150" align="center"></el-table-column>
            </el-table>
            <div v-for="(item, index) in batchOrderMainOrderDetailVo.subOrderList" :key="index">
              <el-alert type="info" :closable="false" class="m-alert f-mt10">
                <div class="f-flex">
                  <i class="iconfont icon-fangan f-cb f-mr5"></i>
                  <div class="f-flex-sub">
                    <span class="f-fb f-cb">培训方案：【{{ getSchemeType(item) }}】{{ item.schemeName }}</span>
                    <el-tag class="f-ml10" size="small">{{ item.skuValueNameProperty.year.skuPropertyName }}年</el-tag>
                    <el-tag class="f-ml10" size="small" v-if="item.skuValueNameProperty.industry.skuPropertyName">{{
                      item.skuValueNameProperty.industry.skuPropertyName
                    }}</el-tag>
                    <el-tag class="f-ml10" size="small" v-if="item.skuValueNameProperty.subjectType.skuPropertyName">{{
                      item.skuValueNameProperty.subjectType.skuPropertyName
                    }}</el-tag>
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item.skuValueNameProperty.trainingCategory.skuPropertyName"
                      >{{ item.skuValueNameProperty.trainingCategory.skuPropertyName }}</el-tag
                    >
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item.skuValueNameProperty.trainingMajor.skuPropertyName"
                      >{{ item.skuValueNameProperty.trainingMajor.skuPropertyName }}</el-tag
                    >
                    <el-tag class="f-ml10" size="small" v-if="item.skuValueNameProperty.jobLevel.skuPropertyName">{{
                      item.skuValueNameProperty.jobLevel.skuPropertyName
                    }}</el-tag>
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item.skuValueNameProperty.trainingObject.skuPropertyName"
                      >{{ item.skuValueNameProperty.trainingObject.skuPropertyName }}</el-tag
                    >
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item.skuValueNameProperty.positionCategory.skuPropertyName"
                      >{{ item.skuValueNameProperty.positionCategory.skuPropertyName }}</el-tag
                    >

                    <!-- 药师行业 -证书类型-执业类别-->
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="
                        item.skuValueNameProperty.certificatesType.skuPropertyName &&
                        item.skuValueNameProperty.practitionerCategory.skuPropertyName
                      "
                      >{{ item.skuValueNameProperty.certificatesType.skuPropertyName }}({{
                        item.skuValueNameProperty.practitionerCategory.skuPropertyName
                      }})</el-tag
                    >

                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item.skuValueNameProperty.technicalGrade.skuPropertyName"
                      >{{ item.skuValueNameProperty.technicalGrade.skuPropertyName }}</el-tag
                    >
                    <el-tag class="f-ml10" size="small" v-if="item.skuValueNameProperty.region.skuPropertyName">{{
                      getRegionName(item.skuValueNameProperty.region.skuPropertyName)
                    }}</el-tag>
                    <!-- 教师行业 -->
                    <el-tag
                      class="f-ml10"
                      size="small"
                      v-if="item.skuValueNameProperty.learningPhase.skuPropertyName"
                      >{{ item.skuValueNameProperty.learningPhase.skuPropertyName }}</el-tag
                    >
                    <el-tag class="f-ml10" size="small" v-if="item.skuValueNameProperty.discipline.skuPropertyName">{{
                      item.skuValueNameProperty.discipline.skuPropertyName
                    }}</el-tag>
                  </div>
                </div>
              </el-alert>
              <el-table :data="batchOrderMainOrderDetailVo.subOrderList" max-height="500px" class="m-table is-body">
                <el-table-column min-width="300">
                  <template>
                    <div class="f-flex f-align-start">
                      <div style="display: flex; flex-direction: column">
                        <el-tag v-if="item.exchangeStatus" size="small">换班</el-tag>
                        <el-tag v-if="item.isExchangeIssue" type="warning" size="small">换期</el-tag>
                      </div>
                      <div class="f-flex-sub f-mt5 f-ml5">
                        <p>
                          {{ item.schemeName || '-' }}
                          <el-tag
                            type="success"
                            size="small"
                            class="f-ml10"
                            v-if="batchOrderMainOrderDetailVo.saleChannel == 2"
                            >专题</el-tag
                          >
                        </p>
                        <p v-if="item.issueName">
                          <el-tag type="info" size="mini">培训期别</el-tag>{{ item.issueName }}
                        </p>
                        <p
                          v-if="batchOrderMainOrderDetailVo.saleChannelName"
                          :title="batchOrderMainOrderDetailVo.saleChannelName"
                        >
                          专题名称:{{ batchOrderMainOrderDetailVo.saleChannelName }}
                        </p>
                        <a
                          class="f-link f-underline f-cb f-f13"
                          v-if="item.exchangeStatus || item.isExchangeIssue"
                          @click="isShowAssociatedShift(item.subOrderNo)"
                          >查看关联售后订单</a
                        >
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column width="150" align="center">
                  <template>{{ item.period || '-' }}</template>
                </el-table-column>
                <el-table-column width="150" align="center">
                  <template>{{ item.quantities || '-' }}</template>
                </el-table-column>
                <el-table-column width="150" align="right">
                  <template
                    ><span class="f-cr">{{ item.payAmount || '-' }}</span></template
                  >
                </el-table-column>
                <el-table-column width="120">
                  <template>
                    <el-badge is-dot type="success" class="badge-status">{{
                      BatchOrderSubOrderStatusEnum[item.subOrderStatus] || '-'
                    }}</el-badge>
                  </template>
                </el-table-column>
                <el-table-column width="120" align="center">
                  <template>
                    <el-tag v-if="item.exchangeStatus == 0">-</el-tag>
                    <el-tag v-if="item.exchangeStatus == 3" type="success">换班成功</el-tag>
                    <el-tag type="success" v-if="item.isExchangeIssue">换期成功</el-tag>
                    <el-tag type="warning" v-if="item.exchangeStatus == 1 || item.exchangeStatus == 2">换班中</el-tag>
                    <p class="f-mt5" v-if="item.exchangeStatus == 3">
                      <a @click="isShowChangeShiftsDetailDialog(item.subOrderNo)" class="f-link f-underline f-cb f-f12"
                        >查看详情</a
                      >
                    </p>
                  </template>
                </el-table-column>
                <el-table-column width="150" align="center">
                  <template v-if="isShipments(item.subOrderStatus)">
                    <!-- <el-button type="text" size="mini" @click="clickRefund(item)">{{
                      BatchOrderSubOrderAfterSaleStatusEnum[item.afterSale] || '-'
                    }}</el-button> -->
                    <el-button
                      type="text"
                      size="mini"
                      v-if="(item.afterSale === 1 || item.afterSale === 5 || item.afterSale === 6) && !isZtlogin"
                      @click="clickRefund(item)"
                      >退款</el-button
                    >
                    <el-button
                      type="text"
                      size="mini"
                      v-else-if="!(item.afterSale === 1 || item.afterSale === 5 || item.afterSale === 6)"
                      @click="clickToDetail(item)"
                      >退款详情</el-button
                    >
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <div class="m-order-sum f-mtb30">
            <div class="item">
              共
              <i class="f-cr">{{ batchOrderMainOrderDetailVo.subOrderListStatistic.totalPeriod }}</i> 学时，商品总额：
              <span class="price"
                >¥ <i class="num">{{ batchOrderMainOrderDetailVo.subOrderListStatistic.totalAmount }}</i></span
              >
            </div>
            <div class="sum-price">
              实付金额：<span class="price"
                >¥ <i class="num">{{ batchOrderMainOrderDetailVo.subOrderListStatistic.payAmount }}</i></span
              >
            </div>
          </div>
        </div>
      </el-card>
    </div>
    <el-drawer title="关联换班信息" :visible.sync="associatedInformationDialog" size="1200px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-table stripe :data="exchangeOrderRecordDetailVo" max-height="500px" class="m-table">
          <el-table-column label="换班时间" min-width="180" fixed="left">
            <template slot-scope="scope"> {{ scope.row.exchangeTrainClassTime || '-' }}</template>
          </el-table-column>
          <el-table-column label="初始班级（换班前）" min-width="300" fixed="left">
            <template slot-scope="scope">
              <!-- <div v-if="scope.row.originalCommodity.resourceVo.schemeName.length > 20">
                <el-popover placement="top-start" width="400" trigger="hover">
                  <div slot="reference">
                    <div class="f-to-two">
                      【{{ scope.row.originalCommodity.resourceVo.schemeTypeVo == 1 ? '选课规则' : '自主选课' }}】{{
                        scope.row.originalCommodity.skuValueNameProperty.year.skuPropertyName
                      }}{{ scope.row.originalCommodity.resourceVo.schemeName }}
                    </div>
                    <p>
                      <el-tag type="danger" size="mini" v-if="scope.row.originalCommodity.exchangeGoodsStatus === 1"
                        >初始物品</el-tag
                      >
                    </p>
                  </div>
                  <p>
                    【{{ scope.row.originalCommodity.resourceVo.schemeTypeVo == 1 ? '选课规则' : '自主选课' }}】{{
                      scope.row.originalCommodity.skuValueNameProperty.year.skuPropertyName
                    }}{{ scope.row.originalCommodity.resourceVo.schemeName }}
                  </p>
                </el-popover>
              </div> -->
              <!-- <div v-else> -->
              <div>
                <div class="f-to-two">
                  <div class="f-to-two">
                    【{{ scope.row.originalCommodity.resourceVo.schemeTypeVo == 1 ? '选课规则' : '自主选课' }}】{{
                      scope.row.originalCommodity.resourceVo.schemeName
                    }}
                  </div>
                  <p>
                    <el-tag type="danger" size="mini" v-if="scope.row.exchangeOrderNo === startOrder">初始物品</el-tag>
                  </p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="单价(元)" min-width="120" align="right">
            <template slot-scope="scope">{{ scope.row.price || 0 }}</template>
          </el-table-column>
          <el-table-column label="新班级（换班后）" min-width="300" fixed="left">
            <template slot-scope="scope">
              <!-- <div v-if="scope.row.exchangeCommodity.resourceVo.schemeName.length > 20">
                <el-popover placement="top-start" width="400" trigger="hover">
                  <div slot="reference">
                    <div class="f-to-two">
                      【{{ scope.row.exchangeCommodity.resourceVo.schemeTypeVo == 1 ? '选课规则' : '自主选课' }}】{{
                        scope.row.exchangeCommodity.skuValueNameProperty.year.skuPropertyName
                      }}{{ scope.row.exchangeCommodity.resourceVo.schemeName }}
                    </div>
                    <p>
                      <el-tag type="danger" size="mini" v-if="scope.row.exchangeCommodity.exchangeGoodsStatus === 3"
                        >最新物品</el-tag
                      >
                    </p>
                  </div>
                  <p>
                    【{{ scope.row.exchangeCommodity.resourceVo.schemeTypeVo == 1 ? '选课规则' : '自主选课' }}】{{
                      scope.row.exchangeCommodity.skuValueNameProperty.year.skuPropertyName
                    }}{{ scope.row.exchangeCommodity.resourceVo.schemeName }}
                  </p>
                </el-popover>
              </div> -->
              <!-- <div v-else> -->

              <div>
                <div class="f-to-two">
                  【{{ scope.row.exchangeCommodity.resourceVo.schemeTypeVo == 1 ? '选课规则' : '自主选课' }}】{{
                    scope.row.exchangeCommodity.resourceVo.schemeName
                  }}
                </div>
                <p>
                  <el-tag type="danger" size="mini" v-if="scope.row.exchangeOrderNo === lastOrder">最新物品</el-tag>
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="换班状态" min-width="120" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.exchangeTrainClassStatus === 3">
                <el-tag type="success">换班成功</el-tag>
                <p class="f-mt5">
                  <a
                    @click="clickInformationDetail(scope.row.exchangeOrderStatusList)"
                    class="f-link f-underline f-cb f-f12"
                    >查看详情</a
                  >
                </p>
              </div>

              <div v-else-if="scope.row.exchangeTrainClassStatus === 2">
                <el-tag type="warning">换班中</el-tag>
                <p class="f-mt5">
                  <a
                    @click="clickInformationDetail(scope.row.exchangeOrderStatusList)"
                    class="f-link f-underline f-cb f-f12"
                    >查看详情</a
                  >
                </p>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="换班操作人" min-width="110">
            <template slot-scope="scope">{{ scope.row.operatorName || '-' }}</template>
          </el-table-column>
        </el-table>
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </div>
    </el-drawer>
    <el-drawer title="查看详情" :visible.sync="informationDetailDialog" size="600px" custom-class="m-drawer">
      <div class="drawer-bd f-mt20 f-mlr40">
        <el-timeline>
          <el-timeline-item v-for="(item, index) in currentItem" :key="index">
            <p class="f-mb10 f-fb f-f15">
              {{ item.date }} <span class="f-ml30">{{ ExchangeOrderStatusEnum[item.status] }}</span>
            </p>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-drawer>
    <associated-shift :exchange-order-lis="[]" ref="AssociatedShift"></associated-shift>
    <change-shifts-detail
      ref="ChangeShiftsDetail"
      :changeShiftsDetailList="changeShiftsDetailList"
    ></change-shifts-detail>
    <refund-dialog
      :refundDetail="refundDetail"
      :refundReasonList="refundReasonList"
      :invoiceStatus="invoiceStatus"
      :isForceType="isForceType"
      @refundClick="refundClick"
      ref="RefundDialog"
    ></refund-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import QueryBatchOrderMainOrderDetail from '@api/service/management/trade/batch/order/query/QueryBatchOrderMainOrderDetail'
  import BatchOrderMainOrderDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderDetailVo'
  import { BatchOrderInvoiceTypeEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderInvoiceType'
  import { BatchOrderDeliveryWayEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderDeliveryWay'
  import { BatchOrderPayModeEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderPayMode'
  import { BatchOrderSubOrderAfterSaleStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderSubOrderAfterSaleStatus'
  import { BatchOrderSubOrderStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderSubOrderStatus'
  import { ExchangeTrainClassStatusEnum } from '@api/service/management/train-class/query/enum/ExchangeTrainClassStatusType'
  import ExchangeOrderRecordDetailVo from '@api/service/management/train-class/query/vo/ExchangeOrderRecordDetailVo'
  import { ExchangeOrderStatusEnum } from '@api/service/management/train-class/query/enum/ExchangeOrderStatusType'
  import ExchangeOrderStatus from '@api/service/management/train-class/query/vo/ExchangeOrderStatus'
  import BatchOrderMainOrderSubOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderMainOrderSubOrderListDetailVo'
  import RefundDialog from '@hbfe/jxjy-admin-trade/src/order/collective/components/refund-dialog.vue'
  import { RefundReason } from '@api/service/management/trade/batch/order/query/QueryRefundCauseList'
  import QueryBatchRefundList from '@api/service/management/trade/batch/order/query/QueryBatchRefund'
  import SellerApplyBatchOrderReturnRequestVo from '@api/service/management/trade/batch/order/mutation/vo/SellerApplyBatchOrderReturnRequestVo'
  import { UiPage } from '@hbfe/common'
  import { BatchOrderMainOrderStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderMainOrderStatus'
  import { BatchReturnSubOrderInfo } from '@api/ms-gateway/ms-order-v1'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import AssociatedShift from '@hbfe/jxjy-admin-trade/src/order/personal/components/associated-shift.vue'
  import ChangeShiftsDetail from '@hbfe/jxjy-admin-trade/src/order/personal/components/change-shifts-detail.vue'
  import { ExchangeOrderRecordVo } from '@api/service/management/trade/single/order/query/vo/ExchangeOrderRecordVo'
  import QueryExchangeDetailAccordingOrder from '@api/service/management/trade/single/order/query/QueryExchangeDetailAccordingOrder'

  @Component({
    components: {
      RefundDialog,
      AssociatedShift,
      ChangeShiftsDetail
    }
  })
  export default class extends Vue {
    @Ref('RefundDialog') RefundDialog: RefundDialog
    @Ref('elTableRef') elTableRef: any
    @Ref('AssociatedShift') AssociatedShift: AssociatedShift
    @Ref('ChangeShiftsDetail') ChangeShiftsDetail: ChangeShiftsDetail

    refundReasonList: Array<RefundReason> = new Array<RefundReason>()
    id = ''
    subId = ''
    // 页面分页控件
    page: UiPage
    //关联换班信息标识
    associatedInformationDialog = false
    //查看信息详情标识
    informationDetailDialog = false
    //业务接口请求
    queryBatchOrderMainOrderDetail: QueryBatchOrderMainOrderDetail =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchOrderMainOrderDetail
    //集体报名退款查询请求
    queryBatchRefound: QueryBatchRefundList =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchRefound
    //基础数据
    batchOrderMainOrderDetailVo = new BatchOrderMainOrderDetailVo()
    exchangeOrderRecordDetailVo: Array<ExchangeOrderRecordDetailVo> = new Array<ExchangeOrderRecordDetailVo>()
    currentItem: Array<ExchangeOrderStatus> = new Array<ExchangeOrderStatus>()
    refundDetail = new BatchOrderMainOrderSubOrderListDetailVo() // 退款弹窗详情
    isForceType: Array<number> = new Array<number>()
    invoiceStatus = [] as number[] //详情页发票状态
    changeShiftsDetailList = new Array<ExchangeOrderRecordVo>() // 换班详情列表
    // 换货单详情根据子订单号查询实例
    queryExchangeDetailByOrderObj = new QueryExchangeDetailAccordingOrder()
    sellerApplyBatchOrderReturnRequestVo: SellerApplyBatchOrderReturnRequestVo =
      new SellerApplyBatchOrderReturnRequestVo()
    associatedId = ''
    // 初始单
    startOrder = ''
    // 最新单
    lastOrder = ''

    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable
    // 是否开启过专题能力服务
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    /**
     * 退款子单集合
     */
    returnSubOrders: BatchReturnSubOrderInfo[] = []
    BatchOrderInvoiceTypeEnumStatus = {
      [BatchOrderInvoiceTypeEnum.VAT_ELECTRONIC_INVOICE]: '增值税电子普通发票（自动开票）',
      [BatchOrderInvoiceTypeEnum.VAT_OFFLINE_INVOICE]: '增值税电子普通发票（线下开票）',
      [BatchOrderInvoiceTypeEnum.VAT_SPECIAL_INVOICE]: '增值税专用发票'
    }
    BatchOrderDeliveryWayEnumStatus = {
      [BatchOrderDeliveryWayEnum.Courier]: '邮寄',
      [BatchOrderDeliveryWayEnum.Self_Fetched]: '自取'
    }
    BatchOrderPayModeEnumMode = {
      [BatchOrderPayModeEnum.Online_Pay]: '线上支付',
      [BatchOrderPayModeEnum.Offline_Pay]: '线下支付'
    }
    BatchOrderSubOrderAfterSaleStatusEnum = {
      [BatchOrderSubOrderAfterSaleStatusEnum.Wait_For_Refund]: '未发起退款',
      [BatchOrderSubOrderAfterSaleStatusEnum.Refund_Approval_In_Process]: '退款审批中',
      [BatchOrderSubOrderAfterSaleStatusEnum.Refund_In_Process]: '退款处理中',
      [BatchOrderSubOrderAfterSaleStatusEnum.Refund_Success]: '退款成功',
      [BatchOrderSubOrderAfterSaleStatusEnum.Refund_Fail]: '退款失败',
      [BatchOrderSubOrderAfterSaleStatusEnum.Cancel_Refund]: '取消退款'
    }
    BatchOrderSubOrderStatusEnum = {
      [BatchOrderSubOrderStatusEnum.Wait_Pay]: '待付款',
      [BatchOrderSubOrderStatusEnum.Paying]: '支付中',
      [BatchOrderSubOrderStatusEnum.Wait_Delivery]: '待发货',
      [BatchOrderSubOrderStatusEnum.Delivering]: '发货中',
      [BatchOrderSubOrderStatusEnum.Complete_Delivery]: '已发货',
      [BatchOrderSubOrderStatusEnum.Canceled]: '已取消'
    }
    ExchangeTrainClassStatusEnum = {
      [ExchangeTrainClassStatusEnum.ALL]: '全部',
      [ExchangeTrainClassStatusEnum.Exchanging]: '换班中',
      [ExchangeTrainClassStatusEnum.Complete_Exchange]: '换班成功'
    }

    ExchangeOrderStatusEnum = {
      [ExchangeOrderStatusEnum.Apply_Exchange]: '发起换班',
      [ExchangeOrderStatusEnum.Off_Shift_Processing]: '退班处理中',
      [ExchangeOrderStatusEnum.Off_Shift_Fail]: '退班失败',
      [ExchangeOrderStatusEnum.Apply_Delivery]: '申请发货',
      [ExchangeOrderStatusEnum.Delivery_Processing]: '发货处理中',
      [ExchangeOrderStatusEnum.Complete_Exchange]: '换班成功'
    }
    BatchOrderMainOrderStatusEnum = {
      [BatchOrderMainOrderStatusEnum.Wait_Pay]: '等待付款',
      [BatchOrderMainOrderStatusEnum.Paying]: '支付中',
      [BatchOrderMainOrderStatusEnum.Opening]: '开通中',
      [BatchOrderMainOrderStatusEnum.Complete_Trade]: '交易成功',
      [BatchOrderMainOrderStatusEnum.Close_Trade]: '交易关闭'
    }
    constructor() {
      super()
      this.page = new UiPage(this.clickAssociatedInformation, this.clickAssociatedInformation)
    }

    /**
     * 获取培训方案类型
     */
    getSchemeType(row: BatchOrderMainOrderSubOrderListDetailVo) {
      const schemeType = SchemeType.getSchemeType(row.schemeType, true)
      if (row.skuValueNameProperty.trainingMode?.skuPropertyName) {
        return `${row.skuValueNameProperty.trainingMode.skuPropertyName}-${schemeType}`
      } else {
        return schemeType
      }
    }

    async activated() {
      this.init()
      await this.getBatchOrder()
      // await this.$nextTick(async () => {
      //   const element = this.elTableRef.bodyWrapper
      //   element.addEventListener('scroll', this.infiniteScroll)
      // })
    }

    /**
     *  滚动加载
     */
    // @bind
    // @debounce(200)
    // async infiniteScroll() {
    //   const element = this.elTableRef.bodyWrapper
    //   const scrollDistance = element.scrollHeight - element.scrollTop - element.clientHeight
    //   // console.log('scrollInfo', scrollDistance, element.scrollHeight, element.scrollTop, element.clientHeight)
    //   if (scrollDistance <= 0) {
    //     if (this.batchOrderMainOrderDetailVo.subOrderList.length >= this.page.totalSize) {
    //       this.$message.warning('没有更多数据')
    //     } else {
    //       this.page.pageNo++
    //       await this.getBatchOrder()
    //     }
    //   }
    // }
    init() {
      this.id = this.$route.params?.id
      this.subId = this.$route.params?.subId
    }
    async getBatchOrder() {
      try {
        if (this.isFxlogin && this.isHadFxAbility) {
          this.batchOrderMainOrderDetailVo = await this.queryBatchOrderMainOrderDetail.queryFxBatchOrderMainOrderDetail(
            this.id,
            this.subId
          )
        } else {
          this.batchOrderMainOrderDetailVo = await this.queryBatchOrderMainOrderDetail.queryBatchOrderMainOrderDetail(
            this.id,
            this.subId
          )
        }
      } catch (e) {
        console.log(e, '加载集体报名退款订单详情页失败')
      } finally {
        //获取开票状态
        this.batchOrderMainOrderDetailVo.invoiceInfoList.forEach((item) => {
          this.invoiceStatus.push(item.invoiceStatus)
        })
        console.log(this.batchOrderMainOrderDetailVo, 'batchOrderMainOrderDetailVo')
      }
    }
    async getAssociated(id: string) {
      this.associatedId = id
      await this.clickAssociatedInformation()
    }

    async clickAssociatedInformation() {
      this.associatedInformationDialog = true

      try {
        const { start, last } = await this.queryBatchOrderMainOrderDetail.queryRelativeExchange(this.associatedId)
        this.startOrder = start
        this.lastOrder = last
        this.exchangeOrderRecordDetailVo = await this.queryBatchOrderMainOrderDetail.queryRelativeExchangeRecord(
          this.associatedId,
          this.page
        )
        console.log(this.exchangeOrderRecordDetailVo, 'exchangeOrderRecordDetailVo')
      } catch (e) {
        console.log(e, '加载关联换班信息失败')
      }
    }
    async clickInformationDetail(current: Array<ExchangeOrderStatus>) {
      this.currentItem = current
      // this.associatedInformationDialog = false
      this.informationDetailDialog = true
    }

    isShowAssociatedShift(id: string) {
      this.AssociatedShift.isShowDialog(id)
    }

    /**
     * 发起退款
     */
    async clickRefund(item: BatchOrderMainOrderSubOrderListDetailVo) {
      if (item.afterSale === 1 || item.afterSale === 5 || item.afterSale === 6) {
        this.returnSubOrders = [] as BatchReturnSubOrderInfo[]
        const itm = new BatchReturnSubOrderInfo()
        itm.orderNo = this.subId
        itm.subOrderNo = item.subOrderNo
        this.returnSubOrders.push(itm)
        this.refundDetail = item

        this.isForceType = await this.queryBatchRefound.queryIsForce(this.id, item.subOrderNo)
        await this.getQueryRefundCauseList()
        this.RefundDialog.isShowDialog()
      } else {
        return
      }
    }
    async clickToDetail(item: BatchOrderMainOrderSubOrderListDetailVo) {
      this.$router.push(
        '/training/trade/refund/collective/sub-detail/' + item.batchRefundOrderNo + '/' + item.refundOrderNo
      )
    }
    /**
     * 获取退款原因
     */
    async getQueryRefundCauseList() {
      //refundReasonList  返回结果
      this.refundReasonList =
        await TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryRefundCauseList.queryBatchRefundReasonList()
    }
    /**
     * 确认退款
     */
    async refundClick(item: any) {
      this.sellerApplyBatchOrderReturnRequestVo.batchOrderNo = this.id
      this.sellerApplyBatchOrderReturnRequestVo.reasonId = item.reason
      this.sellerApplyBatchOrderReturnRequestVo.description = item.explain
      this.sellerApplyBatchOrderReturnRequestVo.batchReturnOrderType = 3
      this.sellerApplyBatchOrderReturnRequestVo.needManualApprove = true
      this.sellerApplyBatchOrderReturnRequestVo.returnSubOrders = this.returnSubOrders
      const res =
        await TradeModule.batchTradeBatchFactor.orderFactor.mutationOrderFactor.mutationBatchOrderRefund.applyBatchRefund(
          this.sellerApplyBatchOrderReturnRequestVo
        )

      if (res?.data?.code === '200') {
        this.$message.success('退款申请提交成功')
        await this.getBatchOrder()
      } else {
        this.$message.error(res?.data?.message || res?.status?.errors?.[0]?.message || '确认退款失败')
      }
    }
    get isShipments() {
      return (item: BatchOrderSubOrderStatusEnum) => {
        return item === BatchOrderSubOrderStatusEnum.Complete_Delivery
      }
    }

    isShowChangeShiftsDetailDialog(id: string) {
      this.queryExchangeDetail(id)
    }

    // 换班详情查询
    async queryExchangeDetail(subOrderNo: string) {
      this.queryExchangeDetailByOrderObj.subOrderNo = subOrderNo
      let res
      if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
        res = await this.queryExchangeDetailByOrderObj.queryRefundOrderDetailInDistributor()
      } else {
        res = await this.queryExchangeDetailByOrderObj.queryRefundOrderDetail()
      }
      if (res.isSuccess()) {
        this.changeShiftsDetailList = this.queryExchangeDetailByOrderObj.exchangeOrderDetail?.records
        this.ChangeShiftsDetail.isShowDialog()
      } else {
        this.$message.warning('请求失败')
      }
    }

    getRegionName(nameList: string) {
      return '【' + nameList?.split('/').join('>') + '】'
    }
  }
</script>

<style scoped></style>
