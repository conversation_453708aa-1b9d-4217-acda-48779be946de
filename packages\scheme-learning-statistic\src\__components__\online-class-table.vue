<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div class="f-p15">
      <!--操作栏-->
      <el-alert type="warning" :closable="false" class="m-alert f-clear">
        <div class="f-fr f-csp f-flex f-align-center" @click="statisticDialog = true">
          <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
        </div>
      </el-alert>
      <!--表格-->
      <el-table
        stripe
        :data="schemeLearningStatisticsResponseVo"
        border
        show-summary
        :summary-method="getSummaries"
        max-height="500px"
        class="m-table is-statistical f-mt10"
        ref="schemeTable"
        v-loading="loading"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="培训方案" min-width="200" fixed="left">
          <!-- <template>【培训班-选课规则】培训方案名称</template> -->
          <template slot-scope="scope">
            <el-tag type="primary" effect="dark" size="mini">{{
              getSchemeType(scope.row.trainClassDetail.schemeType)
            }}</el-tag>
            {{ scope.row.trainClassDetail.commodityBasicData.saleTitle || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="方案属性" min-width="200" align="center">
          <template slot-scope="scope">
            <!-- <p>行业：行业行业</p>
                <p>地区：为空，不展示</p>
                <p>科目类型：科目类型</p>
                <p>培训类别：培训类别</p>
                <p>培训专业：培训专业专业</p>
                <p>培训年度：2019</p> -->
            <p>
              行业：{{
                scope.row.trainClassDetail.skuValueNameProperty.industry.skuPropertyName
                  ? scope.row.trainClassDetail.skuValueNameProperty.industry.skuPropertyName
                  : '-'
              }}
            </p>
            <p>
              培训年度：{{
                scope.row.trainClassDetail.skuValueNameProperty.year.skuPropertyName
                  ? scope.row.trainClassDetail.skuValueNameProperty.year.skuPropertyName
                  : '-'
              }}
            </p>
            <p>
              地区：{{
                scope.row.trainClassDetail.skuValueNameProperty.region.skuPropertyName
                  ? scope.row.trainClassDetail.skuValueNameProperty.region.skuPropertyName
                  : '-'
              }}
            </p>
            <p v-if="scope.row.trainClassDetail.skuValueNameProperty.subjectType.skuPropertyName">
              科目类型：{{
                scope.row.trainClassDetail.skuValueNameProperty.subjectType.skuPropertyName
                  ? scope.row.trainClassDetail.skuValueNameProperty.subjectType.skuPropertyName
                  : '-'
              }}
            </p>
            <p v-if="scope.row.trainClassDetail.skuValueNameProperty.trainingCategory.skuPropertyName">
              培训类型：{{ scope.row.trainClassDetail.skuValueNameProperty.trainingCategory.skuPropertyName }}
            </p>
            <p v-if="scope.row.trainClassDetail.skuValueNameProperty.trainingMajor.skuPropertyName">
              培训专业：{{ scope.row.trainClassDetail.skuValueNameProperty.trainingMajor.skuPropertyName }}
            </p>
            <p v-if="scope.row.trainClassDetail.skuProperty.positionCategory">
              岗位类别：{{
                scope.row.trainClassDetail.skuProperty.positionCategory
                  ? scope.row.trainClassDetail.skuProperty.positionCategory.skuPropertyValueName
                  : '-'
              }}
            </p>
            <template v-if="scope.row.trainClassDetail.skuValueNameProperty.industry.skuPropertyName == '职业卫生行业'">
              <p>
                培训对象：{{
                  scope.row.trainClassDetail.skuProperty.trainingObject
                    ? scope.row.trainClassDetail.skuProperty.trainingObject.skuPropertyValueName ||
                      scope.row.trainClassDetail.skuValueNameProperty.trainingObject.skuPropertyName
                    : '-'
                }}
              </p>
            </template>
            <template v-if="scope.row.trainClassDetail.skuValueNameProperty.industry.skuPropertyName == '工勤行业'">
              <p>
                技术等级：{{
                  scope.row.trainClassDetail.skuProperty.jobLevel
                    ? scope.row.trainClassDetail.skuProperty.jobLevel.skuPropertyValueName
                    : '-'
                }}
              </p>
              <!-- <p>
                  工种：{{
                    scope.row.trainClassDetail.skuProperty.jobCategory
                      ? scope.row.trainClassDetail.skuProperty.jobLevel.jobCategory
                      : '-'
                  }}
                </p> -->
            </template>
            <!-- 教师行业 -->
            <template
              v-if="
                scope.row.trainClassDetail.skuValueNameProperty.industry &&
                scope.row.trainClassDetail.skuValueNameProperty.industry.skuPropertyName == '教师行业'
              "
            >
              <!-- 学科学段 -->
              <p v-if="scope.row.trainClassDetail.skuProperty.learningPhase">
                学段：{{
                  scope.row.trainClassDetail.skuProperty.learningPhase &&
                  scope.row.trainClassDetail.skuProperty.learningPhase.skuPropertyValueName
                    ? scope.row.trainClassDetail.skuProperty.learningPhase.skuPropertyValueName
                    : '-'
                }}
              </p>
              <p v-if="scope.row.trainClassDetail.skuProperty.discipline">
                学科：{{
                  scope.row.trainClassDetail.skuProperty.discipline &&
                  scope.row.trainClassDetail.skuProperty.discipline.skuPropertyValueName
                    ? scope.row.trainClassDetail.skuProperty.discipline.skuPropertyValueName
                    : '-'
                }}
              </p>
            </template>
            <template
              v-if="scope.row.trainClassDetail.skuValueNameProperty.industry.skuPropertyName.indexOf('药师行业') > -1"
            >
              <p
                v-if="
                  scope.row.trainClassDetail.skuProperty.certificatesType &&
                  scope.row.trainClassDetail.skuProperty.practitionerCategory
                "
              >
                执业类别：{{
                  scope.row.trainClassDetail.skuProperty.certificatesType
                    ? scope.row.trainClassDetail.skuProperty.certificatesType.skuPropertyValueName
                    : '-'
                }}/{{
                  scope.row.trainClassDetail.skuProperty.practitionerCategory
                    ? scope.row.trainClassDetail.skuProperty.practitionerCategory.skuPropertyValueName
                    : '-'
                }}
              </p>
            </template>
          </template>
        </el-table-column>
        <el-table-column label="净报名" prop="netRegisterCount" min-width="90" align="center">
          <template slot-scope="scope">{{ scope.row.learningStatistic.netRegisterCount || 0 }}</template>
        </el-table-column>
        <el-table-column label="线上课程" header-align="center">
          <el-table-column label="未学习" prop="courseLearningStatistic.waitStudyCount" min-width="90" align="center">
            <template slot-scope="scope">{{
              scope.row.learningStatistic.courseLearningStatistic.waitStudyCount || 0
            }}</template>
          </el-table-column>
          <el-table-column label="学习中" prop="courseLearningStatistic.studyingCount" min-width="90" align="center">
            <template slot-scope="scope">{{
              scope.row.learningStatistic.courseLearningStatistic.studyingCount || 0
            }}</template>
          </el-table-column>
          <el-table-column label="已学完" prop="courseLearningStatistic.studyFinishCount" min-width="90" align="center">
            <template slot-scope="scope">{{
              scope.row.learningStatistic.courseLearningStatistic.studyFinishCount || 0
            }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="班级考试" align="center">
          <el-table-column label="已考试" prop="examLearningStatistic.committedExamCount" min-width="90" align="center">
            <template slot-scope="scope">{{
              scope.row.learningStatistic.examLearningStatistic.committedExamCount || 0
            }}</template>
          </el-table-column>
          <el-table-column
            label="未考试"
            prop="examLearningStatistic.unCommittedExamCount"
            min-width="90"
            align="center"
          >
            <template slot-scope="scope">{{
              scope.row.learningStatistic.examLearningStatistic.unCommittedExamCount || 0
            }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="调研问卷" align="center">
          <el-table-column label="已提交" prop="questionnaireSubmitCount" min-width="90" align="center">
            <template slot-scope="scope">{{ scope.row.learningStatistic.questionnaireSubmitCount || 0 }}</template>
          </el-table-column>
          <el-table-column label="未提交" prop="questionnaireUnSubmitCount" min-width="90" align="center">
            <template slot-scope="scope">{{ scope.row.learningStatistic.questionnaireUnSubmitCount || 0 }}</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="已合格" prop="qualifiedCount" min-width="90" align="center">
          <template slot-scope="scope">{{ scope.row.learningStatistic.qualifiedCount || 0 }}</template>
        </el-table-column>
        <el-table-column label="合格率" prop="qualifiedRate" min-width="90" align="center">
          <template slot-scope="scope">{{
            scope.row.learningStatistic.netRegisterCount == 0 ||
            scope.row.learningStatistic.netRegisterCount == undefined
              ? '0.00%'
              : (
                  ((scope.row.learningStatistic.qualifiedCount || 0) /
                    (scope.row.learningStatistic.netRegisterCount || 0)) *
                  100
                ).toFixed(2) + '%'
          }}</template>
        </el-table-column>
        <el-table-column label="操作" width="120" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="toCollectDetails(scope.row)">学员学习明细</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination class="f-mt15 f-tr" :page="trainSchemePage" v-bind="trainSchemePage"> </hb-pagination>
    </div>
    <el-drawer title="统计口径说明" :visible.sync="statisticDialog" size="900px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
        </el-alert>
        <p class="f-mt20 f-mb10">
          <span class="f-fb f-f15">搜索条件说明</span>
          （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
        </p>
        <el-table stripe :data="tableData4" border class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="字段/操作" width="150">
            <template slot-scope="scope">
              <div>{{ fieldLabels[scope.$index] }}</div>
            </template>
          </el-table-column>
          <el-table-column label="说明" min-width="300">
            <template slot-scope="scope">
              <div>{{ fieldDescriptions[scope.$index] }}</div>
            </template>
          </el-table-column>
        </el-table>
        <p class="f-mt20 f-mb10">
          <span class="f-fb f-f15">列表字段及详细说明</span>
          （列表下的数据显示受搜索条件的约束，统计单位：人次）
        </p>
        <el-table stripe :data="tableData9" border class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="字段/操作" width="150">
            <template slot-scope="scope">
              <div>{{ fieldLabels2[scope.$index] }}</div>
            </template>
          </el-table-column>
          <el-table-column label="说明" min-width="300">
            <template slot-scope="scope">
              <div>{{ fieldDescriptions2[scope.$index] }}</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
  </el-card>
</template>

<script lang="ts">
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import { QuerySchemeLearningList } from '@api/service/management/statisticalReport/query/QuerySchemeLearningList'
  import { CommoditySkuRequestVo } from '@api/service/management/statisticalReport/query/vo/CommoditySkuRequestVo'
  import { SchemeLearningStatisticsResponseVo } from '@api/service/management/statisticalReport/query/vo/SchemeLearningStatisticsResponseVo'
  import { Query, UiPage } from '@hbfe/common'
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import QueryShowOffline from '@api/service/common/config/QueryShowOffline'
  @Component
  export default class extends Vue {
    @Prop({
      required: true,
      default: () => {
        return new Array<SchemeLearningStatisticsResponseVo>()
      }
    })
    schemeLearningStatisticsResponseVo: Array<SchemeLearningStatisticsResponseVo>
    @Prop({
      required: true,
      default: () => {
        return new UiPage()
      }
    })
    trainSchemePage: UiPage
    @Prop({
      required: true,
      default: () => {
        return new CommoditySkuRequestVo()
      }
    })
    CommoditySkuRequestVo: CommoditySkuRequestVo
    @Prop({
      required: true,
      default: () => {
        return new QuerySchemeLearningList()
      }
    })
    getQuerySchemeLearningList: QuerySchemeLearningList
    @Prop({
      required: true,
      default: () => {
        return false
      }
    })
    loading: boolean
    statisticDialog = false

    fieldLabels = ['年度', '培训类别', '培训专业', '科目类型', '培训方案名称', '报名时间']

    fieldDescriptions = [
      '按培训方案年度查询数据',
      '创建培训方案时定义的培训类别，按培训类别查询数据',
      '选择具体培训类别下的某一专业查询数据',
      '职称申报人员可按科目类型查询数据',
      '按培训方案名称搜索，支持模糊查询',
      '查看在某个开通时间内，培训方案开通数据'
    ]
    fieldLabels2 = [
      '方案名称',
      '方案属性',
      '净报名',
      '未学习',
      '学习中',
      '已学完',
      '已考试',
      '未考试',
      '培训期别已合格',
      '培训期别未合格',
      '调研问卷已提交',
      '调研问卷未提交',
      '已合格',
      '合格率'
    ]

    fieldDescriptions2 = [
      '培训方案名称，默认显示全部已发布的方案',
      '培训方案的属性值展示',
      '统计截止到当前时间扣除退班后的实际有效的报名人次，净报名=未学习+学习中+已学完',
      '统计截止到当前时间净报名人数中，班级课程学习获得学时=0',
      '统计截止到当前时间净报名人数中，0<课程学习已获得的学时<课程学习要求学时，方案无配置课程学习显示：无需学习',
      '统计截止到当前时间净报名人数中，课程学习已获得要求学时',
      '统计截止到当前时间净报名人数中，已参加过考试的人（有提交试卷成功就计一次考试）',
      '统计截止到当前时间净报名人数中，未参加考试的人（有提交试卷成功就计一次考试）',
      '统计截止到当前时间净报名人数中，已满足培训期别考核的人',
      '统计截止到当前时间净报名人数中，不满足培训期别考核的人',
      '统计截止到当前时间净报名人数中，已提交所有要求填写（纳入考核）的问卷的人数。 仅统计班级维度的问卷',
      '统计截止到当前时间净报名人数中，存在未提交要求填写（纳入考核）的问卷的人数。 仅统计班级维度的问卷',
      '统计截止到当前时间净报名人数中，已达到培训考核要求的人',
      '统计截止到当前时间净报名人数中的合格率，合格率=已合格/净报名'
    ]
    showOffline = !QueryShowOffline.getShowOfflineApolloConfig()

    get tableData9() {
      return new Array(this.fieldDescriptions2.length).fill(0)
    }
    get tableData4() {
      return new Array(this.fieldLabels.length).fill(0)
    }

    mounted() {
      this.$emit('getChildRef', this.$refs['schemeTable'] as any, 'online')
      this.fieldDescriptions2 = this.clearOffline(this.fieldDescriptions2)
      this.fieldLabels2 = this.clearOffline(this.fieldLabels2)
    }
    clearOffline(list: Array<string>) {
      let temp = list
      if (!this.showOffline) {
        temp = list.filter((description) => !description.includes('培训期别'))
      }
      return temp
    }
    toCollectDetails(item: SchemeLearningStatisticsResponseVo) {
      let res
      if (this.CommoditySkuRequestVo.skuPropertyRequest.regionSkuPropertySearch?.region) {
        res =
          '/' +
          Object.values(this.CommoditySkuRequestVo.skuPropertyRequest.regionSkuPropertySearch?.region[0])
            .filter((region) => {
              return region
            })
            ?.join('/')
      }
      //console.log(res)
      this.$router.push({
        path: '/statistic/learning-statistic',
        query: {
          schemeName: item.trainClassDetail.commodityBasicData.saleTitle,
          schemeId: item.trainClassDetail.schemeId,
          regionType: res || '',
          year: JSON.stringify(this.CommoditySkuRequestVo.skuPropertyRequest.year) || '',
          registerTimeBegin: this.CommoditySkuRequestVo.registerTime.begin,
          registerTimeEnd: this.CommoditySkuRequestVo.registerTime.end,
          subjectName: this.CommoditySkuRequestVo.trainingChannelName,
          subjectType: JSON.stringify(this.CommoditySkuRequestVo.saleChannel),
          trainingType: 'trainingWay0001'
        }
      })
    }

    /**
     *  合计数据
     *  垃圾合计数据  不看也罢！！！ - -
     */
    getSummaries(param: any) {
      // //console.log(this.getQuerySchemeLearningList.statisticsM, 'statisticsM')
      const { columns, data } = param
      const sums = [] as any[]
      columns.forEach((column: any, index: number) => {
        // 可以根据对象属性名判断是否合计querySelectorAll
        // if (column.property === "amount3") {
        // 根据索引修改‘合计’文案
        if (index === 0) {
          sums[index] = '合计'
          return
          // 可以根据索引判断是否合计
        }
        switch (column.property) {
          case 'examLearningStatistic.unCommittedExamCount':
            sums[index] = this.getQuerySchemeLearningList.statisticsM.examLearningStatistic.unCommittedExamCount || 0
            break
          case 'questionnaireSubmitCount':
            sums[index] = this.getQuerySchemeLearningList.statisticsM.questionnaireSubmitCount || 0
            break
          case 'questionnaireUnSubmitCount':
            sums[index] = this.getQuerySchemeLearningList.statisticsM.questionnaireUnSubmitCount || 0
            break
          case 'netRegisterCount': //净报名人次
            sums[index] = this.getQuerySchemeLearningList.statisticsM.netRegisterCount
            break
          case 'qualifiedCount': //考核通过人次
            sums[index] = this.getQuerySchemeLearningList.statisticsM.qualifiedCount
            break
          case 'courseLearningStatistic.waitStudyCount':
            sums[index] = this.getQuerySchemeLearningList.statisticsM.courseLearningStatistic.waitStudyCount
            break
          case 'courseLearningStatistic.studyingCount':
            sums[index] = this.getQuerySchemeLearningList.statisticsM.courseLearningStatistic.studyingCount
            break
          case 'courseLearningStatistic.studyFinishCount':
            sums[index] = this.getQuerySchemeLearningList.statisticsM.courseLearningStatistic.studyFinishCount
            break
          case 'examLearningStatistic.committedExamCount': //已参与考试人次
            sums[index] = this.getQuerySchemeLearningList.statisticsM.examLearningStatistic.committedExamCount
            break
          case 'qualifiedRate':
            sums[index] =
              (
                (this.getQuerySchemeLearningList.statisticsM.qualifiedCount /
                  (this.getQuerySchemeLearningList.statisticsM.netRegisterCount || 1)) *
                100
              ).toFixed(2) + '%'
            break
        }
      })
      return sums
    }
    /**
     * 获取培训方案类型
     */
    getSchemeType(type: string) {
      return SchemeType.getSchemeType(type, true)
    }
  }
</script>
