import BasicDataQueryForestage, {
  BusinessRegionResponse,
  RegionResponse,
  TrainingPropertyResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import {
  SchemeSkuPropertyResponse,
  SkuPropertyResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import {
  CommoditySkuPropertyResponse,
  PortalCommoditySkuPropertyListResponse,
  PortalCommoditySkuPropertyResponse,
  SkuPropertyListResponse,
  SkuPropertyResponse as TradeSkuPropertyResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
import QueryBasicdataDictionaryFactory from '@api/service/common/basic-data-dictionary/QueryBasicdataDictionaryFactory'
import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
import QueryGrade from '@api/service/common/basic-data-dictionary/query/QueryGrade'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import QueryPropertyDetail, {
  BatchQueryPropertyRequest
} from '@api/service/common/basic-data-dictionary/query/QueryPropertyDetail'
import { SubjectTypeRequest } from '@api/service/common/basic-data-dictionary/query/QuerySubjectType'
import { TechnologyLevelVo } from '@api/service/common/basic-data-dictionary/query/QueryTechnologyLevel'
import { TrainingCategoryRequest } from '@api/service/common/basic-data-dictionary/query/QueryTrainingCategory'
import { TrainingProfessionalRequest } from '@api/service/common/basic-data-dictionary/query/QueryTrainingMajor'
import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
import SubjectTypeVo from '@api/service/common/basic-data-dictionary/query/vo/SubjectTypeVo'
import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
import { RegionVo } from '@api/service/customer/train-class/query/vo/RegionVo'
import SkuPropertyResponseVo from '@api/service/customer/train-class/query/vo/SkuPropertyResponseVo'
import SkuPropertyVo from '@api/service/customer/train-class/query/vo/SkuPropertyVo'
import SkuVo from '@api/service/customer/train-class/query/vo/SkuVo'
// import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
import RegionTreeVo from '@api/service/common/basic-data-dictionary/query/vo/RegionTreeVo'
import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * 复杂sku培训属性返回值
 */
export type ComplexSkuPropertyResponse = CommoditySkuPropertyResponse &
  SchemeSkuPropertyResponse &
  PortalCommoditySkuPropertyResponse

/**
 * 复杂sku培训选项返回值
 */
export type ComplexSkuPropertyListResponse = SkuPropertyListResponse & PortalCommoditySkuPropertyListResponse

export class SchemeSkuInfo {
  /**
   * 培训方案id
   */
  id = ''
  /**
   * 门户商品id
   */
  portalCommodityId = ''
  /**
   * sku属性
   */
  sku: CommoditySkuPropertyResponse & SchemeSkuPropertyResponse & PortalCommoditySkuPropertyResponse = null
  /**
   * sku属性名称
   */
  skuName: SkuPropertyResponseVo = new SkuPropertyResponseVo()

  constructor(
    id: string,
    sku: (CommoditySkuPropertyResponse & SchemeSkuPropertyResponse) & PortalCommoditySkuPropertyResponse,
    portalCommodityId = ''
  ) {
    this.id = id
    this.sku = sku
    this.portalCommodityId = portalCommodityId
  }
}

/**
 * SkuPropertyResponse转换SkuPropertyVo
 */
class SkuPropertyConvertUtils {
  /**
   * 从SkuPropertyVo中找到对应的对象
   */
  static filterSku(
    skuProperty: CommoditySkuPropertyResponse | SchemeSkuPropertyResponse,
    skuProperties: SkuPropertyVo
  ): SkuPropertyResponseVo {
    const skuPro = new SkuPropertyResponseVo()
    if (skuProperty.year) {
      skuPro.year = skuProperties.year.find((item) => item.skuPropertyValueId == skuProperty.year.skuPropertyValueId)
    }
    if (skuProperty.subjectType) {
      skuPro.subjectType = skuProperties.subjectType.find(
        (item) => item.skuPropertyValueId == skuProperty.subjectType.skuPropertyValueId
      )
    }
    if (skuProperty.trainingCategory) {
      skuPro.trainingCategory = skuProperties.trainingCategory.find(
        (item) => item.skuPropertyValueId == skuProperty.trainingCategory.skuPropertyValueId
      )
    }
    if (skuProperty.trainingProfessional) {
      skuPro.trainingMajor = skuProperties.trainingMajor.find(
        (item) => item.skuPropertyValueId == skuProperty.trainingProfessional.skuPropertyValueId
      )
    }
    if (skuProperty.industry) {
      skuPro.industry = skuProperties.industry.find(
        (item) => item.skuPropertyValueId == skuProperty.industry.skuPropertyValueId
      )
    }

    let regionSkuId = '',
      regionSkuName = ''
    if (skuProperty.province.skuPropertyValueId) {
      const proVo = SkuPropertyConvertUtils.findRegion(skuProperties.RegionVos, skuProperty.province.skuPropertyValueId)
      regionSkuId = proVo.id
      regionSkuName = proVo.name
    }
    if (skuProperty.city.skuPropertyValueId) {
      const cityVo = SkuPropertyConvertUtils.findRegion(skuProperties.RegionVos, skuProperty.city.skuPropertyValueId)
      regionSkuId = regionSkuId + '/' + cityVo.id
      regionSkuName = regionSkuName + '/' + cityVo.name
    }
    if (skuProperty.county.skuPropertyValueId) {
      const countyVo = SkuPropertyConvertUtils.findRegion(
        skuProperties.RegionVos,
        skuProperty.county.skuPropertyValueId
      )
      regionSkuId = regionSkuId + '/' + countyVo.id
      regionSkuName = regionSkuName + '/' + countyVo.name
    }
    const skuV = new SkuVo()

    skuV.skuPropertyValueId = regionSkuId
    skuV.skuPropertyName = regionSkuName
    skuPro.region = skuV
    return skuPro
  }

  /**
   * 通用属性id获取属性名称
   * @param skuIds 属性id数组
   * @param industryId 行业id
   */
  static async calPropertyObject(skuIds: string[], industryId?: string) {
    const skuVos = new Array<SkuVo>()

    const trainingObjectList = await QueryPropertyDetail.getPropertyDetailByIds(industryId, skuIds)

    trainingObjectList?.length &&
      trainingObjectList.map((item) => {
        const sku = new SkuVo()

        sku.skuPropertyName = item.showName ? item.showName : item.name
        sku.parentId = item.parentId
        sku.skuPropertyValueId = item.propertyId
        skuVos.push(sku)
      })

    return skuVos
  }

  static findRegion(regionVos: RegionVo[], regionId: string): RegionVo {
    for (let i = 0; i < regionVos.length; i++) {
      const regionVo = regionVos[i]
      if (regionVo.id == regionId) {
        return regionVo
      }
      if (regionVo.child && regionVo.child.length) {
        return SkuPropertyConvertUtils.findRegion(regionVo.child, regionId)
      }
    }
    return new RegionVo()
  }
  /**
   ** SkuPropertyListResponse转换SkuPropertyVo
   */
  async convertToSkuPropertyVo(
    source: ComplexSkuPropertyListResponse,
    currentIndustryId?: string,
    isWeb?: boolean
  ): Promise<SkuPropertyVo> {
    const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
    const skuVo = new SkuPropertyVo()
    const portalSource = source as PortalCommoditySkuPropertyListResponse
    let SkuIds = []
    //年度
    if (source.year.length) {
      SkuIds = source.year.map((item) => item.skuPropertyValueId)
      skuVo.year = await SkuPropertyConvertUtils.calYear(SkuIds, factory)
    }
    //学时
    if (portalSource.periodForPortal && portalSource.periodForPortal.length) {
      if (currentIndustryId) {
        SkuIds = portalSource.periodForPortal.map((item) => item.skuPropertyValueId)
        skuVo.period = await SkuPropertyConvertUtils.calPeriod(SkuIds, factory)
      }
    }
    //地区
    SkuIds = []
    const regionIds: string[] = []
    const regionIdsForPortal: string[] = []
    if (portalSource.provinceForPortal) {
      regionIdsForPortal.push(...portalSource.provinceForPortal.map((item) => item.skuPropertyValueId))
    }
    if (portalSource.cityForPortal) {
      regionIdsForPortal.push(...portalSource.cityForPortal.map((item) => item.skuPropertyValueId))
    }
    if (portalSource.countyForPortal) {
      regionIdsForPortal.push(...portalSource.countyForPortal.map((item) => item.skuPropertyValueId))
    }
    if (source.province) {
      regionIds.push(...source.province.map((item) => item.skuPropertyValueId))
    }
    if (source.city) {
      regionIds.push(...source.city.map((item) => item.skuPropertyValueId))
    }
    if (source.county) {
      regionIds.push(...source.county.map((item) => item.skuPropertyValueId))
    }
    SkuIds = [...new Set([...regionIds, ...regionIdsForPortal])]
    if (SkuIds.length) {
      const consolidationLastRegions = portalSource.regionForPortal?.map((item) => item.skuPropertyValueId) || []
      const schemeLastRegions = source.region?.map((item) => item.skuPropertyValueId) || []
      if (isWeb) {
        skuVo.RegionVos = await this.filterWebRegionTreeByCode(regionIds, schemeLastRegions)
        if (regionIdsForPortal.length) {
          skuVo.regionVoForPortal = await this.filterWebRegionTreeByCode(
            regionIdsForPortal,
            consolidationLastRegions,
            'web'
          )
        } else if (regionIds.length) {
          // 专题没有regionIdsForPortal返回，但是门户需要特殊判断使非服务地区可以点击
          skuVo.regionVoForPortal = await this.filterWebRegionTreeByCode(regionIds, schemeLastRegions, 'web')
        }
      } else {
        skuVo.RegionVos = await this.filterRegionTreeByCode(schemeLastRegions)
        if (regionIdsForPortal.length) {
          // skuVo.regionVoForPortal = await this.filterRegionTreeByCode(consolidationLastRegions)
          // 经产品确认，首页报名筛选不考虑非服务地区的禁用状态，只要有方案即可查询
          skuVo.regionVoForPortal = await this.filterWebRegionTreeByCode(
            regionIdsForPortal,
            consolidationLastRegions,
            'h5'
          )
        } else if (regionIds.length) {
          // 专题没有regionIdsForPortal返回，但是门户需要特殊判断使非服务地区可以点击
          skuVo.regionVoForPortal = await this.filterWebRegionTreeByCode(regionIds, schemeLastRegions, 'h5')
        }
      }
    }
    // const regionArr = await SkuPropertyConvertUtils.requestBusinessRegionResponse(SkuIds, factory)
    // }
    //行业
    if (source.industry.length) {
      SkuIds = source.industry.map((item) => item.skuPropertyValueId)
      skuVo.industry = await SkuPropertyConvertUtils.calIndustry(SkuIds, factory)
    }
    const skuVoIds: string[] = []
    const getSkuVo = (skuResponse: TradeSkuPropertyResponse[]): SkuVo[] => {
      if (skuResponse?.length) {
        return skuResponse.map(item => {
          const skuV = new SkuVo()
          skuV.skuPropertyValueId = item.skuPropertyValueId
          return skuV
        })
      } else {
        return []
      }
    }
    //科目类型
    if (source.subjectType.length) {
      if (currentIndustryId) {
        SkuIds = source.subjectType.map(item => item.skuPropertyValueId)
        skuVoIds.push(...SkuIds)
        skuVo.subjectType = getSkuVo(source.subjectType)
      }
    }
    //培训类别
    if (source.trainingCategory.length) {
      if (currentIndustryId) {
        SkuIds = source.trainingCategory.map(item => item.skuPropertyValueId)
        skuVoIds.push(...SkuIds)
        skuVo.trainingCategory = getSkuVo(source.trainingCategory)
      }
    }
    //技术等级
    if (source.jobLevel.length) {
      if (currentIndustryId) {
        SkuIds = source.jobLevel.map(item => item.skuPropertyValueId)
        skuVoIds.push(...SkuIds)
        skuVo.jobLevel = getSkuVo(source.jobLevel)
      }
    }
    //培训对象
    if (source.trainingObject.length) {
      if (currentIndustryId) {
        SkuIds = source.trainingObject.map(item => item.skuPropertyValueId)
        skuVoIds.push(...SkuIds)
        skuVo.trainingObject = getSkuVo(source.trainingObject)
      }
    }
    //岗位类别
    if (source.positionCategory.length) {
      if (currentIndustryId) {
        SkuIds = source.positionCategory.map(item => item.skuPropertyValueId)
        skuVoIds.push(...SkuIds)
        skuVo.positionCategory = getSkuVo(source.positionCategory)
      }
    }
    //培训专业
    if (source.trainingProfessional.length) {
      if (currentIndustryId) {
        SkuIds = source.trainingProfessional.map(item => item.skuPropertyValueId)
        skuVoIds.push(...SkuIds)
        skuVo.trainingMajor = getSkuVo(source.trainingProfessional)
      }
    }
    // 学科，学段
    if (source.learningPhase && source.learningPhase.length) {
      if (currentIndustryId) {
        SkuIds = source.learningPhase.map(item => item.skuPropertyValueId)
        skuVoIds.push(...SkuIds)
        skuVo.learningPhase = getSkuVo(source.learningPhase)
      }
    }
    if (source.learningPhase.length && source.discipline && source.discipline.length) {
      if (currentIndustryId) {
        SkuIds = source.discipline.map(item => item.skuPropertyValueId)
        skuVoIds.push(...SkuIds)
        skuVo.discipline = getSkuVo(source.discipline)
      }
    }
    if (source.certificatesType && source.certificatesType.length) {
      if (currentIndustryId) {
        SkuIds = source.certificatesType.map(item => item.skuPropertyValueId)
        skuVoIds.push(...SkuIds)
        skuVo.certificatesType = getSkuVo(source.certificatesType)
      }
    }
    if (source.certificatesType.length && source.practitionerCategory && source.practitionerCategory.length) {
      if (currentIndustryId) {
        SkuIds = source.practitionerCategory.map(item => item.skuPropertyValueId)
        skuVoIds.push(...SkuIds)
        skuVo.practitionerCategory = getSkuVo(source.practitionerCategory)
      }
    }
    if (skuVoIds.length && currentIndustryId) {
      const skuVos = await SkuPropertyConvertUtils.batchCalProperty([
        {
          propertyIds: skuVoIds,
          industryId: currentIndustryId
        }
      ])
      const keys = [
        'subjectType',
        'trainingCategory',
        'jobLevel',
        'trainingObject',
        'positionCategory',
        'trainingMajor',
        'learningPhase',
        'discipline',
        'certificatesType',
        'practitionerCategory'
      ]
      keys.forEach(key => {
        const skus = skuVo[key] as SkuVo[]
        if (skus?.length) {
          skus.forEach(sku => {
            const vo = skuVos.find(item => item.skuPropertyValueId === sku.skuPropertyValueId)
            if (vo) {
              Object.assign(sku, vo)
            }
          })
        }
      })
    }
    // 专题行业
    if (portalSource.belongIndustryForPortal?.length) {
      await QueryIndustry.getIndustryDICTZTForestage(
        portalSource.belongIndustryForPortal.map((item) => item.skuPropertyValueId)
      )
      skuVo.specialIndustry = portalSource.belongIndustryForPortal.map((item) => {
        const industryName = QueryIndustry.industryMap.get(item.skuPropertyValueId)?.name ?? ''
        const sku = new SkuVo()
        sku.skuPropertyValueId = item.skuPropertyValueId
        sku.skuPropertyName = industryName
        return sku
      })
    }
    // 培训形式
    if (source.trainingForm?.length) {
      skuVo.trainingMode = source.trainingForm.map((item) => {
        return new SkuVo(item.skuPropertyValueId, TrainingMode.map.get(item.skuPropertyValueId as TrainingModeEnum))
      })
    }
    return skuVo
  }
  // 处理学段
  static async calTrainingPhase(SkuIds: string[], industry: string) {
    await QueryIndustry.queryIndustry()
    const industryProperty = QueryIndustry.industryList.find((item) => item.id == industry)
    const dataList = await QueryGrade.queryGradeByIndustryV2(industryProperty.id, industryProperty.propertyId)
    const skuVos: SkuVo[] = []
    // const dataList = await QueryGrade.queryGradeByIndustry()
    dataList.forEach((tmpItem: TrainingPropertyResponse) => {
      if (SkuIds.indexOf(tmpItem.propertyId) !== -1) {
        const skuV = new SkuVo()
        skuV.skuPropertyValueId = tmpItem.propertyId
        skuV.skuPropertyName = tmpItem.showName ? tmpItem.showName : tmpItem.name
        skuV.parentId = tmpItem.parentId
        skuVos.push(skuV)
      }
    })
    return skuVos
  }
  // 处理学科、学段
  static async calTrainingDiscipline(skuIds: string[], industryId?: string) {
    const skuVos = new Array<SkuVo>()

    const trainingObjectList = await QueryPropertyDetail.getPropertyDetailByIds(industryId, skuIds)

    trainingObjectList?.length &&
      trainingObjectList.map((item) => {
        const sku = new SkuVo()

        sku.skuPropertyName = item.showName ? item.showName : item.name
        sku.parentId = item.parentId
        sku.skuPropertyValueId = item.propertyId
        skuVos.push(sku)
      })

    return skuVos
  }
  // 处理证书类型、执业类别
  static async calCertAndPractitioner(skuIds: string[], industryId?: string) {
    const skuVos = new Array<SkuVo>()
    const trainingObjectList = await QueryPropertyDetail.getPropertyDetailByIds(industryId, skuIds)
    trainingObjectList?.length &&
      trainingObjectList.map((item) => {
        const sku = new SkuVo()
        sku.skuPropertyName = item.showName ? item.showName : item.name
        sku.parentId = item.parentId
        sku.skuPropertyValueId = item.propertyId
        skuVos.push(sku)
      })
    return skuVos
  }
  //处理地区
  static async calBusinessRegion(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    const dataList: Array<BusinessRegionResponse> = await SkuPropertyConvertUtils.requestBusinessRegionResponse(
      SkuIds,
      factory
    )
    SkuIds.map((res) => {
      const curRegion = dataList.find((item) => item.id === res)
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = curRegion?.id
      skuV.skuPropertyName = curRegion?.name
      skuVos.push(skuV)
    })
    return skuVos
  }
  static async requestBusinessRegionResponse(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const dataList: Array<BusinessRegionResponse> = await factory.queryPhysicalRegion.querRegionDetil(SkuIds)
    return dataList
  }
  /**
   * 通用批量查询行业属性
   * @param ids 请求入参
   */
  static async batchCalProperty(ids: BatchQueryPropertyRequest[]) {
    const result = [] as SkuVo[]
    const dataList: Array<TrainingPropertyResponse> = await QueryPropertyDetail.batchGetPropertyDetailByIds(ids)
    dataList?.forEach(item => {
      const opt = new SkuVo()
      opt.skuPropertyValueId = item.propertyId
      opt.skuPropertyName = item.showName ? item.showName : item.name
      result.push(opt)
    })
    return result
  }
  //处理行业
  static async calIndustry(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    const dataList: Array<IndustryVo> = await factory.queryIndustry.getIndustryByIdList(SkuIds)
    dataList.forEach((tmpItem: IndustryVo) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.id
      skuV.skuPropertyName = tmpItem.name
      skuVos.push(skuV)
    })
    return skuVos
  }
  //处理学时
  static async calPeriod(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    // const dataList: Array<IndustryVo> = await factory.queryIndustry.getIndustryByIdList(SkuIds)
    SkuIds?.forEach((item) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = item
      skuV.skuPropertyName = item
      skuVos.push(skuV)
    })
    return skuVos
  }
  //处理年度
  static async calYear(SkuIds: string[], factory: QueryBasicdataDictionaryFactory) {
    const skuVos: SkuVo[] = []
    SkuIds?.forEach((item) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = item
      skuV.skuPropertyName = item
      skuVos.push(skuV)
    })
    return skuVos
    /*const dataList: Array<BusinessYearResponse> = await factory.queryYear.getYearByIdList(SkuIds)
    dataList.forEach((tmpItem: BusinessYearResponse) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.id
      skuV.skuPropertyName = tmpItem.year
      skuVos.push(skuV)
    })
    return skuVos*/
  }
  //处理专业
  static async calTrainingMajor(SkuIds: string[], factory: QueryBasicdataDictionaryFactory, industryId?: string) {
    const skuVos: SkuVo[] = []
    const dataList: Array<TrainingCategoryVo> = await factory.queryTrainingMajor.getTrainingMajorByIdList({
      industryId: industryId || '',
      trainingMajorIdList: SkuIds
    })
    dataList.forEach((tmpItem: TrainingCategoryVo) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.propertyId
      skuV.parentId = tmpItem.parentId
      skuV.skuPropertyName = tmpItem.showName ? tmpItem.showName : tmpItem.name
      skuVos.push(skuV)
    })
    return skuVos
  }
  //处理类别
  static async calTrainingCategory(SkuIds: string[], factory: QueryBasicdataDictionaryFactory, industryId?: string) {
    const skuVos: SkuVo[] = []
    const dataList: Array<TrainingCategoryVo> = await factory.queryTrainingCategory.getTrainingCategoryByIdList({
      industryId: industryId || '',
      trainingCategoryIdList: SkuIds
    })
    dataList.forEach((tmpItem: TrainingCategoryVo) => {
      const skuV = new SkuVo()
      skuV.parentId = tmpItem.parentId
      skuV.skuPropertyValueId = tmpItem.propertyId
      skuV.skuPropertyName = tmpItem.showName ? tmpItem.showName : tmpItem.name
      skuVos.push(skuV)
    })
    return skuVos
  }
  //处理科目类型
  static async calTrainingProperty(SkuIds: string[], factory: QueryBasicdataDictionaryFactory, industryId?: string) {
    const skuVos: SkuVo[] = []
    const dataList: Array<SubjectTypeVo> = await factory.querySubjectType.getSubjectTypeByIdList({
      industryId: industryId || '',
      subjectTypeIdList: SkuIds
    })
    dataList.forEach((tmpItem: SubjectTypeVo) => {
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = tmpItem.propertyId
      skuV.skuPropertyName = tmpItem.showName ? tmpItem.showName : tmpItem.name
      skuVos.push(skuV)
    })
    return skuVos
  }
  //处理技术等级
  async calTechnologyLevel(SkuIds: string[], factory: QueryBasicdataDictionaryFactory, industryId?: string) {
    const skuVos: SkuVo[] = []
    const dataList: Array<TechnologyLevelVo> = await factory.queryTechnologyLevel.query()
    SkuIds.map((res) => {
      const curTechnologyLevel = dataList.find((item) => item.id === res)
      const skuV = new SkuVo()
      skuV.skuPropertyValueId = curTechnologyLevel.id
      skuV.skuPropertyName = curTechnologyLevel.showName
      skuVos.push(skuV)
    })
    return skuVos
  }
  static async filterRegionFirstStep(regionArr: Array<string>, factory: QueryBasicdataDictionaryFactory) {
    // const areaCodeSet = new Set()
    // regionArr.forEach((tmpItem: BusinessRegionResponse) => {
    //   tmpItem.regionPath.split('/').forEach(areaCode => {
    //     areaCode && areaCodeSet.add(areaCode)
    //   })
    // })
    const region2List: Array<BusinessRegionResponse> = await factory.queryBusinessRegion.queryBusinessRegionByIdList(
      regionArr
    )
    return SkuPropertyConvertUtils.filterRegionArr(region2List) || []
  }
  //将地区对象数组组装成树状对象
  static filterRegionArr(regionArr: Array<BusinessRegionResponse>, parentId = '0') {
    const regionVoArr: RegionVo[] = []
    regionArr.forEach((item) => {
      if (item.parentId == parentId) {
        const regionV = new RegionVo()
        regionV.enable = item.enable
        regionV.sort = item.sort
        regionV.name = item.name
        regionV.regionPath = item.regionPath
        regionV.parentId = item.parentId
        regionV.id = item.id
        regionV.child = SkuPropertyConvertUtils.filterRegionArr(regionArr, item.id)
        regionVoArr.push(regionV)
      }
    })
    if (regionVoArr.length) {
      return regionVoArr
    }
    return undefined
  }
  /**
   ** SkuPropertyResponse转换SkuPropertyVo
   */
  async SkuPropertyResponseConvertToSkuPropertyVo(
    source: SkuPropertyResponse,
    currentIndustryId?: string
  ): Promise<SkuPropertyVo> {
    const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
    const skuVo = new SkuPropertyVo()
    let SkuIds = []
    //年度
    if (source.year.length) {
      SkuIds = source.year
      skuVo.year = await SkuPropertyConvertUtils.calYear(SkuIds, factory)
    }
    //地区
    // if (source.region.length) {
    //   SkuIds = source.region
    SkuIds = [...source.province, ...source.city, ...source.county]
    // const regionArr = await SkuPropertyConvertUtils.requestBusinessRegionResponse(SkuIds, factory)
    skuVo.RegionVos = await SkuPropertyConvertUtils.filterRegionFirstStep(SkuIds, factory)
    // }
    //行业
    if (source.industry.length) {
      SkuIds = source.industry
      skuVo.industry = await SkuPropertyConvertUtils.calIndustry(SkuIds, factory)
    }
    //科目类型
    if (source.subjectType.length) {
      SkuIds = source.subjectType
      if (currentIndustryId) {
        skuVo.subjectType = await SkuPropertyConvertUtils.calTrainingProperty(SkuIds, factory, currentIndustryId)
      }
    }
    //培训类别
    if (source.trainingCategory.length) {
      SkuIds = source.trainingCategory
      if (currentIndustryId) {
        skuVo.trainingCategory = await SkuPropertyConvertUtils.calTrainingCategory(SkuIds, factory, currentIndustryId)
      }
    }
    //培训专业
    if (source.trainingProfessional.length) {
      SkuIds = source.trainingProfessional
      if (currentIndustryId) {
        skuVo.trainingMajor = await SkuPropertyConvertUtils.calTrainingMajor(SkuIds, factory, currentIndustryId)
      }
    }
    if (source.trainingWay.length) {
      skuVo.trainingMode = source.trainingWay.map((item) => {
        const vo = new SkuVo()
        vo.skuPropertyValueId = item
        vo.skuPropertyName = TrainingMode.map.get(item as TrainingModeEnum)
        return vo
      })
    }
    return skuVo
  }
  /**
   ** CommoditySkuPropertyResponse转换SkuPropertyResponseVo
   */
  async convertToSkuPropertyResponseVo(source: ComplexSkuPropertyResponse): Promise<SkuPropertyResponseVo> {
    const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
    const skuVo = new SkuPropertyResponseVo()
    let SkuIds = []
    //年度
    if (source?.year) {
      SkuIds = [source.year.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calYear(SkuIds, factory)
      skuVo.year = skuVos[0]
    }
    //地区
    // if (source.region) {
    SkuIds = []
    const regionIds: string[] = []
    const regionForPortalIds: string[] = []
    if (source?.province?.skuPropertyValueId) regionIds.push(source?.province?.skuPropertyValueId)
    if (source?.city?.skuPropertyValueId) regionIds.push(source?.city?.skuPropertyValueId)
    if (source?.county?.skuPropertyValueId) regionIds.push(source?.county?.skuPropertyValueId)
    if (source?.provinceForPortal?.skuPropertyValueId)
      regionForPortalIds.push(source?.provinceForPortal?.skuPropertyValueId)
    if (source?.cityForPortal?.skuPropertyValueId) regionForPortalIds.push(source?.cityForPortal?.skuPropertyValueId)
    if (source?.countyForPortal?.skuPropertyValueId)
      regionForPortalIds.push(source?.countyForPortal?.skuPropertyValueId)
    SkuIds.push(...regionIds, ...regionForPortalIds)
    if (SkuIds.length) {
      const skuVos = await this.batchCalBusinessRegion(SkuIds)
      // 常规地区
      let regionSkuId = '',
        regionSkuName = ''
      regionIds
        .map((item) => {
          return skuVos.find((ite) => ite.skuPropertyValueId == item)
        })
        .forEach((item) => {
          regionSkuId += !regionSkuId ? item.skuPropertyValueId : '/' + item.skuPropertyValueId
          regionSkuName += !regionSkuName ? item.skuPropertyName : '/' + item.skuPropertyName
        })
      const regionSku = new SkuVo()
      regionSku.skuPropertyName = regionSkuName
      regionSku.skuPropertyValueId = regionSkuId
      skuVo.region = regionSku
      // 商品门户地区
      if (regionForPortalIds.length) {
        regionSkuId = ''
        regionSkuName = ''
        regionForPortalIds
          .map((item) => {
            return skuVos.find((ite) => ite.skuPropertyValueId == item)
          })
          .forEach((item) => {
            regionSkuId += !regionSkuId ? item.skuPropertyValueId : '/' + item.skuPropertyValueId
            regionSkuName += !regionSkuName ? item.skuPropertyName : '/' + item.skuPropertyName
          })
        const consolidationRegion = new SkuVo()
        consolidationRegion.skuPropertyName = regionSkuName
        consolidationRegion.skuPropertyValueId = regionSkuId
        skuVo.consolidationRegion = consolidationRegion
      } else {
        skuVo.consolidationRegion = regionSku
      }
    }

    // }
    //行业
    if (source?.industry) {
      SkuIds = [source.industry.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calIndustry(SkuIds, factory)
      skuVo.industry = skuVos[0]
    }
    //科目类型
    if (source?.subjectType) {
      SkuIds = [source.subjectType.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calTrainingProperty(
        SkuIds,
        factory,
        source.industry.skuPropertyValueId
      )
      skuVo.subjectType = skuVos[0]
    }
    //培训类别
    if (source?.trainingCategory) {
      SkuIds = [source.trainingCategory.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calTrainingCategory(
        SkuIds,
        factory,
        source.industry.skuPropertyValueId
      )
      skuVo.trainingCategory = skuVos[0]
    }
    //培训专业
    if (source?.trainingProfessional) {
      SkuIds = [source.trainingProfessional.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calTrainingMajor(SkuIds, factory, source.industry.skuPropertyValueId)
      if (skuVos && skuVos.length) {
        skuVo.trainingMajor = skuVos[0]
      } else {
        skuVo.trainingMajor = new SkuVo()
      }
    }
    if (source?.technicalGrade) {
      SkuIds = [source.technicalGrade.skuPropertyValueId]
      const skuVos = await this.calTechnologyLevel(SkuIds, factory)
      skuVo.technicalGrade = skuVos[0]
    }
    if (source?.trainingObject) {
      SkuIds = [source.trainingObject.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calPropertyObject(SkuIds, source.industry.skuPropertyValueId)
      skuVo.trainingObject = (skuVos?.length && skuVos[0]) || new SkuVo()
    }
    if (source?.positionCategory) {
      SkuIds = [source.positionCategory.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calPropertyObject(SkuIds, source.industry.skuPropertyValueId)
      skuVo.positionCategory = (skuVos?.length && skuVos[0]) || new SkuVo()
    }
    if (source?.jobLevel) {
      SkuIds = [source.jobLevel.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calPropertyObject(SkuIds, source.industry.skuPropertyValueId)
      skuVo.jobLevel = (skuVos?.length && skuVos[0]) || new SkuVo()
    }
    // 学科、学段
    if (source?.learningPhase) {
      SkuIds = [source.learningPhase.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calTrainingDiscipline(SkuIds, source.industry.skuPropertyValueId)
      skuVo.learningPhase = (skuVos?.length && skuVos[0]) || new SkuVo()
    }
    if (source?.discipline) {
      SkuIds = [source.discipline.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calTrainingDiscipline(SkuIds, source.industry.skuPropertyValueId)
      skuVo.discipline = (skuVos?.length && skuVos[0]) || new SkuVo()
    }
    if (source?.certificatesType) {
      SkuIds = [source.certificatesType.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calTrainingDiscipline(SkuIds, source.industry.skuPropertyValueId)
      skuVo.certificatesType = (skuVos?.length && skuVos[0]) || new SkuVo()
    }
    if (source?.practitionerCategory) {
      SkuIds = [source.practitionerCategory.skuPropertyValueId]
      const skuVos = await SkuPropertyConvertUtils.calTrainingDiscipline(SkuIds, source.industry.skuPropertyValueId)
      skuVo.practitionerCategory = (skuVos?.length && skuVos[0]) || new SkuVo()
    }
    if (source?.trainingWay || source?.trainingForm) {
      const skuPropertyValueId = source?.trainingWay?.skuPropertyValueId || source?.trainingForm?.skuPropertyValueId
      const skuPropertyValueName =
        TrainingMode.map.get(skuPropertyValueId as TrainingModeEnum) || source.trainingForm.skuPropertyValueName
      skuVo.trainingMode = new SkuVo<TrainingModeEnum>(skuPropertyValueId as TrainingModeEnum, skuPropertyValueName)
    }
    return skuVo
  }
  technicalGradeMap: Map<string, string> = new Map()
  /**
   ** 获取技术等级
   */
  async technicalGradeMapMethonds(): Promise<Map<string, string>> {
    const ids = [...this.technicalGradeMap.keys()]
    if (ids.length > 0) {
      return this.technicalGradeMap
    }
    const data = await BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryTechnologyLevel.query()
    data.forEach((item) => {
      this.technicalGradeMap.set(item.id, item.showName)
    })
    return this.technicalGradeMap
    // return skuVo
  }

  /**
   * @description ConvertToSkuPropertyResponseVo的批量版
   * @param source 方案sku集合
   */
  async batchConvertToSkuPropertyResponseVo(source: SchemeSkuInfo[]): Promise<SchemeSkuInfo[]> {
    const factory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory
    let yearList: SkuVo[] = [],
      regionList: SkuVo[] = [],
      industryList: SkuVo[] = []
    // 查询年度
    const yearIds = [...new Set(source.map((item) => item.sku.year?.skuPropertyValueId)?.filter(Boolean))]
    if (yearIds.length) {
      yearList = await SkuPropertyConvertUtils.calYear(yearIds, factory)
    }
    // 查询地区
    const regionIds = [
      ...new Set(source.map((item) => item.sku.province?.skuPropertyValueId)?.filter(Boolean)),
      ...new Set(source.map((item) => item.sku.city?.skuPropertyValueId)?.filter(Boolean)),
      ...new Set(source.map((item) => item.sku.county?.skuPropertyValueId)?.filter(Boolean)),
      ...new Set(source.map((item) => item.sku.provinceForPortal?.skuPropertyValueId)?.filter(Boolean)),
      ...new Set(source.map((item) => item.sku.cityForPortal?.skuPropertyValueId)?.filter(Boolean)),
      ...new Set(source.map((item) => item.sku.countyForPortal?.skuPropertyValueId)?.filter(Boolean))
    ]
    if (regionIds.length) {
      regionList = await this.batchCalBusinessRegion(regionIds)
    }
    // 查询行业
    const industryIds = [...new Set(source.map((item) => item.sku.industry?.skuPropertyValueId)?.filter(Boolean))]
    if (industryIds.length) {
      industryList = await SkuPropertyConvertUtils.calIndustry(industryIds, factory)
    }

    const skuPropertyMap = new Map<string, string[]>()
    source.forEach(item => {
      const industry = item.sku?.industry?.skuPropertyValueId

      if (industry) {
        if (!skuPropertyMap.has(industry)) {
          skuPropertyMap.set(industry, [])
        }
        const skuPropertyIdList = skuPropertyMap.get(industry)
        Object.keys(item.sku).forEach(key => {
          if (
            [
              'subjectType',
              'trainingCategory',
              'trainingProfessional',
              'technicalGrade',
              'trainingObject',
              'positionCategory',
              'jobLevel',
              'jobCategory',
              'grade',
              'subject',
              'learningPhase',
              'discipline',
              'certificatesType',
              'practitionerCategory',
              'qualificationCategory',
              'trainingForm'
            ].includes(key) &&
            item.sku?.[key]?.['skuPropertyValueId']
          ) {
            skuPropertyIdList.push(item.sku?.[key].skuPropertyValueId)
          }
        })
        skuPropertyMap.set(industry, [...new Set(skuPropertyIdList)])
      }
    })
    const skuVos: SkuVo[] = []
    if (skuPropertyMap.size) {
      const request: BatchQueryPropertyRequest[] = []
      skuPropertyMap.forEach((value, key) => {
        if (value?.length) {
          request.push(new BatchQueryPropertyRequest(key, value))
        }
      })
      if (request.length) {
        skuVos.push(...(await SkuPropertyConvertUtils.batchCalProperty(request)))
      }
    }
    source.forEach(async (item) => {
      // 填充年度
      item.skuName.year =
        yearList.find((el) => el.skuPropertyValueId === item.sku.year?.skuPropertyValueId) || new SkuVo()
      // 填充地区
      let province =
        regionList.find((el) => el.skuPropertyValueId === item.sku.province?.skuPropertyValueId) || new SkuVo()
      let city = regionList.find((el) => el?.skuPropertyValueId === item.sku.city?.skuPropertyValueId) || new SkuVo()
      let county =
        regionList.find((el) => el?.skuPropertyValueId === item.sku.county?.skuPropertyValueId) || new SkuVo()
      let regionInfoList = [province, city, county]
      item.skuName.region.skuPropertyValueId = regionInfoList
        .map((el) => el.skuPropertyValueId)
        ?.filter(Boolean)
        ?.join('/')
      item.skuName.region.skuPropertyName = regionInfoList
        .map((el) => el.skuPropertyName)
        ?.filter(Boolean)
        ?.join('/')
      // 填充整活地区
      if (
        item.sku.provinceForPortal?.skuPropertyValueId ||
        item.sku.cityForPortal?.skuPropertyValueId ||
        item.sku.countyForPortal?.skuPropertyValueId
      ) {
        province =
          regionList.find((el) => el.skuPropertyValueId === item.sku.provinceForPortal?.skuPropertyValueId) ||
          new SkuVo()
        city =
          regionList.find((el) => el?.skuPropertyValueId === item.sku.cityForPortal?.skuPropertyValueId) || new SkuVo()
        county =
          regionList.find((el) => el?.skuPropertyValueId === item.sku.countyForPortal?.skuPropertyValueId) ||
          new SkuVo()
        regionInfoList = [province, city, county]
        item.skuName.consolidationRegion.skuPropertyValueId = regionInfoList
          .map((el) => el.skuPropertyValueId)
          ?.filter(Boolean)
          ?.join('/')
        item.skuName.consolidationRegion.skuPropertyName = regionInfoList
          .map((el) => el.skuPropertyName)
          ?.filter(Boolean)
          ?.join('/')
      } else {
        item.skuName.consolidationRegion = item.skuName.region
      }
      // 填充行业
      item.skuName.industry =
        industryList.find((el) => el.skuPropertyValueId === item.sku.industry?.skuPropertyValueId) || new SkuVo()
      // 填充科目类型
      item.skuName.subjectType =
        skuVos.find(el => el.skuPropertyValueId === item.sku.subjectType?.skuPropertyValueId) || new SkuVo()
      // 填充培训类别
      item.skuName.trainingCategory =
        skuVos.find(el => el.skuPropertyValueId === item.sku.trainingCategory?.skuPropertyValueId) || new SkuVo()
      // 填充培训专业
      item.skuName.trainingMajor =
        skuVos.find(el => el.skuPropertyValueId === item.sku.trainingProfessional?.skuPropertyValueId) || new SkuVo()
      item.skuName.trainingObject =
        skuVos.find(el => el.skuPropertyValueId === item.sku.trainingObject?.skuPropertyValueId) || new SkuVo()
      item.skuName.positionCategory =
        skuVos.find(el => el.skuPropertyValueId === item.sku.positionCategory?.skuPropertyValueId) || new SkuVo()
      item.skuName.jobLevel =
        skuVos.find(el => el.skuPropertyValueId === item.sku.jobLevel?.skuPropertyValueId) || new SkuVo()
      // 学段、学科
      const phaseSku = skuVos.find(el => el.skuPropertyValueId == item.sku.learningPhase?.skuPropertyValueId)
      item.skuName.learningPhase = phaseSku || new SkuVo()
      const subjectSku = skuVos.find(el => el.skuPropertyValueId == item.sku.discipline?.skuPropertyValueId)
      item.skuName.discipline = subjectSku || new SkuVo()
      item.skuName.certificatesType =
        skuVos.find(el => el.skuPropertyValueId === item.sku.certificatesType?.skuPropertyValueId) || new SkuVo()
      item.skuName.practitionerCategory =
        skuVos.find(el => el.skuPropertyValueId === item.sku.practitionerCategory?.skuPropertyValueId) || new SkuVo()
      const trainingMode = item.sku.trainingForm?.skuPropertyValueId || item.sku.trainingWay?.skuPropertyValueId
      item.skuName.trainingMode.skuPropertyValueId = trainingMode as TrainingModeEnum
      item.skuName.trainingMode.skuPropertyName = trainingMode
        ? TrainingMode.map.get(trainingMode as TrainingModeEnum)
        : ''
    })
    return source
  }

  /**
   * 批量查询科目类型
   * @param subjectTypeGroup 【行业id-科目类型id集合】数组
   * @param factory 查询工厂
   */
  static async batchCalTrainingSubjectType(
    subjectTypeGroup: SubjectTypeRequest[],
    factory: QueryBasicdataDictionaryFactory
  ) {
    const result = [] as SkuVo[]
    const dataList: Array<SubjectTypeVo> = await factory.querySubjectType.batchGetSubjectTypeByIdList(subjectTypeGroup)
    dataList?.forEach((item) => {
      const opt = new SkuVo()
      opt.skuPropertyValueId = item.propertyId
      opt.skuPropertyName = item.showName ? item.showName : item.name
      result.push(opt)
    })
    return result
  }

  /**
   * 批量查询培训类别
   * @param trainingCategoryGroup 请求入参
   * @param factory 查询工厂
   */
  static async batchCalTrainingCategory(
    trainingCategoryGroup: TrainingCategoryRequest[],
    factory: QueryBasicdataDictionaryFactory
  ) {
    const result = [] as SkuVo[]
    const dataList: Array<TrainingCategoryVo> = await factory.queryTrainingCategory.batchGetTrainingCategoryByIdList(
      trainingCategoryGroup
    )
    dataList?.forEach((item) => {
      const opt = new SkuVo()
      opt.skuPropertyValueId = item.propertyId
      opt.skuPropertyName = item.showName ? item.showName : item.name
      result.push(opt)
    })
    return result
  }

  /**
   * 批量查询培训专业
   * @param trainingProfessionalGroup 请求入参
   * @param factory 查询工厂
   */
  static async batchCalTrainingProfessional(
    trainingProfessionalGroup: TrainingProfessionalRequest[],
    factory: QueryBasicdataDictionaryFactory
  ) {
    const result = [] as SkuVo[]
    const dataList: Array<TrainingCategoryVo> = await factory.queryTrainingMajor.batchGetTrainingMajorByIdList(
      trainingProfessionalGroup
    )
    dataList?.forEach((item) => {
      const opt = new SkuVo()
      opt.skuPropertyValueId = item.propertyId
      opt.skuPropertyName = item.showName ? item.showName : item.name
      result.push(opt)
    })
    return result
  }
  regionSkuVo: SkuVo[] = []
  /**
   * 批量查询地区
   * @param skuIds skuId集合
   */
  async batchCalBusinessRegion(skuIds: string[]) {
    let skuVos: SkuVo[] = []
    let isFind = true
    if (this.regionSkuVo.length > 0) {
      skuIds.forEach((item) => {
        const target = this.regionSkuVo.find((el) => el.skuPropertyValueId === item)
        if (target) {
          skuVos.push({
            skuPropertyValueId: target.skuPropertyValueId,
            skuPropertyName: target.skuPropertyName
          } as SkuVo)
        } else {
          isFind = false
        }
      })
      if (isFind) {
        return skuVos
      } else {
        skuVos = []
      }
    }
    this.cacheRegionResponse = JSON.parse(localStorage.getItem('BusinessRegion')) || []
    const time = JSON.parse(localStorage.getItem('BusinessRegionSetTime'))
    const hoursDiff = Math.floor(Math.abs(new Date().getTime() - time) / (1000 * 60 * 60))
    if (this.cacheRegionResponse.length <= 0 || hoursDiff > 3) {
      const response = await BasicDataQueryForestage.getServiceOrIndustryRegion(1)
      this.cacheRegionResponse = response.data
      localStorage.setItem('BusinessRegion', JSON.stringify(response.data))
      localStorage.setItem('BusinessRegionSetTime', new Date().getTime().toString())
    }
    this.regionSkuVo = []
    this.cacheRegionResponse.forEach((item) => {
      this.regionSkuVo.push({
        skuPropertyValueId: item.code,
        skuPropertyName: item.name
      } as SkuVo)
    })
    skuIds.forEach((item) => {
      const target = this.cacheRegionResponse.find((el) => el.code === item)
      if (target) {
        skuVos.push({
          skuPropertyValueId: target.code,
          skuPropertyName: target.name
        } as SkuVo)
      }
    })
    if (skuVos.length < skuIds.length) {
      const skuVosList = skuVos.map((ite) => ite.skuPropertyValueId)
      const otherRegion = skuIds.filter((item) => {
        return !skuVosList.includes(item)
      })
      // const regionList = await QueryRegion.querRegionDetil(otherRegion)
      const regionList = await QueryBusinessRegion.queryRegionsNameByIds(otherRegion)
      // regionList.forEach(item => {
      //   skuVos.push({
      //     skuPropertyValueId: item.id,
      //     skuPropertyName: item.name
      //   } as SkuVo)
      // })
      otherRegion.map((item) => {
        const skuVo = new SkuVo()
        skuVo.skuPropertyName = regionList.get(item)
        skuVo.skuPropertyValueId = item
        skuVos.push(skuVo)
      })
    }
    return skuIds.map((item) => {
      return skuVos.find((ite) => ite.skuPropertyValueId == item) || new SkuVo()
    })
  }
  regionSkuTreeMap: Map<string, RegionVo[]> = new Map()
  cacheRegionResponse = new Array<RegionResponse>()
  /**
   * 根据code生成地区数组
   * @param skuIds skuId集合
   */
  async filterRegionTreeByCode(skuIds: string[]) {
    // let tree: RegionVo[] = []
    // const treeKey = skuIds.join('-')
    // if (this.regionSkuTreeMap.get(treeKey)?.length > 0) {
    //   return this.regionSkuTreeMap.get(treeKey)
    // }
    if (this.cacheRegionResponse.length <= 0) {
      const response = await BasicDataQueryForestage.getServiceOrIndustryRegion(1)
      this.cacheRegionResponse = response.data
    }
    const flatList = [] as RegionVo[]

    this.cacheRegionResponse.forEach((item) => {
      if (item.code && skuIds.includes(item.code)) {
        const opt = new RegionVo()
        opt.id = item.code
        opt.parentId = item.parentCode
        opt.regionPath = item.codePath
        opt.name = item.name
        opt.child = undefined
        flatList.push(opt)
      }
    })
    flatList.sort((a, b) => Number(b.id) - Number(a.id))
    // if (flatList.length) {
    //   tree = SkuPropertyConvertUtils.listToTreeArr(flatList)
    // }
    // this.regionSkuTreeMap.set(treeKey, tree)
    return flatList
  }

  /**
   * 根据code生成地区树
   * @param skuIds skuId集合
   */
  async filterWebRegionTreeByCode(skuIds: string[], schemeLastRegions?: string[], isPortal = '') {
    let tree: RegionVo[] = []
    // const treeKey = skuIds.join('-')
    // if (this.regionSkuTreeMap.get(treeKey)?.length > 0) {
    //   return this.regionSkuTreeMap.get(treeKey)
    // }
    this.cacheRegionResponse = JSON.parse(localStorage.getItem('BusinessRegion')) || []
    const time = JSON.parse(localStorage.getItem('BusinessRegionSetTime'))
    const hoursDiff = Math.floor(Math.abs(new Date().getTime() - time) / (1000 * 60 * 60))
    if (this.cacheRegionResponse.length <= 0 || hoursDiff > 3) {
      const response = await BasicDataQueryForestage.getServiceOrIndustryRegion(1)
      this.cacheRegionResponse = response.data
      localStorage.setItem('BusinessRegion', JSON.stringify(response.data))
      localStorage.setItem('BusinessRegionSetTime', new Date().getTime().toString())
    }
    const flatList = [] as RegionVo[]
    this.cacheRegionResponse.forEach(item => {
      if (item.code && skuIds.includes(item.code)) {
        const opt = new RegionVo()
        opt.id = item.code
        opt.parentId = item.parentCode
        opt.regionPath = item.codePath
        opt.name = item.name
        // 禁用/启用使用错误
        opt.enable = false
        opt.child = undefined
        flatList.push(opt)
      }
    })
    if (flatList.length < skuIds.length) {
      const skuVosList = flatList.map((ite) => ite.id)
      const otherRegion = skuIds.filter((item) => {
        return !skuVosList.includes(item)
      })
      let regionList = [] as RegionTreeVo[]
      if (otherRegion.length) {
        await QueryBusinessRegion.getCountrywideRegion()
        regionList = await QueryBusinessRegion.getCountrywideRegionList(otherRegion)
      }
      regionList.map((ite) => {
        if (ite.id && skuIds.includes(ite.id)) {
          const opt = new RegionVo()
          opt.id = ite.id
          opt.parentId = ite.parentId
          opt.regionPath = ite.regionPath
          opt.name = ite.name
          // 禁用/启用使用错误
          // 经产品确认，首页报名筛选不考虑非服务地区的禁用状态，只要有方案即可查询
          opt.enable = isPortal ? false : true
          opt.child = undefined
          flatList.push(opt)
        }
      })
    }
    if (flatList.length) {
      flatList.forEach((item) => {
        if (schemeLastRegions && !schemeLastRegions.includes(item.id)) {
          // 禁用/启用使用错误
          item.enable = true
        }
      })
      // if (isPortal == 'h5') {
      //   flatList = flatList.filter((item) => !item.enable)
      // }
      tree = SkuPropertyConvertUtils.listToTreeArr(flatList)
    }
    // this.regionSkuTreeMap.set(treeKey, tree)
    return tree
  }

  static listToTreeArr(list: RegionVo[], parentId = '0') {
    const nodeList = list.filter((node) => node.parentId === parentId)
    nodeList?.forEach((node) => {
      const subRes = SkuPropertyConvertUtils.listToTreeArr(list, node.id)
      if (!subRes || !subRes.length) {
        node.child = undefined
      } else {
        node.child = subRes
      }
    })
    return nodeList
  }
}

export default new SkuPropertyConvertUtils()
