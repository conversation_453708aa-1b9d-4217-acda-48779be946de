schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取批量打印证明日志分页"""
	getCertificateBatchPrintJobPage(page:Page,request:CertificatePrintJobQueryRequest):CertificatePrintJobResponsePage @page(for:"CertificatePrintJobResponse")
	"""批量获取证书操作员姓名集合"""
	getCertificateOperatorIdNameByUserIds(userIds:[String]):[IdNameResponse]
	"""获取学习证明分页"""
	getCertificatePage(page:Page,request:CertificateQueryRequest):CertificateResponsePage @page(for:"CertificateResponse")
	"""获取指定证书的打印日志集合"""
	getCertificatePrintLogList(id:String):[CertificateOperationLogResponse]
	"""获取证书的渲染数据"""
	getCertificateRenderInfo(id:String):CertificateRenderResponse
	"""根据期数id获取证明模板信息"""
	getCertificateTemplateByIssueId(issueId:String):CertificateTemplateResponse
	"""根据培训方案id获取证明模板信息"""
	getCertificateTemplateBySchemeId(schemeId:String):CertificateTemplateResponse
	"""获取证书模板预览信息"""
	getCertificateTemplatePreview(templateId:String):CertificateRenderResponse
	"""批量获取证书中的期数id集合对应的期数名"""
	getIssueIdNameListByIds(issueIds:[String]):[IdNameResponse]
	"""批量获取证书中的方案id集合对应的方案名"""
	getSchemeIdNameListByIds(schemeIds:[String]):[IdNameResponse]
	"""获取学员购买的指定期数的培训证明数据"""
	getStudentCertificateData(issueId:String):CertificateRenderResponse
	"""获取学员已有的培训证明分页信息"""
	getStudentCertificatePage(page:Page):CertificateResponsePage @page(for:"CertificateResponse")
	"""获取学员购买的指定期数的培训证明数据"""
	getUserCertificateData(userId:String,issueId:String):CertificateRenderResponse
}
type Mutation {
	"""批量打印证书"""
	batchPrintCertificate(request:CertificateQueryRequest):Void
	"""打印证书"""
	printCertificate(id:String):Void
}
input CertificatePrintJobQueryRequest @type(value:"com.fjhb.btpx.integrative.gateway.graphql.request.CertificatePrintJobQueryRequest") {
	"""打印状态 1、打印中 2：打印成功 3：打印失败"""
	state:String
	""">=打印时间(即任务开始时间)开始"""
	printDateStart:DateTime
	"""<=打印时间(即任务开始时间)结束"""
	printDateEnd:DateTime
}
input CertificateQueryRequest @type(value:"com.fjhb.btpx.integrative.gateway.graphql.request.CertificateQueryRequest") {
	"""学员姓名"""
	userName:String
	"""学员省份证号"""
	uniqueData:String
	"""方案id"""
	schemeId:String
	"""期数id"""
	issueId:String
	"""订单号"""
	orderNo:String
	"""打印状态：1、未打 2、打印中、3、打印成功 4、打印失败 5、作废 6入库中 7 入库成功  8入库失败"""
	printStatusList:[String]
	""">=考核通过时间开始"""
	passedTimeStart:DateTime
	"""<=考核通过时间结束"""
	passedTimeEnd:DateTime
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
type CertificateOperationLogResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CertificateOperationLogResponse") {
	"""主键 证书操作日志表"""
	id:String
	"""操作人ID"""
	userId:String
	"""操作类型 操作类型|1：创建 2：开始打印 3：打印中 4：打印成功 5：打印失败6、作废7、预览
		8 修改,9 入库中 10 入库成功 11入库失败
	"""
	type:String
	"""创建时间"""
	createTime:DateTime
	"""证书ID"""
	certId:String
	"""证书号"""
	certNo:String
	"""备注"""
	remark:String
}
type CertificatePrintJobResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CertificatePrintJobResponse") {
	"""主键 证书ID"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""打印类别:1、个人打印 2、批量打印"""
	type:String
	"""打印状态 1、打印中 2：打印成功 3：打印失败"""
	state:String
	"""打印时间"""
	printDate:DateTime
	"""完成时间"""
	returnDate:DateTime
	"""日志名称"""
	name:String
	"""生成图片路径"""
	picPath:String
	"""创建人"""
	creatorId:String
}
type CertificateRenderDataResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CertificateRenderDataResponse") {
	"""学员姓名"""
	userName:String
	"""学员身份证号"""
	uniqueData:String
	"""报名日期 XXXX年XX月XX日"""
	registerTime:String
	"""合格日期 XXXX年XX月XX日"""
	passedTime:String
	"""工种名称"""
	workTypeName:String
	"""学时"""
	period:Double!
	"""证书编号"""
	certNo:String
	"""培训机构名称"""
	unitName:String
	"""培训机构简称"""
	unitAbouts:String
	"""培训机构电子公章"""
	unitElectronicSealPath:String
	"""二维码图片地址"""
	qrcodeText:String
}
"""用于培训证明cooper渲染模型数据
	<AUTHOR>
"""
type CertificateRenderModelResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CertificateRenderModelResponse") {
	real:Boolean!
	list:[CertificateRenderDataResponse]
}
type CertificateRenderResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CertificateRenderResponse") {
	"""证书模板地址"""
	templateUrl:String
	"""用于cooper渲染的model数据"""
	model:CertificateRenderModelResponse
}
type CertificateResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.CertificateResponse") {
	"""主键 证书ID"""
	id:String
	"""证书名称"""
	name:String
	"""证书编号"""
	certNo:String
	"""证书持有人"""
	owner:String
	"""证书签发时间"""
	signTime:DateTime
	"""证书状态：1、未打 2、打印中、3、打印成功 4、打印失败 5、作废 6入库中 7 入库成功  8入库失败"""
	state:String
	"""是否测试数据"""
	test:Boolean!
	"""是否打印"""
	print:Boolean!
	"""是否批量打印"""
	batchPrint:Boolean!
	"""创建时间"""
	createTime:DateTime
	"""创建人ID"""
	creatorId:String
	"""图片路径"""
	picPath:String
	"""考核完成的学习方案ID"""
	schemeId:String
	"""考核完成的期数编号"""
	issueId:String
	"""考核ID"""
	assessId:String
	"""订单号"""
	orderNo:String
	"""子订单号"""
	subOrderNo:String
	"""用户信息"""
	userInfo:UserSimpleInfoDTO
}
type IdNameResponse @type(value:"com.fjhb.btpx.integrative.gateway.graphql.response.IdNameResponse") {
	id:String
	name:String
}
"""用户对象
	<AUTHOR> create 2020/3/13 9:42
"""
type UserSimpleInfoDTO @type(value:"com.fjhb.btpx.platform.gateway.graphql.resolver.user.dto.UserSimpleInfoDTO") {
	"""账户id"""
	accountId:String
	"""用户id"""
	userId:String
	"""姓名"""
	name:String
	"""昵称"""
	nickName:String
	"""身份证号"""
	idCard:String
	"""头像地址"""
	photo:String
	"""性别"""
	gender:Int!
	"""手机号码"""
	phone:String
	"""所属地区编码"""
	areaPath:String
}
type CertificateTemplateResponse @type(value:"com.fjhb.platform.core.certificate.v1.kernel.gateway.graphql.response.CertificateTemplateResponse") {
	"""主键 证书模板ID"""
	id:String
	"""模板名称"""
	name:String
	"""模板路径"""
	temPath:String
	"""创建时间"""
	createTime:DateTime
	"""创建人ID"""
	creatorId:String
	"""备注"""
	remark:String
	"""使用范围"""
	useableRange:String
	"""适用形式"""
	applicableType:String
}

scalar List
type CertificatePrintJobResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CertificatePrintJobResponse]}
type CertificateResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CertificateResponse]}
