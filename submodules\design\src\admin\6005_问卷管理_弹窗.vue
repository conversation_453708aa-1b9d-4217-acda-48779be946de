<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">调研问卷-问卷管理</span>
        </div>
        <!--复制确认-->
        <el-button type="primary" @click="dialog01 = true" class="f-mr20">复制确认</el-button>
        <el-dialog title="提示" :visible.sync="dialog01" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">确认复制该调研问卷？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确定复制</el-button>
          </div>
        </el-dialog>
        <!--删除确认-->
        <el-button type="primary" @click="dialog02 = true" class="f-mr20">删除确认</el-button>
        <el-dialog title="提示" :visible.sync="dialog02" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">删除后该问卷需要重新创建，是否确认删除？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">调研问卷-新建/修改问卷模板</span>
        </div>
        <!--删除试题确认-->
        <el-button type="primary" @click="dialog001 = true" class="f-mr20">删除试题确认</el-button>
        <el-dialog title="提示" :visible.sync="dialog001" width="400px" class="m-dialog">
          <div class="dialog-alert">
            <!--警告-->
            <i class="icon el-icon-warning warning"></i>
            <span class="txt">是否确认删除该试题？</span>
          </div>
          <div slot="footer">
            <el-button>取 消</el-button>
            <el-button type="primary">确定</el-button>
          </div>
        </el-dialog>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">调研问卷-添加试题</span>
        </div>
        <!--添加试题-单选/多选-->
        <el-button @click="dialog1 = true" type="primary" class="f-mr20 f-mb20">添加试题-单选/多选</el-button>
        <el-drawer title="添加试题" :visible.sync="dialog1" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="试题题型：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="单选题"></el-radio>
                  <el-radio label="多选题"></el-radio>
                  <el-radio label="问答题"></el-radio>
                  <el-radio label="量表题"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="教师评价：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="是"></el-radio>
                  <el-radio label="否"></el-radio>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-warning m-tooltip-icon f-co"></i>
                    <div slot="content">
                      选择“是”，试题选项将读取引用此问卷模板的期别课表中课程授课教师名称，无需进行配置。（培训方案或线上课程问卷不适用此配置）
                    </div>
                  </el-tooltip>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="试题题目：" required>
                <div class="rich-text">
                  <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入试题题目" />
                </div>
              </el-form-item>
              <!--单选题-->
              <el-form-item label="试题选项：" required>
                <!--选项 A-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 A</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top" disabled></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                  <div><el-checkbox>允许填空</el-checkbox><el-checkbox>是否必填</el-checkbox></div>
                </div>
                <!--选项 B-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 B</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                  <div><el-checkbox>允许填空</el-checkbox><el-checkbox>是否必填</el-checkbox></div>
                </div>
                <!--选项 C-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 C</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                  <div><el-checkbox>允许填空</el-checkbox><el-checkbox>是否必填</el-checkbox></div>
                </div>
                <!--选项 D-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 D</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom" disabled></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                  <div><el-checkbox>允许填空</el-checkbox><el-checkbox>是否必填</el-checkbox></div>
                </div>
                <el-button type="primary" size="small" plain icon="el-icon-plus">添加选项</el-button>
              </el-form-item>
              <!--多选题-->
              <el-form-item label="试题选项：" required>
                <!--选项 A-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 A</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top" disabled></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <!--选项 B-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 B</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <!--选项 C-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 C</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom"></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <!--选项 D-->
                <div class="m-option-btn f-mb20">
                  <div class="f-flex f-align-center f-mb5">
                    <p class="f-flex-sub f-cb">选项 D</p>
                    <el-button type="primary" size="mini" plain icon="el-icon-top"></el-button>
                    <el-button type="primary" size="mini" plain icon="el-icon-bottom" disabled></el-button>
                    <el-button type="danger" size="mini" plain icon="el-icon-delete"></el-button>
                  </div>
                  <div class="rich-text">
                    <el-input type="textarea" :rows="6" v-model="form.desc" />
                  </div>
                </div>
                <el-button type="primary" size="small" plain icon="el-icon-plus">添加选项</el-button>
              </el-form-item>
              <!--引用-->
              <el-form-item label="试题选项：" required>
                <span class="f-cb">选项为引用此问卷模板的方案或期别中，课程授课教师名称</span>
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">保存</el-button>
            <el-button type="primary">保存并继续添加</el-button>
          </div>
        </el-drawer>
        <!--添加试题-问答题-->
        <el-button @click="dialog2 = true" type="primary" class="f-mr20 f-mb20">添加试题-问答题</el-button>
        <el-drawer title="添加试题" :visible.sync="dialog2" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="试题题型：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="单选题"></el-radio>
                  <el-radio label="多选题"></el-radio>
                  <el-radio label="问答题"></el-radio>
                  <el-radio label="量表题"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="试题题目：" required>
                <div class="rich-text">
                  <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入试题题目" />
                </div>
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">保存</el-button>
            <el-button type="primary">保存并继续添加</el-button>
          </div>
        </el-drawer>
        <!--添加试题-量表题-->
        <el-button @click="dialog3 = true" type="primary" class="f-mr20 f-mb20">添加试题-量表题</el-button>
        <el-drawer title="添加试题" :visible.sync="dialog3" :direction="direction" size="800px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="试题题型：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="单选题"></el-radio>
                  <el-radio label="多选题"></el-radio>
                  <el-radio label="问答题"></el-radio>
                  <el-radio label="量表题"></el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item label="试题题目：" required>
                <div class="rich-text">
                  <el-input type="textarea" :rows="6" v-model="form.desc" placeholder="请输入试题题目" />
                </div>
              </el-form-item>
              <el-form-item label="量表级数：" required>
                <el-select placeholder="请选择"></el-select>
              </el-form-item>
              <el-form-item label="量表数型：" required>
                <el-select placeholder="请选择"></el-select>
              </el-form-item>
              <el-form-item label="程度：" required>
                <el-input class="input-num"></el-input> - <el-input class="input-num"></el-input>
              </el-form-item>
              <el-form-item label="起始数值：" required>
                <el-input-number v-model="num" @change="handleChange" :min="1" :max="10"></el-input-number>
              </el-form-item>
            </el-form>
          </div>
          <div class="drawer-ft m-btn-bar">
            <el-button>取消</el-button>
            <el-button type="primary">保存</el-button>
            <el-button type="primary">保存并继续添加</el-button>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        num: 100,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        activeNames: ['1'],
        props: { multiple: true },
        radio: 3,
        checked: false,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: false,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog01: false,
        dialog02: false,
        dialog03: false,
        dialog04: false,
        dialog05: false,
        dialog06: false,
        dialog07: false,
        dialog08: false,
        dialog09: false,
        dialog001: false,
        dialog002: false,
        dialog003: false,
        dialog004: false,
        dialog005: false,
        dialog0001: false,
        dialog0002: false,
        dialog0003: false,
        dialog0004: false,
        dialog0005: false,
        dialog00001: false,
        dialog00002: false,
        dialog00003: false,
        dialog00004: false,
        dialog00005: false,
        dialog000001: false,
        dialog000002: false,
        dialog000003: false,
        dialog000004: false,
        dialog000005: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      open3() {
        this.$message({
          message: '本次修改内容如涉及到考核重算，重算任务于程序后台自动执行，即将自动为您跳转到方案管理列表页。',
          type: 'warning',
          duration: 5000,
          customClass: 'm-message'
        })
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
