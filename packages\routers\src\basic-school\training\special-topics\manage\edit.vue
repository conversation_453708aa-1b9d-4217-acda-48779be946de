<route-params content="/:id"></route-params>
<route-meta>
{
"isMenu": true,
"hideMenu": true,
"title": "专题管理"
}
</route-meta>
<script lang="ts">
  import SpecialTopicsManageEdit from '@hbfe/jxjy-admin-specialTopics/src/manage/edit.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    //TODO
    topicsEdit: [WXGLY],
    basicEdit: [WXGLY],
    portalEdit: [WXGLY],
    trainingEdit: [WXGLY],
    // 精品课程
    premiunCourse: [WXGLY],
    //集体报名
    onlineUnitApply: [WXGLY],
    offlineUnitApply: [WXGLY],
    // 有分类
    hasClassify: [WXGLY],
    // 无分类
    noClassified: [WXGLY],
    // 添加弹窗
    addDrawer: [WXGLY],
    // 权限
    query: [WXGLY],
    add: [WXGLY],
    remove: [WXGLY],
    update: [WXGLY],
    save: [WXGLY]
  })
  export default class extends SpecialTopicsManageEdit {}
</script>
