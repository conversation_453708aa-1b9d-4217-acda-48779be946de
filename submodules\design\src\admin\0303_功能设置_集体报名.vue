<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="注册登录" name="first">详见 0301_功能设置_注册登录.vue</el-tab-pane>
      <el-tab-pane label="集体报名" name="second">
        <div class="f-p15">
          <el-tabs v-model="activeName2" type="card" class="m-tab-card">
            <el-tab-pane label="线上集体报名" name="first">
              <el-card shadow="never" class="m-card f-mb15">
                <el-row type="flex" justify="center" class="width-limit">
                  <el-col :md="20" :lg="16" :xl="13">
                    <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                      <el-form-item label="线上集体报名入口：" required>
                        <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                        <el-tooltip effect="dark" placement="right" popper-class="m-tooltip">
                          <i class="el-icon-info m-tooltip-icon f-co f-ml15"></i>
                          <div slot="content">当前配置仅针对网校，专题线上集体报名入口配置请前往专题编辑页面。</div>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="线上集体报名入口图片：" required>
                        <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                        <el-tooltip effect="dark" placement="right" popper-class="m-tooltip">
                          <i class="el-icon-info m-tooltip-icon f-co f-ml15"></i>
                          <div slot="content">
                            当前配置仅针对网校门户线上集体报名入口图片的显示，专题线上集体报名请前往专题编辑页面。
                          </div>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="线上集体报名入口图片：">
                        <el-upload
                          action="#"
                          list-type="picture-card"
                          :auto-upload="false"
                          class="m-pic-upload long-pic"
                        >
                          <div slot="default" class="upload-placeholder">
                            <i class="el-icon-plus"></i>
                            <p class="txt">上传线上集体报名入口图片</p>
                          </div>
                          <div slot="file" slot-scope="{ file }" class="img-file">
                            <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                            <div class="el-upload-list__item-actions">
                              <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                <i class="el-icon-zoom-in"></i>
                              </span>
                              <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                                <i class="el-icon-delete"></i>
                              </span>
                            </div>
                          </div>
                          <div slot="tip" class="el-upload__tip">
                            <i class="el-icon-warning"></i>
                            <span class="txt">
                              <!--默认模板：只有一个入口 1186px * 150px，有两个入口 589px * 150px-->
                              <!--模板01：只有一个入口 1200px * 100px，有两个入口 860px * 100px-->
                              <!--模板02：只有一个入口 1200px * 100px，有两个入口 590px * 100px-->
                              上传线上集体报名入口图片，尺寸：1186px * 150px。
                              <i class="f-link" @click="dialog3 = true">查看示例图片</i>
                            </span>
                            <!--示例图片弹窗-->
                            <el-dialog :visible.sync="dialog3" width="1100px" class="m-dialog-pic">
                              <!--分别读取对应的默认图片-->
                              <img src="./assets/images/demo-pic-process.jpg" alt="" />
                            </el-dialog>
                          </div>
                        </el-upload>
                        <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                          <img :src="dialogImageUrl" alt="" />
                        </el-dialog>
                      </el-form-item>
                      <!--<el-form-item label="是否在门户展示入口：" required>-->
                      <!--  <el-radio-group v-model="radio">-->
                      <!--    <el-radio :label="3">展示</el-radio>-->
                      <!--    <el-radio :label="6">不展示</el-radio>-->
                      <!--  </el-radio-group>-->
                      <!--</el-form-item>-->
                      <el-form-item label="线上集体报名模板：" required>
                        <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false" class="form-l">
                          <el-button slot="trigger" type="primary" plain>点击上传模板</el-button>
                        </el-upload>
                        <div class="el-upload__tip f-pt5">
                          <div class="txt"><i class="f-link">下载示例模版</i></div>
                        </div>
                      </el-form-item>
                      <el-form-item label="查看报名班级链接：">
                        https:tar.59iedu.com
                        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                          <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
                          <div slot="content">点击复制链接</div>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item class="m-btn-bar">
                        <el-button>取消</el-button>
                        <el-button type="primary">保存</el-button>
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="线下集体报名" name="second">
              <el-card shadow="never" class="m-card f-mb15">
                <el-row type="flex" justify="center" class="width-limit">
                  <el-col :md="20" :lg="16" :xl="13">
                    <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                      <el-form-item label="线下集体报名入口：" required>
                        <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                        <el-tooltip effect="dark" placement="right" popper-class="m-tooltip">
                          <i class="el-icon-info m-tooltip-icon f-co f-ml15"></i>
                          <div slot="content">当前配置仅针对网校，专题线下集体报名入口配置请前往专题编辑页面。</div>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="线下集体报名入口图片：">
                        <el-upload
                          action="#"
                          list-type="picture-card"
                          :auto-upload="false"
                          class="m-pic-upload long-pic"
                        >
                          <div slot="default" class="upload-placeholder">
                            <i class="el-icon-plus"></i>
                            <p class="txt">上传线下集体报名入口图片</p>
                          </div>
                          <div slot="file" slot-scope="{ file }" class="img-file">
                            <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                            <div class="el-upload-list__item-actions">
                              <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                                <i class="el-icon-zoom-in"></i>
                              </span>
                              <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                                <i class="el-icon-delete"></i>
                              </span>
                            </div>
                          </div>
                          <div slot="tip" class="el-upload__tip">
                            <i class="el-icon-warning"></i>
                            <span class="txt">
                              <!--默认模板：只有一个入口 1186px * 150px，有两个入口 589px * 150px-->
                              <!--模板01：只有一个入口 1200px * 100px，有两个入口 324px * 100px-->
                              <!--模板02：只有一个入口 1200px * 100px，有两个入口 590px * 100px-->
                              上传线下集体报名入口图片，尺寸：1186px * 150px。
                              <i class="f-link" @click="dialog3 = true">查看示例图片</i>
                            </span>
                            <!--示例图片弹窗-->
                            <el-dialog :visible.sync="dialog3" width="1100px" class="m-dialog-pic">
                              <!--分别读取对应的默认图片-->
                              <img src="./assets/images/demo-pic-process.jpg" alt="" />
                            </el-dialog>
                          </div>
                        </el-upload>
                        <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                          <img :src="dialogImageUrl" alt="" />
                        </el-dialog>
                      </el-form-item>
                      <el-form-item label="线下集体报名名称：" required>
                        <el-input v-model="form.name1" clearable />
                      </el-form-item>
                      <el-form-item label="线上集体报名模板：" required>
                        <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false" class="form-l">
                          <el-button slot="trigger" type="primary" plain>点击上传模板</el-button>
                        </el-upload>
                        <div class="el-upload__tip f-pt5">
                          <div class="txt"><i class="f-link">下载示例模版</i></div>
                        </div>
                      </el-form-item>
                      <el-form-item label="访问链接：">
                        https:tar.59iedu.com/index
                        <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                          <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
                          <div slot="content">点击复制链接</div>
                        </el-tooltip>
                      </el-form-item>
                      <el-form-item label="底部文本说明：" required>
                        <el-input
                          v-model="form.name"
                          clearable
                          placeholder="填写集体报名联系电话，如：如报名过程有问题，请咨询服务热线：968823"
                        />
                      </el-form-item>
                      <el-form-item label="报名步骤：" required>
                        <div class="step f-mb20">
                          <div class="f-flex f-align-center">
                            <div class="f-flex-sub">
                              <span class="f-cb f-fb"><i class="f-dot f-mr5"></i>第一步</span>
                              <el-input
                                v-model="form.name2"
                                clearable
                                placeholder="请输入步骤的标题"
                                class="form-l f-ml10"
                              />
                            </div>
                            <el-button size="mini" type="danger" plain class="f-fr">删除</el-button>
                          </div>
                          <div class="rich-text f-mt10">
                            <el-input type="textarea" :rows="4" v-model="form.desc" placeholder="请输入内容" />
                          </div>
                        </div>
                        <div class="step f-mb20">
                          <div class="f-flex f-align-center">
                            <div class="f-flex-sub">
                              <span class="f-cb f-fb"><i class="f-dot f-mr5"></i>第二步</span>
                              <el-input
                                v-model="form.name3"
                                clearable
                                placeholder="请输入步骤的标题"
                                class="form-l f-ml10"
                              />
                            </div>
                            <el-button size="mini" type="danger" plain class="f-fr">删除</el-button>
                          </div>
                          <div class="rich-text f-mt10">
                            <el-input type="textarea" :rows="4" v-model="form.desc" placeholder="请输入内容" />
                          </div>
                        </div>
                        <div class="step f-mb20">
                          <div class="f-flex f-align-center">
                            <div class="f-flex-sub">
                              <span class="f-cb f-fb"><i class="f-dot f-mr5"></i>第三步</span>
                              <el-input
                                v-model="form.name4"
                                clearable
                                placeholder="请输入步骤的标题"
                                class="form-l f-ml10"
                              />
                            </div>
                            <el-button size="mini" type="danger" plain class="f-fr">删除</el-button>
                          </div>
                          <div class="rich-text f-mt10">
                            <el-input type="textarea" :rows="4" v-model="form.desc" placeholder="请输入内容" />
                          </div>
                        </div>
                        <div class="step f-mb20">
                          <div class="f-flex f-align-center">
                            <div class="f-flex-sub">
                              <span class="f-cb f-fb"><i class="f-dot f-mr5"></i>第四步</span>
                              <el-input
                                v-model="form.name5"
                                clearable
                                placeholder="请输入步骤的标题"
                                class="form-l f-ml10"
                              />
                            </div>
                            <el-button size="mini" type="danger" plain class="f-fr">删除</el-button>
                          </div>
                          <div class="rich-text f-mt10">
                            <el-input type="textarea" :rows="4" v-model="form.desc" placeholder="请输入内容" />
                          </div>
                        </div>
                        <el-button type="primary" plain icon="el-icon-plus">添加步骤</el-button>
                      </el-form-item>
                    </el-form>
                  </el-col>
                </el-row>
              </el-card>
              <div class="m-btn-bar f-tc is-sticky-1">
                <el-button>取消</el-button>
                <el-button>预览</el-button>
                <el-button type="primary">保存</el-button>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="third">详见 0304_功能设置_增值税发票.vue</el-tab-pane>
      <el-tab-pane label="培训证明" name="fourth">详见 0305_功能设置_培训证明.vue</el-tab-pane>
      <el-tab-pane label="视频播放设置" name="five">详见 0306_功能设置_视频播放设置.vue</el-tab-pane>
      <el-tab-pane label="门户精品课程" name="six">详见 0307_功能设置_门户精品课程.vue</el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'second',
        activeName1: 'first',
        activeName2: 'first',
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          name1: '平台名称集体报名说明',
          name2: '请下载 “ XXX单位学员报名及开票信息表 ” 模板',
          name3: '汇款至xxxxx有限公司对公账户',
          name4: '发送学员及开票信息表和汇款凭证',
          name5: '完成集体报名',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
