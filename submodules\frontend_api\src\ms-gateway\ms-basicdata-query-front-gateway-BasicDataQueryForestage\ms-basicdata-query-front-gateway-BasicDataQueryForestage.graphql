"""独立部署的微服务,K8S服务名:ms-basicdata-query-front-gateway-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""根据服务商(网校id)、行业id、行业属性类别（业务、人员类别）查询行业属性id
		@param industryPropertyInfoQueryRequest 查询条件
		@return 行业属性id
	"""
	findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType(industryPropertyInfoQueryRequest:IndustryPropertyInfoQueryRequest):[IndustryPropertyInfoResponse] @optionalLogin
	"""获取地区专题用户信息
		@param regionCode 地区code（必填）
	"""
	getAreaTrainingChannelStudentInfoInMyself(regionCode:String):AreaTrainingChannelInfoResponse
	"""获取指定门户下的获取轮播图集合
		@param portalId 门户id
		@return 轮播图集合响应
	"""
	getBannerListByPortalId(portalId:String):BannerInfoListResponse @optionalLogin
	"""获取轮播图集合
		@param portalType 门户类型
		@return 轮播图集合响应
	"""
	getBannerListByPortalType(portalType:Int!):BannerInfoListResponse @optionalLogin
	"""获取轮播图集合
		-分销平台使用 由于分销平台取上下文的方法与其他项目不一致
		@param portalType 门户类型
		@return 轮播图集合响应
	"""
	getBannerListByPortalTypeForFxpt(portalType:Int!):BannerInfoListResponse @optionalLogin
	"""根据id查询业务地区
		@param id 业务地区编码
		@return 业务地区
	"""
	getBusinessRegionById(id:String!):BusinessRegionResponse @optionalLogin
	"""根据地区编码和业务id查找一条地区
		@param request 请求参数对象
		@return 业务地区
	"""
	getBusinessRegionTree(request:BusinessRegionTreeQueryRequest!):BusinessTreeRegionResponse @optionalLogin
	"""获取网校合约客户端信息"""
	getClientInfoByServicerId(servicerId:String):[ClientInfoResponse] @optionalLogin
	"""功能描述：获取当前登录的集体缴费管理员信息
		描述：查询当前登录的集体缴费管理员信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.collective.CollectiveInfoResponse
		@date : 2022/4/2 14:21
	"""
	getCollectiveInfoInMyself:CollectiveInfoResponse
	"""查询资讯展示详细
		@param commonRequest 通用查询条件，与查询列表相同的查询条件
		@return NewsDetailWithPreviousAndNext 详细资讯信息及上下资讯id
	"""
	getCommonNewsDetailWithPreviousAndNext(commonRequest:NewsWithPreviousAndNextCommonRequest):NewsDetailWithPreviousAndNextCommonResponse @optionalLogin
	"""查询资讯展示详细 -- 分销专属
		@param commonRequest 通用查询条件，与查询列表相同的查询条件
		@return NewsDetailWithPreviousAndNext 详细资讯信息及上下资讯id
	"""
	getCommonNewsDetailWithPreviousAndNextInDistributor(commonRequest:NewsWithPreviousAndNextCommonInDistributorRequest):NewsDetailWithPreviousAndNextCommonResponse @optionalLogin
	"""获取指定门户下的友情链接集合
		@param portalId 门户id
		@return 友情链接集合响应
	"""
	getFriendLinkListByPortalId(portalId:String):FriendLinkListResponse @optionalLogin
	"""获取友情链接集合
		@param portalType 门户类型
		@return 友情链接集合响应
	"""
	getFriendLinkListByPortalType(portalType:Int!):FriendLinkListResponse @optionalLogin
	"""查询指定行业信息
		@param industryId 行业编号
		@return 行业信息
	"""
	getIndustryInfo(industryId:String!):IndustryResponse @optionalLogin
	"""查询指定行业信息
		@param industryId 行业编号
		@return 行业信息
	"""
	getIndustryInfoV2(industryId:String!):IndustryResponse @optionalLogin
	"""查询行业属性 - 平台级
		@param industryPropertyId 行业培训属性ID
	"""
	getIndustryPropertyByIdInSubProject(industryPropertyId:String):IndustryPropertyResponse @optionalLogin
	"""查询行業类型"""
	getIndustryType(businessId:String,code:String):GetIndustryTypeResponse @optionalLogin
	"""查询职称等级信息
		@param levelId 职称等级编号
		@return 职称等级信息
	"""
	getLeaderPositionLevel(levelId:String!):LeaderPositionLevelResponse @optionalLogin
	"""获取栏目信息集合
		@param portalType 栏目类型
		@return 栏目信息集合响应
	"""
	getMenusByPortalType(portalType:Int!):MenuInfoListResponse @optionalLogin
	"""根据分类id获取顶级分类信息
		@param rootCategoryCode 顶级分类代码
		@param code 分类代码
		@return
	"""
	getNewsCategoryId(rootCategoryCode:String!,code:String!):NewsCategoryResponse @optionalLogin
	"""查询资讯展示详细
		@param newId 资讯id
		@return NewsDetailWithPreviousAndNext 详细资讯信息及上下资讯id
	"""
	getNewsDetailWithPreviousAndNext(newId:String!,needAfterPublishTime:Boolean):NewsDetailWithPreviousAndNext @optionalLogin
	"""根据id获取物理地区信息
		@param id 物理地区编号
		@return 物理地区信息
	"""
	getPhysicalRegionById(id:String!):PhysicalRegionResponse @optionalLogin
	"""功能描述：项目级-根据门户ID查询门户信息-详情接口
		@param portalId                : 门户id
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.PortalResponse
		@Author： wtl
		@Date： 2022/10/19 10:32
	"""
	getPortalInfoInSubProject(portalId:String):PortalResponse @optionalLogin
	"""@param type 服务类型 0或1
		@return 服务地区列表
		查询服务地区
		@Date 2023/6/14 17:57
	"""
	getServiceOrIndustryRegion(type:Int!):[RegionResponse] @optionalLogin
	"""@description: 查询服务地区（这里只是对原方法进行二次筛选）
		@author: linq
		@date: 2023/8/22 15:54
	"""
	getServiceOrIndustryRegionByQuery(request:ServiceOrIndustryRegionQueryRequest):[RegionResponse] @optionalLogin
	"""功能描述 :获取我的学员信息接口
		描述：查询当前登录学员的详细信息
		@param dataFetchingEnvironment :
		@return : com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse
		@date : 2022/3/31 16:54
	"""
	getStudentInfoInMyself:StudentInfoResponse
	"""查询指定培训类别信息
		@param categoryId 培训类别编号
		@return 培训类别信息
	"""
	getTrainingCategoryInfo(categoryId:String!):TrainingCategoryResponse @optionalLogin
	"""查询专题资讯展示详细"""
	getTrainingChannelCommonNewsDetailWithPreviousAndNext(commonRequest:TrainingChannelNewsWithPreviousAndNextCommonRequest):NewsDetailWithPreviousAndNextCommonResponse @optionalLogin
	"""上下文服务商id、门户类型获取门户信息
		@param portalType 门户类型(包含富文本信息) 1-WEB 2-移动端
		@param servicerId 服务商ID
		@return 门户信息
	"""
	getTrainingInstitutionPortalInfo(portalType:Int!,servicerId:String):PortalInfoResponse1 @optionalLogin
	"""查询单位类型"""
	getUnitType(businessId:String,code:String):GetUnitTypeResponse @optionalLogin
	"""根据年度编号查询业务年度
		@param yearId 年度编号
		@return 年度信息
	"""
	getYearById(yearId:String!):BusinessYearResponse @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
		这个口不会去查业务那边的配置
		@param categoryCode 行业属性分类代码
		@return 培训属性列表
	"""
	listALLIndustryPropertyRootByCategory(industryId:String!,categoryCode:String!):[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表V2
		这个口不会去查业务那边的配置
	"""
	listALLIndustryPropertyRootByCategoryV2(request:TrainingPropertyCommonRequest):[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业属性编号下的所有培训属性列表"""
	listAllIndustryProperty(industryPropertyId:String!,industryId:String!):[TrainingPropertyResponse] @optionalLogin
	"""根据下级字典获取上级字典信息"""
	listBusinessDictionaryAcrossTypeBySalveId(salveId:String!):[BusinessDictionaryAcrossTypeResponse] @optionalLogin
	"""获取指定业务地区的下一级业务地区列表
		@param id 业务地区编号
		@return 业务地区
	"""
	listBusinessRegionChildById(id:String!):[BusinessRegionResponse] @optionalLogin
	"""根据id列表查询业务地区
		@param idList 业务地区编号，最大支持200个
		@return 业务地区
	"""
	listBusinessRegionListById(idList:[String]!):[BusinessRegionResponse] @optionalLogin
	"""批量查询指定路径下行政区划名称
		@param pathList 行政区划路径，格式：/350000/350100/350101
		@return 路径下行政区划名称
	"""
	listBusinessRegionNameMap(pathList:[String]!):[BusinessTreeRegionNameMap] @optionalLogin
	"""查找指定业务代码的地区树结构的指定父节点下的子节点
		@param request 请求参数对象
		@return 业务地区
	"""
	listBusinessRegionTreeChild(request:BusinessRegionTreeQueryChildrenRequest!):[BusinessTreeRegionResponse] @optionalLogin
	"""查找指定业务代码的地区树结构的根节点
		@param request 请求参数对象
		@return 业务地区
	"""
	listBusinessRegionTreeRoot(request:BusinessRegionTreeQueryRootRequest!):[BusinessTreeRegionResponse] @optionalLogin
	listChildNewsCategory(status:Int!,necId:String!):[NewsCategoryResponse] @optionalLogin
	listChildNewsCategoryInServicer(status:Int!,necId:String!):[NewsCategoryResponse] @optionalLogin
	"""查询学历字典"""
	listEducationProperty:[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业信息
		@param industryIds 行业编号，最大支持200个
		@return 行业信息
	"""
	listIndustryInfo(industryIds:[String]!):[IndustryResponse] @optionalLogin
	"""查询指定行业信息  v2
		@param industryIds 行业编号，最大支持200个
		@return 行业信息
	"""
	listIndustryInfoV2(industryIds:[String]!):[IndustryResponse] @optionalLogin
	"""查询指定查询条件下的培训属性信息列表
		@param request 查询条件
		@return 培训属性信息
		<AUTHOR>
	"""
	listIndustryPropertyByOnlineSchool(request:SchoolTrainingPropertyQueryRequest!):[TrainingPropertyResponse] @optionalLogin
	"""查询指定查询条件下的培训属性信息列表
		@param request 查询条件
		@return 培训属性信息
		<AUTHOR>
	"""
	listIndustryPropertyByOnlineSchoolV2(request:SchoolTrainingPropertyQueryRequest!):[TrainingPropertyResponse] @optionalLogin
	"""查询行业属性编号下行业属性分类列表
		@param industryPropertyId 行业属性编号
		@return 行业属性分类列表
	"""
	listIndustryPropertyCategory(industryPropertyId:String!):[IndustryPropertyCategoryResponse] @optionalLogin
	"""查询行业属性编号下行业属性分类列表
		@param industryPropertyId 行业属性编号
		@return 行业属性分类列表
	"""
	listIndustryPropertyCategoryV2(industryPropertyId:String!):[IndustryPropertyCategoryResponse] @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下指定培训属性编号下子节点培训属性列表
		@param request 查询条件
		@return 培训属性列表
	"""
	listIndustryPropertyChildByCategory(request:TrainingPropertyQueryRequest!):[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下指定培训属性编号下子节点培训属性列表
		@param request 查询条件
		@return 培训属性列表
	"""
	listIndustryPropertyChildByCategoryV2(request:TrainingPropertyQueryRequest!):[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
		@param industryPropertyId 行业属性编号
		@param categoryCode       行业属性分类代码
		@return 培训属性列表
	"""
	listIndustryPropertyRootByCategory(industryPropertyId:String!,categoryCode:String!):[TrainingPropertyResponse] @optionalLogin
	"""查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表
		@param industryPropertyId 行业属性编号
		@param categoryCode       行业属性分类代码
		@return 培训属性列表
	"""
	listIndustryPropertyRootByCategoryV2(industryPropertyId:String!,industryId:String!,categoryCode:String!):[TrainingPropertyResponse] @optionalLogin
	"""查询行业类型子节点列表"""
	listIndustryTypeChild(businessId:String,parentId:String):[IndustryTypeResponse] @optionalLogin
	"""查询行业类型父节点列表
		@param businessId
	"""
	listIndustryTypeRoot(businessId:String):[IndustryTypeResponse] @optionalLogin
	"""查询职称等级信息
		@param levelIds 职称等级编号，最大支持200个
		@return 职称等级信息
	"""
	listLeaderPositionLevel(levelIds:[String]!):[LeaderPositionLevelResponse] @optionalLogin
	"""查询根节点的职称等级列表
		@return 职称等级列表
	"""
	listLeaderPositionLevelRoot:[LeaderPositionLevelResponse] @optionalLogin
	listNewsCategoryTree(status:Int!,parentId:String):[NewsCategoryTreeResponse] @optionalLogin
	"""获取指定物理地区的下一级业务地区列表
		@param id 物理地区编号
		@return 物理地区
	"""
	listPhysicalRegionChildById(id:String!):[PhysicalRegionResponse] @optionalLogin
	"""根据id列表查询物理地区
		@param idList 物理地区编号，最大支持200个
		@return 物理地区
	"""
	listPhysicalRegionListById(idList:[String]!):[PhysicalRegionResponse] @optionalLogin
	"""查询弹窗公告列表
		@param topNum top数量,在1~50之间
		@return 资讯信息列表
	"""
	listPopUpsNews(topNum:Int!,needAfterPublishTime:Boolean):[NewsInfoResponse] @optionalLogin
	"""查询指定门户的弹窗公告列表
		@param topNum top数量,在1~50之间
		@param needAfterPublishTime 是否需要过滤发布时间在当前时间之后的数据
		@param portalId 门户id
		@return 资讯信息列表
	"""
	listPopUpsNewsByPortalId(topNum:Int!,needAfterPublishTime:Boolean,portalId:String):[NewsInfoResponse] @optionalLogin
	"""查询浏览数最多的资讯列表
		@param topNum top数量,在1~50之间
		@return 资讯信息列表
	"""
	listReviewTopNews(topNum:Int!,needAfterPublishTime:Boolean):[NewsInfoResponse] @optionalLogin
	listRootNewsCategory(status:Int!):[NewsCategoryResponse] @optionalLogin
	"""根据主字典ID获取所有从字典 （用于上下级关系的字典类型）
		@param masterId
		@param salveDictionaryType
		@return
	"""
	listSalveDictionaryByMasterId(masterId:String!,salveDictionaryType:String!):[TrainingPropertyResponse] @optionalLogin
	"""查询当前网校合约下的培训属性"""
	listServicerContractPropertyByCategory(request:ServicerContractPropertyByCategoryRequest):[TrainingPropertyResponse] @optionalLogin
	"""批量查询用户信息，不含敏感字段。目前只有头像和姓名"""
	listStudentInfoByUserIdInServicer(userIdList:[String]):[StudentInfoResponse] @optionalLogin
	"""查询最多资讯的资讯分类列表
		@param necId  排除的资讯分类id
		@param topNum top数量,在1~50之间
		@return NewsCategoryResponse 资讯分类信息
	"""
	listTopNewsCategory(necId:String!,topNum:Int!):[NewsCategoryResponse] @optionalLogin
	"""查询指定行业下的培训类别子节点列表
		@param industryId 行业编号
		@param categoryId 培训类别编号
		@return 培训类别列表
	"""
	listTrainingCategoryChild(industryId:String!,categoryId:String!):[TrainingCategoryResponse] @optionalLogin
	"""查询指定培训类别信息
		@param categoryIds 培训类别编号，最大支持200个
		@return 培训类别信息
	"""
	listTrainingCategoryInfo(categoryIds:[String]!):[TrainingCategoryResponse] @optionalLogin
	"""查询指定行业下的根节点培训类别
		@param industryId 行业编号
		@return 培训类别列表
	"""
	listTrainingCategoryRoot(industryId:String!):[TrainingCategoryResponse] @optionalLogin
	"""查询专题弹窗公告列表"""
	listTrainingChannelPopUpsNews(topNum:Int!,needAfterPublishTime:Boolean,specialSubjectId:String):[NewsInfoResponse] @optionalLogin
	"""查询浏览数最多的专题资讯列表"""
	listTrainingChannelReviewTopNews(request:TrainingChannelReviewTopNewsCommonRequest):[NewsInfoResponse] @optionalLogin
	"""查询单位类型子节点列表"""
	listUnitTypeChild(businessId:String,parentId:String):[UnitTypeResponse] @optionalLogin
	"""查询单位类型父节点列表"""
	listUnitTypeRoot(businessId:String):[UnitTypeResponse] @optionalLogin
	"""根据年度编号列表查询业务年度
		@param yearIdList 业务年度编号,最大支持200个
		@return 业务年度列表
	"""
	listYearListById(yearIdList:[String]!):[BusinessYearResponse] @optionalLogin
	"""分页查询指定行业属性编号下指定行业属性分类下根节点的培训属性列表V2"""
	pageALLIndustryPropertyRootByCategoryV2(page:Page,request:TrainingPropertyCommonRequest):TrainingPropertyResponsePage @page(for:"TrainingPropertyResponse") @optionalLogin
	"""查询资讯分页列表-通用
		@param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
		@return 资讯分页列表信息
	"""
	pageCommonSimpleNewsByPublish(request:NewsQueryCommonRequest,page:Page):SimpleNewsByPublishCommonResponsePage @page(for:"SimpleNewsByPublishCommonResponse") @optionalLogin
	"""获取指定门户下的查询资讯分页列表"""
	pageCommonSimpleNewsByPublishByPortalId(request:DistributorNewsQueryCommonRequest,page:Page):SimpleNewsByPublishCommonResponsePage @page(for:"SimpleNewsByPublishCommonResponse") @optionalLogin
	"""查询资讯分页列表-通用（服务商）
		@param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
		@return 资讯分页列表信息
	"""
	pageCommonSimpleNewsByPublishInServicer(request:NewsQueryCommonRequest,page:Page):SimpleNewsByPublishCommonResponsePage @page(for:"SimpleNewsByPublishCommonResponse") @optionalLogin
	"""首页根据条件查询资讯列表
		@param newsFrontQueryRequest 首页资讯查询条件
		@param page                  分页信息
		@return
		@throws InvalidProtocolBufferException
	"""
	pageCompleteNewsByCodeList(newsFrontQueryRequest:NewsFrontQueryByCodeRequest,page:Page):CompleteNewsByPublishResponsePage @page(for:"CompleteNewsByPublishResponse") @optionalLogin
	"""首页根据顶级分类代码查询资讯列表
		@param newsFrontQueryRequest 首页资讯查询条件
		@param page                  分页信息
		@return
		@throws InvalidProtocolBufferException
	"""
	pageCompleteNewsByRootCategoryCode(newsFrontQueryRequest:NewsFrontQueryRequest,page:Page):CompleteNewsByPublishResponsePage @page(for:"CompleteNewsByPublishResponse") @optionalLogin
	"""分页查询指定行业属性编号下指定行业属性分类下的培训属性列表"""
	pageIndustryPropertyByCategoryInSubProject(page:Page,request:PageIndustryPropertyByCategoryRequest):TrainingPropertyResponsePage @page(for:"TrainingPropertyResponse") @optionalLogin
	"""分页查询行业属性 - 平台级"""
	pageIndustryPropertyInSubProject(page:Page,request:IndustryPropertyRequest):IndustryPropertyResponsePage @page(for:"IndustryPropertyResponse") @optionalLogin
	"""功能描述：项目级-分页查询门户信息-分页接口
		@param page                    : 分页对象
		@param request                 : 查询对象
		@param dataFetchingEnvironment :
		@return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.PortalResponse>
		@Author： wtl
		@Date： 2022/10/19 10:40
	"""
	pagePortalInfoInSubProject(page:Page,request:PortalRequest):PortalResponsePage @page(for:"PortalResponse") @optionalLogin
	"""查询资讯分页列表
		@param necId 资讯分类id
		@return 资讯分页列表信息
	"""
	pageSimpleNewsByPublish(necId:String!,needAfterPublishTime:Boolean,page:Page):SimpleNewsByPublishResponsePage @page(for:"SimpleNewsByPublishResponse") @optionalLogin
	"""查询资讯分页列表
		@param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
		@return 资讯分页列表信息
	"""
	pageSimpleNewsByPublishAndAreaCodePath(request:NewsQueryByAreaCodePathRequest,page:Page):SimpleNewsByPublishResponsePage @page(for:"SimpleNewsByPublishResponse") @optionalLogin
	"""查询资讯分页列表（服务商）
		@param request 信息中心资讯查询条件(资讯分类id与资讯发布的地区编码)
		@return 资讯分页列表信息
	"""
	pageSimpleNewsByPublishAndAreaCodePathInServicer(request:NewsQueryByAreaCodePathRequest,page:Page):SimpleNewsByPublishResponsePage @page(for:"SimpleNewsByPublishResponse") @optionalLogin
	"""查询资讯分页列表，可以根据排序字段进行排序
		@param request 信息中心资讯查询条件
		@return 资讯分页列表信息
	"""
	pageSimpleNewsByPublishForOrder(request:NewsQueryForOrderRequest,page:Page):SimpleNewsByPublishResponseWithReviewCountPage @page(for:"SimpleNewsByPublishResponseWithReviewCount") @optionalLogin
	"""查询专题资讯分页列表-通用"""
	pageTrainingChannelCommonSimpleNewsByPublish(request:TrainingChannelNewsQueryCommonRequest,page:Page):SimpleNewsByPublishCommonResponsePage @page(for:"SimpleNewsByPublishCommonResponse") @optionalLogin
	"""首页根据条件查询专题资讯列表"""
	pageTrainingChannelCompleteNewsByCodeList(newsFrontQueryRequest:TrainingChannelNewsFrontQueryByCodeRequest,page:Page):CompleteNewsByPublishResponsePage @page(for:"CompleteNewsByPublishResponse") @optionalLogin
	"""查询当前网校下的学员数（有Redis缓存）"""
	statisticStudentCountInServicer:Long! @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""业务区域树根结点查询"""
input BusinessRegionTreeQueryChildrenRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.BusinessRegionTreeQueryChildrenRequest") {
	"""业务id，代表使用哪一棵业务地区树"""
	businessId:String
	"""父节点Id -1时为根结点"""
	parentId:String
}
"""业务区域树根结点查询"""
input BusinessRegionTreeQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.BusinessRegionTreeQueryRequest") {
	"""业务id，代表使用哪一棵业务地区树"""
	businessId:String
	"""业务区域编码"""
	code:String
}
"""业务区域树根结点查询"""
input BusinessRegionTreeQueryRootRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.BusinessRegionTreeQueryRootRequest") {
	"""业务id，代表使用哪一棵业务地区树"""
	businessId:String
}
"""查询行业属性信息"""
input IndustryPropertyInfoQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.IndustryPropertyInfoQueryRequest") {
	"""服务商(网校id)"""
	servicerId:String
	"""行业id"""
	industryIdList:[String]
	"""行业属性类别 0-业务行业属性，1-人员行业属性"""
	propertyType:Int
}
input IndustryPropertyRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.IndustryPropertyRequest") {
	"""行业属性名称"""
	industryPropertyName:String
	"""行业id"""
	industryIdList:[String]
	"""行业属性类别 0-业务行业属性，1-人员行业属性"""
	propertyType:Int
}
input PageIndustryPropertyByCategoryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.PageIndustryPropertyByCategoryRequest") {
	"""行业属性编号"""
	industryPropertyId:String
	"""行业ID"""
	industryId:String
	"""行业属性分类代码
		TRAINING_CATEGORY
		PERSON_TRAINING_CATEGORY
		TRAINING_PROFESSIONAL
		PERSON_TRAINING_PROFESSIONAL
	"""
	categoryCode:String
	"""字典名称，模糊查询"""
	name:String
	"""是否可用，0停用1可用，不传返回全部"""
	available:Int
}
"""网校培训属性查询条件
	<AUTHOR>
	@since 2022/1/17
"""
input SchoolTrainingPropertyQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.SchoolTrainingPropertyQueryRequest") {
	"""网校编号"""
	schoolId:String
	"""行业编号"""
	industryId:String
	"""培训属性编号列表，最大支持200个"""
	propertyId:[String]
}
"""<AUTHOR> linq
	@date : 2023-08-22 11:33
	@description：查询服务地区 请求
"""
input ServiceOrIndustryRegionQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.ServiceOrIndustryRegionQueryRequest") {
	"""服务类型 0或1
		0 - 平台业务地区(PLATFORM_BUSINESS_REGION)
		1 - 培训方案地区(TRAINING_SCHEME_REGION)
	"""
	type:Int
	"""需要返回的地区级别
		0 - 全部 (自身及所有下级地区)
		1 - 仅省级
		2 - 仅市级
		3 - 仅区县级
	"""
	whichLevel:Int
	"""地区父节点code"""
	parentRegionCode:String
}
input ServicerContractPropertyByCategoryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.ServicerContractPropertyByCategoryRequest") {
	"""字典分类"""
	categoryCode:String
	"""可用状态，不传的情况下默认可用，0停用 1可用"""
	availableList:[Int]
}
"""培训属性通用查询参数"""
input TrainingPropertyCommonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.TrainingPropertyCommonRequest") {
	"""行业属性编号"""
	industryPropertyId:String
	"""行业ID"""
	industryId:String
	"""行业属性分类代码
		TRAINING_CATEGORY
		PERSON_TRAINING_CATEGORY
		TRAINING_PROFESSIONAL
		PERSON_TRAINING_PROFESSIONAL
	"""
	categoryCode:String
	"""字典名称，模糊查询"""
	name:String
	"""是否可用，0停用1可用，不传默认可用。传-1返回全部"""
	available:Int
}
"""培训属性查询条件
	<AUTHOR>
	@since 2022/1/17
"""
input TrainingPropertyQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.dictionary.TrainingPropertyQueryRequest") {
	"""行业属性编号"""
	industryPropertyId:String
	"""行业属性分类"""
	categoryCode:String
	"""培训属性编号"""
	propertyId:String
	"""培训属性编号集合
		目前只在以下接口使用，优先使用 propertyId 字段，即 propertyId 有值时，propertyIdList 入参无效
		com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.DictionaryCommonQueryResolver#listIndustryPropertyChildByCategoryV2
	"""
	propertyIdList:[String]
}
"""分销商指定推广门户资讯查询条件"""
input DistributorNewsQueryCommonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.DistributorNewsQueryCommonRequest") {
	"""门户id"""
	portalId:String
	"""资讯分类编号"""
	necId:String
	"""资讯发布的地区编码"""
	areaCodePath:String
	"""是否需要过滤发布时间在当前时间之后的数据 true-是 false-否"""
	needAfterPublishTime:Boolean
}
"""首页资讯查询条件"""
input NewsFrontQueryByCodeRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.NewsFrontQueryByCodeRequest") {
	"""资讯标题 可为空"""
	title:String
	"""资讯分类代码  必填"""
	codes:[String]
	"""顶级分类代码  必填"""
	rootCategoryCode:String
	"""是否需要过滤发布时间在当前时间之后的数据 true-是 false-否"""
	needAfterPublishTime:Boolean
}
"""首页资讯查询条件"""
input NewsFrontQueryRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.NewsFrontQueryRequest") {
	"""资讯标题 可为空"""
	title:String
	"""资讯分类编代码  可为空"""
	code:String
	"""顶级分类代码  必填"""
	rootCategoryCode:String
	"""是否需要过滤发布时间在当前时间之后的数据 true-是 false-否"""
	needAfterPublishTime:Boolean
}
"""资讯查询条件"""
input NewsQueryByAreaCodePathRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.NewsQueryByAreaCodePathRequest") {
	"""资讯分类编号"""
	necId:String!
	"""资讯发布的地区编码"""
	areaCodePath:String!
	"""是否需要过滤发布时间在当前时间之后的数据 true-是 false-否"""
	needAfterPublishTime:Boolean
}
"""资讯查询条件"""
input NewsQueryCommonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.NewsQueryCommonRequest") {
	"""资讯分类编号"""
	necId:String
	"""资讯发布的地区编码"""
	areaCodePath:String
	"""是否需要过滤发布时间在当前时间之后的数据 true-是 false-否"""
	needAfterPublishTime:Boolean
}
"""资讯查询条件，支持针对不同字段"""
input NewsQueryForOrderRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.NewsQueryForOrderRequest") {
	"""资讯分类编号"""
	necId:String!
	"""排序字段名称，格式如下
		发布时间：0
		浏览数量：1
		<p>
		若排序字段为-1，默认按照从置顶到非置顶，发布时间从新到旧顺序排列
	"""
	orderFiled:Int
	"""排序方式
		0 升序
		1 降序
	"""
	orderType:Int
	"""是否需要过滤发布时间在当前时间之后的数据 true-是 false-否"""
	needAfterPublishTime:Boolean
}
"""资讯查询条件"""
input NewsWithPreviousAndNextCommonInDistributorRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.NewsWithPreviousAndNextCommonInDistributorRequest") {
	"""资讯编号"""
	newId:String
	"""资讯发布的地区编码"""
	areaCodePath:String
	"""是否需要过滤发布时间在当前时间之后的数据 true-是 false-否"""
	needAfterPublishTime:Boolean
	"""门户id"""
	portalId:String
}
"""资讯查询条件"""
input NewsWithPreviousAndNextCommonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.NewsWithPreviousAndNextCommonRequest") {
	"""资讯编号"""
	newId:String
	"""资讯发布的地区编码"""
	areaCodePath:String
	"""是否需要过滤发布时间在当前时间之后的数据 true-是 false-否"""
	needAfterPublishTime:Boolean
}
"""首页资讯查询条件"""
input TrainingChannelNewsFrontQueryByCodeRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.TrainingChannelNewsFrontQueryByCodeRequest") {
	"""资讯标题 可为空"""
	title:String
	"""资讯分类代码  必填"""
	codes:[String]
	"""顶级分类代码  必填"""
	rootCategoryCode:String
	"""是否需要过滤发布时间在当前时间之后的数据 true-是 false-否"""
	needAfterPublishTime:Boolean
	"""专题ID集合"""
	specialSubjectIds:[String]
}
"""专题资讯查询条件"""
input TrainingChannelNewsQueryCommonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.TrainingChannelNewsQueryCommonRequest") {
	"""资讯分类编号"""
	necId:String
	"""资讯分类编号集合
		如果单个咨询分类编号与集合都有值，优先使用单个id
	"""
	necIdList:[String]
	"""资讯发布的地区编码"""
	areaCodePath:String
	"""是否需要过滤发布时间在当前时间之后的数据 true-是 false-否"""
	needAfterPublishTime:Boolean
	"""专题ID"""
	specialSubjectId:String
}
"""专题资讯查询条件"""
input TrainingChannelNewsWithPreviousAndNextCommonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.TrainingChannelNewsWithPreviousAndNextCommonRequest") {
	"""资讯编号"""
	newId:String
	"""资讯发布的地区编码"""
	areaCodePath:String
	"""是否需要过滤发布时间在当前时间之后的数据 true-是 false-否"""
	needAfterPublishTime:Boolean
	"""专题ID"""
	specialSubjectId:String
}
"""浏览数最多的专题资讯列表请求参数"""
input TrainingChannelReviewTopNewsCommonRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.news.TrainingChannelReviewTopNewsCommonRequest") {
	"""top数量,在1~50之间"""
	topNum:Int
	"""是否需要过滤发布时间在当前时间之后"""
	needAfterPublishTime:Boolean
	"""专题ID"""
	specialSubjectId:String
}
"""功能描述 : 门户持核心层查询条件
	@date : 2022年10月18日 19:53:03
"""
input PortalRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.portal.PortalRequest") {
	"""归属信息"""
	owner:PortalOwnerRequest
	"""门户信息"""
	portalInfo:PortalInfoRequest
}
"""功能描述：门户信息查询条件"""
input PortalInfoRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.portal.nested.PortalInfoRequest") {
	"""门户id集合"""
	portalId:[String]
	"""门户类型（1：web端 2：移动端）
		@see com.fjhb.domain.basicdata.api.servicer.consts.PortalTypes
	"""
	portalType:Int
	"""门户状态（0：未发布 1：已发布）"""
	status:Int
}
"""功能描述：门户归属信息查询参数
	@Author： wtl
	@Date： 2022年10月18日 19:53:52
"""
input PortalOwnerRequest @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.request.portal.nested.PortalOwnerRequest") {
	"""所属培训机构id集合"""
	trainingInstitutionIdList:[String]
}
type RegionModel @type(value:"com.fjhb.ms.basicdata.model.RegionModel") {
	regionId:String
	regionPath:String
	provinceId:String
	provinceName:String
	cityId:String
	cityName:String
	countyId:String
	countyName:String
}
type SectionAndSubjects @type(value:"com.fjhb.ms.basicdata.model.account.nested.SectionAndSubjects") {
	section:Int
	subjects:Int
}
"""功能描述：账户信息
	@Author： wtl
	@Date： 2022年5月11日 15:30:56
"""
type AccountResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.AccountResponse") {
	"""账户id"""
	accountId:String
	"""帐户类型（1：企业账户 2：企业个人账户 3：个人账户）
		@see AccountTypes
	"""
	accountType:Int
	"""单位信息"""
	unitInfo:UnitInfoResponse
	"""所属顶级企业帐户Id"""
	rootAccountId:String
	"""帐户状态 1：正常，2：冻结，3：注销
		@see AccountStatus
	"""
	status:Int
	"""注册方式
		0内置，11平台开放注册，21QQ，31新浪微博，41微信，42微信公众号，43 微信小程序，51外部来源，52闽政通,61钉钉
		@see AccountRegisterTypes
	"""
	registerType:Int
	"""来源类型
		0、内置，1、项目主网站，2、安卓，3、IOS，4、后台导入，5、迁移数据，6、分销平台项目主网站，7、专题，8、华医网，9、江西管理平台
		@see AccountSourceTypes
	"""
	sourceType:Int
	"""创建时间"""
	createdTime:DateTime
}
"""功能描述：帐户认证信息
	@Author： wtl
	@Date： 2022年5月11日 14:23:18
"""
type AuthenticationResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.AuthenticationResponse") {
	"""帐号"""
	identity:String
	"""认证标识类型
		1用户名,2手机,3身份证,4邮箱,5第三方OpenId
	"""
	identityType:Int
	"""认证方式状态 1启用，2禁用
		@see AuthenticationStatusEnum
	"""
	status:Int
}
"""功能描述：用户基础信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type UserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.UserInfoResponse") {
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
	"""工作单位名称"""
	companyName:String
	"""工作单位统一社会信用代码"""
	companyCode:String
}
"""单位信息模型"""
type UnitInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.admin.nested.UnitInfoResponse") {
	"""单位ID"""
	unitId:String
}
"""功能描述：集体缴费管理员信息
	@Author： wtl
	@Date： 2022年1月26日 10:38:15
"""
type CollectiveInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.collective.CollectiveInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""用户信息"""
	userInfo:UserInfoResponse
	"""用户认证信息"""
	authenticationList:[AuthenticationResponse]
}
"""Description:业务数据字典"""
type BusinessDictionaryAcrossTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.BusinessDictionaryAcrossTypeResponse") {
	"""字典关系id"""
	id:String
	"""主字典ID"""
	masterId:String
	"""从字典ID"""
	slaveId:String
	"""从字典字典类型|对应枚举BusinessDataDictionaryTypeEnum
		@see BusinessDataDictionaryTypeEnum
	"""
	slaveType:String
	"""初始化数据识别标志"""
	initable:Int
}
"""业务地区信息"""
type BusinessRegionResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.BusinessRegionResponse") {
	"""地区编码"""
	id:String
	"""上级地区编码"""
	parentId:String
	"""地区路径"""
	regionPath:String
	"""地区名称"""
	name:String
	"""排序"""
	sort:Int!
	"""是否启用"""
	enable:Boolean!
}
"""行政区划地区名称
	<AUTHOR>
	@since 2022/7/6
"""
type BusinessTreeRegionNameMap @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.BusinessTreeRegionNameMap") {
	"""地区路径
		格式：/350000/350100/350101
	"""
	path:String
	"""地区名称拼接
		格式：福建省/福州市/鼓楼区
	"""
	name:String
}
type BusinessTreeRegionResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.BusinessTreeRegionResponse") {
	"""id"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""行政区划"""
	code:String
	"""结点名称"""
	name:String
	"""父节点Id -1时为根结点"""
	parentId:String
	"""业务Id"""
	businessId:String
	"""业务树结构路径"""
	regionPath:String
}
"""业务年度信息"""
type BusinessYearResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.BusinessYearResponse") {
	"""业务年度编号"""
	id:String
	"""年度"""
	year:String
	"""序号"""
	sort:Int!
	"""是否启用"""
	enable:Boolean!
}
type GetIndustryTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.GetIndustryTypeResponse") {
	"""行业类型id"""
	id:String
	"""代码"""
	code:String
	"""行业类型名称"""
	industryType:String
	"""上级编号"""
	parentId:String
	"""上级代码"""
	parentCode:String
}
"""单位类型"""
type GetUnitTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.GetUnitTypeResponse") {
	"""单位类型id"""
	id:String
	"""代码"""
	code:String
	"""单位类型名称"""
	unitType:String
	"""上级编号"""
	parentId:String
	"""上级代码"""
	parentCode:String
}
"""行业属性分类信息
	<AUTHOR>
	@since 2022/1/17
"""
type IndustryPropertyCategoryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.IndustryPropertyCategoryResponse") {
	"""分类code"""
	code:String
	"""分类名称"""
	name:String
	"""序号"""
	sort:Int!
}
"""网校行业属性id集合"""
type IndustryPropertyInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.IndustryPropertyInfoResponse") {
	"""行业培训属性ID"""
	industryPropertyId:String
	"""服务商ID"""
	serviceId:String
	"""行业属性名称"""
	industryPropertyName:String
	"""排序"""
	sort:Int
	"""来源id，模板数据的值为-1，网校的值为对应的模板行业培训属性id"""
	sourceId:String
	"""行业属性类型"""
	propertyType:Int
}
type IndustryPropertyResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.IndustryPropertyResponse") {
	"""行业培训属性ID"""
	industryPropertyId:String
	"""行业属性名称"""
	industryPropertyName:String
	"""行业字典ID"""
	industryId:String
	"""行业字典名称"""
	industryName:String
	"""更新时间"""
	updateTime:DateTime
	"""创建时间"""
	createTime:DateTime
}
"""培训类别信息
	<AUTHOR>
	@since 2022/2/10
"""
type IndustryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.IndustryResponse") {
	"""行业编号"""
	id:String
	"""行业名称"""
	name:String
	"""序号"""
	sort:Int!
}
type IndustryTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.IndustryTypeResponse") {
	"""行业类型id"""
	id:String
	"""代码"""
	code:String
	"""行业类型名称"""
	industryType:String
	"""排序"""
	sort:Int!
}
"""职称等级
	<AUTHOR>
	@since 2022/2/10
"""
type LeaderPositionLevelResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.LeaderPositionLevelResponse") {
	"""职称等级编号"""
	id:String
	"""职称等级名称"""
	name:String
	"""序号"""
	sort:Int!
}
"""物理地区信息"""
type PhysicalRegionResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.PhysicalRegionResponse") {
	"""地区编码"""
	id:String
	"""上级地区编码"""
	parentId:String
	"""地区路径"""
	regionPath:String
	"""地区名称"""
	name:String
	"""序号"""
	sort:Int!
	"""是否启用"""
	enable:Boolean!
}
"""功能描述：地区字典信息
	@Author： yxw
	@Date： 2023年6月15日
"""
type RegionResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.RegionResponse") {
	"""地区编码"""
	code:String
	"""父级地区编码"""
	parentCode:String
	"""地区编码路径"""
	codePath:String
	"""地区名称"""
	name:String
	"""级别|1省级 2市级 3区县级"""
	level:Int
	"""地区排序"""
	sort:Int
}
"""培训类别信息
	<AUTHOR>
	@since 2022/2/10
"""
type TrainingCategoryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.TrainingCategoryResponse") {
	"""培训类别编号"""
	categoryId:String
	"""培训类别父级编号"""
	parentId:String
	"""培训类别名称"""
	name:String
	"""序号"""
	sort:Int!
}
"""培训属性信息
	<AUTHOR>
	@since 2022/1/17
"""
type TrainingPropertyResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.TrainingPropertyResponse") {
	"""业务侧培训属性关系主键ID"""
	propertyRelationId:String
	"""清洗侧属性字典ID"""
	propertyId:String
	"""属性名称"""
	name:String
	"""序号"""
	sort:Int!
	"""code值"""
	code:Int
	"""code值，扩展"""
	codeExt:String
	"""展示名称"""
	showName:String
	"""如果是科目类型下的属性，则该值为null"""
	parentId:String
	"""是否可用，0停用1可用"""
	available:Int
}
"""单位类型"""
type UnitTypeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.dictionary.UnitTypeResponse") {
	"""单位类型id"""
	id:String
	"""代码"""
	code:String
	"""单位类型名称"""
	unitType:String
	"""排序"""
	sort:Int!
}
"""网校前端查询的资讯信息"""
type CompleteNewsByPublishResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.CompleteNewsByPublishResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""图片路径"""
	coverPath:String
	"""分类名称"""
	name:String
	"""专题ID"""
	specialSubjectId:String
}
"""资讯分类信息"""
type NewsCategoryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.NewsCategoryResponse") {
	"""资讯分类编号"""
	newsCategoryId:String
	"""分类名称"""
	categoryName:String
	"""分类代码"""
	code:String
}
"""<AUTHOR> linq
	@date : 2025-03-31 09:54
	@description : 资讯分类信息(树形结构)
"""
type NewsCategoryTreeResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.NewsCategoryTreeResponse") {
	"""资讯分类编号"""
	newsCategoryId:String
	"""分类名称"""
	categoryName:String
	"""分类代码"""
	code:String
	"""子资讯分类"""
	children:[NewsCategoryTreeResponse]
}
"""详细资讯信息"""
type NewsDetailResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.NewsDetailResponse") {
	"""资讯编号"""
	newId:String
	"""平台id"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目id"""
	projectId:String
	"""子项目id"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	serviceId:String
	"""分类id"""
	necId:String
	"""标题"""
	title:String
	"""摘要"""
	summary:String
	"""内容"""
	content:String
	"""封面图片路径"""
	coverPath:String
	"""来源"""
	source:String
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""是否置顶"""
	isTop:Boolean!
	"""发布地区编码"""
	areaCodePath:String
	"""资讯状态 0 草稿 1正常"""
	status:Int!
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
	"""浏览数量"""
	reviewCount:Int!
	"""弹窗起始时间"""
	popupBeginTime:DateTime
	"""弹窗截止时间"""
	popupEndTime:DateTime
	"""专题ID"""
	specialSubjectId:String
}
"""详细资讯信息及上下资讯id"""
type NewsDetailWithPreviousAndNext @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.NewsDetailWithPreviousAndNext") {
	"""详细资讯信息"""
	newsDetail:NewsDetailResponse
	"""上一条资讯id"""
	previousId:String
	"""下一条资讯id"""
	nextId:String
}
"""详细资讯信息及上下资讯id"""
type NewsDetailWithPreviousAndNextCommonResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.NewsDetailWithPreviousAndNextCommonResponse") {
	"""详细资讯信息"""
	newsDetail:NewsDetailResponse
	"""上一条资讯id"""
	previousId:String
	"""下一条资讯id"""
	nextId:String
}
"""资讯信息"""
type NewsInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.NewsInfoResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""发布时间"""
	publishTime:DateTime
	"""资讯分类id"""
	necId:String
	"""专题ID"""
	specialSubjectId:String
}
"""网校前端查询的资讯信息"""
type SimpleNewsByPublishCommonResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.SimpleNewsByPublishCommonResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否置顶"""
	isTop:Boolean!
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""浏览数量"""
	reviewCount:Int!
	"""专题ID"""
	specialSubjectId:String
	"""分类编号"""
	necId:String
}
"""网校前端查询的资讯信息"""
type SimpleNewsByPublishResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.SimpleNewsByPublishResponse") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否置顶"""
	isTop:Boolean!
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
}
"""网校前端查询的资讯信息"""
type SimpleNewsByPublishResponseWithReviewCount @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.news.SimpleNewsByPublishResponseWithReviewCount") {
	"""资讯编号"""
	newId:String
	"""标题"""
	title:String
	"""是否置顶"""
	isTop:Boolean!
	"""是否弹窗公告"""
	isPopUps:Boolean!
	"""摘要"""
	summary:String
	"""发布人编号"""
	publishUserId:String
	"""发布时间"""
	publishTime:DateTime
	"""浏览数量"""
	reviewCount:Int!
}
"""门户信息返回模型"""
type PortalResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.PortalResponse") {
	"""归属信息"""
	ownerInfo:PortalOwnerInfoResponse
	"""门户信息"""
	portalInfo:PortalInfoResponse
}
"""门户轮播图"""
type PortalBannerResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.nested.PortalBannerResponse") {
	"""轮播图id"""
	bannerId:String
	"""轮播图名称"""
	name:String
	"""图片路径"""
	path:String
	"""链接地址"""
	link:String
	"""轮播图排序"""
	sort:Int
	"""是否启用"""
	isEnable:Boolean
	"""创建时间"""
	createdTime:DateTime
}
"""门户友情链接"""
type PortalFriendLinkResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.nested.PortalFriendLinkResponse") {
	"""友情链接id"""
	friendLinkId:String
	"""友情链接标题"""
	title:String
	"""友情链接图片"""
	picture:String
	"""友情链接类型（1：文本 2：图片）
		@see FriendLinkTypes
	"""
	friendLinkType:Int
	"""链接"""
	link:String
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
}
"""门户信息"""
type PortalInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.nested.PortalInfoResponse") {
	"""门户id"""
	portalId:String
	"""门户类型（1：web端 2：移动端）
		@see com.fjhb.domain.basicdata.api.servicer.consts.PortalTypes
	"""
	portalType:Int
	"""门户状态（0：未发布 1：已发布）"""
	status:Int
	"""门户标题"""
	title:String
	"""门户logo"""
	logo:String
	"""浏览器图标"""
	icon:String
	"""移动二维码"""
	mobileQRCode:String
	"""客服电话图片"""
	csPhonePicture:String
	"""客服电话"""
	csPhone:String
	"""客服咨询时间"""
	csCallTime:String
	"""在线客服代码内容id"""
	csOnlineCodeId:String
	"""在线客服代码内容"""
	csOnlineCodeContent:String
	"""培训流程图片"""
	trainingFlowPicture:String
	"""底部内容(底部落款)"""
	footContentId:String
	"""底部内容(底部落款)"""
	footContent:String
	"""友情链接集合"""
	friendLinkList:[PortalFriendLinkResponse]
	"""主题颜色"""
	themeColor:String
	"""宣传口号"""
	slogan:String
	"""门户简介说明内容Id"""
	contentId:String
	"""门户简介说明内容"""
	content:String
	"""是否提供服务号"""
	isProvideServiceAccount:Boolean
	"""域名"""
	domainName:String
	"""轮播图列表"""
	portalBannerList:[PortalBannerResponse]
	"""栏目列表"""
	portalMenuList:[PortalMenuResponse]
	"""创建时间"""
	createdTime:DateTime
}
"""栏目信息"""
type PortalMenuResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.nested.PortalMenuResponse") {
	"""栏目id"""
	menuId:String
	"""栏目名称"""
	name:String
	"""父栏目id"""
	parentId:String
	"""栏目类型（1：菜单 2：资讯）
		@see MenuTypes
	"""
	menuType:Int
	"""来源类型（1：内置 2：用户创建）
		@see MenuSourceTypes
	"""
	sourceType:Int
	"""链接"""
	link:String
	"""业务code"""
	code:String
	"""引用id(咨询类型的栏目，引用的是资讯分类id)"""
	referenceId:String
	"""是否允许添加子级"""
	allowChildren:Boolean
	"""是否启用"""
	enable:Boolean
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
}
"""功能描述：门户归属信息
	@Author： wtl
	@Date： 2022/10/19 9:18
"""
type PortalOwnerInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.portal.nested.PortalOwnerInfoResponse") {
	"""所属培训机构id"""
	trainingInstitutionId:String
}
"""功能描述：学员信息
	@Author： wtl
	@Date： 2022年1月26日 10:38:15
"""
type StudentInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.StudentInfoResponse") {
	"""账户信息"""
	accountInfo:AccountResponse
	"""学员用户信息"""
	userInfo:StudentUserInfoResponse
	"""学员人员信息"""
	personInfo:StudentPersonInfoResponse
	"""第三方绑定信息"""
	openPlatformBind:OpenPlatformBindResponse
	"""用户登录认证信息"""
	authenticationList:[AuthenticationResponse]
}
"""功能描述：附件信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type AttachmentInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.AttachmentInfoResponse") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
"""功能描述：学员绑定信息
	@Author： wtl
	@Date： 2022年5月12日 14:42:51
"""
type OpenPlatformBindResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.OpenPlatformBindResponse") {
	"""是否绑定微信"""
	bindWX:Boolean!
	"""微信昵称"""
	nickNameByWX:String
}
"""功能描述：学员证书信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentCertificateResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.StudentCertificateResponse") {
	"""证书id"""
	certificateId:String
	"""证书编号"""
	certificateNo:String
	"""证书类别"""
	certificateCategory:String
	"""注册专业"""
	registerProfessional:String
	"""主/增项 | 1-主项 2-增项
		@see com.fjhb.domain.basicdata.api.user.consts.CertificateMainAddOnTypes
	"""
	mainAddOn:String
	"""发证日期"""
	releaseStartTime:DateTime
	"""证书有效期"""
	certificateEndTime:DateTime
	"""证书附件信息"""
	attachmentList:[AttachmentInfoResponse]
}
"""功能描述：学员行业信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentIndustryResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.StudentIndustryResponse") {
	"""用户行业id"""
	userIndustryId:String
	"""行业id"""
	industryId:String
	"""一级专业类别id"""
	firstProfessionalCategory:String
	"""二级专业类别id"""
	secondProfessionalCategory:String
	"""职称等级"""
	professionalQualification:String
	"""人员类别（职业卫生行业）"""
	personnelCategory:String
	"""岗位类别（职业卫生行业）"""
	positionCategory:String
	"""技术等级（工勤行业）"""
	professionalLevel:String
	"""工种（工勤行业）"""
	jobCategoryId:String
	"""学员证书信息集合"""
	userCertificateList:[StudentCertificateResponse]
	"""教师行业 学段、学科信息"""
	sectionAndSubjects:[SectionAndSubjects]
	"""证书类型（药师行业）"""
	certificatesType:String
	"""执证类别（药师行业）"""
	practitionerCategory:String
}
type StudentPersonInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.StudentPersonInfoResponse") {
	"""学历"""
	education:String
}
"""功能描述：学员用户信息
	@Author： wtl
	@Date： 2022年1月26日 10:30:20
"""
type StudentUserInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.student.nested.StudentUserInfoResponse") {
	"""用户昵称"""
	nickName:String
	"""用户所属地区"""
	region:RegionModel
	"""头像地址"""
	photo:String
	"""联系地址"""
	address:String
	"""学员行业信息集合"""
	userIndustryList:[StudentIndustryResponse]
	"""证书技术工种Id"""
	jobCategoryId:String
	"""证书技术等级"""
	professionalLevel:Int
	"""所属工考办地区编码"""
	managementUnitRegion:RegionModel
	"""证书技术工种名称"""
	jobCategoryName:String
	"""用户工作单位所在地区"""
	companyRegion:RegionModel
	"""是否工勤人员 （0非工勤人员，1工勤人员）"""
	isWorker:String
	"""是否退休 (0非退休人员，1退休人员)"""
	isRetire:String
	"""学历"""
	education:String
	"""用户id"""
	userId:String
	"""用户名称"""
	userName:String
	"""性别
		-1未知，0女，1男
		@see Genders
	"""
	gender:Int
	"""证件类型（1：居民身份证 2：中国护照 3：外国护照 4：台湾居民来往大陆通行证 5：港澳居民来往大陆通行证 6：其他）"""
	idCardType:String
	"""证件号"""
	idCard:String
	"""手机号"""
	phone:String
	"""邮箱"""
	email:String
	"""工作单位名称"""
	companyName:String
	"""工作单位统一社会信用代码"""
	companyCode:String
}
"""地区专题信息响应体"""
type AreaTrainingChannelInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.trainingchannel.AreaTrainingChannelInfoResponse") {
	"""专题用户id"""
	trainingChannelUserId:String
	"""用户id"""
	userId:String
	"""地区id"""
	regionId:String
	"""工作单位性质 字典"""
	unitNature:String
	"""在编情况 字典"""
	staffingStatus:String
	"""是否在专技岗位工作 字典"""
	isZJPosition:String
	"""职称系列 字典"""
	titleSeries:String
	"""职称专业"""
	titleProfessional:String
	"""现有职称等级  字典"""
	titleGrade:String
	"""现有职称资格名称"""
	titleQualificationName:String
	"""现有职称有效范围  字典"""
	titleEffectiveRange:String
	"""最高学历"""
	highestEducationLevel:String
}
"""客户端信息返回"""
type ClientInfoResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.ClientInfoResponse") {
	"""客户端类型 | 1、Web 2、H5
		@see ClientTypesConstant
	"""
	clientType:Int
	"""域名类型
		@see DomainNameTypesConstant
	"""
	domainNameType:Int
	"""域名"""
	domainName:String
	"""前端模板id"""
	portalTemplateId:String
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
}
"""门户信息
	<AUTHOR>
"""
type PortalInfoResponse1 @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.PortalInfoResponse") {
	"""id"""
	id:String
	"""培训机构id"""
	servicerId:String
	"""服务商类型 | 1培训机构、2课件供应商、3渠道商、4参训单位、5合同供应商、6供应商、7分销商、8企业"""
	servicerType:Int
	"""培训机构logo"""
	institutionLogo:String
	"""门户类型"""
	portalType:Int
	"""门户标题"""
	title:String
	"""门户logo"""
	logo:String
	"""门户图标"""
	icon:String
	"""友情链接类型 1-文本  2-图片"""
	friendLinkType:Int
	"""主题颜色"""
	themeColor:String
	"""移动二维码"""
	mobileQRCode:String
	"""移动二维码来源标识
		1-系统生成 2-自定义
	"""
	mobileQRCodeSign:Int
	"""客服电话图片"""
	CSPhonePicture:String
	"""客服电话"""
	CSPhone:String
	"""客服咨询时间"""
	CSCallTime:String
	"""在线客服代码内容id"""
	CSOnlineCodeId:String
	"""培训流程图片"""
	trainingFlowPicture:String
	"""底部内容（底部落款）"""
	footContent:String
	"""宣传口号"""
	slogan:String
	"""域名"""
	domainName:String
	"""H5域名(请求Web端门户信息才会使用这个字段 如果请求的是H5端的门户信息，该字段为null)"""
	domainNameH5:String
	"""域名类型
		系统默认域名 1
		自有域名 2
		@see com.fjhb.domain.basicdata.api.servicer.consts.DomainNameTypeConsts
	"""
	domainNameType:Int
	"""门户简介说明内容"""
	content:String
	"""是否提供服务号"""
	isProvideServiceAccount:Boolean
	"""是否已发布"""
	isPublished:Boolean
	"""网校状态
		1-正常  2-失效
	"""
	onlineSchoolStatus:Int!
	"""创建时间"""
	createTime:DateTime
	"""更新时间"""
	updateTime:DateTime
	"""发布时间"""
	publishedTime:DateTime
	"""取消发布时间"""
	unpublishedTime:DateTime
	"""cnzz信息"""
	cnzz:String
	"""目录名"""
	dirName:String
	"""网校模式
		@see OnlineSchoolModesConstant
	"""
	onlineSchoolModes:Int
	"""是否到期"""
	isExpired:Boolean
	"""前端模板id"""
	portalTemplateId:String
	"""企业客服微信"""
	CSWechat:String
}
"""轮播图信息集合响应
	<AUTHOR>
"""
type BannerInfoListResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.banner.BannerInfoListResponse") {
	"""轮播图信息集合"""
	bannerInfos:[BannerInfo]
}
"""轮播图信息
	<AUTHOR>
"""
type BannerInfo @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.banner.support.BannerInfo") {
	"""id"""
	id:String
	"""所属门户id"""
	portalId:String
	"""名称"""
	name:String
	"""路径"""
	path:String
	"""链接"""
	link:String
	"""排序"""
	sort:Int
	"""是否启用"""
	isEnable:Boolean
	"""创建时间"""
	createdTime:DateTime
}
"""友情链接集合响应
	<AUTHOR>
"""
type FriendLinkListResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.friendlink.FriendLinkListResponse") {
	"""友情链接集合"""
	friendLinkInfos:[FriendLinkInfo]
}
"""友情链接信息
	<AUTHOR>
"""
type FriendLinkInfo @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.friendlink.support.FriendLinkInfo") {
	"""id"""
	id:String
	"""所属门户id"""
	portalId:String
	"""标题"""
	title:String
	"""图片"""
	picture:String
	"""友情链接类型"""
	friendLinkType:Int
	"""链接"""
	link:String
	"""排序"""
	sort:Int
	"""创建时间"""
	createdTime:DateTime
	"""更新时间"""
	updatedTime:DateTime
}
"""栏目信息集合响应
	<AUTHOR>
"""
type MenuInfoListResponse @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.menu.MenuInfoListResponse") {
	"""栏目信息集合"""
	menuInfos:[MenuInfo]
}
"""栏目信息(前台)
	<AUTHOR>
"""
type MenuInfo @type(value:"com.fjhb.ms.basicdata.query.front.gateway.jxjy.graphql.response.traininginstitution.portal.menu.support.MenuInfo") {
	"""栏目id"""
	id:String
	"""栏目类型 1-菜单  2-资讯"""
	menuType:Int
	"""来源类型 1-内置  2-用户创建"""
	sourceType:Int!
	"""栏目名称"""
	name:String
	"""父栏目id"""
	parentId:String
	"""链接"""
	link:String
	"""业务code（用于同步资讯、前端link相同的情况下做二次识别）"""
	code:String
	"""排序"""
	sort:Int
	"""引用id"""
	referenceId:String
}

scalar List
type TrainingPropertyResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [TrainingPropertyResponse]}
type SimpleNewsByPublishCommonResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SimpleNewsByPublishCommonResponse]}
type CompleteNewsByPublishResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [CompleteNewsByPublishResponse]}
type IndustryPropertyResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [IndustryPropertyResponse]}
type PortalResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [PortalResponse]}
type SimpleNewsByPublishResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SimpleNewsByPublishResponse]}
type SimpleNewsByPublishResponseWithReviewCountPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [SimpleNewsByPublishResponseWithReviewCount]}
