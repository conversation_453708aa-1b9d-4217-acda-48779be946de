import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import QueryTradeInfoConfigFactory from './QueryTradeInfoConfigFactory'
import MutationTradeInfoConfigFactory from './MutationTradeInfoConfigFactory'
@Module({
  name: 'TradeInfoConfigModule',
  dynamic: true,
  namespaced: true,
  store
})
class TradeInfoConfigModule extends VuexModule {
  /**
   * 查询交易基础配置工厂
   */
  queryTradeInfoConfigFactory: QueryTradeInfoConfigFactory = new QueryTradeInfoConfigFactory()
  /**
   * 业务交易基础配置工厂
   */
  mutationTradeInfoConfigFactory: MutationTradeInfoConfigFactory = new MutationTradeInfoConfigFactory()
}
export default getModule(TradeInfoConfigModule)
