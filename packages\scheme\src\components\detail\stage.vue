<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div slot="header" class="">
      <span class="tit-txt">学习有效期配置</span>
    </div>
    <div class="m-sub-tit is-border-bottom">
      <span class="tit-txt">学习时间配置</span>
    </div>
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form ref="form" label-width="150px" class="m-text-form is-column f-mt20">
          <el-form-item label="学习起止时间：">
            {{ getTrainDate(schemeDetail) }}
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div class="m-sub-tit is-border-bottom">
      <span class="tit-txt">销售配置</span>
    </div>
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form ref="form" label-width="150px" class="m-text-form is-column f-mt20">
          <el-form-item label="展示在门户：" v-if="isCustomerVisible">
            展示
            <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
              <i class="el-icon-info m-tooltip-icon f-c9"></i>
              <div slot="content">
                展示门户分为学员门户报名列表和集体报名管理员查看培训班，设置为集体报名管理员可见，只生效于有开启线上集体报名的网校。
              </div>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="展示在门户：" v-if="!isCustomerVisible"> 不展示门户 </el-form-item>
          <el-form-item label="展示用户：" v-if="isCustomerVisible">
            {{ getVisibleChannelList(schemeDetail) }}
          </el-form-item>
          <el-form-item label="开放学员报名：" v-if="!openCustomerPurchase && isOnline">
            不开放
            <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
              <i class="el-icon-info m-tooltip-icon f-c9"></i>
              <div slot="content">不开放学员报名，仅支持导入开通的方式报班。</div>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="开放学员报名：" v-if="openCustomerPurchase && isOnline"> 开放 </el-form-item>
          <!-- <el-form-item label="开启报名时间：">{{ getRegisterDateInfo() }}</el-form-item> -->
          <el-form-item label="方案上架状态：">
            {{ schemeDetail.onShelves ? '立即上架' : '暂不开启' }}
          </el-form-item>
          <el-form-item label="方案计划上架时间：">
            {{ getSchemeDate(schemeDetail, 'onShelvesPlanTime', '-') }}
          </el-form-item>
          <el-form-item label="方案计划下架时间：">
            {{ getSchemeDate(schemeDetail, 'offShelvesPlanTime', '-') }}
          </el-form-item>
          <el-form-item label="配置开启报名时间：" v-show="isOpenSignUp" v-if="isOnline">
            {{ getDate(schemeDetail, 'registerBeginDate', '立即开启') }}
          </el-form-item>
          <el-form-item label="关闭报名时间：" v-show="isOpenSignUp" v-if="isOnline">
            {{ getDate(schemeDetail, 'registerEndDate', '无关闭时间') }}
          </el-form-item>
          <el-form-item
            label="期别报名信息："
            required
            v-if="
              schemeDetail.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId !== TrainingModeEnum.online
            "
          >
            本方案下已添加{{ schemeDetail.learningTypeModel.issue.issueConfigList.length }}个期别<el-button
              type="text"
              class="f-ml10"
              @click="drawerConfig.signUpIssueDrawer = true"
              >[查看详情]</el-button
            >
          </el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <div class="m-sub-tit is-border-bottom">
      <span class="tit-txt">报名费用</span>
    </div>
    <el-row type="flex" justify="center" class="width-limit">
      <el-col :md="20" :lg="16" :xl="13">
        <el-form ref="form" label-width="150px" class="m-text-form is-column f-mt20">
          <el-form-item label="培训费：">{{ schemeDetail.price }} 元 / 人</el-form-item>
        </el-form>
      </el-col>
    </el-row>
    <sign-up-issue-drawer
      :issueList="schemeDetail.learningTypeModel.issue.issueConfigList"
      :sign-up-issue-drawer.sync="drawerConfig.signUpIssueDrawer"
    ></sign-up-issue-drawer>
  </el-card>
</template>

<script lang="ts">
  import { Component, PropSync, Vue } from 'vue-property-decorator'
  import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'
  import { CreateSchemeUtils } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import SignUpIssueDrawer from '@hbfe/jxjy-admin-scheme/src/components/drawer-components/sign-up-issue-drawer.vue'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

  @Component({
    components: { SignUpIssueDrawer }
  })
  export default class extends Vue {
    /**
     * 方案信息
     */
    @PropSync('trainSchemeDetail', { type: Object }) schemeDetail: TrainClassDetailClassVo
    /**
     * 抽屉显隐
     */
    drawerConfig = {
      signUpIssueDrawer: false
    }
    TrainingModeEnum = TrainingModeEnum
    /**
     * 是否展示在门户
     */
    get isCustomerVisible() {
      return this.schemeDetail.visibleChannelList.includes(1) || this.schemeDetail.visibleChannelList.includes(2)
    }

    /**
     * 是否网售
     */
    get isOnline() {
      return (
        this.schemeDetail.trainClassBaseInfo.skuProperty.trainingMode.skuPropertyValueId === TrainingModeEnum.online
      )
    }

    /**
     * 是否配置开启报名时间
     */
    get isOpenSignUp() {
      let result = false
      const registerBeginDate = this.schemeDetail.trainClassBaseInfo.registerBeginDate || ''
      const registerEndDate = this.schemeDetail.trainClassBaseInfo.registerEndDate
      if (registerBeginDate || registerEndDate) {
        result = true
      }
      return result
    }

    /**
     * 是否开放学员报名
     */
    get openCustomerPurchase() {
      return this.schemeDetail.closeCustomerPurchase == false ? true : false
    }

    /**
     * 获取学习起止时间
     */
    getTrainDate(info: TrainClassDetailClassVo) {
      const trainingBeginDate = info.trainClassBaseInfo.trainingBeginDate || ''
      const trainingEndDate = info.trainClassBaseInfo.trainingEndDate || ''
      if (
        trainingBeginDate &&
        trainingEndDate &&
        CreateSchemeUtils.checkTimeValidity(trainingBeginDate) &&
        CreateSchemeUtils.checkTimeValidity(trainingEndDate)
      ) {
        return `${trainingBeginDate} 至 ${trainingEndDate}`
      } else {
        return '长期有效'
      }
    }

    /**
     * 获取展示用户
     */
    getVisibleChannelList(info: TrainClassDetailClassVo) {
      const visibleChannelList = [
        { id: 1, value: '学员门户可见' },
        { id: 2, value: '集体报名管理员可见' }
      ]
      const filteredItems = visibleChannelList.filter((el: any) => {
        return info.visibleChannelList.includes(el.id)
      })
      const result = [] as string[]
      filteredItems?.map((item) => {
        result.push(item.value)
      })
      return result.join('、')
    }

    /**
     * 获取是否配置开启报名时间
     */
    getRegisterDateInfo() {
      return this.isOpenSignUp ? '配置开启报名时间' : '暂不开启'
    }

    /**
     * 获取商品时间
     * @param {string} type - 字段类型
     */
    getDate(info: TrainClassDetailClassVo, type: string, message: string) {
      return info.trainClassBaseInfo[type] ? info.trainClassBaseInfo[type] : message
    }

    /**
     * 获取方案时间
     * @param {string} type - 字段类型
     */
    getSchemeDate(info: TrainClassDetailClassVo, type: string, message: string) {
      return info[type] ? info[type] : message
    }
  }
</script>
