import TeachingPlanItem from '@api/service/common/scheme/model/schemeDto/issue-configures/teach-plan-learning/config/teaching-plan-items-groups/teaching-plan-items/TeachingPlanItem'

/**
 * @description 教学计划组
 */
class TeachingPlanItemsGroup {
  /**
   * 操作类型
   */
  operation: number
  /**
   * 教学计划组id
   */
  id: string
  /**
   * 教学计划id
   */
  planId: string
  /**
   * 教学计划组名称
   * @description 上午组、下午组
   */
  name: string
  /**
   * 教学计划组类型
   * @description 1-时间组（上午、下午）
   */
  groupType: 1
  /**
   * 教学计划组code
   * @description AM-上午组、PM-下午组
   */
  groupTypeCode: 'AM' | 'PM'
  /**
   * 教学计划组-教学日期
   * @description 格式：yyyy-MM-dd
   */
  groupTypeData: string
  /**
   * 教学计划项
   */
  teachingPlanItems: TeachingPlanItem[]
}

export default TeachingPlanItemsGroup
