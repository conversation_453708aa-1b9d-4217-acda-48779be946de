schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""通过学员身份证获取管理系统报名信息
		@param request
		@return
	"""
	listSignUpNumberInfo(request:SignUpNumberRequest):[SignUpNumberInfoResponse]
}
type Mutation {
	"""申请学员学习Token
		@param request:
		@return {@link String}
		<AUTHOR> By Cb
		@since 2024/5/24 21:05
	"""
	applyStudentLearningToken(request:ApplyStudentLearningTokenRequest):String
	"""创建订单"""
	createOrder(createOrderInfo:CreateOrderRequest):CreateOrderResultResponse
	"""单点登录入口"""
	enterIndex(request:EnterIndexRequest):EnterIndexResponse @optionalLogin
	"""从管理平台获取监管规则
		@see AntiCheatConfigDto
	"""
	getTrainSupervisionForAHZJ(antiCheatConfigParam:GetTrainSupervisionForAHZJRequest):GetTrainSupervisionForAHZJResponse
	"""从管理平台获取验证码"""
	getVerificationCodeForAHZJ(antiCheatCodeAnswerRequest:GetVerificationCodeForAHZJRequest):GetVerificationCodeForAHZJResponse
	"""机构培训平台向管理平台发送验证结果
		@see AntiCheatCodeAnswerPushResult
	"""
	sendCodeAnswerForAHZJ(antiCheatCodeAnswerRequest:SendCodeAnswerForAHZJRequest):SendCodeAnswerForAHZJResponse
	"""校验是否允许创建订单
		@param request:
		@return {@link Boolean}
		<AUTHOR> By Cb
		@since 2024/5/24 21:28
	"""
	validAllowToCreateOrder(request:ValidAllowToCreateOrderRequest):Boolean
	"""校验是否允许学习
		@param request:
		@return {@link Boolean}
		<AUTHOR> By Cb
		@since 2024/5/24 21:28
	"""
	validAllowToLearning(request:ValidAllowToLearningRequest):Boolean
}
input DeliveryAddress @type(value:"com.fjhb.domain.trade.api.offlineinvoice.events.entities.DeliveryAddress") {
	consignee:String!
	phone:String!
	region:String!
	address:String!
}
input TakePoint @type(value:"com.fjhb.domain.trade.api.offlineinvoice.events.entities.TakePoint") {
	pickupLocation:String!
	pickupTime:String!
	remark:String
}
"""请求创建订单
	<AUTHOR>
	@since 2021/1/22
"""
input CreateOrderRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.request.CreateOrderRequest") {
	"""买家编号"""
	buyerId:String!
	"""商品列表"""
	commodities:[Commodity]!
	"""购买渠道类型
		1-用户自主购买
		2-集体缴费
		3-管理员导入
	"""
	purchaseChannelType:Int!
	"""终端类型
		<p>
		Web端：Web
		IOS端：IOS
		安卓端：Android
		微信小程序：WechatMini
		微信公众号：WechatOfficial
	"""
	terminalCode:String!
	"""渠道商编号"""
	channelVendorId:String
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票信息"""
	invoiceInfo:InvoiceInfoRequest
	"""参训单位id"""
	participatingUnitId:String
	"""销售渠道Id（自营渠道为空，专题渠道时必填）"""
	saleChannelId:String
	"""销售渠道类型
		0-自营渠道
		2-专题渠道
	"""
	saleChannel:Int!
	"""购买来源类型，1-门户，2-专题
		@see com.fjhb.ms.order.v1.api.consts.PurchaseSourceType
	"""
	purchaseSourceType:Int
	"""身份证"""
	idCard:String
}
"""商品描述"""
input Commodity @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.request.CreateOrderRequest$Commodity") {
	"""商品sku编号"""
	skuId:String
	"""商品数量"""
	quantity:Int
	"""面授班时有值"""
	issueInfo:IssueInfo
}
input IssueInfo @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.request.CreateOrderRequest$Commodity$IssueInfo") {
	"""期别id"""
	issueId:String
	"""住宿类型
		住宿类型 0-无需住宿 1-单人住宿 2-合住
	"""
	accommodationType:Int
}
"""发票信息
	<AUTHOR>
	@since 2021/3/23
"""
input InvoiceInfoRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.request.InvoiceInfoRequest") {
	"""发票抬头"""
	title:String
	"""发票抬头类型
		<pre>
		1-个人
		2-企业
		</pre>
	"""
	titleType:Int
	"""发票类型
		<pre>
		1-电子发票
		2-纸质发票
		</pre>
	"""
	invoiceType:Int
	"""发票种类
		<pre>
		1-普通发票
		2-增值税普通发票
		3-增值税专用发票
		</pre>
	"""
	invoiceCategory:Int
	"""购买方纳税人识别号"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账户"""
	account:String
	"""发票票面备注"""
	remark:String
	"""开票方式
		1 - 线上开票
		2 - 线下开票
	"""
	invoiceMethod:Int
	"""联系电子邮箱"""
	email:String
	"""联系电话"""
	contactPhone:String
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""配送方式
		0/1/2,无/自取/快递
		@see OfflineShippingMethods
	"""
	shippingMethod:Int
	"""配送地址信息"""
	deliveryAddress:DeliveryAddress
	"""自取点信息"""
	takePoint:TakePoint
}
"""申请学员学习Token
	<AUTHOR>
	@since 2024/5/24
"""
input ApplyStudentLearningTokenRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ApplyStudentLearningTokenRequest") {
	"""参训资格ID"""
	qualificationId:String!
	"""学习方式ID"""
	learningId:String!
}
"""进入首页请求
	<AUTHOR>
	@since 2024/5/24
"""
input EnterIndexRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.EnterIndexRequest") {
	"""学员信息key"""
	studentInfoKey:String
}
"""<AUTHOR>
input GetTrainSupervisionForAHZJRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.GetTrainSupervisionForAHZJRequest") {
	"""订单id，将在统一网关转为报名序号
		由于清洗延迟问题，后续使用子订单
	"""
	orderId:String
	"""子订单号"""
	subOrderNo:String
	"""用户id，将在同一网关转为用户开发id"""
	userId:String
	"""培训平台的课程唯一标识"""
	courseId:String
	"""培训平台的课程名"""
	courseName:String
}
"""<AUTHOR>
	@since 2024/5/24
"""
input GetVerificationCodeForAHZJRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.GetVerificationCodeForAHZJRequest") {
	configKey:String
	playTime:Int
}
"""<AUTHOR>
input SendCodeAnswerForAHZJRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.SendCodeAnswerForAHZJRequest") {
	"""验证码ID"""
	codeId:String
	"""验证码答案"""
	codeAnswer:String
}
"""<AUTHOR>
input SignUpNumberRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.SignUpNumberRequest") {
	"""用户身份证"""
	certificateNumber:String
}
"""校验是否允许创建订单
	<AUTHOR>
	@since 2024/5/24
"""
input ValidAllowToCreateOrderRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ValidAllowToCreateOrderRequest") {
	"""商品sku"""
	skuId:String!
	"""身份证"""
	idCard:String!
}
"""校验是否允许学习
	<AUTHOR>
	@since 2024/5/24
"""
input ValidAllowToLearningRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.ValidAllowToLearningRequest") {
	"""参训资格ID"""
	qualificationId:String!
}
type AntiCheatCodeAnswerPushResult @type(value:"com.fjhb.ms.unifiedinteraction.gateway.api.response.AntiCheatCodeAnswerPushResult") {
	id:String
	verifyResult:Int!
	goNext:Boolean!
}
type AntiCheatCodeDto @type(value:"com.fjhb.ms.unifiedinteraction.gateway.api.response.AntiCheatCodeDto") {
	id:String
	content:String
	couldAnswerCount:Int!
	remainAnswerCount:Int!
}
type AntiCheatConfigDto @type(value:"com.fjhb.ms.unifiedinteraction.gateway.api.response.AntiCheatConfigDto") {
	configKey:String
	allVerifyCount:Int!
	coursePlayTimeList:[PlayTimePointDto]
}
type PlayTimePointDto @type(value:"com.fjhb.ms.unifiedinteraction.gateway.api.response.PlayTimePointDto") {
	playTime:Int!
	isVerify:Int!
}
"""创建订单结果
	<AUTHOR> create 2021/1/29 17:27
"""
type CreateOrderResultResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.response.CreateOrderResultResponse") {
	"""是否创建成功"""
	success:Boolean!
	"""订单号，仅当{@link #success}为{@code true}时有值"""
	orderNo:String
	"""订单创建时间，仅当{@link #success}为{@code true}时有值"""
	createTime:DateTime
	"""下单结果信息"""
	message:String
	"""商品验证结果信息"""
	resultList:[VerifyResultResponse]
}
"""校验结果返回
	<AUTHOR> create 2021/2/3 10:53
"""
type VerifyResultResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.appservice.response.VerifyResultResponse") {
	"""校验结果"""
	message:String
	"""校验code"""
	code:String
	"""订单内的商品skuId"""
	skuId:String
}
"""进入首页响应
	<AUTHOR>
	@since 2024/5/24
"""
type EnterIndexResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.EnterIndexResponse") {
	"""登录token"""
	token:String
	"""页面中转key
		@see http://192.168.1.225:8090/pages/viewpage.action?pageId=284295407
	"""
	key:String
	"""用户id"""
	userId:String
	"""参训资格id"""
	qualificationId:String
	"""商品skuId"""
	commoditySkuId:String
	"""培训方案id"""
	schemeId:String
	"""订单号"""
	orderNo:String
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""<AUTHOR>
	@since 2024/5/24
"""
type GetTrainSupervisionForAHZJResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.GetTrainSupervisionForAHZJResponse") {
	antiCheatConfigDto:AntiCheatConfigDto
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""<AUTHOR>
	@since 2024/5/24
"""
type GetVerificationCodeForAHZJResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.GetVerificationCodeForAHZJResponse") {
	antiCheatCodeDto:AntiCheatCodeDto
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""<AUTHOR>
	@since 2024/5/24
"""
type SendCodeAnswerForAHZJResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.SendCodeAnswerForAHZJResponse") {
	antiCheatCodeAnswerPushResult:AntiCheatCodeAnswerPushResult
	"""状态码"""
	code:String
	"""状态信息"""
	message:String
}
"""报名序列号信息
	<AUTHOR>
"""
type SignUpNumberInfoResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.SignUpNumberInfoResponse") {
	"""报名序号，培训平台报名成功保存用于后续使用"""
	signUpNumber:String
	"""培训班名称"""
	trainingClassName:String
	"""培训形式
		1：网授-培训班
	"""
	trainingType:Int!
	"""报名年度"""
	trainingYear:Int!
	"""科目类型
		0：公需课
	"""
	subjectType:Int!
	"""科目名称"""
	subject:String
	"""公需学时，班级科目类型为专业，该值为空"""
	publicPeriod:Double!
	"""培训开始时间"""
	trainingStartTime:String
	"""培训结束时间"""
	trainingEndTime:String
	"""订单号"""
	orderNo:String
	"""对应订单状态"""
	orderStatus:String
}

scalar List
