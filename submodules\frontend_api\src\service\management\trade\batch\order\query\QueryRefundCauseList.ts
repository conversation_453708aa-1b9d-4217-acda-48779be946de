/*
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-27 13:56:54
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-29 11:22:36
 */
import ReturnOrder from '@api/ms-gateway/ms-return-order-v1'
import BatchReturnOrder from '@api/ms-gateway/ms-batch-return-v1'
export class RefundReason {
  refundReasonId: string //退款原因ID
  refundReasonValue: string //退款原因内容
}
export default class QueryRefundList {
  /**
   * 获取退款原因
   *
   */
  async queryRefundReasonList(): Promise<RefundReason[]> {
    const { data } = await ReturnOrder.prepareReturn()
    // const data = new Array<RefundReason>()
    return Object.keys(data).map((key: string) => {
      return {
        refundReasonId: key,
        refundReasonValue: data[key]
      }
    })
  }

  /**
   * 获取退款原因
   *
   */
  async queryBatchRefundReasonList(): Promise<RefundReason[]> {
    const { data } = await BatchReturnOrder.prepareReturn()
    // const data = new Array<RefundReason>()
    return Object.keys(data).map((key: string) => {
      return {
        refundReasonId: key,
        refundReasonValue: data[key]
      }
    })
  }
}
