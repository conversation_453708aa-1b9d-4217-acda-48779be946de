import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 培训班状态枚举
 * 1：有效
 * 2：冻结
 * 3：失效
 */
export enum TrainClassStatusEnum {
  Effective = 1,
  Frozen = 2,
  Failure = 3
}

class TrainClassStatusType extends AbstractEnum<TrainClassStatusEnum> {
  static enum = TrainClassStatusEnum
  constructor(status?: TrainClassStatusEnum) {
    super()
    this.current = status
    this.map.set(TrainClassStatusEnum.Effective, '有效')
    this.map.set(TrainClassStatusEnum.Frozen, '冻结')
    this.map.set(TrainClassStatusEnum.Failure, '失效')
  }
}

export default new TrainClassStatusType()
