<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">专题管理</el-breadcrumb-item>
      <el-breadcrumb-item>编辑专题</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-tabs v-model="activeName" type="card" class="m-tab-card">
        <div class="tab-right">
          <el-button type="primary" size="small" class="f-mr5"
            ><i class="el-icon-picture f-f14 f-vm f-mr5"></i>查看专题示例</el-button
          >
          <el-button type="primary" size="small" class="f-mr5">
            <i class="el-icon-link f-f14 f-vm f-mr5"></i>访问专题web
          </el-button>
          <el-button type="primary" size="small" class="f-mr15">
            <i class="el-icon-link f-f14 f-vm f-mr5"></i>访问专题h5
          </el-button>
        </div>
        <el-tab-pane label="基础信息" name="first">
          见1739_专题管理_编辑专题_基础信息.vue
        </el-tab-pane>
        <el-tab-pane label="门户信息" name="second">
          1740_专题管理_编辑专题_门户信息.vue
        </el-tab-pane>
        <el-tab-pane label="培训方案" name="third">
          1741_专题管理_编辑专题_设置培训信息.vue
        </el-tab-pane>
        <el-tab-pane label="精品课程" name="fourth">
          1742_专题管理_编辑专题_精品课程.vue
        </el-tab-pane>
        <el-tab-pane label="线上集体报名" name="fifth">
          1743_专题管理_编辑专题_线上集体报名.vue
        </el-tab-pane>
        <el-tab-pane label="线下集体报名" name="sixth">
          <el-card shadow="never" class="m-card f-mb15 is-bg">
            <div class="f-p15">
              <el-row type="flex" justify="center" class="width-limit">
                <el-col :md="20" :lg="16" :xl="13">
                  <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                    <el-form-item label="专题线下集体报名入口：" required>
                      <el-switch v-model="form.delivery" active-text="开启" inactive-text="关闭" class="m-switch" />
                    </el-form-item>
                    <el-form-item label="专题线下集体报名入口图片：">
                      <el-upload action="#" list-type="picture-card" :auto-upload="false" class="m-pic-upload">
                        <div slot="default" class="upload-placeholder">
                          <i class="el-icon-plus"></i>
                          <p class="txt">上传图片</p>
                        </div>
                        <div slot="file" slot-scope="{ file }" class="img-file">
                          <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                          <div class="el-upload-list__item-actions">
                            <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
                              <i class="el-icon-zoom-in"></i>
                            </span>
                            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove(file)">
                              <i class="el-icon-delete"></i>
                            </span>
                          </div>
                        </div>
                        <div slot="tip" class="el-upload__tip">
                          <i class="el-icon-warning"></i>
                          <span class="txt">
                            上传线下集体报名入口图片，只开放线下入口图片尺寸为1200x100px，<br />线上入口和线下入口都开放则图片尺寸为590x100px，不上传则显示模板默认图片。<br />
                            <i class="f-link" @click="dialog3 = true">查看示例图片</i>
                          </span>
                          <!--示例图片弹窗-->
                          <el-dialog :visible.sync="dialog3" width="640px" class="m-dialog-pic">
                            <!--分别读取对应的默认图片-->
                            <img src="./assets/images/demo-specialenter-xx-default-2.jpg" alt="" />
                          </el-dialog>
                        </div>
                      </el-upload>
                      <el-dialog :visible.sync="dialogVisible" width="1100px" class="m-dialog-pic">
                        <img :src="dialogImageUrl" alt="" />
                      </el-dialog>
                    </el-form-item>
                    <el-form-item label="专题线下集体报名名称：" required>
                      <el-input v-model="form.name1" clearable />
                    </el-form-item>
                    <el-form-item label="线下集体报名模板：" required>
                      <el-upload ref="upload" :on-remove="handleRemove" :auto-upload="false" class="form-l">
                        <el-button slot="trigger" type="primary" plain>点击上传模板</el-button>
                      </el-upload>
                      <div class="el-upload__tip f-pt5">
                        <div class="txt"><i class="f-link">下载示例模版</i></div>
                      </div>
                    </el-form-item>
                    <el-form-item label="访问链接：">
                      https:tar.59iedu.com/index
                      <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                        <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
                        <div slot="content">点击复制链接</div>
                      </el-tooltip>
                    </el-form-item>
                    <el-form-item label="底部文本说明：" required>
                      <el-input
                        v-model="form.name"
                        clearable
                        placeholder="填写集体报名联系电话，如：如报名过程有问题，请咨询服务热线：968823"
                      />
                    </el-form-item>
                    <el-form-item label="报名步骤：" required>
                      <div class="step f-mb20">
                        <div class="f-flex f-align-center">
                          <div class="f-flex-sub">
                            <span class="f-cb f-fb"><i class="f-dot f-mr5"></i>第一步</span>
                            <el-input
                              v-model="form.name2"
                              clearable
                              placeholder="请输入步骤的标题"
                              class="form-l f-ml10"
                            />
                          </div>
                          <el-button size="mini" type="danger" plain class="f-fr">删除</el-button>
                        </div>
                        <div class="rich-text f-mt10">
                          <el-input type="textarea" :rows="4" v-model="form.desc" placeholder="请输入内容" />
                        </div>
                      </div>
                      <div class="step f-mb20">
                        <div class="f-flex f-align-center">
                          <div class="f-flex-sub">
                            <span class="f-cb f-fb"><i class="f-dot f-mr5"></i>第二步</span>
                            <el-input
                              v-model="form.name3"
                              clearable
                              placeholder="请输入步骤的标题"
                              class="form-l f-ml10"
                            />
                          </div>
                          <el-button size="mini" type="danger" plain class="f-fr">删除</el-button>
                        </div>
                        <div class="rich-text f-mt10">
                          <el-input type="textarea" :rows="4" v-model="form.desc" placeholder="请输入内容" />
                        </div>
                      </div>
                      <div class="step f-mb20">
                        <div class="f-flex f-align-center">
                          <div class="f-flex-sub">
                            <span class="f-cb f-fb"><i class="f-dot f-mr5"></i>第三步</span>
                            <el-input
                              v-model="form.name4"
                              clearable
                              placeholder="请输入步骤的标题"
                              class="form-l f-ml10"
                            />
                          </div>
                          <el-button size="mini" type="danger" plain class="f-fr">删除</el-button>
                        </div>
                        <div class="rich-text f-mt10">
                          <el-input type="textarea" :rows="4" v-model="form.desc" placeholder="请输入内容" />
                        </div>
                      </div>
                      <div class="step f-mb20">
                        <div class="f-flex f-align-center">
                          <div class="f-flex-sub">
                            <span class="f-cb f-fb"><i class="f-dot f-mr5"></i>第四步</span>
                            <el-input
                              v-model="form.name5"
                              clearable
                              placeholder="请输入步骤的标题"
                              class="form-l f-ml10"
                            />
                          </div>
                          <el-button size="mini" type="danger" plain class="f-fr">删除</el-button>
                        </div>
                        <div class="rich-text f-mt10">
                          <el-input type="textarea" :rows="4" v-model="form.desc" placeholder="请输入内容" />
                        </div>
                      </div>
                      <el-button type="primary" plain icon="el-icon-plus">添加步骤</el-button>
                    </el-form-item>
                  </el-form>
                </el-col>
              </el-row>
              <div class="m-btn-bar f-tc is-sticky f-pt15">
                <el-button>取 消</el-button>
                <el-button type="primary">保 存</el-button>
              </div>
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'sixth',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog3: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
