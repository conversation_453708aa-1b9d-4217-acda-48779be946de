import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-order-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-order-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum BatchOrderState {
  UNCONFIRMED = 'UNCONFIRMED',
  NORMAL = 'NORMAL',
  COMPLETED = 'COMPLETED',
  CLOSED = 'CLOSED',
  COMMITTING = 'COMMITTING',
  CANCELING = 'CANCELING'
}
export enum PaymentState {
  NONE = 'NONE',
  PAYING = 'PAYING',
  PAID = 'PAID'
}

// 类

export class BatchReturnSubOrderInfo {
  orderNo: string
  subOrderNo: string
}

export class DeliveryAddress {
  consignee: string
  phone: string
  region: string
  address: string
}

export class TakePoint {
  pickupLocation: string
  pickupTime: string
  remark?: string
}

export class SubOrderAfterInfo {
  subOrderNo?: string
  quantity: number
  amount?: number
  amountSource: number
  properties?: Map<string, string>
}

export class BatchOrderInvoiceDeliveryAddress {
  /**
   * 收件人
   */
  consignee?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 所在物理地区
   */
  region?: string
  /**
   * 地址
   */
  address?: string
}

export class BatchOrderInvoiceTakePoint {
  /**
   * 领取地点
   */
  pickupLocation?: string
  /**
   * 领取时间
   */
  pickupTime?: string
  /**
   * 备注
   */
  remark?: string
}

/**
 * 电子发票更新信息传输对象
<AUTHOR> By lincong
@date 2023/09/21 18:01
 */
export class InvoiceDto {
  /**
   * 发票种类,必传
<pre>
1-普通发票
2-增值税普通发票
3-增值税专用发票
</pre>
   */
  invoiceCategory: number
  /**
   * 发票类型，必传
1-电子发票
2-纸质发票
   */
  invoiceType: number
  /**
   * 开票方式，必传
1-线上开票
2-线下开票
   */
  invoiceMethod: number
  /**
   * 发票抬头,必传
   */
  title?: string
  /**
   * 发票抬头类型，必传
<pre>
1-个人
2-企业
</pre>
   */
  titleType?: number
  /**
   * 购买方纳税人编号
   */
  taxpayerNo?: string
  /**
   * 购买方地址
   */
  address?: string
  /**
   * 购买方电话号码
   */
  phone?: string
  /**
   * 购买方开户行名称
   */
  bankName?: string
  /**
   * 购买方银行账户
   */
  account?: string
  /**
   * 购买方电子邮箱
   */
  contactEmail?: string
  /**
   * 联系电话
   */
  contactPhone?: string
  /**
   * 发票票面备注
   */
  remark?: string
  /**
   * 营业执照
   */
  businessLicensePath?: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath?: string
  /**
   * 配送方式
0/1/2,无/自取/快递
@see OfflineShippingMethods
   */
  shippingMethod?: number
  /**
   * 配送地址信息
   */
  deliveryAddress?: BatchOrderInvoiceDeliveryAddress
  /**
   * 自取点信息
   */
  batchOrderInvoiceTakePoint?: BatchOrderInvoiceTakePoint
}

/**
 * 索取发票
<AUTHOR>
@since 2021/3/25
 */
export class ApplyOrderInvoiceRequest {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceInfoRequest
}

/**
 * 索取发票校验
<AUTHOR>
@since 2021/3/25
 */
export class ApplyOrderInvoiceValidateRequest {
  /**
   * 订单号
   */
  orderNo?: string
}

/**
 * 买家申请取消订单
<AUTHOR> create 2021/1/27 19:32
 */
export class BuyerApplyCancelOrderRequest {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 取消原因编号
   */
  reasonId?: string
  /**
   * 取消原因描述
   */
  reason?: string
}

/**
 * 卖家申请订单退货请求
<AUTHOR>
 */
export class BuyerApplyOrderReturnRequest {
  orderNo: string
  subOrderNo: string
  /**
   * 退货原因id
   */
  reasonId?: string
  /**
   * 退货原因描述
   */
  description?: string
  /**
   * 是否需要人工审批,默认是
   */
  needManualApprove: boolean
  /**
   * 退货单类型;1:仅退货,2:仅退款,3:退货并退款
   */
  returnOrderType: number
}

/**
 * 请求创建订单
<AUTHOR>
@since 2021/1/22
 */
export class CreateOrderRequest {
  /**
   * 买家编号
   */
  buyerId: string
  /**
   * 商品列表
   */
  commodities: Array<Commodity>
  /**
   * 购买渠道类型
1-用户自主购买
2-集体缴费
3-管理员导入
   */
  purchaseChannelType: number
  /**
   * 终端类型
<p>
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  terminalCode: string
  /**
   * 渠道商编号
   */
  channelVendorId?: string
  /**
   * 是否需要发票
   */
  needInvoice: boolean
  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceInfoRequest
  /**
   * 参训单位id
   */
  participatingUnitId?: string
  /**
   * 销售渠道Id（自营渠道为空，专题渠道时必填）
   */
  saleChannelId?: string
  /**
   * 销售渠道类型
0-自营渠道
2-专题渠道
   */
  saleChannel: number
  /**
   * 购买来源类型，1-门户，2-专题
@see com.fjhb.ms.order.v1.api.consts.PurchaseSourceType
   */
  purchaseSourceType?: number
}

/**
 * 商品描述
 */
export class Commodity {
  /**
   * 商品sku编号
   */
  skuId?: string
  /**
   * 商品数量
   */
  quantity?: number
  /**
   * 面授班时有值
   */
  issueInfo?: IssueInfo
}

export class IssueInfo {
  /**
   * 期别id
   */
  issueId?: string
  /**
   * 住宿类型
住宿类型 0-无需住宿 1-单人住宿 2-合住
   */
  accommodationType?: number
}

/**
 * 发票信息
<AUTHOR>
@since 2021/3/23
 */
export class InvoiceInfoRequest {
  /**
   * 发票抬头
   */
  title?: string
  /**
   * 发票抬头类型
<pre>
1-个人
2-企业
</pre>
   */
  titleType?: number
  /**
   * 发票类型
<pre>
1-电子发票
2-纸质发票
</pre>
   */
  invoiceType?: number
  /**
   * 发票种类
<pre>
1-普通发票
2-增值税普通发票
3-增值税专用发票
</pre>
   */
  invoiceCategory?: number
  /**
   * 购买方纳税人识别号
   */
  taxpayerNo?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bankName?: string
  /**
   * 账户
   */
  account?: string
  /**
   * 发票票面备注
   */
  remark?: string
  /**
   * 开票方式
1 - 线上开票
2 - 线下开票
   */
  invoiceMethod?: number
  /**
   * 联系电子邮箱
   */
  email?: string
  /**
   * 联系电话
   */
  contactPhone?: string
  /**
   * 营业执照
   */
  businessLicensePath?: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath?: string
  /**
   * 配送方式
0/1/2,无/自取/快递
@see OfflineShippingMethods
   */
  shippingMethod?: number
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddress
  /**
   * 自取点信息
   */
  takePoint?: TakePoint
  /**
   * 发票信息校验策略
@see InvoiceVerifyStrategy
   */
  invoiceVerifyStrategy: number
}

/**
 * graphql 使用的key、value 返回对象
<AUTHOR> create 2021/2/24 20:07
 */
export class KeyValueDataRequest {
  /**
   * 字段的key
   */
  key?: string
  /**
   * 字段value
   */
  value?: string
}

/**
 * 线上支付参数
<AUTHOR> create 2021/1/28 19:39
 */
export class OrderOfflinePaymentRequest {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 购买渠道类型
1-用户自主购买
2-集体缴费
3-管理员导入
   */
  purchaseChannelType: number
  /**
   * 支付终端
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  paymentChannelTerminal: string
  /**
   * /**
付款人
   */
  payerId: string
  /**
   * 备注
选填
   */
  remark?: string
  /**
   * 线下付款凭证文件路径集合
   */
  paths: Array<string>
}

/**
 * 线上支付参数
<AUTHOR> create 2021/1/28 19:39
 */
export class OrderOnlinePaymentRequest {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 支付渠道编号
培训券，对接众智汇云培训券:TRAINING_VOUCHER
支付宝:ALIPAY
微信：WXPAY
兴业银行: CIB_PAY
   */
  paymentChannelId: string
  /**
   * 购买渠道类型
1-用户自主购买
2-集体缴费
3-管理员导入
   */
  purchaseChannelType: number
  /**
   * 支付终端
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  purchaseChannelTerminal: string
  /**
   * 支付描述
   */
  description?: string
  /**
   * 支付成功后跳转地址
   */
  pageUrl?: string
  /**
   * 支付的附加属性，由支付渠道决定
当支付渠道为培训券（TRAINING4_VOUCHER）时，
{
&quot;couponCode&quot;:&quot;培训券编码&quot;,
&quot;educationCode&quot;:&quot;机构编码（企业统一社会信用代码）&quot;,
&quot;workType&quot;: &quot;工种名称&quot;,
&quot;learningSchemeId&quot;: &quot;培训班id&quot;
}
当支付渠道为支付宝-移动端：
{
&quot;method&quot;:&quot;alipay.trade.wap.pay&quot;
}
当支付渠道为支付宝-电脑端：
{
&quot;method&quot;:&quot;alipay.trade.page.pay&quot;
}
当支付渠道为微信：
{
“method”:&quot;native&quot;
}
微信h5中clientIp不需要提供 由服务端获取
当前支付渠道为兴业银行:
兴业银行统一扫码支付
{
&quot;method&quot;:&quot;unifiedTradeNative&quot;
}
兴业银行微信小程序&amp;公众号支付
{
&quot;method&quot;:&quot;payWeixinJspay&quot;,
&quot;sub_openid&quot;:&quot;用户openid&quot;,
&quot;is_raw&quot;:&quot;是否原生态JS,1-是;其它-否&quot;,
&quot;is_minipg&quot;：&quot;是否小程序支付,1-是;其它-否“,
}
   */
  paymentProperties?: Array<KeyValueDataRequest>
}

/**
 * 准备支付参数
<AUTHOR>
@since 2022/3/24
 */
export class PreparePayRequest {
  /**
   * 订单号
   */
  orderNo?: string
  /**
   * 终端
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
H5端：H5
   */
  purchaseChannelTerminal: string
  /**
   * 购买渠道类型：
1-用户自主购买
2-集体缴费
3-管理员导入
4-集体报名个人缴费
   */
  purchaseChannelType: number
}

/**
 * 卖家申请退货请求
 */
export class SellerApplyAfterSaleRequest {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 退货原因ID
   */
  reasonId: string
  /**
   * 退货说明
   */
  description: string
  /**
   * 退货单类型
@see ReturnOrderType
   */
  returnOrderType: number
  /**
   * 退货商品列表
   */
  returnList: Array<SubOrderAfterInfo>
  /**
   * 扩展信息（华医网差异化courseType） 1&#x3D;专业 2&#x3D;公需
   */
  properties?: Map<string, string>
}

/**
 * 卖家申请订单退货请求
<AUTHOR>
 */
export class SellerApplyBatchOrderReturnRequest {
  batchOrderNo: string
  /**
   * 退货原因id
   */
  reasonId: string
  /**
   * 退货原因描述
   */
  description: string
  /**
   * 是否需要人工审批,默认是
   */
  needManualApprove: boolean
  /**
   * 批次退货单类型;1:仅退货,2:仅退款,3:退货并退款
   */
  batchReturnOrderType: number
  /**
   * 如果有指定的退货的子订单信息,就只退指定的子订单,否则认为该批次单下的所有订单退货
   */
  returnSubOrders?: Array<BatchReturnSubOrderInfo>
}

/**
 * 申请订单换货
<AUTHOR>
 */
export class SellerApplyOrderExchangeRequest {
  orderNo: string
  subOrderNo: string
  /**
   * 换货原因id,一般是系统内置一些原因
   */
  reasonId?: string
  /**
   * 换货原因描述
   */
  description?: string
  /**
   * 换货的目标商品sku id
   */
  exchangeSkuId: string
  /**
   * 当为分销订单单换货的时候，需要指定换货目标的定价策略
   */
  exchangePricingPolicyId?: string
  /**
   * 原期别id
   */
  originIssueId?: string
  /**
   * 目标期别id
   */
  targetIssueId?: string
  /**
   * 是否需要人工审批,默认是
   */
  needManualApprove: boolean
}

/**
 * 卖家申请订单退货请求
<AUTHOR>
 */
export class SellerApplyOrderReturnRequest {
  orderNo: string
  subOrderNo: string
  /**
   * 退货原因id
   */
  reasonId?: string
  /**
   * 退货原因描述
   */
  description?: string
  /**
   * 是否需要人工审批,默认是
   */
  needManualApprove: boolean
  /**
   * 退货单类型;1:仅退货,2:仅退款,3:退货并退款
   */
  returnOrderType: number
}

/**
 * 卖家取消订单
<AUTHOR> create 2021/1/27 20:08
 */
export class SellerCancelOrderRequest {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 取消原因ID
   */
  reasonId?: string
  /**
   * 取消原因说明
选填
   */
  reason?: string
}

/**
 * 批次发票
<AUTHOR> create 2021/4/30 9:36
 */
export class BatchInvoiceRequest {
  /**
   * 发票种类，默认2
<pre>
1-普通发票
2-增值税普通发票
3-增值税专用发票
</pre>
   */
  invoiceCategory: number
  /**
   * 发票种类
1-电子发票
2-纸质发票
   */
  invoiceType: number
  /**
   * 开票方式
1-线上开票
2-线下开票
   */
  invoiceMethod: number
  /**
   * 发票抬头
   */
  title?: string
  /**
   * 发票抬头类型
<pre>
1-个人
2-企业
</pre>
   */
  titleType?: number
  /**
   * 购买方纳税人识别号
   */
  taxpayerNo?: string
  /**
   * 地址
   */
  address?: string
  /**
   * 电话
   */
  phone?: string
  /**
   * 开户行
   */
  bankName?: string
  /**
   * 账户
   */
  account?: string
  /**
   * 联系邮箱
   */
  contactEmail?: string
  /**
   * 联系电话
   */
  contactPhone?: string
  /**
   * 发票票面备注
   */
  remark?: string
  /**
   * 营业执照
   */
  businessLicensePath?: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath?: string
  /**
   * 配送方式
   */
  shippingMethod?: number
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddress
  /**
   * 自取点信息
   */
  takePoint?: TakePoint
  /**
   * 发票信息校验策略
@see InvoiceVerifyStrategy
   */
  invoiceVerifyStrategy?: number
}

/**
 * 批次单申请发票
<AUTHOR> create 2021/4/30 9:12
 */
export class BatchOrderApplyInvoiceRequest {
  /**
   * 批次单编号
   */
  batchOrderNo?: string
  /**
   * 申请批次发票信息
   */
  invoiceInfo?: BatchInvoiceRequest
}

/**
 * 批次单申请发票
<AUTHOR> create 2021/4/30 9:12
 */
export class BatchOrderApplyInvoiceValidateRequest {
  /**
   * 批次单编号
   */
  batchOrderNo?: string
}

/**
 * 更新批次单申请发票
<AUTHOR> create 2021/4/30 9:12
 */
export class BatchOrderInvoiceChangeRequest {
  /**
   * 批次单编号
   */
  batchOrderNo?: string
  /**
   * 申请批次发票信息
   */
  invoiceInfo?: BatchInvoiceRequest
}

/**
 * 批次单线下支付并申请发票请求参数
<AUTHOR> create 2021/4/23 11:11
 */
export class BatchOrderOfflinePaymentAndInvoiceRequest {
  /**
   * 批次单号
   */
  batchOrderNo?: string
  /**
   * 购买渠道类型
1-用户自主购买
2-集体缴费
3-管理员导入
   */
  purchaseChannelType: number
  /**
   * 支付终端
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  paymentChannelTerminal: string
  /**
   * 付款人
   */
  payerId?: string
  /**
   * 支付描述
   */
  description?: string
  /**
   * 备注
选填
   */
  remark?: string
  /**
   * 线下付款凭证文件路径集合
   */
  paths?: Array<string>
  /**
   * 申请批次发票信息
   */
  invoiceInfo?: BatchInvoiceRequest
}

/**
 * 批次单线下支付请求参数
<AUTHOR> create 2021/4/23 11:11
 */
export class BatchOrderOfflinePaymentRequest {
  /**
   * 批次单号
   */
  batchOrderNo?: string
  /**
   * 购买渠道类型
1-用户自主购买
2-集体缴费
3-管理员导入
   */
  purchaseChannelType: number
  /**
   * 支付终端
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
   */
  paymentChannelTerminal: string
  /**
   * 付款人
   */
  payerId?: string
  /**
   * 支付描述
   */
  description?: string
  /**
   * 备注
选填
   */
  remark?: string
  /**
   * 线下付款凭证文件路径集合
   */
  paths?: Array<string>
}

/**
 * 批次单线上支付并申请发票请求参数
<AUTHOR> create 2021/4/23 11:10
 */
export class BatchOrderOnlinePaymentAndApplyInvoiceRequest {
  /**
   * 批次单号
   */
  batchOrderNo?: string
  /**
   * 支付渠道ID
   */
  paymentChannelId?: string
  /**
   * 终端类型
   */
  terminalCode?: string
  /**
   * 支付成功后跳转地址
   */
  pageUrl?: string
  /**
   * 支付描述
   */
  description?: string
  /**
   * 支付的附加属性，由支付渠道决定
当支付渠道为培训券（TRAINING_VOUCHER）时，
{
&quot;couponCode&quot;:&quot;培训券编码&quot;,
&quot;educationCode&quot;:&quot;机构编码（企业统一社会信用代码）&quot;,
&quot;workType&quot;: &quot;工种名称&quot;,
&quot;learningSchemeId&quot;: &quot;培训班id&quot;
}
   */
  paymentProperties?: Array<KeyValueDataRequest>
  /**
   * 申请批次发票信息
   */
  invoiceInfo?: BatchInvoiceRequest
}

/**
 * 批次单线上支付请求参数
<AUTHOR> create 2021/4/23 11:10
 */
export class BatchOrderOnlinePaymentRequest {
  /**
   * 批次单号
   */
  batchOrderNo?: string
  /**
   * 支付渠道ID
   */
  paymentChannelId?: string
  /**
   * 终端类型
   */
  terminalCode?: string
  /**
   * 支付成功后跳转地址
   */
  pageUrl?: string
  /**
   * 支付描述
   */
  description?: string
  /**
   * 支付的附加属性，由支付渠道决定
当支付渠道为培训券（TRAINING_VOUCHER）时，
{
&quot;couponCode&quot;:&quot;培训券编码&quot;,
&quot;educationCode&quot;:&quot;机构编码（企业统一社会信用代码）&quot;,
&quot;workType&quot;: &quot;工种名称&quot;,
&quot;learningSchemeId&quot;: &quot;培训班id&quot;
}
   */
  paymentProperties?: Array<KeyValueDataRequest>
}

/**
 * 准备批次支付参数
<AUTHOR> By Cb
@since 2022/04/28
 */
export class PrepareBatchPayRequest {
  /**
   * 批次订单号
   */
  batchOrderNo?: string
  /**
   * 终端
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
H5端：H5
   */
  purchaseChannelTerminal: string
  /**
   * 购买渠道类型：
1-用户自主购买
2-集体缴费
3-管理员导入
4-集体报名个人缴费
   */
  purchaseChannelType: number
}

/**
 * 批次单更新电子发票信息请求
<AUTHOR> By lincong
@date 2023/09/21 17:51
 */
export class UpdateBatchOrderInvoiceRequest {
  /**
   * 当前发票所属的批次单号
   */
  batchOrderNo?: string
  /**
   * 触发重新开票按钮的当前发票id
   */
  invoiceId?: string
  /**
   * 电子票更新信息对象
   */
  invoiceInfo?: InvoiceDto
}

/**
 * 更新批次单线下支付凭证请求参数
<AUTHOR> create 2021/4/23 11:11
 */
export class UpdateBatchOrderOfflinePaymentVoucherRequest {
  /**
   * 批次单号
   */
  batchOrderNo?: string
  /**
   * 备注
选填
   */
  remark?: string
  /**
   * 线下付款凭证文件路径集合
   */
  paths?: Array<string>
}

/**
 * 卖家申请退货响应
 */
export class SellerApplyAfterSaleResponse {
  /**
   * 是否成功
   */
  success: boolean
  /**
   * 售后单号
   */
  orderAfterSaleNo: string
  /**
   * 错误代码
   */
  code: string
  /**
   * 错误信息
   */
  message: string
}

/**
 * APPLY_INVOICE_NO_OPEN(&quot;30001&quot;, &quot;当前订单不开放申请发票&quot;),
APPLY_INVOICE_NO_ALLOW(&quot;30002&quot;, &quot;当前订单不允许申请发票&quot;),
APPLY_INVOICE_EXPIRE(&quot;30003&quot;, &quot;当前订单索取发票日期已经截止&quot;),
APPLY_INVOICE_ALREADY(&quot;30004&quot;, &quot;当前订单已经申请开过发票&quot;);
APPLY_INVOICE_DIRECTLY_NO_ALLOW(&quot;30005&quot;, &quot;当前订单不允许直接申请发票&quot;);
 */
export class ApplyInvoiceResult {
  /**
   * 是否允许开票
   */
  isAllow: boolean
  /**
   * 失败错误代码
@see 192.168.1.225:8090/pages/viewpage.action?pageId&#x3D;39748749
   */
  code: string
  /**
   * 错误信息
   */
  message: string
}

/**
 * 批次单发票信息
<AUTHOR>
@since 2022/3/25
 */
export class BatchOrderInvoiceResult {
  /**
   * 发票种类
1 - 普通发票
2 - 增值税普通发票
3 - 增值税专用发票
   */
  invoiceCategory: number
  /**
   * 发票类型
1 - 电子票
2 - 纸质票
   */
  invoiceType: number
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型
1 - 个人
2 - 企业
   */
  titleType: number
  /**
   * 纳税人识别号
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账户
   */
  account: string
  /**
   * 联系电子邮箱
   */
  email: string
  /**
   * 发票票面备注
   */
  remark: string
  /**
   * 联系电话
   */
  contactPhone: string
  /**
   * 开票方式
1 - 线上开票
2 - 线下开票
   */
  invoiceMethod: number
  /**
   * 营业执照
   */
  businessLicensePath: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath: string
  /**
   * 配送方式
0/1/2,无/自取/快递
   */
  shippingMethod: number
  /**
   * 配送地址信息
   */
  deliveryAddress: PrepareBatchPayDeliveryAddress
  /**
   * 自取点信息
   */
  takePoint: PrepareBatchPayTakePoint
}

export class PrepareBatchPayDeliveryAddress {
  /**
   * 收件人
   */
  consignee: string
  /**
   * 电话
   */
  phone: string
  /**
   * 所在物理地区
   */
  region: string
  /**
   * 地址
   */
  address: string
}

export class PrepareBatchPayTakePoint {
  /**
   * 领取地点
   */
  pickupLocation: string
  /**
   * 领取时间
   */
  pickupTime: string
  /**
   * 备注
   */
  remark: string
}

/**
 * 申请批次订单退货结果
<AUTHOR>
 */
export class ApplyBatchOrderReturnResponse {
  /**
   * 申请退货业务code
   */
  code: string
  /**
   * 申请退货信息
   */
  message: string
  data: ApplyBatchOrderReturnData
}

export class ApplyBatchOrderReturnData {
  /**
   * 生成的批次退货单号
   */
  batchReturnOrderNo: string
}

/**
 * APPLY_INVOICE_NO_OPEN(&quot;30001&quot;, &quot;当前订单不开放申请发票&quot;),
APPLY_INVOICE_NO_ALLOW(&quot;30002&quot;, &quot;当前订单不允许申请发票&quot;),
APPLY_INVOICE_EXPIRE(&quot;30003&quot;, &quot;当前订单索取发票日期已经截止&quot;),
APPLY_INVOICE_ALREADY(&quot;30004&quot;, &quot;当前订单已经申请开过发票&quot;);
 */
export class ApplyInvoiceResponse {
  /**
   * 是否允许开票
   */
  isAllow: boolean
  /**
   * 失败错误代码
   */
  code: string
  /**
   * 错误信息
   */
  message: string
}

/**
 * 请求线下支付结果
<AUTHOR> create 2021/2/4 15:16
 */
export class ApplyOfflinePaymentResultResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 交易号（付款流水号）
   */
  payFlowNo: string
}

/**
 * 请求线上支付结果
<AUTHOR> create 2021/2/4 15:16
 */
export class ApplyOnlinePaymentResultResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 去支付的地址
   */
  payUrl: string
  /**
   * 请求支付结果
   */
  result: boolean
  /**
   * 失败错误代码，仅当result&#x3D;false时有值
培训券支付渠道返回错误代码：
261-培训券已使用
262-培训券应用条件不符合
263-培训券已过期
264-培训券已作废
265-已兑换过【xxx】的线上培训课程
266-当前券仅用于兑换【xxx】工种的课程
267-今年内已参加过该工种培训
269-培训券余额不足
   */
  code: string
  /**
   * 错误信息
   */
  message: string
  /**
   * 支付网关支付模式
0-同步
1-重定向
   */
  payMode: number
  /**
   * 交易号（付款流水号）
   */
  payFlowNo: string
}

/**
 * 申请订单换货结果
<AUTHOR>
 */
export class ApplyOrderExchangeResponse {
  /**
   * 申请退货业务code
(&quot;3002&quot;, &quot;当前渠道下，商品定价信息不存在&quot;);
(&quot;3003&quot;, &quot;换货商品价格信息与原订单商品价格不一致&quot;);
(&quot;3004&quot;, &quot;当前渠道下，存在无效的商品优惠信息&quot;);
(&quot;3005&quot;, &quot;目标商品已下架&quot;);
(&quot;3006&quot;, &quot;商品授权无效&quot;);
(&quot;3007&quot;, &quot;目标商品存在未付款订单&quot;)
   */
  code: string
  /**
   * 申请退货信息
   */
  message: string
  data: ApplyOrderExchangeData
}

export class ApplyOrderExchangeData {
  /**
   * 生成的换货单号
   */
  exchangeOrderNo: string
}

/**
 * 申请订单退货结果
<AUTHOR>
 */
export class ApplyOrderReturnResponse {
  /**
   * 申请退货业务code
   */
  code: string
  /**
   * 申请退货信息
   */
  message: string
  data: ApplyOrderReturnData
}

export class ApplyOrderReturnData {
  /**
   * 生成的退货单号
   */
  returnOrderNo: string
}

/**
 * 批次单直接申请发票响应
<AUTHOR>
 */
export class BatchOrderApplyInvoiceDirectlyResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 更新批次单申请发票响应
NO_APPLY_INVOICE(&quot;60001&quot;, &quot;当前订单未申请发票&quot;),
CHANGE_INVOICE_NO_ALLOW(&quot;60002&quot;, &quot;当前订单状态不允许修改发票&quot;);
<AUTHOR>
 */
export class BatchOrderInvoiceChangeResponse {
  /**
   * 是否允许修改发票
   */
  isAllow: boolean
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 取消订单结果
<AUTHOR> create 2021/1/29 17:27
 */
export class CancelOrderResultResponse {
  /**
   * 是否取消成功
   */
  success: boolean
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 取消结果信息
   */
  message: string
  /**
   * 商品验证结果信息
   */
  resultList: Array<VerifyResultResponse>
}

/**
 * 创建订单结果
<AUTHOR> create 2021/1/29 17:27
 */
export class CreateOrderResultResponse {
  /**
   * 是否创建成功
   */
  success: boolean
  /**
   * 订单号，仅当{@link #success}为{@code true}时有值
   */
  orderNo: string
  /**
   * 订单创建时间，仅当{@link #success}为{@code true}时有值
   */
  createTime: string
  /**
   * 下单结果信息
   */
  message: string
  /**
   * 商品验证结果信息
   */
  resultList: Array<VerifyResultResponse>
}

/**
 * 发票配置
<AUTHOR>
@since 2022/3/24
 */
export class InvoiceConfigResponse {
  /**
   * 开放发票类型
0 - 不开放
1 - 自主选择
2 - 强制提供
   */
  openInvoiceType: number
  /**
   * 是否允许索取发票
当openInvoiceType&#x3D;0时，该值为null
   */
  allowAskFor: boolean
  /**
   * 索取发票年度类型
1 - 当年度
2 - 下一个年度
当openInvoiceType&#x3D;0时，该值为null
   */
  askForInvoiceYearType: number
  /**
   * 索取发票截止日期，格式（MM/dd）,如：5月3日，则05/03
当openInvoiceType&#x3D;0时，该值为null
   */
  askForInvoiceDeadline: string
  /**
   * 不同发票种类下发票配置
   */
  allowInvoiceCategoryList: Array<InvoiceCategoryConfig>
}

export class InvoiceCategoryConfig {
  /**
   * 允许开具的发票种类
1 - 普通发票
2 - 增值税普通发票
3 - 增值税专用发票
当openInvoiceType&#x3D;0时，该值为null
   */
  invoiceCategory: number
  /**
   * 开票方式
1 - 线上开票
2 - 线下开票
当openInvoiceType&#x3D;0时，该值为null
   */
  invoiceMethod: number
  /**
   * 允许发票抬头
1 - 个人
2 - 企业
当openInvoiceType&#x3D;0时，该值为null
   */
  invoiceTitleList: Array<number>
  /**
   * 发票类型
1 - 电子票
2 - 纸质票
   */
  invoiceType: number
  /**
   * 发票备注类型
0-未配置
1-学员填写（包括集体报名管理员）
2-统一配置
   */
  invoiceRemarksType: number
  /**
   * 发票备注内容
   */
  invoiceRemarksContent: string
}

/**
 * 订单发票配送信息
<AUTHOR>
@since 2022/4/26
 */
export class OrderInvoiceDeliveryAddressResponse {
  /**
   * 收件人
   */
  consignee: string
  /**
   * 电话
   */
  phone: string
  /**
   * 所在物理地区
   */
  region: string
  /**
   * 地址
   */
  address: string
}

/**
 * 订单发票自取信息
<AUTHOR>
@since 2022/4/26
 */
export class OrderInvoiceTakePointResponse {
  /**
   * 领取地点
   */
  pickupLocation: string
  /**
   * 领取时间
   */
  pickupTime: string
  /**
   * 备注
   */
  remark: string
}

export class PaymentChannelInfoResponse {
  /**
   * 名称
   */
  name: string
  /**
   * logo地址
   */
  logoPath: string
  /**
   * 支付渠道编号
支付宝:ALIPAY
微信：WXPAY
   */
  channelId: string
}

/**
 * 准备开票返回信息
<AUTHOR> By Cb
@date 2022/4/12 15:08
 */
export class PrepareApplyInvoiceResponse {
  /**
   * 发票配置返回
   */
  invoiceConfigResult: InvoiceConfigResponse
}

/**
 * 准备下单返回信息
 */
export class PrepareOrderResponse {
  /**
   * 发票配置返回
   */
  invoiceConfigResult: InvoiceConfigResponse
}

/**
 * 准备支付返回值
<AUTHOR>
@since 2022/3/24
 */
export class PreparePayResponse {
  /**
   * 支付渠道信息
   */
  paymentChannelInfoList: Array<PaymentChannelInfoResponse>
  /**
   * 订单信息
   */
  orderInfo: OrderResponse
}

/**
 * 订单发票信息
<AUTHOR>
@since 2022/3/25
 */
export class OrderInvoiceResponse {
  /**
   * 发票种类
1 - 普通发票
2 - 增值税普通发票
3 - 增值税专用发票
   */
  invoiceCategory: number
  /**
   * 发票类型
1 - 电子票
2 - 纸质票
   */
  invoiceType: number
  /**
   * 发票抬头
   */
  title: string
  /**
   * 发票抬头类型
1 - 个人
2 - 企业
   */
  titleType: number
  /**
   * 纳税人识别号
   */
  taxpayerNo: string
  /**
   * 地址
   */
  address: string
  /**
   * 电话
   */
  phone: string
  /**
   * 开户行
   */
  bankName: string
  /**
   * 账户
   */
  account: string
  /**
   * 联系电子邮箱
   */
  email: string
  /**
   * 发票票面备注
   */
  remark: string
  /**
   * 联系电话
   */
  contactPhone: string
  /**
   * 开票方式
1 - 线上开票
2 - 线下开票
   */
  invoiceMethod: number
  /**
   * 营业执照
   */
  businessLicensePath: string
  /**
   * 开户许可
   */
  accountOpeningLicensePath: string
  /**
   * 配送方式
0/1/2,无/自取/快递
   */
  shippingMethod: number
  /**
   * 配送地址信息
   */
  deliveryAddress: OrderInvoiceDeliveryAddressResponse
  /**
   * 自取点信息
   */
  takePoint: OrderInvoiceTakePointResponse
}

/**
 * 准备支付的订单信息
<AUTHOR>
@since 2022/3/25
 */
export class OrderResponse {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 买家编号
   */
  buyerId: string
  /**
   * 订单金额
   */
  amount: number
  /**
   * 订单状态
0 - 未确认
1 - 正常
2 - 交易完成
3 - 交易关闭
   */
  state: number
  /**
   * 支付状态
0 - 未支付
1 - 支付中
2 - 已支付
   */
  paymentState: number
  /**
   * 订单创建时间
   */
  createTime: string
  /**
   * 子订单列表
   */
  subOrders: Array<SubOrderResponse>
  /**
   * 发票信息
   */
  invoiceInfo: OrderInvoiceResponse
  /**
   * 终端
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
H5端：H5
   */
  purchaseChannelTerminal: string
  /**
   * 购买渠道类型：
1-用户自主购买
2-集体缴费
3-管理员导入
4-集体报名个人缴费
   */
  purchaseChannelType: number
}

/**
 * 子订单信息
<AUTHOR>
@since 2022/3/25
 */
export class SubOrderResponse {
  /**
   * 子订单号
   */
  subOrderNo: string
  /**
   * 商品sku编号
   */
  skuId: string
  /**
   * 商品sku名称
   */
  skuName: string
  /**
   * 商品单价
   */
  price: number
  /**
   * 商品数量
   */
  quantity: number
  /**
   * 总价
   */
  amount: number
}

/**
 * 批次单更新线下汇款凭证响应
CHANGE_PAYMENT_VOUCHER_NO_ALLOW(&quot;70002&quot;, &quot;当前订单状态不允许修改付款凭证&quot;);
<AUTHOR>
 */
export class UpdateBatchOrderOfflinePaymentVoucherResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 校验结果返回
<AUTHOR> create 2021/2/3 10:53
 */
export class VerifyResultResponse {
  /**
   * 校验结果
   */
  message: string
  /**
   * 校验code
   */
  code: string
  /**
   * 订单内的商品skuId
   */
  skuId: string
}

/**
 * 批次单请求线下支付结果
异常code
500-接口异常
40001-购买渠道、支付渠道、终端下暂无可用收款账号
APPLY_INVOICE_DIRECTLY_NO_ALLOW(&quot;30005&quot;, &quot;当前订单不允许直接申请发票&quot;);
<AUTHOR>
@since 2022/5/12
 */
export class BatchApplyOfflinePaymentAndApplyInvoiceResponse {
  /**
   * 批次订单号
   */
  batchOrderNo: string
  /**
   * 交易号（付款流水号）
   */
  payFlowNo: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 批次单请求线下支付结果
异常code
500-接口异常
40001-购买渠道、支付渠道、终端下暂无可用收款账号
<AUTHOR>
@since 2022/5/12
 */
export class BatchApplyOfflinePaymentResponse {
  /**
   * 批次订单号
   */
  batchOrderNo: string
  /**
   * 交易号（付款流水号）
   */
  payFlowNo: string
  /**
   * 状态码
   */
  code: string
  /**
   * 状态信息
   */
  message: string
}

/**
 * 请求线上支付并申请发票结果
APPLY_INVOICE_DIRECTLY_NO_ALLOW(&quot;30005&quot;, &quot;当前订单不允许直接申请发票&quot;);
<AUTHOR> create 2021/2/4 15:16
 */
export class BatchApplyOnlinePaymentAndApplyInvoiceResponse {
  /**
   * 批次单号
   */
  batchNo: string
  /**
   * 是否允许开票
   */
  isAllow: boolean
  /**
   * 付款地址
   */
  payUrl: string
  /**
   * 支付网关支付模式
<pre>
0-同步
1-重定向
</pre>
   */
  payMode: number
  /**
   * 请求支付结果
   */
  result: boolean
  code: string
  /**
   * 错误信息
   */
  message: string
}

/**
 * 请求线上支付结果
<AUTHOR> create 2021/2/4 15:16
 */
export class BatchApplyOnlinePaymentResponse {
  /**
   * 批次单号
   */
  batchNo: string
  /**
   * 付款地址
   */
  payUrl: string
  /**
   * 支付网关支付模式
<pre>
0-同步
1-重定向
</pre>
   */
  payMode: number
  /**
   * 请求支付结果
   */
  result: boolean
  /**
   * 错误信息
   */
  message: string
}

/**
 * 准备批次支付响应
<AUTHOR> By Cb
@date 2022/04/28
 */
export class PrepareBatchPayResponse {
  /**
   * 支付渠道信息
   */
  paymentChannelInfoList: Array<PaymentChannelInfoResponse>
  /**
   * 批次单信息
   */
  batchOrderResult: BatchOrderResponse
}

/**
 * 准备支付的批次单信息
<AUTHOR> By Cb
@date 2022/04/28
 */
export class BatchOrderResponse {
  /**
   * 批次单号
   */
  batchOrderNo: string
  /**
   * 买家编号
   */
  buyerId: string
  /**
   * 订单金额
   */
  amount: number
  /**
   * 订单状态
0 - 未确认
1 - 正常
2 - 交易完成
3 - 交易关闭
   */
  state: BatchOrderState
  /**
   * 支付状态
0 - 未支付
1 - 支付中
2 - 已支付
   */
  paymentState: PaymentState
  /**
   * 订单创建时间
   */
  createTime: string
  /**
   * 发票信息
   */
  invoiceResult: BatchOrderInvoiceResult
  /**
   * 终端
Web端：Web
IOS端：IOS
安卓端：Android
微信小程序：WechatMini
微信公众号：WechatOfficial
H5端：H5
   */
  purchaseChannelTerminal: string
  /**
   * 购买渠道类型：
1-用户自主购买
2-集体缴费
3-管理员导入
4-集体报名个人缴费
   */
  purchaseChannelType: number
  /**
   * 订单总数
   */
  orderTotal: number
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 买家申请取消订单
   * @param applyCancelOrder 申请取消订单信息
   * @return 取消订单结果
   * @param mutate 查询 graphql 语法文档
   * @param applyCancelOrder 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCancelOrder(
    applyCancelOrder: BuyerApplyCancelOrderRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyCancelOrder,
    operation?: string
  ): Promise<Response<CancelOrderResultResponse>> {
    return commonRequestApi<CancelOrderResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyCancelOrder },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 索取订单发票
   * @param orderInvoiceRequest 发票信息
   * @param mutate 查询 graphql 语法文档
   * @param orderInvoiceRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyInvoice(
    orderInvoiceRequest: ApplyOrderInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyInvoice,
    operation?: string
  ): Promise<Response<ApplyInvoiceResponse>> {
    return commonRequestApi<ApplyInvoiceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { orderInvoiceRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 索取订单发票校验
   * @param orderInvoiceRequest 发票信息
   * @param mutate 查询 graphql 语法文档
   * @param orderInvoiceRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyInvoiceValidate(
    orderInvoiceRequest: ApplyOrderInvoiceValidateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyInvoiceValidate,
    operation?: string
  ): Promise<Response<ApplyInvoiceResponse>> {
    return commonRequestApi<ApplyInvoiceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { orderInvoiceRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批次单申请发票
   * @param applyInvoiceParam 申请发票信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInvoiceParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchApplyInvoice(
    applyInvoiceParam: BatchOrderApplyInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchApplyInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInvoiceParam },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批次单直接申请发票（非补要发票）
   * @param mutate 查询 graphql 语法文档
   * @param applyInvoiceParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchApplyInvoiceDirectly(
    applyInvoiceParam: BatchOrderApplyInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchApplyInvoiceDirectly,
    operation?: string
  ): Promise<Response<BatchOrderApplyInvoiceDirectlyResponse>> {
    return commonRequestApi<BatchOrderApplyInvoiceDirectlyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInvoiceParam },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批次单申请发票校验
   * @param applyInvoiceValidateParam 申请发票信息
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param applyInvoiceValidateParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchApplyInvoiceValidate(
    applyInvoiceValidateParam: BatchOrderApplyInvoiceValidateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchApplyInvoiceValidate,
    operation?: string
  ): Promise<Response<ApplyInvoiceResult>> {
    return commonRequestApi<ApplyInvoiceResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInvoiceValidateParam },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 更新批次单申请发票信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchInvoiceChange(
    request: BatchOrderInvoiceChangeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchInvoiceChange,
    operation?: string
  ): Promise<Response<BatchOrderInvoiceChangeResponse>> {
    return commonRequestApi<BatchOrderInvoiceChangeResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 买家申请退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async buyerApplyReturn(
    request: BuyerApplyOrderReturnRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.buyerApplyReturn,
    operation?: string
  ): Promise<Response<ApplyOrderReturnResponse>> {
    return commonRequestApi<ApplyOrderReturnResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 取消集体报名
   * @param batchNo 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelCollectiveSignup(
    params: { batchNo?: string; reasonId?: string; reason?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.cancelCollectiveSignup,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建订单
   * 接口异常code定义：http://192.168.1.225:8090/pages/viewpage.action?pageId=39748749
   * @param createOrderInfo 创建参数
   * @return 订单创建序列号
   * @param mutate 查询 graphql 语法文档
   * @param createOrderInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOrder(
    createOrderInfo: CreateOrderRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createOrder,
    operation?: string
  ): Promise<Response<CreateOrderResultResponse>> {
    return commonRequestApi<CreateOrderResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createOrderInfo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取集体报名状态
   * 0 - 已创建，未提交
   * 1 - 正常
   * 2 - 交易完成
   * 3 - 交易关闭
   * 4 - 提交处理中
   * 5 - 取消处理中
   * @param collectiveSignupNo 集体报名编号
   * @param mutate 查询 graphql 语法文档
   * @param collectiveSignupNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findBatchOrderBatchPayStatus(
    collectiveSignupNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.findBatchOrderBatchPayStatus,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: mutate,
        variables: { collectiveSignupNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批次线下支付
   * @param mutate 查询 graphql 语法文档
   * @param batchOrderOfflinePayParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async offlinePayBatchOrder(
    batchOrderOfflinePayParam: BatchOrderOfflinePaymentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.offlinePayBatchOrder,
    operation?: string
  ): Promise<Response<BatchApplyOfflinePaymentResponse>> {
    return commonRequestApi<BatchApplyOfflinePaymentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { batchOrderOfflinePayParam },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批次线下支付并申请发票
   * <p>
   * 本方法用于处理批次订单的线下支付逻辑，并在支付成功后根据请求中的发票信息申请发票
   * @param request 包含批次订单线下支付和发票申请信息的请求对象
   * @return 返回批次线下支付并申请发票的响应对象
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async offlinePayBatchOrderAndApplyInvoice(
    request: BatchOrderOfflinePaymentAndInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.offlinePayBatchOrderAndApplyInvoice,
    operation?: string
  ): Promise<Response<BatchApplyOfflinePaymentAndApplyInvoiceResponse>> {
    return commonRequestApi<BatchApplyOfflinePaymentAndApplyInvoiceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 线下支付
   * @param request:
   * @return {@link ApplyOfflinePaymentResultResponse}
   * <AUTHOR> By Cb
   * @date 2022/5/13 16:23
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async offlinePayOrder(
    request: OrderOfflinePaymentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.offlinePayOrder,
    operation?: string
  ): Promise<Response<ApplyOfflinePaymentResultResponse>> {
    return commonRequestApi<ApplyOfflinePaymentResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批次线上支付
   * @param mutate 查询 graphql 语法文档
   * @param batchOrderOnlinePayParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async onlinePayBatchOrder(
    batchOrderOnlinePayParam: BatchOrderOnlinePaymentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.onlinePayBatchOrder,
    operation?: string
  ): Promise<Response<BatchApplyOnlinePaymentResponse>> {
    return commonRequestApi<BatchApplyOnlinePaymentResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { batchOrderOnlinePayParam },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批次线上支付并申请发票
   * <p>
   * 本方法用于处理批次订单的线上支付，并在支付成功后申请发票
   * 它首先将请求映射为批次线上支付请求，并发送命令进行支付处理
   * 如果支付成功且请求中包含了发票信息，则调用申请发票的方法
   * @param request 包含批次订单线上支付和申请发票信息的请求对象
   * @return 返回批次线上支付并申请发票的响应对象，包含处理结果
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async onlinePayBatchOrderAndApplyInvoice(
    request: BatchOrderOnlinePaymentAndApplyInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.onlinePayBatchOrderAndApplyInvoice,
    operation?: string
  ): Promise<Response<BatchApplyOnlinePaymentAndApplyInvoiceResponse>> {
    return commonRequestApi<BatchApplyOnlinePaymentAndApplyInvoiceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 线上支付
   * @param onlinePaymentRequest 线上支付信息
   * @return 线上支付结果
   * @param mutate 查询 graphql 语法文档
   * @param onlinePaymentRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async onlinePayOrder(
    onlinePaymentRequest: OrderOnlinePaymentRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.onlinePayOrder,
    operation?: string
  ): Promise<Response<ApplyOnlinePaymentResultResponse>> {
    return commonRequestApi<ApplyOnlinePaymentResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { onlinePaymentRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 准备开票
   * @param orderNo:
   * @return {@link PrepareApplyInvoiceResponse}
   * <AUTHOR> By Cb
   * @date 2022/4/12 15:09
   * @param mutate 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async prepareApplyInvoice(
    orderNo: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.prepareApplyInvoice,
    operation?: string
  ): Promise<Response<PrepareApplyInvoiceResponse>> {
    return commonRequestApi<PrepareApplyInvoiceResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { orderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 准备批次支付
   * @param request 请求参数
   * @return 返回值
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async prepareBatchOrderPay(
    request: PrepareBatchPayRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.prepareBatchOrderPay,
    operation?: string
  ): Promise<Response<PrepareBatchPayResponse>> {
    return commonRequestApi<PrepareBatchPayResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 准备支付
   * @param request 请求参数
   * @return 返回值
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async preparePay(
    request: PreparePayRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.preparePay,
    operation?: string
  ): Promise<Response<PreparePayResponse>> {
    return commonRequestApi<PreparePayResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 准备下单
   * 购买渠道类型：
   * 1-用户自主购买
   * 2-集体缴费
   * 3-管理员导入
   * 4-集体报名个人缴费
   * @param purchaseChannelType 购买渠道类型，见备注
   * @return PrepareOrderResponse 准备下单配置信息
   * @param mutate 查询 graphql 语法文档
   * @param purchaseChannelType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async preparePlaceOrder(
    purchaseChannelType: number,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.preparePlaceOrder,
    operation?: string
  ): Promise<Response<PrepareOrderResponse>> {
    return commonRequestApi<PrepareOrderResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { purchaseChannelType },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 卖家申请退货(订单售后服务领域)
   * @param request 申请退货请求
   * @return 申请退货响应
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sellerApplyAfterSale(
    request: SellerApplyAfterSaleRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sellerApplyAfterSale,
    operation?: string
  ): Promise<Response<SellerApplyAfterSaleResponse>> {
    return commonRequestApi<SellerApplyAfterSaleResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请批次退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sellerApplyBatchReturn(
    request: SellerApplyBatchOrderReturnRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sellerApplyBatchReturn,
    operation?: string
  ): Promise<Response<ApplyBatchOrderReturnResponse>> {
    return commonRequestApi<ApplyBatchOrderReturnResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 卖家申请换货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sellerApplyExchange(
    request: SellerApplyOrderExchangeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sellerApplyExchange,
    operation?: string
  ): Promise<Response<ApplyOrderExchangeResponse>> {
    return commonRequestApi<ApplyOrderExchangeResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 卖家申请退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sellerApplyReturn(
    request: SellerApplyOrderReturnRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sellerApplyReturn,
    operation?: string
  ): Promise<Response<ApplyOrderReturnResponse>> {
    return commonRequestApi<ApplyOrderReturnResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 卖家取消订单
   * @param sellerCancelOrder 卖家申请取消信息
   * @return 取消订单结果
   * @param mutate 查询 graphql 语法文档
   * @param sellerCancelOrder 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sellerCancelOrder(
    sellerCancelOrder: SellerCancelOrderRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sellerCancelOrder,
    operation?: string
  ): Promise<Response<CancelOrderResultResponse>> {
    return commonRequestApi<CancelOrderResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { sellerCancelOrder },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批次单更新线下汇款凭证
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateBatchOfflinePaymentVouchers(
    request: UpdateBatchOrderOfflinePaymentVoucherRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateBatchOfflinePaymentVouchers,
    operation?: string
  ): Promise<Response<UpdateBatchOrderOfflinePaymentVoucherResponse>> {
    return commonRequestApi<UpdateBatchOrderOfflinePaymentVoucherResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批次单重新开票
   * @param request 批次单发票更新信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateBatchOrderInvoice(
    request: UpdateBatchOrderInvoiceRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateBatchOrderInvoice,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
