<template>
  <div class="pure">
    <!--补要发票-->
    <el-drawer
      title="补开发票"
      :visible.sync="show"
      size="900px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <el-form ref="form" :model="reapplyInvoiceForm" label-width="170px" class="m-form f-mt10">
          <el-form-item label="发票类型：" required>
            <el-radio-group v-model="invoiceType" @change="handleInvoiceType">
              <el-radio v-for="item in filterInvoiceTypeList" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="发票抬头：" required>
            <el-radio-group v-model="invoiceTitleType" @change="handleInvoiceTitleType">
              <el-radio v-for="item in filterInvoiceTitleTypeList" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
            <div class="f-mt10">
              <el-input v-model="reapplyInvoiceForm.invoiceTitle" clearable placeholder="请输入抬头" class="form-l" />
            </div>
          </el-form-item>
          <el-form-item label="统一社会信用代码：" required v-show="unitInfoVisible">
            <el-input
              v-model="reapplyInvoiceForm.taxpayerNo"
              clearable
              placeholder="请输入18位统一社会信用代码"
              class="form-l"
              maxlength="18"
              @input="toUp"
            />
          </el-form-item>
          <el-form-item label=" " class="is-text" v-show="unitInfoVisible">
            <span class="f-co">注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span>
          </el-form-item>
          <div class="bg-gray f-plr20 f-mb20 f-pb10" v-show="unitInfoVisible">
            <div class="f-pt5 f-pb10">
              <el-divider content-position="left">
                <span class="f-cr" v-if="invoiceType != 3">* 以下内容请根据需要填写，请全部填写或者全部不填写。</span>
                <span class="f-cr" v-if="invoiceType === 3"
                  >注：开企业抬头发票须填写统一社会信用代码，以免影响报销。</span
                >
              </el-divider>
            </div>
            <el-form-item label="开户银行：" :required="invoiceType === 3">
              <el-input v-model="reapplyInvoiceForm.bankName" clearable placeholder="请输入开户银行" class="form-l" />
            </el-form-item>
            <el-form-item label="开户帐号：" :required="invoiceType === 3">
              <el-input v-model="reapplyInvoiceForm.account" clearable placeholder="请输入开户帐号" class="form-l" />
            </el-form-item>
            <el-form-item label="注册电话：" :required="invoiceType === 3">
              <el-input
                v-model="reapplyInvoiceForm.rePhone"
                maxlength="20"
                clearable
                placeholder="请输入单位注册电话"
                class="form-m"
              />
            </el-form-item>
            <el-form-item label="注册地址：" :required="invoiceType === 3">
              <el-input
                maxlength="100"
                v-model="reapplyInvoiceForm.address"
                clearable
                placeholder="请输入单位注册地址"
              />
            </el-form-item>
          </div>
          <el-form-item label="手机号：" required v-show="offlineInfoVisible">
            <el-input
              v-model="reapplyInvoiceForm.phone"
              clearable
              placeholder="请输入11位手机号码"
              class="form-l"
              maxlength="11"
            />
          </el-form-item>
          <el-form-item label="电子邮箱：" required v-show="offlineInfoVisible">
            <el-input v-model="reapplyInvoiceForm.email" clearable placeholder="请输入电子邮箱" class="form-l" />
          </el-form-item>
          <!--          <el-form-item label="统一社会信用代码证：" v-show="distributeInfoVisible" required>-->
          <!--            <upload-images :limit="1" v-model="reapplyInvoiceForm.taxpayerNoLicense" />-->
          <!--          </el-form-item>-->
          <!--          <el-form-item label="开户许可证：" v-show="distributeInfoVisible" required>-->
          <!--            <upload-images :limit="1" v-model="reapplyInvoiceForm.opensAnAccountLicence" />-->
          <!--          </el-form-item>-->
        </el-form>
        <div class="m-sub-tit" v-show="distributeInfoVisible && isShow">
          <span class="tit-txt">配送信息</span>
        </div>
        <el-form
          ref="form"
          :model="distributeForm"
          label-width="150px"
          class="m-form f-mt10"
          v-show="distributeInfoVisible && isShow"
        >
          <el-form-item label="配送方式：">
            <el-radio-group v-model="distributeType" @change="handleDistributeType">
              <el-radio v-for="item in filterDistributeTypeList" :key="item.value" :label="item.value">
                {{ item.label }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="收货人：" required v-if="!selfPickUpVisible">
            <el-input
              v-model="distributeForm.distributeConsignee"
              clearable
              placeholder="请输入收货人"
              class="form-s"
            />
          </el-form-item>
          <el-form-item label="手机号：" required v-if="!selfPickUpVisible">
            <el-input v-model="distributeForm.distributePhone" clearable placeholder="请输入手机号" class="form-m" />
          </el-form-item>
          <el-form-item label="所在地区：" required v-if="!selfPickUpVisible">
            <biz-region-cascader v-model="distributeForm.distributeRegion" placeholder="请选择所在地区" />
          </el-form-item>
          <el-form-item label="详细地址：" required v-if="!selfPickUpVisible">
            <el-input v-model="distributeForm.distributeAddress" clearable placeholder="请输入详细地址" />
          </el-form-item>
          <el-form-item label=" " class="is-text" v-if="selfPickUpVisible">
            <div
              class="take-address"
              :class="{ 'is-checked': curPickUpIndex == index }"
              v-for="(item, index) in pickUpList"
              :key="item.id"
              @click="changeCurIndex(index)"
            >
              <div class="label"><i class="hb-iconfont icon-finish"></i></div>
              <el-form ref="form" label-width="auto" class="m-form">
                <el-form-item label="领取时间：" class="is-text">
                  {{ item.openTakeTime }}
                </el-form-item>
                <el-form-item label="领取地点：" class="is-text">
                  {{ item.address }}
                </el-form-item>
                <el-form-item label="备注：" class="is-text">
                  {{ item.remark }}
                </el-form-item>
              </el-form>
            </div>
          </el-form-item>
          <!-- <el-form-item label="邮编：">
            <el-input
              v-model="distributeForm.distributePostCode"
              clearable
              placeholder="请输入邮编信息"
              class="form-s"
            />
          </el-form-item> -->
        </el-form>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="cancelReApplyInvoice">取消</el-button>
        <el-button type="primary" @click="saveInvoice">保存发票信息</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, PropSync, Vue, Watch, Prop } from 'vue-property-decorator'
  import { TitleTypeEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import { DeliveryWayEnum } from '@api/service/management/trade/single/invoice/enum/DeliveryInvoiceEnum'
  import UploadImages from '@hbfe/jxjy-admin-components/src/upload-images.vue'
  import BizRegionCascader from '@hbfe/jxjy-admin-components/src/biz/biz-region-cascader.vue'
  import { DistributionWayEnum } from '@api/service/management/online-school-config/distribution-channels-config/enum/DistributionWayEnum'
  import TakePlaceDetailVo from '@api/service/management/online-school-config/distribution-channels-config/query/vo/TakePlaceDetailVo'
  import QueryDeliverWayTypeList from '@api/service/common/trade-config/query/QueryDeliverWayTypeList'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { DeliveryAddress, InvoiceInfoRequest, TakePoint } from '@api/ms-gateway/ms-order-v1'
  import { InvoiceConfigResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import QueryDeliverWayList from '@api/service/common/trade-config/query/QueryDeliverWayList'
  import DeliverWayType, { DeliverWayTypeEnum } from '@api/service/common/trade-config/query/enums/DeliverWayType'
  import MutationModifyDeliverWay from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationModifyDeliverWay'
  import { cloneDeep } from 'lodash'

  // 【UI】发票类型
  enum InvoiceTypeUIEnum {
    // 增值税电子普通发票（自动开票）
    VAT_ELECTRONIC_INVOICE = 1,

    // 增值税电子发票（线下开票）
    VAT_OFFLINE_INVOICE = 2,

    // 增值税专用发票
    VAT_SPECIAL_INVOICE = 3
  }

  //【UI】补要发票模型
  class ReapplyInvoiceUIModel {
    // 发票抬头
    invoiceTitle = ''

    // 统一社会信用代码
    taxpayerNo = ''

    // 开户银行
    bankName = ''

    // 开户账号
    account = ''

    // 注册电话
    rePhone = ''

    // 注册地址
    address = ''

    // 统一社会信用代码证
    taxpayerNoLicense: any[] = []

    // 开户许可证
    opensAnAccountLicence: any[] = []

    // 手机号码
    phone = ''

    // 电子邮箱
    email = ''
  }

  // 【UI】收货信息
  class DistributeUIModel {
    // 收货人
    distributeConsignee = ''

    // 手机号
    distributePhone = ''

    // 地区
    distributeRegion: string[] = []

    // 详细地址
    distributeAddress = ''

    // 邮编地址
    distributePostCode = ''
  }

  @Component({
    components: { BizRegionCascader, UploadImages }
  })
  export default class extends Vue {
    // 是否展示
    @PropSync('visible', {
      type: Boolean
    })
    show!: boolean
    // 用户手机号码
    @Prop({
      type: String,
      default: ''
    })
    phone: string
    @Prop({
      type: String,
      default: ''
    })
    userName: string //传进来的初始名称

    @Watch('phone')
    phoneWatch(val: string) {
      this.phone = val
    }
    // 订单号
    @Prop({
      type: String,
      default: ''
    })
    orderNo: string

    @Watch('orderNo')
    orderNoWatch(val: string) {
      this.orderNo = val
    }
    // 发票配置
    @Prop({
      type: Object,
      default: ''
    })
    invoiceConfig: InvoiceConfigResponse

    @Watch('invoiceConfig', { deep: true })
    invoiceConfigWatch(val: InvoiceConfigResponse) {
      if (val.allowInvoiceCategoryList) {
        this.invoiceConfig = val
        for (let i = 0; i < this.invoiceConfig.allowInvoiceCategoryList.length; i++) {
          if (
            this.invoiceConfig.allowInvoiceCategoryList.length === 1 &&
            this.invoiceConfig.allowInvoiceCategoryList[i].invoiceCategory === 2
          ) {
            if (this.invoiceConfig.allowInvoiceCategoryList[i].invoiceMethod === 1) {
              this.invoiceTypeList[0].visible = true
              this.invoiceTypeList[1].visible = false
              this.invoiceTypeList[2].visible = false
            } else {
              this.invoiceTypeList[0].visible = false
              this.invoiceTypeList[1].visible = true
              this.invoiceTypeList[2].visible = false
            }
            const invoiceTitleList = this.invoiceConfig.allowInvoiceCategoryList[i].invoiceTitleList
            for (let j = 0; j < invoiceTitleList.length; j++) {
              if (invoiceTitleList.length === 1 && invoiceTitleList[j] === 1) {
                this.invoiceTitleTypeList[0].visible = true
                this.invoiceTitleTypeList[1].visible = false
              }
              if (invoiceTitleList.length === 1 && invoiceTitleList[j] === 2) {
                this.invoiceTitleTypeList[1].visible = true
                this.invoiceTitleTypeList[0].visible = false
              }
              if (invoiceTitleList.length === 2 && invoiceTitleList[j] === 1) {
                this.invoiceTitleTypeList[0].visible = true
              }
              if (invoiceTitleList.length === 2 && invoiceTitleList[j] === 2) {
                this.invoiceTitleTypeList[1].visible = true
              }
            }
          }
          if (
            this.invoiceConfig.allowInvoiceCategoryList.length === 2 &&
            this.invoiceConfig.allowInvoiceCategoryList[i].invoiceCategory === 2
          ) {
            if (this.invoiceConfig.allowInvoiceCategoryList[i].invoiceMethod === 1) {
              this.invoiceTypeList[0].visible = true
              this.invoiceTypeList[1].visible = false
            } else {
              this.invoiceTypeList[0].visible = false
              this.invoiceTypeList[1].visible = true
            }
            const invoiceTitleList = this.invoiceConfig.allowInvoiceCategoryList[i].invoiceTitleList
            for (let j = 0; j < invoiceTitleList.length; j++) {
              if (invoiceTitleList.length === 1 && invoiceTitleList[j] === 1) {
                this.invoiceTitleTypeList[0].visible = true
                this.invoiceTitleTypeList[1].visible = false
              }
              if (invoiceTitleList.length === 1 && invoiceTitleList[j] === 2) {
                this.invoiceTitleTypeList[1].visible = true
                this.invoiceTitleTypeList[0].visible = false
              }
              if (invoiceTitleList.length === 2 && invoiceTitleList[j] === 1) {
                this.invoiceTitleTypeList[0].visible = true
              }
              if (invoiceTitleList.length === 2 && invoiceTitleList[j] === 2) {
                this.invoiceTitleTypeList[1].visible = true
              }
            }
          }
          if (
            this.invoiceConfig.allowInvoiceCategoryList.length === 1 &&
            this.invoiceConfig.allowInvoiceCategoryList[i].invoiceCategory === 3
          ) {
            this.invoiceTypeList[0].visible = false
            this.invoiceTypeList[1].visible = false
            this.invoiceTypeList[2].visible = true
          }
          if (
            this.invoiceConfig.allowInvoiceCategoryList.length === 2 &&
            this.invoiceConfig.allowInvoiceCategoryList[i].invoiceCategory === 3
          ) {
            this.invoiceTypeList[2].visible = true
          }
        }
      }
      for (let i = 0; i < this.invoiceTypeList.length; i++) {
        if (this.invoiceTypeList[i].visible) {
          this.invoiceType = this.invoiceTypeList[i].value
          break
        }
      }
      for (let i = 0; i < this.invoiceTitleTypeList.length; i++) {
        if (this.invoiceTitleTypeList[i].visible) {
          this.invoiceTitleType = this.invoiceTitleTypeList[i].value
          break
        }
      }
    }
    @Watch('invoiceType')
    invoiceTypeWatch() {
      if (this.invoiceType === 3) {
        this.invoiceTitleTypeList[1].visible = true
        this.invoiceTitleType = 2
      }
    }
    @Watch('mutationModifyDeliverWay.deliverWays', { deep: true })
    wayListWatch(val: Array<DeliverWayTypeEnum>) {
      this.mutationModifyDeliverWay.deliverWays = val
      if (this.mutationModifyDeliverWay.deliverWays.length <= 0) {
        this.isShow = false
      }
      if (this.mutationModifyDeliverWay.deliverWays.length === 1) {
        this.isShow = true
        if (this.mutationModifyDeliverWay.deliverWays[0] === 1) {
          this.distributeType = DeliveryWayEnum.COURIER
          this.distributeTypeList[0].visible = true
          this.distributeTypeList[1].visible = false
        }
        if (this.mutationModifyDeliverWay.deliverWays[0] === 2) {
          this.distributeType = DeliveryWayEnum.SELFFETCHED
          this.distributeTypeList[0].visible = false
          this.distributeTypeList[1].visible = true
        }
      }
      if (this.mutationModifyDeliverWay.deliverWays.length === 2) {
        this.isShow = true
        this.distributeType = DeliveryWayEnum.COURIER
        this.distributeTypeList[0].visible = true
        this.distributeTypeList[1].visible = true
      }
    }
    isShow = false // 是否显示配送信息
    // 自取点索引
    curPickUpIndex = 0
    // 自取点列表
    pickUpList: Array<TakePlaceDetailVo> = new Array<TakePlaceDetailVo>()
    queryDeliverWayTypeList = new QueryDeliverWayTypeList()
    // 发票实例
    mutationInvoiceObj = TradeModule.singleTradeBatchFactor.invoiceFactor.mutationInvoice

    //发票抬头类型枚举
    TitleTypeEnum = TitleTypeEnum
    // 发票类型
    invoiceType: InvoiceTypeUIEnum = InvoiceTypeUIEnum.VAT_ELECTRONIC_INVOICE
    // 发票抬头类型
    invoiceTitleType: TitleTypeEnum = TitleTypeEnum.PERSONAL
    // 配送方式
    distributeType: DeliveryWayEnum = DeliveryWayEnum.COURIER
    // 补要发票表单
    reapplyInvoiceForm: ReapplyInvoiceUIModel = new ReapplyInvoiceUIModel()
    // 收获信息表单
    distributeForm: DistributeUIModel = new DistributeUIModel()
    // 自取点信息
    queryDeliverWayList: QueryDeliverWayList = new QueryDeliverWayList()
    mutationModifyDeliverWay: MutationModifyDeliverWay = new MutationModifyDeliverWay()
    wayList = new DeliverWayType().list()
    // 发票类型列表
    invoiceTypeList = [
      { label: '增值税电子普通发票（自动开票）', value: InvoiceTypeUIEnum.VAT_ELECTRONIC_INVOICE, visible: false },
      { label: '增值税电子发票（线下开票）', value: InvoiceTypeUIEnum.VAT_OFFLINE_INVOICE, visible: false },
      { label: '增值税专用发票', value: InvoiceTypeUIEnum.VAT_SPECIAL_INVOICE, visible: false }
    ]
    // 配送方式列表
    distributeTypeList = [
      { label: '快递', value: DeliveryWayEnum.COURIER, visible: false },
      { label: '自取', value: DeliveryWayEnum.SELFFETCHED, visible: false }
    ]
    // 发票抬头类型列表
    invoiceTitleTypeList = [
      { label: '个人', value: TitleTypeEnum.PERSONAL, visible: false },
      { label: '单位', value: TitleTypeEnum.UNIT, visible: false }
    ]

    @Watch('reapplyInvoiceForm', {
      deep: true
    })
    reapplyInvoiceFormChange(val: any) {
      console.log('reapplyInvoiceForm', JSON.stringify(val))
    }

    @Watch('distributeForm', {
      deep: true
    })
    distributeFormChange(val: any) {
      console.log('distributeForm', val)
    }

    //【已过滤】发票类型列表
    get filterInvoiceTypeList() {
      return this.invoiceTypeList.filter((item) => item.visible)
    }

    // 【已过滤】发票抬头类型列表
    get filterInvoiceTitleTypeList() {
      return this.invoiceTitleTypeList.filter((item) => item.visible)
    }

    // 【已过滤】配送方式列表
    get filterDistributeTypeList() {
      return this.distributeTypeList.filter((item) => item.visible)
    }

    // 【是否是企业发票】是否允许填写统一社会信用代码、开户信息
    get unitInfoVisible() {
      return this.invoiceTitleType === TitleTypeEnum.UNIT
    }

    // 【是否是线下发票】是否允许填写手机号码、电子邮箱
    get offlineInfoVisible() {
      return this.invoiceType === InvoiceTypeUIEnum.VAT_OFFLINE_INVOICE
    }

    // 【是否是专用发票-单位】是否允许配置配送信息
    get distributeInfoVisible() {
      return this.invoiceType === InvoiceTypeUIEnum.VAT_SPECIAL_INVOICE && this.invoiceTitleType === TitleTypeEnum.UNIT
    }

    // 【配送方式】是否自取
    get selfPickUpVisible() {
      return this.distributeType === DeliveryWayEnum.SELFFETCHED
    }
    // 切换发票类型响应事件
    async handleInvoiceType(val: InvoiceTypeUIEnum) {
      await this.getPickUpInfo()
      this.reapplyInvoiceForm.invoiceTitle = this.userName
      // 专票默认选中单位
      if (val === InvoiceTypeUIEnum.VAT_SPECIAL_INVOICE) {
        const item = this.invoiceTitleTypeList.find((item) => item.value === TitleTypeEnum.PERSONAL)
        item.visible = false
        this.invoiceTitleType = TitleTypeEnum.UNIT
      } else {
        for (let i = 0; i < this.invoiceConfig.allowInvoiceCategoryList.length; i++) {
          if (this.invoiceConfig.allowInvoiceCategoryList[i].invoiceCategory === 2) {
            const invoiceTitleList = this.invoiceConfig.allowInvoiceCategoryList[i].invoiceTitleList
            for (let j = 0; j < invoiceTitleList.length; j++) {
              if (invoiceTitleList.length === 1 && invoiceTitleList[j] === 1) {
                this.invoiceTitleTypeList[0].visible = true
                this.invoiceTitleTypeList[1].visible = false
              }
              if (invoiceTitleList.length === 1 && invoiceTitleList[j] === 2) {
                this.invoiceTitleTypeList[1].visible = true
                this.invoiceTitleTypeList[0].visible = false
              }
              if (invoiceTitleList.length === 2 && invoiceTitleList[j] === 1) {
                this.invoiceTitleTypeList[0].visible = true
              }
              if (invoiceTitleList.length === 2 && invoiceTitleList[j] === 2) {
                this.invoiceTitleTypeList[1].visible = true
              }
            }
          }
          if (this.invoiceConfig.allowInvoiceCategoryList[i].invoiceCategory === 3) {
            this.invoiceTypeList[2].visible = true
          }
        }
        if (this.invoiceTitleTypeList[0].visible) {
          this.invoiceTitleType = TitleTypeEnum.PERSONAL
        }
      }
      this.reapplyInvoiceForm = new ReapplyInvoiceUIModel()
      this.reapplyInvoiceForm.phone = this.phone
    }
    titleName = ''
    isFirst = true
    // 切换发票抬头类型响应事件
    handleInvoiceTitleType(val: TitleTypeEnum) {
      if (this.isFirst) {
        this.titleName = this.reapplyInvoiceForm.invoiceTitle
        this.isFirst = false
      } else {
        if (val === TitleTypeEnum.UNIT) {
          this.titleName = this.reapplyInvoiceForm.invoiceTitle
        }
      }
      this.reapplyInvoiceForm = new ReapplyInvoiceUIModel()
      this.reapplyInvoiceForm.phone = this.phone
      if (val === TitleTypeEnum.PERSONAL) {
        // 如果之前的抬头名称与用户名一致，则使用用户名；否则使用之前的抬头名称
        this.reapplyInvoiceForm.invoiceTitle = this.titleName
      }
    }

    // 切换配送方式响应事件
    handleDistributeType(val: DistributionWayEnum) {
      this.distributeForm = new DistributeUIModel()
    }

    // 小写转换大写
    toUp(val: string) {
      this.reapplyInvoiceForm.taxpayerNo = val.toUpperCase().replace(/\s+/g, '')
    }

    // 取消补要发票
    cancelReApplyInvoice() {
      this.reapplyInvoiceForm = new ReapplyInvoiceUIModel()
      this.show = false
    }

    // 表单验证
    validateForm() {
      let valid = false
      // 单位正则：中文、英文、数字、《》、（）、()、-、—
      const unitReg = new RegExp(/^[\u4e00-\u9fa5a-zA-Z0-9《》—\-（）]+$/)
      // 用户名称正则：中文、·
      const userNameReg = new RegExp(/^[\u4e00-\u9fa5]+·?[\u4e00-\u9fa5]+$/)
      // 手机号正则
      const phoneReg = new RegExp(/^1[3-9]\d{9}$/)
      // 电子邮箱正则
      const emailReg = new RegExp(/^([a-zA-Z\d])(\w|\/-)+@[a-zA-Z\d]+\.[a-zA-Z]{2,4}$/)
      // 统一社会信用代码正则
      const taxpayerNoReg = new RegExp(/^[A-Za-z0-9]{18}$/)
      // 邮编正则
      const postCodeReg = new RegExp(/^\d{6}$/)

      const reapplyInvoice = this.reapplyInvoiceForm
      const distributeInfo = this.distributeForm

      if (!reapplyInvoice.invoiceTitle) {
        this.$message.error('请输入发票抬头')
        return valid
      }
      // 个人
      if (this.invoiceTitleType === TitleTypeEnum.PERSONAL) {
        if (!userNameReg.test(reapplyInvoice.invoiceTitle)) {
          this.$message.error('请输入正确的个人名称')
          return valid
        }
        // 增值税电子发票（线下开票）
        if (this.invoiceType === InvoiceTypeUIEnum.VAT_OFFLINE_INVOICE) {
          if (!reapplyInvoice.phone) {
            this.$message.error('请输入11位手机号码')
            return valid
          }
          if (!phoneReg.test(reapplyInvoice.phone)) {
            this.$message.error('请输入正确的11位手机号码')
            return valid
          }
          if (!reapplyInvoice.email) {
            this.$message.error('请输入电子邮箱')
            return valid
          }
          if (!emailReg.test(reapplyInvoice.email)) {
            this.$message.error('请输入正确的电子邮箱')
            return valid
          }
        }
      }

      // 单位
      if (this.invoiceTitleType === TitleTypeEnum.UNIT) {
        if (!unitReg.test(reapplyInvoice.invoiceTitle)) {
          this.$message.error('单位名称特殊符号仅支持《》、—、-、（）')
          return valid
        }

        if (!reapplyInvoice.taxpayerNo) {
          this.$message.error('请输入18位统一社会信用代码')
          return valid
        }
        if (!taxpayerNoReg.test(reapplyInvoice.taxpayerNo)) {
          this.$message.error('请输入正确的18位统一社会信用代码')
          return valid
        }
        if (reapplyInvoice.bankName || reapplyInvoice.account || reapplyInvoice.rePhone || reapplyInvoice.address) {
          if (!reapplyInvoice.bankName) {
            this.$message.error('请填写开户银行')
            return valid
          }
          if (!reapplyInvoice.account) {
            this.$message.error('请填写开户账号')
            return valid
          }
          if (!reapplyInvoice.rePhone) {
            this.$message.error('请填写注册电话')
            return valid
          }
          if (!reapplyInvoice.address) {
            this.$message.error('请填写注册地址')
          }
          if (reapplyInvoice.address.indexOf('·') != -1) {
            this.$message.error('注册地址暂不支持特殊字符“  · ”')
          }
        }
        // 增值税电子发票（线下开票）
        if (this.invoiceType === InvoiceTypeUIEnum.VAT_OFFLINE_INVOICE) {
          if (!reapplyInvoice.phone) {
            this.$message.error('请输入11位手机号码')
            return valid
          }
          if (!phoneReg.test(reapplyInvoice.phone)) {
            this.$message.error('请输入正确的11位手机号码')
            return valid
          }
          if (!reapplyInvoice.email) {
            this.$message.error('请输入电子邮箱')
            return valid
          }
          if (!emailReg.test(reapplyInvoice.email)) {
            this.$message.error('请输入正确的电子邮箱')
            return valid
          }
        }
        // 增值税专用发票
        if (this.invoiceType === InvoiceTypeUIEnum.VAT_SPECIAL_INVOICE) {
          if (!this.reapplyInvoiceForm.bankName) {
            this.$message.error('请填写开户银行')
            return valid
          }
          if (!this.reapplyInvoiceForm.account) {
            this.$message.error('请填写开户帐号')
            return valid
          }
          if (!this.reapplyInvoiceForm.rePhone) {
            this.$message.error('请填写单位注册电话')
            return valid
          }
          if (!this.reapplyInvoiceForm.address) {
            this.$message.error('请填写单位注册地址')
            return valid
          }
          // if (!this.reapplyInvoiceForm.taxpayerNoLicense || this.reapplyInvoiceForm.taxpayerNoLicense.length <= 0) {
          //   this.$message.error('请上传统一社会信用代码证！')
          //   return valid
          // }
          // if (
          //   !this.reapplyInvoiceForm.opensAnAccountLicence ||
          //   this.reapplyInvoiceForm.opensAnAccountLicence.length <= 0
          // ) {
          //   this.$message.error('请上传开户许可证！')
          //   return valid
          // }
          // 快递
          if (this.distributeType === DeliveryWayEnum.COURIER) {
            const region = distributeInfo.distributeRegion
            const isEmptyRegion = Array.isArray(region) && region.length ? false : true
            if (!distributeInfo.distributeConsignee) {
              this.$message.error('请输入收货人')
              return valid
            }
            if (!distributeInfo.distributePhone) {
              this.$message.error('请输入手机号')
              return valid
            }
            if (!phoneReg.test(distributeInfo.distributePhone)) {
              this.$message.error('请输入正确的手机号')
              return valid
            }

            if (isEmptyRegion) {
              this.$message.error('请选择所在地区')
              return valid
            }
            if (!distributeInfo.distributeAddress) {
              this.$message.error('请输入详细地址')
              return valid
            }
            // if (distributeInfo.distributePostCode && !postCodeReg.test(distributeInfo.distributePostCode)) {
            //   this.$message.error('请输入正确的邮编')
            //   return valid
            // }
          }
          // 自取
          if (this.distributeType === DeliveryWayEnum.SELFFETCHED) {
            if (!this.pickUpList || this.pickUpList.length <= 0) {
              this.$message.error('请先配置自取点')
              return valid
            }
          }
        }
      }
      valid = true
      return valid
    }

    // 选择自取地点
    changeCurIndex(index: number) {
      this.curPickUpIndex = index
    }

    // 获取自取点
    async getPickUpInfo() {
      try {
        this.curPickUpIndex = 0
        await this.queryDeliverWayTypeList.query()
        this.pickUpList = this.queryDeliverWayTypeList.pickUpList
        await this.queryDeliverWayList.queryList()
        this.mutationModifyDeliverWay.setDeliverWays(this.queryDeliverWayList.typeList)
      } catch (e) {
        console.log(e)
      }
    }

    // 重置表单
    resetForm() {
      this.reapplyInvoiceForm = new ReapplyInvoiceUIModel()
      this.distributeForm = new DistributeUIModel()
    }

    // 保存发票
    async saveInvoice() {
      const validResult = this.validateForm()
      const invoiceInfo = new InvoiceInfoRequest()
      if (!validResult) return
      invoiceInfo.invoiceCategory = this.invoiceType // 发票种类
      invoiceInfo.titleType = this.invoiceTitleType // 发票抬头类型
      invoiceInfo.title = this.reapplyInvoiceForm.invoiceTitle // 发票抬头
      if (this.invoiceType === 3) {
        invoiceInfo.invoiceType = 2 // 纸质发票
      } else {
        invoiceInfo.invoiceType = 1 // 电子发票
      }
      if (this.invoiceTitleType === 2) {
        invoiceInfo.taxpayerNo = this.reapplyInvoiceForm.taxpayerNo // 纳税人识别号
        invoiceInfo.bankName = this.reapplyInvoiceForm.bankName // 开户银行
        invoiceInfo.account = this.reapplyInvoiceForm.account // 开户号
        invoiceInfo.phone = this.reapplyInvoiceForm.rePhone // 注册电话
        invoiceInfo.address = this.reapplyInvoiceForm.address // 注册地址
      }
      if (this.invoiceType === 1) {
        invoiceInfo.invoiceCategory = 2
        invoiceInfo.invoiceMethod = 1
      }
      if (this.invoiceType === 2) {
        invoiceInfo.invoiceMethod = 2
        invoiceInfo.contactPhone = this.reapplyInvoiceForm.phone // 手机号
        invoiceInfo.email = this.reapplyInvoiceForm.email // 电子邮箱
      }
      if (this.invoiceType === 3) {
        invoiceInfo.invoiceMethod = 2
        // if (this.reapplyInvoiceForm.taxpayerNoLicense && this.reapplyInvoiceForm.taxpayerNoLicense.length > 0) {
        //   invoiceInfo.businessLicensePath = this.reapplyInvoiceForm.taxpayerNoLicense[0].url // 统一社会信用代码证
        // }
        // if (this.reapplyInvoiceForm.opensAnAccountLicence && this.reapplyInvoiceForm.opensAnAccountLicence.length > 0) {
        //   invoiceInfo.accountOpeningLicensePath = this.reapplyInvoiceForm.opensAnAccountLicence[0].url // 开户许可证
        // }
        invoiceInfo.invoiceVerifyStrategy = 2
        if (this.selfPickUpVisible) {
          invoiceInfo.shippingMethod = 1 // 自取
          invoiceInfo.takePoint = new TakePoint()
          invoiceInfo.takePoint.pickupLocation = this.pickUpList[this.curPickUpIndex].address // 自取地点
          invoiceInfo.takePoint.pickupTime = this.pickUpList[this.curPickUpIndex].openTakeTime // 自取时间
          invoiceInfo.takePoint.remark = this.pickUpList[this.curPickUpIndex].remark // 备注
        } else {
          invoiceInfo.deliveryAddress = new DeliveryAddress()
          invoiceInfo.shippingMethod = 2 // 快递
          invoiceInfo.deliveryAddress.consignee = this.distributeForm.distributeConsignee // 收货人
          invoiceInfo.deliveryAddress.phone = this.distributeForm.distributePhone // 手机号
          if (this.distributeForm.distributeRegion.length === 1) {
            invoiceInfo.deliveryAddress.region = this.distributeForm.distributeRegion[0] // 地区
          }
          if (this.distributeForm.distributeRegion.length === 2) {
            // 地区
            invoiceInfo.deliveryAddress.region =
              this.distributeForm.distributeRegion[0] + '/' + this.distributeForm.distributeRegion[1]
          }
          if (this.distributeForm.distributeRegion.length === 3) {
            // 地区
            invoiceInfo.deliveryAddress.region =
              this.distributeForm.distributeRegion[0] +
              '/' +
              this.distributeForm.distributeRegion[1] +
              '/' +
              this.distributeForm.distributeRegion[2]
          }
          invoiceInfo.deliveryAddress.address = this.distributeForm.distributeAddress // 地址
        }
      }
      const params = {
        orderNo: this.orderNo,
        invoiceInfo: invoiceInfo
      }
      const res = await this.mutationInvoiceObj.applyInvoice(params)
      // if (res.status.isSuccess()) {
      //   this.$message.success('补要发票成功')
      //   this.$emit('searchBase')
      //   this.show = false
      // } else {
      //   this.$message.error('补要发票失败')
      // }
      if (res.data.code == '30001') {
        this.$message.success('当前订单不开放申请发票')
      } else if (res.data.code == '30002') {
        this.$message.error('当前订单不允许申请发票')
      } else if (res.data.code == '30003') {
        this.$message.error('当前订单索取发票日期已经截止')
      } else {
        this.$message.success('补要发票成功')
        this.$emit('searchBase')
        this.show = false
      }
    }
  }
</script>
<style lang="scss" scoped>
  .pure {
    margin: 0;
    padding: 0;
  }
</style>
