import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 问卷时间状态
 */
export enum QuestionnaireStatisEnum {
  /**
   * 问卷未开始
   */
  before = 1,
  /**
   * 问卷可提交
   */
  in = 2,
  /**
   * 问卷已结束
   */
  after = 3,
  /**
   * 问卷被停用
   */
  close = 4
}
class QuestionnaireStatis extends AbstractEnum<QuestionnaireStatisEnum> {
  static enum = QuestionnaireStatisEnum

  constructor(status?: QuestionnaireStatisEnum) {
    super()
    this.current = status
    this.map.set(QuestionnaireStatisEnum.before, '问卷未开始')
    this.map.set(QuestionnaireStatisEnum.in, '问卷可提交')
    this.map.set(QuestionnaireStatisEnum.after, '问卷已结束')
  }
}
export default QuestionnaireStatis
