<template>
  <div>
    <el-upload
      ref="upload"
      action="#"
      list-type="picture-card"
      :auto-upload="false"
      :limit="limit"
      :file-list="fileList"
      :multiple="true"
      class="m-pic-upload long-pic"
      :before-remove="handleBeforeRemove"
    >
      <div slot="default" class="upload-placeholder" @click.stop="showCropper">
        <i class="el-icon-plus"></i>
        <p class="txt">上传图片</p>
      </div>
      <div slot="file" slot-scope="{ file }" class="img-file">
        <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
        <div class="el-upload-list__item-actions">
          <span class="el-upload-list__item-preview" @click="handlePictureCardPreview(file)">
            <i class="el-icon-zoom-in"></i>
          </span>
          <span class="el-upload-list__item-delete" @click="handleRemove(file)">
            <i class="el-icon-delete"></i>
          </span>
        </div>
        <div class="other">
          <p>链接地址</p>
          <el-input
            v-model="file.address"
            clearable
            class="f-wf"
            placeholder="输入完整域名，例如：https://www.baidu.com/"
          />
        </div>
      </div>
      <div slot="tip" class="el-upload__tip">
        <i class="el-icon-warning f-mt5"></i>
        <div class="txt f-mt5">{{ shortText }}</div>
      </div>
    </el-upload>
    <!-- 剪裁组件弹窗 -->
    <vue-cropper
      ref="vueCropper"
      :visible.sync="cropperModel"
      :action="actionUrl"
      :headers="headersObj"
      :title="title"
      v-model="showUrl"
      :initWidth="initWidth"
      :dialogStyleOpation="dialogStyleOpation"
      class="vue-cropper"
      :hbOpaction="{
        ratioArr: ratioArr,
        replaceOptions: { canMoveBox: false, fixedBox: true, centerBox: false, mode: mode }
      }"
      :hasPreview="hasPreview"
    ></vue-cropper>
    <!-- 大图预览 -->
    <el-image style="width: 100px; height: 100px" :previewSrcList="previewList" v-show="false" ref="elImage">
    </el-image>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Prop, Watch, PropSync } from 'vue-property-decorator'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import VueCropper from '@hbfe-vue-components/image-cropper'
  import { ingress } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

  @Component({
    components: {
      VueCropper
    }
  })
  export default class CoverImageUpload extends Vue {
    // 大图预览ref
    @Ref('elImage') elImage: any
    // 上传文件列表
    fileList: Array<any> = []
    // 文件上传限制
    limit = 1
    // 请求路径
    actionUrl = ''
    // 请求头
    headersObj = {
      'App-Authentication': '',
      Authorization: ''
    }
    url: string | null = null
    // showUrl = ''
    cropperModel = false //裁剪弹窗

    // 图片预览数组
    previewList = new Array<string>()

    @Watch('fileList', {
      deep: true,
      immediate: true
    })
    fileListChange(val: any) {
      if (val) {
        this.$emit('callback', val)
        // console.log(val, '数据变化')
      }
    }

    @Prop({ type: String, default: '', required: true }) value: string
    @Prop({ type: String, default: '班级封面' }) title: string
    @Prop({ type: Number, default: 150 }) initWidth: number
    @Prop({ type: Array, default: ['16:9'] }) ratioArr: string[]
    @Prop({ type: String }) link: string
    @Prop({ type: Boolean, default: false }) isEdit: boolean
    @Prop({ type: Boolean, default: true }) hasPreview: boolean
    @Prop({ type: String, default: '建议选择高清大图，尺寸：宽度1920PX，高度 560PX' })
    shortText?: string
    @Prop({
      type: Object,
      default: {
        width: '300px',
        height: '300px'
      }
    })
    dialogStyleOpation: object

    /**
     * 获取默认宽高
     */
    @Prop({ type: String, default: '' }) mode: string

    get showUrl() {
      if (this.value !== '') {
        const mfsHeadReg = /^\/mfs\/\.*/
        const localHeadReg = /^img\/\.*/
        if (mfsHeadReg.test(this.value) || localHeadReg.test(this.value)) {
          return this.value
        }
        return `/mfs${this.value}`
      }
      return null
    }
    set showUrl(val) {
      let url = ''
      if (val) {
        const mfsHeadReg = /^\/mfs\/\.*/
        const localHeadReg = /^img\/\.*/
        if (mfsHeadReg.test(val) || localHeadReg.test(val)) {
          url = val?.split('/mfs/')[0]
        } else {
          url = val
        }
      }
      console.log(url, 'addfff')
      this.$emit('input', url)
    }

    @Watch('showUrl', {
      immediate: true,
      deep: true
    })
    showUrlChange(val: any) {
      if (val) {
        if (!this.isEdit) {
          this.fileList.push({ url: val })
          this.previewList.push(val)
        } else {
          this.fileList = new Array<any>()
          this.previewList.push(val)
          this.fileList.push({ url: val, address: this.link })
        }
      }
    }

    @Ref('upload') upload: any
    @Ref('vueCropper') vueCropper: any

    showCropper() {
      // 点击弹出剪裁框
      this.cropperModel = true
      this.showUrl = ''
    }
    beforeClose() {
      this.cropperModel = false
    }
    mounted() {
      this.headersObj.Authorization = `Mship ${this.$authentication.getAccessToken()}`
      this.headersObj['App-Authentication'] = `Basic ${process.env.VUE_APP_KEY}`
      this.headersObj['Content-Type'] = 'application/json;charset=UTF-8'
      this.actionUrl = `${ConfigCenterModule.getIngress(ingress.apiendpoint)}/web/ms-file-v1/web/uploadPublicBase64`
    }

    /**
     * 删除封面图片
     */
    handleRemove(file: any) {
      this.fileList.find((item, index) => {
        if (item.url === file.url) {
          if (this.isEdit) {
            this.link = ''
            this.$emit('linkChange', this.link)
          }
          this.fileList.splice(index, 1)
        }
      })
    }
    /**
     * 捞仔element 解决el-upload点击Backspace会出现显示异常问题
     */
    handleBeforeRemove(file: any, fileList: any) {
      return false
    }
    // 点击预览图片
    handlePictureCardPreview(file: any) {
      this.$nextTick(() => {
        // 触发点击方法
        this.elImage.clickHandler()
      })
    }
    reset() {
      this.fileList = []
    }
  }
</script>

<style lang="scss" scoped>
  // ::v-deep.hideUpload .el-upload--picture-card {
  //   display: none;
  // }
</style>
