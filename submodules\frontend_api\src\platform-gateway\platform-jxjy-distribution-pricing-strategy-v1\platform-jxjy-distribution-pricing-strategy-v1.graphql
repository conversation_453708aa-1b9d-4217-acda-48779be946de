schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""批量将定价方案开启/关闭推广"""
	pricePlanBatchOperatorPromotion(request:PricingStrategyBatchOperatorPromotionByConditionRequest):PricingStrategyBatchResultResponse
	"""批量移除销售渠道的定价方案"""
	pricePlanBatchRemoveSaleChannel(request:PricingStrategyBatchRemoveSaleChannelByConditionRequest):PricingStrategyBatchResultResponse
}
"""定价策略批量操作推广请求, 根据条件
	<AUTHOR>
	@date 2024-08-28
"""
input PricingStrategyBatchOperatorPromotionByConditionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.pricingstrategy.PricingStrategyBatchOperatorPromotionByConditionRequest") {
	"""未选择时 根据查询条件操作数据"""
	distributorCommodityAndRelationInfo:DistributorCommodityAndRelationRequest
	"""未选择时 根据查询条件操作数据"""
	sortRequests:[DistributorCommoditySortRequest]
	"""当列表不为空则只操作该列表 - 定价策略ID 列表"""
	pricingSchemeIds:[String]
	"""【必填】销售渠道ID"""
	saleChannelId:String
	"""【必填】操作状态"""
	promotionStatus:Boolean
}
"""定价策略批量移除销售渠道的定价方案请求, 根据条件
	<AUTHOR>
	@date 2024-08-28
"""
input PricingStrategyBatchRemoveSaleChannelByConditionRequest @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.request.distribution.pricingstrategy.PricingStrategyBatchRemoveSaleChannelByConditionRequest") {
	"""未选择时 根据查询条件操作数据"""
	distributorCommodityAndRelationInfo:DistributorCommodityAndRelationRequest
	"""未选择时 根据查询条件操作数据"""
	sortRequests:[DistributorCommoditySortRequest]
	"""当列表不为空则只操作该列表 - 定价策略ID 列表"""
	pricingSchemeIds:[String]
	"""【必填】销售渠道ID"""
	saleChannelId:String
}
"""商品sku属性查询条件"""
input PropertyRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.common.PropertyRequest") {
	"""商品skuKey"""
	propertyKey:String
	"""商品skuValue"""
	propertyValue:String
}
"""供应商授权出的商品"""
input DistributorCommodityAndRelationRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.request.distributorcommodity.DistributorCommodityAndRelationRequest") {
	"""分销商商品名称"""
	saleTitle:String
	"""分销商id集合"""
	distributorIdList:[String]
	"""分销商等级"""
	distributorLevel:Int
	"""销售状态 1-有效 2-无效"""
	saleStatus:Int
	"""分销地区路径"""
	contractDistributionRegionPathList:[String]
	"""商品sku属性"""
	propertyList:[PropertyRequest]
	"""培训方案类型"""
	schemeTypeList:[String]
	"""授权价格类型 1-固定 2-区间"""
	priceType:Int
	"""分销价格范围查询-最大价格"""
	maxPrice:BigDecimal
	"""分销价格范围查询-最小价格"""
	minPrice:BigDecimal
	"""分销价格-最大价格"""
	policyMaxPrice:BigDecimal
	"""分销价格-最小价格"""
	policyMinPrice:BigDecimal
	"""定价方案状态"""
	statusList:[Int]
	"""分销是否有效
		0-有效 1-无效
		商品的分销开始时间、结束时间作为判断
	"""
	distributionStatus:Int
	"""分销商商品id集合"""
	distributorCommodityIdList:[String]
	"""商品id集合"""
	commodityIdList:[String]
	"""网校原始商品id集合"""
	rootCommodityIdList:[String]
	"""需要排除的网校原始商品id集合"""
	excludedRootCommodityIdList:[String]
	"""网校id"""
	onlineSchoolId:String
	"""培训方案名称"""
	schemeName:String
	"""网校销售状态
		0-开启 1-关闭
		商品的网校销售开始时间、结束时间作为判断
	"""
	onlineSchoolStatus:Int
	"""授权商品来源类型"""
	commoditySourceTypeList:[Int]
	"""定价方案id"""
	productPricingSchemeIdList:[String]
	"""需要排除的定价方案id"""
	excludedPricingSchemeIdList:[String]
	"""是否存在定价方案"""
	existPricingScheme:Boolean
	"""是否已启用定价方案"""
	enablePricingScheme:Boolean
	"""是否已启用优惠申请"""
	enableDiscountScheme:Boolean
	"""推广门户标识id"""
	portalIdentify:String
	"""推广门户展示名称"""
	showName:String
	"""推广门户简称"""
	shortName:String
	"""门户域名"""
	domainName:String
	"""门户状态
		0-停用 1-启用
	"""
	portalStatus:Int
	"""门户展示 (0-不展示, 1-展示）"""
	showPortal:Int
	"""门户推广 (0-不推广, 1-推广）"""
	portalPromotion:Int
	"""商品上架状态
		0-下架 1-上架
	"""
	shelveStatus:Int
	"""销售渠道类型
		0-自营渠道 1-分销渠道 2-专题渠道 3-华医网 4-推广门户渠道
	"""
	saleChannelType:Int
	"""销售渠道id"""
	saleChannelId:String
	"""优惠申请审批状态
		0-待处理 1-通过 2-未通过
	"""
	auditStatusList:[Int]
	"""优惠状态
		1-开启 2-关闭
		与优惠申请内容状态有关、与优惠周期约束、优惠开始时间、优惠结束时间有关
	"""
	discountStatusList:[Int]
}
input DistributorCommoditySortRequest @type(value:"com.fjhb.platform.jxjy.v1.query.kernel.support.sort.DistributorCommoditySortRequest") {
	"""排序字段"""
	field:DistributorCommoditySortEnum
	"""正序或倒序"""
	policy:Direction
}
enum Direction @type(value:"com.fjhb.commons.dao.page.SortPolicy$Direction") {
	ASC
	DESC
}
enum DistributorCommoditySortEnum @type(value:"com.fjhb.ms.datapedestal.kernel.enums.DistributorCommoditySortEnum") {
	RECORD_UPDATED_TIME
	RECORD_CREATED_TIME
	DISTRIBUTION_END_TIME
	SALE_CHANNEL_SORT
}
type PricingStrategyMessageRecord @type(value:"com.fjhb.ms.distribution.v1.api.entities.PricingStrategyMessageRecord") {
	pricingSchemeId:String
	message:String
}
"""定价方案 批量操作结果响应"""
type PricingStrategyBatchResultResponse @type(value:"com.fjhb.platform.jxjy.v1.kernel.gateway.graphql.response.distribution.pricingstrategy.PricingStrategyBatchResultResponse") {
	"""定价策略ID以及对应的错误信息"""
	pricingStrategyMessageRecords:[PricingStrategyMessageRecord]
	"""状态码
		@see com.fjhb.ms.distribution.v1.api.consts.PricingStrategyStatusEnum
		枚举如下：
		("200", "成功")
		("500", "失败")
		("501", "存在销售价格相同的定价方案") => 返回已存在的相同定价方案ID
		("502", "定价方案已有关联订单，无法删除")
		("503", "销售价格不在分销商品定价范围内，无法启用")
		("504", "无法同时符合三个条件") => 返回定价方案无法设置推广的 定价方案ID 以及原因
		("505", "同一个分销商品存在其他定价方案展示在门户") => 返回已设置推广的 定价方案ID
	"""
	code:String
	"""响应信息"""
	message:String
}

scalar List
