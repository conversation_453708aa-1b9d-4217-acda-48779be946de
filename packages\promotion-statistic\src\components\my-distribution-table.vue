<template>
  <el-table stripe :data="tableData" border class="m-table f-mt10" ref="tableRef">
    <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
    <el-table-column label="分销层级" min-width="180" fixed="left" class-name="tree-cell">
      <template slot-scope="{ row }">
        <el-tag type="warning">{{ row.distributionTiers.toString() }}</el-tag>
      </template>
    </el-table-column>
    <el-table-column label="上级分销商" min-width="180" fixed="left">
      <template slot-scope="scope">
        {{ getName(scope.row) }}
      </template>
    </el-table-column>
    <el-table-column label="合计" header-align="center">
      <el-table-column label="开通" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.total.open }}</template>
      </el-table-column>
      <el-table-column label="退班" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.total.return }}</template>
      </el-table-column>
      <el-table-column label="净开通" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.total.netOpen }}</template>
      </el-table-column>
      <el-table-column label="分销金额" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.total.netAmount }}</template>
      </el-table-column>
    </el-table-column>
    <el-table-column label="直接销售" header-align="center">
      <el-table-column label="开通" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.direct.open }}</template>
      </el-table-column>
      <el-table-column label="退班" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.direct.return }}</template>
      </el-table-column>
      <el-table-column label="净开通" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.direct.netOpen }}</template>
      </el-table-column>
      <el-table-column label="分销金额" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.direct.netAmount }}</template>
      </el-table-column>
    </el-table-column>
    <el-table-column label="下级销售" header-align="center">
      <el-table-column label="开通" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.subordinate.open }}</template>
      </el-table-column>
      <el-table-column label="退班" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.subordinate.return }}</template>
      </el-table-column>
      <el-table-column label="净开通" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.subordinate.netOpen }}</template>
      </el-table-column>
      <el-table-column label="分销金额" min-width="90" align="right">
        <template slot-scope="{ row }">{{ row.subordinate.netAmount }}</template>
      </el-table-column>
    </el-table-column>
    <el-table-column label="操作" width="140" align="center" fixed="right">
      <template> </template>
      <template slot-scope="scope">
        <div v-if="scope.row.distributionLevel === '一级'">
          <el-button @click="handleLowDistributeData" type="text" size="mini">查看下级分销数据</el-button>
        </div>
        <div v-else>-</div>
      </template>
    </el-table-column>
  </el-table>
</template>
<script lang="ts">
  import { Component, Ref, Vue, Prop, Watch } from 'vue-property-decorator'
  import DistributorSalesStatisticsInfo from '@api/service/management/statisticalReport/DistributorSalesStatistics/model/DistributorSalesStatisticsInfo'
  // import MyDistributorPromotionItem from '@api/service/management/statisticalReport/DistributorPromotionStatistics/model/MyDistributorPromotionItem'

  @Component
  export default class extends Vue {
    @Prop({
      type: Array,
      required: true
    })
    tableData: DistributorSalesStatisticsInfo[]
    /**
     * 监听事件
     * @param val
     */
    @Watch('tableData', {
      immediate: true
    })
    valueChange(val: string) {
      if (val && val.length) {
        ;(this.$refs['tableRef'] as any)?.doLayout()
      }
    }
    created() {
      console.log(this.tableData, 'tableData')
    }
    getName(row: any) {
      // getName(row: MyDistributorPromotionItem) {
      // TODO 只有分销商
      return (row.superType == 1 ? '供应商：' : '分销商：') + row.superName
    }
    /**
     * 查看下级分销数据
     */
    handleLowDistributeData() {
      this.$emit('toLowData')
    }
  }
</script>
