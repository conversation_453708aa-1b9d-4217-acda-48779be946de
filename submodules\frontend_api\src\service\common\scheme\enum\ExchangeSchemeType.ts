import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 换班类型枚举
 * scheme 换班
 * issue 换期
 */
export enum ExchangeSchemeTypeEnum {
  scheme = 'scheme',
  issue = 'issue'
}

/**
 * @description 换班类型
 */
class ExchangeSchemeType extends AbstractEnum<ExchangeSchemeTypeEnum> {
  static enum = ExchangeSchemeTypeEnum

  constructor(status?: ExchangeSchemeTypeEnum) {
    super()
    this.current = status
    this.map.set(ExchangeSchemeTypeEnum.scheme, '换班')
    this.map.set(ExchangeSchemeTypeEnum.issue, '换期')
  }
}

export default new ExchangeSchemeType()
