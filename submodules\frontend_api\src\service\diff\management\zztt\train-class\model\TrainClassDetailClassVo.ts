import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
import MutationTrainClassCommodityClass from '@api/service/management/train-class/mutation/MutationTrainClassCommodityClass'
import LearningType from '@api/service/management/train-class/mutation/vo/LearningType'
import CertificateVo from '@api/service/management/train-class/mutation/vo/CertificateVo'
import TrainClassBaseModel from '@api/service/diff/management/zztt/train-class/model/TrainClassBaseModel'
import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'

/**
 * 培训班商品详情Vo
 */
class TrainClassDetailClassVoDiff extends TrainClassDetailClassVo {
  /**
   *培训班基础信息，类型为TrainClassBaseModel
   */
  trainClassBaseInfo = new TrainClassBaseModel()
}
export default TrainClassDetailClassVoDiff
