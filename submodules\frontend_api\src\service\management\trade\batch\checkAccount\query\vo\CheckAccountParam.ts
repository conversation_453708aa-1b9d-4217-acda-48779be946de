/*
 * @Description: 查询列表请求参数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-17 19:42:10
 */

import {
  BatchOrderBasicDataRequest,
  BatchOrderStatusChangeTimeRequest,
  BigDecimalScopeExcelRequest,
  DateScopeRequest,
  OrderPayInfoRequest
} from '@api/ms-gateway/ms-data-export-front-gateway-DataExportBackstage'
import { BatchOrderRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import SaleChannelType, { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { PaymentMethodEnum } from '@api/service/management/trade/batch/order/enum/PaymentMethod'
export default class CheckAccountParam {
  /**
   * 收款账号ID
   */
  paymentAccountID?: string
  /**
   * 批次号
   */
  orderId?: string
  /**
   * 交易流水号
   */
  batchId?: string
  /**
   * 开始时间
   */
  startDate?: string
  /**
   * 结束时间
   */
  endDate?: string
  /**
   * 专题名称
   */
  specialSubjectName = ''

  /**
   * 是否来源专题
   */
  isFromSpecialSubject: boolean = undefined

  /**
   * 销售渠道
   */
  saleSource: SaleChannelEnum = null

  /**
   * 分销商id
   */
  distributorId = ''

  /**
   * 推广门户id
   */
  promotionPortalId = ''
  /**
   * 缴费方式
   */
  paymentMethod: PaymentMethodEnum = null

  static to(checkAccountParam: CheckAccountParam) {
    //
    const request = new BatchOrderRequest()
    request.batchOrderNoList = checkAccountParam.orderId ? [checkAccountParam.orderId] : undefined
    request.payInfo = new OrderPayInfoRequest()
    request.payInfo.flowNoList = checkAccountParam.batchId ? [checkAccountParam.batchId] : undefined
    request.payInfo.receiveAccountIdList = checkAccountParam.paymentAccountID
      ? [checkAccountParam.paymentAccountID]
      : undefined
    request.basicData = new BatchOrderBasicDataRequest()
    request.basicData.batchOrderStatusChangeTime = new BatchOrderStatusChangeTimeRequest()
    request.basicData.batchOrderStatusList = [2]
    request.payInfo.paymentOrderTypeList = [1, 2]
    request.basicData.batchOrderAmountScope = new BigDecimalScopeExcelRequest()
    request.basicData.batchOrderAmountScope.begin = 0.01
    request.basicData.batchOrderStatusChangeTime.completed = new DateScopeRequest()
    request.basicData.batchOrderStatusChangeTime.completed.begin = checkAccountParam.startDate
    request.basicData.batchOrderStatusChangeTime.completed.end = checkAccountParam.endDate
    if (checkAccountParam.saleSource || checkAccountParam.saleSource === SaleChannelEnum.self) {
      request.basicData.saleChannels = [checkAccountParam.saleSource]
    } else {
      request.basicData.saleChannels = [SaleChannelEnum.self, SaleChannelEnum.distribution, SaleChannelEnum.topic]
    }
    request.basicData.saleChannelName = request.basicData.saleChannels.includes(SaleChannelEnum.topic)
      ? checkAccountParam.specialSubjectName
      : ''
    // request.basicData.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(
    //   checkAccountParam.isFromSpecialSubject
    // )
    if (checkAccountParam.distributorId) {
      request.distributorId = checkAccountParam.distributorId
    }
    if (checkAccountParam.promotionPortalId) {
      request.portalId = checkAccountParam.promotionPortalId
    }
    if (checkAccountParam.paymentMethod) {
      request.payInfo.paymentOrderTypeList = [checkAccountParam.paymentMethod]
    } else {
      request.payInfo.paymentOrderTypeList = []
    }
    return request
  }
}
