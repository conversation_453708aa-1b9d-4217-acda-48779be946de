import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  CourseAppraise,
  CourseAppraiseStatisticsResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

class QueryUserCourseAppraiseSummary extends CourseAppraise {
  constructor(courseId?: string) {
    super()
    this.courseId = courseId
  }

  private readonly courseId: string
  /**
   * 综合评价值
   */
  totalAppraise = 0
  /**
   * 课程评价值
   */
  courseAppraise = 0
  /**
   * 教师评价值
   */
  teacherAppraise = 0

  async queryInfo() {
    const totalAppraiseForCourse = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.getCourseAppraiseStatisticsInServicer(
      [this.courseId]
    )

    if (totalAppraiseForCourse.data && totalAppraiseForCourse.data.length > 0) {
      if (totalAppraiseForCourse.status.isSuccess()) {
        const appraiseItem = totalAppraiseForCourse.data[0]
        this.totalAppraise = appraiseItem.courseAppraisalStatistics.totalAppraise
        this.courseAppraise = appraiseItem.courseAppraisalStatistics.courseAppraise
        this.teacherAppraise = appraiseItem.courseAppraisalStatistics.teacherAppraise
      }
    } else {
      this.totalAppraise = 0
      this.courseAppraise = 0
      this.teacherAppraise = 0
    }
  }
  /**
   * 批量查课程评价
   */
  async queryInfoList(idList: Array<string>): Promise<CourseAppraiseStatisticsResponse[]> {
    const appraiseList = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.getCourseAppraiseStatisticsInServicer(
      idList
    )
    return appraiseList.data || []
  }
}

export default QueryUserCourseAppraiseSummary
