import { FieldConstraintResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import FieldConstraintVo from './FieldConstraintVo'

class WorkServiceIndustryRegisterVo {
  /**
   * 技术等级
   */
  professionalLevel: FieldConstraintVo = new FieldConstraintVo()
  /**
   * 技术工种
   */
  jobCategoryId: FieldConstraintVo = new FieldConstraintVo()

  from(res: Array<FieldConstraintResponse>) {
    const resMap = new Map()
    res?.forEach(item => {
      resMap.set(item.field, item)
    })
    this.professionalLevel = FieldConstraintVo.from(resMap.get('professionalLevel'))
    this.jobCategoryId = FieldConstraintVo.from(resMap.get('jobCategoryId'))
  }
}
export default WorkServiceIndustryRegisterVo
