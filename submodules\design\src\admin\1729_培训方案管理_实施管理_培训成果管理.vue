<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">培训方案管理</el-breadcrumb-item>
      <el-breadcrumb-item>实施管理</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="m-tab-center">
      <div class="content">
        <div class="item">训前实施设置</div>
        <div class="item">培训过程管理</div>
        <div class="item z-cur">培训成果管理</div>
      </div>
    </div>
    <div class="f-p15">
      <el-tabs v-model="activeName2" type="card" class="m-tab-card">
        <el-tab-pane label="网授班" name="first">
          <el-card shadow="never" class="m-card">
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="方案名称" min-width="300" fixed="left">
                <template>这是期别完整名称</template>
              </el-table-column>
              <el-table-column label="方案属性" min-width="240">
                <template>
                  <p>年度：2025</p>
                  <p>地区：安徽省/省直</p>
                  <p>培训类别：施工现场专业人员</p>
                  <p>岗位类别：资料员</p>
                </template>
              </el-table-column>
              <el-table-column label="已报名人数" min-width="110" align="center">
                <template>50</template>
              </el-table-column>
              <el-table-column label="合格人数" min-width="110" align="center">
                <template>50</template>
              </el-table-column>
              <el-table-column label="报名起止时间" min-width="220">
                <template>
                  <p><el-tag type="info" size="mini">起始</el-tag>2021-10-15 00:21:21</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>2021-10-15 00:21:21</p>
                </template>
              </el-table-column>
              <el-table-column label="学习起止时间" min-width="220">
                <template>
                  <p><el-tag type="info" size="mini">起始</el-tag>2021-10-15 00:21:21</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>2021-10-15 00:21:21</p>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text">查看学员学习明细</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text">查看学员学习明细</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
        <el-tab-pane label="面授/面网授班" name="second">
          <el-card shadow="never" class="m-card">
            <!--条件查询-->
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="6">
                  <el-form-item label="期别名称">
                    <el-input v-model="input" clearable placeholder="请输入期别名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item label="期别编号">
                    <el-input v-model="input" clearable placeholder="请输入期别编号" />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="期别名称" min-width="300" fixed="left">
                <template>这是期别完整名称</template>
              </el-table-column>
              <el-table-column label="期别编号" min-width="240">
                <template>001</template>
              </el-table-column>
              <el-table-column label="已报名人数" min-width="110" align="center">
                <template>50</template>
              </el-table-column>
              <el-table-column label="合格人数" min-width="110" align="center">
                <template>50</template>
              </el-table-column>
              <el-table-column label="报名起止时间" min-width="220">
                <template>
                  <p><el-tag type="info" size="mini">起始</el-tag>2021-10-15 00:21:21</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>2021-10-15 00:21:21</p>
                </template>
              </el-table-column>
              <el-table-column label="学习起止时间" min-width="220">
                <template>
                  <p><el-tag type="info" size="mini">起始</el-tag>2021-10-15 00:21:21</p>
                  <p><el-tag type="info" size="mini">结束</el-tag>2021-10-15 00:21:21</p>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="200" align="center" fixed="right">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <el-button type="text">查看学员学习明细</el-button>
                  </div>
                  <div v-else>
                    <el-button type="text">查看学员学习明细</el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}, {}, {}, {}, {}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
