import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformCommodity'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformCommodity'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum QueryParamOperatePolicyEnum {
  AND = 'AND',
  OR = 'OR'
}
export enum QueryParamSortEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}

// 类

/**
 * @Description  渠道商开通数
<AUTHOR>
@Date 9:23 2021/10/22
 */
export class ChannelVendorCommodityOpenNumberQueryParamDTO {
  /**
   * 商品状态：&quot;UPED&quot;上架|&quot;DOWNED&quot;下架
   */
  commodityState?: string
  /**
   * 机构ID
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 渠道商id
   */
  channelVendorIds?: Array<string>
}

/**
 * 商品中间表查询条件
Author:FangKunSen
Time:2021-02-02,15:19
 */
export class LazyCommodityQueryParamDTO {
  /**
   * 查询条件并行策略
AND: 与| OR: 或
默认AND
全平台搜索时请设置OR策略并将所需要覆盖的条件一起赋值
   */
  policy?: QueryParamOperatePolicyEnum
  /**
   * 商品名称
   */
  commodityName?: string
  /**
   * 商品id
   */
  commodityIds?: Array<string>
  /**
   * 不包括哪些商品
   */
  excludeCommodityIds?: Array<string>
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 方案id集合
   */
  schemeIds?: Array<string>
  /**
   * 期数名称
   */
  issueName?: string
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 工种名称
   */
  workTypeName?: string
  /**
   * 工种id
   */
  workTypeId?: string
  /**
   * 工种id集合，当workTypeId有值时，该批量查询无效
   */
  workTypeIds?: Array<string>
  /**
   * 机构ID
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 机构名称
   */
  trainingInstitutionName?: string
  /**
   * 课件供应商id
   */
  coursewareSupplierIdList?: Array<string>
  /**
   * 渠道供应商id集合
add by wtl 2021年7月21日 09:19:42
   */
  channelVendorIdList?: Array<string>
  /**
   * 参训单位id集合
add by sugs 2021年10月11日 15:53:12
   */
  participatingUnitIdList?: Array<string>
  /**
   * 培训类别id(右like查询该节点及该节点以下的所有子节点)
   */
  trainingCategoryId?: string
  /**
   * 工种类别与工种完整路径（用于查询）
   */
  categoryWorkTypePath?: Array<string>
  /**
   * 适用人群名
   */
  suitableCrowNames?: Array<string>
  /**
   * 报名人数排序，如果没填则默认根据班级创建时间排序（优先于价格排序）
   */
  trainingUserNumberSortPolicy?: QueryParamSortEnum
  /**
   * 价格排序，如果没填则默认根据班级创建时间排序
   */
  priceSortPolicy?: QueryParamSortEnum
  /**
   * 渠道商报名人数排序，如果没填则默认根据班级创建时间排序（排序等级最高）
   */
  channelUserNumberSortPolicy?: QueryParamSortEnum
  /**
   * 课程评价排序
   */
  appraiseSortPolicy?: QueryParamSortEnum
  /**
   * 渠道商推广班级添加时间排序
   */
  channelAddTimeSortPolicy?: QueryParamSortEnum
  /**
   * 下架时间排序
   */
  offShelveTimeSortPolicy?: QueryParamSortEnum
  /**
   * 发布时间 起
   */
  publishTimeStart?: string
  /**
   * 发布时间 止
   */
  publishTimeEnd?: string
  /**
   * 商品状态：&quot;UPED&quot;上架|&quot;DOWNED&quot;下架
   */
  commodityState?: string
  /**
   * 商品是否开启web渠道(即是否开放报名)
   */
  allowWebChannel?: boolean
  /**
   * 商品是否有效
   */
  commodityAvailable?: boolean
  /**
   * 开通数大于等于
   */
  openNumberStart?: number
  /**
   * 开通数小于等于
   */
  openNumberEnd?: number
  /**
   * 是否是推广班级
   */
  spread?: boolean
  /**
   * @Description 渠道供应商id集合用于商品清洗查询， 因为旧的 channelVendorIdList不用于商品清洗，怕其他地方有影响这里重新定义一个
<AUTHOR>
@Date 16:44 2021/10/21
   */
  channelVendorIds?: Array<string>
  /**
   * 商品所属机构状态
true 启用
false 禁用
空 忽略
   */
  trainingInstitutionStatus?: boolean
  /**
   * 排除已授权给渠道商的商品
   */
  excludeChannelVendorIds?: Array<string>
}

/**
 * 商品带评价的查询条件
Author:FangKunSen
Time:2021-05-19,17:25
 */
export class LazyCommodityWithAppraiseQueryParamDTO {
  /**
   * 限制取几条数据
   */
  limit?: number
  /**
   * 综合评分排序（优先于开通数排序）
默认不排序
   */
  comprehensiveAppraiseSoft?: QueryParamSortEnum
  /**
   * 0分数据是否当作10分来排序处理
   */
  zeroIsTop?: boolean
  /**
   * 查询条件并行策略
AND: 与| OR: 或
默认AND
全平台搜索时请设置OR策略并将所需要覆盖的条件一起赋值
   */
  policy?: QueryParamOperatePolicyEnum
  /**
   * 商品名称
   */
  commodityName?: string
  /**
   * 商品id
   */
  commodityIds?: Array<string>
  /**
   * 不包括哪些商品
   */
  excludeCommodityIds?: Array<string>
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 方案id集合
   */
  schemeIds?: Array<string>
  /**
   * 期数名称
   */
  issueName?: string
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 工种名称
   */
  workTypeName?: string
  /**
   * 工种id
   */
  workTypeId?: string
  /**
   * 工种id集合，当workTypeId有值时，该批量查询无效
   */
  workTypeIds?: Array<string>
  /**
   * 机构ID
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 机构名称
   */
  trainingInstitutionName?: string
  /**
   * 课件供应商id
   */
  coursewareSupplierIdList?: Array<string>
  /**
   * 渠道供应商id集合
add by wtl 2021年7月21日 09:19:42
   */
  channelVendorIdList?: Array<string>
  /**
   * 参训单位id集合
add by sugs 2021年10月11日 15:53:12
   */
  participatingUnitIdList?: Array<string>
  /**
   * 培训类别id(右like查询该节点及该节点以下的所有子节点)
   */
  trainingCategoryId?: string
  /**
   * 工种类别与工种完整路径（用于查询）
   */
  categoryWorkTypePath?: Array<string>
  /**
   * 适用人群名
   */
  suitableCrowNames?: Array<string>
  /**
   * 报名人数排序，如果没填则默认根据班级创建时间排序（优先于价格排序）
   */
  trainingUserNumberSortPolicy?: QueryParamSortEnum
  /**
   * 价格排序，如果没填则默认根据班级创建时间排序
   */
  priceSortPolicy?: QueryParamSortEnum
  /**
   * 渠道商报名人数排序，如果没填则默认根据班级创建时间排序（排序等级最高）
   */
  channelUserNumberSortPolicy?: QueryParamSortEnum
  /**
   * 课程评价排序
   */
  appraiseSortPolicy?: QueryParamSortEnum
  /**
   * 渠道商推广班级添加时间排序
   */
  channelAddTimeSortPolicy?: QueryParamSortEnum
  /**
   * 下架时间排序
   */
  offShelveTimeSortPolicy?: QueryParamSortEnum
  /**
   * 发布时间 起
   */
  publishTimeStart?: string
  /**
   * 发布时间 止
   */
  publishTimeEnd?: string
  /**
   * 商品状态：&quot;UPED&quot;上架|&quot;DOWNED&quot;下架
   */
  commodityState?: string
  /**
   * 商品是否开启web渠道(即是否开放报名)
   */
  allowWebChannel?: boolean
  /**
   * 商品是否有效
   */
  commodityAvailable?: boolean
  /**
   * 开通数大于等于
   */
  openNumberStart?: number
  /**
   * 开通数小于等于
   */
  openNumberEnd?: number
  /**
   * 是否是推广班级
   */
  spread?: boolean
  /**
   * @Description 渠道供应商id集合用于商品清洗查询， 因为旧的 channelVendorIdList不用于商品清洗，怕其他地方有影响这里重新定义一个
<AUTHOR>
@Date 16:44 2021/10/21
   */
  channelVendorIds?: Array<string>
  /**
   * 商品所属机构状态
true 启用
false 禁用
空 忽略
   */
  trainingInstitutionStatus?: boolean
  /**
   * 排除已授权给渠道商的商品
   */
  excludeChannelVendorIds?: Array<string>
}

/**
 * @Description  sku查询参数
<AUTHOR>
@Date 9:23 2021/10/22
 */
export class SkuUsedQueryParamDTO {
  /**
   * 商品名 或工种名
   */
  name?: string
  /**
   * 机构ID
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 渠道商id
   */
  channelVendorIds?: Array<string>
}

/**
 * Author:FangKunSen
Time:2021-02-02,19:32
 */
export class WorkTypeCommodityOpenNumberQueryParamDTO {
  /**
   * 商品状态：&quot;UPED&quot;上架|&quot;DOWNED&quot;下架
   */
  commodityState?: string
  /**
   * 培训类别id(右like查询该节点及该节点以下的所有子节点)
   */
  trainingCategoryId?: string
  /**
   * 工种id集合
   */
  workTypeIdList?: Array<string>
  /**
   * 机构ID
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 需要取得几个工种，不传则返回全部自己取
   */
  workTypeResponseNumber?: number
  /**
   * 开通人数排序策略，默认不排序
   */
  queryParamSortEnum?: QueryParamSortEnum
  /**
   * 渠道商id
   */
  channelVendorIds?: Array<string>
  /**
   * 商品所属机构状态
true 启用
false 禁用
空 忽略
   */
  trainingInstitutionStatus?: boolean
}

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * 渠道每个工种的商品购买数
Author:FangKunSen
Time:2021-02-02,19:29
 */
export class ChannelVendorWorkTypeCommodityOpenNumberDTO {
  /**
   * 工种id
   */
  workTypeId: string
  /**
   * 渠道商id
   */
  channelVendorId: string
  /**
   * 开通人数
   */
  openNumber: number
}

/**
 * Author:FangKunSen
Time:2021-02-02,19:06
 */
export class LazyCommodityDTO {
  /**
   * 商品id
   */
  commodityId: string
  /**
   * 商品名
   */
  commoditySaleTitle: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方案名
   */
  schemeName: string
  /**
   * 期别id
   */
  stageId: string
  /**
   * 期别名
   */
  stageName: string
  /**
   * 期数id
   */
  issueId: string
  /**
   * 期数名
   */
  issueName: string
  /**
   * 方案封面图路径
   */
  schemePicturePath: string
  /**
   * 商品销售卖点
   */
  commoditySellingPoint: string
  /**
   * 商品描述介绍
   */
  commodityDescription: string
  /**
   * 商品描述介绍小程序
   */
  commodityDescriptionUniApp: string
  /**
   * 商品是否开启web渠道(即是否开放报名)
   */
  allowWebChannel: boolean
  /**
   * 商品是否开启导入开通渠道
   */
  allowImportChannel: boolean
  /**
   * 商品是否开启集体缴费渠道
   */
  allowBatchChannel: boolean
  /**
   * 机构ID
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 机构简介
   */
  trainingInstitutionAbouts: string
  /**
   * 课件供应商id
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称
   */
  coursewareSupplierName: string
  /**
   * 培训类别id(路径)
   */
  trainingCategoryId: string
  /**
   * 培训类别名称
   */
  trainingCategoryName: string
  /**
   * 培训类别 名称路径
   */
  trainingCategoryNamePath: string
  /**
   * 工种id
   */
  workTypeId: string
  /**
   * 工种name
   */
  workTypeName: string
  /**
   * 适用人群名
   */
  suitableCrowNames: Array<string>
  /**
   * 学时
   */
  period: number
  /**
   * 价格
   */
  price: number
  /**
   * 商品状态：&quot;UPED&quot;上架|&quot;DOWNED&quot;下架
   */
  commodityState: string
  /**
   * 商品是否有效
   */
  commodityAvailable: boolean
  /**
   * 培训时间 起
   */
  trainingTimeStart: string
  /**
   * 培训时间 止
   */
  trainingTimeEnd: string
  /**
   * 开通人数
   */
  openNumber: number
  /**
   * 创建者id
   */
  creatorId: string
  /**
   * 创建者名
   */
  creatorName: string
  /**
   * 上架时间
   */
  onShelveTime: string
  /**
   * 下架时间
   */
  offShelveTime: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 最后修改时间
   */
  lastUpdateTime: string
  /**
   * 价格变更记录
   */
  priceChangeHistoryDTOS: Array<PriceChangeHistoryDTO>
  /**
   * 渠道商
   */
  channelVendorList: Array<ServicerDTO>
  /**
   * 商品总评价
   */
  evaluation: number
}

/**
 * 商品带评价
Author:FangKunSen
Time:2021-05-19,17:37
 */
export class LazyCommodityWithAppraiseDTO {
  /**
   * 综合评价
   */
  comprehensiveAppraise: number
  /**
   * 商品id
   */
  commodityId: string
  /**
   * 商品名
   */
  commoditySaleTitle: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方案名
   */
  schemeName: string
  /**
   * 期别id
   */
  stageId: string
  /**
   * 期别名
   */
  stageName: string
  /**
   * 期数id
   */
  issueId: string
  /**
   * 期数名
   */
  issueName: string
  /**
   * 方案封面图路径
   */
  schemePicturePath: string
  /**
   * 商品销售卖点
   */
  commoditySellingPoint: string
  /**
   * 商品描述介绍
   */
  commodityDescription: string
  /**
   * 商品描述介绍小程序
   */
  commodityDescriptionUniApp: string
  /**
   * 商品是否开启web渠道(即是否开放报名)
   */
  allowWebChannel: boolean
  /**
   * 商品是否开启导入开通渠道
   */
  allowImportChannel: boolean
  /**
   * 商品是否开启集体缴费渠道
   */
  allowBatchChannel: boolean
  /**
   * 机构ID
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 机构简介
   */
  trainingInstitutionAbouts: string
  /**
   * 课件供应商id
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称
   */
  coursewareSupplierName: string
  /**
   * 培训类别id(路径)
   */
  trainingCategoryId: string
  /**
   * 培训类别名称
   */
  trainingCategoryName: string
  /**
   * 培训类别 名称路径
   */
  trainingCategoryNamePath: string
  /**
   * 工种id
   */
  workTypeId: string
  /**
   * 工种name
   */
  workTypeName: string
  /**
   * 适用人群名
   */
  suitableCrowNames: Array<string>
  /**
   * 学时
   */
  period: number
  /**
   * 价格
   */
  price: number
  /**
   * 商品状态：&quot;UPED&quot;上架|&quot;DOWNED&quot;下架
   */
  commodityState: string
  /**
   * 商品是否有效
   */
  commodityAvailable: boolean
  /**
   * 培训时间 起
   */
  trainingTimeStart: string
  /**
   * 培训时间 止
   */
  trainingTimeEnd: string
  /**
   * 开通人数
   */
  openNumber: number
  /**
   * 创建者id
   */
  creatorId: string
  /**
   * 创建者名
   */
  creatorName: string
  /**
   * 上架时间
   */
  onShelveTime: string
  /**
   * 下架时间
   */
  offShelveTime: string
  /**
   * 发布时间
   */
  publishTime: string
  /**
   * 最后修改时间
   */
  lastUpdateTime: string
  /**
   * 价格变更记录
   */
  priceChangeHistoryDTOS: Array<PriceChangeHistoryDTO>
  /**
   * 渠道商
   */
  channelVendorList: Array<ServicerDTO>
  /**
   * 商品总评价
   */
  evaluation: number
}

/**
 * Author:FangKunSen
Time:2021-02-02,19:15
 */
export class PriceChangeHistoryDTO {
  /**
   * 变更时间
   */
  changeTime: string
  /**
   * 操作者
   */
  operatorId: string
  /**
   * 操作者名字
   */
  operatorName: string
  /**
   * 原价格
   */
  oldPrice: number
  /**
   * 新价格
   */
  newPrice: number
}

/**
 * @Description 服务商
<AUTHOR>
@Date 15:30 2021/10/21
 */
export class ServicerDTO {
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 服务商名称
   */
  servicerName: string
}

/**
 * 已被引用的sku属性
Author:FangKunSen
Time:2021-03-09,08:39
 */
export class SkuUsedByCommodityDTO {
  /**
   * 已被引用的工种类别与工种树
   */
  workTypeCategoryWhichUsedList: Array<WorkTypeCategoryWhichUsed>
  /**
   * 已有发布商品的机构
   */
  trainingInstitutionList: Array<TrainingInstitutionBaseInfoDTO>
  /**
   * 已被引用的适用人群列表
   */
  suitablePeopleList: Array<string>
}

/**
 * 机构基础信息
Author:FangKunSen
Time:2021-03-08,19:53
 */
export class TrainingInstitutionBaseInfoDTO {
  /**
   * 机构id
   */
  id: string
  /**
   * 机构名字
   */
  name: string
  /**
   * 首字母(给前端用，后端暂时不设置)
   */
  letter: string
}

/**
 * 工种基础信息
Author:FangKunSen
Time:2021-03-08,16:11
 */
export class WorkTypeBaseInfo {
  /**
   * id
   */
  id: string
  /**
   * 名字
   */
  name: string
}

/**
 * 已被（商品）使用的工种类别树,包含工种信息
目前如果有子类别（hasChild&#x3D;true）则不会挂工种，反之亦如此
Author:FangKunSen
Time:2021-03-08,16:05
 */
export class WorkTypeCategoryWhichUsed {
  /**
   * 工种类别id
   */
  id: string
  /**
   * 工种类别name
   */
  name: string
  /**
   * 类别父id
   */
  parentId: string
  /**
   * 是否有子类别（是否没有挂工种）
   */
  hasChild: boolean
  /**
   * 子类别
   */
  children: Array<WorkTypeCategoryWhichUsed>
  /**
   * 所挂工种基础信息
   */
  workTypeList: Array<WorkTypeBaseInfo>
  /**
   * 排序值
   */
  sort: number
}

/**
 * 每个工种的商品购买数
Author:FangKunSen
Time:2021-02-02,19:29
 */
export class WorkTypeCommodityOpenNumberDTO {
  /**
   * 工种id
   */
  workTypeId: string
  /**
   * 开通人数
   */
  openNumber: number
}

export class LazyCommodityWithAppraiseDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LazyCommodityWithAppraiseDTO>
}

export class LazyCommodityDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LazyCommodityDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * @Description 渠道商推广交易成功的学员报名次数(机构授权给渠道商推广的的商品开通数)
   * <AUTHOR>
   * @Date 8:59 2021/10/22
   * @param: paramDTO
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async countCommodityChannelVendorOpenNumber(
    paramDTO: ChannelVendorCommodityOpenNumberQueryParamDTO,
    query: DocumentNode = GraphqlImporter.countCommodityChannelVendorOpenNumber,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取班级总数
   * @param paramDTO 商品查询参数
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async countCommodityNumber(
    paramDTO: LazyCommodityQueryParamDTO,
    query: DocumentNode = GraphqlImporter.countCommodityNumber,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 功能描述：渠道供应商：获取当前渠道供应商班级总数
   * @param paramDTO : 商品查询参数
   * @return : int
   * @Author： wtl
   * @Date： 2021/7/20 11:42
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async countCommodityNumberForChannelVendor(
    paramDTO: LazyCommodityQueryParamDTO,
    query: DocumentNode = GraphqlImporter.countCommodityNumberForChannelVendor,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 功能描述：课件供应商：获取当前课件供应商班级总数
   * @param paramDTO : 商品查询参数
   * @return : int
   * @Author： wtl
   * @Date： 2021/7/20 11:42
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async countCommodityNumberForCoursewareSupplier(
    paramDTO: LazyCommodityQueryParamDTO,
    query: DocumentNode = GraphqlImporter.countCommodityNumberForCoursewareSupplier,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 功能描述：机构：获取当前机构班级总数
   * @param paramDTO : 商品查询参数
   * @return : int
   * @Author： wtl
   * @Date： 2021/7/20 11:42
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async countCommodityNumberForTrainingInstitution(
    paramDTO: LazyCommodityQueryParamDTO,
    query: DocumentNode = GraphqlImporter.countCommodityNumberForTrainingInstitution,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取当前已开班的工种数
   * @param commodityState 指定一个商品状态，如果没指定则返回所有
   * @return
   * @param query 查询 graphql 语法文档
   * @param commodityState 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async countWorkTypeHasCommodityNumber(
    commodityState: string,
    query: DocumentNode = GraphqlImporter.countWorkTypeHasCommodityNumber,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { commodityState },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取某个商品
   * @param commodityId
   * @return
   * @param query 查询 graphql 语法文档
   * @param commodityId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLazyCommodityById(
    commodityId: string,
    query: DocumentNode = GraphqlImporter.getLazyCommodityById,
    operation?: string
  ): Promise<Response<LazyCommodityDTO>> {
    return commonRequestApi<LazyCommodityDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { commodityId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取所有被使用的sku
   * @return
   * @param query 查询 graphql 语法文档
   * @param name 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSkuWhichUsedByCommodity(
    name: string,
    query: DocumentNode = GraphqlImporter.getSkuWhichUsedByCommodity,
    operation?: string
  ): Promise<Response<SkuUsedByCommodityDTO>> {
    return commonRequestApi<SkuUsedByCommodityDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { name },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 加了机构和渠道商参数的 获取被使用的sku
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSkuWhichUsedByCommodityQuery(
    paramDTO: SkuUsedQueryParamDTO,
    query: DocumentNode = GraphqlImporter.getSkuWhichUsedByCommodityQuery,
    operation?: string
  ): Promise<Response<SkuUsedByCommodityDTO>> {
    return commonRequestApi<SkuUsedByCommodityDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询带延迟的商品集合（带评价），以评价维度展开去查商品
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listCommodityWithAppraise(
    paramDTO: LazyCommodityWithAppraiseQueryParamDTO,
    query: DocumentNode = GraphqlImporter.listCommodityWithAppraise,
    operation?: string
  ): Promise<Response<Array<LazyCommodityWithAppraiseDTO>>> {
    return commonRequestApi<Array<LazyCommodityWithAppraiseDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量获取期数商品
   * @param issueIds
   * @return
   * @param query 查询 graphql 语法文档
   * @param issueIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listIssueCommodityInfo(
    issueIds: Array<string>,
    query: DocumentNode = GraphqlImporter.listIssueCommodityInfo,
    operation?: string
  ): Promise<Response<Array<LazyCommodityDTO>>> {
    return commonRequestApi<Array<LazyCommodityDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { issueIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * @Description  根据工种id集合查询每个渠道工种报名数
   * <AUTHOR>
   * @Date 10:50 2021/10/22
   * @param: paramDTO
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listWorkTypeCommodityChannelVendorOpenNumber(
    paramDTO: WorkTypeCommodityOpenNumberQueryParamDTO,
    query: DocumentNode = GraphqlImporter.listWorkTypeCommodityChannelVendorOpenNumber,
    operation?: string
  ): Promise<Response<Array<ChannelVendorWorkTypeCommodityOpenNumberDTO>>> {
    return commonRequestApi<Array<ChannelVendorWorkTypeCommodityOpenNumberDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据工种id集合查询每个工种报名数
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param paramDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listWorkTypeCommodityOpenNumber(
    paramDTO: WorkTypeCommodityOpenNumberQueryParamDTO,
    query: DocumentNode = GraphqlImporter.listWorkTypeCommodityOpenNumber,
    operation?: string
  ): Promise<Response<Array<WorkTypeCommodityOpenNumberDTO>>> {
    return commonRequestApi<Array<WorkTypeCommodityOpenNumberDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: { paramDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询带延迟的商品集合（带评价），以评价维度展开去查商品
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCommodityWithAppraise(
    params: { page?: Page; paramDTO?: LazyCommodityWithAppraiseQueryParamDTO },
    query: DocumentNode = GraphqlImporter.pageCommodityWithAppraise,
    operation?: string
  ): Promise<Response<LazyCommodityWithAppraiseDTOPage>> {
    return commonRequestApi<LazyCommodityWithAppraiseDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取清洗商品表分页
   * @param page
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageLazyCommodity(
    params: { page?: Page; paramDTO?: LazyCommodityQueryParamDTO },
    query: DocumentNode = GraphqlImporter.pageLazyCommodity,
    operation?: string
  ): Promise<Response<LazyCommodityDTOPage>> {
    return commonRequestApi<LazyCommodityDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 渠道商：获取清洗商品表分页
   * @param page
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageLazyCommodityForChannelVendor(
    params: { page?: Page; paramDTO?: LazyCommodityQueryParamDTO },
    query: DocumentNode = GraphqlImporter.pageLazyCommodityForChannelVendor,
    operation?: string
  ): Promise<Response<LazyCommodityDTOPage>> {
    return commonRequestApi<LazyCommodityDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课件供应商：获取清洗商品表分页
   * @param page
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageLazyCommodityForCoursewareSupplier(
    params: { page?: Page; paramDTO?: LazyCommodityQueryParamDTO },
    query: DocumentNode = GraphqlImporter.pageLazyCommodityForCoursewareSupplier,
    operation?: string
  ): Promise<Response<LazyCommodityDTOPage>> {
    return commonRequestApi<LazyCommodityDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 参训单位：获取清洗商品表分页
   * @param page
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageLazyCommodityForParticipatingUnit(
    params: { page?: Page; paramDTO?: LazyCommodityQueryParamDTO },
    query: DocumentNode = GraphqlImporter.pageLazyCommodityForParticipatingUnit,
    operation?: string
  ): Promise<Response<LazyCommodityDTOPage>> {
    return commonRequestApi<LazyCommodityDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 机构：获取清洗商品表分页
   * @param page
   * @param paramDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageLazyCommodityForTrainingInstitution(
    params: { page?: Page; paramDTO?: LazyCommodityQueryParamDTO },
    query: DocumentNode = GraphqlImporter.pageLazyCommodityForTrainingInstitution,
    operation?: string
  ): Promise<Response<LazyCommodityDTOPage>> {
    return commonRequestApi<LazyCommodityDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
