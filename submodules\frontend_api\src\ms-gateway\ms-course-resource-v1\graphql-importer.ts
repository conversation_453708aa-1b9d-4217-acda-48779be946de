import exportAllCoursePackageImportResult from './queries/exportAllCoursePackageImportResult.graphql'
import exportErrorCoursePackageImportResult from './queries/exportErrorCoursePackageImportResult.graphql'
import pageCoursePackageImportTask from './queries/pageCoursePackageImportTask.graphql'
import pageCoursewareImportTask from './queries/pageCoursewareImportTask.graphql'
import batchCreateCourseware from './mutates/batchCreateCourseware.graphql'
import batchImportCoursePackage from './mutates/batchImportCoursePackage.graphql'
import checkCourse from './mutates/checkCourse.graphql'
import checkCoursePackageForName from './mutates/checkCoursePackageForName.graphql'
import checkCourseware from './mutates/checkCourseware.graphql'
import configJSON from './mutates/configJSON.graphql'
import copyCoursePackage from './mutates/copyCoursePackage.graphql'
import createCourse from './mutates/createCourse.graphql'
import createCourseCategory from './mutates/createCourseCategory.graphql'
import createCoursePackage from './mutates/createCoursePackage.graphql'
import createCourseware from './mutates/createCourseware.graphql'
import createCoursewareCategory from './mutates/createCoursewareCategory.graphql'
import disableCourse from './mutates/disableCourse.graphql'
import disableCourseware from './mutates/disableCourseware.graphql'
import enableCourse from './mutates/enableCourse.graphql'
import enableCourseware from './mutates/enableCourseware.graphql'
import exportCoursewareImportResult from './mutates/exportCoursewareImportResult.graphql'
import removeCourse from './mutates/removeCourse.graphql'
import removeCourseCategory from './mutates/removeCourseCategory.graphql'
import removeCoursePackage from './mutates/removeCoursePackage.graphql'
import removeCourseware from './mutates/removeCourseware.graphql'
import removeCoursewareCategory from './mutates/removeCoursewareCategory.graphql'
import updateCourse from './mutates/updateCourse.graphql'
import updateCourseCategory from './mutates/updateCourseCategory.graphql'
import updateCoursePackage from './mutates/updateCoursePackage.graphql'
import updateCourseware from './mutates/updateCourseware.graphql'
import updateCoursewareCategory from './mutates/updateCoursewareCategory.graphql'

export {
  exportAllCoursePackageImportResult,
  exportErrorCoursePackageImportResult,
  pageCoursePackageImportTask,
  pageCoursewareImportTask,
  batchCreateCourseware,
  batchImportCoursePackage,
  checkCourse,
  checkCoursePackageForName,
  checkCourseware,
  configJSON,
  copyCoursePackage,
  createCourse,
  createCourseCategory,
  createCoursePackage,
  createCourseware,
  createCoursewareCategory,
  disableCourse,
  disableCourseware,
  enableCourse,
  enableCourseware,
  exportCoursewareImportResult,
  removeCourse,
  removeCourseCategory,
  removeCoursePackage,
  removeCourseware,
  removeCoursewareCategory,
  updateCourse,
  updateCourseCategory,
  updateCoursePackage,
  updateCourseware,
  updateCoursewareCategory
}
