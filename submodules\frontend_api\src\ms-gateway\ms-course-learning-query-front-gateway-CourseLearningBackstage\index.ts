import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-course-learning-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-course-learning-query-front-gateway-CourseLearningBackstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum CourseDistributionSortEnum {
  COUNT = 'COUNT'
}
export enum CourseSortEnum {
  CREATE_TIME = 'CREATE_TIME'
}
export enum CourseStatusEnum {
  DISABLE = 'DISABLE',
  ENABLE = 'ENABLE'
}
export enum CoursewareStatusEnum {
  TRANSCODING = 'TRANSCODING',
  AVAILABLE = 'AVAILABLE',
  UNAVAILABLE = 'UNAVAILABLE'
}
export enum EffectiveScopeEnum {
  PLATFORM = 'PLATFORM'
}
export enum SortTypeEnum1 {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum AuditType {
  AUTO_AUDIT = 'AUTO_AUDIT',
  MANUAL_AUDIT = 'MANUAL_AUDIT'
}
export enum ExperienceType {
  SCHEME = 'SCHEME',
  COURSE = 'COURSE'
}
export enum ParticipateTimeType {
  SCHEME_TIME = 'SCHEME_TIME',
  CUSTOME_TIME = 'CUSTOME_TIME'
}
export enum ParticipateType {
  SUBMIT_FILE = 'SUBMIT_FILE',
  EDIT_ONLINE = 'EDIT_ONLINE'
}
export enum StudentLearningExperienceStatus {
  SUBMITING = 'SUBMITING',
  SUBMITTED = 'SUBMITTED',
  PASS = 'PASS',
  RETURNED = 'RETURNED'
}
export enum CourseSortRuleConfigSortEnum {
  CREATE_TIME = 'CREATE_TIME'
}
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum CourseInSchemeSortEnum {
  PERIOD = 'PERIOD',
  TOTAL_APPRAISE = 'TOTAL_APPRAISE',
  CHOOSE_COURSE_COUNT = 'CHOOSE_COURSE_COUNT',
  OUTLINE_ID = 'OUTLINE_ID',
  COURSE_TYPE = 'COURSE_TYPE',
  SORT = 'SORT',
  COURSE_CONFIG_SORT = 'COURSE_CONFIG_SORT',
  CREATE_TIME = 'CREATE_TIME'
}
export enum SortTypeEnum12 {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum StudentLearningExperienceSortEnum {
  CREATE_TIME = 'CREATE_TIME',
  START_TIME = 'START_TIME',
  IS_REQUIRED = 'IS_REQUIRED'
}
export enum StudyStatusEnum {
  UN_LEARNING = 'UN_LEARNING',
  LEARNING = 'LEARNING',
  COMPLETED = 'COMPLETED'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * <AUTHOR>
 */
export class ChooseCourseStatisticsRequest {
  /**
   * 是否剔除无选课数据
   */
  eliminate?: boolean
  /**
   * 课件供应商ID
   */
  supplierId?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 课程Id
   */
  courseId?: Array<string>
  /**
   * 选课时间开始
   */
  chooseCourseDateStart?: string
  /**
   * 选课时间结束
   */
  chooseCourseDateEnd?: string
  /**
   * 商品sku年度
   */
  year?: string
  /**
   * 课程分类
   */
  courseCategory?: string
  /**
   * 培训工种 (携带培训工种的字典id)
   */
  trainTypeOfWorkIdList?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: string
  /**
   * 课程名称
   */
  courseName?: string
}

/**
 * 课程分类查询条件
 */
export class CourseCategoryRequest {
  /**
   * 分类ID集合
   */
  categoryIdList?: Array<string>
  /**
   * 父级分类id
   */
  parentId?: string
  /**
   * 分类名称
   */
  name?: string
}

/**
 * @Description  查看课程分布情况 请求参数
<AUTHOR>
@Date 2023/4/18 10:23
 */
export class CourseDistributionRequest {
  /**
   * 课件供应商Id
   */
  supplierId?: string
  /**
   * 培训方案Id
   */
  schemeId?: string
  /**
   * 方案状态
   */
  schemeState: number
  /**
   * 分类Id
   */
  classificationId?: string
  /**
   * 课程数量范围  左值
   */
  leftCount: number
  /**
   * 课程数量范围  右值
   */
  rightCount: number
}

/**
 * @version: 1.0
@description: 课程包下课程请求参数
@author: sugs
@create: 2023-02-28 14:21
 */
export class CourseInPackageRequest {
  /**
   * 页码
   */
  pageNo: number
  /**
   * 页面条数
   */
  pageSize: number
  /**
   * 课程包id
   */
  coursePackageId: string
  /**
   * 课程id集合
   */
  courseIds?: Array<string>
  /**
   * 课程名称
   */
  courseName?: string
  /**
   * 课程名称匹配方式
   */
  courseNameMatchType?: number
}

/**
 * 方案下课程配置请求参数
<AUTHOR>
@version 1.0
@date 2022/1/6 19:22
 */
export class CourseInSchemeRequest {
  /**
   * 课程学习大纲下课程
   */
  courseOfCourseTrainingOutline?: CourseOfCourseTrainingOutlineRequest
  /**
   * 课程信息
   */
  course?: CourseInfoRequest
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 课程包id
   */
  coursePackageId?: string
}

/**
 * @description: 课程包查询条件
@author: sugs
@create: 2022-01-26 16:20
 */
export class CoursePackageRequest {
  /**
   * 课程包id集合
   */
  coursePackageId?: Array<string>
  /**
   * 课程包名称
   */
  name?: string
  /**
   * 课程包展示名称
   */
  displayName?: string
  /**
   * 是否被方案引用 0否 1是
   */
  isReferenced?: number
  /**
   * 排除的课程包id
   */
  excludeCoursePackageId?: Array<string>
}

/**
 * 课程查询条件
 */
export class CourseRequest {
  /**
   * 课程ID集合
   */
  courseIdList?: Array<string>
  /**
   * 分类ID集合
   */
  categoryIdList?: Array<string>
  /**
   * 课件供应商ID
传-1代表查询未被授权的课程
-2 为 已分配的
   */
  supplierId?: string
  /**
   * 是否排除0学时
   */
  excludeZeroPeriod?: boolean
  /**
   * 课程名称
   */
  name?: string
  /**
   * 转换状态 0.草稿 1.转换中 2.转换成功 3.转换失败
@see CourseStatus
   */
  status?: number
  /**
   * 转换状态数组，当此参数不为空时，将忽略status参数
0.草稿 1.转换中 2.转换成功 3.转换失败
@see CourseStatus
   */
  statuses?: Array<number>
  /**
   * 课程状态 0停用，1启用
   */
  enable?: number
  /**
   * 创建时间开始
   */
  createTimeBegin?: string
  /**
   * 创建时间结束
   */
  createTimeEnd?: string
  /**
   * 课件id
   */
  coursewareId?: string
}

/**
 * <AUTHOR>
 */
export class CourseSalesStatisticsRequest {
  /**
   * 课件供应商ID
   */
  supplierId?: Array<string>
  /**
   * 选课时间开始
   */
  chooseCourseDateStart?: string
  /**
   * 选课时间结束
   */
  chooseCourseDateEnd?: string
}

/**
 * 课程排序规则配置请求
 */
export class CourseSortRuleConfigRequest {
  /**
   * 配置规则id
   */
  configId?: Array<string>
  /**
   * 状态 0-停用 1-启用
   */
  status?: number
}

/**
 * @version: 1.0
@description: 课程查询条件
@author: sugs
@create: 2023-02-28 14:35
 */
export class CourseV2Request {
  /**
   * 页码
   */
  pageNo: number
  /**
   * 页面条数
   */
  pageSize: number
  /**
   * 课程排序请求参数
   */
  sort?: Array<CourseSortRequest>
  /**
   * 课程ID集合
   */
  courseIdList?: Array<string>
  /**
   * 分类ID集合
   */
  categoryIdList?: Array<string>
  /**
   * 课件供应商ID
传-1代表查询未被授权的课程
-2 为 已分配的
   */
  supplierId?: string
  /**
   * 是否排除0学时
   */
  excludeZeroPeriod?: boolean
  /**
   * 课程名称
   */
  name?: string
  /**
   * 转换状态 0.草稿 1.转换中 2.转换成功 3.转换失败
@see CourseStatus
   */
  status?: number
  /**
   * 转换状态数组，当此参数不为空时，将忽略status参数
0.草稿 1.转换中 2.转换成功 3.转换失败
@see CourseStatus
   */
  statuses?: Array<number>
  /**
   * 课程状态 0停用，1启用
   */
  enable?: number
  /**
   * 创建时间开始
   */
  createTimeBegin?: string
  /**
   * 创建时间结束
   */
  createTimeEnd?: string
  /**
   * 课件id
   */
  coursewareId?: string
}

/**
 * @description: 课程包下方案同步查询条件
@author: sugs
@create: 2022-01-26 17:17
 */
export class CourserPackageSyncSchemeRequest {
  /**
   * 课程包id(必填)
   */
  coursePackageId: string
  /**
   * 方案集合
   */
  schemeIds?: Array<string>
  /**
   * 数据状态  1一致 0不一致
@see
   */
  dataStatus?: number
  /**
   * 同步状态 0未同步 1同步成功 2同步中 3同步失败
@see UsedStatus
   */
  syncStatus?: number
}

/**
 * 课件分类查询条件
 */
export class CoursewareCategoryRequest {
  /**
   * 分类ID集合
   */
  categoryIdList?: Array<string>
  /**
   * 父级分类ID
   */
  parentId?: string
  /**
   * 分类名称
   */
  name?: string
}

/**
 * 课件学习记录查询条件
<AUTHOR>
@version 1.0
@date 2022/2/10 9:28
 */
export class CoursewareLearningRecordRequest {
  /**
   * 学员课程id
   */
  studentCourseId?: string
  /**
   * 学员学号
   */
  studentNo?: string
}

/**
 * 课件查询条件
 */
export class CoursewareRequest {
  /**
   * 课件ID集合
   */
  coursewareIdList?: Array<string>
  /**
   * 课件分类ID集合
   */
  categoryIdList?: Array<string>
  /**
   * 课件名称（模糊）
   */
  name?: string
  /**
   * 供应商id集合
   */
  supplierIdList?: Array<string>
  /**
   * 课件类型(0：未知 1：单文档 2：单视频 3：多媒体 4：网站)
@see com.fjhb.domain.course.api.coursewareresource.consts.MediaTypes
   */
  coursewareType?: number
  /**
   * 课件转换状态（0：解析中 1：解析成功 2：解析失败）
   */
  status?: number
  /**
   * 课件状态（0：停用 1：正常）
   */
  enable?: number
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 资源类型 (0表示八百里，1表示保利威视，2表示华为云，20表示华为云音频 4表示外链地址)
   */
  resourceType?: number
}

/**
 * 创建课程排序规则配置请求
 */
export class CreateCourseSortRuleConfigRequest {
  /**
   * 规则名称
   */
  name: string
  /**
   * 是否乱序
   */
  isShuffle: boolean
  /**
   * 周期 Cron表达式
   */
  cron: string
  /**
   * Cron的描述
   */
  cronDescribe: string
  /**
   * 使用范围
   */
  effectiveScopeEnum: EffectiveScopeEnum
}

/**
 * 功能描述：时间范围查询条件
@Author： wtl
@Date： 2022/1/25 15:30
 */
export class DateScopeRequest {
  /**
   * 开始时间
查询大于等于开始时间的结果
   */
  beginTime?: string
  /**
   * 结束时间
查询小于等于结束时间的结果
   */
  endTime?: string
}

/**
 * @version: 1.0
@description: 精品课程请求参数
@author: sugs
@create: 2023-05-11 17:41
 */
export class ExcellentCourseRequest {
  /**
   * 课程名称
   */
  courseName?: string
  /**
   * 课程分类
   */
  courseCategory?: string
}

export class GetCourseCountRequest {
  /**
   * 方案id
   */
  schemeIds?: Array<string>
  /**
   * 课程学习场景
@see com.fjhb.ms.study.query.constants.CourseLearningSceneTypes
   */
  courseLearningSceneTypes?: Array<number>
}

export class LearningExperienceTopicRequest {
  /**
   * 学习心得主题配置id
   */
  topicIds?: Array<string>
  /**
   * 数据附属信息
   */
  owner?: OwnerInfo1
  /**
   * 方案信息
   */
  scheme?: SchemeRequest
  /**
   * 学习心得类型
   */
  experienceType?: ExperienceType
  /**
   * 参与形式
   */
  participateType?: ParticipateType
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 结束时间
   */
  endTime?: string
  /**
   * 审核方式
   */
  auditType?: AuditType
  /**
   * 不传值默认全查
   */
  isDelete?: boolean
}

export class PageLearningLogsRequest {
  /**
   * 参训资格id。不可为空
   */
  qualificationId?: string
  /**
   * 课程名称
   */
  courseName?: string
  /**
   * 日志类型 0开始学习 1结束学习 2学习监管
@deprecated  后续使用typeList控制
   */
  type?: number
  /**
   * 日志类型 0开始学习 1结束学习 2学习监管
   */
  typeList?: Array<number>
  /**
   * 学习类型 0正常学习 1一键合格 2补学
   */
  learningTypeList?: Array<number>
  /**
   * 记录开始时间
   */
  logBeginTime?: string
  /**
   * 记录结束时间
   */
  logEndTime?: string
}

/**
 * @version: 1.0
@description: 课程包中的重复课程请求参数
@author: sugs
@create: 2023-03-14 10:18
 */
export class RepeatedCoursesInSpecifiedCoursePackageRequest {
  /**
   * 源课程包ID
   */
  sourceCoursePackageId: string
  /**
   * 目标课程包ID
   */
  targetCoursePackageId: string
}

/**
 * 学员课程学习通用查询条件
 */
export class StudentCourseLearningCommonRequest {
  /**
   * 学员课程学习数据范围
   */
  studentCourseLearningRange?: StudentCourseLearningRangeRequest
  /**
   * 学号（必填）
   */
  studentNo?: string
  /**
   * 课程学习大纲课程
   */
  courseOfCourseTrainingOutline?: CourseOfCourseTrainingOutlineRequest
  /**
   * 课程信息
   */
  course?: CourseInfoRequest
  /**
   * 学员课程信息
   */
  studentCourse?: StudentCourseRequest
  /**
   * 学员媒体课程学习信息
   */
  studentCourseMediaLearningRecord?: StudentCourseMediaLearningRecordRequest
}

/**
 * 学员课程学习查询条件
 */
export class StudentCourseLearningRequest {
  /**
   * 学号（必填）
   */
  studentNo?: string
  /**
   * 课程学习大纲课程
   */
  courseOfCourseTrainingOutline?: CourseOfCourseTrainingOutlineRequest
  /**
   * 课程信息
   */
  course?: CourseInfoRequest
  /**
   * 学员课程信息
   */
  studentCourse?: StudentCourseRequest
  /**
   * 学员媒体课程学习信息
   */
  studentCourseMediaLearningRecord?: StudentCourseMediaLearningRecordRequest
}

export class StudentLearningExperienceRequest {
  /**
   * 学员学习心得ID
   */
  studentLearningExperienceIds?: Array<string>
  /**
   * 数据归属
   */
  owner?: OwnerInfo1
  /**
   * 学习心得主题
   */
  learningExperienceTopic?: LearningExperienceTopicRequest1
  /**
   * 学员方案学习
   */
  studentLearning?: StudentSchemeLearningRequest
  /**
   * 用户信息
   */
  user?: UserRequest
  /**
   * 学习心得类型（班级心得，课程心得）
   */
  experienceType?: ExperienceType
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 状态
@see com.fjhb.ms.course.learning.query.constants.StudentLearningExperienceStatus
   */
  status?: Array<StudentLearningExperienceStatus>
  /**
   * true只要被删除的 false只要未被删除的  null全查
   */
  isDelete?: boolean
}

/**
 * 修改课程排序规则配置请求
 */
export class UpdateCourseSortRuleConfigRequest {
  /**
   * 配置id
   */
  configId: string
  /**
   * 规则名称
   */
  name?: string
  /**
   * 是否乱序
   */
  isShuffle?: boolean
  /**
   * 更新周期 Cron表达式
   */
  cron?: string
  /**
   * Cron的描述
   */
  cronDescribe?: string
  /**
   * 状态 0-停用 1-启用
   */
  status?: number
  /**
   * 排序课程数量
   */
  sortCourseNum?: Array<SortCourseNumRequest>
  /**
   * 是否更新排序课程数量 false将忽略sortCourseNum内容
   */
  updateSortCourseNum: boolean
}

/**
 * 课程信息
 */
export class CourseInfoRequest {
  /**
   * 课程Id
   */
  courseId?: string
  /**
   * 排除的课程id
   */
  excludeCourseIds?: Array<string>
  /**
   * 课程id集合
   */
  courseIdList?: Array<string>
  /**
   * 课程名称
   */
  courseName?: string
  /**
   * 课程名称匹配方式
   */
  courseNameMatchType?: number
}

/**
 * 大纲课程配置信息
 */
export class CourseOfCourseTrainingOutlineRequest {
  /**
   * 课程学习大纲课程id
   */
  courseOfCourseTrainingOutlineId?: string
  /**
   * 课程学习大纲课程id
   */
  courseOfCourseTrainingOutlineIds?: Array<string>
  /**
   * 课程学习大纲id集合
   */
  outlineIds?: Array<string>
  /**
   * 排除课程学习大纲id
   */
  excludeOutlineIds?: Array<string>
  /**
   * 大纲内课程类型(1：必修 2：选修 3：兴趣课)
@see CourseTypes
   */
  courseType?: number
}

/**
 * 学习心得主题
 */
export class LearningExperienceTopicRequest1 {
  /**
   * 心得主题id
   */
  topicIds?: Array<string>
  /**
   * 参与活动时间
   */
  dateScopeRequest?: DateScopeRequest1
  /**
   * 审核方式
   */
  auditType?: AuditType
  /**
   * 心得参与形式
   */
  participateType?: ParticipateType
  /**
   * true只要被删除的 false只要未被删除的  null全查
   */
  isDelete?: boolean
}

export class SchemeRequest {
  /**
   * 培训方案id
   */
  schemeId?: string
  /**
   * 学习方式od
   */
  learningId?: string
}

/**
 * 排序课程数量
 */
export class SortCourseNumRequest {
  /**
   * 课件供应商id
   */
  coursewareSupplierId?: string
  /**
   * 配置的课程数
   */
  num?: number
}

/**
 * @description: 学员课程学习数据范围
@author: sugs
@create: 2022-04-02 15:34
 */
export class StudentCourseLearningRangeRequest {
  /**
   * 课程学习方式资源类型 1.选课规则课程学习场景 2.自主选课课程学习场景 3.兴趣课课程学习场景
@see CourseLearningSceneTypes
   */
  courseLearningResourceType?: number
}

export class StudentCourseMediaLearningRecordRequest {
  /**
   * 学习状态
   */
  studyStatus?: StudyStatusEnum
}

/**
 * 课程学习信息条件
 */
export class StudentCourseRequest {
  /**
   * 学员课程学习状态（0：未评定 1：未合格 2：合格）
@see StudentCourseLearningStatus
   */
  courseLearningStatus?: Array<number>
}

export class StudentSchemeLearningRequest {
  /**
   * 参训资格ID
   */
  qualificationIds?: Array<string>
  /**
   * 学号
   */
  studentNos?: Array<string>
  /**
   * 方案id
   */
  schemeIds?: Array<string>
  /**
   * 学习方式id
   */
  learningIds?: Array<string>
}

export class UserRequest {
  /**
   * 用户id
   */
  userIds?: Array<string>
}

/**
 * @Description  查看课程分布情况 请求参数
<AUTHOR>
@Date 2023/4/18 10:23
 */
export class CourseDistributionSortRequest {
  /**
   * 课程分布排序
   */
  courseDistributionSort?: CourseDistributionSortEnum
  /**
   * 支持排序方式
   */
  sortType?: SortTypeEnum1
}

/**
 * @description: 课程排序请求参数
@author: sugs
@create: 2022-03-14 09:30
 */
export class CourseSortRequest {
  /**
   * 课程排序枚举
   */
  courseSort?: CourseSortEnum
  /**
   * 排序方式 1降序 0升序
   */
  sortType?: number
}

/**
 * 课程排序规则配置排序请求
 */
export class CourseSortRuleConfigSortRequest {
  /**
   * 课程排序规则排序
   */
  courseSortRuleConfigSortEnum?: CourseSortRuleConfigSortEnum
  /**
   * 支持排序方式
   */
  sortType?: SortTypeEnum
}

/**
 * @version: 1.0
@description: 精品课程排序参数
@author: sugs
@create: 2023-05-11 18:21
 */
export class ExcellentCourseSortRequest {
  /**
   * 方案下课程排序
   */
  courseInSchemeSortEnum?: CourseInSchemeSortEnum
  /**
   * 支持排序方式
   */
  sortType?: SortTypeEnum1
}

export class StudentLearningExperienceSortRequest {
  /**
   * 大纲下课程数量排序
   */
  sort?: StudentLearningExperienceSortEnum
  /**
   * 支持排序方式
   */
  sortType?: SortTypeEnum12
}

/**
 * 数据归属模型
 */
export class OwnerInfo1 {
  /**
   * 所属平台ID
   */
  platformId?: string
  /**
   * 所属平台版本ID
   */
  platformVersionId?: string
  /**
   * 所属项目ID
   */
  projectId?: string
  /**
   * 所属子项目ID
   */
  subProjectId?: string
  /**
   * 所属单位id
   */
  unitId?: string
  /**
   * 所属服务商类型
@see ServicerTypes
1.培训机构; 2.课件供应商; 3.渠道商; 4.参训单位;
   */
  servicerType?: number
  /**
   * 所属服务商id
   */
  servicerId?: string
}

export class DateScopeRequest1 {
  begin?: string
  end?: string
}

export class BaseResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 响应消息
   */
  message: string
}

/**
 * 选课统计响应
 */
export class ChooseCourseStatisticsResponse {
  /**
   * 汇总统计
   */
  chooseCourseStatisticsInfoTotal: ChooseCourseStatisticsInfoTotal
  /**
   * 分页列表
   */
  courseStatisticsInfoPage: ChooseCourseStatisticsInfoPage
}

/**
 * 课程分类
 */
export class CourseCategoryResponse {
  /**
   * 分类id
   */
  id: string
  /**
   * 父级分类id
   */
  parentId: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 当前排序
   */
  sort: number
}

export class CourseCountBySchemeIdResponse {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 课程数
   */
  courseCount: number
}

/**
 * 课程
 */
export class CourseDetailResponse {
  /**
   * 课程目录
   */
  courseOutlines: Array<CourseOutline>
  /**
   * 课程章节
   */
  courseChapters: Array<CourseChapter>
  /**
   * 课程ID
   */
  id: string
  /**
   * 课件供应商ID
   */
  supplierId: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 课程简介内容
   */
  aboutsContent: string
  /**
   * 课程分类
   */
  categorys: Array<CourseCategoryResponse>
  /**
   * 教师ID集合
   */
  teacherIds: Array<string>
  /**
   * 学时
   */
  period: number
  /**
   * 转换状态 0.草稿 1.转换中 2.转换成功 3.转换失败
@see CourseStatus
   */
  status: number
  /**
   * 课程状态 0停用，1启用
   */
  enable: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 课程时长
   */
  courseTimeLength: number
  /**
   * 试听 0禁止 1启用
   */
  auditionStatus: number
  /**
   * 课程评价信息
   */
  courseAppraiseInfo: CourseAppraise
}

/**
 * @Description  查看课程分布情况 响应参数
<AUTHOR>
@Date 2023/4/18 10:23
 */
export class CourseDistributionResponse {
  /**
   * 课件供应商Id
   */
  supplierId: string
  /**
   * 培训方案Id
   */
  schemeId: string
  /**
   * 方案状态
   */
  schemeState: number
  /**
   * 分类Id
   */
  classificationId: string
  /**
   * 课程数量
   */
  count: number
}

/**
 * @description: 课程包下的课程信息
@author: sugs
@create: 2022-01-27 10:59
 */
export class CourseInPackageResponse {
  /**
   * 包下课程id
   */
  courseInPackageId: string
  /**
   * 数据归属信息
   */
  owner: OwnerInfo
  /**
   * 包下课程配置信息
   */
  courseInPackage: CourseInPackageInfo
  /**
   * 课程信息
   */
  course: CourseInfo
}

/**
 * 方案课程配置主题模型
<AUTHOR>
@version 1.0
@date 2022/1/6 20:10
 */
export class CourseInSchemeResponse {
  /**
   * 课程学习大纲课程id
   */
  courseOfCourseTrainingOutlineId: string
  /**
   * 数据归属信息
   */
  owner: OwnerInfo
  /**
   * 数据范围
   */
  range: CourseInSchemeRange
  /**
   * 课程学习大纲课程
   */
  courseOfCourseTrainingOutline: CourseOfCourseTrainingOutlineInfo
  /**
   * 课程信息
   */
  course: CourseInfo
  /**
   * 选择课程数量
   */
  chooseCoursePeopleCount: number
  /**
   * 综合评价值
   */
  totalAppraise: number
}

/**
 * @description: 课程包
@author: sugs
@create: 2022-01-26 16:20
 */
export class CoursePackageResponse {
  /**
   * 课程包id
   */
  coursePackageId: string
  /**
   * 数据归属信息
   */
  owner: OwnerInfo
  /**
   * 课程包信息
   */
  coursePackage: CoursePackageInfo
  /**
   * 课程包下课程统计信息
   */
  courseStatisticOfCoursePackage: CourseStatisticOfCoursePackageResponse
  /**
   * 创建人信息
   */
  creator: UserInfo
}

/**
 * 大纲下课程统计
 */
export class CoursePeriodCountOfCourseTrainingOutlineResponse {
  /**
   * 课程学习大纲下课程总学时
   */
  courseTotalPeriod: number
}

/**
 * 课程
 */
export class CourseResponse {
  /**
   * 课程ID
   */
  id: string
  /**
   * 课件供应商ID
   */
  supplierId: string
  /**
   * 课程名称
   */
  name: string
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 课程简介内容
   */
  aboutsContent: string
  /**
   * 课程分类
   */
  categorys: Array<CourseCategoryResponse>
  /**
   * 教师ID集合
   */
  teacherIds: Array<string>
  /**
   * 学时
   */
  period: number
  /**
   * 转换状态 0.草稿 1.转换中 2.转换成功 3.转换失败
@see CourseStatus
   */
  status: number
  /**
   * 课程状态 0停用，1启用
   */
  enable: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 课程时长
   */
  courseTimeLength: number
  /**
   * 试听 0禁止 1启用
   */
  auditionStatus: number
  /**
   * 课程评价信息
   */
  courseAppraiseInfo: CourseAppraise
}

/**
 * <AUTHOR>
 */
export class CourseSalesStatisticsResponse {
  /**
   * 总数统计
   */
  courseSalesStatisticsInfoTotal: CourseSalesStatisticsInfoTotal
  /**
   * 统计分页信息
   */
  data: CourseSalesStatisticsInfoPage
}

/**
 * 课程排序规则配置详情
 */
export class CourseSortRuleConfigDetailResponse {
  /**
   * id
   */
  configId: string
  /**
   * 规则名称
   */
  name: string
  /**
   * 是否乱序
   */
  isShuffle: boolean
  /**
   * 更新周期 Cron表达式
   */
  cron: string
  /**
   * Cron的描述
   */
  cronDescribe: string
  /**
   * 生效范围
   */
  effectiveScope: EffectiveScopeEnum
  /**
   * 状态 0-停用 1-启用
   */
  status: number
  /**
   * 排序课程数量
   */
  sortCourseNum: Array<SortCourseNumResponse>
}

/**
 * 课程排序规则配置返回
 */
export class CourseSortRuleConfigResponse {
  /**
   * id
   */
  configId: string
  /**
   * 规则名称
   */
  name: string
  /**
   * 是否乱序
   */
  isShuffle: boolean
  /**
   * 更新周期 Cron表达式
   */
  cron: string
  /**
   * Cron的描述
   */
  cronDescribe: string
  /**
   * 生效范围
   */
  effectiveScope: EffectiveScopeEnum
  /**
   * 状态 0-停用 1-启用
   */
  status: number
}

/**
 * 课程包下课程统计信息
<AUTHOR>
@version 1.0
@date 2022/2/9 16:40
 */
export class CourseStatisticOfCoursePackageResponse {
  /**
   * 课程包下课程数
   */
  courseCount: number
  /**
   * 课程包下课程总学时
   */
  courseTotalPeriod: number
}

/**
 * 大纲下课程统计
 */
export class CourseStatisticsOfCourseTrainingOutlineResponse {
  /**
   * 课程学习大纲下课程数
   */
  courseCount: number
  /**
   * 课程学习大纲下课程总学时
   */
  courseTotalPeriod: number
  /**
   * 课程学习大纲下课程完成学时
   */
  completeTotalPeriod: number
  /**
   * 课程学习大纲下课程学习中的学时
   */
  learningTotalPeriod: number
}

/**
 * 课程学习大纲信息
 */
export class CourseTrainingOutlineResponse {
  /**
   * 课程学习大纲Id
   */
  outlineId: string
  /**
   * 数据归属信息
   */
  owner: OwnerInfo
  /**
   * 课程学习大纲信息
   */
  courseTrainingOutline: CourseTrainingOutlineInfo
  /**
   * 课程学习大纲课程统计信息
   */
  courseStatisticOfCourseTrainingOutline: CourseStatisticsOfCourseTrainingOutlineResponse
  /**
   * 创建人信息
   */
  creator: UserInfo
}

/**
 * @description: 课程包同步方案情况
@author: sugs
@create: 2022-01-27 11:20
 */
export class CourserPackageSyncSchemeResponse {
  /**
   * 课程包同步情况id
   */
  coursePackageUseId: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 数据状态  1一致 0不一致
@see
   */
  dataStatus: number
  /**
   * 同步状态 0未同步 1同步成功 2同步中 3同步失败
@see UsedStatus
   */
  syncStatus: number
  /**
   * 方案是否执行任务中
   */
  executed: boolean
  /**
   * 最新同步时间
   */
  syncTime: string
  /**
   * 错误信息
   */
  errorMessage: string
}

/**
 * @description: 课程包同步方案统计
@author: sugs
@create: 2022-01-27 11:20
 */
export class CourserPackageSyncSchemeStatisticResponse {
  /**
   * 方案引用数量
   */
  schemeSyncCount: number
  /**
   * 未同步数量
   */
  noSyncedCount: number
  /**
   * 同步中数量
   */
  syncingCount: number
  /**
   * 已同步数量
   */
  syncedCount: number
  /**
   * 同步失败数量
   */
  syncFailedCount: number
}

/**
 * 课件分类
 */
export class CoursewareCategoryResponse {
  /**
   * 分类ID
   */
  id: string
  /**
   * 父级分类ID
   */
  parentId: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 分类级别集合
   */
  parentIds: Array<string>
  /**
   * 当前排序
   */
  sort: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人
   */
  createUserId: string
}

/**
 * 课件详细信息
 */
export class CoursewareDetailResponse {
  /**
   * 媒体资源id
   */
  coursewareMediaResourceId: string
  /**
   * 课件简介内容
   */
  aboutsContent: string
  /**
   * 成品完成时间
   */
  finishProductTime: string
  /**
   * 资源类型 (0表示八百里，1表示保利威视，2表示华为云，20表示华为云音频 4表示外链地址)
   */
  resourceType: number
  /**
   * 资源地址集合
   */
  coursewareMediaResourceResponses: Array<CoursewareMediaResourceResponse>
  /**
   * 课件ID
   */
  id: string
  /**
   * 课件名称
   */
  name: string
  /**
   * 课件类型(0：未知 1：单文档 2：单视频 3：多媒体 4：网站)
@see com.fjhb.domain.course.api.coursewareresource.consts.MediaTypes
   */
  type: number
  /**
   * 课件分类
   */
  category: CoursewareCategoryResponse
  /**
   * 供应商信息
   */
  supplierInfo: ServicerInfo
  /**
   * 媒体时长
   */
  timeLength: number
  /**
   * 课件转换状态（0：解析中 1：解析成功 2：解析失败）
   */
  status: number
  /**
   * 课件状态（0：停用 1：正常）
   */
  enable: number
  /**
   * 教师信息集合
   */
  coursewareTeacherList: Array<CoursewareTeacher>
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人
   */
  createUserId: string
  /**
   * 是否被引用(0：否 1：是)
   */
  isReferenced: number
  /**
   * 是否外链地址  true 是  false 不是
   */
  isExternalLink: boolean
  /**
   * 课件转码失败原因
   */
  errorMsg: string
  /**
   * 失败操作来源  1.创建 2。修改
   */
  errorSource: number
}

/**
 * 课件
 */
export class CoursewareInCourseResponse {
  /**
   * 课件ID
   */
  coursewareId: string
  /**
   * 课件名称
   */
  coursewareName: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 课程名称
   */
  courseName: string
}

/**
 * 课件学习记录返回信息
 */
export class CoursewareLearningRecordResponse {
  /**
   * 课件学习记录id
   */
  coursewareLearningRecordId: string
  /**
   * 数据归属信息
   */
  owner: OwnerInfo
  /**
   * 课件信息
   */
  courseware: CoursewareInfo
  /**
   * 课件学习信息
   */
  coursewareLearningRecord: CoursewareLearningRecordInfo
}

/**
 * 课件
 */
export class CoursewareResponse {
  /**
   * 课件ID
   */
  id: string
  /**
   * 课件名称
   */
  name: string
  /**
   * 课件类型(0：未知 1：单文档 2：单视频 3：多媒体 4：网站)
@see com.fjhb.domain.course.api.coursewareresource.consts.MediaTypes
   */
  type: number
  /**
   * 课件分类
   */
  category: CoursewareCategoryResponse
  /**
   * 供应商信息
   */
  supplierInfo: ServicerInfo
  /**
   * 媒体时长
   */
  timeLength: number
  /**
   * 课件转换状态（0：解析中 1：解析成功 2：解析失败）
   */
  status: number
  /**
   * 课件状态（0：停用 1：正常）
   */
  enable: number
  /**
   * 教师信息集合
   */
  coursewareTeacherList: Array<CoursewareTeacher>
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人
   */
  createUserId: string
  /**
   * 是否被引用(0：否 1：是)
   */
  isReferenced: number
  /**
   * 是否外链地址  true 是  false 不是
   */
  isExternalLink: boolean
  /**
   * 课件转码失败原因
   */
  errorMsg: string
  /**
   * 失败操作来源  1.创建 2。修改
   */
  errorSource: number
}

/**
 * @description: 课件供应商供应商
@author: sugs
@create: 2022-03-09 19:35
 */
export class CoursewareSupplierResponse {
  /**
   * 课件供应商ID
   */
  id: string
  /**
   * 课件供应商名称
   */
  name: string
}

/**
 * @version: 1.0
@description: 精品课程信息
@author: sugs
@create: 2023-05-11 17:41
 */
export class ExcellentCourseResponse {
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课程名称
   */
  courseName: string
  /**
   * 课程学时
   */
  period: number
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 课程简介内容
   */
  aboutsContent: string
  /**
   * 课程分类
   */
  categorys: Array<CourseCategoryResponse>
  /**
   * 教师ID集合
   */
  teacherIds: Array<string>
  /**
   * 教师名称
   */
  teacherNames: Array<string>
  /**
   * 课程评价信息
   */
  courseAppraiseInfo: CourseAppraise
}

export class LearningExperienceTopicResponse {
  /**
   * 学习心得主题id
   */
  topicId: string
  /**
   * 数据归属
   */
  owner: OwnerInfo
  /**
   * 方案信息
   */
  scheme: SchemeInfo
  /**
   * 心得主题
   */
  experienceTopicName: string
  /**
   * 学习心得类型
   */
  experienceType: ExperienceType
  /**
   * 学习心得类型信息
   */
  experienceTypeInfo: Map<string, string>
  /**
   * 参与形式
   */
  participateType: ParticipateType
  /**
   * 活动文本内容id
   */
  descriptionContent: string
  /**
   * 参加活动类型
   */
  participateTimeType: ParticipateTimeType
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 提交要求
   */
  submitLimitNum: number
  /**
   * 审核方式
   */
  auditType: AuditType
  /**
   * 提交次数
   */
  submitLimitCount: number
  /**
   * 通过分
   */
  passScore: number
  /**
   * 总分
   */
  totalScore: number
  /**
   * 是否必选
   */
  isRequired: boolean
  /**
   * 数据操作信息
   */
  dataManipulation: DataManipulationResponse
}

export class LearningLogsResponse {
  /**
   * 记录时间
   */
  logTime: string
  /**
   * 操作类型
0: 开始学习,1: 结束学习,2: 学习监管
   */
  type: number
  /**
   * 课程id
   */
  courseId: string
  courseName: string
}

/**
 * @version: 1.0
@description: 教学计划考勤请求参数
@author: sugs
@create: 2024-11-09 15:25
 */
export class PlanItemAttendanceResponse {
  /**
   * 教学计划项信息
   */
  planItem: PlanItemResponse
  /**
   * 教学计划签到点信息
   */
  signInPoint: Array<SignInPointResponse>
  /**
   * 签到记录信息
   */
  signInRecord: Array<SignInRecordResponse>
}

/**
 * 学员课程评价
 */
export class StudentCourseAppraiseResponse {
  /**
   * 数据归属信息
   */
  owner: OwnerInfo
  /**
   * 课程信息
   */
  course: CourseInfo
  /**
   * 学员课程信息
   */
  studentCourse: StudentCourseInfo
  /**
   * 课程评价信息
   */
  courseAppraisalInfo: CourseAppraisalInfo
  /**
   * 学员课程评价数据范围
   */
  range: StudentCourseAppraisalRange
  /**
   * true 已屏蔽 false 未屏蔽
   */
  isBlocked: boolean
}

export class StudentCourseAppraiseStarStatisticsResponse {
  /**
   * 课程id
   */
  courseId: string
  /**
   * 该课程的评价总数
   */
  appraiseTotalCount: number
  /**
   * 各星级数量
固定 100 200 300 400 500
   */
  starStatisticsResponseList: Array<StarStatisticsResponse>
}

/**
 * 用户课程信息
 */
export class StudentCourseLearningResponse {
  /**
   * 数据归属信息
   */
  owner: OwnerInfo
  /**
   * 数据范围
   */
  range: StudentCourseLearningRange
  /**
   * 课程学习大纲下课程配置信息
   */
  courseOfCourseTrainingOutline: CourseOfCourseTrainingOutlineInfo
  /**
   * 课程信息
   */
  course: CourseInfo
  /**
   * 所属学员信息
   */
  student: StudentInfo
  /**
   * 所属用户信息
   */
  user: UserInfo
  /**
   * 学员课程信息
   */
  studentCourse: StudentCourse
  /**
   * 学员媒体课程学习信息
   */
  studentCourseMediaLearningRecord: StudentCourseMediaLearningRecord
  /**
   * 学员课后测验
   */
  studentCourseQuiz: StudentCourseQuiz
  /**
   * 学员课程已评价信息
   */
  studentCourseAppraised: StudentCourseAppraised
  /**
   * 课程配置是否配置学习心得
   */
  hasLearningExperienceTopic: boolean
}

export class StudentLearningExperienceLastedResponse {
  /**
   * 学员学习心得模型  未参加 待提交的会有这个模型 用于回显
   */
  studentLearningExperience: StudentLearningExperienceResponse
  /**
   * 已提交的次数
   */
  count: number
  /**
   * 心得主题模型  已参加的口该值为null
   */
  topicId: string
  /**
   * 大纲id 已参加的口该值为null
   */
  outLineId: string
  /**
   * 心得配置的可提交总次数  （未减去已提交的次数，如果提交）
   */
  submitLimitCount: number
}

export class StudentLearningExperienceResponse {
  /**
   * 学员学习心得ID
   */
  studentLearningExperienceId: string
  /**
   * 数据归属
   */
  owner: OwnerInfo
  /**
   * 学员方案学习
   */
  studentLearning: StudentSchemeLearningResponse
  /**
   * 学员
   */
  student: UserInfo
  /**
   * 学习心得主题
   */
  learningExperienceTopic: LearningExperienceTopicResponse1
  /**
   * 学习心得类型
   */
  experienceType: ExperienceType
  /**
   * 学习心得类型信息  根据心得类型判断 是课程就是课程id 是班级就是班级id
   */
  experienceTypeInfo: Array<string>
  /**
   * 提交内容（如果为文本时，存储关联文本id，如果是附件是一个json结构，包含名称和地址）
   */
  content: string
  /**
   * 状态
   */
  status: StudentLearningExperienceStatus
  /**
   * 状态变更时间
   */
  statusChangeTime: Array<StatusChangeResponse>
  /**
   * 审核意见
   */
  remark: string
  /**
   * 得分
   */
  score: number
  /**
   * 数据操作信息
   */
  dataManipulation: DataManipulationResponse
}

export class StudentLearningRuleResponse {
  /**
   * 规则id
   */
  ruleId: string
  /**
   * 适用行业范围
   */
  suitIndustryRangeList: Array<SuitIndustryRangeResponse>
  /**
   * 适用区域范围
   */
  regionCode: Array<string>
  /**
   * 最大学习时长-单位秒
   */
  maxStudyTimeLength: number
  /**
   * 状态
   */
  status: number
  /**
   * 生效时间
   */
  effectTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 操作人id
   */
  operatorId: string
  /**
   * 排除的方案id
   */
  excludeSchemeIds: Array<string>
  /**
   * 特殊规则方案配置
   */
  specialSchemeRuleList: Array<SpecialSchemeRuleResponse>
  /**
   * 规则类型 1学习时长 2课时
   */
  ruleType: number
}

/**
 * @description: 教师信息
@author: sugs
@create: 2022-03-10 10:04
 */
export class TeacherResponse {
  /**
   * 教师id
   */
  id: string
  /**
   * 数据归属信息
   */
  owner: OwnerInfo
  /**
   * 教师名称
   */
  name: string
  /**
   * 头像/照片
   */
  photo: string
  /**
   * 简介内容
   */
  aboutsContent: string
  /**
   * 性别 -1:未知 0:女 1:男
@see TeacherGenders
   */
  gender: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人id
   */
  createUserId: string
}

/**
 * @version: 1.0
@description: 课程包中的重复课程信息
@author: sugs
@create: 2023-03-14 10:18
 */
export class RepeatedCoursesInSpecifiedCoursePackageResponse {
  /**
   * 源课程包ID
   */
  sourceCoursePackageId: string
  /**
   * 源课程包名称
   */
  sourceCoursePackageName: string
  /**
   * 目标课程包ID
   */
  targetCoursePackageId: string
  /**
   * 目标程包名称
   */
  targetCoursePackageName: string
  /**
   * 重复课程id
   */
  courseId: string
  /**
   * 重复课程名称
   */
  courseName: string
}

export class StarStatisticsResponse {
  /**
   * 评价值
   */
  appraisalValue: number
  /**
   * 数量
   */
  count: number
}

/**
 * 方案课程配置数据范围
<AUTHOR>
@version 1.0
@date 2022/2/9 20:53
 */
export class CourseInSchemeRange {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 期数id
   */
  issueId: string
  /**
   * 课程学习方式id
   */
  courseLearningId: string
  /**
   * 课程学习方式资源类型(1：选课学习场景 2：自主课程学习场景 3：兴趣课程学习场景)
@see CourseLearningSceneTypes
   */
  courseLearningResourceType: string
  /**
   * 课程学习方式资源id
   */
  courseLearningResourceId: string
  /**
   * 课程来源类型//1-课程包 2-课程
   */
  courseSourceType: string
  /**
   * 课程来源id
   */
  courseSourceId: string
}

/**
 * 课程信息
 */
export class CourseInfo {
  /**
   * 课程Id
   */
  courseId: string
  /**
   * 课程名称
   */
  courseName: string
  /**
   * 课程时长
   */
  courseTimeLength: number
  /**
   * 教师名称
   */
  teacherNames: Array<string>
  /**
   * 教师id
   */
  teacherIds: Array<string>
  /**
   * 课程学时
   */
  period: number
  /**
   * 试听 0禁止 1启用
   */
  auditionStatus: number
  /**
   * 封面图片路径
   */
  iconPath: string
  /**
   * 课程评价信息
   */
  courseAppraiseInfo: CourseAppraise
  /**
   * 选择课程数量
   */
  chooseCoursePeopleCount: number
  /**
   * 课程分类
   */
  categorys: Array<CourseCategoryResponse>
}

/**
 * 课件信息
 */
export class CoursewareInfo {
  /**
   * 课件ID
   */
  coursewareId: string
}

/**
 * 数据归属模型
 */
export class OwnerInfo {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属服务商类型
@see ServicerTypes
1.培训机构; 2.课件供应商; 3.渠道商; 4.参训单位;
   */
  servicerType: number
  /**
   * 所属服务商id
   */
  servicerId: string
}

export class ServicerInfo {
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 服务商名称
   */
  servicerName: string
}

/**
 * @description: 学员课程评价数据范围
@author: sugs
@create: 2022-05-30 09:59
 */
export class StudentCourseAppraisalRange {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 期数id
   */
  issueId: string
  /**
   * 课程学习方式id
   */
  courseLearningId: string
  /**
   * 课程学习方式资源类型(1：选课学习场景 2：自主课程学习场景 3：兴趣课程学习场景)
@see CourseLearningSceneTypes
   */
  courseLearningResourceType: string
  /**
   * 课程学习方式资源id
   */
  courseLearningResourceId: string
}

/**
 * 学员课程学习数据范围
<AUTHOR>
@version 1.0
@date 2022/2/9 20:53
 */
export class StudentCourseLearningRange {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 期数id
   */
  issueId: string
  /**
   * 课程学习方式id
   */
  courseLearningId: string
  /**
   * 课程学习方式资源类型(1：选课学习场景 2：自主课程学习场景 3：兴趣课程学习场景)
@see CourseLearningSceneTypes
   */
  courseLearningResourceType: string
  /**
   * 课程学习方式资源id
   */
  courseLearningResourceId: string
}

/**
 * 学员信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:00
 */
export class StudentInfo {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学号
   */
  studentNo: string
}

export class UserInfo {
  /**
   * 账户id
   */
  accountId: string
  /**
   * 用户id
   */
  userId: string
}

/**
 * 课程评价值
 */
export class CourseAppraise {
  /**
   * 综合评价值
   */
  totalAppraise: number
  /**
   * 课程评价值
   */
  courseAppraise: number
  /**
   * 教师评价值
   */
  teacherAppraise: number
}

/**
 * 课程章节
 */
export class CourseChapter {
  /**
   * 课程章节id
   */
  id: string
  /**
   * 章节名称
   */
  name: string
  /**
   * 课程目录
   */
  courseOutline: CourseOutline
  /**
   * 课件id
   */
  coursewareId: string
  /**
   * 课件名称
   */
  coursewareName: string
  /**
   * 课件媒体时长
   */
  timeLength: number
  /**
   * 试听 0禁止 1启用
   */
  auditionStatus: number
  /**
   * 排序
   */
  sort: number
}

/**
 * 课程目录
 */
export class CourseOutline {
  /**
   * 课程目录id
   */
  id: string
  /**
   * 父级课程目录id
   */
  parentId: string
  /**
   * 课程目录名称
   */
  name: string
  /**
   * 排序
   */
  sort: number
}

export class CoursewareMediaResourceResponse {
  /**
   * 1:流畅普屏 2:标清普屏 3:高清普屏 4:流畅宽屏 5:标清宽屏 6:高清宽屏
   */
  clarity: number
  /**
   * 相对路径
   */
  path: string
}

/**
 * 课件教师信息
 */
export class CoursewareTeacher {
  /**
   * 教师id
   */
  teacherId: string
  /**
   * 教师名称
   */
  teacherName: string
  /**
   * 教师简介
   */
  teacherContent: string
}

/**
 * 排序课程数量
 */
export class SortCourseNumResponse {
  /**
   * 课件供应商id
   */
  coursewareSupplierId: string
  /**
   * 配置的课程数
   */
  num: number
}

/**
 * @description: 课程评价信息
@author: sugs
@create: 2022-05-30 09:57
 */
export class CourseAppraisalInfo {
  /**
   * 学员课程评价编号
   */
  studentCourseAppraisalId: string
  /**
   * 评价内容
   */
  content: string
  /**
   * 课程评价值
   */
  courseAppraise: number
  /**
   * 教师评价值
   */
  teacherAppraise: number
  /**
   * 评价用户id
   */
  appraisalUserId: string
  /**
   * 评价时间
   */
  appraisalTime: string
}

/**
 * 课程包下课程信息
<AUTHOR>
@version 1.0
@date 2022/2/9 20:16
 */
export class CourseInPackageInfo {
  /**
   * 课程排序
   */
  sort: number
  /**
   * 选课学时
   */
  period: number
}

/**
 * 课程包信息
<AUTHOR>
@version 1.0
@date 2022/2/9 16:37
 */
export class CoursePackageInfo {
  /**
   * 课程包名称
   */
  name: string
  /**
   * 课程包展示名称
   */
  displayName: string
  /**
   * 是否被方案引用为必修
   */
  isBeingUsedAsCompulsory: boolean
  /**
   * 是否被方案引用 是否被引用 0否；1是
   */
  isReferenced: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 最后更新时间
   */
  lastUpdatedTime: string
}

/**
 * 课程学习大纲下课程信息
<AUTHOR>
@version 1.0
@date 2022/2/9 19:59
 */
export class CourseOfCourseTrainingOutlineInfo {
  /**
   * 课程学习大纲课程id
   */
  courseOfCourseTrainingOutlineId: string
  /**
   * 课程学习大纲id
   */
  outlineId: string
  /**
   * 大纲内课程序号
   */
  sort: number
  /**
   * 选课学时
   */
  period: number
  /**
   * 大纲内课程类型(1：必修 2：选修 3：兴趣课)
@see CourseTypes
   */
  courseType: number
}

/**
 * 课程学习大纲信息
<AUTHOR>
@version 1.0
@date 2022/2/10 9:55
 */
export class CourseTrainingOutlineInfo {
  /**
   * 课程学习大纲Id
   */
  outlineId: string
  /**
   * 课程学习大纲名称
   */
  name: string
  /**
   * 所属场景id
   */
  sceneId: string
  /**
   * 课程学习大纲上级id
   */
  parentId: string
  /**
   * 课程学习大纲序号
   */
  sort: number
  /**
   * 大纲类型 1.必修 2.选修
@see CourseTrainingCategorys
   */
  category: number
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 最后更新时间
   */
  lastUpdatedTime: string
}

export class SchemeInfo {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
}

export class AddtionalRangeResponse {
  /**
   * 范围类型
年度 1
科目 2
培训类别 3
培训对象 4
学科 5
岗位类别 6
技术等级 7
学段 8
   */
  rangeType: string
  /**
   * 附加范围值 -1 为全选
   */
  rangeValue: string
}

export class SpecialSchemeRuleResponse {
  /**
   * 规则ID
   */
  specifySchemeRuleId: string
  /**
   * 方案ID
   */
  schemeIdList: Array<string>
  /**
   * 规则类型 1学习时长 2课时
   */
  ruleType: number
  /**
   * 最大学习时长-单位秒
   */
  maxStudyTimeLength: number
}

export class SuitIndustryRangeResponse {
  /**
   * 行业id
   */
  industryId: string
  /**
   * 附加范围
   */
  addtionalRangeList: Array<AddtionalRangeResponse>
}

/**
 * @version: 1.0
@description: 教学计划参数
@author: sugs
@create: 2024-11-10 17:31
 */
export class PlanItemResponse {
  /**
   * 教学计划项id
   */
  id: string
  /**
   * 教学计划id
   */
  planId: string
  /**
   * 教学计划组id
   */
  planItemGroupId: string
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属服务商id
   */
  servicerId: string
  /**
   * 平台租户id
   */
  tenantId: string
  /**
   * 教学计划项名称
   */
  name: string
  /**
   * 划教学模式
   */
  planMode: number
  /**
   * 计划项类型
@see PlanItemTypes
   */
  planItemType: number
  /**
   * 学时
   */
  period: number
  /**
   * 教学开始时间
   */
  startTime: string
  /**
   * 教学结束时间
   */
  endTime: string
  /**
   * 简介
   */
  abouts: string
  /**
   * 时长
   */
  timeLength: number
  /**
   * 教师
   */
  teachers: Array<PlanTeacherResponse>
}

/**
 * @version: 1.0
@description: 教学计划教师
@author: sugs
@create: 2024-11-15 13:47
 */
export class PlanTeacherResponse {
  /**
   * 教师id
   */
  id: string
  /**
   * 教师名称
   */
  teacherName: string
  /**
   * 教师类型
   */
  teacherType: number
  /**
   * 性质
   */
  nature: number
  /**
   * 教师职称
   */
  teacherTitle: string
  /**
   * 教师所在单位
   */
  unitName: string
}

/**
 * @version: 1.0
@description: 教学计划签到点信息
@author: sugs
@create: 2024-11-10 17:24
 */
export class SignInPointResponse {
  /**
   * 签到点id
   */
  id: string
  /**
   * 教学计划id
   */
  planId: string
  /**
   * 教学计划项id
   */
  planItemId: string
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 期别id
   */
  issueId: string
  /**
   * 签到点规则id
   */
  pointRuleId: string
  /**
   * 签到开始时间
   */
  startTime: string
  /**
   * 签到结束时间
   */
  endTime: string
  /**
   * 地点坐标经度
   */
  longitude: number
  /**
   * 地点坐标维度
   */
  latitude: number
  /**
   * 有效半径
   */
  diameter: number
  /**
   * 教学计划签到点状态
1.正常 2.作废
@see PlanSignInPointStatus
   */
  status: number
  /**
   * 状态描述
   */
  statusRemark: string
  /**
   * 签到类型
1.签到 2.签退
@see PlanSignTypes
   */
  signType: number
}

/**
 * @version;1.0
@description;签到记录
@author;sugs
@create;2024-11-10 17:31
 */
export class SignInRecordResponse {
  /**
   * 记录ID
   */
  id: string
  /**
   * 平台租户id
   */
  tenantId: string
  /**
   * 教学计划ID
   */
  planId: string
  /**
   * 教学计划项组ID
   */
  planItemGroupId: string
  /**
   * 教学计划项ID
   */
  planItemId: string
  /**
   * 学习方案id（冗余，当教学计划来自方案\期别时）
   */
  schemeId: string
  /**
   * 学习方式id（冗余，当教学计划来自方案\期别时）
   */
  learningId: string
  /**
   * 期别ID（冗余，当教学计划来自期别时）
   */
  issueId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 签到时间
   */
  signTime: string
  /**
   * 地点坐标经度
   */
  longitude: number
  /**
   * 地点坐标纬度
   */
  latitude: number
  /**
   * 签到Key（签到点id）
   */
  pointKey: string
  /**
   * 签到类型 (1:签到, 2:签退)
@see com.fjhb.domain.teachingplan.api.sign.consts.attendance.PlanAttendanceType
attendanceType
   */
  signType: number
  /**
   * 签到结果代码
正常考勤 200
重复考勤 300
过早   301
过晚   302
不在地点范围   400
系统异常 500
@see com.fjhb.domain.teachingplan.api.sign.consts.attendance.PlanAttendanceResultCodes
attendanceResultCode
   */
  signResultCode: number
  /**
   * 签到模式
签到 1
签退 2
@see com.fjhb.domain.teachingplan.api.sign.consts.attendance.PlanAttendanceType
attendanceSourceType
   */
  signMode: number
  /**
   * 签到来源类型（1：学员签到、2：审批）
   */
  signSourceType: number
  /**
   * 签到来源ID（学员id，审批单id？）
   */
  signSourceId: string
}

/**
 * <AUTHOR>
 */
export class ChooseCourseStatisticsInfo {
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 课件供应商
   */
  supplierId: string
  /**
   * 技术等级
   */
  technicalGrade: string
  /**
   * 选课学时
   */
  period: number
  /**
   * 综合评分
   */
  totalAppraise: number
  /**
   * 累计选课人次
   */
  sumChooseCourseCount: number
  /**
   * 累计退课人次
   */
  totalWithdrawalCount: number
  /**
   * 净选课人次
   */
  totalChooseCourseCount: number
  /**
   * 已学完人次
   */
  totalCompletedCount: number
}

/**
 * 汇总数据
<AUTHOR>
 */
export class ChooseCourseStatisticsInfoTotal {
  /**
   * 累计选课人次
   */
  sumChooseCourseCount: number
  /**
   * 累计退课人次
   */
  totalWithdrawalCount: number
  /**
   * 净选课人次
   */
  totalChooseCourseCount: number
  /**
   * 已学完人次
   */
  totalCompletedCount: number
}

/**
 * 课程销售统计信息
<AUTHOR>
 */
export class CourseSalesStatisticsInfo {
  /**
   * 课件供应商ID
   */
  coursewareSupplierId: string
  /**
   * 累计选课人次
   */
  sumChooseCourseCount: number
  /**
   * 累计退课人次
   */
  totalWithdrawalCount: number
  /**
   * 净选课人次
   */
  totalChooseCourseCount: number
}

/**
 * <AUTHOR>
 */
export class CourseSalesStatisticsInfoTotal {
  /**
   * 累计选课人次
   */
  sumChooseCourseCount: number
  /**
   * 累计退课人次
   */
  totalWithdrawalCount: number
  /**
   * 净选课人次
   */
  totalChooseCourseCount: number
}

/**
 * 课件学习信息
 */
export class CoursewareLearningRecordInfo {
  /**
   * 学员课程学习记录id
   */
  courseLearningRecordId: string
  /**
   * 学习状态 0/1/2，未学习/学习中/学习完成
@see StudyStatus
   */
  status: number
  /**
   * 课件学习进度百分比
   */
  schedule: number
  /**
   * 开始学习时间
   */
  startLearningTime: string
  /**
   * 最后一次学习时间
   */
  lastLearningTime: string
  /**
   * 学习完成时间
   */
  completedTime: string
}

/**
 * 学员课程信息
<AUTHOR>
@version 1.0
@date 2022/2/9 19:07
 */
export class StudentCourse {
  /**
   * 学员课程id
   */
  studentCourseId: string
  /**
   * 学员课程状态(0：失效 1：有效)
@see StudentCourseStatus
   */
  studentCourseStatus: number
  /**
   * 学员课程学习状态（0：未评定 1：未合格 2：合格）
@see StudentCourseLearningStatus
   */
  courseLearningStatus: number
  /**
   * 选课时间
   */
  selectCourseTime: string
  /**
   * 课程学习取得结果时间
   */
  learningResultTime: string
}

/**
 * 学员课程已评价信息
<AUTHOR>
@version 1.0
@date 2022/2/9 19:01
 */
export class StudentCourseAppraised {
  /**
   * 学员是否已评价
   */
  appraisalCourse: boolean
  /**
   * 学员课程评价编号
   */
  studentCourseAppraisalId: string
  /**
   * 评价时间
   */
  appraisedTime: string
}

/**
 * @description: 学员课程信息
@author: sugs
@create: 2022-05-30 09:39
 */
export class StudentCourseInfo {
  /**
   * 学员课程id
   */
  studentCourseId: string
}

/**
 * 学员媒体课程学习信息
<AUTHOR>
@version 1.0
@date 2022/2/9 19:22
 */
export class StudentCourseMediaLearningRecord {
  /**
   * 课程学习记录id
   */
  courseLearningRecordId: string
  /**
   * 课程媒体学习进度状态（0：未评定 1：未合格 2：合格）
   */
  courseScheduleStatus: number
  /**
   * 课程学习进度百分比
   */
  schedule: number
  /**
   * 开始学习时间
   */
  startLearningTime: string
  /**
   * 最后一次学习时间
   */
  lastLearningTime: string
  /**
   * 媒体课程学习取得结果时间
   */
  learningResultTime: string
}

/**
 * 课后测验
<AUTHOR>
@version 1.0
@date 2022/2/9 19:28
 */
export class StudentCourseQuiz {
  /**
   * 课后测验id
   */
  courseQuizId: string
  /**
   * 课后测验学习状态（0：未评定 1：未合格 2：合格）
   */
  courseQuizStatus: number
  /**
   * 课后测验答卷id
   */
  courseQuizAnswerId: string
  /**
   * 课后测验得分
   */
  score: number
  /**
   * 课后测验取得结果时间
   */
  learningResultTime: string
}

export class DataManipulationResponse {
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 删除时间
   */
  deletedTime: string
  /**
   * 是否删除
   */
  isDeleted: boolean
}

/**
 * 学习心得主题
 */
export class LearningExperienceTopicResponse1 {
  /**
   * 主题ID
   */
  topicId: string
  /**
   * 心得主题名称
   */
  experienceTopicName: string
  /**
   * 参与形式
   */
  participateType: ParticipateType
  /**
   * 参加活动时间类型
   */
  participateTimeType: ParticipateTimeType
  /**
   * 活动开始时间
   */
  startTime: string
  /**
   * 活动结束时间
   */
  endTime: string
  /**
   * 提交要求（根据参与形式不同，表征的意义不同：提交附件表示附件大小限制，在线编辑表示编辑字数限制）
   */
  submitLimitNum: number
  /**
   * 是否必选
   */
  isRequired: boolean
  /**
   * 审核方式
   */
  auditType: AuditType
  /**
   * 活动内容文本
   */
  descriptionContent: string
  /**
   * 是否删除
   */
  isDelete: boolean
}

export class StatusChangeResponse {
  status: StudentLearningExperienceStatus
  changeTime: string
}

export class StudentSchemeLearningResponse {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 学习id
   */
  learningId: string
}

export class StudentLearningExperienceParticipatedStatistics {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 已参加练习数量
   */
  count: number
}

export class CourseDistributionResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseDistributionResponse>
}

export class CourseResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseResponse>
}

export class CourseInPackageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseInPackageResponse>
}

export class CourseInSchemeResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseInSchemeResponse>
}

export class CoursePackageResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursePackageResponse>
}

export class CourseSortRuleConfigResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseSortRuleConfigResponse>
}

export class CourserPackageSyncSchemeResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourserPackageSyncSchemeResponse>
}

export class CoursewareInCourseResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareInCourseResponse>
}

export class CoursewareCategoryResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareCategoryResponse>
}

export class CoursewareResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareResponse>
}

export class CoursewareLearningRecordResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareLearningRecordResponse>
}

export class CoursewareSupplierResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareSupplierResponse>
}

export class ExcellentCourseResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ExcellentCourseResponse>
}

export class StudentLearningExperienceResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentLearningExperienceResponse>
}

export class StudentLearningExperienceLastedResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentLearningExperienceLastedResponse>
}

export class LearningLogsResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LearningLogsResponse>
}

export class PlanItemAttendanceResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<PlanItemAttendanceResponse>
}

export class StudentCourseAppraiseResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentCourseAppraiseResponse>
}

export class StudentCourseLearningResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentCourseLearningResponse>
}

export class StudentLearningRuleResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentLearningRuleResponse>
}

export class ChooseCourseStatisticsInfoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ChooseCourseStatisticsInfo>
}

export class CourseSalesStatisticsInfoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseSalesStatisticsInfo>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 方便测试操作用
   * @param userId
   * @param schemeId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async clearStudentLearningRule(
    params: { userId?: string; schemeId?: string; isFresh?: boolean },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.clearStudentLearningRule,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取子项目下课程数量
   * @param servicerId 网校ID，传入查询指定网校
   * @param status     课程状态，传入查询指定状态课程数量
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async countCourseInSubProject(
    params: { servicerId?: string; status?: CourseStatusEnum },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.countCourseInSubProject,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取子项目下课件数量
   * @param servicerId 网校ID，传入查询指定网校
   * @param status     课件状态，传入查询指定状态课件数量
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async countCoursewareInSubProject(
    params: { servicerId?: string; status?: CoursewareStatusEnum },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.countCoursewareInSubProject,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下根据方案统计大纲信息
   * @param
   * @param dataFetchingEnvironment
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async countPeriodCountOfCourseTrainingOutlineInSchemeInServicer(
    request: CourseInSchemeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.countPeriodCountOfCourseTrainingOutlineInSchemeInServicer,
    operation?: string
  ): Promise<Response<CoursePeriodCountOfCourseTrainingOutlineResponse>> {
    return commonRequestApi<CoursePeriodCountOfCourseTrainingOutlineResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下课程分类详情
   * @param courseCategoryId 课程分类ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param courseCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCourseCategoryInServicer(
    courseCategoryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCourseCategoryInServicer,
    operation?: string
  ): Promise<Response<CourseCategoryResponse>> {
    return commonRequestApi<CourseCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { courseCategoryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取服务商下指定方案的课程数量
   * @return
   * @param query 查询 graphql 语法文档
   * @param getCourseCountRequest 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCourseCountBySchemeIdInServicer(
    getCourseCountRequest: GetCourseCountRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCourseCountBySchemeIdInServicer,
    operation?: string
  ): Promise<Response<Array<CourseCountBySchemeIdResponse>>> {
    return commonRequestApi<Array<CourseCountBySchemeIdResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { getCourseCountRequest },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下课程详情
   * @param courseId 课程分类ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCourseInServicer(
    courseId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCourseInServicer,
    operation?: string
  ): Promise<Response<CourseDetailResponse>> {
    return commonRequestApi<CourseDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { courseId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下的课程包详情
   * @param coursePackageId 课程包id
   * @return 课程包详情
   * @param query 查询 graphql 语法文档
   * @param coursePackageId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCoursePackageInServicer(
    coursePackageId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCoursePackageInServicer,
    operation?: string
  ): Promise<Response<CoursePackageResponse>> {
    return commonRequestApi<CoursePackageResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { coursePackageId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取课程排序规则配置详情
   * @param query 查询 graphql 语法文档
   * @param configId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCourseSortRuleConfigDetailInServicer(
    configId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCourseSortRuleConfigDetailInServicer,
    operation?: string
  ): Promise<Response<CourseSortRuleConfigDetailResponse>> {
    return commonRequestApi<CourseSortRuleConfigDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { configId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下的课程学习大纲
   * @param outlineId 课程学习大纲id(必填)
   * @return
   * @param query 查询 graphql 语法文档
   * @param outlineId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCourseTrainingOutlineInServicer(
    outlineId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCourseTrainingOutlineInServicer,
    operation?: string
  ): Promise<Response<CourseTrainingOutlineResponse>> {
    return commonRequestApi<CourseTrainingOutlineResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { outlineId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下课件分类详情
   * @param coursewareCategoryId 课件分类ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param coursewareCategoryId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCoursewareCategoryInServicer(
    coursewareCategoryId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCoursewareCategoryInServicer,
    operation?: string
  ): Promise<Response<CoursewareCategoryResponse>> {
    return commonRequestApi<CoursewareCategoryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { coursewareCategoryId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下课件详情
   * @param coursewareId 课件ID
   * @return
   * @param query 查询 graphql 语法文档
   * @param coursewareId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getCoursewareInServicer(
    coursewareId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCoursewareInServicer,
    operation?: string
  ): Promise<Response<CoursewareDetailResponse>> {
    return commonRequestApi<CoursewareDetailResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { coursewareId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下内容id对应的学习心得内容
   * @returns
   * @param query 查询 graphql 语法文档
   * @param contentId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getLearningExperienceContentInServicer(
    contentId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getLearningExperienceContentInServicer,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: { contentId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下指定课程包中重复的课程
   * @param request 课程包中的重复课程请求参数
   * @return 课程包中的重复课程信息 {@link List<RepeatedCoursesInSpecifiedCoursePackageResponse>}
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getRepeatedCoursesInSpecifiedCoursePackageInServicer(
    request: RepeatedCoursesInSpecifiedCoursePackageRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRepeatedCoursesInSpecifiedCoursePackageInServicer,
    operation?: string
  ): Promise<Response<Array<RepeatedCoursesInSpecifiedCoursePackageResponse>>> {
    return commonRequestApi<Array<RepeatedCoursesInSpecifiedCoursePackageResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 按星级分组统计课程评价数量
   * @param query 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getStudentCourseAppraiseStarStatisticsInCourseSupplier(
    courseId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentCourseAppraiseStarStatisticsInCourseSupplier,
    operation?: string
  ): Promise<Response<StudentCourseAppraiseStarStatisticsResponse>> {
    return commonRequestApi<StudentCourseAppraiseStarStatisticsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { courseId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 按星级分组统计课程评价数量
   * @param query 查询 graphql 语法文档
   * @param courseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getStudentCourseAppraiseStarStatisticsInServicer(
    courseId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentCourseAppraiseStarStatisticsInServicer,
    operation?: string
  ): Promise<Response<StudentCourseAppraiseStarStatisticsResponse>> {
    return commonRequestApi<StudentCourseAppraiseStarStatisticsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { courseId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下的学员课程学习详情
   * @param studentCourseId 学员课程Id
   * @param query 查询 graphql 语法文档
   * @param studentCourseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getStudentCourseLearningInServicer(
    studentCourseId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentCourseLearningInServicer,
    operation?: string
  ): Promise<Response<StudentCourseLearningResponse>> {
    return commonRequestApi<StudentCourseLearningResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { studentCourseId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前子项目下的学员课程学习详情
   * @param studentCourseId 学员课程Id
   * @param query 查询 graphql 语法文档
   * @param studentCourseId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getStudentCourseLearningInSubProject(
    studentCourseId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentCourseLearningInSubProject,
    operation?: string
  ): Promise<Response<StudentCourseLearningResponse>> {
    return commonRequestApi<StudentCourseLearningResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { studentCourseId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取规则详情
   * @param ruleId
   * @return
   * @param query 查询 graphql 语法文档
   * @param ruleId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getStudentLearningRule(
    ruleId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentLearningRule,
    operation?: string
  ): Promise<Response<StudentLearningRuleResponse>> {
    return commonRequestApi<StudentLearningRuleResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { ruleId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 课程包是否被方案引为必修
   * @param coursePackageId 课程包Id
   * @return true 引用 false 不引用
   * @param query 查询 graphql 语法文档
   * @param coursePackageId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async isBeingUsedAsCompulsory(
    coursePackageId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.isBeingUsedAsCompulsory,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { coursePackageId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下课程分类列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listCourseCategoryInServicer(
    request: CourseCategoryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listCourseCategoryInServicer,
    operation?: string
  ): Promise<Response<Array<CourseCategoryResponse>>> {
    return commonRequestApi<Array<CourseCategoryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下的培训方案配置的课程列表
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listCourseInSchemeInServicer(
    request: CourseInSchemeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listCourseInSchemeInServicer,
    operation?: string
  ): Promise<Response<Array<CourseInSchemeResponse>>> {
    return commonRequestApi<Array<CourseInSchemeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 聚合查询当前服务商下的学员心得主题列表
   * 取最后一次提交的审核结果、聚合出已提交的次数 （不含已删除、待提交的） 创
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listLearningExperienceLatestInServicer(
    params: { request?: StudentLearningExperienceRequest; sorts?: Array<StudentLearningExperienceSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listLearningExperienceLatestInServicer,
    operation?: string
  ): Promise<Response<Array<StudentLearningExperienceLastedResponse>>> {
    return commonRequestApi<Array<StudentLearningExperienceLastedResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * list查询学习心得主题配置
   * @param
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listLearningExperienceTopic(
    request: LearningExperienceTopicRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listLearningExperienceTopic,
    operation?: string
  ): Promise<Response<Array<LearningExperienceTopicResponse>>> {
    return commonRequestApi<Array<LearningExperienceTopicResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取当前服务商下教师列表
   * @param teacherIds 教师id集合
   * @return 当前服务商下教师列表
   * @param query 查询 graphql 语法文档
   * @param teacherIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listTeacherInServicer(
    teacherIds: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listTeacherInServicer,
    operation?: string
  ): Promise<Response<Array<TeacherResponse>>> {
    return commonRequestApi<Array<TeacherResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { teacherIds },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 超管选课统计分页查询
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageChooseCourseStatistics(
    params: { page?: Page; request?: ChooseCourseStatisticsRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageChooseCourseStatistics,
    operation?: string
  ): Promise<Response<ChooseCourseStatisticsResponse>> {
    return commonRequestApi<ChooseCourseStatisticsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 课程供应商的选课统计(以课程为维度)
   * @param page    分页参数
   * @param request 请求参数
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageChooseCourseStatisticsInCourseSupplier(
    params: { page?: Page; request?: ChooseCourseStatisticsRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageChooseCourseStatisticsInCourseSupplier,
    operation?: string
  ): Promise<Response<ChooseCourseStatisticsResponse>> {
    return commonRequestApi<ChooseCourseStatisticsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取服务商下的选课统计(以课程为维度)
   * @param page    分页参数
   * @param request 请求参数
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageChooseCourseStatisticsInServicer(
    params: { page?: Page; request?: ChooseCourseStatisticsRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageChooseCourseStatisticsInServicer,
    operation?: string
  ): Promise<Response<ChooseCourseStatisticsResponse>> {
    return commonRequestApi<ChooseCourseStatisticsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查看我的选课记录
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageChooseCourseStatisticsMyself(
    params: { page?: Page; request?: ChooseCourseStatisticsRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageChooseCourseStatisticsMyself,
    operation?: string
  ): Promise<Response<ChooseCourseStatisticsResponse>> {
    return commonRequestApi<ChooseCourseStatisticsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取课程分布  只有工勤有需求 在工勤上实现  所以这个口暂时不做实现
   * @param request 分页获取课程分布
   * @return 分页获取课程分布
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCourseDistributionResponse(
    params: { page?: Page; request?: CourseDistributionRequest; sort?: Array<CourseDistributionSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourseDistributionResponse,
    operation?: string
  ): Promise<Response<CourseDistributionResponsePage>> {
    return commonRequestApi<CourseDistributionResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取当前课程/课件供应商下课程分页
   * @param page    分页对象
   * @param request 查询参数对象
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCourseInCourseSupplier(
    params: { page?: Page; request?: CourseRequest; sort?: Array<CourseSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourseInCourseSupplier,
    operation?: string
  ): Promise<Response<CourseResponsePage>> {
    return commonRequestApi<CourseResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取当前服务商下的课程包下课程列表
   * @param page    分页参数
   * @param request 查询参数
   * @return 当前服务商下的课程包下课程分页列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCourseInPackageInServicer(
    params: { page?: Page; request?: CourseInPackageRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourseInPackageInServicer,
    operation?: string
  ): Promise<Response<CourseInPackageResponsePage>> {
    return commonRequestApi<CourseInPackageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取当前服务商下的课程包下课程列表
   * @param request 课程包下课程请求参数
   * @return 当前服务商下的课程包下课程列表 {@link  Page<CourseInPackageResponse> }
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCourseInPackageV2InServicer(
    request: CourseInPackageRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourseInPackageV2InServicer,
    operation?: string
  ): Promise<Response<CourseInPackageResponsePage>> {
    return commonRequestApi<CourseInPackageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取当前服务商下的培训方案配置的课程列表
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCourseInSchemeInServicer(
    params: { page?: Page; request?: CourseInSchemeRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourseInSchemeInServicer,
    operation?: string
  ): Promise<Response<CourseInSchemeResponsePage>> {
    return commonRequestApi<CourseInSchemeResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下课程分页
   * @param page    分页对象
   * @param request 查询参数对象
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCourseInServicer(
    params: { page?: Page; request?: CourseRequest; sort?: Array<CourseSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourseInServicer,
    operation?: string
  ): Promise<Response<CourseResponsePage>> {
    return commonRequestApi<CourseResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取当前服务商下的课程包列表
   * @param page    分页参数
   * @param request 课程包查询请求参数
   * @return 课程包分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCoursePackageInServicer(
    params: { page?: Page; request?: CoursePackageRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursePackageInServicer,
    operation?: string
  ): Promise<Response<CoursePackageResponsePage>> {
    return commonRequestApi<CoursePackageResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 培训单位销售统计
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCourseSalesStatistics(
    params: { page?: Page; request?: CourseSalesStatisticsRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourseSalesStatistics,
    operation?: string
  ): Promise<Response<CourseSalesStatisticsResponse>> {
    return commonRequestApi<CourseSalesStatisticsResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取课程排序规则配置
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCourseSortRuleConfigInServicer(
    params: { page?: Page; request?: CourseSortRuleConfigRequest; sort?: Array<CourseSortRuleConfigSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourseSortRuleConfigInServicer,
    operation?: string
  ): Promise<Response<CourseSortRuleConfigResponsePage>> {
    return commonRequestApi<CourseSortRuleConfigResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下课程分页
   * @param request 查询参数对象
   * @return 当前服务商下课程分页 {@link Page<CourseResponse>}
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCourseV2InServicer(
    request: CourseV2Request,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourseV2InServicer,
    operation?: string
  ): Promise<Response<CourseResponsePage>> {
    return commonRequestApi<CourseResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取当前服务商下的课程包同步方案情况列表
   * @param page    分页参数
   * @param request 课程包下方案同步查询条件
   * @return 当前服务商下的课程包同步方案情况分页列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCourserPackageSyncSchemeInServicer(
    params: { page?: Page; request?: CourserPackageSyncSchemeRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCourserPackageSyncSchemeInServicer,
    operation?: string
  ): Promise<Response<CourserPackageSyncSchemeResponsePage>> {
    return commonRequestApi<CourserPackageSyncSchemeResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 学号获取课件信息
   * @param page
   * @param studentNo
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCoursewareByStudentNoInSubProject(
    params: { page?: Page; studentNo?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursewareByStudentNoInSubProject,
    operation?: string
  ): Promise<Response<CoursewareInCourseResponsePage>> {
    return commonRequestApi<CoursewareInCourseResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下课件分类分页
   * @param page    分页对象
   * @param request 查询参数对象
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCoursewareCategoryInServicer(
    params: { page?: Page; request?: CoursewareCategoryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursewareCategoryInServicer,
    operation?: string
  ): Promise<Response<CoursewareCategoryResponsePage>> {
    return commonRequestApi<CoursewareCategoryResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下课件分页
   * @param page    分页对象
   * @param request 查询参数对象
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCoursewareInServicer(
    params: { page?: Page; request?: CoursewareRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursewareInServicer,
    operation?: string
  ): Promise<Response<CoursewareResponsePage>> {
    return commonRequestApi<CoursewareResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取当前服务商下的学员课件学习记录列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCoursewareLearningRecordInServicer(
    params: { page?: Page; request?: CoursewareLearningRecordRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursewareLearningRecordInServicer,
    operation?: string
  ): Promise<Response<CoursewareLearningRecordResponsePage>> {
    return commonRequestApi<CoursewareLearningRecordResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取当前子项目下的学员课件学习记录列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCoursewareLearningRecordInSubProject(
    params: { page?: Page; request?: CoursewareLearningRecordRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursewareLearningRecordInSubProject,
    operation?: string
  ): Promise<Response<CoursewareLearningRecordResponsePage>> {
    return commonRequestApi<CoursewareLearningRecordResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取子项目下课件服务商
   * @param page 分页参数
   * @return 子项目下课件服务商分页信息
   * @param query 查询 graphql 语法文档
   * @param page 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageCoursewareSupplierInSubProject(
    page: Page,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageCoursewareSupplierInSubProject,
    operation?: string
  ): Promise<Response<CoursewareSupplierResponsePage>> {
    return commonRequestApi<CoursewareSupplierResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: { page },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取前服务商下精品课程信息
   * @param page    分页参数
   * @param request 请求擦书
   * @param sort    排序参数
   * @return 精品课程
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageExcellentCourseInServicer(
    params: { page?: Page; request?: ExcellentCourseRequest; sort?: Array<ExcellentCourseSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageExcellentCourseInServicer,
    operation?: string
  ): Promise<Response<ExcellentCourseResponsePage>> {
    return commonRequestApi<ExcellentCourseResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询当前服务商下的学员学习心得所有的提交记录的分页列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageLearningExperienceInServicer(
    params: {
      page?: Page
      request?: StudentLearningExperienceRequest
      sorts?: Array<StudentLearningExperienceSortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageLearningExperienceInServicer,
    operation?: string
  ): Promise<Response<StudentLearningExperienceResponsePage>> {
    return commonRequestApi<StudentLearningExperienceResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 当前学员的学员心得主题列表 ->已参加 不回显
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageLearningExperienceParticipatedInServicer(
    params: {
      page?: Page
      request?: StudentLearningExperienceRequest
      sorts?: Array<StudentLearningExperienceSortRequest>
    },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageLearningExperienceParticipatedInServicer,
    operation?: string
  ): Promise<Response<StudentLearningExperienceLastedResponsePage>> {
    return commonRequestApi<StudentLearningExperienceLastedResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学习日志
   * 记录时间正序
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageLearningLogsInDistributor(
    params: { page?: Page; pageLearningLogsRequest?: PageLearningLogsRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageLearningLogsInDistributor,
    operation?: string
  ): Promise<Response<LearningLogsResponsePage>> {
    return commonRequestApi<LearningLogsResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 学习日志
   * 记录时间正序
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageLearningLogsInServicer(
    params: { page?: Page; pageLearningLogsRequest?: PageLearningLogsRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageLearningLogsInServicer,
    operation?: string
  ): Promise<Response<LearningLogsResponsePage>> {
    return commonRequestApi<LearningLogsResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询当前网校下学员教学计划签到情况列表
   * @param page            分页参数
   * @param qualificationId 期别参训资格id
   * @return 分页结果
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pagePlanItemAttendanceInServicer(
    params: { page?: Page; qualificationId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pagePlanItemAttendanceInServicer,
    operation?: string
  ): Promise<Response<PlanItemAttendanceResponsePage>> {
    return commonRequestApi<PlanItemAttendanceResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 课程供应商 学员课程评价分页
   * @param page
   * @param courseId 课程id
   * @return 学员课程评价
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageStudentCourseAppraiseInCourseSupplier(
    params: { page?: Page; courseId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentCourseAppraiseInCourseSupplier,
    operation?: string
  ): Promise<Response<StudentCourseAppraiseResponsePage>> {
    return commonRequestApi<StudentCourseAppraiseResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下学员课程评价分页
   * @param page
   * @param courseId 课程id
   * @return 学员课程评价
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageStudentCourseAppraiseInServicer(
    params: { page?: Page; courseId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentCourseAppraiseInServicer,
    operation?: string
  ): Promise<Response<StudentCourseAppraiseResponsePage>> {
    return commonRequestApi<StudentCourseAppraiseResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取当前服务商下的学员课程学习列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageStudentCourseInServicer(
    params: { page?: Page; request?: StudentCourseLearningCommonRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentCourseInServicer,
    operation?: string
  ): Promise<Response<StudentCourseLearningResponsePage>> {
    return commonRequestApi<StudentCourseLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取当前子项目下的学员课程学习列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageStudentCourseInSubProject(
    params: { page?: Page; request?: StudentCourseLearningRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentCourseInSubProject,
    operation?: string
  ): Promise<Response<StudentCourseLearningResponsePage>> {
    return commonRequestApi<StudentCourseLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取学习规则列表
   * @param page
   * @param dataFetchingEnvironment
   * @return
   * @param query 查询 graphql 语法文档
   * @param page 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageStudentLearningRule(
    page: Page,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentLearningRule,
    operation?: string
  ): Promise<Response<StudentLearningRuleResponsePage>> {
    return commonRequestApi<StudentLearningRuleResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: { page },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下的课程包下方案同步情况统计
   * @param request 课程包下方案同步查询条件
   * @return 当前服务商下的课程包下方案同步情况统计
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async statisticCourserPackageSyncSchemeInServicer(
    request: CourserPackageSyncSchemeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticCourserPackageSyncSchemeInServicer,
    operation?: string
  ): Promise<Response<CourserPackageSyncSchemeStatisticResponse>> {
    return commonRequestApi<CourserPackageSyncSchemeStatisticResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下根据方案统计大纲信息
   * @param studentNo               学号
   * @param dataFetchingEnvironment
   * @returns
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async statisticsCourseInSchemeInMyself(
    params: { studentNo?: string; schemeId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticsCourseInSchemeInMyself,
    operation?: string
  ): Promise<Response<Array<CourseTrainingOutlineResponse>>> {
    return commonRequestApi<Array<CourseTrainingOutlineResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下根据方案统计大纲信息
   * @param schemeId                学习方案id
   * @param dataFetchingEnvironment
   * @return
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async statisticsCourseInSchemeInServicer(
    schemeId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticsCourseInSchemeInServicer,
    operation?: string
  ): Promise<Response<Array<CourseTrainingOutlineResponse>>> {
    return commonRequestApi<Array<CourseTrainingOutlineResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticsLearningExperienceParticipatedBySchemeIdInServicer(
    params: { schemeIds?: Array<string>; userId?: string },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.statisticsLearningExperienceParticipatedBySchemeIdInServicer,
    operation?: string
  ): Promise<Response<Array<StudentLearningExperienceParticipatedStatistics>>> {
    return commonRequestApi<Array<StudentLearningExperienceParticipatedStatistics>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建课程排序规则配置
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async createCourseSortRuleConfigInServicer(
    request: CreateCourseSortRuleConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createCourseSortRuleConfigInServicer,
    operation?: string
  ): Promise<Response<BaseResponse>> {
    return commonRequestApi<BaseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 修改课程排序规则配置
   * @param request
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async updateCourseSortRuleConfigInServicer(
    request: UpdateCourseSortRuleConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateCourseSortRuleConfigInServicer,
    operation?: string
  ): Promise<Response<BaseResponse>> {
    return commonRequestApi<BaseResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
