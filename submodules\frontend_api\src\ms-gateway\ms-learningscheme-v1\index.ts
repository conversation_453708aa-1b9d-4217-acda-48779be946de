import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-learningscheme-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-learningscheme-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class Property {
  name: string
  value?: string
}

/**
 * <AUTHOR> create 2021/7/6 9:04
 */
export class UpdateLearningSchemeCommand {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方案名称
   */
  name: string
  /**
   * 学习方案封面图片
   */
  picture?: string
  /**
   * 学习方案介绍内容ID
   */
  commentId?: string
  /**
   * 报名开始时间
   */
  registerBeginDate?: string
  /**
   * 报名结束时间
   */
  registerEndDate?: string
  /**
   * 培训开始时间
   */
  trainingBeginDate?: string
  /**
   * 培训结束时间
   */
  trainingEndDate?: string
  /**
   * 是否提供重学
   */
  provideRelearn: boolean
  /**
   * 简介id
   */
  introId?: string
  /**
   * 培训须知
   */
  notice?: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 扩展属性集合
   */
  properties?: Array<Property>
  /**
   * 地区
   */
  region?: string
}

export class MetaData {
  /**
   * key
包含学习方式key+合格属性key以.分隔
chooseCourseLearning:选课学习 chooseCourseLearning.courseQuizQualifiedScore
examLearning:考试 examLearning.qualifiedScore
practiceLearning:练习
autonomousCourseLearning:自主学习 autonomousCourseLearning.courseQuizQualifiedScore
interestCourseLearning:兴趣课学习
@see
   */
  key?: string
  /**
   * 值
   */
  value?: string
}

/**
 * 生成自动学习 token
<AUTHOR> create 2021/4/15 8:48
 */
export class ApplyAutoLearningTokenRequest {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学习方式id
   */
  learningId: string
}

/**
 * 生成重新学习 token
<AUTHOR> create 2021/4/15 8:48
 */
export class ApplyReLearnTokenRequest {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学习方式id
   */
  learningId: string
}

/**
 * 异步创建学习方案请求
<AUTHOR>
 */
export class AsyncCreateLearningSchemeRequest {
  /**
   * 校验token
   */
  token?: string
  /**
   * 方案配置json字符串
   */
  configJson?: string
}

/**
 * 异步删除学习方案请求
<AUTHOR>
 */
export class AsyncRemoveLearningSchemeRequest {
  schemeId?: string
}

/**
 * 异步修改学习方案请求
<AUTHOR>
 */
export class AsyncUpdateLearningSchemeRequest {
  /**
   * 校验token
   */
  token?: string
  /**
   * 方案配置json字符串
   */
  configJson?: string
}

/**
 * 批量更新商品购买渠道命令
<AUTHOR> By Cb
@since 2024/4/26 10:21
 */
export class BatchUpdateCommodityPurchaseChannelRequest {
  /**
   * 服务商ID
   */
  servicerId?: string
  /**
   * 需要更新的方案ID列表
   */
  schemeIdList?: Array<string>
  /**
   * 跳过更新的方案ID列表
   */
  skipSchemeIdList?: Array<string>
  /**
   * 是否覆盖方案配置
默认: true
   */
  overwriteSchemeConfigure: boolean
  /**
   * 开启的购买渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  openPurchaseChannelList?: Array<number>
}

export class BatchUpdateLearningSchemeRequest {
  /**
   * 文件路径
   */
  filePath: string
  /**
   * 文件名
   */
  fileName?: string
}

/**
 * 批量方案地区命令
<AUTHOR> By Cb
@since 2024/4/26 10:21
 */
export class BatchUpdateSchemeRegionRequest {
  /**
   * 服务商ID
   */
  servicerId?: string
  /**
   * 需要更新的方案数据
   */
  schemeInfoList?: Array<StringKeyValue>
}

export class StringKeyValue {
  key?: string
  value?: string
}

/**
 * 方案配置是否由事务处理查询请求
<AUTHOR>
 */
export class IsProcessedByTransactionRequest {
  /**
   * 学习方案id
   */
  schemeIds?: Array<string>
}

/**
 * 学习方案创建修复请求
<AUTHOR> By Cb
@since 2023/08/30 9:54
 */
export class LearningSchemeCreateRepairRequestCommand {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 学习方案名称
   */
  name?: string
  /**
   * 学习方案封面图片
   */
  picture?: string
  /**
   * 学习方案介绍内容ID
   */
  commentId?: string
  /**
   * 类型
   */
  type?: string
  /**
   * 年度
   */
  year?: string
  /**
   * 地区
   */
  region?: string
  /**
   * 报名开始时间
   */
  registerBeginDate?: string
  /**
   * 报名结束时间
   */
  registerEndDate?: string
  /**
   * 培训开始时间
   */
  trainingBeginDate?: string
  /**
   * 培训结束时间
   */
  trainingEndDate?: string
  /**
   * 是否提供重学
   */
  provideRelearn: boolean
  /**
   * 扩展属性集合
   */
  properties?: Array<Property>
  /**
   * 简介内容
   */
  intro?: string
  /**
   * 培训须知
   */
  notice?: string
}

/**
 * <AUTHOR> create 2022/5/25 16:14
 */
export class OneKeyPassRequest {
  /**
   * 参训资格id
   */
  qualificationId?: string
  /**
   * 合格时间配置 1：按系统当前操作成功时间
   */
  passTimeType: number
  /**
   * 合格元数据
   */
  metaDataList?: Array<MetaData>
}

/**
 * 分页查询培训方案批量更新导入任务数据请求信息
 */
export class PageLearningSchemeImportTaskRequest {
  /**
   * 任务名称
   */
  taskName?: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState?: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult?: number
  /**
   * 执行时间（起始）
   */
  executeStartTime?: string
  /**
   * 执行时间（终止）
   */
  executeEndTime?: string
}

/**
 * 数据修复使用
<AUTHOR>
 */
export class RefreshSchemeConfigRequest {
  schemeId?: string
  configJson?: string
}

/**
 * 预约方案校验请求
<AUTHOR> create 2022/3/10 14:30
 */
export class ReservingSchemeIssueValidateRequest {
  /**
   * 方案id
   */
  schemeId: string
  issueId?: string
}

/**
 * 预约方案校验请求
<AUTHOR> create 2022/3/10 14:30
 */
export class ReservingSchemeValidateRequest {
  /**
   * 方案id
   */
  schemeId: string
}

/**
 * 用户报名方案数据修复请求
<AUTHOR>
 */
export class UserEnrolmentSchemeRepairRequest {
  /**
   * 用户id
   */
  userId?: string
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 报名来源类型
@see StudentSourceTypes
   */
  sourceType?: string
  /**
   * 报名来源ID
   */
  sourceId?: string
}

export class DuplicateReservingInfo {
  id: string
  sourceType: string
  sourceId: string
  status: number
}

/**
 * 学习方案处理转台响应
<AUTHOR>
 */
export class LearningSchemeProcessStatusResponse {
  /**
   * 方案配置最后执行事务状态
@see com.fjhb.domain.learningscheme.api.configure.consts.LearningSchemeConfigureTransactionStatus
   */
  lastTransactionStep: number
  /**
   * 事务是否挂起
   */
  hangUp: boolean
  /**
   * 是否重算中
   */
  recalculating: boolean
  /**
   * 异常
   */
  errors: Array<ResultResponse>
}

/**
 * 执行结果响应对象
<AUTHOR>
 */
export class ResultResponse {
  /**
   * 状态码
   */
  code: string
  /**
   * 信息
   */
  message: string
}

/**
 * 申请自动学习token响应
<AUTHOR> create 2022/1/12 14:54
 */
export class ApplyTrainingQualificationAutoLearningTokenResponse {
  /**
   * 重学token
   */
  token: string
}

/**
 * 参训资格重新学习命令
将清空所有学习记录和考核
<AUTHOR> create 2022/1/12 14:54
 */
export class ApplyTrainingQualificationRelearnTokenResponse {
  /**
   * ID
   */
  qualificationId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 重学token
   */
  token: string
  /**
   * 培训结果.
-1:未知，培训尚未完成
1:培训合格
0:培训不合格
@see StudentTrainingResults
@see StudentTrainingQualifiedEvent
@see StudentTrainingUnqualifiedEvent
@see StudentTrainingResultRevokedEvent
   */
  trainingResult: number
}

export class ExportUpdateLearningSchemeImportResultResponse {
  /**
   * 导入批量修改方案信息执行结果文件地址
   */
  fileUrl: string
}

/**
 * 批量更新培训方案导入任务数据信息
 */
export class LearningSchemeImportTaskResponse {
  /**
   * 任务编号
   */
  id: string
  /**
   * 【必填】平台编号
   */
  platformId: string
  /**
   * 【必填】平台版本编号
   */
  platformVersionId: string
  /**
   * 【必填】项目编号
   */
  projectId: string
  /**
   * 【必填】子项目编号
   */
  subProjectId: string
  /**
   * 任务名称
   */
  name: string
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  taskState: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  processResult: number
  /**
   * 处理信息
   */
  message: string
  /**
   * 处理时间
   */
  executingTime: string
  /**
   * 结束（完成）时间
   */
  completedTime: string
  /**
   * 各状态及执行结果对应数量集合
总数：全部数量之和
成功数：result &#x3D; 1数量之和
失败数：result &#x3D; 2数量之和
   */
  eachStateCounts: Array<EachStateCount>
}

/**
 * 各状态及执行结果对应数量
 */
export class EachStateCount {
  /**
   * 任务执行状态
0-已创建 1-已就绪 2-执行中 3-已完成
@see com.fjhb.batchtask.core.enums.TaskState
   */
  state: number
  /**
   * 执行结果
0-未处理 1-成功 2-失败 3-就绪失败
@see com.fjhb.batchtask.core.enums.ProcessResult
   */
  result: number
  /**
   * 数量
   */
  count: number
}

/**
 * 方案是否被事务方式处理结果集
<AUTHOR>
 */
export class ProcessedByTransactionResponse {
  results: Array<ProcessedByTransactionResult>
}

/**
 * 方案是否被事务方式处理
<AUTHOR>
 */
export class ProcessedByTransactionResult {
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 是否被处理过
   */
  isProcessed: boolean
}

/**
 * 预约方案校验响应
<AUTHOR> create 2022/3/10 15:19
 */
export class ReservingSchemeValidateResponse {
  /**
   * 正常&#x3D;200.
不可重复报名同一个班级 &#x3D; 50001
培训未开始 &#x3D; 50002
培训已结束 &#x3D; 50003
报名未开始 &#x3D; 50004
报名已结束 &#x3D; 50005
500 其他未定义异常
   */
  code: string
  /**
   * 异常信息
   */
  message: string
  /**
   * 重复预约信息
不可重复报名同一个班级 &#x3D; 50001返回
   */
  duplicateReservingInfos: Array<DuplicateReservingInfo>
}

export class LearningSchemeImportTaskResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LearningSchemeImportTaskResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取导入模版下载路径
   * /ms-file/~~
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getImportUpdateTemplateUrl(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getImportUpdateTemplateUrl,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 方案配置是否由事务处理查询(临时接口)
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async isProcessedByTransaction(
    request: IsProcessedByTransactionRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.isProcessedByTransaction,
    operation?: string
  ): Promise<Response<ProcessedByTransactionResponse>> {
    return commonRequestApi<ProcessedByTransactionResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学习方案配置执行事务阶段查询请求
   * @see LearningSchemeConfigureTransactionStatus
   * 状态值说明：
   * 已开始 = 1
   * 预提交 = 2
   * 提交 = 3
   * 回滚 = 4
   * 完成 = 5(仅此状态，且重算完成的情况下，方案可修改)
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningSchemeProcessTransactionStepQuery(
    schemeId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.learningSchemeProcessTransactionStepQuery,
    operation?: string
  ): Promise<Response<LearningSchemeProcessStatusResponse>> {
    return commonRequestApi<LearningSchemeProcessStatusResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { schemeId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询批量更新培训方案导入任务结果分页列表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageImportLearningSchemeImportTask(
    params: { page?: Page; request?: PageLearningSchemeImportTaskRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageImportLearningSchemeImportTask,
    operation?: string
  ): Promise<Response<LearningSchemeImportTaskResponsePage>> {
    return commonRequestApi<LearningSchemeImportTaskResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 管理人员申请自动学习token
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyAutoLearningTokenForManage(
    request: ApplyAutoLearningTokenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyAutoLearningTokenForManage,
    operation?: string
  ): Promise<Response<ApplyTrainingQualificationAutoLearningTokenResponse>> {
    return commonRequestApi<ApplyTrainingQualificationAutoLearningTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 管理人员申请重学token(班级不提供重学也能获取到重学token)
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyRelearnTokenForManage(
    request: ApplyReLearnTokenRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyRelearnTokenForManage,
    operation?: string
  ): Promise<Response<ApplyTrainingQualificationRelearnTokenResponse>> {
    return commonRequestApi<ApplyTrainingQualificationRelearnTokenResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 异步创建学习方案配置接口
   * @return 学习方案id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async asyncCreateLearningScheme(
    request: AsyncCreateLearningSchemeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.asyncCreateLearningScheme,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async asyncRemoveLearningScheme(
    request: AsyncRemoveLearningSchemeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.asyncRemoveLearningScheme,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 异步修改学习方案配置接口
   * @return 学习方案id
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async asyncUpdateLearningScheme(
    request: AsyncUpdateLearningSchemeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.asyncUpdateLearningScheme,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量更新商品购买渠道
   * @param request:
   * <AUTHOR> By Cb
   * @since 2024/4/26 10:29
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchUpdateCommodityPurchaseChannel(
    request: BatchUpdateCommodityPurchaseChannelRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchUpdateCommodityPurchaseChannel,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量修改培训方案
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchUpdateLearningScheme(
    request: BatchUpdateLearningSchemeRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchUpdateLearningScheme,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出全部培训方案数据（本次批量修改的全部数据）
   * @param mutate 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchUpdateLearningSchemeExport(
    mainTaskId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchUpdateLearningSchemeExport,
    operation?: string
  ): Promise<Response<ExportUpdateLearningSchemeImportResultResponse>> {
    return commonRequestApi<ExportUpdateLearningSchemeImportResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出失败数据（批量修改培训方案数据）
   * @param mutate 查询 graphql 语法文档
   * @param mainTaskId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchUpdateLearningSchemeExportFail(
    mainTaskId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchUpdateLearningSchemeExportFail,
    operation?: string
  ): Promise<Response<ExportUpdateLearningSchemeImportResultResponse>> {
    return commonRequestApi<ExportUpdateLearningSchemeImportResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { mainTaskId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量刷新方案地区
   * @param request:
   * <AUTHOR> By Cb
   * @since 2024/5/6 10:12
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async batchUpdateSchemeRegion(
    request: BatchUpdateSchemeRegionRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.batchUpdateSchemeRegion,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 创建学习方案
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createLearningScheme(
    params: { token?: string; configJson?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.createLearningScheme,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学习方案创建修复
   * @param requestCommand :
   * @return java.lang.String
   * <AUTHOR> By Cb
   * @since 2023/8/30 9:58
   * @param mutate 查询 graphql 语法文档
   * @param requestCommand 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningSchemeCreateRepair(
    requestCommand: LearningSchemeCreateRepairRequestCommand,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.learningSchemeCreateRepair,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { requestCommand },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学习方案更新修复
   * @param command:
   * @return {@link String}
   * <AUTHOR> By Cb
   * @since 2024/4/24 15:18
   * @param mutate 查询 graphql 语法文档
   * @param command 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningSchemeUpdateRepair(
    command: UpdateLearningSchemeCommand,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.learningSchemeUpdateRepair,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { command },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 一键合格
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async oneKeyPass(
    request: OneKeyPassRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.oneKeyPass,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 刷新方案配置且会同步更新学习方案
   * @param request:
   * <AUTHOR> By Cb
   * @since 2024/5/6 10:02
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async refreshConfigAndUpdate(
    request: RefreshSchemeConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.refreshConfigAndUpdate,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async refreshSchemeConfig(
    request: RefreshSchemeConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.refreshSchemeConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 刷新方案配置
   * @param request:
   * <AUTHOR> By Cb
   * @since 2024/4/26 10:29
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async refreshSchemeConfigAndSaveConfig(
    request: RefreshSchemeConfigRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.refreshSchemeConfigAndSaveConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员重学
   * @param mutate 查询 graphql 语法文档
   * @param qualificationId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async relearnForStudent(
    qualificationId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.relearnForStudent,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { qualificationId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除学习方案
   * @param mutate 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeLearningScheme(
    id: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.removeLearningScheme,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { id },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 事务修复接口
   * @param schemeId      学习方案id
   * @param transactionId 事务ID
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async repairLearningSchemeProcessTransaction(
    params: { schemeId?: string; transactionId?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.repairLearningSchemeProcessTransaction,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验方案期别是否能够预约
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async reservingSchemeIssueValidate(
    request: ReservingSchemeIssueValidateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.reservingSchemeIssueValidate,
    operation?: string
  ): Promise<Response<ReservingSchemeValidateResponse>> {
    return commonRequestApi<ReservingSchemeValidateResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验方案是否能够预约
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async reservingSchemeValidate(
    request: ReservingSchemeValidateRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.reservingSchemeValidate,
    operation?: string
  ): Promise<Response<ReservingSchemeValidateResponse>> {
    return commonRequestApi<ReservingSchemeValidateResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重试配置资源处理完成事件处理
   * @return java.lang.String
   * <AUTHOR> By Cb
   * @since 2023/9/11 16:51
   * @param mutate 查询 graphql 语法文档
   * @param payload 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async retryLearningConfigureResourceProcessCompletedEventHandle(
    payload: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.retryLearningConfigureResourceProcessCompletedEventHandle,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { payload },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 特殊修改学习方案
   * 只针对方案上的配置和商品配置进行修改 其余配置就算传入也不会进行操作
   * @param token      :
   * @param configJson :
   * @return java.lang.String
   * <AUTHOR> By Cb
   * @since 2023/9/4 9:40
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async specialUpdateLearningScheme(
    params: { token?: string; configJson?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.specialUpdateLearningScheme,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 修改学习方案
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateLearningScheme(
    params: { token?: string; configJson?: string },
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.updateLearningScheme,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 用户报名方案补偿接口
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async userEnrolmentSchemeRepair(
    request: UserEnrolmentSchemeRepairRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.userEnrolmentSchemeRepair,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
