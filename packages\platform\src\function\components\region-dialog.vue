<template>
  <el-drawer
    title="选择地区"
    :visible.sync="openDialog"
    size="900px"
    :append-to-body="true"
    custom-class="m-drawer m-table-auto"
    @close="close()"
  >
    <div class="drawer-bd">
      <el-row :gutter="0" class="m-query is-border-bottom">
        <el-form :inline="true" label-width="auto">
          <el-col :span="12">
            <el-form-item label="地区">
              <district-selector
                v-model="regionValue"
                :check-strictly="true"
                :removeChild="true"
                placeholder="请选择地区"
                @regionCode="regionCall"
                @clear="clear"
              ></district-selector>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>

      <el-alert type="warning" :closable="false" class="m-alert f-mb10">
        <div class="f-c6">
          包含地区的区县共 <span class="f-fb f-co">{{ accont }}</span> 个
        </div>
      </el-alert>
      <!--表格-->
      <el-table stripe :data="pagRegionTreeList" class="m-table" v-loading="loading">
        <el-table-column type="index" label="No." width="60"></el-table-column>
        <el-table-column label="省份" min-width="150">
          <template v-slot="{ row }">{{ diffRegion(row, '省份') }} </template>
        </el-table-column>
        <el-table-column label="地市" min-width="150">
          <template v-slot="{ row }"> {{ diffRegion(row, '地市') }}</template>
        </el-table-column>
        <el-table-column label="区县" min-width="240">
          <template v-slot="{ row }"> {{ diffRegion(row, '区县') }}</template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <hb-pagination :page="page" v-bind="page"></hb-pagination>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="openDialog = false">取消</el-button>
      <el-button @click="sure" type="primary">确定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import DistrictSelector from '@hbfe/jxjy-admin-platform/src/function/learningRules/components/districtSelector.vue'
  import BasicInfo from '@api/service/management/learning-rule/model/BasicInfo'
  import { UiPage } from '@hbfe/common'
  import RegionTreeItem from '@api/service/management/learning-rule/model/RegionTreeItem'

  @Component({
    components: { DistrictSelector }
  })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.pageData, this.pageData)
    }
    // 基础信息配置
    basicInfo = new BasicInfo()

    // 分页
    page: UiPage

    // 打开抽屉
    openDialog = false

    // 地区code
    regionValue: Array<string> = []

    //地区树列表
    regionTreeList: Array<RegionTreeItem> = []

    // 分页地区树列表
    pagRegionTreeList: Array<RegionTreeItem> = []

    // 切换分页时需要缓存上一次的code
    stagCode = ''

    // 加载
    loading = false
    // 区县总数
    accont = 0

    // 初始化
    async init(row: BasicInfo) {
      this.openDialog = true
      this.loading = true
      // 赋值基础信息
      this.basicInfo = row
      // 初始化地区树
      await this.basicInfo.getRegionTree()
      this.regionTreeList = this.basicInfo.getSelectedRegion()
      this.accont = this.regionTreeList.reduce((pre, cur) => {
        if (cur.leaf) {
          return pre + 1
        } else {
          return pre + cur.children.length
        }
      }, 0)
      this.page.currentChange(1)
      this.loading = false
    }

    // 抽屉销毁时的回调
    close() {
      // 初始化列表
      this.regionValue = []
      this.regionTreeList = []
      this.pagRegionTreeList = []
      this.stagCode = ''
      this.page.currentChange(1)
    }

    // 清空列表
    clear() {
      this.close()
    }

    // 确认方法
    sure() {
      this.openDialog = false
    }

    // 地区回调
    regionCall(value: string) {
      // 切换分页时需要缓存上一次的code
      if (!value) {
        this.pagRegionTreeList = []
      }
      this.stagCode = value
      this.regionTreeList = this.basicInfo.getSelectedRegion(value)
      this.accont = this.regionTreeList.reduce((pre, cur) => {
        if (cur.leaf) {
          return pre + 1
        } else {
          return pre + cur.children.length
        }
      }, 0)
      this.loading = true
      this.page.currentChange(1)
      this.loading = false
    }

    // ui 分页方法
    async pageData() {
      const filterList =
        this.regionTreeList?.slice(
          this.page.pageSize * (this.page.pageNo - 1),
          this.page.pageSize * this.page.pageNo
        ) || []
      this.page.totalSize = this.regionTreeList.length
      this.page.totalPageSize = Math.ceil(this.regionTreeList.length / this.page.pageSize)
      this.pagRegionTreeList = filterList
    }

    // 差异化地区，北京、上海、天津、重庆、澳门、香港
    diffRegion(item: RegionTreeItem, region: string) {
      if (
        item.names[0] == '北京市' ||
        item.names[0] == '上海市' ||
        item.names[0] == '天津市' ||
        item.names[0] == '重庆市' ||
        item.names[0] == '澳门特别行政区' ||
        item.names[0] == '香港特别行政区'
      ) {
        if (region == '省份') {
          return '-'
        } else if (region == '地市') {
          return item.names[0] || '-'
        } else {
          return item.childrenNames.join('、') || '-'
        }
      } else {
        if (region == '省份') {
          return item.names[0] || '-'
        } else if (region == '地市') {
          return item.names[1] || '-'
        } else {
          return item.childrenNames.join('、') || '-'
        }
      }
    }
  }
</script>
