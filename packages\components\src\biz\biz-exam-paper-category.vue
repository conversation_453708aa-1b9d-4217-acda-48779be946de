<route-meta>
{
"title": "试卷分类下拉搜索选择器"
}
</route-meta>
<template>
  <el-cascader
    v-model="categoryIdList"
    :clearable="clearable"
    filterable
    @clear="categoryIdList = undefined"
    :props="props"
    :options="options"
    :placeholder="placeholder"
    @change="selectedChange"
  />
</template>

<script lang="ts">
  import { Prop, Emit, Component, Vue, Watch } from 'vue-property-decorator'
  import { CascaderOptions } from '@hbfe/jxjy-admin-components/src/models/CascaderOptions'
  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    multiple: boolean

    @Prop({
      type: String,
      default: '请选择试卷分类'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      type: Array,
      default: () => [] as string[]
    })
    value: Array<string>

    categoryIdList: Array<string> = new Array<string>()
    options: Array<CascaderOptions> = new Array<CascaderOptions>()
    props = {
      multiple: false
    }

    @Watch('value')
    valueChange() {
      this.categoryIdList = this.value
    }

    @Emit('input')
    selectedChange() {
      return this.categoryIdList
    }

    created() {
      this.setProps()
      this.options = [
        {
          value: 'zhinan',
          label: '指南',
          children: [
            {
              value: 'shejiyuanze',
              label: '设计原则',
              children: [
                {
                  value: 'yizhi',
                  label: '一致'
                },
                {
                  value: 'fankui',
                  label: '反馈'
                },
                {
                  value: 'xiaolv',
                  label: '效率'
                },
                {
                  value: 'kekong',
                  label: '可控'
                }
              ]
            },
            {
              value: 'daohang',
              label: '导航',
              children: [
                {
                  value: 'cexiangdaohang',
                  label: '侧向导航'
                },
                {
                  value: 'dingbudaohang',
                  label: '顶部导航'
                }
              ]
            }
          ]
        },
        {
          value: 'ziyuan',
          label: '资源',
          children: [
            {
              value: 'axure',
              label: 'Axure Components'
            },
            {
              value: 'sketch',
              label: 'Sketch Templates'
            },
            {
              value: 'jiaohu',
              label: '组件交互文档'
            }
          ]
        }
      ]
      this.categoryIdList = this.value
    }
    setProps() {
      this.props.multiple = this.multiple
    }
  }
</script>
