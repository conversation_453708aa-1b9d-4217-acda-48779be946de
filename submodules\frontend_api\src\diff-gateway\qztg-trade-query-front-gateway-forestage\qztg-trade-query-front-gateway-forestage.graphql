"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取专题商品详情
		@param commoditySkuId    商品ID
		@param trainingChannelId 专题ID
	"""
	getCommoditySkuTrainingChannelInServicer(commoditySkuId:String,trainingChannelId:String,isShowAll:Boolean):QZTGCommoditySkuForestageResponse @optionalLogin
	"""门户商品 - 获取用户自主购买渠道商品详情
		@Param portalCommoditySkuId 门户商品ID或原商品id(使用原商品id的话只能获取到原商品信息）
		@Param commoditySkuId 需要获取合并商品的信息传，不需要合并商品信息不用传
		@Param isShowAll 是否展示所有资源
	"""
	getPortalCommoditySkuCustomerPurchaseInServicer(portalCommoditySkuId:String,commoditySkuId:String,isShowAll:Boolean):QZTGPortalCommoditySkuResponse @optionalLogin
}
type CommoditySkuForestageResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.CommoditySkuForestageResponse") {
	commoditySkuId:String
	commodityBasicData:CommodityBasicDataResponse
	skuProperty:CommoditySkuPropertyResponse
	possessionInfo:UserPossessionInfoResponse
	commodityPurchaseChannelConfig:PurchaseChannelConfigResponse
	onShelve:OnShelveResponse
	resource:ResourceResponse
	trainingChannels:[CommodityTrainingChannelResponse]
	tppTypeId:String
	unitId:String
	unitName:String
}
type PortalCommoditySkuForestageResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.PortalCommoditySkuForestageResponse") {
	portalCommoditySkuId:String
	portalSaleTitle:String
	portalCommoditySkuSourceType:Int
	portalCommoditySkuSourceId:String
	skuProperty:PortalCommoditySkuPropertyResponse
	portalCommoditySoldCount:Int!
}
type CommodityBasicDataResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityBasicDataResponse") {
	saleTitle:String
	price:BigDecimal
	commodityPicturePath:String
}
type CommodityPurchaseChannelConfigResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityPurchaseChannelConfigResponse") {
	customerPurchase:PurchaseChannelConfigResponse
	collectivePurchase:PurchaseChannelConfigResponse
	administratorImport:PurchaseChannelConfigResponse
	collectiveSignUpPersonalPay:PurchaseChannelConfigResponse
}
type CommoditySkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommoditySkuPropertyResponse") {
	year:SkuPropertyResponse
	province:SkuPropertyResponse
	city:SkuPropertyResponse
	county:SkuPropertyResponse
	industry:SkuPropertyResponse
	subjectType:SkuPropertyResponse
	trainingCategory:SkuPropertyResponse
	trainingProfessional:SkuPropertyResponse
	technicalGrade:SkuPropertyResponse
	trainingObject:SkuPropertyResponse
	positionCategory:SkuPropertyResponse
	jobLevel:SkuPropertyResponse
	jobCategory:SkuPropertyResponse
	grade:SkuPropertyResponse
	subject:SkuPropertyResponse
	learningPhase:SkuPropertyResponse
	discipline:SkuPropertyResponse
	certificatesType:SkuPropertyResponse
	practitionerCategory:SkuPropertyResponse
	qualificationCategory:SkuPropertyResponse
	trainingForm:SkuPropertyResponse
}
type CommodityTrainingChannelResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.CommodityTrainingChannelResponse") {
	trainingChannelId:String
	trainingChannelName:String
	sort:Int
}
type OnShelveResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.OnShelveResponse") {
	shelveStatus:Int
	lastOnShelveTime:DateTime
	offShelveTime:DateTime
	onShelvePlanTime:DateTime
	offShelvePlanTime:DateTime
	publishTime:DateTime
}
type PortalCommoditySkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.PortalCommoditySkuPropertyResponse") {
	yearForPortal:SkuPropertyResponse
	provinceForPortal:SkuPropertyResponse
	cityForPortal:SkuPropertyResponse
	countyForPortal:SkuPropertyResponse
	industryForPortal:SkuPropertyResponse
	trainingProfessionalForPortal:SkuPropertyResponse
	belongIndustryForPortal:SkuPropertyResponse
}
type PurchaseChannelConfigResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.PurchaseChannelConfigResponse") {
	couldSee:Boolean
	couldBuy:Boolean
}
type SkuPropertyResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.SkuPropertyResponse") {
	skuPropertyValueId:String
	skuPropertyValueName:String
}
type UserPossessionInfoResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.commodity.gateway.graphql.response.nested.UserPossessionInfoResponse") {
	possessing:Boolean
	sourceType:Int
	sourceId:String
	subOrderDeliveryStatus:Int
}
interface ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.ResourceResponse") {
	resourceType:String
}
type SchemeResourceResponse implements ResourceResponse @type(value:"com.fjhb.ms.trade.query.front.gateway.jxjy.kernel.common.request.SchemeResourceResponse") {
	schemeId:String
	schemeName:String
	period:BigDecimal
	schemeType:String
	resourceType:String
}
type CommoditySkuResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.response.CommoditySkuResponse") {
	"""商品id"""
	commoditySkuId:String
	"""商品销售标题"""
	saleTitle:String
	"""商品价格"""
	price:BigDecimal
	"""培训方案id"""
	schemeId:String
	"""商品封面图路径"""
	commodityPicturePath:String
	"""用户商品拥有信息"""
	possessionInfo:UserPossessionInfoResponse
	"""商品属性信息"""
	skuProperty:CommoditySkuPropertyResponse
	"""商品所有渠道的配置信息"""
	commodityPurchaseChannelConfig:CommodityPurchaseChannelConfigResponse
	"""上下架信息"""
	onShelve:OnShelveResponse
}
type QZTGCommoditySkuForestageResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.response.QZTGCommoditySkuForestageResponse") {
	"""当前商品合并了哪些列表"""
	mergedCommodities:[CommoditySkuResponse]
	commoditySkuId:String
	commodityBasicData:CommodityBasicDataResponse
	skuProperty:CommoditySkuPropertyResponse
	possessionInfo:UserPossessionInfoResponse
	commodityPurchaseChannelConfig:PurchaseChannelConfigResponse
	onShelve:OnShelveResponse
	resource:ResourceResponse
	trainingChannels:[CommodityTrainingChannelResponse]
	tppTypeId:String
	unitId:String
	unitName:String
}
type QZTGPortalCommoditySkuResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.qztg.v1.kernel.gateway.response.QZTGPortalCommoditySkuResponse") {
	"""当前商品合并了哪些列表"""
	mergedCommodities:[CommoditySkuResponse]
	originCommodityInfo:CommoditySkuForestageResponse
	portalCommoditySkuForestageResponse:PortalCommoditySkuForestageResponse
}

scalar List
