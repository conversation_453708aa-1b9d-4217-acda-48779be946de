import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import MsMediaResourceLearningV1, {
  CoursewareMediaLearningTokenApplyByCourseRecordTokenRequest,
  CoursewareMediaLearningTokenApplyRequest
} from '@api/ms-gateway/ms-media-resource-learning-v1'
import { ResponseStatus } from '@hbfe/common'

/**
 * 申请课件媒体播放资源的 token
 */
class ApplyLearningCoursewareToken extends AbstractApplyToken {
  /**
   * 课程学习 token
   * @private
   */
  private readonly courseLearningToken: string
  /**
   * 课件 id
   * @private
   */
  private readonly coursewareId: string

  /**
   * 多媒体 id
   * @private
   */
  private readonly multiMediaId: string

  constructor(courseLearningToken: string, coursewareId: string, multiMediaId: string) {
    super()
    this.courseLearningToken = courseLearningToken
    this.coursewareId = coursewareId
    this.multiMediaId = multiMediaId
  }

  async apply(): Promise<ResponseStatus> {
    const courseLearningPlayToken = await MsMediaResourceLearningV1.applyCoursewareLearningTokenByToken(this.toJSON())
    this.token = courseLearningPlayToken.data.token
    if (!courseLearningPlayToken.data.token) {
      return Promise.reject(
        new ResponseStatus(
          parseInt(courseLearningPlayToken.data.applyResult.code),
          courseLearningPlayToken.data.applyResult.message
        )
      )
    }
    return new ResponseStatus(200, '申请成功')
  }

  toJSON(): CoursewareMediaLearningTokenApplyByCourseRecordTokenRequest {
    return {
      courseRecordToken: this.courseLearningToken,
      coursewareId: this.coursewareId,
      multiMediaId: this.multiMediaId
    }
  }
}

export default ApplyLearningCoursewareToken
