/**
 * @description 【集体报名订单】子单状态枚举
 */
import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum BatchOrderSubOrderStatusEnum {
  // 待付款
  Wait_Pay = 1,
  // 支付中
  Paying,
  // 待发货
  Wait_Delivery,
  // 发货中
  Delivering,
  // 已发货
  Complete_Delivery,
  // 已取消
  Canceled
}

/**
 * @description 【集体报名订单】子单状态枚举
 */
class BatchOrderSubOrderStatus extends AbstractEnum<BatchOrderSubOrderStatusEnum> {
  static enum = BatchOrderSubOrderStatusEnum
  constructor(status?: BatchOrderSubOrderStatusEnum) {
    super()
    this.current = status
    this.map.set(BatchOrderSubOrderStatusEnum.Wait_Pay, '待付款')
    this.map.set(BatchOrderSubOrderStatusEnum.Paying, '支付中')
    this.map.set(BatchOrderSubOrderStatusEnum.Wait_Delivery, '待发货')
    this.map.set(BatchOrderSubOrderStatusEnum.Delivering, '发货中')
    this.map.set(BatchOrderSubOrderStatusEnum.Complete_Delivery, '已发货')
    this.map.set(BatchOrderSubOrderStatusEnum.Canceled, '已取消')
  }
}

export default new BatchOrderSubOrderStatus()
