import AnswerPaper from './AnswerPaper'

class AnswerExamPaper extends AnswerPaper {
  platformId: string
  platformVersionId: string
  dataPlatformVersionId: string
  projectId: string
  dataProjectId: string
  subProjectId: string
  unitId: string
  organizationId: string
  // 用户id
  userId: string
  // 考试名称
  name: string
  // 是否公布成绩
  publish: boolean
  // 显示模式 0:全部题目 1:单题
  displayType: number
  // 单题是否可回退
  back: boolean
  // 是否强制交卷
  mandatorySubmission: boolean
  // 考试时长 分钟
  examTimeLength: number
  // 剩余时间 秒
  surplusTime: number
  // 及格分数
  passScore: number
  // 是否开始考试
  started: boolean
  // 是否错过考试
  miss: boolean
  // 是否完成考试
  complete: boolean
  // 考试分数
  score: number
  // 试卷总分
  totalScore: number
  // 是否及格
  passed: boolean
  // 试题项
  questionItemDtos: Array<any>
  examRoundBaseDto: any
}

export default AnswerExamPaper
