import DateScope from '@api/service/common/models/DateScope'
import PlatCourseLearningBackstage, {
  PlanItemAttendanceResponse,
  SignInPointResponse,
  SignInRecordResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage/index'
export default class AttendanceReportItem {
  /**
   * 课程id
   */
  courseId = ''
  /**
   * 课程名称
   */
  courseName = ''
  /**
   * 授课时间
   */
  teachTime = new DateScope()
  /**
   * 要求签到
   */
  isSignIn = false
  /**
   * 要求签退
   */
  isSignOut = false
  /**
   * 签到时间
   */
  signInTime = ''
  /**
   * 签退时间
   */
  signOutTime = ''
  /**
   * 是否 签到成功
   */
  isNotLackSignIn = false
  /**
   * 是否 签退成功
   */
  isNotLackSignOut = false
  /**
   * 是否 签到时间还没结束
   */
  isSignIning = false
  /**
   * 是否 签退时间还没结束
   */
  isSignOuting = false
  static from(dto: PlanItemAttendanceResponse, now?: string) {
    const vo = new AttendanceReportItem()
    const { planItem, signInPoint, signInRecord } = dto
    // 课程名称
    vo.courseName = planItem?.name

    // 课程时间
    vo.teachTime.begin = planItem?.startTime
    vo.teachTime.end = planItem?.endTime

    // 判断签到签退要求
    const signIn = signInPoint?.find((item: SignInPointResponse) => item.signType === 1)
    const signOut = signInPoint?.find((item: SignInPointResponse) => item.signType === 2)
    vo.isSignIn = !!signIn
    vo.isSignOut = !!signOut

    // 签到和签退时间
    vo.signInTime = vo.isSignIn ? this.getSignTime(signInRecord, signInPoint, 1) : '-'
    vo.signOutTime = vo.isSignOut ? this.getSignTime(signInRecord, signInPoint, 2) : '-'

    // 判断签到签退是否成功 或 签到签退时间还没结束
    vo.isNotLackSignIn = !!signInRecord?.find(
      (item: SignInRecordResponse) => item.signType === 1 && item.signResultCode === 200
    )
    vo.isSignIning = now > signIn?.startTime && now < signIn?.endTime
    vo.isNotLackSignOut = !!signInRecord?.find(
      (item: SignInRecordResponse) => item.signType === 2 && item.signResultCode === 200
    )
    vo.isSignOuting = now > signOut?.startTime && now < signOut?.endTime
    return vo
  }
  // 获取签到签退时间的公共方法
  private static getSignTime(
    signInRecord: Array<SignInRecordResponse>,
    signInPoint: Array<SignInPointResponse>,
    signType: number
  ): string {
    const signPoint = signInPoint?.find((item) => item.signType === signType)
    const signRecord = signInRecord?.find(
      (item) => item.signType === signType && item.signResultCode === 200 && item.pointKey === signPoint?.id
    )
    if (signRecord) {
      return signRecord ? signRecord.signTime : signType === 1 ? '未签到' : '未签退'
    }
    return ''
  }
}
