import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'qztg-data-export-gateway-backstage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum CommoditySkuSortField {
  ON_SHELVE_TIME = 'ON_SHELVE_TIME',
  ISSUE_TRAINING_BEGIN_TIME = 'ISSUE_TRAINING_BEGIN_TIME',
  COMMODITY_CREATED_TIME = 'COMMODITY_CREATED_TIME',
  LAST_EDIT_TIME = 'LAST_EDIT_TIME',
  SALE_TOTAL_NUMBER = 'SALE_TOTAL_NUMBER',
  SKU_PROPERTY_YEAR = 'SKU_PROPERTY_YEAR',
  TRAINING_CHANNEL = 'TRAINING_CHANNEL'
}
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum OrderSortField {
  ORDER_NORMAL_TIME = 'ORDER_NORMAL_TIME',
  ORDER_COMPLETED_TIME = 'ORDER_COMPLETED_TIME'
}
export enum OrderSortField1 {
  ORDER_NORMAL_TIME = 'ORDER_NORMAL_TIME',
  ORDER_COMPLETED_TIME = 'ORDER_COMPLETED_TIME'
}
export enum ReturnOrderSortField {
  APPLIED_TIME = 'APPLIED_TIME'
}
export enum SortPolicy1 {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum StudentSortFieldEnum {
  createdTime = 'createdTime'
}

// 类

export class ObsFileMetaData {
  bizType?: string
  owner?: string
  sign?: string
}

export class BigDecimalScopeRequest {
  begin?: number
  end?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class StudentSchemeLearningRequest {
  studentNoList?: Array<string>
  student?: UserRequest
  learningRegister?: LearningRegisterRequest
  scheme?: SchemeRequest1
  studentLearning?: StudentLearningRequest
  dataAnalysis?: DataAnalysisRequest
  connectManageSystem?: ConnectManageSystemRequest
  extendedInfo?: ExtendedInfoRequest
  openPrintTemplate?: boolean
  saleChannels?: Array<number>
  trainingChannelName?: string
  trainingChannelId?: string
  notDistributionPortal?: boolean
  trainingType?: string
}

export class ConnectManageSystemRequest {
  syncStatus?: number
}

export class DataAnalysisRequest {
  trainingResultPeriod?: DoubleScopeRequest
  requirePeriod?: DoubleScopeRequest
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest {
  whetherToPrint?: boolean
  applyCompanyCode?: string
  policyTrainingSchemeId?: string
  policyTrainingSchemeName?: string
}

export class LearningRegisterRequest {
  registerType?: number
  sourceType?: string
  sourceId?: string
  status?: Array<number>
  registerTime?: DateScopeRequest
  saleChannels?: Array<number>
  orderNoList?: Array<string>
  subOrderNoList?: Array<string>
  batchOrderNoList?: Array<string>
  distributorId?: string
  portalId?: string
}

export class RegionRequest {
  province?: string
  city?: string
  county?: string
}

export class StudentLearningRequest {
  trainingResultList?: Array<number>
  trainingResultTime?: DateScopeRequest
  notLearningTypeList?: Array<number>
  courseScheduleStatus?: number
  examAssessResultList?: Array<number>
}

export class RegionSkuPropertyRequest1 {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest1 {
  region?: Array<RegionSkuPropertyRequest1>
  regionSearchType?: number
}

export class SchemeRequest1 {
  schemeId?: string
  schemeIdList?: Array<string>
  schemeType?: string
  schemeName?: string
  skuProperty?: SchemeSkuPropertyRequest
}

export class SchemeSkuPropertyRequest {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest1
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  positionCategory?: Array<string>
  trainingObject?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  subject?: Array<string>
  grade?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  qualificationCategory?: Array<string>
  trainingWay?: Array<string>
  trainingInstitution?: Array<string>
  mainAdditionalItem?: Array<string>
}

export class UserPropertyRequest {
  regionList?: Array<RegionRequest>
  companyName?: string
  payOrderRegionList?: Array<RegionRequest>
}

export class UserRequest {
  userIdList?: Array<string>
  accountIdList?: Array<string>
  userProperty?: UserPropertyRequest
}

export class CommoditySkuRequest1 {
  needQuerySkuPropertyList?: Array<string>
  commoditySkuIdList?: Array<string>
  saleTitleList?: Array<string>
  saleTitleMatchLike?: string
  notShowCommoditySkuIdList?: Array<string>
  price?: number
  onShelveRequest?: OnShelveRequest
  schemeRequest?: SchemeRequest
  skuPropertyRequest?: SkuPropertyRequest
  fixSkuPropertyRequest?: Array<SkuPropertyRequest>
  isDisabledResourceShow?: boolean
  isShowAll?: boolean
  existTrainingChannel?: boolean
  trainingChannelName?: string
  trainingChannelIds?: Array<string>
  tppTypeIds?: Array<string>
  distributorId?: string
  portalId?: string
  externalTrainingPlatform?: Array<string>
  unitIdList?: Array<string>
  payeeIdList?: Array<string>
  extInfoRequest?: CommoditySkuExtInfoRequest
  purchaseChannelList?: Array<PurchaseChannelRequest>
}

export class CommoditySkuSortRequest {
  sortField?: CommoditySkuSortField
  policy?: SortPolicy
}

export class CommoditySkuExtInfoRequest {
  resourceProviderIdList?: Array<string>
}

export class PurchaseChannelRequest {
  purchaseChannelType?: number
  couldSee?: boolean
  couldBuy?: boolean
}

export class OnShelveRequest {
  onShelveStatus?: number
}

export class SchemeRequest {
  schemeIdList?: Array<string>
  excludedSchemeIdList?: Array<string>
  schemeType?: string
  schemeName?: string
  trainingBeginDate?: DateScopeRequest
  trainingEndDate?: DateScopeRequest
  period?: DoubleScopeRequest
}

export class CommodityAuthInfoRequest {
  distributorId?: string
  distributionLevel?: number
  superiorDistributorId?: string
  supplierId?: string
  salesmanId?: string
}

export class CommoditySkuRequest {
  commoditySkuIdList?: Array<string>
  saleTitle?: string
  issueInfo?: IssueInfo
  skuProperty?: SkuPropertyRequest
  externalTrainingPlatform?: Array<string>
  trainingInstitution?: Array<string>
}

export class RegionSkuPropertyRequest {
  province?: string
  city?: string
  county?: string
}

export class RegionSkuPropertySearchRequest {
  regionSearchType?: number
  region?: Array<RegionSkuPropertyRequest>
}

export class SkuPropertyRequest {
  year?: Array<string>
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  industry?: Array<string>
  subjectType?: Array<string>
  trainingCategory?: Array<string>
  trainingProfessional?: Array<string>
  technicalGrade?: Array<string>
  trainingObject?: Array<string>
  positionCategory?: Array<string>
  jobLevel?: Array<string>
  jobCategory?: Array<string>
  grade?: Array<string>
  subject?: Array<string>
  learningPhase?: Array<string>
  discipline?: Array<string>
  trainingChannelIds?: Array<string>
  certificatesType?: Array<string>
  practitionerCategory?: Array<string>
  qualificationCategory?: Array<string>
  trainingForm?: Array<string>
}

export class IssueInfo {
  issueId?: string
  issueName?: string
  issueNum?: string
  trainStartTime?: string
  trainEndTime?: string
  sourceType?: string
  sourceId?: string
}

export class OrderRequest {
  orderNoList?: Array<string>
  subOrderNoList?: Array<string>
  subOrderReturnStatus?: Array<number>
  orderBasicData?: OrderBasicDataRequest
  subOrderBasicData?: SubOrderBasicDataRequest
  payInfo?: OrderPayInfoRequest
  buyerIdList?: Array<string>
  deliveryCommodity?: CommoditySkuRequest
  currentCommodity?: CommoditySkuRequest
  saleChannel?: number
  saleChannels?: Array<number>
  excludeSaleChannels?: Array<number>
  saleChannelIds?: Array<string>
  saleChannelName?: string
  cardTypeId?: string
  distributorId?: string
  portalId?: string
  orderFixQuery?: OrderFixQueryRequest
  isDistributionExcludePortal?: boolean
  externalTrainingPlatform?: Array<string>
  unitIds?: Array<string>
  issueId?: Array<string>
  policyTrainingSchemeIdList?: Array<string>
  declarationUnitCodeList?: Array<string>
  settlementStatus?: number
  settlementDate?: DateScopeRequest
}

export class OrderSortRequest {
  field?: OrderSortField
  policy?: SortPolicy
}

export class OrderBasicDataRequest {
  orderType?: number
  batchOrderNoList?: Array<string>
  orderStatusList?: Array<number>
  orderPaymentStatusList?: Array<number>
  orderDeliveryStatusList?: Array<number>
  orderStatusChangeTime?: OrderStatusChangeTimeRequest
  channelTypesList?: Array<number>
  excludeChannelTypesList?: Array<number>
  terminalCodeList?: Array<string>
  orderAmountScope?: BigDecimalScopeRequest
}

export class OrderFixQueryRequest {
  excludeChannelTypesList?: Array<number>
  excludeSaleChannels?: Array<number>
}

export class OrderPayInfoRequest {
  receiveAccountIdList?: Array<string>
  flowNoList?: Array<string>
  paymentOrderTypeList?: Array<number>
}

export class OrderStatusChangeTimeRequest {
  normalDateScope?: DateScopeRequest
  completedDatesScope?: DateScopeRequest
}

export class SubOrderBasicDataRequest {
  discountType?: number
  discountSourceId?: string
  useDiscount?: boolean
  commodityAuthInfo?: CommodityAuthInfoRequest
}

/**
 * <AUTHOR> linq
@date : 2024-05-24 10:32
@description：商品查询请求入参
 */
export class CommoditySkuRequest12 {
  /**
   * 商品id
   */
  commoditySkuIdList?: Array<string>
  /**
   * 商品名称（模糊查询）
   */
  saleTitleMatchLike?: string
  /**
   * 商品上下架信息
   */
  onShelveRequest?: OnShelveRequest
  /**
   * 商品sku属性查询
   */
  skuPropertyRequest?: SkuPropertyRequest
  /**
   * 是否展示资源不可用的商品
   */
  isDisabledResourceShow?: boolean
  /**
   * 是否展示所有资源
（该字段会屏蔽可见渠道、商品资源是否可用、商品上下架状态三个条件）
   */
  isShowAll?: boolean
  /**
   * 商品购买渠道配置
   */
  purchaseChannelConfig?: Array<PurchaseChannelConfigRequest>
}

/**
 * <AUTHOR> linq
@date : 2024-05-27 16:17
@description：商品购买渠道配置查询参数
 */
export class PurchaseChannelConfigRequest {
  /**
   * 购买渠道类型
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes;
   */
  purchaseChannel?: number
  /**
   * 是否可见
   */
  couldSee?: boolean
  /**
   * 是否可购买
   */
  couldBuy?: boolean
}

/**
 * 发票关联订单查询参数
<AUTHOR>
@date 2022/3/18
 */
export class InvoiceAssociationInfoRequest {
  /**
   * 关联订单类型
0:订单号
1:批次单号
@see AssociationTypes
   */
  associationType?: number
  /**
   * 订单号 | 批次单号
   */
  associationIdList?: Array<string>
  /**
   * 买家信息
   */
  buyerIdList?: Array<string>
  /**
   * 企业id
   */
  unitBuyerUnitIdList?: Array<string>
  /**
   * 收款账号
   */
  receiveAccountIdList?: Array<string>
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
}

/**
 * 线下发票查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceExportRequest {
  /**
   * 发票ID集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基本信息
   */
  basicData?: OfflineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfo?: InvoiceAssociationInfoRequest
  /**
   * 发票配送信息
   */
  invoiceDeliveryInfo?: OfflineInvoiceDeliveryInfoRequest
  jobName?: string
  metaData?: ObsFileMetaData
}

/**
 * 发票查询参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceRequest {
  /**
   * 发票id集合
   */
  invoiceIdList?: Array<string>
  /**
   * 发票基础信息查询参数
   */
  basicData?: OnlineInvoiceBasicDataRequest
  /**
   * 发票关联订单查询参数
   */
  associationInfoList?: Array<InvoiceAssociationInfoRequest>
  /**
   * 蓝票票据查询参数
   */
  blueInvoiceItem?: OnlineInvoiceItemRequest
  /**
   * 红票票据查询参数
   */
  redInvoiceItem?: OnlineInvoiceItemRequest
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  jobName?: string
  metaData?: ObsFileMetaData
}

/**
 * 订单查询参数
<AUTHOR>
@date 2022/01/26
 */
export class OrderRequest1 {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 子订单退货状态
0: 未退货
1: 退货申请中
2: 退货中
3: 退货成功
4: 退款中
5: 退款成功
@see SubOrderReturnStatus
   */
  subOrderReturnStatus?: Array<number>
  /**
   * 订单基本信息查询参数
   */
  orderBasicData?: OrderBasicDataRequest1
  /**
   * 订单支付信息查询
   */
  payInfo?: OrderPayInfoRequest1
  /**
   * 买家查询参数
   */
  buyerIdList?: Array<string>
  /**
   * 发货商品信息
   */
  deliveryCommodity?: CommoditySkuRequest12
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderSortRequest1 {
  /**
   * 需要排序的字段
   */
  field?: OrderSortField1
  /**
   * 正序或倒序
   */
  policy?: SortPolicy1
}

/**
 * 商品查询参数
<AUTHOR>
@date 2022/05/11
 */
export class QZTGCommoditySkuRequest {
  /**
   * 商品Sku名称
   */
  saleTitle?: string
  /**
   * 商品sku属性查询
   */
  skuProperty?: SkuPropertyRequest
  /**
   * 学习方案查询参数
   */
  scheme?: QZTGSchemeRequest
}

/**
 * 方案查询参数
<AUTHOR>
@date 2022/05/11
 */
export class QZTGSchemeRequest {
  /**
   * 培训方案ID
   */
  schemeIdList?: Array<string>
  /**
   * 方案类型
@see SchemeType
   */
  schemeType?: string
  /**
   * 方案学时
   */
  schemePeriodScope?: DoubleScopeRequest
}

/**
 * 商品开通统计报表查询参数
<AUTHOR>
@date 2022/05/11
 */
export class QZTGTradeReportRequest {
  /**
   * 交易时间范围
   */
  tradeTime?: DateScopeRequest
  /**
   * 买家所在地区路径
   */
  buyerAreaPath?: Array<string>
  /**
   * 商品查询条件
   */
  commoditySku?: QZTGCommoditySkuRequest
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel?: number
  /**
   * 销售渠道
0-自营 1-分销 2专题 3-华医网 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 排除的销售渠道
0-自营 1-分销 2-专题 3-华医网
   */
  excludedSaleChannels?: Array<number>
  /**
   * 专题名称
   */
  saleChannelName?: string
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 门户id
   */
  portalId?: string
  /**
   * 查看非推广门户数据 | true 为勾选效果
   */
  notDistributionPortal?: boolean
  /**
   * 收款账户
   */
  receiveAccountIdList?: Array<string>
  /**
   * 期别id
   */
  issueId?: Array<string>
  /**
   * 机构ID集合
   */
  institutionIdList?: Array<string>
}

/**
 * 退货单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderRequest {
  /**
   * 退货单号
   */
  returnOrderNoList?: Array<string>
  /**
   * 基本信息
   */
  basicData?: ReturnOrderBasicDataRequest
  /**
   * 审批信息
   */
  approvalInfo?: ReturnOrderApprovalInfoRequest
  /**
   * 退货商品id集合
   */
  returnCommoditySkuIdList?: Array<string>
  /**
   * 退款商品id集合
   */
  refundCommoditySkuIdList?: Array<string>
  /**
   * 退货单关联子订单查询参数
   */
  subOrderInfo?: SubOrderInfoRequest
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 推广门户id
   */
  portalId?: string
  /**
   * 是否开启分销商下排除推广门户的订单
   */
  isDistributionExcludePortal?: boolean
}

/**
 * 订单排序参数
<AUTHOR>
@date 2022/01/27
 */
export class ReturnSortRequest {
  /**
   * 需要排序的字段
   */
  field?: ReturnOrderSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy1
}

/**
 * 功能描述：学员查询条件
@Author： wtl
@Date： 2022年1月26日 10:10:33
 */
export class StudentQueryRequest {
  /**
   * 账户信息
   */
  account?: AccountRequest
  /**
   * 用户信息
   */
  user?: StudentUserRequest
  /**
   * 用户认证信息
   */
  authentication?: AuthenticationRequest
  /**
   * 排序
   */
  sortList?: Array<StudentSortRequest>
}

export class AccountRequest {
  /**
   * 账户状态 1：正常，2：冻结，3：注销
@see AccountStatus
   */
  statusList?: Array<number>
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequest
  /**
   * 创建人用户id
   */
  createdUserId?: string
  /**
   * 来源类型
0-内置 1-项目主网站 2-安卓 3-IOS 4-后台导入 5-迁移数据 6-分销平台项目主网站 7-专题 8-华医网
   */
  sourceTypes?: Array<number>
}

/**
 * 功能描述：账户认证查询条件
@Author： wtl
@Date： 2022年1月26日 09:30:12
 */
export class AuthenticationRequest {
  /**
   * 帐号
   */
  identity?: string
}

/**
 * 发票开具状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class BillStatusChangeTimeRequest {
  /**
   * 未开具
   */
  unBill?: DateScopeExcelRequest
  /**
   * 开票中
   */
  billing?: DateScopeExcelRequest
  /**
   * 开票成功
   */
  success?: DateScopeExcelRequest
  /**
   * 开票失败
   */
  failure?: DateScopeExcelRequest
}

/**
 * 功能描述：学员集体缴费信息
@Author： wtl
@Date： 2022年4月21日 08:58:49
 */
export class CollectiveRequest {
  /**
   * 集体缴费管理员用户id集合
   */
  collectiveUserIdList?: Array<string>
}

/**
 * 范围查询条件
<AUTHOR>
@version 1.0
@date 2022/5/7 15:34
 */
export class DateScopeExcelRequest {
  /**
   * result >&#x3D; begin
   */
  begin?: string
  /**
   * result <&#x3D; end
   */
  end?: string
}

/**
 * 配送地址信息
<AUTHOR>
@date 2022/05/07
 */
export class DeliveryAddressRequest {
  /**
   * 收件人
   */
  consignee?: string
}

/**
 * 配送状态变更时间查询参数
<AUTHOR>
@date 2022/04/06
 */
export class DeliveryStatusChangeTimeRequest {
  /**
   * 未就绪
   */
  unReady?: DateScopeExcelRequest
  /**
   * 已就绪
   */
  ready?: DateScopeExcelRequest
  /**
   * 已配送
   */
  shipped?: DateScopeExcelRequest
  /**
   * 已自取
   */
  taken?: DateScopeExcelRequest
}

/**
 * 快递信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class ExpressRequest {
  /**
   * 快递单号
   */
  expressNo?: string
}

/**
 * 发票开票状态变更时间记录查询参数
<AUTHOR>
@date 2022/04/06
 */
export class InvoiceBillStatusChangTimeRequest {
  /**
   * 发票申请开票时间
   */
  unBillDateScope?: DateScopeExcelRequest
  /**
   * 发票开票时间
   */
  successDateScope?: DateScopeExcelRequest
}

/**
 * 发票状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class InvoiceStatusChangeTimeRequest {
  /**
   * 正常
   */
  normal?: DateScopeExcelRequest
  /**
   * 作废
   */
  invalid?: DateScopeExcelRequest
}

/**
 * 线下发票基本信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceTypeList?: Array<number>
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategory?: Array<number>
  /**
   * 发票状态
1:正常
2:作废
@see InvoiceStatus
   */
  invoiceStatus?: number
  /**
   * 发票状态变更时间记录
   */
  invoiceStatusChangeTime?: InvoiceStatusChangeTimeRequest
  /**
   * 发票开票状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  billStatusList?: Array<number>
  /**
   * 发票开票状态变更时间记录
   */
  billStatusChangTime?: InvoiceBillStatusChangTimeRequest
  /**
   * 发票是否冻结
   */
  freeze?: boolean
  /**
   * 发票号集合
   */
  invoiceNoList?: Array<string>
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
}

/**
 * 线下发票配送信息
<AUTHOR>
@date 2022/04/06
 */
export class OfflineInvoiceDeliveryInfoRequest {
  /**
   * 配送状态
0:未就绪 1：已就绪 2：已自取 3：已配送
@see OfflineDeliveryStatus
   */
  deliveryStatusList?: Array<number>
  /**
   * 配送状态变更时间记录
0:未就绪 1：已就绪 2：已自取 3：已配送
key值 {@link OfflineDeliveryStatus}
   */
  deliveryStatusChangeTime?: DeliveryStatusChangeTimeRequest
  /**
   * 配送方式
0:无 1：自取 2：快递
@see OfflineShippingMethods
   */
  shippingMethodList?: Array<number>
  /**
   * 快递信息
   */
  express?: ExpressRequest
  /**
   * 自取信息
   */
  takeResult?: TakeResultRequest
  /**
   * 配送地址信息
   */
  deliveryAddress?: DeliveryAddressRequest
}

/**
 * 发票基础信息查询参数
<AUTHOR>
@date 2022/03/23
 */
export class OnlineInvoiceBasicDataRequest {
  /**
   * 发票类型
1:电子发票 2:纸质发票
@see InvoiceTypes
   */
  invoiceType?: number
  /**
   * 发票种类
1:普通发票 2:增值税普通发票 3:增值税专用发票
@see InvoiceCategories
   */
  invoiceCategoryList?: Array<number>
  /**
   * 发票状态变更时间
@see InvoiceStatus
   */
  invoiceStatusChangeTime?: InvoiceStatusChangeTimeRequest
  /**
   * 发票状态
1：正常 2：作废
@see InvoiceStatus
   */
  invoiceStatusList?: Array<number>
  /**
   * 蓝票票据开具状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  blueInvoiceItemBillStatusList?: Array<number>
  /**
   * 红票票据开具状态
0:未开具 1：开票中 2：开票成功 3：开票失败
@see InvoiceBillStatus
   */
  redInvoiceItemBillStatusList?: Array<number>
  /**
   * 发票是否已冲红
   */
  flushed?: boolean
  /**
   * 发票是否已生成红票票据
   */
  redInvoiceItemExist?: boolean
  /**
   * 商品id集合
   */
  commoditySkuIdList?: Array<string>
  /**
   * 发票是否冻结
   */
  freeze?: boolean
}

/**
 * 发票票据
<AUTHOR>
@date 2022/03/18
 */
export class OnlineInvoiceItemRequest {
  /**
   * 票据开具状态变更时间
   */
  billStatusChangeTime?: BillStatusChangeTimeRequest
  /**
   * 发票号码
   */
  billNoList?: Array<string>
}

/**
 * 订单基本信息查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderBasicDataRequest1 {
  /**
   * 订单类型
1:常规订单 2:批次关联订单
@see com.fjhb.domain.trade.api.order.consts.OrderTypes
   */
  orderType?: number
  /**
   * 批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 订单状态
<br> 1:正常 2：交易完成 3：交易关闭
@see OrderStatus
   */
  orderStatusList?: Array<number>
  /**
   * 订单支付状态
<br> 0:未支付 1：支付中 2：已支付
@see com.fjhb.domain.trade.api.order.consts.OrderPaymentStatus
   */
  orderPaymentStatusList?: Array<number>
  /**
   * 订单发货状态
<br> 0:未发货 1：发货中 2：已发货
@see com.fjhb.domain.trade.api.order.consts.OrderDeliveryStatus
   */
  orderDeliveryStatusList?: Array<number>
  /**
   * 订单状态变更时间
   */
  orderStatusChangeTime?: OrderStatusChangeTimeRequest1
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
  /**
   * 订单价格范围
<br> 查询非0元订单 begin填0.01
   */
  orderAmountScope?: BigDecimalScopeExcelRequest
}

/**
 * 退货单关联子订单的主订单查询参数
<AUTHOR>
@date 2022/03/24
 */
export class OrderInfoRequest {
  /**
   * 订单号集合
   */
  orderNoList?: Array<string>
  /**
   * 关联批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 买家id集合
   */
  buyerIdList?: Array<string>
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 原始订单交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 购买渠道
<br> 1:用户自主购买 2:集体缴费 3:管理员导入 4:集体报名个人缴费渠道
@see com.fjhb.domain.trade.api.purchasechannel.consts.PurchaseChannelTypes
   */
  channelTypesList?: Array<number>
  /**
   * 终端
<br> Web:Web端 H5: H5 IOS:IOS端 Android:安卓端 WechatMini:微信小程序 WechatOfficial:微信公众号 ExternalSystemManage:外部管理系统
@see PurchaseChannelTerminalCodes
   */
  terminalCodeList?: Array<string>
  /**
   * 销售渠道
0-自营 1-分销 2专题 不传则查全部
   */
  saleChannels?: Array<number>
  /**
   * 专题id
   */
  saleChannelIds?: Array<string>
  /**
   * 专题名称
   */
  saleChannelName?: string
}

/**
 * 订单支付信息相关查询参数
<AUTHOR>
@date 2022/01/27
 */
export class OrderPayInfoRequest1 {
  /**
   * 收款账号ID
   */
  receiveAccountIdList?: Array<string>
  /**
   * 交易流水号
   */
  flowNoList?: Array<string>
  /**
   * 付款类型
1: 线上付款单
2: 线下付款单
3: 无需付款的付款单
   */
  paymentOrderTypeList?: Array<number>
}

/**
 * 订单状态变更时间查询参数
<AUTHOR>
@date 2022/03/22
 */
export class OrderStatusChangeTimeRequest1 {
  /**
   * 订单处于正常状态时间范围(创建时间范围)
   */
  normalDateScope?: DateScopeExcelRequest
  /**
   * 订单创建时间范围
   */
  completedDatesScope?: DateScopeExcelRequest
}

/**
 * 退货单关闭信息
<AUTHOR>
@date 2022年4月11日 11:33:35
 */
export class ReturnCloseReasonRequest {
  /**
   * 退货单关闭类型（1：买家关闭 2：卖家关闭 3：卖家拒绝 4：批次退货确认失败）
@see ReturnOrderCloseTypes
   */
  closeTypeList?: Array<number>
}

/**
 * 退货单审批信息查询参数
<AUTHOR>
@date 2022/03/18
 */
export class ReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime?: DateScopeExcelRequest
}

/**
 * 退货单基本信息查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderBasicDataRequest {
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   */
  returnOrderStatus?: Array<number>
  /**
   * 退货单类型
1-仅退货
2-仅退款
3-退货并退款
4-部分退货
5-部分退款
6-部分退货并部分退款
7-部分退货并全额退款
8-全部退货并部分退款
   */
  returnOrderTypes?: Array<number>
  /**
   * 退货单申请来源类型
SUB_ORDER
BATCH_RETURN_ORDER
@see ReturnOrderApplySourceTypes
   */
  applySourceType?: string
  /**
   * 来源ID集合
   */
  applySourceIdList?: Array<string>
  /**
   * 退货单关闭信息
   */
  returnCloseReason?: ReturnCloseReasonRequest
  /**
   * 退货单状态变更时间
   */
  returnStatusChangeTime?: ReturnOrderStatusChangeTimeRequest
  /**
   * 退款金额范围
<br> 查询非0元  begin填0.01
   */
  refundAmountScope?: BigDecimalScopeExcelRequest
}

/**
 * 退货单状态变更时间查询参数
<AUTHOR>
@date 2022/03/24
 */
export class ReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied?: DateScopeExcelRequest
  /**
   * 退货单完成时间
<br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted?: DateScopeExcelRequest
}

/**
 * 功能描述 : 学员排序参数
@date : 2022/4/1 17:15
 */
export class StudentSortRequest {
  /**
   * 学员排序字段
   */
  studentSortField?: StudentSortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

export class StudentUserRequest {
  /**
   * 工作单位名称（模糊）
   */
  companyName?: string
  /**
   * 用户所属地区路径集合（模糊，右like）
   */
  regionPathList?: Array<string>
  /**
   * 集体缴费信息
   */
  collective?: CollectiveRequest
  /**
   * 单位所属地区路径集合（模糊，右like）
   */
  companyRegionPathList?: Array<string>
  /**
   * 单位所属地区路径集合匹配方式，默认为rlike(0：完全匹配 1：模糊查询，*regionPathList* 2：左模糊查询，*regionPathList 3:右模糊查询，regionPathList*)
@see MatchTypeConstant
   */
  companyRegionPathListMatchType?: number
  /**
   * 是否工勤人员  (0:非工勤人员  1:工勤人员)
   */
  isWorker?: string
  /**
   * 是否退休   (0:非退休人员 1:退休人员)
   */
  isRetire?: string
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 用户名称
   */
  userName?: string
  /**
   * 用户名称匹配方式，默认为like(0：完全匹配 1：模糊查询，*userName* 2：左模糊查询，*userName 3:右模糊查询，userName*)
@see MatchTypeConstant
   */
  userNameMatchType?: number
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
}

/**
 * <AUTHOR>
@date 2022/03/24
 */
export class SubOrderInfoRequest {
  /**
   * 子订单号集合
   */
  subOrderNoList?: Array<string>
  /**
   * 订单查询参数
   */
  orderInfo?: OrderInfoRequest
}

/**
 * 取件信息查询参数
<AUTHOR>
@date 2022/04/06
 */
export class TakeResultRequest {
  /**
   * 领取人
   */
  takePerson?: string
}

/**
 * @Description 范围查询条件
<AUTHOR>
@Date 8:51 2022/5/23
 */
export class BigDecimalScopeExcelRequest {
  /**
   * result >&#x3D; begin
   */
  begin?: number
  /**
   * result <&#x3D; end
   */
  end?: number
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出中心财务数据表
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCentralFinancialDataInServicer(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    query: DocumentNode = GraphqlImporter.exportCentralFinancialDataInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出组合商品开通统计列表
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCombinationCommodityOpenReportFormsInServicer(
    request: QZTGTradeReportRequest,
    query: DocumentNode = GraphqlImporter.exportCombinationCommodityOpenReportFormsInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 培训方案导出
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCommoditySkuInServicer(
    params: { queryRequest?: CommoditySkuRequest1; sortRequest?: Array<CommoditySkuSortRequest> },
    query: DocumentNode = GraphqlImporter.exportCommoditySkuInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 线下发票导出-继续教育
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOfflineInvoiceInServicerForJxjy(
    request: OfflineInvoiceExportRequest,
    query: DocumentNode = GraphqlImporter.exportOfflineInvoiceInServicerForJxjy,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 线上发票导出-继续教育
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOnlineInvoiceInServicerForJxjy(
    request: OnlineInvoiceRequest,
    query: DocumentNode = GraphqlImporter.exportOnlineInvoiceInServicerForJxjy,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人订单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrderExcelInDistributor(
    params: { request?: OrderRequest1; sort?: Array<OrderSortRequest1> },
    query: DocumentNode = GraphqlImporter.exportOrderExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人订单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOrderExcelInServicer(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    query: DocumentNode = GraphqlImporter.exportOrderExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliationExcelInDistributor(
    params: { request?: OrderRequest1; sort?: Array<OrderSortRequest1> },
    query: DocumentNode = GraphqlImporter.exportReconciliationExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReconciliationExcelInServicer(
    params: { request?: OrderRequest; sort?: Array<OrderSortRequest> },
    query: DocumentNode = GraphqlImporter.exportReconciliationExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnOrderExcelInDistributor(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    query: DocumentNode = GraphqlImporter.exportReturnOrderExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人退货单
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnOrderExcelInServicer(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    query: DocumentNode = GraphqlImporter.exportReturnOrderExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人报名退货对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnReconciliationExcelInDistributor(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    query: DocumentNode = GraphqlImporter.exportReturnReconciliationExcelInDistributor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 导出个人报名退货对账
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportReturnReconciliationExcelInServicer(
    params: { request?: ReturnOrderRequest; sort?: Array<ReturnSortRequest> },
    query: DocumentNode = GraphqlImporter.exportReturnReconciliationExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 学员导出
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentExcelInServicer(
    request: StudentQueryRequest,
    query: DocumentNode = GraphqlImporter.exportStudentExcelInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 泉州提高学习明细差异化导出
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportStudentLearningDetailInServicer(
    request: StudentSchemeLearningRequest,
    query: DocumentNode = GraphqlImporter.exportStudentLearningDetailInServicer,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
