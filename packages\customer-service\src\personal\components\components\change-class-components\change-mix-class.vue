<template>
  <div>
    <template
      v-if="$hasPermission('queryMixExchangeClass')"
      desc="查看面网授换班信息"
      actions="searchMixWaitExchangeBase,searchMixExchangedRecordsBase"
    >
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="f-flex f-align-center">
          <span class="tit-txt">待更换班级/期别</span>
          <span class="f-cr f-ml5">
            （换班/换期的前提条件为：培训班同等价格！换期仅支持选择本培训班内的期别）此功能操作仅支持【培训班】类型的培训方案。
          </span>
        </div>
        <div class="f-p20">
          <hb-search-wrapper @reset="resetWaitExchangeQueryParams" class="m-query">
            <el-form-item label="年度：">
              <biz-year-select v-model="waitExchangeQueryParams.year" placeholder="请选择年度"></biz-year-select>
            </el-form-item>
            <el-form-item label="班级名称：">
              <el-input
                v-model="waitExchangeQueryParams.schemeName"
                clearable
                placeholder="请输入班级名称"
                @clear="waitExchangeQueryParams.schemeName = ''"
              />
            </el-form-item>
            <el-form-item label="期别名称：">
              <el-input
                v-model="waitExchangeQueryParams.issueName"
                clearable
                placeholder="请输入期别名称"
                @clear="waitExchangeQueryParams.issueName = ''"
              />
            </el-form-item>
            <template slot="actions">
              <el-button type="primary" @click="searchMixWaitExchangeBase">查询</el-button>
            </template>
          </hb-search-wrapper>
          <!--表格-->
          <el-table
            stripe
            :data="waitExchangeList"
            max-height="500px"
            class="m-table"
            v-loading="waitExchangeQuery.loading"
          >
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="培训班信息" min-width="300">
              <template slot-scope="scope">
                <p>
                  <el-tag type="primary" effect="dark" size="mini">{{ waitExchangeSchemeType(scope.row) }}</el-tag
                  >{{ scope.row.schemeName }}
                </p>
                <p class="f-f13 f-c9">
                  {{ getPrototype(scope.row) }}
                  <!-- 【{{ scope.row.industry }} / {{ scope.row.year }}年 / {{ scope.row.subjectType }}】 -->
                  <!-- <el-tag type="warning" size="mini" v-if="scope.row.distributorId">分销推广</el-tag> -->
                  <!-- {{ scope.row.professionalName }} -->
                </p>
              </template>
            </el-table-column>
            <el-table-column label="期别信息" min-width="140">
              <template slot-scope="scope">
                <span> {{ scope.row.periodName }}/{{ scope.row.periodNo }}</span>
              </template>
            </el-table-column>
            <el-table-column label="单价(元)" min-width="140" align="right">
              <template slot-scope="scope">
                {{ scope.row.price }}
              </template>
            </el-table-column>
            <el-table-column label="考核情况" min-width="140" align="center">
              <template slot-scope="scope">
                <div v-if="assessmentStatusStyle(scope.row)">
                  <el-tag :type="assessmentStatusStyle(scope.row)">{{ assessmentStatus(scope.row) }}</el-tag>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" min-width="100">
              <template slot-scope="scope">
                <div v-if="statusStyle(scope.row)">
                  <el-badge is-dot :type="statusStyle(scope.row)" class="badge-status">
                    {{ status(scope.row) }}
                  </el-badge>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="来源类型" min-width="120" align="center">
              <template slot-scope="scope">{{ resourceType(scope.row) }}</template>
            </el-table-column>
            <el-table-column label="操作" min-width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <template
                  v-if="$hasPermission('exchangeMixClass')"
                  desc="面网授班换班"
                  actions="exchangeMixClass,@changeMixClassDrawer"
                >
                  <el-button
                    type="text"
                    size="mini"
                    @click="exchangeMixClass(scope.row)"
                    :disabled="!enableExchangeTrainClass(scope.row)"
                  >
                    换班
                  </el-button>
                </template>
                <template
                  v-if="$hasPermission('exchangeMixPeriod')"
                  desc="面网授班换期"
                  actions="exchangePeriod,@changePeriodDrawer"
                >
                  <el-button
                    type="text"
                    size="mini"
                    @click="exchangePeriod(scope.row)"
                    :disabled="!enableExchangeTrainPeriod(scope.row)"
                  >
                    换期
                  </el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="waitExchangePage" v-bind="waitExchangePage"> </hb-pagination>
        </div>
      </el-card>
      <!-- 下半部分下半部分下半部分下半部分下半部分下半部分下半部分下半部分下半部分下半部分下半部分下半部分下半部分下半部分下半部分下半部分 -->
      <el-tabs v-model="changeRecord" type="card" class="m-tab-card is-sticky" @tab-click="handleClick">
        <el-tab-pane name="shiftChange">
          <div slot="label">更换班级记录</div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-p20">
              <hb-search-wrapper @reset="resetMixExchangedRecordsQueryParams" class="m-query">
                <el-form-item label="年度：">
                  <biz-year-select
                    v-model="exchangedRecordsQueryParams.year"
                    placeholder="请选择年度"
                  ></biz-year-select>
                </el-form-item>
                <el-form-item label="班级名称：">
                  <el-input
                    v-model="exchangedRecordsQueryParams.schemeName"
                    clearable
                    placeholder="请输入班级名称"
                    @clear="exchangedRecordsQueryParams.schemeName = ''"
                  />
                </el-form-item>
                <el-form-item label="期别名称：">
                  <el-input
                    v-model="exchangedRecordsQueryParams.issueName"
                    clearable
                    placeholder="请输入期别名称"
                    @clear="exchangedRecordsQueryParams.issueName = ''"
                  />
                </el-form-item>
                <el-form-item label="换班状态：">
                  <el-select clearable v-model="exchangedRecordsQueryParams.exchangeTrainClassStatus">
                    <el-option label="全部" :value="1"></el-option>
                    <el-option label="换班中" :value="2"></el-option>
                    <el-option label="换班成功" :value="3"></el-option>
                  </el-select>
                </el-form-item>
                <template slot="actions">
                  <el-button type="primary" @click="searchMixExchangedRecordsBase">查询</el-button>
                </template>
              </hb-search-wrapper>
              <!--表格-->
              <el-table
                stripe
                :data="exchangedRecordsList"
                max-height="500px"
                class="m-table"
                v-loading="exchangedRecordsQuery.loading"
                ref="exchangedRecordsUITableRef"
              >
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="初始班级" width="300">
                  <template slot-scope="scope">
                    <p class="f-to-two" :title="scope.row.originalCommodity.saleTitle">
                      <el-tag type="primary" effect="dark" size="mini">
                        {{ exchangeRecordCommoditySchemeType(scope.row.originalCommodity) }}
                      </el-tag>
                      {{ scope.row.originalCommodity.saleTitle }}
                    </p>
                    <p class="f-f13 f-c9 f-ml5">
                      {{ getChangeClassPrototype(scope.row.originalCommodity.skuValueNameProperty) }}
                    </p>
                    <p>
                      {{ scope.row.originalCommodity.issueInfo.issueName }}/{{
                        scope.row.originalCommodity.issueInfo.issueNum
                      }}
                    </p>
                  </template>
                </el-table-column>
                <el-table-column label="新班级" min-width="300">
                  <template slot-scope="scope">
                    <p class="f-to-two" :title="scope.row.exchangeCommodity.saleTitle">
                      <el-tag type="primary" effect="dark" size="mini">
                        {{ exchangeRecordCommoditySchemeType(scope.row.exchangeCommodity) }}
                      </el-tag>
                      {{ scope.row.exchangeCommodity.saleTitle }}
                    </p>
                    <p class="f-f13 f-c9 f-ml5">
                      {{ getChangeClassPrototype(scope.row.exchangeCommodity.skuValueNameProperty) }}
                    </p>
                    <p>
                      {{ scope.row.exchangeCommodity.issueInfo.issueName }}/{{
                        scope.row.exchangeCommodity.issueInfo.issueNum
                      }}
                    </p>
                  </template>
                </el-table-column>
                <el-table-column label="更换状态" min-width="150" align="center">
                  <template slot-scope="scope">
                    <div>
                      <el-tag
                        :type="exchangeTrainClassStatusStyle(scope.row)"
                        v-if="exchangeTrainClassStatusStyle(scope.row)"
                      >
                        {{ exchangeTrainClassStatus(scope.row) }}
                      </el-tag>
                      <p class="f-mt5">
                        <a
                          class="f-link f-underline f-cb f-f12"
                          v-if="$hasPermission('viewExchangeMixDetail')"
                          desc="面网授班换班换期记录详情查看"
                          actions="viewExchangeMixDetail"
                          @click="viewExchangeMixDetail(scope.row)"
                          >查看详情</a
                        >
                        <!--                        <el-popover-->
                        <!--                          placement="bottom"-->
                        <!--                          width="160"-->
                        <!--                          trigger="hover"-->
                        <!--                          v-if="exchangeTrainClassStatus(scope.row) === '换班中'"-->
                        <!--                        >-->
                        <!--                          <span slot="content">{{ scope.row.failMsg }}</span>-->
                        <!--                          <a slot="reference" class="f-link f-underline f-ci f-f12 f-ml20">失败原因</a>-->
                        <!--                        </el-popover>-->
                      </p>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="更换时间" min-width="180">
                  <template slot-scope="scope">{{ scope.row.exchangeTrainClassTime }}</template>
                </el-table-column>
                <el-table-column label="操作帐号" min-width="120">
                  <template slot-scope="scope">{{ scope.row.operatorName }}</template>
                </el-table-column>
                <el-table-column label="操作" min-width="100" align="center">
                  <template slot-scope="scope">
                    <template
                      v-if="$hasPermission('continueExchangeMixTrainClass')"
                      desc="面网授班继续换班"
                      actions="continueExchangeMixTrainClass"
                    >
                      <el-popconfirm
                        v-if="scope.row.enableExchangeTrainClass"
                        confirm-button-text="确认"
                        title="是否确认继续换班？"
                        @confirm="continueExchangeMixTrainClass(scope.row)"
                      >
                        <el-button type="text" size="mini" slot="reference">继续换班</el-button>
                      </el-popconfirm>
                    </template>
                    <span v-if="!scope.row.enableExchangeTrainClass">—</span>
                  </template>
                </el-table-column>
              </el-table>
              <hb-pagination :page="exchangedRecordsPage" v-bind="exchangedRecordsPage"> </hb-pagination>
            </div>
          </el-card>
        </el-tab-pane>
        <el-tab-pane name="termChange">
          <div slot="label">更换期别记录</div>
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20 f-pt40">
              <hb-search-wrapper @reset="resetExchangePeriodRecordQuery" class="m-query">
                <el-form-item label="年度：">
                  <biz-year-select v-model="exchangedPeriodQueryParams.year" placeholder="请选择年度"></biz-year-select>
                </el-form-item>
                <el-form-item label="班级名称：">
                  <el-input
                    v-model="exchangedPeriodQueryParams.schemeName"
                    clearable
                    placeholder="请输入班级名称"
                    @clear="exchangedPeriodQueryParams.schemeName = ''"
                  />
                </el-form-item>
                <el-form-item label="期别名称：">
                  <el-input
                    v-model="exchangedPeriodQueryParams.issueName"
                    clearable
                    placeholder="请输入期别名称"
                    @clear="exchangedPeriodQueryParams.issueName = ''"
                  />
                </el-form-item>
                <template slot="actions">
                  <el-button type="primary" @click="exchangedPeriodPage.currentChange(1)">查询</el-button>
                </template>
              </hb-search-wrapper>
              <!--表格-->
              <el-table
                stripe
                :data="exchangedPeriodList"
                max-height="500px"
                class="m-table"
                ref="exchangedPeriodListRef"
                v-loading="exchangedPeriodListLoading"
              >
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="初始期别" min-width="300">
                  <template #default="scope">
                    <!--                    <el-popover placement="top-start" width="400" trigger="hover">-->
                    <!--                      <div slot="reference">-->
                    <p>
                      <el-tag type="info">培训期别</el-tag>
                      {{
                        scope.row.originalCommodity.issueInfo ? scope.row.originalCommodity.issueInfo.issueName : ''
                      }}/{{
                        scope.row.originalCommodity.issueInfo ? scope.row.originalCommodity.issueInfo.issueNum : ''
                      }}
                    </p>
                    <div>
                      <el-tag type="primary" effect="dark" size="mini">{{
                        exchangeRecordCommoditySchemeType(scope.row.originalCommodity)
                      }}</el-tag>
                      {{ scope.row.originalCommodity.saleTitle }}
                    </div>
                    <p>
                      {{ getChangeClassPrototype(scope.row.originalCommodity.skuValueNameProperty) }}
                    </p>
                    <!--                      </div>-->
                    <!--                      <p>-->
                    <!--                        【{{ exchangeRecordCommoditySchemeType(scope.row.originalCommodity) }}】{{-->
                    <!--                          scope.row.originalCommodity.saleTitle-->
                    <!--                        }}-->
                    <!--                      </p>-->
                    <!--                    </el-popover>-->
                  </template>
                </el-table-column>
                <el-table-column label="新期别" min-width="300">
                  <template slot-scope="scope">
                    <p>
                      <el-tag type="info">培训期别</el-tag
                      >{{
                        scope.row.exchangeCommodity.issueInfo ? scope.row.exchangeCommodity.issueInfo.issueName : ''
                      }}/{{
                        scope.row.exchangeCommodity.issueInfo ? scope.row.exchangeCommodity.issueInfo.issueNum : ''
                      }}
                    </p>
                    <div class="f-to-two">
                      <el-tag type="primary" effect="dark" size="mini">
                        {{ exchangeRecordCommoditySchemeType(scope.row.exchangeCommodity) }}
                      </el-tag>
                      {{ scope.row.exchangeCommodity.saleTitle }}
                    </div>
                    <p>{{ getChangeClassPrototype(scope.row.exchangeCommodity.skuValueNameProperty) }}</p>
                  </template>
                </el-table-column>
                <el-table-column label="更换状态" min-width="150" align="center">
                  <template #default="scope">
                    <div class="f-cg">换期成功</div>
                    <el-tag v-if="scope.row.changeType === ChangeIssueType.changeClass">换班换期</el-tag>
                    <el-tag type="warning" v-else>班内换期</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="更换时间" min-width="180">
                  <template #default="scope">{{ scope.row.changeTime }}</template>
                </el-table-column>
                <el-table-column label="操作账号" min-width="120">
                  <template #default="scope">{{ scope.row.operatorName }}</template>
                </el-table-column>
                <!-- <el-table-column label="操作" min-width="100" align="center" fixed="right">
                  <template slot-scope="scope">
                    <template
                      v-if="$hasPermission('continueExchangePeriod')"
                      desc="面网授班继续换期"
                      actions="continueExchangePeriod"
                    >
                      <el-popconfirm
                        v-if="scope.row.enableExchangeTrainClass"
                        confirm-button-text="确认"
                        title="是否确认继续换期？"
                        @confirm="continueExchangePeriod(scope.row)"
                      >
                        <el-button type="text" size="mini" slot="reference">继续换期</el-button>
                      </el-popconfirm>
                    </template>
                  </template>
                </el-table-column> -->
              </el-table>
              <!--分页-->
              <hb-pagination :page="exchangedPeriodPage" v-bind="exchangedPeriodPage"> </hb-pagination>
            </div>
          </el-card>
        </el-tab-pane>
      </el-tabs>
      <!--查看换班详情-->
      <el-drawer
        title="查看详情"
        :visible.sync="uiConfig.dialog.viewExchangeTrainClassDetailVisible"
        size="600px"
        custom-class="m-drawer"
      >
        <div class="drawer-bd f-mt20 f-mlr40">
          <el-timeline>
            <el-timeline-item v-for="(item, index) in selectedExchangeOrderStatusList" :key="index">
              <p class="f-mb10 f-fb f-f15">
                {{ item.date }} <span class="f-ml30">{{ exchangeOrderStatus(item.status) }}</span>
              </p>
            </el-timeline-item>
          </el-timeline>
        </div>
      </el-drawer>
      <ChangeMixClassDrawer
        ref="exchangeMixTrainClassRef"
        :visible.sync="uiConfig.dialog.replaceTrainClassVisible"
        :userId="userId"
        @reloadData="handleReloadData"
      ></ChangeMixClassDrawer>
      <change-period-drawer ref="changePeriodDrawerRef" :userId="userId"></change-period-drawer>
      <!-- <exchange-train-class-distributor
        ref="exchangeDistributorTrainClassRef"
        :visible.sync="uiConfig.dialog.replaceFxTrainClassVisible"
        @confirmExchangeTrainClass="confirmExchangeTrainClass"
      /> -->
      <ChangeMixClassDrawerFx
        ref="changeMixClassDrawerFxRef"
        :visible.sync="uiConfig.dialog.replaceFxTrainClassVisible"
        @confirmExchangeTrainClass="confirmExchangeTrainClass"
      ></ChangeMixClassDrawerFx>
    </template>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import TrainClassResourceType, {
    TrainClassResourceEnum
  } from '@api/service/management/train-class/query/enum/TrainClassResourceType'
  import QueryWaitExchangeTrainClassListVo from '@api/service/management/train-class/query/vo/QueryWaitExchangeTrainClassListVo'
  import QueryExchangeOrderRecordListVo from '@api/service/management/train-class/query/vo/QueryExchangeOrderRecordListVo'
  import WaitExchangeTrainClassDetailVo from '@api/service/management/train-class/query/vo/WaitExchangeTrainClassDetailVo'
  import ExchangeOrderRecordDetailVo from '@api/service/management/train-class/query/vo/ExchangeOrderRecordDetailVo'
  import BizYearSelect from '@hbfe/jxjy-admin-components/src/biz/biz-year-select.vue'
  import TrainClassSchemeType, {
    TrainClassSchemeEnum
  } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
  import TrainClassAssessmentStatusType, {
    TrainClassAssessmentStatusEnum
  } from '@api/service/management/train-class/query/enum/TrainClassAssessmentStatusType'
  import TrainClassStatusType, {
    TrainClassStatusEnum
  } from '@api/service/management/train-class/query/enum/TrainClassStatusType'
  import ExchangeTrainClassStatusType, {
    ExchangeTrainClassStatusEnum
  } from '@api/service/management/train-class/query/enum/ExchangeTrainClassStatusType'
  import QueryExchangeTrainClass from '@api/service/management/train-class/query/QueryExchangeTrainClass'
  import TrainClassManagerModule from '@api/service/management/train-class/TrainClassManagerModule'
  import { MutationCreateExchangeOrder } from '@api/service/management/trade/single/order/mutation/MutationCreateExchangeOrder'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import ExchangeOrderStatus from '@api/service/management/train-class/query/vo/ExchangeOrderStatus'
  import ExchangeOrderStatusType, {
    ExchangeOrderStatusEnum
  } from '@api/service/management/train-class/query/enum/ExchangeOrderStatusType'
  import ExchangePeriodStatusType, {
    ExchangePeriodStatusEnum
  } from '@api/service/management/train-class/query/enum/ExchangePeriodStatusType'
  // import ExchangeTrainClass from '@/unit-share/network-school/training/customer-service/personal/__components__/components/exchange-train-class.vue'
  import { bind, debounce } from 'lodash-decorators'
  import { cloneDeep } from 'lodash'
  import OrderCommodityVo from '@api/service/management/train-class/query/vo/OrderCommodityVo'
  import CreateExchangeOrderModel from '@hbfe/jxjy-admin-customerService/src/personal/components/model/CreateExchangeOrderModel'
  import QueryOrderDetail from '@api/service/management/trade/single/order/query/QueryOrderDetail'
  import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
  import TrainingMode, { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  //由于换班弹窗需要查询退款中班级信息
  import QueryRefundList from '@api/service/management/trade/single/order/query/QueryRefundList'
  import { ReturnOrderRequestVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
  // import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
  import SchemeType from '@api/service/common/enums/train-class/SchemeTypeEnums'
  import MutationExchangeOrder from '@api/service/management/trade/single/order/mutation/MutationExchangeOrder'
  // import ExchangeTrainClassDistributor from '@hbfe/fx-manage/src/components/pricing-scheme-dialog/exchange-train-class.vue'
  // 换班的抽屉
  import ChangeMixClassDrawer from '@hbfe/jxjy-admin-customerService/src/personal/components/components/change-class-components/change-mixclass-drawer.vue'
  // 换期的抽屉
  import changePeriodDrawer from '@hbfe/jxjy-admin-customerService/src/personal/components/components/change-class-components/change-period-drawer.vue'
  import QueryIssueChangeRecordParam from '@api/service/management/train-class/offlinePart/model/QueryIssueChangeRecordParam'
  import IssueChangeRecordItem from '@api/service/management/train-class/offlinePart/model/IssueChangeRecordItem'
  import { ChangeIssueType } from '@api/service/management/train-class/query/enum/ChangeIssueType'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import { ElTable } from 'element-ui/types/table'
  // 换班fx抽屉
  import ChangeMixClassDrawerFx from '@hbfe/fx-manage/src/components/pricing-scheme-dialog/exchange-mixorface-class.vue'

  @Component({
    components: {
      // ExchangeTrainClass,
      BizYearSelect,
      // ExchangeTrainClassDistributor,
      ChangeMixClassDrawer,
      changePeriodDrawer,
      ChangeMixClassDrawerFx
    }
  })
  export default class extends Vue {
    // 学员id 由主文件ref传入
    // userId = ''
    @Prop({
      type: String,
      default: ''
    })
    userId = ''
    @Watch('userId', {
      immediate: true,
      deep: true
    })
    async userIdChange(val: string) {
      console.log('changeClass_userId-onandoffline', val)
      await this.resetWaitExchangeQueryParams()
      await this.resetMixExchangedRecordsQueryParams()
      await this.resetExchangePeriodRecordQuery()
    }

    /**
     * 【组件】更换班级
     */
    @Ref('exchangeMixTrainClassRef') exchangeMixTrainClassRef: ChangeMixClassDrawer
    /**
     * 【组件】更换期别
     */
    @Ref('changePeriodDrawerRef') changePeriodDrawerRef: changePeriodDrawer
    @Ref('changeMixClassDrawerFxRef') changeMixClassDrawerFxRef: ChangeMixClassDrawerFx
    /**
     * 【组件】换班/换期记录表格
     */
    @Ref('exchangedRecordsUITableRef') exchangedRecordsUITableRef: ElTable
    @Ref('exchangedPeriodListRef') exchangedPeriodListRef: ElTable

    /**
     * ui控制组
     */
    uiConfig = {
      dialog: {
        // 查看换班详情
        viewExchangeTrainClassDetailVisible: false,
        // 更换培训班
        replaceTrainClassVisible: false,
        // 更换培训班
        replaceFxTrainClassVisible: false
      }
    }
    // 订单详情查询实例对象
    orderDetailObj: QueryOrderDetail = TradeModule.singleTradeBatchFactor.orderFactor.getQueryOrderDetail()
    // 分页 - 待更换班级/期别列表
    waitExchangePage: UiPage
    // 查询 - 待更换班级/期别列表
    waitExchangeQuery: Query = new Query()
    // 查询参数 - 待更换班级/期别列表
    waitExchangeQueryParams: QueryWaitExchangeTrainClassListVo = new QueryWaitExchangeTrainClassListVo()
    // 列表 - 待更换班级/期别列表
    waitExchangeList: WaitExchangeTrainClassDetailVo[] = []

    //退款查询接口请求
    queryRefundOrder: QueryRefundList = TradeModule.singleTradeBatchFactor.orderFactor.getQueryRefundOrder()
    // 分页 - 换班/换期记录
    exchangedRecordsPage: UiPage

    //分页 - 退款班级记录 （筛选可更换班级）
    returnOrderPage: UiPage
    // 查询 - 换班/换期记录
    exchangedRecordsQuery: Query = new Query()
    // 查询参数 - 换班/换期记录
    exchangedRecordsQueryParams: QueryExchangeOrderRecordListVo = new QueryExchangeOrderRecordListVo()
    // 列表 - 换班/换期记录
    exchangedRecordsList: ExchangeOrderRecordDetailVo[] = []

    // 培训方案类型列表
    schemeTypeList: EnumOption<TrainClassSchemeEnum>[] = TrainClassSchemeType.list()
    // 培训类型列表
    trainingModeList: EnumOption<TrainingModeEnum>[] = TrainingMode.list()
    // 考核情况列表 - 待更换班级/期别列表
    assessmentStatusList: EnumOption<TrainClassAssessmentStatusEnum>[] = TrainClassAssessmentStatusType.list()
    // 考核情况列表 - 待更换班级/期别列表
    assessmentStatusMap: Map<TrainClassAssessmentStatusEnum, string> = new Map<TrainClassAssessmentStatusEnum, string>()
      .set(TrainClassAssessmentStatusEnum.Qualified, 'success')
      .set(TrainClassAssessmentStatusEnum.Unqualified, 'danger')
      .set(TrainClassAssessmentStatusEnum.For_Inspection, 'warning')

    // 状态列表 - 待更换班级/期别列表
    statusList: EnumOption<TrainClassStatusEnum>[] = TrainClassStatusType.list()
    // 状态样式列表 - 待更换班级/期别列表
    statusMap: Map<TrainClassStatusEnum, string> = new Map<TrainClassStatusEnum, string>()
      .set(TrainClassStatusEnum.Effective, 'success')
      .set(TrainClassStatusEnum.Frozen, 'primary')
      .set(TrainClassStatusEnum.Failure, 'info')

    // 来源类型列表 - 待更换班级/期别列表
    resourceTypeList: EnumOption<TrainClassResourceEnum>[] = TrainClassResourceType.list()

    // 换班状态 - 换班/换期记录
    exchangeTrainClassStatusList: EnumOption<ExchangeTrainClassStatusEnum>[] = ExchangeTrainClassStatusType.list()
    // 换班状态样式列表 - 换班/换期记录
    exchangeTrainClassStatusStyleMap: Map<ExchangeTrainClassStatusEnum, string> = new Map<
      ExchangeTrainClassStatusEnum,
      string
    >()
      .set(ExchangeTrainClassStatusEnum.Exchanging, 'warning')
      .set(ExchangeTrainClassStatusEnum.Complete_Exchange, 'success')

    // 换货单状态 - 换班/换期记录
    exchangeOrderStatusList: EnumOption<ExchangeOrderStatusEnum>[] = ExchangeOrderStatusType.list()
    // 换货单状态 - 换期记录
    exchangePeriodStatusList: EnumOption<ExchangePeriodStatusEnum>[] = ExchangePeriodStatusType.list()
    // 查询接口入口
    queryRequestEntrance: QueryExchangeTrainClass =
      TrainClassManagerModule.queryTrainClassFactory.getQueryExchangeTrainClass()

    // 业务接口入口（发起换货）
    mutationRequestEntrance: MutationCreateExchangeOrder =
      TradeModule.singleTradeBatchFactor.mutationFactory.getMutationCreateExchangeOrder()

    // 换班详情列表（选中后）
    selectedExchangeOrderStatusList: ExchangeOrderStatus[] = []

    // 创建换货单
    createExchangeOrder: CreateExchangeOrderModel = new CreateExchangeOrderModel()

    // 剔除培训班商品id集合
    excludeCommoditySkuIdList: string[] = []
    /**
     * 剔除的期别id集合
     */
    excludedIssueIdList = new Array<string>()
    /**
     * 更换记录tab激活项
     */
    changeRecord = 'shiftChange'

    industryId = ''
    // 更换期别记录字段模块start-------------------------
    /**
     * 更换期别记录查询参数
     */
    exchangedPeriodQueryParams = new QueryIssueChangeRecordParam()
    /**
     * 更换期别记录列表
     */
    exchangedPeriodList = new Array<IssueChangeRecordItem>()
    /**
     * 表格loading
     */
    exchangedPeriodListLoading = false
    /**
     * 更换期别记录列表分页
     */
    exchangedPeriodPage = new UiPage()
    ChangeIssueType = ChangeIssueType
    // 更换期别记录模块end-------------------------
    // @Watch('waitExchangeQueryParams', {
    //   deep: true
    // })
    // waitExchangeQueryParamsChange(val: any) {
    //   // console.log('waitExchangeQueryParams', val)
    // }

    @Watch('exchangedRecordsQueryParams', {
      deep: true
    })
    exchangedRecordsQueryParamsChange(val: any) {
      // console.log('exchangedRecordsQueryParams', val)
    }

    constructor() {
      super()
      this.waitExchangePage = new UiPage(this.pageMixWaitExchangeList, this.pageMixWaitExchangeList)
      this.exchangedRecordsPage = new UiPage(this.pageMixExchangedRecordsList, this.pageMixExchangedRecordsList)
      this.returnOrderPage = new UiPage(this.getRefundOrderList, this.getRefundOrderList)
      this.exchangedPeriodPage = new UiPage(this.getExchangePeriodRecordList, this.getExchangePeriodRecordList)
    }

    /**
     * 页面初始化
     */
    async created() {
      console.log(TrainClassResourceEnum, 'TrainClassResourceEnum')
      console.log(this.resourceTypeList, 'resourceTypeList')

      // 给表格内滚动条滚动增加监听事件
      //   await this.$nextTick(async () => {
      //     const element = this.exchangedRecordsUITableRef.bodyWrapper
      //     element.addEventListener('scroll', this.infiniteScroll)
      //   })
      // 查询行业sku填进去行业下拉
      if (QueryIndustry.industryList.length == 1) {
        this.industryId = QueryIndustry.industryList[0].id
      }

      await this.searchMixExchangedRecordsBase()
    }

    /**
     * 加载第一页数据标志位
     */
    firstPageLoadFlag = false

    /**
     * 【换班/换期记录】无限加载
     */
    @bind
    @debounce(200)
    async infiniteScroll() {
      //   const element = this.exchangedRecordsUITableRef.bodyWrapper
      //   const scrollDistance = element.scrollHeight - element.scrollTop - element.clientHeight
      //   // console.log('scrollInfo', scrollDistance, element.scrollHeight, element.scrollTop, element.clientHeight)
      //   if (scrollDistance <= 0 && !this.firstPageLoadFlag) {
      //     if (this.exchangedRecordsList.length >= this.exchangedRecordsPage.totalSize) {
      //       this.$message.warning('没有更多数据')
      //     } else {
      //       this.exchangedRecordsPage.pageNo++
      //       await this.loadMoreMixExchangedRecords()
      //     }
      //   }
    }

    @bind
    @debounce(200)
    /**
     * 查询待换班列表
     */
    async searchMixWaitExchangeBase() {
      this.waitExchangePage.pageNo = 1
      await this.pageMixWaitExchangeList()
    }

    /**
     * 查询待换班列表
     */
    async pageMixWaitExchangeList() {
      this.waitExchangeQuery.loading = true
      console.log(1)

      try {
        this.waitExchangeList = [] as WaitExchangeTrainClassDetailVo[]
        this.excludeCommoditySkuIdList = [] as string[]
        this.excludedIssueIdList = [] as string[]
        // 未选中用户 拦截查询全部数据
        if (!this.waitExchangeQueryParams.buyerId) {
          this.waitExchangeList = []
          this.excludeCommoditySkuIdList = []
          this.excludedIssueIdList = []
          return
        }
        this.waitExchangeQueryParams.trainingMode = TrainingModeEnum.mixed
        const response = await this.queryRequestEntrance.queryAllWaitExchangeTrainClassList(
          this.waitExchangePage,
          this.waitExchangeQueryParams
        )
        this.waitExchangeList = response.pageList
        this.excludeCommoditySkuIdList = response.commoditySkuIdList
        this.excludedIssueIdList = response.issueIdList
      } catch (e) {
        console.log(e)
      } finally {
        this.waitExchangeQuery.loading = false
      }
      console.log('🚀🐱‍🚀🐱‍👓 ~ pageMixWaitExchangeList ~ this.waitExchangeList:', this.waitExchangeList)
    }

    @bind
    @debounce(200)
    /**
     * 查询换班/换期记录列表
     */
    async searchMixExchangedRecordsBase() {
      this.firstPageLoadFlag = true
      this.exchangedRecordsPage.pageNo = 1
      await this.pageMixExchangedRecordsList()
      this.firstPageLoadFlag = false
    }

    /**
     * 查询换班/换期记录列表
     */
    async pageMixExchangedRecordsList() {
      if (!this.exchangedRecordsQueryParams.buyerIdList.length) {
        return
      }
      this.exchangedRecordsQuery.loading = true
      try {
        this.exchangedRecordsList = [] as ExchangeOrderRecordDetailVo[]
        this.exchangedRecordsQueryParams.trainingMode = TrainingModeEnum.mixed
        this.exchangedRecordsList = await this.queryRequestEntrance.queryExchangeTrainClassRecordList(
          this.exchangedRecordsPage,
          this.exchangedRecordsQueryParams
        )
        console.log('exchangedRecordsList', this.exchangedRecordsList)
      } catch (e) {
        console.log(e)
      } finally {
        ;(this.exchangedRecordsUITableRef as any).doLayout()
        this.exchangedRecordsQuery.loading = false
      }
    }
    /**
     *查询退款中班级，筛选出班级ID
     */
    async getRefundOrderList() {
      const returnOrderRequestVo: ReturnOrderRequestVo = new ReturnOrderRequestVo()
      this.returnOrderPage.pageSize = 200
      this.returnOrderPage.pageNo = 1
      returnOrderRequestVo.basicData.returnOrderStatus = [0, 1, 2]
      const RefundOrderList = await this.queryRefundOrder.queryRefundOrderList(
        this.returnOrderPage,
        returnOrderRequestVo
      )

      if (RefundOrderList?.length > 0) {
        const RefundOrderIdList = RefundOrderList.map((item) => {
          //退款中的ID如果没有在筛选项中 就返回
          const RefundOrderIndex = this.excludeCommoditySkuIdList.findIndex((index) => {
            return index === item.refundCommodity.commoditySku.commoditySkuId
          })
          if (RefundOrderIndex === -1) {
            return item.refundCommodity.commoditySku.commoditySkuId
          }
        })
        // 将没有在筛选项里的添加到筛选项中
        this.excludeCommoditySkuIdList = this.excludeCommoditySkuIdList.concat(RefundOrderIdList)
      }
    }

    /**
     * 重置 - 待更换班级/期别列表
     */
    @bind
    @debounce(200)
    async resetWaitExchangeQueryParams() {
      this.waitExchangeQueryParams = new QueryWaitExchangeTrainClassListVo()
      if (this.userId) {
        this.waitExchangeQueryParams.buyerId = this.userId
        await this.searchMixWaitExchangeBase()
      } else {
        this.waitExchangeList = [] as WaitExchangeTrainClassDetailVo[]
        this.waitExchangePage.totalSize = 0
      }
    }

    /**
     * 重置 - 换班/换期记录
     */
    @bind
    @debounce(200)
    async resetMixExchangedRecordsQueryParams() {
      this.exchangedRecordsQueryParams = new QueryExchangeOrderRecordListVo()
      this.exchangedRecordsQueryParams.exchangeTrainClassStatus = 1
      if (this.userId) {
        this.exchangedRecordsQueryParams.buyerIdList = this.userId ? [this.userId] : ([] as string[])
        await this.searchMixExchangedRecordsBase()
      } else {
        this.exchangedRecordsList = [] as ExchangeOrderRecordDetailVo[]
      }
    }

    /**
     * 查看换班详情
     */
    viewExchangeMixDetail(row: ExchangeOrderRecordDetailVo) {
      this.selectedExchangeOrderStatusList = row.exchangeOrderStatusList || ([] as ExchangeOrderStatus[])
      this.uiConfig.dialog.viewExchangeTrainClassDetailVisible = true
    }

    /**
     * 换班
     */
    @bind
    @debounce(200)
    async exchangeMixClass(row: WaitExchangeTrainClassDetailVo) {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        this.orderDetailObj.orderNo = row.orderNo
        await this.orderDetailObj.queryOrderDetailByOrderNo()
        // const orderDetail = this.orderDetailObj.orderDetail
        // if (orderDetail.subOrderItems[0]?.returnStatus != 0) {
        //   return this.$message.warning('当前班级正在换班中，不可重复操作')
        // }

        this.createExchangeOrder = new CreateExchangeOrderModel()
        this.createExchangeOrder.schemeName = row.schemeName
        this.createExchangeOrder.orderNo = row.orderNo
        this.createExchangeOrder.subOrderNo = row.subOrderNo
        this.createExchangeOrder.price = row.price
        this.createExchangeOrder.isSpecialOrder = row.saleChannel == 2 ? true : false
        this.createExchangeOrder.specialOrderName = row.saleChannelName
        this.createExchangeOrder.saleChannelId = row.saleChannelId
        this.createExchangeOrder.originIssueId = row.periodId

        this.createExchangeOrder.distributorId = row.distributorId
        this.createExchangeOrder.excludeCommoditySkuIdList = cloneDeep(this.excludeCommoditySkuIdList)
        this.createExchangeOrder.excludedIssueIdList = cloneDeep(this.excludedIssueIdList)

        // 如果是分销打开分销抽屉
        if (this.createExchangeOrder.distributorId) {
          this.mutationRequestEntrance = new MutationCreateExchangeOrder()
          this.mutationRequestEntrance.orderNo = this.createExchangeOrder.orderNo || ''
          this.mutationRequestEntrance.subOrderNo = this.createExchangeOrder.subOrderNo || ''
          this.mutationRequestEntrance.originIssueId = this.createExchangeOrder.originIssueId || ''
          await this.changeMixClassDrawerFxRef.searchByExternal(this.createExchangeOrder, 'mix')
          this.uiConfig.dialog.replaceFxTrainClassVisible = true
        } else {
          this.uiConfig.dialog.replaceTrainClassVisible = true
          await this.exchangeMixTrainClassRef.searchByExternal(this.createExchangeOrder, 'mix')
        }
      } catch (e) {
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }
    /**
     * 继续换班
     */
    @bind
    @debounce(200)
    async continueExchangeMixTrainClass(row: ExchangeOrderRecordDetailVo) {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        const exchangeStatus = row.remote?.basicData?.status ?? null
        const queryRemote = new MutationExchangeOrder()
        queryRemote.exchangeOrderNo = row.remote.exchangeOrderNo ?? ''
        // 退货失败 => 继续退货
        if (exchangeStatus === 3) {
          const response = await queryRemote.retryRecycleResouce()
          if (response?.isSuccess()) {
            this.$message.success('操作成功！')
            await this.handleReloadData()
          }
        }
        // 发货失败 => 继续发货
        if (exchangeStatus === 6) {
          const response = await queryRemote.retryDelivery()
          if (response?.isSuccess()) {
            this.$message.success('操作成功！')
            await this.handleReloadData()
          }
        }
      } catch (e) {
        // console.log(e)
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }

    /**
     * 响应刷新事件
     */

    async handleReloadData() {
      // console.log('重新加载')
      await this.resetWaitExchangeQueryParams()
      await this.resetMixExchangedRecordsQueryParams()
    }

    /**
     * 确认换班
     */
    async confirmExchangeTrainClass(
      commoditySkuId: string,
      pricingSchemeId?: string,
      schemeName?: string,
      issueName?: string,
      issueId?: string
    ) {
      this.$confirm(`当前选择的目标班级：${schemeName}，目标期别：${issueName}，是否确认继续更换？`, '提示', {
        closeOnClickModal: false,
        confirmButtonText: '确认',
        showCancelButton: true,
        type: 'warning'
      }).then(async () => {
        const loading = this.$loading({
          lock: true,
          text: '加载中',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.8)'
        })
        this.mutationRequestEntrance.commoditySkuId = commoditySkuId
        this.mutationRequestEntrance.exchangePricingPolicyId = pricingSchemeId
        this.mutationRequestEntrance.targetIssueID = issueId

        try {
          const status = await this.mutationRequestEntrance.sellerApplyExchange()
          console.log('🚀🐱‍🚀🐱‍👓 ~ .then ~ status:', status)
          if (status.isSuccess()) {
            this.$message.success('操作成功')
            this.$emit('reloadData')
          } else if (status.code === 90001) {
            this.$message.error('目标班级不在学习时间，请重新选择')
          } else if (status.code === 60007) {
            this.$message.error('目标期别未开启报名，请重新选择')
          } else if (status.code === 60004 || status.code == 60005) {
            this.$message.error('目标期别报名时间已过，请重新选择')
          } else if (status.code === 10005 || status.code == 10006) {
            this.$message.error('目标期别培训时间已过，请重新选择')
          } else if (status.code === 60001) {
            this.$message.error('目标期别已报满，请重新选择')
          } else if (status.code == 3008) {
            this.$message.error('因当前订单正在办理退货/款业务，不能进行换班')
          } else if (status.code == 3009) {
            this.$message.error('因当前订单已办理部分退款，系统暂不支持课程更换服务')
          } else if ([99996, 99997, 99998, 99999, 30000, 30001, 30002].includes(Number(status.code))) {
            this.$message.error('不允许换班')
          } else if (status.code === 10001) {
            this.$confirm('无证书信息，不可报名', '提示', {
              showConfirmButton: false,
              cancelButtonText: '关闭',
              type: 'warning'
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消'
              })
            })
          } else if (status.code === 10002) {
            this.$confirm('班级专业与证书专业主增项不一致，无法报名', '提示', {
              showConfirmButton: false,
              cancelButtonText: '关闭',
              type: 'warning'
            }).catch(() => {
              this.$message({
                type: 'info',
                message: '已取消'
              })
            })
          } else {
            this.$message.error((status?.message as string) || '操作失败')
          }
        } catch (e) {
          this.$message.error(e)
        } finally {
          loading.close()
        }
      })
    }
    // 下面都是计算属性-------------------下面都是计算属性-------------------下面都是计算属性-------------------------下面都是计算属性-----
    /**
     * 【待更换班级/期别列表】培训方案类型
     */
    get waitExchangeSchemeType() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return SchemeType.getNewSchemeType(item, false)
      }
    }

    /**
     * 【待更换班级/期别列表】考核情况
     */
    get assessmentStatus() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return this.assessmentStatusList.find((el) => el.code === item.assessmentStatus)?.desc || ''
      }
    }

    /**
     * 【待更换班级/期别列表】考核情况样式
     */
    get assessmentStatusStyle() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return this.assessmentStatusMap.get(item.assessmentStatus)
      }
    }

    /**
     * 【待更换班级/期别列表】状态
     */
    get status() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return this.statusList.find((el) => el.code === item.status)?.desc || ''
      }
    }

    /**
     * 【待更换班级/期别列表】状态样式
     */
    get statusStyle() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return this.statusMap.get(item.status)
      }
    }

    /**
     * 【待更换班级/期别列表】来源类型
     */
    get resourceType() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return this.resourceTypeList.find((el) => el.code === item.resourceType)?.desc || ''
      }
    }

    /**
     * 【待更换班级/期别列表】允许换班
     */
    get enableExchangeTrainClass() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return item.status == TrainClassStatusEnum.Effective ? true : false
      }
    }
    /**
     * 【待更换班级/期别列表】允许换期
     */
    get enableExchangeTrainPeriod() {
      return (item: WaitExchangeTrainClassDetailVo) => {
        return item.status == TrainClassStatusEnum.Effective ? true : false
      }
    }
    /**
     * 【换班/换期记录】培训班方案类型
     */
    get exchangeRecordCommoditySchemeType() {
      return (item: OrderCommodityVo) => {
        const schemeType = item.resourceVo?.schemeTypeVo || null
        const schemeTypeName = this.schemeTypeList.find((el) => el.code === schemeType)?.desc || ''
        const showName = schemeTypeName ? '培训班-' + schemeTypeName : ''
        // const showName = schemeTypeName ? schemeTypeName : ''
        return showName
      }
    }

    /**
     * 【换班/换期记录】换班状态
     */
    get exchangeTrainClassStatus() {
      return (item: ExchangeOrderRecordDetailVo) => {
        return this.exchangeTrainClassStatusList.find((el) => el.code === item.exchangeTrainClassStatus)?.desc || ''
      }
    }

    /**
     * 【换班/换期记录】换班状态样式
     */
    get exchangeTrainClassStatusStyle() {
      return (item: ExchangeOrderRecordDetailVo) => {
        return this.exchangeTrainClassStatusStyleMap.get(item.exchangeTrainClassStatus)
      }
    }

    /**
     * 【换班/换期记录】换货单状态
     */
    get exchangeOrderStatus() {
      return (status: ExchangeOrderStatusEnum) => {
        return this.exchangeOrderStatusList.find((el) => el.code === status)?.desc || ''
      }
    }
    /**
     * 【换期记录】换货单状态
     */
    get exchangePeriodStatus() {
      return (status: ExchangePeriodStatusEnum) => {
        return this.exchangePeriodStatusList.find((el) => el.code === status)?.desc || ''
      }
    }
    // 待更换班级/期别列表获取属性
    getPrototype(val: WaitExchangeTrainClassDetailVo) {
      let arrList = new Array<any>()
      if (val && val.industry) arrList.push(val.industry)
      if (val && val.year) arrList.push(val.year + '年')
      if (val && val.industryId == 'industry0221018501809dc4d43e0002') {
        arrList.push(val.subjectType)
        arrList.push(val.trainingCategory)
        arrList.push(val.trainingProfessional)
      }
      if (val && val.industryId == 'industry0221018501809dc4d43e0003') {
        arrList.push(val.subjectType)
        arrList.push(val.trainingProfessional)
      }
      if (val && val.industryId == 'industry0221018501809dc4d43e0004') {
        arrList.push(val.trainingCategory)
        arrList.push(val.trainingObject)
        arrList.push(val.positionCategory)
      }
      if (val && val.industryId == 'industry0221018501809dc4d43e0005') {
        arrList.push(val.jobLevel)
      }
      // 教师行业
      if (val && val.industryId == 'industry0221018501809dc4d43e0006') {
        arrList.push(val.grade)
        arrList.push(val.subject)
      }
      if (!arrList.length) return
      arrList = arrList.filter((item) => {
        // 这里检查item是否为非空字符串，即它既不是空字符串、null，也不是undefined
        return typeof item === 'string' && item.trim().length > 0
      })
      return '【' + arrList.join(' / ') + '】'
    }

    //换班/换期记录获取属性
    getChangeClassPrototype(val: SkuPropertyResponseVo) {
      const arrList = new Array<any>()
      if (!val) return
      if (val.industry && val.industry.skuPropertyName) arrList.push(val.industry.skuPropertyName)
      if (val.year && val.year.skuPropertyName) arrList.push(val.year.skuPropertyName + '年')
      if (val.subjectType && val.subjectType.skuPropertyName) arrList.push(val.subjectType.skuPropertyName)
      if (val.trainingMajor && val.trainingMajor.skuPropertyName) arrList.push(val.trainingMajor.skuPropertyName)
      if (val.trainingCategory && val.trainingCategory.skuPropertyName)
        arrList.push(val.trainingCategory.skuPropertyName)
      // 主项增项的sku
      if (val.trainingObject && val.trainingObject.skuPropertyName) arrList.push(val.trainingObject.skuPropertyName)
      if (val.positionCategory && val.positionCategory.skuPropertyName)
        arrList.push(val.positionCategory.skuPropertyName)
      if (val.jobLevel && val.jobLevel.skuPropertyName) arrList.push(val.jobLevel.skuPropertyName)
      if (val.learningPhase && val.learningPhase.skuPropertyName) arrList.push(val.learningPhase.skuPropertyName)
      if (val.discipline && val.discipline.skuPropertyName) arrList.push(val.discipline.skuPropertyName)

      if (!arrList.length) return
      return '【' + arrList.join(' / ') + '】'
    }
    /**
     * tab切换
     */
    async handleClick(val: any) {
      if (val.name === 'shiftChange') {
        await this.resetMixExchangedRecordsQueryParams()
      } else {
        await this.resetExchangePeriodRecordQuery()
      }
    }

    // 更换期别记录方法模块start-------------------------
    /**
     * 查询更换期别记录列表
     */
    async getExchangePeriodRecordList() {
      if (this.userId) {
        this.exchangedPeriodListLoading = true
        try {
          this.exchangedPeriodQueryParams.buyerId = this.userId
          this.exchangedPeriodQueryParams.trainingMode = TrainingModeEnum.mixed
          this.exchangedPeriodList = await this.queryRequestEntrance.queryIssueChangeRecord(
            this.exchangedPeriodPage,
            this.exchangedPeriodQueryParams
          )
        } catch (e) {
          console.log('获取更换期别记录列表失败！', e)
        } finally {
          this.exchangedPeriodListLoading = false
          ;(this.exchangedPeriodListRef as any).doLayout()
        }
        console.log('🚀🐱‍🚀🐱‍👓 ~ getExchangePeriodRecordList ~ this.exchangedPeriodList:', this.exchangedPeriodList)
      } else {
        this.exchangedPeriodList = []
      }
    }
    /**
     * 重置查询更换期别记录
     */
    resetExchangePeriodRecordQuery() {
      this.exchangedPeriodPage.pageNo = 1
      this.exchangedPeriodQueryParams = new QueryIssueChangeRecordParam()
      this.getExchangePeriodRecordList()
    }
    /**
     * 换期
     */
    @bind
    @debounce(200)
    async exchangePeriod(row: WaitExchangeTrainClassDetailVo) {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        await this.changePeriodDrawerRef.openDrawer(row, this.excludedIssueIdList)
      } catch (e) {
        console.log(e)
      } finally {
        loading.close()
      }
    }
    /**
     * 继续换期
     */
    @bind
    @debounce(200)
    continueExchangePeriod(item: any) {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        //
      } catch (e) {
        console.log(e)
      } finally {
        loading.close()
      }
    }
    // 更换期别记录方法模块end-------------------------
  }
</script>
