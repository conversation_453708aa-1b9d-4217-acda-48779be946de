<route-meta>
{
"isMenu": true,
"title": "地区管理员管理",
"sort": 5,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  import RegionAdministrator from '@hbfe/jxjy-admin-account/src/region-administrator/index.vue'
  import { WXGLY } from '@/models/RoleTypes'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  @RoleTypeDecorator({
    query: [WXGLY],
    create: [WXGLY],
    resetPassword: [WXGLY],
    detail: [WXGLY],
    modify: [WXGLY],
    enable: [WXGLY]
  })
  export default class extends RegionAdministrator {}
</script>
<style scoped>
  .region-admin-table ::v-deep .el-table .cell {
    white-space: pre-wrap;
  }
</style>
