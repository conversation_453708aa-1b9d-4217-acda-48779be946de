<!-- 考试日志 -->
<template>
  <!-- 考试日志 -->
  <div class="f-p20">
    <el-tabs v-model="activeName" type="card" class="no-margin">
      <el-tab-pane label="进入考试日志" name="first">
        <div class="f-mt15 f-mb15">
          <el-tag type="danger" class="f-vm f-mr10">第一次</el-tag>
          <el-tag type="info">考试时间：2020-01-05 10:35:40 ~ 2020-01-05 10:45:40</el-tag>
        </div>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500" highlight-current-row class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="拍摄照片" min-width="150">
            <template>
              <img src="@design/admin/assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
            </template>
          </el-table-column>
          <el-table-column label="拍摄时间" min-width="200">
            <template>2017-12-12 17:23:23</template>
          </el-table-column>
          <el-table-column label="匹配结果" min-width="100" align="center">
            <template>
              <el-badge is-dot type="danger" class="badge-status">不匹配</el-badge>
            </template>
          </el-table-column>
        </el-table>
        <div class="f-mt15 f-mb15">
          <el-tag type="danger" class="f-vm f-mr10">第二次</el-tag>
          <el-tag type="info">考试时间：2020-01-05 10:35:40 ~ 2020-01-05 10:45:40</el-tag>
        </div>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500" highlight-current-row class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="拍摄照片" min-width="150">
            <template>
              <img src="@design/admin/assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
            </template>
          </el-table-column>
          <el-table-column label="拍摄时间" min-width="200">
            <template>2017-12-12 17:23:23</template>
          </el-table-column>
          <el-table-column label="匹配结果" min-width="100" align="center">
            <template>
              <el-badge is-dot type="success" class="badge-status">匹配</el-badge>
            </template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
      <el-tab-pane label="考试过程日志" name="second">
        <div class="f-mt15 f-mb15">
          <el-tag type="danger" class="f-vm f-mr10">第一次</el-tag>
          <el-tag type="info">考试时间：2020-01-05 10:35:40 ~ 2020-01-05 10:45:40</el-tag>
        </div>
        <el-row class="no-gutter">
          <el-form :inline="true" label-width="auto" class="m-text-form">
            <el-col :span="8">
              <el-form-item label="考试成绩：">100分</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否合格：">合格</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否替考：">无替考</el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500" highlight-current-row class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="拍摄照片" min-width="150">
            <template>
              <img src="@design/admin/assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
            </template>
          </el-table-column>
          <el-table-column label="拍摄时间" min-width="200">
            <template>2017-12-12 17:23:23</template>
          </el-table-column>
          <el-table-column label="匹配结果" min-width="100">
            <template>
              <el-badge is-dot type="danger" class="badge-status">不匹配</el-badge>
            </template>
          </el-table-column>
          <el-table-column label="考试时间点" min-width="100" align="center">
            <template>00:01:23</template>
          </el-table-column>
        </el-table>
        <div class="f-mt15 f-mb15">
          <el-tag type="danger" class="f-vm f-mr10">第二次</el-tag>
          <el-tag type="info">考试时间：2020-01-05 10:35:40 ~ 2020-01-05 10:45:40</el-tag>
        </div>
        <el-row class="no-gutter">
          <el-form :inline="true" label-width="auto" class="m-text-form">
            <el-col :span="8">
              <el-form-item label="考试成绩：">100分</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否合格：">合格</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否替考：">无替考</el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500" highlight-current-row class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="拍摄照片" min-width="150">
            <template>
              <img src="@design/admin/assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
            </template>
          </el-table-column>
          <el-table-column label="拍摄时间" min-width="200">
            <template>2017-12-12 17:23:23</template>
          </el-table-column>
          <el-table-column label="匹配结果" min-width="100">
            <template>
              <el-badge is-dot type="success" class="badge-status">匹配</el-badge>
            </template>
          </el-table-column>
          <el-table-column label="考试时间点" min-width="100" align="center">
            <template>00:01:23</template>
          </el-table-column>
        </el-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  @Component
  export default class extends Vue {
    // 考试日志
    @Prop({
      type: Object,
      default: () => {
        return {}
      }
    })
    examLog: object

    tableData = [{ field101: '1' }]

    activeName = 'first'
  }
</script>
