<template>
  <el-main>
    <div class="m-questionnaire" ref="previewRef">
      <div class="content">
        <div class="header">{{ params.name }}</div>
        <div class="questionnaire-bd">
          <!--单选题-->
          <div class="question" v-for="(item, index) in questionList" :key="index">
            <div class="tit" style="display: flex; align-items: center" :id="`${index}`">
              <span class="num">{{ index + 1 }}、</span>

              <div class="f-flex-sub" style="display: flex; align-items: center">
                <span class="type">{{ QuestionType.map.get(item.type) }}</span>
                <span class="text" v-if="item.type === QuestionTypeEnum.single" style="display: flex">
                  <component :is="richValue(item.optionQuestion.described)"></component
                  ><span v-if="item.isTeacherEvaluate">{{
                    item.onlineOrOffline == '1' ? '（线上课程）' : '（线下学习）'
                  }}</span>
                </span>
                <span class="text" v-if="item.type === QuestionTypeEnum.multiple" style="display: flex">
                  <component :is="richValue(item.multipleOptionQuestion.described)"></component
                  ><span v-if="item.isTeacherEvaluate">{{
                    item.onlineOrOffline == '1' ? '（线上课程）' : '（线下学习）'
                  }}</span>
                </span>
                <span class="text" v-if="item.type === QuestionTypeEnum.answer">
                  <component :is="richValue(item.answerQuestion.described)"></component>
                </span>
                <span class="text" v-if="item.type === QuestionTypeEnum.gauge">
                  <component :is="richValue(item.gaugeQuestion.described)"></component>
                </span>
              </div>
              <div class="tag" v-if="item.isMustAnswered"><i class="icon el-icon-warning-outline"></i>本题必选</div>
            </div>
            <div class="option" v-if="item.isTeacherEvaluate || item.isTeacherEvaluate">
              当前试题为教师评价试题，选项将读取引用此问卷模板的方案或期别中，课程授课教师名称，无需进行配置。
            </div>
            <div
              class="option"
              v-if="item.optionQuestion.options && item.type === QuestionTypeEnum.single && !item.isTeacherEvaluate"
            >
              <el-radio-group>
                <el-col :span="24" v-for="(itm, idx) in item.optionQuestion.options" :key="idx">
                  <el-radio label="1" style="display: flex; align-items: center">
                    <div style="display: flex">
                      <span class="f-cb f-fb f-mr5">{{ resolverIndexToCharCode(idx) }}.</span>
                      <component :is="richValue(itm.content)"></component>
                      <div v-if="itm.completion">
                        ({{ itm.isRequire ? '必填' : '选填' }})<span class="u-underline">请输入内容</span>
                      </div>
                    </div>
                  </el-radio></el-col
                >
              </el-radio-group>
            </div>

            <div
              class="option"
              v-if="
                item.multipleOptionQuestion.options &&
                item.type === QuestionTypeEnum.multiple &&
                !item.isTeacherEvaluate
              "
            >
              <el-radio-group>
                <el-col :span="24" v-for="(itm, idx) in item.multipleOptionQuestion.options" :key="idx">
                  <el-checkbox :value="1" style="display: flex; align-items: center">
                    <div style="display: flex">
                      <span class="f-cb f-fb f-mr5">{{ resolverIndexToCharCode(idx) }}.</span>
                      <component :is="richValue(itm.content)"></component>

                      <div v-if="itm.completion">
                        ({{ itm.isRequire ? '必填' : '选填' }})<span class="u-underline">请输入内容</span>
                      </div>
                    </div>
                  </el-checkbox></el-col
                >
              </el-radio-group>
            </div>
            <div class="option" v-if="item.optionQuestion && item.type === QuestionTypeEnum.gauge">
              <el-row :gutter="20">
                <el-col :span="24" class="f-mt10" style="display: flex; align-items: center; flex-wrap: wrap">
                  <span class="f-mr30" v-if="item.gaugeQuestion.type !== GaugeTypeEnum.customer"
                    >非常不{{ GaugeQuestionTypeName(item.gaugeQuestion.type) }}</span
                  >
                  <span class="f-mr30" v-else>{{ item.gaugeQuestion.minDeepTip }}</span>

                  <el-radio
                    v-for="value in GaugeQuestionRow(item.gaugeQuestion).array"
                    :key="value"
                    :label="value"
                    class="f-mr20"
                    style="padding-top: 0px"
                    >{{ value }}</el-radio
                  >
                  <span v-if="item.gaugeQuestion.type !== GaugeTypeEnum.customer"
                    >非常{{ GaugeQuestionTypeName(item.gaugeQuestion.type) }}</span
                  >
                  <span v-else>{{ item.gaugeQuestion.maxDeepTip }}</span>
                </el-col>
              </el-row>
            </div>
            <div class="option" v-if="item.answerQuestion.described && item.type === QuestionTypeEnum.answer">
              <el-input type="textarea" :rows="8"></el-input>
            </div>
          </div>

          <div class="f-tc f-mt25">
            <el-button type="primary" round class="submit">提交问卷</el-button>
          </div>
        </div>
      </div>
      <div class="question-card" :class="isShow ? 'is-show' : ''" @click.stop="isShow = true">
        <div class="cont">
          <!--打开按钮-->
          <div class="btn"></div>
          <div class="tips">
            <div class="item"><span class="type"></span>未答</div>
            <div class="item"><span class="type is-answered"></span>已答</div>
            <div class="item"><span class="type is-choose"></span>选答</div>
            <div class="item"><span class="type is-must"></span>必答</div>
            <div class="item">
              <el-button type="primary" size="mini" v-if="isShow" @click.stop="isShow = false">收起答题卡</el-button>
            </div>
          </div>
          <div class="nums">
            <span
              class="num"
              v-for="(item, index) in questionList"
              :key="index"
              @click="scrollToAnchor(index)"
              :class="item.isMustAnswered ? 'is-must' : 'is-choose'"
              >{{ index + 1 }}</span
            >
          </div>
        </div>
      </div>
    </div>
  </el-main>
</template>
<script lang="ts">
  import Question from '@api/service/common/question-naire/Question'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import QuestionType, { QuestionTypeEnum } from '@api/service/common/enums/question-naire/QuestionType'
  import GaugeType, { GaugeTypeEnum } from '@api/service/common/question-naire/enums/GaugeType'
  import GaugeQuestion from '@api/service/common/question-naire/GaugeQuestion'
  import QuestionnaireDetail from '@api/service/management/resource/question-naire/QuestionnaireDetail'
  import RootModule from '@/store/RootModule'
  import { ExamUtil } from '@/store/module/exam/common/ExamUtil'
  import { QuestionSourceTypeEnum } from '@api/service//common/question-naire/enums/QuestionSourceType'
  @Component
  export default class extends Vue {
    @Ref('previewRef') previewRef: any
    radio = '' //占位字段
    isShow = false
    params = new QuestionnaireDetail() //问卷详情 请求
    questionList: Question[] = new Array<Question>() //展示数据
    QuestionType = new QuestionType() //试题类型枚举
    QuestionTypeEnum = QuestionTypeEnum //试题类型枚举
    GaugeType = new GaugeType() //量表状态枚举
    GaugeTypeEnum = GaugeTypeEnum //量表状态枚举
    /**
     * 详情
     */
    async queryDetail() {
      try {
        // this.query.loading = true
        this.params.id = this.id
        await this.params.queryDetail(true)
        this.questionList = this.params.questionList
      } catch (e) {
        console.log(e)
      } finally {
        // this.query.loading = false
      }
    }
    get richValue() {
      return (row: string) => {
        return {
          template: '<div>' + this.testFwbImg(row) + '</div>'
        }
      }
    }
    /**
     * 测试富文本图片
     */
    testFwbImg(str: string) {
      return str.replace(/<img.*?>/g, function (img) {
        const regex = /<img[^>]+src="([^">]+)"/
        const match = img.match(regex)
        return `
            <el-popover
            placement="top-start"
            width="200"
            trigger="hover">
              <img src="${match[1]}" style="width: 200px; height: 200px" />
              <span slot="reference" style="color:#1f86f0">[查看图片]</span>
          </el-popover>
         `.trim()
      })
    }
    /**
     * 量级-程度（非自定义前提下）
     */
    get GaugeQuestionTypeName() {
      return (row: GaugeTypeEnum) => {
        return this.GaugeType.map.get(row).replace(/度/g, '')
      }
    }

    /**
     * 量级-区间
     */
    get GaugeQuestionRow() {
      return (row: GaugeQuestion) => {
        return {
          minRow: row.startLevel,
          maxRow: row.startLevel + row.levelNum - 1,
          array: Array.from({ length: row.levelNum }, (_, index) => row.startLevel + index)
        }
      }
    }
    /**
     * 试题选项
     */
    resolverIndexToCharCode(index: number) {
      return ExamUtil.matchCharCode(index)
    }
    scrollToAnchor(index: number) {
      const anchors = document.querySelectorAll('.tit')
      const target = anchors[index]
      if (target) {
        target.scrollIntoView(true)
      }
    }
    id = ''
    created() {
      if (this.$route?.query?.id) {
        // this.id = this.$route.params.id
        this.id = this.$route?.query?.id as string

        this.queryDetail()
      } else {
        this.params.name = this.$route?.query?.name as string
        this.questionList = JSON.parse(localStorage.getItem('questionList'))
      }
    }
    destroyed() {
      localStorage.removeItem('questionList')
    }

    async mounted() {
      RootModule.changeMenuStatus(true)
    }
  }
</script>
