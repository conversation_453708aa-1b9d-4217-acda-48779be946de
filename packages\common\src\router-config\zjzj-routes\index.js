function Statistic() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/zjzj/statistic" */ '@hbfe/jxjy-admin-routers/src/zjzj/statistic.vue'
  )
}
function StatisticLearningStatisticIndex() {
  return import(
    /* webpackChunkName: "@hbfe/jxjy-admin-routers/src/zjzj/statistic-learning-statistic-index" */ '@hbfe/jxjy-admin-routers/src/zjzj/statistic/learning-statistic/index.vue'
  )
}

export default [
  {
    name: 'statistic',
    path: '/statistic',
    component: Statistic,
    meta: {
      permissionMap: {},
      openWhenInit: false,
      closeAble: false,
      isMenu: true,
      title: '统计报表',
      sort: 5,
      icon: 'icon-shuju',
      ownerGroup: [],
      group: 'statistic'
    },
    children: [
      {
        name: 'statistic-learning-statistic-index',
        path: 'learning-statistic',
        component: StatisticLearningStatisticIndex,
        meta: {
          permissionMap: {},
          isMenu: true,
          title: '学员学习明细',
          sort: 5,
          icon: 'icon-mingxi',
          ownerGroup: [],
          group: 'statistic.learning-statistic'
        }
      }
    ]
  }
]
