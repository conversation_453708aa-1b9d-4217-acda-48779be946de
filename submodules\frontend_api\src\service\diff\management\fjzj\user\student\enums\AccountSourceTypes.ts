import AbstractEnum from '@api/service/common/enums/AbstractEnum'
/**
 * 注册来源
 */
enum AccountSourceEnum {
  BUILT_IN = 0,
  HOME = 1,
  ANDROID = 2,
  IOS = 3,
  IMPORT = 4,
  MOVE = 5,
  FXPT = 6,
  TRAINING_CHANNEL = 7,
  HUAYI = 8
}
export { AccountSourceEnum }

class AccountSourceTypes extends AbstractEnum<AccountSourceEnum> {
  static enum = AccountSourceEnum

  constructor(status?: AccountSourceEnum) {
    super()
    this.current = status
    this.map.set(AccountSourceEnum.BUILT_IN, '内置')
    this.map.set(AccountSourceEnum.HOME, '项目主网站')
    this.map.set(AccountSourceEnum.ANDROID, '安卓')
    this.map.set(AccountSourceEnum.IOS, 'IOS')
    this.map.set(AccountSourceEnum.IMPORT, '后台导入')
    this.map.set(AccountSourceEnum.MOVE, '迁移数据')
    this.map.set(AccountSourceEnum.FXPT, '分销')
    this.map.set(AccountSourceEnum.TRAINING_CHANNEL, '专题')
    this.map.set(AccountSourceEnum.HUAYI, '华医网')
  }
  list() {
    return super.list().filter(item => {
      return [
        AccountSourceEnum.HOME,
        AccountSourceEnum.IMPORT,
        AccountSourceEnum.MOVE,
        AccountSourceEnum.FXPT,
        AccountSourceEnum.HUAYI
      ].includes(item.code)
    })
  }
}

export default new AccountSourceTypes()
