<template>
  <el-main>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="注册登录" name="first">详见 0301_功能设置_注册登录.vue</el-tab-pane>
      <el-tab-pane label="集体报名" name="second">详见 0303_功能设置_集体报名.vue</el-tab-pane>
      <el-tab-pane label="增值税电子普通发票（自动开票）" name="third">详见 0304_功能设置_增值税发票.vue</el-tab-pane>
      <el-tab-pane label="培训证明" name="fourth">详见 0305_功能设置_培训证明.vue</el-tab-pane>
      <el-tab-pane label="视频播放设置" name="five">详见 0306_功能设置_视频播放设置.vue</el-tab-pane>
      <el-tab-pane label="门户精品课程" name="six">
        <div class="f-p15">
          <!--无分类-->
          <el-card shadow="never" class="m-card f-mb15">
            <div class="f-mt10">
              <span class="f-mr10">请选择精品课程包展示方式：</span>
              <el-radio-group v-model="radio">
                <el-radio :label="3">
                  无分类
                  <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9"></i>
                    <div slot="content">
                      支持不按照分类添加课程，最多7门。若无精品课程配置，门户将默认展示最新发布的7门课程。
                    </div>
                  </el-tooltip>
                </el-radio>
                <el-radio :label="6">
                  有分类
                  <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9"></i>
                    <div slot="content">
                      支持按不同的课程分类添加课程，每一个分类最多添加7门。若无精品课程配置，门户将默认展示最新发布的7门课程。
                    </div>
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
            </div>
            <el-divider class="m-divider"></el-divider>
            <el-button type="primary" icon="el-icon-plus" class="f-mb20">添加精品课程</el-button>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="排序" min-width="70" align="center">
                <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
              </el-table-column>
              <el-table-column label="课程名称" min-width="300">
                <template>课程名称课程名称课程名称课程名称课程名称</template>
              </el-table-column>
              <el-table-column label="课程分类" min-width="180">
                <template>课程分类课程分类</template>
              </el-table-column>
              <el-table-column label="操作时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">取消展示</el-button>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
          <!--有分类-->
          <el-card shadow="never" class="m-card f-mb15">
            <div class="f-mt10">
              <span class="f-mr10">请选择精品课程包展示方式：</span>
              <el-radio-group v-model="radio1">
                <el-radio :label="3">
                  无分类
                  <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9"></i>
                    <div slot="content">
                      支持不按照分类添加课程，最多7门。若无精品课程配置，门户将默认展示最新发布的7门课程。
                    </div>
                  </el-tooltip>
                </el-radio>
                <el-radio :label="6">
                  有分类
                  <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-info m-tooltip-icon f-c9"></i>
                    <div slot="content">
                      支持按不同的课程分类添加课程，每一个分类最多添加7门。若无精品课程配置，门户将默认展示最新发布的7门课程。
                    </div>
                  </el-tooltip>
                </el-radio>
              </el-radio-group>
            </div>
            <el-divider class="m-divider"></el-divider>
            <el-row type="flex" :gutter="20">
              <el-col :sm="7" :lg="6" :xl="5">
                <div class="m-course-classify">
                  <div class="m-tit is-mini">
                    <span class="tit-txt">精品课程分类</span>
                  </div>
                  <div class="f-pl15 f-mt20">
                    <el-button type="primary" icon="el-icon-plus">添加分类</el-button>
                    <div class="f-mt20">
                      <div class="item">
                        <div class="f-flex-sub">分类名称</div>
                        <i class="icon el-icon-delete"></i>
                      </div>
                      <div class="item current">
                        <div class="f-flex-sub">分类名称</div>
                        <i class="icon el-icon-delete"></i>
                      </div>
                      <div class="item">
                        <div class="f-flex-sub">分类名称</div>
                        <i class="icon el-icon-delete"></i>
                      </div>
                      <div class="item">
                        <el-input v-model="form.name" placeholder="请输入" class="f-flex-sub" />
                        <i class="icon el-icon-delete"></i>
                      </div>
                      <div class="item">
                        <el-input v-model="form.name" placeholder="请输入" class="f-flex-sub" />
                        <i class="icon el-icon-delete"></i>
                      </div>
                    </div>
                  </div>
                </div>
              </el-col>
              <el-col :sm="17" :lg="18" :xl="19">
                <div class="m-tit is-mini">
                  <span class="tit-txt">精品课程列表</span>
                </div>
                <div class="f-pl15 f-mtb20">
                  <el-button type="primary" icon="el-icon-plus" class="f-mb20">添加精品课程</el-button>
                  <!--表格-->
                  <el-table stripe :data="tableData" max-height="500px" class="m-table">
                    <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                    <el-table-column label="排序" min-width="70" align="center">
                      <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
                    </el-table-column>
                    <el-table-column label="课程名称" min-width="300">
                      <template>课程名称课程名称课程名称课程名称课程名称</template>
                    </el-table-column>
                    <el-table-column label="课程分类" min-width="180">
                      <template>课程分类课程分类</template>
                    </el-table-column>
                    <el-table-column label="操作时间" min-width="180">
                      <template>2020-11-11 12:20:20</template>
                    </el-table-column>
                    <el-table-column label="操作" width="100" align="center" fixed="right">
                      <template>
                        <el-button type="text" size="mini">取消展示</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                  <!--分页-->
                  <el-pagination
                    background
                    class="f-mt15 f-tr"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage4"
                    :page-sizes="[100, 200, 300, 400]"
                    :page-size="100"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="400"
                  >
                  </el-pagination>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'six',
        activeName1: 'first',
        activeName2: 'first',
        radio: 3,
        radio1: 6,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
