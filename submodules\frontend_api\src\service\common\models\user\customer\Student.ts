import UserBindedThirdAccount from '../UserBindedThirdAccount'
import { RegisterType, Sex, SourceType, UniqueType } from '@api/service/common/models/user/enums'

class Student {
  userCategories: Array<string>
  /**
   * 人员类别
   */
  peoples?: Array<string>
  /**
   * 所属单位ID
   */
  unitId: string
  /**
   * 账户id
   */
  accountId: string
  /**
   * 学员ID
   */
  userId: string
  /**
   * 姓名
   */
  name: string
  /**
   * 昵称
   */
  nickName: string
  /**
   * 唯一性类型
   @see UniqueTypeConst
   */
  uniqueType: UniqueType
  /**
   * 唯一性值
   */
  uniqueData: string
  /**
   * 头像地址
   */
  displayPhotoUrl: string
  /**
   * 性别
   @see SexConst
   */
  sex: Sex
  /**
   * 出生年月
   */
  bornDay: string
  /**
   * 手机号码
   */
  phoneNumber: string
  /**
   * 邮政编码
   */
  postCode: string
  /**
   * 所属地区编码
   */
  areaPath: string
  /**
   * 所属地区名称路径
   例：福建省-福州市-鼓楼区
   */
  areaPathName: string
  /**
   * 联系地址
   */
  address: string
  /**
   * 用户创建日期
   */
  createDate: string
  /**
   * 注册方式
   @see RegisterTypeConst
   */
  registerType: RegisterType
  /**
   * 注册来源
   @see SourceTypeConst
   */
  sourceType: SourceType
  /**
   * 注册来源单位id
   */
  registerUnitId: string
  /**
   * 登陆账号集合
   */
  //loginAccounts: Array<LoginAccountResponse>
  /**
   * 最高学历 | 推荐使用：参考国标【GB/T 4658 2006】, 值为【学历】字典ID；不完全参考国标，值为学历名称
   */
  highestEducation: string
  /**
   * 学员所属工作单位（实体单位）
   */
  workUnitIds: Array<string>
  /**
   * 工作单位名称(只存储名称，用于给没有实体工作单位的项目使用)
   */
  workUnitName: string
  /**
   * 工作单位所在地区路径(用于给没有实体工作单位的项目使用)
   */
  workUnitAreaPath: string
  /**
   * 绑定的第三方账号
   */
  bindedThirdAccounts: Array<UserBindedThirdAccount>
}

export default Student
