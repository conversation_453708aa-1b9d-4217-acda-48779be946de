<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <el-row :gutter="16" class="m-query">
        <el-form :inline="true" label-width="auto">
          <el-col :sm="12" :md="8" :xl="6">
            <el-form-item label="集体报名批次号">
              <el-input v-model="returnOrderRequestVo.batchOrderNo" clearable placeholder="请输入集体报名批次号" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="8" :xl="6">
            <el-form-item label="退款状态">
              <el-select v-model="returnOrderRequestVo.refundStatus" clearable filterable placeholder="请选择退款状态">
                <el-option label="全部" value=""></el-option>
                <el-option label="退款审批中" value="1"></el-option>
                <el-option label="退款处理中" value="2"></el-option>
                <el-option label="退款成功" value="3"></el-option>
                <el-option label="退款失败" value="4"></el-option>
                <el-option label="拒绝退款" value="5"></el-option>
                <el-option label="已取消" value="6"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="8" :xl="6">
            <el-form-item label="退款申请时间">
              <double-date-picker
                :begin-create-time.sync="returnOrderRequestVo.refoundStartDate"
                :end-create-time.sync="returnOrderRequestVo.refoundEndDate"
                begin-time-placeholder="退款申请时间"
                end-time-placeholder="退款申请时间"
              ></double-date-picker>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="8" :xl="6" class="f-fr">
            <el-form-item class="f-tr">
              <el-button type="primary" @click="search" :loading="query.loading">查询</el-button>
              <el-button @click="resetParams">重置</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table
        stripe
        :data="returnOrderResponseVo"
        v-loading="query.loading"
        ref="returnOrderRef"
        max-height="500px"
        class="m-table"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="集体报名批次号" min-width="240" fixed="left">
          <template slot-scope="scope">
            {{ scope.row.refoundNo }}
            <hb-copy :content="scope.row.refoundNo"></hb-copy>
            <p class="f-c9">所属批次号：{{ scope.row.batchOrderNo }}</p>
          </template>
        </el-table-column>
        <el-table-column label="退款人次" prop="refoundCount" min-width="120" align="center"> </el-table-column>
        <el-table-column label="实付金额(元)" width="140" align="right">
          <template slot-scope="scope">
            <p>{{ scope.row.payAmount || 0 }}</p>
          </template>
        </el-table-column>
        <el-table-column label="退款金额(元)" width="140" align="right">
          <template slot-scope="scope">
            <p>{{ scope.row.refoundAmount || 0 }}</p>
          </template>
        </el-table-column>
        <el-table-column label="购买人信息" min-width="240">
          <template slot-scope="scope">
            <p>姓名：{{ scope.row.buyerName }}</p>
            <p>账号：{{ scope.row.buyerAccount }}</p>
          </template>
        </el-table-column>
        <el-table-column label="申请时间" min-width="180">
          <template slot-scope="scope">
            <p>申请：{{ scope.row.refoundDate || '-' }}</p></template
          >
        </el-table-column>
        <el-table-column label="退款状态" min-width="130">
          <template slot-scope="scope">
            <div v-if="scope.row.refundStatus === 1">
              <el-badge is-dot type="primary" class="badge-status">退款审批中</el-badge>
            </div>
            <div v-else-if="scope.row.refundStatus === 2">
              <el-badge is-dot type="primary" class="badge-status">退款处理中</el-badge>
            </div>
            <div v-else-if="scope.row.refundStatus === 3">
              <el-badge is-dot type="success" class="badge-status">退款成功</el-badge>
            </div>
            <div v-else-if="scope.row.refundStatus === 4">
              <el-badge is-dot type="danger" class="badge-status">退款失败</el-badge>
            </div>
            <div v-else-if="scope.row.refundStatus === 5">
              <el-badge is-dot type="danger" class="badge-status">拒绝退款</el-badge>
            </div>
            <div v-else-if="scope.row.refundStatus === 6">
              <el-badge is-dot type="danger" class="badge-status">已取消</el-badge>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="退款时间" min-width="180">
          <template slot-scope="scope">{{ scope.row.refoundSuccessDate || '-' }}</template>
        </el-table-column>
        <el-table-column label="操作" width="140" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="mini" @click="goDetail(scope.row.refoundNo)">详情</el-button>
            <el-button type="text" size="mini" v-if="scope.row.refundStatus === 1" @click="openCancelRefund(scope.row)"
              >取消退款</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <el-drawer title="取消退款申请" :visible.sync="cancelRefundDialog" size="800px" custom-class="m-drawer">
        <div class="drawer-bd">
          <el-alert type="warning" show-icon :closable="false" class="m-alert">
            确认取消该订单的退款申请？取消后需要重新发起退款！
          </el-alert>
          <el-row type="flex" justify="center">
            <el-col :span="18">
              <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt20">
                <el-form-item label="取消原因：" required>
                  <el-input type="textarea" :rows="6" v-model="cancelReason" placeholder="请输入取消原因" />
                </el-form-item>
                <el-form-item class="m-btn-bar">
                  <el-button @click="cancelRefundDialog = false">取消</el-button>
                  <el-button type="primary" @click="cancelRefund">确定</el-button>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-drawer>
    </el-card>
  </div>
</template>

<script lang="ts">
  import QueryBatchRefundListParamVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchRefundListParamVo'
  import { Component, Prop, Ref, Vue, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import {
    BatchReturnOrderSortField,
    BatchReturnOrderSortRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import { SortPolicy } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
  import BatchRefoundListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchRefoundListDetailVo'
  import QueryBatchRefundList from '@api/service/management/trade/batch/order/query/QueryBatchRefund'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import MutationBatchOrderRefund from '@api/service/management/trade/batch/order/mutation/MutationBatchOrderRefund'
  import { bind, debounce } from 'lodash-decorators'

  @Component({
    components: {
      DoubleDatePicker
    }
  })
  export default class extends Vue {
    @Ref('returnOrderRef') returnOrderRef: any

    @Prop({
      type: String,
      default: ''
    })
    userId: string

    input = ''
    select = ''
    form = {
      data1: ''
    }
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    // 页面分页控件
    page: UiPage
    // 分页查询
    query: Query = new Query()
    //接口查询参数
    returnOrderRequestVo: QueryBatchRefundListParamVo = new QueryBatchRefundListParamVo()
    //排序入参
    sortRequest: Array<BatchReturnOrderSortRequest> = new Array<BatchReturnOrderSortRequest>()
    //查询接口结果
    returnOrderResponseVo: Array<BatchRefoundListDetailVo> = new Array<BatchRefoundListDetailVo>()
    //查询接口请求
    queryRefundOrder: QueryBatchRefundList =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchRefound
    //取消退款弹窗标识
    cancelRefundDialog = false
    //拒绝退款弹窗标识
    refuseRefundDialog = false
    //退款原因
    cancelReason = ''
    //退款当前项数据
    currentItem: BatchRefoundListDetailVo = new BatchRefoundListDetailVo()
    //业务接口请求
    mutationBatchOrderRefund: MutationBatchOrderRefund =
      TradeModule.batchTradeBatchFactor.orderFactor.mutationOrderFactor.mutationBatchOrderRefund
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
      this.page.pageNo = 1
      this.page.pageSize = 10
    }
    /**
     * 页面初始化
     */
    async created() {
      // 给表格内滚动条滚动增加监听事件
      await this.$nextTick(async () => {
        const element = this.returnOrderRef.bodyWrapper
        element.addEventListener('scroll', this.infiniteScroll)
      })
      await this.doQueryPage()
    }

    @Watch('userId')
    async userIdChange(id: string) {
      await this.search()
    }
    /**
     * 【换班记录】无限加载
     */
    @bind
    @debounce(200)
    async infiniteScroll() {
      const element = this.returnOrderRef.bodyWrapper
      const scrollDistance = element.scrollHeight - element.scrollTop - element.clientHeight
      if (scrollDistance <= 0) {
        if (this.returnOrderResponseVo.length >= this.page.totalSize) {
          // this.$message.warning('没有更多数据')
        } else {
          this.page.pageNo++
          await this.doQueryPage()
        }
      }
    }
    async resetParams() {
      this.page.pageNo = 1
      this.page.pageSize = 10
      this.returnOrderRequestVo = new QueryBatchRefundListParamVo()
      await this.doQueryPage()
    }
    async doQueryPage() {
      if (!this.userId) {
        this.returnOrderResponseVo = []
        return
      }
      this.query.loading = true
      this.sortRequest = []
      try {
        const item = new BatchReturnOrderSortRequest()
        item.field = BatchReturnOrderSortField.CREATED_TIME
        item.policy = SortPolicy.DESC
        this.sortRequest.push(item)
        const origin = this.returnOrderResponseVo.slice()
        this.returnOrderRequestVo.createdUserId = this.userId ? [this.userId] : []
        const result = await this.queryRefundOrder.queryBatchRefoundList(
          this.page,
          this.returnOrderRequestVo,
          this.sortRequest
        )
        this.returnOrderResponseVo = [...origin, ...result]
      } catch (e) {
        console.log(e, '加载个人报名退款订单失败')
      } finally {
        ;(this.$refs['returnOrderRef'] as any)?.doLayout()
        this.queryRefundOrder.totalRefoundCount

        this.query.loading = false
      }
    }
    goDetail(id: string) {
      this.$router.push('/training/trade/refund/collective/detail/' + id)
    }
    /*
     * 取消退款弹窗
     * */
    openCancelRefund(item: BatchRefoundListDetailVo) {
      this.currentItem = item
      this.cancelReason = ''
      this.cancelRefundDialog = true
    }
    /*
     * 取消退款
     * */
    @bind
    @debounce(200)
    async cancelRefund() {
      if (!this.cancelReason) {
        this.$message.error('请填写取消退款原因')
        return
      }
      this.query.loading = true
      //this.cancelReason  待放入

      const status = await this.mutationBatchOrderRefund.cancelRefund(this.currentItem.refoundNo, this.cancelReason)
      if (status?.code == '200') {
        this.query.loading = false
        this.cancelRefundDialog = false
        this.$message.success('取消退款成功')
        await this.doQueryPage()
      } else {
        this.query.loading = false
        this.$message.error('取消退款失败')
        return
      }
    }
    async search() {
      this.page.pageNo = 1
      this.returnOrderResponseVo = new Array<BatchRefoundListDetailVo>()
      await this.doQueryPage()
    }
  }
</script>
