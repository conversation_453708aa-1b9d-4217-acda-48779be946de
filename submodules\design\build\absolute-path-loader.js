/**
 * 绝对路径处理Loader
 */
const gitInfo = require('../.cache/git-info.json')
let baseUrl = '/'
if (process.env.NODE_ENV === 'production') {
  let type = 'web'
  if (gitInfo.slug.includes('uniapp')) {
    type = 'uniapp'
  } else if (gitInfo.slug.includes('customer')) {
    type = 'customer'
  }
  baseUrl =
    'https://hbfe-apps.dev.59iedu.com:8443/' +
    'design/' +
    gitInfo.project.toUpperCase() +
    '/' +
    type +
    '/' +
    gitInfo.branch
}
module.exports = function(source) {
  // 保持loader的异步特性
  const callback = this.async();

  // 匹配各种可能的绝对路径引用方式
  // 例如：在import语句、url()、src=""、href=""等中的绝对路径
  const absolutePathRegex = /(?<=(import\s+['"]|from\s+['"]|url\(\s*['"]?|src=["']|href=["']|require\(['"]|@import\s+['"]|background(-image)?:\s*url\(\s*['"]?))\/(?!\/|test\/test)([\w\d._/-]+)(?=['")]|\))/g;

  // 匹配Vue组件的preview-src-list属性，考虑属性值有双引号的情况
  const previewSrcListRegex = /:preview-src-list\s*=\s*"(\[\s*(?:['"][^'"]*['"](?:\s*,\s*)?)+\s*\])"/g;

  // 先处理常规的绝对路径
  let modifiedSource = source.replace(absolutePathRegex, (match) => {
    // 避免重复添加前缀
    if (match.startsWith(baseUrl)) {
      return match;
    }
    return baseUrl + match;
  });

  // 处理preview-src-list中的路径
  modifiedSource = modifiedSource.replace(previewSrcListRegex, (fullMatch, arrayContent) => {
    // 处理数组中的每个路径字符串
    const processedArray = arrayContent.replace(/(['"])(\/?[^'"]+)(['"])/g, (pathMatch, openQuote, path, closeQuote) => {
      if (path.startsWith('/') && !path.startsWith(baseUrl)) {
        return openQuote + baseUrl+'/' + path.substring(1) + closeQuote;
      }
      return pathMatch;
    });

    // 重建完整的属性
    return `:preview-src-list="${processedArray}"`;
  });

  // 返回修改后的内容
  callback(null, modifiedSource);
};
