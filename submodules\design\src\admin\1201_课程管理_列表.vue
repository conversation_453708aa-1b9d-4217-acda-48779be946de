<template>
  <el-main>
    <div class="f-p15">
      <div class="f-mb15">
        <el-button type="primary" icon="el-icon-plus">新建课程</el-button>
        <el-button type="primary" plain>课程分类管理</el-button>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="课程名称">
                <el-input v-model="input" clearable placeholder="请输入课程名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="转换状态">
                <el-select v-model="select" clearable placeholder="请选择转换状态">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="课程状态">
                <el-select v-model="select" clearable placeholder="请选择课件状态">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="课程分类">
                <el-cascader clearable filterable multiple :options="cascader" placeholder="请选择课件分类" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="课程名称" min-width="280" fixed="left">
            <template>课程名称课程名称课程名称课程名称</template>
          </el-table-column>
          <el-table-column label="转换状态" min-width="120">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-badge is-dot type="primary" class="badge-status">转换中</el-badge>
              </div>
              <div v-else-if="scope.$index === 1">
                <el-badge is-dot type="danger" class="badge-status">转换失败</el-badge>
              </div>
              <div v-else>
                <el-badge is-dot type="success" class="badge-status">转换成功</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="课程状态" min-width="100">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-tag type="info">停用</el-tag>
              </div>
              <div v-else>
                <el-tag type="success">正常</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="学时" min-width="120" align="center">
            <template>12</template>
          </el-table-column>
          <el-table-column label="综合评价" min-width="180" align="center">
            <template>
              <el-rate v-model="rate" disabled show-score text-color="#ff9900" score-template="4.5" />
            </template>
          </el-table-column>
          <el-table-column label="试题数量" min-width="120" align="center">
            <template>34</template>
          </el-table-column>
          <el-table-column label="创建时间" min-width="180">
            <template>2020-11-11 12:20:20</template>
          </el-table-column>
          <el-table-column label="操作" width="240" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">详情</el-button>
              <el-button type="text" size="mini">预览</el-button>
              <el-button type="text" size="mini">修改</el-button>
              <el-button type="text" size="mini">停用</el-button>
              <el-button type="text" size="mini">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        >
        </el-pagination>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        rate: '4.5',
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
