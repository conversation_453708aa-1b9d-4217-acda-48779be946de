import { TrainingChannelOnlineCollectiveSignUpSettingResponse } from '@api/platform-gateway/platform-training-channel-back-gateway'

export default class OnlineCollectiveInfo {
  id: string
  /**
   * 线上集体报名入口是否
   */
  onlineCollectiveEntry = false
  /**
   * 线上集团报名入口图片是否
   */
  onlineCollectiveEntryImage = false
  /**
   * 线上集团报名入口图片链接
   */
  onlineCollectiveEntryImageLink = ''
  /**
   * 线上集体报名模板
   */
  onlineCollectiveEntryTemplate: { name?: string; url?: string } = null
  /**
   * 班级报名链接
   */
  signUpClassUrl = `${window.location.origin}/manage/view-train-class`
  static from(dto: TrainingChannelOnlineCollectiveSignUpSettingResponse) {
    const vo = new OnlineCollectiveInfo()
    vo.id = dto.id
    vo.onlineCollectiveEntry = dto.openEntrySwitch
    vo.onlineCollectiveEntryImage = dto.openEntryPictureSwitch
    vo.onlineCollectiveEntryImageLink = dto.entryPictureAttachments[0]?.url
    vo.onlineCollectiveEntryTemplate = dto.templateAttachment
    vo.signUpClassUrl = dto.showSignUpClassUrl
    return vo
  }
}
