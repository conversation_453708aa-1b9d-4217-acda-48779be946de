import CreateCourse from '@api/service/management/resource/course/mutation/vo/CreateCourse'
import CreateCourseDto from '@api/service/management/resource/course/mutation/dto/CreateCourse'
import { ResponseStatus } from '@hbfe/common'

class MutationCreateCourse {
  createCourse: CreateCourse = new CreateCourse()

  async doCreate(): Promise<ResponseStatus> {
    const status = await CreateCourseDto.from(this.createCourse).save()
    return new ResponseStatus(status.code, status.message)
  }
}

export default MutationCreateCourse
