/**
 * @description:  方法级别错误拦截装饰器。 后续需要部署特定yichang
 * @param {function} errorHandler 抛出异常后回调函数
 * @return {*}
 */
const errorProxy = (errorHandler?: (error?: Error) => void) => (
  target: any,
  propertyKey: string,
  descriptor: PropertyDescriptor
) => {
  const func = descriptor.value
  return {
    get() {
      return (...args: any[]) => {
        return Promise.resolve(func.apply(this, args)).catch(error => {
          return errorHandler && errorHandler(error)
        })
      }
    },
    set(newValue: any) {
      return newValue
    }
  }
}

export default errorProxy
