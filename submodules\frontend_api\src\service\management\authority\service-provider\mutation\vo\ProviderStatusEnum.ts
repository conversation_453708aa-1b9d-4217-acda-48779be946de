import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum ProviderStatusEnum {
  /**
   * 启用
   */
  enable = 1,
  /**
   * 停用
   */
  disable
}

class ProviderStatus extends AbstractEnum<ProviderStatusEnum> {
  static enum = ProviderStatusEnum

  constructor(status?: ProviderStatusEnum) {
    super()
    this.current = status
    this.map.set(ProviderStatusEnum.disable, '停用')
    this.map.set(ProviderStatusEnum.enable, '启用')
  }
}

export default ProviderStatus
