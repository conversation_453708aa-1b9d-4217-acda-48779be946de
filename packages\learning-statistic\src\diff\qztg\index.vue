<route-meta>
{
"isMenu": true,
"title": "学员学习明细",
"sort": 5,
"icon": "icon-mingxi"
}
</route-meta>
<template>
  <QztgLearningStatistic>
    <template #sale-channel="{ localSkuProperty }">
      <el-form-item label="销售渠道" v-if="!isHaveZtRole">
        <biz-sale-channel-select v-model="localSkuProperty.tradeChannels"></biz-sale-channel-select>
      </el-form-item>
    </template>
  </QztgLearningStatistic>
</template>
<script lang="ts">
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  import QueryStudentLearningList from '@api/service/diff/management/qztg/statisticalReport/query/QueryStudentLearningList'
  import LearningStatistic from '@hbfe/jxjy-admin-learningStatistic/src/index.vue'
  import bizSaleChannelSelect from '@hbfe/jxjy-admin-components//src/biz-sale-channel-select.vue'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import { StudentSchemeLearningRequestVo } from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-learningStatistic/src/models'
  class NewSchemeSkuProperty extends SchemeSkuProperty {
    tradeChannels: number = null
  }
  @Component
  class QztgLearningStatistic extends LearningStatistic {
    /**
     * 查询参数
     */
    filter: StudentSchemeLearningRequestVo = new StudentSchemeLearningRequestVo()
    /**
     * 本地sku属性
     */
    localSkuProperty = new NewSchemeSkuProperty()

    /**
     * 查询列表——分销商
     */
    async pageStudentSchemeLearningInDistributor() {
      this.setFilter()
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningList()
        .pageStudentSchemeLearningInDistributor(this.page, this.filter)
    }

    /**
     * 学员学习统计列表--专题管理员
     */
    async listRegionLearningReportFormsInTrainingChannel() {
      this.setFilter()
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningList()
        .listRegionLearningReportFormsInTrainingChannel(this.page, this.filter)
    }

    /**
     * 学员学习统计列表
     */
    async listRegionLearningReportFormsInServicer() {
      this.setFilter()
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningList()
        .listRegionLearningReportFormsInServicer(this.page, this.filter)
    }
    /**
     * 设置过滤条件
     */
    setFilter() {
      this.filter.learningRegister.saleChannels = this.localSkuProperty.tradeChannels
        ? [this.localSkuProperty.tradeChannels]
        : undefined
    }
    /**
     * 超级管理员导出
     */
    async exportExcel() {
      this.setFilter()
      const queryStudentLearningList = new QueryStudentLearningList()
      return await queryStudentLearningList.exportExcel(this.filter)
    }
  }
  @Component({
    components: {
      QztgLearningStatistic,
      bizSaleChannelSelect
    }
  })
  export default class extends Vue {
    /**
     * 判断当前用户是否专题管理员角色类型
     */
    get isHaveZtRole() {
      return QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    }
  }
</script>
