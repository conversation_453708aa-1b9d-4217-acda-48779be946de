import ShortCode from '@api/service/common/common/short-code'
import { ApplyShortLinkRequest } from '@api/ms-gateway/ms-shortcode-v1'

/**
 * @description 加解密工具类
 */
class CryptUtil {
  /**
   * 加密
   * @param obj 待加密对象
   * @returns 加密字符串，若异常返回空字符串
   */
  async encrypt<T = any>(obj: T): Promise<string> {
    try {
      const params = new ApplyShortLinkRequest()
      params.metadata = new Map<string, string>()
      params.metadata['data'] = JSON.stringify(obj)
      const { status, data } = await ShortCode.applyShortLink(params)
      if (status && status.isSuccess() && data) {
        return data.shortCode
      } else {
        return ''
      }
    } catch (e) {
      return ''
    }
  }

  /**
   * 解密
   * @param str 加密字符串
   * @returns 解密对象，若异常返回null
   */
  async decrypt<T = any>(str: string): Promise<T> {
    try {
      const { status, data } = await ShortCode.getShortCode(str)
      if (status && status.isSuccess() && data && data.metadata) {
        return JSON.parse(data.metadata['data']) as T
      } else {
        return null
      }
    } catch (e) {
      return null
    }
  }

  /**
   * 加密（字符串）
   * @param str 待加密字符串
   * @returns 加密字符串，若异常返回空字符串
   */
  async encryptStr(str: string): Promise<string> {
    try {
      const params = new ApplyShortLinkRequest()
      params.metadata = new Map<string, string>()
      params.metadata['data'] = str
      const { status, data } = await ShortCode.applyShortLink(params)
      if (status && status.isSuccess() && data) {
        return data.shortCode
      } else {
        return ''
      }
    } catch (e) {
      return ''
    }
  }

  /**
   * 解密（字符串）
   * @param str 加密字符串
   * @returns 解密对象，若异常返回null
   */
  async decryptStr(str: string): Promise<string> {
    try {
      const { status, data } = await ShortCode.getShortCode(str)
      if (status && status.isSuccess() && data && data.metadata) {
        return data.metadata['data']
      } else {
        return null
      }
    } catch (e) {
      return null
    }
  }
}
export default new CryptUtil()
