schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出调查问卷内问答试题作答内容(问答文本
		@return
	"""
	exportAnswerAskQuestionExcelInServicer(request:AnswerAskQuestionRequest):Boolean!
	"""导出调查问卷内选项试题作答内容"""
	exportAnswerPaperAnswerQuestionExcelInServicer(request:AnswerPaperAnswerQuestionRequest):Boolean!
	"""打印调查问卷试题
		@param request
		@return
	"""
	exportAnswerQuestionPdfInServicer(request:AnswerQuestionPdfRequest):Boolean!
}
input ObsFileMetaData @type(value:"com.fjhb.ms.data.export.commons.utils.async.ObsFileMetaData") {
	bizType:String
	owner:String
	sign:String
}
input AnswerAskQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.answerpaper.AnswerAskQuestionRequest") {
	"""调查问卷id"""
	questionnaireId:String
	"""试题id"""
	questionId:String
	jobName:String
	metaData:ObsFileMetaData
}
input AnswerPaperAnswerQuestionRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.answerpaper.AnswerPaperAnswerQuestionRequest") {
	"""调查问卷id"""
	questionnaireId:String
	jobName:String
	metaData:ObsFileMetaData
}
input AnswerQuestionPdfRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.answerpaper.AnswerQuestionPdfRequest") {
	"""调查问卷id"""
	questionnaireId:String
	jobName:String
	metaData:ObsFileMetaData
}

scalar List
