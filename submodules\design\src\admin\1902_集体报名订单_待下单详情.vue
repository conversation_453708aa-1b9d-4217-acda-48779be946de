<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">集体报名订单</el-breadcrumb-item>
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <el-alert type="warning" :closable="false" class="m-alert is-border-bottom">
      <p>温馨提示：</p>
      <p>1. 需为学员缴费，请先创建报名批次，再选择对应批次缴费的人员及培训班；</p>
      <p>2. 平台提供批量导班，需先下载导入模版再提交，导入任务成功的记录会直接显示在报名批次中；</p>
      <p>3. 报名批次提交后，请在 24小时 内进行缴费，超过时间未缴则系统将默认关闭批次，需重新创建批次进行缴费。</p>
    </el-alert>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header m-order-info f-mb15">
        <div class="f-flex-sub f-plr20 f-pt10">
          <div class="m-tit">
            <span class="tit-txt">批次信息</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="24">
              <el-form-item label="报名批次单号：">batch211202103521749006690002</el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="批次状态："><el-tag size="small">下单中</el-tag></el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="缴费人次：">1</el-form-item>
            </el-col>
            <el-col :span="5">
              <el-form-item label="实付金额（元）：">0</el-form-item>
            </el-col>
          </el-form>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="f-flex-sub f-plr20 f-pt10">
          <div class="m-tit is-small">
            <span class="tit-txt">当前批次状态</span>
          </div>
          <!--条件查询-->
          <el-row :gutter="16" class="m-query f-pt10">
            <el-form :inline="true" label-width="auto">
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="身份证号">
                  <el-input v-model="input" clearable placeholder="请输入身份证号" />
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6">
                <el-form-item label="培训班">
                  <el-select v-model="select" clearable filterable placeholder="请选择培训班">
                    <el-option value="选项1"></el-option>
                    <el-option value="选项2"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :sm="12" :md="8" :xl="6" class="f-fr">
                <el-form-item class="f-tr">
                  <el-button type="primary">查询</el-button>
                  <el-button>查看导入任务</el-button>
                  <el-button>重置</el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <!--表格-->
          <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt5">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="学员信息" min-width="250" fixed="left">
              <template>
                <p>姓名：张依依</p>
                <p>手机号：15659135119</p>
                <p>身份证号：350103199001011234</p>
              </template>
            </el-table-column>
            <el-table-column label="培训方案名称" min-width="300">
              <template>培训方案名称培训方案名称</template>
            </el-table-column>
            <el-table-column label="属性" min-width="240">
              <template>
                <p>行业：专技行业</p>
                <p>科目类型：科目类型</p>
              </template>
            </el-table-column>
            <el-table-column label="学时" min-width="140" align="center">
              <template>20</template>
            </el-table-column>
            <el-table-column label="价格(元)" width="200" align="right">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">3.15</div>
                <div v-else-if="scope.$index === 1">52.36</div>
                <div v-else>158.15</div>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination>
        </div>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
