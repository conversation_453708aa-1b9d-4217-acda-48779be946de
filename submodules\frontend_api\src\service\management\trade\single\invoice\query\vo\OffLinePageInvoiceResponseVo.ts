/*
 * @Description: 列表转换数据
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-19 08:39:20
 */

import {
  InvoiceFaceInfoResponse,
  OfflineDeliveryRecord,
  OfflineInvoiceDeliveryInfoResponse,
  OfflineInvoiceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { InvoiceCategoryEnum, InvoiceStatusEnum, OrderReturnStatusEnum, TitleTypeEnum } from '../../enum/InvoiceEnum'
export default class OffLinePageInvoiceResponseVo {
  /**
   * 发票ID
   */
  invoiceId?: string
  /**
   * 订单号
   */
  associationId?: string
  /**
   * 发票类型 PLAININVOICE:普通发票 VATPLAININVOICE:增值税普通发票 VATSPECIALPLAININVOICE:增值税专用发票
   */
  invoiceCategory?: InvoiceCategoryEnum
  /**
   * 订单退货状态
   *  0:未退货 1：退货中 2：退货成功
   */
  orderReturnStatus?: OrderReturnStatusEnum
  /**
   * 付款金额
   */
  payAmount?: number
  /**
   * 开票金额
   */
  totalAmount?: number
  /**
   * 税额
   */
  totalTax?: number
  /**
   * 用户ID
   */
  userId?: string
  /**
   * 姓名
   */
  name?: string
  /**
   * 登录账号
   */
  loginAccount?: string
  /**
   * 身份证
   */
  idCard?: string
  /**
   * 发票抬头类型  1：个人  2：企业
   */
  titleType?: TitleTypeEnum
  /**
   * 发票抬头
   */
  title?: string
  /**
   * 统一社会信用代码
   */
  taxpayerNo?: string

  /**
   * 申请开票时间
   */
  applyForDate?: string

  /**
   * 发票状态 0:未开具 1：开票中 2：开票成功 3：开票失败
   */
  invoiceStatus?: InvoiceStatusEnum
  /**
   * 开票时间
   */
  invoiceDate?: string
  /**
   * 发票号
   */
  invoiceNo?: string
  /**
   * 邮箱（用户信息使用）
   */
  email?: string
  /**
   * 手机号（用户信息使用）
   */
  phone?: string
  /**
   * 发票冻结状态
   */
  invoiceFreezeStatus?: boolean
  /**
   * 发票票面信息
   */
  invoiceFaceInfo?: InvoiceFaceInfoResponse
  /**
   * 购买方地址
   */
  address?: string
  /**
   * 购买方开户行名称
   */
  bankName?: string
  /**
   * 购买方注册电话
   */
  rePhone?: string
  /**
   * 购买方银行账户
   */
  account?: string
  /**
   * 购买方营业执照url
   */
  businessLicenseUrl?: string
  /**
   * 购买方许可证url
   */
  permitUrl?: string
  /**
   * 发票票面备注
   */
  remark?: string

  /**
   * 发票配送信息
   */
  deliveryInfo: OfflineInvoiceDeliveryInfoResponse
  /**
   * 联系电子邮箱
   */
  contactEmail: string
  /**
   * 联系电话
   */
  contactPhone: string
  /**
   * 发票配送记录
   */
  deliveryRecordList: Array<OfflineDeliveryRecord>
  /*********配送打平***********/
  /**
   * 配送状态
@see OfflineDeliveryStatus
   */
  deliveryStatus: number
  /**
   * 未就绪 配送状态变更时间记录
   */
  unReady: string
  /**
   * 已就绪 配送状态变更时间记录
   */
  ready: string
  /**
   * 已配送 配送状态变更时间记录
   */
  shipped: string
  /**
   * 已自取 配送状态变更时间记录
   */
  taken: string
  /**
   * 配送方式
   */
  shippingMethod: number
  /**
   * 收件人  配送地址信息
   */
  consignee: string
  /**
   * 手机号   配送地址信息
   */
  deliveryphone: string
  /**
   * 所在物理地区   配送地址信息
   */
  deliveryRegion: string
  /**
   * 详细地址   配送地址信息
   */
  deliveryAddress: string
  /**
   * 领取地点   自取点信息
   */
  takePointPickupLocation: string
  /**
   * 领取时间   自取点信息
   */
  takePointPickupTime: string
  /**
   * 备注   自取点信息
   */
  takePointRemark: string
  /**
   * 快递公司名称   快递信息
   */
  expressCompanyName: string
  /**
   * 快递单号   快递信息
   */
  expressNo: string
  /**
   * 领取人 自取信息
   */
  takePerson: string
  /**
   * 手机号  自取信息
   */
  takePhone: string
  /**
   * 发票类型
   */
  invoiceType?: number
  /**
   * 渠道类型
   */
  saleChannel?: number = undefined
  /**
   * 是否作废
   */
  useless?: boolean = undefined
  /**
   * 发票信息校验策略
   */
  invoiceVerifyStrategy?: number = undefined
  /*********配送打平***********/
  /**
   * 转换
   * @param offlineInvoiceResponse
   * @returns
   */
  static from(offlineInvoiceResponse: OfflineInvoiceResponse) {
    const offLinePageInvoiceResponseVo = new OffLinePageInvoiceResponseVo()
    offLinePageInvoiceResponseVo.invoiceId = offlineInvoiceResponse?.offlineInvoiceId
    offLinePageInvoiceResponseVo.saleChannel = offlineInvoiceResponse?.associationInfo?.saleChannel
    offLinePageInvoiceResponseVo.associationId = offlineInvoiceResponse?.associationInfo?.associationId
    offLinePageInvoiceResponseVo.invoiceCategory = offlineInvoiceResponse?.basicData?.invoiceCategory
    offLinePageInvoiceResponseVo.orderReturnStatus = offlineInvoiceResponse?.associationInfo?.orderReturnStatus
    offLinePageInvoiceResponseVo.payAmount = offlineInvoiceResponse?.associationInfo?.payAmount
    offLinePageInvoiceResponseVo.totalAmount = offlineInvoiceResponse?.basicData?.amount
    offLinePageInvoiceResponseVo.useless = offlineInvoiceResponse?.basicData?.invoiceStatus == 2
    // 个人线下没有税额 集体有
    offLinePageInvoiceResponseVo.userId = offlineInvoiceResponse?.associationInfo?.buyer?.userId
    offLinePageInvoiceResponseVo.titleType = offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.titleType
    offLinePageInvoiceResponseVo.title = offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.title
    offLinePageInvoiceResponseVo.taxpayerNo = offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.taxpayerNo
    offLinePageInvoiceResponseVo.applyForDate = offlineInvoiceResponse?.basicData?.invoiceStatusChangeTime?.normal
    offLinePageInvoiceResponseVo.invoiceStatus = offlineInvoiceResponse?.basicData?.billStatus
    offLinePageInvoiceResponseVo.invoiceDate = offlineInvoiceResponse?.basicData?.billStatusChangeTime?.success
    offLinePageInvoiceResponseVo.invoiceNo = offlineInvoiceResponse?.basicData?.invoiceNoList?.join('/')
    offLinePageInvoiceResponseVo.invoiceFaceInfo = offlineInvoiceResponse?.basicData?.invoiceFaceInfo
    offLinePageInvoiceResponseVo.invoiceFreezeStatus = offlineInvoiceResponse?.basicData?.freeze
    offLinePageInvoiceResponseVo.address = offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.address
    offLinePageInvoiceResponseVo.bankName = offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.bankName
    offLinePageInvoiceResponseVo.rePhone = offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.phone
    offLinePageInvoiceResponseVo.contactPhone = offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.contactPhone
    offLinePageInvoiceResponseVo.contactEmail = offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.contactEmail
    offLinePageInvoiceResponseVo.email = offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.email

    offLinePageInvoiceResponseVo.account = offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.account
    offLinePageInvoiceResponseVo.businessLicenseUrl =
      offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.businessLicensePath
    offLinePageInvoiceResponseVo.permitUrl =
      offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.accountOpeningLicensePath
    offLinePageInvoiceResponseVo.remark = offlineInvoiceResponse?.basicData?.invoiceFaceInfo?.remark
    offLinePageInvoiceResponseVo.deliveryInfo = offlineInvoiceResponse?.deliveryInfo
    offLinePageInvoiceResponseVo.deliveryStatus = offlineInvoiceResponse?.deliveryInfo?.deliveryStatus
    offLinePageInvoiceResponseVo.unReady = offlineInvoiceResponse?.deliveryInfo?.deliveryStatusChangeTime?.unReady
    offLinePageInvoiceResponseVo.ready = offlineInvoiceResponse?.deliveryInfo?.deliveryStatusChangeTime?.ready
    offLinePageInvoiceResponseVo.shipped = offlineInvoiceResponse?.deliveryInfo?.deliveryStatusChangeTime?.shipped
    offLinePageInvoiceResponseVo.taken = offlineInvoiceResponse?.deliveryInfo?.deliveryStatusChangeTime?.taken
    offLinePageInvoiceResponseVo.shippingMethod = offlineInvoiceResponse?.deliveryInfo?.shippingMethod
    offLinePageInvoiceResponseVo.consignee = offlineInvoiceResponse?.deliveryInfo?.deliveryAddress?.consignee
    offLinePageInvoiceResponseVo.deliveryphone = offlineInvoiceResponse?.deliveryInfo?.deliveryAddress?.phone
    offLinePageInvoiceResponseVo.deliveryRegion = offlineInvoiceResponse?.deliveryInfo?.deliveryAddress?.region
    offLinePageInvoiceResponseVo.deliveryAddress = offlineInvoiceResponse?.deliveryInfo?.deliveryAddress?.address
    offLinePageInvoiceResponseVo.takePointPickupLocation =
      offlineInvoiceResponse?.deliveryInfo?.takePoint?.pickupLocation
    offLinePageInvoiceResponseVo.takePointPickupTime = offlineInvoiceResponse?.deliveryInfo?.takePoint?.pickupTime
    offLinePageInvoiceResponseVo.takePointRemark = offlineInvoiceResponse?.deliveryInfo?.takePoint?.remark
    offLinePageInvoiceResponseVo.expressCompanyName = offlineInvoiceResponse?.deliveryInfo?.express?.expressCompanyName
    offLinePageInvoiceResponseVo.expressNo = offlineInvoiceResponse?.deliveryInfo?.express?.expressNo
    offLinePageInvoiceResponseVo.takePerson = offlineInvoiceResponse?.deliveryInfo?.takeResult?.takePerson
    offLinePageInvoiceResponseVo.takePhone = offlineInvoiceResponse?.deliveryInfo?.takeResult?.phone
    offLinePageInvoiceResponseVo.deliveryRecordList = offlineInvoiceResponse?.deliveryRecordList
    offLinePageInvoiceResponseVo.invoiceType = offlineInvoiceResponse?.basicData?.invoiceType
    return offLinePageInvoiceResponseVo
  }
  setUserInfo(name: string, idCard: string, phone: string, email: string, loginAccount?: string) {
    this.name = name
    this.idCard = idCard
    this.phone = phone
    this.email = email
    this.loginAccount = loginAccount
  }
}
