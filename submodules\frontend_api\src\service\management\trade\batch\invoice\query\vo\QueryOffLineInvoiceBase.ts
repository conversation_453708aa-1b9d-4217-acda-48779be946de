import TradeQueryBackstage, {
  OfflineInvoiceResponse,
  OfflineInvoiceSortRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { Page } from '@hbfe/common'
import QueryOffLinePageInvoiceParam from '@api/service/management/trade/batch/invoice/query/vo/QueryOffLinePageInvoiceParam'
import OffLinePageInvoiceVo from '@api/service/management/trade/batch/invoice/query/vo/OffLinePageInvoiceResponseVo'
import UserModule from '@api/service/management/user/UserModule'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { getOfflineInvoiceInServicer } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage/graphql-importer'
import CollectiveManagerInfoVo from '@api/service/management/user/query/manager/vo/CollectiveManagerInfoVo'
import OperationLogItem from '@api/service/management/trade/batch/invoice/query/vo/OperationLogItem'

export default abstract class QueryOffLineInvoiceBase {
  /**
   * 分页电子查询发票 【增值税电子普通发票】
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<offLinePageInvoiceVo>
   */
  abstract offLinePageInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest>
  ): Promise<Array<OffLinePageInvoiceVo>>

  /**
   * 集体线下发票导出
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  abstract offLinePageInvoiceInExport(queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam): Promise<boolean>

  /**
   * 分页专票查询发票 【增值税专用发票（纸质票）】
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  abstract offLinePageVatspecialplaInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest>
  ): Promise<Array<OffLinePageInvoiceVo>>

  /**
   * 分页专票查询发票 【增值税专用发票（电子）】
   * @param page 页数
   * @param QueryOffLinePageInvoiceParam 查询参数
   * @param sort 根据创建时间进行排序
   * @returns  Array<OffLinePageInvoiceVo>
   */
  abstract offLinePageElectVatspecialplaInvoiceInServicer(
    page: Page,
    queryOffLinePageInvoiceParam: QueryOffLinePageInvoiceParam,
    sort: Array<OfflineInvoiceSortRequest>
  ): Promise<Array<OffLinePageInvoiceVo>>

  /**
   * 集体线下发票导出 - 专票（纸质票）
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  abstract offLinePageVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean>

  /**
   * 集体线下发票导出 - 专票（电子票）
   * @param queryOffLinePageInvoiceParam
   * @returns
   */
  abstract offLinePageElectVatspecialplaInvoiceInExport(
    queryOffLinePageInvoiceParam?: QueryOffLinePageInvoiceParam
  ): Promise<boolean>

  /**
   * 查询发票详情
   * @param invoiceId 发票ID
   */
  async offLineGetInvoiceInServicer(invoiceId: string) {
    const result = await TradeQueryBackstage.getOfflineInvoiceInServicer(invoiceId)
    const data = OffLinePageInvoiceVo.from(result.data)
    return data
  }
  /**
   * 批量查询发票详情
   * @param invoiceId 发票ID
   */
  async offLineGetInvoiceInServicers(invoiceIds: string[]) {
    const reG = new RewriteGraph<OfflineInvoiceResponse, string>(
      TradeQueryBackstage._commonQuery,
      getOfflineInvoiceInServicer
    )
    await reG.request(invoiceIds)
    const map = new Map<string, OffLinePageInvoiceVo>()
    const invoiceListResponses = [...reG.itemMap.values()].map(item => {
      const temp = OffLinePageInvoiceVo.from(item)
      map.set(temp.invoiceId, temp)
      return temp
    })
    return map
  }
  /**
   * 获取用户信息
   * @param ids id数组
   */
  protected async getUserInfo(ids: Array<string>) {
    if (ids.length === 0) {
      return new Map()
    }
    // 根据用户ID获取用户信息
    const response = await UserModule.queryUserFactory.queryCollectiveManagerList.queryCollectiveManagerInfoList(ids)
    const userIdMap: Map<string, CollectiveManagerInfoVo> = new Map()
    response.forEach(item => {
      userIdMap.set(item.userId, item)
    })
    return userIdMap
  }

  /**
   * 查询记录 -- 线下和专票使用
   * @param offlineInvoiceId 发票ID
   */
  async listOfflineInvoiceOperationRecord(offlineInvoiceId: string) {
    const res = await TradeQueryBackstage.listOfflineInvoiceOperationRecord(offlineInvoiceId)
    const operationList = (res?.data?.length && res.data.map(OperationLogItem.from)) || []
    return operationList
  }
}
