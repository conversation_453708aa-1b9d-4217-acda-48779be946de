import CreateOrderCommodity from '@api/service/diff/customer/qztg/trade/single/mutation/vo/create-order/CreateOrderCommodity'
import UserModule from '@api/service/customer/user/UserModule'
import {
  QZTGCreateOrderRequest,
  Commodity,
  InvoiceInfoRequest,
  IssueInfo
} from '@api/diff-gateway/platform-qztg-school'
import AccommodationInformation from '@api/service/customer/trade/single/query/vo/AccommodationInformation'
import { LodgingTypeEnum } from '@api/service/common/implement/enums/LodgingTypeEnum'
export default class CreateOrderParams {
  /**
   * 买家编号
   */
  buyerId: string = undefined

  /**
   * 商品信息（主）
   */
  commodities: CreateOrderCommodity = new CreateOrderCommodity()

  /**
   * 合并的商品信息（主）
   */
  mergeCommodities: Array<CreateOrderCommodity> = new Array<CreateOrderCommodity>()

  /**
   * 购买渠道类型
   1-用户自主购买
   2-集体缴费
   3-管理员导入
   */
  purchaseChannelType: number = undefined

  /**
   * 终端类型
   <p>
   Web端：Web
   IOS端：IOS
   安卓端：Android
   微信小程序：WechatMini
   微信公众号：WechatOfficial
   */
  terminalCode: string = undefined

  // /**
  //  * 渠道商编号
  //  */
  // channelVendorId?: string

  /**
   * 是否需要发票
   */
  needInvoice: boolean

  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceInfoRequest = new InvoiceInfoRequest()

  /**
   * 参训单位id
   */
  participatingUnitId?: string

  /**
   * 销售渠道Id（自营渠道为空，专题渠道时必填）
   */
  saleChannelId?: string = undefined

  /**
   * 销售渠道类型
   0-自营渠道
   2-专题渠道
   */
  saleChannel: number = undefined

  /**
   * 购买来源类型，1-门户，2-专题
   @see com.fjhb.ms.order.v1.api.consts.PurchaseSourceType
   */
  purchaseSourceType?: number = undefined

  /**
   * 住宿采集信息
   */
  accommodationInformation: AccommodationInformation = new AccommodationInformation()

  /**
   * 转换成通用的下单模型
   */
  toMergeCreateOrderRequest() {
    const request = new QZTGCreateOrderRequest()
    request.buyerId = UserModule.queryUserFactory.getQueryUserInfo()?.userInfo.userInfo.userId
    request.mainCommodity = new Commodity()
    request.mainCommodity.quantity = this.commodities.quantity
    request.mainCommodity.skuId = this.commodities.skuId
    request.mainCommodity.enrolled = this.commodities.bought
    if (this.commodities.issueId) {
      request.mainCommodity.issueInfo = new IssueInfo()
      request.mainCommodity.issueInfo.issueId = this.commodities.issueId
      if (this.accommodationInformation.isAccommodation) {
        request.mainCommodity.issueInfo.accommodationType = this.accommodationInformation.accommodationType
      } else {
        request.mainCommodity.issueInfo.accommodationType = LodgingTypeEnum.noNeed
      }
    }
    // 合并
    request.mergedCommodities = this.mergeCommodities.map((item) => {
      const commodity = new Commodity()
      commodity.quantity = item.quantity
      commodity.skuId = item.skuId
      commodity.enrolled = item.bought
      return commodity
    })
    request.needInvoice = this.needInvoice
    if (this.needInvoice) {
      request.invoiceInfo = this.invoiceInfo
    }
    request.participatingUnitId = this.participatingUnitId
    request.purchaseChannelType = this.purchaseChannelType
    request.saleChannel = this.saleChannel
    request.saleChannelId = this.saleChannelId
    request.terminalCode = this.terminalCode

    return request
  }
}
