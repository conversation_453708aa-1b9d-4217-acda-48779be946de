import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformStatisticReportQuery'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformStatisticReportQuery'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum MonitorTypeEnum {
  STATIC = 'STATIC',
  STUDY = 'STUDY',
  EXAM = 'EXAM',
  BUY = 'BUY'
}
export enum SortByEnum {
  CHOOSE_TIMES = 'CHOOSE_TIMES',
  CREATE_TIME = 'CREATE_TIME'
}
export enum QueryParamSortEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum StudyState {
  WAIT_STUDY = 'WAIT_STUDY',
  STUDY = 'STUDY',
  STUDY_FINISH = 'STUDY_FINISH'
}
export enum ClientType {
  WEB = 'WEB',
  WX = 'WX',
  WXMP = 'WXMP',
  WXSB = 'WXSB'
}
export enum PaymentChannelTypeEnum {
  WEB = 'WEB',
  ANDROID = 'ANDROID',
  IOS = 'IOS',
  WECHAT_OFFICIAL_ACCOUNTS = 'WECHAT_OFFICIAL_ACCOUNTS',
  WECHAT_MINI_PROGRAMS = 'WECHAT_MINI_PROGRAMS',
  PRESENT = 'PRESENT',
  COLLECTIVE = 'COLLECTIVE',
  HTML5 = 'HTML5',
  DINGDING = 'DINGDING',
  CHANNEL_PRESENT_OPEN = 'CHANNEL_PRESENT_OPEN'
}
export enum TimeUnit {
  YEARS = 'YEARS',
  MONTHS = 'MONTHS',
  DAYS = 'DAYS',
  HOURS = 'HOURS'
}
export enum OrderStatus {
  WAITING_FOR_PAYMENT = 'WAITING_FOR_PAYMENT',
  WAITING_FOR_SELLER_AFFIRM = 'WAITING_FOR_SELLER_AFFIRM',
  PAY_IN = 'PAY_IN',
  PAY_SUCCESS = 'PAY_SUCCESS',
  DELIVERING = 'DELIVERING',
  DELIVERY_PART_FAIL = 'DELIVERY_PART_FAIL',
  DELIVERY_FAIL = 'DELIVERY_FAIL',
  DELIVER_COMPLETE = 'DELIVER_COMPLETE',
  TRADE_SUCCESS = 'TRADE_SUCCESS',
  TRADE_CLOSE = 'TRADE_CLOSE'
}
export enum QuestionType {
  SINGLE_SELECTION = 'SINGLE_SELECTION',
  MULTIPLE_SELECTION = 'MULTIPLE_SELECTION',
  JUDGEMENT = 'JUDGEMENT',
  BLANK_FILLING = 'BLANK_FILLING',
  ESSAY = 'ESSAY',
  COMPREHENSIVE = 'COMPREHENSIVE',
  SCALE = 'SCALE'
}

// 类

/**
 * 课程维度评价统计查询条件
Author:FangKunSen
Time:2021-05-07,10:55
 */
export class CourseAppraiseStatisticRequest {
  /**
   * 课程id集合
   */
  courseIds?: Array<string>
  /**
   * 工种id路径集合
   */
  workTypeIdPaths?: Array<string>
  /**
   * 课程名
   */
  courseName?: string
  /**
   * 机构Id集合
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 课件供应商id
   */
  coursewareSupplierIdList?: Array<string>
  /**
   * 微服务上下文
   */
  microContext?: MicroContext
  /**
   * 排序字段
   */
  sortField?: string
}

/**
 * 方案维度课程评价统计
Author:FangKunSen
Time:2021-05-07,15:12
 */
export class SchemeCourseAppraiseStatisticRequest {
  /**
   * 方案id
   */
  schemeIds?: Array<string>
  /**
   * 方案名
   */
  schemeName?: string
  /**
   * 工种id路径集合
   */
  workTypeIdPaths?: Array<string>
  /**
   * 机构Id集合
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 渠道商ID集合
   */
  channelVendorIdList?: Array<string>
  /**
   * 课件供应商id
   */
  coursewareSupplierIdList?: Array<string>
  /**
   * 综合评分排序
默认不排序
   */
  comprehensiveAppraiseSoft?: QueryParamSortEnum
  /**
   * 排序字段
   */
  sortField?: string
}

/**
 * 机构维度的课程统计查询条件
Author:FangKunSen
Time:2021-05-07,14:46
 */
export class TrainingInstitutionCourseAppraiseStatisticRequest {
  /**
   * 机构Id集合
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 渠道商ID集合
   */
  channelVendorIdList?: Array<string>
  /**
   * 课件供应商id
   */
  coursewareSupplierIdList?: Array<string>
}

/**
 * @author: eleven
@date: 2020/6/11
 */
export class UserLoginLogRequest {
  /**
   * 用户id
   */
  userId?: string
}

/**
 * 首页统计数据统计参数
@author: eleven
@date: 2020/4/18
 */
export class DashboardParam {
  /**
   * 作答提交时间起 >&#x3D;
   */
  submitAnswerTimeBegin?: string
  /**
   * 作答提交时间止 <&#x3D;
   */
  submitAnswerTimeEnd?: string
  /**
   * 机构id
   */
  trainingInstitutionId?: string
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
  /**
   * 年度
   */
  year?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId?: string
}

/**
 * 答题统计查询参数
Author:FangKunSen
Time:2020-03-04,14:13
 */
export class AnswerQuestionStatisticQueryParamsDTO {
  /**
   * 试题题目关键字
   */
  questionContent?: string
  /**
   * 试题类型
@see PreExamQuestionCategory
   */
  type?: string
  /**
   * 试题题型标签id
   */
  questionType?: QuestionType
  /**
   * 考纲标签id
   */
  knowledgeTagIds?: Array<string>
  /**
   * 对题率 起
   */
  rightRateStart?: string
  /**
   * 对题率 止
   */
  rightRateEnd?: string
  /**
   * 创建时间 起
   */
  createTimeStart?: string
  /**
   * 创建时间 止
   */
  createTimeEnd?: string
}

/**
 * 热门课程查询条件dto
Author:FangKunSen
Time:2020-06-10,15:48
 */
export class HotCourseChooseStatisticQueryParamsDTO {
  /**
   * 课程id
   */
  courseId?: string
  /**
   * 网校机构id
   */
  schoolUnitId?: string
  /**
   * 各地区的单位id
例如福州市人设单位：2ca4cd0772967f5d0172a347ee0e1hs1
错误入参：/350100
   */
  regionId?: string
  /**
   * 年度集合
   */
  years?: Array<number>
  /**
   * 排序依据
   */
  sort?: SortByEnum
  /**
   * 排序：0不排序，1升序，2降序
   */
  sortOrder: number
  /**
   * 子排序依据（子排序会在主排序依据字段相等的情况下，对相等的若干个记录进行二次排序）
   */
  subSort?: SortByEnum
  /**
   * 子排序：0不排序，1升序，2降序
   */
  subSortOrder: number
}

/**
 * 学习直方图统计查询条件
<AUTHOR>
@version 1.0
@date 2021/7/17 14:51
 */
export class LearningStatisticDateHistogramParamDTO {
  /**
   * 合格时间  查询时间范围
   */
  qualifiedTime?: TimeRegionRequest
  /**
   * 机构ID
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 课件供应商id
   */
  coursewareSupplierIdList?: Array<string>
  /**
   * 渠道商id
   */
  channelVendorIdList?: Array<string>
  /**
   * 时间单位枚举
YEARS,
<p>
MONTHS,
<p>
DAYS,
<p>
HOURS
   */
  timeUnit?: TimeUnit
}

/**
 * 培训学习统计查询参数
Author:FangKunSen
Time:2020-03-13,10:21
 */
export class LearningStatisticRequest {
  /**
   * 机构ID
   */
  trainingInstitutionId?: string
  /**
   * 机构ID集合
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 机构名称
   */
  trainingInstitutionName?: string
  /**
   * 课件供应商id集合
   */
  coursewareSupplierIdList?: Array<string>
  /**
   * 渠道商id集合
   */
  channelVendorIdList?: Array<string>
  /**
   * 参训单位id集合
   */
  participatingUnitIdList?: Array<string>
  /**
   * 用户id
   */
  userId?: string
  /**
   * 用户id
   */
  userIdList?: Array<string>
  /**
   * 手机号码
   */
  phone?: string
  /**
   * 用户名
   */
  userName?: string
  /**
   * 身份证号
   */
  uniqueData?: string
  /**
   * 地区路径
   */
  regionPath?: string
  /**
   * 人员类别
   */
  userCategory?: string
  /**
   * 培训方案id
   */
  schemeIds?: Array<string>
  /**
   * 方案名称，当该字段不为空时，前端入参方案id无效，反之有效
   */
  schemeName?: string
  /**
   * 适用人群
   */
  suitableCrowNames?: Array<string>
  /**
   * 期数ids
   */
  issueIds?: Array<string>
  /**
   * 开通成功时间 起
   */
  openTimeStart?: string
  /**
   * 开通成功时间 止
   */
  openTimeEnd?: string
  /**
   * 合格时间 起
   */
  passTimeStart?: string
  /**
   * 合格时间 止
   */
  passTimeEnd?: string
  /**
   * 考试是否合格
   */
  examQualified?: boolean
  /**
   * 是否已考试
   */
  hasDoExam?: boolean
  /**
   * 班级总体考核结果
   */
  qualified?: boolean
  /**
   * 学习状态
   */
  studyState?: StudyState
  /**
   * 学习进度（起）
   */
  scheduleStart?: string
  /**
   * 学习进度（止）
   */
  scheduleEnd?: string
  /**
   * 报名方式
PRESENT为导入开通；COLLECTIVE为集体缴费；其他为自主报名
   */
  registrationWay?: PaymentChannelTypeEnum
  /**
   * 培训工种类别/培训工种id path
用于培训类别联合工种多条件查询
   */
  workTypeIdPathList?: Array<string>
  /**
   * 年度
   */
  year?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId?: string
}

/**
 * 开通直方图统计查询条件
<AUTHOR>
@version 1.0
@date 2021/7/17 14:51
 */
export class OpenStatisticDateHistogramParamDTO {
  /**
   * 订单状态查询条件
   */
  orderStatusList?: Array<OrderStatus>
  /**
   * 订单创建时间 查询时间范围
   */
  orderCreateTime?: TimeRegionRequest
  /**
   * 订单完成时间 查询时间范围
   */
  orderCompleteTime?: TimeRegionRequest
  /**
   * 机构ID
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 课件供应商id
   */
  coursewareSupplierIdList?: Array<string>
  /**
   * 渠道商id
   */
  channelVendorIdList?: Array<string>
  /**
   * 时间单位枚举
YEARS,
<p>
MONTHS,
<p>
DAYS,
<p>
HOURS
   */
  timeUnit?: TimeUnit
}

/**
 * 开通统计查询参数
Author:FangKunSen
Time:2020-03-04,14:05
 */
export class OpenStatisticQueryParamsDTO {
  /**
   * 机构ID
   */
  trainingInstitutionIdList?: Array<string>
  /**
   * 机构名称
   */
  trainingInstitutionName?: string
  /**
   * 课件供应商id
   */
  coursewareSupplierIdList?: Array<string>
  /**
   * 渠道商id
   */
  channelVendorIdList?: Array<string>
  /**
   * 参训单位ID
   */
  participatingUnitIdList?: Array<string>
  /**
   * 培训方案名称
   */
  schemeName?: string
  /**
   * 方案id
   */
  schemeIds?: Array<string>
  /**
   * 期数id
   */
  issueId?: string
  /**
   * 期数id集合
   */
  issueIds?: Array<string>
  /**
   * 适用人群
   */
  suitableCrowNames?: Array<string>
  /**
   * 期别学习时间
   */
  stageLearningTime?: TimeRegionRequest
  /**
   * 开通成功时间
   */
  openTime?: TimeRegionRequest
  /**
   * 退款成功时间
   */
  refundTime?: TimeRegionRequest
  /**
   * 工种id list
   */
  workTypeIdList?: Array<string>
  /**
   * 培训工种类别/培训工种id path
用于培训类别联合工种多条件查询
   */
  workTypeIdPathList?: Array<string>
  /**
   * 年度
   */
  year?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId?: string
}

/**
 * 培训学习统计查询参数
Author:FangKunSen
Time:2020-03-13,10:21
 */
export class SchemeSkuLearningStatisticRequest {
  /**
   * 单位id
   */
  unitId?: string
  /**
   * 机构ID
   */
  trainingInstitutionId?: string
  /**
   * 年度
   */
  year?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId?: string
}

/**
 * 时间范围
<AUTHOR>
@date 2020/5/3116:42
 */
export class TimeRegionRequest {
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 结束时间
   */
  endTime?: string
}

export class Page {
  pageNo?: number
  pageSize?: number
}

export class DataRouterIdentity {
  dataPlatformVersionId?: string
  dataProjectId?: string
}

export class HttpIdentity {
  ip?: string
  domain?: string
  requestUrl?: string
}

export class MicroContext {
  sequenceNo?: string
  platformId?: string
  platformVersionId?: string
  projectId?: string
  subProjectId?: string
  servicerProvider?: ServicerProvider
  userIdentity?: UserIdentity
  dataRouterIdentity?: DataRouterIdentity
  httpIdentity?: HttpIdentity
}

export class ServicerProvider {
  unitId?: string
  servicerType: number
  servicerId?: string
}

export class UserIdentity {
  accountId?: string
  rootAccountId?: string
  accountType?: number
  userId?: string
}

/**
 * 课程维度评价统计
Author:FangKunSen
Time:2021-05-07,10:52
 */
export class CourseAppraiseStatisticDTO {
  /**
   * 课程id
   */
  id: string
  /**
   * 课程名称
   */
  courseName: string
  /**
   * 课程关联工种路径：/类别/类别/工种
   */
  workTypeIdPath: Array<string>
  /**
   * 综合评分
   */
  average: number
  /**
   * 综合评分1星命中数
   */
  one: number
  /**
   * 综合评分2星命中数
   */
  two: number
  /**
   * 综合评分3星命中数
   */
  three: number
  /**
   * 综合评分4星命中数
   */
  four: number
  /**
   * 综合评分5星命中数
   */
  five: number
  /**
   * 课件供应商Id
v1.7.0.0
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称
v1.7.0.0
   */
  coursewareSupplierName: string
  /**
   * 学时数
v1.7.0.0
   */
  period: number
}

/**
 * 课程维度评价统计合计行
Author:FangKunSen
Time:2021-05-07,15:54
 */
export class CourseAppraiseStatisticTotalRowDTO {
  /**
   * 综合得分
   */
  average: number
}

/**
 * 方案维度课程评价统计
Author:FangKunSen
Time:2021-05-07,15:08
 */
export class SchemeCourseAppraiseStatisticDTO {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方案名称
   */
  schemeName: string
  /**
   * 机构ID
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 课件供应商id
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称
   */
  coursewareSupplierName: string
  /**
   * 综合评分
   */
  average: number
  /**
   * 综合评分1星命中数
   */
  one: number
  /**
   * 综合评分2星命中数
   */
  two: number
  /**
   * 综合评分3星命中数
   */
  three: number
  /**
   * 综合评分4星命中数
   */
  four: number
  /**
   * 综合评分5星命中数
   */
  five: number
}

/**
 * 学习统计（方案维度）dto
Author:FangKunSen
Time:2020-05-30,11:24
 */
export class SchemeLearningStatisticResponse {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方案名称
   */
  schemeName: string
  /**
   * 适用人群
   */
  suitableCrowNames: Array<string>
  /**
   * 培训工种类别名称
   */
  trainingTypeNamePath: string
  /**
   * 培训工种名称
   */
  workTypeName: string
  /**
   * 机构Id v1.7.0.0
   */
  trainingInstitutionId: string
  /**
   * 机构名称 v1.7.0.0
   */
  trainingInstitutionName: string
  /**
   * 课件供应商Id v1.7.0.0
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称 v1.7.0.0
   */
  coursewareSupplierName: string
  /**
   * 渠道商ID v1.7.0.0
   */
  channelVendorId: string
  /**
   * 渠道商名称 v1.7.0.0
   */
  channelVendorName: string
  /**
   * 净报名数量 (总数-退班的)
   */
  openUserCount: number
  /**
   * 未学习人数（对应培训班学习进度&#x3D;0）
   */
  waitStudyCount: number
  /**
   * 学习中人数（0<学习进度<&#x3D;100之间）
   */
  studyingCount: number
  /**
   * 已学完人数（对应培训人员在对应班级学习进度&#x3D;100%人员）
   */
  studyFinishCount: number
  /**
   * 已考试人数。
   */
  examedUserCount: number
  /**
   * 合格人数
没有考核就直接合格
   */
  qualifiedCount: number
  /**
   * 是否有课程学习方式
   */
  hasCourseLearning: boolean
  /**
   * 是否有考试学习方式
   */
  hasExamLearning: boolean
  /**
   * 已考核通过获得学时数
   */
  finishAssessGrade: number
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * 单位维度课程评价统计
Author:FangKunSen
Time:2021-05-07,14:59
 */
export class TrainingInstitutionCourseAppraiseStatisticDTO {
  /**
   * 机构ID
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 综合评分
   */
  average: number
  /**
   * 综合评分1星命中数
   */
  one: number
  /**
   * 综合评分2星命中数
   */
  two: number
  /**
   * 综合评分3星命中数
   */
  three: number
  /**
   * 综合评分4星命中数
   */
  four: number
  /**
   * 综合评分5星命中数
   */
  five: number
}

/**
 * @author: eleven
@date: 2020/6/11
 */
export class UserLoginLogResponse {
  /**
   * 用户id
   */
  userId: string
  /**
   * 登录时间
   */
  loginTime: string
  /**
   * 登录ip
   */
  loginIP: string
}

/**
 * 课程信息
<AUTHOR>
 */
export class LearningProcessCourseInfoDto {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 课程
   */
  courseName: string
  /**
   * 课程下的课件分组
   */
  coursewareList: Array<LearningProcessCoursewareInfoDto>
}

/**
 * 课件信息
<AUTHOR>
 */
export class LearningProcessCoursewareInfoDto {
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 课件ID
   */
  coursewareId: string
  /**
   * 课件
   */
  coursewareName: string
  /**
   * 进入学习时间
   */
  enterTime: string
  /**
   * 播放时长
   */
  playTimeLength: number
  /**
   * 播放时长
   */
  playTime: string
  /**
   * 学习进度
   */
  schedule: number
  /**
   * 具体的拍摄记录
   */
  captureList: Array<LearningProcessFsCaptureDto>
}

/**
 * 拍摄信息
<AUTHOR>
 */
export class LearningProcessFsCaptureDto {
  /**
   * 拍摄时间
   */
  captureTimeLength: number
  /**
   * 拍摄时间
   */
  captureTime: string
  /**
   * 认证结果
   */
  result: boolean
  /**
   * 是否已操作
   */
  operation: boolean
}

/**
 * sku属性的专用类，所有需要sku属性字段的统一继承该类，无需到处散落属性字段
进行数据转换提取
@author: eleven
@date: 2020/3/27
 */
export class SkuProperty {
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * Author:FangKunSen
Time:2020-08-18,15:25
 */
export class SkuValue {
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别 id路径
   */
  trainingTypeIdPath: string
  /**
   * 培训类别
   */
  trainingTypeName: string
  /**
   * 培训类别 名称路径
   */
  trainingTypeNamePath: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeName: string
}

/**
 * 试题作答统计dto
Author:FangKunSen
Time:2020-03-04,14:42
 */
export class AnswerQuestionStatisticDTO {
  /**
   * 试题id
   */
  questionId: string
  /**
   * 试题题目
   */
  questionContent: string
  /**
   * 考试类别
   */
  examCategory: string
  /**
   * 专业
   */
  major: string
  /**
   * 所属考纲id
   */
  belongKnowledgeIds: Array<string>
  /**
   * 试题类型：真题、练习题、模拟题
   */
  type: string
  /**
   * 试题题型
   */
  questionType: string
  /**
   * 被作答次数
   */
  answerTimes: number
  /**
   * 对题率
   */
  rightRate: number
}

/**
 * 时间直方图统计结果
<AUTHOR>
@version 1.0
@date 2021/7/17 14:52
 */
export class DateHistogramDTO {
  /**
   * 时间单位
   */
  timeUnit: TimeUnit
  /**
   * 统计结果元素
   */
  histogram: Array<DateHistogramElementDTO>
  /**
   * 总计
   */
  totalCount: number
}

/**
 * 时间直方图单项结果
<AUTHOR>
@version 1.0
@date 2021/7/17 14:52
 */
export class DateHistogramElementDTO {
  /**
   * 时间
switch (timeUnit) {
case &#x27;YEARS&#x27;:
result.date &#x3D; year + &#x27;-01-01 00:00&#x27;;
break;
case &#x27;MONTHS&#x27;:
result.date &#x3D; year + &#x27;-&#x27; + month + &#x27;-01 00:00&#x27;;
break;
case &#x27;DAYS&#x27;:
result.date &#x3D; year + &quot;-&quot; + month + &quot;-&quot; + day + &quot; 00:00&quot;;
break;
case &#x27;HOURS&#x27;:
result.date &#x3D; year + &#x27;-&#x27; + month + &#x27;-&#x27; + day + &#x27; &#x27; + hours + &quot;:00&quot;;
break;
default:result.date&#x3D;currentThis.qualifiedTime;
}
   */
  date: string
  /**
   * 总计
   */
  count: number
}

/**
 * 人气课程dto
Author:FangKunSen
Time:2020-06-10,15:40
 */
export class HotCourseChooseStatisticDTO {
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课程名字
   */
  courseName: string
  /**
   * 图片路径
   */
  courseImagePath: string
  /**
   * 供应商id
   */
  supplierId: string
  /**
   * 课时
   */
  period: number
  /**
   * 教师id
   */
  teacherId: string
  /**
   * 课程创建时间
   */
  createTime: string
  /**
   * 被成功选课次数
   */
  beChooseTimes: number
  /**
   * 评分
   */
  evaluate: number
}

/**
 * 培训学习统计dto
Author:FangKunSen
Time:2020-03-13,10:34
 */
export class LearningStatisticDTO {
  /**
   * 本条记录来源子订单号
   */
  subOrderNo: string
  /**
   * 机构ID
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 用户id
   */
  userId: string
  /**
   * 用户名
   */
  userName: string
  /**
   * 手机号
   */
  phone: string
  /**
   * 用户唯一性值
   */
  uniqueData: string
  /**
   * 用户的单位的地区
   */
  region: Region
  /**
   * 工作单位名称 v1.7.0.0
   */
  companyName: string
  /**
   * 所属人群 v1.7.0.0
   */
  peoples: Array<string>
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方案名字
   */
  schemeName: string
  /**
   * 期数id
   */
  issueId: string
  /**
   * 适用人群
   */
  suitableCrowNames: Array<string>
  /**
   * 培训学时
   */
  period: string
  /**
   * 报名成功时间
   */
  reportSuccessTime: string
  /**
   * 商品购买金额
   */
  commodityDealAmount: number
  /**
   * 报名方式
   */
  registrationWay: string
  /**
   * 物品状态
valid 有效 freeze 冻结 invalid 无效
@see UserGoodState#getValue()
   */
  goodState: string
  /**
   * 是否启用培训班考核
   */
  enabledAssess: boolean
  /**
   * 学习状态
   */
  studyState: StudyState
  /**
   * 方案是否合格
   */
  qualified: boolean
  /**
   * 方案合格时间
   */
  qualifiedTime: string
  /**
   * 是否有课程学习方式
   */
  hasCourseLearning: boolean
  /**
   * 课程学习方式id
   */
  courseLearningId: string
  /**
   * 是否要求已选课程全部完成
   */
  courseAssessSettingAllSelectedComplete: boolean
  /**
   * 课程学习考核要求完成进度
   */
  courseAssessSettingCourseSchedule: number
  /**
   * 课程-课程数
   */
  courseNumber: number
  /**
   * 课程-学习进度
   */
  courseLearningProgress: number
  /**
   * 课程合格时间
达到考核学时的最后一门课程完成时间
   */
  coursePassTime: string
  /**
   * 是否有考试学习方式
   */
  hasExamLearning: boolean
  /**
   * 考试学习方式id
   */
  examLearningId: string
  /**
   * 练习学习方式id
   */
  practiceLearningId: string
  /**
   * 考试考核要求成绩
   */
  examAssessSettingScore: number
  /**
   * 考试次数
   */
  examCount: number
  /**
   * 考试是否合格
   */
  examQualified: boolean
  /**
   * 模拟卷-最高成绩
   */
  analogExamHighestScore: number
  /**
   * 首次进入考试时间
   */
  firstJoinExamTime: string
  /**
   * 数据最后更新时间
   */
  updateTime: string
  /**
   * 课件供应商Id
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称
   */
  coursewareSupplierName: string
  /**
   * 渠道商ID
   */
  channelVendorId: string
  /**
   * 渠道商名称
   */
  channelVendorName: string
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * 用户方案内课程的学习进度
Author:FangKunSen
Time:2020-03-18,10:54
 */
export class LearningStatisticUserCourseLearningScheduleDTO {
  /**
   * 用户id
   */
  userId: string
  /**
   * 课程名字
   */
  courseName: string
  /**
   * 总讲数
   */
  totalLectureNumber: number
  /**
   * 已更新讲数
   */
  replaceLectureNumber: number
  /**
   * 学习方案id
   */
  schemeId: string
  /**
   * 课程id
   */
  courseId: string
  /**
   * 学习进度
   */
  schedule: number
  /**
   * 学习状态
0/1/2，未学习/学习中/学习完成
@see com.fjhb.btpx.platform.dao.elasticsearch.enums.StudyState
   */
  studyState: number
  /**
   * 最新学习时间
   */
  lastStudyTime: string
}

/**
 * 学习统计详情-用户练习作答数据统计图（包括练习与每日一练）
Author:FangKunSen
Time:2020-03-18,15:28
 */
export class LearningStatisticUserPracticeChartDTO {
  /**
   * 用户od
   */
  userId: string
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 练习学习方式id
   */
  practiceLearningId: string
  /**
   * 整体答题次数总和
   */
  totalAnswerTimes: number
  /**
   * 整体对题率
   */
  totalRightRate: number
  /**
   * 真题项
   */
  realQuestion: UserPracticeChartSubItemDTO
  /**
   * 模拟题项
   */
  simulationQuestion: UserPracticeChartSubItemDTO
  /**
   * 练习题项
   */
  practiceQuestion: UserPracticeChartSubItemDTO
}

/**
 * 开通统计聚合对象
Author:FangKunSen
Time:2020-03-10,10:43
 */
export class OpenStatisticAggregationDTO {
  /**
   * 报名数
   */
  openNumber: number
  /**
   * 取消报名数
   */
  cancelOpenNumber: number
  /**
   * 净报名数
   */
  realOpenNumber: number
  /**
   * 开通总金额
   */
  totalAmount: number
  /**
   * 退款总金额
   */
  refundTotalAmount: number
  /**
   * 净成交总金额
   */
  realTotalAmount: number
}

/**
 * 学习统计（地区维度）dto
Author:FangKunSen
Time:2020-05-30,10:48
 */
export class RegionLearningStatisticResponse {
  /**
   * 地区code
   */
  regionCode: string
  /**
   * 分组id
   */
  id: string
  /**
   * 净报名数量 (总数-退班的)
   */
  openUserCount: number
  /**
   * 未学习人数（对应培训班学习进度&#x3D;0）
   */
  waitStudyCount: number
  /**
   * 学习中人数（0<学习进度<&#x3D;100之间）
   */
  studyingCount: number
  /**
   * 已学完人数（对应培训人员在对应班级学习进度&#x3D;100%人员）
   */
  studyFinishCount: number
  /**
   * 已考试人数。
   */
  examedUserCount: number
  /**
   * 合格人数
没有考核就直接合格
   */
  qualifiedCount: number
  /**
   * 是否有课程学习方式
   */
  hasCourseLearning: boolean
  /**
   * 是否有考试学习方式
   */
  hasExamLearning: boolean
  /**
   * 已考核通过获得学时数
   */
  finishAssessGrade: number
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * 开通统计dto
Author:FangKunSen
Time:2020-03-09,14:03
 */
export class SchemeOpenStatisticDTO {
  /**
   * 方案id
   */
  schemeId: string
  /**
   * 方案名字
   */
  schemeName: string
  /**
   * 课件供应商id
   */
  coursewareSupplierId: string
  /**
   * 课件供应商名称
   */
  coursewareSupplierName: string
  /**
   * 渠道供应商id
add by wtl 2021年7月21日 09:19:42 v1.7.0.0
   */
  channelVendorId: string
  /**
   * 渠道供应商名称
add by wtl 2021年7月21日 09:19:42 v1.7.0.0
   */
  channelVendorName: string
  /**
   * sku属性id
   */
  skuProperty: SkuProperty
  /**
   * sku属性值
   */
  skuValue: SkuValue
  /**
   * 期数id
   */
  issueId: string
  /**
   * 期数名字
   */
  issueName: string
  /**
   * 商品id
   */
  commoditySkuId: string
  /**
   * 适用人群
   */
  suitableCrowNames: Array<string>
  /**
   * 购买时商品标价
   */
  subOrderLabelPrice: number
  /**
   * 机构ID
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 自主报名
   */
  selfOpen: OpenStatisticAggregationDTO
  /**
   * 导入报名
   */
  importOpen: OpenStatisticAggregationDTO
  /**
   * 集体缴费线上支付
   */
  batchOpenOnline: OpenStatisticAggregationDTO
  /**
   * 集体缴费线下支付
   */
  batchOpenOffline: OpenStatisticAggregationDTO
  /**
   * 合计
   */
  totalOpen: OpenStatisticAggregationDTO
}

/**
 * 学习统计基类
<AUTHOR> create 2020/7/2 16:45
 */
export class SchemeSkuLearniningStatisticsResponse {
  /**
   * 学习中人数（学习进度<100）
   */
  studyingCount: number
  /**
   * 已学完人数（对应培训人员在对应班级学习进度&#x3D;100%人员）
   */
  studyFinishCount: number
  /**
   * 学习总人数
   */
  studyCount: number
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * 答题统计总计
Author:FangKunSen
Time:2020-03-04,14:50
 */
export class TotalAnswerQuestionStatisticDTO {
  /**
   * 试题总数
   */
  totalQuestionNumber: number
  /**
   * 累计被作答次数
   */
  totalAnswerTimes: number
  /**
   * 对题率
   */
  totalRightRate: number
}

/**
 * 开通统计合计dto
Author:FangKunSen
Time:2020-03-09,14:03
 */
export class TotalOpenCountStatisticDTO {
  /**
   * 总开通数
   */
  openNumber: number
  /**
   * 净开通（单位：人/次）
   */
  realOpenNumber: number
  /**
   * 净开通学员数（一个学员多次报名仅算一个人数）
   */
  realUserOpenNumber: number
}

/**
 * 开通统计合计dto
Author:FangKunSen
Time:2020-03-09,14:03
 */
export class TotalOpenStatisticDTO {
  /**
   * 年
   */
  year: number
  /**
   * 月
   */
  month: number
  /**
   * 日
   */
  day: number
  /**
   * 总开通数
   */
  openNumber: number
  /**
   * 退款数
   */
  refundNumber: number
  /**
   * 净开通（单位：人/次）
   */
  realOpenNumber: number
  /**
   * 净开通学员数（一个学员多次报名仅算一个人数）
   */
  realUserOpenNumber: number
  /**
   * 开通总金额（单位：元）
   */
  totalAmount: number
  /**
   * 退款总金额（单位：元）
   */
  refundTotalAmount: number
  /**
   * 净开通总金额（单位：元）
   */
  realTotalAmount: number
}

/**
 * 学习统计（单位维度）dto
Author:FangKunSen
Time:2020-05-30,10:48
 */
export class TrainingInstitutionLearningStatisticResponse {
  /**
   * 机构ID
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 属于该单位的人数
   */
  unitUserCount: number
  /**
   * 净报名数量 (总数-退班的)
   */
  openUserCount: number
  /**
   * 未学习人数（对应培训班学习进度&#x3D;0）
   */
  waitStudyCount: number
  /**
   * 学习中人数（0<学习进度<&#x3D;100之间）
   */
  studyingCount: number
  /**
   * 已学完人数（对应培训人员在对应班级学习进度&#x3D;100%人员）
   */
  studyFinishCount: number
  /**
   * 已考试人数。
   */
  examedUserCount: number
  /**
   * 合格人数
没有考核就直接合格
   */
  qualifiedCount: number
  /**
   * 是否有课程学习方式
   */
  hasCourseLearning: boolean
  /**
   * 是否有考试学习方式
   */
  hasExamLearning: boolean
  /**
   * 已考核通过获得学时数
   */
  finishAssessGrade: number
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * 开通统计dto
Author:FangKunSen
Time:2020-03-09,14:03
 */
export class TrainingInstitutionOpenStatisticDTO {
  /**
   * 机构ID
   */
  trainingInstitutionId: string
  /**
   * 机构名称
   */
  trainingInstitutionName: string
  /**
   * 自主报名
   */
  selfOpen: OpenStatisticAggregationDTO
  /**
   * 导入报名
   */
  importOpen: OpenStatisticAggregationDTO
  /**
   * 集体缴费线上支付
   */
  batchOpenOnline: OpenStatisticAggregationDTO
  /**
   * 集体缴费线下支付
   */
  batchOpenOffline: OpenStatisticAggregationDTO
  /**
   * 合计
   */
  totalOpen: OpenStatisticAggregationDTO
}

/**
 * 在线学习人数专业分布
@author: eleven
@date: 2020/4/24
 */
export class UserOnLineMajorDistributeResponse {
  /**
   * 人数
   */
  userCount: number
  /**
   * 年度
   */
  year: number
  /**
   * 培训类别
   */
  trainingTypeId: string
  /**
   * 培训工种（有原来字段：培训对象-traineesId替换而来）
   */
  workTypeId: string
}

/**
 * 在线学习人数终端分布
@author: eleven
@date: 2020/4/24
 */
export class UserOnLineTerminalDistributeResponse {
  /**
   * 终端类型
   */
  client: ClientType
  /**
   * 人数
   */
  userCount: number
}

/**
 * 在线学习人数施教机构分布
@author: eleven
@date: 2020/4/24
 */
export class UserOnLineUnitDistributeResponse {
  /**
   * 单位id
   */
  unitId: string
  /**
   * 人数
   */
  userCount: number
  /**
   * 单位名称
   */
  unitName: string
}

/**
 * 学习统计-用户练习作答数据统计图-子项
Author:FangKunSen
Time:2020-03-18,15:35
 */
export class UserPracticeChartSubItemDTO {
  /**
   * 试题类型
   */
  practiceType: string
  /**
   * 该题型库内总题数
   */
  totalNumber: number
  /**
   * 答题总次数
   */
  answerTimes: number
  /**
   * 答对次数
   */
  answerRightTimes: number
  /**
   * 答对次数占比
   */
  proportion: number
}

/**
 * 工种学习统计
Author:FangKunSen
Time:2021-04-21,09:25
 */
export class WorkTypeLearningStatisticDTO {
  /**
   * 工种id路径
   */
  workTypeIdPath: string
  /**
   * 工种路径名
   */
  workTypeIdPathName: string
  /**
   * 净报名数量 (总数-退班的)
   */
  openUserCount: number
  /**
   * 未学习人数（对应培训班学习进度&#x3D;0）
   */
  waitStudyCount: number
  /**
   * 学习中人数（0<学习进度<&#x3D;100之间）
   */
  studyingCount: number
  /**
   * 已学完人数（对应培训人员在对应班级学习进度&#x3D;100%人员）
   */
  studyFinishCount: number
  /**
   * 合格人数
没有考核就直接合格
   */
  qualifiedCount: number
}

/**
 * 开通统计dto
Author:FangKunSen
Time:2020-03-09,14:03
 */
export class WorkTypeOpenStatisticDTO {
  /**
   * 培训工种类别id
   */
  trainingTypeId: string
  /**
   * 培训工种类别名称
   */
  trainingTypeName: string
  /**
   * 培训工种id
   */
  workTypeId: string
  /**
   * 培训工种名称
   */
  workTypeName: string
  /**
   * 报名数
   */
  openNumber: number
}

/**
 * <AUTHOR>
@date 2020/8/1010:43
 */
export class Region {
  /**
   * 地区路径
   */
  regionPath: string
  /**
   * 省份id
   */
  provinceId: string
  /**
   * 城市id
   */
  cityId: string
  /**
   * 区、县id
   */
  countyId: string
}

export class LearningProcessCourseInfoDtoPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LearningProcessCourseInfoDto>
}

export class AnswerQuestionStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<AnswerQuestionStatisticDTO>
}

export class CourseAppraiseStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseAppraiseStatisticDTO>
}

export class HotCourseChooseStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<HotCourseChooseStatisticDTO>
}

export class RegionLearningStatisticResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<RegionLearningStatisticResponse>
}

export class SchemeCourseAppraiseStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SchemeCourseAppraiseStatisticDTO>
}

export class SchemeLearningStatisticResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SchemeLearningStatisticResponse>
}

export class SchemeOpenStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SchemeOpenStatisticDTO>
}

export class TrainingInstitutionCourseAppraiseStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingInstitutionCourseAppraiseStatisticDTO>
}

export class TrainingInstitutionLearningStatisticResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingInstitutionLearningStatisticResponse>
}

export class TrainingInstitutionOpenStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingInstitutionOpenStatisticDTO>
}

export class LearningStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LearningStatisticDTO>
}

export class UserLoginLogResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<UserLoginLogResponse>
}

export class WorkTypeLearningStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<WorkTypeLearningStatisticDTO>
}

export class WorkTypeOpenStatisticDTOPage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<WorkTypeOpenStatisticDTO>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 统计在线人数  不会重复
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async countUserOnlineCount(
    param: DashboardParam,
    query: DocumentNode = GraphqlImporter.countUserOnlineCount,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量导出学员学习日志
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createBatchExportLearningLogTask(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.createBatchExportLearningLogTask,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量导出学员学习日志
   * 为渠道商提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createBatchExportLearningLogTaskForChannelVendor(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.createBatchExportLearningLogTaskForChannelVendor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量导出学员学习日志
   * 为参训单位提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createBatchExportLearningLogTaskForParticipatingUnit(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.createBatchExportLearningLogTaskForParticipatingUnit,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 批量导出学员学习日志
   * 为机构提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createBatchExportLearningLogTaskForTrainingInstitution(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.createBatchExportLearningLogTaskForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出单个学员学习日志
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createExportLearningLogTask(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.createExportLearningLogTask,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出单个学员学习日志
   * 为渠道商提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createExportLearningLogTaskForChannelVendor(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.createExportLearningLogTaskForChannelVendor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出单个学员学习日志
   * 为参训单位提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createExportLearningLogTaskForParticipatingUnit(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.createExportLearningLogTaskForParticipatingUnit,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出单个学员学习日志
   * 为机构提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createExportLearningLogTaskForTrainingInstitution(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.createExportLearningLogTaskForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出答题统计
   * @param answerQuestionStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param answerQuestionStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportAnswerQuestionStatistic(
    answerQuestionStatisticQueryParamsDTO: AnswerQuestionStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.exportAnswerQuestionStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { answerQuestionStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出课程维度评价统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCourseAppraiseStatistic(
    request: CourseAppraiseStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportCourseAppraiseStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出课程维度评价统计
   * 为课件供应商提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCourseAppraiseStatisticForCoursewareSupplier(
    request: CourseAppraiseStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportCourseAppraiseStatisticForCoursewareSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出课程维度评价统计
   * 为机构提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportCourseAppraiseStatisticForTrainingInstitution(
    request: CourseAppraiseStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportCourseAppraiseStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出学习统计时间直方图
   * 为渠道商提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportLearningDateHistogramForChannelVendor(
    dateHistogramParam: LearningStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.exportLearningDateHistogramForChannelVendor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出学习统计时间直方图
   * 为课件供应商提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportLearningDateHistogramForCoursewareSupplier(
    dateHistogramParam: LearningStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.exportLearningDateHistogramForCoursewareSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出学习统计时间直方图
   * 为参训单位提交接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportLearningDateHistogramForParticipatingUnit(
    dateHistogramParam: LearningStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.exportLearningDateHistogramForParticipatingUnit,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出学习统计时间直方图
   * 为机构提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportLearningDateHistogramForTrainingInstitution(
    dateHistogramParam: LearningStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.exportLearningDateHistogramForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出开通统计时间直方图
   * 为渠道商提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOpenDateHistogramForChannelVendor(
    dateHistogramParam: OpenStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.exportOpenDateHistogramForChannelVendor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出开通统计时间直方图
   * 为课件供应商提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOpenDateHistogramForCoursewareSupplier(
    dateHistogramParam: OpenStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.exportOpenDateHistogramForCoursewareSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出开通统计时间直方图
   * 为机构提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportOpenDateHistogramForTrainingInstitution(
    dateHistogramParam: OpenStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.exportOpenDateHistogramForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出地区学习统计
   * @param params
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportRegionLearningStatistic(
    params: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportRegionLearningStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { params },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出地区学习统计
   * 为机构提供接口
   * @param params
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportRegionLearningStatisticForTrainingInstitution(
    params: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportRegionLearningStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { params },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出方案纬度学习统计
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportSchemeLearningStatistic(
    params: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportSchemeLearningStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { params },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出方案纬度学习统计
   * 为机构提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportSchemeLearningStatisticForTrainingInstitution(
    params: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportSchemeLearningStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { params },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出方案开通统计
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportSchemeOpenStatistic(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.exportSchemeOpenStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出方案开通统计
   * 为渠道供应商提供接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportSchemeOpenStatisticForChannelVendor(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.exportSchemeOpenStatisticForChannelVendor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出方案开通统计
   * 为课件供应商提供接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportSchemeOpenStatisticForCoursewareSupplier(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.exportSchemeOpenStatisticForCoursewareSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出方案开通统计
   * 为机构提供接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportSchemeOpenStatisticForTrainingInstitution(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.exportSchemeOpenStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出机构维度课程评价统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingInstitutionCourseAppraiseStatistic(
    request: TrainingInstitutionCourseAppraiseStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportTrainingInstitutionCourseAppraiseStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出机构纬度学习统计
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingInstitutionLearningStatistic(
    params: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportTrainingInstitutionLearningStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { params },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出开通统计
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingInstitutionOpenStatistic(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.exportTrainingInstitutionOpenStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出机构开通统计
   * 为渠道商提供接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingInstitutionOpenStatisticForChannelVendor(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.exportTrainingInstitutionOpenStatisticForChannelVendor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出机构开通统计
   * 为机构提供接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportTrainingInstitutionOpenStatisticForTrainingInstitution(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.exportTrainingInstitutionOpenStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出学习统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportUserLearningStatistic(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportUserLearningStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出学习统计
   * 为渠道商提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportUserLearningStatisticForChannelVendor(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportUserLearningStatisticForChannelVendor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出学习统计
   * 为参训单位提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportUserLearningStatisticForParticipatingUnit(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportUserLearningStatisticForParticipatingUnit,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出学习统计
   * 为机构提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportUserLearningStatisticForTrainingInstitution(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportUserLearningStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出工种学习统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportWorkTypeLearningStatistic(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportWorkTypeLearningStatistic,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出工种学习统计
   * 为课件供应商提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportWorkTypeLearningStatisticForCoursewareSupplier(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportWorkTypeLearningStatisticForCoursewareSupplier,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出工种学习统计
   * 为参训单位提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportWorkTypeLearningStatisticForParticipatingUnit(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportWorkTypeLearningStatisticForParticipatingUnit,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 导出工种学习统计
   * 为机构提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async exportWorkTypeLearningStatisticForTrainingInstitution(
    request: LearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.exportWorkTypeLearningStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习过程日志记录
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findLearningProcessLog(
    params: { page?: Page; userId?: string; schemeId?: string },
    query: DocumentNode = GraphqlImporter.findLearningProcessLog,
    operation?: string
  ): Promise<Response<LearningProcessCourseInfoDtoPage>> {
    return commonRequestApi<LearningProcessCourseInfoDtoPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户方案学习详情
   * @param userId
   * @param schemeId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getLearningStatisticDetail(
    params: { userId?: string; schemeId?: string },
    query: DocumentNode = GraphqlImporter.getLearningStatisticDetail,
    operation?: string
  ): Promise<Response<LearningStatisticDTO>> {
    return commonRequestApi<LearningStatisticDTO>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据条件获取答题统计合计结果
   * @param answerQuestionStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param answerQuestionStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTotalAnswerQuestionStatistic(
    answerQuestionStatisticQueryParamsDTO: AnswerQuestionStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.getTotalAnswerQuestionStatistic,
    operation?: string
  ): Promise<Response<TotalAnswerQuestionStatisticDTO>> {
    return commonRequestApi<TotalAnswerQuestionStatisticDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { answerQuestionStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通数量统计，忽略金额
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTotalOpenCount(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.getTotalOpenCount,
    operation?: string
  ): Promise<Response<TotalOpenCountStatisticDTO>> {
    return commonRequestApi<TotalOpenCountStatisticDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通统计合计结果
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTotalOpenStatistic(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.getTotalOpenStatistic,
    operation?: string
  ): Promise<Response<TotalOpenStatisticDTO>> {
    return commonRequestApi<TotalOpenStatisticDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通统计合计结果
   * 为渠道商提供接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTotalOpenStatisticForChannelVendor(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.getTotalOpenStatisticForChannelVendor,
    operation?: string
  ): Promise<Response<TotalOpenStatisticDTO>> {
    return commonRequestApi<TotalOpenStatisticDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通统计合计结果
   * 为渠道商提供不强制设置上下文接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTotalOpenStatisticForChannelVendorWithoutContext(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.getTotalOpenStatisticForChannelVendorWithoutContext,
    operation?: string
  ): Promise<Response<TotalOpenStatisticDTO>> {
    return commonRequestApi<TotalOpenStatisticDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通统计合计结果
   * 为课件供应商提供接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTotalOpenStatisticForCoursewareSupplier(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.getTotalOpenStatisticForCoursewareSupplier,
    operation?: string
  ): Promise<Response<TotalOpenStatisticDTO>> {
    return commonRequestApi<TotalOpenStatisticDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通统计合计结果
   * 为机构提供接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param openStatisticQueryParamsDTO 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTotalOpenStatisticForTrainingInstitution(
    openStatisticQueryParamsDTO: OpenStatisticQueryParamsDTO,
    query: DocumentNode = GraphqlImporter.getTotalOpenStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<TotalOpenStatisticDTO>> {
    return commonRequestApi<TotalOpenStatisticDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { openStatisticQueryParamsDTO },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取机构维度课程评价统计
   * @param trainingInstitutionId
   * @return
   * @param query 查询 graphql 语法文档
   * @param trainingInstitutionId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getTrainingInstitutionCourseAppraiseStatistic(
    trainingInstitutionId: string,
    query: DocumentNode = GraphqlImporter.getTrainingInstitutionCourseAppraiseStatistic,
    operation?: string
  ): Promise<Response<TrainingInstitutionCourseAppraiseStatisticDTO>> {
    return commonRequestApi<TrainingInstitutionCourseAppraiseStatisticDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { trainingInstitutionId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户练习作答统计
   * @param userId
   * @param schemeId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getUserPracticeChart(
    params: { userId?: string; schemeId?: string; practiceLearningId?: string },
    query: DocumentNode = GraphqlImporter.getUserPracticeChart,
    operation?: string
  ): Promise<Response<LearningStatisticUserPracticeChartDTO>> {
    return commonRequestApi<LearningStatisticUserPracticeChartDTO>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习统计时间直方图
   * 为渠道商提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningDateHistogramForChannelVendor(
    dateHistogramParam: LearningStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.learningDateHistogramForChannelVendor,
    operation?: string
  ): Promise<Response<DateHistogramDTO>> {
    return commonRequestApi<DateHistogramDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习统计时间直方图
   * 为课件供应商提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningDateHistogramForCoursewareSupplier(
    dateHistogramParam: LearningStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.learningDateHistogramForCoursewareSupplier,
    operation?: string
  ): Promise<Response<DateHistogramDTO>> {
    return commonRequestApi<DateHistogramDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习统计时间直方图
   * 为课件供应商提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningDateHistogramForParticipatingUnit(
    dateHistogramParam: LearningStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.learningDateHistogramForParticipatingUnit,
    operation?: string
  ): Promise<Response<DateHistogramDTO>> {
    return commonRequestApi<DateHistogramDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习统计时间直方图
   * 为机构提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async learningDateHistogramForTrainingInstitution(
    dateHistogramParam: LearningStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.learningDateHistogramForTrainingInstitution,
    operation?: string
  ): Promise<Response<DateHistogramDTO>> {
    return commonRequestApi<DateHistogramDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户方案内所有课程的学习进度
   * @param userId
   * @param schemeId
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserCourseLearningSchedule(
    params: { userId?: string; schemeId?: string; courseLearningId?: string },
    query: DocumentNode = GraphqlImporter.listUserCourseLearningSchedule,
    operation?: string
  ): Promise<Response<Array<LearningStatisticUserCourseLearningScheduleDTO>>> {
    return commonRequestApi<Array<LearningStatisticUserCourseLearningScheduleDTO>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 加载系统最后一次执行评价定时器的时间
   * 数据来源：评价定时器执行日志表
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async loadStatisticDataUpdateTime(
    query: DocumentNode = GraphqlImporter.loadStatisticDataUpdateTime,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据学习统计查询条件督学符合条件的用户
   * @param request
   * @param monitorType
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async monitorStudy(
    params: { request?: LearningStatisticRequest; monitorType?: MonitorTypeEnum },
    query: DocumentNode = GraphqlImporter.monitorStudy,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据学习统计查询条件督学符合条件的用户
   * 渠道商
   * @param request
   * @param monitorType
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async monitorStudyForChannelVendor(
    params: { request?: LearningStatisticRequest; monitorType?: MonitorTypeEnum },
    query: DocumentNode = GraphqlImporter.monitorStudyForChannelVendor,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据学习统计查询条件督学符合条件的用户
   * 参训单位
   * @param request
   * @param monitorType
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async monitorStudyForParticipatingUnit(
    params: { request?: LearningStatisticRequest; monitorType?: MonitorTypeEnum },
    query: DocumentNode = GraphqlImporter.monitorStudyForParticipatingUnit,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据学习统计查询条件督学符合条件的用户
   * 机构
   * @param request
   * @param monitorType
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async monitorStudyForTrainingInstitution(
    params: { request?: LearningStatisticRequest; monitorType?: MonitorTypeEnum },
    query: DocumentNode = GraphqlImporter.monitorStudyForTrainingInstitution,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通统计时间直方图
   * 为渠道商提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async openDateHistogramForChannelVendor(
    dateHistogramParam: OpenStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.openDateHistogramForChannelVendor,
    operation?: string
  ): Promise<Response<DateHistogramDTO>> {
    return commonRequestApi<DateHistogramDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通统计时间直方图
   * 为课件供应商提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async openDateHistogramForCoursewareSupplier(
    dateHistogramParam: OpenStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.openDateHistogramForCoursewareSupplier,
    operation?: string
  ): Promise<Response<DateHistogramDTO>> {
    return commonRequestApi<DateHistogramDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通统计时间直方图
   * 为机构提供接口
   * @return
   * @param query 查询 graphql 语法文档
   * @param dateHistogramParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async openDateHistogramForTrainingInstitution(
    dateHistogramParam: OpenStatisticDateHistogramParamDTO,
    query: DocumentNode = GraphqlImporter.openDateHistogramForTrainingInstitution,
    operation?: string
  ): Promise<Response<DateHistogramDTO>> {
    return commonRequestApi<DateHistogramDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { dateHistogramParam },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据条件获取答题统计分页
   * @param page
   * @param answerQuestionStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageAnswerQuestionStatistic(
    params: { page?: Page; answerQuestionStatisticQueryParamsDTO?: AnswerQuestionStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageAnswerQuestionStatistic,
    operation?: string
  ): Promise<Response<AnswerQuestionStatisticDTOPage>> {
    return commonRequestApi<AnswerQuestionStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页获取课程维度评价统计
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCourseAppraiseStatistic(
    params: { page?: Page; request?: CourseAppraiseStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageCourseAppraiseStatistic,
    operation?: string
  ): Promise<Response<CourseAppraiseStatisticDTOPage>> {
    return commonRequestApi<CourseAppraiseStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页获取课程维度评价统计
   * 为课件供应商提供接口
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCourseAppraiseStatisticForCoursewareSupplier(
    params: { page?: Page; request?: CourseAppraiseStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageCourseAppraiseStatisticForCoursewareSupplier,
    operation?: string
  ): Promise<Response<CourseAppraiseStatisticDTOPage>> {
    return commonRequestApi<CourseAppraiseStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页获取课程维度评价统计
   * 为机构提供接口
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageCourseAppraiseStatisticForTrainingInstitution(
    params: { page?: Page; request?: CourseAppraiseStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageCourseAppraiseStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<CourseAppraiseStatisticDTOPage>> {
    return commonRequestApi<CourseAppraiseStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课程人气统计
   * @param page
   * @param paramsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageHotCourseChooseStatistic(
    params: { page?: Page; paramsDTO?: HotCourseChooseStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageHotCourseChooseStatistic,
    operation?: string
  ): Promise<Response<HotCourseChooseStatisticDTOPage>> {
    return commonRequestApi<HotCourseChooseStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 地区学习统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageRegionLearningStatistic(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageRegionLearningStatistic,
    operation?: string
  ): Promise<Response<RegionLearningStatisticResponsePage>> {
    return commonRequestApi<RegionLearningStatisticResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 地区学习统计
   * 为机构提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageRegionLearningStatisticForTrainingInstitution(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageRegionLearningStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<RegionLearningStatisticResponsePage>> {
    return commonRequestApi<RegionLearningStatisticResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 方案维度课程评价统计
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSchemeCourseAppraiseStatistic(
    params: { page?: Page; request?: SchemeCourseAppraiseStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageSchemeCourseAppraiseStatistic,
    operation?: string
  ): Promise<Response<SchemeCourseAppraiseStatisticDTOPage>> {
    return commonRequestApi<SchemeCourseAppraiseStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 方案维度课程评价统计
   * 为渠道商提供接口
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSchemeCourseAppraiseStatisticForChannelVendor(
    params: { page?: Page; request?: SchemeCourseAppraiseStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageSchemeCourseAppraiseStatisticForChannelVendor,
    operation?: string
  ): Promise<Response<SchemeCourseAppraiseStatisticDTOPage>> {
    return commonRequestApi<SchemeCourseAppraiseStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 方案维度课程评价统计
   * 为机构提供接口
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSchemeCourseAppraiseStatisticForTrainingInstitution(
    params: { page?: Page; request?: SchemeCourseAppraiseStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageSchemeCourseAppraiseStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<SchemeCourseAppraiseStatisticDTOPage>> {
    return commonRequestApi<SchemeCourseAppraiseStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 方案学习统计，带合计行（方案维度）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSchemeLearningStatistic(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageSchemeLearningStatistic,
    operation?: string
  ): Promise<Response<SchemeLearningStatisticResponsePage>> {
    return commonRequestApi<SchemeLearningStatisticResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 方案学习统计，带合计行（方案维度）
   * 为机构提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSchemeLearningStatisticForTrainingInstitution(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageSchemeLearningStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<SchemeLearningStatisticResponsePage>> {
    return commonRequestApi<SchemeLearningStatisticResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通统计分页
   * @param page
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSchemeOpenStatistic(
    params: { page?: Page; openStatisticQueryParamsDTO?: OpenStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageSchemeOpenStatistic,
    operation?: string
  ): Promise<Response<SchemeOpenStatisticDTOPage>> {
    return commonRequestApi<SchemeOpenStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取班级开通统计分页
   * 为渠道供应商提供接口
   * @param page
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSchemeOpenStatisticForChannelVendor(
    params: { page?: Page; openStatisticQueryParamsDTO?: OpenStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageSchemeOpenStatisticForChannelVendor,
    operation?: string
  ): Promise<Response<SchemeOpenStatisticDTOPage>> {
    return commonRequestApi<SchemeOpenStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通统计分页
   * 为课件供应商提供接口
   * @param page
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSchemeOpenStatisticForCoursewareSupplier(
    params: { page?: Page; openStatisticQueryParamsDTO?: OpenStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageSchemeOpenStatisticForCoursewareSupplier,
    operation?: string
  ): Promise<Response<SchemeOpenStatisticDTOPage>> {
    return commonRequestApi<SchemeOpenStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取开通统计分页
   * 为机构提供接口
   * @param page
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSchemeOpenStatisticForTrainingInstitution(
    params: { page?: Page; openStatisticQueryParamsDTO?: OpenStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageSchemeOpenStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<SchemeOpenStatisticDTOPage>> {
    return commonRequestApi<SchemeOpenStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页获取机构维度课程评价统计
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTrainingInstitutionCourseAppraiseStatistic(
    params: { page?: Page; request?: TrainingInstitutionCourseAppraiseStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageTrainingInstitutionCourseAppraiseStatistic,
    operation?: string
  ): Promise<Response<TrainingInstitutionCourseAppraiseStatisticDTOPage>> {
    return commonRequestApi<TrainingInstitutionCourseAppraiseStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 机构学习统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTrainingInstitutionLearningStatistic(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageTrainingInstitutionLearningStatistic,
    operation?: string
  ): Promise<Response<TrainingInstitutionLearningStatisticResponsePage>> {
    return commonRequestApi<TrainingInstitutionLearningStatisticResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取机构开通统计分页
   * @param page
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTrainingInstitutionOpenStatistic(
    params: { page?: Page; openStatisticQueryParamsDTO?: OpenStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageTrainingInstitutionOpenStatistic,
    operation?: string
  ): Promise<Response<TrainingInstitutionOpenStatisticDTOPage>> {
    return commonRequestApi<TrainingInstitutionOpenStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取机构开通统计分页
   * 为渠道商提供接口
   * @param page
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTrainingInstitutionOpenStatisticForChannelVendor(
    params: { page?: Page; openStatisticQueryParamsDTO?: OpenStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageTrainingInstitutionOpenStatisticForChannelVendor,
    operation?: string
  ): Promise<Response<TrainingInstitutionOpenStatisticDTOPage>> {
    return commonRequestApi<TrainingInstitutionOpenStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取机构开通统计分页
   * 为机构提供接口
   * @param page
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTrainingInstitutionOpenStatisticForTrainingInstitution(
    params: { page?: Page; openStatisticQueryParamsDTO?: OpenStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageTrainingInstitutionOpenStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<TrainingInstitutionOpenStatisticDTOPage>> {
    return commonRequestApi<TrainingInstitutionOpenStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习统计分页
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserLearningStatistic(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageUserLearningStatistic,
    operation?: string
  ): Promise<Response<LearningStatisticDTOPage>> {
    return commonRequestApi<LearningStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习统计分页
   * 为渠道商提供接口
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserLearningStatisticForChannelVendor(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageUserLearningStatisticForChannelVendor,
    operation?: string
  ): Promise<Response<LearningStatisticDTOPage>> {
    return commonRequestApi<LearningStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习统计分页
   * 为参训单位提供接口
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserLearningStatisticForParticipatingUnit(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageUserLearningStatisticForParticipatingUnit,
    operation?: string
  ): Promise<Response<LearningStatisticDTOPage>> {
    return commonRequestApi<LearningStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取学习统计分页
   * 为机构提供接口
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserLearningStatisticForTrainingInstitution(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageUserLearningStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<LearningStatisticDTOPage>> {
    return commonRequestApi<LearningStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 用户登录日志
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageUserLoginLog(
    params: { page?: Page; request?: UserLoginLogRequest },
    query: DocumentNode = GraphqlImporter.pageUserLoginLog,
    operation?: string
  ): Promise<Response<UserLoginLogResponsePage>> {
    return commonRequestApi<UserLoginLogResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 工种学习统计（工种维度）
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageWorkTypeLearningStatistic(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageWorkTypeLearningStatistic,
    operation?: string
  ): Promise<Response<WorkTypeLearningStatisticDTOPage>> {
    return commonRequestApi<WorkTypeLearningStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 工种学习统计（工种维度）
   * 为课件供应商提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageWorkTypeLearningStatisticForCoursewareSupplier(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageWorkTypeLearningStatisticForCoursewareSupplier,
    operation?: string
  ): Promise<Response<WorkTypeLearningStatisticDTOPage>> {
    return commonRequestApi<WorkTypeLearningStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 工种学习统计（工种维度）
   * 为参训单位提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageWorkTypeLearningStatisticForParticipatingUnit(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageWorkTypeLearningStatisticForParticipatingUnit,
    operation?: string
  ): Promise<Response<WorkTypeLearningStatisticDTOPage>> {
    return commonRequestApi<WorkTypeLearningStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 工种学习统计（工种维度）
   * 为机构提供接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageWorkTypeLearningStatisticForTrainingInstitution(
    params: { page?: Page; request?: LearningStatisticRequest },
    query: DocumentNode = GraphqlImporter.pageWorkTypeLearningStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<WorkTypeLearningStatisticDTOPage>> {
    return commonRequestApi<WorkTypeLearningStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询每个工种报名数
   * 为渠道商提供接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageWorkTypeOpenStatisticForChannelVendor(
    params: { page?: Page; openStatisticQueryParamsDTO?: OpenStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageWorkTypeOpenStatisticForChannelVendor,
    operation?: string
  ): Promise<Response<WorkTypeOpenStatisticDTOPage>> {
    return commonRequestApi<WorkTypeOpenStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询工种报名数
   * 为课件供应商提供接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageWorkTypeOpenStatisticForCoursewareSupplier(
    params: { page?: Page; openStatisticQueryParamsDTO?: OpenStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageWorkTypeOpenStatisticForCoursewareSupplier,
    operation?: string
  ): Promise<Response<WorkTypeOpenStatisticDTOPage>> {
    return commonRequestApi<WorkTypeOpenStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询每个工种报名数
   * 为机构提供接口
   * @param openStatisticQueryParamsDTO
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageWorkTypeOpenStatisticForTrainingInstitution(
    params: { page?: Page; openStatisticQueryParamsDTO?: OpenStatisticQueryParamsDTO },
    query: DocumentNode = GraphqlImporter.pageWorkTypeOpenStatisticForTrainingInstitution,
    operation?: string
  ): Promise<Response<WorkTypeOpenStatisticDTOPage>> {
    return commonRequestApi<WorkTypeOpenStatisticDTOPage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 方案sku学习统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async schemeSkuLearningStatistic(
    request: SchemeSkuLearningStatisticRequest,
    query: DocumentNode = GraphqlImporter.schemeSkuLearningStatistic,
    operation?: string
  ): Promise<Response<Array<SchemeSkuLearniningStatisticsResponse>>> {
    return commonRequestApi<Array<SchemeSkuLearniningStatisticsResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 统计在线学习人数专业分布
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserOnlineMajorDistribute(
    param: DashboardParam,
    query: DocumentNode = GraphqlImporter.statisticUserOnlineMajorDistribute,
    operation?: string
  ): Promise<Response<Array<UserOnLineMajorDistributeResponse>>> {
    return commonRequestApi<Array<UserOnLineMajorDistributeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 统计在线学习人数终端分布
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserOnlineTerminalDistribute(
    param: DashboardParam,
    query: DocumentNode = GraphqlImporter.statisticUserOnlineTerminalDistribute,
    operation?: string
  ): Promise<Response<Array<UserOnLineTerminalDistributeResponse>>> {
    return commonRequestApi<Array<UserOnLineTerminalDistributeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 统计在线学习人数单位分布
   * @param param
   * @return
   * @param query 查询 graphql 语法文档
   * @param param 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async statisticUserOnlineUnitDistribute(
    param: DashboardParam,
    query: DocumentNode = GraphqlImporter.statisticUserOnlineUnitDistribute,
    operation?: string
  ): Promise<Response<Array<UserOnLineUnitDistributeResponse>>> {
    return commonRequestApi<Array<UserOnLineUnitDistributeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { param },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 课程维度评价统计合计行，综合得分
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async totalCourseAverage(
    request: CourseAppraiseStatisticRequest,
    query: DocumentNode = GraphqlImporter.totalCourseAverage,
    operation?: string
  ): Promise<Response<CourseAppraiseStatisticTotalRowDTO>> {
    return commonRequestApi<CourseAppraiseStatisticTotalRowDTO>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
