import {
  CommoditySkuBackstageResponse,
  CommoditySkuPropertyResponse,
  SchemeResourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import SkuPropertyResponseVo from '@api/service/management/train-class/query/vo/SkuPropertyResponseVo'
import { TrainClassSchemeEnum } from '@api/service/management/train-class/query/enum/TrainClassSchemeType'
import {
  ComplexSkuPropertyResponse,
  SkuPropertyConvertUtils
} from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import SchemeConfigUtils from '@api/service/management/train-class/Utils/SchemeConfigUtils'
import DateScope from '@api/service/common/models/DateScope'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'

/**
 * @description 可替换班级列表内详情
 */
class ReplaceableTrainClassDetailVo {
  /**
   * 班级id
   */
  schemeId = ''

  /**
   * 培训班商品Id，用于换货时使用
   */
  commoditySkuId = ''

  /**
   * 班级上的sku属性值，类型为CommoditySkuPropertyResponse
   */
  skuProperty: CommoditySkuPropertyResponse = new CommoditySkuPropertyResponse()

  /**
   * 班级上的sku属性值，类型为SkuPropertyResponseVo
   */
  skuValueNameProperty: SkuPropertyResponseVo = new SkuPropertyResponseVo()

  /**
   * 培训方案类型
   * 1: 选课规则
   * 2: 自主选课
   */
  schemeType: TrainClassSchemeEnum = null

  /**
   * 培训方案名称
   */
  schemeName = ''

  /**
   * 培训班价格
   */
  price = 0

  /**
   * 学时
   */
  period = 0

  /**
   * 学习起止时间
   */
  learningDateScope: DateScope = new DateScope()

  /**
   * 报名起止时间
   */
  registerDateScope: DateScope = new DateScope()

  /**
   * 方案期别数量
   */
  issueCount = 0

  /**
   * 转换远端模型为本地vo模型
   */
  static async from(response: CommoditySkuBackstageResponse): Promise<ReplaceableTrainClassDetailVo> {
    const detail = new ReplaceableTrainClassDetailVo()
    detail.schemeId = (response.resource as SchemeResourceResponse)?.schemeId
    detail.commoditySkuId = response.commoditySkuId || ''
    detail.skuProperty = response.skuProperty
    detail.schemeName = response.commodityBasicData?.saleTitle
    detail.price = response.commodityBasicData?.price
    detail.issueCount = response.issueInfoResponse?.issueCount
    if (response.resource) {
      const resource = response.resource as SchemeResourceResponse
      detail.period = resource.period
    }

    try {
      detail.skuValueNameProperty = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
        detail.skuProperty as ComplexSkuPropertyResponse
      )
      const configJson = await SchemeConfigUtils.queryConfig(detail.schemeId)
      if (configJson) {
        detail.schemeType = SchemeTypeEnum[configJson.type as string]
        detail.learningDateScope.begin = configJson.trainingBeginDate
        detail.learningDateScope.end = configJson.trainingEndDate
        detail.registerDateScope.begin = configJson.registerBeginDate
        detail.registerDateScope.end = configJson.registerEndDate
      }
    } catch (e) {
      console.log(e)
    }

    return detail
  }

  /**
   * 获取培训方案类型
   */
  static getSchemeType(type: any): TrainClassSchemeEnum | null {
    if (type == 'chooseCourseLearning') {
      return TrainClassSchemeEnum.Choose_Course_Learning
    } else {
      return TrainClassSchemeEnum.Autonomous_Course_Learning
    }
  }
}

export default ReplaceableTrainClassDetailVo
