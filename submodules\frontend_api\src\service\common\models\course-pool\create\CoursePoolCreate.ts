import CourseInCoursePool from '@api/service/common/models/course-pool/CourseInCoursePool'

/**
 * 创建课程包
 * <AUTHOR> update 2021/1/28  TODO
 */
class CoursePoolCreate {
  /**
   * 课程供应商id【必填】
   */
  coursewareSupplierId?: string
  /**
   * 必填，课程池名称
   */
  poolName?: string = ''
  /**
   * 过期时间,null表示不设置过期
   */
  expireTime?: string = ''
  /**
   * 课程池描述
   */
  poolDescription?: string = ''
  /**
   * 展示名称
   */
  showName?: string = ''

  /**
   * 包内课程
   */
  courseInPool?: Array<CourseInCoursePool>
  /**
   * 课程包所属单位id
   */
  unitId?: string = ''
}

export default CoursePoolCreate
