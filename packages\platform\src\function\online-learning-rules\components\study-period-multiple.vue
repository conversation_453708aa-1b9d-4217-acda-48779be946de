<template>
  <el-select
    v-model="selected"
    :placeholder="placeholder"
    @clear="clearSelected"
    class="form-l"
    filterable
    clearable
    multiple
  >
    <el-option
      v-for="item in studyPeriodOptions"
      :label="item.name"
      :value="item.propertyId"
      :key="item.propertyId"
    ></el-option>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import QueryGrade from '@api/service/common/basic-data-dictionary/query/QueryGrade'
  import { TrainingPropertyResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

  @Component
  export default class extends Vue {
    selected: string[] = []
    // 培训类别选项
    studyPeriodOptions = new Array<TrainingPropertyResponse>()

    @Prop({
      type: String,
      default: '请选培训类别'
    })
    placeholder: string

    //行业属性分类id
    @Prop({
      type: String,
      default: ''
    })
    industryPropertyId: string

    @Prop({
      type: Array,
      default: ''
    })
    value: string[]

    @Watch('value', {
      deep: true,
      immediate: true
    })
    valueChange(val: string[]) {
      this.selected = val
    }

    @Emit('input')
    selectedChange() {
      this.$emit('updateStudyPeriod', this.selected)
      return this.selected
    }

    @Watch('industryPropertyId', {
      immediate: true,
      deep: true
    })
    async industryPropertyIdChange() {
      await this.getTrainingCategoryOptions()
    }

    clearSelected() {
      this.$emit('clearSubject')
    }

    /**
     * 获取培训类别
     */
    async getTrainingCategoryOptions() {
      await QueryGrade.queryGradeByIndustry()
      this.studyPeriodOptions = QueryGrade.gradeList
    }
  }
</script>
