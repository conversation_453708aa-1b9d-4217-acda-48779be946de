import { CommoditySkuRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { DateScopeRequest } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
export class CommoditySkuRequestVo extends CommoditySkuRequest {
  /**
   * 报名时间
   */
  registerTime?: DateScopeRequest

  /**
   * 是否来源专题
   */
  saleChannel = new Array<number>()

  /**
   * 专题名称
   */
  trainingChannelName = ''
  /**
   * 分销商名称
   */
  distributorName = ''
  /**
   * 分销商Id
   */
  distributorId = ''
  /**
   * 推广门户别名
   */
  promoteThePortalAlias = ''
  /**
   * 推广门户别名
   */
  promoteThePortalId = ''
  /**
   * 是否非门户推广数据
   */
  notDistributionPortal = false
  /**
 * 培训形式
 * trainingWay0001：网授
 * trainingWay0002：面网授
 */
  trainingType = 'trainingWay0001'
}
