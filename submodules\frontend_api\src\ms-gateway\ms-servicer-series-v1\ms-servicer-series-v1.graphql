"""独立部署的微服务,K8S服务名:ms-servicer-series-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取学员工作单位对接天眼查企查查配置
		@return
	"""
	getDockingTycAndQcc:DockingTycAndQccResponse @optionalLogin
	"""获取精品课程信息
		@return  精品课程信息
	"""
	getExcellentCourses:ExcellentCoursesResponse @optionalLogin
	"""获取学员登录时强制修改初始密码配置"""
	getForceModifyInitialPassword:StudentForceModifyInitialPasswordConfigResponse @optionalLogin
	"""获取服务商专属行业列表
		@return 行业字典数据列表
	"""
	getIndustries:[IndustryDictionaryResponse] @optionalLogin
	"""获取线下集体报名配置
		@return 线下集体报名配置信息
	"""
	getOfflineCollectiveRegisterConfig:OfflineCollectiveRegisterConfigResponse @optionalLogin
	"""获取线下集体报名配置（需授权）
		@return 线下集体报名配置信息
	"""
	getOfflineCollectiveRegisterConfigWithAuth:OfflineCollectiveRegisterConfigResponse
	"""获取线上集体报名配置
		@return 线上集体报名配置信息
	"""
	getOnlineCollectiveRegisterConfig:OnlineCollectiveRegisterConfigResponse @optionalLogin
	"""获取网校配置"""
	getOnlineSchoolConfig:OnlineSchoolConfigResponse @optionalLogin
	getOnlineSchoolProtolConfig:OnlineSchoolProtocolConfigResponse @optionalLogin
	"""获取服务商物理专属地区列表
		@return 地区字典数据列表
	"""
	getPhysicalRegions:[PhysicalRegionDictionaryResponse] @optionalLogin
	"""获取服务商业务专属地区列表
		@return 地区字典数据列表
	"""
	getRegions:[RegionDictionaryResponse] @optionalLogin
	"""获取学员注册表单约束信息
		<p>服务商（培训机构）学员注册页面表单约束构建使用</p>
		@return 学员注册表单约束信息 null表示未开放学员注册
	"""
	getStudentRegisterFormConstraint:StudentRegisterFormConstraintResponse @optionalLogin
	"""获取服务商注册
		@return
	"""
	getStudentRegisterFormIdCardTypeList:StudentRegisterFormIdCardTypeResponse @optionalLogin
	"""获取学员注册表单约束配置信息
		@return 学员注册表单约束配置信息
	"""
	getStudentResisterFormConstraintForConfig:StudentRegisterFormConstraintConfigResponse @optionalLogin
	"""获取学员微信登录配置
		@return 学员微信登录配置信息
	"""
	getWechatLoginConfig:WechatLoginConfigResponse @optionalLogin
	"""获取服务商专属年度列表
		@return 年度字典数据列表
	"""
	getYears:[YearDictionaryResponse] @optionalLogin
	"""查询线下集体报名配置是否已填写
		@return true：已填写，false：未填写
	"""
	isOfflineCollectiveRegisterConfigFilled:Boolean!
}
type Mutation {
	"""存在而存在的口"""
	save:Void
	"""学员工作单位对接天眼查/企查查-配置"""
	saveDockingTycAndQccConfig(request:DockingTycAndQccRequest):Void
	"""保存精品课程信息
		@param request 精品课程信息
	"""
	saveExcellentCoursesConfig(request:ExcellentCoursesSaveRequest):Void
	"""保存学员登录时强制修改初始密码配置"""
	saveForceModifyInitialPassword(request:StudentForceModifyInitialPasswordConfigRequest):Void
	"""保存线下集体报名配置
		@param request 【必填】线下集体报名配置
	"""
	saveOfflineCollectiveRegisterConfig(request:OfflineCollectiveRegisterConfigSaveRequest):Void
	"""保存线上集体报名配置
		@param request 【必填】线上集体报名配置
	"""
	saveOnlineCollectiveRegisterConfig(request:OnlineCollectiveRegisterConfigSaveRequest):Void
	"""保存网校协议配置
		@param request 网校协议入参
	"""
	saveOnlineSchoolProtocolConfig(request:OnlineSchoolProtocolConfigRequest):Void
	"""保存网校SEO配置"""
	saveOnlineSchoolSEOConfig(request:OnlineSchoolSEOConfigRequest):Void
	"""保存学员注册表单约束配置
		@param request 【必填】表单约束配置信息
	"""
	saveStudentRegisterFormConstraintConfig(request:StudentRegisterFormConstraintConfigRequest):Void
	"""保存学员注册表单约束配置
		@param request 【必填】表单约束配置信息
	"""
	saveStudentRegisterFormIdCardTypeList(request:StudentRegisterIdCardTypeConfigRequest):Void
	"""保存验证设置"""
	saveValidateMethodConfig(request:ValidateMethodConfigRequest):Void
	"""保存学员微信登录配置
		@param request 学员微信登录配置信息
	"""
	saveWechatLoginConfig(request:WechatLoginConfigSaveRequest):Void
	"""修改培训类别名称"""
	updateTrainingCategoryName(request:UpdateTrainingCategoriesRequest):GeneraleResponse
}
"""附件信息请求"""
input AttachmentRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.AttachmentRequest") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
"""对接信息"""
input DockingInfoRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.DockingInfoRequest") {
	"""【必填】服务商类型
		1.天眼查
		2.企查查
	"""
	serviceType:Int
	"""【必填】对接账号信息"""
	dockingAccount:String
	"""秘钥(企查查使用)"""
	secret:String
}
"""学员工作单位对接天眼查/企查查请求类"""
input DockingTycAndQccRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.DockingTycAndQccRequest") {
	"""【必填】是否启用工作单位对接天眼查企查查配置"""
	enabled:Boolean!
	"""表单字段约束集合，集合为null不更新"""
	dockingInfoList:[DockingInfoRequest]
}
"""精品课程信息
	<AUTHOR>
"""
input ExcellentCourseItemRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.ExcellentCourseItemRequest") {
	"""课程ID"""
	courseId:String
	"""排序"""
	sort:Int!
}
"""精品课程分类信息
	<AUTHOR>
"""
input ExcellentCoursesCategoryRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.ExcellentCoursesCategoryRequest") {
	"""课程关联的分类ID"""
	categoryId:String
	"""排序"""
	sort:Int!
	"""精品课程列表"""
	courses:[ExcellentCourseItemRequest]
}
"""精品课程信息
	<AUTHOR>
"""
input ExcellentCoursesSaveRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.ExcellentCoursesSaveRequest") {
	"""是否使用分类"""
	usedCategory:Boolean!
	"""精品课程分类
		<p>使用分类时设置</p>
	"""
	categories:[ExcellentCoursesCategoryRequest]
	"""精品课程列表
		<p>不使用分类时设置</p>
	"""
	courses:[ExcellentCourseItemRequest]
}
"""表单字段约束信息
	<AUTHOR>
"""
input FieldConstraintRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.FieldConstraintRequest") {
	"""【必填】字段名称"""
	field:String
	"""【必填】是否为关联字段，表示需要其他字段选择值是才会出现该字段"""
	relate:Boolean!
	"""【必填】字段验证器集合"""
	validators:[FieldConstraintValidatorRequest]
}
"""字段验证器信息
	<AUTHOR>
"""
input FieldConstraintValidatorRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.FieldConstraintValidatorRequest") {
	"""【必填】验证器类型，多种类型使用 | 符号分割，如require|email"""
	type:String
	"""关联的字段名称
		<p>如果{@link FieldConstraintRequest#isRelate()}为true 必填</p>
	"""
	relateField:String
	"""关联字段的值集合
		<p>如果{@link FieldConstraintRequest#isRelate()}为true 必填</p>
	"""
	values:[String]
}
input IdCardTypeModelRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.IdCardTypeModelRequest") {
	"""【必填】字段名称"""
	idCardTypeName:String
	"""【必填】字段值"""
	idCardTypeValue:Int!
	"""是否可选"""
	select:Boolean!
}
"""线下集体报名配置信息
	<AUTHOR>
"""
input OfflineCollectiveRegisterConfigSaveRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.OfflineCollectiveRegisterConfigSaveRequest") {
	"""是否启用"""
	enabled:Boolean!
	"""线下集体报名入口图片"""
	registerPortalPicture:[AttachmentRequest]
	"""线下集体报名标题"""
	title:String
	"""报名模板路径"""
	templatePath:String
	"""报名模板文件名"""
	templateFileName:String
	"""报名步骤集合"""
	steps:[OfflineCollectiveRegisterStepRequest]
	"""底部内容"""
	footContent:String
}
"""线下集体报名步骤信息
	<AUTHOR>
"""
input OfflineCollectiveRegisterStepRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.OfflineCollectiveRegisterStepRequest") {
	"""步骤序号"""
	no:Int!
	"""步骤名称"""
	name:String
	"""步骤内容"""
	content:String
}
"""线上集体报名配置信息
	<AUTHOR>
"""
input OnlineCollectiveRegisterConfigSaveRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.OnlineCollectiveRegisterConfigSaveRequest") {
	"""是否启用"""
	enabled:Boolean!
	"""是否启用线上集体报名入口图片"""
	enabledPicture:Boolean!
	"""线上集体报名入口图片"""
	registerPortalPicture:[AttachmentRequest]
	"""报名模板路径"""
	templatePath:String
	"""报名模板文件名"""
	templateFileName:String
}
"""@Description 网校协议配置
	<AUTHOR>
	@Date 2025/3/14 9:57
"""
input OnlineSchoolProtocolConfigRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.OnlineSchoolProtocolConfigRequest") {
	"""是否开启注册协议"""
	hasRegisterProtocol:Boolean!
	"""注册协议名称"""
	registerProtocolName:String
	"""注册协议内容"""
	registerProtocolContent:String
	"""是否开启登录协议"""
	hasLoginProtocol:Boolean!
	"""登录协议名称"""
	loginProtocolName:String
	"""登录协议内容"""
	loginProtocolContent:String
}
input OnlineSchoolSEOConfigRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.OnlineSchoolSEOConfigRequest") {
	"""描述"""
	description:String
	"""关键词"""
	keywords:String
}
"""学员初始密码强制修改配置"""
input StudentForceModifyInitialPasswordConfigRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.StudentForceModifyInitialPasswordConfigRequest") {
	"""是否开启学员初始密码强制修改,开启为true，否则为false"""
	enabledModifyInitPassword:Boolean!
}
"""学员注册表单约束信息
	<AUTHOR>
"""
input StudentRegisterFormConstraintConfigRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.StudentRegisterFormConstraintConfigRequest") {
	"""【必填】是否启用学员注册"""
	enabled:Boolean!
	"""是否强制完善学员信息"""
	forceCompleteStuInfo:Boolean!
	"""表单字段约束集合，集合为null不更新"""
	fieldConstraints:[FieldConstraintRequest]
	"""是否开启证件照"""
	enableIdCardPhoto:Boolean
	"""证件照尺寸
		@see com.fjhb.domain.basicdata.api.series.consts.IdCardSizes
		{@link this#getEnableIdCardPhoto()}  为true时(开启证件照)，需要设置该属性
	"""
	idCardSize:String
}
input StudentRegisterIdCardTypeConfigRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.StudentRegisterIdCardTypeConfigRequest") {
	responseList:[IdCardTypeModelRequest]
}
input UpdateTrainingCategoriesRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.UpdateTrainingCategoriesRequest") {
	"""培训类别id
		培训类别中每个元素模型包含id和name
		name有值.id必须传值，不传值不修改
		name无值，id传值，把name置空
	"""
	id:String
	"""培训类别名称"""
	name:String
}
input ValidateMethodConfigRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.ValidateMethodConfigRequest") {
	"""是否启用验证码,默认启用验证码"""
	enabledCaptcha:Boolean!
	"""是否启用滑块拼图"""
	enabledSlideCircus:Boolean!
}
"""学员微信扫码登录配置信息
	<AUTHOR>
"""
input WechatLoginConfigSaveRequest @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.request.WechatLoginConfigSaveRequest") {
	"""是否启用"""
	enabled:Boolean!
	"""扫码登录appId"""
	qrAppId:String
	"""扫码登录appSecret"""
	qrAppSecret:String
	"""是否启用微信授权登录设置"""
	enabledAuth:Boolean!
	"""授权登录appId"""
	authAppId:String
	"""授权登录appSecret"""
	authAppSecret:String
	"""是否启用账号登录设置"""
	enabledAccLogin:Boolean!
	"""登陆认证设置，实名认证设置，是否启用登陆实名认证
		遴选版本默认实名认证
	"""
	enabledRealName:Boolean
}
"""附件信息"""
type Attachment @type(value:"com.fjhb.ms.servicerseries.v1.kernel.collectiveregister.model.Attachment") {
	"""附件名称"""
	name:String
	"""附件地址"""
	url:String
}
type DockingInfoResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.DockingInfoResponse") {
	"""服务商类型类型"""
	serviceType:Int
	"""对接账号信息"""
	dockingAccount:String
	"""秘钥(企查查使用)"""
	secret:String
}
type DockingTycAndQccResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.DockingTycAndQccResponse") {
	"""是否启动对接天眼查/企查查"""
	enabled:Boolean!
	"""对接信息"""
	dockingInfoList:[DockingInfoResponse]
}
"""精品课程信息
	<AUTHOR>
"""
type ExcellentCourseItemResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.ExcellentCourseItemResponse") {
	"""课程ID"""
	courseId:String
	"""排序"""
	sort:Int!
	"""创建时间"""
	createdTime:DateTime
}
"""精品课程分类信息
	<AUTHOR>
"""
type ExcellentCoursesCategoryResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.ExcellentCoursesCategoryResponse") {
	"""分类名称"""
	categoryId:String
	"""排序"""
	sort:Int!
	"""创建时间"""
	createdTime:DateTime
	"""精品课程列表"""
	courses:[ExcellentCourseItemResponse]
}
"""精品课程信息
	<AUTHOR>
"""
type ExcellentCoursesResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.ExcellentCoursesResponse") {
	"""是否使用分类"""
	usedCategory:Boolean!
	"""精品课程分类
		<p>使用分类时读取</p>
	"""
	categories:[ExcellentCoursesCategoryResponse]
	"""精品课程列表
		<p>不使用分类时读取</p>
	"""
	courses:[ExcellentCourseItemResponse]
}
"""表单字段约束信息
	<AUTHOR>
"""
type FieldConstraintResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.FieldConstraintResponse") {
	"""字段名称"""
	field:String
	"""是否为关联字段，表示需要其他字段选择值是才会出现该字段"""
	relate:Boolean!
	"""字段验证器集合"""
	validators:[FieldConstraintValidatorResponse]
}
"""字段验证器信息
	<AUTHOR>
"""
type FieldConstraintValidatorResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.FieldConstraintValidatorResponse") {
	"""验证器类型，多种类型使用 | 符号分割，如require|email"""
	type:String
	"""关联的字段名称"""
	relateField:String
	"""关联字段的值集合"""
	values:[String]
}
"""通用响应类"""
type GeneraleResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.GeneraleResponse") {
	"""状态码
		@see CommonStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
type IdCardTypeModelResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.IdCardTypeModelResponse") {
	"""【必填】字段名称"""
	idCardTypeName:String
	"""【必填】字段值"""
	idCardTypeValue:Int!
	"""是否可选"""
	select:Boolean!
}
"""行业字典数据
	<AUTHOR>
"""
type IndustryDictionaryResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.IndustryDictionaryResponse") {
	"""行业字典数据ID"""
	id:String
	"""行业属性字典ID"""
	propertyId:String
	"""排序"""
	sort:Int!
}
"""线下集体报名配置信息
	<AUTHOR>
"""
type OfflineCollectiveRegisterConfigResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.OfflineCollectiveRegisterConfigResponse") {
	"""【必填】配置ID"""
	id:String
	"""是否启用"""
	enabled:Boolean!
	"""线下集体报名入口图片"""
	registerPortalPicture:[Attachment]
	"""线下集体报名标题"""
	title:String
	"""报名模板路径"""
	templatePath:String
	"""报名模板文件名"""
	templateFileName:String
	"""报名步骤集合"""
	steps:[OfflineCollectiveRegisterStepResponse]
	"""底部内容"""
	footContent:String
}
"""线下集体报名步骤信息
	<AUTHOR>
"""
type OfflineCollectiveRegisterStepResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.OfflineCollectiveRegisterStepResponse") {
	"""步骤序号"""
	no:Int!
	"""步骤名称"""
	name:String
	"""步骤内容"""
	content:String
}
"""线上集体报名配置信息
	<AUTHOR>
"""
type OnlineCollectiveRegisterConfigResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.OnlineCollectiveRegisterConfigResponse") {
	"""是否启用"""
	enabled:Boolean!
	"""报名模板路径"""
	templatePath:String
	"""报名模板文件名"""
	templateFileName:String
	"""是否启用线上集体报名入口图片"""
	enabledPicture:Boolean!
	"""线上集体报名入口图片"""
	registerPortalPicture:[Attachment]
}
type OnlineSchoolConfigResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.OnlineSchoolConfigResponse") {
	"""网校seo"""
	seoConfig:OnlineSchoolSEO
	"""强制完善信息：默认为true,
		true：当学员信息不全时，强制触发完善信息页面
		false：当学员信息不全时，强制跳过完善信息页面
	"""
	enabledForceCompleteInfo:Boolean!
	"""是否开启学员初始密码强制修改,默认为false"""
	enabledModifyInitPassword:Boolean!
	"""验证方式配置,默认启用验证码验证方式"""
	validateMethodConfig:ValidateMethods
}
"""@Description 网校协议配置响应
	<AUTHOR>
	@Date 2025/3/18 9:57
"""
type OnlineSchoolProtocolConfigResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.OnlineSchoolProtocolConfigResponse") {
	"""是否开启注册协议"""
	hasRegisterProtocol:Boolean!
	"""注册协议名称"""
	registerProtocolName:String
	"""注册协议内容"""
	registerProtocolContent:String
	"""是否开启登录协议"""
	hasLoginProtocol:Boolean!
	"""登录协议名称"""
	loginProtocolName:String
	"""登录协议内容"""
	loginProtocolContent:String
}
"""物理地区字典数据
	<AUTHOR>
"""
type PhysicalRegionDictionaryResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.PhysicalRegionDictionaryResponse") {
	"""地区字典数据ID"""
	id:String
	"""排序"""
	sort:Int!
}
"""业务地区字典数据
	<AUTHOR>
"""
type RegionDictionaryResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.RegionDictionaryResponse") {
	"""地区字典数据ID"""
	id:String
	"""排序"""
	sort:Int!
}
type StudentForceModifyInitialPasswordConfigResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.StudentForceModifyInitialPasswordConfigResponse") {
	"""是否开启学员初始密码强制修改"""
	enabledModifyInitPassword:Boolean!
}
"""学员注册表单注册配置信息
	<AUTHOR>
"""
type StudentRegisterFormConstraintConfigResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.StudentRegisterFormConstraintConfigResponse") {
	"""是否启用学员注册"""
	enabled:Boolean!
	"""是否强制完善学员信息"""
	forceCompleteStuInfo:Boolean!
	"""表单字段约束集合"""
	fieldConstraints:[FieldConstraintResponse]
	"""是否开启证件照"""
	enableIdCardPhoto:Boolean
	"""证件照尺寸
		@see com.fjhb.domain.basicdata.api.series.consts.IdCardSizes
		{@link this#getEnableIdCardPhoto()}  为true时(开启证件照)，需要设置该属性
	"""
	idCardSize:String
}
"""学员注册表单注册配置信息
	<AUTHOR>
"""
type StudentRegisterFormConstraintResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.StudentRegisterFormConstraintResponse") {
	"""约束验证Token
		<p>用于学员注册页面提交时传递给学员注册接口的约束验证token</p>
	"""
	token:String
	"""表单字段约束集合"""
	fieldConstraints:[FieldConstraintResponse]
}
type StudentRegisterFormIdCardTypeResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.StudentRegisterFormIdCardTypeResponse") {
	responseList:[IdCardTypeModelResponse]
}
"""学员微信授权登录配置信息
	<AUTHOR>
"""
type WechatLoginConfigResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.WechatLoginConfigResponse") {
	"""是否启用"""
	enabled:Boolean!
	"""扫码登录appId"""
	qRAppId:String
	"""扫码登录appSecret"""
	qRAppSecret:String
	"""是否启用微信授权登录设置"""
	enabledAuth:Boolean!
	"""授权登录appId"""
	authAppId:String
	"""授权登录appSecret"""
	authAppSecret:String
	"""是否启用账号登录设置"""
	enabledAccLogin:Boolean!
	"""登陆认证设置，实名认证设置，是否启用登陆实名认证
		遴选版本默认实名认证
	"""
	enabledRealName:Boolean
}
"""年度字典数据
	<AUTHOR>
"""
type YearDictionaryResponse @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.YearDictionaryResponse") {
	"""年度字典数据ID"""
	id:String
	"""排序"""
	sort:Int!
}
type OnlineSchoolSEO @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.model.OnlineSchoolSEO") {
	"""描述"""
	description:String
	"""关键词"""
	keywords:String
}
type ValidateMethods @type(value:"com.fjhb.ms.servicerseries.v1.kernel.gateway.graphql.response.model.ValidateMethods") {
	"""是否启用验证码,默认启用验证码"""
	enabledCaptcha:Boolean!
	"""是否启用滑块拼图"""
	enabledSlideCircus:Boolean!
}

scalar List
