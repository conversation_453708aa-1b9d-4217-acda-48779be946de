import affirmOfflineOrderRefund from './mutates/affirmOfflineOrderRefund.graphql'
import affirmOfflineRefund from './mutates/affirmOfflineRefund.graphql'
import agreeBatchRefund from './mutates/agreeBatchRefund.graphql'
import agreeBatchRefundApply from './mutates/agreeBatchRefundApply.graphql'
import agreeRefund from './mutates/agreeRefund.graphql'
import agreeRefundApply from './mutates/agreeRefundApply.graphql'
import applyBatchRefund from './mutates/applyBatchRefund.graphql'
import applyBatchRefundCheck from './mutates/applyBatchRefundCheck.graphql'
import applyRefund from './mutates/applyRefund.graphql'
import applyRefundCheck from './mutates/applyRefundCheck.graphql'
import cancelBatchOrder from './mutates/cancelBatchOrder.graphql'
import cancelBatchRefund from './mutates/cancelBatchRefund.graphql'
import cancelOrder from './mutates/cancelOrder.graphql'
import cancelRefundApply from './mutates/cancelRefundApply.graphql'
import commitBatchOrder from './mutates/commitBatchOrder.graphql'
import confirmBatchOrderOfflinePay from './mutates/confirmBatchOrderOfflinePay.graphql'
import createBatchOrder from './mutates/createBatchOrder.graphql'
import createBatchOrderAndOrder from './mutates/createBatchOrderAndOrder.graphql'
import createOrder from './mutates/createOrder.graphql'
import forceCancelBatchOrder from './mutates/forceCancelBatchOrder.graphql'
import forceCloseOrder from './mutates/forceCloseOrder.graphql'
import hideOrder from './mutates/hideOrder.graphql'
import offlinePayBatchOrder from './mutates/offlinePayBatchOrder.graphql'
import onlinePayBatchOrder from './mutates/onlinePayBatchOrder.graphql'
import payOrder from './mutates/payOrder.graphql'
import rejectBatchRefund from './mutates/rejectBatchRefund.graphql'
import rejectBatchRefundApply from './mutates/rejectBatchRefundApply.graphql'
import rejectRefund from './mutates/rejectRefund.graphql'
import rejectRefundApply from './mutates/rejectRefundApply.graphql'
import removeBatchOrderInvoice from './mutates/removeBatchOrderInvoice.graphql'
import retryOnlineRefund from './mutates/retryOnlineRefund.graphql'
import retrySubOrderRecycleProduct from './mutates/retrySubOrderRecycleProduct.graphql'
import updateBatchOrderInvoice from './mutates/updateBatchOrderInvoice.graphql'
import updateOrderInvoice from './mutates/updateOrderInvoice.graphql'

export {
  affirmOfflineOrderRefund,
  affirmOfflineRefund,
  agreeBatchRefund,
  agreeBatchRefundApply,
  agreeRefund,
  agreeRefundApply,
  applyBatchRefund,
  applyBatchRefundCheck,
  applyRefund,
  applyRefundCheck,
  cancelBatchOrder,
  cancelBatchRefund,
  cancelOrder,
  cancelRefundApply,
  commitBatchOrder,
  confirmBatchOrderOfflinePay,
  createBatchOrder,
  createBatchOrderAndOrder,
  createOrder,
  forceCancelBatchOrder,
  forceCloseOrder,
  hideOrder,
  offlinePayBatchOrder,
  onlinePayBatchOrder,
  payOrder,
  rejectBatchRefund,
  rejectBatchRefundApply,
  rejectRefund,
  rejectRefundApply,
  removeBatchOrderInvoice,
  retryOnlineRefund,
  retrySubOrderRecycleProduct,
  updateBatchOrderInvoice,
  updateOrderInvoice
}
