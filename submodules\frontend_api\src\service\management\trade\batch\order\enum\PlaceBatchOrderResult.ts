import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 【集体报名订单】下单结果枚举
 */
export enum PlaceBatchOrderResultEnum {
  // 处理中
  Processing = 1,
  // 处理完成
  Complete_Process
}

/**
 * @description 【集体报名订单】下单结果列表
 */
class PlaceBatchOrderResult extends AbstractEnum<PlaceBatchOrderResultEnum> {
  static enum = PlaceBatchOrderResultEnum
  constructor(status?: PlaceBatchOrderResultEnum) {
    super()
    this.current = status
    this.map.set(PlaceBatchOrderResultEnum.Processing, '处理中')
    this.map.set(PlaceBatchOrderResultEnum.Complete_Process, '处理完成')
  }
}

export default new PlaceBatchOrderResult()
