/**
 * 打平数组组装树返回
 */
export default class AssembleTree<T> {
  allData: Array<T>
  idStr: string
  fatherIdStr: string
  childrenStr: string
  dataMap: Map<string, T>

  /**
   *
   * @param allData 打平的数组
   * @param idStr 父节点ID的对象Key值
   * @param fatherIdStr 子节点对应的父节点ID的对象key值
   * @param childrenStr 父节点存放子节点的对象key值
   */
  constructor(allData: Array<T>, idStr = 'id', fatherIdStr = 'fatherId', childrenStr = 'children') {
    this.allData = allData
    this.idStr = idStr
    this.fatherIdStr = fatherIdStr
    this.childrenStr = childrenStr
  }

  /**
   * 组装
   * @returns
   */
  assembleTree() {
    const result = new Array<T>()
    const map = new Map<string, T>()
    if (!this.allData) return []
    for (let i = 0; i < this.allData.length; i++) {
      const element = this.allData[i]
      map.set(element[this.idStr] == '' ? '-1' : element[this.idStr], element)
    }
    this.dataMap = map
    for (let i = 0; i < this.allData.length; i++) {
      const element = this.allData[i]
      if (map.get(element[this.fatherIdStr])) {
        // 有父节点
        const temp = map.get(element[this.fatherIdStr])
        if (temp[this.childrenStr]) {
          temp[this.childrenStr].push(element)
        } else {
          temp[this.childrenStr] = []
          temp[this.childrenStr].push(element)
        }
      } else {
        // 顶级
        result.push(element)
      }
    }
    return result
  }
}
