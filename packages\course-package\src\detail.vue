<route-params content="/:id"></route-params>
<route-meta>
{
"title": "课程包详情"
}
</route-meta>
<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn" @click="$router.push('/training/course-package')">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/training/course-package' }">课程包课程</el-breadcrumb-item>
      <el-breadcrumb-item>课程包详情</el-breadcrumb-item>
    </el-breadcrumb>
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="课程包详情" name="package-detail">
        <package-detail></package-detail>
      </el-tab-pane>
      <el-tab-pane label="同步方案详情" name="scheme-sync">
        <scheme-sync></scheme-sync>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import Page from '@hbfe/jxjy-admin-common/src/models/Page'
  import PackageDetail from '@hbfe/jxjy-admin-coursePackage/src/detail/package-detail.vue'
  import SchemeSync from '@hbfe/jxjy-admin-coursePackage/src/detail/scheme-sync.vue'
  import MutationUpdateCoursePackage from '@api/service/management/resource/course-package/mutation/MutationUpdateCoursePackage'
  import UpdateCoursePackageVo from '@api/service/management/resource/course-package/mutation/vo/UpdateCoursePackageVo'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  @Component({
    components: { PackageDetail, SchemeSync }
  })
  export default class extends Vue {
    activeName = 'package-detail'
    page: Page = new Page()
    // id = ''
    // mutationUpdateCoursePackage: MutationUpdateCoursePackage

    // constructor() {
    //   super()
    //   this.mutationUpdateCoursePackage = new MutationUpdateCoursePackage()
    //   this.mutationUpdateCoursePackage.updateCoursePackageVo = new UpdateCoursePackageVo()
    // }

    // async activated() {
    //   this.id = this.$route.params.id
    //   this.mutationUpdateCoursePackage = await ResourceModule.coursePackageFactory.getUpdateCoursePackage(this.id)

    // }
  }
</script>

<style scoped></style>
