<route-meta>
{
"isMenu": true,
"title": "培训属性值管理",
"sort": 3
}
</route-meta>
<template>
  <el-main>
    <template v-if="$hasPermission('query')" desc="查询" actions="queryIndustryList, @FieldUnderIndustry">
      <el-tabs v-model="activeName" class="m-tab-top is-sticky" v-loading="uiConfig.pageLoading">
        <el-tab-pane v-for="(item, index) in industryList" :key="index" :label="item.name" :name="item.name">
          <field-under-industry :industry-info="item" :industry-type="industryType(item)"></field-under-industry>
        </el-tab-pane>
      </el-tabs>
    </template>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import FieldUnderIndustry from '@hbfe/jxjy-admin-scheme/src/field/field-under-industry.vue'
  import DataResolve from '@api/service/common/utils/DataResolve'
  import QueryBasicdataDictionaryFactory from '@api/service/common/basic-data-dictionary/QueryBasicdataDictionaryFactory'
  import IndustryEnum, { IndustryIdEnum } from '@api/service/training-institution/online-school/enum/IndustryEnum'

  @Component({
    components: { FieldUnderIndustry }
  })
  export default class extends Vue {
    activeName = ''

    /**
     * ui控制组
     */
    uiConfig = {
      // 页面加载
      pageLoading: false
    }

    /**
     * 接口查询
     */
    queryM: QueryBasicdataDictionaryFactory = BasicDataDictionaryModule.queryBasicDataDictionaryFactory

    /**
     * 行业列表
     */
    industryList: IndustryVo[] = new Array<IndustryVo>()

    /**
     * 行业类型 1：人社行业 2：建设行业 3: 职业卫生行业 4:工勤行业
     */
    get industryType() {
      return (item: IndustryVo) => {
        let type = 0
        switch (item.id) {
          case IndustryIdEnum.RS:
            type = 1
            break
          case IndustryIdEnum.JS:
            type = 2
            break
          case IndustryIdEnum.WS:
            type = 3
            break
          case IndustryIdEnum.GQ:
            type = 4
            break
          case IndustryIdEnum.LS:
            type = 5
            break
          case IndustryIdEnum.YS:
            type = 6
            break
          default:
            break
        }
        return type
      }
    }

    async created() {
      this.uiConfig.pageLoading = true
      await this.queryIndustryList()
      this.uiConfig.pageLoading = false
    }

    /**
     * 查询行业列表
     */
    async queryIndustryList() {
      const res = await this.queryM.queryIndustry.queryIndustry()
      this.industryList = res.isSuccess() ? this.queryM.queryIndustry.industryList : ([] as IndustryVo[])
      this.activeName = DataResolve.isWeightyArr(this.industryList) ? this.industryList[0].name : ''
      console.log('industryList', this.industryList)
    }
  }
</script>
