import msOfflineInvoice from '@api/ms-gateway/ms-offlineinvoice-v1'
import msOrder, { Commodity, InvoiceConfigResponse } from '@api/ms-gateway/ms-order-v1'
import { PurchaseChannelTypeEnum } from '@api/service/customer/trade/single/enum/PurchaseChannelTypeEnum'
import { ShipTypeEnum } from '@api/service/customer/trade/single/enum/ShipTypeEnum'
import TrainClassDetailClassVo from '@api/service/customer/train-class/query/vo/TrainClassDetailClassVo'
import { Description, upMyLog } from '@hbfe/jxjy-customer-common/src/monitor/WebfunnyUpMyLog'
import { ResponseStatus } from '@hbfe/common'
import { cloneDeep } from 'lodash'
import QueryTrainClassDetail from '@api/service/diff/customer/qztg/train-class/QueryTrainClassDetail'
import CreateOrder from '@api/service/diff/customer/qztg/trade/single/mutation/vo/create-order/CreateOrder'
import CreateOrderCommodity from '@api/service/diff/customer/qztg/trade/single/mutation/vo/create-order/CreateOrderCommodity'
import IssueDetail from "@api/service/customer/train-class/offlinePart/model/IssueDetail";
import QueryTrainClassIssue from "@api/service/customer/train-class/offlinePart/QueryTrainClassIssue";
import IssueListInDistributor from '@hbfe/fx-api/dist/service/customer/distribution-issue/IssueListInDistributor'

// 加载创建订单/支付订单需要加载的配置 如培训券 发票 商品信息等
export default class QueryCreateOrderConfig {
  // 方案Id
  private commoditySkuId = ''
  private commoditySkuIds: string[] = []

  /**
   * 是否分销环境
   */
  private isFx = false
  //   门户商品id
  private portalCommoditySkuId = ''
  //渠道
  private purchaseChannelType = PurchaseChannelTypeEnum.PurchaseChannelTypeEnumUserBuy
  // 查询期别列表实例
  private queryTrainClassIssue = new QueryTrainClassIssue()
  // 培训班详情（主商品）
  commodityDetail = new TrainClassDetailClassVo()
  // 合并的商品详情 (包含已购买)
  mergeCommodityDetail = new Array<TrainClassDetailClassVo>()
  // 培训班详情（多个商品）（主商品 + 合并商品）（剔除已购买）
  commodityDetailList = new Array<TrainClassDetailClassVo>()
  //发票配置详情
  invoiceConfig = new InvoiceConfigResponse()
  //配送列表
  shipList: ShipTypeEnum[] = []
  // order业务对象实例
  // private _orderInstance: CustomerOrderAction = undefined
  /**
   * 班级可选期别Map <商品，期别列表>
   */
  commodityIssueListMap = new Map<string, Array<IssueDetail>>()
  constructor(
    commoditySkuId?: string,
    commoditySkuIds?: string[],
    purchaseChannelType = PurchaseChannelTypeEnum.PurchaseChannelTypeEnumUserBuy,
    portalCommoditySkuId?: string,
    isFx?: boolean
  ) {
    this.commoditySkuId = commoditySkuId
    this.commoditySkuIds = commoditySkuIds
    this.portalCommoditySkuId = portalCommoditySkuId
    this.purchaseChannelType = purchaseChannelType
    this.isFx = isFx
  }

  /**
   * @description: 拿到创建订单对象
   */
  getCreateOrder() {
    const createOrder = new CreateOrder()

    const commodity = new CreateOrderCommodity()
    commodity.skuId = this.commodityDetail.commoditySkuId
    commodity.quantity = 1
    commodity.bought = this.commodityDetail.bought
    createOrder.createOrderParams.commodities = commodity
    createOrder.createOrderParams.purchaseChannelType = 1

    if (this.mergeCommodityDetail.length) {
      createOrder.createOrderParams.mergeCommodities = this.mergeCommodityDetail.map((item) => {
        const commodity = new CreateOrderCommodity()
        commodity.skuId = item.commoditySkuId
        commodity.quantity = 1
        commodity.bought = item.bought
        return commodity
      })
    }

    // createOrder.createOrderParams.needInvoice = false
    return createOrder
  }

  /**
   * @description: 初始化下单页面的配置。请先调用该方法。
   * @param {*}
   * @return {*}
   */
  async queryCreateOrderConfigInfo(trainingChannelId?: string) {
    // 加载商品信息
    let res = await this.queryCommodity(trainingChannelId)
    res = await this.queryInvoice(this.purchaseChannelType)
    res = await this.queryShippingMethodsForSchool()

    return res
  }
  /*
   * 查询配送列表
   *
   * */
  private async queryShippingMethodsForSchool() {
    const res = await msOfflineInvoice.queryShippingMethodsForSchool()
    // 加载商品信息
    if (res.status.isSuccess()) {
      this.shipList = res.data.shippingMethods
    } else {
      // webfunny埋点
      const description = new Description()
      description.response = res
      description.message = '加载配送渠道失败'
      upMyLog(description, 'queryShippingMethodsForSchool')
    }
    return res.status
  }
  /**
   * @description: 加载商品信息
   * @param {*}
   * @return {*}
   */

  private async queryCommodity(trainingChannelId?: string) {
    this.commodityDetailList = []
    this.mergeCommodityDetail = []
    this.commodityIssueListMap = new Map<string, Array<IssueDetail>>()
    if (this.commoditySkuIds?.length > 1) {
      // 多个商品 不考虑专题
      await Promise.all(
        this.commoditySkuIds.map(async (item) => {
          const queryTrainClassDetail: QueryTrainClassDetail = new QueryTrainClassDetail()
          queryTrainClassDetail.commodityId = item
          queryTrainClassDetail.portalCommoditySkuId = item
          // 加载商品信息
          const status = await queryTrainClassDetail.queryTrainClassDetail(trainingChannelId)
          // if (!queryTrainClassDetail.trainClassDetail.commoditySkuId) {
          //   queryTrainClassDetail.trainClassDetail.commoditySkuId = item
          // }
          // 获取期别
          if (this.isFx) {
            const queryFxIssue = new IssueListInDistributor()

            const issueFxListDto = await queryFxIssue.querySchemeAvailableIssueList(
              queryTrainClassDetail.commodityId,
              queryTrainClassDetail.trainClassDetail.schemeId
            )
            const issueList = issueFxListDto.map((item) => {
              return Object.assign(new IssueDetail(), item) as IssueDetail
            })
            this.commodityIssueListMap.set(item, issueList)
          } else if (trainingChannelId) {
            const issueList = await this.queryTrainClassIssue.queryChannelSchemeAvailableIssueList(item)
            this.commodityIssueListMap.set(item, issueList)
          } else {
            const issueList = await this.queryTrainClassIssue.querySchemeAvailableIssueList(item)
            this.commodityIssueListMap.set(item, issueList)
          }
          if (!status.isSuccess()) {
            // webfunny埋点
            const description = new Description()
            description.params = item
            description.response = status
            description.message = '加载商品信息失败'
            upMyLog(description, 'queryTrainClassDetail')
            return status
          }
          this.commodityDetailList.push(cloneDeep(queryTrainClassDetail.trainClassDetail))
          console.log(cloneDeep(queryTrainClassDetail.trainClassDetail), '(queryTrainClassDetail.trainClassDetail) lzh')
          console.log(this.commodityDetailList, 'this.commodityDetailList lzh')
        })
      )
      if (this.commodityDetailList?.length) {
        return new ResponseStatus(200, '加载多个商品成功')
      } else {
        return new ResponseStatus(500, '加载多个商品失败')
      }
    } else {
      const queryTrainClassDetail = new QueryTrainClassDetail()
      queryTrainClassDetail.commodityId = this.commoditySkuId
      queryTrainClassDetail.portalCommoditySkuId = this.portalCommoditySkuId

      // 加载商品信息
      const status = await queryTrainClassDetail.queryTrainClassDetailByMerge(trainingChannelId)
      // 获取期别
      if (this.isFx) {
        const queryFxIssue = new IssueListInDistributor()

        const issueFxListDto = await queryFxIssue.querySchemeAvailableIssueList(
          queryTrainClassDetail.trainClassDetail.commoditySkuId,
          queryTrainClassDetail.trainClassDetail.schemeId
        )
        const issueList = issueFxListDto.map(item => {
          return Object.assign(new IssueDetail(), item) as IssueDetail
        })
        this.commodityIssueListMap.set(this.commoditySkuId, issueList)
      } else if (trainingChannelId) {
        const issueList = await this.queryTrainClassIssue.queryChannelSchemeAvailableIssueList(this.commoditySkuId)
        this.commodityIssueListMap.set(this.commoditySkuId, issueList)
      } else {
        const issueList = await this.queryTrainClassIssue.querySchemeAvailableIssueList(this.commoditySkuId)
        this.commodityIssueListMap.set(this.commoditySkuId, issueList)
      }
      if (!status.isSuccess()) {
        // webfunny埋点
        const description = new Description()
        description.params = this.commoditySkuId
        description.response = status
        description.message = '加载商品信息失败'
        upMyLog(description, 'queryTrainClassDetail')
        return status
      }
      this.commodityDetail = queryTrainClassDetail.trainClassDetail
      if (queryTrainClassDetail.haveMerge) {
        if (!queryTrainClassDetail.trainClassDetail.bought) {
          this.commodityDetailList.push(queryTrainClassDetail.trainClassDetail)
        }
        queryTrainClassDetail.trainClassDetailClassList.map((item) => {
          if (!item.bought) {
            this.commodityDetailList.push(item)
          }
          this.mergeCommodityDetail.push(item)
        })
      }
      // 加载机构组织代码
      return status
    }
  }
  /**
   * @description: 发票信息
   * @param {*}
   * @return {*}
   */

  private async queryInvoice(purchaseChannelType: PurchaseChannelTypeEnum) {
    // 加载商品信息
    const res = await msOrder.preparePlaceOrder(purchaseChannelType)
    if (!res.status.isSuccess()) {
      // webfunny埋点
      const description = new Description()
      description.params = purchaseChannelType
      description.response = res
      description.message = '获取发票配置失败'
      upMyLog(description, 'preparePlaceOrder')
    }
    this.invoiceConfig = res.data.invoiceConfigResult
    // }

    return res.status
  }

  /**
   * @description: 加载机构组织代码
   * @param commodityDetail 商品信息
   * @return {*}
   */
}
