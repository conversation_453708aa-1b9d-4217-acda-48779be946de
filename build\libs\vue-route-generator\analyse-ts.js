const path = require('path')
const fs = require('fs')
const parser = require('@babel/parser')
const glob = require('glob').sync

const src = path.resolve(process.cwd(), `${process.env.API}`)
const analyse = (actions, permissionMap) => {
  const map = {}
  actions.forEach(item => {
    const split = item.split('/')
    if (!map[split[0]]) {
      map[split[0]] = {}
    }
    map[split[0]][split[1]] = 'alpha'
  })
  Object.keys(map).forEach(key => {
    const findFiles = glob(`**/${key}.ts`, {
      cwd: src
    })
    const values = map[key]
    if (findFiles.length) {
      findFiles.forEach(file => {
        const findFileContent = fs.readFileSync(path.join(src, file)).toString()
        const result = parser.parse(findFileContent, {
          sourceType: 'module',
          plugins: [
            'typescript',
            [
              'decorators',
              {
                decoratorsBeforeExport: true
              }
            ],
            'classProperties'
          ]
        })
        result.program.body.forEach(body => {
          if (body.type === 'ClassDeclaration' && body.superClass && body.superClass.name === 'VuexModule') {
            body.body.body.forEach(classBody => {
              if (classBody.type === 'ClassMethod') {
                // 找到函数为Action的
                if (classBody.decorators) {
                  const isAction = !!classBody.decorators.find(item => {
                    return item.expression.name === 'Action'
                  })
                  if (isAction) {
                    Object.keys(values).forEach(item => {
                      if (classBody.key.name === item) {
                        const secureDesign = classBody.decorators.find(
                          o => o.expression.callee && o.expression.callee.name === 'Secure'
                        )
                        if (secureDesign) {
                          secureDesign.expression.arguments.forEach(arg => {
                            arg.properties.forEach(property => {
                              property.value.elements.forEach(element => {
                                let key = property.key.name
                                if (property.key.type === 'StringLiteral') {
                                  key = property.key.value
                                }
                                permissionMap.graphql.push(`${key}.${element.value}`)
                              })
                            })
                          })
                        }
                      }
                    })
                  }
                }
              }
            })
          }
        })
      })
    }
  })
}

module.exports = analyse
