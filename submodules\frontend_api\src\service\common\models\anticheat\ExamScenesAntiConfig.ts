import FaceRecognitionShape from '@api/service/common/models/anticheat/FaceRecognitionShape'
import ProcessBehavior from '@api/service/common/models/anticheat/ProcessBehavior'

class ExamScenesAntiConfig {
  /**
   * 防作弊配置编号
   */
  id: string
  /**
   * 所属平台编号
   */
  platformId: string
  /**
   * 所属平台版本编号
   */
  platformVersionId: string
  /**
   * 所属项目编号
   */
  projectId: string
  /**
   * 所属子项目编号
   */
  subProjectId: string
  /**
   * 所属组织机构编号
   */
  organizationId: string
  /**
   * 所属单位编号
   */
  unitId: string
  /**
   * <pre>
   考试方案编号，如果需要应用到考试方案，则useRange&#x3D;3且填写具体考试方案编号
   </pre>
   */
  schemeId: string
  /**
   * <pre>
   试卷配置编号，如果需要应用到试卷配置，则useRange&#x3D;3且则填写具体的试卷配置编号
   </pre>
   */
  paperId: string
  /**
   * 应用场景,0/1/2,考试/考试/登录
   */
  usageScenarios: number
  /**
   * 应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
   */
  useRange: number
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 当前只支持人脸识别模式
   */
  shapeModel: FaceRecognitionShape
  /**
   * 考试前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeExamBehavior: number
  /**
   * 考试中监管行为
   */
  processExamBehavior: ProcessBehavior
  /**
   * 考试后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterExamBehavior: number
  /**
   * 配置时间
   */
  createTime: Date
  /**
   * 创建人编号
   */
  createUserId: string
  /**
   * 最后更新人
   */
  updateUserId: string
  /**
   * 最后更新时间
   */
  updateTime: Date
}

export default ExamScenesAntiConfig
