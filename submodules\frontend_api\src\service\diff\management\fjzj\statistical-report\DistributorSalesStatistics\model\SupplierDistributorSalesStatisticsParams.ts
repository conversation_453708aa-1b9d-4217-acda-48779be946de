import DateScope from '@api/service/common/models/DateScope'
import { QueryWayType, StatisticTradeRecordRequest } from '@api/platform-gateway/fxnl-query-front-gateway-backstage'
import {
  QueryWayType as QueryWayTypeExport,
  StatisticTradeRecordRequest as StatisticTradeRecordRequestExport
} from '@api/platform-gateway/fxnl-data-export-gateway-backstage'
import Context from '@api/service/common/context/Context'
import SupplierDistributorSalesStatisticsParamsMain from '@api/service/management/statisticalReport/DistributorSalesStatistics/model/SupplierDistributorSalesStatisticsParams'
export default class SupplierDistributorSalesStatisticsParams extends SupplierDistributorSalesStatisticsParamsMain {
  /**
   * 排除商品ID集合
   */
  excludeCommodityIdList: Array<string> = []

  static toStatisticTradeRecordRequestDiff(dto: SupplierDistributorSalesStatisticsParams) {
    const vo = new StatisticTradeRecordRequest()
    Object.assign(vo, this.toStatisticTradeRecordRequest(dto))
    vo.excludeCommodityIdList = dto.excludeCommodityIdList
    return vo
  }

  static toStatisticTradeRecordRequestExportDiff(dto: SupplierDistributorSalesStatisticsParams) {
    const vo = new StatisticTradeRecordRequest()
    Object.assign(vo, this.toStatisticTradeRecordRequest(dto))
    vo.excludeCommodityIdList = dto.excludeCommodityIdList
    return vo
  }
}
