"""独立部署的微服务,K8S服务名:ms-servicer-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询签约状态
		@param request
	"""
	getContractStatus(request:CVendorForTInstitutionRequest):Int
}
type Mutation {
	"""服务商申请服务凭证（获取服务商对应服务提供商信息）
		@param servicerId 服务商编号
		@return 服务凭证
	"""
	applyForService(servicerId:String):ServicerTokenResponse
	"""取消培训机构下的某个培训班的所有渠道商授权
		@param cancelAllCVendorAuthParam 取消授权参数
	"""
	cancelAllCVendorAuthPromotionTraining(cancelAllCVendorAuthParam:CancelAllCVendorAuthPromotionTrainingRequest):Void
	"""创建平台下渠道商
		<p>
		该方法仅创建平台下渠道商类型的服务商
		@param createRequest 渠道商信息
		@return 渠道商编号
	"""
	createChannelVendor(createRequest:ChannelVendorCreateRequest):String
	"""创建平台下课件供应商
		<p>
		该方法仅创建平台下课件供应商类型的服务商
		@param createRequest 课件供应商信息
		@return 课件供应商编号
	"""
	createCoursewareSupplier(createRequest:CoursewareSupplierCreateRequest):String
	"""创建平台下参训单位
		<p>
		该方法仅创建平台下参训单位类型的服务商
		@param createRequest 参训单位信息
		@return 参训单位编号
	"""
	createParticipatingUnit(createRequest:ParticipatingUnitCreateRequest):String
	"""创建平台下培训机构
		<p>
		该方法仅创建平台下培训机构类型的服务商
		@param request 培训机构信息
		@return 培训机构编号
	"""
	createTrainingInstitution(request:TrainingInstitutionCreateRequest):String
	"""创建培训机构门户
		@param request 门户信息
		@return 门户编号
	"""
	createTrainingInstitutionPortal(request:TrainingInstitutionPortalCreateRequest):String
	"""禁用培训机构
		@param request
	"""
	disableTrainingInstitution(request:TrainingInstitutionDisableRequest):Void
	"""启用培训机构
		@param request
	"""
	enableTrainingInstitution(request:TrainingInstitutionEnableRequest):Void
	"""移除培训机构门户
		@param request 门户信息
	"""
	removeTrainingInstitutionPortal(request:TrainingInstitutionPortalRemoveRequest):Void
	"""培训机构恢复与渠道商合作
		@param request 恢复信息
	"""
	resumeWithChannelVendorSigned(request:ResumeSignedCVendorForTInstitutionRequest):Void
	"""培训机构恢复与课件供应商合作
		@param request 恢复信息
	"""
	resumeWithCoursewareSupplierSigned(request:ResumeSignedCSupplierForTInstitutionRequest):Void
	"""参训单位恢复与培训机构合作
		@param request 恢复信息
	"""
	resumeWithTrainingInstitutionSigned(request:ResumeSignedTInstitutionForPUnitRequest):Void
	"""培训机构签约自己的渠道商
		<p>
		签约培训机构（甲方）与渠道商关系（乙方）
		@param request 签约信息
		@return 签约编号
	"""
	signUpChannelVendor(request:SignUpCVendorForTInstitutionRequest):String
	"""培训机构签约自己的课件供应商
		<p>
		签约培训机构（甲方）与课件供应商关系（乙方）
		@param request 签约信息
		@return 签约编号
	"""
	signUpCoursewareSupplier(request:SignUpCSupplierForTInstitutionRequest):String
	"""参训单位签约培训机构(参训单位角色操作)
		<p>
		培训机构（乙方）与参训单位关系（甲方）
		@param request 签约信息
		@return 签约编号
	"""
	signUpTrainingInstitution(request:SignUpTInstitutionForPUnitRequest):String
	"""培训机构中止与渠道商合作
		@param request 中止信息
	"""
	suspendWithChannelVendorSigned(request:SuspendSignedCVendorForTInstitutionRequest):Void
	"""培训机构中止与课件供应商合作
		@param request 中止信息
	"""
	suspendWithCoursewareSupplierSigned(request:SuspendSignedCSupplierForTInstitutionRequest):Void
	"""参训单位中止与培训机构合作
		@param request 中止信息
	"""
	suspendWithTrainingInstitutionSigned(request:SuspendSignedTInstitutionForPUnitRequest):Void
	"""更新平台下渠道商
		<p>
		该方法仅更新平台下渠道商类型服务商的信息
		@param updateRequest 渠道商信息
	"""
	updateChannelVendor(updateRequest:ChannelVendorUpdateRequest):Void
	"""更新平台下课件供应商
		<p>
		该方法仅更新平台下课件供应商类型服务商的信息
		@param updateRequest 课件供应商信息
	"""
	updateCoursewareSupplier(updateRequest:CoursewareSupplierUpdateRequest):Void
	"""更新平台下参训单位
		<p>
		该方法仅更新平台下参训单位类型服务商的信息
		@param updateRequest 参训单位信息
	"""
	updateParticipatingUnit(updateRequest:ParticipatingUnitUpdateRequest):Void
	"""更新平台下培训机构
		<p>
		该方法仅更新平台下培训机构类型服务商的信息
		@param request 培训机构信息
	"""
	updateTrainingInstitution(request:TrainingInstitutionUpdateRequest):Void
	"""更新培训机构门户
		@param request 门户信息
	"""
	updateTrainingInstitutionPortal(request:TrainingInstitutionPortalUpdateRequest):Void
	"""验证培训机构下对已签约的渠道商授权推广班级是否有效
		@param verifyAuthParam 验证授权参数
	"""
	verifyAuthCVendorPromotionTrainingEffective(verifyAuthParam:VerifyAuthCVendorPromotionTrainingRequest):Boolean!
}
input CVendorForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.CVendorForTInstitutionRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""渠道商编号"""
	channelVendorId:String
}
input CancelAllCVendorAuthPromotionTrainingRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.CancelAllCVendorAuthPromotionTrainingRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""培训班编号"""
	trainingId:String
}
"""渠道商创建信息
	<AUTHOR>
	@since 2021/7/7
"""
input ChannelVendorCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ChannelVendorCreateRequest") {
	"""渠道商名称"""
	name:String!
	"""所属企业账户编号"""
	accountId:String!
	"""所在地区"""
	region:String
	"""渠道商LOGO"""
	logo:String
	"""联系人"""
	contactPerson:String
	"""手机号"""
	phone:String
	"""渠道商简介"""
	abouts:String
	"""渠道商介绍"""
	content:String
	"""电子公章文件路径"""
	electronicSealPath:String
}
"""渠道商更新信息
	<AUTHOR>
	@since 2021/7/7
"""
input ChannelVendorUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ChannelVendorUpdateRequest") {
	"""渠道商编号"""
	channelVendorId:String!
	"""渠道商名称，为null，表示不更新"""
	name:String
	"""所在地区，为null，表示不更新"""
	region:String
	"""渠道商Logo，为null，表示不更新"""
	logo:String
	"""联系人，为null，表示不更新"""
	contactPerson:String
	"""手机号，为null，表示不更新"""
	phone:String
	"""渠道商简介，为null，表示不更新"""
	abouts:String
	"""渠道商介绍，为null，表示不更新"""
	content:String
	"""电子公章文件路径，为null，表示不更新"""
	electronicSealPath:String
}
"""课件供应商信息
	<AUTHOR>
	@since 2021/7/8
"""
input CoursewareSupplierCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.CoursewareSupplierCreateRequest") {
	"""所属企业主账号编号"""
	accountId:String!
	"""课件供应商名称"""
	name:String!
	"""所在地区"""
	region:String
	"""课件供应商Logo"""
	logo:String
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""课件供应商简介"""
	abouts:String
	"""课件供应商介绍"""
	content:String
	"""电子公章文件路径"""
	electronicSealPath:String
}
"""课件供应商信息
	<AUTHOR>
	@since 2021/7/8
"""
input CoursewareSupplierUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.CoursewareSupplierUpdateRequest") {
	"""课件供应商编号"""
	coursewareSupplierId:String!
	"""课件供应商名称，为null，表示不更新"""
	name:String
	"""所在地区，为null，表示不更新"""
	region:String
	"""课件供应商Logo，为null，表示不更新"""
	logo:String
	"""联系人，为null，表示不更新"""
	contactPerson:String
	"""联系电话，为null，表示不更新"""
	phone:String
	"""课件供应商简介，为null，表示不更新"""
	abouts:String
	"""课件供应商介绍，为null，表示不更新"""
	content:String
	"""电子公章文件路径，为null，表示不更新"""
	electronicSealPath:String
}
"""参训单位信息"""
input ParticipatingUnitCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ParticipatingUnitCreateRequest") {
	"""所属企业主账号编号"""
	accountId:String!
	"""参训单位名称"""
	name:String!
	"""所在地区"""
	region:String
	"""参训单位Logo"""
	logo:String
	"""联系人"""
	contactPerson:String
	"""联系电话"""
	phone:String
	"""参训单位简介"""
	abouts:String
	"""参训单位介绍"""
	content:String
	"""电子公章文件路径"""
	electronicSealPath:String
}
"""参训单位信息"""
input ParticipatingUnitUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ParticipatingUnitUpdateRequest") {
	"""参训单位编号"""
	participatingUnitId:String!
	"""参训单位名称，为null，表示不更新"""
	name:String
	"""所在地区，为null，表示不更新"""
	region:String
	"""参训单位Logo，为null，表示不更新"""
	logo:String
	"""联系人，为null，表示不更新"""
	contactPerson:String
	"""联系电话，为null，表示不更新"""
	phone:String
	"""参训单位简介，为null，表示不更新"""
	abouts:String
	"""参训单位介绍，为null，表示不更新"""
	content:String
	"""电子公章文件路径，为null，表示不更新"""
	electronicSealPath:String
}
"""门户轮播图
	<AUTHOR>
	@since 2021/7/13
"""
input PortalBanner @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.PortalBanner") {
	"""轮播图编号，新建时可以不填"""
	id:String
	"""轮播图名称"""
	name:String
	"""轮播图路径"""
	path:String
	"""链接地址"""
	link:String
	"""轮播图排序"""
	sort:Int!
	"""是否启用"""
	enable:Boolean!
}
"""培训机构恢复与课件供应商合作信息
	<AUTHOR>
	@since 2021/7/8
"""
input ResumeSignedCSupplierForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ResumeSignedCSupplierForTInstitutionRequest") {
	"""培训机构与课件供应商签约编号"""
	servicerContractId:String
}
"""培训机构恢复与渠道商合作信息
	<AUTHOR>
	@since 2021/7/8
"""
input ResumeSignedCVendorForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ResumeSignedCVendorForTInstitutionRequest") {
	"""培训机构与渠道商签约编号"""
	servicerContractId:String
}
"""参训单位恢复与培训机构合作信息"""
input ResumeSignedTInstitutionForPUnitRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.ResumeSignedTInstitutionForPUnitRequest") {
	"""参训单位与培训机构签约编号"""
	servicerContractId:String
}
"""签约成为培训机构的课件供应商合作
	<AUTHOR>
	@since 2021/7/8
"""
input SignUpCSupplierForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SignUpCSupplierForTInstitutionRequest") {
	"""课件供应商编号"""
	coursewareSupplierId:String
	"""合约内容"""
	content:String
}
"""签约成为培训机构的渠道商合作
	<AUTHOR>
	@since 2021/7/8
"""
input SignUpCVendorForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SignUpCVendorForTInstitutionRequest") {
	"""渠道商编号"""
	channelVendorId:String
	"""合约内容"""
	content:String
}
input SignUpTInstitutionForPUnitRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SignUpTInstitutionForPUnitRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""合约内容"""
	content:String
}
"""培训机构中止与课件供应商合作信息
	<AUTHOR>
	@since 2021/7/8
"""
input SuspendSignedCSupplierForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SuspendSignedCSupplierForTInstitutionRequest") {
	"""培训机构与课件供应商签约编号"""
	servicerContractId:String
}
"""培训机构中止与渠道商合作信息
	<AUTHOR>
	@since 2021/7/8
"""
input SuspendSignedCVendorForTInstitutionRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SuspendSignedCVendorForTInstitutionRequest") {
	"""培训机构与渠道商签约编号"""
	servicerContractId:String
}
"""参训单位中止与培训机构合作信息"""
input SuspendSignedTInstitutionForPUnitRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.SuspendSignedTInstitutionForPUnitRequest") {
	"""参训单位与培训机构签约编号"""
	servicerContractId:String
}
"""培训机构创建信息
	<AUTHOR>
	@since 2021/7/7
"""
input TrainingInstitutionCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionCreateRequest") {
	"""培训机构名称"""
	name:String!
	"""所属企业账户编号"""
	accountId:String!
	"""所在地区"""
	region:String
	"""联系人"""
	contactPerson:String
	"""手机号"""
	phone:String
	"""培训机构LOGO"""
	logo:String
	"""培训机构简介"""
	abouts:String
	"""培训机构介绍"""
	content:String
	"""电子公章文件路径"""
	electronicSealPath:String
}
"""培训机构禁用请求"""
input TrainingInstitutionDisableRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionDisableRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
}
"""培训机构启用请求"""
input TrainingInstitutionEnableRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionEnableRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
}
"""培训机构门户创建信息
	<AUTHOR>
	@since 2021/7/13
"""
input TrainingInstitutionPortalCreateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionPortalCreateRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
	"""门户类型
		<p>
		1-WEB
		2-移动端
	"""
	portalType:Int!
	"""门户标题"""
	title:String
	"""宣传口号"""
	slogan:String
	"""门户简介说明内容"""
	content:String
	"""域名"""
	domainName:String
	"""轮播图列表"""
	banners:[PortalBanner]
}
"""培训机构门户创建信息
	<AUTHOR>
	@since 2021/7/13
"""
input TrainingInstitutionPortalRemoveRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionPortalRemoveRequest") {
	"""门户编号"""
	id:String!
	"""培训机构编号"""
	trainingInstitutionId:String!
}
"""培训机构门户创建信息
	<AUTHOR>
	@since 2021/7/13
"""
input TrainingInstitutionPortalUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionPortalUpdateRequest") {
	"""门户编号"""
	id:String!
	"""培训机构编号"""
	trainingInstitutionId:String!
	"""门户标题"""
	title:String
	"""宣传口号"""
	slogan:String
	"""门户简介说明内容"""
	content:String
	"""域名"""
	domainName:String
	"""轮播图列表"""
	banners:[PortalBanner]
}
"""培训机构创建信息
	<AUTHOR>
	@since 2021/7/7
"""
input TrainingInstitutionUpdateRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.TrainingInstitutionUpdateRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String!
	"""培训机构名称，为null，表示不更新"""
	name:String
	"""所在地区，为null，表示不更新"""
	region:String
	"""联系人，为null，表示不更新"""
	contactPerson:String
	"""手机号，为null，表示不更新"""
	phone:String
	"""培训机构LOGO，为null，表示不更新"""
	logo:String
	"""培训机构简介，为null，表示不更新"""
	abouts:String
	"""培训机构介绍，为null，表示不更新"""
	content:String
	"""电子公章文件路径，为null，表示不更新"""
	electronicSealPath:String
}
input VerifyAuthCVendorPromotionTrainingRequest @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.request.VerifyAuthCVendorPromotionTrainingRequest") {
	"""培训机构编号"""
	trainingInstitutionId:String
	"""渠道商编号"""
	channelVendorId:String
	"""培训班编号"""
	trainingId:String
}
"""服务方信息元数据
	<AUTHOR>
	@since 2021/7/27
"""
type ServicerTokenMetaResponse @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.response.ServicerTokenMetaResponse") {
	"""服务商编号"""
	servicerId:String
	"""服务商类型"""
	servicerType:Int!
	"""服务商状态"""
	status:Int!
	"""服务商所属单位编号"""
	unitId:String
}
"""服务凭证
	<AUTHOR>
	@since 2021/7/27
"""
type ServicerTokenResponse @type(value:"com.fjhb.ms.servicer.v1.kernel.gateway.graphql.response.ServicerTokenResponse") {
	"""服务凭证"""
	token:String
	"""服务凭证元数据"""
	tokenMeta:ServicerTokenMetaResponse
}

scalar List
