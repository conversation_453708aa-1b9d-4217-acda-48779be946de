/*
 * @Description: 创建资讯VO
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-09 19:01:15
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-18 11:52:14
 */
export default class UpdateDraftNewsVo {
  newsId: string
  /**
   * 发布时间
   */
  time: string
  /**
   * 资讯标题
   */
  title: string
  /**
   * 是否弹窗
   */
  isPopup = false
  /**
   * 是否置顶
   */
  top = false
  /**
   * 资讯类别
   */
  categoryType: string
  /**
   * 资讯来源
   */
  source?: string
  /**
   * 资讯摘要
   */
  abstract?: string
  /**
   * 封面图片
   */
  bgImage?: string
  /**
   * 资讯内容
   */
  content: string
}
