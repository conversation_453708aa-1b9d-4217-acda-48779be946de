/**
 *
 * 试题更新对象
 * @author: eleven
 * @date: 2020/4/14
 */
import { Tag } from '@/store/module/exam/mode/question/Tag'
import { QuestionDetail } from '@/store/module/exam/mode/question/QuestionDetail'
import {
  ComprehensiveRequest,
  JudgementRequest,
  MultipleChoiceRequest,
  PreExamQuestionUpdateRequest,
  SingleChoiceRequest
} from '@api/gateway/PreExam-default'

export class QuestionUpdateForm {
  /**
   * 试题id
   */
  id = ''
  /**
   * 章节
   */
  chapters = new Array<Tag>()
  /**
   * 题类
   */
  questionCategory = ''
  /**
   * 是否易错题
   */
  errorProne: boolean
  applyTypes: Array<number>
  libraryId: string
  title: string
  judgement: JudgementRequest
  singleChoice: SingleChoiceRequest
  multipleChoice: MultipleChoiceRequest
  comprehensive: ComprehensiveRequest
  questionType: number
  mode: number
  difficulty: number
  description: string
  enabled: boolean

  /**
   * 转为更新对象
   * @param detail
   */
  public static from(detail: QuestionDetail) {
    const result = new PreExamQuestionUpdateRequest()
    result.id = detail.id
    result.title = detail.title
    result.questionCategory = detail.questionCategory
    result.questionType = detail.questionType
    result.chapters = detail.chapters
    result.libraryId = detail.libraryId
    result.errorProne = detail.errorProne
    result.applyTypes = detail.applyTypes
    result.judgement = detail.judgement
    result.singleChoice = detail.singleChoice
    result.multipleChoice = detail.multipleChoice
    result.comprehensive = detail.comprehensive
    result.mode = detail.mode
    result.difficulty = detail.difficulty
    result.enabled = detail.enabled
    result.description = detail.description
    return result
  }
}
