import MsBasicdataDomainGatewayV1, { RoleToAccountCountResponse } from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import { ResponseStatus } from '@hbfe/common'

export default class RoleInfoResponseVo {
  /**
   * 唯一标识
   */
  id: string

  /**
   * 角色名称
   */
  name: string

  /**
   * 角色说明
   */
  description: string

  /**
   * 角色类别
   1-学员  2-集体报名管理员  3-管理员  4-人设管理员  5-企业管理员  6-人社审批管理员
   */
  category: number

  /**
   * 是否内置
   */
  isBuiltIn = false

  /**
   * 旗下管理员账号数
   */
  adminAccountNum: number

  /**
   * 是否选中
   */
  isSelect = false

  /**
   * 管理管理员人数
   */
  adminCount = 0

  /**
   * 所属方id
   */
  belongId = ''

  /**
   * 所属角色名称
   */
  belongRoleName = ''

  static from(roleResponse: RoleToAccountCountResponse) {
    const roleInfoResponseVo = new RoleInfoResponseVo()
    roleInfoResponseVo.id = roleResponse.roleResponse?.id
    roleInfoResponseVo.name = roleResponse.roleResponse?.name
    roleInfoResponseVo.description = roleResponse.roleResponse?.description
    roleInfoResponseVo.category = roleResponse.roleResponse?.category
    roleInfoResponseVo.isBuiltIn = roleResponse.roleResponse?.nature == 1
    roleInfoResponseVo.belongId = roleResponse.roleResponse?.ownerMemberId
    roleInfoResponseVo.belongRoleName = roleResponse.roleResponse?.belongRoleName
    roleInfoResponseVo.adminAccountNum = roleResponse.accountCount
    roleInfoResponseVo.adminCount = roleResponse.accountCount
    return roleInfoResponseVo
  }

  onRemoving = false

  removed = false

  async doRemove() {
    if (this.isBuiltIn) {
      return Promise.reject(new Error('内置角色不允许删除'))
    }
    this.onRemoving = true
    try {
      this.onRemoving = true
      const res = await MsBasicdataDomainGatewayV1.removeRoleWithNoAuthRelById(this.id)
      this.removed = true
      return res
    } catch (e) {
      return new ResponseStatus(500, '系统异常')
    } finally {
      this.onRemoving = false
    }
  }

  /**
   * 删除角色
   */
  async removeRoleWithAdminTypeById() {
    if (this.isBuiltIn) {
      return Promise.reject(new Error('内置角色不允许删除'))
    }
    this.onRemoving = true
    try {
      this.onRemoving = true
      const res = await MsBasicdataDomainGatewayV1.removeRoleWithAdminTypeById(this.id)
      this.removed = true
      return res
    } catch (e) {
      return new ResponseStatus(500, '系统异常')
    } finally {
      this.onRemoving = false
    }
  }
}
