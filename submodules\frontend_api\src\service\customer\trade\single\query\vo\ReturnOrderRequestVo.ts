import { DateScopeRequest, OrderInfoRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { SubOrderInfoRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
/**
 * 退货单审批信息查询参数
 <AUTHOR>
 @date 2022/03/18
 */
export class ReturnOrderApprovalInfoRequest {
  /**
   * 审批时间
   */
  approveTime = new DateScopeRequest()
}
/**
 * 退货单状态变更时间查询参数
 <AUTHOR>
 @date 2022/03/24
 */
export class ReturnOrderStatusChangeTimeRequest {
  /**
   * 申请退货时间
   */
  applied = new DateScopeRequest()
  /**
   * 退货单完成时间
   <br> 这个参数包含了退货退款完成（退货单类型为退货退款）、仅退货完成（退货单类型为仅退货）、仅退款完成（退货单类型为仅退款）时间，三个时间之间用or匹配
   */
  returnCompleted = new DateScopeRequest()
}
/**
 * 发货单基本信息查询参数
 <AUTHOR>
 @date 2022/03/24
 */
export class ReturnOrderBasicDataRequest {
  /**
   * 退货单状态(0:申请退货 1:申请退货取消处理中 2:退货处理中 3:退货失败 4:正在申请退款 5:已申请退款 6:退款处理中 7:退款失败 8:退货完成 9:退款完成 10:退货退款完成 11:已关闭)
   */
  returnOrderStatus?: Array<number>
  /**
   * 退货单状态变更时间
   */
  returnStatusChangeTime = new ReturnOrderStatusChangeTimeRequest()
}

/**
 * 退货单查询参数
 */
export class ReturnOrderRequestVo {
  /**
   * 退货单号
   */
  returnOrderNoList: Array<string> = []
  /**
   * 基本信息
   */
  basicData: ReturnOrderBasicDataRequest = new ReturnOrderBasicDataRequest()
  /**
   * 审批信息
   */
  approvalInfo: ReturnOrderApprovalInfoRequest = new ReturnOrderApprovalInfoRequest()
  /**
   * 退货商品id集合
   */
  returnCommoditySkuIdList: Array<string> = []
  /**
   * 退款商品id集合
   */
  refundCommoditySkuIdList: Array<string> = []
  /**
   * 退货单关联子订单查询参数
   */
  subOrderInfo: SubOrderInfoRequest = new SubOrderInfoRequest()
}
