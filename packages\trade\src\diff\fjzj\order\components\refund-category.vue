<route-meta>
{
"title": "退货/款类别"
}
</route-meta>
<template>
  <el-select v-model="selected" :placeholder="placeholder" @clear="handleClear" class="el-input" filterable clearable>
    <el-option-group v-for="group in options" :key="group.label" :label="group.label">
      <el-option v-for="item in group.arr" :label="item.label" :value="item.value" :key="item.value"></el-option>
    </el-option-group>
  </el-select>
</template>
<script lang="ts">
  import { Component, Vue, Prop, Watch, Emit } from 'vue-property-decorator'
  import OrderRefundOption from '@api/service/common/return-order/models/OrderRefundOption'
  import OrderRefundType, { OrderRefundTypeEnum } from '@api/service/common/return-order/enums/OrderRefundType'
  import { SubOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import OrderDetailVo from '@api/service/management/trade/single/order/query/vo/UserOrderDetailVo'

  @Component
  export default class extends Vue {
    selected: string | number | undefined = ''

    options = [] as OrderRefundOption[] //返回结果
    optionsList = [] as OrderRefundOption[]
    orderDetail = new OrderDetailVo() // 订单详情
    OrderRefundTypeEnum = OrderRefundTypeEnum //退货/款类型枚举
    /**
     * 5
     * 1 实付金额大于0时，“退货/款物品”的培训方案选中一个
     * 2 实付金额大于0时，“退货/款物品”的培训方案全部选中时
     * 3 实付金额等于0时，“退货/款物品”的培训方案全部选中时
     * 4 “退货/款物品” 全部取消选中时，无下拉选项
     */
    @Prop({
      type: Number,
      default: 5
    })
    typesof: number

    @Prop({
      type: String,
      default: '请选择退货/款类型'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    isGetAllOptions!: boolean //判断是否要获取下拉的全部信息   在发起弹框时交互特意添加 “退货/款物品” 全部取消选中时，无下拉选项

    @Prop({
      type: Boolean,
      default: true
    })
    isNeedRefundMoney: boolean //判断是否是0元单

    @Prop({
      type: Boolean,
      default: true
    })
    isShowAll: boolean // 是否显示全部选项 弹窗定制化

    @Prop({
      type: Number
    })
    value: number // 传入的值

    @Watch('value')
    valueChange() {
      this.selected = this.value // 监听传入的值变化
    }

    @Emit('input')
    @Watch('selected')
    selectedChange() {
      return this.selected
    }

    @Watch('typesof', {
      immediate: true,
      deep: true
    })
    typesofChange(val: number) {
      this.changeOptions()
    }

    changeOptions() {
      // 定义一个映射对象，根据不同的 type 值进行不同的过滤操作
      const filterMap = {
        1: {
          groupLabel: '含退款',
          itemValue: OrderRefundTypeEnum.partialReturnPartialRefund
        },
        2: {
          groupLabel: '含退款',
          itemValue: OrderRefundTypeEnum.returnAndRefund
        },
        3: {
          groupLabel: '不含退款',
          itemValue: OrderRefundTypeEnum.returnOnly
        }
      }
      // 根据 type 值获取对应的过滤条件
      const filterConfig = filterMap[this.typesof]
      if (filterConfig) {
        // 如果有对应的过滤条件，则进行过滤操作
        this.options = this.optionsList
          .filter((group) => group.label === filterConfig.groupLabel)
          .map((group) => ({
            label: group.label,
            arr: group.arr.filter((item) => item.value === filterConfig.itemValue)
          }))
      } else {
        this.options = []
      }
    }
    // 提取公共过滤逻辑
    filterOptionsByNeedRefund(options: OrderRefundOption[]) {
      if (!Array.isArray(options)) {
        console.warn('传入的选项不是数组:', options)
        return []
      }
      return this.isNeedRefundMoney ? options : options.filter((item) => item.label === '不含退款')
    }
    /**
     * 获取所有选项
     */
    async getAllEnumMemberUiOptions() {
      try {
        // 获取所有选项
        const allOptions = await OrderRefundType.getAllEnumMemberUiOptionsWithDiff()
        console.log(allOptions, 'allOptions')
        // 根据 isNeedRefundMoney 决定是否过滤选项
        // 使用公共过滤逻辑
        this.optionsList = this.filterOptionsByNeedRefund(allOptions)
        this.changeOptions()
      } catch (error) {
        console.error('获取选项时出错:', error)
      }
    }
    /**
     * 获取子单可退款类型
     */
    async subOrderCanRefundType(item: SubOrderResponse) {
      const allOptions = await this.orderDetail.subOrderCanRefundType(item)
      // 根据 isNeedRefundMoney 决定是否过滤选项
      this.options = this.filterOptionsByNeedRefund(allOptions)
    }
    async mounted() {
      if (this.isGetAllOptions) {
        await this.getAllEnumMemberUiOptions()
      } else {
        this.options = [] as OrderRefundOption[]
      }
    }

    handleClear() {
      this.selected = undefined // 清除时将 selected 设置为 undefined
    }
  }
</script>
