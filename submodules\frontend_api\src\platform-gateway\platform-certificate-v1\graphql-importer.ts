import batchImportStudentList from './mutates/batchImportStudentList.graphql'
import batchPrintCertificateSync from './mutates/batchPrintCertificateSync.graphql'
import batchPrintCertificates from './mutates/batchPrintCertificates.graphql'
import checkImportPrintListTheUploadProgress from './mutates/checkImportPrintListTheUploadProgress.graphql'
import chooseTemplatePrintCertificate from './mutates/chooseTemplatePrintCertificate.graphql'
import collectivelyAdministratorExportCertificateFailedData from './mutates/collectivelyAdministratorExportCertificateFailedData.graphql'
import dataMigrationCertificatePrintWithOutLogin from './mutates/dataMigrationCertificatePrintWithOutLogin.graphql'
import dataMigrationPrintCertificate from './mutates/dataMigrationPrintCertificate.graphql'
import deleteQuestion from './mutates/deleteQuestion.graphql'
import exportCertificateFailedData from './mutates/exportCertificateFailedData.graphql'
import exportFailedData from './mutates/exportFailedData.graphql'
import findImportPrintData from './mutates/findImportPrintData.graphql'
import findImportPrintFailData from './mutates/findImportPrintFailData.graphql'
import findUploadSuccessFailureQuantity from './mutates/findUploadSuccessFailureQuantity.graphql'
import getCertificateSnapshot from './mutates/getCertificateSnapshot.graphql'
import groupRegistrationPrintCertificate from './mutates/groupRegistrationPrintCertificate.graphql'
import learnerImportBatchPrintCertificates from './mutates/learnerImportBatchPrintCertificates.graphql'
import printCertificate from './mutates/printCertificate.graphql'
import printTheListRemoveAllStudent from './mutates/printTheListRemoveAllStudent.graphql'
import printTheListRemoveStudent from './mutates/printTheListRemoveStudent.graphql'
import returnImportedData from './mutates/returnImportedData.graphql'
import screeningBatchPrintCertificates from './mutates/screeningBatchPrintCertificates.graphql'
import studentBatchPrintCertificates from './mutates/studentBatchPrintCertificates.graphql'
import studentPrintCertificate from './mutates/studentPrintCertificate.graphql'

export {
  batchImportStudentList,
  batchPrintCertificateSync,
  batchPrintCertificates,
  checkImportPrintListTheUploadProgress,
  chooseTemplatePrintCertificate,
  collectivelyAdministratorExportCertificateFailedData,
  dataMigrationCertificatePrintWithOutLogin,
  dataMigrationPrintCertificate,
  deleteQuestion,
  exportCertificateFailedData,
  exportFailedData,
  findImportPrintData,
  findImportPrintFailData,
  findUploadSuccessFailureQuantity,
  getCertificateSnapshot,
  groupRegistrationPrintCertificate,
  learnerImportBatchPrintCertificates,
  printCertificate,
  printTheListRemoveAllStudent,
  printTheListRemoveStudent,
  returnImportedData,
  screeningBatchPrintCertificates,
  studentBatchPrintCertificates,
  studentPrintCertificate
}
