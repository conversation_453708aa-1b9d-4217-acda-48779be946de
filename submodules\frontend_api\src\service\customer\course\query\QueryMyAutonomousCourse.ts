import MsCourseLearningQueryFrontGatewayCourseLearningForestage, {
  CourseCategoryRequest,
  CourseCategoryResponse,
  CourseInfoRequest,
  CourseLearningSortRequest,
  CourseOfCourseTrainingOutlineRequest,
  CourseResponse,
  StudentCourseLearningRequest,
  StudentCourseLearningResponse
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
import QueryCourseList from '@api/service/customer/course/query/QueryCourseList'
import QuerySchemeCourse from '@api/service/customer/course/query/QuerySchemeCourse'
import AfterCourseTestLastResult from '@api/service/customer/course/query/vo/AfterCourseTestLastResult'
import MyLearningCourse from '@api/service/customer/course/query/vo/MyLearningCourse'
import AfterCourseConfig from '@api/service/customer/course/query/vo/after-course-config/AfterCourseConfig'
import { TrainingSchemeTypeEnum } from '@api/service/customer/scheme/enums/TrainingSchemeType'
import ClassOutline, { ChildOutlineResp } from '@api/service/customer/scheme/models/ClassOutline'
import ClassOutlineUtil from '@api/service/customer/scheme/utils/ClassOutlineUtil'
import SchemeUtil from '@api/service/customer/scheme/utils/SchemeUtil'
import TemplateNameManager from '@api/service/management/train-class/mutation/dto/TemplateNameManager'
import { Page, UiPage } from '@hbfe/common'
import Classification from '../../train-class/query/vo/Classification'
import Utils from './utils'
import CourseCategory from './vo/CourseCategory'
import QueryMyAutonomousCoursePageParam from './vo/QueryMyAutonomousCoursePageParam'

class QueryMyAutonomousCourse extends QuerySchemeCourse {
  private readonly studentNo: string

  /**
   * 培训方案id
   */
  schemeId = ''

  /**
   * 课程大纲
   * @param studentNo
   */
  classOutlines: ClassOutline[] = [] as ClassOutline[]

  /**
   * 课程大纲（平铺）
   */
  flatClassOutlineList: ClassOutline[] = []

  /**
   * 方案工具类
   * @private
   */
  private _schemeUtil = new SchemeUtil()

  /**
   * 大纲工具类
   * @private
   */
  private _classOutlineUtil = new ClassOutlineUtil()

  constructor(studentNo: string) {
    super()
    this.studentNo = studentNo
  }

  async querySchemeConfig(schemeId: string): Promise<AfterCourseConfig> {
    const result = await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.getSchemeConfigInServicer({
      schemeId,
      needField: ['chooseCourseLearning.config.courseQuizConfig']
    })
    const schemeConfig = JSON.parse(result.data.schemeConfig)
    return AfterCourseConfig.from(schemeConfig.chooseCourseLearning.config.courseQuizConfig)
  }

  private async queryCourseDetails(teacherIdList: Array<string>, categoryIdList: Array<string>) {
    const queryString = `
      query queryCourseDetail($courseCategoryRequest, $teacherIds: [String]) {
        listTeacherInServicer(teacherIds: $teacherIds) {
          id, name
        }

        listCourseCategoryInServicer(request: $courseCategoryRequest) {
          id, name
        }
      }
    `
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const requestCourseCategory = new CourseCategoryRequest()
    requestCourseCategory.categoryIdList = categoryIdList
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage._commonQuery(queryString as any, {
      teacherIds: teacherIdList,
      courseCategoryRequest: requestCourseCategory
    })
  }

  private async getCoursePageMap(idList: Array<string>): Promise<Map<string, CourseResponse>> {
    if (!idList.length) return
    const queryCourseList = new QueryCourseList()
    const resourceCourse = await queryCourseList.queryCoursePageByIdList(Array.from(idList))
    const resultMap = new Map<string, CourseResponse>()
    resourceCourse.forEach((course: CourseResponse) => {
      resultMap.set(course.id, course)
    })
    return resultMap
  }

  /**
   * 查询我的自主选课学习课程分页
   * @param page
   * @param queryParams
   */
  async queryPage(
    page: UiPage,
    queryParams: QueryMyAutonomousCoursePageParam,
    sort?: Array<CourseLearningSortRequest>,
    outlineTree?: Array<Classification>
  ): Promise<Array<MyLearningCourse>> {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseOfAutonomousCourseLearningSceneInMyself(
      {
        page,
        request: queryParams.to(),
        sort: sort
      }
    )
    // 原课程 id 集合
    if (result.status?.isSuccess() && result.data?.currentPageData?.length) {
      const metadata = result.data?.currentPageData
      /** 查询课程大纲 **/
      await this.getClassOutlines(metadata)
    }
    const resultList = result?.data?.currentPageData?.map((response: StudentCourseLearningResponse) => {
      return MyLearningCourse.from(response)
    })
    resultList.forEach((learningCourse: MyLearningCourse) => {
      learningCourse.detail.categoryPath = this.getCategoryPath(
        outlineTree,
        learningCourse.courseOfCourseTrainingOutline.outlineId
      )
    })
    page.totalPageSize = result.data?.totalPageSize
    page.totalSize = result.data?.totalSize
    /** 判断是否为必学 **/
    resultList?.forEach(item => {
      const compulsoryCourseIdList = this.flatClassOutlineList.find(
        el => el.id == item.courseOfCourseTrainingOutline.outlineId
      )?.compulsoryCourseIdList
      if (compulsoryCourseIdList?.length && compulsoryCourseIdList.includes(item.detail.id)) {
        item.detail.isMustLearn = true
      }
    })
    // console.log('###autoChooseCourseList', resultList)
    return resultList
  }

  async queryAfterCourseTestLastResult(courseOfCourseTrainingOutlineId: string): Promise<AfterCourseTestLastResult> {
    const request = new StudentCourseLearningRequest()
    request.studentNo = this.studentNo
    request.courseOfCourseTrainingOutline = new CourseOfCourseTrainingOutlineRequest()
    request.courseOfCourseTrainingOutline.courseOfCourseTrainingOutlineId = courseOfCourseTrainingOutlineId
    const page = new UiPage()
    page.pageNo = 1
    page.pageSize = 1
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseOfAutonomousCourseLearningSceneInMyself(
      {
        page,
        request
      }
    )
    if (result.data?.currentPageData?.length) {
      const lastResult = result.data.currentPageData[0]
      return AfterCourseTestLastResult.from(lastResult.studentCourseQuiz)
    }
    return new AfterCourseTestLastResult()
  }

  private getCategoryPath(tree: Array<Classification>, leafOutlineId: string): string {
    const categoryList = Utils.treeFindPath(
      tree,
      (node: Classification) => {
        return node.id === leafOutlineId
      },
      'name',
      'childOutlines'
    )
    return categoryList.length ? categoryList.join(' > ') : ''
  }

  // * 通过学号课程ID获取评价ID
  async getEvaluateId(studentNo: string, courseId: string) {
    const params = new StudentCourseLearningRequest()
    params.course = new CourseInfoRequest()
    params.studentNo = studentNo
    params.course.courseId = courseId
    let result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseOfAutonomousCourseLearningSceneInMyself(
      {
        page: new Page(1, 1),
        request: params
      }
    )
    if (result.status.code !== 200 || !result.status.isSuccess()) {
      console.error('返回报错', result)
      return Promise.reject(result)
    }
    result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.pageCourseOfAutonomousCourseLearningSceneInMyself(
      {
        page: new Page(1, result.data.totalSize),
        request: params
      }
    )
    if (result.status.code !== 200 || !result.status.isSuccess()) {
      console.error('返回报错', result)
      return Promise.reject(result)
    }

    return result.data.currentPageData.find(res => res.course.courseId === courseId)?.studentCourseAppraised
      ?.studentCourseAppraisalId
  }

  private async getClassOutlines(metadata: StudentCourseLearningResponse[]) {
    /** 拿到培训方案id **/
    this.schemeId = metadata[0].range?.schemeId
    const jsonConfigMap = await this._schemeUtil.batchQuerySchemeConfig([this.schemeId])
    const jsonConfig = jsonConfigMap.get(this.schemeId)
    // console.log('###jsonConfig', jsonConfig)
    const schemeType = jsonConfig.type as TrainingSchemeTypeEnum
    const _courseTrainingOutlines =
      (jsonConfig[schemeType]?.config?.courseTrainingOutlines as ChildOutlineResp[]) || ([] as ChildOutlineResp[])
    this.classOutlines = this._classOutlineUtil.reShapeTree(_courseTrainingOutlines, this.schemeId)
    this.flatClassOutlineList = this._classOutlineUtil.treeToList(this.classOutlines)
    // console.log('###classOutline', this.classOutlines, this.flatClassOutlineList)
  }
  // 查大纲
  async queryClassOutlines(qualificationId: string) {
    const jsonConfig = await MsSchemeLearningQueryFrontGatewaySchemeLearningQueryForestage.getMySchemeConfig({
      qualificationId
    })
    const config = JSON.parse(jsonConfig?.data?.schemeConfig)
    console.log(config)

    return config[config.type]?.config?.courseTrainingOutlines[0].name !=
      TemplateNameManager.NOCourseTrainingOutlinesName
      ? (config[config.type]?.config?.courseTrainingOutlines as Classification[])
      : new Array<Classification>()
  }
}

export default QueryMyAutonomousCourse
