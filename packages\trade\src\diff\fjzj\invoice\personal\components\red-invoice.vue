<template>
  <el-card shadow="never" class="m-card f-mb15">
    <!--条件查询-->
    <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
    <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
    <hb-search-wrapper :model="pageQueryParam" @reset="resetCondition">
      <el-form-item label="冲红发票号">
        <el-input v-model="pageQueryParam.redBillNo" clearable placeholder="请输入冲红发票号" />
      </el-form-item>
      <el-form-item label="冲红状态">
        <el-select
          v-model="pageQueryParam.redInvoiceItemBillStatusList"
          clearable
          filterable
          placeholder="请选择冲红状态"
        >
          <el-option
            v-for="item in redInvoiceStatus"
            :label="item.name"
            :value="item.value"
            :key="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="购买人姓名">
        <el-input v-model="pageQueryParam.userName" clearable placeholder="请输入购买人姓名" />
      </el-form-item>
      <el-form-item label="登录账号" v-if="queryShowLoginAccount.isShowLoginAccount">
        <el-input v-model="pageQueryParam.loginAccount" clearable placeholder="请输入省平台id" />
      </el-form-item>
      <el-form-item label="证件号">
        <el-input v-model="pageQueryParam.idCard" clearable placeholder="请输入证件号" />
      </el-form-item>
      <el-form-item label="订单号">
        <el-input v-model="pageQueryParam.orderNoList" clearable placeholder="请输入订单号" />
      </el-form-item>
      <el-form-item label="开票时间">
        <double-date-picker
          :begin-create-time.sync="pageQueryParam.redBillStatusChangeTime.success.begin"
          :end-create-time.sync="pageQueryParam.redBillStatusChangeTime.success.end"
          begin-time-placeholder="红票开具时间"
          end-time-placeholder="红票开具时间"
        ></double-date-picker>
      </el-form-item>

      <el-form-item label="销售渠道" v-if="!isZtlogin">
        <el-select v-model="pageQueryParam.saleSource" clearable filterable placeholder="请选择销售渠道">
          <el-option v-for="item in saleChannelList" :key="item.code" :value="item.code" :label="item.desc"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="专题名称" v-if="topPicNameFilterShow">
        <el-input v-model="pageQueryParam.specialSubjectName" clearable placeholder="请输入专题进行查询" />
      </el-form-item>
      <el-form-item label="培训方案" v-if="!isZtlogin">
        <learning-scheme-select-diff v-model="commoditySkuIdList"></learning-scheme-select-diff>
      </el-form-item>
      <el-form-item label="培训方案" v-if="isZtlogin">
        <zt-learning-scheme-select v-model="commoditySkuIdList"></zt-learning-scheme-select>
      </el-form-item>
      <el-form-item label="期别名称" v-if="showPeriodName">
        <biz-period-select :scheme-id="hasSelectSchemeMode[0].id" v-model="pageQueryParam.periodId" />
      </el-form-item>
      <template slot="actions">
        <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
        <el-button @click="exportInvoice">导出数据</el-button>
        <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
      </template>
    </hb-search-wrapper>
    <div class="f-mt20">
      <el-alert type="warning" :closable="false" class="m-alert">
        <div class="f-c6">
          统计： 冲红发票总金额
          <span class="f-fb f-co">¥ {{ totalAmount }}</span>
          ，冲红税额总金额
          <span class="f-fb f-co">¥ {{ totalTax }}</span>
          ，冲红发票仅统计普通电子发票退款或重新开票产生。
        </div>
      </el-alert>
    </div>
    <!--表格-->
    <el-table stripe :data="pageData" class="m-table f-mt10" v-loading="query.loading" ref="redInvoiceTable">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="订单号" min-width="220" fixed="left" prop="orderNo">
        <template #default="scope"
          >{{ scope.row.orderNo || '-' }}
          <el-tag type="success" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag>

          <el-tag type="warning" size="small" v-if="scope.row.saleChannel == SaleChannelEnum.distribution"
            >分销推广
          </el-tag>

          <el-tag type="danger" size="small" v-if="scope.row.thirdPartyPlatform"
            >{{ scope.row.thirdPartyPlatform }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="蓝票发票号" min-width="220" ixed="left" prop="blueInvoiceNo">
        <template #default="scope">{{ scope.row.blueInvoiceNo || '-' }}</template>
      </el-table-column>
      <el-table-column label="红票发票号" min-width="220" ixed="left" prop="redInvoiceNo">
        <template #default="scope"> {{ scope.row.redInvoiceNo || '-' }}</template>
      </el-table-column>
      <el-table-column label="购买人信息" min-width="240">
        <template #default="scope">
          <p>姓名：{{ scope.row.name || '-' }}</p>
          <p>证件号：{{ scope.row.idCard || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="发票内容" min-width="400">
        <template #default="scope">
          <p class="f-flex">
            <span>发票抬头：</span>
            <span class="f-flex-sub"
              >【{{ invoiceTitleMapType[scope.row.titleType] }}】{{ scope.row.title || '-' }}</span
            >
          </p>
          <p>统一社会信用代码：{{ scope.row.taxpayerNo || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="冲红金额(元)" width="140" align="right" prop="redTotalAmount">
        <template #default="scope">
          {{ scope.row.redTotalAmount || '-' }}
        </template>
      </el-table-column>
      <!--      <el-table-column label="申请开票时间" min-width="180" prop="applyForDate">
        <template #default="scope">
          {{ scope.row.applyForDate || '-' }}
        </template>
      </el-table-column>-->
      <el-table-column label="冲红状态" min-width="130">
        <template #default="scope">
          <el-tooltip
            class="item"
            effect="dark"
            :content="scope.row.redInvoiceExceptionMsg ? `异常说明：${scope.row.redInvoiceExceptionMsg}` : '暂无数据'"
            placement="top"
            v-if="scope.row.redStatus === InvoiceStatusEnum.OPENING"
          >
            <el-badge is-dot :type="redInvoiceStatusMapType[scope.row.redStatus]" class="badge-status">
              {{ redInvoiceStatusMapName[scope.row.redStatus] }}
            </el-badge>
          </el-tooltip>
          <el-badge
            is-dot
            :type="redInvoiceStatusMapType[scope.row.redStatus]"
            class="badge-status"
            v-else-if="redInvoiceStatusMapName[scope.row.redStatus]"
          >
            {{ redInvoiceStatusMapName[scope.row.redStatus] }}
          </el-badge>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="160" align="center" fixed="right">
        <template #default="scope">
          <el-button
            type="text"
            size="mini"
            v-if="scope.row.redStatus == 2"
            @click="downloadRedInvoice(scope.row.redFilePath)"
            >下载冲红发票</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务管理-个人报名冲红发票</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"></hb-pagination>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import { Query, UiPage } from '@hbfe/common'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import QueryPageInvoiceParam from '@api/service/management/trade/single/invoice/query/dto/QueryPageInvoiceParam'
  import {
    BillStatusChangeTimeRequest,
    DateScopeRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import InvoiceListResponse from '@api/service/diff/management/fjzj/trade/invoice/model/InvoiceListResponse'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { InvoiceStatusEnum, TitleTypeEnum } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import QueryInvoice from '@api/service/diff/management/fjzj/trade/invoice/QueryInvoice'
  import SaleChannelType, { SaleChannelEnum } from '@api/service/diff/management/fjzj/trade/enums/SaleChannelType'
  import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import LearningSchemeSelectDiff from '@hbfe/jxjy-admin-trade/src/diff/fjzj/order/personal/components/learning-scheme-select-diff.vue'
  import FileModule from '@api/service/common/file/FileModule'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import QueryInvoiceInTrainingChannel from '@api/service/diff/management/fjzj/trade/invoice/QueryFjzjInvoiceInTrainingChannel'
  import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'
  import ZtLearningSchemeSelect from '@hbfe/jxjy-admin-trade/src/order/personal/components/zt-learning-scheme-select.vue'

  @Component({
    components: { DoubleDatePicker, LearningSchemeSelectDiff, ZtLearningSchemeSelect }
  })
  export default class extends Vue {
    SaleChannelEnum = SaleChannelEnum

    select = ''
    input = ''
    pageData: Array<InvoiceListResponse> = new Array<InvoiceListResponse>()
    form = {
      data1: ''
    }
    pageQueryParam: QueryPageInvoiceParam = new QueryPageInvoiceParam()
    exportQueryParam: QueryPageInvoiceParam = new QueryPageInvoiceParam()
    page: UiPage
    query: Query = new Query()
    //导出成功弹窗
    exportSuccessVisible = false
    InvoiceStatusEnum = InvoiceStatusEnum
    hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
    //开票总金额
    totalAmount = 0
    //发票总税额
    totalTax = 0
    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    //专题查询请求
    queryZtInvoice = new QueryInvoiceInTrainingChannel()
    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }

    redInvoiceStatus = [
      {
        name: '冲红中',
        value: InvoiceStatusEnum.OPENING
      },
      {
        name: '冲红失败',
        value: InvoiceStatusEnum.OPENERROR
      },
      {
        name: '冲红成功',
        value: InvoiceStatusEnum.OPEMSUCCESS
      }
    ]

    redInvoiceStatusMapName = {
      [InvoiceStatusEnum.OPENING]: '冲红中',
      [InvoiceStatusEnum.OPENERROR]: '冲红失败',
      [InvoiceStatusEnum.OPEMSUCCESS]: '冲红成功'
    }

    redInvoiceStatusMapType = {
      [InvoiceStatusEnum.OPENING]: 'primary',
      [InvoiceStatusEnum.OPENERROR]: 'danger',
      [InvoiceStatusEnum.OPEMSUCCESS]: 'success'
    }

    // 获取销售渠道列表
    saleChannelList = SaleChannelType.list()
    /**
     * 商品id数组
     */
    commoditySkuIdList = new Array<HasSelectSchemeMode>()
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    TrainingModeEnum = TrainingModeEnum
    // 专题名称筛选显示
    get topPicNameFilterShow() {
      return (
        this.pageQueryParam.saleSource === SaleChannelEnum.topic ||
        (!this.pageQueryParam.saleSource && this.pageQueryParam.saleSource !== SaleChannelEnum.self)
      )
    }
    // 培训方案入参
    @Watch('commoditySkuIdList', {
      deep: true
    })
    changeScheme(val: Array<HasSelectSchemeMode>) {
      this.pageQueryParam.periodId = ''
      if (val?.length && val[0]?.schemeId) {
        this.pageQueryParam.commoditySkuIdList = ''
        this.pageQueryParam.commoditySkuIdList = val[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = undefined
      }
    }

    constructor() {
      super()
      this.pageQueryParam.redBillStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.redBillStatusChangeTime.success = new DateScopeRequest()
      if (this.isZtlogin) {
        this.page = new UiPage(this.doSearchZt, this.doSearchZt)
      } else {
        this.page = new UiPage(this.doSearch, this.doSearch)
      }
    }
    get showPeriodName() {
      return [this.TrainingModeEnum.mixed, this.TrainingModeEnum.offline].includes(
        this.hasSelectSchemeMode[0]?.trainingMode?.skuPropertyValueId
      )
    }
    async resetCondition() {
      this.page.pageNo = 1
      this.pageQueryParam = new QueryPageInvoiceParam()
      this.pageQueryParam.redBillStatusChangeTime = new BillStatusChangeTimeRequest()
      this.pageQueryParam.redBillStatusChangeTime.success = new DateScopeRequest()
      this.commoditySkuIdList = new Array<HasSelectSchemeMode>()
      /*this.pageQueryParam.redBillNo = undefined
      this.pageQueryParam.redInvoiceItemBillStatusList = undefined
      this.pageQueryParam.userName = undefined
      this.pageQueryParam.idCard = undefined
      this.pageQueryParam.orderNoList = undefined
      this.pageQueryParam.redBillStatusChangeTime.success.begin = undefined
      this.pageQueryParam.redBillStatusChangeTime.success.end = undefined*/
      if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }

    queryInvoice = new QueryInvoice()

    async doSearch() {
      this.query.loading = true
      if (this.commoditySkuIdList.length) {
        this.pageQueryParam.commoditySkuIdList = this.commoditySkuIdList[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }
      try {
        this.pageQueryParam.flushed = true
        this.pageData = await this.queryInvoice.onLinePageInvoiceInServicer(this.page, this.pageQueryParam)
        this.totalAmount = this.queryInvoice.totalAmount
        this.totalTax = this.queryInvoice.totalTax
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['redInvoiceTable'] as any)?.doLayout()
        this.query.loading = false
      }
    }
    async doSearchZt() {
      this.query.loading = true
      if (this.commoditySkuIdList.length) {
        this.pageQueryParam.commoditySkuIdList = this.commoditySkuIdList[0].schemeId
      } else {
        this.pageQueryParam.commoditySkuIdList = null
      }
      try {
        this.pageQueryParam.flushed = true
        this.pageData = await this.onLinePageInvoiceInZtServicer()
        this.totalAmount = this.queryZtInvoice.totalAmount
        this.totalTax = this.queryZtInvoice.totalTax
      } catch (e) {
        this.$message.error('网络错误，请刷新重试')
        console.log(e)
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['redInvoiceTable'] as any)?.doLayout()
        this.query.loading = false
      }
    }

    /**
     * 分页查询发票
     */
    async onLinePageInvoiceInZtServicer() {
      return await this.queryZtInvoice.onLineFjzjPageInvoiceInServicer(this.page, this.pageQueryParam)
    }
    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportRedVatSpecialOnlineInvoice' }
      })
    }

    // 导出列表数据
    async exportInvoice() {
      this.exportQueryParam = Object.assign(new QueryPageInvoiceParam(), this.pageQueryParam)

      try {
        let res
        if (this.isZtlogin) {
          res = await this.onLinePageInvoiceInZtExport()
        } else {
          res = await this.queryInvoice.onLinePageInvoiceInExport(this.exportQueryParam)
        }
        if (res) {
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }
    async onLinePageInvoiceInZtExport() {
      return await this.queryZtInvoice.onLinePageInvoiceInExport(this.exportQueryParam)
    }
    //下载冲红发票
    // downloadRedInvoice(url: string) {
    //   window.open('/mfs' + url, '_blank')
    // }
    async downloadRedInvoice(baseUrl: string) {
      await FileModule.applyResourceAccessToken()
      let url = ''
      if (baseUrl && baseUrl.substring(0, 4) !== '/mfs') {
        url = this.$util.imgUrlWithToken('/mfs' + baseUrl)
      } else {
        url = this.$util.imgUrlWithToken(baseUrl)
      }
      window.open(url, '_blank')
    }

    // 导出列表数据
    async exportSpecialInvoice() {
      try {
        const res = await this.queryInvoice.onLinePageInvoiceInExport(this.exportQueryParam)
        if (res) {
          this.$message.success('导出成功')
          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }

    async created() {
      if (this.isZtlogin) {
        await this.doSearchZt()
      } else {
        await this.doSearch()
      }
    }
  }
</script>
