"""独立部署的微服务,K8S服务名:ms-identity-authentication-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取失败认证配置
		@param request
		@return
	"""
	getFailedAuthConfig(request:CredentialRequest):FailedAuthConfigResponse @optionalLogin
	"""获取单点安全配置(获取当前登录主体的单点安全配置信息)"""
	getSSOSecureConfig:GetSSOSecureConfigResponse
	"""获取短信认证配置
		@param request
		@return
	"""
	getSmsCodeAuthConfig(request:CredentialRequest):SmsCodeAuthConfigResponse @optionalLogin
}
type Mutation {
	"""保存单点安全配置
		@param request
		@return
	"""
	saveSSOSecureConfig(request:SaveSSOSecureConfigRequest):SaveSSOSecureConfigResponse
}
input ApplicationDomain @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.ApplicationDomain") {
	"""应用类型【必填】"""
	applicationType:Int
	"""应用方类型【必填】"""
	applicationMemberType:Int
}
"""<AUTHOR>
input CredentialRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.CredentialRequest") {
	"""初始token"""
	token:String!
	"""授权类型
		@see com.fjhb.ms.identity.authentication.v1.api.credential.GrantTypes
		identity_auth_token:身份凭证Token(默认授权类型)
		identity_chain_auth_token:身份认证链Token(可交由后续认证处理器使用)
	"""
	grantType:String
}
"""失败认证配置"""
input FailedAuthConfig @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.FailedAuthConfig") {
	"""失败认证配置id"""
	id:String
	"""适配的应用域【必填】"""
	applicationDomains:[ApplicationDomain]
	"""适配的凭据类型【必填】"""
	credentialTypes:[Int]
	"""认证失败次数检测周期（分钟）【必填】"""
	failedAuthAttemptDuration:Int
	"""认证失败次数上限（次）【必填】"""
	failedAuthAttemptUpperLimit:Int
	"""达到认证失败次数上限后账户锁定的时间（分钟）【必填】"""
	accountLockDuration:Int
	"""安全级别（默认为3）【必填】"""
	securityLevel:Int
	"""是否启用(默认false)【必填】"""
	enabled:Boolean
}
"""更换密码配置"""
input PasswordChangeConfig @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.PasswordChangeConfig") {
	"""更换密码配置id"""
	id:String
	"""适配的应用域【必填】"""
	applicationDomains:[ApplicationDomain]
	"""适配的凭据类型【必填】"""
	credentialTypes:[Int]
	"""更换密码周期（天）【必填】"""
	passwordChangeCycle:Int
	"""安全级别（默认为3）【必填】"""
	securityLevel:Int
	"""是否启用(默认false)【必填】"""
	enabled:Boolean
}
input SaveSSOSecureConfigRequest @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.SaveSSOSecureConfigRequest") {
	"""短信认证配置【必填】"""
	smsCodeAuthConfig:SmsCodeAuthConfig
	"""更换密码配置【必填】"""
	passwordChangeConfig:PasswordChangeConfig
	"""失败认证配置【必填】"""
	failedAuthConfig:FailedAuthConfig
}
"""短信认证配置"""
input SmsCodeAuthConfig @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.request.SmsCodeAuthConfig") {
	"""短信认证配置id"""
	id:String
	"""适配的应用域【必填】"""
	applicationDomains:[ApplicationDomain]
	"""适配的凭据类型【必填】"""
	credentialTypes:[Int]
	"""是否启用(默认false)【必填】"""
	enabled:Boolean
}
type ApplicationDomainData @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.config.ApplicationDomainData") {
	"""应用类型【必填】"""
	applicationType:Int
	"""应用方类型【必填】"""
	applicationMemberType:Int
}
"""失败认证配置"""
type FailedAuthConfigData @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.config.FailedAuthConfigData") {
	"""失败认证配置id"""
	id:String
	"""适配的应用域【必填】"""
	applicationDomainData:[ApplicationDomainData]
	"""适配的凭据类型【必填】"""
	credentialTypes:[Int]
	"""认证失败次数检测周期（分钟）【必填】"""
	failedAuthAttemptDuration:Int
	"""认证失败次数上限（次）【必填】"""
	failedAuthAttemptUpperLimit:Int
	"""达到认证失败次数上限后账户锁定的时间（分钟）【必填】"""
	accountLockDuration:Int
	"""安全级别（默认为3）【必填】"""
	securityLevel:Int
	"""是否启用(默认false)【必填】"""
	enabled:Boolean
}
type FailedAuthConfigResponse @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.config.FailedAuthConfigResponse") {
	"""认证失败次数检测周期（分钟）"""
	failedAuthAttemptDuration:Int
	"""认证失败次数上限（次）"""
	failedAuthAttemptUpperLimit:Int
	"""达到认证失败次数上限后账户锁定的时间（分钟）"""
	accountLockDuration:Int
	"""安全级别"""
	securityLevel:Int
	"""是否启用"""
	enabled:Boolean
	"""状态码
		@see IdentityAuthenticationStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
type GetSSOSecureConfigResponse @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.config.GetSSOSecureConfigResponse") {
	"""短信认证配置【必填】"""
	smsCodeAuthConfigData:[SmsCodeAuthConfigData]
	"""更换密码配置【必填】"""
	passwordChangeConfigData:[PasswordChangeConfigData]
	"""失败认证配置【必填】"""
	failedAuthConfigData:[FailedAuthConfigData]
}
"""更换密码配置"""
type PasswordChangeConfigData @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.config.PasswordChangeConfigData") {
	"""更换密码配置id"""
	id:String
	"""适配的应用域【必填】"""
	applicationDomainData:[ApplicationDomainData]
	"""适配的凭据类型【必填】"""
	credentialTypes:[Int]
	"""更换密码周期（天）【必填】"""
	passwordChangeCycle:Int
	"""安全级别（默认为3）【必填】"""
	securityLevel:Int
	"""是否启用(默认false)【必填】"""
	enabled:Boolean
}
type SaveSSOSecureConfigResponse @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.config.SaveSSOSecureConfigResponse") {
	"""状态码
		@see IdentityAuthenticationStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}
"""短信认证配置"""
type SmsCodeAuthConfigData @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.config.SmsCodeAuthConfigData") {
	"""短信认证配置id"""
	id:String
	"""适配的应用域【必填】"""
	applicationDomainData:[ApplicationDomainData]
	"""适配的凭据类型【必填】"""
	credentialTypes:[Int]
	"""是否启用(默认false)【必填】"""
	enabled:Boolean
}
"""短信验证码认证配置响应
	<AUTHOR>
"""
type SmsCodeAuthConfigResponse @type(value:"com.fjhb.ms.identity.authentication.v1.kernel.gateway.graphql.response.secure.config.SmsCodeAuthConfigResponse") {
	"""配置是否启用"""
	enabled:Boolean!
	"""状态码
		@see IdentityAuthenticationStatusEnum
	"""
	code:String
	"""响应消息"""
	message:String
}

scalar List
