import { Appraise, EveryStartHit } from '@api/service/common/models/statistic/course-appraise-statistic/Appraise'
import { CourseAppraiseStatisticDTO } from '@api/gateway/PlatformStatisticReportQuery'
import { CourseLearningStatisticDTO } from '@api/gateway/PlatformCourse'

/**
 * 分页数据
 * <AUTHOR>
 * @Date 2021/5/7/0007 9:25
 */
export class CourseAppraiseStatistic {
  /**
   * 课程id
   */
  courseId: string
  /**
   * 课程名
   */
  courseName: string
  /**
   * 学时
   */
  period: number
  /**
   * 综合评价
   */
  averageComprehensive: Appraise
  /**
   * 选课人次
   */
  chooseTimes: number
  /**
   * 已学完人次
   */
  learnedTimes: number

  /**
   * 机构名称
   */
  unitName?: string
  /**
   * 课件供应商名称
   */
  coursewareSupplierName?: string
  construct() {
    this.averageComprehensive = new Appraise()
    return this
  }
}

/**
 * 课程维度评价统计分页
 * <AUTHOR>
 * @Date 2021/5/7/0007 9:12
 */
export class CourseAppraiseStatisticPage {
  totalRow: number
  list: Array<CourseAppraiseStatistic>
  totalSize: number

  static fromRemote(remotes: Array<CourseAppraiseStatisticDTO>, counts: Array<CourseLearningStatisticDTO>) {
    const locals = new Array<CourseAppraiseStatistic>()
    remotes.forEach(remote => {
      const local = new CourseAppraiseStatistic()
      local.courseId = remote.id
      local.courseName = remote.courseName
      local.period = remote.period
      // 课程评价 - 课程列表 ：需返回课件供应商名称
      remote.coursewareSupplierName && (local.coursewareSupplierName = remote.coursewareSupplierName)
      local.averageComprehensive = new Appraise()
      local.averageComprehensive.average = remote.average
      local.averageComprehensive.everyStartHit = new EveryStartHit()
      local.averageComprehensive.everyStartHit.one = remote.one
      local.averageComprehensive.everyStartHit.two = remote.two
      local.averageComprehensive.everyStartHit.three = remote.three
      local.averageComprehensive.everyStartHit.four = remote.four
      local.averageComprehensive.everyStartHit.five = remote.five
      const count = counts.find(unit => unit.courseId === remote.id)
      local.chooseTimes = count?.selectedCount || 0
      local.learnedTimes = count?.studyFinishCount || 0
      locals.push(local)
    })
    return locals
  }
}

/**
 * 查询条件
 * <AUTHOR>
 * @Date 2021/5/7/0007 9:25
 */
export class CourseAppraiseStatisticQueryParam {
  workTypePathList?: Array<string>
  courseName?: string
  courseIds?: Array<string>
  /**
   * 课件供应商id
   */
  coursewareSupplierIdList?: Array<string>
  /**
   * 机构ID
   */
  trainingInstitutionIdList?: Array<string>
}
