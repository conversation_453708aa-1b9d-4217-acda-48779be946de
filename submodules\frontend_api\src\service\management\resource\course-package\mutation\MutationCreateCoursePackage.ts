import CreateCoursePackageVo from '@api/service/management/resource/course-package/mutation/vo/CreateCoursePackageVo'
import { ResponseStatus } from '@hbfe/common'
import CreateCoursePackageDto from '@api/service/management/resource/course-package/mutation/dto/CreateCoursePackageDto'

class MutationCreateCoursePackage {
  createCoursePackageVo: CreateCoursePackageVo

  constructor() {
    this.createCoursePackageVo = new CreateCoursePackageVo()
  }

  async doCreate(): Promise<ResponseStatus> {
    const status = await CreateCoursePackageDto.from(this.createCoursePackageVo).save()
    return new ResponseStatus(status.code, status.message)
  }
}

export default MutationCreateCoursePackage
