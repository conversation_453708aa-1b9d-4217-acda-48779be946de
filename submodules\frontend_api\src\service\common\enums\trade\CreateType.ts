/**
 * 创建方式
 * 1:系统创建
 * 2:用户创建
 * 3:管理员创建
 * 4:历史迁移
 * 5:外部接口
 */
import AbstractEnum from '../AbstractEnum'

enum CreateTypeEnum {
  system = 1,
  user = 2,
  manager = 3,
  history_moved = 4,
  external_interface = 5
}

export { CreateTypeEnum }

class CreateType extends AbstractEnum<CreateTypeEnum> {
  static enum = CreateTypeEnum

  constructor() {
    super()
    this.map[CreateTypeEnum.system] = '系统创建'
    this.map[CreateTypeEnum.user] = '用户创建'
    this.map[CreateTypeEnum.manager] = '管理员创建'
    this.map[CreateTypeEnum.history_moved] = '历史迁移'
    this.map[CreateTypeEnum.external_interface] = '外部接口'
  }
}

export default CreateType
