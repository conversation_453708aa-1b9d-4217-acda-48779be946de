schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""同意退款(此操作是在同意了退款申请后同意进行退款)
		@param refundOrderNo 退款单号
	"""
	agreeRefund(refundOrderNo:String):Void
	"""同意退款申请
		@param refundOrderNo 退款单号
		@param remark        备注
	"""
	agreeRefundApply(refundOrderNo:String,remark:String):Void
	"""申请退款
		@param applyInfo 退款申请信息
		@return 退款单
	"""
	applyRefund(applyInfo:ApplyRefundRequest):RefundOrderResponse
	"""取消订单
		@param orderNo  订单号
		@param reasonId 取消原因ID
		@param remark   取消备注
	"""
	cancelOrder(orderNo:String,reasonId:String,remark:String):Void
	"""取消退款申请
		@param refundOrderNo 退款单号
		@param reason        取消退款申请原因，可为空
	"""
	cancelRefundApply(refundOrderNo:String,reason:String):Void
	"""创建订单
		@param createInfo 订单创建信息
	"""
	createOrder(createInfo:OrderCreateRequest):OrderResponse
	"""强制关闭订单
		@param orderNo  订单号
		@param reasonId 关闭原因ID
		@param remark   关闭备注
	"""
	forceCloseOrder(orderNo:String,reasonId:String,remark:String):Void
	"""隐藏用户订单
		@param orderNo 订单号
	"""
	hideOrder(orderNo:String):Void
	"""支付订单
		@param orderPayInfo 订单支付信息
		@return 返回支付跳转的第三方支付页面地址或数据
	"""
	payOrder(orderPayInfo:OrderPayInfoRequest):String
	"""拒绝退款(此操作是在同意了退款申请后在放款给买家时拒绝的)
		@param refundOrderNo 退款单号
		@param reason        拒绝退款原因
	"""
	rejectRefund(refundOrderNo:String,reason:String):Void
	"""拒绝退款申请
		@param refundOrderNo 退款单号
		@param reason        拒绝退款申请原因
	"""
	rejectRefundApply(refundOrderNo:String,reason:String):Void
	"""更新订单发票信息
		@param updateInfo 更新信息
	"""
	updateOrderInvoice(updateInfo:OrderInvoiceUpdateRequest):Void
}
"""申请退款信息"""
input ApplyRefundRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.ApplyRefundRequest") {
	"""订单号"""
	orderNo:String
	"""子订单号"""
	subOrderNo:String
	"""退款原因ID"""
	reasonId:String
	"""退款原因"""
	reason:String
}
"""索要发票信息"""
input InvoiceRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.InvoiceRequest") {
	"""[必填]发票抬头"""
	title:String
	"""[必填]发票抬头类型
		@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTitleTypeConst
	"""
	titleType:Int!
	"""发票类型
		@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTypeConst
	"""
	type:Int!
	"""纳税人识别号，发票抬头类型为企业时必填"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账号"""
	account:String
	"""发票邮箱"""
	email:String
	"""发票备注"""
	remark:String
	"""发票的object信息集合"""
	objectList:[InvoiceObj]
	"""是否电子发票"""
	electron:Boolean!
	"""是否非税发票"""
	noTaxBill:Boolean!
}
input InvoiceObj @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.InvoiceRequest$InvoiceObj") {
	objectType:String
	objectId:String
}
"""订单创建信息"""
input OrderCreateRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.OrderCreateRequest") {
	"""销售渠道ID"""
	marketingChannelId:String
	"""发票信息"""
	invoiceInfo:InvoiceRequest
	"""子订单集合"""
	subOrderList:[SubOrderItemCreateRequest]
}
"""订单发票更新信息"""
input OrderInvoiceUpdateRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.OrderInvoiceUpdateRequest") {
	"""订单号"""
	orderNo:String
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票信息"""
	invoiceInfo:InvoiceRequest
}
input OrderPayInfoRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.OrderPayInfoRequest") {
	orderNo:String
	receiveAccountId:String
	pageUrl:String
	extParams:Map
}
input SubOrderItemCreateRequest @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.SubOrderItemCreateRequest") {
	"""商品ID"""
	commodityId:String
	"""是否需要发票"""
	needBill:Boolean!
	"""购买数量"""
	purchaseQuantity:Int!
}
"""索要发票信息"""
type InvoiceRequest1 @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.InvoiceRequest") {
	"""[必填]发票抬头"""
	title:String
	"""[必填]发票抬头类型
		@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTitleTypeConst
	"""
	titleType:Int!
	"""发票类型
		@see com.fjhb.platform.core.v1.order.api.constants.InvoiceTypeConst
	"""
	type:Int!
	"""纳税人识别号，发票抬头类型为企业时必填"""
	taxpayerNo:String
	"""地址"""
	address:String
	"""电话"""
	phone:String
	"""开户行"""
	bankName:String
	"""账号"""
	account:String
	"""发票邮箱"""
	email:String
	"""发票备注"""
	remark:String
	"""发票的object信息集合"""
	objectList:[InvoiceObj1]
	"""是否电子发票"""
	electron:Boolean!
	"""是否非税发票"""
	noTaxBill:Boolean!
}
type InvoiceObj1 @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.request.InvoiceRequest$InvoiceObj") {
	objectType:String
	objectId:String
}
"""订单"""
type OrderResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.OrderResponse") {
	"""订单号"""
	orderNo:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""买家ID"""
	buyerId:String
	"""买家名称"""
	buyerName:String
	"""卖家ID"""
	sellerId:String
	"""卖家名称"""
	sellerName:String
	"""是否需要发票"""
	needInvoice:Boolean!
	"""发票索要信息"""
	invoiceInfo:InvoiceRequest1
	"""子订单列表"""
	subOrderList:[SubOrderItemResponse]
	"""订单状态
		@see com.fjhb.platform.core.v1.order.api.constants.OrderStatusConst
	"""
	orderStatus:Int!
	"""订单总金额"""
	totalAmount:BigDecimal
	"""创建方式
		@see com.fjhb.platform.core.v1.order.api.constants.OrderCreateTypeConst
	"""
	createType:Int!
	"""创建时间"""
	createTime:DateTime
}
"""退款单拓展信息"""
type RefundOrderExtResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.RefundOrderExtResponse") {
	"""业务自定义的objectType"""
	objectType:String
	"""业务自定义的objectId"""
	objectId:String
	"""备注"""
	remark:String
}
"""退款单"""
type RefundOrderResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.RefundOrderResponse") {
	"""退款单号"""
	refundServiceNo:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""主订单号"""
	refundMasterOrderNo:String
	"""子订单号"""
	refundSubOrderNo:String
	"""是否为虚拟物品"""
	refundProductVirtual:String
	"""退款金额"""
	refundAmount:BigDecimal
	"""退款状态
		@see com.fjhb.platform.core.v1.order.api.constants.RefundStatusConst
	"""
	refundStatus:Int!
	"""退款申请人ID"""
	applyUserId:String
	"""退款申请时间"""
	applyTime:DateTime
	"""退款原因ID"""
	reasonId:String
	"""退款原因描述"""
	reason:String
	"""审核时间"""
	auditTime:DateTime
	"""确认退款时间(即点击放款的时间)"""
	affirmRefundTime:DateTime
	"""退款成功或退款失败的时间(即退款单处于最终退款结果的时间)"""
	finishTime:DateTime
	"""创建类型
		@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderCreateTypeConst
	"""
	createType:Int!
	"""退款单类型
		@see com.fjhb.platform.core.v1.order.api.constants.RefundOrderTypeConst
	"""
	type:Int!
	"""退款方式
		@see com.fjhb.platform.core.v1.order.api.constants.RefundModeConst
	"""
	mode:Int!
	"""取消退款原因描述"""
	cancelReason:String
	"""取消退款申请时间"""
	cancelApplyDate:DateTime
	"""拒绝申请原因描述"""
	refuseApplyDesc:String
	"""拒绝退款原因描述"""
	refuseRefundDesc:String
	"""备注"""
	remark:String
	"""操作员ID"""
	operatorId:String
	"""拓展信息集合"""
	extList:[RefundOrderExtResponse]
}
"""子订单的外链信息"""
type SubOrderItemLinkResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.SubOrderItemLinkResponse") {
	"""子订单外链资源的objectType"""
	objectType:String
	"""子订单外链资源的objectId"""
	objectId:String
	"""备注"""
	remark:String
}
"""子订单"""
type SubOrderItemResponse @type(value:"com.fjhb.platform.core.v1.trade.kernel.gateway.graphql.response.SubOrderItemResponse") {
	"""子订单号"""
	subOrderNo:String
	"""商品溯源码"""
	commodityTraceCode:String
	"""商品ID"""
	commodityId:String
	"""商品名称"""
	commodityName:String
	"""商品图片地址"""
	photoPath:String
	"""商品规格"""
	specification:String
	"""是否为虚拟物品"""
	virtualGoods:Boolean!
	"""商品标价"""
	labelPrice:BigDecimal
	"""成交单价"""
	dealPrice:BigDecimal
	"""购买数量"""
	purchaseQuantity:Int!
	"""实付总价"""
	totalAmount:BigDecimal
	"""子订单状态
		@see com.fjhb.platform.core.v1.order.api.constants.SubOrderStatusConst
	"""
	orderStatus:Int!
	"""是否需要发票"""
	needBill:Boolean!
	"""子订单的外链资源"""
	subOrderLinks:[SubOrderItemLinkResponse]
}


"""建设考前相关创建订单类
	<AUTHOR> create 2020/3/13 15:22
"""
input JSKQOrderCreateInfo @type(value:"com.fjhb.fjwjw.trade.appservice.dto.JSKQOrderCreateInfo") {
	"""培训机构id"""
	unitId:String
	marketingChannelId:String
	invoiceInfo:InvoiceRequestDTO
	subOrderList:[SubOrderItemCreateInfo]
}
input InvoiceRequestDTO @type(value:"com.fjhb.platform.core.v1.order.api.dto.InvoiceRequestDTO") {
	title:String
	titleType:Int!
	type:Int!
	taxpayerNo:String
	address:String
	phone:String
	bankName:String
	account:String
	email:String
	remark:String
	objectList:[InvoiceObjDTO]
	electron:Boolean!
	noTaxBill:Boolean!
}
input InvoiceObjDTO @type(value:"com.fjhb.platform.core.v1.order.api.dto.InvoiceRequestDTO$InvoiceObjDTO") {
	objectType:String
	objectId:String
}
input SubOrderItemCreateInfo @type(value:"com.fjhb.platform.core.v1.trade.kernel.appservice.dto.SubOrderItemCreateInfo") {
	commodityId:String
	needBill:Boolean!
	purchaseQuantity:Int!
}
type InvoiceRequestDTO1 @type(value:"com.fjhb.platform.core.v1.order.api.dto.InvoiceRequestDTO") {
	title:String
	titleType:Int!
	type:Int!
	taxpayerNo:String
	address:String
	phone:String
	bankName:String
	account:String
	email:String
	remark:String
	objectList:[InvoiceObjDTO1]
	electron:Boolean!
	noTaxBill:Boolean!
}
type InvoiceObjDTO1 @type(value:"com.fjhb.platform.core.v1.order.api.dto.InvoiceRequestDTO$InvoiceObjDTO") {
	objectType:String
	objectId:String
}
type OrderDTO @type(value:"com.fjhb.platform.core.v1.order.api.dto.OrderDTO") {
	orderNo:String
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	organizationId:String
	buyerId:String
	buyerName:String
	sellerId:String
	sellerName:String
	needInvoice:Boolean!
	receiveAccountId:String
	invoiceInfo:InvoiceRequestDTO1
	subOrderList:[SubOrderItemDTO]
	orderStatus:Int!
	totalAmount:BigDecimal
	createType:Int!
	createTime:DateTime
}
type SubOrderItemDTO @type(value:"com.fjhb.platform.core.v1.order.api.dto.SubOrderItemDTO") {
	subOrderNo:String
	commodityTraceCode:String
	commodityId:String
	commodityName:String
	photoPath:String
	specification:String
	virtualGoods:Boolean!
	labelPrice:BigDecimal
	dealPrice:BigDecimal
	purchaseQuantity:Int!
	totalAmount:BigDecimal
	orderStatus:Int!
	needBill:Boolean!
	subOrderLinks:[SubOrderItemLinkDTO]
}
type SubOrderItemLinkDTO @type(value:"com.fjhb.platform.core.v1.order.api.dto.SubOrderItemLinkDTO") {
	objectType:String
	objectId:String
	remark:String
}
extend type Mutation {
	"""创建订单
		@param createInfo 订单创建信息
	"""
	jskqCreateOrder(createInfo:JSKQOrderCreateInfo):OrderDTO
}

scalar List
