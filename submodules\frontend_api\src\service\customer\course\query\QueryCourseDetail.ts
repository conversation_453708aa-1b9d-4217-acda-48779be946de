import MsCourseLearningQueryFrontGatewayCourseLearningForestage from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'
import CourseDetail from '@api/service/customer/course/query/vo/CourseDetail'

/**
 * 查询课程详情
 */
class QueryCourseDetail extends CourseDetail {
  constructor(courseId: string) {
    super()
    this.id = courseId
  }

  id: string

  /**
   * 查询课程详情
   */
  async queryCourseDetail() {
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.getCourseInServicer(this.id)
    this.from(result.data)
  }

  /**
   * 查询教师信息
   */
  async queryTeachers() {
    const teacherIds = this.teachers.map(teacher => teacher.id)
    const result = await MsCourseLearningQueryFrontGatewayCourseLearningForestage.listTeacherInServicer(teacherIds)
    if (result.status.isSuccess()) {
      this.teachers.map(teacher => {
        const findOut = result.data.find(remoteTeacher => remoteTeacher.id === teacher.id)
        return teacher.from(findOut)
      })
    }
  }
}

export default QueryCourseDetail
