<route-meta>
{
"isMenu": true,
"title": "培训方案管理",
"sort": 2,
"icon": "icon_guanli"
}
</route-meta>
<script lang="ts">
  import SchemeManage from '@hbfe/jxjy-admin-scheme/src/manage.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    batchUpdate: [WXGLY],
    query: [WXGLY],
    enrollClose: [WXGLY],
    enrollOpen: [WXGLY],
    detail: [WXGLY],
    modify: [WXGLY],
    delete: [WXGLY],
    create: [WXGLY],
    exportList: [WXGLY],
    signUpIssue: [WXGLY],
    enterImpManagement: [WXGLY]
  })
  export default class extends SchemeManage {}
</script>
