<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/basic-data/platform/function' }">学习规则</el-breadcrumb-item>
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header">
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">基础设置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit f-p20">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="basicInfoForm" label-width="auto" class="m-form">
              <el-form-item label="适用行业：">
                {{ IndustryName(basicInfoForm.industryIdList) }}
                <div
                  class="m-left-divider"
                  v-if="hasIndustry(IndustryIdEnum.RS) && basicInfoForm.RSProperty.year.length"
                >
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">人社行业</i></el-divider>
                  <el-form ref="form" :model="form" label-width="150px" class="m-form f-mb20">
                    <el-form-item label="年度：">{{ basicInfoForm.RSProperty.yearName }} </el-form-item>
                    <el-form-item label="科目类型：">{{ basicInfoForm.RSProperty.subjectTypeName }} </el-form-item>
                    <el-form-item label="培训专业：" v-if="basicInfoForm.RSProperty.trainingProfessionalName"
                      >{{ trainingProfessionalName(basicInfoForm.RSProperty) }}
                    </el-form-item>
                  </el-form>
                </div>
                <div
                  class="m-left-divider"
                  v-if="hasIndustry(IndustryIdEnum.JS) && basicInfoForm.JSProperty.year.length"
                >
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">建设行业</i></el-divider>
                  <el-form ref="form" :model="form" label-width="150px" class="m-form f-mb20">
                    <el-form-item label="年度：">{{ basicInfoForm.JSProperty.yearName }} </el-form-item>
                    <el-form-item label="科目类型：">{{ basicInfoForm.JSProperty.subjectTypeName }} </el-form-item>
                    <el-form-item label="培训类别：">{{ basicInfoForm.JSProperty.trainingCategoryName }} </el-form-item>
                  </el-form>
                </div>
                <div
                  class="m-left-divider"
                  v-if="hasIndustry(IndustryIdEnum.WS) && basicInfoForm.WSProperty.year.length"
                >
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">职业卫生行业</i></el-divider>
                  <el-form ref="form" :model="form" label-width="150px" class="m-form f-mb20">
                    <el-form-item label="年度：">{{ basicInfoForm.WSProperty.yearName }} </el-form-item>
                    <el-form-item label="培训类别：">{{ basicInfoForm.WSProperty.trainingCategoryName }} </el-form-item>
                    <el-form-item label="培训对象：">{{ basicInfoForm.WSProperty.trainingObjectName }} </el-form-item>
                    <el-form-item label="岗位类别：">{{ basicInfoForm.WSProperty.positionCategoryName }} </el-form-item>
                  </el-form>
                </div>
                <div
                  class="m-left-divider"
                  v-if="hasIndustry(IndustryIdEnum.GQ) && basicInfoForm.GQProperty.year.length"
                >
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">工勤行业</i></el-divider>
                  <el-form ref="form" :model="form" label-width="150px" class="m-form f-mb20">
                    <el-form-item label="年度：">{{ basicInfoForm.GQProperty.yearName }} </el-form-item>
                    <el-form-item label="技术等级：">{{ basicInfoForm.GQProperty.jobLevelName }} </el-form-item>
                  </el-form>
                </div>
                <div
                  class="m-left-divider"
                  v-if="hasIndustry(IndustryIdEnum.LS) && basicInfoForm.LSProperty.year.length"
                >
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">教师行业</i></el-divider>
                  <el-form ref="form" :model="form" label-width="150px" class="m-form f-mb20">
                    <el-form-item label="年度：">{{ basicInfoForm.LSProperty.yearName }} </el-form-item>
                    <el-form-item label="学段：">{{ basicInfoForm.LSProperty.learningPhaseName }} </el-form-item>
                    <el-form-item label="学科：">{{ basicInfoForm.LSProperty.disciplineName }} </el-form-item>
                  </el-form>
                </div>
                <div
                  class="m-left-divider"
                  v-if="hasIndustry(IndustryIdEnum.YS) && basicInfoForm.YSProperty.year.length"
                >
                  <el-divider content-position="left"><i class="f-f15 f-fb f-cb">药师行业</i></el-divider>
                  <el-form ref="form" :model="form" label-width="150px" class="m-form f-mb20">
                    <el-form-item label="年度：">{{ basicInfoForm.YSProperty.yearName }} </el-form-item>
                    <el-form-item label="科目类型：">{{ basicInfoForm.YSProperty.subjectTypeName }} </el-form-item>
                  </el-form>
                </div>
              </el-form-item>
              <el-form-item label="每天学习时长：" v-if="basicInfoForm.timeMode == TimeModeEnum.learning">
                每天课程学习最多 {{ basicInfoForm.everyDayLearningHours }} 学时
              </el-form-item>
              <el-form-item label="每天学习时长：" v-else>
                每天课程学习最多 {{ basicInfoForm.everyDayLearningTime }} 分钟
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <div class="m-tit is-border-bottom">
          <span class="tit-txt">特殊规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit f-p20">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="m-sub-tit">
              <span class="tit-txt">设置不包含的方案</span>
            </div>
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-ml15">
              <el-form-item label="培训方案：">
                <!--表格-->
                <el-table :data="schemeList" max-height="500px" class="m-table f-mt5" border v-loading="loading">
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="培训方案名称" min-width="220">
                    <template v-slot="{ row }">{{ row.schemeName }}</template>
                  </el-table-column>
                  <el-table-column label="方案属性" min-width="240">
                    <template v-slot="{ row }">
                      <sku-display :sku-item="row"></sku-display>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
        <el-row type="flex" justify="center" class="width-limit f-p20">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="m-sub-tit">
              <span class="tit-txt">指定方案设置规则</span>
            </div>
            <div class="f-pl15">
              <!--表格-->
              <el-table :data="rulesInfoForm.schemeSpecialRuleList" max-height="500px" class="m-table" border>
                <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                <el-table-column label="适用培训方案" min-width="220" align="center">
                  <template v-slot="{ row }">
                    <div v-if="row.schemeNameList.length > 3">
                      <el-tooltip class="item" effect="dark" placement="top">
                        <div slot="content">
                          <p v-for="(item, index) in row.schemeNameList" :key="index">
                            {{ item + (index < row.schemeNameList.length - 1 ? '；' : '') }}
                          </p>
                        </div>
                        <div>
                          <p v-for="(item, index) in row.schemeNameList.slice(0, 3)" :key="index">
                            {{ item + (index < row.schemeNameList.length - 1 ? '；' : '') }}
                          </p>
                          <p>…</p>
                        </div>
                      </el-tooltip>
                    </div>
                    <div v-else>
                      <div>
                        <p v-for="(item, index) in row.schemeNameList" :key="index">
                          {{ item + (index < row.schemeNameList.length - 1 ? '；' : '') }}
                        </p>
                      </div>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="每天学习时长" min-width="240" align="center">
                  <template v-slot="{ row }">{{
                    row.timeMode == TimeModeEnum.learning
                      ? row.everyDayLearningHours + '学时'
                      : row.everyDayLearningTime + '分钟'
                  }}</template>
                </el-table-column>
              </el-table>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button @click="cencel()">关闭</el-button>
      </div>
    </div>
  </el-main>
</template>
<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import LearningRuleItem from '@api/service/management/online-learning-rule/LearningRuleItem'
  import RuleSchemeItem from '@api/service/management/train-class/query/vo/RuleSchemeItem'
  import { UiPage } from '@hbfe/common'
  import RuleSchemeParams from '@api/service/management/train-class/query/vo/RuleSchemeParams'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'
  import SkuDisplay from '@hbfe/jxjy-admin-platform/src/function/components/skuDisplay.vue'
  import { TimeModeEnum } from '@api/service/management/online-learning-rule/enum/TimeModeEnum'
  import TrainingCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/TrainingCategoryVo'
  import QueryTrainingCategory from '@api/service/common/basic-data-dictionary/query/QueryTrainingCategory'
  import IndustryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryVo'
  import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
  import { RSProperty } from '@api/service/management/online-learning-rule/model/Property'

  @Component({
    components: { SkuDisplay }
  })
  export default class extends Vue {
    form = {
      type: 1
    }
    select = {}

    // 表单初始化
    learningRuleForm = new LearningRuleItem()
    // 基础设置表单
    basicInfoForm = this.learningRuleForm.basicInfo
    // 规则设置表单
    rulesInfoForm = this.learningRuleForm.ruleInfo
    // 方案列表
    schemeList = new Array<RuleSchemeItem>()
    // 列表加载中
    loading = false
    // 行业枚举
    IndustryIdEnum = IndustryIdEnum

    /**
     * 每日学习时长类型
     */
    TimeModeEnum = TimeModeEnum

    /**
     * 行业信息
     */
    industryList: Array<IndustryVo> = QueryIndustry.industryList

    /**
     * 培训专业信息
     */
    trainingCategoryList: Array<TrainingCategoryVo> = QueryTrainingCategory.trainingCategoryListV2

    /**
     * 培训专业长度
     */
    trainingCategoryLen = 0

    /**
     * 培训专业名称
     */
    get trainingProfessionalName() {
      return (item: RSProperty) => {
        if (item.trainingProfessionalName) {
          if (item.trainingProfessional.length && item.trainingProfessional.length == this.trainingCategoryLen) {
            return '全部培训专业'
          } else {
            return item.trainingProfessionalName
          }
        } else {
          return ''
        }
      }
    }

    // 查询方案
    async querySchemeList() {
      const page = new UiPage()
      page.pageSize = this.learningRuleForm.ruleInfo.schemeIds.length
      const queryParams = new RuleSchemeParams()
      queryParams.schemeIds = this.learningRuleForm.ruleInfo.schemeIds
      const res = await new QueryTrainClassCommodityList().queryTrainClassCommodityList(page, {
        schemeRequest: {
          schemeName: queryParams.schemeName,
          schemeType: queryParams.trainType,
          schemeIdList: queryParams.schemeIds
        },
        skuPropertyRequest: {
          industry: queryParams.industryIds
        }
      })
      this.schemeList = res.map((item) => {
        return new QueryTrainClassCommodityList().fromRuleSchemeItem(item)
      })
    }

    // 初始化
    async init() {
      await this.getTrainMajorOptionLength()
      const res = await this.learningRuleForm.getDetail(this.$route.params.id)
      if (!res.isSuccess()) {
        this.$message.error('获取详情失败，原因：' + res.getMessage())
        return
      }
      this.basicInfoForm = this.learningRuleForm.basicInfo
      this.rulesInfoForm = this.learningRuleForm.ruleInfo
      this.loading = true
      // 只有方案类型的需要查询方案列表
      await this.querySchemeList()
      this.loading = false
    }

    /**
     * 获取培训专业选项长度
     */
    async getTrainMajorOptionLength() {
      this.industryList = QueryIndustry.industryList
      const findId = this.industryList.find((industry) => industry.id.indexOf(IndustryIdEnum.RS) != -1)?.id
      const propertyId =
        this.industryList.find((industry) => industry.id.indexOf(IndustryIdEnum.RS) != -1)?.propertyId || ''
      const res = await QueryTrainingCategory.queryTrainingCategoryV2(propertyId, findId)
      this.trainingCategoryList = res.isSuccess()
        ? QueryTrainingCategory.trainingCategoryListV2
        : ([] as TrainingCategoryVo[])
      this.trainingCategoryLen = this.trainingCategoryList.filter((el) => el.parentId !== '0')?.length
    }

    async created() {
      this.init()
    }

    // 返回上一级
    cencel() {
      this.$router.push('/basic-data/platform/function')
    }

    // 查找行业数组里是否包含对应行业
    get hasIndustry() {
      return (id: IndustryIdEnum) => {
        return this.basicInfoForm.industryIdList.find((industry) => industry.indexOf(id) != -1)
      }
    }

    // 获取行业名字
    get IndustryName() {
      return (ids: Array<string>) => {
        const r = []
        if (ids.find((id) => id === IndustryIdEnum.RS)) {
          r.push('人社行业')
        }
        if (ids.find((id) => id === IndustryIdEnum.JS)) {
          r.push('建设行业')
        }
        if (ids.find((id) => id === IndustryIdEnum.WS)) {
          r.push('职业卫生行业')
        }
        if (ids.find((id) => id === IndustryIdEnum.GQ)) {
          r.push('工勤行业')
        }
        if (ids.find((id) => id === IndustryIdEnum.LS)) {
          r.push('教师行业')
        }
        return r.join('、')
      }
    }
  }
</script>
