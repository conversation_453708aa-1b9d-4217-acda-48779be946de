/*
 * @Description: 查询列表请求参数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-29 11:53:50
 * @LastEditors: Zhu<PERSON>ong
 * @LastEditTime: 2022-11-30 15:18:25
 */

import {
  CommoditySkuRequest1,
  OrderRequest,
  ReturnOrderRequest,
  IssueInfo1
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import CheckAccountParamVo from '@api/service/management/trade/single/checkAccount/query/vo/CheckAccountParam'
import { SaleChannelEnum } from '@api/service/diff/management/fjzj/trade/enums/SaleChannelType'

export default class CheckAccountParam extends CheckAccountParamVo {
  /**
   * 商品方案id
   */
  commoditySkuId = ''
  static to(checkAccountParam: CheckAccountParam) {
    const orderRequest = Object.assign(new OrderRequest(), CheckAccountParamVo.to(checkAccountParam))
    orderRequest.orderBasicData.channelTypesList = [1, 5]
    if (checkAccountParam.saleSource || checkAccountParam.saleSource === SaleChannelEnum.self) {
      orderRequest.saleChannels = [checkAccountParam.saleSource]
    } else {
      orderRequest.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic,
        SaleChannelEnum.huayi
      ]
    }
    orderRequest.saleChannelName = orderRequest.saleChannels.includes(SaleChannelEnum.topic)
      ? checkAccountParam.specialSubjectName
      : ''
    orderRequest.deliveryCommodity = new CommoditySkuRequest1()
    orderRequest.deliveryCommodity.issueInfo = new IssueInfo1()
    orderRequest.deliveryCommodity.issueInfo.issueId = checkAccountParam.periodId
    orderRequest.deliveryCommodity.commoditySkuIdList = checkAccountParam.commoditySkuId
      ? [checkAccountParam.commoditySkuId]
      : undefined

    if (checkAccountParam.distributorId) {
      orderRequest.distributorId = checkAccountParam.distributorId
    }
    if (checkAccountParam.promotionPortalId && !checkAccountParam.isDistributionExcludePortal) {
      orderRequest.portalId = checkAccountParam.promotionPortalId
    }
    orderRequest.isDistributionExcludePortal = checkAccountParam.isDistributionExcludePortal
    return orderRequest
  }
  static toReturn(checkAccountParam: CheckAccountParam) {
    const orderRequest = Object.assign(new ReturnOrderRequest(), CheckAccountParamVo.toReturn(checkAccountParam))
    if (checkAccountParam.saleSource || checkAccountParam.saleSource === SaleChannelEnum.self) {
      orderRequest.subOrderInfo.orderInfo.saleChannels = [checkAccountParam.saleSource]
    } else {
      orderRequest.subOrderInfo.orderInfo.saleChannels = [
        SaleChannelEnum.self,
        SaleChannelEnum.distribution,
        SaleChannelEnum.topic,
        SaleChannelEnum.huayi
      ]
    }
    orderRequest.subOrderInfo.orderInfo.saleChannelName = orderRequest.subOrderInfo.orderInfo.saleChannels.includes(
      SaleChannelEnum.topic
    )
      ? checkAccountParam.specialSubjectName
      : ''
    if (checkAccountParam.distributorId) {
      orderRequest.distributorId = checkAccountParam.distributorId
    }
    if (checkAccountParam.promotionPortalId && !checkAccountParam.isDistributionExcludePortal) {
      orderRequest.portalId = checkAccountParam.promotionPortalId
    }
    orderRequest.isDistributionExcludePortal = checkAccountParam.isDistributionExcludePortal

    return orderRequest
  }
}
