import { Page } from '@hbfe/common'
import CheckAccountParam from '@api/service/management/trade/single/checkAccount/query/vo/CheckAccountParam'
import CheckAccountListResponse from '@api/service/diff/management/xmlg/trade/order/model/CheckAccountListResponse'
import RefundCheckAccountParam from '@api/service/management/trade/single/checkAccount/query/vo/RefundCheckAccountParam'
import RefundCheckAccountListResponse from '@api/service/diff/management/xmlg/trade/order/model/RefundCheckAccountListResponse'
import QueryCheckAccountInTrainingChannel from '@api/service/management/trade/single/checkAccount/query/QueryCheckAccountInTrainingChannel'

export default class QueryXmlgCheckAccountInTrainingChannel extends QueryCheckAccountInTrainingChannel {
  /**
   * 报名订单分页查询
   * @param page 页数
   * @param queryCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<CheckAccountListResponse>
   */
  async queryXmlgOfRegistrationOrder(
    page: Page,
    checkAccountParam: CheckAccountParam
  ): Promise<Array<CheckAccountListResponse>> {
    const data = await this.queryOfRegistrationOrder(page, checkAccountParam)
    return data.map((item) => {
      const checkAccountListResponse = new CheckAccountListResponse()
      Object.assign(checkAccountListResponse, item)
      return checkAccountListResponse
    })
  }

  /**
   * 退款订单分页查询
   * @param page 页数
   * @param queryRefundCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<RefundCheckAccountListResponse>
   */
  async queryXmlgOfRefundOrder(
    page: Page,
    queryRefundCheckAccountParam: RefundCheckAccountParam
  ): Promise<Array<RefundCheckAccountListResponse>> {
    const data = await this.queryOfRefundOrder(page, queryRefundCheckAccountParam)
    return data.map((item) => {
      const refundCheckAccountListResponse = new RefundCheckAccountListResponse()
      Object.assign(refundCheckAccountListResponse, item)
      return refundCheckAccountListResponse
    })
  }
}
