import CreateReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/CreateReceiveAccountVo'
import MutationReceiveAccountVo from '@api/service/management/trade-info-config/mutation/vo/MutationReceiveAccountVo'
import { CreateReceiveAccountRequest, ReceiveAccountExtProperty } from '@api/ms-gateway/ms-trade-configuration-v1'

export default class CreateXYPayReceiveAccountVo extends CreateReceiveAccountVo {
  /**
   * 支付账号类型id
   * 支付宝:ALIPAY
   * 微信：WXPAY
   * 支付宝H5:ALIPAYH5
   * 微信H5：WXPAYH5
   */
  paymentChannelId = ''
  /**
   * 应用id
   */
  xyPayAppId = ''
  /**
   * 公众号或小程序id
   */
  xyPaySubAppId = ''
  /**
   * 终端编号
   */
  terminalId = ''
  /**
   * SM2签名私钥
   */
  sm2key = ''
  /**
   * 响应公钥
   */
  resPublicKey = ''
  /**
   * 请求私钥
   */
  reqKey = ''

  constructor(type: string) {
    super()
    this.paymentChannelId = type
  }

  from(mutationReceiveAccountVo: MutationReceiveAccountVo) {
    this.accountType = mutationReceiveAccountVo.accountType
    this.accountNo = mutationReceiveAccountVo.accountNo
    this.accountName = mutationReceiveAccountVo.accountName
    this.qrScanPrompt = mutationReceiveAccountVo.qrScanPrompt
    this.taxPayerId = mutationReceiveAccountVo.taxPayerId
    this.refundWay = mutationReceiveAccountVo.refundWay
    this.paymentChannelId = mutationReceiveAccountVo.paymentChannelId
    this.xyPayAppId = mutationReceiveAccountVo.xyPayAppId
    this.terminalId = mutationReceiveAccountVo.terminalId
    this.sm2key = mutationReceiveAccountVo.sm2key
    this.resPublicKey = mutationReceiveAccountVo.resPublicKey
    this.reqKey = mutationReceiveAccountVo.reqKey
    this.xyPaySubAppId = mutationReceiveAccountVo.xyPaySubAppId
  }

  to(): CreateReceiveAccountRequest {
    const createReceiveAccountRequest = new CreateReceiveAccountRequest()
    createReceiveAccountRequest.accountType = 1
    createReceiveAccountRequest.paymentChannelId = this.paymentChannelId
    createReceiveAccountRequest.accountNo = this.accountNo
    createReceiveAccountRequest.name = this.accountName
    createReceiveAccountRequest.qrScanPrompt = this.qrScanPrompt
    createReceiveAccountRequest.refundWay = this.refundWay
    createReceiveAccountRequest.taxPayerId = this.taxPayerId
    createReceiveAccountRequest.properties = new Array<ReceiveAccountExtProperty>()
    createReceiveAccountRequest.properties.push({ name: 'appId', value: this.xyPayAppId })
    createReceiveAccountRequest.properties.push({ name: 'terminalId', value: this.terminalId })
    createReceiveAccountRequest.properties.push({ name: 'requestParamEncryptKey', value: this.reqKey })
    createReceiveAccountRequest.properties.push({ name: 'responsePublicKey', value: this.resPublicKey })
    createReceiveAccountRequest.properties.push({ name: 'requestPrivateKey', value: this.sm2key })
    createReceiveAccountRequest.properties.push({ name: 'subAppid', value: this.xyPaySubAppId })
    return createReceiveAccountRequest
  }
}
