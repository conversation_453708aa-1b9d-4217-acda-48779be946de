import { ExchangeOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { ExchangeOrderStatue } from '@api/service/common/trade/ExchangeOrderStatue'
import ExchangeOrderResponseVo from '@api/service/management/trade/single/order/query/vo/ExchangeOrderResponseVo'
import { ExchangeOrderRecordVo } from '@api/service/management/trade/single/order/query/vo/ExchangeOrderRecordVo'

export default class ExchangeDetailVo extends ExchangeOrderResponseVo {
  records: ExchangeOrderRecordVo[] = []
  // 0: 申请换货
  // 1: 取消中
  // 2: 退货中
  // 3: 退货失败
  // 4: 申请发货
  // 5: 发货中
  // 6: 换货完成
  // 7: 已关闭

  readonly stateValueTree = {
    '0': {
      parent: '',
      getModel: () => {
        return new ExchangeOrderRecordVo(this.basicData.statusChangeTime.applied, '发起换班')
      }
    },
    '2': {
      parent: '0',
      getModel: () => {
        return new ExchangeOrderRecordVo(this.basicData.statusChangeTime.returning, '退班处理中')
      }
    },
    '3': {
      parent: '2',
      getModel: () => {
        return new ExchangeOrderRecordVo(this.basicData.statusChangeTime.returnFailed, '退班失败')
      }
    },
    '4': {
      parent: '2',
      getModel: () => {
        return new ExchangeOrderRecordVo(this.basicData.statusChangeTime.deliveryApplied, '申请发货')
      }
    },
    '5': {
      parent: '4',
      getModel: () => {
        return new ExchangeOrderRecordVo(this.basicData.statusChangeTime.delivering, '发货处理中')
      }
    },
    '7': {
      parent: '5',
      getModel: () => {
        return new ExchangeOrderRecordVo(this.basicData.statusChangeTime.exchanged, '换班成功')
      }
    },
    '1': {
      parent: '0',
      getModel: () => {
        return new ExchangeOrderRecordVo('', '')
      }
    },
    '6': {
      parent: '0',
      getModel: () => {
        return new ExchangeOrderRecordVo('', '')
      }
    },
    '8': {
      parent: '0',
      getModel: () => {
        return new ExchangeOrderRecordVo('', '')
      }
    }
  }

  addRecords() {
    //this.basicData.status
    this.readTree(this.basicData.status + '')
  }

  readTree(states: string) {
    const record = this.stateValueTree[states]
    console.log('获得的模型', record.getModel())
    if (record) {
      const recordModel = record.getModel()
      recordModel.stateDescription && this.records.unshift(recordModel)
      if (record.parent != '') {
        this.readTree(record.parent)
      }
    }
  }
}
