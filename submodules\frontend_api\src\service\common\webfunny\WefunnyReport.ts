import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import Context from '@api/service/common/context/Context'
import UserModule from '@api/service/customer/user/UserModule'
import BaseEventTracking, { Env } from '@hbfe/base-event-tracking/src/index'
import { WebfunnyConfig, ScreeningErrorPoint } from '@api/service/common/webfunny/config/ConfigInfo'
import BaseReportModel from '@api/service/common/webfunny/models/BaseReportModel'
import Environment from '@api/service/common/utils/Env'
interface EventTrack {
  projectId: string
  pointId: string
  projectKey: string
  timeoutSecond: string
}
export interface UpErrorTrack {
  response: string
  bizCode: string
  httpCode: string
  ciphertext: string
}

class WebfunnyReport extends BaseEventTracking {
  webfunnyConfig = new WebfunnyConfig()
  /***
   * 是否初始化完成
   */
  private initOver = false
  /**
   * 是否启用 字符串的布尔值
   */
  private webfunnySwitch = 'false'
  /**
   * webfunny体系下的项目ID
   */
  private projectId = ''
  /**
   * 业务系统的项目Key(当前项目名称)projectKey 业务系统的项目Key(当前项目名称)
   */
  private projectKey = 'jxjy_customer'
  /**
   * 定位ID
   */
  private pointId = 215
  /**
   * 上报慢请求时间
   */
  private timeoutSecond = 2000
  constructor() {
    super()
    // 初始化配置
    // this.initWithConfig(
    //   '{"env":"dev","status":true,"projectId":"event1054","bizKey":"btglxt","nomarlEvent":{"slowRQ":{"id":"68","limit":1000},"networkError":{"id":"69","exHttp":[200,401]}}}'
    // )
  }

  /**
   * 最初初始化
   */
  beforeInit() {
    this.webfunnySwitch = ConfigCenterModule.getFrontendApplication(frontendApplication.webfunnySwitch)
    if (this.webfunnySwitch === 'false') {
      console.log('不启用')
      return
    }
    if (Environment.isProxyInnerNetworkEnv) {
      this.projectId = this.webfunnyConfig.config.dev.projectId
      this.init(this.projectId, this.projectKey, 'tourist', Env.dev)
      const userModule = UserModule.queryUserFactory.getQueryUserInfo()
      this.setUserId(userModule.userInfo.userInfo.userId)
    } else {
      this.projectId = this.webfunnyConfig.config.release.projectId
      this.init(this.projectId, this.projectKey, 'tourist', Env.release)
      const userModule = UserModule.queryUserFactory.getQueryUserInfo()
      this.setUserId(userModule.userInfo.userInfo.userId)
    }
    this.initOver = true
  }
  /**
   * 登录后 重新初始化 只有在上报时去重新初始化
   */
  private login(userId: string) {
    if (Environment.isProxyInnerNetworkEnv) {
      this.init(this.projectId, this.projectKey, userId, Env.dev)
    } else {
      this.init(this.projectId, this.projectKey, userId, Env.release)
    }
  }
  private findIdByName(name: string, list: { id: number; name: string }[]): number | undefined {
    for (const item of list) {
      if (item.name === name) {
        return item.id
      }
    }
    return undefined // 如果没有找到匹配项，则返回 undefined
  }

  private removeSpecialCharacters(str: string) {
    return str.replace(/\s+/g, '_').replace(/[^\w\u4e00-\u9fa5\-_]/g, '')
  }

  /**
   * 上报完善信息
   */
  upPerfectInfo(screeningError: any) {
    try {
      if (this.webfunnySwitch === 'false') {
        console.log('不启用')
        return
      }
      const userModule = UserModule.queryUserFactory.getQueryUserInfo()
      if (userModule.userInfo.userInfo.userId) {
        this.login(userModule.userInfo.userInfo.userId)
      }
      if (Environment.isProxyInnerNetworkEnv) {
        this.pointId = this.findIdByName('通用-完善', this.webfunnyConfig.config.dev.pointIdList)
      } else {
        this.pointId = this.findIdByName('通用-完善', this.webfunnyConfig.config.release.pointIdList)
      }

      const screeningErrorPoint = { ...screeningError }
      screeningErrorPoint.pointId = this.pointId
      screeningErrorPoint.userId = userModule.userInfo.userInfo.userId
      screeningErrorPoint.servicerId = Context.businessEnvironment?.serviceToken?.tokenMeta?.servicerId
      screeningErrorPoint.domainName = location.hostname
      screeningErrorPoint.accountId = userModule.userInfo.accountInfo.accountId
      Object.keys(screeningErrorPoint).forEach((key: string) => {
        if (typeof screeningErrorPoint[key] == 'string') {
          screeningErrorPoint[key] = this.removeSpecialCharacters(screeningErrorPoint[key])
          // jsonObj[key] = btoa(jsonObj[key])
          // jsonObj[key] = encodeURIComponent(jsonObj[key])
        }
      })
      this.upMyEvents(screeningErrorPoint)
    } catch (e) {
      console.group(
        '%c%s',
        'padding:3px 60px;color:#fff;background-image: linear-gradient(to right, #ffa17f, #00223e)',
        'schemeRefundList调试'
      )
      console.log(e, '报错')
      console.groupEnd()
    }
  }
  /**
   * 上报课程异常
   */
  upCoursePlayback(screeningError: any) {
    try {
      if (this.webfunnySwitch === 'false') {
        console.log('不启用')
        return
      }
      const userModule = UserModule.queryUserFactory.getQueryUserInfo()
      if (userModule.userInfo.userInfo.userId) {
        this.login(userModule.userInfo.userInfo.userId)
      }
      if (Environment.isProxyInnerNetworkEnv) {
        this.pointId = this.findIdByName('通用--课程播放', this.webfunnyConfig.config.dev.pointIdList)
      } else {
        this.pointId = this.findIdByName('通用--课程播放', this.webfunnyConfig.config.release.pointIdList)
      }
      const screeningErrorPoint = { ...screeningError }
      screeningErrorPoint.pointId = this.pointId
      screeningErrorPoint.userId = userModule.userInfo.userInfo.userId
      screeningErrorPoint.servicerId = Context.businessEnvironment?.serviceToken?.tokenMeta?.servicerId
      screeningErrorPoint.domainName = location.hostname
      screeningErrorPoint.accountId = userModule.userInfo.accountInfo.accountId
      Object.keys(screeningErrorPoint).forEach((key: string) => {
        if (typeof screeningErrorPoint[key] == 'string') {
          screeningErrorPoint[key] = this.removeSpecialCharacters(screeningErrorPoint[key])
          // jsonObj[key] = btoa(jsonObj[key])
          // jsonObj[key] = encodeURIComponent(jsonObj[key])
        }
      })
      this.upMyEvents(screeningErrorPoint)
    } catch (e) {
      console.log('上报失败', e)
    }
  }
  /**
   * 上报
   */
  slowRQEvent(duration: number, url: string) {
    try {
      if (this.webfunnySwitch === 'false') {
        console.log('不启用')
        return
      }
      const userModule = UserModule.queryUserFactory.getQueryUserInfo()
      if (userModule.userInfo.userInfo.userId) {
        this.login(userModule.userInfo.userInfo.userId)
      }
      if (Environment.isProxyInnerNetworkEnv) {
        this.pointId = this.findIdByName('customer-网络慢请求', this.webfunnyConfig.config.dev.pointIdList)
      } else {
        this.pointId = this.findIdByName('customer-网络慢请求', this.webfunnyConfig.config.release.pointIdList)
      }

      if (this.initOver) {
        if (duration > this.timeoutSecond) {
          this.upSlowRQEvent(duration, url, this.pointId)
          console.log(`Request ${url} duration: ${duration} ms`)
        }
      }
    } catch (e) {
      console.log('上报失败', e)
    }
  }
  //华医网跳转页面-异常上报
  upErrorRQEvent(upData: UpErrorTrack) {
    if (Environment.isProxyInnerNetworkEnv) {
      this.pointId = this.findIdByName('华医网跳转页面-customer-异常上报', this.webfunnyConfig.config.dev.pointIdList)
    } else {
      this.pointId = this.findIdByName(
        '华医网跳转页面-customer-异常上报',
        this.webfunnyConfig.config.release.pointIdList
      )
    }
    upData.response = upData.response.substr(0, 499)
    if (this.webfunnySwitch === 'false') {
      console.log('不启用')
      return
    }
    const userModule = UserModule.queryUserFactory.getQueryUserInfo()
    if (userModule.userInfo.userInfo.userId) {
      this.login(userModule.userInfo.userInfo.userId)
    } else {
      this.login(`tourist`)
    }
    if (!this.pointId) {
      this.pointId = this.getPointNetworkError().id
    }
    if (this.pointId <= 0) return

    const comments = { ...upData, pointId: this.pointId, projectKey: location.hostname }
    if (this.initOver) {
      this.upMyEvents(comments)
    }
  }
  /**
   *
   * @param eventType 点位名称
   * @param eventDetails
   * @returns 公用触发点位
   */
  upCommonEvent(eventType: string, eventDetails: BaseReportModel) {
    try {
      if (this.webfunnySwitch === 'false') {
        console.log('Feature disabled')
        return
      }

      const userModule = UserModule.queryUserFactory.getQueryUserInfo()
      if (userModule.userInfo.userInfo.userId) {
        this.login(userModule.userInfo.userInfo.userId)
      }
      const pointId = Environment.isProxyInnerNetworkEnv
        ? this.findIdByName(eventType, this.webfunnyConfig.config.dev.pointIdList)
        : this.findIdByName(eventType, this.webfunnyConfig.config.release.pointIdList)
      eventDetails.pointId = pointId
      eventDetails.userId = userModule.userInfo.userInfo.userId
      eventDetails.servicerId = Context.businessEnvironment?.serviceToken?.tokenMeta?.servicerId
      eventDetails.domainName = location.hostname

      this.upMyEvents(eventDetails)
    } catch (e) {
      console.log('上报失败', e)
    }
  }
  /**
   *网络异常上报
   * @param eventType 点位名称
   * @param eventDetails
   * @returns
   */
  upErrorNetEvent(eventType: string, response: any) {
    try {
      if (this.webfunnySwitch === 'false') {
        console.log('Feature disabled')
        return
      }

      const userModule = UserModule.queryUserFactory.getQueryUserInfo()
      if (userModule.userInfo.userInfo.userId) {
        this.login(userModule.userInfo.userInfo.userId)
      }
      const pointId = Environment.isProxyInnerNetworkEnv
        ? this.findIdByName(eventType, this.webfunnyConfig.config.dev.pointIdList)
        : this.findIdByName(eventType, this.webfunnyConfig.config.release.pointIdList)
      this.upNetWorkErrorEvent(response, pointId)
    } catch (e) {
      console.log('上报失败', e)
    }
  }
}
export default new WebfunnyReport()
