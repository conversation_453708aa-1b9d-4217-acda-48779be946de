import { getModule, Module, VuexModule, Mutation, Action } from 'vuex-module-decorators'
import store from '@/store'
import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'

@Module({
  name: 'PremiumCourseStore',
  dynamic: true,
  namespaced: true,
  store
})
/**
 * ui交互用的状态层
 */
class PremiumCourseStore extends VuexModule {
  courseList: CourseListDetail[] = []

  get getCourseList() {
    return this.courseList
  }
  @Mutation
  SET_COURSE_LIST(courseList: CourseListDetail[]) {
    this.courseList = courseList
  }
}

export default getModule(PremiumCourseStore)
