import { ResponseStatus } from '@hbfe/common'
import ExamCategoryGateway from '@api/ms-gateway/ms-examextraction-v1'
import UpdateExamPaperCategoryDto from './vo/UpdateExamPaperCategoryVo'

/**
 * @description: 试卷分类基础操作
 * @param {*}
 * @return {*}
 */
class ExamPaperCategoryAction {
  updateExamPaperCategoryDto: UpdateExamPaperCategoryDto = new UpdateExamPaperCategoryDto()

  constructor(id: string, name: string, parentId: string, sort?: number) {
    this.updateExamPaperCategoryDto.id = id
    this.updateExamPaperCategoryDto.name = name
    this.updateExamPaperCategoryDto.parentId = parentId
    this.updateExamPaperCategoryDto.sort = sort
  }

  /**
   * @description: 编辑试卷分类
   * @param {*}
   * @return {*}
   */
  async doUpdateExamPaperCategory(): Promise<ResponseStatus> {
    const { status } = await ExamCategoryGateway.updatePaperPublishConfigureCategory(this.updateExamPaperCategoryDto)
    return status
  }

  /**
   * @description: 删除试卷分类
   * @param {*}
   * @return {*}
   */
  async doDeleteExamPaperCategory(): Promise<ResponseStatus> {
    const { status } = await ExamCategoryGateway.removePaperPublishConfigureCategory(this.updateExamPaperCategoryDto.id)
    return status
  }
}
export default ExamPaperCategoryAction
