<route-meta>
{
"isMenu": true,
"title": "单位管理员管理",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="activated">
    <div class="f-p15">
      <div class="f-mb15">
        <el-button
          type="primary"
          icon="el-icon-plus"
          @click="addAccount"
          v-if="$hasPermission('create')"
          desc="创建"
          actions="@hbfe/jxjy-admin-account/src/unit-account/create.vue"
          >新增管理员</el-button
        >
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <hb-search-wrapper @reset="reset" class="m-query is-border-bottom">
          <el-form-item label="管理员名称">
            <el-input clearable placeholder="请输入管理员名称" v-model="unitList.params.name" />
          </el-form-item>
          <el-form-item label="管理员帐号">
            <el-input clearable placeholder="请输入管理员帐号" v-model="unitList.params.account" />
          </el-form-item>
          <el-form-item label="帐号状态">
            <el-select v-model="unitList.params.status" clearable filterable placeholder="请选择帐号状态">
              <el-option
                v-for="item in utilAdministratorStatusList"
                :key="item.code"
                :label="item.desc"
                :value="item.code"
                >{{ item.desc }}</el-option
              >
            </el-select>
          </el-form-item>
          <el-form-item label="所属单位">
            <el-input clearable placeholder="请输入单位名称" v-model="unitList.params.unitName" />
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" :loading="loading" @click="search">查询</el-button>
          </template>
        </hb-search-wrapper>
        <!--表格-->
        <el-table ref="tableRef" stripe :data="unitList.list" max-height="500px" class="m-table" v-loading="loading">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="姓名" min-width="100" fixed="left" prop="name"> </el-table-column>
          <el-table-column label="帐号" min-width="150" prop="account"> </el-table-column>
          <el-table-column label="手机号" min-width="150" prop="phone"> </el-table-column>
          <el-table-column label="所属单位" min-width="150" prop="unitName"> </el-table-column>
          <el-table-column label="状态" min-width="100">
            <template v-slot="{ row }">
              <div v-if="row.status == UtilAdministratorStatusEnum.enable">
                <el-badge is-dot type="success" class="badge-status">正常</el-badge>
              </div>
              <div v-if="row.status == UtilAdministratorStatusEnum.disable">
                <el-badge is-dot type="info" class="badge-status">停用</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220" align="center" fixed="right">
            <template v-slot="{ row }">
              <div>
                <el-button
                  type="text"
                  size="mini"
                  @click="detailAccount(row)"
                  v-if="$hasPermission('detail')"
                  desc="详情"
                  actions="lookUserDetail"
                  >详情</el-button
                >
                <el-button
                  type="text"
                  size="mini"
                  @click="modifyAccount(row)"
                  v-if="$hasPermission('modify')"
                  desc="修改"
                  actions="@hbfe/jxjy-admin-account/src/monographic-account/modify.vue"
                  >修改</el-button
                >
                <template v-if="$hasPermission('resetPassword')" desc="重置密码" actions="resetPassword">
                  <hb-popconfirm
                    placement="top"
                    title="确认重置该账号的密码为dwgly123吗？"
                    @confirm="resetPassword(row)"
                    confirm-button-text="确定重置"
                  >
                    <el-button slot="reference" type="text" size="mini" :loading="businessLoading.resetPassword[row.id]"
                      >重置密码</el-button
                    >
                  </hb-popconfirm></template
                >
                <template v-if="$hasPermission('deactivate')" desc="停用" actions="doDeactivate">
                  <hb-popconfirm
                    placement="top"
                    v-if="row.status == UtilAdministratorStatusEnum.enable"
                    title="是否停用该管理员账号"
                    @confirm="doDeactivate(row)"
                  >
                    <el-button slot="reference" type="text" size="mini" :loading="businessLoading.doDeactivate[row.id]"
                      >停用</el-button
                    >
                  </hb-popconfirm>
                </template>
                <template v-if="$hasPermission('enable')" desc="启用" actions="doEnable">
                  <hb-popconfirm
                    placement="top"
                    v-if="row.status == UtilAdministratorStatusEnum.disable"
                    title="是否启用该管理员账号"
                    @confirm="doEnable(row)"
                  >
                    <el-button slot="reference" type="text" size="mini" :loading="businessLoading.doEnable[row.id]"
                      >启用</el-button
                    >
                  </hb-popconfirm>
                </template>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import { bind, debounce } from 'lodash-decorators'
  import UnitAdministratorList from '@api/service/management/user/unit-administrator/UnitAdministratorList'
  import UtilAdministratorParams from '@api/service/management/user/unit-administrator/model/UtilAdministratorParams'
  import UnitAdministratorItem from '@api/service/management/user/unit-administrator/model/UnitAdministratorItem'
  import UtilAdministratorStatus, {
    UtilAdministratorStatusEnum
  } from '@api/service/management/user/unit-administrator/enum/UtilAdministratorStatusEnum'
  @Component
  export default class extends Vue {
    page: UiPage
    loading = false
    unitList = new UnitAdministratorList()
    UtilAdministratorStatusEnum = UtilAdministratorStatusEnum
    utilAdministratorStatusList = new UtilAdministratorStatus().list()

    /**
     * 业务按钮loading组
     */
    businessLoading: {
      resetPassword: { [key: string]: boolean }
      doDeactivate: { [key: string]: boolean }
      doEnable: { [key: string]: boolean }
    } = {
      resetPassword: {},
      doDeactivate: {},
      doEnable: {}
    }
    constructor() {
      super()
      this.page = new UiPage(this.queryList, this.queryList)
    }

    created() {
      this.search()
    }

    /**
     * 查询列表
     */
    async queryList() {
      this.loading = true
      this.unitList.queryList(this.page).finally(() => {
        this.loading = false
      })
    }

    /**
     * 搜索
     */
    @bind
    @debounce(100)
    search() {
      this.page.pageNo = 1
      this.queryList()
    }

    /**
     * 重置搜索
     */
    reset() {
      this.unitList.params = new UtilAdministratorParams()
      this.search()
    }

    /**
     * 新建
     */
    addAccount() {
      this.$router.push({
        path: '/basic-data/account/unit-account/create'
      })
    }

    /**
     * 详情
     */
    detailAccount(row: UnitAdministratorItem) {
      // todo
    }

    /**
     * 修改
     */
    modifyAccount(row: UnitAdministratorItem) {
      this.$router.push({
        path: '/'
      })
    }

    /**
     * 重置密码
     */
    @bind
    @debounce(100)
    async resetPassword(row: UnitAdministratorItem) {
      this.$set(this.businessLoading.resetPassword, row.id, true)
      await this.delay()
      row
        .resetPassword()
        .then((res) => {
          this.$message.success('操作成功')
        })
        .finally(() => {
          this.$set(this.businessLoading.resetPassword, row.id, false)
        })
    }

    /**
     * 启用
     */
    @bind
    @debounce(100)
    async doEnable(row: UnitAdministratorItem) {
      this.$set(this.businessLoading.doEnable, row.id, true)
      row
        .updateStatus()
        .then((res) => {
          this.$message.success('操作成功')
        })
        .finally(() => {
          this.$set(this.businessLoading.doEnable, row.id, false)
        })
    }

    /**
     * 停用
     */
    @bind
    @debounce(100)
    async doDeactivate(row: UnitAdministratorItem) {
      this.$set(this.businessLoading.doDeactivate, row.id, true)
      row
        .updateStatus()
        .then((res) => {
          this.$message.success('操作成功')
        })
        .finally(() => {
          this.$set(this.businessLoading.doDeactivate, row.id, false)
        })
    }

    /**
     * 模拟延时
     */
    async delay() {
      await new Promise((resolve) => setTimeout(resolve, 1000))
    }
  }
</script>
