import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum DataStatusEnum {
  INCONSISTENT = 0,
  CONSISTENT = 1
}

class DataStatus extends AbstractEnum<DataStatusEnum> {
  static enum = DataStatusEnum

  constructor(status?: DataStatusEnum) {
    super()
    this.current = status
    this.map.set(DataStatusEnum.CONSISTENT, '一致的')
    this.map.set(DataStatusEnum.INCONSISTENT, '不一致')
  }
}

export default DataStatus
