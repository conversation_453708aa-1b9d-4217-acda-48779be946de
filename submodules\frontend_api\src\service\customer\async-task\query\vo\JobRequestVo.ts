import { DateScopeRequest, JobRequest } from '@api/ms-gateway/ms-data-export-front-gateway-DataExportBackstage'
import { AsyncTaskJobStatusEnum } from '@api/service/common/enums/async-task/AsyncTaskJobStatusType'

class JobRequestVo extends JobRequest {
  /**
   * 任务名称（模糊匹配）
   */
  jobName?: string = ''

  /**
   * 任务组名（必填）
   */
  group?: string = ''

  /**
   * 执行状态
   */
  jobState?: AsyncTaskJobStatusEnum = null

  /*
    开始时间
  */
  beginTime?: string = ''

  /*
    结束时间
  */
  endTime?: string = ''

  toDto() {
    const params = new JobRequest()
    params.group = this.group
    params.jobState = this.jobState
    params.executeTimeScope = new DateScopeRequest()
    params.executeTimeScope.begin = this.beginTime
    params.executeTimeScope.end = this.endTime
    params.jobName = this.jobName
    return params
  }
}

export default JobRequestVo
