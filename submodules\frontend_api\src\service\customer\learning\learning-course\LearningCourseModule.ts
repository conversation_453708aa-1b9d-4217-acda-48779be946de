import CourseLearningGateway, {
  ChaptersPlayResponse,
  CoursePlayResponse,
  CoursewareResourcePlayResponse,
  LecturePlayResponse,
  PlayingResponse
} from '@api/gateway/CourseLearning-default'
import PlatformCourseGateway, { CourseDTO, TeacherInfoDTO } from '@api/gateway/PlatformCourse'
import $http from '@packages/request'
import moment from 'moment'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { UnAuthorize } from '@api/Secure'
import LearningCourse from '@api/service/common/models/learning/LearningCourse'
import LearningCourseChapter from '@api/service/common/models/learning/LearningCourseChapter'
import LearningCourseWare from '@api/service/common/models/learning/LearningCourseWare'
import LastLearningCourseWare from '@api/service/common/models/learning/LastLearningCourseWare'
import LearningMode from '@api/service/common/models/learning/LearningMode'
import ConfigCenterModule from '@api/service/common/config-center/ConfigCenter'
import LearningTeacher from '@api/service/common/models/learning/LearningTeacher'
import LearningResponse from '@api/service/common/models/learning/LearningResponse'
import LearningMedia from '@api/service/common/models/learning/LearningMedia'
import VideoResource from '@api/service/common/models/learning/VideoResource'
import LearningResourceType from '@api/service/common/models/learning/LearningResourceType'
import TypescriptUtils from '@api/service/customer/learning/learning-course/model/TypescriptUtils'
import VideoChapter from '@api/service/common/models/learning/VideoChapter'
import Lecture from '@api/service/common/models/learning/Lecture'
import { ResponseStatus } from '../../../../Response'
import { Constants } from '@api/service/common/models/common/Constants'

/**
 * 本地状态数据
 */
interface LearningCourseState {
  /**
   * 学习的课程信息
   */
  learningCourse: LearningCourse
  /**
   * 课程目录列表
   */
  learningCourseChapterList: Array<LearningCourseChapter>
  /**
   * 学习的课件信息列表
   */
  learningCourseWareList: Array<LearningCourseWare>
  /**
   * 当前课程最后学习的课件及媒体
   */
  lastLearningCourseWare: LastLearningCourseWare
  /**
   * 用户编号，如果是试听模块，没有用户编号
   */
  userId: string
  /**
   * 计时间隔时间，单位秒
   */
  learningIntervalTime: number
  /**
   * 是否变更策略，变更策略时IntervalTime改变
   */
  isChangePolicy: boolean
  /**
   * 是否必须刷新，由于重算导致的学习实体变更，需要重置客户端学习模型
   */
  isMustRefresh: boolean
  /**
   * 当前子项目下用户学习的唯一票据
   */
  learningTicket: string
  /**
   * 学习参数凭证，由学习方案组件提供，状态层初始化时需要提供
   */
  learningToken: string
  /**
   * 学习模式
   */
  learningMode: LearningMode
  /**
   * 播放资源类型，1表示课程，2表示课件
   */
  resourceType: number
  /**
   * 是否加载完成学习数据信息
   */
  isLoadCache: boolean
}

const internalFunction = {
  /**
   * 获取学习能力服务请求地址
   * @return 学习能力服务地址
   */
  getLearningServiceUrl: (): string => {
    return ConfigCenterModule.getIngressByName('ingress.study')
  },
  /**
   * 获取完整的学习服务请求地址
   * @param path 地址层级数组
   */
  getLearningServiceUrlWithQuery: (...path: string[]): string => {
    return internalFunction.getLearningServiceUrl() + '/api/learning/' + path.join('/')
  },
  /**
   * 加载课程学习的课程信息
   * @param courseId 课程编号
   * @param teacherIdList 教师编号列表
   */
  getCourseInfoFromRemote: async (courseId: string, teacherIdList: Array<string>): Promise<LearningCourse> => {
    const response = await PlatformCourseGateway.getCourse(courseId)
    if (response.status.isSuccess()) {
      const courseDto: CourseDTO = response.data
      const course: LearningCourse = new LearningCourse()
      course.courseId = courseDto.id
      course.courseName = courseDto.name
      course.description = courseDto.abouts
      course.timeTotalLength = courseDto.timeLength
      course.period = courseDto.period
      course.alreadyUpdateLecture = courseDto.courseWareUpdateCount
      course.totalLecture = courseDto.courseWareCount
      course.period = courseDto.period
      course.iconPath = courseDto.iconPath
      course.supportAudition = courseDto.supportAudition
      if (teacherIdList && teacherIdList.length > 0) {
        // 加载教师信息
        const teacherListResponse = await PlatformCourseGateway.listTeachersByIds(teacherIdList)
        if (teacherListResponse.status.isSuccess()) {
          const teacherInfoList: Array<TeacherInfoDTO> = teacherListResponse.data
          const teacherList: Array<LearningTeacher> = new Array<LearningTeacher>()
          for (const teacher of teacherInfoList) {
            const teacherInfo: LearningTeacher = new LearningTeacher()
            teacherInfo.id = teacher.id
            teacherInfo.name = teacher.name
            teacherInfo.abouts = teacher.abouts
            teacherInfo.gender = teacher.gender
            teacherInfo.photo = teacher.photo
            teacherInfo.professionalTitle = teacher.professionalTitle
            teacherList.push(teacherInfo)
          }
          course.teacherList = teacherList
        } else {
          return Promise.reject(response.status)
        }
      }
      return course
    } else {
      return Promise.reject(response.status)
    }
  }
}

/**
 * 学习模块
 */
@Module({ namespaced: true, dynamic: true, name: 'CustomerLearningCourseModule', store })
class LearningCourseModule extends VuexModule implements LearningCourseState {
  //region implements
  /**
   * 学习的课程信息
   */
  public learningCourse: LearningCourse = new LearningCourse()
  /**
   * 课程目录列表
   */
  public learningCourseChapterList: Array<LearningCourseChapter> = new Array<LearningCourseChapter>()
  /**
   * 学习的课件信息列表
   */
  public learningCourseWareList: Array<LearningCourseWare> = new Array<LearningCourseWare>()
  /**
   * 当前课程最后学习的课件及媒体
   */
  public lastLearningCourseWare: LastLearningCourseWare = new LastLearningCourseWare()
  /**
   * 用户编号，如果是试听模块，没有用户编号
   */
  public userId = ''
  /**
   * 计时间隔时间，单位秒
   */
  public learningIntervalTime = 30
  /**
   * 是否变更策略，变更策略时IntervalTime改变
   */
  public isChangePolicy = false
  /**
   * 是否必须刷新，由于重算导致的学习实体变更，需要重置客户端学习模型
   */
  public isMustRefresh = false
  /**
   * 当前子项目下用户学习的唯一票据
   */
  public learningTicket = ''
  /**
   * 学习参数凭证，由学习方案组件提供，状态层初始化时需要提供
   */
  public learningToken = ''
  /**
   * 学习模式
   */
  public learningMode: LearningMode = LearningMode.UnKnow
  /**
   * 播放资源类型，1表示课程，2表示课件
   */
  public resourceType: number
  /**
   * 是否加载完成学习数据信息
   */
  public isLoadCache = false

  //endregion
  //region private

  /**
   * 更新State状态数据
   * @param params 学习响应对象
   */
  @Action
  private updateStateData(params: { learningResponse: LearningResponse; updateTicket: boolean }): void {
    const { learningResponse, updateTicket } = params
    const originalData = learningResponse.data
    this.SET_LEARNING_SCHEDULE({
      courseId: originalData.courseId,
      courseSchedule: originalData.courseSchedule,
      courseWareId: originalData.coursewareId,
      courseWareSchedule: originalData.coursewareSchedule,
      mediaId: originalData.mediaId,
      mediaSchedule: originalData.mediaSchedule,
      lastPlayScale: originalData.lastPlayScale,
      mediaTimeLength: originalData.mediaTimeLength
    })
    if (updateTicket || !this.learningTicket) {
      this.SET_LEARNING_TICKET(originalData.ticket)
    }
    if (originalData?.isChangePolicy) {
      this.SET_IS_CHANGE_POLICY(originalData.isChangePolicy)
      this.SET_LEARNING_INTERVAL_TIME(originalData.intervalTime)
    }
    if (originalData?.isMustRefresh) {
      this.SET_IS_MUST_REFRESH(originalData.isMustRefresh)
    }
  }

  /**
   * 填充学习媒体资源
   * @param resourceDto 远程媒体资源
   */
  @Action
  commitLearningMedia(resourceDto: CoursewareResourcePlayResponse) {
    const learningCourseware = this.learningCourseWareList.find(x => x.courseWareId === resourceDto.id)
    if (learningCourseware) {
      const learningMedia: LearningMedia = new LearningMedia()
      if (learningCourseware.type === 1) {
        learningMedia.mediaId = resourceDto.document.id
        learningMedia.type = resourceDto.type
        learningMedia.currentTimeLength = resourceDto.document.playedTimeLength
        learningMedia.mediaTimeLength = resourceDto.document.timeLength
        const videoResourceList: Array<VideoResource> = new Array<VideoResource>()
        const videoResource: VideoResource = new VideoResource()
        videoResource.url = resourceDto.document.path
        videoResource.resourceType = LearningResourceType.RESOURCE_PDF_DOCUMENT
        videoResourceList.push(videoResource)
        learningMedia.videoResourceList = videoResourceList
      } else {
        learningMedia.mediaId = resourceDto.video.id
        learningMedia.type = resourceDto.type
        learningMedia.currentTimeLength = resourceDto.video.playedTimeLength
        learningMedia.mediaTimeLength = resourceDto.video.timeLength
        const videoResourceList: Array<VideoResource> = new Array<VideoResource>()
        if (resourceDto.video?.hwyVideo?.videoResources.length) {
          for (const videoResourceDto of resourceDto.video.hwyVideo.videoResources) {
            const videoResource: VideoResource = new VideoResource()
            videoResource.url = videoResourceDto.path
            videoResource.resourceType = LearningResourceType.RESOURCE_HUA_WEI_VIDEO
            videoResource.quality = videoResourceDto.clarity
            videoResourceList.push(videoResource)
          }
        }
        if (resourceDto.video?.hwyVideo?.audioResource) {
          const audioResourceDto = resourceDto.video.hwyVideo.audioResource
          const videoResource: VideoResource = new VideoResource()
          videoResource.url = audioResourceDto.path
          videoResource.resourceType = LearningResourceType.RESOURCE_HUA_WEI_AUDIO
          videoResourceList.push(videoResource)
        }
        if (resourceDto.video?.externalLinksVideo?.videoResources.length) {
          for (const videoResourceDto of resourceDto.video.externalLinksVideo.videoResources) {
            const videoResource: VideoResource = new VideoResource()
            videoResource.url = videoResourceDto.url
            videoResource.resourceType = LearningResourceType.EXTERNAL_LINKS_VIDEO
            videoResource.quality = videoResourceDto.quality
            videoResourceList.push(videoResource)
          }
        }
        learningMedia.videoResourceList = videoResourceList
        const chaptersDto = resourceDto.video.chapters
        const lecturesDto = resourceDto.video.lectures
        if (chaptersDto?.length) {
          learningMedia.catalogList = chaptersDto.map(chapterDto =>
            TypescriptUtils.copyObjWhenKeyEqual<ChaptersPlayResponse, VideoChapter>(chapterDto, new VideoChapter())
          )
        }
        if (lecturesDto?.length) {
          learningMedia.lectureList = lecturesDto.map(lectureDto =>
            TypescriptUtils.copyObjWhenKeyEqual<LecturePlayResponse, Lecture>(lectureDto, new Lecture())
          )
        }
      }
      learningMedia.listenTime = resourceDto.listenTime
      this.SET_COURSE_WARE_MEDIA({
        coursewareId: learningCourseware.courseWareId,
        learningMedia: learningMedia
      })
    }
  }

  //endregion
  //region actions

  /**
   * 初始化正常学习
   * @param params 参数：learningToken - 学习参数凭证
   */
  @Action
  @UnAuthorize
  public async init(params: { learningToken: string }): Promise<ResponseStatus> {
    if (params?.learningToken) {
      const response = await CourseLearningGateway.applyPlaying(params.learningToken)
      if (response.status.isSuccess()) {
        const mode = response.data.mode - 1
        this.SET_LEARNING_MODE(mode)
        const resourceType = response.data.resourceType
        this.SET_RESOURCE_TYPE(resourceType)
        if (resourceType === 1) {
          const coursePlay: CoursePlayResponse = response.data.course
          const course: LearningCourse = await internalFunction.getCourseInfoFromRemote(
            coursePlay.id,
            coursePlay.teacherIds
          )
          if (mode === LearningMode.Learning) {
            course.currentLearningSchedule = coursePlay.schedule
            course.lastLearningTime = moment(coursePlay.lastPlayTime, Constants.DATE_PATTERN).toDate()
          }
          this.SET_LEARNING_COURSE(course)
          // 设置目录
          const courseOutlines = coursePlay.courseOutlines
          const learningCourseChapterList: Array<LearningCourseChapter> = new Array<LearningCourseChapter>()
          for (const outline of courseOutlines) {
            const chapter: LearningCourseChapter = new LearningCourseChapter()
            chapter.id = outline.id
            chapter.name = outline.name
            chapter.parentId = outline.parentId
            learningCourseChapterList.push(chapter)
          }
          this.SET_LEARNING_COURSE_CHAPTER_LIST(learningCourseChapterList)
          // 设置课件列表
          const coursewareList = coursePlay.coursewareList
          const learningCourseWareList: Array<LearningCourseWare> = new Array<LearningCourseWare>()
          for (const courseware of coursewareList) {
            const learningCourseware: LearningCourseWare = new LearningCourseWare()
            learningCourseware.courseWareId = courseware.id
            learningCourseware.courseWareName = courseware.name
            learningCourseware.courseChapterId = courseware.courseOutlineId
            learningCourseware.timeTotalLength = courseware.timeLength
            learningCourseware.type = courseware.type
            learningCourseware.customeStatus = courseware.customeStatus
            if (mode === LearningMode.Learning) {
              learningCourseware.lastLearningTime = moment(courseware.lastPlayTime, Constants.DATE_PATTERN).toDate()
              learningCourseware.currentLearningSchedule = courseware.schedule
              if (courseware.lastedPlay) {
                const lastLearningCourseWare: LastLearningCourseWare = new LastLearningCourseWare()
                lastLearningCourseWare.chapterId = courseware.courseOutlineId
                lastLearningCourseWare.courseId = course.courseId
                lastLearningCourseWare.coursewareId = courseware.id
                this.SET_LAST_LEARNING_COURSE_WARE(lastLearningCourseWare)
              }
            }
            learningCourseWareList.push(learningCourseware)
          }
          this.SET_LEARNING_COURSE_WARE_LIST(learningCourseWareList)
          // 设置当前用户
          this.SET_USER_ID(response.data.userId)
          // 存储当前学习参数凭证
          this.SET_LEARNING_TOKEN(params.learningToken)
          this.SET_LOAD_STATUS(true)
          return Promise.resolve(response.status)
        }
        return Promise.resolve(new ResponseStatus(500, '暂不支持课件学习'))
      } else {
        return Promise.reject(response.status)
      }
    } else {
      return Promise.reject(new ResponseStatus(500, '初始化异常，初始化参数无效。'))
    }
  }

  /**
   * 进行开始学习，处理远端初始化学习操作
   * @param ctx 上下文
   * @param params 参数
   * @typedef LastLearningCourseWare
   * @typedef LearningResponse
   * @return 初始化响应信息
   */
  @Action
  @UnAuthorize
  public async doStartLearningWithEvent(params: LastLearningCourseWare): Promise<ResponseStatus> {
    let errorMessage = ''
    if (!(params?.courseId && params?.coursewareId)) {
      errorMessage = '进行开始学习操作异常，参数异常。'
    } else {
      if (!this.isLoadCache) {
        errorMessage = '学习数据未加载，无法进行初始化'
      } else {
        if (this.learningCourse?.courseId !== params.courseId) {
          errorMessage = '指定初始化学习的课程与当前状态层的课程不一致，请重新进行状态层初始化'
        } else {
          const response = await CourseLearningGateway.applyCoursewarePlayingResource({
            token: this.learningToken,
            coursewareId: params.coursewareId
          })
          if (response.status.isSuccess()) {
            const resourceDto: CoursewareResourcePlayResponse = response.data
            this.commitLearningMedia(resourceDto)
            // 查询需要播放的媒体资源
            const learningCourseware = this.learningCourseWareList.find(x => x.courseWareId === params.coursewareId)
            if (learningCourseware?.learningMedia?.mediaId) {
              if (this.learningMode === LearningMode.Learning) {
                const accessTokenResponse = await CourseLearningGateway.getAssessKey({
                  token: this.learningToken,
                  courseWareId: params.coursewareId,
                  mediaId: learningCourseware.learningMedia.mediaId
                })
                if (accessTokenResponse.status.isSuccess()) {
                  const accessToken = accessTokenResponse.data
                  const response = await $http.post(
                    internalFunction.getLearningServiceUrlWithQuery('initing', accessToken)
                  )
                  const learningResponse = Object.assign(new LearningResponse(), response.data)
                  if (learningResponse.successfully()) {
                    this.updateStateData({ learningResponse, updateTicket: true })
                  } else {
                    return Promise.reject(new ResponseStatus(500, learningResponse.getMessage()))
                  }
                } else {
                  return Promise.reject(accessTokenResponse.status)
                }
              }
              return Promise.resolve(new ResponseStatus(200, ''))
            } else {
              errorMessage = '未加载到课件下的媒体资源'
            }
          } else {
            return Promise.reject(response.status)
          }
        }
      }
    }
    return Promise.reject(new ResponseStatus(500, errorMessage))
  }

  /**
   * 进行学习进度提交，默认从配置中心读取学习服务地址
   * @param params 参数： mediaSchedule - 媒体进度；currentMediaTimeLength - 当前媒体播放刻度，单位:秒；playState - 当前播放器状态，0/1/2，暂停/播放/结束
   */
  @Action
  public async doLearningWithEvent(params: {
    mediaSchedule: number
    currentMediaTimeLength: number
    playState: number
    antiToken: string
  }): Promise<ResponseStatus> {
    if (this.learningMode === LearningMode.Preview) {
      return Promise.resolve(new ResponseStatus(200, ''))
    }
    if (!this.learningTicket) {
      console.error('未进行初始化学习，无法进行进度提交')
      return Promise.reject(new Error('未进行初始化学习，无法进行进度提交'))
    }
    // eslint-disable-next-line no-async-promise-executor
    return new Promise<ResponseStatus>(async resolve => {
      let response
      try {
        response = await $http.post(
          internalFunction.getLearningServiceUrlWithQuery('timingAnti', this.learningTicket),
          params
        )
      } catch (error) {
        console.error(error, '提交进度异常')
        const response = new ResponseStatus(900)
        // 网路异常
        response.errors.push({
          code: 900,
          message: '网络异常',
          source: ''
        })

        resolve(response)
      }
      const learningResponse = Object.assign(new LearningResponse(), response.data)
      if (learningResponse.successfully()) {
        this.updateStateData({ learningResponse, updateTicket: false })
      }
      resolve(new ResponseStatus(parseInt(learningResponse.head.code), learningResponse.getMessage()))
    })
  }

  /**
   * 变更防盗链，必须在一次doStartLearningWithEvent后，才能进行变更，否则不存在课件信息
   * @param params 参数：coursewareId - 课件编号
   */
  @Action
  @UnAuthorize
  public async doChangePlayUrl(params: { coursewareId: string }): Promise<ResponseStatus> {
    const learningCourseware = this.learningCourseWareList.find(x => x.courseWareId === params.coursewareId)
    if (!learningCourseware || !learningCourseware.learningMedia) {
      return Promise.resolve(new ResponseStatus(500, '指定的课件编号下没有找到课件或者课件下媒体信息'))
    }
    if (!learningCourseware.learningMedia.isVideo()) {
      return Promise.resolve(new ResponseStatus(500, '指定的课件不是多媒体资源'))
    }
    const { status, data } = await CourseLearningGateway.applyReloadHwyVideoAntiLeech(
      learningCourseware.learningMedia.mediaId
    )
    if (!status.isSuccess()) {
      return Promise.resolve(status)
    }
    const currentVideoResourceList: Array<VideoResource> = new Array<VideoResource>()
    const videoResourceList: Array<VideoResource> = learningCourseware.learningMedia.videoResourceList
    const originList = videoResourceList.filter(
      x =>
        x.resourceType !== LearningResourceType.RESOURCE_HUA_WEI_VIDEO &&
        x.resourceType !== LearningResourceType.RESOURCE_HUA_WEI_AUDIO
    )
    if (originList?.length) {
      currentVideoResourceList.push(...originList)
    }
    // 重新设置华为云资源
    if (data?.videoResources.length) {
      for (const videoResourceDto of data?.videoResources) {
        const videoResource: VideoResource = new VideoResource()
        videoResource.url = videoResourceDto.path
        videoResource.resourceType = LearningResourceType.RESOURCE_HUA_WEI_VIDEO
        videoResource.quality = videoResourceDto.clarity
        currentVideoResourceList.push(videoResource)
      }
    }
    if (data?.audioResource) {
      const videoResource: VideoResource = new VideoResource()
      videoResource.url = data?.audioResource.path
      videoResource.resourceType = LearningResourceType.RESOURCE_HUA_WEI_AUDIO
      currentVideoResourceList.push(videoResource)
    }
    this.SET_COURSE_WARE_MEDIA_RESOURCE({ coursewareId: params.coursewareId, resourceList: currentVideoResourceList })
    return Promise.resolve(status)
  }

  /**
   * 更新课件的进度。进度回滚动作
   * @param params
   */
  @Action
  async updateCoursewareSchedule(params: { token: string; coursewareId: string; schedule: number; time: number }) {
    const { status } = await CourseLearningGateway.updateCoursewareSchedule({
      token: params.token,
      schedule: params.schedule,
      coursewareId: params.coursewareId
    })
    if (status.isSuccess()) {
      this.updateCoursewareMediaSchedule({
        coursewareId: params.coursewareId,
        schedule: params.schedule,
        lastPlayScale: params.time
      })
      return status
    }
    return Promise.reject({})
  }

  @Action
  updateCoursewareMediaSchedule(params: { coursewareId: string; schedule: number; lastPlayScale: number }) {
    this.UPDATE_COURSEWARE_MEDIA_SCHEDULE(params)
  }

  /**
   * 获取试听课程token
   * @param courseId
   */
  @Action
  @UnAuthorize
  async applyListenCourseToken(courseId: string) {
    return await CourseLearningGateway.applyListenCourseToken(courseId)
  }

  @Mutation
  UPDATE_COURSEWARE_MEDIA_SCHEDULE(params: { coursewareId: string; schedule: number; lastPlayScale: number }) {
    const courseWare = this.learningCourseWareList.find(x => x.courseWareId === params.coursewareId)
    if (courseWare) {
      courseWare.currentLearningSchedule = params.schedule
      if (courseWare.learningMedia) {
        courseWare.learningMedia.currentLearningSchedule = params.schedule
        courseWare.learningMedia.lastMediaPlayScale = params.lastPlayScale
      }
    }
  }

  //endregion
  //region getters
  /**
   * 当前学习中的课程是否包含章节，由于可能存在没有章节的情况，页面布局可以依据该方法进行判定
   * @return 是否包含章节，true/false,包含/不包含
   */
  get hasChapterList(): boolean {
    return this.learningCourseChapterList.length > 0
  }

  /**
   * 指示获取当前学习参数凭证
   * @return 学习参数凭证
   */
  get getLearningToken(): string {
    return this.learningToken
  }

  /**
   * 指示获取当前学习中的课程信息
   * @return 学习中课程的信息
   */
  get getLearningCourse(): LearningCourse | undefined {
    return this.learningCourse
  }

  /**
   * 指示获取当前学习中的课程的目录列表
   * @typedef LearningCourseChapter
   * @return 课程章节列表
   */
  get getLearningCourseChapterList(): Array<LearningCourseChapter> {
    return this.learningCourseChapterList
  }

  /**
   * 指示获取当前学习中的课程的课件列表
   * @typedef LearningCourseWare
   * @return 课件列表信息
   */
  get getLearningCourseWareList(): Array<LearningCourseWare> {
    return this.learningCourseWareList
  }

  /**
   * 指示获取指定课件下的课件信息
   */
  get getLearningCoursewareByCoursewareId() {
    return (coursewareId: string): LearningCourseWare | undefined => {
      return this.learningCourseWareList.find(x => x.courseWareId === coursewareId)
    }
  }

  /**
   * 指示获取某课程目录下课件列表
   * @return Function 参数：categoryId - 目录编号
   */
  get getLearningCourseWareListByChapterId() {
    return (categoryId: string): Array<LearningCourseWare> => {
      return this.learningCourseWareList.filter(x => x.courseChapterId === categoryId)
    }
  }

  /**
   * 指示获取当前状态层是否加载完成
   */
  get getLearningStateInitCompleted(): boolean {
    return this.isLoadCache
  }

  /**
   * 获取指定课件的媒体下讲义信息
   * @return 讲义列表
   */
  get getCoursewareMediaLectureList() {
    return (coursewareId: string): Array<Lecture> => {
      const learningCourseWare = this.learningCourseWareList.find(x => x.courseWareId === coursewareId)
      if (learningCourseWare) {
        return learningCourseWare.learningMedia.lectureList
      }
      return []
    }
  }

  /**
   * 获取指定课件的媒体下讲义章节信息
   * @return 讲义章节信息
   */
  get getCoursewareMediaLectureChapterList() {
    return (coursewareId: string): Array<VideoChapter> => {
      const learningCourseWare = this.learningCourseWareList.find(x => x.courseWareId === coursewareId)
      if (learningCourseWare) {
        return learningCourseWare.learningMedia.catalogList
      }
      return []
    }
  }

  /**
   * 获取指定课件的媒体下播放资源信息
   * @return 播放资源信息
   */
  get getCoursewareMediaVideoResourceList() {
    return (coursewareId: string): Array<VideoResource> => {
      const learningCourseWare = this.learningCourseWareList.find(x => x.courseWareId === coursewareId)
      if (learningCourseWare) {
        return learningCourseWare.learningMedia.videoResourceList
      }
      return []
    }
  }

  /**
   * 获取指定课件的媒体下媒体资源信息
   * @return 媒体资源信息
   */
  get getCoursewareMediaResource() {
    return (coursewareId: string): LearningMedia | undefined => {
      const learningCourseWare = this.learningCourseWareList.find(x => x.courseWareId === coursewareId)
      if (learningCourseWare) {
        return learningCourseWare.learningMedia
      }
      return undefined
    }
  }

  /**
   * 获取当前课程下的某个课件是否支持试听
   */
  get isApplyCourseWareSupportListen() {
    return (courseWareId: string): boolean => {
      console.log('》》》获取课件是否支持试听，courseWareId：' + courseWareId)
      console.log('》》》当前课程所有课件列表：', this.learningCourseWareList)
      const courseWare = this.learningCourseWareList?.find(x => x.courseWareId === courseWareId)
      console.log('》》》当前课件' + courseWareId + '信息', courseWare)
      return courseWare?.customeStatus === 1 || false
    }
  }

  //endregion
  //region mutations
  /**
   * 设置学习中的课程信息
   * @param learningCourse 学习的课程信息
   */
  @Mutation
  private SET_LEARNING_COURSE(learningCourse: LearningCourse) {
    this.learningCourse = learningCourse
  }

  /**
   * 设置学习中的课件列表信息
   * @param learningCourseWareList 学习的课件列表
   */
  @Mutation
  private SET_LEARNING_COURSE_WARE_LIST(learningCourseWareList: Array<LearningCourseWare>) {
    this.learningCourseWareList = learningCourseWareList
  }

  /**
   * 设置学习中的课程章节列表信息
   * @param learningCourseChapterList 学习课程章节信息列表
   */
  @Mutation
  private SET_LEARNING_COURSE_CHAPTER_LIST(learningCourseChapterList: Array<LearningCourseChapter>) {
    this.learningCourseChapterList = learningCourseChapterList
  }

  /**
   * 设置最后学习的课件及媒体
   * @param lastLearningCourseWare 最后学习的课件及媒体信息
   */
  @Mutation
  private SET_LAST_LEARNING_COURSE_WARE(lastLearningCourseWare: LastLearningCourseWare) {
    this.lastLearningCourseWare = lastLearningCourseWare
  }

  /**
   * 设置每次提交进度的间隔时间，单位：秒
   * @param intervalTime 提交间隔时间：单位秒
   */
  @Mutation
  private SET_LEARNING_INTERVAL_TIME(intervalTime: number) {
    this.learningIntervalTime = intervalTime
  }

  /**
   * 设置当前学习的用户编号
   * @param userId 用户编号
   */
  @Mutation
  private SET_USER_ID(userId: string) {
    this.userId = userId
  }

  /**
   * 设置是否变更提交策略
   * @param isChangePolicy 是否变更策略
   */
  @Mutation
  private SET_IS_CHANGE_POLICY(isChangePolicy: boolean) {
    this.isChangePolicy = isChangePolicy
  }

  /**
   * 设置是否强制重新初始化
   * @param isMustRefresh 是否强制刷新
   */
  @Mutation
  private SET_IS_MUST_REFRESH(isMustRefresh: boolean) {
    this.isMustRefresh = isMustRefresh
  }

  /**
   * 设置学习提交票据
   * @param learningTicket 学习票据
   */
  @Mutation
  SET_LEARNING_TICKET(learningTicket: string) {
    this.learningTicket = learningTicket
  }

  /**
   * 设置学习过程中的课程、课件、媒体的进度
   * @param params 参数
   */
  @Mutation
  private SET_LEARNING_SCHEDULE(params: {
    courseId: string
    courseSchedule: number
    courseWareId: string
    courseWareSchedule: number
    mediaId: string
    mediaSchedule: number
    lastPlayScale: number
    mediaTimeLength: number
  }) {
    if (this.isLoadCache) {
      if (this.learningCourse?.courseId === params.courseId) {
        this.learningCourse.currentLearningSchedule = params.courseSchedule
        const courseWare = this.learningCourseWareList.find(x => x.courseWareId === params.courseWareId)
        if (courseWare) {
          courseWare.currentLearningSchedule = params.courseWareSchedule
          if (courseWare.learningMedia && courseWare.learningMedia.mediaId === params.mediaId) {
            courseWare.learningMedia.currentLearningSchedule = params.mediaSchedule
            courseWare.learningMedia.lastMediaPlayScale = params.lastPlayScale
            courseWare.learningMedia.currentTimeLength = params.mediaTimeLength
          }
        }
      }
    } else {
      console.error('学习数据未加载，无法进行变更')
    }
  }

  /**
   * 设置初始化使用参数凭证
   * @param token 学习参数凭证
   */
  @Mutation
  private SET_LEARNING_TOKEN(token: string) {
    this.learningToken = token
  }

  /**
   * 设置学习模式
   * @param learningMode 学习模式
   */
  @Mutation
  private SET_LEARNING_MODE(learningMode: LearningMode) {
    this.learningMode = learningMode
  }

  /**
   * 设置学习模式
   * @param resourceType 播放资源类型，1表示课程，2表示课件
   */
  @Mutation
  private SET_RESOURCE_TYPE(resourceType: number) {
    this.resourceType = resourceType
  }

  /**
   * 设置状态数据加载状态
   * @param loadStatus 加载状态，true/false
   */
  @Mutation
  private SET_LOAD_STATUS(loadStatus: boolean) {
    this.isLoadCache = loadStatus
  }

  /**
   * 设置某课件的媒体资源
   * @param params 参数： coursewareId - 课件编号；learningMedia - 媒体资源
   */
  @Mutation
  private SET_COURSE_WARE_MEDIA(params: { coursewareId: string; learningMedia: LearningMedia }) {
    const courseware = this.learningCourseWareList.find(x => x.courseWareId === params.coursewareId)
    if (courseware) {
      courseware.learningMedia = params.learningMedia
    }
  }

  /**
   * 设置某课件的媒体资源
   * @param params 参数： coursewareId - 课件编号；learningMedia - 媒体资源
   */
  @Mutation
  private SET_COURSE_WARE_MEDIA_RESOURCE(params: { coursewareId: string; resourceList: Array<VideoResource> }) {
    const courseware = this.learningCourseWareList.find(x => x.courseWareId === params.coursewareId)
    if (courseware && courseware.learningMedia) {
      courseware.learningMedia.videoResourceList = params.resourceList
    }
  }

  //endregion
}

export default getModule(LearningCourseModule)
