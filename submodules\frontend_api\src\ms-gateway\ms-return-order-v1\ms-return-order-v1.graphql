"""独立部署的微服务,K8S服务名:ms-return-order-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""返回退货的原因id和原因描述的列Map,key为原因id,value为原因描述"""
	prepareReturn:Map
}
type Mutation {
	"""同意退货"""
	agreeReturnApply(request:ReturnOrderAgreeApplyRequest):ReturnOrderAgreeApplyResponse
	"""批量同意退货"""
	agreeReturnBatchApply(request:ReturnOrderAgreeBatchApplyRequest):ReturnOrderAgreeBatchApplyResponse
	"""批量确认退款，前端要求已退款给予300状态码"""
	confirmBatchRefund(request:ReturnOrderConfirmBatchRefundRequest):ReturnOrderConfirmBatchRefundResponse
	"""确认退款，前端要求已退款给予300状态码"""
	confirmRefund(request:ReturnOrderConfirmRefundRequest):ReturnOrderConfirmRefundResponse
	"""拒绝退货"""
	rejectReturnApply(request:ReturnOrderRejectApplyRequest):ReturnOrderRejectApplyResponse
	"""重新回收资源"""
	retryRecycleResource(request:ReturnOrderRetryRecycleRequest):Void
	"""继续退款"""
	retryRefund(request:ReturnOrderRetryRefundRequest):Void
	"""卖家取消退货申请"""
	sellerCancelReturnApply(request:ReturnOrderCancelApplyRequest):ReturnOrderCancelApplyResponse
}
"""退货单同意申请请求
	<AUTHOR>
"""
input ReturnOrderAgreeApplyRequest @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.request.ReturnOrderAgreeApplyRequest") {
	"""退货单号"""
	returnOrderNo:String!
	"""审批意见"""
	approveComment:String
	"""订单号"""
	orderNo:String
	"""是否自动同意退款"""
	autoAgreeReturn:Boolean!
}
"""退货单批量同意申请请求
	<AUTHOR>
"""
input ReturnOrderAgreeBatchApplyRequest @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.request.ReturnOrderAgreeBatchApplyRequest") {
	"""订单和退货单信息列表"""
	orderReturnPairs:[OrderReturnPair]!
	"""审批意见"""
	approveComment:String
	"""是否自动同意退款"""
	autoAgreeReturn:Boolean!
}
"""订单和退货单对应关系"""
input OrderReturnPair @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.request.ReturnOrderAgreeBatchApplyRequest$OrderReturnPair") {
	"""订单号"""
	orderNo:String
	"""退货单号"""
	returnOrderNo:String!
}
"""退货单申请取消请求
	<AUTHOR>
"""
input ReturnOrderCancelApplyRequest @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.request.ReturnOrderCancelApplyRequest") {
	returnOrderNo:String!
	"""订单号"""
	orderNo:String
	cancelReason:String
}
"""退货单批量确认退款请求
	<AUTHOR>
"""
input ReturnOrderConfirmBatchRefundRequest @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.request.ReturnOrderConfirmBatchRefundRequest") {
	returnOrderNoList:[String]!
}
"""退货单确认退款请求
	<AUTHOR>
"""
input ReturnOrderConfirmRefundRequest @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.request.ReturnOrderConfirmRefundRequest") {
	returnOrderNo:String
}
"""退货单拒绝申请请求
	<AUTHOR>
"""
input ReturnOrderRejectApplyRequest @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.request.ReturnOrderRejectApplyRequest") {
	returnOrderNo:String!
	"""订单号"""
	orderNo:String
	"""审批意见"""
	approveComment:String
}
input ReturnOrderRetryRecycleRequest @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.request.ReturnOrderRetryRecycleRequest") {
	returnOrderNo:String!
}
input ReturnOrderRetryRefundRequest @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.request.ReturnOrderRetryRefundRequest") {
	returnOrderNo:String!
}
type ReturnOrderAgreeApplyResponse @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.response.ReturnOrderAgreeApplyResponse") {
	code:String
	message:String
	returnOrderNo:String
}
type ReturnOrderAgreeBatchApplyResponse @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.response.ReturnOrderAgreeBatchApplyResponse") {
	returnOrderAgreeApplyResponseList:[ReturnOrderAgreeApplyResponse]
}
type ReturnOrderCancelApplyResponse @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.response.ReturnOrderCancelApplyResponse") {
	code:String
	message:String
}
type ReturnOrderConfirmBatchRefundResponse @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.response.ReturnOrderConfirmBatchRefundResponse") {
	returnOrderConfirmRefundResponseList:[ReturnOrderConfirmRefundResponse]
}
type ReturnOrderConfirmRefundResponse @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.response.ReturnOrderConfirmRefundResponse") {
	code:String
	message:String
	returnOrderNo:String
}
type ReturnOrderRejectApplyResponse @type(value:"com.fjhb.ms.returnorder.v1.kernel.gateway.graphql.response.ReturnOrderRejectApplyResponse") {
	code:String
	message:String
}

scalar List
