<template>
  <div class="f-p15">
    <el-card shadow="never" class="m-card f-mb15">
      <el-row :gutter="16" class="m-query">
        <el-form :inline="true" label-width="auto">
          <el-col :sm="12" :md="8" :xl="6">
            <el-form-item label="集体报名批次号">
              <el-input v-model="queryParams.batchOrderNo" clearable placeholder="请输入集体报名批次号" />
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="8" :xl="6">
            <el-form-item label="订单状态">
              <el-select
                v-model="queryParams.orderStatus"
                clearable
                filterable
                placeholder="请选择交易状态"
                @clear="queryParams.orderStatus = null"
              >
                <el-option v-for="item in orderStatusList" :key="item.code" :value="item.code" :label="item.desc">
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="8" :xl="6">
            <el-form-item label="批次提交时间：">
              <el-date-picker
                v-model="queryParams.applyTime"
                type="datetimerange"
                :value-format="'yyyy-MM-dd HH:mm:ss'"
                range-separator="至"
                start-placeholder="批次提交时间"
                end-placeholder="批次提交时间"
              >
              </el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :sm="12" :md="8" :xl="6" class="f-fr">
            <el-form-item class="f-tr">
              <el-button type="primary" @click="searchBase" :loading="searchLoading">查询</el-button>
              <el-button @click="resetCondition">重置</el-button>
            </el-form-item>
          </el-col>
        </el-form>
      </el-row>
      <!--表格-->
      <el-table
        v-loading="searchLoading"
        ref="batchOrderTableRef"
        stripe
        :data="batchOrderListDetailVo"
        max-height="500px"
        class="m-table"
      >
        <el-table-column type="index" label="No." width="60" align="center">
          <template slot-scope="scope"
            ><span
              v-observe-visibility="
                scope.$index === batchOrderListDetailVo.length - 1
                  ? (isVisible, entry) => {
                      lastOrderVisible(isVisible, scope.$index)
                    }
                  : null
              "
              >{{ scope.$index + 1 }}</span
            ></template
          >
        </el-table-column>
        <el-table-column label="集体报名批次号" min-width="280">
          <template slot-scope="scope">{{ scope.row.batchOrderNo }}</template>
        </el-table-column>
        <el-table-column label="缴费人次" min-width="120" align="right">
          <template slot-scope="scope">{{ scope.row.payTimes }}</template>
        </el-table-column>
        <el-table-column label="订单金额(元)" min-width="140" align="right">
          <template slot-scope="scope">
            <div>{{ scope.row.payAmount }}</div>
          </template>
        </el-table-column>
        <el-table-column label="实付金额(元)" min-width="140" align="right">
          <template slot-scope="scope">
            <div>{{ scope.row.payAmount }}</div>
          </template>
        </el-table-column>
        <el-table-column label="提交时间" min-width="180">
          <template slot-scope="scope">{{ scope.row.applyTime }}</template>
        </el-table-column>
        <el-table-column label="交易状态" min-width="120">
          <template slot-scope="scope">
            {{ orderStatus(scope.row.orderStatus) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="240" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button
              type="text"
              size="mini"
              @click="viewOrderResults(scope.row)"
              v-show="orderResultsVisible(scope.row)"
              >下单结果</el-button
            >
            <el-button type="text" size="mini" @click="viewDetail(scope.row)">批次详情</el-button>
            <el-button
              v-if="scope.row.canGetInvoice && invoiceConfig.allowAskFor"
              type="text"
              size="mini"
              @click="editInvoice(scope.row)"
              >补开发票</el-button
            >
            <el-button v-if="scope.row.canRefund" type="text" size="mini" @click="viewRefundOrder(scope.row)"
              >发起退款</el-button
            >
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <view-order-result ref="viewOrderResultRef" :visible.sync="uiConfig.dialog.viewOrderVisible"></view-order-result>
    <invoice-dialog
      :dialog-ctrl.sync="editInvoiceVisible"
      invoice-type="2"
      :batchOrderNo="batchOrderNo"
      :user-id="invoiceUserId"
      @callBack="searchBase"
    ></invoice-dialog>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Prop, Watch } from 'vue-property-decorator'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { UiPage } from '@hbfe/common'
  import EnumOption from '@api/service/common/enums/EnumOption'
  import BatchOrderTradeStatus, {
    BatchOrderTradeStatusEnum
  } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
  import InvoiceDialog from '@hbfe/jxjy-admin-customerService/src/collective/components/repair-invoice.vue'
  import QueryBatchOrderListVo from '@api/service/management/trade/batch/order/query/vo/QueryBatchOrderListVo'
  import ViewOrderResult from '@hbfe/jxjy-admin-trade/src/order/collective/components/view-order-result.vue'
  import BatchOrderListDetailVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListDetailVo'
  import { bind, debounce } from 'lodash-decorators'
  import QueryBatchOrderList from '@api/service/management/trade/batch/order/query/QueryBatchOrderList'
  import BatchOrderListPlaceOrderResultVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderListPlaceOrderResultVo'
  import { ElTable } from 'element-ui/types/table'
  import QueryTradeConfig from '@api/service/common/trade-config/query/QueryTradeConfig'
  import { InvoiceConfigResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  // 发票类型
  export enum InvoiceTypeEnum {
    /**
     *   电子发票
     */
    INVOICEELECTRONICORDINARYVAT = 'INVOICEELECTRONICORDINARYVAT',
    /**
     * 专用发票
     */
    INVOICETHESPECIALVAT = 'INVOICETHESPECIALVAT'
  }
  @Component({
    components: {
      ViewOrderResult,
      InvoiceDialog
    }
  })
  export default class extends Vue {
    @Ref('viewOrderResultRef') viewOrderResultRef: ViewOrderResult

    @Ref('batchOrderTableRef') batchOrderTableRef: ElTable

    @Prop({
      type: String,
      default: ''
    })
    userId: string

    searchLoading = false
    input = ''
    page: UiPage
    select = ''
    editInvoiceVisible = false
    batchOrderNo = ''
    invoiceUserId = ''
    /**
     * 接口查询
     */
    queryRemote: QueryBatchOrderList =
      TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryBatchOrderList
    /**
     * 交易状态列表
     */
    orderStatusList: EnumOption<BatchOrderTradeStatusEnum>[] = BatchOrderTradeStatus.list()
    queryBatchOrderList = new QueryBatchOrderList()
    queryParams: QueryBatchOrderListVo = new QueryBatchOrderListVo()
    form = {
      data1: ''
    }

    invoiceConfig: InvoiceConfigResponse = new InvoiceConfigResponse()

    constructor() {
      super()
      this.page = new UiPage()
    }

    batchOrderListDetailVo: BatchOrderListDetailVo[] = [] as BatchOrderListDetailVo[]
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    /**
     * ui控制组
     */
    uiConfig = {
      // 统计模块过渡动画
      staticLoading: false,
      dialog: {
        // 导出成功
        exportSuccessVisible: false,
        // 下单结果
        viewOrderVisible: false,
        // 关闭批次
        closeBatchOrderVisible: false,
        // 查看汇款凭证
        auditRemittanceVoucherVisible: false,
        // 申请退款
        applyOrderRefundVisible: false
      }
    }
    async created() {
      const queryTradeConfig = new QueryTradeConfig()
      this.invoiceConfig = await queryTradeConfig.hasBatchOpenInvoice()
      await this.queryOrderList()
    }

    @Watch('userId')
    async userIdChange() {
      await this.searchBase()
    }

    //修改发票信息
    async editInvoice(item: BatchOrderListDetailVo) {
      const currentTime = new Date()
      const currentYear = currentTime.getFullYear()
      const currentMonth = currentTime.getMonth() + 1
      const currentDay = currentTime.getDate()

      const overTime = this.invoiceConfig.askForInvoiceDeadline
      const overYear =
        this.invoiceConfig.askForInvoiceYearType == 1 ? currentTime.getFullYear() : currentTime.getFullYear() + 1
      const date = overTime.split('/')
      if (date?.length) {
        const overMonth = date[0]
        const overDay = date[1]
        let flag = false
        if (overYear && overMonth && overDay) {
          if (currentYear > Number(overYear)) {
            flag = true
          } else if (Number(overYear) == currentYear) {
            if (currentMonth > Number(overMonth)) {
              flag = true
            } else if (currentMonth == Number(overMonth) && currentDay > Number(overDay)) {
              flag = true
            }
          }
        }
        if (flag) {
          this.$message.error('当前补开发票已过有效期，无法申请发票。')
          return
        }
      }

      this.batchOrderNo = item.batchOrderNo
      this.invoiceUserId = this.userId
      this.editInvoiceVisible = true
    }
    /**
     * 查询集体报名订单列表
     */
    async searchBase() {
      await this.queryOrderList()
    }

    /**
     * 交易状态
     */
    get orderStatus() {
      return (status: number) => {
        return this.orderStatusList.find((item) => item.code === status)?.desc || ''
      }
    }
    /**
     * 查看下单结果
     */
    @bind
    @debounce(200)
    async viewOrderResults(row: BatchOrderListDetailVo) {
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        const batchOrderNo = row.batchOrderNo
        const result: BatchOrderListPlaceOrderResultVo = await this.queryRemote.queryPlaceBatchOrderResult(batchOrderNo)
        this.viewOrderResultRef.data = result
        this.uiConfig.dialog.viewOrderVisible = true
      } catch (e) {
        console.log(e)
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }
    /**
     * 查看退款单详情
     */
    viewRefundOrder(row: BatchOrderListDetailVo) {
      this.$router.push({
        path: '/training/trade/order/collective',
        query: {
          batchOrderNo: row.batchOrderNo ?? ''
        }
      })
    }
    /**
     * 是否展示申请退款
     */
    get isApplyRefundVisible() {
      return (item: BatchOrderListDetailVo) => {
        const optionList = Array(1).fill(BatchOrderTradeStatusEnum.Pay_Success)
        const result =
          optionList.includes(item.orderStatus) && !item.isBatchOrderWaitDelivery && item.refundInfo.enableRefund
        return result ? true : false
      }
    }
    /**
     * 是否展示下单结果【待付款、支付中、开通中、交易成功、交易关闭中、交易关闭】
     */
    get orderResultsVisible() {
      return (item: BatchOrderListDetailVo) => {
        const optionList: BatchOrderTradeStatusEnum[] = []
        optionList.push(
          BatchOrderTradeStatusEnum.Wait_Pay,
          BatchOrderTradeStatusEnum.Paying,
          BatchOrderTradeStatusEnum.Opening,
          BatchOrderTradeStatusEnum.Pay_Success,
          BatchOrderTradeStatusEnum.Closing_Pay
        )
        return optionList.includes(item.orderStatus)
      }
    }
    /**
     * 重置条件
     */
    async resetCondition() {
      this.queryParams = new QueryBatchOrderListVo()
      await this.searchBase()
    }
    /**
     * 查看详情
     */
    viewDetail(row: BatchOrderListDetailVo) {
      this.$router.push('/training/trade/order/collective/detail/' + row.batchOrderNo)
    }

    async queryOrderList(polling?: boolean) {
      if (!this.userId) {
        this.batchOrderListDetailVo = []
        return
      }
      this.searchLoading = true

      try {
        this.page.pageNo = polling ? this.page.pageNo + 1 : 1
        this.queryParams.createUserId = this.userId ? [this.userId] : []
        const res = await this.queryBatchOrderList.queryBatchOrderList(this.page, this.queryParams)
        if (polling) {
          this.batchOrderListDetailVo.push(...res)
        } else {
          this.batchOrderListDetailVo = new Array<BatchOrderListDetailVo>()
          // 操作滚动条滚动到0 否则若表格滚动条有滚动距离 刷新后还会强行拉至该滚动距离
          const tableBodyWrapper = (this.batchOrderTableRef as any).bodyWrapper
          tableBodyWrapper.scrollTop = 0
          this.batchOrderListDetailVo = res
        }
        this.searchLoading = false
        this.batchOrderTableRef.doLayout()
      } catch (e) {
        console.log(e)
        this.searchLoading = false
      }
    }

    lastOrderVisible(isVisible: boolean, index: number) {
      if (isVisible && index === this.batchOrderListDetailVo.length - 1) {
        if (this.page.pageNo >= this.page.totalPageSize) {
          return
        }
        this.queryOrderList(true)
      }
    }
  }
</script>
