import getExchangeApplyReason from './queries/getExchangeApplyReason.graphql'
import agreeExchangeApply from './mutates/agreeExchangeApply.graphql'
import rejectExchangeApply from './mutates/rejectExchangeApply.graphql'
import retryDelivery from './mutates/retryDelivery.graphql'
import retryRecycleResouce from './mutates/retryRecycleResouce.graphql'
import sellerCancelExchangeApply from './mutates/sellerCancelExchangeApply.graphql'

export {
  getExchangeApplyReason,
  agreeExchangeApply,
  rejectExchangeApply,
  retryDelivery,
  retryRecycleResouce,
  sellerCancelExchangeApply
}
