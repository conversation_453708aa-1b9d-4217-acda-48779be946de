const glob = require('fast-glob')
const dir = process.env.NODE_ENV === 'production' ? '/mnt/mfs/ms-schemas' : '\\\\192.168.1.228\\mfs\\ms-schemas'
const path = require('path')
const fs = require('fs')
const yargs = require('yargs').argv
const list = glob.sync('**/*.graphql', {
  cwd: path.join(dir, yargs.target)
})
const schemaJson = path.resolve(__dirname, `../../src/${yargs.type}-schemas.json`)
fs.writeFileSync(
  schemaJson,
  JSON.stringify(
    list.map(item => {
      return path.join(dir, yargs.target, item)
    })
  ),
  { encoding: 'utf-8' }
)
