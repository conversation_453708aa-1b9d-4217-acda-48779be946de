import StatisticInfoItem from '@api/service/management/statisticalReport/models/StatisticInfoItem'
import DistributionTiersEnumClass, {
  DistributionTiersEnum
} from '@api/service/management/statisticalReport/enums/DistributionTiersEnumClass'

export default class DistributorSalesStatisticsInfo {
  /**
   * 分销层级
   */
  distributionTiers = new DistributionTiersEnumClass()
  /**
   * 分销商名称
   */
  distributorName = ''
  /**
   * 合计
   */
  total: StatisticInfoItem = new StatisticInfoItem()
  /**
   * 直接销售
   */
  directSales: StatisticInfoItem = new StatisticInfoItem()
  /**
   * 下级销售
   */
  subSales: StatisticInfoItem = new StatisticInfoItem()
}
