import platformCourseGateway from '@api/gateway/PlatformCourse'
import CoursePool from '@api/service/common/models/course-pool/CoursePool'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'

export interface ICoursePool {
  /**
   * 取该值前请先调用listCourseInfoByIds查询
   */
  courseInfoPoolsCache: Array<CoursePool>
}
@Module({ namespaced: true, dynamic: true, name: 'CustomerCoursePoolCustomerModule', store })
class CoursePoolCustomerModule extends VuexModule implements ICoursePool {
  courseInfoPoolsCache = new Array<CoursePool>()

  @Mutation
  protected SET_COURSE_POOL_INFO_LIST(courseInfoPoolsCache: Array<CoursePool>) {
    this.courseInfoPoolsCache = courseInfoPoolsCache
  }

  @Action
  async listCoursePoolInfoByIds(ids: Array<string>) {
    const { status, data } = await platformCourseGateway.listCoursePoolInfoByIds(ids)
    if (status.isSuccess()) {
      const list = new Array<CoursePool>()
      Object.assign(list, data)
      this.SET_COURSE_POOL_INFO_LIST(list)
    }
    return status
  }
}
export default getModule(CoursePoolCustomerModule)
