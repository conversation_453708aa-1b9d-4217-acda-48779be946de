<template>
  <el-main v-if="$hasPermission('queryData')" desc="查询数据" actions="created">
    <!-- 顶部的tab标签 -->
    <el-tabs v-model="tabName" class="m-tab-top is-sticky" @tab-click="resetQuery">
      <!-- 登录日志 -->
      <el-tab-pane label="登录日志" name="loginIn"> </el-tab-pane>
      <!-- 登出日志 -->
      <el-tab-pane label="登出日志" name="loginOut"> </el-tab-pane>
      <div class="f-p15">
        <el-card shadow="never" class="m-card f-mb15">
          <el-row :gutter="24" class="m-query is-border-bottom">
            <el-form :inline="true" label-width="auto">
              <el-col :span="6">
                <el-form-item :label="loginNoteList.params.type === 'loginIn' ? '登录地址' : '登出地址'">
                  <el-input
                    v-model="loginNoteList.params.ip"
                    clearable
                    :placeholder="`请输入${loginNoteList.params.type === 'loginIn' ? '登录地址' : '登出地址'}`"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="用户名称">
                  <el-input v-model="loginNoteList.params.userName" clearable placeholder="请输入用户名称" />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item :label="loginNoteList.params.type === 'loginIn' ? '登录时间' : '登出时间'">
                  <single-date-picker
                    :dateRange.sync="loginNoteList.params.dateRange"
                    begin-time-placeholder="开始日期"
                    end-time-placeholder="结束日期"
                    @emitChange="emitChange"
                  ></single-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6" style="padding-top: 10px; padding-bottom: 10px">
                <span style="color: red">开始日期和结束日期区间跨度不能超过三个月</span>
              </el-col>

              <el-col :span="6" class="f-fr" style="text-align: right">
                <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
                <el-button @click="resetQuery">重置</el-button>
              </el-col>
            </el-form>
          </el-row>
          <!--表格-->
          <el-table
            stripe
            :data="loginNoteList.list"
            max-height="500px"
            class="m-table f-mt10"
            ref="tabRef"
            v-loading="loading"
          >
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="用户名称" min-width="300">
              <template #default="scope">{{ scope.row.userName || '-' }}</template>
            </el-table-column>
            <el-table-column
              :label="`${loginNoteList.params.type === 'loginIn' ? '登录' : '登出'}IP地址`"
              min-width="300"
            >
              <template #default="scope">{{ scope.row.ip || '-' }}</template>
            </el-table-column>
            <el-table-column :label="loginTimeName" min-width="180">
              <template #default="scope">{{
                loginNoteList.params.type === 'loginIn' ? scope.row.loginSuccessTime : scope.row.logoutTime || '-'
              }}</template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"> </hb-pagination>
        </el-card>
      </div>
    </el-tabs>
  </el-main>
</template>

<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import { ElTable } from 'element-ui/types/table'
  import { UiPage } from '@hbfe/common'
  import LoginNoteList from '@api/service/management/login-note/LoginNoteList'
  import LoginNoteParams from '@api/service/management/login-note/models/LoginNoteParams'
  import SingleDatePicker from '@hbfe/jxjy-admin-components/src/single-date-picker/index.vue'

  @Component({ components: { SingleDatePicker } })
  export default class extends Vue {
    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }
    @Ref('datePickerRef') datePickerRef: SingleDatePicker
    /**
     * 表格Ref
     */
    @Ref('tabRef') tabRef: ElTable
    /**
     * 模型初始化
     */
    loginNoteList = new LoginNoteList()
    /**
     * 分页
     */
    page = new UiPage()
    /**
     * 表格loading
     */
    loading = false
    /**
     * tab名称
     */
    tabName = 'loginIn'

    isShow = false
    /**
     * 登录需要呈现的时间
     */
    get loginTimeName() {
      return this.loginNoteList.params.type === 'loginIn' ? '登录成功时间' : '登出时间'
    }

    created() {
      this.doQuery()
    }
    emitChange(val: boolean) {
      this.isShow = val
      if (this.isShow) {
        this.loginNoteList.params.dateRange = [] as string[]
      }
    }
    /**
     * 查询数据
     */
    async doQuery() {
      this.loading = true
      try {
        this.tabName == 'loginIn'
          ? (this.loginNoteList.params.type = 'loginIn')
          : (this.loginNoteList.params.type = 'loginOut')
        await this.loginNoteList.doQuery(this.page)
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
        this.tabRef.doLayout()
      }
    }
    /**
     * 重置查询
     */
    resetQuery() {
      this.page.pageNo = 1
      this.isShow = false
      this.loginNoteList.params = new LoginNoteParams()
      this.doQuery()
    }
  }
</script>
<style lang="scss" scoped>
  .custom-form-item {
    display: flex;
    align-items: center;
  }

  .date-range-tip {
    padding-left: 10px;
    color: red;
    white-space: nowrap;
  }
</style>
