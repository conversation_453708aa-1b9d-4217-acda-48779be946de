import { ResponseStatus } from '@hbfe/common'
import CreateOrderParams from '@api/service/diff/customer/qztg/trade/single/mutation/vo/create-order/CreateOrderParams'
import PlatformQztgSchool from '@api/diff-gateway/platform-qztg-school'
import MsTradeQueryFrontGatewayCourseLearningForestage, {
  OrderRequest,
  OrderResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { CreateOrderResponseCodeEnum } from '@api/service/diff/customer/qztg/trade/single/mutation/enums/CreateOrderResponseCodeEnum'

export default class CreateOrder {
  //  创建订单入参
  createOrderParams: CreateOrderParams = new CreateOrderParams()

  /**
   * 创建合并订单
   */
  async doCreateMergeOrder() {
    const request = this.createOrderParams.toMergeCreateOrderRequest()
    const res = await PlatformQztgSchool.createOrder(request)

    if (res?.status?.isSuccess()) {
      const data = res?.data

      const repeatSubOrderNo = new Array<string>()
      if (data?.resultList?.length) {
        for (let i = 0; i < data.resultList.length; i++) {
          const item = data.resultList[i]
          const code = item.code as CreateOrderResponseCodeEnum
          if (code == CreateOrderResponseCodeEnum.duplicateRegistration) {
            if (item.sourceId) {
              repeatSubOrderNo.push(item.sourceId)
            }
          }
        }
      }
      if (repeatSubOrderNo.length) {
        const request = new OrderRequest()
        request.subOrderNoList = repeatSubOrderNo
        const orderListRes = await MsTradeQueryFrontGatewayCourseLearningForestage.pageOrderInMyself({
          page: {
            pageNo: 1,
            pageSize: repeatSubOrderNo.length
          },
          request: request
        })

        const orderList = orderListRes?.data?.currentPageData || new Array<OrderResponse>()

        data.resultList.map((item) => {
          const order = orderList.find((orderItem) => {
            if (orderItem.subOrderItems.length) {
              const findSubOrder = orderItem.subOrderItems.find((subOrder) => subOrder.subOrderNo === item.sourceId)
              if (findSubOrder) {
                return true
              } else {
                return false
              }
            } else {
              return false
            }
          })
          if (order) {
            if ([0, 1].includes(order.basicData?.orderPaymentStatus)) {
              if (order.basicData?.channelType == 2) {
                // 集体报名重复
                item.code = CreateOrderResponseCodeEnum.unpaidGroupOrderExists
              } else {
                // 个人报名重复
                item.code = CreateOrderResponseCodeEnum.unpaidOrderExists
              }
            }
          }
        })
      }
      return res
    } else {
      return Promise.reject(new ResponseStatus(500, '系统异常'))
    }
  }
}
