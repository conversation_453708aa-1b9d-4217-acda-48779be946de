import CourseResourceGateway, { CourseCategoryCreateRequest } from '@api/ms-gateway/ms-course-resource-v1'
import CreateCourseCategory from '@api/service/management/resource/course-category/mutation/vo/CreateCourseCategory'

class CreateCourseCategoryDto extends CourseCategoryCreateRequest {
  async save() {
    return await CourseResourceGateway.createCourseCategory(this)
  }

  static from(vo: CreateCourseCategory): CreateCourseCategoryDto {
    const detail = new CreateCourseCategoryDto()
    detail.parentId = vo.parentId
    detail.name = vo.name
    detail.sort = vo.sort
    return detail
  }
}

export default CreateCourseCategoryDto
