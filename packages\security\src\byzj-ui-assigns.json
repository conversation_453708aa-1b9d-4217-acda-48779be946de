[{"name": "WXGLY", "meta": {"title": "网校管理员", "isMenu": true, "ownerGroup": ["WXGLY"], "roles": ["FXS", "GYS", "NZGYS", "NZFXS", "NZFXSJCB", "NZGYSJCB", "ZTGLY", "JTJFGLY", "WXGLY", "DQGLY"], "permissionMap": {"query": {"name": "查询(必选)", "ownerGroup": ["WXGLY.query"], "graphql": ["ms-config-v1.query.getCurrentFrontendConfig:{\"serviceName\":\"ms-config-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForServiceByDomainName:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.query.getDistributionService:{\"authorizationRequired\":false}", "ms-identity-authentication-v1.query.getAccessTokenValue:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getAdminInfoInMyself:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjy-distributor-admin-v1.query.isOnlineSchoolContractExpired:{\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.isOnlineSchoolContractExpired:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.query.findFunctionalAuthorityByRoleIdsNew:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.query.findRoleByAccountId:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfigByServicerId:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applyCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptchaForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCodeForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwd:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptcha:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountId:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCode:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-account-v1.mutation.changeAuthorizationUnitInfoList:{\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validSmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentUser:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.validCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOnlineSchoolConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getSmsCodeAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getFailedAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.forgetPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCoursePreview:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "platform-training-channel-v1.mutation.updateSaleSetting:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursePackageInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseInSchemeInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageCoursewareSupplierInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyChangeIdentityAuthentication:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticate:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyProxyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticateBasic:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryPayFlowStatus:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-payment-v1.mutation.getBatchPreParePayResult:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryQrScanPromptByPayFlowNo:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.deliveredOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":true}", "ms-assess-v1.mutation.batchRePushSchemeIndicator:{\"serviceName\":\"ms-assess-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.createJxjyCommonMenu:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.temporaryOrderUpdateOrderInfoInSubject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.enableOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForService:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.tryComparePhoto:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.updateUserDatumCodePhoto:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.refreshConfigAndUpdate:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.executeAntiInspection:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":false}", "platform-account-v1.mutation.compensatingStudentAccount:{\"serviceName\":\"platform-account-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.mutation.openDistributionService:{\"authorizationRequired\":false}", "ms-order-repair-data-v1.mutation.orderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-repair-data-v1.mutation.batchOrderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.distributionAuthBatchProduct:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.createPricePlan:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.pricePlanSetSaleChannel:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissReturnOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissExchangeOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.clearStudentLearningRule:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-teachingplan-studentattendance-sds-v1.mutation.oneClickClock:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":false}"], "roles": ["FXS", "GYS", "NZGYS", "NZFXS", "NZFXSJCB", "NZGYSJCB", "ZTGLY", "JTJFGLY", "WXGLY", "DQGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}}, "sort": "2", "diffSchool": ["<PERSON><PERSON><PERSON>"]}, "specifier": "WXGLY", "path": "", "pathSegments": ["WXGLY"], "children": [{"name": "statistic", "specifier": "Statistic", "path": "/statistic", "pathSegments": ["WXGLY", "statistic"], "component": "@/packages/routers/src/basic-router/statistic.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "统计报表", "sort": 5, "icon": "icon-shu<PERSON>", "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.statistic"], "diffSchool": ["<PERSON><PERSON><PERSON>"]}, "children": [{"name": "statistic-learning-statistic-index", "specifier": "StatisticLearningStatisticIndex", "path": "learning-statistic", "pathSegments": ["WXGLY", "statistic", "learning-statistic"], "component": "@/packages/routers/src/basic-router/statistic/learning-statistic/index.vue", "meta": {"isMenu": true, "title": "学员学习明细", "sort": 5, "icon": "icon-mingxi", "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {"query": {"roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "name": "查询", "ownerGroup": ["WXGLY.statistic.learning-statistic.query"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegionInDistribution:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicerManageRegion:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInServicerManageRegion:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInTrainingChannelV2:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInTrainingChannel:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicerV2:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiBasicConfigInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiModuleConfigurationInfoInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}", "fxnl-query-common-disposition-information.query.getIndustriesByServiceIdInDistributor:{\"authorizationRequired\":true}", "fxnl-query-common-disposition-information.query.getYearsByServiceIdInDistributor:{\"authorizationRequired\":true}"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "exceptionManagement": {"name": "学习数据异常管理", "ownerGroup": ["WXGLY.statistic.learning-statistic.exceptionManagement"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "learnAnomalousList": {"name": "学习数据异常管理-学习规则", "ownerGroup": ["WXGLY.statistic.learning-statistic.learnAnomalousList"], "graphql": ["student-course-learning-query-back-gateway.query.pageLearningResultErrorInTrainingChannel:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.pageLearningResultErrorInServicer:{\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.reGenerateStudentTrainingResultSimulateInServicer:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "intelligentLearningList": {"name": "学习数据异常管理-智能学习", "ownerGroup": ["WXGLY.statistic.learning-statistic.intelligentLearningList"], "graphql": ["ms-autolearning-student-auto-learning-task-result-v1.query.queryByPage:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-autolearning-student-auto-learning-task-result-v1.query.findLastFailSubTaskByMainTaskIdList:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "export": {"name": "导出", "ownerGroup": ["WXGLY.statistic.learning-statistic.export"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInDistributor:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInDistributor:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInDistributor:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInTrainingChannel:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInTrainingChannel:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInTrainingChannel:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInServicerManageRegion:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInServicerManageRegion:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInServicerManageRegion:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "exportStatementsData": {"name": "导出报盘数据", "ownerGroup": ["WXGLY.statistic.learning-statistic.exportStatementsData"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-byzj-school.query.exportStudentCourseLearningQuotationInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "onlineClassTable": {"name": "网授班列表", "ownerGroup": ["WXGLY.statistic.learning-statistic.onlineClassTable"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pagePlanItemAttendanceInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "allSync": {"name": "批量同步", "ownerGroup": ["WXGLY.statistic.learning-statistic.allSync"], "graphql": ["platform-jxjypxtypt-student-learning-backstage.query.rePushStudentTrainingResultToGatewayInServicerV2:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "examDetail": {"name": "考试详情", "ownerGroup": ["WXGLY.statistic.learning-statistic.examDetail"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "testDetail": {"name": "测验详情", "ownerGroup": ["WXGLY.statistic.learning-statistic.testDetail"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "toLog": {"name": "查阅监管日志", "ownerGroup": ["WXGLY.statistic.learning-statistic.toLog"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "userInfo": {"name": "用户信息组件", "ownerGroup": ["WXGLY.statistic.learning-statistic.userInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "classInfo": {"name": "培训班信息组件", "ownerGroup": ["WXGLY.statistic.learning-statistic.classInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigByRequestInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "studyLog": {"name": "学习日志组件", "ownerGroup": ["WXGLY.statistic.learning-statistic.studyLog"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.pageSupervisionCourseDetailsInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.listLearningSupervisionBehaviorInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "toLearningLog": {"name": "查阅学习日志", "ownerGroup": ["WXGLY.statistic.learning-statistic.toLearningLog"], "graphql": [], "roles": ["WXGLY", "DQGLY", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "queryInfo": {"name": "查询学习日志", "ownerGroup": ["WXGLY.statistic.learning-statistic.queryInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningLogsInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getLearningType:{\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "faceClassTable": {"name": "面授班列表", "ownerGroup": ["WXGLY.statistic.learning-statistic.faceClassTable"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pagePlanItemAttendanceInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "mixedClassTable": {"name": "混合班列表", "ownerGroup": ["WXGLY.statistic.learning-statistic.mixedClassTable"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pagePlanItemAttendanceInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "attendanceDetail": {"name": "考勤详情", "ownerGroup": ["WXGLY.statistic.learning-statistic.attendanceDetail"], "graphql": [], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}}, "ownerGroup": ["WXGLY.statistic.learning-statistic"], "diffSchool": ["<PERSON><PERSON><PERSON>"]}}]}, {"name": "training", "specifier": "Training", "path": "/training", "pathSegments": ["WXGLY", "training"], "component": "@/packages/routers/src/basic-router/training.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "培训管理", "sort": 4, "icon": "icon-peixun", "roles": ["WXGLY", "FXS", "GYS", "ZTGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.training"], "diffSchool": ["<PERSON><PERSON><PERSON>"]}, "children": [{"name": "training-customer-service", "specifier": "TrainingCustomerService", "path": "customer-service", "pathSegments": ["WXGLY", "training", "customer-service"], "component": "@/packages/routers/src/basic-router/training/customer-service.vue", "meta": {"isMenu": true, "title": "客服管理", "sort": 6, "icon": "icon-kefu", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {}, "ownerGroup": ["WXGLY.training.customer-service"], "diffSchool": ["<PERSON><PERSON><PERSON>"]}, "children": [{"name": "training-customer-service-personal-index", "specifier": "TrainingCustomerServicePersonalIndex", "path": "personal", "pathSegments": ["WXGLY", "training", "customer-service", "personal"], "component": "@/packages/routers/src/basic-router/training/customer-service/personal/index.vue", "meta": {"isMenu": true, "title": "业务咨询", "sort": 1, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {"query": {"name": "查询", "ownerGroup": ["WXGLY.training.customer-service.personal.query"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiBasicConfigInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiModuleConfigurationInfoInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryNewestTaskResultByQualificationIds:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInformationInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "basicData": {"name": "学员信息", "ownerGroup": ["WXGLY.training.customer-service.personal.basicData"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getDockingTycAndQcc:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "editCompany": {"name": "编辑基础信息", "ownerGroup": ["WXGLY.training.customer-service.personal.editCompany"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "confirmResetPwd": {"name": "重置密码", "ownerGroup": ["WXGLY.training.customer-service.personal.confirmResetPwd"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.ResetPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "editProfessionalCategoryName": {"name": "编辑人社专业类别", "ownerGroup": ["WXGLY.training.customer-service.personal.editProfessionalCategoryName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "editProfessionalQualification": {"name": "编辑人社职称等级", "ownerGroup": ["WXGLY.training.customer-service.personal.editProfessionalQualification"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "deleteAttachmentInfoItem": {"name": "删除行业证书", "ownerGroup": ["WXGLY.training.customer-service.personal.deleteAttachmentInfoItem"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.deleteCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "editPersonnelCategoryName": {"name": "编辑卫生人员类别", "ownerGroup": ["WXGLY.training.customer-service.personal.editPersonnelCategoryName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "editPositionCategoryName": {"name": "编辑卫生岗位类别", "ownerGroup": ["WXGLY.training.customer-service.personal.editPositionCategoryName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "editProfessionalLevelName": {"name": "编辑工勤技术等级", "ownerGroup": ["WXGLY.training.customer-service.personal.editProfessionalLevelName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "editJobCategoryIdName": {"name": "编辑工勤工种", "ownerGroup": ["WXGLY.training.customer-service.personal.editJobCategoryIdName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "editStudyperiodSubjectName": {"name": "编辑学段学科", "ownerGroup": ["WXGLY.training.customer-service.personal.editStudyperiodSubjectName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "updateAndEditCertifition": {"name": "新增/编辑证书抽屉", "ownerGroup": ["WXGLY.training.customer-service.personal.updateAndEditCertifition"], "graphql": ["ms-basicdata-domain-gateway-v1.mutation.addStudentCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "unbundle": {"name": "微信解绑", "ownerGroup": ["WXGLY.training.customer-service.personal.unbundle"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.unbindWeChatOpenPlatform:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "revisePhotoTime": {"name": "调整基准照次数", "ownerGroup": ["WXGLY.training.customer-service.personal.revisePhotoTime"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.changeAllowUpdateCount:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "deletePhoto": {"name": "删除基准照", "ownerGroup": ["WXGLY.training.customer-service.personal.deletePhoto"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.doDeleteUserDatum:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "studyDontent": {"name": "学习内容", "ownerGroup": ["WXGLY.training.customer-service.personal.studyDontent"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryNewestTaskResultByQualificationIds:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInformationInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningExperienceParticipatedInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listLearningExperienceTopic:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Forestage.query.getAntiBasicConfigDetailInScheme:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "studyQuery": {"name": "查询", "ownerGroup": ["WXGLY.training.customer-service.personal.studyQuery"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryNewestTaskResultByQualificationIds:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInformationInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "showIntelligentLearningTag": {"name": "是否展示智能学习标识", "ownerGroup": ["WXGLY.training.customer-service.personal.showIntelligentLearningTag"], "graphql": [], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "qualified": {"name": "一键合格", "ownerGroup": ["WXGLY.training.customer-service.personal.qualified"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-learningscheme-v1.mutation.oneKeyPass:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "learningSituation": {"name": "学习情况", "ownerGroup": ["WXGLY.training.customer-service.personal.learningSituation"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageStudentCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareLearningRecordInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "studty": {"name": "一键学习", "ownerGroup": ["WXGLY.training.customer-service.personal.studty"], "graphql": ["ms-learningscheme-v1.mutation.applyAutoLearningTokenForManage:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.applyCourseImmediatelyLearning:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}", "ms-autonomouscourselearningscene-v1.mutation.applyCourseImmediatelyLearning:{\"serviceName\":\"ms-autonomouscourselearningscene-v1\",\"authorizationRequired\":true}", "ms-studentcourselearning-v1.mutation.commitImmediatelyCourseLearning:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.applyCoursewareImmediatelyLearning:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}", "ms-autonomouscourselearningscene-v1.mutation.applyCoursewareImmediatelyLearning:{\"serviceName\":\"ms-autonomouscourselearningscene-v1\",\"authorizationRequired\":true}", "ms-studentcourselearning-v1.mutation.commitImmediatelyCoursewareLearning:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "deleteCourse": {"name": "删除课程", "ownerGroup": ["WXGLY.training.customer-service.personal.deleteCourse"], "graphql": ["ms-learningscheme-v1.mutation.applyRelearnTokenForManage:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-autonomouscourselearningscene-v1.mutation.invalidStudentCourse:{\"serviceName\":\"ms-autonomouscourselearningscene-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.invalidStudentCourse:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "toLog": {"name": "监管日志", "ownerGroup": ["WXGLY.training.customer-service.personal.toLog"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "userInfo": {"name": "用户信息组件", "ownerGroup": ["WXGLY.training.customer-service.personal.userInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "classInfo": {"name": "培训班信息组件", "ownerGroup": ["WXGLY.training.customer-service.personal.classInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigByRequestInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "studyLog": {"name": "学习日志组件", "ownerGroup": ["WXGLY.training.customer-service.personal.studyLog"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.pageSupervisionCourseDetailsInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.listLearningSupervisionBehaviorInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "attendanceDetailsDrawer": {"name": "考勤详情", "ownerGroup": ["WXGLY.training.customer-service.personal.attendanceDetailsDrawer"], "graphql": ["ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pagePlanItemAttendanceInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "deleteExamRecord": {"name": "删除考试记录", "ownerGroup": ["WXGLY.training.customer-service.personal.deleteExamRecord"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-examination-v1.mutation.invalidStudentExamAnswerPaper:{\"serviceName\":\"ms-examination-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "deleteExperience": {"name": "删除学习心得", "ownerGroup": ["WXGLY.training.customer-service.personal.deleteExperience"], "graphql": ["ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningExperienceParticipatedInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listLearningExperienceTopic:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.mutation.removeStudentLearningExperience:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "surveyQuestionnaire": {"name": "调研问卷", "ownerGroup": ["WXGLY.training.customer-service.personal.surveyQuestionnaire"], "graphql": ["ms-exam-query-front-gateway-QuestionnaireQueryBackStage.query.pageQuestionnaireSchemeInServicer:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "queryExchangeClass": {"name": "查看换班信息", "ownerGroup": ["WXGLY.training.customer-service.personal.queryExchangeClass"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.pageProductPricingSchemeInSupplier:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "exchangeClass": {"name": "换班", "ownerGroup": ["WXGLY.training.customer-service.personal.exchangeClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-order-v1.mutation.sellerApplyExchange:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "continueExchangeClass": {"name": "继续换班", "ownerGroup": ["WXGLY.training.customer-service.personal.continueExchangeClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryRecycleResouce:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryDelivery:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "queryMixExchangeClass": {"name": "查看面网授换班信息", "ownerGroup": ["WXGLY.training.customer-service.personal.queryMixExchangeClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "exchangeMixClass": {"name": "面网授班换班", "ownerGroup": ["WXGLY.training.customer-service.personal.exchangeMixClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "exchangeMixPeriod": {"name": "面网授班换期", "ownerGroup": ["WXGLY.training.customer-service.personal.exchangeMixPeriod"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-information-gateway.query.getCurrentTime:{\"authorizationRequired\":false}", "ms-learningscheme-enrollment-v1.mutation.changeIssue:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "viewExchangeMixDetail": {"name": "面网授班换班换期记录详情查看", "ownerGroup": ["WXGLY.training.customer-service.personal.viewExchangeMixDetail"], "graphql": [], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "continueExchangeMixTrainClass": {"name": "面网授班继续换班", "ownerGroup": ["WXGLY.training.customer-service.personal.continueExchangeMixTrainClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryRecycleResouce:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryDelivery:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "queryFaceExchangeClass": {"name": "查看面授换班信息", "ownerGroup": ["WXGLY.training.customer-service.personal.queryFaceExchangeClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "exchangeFaceClass": {"name": "面授班换班", "ownerGroup": ["WXGLY.training.customer-service.personal.exchangeFaceClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "exchangeFacePeriod": {"name": "面授班换期", "ownerGroup": ["WXGLY.training.customer-service.personal.exchangeFacePeriod"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-information-gateway.query.getCurrentTime:{\"authorizationRequired\":false}", "ms-learningscheme-enrollment-v1.mutation.changeIssue:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "viewExchangeFacexDetail": {"name": "面授班换班换期记录详情查看", "ownerGroup": ["WXGLY.training.customer-service.personal.viewExchangeFacexDetail"], "graphql": [], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "continueExchangeFaceTrainClass": {"name": "面授班继续换班", "ownerGroup": ["WXGLY.training.customer-service.personal.continueExchangeFaceTrainClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryRecycleResouce:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryDelivery:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "orderInfo": {"name": "订单详情", "ownerGroup": ["WXGLY.training.customer-service.personal.orderInfo"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "detail": {"name": "详情", "ownerGroup": ["WXGLY.training.customer-service.personal.detail"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listReturnReasonInfoInSubProject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-order-v1.mutation.sellerApplyAfterSale:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "queryRelevancyOrder": {"name": "详情-查看关联换班订单", "ownerGroup": ["WXGLY.training.customer-service.personal.queryRelevancyOrder"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInInDistributor:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueLogInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueLogInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "queryChangeClassDetail": {"name": "详情-查看换班/换期详情", "ownerGroup": ["WXGLY.training.customer-service.personal.queryChangeClassDetail"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getExchangeOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "refundOrder": {"name": "详情-退款操作", "ownerGroup": ["WXGLY.training.customer-service.personal.refundOrder"], "graphql": ["ms-order-v1.mutation.sellerApplyAfterSale:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listReturnReasonInfoInSubProject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "fillToInvoice": {"name": "补要发票", "ownerGroup": ["WXGLY.training.customer-service.personal.fillToInvoice"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryShippingMethodsForSchool:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.applyInvoice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "invoiceInfo": {"name": "发票信息", "ownerGroup": ["WXGLY.training.customer-service.personal.invoiceInfo"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.mutation.updateElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflinePaperInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceDeliveryInfoWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.deliveryInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.pickupInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "invoiceAutomatic": {"name": "电子发票", "ownerGroup": ["WXGLY.training.customer-service.personal.invoiceAutomatic"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.mutation.updateElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "invoiceOffline": {"name": "电子发票（线下开票）", "ownerGroup": ["WXGLY.training.customer-service.personal.invoiceOffline"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "electronicSpecialInvoice": {"name": "增值税电子专用发票（线下开票）", "ownerGroup": ["WXGLY.training.customer-service.personal.electronicSpecialInvoice"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineSpecialPaperElectronicInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "invoiceIncrement": {"name": "增值税专票", "ownerGroup": ["WXGLY.training.customer-service.personal.invoiceIncrement"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflinePaperInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "invoiceDistribution": {"name": "发票配送", "ownerGroup": ["WXGLY.training.customer-service.personal.invoiceDistribution"], "graphql": ["ms-offlineinvoice-v1.mutation.importOfflineInvoiceDeliveryInfoWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-offlineinvoice-v1.mutation.deliveryInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.pickupInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "refundInfo": {"name": "退款信息", "ownerGroup": ["WXGLY.training.customer-service.personal.refundInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "refundDetail": {"name": "退款详情详情", "ownerGroup": ["WXGLY.training.customer-service.personal.refundDetail"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "approve": {"name": "退款审批", "ownerGroup": ["WXGLY.training.customer-service.personal.approve"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.sellerCancelReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.rejectReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.agreeReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "retryRecycleRefund": {"name": "重新回收资源", "ownerGroup": ["WXGLY.training.customer-service.personal.retryRecycleRefund"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.retryRecycleResource:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "continueRefund": {"name": "继续退款", "ownerGroup": ["WXGLY.training.customer-service.personal.continueRefund"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.retryRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "confirmRefund": {"name": "确认退款", "ownerGroup": ["WXGLY.training.customer-service.personal.confirmRefund"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.confirmRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "studyRecords": {"name": "学习档案", "ownerGroup": ["WXGLY.training.customer-service.personal.studyRecords"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryNewestTaskResultByQualificationIds:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInformationInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "lookCourseCertification": {"name": "查阅学时证明，重新推送", "ownerGroup": ["WXGLY.training.customer-service.personal.lookCourseCertification"], "graphql": ["platform-certificate-v1.mutation.printCertificate:{\"authorizationRequired\":true}", "platform-jxjypxtypt-student-learning-backstage.query.rePushStudentTrainingResultToGatewayInServicerV2:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}}, "ownerGroup": ["WXGLY.training.customer-service.personal"], "diffSchool": ["<PERSON><PERSON><PERSON>"]}}]}, {"name": "training-scheme", "specifier": "TrainingScheme", "path": "scheme", "pathSegments": ["WXGLY", "training", "scheme"], "component": "@/packages/routers/src/basic-router/training/scheme.vue", "meta": {"isMenu": true, "title": "培训方案管理", "sort": 2, "icon": "icon-fangan", "roles": ["WXGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.training.scheme"], "diffSchool": ["<PERSON><PERSON><PERSON>"]}, "children": [{"name": "training-scheme-course-maintenance", "specifier": "TrainingSchemeCourseMaintenance", "path": "course-maintenance", "pathSegments": ["WXGLY", "training", "scheme", "course-maintenance"], "component": "@/packages/routers/src/basic-router/training/scheme/course-maintenance.vue", "meta": {"isMenu": true, "title": "公需课课程维护", "sort": 5, "roles": ["WXGLY"], "permissionMap": {"query": {"roles": ["WXGLY"], "name": "查询", "ownerGroup": ["WXGLY.training.scheme.course-maintenance.query"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-byzj-school.query.pageCourseSubjectInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "import": {"name": "批量导入公需课程", "ownerGroup": ["WXGLY.training.scheme.course-maintenance.import"], "graphql": ["platform-jxjypxtypt-byzj-school.query.importCourseSubjectByExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "remove": {"name": "删除", "ownerGroup": ["WXGLY.training.scheme.course-maintenance.remove"], "graphql": ["platform-jxjypxtypt-byzj-school.query.pageCourseSubjectInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-byzj-school.query.deleteCourseSubjectByIdInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}}, "ownerGroup": ["WXGLY.training.scheme.course-maintenance"], "diffSchool": ["<PERSON><PERSON><PERSON>"]}}]}, {"name": "training-task", "specifier": "TrainingTask", "path": "task", "pathSegments": ["WXGLY", "training", "task"], "component": "@/packages/routers/src/basic-router/training/task.vue", "meta": {"isMenu": true, "title": "导入导出任务管理", "sort": 11, "icon": "icon-ding<PERSON>", "roles": ["WXGLY", "ZTGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.training.task"], "diffSchool": ["<PERSON><PERSON><PERSON>"]}, "children": [{"name": "training-task-importtask-index", "specifier": "TrainingTaskImporttaskIndex", "path": "importtask", "pathSegments": ["WXGLY", "training", "task", "importtask"], "component": "@/packages/routers/src/basic-router/training/task/importtask/index.vue", "meta": {"isMenu": true, "title": "导入任务管理", "sort": 1, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "ZTGLY"], "permissionMap": {"query": {"name": "查询", "ownerGroup": ["WXGLY.training.task.importtask.query"], "graphql": ["ms-importopen-v1.query.findTaskExecuteWithSelfResponseByPage:{\"serviceName\":\"ms-importopen-v1\",\"authorizationRequired\":true}", "ms-importopen-v1.query.findTaskExecuteWithServicerResponseByPage:{\"serviceName\":\"ms-importopen-v1\",\"authorizationRequired\":true}", "ms-account-v1.query.pageStudentImportTask:{\"serviceName\":\"ms-account-v1\",\"authorizationRequired\":true}", "ms-course-resource-v1.query.pageCoursePackageImportTask:{\"serviceName\":\"ms-course-resource-v1\",\"authorizationRequired\":true}", "ms-examquestion-v1.query.findBatchImportQuestionByPage:{\"serviceName\":\"ms-examquestion-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportSpecialPaperOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportSpecialInvoiceDeliveryWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportOfflineSpecialElectronicInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportBatchPayOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.commonQueryOfflineInvoiceImportResult:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportBatchPaySpecialPaperOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryForImportBatchPaySpecialInvoiceDelivery:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-course-resource-v1.query.pageCoursewareImportTask:{\"serviceName\":\"ms-course-resource-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.query.findImportPrintTaskExecuteResponsePage:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.query.pageImportLearningSchemeImportTask:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "platform-jxjy-learning-result-v1.query.pageImportGradeResultTaskInfo:{\"authorizationRequired\":true}", "platform-training-channel-v1.query.pageUpdateSchemePortalResultTaskInfo:{\"authorizationRequired\":true}", "platform-jxjypxtypt-byzj-school.query.findBatchImportByPageInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfigByServicerId:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "download": {"name": "下载全部数据", "ownerGroup": ["WXGLY.training.task.importtask.download"], "graphql": ["ms-importopen-v1.query.exportExcelAllData:{\"serviceName\":\"ms-importopen-v1\",\"authorizationRequired\":true}", "platform-account-v1.query.exportAllStudentResult:{\"authorizationRequired\":true}", "ms-course-resource-v1.query.exportAllCoursePackageImportResult:{\"serviceName\":\"ms-course-resource-v1\",\"authorizationRequired\":true}", "ms-examquestion-v1.mutation.batchExportAllChooseQuestionData:{\"serviceName\":\"ms-examquestion-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.getAllImportData:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-course-resource-v1.mutation.exportCoursewareImportResult:{\"serviceName\":\"ms-course-resource-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.findImportPrintData:{\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.batchUpdateLearningSchemeExport:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "platform-jxjy-learning-result-v1.mutation.exportTaskExcelAllResultWithName:{\"authorizationRequired\":true}", "platform-training-channel-v1.query.exportAllUpdateSchemeShowResult:{\"authorizationRequired\":true}", "platform-jxjypxtypt-byzj-school.query.batchExportAllChooseDataInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-importopen-v1.query.exportExcel:{\"serviceName\":\"ms-importopen-v1\",\"authorizationRequired\":true}", "platform-account-v1.query.exportErrorStudentResult:{\"authorizationRequired\":true}", "ms-course-resource-v1.query.exportErrorCoursePackageImportResult:{\"serviceName\":\"ms-course-resource-v1\",\"authorizationRequired\":true}", "ms-examquestion-v1.mutation.batchExportFailChooseQuestionData:{\"serviceName\":\"ms-examquestion-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.getImportFailedData:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.findImportPrintFailData:{\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.batchUpdateLearningSchemeExportFail:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "platform-jxjy-learning-result-v1.mutation.exportTaskExcelFailedResultWithName:{\"authorizationRequired\":true}", "platform-training-channel-v1.query.exportFailUpdateSchemeShowResult:{\"authorizationRequired\":true}", "platform-jxjypxtypt-byzj-school.query.batchExportFailChooseDataInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}, "viewLog": {"name": "查看日志", "ownerGroup": ["WXGLY.training.task.importtask.viewLog"], "graphql": [], "roles": ["WXGLY", "ZTGLY"], "ext": {"diffSchool": ["<PERSON><PERSON><PERSON>"]}}}, "ownerGroup": ["WXGLY.training.task.importtask"], "diffSchool": ["<PERSON><PERSON><PERSON>"]}}]}]}]}]