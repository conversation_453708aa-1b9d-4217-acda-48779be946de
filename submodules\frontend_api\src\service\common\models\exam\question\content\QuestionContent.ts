import {
  BlankFillingAnswerType,
  QuestionMode,
  QuestionType
} from '@api/service/customer/my-question/question-practice/models/enums'

class QuestionContent {}

export class Judgement extends QuestionContent {
  /**
   * 正确答案
   */
  correctAnswer = false
}

export class SingleChoice extends QuestionContent {
  /**
   * 选项
   */
  choiceItems: Array<ChoiceItemDTO> = new Array<ChoiceItemDTO>()
  /**
   * 标准答案
   */
  correctAnswer = ''
}

export class MultipleChoice extends QuestionContent {
  /**
   * 选项
   */
  choiceItems: Array<ChoiceItemDTO> = new Array<ChoiceItemDTO>()
  /**
   * 标准答案
   */
  correctAnswers: Array<string> = new Array<string>()
}

export class BlankFilling extends QuestionContent {
  /**
   * 答案数量
   **/
  answerCount = 0
  /**
   * 当填空题类型为 精确匹配时，最外层集合为答案组（也就是每一组都是一道填空题的答案，满足当中的任意一组表示回答正确）第二层集合为空
   * 当填空题类似为 每空多答案时，最外层的集合为每个空的答案，第二层集合为每个空的备选答案
   **/
  answersGroup: Array<Array<string>> = new Array<Array<string>>()
  /**
   * 答案项分值
   * 当填空题类型为 精确匹配时此项值无效
   */
  answersItemScore: Array<number> = new Array<number>()
  /**
   * 答案类型
   * @see BlankFillingAnswerType
   */
  answerType: BlankFillingAnswerType = BlankFillingAnswerType.MULTIPLE_SETS
  /**
   * 答案是否有顺序.当{@link #answerType } = {@link BlankFillingAnswerType#MULTIPLE_PER_BLANK} 时，
   * 即每空多答案的情况下，答案是否是按照填空顺序排列。
   */
  sequence = false
  /**
   * 评分标准
   **/
  standard = ''
}

export class Essay extends QuestionContent {
  /**
   * 参考答案
   */
  referenceAnswer = ''
  /**
   * 评分标准
   **/
  standard = ''
  /**
   * 是否限制作答长度
   */
  limitAnswerLength = false
  /**
   * 允许作答的文本字符最少长度
   */
  permitAnswerLengthMin = 0
  /**
   * 允许作答的文本字符最大长度
   */
  permitAnswerLengthMax = 0
}

export class Scale {
  /**
   * 参考答案
   */
  referenceAnswer = ''
  /**
   * 评分标准
   **/
  standard = ''
  /**
   * 是否限制作答长度
   */
  limitAnswerLength = false
  /**
   * 允许作答的文本字符最少长度
   */
  permitAnswerLengthMin = 0
  /**
   * 允许作答的文本字符最大长度
   */
  permitAnswerLengthMax = 0
}

export class Comprehensive {
  /**
   * 子题
   */
  children: Array<ComprehensiveChildQuestionDTO> = new Array<ComprehensiveChildQuestionDTO>()
}

export class ComprehensiveChildQuestionDTO {
  /**
   * 子试题id
   */
  questionId = ''
  /**
   * 题目
   */
  title = ''
  /**
   * 试题类型
   */
  questionType = QuestionType
  /**
   * 难度
   */
  mode = QuestionMode
  /**
   * 难度值
   */
  difficulty = 0.0
  /**
   * 试题解析
   */
  description = ''
  /**
   * 判断题
   */
  judgement: Judgement = new Judgement()
  /**
   * 单选题
   */
  singleChoice = SingleChoice

  /**
   * 多选
   */
  multipleChoice = MultipleChoice
  /**
   * 填空
   */
  blankFilling = BlankFilling
  /**
   * 问答题
   */
  essay = Essay
  /**
   * 量表题
   */
  scale = Scale
}

// 选项
export class ChoiceItemDTO extends QuestionContent {
  /**
   * 选项ID
   */
  id = ''
  /**
   * 选项内容
   */
  content = ''
}

export default QuestionContent
