import { OperationEnum } from '@api/service/management/train-class/mutation/Enum/OperationEnum'

/**
 * 学习方式基类
 */
class LearningTypeBase {
  // region properties
  /**
   * 配置id
   */
  configId = ''
  /**
   * 学习方式id
   */
  learningTypeId = ''
  /**
   * 学习方式考核id
   */
  assessId = ''
  /**
   * 学习方式考核名称
   */
  assessName = ''
  /**
   *是否有配置，类型为boolean
   */
  isSelected = false
  /**
   *是否有考核，类型为boolean
   */
  isAssess = false
  // endregion
  // region methods
  /**
   * 操作类型1创建2修改3删除
   */
  operation?: OperationEnum = undefined
  // endregion
}
export default LearningTypeBase
