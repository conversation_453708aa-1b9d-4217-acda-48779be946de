<template>
  <el-drawer :title="title" :visible.sync="openDialog" size="800px" custom-class="m-drawer">
    <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20" v-if="CheckInType == 'tem'">
      统一设置面授期别的考勤规则模板，设置完成后期别可以选择考勤规则模板设置考勤规则。
    </el-alert>
    <el-alert type="warning" show-icon :closable="false" class="m-alert f-mb20" v-if="CheckInType == 'edit'">
      <p>1、设置期别的考勤规则，设置后学员按规进行面授课程的考勤打卡</p>
      <p>2、当期别有学员报名后，则无法修改签到和签退的开关以及签到频率</p>
      <p>3、修改签到/签退地点和签到/签退时间后，学员按照新设置的规则进行考勤打卡</p>
    </el-alert>
    <div class="f-tr" v-if="CheckInType == 'edit'">
      <el-tooltip effect="dark" placement="top" class="f-mr10">
        <span class="el-icon-info f-co"></span>
        <div slot="content">根据模板设置考勤规则，若未配置模板，则按钮置灰</div>
      </el-tooltip>
      <!--灰显添加属性disabled-->
      <el-button type="primary" :disabled="!settingTemplate" @click="useTemplate">按考勤模板设置</el-button>
    </div>
    <div class="drawer-bd">
      <el-form label-width="150px" class="m-form">
        <el-form-item label="是否开启签到：" required>
          <el-switch
            v-model="periodConfig.signIn.isOpen"
            active-text="开启"
            inactive-text="关闭"
            :disabled="showSignUpNumWithoutTem"
            class="m-switch"
            @change="handleType('signIn')"
          />
        </el-form-item>
      </el-form>
      <el-form
        :rules="rules"
        ref="signInForm"
        :model="periodConfig"
        label-width="150px"
        class="m-form"
        :disabled="!periodConfig.signIn.isOpen"
      >
        <el-form-item label="签到频率：" required>
          <el-radio-group v-model="periodConfig.signIn.checkInFrequency" :disabled="showSignUpNumWithoutTem">
            <el-radio :label="CheckInFrequencyEnum.halfDay">每半天签到一次</el-radio>
            <el-radio :label="CheckInFrequencyEnum.perLesson">每节课签到一次</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="签到地点：" prop="signIn.checkInLocationRadius">
          在培训班办班地点定位签到，允许微调地点，微调半径：
          <el-input-number
            :controls="false"
            :min="0"
            :max="9999"
            class="input-num"
            placeholder="请输入"
            v-model="periodConfig.signIn.checkInLocationRadius"
          ></el-input-number>
          米，建议设为500米
        </el-form-item>
        <el-form-item
          label="签到时间："
          prop="signIn.preCheckInTime"
          v-if="periodConfig.signIn.checkInFrequency == CheckInFrequencyEnum.halfDay"
        >
          第一节课开始授课前
          <el-input-number
            :controls="false"
            :min="0"
            :max="60"
            class="input-num"
            placeholder="请输入"
            v-model="periodConfig.signIn.preCheckInTime"
          ></el-input-number>
          分钟和开始授课后
          <el-input-number
            :controls="false"
            :min="0"
            :max="60"
            class="input-num"
            placeholder="请输入"
            v-model="periodConfig.signIn.afterCheckInTime"
          ></el-input-number>
          分钟之间，需签到1次。
        </el-form-item>
        <el-form-item
          label="签到时间："
          prop="signIn.preCheckInTime"
          v-if="periodConfig.signIn.checkInFrequency == CheckInFrequencyEnum.perLesson"
        >
          每节课开始授课前
          <el-input-number
            :controls="false"
            :min="0"
            :max="60"
            class="input-num"
            placeholder="请输入"
            v-model="periodConfig.signIn.preCheckInTime"
          ></el-input-number>
          分钟和开始授课后
          <el-input-number
            :controls="false"
            :min="0"
            :max="60"
            class="input-num"
            placeholder="请输入"
            v-model="periodConfig.signIn.afterCheckInTime"
          ></el-input-number>
          分钟之间，需签到1次。
        </el-form-item>
        <el-divider></el-divider>
      </el-form>
      <el-form label-width="150px" class="m-form">
        <el-form-item label="是否开启签退：" required>
          <el-switch
            v-model="periodConfig.signOut.isOpen"
            active-text="开启"
            inactive-text="关闭"
            :disabled="showSignUpNumWithoutTem"
            class="m-switch"
            @change="handleType('signOut')"
          />
        </el-form-item>
      </el-form>
      <el-form
        :rules="rules"
        ref="signOutForm"
        :model="periodConfig"
        label-width="150px"
        class="m-form"
        :disabled="!periodConfig.signOut.isOpen"
      >
        <el-form-item label="签退频率：" required>
          <el-radio-group v-model="periodConfig.signOut.checkInFrequency" :disabled="showSignUpNumWithoutTem">
            <el-radio :label="CheckInFrequencyEnum.halfDay">每半天签退一次</el-radio>
            <el-radio :label="CheckInFrequencyEnum.perLesson">每节课签退一次</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="签退地点：" prop="signOut.checkInLocationRadius">
          在培训班办班地点定位签退，允许微调地点，微调半径：
          <el-input-number
            :controls="false"
            :min="0"
            :max="9999"
            class="input-num"
            placeholder="请输入"
            v-model="periodConfig.signOut.checkInLocationRadius"
          ></el-input-number>
          米，建议设为500米
        </el-form-item>
        <el-form-item
          label="签退时间："
          prop="signOut.preCheckOutTime"
          v-if="periodConfig.signOut.checkInFrequency == CheckInFrequencyEnum.halfDay"
        >
          最后一节课结束授课前
          <el-input-number
            :controls="false"
            :min="0"
            :max="60"
            class="input-num"
            placeholder="请输入"
            v-model="periodConfig.signOut.preCheckInTime"
          ></el-input-number>
          分钟和结束授课后
          <el-input-number
            :controls="false"
            :min="0"
            :max="60"
            class="input-num"
            placeholder="请输入"
            v-model="periodConfig.signOut.afterCheckInTime"
          ></el-input-number>
          分钟之间，需签退1次。
        </el-form-item>
        <el-form-item
          label="签退时间："
          prop="signOut.preCheckOutTime"
          v-if="periodConfig.signOut.checkInFrequency == CheckInFrequencyEnum.perLesson"
        >
          每节课结束授课前
          <el-input-number
            :controls="false"
            :min="0"
            :max="60"
            class="input-num"
            placeholder="请输入"
            v-model="periodConfig.signOut.preCheckInTime"
          ></el-input-number>
          分钟和结束授课后
          <el-input-number
            :controls="false"
            :min="0"
            :max="60"
            class="input-num"
            placeholder="请输入"
            v-model="periodConfig.signOut.afterCheckInTime"
          ></el-input-number>
          分钟之间，需签退1次。
        </el-form-item>
      </el-form>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button @click="openDialog = false">取消</el-button>
      <el-button type="primary" @click="sure()" :loading="loading">确定</el-button>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, Vue, Ref, Inject } from 'vue-property-decorator'
  import AttendanceConfig from '@api/service/management/implement/AttendanceConfig'
  import { CheckInFrequencyEnum } from '@api/service/common/implement/enums/CheckInFrequencyEnum'
  import PeriodConfig from '@api/service/management/implement/models/PeriodConfig'
  import { ElForm } from 'element-ui/types/form'
  import { cloneDeep } from 'lodash'
  import SchemeStepProcess from '@api/service/management/train-class/Utils/SchemeStepProcess'
  import { SchemeProcessStatusEnum } from '@api/service/management/train-class/query/enum/SchemeProcessStatusEnum'
  import AttendanceTemplateConfig from '@api/service/management/implement/AttendanceTemplateConfig'

  @Component({})
  export default class extends Vue {
    @Ref('signInForm') signInForm: ElForm
    @Ref('signOutForm') signOutForm: ElForm
    // todo
    form = [{}]
    openDialog = false

    // 接收考勤设置
    @Prop({
      required: true,
      default: () => {
        return new AttendanceConfig()
      }
    })
    attendanceConfig: AttendanceConfig

    @Inject('SchemeStepProcess')
    SchemeStepProcess: SchemeStepProcess

    // 考勤报道枚举赋值
    CheckInFrequencyEnum = CheckInFrequencyEnum

    attendanceTemplateConfig = new AttendanceTemplateConfig()

    CheckInType = ''
    settingTemplate = false
    showSignUpNum = false

    get showSignUpNumWithoutTem() {
      return this.showSignUpNum && this.CheckInType === 'edit'
    }

    // 期别考勤模型
    periodConfig: PeriodConfig = new PeriodConfig()

    // 加载动画
    loading = false

    rules = {
      'signIn.checkInLocationRadius': [{ required: true, message: '请输入签到地点', trigger: 'blur' }],
      'signOut.checkInLocationRadius': [{ required: true, message: '请输入签退地点', trigger: 'blur' }],
      'signIn.preCheckInTime': [{ required: true, validator: this.validateCheckInTime, trigger: 'blur' }],
      'signOut.preCheckOutTime': [{ required: true, validator: this.validateCheckOutTime, trigger: 'blur' }]
    }

    /**
     * 查询单期别设置
     */
    async doQuery() {
      this.loading = true
      await this.periodConfig.getPeriodConfigById()
      this.showSignUpNum = await this.periodConfig.getPeriodHasStudentRegister()
      this.loading = false
    }

    get title() {
      return this.CheckInType === 'tem' ? '设置考勤规则模板' : '期别考勤设置'
    }

    //确定
    async sure() {
      switch (this.CheckInType) {
        case 'tem':
          this.creatOrEditTemplate()
          break
        case 'edit':
          this.editCheck()
          break
        default:
          break
      }
    }

    async editCheck() {
      // 需要校验的数组
      this.loading = true
      // 如果没有开启
      let verifyArr = []
      if (this.periodConfig.signIn.isOpen) verifyArr.push(this.signInForm.validate())
      if (this.periodConfig.signOut.isOpen) verifyArr.push(this.signOutForm.validate())
      await Promise.all(verifyArr)
        .then(async () => {
          const schemeId = this.$route.params.schemeId
          const resp = await this.SchemeStepProcess.checkSchemeCanModify(schemeId)
          if (resp.code === SchemeProcessStatusEnum.finish) {
            const res = await this.periodConfig.setAttendanceRules()
            if (res.isSuccess()) {
              this.$emit('updateSuccess')
              this.openDialog = false
            } else {
              this.$message.error(res.getMessage())
              this.loading = false
            }
          } else {
            this.$message.error(resp.message as string)
          }
        })
        .catch(error => {
          if (error.message) {
            this.$message.error(error.message)
          }
          console.log(error)
        })
        .finally(() => {
          this.loading = false
          verifyArr = []
        })
    }

    async useTemplate() {
      await this.attendanceTemplateConfig.queryTemplate()
      this.periodConfig.useAttendanceTemplate(this.attendanceTemplateConfig, this.showSignUpNumWithoutTem)
    }
    async creatOrEditTemplate() {
      const { attendanceSettingId, signIn, signOut } = this.periodConfig
      this.attendanceTemplateConfig.attendanceTemplateId = attendanceSettingId
      this.attendanceTemplateConfig.signIn = signIn
      this.attendanceTemplateConfig.signOut = signOut
      if (attendanceSettingId) {
        const res = await this.attendanceTemplateConfig.updateTemplate()
        if (res.isSuccess()) {
          this.openDialog = false
          this.$message.success('保存成功')
        } else {
          this.$message.error(res.getMessage())
        }
      } else {
        const res = await this.attendanceTemplateConfig.createTemplate()
        if (res.isSuccess()) {
          this.openDialog = false
        } else {
          this.$message.error(res.getMessage())
        }
      }
    }

    /**
     * 打开弹窗
     */
    open(row: PeriodConfig) {
      this.openDialog = true
      this.periodConfig = cloneDeep(row)
    }

    validateCheckInTime(rule: any, value: any, callback: any) {
      if (this.periodConfig.signIn.preCheckInTime != 0 && !this.periodConfig.signIn.preCheckInTime) {
        callback(new Error('请输入授课前签到时间'))
      } else if (this.periodConfig.signIn.afterCheckInTime != 0 && !this.periodConfig.signIn.afterCheckInTime) {
        callback(new Error('请输入授课后签到时间'))
      } else {
        callback()
      }
    }

    validateCheckOutTime(rule: any, value: any, callback: any) {
      if (this.periodConfig.signOut.preCheckInTime != 0 && !this.periodConfig.signOut.preCheckInTime) {
        callback(new Error('请输入授课前签退时间'))
      } else if (this.periodConfig.signOut.afterCheckInTime != 0 && !this.periodConfig.signOut.afterCheckInTime) {
        callback(new Error('请输入授课后签退时间'))
      } else {
        callback()
      }
    }

    // 切换选项 清除表单校验
    handleType(type: string) {
      if (type == 'signIn') {
        this.signInForm.clearValidate()
      } else if (type == 'signOut') {
        this.signOutForm.clearValidate()
      }
    }
  }
</script>
