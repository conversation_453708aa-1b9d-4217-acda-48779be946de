import AbstractEnum from '@api/service/common/enums/AbstractEnum'
// 定义客户电话类型
// 定义底部落款类型

export enum PhoneFooterType {
  sameasschool = 1,
  custom = 2
}

export default class ApproveStatusType extends AbstractEnum<PhoneFooterType> {
  static enum = PhoneFooterType
  constructor(status?: PhoneFooterType) {
    super()
    this.current = status
    this.map.set(PhoneFooterType.sameasschool, '同本网校')
    this.map.set(PhoneFooterType.custom, '自定义')
  }
}
