import confirmPaymentOrderOfflinePaid from './mutates/confirmPaymentOrderOfflinePaid.graphql'
import createReceiveAccount from './mutates/createReceiveAccount.graphql'
import deleteReceiveAccount from './mutates/deleteReceiveAccount.graphql'
import disableReceiveAccount from './mutates/disableReceiveAccount.graphql'
import enableReceiveAccount from './mutates/enableReceiveAccount.graphql'
import updateOfflinePaymentVoucher from './mutates/updateOfflinePaymentVoucher.graphql'
import updateReceiveAccount from './mutates/updateReceiveAccount.graphql'

export {
  confirmPaymentOrderOfflinePaid,
  createReceiveAccount,
  deleteReceiveAccount,
  disableReceiveAccount,
  enableReceiveAccount,
  updateOfflinePaymentVoucher,
  updateReceiveAccount
}
