@import '../../common/variables';

//文本框-小号-居中
.f-input-num {
  width: 80px;

  .el-input__inner {
    text-align: center;
  }
}

//卡片
.m-card {
  overflow: inherit;

  &.is-header {
    .el-card__body {
      padding: 0;
    }
  }

  &.is-header-sticky {
    .el-card__header {
      position: sticky;
      top: 0;
      z-index: 9;
      background-color: #fff;
      border-radius: 4px 4px 0 0;
    }

    .el-card__body {
      padding: 0;
    }
  }

  &.is-bg {
    border: none;

    .el-card__body {
      padding: 0;
    }
  }

  &.is-overflow-hidden {
    overflow: hidden;
  }

  .el-card__header {
    padding: 14px 20px;

    .tit-txt {
      font-size: 16px;
      font-weight: bold;
      display: flex;
      align-items: center;

      &::before {
        content: '';
        width: 8px;
        height: 8px;
        border-radius: 100%;
        background-color: $base;
        margin-right: 8px;
      }
    }
  }

  .sub-tit {
    font-size: 15px;
    font-weight: bold;
    display: flex;
    align-items: center;
    border-bottom: 1px dashed #eee;
    padding: 12px 22px;

    &::before {
      content: '';
      width: 6px;
      height: 6px;
      border-radius: 100%;
      background-color: #bbb;
      margin-right: 10px;
    }
  }

  .el-collapse {
    border: none;
  }
}

//可收缩卡片
.m-collapse-item {
  .el-collapse-item__header {
    font-size: 16px;
    font-weight: bold;
    padding-left: 20px;
    padding-right: 10px;
  }

  .el-collapse-item__content {
    border-top: 1px solid #e4e7ed;
  }
}

//标题
.m-tit {
  padding-top: 14px;
  padding-bottom: 14px;
  display: flex;

  &.is-border-bottom {
    padding-left: 20px;
    padding-right: 20px;
    border-bottom: 1px solid #eee;

    &.f-pl0 {
      padding-left: 0;
    }
  }

  &.is-small {
    .tit-txt {
      font-size: 15px;
    }
  }

  &.is-mini {
    padding-top: 0;
    padding-bottom: 0;

    .tit-txt {
      font-size: 15px;
    }
  }

  &.bg-gray {
    background-color: #f8f8f8;
  }

  .tit-txt {
    font-size: 16px;
    font-weight: bold;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      width: 8px;
      height: 8px;
      border-radius: 100%;
      background-color: $base;
      margin-right: 8px;
    }
  }

  .ipt {
    width: 200px;
  }
}

//二级标题
.m-sub-tit {
  display: flex;
  align-items: center;
  padding-top: 12px;
  padding-bottom: 12px;

  &.is-border-bottom {
    padding-left: 22px;
    padding-right: 22px;
    border-bottom: 1px dashed #eee;
  }

  &.bg-gray {
    background-color: #f8f8f8;
  }

  .tit-txt {
    font-size: 15px;
    font-weight: bold;
    display: flex;
    align-items: center;

    &::before {
      content: '';
      width: 6px;
      height: 6px;
      border-radius: 100%;
      background-color: #bbb;
      margin-right: 10px;
    }
  }
}

//顶部tab标签
.m-tab-top {
  &.is-sticky {
    .el-tabs__header {
      position: sticky;
      top: 0;
      z-index: 9;
    }

    .el-tabs {
      .el-tabs__header {
        position: relative;
        z-index: 8;
      }
    }
  }

  &.is-border-top {
    border-top: 1px solid #f2f2f2;
  }

  .el-tabs__header {
    background-color: #fff;
    margin-bottom: 0;
  }

  .el-tabs__nav {
    padding-left: 30px;
  }

  .el-tabs__item {
    height: 60px;
    line-height: 60px;
    font-size: 15px;

    &.is-active {
      font-weight: bold;
    }
  }

  .el-tabs__active-bar {
    padding: 0 15px;
    left: 15px;
  }

  .el-tabs {
    .el-tabs__header {
      background-color: transparent;
      margin-bottom: 20px;
    }

    .el-tabs__nav {
      padding-left: inherit;
    }

    .el-tabs__item {
      height: 40px;
      line-height: 40px;
      font-size: 14px;

      &.is-active {
        font-weight: inherit;
      }
    }
  }
}

//卡片标签
.m-tab-card {
  position: relative;

  &.el-tabs {
    .el-tabs__header {
      margin-bottom: 0;
      position: relative;
      top: 1px;

      .el-tabs__nav {
        overflow: hidden;
      }

      .el-tabs__item {
        background-color: #f7f8f9;
        border-bottom: 1px solid #e4e7ed;
        padding: 0 30px !important;
        height: 44px;
        line-height: 44px;

        &.is-active {
          background-color: #fff;
          border-bottom-color: #fff;
          font-weight: bold;
        }
      }
    }
  }

  .tab-tips {
    position: absolute;
    z-index: 9;
    left: 550px;
    top: -30px;
  }

  &.is-margin-bottom {
    .el-tabs__header {
      margin-bottom: 20px;
    }
  }

  &.is-sticky {
    .el-tabs__header {
      position: sticky;
      top: 0;
      z-index: 9;
      margin-bottom: -1px;
      background-color: #f0f2f5;
      padding-top: 15px;
    }

    .fixed-btn {
      position: sticky;
      top: 60px;
      z-index: 9;
      background-color: rgba(#fff, 0.95);
      border-bottom: 1px solid #eee;
      margin-bottom: -1px;
    }
  }

  .el-tabs--left .el-tabs__item.is-left {
    background: none;
    border-bottom: 0;
    padding-left: 10px !important;
    height: 36px;
    line-height: 36px;
  }

  &.is-badge {
    .el-tabs__header {
      .el-tabs__nav-wrap {
        overflow: initial;

        .el-tabs__nav {
          overflow: initial;
        }

        .el-tabs__item {
          position: relative;

          .el-badge {
            position: absolute;
            top: -6px;
            right: -7px;
            z-index: 100;
          }
        }
      }
    }
  }
}

//查询条件
.m-query {
  position: relative;

  &.is-border-bottom {
    margin-bottom: 20px;

    .el-form {
      overflow: hidden;

      &::after {
        content: '';
        position: absolute;
        left: -20px;
        right: -20px;
        bottom: 0;
        height: 1px;
        background-color: #f2f2f2;
      }
    }
  }

  & + .m-collapse {
    margin-top: -20px;
  }

  &.mb0 {
    margin-bottom: 0;
  }

  .el-form-item {
    display: flex;
    margin-bottom: 16px;
  }

  .el-form-item__label {
    line-height: 36px;
  }

  .el-form-item__content {
    flex: 1;
    line-height: 35px;
  }

  .el-select,
  .el-cascader {
    width: 100%;
  }

  .el-date-editor--daterange,
  .el-date-editor--timerange,
  .el-date-editor--datetimerange {
    &.el-input,
    &.el-input__inner {
      width: 100%;
    }

    .el-range__icon,
    .el-range-separator,
    .el-range__close-icon {
      line-height: 28px;
    }
  }

  .el-date-editor .el-range-input {
    font-size: 12px;

    &::placeholder {
      font-size: 14px;
    }
  }

  .input-num {
    width: 80px;

    .el-input__inner {
      text-align: center;
    }
  }

  .el-button {
    padding: 10px;
    min-width: 70px;
  }

  .el-cascader .el-input .el-input__inner {
    height: 36px !important;
  }
}

//表格
.m-table {
  width: 100%;
  color: #444;
  line-height: 1.5;

  thead {
    color: #555;
  }

  th {
    &.el-table__cell {
      background-color: #f5f5f5;
      padding: 10px 0;

      & > .cell {
        padding-left: 18px;
        padding-right: 18px;
      }
    }

    &.is-selection {
      .el-checkbox__input {
        &::after {
          content: '全选';
          display: inline-block;
          vertical-align: 2px;
          margin-left: 10px;
        }
      }
    }
  }

  [class^=el-icon-],
  [class*=" el-icon-"] {
    vertical-align: middle;
  }

  .cell {
    padding-left: 18px;
    padding-right: 18px;
    line-height: 1.5;
  }

  &.el-table--border {
    .el-table__cell {
      padding: 8px 0;

      &.is-right {
        padding-right: 0;
      }
    }
  }

  &.is-statistical {
    .el-table__cell {
      padding: 5px 0;
    }

    th.el-table__cell > .cell {
      padding-left: 10px;
      padding-right: 10px;
    }

    .cell {
      padding-left: 10px;
      padding-right: 10px;
    }
  }

  .el-table__cell {
    padding: 10px 0;

    &.is-right {
      padding-right: 20px;
    }
  }

  &.is-header {
    .el-table__empty-block {
      display: none;
    }
  }

  &.is-body {
    .el-table__header-wrapper {
      display: none;
    }

    .el-table__body-wrapper {
      border-top: 1px solid #ebeef5;
    }
  }

  &.is-tree {
    .el-table__cell {
      padding: 12px 0;
    }

    .cell {
      padding-left: 10px;
      padding-right: 10px;
      display: flex;
      align-items: center;
    }
  }

  .el-tag {
    margin: 3px 5px 3px 0;
    padding: 5px 8px 3px;
    white-space: inherit;
    height: auto;
    line-height: 1.3;
    text-align: left;

    .icon {
      font-size: 16px;
      vertical-align: -1px;
      margin-left: 5px;
    }

    &.el-tag--mini {
      padding: 3px 4px 1px;
    }
  }

  .el-button--text {
    padding: 3px 6px;

    & + .el-button--text {
      margin-left: 0;
    }

    &.f-to-three {
      padding: 0;
      width: 100%;
      text-align: left;

      span {
        display: -webkit-box;
        overflow: hidden;
        white-space: normal;
        text-overflow: ellipsis;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
        word-break: break-all;
        line-height: 20px;

        &.el-tag {
          display: inline-block;
          margin: 0 5px 0 0;
          padding: 0 3px;
          vertical-align: middle;
        }
      }
    }
  }

  .el-empty {
    flex-direction: row;
    padding: 10px 0 15px;
    line-height: 1.5;

    .el-empty__description {
      margin-top: 3px;
      margin-left: 10px;
    }
  }

  .is-selection {
    .el-checkbox__input {
      &::after {
        content: '选择';
        display: inline-block;
        vertical-align: 2px;
        margin-left: 10px;
      }

      &.is-checked {
        &::after {
          color: $base;
        }
      }
    }
  }

  .op-col {
    .el-button {
      font-size: 18px;
    }

    .cell {
      display: block;
    }
  }
  //设置圆形标签-大圆
  .tag-round {
    width: 64px;
    height: 64px;
    border-radius: 64px;
    background-color: $base;
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    line-height: 16px;

    &.is-bg-gray {
      background-color: #999;
      color: #fff;
    }
  }
}
//竖式表格
.info-table {
  border-top: 1px solid #ebeef5;
  border-left: 1px solid #ebeef5;
  position: relative;
  background-color: #fff;

  .info-row {
    width: 100%;
    height: 40px;
    border-bottom: 1px solid #ebeef5;
    display: table;
  }

  .info-th,
  .info-td {
    display: table-cell;
    vertical-align: middle;
    box-sizing: border-box;
    line-height: 1.2;
    padding: 10px;
    word-wrap: break-word;
    white-space: normal;
    word-break: break-all;
  }

  .info-th {
    width: 20%;
    text-align: right;
    border-right: 1px solid #ebeef5;
    font-weight: bold;
    position: relative;

    &.f-tl {
      text-align: left;
    }

    &.bg-gray {
      border-top: 2px solid #e6e6e6;
    }
  }

  .info-td {
    width: 30%;
    border-right: 1px solid #ebeef5;
    position: relative;

    &.p0 {
      padding: 0;
    }
  }

  .col-merge {
    .info-th {
      width: 20%;
    }

    .info-td {
      width: 80%;
    }
  }
}

//表单
.m-form {
  .el-form-item__label {
    font-weight: bold;
    color: #555;
    line-height: 36px;

    .f-vm {
      vertical-align: 0;
    }
  }

  .is-tag {
    position: absolute;
    left: -13px;
    top: 16px;
  }

  .el-select,
  .el-cascader {
    width: 100%;
  }

  .el-date-editor--daterange,
  .el-date-editor--timerange,
  .el-date-editor--datetimerange {
    .el-range__icon,
    .el-range-separator,
    .el-range__close-icon {
      line-height: 28px;
    }
  }

  .el-switch__label {
    color: #999;

    &.is-active {
      color: $base;
    }
  }

  .form-l {
    width: 60%;
  }

  .form-m {
    width: 40%;
  }

  .form-s {
    width: 30%;
  }

  .input-num {
    width: 80px;

    .el-input__inner {
      text-align: center;
    }
  }

  .is-column {
    .el-checkbox,
    .el-radio {
      display: block;
    }

    .el-radio {
      line-height: 36px;
    }
  }

  .rich-text {
    width: 100%;
  }

  .is-text {
    .el-form-item__label,
    .el-form-item__content {
      line-height: 1.5;
    }

    .el-form-item__content {
      //margin-top: 2px;
    }
  }

  .el-upload__tip {
    line-height: 1.5;
    font-size: 13px;
    color: #777;
    display: flex;
    margin-top: 10px;

    .el-icon-warning {
      color: #999;
      font-size: 16px;
      margin-right: 3px;
      position: relative;
      top: 1px;
    }

    .f-link {
      color: $base;
      text-decoration: underline;
      display: inline-block;
    }
  }

  .code {
    width: 120px;
    height: 36px;
    cursor: pointer;
    margin-left: 10px;
    text-align: center;

    img {
      width: 100%;
      height: 100%;
      display: block;
      border-radius: 4px;
      border: 1px solid #dcdef6;
      box-sizing: border-box;
    }

    .el-button {
      width: 100%;
      height: 100%;
      padding: 0 10px;
    }
  }

  .bg-gray {
    background-color: #f5f5f5;

    .el-divider__text {
      background-color: #f5f5f5;
    }
  }
  //竖行表单样式重置
  &.pb0 {
    &.el-form--label-top .el-form-item__label {
      padding-bottom: 0;
    }
  }
  //行高设置
  .lh20 {
    line-height: 20px;
  }
}

//表单宽度限制
.width-limit {
  .el-col {
    max-width: 850px;
  }

  &.m-form {
    position: relative;
    left: -40px;
  }
}

//密码安全性判断
.psw-tips {
  margin-top: 5px;
  display: flex;
  align-items: center;

  .el-progress {
    flex: 1;
  }

  .txt {
    font-size: 12px;
    margin-left: 6px;
    line-height: 1;
    padding-right: 5px;
  }

  .txt-l {
    color: #e93737;
  }

  .txt-m {
    color: #ee9e2d;
  }

  .txt-h {
    color: #49b042;
  }
}

//按钮模块
.m-btn-bar {
  padding-bottom: 10px;

  .el-button {
    height: 40px;
    min-width: 120px;
  }

  &.is-sticky {
    position: sticky;
    bottom: 0;
    z-index: 9999;
    padding-bottom: 15px;
    background-color: rgba(#fff, 0.7);
    backdrop-filter: blur(5px);
  }

  &.is-sticky-1 {
    position: sticky;
    bottom: 0;
    z-index: 9;
    padding-bottom: 15px;
    background-color: rgba(#f0f2f5, 0.7);
    backdrop-filter: blur(5px);
    padding-top: 20px;
  }
}

//文字列表
.m-text-form {
  overflow: hidden;
  display: flex;
  flex-wrap: wrap;

  &.is-column {
    flex-direction: column;
  }

  &.is-border-bottom {
    border-bottom: 1px solid #eee;
  }

  &.is-edit {
    overflow: inherit;

    .edit-icon {
      font-size: 16px;
      margin-left: 5px;
      display: inline-block;
      vertical-align: -1px;
      cursor: pointer;

      &.item {
        border-bottom: none;
      }

      &:hover {
        color: $base;
      }
    }

    .edit-box {
      width: 100%;
      display: flex;
      align-items: center;
      margin-right: 20px;
      position: absolute;
      top: -5px;
      left: 0;
      background-color: #fff;
      transition: all 0.3s;
      opacity: 0;
    }

    .is-editing {
      .edit-box {
        opacity: 1;
      }
    }
  }

  .el-form-item {
    margin-bottom: 16px;

    &.is-form {
      .el-form-item__label,
      .el-form-item__content {
        line-height: 36px;
      }
    }
  }

  .el-form-item__label {
    line-height: 1.6;
    color: #555;
    font-weight: bold;
  }

  .el-form-item__content {
    flex: 1;
    line-height: 1.6;
    word-break: break-all;
  }
}

//图片上传
.m-pic-upload {
  .el-upload--picture-card {
    .el-icon-plus {
      font-size: 20px;
      font-weight: bold;
    }

    &:hover {
      .upload-placeholder,
      .el-icon-plus {
        color: $base;
      }
    }
  }

  .el-upload-list__item {
    overflow: inherit;
  }

  .el-upload-list__item-thumbnail {
    object-fit: contain;
    border-radius: 4px;
  }

  .img-file {
    width: 100%;
    height: 100%;
  }

  .other {
    width: 100%;
    position: absolute;
    left: 100%;
    bottom: 0;
    margin-left: 20px;
  }

  .upload-placeholder {
    line-height: 1.5;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #8c939d;

    .txt {
      margin-top: 8px;
    }
  }

  .el-upload__tip {
    line-height: 1.5;
    font-size: 13px;
    color: #777;
    display: flex;
    margin-top: 10px;

    .el-icon-warning {
      color: #999;
      font-size: 16px;
      margin-right: 3px;
      position: relative;
      top: 1px;
    }

    .f-link {
      color: $base;
      text-decoration: underline;
      display: inline-block;
      cursor: pointer;
    }
  }

  //资讯或课程图16:9上传
  &.proportion-pic {
    .el-upload--picture-card {
      width: 320px;
      height: 180px;
    }

    .el-upload-list__item {
      width: 320px;
      height: 180px;
      display: block;
      margin-bottom: 15px;
    }

    &.is-small {
      .el-upload--picture-card {
        width: 160px;
        height: 90px;
      }

      .el-upload-list__item {
        width: 160px;
        height: 90px;
      }
    }

    .upload-placeholder {
      flex-direction: row;

      .txt {
        margin-left: 10px;
        margin-top: 0;
      }
    }
  }

  //长图上传
  &.long-pic {
    .el-upload--picture-card {
      width: 48%;
      height: 80px;
    }

    .el-upload-list__item {
      width: 48%;
      height: 80px;
      display: block;
      margin-bottom: 15px;
    }

    .upload-placeholder {
      flex-direction: row;

      .txt {
        margin-left: 10px;
        margin-top: 0;
      }
    }
  }

  //小图上传
  &.small-pic {
    .el-upload--picture-card {
      width: 80px;
      height: 80px;
    }

    .el-upload-list__item {
      width: 80px;
      height: 80px;
    }

    .upload-placeholder {
      line-height: 1.2;

      .txt {
        font-size: 12px;
      }
    }
  }
}

//图片分组上传
.m-upload-item {
  display: flex;
  align-items: flex-end;

  .m-pic-upload {
    &.long-pic {
      width: 48%;

      .el-upload--picture-card,
      .el-upload-list__item {
        width: 100%;
      }
    }
  }

  .other {
    width: 48%;
    padding-left: 20px;
  }
}

//开关
.m-switch {
  .el-switch__label {
    opacity: 0;
    transition: all 0.3s;

    &.is-active {
      opacity: 1;
    }
  }

  .el-switch__label--left {
    position: absolute;
    left: 50px;
    min-width: 50px;

    &.is-active {
      color: #999;
    }
  }
}

//文字提示
.m-tooltip {
  &.el-tooltip__popper {
    font-size: 14px;
    max-width: 400px;
    line-height: 1.5;
  }

  .f-cb {
    color: lighten($base, 15%);

    &:hover {
      color: lighten($base, 15%);
    }
  }

  &.is-small {
    padding: 5px 8px;
    font-size: 13px;
  }
}

.m-tooltip-icon {
  display: inline-block;
  vertical-align: -1px;
  font-size: 18px;
  line-height: 1;
}

//弹窗
.m-dialog {
  display: flex;
  align-items: center;

  .el-dialog {
    margin-top: -50px !important;
  }

  .el-dialog__body {
    color: #444;
  }

  .dialog-alert {
    display: flex;
    padding-right: 10px;

    &.is-big {
      justify-content: center;
      align-items: center;

      .icon {
        font-size: 60px;
      }
    }

    .icon {
      font-size: 30px;
      margin-right: 16px;

      &.warning {
        color: #de9e3e;
      }

      &.error {
        color: #f56c6c;
      }

      &.success {
        color: #67c23a;
      }

      &.info {
        color: #909399;
      }
    }

    .txt {
      margin-top: 5px;
    }
  }

  &.no-close {
    .el-dialog__body {
      margin-top: -50px !important;
    }
  }
}

//图片弹窗
.m-dialog-pic {
  display: flex;
  align-items: center;

  .el-dialog {
    margin-top: -50px !important;
  }

  .el-dialog__body {
    padding-top: 20px;
    padding-bottom: 40px;
  }

  img {
    width: auto;
    max-width: 1000px;
    max-height: 600px;
    display: block;
    margin-left: auto;
    margin-right: auto;
  }
}

//抽屉
.m-drawer {
  .el-drawer__body {
    overflow-y: scroll;
    display: flex;
    flex-direction: column;
  }

  .el-drawer__header {
    font-size: 18px;
    color: #333;
    margin-bottom: 20px;
  }

  .drawer-bd {
    padding: 0 20px 20px;
    flex: 1;
  }

  .drawer-ft {
    position: sticky;
    bottom: 0;
    z-index: 9;
    padding: 15px 0;
    background-color: rgba(#f8f8f8, 0.9);
    border-top: 1px solid #eee;
    text-align: center;
  }

  .m-btn-bar {
    &.is-sticky {
      padding-top: 10px;
    }
  }
}

//提示
.m-alert {
  padding: 9px 16px;

  &.is-border-bottom {
    &.el-alert--warning {
      border-bottom: 1px solid #ece2d3;
    }
  }

  .el-alert__title {
    font-size: 14px;

    & + .el-alert__description {
      margin-top: 5px;
    }
  }

  .el-alert__description {
    margin-top: 0;
    font-size: 14px;

    .f-c3 {
      color: #333;
    }

    .f-c6 {
      color: #666;
    }
  }

  .el-alert__icon {
    font-size: 20px;
    width: auto;
    margin-right: 8px;
  }

  .el-alert__content {
    padding: 0;
    width: 100%;
  }
}

//栏目设置
.m-web-column {
  width: 930px;
  margin: 0 auto;

  .item {
    border: 1px solid #ebedf1;
    border-radius: 4px;
    margin-bottom: 15px;
    overflow: hidden;
    transition: all 0.3s;
    background-color: #fff;

    &:hover {
      box-shadow: 0 0 15px rgba(#000, 0.1);
    }

    &.disabled {
      background-color: #f5f5f5;

      .item-ft {
        background-color: #eee;
      }
    }

    &.add {
      height: 120px;
      line-height: 120px;
      border: 1px dashed $base;
      color: $base;
      background-color: rgba($base, 0.1);
      text-align: center;
      cursor: pointer;

      &:hover {
        background-color: rgba($base, 0.15);
        box-shadow: none;
      }
    }
  }

  .item-hd {
    padding: 10px 15px 0;
    color: #bbb;

    & + .item-bd {
      //height: 46px;
    }
  }

  .item-bd {
    display: flex;
    padding: 0 15px;
    height: 56px;
    align-items: center;
    position: relative;

    .name {
      flex: 1;
      line-height: 1.3;
      margin-right: 5px;
    }

    .el-tag {
      border-radius: 100px;
    }

    .ipt {
      position: absolute;
      top: 10px;
      left: 10px;
      right: 10px;
      width: auto;
    }
  }

  .item-ft {
    text-align: right;
    background-color: #f9f9f9;
    line-height: 28px;
    padding: 0 15px;

    .el-button {
      font-size: 12px;
    }
  }
}

//属性值
.m-attribute {
  .item {
    border: 1px solid #ebedf1;
    border-radius: 4px;
    margin-bottom: 20px;
    overflow: hidden;
    transition: all 0.3s;
    background-color: #fff;
    display: flex;
    align-items: center;

    &:hover {
      box-shadow: 0 0 15px rgba(#000, 0.1);
    }

    &.disabled {
      background-color: #f5f5f5;

      .item-ft {
        background-color: #eee;
      }
    }
  }

  .item-bd {
    display: flex;
    padding-left: 15px;
    height: 50px;
    align-items: center;
    position: relative;
    flex: 1;
    min-width: 0;

    .name {
      line-height: 1.2;
      font-weight: bold;
    }
  }

  .item-ft {
    padding: 0 15px;
    text-align: right;

    .el-button {
      font-size: 12px;
      padding: 5px 0;
    }
  }

  .external {
    border-radius: 0 100px 100px 100px;
    background-color: lighten($base, 40%);
    font-size: 12px;
    height: 22px;
    display: flex;
    overflow: hidden;
    margin-bottom: 2px;

    .tit {
      width: 60px;
      padding: 3px 0;
      background-color: $base;
      color: #fff;
      text-align: center;
    }

    .txt {
      max-width: 110px;
      padding: 3px 8px;
      color: $base;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .m-tit,
  .m-sub-tit {
    .external {
      .txt {
        width: auto;
        padding: 3px 8px;
      }
    }
  }

  .el-collapse {
    border: none;
  }

  .el-collapse-item__wrap {
    border-bottom: none;
  }

  .el-collapse-item {
    margin-bottom: 10px;
  }

  .el-collapse-item__header {
    background-color: #f5f5f5;
    padding: 0 10px 0 20px;
    border-radius: 4px;
    transition: border 0.3s;
    border: 1px solid transparent;

    &.is-active {
      margin-bottom: 0;
      border-radius: 4px 4px 0 0;
      border-color: #ebedf1;
      border-bottom-color: transparent;
    }
  }

  .el-collapse-item__content {
    border: 1px solid #ebedf1;
    border-top: none;
    border-radius: 0 0 4px 4px;
    padding-bottom: 0;
  }

  .el-cascader-panel {
    &.is-bordered {
      border: none;
    }
  }

  .el-cascader-menu__wrap {
    height: auto;
    overflow: inherit;
  }

  .el-cascader-menu {
    padding: 10px 6px 16px 0;
  }

  .el-cascader-node {
    height: 40px;
    line-height: 40px;
  }
}

//风格设置
.m-style-set {
  width: 800px;
  margin: 0 auto;

  .style-bd {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1;
    border: 1px solid #ebedf1;
    border-radius: 4px;
    padding: 30px 50px;

    .item {
      width: 60px;
      height: 60px;
      line-height: 60px;
      text-align: center;
      border-radius: 4px;
      margin: 10px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        transform: scale(1.1);
      }

      &.selected {
        color: #fff;
        font-size: 30px;
        font-weight: bold;
      }
    }
  }
}

//功能设置
.m-function-set {
  display: flex;
  flex-wrap: wrap;
  margin-right: 80px;

  .item {
    padding: 20px;
    display: flex;
    align-items: center;
    border: 1px solid #ebedf1;
    margin-top: -1px;
    margin-left: -1px;

    .name {
      flex: 1;
    }
  }
}

//web-banner图片大小
.web-banner {
  width: 400px;

  img {
    border-radius: 4px;
  }
}

//h5-banner图片大小
.h5-banner {
  height: 90px;

  img {
    border-radius: 4px;
  }
}

//课程图片大小
.course-pic {
  width: 320px;
  height: 180px;

  img {
    border-radius: 4px;
    width: 100%;
    height: 100%;
  }

  &.is-small {
    width: 160px;
    height: 90px;
  }
}

//汇款凭证图片
.img-hover {
  position: relative;
  overflow: hidden;

  &:hover {
    .hover-txt {
      bottom: 0;
    }
  }

  .hover-txt {
    width: 100%;
    position: absolute;
    left: 0;
    bottom: -32px;
    background-color: rgba(0, 0, 0, 0.6);
    color: #fff;
    text-align: center;
    padding: 2px 10px;
    box-sizing: border-box;
    transition: all 0.4s;
    font-size: 13px;
  }
}

//空数据
.m-no-date {
  width: 100%;
  max-width: 700px;
  margin-left: auto;
  margin-right: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  &.is-row {
    flex-direction: row;

    .date-bd {
      margin-top: 10px;
      margin-left: 20px;
      text-align: left;
    }
  }

  .img {
    width: 200px;
    display: block;

    &.is-small {
      width: 150px;
    }

    img {
      width: 100%;
      height: 100%;
    }
  }

  .date-bd {
    margin-bottom: 10px;
    text-align: center;
  }

  .login-txt {
    margin-left: 30px;

    .txt-1 {
      font-size: 30px;
      font-weight: bold;
    }

    .txt-2 {
      font-size: 24px;
    }
  }
}

.m-tree {
  .el-tree-node__content {
    line-height: 36px;
  }
}

//课程分类树形列表
.m-course-tree {
  max-height: 500px;
  overflow: auto;
  color: #333;

  .custom-tree-node {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .el-tree-node__expand-icon {
    font-size: 18px;
  }

  .is-current {
    &.is-focusable {
      & > .el-tree-node__content {
        background-color: $base;

        .el-tree-node__expand-icon {
          color: #fff;

          &.is-leaf {
            color: transparent;
          }
        }

        .custom-tree-node {
          color: #fff;

          .el-button {
            color: #fff;
          }
        }
      }
    }
  }

  .el-tree-node__content {
    height: auto;
    padding: 8px 4px 8px;
    border-bottom: 1px solid #ebeef5;
    white-space: initial;
  }

  .tit {
    flex: 1;
    min-width: 0;
    padding-right: 10px;
  }

  .op {
    padding-right: 15px;

    .el-button {
      font-size: 18px;
    }
  }
}

//关联媒体文件
.m-file-upload {
  border: 1px solid #ebedf1;
  padding: 10px 20px;
  border-radius: 4px;
  display: flex;
  align-items: center;

  .el-progress {
    width: 240px;
    margin-right: 10px;
    margin-left: 30px;
  }

  .time {
    margin-right: 30px;
  }

  .size {
    flex: 1;
    color: #999;
    margin-left: 5px;
  }

  .icon {
    color: #999;
    font-size: 18px;
    margin-right: 10px;
  }

  .name {
    max-width: 280px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: center;

    &.f-flex-sub {
      max-width: inherit;
    }
  }
}

//权限配置
.m-authority {
  .selected-all {
    background-color: #e3f0fe;
    padding: 0 20px;
    margin-bottom: 18px;
    height: 50px;
    line-height: 50px;
    border-radius: 4px;
    position: relative;

    &::after {
      content: '';
      width: 0;
      height: 0;
      border: 8px solid transparent;
      border-top-color: #e3f0fe;
      position: absolute;
      left: 20px;
      bottom: -16px;
    }
  }

  .el-collapse {
    border: none;
  }

  .el-collapse-item__wrap {
    border-bottom: none;
  }

  .el-collapse-item {
    margin-bottom: 10px;
  }

  .el-collapse-item__header {
    background-color: #f5f5f5;
    padding: 0 10px 0 20px;
    border-radius: 4px;
    transition: border 0.3s;
    border: 1px solid transparent;

    &.is-active {
      margin-bottom: 0;
      border-radius: 4px 4px 0 0;
      border-color: #dcdfe6;
      border-bottom-color: transparent;
    }
  }

  .el-collapse-item__content {
    border: 1px solid #dcdfe6;
    border-top: none;
    border-radius: 0 0 4px 4px;
    padding-bottom: 0;
    transition: all 0.3s;
  }

  .el-cascader-panel {
    &.is-bordered {
      border: none;
    }
  }

  .el-cascader-menu__wrap {
    height: auto;
    overflow: inherit;
  }

  .el-cascader-menu {
    padding: 10px 6px 16px 0;
  }

  .el-cascader-node {
    height: 40px;
    line-height: 40px;
  }
}

//试题选项
.m-option-btn {
  .el-button--mini {
    padding: 4px 8px;
  }
}

//步骤
.m-steps {
  .el-step__icon {
    width: 56px;
    height: 56px;
    background-color: #ddd;
    border: 8px solid #fff;
    font-size: 18px;
    color: #ababab;
  }

  .is-horizontal {
    .el-step__line {
      height: 3px;
      background-color: #ddd;
      top: 50%;
      margin-top: -1px;
    }
  }

  .el-step__head {
    position: relative;

    &.is-finish {
      .el-step__icon {
        background-color: $base;
        color: #fff;
      }

      .el-step__line-inner {
        background-color: $base;
      }
    }
  }

  .el-step__title {
    font-size: 14px;

    &.is-process {
      font-weight: normal;
      color: #ababab;
    }

    &.is-wait {
      color: #ababab;
    }
  }
}

//步骤
.m-vertical-steps {
  padding: 30px 80px 20px;

  .el-step__icon {
    width: 56px;
    height: 56px;
    background-color: #ddd;
    border: 8px solid #fff;
    font-size: 18px;
    color: #ababab;
  }

  .is-horizontal {
    .el-step__line {
      height: 3px;
      background-color: #ddd;
      top: 50%;
      margin-top: -1px;
    }
  }

  .el-step__head {
    position: relative;

    &.is-finish {
      .el-step__icon {
        background-color: $base;
        color: #fff;
      }
    }
  }

  .el-step__title {
    font-size: 14px;

    &.is-process {
      font-weight: normal;
      color: #444;
    }

    &.is-finish {
      color: #444;
    }
  }

  .is-vertical {
    justify-content: flex-start;
    flex-basis: inherit !important;

    .el-step__line {
      left: 27px;
    }

    .el-step__head {
      width: 56px;
    }

    .el-step__main {
      margin-bottom: 30px;
    }

    .el-step__title {
      line-height: 56px;
    }
  }
}

//分割线
.m-divider {
  background-color: #f1f1f1;

  &.el-divider--horizontal {
    margin: 20px 0;
  }

  &.no-mb {
    margin-bottom: 0;
  }
}

//大题配置
.m-question-set {
  background-color: #f8f8f8;
  border-radius: 4px;
  padding: 22px 20px 0;
  border: 1px solid #eee;
  position: relative;

  .is-text .el-form-item__content {
    margin-top: 0;
  }
}

//订单状态
.m-order-state {
  margin-bottom: 10px;

  &.el-card {
    border-top: 4px solid $base;
  }

  .el-card__body {
    display: flex;
  }

  .info {
    min-width: 30%;
    padding: 0 20px;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .state {
    font-size: 20px;
    font-weight: bold;
    margin-top: 15px;
  }

  .process {
    flex: 1;
    border-left: 1px solid #e6e6e6;
    padding: 50px 50px 0;

    .el-step__icon {
      font-size: 26px;
      width: 60px;
      height: 26px;

      .hb-iconfont {
        font-size: 26px;
        color: #ccc;
        font-weight: normal;
      }
    }

    .el-step__title {
      font-size: 14px;
      line-height: 38px;
    }

    .el-step.is-horizontal .el-step__line {
      background-color: #ddd;
      height: 1px;
    }

    .el-step__line-inner {
      border-bottom: 0;
    }

    .el-step__description {
      line-height: 16px;
    }

    .el-step__head.is-process {
      color: #ababab;
      border-color: #ababab;
    }

    .el-step__head.is-wait {
      color: #ababab;
      border-color: #ababab;
    }

    .el-step__title.is-wait {
      color: #ababab;
    }

    .el-step__title.is-process {
      font-weight: normal;
      color: #ababab;
    }

    .el-step__head.is-finish {
      color: $base;
      border-color: $base;

      .hb-iconfont {
        color: $base;
      }
    }

    .el-step__title.is-finish,
    .el-step__description.is-finish {
      color: $base;
    }
  }
}

//订单详情页 订单信息
.m-order-info {
  .el-card__body {
    display: flex;
    flex-wrap: wrap;

    .el-col {
      height: 100%;
    }
  }

  .card-header {
    padding: 0;
  }

  .m-tit {
    .tit-txt {
      font-size: 15px;
    }
  }

  .el-form-item {
    margin-bottom: 12px;
  }

  .el-form-item__label {
    color: #999;
    font-weight: normal;
  }

  .right {
    width: 30%;
    border-left: 1px solid #e6e6e6;
  }
}

//订单-价格汇总
.m-order-sum {
  text-align: right;
  color: #666;
  font-size: 13px;

  .item {
    padding-right: 20px;
    line-height: 2;
  }

  .price {
    width: 80px;
    display: inline-block;
    font-weight: bold;
    color: #333;

    .num {
      font-size: 14px;
    }
  }

  .sum-price {
    background-color: #f8f8f8;
    border-top: 1px solid #eee;
    padding: 8px 20px;
    margin-top: 10px;

    .price {
      color: $danger;
    }

    .num {
      font-size: 18px;
    }
  }

  .el-button {
    min-width: 130px;
    height: 40px;
    font-size: 15px;
    font-weight: bold;
  }
}

//弹窗提示样式
.m-popover {
  padding: 20px;
}

//证书图片列表
.m-certificate-img {
  li {
    float: left;
    margin: 5px;
  }

  .img {
    width: 80px;
    height: 45px;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

//折叠面板
.m-collapse {
  &.no-border {
    border: none;

    .el-collapse-item__wrap {
      border-bottom: none;
    }
  }
}

//精品课程分类
.m-course-classify {
  border-right: 1px solid #e6e6e6;
  padding-right: 30px;
  height: 100%;

  .item {
    padding: 12px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #eee;
    cursor: pointer;

    &:hover,
    &.current {
      background-color: rgba($base, 0.1);
    }

    &.current {
      background-color: $base;
      color: #fff;

      .icon {
        &:hover {
          color: #fff;
          opacity: 0.8;
        }
      }
    }

    .icon {
      cursor: pointer;
      font-size: 20px;
      margin-left: 10px;

      &:hover {
        color: $base;
      }
    }
  }
}

//已报方案
.m-plan-list {
  max-height: 580px;
  overflow: auto;

  .el-table__cell {
    padding: 12px 0;
    cursor: pointer;
  }

  &.is-arrow {
    .el-table__body-wrapper,
    .cell {
      overflow: inherit;
    }

    .cell {
      &::after {
        content: '';
        width: 0;
        height: 0;
        border: 8px solid transparent;
        position: absolute;
        right: 5px;
        top: 50%;
        margin-top: -8px;
        transition: all 0.4s;
      }
    }

    .current-row {
      .cell {
        position: relative;

        &::after {
          border-left-color: #fff;
        }
      }
    }
  }

  .cell {
    padding: 0 10px;
  }

  .current-row {
    .el-button--text,
    .el-table__expand-icon {
      color: #fff;
    }
  }

  .el-table__body tr.current-row > td.el-table__cell,
  .el-table__body tr.el-table__row--striped.current-row td.el-table__cell {
    background-color: $base;
    color: #fff;

    .f-c9 {
      color: rgba(#fff, 0.6);
    }
  }
}

//考试分数
.m-score {
  width: 15%;
  min-width: 100px;
  background-color: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .num {
    font-size: 36px;
    line-height: 1;
    margin-right: 5px;
    font-weight: bold;
  }
}

//登录/忘记密码
.m-login-wrap {
  height: 100%;
  width: 100%;
  background: url('../assets/images/login_bg.jpg') no-repeat #ddd center -40px;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-between;

  &.bg-forget {
    background-image: url('../assets/images/forget_bg.jpg') ;
    justify-content: center;

    .wrap-bd {
      background: #fff;
      height: auto;
    }
  }

  .wrap-bd {
    width: 1200px;
    min-height: 700px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    transition: all 0.4s;
    position: relative;

    .m-logo {
      font-size: 40px;
      font-weight: bold;
      padding: 100px 0 90px 37px;
      display: flex;
      align-items: center;

      .logo-pic {
        margin-right: 10px;
      }
    }
  }

  .m-login-footer {
    background-color: #fff;
    border-top: 1px solid #dce9fc;
    width: 100%;
    padding: 20px 0;
    color: #666;
    font-size: 14px;
    text-align: center;
    height: 100px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    p {
      display: block;
      margin: 0;
      padding: 0;
    }

    a {
      color: #666;

      &:hover {
        color: $base;
        text-decoration: underline;
      }
    }

    .img {
      vertical-align: middle;
      margin-right: 5px;
    }
  }

  .m-login {
    width: 400px;
    height: 460px;
    padding: 40px;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 10px;
    position: absolute;
    right: 35px;
    top: 240px;

    .m-login-tit {
      font-size: 18px;
      font-weight: bold;
      text-align: center;
      position: relative;
      height: 38px;
      margin-bottom: 24px;

      &::before {
        content: "";
        display: inline-block;
        width: 72px;
        position: absolute;
        left: 50%;
        margin-left: -36px;
        bottom: 0;
        background-color: $base;
        height: 3px;
      }
    }

    .m-form {
      margin-top: 30px;
    }

    .el-tabs__item {
      height: 60px;
      line-height: 60px;
      font-size: 18px;
      padding: 0;

      &.is-active {
        font-weight: bold;
      }
    }

    .el-form-item {
      margin-bottom: 28px;

      &.op {
        margin-top: -9px;
        margin-bottom: 8px;
      }
    }

    .el-input__inner {
      height: 52px;
      line-height: 52px;
      padding-left: 40px;
    }

    .el-input__prefix {
      color: #dcdfe6;
      left: 14px;
      top: 2px;

      .iconfont,
      .hb-iconfont {
        font-size: 18px;
      }
    }

    .el-form-item__error {
      left: 42px;
    }

    .m-btn-bar {
      padding-top: 0;

      .el-button {
        width: 100%;
        height: 52px;
        font-size: 15px;
      }

      .el-form-item__content {
        line-height: 1.5;
      }
    }

    .yzm {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      flex-direction: row;

      .el-input {
        width: 190px;
      }

      .yzm-pic {
        width: 120px;
        height: 52px;
        margin-left: 10px;

        .img {
          width: 100%;
          height: 100%;
          display: block;
          border-radius: 4px;
          border: 1px solid #dcdef6;
          box-sizing: border-box;
        }
      }
    }
  }

  .m-forget {
    display: block;
    padding: 30px;
    box-sizing: border-box;
    height: 100%;
    overflow: auto;
  }
}

//封面图片列表
.m-pic-list {
  width: 684px;
  margin: 0 auto;

  .item {
    width: 320px;
    border: 1px solid #eee;
    border-radius: 5px;
    float: left;
    margin-right: 20px;
    margin-bottom: 20px;
    transition: all 0.4s;
    cursor: pointer;

    &:hover {
      box-shadow: 0 0 15px rgba(#000, 0.15);
    }

    .info {
      height: 50px;
      box-sizing: border-box;
      padding: 10px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .course-pic {
    display: block;

    img {
      border-radius: 4px 4px 0 0;
    }
  }
}

//网校配置概览
.m-school-set {
  .item {
    border-right: 1px solid #ebeef5;
    position: relative;
    padding: 40px 30px;

    .arrow {
      position: absolute;
      right: -20px;
      top: 50%;
      margin-top: -18px;
      font-size: 36px;
      background-color: #fff;
      color: #d5d9e2;
    }
  }

  .item-hd {
    display: flex;
    align-items: center;

    .name {
      flex: 1;
      font-size: 15px;
      font-weight: bold;
    }
  }

  .item-bd {
    margin-top: 40px;
    margin-left: -3px;

    .el-button {
      border: 3px solid #fff;
      padding: 9px 28px 9px 12px;
      position: relative;

      &::before {
        content: '';
        width: 20px;
        height: 38px;
        position: absolute;
        left: -3px;
        top: -3px;
      }

      & + .el-button {
        margin-left: -25px;
      }

      &:last-child {
        padding-right: 12px;
      }
    }

    .el-button--primary {
      background-color: $base;
      cursor: default;
    }

    .el-button--info {
      background-color: #b7c5da;

      &:hover {
        background-color: darken(#b7c5da, 5%);
      }
    }
  }

  .el-col {
    &:last-child {
      border-right: 0;

      .arrow {
        display: none;
      }
    }
  }

  //.el-col-sm-12 {
  //  &:nth-child(2n) {
  //    border-right: 0;
  //
  //    .arrow {
  //      display: none;
  //    }
  //  }
  //
  //  &:nth-child(-n + 2) {
  //    .item {
  //      border-bottom: 1px solid #ebeef5;
  //    }
  //  }
  //}
}

//首页数据展示
.m-index-data {
  display: flex;
  align-items: center;
  padding: 20px;

  .icon {
    width: 108px;
    height: 108px;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 100%;

    .iconfont {
      font-size: 50px;
    }
  }

  .num-item {
    flex: 1.2;
    text-align: center;

    .num {
      font-size: 36px;
      font-weight: bold;
    }

    .tit {
      font-size: 15px;
      color: #999;
    }
  }

  &.data-1 {
    .icon {
      background-color: #f1f9ee;
      color: #8ccc6d;
    }

    .iconfont {
      text-shadow: 0 0 16px rgba(#67c23a, 0.5);
    }

    .important {
      color: #8ccc6d;
    }
  }

  &.data-2 {
    .icon {
      background-color: #fdf8eb;
      color: #f0bc4d;
    }

    .iconfont {
      text-shadow: 0 0 16px rgba(#f0bc4d, 0.5);
    }

    .important {
      color: #f0bc4d;
    }
  }
}

//首页方案排行
.m-index-rank {
  padding: 0 15px;

  .item {
    display: flex;
    min-width: 0;
    line-height: 34px;

    &.hd {
      font-weight: bold;
      font-size: 15px;
      margin-bottom: 5px;

      .rank {
        color: #444;
      }
    }
  }

  .rank {
    width: 30px;
    text-align: center;
    color: #999;

    .rank-num {
      position: relative;
      z-index: 1;
    }

    &.first,
    &.second,
    &.third {
      font-weight: bold;
      position: relative;

      &::before {
        content: '';
        width: 24px;
        height: 24px;
        position: absolute;
        left: 50%;
        top: 50%;
        margin: -12px 0 0 -12px;
        border-radius: 100%;
      }
    }

    &.first {
      color: #e22929;

      &::before {
        background-color: #fadddd;
      }
    }

    &.second {
      color: #f66313;

      &::before {
        background-color: #fee6d9;
      }
    }

    &.third {
      color: #f19c16;

      &::before {
        background-color: #fdefda;
      }
    }
  }

  .name {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 15px 0 30px;
  }

  .num {
    width: 50px;
    text-align: right;
  }
}

//首页图表
.m-index-chart {
  height: 380px;
}

//侧边悬浮
.m-layout {
  position: fixed;
  right: 0;
  top: 45%;
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  .item {
    background-color: $base;
    color: #fff;
    margin-bottom: 5px;
    border-radius: 4px 0 0 4px;
    height: 40px;
    max-width: 40px;
    padding: 0 10px;
    display: flex;
    align-items: center;
    line-height: 1.2;
    overflow: hidden;
    transition: all 0.4s;
    box-sizing: border-box;

    .bd {
      flex: 1;
      opacity: 0;
      transition: all 0.9s;
    }

    .icon {
      font-size: 22px;
      margin-right: 8px;
    }

    &.is-important {
      background-color: $important;
    }

    &.is-warning {
      background-color: $warning;
    }

    &:hover {
      max-width: 200px;

      .bd {
        opacity: 1;
      }
    }
  }
}

//修改标记
.is-tag {
  width: 6px;
  height: 6px;
  background-color: $warning;
  border-radius: 100%;
  display: inline-block;
  vertical-align: 2px;
  margin-right: 8px;
}

//新手引导
.m-guide-wrap {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 10;
  background-color: rgba(#000, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .wrap-bd {
    width: 100%;
    flex: 1;
    overflow: auto;
  }
}

.m-guide {
  width: 450px;
  margin-left: auto;
  margin-right: auto;
  background-color: #fff;
  position: relative;
  border-radius: 5px;
  text-align: center;
  padding: 30px 0 25px;
  margin-bottom: 20px;

  .tit {
    font-size: 16px;
    margin-bottom: 20px;
    font-weight: bold;
    color: #222;
  }

  .img {
    margin-top: 10px;
  }

  .el-button {
    width: 160px;
  }
}

.m-guide-step {
  width: 350px;
  margin-left: auto;
  margin-right: auto;
  background-color: #fff;
  border-radius: 5px;
  margin-bottom: 20px;
  display: flex;
  align-items: flex-start;
  box-sizing: border-box;
  padding: 15px 20px 10px;

  &.is-big {
    width: 460px;
  }

  .img {
    padding-right: 20px;
    display: block;
  }

  .hd {
    font-size: 15px;
    font-weight: bold;
    margin-bottom: 5px;
  }

  .ft {
    margin-top: 10px;
    text-align: right;
  }

  .el-button {
    padding-top: 0;
    padding-bottom: 0;

    &:hover {
      text-decoration: underline;
    }

    &.is-gray {
      color: #bbb;
    }
  }

  .dot {
    position: relative;
    padding-left: 13px;
    margin-bottom: 3px;

    &::before {
      content: '';
      width: 6px;
      height: 6px;
      border-radius: 100%;
      position: absolute;
      left: 0;
      top: 7px;
      background-color: #ccc;
    }
  }
}

//富文本样式
.tox-tinymce {
  border-radius: 4px;
  border-color: #dcdfe6;
}

.tox {
  .tox-statusbar {
    border-color: #dcdfe6;
  }
}

//配送信息-领取列表
.take-address {
  border-radius: 5px;
  border: 1px solid #e6e6e6;
  position: relative;
  overflow: hidden;
  padding: 20px;
  margin-bottom: 20px;
  cursor: pointer;

  &.is-checked,
  &:hover {
    .label {
      &::before {
        border-right: 50px solid $base;
      }
    }
  }

  .label {
    position: absolute;
    right: 0;
    top: 0;
    padding-top: 2px;
    padding-right: 6px;

    &::before {
      content: ' ';
      width: 0;
      height: 0;
      border-right: 50px solid #e6e6e6;
      border-bottom: 50px solid transparent;
      position: absolute;
      top: 0;
      right: 0;
      transition: all 0.4s;
    }

    .hb-iconfont {
      color: #fff;
      position: relative;
      z-index: 2;
    }
  }
}

//公共业务属性-选择年度列表
.m-radio-border-list {
  max-height: 150px;
  overflow: hidden;
  overflow-y: scroll;
  padding-left: 10px;
  padding-bottom: 10px;

  .el-radio.is-bordered + .el-radio.is-bordered,
  .el-checkbox.is-bordered + .el-checkbox.is-bordered {
    margin-left: 0;
  }

  .el-radio.is-bordered,
  .el-checkbox.is-bordered {
    margin-right: 10px;
    margin-top: 10px;
    min-width: 101px;
  }
}

//公共业务属性-地区列表
.m-city-btn-list {
  max-height: 138px;
  overflow: hidden;
  overflow-y: scroll;
  padding-left: 10px;
  padding-bottom: 10px;

  .el-button + .el-button {
    margin-left: 0;
  }

  .el-button {
    margin-top: 10px;
    min-width: 101px;
    margin-right: 10px;

    .el-icon-arrow-right {
      margin-left: 3px;
    }
  }
}

//行业属性弹窗-单选控件列表
.m-attribute-select {
  width: 100%;

  .el-radio {
    border: 1px solid #e4e7ed;
    border-radius: 3px;
    padding: 15px;
    margin-top: 20px;
    margin-right: 0;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    box-sizing: border-box;

    .el-radio__label {
      flex: 1;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
    }

    .tit {
      width: 400px;
      display: inline-block;
      font-size: 15px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    &.is-checked {
      border: 1px solid $base;
    }
  }
}

//行业属性弹窗-树形控件
.m-attribute-tree {
  font-size: 15px;
  overflow: auto;
  max-height: 525px;

  .el-tree-node__content {
    line-height: 35px;
    height: 35px;

    .el-tree-node__expand-icon {
      font-size: 15px;
    }

    .el-tree-node__label {
      font-size: 15px;
      font-weight: bold;
    }
  }

  .el-tree-node__children {
    .el-tree-node__label {
      font-weight: normal;
    }
  }
}

//模板配置-模版图片列表
.m-demo-pic {
  li {
    width: 256px;
    float: left;
    margin-right: 100px;
    margin-bottom: 20px;

    &:nth-child(3n) {
      margin-right: 0;
    }

    .demo-pic {
      width: 250px;
      height: 350px;
      background-color: #fff;
      text-align: center;
      position: relative;
      border: 1px solid #dcdcdc;
      margin-bottom: 10px;

      .mask {
        width: 100%;
        height: 100%;
        position: absolute;
        top: 0;
        left: 0;
        background-color: rgba(#000, 0.6);
        text-align: center;
        color: #fff;
        font-size: 30px;
        padding-top: 45%;
        box-sizing: border-box;
        opacity: 0;
        transition: all 0.4s;

        .icon {
          cursor: pointer;
          transition: all 0.4s;

          &:hover {
            opacity: 0.8;
          }
        }
      }

      .el-image {
        width: 100%;
        height: 100%;
        overflow: hidden;
      }

      .pic {
        width: 250px;
        height: 350px;
        overflow: overlay;

        img {
          width: 100%;
          vertical-align: middle;
          height: auto;
          min-height: 100%;
          object-fit: contain;
        }

        //滚动条样式
        &::-webkit-scrollbar {
          width: 4px;
          height: 1px;
        }

        &::-webkit-scrollbar-thumb {
          border-radius: 6px;
          background: rgba(0, 0, 0, 0.3);
        }

        &::-webkit-scrollbar-track {
          background-color: #fff;
        }
      }

      .el-checkbox {
        position: absolute;
        left: 0;
        bottom: 0;
        width: 250px;
        height: 80px;
        z-index: 2;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        font-size: 20px;
        display: flex;
        background: rgba(0, 0, 0, 0.8);
        transition: all 0.4s;
        opacity: 0;

        .el-checkbox__inner {
          width: 32px;
          height: 32px;
          margin-right: 10px;

          &::after {
            height: 14px;
            left: 10px;
            top: 5px;
            width: 8px;
            border-width: 2px;
          }
        }

        .el-checkbox__label {
          padding-left: 0;
          font-size: 20px;
          color: #fff;
        }

        &.is-checked {
          z-index: 2;
          color: #fff;
          opacity: 1;
        }

        .el-checkbox__input.is-checked + .el-checkbox__label {
          color: #fff;
        }
      }

      &:hover {
        .el-checkbox,
        .mask {
          opacity: 1;
        }
      }
    }

    .demo-pic-info {
      height: 182px;
      color: #666;

      p {
        line-height: 18px;
        margin-bottom: 8px;

        .t {
          display: inline-block;
          font-weight: bold;
        }

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }
}

//单选框组标题
.m-radio-group-title {
  width: 98px;
  margin-right: 10px;
  line-height: 36px;
  text-align: right;
}

//loading样式
.m-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .txt {
    color: #666;
  }
}

// 专题管理登录
.m-login-special {
  height: 100%;
  width: 100%;
  background: #f5f7fc url('../assets/images/login_special.jpg') no-repeat center top;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: space-between;
  overflow: auto;

  .wrap-bd {
    width: 1200px;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    transition: all 0.4s;
    position: relative;
    margin-bottom: 30px;

    .m-logo {
      font-size: 36px;
      font-weight: bold;
      padding: 180px 0 50px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #fff;
    }
  }

  .m-login-footer {
    width: 100%;
    padding: 20px 0;
    color: #b4bac6;
    font-size: 14px;
    text-align: center;
    height: 100px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    align-items: center;

    p {
      display: block;
      margin: 0;
      padding: 0;
    }

    a {
      color: #b4bac6;

      &:hover {
        text-decoration: underline;
      }
    }

    .img {
      vertical-align: middle;
      margin-right: 5px;
    }
  }

  .m-login {
    width: 540px;
    margin: 0 auto;
    padding: 50px 100px;
    box-sizing: border-box;
    background-color: #fff;
    border-radius: 20px;
    box-shadow: 0 0 35px rgba(118, 136, 192, 0.2);

    .title {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 24px;

      .item {
        font-size: 24px;
        text-align: center;
        position: relative;
        height: 38px;
        cursor: pointer;

        &::before {
          content: "";
          display: none;
          width: 60px;
          position: absolute;
          left: 50%;
          margin-left: -30px;
          bottom: -6px;
          background-color: #174ee6;
          height: 4px;
          border-radius: 2px;
        }

        &.z-cur {
          &::before {
            display: block;
          }
        }
      }

      .item + .item {
        margin-left: 50px;
      }
    }

    .m-form {
      margin-top: 30px;

      .code {
        height: 48px;
      }
    }

    .el-tabs__item {
      height: 60px;
      line-height: 60px;
      font-size: 18px;
      padding: 0;

      &.is-active {
        font-weight: bold;
      }
    }

    .el-form-item {
      margin-bottom: 28px;

      &.op {
        margin-top: -9px;
        margin-bottom: 8px;

        a:hover {
          color: #174ee6;
        }
      }
    }

    .el-input__inner {
      height: 48px;
      line-height: 48px;
      padding-left: 40px;
      border-radius: 10px;
      border-color: #d2d7e4;
      font-size: 16px;
    }

    .el-input__inner:focus {
      border-color: #174ee6;
    }

    .el-input__prefix {
      color: #dcdfe6;
      left: 14px;
      top: 2px;

      .iconfont,
      .hb-iconfont {
        font-size: 18px;
      }
    }

    .el-button {
      border-radius: 10px;
    }

    .el-button--primary {
      background-color: #174ee6;
      border-color: #174ee6;

      &.is-plain {
        color: #4671e7;
        background: #f4f7ff;
        border-color: #c6d4fb;

        &:hover {
          background-color: #174ee6;
          border-color: #174ee6;
          color: #fff;
        }
      }
    }

    .el-checkbox__input.is-checked .el-checkbox__inner {
      border-color: #174ee6;
      background-color: #174ee6;
    }

    .el-checkbox__input.is-checked + .el-checkbox__label {
      color: #174ee6;
    }

    .el-checkbox__inner:hover {
      border-color: #174ee6;
    }

    .el-form-item__error {
      left: 42px;
    }

    .m-btn-bar {
      padding-top: 0;
      margin-bottom: 0;

      .el-button {
        width: 100%;
        height: 48px;
        font-size: 15px;
        border: none;
        background: linear-gradient(to right, #174ee5, #3c4ded);
        box-shadow: 0 8px 16px rgba(23, 78, 229, 0.3);

        &:hover {
          opacity: 0.9;
        }
      }

      .el-form-item__content {
        line-height: 1.5;
      }
    }

    .yzm {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      justify-content: space-between;
      flex-direction: row;

      .el-input {
        width: 202px;
      }

      .yzm-pic {
        width: 120px;
        height: 52px;
        margin-left: 10px;
        cursor: pointer;

        .img {
          width: 100%;
          height: 100%;
          display: block;
          border-radius: 10px;
          border: 1px solid #dcdef6;
          box-sizing: border-box;
        }
      }
    }
  }

  .m-forget {
    display: block;
    padding: 30px;
    box-sizing: border-box;
    height: 100%;
    overflow: auto;
  }
}
