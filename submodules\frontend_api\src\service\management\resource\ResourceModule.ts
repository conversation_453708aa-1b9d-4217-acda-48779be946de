import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import CourseFactory from '@api/service/management/resource/course/CourseFactory'
import CoursewareFactory from '@api/service/management/resource/courseware/CoursewareFactory'
import CourseCategoryFactory from '@api/service/management/resource/course-category/CourseCategoryFactory'
import QueryQuestionLibraryFactory from '@api/service/management/resource/question-library/QueryQuestionLibraryFactory'
import MutationQuestionLibraryFactory from '@api/service/management/resource/question-library/MutationQuestionLibraryFactory'
import QueryExamPaperCategoryFactory from '@api/service/management/resource/exam-paper-category/QueryExamPaperCategoryFactory'
import MutationExamPaperCategoryFactory from '@api/service/management/resource/exam-paper-category/MutationExamPaperCategoryFactory'
import CoursePackageFactory from '@api/service/management/resource/course-package/CoursePackageFactory'
import CoursewareCategoryFactory from '@api/service/management/resource/courseware-category/CoursewareCategoryFactory'
import QueryQuestionFactory from './question/QueryQuestionFactory'
import MutationQuestionFactory from './question/MutationQuestionFactory'
import MutationExamPaperFactory from './exam-paper/MutationExamPaperFactory'
import QueryExamPaperFactory from './exam-paper/QueryExamPaperFactory'
@Module({
  name: 'ManagementResourceModule',
  dynamic: true,
  namespaced: true,
  store
})
class ResourceModule extends VuexModule {
  /**
   * 课程工厂
   */
  courseFactory: CourseFactory = new CourseFactory()

  /**
   * 课程分类工厂
   */
  courseCategoryFactory: CourseCategoryFactory = new CourseCategoryFactory()

  /**
   * 课件工厂
   */
  coursewareFactory: CoursewareFactory = new CoursewareFactory()

  /**
   * 课件分类工厂
   */
  coursewareCategoryFactory: CoursewareCategoryFactory = new CoursewareCategoryFactory()

  coursePackageFactory: CoursePackageFactory = new CoursePackageFactory()

  /**
   * 试题查询工厂
   */
  get queryQuestionFactory() {
    return QueryQuestionFactory
  }

  /**
   * 试题业务工厂
   */
  get mutationQuestionFactory() {
    return MutationQuestionFactory
  }

  /**
   * @description: 题库查询工厂
   */
  get queryQuestionLibraryFactory() {
    return QueryQuestionLibraryFactory
  }

  /**
   * @description: 题库业务工厂
   */
  get mutationQuestionLibraryFactory() {
    return MutationQuestionLibraryFactory
  }

  /**
   * @description: 试卷分类查询工厂
   * @param {*}
   * @return {*}
   */
  get queryExamPaperCategoryFactory() {
    return QueryExamPaperCategoryFactory
  }

  /**
   * @description: 试卷分类业务工厂
   * @param {*}
   * @return {*}
   */
  get mutationExamPaperCategoryFactory() {
    return MutationExamPaperCategoryFactory
  }

  /**
   * @description: 试卷查询工厂
   */
  get queryExamPaperFactory() {
    return QueryExamPaperFactory
  }

  /**
   * @description: 试卷业务工厂
   */
  get mutationExamPaperFactory() {
    return MutationExamPaperFactory
  }
}

export default getModule(ResourceModule)
