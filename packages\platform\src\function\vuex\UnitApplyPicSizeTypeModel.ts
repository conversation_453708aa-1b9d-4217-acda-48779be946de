import { getModule, Module, VuexModule, Mutation, Action } from 'vuex-module-decorators'
import store from '@/store'
@Module({
  name: 'UnitApplyPicSizeTypeModel',
  dynamic: true,
  namespaced: true,
  store
})
class UnitApplyPicSizeTpyeModel extends VuexModule {
  /**
   * 线下报名图片启用初始值
   */
  originOfflinePicEnale = false
  /**
   * 线上报名图片启用初始值
   */
  originOnlinePicEnable = false
  /**
   * 线下报名图片启用
   */
  offlinePicEnable = false
  /**
   * 线上报名图片启用
   */
  onlinePicEnable = false
  /**
   * 线下报名图片是否存在
   */
  offlinePicExist = false
  /**
   * 线上报名图片是否存在
   */
  onlinePicExist = false
  /**
   * 判断是否是单入口
   */
  get isSingle() {
    return (this.offlinePicEnable && !this.onlinePicEnable) || (!this.offlinePicEnable && this.onlinePicEnable)
  }
  /**
   * 判断线下报名图片尺寸是否发生改变
   */
  get isOfflinePicChange() {
    return this.onlinePicExist && this.onlinePicEnable && this.offlinePicEnable !== this.originOfflinePicEnale
  }
  /**
   * 判断线上报名图片尺寸是否发生改变
   */
  get isOnlinePicChange() {
    return this.offlinePicExist && this.offlinePicEnable && this.onlinePicEnable !== this.originOnlinePicEnable
  }
  /**
   * @param value 设置线下报名图片启用初始值
   */
  @Mutation
  setOriginOfflinePicEnable(value: boolean) {
    this.originOfflinePicEnale = value
    this.offlinePicEnable = value
  }
  /**
   *设置线上报名图片启用初始值
   * @param value
   */
  @Mutation
  setOriginOnlinePicEnable(value: boolean) {
    this.originOnlinePicEnable = value
    this.onlinePicEnable = value
  }
  /**
   * 设置线下报名图片启用
   * @param value
   */
  @Mutation
  setOfflinePicEnable(value: boolean) {
    this.offlinePicEnable = value
  }
  /**
   * 设置线上报名图片启用
   * @param value
   */
  @Mutation
  setOnlinePicEnable(value: boolean) {
    this.onlinePicEnable = value
  }
  /**
   * 设置线下报名图片是否存在
   * @param value
   */
  @Mutation
  setOfflinePicExist(value: boolean) {
    this.offlinePicExist = value
  }
  /**
   * 设置线上报名图片是否存在
   * @param value
   */
  @Mutation
  setOnlinePicExist(value: boolean) {
    this.onlinePicExist = value
  }
}
export default getModule(UnitApplyPicSizeTpyeModel)
