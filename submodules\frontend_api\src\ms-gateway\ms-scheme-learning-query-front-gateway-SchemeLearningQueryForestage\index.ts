import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-scheme-learning-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortPolicy {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum StudentSchemeLearningSortField {
  REGISTER_TIME = 'REGISTER_TIME',
  SCHEME_YEAR = 'SCHEME_YEAR'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class DateScopeRequest {
  begin?: string
  end?: string
}

export class DoubleScopeRequest {
  begin?: number
  end?: number
}

export class CountSignInRecordRequest {
  /**
   * 期数参训资格id
   */
  qualificationId?: string
  /**
   * 是否有效 不传默认查询有效的
   */
  isInvalid?: boolean
}

/**
 * 获取我的班级请求参数
 */
export class MySchemeConfigRequest {
  /**
   * 参训资格id
   */
  qualificationId?: string
  /**
   * 是否剔除失效大纲id   用于班级修改大纲时  显示失效的大纲信息
   */
  isIncludeUnableOutlineId: boolean
}

/**
 * 获取我的班级请求参数
 */
export class SchemeConfigRequest {
  /**
   * 方案id
   */
  schemeId?: string
  /**
   * 方案id集合
   */
  schemeIds?: Array<string>
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 是否包含失效大纲id   用于班级修改大纲时  显示失效的大纲信息
   */
  isIncludeUnableOutlineId?: boolean
  /**
   * 是否包含已配置监管方案
   */
  isIncludeHasAntiScheme?: boolean
  /**
   * 培训形式
   */
  trainType?: string
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 监管配置id
   */
  antiConfigId?: string
}

export class SchemeIssueConfigRequest {
  /**
   * 方案idList
   */
  schemeIdList?: Array<string>
  /**
   * 期别idList
   */
  issueIdList?: Array<string>
  /**
   * 期别编号
   */
  issueNum?: string
  /**
   * 期别名称
   */
  issueName?: string
}

/**
 * 查询当前网校指定期别下的教学计划项请求参数
 */
export class SchemeIssuePlanItemRequest {
  /**
   * 期别id
   */
  issueId?: string
  /**
   * 教学开始时间
   */
  startTime?: string
  /**
   * 教学结束时间
   */
  endTime?: string
}

/**
 * @Description
@Date 2025/4/22
 */
export class StudentReportRecordRequest {
  /**
   * 报告记录ID
   */
  id?: string
  /**
   * 数据归属平台ID
   */
  platformId?: string
  /**
   * 数据归属平台版本ID
   */
  platformVersionId?: string
  /**
   * 数据归属项目ID
   */
  projectId?: string
  /**
   * 数据归属子项目ID
   */
  subProjectId?: string
  /**
   * 数据归属单位ID
   */
  unitId?: string
  /**
   * 数据归属服务商ID
   */
  servicerId?: string
  /**
   * 平台租户ID
   */
  tenantId?: string
  /**
   * 规则ID
   */
  ruleId?: string
  /**
   * 参训资格（期别方案参训资格）
   */
  qualificationId?: string
  /**
   * 学员学号
   */
  studentNo?: string
  /**
   * 学员用户ID
   */
  userId?: string
  /**
   * 学习方案ID（冗余）
   */
  schemeId?: string
  /**
   * 期别ID（冗余）
   */
  issueId?: string
  /**
   * 拥有者类型(1.SCHEME学习方案 2.STAGE期别（作废） 3.ISSUSE期别（原标期数） 4.LEARNING学习方式)
@see OwnerTypes
   */
  ownerType?: number
  /**
   * 拥有者ID
   */
  ownerId?: string
  /**
   * 报到时间
   */
  checkInTime?: string
  /**
   * 用户id集合
   */
  userIdList?: Array<string>
}

/**
 * 方案配置查询条件
<AUTHOR>
@version 1.0
@date 2022/1/17 10:50
 */
export class StudentSchemeConfigRequest {
  /**
   * 学员id列表
   */
  qualificationIdList?: Array<string>
}

/**
 * 学员培训方案学习查询条件
<AUTHOR>
@version 1.0
@date 2022/1/17 11:40
 */
export class StudentSchemeLearningRequest {
  /**
   * 学号
   */
  studentNoList?: Array<string>
  /**
   * 学员信息
   */
  student?: UserRequest
  /**
   * 报名信息
   */
  learningRegister?: LearningRegisterRequest
  /**
   * 培训班信息
   */
  scheme?: SchemeRequest
  /**
   * 学习信息
   */
  studentLearning?: StudentLearningRequest
  /**
   * 数据分析信息
   */
  dataAnalysis?: DataAnalysisRequest
  /**
   * 对接管理系统
   */
  connectManageSystem?: ConnectManageSystemRequest
  /**
   * 扩展信息
   */
  extendedInfo?: ExtendedInfoRequest
  /**
   * 方案是否提供培训证明
   */
  openPrintTemplate?: boolean
  /**
   * 是否来源于专题 是专题时 saleChannels 传 2 ， 否时传 0,1
   */
  saleChannels?: Array<number>
  /**
   * 专题名称
   */
  trainingChannelName?: string
  /**
   * 专题Id  用于不同专题域名 查询对应专题的培训班
   */
  trainingChannelId?: string
  /**
   * 非分销门户
   */
  notDistributionPortal?: boolean
  /**
   * 培训形式
trainingWay0001：网授
trainingWay0002：面网授
trainingWay0003：面授
如需使用培训形式条件请使用SKU 此参数无效，即将删除
   */
  trainingType?: string
  /**
   * 期别id
   */
  issueId?: string
}

/**
 * 学员方案学习查询排序条件
 */
export class StudentSchemeLearningSortRequest {
  /**
   * 需要排序的字段
   */
  field?: StudentSchemeLearningSortField
  /**
   * 正序或倒序
   */
  policy?: SortPolicy
}

export class TrainingPointRequest {
  /**
   * 培训地点id
   */
  ids?: Array<string>
  /**
   * 地点名称
   */
  name?: string
  /**
   * 是否被期别引用状态
   */
  isReferencedByIssue?: boolean
  /**
   * 是否启用
   */
  enabled?: boolean
  /**
   * 地点名称查询类型、不传默认EQUALS
@see SearchPattern
LIKE 模糊查询
EQUALS 精确查询
   */
  nameSearchPattern?: string
}

/**
 * @version: 1.0
@description: 对接管理系统
@author: sugs
@create: 2022-11-15 11:27
 */
export class ConnectManageSystemRequest {
  /**
   * 同步状态
@see SyncStatus
0 未同步
1 已同步
2 同步失败
-1 不同步
   */
  syncStatus?: number
}

/**
 * 数据分析信息
<AUTHOR>
@version 1.0
@date 2022/1/20 15:14
 */
export class DataAnalysisRequest {
  /**
   * 成果配置可获得学时
   */
  trainingResultPeriod?: DoubleScopeRequest
  /**
   * 考核要求学时
   */
  requirePeriod?: DoubleScopeRequest
  /**
   * 已获得总学时
   */
  acquiredPeriod?: DoubleScopeRequest
}

export class ExtendedInfoRequest {
  /**
   * 是否打印
true 打印 false 未打印
   */
  whetherToPrint?: boolean
  /**
   * 申报单位统一社会信用代码
@see com.fjhb.ms.scheme.learning.query.constants.ExtendedInfoType#APPLY_COMPANY_CODE
   */
  applyCompanyCode?: string
  /**
   * 政策培训方案id
@see com.fjhb.ms.scheme.learning.query.constants.ExtendedInfoType#POLICY_TRAINING_SCHEME_ID
   */
  policyTrainingSchemeId?: string
  /**
   * 政策培训方案名称
网关层转id再传递到业务层
   */
  policyTrainingSchemeName?: string
}

/**
 * 学员学习报名信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:08
 */
export class LearningRegisterRequest {
  /**
   * 报名方式
<p>
1:学员自主报名
2:集体报名
3:管理员导入
@see StudentRegisterTypes
   */
  registerType?: number
  /**
   * 报名来源类型(ORDER：订单 SUB_ORDER：子订单 EXCHANGE_ORDER：换货单)
@see StudentSourceTypes
   */
  sourceType?: string
  /**
   * 报名来源ID
   */
  sourceId?: string
  /**
   * 学员状态(1:正常 2：冻结 3：失效)
@see StudentStatus
   */
  status?: Array<number>
  /**
   * 报名时间
   */
  registerTime?: DateScopeRequest
  /**
   * 订单销售渠道
   */
  saleChannels?: Array<number>
  /**
   * 来源订单号
   */
  orderNoList?: Array<string>
  /**
   * 来源子订单号
   */
  subOrderNoList?: Array<string>
  /**
   * 来源批次单号
   */
  batchOrderNoList?: Array<string>
  /**
   * 分销商id
   */
  distributorId?: string
  /**
   * 分销门户id
   */
  portalId?: string
}

/**
 * 地区模型
<AUTHOR>
@version 1.0
@date 2022/2/27 20:01
 */
export class RegionRequest {
  /**
   * 地区：省
   */
  province?: string
  /**
   * 地区：市
   */
  city?: string
  /**
   * 地区：区
   */
  county?: string
}

/**
 * 学员学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class StudentLearningRequest {
  /**
   * 培训结果
<p>
-1:未知，培训尚未完成
1:培训合格
0:培训不合格
@see StudentTrainingResults
   */
  trainingResultList?: Array<number>
  /**
   * 培训结果时间
   */
  trainingResultTime?: DateScopeRequest
  /**
   * 无需学习的学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 练习学习方式
4：自主学习课程学习方式
@see LearningTypes
   */
  notLearningTypeList?: Array<number>
  /**
   * 课程学习状态（0：未学习 1：学习中 2：学习完成）
@see StudyStatus
   */
  courseScheduleStatus?: number
  /**
   * 考试结果（-1：未考核 0：不合格 1：合格）
@see AssessCalculateResults
   */
  examAssessResultList?: Array<number>
}

/**
 * 地区sku属性查询条件
<AUTHOR>
@version 1.0
@date 2022/2/25 10:55
 */
export class RegionSkuPropertyRequest {
  /**
   * 地区: 省
   */
  province?: string
  /**
   * 地区: 市
   */
  city?: string
  /**
   * 地区: 区县
   */
  county?: string
}

/**
 * 地区匹配查询
<AUTHOR>
@version 1.0
@date 2022/2/25 14:19
 */
export class RegionSkuPropertySearchRequest {
  /**
   * 地区
   */
  region?: Array<RegionSkuPropertyRequest>
  /**
   * 地区匹配条件
<p>
ALL完全匹配：查询结果返回的地区与查询条件给出的地区完全一致才会返回，如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
PART部分匹配：查询结果返回的地区与查询条件有给值的地区就会返回 如查询条件：给省350000市350100没给区县，返回结果：返回福州市及福州市下所有的地区的数据
@see RegionSearchType
   */
  regionSearchType?: number
}

/**
 * 培训方案信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class SchemeRequest {
  /**
   * 培训方案id
   */
  schemeId?: string
  /**
   * 培训方案id
   */
  schemeIdList?: Array<string>
  /**
   * 培训方案类型
<p>
chooseCourseLearning: 选课规则
autonomousCourseLearning: 自主选课
@see SchemeType
   */
  schemeType?: string
  /**
   * 方案名称
   */
  schemeName?: string
  /**
   * 培训属性
   */
  skuProperty?: SchemeSkuPropertyRequest
}

/**
 * 培训属性
<AUTHOR>
@version 1.0
@date 2022/1/17 10:22
 */
export class SchemeSkuPropertyRequest {
  /**
   * 年度
   */
  year?: Array<string>
  /**
   * 地区
   */
  regionSkuPropertySearch?: RegionSkuPropertySearchRequest
  /**
   * 行业
   */
  industry?: Array<string>
  /**
   * 科目类型
   */
  subjectType?: Array<string>
  /**
   * 培训类别
   */
  trainingCategory?: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional?: Array<string>
  /**
   * 技术等级
   */
  technicalGrade?: Array<string>
  /**
   * 岗位类别
   */
  positionCategory?: Array<string>
  /**
   * 培训对象
   */
  trainingObject?: Array<string>
  /**
   * 技术等级
   */
  jobLevel?: Array<string>
  /**
   * 工种
   */
  jobCategory?: Array<string>
  /**
   * 科目
   */
  subject?: Array<string>
  /**
   * 年级
   */
  grade?: Array<string>
  /**
   * 学段
   */
  learningPhase?: Array<string>
  /**
   * 学科
   */
  discipline?: Array<string>
  /**
   * 资质类别
   */
  qualificationCategory?: Array<string>
  /**
   * 培训类型 （网授、面授）
   */
  trainingWay?: Array<string>
  /**
   * 培训机构
   */
  trainingInstitution?: Array<string>
  /**
   * 主项/增项
   */
  mainAdditionalItem?: Array<string>
}

/**
 * 用户属性
<AUTHOR>
@version 1.0
@date 2022/1/15 11:01
 */
export class UserPropertyRequest {
  /**
   * 所属地区路径
   */
  regionList?: Array<RegionRequest>
  /**
   * 工作单位名称
   */
  companyName?: string
  /**
   * 下单地区
   */
  payOrderRegionList?: Array<RegionRequest>
}

/**
 * 用户信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:00
 */
export class UserRequest {
  /**
   * 用户id
   */
  userIdList?: Array<string>
  /**
   * 账户id
   */
  accountIdList?: Array<string>
  /**
   * 用户属性
   */
  userProperty?: UserPropertyRequest
}

/**
 * 证书模板
<AUTHOR>
 */
export class CertificateTemplateResponse {
  /**
   * 模板id
   */
  id: string
  /**
   * 平台id
   */
  platformId: string
  /**
   * 平台版本id
   */
  platformVersionId: string
  /**
   * 项目id
   */
  projectId: string
  /**
   * 子项目id
   */
  subProjectId: string
  /**
   * 单位id
   */
  unitId: string
  /**
   * 服务商id
   */
  servicerId: string
  /**
   * 模板名称
   */
  name: string
  /**
   * 模板说明
   */
  describe: string
  /**
   * 所属行业id
   */
  belongsIndustryId: string
  /**
   * 使用范围
   */
  usableRange: string
  /**
   * 适用培训方案形式
   */
  suitableSchemeType: string
  /**
   * html模板地址
   */
  url: string
  /**
   * 预览html模板地址
   */
  previewUrl: string
  /**
   * 打印快照数据源
   */
  printSnapShotDataSource: string
  /**
   * 是否应用电子章
   */
  provideElectronicSeal: boolean
  /**
   * 电子章数据源
   */
  electronicDataSource: string
  /**
   * 0不合并 1合并
   */
  isMerge: string
  /**
   * 模板尺寸
   */
  size: string
  /**
   * 是否提供防伪二维码
   */
  provideAntiBogusQRCode: boolean
  /**
   * 防伪二维码id
   */
  AntiBogusQRCodeId: string
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 是否可用
   */
  available: boolean
}

/**
 * <AUTHOR>
对接管理系统
@date 2022/11/15 14:40
 */
export class ConnectManageSystemResponse {
  /**
   * 同步状态
@see SyncStatus
0 未同步
1 已同步
2 同步失败
3 待推送
   */
  syncStatus: number
  /**
   * 同步信息
   */
  syncMessage: string
}

/**
 * 期别配置Response
 */
export class IssueConfigureInfoResponse {
  /**
   * 期别id
   */
  issueId: string
  /**
   * 数据归属平台id
   */
  platformId: string
  /**
   * 数据归属平台版本id
   */
  platformVersionId: string
  /**
   * 数据归属项目id
   */
  projectId: string
  /**
   * 数据归属子项目id
   */
  subProjectId: string
  /**
   * 数据归属单位id
   */
  unitId: string
  /**
   * 数据归属服务id
   */
  servicerId: string
  /**
   * 平台租户id
   */
  tenantId: string
  /**
   * 期别分类ID
   */
  issueCategoryId: string
  /**
   * 期别编号
   */
  issueNum: string
  /**
   * 学习方案id
   */
  learningSchemeId: string
  /**
   * 期别名称
   */
  issueName: string
  /**
   * 报到开始时间
   */
  startReportTimePeriod: string
  /**
   * 报到结束时间
   */
  endReportTimePeriod: string
  /**
   * 报名开始时间
   */
  startSignUpTime: string
  /**
   * 报名结束时间
   */
  endSignUpTime: string
  /**
   * 允许报名人数
   */
  alowSignUpNum: number
  /**
   * 已报名人数显示方式 0.实际人数 1.固定人数
   */
  signUpNumRevealType: number
  /**
   * 固定显示报名人数
   */
  fixedSignUpRevealNum: number
  /**
   * 是否门户展示
   */
  portalDisplay: boolean
  /**
   * 是否开放报名
   */
  openSignUp: boolean
  /**
   * 培训须知
   */
  trainingNotice: string
  /**
   * 教学地点id
   */
  planAddressId: string
  /**
   * 创建用户id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
  /**
   * 培训开始时间
   */
  startTrainingTime: string
  /**
   * 培训结束时间
   */
  endTrainingTime: string
  /**
   * 线下期别课表类型（有课表、无课表）
   */
  offlineIssueCourseType: boolean
  /**
   * 课程培训学时（冗余）
   */
  trainingPeriod: number
  /**
   * 已报名人数
   */
  signUpNum: number
}

/**
 * 分页查询当前网校指定期别下的教学计划项Response
 */
export class IssuePlanItemResponse {
  /**
   * 教学计划项id
   */
  id: string
  /**
   * 数据归属平台id
   */
  platformId: string
  /**
   * 数据归属平台版本id
   */
  platformVersionId: string
  /**
   * 数据归属项目id
   */
  projectId: string
  /**
   * 数据归属子项目id
   */
  subProjectId: string
  /**
   * 数据归属单位id
   */
  unitId: string
  /**
   * 数据归属服务id
   */
  servicerId: string
  /**
   * 平台租户id
   */
  tenantId: string
  /**
   * 教学计划id
   */
  planId: string
  /**
   * 教学计划组Id
   */
  planItemGroupId: string
  /**
   * 教学计划项id
   */
  name: string
  /**
   * 简介
   */
  abouts: string
  /**
   * 教学模式 1 线下教学接话 2 线上教学计划 3 直播教学计划 10 混合/多样教学计划
   */
  planMode: number
  /**
   * 相关教学人员集合
   */
  teachers: Array<PlanTeacherResponse>
  /**
   * 时长(秒)
   */
  timeLength: number
  /**
   * 学时（课时）
   */
  period: number
  /**
   * 教学计划项类型 101 专业基础课程  102 操作技能课程
   */
  planItemType: number
  /**
   * 教学开始时间
   */
  startTime: string
  /**
   * 教学结束时间
   */
  endTime: string
  /**
   * 教学地点
   */
  address: string
  /**
   * 状态 1 正常
   */
  status: number
  /**
   * 状态备注
   */
  statusRemark: string
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人id
   */
  createdUserId: string
  /**
   * 修改时间
   */
  updateTime: string
  /**
   * 课程名称
   */
  courseName: string
}

/**
 * 期别学习配置信息
 */
export class IssueStudyConfigResponse {
  /**
   * 期别配置
   */
  schemeIssueResponse: SchemeIssueResponse
  /**
   * 参训配置
   */
  trainingConfigResponse: TrainingConfigResponse
  /**
   * 报到规则
   */
  reportRuleResponse: ReportRuleResponse
  /**
   * 考勤配置
   */
  attendanceConfigResponse: AttendanceConfigResponse
  /**
   * 教学资源配置
   */
  teachResourceConfigResponse: TeachResourceConfigResponse
}

/**
 * 相关教学人员集合
 */
export class PlanTeacherResponse {
  /**
   * 教学人员ID
   */
  id: string
  /**
   * 教学计划项Id
   */
  planItemId: string
  /**
   * 教学计划项组Id
   */
  planItemGroupId: string
  /**
   * 教学计划项组Id
   */
  planIdGroup: string
  /**
   * 教学计划Id
   */
  planId: string
  /**
   * 教学人员类型
@see PlanTeacherTypes
   */
  teacherType: number
  /**
   * 教学人员性质
@see PlanTheacherNature
   */
  nature: number
  /**
   * 教学人员名称
   */
  teacherName: string
  /**
   * 教学人员关联ID
   */
  teacherReferenceId: string
  /**
   * 单位名称
   */
  unitName: string
}

/**
 * 最近学习的期别参训资格信息
 */
export class RecentTrainingQualificationResponse {
  /**
   * 期别参训资格ID
   */
  qualificationId: string
  /**
   * 方案参训资格ID
   */
  schemeQualificationId: string
  /**
   * 学号
   */
  studentNo: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 期别ID
   */
  issueId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 培训结果
<p>
-1:未知，培训尚未完成
1:培训合格
0:培训不合格
@see StudentTrainingResults
   */
  trainingResult: number
  /**
   * 学员培训结果取得时间
   */
  trainingResultTime: string
  /**
   * 学员报名来源类型
   */
  sourceType: string
  /**
   * 学员报名来源ID
   */
  sourceId: string
}

/**
 * 方案配置主题模型
<AUTHOR>
@version 1.0
@date 2022/1/6 19:22
 */
export class SchemeConfigResponse {
  /**
   * 培训方案id
   */
  schemeId: string
  /**
   * 数据归属信息
   */
  owner: OwnerResponse
  /**
   * json结构方案配置
   */
  schemeConfig: string
  /**
   * 培训属性
   */
  skuProperty: SchemeSkuPropertyResponse
  /**
   * 学习方案名称
   */
  name: string
  /**
   * 方案是否有配置反作弊
   */
  hasAntiConfig: boolean
  /**
   * 反作弊id
   */
  antiConfigId: string
  /**
   * 简介
   */
  intro: string
}

export class SchemeIssueConfigResponse {
  /**
   * 方案配置
   */
  schemeConfig: SchemeConfigResponse
  /**
   * 期别配置
   */
  issueConfig: Array<SchemeIssueResponse>
}

/**
 * 已报班的培训属性
<AUTHOR>
@version 1.0
@date 2022/1/17 10:22
 */
export class SkuPropertyResponse {
  /**
   * 年度
   */
  year: Array<string>
  /**
   * 地区: 省
   */
  province: Array<string>
  /**
   * 地区: 市
   */
  city: Array<string>
  /**
   * 地区: 区县
   */
  county: Array<string>
  /**
   * 行业
   */
  industry: Array<string>
  /**
   * 科目类型
   */
  subjectType: Array<string>
  /**
   * 培训类别
   */
  trainingCategory: Array<string>
  /**
   * 培训专业
   */
  trainingProfessional: Array<string>
  /**
   * 技术等级
   */
  technicalGrade: Array<string>
  /**
   * 岗位类别
   */
  positionCategory: Array<string>
  /**
   * 培训对象
   */
  trainingObject: Array<string>
  /**
   * 技术等级
   */
  jobLevel: Array<string>
  /**
   * 工种
   */
  jobCategory: Array<string>
  /**
   * 科目
   */
  subject: Array<string>
  /**
   * 年级
   */
  grade: Array<string>
  /**
   * 学段
   */
  learningPhase: Array<string>
  /**
   * 学科
   */
  discipline: Array<string>
  /**
   * 学科
   */
  certificatesType: Array<string>
  /**
   * 学科
   */
  practitionerCategory: Array<string>
  /**
   * 培训机构
   */
  trainingInstitution: Array<string>
  /**
   * 主增项
   */
  mainAdditionalItem: Array<string>
  /**
   * 培训形式
   */
  trainingWay: Array<string>
}

/**
 * @Description
@Date 2025/4/22
 */
export class StudentReportRecordResponse {
  /**
   * 报告记录ID
   */
  id: string
  /**
   * 数据归属平台ID
   */
  platformId: string
  /**
   * 数据归属平台版本ID
   */
  platformVersionId: string
  /**
   * 数据归属项目ID
   */
  projectId: string
  /**
   * 数据归属子项目ID
   */
  subProjectId: string
  /**
   * 数据归属单位ID
   */
  unitId: string
  /**
   * 数据归属服务商ID
   */
  servicerId: string
  /**
   * 平台租户ID
   */
  tenantId: string
  /**
   * 规则ID
   */
  ruleId: string
  /**
   * 参训资格（期别方案参训资格）
   */
  qualificationId: string
  /**
   * 学员学号
   */
  studentNo: string
  /**
   * 学员用户ID
   */
  userId: string
  /**
   * 学习方案ID（冗余）
   */
  schemeId: string
  /**
   * 期别ID（冗余）
   */
  issueId: string
  /**
   * 拥有者类型(1.SCHEME学习方案 2.STAGE期别（作废） 3.ISSUSE期别（原标期数） 4.LEARNING学习方式)
@see OwnerTypes
   */
  ownerType: number
  /**
   * 拥有者ID
   */
  ownerId: string
  /**
   * 报到时间
   */
  checkInTime: string
}

/**
 * 学员方案与期数学习信息（含网授+面网授班）
 */
export class StudentSchemeAndIssueLearningResponse {
  /**
   * 学员方案学习信息
   */
  studentSchemeWithIssueLearningResponse: StudentSchemeWithIssueLearningResponse
  /**
   * 学员期数学习信息
   */
  studentIssueLearning: StudentIssueLearningDetailsResponse
}

/**
 * 学员方案学习主题模型
<AUTHOR>
@version 1.0
@date 2021/12/14 11:41
 */
export class StudentSchemeLearningResponse {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学号id
   */
  studentNo: string
  /**
   * 数据归属信息
   */
  owner: OwnerResponse
  /**
   * 学员信息
   */
  student: UserResponse
  /**
   * 学员学习报名信息
   */
  learningRegister: LearningRegisterResponse
  /**
   * 培训方案信息
   */
  scheme: SchemeResponse
  /**
   * 学员学习信息
   */
  studentLearning: StudentLearningResponse
  /**
   * 数据分析信息
   */
  dataAnalysis: DataAnalysisResponse
  /**
   * 对接管理系统
   */
  connectManageSystem: ConnectManageSystemResponse
  /**
   * 扩展信息
   */
  extendedInfo: ExtendedInfoResponse
  /**
   * 问卷要求份数（纳入考核的份数）
   */
  schemeQuestionnaireRequirementCount: number
  /**
   * 方案问卷不需要考核已提交个数（冗余，用于统计）
   */
  schemeQuestionnaireNoAssessSubmittedCount: number
  /**
   * 方案纳入考核问卷数
   */
  schemeQuestionnaireSubmittedCount: number
  /**
   * 期别名字
   */
  issueName: string
}

/**
 * 集体缴费报名班级统计信息
<AUTHOR>
@version 1.0
@date 2021/12/14 11:41
 */
export class StudentSchemeLearningStatisticsResponse {
  /**
   * 培训方案信息
   */
  scheme: SchemeResponse
  /**
   * 合格人次
   */
  qualifiedCount: number
  /**
   * 未合格人次
   */
  unQualifiedCount: number
}

export class TrainingPointResponse {
  /**
   * 培训地点ID，主键
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 网校服务商ID
   */
  servicerId: string
  /**
   * 平台租户id
   */
  tenantId: string
  /**
   * 地点名称
   */
  name: string
  /**
   * 地点坐标经度
   */
  longitude: number
  /**
   * 地点坐标纬度
   */
  latitude: number
  /**
   * 地点数据（冗余，供地图工具使用）
   */
  specificAddressData: string
  /**
   * 培训点所在地区
   */
  areaPath: string
  /**
   * 培训点所在地区名称
   */
  areaName: RegionResponse
  /**
   * 培训教室
   */
  classRoom: string
  /**
   * 拥有者id
   */
  ownerId: string
  /**
   * 拥有者类型
   */
  ownerType: string
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 创建时间
   */
  createdTime: string
  /**
   * 更新时间
   */
  updatedTime: string
  /**
   * 是否被期别引用状态
   */
  isReferencedByIssue: boolean
}

/**
 * 考勤规则配置
 */
export class AttendanceConfigResponse {
  /**
   * 方案ID
   */
  schemeId: string
  /**
   * 考勤规则配置ID
   */
  settingId: string
  /**
   * 签到配置
   */
  attendancesSignInConfig: AttendancesSignInConfig
  /**
   * 签退配置
   */
  attendancesSignOutConfig: AttendancesSignOutConfig
  /**
   * 拥有者类型
1-学习方案
3-期别
4-学习方式
   */
  ownerType: number
  /**
   * 拥有者ID
   */
  ownerId: string
}

/**
 * 所属集体缴费信息
 */
export class BatchOwnerResponse {
  /**
   * 所属集体缴费单位id
   */
  unitId: string
  /**
   * 所属集体缴费用户id
   */
  userId: string
}

export class ExtendedInfoResponse {
  /**
   * 是否打印
true 打印 false 未打印
   */
  whetherToPrint: boolean
  /**
   * 打印时间
   */
  printTime: string
  /**
   * pdf证书地址
   */
  pdfUrl: string
  /**
   * 证书id
   */
  certificateId: string
  /**
   * 证书编号
   */
  certificateNo: string
}

/**
 * 学员学习报名信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:08
 */
export class LearningRegisterResponse {
  /**
   * 报名方式
<p>
1:学员自主报名
2:集体报名
3:管理员导入
@see StudentRegisterTypes
   */
  registerType: number
  /**
   * 报名来源类型
Order: 订单
@see StudentSourceTypes
   */
  sourceType: string
  /**
   * 报名来源ID
   */
  sourceId: string
  /**
   * 学员方案状态
<p>
1:正常
2:冻结
3:失效
@see StudentStatus
   */
  status: number
  /**
   * 学员状态最后变更时间
   */
  statusChangeTime: string
  /**
   * 报名时间
   */
  registerTime: string
  /**
   * 订单销售渠道
   */
  saleChannel: number
  /**
   * 来源订单号
   */
  orderNo: string
  /**
   * 来源子订单号
   */
  subOrderNo: string
  /**
   * 来源批次单号
   */
  batchOrderNo: string
  /**
   * 失效来源类型
@see StudentFrozenAndInvalidTypes
   */
  frozenAndInvalidSourceType: string
  /**
   * 失效来源id
   */
  frozenAndInvalidSourceId: string
}

/**
 * 数据归属信息
<AUTHOR>
@version 1.0
@date 2022/1/8 17:29
 */
export class OwnerResponse {
  /**
   * 所属平台ID
   */
  platformId: string
  /**
   * 所属平台版本ID
   */
  platformVersionId: string
  /**
   * 所属项目ID
   */
  projectId: string
  /**
   * 所属子项目ID
   */
  subProjectId: string
  /**
   * 所属单位id
   */
  unitId: string
  /**
   * 所属服务商类型
<p>
1：培训机构
2：课件供应商
3：渠道商
4：参训单位
@see ServicerTypes
   */
  servicerType: number
  /**
   * 所属服务商id
   */
  servicerId: string
  /**
   * 所属集体缴费信息
   */
  batchOwner: BatchOwnerResponse
}

/**
 * 报到规则
 */
export class ReportRuleResponse {
  /**
   * 报到规则ID
   */
  reportRuleId: string
  /**
   * 报到规则集
   */
  ruleCollectId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 期别ID
   */
  issueId: string
  /**
   * 教学计划ID
   */
  planId: string
  /**
   * 上午上课时间
   */
  amClassTime: string
  /**
   * 上午上课时间类型
0-同课程
1-指定
   */
  amClassTimeType: number
  /**
   * 下午上课时间
   */
  pmClassTime: string
  /**
   * 下午上课时间类型
0-同课程
1-指定
   */
  pmClassTimeType: number
  /**
   * 报到形式
0-证明
1-签到
   */
  checkInType: number
  /**
   * 证明模板ID
   */
  certificateTemplateId: string
  /**
   * 打卡半径范围（X米）
   */
  signRadiusRange: number
  /**
   * 拥有者类型
1-方案
3-期别
   */
  ownerType: number
  /**
   * 拥有者ID
   */
  ownerId: string
}

export class SchemeIssueResponse {
  /**
   * 期别ID
   */
  issueId: string
  /**
   * 期别分类ID
   */
  issueCategoryId: string
  /**
   * 期别编号
   */
  issueNum: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 期别名称
   */
  issueName: string
  /**
   * 期别总学时 (目前走教学计划，没有教学计划则为null）
   */
  periods: number
  /**
   * 报到开始时间
   */
  startReportTimePeriod: string
  /**
   * 报到结束时间
   */
  endReportTimePeriod: string
  /**
   * 报名开始时间
   */
  startSignUpTime: string
  /**
   * 报名结束时间
   */
  endSignUpTime: string
  /**
   * 培训开始时间
   */
  startTrainTime: string
  /**
   * 培训结束时间
   */
  endTrainTime: string
  /**
   * 线下期别课表类型（有课表、无课表）
   */
  offlineIssueCourseType: boolean
  /**
   * 课程培训学时（冗余）
   */
  trainingPeriod: number
  /**
   * 允许报名人数
   */
  allowSignUpNum: number
  /**
   * 已报名人数显示方式
   */
  signUpNumRevealType: number
  /**
   * 固定显示报名人数
   */
  fixedSignUpRevealNum: number
  /**
   * 是否门户展示
   */
  portalDisplay: boolean
  /**
   * 是否开放报名
   */
  openSignUp: boolean
  /**
   * 教学地点ID
   */
  planAddressId: string
}

/**
 * 学员期数学习
 */
export class StudentIssueLearningDetailsResponse {
  /**
   * 期数参训资格ID
   */
  qualificationId: string
  /**
   * 学员学号
   */
  studentNo: string
  /**
   * 业务学号
   */
  businessStudentNo: string
  /**
   * 关联的用户ID
   */
  userId: string
  /**
   * 关联的学习方案ID
   */
  schemeId: string
  /**
   * 关联的期别ID
   */
  issueId: string
  /**
   * 学员方案培训结果 | -1 -培训中 0-培训未合格 1-培训合格
@see  StudentTrainingResults
   */
  trainingResult: number
  /**
   * 培训合格时间
   */
  trainingResultGainTime: string
  /**
   * 住宿信息
0-无需住宿
1-单住
2-合住
   */
  roomInfo: number
  /**
   * 问卷考核要求提交个数（冗余，用于统计）
   */
  questionnaireRequirementCount: number
  /**
   * 问卷考核已提交个数（冗余，用于统计）
   */
  questionnaireSubmittedCount: number
  /**
   * 成果配置可获得学时（冗余）
   */
  trainingResultPeriod: number
  /**
   * 成果要求学时（冗余）
   */
  requirePeriod: number
  /**
   * 学员方案已获得学时（冗余）
   */
  acquiredPeriod: number
  /**
   * 同步管理系统状态
   */
  syncStatus: number
  /**
   * 同步管理系统信息
   */
  syncMessage: string
  /**
   * 学习方式学习信息
   */
  learning: Array<LearningResponseV2>
  /**
   * 学员期别学习考核指标结果
   */
  userAssessResult: Array<string>
  /**
   * 学员培训成果
   */
  learningResult: Array<LearningResultResponseV2>
}

/**
 * (学员参训资格信息包含学习情况）（含网授+面网授班）
 */
export class StudentSchemeWithIssueLearningResponse {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学员学号
   */
  studentNo: string
  /**
   * 关联的用户ID
   */
  userId: string
  /**
   * 关联的学习方案ID
   */
  schemeId: string
  /**
   * 学员方案培训结果 | -1 -培训中 0-培训未合格 1-培训合格
@see  StudentTrainingResults
   */
  trainingResult: number
  /**
   * 培训合格时间
   */
  trainingResultGainTime: string
  /**
   * 同步管理系统状态
   */
  syncStatus: number
  /**
   * 同步管理系统信息
   */
  syncMessage: string
  /**
   * 问卷考核要求提交个数（冗余，用于统计）
   */
  questionnaireRequirementCount: number
  /**
   * 问卷考核已提交个数（冗余，用于统计）
   */
  questionnaireSubmittedCount: number
  /**
   * 成果配置可获得学时（冗余）
   */
  trainingResultPeriod: number
  /**
   * 成果要求学时（冗余）
   */
  requirePeriod: number
  /**
   * 学员方案已获得学时（冗余）
   */
  acquiredPeriod: number
  /**
   * 学习方式学习信息
   */
  learning: Array<LearningResponseV2>
  /**
   * 学员方案学习考核指标结果
   */
  userAssessResult: Array<string>
  /**
   * 方案下学员培训成果
   */
  learningResult: Array<LearningResultResponseV2>
}

/**
 * 教学资源配置
 */
export class TeachResourceConfigResponse {
  /**
   * 配置ID
   */
  settingId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 教学资源列表
   */
  teachResourceList: Array<TeachResourceResponse>
  /**
   * 拥有者类型
1-学习方案
3-期别
4-学习方式
   */
  ownerType: number
  /**
   * 拥有者ID
   */
  ownerId: string
}

/**
 * 参训配置
 */
export class TrainingConfigResponse {
  /**
   * 配置ID
   */
  trainingConfigId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 是否开启考勤
   */
  openAttendance: boolean
  /**
   * 考勤要求类型
0-自定义
1-同方案要求
   */
  attendanceRequireType: number
  /**
   * 最低考勤率
   */
  minAttendanceRate: number
  /**
   * 是否开启报到
   */
  openReport: boolean
  /**
   * 是否开启结业测试
   */
  openCompletionTest: boolean
  /**
   * 是否开启住宿信息采集
   */
  openStayInfoCollect: boolean
  /**
   * 是否开启问卷考核
   */
  openQuestionnaireAssessment: boolean
  /**
   * 拥有者类型
1-学习方案
3-期别
4-学习方式
   */
  ownerType: number
  /**
   * 拥有者ID
   */
  ownerId: string
}

/**
 * 考勤签到配置
 */
export class AttendancesSignInConfig {
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 签到频率类型
1:半天，2:每节课
   */
  signInFrequencyType: number
  /**
   * 签到地点范围(xxx米)
   */
  signInAddressRadius: number
  /**
   * 预签到时间（第一节课前X分钟）
   */
  preSignTime: number
  /**
   * 后签到时间（开始授课后X分钟）
   */
  postSignTime: number
}

/**
 * 考勤签退配置
 */
export class AttendancesSignOutConfig {
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 签到频率类型
1:半天，2:每节课
   */
  signInFrequencyType: number
  /**
   * 签到地点范围(xxx米)
   */
  signInAddressRadius: number
  /**
   * 预签到时间（第一节课前X分钟）
   */
  preSignTime: number
  /**
   * 后签到时间（开始授课后X分钟）
   */
  postSignTime: number
}

/**
 * 课程学习方式学习信息
<AUTHOR>
@version 1.0
true:@date 2022/1/15 14:08
 */
export class CourseLearningResponse {
  /**
   * 整体课程学习状态
<p>
0:未学习
1:学习中
2:学习完成
@see StudyStatus
   */
  courseScheduleStatus: number
  /**
   * 整体课程完成学习时间
   */
  courseQualifiedTime: string
  /**
   * 已选课程数
   */
  selectedCourseCount: number
  /**
   * 已选课总学时
   */
  selectedCoursePeriod: number
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 自主学习课程学习方式
@see LearningTypes
   */
  learningType: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 学习资源类型
<p>
1: 课程
2: 考试
@see LearningResourceTypes
   */
  learningResourceType: number
  /**
   * 学习资源ID
<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
   */
  learningResourceId: string
  /**
   * 课程学习方式下只提供课程学习方式考核指标结果json结构
如果是在考试学习方式则只提供考核指标结果
扩展方案配置json课程或考试学习方式考核指标部分的json结构
ownerType枚举值
1：学习方案
2：期别
3：期数
4：学习方式
5：课程大纲
json结构如：
[{
assessId: 考核指标id,
ownerType: 拥有者类型,
ownerId: 拥有者id如方案id学习方式id等,
name: 考核要求模板如Scheme_Assess_001,
必修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
},
选修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
}
},{
...(数组结构，允许有过个考核要求配置)
}]
   */
  userAssessResult: Array<string>
  /**
   * 学员培训成果
   */
  learningResult: Array<LearningResultResponse>
}

/**
 * 数据分析信息
<AUTHOR>
@version 1.0
@date 2022/1/20 15:14
 */
export class DataAnalysisResponse {
  /**
   * 成果配置可获得学时
   */
  trainingResultPeriod: number
  /**
   * 考核要求学时
   */
  requirePeriod: number
  /**
   * 已获得总学时
   */
  acquiredPeriod: number
}

/**
 * 考试学习方式学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 14:09
 */
export class ExamLearningResponse {
  /**
   * 是否已考试
   */
  committedExam: boolean
  /**
   * 考试考核计算结果
-1：未考核
1：合格
0：不合格
@see AssessCalculateResults
   */
  examAssessResult: number
  /**
   * 考试合格时间
   */
  examQualifiedTime: string
  /**
   * 考试次数
   */
  examCount: number
  /**
   * 最高成绩
   */
  maxExamScore: number
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 自主学习课程学习方式
@see LearningTypes
   */
  learningType: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 学习资源类型
<p>
1: 课程
2: 考试
@see LearningResourceTypes
   */
  learningResourceType: number
  /**
   * 学习资源ID
<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
   */
  learningResourceId: string
  /**
   * 课程学习方式下只提供课程学习方式考核指标结果json结构
如果是在考试学习方式则只提供考核指标结果
扩展方案配置json课程或考试学习方式考核指标部分的json结构
ownerType枚举值
1：学习方案
2：期别
3：期数
4：学习方式
5：课程大纲
json结构如：
[{
assessId: 考核指标id,
ownerType: 拥有者类型,
ownerId: 拥有者id如方案id学习方式id等,
name: 考核要求模板如Scheme_Assess_001,
必修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
},
选修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
}
},{
...(数组结构，允许有过个考核要求配置)
}]
   */
  userAssessResult: Array<string>
  /**
   * 学员培训成果
   */
  learningResult: Array<LearningResultResponse>
}

/**
 * 考试学习方式学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 14:09
 */
export class LearningExperienceLearningResponse {
  /**
   * 是否已提交学习心得
   */
  committedLearningExperience: boolean
  /**
   * 学习心得考核计算结果
-1：未考核
1：合格
0：不合格
@see AssessCalculateResults
   */
  learningExperienceAssessResult: number
  /**
   * 学习心得合格时间
   */
  learningExperienceQualifiedTime: string
  /**
   * 最高成绩
   */
  maxLearningExperienceScore: number
  /**
   * 心得通过数量  没有考核时使用这个字段
   */
  learningExperiencePassCount: number
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 学习方式类型
<p>
1: 选课学习方式
2: 考试学习方式
3: 自主学习课程学习方式
@see LearningTypes
   */
  learningType: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 学习资源类型
<p>
1: 课程
2: 考试
@see LearningResourceTypes
   */
  learningResourceType: number
  /**
   * 学习资源ID
<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
   */
  learningResourceId: string
  /**
   * 课程学习方式下只提供课程学习方式考核指标结果json结构
如果是在考试学习方式则只提供考核指标结果
扩展方案配置json课程或考试学习方式考核指标部分的json结构
ownerType枚举值
1：学习方案
2：期别
3：期数
4：学习方式
5：课程大纲
json结构如：
[{
assessId: 考核指标id,
ownerType: 拥有者类型,
ownerId: 拥有者id如方案id学习方式id等,
name: 考核要求模板如Scheme_Assess_001,
必修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
},
选修考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
}
},{
...(数组结构，允许有过个考核要求配置)
}]
   */
  userAssessResult: Array<string>
  /**
   * 学员培训成果
   */
  learningResult: Array<LearningResultResponse>
}

/**
 * 学习信息
 */
export class LearningResponseV2 {
  /**
   * 参训资格ID
   */
  qualificationId: string
  /**
   * 学习方式id
   */
  learningId: string
  /**
   * 学习方式类型
@see LearningTypes
   */
  learningType: number
  /**
   * 是否启用
   */
  enabled: boolean
  /**
   * 是否已删除
   */
  deleted: boolean
  /**
   * 学习资源类型
@see LearningResourceTypes
   */
  learningResourceType: number
  /**
   * 学习资源ID
<p>说明：该ID由{@link #learningResourceType 学习资源类型}决定</p>
   */
  learningResourceId: string
  /**
   * 学习方式参与状态 | -1 未参与 0-已参与 1-参与完成
@see StudentLearningStatus
   */
  learningStatus: number
  /**
   * 学习考核结果 | -1 未考核 0-未合格 1-已合格
@see StudentLearningTrainingResults
   */
  learningAssessResult: number
  /**
   * 考核指标结果
   */
  userAssessResult: Array<string>
  /**
   * 学员培训成果
   */
  learningResult: Array<LearningResultResponseV2>
  /**
   * 报到信息
学习方式为-教学计划才会有
   */
  reportInfo: ReportInfo
}

/**
 * 学习成果
 */
export class LearningResultResponseV2 {
  /**
   * 学员学习成果id
   */
  studentLearningResultId: string
  /**
   * 学习成果id
   */
  learningResultId: string
  /**
   * 成果拥有者类型
   */
  ownerType: string
  /**
   * 成果拥有者id
   */
  ownerId: string
  /**
   * 学习成果获得时间
   */
  gainedTime: string
  /**
   * 培训成果配置
   */
  learningResultConfig: LearningResultConfigResponse
}

/**
 * 报到信息
 */
export class ReportInfo {
  /**
   * 是否报到
   */
  isReport: boolean
  /**
   * 报到时间
   */
  reportTime: string
}

/**
 * 学员学习信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class StudentLearningResponse {
  /**
   * 培训结果
<p>
-1:未知，培训尚未完成
1:培训合格
0:培训不合格
@see StudentTrainingResults
   */
  trainingResult: number
  /**
   * 取得培训结果时间
   */
  trainingResultTime: string
  /**
   * 课程学习方式学习信息
   */
  courseLearning: CourseLearningResponse
  /**
   * 考试学习方式学习信息
   */
  examLearning: ExamLearningResponse
  /**
   * 学习心得学习方式学习信息
   */
  learningExperienceLearning: LearningExperienceLearningResponse
  /**
   * 方案考核指标结果，json结构（只提供方案部分配置的考核要求）
扩展方案配置json考核指标部分的json结构
ownerType枚举值
1：学习方案
2：期别
3：期数
4：学习方式
5：课程大纲
json结构如：
[{
assessId: 考核指标id,
ownerType: 拥有者类型,
ownerId: 拥有者id如方案id学习方式id等,
name: 考核要求模板如Scheme_Assess_001,
方案考核要求字段名: {
config: 10,//配置值
current: 5//学员当前已获得值
}
},{
...(数组结构，允许有过个考核要求配置)
}]
   */
  userAssessResult: Array<string>
  /**
   * 学员培训成果
   */
  learningResult: Array<LearningResultResponse>
}

/**
 * 证书型型培训成果配置
<AUTHOR>
@version 1.0
@date 2022/1/15 14:30
 */
export class CertificateLearningConfigResultResponse implements LearningResultConfigResponse {
  /**
   * 证书模板ID
   */
  certificateTemplateId: string
  /**
   * 是否开放打印
   */
  openPrintTemplate: boolean
  /**
   * 成果类型
<p>
1：分数型学习成果
2：证书型学习成果
@see LearningResultTypes
   */
  resultType: number
}

/**
 * 分数型培训成果配置
<AUTHOR>
@version 1.0
@date 2022/1/15 14:30
 */
export class GradeLearningConfigResultResponse implements LearningResultConfigResponse {
  /**
   * 分数类型
<p>说明：查询时可以根据相同的分数类型进行累加分数</p>
<p>
CREDIT: 学分
结业测试：IMPORT_GRADE_RESULT
@see LearningResultGradeTypes
   */
  gradeType: string
  /**
   * 分数
当type为结业测试时，1-合格，0-不合格
   */
  grade: number
  /**
   * 成果类型
<p>
1：分数型学习成果
2：证书型学习成果
@see LearningResultTypes
   */
  resultType: number
}

/**
 * 培训成果配置
<AUTHOR>
@version 1.0
@date 2022/1/15 14:30
 */
export interface LearningResultConfigResponse {
  /**
   * 成果类型
<p>
1：分数型学习成果
2：证书型学习成果
@see LearningResultTypes
   */
  resultType: number
}

/**
 * 学员培训成果
<AUTHOR>
@version 1.0
@date 2022/1/15 14:28
 */
export class LearningResultResponse {
  /**
   * 用户成果id
   */
  learningResultId: string
  /**
   * 取得时间
   */
  gainedTime: string
  /**
   * 培训成果配置
   */
  learningResultConfig: LearningResultConfigResponse
}

/**
 * 培训方案信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:25
 */
export class SchemeResponse {
  /**
   * 培训方案id
   */
  schemeId: string
  /**
   * 培训方案类型
<p>
chooseCourseLearning: 选课规则
autonomousCourseLearning: 自主选课
@see SchemeType
   */
  schemeType: string
  /**
   * 培训属性
   */
  skuProperty: SchemeSkuPropertyResponse
  /**
   * 方案名称
   */
  schemeName: string
  /**
   * 学习成果
   */
  learningResult: Array<LearningResultConfigResponse>
}

/**
 * 培训属性
<AUTHOR>
@version 1.0
@date 2022/1/17 10:22
 */
export class SchemeSkuPropertyResponse {
  /**
   * 年度
   */
  year: SchemeSkuPropertyValueResponse
  /**
   * 地区: 省
   */
  province: SchemeSkuPropertyValueResponse
  /**
   * 地区: 市
   */
  city: SchemeSkuPropertyValueResponse
  /**
   * 地区: 区县
   */
  county: SchemeSkuPropertyValueResponse
  /**
   * 行业
   */
  industry: SchemeSkuPropertyValueResponse
  /**
   * 科目类型
   */
  subjectType: SchemeSkuPropertyValueResponse
  /**
   * 培训类别
   */
  trainingCategory: SchemeSkuPropertyValueResponse
  /**
   * 培训专业
   */
  trainingProfessional: SchemeSkuPropertyValueResponse
  /**
   * 技术等级
   */
  technicalGrade: SchemeSkuPropertyValueResponse
  /**
   * 岗位类别
   */
  positionCategory: SchemeSkuPropertyValueResponse
  /**
   * 培训对象
   */
  trainingObject: SchemeSkuPropertyValueResponse
  /**
   * 技术等级
   */
  jobLevel: SchemeSkuPropertyValueResponse
  /**
   * 工种
   */
  jobCategory: SchemeSkuPropertyValueResponse
  /**
   * 科目
   */
  subject: SchemeSkuPropertyValueResponse
  /**
   * 年级
   */
  grade: SchemeSkuPropertyValueResponse
  /**
   * 学段
   */
  learningPhase: SchemeSkuPropertyValueResponse
  /**
   * 学科
   */
  discipline: SchemeSkuPropertyValueResponse
  /**
   * 证书类型
   */
  certificatesType: SchemeSkuPropertyValueResponse
  /**
   * 执业类别
   */
  practitionerCategory: SchemeSkuPropertyValueResponse
  /**
   * 培训机构
   */
  trainingInstitution: SchemeSkuPropertyValueResponse
  /**
   * 主增项
   */
  mainAdditionalItem: SchemeSkuPropertyValueResponse
  /**
   * 培训类型 （网授、面授）
   */
  trainingWay: SchemeSkuPropertyValueResponse
}

/**
 * 培训属性值
<AUTHOR>
@version 1.0
@date 2022/1/17 10:22
 */
export class SchemeSkuPropertyValueResponse {
  /**
   * sku属性值id
   */
  skuPropertyValueId: string
}

/**
 * 教学资源
 */
export class TeachResourceResponse {
  /**
   * 资源名称
   */
  resourceName: string
  /**
   * 资源类型
0-附件
   */
  resourceType: number
  /**
   * 资源内容
   */
  resourceContent: string
}

export class RegionResponse {
  /**
   * 地区：省
   */
  province: string
  /**
   * 地区：市
   */
  city: string
  /**
   * 地区：区
   */
  county: string
}

/**
 * 用户属性
<AUTHOR>
@version 1.0
@date 2022/1/15 11:01
 */
export class UserPropertyResponse {
  /**
   * 所属地区
   */
  region: RegionResponse
  /**
   * 下单地区
   */
  payOrderRegion: RegionResponse
}

/**
 * 用户信息
<AUTHOR>
@version 1.0
@date 2022/1/15 11:00
 */
export class UserResponse {
  /**
   * 用户id
   */
  userId: string
  /**
   * 账户id
   */
  accountId: string
  /**
   * 用户属性
   */
  userProperty: UserPropertyResponse
}

export class IssuePlanItemResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<IssuePlanItemResponse>
}

export class SchemeConfigResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SchemeConfigResponse>
}

export class SchemeIssueConfigResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<SchemeIssueConfigResponse>
}

export class StudentSchemeLearningResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentSchemeLearningResponse>
}

export class StudentSchemeLearningStatisticsResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<StudentSchemeLearningStatisticsResponse>
}

export class TrainingPointResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<TrainingPointResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询证书模板详情
   * @param certificateTemplateId 证书模板id
   * @return 证书模板对象
   * @param query 查询 graphql 语法文档
   * @param certificateTemplateId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getCertificateTemplate(
    certificateTemplateId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getCertificateTemplate,
    operation?: string
  ): Promise<Response<CertificateTemplateResponse>> {
    return commonRequestApi<CertificateTemplateResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { certificateTemplateId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询当前分销商下的期别配置信息（无需鉴权）
   * @param issueId 期别id
   * @return
   * @param query 查询 graphql 语法文档
   * @param issueId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIssueConfigureInfoInDistributor(
    issueId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIssueConfigureInfoInDistributor,
    operation?: string
  ): Promise<Response<IssueConfigureInfoResponse>> {
    return commonRequestApi<IssueConfigureInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { issueId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 查询当前网校下期别配置信息（无需鉴权）
   * @param issueId 期别id
   * @return
   * @param query 查询 graphql 语法文档
   * @param issueId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getIssueConfigureInfoInServicer(
    issueId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIssueConfigureInfoInServicer,
    operation?: string
  ): Promise<Response<IssueConfigureInfoResponse>> {
    return commonRequestApi<IssueConfigureInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { issueId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取我的培训方案配置
   * <p>
   * 在学员中心使用，如果学员还在学习中会始终返回最新的方案配置，
   * 如果学员已培训通过，该接口会返回培训通过之前最新的培训方案配置‘快照’
   * fixme：接口正确命名应该是getSchemeConfigInMySelf，之前确定规范过程中漏改了这个接口，前端已经对接并建议暂时不要改名
   * @param qualificationId
   * @param needField       以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
   * 返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
   * }
   * 如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getMySchemeConfig(
    params: { qualificationId?: string; needField?: Array<string> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getMySchemeConfig,
    operation?: string
  ): Promise<Response<SchemeConfigResponse>> {
    return commonRequestApi<SchemeConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取我的培训方案配置
   * <p>
   * 在学员中心使用，如果学员还在学习中会始终返回最新的方案配置，
   * 如果学员已培训通过，该接口会返回培训通过之前最新的培训方案配置‘快照’
   * fixme：接口正确命名应该是getSchemeConfigInMySelf，之前确定规范过程中漏改了这个接口，前端已经对接并建议暂时不要改名
   * @param request
   * @param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
   * 返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
   * }
   * 如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
   * 8.0通用以及差异化均未使用此接口，以此弃用
   * 可以替换使用：getMySchemeConfigInMySelf接口
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getMySchemeConfigByRequest(
    params: { request?: MySchemeConfigRequest; needField?: Array<string> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getMySchemeConfigByRequest,
    operation?: string
  ): Promise<Response<SchemeConfigResponse>> {
    return commonRequestApi<SchemeConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取期别规则配置信息详情（如果期别没配则则返回方案配置）
   * 无需鉴权
   * todo 报到配置下期做
   * @return
   * @param query 查询 graphql 语法文档
   * @param issueId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getMySchemeIssueConfigInMySelf(
    issueId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getMySchemeIssueConfigInMySelf,
    operation?: string
  ): Promise<Response<IssueStudyConfigResponse>> {
    return commonRequestApi<IssueStudyConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { issueId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询我的最近可报到的期别参训资格
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getRecentTrainingQualificationInMyself(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getRecentTrainingQualificationInMyself,
    operation?: string
  ): Promise<Response<RecentTrainingQualificationResponse>> {
    return commonRequestApi<RecentTrainingQualificationResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getSchemeConfigByRequestInServicer(
    params: { request?: SchemeConfigRequest; needField?: Array<string> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSchemeConfigByRequestInServicer,
    operation?: string
  ): Promise<Response<SchemeConfigResponse>> {
    return commonRequestApi<SchemeConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下的培训方案配置
   * @param schemeId
   * @param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
   * 返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
   * }
   * 如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getSchemeConfigInDistributor(
    params: { schemeId?: string; needField?: Array<string> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSchemeConfigInDistributor,
    operation?: string
  ): Promise<Response<SchemeConfigResponse>> {
    return commonRequestApi<SchemeConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前服务商下的培训方案配置
   * @param schemeId
   * @param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
   * 返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
   * }
   * 如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getSchemeConfigInServicer(
    params: { schemeId?: string; needField?: Array<string> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSchemeConfigInServicer,
    operation?: string
  ): Promise<Response<SchemeConfigResponse>> {
    return commonRequestApi<SchemeConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取当前子项目下的培训方案配置
   * @param schemeId
   * @param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
   * 返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
   * }
   * 如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getSchemeConfigInSubject(
    params: { schemeId?: string; needField?: Array<string> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSchemeConfigInSubject,
    operation?: string
  ): Promise<Response<SchemeConfigResponse>> {
    return commonRequestApi<SchemeConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询当前分销商指定期别下的教学计划项（无需鉴权）
   * @param page
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getSchemeIssuePlanItemInDistributor(
    params: { page?: Page; request?: SchemeIssuePlanItemRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSchemeIssuePlanItemInDistributor,
    operation?: string
  ): Promise<Response<IssuePlanItemResponsePage>> {
    return commonRequestApi<IssuePlanItemResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询当前网校指定期别下的教学计划项（无需鉴权）
   * @param page
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getSchemeIssuePlanItemInServicer(
    params: { page?: Page; request?: SchemeIssuePlanItemRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSchemeIssuePlanItemInServicer,
    operation?: string
  ): Promise<Response<IssuePlanItemResponsePage>> {
    return commonRequestApi<IssuePlanItemResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 根据子订单号获取我的培训方案学习详情
   * @deprecated 弃用：使用pageSchemeLearningInMyself接口并提供来源类型、来源id进行查询。
   * 因为该学员学习不一定是来自子订单，也可能由换货而来的，这时该接口就无法根据子订单号查到换货后的学员方案学习信息
   * @param query 查询 graphql 语法文档
   * @param subOrderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSchemeLearningBySubOrderInMyself(
    subOrderNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSchemeLearningBySubOrderInMyself,
    operation?: string
  ): Promise<Response<StudentSchemeLearningResponse>> {
    return commonRequestApi<StudentSchemeLearningResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { subOrderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询我的学员方案学习详情（含网授+面网授班）
   * @param query 查询 graphql 语法文档
   * @param studentNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSchemeLearningDetailInMyself(
    studentNo: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSchemeLearningDetailInMyself,
    operation?: string
  ): Promise<Response<StudentSchemeAndIssueLearningResponse>> {
    return commonRequestApi<StudentSchemeAndIssueLearningResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { studentNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取我的培训方案学习详情
   * @param query 查询 graphql 语法文档
   * @param qualificationId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSchemeLearningInMyself(
    qualificationId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSchemeLearningInMyself,
    operation?: string
  ): Promise<Response<StudentSchemeLearningResponse>> {
    return commonRequestApi<StudentSchemeLearningResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { qualificationId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取学员打卡记录数
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getStudentPlanSignRecordCountInMyself(
    request: CountSignInRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getStudentPlanSignRecordCountInMyself,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 查询当前网校指定期别下教学计划项教师名称列表（按教师名称去重）
   * @return
   * @param query 查询 graphql 语法文档
   * @param issueId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getTeacherListInServicer(
    issueId: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getTeacherListInServicer,
    operation?: string
  ): Promise<Response<Array<string>>> {
    return commonRequestApi<Array<string>>(
      SERVER_URL,
      {
        query: query,
        variables: { issueId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 获取我批次缴费学员已报班的培训属性列表
   * @param query 查询 graphql 语法文档
   * @param skuProperty 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listSchemeSkuBatchedInMyself(
    skuProperty: SchemeSkuPropertyRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listSchemeSkuBatchedInMyself,
    operation?: string
  ): Promise<Response<SkuPropertyResponse>> {
    return commonRequestApi<SkuPropertyResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { skuProperty },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取我批次缴费学员已报班的培训属性列表
   * @param query 查询 graphql 语法文档
   * @param skuProperty 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listSchemeSkuBatchedInMyselfBTXPXPT(
    skuProperty: SchemeSkuPropertyRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listSchemeSkuBatchedInMyselfBTXPXPT,
    operation?: string
  ): Promise<Response<SkuPropertyResponse>> {
    return commonRequestApi<SkuPropertyResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { skuProperty },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取我已报班的培训属性列表
   * @param query 查询 graphql 语法文档
   * @param skuProperty 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listSchemeSkuForStudentInMyself(
    skuProperty: SchemeSkuPropertyRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listSchemeSkuForStudentInMyself,
    operation?: string
  ): Promise<Response<SkuPropertyResponse>> {
    return commonRequestApi<SkuPropertyResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { skuProperty },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listStudentReportRecordInMyself(
    request: StudentReportRecordRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listStudentReportRecordInMyself,
    operation?: string
  ): Promise<Response<Array<StudentReportRecordResponse>>> {
    return commonRequestApi<Array<StudentReportRecordResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取方案配置
   * @param page
   * @param request
   * @param needField
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSchemeConfigByRequestInServicer(
    params: { page?: Page; request?: SchemeConfigRequest; needField?: Array<string> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSchemeConfigByRequestInServicer,
    operation?: string
  ): Promise<Response<SchemeConfigResponsePage>> {
    return commonRequestApi<SchemeConfigResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取方案配置
   * @param page
   * @param schemeIds
   * @param needField
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSchemeConfigInDistributor(
    params: { page?: Page; schemeIds?: Array<string>; needField?: Array<string> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSchemeConfigInDistributor,
    operation?: string
  ): Promise<Response<SchemeConfigResponsePage>> {
    return commonRequestApi<SchemeConfigResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取方案配置
   * @param page
   * @param schemeIds
   * @param needField
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSchemeConfigInServicer(
    params: { page?: Page; schemeIds?: Array<string>; needField?: Array<string> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSchemeConfigInServicer,
    operation?: string
  ): Promise<Response<SchemeConfigResponsePage>> {
    return commonRequestApi<SchemeConfigResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询当前分销商下期别配置列表（无需鉴权）
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSchemeIssueConfigListInDistributor(
    params: { page?: Page; request?: SchemeIssueConfigRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSchemeIssueConfigListInDistributor,
    operation?: string
  ): Promise<Response<SchemeIssueConfigResponsePage>> {
    return commonRequestApi<SchemeIssueConfigResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询当前网校下期别配置列表（无需鉴权）
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageSchemeIssueConfigListOptionalLoginInServicer(
    params: { page?: Page; request?: SchemeIssueConfigRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSchemeIssueConfigListOptionalLoginInServicer,
    operation?: string
  ): Promise<Response<SchemeIssueConfigResponsePage>> {
    return commonRequestApi<SchemeIssueConfigResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页获取我的培训方案学习列表
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageSchemeLearningInMyself(
    params: { page?: Page; request?: StudentSchemeLearningRequest; sort?: Array<StudentSchemeLearningSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageSchemeLearningInMyself,
    operation?: string
  ): Promise<Response<StudentSchemeLearningResponsePage>> {
    return commonRequestApi<StudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 集体缴费管理员下培训方案学员学习情况统计
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStatisticsStudentSchemeLearningInCollective(
    params: { page?: Page; request?: StudentSchemeLearningRequest; sort?: Array<StudentSchemeLearningSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStatisticsStudentSchemeLearningInCollective,
    operation?: string
  ): Promise<Response<StudentSchemeLearningStatisticsResponsePage>> {
    return commonRequestApi<StudentSchemeLearningStatisticsResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取我的培训方案配置
   * <p>
   * 在学员中心使用，如果学员还在学习中会始终返回最新的方案配置，
   * 如果学员已培训通过，该接口会返回培训通过之前最新的培训方案配置‘快照’
   * @param page      分页参数
   * @param request   方案配置查询条件
   * @param needField 以配置模板为原型给出需要的部分配置（如：课程考核要求，返回结果将返回仅返回课程考核要求内容），不传返回完整配置
   * 返回指定配置内容(参数使用 数组["一层级字段.第二层级字段...","一层级字段.第二层级字段..."] 放在参数中，告诉接口仅获取哪些字段或“块”，不填则返回全部)
   * }
   * 如：needField = ["name","picture","chooseCourseLearning.config"]，返回方案名称、图片、选课规则配置下的所有字段值
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeConfigInMySelf(
    params: { page?: Page; request?: StudentSchemeConfigRequest; needField?: Array<string> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentSchemeConfigInMySelf,
    operation?: string
  ): Promise<Response<SchemeConfigResponsePage>> {
    return commonRequestApi<SchemeConfigResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页获取我批次缴费的学员培训方案学习列表
   * 因为pageStudentSchemeLearningInCollective接口与此接口功能一致，且8.0通用与差异化使用此接口的地方均注释，所以此接口弃用且停止维护
   * 使用pageStudentSchemeLearningInCollective接口
   * @param page
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningBatchedInMyself(
    params: { page?: Page; request?: StudentSchemeLearningRequest; sort?: Array<StudentSchemeLearningSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningBatchedInMyself,
    operation?: string
  ): Promise<Response<StudentSchemeLearningResponsePage>> {
    return commonRequestApi<StudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 集体缴费管理员分页获取学员列表
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningInCollective(
    params: { page?: Page; request?: StudentSchemeLearningRequest; sort?: Array<StudentSchemeLearningSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningInCollective,
    operation?: string
  ): Promise<Response<StudentSchemeLearningResponsePage>> {
    return commonRequestApi<StudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 集体缴费管理员分页获取学员列表
   * @deprecated 差异化在平台上了
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageStudentSchemeLearningInCollectiveBTXPXPT(
    params: { page?: Page; request?: StudentSchemeLearningRequest; sort?: Array<StudentSchemeLearningSortRequest> },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageStudentSchemeLearningInCollectiveBTXPXPT,
    operation?: string
  ): Promise<Response<StudentSchemeLearningResponsePage>> {
    return commonRequestApi<StudentSchemeLearningResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询当前分销商下的教学地点列表（无需鉴权）
   * 沟通后弃用该接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageTrainingClassroomInDistributor(
    params: { page?: Page; request?: TrainingPointRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageTrainingClassroomInDistributor,
    operation?: string
  ): Promise<Response<TrainingPointResponsePage>> {
    return commonRequestApi<TrainingPointResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 分页查询当前网校下的教学地点列表
   * 沟通后弃用该接口
   * @param request
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async pageTrainingClassroomInMyself(
    params: { page?: Page; request?: TrainingPointRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageTrainingClassroomInMyself,
    operation?: string
  ): Promise<Response<TrainingPointResponsePage>> {
    return commonRequestApi<TrainingPointResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 分页查询当前分销商下的培训地点列表（无需鉴权）
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageTrainingPointInDistributor(
    params: { page?: Page; request?: TrainingPointRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageTrainingPointInDistributor,
    operation?: string
  ): Promise<Response<TrainingPointResponsePage>> {
    return commonRequestApi<TrainingPointResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
