<template>
  <el-main>
    <el-card shadow="never" class="m-card is-bg is-overflow-hidden">
      <div class="f-plr20 f-pt20">
        <!--条件查询-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="姓名">
                <el-input v-model="input" clearable placeholder="请输入姓名" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="证件号">
                <el-input v-model="input" clearable placeholder="请输入证件号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="手机号">
                <el-input v-model="input" clearable placeholder="请输入手机号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="5">
              <el-form-item label="订单号">
                <el-input v-model="input" clearable placeholder="请输入订单号" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="4" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--人员信息-->
        <el-collapse v-model="activeNames5" @change="handleChange" class="m-collapse no-border">
          <el-collapse-item name="1">
            <div slot="title" class="m-tit">
              <span class="tit-txt">人员信息</span>
            </div>
            <!--表格-->
            <el-table stripe :data="tableData" max-height="240" highlight-current-row class="m-table">
              <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
              <el-table-column label="姓名" min-width="160" fixed="left">
                <template>张依依</template>
              </el-table-column>
              <el-table-column label="证件号" min-width="200">
                <template>354875965412365896</template>
              </el-table-column>
              <el-table-column label="手机号" min-width="200">
                <template>13003831002</template>
              </el-table-column>
              <el-table-column label="单位地区" min-width="200">
                <template>福建省-福州市-鼓楼区</template>
              </el-table-column>
              <el-table-column label="注册时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-collapse-item>
        </el-collapse>
      </div>
    </el-card>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-border-top is-sticky">
      <el-tab-pane label="学员信息" name="first">
        <div class="f-p15">详见 2601_客服管理_业务咨询_学员信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="学习内容" name="second">
        <div class="f-p15">
          <el-card shadow="never" class="m-card is-header f-mb15">
            <div class="f-plr20 f-pt20">
              <el-row :gutter="16" class="m-query">
                <el-form :inline="true" label-width="auto">
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="年度">
                      <el-select v-model="select" clearable filterable placeholder="请选择">
                        <el-option value="选项1"></el-option>
                        <el-option value="选项2"></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item label="培训方案">
                      <el-input v-model="input" clearable placeholder="请输入培训方案名称" />
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :md="8" :xl="6">
                    <el-form-item>
                      <el-button type="primary">查询</el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
            </div>
          </el-card>
          <el-row :gutter="15" class="is-height">
            <el-col :md="8">
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div class="f-plr20">
                  <div class="m-tit">
                    <span class="tit-txt">已报方案</span>
                  </div>
                </div>
                <div class="f-plr10 f-pb20 m-plan-list">
                  <el-table stripe :data="tableData" highlight-current-row class="m-table is-body">
                    <el-table-column>
                      <template slot-scope="scope">
                        <div v-if="scope.$index === 0">
                          <div class="f-flex f-align-start">
                            <div class="tag is-gray"><i class="txt">失效</i></div>
                            <div class="f-flex-sub">
                              <p>2020年公需科目培训班，方案名称支持两行</p>
                              <p class="f-f13 f-c9">
                                【面网授-培训班-选课规则】<span class="f-ml20">2020 / 人社 / 公需</span>
                              </p>
                              <el-tag type="warning">分销</el-tag>
                            </div>
                            <el-tooltip class="item" effect="dark" placement="right">
                              <!--处理中 添加 is-warning，成功 添加 is-success，失败 添加 is-error-->
                              <span class="label">智能<br />学习</span>
                              <div slot="content">待处理</div>
                            </el-tooltip>
                          </div>
                        </div>
                        <div v-else-if="scope.$index === 1">
                          <div class="f-flex f-align-start">
                            <div class="tag is-gray"><i class="txt">失效</i></div>
                            <div class="f-flex-sub">
                              <p>2020年公需科目培训班，方案名称支持两行</p>
                              <p class="f-f13 f-c9">
                                【面网授-培训班-选课规则】<span class="f-ml20">2020 / 人社 / 公需</span>
                              </p>
                              <el-tooltip class="item" effect="dark" placement="top">
                                <el-tag type="primary">专题</el-tag>
                                <div slot="content">专题名称专题名称</div>
                              </el-tooltip>
                            </div>
                            <el-tooltip class="item" effect="dark" placement="right">
                              <!--处理中 添加 is-warning，成功 添加 is-success，失败 添加 is-error-->
                              <span class="label is-success">智能<br />学习</span>
                              <div slot="content">处理成功</div>
                            </el-tooltip>
                          </div>
                        </div>
                        <div v-else-if="scope.$index === 3">
                          <div class="f-flex f-align-start">
                            <div class="tag is-gray"><i class="txt">失效</i></div>
                            <div class="f-flex-sub">
                              <p>2020年公需科目培训班，方案名称支持两行</p>
                              <p class="f-f13 f-c9">
                                【面网授-培训班-选课规则】<span class="f-ml20">2020 / 人社 / 公需</span>
                              </p>
                              <el-tooltip class="item" effect="dark" placement="top">
                                <el-tag type="primary">专题</el-tag>
                                <div slot="content">专题名称专题名称</div>
                              </el-tooltip>
                            </div>
                            <el-tooltip class="item" effect="dark" placement="right">
                              <!--处理中 添加 is-warning，成功 添加 is-success，失败 添加 is-error-->
                              <span class="label is-warning">智能<br />学习</span>
                              <div slot="content">处理中</div>
                            </el-tooltip>
                          </div>
                        </div>
                        <div v-else-if="scope.$index === 2">
                          <div class="f-flex f-align-start">
                            <div class="tag is-gray"><i class="txt">失效</i></div>
                            <div class="f-flex-sub">
                              <p>2020年公需科目培训班，方案名称支持两行</p>
                              <p class="f-f13 f-c9">
                                【面网授-培训班-选课规则】<span class="f-ml20">2020 / 人社 / 公需</span>
                              </p>
                            </div>
                            <el-tooltip class="item" effect="dark" placement="right">
                              <!--处理中 添加 is-warning，成功 添加 is-success，失败 添加 is-error-->
                              <span class="label is-error">智能<br />学习</span>
                              <div slot="content">处理失败，失败原因：读取对应的失败原因。</div>
                            </el-tooltip>
                          </div>
                        </div>
                        <div v-else>
                          <div class="f-flex f-align-start">
                            <div class="tag"><i class="txt">有效</i></div>
                            <div class="f-flex-sub">
                              <p>2020年公需科目培训班，方案名称支持两行</p>
                              <p class="f-f13 f-c9">
                                【面网授-培训班-选课规则】<span class="f-ml20">2020 / 教师 / 学段学科</span>
                              </p>
                              <!--<el-tag type="success" size="mini">有效</el-tag>-->
                            </div>
                          </div>
                        </div>
                      </template>
                    </el-table-column>
                  </el-table>
                  <!--分页-->
                  <el-pagination
                    background
                    class="f-mt15 f-tr"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
                    :current-page="currentPage4"
                    :page-sizes="[100, 200, 300, 400]"
                    :page-size="100"
                    layout="total, prev, pager, next"
                    :total="400"
                  >
                  </el-pagination>
                </div>
              </el-card>
            </el-col>
            <el-col :md="16">
              <el-card shadow="never" class="m-card is-header f-mb15">
                <div slot="header" class="">
                  <span class="tit-txt">培训内容</span>
                </div>
                <div class="f-p20">
                  <el-tabs v-model="activeName2" type="card" class="no-margin">
                    <el-tab-pane label="基本信息" name="first">
                      <el-row class="no-gutter">
                        <el-form ref="form" :model="form" label-width="auto" class="m-text-form f-ml30 f-mt30">
                          <el-col :span="12">
                            <el-form-item label="培训方案：">2020年公需科目培训班</el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="方案类型：">面网授-培训班-选课规则</el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="年度：">2020</el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="培训属性：">2020年 / 教师行业 / 学段学科</el-form-item>
                          </el-col>
                          <!--                          <el-col :span="12">
                            <el-form-item label="培训属性：">2020年 / 人社行业 / 公需科目</el-form-item>
                          </el-col>-->
                          <el-col :span="12">
                            <el-form-item label="开通方式：">个人报名</el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="开通时间：">2020-12-02 14:00:00</el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="面授期别/编号：">2024年xx专业（第一期）/1001</el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="考勤要求：">75%</el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="考核成绩：">-</el-form-item>
                          </el-col>
                          <el-col :span="12">
                            <el-form-item label="考核学时：">30</el-form-item>
                          </el-col>
                          <el-col :span="24">
                            <el-form-item label="考核结果：" class="is-form">
                              <span class="f-cg f-mr10">合格</span>
                              <span class="f-cr f-mr10">未合格</span>
                              <span class="f-co f-mr10">待考核</span>
                              <el-button type="primary" size="small">一键合格</el-button>
                            </el-form-item>
                          </el-col>
                        </el-form>
                      </el-row>
                    </el-tab-pane>
                    <el-tab-pane label="学习情况" name="second">
                      <el-tabs v-model="activeName3" class="m-tab-custom">
                        <el-tab-pane label="线上课程" name="first">
                          <div class="m-tit is-small">
                            <span class="tit-txt">考核情况</span>
                          </div>
                          <el-form ref="form" :model="form" label-width="auto" class="m-form f-ml20 f-mt10">
                            <el-form-item label="整体考核进度：">
                              <el-progress
                                :show-text="false"
                                :stroke-width="26"
                                :percentage="70"
                                class="f-mt5 form-l"
                              />
                              <div class="f-mt5">
                                <span class="f-mr40 f-fb f-co">要求：40学时</span>
                                <span class="f-mr40"><i class="f-dot f-mr5"></i>已完成：36学时</span>
                                <span class="f-mr40"><i class="f-dot gray f-mr5"></i>还差：4学时</span>
                              </div>
                            </el-form-item>
                            <el-form-item label="选课情况：">
                              <el-progress
                                :show-text="false"
                                :stroke-width="26"
                                :percentage="50"
                                class="f-mt5 form-l"
                              />
                              <div class="f-mt5">
                                <span class="f-mr40 f-fb f-co">要求：40学时</span>
                                <span class="f-mr40"><i class="f-dot f-mr5"></i>已完成：36学时</span>
                                <span class="f-mr40"><i class="f-dot gray f-mr5"></i>还差：4学时</span>
                              </div>
                            </el-form-item>
                          </el-form>
                          <div class="m-tit is-small f-flex">
                            <span class="tit-txt">课程学时</span>
                            <span class="f-ml10">（共 <i class="f-cr">5</i> 门课程）</span>
                          </div>
                          <el-table
                            :data="tableData1"
                            row-key="id"
                            lazy
                            :load="load"
                            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
                            max-height="500px"
                            class="m-table"
                          >
                            <el-table-column prop="field01" min-width="300">
                              <template slot="header">
                                <div class="f-ml5">课程名称</div>
                              </template>
                            </el-table-column>
                            <el-table-column label="时长" min-width="120">
                              <template>00:02:54</template>
                            </el-table-column>
                            <el-table-column label="学时" min-width="100" align="center">
                              <template>54</template>
                            </el-table-column>
                            <el-table-column label="进度" min-width="110" align="right">
                              <template>60.01%</template>
                            </el-table-column>
                            <el-table-column label="开始学习时间" min-width="170">
                              <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                                <i class="f-dot red f-mr5"></i>
                                <div slot="content">同步数据/模拟数据: 2021.07.23 11:11:11</div>
                              </el-tooltip>
                              <template>2020-11-11 12:20:20</template>
                            </el-table-column>
                            <el-table-column label="最后学习时间" min-width="170">
                              <template>2021.02 02 14:00:00</template>
                            </el-table-column>
                            <el-table-column prop="field02" label="操作" width="160" align="center" fixed="right">
                              <template>
                                <el-button type="text" size="mini">一键学习</el-button>
                                <el-button type="text" size="mini">测验详情</el-button>
                                <el-button type="text" size="mini">删除课程</el-button>
                                <el-button type="text" size="mini">监管日志</el-button>
                              </template>
                            </el-table-column>
                          </el-table>
                        </el-tab-pane>
                        <el-tab-pane label="培训期别" name="second">
                          <div class="m-offline-course">
                            <div class="item">
                              <div class="status"><el-tag type="success">已合格</el-tag></div>
                              <div class="require">考勤要求：扫码/GPS签到/签退，考勤率>75%（要求签到/签退 X 次）</div>
                              <div class="rate">考勤率：75%（完成签到/签退 Y 次）</div>
                              <div class="op"><el-button type="text">查看考勤详情</el-button></div>
                            </div>
                            <div class="item">
                              <div class="status"><el-tag type="danger">未合格</el-tag></div>
                              <div class="require">结业测试：-</div>
                              <div class="rate">合格时间：2024-11-03 12:12:12</div>
                              <div class="op"><el-button type="text">查看考勤详情</el-button></div>
                            </div>
                            <div class="item">
                              <div class="status"><el-tag type="danger">未考核</el-tag></div>
                              <div class="require">考勤要求：扫码/GPS签到/签退，考勤率>75%（要求签到/签退 X 次）</div>
                              <div class="rate">考勤率：75%（完成签到/签退 Y 次）</div>
                              <div class="op"><el-button type="text">查看考勤详情</el-button></div>
                            </div>
                            <div class="item">
                              <div class="status"><el-tag type="warning">不纳入考核</el-tag></div>
                              <div class="require">考勤要求：扫码/GPS签到/签退，考勤率>75%（要求签到/签退 X 次）</div>
                              <div class="rate">考勤率：75%（完成签到/签退 Y 次）</div>
                              <div class="op"><el-button type="text">查看考勤详情</el-button></div>
                            </div>
                          </div>
                        </el-tab-pane>
                      </el-tabs>
                    </el-tab-pane>
                    <el-tab-pane label="班级考试" name="third">
                      <div class="f-ptb25 f-plr5">
                        <div class="f-f16 f-fb">考试名称支持显示两行</div>
                        <div class="f-flex f-mt20">
                          <div class="m-score f-mr30">
                            <!--合格 .f-cg，不合格 .f-cr-->
                            <p><span class="num f-cg">80</span>分</p>
                            <p class="f-mt5 f-c9">考试最高分</p>
                          </div>
                          <el-row class="no-gutter f-flex-sub">
                            <el-form :inline="true" label-width="auto" class="m-text-form f-mt15">
                              <el-col :span="12">
                                <el-form-item label="总分 / 及格分：">100 / 60分</el-form-item>
                              </el-col>
                              <el-col :span="12">
                                <el-form-item label="考试时长：">60分钟</el-form-item>
                              </el-col>
                              <el-col :span="12">
                                <el-form-item label="考试周期：">2021-11-22 至 2022-12-31</el-form-item>
                              </el-col>
                              <el-col :span="12">
                                <el-form-item label="已考 / 剩余：">3 / 6</el-form-item>
                              </el-col>
                            </el-form>
                          </el-row>
                        </div>
                      </div>
                      <el-table stripe :data="tableData" max-height="500px" class="m-table">
                        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                        <el-table-column label="答卷时间" min-width="180">
                          <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                            <i class="el-icon-info f-co f-mr5"></i>
                            <div slot="content">同步数据/模拟数据: 2021.07.23 11:11:11</div>
                          </el-tooltip>
                          <template>
                            2020-11-11 12:20:20
                          </template>
                        </el-table-column>
                        <el-table-column label="成绩(分)" min-width="200" align="center">
                          <template slot-scope="scope">
                            <div v-if="scope.$index === 0">80</div>
                            <div v-else-if="scope.$index === 1">52</div>
                            <div v-else>69</div>
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" min-width="100" align="center" fixed="right">
                          <template>
                            <el-button type="text" size="mini">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-tab-pane>
                    <el-tab-pane label="替考记录" name="fourth">
                      <div class="m-tit is-small">
                        <span class="tit-txt">每次考试监拍情况</span>
                      </div>
                      <div class="m-supervision-situation">
                        <div class="item">
                          <el-tag type="danger" class="item-no">1</el-tag>
                          <div class="f-flex-sub">
                            <el-row class="no-gutter f-flex-sub">
                              <el-form :inline="true" label-width="auto" class="m-text-form">
                                <el-col :span="12">
                                  <el-form-item label="进入考试时间：">2017-12-12 17:23:23</el-form-item>
                                </el-col>
                                <el-col :span="12">
                                  <el-form-item label="考试成绩：">100分</el-form-item>
                                </el-col>
                                <el-col :span="12">
                                  <el-form-item label="是否合格：">合格</el-form-item>
                                </el-col>
                                <el-col :span="12">
                                  <el-form-item label="是否替考：">无替考</el-form-item>
                                </el-col>
                              </el-form>
                            </el-row>
                            <!--表格-->
                            <el-table stripe :data="tableData2" max-height="500px" class="m-table">
                              <el-table-column label="拍摄照片" min-width="150">
                                <template>
                                  <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
                                </template>
                              </el-table-column>
                              <el-table-column label="拍摄时间" min-width="180">
                                <template>2017-12-12 17:23:23</template>
                              </el-table-column>
                              <el-table-column label="匹配结果" min-width="200">
                                <template>匹配</template>
                              </el-table-column>
                              <el-table-column label="考试时间点" width="140" align="center" fixed="right">
                                <template>00:01:00</template>
                              </el-table-column>
                            </el-table>
                          </div>
                        </div>
                        <div class="item">
                          <el-tag type="danger" class="item-no">2</el-tag>
                          <div class="f-flex-sub">
                            <el-row class="no-gutter f-flex-sub">
                              <el-form :inline="true" label-width="auto" class="m-text-form">
                                <el-col :span="12">
                                  <el-form-item label="进入考试时间：">2017-12-12 17:23:23</el-form-item>
                                </el-col>
                                <el-col :span="12">
                                  <el-form-item label="考试成绩：">100分</el-form-item>
                                </el-col>
                                <el-col :span="12">
                                  <el-form-item label="是否合格：">合格</el-form-item>
                                </el-col>
                                <el-col :span="12">
                                  <el-form-item label="是否替考：">无替考</el-form-item>
                                </el-col>
                              </el-form>
                            </el-row>
                            <!--表格-->
                            <el-table stripe :data="tableData2" max-height="500px" class="m-table">
                              <el-table-column label="拍摄照片" min-width="150">
                                <template>
                                  <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
                                </template>
                              </el-table-column>
                              <el-table-column label="拍摄时间" min-width="180">
                                <template>2017-12-12 17:23:23</template>
                              </el-table-column>
                              <el-table-column label="匹配结果" min-width="200">
                                <template>匹配</template>
                              </el-table-column>
                              <el-table-column label="考试时间点" width="140" align="center" fixed="right">
                                <template>00:01:00</template>
                              </el-table-column>
                            </el-table>
                          </div>
                        </div>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane label="申诉记录" name="fifth">
                      <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                        <el-table-column label="申诉来源" min-width="240">
                          <template>
                            <div class="f-fb">选修必修选修必修</div>
                            <div>进考时间：2021-07-12 18:32:58</div>
                          </template>
                        </el-table-column>
                        <el-table-column label="所属方案" min-width="150">
                          <template>选修必修选修必修</template>
                        </el-table-column>
                        <el-table-column label="申诉内容" min-width="200">
                          <template>申诉内容申诉内容申诉内容申诉内容申诉内容申诉内容</template>
                        </el-table-column>
                        <el-table-column label="申诉时间" min-width="180">
                          <template>2017-12-12 17:23:23</template>
                        </el-table-column>
                        <el-table-column label="申诉状态" min-width="120" align="center" fixed="right">
                          <template slot-scope="scope">
                            <div v-if="scope.$index === 0">
                              <el-badge is-dot type="danger" class="badge-status">审批不通过</el-badge>
                            </div>
                            <div v-else-if="scope.$index === 1">
                              <el-badge is-dot type="success" class="badge-status">审批通过</el-badge>
                            </div>
                            <div v-else>
                              <el-badge is-dot type="warning" class="badge-status">待审批</el-badge>
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                    </el-tab-pane>
                    <el-tab-pane label="学习心得" name="sisth">
                      <el-row class="no-gutter">
                        <el-form ref="form" :model="form" label-width="auto" class="m-text-form f-ml30 f-mt30">
                          <el-col :span="12">
                            <el-form-item label="考核要求：">
                              ①各项学习心得要求以具体配置为准
                              <br />
                              ②学习心得纳入考核，至少参加 <i class="f-cr">Z</i> 个心得，且每项心得均为通过。
                            </el-form-item>
                          </el-col>
                        </el-form>
                      </el-row>
                      <div class="m-attribute">
                        <el-collapse v-model="activeNames" @change="handleChange" accordion>
                          <el-collapse-item name="1">
                            <template slot="title">
                              <span>
                                <el-tag type="primary" effect="dark" size="mini">必选</el-tag>
                                <i class="f-fb f-ml5 wzxz"
                                  >活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称</i
                                ></span
                              >
                              <span class="f-mlr40">参加时间：2023-10-23 00:00:00至2023-11-11 23:59:59</span>
                              <span>审核方式：人工审核</span>
                              <span class="f-ml40">剩余提交次数：2次</span>
                            </template>
                            <div class="f-plr20">
                              <el-row :gutter="20">
                                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                                  <el-table-column label="提交时间" min-width="140">
                                    <template>2023-10-23 00:00:00</template>
                                  </el-table-column>
                                  <el-table-column label="审核状态" min-width="100" align="center">
                                    <template>已审核</template>
                                  </el-table-column>
                                  <el-table-column label="审核时间" min-width="140">
                                    <template>2023-10-23 00:00:00</template>
                                  </el-table-column>
                                  <el-table-column label="审核结果" min-width="100" align="center">
                                    <template>通过(100)</template>
                                  </el-table-column>
                                  <el-table-column label="操作" width="80" align="center" fixed="right">
                                    <template>
                                      <el-button type="text" size="mini">删除</el-button>
                                    </template>
                                  </el-table-column>
                                </el-table>
                              </el-row>
                            </div>
                          </el-collapse-item>
                          <el-collapse-item name="2">
                            <template slot="title">
                              <span>
                                <el-tag type="primary" effect="dark" size="mini">必选</el-tag>
                                <i class="f-fb f-ml5 wzxz"
                                  >活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称</i
                                ></span
                              >
                              <span class="f-mlr40">参加时间：2023-10-23 00:00:00至2023-11-11 23:59:59</span>
                              <span>审核方式：人工审核</span>
                              <span class="f-ml40">剩余提交次数：2次</span>
                            </template>
                            <div class="f-plr20">
                              <el-row :gutter="20">
                                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                                  <el-table-column label="提交时间" min-width="140">
                                    <template>2023-10-23 00:00:00</template>
                                  </el-table-column>
                                  <el-table-column label="审核状态" min-width="100" align="center">
                                    <template>已审核</template>
                                  </el-table-column>
                                  <el-table-column label="审核时间" min-width="140">
                                    <template>2023-10-23 00:00:00</template>
                                  </el-table-column>
                                  <el-table-column label="审核结果" min-width="100" align="center">
                                    <template>通过(100)</template>
                                  </el-table-column>
                                  <el-table-column label="操作" width="80" align="center" fixed="right">
                                    <template>
                                      <el-button type="text" size="mini">删除</el-button>
                                    </template>
                                  </el-table-column>
                                </el-table>
                              </el-row>
                            </div>
                          </el-collapse-item>
                          <el-collapse-item name="3">
                            <template slot="title">
                              <span>
                                <el-tag type="primary" effect="dark" size="mini">必选</el-tag>
                                <i class="f-fb f-ml5 wzxz"
                                  >活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称活动主题名称</i
                                ></span
                              >
                              <span class="f-mlr40">参加时间：2023-10-23 00:00:00至2023-11-11 23:59:59</span>
                              <span>审核方式：人工审核</span>
                              <span class="f-ml40">剩余提交次数：2次</span>
                            </template>
                            <div class="f-plr20">
                              <el-row :gutter="20">
                                <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                                  <el-table-column label="提交时间" min-width="140">
                                    <template>2023-10-23 00:00:00</template>
                                  </el-table-column>
                                  <el-table-column label="审核状态" min-width="100" align="center">
                                    <template>已审核</template>
                                  </el-table-column>
                                  <el-table-column label="审核时间" min-width="140">
                                    <template>2023-10-23 00:00:00</template>
                                  </el-table-column>
                                  <el-table-column label="审核结果" min-width="100" align="center">
                                    <template>通过(100)</template>
                                  </el-table-column>
                                  <el-table-column label="操作" width="80" align="center" fixed="right">
                                    <template>
                                      <el-button type="text" size="mini">删除</el-button>
                                    </template>
                                  </el-table-column>
                                </el-table>
                              </el-row>
                            </div>
                          </el-collapse-item>
                        </el-collapse>
                      </div>
                      <!--                      <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                        <el-table-column label="主题" min-width="240">
                          <template>
                            <el-tag type="primary" effect="dark" size="mini">必选</el-tag>
                            活动主题名称
                          </template>
                        </el-table-column>
                        <el-table-column label="参加时间" min-width="240">
                          <template>2023-10-23 00:00:00至2023-11-11 23:59:59</template>
                        </el-table-column>
                        <el-table-column label="作答形式" min-width="100">
                          <template>提交附件</template>
                        </el-table-column>
                        <el-table-column label="审核方式" min-width="100">
                          <template>提交自动通过</template>
                        </el-table-column>
                        <el-table-column label="提交时间" min-width="140">
                          <template>2023-10-23 00:00:00</template>
                        </el-table-column>
                        <el-table-column label="审核状态" min-width="100" align="center">
                          <template>已审核</template>
                        </el-table-column>
                        <el-table-column label="审核时间" min-width="140">
                          <template>2023-10-23 00:00:00</template>
                        </el-table-column>
                        <el-table-column label="审核结果" min-width="100" align="center">
                          <template>通过(100)</template>
                        </el-table-column>
                        <el-table-column label="剩余提交次数" min-width="140" align="center">
                          <template>1次</template>
                        </el-table-column>
                        <el-table-column label="操作" width="80" align="center" fixed="right">
                          <template>
                            <el-button type="text" size="mini">删除</el-button>
                          </template>
                        </el-table-column>
                      </el-table>-->
                    </el-tab-pane>
                    <el-tab-pane label="调研问卷 " name="seventh">
                      <el-form ref="form" :model="form" label-width="auto" class="m-text-form f-ml30 f-mt30">
                        <el-col :span="12">
                          <el-form-item label="考核要求：">调研问卷纳入考核，按具体问卷要求提交。</el-form-item>
                        </el-col>
                      </el-form>
                      <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt15">
                        <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                        <el-table-column label="问卷名称" min-width="240">
                          <template>
                            <p>读取问卷名称读取问卷名称</p>
                            <el-tag type="danger" size="mini">停用</el-tag>
                          </template>
                        </el-table-column>
                        <el-table-column label="应用范围" min-width="120">
                          <template>指定的期别名称</template>
                        </el-table-column>
                        <el-table-column label="是否纳入考核" min-width="120">
                          <template>是</template>
                        </el-table-column>
                        <el-table-column label="是否强制完成" min-width="120">
                          <template>是</template>
                        </el-table-column>
                        <el-table-column label="问卷开放时间" min-width="240" align="center">
                          <template>
                            <p><el-tag type="info" size="mini">开始</el-tag>2024-10-01 08:08:08</p>
                            <p><el-tag type="info" size="mini">结束</el-tag>2024-10-01 08:08:08</p>
                          </template>
                        </el-table-column>
                        <el-table-column label="提交时间" min-width="120" align="center">
                          <template>2024-10-01 08:08:08</template>
                        </el-table-column>
                        <el-table-column label="提交状态" min-width="120" align="center" fixed="right">
                          <template slot-scope="scope">
                            <div v-if="scope.$index === 0">
                              <el-badge is-dot type="primary" class="badge-status">已提交</el-badge>
                            </div>
                            <div v-else>
                              <el-badge is-dot type="danger" class="badge-status">未提交</el-badge>
                            </div>
                          </template>
                        </el-table-column>
                        <el-table-column label="操作" min-width="120" align="center" fixed="right">
                          <template slot-scope="scope">
                            <div v-if="scope.$index === 0">
                              <el-button type="text">查看问卷</el-button>
                              <el-button type="text">答题记录</el-button>
                            </div>
                            <div v-else>
                              <el-button type="text" disabled>查看问卷</el-button>
                              <el-button type="text" disabled>答题记录</el-button>
                            </div>
                          </template>
                        </el-table-column>
                      </el-table>
                      <!--分页-->
                      <el-pagination
                        background
                        class="f-mt15 f-tr"
                        @size-change="handleSizeChange"
                        @current-change="handleCurrentChange"
                        :current-page="currentPage4"
                        :page-sizes="[100, 200, 300, 400]"
                        :page-size="100"
                        layout="total, prev, pager, next"
                        :total="400"
                      >
                      </el-pagination>
                    </el-tab-pane>
                  </el-tabs>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </div>
      </el-tab-pane>
      <el-tab-pane label="换班信息" name="third">
        <div class="f-p15">详见 2603_客服管理_业务咨询_换班信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="订单信息" name="fourth">
        <div class="f-p15">详见 2604_客服管理_业务咨询_订单信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="发票信息" name="five">
        <div class="f-p15">详见 2605_客服管理_业务咨询_发票信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="退款信息" name="six">
        <div class="f-p15">详见 2606_客服管理_业务咨询_退款信息.vue</div>
      </el-tab-pane>
      <el-tab-pane label="学习档案" name="seven">
        <div class="f-p15">详见 2607_客服管理_业务咨询_培训档案.vue</div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames5: ['1'],
        activeName: 'second',
        activeName1: 'first',
        activeName2: 'first',
        activeName3: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [
          { field101: '1' },
          { field101: '2' },
          { field101: '3' },
          { field101: '4' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' },
          { field101: '5' }
        ],
        tableData1: [
          {
            id: 1,
            field01: '这里是分类名称',
            hasChildren: true
          },
          {
            id: 2,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 3,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 4,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 5,
            field01: '分类名称1',
            hasChildren: true
          }
        ],
        tableData2: [{ field101: '1' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      },
      load(tree, treeNode, resolve) {
        setTimeout(() => {
          resolve([
            {
              id: 11,
              field01: '分类名称1-1'
            },
            {
              id: 12,
              field01: '分类名称1-2'
            }
          ])
        }, 1000)
      }
    }
  }
</script>
