import OrderRefundStatus, { OrderRefundStatusEnum } from '@api/service/common/return-order/enums/OrderRefundStatus'
import { OrderRefundTypeEnum, OnlyReturnList } from '@api/service/common/return-order/enums/OrderRefundType'

import { ReturnOrderResponse as CustomerReturnOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import {
  CommoditySkuResponse,
  ReturnOrderBasicDataResponse,
  ReturnOrderResponse,
  SchemeResourceResponse
} from '@api/diff-gateway/zztt-trade-query-front-gateway-TradeQueryBackstage'
import RefundRecordItem from '@api/service/common/return-order/models/RefundRecordItem'
export default class RefundRecordItemXMLG extends RefundRecordItem {
  /**
   * 退货单扩展信息
key:courseType,华医部分退款
value:1-专业课   2-公需课  3-都退
   */
  courseType = ''

  constructor() {
    super()
  }

  static fromDiff(dto: ReturnOrderResponse) {
    const diffDto = Object.assign(new CustomerReturnOrderResponse(), dto)
    const vo = Object.assign(new RefundRecordItemXMLG(), RefundRecordItem.from(diffDto))
    const ext = dto?.ext as any
    vo.courseType = ext?.courseType || ''
    return vo
  }

  static fromCustomer(dto: CustomerReturnOrderResponse) {
    const vo = new RefundRecordItemXMLG()
    const basicData = dto?.basicData || new ReturnOrderBasicDataResponse()
    let dtoCommodity = new CommoditySkuResponse()
    if (OnlyReturnList.includes(basicData.returnOrderType)) {
      dtoCommodity = dto.returnCommodity.commoditySku
    } else {
      dtoCommodity = dto.refundCommodity.commoditySku
    }

    vo.returnOrderId = dto.returnOrderNo
    vo.applyTime = basicData.returnOrderStatusChangeTime?.applied

    vo.reason = basicData.applyInfo?.reasonContent
    vo.type = basicData.returnOrderType
    vo.amount = basicData.refundAmount

    const schemeInfo = dtoCommodity.resource as SchemeResourceResponse
    if (dto?.subOrderInfo?.exchanged) {
      vo.returnContent = dto?.returnCommodity?.commoditySku?.saleTitle
    } else {
      vo.returnContent = schemeInfo.schemeName || dtoCommodity.saleTitle
    }
    vo.status = OrderRefundStatus.transferDtoToCurrentEnum(
      basicData.returnOrderStatus,
      basicData.returnCloseReason.closeType
    )

    return vo
  }
}
