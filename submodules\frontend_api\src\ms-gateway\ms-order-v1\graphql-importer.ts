import applyCancelOrder from './mutates/applyCancelOrder.graphql'
import applyInvoice from './mutates/applyInvoice.graphql'
import applyInvoiceValidate from './mutates/applyInvoiceValidate.graphql'
import batchApplyInvoice from './mutates/batchApplyInvoice.graphql'
import batchApplyInvoiceDirectly from './mutates/batchApplyInvoiceDirectly.graphql'
import batchApplyInvoiceValidate from './mutates/batchApplyInvoiceValidate.graphql'
import batchInvoiceChange from './mutates/batchInvoiceChange.graphql'
import buyerApplyReturn from './mutates/buyerApplyReturn.graphql'
import cancelCollectiveSignup from './mutates/cancelCollectiveSignup.graphql'
import createOrder from './mutates/createOrder.graphql'
import findBatchOrderBatchPayStatus from './mutates/findBatchOrderBatchPayStatus.graphql'
import offlinePayBatchOrder from './mutates/offlinePayBatchOrder.graphql'
import offlinePayBatchOrderAndApplyInvoice from './mutates/offlinePayBatchOrderAndApplyInvoice.graphql'
import offlinePayOrder from './mutates/offlinePayOrder.graphql'
import onlinePayBatchOrder from './mutates/onlinePayBatchOrder.graphql'
import onlinePayBatchOrderAndApplyInvoice from './mutates/onlinePayBatchOrderAndApplyInvoice.graphql'
import onlinePayOrder from './mutates/onlinePayOrder.graphql'
import prepareApplyInvoice from './mutates/prepareApplyInvoice.graphql'
import prepareBatchOrderPay from './mutates/prepareBatchOrderPay.graphql'
import preparePay from './mutates/preparePay.graphql'
import preparePlaceOrder from './mutates/preparePlaceOrder.graphql'
import sellerApplyAfterSale from './mutates/sellerApplyAfterSale.graphql'
import sellerApplyBatchReturn from './mutates/sellerApplyBatchReturn.graphql'
import sellerApplyExchange from './mutates/sellerApplyExchange.graphql'
import sellerApplyReturn from './mutates/sellerApplyReturn.graphql'
import sellerCancelOrder from './mutates/sellerCancelOrder.graphql'
import updateBatchOfflinePaymentVouchers from './mutates/updateBatchOfflinePaymentVouchers.graphql'
import updateBatchOrderInvoice from './mutates/updateBatchOrderInvoice.graphql'

export {
  applyCancelOrder,
  applyInvoice,
  applyInvoiceValidate,
  batchApplyInvoice,
  batchApplyInvoiceDirectly,
  batchApplyInvoiceValidate,
  batchInvoiceChange,
  buyerApplyReturn,
  cancelCollectiveSignup,
  createOrder,
  findBatchOrderBatchPayStatus,
  offlinePayBatchOrder,
  offlinePayBatchOrderAndApplyInvoice,
  offlinePayOrder,
  onlinePayBatchOrder,
  onlinePayBatchOrderAndApplyInvoice,
  onlinePayOrder,
  prepareApplyInvoice,
  prepareBatchOrderPay,
  preparePay,
  preparePlaceOrder,
  sellerApplyAfterSale,
  sellerApplyBatchReturn,
  sellerApplyExchange,
  sellerApplyReturn,
  sellerCancelOrder,
  updateBatchOfflinePaymentVouchers,
  updateBatchOrderInvoice
}
