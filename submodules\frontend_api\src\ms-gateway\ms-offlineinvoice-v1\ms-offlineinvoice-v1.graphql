"""独立部署的微服务,K8S服务名:ms-offlineinvoice-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	commonQueryOfflineInvoiceImportResult(request:QueryForImportInfoRequest,page:Page):QueryImportInfoResponsePage @page(for:"QueryImportInfoResponse")
	"""根据主任务编号获取全部导入数据
		@param mainTaskId
		@return 导入的excel访问地址
	"""
	getAllImportData(mainTaskId:String):String
	"""根据主任务编号获取失败数据excel表格
		@param mainTaskId
		@return 生成的失败数据excel表格
	"""
	getImportFailedData(mainTaskId:String):String
	queryForImportBatchPayOfflineInvoice(request:QueryForImportInfoRequest,page:Page):QueryImportInfoResponsePage @page(for:"QueryImportInfoResponse")
	queryForImportBatchPaySpecialInvoiceDelivery(request:QueryForImportInfoRequest,page:Page):QueryImportInfoResponsePage @page(for:"QueryImportInfoResponse")
	queryForImportBatchPaySpecialPaperOfflineInvoice(request:QueryForImportInfoRequest,page:Page):QueryImportInfoResponsePage @page(for:"QueryImportInfoResponse")
	queryForImportOfflineInvoice(request:QueryForImportInfoRequest,page:Page):QueryImportInfoResponsePage @page(for:"QueryImportInfoResponse")
	queryForImportOfflineInvoiceWithServiceId(request:QueryForImportInfoRequest,page:Page):QueryImportInfoResponsePage @page(for:"QueryImportInfoResponse")
	queryForImportOfflineSpecialElectronicInvoice(request:QueryForImportInfoRequest,page:Page):QueryImportInfoResponsePage @page(for:"QueryImportInfoResponse")
	queryForImportSpecialInvoiceDelivery(request:QueryForImportInfoRequest,page:Page):QueryImportInfoResponsePage @page(for:"QueryImportInfoResponse")
	queryForImportSpecialInvoiceDeliveryWithServiceId(request:QueryForImportInfoRequest,page:Page):QueryImportInfoResponsePage @page(for:"QueryImportInfoResponse")
	queryForImportSpecialPaperOfflineInvoice(request:QueryForImportInfoRequest,page:Page):QueryImportInfoResponsePage @page(for:"QueryImportInfoResponse")
	queryForImportSpecialPaperOfflineInvoiceWithServiceId(request:QueryForImportInfoRequest,page:Page):QueryImportInfoResponsePage @page(for:"QueryImportInfoResponse")
	"""查询网校下的配送列表"""
	queryShippingMethodsForSchool:QueryShippingMethodsResponse
}
type Mutation {
	"""创建线下发票配送渠道"""
	createChannel(request:CreateOfflineInvoiceDeliveryChannelRequest):Void
	"""新建网校下的配送方式列表"""
	createChannelShippingMethods(request:CreateDeliveryChannelShippingMethodsRequest):Void
	"""创建增值税专用发票电子票(自测专用口)"""
	createOfflineSpecialElectronicInvoice(command:CreateOfflineSpecialPaperElectronicRequestCommand):Void
	"""删除线下发票配送渠道"""
	deleteChannel(request:DeleteOfflineInvoiceDeliveryChannelRequest):Void
	"""请求发票配送"""
	deliveryInvoice(request:ConfirmDeliveryRequest):Void
	"""冻结发票 补偿接口！"""
	freezeInvoice(request:FreezeInvoiceRequest):Void
	"""导入个人报名线下发票
		@param importRequest
		@return  批次导入主任务编号
	"""
	importOfflineInvoice(importRequest:OfflineInvoiceImportRequest):String
	"""导入线下纸质发票配送、自取信息
		@param importRequest
		@return  批次导入主任务编号
	"""
	importOfflineInvoiceDeliveryInfo(importRequest:OfflineInvoiceDeliveryInfoImportRequest):String
	"""导入线下纸质发票配送、自取信息(创建主任务时设置了服务商id)
		@param importRequest
		@return  批次导入主任务编号
	"""
	importOfflineInvoiceDeliveryInfoWithServiceId(importRequest:OfflineInvoiceDeliveryInfoImportRequest):String
	"""导入个人报名线下发票(创建主任务时设置了服务商id)
		@param importRequest
		@return  批次导入主任务编号
	"""
	importOfflineInvoiceWithServiceId(importRequest:OfflineInvoiceImportRequest):String
	"""开票"""
	issueOfflineInvoice(request:IssueInvoiceRequest):Void
	"""请求发票自提"""
	pickupInvoice(request:ConfirmPickupRequest):Void
	"""重置开票"""
	resetInvoice(request:ResetInvoiceRequest):Void
	"""更新线下发票配送渠道"""
	updateChannel(request:UpdateOfflineInvoiceDeliveryChannelRequest):Void
	"""修改网校下的配送列表"""
	updateChannelShippingMethods(request:UpdateDeliveryChannelShippingMethodsRequest):Void
	"""更新线下发票配送渠道状态"""
	updateChannelStatus(request:UpdateOfflineInvoiceDeliveryChannelStatusRequest):Void
	"""更新线下电子发票信息"""
	updateOfflineInvoice(request:UpdateOfflineInvoiceRequest):Void
	"""更新线下纸质专用发票信息"""
	updateOfflinePaperInvoice(request:UpdateOfflineSpecialPaperInvoiceRequest):Void
	"""更新增值税专用发票电子票信息"""
	updateOfflineSpecialPaperElectronicInvoice(request:UpdateOfflineSpecialElectronicInvoiceRequest):OfflineInvoiceCommonResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""创建增值税专用票电子票请求命令
	<AUTHOR> By lincong
	@date 2023/08/01 15:35
"""
input CreateOfflineSpecialPaperElectronicRequestCommand @type(value:"com.fjhb.microservice.offline.invoice.v1.api.commands.CreateOfflineSpecialPaperElectronicRequestCommand") {
	"""增值税专用票(电子票)发票编号【必传】"""
	offlineInvoiceId:String
	"""关联实体类型
		@see AssociationTypeEnum
	"""
	associationType:Int!
	"""关联实体编号"""
	associationId:String
	"""发票种类"""
	invoiceCategory:Int!
	"""发票抬头类型"""
	titleType:Int!
	"""发票抬头"""
	title:String
	"""纳税人识别号(统一社会信用代码)"""
	taxPayerNo:String
	"""购买方地址(注册地址)"""
	address:String
	"""购买方电话(注册电话)"""
	phone:String
	"""购买方银行账户(开户银行)"""
	account:String
	"""购买方开户名称(银行账户)"""
	bankName:String
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""手机号码、联系电话"""
	contactPhone:String
	"""联系电子邮箱"""
	contactEmail:String
	"""发票总金额"""
	amount:BigDecimal
	"""创建时间"""
	createDate:DateTime
	"""发票备注"""
	remark:String
	"""平台编号"""
	platformId:String
	"""平台版本编号"""
	platformVersionId:String
	"""项目编号"""
	projectId:String
	"""子项目编号"""
	subProjectId:String
	"""服务商编号"""
	servicerId:String
	"""单位编号"""
	unitId:String
	"""发票信息校验策略
		@see InvoiceVerifyStrategy
	"""
	invoiceVerifyStrategy:Int
}
input DeliveryAddress @type(value:"com.fjhb.microservice.offline.invoice.v1.api.commands.entities.DeliveryAddress") {
	"""收件人"""
	consignee:String
	"""电话"""
	phone:String
	"""所在物理地区"""
	region:String
	"""地址"""
	address:String
}
input TakePoint @type(value:"com.fjhb.microservice.offline.invoice.v1.api.commands.entities.TakePoint") {
	"""领取地点"""
	pickupLocation:String
	"""领取时间"""
	pickupTime:String
	"""备注"""
	remark:String
}
input ConfirmDeliveryRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.ConfirmDeliveryRequest") {
	"""线下发票编号"""
	offlineInvoiceId:String
	"""快递公司名称"""
	expressCompanyName:String
	"""快递单号"""
	expressNo:String
}
input ConfirmPickupRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.ConfirmPickupRequest") {
	"""线下发票编号"""
	offlineInvoiceId:String
	"""领取人"""
	takePerson:String
	"""手机号"""
	phone:String
}
"""<AUTHOR>
	@since 2022/5/6
"""
input CreateDeliveryChannelShippingMethodsRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.CreateDeliveryChannelShippingMethodsRequest") {
	"""渠道配送方式列表，1为自取，2为快递"""
	shippingMethodList:[Int]
}
input CreateOfflineInvoiceDeliveryChannelRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.CreateOfflineInvoiceDeliveryChannelRequest") {
	"""配送方式
		@see com.fjhb.domain.trade.api.offlineinvoice.consts.OfflineShippingMethods
	"""
	shippingMethod:Int!
	"""渠道名称"""
	channelName:String
	"""配送地点"""
	address:String
	"""配送时间"""
	deliveryDate:String
	"""是否启用"""
	enable:Boolean!
	"""备注"""
	remark:String
}
input DeleteOfflineInvoiceDeliveryChannelRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.DeleteOfflineInvoiceDeliveryChannelRequest") {
	"""渠道编号"""
	channelId:String
}
"""<AUTHOR>
	@since 2024/4/25
"""
input FreezeInvoiceRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.FreezeInvoiceRequest") {
	"""线下发票id"""
	offlineInvoiceId:String
	"""冻结来源编号"""
	freezeSourceId:String
	"""冻结来源类型"""
	freezeSourceType:Int
}
input IssueInvoiceRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.IssueInvoiceRequest") {
	"""线下发票编号"""
	offlineInvoiceId:String
	"""发票编号,格式以英文逗号分隔"""
	invoiceNo:String
}
"""线下发票配送、自取导入请求
	<AUTHOR>
	@since 2022/5/12
"""
input OfflineInvoiceDeliveryInfoImportRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.OfflineInvoiceDeliveryInfoImportRequest") {
	"""文件路径"""
	filePath:String
	"""导入类型
		1. 个人报名专票配送
		2. 集体报名专票配送
	"""
	importType:Int
}
"""个人线下电子发票导入请求
	<AUTHOR>
	@since 2022/5/9
"""
input OfflineInvoiceImportRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.OfflineInvoiceImportRequest") {
	"""文件路径"""
	filePath:String
	"""导入类型
		1. 个人缴费线下电子票
		2. 个人缴费线下专票
		3. 批次缴费线下电子票
		4. 批次缴费线下纸质票
		5.个人缴费增值税专用电子票
	"""
	importType:Int
}
"""查询批量导入任务的执行情况请求
	<AUTHOR>
	@since 2022/5/19
"""
input QueryForImportInfoRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.QueryForImportInfoRequest") {
	"""任务名称"""
	taskName:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""起始执行时间"""
	executeStartTime:DateTime
	"""结束执行时间"""
	executeEndTime:DateTime
	"""导入任务类型
		PERSONAL_OFFLINE_INVOICE_IMPORT(导入个人线下电子发票)
		PERSONAL_OFFLINE_SPECIAL_INVOICE_IMPORT(导入个人线下纸质发票)
		BATCH_OFFLINE_INVOICE_IMPORT(导入批次缴费线下电子发票)
		BATCH_OFFLINE_SPECIAL_INVOICE_IMPORT(导入批次缴费线下纸质发票)
		PERSONAL_OFFLINE_INVOICE_DELIVERY_INFO_IMPORT(导入个人报名线下发票配送信息)
		BATCH_OFFLINE_INVOICE_DELIVERY_INFO_IMPORT(导入集体报名线下专票配送信息)
		PERSONAL_OFFLINE_SPECIAL_ELECTRONIC_INVOICE_IMPORT(导入个人线下增值税专用票电子票)
		BATCH_OFFLINE_SPECIAL_ELECTRONIC_INVOICE_IMPORT(导入批次缴费线下增值税专用票电子票)
		@see com.fjhb.ms.offline.invoice.v1.kernel.supports.ImportCategorysTypes
	"""
	category:String
}
"""重置线下发票开票
	<AUTHOR>
	@since 2022/5/5
"""
input ResetInvoiceRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.ResetInvoiceRequest") {
	"""线下发票编号"""
	offlineInvoiceId:String
	"""原因，可为空"""
	reason:String
}
"""<AUTHOR>
	@since 2022/5/6
"""
input UpdateDeliveryChannelShippingMethodsRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.UpdateDeliveryChannelShippingMethodsRequest") {
	"""渠道配送方式列表，1为自取，2为快递"""
	shippingMethodList:[Int]
}
input UpdateOfflineInvoiceDeliveryChannelRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.UpdateOfflineInvoiceDeliveryChannelRequest") {
	"""渠道编号"""
	channelId:String
	"""配送方式"""
	shippingMethod:Int
	"""渠道名称"""
	channelName:String
	"""配送地点"""
	address:String
	"""配送时间"""
	deliveryDate:String
	"""是否启用"""
	enable:Boolean
	"""备注"""
	remark:String
}
input UpdateOfflineInvoiceDeliveryChannelStatusRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.UpdateOfflineInvoiceDeliveryChannelStatusRequest") {
	"""渠道编号"""
	channelId:String
	"""渠道状态（是否开启）"""
	enable:Boolean
}
input UpdateOfflineInvoiceRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.UpdateOfflineInvoiceRequest") {
	"""线下发票编号"""
	offlineInvoiceId:String
	"""发票总金额,null表示不更新"""
	amount:BigDecimal
	"""发票种类,null表示不更新"""
	invoiceCategory:Int
	"""发票抬头,null表示不更新"""
	title:String
	"""抬头类型,null表示不更新"""
	titleType:Int
	"""纳税人识别号,null表示不更新"""
	taxpayerNo:String
	"""购买方地址,null表示不更新"""
	address:String
	"""购买方电话,null表示不更新"""
	phone:String
	"""购买方开户行名称,null表示不更新"""
	bankName:String
	"""购买方银行账户,null表示不更新"""
	account:String
	"""联系电子邮箱,null表示不更新"""
	contactEmail:String
	"""联系电话,null表示不更新"""
	contactPhone:String
	"""发票备注"""
	remark:String
}
"""更新增值税专票电子票请求
	<AUTHOR> By lincong
	@date 2023/08/02 11:27
"""
input UpdateOfflineSpecialElectronicInvoiceRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.UpdateOfflineSpecialElectronicInvoiceRequest") {
	"""增值税专用票(电子票)发票编号【必传】"""
	offlineInvoiceId:String
	"""发票抬头类型"""
	titleType:Int
	"""发票抬头，null表示不跟新"""
	title:String
	"""纳税人识别号"""
	taxpayerNo:String
	"""购买方银行账户(开户银行)"""
	account:String
	"""购买方开户行名称(银行账户)"""
	bankName:String
	"""购买方电话(注册电话)"""
	phone:String
	"""购买方地址(注册地址)"""
	address:String
	"""营业执照"""
	businessLicensePath:String
	"""开户许可"""
	accountOpeningLicensePath:String
	"""手机号码、联系电话"""
	contactPhone:String
	"""联系电子邮箱"""
	contactEmail:String
	"""发票类型"""
	invoiceType:Int
	"""发票种类"""
	invoiceCategory:Int
	"""发票总金额"""
	amount:BigDecimal
	"""发票备注"""
	remark:String
	"""发票信息校验策略
		@see InvoiceVerifyStrategy
	"""
	invoiceVerifyStrategy:Int
}
input UpdateOfflineSpecialPaperInvoiceRequest @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.requests.UpdateOfflineSpecialPaperInvoiceRequest") {
	"""线下发票编号"""
	offlineInvoiceId:String
	"""发票总金额,null表示不修改"""
	amount:BigDecimal
	"""发票类型,null表示不修改"""
	invoiceType:Int
	"""发票种类,null表示不修改"""
	invoiceCategory:Int
	"""发票抬头,null表示不修改"""
	title:String
	"""纳税人识别号,null表示不修改"""
	taxpayerNo:String
	"""购买方地址,null表示不修改"""
	address:String
	"""购买方电话,null表示不修改"""
	phone:String
	"""购买方开户行名称,null表示不修改"""
	bankName:String
	"""购买方银行账户,null表示不修改"""
	account:String
	"""联系电子邮箱,null表示不修改"""
	contactEmail:String
	"""营业执照,null表示不修改"""
	businessLicensePath:String
	"""开户许可,null表示不修改"""
	accountOpeningLicensePath:String
	"""配送方式,null表示不修改"""
	shippingMethod:Int
	"""配送地址信息,null表示不修改"""
	deliveryAddress:DeliveryAddress
	"""自取点信息,null表示不修改"""
	takePoint:TakePoint
	"""发票备注"""
	remark:String
	"""发票信息校验策略，策略中不校验的字段传null时，会将字段修改为null
		@see InvoiceVerifyStrategy
	"""
	invoiceVerifyStrategy:Int
}
"""各状态及执行结果对应数量
	<AUTHOR>
	@since 2022/5/24
"""
type EachStateCount @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.response.EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Int!
}
"""线下发票通用返回类
	<AUTHOR> By lincong
	@date 2023/08/03 10:26
"""
type OfflineInvoiceCommonResponse @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.response.OfflineInvoiceCommonResponse") {
	"""状态码"""
	code:String
	"""描述信息"""
	message:String
	"""线下发票id"""
	offlineInvoiceId:String
	"""返回数据"""
	data:String
}
"""执行情况信息请求响应体
	<AUTHOR>
	@since 2022/5/19
"""
type QueryImportInfoResponse @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.response.QueryImportInfoResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""任务名称"""
	name:String
	"""任务分类"""
	category:String
	"""所属批次单编号"""
	batchNo:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""创建时间"""
	createdTime:DateTime
	"""就绪时间"""
	alreadyTime:DateTime
	"""执行时间"""
	executingTime:DateTime
	"""完成时间"""
	completedTime:DateTime
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
}
"""<AUTHOR>
	@since 2022/5/6
"""
type QueryShippingMethodsResponse @type(value:"com.fjhb.ms.offline.invoice.v1.kernel.gateway.graphql.response.QueryShippingMethodsResponse") {
	"""信息"""
	message:String
	"""配送渠道"""
	shippingMethods:[Int]
}

scalar List
type QueryImportInfoResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [QueryImportInfoResponse]}
