import ApplySingleCourseLearningTokenRequest from './models/ApplySingleCourseLearningTokenRequest'
import ApplyExamLearningTokenRequest from './models/ApplyExamLearningTokenRequest'
import ApplyQuestionLibPLTokenRequest from './models/ApplyQuestionLibPLTokenRequest'
import preExamLsGateway, {
  ApplyCourseLearningTokenRequest,
  ApplyExamLearningTokenRequest as ApplyExamLearningTokenDto,
  ApplyInterestCourseLearningTokenRequest,
  ApplyQuestionLibPLTokenDto
} from '@api/gateway/NormalIssueClassLS-default'
import courseLearningGateway from '@api/gateway/CourseLearning-default'
import Response from '../../../Response'
import ApplySingleCoursePreviewTokenRequest from './models/ApplySingleCoursePreviewTokenRequest'
import PlatformLogin, { AppletsRandomLoginRequest } from '@api/gateway/PlatformLogin'
import { Action, getModule, Module, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { Role, RoleType, Secure } from '@api/Secure'

/**
 * 调用申请token前要设置好当前用户的用户信息
 */
@Module({ namespaced: true, dynamic: true, name: 'CustomerTokenModule', store })
class TokenModule extends VuexModule {
  /**
   * 申请课程学习Token
   */
  @Action
  @Role([RoleType.user])
  async applySingleCourseLearningToken(request: ApplySingleCourseLearningTokenRequest): Promise<Response<string>> {
    const dto = new ApplyCourseLearningTokenRequest()
    Object.assign(dto, request)
    const response = await preExamLsGateway.applySingleCourseLearningToken(dto)
    return response
  }
  /**
   * 申请兴趣课程学习Token
   */
  @Action
  @Role([RoleType.user])
  async applyInterestCourseLearningToken(request: ApplyInterestCourseLearningTokenRequest): Promise<Response<string>> {
    const dto = new ApplyInterestCourseLearningTokenRequest()
    Object.assign(dto, request)
    const response = await preExamLsGateway.applyInterestCourseLearningToken(dto)
    return response
  }

  /**
   * 申请课程预览Token
   * @param ctx 状态
   * @param request 请求参数
   */
  @Action
  @Role([RoleType.user])
  async applySingleCoursePreviewToken(request: ApplySingleCoursePreviewTokenRequest): Promise<Response<string>> {
    const response = await courseLearningGateway.applyPreviewCourseToken(request.courseId)
    return response
  }

  /**
   * 申请试题练习Token
   */
  @Action
  @Role([RoleType.user])
  async applyQuestionLibLearningToken(request: ApplyQuestionLibPLTokenRequest): Promise<Response<string>> {
    const dto = new ApplyQuestionLibPLTokenDto()
    Object.assign(dto, request)
    const response = await preExamLsGateway.applyQuestionLibPracticeLearningToken(dto)
    return response
  }

  /**
   * 申请考试Token
   */
  @Action
  @Role([RoleType.user])
  async applyExamLearningToken(request: ApplyExamLearningTokenRequest): Promise<Response<string>> {
    const dto = new ApplyExamLearningTokenDto()
    Object.assign(dto, request)
    const response = await preExamLsGateway.applyExamLearningToken(dto)
    return response
  }

  /**
   * 根据身份证+姓名，获取登录认证的Token appletsCallBackLogin
   */
  @Action
  @Role([RoleType.user])
  async applyIdcardAndNameLoginToken(request: AppletsRandomLoginRequest): Promise<Response<string>> {
    // const dto = new ApplyInterestCourseLearningTokenRequest()
    // Object.assign(dto, request)
    const response = await PlatformLogin.appletsCallBackLogin(request)
    return response
  }
}

export default getModule(TokenModule)
