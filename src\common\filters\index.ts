import { VueConstructor } from 'vue'

const filters = {
  // 空数据占位符
  emptyDataPlaceholder(val: string) {
    return val ? val : '-'
  },

  /**
   * 价格过滤 不足的不零 保留两位小数
   */
  formatPrice(val: number): string {
    if (!val) {
      return '0.00'
    }
    let price: string = Math.round(val * 100) / 100 + ''
    const priceStr = price.toString()
    const priceArr = priceStr.split('.')
    if (priceArr.length === 1) {
      price = priceStr + '.00'
      return price
    }
    if (priceArr.length > 1) {
      if (priceArr[1].length < 2) {
        return price.toString() + '0'
      }
      return price
    }
  },

  /**
   * 数量过滤 满足千分位时添加，
   */
  formatThousands(num: number) {
    const numToString = `${num}`
    return `${numToString.replace(/\d{1,3}(?=(\d{3})+(\.\d*)?$)/g, '$&,')}`
  },

  /**
   * 身份证号脱敏 隐藏中间8位
   */
  formatCertification(id: string, serviceType: number) {
    if (id) {
      if (serviceType == -1) {
        return id
      }
      return id.replace(/^(.{6})(?:\d+)(.{4})$/, '$1********$2')
    }
  },
  /**
   * 手机号脱敏 隐藏中间4位
   */
  formatPhone(phone: string, serviceType: number) {
    if (phone) {
      if (serviceType == -1) {
        return phone
      }
      return phone.replace(/(\d{3})\d*(\d{4})/, '$1****$2')
    } else {
      return '-'
    }
  }
}

export default {
  install(Vue: VueConstructor) {
    Vue.filter('emptyDataPlaceholder', filters.emptyDataPlaceholder)
    // 价格格式化
    Vue.filter('formatPrice', filters.formatPrice)
    // 数量千分位
    Vue.filter('formatThousands', filters.formatThousands)
    // 身份证号脱敏
    Vue.filter('formatCertification', filters.formatCertification)
    // 手机号脱敏
    Vue.filter('formatPhone', filters.formatPhone)
  }
}
