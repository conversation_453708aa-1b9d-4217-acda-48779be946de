// 单位信息即培训机构信息
// import { TeachUnitPageDTO } from '@api/gateway/PlatformTrade'

export class TeachUnit {
  id: string
  name: string
  /**
   * 组织机构证
   */
  organizationCode: string
  /**
   * 单位省份id
   */
  provinceId: string
  /**
   * 单位地区 市
   */
  cityId: string
  /**
   * 单位地区 区县
   */
  countyId: string
  /**
   * 单位状态 | 1：正常;2:停用  默认正常
   */
  status: string

  //todo 弃用该方法，弃用整个状态层，请改用机构服务商接口
  // static from(dto: TeachUnitPageDTO): TeachUnit {
  //   const unit = new TeachUnit()
  //   unit.id = dto.id
  //   unit.name = dto.name
  //   unit.organizationCode = dto.organizationCode
  //   unit.provinceId = dto.provinceId
  //   unit.cityId = dto.cityId
  //   unit.countyId = dto.countyId
  //   unit.status = dto.status
  //   return unit
  // }
}
