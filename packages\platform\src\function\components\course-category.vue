<route-meta>
{ "title": "课程分类下拉搜索选择器" }
</route-meta>
<template>
  <el-cascader
    class="biz-common"
    v-model="categoryIdList"
    :clearable="clearable"
    filterable
    @clear="categoryIdList = undefined"
    :props="props"
    :options="excellentCourseConfig.hasCategory ? newTree : options"
    :placeholder="placeholder"
    @change="selectedChange"
  />
</template>

<script lang="ts">
  import { Watch, Prop, Emit, Component, Vue } from 'vue-property-decorator'
  import { CascaderOptions } from '@hbfe/jxjy-admin-components/src/models/CascaderOptions'
  import QueryCourseCategory from '@api/service/management/resource/course-category/query/QueryCourseCategory'
  import CourseCategoryListDetail from '@api/service/management/resource/course-category/query/vo/CourseCategoryListDetail'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import { rootCourseCategory } from '@api/service/common/config/CommonConfig'
  import { cloneDeep } from 'lodash'
  import ExcellentCourseConfig from '@api/service/management/online-school-config/excellent-course/query/ExcellentCourseConfig'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    multiple: boolean

    @Prop({
      type: String,
      default: '请选择课程分类'
    })
    placeholder: string

    @Prop({
      type: Boolean,
      default: true
    })
    clearable: boolean

    @Prop({
      type: [Array, String]
    })
    value: Array<string>

    @Prop({
      type: Boolean,
      default: false
    })
    isArray: boolean

    @Prop({
      type: Boolean,
      default: true
    })
    showRootNode: boolean

    @Prop({
      type: String,
      default: ''
    })
    leafId: string

    @Prop({
      type: Boolean,
      default: true
    })
    checkStrictly: boolean

    @Prop({
      type: Boolean,
      default: false
    })
    filterable: boolean

    @Prop({
      type: ExcellentCourseConfig,
      default: new ExcellentCourseConfig()
    })
    excellentCourseConfig: ExcellentCourseConfig

    categoryIdList: Array<string> = new Array<string>()
    props = {
      multiple: false,
      label: 'name',
      value: 'id',
      lazy: false,
      leaf: 'leaf',
      checkStrictly: false,
      lazyLoad: async (node: { data: CourseCategoryListDetail }, resolve: any) => {
        if (node?.data?.id) {
          if (node?.data?.children?.length) {
            resolve(null)
          } else {
            const result = await this.query(node?.data?.id)
            resolve(result)
          }
        }
      }
    }
    options: any = []

    newTree: any = []

    @Watch('value')
    valueChange() {
      this.categoryIdList = this.value
    }

    selectedChange() {
      if (this.categoryIdList && this.categoryIdList.length) {
        return this.categoryIdList
      }
    }

    get courseCategoryTreeList() {
      return this.queryCourseCategory.courseCategoryTreeList
    }

    queryCourseCategory = new QueryCourseCategory()

    async created() {
      this.setProps()
      if (this.filterable) {
        this.props.lazy = false
        this.props.label = 'label'
        this.props.value = 'value'
        if (this.showRootNode) {
          this.options = [
            {
              value: '-1',
              label: '课程分类',
              children: []
            }
          ]
          await this.getAllCategoryData('-1', this.options[0].children)
          this.reShade(this.options)
        } else {
          this.options = []
          await this.getAllCategoryData('-1', this.options)
          this.reShade(this.options)
        }
        await this.formatLimitTree()
      } else {
        await this.loadDataList()
        if (this.value) await this.echo()
        this.categoryIdList = cloneDeep(this.value)
      }
    }

    //获取所有数据
    async getAllCategoryData(id: string, option: Array<CascaderOptions>) {
      const tree = await this.query(id)
      await Promise.all(
        tree.map((item, index) => {
          const category = new CascaderOptions()
          category.label = item.name
          category.value = item.id
          category.children = []
          option.push(category)
          return this.getAllCategoryData(item.id, option[index].children)
        })
      )
    }

    @Watch('categoryIdList')
    @Emit('input')
    changeValue() {
      return this.categoryIdList
    }

    async loadDataList() {
      if (this.showRootNode) {
        this.options = [rootCourseCategory]
      } else {
        this.options = await this.query('-1')
      }
    }

    async echo() {
      const list: string[] = cloneDeep(this.value)
      // 递归查询 拼装数据
      await this.formatTree(this.options, list.shift(), list)
    }

    /**
     * @description: 格式化这颗课件树
     * @param {*} optionList 当前层级的可选项
     * @param {*} searchNode 需要查找的节点
     * @param {*} nodes 当前节点集合
     * @return {*}
     */
    async formatTree(optionList: CourseCategoryListDetail[], searchNode: string, nodes: string[]) {
      if (!searchNode) {
        return
      }
      const currentOptions = optionList.filter((el) => {
        return el.id === searchNode
      })
      const currentOption: any = currentOptions.length ? currentOptions[0] : undefined
      if (!currentOption) return
      const subNodes = await this.query(currentOption?.id)
      currentOption.hasChildren = true
      currentOption.children = subNodes
      if (this.checkStrictly) {
        currentOption.leaf = false
      } else {
        currentOption.leaf = !nodes.length
      }
      await this.formatTree(subNodes, nodes.shift(), nodes)
    }

    async query(id?: string) {
      return await ResourceModule.courseCategoryFactory.query.queryChildrenById(id)
    }

    //未使用懒加载时使用的格式化数据方法
    formatCategoryTree(tree: Array<CourseCategoryListDetail>, option: Array<CascaderOptions>) {
      tree.forEach((item) => {
        const category = new CascaderOptions()
        category.label = item.name
        category.value = item.id
        if (item.children && item.children.length) {
          category.children = []
        }
        option.push(category)
        if (item.children && item.children.length) {
          this.formatCategoryTree(item.children, option[option.length - 1].children)
        }
      })
    }

    setProps() {
      this.props.multiple = this.multiple
      this.props.checkStrictly = this.checkStrictly
    }

    // 重新生成树
    formatLimitTree() {
      if (!this.excellentCourseConfig.hasCategory) return
      this.newTree = this.excellentCourseConfig.categories.map((item: any) => {
        return { children: new Array<any>(), label: item.name, value: item.id }
      })
      this.reShade(this.newTree)
    }

    /**
     * 树形结构选项重塑
     * @description 没有子级的节点，children统一改为null
     */
    reShade(options: Array<CascaderOptions>) {
      if (options && options.length) {
        options.forEach((item) => {
          if (!item.children || !item.children.length) {
            item.children = null
          } else {
            this.reShade(item.children)
          }
        })
      }
    }
  }
</script>
