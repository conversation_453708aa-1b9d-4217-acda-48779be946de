/**
 * 查询工厂类
 */
import { QueryTrainClassReportList } from '@api/service/management/statisticalReport/query/QueryTrainClassReportList'
import { QueryStudentLearningManagerRegionList } from '@api/service/management/statisticalReport/query/QueryStudentLearningManagerRegionList'
import QueryStudentLearningList from '@api/service/management/statisticalReport/query/QueryStudentLearningList'
import { QuerySchemeLearningList } from '@api/service/management/statisticalReport/query/QuerySchemeLearningList'
import { QueryRegionReportList } from '@api/service/management/statisticalReport/query/QueryRegionReportList'
import { QueryRegionManagerRegionLearningList } from '@api/service/management/statisticalReport/query/QueryRegionManagerRegionLearningList'
import QueryManagerRegionLearningList from '@api/service/management/statisticalReport/query/QueryManagerRegionLearningList'
import QueryPraticeRecordList from '@api/service/management/statisticalReport/query/QueryPraticeRecordList'
import QueryExamRecordList from '@api/service/management/statisticalReport/query/QueryExamRecordList'
import QueryClassReportBySucnum from '@api/service/management/statisticalReport/query/QueryClassReportBySucnum'
import { QueryTradeStatistic } from '@api/service/management/statisticalReport/query/QueryTradeStatistic'
import QueryChooseCourseStatistic from '@api/service/management/statisticalReport/query/QueryChooseCourseStatistic'
import TrainingUnitSalesStatistics from '@api/service/management/statisticalReport/query/TrainingUnitSalesStatistics'

class QueryStaticticalReportFactory {
  /*
   * 查询首页交易概述
   * */
  getQueryTradeStatistic() {
    return new QueryTradeStatistic()
  }
  /*
   * 查询首页方案报名排行
   * */
  getQueryClassReportBySucnum() {
    return new QueryClassReportBySucnum()
  }
  /*
   * 查询培训班开通统计
   * */
  getQueryTrainClassReportList() {
    return new QueryTrainClassReportList()
  }
  /*
   * 地区管理员查询学员学习明细
   * */
  getQueryStudentLearningManagerRegionList() {
    return new QueryStudentLearningManagerRegionList()
  }
  /*
   * 查询学员学习明细
   * */
  getQueryStudentLearningList() {
    return new QueryStudentLearningList()
  }
  /*
   * 班级学习统计
   * */
  getQuerySchemeLearningList() {
    return new QuerySchemeLearningList()
  }
  /*
   * 地区开通统计
   * */
  getQueryRegionReportList() {
    return new QueryRegionReportList()
  }
  /*
   * 地区管理员地区学习统计
   * */
  getQueryRegionManagerRegionLearningList() {
    return new QueryRegionManagerRegionLearningList()
  }
  /*
   * 地区学习统计
   * */
  getQueryManagerRegionLearningList() {
    return new QueryManagerRegionLearningList()
  }
  /*
   * 课后测验记录
   * */
  getQueryPraticeRecordList() {
    return new QueryPraticeRecordList()
  }
  /*
   * 考试记录
   * */
  getQueryExamRecordList() {
    return new QueryExamRecordList()
  }

  // * 获取选课统计列表
  getQueryChooseCourseStatisticList() {
    return new QueryChooseCourseStatistic()
  }

  // * 获取培训课程销售统计列表
  getQueryTrainingUnitSalesStatistics() {
    return new TrainingUnitSalesStatistics()
  }
}
export default QueryStaticticalReportFactory
