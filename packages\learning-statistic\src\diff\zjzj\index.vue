<route-meta>
{
"isMenu": true,
"title": "学员学习明细",
"sort": 5,
"icon": "icon-mingxi"
}
</route-meta>
<template>
  <div>
    <learning-statistic ref="learningStatistic" v-bind="$attrs" v-on="$listeners">
      <template #export-docking-data>
        <el-button
          v-if="$hasPermission('exportData')"
          query
          desc="导出对接数据"
          actions="doExportData"
          @click="doExportData"
          >导出对接数据</el-button
        >
      </template>
    </learning-statistic>
    <el-dialog title="提示" :visible.sync="exportDataSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：导出任务查看-对接数据</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportDataSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadDataPage">前往下载</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import LearningStatistic from '@hbfe/jxjy-admin-learningStatistic/src/index.vue'
  import UserModule from '@api/service/management/user/UserModule'
  import ExportDockingData from '@api/service/diff/management/zjzj/learning-statistic/ExportDockingData'

  @Component({ components: { LearningStatistic } })
  export default class extends Vue {
    @Ref('learningStatistic') learningStatistic: LearningStatistic

    exportDockingData: ExportDockingData = new ExportDockingData()

    // 对接数据导出弹窗
    exportDataSuccessVisible = false
    /**
     * 导出对接数据
     */
    async doExportData() {
      console.log(this.learningStatistic.filter, '===')
      this.learningStatistic.getLocalSkuProperty()
      const res = await this.exportDockingData.exportDockingDiff(this.learningStatistic.filter)
      if (res.status.code == 200 && res.data) {
        this.exportDataSuccessVisible = true
        // this.$message.success('导出成功')
      } else {
        this.$message.warning('导出失败')
      }
    }

    /**
     * 导出对接数据任务下载
     */
    goDownloadDataPage() {
      this.exportDataSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportStudentLearningIntegrationDataStatistical' }
      })
    }
  }
</script>
