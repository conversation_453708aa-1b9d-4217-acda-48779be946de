import roleGateWay, { SecurityObjectGroupDto } from '@api/ms-gateway/ms-role-v1'
export class QueryCurrentUserSecurityGroup {
  /*
   *  获取当前角色安全对象
   * */
  async getCurrentUserSecurityGroup() {
    try {
      const res = await roleGateWay.getCurrentUserSecurityGroup()
      let tmpArr: SecurityObjectGroupDto[] = []

      if (res.status.isSuccess()) {
        tmpArr = res.data
      }

      console.log('调用了getCurrentUserSecurityGroup方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/authority/role/query/QueryCurrentUserSecurityGroup.ts所处方法，getCurrentUserSecurityGroup',
        e
      )
    }
  }
}
