<script lang="ts">
  import { Component } from 'vue-property-decorator'
  import BasicData from '@hbfe/jxjy-admin-customerService/src/diff/fjzj/personal/__components__/basic-data.vue'
  import ChangeClass from '@hbfe/jxjy-admin-customerService/src/diff/fjzj/personal/__components__/change-class.vue'
  import InvoiceInfo from '@hbfe/jxjy-admin-customerService/src/diff/fjzj/personal/__components__/invoice-info.vue'
  import OrderInfo from '@hbfe/jxjy-admin-customerService/src/diff/fjzj/personal/__components__/order-info.vue'
  import RefundInfo from '@hbfe/jxjy-admin-customerService/src/diff/fjzj/personal/__components__/refund-info.vue'
  import StudyContent from '@hbfe/jxjy-admin-customerService/src/diff/fjzj/personal/__components__/study-content.vue'
  import StudyRecords from '@hbfe/jxjy-admin-customerService/src/diff/fjzj/personal/__components__/study-records.vue'
  import Personal from '@hbfe/jxjy-admin-customerService/src/personal/index.vue'

  @Component({
    components: { BasicData, ChangeClass, InvoiceInfo, OrderInfo, RefundInfo, StudyContent, StudyRecords }
  })
  export default class extends Personal {}
</script>
