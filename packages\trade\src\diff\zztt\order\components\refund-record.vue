<template>
  <el-dialog title="查看退货/款记录" :visible.sync="isShow" width="1000px" class="m-dialog">
    <el-descriptions title="">
      <el-descriptions-item label="物品名称">{{ formattedSaleTitle }}</el-descriptions-item>
      <el-descriptions-item label="金额">
        {{ subOrderResponse.amount ? '¥' + subOrderResponse.amount : '0' }}
      </el-descriptions-item>
      <el-descriptions-item label="数量">{{ subOrderResponse.quantity || '0' }}</el-descriptions-item>
      <el-descriptions-item label="当前商品状态">{{ refundStatusText }}</el-descriptions-item>
    </el-descriptions>
    <el-table stripe ref="tableRef" v-loading="loading" :data="tableData" max-height="500px" class="m-table">
      <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
      <el-table-column label="发起退款时间" min-width="150" prop="applyTime"> </el-table-column>
      <el-table-column label="退款原因" min-width="150" prop="reason"> </el-table-column>
      <el-table-column label="退款类型" min-width="120">
        <template slot-scope="scope">
          {{ OrderRefundType.map.get(scope.row.type) }}
        </template>
      </el-table-column>
      <el-table-column label="退款金额" min-width="100" prop="amount"> </el-table-column>
      <el-table-column label="退货内容" min-width="100" prop="returnContent" show-overflow-tooltip>
        <template slot-scope="scope">
          <span v-if="isHuaYiWang && scope.row.courseType == 2">2025年公需课</span>
          <span v-else-if="isHuaYiWang && scope.row.courseType == 1">华医网2025年专业课</span>
          <span v-else>
            {{ scope.row.returnContent }}
          </span>
        </template>
      </el-table-column>
      <el-table-column label="退款状态" min-width="100">
        <template slot-scope="scope">
          <span>{{ OrderRefundStatusEnum.map.get(scope.row.status) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" @click="toDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div slot="footer">
      <el-button type="primary" @click="isShowDialog">关闭</el-button>
    </div>
  </el-dialog>
</template>
<script lang="ts">
  import { UiPage } from '@hbfe/common'
  import { Component, Vue, Prop } from 'vue-property-decorator'
  import RefundRecordItem from '@api/service/diff/management/zztt/trade/single/order/models/RefundRecordItem'
  import CommodityReturnLog from '@api/service/diff/management/zztt/trade/single/order/CommodityReturnLog'
  import OrderRefundType from '@api/service/common/return-order/enums/OrderRefundType'
  import OrderRefundStatusEnum from '@api/service/common/return-order/enums/OrderRefundStatus'
  import { SubOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import OrderDetailVo from '@api/service/management/trade/single/order/query/vo/UserOrderDetailVo'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import CommodityRefundStatus from '@api/service/common/return-order/enums/CommodityRefundStatus'

  @Component
  export default class extends Vue {
    @Prop({
      type: Boolean,
      default: false
    })
    isHuaYiWang: boolean

    page: UiPage

    isShow = false //打开抽屉弹窗

    loading = false //loading标识

    commodityReturnLog = new CommodityReturnLog() // 退货记录请求

    tableData = [] as RefundRecordItem[] // 列表返回数据

    OrderRefundStatusEnum = OrderRefundStatusEnum // 退货退款状态

    CommodityRefundStatus = CommodityRefundStatus // 退款类型

    OrderRefundType = OrderRefundType // 退款类型

    subOrderResponse = new SubOrderResponse() //子订单信息

    detail = new OrderDetailVo() //订单详情

    isFxlogin = QueryManagerDetail.hasCategory(CategoryEnums.fxs) //是否分销登录

    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable // 是否开启过分销增值能力服务

    isZtlogin = QueryManagerDetail.hasCategory(CategoryEnums.ztgly) //是否专题登录

    // 计算属性用于安全地获取并格式化 saleTitle
    get formattedSaleTitle() {
      const sku = this.subOrderResponse?.deliveryCommoditySku
      if (!sku || sku.saleTitle === undefined || sku.saleTitle === null) {
        return '-' // 处理未定义或为空的情况
      }
      return sku.saleTitle || '-' // 处理空字符串或其他假值
    }

    // 封装逻辑到计算属性中
    get refundStatusText() {
      if (!this.detail || !this.detail.subOrderRefundStatus || !CommodityRefundStatus.map) {
        return null // 确保空值情况下不会报错
      }
      const status = this.detail.subOrderRefundStatus(this.subOrderResponse)
      const refundMap = CommodityRefundStatus.map
      // 如果 map 中不存在对应值，返回默认值
      return refundMap.has(status) ? (refundMap.get(status) ? refundMap.get(status) : '-') : '-'
    }

    /**
     * 打开弹窗
     * @param id 退货单id??? TODO待定
     */
    isShowDialog(item: SubOrderResponse, detailVo: OrderDetailVo) {
      this.subOrderResponse = item
      this.detail = detailVo
      this.isShow = !this.isShow
      if (this.isShow) {
        this.commodityReturnLog.subOrderNo = item.subOrderNo
        this.doQueryPage()
      }
    }
    /**
     * 查询
     */
    async doQueryPage() {
      try {
        this.loading = true
        await this.initRequest()
        this.tableData = this.commodityReturnLog.records
        this.loading = false
      } catch (e) {
        console.log(e)
        this.$message.error('请求失败')
        this.loading = false
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['tableRef'] as any)?.doLayout()
        this.loading = false
      }
    }
    // 页面初始化调用的请求
    async initRequest() {
      if (this.isFxlogin && this.isHadFxAbility) {
        await this.commodityReturnLog.queryReturnLogDetailInDistributor()
      } else if (this.isZtlogin) {
        await this.commodityReturnLog.queryReturnLogDetailInTrainingChannel()
      } else {
        await this.commodityReturnLog.queryReturnLogDetail()
      }
    }
    /**
     * 跳转详情
     */
    toDetail(row: RefundRecordItem) {
      this.isShow = false // 关闭弹窗
      this.$router.push(`/training/trade/refund/personal/detail/${row.returnOrderId}`)
    }
  }
</script>

<style scoped></style>
