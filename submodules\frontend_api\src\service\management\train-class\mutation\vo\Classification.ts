/**
 * 分类大纲
 */
import { CourseLoadModeEnum } from '@api/service/management/train-class/mutation/Enum/CourseLoadMode'
import { OperationEnum } from '@api/service/management/train-class/mutation/Enum/OperationEnum'
import { ClassificationAssess } from '@api/service/management/train-class/mutation/vo/ClassificationAssess'
import { CompulsoryCourseInfo } from '@api/service/management/train-class/mutation/vo/CompulsoryCourseInfo'

class Classification {
  // region properties
  /**
   * 课程加载模式（仅修改时有效）
   */
  courseLoadMode: CourseLoadModeEnum = CourseLoadModeEnum.BY_COURSE_PACKAGE_ID
  /**
   * 默认id前缀
   */
  static classificationIdPre = 'classificationIdPre'
  /**
   * 是否处在编辑状态 //为了ui界面方便根据状态展示不同ui
   */
  isEditing = false
  /**
   * 课程数组-类型还未定义
   */
  courseList: any[] = []
  /**
   *父id，类型为string
   */
  parentId = ''
  /**
   * 课程总数
   */
  courseTotal = 0
  /**
   * 课程学时总数
   */
  coursePeriodTotal = 0
  /**
   *id，类型为string
   */
  id = ''
  /**
   *课程学习大纲名称，类型为string
   */
  name = ''
  /**
   *课程包id，类型为string
   */
  coursePackageId = ''
  /**
   *当前课程学习大纲下的必学课程列表，仅当没有childOutlines时生效，类型为string[]
   */
  compulsoryCourseIdList: string[] = []
  /**
   * 必学课程学时总数
   */
  compulsoryCoursePeriodTotal = 0
  /**
   * 必学课程总数-UI
   */
  compulsoryCourseTotal = 0
  /**
   *课程学习大纲序号，同级序号，类型为number
   */
  sort = 0
  /**
   *大纲类型，1-必修，2-选修 仅限选课规则时候选用，类型为number
   */
  category = 0
  /**
   * 分类考核对象
   */
  assessSetting = new ClassificationAssess()
  /**
   *子节点，类型为Classification[]
   */
  childOutlines: Classification[] = []
  // endregion
  // region methods
  /**
   * 删除标记
   */
  operation?: OperationEnum = undefined
  // endregion
  /**
   * 必学课程信息数组(自主选课用)
   */
  compulsoryCourseInfoList?: Array<CompulsoryCourseInfo> = new Array<CompulsoryCourseInfo>()

  /**
   *id，类型为string(自主选课复制用)
   */
  idCopy = ''

  constructor(id = '') {
    this.id = id
  }
}
export default Classification
