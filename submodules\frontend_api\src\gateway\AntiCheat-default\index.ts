import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/AntiCheat-default'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'AntiCheat-default'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum ShapeModeType {
  KnowledgePoint = 'KnowledgePoint',
  RandomQuestions = 'RandomQuestions',
  FaceRecognition = 'FaceRecognition'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

export class FaceRecognitionShapeModel {
  similarity: number
  protocolText?: string
  promptText?: string
  id?: string
  verification: number
  verificationTimes: number
  shape?: ShapeModeType
  createTime?: string
}

/**
 * 同意用户的考试申诉信息
 */
export class AgreeExamUserAppealRequest {
  /**
   * 申诉ID
   */
  id: string
  /**
   * 同意的理由
   */
  reason?: string
}

/**
 * 防作弊形式
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class AntiModeSettingRequest {
  /**
   * 验证方式，0/1，成功为止/验证次数
   */
  verification: number
  /**
   * 验证次数，当verification&#x3D;1时有效
   */
  verificationTimes: number
  /**
   * 匹配相似度
   */
  similarity: number
  /**
   * 协议文本
   */
  protocolText?: string
  /**
   * 提示信息文本
   */
  promptText?: string
  /**
   * 创建时间
   */
  createTime?: string
}

/**
 * 申请课件防作弊随机拍摄点信息
<AUTHOR>
 */
export class ApplyCoursewareRandomPointRequest {
  /**
   * 防作弊配置编号
   */
  configId?: string
  /**
   * 防作弊方式编号
   */
  modeId?: string
  /**
   * 所属平台编号
   */
  platformId?: string
  /**
   * 所属平台版本编号
   */
  platformVersionId?: string
  /**
   * 所属项目编号
   */
  projectId?: string
  /**
   * 所属子项目编号
   */
  subProjectId?: string
  /**
   * 所属组织机构编号
   */
  organizationId?: string
  /**
   * 所属单位编号
   */
  unitId?: string
  /**
   * 特征标记列表，于判断是否需要反作弊请求接口的markers保存一致
   */
  markers?: Array<MarkerRequest>
  /**
   * 用户编号
   */
  userId?: string
}

/**
 * 用户考试申诉次数查询信息
 */
export class CurrentUserExamAppealCountInfoQueryRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 学习方式ID
   */
  learningId?: string
  /**
   * 考试场次ID
   */
  examRoundId?: string
}

/**
 * 用户考试申诉查询信息
 */
export class CurrentUserExamAppealQueryRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 学习方式ID
   */
  learningId?: string
  /**
   * 考试场次ID
   */
  examRoundId?: string
}

/**
 * 基准库采集配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class DatumConfigSettingRequest {
  /**
   * 采集照片数量，默认：1
   */
  collectCount: number
  /**
   * 更新次数
   */
  updateCount: number
  /**
   * 采集协议文本
   */
  protocolText?: string
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * 基准照采集配置更新
<AUTHOR>
@date 2020/6/8
@since 1.0.0
 */
export class DatumConfigUpdateRequest {
  /**
   * 采集照片数量，默认：1
   */
  collectCount?: number
  /**
   * 更新次数
   */
  updateCount?: number
  /**
   * 采集协议文本
   */
  protocolText?: string
}

/**
 * 考试作答记录人脸识别结果查询
 */
export class ExamAnswerFSResultQueryRequest {
  /**
   * 用户ID
   */
  userId?: string
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 学习方式ID
   */
  learningId?: string
  /**
   * 考试场次ID
   */
  examRoundId?: string
}

/**
 * 用户考试答卷防作弊人脸识别记录查询
 */
export class ExamAnswerPagerFSRecordQueryRequest {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 考试作答ID
   */
  examAnswerId: string
}

/**
 * 考试过程监管行为
<AUTHOR>
@date 2020/5/25
@since 1.0.0
 */
export class ExamProcessBehaviorRequest {
  /**
   * 维度，2-时间
   */
  dimensions: number
  /**
   * 跟踪方式，0/1，精确点/范围点
   */
  traceMode: number
  /**
   * 跟踪记录点
   */
  recordPoints?: Array<string>
}

/**
 * 考试场景防作弊场景配置
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class ExamScenesConfigAddRequest {
  /**
   * 所属平台编号
   */
  platformId?: string
  /**
   * 所属平台版本编号
   */
  platformVersionId?: string
  /**
   * 所属项目编号
   */
  projectId?: string
  /**
   * 所属子项目编号
   */
  subProjectId?: string
  /**
   * 所属组织机构编号
   */
  organizationId?: string
  /**
   * 所属单位编号
   */
  unitId?: string
  /**
   * 应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
   */
  useRange: number
  /**
   * <pre>
学习方案编号，如果需要应用到学习方案，则useRange&#x3D;3且填写具体学习方案编号
</pre>
   */
  schemeId?: string
  /**
   * <pre>
考试场次编号，如果需要应用到考试场次，则useRange&#x3D;3且则填写具体的考试场次编号
</pre>
   */
  examRoundId?: string
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 当前只支持人脸识别模式
   */
  shapeModel?: FaceRecognitionShapeRequest
  /**
   * 考试前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeExamBehavior: number
  /**
   * 考试中监管行为
   */
  processBehavior?: ExamProcessBehaviorRequest
  /**
   * 考试后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterExamBehavior: number
}

/**
 * 考试防作弊配置查询
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class ExamScenesConfigQueryRequest {
  /**
   * 应用组织机构编号
   */
  organizationId?: string
  /**
   * 应用单位编号
   */
  unitId?: string
  /**
   * 学习方案编号
   */
  schemeId?: string
  /**
   * 考试场次编号
   */
  examRoundId?: string
}

/**
 * 考试场景防作弊场景配置
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class ExamScenesConfigUpdateRequest {
  /**
   * 配置编号
   */
  configId?: string
  /**
   * 应用组织机构编号,useRange&#x3D;2时有效
   */
  organizationId?: string
  /**
   * 应用单位编号,useRange&#x3D;1时有效
   */
  unitId?: string
  /**
   * 学习方案编号,useRange&#x3D;3时有效
   */
  schemeId?: string
  /**
   * 考试场次编号，useRange&#x3D;3时有效
   */
  examRoundId?: string
  /**
   * 应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
null表示不更新
   */
  useRange?: number
  /**
   * 是否启用,
null表示不修改
   */
  enable?: boolean
  /**
   * 是否更新监管形式
   */
  updateShapeModel: boolean
  /**
   * 监管形式，更新时需要全部更新
   */
  shapeModel?: FaceRecognitionShapeRequest
  /**
   * 是否更新考试前监管行为
   */
  updateBeforeExamBehavior: boolean
  /**
   * 考试前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeExamBehavior: number
  /**
   * 是否更新考试中监管行为
   */
  updateProcessExamBehavior: boolean
  /**
   * 考试中监管行为，如果updateProcessExamBehavior&#x3D;true,且processBehavior&#x3D;null表示移除考试中监管行为
   */
  processBehavior?: ExamProcessBehaviorRequest
  /**
   * 是否更新考试后监管行为
   */
  updateAfterExamBehavior: boolean
  /**
   * 考试后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterExamBehavior: number
}

/**
 * 用户考试申诉创建信息
 */
export class ExamUserAppealCreateRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 学习方式ID
   */
  learningId?: string
  /**
   * 考试场次ID
   */
  examRoundId?: string
  /**
   * 考试作答记录ID
   */
  examAnswerId?: string
  /**
   * 申诉原因
   */
  reason?: string
}

/**
 * 用户申诉分页查询
 */
export class ExamUserAppealPageQueryRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 用户ID集合
   */
  userIds?: Array<string>
  /**
   * 申诉创建时间起始查询
   */
  startCreateTime?: string
  /**
   * 申诉创建时间截止查询
   */
  endCreateTime?: string
  /**
   * 审批结果
@see com.fjhb.platform.core.v1.anti.api.constants.UserAppealApprovedResultConst
   */
  approvedResultList?: Array<number>
}

/**
 * 考试监管配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class ExaminationConfigSettingRequest {
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 考试前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeExamBehavior: number
  /**
   * 考试中监管行为
   */
  processBehavior?: ProcessBehaviorSettingRequest
  /**
   * 考试后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterExamBehavior: number
  /**
   * 监管形式
   */
  shapeMode?: AntiModeSettingRequest
  /**
   * 每场考试可申诉次数
   */
  appealCount: number
}

/**
 * 人脸识别模式
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class FaceRecognitionShapeRequest {
  /**
   * 编号
   */
  id?: string
  /**
   * 验证方式，0/1，成功为止/验证次数
   */
  verification: number
  /**
   * 验证次数，当verification&#x3D;1时有效
   */
  verificationTimes: number
  /**
   * 匹配相似度
   */
  similarity: number
  /**
   * 协议文本
   */
  protocolText?: string
  /**
   * 提示信息文本
   */
  promptText?: string
  /**
   * 创建时间
   */
  createTime?: string
}

/**
 * 人脸识别监管配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class FaceSupervisionSettingRequest {
  /**
   * 基准照采集配置
   */
  datumConfigSetting?: DatumConfigSettingRequest
  /**
   * 登录监管配置
   */
  loginConfigSetting?: LoginConfigSettingRequest
  /**
   * 学习监管配置
   */
  learningConfigSetting?: LearningConfigSettingRequest
  /**
   * 考试监管配置
   */
  examinationConfigSetting?: ExaminationConfigSettingRequest
}

/**
 * 学习监管配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class LearningConfigSettingRequest {
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 学习前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLearningBehavior: number
  /**
   * 学习中监管行为
   */
  processBehavior?: ProcessBehaviorSettingRequest
  /**
   * 学习后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterLearningBehavior: number
  /**
   * 监管形式
   */
  shapeMode?: AntiModeSettingRequest
  /**
   * 如果最终验证未匹配，则本次学习资源是否有效
   */
  effectiveIfNoMatch?: boolean
}

/**
 * 课程学习过程监管行为
<AUTHOR>
@date 2020/5/25
@since 1.0.0
 */
export class LearningProcessBehaviorRequest {
  /**
   * 维度，2/3，时间/进度
   */
  dimensions: number
  /**
   * 跟踪方式，0/1，精确点/范围点/随机点
   */
  traceMode: number
  /**
   * 跟踪记录点
   */
  recordPoints?: Array<string>
}

/**
 * 学习场景防作弊场景配置
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class LearningScenesConfigAddRequest {
  /**
   * 所属平台编号
   */
  platformId?: string
  /**
   * 所属平台版本编号
   */
  platformVersionId?: string
  /**
   * 所属项目编号
   */
  projectId?: string
  /**
   * 所属子项目编号
   */
  subProjectId?: string
  /**
   * 所属组织机构编号
   */
  organizationId?: string
  /**
   * 所属单位编号
   */
  unitId?: string
  /**
   * 应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
   */
  useRange: number
  /**
   * <pre>
学习方案编号，如果需要应用到学习方案，则useRange&#x3D;3且填写具体学习方案编号
</pre>
   */
  schemeId?: string
  /**
   * <pre>
课程编号，如果需要应用到课程，则useRange&#x3D;3且则填写具体的课程编号
</pre>
   */
  courseId?: string
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 当前只支持人脸识别模式
   */
  shapeModel?: FaceRecognitionShapeRequest
  /**
   * 学习前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLearningBehavior: number
  /**
   * 学习中监管行为
   */
  processLearningBehavior?: LearningProcessBehaviorRequest
  /**
   * 学习后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterLearningBehavior: number
  /**
   * 如果最终验证未匹配，则本次学习资源是否有效
   */
  effectiveIfNoMatch?: boolean
}

/**
 * 课程学习防作弊配置查询
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class LearningScenesConfigQueryRequest {
  /**
   * 应用组织机构编号
   */
  organizationId?: string
  /**
   * 应用单位编号
   */
  unitId?: string
  /**
   * 学习方案编号
   */
  schemeId?: string
  /**
   * 课程编号
   */
  courseId?: string
}

/**
 * 学习场景防作弊场景配置
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class LearningScenesConfigUpdateRequest {
  /**
   * 配置编号
   */
  configId?: string
  /**
   * 应用组织机构编号,useRange&#x3D;2时有效
   */
  organizationId?: string
  /**
   * 应用单位编号,useRange&#x3D;1时有效
   */
  unitId?: string
  /**
   * 学习方案编号,useRange&#x3D;3时有效
   */
  schemeId?: string
  /**
   * 课程编号，useRange&#x3D;3时有效
   */
  courseId?: string
  /**
   * 应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
null表示不更新
   */
  useRange?: number
  /**
   * 是否启用,
null表示不修改
   */
  enable?: boolean
  /**
   * 是否更新监管形式
   */
  updateShapeModel: boolean
  /**
   * 监管形式，更新时需要全部更新
   */
  shapeModel?: FaceRecognitionShapeRequest
  /**
   * 是否更新学习前监管行为
   */
  updateBeforeLearningBehavior: boolean
  /**
   * 学习前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLearningBehavior: number
  /**
   * 是否更新学习中监管行为
   */
  updateProcessLearningBehavior: boolean
  /**
   * 学习中监管行为，如果updateProcessLearningBehavior&#x3D;true,且processLearningBehavior&#x3D;null表示移除学习中监管行为
   */
  processLearningBehavior?: LearningProcessBehaviorRequest
  /**
   * 是否更新学习后监管行为
   */
  updateAfterLearningBehavior: boolean
  /**
   * 学习后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterLearningBehavior: number
  /**
   * 如果最终验证未匹配，则本次学习资源是否有效
   */
  effectiveIfNoMatch?: boolean
}

/**
 * 登录配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class LoginConfigSettingRequest {
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 登录前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLoginBehavior: number
  /**
   * 监管形式
   */
  shapeMode?: AntiModeSettingRequest
}

/**
 * 用户登录防作弊人脸识别记录查询
 */
export class LoginFSRecordQueryRequest {
  /**
   * 用户ID
   */
  userId?: string
}

/**
 * 登录场景防作弊场景配置
<AUTHOR>
@date 2020/5/28
@since 1.0.0
 */
export class LoginScenesConfigAddRequest {
  /**
   * 所属平台编号
   */
  platformId?: string
  /**
   * 所属平台版本编号
   */
  platformVersionId?: string
  /**
   * 所属项目编号
   */
  projectId?: string
  /**
   * 所属子项目编号
   */
  subProjectId?: string
  /**
   * 所属组织机构编号
   */
  organizationId?: string
  /**
   * 所属单位编号
   */
  unitId?: string
  /**
   * 应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
   */
  useRange: number
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 当前只支持人脸识别模式
   */
  shapeModel?: FaceRecognitionShapeModel
  /**
   * 登录前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLoginBehavior: number
}

/**
 * 登录防作弊配置查询
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class LoginScenesConfigQueryRequest {
  /**
   * 应用组织机构编号
   */
  organizationId?: string
  /**
   * 应用单位编号
   */
  unitId?: string
}

/**
 * 登录场景防作弊场景配置
<AUTHOR>
@date 2020/5/28
@since 1.0.0
 */
export class LoginScenesConfigUpdateRequest {
  /**
   * 配置编号
   */
  configId?: string
  /**
   * 应用组织机构编号,useRange&#x3D;2时有效
   */
  organizationId?: string
  /**
   * 应用单位编号,useRange&#x3D;1时有效
   */
  unitId?: string
  /**
   * 应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
null表示不更新
   */
  useRange?: number
  /**
   * 是否启用,
null表示不修改
   */
  enable?: boolean
  /**
   * 是否更新监管形式
   */
  updateShapeModel: boolean
  /**
   * 监管形式，更新时需要全部更新
   */
  shapeModel?: FaceRecognitionShapeModel
  /**
   * 是否更新学习前监管行为
   */
  updateBeforeLoginBehavior: boolean
  /**
   * 学习前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLoginBehavior: number
}

/**
 * 特征标记
<AUTHOR>
 */
export class MarkerRequest {
  key?: string
  value?: string
}

/**
 * 基准照采集配置添加
<AUTHOR>
@date 2020/6/8
@since 1.0.0
 */
export class PlatformDatumConfigAddRequest {
  /**
   * 采集照片数量，默认：1
   */
  collectCount: number
  /**
   * 更新次数
   */
  updateCount: number
  /**
   * 采集协议文本
   */
  protocolText?: string
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * 平台级别学习场景防作弊场景配置
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class PlatformLearningScenesConfigAddRequest {
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 当前只支持人脸识别模式
   */
  shapeModel?: FaceRecognitionShapeRequest
  /**
   * 学习前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLearningBehavior: number
  /**
   * 学习中监管行为
   */
  processLearningBehavior?: LearningProcessBehaviorRequest
  /**
   * 学习后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterLearningBehavior: number
  /**
   * 如果最终验证未匹配，则本次学习资源是否有效
   */
  effectiveIfNoMatch?: boolean
}

/**
 * 学习场景防作弊场景配置
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class PlatformLearningScenesConfigUpdateRequest {
  /**
   * 配置编号
   */
  configId?: string
  /**
   * 是否启用,
null表示不修改
   */
  enable?: boolean
  /**
   * 是否更新监管形式
   */
  updateShapeModel: boolean
  /**
   * 监管形式，更新时需要全部更新
   */
  shapeModel?: FaceRecognitionShapeRequest
  /**
   * 是否更新学习前监管行为
   */
  updateBeforeLearningBehavior: boolean
  /**
   * 学习前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLearningBehavior: number
  /**
   * 是否更新学习中监管行为
   */
  updateProcessLearningBehavior: boolean
  /**
   * 学习中监管行为，如果updateProcessLearningBehavior&#x3D;true,且processLearningBehavior&#x3D;null表示移除学习中监管行为
   */
  processLearningBehavior?: LearningProcessBehaviorRequest
  /**
   * 是否更新学习后监管行为
   */
  updateAfterLearningBehavior: boolean
  /**
   * 学习后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterLearningBehavior: number
  /**
   * 如果最终验证未匹配，则本次学习资源是否有效
   */
  effectiveIfNoMatch?: boolean
}

/**
 * 过程监管行为配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class ProcessBehaviorSettingRequest {
  /**
   * 维度，2/3，时间/进度
   */
  dimensions: number
  /**
   * 跟踪方式，0/1，精确点/范围点
   */
  traceMode: number
  /**
   * 跟踪记录点
   */
  recordPoints?: Array<string>
}

/**
 * 拒绝用户的考试申诉信息
 */
export class RejectExamUserAppealRequest {
  /**
   * 申诉ID
   */
  id: string
  /**
   * 拒绝的理由
   */
  reason?: string
}

/**
 * 用户指定学习方案下课程人脸识别记录查询信息
 */
export class SchemeCourseFSRecordsQueryRequest {
  /**
   * 用户ID
   */
  userId?: string
  /**
   * 学习方案ID
   */
  schemeId?: string
}

/**
 * 用户指定学习方案下课件人脸识别记录查询信息
 */
export class SchemeCoursewareFSRecordsQueryRequest {
  /**
   * 用户ID
   */
  userId?: string
  /**
   * 学习方案ID
   */
  schemeId?: string
}

/**
 * 用户指定学习方案下考试人脸识别记录查询信息
 */
export class SchemeExamFSRecordQueryRequest {
  /**
   * 用户ID
   */
  userId?: string
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 跟踪维度列表，-1/0/1/2/3/4，全部/进入场景/退出场景/时间/进度/具体项目
   */
  dimensionsList?: Array<number>
}

export class SimilarResult {
  similar: number
  result: boolean
  errorInfo: string
}

export class FaceRecognitionShapeModel1 {
  similarity: number
  protocolText: string
  promptText: string
  id: string
  verification: number
  verificationTimes: number
  shape: ShapeModeType
  createTime: string
}

/**
 * 考试过程监管行为
<AUTHOR>
@date 2020/5/25
@since 1.0.0
 */
export class ExamProcessBehaviorRequest1 {
  /**
   * 维度，2-时间
   */
  dimensions: number
  /**
   * 跟踪方式，0/1，精确点/范围点
   */
  traceMode: number
  /**
   * 跟踪记录点
   */
  recordPoints: Array<string>
}

/**
 * 人脸识别模式
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class FaceRecognitionShapeRequest1 {
  /**
   * 编号
   */
  id: string
  /**
   * 验证方式，0/1，成功为止/验证次数
   */
  verification: number
  /**
   * 验证次数，当verification&#x3D;1时有效
   */
  verificationTimes: number
  /**
   * 匹配相似度
   */
  similarity: number
  /**
   * 协议文本
   */
  protocolText: string
  /**
   * 提示信息文本
   */
  promptText: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 课程学习过程监管行为
<AUTHOR>
@date 2020/5/25
@since 1.0.0
 */
export class LearningProcessBehaviorRequest1 {
  /**
   * 维度，2/3，时间/进度
   */
  dimensions: number
  /**
   * 跟踪方式，0/1，精确点/范围点/随机点
   */
  traceMode: number
  /**
   * 跟踪记录点
   */
  recordPoints: Array<string>
}

/**
 * 防作弊形式
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class AntiModeSettingResponse {
  /**
   * 验证方式，0/1，成功为止/验证次数
   */
  verification: number
  /**
   * 验证次数，当verification&#x3D;1时有效
   */
  verificationTimes: number
  /**
   * 匹配相似度
   */
  similarity: number
  /**
   * 协议文本
   */
  protocolText: string
  /**
   * 提示信息文本
   */
  promptText: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 用户课程防作弊人脸识别记录信息
 */
export class CourseFSInfoResponse {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 课程包ID
   */
  packageId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 课程的人脸识别记录
   */
  records: Array<CourseFSRecordResponse>
}

/**
 * 课程防作弊人脸识别记录信息
 */
export class CourseFSRecordResponse {
  /**
   * 记录ID
   */
  id: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 课程包ID
   */
  packageId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 跟踪维度，0/1/2/3/4，进入场景/退出场景/时间/进度/具体项目
   */
  dimensions: number
  /**
   * 跟踪记录点，当dimensions&#x3D;0/1时，当前无值
当dimensions&#x3D;2时，单位：秒
当dimensions&#x3D;3时，单位：百分比
当dimensions&#x3D;4时，具体编号
   */
  recordPoint: string
  /**
   * 基准照片路径
   */
  standardAnswer: string
  /**
   * 单次拍照路径
   */
  currentAnswer: string
  /**
   * 达标对比值，匹配相似度
   */
  standardValue: string
  /**
   * 当前对比值，当前相似度
   */
  currentValue: string
  /**
   * 记录点对应的时间点，单位：秒，如：学习对应时长，考试对应当前考试时间
   */
  timeLength: number
  /**
   * 记录点对应的进度百分比，如：学习对应进度，考试对应的考试进度
   */
  schedule: number
  /**
   * 验证结果
   */
  result: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
}

/**
 * 用户课件防作弊人脸识别记录信息
 */
export class CoursewareFSInfoResponse {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 课程包ID
   */
  packageId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 课件ID
   */
  coursewareId: string
  /**
   * 课件的人脸识别记录
   */
  records: Array<CoursewareFSRecordResponse>
}

/**
 * 课件防作弊人脸识别记录信息
 */
export class CoursewareFSRecordResponse {
  /**
   * 记录ID
   */
  id: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 课程包ID
   */
  packageId: string
  /**
   * 课程ID
   */
  courseId: string
  /**
   * 课件ID
   */
  coursewareId: string
  /**
   * 跟踪维度，0/1/2/3/4，进入场景/退出场景/时间/进度/具体项目
   */
  dimensions: number
  /**
   * 跟踪记录点，当dimensions&#x3D;0/1时，当前无值
当dimensions&#x3D;2时，单位：秒
当dimensions&#x3D;3时，单位：百分比
当dimensions&#x3D;4时，具体编号
   */
  recordPoint: string
  /**
   * 基准照片路径
   */
  standardAnswer: string
  /**
   * 单次拍照路径
   */
  currentAnswer: string
  /**
   * 达标对比值，匹配相似度
   */
  standardValue: string
  /**
   * 当前对比值，当前相似度
   */
  currentValue: string
  /**
   * 记录点对应的时间点，单位：秒，如：学习对应时长，考试对应当前考试时间
   */
  timeLength: number
  /**
   * 记录点对应的进度百分比，如：学习对应进度，考试对应的考试进度
   */
  schedule: number
  /**
   * 验证结果
   */
  result: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
}

/**
 * 课件防作弊随机拍摄点信息
 */
export class CoursewareRandomPointResponse {
  /**
   * 拍摄点，单位百分比进度
   */
  recordPoints: Array<number>
}

/**
 * 采集基准照配置
<AUTHOR>
@date 2020/6/8
@since 1.0.0
 */
export class DatumConfigResponse {
  /**
   * 基准照片采集配置编号
   */
  id: string
  /**
   * 所属平台编号
   */
  platformId: string
  /**
   * 所属平台版本编号
   */
  platformVersionId: string
  /**
   * 所属项目编号
   */
  projectId: string
  /**
   * 所属子项目编号
   */
  subProjectId: string
  /**
   * 所属组织机构编号
   */
  organizationId: string
  /**
   * 所属单位编号
   */
  unitId: string
  /**
   * 采集照片数量，默认：1
   */
  collectCount: number
  /**
   * 更新次数
   */
  updateCount: number
  /**
   * 采集协议文本
   */
  protocolText: string
  /**
   * 创建人
   */
  createUserId: string
  /**
   * 最后更新人
   */
  updateUserId: string
  /**
   * 最后更新时间
   */
  updateTime: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * 基准库采集配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class DatumConfigSettingResponse {
  /**
   * 采集照片数量，默认：1
   */
  collectCount: number
  /**
   * 更新次数
   */
  updateCount: number
  /**
   * 采集协议文本
   */
  protocolText: string
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 配置id
   */
  id: string
}

/**
 * 考试作答记录人脸识别结果
 */
export class ExamAnswerFSResultResponse {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 考试场次ID
   */
  examRoundId: string
  /**
   * 考试作答记录ID
   */
  examAnswerId: string
  /**
   * 是否通过
   */
  passed: boolean
  /**
   * 人脸拍摄总点数
   */
  totalPointCount: number
  /**
   * 通过的人脸拍摄总点数
   */
  passedPointCount: number
}

/**
 * 用户考试防作弊人脸识别记录信息
 */
export class ExamFSInfoResponse {
  /**
   * 用户ID
   */
  userId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 考试场次ID
   */
  examRoundId: string
  /**
   * 考试场次的人脸识别记录
   */
  records: Array<ExamFSRecordResponse>
  /**
   * 用户答卷ID
   */
  examAnswerId: string
}

/**
 * 考试防作弊人脸识别记录信息
 */
export class ExamFSRecordResponse {
  /**
   * 记录ID
   */
  id: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 考试场次ID
   */
  examRoundId: string
  /**
   * 用户答卷ID
   */
  examAnswerId: string
  /**
   * 跟踪维度，0/1/2/3/4，进入场景/退出场景/时间/进度/具体项目
   */
  dimensions: number
  /**
   * 跟踪记录点，当dimensions&#x3D;0/1时，当前无值
当dimensions&#x3D;2时，单位：秒
当dimensions&#x3D;3时，单位：百分比
当dimensions&#x3D;4时，具体编号
   */
  recordPoint: string
  /**
   * 基准照片路径
   */
  standardAnswer: string
  /**
   * 单次拍照路径
   */
  currentAnswer: string
  /**
   * 达标对比值，匹配相似度
   */
  standardValue: string
  /**
   * 当前对比值，当前相似度
   */
  currentValue: string
  /**
   * 记录点对应的时间点，单位：秒，如：学习对应时长，考试对应当前考试时间
   */
  timeLength: number
  /**
   * 记录点对应的进度百分比，如：学习对应进度，考试对应的考试进度
   */
  schedule: number
  /**
   * 验证结果
   */
  result: boolean
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 更新时间
   */
  updateTime: string
}

/**
 * 考试场景防作弊配置
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class ExamScenesConfigResponse {
  /**
   * 防作弊配置编号
   */
  id: string
  /**
   * 所属平台编号
   */
  platformId: string
  /**
   * 所属平台版本编号
   */
  platformVersionId: string
  /**
   * 所属项目编号
   */
  projectId: string
  /**
   * 所属子项目编号
   */
  subProjectId: string
  /**
   * 所属组织机构编号
   */
  organizationId: string
  /**
   * 所属单位编号
   */
  unitId: string
  /**
   * <pre>
考试方案编号，如果需要应用到考试方案，则useRange&#x3D;3且填写具体考试方案编号
</pre>
   */
  schemeId: string
  /**
   * <pre>
考试场次编号，如果需要应用到考试场次，则useRange&#x3D;3且则填写具体的考试场次编号
</pre>
   */
  examRoundId: string
  /**
   * 应用场景,0/1/2,考试/考试/登录
   */
  usageScenarios: number
  /**
   * 应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
   */
  useRange: number
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 当前只支持人脸识别模式
   */
  shapeModel: FaceRecognitionShapeRequest1
  /**
   * 考试前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeExamBehavior: number
  /**
   * 考试中监管行为
   */
  processExamBehavior: ExamProcessBehaviorRequest1
  /**
   * 考试后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterExamBehavior: number
  /**
   * 配置时间
   */
  createTime: string
  /**
   * 创建人编号
   */
  createUserId: string
  /**
   * 最后更新人
   */
  updateUserId: string
  /**
   * 最后更新时间
   */
  updateTime: string
}

/**
 * 用户考试申诉次数信息
 */
export class ExamUserAppealCountInfoResponse {
  /**
   * 允许申诉次数
   */
  allowAppealCount: number
  /**
   * 已申诉次数
   */
  appealedCount: number
}

/**
 * 用户考试申诉信息
 */
export class ExamUserAppealResponse {
  /**
   * 申诉ID
   */
  id: string
  /**
   * 平台ID
   */
  platformId: string
  /**
   * 平台版本ID
   */
  platformVersionId: string
  /**
   * 项目ID
   */
  projectId: string
  /**
   * 子项目ID
   */
  subProjectId: string
  /**
   * 单位ID
   */
  unitId: string
  /**
   * 组织机构ID
   */
  organizationId: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 考试场次ID
   */
  examRoundId: string
  /**
   * 考试作答记录ID
   */
  examAnswerId: string
  /**
   * 申诉原因
   */
  reason: string
  /**
   * 申诉审批结果
@see UserAppealApprovedResultConst
   */
  approvedResult: number
  /**
   * 申诉审批结果备注
   */
  approvedRemark: string
  /**
   * 审批时间
   */
  approvedTime: string
  /**
   * 审批人ID
   */
  approvedUserId: string
  /**
   * 申诉创建时间
   */
  createTime: string
  /**
   * 创建人ID
   */
  createUserId: string
}

/**
 * 考试监管配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class ExaminationConfigSettingResponse {
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 考试前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeExamBehavior: number
  /**
   * 考试中监管行为
   */
  processBehavior: ProcessBehaviorSettingResponse
  /**
   * 考试后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterExamBehavior: number
  /**
   * 监管形式
   */
  shapeMode: AntiModeSettingResponse
  /**
   * 每场考试可申诉次数
   */
  appealCount: number
}

/**
 * 人脸识别监管配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class FaceSupervisionSettingResponse {
  /**
   * 基准照采集配置
   */
  datumConfigSetting: DatumConfigSettingResponse
  /**
   * 登录监管配置
   */
  loginConfigSetting: LoginConfigSettingResponse
  /**
   * 学习监管配置
   */
  learningConfigSetting: LearningConfigSettingResponse
  /**
   * 考试监管配置
   */
  examinationConfigSetting: ExaminationConfigSettingResponse
}

/**
 * 学习监管配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class LearningConfigSettingResponse {
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 学习前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLearningBehavior: number
  /**
   * 学习中监管行为
   */
  processBehavior: ProcessBehaviorSettingResponse
  /**
   * 学习后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterLearningBehavior: number
  /**
   * 监管形式
   */
  shapeMode: AntiModeSettingResponse
  /**
   * 如果最终验证未匹配，则本次学习资源是否有效
   */
  effectiveIfNoMatch: boolean
}

/**
 * 课程学习场景防作弊配置
<AUTHOR>
@date 2020/5/27
@since 1.0.0
 */
export class LearningScenesConfigResponse {
  /**
   * 防作弊配置编号
   */
  id: string
  /**
   * 所属平台编号
   */
  platformId: string
  /**
   * 所属平台版本编号
   */
  platformVersionId: string
  /**
   * 所属项目编号
   */
  projectId: string
  /**
   * 所属子项目编号
   */
  subProjectId: string
  /**
   * 所属组织机构编号
   */
  organizationId: string
  /**
   * 所属单位编号
   */
  unitId: string
  /**
   * <pre>
学习方案编号，如果需要应用到学习方案，则useRange&#x3D;3且填写具体学习方案编号
</pre>
   */
  schemeId: string
  /**
   * <pre>
课程编号，如果需要应用到课程，则useRange&#x3D;3且则填写具体的课程编号
</pre>
   */
  courseId: string
  /**
   * 应用场景,0/1/2,学习/考试/登录
   */
  usageScenarios: number
  /**
   * 应用范围，0/1/2/3/4，子项目/单位/组织机构/具体资源/平台
   */
  useRange: number
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 当前只支持人脸识别模式
   */
  shapeModel: FaceRecognitionShapeRequest1
  /**
   * 学习前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLearningBehavior: number
  /**
   * 学习中监管行为
   */
  processLearningBehavior: LearningProcessBehaviorRequest1
  /**
   * 学习后监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  afterLearningBehavior: number
  /**
   * 如果最终验证未匹配，则本次学习资源是否有效
   */
  effectiveIfNoMatch: boolean
  /**
   * 配置时间
   */
  createTime: string
  /**
   * 创建人编号
   */
  createUserId: string
  /**
   * 最后更新人
   */
  updateUserId: string
  /**
   * 最后更新时间
   */
  updateTime: string
}

/**
 * 登录配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class LoginConfigSettingResponse {
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 登录前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLoginBehavior: number
  /**
   * 监管形式
   */
  shapeMode: AntiModeSettingResponse
}

/**
 * 用户登录防作弊记录
 */
export class LoginFSRecordResponse {
  /**
   * 记录ID
   */
  id: string
  /**
   * 用户ID
   */
  userId: string
  /**
   * 跟踪维度，0/1/2/3/4，进入场景/退出场景/时间/进度/具体项目
   */
  dimensions: number
  /**
   * 跟踪记录点，当dimensions&#x3D;0/1时，当前无值
当dimensions&#x3D;2时，单位：秒
当dimensions&#x3D;3时，单位：百分比
当dimensions&#x3D;4时，具体编号
   */
  recordPoint: string
  /**
   * 基准照片路径
   */
  standardAnswer: string
  /**
   * 单次拍照路径
   */
  currentAnswer: string
  /**
   * 达标对比值，匹配相似度
   */
  standardValue: string
  /**
   * 当前对比值，当前相似度
   */
  currentValue: string
  /**
   * 记录点对应的时间点，单位：秒，如：学习对应时长，考试对应当前考试时间
   */
  timeLength: number
  /**
   * 记录点对应的进度百分比，如：学习对应进度，考试对应的考试进度
   */
  schedule: number
  /**
   * 验证结果
   */
  result: boolean
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 登录场景防作弊配置
<AUTHOR>
@date 2020/5/28
@since 1.0.0
 */
export class LoginScenesConfigResponse {
  /**
   * 防作弊配置编号
   */
  id: string
  /**
   * 所属平台编号
   */
  platformId: string
  /**
   * 所属平台版本编号
   */
  platformVersionId: string
  /**
   * 所属项目编号
   */
  projectId: string
  /**
   * 所属子项目编号
   */
  subProjectId: string
  /**
   * 所属组织机构编号
   */
  organizationId: string
  /**
   * 所属单位编号
   */
  unitId: string
  /**
   * 应用场景,0/1/2,学习/考试/登录
   */
  usageScenarios: number
  /**
   * 应用范围，0/1/2/3，子项目/单位/组织机构/具体资源
   */
  useRange: number
  /**
   * 是否启用
   */
  enable: boolean
  /**
   * 当前只支持人脸识别模式
   */
  shapeModel: FaceRecognitionShapeModel1
  /**
   * 学习前监管行为，-1/0/1|不生效/每次生效/生效一次
   */
  beforeLoginBehavior: number
  /**
   * 配置时间
   */
  createTime: string
  /**
   * 创建人编号
   */
  createUserId: string
  /**
   * 最后更新人
   */
  updateUserId: string
  /**
   * 最后更新时间
   */
  updateTime: string
}

/**
 * 采集基准照配置
<AUTHOR>
@date 2020/6/8
@since 1.0.0
 */
export class PlatformDatumConfigResponse {
  /**
   * 基准照片采集配置编号
   */
  id: string
  /**
   * 采集照片数量，默认：1
   */
  collectCount: number
  /**
   * 更新次数
   */
  updateCount: number
  /**
   * 采集协议文本
   */
  protocolText: string
  /**
   * 创建人
   */
  createUserId: string
  /**
   * 最后更新人
   */
  updateUserId: string
  /**
   * 最后更新时间
   */
  updateTime: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 是否启用
   */
  enable: boolean
}

/**
 * 过程监管行为配置
<AUTHOR>
@date 2020/7/23
@since 1.0.0
 */
export class ProcessBehaviorSettingResponse {
  /**
   * 维度，2/3，时间/进度
   */
  dimensions: number
  /**
   * 跟踪方式，0/1，精确点/范围点/随机点
   */
  traceMode: number
  /**
   * 跟踪记录点
   */
  recordPoints: Array<string>
}

/**
 * 对比结果信息
<AUTHOR>
@since 2020/06/05
 */
export class SimilarResultResponse {
  /**
   * 相似度
   */
  similar: number
  /**
   * 对比结果，true/false,成功/失败
   */
  result: boolean
  /**
   * 失败信息
   */
  errorInfo: string
}

/**
 * eleven
 */
export class UserDatumPhotoChangeRecordResponse {
  /**
   * 基准照id
   */
  id: string
  /**
   * 原始基准照信息
   */
  original: UserDatumResponse
  /**
   * 更新后基准照信息
   */
  target: UserDatumResponse
  /**
   * 操作人编号
   */
  operatorId: string
  /**
   * 更新时间
   */
  updateTime: string
}

/**
 * 用户基准照信息
<AUTHOR>
@date 2020/5/25
@since 1.0.0
 */
export class UserDatumResponse {
  /**
   * 用户编号
   */
  userId: string
  /**
   * 基准照相对路径，不包含msf
   */
  photoPaths: Array<string>
  /**
   * 允许更新次数
   */
  allowUpdateCount: number
  /**
   * 已更新次数
   */
  updateCount: number
  /**
   * 最后更新时间
   */
  lastUpdateTime: string
}

export class CourseFSInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CourseFSInfoResponse>
}

export class CoursewareFSInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<CoursewareFSInfoResponse>
}

export class ExamFSInfoResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ExamFSInfoResponse>
}

export class ExamUserAppealResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<ExamUserAppealResponse>
}

export class LoginFSRecordResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<LoginFSRecordResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 查询用户一场考试下所有作答记录人脸识别结果
   * @param queryInfo 查询信息
   * @param query 查询 graphql 语法文档
   * @param queryInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllUserExamAnswerFSResults(
    queryInfo: ExamAnswerFSResultQueryRequest,
    query: DocumentNode = GraphqlImporter.findAllUserExamAnswerFSResults,
    operation?: string
  ): Promise<Response<Array<ExamAnswerFSResultResponse>>> {
    return commonRequestApi<Array<ExamAnswerFSResultResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { queryInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询用户指定学习方案的所有课程人脸识别防作弊记录
   * @param page 分页信息
   * @param queryInfo 查询信息
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllUserSchemeCourseFSRecordPage(
    params: { page?: Page; queryInfo?: SchemeCourseFSRecordsQueryRequest },
    query: DocumentNode = GraphqlImporter.findAllUserSchemeCourseFSRecordPage,
    operation?: string
  ): Promise<Response<CourseFSInfoResponsePage>> {
    return commonRequestApi<CourseFSInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询用户指定学习方案的所有课件人脸识别防作弊记录
   * @param page 分页信息
   * @param queryInfo 查询信息
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllUserSchemeCoursewareFSRecordPage(
    params: { page?: Page; queryInfo?: SchemeCoursewareFSRecordsQueryRequest },
    query: DocumentNode = GraphqlImporter.findAllUserSchemeCoursewareFSRecordPage,
    operation?: string
  ): Promise<Response<CoursewareFSInfoResponsePage>> {
    return commonRequestApi<CoursewareFSInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询用户指定学习方案的所有课件人脸识别防作弊记录，学习过程的相同拍摄点只返回最新的一次拍摄记录
   * @param page 分页信息
   * @param queryInfo 查询信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllUserSchemeCoursewareFSRecordPageByLasted(
    params: { page?: Page; queryInfo?: SchemeCoursewareFSRecordsQueryRequest },
    query: DocumentNode = GraphqlImporter.findAllUserSchemeCoursewareFSRecordPageByLasted,
    operation?: string
  ): Promise<Response<CoursewareFSInfoResponsePage>> {
    return commonRequestApi<CoursewareFSInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询用户指定学习方案的所有考试人脸识别防作弊记录
   * @param page 分页信息
   * @param queryInfo 查询信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findAllUserSchemeExamFSRecordPage(
    params: { page?: Page; queryInfo?: SchemeExamFSRecordQueryRequest },
    query: DocumentNode = GraphqlImporter.findAllUserSchemeExamFSRecordPage,
    operation?: string
  ): Promise<Response<ExamFSInfoResponsePage>> {
    return commonRequestApi<ExamFSInfoResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询子项目下的人脸识别采集配置
   * @return 采集配置
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findDatumConfig(
    query: DocumentNode = GraphqlImporter.findDatumConfig,
    operation?: string
  ): Promise<Response<DatumConfigResponse>> {
    return commonRequestApi<DatumConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询平台下的人脸识别采集配置
   * @return 采集配置
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findDatumConfigByPlatform(
    query: DocumentNode = GraphqlImporter.findDatumConfigByPlatform,
    operation?: string
  ): Promise<Response<PlatformDatumConfigResponse>> {
    return commonRequestApi<PlatformDatumConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询当前用户指定考试的申诉次数信息
   * @param queryInfo 查询信息
   * @param query 查询 graphql 语法文档
   * @param queryInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findExamAppealCountInfoByCurrentUser(
    queryInfo: CurrentUserExamAppealCountInfoQueryRequest,
    query: DocumentNode = GraphqlImporter.findExamAppealCountInfoByCurrentUser,
    operation?: string
  ): Promise<Response<ExamUserAppealCountInfoResponse>> {
    return commonRequestApi<ExamUserAppealCountInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { queryInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询当前用户考试申诉
   * @param queryInfo 查询信息
   * @param query 查询 graphql 语法文档
   * @param queryInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findExamAppealListByCurrentUser(
    queryInfo: CurrentUserExamAppealQueryRequest,
    query: DocumentNode = GraphqlImporter.findExamAppealListByCurrentUser,
    operation?: string
  ): Promise<Response<Array<ExamUserAppealResponse>>> {
    return commonRequestApi<Array<ExamUserAppealResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { queryInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * <pre>
   * 按条件查询平台中已启用的考试防作弊配置，
   * 如果指定条件不存在，则进行向上查询，查询步骤为：试卷配置->学习方案->单位->组织机构->子项目
   * </pre>
   * @param configQuery 查询条件
   * @return 防作弊配置编号，如果为null则表示没有找到配置
   * @param query 查询 graphql 语法文档
   * @param configQuery 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findExamScenesConfig(
    configQuery: ExamScenesConfigQueryRequest,
    query: DocumentNode = GraphqlImporter.findExamScenesConfig,
    operation?: string
  ): Promise<Response<ExamScenesConfigResponse>> {
    return commonRequestApi<ExamScenesConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { configQuery },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询考试场景配置，如果提供的配置编号不是考试场景，则抛出异常
   * @param configId 考试场景配置编号
   * @return 配置信息
   * @param query 查询 graphql 语法文档
   * @param configId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findExamScenesConfigInfo(
    configId: string,
    query: DocumentNode = GraphqlImporter.findExamScenesConfigInfo,
    operation?: string
  ): Promise<Response<ExamScenesConfigResponse>> {
    return commonRequestApi<ExamScenesConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { configId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取用户考试申诉
   * @param id 申诉ID
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findExamUserAppealById(
    id: string,
    query: DocumentNode = GraphqlImporter.findExamUserAppealById,
    operation?: string
  ): Promise<Response<ExamUserAppealResponse>> {
    return commonRequestApi<ExamUserAppealResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询用户考试申诉
   * @param pageIndex 当前页码
   * @param pageSize 分页大小
   * @param queryInfo 查询信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findExamUserAppealPageList(
    params: { pageIndex: number; pageSize: number; queryInfo?: ExamUserAppealPageQueryRequest },
    query: DocumentNode = GraphqlImporter.findExamUserAppealPageList,
    operation?: string
  ): Promise<Response<ExamUserAppealResponsePage>> {
    return commonRequestApi<ExamUserAppealResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * <pre>
   * 按条件查询平台中已启用的课程学习防作弊配置，
   * 如果指定条件不存在，则进行向上查询，查询步骤为：课程->学习方案->单位->组织机构->子项目
   * </pre>
   * @param configQuery 查询条件
   * @return 防作弊配置编号，如果为null则表示没有找到配置
   * @param query 查询 graphql 语法文档
   * @param configQuery 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findLearningScenesConfig(
    configQuery: LearningScenesConfigQueryRequest,
    query: DocumentNode = GraphqlImporter.findLearningScenesConfig,
    operation?: string
  ): Promise<Response<LearningScenesConfigResponse>> {
    return commonRequestApi<LearningScenesConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { configQuery },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询课程学习场景配置，如果提供的配置编号不是课程学习场景，则抛出异常
   * @param configId 课程学习场景配置编号
   * @return 配置信息
   * @param query 查询 graphql 语法文档
   * @param configId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findLearningScenesConfigInfo(
    configId: string,
    query: DocumentNode = GraphqlImporter.findLearningScenesConfigInfo,
    operation?: string
  ): Promise<Response<LearningScenesConfigResponse>> {
    return commonRequestApi<LearningScenesConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { configId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * <pre>
   * 按条件查询平台中已启用的登录防作弊配置，
   * 如果指定条件不存在，则进行向上查询，查询步骤为：单位->组织机构->子项目
   * </pre>
   * @param configQuery 查询条件
   * @return 防作弊配置编号
   * @param query 查询 graphql 语法文档
   * @param configQuery 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findLoginScenesConfig(
    configQuery: LoginScenesConfigQueryRequest,
    query: DocumentNode = GraphqlImporter.findLoginScenesConfig,
    operation?: string
  ): Promise<Response<LoginScenesConfigResponse>> {
    return commonRequestApi<LoginScenesConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { configQuery },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询登录场景配置，如果提供的配置编号不是登录场景，则抛出异常
   * @param configId 登录场景配置编号
   * @return 配置信息
   * @param query 查询 graphql 语法文档
   * @param configId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findLoginScenesConfigInfo(
    configId: string,
    query: DocumentNode = GraphqlImporter.findLoginScenesConfigInfo,
    operation?: string
  ): Promise<Response<LoginScenesConfigResponse>> {
    return commonRequestApi<LoginScenesConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { configId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询平台级的防作弊配置
   * @return 配置信息
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findPlatformLearningScenesConfigInfo(
    query: DocumentNode = GraphqlImporter.findPlatformLearningScenesConfigInfo,
    operation?: string
  ): Promise<Response<LearningScenesConfigResponse>> {
    return commonRequestApi<LearningScenesConfigResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建和更新单位级别人脸识别监管配置
   * @param unitId 单位编号
   * @return 人脸识别监管配置
   * @param query 查询 graphql 语法文档
   * @param unitId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findSupervisionConfigByByUnit(
    unitId: string,
    query: DocumentNode = GraphqlImporter.findSupervisionConfigByByUnit,
    operation?: string
  ): Promise<Response<FaceSupervisionSettingResponse>> {
    return commonRequestApi<FaceSupervisionSettingResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { unitId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取平台级别人脸识别监管配置
   * @return 人脸识别监管配置
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findSupervisionConfigByPlatform(
    query: DocumentNode = GraphqlImporter.findSupervisionConfigByPlatform,
    operation?: string
  ): Promise<Response<FaceSupervisionSettingResponse>> {
    return commonRequestApi<FaceSupervisionSettingResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取子项目级别人脸识别监管配置
   * @return 人脸识别监管配置
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findSupervisionConfigBySubProject(
    query: DocumentNode = GraphqlImporter.findSupervisionConfigBySubProject,
    operation?: string
  ): Promise<Response<FaceSupervisionSettingResponse>> {
    return commonRequestApi<FaceSupervisionSettingResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询用户基准照信息
   * @param userId 用户编号
   * @return 基准照信息
   * @param query 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findUserDatum(
    userId: string,
    query: DocumentNode = GraphqlImporter.findUserDatum,
    operation?: string
  ): Promise<Response<UserDatumResponse>> {
    return commonRequestApi<UserDatumResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { userId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询用户指定考试答卷的人脸识别防作弊记录
   * @param queryInfo 查询信息
   * @param query 查询 graphql 语法文档
   * @param queryInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findUserExamAnswerPagerFSRecords(
    queryInfo: ExamAnswerPagerFSRecordQueryRequest,
    query: DocumentNode = GraphqlImporter.findUserExamAnswerPagerFSRecords,
    operation?: string
  ): Promise<Response<Array<ExamFSRecordResponse>>> {
    return commonRequestApi<Array<ExamFSRecordResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { queryInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询用户指定考试答卷的人脸识别防作弊记录，考试过程的相同拍摄点只返回最新的一次拍摄记录，进入考试和退出考试全部返回
   * @param queryInfo 查询信息
   * @param query 查询 graphql 语法文档
   * @param queryInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findUserExamAnswerPagerFSRecordsByLasted(
    queryInfo: ExamAnswerPagerFSRecordQueryRequest,
    query: DocumentNode = GraphqlImporter.findUserExamAnswerPagerFSRecordsByLasted,
    operation?: string
  ): Promise<Response<Array<ExamFSRecordResponse>>> {
    return commonRequestApi<Array<ExamFSRecordResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { queryInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 分页查询用户登录人脸识别防作弊记录
   * @param page 分页信息
   * @param queryInfo 查询信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findUserLoginFSRecordPage(
    params: { page?: Page; queryInfo?: LoginFSRecordQueryRequest },
    query: DocumentNode = GraphqlImporter.findUserLoginFSRecordPage,
    operation?: string
  ): Promise<Response<LoginFSRecordResponsePage>> {
    return commonRequestApi<LoginFSRecordResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 查询某用户基准照的变更记录
   * @param userId  用户编号
   * @return 变更记录列表
   * @param query 查询 graphql 语法文档
   * @param userId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listUserDatumPhotoChangeRecord(
    userId: string,
    query: DocumentNode = GraphqlImporter.listUserDatumPhotoChangeRecord,
    operation?: string
  ): Promise<Response<Array<UserDatumPhotoChangeRecordResponse>>> {
    return commonRequestApi<Array<UserDatumPhotoChangeRecordResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { userId },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 添加考试场景防作弊配置
   * @param config 配置信息
   * @return 配置信息
   * @param mutate 查询 graphql 语法文档
   * @param config 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addExamScenesConfig(
    config: ExamScenesConfigAddRequest,
    mutate: DocumentNode = GraphqlImporter.addExamScenesConfig,
    operation?: string
  ): Promise<Response<ExamScenesConfigResponse>> {
    return commonRequestApi<ExamScenesConfigResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { config },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 添加课程场景防作弊配置
   * @param config 配置信息
   * @return 配置信息
   * @param mutate 查询 graphql 语法文档
   * @param config 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addLearningScenesConfig(
    config: LearningScenesConfigAddRequest,
    mutate: DocumentNode = GraphqlImporter.addLearningScenesConfig,
    operation?: string
  ): Promise<Response<LearningScenesConfigResponse>> {
    return commonRequestApi<LearningScenesConfigResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { config },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 添加登录场景防作弊配置
   * @param config 配置信息
   * @return 配置信息
   * @param mutate 查询 graphql 语法文档
   * @param config 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addLoginScenesConfig(
    config: LoginScenesConfigAddRequest,
    mutate: DocumentNode = GraphqlImporter.addLoginScenesConfig,
    operation?: string
  ): Promise<Response<LoginScenesConfigResponse>> {
    return commonRequestApi<LoginScenesConfigResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { config },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 添加平台级课程场景防作弊配置
   * @param config 配置信息
   * @return 配置信息
   * @param mutate 查询 graphql 语法文档
   * @param config 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addPlatformLearningScenesConfig(
    config: PlatformLearningScenesConfigAddRequest,
    mutate: DocumentNode = GraphqlImporter.addPlatformLearningScenesConfig,
    operation?: string
  ): Promise<Response<LearningScenesConfigResponse>> {
    return commonRequestApi<LearningScenesConfigResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { config },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 同意用户考试申诉
   * @param agreeInfo 同意信息
   * @param mutate 查询 graphql 语法文档
   * @param agreeInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeAppeal(
    agreeInfo: AgreeExamUserAppealRequest,
    mutate: DocumentNode = GraphqlImporter.agreeAppeal,
    operation?: string
  ): Promise<Response<ExamUserAppealResponse>> {
    return commonRequestApi<ExamUserAppealResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { agreeInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 用户新增基准照，原有的基准照不变，新增一张基准照
   * @param photoPath 基准照照片路径
   * @param mutate 查询 graphql 语法文档
   * @param photoPath 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async appendPhoto(
    photoPath: string,
    mutate: DocumentNode = GraphqlImporter.appendPhoto,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { photoPath },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请课件防作弊随机拍摄点信息
   * @param applyInfo 申请信息
   * @return 课件防作弊随机拍摄点
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyCoursewareRandomFacePoints(
    applyInfo: ApplyCoursewareRandomPointRequest,
    mutate: DocumentNode = GraphqlImporter.applyCoursewareRandomFacePoints,
    operation?: string
  ): Promise<Response<CoursewareRandomPointResponse>> {
    return commonRequestApi<CoursewareRandomPointResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { applyInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 对比照片
   * @param photo1 第一张照片Base64字符串
   * @param photo2 第二张照片Base64字符串
   * @return 对比结果
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async comparePhoto(
    params: { photo1?: string; photo2?: string },
    mutate: DocumentNode = GraphqlImporter.comparePhoto,
    operation?: string
  ): Promise<Response<SimilarResultResponse>> {
    return commonRequestApi<SimilarResultResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 尝试使用新的照片与基准照对比
   * @param newPhotoBase64 新的照片Base64字符串
   * @return 对比结果
   * @param mutate 查询 graphql 语法文档
   * @param newPhotoBase64 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async comparePhotoFromDatum(
    newPhotoBase64: string,
    mutate: DocumentNode = GraphqlImporter.comparePhotoFromDatum,
    operation?: string
  ): Promise<Response<SimilarResult>> {
    return commonRequestApi<SimilarResult>(
      SERVER_URL,
      {
        query: mutate,
        variables: { newPhotoBase64 },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建基准照采集信息
   * @param request 信息
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createDatumConfig(
    request: PlatformDatumConfigAddRequest,
    mutate: DocumentNode = GraphqlImporter.createDatumConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建用户考试申诉
   * @param createInfo 创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createExamAppeal(
    createInfo: ExamUserAppealCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createExamAppeal,
    operation?: string
  ): Promise<Response<ExamUserAppealResponse>> {
    return commonRequestApi<ExamUserAppealResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { createInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建和更新平台级别人脸识别监管配置
   * @param setting 人脸识别监管配置
   * @param mutate 查询 graphql 语法文档
   * @param setting 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOrUpdateSupervisionConfigByPlatform(
    setting: FaceSupervisionSettingRequest,
    mutate: DocumentNode = GraphqlImporter.createOrUpdateSupervisionConfigByPlatform,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { setting },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建和更新子项目级别人脸识别监管配置
   * @param setting 人脸识别监管配置
   * @param mutate 查询 graphql 语法文档
   * @param setting 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOrUpdateSupervisionConfigBySubProject(
    setting: FaceSupervisionSettingRequest,
    mutate: DocumentNode = GraphqlImporter.createOrUpdateSupervisionConfigBySubProject,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { setting },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建和更新单位级别人脸识别监管配置
   * @param unitId  单位编号
   * @param setting 人脸识别监管配置
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createOrUpdateSupervisionConfigByUnit(
    params: { unitId?: string; setting?: FaceSupervisionSettingRequest },
    mutate: DocumentNode = GraphqlImporter.createOrUpdateSupervisionConfigByUnit,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 创建用户基准照
   * @param photoPaths       基准照相对路径，不包含mfs
   * @param allowUpdateCount 允许更新基准照次数
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createUserDatum(
    params: { photoPaths?: Array<string>; allowUpdateCount: number },
    mutate: DocumentNode = GraphqlImporter.createUserDatum,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 检测提供的照片是否有人脸
   * @param photoBase64 照片的Base64字符串
   * @return 是否有人脸，true/false，有/没有
   * @param mutate 查询 graphql 语法文档
   * @param photoBase64 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async detectPhotoFace(
    photoBase64: string,
    mutate: DocumentNode = GraphqlImporter.detectPhotoFace,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { photoBase64 },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 拒绝用户考试申诉
   * @param rejectInfo 拒绝信息
   * @param mutate 查询 graphql 语法文档
   * @param rejectInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rejectAppeal(
    rejectInfo: RejectExamUserAppealRequest,
    mutate: DocumentNode = GraphqlImporter.rejectAppeal,
    operation?: string
  ): Promise<Response<ExamUserAppealResponse>> {
    return commonRequestApi<ExamUserAppealResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { rejectInfo },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 重置允许更新次数
   * @param userId           用户编号
   * @param allowUpdateCount 允许更新次数
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resetAllowUpdateCount(
    params: { userId?: string; allowUpdateCount: number },
    mutate: DocumentNode = GraphqlImporter.resetAllowUpdateCount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 重置更新次数
   * @param userId      用户编号
   * @param updateCount 更新次数
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async resetUpdateCount(
    params: { userId?: string; updateCount: number },
    mutate: DocumentNode = GraphqlImporter.resetUpdateCount,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新人脸识别采集配置
   * @param configId 配置编号
   * @param update   更新信息
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateDatumConfig(
    params: { configId?: string; update?: DatumConfigUpdateRequest },
    mutate: DocumentNode = GraphqlImporter.updateDatumConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新人脸识别配置启禁用状态
   * @param configId 配置编号
   * @param enable   true/false,启用/禁用
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateDatumConfigState(
    params: { configId?: string; enable: boolean },
    mutate: DocumentNode = GraphqlImporter.updateDatumConfigState,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新考试场景防作弊配置
   * @param configUpdate 更新配置信息
   * @param mutate 查询 graphql 语法文档
   * @param configUpdate 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateExamScenesConfig(
    configUpdate: ExamScenesConfigUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateExamScenesConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { configUpdate },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新课程场景防作弊配置
   * @param configUpdate 更新配置信息
   * @param mutate 查询 graphql 语法文档
   * @param configUpdate 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateLearningScenesConfig(
    configUpdate: LearningScenesConfigUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateLearningScenesConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { configUpdate },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新登录场景防作弊配置
   * @param configUpdate 更新配置信息
   * @param mutate 查询 graphql 语法文档
   * @param configUpdate 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateLoginScenesConfig(
    configUpdate: LoginScenesConfigUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateLoginScenesConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { configUpdate },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新用户基准照，采用覆盖的方式
   * @param photoPaths 用户基准照列表
   * @param mutate 查询 graphql 语法文档
   * @param photoPaths 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updatePhotoPaths(
    photoPaths: Array<string>,
    mutate: DocumentNode = GraphqlImporter.updatePhotoPaths,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { photoPaths },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 更新平台级课程场景防作弊配置
   * @param configUpdate 更新配置信息
   * @param mutate 查询 graphql 语法文档
   * @param configUpdate 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updatePlatformLearningScenesConfig(
    configUpdate: PlatformLearningScenesConfigUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updatePlatformLearningScenesConfig,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { configUpdate },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 检测提供照片是否为活体
   * @param photoBase64 照片的Base64字符串
   * @return 是否活体，true/false，是/否
   * @param mutate 查询 graphql 语法文档
   * @param photoBase64 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async verifyPhotoLiveness(
    photoBase64: string,
    mutate: DocumentNode = GraphqlImporter.verifyPhotoLiveness,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(
      SERVER_URL,
      {
        query: mutate,
        variables: { photoBase64 },
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
