schema {
	query:Query
	mutation:Mutation
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取试题纠错分页信息
		@return
	"""
	findMyQuestionCorrectionResult:[QuestionCorrectionResultResponse]
	"""获取试题纠错完成的试题结果
		@return
	"""
	findQuestionCorrectionDone(questionIds:[String]):[QuestionCorrectionResultResponse]
	"""获取试题纠错分页信息
		@param page
		@param query
		@return
	"""
	findQuestionCorrectionPage(page:Page,query:QuestionCorrectionQueryRequest):QuestionCorrectionPageItemResponsePage @page(for:"QuestionCorrectionPageItemResponse")
	"""获取所有试题纠错分类
		@return
	"""
	listAllQuestionCorrectionCategory:[QuestionCorrectionCategoryResponse]
}
type Mutation {
	"""创建试题纠错信息
		@param request
		@return
	"""
	createQuestionCorrection(request:QuestionCorrectionCreateRequest):QuestionCorrectionResponse
	"""标记试题纠错信息
		@param request
		@return
	"""
	signQuestionCorrection(request:SignQuestionCorrectionRequest):QuestionCorrectionResponse
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""附件请求
	<AUTHOR> create 2020/5/21 19:26
"""
input AttachmentRequest @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.request.AttachmentRequest") {
	"""附件名称"""
	name:String
	"""文件后缀名"""
	suffix:String
	"""文件资源路径"""
	resPath:String
}
"""创建试题纠错
	<AUTHOR> create 2020/5/21 16:05
"""
input QuestionCorrectionCreateRequest @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.request.QuestionCorrectionCreateRequest") {
	"""试题id"""
	questionId:String
	"""纠错分类id"""
	categoryId:String
	"""描述"""
	describe:String
	"""附件"""
	attachments:[AttachmentRequest]
}
"""试题纠错查询请求
	<AUTHOR> create 2020/5/22 8:36
"""
input QuestionCorrectionQueryRequest @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.request.QuestionCorrectionQueryRequest") {
	"""试题题目"""
	title:String
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""分类id"""
	categoryId:String
	"""创建时间起"""
	createTimeBegin:DateTime
	"""创建时间止"""
	createTimeEnd:DateTime
}
"""标记试题纠错
	<AUTHOR> create 2020/5/21 19:45
"""
input SignQuestionCorrectionRequest @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.request.SignQuestionCorrectionRequest") {
	"""试题纠错id"""
	questionCorrectionId:String
	"""标记内容"""
	content:String
	"""标记试题纠错状态
		@see QuestionCorrectionStatus
	"""
	singStatus:Int!
}
enum ScaleType @type(value:"com.fjhb.platform.component.exam.commons.api.consts.question.ScaleType") {
	CUSTOM
	SATISFACTION
	RECOGNITION
	IMPORTANCE
	WILLING
	CONFORMITY
}
"""
	<AUTHOR> create 2020/5/21 19:23
"""
type AttachmentResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.AttachmentResponse") {
	"""附件名称"""
	name:String
	"""文件后缀名"""
	suffix:String
	"""文件资源路径"""
	resPath:String
}
"""
	<AUTHOR> create 2020/5/21 19:04
"""
type OperatorResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.OperatorResponse") {
	"""操作人信息"""
	id:String
	"""操作人昵称"""
	nickName:String
}
"""试题纠错分类
	<AUTHOR> create 2020/5/22 16:08
"""
type QuestionCorrectionCategoryResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.QuestionCorrectionCategoryResponse") {
	"""问题分类id"""
	id:String
	"""父级分类id"""
	parentId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""分类名称"""
	name:String
	"""创建时间"""
	createTime:DateTime
	"""创建人信息"""
	creatorId:String
}
"""
	<AUTHOR> create 2020/5/21 19:50
"""
type QuestionCorrectionPageItemResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.QuestionCorrectionPageItemResponse") {
	"""id"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""创建时间"""
	createTime:DateTime
	"""试题id"""
	questionId:String
	"""纠错分类id"""
	categoryId:String
	"""描述"""
	describe:String
	"""附件"""
	attachments:[AttachmentResponse]
	"""试题纠错状态
		@see QuestionCorrectionStatus
	"""
	status:Int!
	"""标记记录集合"""
	signList:[SignItemResponse]
	question:QuestionResponse
}
"""
	<AUTHOR> create 2020/5/21 19:22
"""
type QuestionCorrectionResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.QuestionCorrectionResponse") {
	"""id"""
	id:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""组织机构ID"""
	organizationId:String
	"""创建人id"""
	creatorId:String
	"""创建时间"""
	createTime:DateTime
	"""试题id"""
	questionId:String
	"""纠错分类id"""
	categoryId:String
	"""描述"""
	describe:String
	"""附件"""
	attachments:[AttachmentResponse]
	"""试题纠错状态
		@see QuestionCorrectionStatus
	"""
	status:Int!
	"""标记记录集合"""
	signList:[SignResponse]
}
"""试题纠错结果
	<AUTHOR> create 2020/5/23 10:23
"""
type QuestionCorrectionResultResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.QuestionCorrectionResultResponse") {
	"""试题id"""
	questionId:String
	"""试题纠错状态
		@see QuestionCorrectionStatus
	"""
	status:Int!
}
"""试题信息
	<AUTHOR> create 2019/12/23 15:56
"""
type QuestionResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.QuestionResponse") {
	"""试题id"""
	id:String
	"""试题应用类型
		@see QuestionApplyType
	"""
	applyTypes:[Int]
	"""所属平台ID"""
	platformId:String
	"""所属平台版本ID"""
	platformVersionId:String
	"""所属项目ID"""
	projectId:String
	"""所属子项目ID"""
	subProjectId:String
	"""所属单位ID"""
	unitId:String
	"""所属组织机构ID"""
	organizationId:String
	"""题库ID"""
	libraryId:String
	"""题目"""
	title:String
	"""判断题"""
	judgement:JudgementResponse
	"""单选题"""
	singleChoice:SingleChoiceResponse
	"""多选"""
	multipleChoice:MultipleChoiceResponse
	"""填空"""
	blankFilling:BlankFillingResponse
	"""问答题"""
	essay:EssayResponse
	"""量表题"""
	scale:ScaleResponse
	"""综合题"""
	comprehensive:ComprehensiveResponse
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficulty:Double!
	"""试题解析"""
	description:String
	"""创建人id"""
	createUserId:String
	"""创建时间"""
	createTime:DateTime
	"""最后修改时间"""
	lastChangeTime:DateTime
	"""是否启用"""
	enabled:Boolean!
	"""资源记录(数据)的授权源id
		a 授权 b, b 授权 c, c的sourceId是b, c的rootId是a
	"""
	rootId:String
	"""数据授权的Token, 并不需要默认值"""
	token:String
}
"""
	<AUTHOR> create 2020/5/21 19:51
"""
type SignItemResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.SignItemResponse") {
	"""标记人id"""
	signerId:String
	"""标记人信息"""
	signer:OperatorResponse
	"""标记时间"""
	signTime:DateTime
	"""标记内容"""
	content:String
	"""标记试题纠错状态
		@see QuestionCorrectionStatus
	"""
	singStatus:Int!
}
"""
	<AUTHOR> create 2020/5/21 19:23
"""
type SignResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.SignResponse") {
	"""标记人id"""
	signerId:String
	"""标记时间"""
	signTime:DateTime
	"""标记内容"""
	content:String
	"""标记试题纠错状态
		@see QuestionCorrectionStatus
	"""
	singStatus:Int!
}
"""
	<AUTHOR> create 2019/12/11 10:54
"""
type BlankFillingResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.content.BlankFillingResponse") {
	"""答案数量"""
	answerCount:Int!
	"""当填空题类型为 精确匹配时，最外层集合为答案组（也就是每一组都是一道填空题的答案，满足当中的任意一组表示回答正确）第二层集合为空
		当填空题类似为 每空多答案时，最外层的集合为每个空的答案，第二层集合为每个空的备选答案
	"""
	answersGroup:[[String]]
	"""答案项分值
		当填空题类型为 精确匹配时此项值无效
	"""
	answersItemScore:[Double]
	"""答案类型
		@see BlankFillingAnswerType
	"""
	answerType:Int!
	"""答案是否有顺序.当{@link #answerType } = {@link BlankFillingAnswerType#MULTIPLE_PER_BLANK} 时，
		即每空多答案的情况下，答案是否是按照填空顺序排列。
	"""
	sequence:Boolean!
	"""评分标准"""
	standard:String
}
"""选项
	<AUTHOR> create 2019/12/23 16:05
"""
type ChoiceItemResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.content.ChoiceItemResponse") {
	"""选项ID"""
	id:String
	"""选项内容"""
	content:String
}
"""综合题子题
	<AUTHOR> create 2019/12/11 13:36
"""
type ComprehensiveChildQuestionResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.content.ComprehensiveChildQuestionResponse") {
	"""子试题id"""
	questionId:String
	"""题目"""
	title:String
	"""试题类型
		@see QuestionType
	"""
	questionType:Int!
	"""判断题"""
	judgement:JudgementResponse
	"""单选题"""
	singleChoice:SingleChoiceResponse
	"""多选"""
	multipleChoice:MultipleChoiceResponse
	"""填空"""
	blankFilling:BlankFillingResponse
	"""问答题"""
	essay:EssayResponse
	"""量表题"""
	scale:ScaleResponse
	"""试题难度
		@see QuestionMode
	"""
	mode:Int!
	"""难度值"""
	difficultyValue:Double!
	"""试题解析"""
	description:String
}
"""
	<AUTHOR> create 2019/12/11 13:54
"""
type ComprehensiveResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.content.ComprehensiveResponse") {
	"""子题"""
	children:[ComprehensiveChildQuestionResponse]
}
"""问答题
	<AUTHOR> create 2019/12/11 11:42
"""
type EssayResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.content.EssayResponse") {
	"""参考答案"""
	referenceAnswer:String
	"""评分标准"""
	standard:String
	"""是否限制作答长度"""
	limitAnswerLength:Boolean!
	"""允许作答的文本字符最少长度"""
	permitAnswerLengthMin:Int!
	"""允许作答的文本字符最大长度"""
	permitAnswerLengthMax:Int!
}
"""判断题试题内容dto
	<AUTHOR> create 2019/12/23 16:03
"""
type JudgementResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.content.JudgementResponse") {
	"""正确答案"""
	correctAnswer:Boolean!
	"""正确文本"""
	correctText:String
	"""错误文本"""
	incorrectText:String
}
"""
	<AUTHOR> create 2019/12/23 16:09
"""
type MultipleChoiceResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.content.MultipleChoiceResponse") {
	"""选项"""
	choiceItems:[ChoiceItemResponse]
	"""正确答案"""
	correctAnswers:[String]
}
"""量表题
	<AUTHOR> create 2019/12/11 14:04
"""
type ScaleResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.content.ScaleResponse") {
	"""量表类型"""
	scaleType:ScaleType
	"""程度_始，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写"""
	startDegree:String
	"""程度_止，{@link #scaleType}为{@link ScaleType#CUSTOM 自定义}时填写"""
	endDegree:String
	"""级数"""
	series:Int!
	"""初始值"""
	initialValue:Int!
}
"""单选题dto
	<AUTHOR> create 2019/12/23 16:03
"""
type SingleChoiceResponse @type(value:"com.fjhb.platform.core.v1.questioncorrection.kernel.gateway.graphql.response.content.SingleChoiceResponse") {
	"""选项"""
	choiceItems:[ChoiceItemResponse]
	"""标准答案"""
	correctAnswer:String
}

scalar List
type QuestionCorrectionPageItemResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [QuestionCorrectionPageItemResponse]}
