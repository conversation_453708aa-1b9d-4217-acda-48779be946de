import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import BillConfigInfo from '@api/service/customer/payment-channel/models/BillConfigInfo'
import { ResponseStatus } from '@api/Response'
import platformPaymentChannelGateway, {
  PaymentChannelInfoResponse,
  PaymentChannelQueryRequest,
  PaymentChannelTypeEnum
} from '@api/gateway/PlatformPaymentChannel'
import { PayeeAccount } from '@api/service/customer/order/models/PayeeAccount'
import platformPaymentAccount, { PaymentAccountInfo } from '@api/gateway/PlatformPaymentAccount'
import Vue from 'vue'
import { Deprecated } from '@api/Secure'

interface PaymentAccountChannelState {
  billConfigMap: { [key: string]: BillConfigInfo }
  /**
   * 收款账号集合
   */
  payeeAccounts: Array<PayeeAccount>
  /**
   * 收款账号详情
   */
  paymentAccountInfo: PaymentAccountInfo
  /**
   * 支付渠道集合
   */
  paymentChannelList: Array<PaymentChannelInfoResponse>
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerPaymentAccountChannelModule', store })
class PaymentAccountChannelModule extends VuexModule implements PaymentAccountChannelState {
  //region state
  // billConfigMap: Map<string, BillConfigInfo> = new Map<string, BillConfigInfo>()
  billConfigMap: { [key: string]: BillConfigInfo } = {}
  /**
   * 收款账号集合
   */
  payeeAccounts = new Array<PayeeAccount>()
  /**
   * 收款账号详情
   */
  paymentAccountInfo = new PaymentAccountInfo()
  /**
   * 支付渠道集合
   */
  paymentChannelList = new Array<PaymentChannelInfoResponse>()
  //endregion

  //region action
  /**
   * 根据渠道查询发票配置
   * @param channelName  PaymentChannelTypeEnum
   */
  @Action
  @Deprecated
  async getBillConfigByPaymentChannel(channelName: string): Promise<ResponseStatus> {
    let billConfig = this.billConfigMap[channelName]
    if (billConfig) {
      return new ResponseStatus(200)
    } else {
      const typeEnum = PaymentChannelTypeEnum[channelName]
      const response = await platformPaymentChannelGateway.getBillConfigByPaymentChannel(typeEnum)
      billConfig = new BillConfigInfo()
      Object.assign(billConfig, response.data)
      this.SET_BILL_CONFIG_MAP({ channelName, billConfig })
      return response.status
    }
  }

  /**
   * 获取集体缴费渠道的收款账号
   * payType 支付方式(1:代表线上;2:代表线下)
   * @param data
   */
  // @Action
  // async getPayeeAccountsByChannel(data: {
  //   channelName: PaymentChannelTypeEnum
  //   payType: number
  // }): Promise<ResponseStatus> {
  // const query = new MerchantAccountQuery()
  // query.payType = data.payType
  // query.placeChannelEnum = data.channelName
  // const response = await tradeQueryGateway.listAccount(query)
  // if (response.status.isSuccess()) {
  //   const payAccounts = new Array<PayeeAccount>()
  //   Object.assign(payAccounts, response.data)
  //   this.SET_PAY_ACCOUNTS(payAccounts)
  // }
  // return response.status
  // }

  /**
   * 根据收款账号id获取详情
   * @param params
   * @constructor
   */
  @Action
  @Deprecated
  async getPaymentAccountInfo(id: string) {
    const { data, status } = await platformPaymentAccount.get(id)
    if (status.isSuccess()) {
      this.SET_PAYMENT_ACCOUNT_INFO(data)
    }
  }

  /**
   * 获取商品的支付渠道
   * @constructor
   */
  @Action
  @Deprecated
  async listPaymentChannel(queryParams: PaymentChannelQueryRequest) {
    const { data, status } = await platformPaymentChannelGateway.listPaymentChannel(queryParams)
    if (status.isSuccess()) {
      this.SET_PAYMENT_CHANNEL_LIST(data)
    }
    return status
  }
  //endregion

  //region mutaion
  @Mutation
  SET_BILL_CONFIG_MAP(params: { channelName: string; billConfig: BillConfigInfo }) {
    // this.billConfigMap[params.channelName] = params.billConfig
    Vue.set(this.billConfigMap, params.channelName, params.billConfig)
  }

  @Mutation
  SET_PAY_ACCOUNTS(payeeAccounts: Array<PayeeAccount>) {
    this.payeeAccounts = payeeAccounts
  }

  @Mutation
  SET_PAYMENT_ACCOUNT_INFO(paymentInfo: PaymentAccountInfo) {
    this.paymentAccountInfo = paymentInfo
  }

  @Mutation
  SET_PAYMENT_CHANNEL_LIST(paymentChannelList: Array<PaymentChannelInfoResponse>) {
    this.paymentChannelList = paymentChannelList
  }
  //endregion
}

export default getModule(PaymentAccountChannelModule)
