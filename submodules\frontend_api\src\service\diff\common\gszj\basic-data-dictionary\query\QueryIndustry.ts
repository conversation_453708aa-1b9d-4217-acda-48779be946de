import IndustryVo from '@api/service/diff/common/gszj/basic-data-dictionary/query/vo/IndustryVo'
import QueryIndustry from '@api/service/common/basic-data-dictionary/query/QueryIndustry'
import { IndustryIdEnum } from '@api/service/common/basic-data-dictionary/enum/IndustryIdEnum'

/**
 * @description 查询行业类
 */
class QueryIndustryDiff {
  /**
   * 行业列表（带有行业属性编号）
   */
  industryList: Array<IndustryVo> = []

  /**
   * 查询行业列表 ----------- 数据参与业务使用
   * resIndustry 获取网校配置的行业列表（缺少名称）
   * resInfo 带有名称的行业详细信息
   * industryVo 返回给UI展示
   * @return status
   */
  async queryIndustry() {
    await QueryIndustry.queryIndustry()
    this.industryList = QueryIndustry.industryList.map((item) => {
      const industryVo = new IndustryVo()
      industryVo.id = item.id
      industryVo.name = item.name
      industryVo.sort = item.sort
      industryVo.propertyId = item.propertyId
      if (item.id === IndustryIdEnum.RS) {
        industryVo.showName = '职称系列'
      } else {
        industryVo.showName = item.name
      }
      return industryVo
    })
  }
}

export default QueryIndustryDiff
