import fjzjBxDataRepair from './mutates/fjzjBxDataRepair.graphql'
import fjzjBxDataRepairTestPage from './mutates/fjzjBxDataRepairTestPage.graphql'
import fjzjBxDataRepairTestUser from './mutates/fjzjBxDataRepairTestUser.graphql'
import fjzjHandleStudentNoList from './mutates/fjzjHandleStudentNoList.graphql'
import fjzjRetryArrangeFailedTask from './mutates/fjzjRetryArrangeFailedTask.graphql'

export {
  fjzjBxDataRepair,
  fjzjBxDataRepairTestPage,
  fjzjBxDataRepairTestUser,
  fjzjHandleStudentNoList,
  fjzjRetryArrangeFailedTask
}
