<template>
  <el-drawer title="设置每天不学习时间" :visible.sync="openDialog" size="700px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-alert type="warning" show-icon :closable="false" class="m-alert">
        设置每天不学习的时间段，设置完成后，学员课程学习不会在这些时间段内进行学习
      </el-alert>
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="formRef" :model="form" :rules="rules" label-width="auto" class="m-form f-mt30">
            <el-form-item label="每天不学习时间段：" prop="time">
              <el-time-picker
                is-range
                v-model="form.time"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
                value-format="HH:mm:ss"
                @change="change"
              >
              </el-time-picker>
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="openDialog = false">取消</el-button>
              <el-button type="primary" @click="confirm">确认</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import { TimeRange } from '@api/ms-gateway/ms-autolearning-v1'
  import { ElForm } from 'element-ui/types/form'

  @Component
  export default class extends Vue {
    @Ref('formRef')
    formRef: ElForm
    /**
     * 抽屉显隐
     */
    openDialog = false
    /**
     * 表单模型
     */
    form = { time: ['', ''] }
    /**
     * 规则
     */
    rules = {
      time: [
        { required: true, validator: this.validateTime, message: '请设置每天不学习时间', trigger: ['blur', 'change'] }
      ]
    }
    /**
     * 自定义时间校验
     */
    validateTime(rule: any, value: any, callback: any) {
      const newValue = value?.filter((item: any) => item !== null && item !== undefined)
      if (!newValue?.length || !newValue[0] || !newValue[1]) {
        callback(new Error('请设置每天不学习时间'))
      } else {
        callback()
      }
    }
    change(val: any) {
      const timeList = val?.filter((item: any) => item !== null && item !== undefined)
      if (!timeList?.length) {
        this.formRef.resetFields()
      }
    }
    /**
     * 控制抽屉显隐
     */
    showDialog() {
      this.form.time = ['', '']
      this.openDialog = !this.openDialog
      this.$nextTick(() => {
        this.formRef.clearValidate()
      })
    }
    /**
     * 确认添加时间
     */
    async confirm() {
      this.formRef.validate((valid: boolean) => {
        if (valid) {
          const noStudyTime = new TimeRange()
          noStudyTime.min = this.form.time[0]
          noStudyTime.max = this.form.time[1]
          this.$emit('addNoStudyTime', noStudyTime)
          this.openDialog = false
        }
      })
    }
  }
</script>
