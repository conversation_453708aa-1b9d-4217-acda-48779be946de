import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PlatformPaymentChannel'

// 枚举
export enum PaymentChannelTypeEnum {
  WEB = 'WEB',
  ANDROID = 'ANDROID',
  IOS = 'IOS',
  WECHAT_OFFICIAL_ACCOUNTS = 'WECHAT_OFFICIAL_ACCOUNTS',
  WECHAT_MINI_PROGRAMS = 'WECHAT_MINI_PROGRAMS',
  PRESENT = 'PRESENT',
  COLLECTIVE = 'COLLECTIVE',
  HTML5 = 'HTML5',
  DINGDING = 'DINGDING',
  CHANNEL_PRESENT_OPEN = 'CHANNEL_PRESENT_OPEN'
}
export enum AuthorizationStateEnum {
  AUTHORIZATION = 'AUTHORIZATION',
  CANCEL_AUTHORIZATION = 'CANCEL_AUTHORIZATION'
}
export enum MerchantAccountCreateType {
  INTERNAL = 'INTERNAL',
  NORMAL = 'NORMAL',
  AUTHORIZE = 'AUTHORIZE'
}

// 类

/**
 * 支付渠道查询对象
<AUTHOR> create 2021/2/24 10:24
 */
export class PaymentChannelQueryRequest {
  /**
   * 商品skuid
   */
  commoditySkuId?: string
}

/**
 * 缴费渠道收款账号查询参数
Created by chenwq on 2018/1/22.
 */
export class ChannelPaymentAccountQueryParam {
  /**
   * 账户一级类型代表的是支付方式(1:线上,2:线下)
   */
  firstType?: number
  /**
   * 缴费渠道类型
   */
  channelTypeEnum?: PaymentChannelTypeEnum
  /**
   * 指定查询商品skuId,指定的情况下会查询商品所属单位的对应收款账号
   */
  skuId?: string
}

/**
 * Created by ljl on 2017/8/10.
 */
export class PaymentChannelAccountAddDto {
  /**
   * 收款账号Id
   */
  accountId: string
  /**
   * 渠道类型
   */
  channelType?: PaymentChannelTypeEnum
}

/**
 * Created by ljl on 2017/8/10.
<AUTHOR>
@ipdate:  2018/04/28 删除冗余的字段
 */
export class BillConfigAddDto {
  /**
   * 发票配置 Id
   */
  id?: string
  /**
   * 收款账号id
   */
  accountId?: string
  /**
   * tab类型
   */
  tabType: string
  /**
   * 是否提供发票|1：不提供 2：提供&#x27;
   */
  isProvide: number
  /**
   * 发票提供类型|0：无 1：学员自选是否需要发票 2：强制提供&#x27;
   */
  provideType: number
  /**
   * 电子发票查询地址
   */
  eInvoiceSearchAddress?: string
  /**
   * 是否选择增值税普通发票(发票类型)
   */
  selectCommonVAT?: boolean
  /**
   * 是否选择普通电子发票(发票类型)
   */
  selectCommonElectron?: boolean
  /**
   * 是否选择增值税专用发票(发票类型)
   */
  selectVATOnly?: boolean
  /**
   * 是否选择非税务发票(发票类型)
   */
  selectNonTax?: boolean
  /**
   * 是否选择了个人(发票抬头)
   */
  selectPersonal?: boolean
  /**
   * 是否选择了单位(发票抬头)
   */
  selectUnit?: boolean
}

/**
 * 支付渠道信息
<AUTHOR> create 2021/2/24 10:19
 */
export class PaymentChannelInfoResponse {
  /**
   * 支付渠道id
   */
  paymentChannelId: string
}

/**
 * 收款账号
<AUTHOR>
@date 2018/4/12
 */
export class MerchantAccount {
  /**
   * 账户id
   */
  id: string
  /**
   * 账户别名
   */
  accountAlias: string
  /**
   * 账户账号
   */
  accountNo: string
  /**
   * 支付渠道的code
   */
  code: string
  /**
   * 支付渠道的logoPath
   */
  logoPath: string
}

/**
 * Created by ljl on 2017/8/22.
 */
export class PaymentChannelConfigurationDto {
  /**
   * @see PaymentChannelTypeEnum
渠道类型
   */
  channelType: string
  /**
   * 账户一级类型代表的是支付方式(1:线上,2:线下)
只能是1或2
   */
  firstTypes: Array<number>
}

/**
 * Created by ljl on 2017/8/10.
 */
export class PaymentChannelDto {
  /**
   * 渠道Id
   */
  id: string
  /**
   * 渠道类型
   */
  type: string
  /**
   * 发票Id
   */
  obcId: string
  /**
   * 是否可用
   */
  enable: boolean
  /**
   * 子项目Id
   */
  subProjectId: string
}

export class BillConfigDto {
  /**
   * 发票配置 Id
   */
  id: string
  /**
   * 是否提供发票|1：不提供 2：提供&#x27;
   */
  isProvide: number
  /**
   * 发票提供类型|0：无 1：学员自选是否需要发票 2：强制提供&#x27;
   */
  provideType: number
  /**
   * 支持开票的发票类型 (将会是com.fjhb.courseSupermarket.service.bill.dto.InvoiceTypeEnum里面值的组合或者单个值，例如：COMMON_VAT&amp;-&amp;COMMON_ELECTRON)
   */
  supportInvoiceTypes: string
  /**
   * 支持开票的发票抬头 (将会是com.fjhb.courseSupermarket.service.bill.dto.InvoiceTitleEnum里面值的组合或者单个值，例如：PERSONAL&amp;-&amp;UNIT)
@see InvoiceTitleEnum
   */
  supportInvoiceTitles: string
  /**
   * 是否选择增值税普通发票(发票类型)
   */
  selectCommonVAT: boolean
  /**
   * 是否选择普通电子发票(发票类型)
   */
  selectCommonElectron: boolean
  /**
   * 是否选择增值税专用发票(发票类型)
   */
  selectVATOnly: boolean
  /**
   * 是否选择非税务发票(发票类型)
   */
  selectNonTax: boolean
  /**
   * 是否选择了个人(发票抬头)
   */
  selectPersonal: boolean
  /**
   * 是否选择了单位(发票抬头)
   */
  selectUnit: boolean
}

/**
 * @author: eleven
@since: 2020/02/19 13:01
@description: 收款账号
 */
export class PaymentAccount {
  /**
   * 收款账户别名
   */
  accountAlias: string
  /**
   * 账户一级类型代表的是支付方式(1:线上,2:线下)
只能是1或2
   */
  firstType: number
  /**
   * 二级类型(在一级类型的基础上拓展的描述在线下支付方式的情况下可能有&quot;对汇&quot;等)
如果没有填写为空
   */
  secondType: string
  /**
   * 企业名称
   */
  merchantName: string
  /**
   * 企业联系电话
   */
  merchantPhone: string
  /**
   * 开户银行
   */
  depositBank: string
  /**
   * 开户银行的行号
   */
  bankNumber: string
  /**
   * 柜台号
   */
  counterNumber: string
  /**
   * 收款账户的第三方接口的账号
   */
  accountNo: string
  /**
   * 建行接口需要的BRANCHID(分行代码)
   */
  branchBankId: string
  /**
   * 状态0:启用,1:停用
   */
  status: number
  /**
   * 支付网关名称
   */
  tradeChannelName: string
  /**
   * 支付渠道的code
   */
  code: string
  /**
   * 授权状态
   */
  authorizationState: AuthorizationStateEnum
  /**
   * 账户id
   */
  id: string
  /**
   * 创建单位id
   */
  unitId: string
  /**
   * 收款账号的创建方式
   */
  createType: MerchantAccountCreateType
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 通过缴费渠道类型获取培训平台下该缴费渠道对应的发票配置信息
   * @param placeChannelEnum 订单业务对象之下单渠道
   * @return
   * @param query 查询 graphql 语法文档
   * @param placeChannelEnum 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getBillConfigByPaymentChannel(
    placeChannelEnum: PaymentChannelTypeEnum,
    query: DocumentNode = GraphqlImporter.getBillConfigByPaymentChannel,
    operation?: string
  ): Promise<Response<BillConfigDto>> {
    return commonRequestApi<BillConfigDto>(SERVER_URL, {
      query: query,
      variables: { placeChannelEnum },
      operation: operation
    })
  }

  /**   * 通过渠道tab类型(即前端tab)获取发票配置
   * @param paymentChannelTabType
   * @return
   * @param query 查询 graphql 语法文档
   * @param paymentChannelTabType 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getInvoiceConfigByPaymentChannelTab(
    paymentChannelTabType: string,
    query: DocumentNode = GraphqlImporter.getInvoiceConfigByPaymentChannelTab,
    operation?: string
  ): Promise<Response<BillConfigDto>> {
    return commonRequestApi<BillConfigDto>(SERVER_URL, {
      query: query,
      variables: { paymentChannelTabType },
      operation: operation
    })
  }

  /**   * 通过渠道tab类型(即前端tab)获取发票配置
   * @param paymentChannelTabType
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getInvoiceConfigByPaymentChannelTabAndSkuId(
    params: { paymentChannelTabType?: string; skuId?: string },
    query: DocumentNode = GraphqlImporter.getInvoiceConfigByPaymentChannelTabAndSkuId,
    operation?: string
  ): Promise<Response<BillConfigDto>> {
    return commonRequestApi<BillConfigDto>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 根据渠道和支付方式获取收款账号
   * @param placeChannelEnum
   * @param payType
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getMerchantAccountList(
    params: { placeChannelEnum?: PaymentChannelTypeEnum; payType: number },
    query: DocumentNode = GraphqlImporter.getMerchantAccountList,
    operation?: string
  ): Promise<Response<Array<MerchantAccount>>> {
    return commonRequestApi<Array<MerchantAccount>>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 通过缴费渠道类型获取培训平台下该缴费渠道对应的收款账号信息
   * @param placeChannelEnum 渠道
   * @param payType          交易类型(1:代表线上;2:代表线下)
   * @return
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPaymentAccountByPaymentChannel(
    params: { placeChannelEnum?: PaymentChannelTypeEnum; payType: number },
    query: DocumentNode = GraphqlImporter.getPaymentAccountByPaymentChannel,
    operation?: string
  ): Promise<Response<Array<PaymentAccount>>> {
    return commonRequestApi<Array<PaymentAccount>>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 通过渠道id获取发票配置
   * @return
   * @param query 查询 graphql 语法文档
   * @param paymentChannelId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPaymentAccountByPaymentChannelId(
    paymentChannelId: string,
    query: DocumentNode = GraphqlImporter.getPaymentAccountByPaymentChannelId,
    operation?: string
  ): Promise<Response<BillConfigDto>> {
    return commonRequestApi<BillConfigDto>(SERVER_URL, {
      query: query,
      variables: { paymentChannelId },
      operation: operation
    })
  }

  /**   * 通过渠道Id获取收款账号列表
   * @return
   * @param query 查询 graphql 语法文档
   * @param paymentChannelId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPaymentAccountListByPaymentChannelId(
    paymentChannelId: string,
    query: DocumentNode = GraphqlImporter.getPaymentAccountListByPaymentChannelId,
    operation?: string
  ): Promise<Response<Array<PaymentAccount>>> {
    return commonRequestApi<Array<PaymentAccount>>(SERVER_URL, {
      query: query,
      variables: { paymentChannelId },
      operation: operation
    })
  }

  /**   * 根据缴费渠道类型和支付方式查询收款账号
   * @return
   * @param query 查询 graphql 语法文档
   * @param queryParam 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPaymentAccountListByQueryParam(
    queryParam: ChannelPaymentAccountQueryParam,
    query: DocumentNode = GraphqlImporter.getPaymentAccountListByQueryParam,
    operation?: string
  ): Promise<Response<Array<PaymentAccount>>> {
    return commonRequestApi<Array<PaymentAccount>>(SERVER_URL, {
      query: query,
      variables: { queryParam },
      operation: operation
    })
  }

  /**   * 获取渠道配置
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPaymentChannelConfiguration(
    query: DocumentNode = GraphqlImporter.getPaymentChannelConfiguration,
    operation?: string
  ): Promise<Response<Array<PaymentChannelConfigurationDto>>> {
    return commonRequestApi<Array<PaymentChannelConfigurationDto>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 获取渠道配置,将web ios 微信合并到个人缴费
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPaymentChannelConfiguration2(
    query: DocumentNode = GraphqlImporter.getPaymentChannelConfiguration2,
    operation?: string
  ): Promise<Response<Array<PaymentChannelConfigurationDto>>> {
    return commonRequestApi<Array<PaymentChannelConfigurationDto>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 获取子项目渠道集合
   * @return
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getPaymentChannelList(
    query: DocumentNode = GraphqlImporter.getPaymentChannelList,
    operation?: string
  ): Promise<Response<Array<PaymentChannelDto>>> {
    return commonRequestApi<Array<PaymentChannelDto>>(SERVER_URL, {
      query: query,
      variables: undefined,
      operation: operation
    })
  }

  /**   * 根据参数 查询支付渠道信息
   * @param queryDto
   * @return
   * @param query 查询 graphql 语法文档
   * @param queryParams 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async listPaymentChannel(
    queryParams: PaymentChannelQueryRequest,
    query: DocumentNode = GraphqlImporter.listPaymentChannel,
    operation?: string
  ): Promise<Response<Array<PaymentChannelInfoResponse>>> {
    return commonRequestApi<Array<PaymentChannelInfoResponse>>(SERVER_URL, {
      query: query,
      variables: { queryParams },
      operation: operation
    })
  }

  /**   * 为渠道增加收款账号
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param paymentChannelAccountAddDto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async addPaymentAccount(
    paymentChannelAccountAddDto: PaymentChannelAccountAddDto,
    mutate: DocumentNode = GraphqlImporter.addPaymentAccount,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: mutate,
      variables: { paymentChannelAccountAddDto },
      operation: operation
    })
  }

  /**   * 操作发票配置
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param billConfigAddDto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async operaBillConfig(
    billConfigAddDto: BillConfigAddDto,
    mutate: DocumentNode = GraphqlImporter.operaBillConfig,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { billConfigAddDto },
      operation: operation
    })
  }

  /**   * 去除收款账号
   * @return
   * @param mutate 查询 graphql 语法文档
   * @param paymentChannelAccountAddDto 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removePaymentAccount(
    paymentChannelAccountAddDto: PaymentChannelAccountAddDto,
    mutate: DocumentNode = GraphqlImporter.removePaymentAccount,
    operation?: string
  ): Promise<Response<boolean>> {
    return commonRequestApi<boolean>(SERVER_URL, {
      query: mutate,
      variables: { paymentChannelAccountAddDto },
      operation: operation
    })
  }
}

export default new DataGateway()
