import { ResponseStatus } from '@hbfe/common'
import MsCourseResourceV1 from '@api/ms-gateway/ms-course-resource-v1'
import UpdateCoursePackageByPageVo from '@api/service/management/resource/course-package/mutation/vo/UpdateCoursePackageByPageVo'

class MutationUpdateCoursePackage {
  /**
   * 更新课程包信息
   */
  updateCoursePackageVo: UpdateCoursePackageByPageVo = new UpdateCoursePackageByPageVo()

  /**
   * 执行更新动作
   */
  async doUpdate(): Promise<ResponseStatus> {
    const request = this.updateCoursePackageVo.getUpdateCoursePackageRequest()
    const { status } = await MsCourseResourceV1.updateCoursePackage(request)
    return status
  }
}

export default MutationUpdateCoursePackage
