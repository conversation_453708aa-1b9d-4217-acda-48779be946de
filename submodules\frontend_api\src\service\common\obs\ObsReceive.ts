import axios from 'axios'
import { checkDomain } from '@api/service/common/Common'
import Env from '@api/service/common/utils/Env'

/**
 * @description 资源访问——状态层
 */
export class FileResponse {
  // 是否是公共访问资源
  isPublic: boolean = null
  // 原文件路径（唯一key值）
  originPath = ''
  // 上传时的原文件名【仅私有资源有】
  fileName = ''
  // 文件类型
  fileType: FileType = null
  // obs临时授权相对于当前域名的路径【是公共访问资源则与originPat值相等】
  path = ''
  // 文件大小【单位：bit】
  size = 0
  // * 创建时间
  createdTime: string = null
}

/**
 * 文件类型枚举
 * 0-其他
 * 1-图片
 * 2-EXCEL
 * 3-WORD
 * 4-PDF
 * 5-PPT
 * 6-压缩文件
 * 7-视频文件
 * 8-音频文件
 * 9-xml文件
 * 10-文本文件
 * 11-CSV文件
 * 12-JSON文件
 */
export enum FileType {
  OTHER = 0,
  IMG = 1,
  EXCEL = 2,
  WORD = 3,
  PDF = 4,
  PPT = 5,
  COMPRESSED_FILE = 6,
  VIDEO_FILE = 7,
  AUDIO_FILE = 8,
  XML_FILE = 9,
  TEXT_FILE = 10,
  CSV_FILE = 11,
  JSON_FILE = 12
}

export default class ObsReceive {
  /**
   * 【批量】获取可访问的文件路径Map
   * @param filePathList 文件路径集合
   */
  async getBatchResolveFilePathMap(filePathList: string[]): Promise<Map<string, FileResponse>> {
    const result = new Map<string, FileResponse>()
    const resp = await Promise.all(
      filePathList.map(async (item) => {
        return this.getResolveFilePath(item)
      })
    )
    resp?.forEach((item) => {
      if (item.path) {
        result.set(item.path, item)
      }
    })
    return result
  }

  /**
   * 【批量】获取可访问的文件路径List
   * @param filePathList 文件路径集合
   */
  async getBatchResolveFilePathList(filePathList: string[]): Promise<FileResponse[]> {
    const resp = await Promise.all(
      filePathList.map(async (item) => {
        return this.getResolveFilePath(item)
      })
    )
    return resp
  }

  /**
   * 【单个】获取可访问的文件路径
   * @param 文件路径
   */
  async getResolveFilePath(filePath: string): Promise<FileResponse> {
    let result = new FileResponse()
    if (!filePath) return result
    const publicRegx = /\/public\//
    const isPublic = publicRegx.test(filePath)
    if (!isPublic) {
      result = (await this.getOBSFilePath(filePath)) as FileResponse
      result.isPublic = false
      result.originPath = filePath
      const { fileType } = this.getFileTypeByFilePath(filePath)
      result.fileType = fileType
      result.size = result.size ? Number(result.size) : 0
    } else {
      result.isPublic = true
      result.originPath = filePath
      result.path = filePath
      const { fileType, fileName } = this.getFileTypeByFilePath(filePath)
      // 公共资源经过上传处理后，名称丢失，不处理
      result.fileType = fileType
      result.fileName = fileName
    }
    return result
  }

  /**
   * 【单个】获取可访问的文件信息
   * @param 文件路径
   */
  async getResolveFileInfo(filePath: string) {
    const result = (await this.getOBSFileInfo(filePath)) as any
    return result
  }

  /**
   * 【单个】下载文件
   * @param 文件路径
   */
  async webDownloadFile(filePath: string) {
    const resolvePathInfo = await this.getResolveFilePath(filePath)
    const fullPath = this.getWebFullPath(resolvePathInfo.path)
    const link = document.createElement('a')
    link.id = 'taskLink'
    link.style.display = 'none'
    link.href = fullPath
    link.setAttribute('download', resolvePathInfo.fileName)
    document.body.appendChild(link)
    link.click()
    link.remove()
  }

  /**
   * 获取Web端完整访问路径
   * @description 适配不同环境：本地|远端、内网|生产
   * @param urlPath url路径
   */
  getWebFullPath(urlPath: string) {
    const urlRegx = /^http|https:\/\/+/
    const httpRegx = /^http:\/\/+/
    if (urlRegx.test(urlPath)) {
      // 如果以http(s)://开头的url，直接返回
      return urlPath
    } else {
      const href = window.location.href || location.href
      if (checkDomain()) {
        if (httpRegx.test(href)) {
          // 本地环境，为保证访问，使用资源服务域名开头的url
          const env = Env.proxyEnvStr
          const port = Env.proxyPortStr
          return 'https://api' + env + '59iedu.com' + port + urlPath
        } else {
          // 远端环境
          const origin = window.location.origin || location.origin
          return origin + urlPath
        }
      } else {
        return `${location.protocol}//${location.host}${urlPath}`
      }
    }
  }

  // 初始化请求配置
  private init() {
    const env = Env.proxyEnvStr
    const port = Env.proxyPortStr
    const host = location.hostname
    let action
    const headers = new Object()
    if (checkDomain()) {
      if (Env.isProxyInnerNetworkEnv) {
        // 内网环境
        action = `${['https://api', env, '59iedu.com', port].join('')}${'/web/ms-obsfile-v1'}`
      } else {
        // 生产环境
        action = `https://api.59iedu.com${'/web/ms-obsfile-v1'}`
      }
    } else {
      // IP访问
      if (process.env.NODE_ENV != 'production') {
        // 开发模式
        action = `http://************:1457/web/ms-obsfile-v1`
      } else {
        // 部署模式
        action = `${location.protocol}//${location.host}/web/ms-obsfile-v1`
        // console.log(config.url)
      }
    }

    action += '/web/getResourceVisitInfo'
    // 设置额外请求头
    headers['Service-Name'] = 'ms-obsfile-v1'
    headers['Graphql-SchemaName'] = 'ms-obsfile-v1'
    headers['Content-Type'] = 'application/json'
    const accessToken = localStorage.getItem('admin/customer.Access-Token')
    if (accessToken) {
      headers['Authorization'] = accessToken
    }
    return {
      action,
      headers
    }
  }

  // 初始化请求配置
  private initFileResourceInfo() {
    const env = Env.proxyEnvStr
    const port = Env.proxyPortStr
    const host = location.hostname
    let action
    const headers = new Object()
    if (checkDomain()) {
      if (Env.isProxyInnerNetworkEnv) {
        // 内网环境
        action = `${['https://api', env, '59iedu.com', port].join('')}${'/web/ms-obsfile-v1'}`
      } else {
        // 生产环境
        action = `https://api.59iedu.com${'/web/ms-obsfile-v1'}`
      }
    } else {
      // IP访问
      if (process.env.NODE_ENV != 'production') {
        // 开发模式
        action = `http://************:1457/web/ms-obsfile-v1`
      } else {
        // 部署模式
        action = `${location.protocol}//${location.host}/web/ms-obsfile-v1`
        // console.log(config.url)
      }
    }

    action += '/web/getFileResourceInfo'
    // 设置额外请求头
    headers['Service-Name'] = 'ms-obsfile-v1'
    headers['Graphql-SchemaName'] = 'ms-obsfile-v1'
    headers['Content-Type'] = 'application/json'
    return {
      action,
      headers
    }
  }

  /**
   * 置换OBS文件资源访问路径
   * @description 参考 http://*************:8090/pages/viewpage.action?pageId=45508680
   * @param filePath 文件路径
   */
  private async getOBSFilePath(filePath: string) {
    const config = this.init()
    return await new Promise((resolve, reject) => {
      axios
        .request({
          url: config.action,
          method: 'post',
          headers: config.headers,
          data: {
            path: filePath
          }
        })
        .then((res) => {
          if (res.data?.code === 200) {
            resolve(res.data?.data)
          } else {
            reject(new FileResponse())
          }
        })
    })
  }

  private async getOBSFileInfo(filePath: string) {
    const config = this.initFileResourceInfo()
    return await new Promise((resolve, reject) => {
      axios
        .request({
          url: config.action,
          method: 'post',
          headers: config.headers,
          data: {
            filePath: filePath
          }
        })
        .then((res) => {
          if (res.data?.code === 200) {
            resolve(res.data?.data)
          } else {
            reject(new FileResponse())
          }
        })
    })
  }

  /**
   * 根据文件名称获取文件类型
   * @param filePath 文件路径
   */
  getFileTypeByFilePath(filePath: string): { fileType: FileType; fileName: string } {
    const result = {
      fileType: null as FileType,
      fileName: ''
    }
    const splitArr = filePath.split('/')
    if (!splitArr.length) return result
    const fileName = splitArr[splitArr.length - 1]
    result.fileName = fileName
    const imgRegx = /\.jpg|\.png|\.jpeg|\.bmp|\.gif$/i // 图片正则
    const excelRegx = /\.xls|\.xlsx$/i // excel文件正则
    const wordRegx = /\.doc|\.docx$/i // word文件正则
    const pdfRegx = /\.pdf$/i // pdf文件正则
    const pptRegx = /\.ppt|\.pptx$/i // ppt文件正则
    const compressedFileRegx = /\.7z|\.zip|\.rar|\.tgz$/i // 压缩文件正则
    const videoRegx = /\.mp4|\.avi|\.flv|\.wmv|\.mpeg$/i // 视频文件正则
    const audioRegx = /\.mp3|\.wma$/i // 音频文件正则
    const xmlRegx = /\.xml$/i // xml文件正则
    const txtRegx = /\.txt$/i // 文本文件正则
    const csvRegx = /\.csv$/i // csv文件正则
    const jsonRegx = /\.json$/i // json文件正则
    if (imgRegx.test(fileName)) {
      result.fileType = FileType.IMG
    }
    if (excelRegx.test(fileName)) {
      result.fileType = FileType.EXCEL
    }
    if (wordRegx.test(fileName)) {
      result.fileType = FileType.WORD
    }
    if (pdfRegx.test(fileName)) {
      result.fileType = FileType.PDF
    }
    if (pptRegx.test(fileName)) {
      result.fileType = FileType.PPT
    }
    if (compressedFileRegx.test(fileName)) {
      result.fileType = FileType.COMPRESSED_FILE
    }
    if (videoRegx.test(fileName)) {
      result.fileType = FileType.VIDEO_FILE
    }
    if (audioRegx.test(fileName)) {
      result.fileType = FileType.AUDIO_FILE
    }
    if (xmlRegx.test(fileName)) {
      result.fileType = FileType.XML_FILE
    }
    if (txtRegx.test(fileName)) {
      result.fileType = FileType.TEXT_FILE
    }
    if (csvRegx.test(fileName)) {
      result.fileType = FileType.CSV_FILE
    }
    if (jsonRegx.test(fileName)) {
      result.fileType = FileType.JSON_FILE
    }
    return result
  }
}
