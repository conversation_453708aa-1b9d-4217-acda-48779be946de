<template>
  <div>
    <!--退款-->
    <el-drawer
      title="退款"
      :visible.sync="show"
      size="800px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          退款后，原有的学习记录将清空，请确认是否继续。
        </el-alert>
        <el-row type="flex" justify="center">
          <el-col :span="18">
            <el-form ref="form" :model="refundParams" label-width="auto" class="m-form f-mt20">
              <el-form-item label="退款金额：">
                <span class="f-cr f-fb f-f16">¥ {{ refundAmount }}</span
                >，<span class="f-fb f-ml20">退款人次：</span
                ><span class="f-cr f-fb f-f16">{{ refundPersonTime }}</span>
                <p class="f-cr" v-if="tipType === 5">（换班订单退款退的是初始订单子订单，商品是最新换入订单子订单）</p>
              </el-form-item>
              <el-form-item label="退款说明：" class="is-text">
                退款后，原有的学习记录将清空，请确认是否继续。
              </el-form-item>

              <!--强制退款 + 存在学员考核通过-->
              <el-form-item label="退款提示：" class="is-text" v-if="tipType === 1">
                已有学员考核通过，是否强制退款？
              </el-form-item>
              <!--强制退款 + 存在学员进度100% + 未开票-->
              <el-form-item label="退款提示：" class="is-text" v-if="tipType === 2">
                已有学员培训班的学习进度已达到100%，是否强制退款？
              </el-form-item>
              <!--强制退款 + 存在学员进度100% + 已开票-->
              <el-form-item label="退款提示：" class="is-text" v-if="tipType === 3">
                此报名批次订单发票已开具且已有学员培训班学习进度已达到100%，是否强制退款？
              </el-form-item>
              <!--强制退款 + 无学员进度100%、也无学员 + 未开票-->
              <el-form-item label="退款提示：" class="is-text" v-if="tipType === 4">
                此报名批次订单已开票，是否强制退款？
              </el-form-item>

              <el-form-item label="退款理由：" required>
                <el-select v-model="refundParams.reasonId" clearable placeholder="请选择退款理由">
                  <el-option
                    v-for="(item, index) in refundReasonList"
                    :key="index"
                    :label="item.refundReasonValue"
                    :value="item.refundReasonId"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="退款说明：" required>
                <el-input type="textarea" :rows="6" v-model="refundParams.description" placeholder="请输入退款说明" />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="cancelApplyForceOrderRefund">取消</el-button>
                <el-button type="primary" @click="confirmRefund">确认退款</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, PropSync, Vue } from 'vue-property-decorator'
  import { RefundReason } from '@api/service/management/trade/batch/order/query/QueryRefundCauseList'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import SellerApplyBatchOrderReturnRequestVo from '@api/service/management/trade/batch/order/mutation/vo/SellerApplyBatchOrderReturnRequestVo'
  import { bind, debounce } from 'lodash-decorators'
  import { BatchReturnSubOrderInfo } from '@api/ms-gateway/ms-order-v1'
  import DataResolve from '@api/service/common/utils/DataResolve'

  @Component
  export default class extends Vue {
    /**
     * 是否展示
     */
    @PropSync('visible', {
      type: Boolean
    })
    show!: boolean

    /**
     * 退款类型
     */
    @Prop({
      type: Array,
      default: () => {
        return new Array<number>()
      }
    })
    refundType: number[]

    /**
     * 开票状态
     */
    @Prop({
      type: Number
    })
    invoiceDraftType: number

    /**
     * 退款人次
     */
    @Prop({
      type: Number
    })
    refundPersonTime: number

    /**
     * 退款金额
     */
    @Prop({
      type: Number
    })
    refundAmount: number

    /**
     * 批次单id
     */
    @Prop({
      type: String
    })
    batchOrderNo: string

    /**
     * 退款子单集合
     */
    @Prop({
      type: Array,
      default: () => {
        return new Array<BatchReturnSubOrderInfo>()
      }
    })
    returnSubOrders: BatchReturnSubOrderInfo[]

    /**
     * 退款原因列表
     */
    refundReasonList: Array<RefundReason> = new Array<RefundReason>()

    /**
     * 退款参数
     */
    refundParams: SellerApplyBatchOrderReturnRequestVo = new SellerApplyBatchOrderReturnRequestVo()

    /**
     * 提示类型：
     * 1：存在学员考核通过 --> 强制退款
     * 2：存在学员进度100% + 未开票 --> 强制退款
     * 3：存在学员进度100% + 已开票 --> 强制退款
     * 4：无学员进度100%、也无学员考核通过 + 已开票 --> 强制退款
     * 5：无学员进度100%、也无学员考核通过 + 未开票 --> 普通退款
     */
    get tipType() {
      let result: number = null
      if (!DataResolve.isWeightyArr(this.refundType)) return result
      if (this.refundType.includes(1)) {
        result = 1
        return result
      }
      if (this.refundType.includes(2)) {
        if (this.invoiceDraftType === 2) {
          result = 2
          return result
        }
        if (this.invoiceDraftType === 1) {
          result = 3
          return result
        }
      }
      if (this.invoiceDraftType === 1) {
        result = 4
        return result
      }
      if (this.invoiceDraftType === 2) {
        result = 5
        return result
      }
      return result
    }

    async created() {
      await this.getRefundReasonList()
    }

    /**
     * 获取退款原因列表
     */
    async getRefundReasonList() {
      this.refundReasonList =
        await TradeModule.batchTradeBatchFactor.orderFactor.queryOrderFactor.queryRefundCauseList.queryBatchRefundReasonList()
    }

    /**
     * 取消
     */
    cancelApplyForceOrderRefund() {
      this.show = false
    }

    /**
     * 确认退款
     */
    @bind
    @debounce(200)
    async confirmRefund() {
      if (!this.refundParams.reasonId) {
        this.$message.error('请选择退款理由，不能为空！')
        return
      }
      if (!this.refundParams.description) {
        this.$message.error('请输入退款说明')
        return
      }
      const loading = this.$loading({
        lock: true,
        text: '加载中',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.8)'
      })
      try {
        this.refundParams.batchOrderNo = this.batchOrderNo
        this.refundParams.needManualApprove = true
        this.refundParams.batchReturnOrderType = 3
        if (DataResolve.isWeightyArr(this.returnSubOrders)) {
          this.refundParams.returnSubOrders = this.returnSubOrders
        }
        const res =
          await TradeModule.batchTradeBatchFactor.orderFactor.mutationOrderFactor.mutationBatchOrderRefund.applyBatchRefund(
            this.refundParams
          )
        if (res.status?.isSuccess()) {
          this.refundParams = new SellerApplyBatchOrderReturnRequestVo()
          this.$message.success('退款申请提交成功')
          this.show = false
          this.$emit('reloadData')
        } else {
          this.$message.error(res.status?.getMessage() || '确认退款失败')
        }
      } catch (e) {
        this.$message.error(e)
      } finally {
        loading.close()
      }
    }
  }
</script>
