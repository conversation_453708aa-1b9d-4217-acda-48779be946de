import QueryReceiveAccount from './query/QueryReceiveAccount'
import QueryReceiveAccountDetail from './query/QueryReceiveAccountDetail'

class QueryTradeInfoConfigFactory {
  /**
   * 获取收款账户列表
   * @returns
   */
  getQueryReceiveAccount() {
    return new QueryReceiveAccount()
  }

  /**
   * 获取收款账户详情
   * @returns
   */
  getQueryReceiveAccountDetail(id: string) {
    return new QueryReceiveAccountDetail(id)
  }
}
export default QueryTradeInfoConfigFactory
