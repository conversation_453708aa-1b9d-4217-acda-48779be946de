import PageRequest from '@api/service/common/models/common/PageRequest'

export class LsCommodityPageQuery extends PageRequest {
  // 年度code
  yearCode?: number
  /**
   * 培训类别
   */
  trainingTypeId?: string
  /**
   * 培训对象
   */
  traineesId?: string
  /**
   * 岗位类别
   */
  jobCategoryId?: string
  /**
   * 单位类别
   */
  unitCategoryId?: string
  /**
   * 商品名称
   */
  schemeName?: string
  /**
   * 方案id集合
   */
  schemeIds?: Array<string>

  constructor() {
    super()
    this.pageNo = 1
    this.pageSize = 10
    this.yearCode = undefined
  }

  equals(query: any): boolean {
    if (!super.equals(query)) {
      return false
    }
    if (this.yearCode !== query.yearCode) {
      return false
    }
    return true
  }
}
