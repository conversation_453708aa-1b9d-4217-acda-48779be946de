import { DateScopeRequest1, QuestionRequest } from '@api/ms-gateway/ms-data-export-front-gateway-DataExportBackstage'
import { QuestionTypeEnum } from '@api/service/common/enums/question/QuestionType'

class QuestionOutputVo extends QuestionRequest {
  /**
   * 试题ID集合
   */
  questionIdList?: Array<string> = []
  /**
   * 题库ID集合
   */
  libraryIdList?: Array<string> = []
  /**
   * 关联课程ID集合
   */
  relateCourseIds?: Array<string> = []
  /**
   * 试题名称
   */
  topic?: string = ''
  /**
   * 试题类型（1：单选题 2：多选题 3：填空题 4：判断题 5：简答题 6：父子题）
   */
  questionType?: QuestionTypeEnum = null

  /* 
    创建开始时间
  */
  beginTime = ''
  /* 
    创建结束时间
  */
  endTime = ''
  /**
   * 是否启用
   */
  isEnabled?: boolean = null

  toDto() {
    const temp = new QuestionRequest()
    temp.questionIdList = this.questionIdList
    temp.libraryIdList = this.libraryIdList
    temp.relateCourseIds = this.relateCourseIds
    temp.topic = this.topic
    temp.questionType = this.questionType
    temp.isEnabled = this.isEnabled
    temp.createTimeScope = new DateScopeRequest1()
    temp.createTimeScope.beginTime = this.beginTime
    temp.createTimeScope.endTime = this.endTime
    return temp
  }
}

export default QuestionOutputVo
