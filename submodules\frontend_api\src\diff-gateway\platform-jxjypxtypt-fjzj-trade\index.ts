import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'tomcat-jxjytyptcyh'
// 请求地址路径
export const SERVER_URL = '/diff/web/gql'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = true

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'platform-jxjypxtypt-fjzj-trade'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

export class SubOrderAfterInfo {
  subOrderNo?: string
  quantity: number
  amount?: number
  amountSource: number
  properties?: Map<string, string>
}

/**
 * 退货单批量同意申请请求
<AUTHOR>
 */
export class AgreeOrderReturnBatchRequest {
  /**
   * 订单和退货单信息列表
   */
  orderReturnPairs: Array<OrderReturnPair>
  /**
   * 审批意见
   */
  approveComment?: string
  /**
   * 是否自动同意退款
   */
  autoAgreeReturn: boolean
}

/**
 * 订单和退货单对应关系
 */
export class OrderReturnPair {
  /**
   * 订单号
   */
  orderNo: string
  /**
   * 退货单号
   */
  returnOrderNo: string
}

/**
 * 确认退货请求
<AUTHOR>
@since 2025-03-07
 */
export class AgreeOrderReturnRequest {
  orderNo: string
  /**
   * 退货单号
   */
  returnOrderNo: string
  /**
   * 审批意见
   */
  approveComment?: string
  /**
   * 是否自动同意退款
   */
  autoAgreeReturn: boolean
}

/**
 * 申请退货
<AUTHOR>
@since 2025-03-07
 */
export class ApplyOrderReturnRequest {
  orderNo: string
  /**
   * 退货原因id
   */
  reasonId?: string
  /**
   * 退货原因描述
   */
  description?: string
  /**
   * 退货单类型
- 仅退货             1
- 退货且退款          3
- 部分退货且部分退款   6
   */
  returnOrderType: number
  /**
   * 退货商品信息
   */
  returnInfo: SubOrderAfterInfo
}

/**
 * 取消退货请求
<AUTHOR>
@since 2025-03-07
 */
export class CancelOrderReturnRequest {
  orderNo: string
  returnOrderNo: string
  cancelReason?: string
}

/**
 * 拒绝退货请求
<AUTHOR>
@since 2025-03-07
 */
export class RefuseOrderReturnRequest {
  orderNo: string
  returnOrderNo: string
  /**
   * 审批意见
   */
  approveComment?: string
}

/**
 * 校验订单售后请求
 */
export class VerifyOrderAfterSaleRequest {
  orderNo: string
}

export class ReturnInfo {
  /**
   * 是否有退款
   */
  refund: boolean
  /**
   * 是否已完成
   */
  completed: boolean
}

export class AgreeOrderReturnBatchResponse {
  agreeOrderReturnResponseList: Array<AgreeOrderReturnResponse>
}

/**
 * 确认退货响应
<AUTHOR>
@since 2025-03-07
 */
export class AgreeOrderReturnResponse {
  code: string
  message: string
  returnOrderNo: string
}

/**
 * 退货申请返回结果
<AUTHOR>
@since 2025-03-07
 */
export class ApplyOrderReturnResponse {
  /**
   * 申请退货业务code
   */
  code: string
  /**
   * 申请退货信息
   */
  message: string
  data: ApplyOrderReturnData
}

export class ApplyOrderReturnData {
  /**
   * 生成的退货单号
   */
  returnOrderNo: string
}

/**
 * 取消退货响应
<AUTHOR>
@since 2025-03-07
 */
export class CancelOrderReturnResponse {
  code: string
  message: string
}

/**
 * <AUTHOR>
@since 2025-03-13
 */
export class QueryHymSchemeRefundInfoResponse {
  code: string
  message: string
  orderNo: string
  /**
   * 公需课程退款信息
   */
  publicCourse: ReturnInfo
  /**
   * 专业课程退款信息
   */
  professionalCourse: ReturnInfo
}

/**
 * 拒绝退货响应
<AUTHOR>
@since 2025-03-07
 */
export class RefuseOrderReturnResponse {
  code: string
  message: string
}

/**
 * 校验订单售后结果
 */
export class VerifyOrderAfterSaleResponse {
  /**
   * 校验订单售后业务code
- 200 成功
- -4000 失败
   */
  code: string
  /**
   * 校验订单售后信息
   */
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取华医网组合方案的课程退款情况
   * @param query 查询 graphql 语法文档
   * @param orderNo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async queryHymSchemeRefundInfo(
    orderNo: string,
    query: DocumentNode = GraphqlImporter.queryHymSchemeRefundInfo,
    operation?: string
  ): Promise<Response<QueryHymSchemeRefundInfoResponse>> {
    return commonRequestApi<QueryHymSchemeRefundInfoResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { orderNo },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 校验是否存在未处理完成的退货流程
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async VerifyOrderAfterSale(
    request: VerifyOrderAfterSaleRequest,
    mutate: DocumentNode = GraphqlImporter.VerifyOrderAfterSale,
    operation?: string
  ): Promise<Response<VerifyOrderAfterSaleResponse>> {
    return commonRequestApi<VerifyOrderAfterSaleResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 同意退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeOrderReturn(
    request: AgreeOrderReturnRequest,
    mutate: DocumentNode = GraphqlImporter.agreeOrderReturn,
    operation?: string
  ): Promise<Response<AgreeOrderReturnResponse>> {
    return commonRequestApi<AgreeOrderReturnResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 批量同意退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeOrderReturnBatch(
    request: AgreeOrderReturnBatchRequest,
    mutate: DocumentNode = GraphqlImporter.agreeOrderReturnBatch,
    operation?: string
  ): Promise<Response<AgreeOrderReturnBatchResponse>> {
    return commonRequestApi<AgreeOrderReturnBatchResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyOrderReturn(
    request: ApplyOrderReturnRequest,
    mutate: DocumentNode = GraphqlImporter.applyOrderReturn,
    operation?: string
  ): Promise<Response<ApplyOrderReturnResponse>> {
    return commonRequestApi<ApplyOrderReturnResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 取消退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async cancelOrderReturn(
    request: CancelOrderReturnRequest,
    mutate: DocumentNode = GraphqlImporter.cancelOrderReturn,
    operation?: string
  ): Promise<Response<CancelOrderReturnResponse>> {
    return commonRequestApi<CancelOrderReturnResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }

  /**   * 拒绝退货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async refuseOrderReturn(
    request: RefuseOrderReturnRequest,
    mutate: DocumentNode = GraphqlImporter.refuseOrderReturn,
    operation?: string
  ): Promise<Response<RefuseOrderReturnResponse>> {
    return commonRequestApi<RefuseOrderReturnResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
