/*
 * @Description: 资讯状态改变 资讯列表使用
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-10 09:36:59
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-02-17 10:38:21
 */
import { ResponseStatus } from '@hbfe/common'
import MsBusinessNews from '@api/ms-gateway/ms-news-v1'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import { publishNews } from '@api/ms-gateway/ms-news-v1/graphql-importer'

export default class MutationNewsChangeStatus {
  id: string
  constructor(id?: string) {
    if (id) {
      this.id = id
    }
  }
  /**
   * 置为草稿
   * @returns ResponseStatus
   */
  async doDraftNews(): Promise<ResponseStatus> {
    //
    const { status } = await MsBusinessNews.unPublishNews(this.id)
    return status
  }
  /**
   * 发布草稿
   * @returns ResponseStatus
   */
  async doPublishNews(): Promise<ResponseStatus> {
    const { status } = await MsBusinessNews.publishNews(this.id)
    return status
  }

  /**
   * 批量发布草稿
   * @param ids
   */
  async batchPublishNews(ids: Array<string>) {
    const reWriteGQL = new RewriteGraph<void, string>(MsBusinessNews._commonQuery, publishNews)
    const res = await reWriteGQL.request(ids)

    return res
  }
}
