import QueryStudentVo from '@api/service/management/user/query/student/vo/StudentQueryVo'
import { StudentQueryRequest } from '@api/diff-gateway/qztg-data-export-gateway-backstage'

class StudentQueryVo extends QueryStudentVo {
  /**
   * 注册来源
   */
  sourceType = ''
  toDto() {
    const params = super.toDto()
    params.account.sourceTypes = this.sourceType ? [Number(this.sourceType)] : undefined
    return params
  }
  toExport() {
    const params = super.toExport() as StudentQueryRequest
    params.account.sourceTypes = this.sourceType ? [Number(this.sourceType)] : undefined
    return params
  }
}

export default StudentQueryVo
