import store from '@/store'
import { Module, VuexModule, getModule, Action, Mutation } from 'vuex-module-decorators'
import PlatformExamGateway, {
  AnswerQuestionStatisticPerDayDTO,
  AnswerQuestionStatisticPerDayParamDTO,
  PracticeCountTrendDTO,
  PracticeTrendStatisticParamDTO,
  PracticeType,
  UserWrongQuestionStatisticDTO,
  UserWrongQuestionStatisticParamDTO,
  UserWrongQuestionStatisticSummaryDTO,
  UserWrongQuestionSummaryParamDTO,
  WrongQuestionType
} from '@api/gateway/PlatformExam'
import Response, { ResponseStatus } from '../../../../../Response'
import { Role, RoleType, Secure } from '../../../../../Secure'
import { DateRange } from '@api/service/customer/answer-record/report/enums/enums'
import SyllabusModule from '@api/service/customer/syllabus/SyllabusModule'
import Chapter from '@api/service/common/models/syllabus/Chapter'
import AnswerRecordUtils from '@api/service/customer/answer-record/report/utils/AnswerRecordUtils'

class StateCache {
  constructor(schemeId: string, majorId: string) {
    this.schemeId = schemeId
    this.majorId = majorId
  }

  // 方案id
  schemeId: string
  majorId: string
  // 总计信息
  summary: UserWrongQuestionStatisticSummaryDTO = new UserWrongQuestionStatisticSummaryDTO()
  allTypeWrongQuestionStatistic: Array<UserWrongQuestionStatisticDTO>
  allCategoryWrongQuestionStatistic: Array<UserWrongQuestionStatisticDTO>
  // 总计信息基础
  summaryBase: UserWrongQuestionStatisticSummaryDTO = new UserWrongQuestionStatisticSummaryDTO()
  // 总计信息要比较的
  summaryBaseToCompare: UserWrongQuestionStatisticSummaryDTO = new UserWrongQuestionStatisticSummaryDTO()
  // 每日作答统计基础
  answerStatisticPerDayBase: Array<AnswerQuestionStatisticPerDayDTO> = new Array<AnswerQuestionStatisticPerDayDTO>()
  // 练习量趋势基础
  practiceCountTrendBase: Array<PracticeCountTrendDTO> = new Array<PracticeCountTrendDTO>()
  // 是否需要重载，在学员触发去作答练习是置为true，在下次取数时先清空数据然后加载新数据
  needReload = true
  // 最新一次加载时间，从apollo配置的超时时间与当前时间判断，在下次取数时清空当前取数数据然后加载新数据
  latestLoadTime: Date = new Date()
}

export interface IState {
  schemeStateCacheMap: Array<StateCache>
}

@Module({
  namespaced: true,
  name: 'CorrectionReportModule',
  store,
  dynamic: true
})
class CorrectionReportModule extends VuexModule implements IState {
  schemeStateCacheMap = new Array<StateCache>()

  @Role([RoleType.user])
  @Action
  async init(payload: {
    schemeId: string
    majorId: string
    range: DateRange
    customStartDate: Date
    customEndDate: Date
  }) {
    if (
      !this.schemeStateCacheMap.find(p => p.schemeId === payload.schemeId) ||
      this.schemeStateCacheMap.find(p => p.schemeId === payload.schemeId)?.needReload
    ) {
      if (!payload.schemeId || !payload.majorId || payload.range === undefined) {
        console.log('初始化错题重答报告参数异常' + JSON.stringify(payload))
        return new ResponseStatus(500, '参数为空，请检查')
      }
      if (!this.schemeStateCacheMap.find(p => p.schemeId === payload.schemeId)) {
        const stateCache = new StateCache(payload.schemeId, payload.majorId)
        this.setStateCacheToSchemeStateCache(stateCache)
      }

      // 初始化考纲信息
      const status = await SyllabusModule.init()
      if (!status.isSuccess()) {
        return status
      }
      const leafSyllabus: Array<Chapter> = SyllabusModule.getLeafSyllabusByMajorId(payload.majorId)
      const stateCache = new StateCache(payload.schemeId, payload.majorId)
      const currentDate = new Date()
      // 统计信息
      const summaryParam: UserWrongQuestionSummaryParamDTO = new UserWrongQuestionSummaryParamDTO()
      summaryParam.schemeId = payload.schemeId
      summaryParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      summaryParam.type = WrongQuestionType.ALL
      summaryParam.answerTimeStart = AnswerRecordUtils.getStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      summaryParam.answerTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      let response: Response<any> = await PlatformExamGateway.statisticUserWrongQuestionSummary(summaryParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.summary = response.data
      const param: UserWrongQuestionStatisticParamDTO = new UserWrongQuestionStatisticParamDTO()
      param.schemeId = payload.schemeId
      param.type = WrongQuestionType.ALL
      param.tagIdList = leafSyllabus.map(leaf => leaf.id)
      param.firstTimeStart = AnswerRecordUtils.getStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      param.firstTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      response = await PlatformExamGateway.statisticUserWrongQuestionGroupByQuestionType(param)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allTypeWrongQuestionStatistic = response.data

      response = await PlatformExamGateway.statisticUserWrongQuestionGroupByQuestionCategory(param)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.allCategoryWrongQuestionStatistic = response.data

      // 基准数据
      summaryParam.answerTimeStart = undefined
      summaryParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      summaryParam.answerTimeEnd = AnswerRecordUtils.getEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      response = await PlatformExamGateway.statisticUserWrongQuestionSummary(summaryParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.summaryBase = response.data
      // 每日练习数

      const submitAnswerTimeBegin = AnswerRecordUtils.getTrendQueryStartTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      // 错题重练练习量趋势
      const practiceTrendParam: PracticeTrendStatisticParamDTO = new PracticeTrendStatisticParamDTO()
      practiceTrendParam.schemeId = payload.schemeId
      if (submitAnswerTimeBegin) {
        practiceTrendParam.completeTimeStart = submitAnswerTimeBegin
      } else {
        delete practiceTrendParam.completeTimeStart
      }
      practiceTrendParam.completeTimeEnd = AnswerRecordUtils.getTrendQueryEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      practiceTrendParam.statisticSize = AnswerRecordUtils.getTrendQueryTimes(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      practiceTrendParam.statisticPracticeTypeList = [PracticeType.CORRECTION]
      response = await PlatformExamGateway.statisticUserPracticeCountTrend(practiceTrendParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.practiceCountTrendBase = response.data

      const perDayParam: AnswerQuestionStatisticPerDayParamDTO = new AnswerQuestionStatisticPerDayParamDTO()
      perDayParam.schemeId = payload.schemeId
      if (submitAnswerTimeBegin) {
        perDayParam.submitAnswerTimeBegin = submitAnswerTimeBegin
      } else {
        delete perDayParam.submitAnswerTimeBegin
      }
      perDayParam.submitAnswerTimeEnd = AnswerRecordUtils.getTrendQueryEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      perDayParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      response = await PlatformExamGateway.statisticUserAnswerPerDay(perDayParam)
      stateCache.answerStatisticPerDayBase = response.data

      // 要比较的数据
      summaryParam.answerTimeStart = undefined
      summaryParam.tagIdList = leafSyllabus.map(leaf => leaf.id)
      summaryParam.answerTimeEnd = AnswerRecordUtils.getToCompareQueryEndTime(
        currentDate,
        payload.range,
        payload.customStartDate,
        payload.customEndDate
      )
      response = await PlatformExamGateway.statisticUserWrongQuestionSummary(summaryParam)
      if (!response.status.isSuccess()) {
        return response.status
      }
      stateCache.summaryBaseToCompare = response.data

      this.setStateCacheToSchemeStateCache(stateCache)
    } else if (
      (this.schemeStateCacheMap.find(p => p.schemeId === payload.schemeId)?.latestLoadTime?.getTime() || 0) <
      new Date().getTime() - 10000
    ) {
      this.setNeedReload({
        schemeId: payload.schemeId,
        majorId: payload.majorId
      })
      this.init(payload)
    }
    return new ResponseStatus(200)
  }

  @Mutation
  setStateCacheToSchemeStateCache(payload: StateCache) {
    this.schemeStateCacheMap = this.schemeStateCacheMap.filter(p => p.schemeId !== payload.schemeId)
    this.schemeStateCacheMap.push(payload)
  }

  @Mutation
  setNeedReload(payload: any) {
    const stateCache = this.schemeStateCacheMap.find(p => p.schemeId === payload.schemeId)
    if (stateCache) {
      stateCache.needReload = true
    }
  }

  /**
   * 获取状态层数据
   * @param state
   */
  get getState() {
    return (schemeId: string) => {
      return this.schemeStateCacheMap.find(p => p.schemeId === schemeId)
    }
  }

  /**
   * 获取总计信息
   * @param state
   */
  get getSummary() {
    return (schemeId: string) => {
      return this.schemeStateCacheMap.find(p => p.schemeId === schemeId)?.summary
    }
  }

  /**
   * 获取所有题型的错题统计
   * @param state
   */
  get getAllTypeWrongQuestionStatistic() {
    return (schemeId: string) => {
      return this.schemeStateCacheMap.find(p => p.schemeId === schemeId)?.allTypeWrongQuestionStatistic
    }
  }

  /**
   * 获取所有试题类型的错题统计
   * @param state
   */
  get getAllCategoryWrongQuestionStatistic() {
    return (schemeId: string) => {
      return this.schemeStateCacheMap.find(p => p.schemeId === schemeId)?.allCategoryWrongQuestionStatistic
    }
  }

  /**
   * 获取总计信息基础
   * @param state
   */
  get getSummaryBase() {
    return (schemeId: string) => {
      return this.schemeStateCacheMap.find(p => p.schemeId === schemeId)?.summaryBase
    }
  }

  /**
   * 获取总计信息要比较的
   * @param state
   */
  get getSummaryBaseToCompare() {
    return (schemeId: string) => {
      return this.schemeStateCacheMap.find(p => p.schemeId === schemeId)?.summaryBaseToCompare
    }
  }

  /**
   * 获取每日作答统计基础
   * @param state
   */
  get getAnswerStatisticPerDayBase() {
    return (schemeId: string) => {
      return this.schemeStateCacheMap.find(p => p.schemeId === schemeId)?.answerStatisticPerDayBase
    }
  }

  /**
   * 获取每日作答统计基础
   * @param state
   */
  get getPracticeCountTrendBase() {
    return (schemeId: string) => {
      return this.schemeStateCacheMap.find(p => p.schemeId === schemeId)?.practiceCountTrendBase
    }
  }

  get wrongAnswerCountChange() {
    return (schemeId: string) => {
      return (
        (this.getSummaryBase(schemeId)?.wrongQuestionTimes || 0) -
        (this.getSummaryBaseToCompare(schemeId)?.wrongQuestionTimes || 0)
      )
    }
  }

  get wrongRateChange() {
    return (schemeId: string) => {
      return (this.getSummaryBase(schemeId)?.wrongRate || 0) - (this.getSummaryBaseToCompare(schemeId)?.wrongRate || 0)
    }
  }
}

export default getModule(CorrectionReportModule)
