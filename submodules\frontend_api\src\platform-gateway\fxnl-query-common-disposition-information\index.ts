import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = ''
// 请求地址路径
export const SERVER_URL = '/web/gql/fxnl-query-common-disposition-information'

// 是否微服务
const isMicroService = false

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'fxnl-query-common-disposition-information'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * <AUTHOR>
@description:行业字典数据
@date 2024/10/16 15:02
 */
export class IndustryDictionaryResponse {
  industryDictionaryList: Array<IndustryDictionary>
  code: string
  message: string
}

/**
 * <AUTHOR>
@description:年度字典数据
@date 2024/10/16 15:03
 */
export class YearDictionaryResponse {
  yearList: Array<Dictionary>
  code: string
  message: string
}

/**
 * 字典数据
<AUTHOR>
 */
export class Dictionary {
  /**
   * 字典ID
   */
  id: string
  /**
   * 排序
   */
  sort: number
}

/**
 * 行业字典实体
<AUTHOR>
 */
export class IndustryDictionary {
  /**
   * 行业字典ID
   */
  id: string
  /**
   * 行业属性字典ID
   */
  propertyId: string
  /**
   * 排序
   */
  sort: number
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getIndustriesByServiceIdInDistributor(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getIndustriesByServiceIdInDistributor,
    operation?: string
  ): Promise<Response<IndustryDictionaryResponse>> {
    return commonRequestApi<IndustryDictionaryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 获取分销商所属服务商专属年度列表
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getYearsByServiceIdInDistributor(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getYearsByServiceIdInDistributor,
    operation?: string
  ): Promise<Response<YearDictionaryResponse>> {
    return commonRequestApi<YearDictionaryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
