import { StudentCourseQuiz } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningForestage'

class AfterCourseTestLastResult extends StudentCourseQuiz {
  get isQualified() {
    return this.courseQuizStatus === 2
  }

  static from(remote: StudentCourseQuiz) {
    const detail = new AfterCourseTestLastResult()
    Object.assign(detail, remote)
    return detail
  }
}

export default AfterCourseTestLastResult
