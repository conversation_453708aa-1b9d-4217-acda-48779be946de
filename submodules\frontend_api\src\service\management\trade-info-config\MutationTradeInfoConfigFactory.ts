import MutationCreateReceiveAccount from './mutation/MutationCreateReceiveAccount'
import MutationPreparePurchaseChannel from './mutation/MutationPreparePurchaseChannel'
import MutationReceiveAccount from './mutation/MutationReceiveAccount'
import MutationUpdatePurchaseChannel from './mutation/MutationUpdatePurchaseChannel'
import MutationUpdateReceiveAccount from './mutation/MutationUpdateReceiveAccount'
import ReceiveAccountVo from './query/vo/ReceiveAccountVo'

class MutationTradeInfoConfigFactory {
  /**
   * 获取购买渠道配置
   * @returns
   */
  getPreparePurchaseChannel() {
    return MutationPreparePurchaseChannel
  }

  /**
   * 获取更新购买渠道
   * @returns
   */
  getUpdatePurchaseChannel(id?: string, type?: number) {
    return new MutationUpdatePurchaseChannel(id, type)
  }

  /**
   * 获取收款账户操作类
   * @returns
   */
  getReceiveAccount(receiveAccount: ReceiveAccountVo) {
    return new MutationReceiveAccount(receiveAccount)
  }

  /**
   * 创建收款账户类
   */
  getCreateReceiveAccount() {
    return new MutationCreateReceiveAccount()
  }

  /**
   * 更新收款账户类
   */
  getUpdateReceiveAccount(id: string) {
    return new MutationUpdateReceiveAccount(id)
  }
}
export default MutationTradeInfoConfigFactory
