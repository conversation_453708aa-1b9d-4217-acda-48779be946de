<!--
 * @Author: lix<PERSON><PERSON> <EMAIL>
 * @Date: 2024-03-20 15:51:59
 * @LastEditors: lixinye <EMAIL>
 * @LastEditTime: 2024-03-20 16:15:32
 * @FilePath: \jxjyv2_frontend_web_admin\src\unit-share\network-school\training\special-topics\manage\components\__components__\premium-course-components\no-classified.vue
 * @Description: 有分类表格
-->
<template>
  <div>
    <el-row type="flex" :gutter="20">
      <el-col :sm="7" :lg="6" :xl="5">
        <div class="m-course-classify">
          <div class="m-tit is-mini">
            <span class="tit-txt">精品课程分类</span>
          </div>
          <div class="f-pl15 f-mt20">
            <el-button type="primary" icon="el-icon-plus">添加分类</el-button>
            <div class="f-mt20">
              <div class="item">
                <div class="f-flex-sub">分类名称</div>
                <i class="icon el-icon-delete"></i>
              </div>
              <div class="item current">
                <div class="f-flex-sub">分类名称</div>
                <i class="icon el-icon-delete"></i>
              </div>
              <div class="item">
                <div class="f-flex-sub">分类名称</div>
                <i class="icon el-icon-delete"></i>
              </div>
              <div class="item">
                <el-input v-model="form.name" placeholder="请输入" class="f-flex-sub" />
                <i class="icon el-icon-delete"></i>
              </div>
              <div class="item">
                <el-input v-model="form.name" placeholder="请输入" class="f-flex-sub" />
                <i class="icon el-icon-delete"></i>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      <el-col :sm="17" :lg="18" :xl="19">
        <div class="m-tit is-mini">
          <span class="tit-txt">精品课程列表</span>
        </div>
        <div class="f-pl15 f-mtb20">
          <el-button type="primary" icon="el-icon-plus" class="f-mb20">添加精品课程</el-button>
          <!--表格-->
          <el-table stripe :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="排序" min-width="70" align="center">
              <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
            </el-table-column>
            <el-table-column label="课程名称" min-width="300">
              <template>课程名称课程名称课程名称课程名称课程名称</template>
            </el-table-column>
            <el-table-column label="课程分类" min-width="180">
              <template>课程分类课程分类</template>
            </el-table-column>
            <el-table-column label="操作时间" min-width="180">
              <template>2020-11-11 12:20:20</template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template>
                <el-button type="text" size="mini">取消展示</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <!-- <el-pagination
            background
            class="f-mt15 f-tr"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage4"
            :page-sizes="[100, 200, 300, 400]"
            :page-size="100"
            layout="total, sizes, prev, pager, next, jumper"
            :total="400"
          >
          </el-pagination> -->
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'

  @Component({
    components: {}
  })
  export default class extends Vue {
    // ========================= 变量 =========================

    /**
     * @description 渲染数据
     * */
    tableData = [{}, {}]

    /**
     * @description 渲染数据
     * */
    form = { name: '123' }
  }
</script>
