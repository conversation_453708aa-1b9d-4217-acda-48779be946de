import { ExcellentCourseItemResponse } from '@api/ms-gateway/ms-servicer-series-v1'
import SimpleUserInfo from '@api/service/common/models/SimpleUserInfo'
import CourseCategory from '@api/service/customer/course/query/vo/CourseCategory'
import { CourseResponse } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'

class SimpleCourseDetail {
  id: string
  name: string
  coverImage: string
  period: number
  description: string
  teachers: Array<SimpleUserInfo> = new Array<SimpleUserInfo>()
  categories: Array<CourseCategory> = new Array<CourseCategory>()

  get categoriesDesc() {
    return this.categories.map(category => category.name).join('、')
  }

  get teachersDesc() {
    return this.teachers.map(teacher => teacher.name).join('、')
  }

  static from(remote: ExcellentCourseItemResponse) {
    const detail = new SimpleCourseDetail()
    detail.id = remote.courseId
    return detail
  }

  from(courseDetail: CourseResponse) {
    this.name = courseDetail.name
    this.coverImage = courseDetail.iconPath
      ? `${ConfigCenterModule.getFrontendApplication(frontendApplication.mfsHost)}${courseDetail.iconPath}`
      : ''
    this.period = courseDetail.period
    this.categories = courseDetail.categorys.map(CourseCategory.from)
    this.description = courseDetail.aboutsContent
    this.teachers = courseDetail.teacherIds.map(id => {
      const teacher = new SimpleUserInfo()
      teacher.id = id
      return teacher
    })
  }
}

export default SimpleCourseDetail
