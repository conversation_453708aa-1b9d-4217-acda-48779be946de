<template>
  <div>
    <el-drawer title="批量更新方案展示" :visible.sync="showBatchUpdateDialog" size="800px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="24">
            <el-alert type="warning" :closable="false" class="m-alert">
              <p>温馨提示：</p>
              <p>1. 如需批量导入培训班，请先下载 <span class="f-ci">更新培训方案展示模板</span>。</p>
              <p>2. 支持批量更新培训方案是否展示在专题门户上。</p>
            </el-alert>
            <el-form ref="form" label-width="auto" class="m-form f-mt20">
              <el-steps direction="vertical" :active="4" class="m-vertical-steps" style="padding: 20px 0">
                <el-step title="下载更新培训方案展示模板，按表格中提示说明填写信息。">
                  <div slot="description">
                    <el-button
                      type="primary"
                      size="small"
                      plain
                      class="f-mt5"
                      icon="el-icon-download"
                      @click="downloadTemplate"
                    >
                      下载模板
                    </el-button>
                  </div>
                </el-step>
                <el-step title="上传填写好的更新方案表格">
                  <div slot="description">
                    <min-upload-file v-model="hbFileUploadResponse" :file-type="1" :is-protected="true">
                      <el-button type="primary" size="small" plain class="f-mt5" icon="el-icon-upload2">
                        选择文件
                      </el-button>
                    </min-upload-file>
                    <div slot="tip" class="el-upload__tip" v-if="!hbFileUploadResponse.url">
                      <i class="el-icon-warning"></i>
                      <span class="txt">导入的文件格式必须为xls,xlsx文件</span>
                    </div>
                  </div>
                </el-step>
              </el-steps>
              <el-form-item class="m-btn-bar f-tc">
                <el-button @click="showBatchUpdateDialog = false">取消</el-button>
                <el-button type="primary" :loading="saveLoading" @click="save">确定</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import ThematicManagementList from '@api/service/management/thematic-management/ThematicManagementList'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import MinUploadFile from '@hbfe/jxjy-admin-platform/src/function/components/min-upload-file.vue'
  import { bind, debounce } from 'lodash-decorators'
  import { Component, Vue } from 'vue-property-decorator'

  @Component({
    components: { MinUploadFile }
  })
  export default class extends Vue {
    // 是否展示弹窗
    showBatchUpdateDialog = false
    //文件上传之后的回调参数
    hbFileUploadResponse = new HBFileUploadResponse()
    // 提示语
    message = ''
    // 保存时的loading
    saveLoading = false
    // 批量更新方案
    thematicManagementList = new ThematicManagementList()

    // 打开弹出
    open() {
      this.showBatchUpdateDialog = true
      this.hbFileUploadResponse = new HBFileUploadResponse()
    }

    //下载模板
    @bind
    @debounce(1000)
    async downloadTemplate() {
      const link = document.createElement('a')
      const res = this.$router.resolve({
        name: '/mfs/ms-file/public/402896786e449jxjyPlatformVersion/402896786e449f3901jxjySubProject/template/更新培训方案展示模板.xls'
      })

      link.id = 'taskLink'
      link.style.display = 'none'
      link.href = res.location.name
      const urlArr = link.href.split('.')
      const typeName = urlArr.pop()
      link.setAttribute('download', '更新培训方案展示模板')
      document.body.appendChild(link)
      link.click()
      link.remove()
    }

    // 保存
    async save() {
      this.message = ''
      if (!this.hbFileUploadResponse.url) {
        this.$message.error('请先选择上传文件')
        return
      }
      console.log(this.hbFileUploadResponse, 'zrr0707')

      try {
        this.saveLoading = true
        await this.thematicManagementList.batchUpdateSchemeShow({
          filePath: this.hbFileUploadResponse.url,
          fileName: this.hbFileUploadResponse.fileName
        })
        this.$emit('save')
        this.showBatchUpdateDialog = false
      } catch (error: any) {
        console.log(error)
        this.$message.error('系统异常')
      } finally {
        this.saveLoading = false
      }
    }
  }
</script>
