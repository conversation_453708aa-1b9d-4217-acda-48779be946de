import AutonomousCourseLearningCourseAppraisalConfig from '@api/service/common/scheme/model/schemeDto/autonomous-course-learning/config/course-appraisal-config/AutonomousCourseLearningCourseAppraisalConfig'
import AutonomousCourseLearningCourseCompleteEvaluateConfig from '@api/service/common/scheme/model/schemeDto/autonomous-course-learning/config/course-complete-evaluate-config/AutonomousCourseLearningCourseCompleteEvaluateConfig'
import AutonomousCourseLearningCourseQuizConfig from '@api/service/common/scheme/model/schemeDto/autonomous-course-learning/config/course-quiz-config/AutonomousCourseLearningCourseQuizConfig'
import AutonomousCourseLearningCourseTrainingOutline from '@api/service/common/scheme/model/schemeDto/autonomous-course-learning/config/course-training-outlines/AutonomousCourseLearningCourseTrainingOutline'

/**
 * @description 自主选课学习方式-配置
 */
class AutonomousCourseLearningConfig {
  /**
   * 资源id
   */
  id: string
  /**
   * 课程大纲列表
   */
  courseTrainingOutlines: AutonomousCourseLearningCourseTrainingOutline[]
  /**
   * 课后测验配置
   */
  courseQuizConfig: AutonomousCourseLearningCourseQuizConfig
  /**
   * 课程完成评定配置
   */
  courseCompleteEvaluateConfig: AutonomousCourseLearningCourseCompleteEvaluateConfig
  /**
   * 课程评价配置
   */
  courseAppraisalConfig: AutonomousCourseLearningCourseAppraisalConfig
  /**
   * 操作类型
   */
  operation: number
}

export default AutonomousCourseLearningConfig
