/**
 * @description 资讯分类
 */
import MenuVo from '@api/service/common/online-school-config/vo/MenuVo'
import { MenuTypeEnum } from '@api/service/customer/online-school-config/online-school-partal-config/query/enum/MenuTypeEnum'

class InfoCategoryVo {
  /**
   * id
   */
  id: string
  /**
   * 父分类id
   */
  parentId: string
  /**
   * 分类名称
   */
  name: string
  /**
   * 父分类名称
   */
  parentName: string
  /**
   * 分类说明
   */
  description: string
  /**
   * 资讯类别类型
   */
  categoryType: MenuTypeEnum
  /**
   * 状态 0-停用 1-启用
   */
  status: number
  /**
   * code 表征唯一性
   */
  code: string

  static from(response: MenuVo) {
    const infoCategory = new InfoCategoryVo()
    infoCategory.id = response.id
    infoCategory.parentId = response.parentId
    infoCategory.name = response.name
    infoCategory.categoryType = response.type
    infoCategory.code = response.code
    return infoCategory
  }
}

export default InfoCategoryVo
