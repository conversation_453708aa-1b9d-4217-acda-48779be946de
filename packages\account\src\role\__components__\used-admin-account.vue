<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: Lin yt
 * @Date: 2022-02-15 16:01:36
 * @LastEditors: <PERSON> yt
 * @LastEditTime: 2022-06-10 11:18:36
-->
<template>
  <div>
    <el-table
      stripe
      :data="adminAccountList"
      max-height="240"
      highlight-current-row
      class="m-table"
      v-loading="query.loading"
    >
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="管理员" min-width="160" fixed="left" prop="userName"></el-table-column>
      <el-table-column label="帐号" min-width="200" prop="adminAccount"></el-table-column>
      <el-table-column label="状态" min-width="140">
        <template slot-scope="scope">
          <div>
            {{ filterAdminInfoStatus(scope.row.status) }}
          </div>
        </template>
      </el-table-column>
    </el-table>
    <hb-pagination :total-size="totalSize" :page="page" class="f-mt15 f-tr" @current-change="currentPageChange">
    </hb-pagination>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  import { Query, UiPage } from '@hbfe/common'
  import PageAdminListRequest from '@api/service/management/user/query/manager/vo/PageAdminListRequest'
  import RoleDetail from '@api/service/management/authority/role/RoleDetail'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import PageAdminInfoResponse from '@api/service/management/user/query/manager/vo/PageAdminInfoResponse'

  @Component
  export default class extends Vue {
    page: UiPage = new UiPage()
    queryPageAdminListObj = UserModule.queryUserFactory.queryManager
    totalSize = 0
    adminAccountList = [] as any
    constructor() {
      super()
      this.page = new UiPage(this.roleChange, this.roleChange)
    }

    @Prop({
      type: Object,
      default: '',
      required: true
    })
    role: RoleDetail
    query: Query = new Query()

    @Watch('role.id', { deep: true, immediate: true })
    async roleChange() {
      console.log(this.role, 'this.role')

      const pageAdminListRequest = new PageAdminListRequest()
      pageAdminListRequest.roleId = this.role.id
      pageAdminListRequest.roleCategory = this.role.category
      let res: Array<PageAdminInfoResponse> = []
      if (QueryManagerDetail.hasCategory(CategoryEnums.fxs)) {
        res = await this.queryPageAdminListObj.queryPageDistributorAdminList(this.page, pageAdminListRequest)
      } else {
        res = await this.queryPageAdminListObj.queryPageAdminList(this.page, pageAdminListRequest)
      }
      this.totalSize = this.page.totalSize
      this.adminAccountList = res

      this.query.loading = false
    }
    filterAdminInfoStatus(status: number) {
      // 正常: 1; 冻结: 2; 注销: 3;
      switch (status) {
        case 1:
          return '正常'
        case 2:
          return '冻结'
        case 3:
          return '注销'
      }
    }

    async currentPageChange() {
      this.page.currentChange(this.page.pageNo)
    }
  }
</script>
