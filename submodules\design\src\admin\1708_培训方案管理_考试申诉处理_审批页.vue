<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">考试申诉处理</el-breadcrumb-item>
      <el-breadcrumb-item>审批申诉情况</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">学员申诉信息</span>
        </div>
        <el-row class="no-gutter">
          <el-col :span="15">
            <el-form ref="form" :model="form" label-width="auto" class="m-text-form border-right f-pr30 f-ml30 f-mt10">
              <el-col :span="12">
                <el-form-item label="姓名：">张三丰</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="身份证号：">350103198801011234</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="考试名称：">考试名称考试名称考试名称考试名称</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="考试总分/及格分：">100分/80分</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="是否合格：">合格</el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="剩余考试次数：">10次</el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="申诉原因：">申诉原因申诉原因申诉原因申诉原因申诉原因</el-form-item>
              </el-col>
            </el-form>
          </el-col>
          <el-col :span="9">
            <div class="u-benchmark-photos-medium f-pt10 f-pl30">
              <img class="photo" src="./assets/images/face-pic.jpg" alt="" />
              <div class="f-tc">基准照</div>
            </div>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">拍摄记录</span>
        </div>
        <el-row class="no-gutter">
          <el-form :inline="true" label-width="150px" class="m-text-form f-mt10">
            <el-col :span="8">
              <el-form-item label="进入考试时间：">2017-12-12 17:23:23</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="考试成绩：">100分</el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否合格：">合格</el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="拍摄照片" min-width="150">
            <template>
              <img src="./assets/images/face-pic.jpg" alt="" class="u-benchmark-photos-small" />
            </template>
          </el-table-column>
          <el-table-column label="拍摄时间" min-width="180">
            <template>2017-12-12 17:23:23</template>
          </el-table-column>
          <el-table-column label="匹配结果" min-width="200">
            <template>匹配</template>
          </el-table-column>
          <el-table-column label="考试时间点" width="140" align="center" fixed="right">
            <template>00:01:00</template>
          </el-table-column>
        </el-table>
      </el-card>
      <el-card shadow="never" class="m-card">
        <div slot="header" class="">
          <span class="tit-txt">申诉处理</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form">
              <el-col :span="24">
                <el-form-item label="审批结果：" required>
                  <el-radio-group v-model="form.resource">
                    <el-radio v-model="radio1" label="1" class="f-mr10">通过</el-radio>
                    <el-radio v-model="radio1" label="1" class="f-mr10">不通过</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="审批理由：">
                  <el-input type="textarea" :rows="10" placeholder="请输入内容" v-model="textarea"> </el-input>
                </el-form-item>
              </el-col>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeNames: ['1'],
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleChange(val) {
        console.log(val)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
