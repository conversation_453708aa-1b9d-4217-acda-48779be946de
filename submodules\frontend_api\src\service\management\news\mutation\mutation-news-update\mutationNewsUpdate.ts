/*
 * @Description: 更新资讯
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-17 19:34:54
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-05-18 14:24:48
 */
import { ResponseStatus } from '@hbfe/common'
import MsBusinessNews, { NewsUpdateRequest } from '@api/ms-gateway/ms-news-v1'
import { NewsDetailResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import NewsDetailVo from '../../query/query-news-detail/vo/NewsDetail'
import NewsUpdate from './dto/NewsUpdate'
import NewsSpecialUpdate from '@api/service/management/news/mutation/mutation-news-update/dto/NewsSpecialUpdate'

export default class MutitionNewsUpdate {
  /**
   * 更新资讯（修改资讯）
   * @param param 资讯详情
   * @returns ResponseStatus
   */
  async doUpdateNews(newsDetailVo: NewsDetailVo): Promise<ResponseStatus> {
    const param = NewsUpdate.from(newsDetailVo)
    const { status } = await MsBusinessNews.updateNews(param)
    return status
  }

  /**
   * 更新专题资讯
   * @param newsDetailVo 资讯详情
   * @returns ResponseStatus
   */
  async doUpdateSpecialNews(newsDetailVo: NewsDetailVo): Promise<ResponseStatus> {
    const request = NewsSpecialUpdate.from(newsDetailVo)
    const { status } = await MsBusinessNews.updateSpecialSubjectNews(request)
    return status
  }
}
