/**
 * 付款类型
 */
import AbstractEnum from '../AbstractEnum'

enum PayTypeEnum {
  not_pay = -1,
  online = 1,
  offline = 2
}

export { PayTypeEnum }

class PayType extends AbstractEnum<PayTypeEnum> {
  static enum = PayTypeEnum

  constructor() {
    super()
    this.map[PayTypeEnum.not_pay] = '未支付'
    this.map[PayTypeEnum.online] = '线上支付'
    this.map[PayTypeEnum.offline] = '线下支付'
  }
}

export default PayType
