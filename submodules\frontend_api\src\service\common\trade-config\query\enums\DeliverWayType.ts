/**
 * 配送方式，跟自取点的 shippingMethod 要区分开来
 *  后端这么设计的
 */
import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum DeliverWayTypeEnum {
  // 快递
  deliver = 1,
  // 自取
  pick_up = 2
}

class DeliverWayType extends AbstractEnum<DeliverWayTypeEnum> {
  static enum = DeliverWayTypeEnum

  constructor(status?: DeliverWayTypeEnum) {
    super()
    this.current = status
    this.map.set(DeliverWayTypeEnum.deliver, '快递')
    this.map.set(DeliverWayTypeEnum.pick_up, '自取')
  }
}

export default DeliverWayType
