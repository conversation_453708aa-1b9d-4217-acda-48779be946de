import { DateScopeRequest, QuestionRequest } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'
import { QuestionTypes } from '../../../exam-paper/enum/ExamQuestionTypes'
import DateScopeRequestVo from './DateScopeRequestVo'

class QuestionRequestVo extends QuestionRequest {
  /**
   * 试题ID集合
   */
  questionIdList?: Array<string> = []
  /**
   * 题库ID集合
   */
  libraryIdList?: Array<string> = []
  /**
   * 关联课程ID集合
   */
  relateCourseIds?: Array<string> = []
  /**
   * 试题题目
   */
  topic?: string = ''
  /**
   * 试题类型
     @see QuestionTypeEnum
  */
  questionType?: QuestionTypes = null
  /**
   * 创建时间范围
   */
  createTimeScope?: DateScopeRequestVo = new DateScopeRequestVo()
  /**
   * 是否启用
   */
  enable?: boolean = null

  // 模型转换
  toDto() {
    const questionRequestDto = new QuestionRequest()
    questionRequestDto.questionIdList = this.questionIdList
    questionRequestDto.libraryIdList = this.libraryIdList
    questionRequestDto.relateCourseIds = this.relateCourseIds
    questionRequestDto.topic = this.topic
    questionRequestDto.questionType = this.questionType
    questionRequestDto.createTimeScope = new DateScopeRequest()
    questionRequestDto.createTimeScope.beginTime = this.createTimeScope.beginTime
    questionRequestDto.createTimeScope.endTime = this.createTimeScope.endTime
    questionRequestDto.isEnabled = this.enable
    return questionRequestDto
  }
}

export default QuestionRequestVo
