import store from '@/store'
import QueryTradeBatchFactory from '@api/service/customer/trade/batch/QueryTradeBatchFactory'
import MutationTradeFactory from '@api/service/customer/trade/single/MutationTradeFactory'
import QueryTradeSingleFactory from '@api/service/customer/trade/single/QueryTradeFactory'
import InvoiceFactor from '@api/service/customer/trade/single/invoice/InvoiceFactor'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
/**
 * 培训班中控层
 */
@Module({
  name: 'CustomerTradeModule',
  dynamic: true,
  namespaced: true,
  store
})
class TradeModule extends VuexModule {
  // region properties

  /**
   *个人订单查询工厂
   */
  get singleTradeFactory() {
    return QueryTradeSingleFactory
  }
  /**
   *个人订单业务工厂
   */
  get MutationTradeFactory() {
    return MutationTradeFactory
  }

  /**
   * 批次订单查询工厂
   */
  get queryBatchTradeFactory() {
    return QueryTradeBatchFactory
  }

  /**
   *批次单工厂
   */
  // get mutationTrainClassFactory() {
  //   return
  // }
  get QuerySingleInoiveFactory() {
    return InvoiceFactor
  }
}
export default getModule(TradeModule)
