import { Page } from '@hbfe/common'
import CheckAccountParam from '@api/service/management/trade/single/checkAccount/query/vo/CheckAccountParam'
import CheckAccountListResponse from '@api/service/management/trade/single/checkAccount/query/vo/CheckAccountListResponse'
import {
  default as MsTradeQueryFrontGatewayCourseLearningBacktage,
  default as MsTradeQuery,
  OrderRequest,
  OrderSortField,
  OrderSortRequest,
  OrderStatisticResponse,
  ReturnOrderRequest,
  ReturnOrderSortField,
  ReturnSortRequest,
  SortPolicy
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import RefundCheckAccountParam from '@api/service/management/trade/single/checkAccount/query/vo/RefundCheckAccountParam'
import RefundCheckAccountListResponse from '@api/service/management/trade/single/checkAccount/query/vo/RefundCheckAccountListResponse'
import QueryCheckAccountBase from '@api/service/management/trade/single/checkAccount/query/vo/QueryCheckAccountBase'
import { statisticOrderInTrainingChannel } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage/graphql-importer'
import { ReturnOrderBasicDataRequest } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRequestVo'
import TradeQueryFrontGatewayTradeQueryBackstage from '@api/diff-gateway/fjzj-trade-query-front-gateway-TradeQueryBackstage'
import QueryCheckAccountInTrainingChannelMain from '@api/service/management/trade/single/checkAccount/query/QueryCheckAccountInTrainingChannel'
import { ReturnOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
export default class QueryCheckAccountInTrainingChannel extends QueryCheckAccountInTrainingChannelMain {
  /**
   * 退款订单分页查询
   * @param page 页数
   * @param queryRefundCheckAccountParam 查询参数
   * @param sort 根据条件进行排序
   * @returns  Array<RefundCheckAccountListResponse>
   */
  async queryOfRefundOrder(
    page: Page,
    queryRefundCheckAccountParam: RefundCheckAccountParam
  ): Promise<Array<RefundCheckAccountListResponse>> {
    const request = RefundCheckAccountParam.refurnTo(queryRefundCheckAccountParam)
    const sort = new ReturnSortRequest()
    sort.field = ReturnOrderSortField.APPLIED_TIME
    sort.policy = SortPolicy.DESC
    const { data } = await TradeQueryFrontGatewayTradeQueryBackstage.pageReturnOrderInTrainingChannel({
      page: page,
      request,
      sort: [sort]
    })
    await this.queryStatisticReturnOrder(request)
    page.totalPageSize = data.totalPageSize
    page.totalSize = data.totalSize
    const refundArr = new Array<RefundCheckAccountListResponse>()
    const userIdList = new Array<string>()
    data.currentPageData.map((item) => {
      const data = RefundCheckAccountListResponse.from(Object.assign(item, new ReturnOrderResponse()))
      userIdList.push(data.userId)
      refundArr.push(data)
    })
    const map = await this.getUserInfo(userIdList)
    refundArr.map((item) => {
      const userInfo = map.get(item.userId)
      if (userInfo) {
        item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
      }
      item.setUserInfo(userInfo.idCard, userInfo.userName, userInfo.phone, userInfo.loginAccount)
    })
    return refundArr
  }
}
