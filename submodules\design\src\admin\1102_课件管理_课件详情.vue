<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">课程管理</el-breadcrumb-item>
      <el-breadcrumb-item>课件详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <el-form ref="form" :model="form" label-width="120px" class="m-text-form">
                <el-col :span="12">
                  <el-form-item label="课件分类：">课件分类名称</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="课件名称：">《审计理论与方法》</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="课件提供商：">福建华博</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="课件类型：">单视频</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="转换状态：">处理成功</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="课件状态：">禁用</el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="课件教师：">郑秀杰</el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="教师简介：">
                    读取教师简介的信息管理学博士，副教授，中国审计学会会员。主讲审计学、审计案例、财务审计、会计学、审计英语、审计软件应用等多门课程。2006年被评为河北经贸大学优秀教师，2003年度和2008年度优秀党员，2009年度校教学优秀奖。承担省级及以上课题多项，在经济管理、管理评论等国家级权威期刊发表论文5篇，在国家核心期刊及省级以上期刊发表论文近30篇，出版学术专著一部。
                  </el-form-item>
                </el-col>
                <el-col :span="24">
                  <el-form-item label="课件简介：">暂无简介</el-form-item>
                </el-col>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <!-- 关联媒体-华为云 -->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">关联媒体</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <div class="f-flex f-align-center">
                <div class="f-flex-sub">媒体源：华为云</div>
              </div>
              <div class="m-file-upload f-mt15">
                <!--视频-->
                <i class="icon el-icon-film"></i>
                <span class="name f-flex-sub">这里是单视频文件</span>
                <span class="time f-plr50">00:02:54</span>
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <el-button type="text" disabled>预览</el-button>
                  <div slot="content">课件转换状态为转换中/转换失败，暂不支持预览</div>
                </el-tooltip>
              </div>
              <div class="m-file-upload f-mt15">
                <!--压缩包-->
                <i class="icon el-icon-files"></i>
                <span class="name f-flex-sub">这里是压缩包文件</span>
                <span class="time f-plr50">00:02:54</span>
                <el-button type="text">预览</el-button>
              </div>
              <div class="m-file-upload f-mt15">
                <!--文档-->
                <i class="icon el-icon-document"></i>
                <span class="name f-flex-sub">这里是文档文件</span>
                <el-button type="text">预览</el-button>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <!-- 关联媒体-外链地址 -->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">关联媒体</span>
        </div>
        <div class="f-p30">
          <el-row type="flex" justify="center" class="width-limit">
            <el-col :md="20" :lg="16" :xl="13">
              <div class="f-flex f-align-center">
                <div class="f-flex-sub">媒体源：外链课件</div>
                <el-button type="text">预览</el-button>
              </div>
              <div class="f-flex f-align-center">
                <div class="f-flex-sub">媒体名称：读取属性值</div>
              </div>
              <div class="m-file-upload f-mt15">
                <!--视频-->
                <i class="icon el-icon-film"></i>
                <span class="name f-mr15">标清</span>
                <span class="f-flex-sub">https/lxxxcom/xxxxx.m3u8</span>
              </div>
              <div class="m-file-upload f-mt15">
                <!--视频-->
                <i class="icon el-icon-film"></i>
                <span class="name f-mr15">高清</span>
                <span class="f-flex-sub">https/lxxxcom/xxxxx.m3u8</span>
              </div>
              <div class="m-file-upload f-mt15">
                <!--视频-->
                <i class="icon el-icon-film"></i>
                <span class="name f-mr15">超清</span>
                <span class="f-flex-sub">https/lxxxcom/xxxxx.m3u8</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">使用该课件的课程清单</span>
        </div>
        <div class="f-p20">
          <el-table stripe :data="tableData" max-height="500px" class="m-table">
            <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
            <el-table-column label="课程名称" min-width="300" fixed="left">
              <template>课程名称课程名称课程名称课程名称</template>
            </el-table-column>
            <el-table-column label="课程分类" min-width="180">
              <template>课程分类课程分类</template>
            </el-table-column>
            <el-table-column label="学时" min-width="180" align="center">
              <template>20.2</template>
            </el-table-column>
            <el-table-column label="创建时间" width="180">
              <template>2020-11-11 12:20:20</template>
            </el-table-column>
          </el-table>
        </div>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        rate: '4.5',
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        checked: true,
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
