import OldQueryTrainClassDetail from '@api/service/customer/train-class/query/QueryTrainClassDetail'

import MsSchemeQueryFrontGatewayCourseLearningForestage, {
  SchemeConfigResponse,
  SchemeSkuPropertyResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import MsTradeQueryFrontGatewayCourseLearningForestage, {
  CommoditySkuForestageResponse,
  CommoditySkuPropertyResponse,
  PortalCommoditySkuPropertyResponse,
  SchemeResourceResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import calculatorObj from '@api/service/customer/train-class/Utils/CalculatorObj'
import SkuPropertyConvertUtils, {
  ComplexSkuPropertyResponse,
  SchemeSkuInfo
} from '@api/service/customer/train-class/Utils/SkuPropertyConvertUtils'
import TrainClassConfigJsonManager from '@api/service/customer/train-class/Utils/TrainClassConfigJsonManager'
import TrainClassDetailClassVo from '@api/service/customer/train-class/query/vo/TrainClassDetailClassVo'
import { Page, ResponseStatus } from '@hbfe/common'
import ThematicMap from '@api/service/customer/thematic-config/ThematicMap'
import { SchemeTypeEnum } from '@api/service/common/enums/train-class/SchemeTypeEnums'
import Classification from '@api/service/customer/train-class/query/vo/Classification'
import QztgTradeQuery, { CommoditySkuResponse } from '@api/diff-gateway/qztg-trade-query-front-gateway-forestage'
import CalculatorObj from '@api/service/common/utils/CalculatorObj'
import PlatformLearningscheme, {
  StudentLearningResourcePushIsCompletedRequest
} from '@api/platform-gateway/platform-learningscheme-v1'
import Scheme from '@api/service/common/scheme/model/schemeDto/Scheme'
import LearningResult from '@api/service/common/scheme/model/schemeDto/common/learning-results/LearningResult'
import LearningType from '@api/service/common/scheme/model/LearningType'
export default class QueryTrainClassDetail extends OldQueryTrainClassDetail {
  /**
   * 合并商品列表
   */
  trainClassDetailClassList = new Array<TrainClassDetailClassVo>()

  /**
   * 后端原方案信息
   */
  schemeRes = new Array<SchemeConfigResponse>()

  /**
   * 是否有合并方案
   */
  get haveMerge() {
    return !!this.trainClassDetailClassList?.length
  }

  /**
   *分类+课程包id  其中第一层是顶级分类，如果第一级无children则代表无分类，类型为Classification[]
   */
  get classification() {
    const arr = new Array<{ schemeName: string; schemeId: string; classification: Classification }>()
    // 主方案
    if (this.trainClassDetail.learningTypeModel.courseLearning.isSelected) {
      arr.push({
        schemeName: this.trainClassDetail.trainClassName,
        schemeId: this.trainClassDetail.schemeId,
        classification: this.trainClassDetail.learningTypeModel.courseLearning.classification
      })
    }
    // 被引用方案
    this.trainClassDetailClassList.forEach((item: TrainClassDetailClassVo) => {
      if (item.learningTypeModel.courseLearning.isSelected) {
        arr.push({
          schemeName: item.trainClassName,
          schemeId: item.schemeId,
          classification: item.learningTypeModel.courseLearning.classification
        })
      }
    })
    return arr
  }

  /**
   * 是否存在已购买商品
   */
  get existBoughtTrainClass() {
    let flag = false
    if (this.trainClassDetail.bought) {
      flag = true

      return flag
    }
    this.trainClassDetailClassList.map((item) => {
      if (item.bought) {
        flag = true
      }
    })

    return flag
  }

  /**
   * 可报名的商品
   */
  get canRegisterTrainClass() {
    const list = new Array<TrainClassDetailClassVo>()
    if (!this.trainClassDetail.bought) {
      list.push(this.trainClassDetail)
    }
    this.trainClassDetailClassList.map((item) => {
      if (!item.bought) {
        list.push(item)
      }
    })

    return list
  }

  /**
   * 可报名商品金额
   */
  get canRegisterTrainClassMount() {
    const canRegisterList = this.canRegisterTrainClass

    let price = 0

    canRegisterList.map((item) => {
      price = CalculatorObj.add(price, item.price)
    })

    return price
  }

  /**
   * 已经报名的商品
   */
  get registeredTrainClass() {
    const list = new Array<TrainClassDetailClassVo>()
    if (this.trainClassDetail.bought) {
      list.push(this.trainClassDetail)
    }
    this.trainClassDetailClassList.map((item) => {
      if (item.bought) {
        list.push(item)
      }
    })

    return list
  }

  /**
   * 总价格
   */
  get totalPrice() {
    let price = 0
    price = CalculatorObj.add(price, this.trainClassDetail.price)

    this.trainClassDetailClassList.map((item) => {
      price = CalculatorObj.add(price, item.price)
    })

    return price
  }

  /**
   * 总学时
   */
  get totalPeriod() {
    let period = 0
    period = CalculatorObj.add(period, this.trainClassDetail.period)

    this.trainClassDetailClassList.map((item) => {
      period = CalculatorObj.add(period, item.period)
    })

    return period
  }

  /**
   * 获取培训班详情 由详情和配置信息组合而成
   */
  async queryTrainClassDetailByMerge(trainingChannelId?: string, isShowAll?: boolean): Promise<ResponseStatus> {
    const commodityId = this.portalCommoditySkuId || this.commodityId
    let responseStatus = new ResponseStatus(200)

    let mergedCommodity = new Array<CommoditySkuResponse>()

    this.trainClassDetailClassList = new Array<TrainClassDetailClassVo>()
    if (trainingChannelId) {
      const res = await QztgTradeQuery.getCommoditySkuTrainingChannelInServicer({
        commoditySkuId: this.commodityId,
        trainingChannelId: trainingChannelId,
        isShowAll: isShowAll
      })
      if (res.status.isSuccess()) {
        const commodityDetail: CommoditySkuForestageResponse = res.data
        if (commodityDetail.commoditySkuId) {
          await this.convertToTrainClassDetailClassVo(commodityDetail)
          mergedCommodity = res.data.mergedCommodities || []
        } else {
          return res.status
        }
      }

      responseStatus = res.status
    } else {
      const res = await QztgTradeQuery.getPortalCommoditySkuCustomerPurchaseInServicer({
        portalCommoditySkuId: commodityId,
        commoditySkuId: this.commodityId,
        isShowAll: isShowAll
      })

      if (res.status.isSuccess()) {
        const commodityDetail: CommoditySkuForestageResponse = res.data.originCommodityInfo
        const trainingDetail: TrainClassDetailClassVo = new TrainClassDetailClassVo()

        if (res.data.portalCommoditySkuForestageResponse) {
          // 商品门户信息---待抽离方法
          trainingDetail.portalCommoditySkuId = res.data.portalCommoditySkuForestageResponse.portalCommoditySkuId
          trainingDetail.portalCommoditySkuSourceType =
            res.data.portalCommoditySkuForestageResponse.portalCommoditySkuSourceType
          trainingDetail.portalCommoditySkuSourceId =
            res.data.portalCommoditySkuForestageResponse.portalCommoditySkuSourceId
          Object.assign(commodityDetail.skuProperty, res.data.portalCommoditySkuForestageResponse.skuProperty)
          if (res.data.portalCommoditySkuForestageResponse.portalCommoditySkuSourceType == 2) {
            await ThematicMap.getThematicMap([res.data.portalCommoditySkuForestageResponse.portalCommoditySkuSourceId])
            trainingDetail.specialType =
              ThematicMap.map.get(res.data.portalCommoditySkuForestageResponse.portalCommoditySkuSourceId)?.type || []
          }
        }
        if (commodityDetail) {
          await this.fillCommoditySkuInTrainingDetail(trainingDetail, commodityDetail)
        }

        this.trainClassDetail = trainingDetail

        mergedCommodity = res.data.mergedCommodities || []
      }

      responseStatus = res.status
    }

    const skuRequest: SchemeSkuInfo[] = []
    const mergedTrainingList: Array<TrainClassDetailClassVo> = new Array<TrainClassDetailClassVo>()

    if (mergedCommodity.length) {
      mergedCommodity.map(async (item) => {
        const mergeSku = Object.assign(new SchemeSkuPropertyResponse(), new PortalCommoditySkuPropertyResponse())
        skuRequest.push(new SchemeSkuInfo(item.schemeId, Object.assign(mergeSku, item.skuProperty)))
        mergedTrainingList.push(new TrainClassDetailClassVo())
      })
    }

    const skuInfos = await SkuPropertyConvertUtils.batchConvertToSkuPropertyResponseVo(skuRequest)

    for (let i = 0; i < mergedCommodity.length; i++) {
      this.transferMergeCommodityToTrainingDetail(mergedTrainingList[i], mergedCommodity[i], skuInfos)
    }

    this.trainClassDetailClassList.push(...mergedTrainingList)

    const schemeIds = this.trainClassDetailClassList.map((item) => item.schemeId)
    schemeIds.push(this.trainClassDetail.schemeId)
    const page = new Page()
    page.pageNo = 1
    page.pageSize = schemeIds.length
    const schemeRes = await MsSchemeQueryFrontGatewayCourseLearningForestage.pageSchemeConfigInServicer({
      page,
      schemeIds
    })
    if (schemeRes?.data?.currentPageData?.length) {
      this.schemeRes = schemeRes.data.currentPageData
      this.trainClassDetailClassList.map((item) => {
        this.fillSchemeInfo(item, schemeRes.data.currentPageData)
      })
      this.fillSchemeInfo(this.trainClassDetail, schemeRes.data.currentPageData)
    }

    // 只有详情口能获取到方案简介 只有主方案需要展示简介 故再掉一次详情口填充主方案简介
    const trainSchemeRes = await MsSchemeQueryFrontGatewayCourseLearningForestage.getSchemeConfigInServicer({
      schemeId: this.trainClassDetail.schemeId
    })
    if (trainSchemeRes?.data) {
      this.trainClassDetail.introContent = trainSchemeRes.data.intro
    } else {
      this.trainClassDetail.introContent = ''
    }

    return responseStatus
  }

  /**
   * 填充sku
   */
  async fillCommoditySkuInTrainingDetail(
    trainDetail: TrainClassDetailClassVo,
    commodityDetail: CommoditySkuForestageResponse
  ) {
    trainDetail.skuProperty = await SkuPropertyConvertUtils.convertToSkuPropertyResponseVo(
      commodityDetail.skuProperty as ComplexSkuPropertyResponse
    )
    trainDetail.sourceId = commodityDetail.possessionInfo.sourceId
    trainDetail.sourceType = commodityDetail.possessionInfo.sourceType
    trainDetail.picturePath = commodityDetail.commodityBasicData.commodityPicturePath
    trainDetail.price = commodityDetail.commodityBasicData.price
    trainDetail.trainClassName = commodityDetail.commodityBasicData.saleTitle
    trainDetail.bought = commodityDetail.possessionInfo.possessing
    trainDetail.subOrderNo = commodityDetail.possessionInfo.sourceId
    trainDetail.schemeId = (commodityDetail.resource as SchemeResourceResponse).schemeId
    trainDetail.commoditySkuId = commodityDetail.commoditySkuId
    trainDetail.isOnShelve = commodityDetail?.onShelve?.shelveStatus === 1
  }

  /**
   * 填充合并商品信息
   * @param trainDetail
   * @param commodityDetail
   * @param skuInfos
   */
  transferMergeCommodityToTrainingDetail(
    trainDetail: TrainClassDetailClassVo,
    commodityDetail: CommoditySkuResponse,
    skuInfos: SchemeSkuInfo[]
  ) {
    const sku = skuInfos.find((it) => it.id === commodityDetail.schemeId)
    trainDetail.skuProperty = sku.skuName
    // todo
    // trainDetail.sourceId = commodityDetail.possessionInfo.sourceId
    // trainDetail.sourceType = commodityDetail.possessionInfo.sourceType
    trainDetail.picturePath = commodityDetail.commodityPicturePath
    trainDetail.price = commodityDetail.price
    trainDetail.trainClassName = commodityDetail.saleTitle
    trainDetail.bought = commodityDetail.possessionInfo?.possessing
    // l.possessionInfo.possessing
    // trainDetail.subOrderNo = commodityDetail.possessionInfo.sourceId
    trainDetail.schemeId = commodityDetail.schemeId
    trainDetail.commoditySkuId = commodityDetail.commoditySkuId
  }

  /**
   * 填充合并商品方案信息
   * @param trainClassDetail
   * @param schemeRes
   */
  fillSchemeInfo(trainClassDetail: TrainClassDetailClassVo, schemeRes: Array<SchemeConfigResponse>) {
    const commodityDetail = schemeRes.find((it) => it.schemeId === trainClassDetail.schemeId)

    if (commodityDetail) {
      const classConfigJson: Scheme = TrainClassConfigJsonManager.calculatorSchemeConfigJson(
        commodityDetail.schemeConfig
      )
      if (classConfigJson) {
        const { schemeAssessItem } = Scheme.parseTrainClassAssess(classConfigJson)
        const creditLearningResult = schemeAssessItem.learningResults.find(
          (learnResult: LearningResult) => learnResult.type == 1
        )
        trainClassDetail.period = creditLearningResult.grade || 0
        trainClassDetail.registerBeginDate = classConfigJson.registerBeginDate
        trainClassDetail.registerEndDate = classConfigJson.registerEndDate
        trainClassDetail.trainingBeginDate = classConfigJson.trainingBeginDate
        trainClassDetail.trainingEndDate = classConfigJson.trainingEndDate
        trainClassDetail.notice = classConfigJson.notice
        trainClassDetail.issueNotice = classConfigJson.issueNotice
        trainClassDetail.introContent = commodityDetail.intro
        const getExtendProperties = (key: string) =>
          classConfigJson.extendProperties.find((item: any) => item.name == key)?.value
        trainClassDetail.showNoticeDialog = getExtendProperties('showNoticeDialog') as boolean
        //获取培训成果中学分

        let courseRequirePeriod = 0
        let courseAssess
        let courseQuizEva
        let courseQuizConfig
        if (classConfigJson.type == 'chooseCourseLearning' && classConfigJson.chooseCourseLearning) {
          trainClassDetail.learningTypeModel.courseLearning.isSelected = true
          trainClassDetail.learningTypeModel.courseLearning.isAssess = true
          trainClassDetail.schemeType = 1
          courseAssess = classConfigJson.chooseCourseLearning.assessSetting
          courseRequirePeriod = calculatorObj.add(
            courseAssess.compulsoryRequirePeriod,
            courseAssess.electiveRequirePeriod
          )
          courseQuizEva = classConfigJson.chooseCourseLearning.config.courseCompleteEvaluateConfig
          courseQuizConfig = classConfigJson.chooseCourseLearning.config.courseQuizConfig
        } else if (classConfigJson.type == 'autonomousCourseLearning' && classConfigJson.autonomousCourseLearning) {
          trainClassDetail.learningTypeModel.courseLearning.isSelected = true
          trainClassDetail.learningTypeModel.courseLearning.isAssess = true
          trainClassDetail.schemeType = 2
          courseAssess = classConfigJson.autonomousCourseLearning.assessSetting
          courseRequirePeriod = courseAssess.requirePeriod
          courseQuizEva = classConfigJson.autonomousCourseLearning.config.courseCompleteEvaluateConfig
          courseQuizConfig = classConfigJson.autonomousCourseLearning.config.courseQuizConfig
        } else {
          trainClassDetail.schemeType =
            SchemeTypeEnum[classConfigJson.type as string] ?? SchemeTypeEnum.chooseCourseLearning
        }
        // 获取心得方案配置
        TrainClassConfigJsonManager.configExperience(this.trainClassDetail.learningTypeModel, classConfigJson)
        if (courseAssess) {
          trainClassDetail.trainClassConfig.courseRequirePeriod = courseRequirePeriod
        }
        if (courseQuizEva && courseQuizConfig) {
          trainClassDetail.trainClassConfig.incorporateCourseQuiz = courseQuizEva.courseQuizPagerStandard
          trainClassDetail.trainClassConfig.eachCourseQuizPassScore = courseQuizConfig.quizConfig.passScore
        }
        if (classConfigJson.examLearning) {
          trainClassDetail.learningTypeModel.exam.isSelected = true
          trainClassDetail.learningTypeModel.exam.isAssess = true
          trainClassDetail.trainClassConfig.examPassScore = classConfigJson.examLearning.config.qualifiedScore
          if (classConfigJson.examLearning.config.gradesWhetherHide === undefined) {
            trainClassDetail.learningTypeModel.exam.gradesWhetherHide = true
          } else {
            trainClassDetail.learningTypeModel.exam.gradesWhetherHide =
              classConfigJson.examLearning.config.gradesWhetherHide
          }
          trainClassDetail.learningTypeModel.exam.isExamAssessed = trainClassDetail.trainClassConfig.isExamAssessed =
            classConfigJson.examLearning.assessSetting && classConfigJson.examLearning.assessSetting.operation != 3
          trainClassDetail.learningTypeModel.exam.preCondition =
            classConfigJson.examLearning.precondition && classConfigJson.examLearning.precondition.operation != 3
              ? 1
              : 0
        }
        TrainClassConfigJsonManager.configCourseLearning(trainClassDetail.learningTypeModel, classConfigJson)
        const { issue, questionnaire } = LearningType.configIssueAndQuestionnaire(classConfigJson)
        trainClassDetail.learningTypeModel.issue = issue
        trainClassDetail.learningTypeModel.questionnaire = questionnaire
      }
    }
  }

  /**
   * 校验班级是否开通完成（true 完成， false 开通中）
   * @param qualificationId 参训资格id
   */
  async checkTrainClassIsOpened(qualificationId: string) {
    const request = new StudentLearningResourcePushIsCompletedRequest()
    request.qualificationId = qualificationId
    const res = await PlatformLearningscheme.studentLearningResourcePushIsCompleted(request)

    return !!res?.data
  }
}
