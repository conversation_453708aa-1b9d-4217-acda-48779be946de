import CheckType, { CheckTypeEnum } from '@api/service/management/train-class/query/enum/CheckTypeEnum'
import ExperienceRecordItem from '@api/service/management/train-class/query/vo/ExperienceRecordItem'
import CourseLearningBackstage, {
  LearningExperienceTopicRequest1,
  LearningExperienceTopicResponse,
  StudentLearningExperienceLastedResponse,
  StudentLearningExperienceRequest,
  StudentLearningExperienceResponse,
  StudentLearningExperienceStatus,
  StudentSchemeLearningRequest
} from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
import { Page } from '@hbfe/common'

/**
 * 学员学习心得模型
 */
class ExperienceItem {
  /**
   * 心得id
   */
  id = ''
  /**
   * 主题
   */
  theme = ''
  /**
   * 是否必选
   */
  isRequired: boolean = null
  /**
   * 参加时间开始
   */
  joinStartTime = ''
  /**
   * 参加时间结束
   */
  joinEndTime = ''
  /**
   * 审核方式
   */
  checkType: CheckType = null
  /**
   * 剩余提交次数
   */
  remainderCount = ''

  /**
   * 提交记录列表
   */
  recordList: Array<ExperienceRecordItem> = []
  /**
   * 是否加载
   */
  isLoad = false

  /**
   * 学号
   */
  studentNo = ''
  /**
   * 填充
   */
  static from(dto: StudentLearningExperienceLastedResponse, itemConfigs: LearningExperienceTopicResponse[]) {
    const vo = new ExperienceItem()
    const itemConfig = itemConfigs.find(
      item => item.topicId === dto.studentLearningExperience.learningExperienceTopic.topicId
    )
    vo.studentNo = dto.studentLearningExperience.studentLearning.studentNo
    vo.id = dto.studentLearningExperience.learningExperienceTopic.topicId
    vo.theme = dto.studentLearningExperience.learningExperienceTopic.experienceTopicName
    vo.isRequired = itemConfig.isRequired
    vo.joinStartTime = dto.studentLearningExperience.learningExperienceTopic.startTime
    vo.joinEndTime = dto.studentLearningExperience.learningExperienceTopic.endTime
    vo.checkType = new CheckType(
      (dto.studentLearningExperience.learningExperienceTopic.auditType as unknown) as CheckTypeEnum
    )
    vo.remainderCount =
      itemConfig.submitLimitCount == -1 ? '无限' : (itemConfig.submitLimitCount - dto.count).toString()
    return vo
  }

  /**
   * 查心得记录
   * @returns {Promise<void>}
   */
  async queryRecord() {
    if (this.isLoad) {
      return
    }
    const response = [] as StudentLearningExperienceResponse[]
    const page = new Page(1, 200)
    const request = new StudentLearningExperienceRequest()
    request.studentLearning = new StudentSchemeLearningRequest()
    request.learningExperienceTopic = new LearningExperienceTopicRequest1()
    request.studentLearning.studentNos = [this.studentNo]
    request.learningExperienceTopic.topicIds = [this.id]
    request.status = [
      StudentLearningExperienceStatus.SUBMITTED,
      StudentLearningExperienceStatus.PASS,
      StudentLearningExperienceStatus.RETURNED
    ]
    const res = await CourseLearningBackstage.pageLearningExperienceInServicer({ page, request })
    if (!res.status.isSuccess()) return
    response.push(...res.data.currentPageData)
    if (res.data.totalPageSize > 1) {
      const num = Array.from({ length: res.data.totalPageSize - 1 }, (v, k) => k + 2)
      await Promise.all(
        num.map(async ite => {
          const red = await CourseLearningBackstage.pageLearningExperienceInServicer({
            page: { pageNo: ite, pageSize: 200 },
            request
          })
          return red
        })
      ).then(res => {
        res.map(re => {
          response.push(...re.data.currentPageData)
        })
      })
    }
    this.recordList.push(...response.map(ExperienceRecordItem.from))
    this.isLoad = true
  }
}

export default ExperienceItem
