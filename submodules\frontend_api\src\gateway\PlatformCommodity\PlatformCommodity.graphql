schema {
	query:Query
}
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
"""Long type"""
scalar Long
"""Built-in Short as Int"""
scalar Short
"""Built-in Byte as Int"""
scalar Byte
"""Built-in java.math.BigInteger"""
scalar BigInteger
"""Built-in java.math.BigDecimal"""
scalar BigDecimal
"""Built-in Char as Character"""
scalar Char
directive @NotAuthenticationRequired on FIELD_DEFINITION | INPUT_FIELD_DEFINITION | ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""@Description 渠道商推广交易成功的学员报名次数(机构授权给渠道商推广的的商品开通数)
		<AUTHOR>
		@Date 8:59 2021/10/22
		@param: paramDTO
	"""
	countCommodityChannelVendorOpenNumber(paramDTO:ChannelVendorCommodityOpenNumberQueryParamDTO):Long! @NotAuthenticationRequired
	"""获取班级总数
		@param paramDTO 商品查询参数
		@return
	"""
	countCommodityNumber(paramDTO:LazyCommodityQueryParamDTO):Int! @NotAuthenticationRequired
	"""功能描述：渠道供应商：获取当前渠道供应商班级总数
		@param paramDTO : 商品查询参数
		@return : int
		@Author： wtl
		@Date： 2021/7/20 11:42
	"""
	countCommodityNumberForChannelVendor(paramDTO:LazyCommodityQueryParamDTO):Int!
	"""功能描述：课件供应商：获取当前课件供应商班级总数
		@param paramDTO : 商品查询参数
		@return : int
		@Author： wtl
		@Date： 2021/7/20 11:42
	"""
	countCommodityNumberForCoursewareSupplier(paramDTO:LazyCommodityQueryParamDTO):Int!
	"""功能描述：机构：获取当前机构班级总数
		@param paramDTO : 商品查询参数
		@return : int
		@Author： wtl
		@Date： 2021/7/20 11:42
	"""
	countCommodityNumberForTrainingInstitution(paramDTO:LazyCommodityQueryParamDTO):Int!
	"""获取当前已开班的工种数
		@param commodityState 指定一个商品状态，如果没指定则返回所有
		@return
	"""
	countWorkTypeHasCommodityNumber(commodityState:String):Int! @NotAuthenticationRequired
	"""获取某个商品
		@param commodityId
		@return
	"""
	getLazyCommodityById(commodityId:String):LazyCommodityDTO @NotAuthenticationRequired
	"""获取所有被使用的sku
		@return
	"""
	getSkuWhichUsedByCommodity(name:String):SkuUsedByCommodityDTO @NotAuthenticationRequired
	"""加了机构和渠道商参数的 获取被使用的sku
		@return
	"""
	getSkuWhichUsedByCommodityQuery(paramDTO:SkuUsedQueryParamDTO):SkuUsedByCommodityDTO @NotAuthenticationRequired
	"""查询带延迟的商品集合（带评价），以评价维度展开去查商品
		@param paramDTO
		@return
	"""
	listCommodityWithAppraise(paramDTO:LazyCommodityWithAppraiseQueryParamDTO):[LazyCommodityWithAppraiseDTO] @NotAuthenticationRequired
	"""批量获取期数商品
		@param issueIds
		@return
	"""
	listIssueCommodityInfo(issueIds:[String]):[LazyCommodityDTO]
	"""@Description  根据工种id集合查询每个渠道工种报名数
		<AUTHOR>
		@Date 10:50 2021/10/22
		@param: paramDTO
	"""
	listWorkTypeCommodityChannelVendorOpenNumber(paramDTO:WorkTypeCommodityOpenNumberQueryParamDTO):[ChannelVendorWorkTypeCommodityOpenNumberDTO] @NotAuthenticationRequired
	"""根据工种id集合查询每个工种报名数
		@param paramDTO
		@return
	"""
	listWorkTypeCommodityOpenNumber(paramDTO:WorkTypeCommodityOpenNumberQueryParamDTO):[WorkTypeCommodityOpenNumberDTO] @NotAuthenticationRequired
	"""查询带延迟的商品集合（带评价），以评价维度展开去查商品
		@param paramDTO
		@return
	"""
	pageCommodityWithAppraise(page:Page,paramDTO:LazyCommodityWithAppraiseQueryParamDTO):LazyCommodityWithAppraiseDTOPage @page(for:"LazyCommodityWithAppraiseDTO") @NotAuthenticationRequired
	"""获取清洗商品表分页
		@param page
		@param paramDTO
		@return
	"""
	pageLazyCommodity(page:Page,paramDTO:LazyCommodityQueryParamDTO):LazyCommodityDTOPage @page(for:"LazyCommodityDTO") @NotAuthenticationRequired
	"""渠道商：获取清洗商品表分页
		@param page
		@param paramDTO
		@return
	"""
	pageLazyCommodityForChannelVendor(page:Page,paramDTO:LazyCommodityQueryParamDTO):LazyCommodityDTOPage @page(for:"LazyCommodityDTO")
	"""课件供应商：获取清洗商品表分页
		@param page
		@param paramDTO
		@return
	"""
	pageLazyCommodityForCoursewareSupplier(page:Page,paramDTO:LazyCommodityQueryParamDTO):LazyCommodityDTOPage @page(for:"LazyCommodityDTO")
	"""参训单位：获取清洗商品表分页
		@param page
		@param paramDTO
		@return
	"""
	pageLazyCommodityForParticipatingUnit(page:Page,paramDTO:LazyCommodityQueryParamDTO):LazyCommodityDTOPage @page(for:"LazyCommodityDTO")
	"""机构：获取清洗商品表分页
		@param page
		@param paramDTO
		@return
	"""
	pageLazyCommodityForTrainingInstitution(page:Page,paramDTO:LazyCommodityQueryParamDTO):LazyCommodityDTOPage @page(for:"LazyCommodityDTO") @NotAuthenticationRequired
}
"""@Description  渠道商开通数
	<AUTHOR>
	@Date 9:23 2021/10/22
"""
input ChannelVendorCommodityOpenNumberQueryParamDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.param.ChannelVendorCommodityOpenNumberQueryParamDTO") {
	"""商品状态："UPED"上架|"DOWNED"下架"""
	commodityState:String
	"""机构ID"""
	trainingInstitutionIdList:[String]
	"""渠道商id"""
	channelVendorIds:[String]
}
"""商品中间表查询条件
	Author:FangKunSen
	Time:2021-02-02,15:19
"""
input LazyCommodityQueryParamDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.param.LazyCommodityQueryParamDTO") {
	"""查询条件并行策略
		AND: 与| OR: 或
		默认AND
		全平台搜索时请设置OR策略并将所需要覆盖的条件一起赋值
	"""
	policy:QueryParamOperatePolicyEnum
	"""商品名称"""
	commodityName:String
	"""商品id"""
	commodityIds:[String]
	"""不包括哪些商品"""
	excludeCommodityIds:[String]
	"""方案名称"""
	schemeName:String
	"""方案id"""
	schemeId:String
	"""方案id集合"""
	schemeIds:[String]
	"""期数名称"""
	issueName:String
	"""期数id"""
	issueId:String
	"""工种名称"""
	workTypeName:String
	"""工种id"""
	workTypeId:String
	"""工种id集合，当workTypeId有值时，该批量查询无效"""
	workTypeIds:[String]
	"""机构ID"""
	trainingInstitutionIdList:[String]
	"""机构名称"""
	trainingInstitutionName:String
	"""课件供应商id"""
	coursewareSupplierIdList:[String]
	"""渠道供应商id集合
		add by wtl 2021年7月21日 09:19:42
	"""
	channelVendorIdList:[String]
	"""参训单位id集合
		add by sugs 2021年10月11日 15:53:12
	"""
	participatingUnitIdList:[String]
	"""培训类别id(右like查询该节点及该节点以下的所有子节点)"""
	trainingCategoryId:String
	"""工种类别与工种完整路径（用于查询）"""
	categoryWorkTypePath:[String]
	"""适用人群名"""
	suitableCrowNames:[String]
	"""报名人数排序，如果没填则默认根据班级创建时间排序（优先于价格排序）"""
	trainingUserNumberSortPolicy:QueryParamSortEnum
	"""价格排序，如果没填则默认根据班级创建时间排序"""
	priceSortPolicy:QueryParamSortEnum
	"""渠道商报名人数排序，如果没填则默认根据班级创建时间排序（排序等级最高）"""
	channelUserNumberSortPolicy:QueryParamSortEnum
	"""课程评价排序"""
	appraiseSortPolicy:QueryParamSortEnum
	"""渠道商推广班级添加时间排序"""
	channelAddTimeSortPolicy:QueryParamSortEnum
	"""下架时间排序"""
	offShelveTimeSortPolicy:QueryParamSortEnum
	"""发布时间 起"""
	publishTimeStart:DateTime
	"""发布时间 止"""
	publishTimeEnd:DateTime
	"""商品状态："UPED"上架|"DOWNED"下架"""
	commodityState:String
	"""商品是否开启web渠道(即是否开放报名)"""
	allowWebChannel:Boolean
	"""商品是否有效"""
	commodityAvailable:Boolean
	"""开通数大于等于"""
	openNumberStart:Int
	"""开通数小于等于"""
	openNumberEnd:Int
	"""是否是推广班级"""
	spread:Boolean
	"""@Description 渠道供应商id集合用于商品清洗查询， 因为旧的 channelVendorIdList不用于商品清洗，怕其他地方有影响这里重新定义一个
		<AUTHOR>
		@Date 16:44 2021/10/21
	"""
	channelVendorIds:[String]
	"""商品所属机构状态
		true 启用
		false 禁用
		空 忽略
	"""
	trainingInstitutionStatus:Boolean
	"""排除已授权给渠道商的商品"""
	excludeChannelVendorIds:[String]
}
"""商品带评价的查询条件
	Author:FangKunSen
	Time:2021-05-19,17:25
"""
input LazyCommodityWithAppraiseQueryParamDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.param.LazyCommodityWithAppraiseQueryParamDTO") {
	"""限制取几条数据"""
	limit:Int
	"""综合评分排序（优先于开通数排序）
		默认不排序
	"""
	comprehensiveAppraiseSoft:QueryParamSortEnum
	"""0分数据是否当作10分来排序处理"""
	zeroIsTop:Boolean
	"""查询条件并行策略
		AND: 与| OR: 或
		默认AND
		全平台搜索时请设置OR策略并将所需要覆盖的条件一起赋值
	"""
	policy:QueryParamOperatePolicyEnum
	"""商品名称"""
	commodityName:String
	"""商品id"""
	commodityIds:[String]
	"""不包括哪些商品"""
	excludeCommodityIds:[String]
	"""方案名称"""
	schemeName:String
	"""方案id"""
	schemeId:String
	"""方案id集合"""
	schemeIds:[String]
	"""期数名称"""
	issueName:String
	"""期数id"""
	issueId:String
	"""工种名称"""
	workTypeName:String
	"""工种id"""
	workTypeId:String
	"""工种id集合，当workTypeId有值时，该批量查询无效"""
	workTypeIds:[String]
	"""机构ID"""
	trainingInstitutionIdList:[String]
	"""机构名称"""
	trainingInstitutionName:String
	"""课件供应商id"""
	coursewareSupplierIdList:[String]
	"""渠道供应商id集合
		add by wtl 2021年7月21日 09:19:42
	"""
	channelVendorIdList:[String]
	"""参训单位id集合
		add by sugs 2021年10月11日 15:53:12
	"""
	participatingUnitIdList:[String]
	"""培训类别id(右like查询该节点及该节点以下的所有子节点)"""
	trainingCategoryId:String
	"""工种类别与工种完整路径（用于查询）"""
	categoryWorkTypePath:[String]
	"""适用人群名"""
	suitableCrowNames:[String]
	"""报名人数排序，如果没填则默认根据班级创建时间排序（优先于价格排序）"""
	trainingUserNumberSortPolicy:QueryParamSortEnum
	"""价格排序，如果没填则默认根据班级创建时间排序"""
	priceSortPolicy:QueryParamSortEnum
	"""渠道商报名人数排序，如果没填则默认根据班级创建时间排序（排序等级最高）"""
	channelUserNumberSortPolicy:QueryParamSortEnum
	"""课程评价排序"""
	appraiseSortPolicy:QueryParamSortEnum
	"""渠道商推广班级添加时间排序"""
	channelAddTimeSortPolicy:QueryParamSortEnum
	"""下架时间排序"""
	offShelveTimeSortPolicy:QueryParamSortEnum
	"""发布时间 起"""
	publishTimeStart:DateTime
	"""发布时间 止"""
	publishTimeEnd:DateTime
	"""商品状态："UPED"上架|"DOWNED"下架"""
	commodityState:String
	"""商品是否开启web渠道(即是否开放报名)"""
	allowWebChannel:Boolean
	"""商品是否有效"""
	commodityAvailable:Boolean
	"""开通数大于等于"""
	openNumberStart:Int
	"""开通数小于等于"""
	openNumberEnd:Int
	"""是否是推广班级"""
	spread:Boolean
	"""@Description 渠道供应商id集合用于商品清洗查询， 因为旧的 channelVendorIdList不用于商品清洗，怕其他地方有影响这里重新定义一个
		<AUTHOR>
		@Date 16:44 2021/10/21
	"""
	channelVendorIds:[String]
	"""商品所属机构状态
		true 启用
		false 禁用
		空 忽略
	"""
	trainingInstitutionStatus:Boolean
	"""排除已授权给渠道商的商品"""
	excludeChannelVendorIds:[String]
}
"""@Description  sku查询参数
	<AUTHOR>
	@Date 9:23 2021/10/22
"""
input SkuUsedQueryParamDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.param.SkuUsedQueryParamDTO") {
	"""商品名 或工种名"""
	name:String
	"""机构ID"""
	trainingInstitutionIdList:[String]
	"""渠道商id"""
	channelVendorIds:[String]
}
"""Author:FangKunSen
	Time:2021-02-02,19:32
"""
input WorkTypeCommodityOpenNumberQueryParamDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.param.WorkTypeCommodityOpenNumberQueryParamDTO") {
	"""商品状态："UPED"上架|"DOWNED"下架"""
	commodityState:String
	"""培训类别id(右like查询该节点及该节点以下的所有子节点)"""
	trainingCategoryId:String
	"""工种id集合"""
	workTypeIdList:[String]
	"""机构ID"""
	trainingInstitutionIdList:[String]
	"""需要取得几个工种，不传则返回全部自己取"""
	workTypeResponseNumber:Int
	"""开通人数排序策略，默认不排序"""
	queryParamSortEnum:QueryParamSortEnum
	"""渠道商id"""
	channelVendorIds:[String]
	"""商品所属机构状态
		true 启用
		false 禁用
		空 忽略
	"""
	trainingInstitutionStatus:Boolean
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""渠道每个工种的商品购买数
	Author:FangKunSen
	Time:2021-02-02,19:29
"""
type ChannelVendorWorkTypeCommodityOpenNumberDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.ChannelVendorWorkTypeCommodityOpenNumberDTO") {
	"""工种id"""
	workTypeId:String
	"""渠道商id"""
	channelVendorId:String
	"""开通人数"""
	openNumber:Int
}
"""Author:FangKunSen
	Time:2021-02-02,19:06
"""
type LazyCommodityDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.LazyCommodityDTO") {
	"""商品id"""
	commodityId:String
	"""商品名"""
	commoditySaleTitle:String
	"""方案id"""
	schemeId:String
	"""方案名"""
	schemeName:String
	"""期别id"""
	stageId:String
	"""期别名"""
	stageName:String
	"""期数id"""
	issueId:String
	"""期数名"""
	issueName:String
	"""方案封面图路径"""
	schemePicturePath:String
	"""商品销售卖点"""
	commoditySellingPoint:String
	"""商品描述介绍"""
	commodityDescription:String
	"""商品描述介绍小程序"""
	commodityDescriptionUniApp:String
	"""商品是否开启web渠道(即是否开放报名)"""
	allowWebChannel:Boolean
	"""商品是否开启导入开通渠道"""
	allowImportChannel:Boolean
	"""商品是否开启集体缴费渠道"""
	allowBatchChannel:Boolean
	"""机构ID"""
	trainingInstitutionId:String
	"""机构名称"""
	trainingInstitutionName:String
	"""机构简介"""
	trainingInstitutionAbouts:String
	"""课件供应商id"""
	coursewareSupplierId:String
	"""课件供应商名称"""
	coursewareSupplierName:String
	"""培训类别id(路径)"""
	trainingCategoryId:String
	"""培训类别名称"""
	trainingCategoryName:String
	"""培训类别 名称路径"""
	trainingCategoryNamePath:String
	"""工种id"""
	workTypeId:String
	"""工种name"""
	workTypeName:String
	"""适用人群名"""
	suitableCrowNames:[String]
	"""学时"""
	period:Double!
	"""价格"""
	price:Double!
	"""商品状态："UPED"上架|"DOWNED"下架"""
	commodityState:String
	"""商品是否有效"""
	commodityAvailable:Boolean!
	"""培训时间 起"""
	trainingTimeStart:DateTime
	"""培训时间 止"""
	trainingTimeEnd:DateTime
	"""开通人数"""
	openNumber:Long!
	"""创建者id"""
	creatorId:String
	"""创建者名"""
	creatorName:String
	"""上架时间"""
	onShelveTime:DateTime
	"""下架时间"""
	offShelveTime:DateTime
	"""发布时间"""
	publishTime:DateTime
	"""最后修改时间"""
	lastUpdateTime:DateTime
	"""价格变更记录"""
	priceChangeHistoryDTOS:[PriceChangeHistoryDTO]
	"""渠道商"""
	channelVendorList:[ServicerDTO]
	"""商品总评价"""
	evaluation:Double!
}
"""商品带评价
	Author:FangKunSen
	Time:2021-05-19,17:37
"""
type LazyCommodityWithAppraiseDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.LazyCommodityWithAppraiseDTO") {
	"""综合评价"""
	comprehensiveAppraise:Double
	"""商品id"""
	commodityId:String
	"""商品名"""
	commoditySaleTitle:String
	"""方案id"""
	schemeId:String
	"""方案名"""
	schemeName:String
	"""期别id"""
	stageId:String
	"""期别名"""
	stageName:String
	"""期数id"""
	issueId:String
	"""期数名"""
	issueName:String
	"""方案封面图路径"""
	schemePicturePath:String
	"""商品销售卖点"""
	commoditySellingPoint:String
	"""商品描述介绍"""
	commodityDescription:String
	"""商品描述介绍小程序"""
	commodityDescriptionUniApp:String
	"""商品是否开启web渠道(即是否开放报名)"""
	allowWebChannel:Boolean
	"""商品是否开启导入开通渠道"""
	allowImportChannel:Boolean
	"""商品是否开启集体缴费渠道"""
	allowBatchChannel:Boolean
	"""机构ID"""
	trainingInstitutionId:String
	"""机构名称"""
	trainingInstitutionName:String
	"""机构简介"""
	trainingInstitutionAbouts:String
	"""课件供应商id"""
	coursewareSupplierId:String
	"""课件供应商名称"""
	coursewareSupplierName:String
	"""培训类别id(路径)"""
	trainingCategoryId:String
	"""培训类别名称"""
	trainingCategoryName:String
	"""培训类别 名称路径"""
	trainingCategoryNamePath:String
	"""工种id"""
	workTypeId:String
	"""工种name"""
	workTypeName:String
	"""适用人群名"""
	suitableCrowNames:[String]
	"""学时"""
	period:Double!
	"""价格"""
	price:Double!
	"""商品状态："UPED"上架|"DOWNED"下架"""
	commodityState:String
	"""商品是否有效"""
	commodityAvailable:Boolean!
	"""培训时间 起"""
	trainingTimeStart:DateTime
	"""培训时间 止"""
	trainingTimeEnd:DateTime
	"""开通人数"""
	openNumber:Long!
	"""创建者id"""
	creatorId:String
	"""创建者名"""
	creatorName:String
	"""上架时间"""
	onShelveTime:DateTime
	"""下架时间"""
	offShelveTime:DateTime
	"""发布时间"""
	publishTime:DateTime
	"""最后修改时间"""
	lastUpdateTime:DateTime
	"""价格变更记录"""
	priceChangeHistoryDTOS:[PriceChangeHistoryDTO]
	"""渠道商"""
	channelVendorList:[ServicerDTO]
	"""商品总评价"""
	evaluation:Double!
}
"""Author:FangKunSen
	Time:2021-02-02,19:15
"""
type PriceChangeHistoryDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.PriceChangeHistoryDTO") {
	"""变更时间"""
	changeTime:DateTime
	"""操作者"""
	operatorId:String
	"""操作者名字"""
	operatorName:String
	"""原价格"""
	oldPrice:Double!
	"""新价格"""
	newPrice:Double!
}
"""@Description 服务商
	<AUTHOR>
	@Date 15:30 2021/10/21
"""
type ServicerDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.ServicerDTO") {
	"""服务商id"""
	servicerId:String
	"""服务商名称"""
	servicerName:String
}
"""已被引用的sku属性
	Author:FangKunSen
	Time:2021-03-09,08:39
"""
type SkuUsedByCommodityDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.SkuUsedByCommodityDTO") {
	"""已被引用的工种类别与工种树"""
	workTypeCategoryWhichUsedList:[WorkTypeCategoryWhichUsed]
	"""已有发布商品的机构"""
	trainingInstitutionList:[TrainingInstitutionBaseInfoDTO]
	"""已被引用的适用人群列表"""
	suitablePeopleList:[String]
}
"""机构基础信息
	Author:FangKunSen
	Time:2021-03-08,19:53
"""
type TrainingInstitutionBaseInfoDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.TrainingInstitutionBaseInfoDTO") {
	"""机构id"""
	id:String
	"""机构名字"""
	name:String
	"""首字母(给前端用，后端暂时不设置)"""
	letter:String
}
"""工种基础信息
	Author:FangKunSen
	Time:2021-03-08,16:11
"""
type WorkTypeBaseInfo @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.WorkTypeBaseInfo") {
	"""id"""
	id:String
	"""名字"""
	name:String
}
"""已被（商品）使用的工种类别树,包含工种信息
	目前如果有子类别（hasChild=true）则不会挂工种，反之亦如此
	Author:FangKunSen
	Time:2021-03-08,16:05
"""
type WorkTypeCategoryWhichUsed @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.WorkTypeCategoryWhichUsed") {
	"""工种类别id"""
	id:String
	"""工种类别name"""
	name:String
	"""类别父id"""
	parentId:String
	"""是否有子类别（是否没有挂工种）"""
	hasChild:Boolean!
	"""子类别"""
	children:[WorkTypeCategoryWhichUsed]
	"""所挂工种基础信息"""
	workTypeList:[WorkTypeBaseInfo]
	"""排序值"""
	sort:Int!
}
"""每个工种的商品购买数
	Author:FangKunSen
	Time:2021-02-02,19:29
"""
type WorkTypeCommodityOpenNumberDTO @type(value:"com.fjhb.btpx.integrative.service.commodity.dto.response.WorkTypeCommodityOpenNumberDTO") {
	"""工种id"""
	workTypeId:String
	"""开通人数"""
	openNumber:Int
}
"""Author:FangKunSen
	Time:2021-02-02,15:57
"""
enum QueryParamOperatePolicyEnum @type(value:"com.fjhb.btpx.integrative.service.utils.dto.QueryParamOperatePolicyEnum") {
	"""and查询"""
	AND
	"""or查询"""
	OR
}
"""Author:FangKunSen
	Time:2020-10-27,15:55
"""
enum QueryParamSortEnum @type(value:"com.fjhb.btpx.integrative.service.utils.dto.QueryParamSortEnum") {
	"""升序"""
	ASC
	"""降序"""
	DESC
}

scalar List
type LazyCommodityWithAppraiseDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LazyCommodityWithAppraiseDTO]}
type LazyCommodityDTOPage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [LazyCommodityDTO]}
