<template>
  <el-card shadow="never" class="m-card is-header f-mb15">
    <div class="f-plr20 f-pt20">
      <div class="f-pt5 f-mb20"><span class="f-mr10">练习来源：</span>{{ getPracticeSource() }}</div>
      <el-table
        stripe
        :data="questionLibraryList"
        max-height="500px"
        class="m-table"
        v-if="practiceLearningInfo.type == 1"
      >
        <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
        <el-table-column label="题库名称" min-width="240" fixed="left">
          <template slot-scope="scope">{{ scope.row.name }}</template>
        </el-table-column>
        <el-table-column label="已启用的试题数量" min-width="190" align="center">
          <template slot-scope="scope">{{ scope.row.enabledQuestionCount }}</template>
        </el-table-column>
      </el-table>
      <el-form ref="form" label-width="auto" class="is-column f-mt30">
        <el-form-item label="前置条件：" style="margin: auto">无</el-form-item>
        <el-form-item label="培训要求：" style="margin: auto">无</el-form-item>
      </el-form>
    </div>
  </el-card>
</template>

<script lang="ts">
  import { Component, Vue, PropSync } from 'vue-property-decorator'
  import PracticeLearningType from '@api/service/management/train-class/mutation/vo/PracticeLearningType'
  import LibraryResponseVo from '@api/service/management/resource/question-library/query/vo/LibraryResponseVo'
  import ResourceModule from '@api/service/management/resource/ResourceModule'

  @Component
  export default class extends Vue {
    tableData = [{}]
    form = {}

    @PropSync('practiceLearning', { type: PracticeLearningType }) practiceLearningInfo!: PracticeLearningType

    questionLibraryBusinessRemote = ResourceModule.queryQuestionLibraryFactory.queryQuestionLibraryMultiton

    getPracticeSource() {
      return this.practiceLearningInfo.type == 1
        ? '题库'
        : this.practiceLearningInfo.type == 2
        ? '按照学员课程ID出题'
        : this.practiceLearningInfo.type == 3
        ? '同考试'
        : ''
    }

    questionLibraryList: Array<LibraryResponseVo> = new Array<LibraryResponseVo>()

    async created() {
      if (this.practiceLearningInfo.libraryIds && this.practiceLearningInfo.libraryIds.length) {
        await this.getQuestionLibraryList()
      }
    }

    async getQuestionLibraryList() {
      const questionLibraryList: Array<LibraryResponseVo> = await Promise.all(
        this.practiceLearningInfo.libraryIds.map(async (item) => {
          const response = await this.questionLibraryBusinessRemote.queryQuestionBankLibraryDetail(item)
          const question = response?.data || new LibraryResponseVo()
          return question
        })
      )
      this.questionLibraryList = questionLibraryList || ([] as LibraryResponseVo[])
    }
  }
</script>
