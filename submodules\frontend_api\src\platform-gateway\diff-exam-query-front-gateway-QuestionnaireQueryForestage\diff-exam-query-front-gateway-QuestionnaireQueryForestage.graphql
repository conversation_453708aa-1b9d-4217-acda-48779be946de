schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询各班级下学习的课程教师集合包含期别中的教师集合和线上课程的教师集合
		@param request
		@return
	"""
	getCourseTeachersWithPeriodAndOnlineInMyself(request:QuestionnaireAnswerTeachersRequest):QuestionnaireAnswerAllTeacherResponse
}
"""@Description 管理端统计报表查询班级下课程教师集合条件
	@Date 2025/4/25
"""
input QuestionnaireAnswerTeachersRequest @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.request.export.question.QuestionnaireAnswerTeachersRequest") {
	"""方案id"""
	schemeId:String
	"""课程id"""
	courseId:String
	"""期别id"""
	issueId:String
}
type OwnerInfo @type(value:"com.fjhb.ms.course.learning.front.gateway.jxjy.gateway.graphql.response.nested.common.OwnerInfo") {
	platformId:String
	platformVersionId:String
	projectId:String
	subProjectId:String
	unitId:String
	servicerType:Int
	servicerId:String
}
"""@Description
	@Date 2025/4/25
"""
type QuestionnaireAnswerAllTeacherResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.question.QuestionnaireAnswerAllTeacherResponse") {
	"""线上教师集合"""
	onlineTeachers:[QuestionnaireAnswerTeacherResponse]
	"""线上教师集合"""
	offlineTeachers:[QuestionnaireAnswerTeacherResponse]
}
"""@description: 教师信息
	@author: sugs
	@create: 2022-03-10 10:04
"""
type QuestionnaireAnswerTeacherResponse @type(value:"com.fjhb.platform.jxjy.v1.gateway.graphql.response.question.QuestionnaireAnswerTeacherResponse") {
	"""教师id"""
	id:String
	"""数据归属信息"""
	owner:OwnerInfo
	"""教师名称"""
	name:String
	"""头像/照片"""
	photo:String
	"""简介内容"""
	aboutsContent:String
	"""性别 -1:未知 0:女 1:男
		@see TeacherGenders
	"""
	gender:Int!
	"""创建时间"""
	createTime:DateTime
	"""创建人id"""
	createUserId:String
}

scalar List
