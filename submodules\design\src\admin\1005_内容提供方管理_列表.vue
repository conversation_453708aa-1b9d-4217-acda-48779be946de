<template>
  <el-main>
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">课件供应商管理</el-breadcrumb-item>
      <el-breadcrumb-item>内容提供方管理</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <div class="f-mb15">
        <el-button type="primary" icon="el-icon-plus">新建内容提供方</el-button>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column label="排序" min-width="70" align="center">
            <template slot="header">
              排序
              <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                <div slot="content">
                  排序说明：选课页面仅展示已启用的内容提供方筛选项，按照列表内容正序展示，如需调整顺序请进行拖拽排序。
                </div>
              </el-tooltip>
            </template>
            <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
          </el-table-column>
          <el-table-column label="内容提供方" min-width="400">
            <template>内容提供方</template>
          </el-table-column>
          <el-table-column label="状态" min-width="100">
            <template>启用</template>
          </el-table-column>
          <el-table-column label="操作" width="300" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">详情</el-button>
              <el-button type="text" size="mini">编辑</el-button>
              <el-button type="text" size="mini">禁用</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        input1: '1',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        tableData1: [
          {
            id: 1,
            field01: '这里是分类名称',
            hasChildren: true
          },
          {
            id: 2,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 3,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 4,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 5,
            field01: '分类名称1',
            hasChildren: true
          }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      },
      load(tree, treeNode, resolve) {
        setTimeout(() => {
          resolve([
            {
              id: 11,
              field01: '分类名称1-1'
            },
            {
              id: 12,
              field01: '分类名称1-2'
            }
          ])
        }, 1000)
      }
    }
  }
</script>
