<template>
  <el-drawer
    title="批量创建课程"
    :visible.sync="showDrawer"
    size="900px"
    custom-class="m-drawer"
    destroy-on-close
    :show-close="false"
    @open="open"
    :before-close="beforeClose"
    @close="close"
  >
    <div class="drawer-bd" v-loading="openLoading">
      <el-alert type="warning" :closable="false" class="m-alert f-mb20">
        <p class="f-fb">温馨提示：</p>
        <p>1.导入课程，需选择下载对应课程模板；</p>
        <p>2.填写的信息，请按下载的模板填写要求，严格填写。</p>
      </el-alert>
      <el-steps direction="vertical" :active="4" class="m-vertical-steps" style="height: auto">
        <el-step title="下载批量创建课程模板,填写要求信息">
          <div slot="description">
            <el-button
              type="primary"
              size="small"
              plain
              class="f-mt5"
              icon="el-icon-download"
              @click="downloadModule(templateUrl)"
            >
              批量创建课程模板
            </el-button>
          </div>
        </el-step>
        <el-step title="上传填写好的表格">
          <div slot="description">
            <hb-upload-file v-model="hbFileUploadResponse" :file-type="1" :width="500"></hb-upload-file>
          </div>
        </el-step>
        <el-step title="导入结果">
          <div slot="description">
            <div class="f-c9" v-if="importStatus(ImportStatusEnum.not_yet)">未导入</div>
            <div class="f-cb" v-else-if="importStatus(ImportStatusEnum.importing)">
              导入中<i class="el-icon-loading"></i>
            </div>
            <div class="f-cr" v-else-if="importStatus(ImportStatusEnum.fail)">
              导入失败：请修改数据后重新导入！
              <el-link
                type="primary"
                v-if="mutationImportIssueCourse.importFailFileInfo.url"
                @click="
                  downloadModule(
                    mutationImportIssueCourse.importFailFileInfo.url,
                    mutationImportIssueCourse.importFailFileInfo.name || '查看失败数据.xlsx'
                  )
                "
                :underline="true"
                >{{ mutationImportIssueCourse.importFailFileInfo.name || '查看失败数据.xlsx' }}</el-link
              >
            </div>
          </div>
        </el-step>
      </el-steps>
    </div>
    <div class="drawer-ft m-btn-bar">
      <el-button type="primary" :disabled="loading" @click="importFile">导入</el-button>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Prop, PropSync, Vue } from 'vue-property-decorator'
  import Downloader, { HeaderObj } from '@api/service/common/utils/Downloader'
  import hbUploadFile from '@hbfe/jxjy-admin-components/src/hb-upload-file.vue'
  import { HBFileUploadResponse } from '@hbfe/jxjy-admin-components/src/models/HBFileUploadResponse'
  import MutationImportIssueCourse from '@api/service/management/train-class/mutation/MutationImportIssueCourse'
  import { ImportStatusEnum } from '@api/service/management/train-class/mutation/Enum/ImportStatus'
  import { bind, debounce } from 'lodash-decorators'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'

  @Component({
    components: { hbUploadFile }
  })
  export default class extends Vue {
    @PropSync('batchCourseDrawer', { type: Boolean, default: true }) showDrawer: boolean
    @Prop({ type: Object, default: () => new IssueConfigDetail() }) selectIssue: IssueConfigDetail
    /**
     * 轮询中
     */
    loading = false
    /**
     * 模板地址
     */
    templateUrl = ''
    /**
     * 公共文件上传之后的回调参数
     */
    hbFileUploadResponse = new HBFileUploadResponse()
    /**
     * 课程上传实例
     */
    mutationImportIssueCourse = new MutationImportIssueCourse()
    ImportStatusEnum = ImportStatusEnum
    openLoading = false
    /**
     * 导入状态
     */
    get importStatus() {
      return (status: ImportStatusEnum) => this.mutationImportIssueCourse.importStatus === status
    }
    open() {
      if (!this.templateUrl) {
        this.openLoading = true
        this.mutationImportIssueCourse
          .queryBatchImportIssueCourseTemplate()
          .then((res) => {
            this.templateUrl = res.data
          })
          .finally(() => {
            this.openLoading = false
          })
      }
    }
    @bind
    @debounce(100)
    importFile() {
      if (this.loading) return
      if (this.hbFileUploadResponse && this.hbFileUploadResponse.url) {
        this.loading = true
        this.mutationImportIssueCourse.fileName = this.hbFileUploadResponse.fileName
        this.mutationImportIssueCourse.filePath = this.hbFileUploadResponse.url
        this.mutationImportIssueCourse
          .doImportIssueCourse()
          .then((status) => {
            if (!status.isSuccess()) {
              if (status.code === 3003) this.$message.error('上传文件与模板不符，请检查后重新导入')
              else this.$message.error((status.message as string) || status?.errors?.[0]?.message || '导入失败')
            } else if (this.loading && this.mutationImportIssueCourse.importStatus === ImportStatusEnum.success) {
              this.selectIssue.issueCourseList = this.mutationImportIssueCourse.issueCourseList
              this.$emit('importSuccess')
              this.showDrawer = false
            }
          })
          .catch((error) => {
            this.$message.error(error?.message || '导入失败')
          })
          .finally(() => {
            this.loading = false
          })
      } else {
        this.$message.error('请选择上传的文件，请检查后重新导入')
      }
    }

    async downloadModule(url: string, name = '批量创建期别课程导入表') {
      if (!url) return this.$message.error('文件路径不存在')
      const authorization = localStorage.getItem('admin.Access-Token')
      const header: HeaderObj = {
        ['Authorization']: authorization
      }
      const download = new Downloader(`/mfs${url}`, name, header)
      download.download()
    }
    beforeClose(done: Function) {
      this.$confirm('当前正在操作批量创建课程，是否确定取消操作？', '提示').then(() => {
        done()
      })
    }

    /**
     * 导入成功方法
     */
    importSuccess() {
      //
    }
    close() {
      this.loading = false
      this.hbFileUploadResponse = new HBFileUploadResponse()
      this.mutationImportIssueCourse = new MutationImportIssueCourse()
    }
  }
</script>
