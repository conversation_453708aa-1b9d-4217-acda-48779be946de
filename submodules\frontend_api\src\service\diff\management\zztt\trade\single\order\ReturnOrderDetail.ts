import OrderRefundStatus, { OrderRefundStatusEnum } from '@api/service/common/return-order/enums/OrderRefundStatus'
import { OrderRefundTypeEnum, OnlyReturnList } from '@api/service/common/return-order/enums/OrderRefundType'
import RefundInfo from '@api/service/management/trade/single/order/models/RefundInfo'
import RefundCommodity from '@api/service/management/trade/single/order/models/RefundCommodity'
import { ReturnOrderRecordVo } from '@api/service/management/trade/single/order/query/vo/ReturnOrderRecordVo'
import RefundProcess from '@api/service/management/trade/single/order/models/RefundProcess'
import MsTradeQueryFront, {
  // CommodityAuthInfoResponse,
  CommoditySkuPropertyResponse,
  // CommoditySkuResponse,
  ReturnOrderResponse as ReturnOrder,
  SubOrderInfoResponse1
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
import { RefundProcessTypeEnum } from '@api/service/management/trade/single/order/enum/RefundProcessTypeEnum'
import RefundOrderStatusMapType, { RefundOrderStatusEnum } from '@api/service/common/trade/RefundOrderStatusEnum'
import BasicDataQueryBackstage, {
  AdminInfoResponse,
  AdminQueryRequest,
  AdminUserRequest
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import { Page } from '@hbfe/common'
import { SkuPropertyConvertUtils } from '@api/service/management/train-class/Utils/SkuPropertyConvertUtils'
import ReturnOrderDetailMain from '@api/service/management/trade/single/order/ReturnOrderDetail'
import TradeQueryFrontGatewayTradeQueryBackstage, {
  CommodityAuthInfoResponse,
  // CommoditySkuPropertyResponse,
  CommoditySkuResponse,
  ReturnOrderResponse
} from '@api/diff-gateway/zztt-trade-query-front-gateway-TradeQueryBackstage'

export default class ReturnOrderDetail extends ReturnOrderDetailMain {
  /**
   * 退款单号
   */
  returnOrderNo = ''

  /**
   * 订单号
   */
  orderNo = ''

  /**
   * 子订单号
   */
  subOrderNo = ''

  /**
   * 交易流水号
   */
  flowNo = ''

  /**
   * 购买人信息
   */
  buyer: StudentUserInfoVo = new StudentUserInfoVo()

  /**
   * 主单实付金额
   */
  orderPayAmount = 0

  /**
   * 退货状态
   */
  returnOrderStatus: OrderRefundStatusEnum = undefined

  /**
   * 退款类型
   */
  refundType: OrderRefundTypeEnum = undefined

  /**
   * 退货步骤状态
   */
  refundProcessList: Array<RefundProcess> = new Array<RefundProcess>()

  /**
   * 步骤条完成步骤位置
   */
  refundProcessActivePoint = 0

  /**
   * 退款信息
   */
  refundInfo: RefundInfo = new RefundInfo()

  /**
   * 退款记录
   */
  refundRecords: Array<ReturnOrderRecordVo> = new Array<ReturnOrderRecordVo>()

  /**
   * 商品授权信息
   */
  commodityAuthInfo: CommodityAuthInfoResponse = new CommodityAuthInfoResponse()

  /**
   * 退货 / 款商品（由退货 / 退款商品提级而来）
   */
  operationCommodity: RefundCommodity = new RefundCommodity()

  /**
   * UI使用退款状态
   */
  uiReturnOrderStatue = RefundOrderStatusEnum.RefundOrderStatusEnumApproving

  /**
   * 退货单扩展信息
  key:courseType,华医部分退款
  value:1-专业课   2-公需课  3-都退
   */
  ext = ''

  constructor(returnOrderNo?: string) {
    super()
    this.returnOrderNo = returnOrderNo
  }

  /**
   * 模型转化
   * @param dto 后端元数据
   * @param studentUserInfoList 学员用户信息映射列表
   * @param adminUserInfo 管理员用户信息映射列表
   */
  async fromDiff(
    dto: ReturnOrderResponse,
    studentUserInfoList: Array<StudentUserInfoVo>,
    adminUserInfo: Array<AdminInfoResponse>
  ) {
    const { returnOrderNo, subOrderInfo, basicData, returnCommodity, refundCommodity, commodityAuthInfo } = dto
    this.returnOrderNo = returnOrderNo
    console.log(dto, 'dto')
    const ext = dto.ext as any
    this.ext = ext?.courseType || ''
    const diffDto = Object.assign(new ReturnOrder(), dto)
    await this.from(diffDto, studentUserInfoList, adminUserInfo)
  }
  /**
   * 查询退款单详情
   */
  async queryDetail() {
    const res = await TradeQueryFrontGatewayTradeQueryBackstage.getReturnOrderInServicer(this.returnOrderNo)

    if (res?.status && res.status.isSuccess() && res?.data) {
      // 提取所有需要转换的学员id  一次性取出所以学员信息
      const userIds = new Array<string>()
      const data = res.data || new ReturnOrderResponse()
      if (data.subOrderInfo?.orderInfo?.buyer?.userId) {
        userIds.push(data.subOrderInfo?.orderInfo?.buyer?.userId)
      }
      if (data.approvalInfo?.approveUser?.userId) {
        userIds.push(data.approvalInfo?.approveUser?.userId)
      }
      if (data.basicData?.applyInfo?.applyUser?.userId) {
        userIds.push(data.basicData?.applyInfo?.applyUser?.userId)
      }
      if (data.confirmUser?.userId) {
        userIds.push(data.confirmUser?.userId)
      }
      if (data.basicData?.returnCloseReason?.cancelUser?.userId) {
        userIds.push(data.basicData?.returnCloseReason?.cancelUser?.userId)
      }
      const queryStudentUser = new QueryStudentList()

      // 这边因为有的用户是学员有的用户是管理员，而且区分不开，故两个都查
      let studentUserInfoList = new Array<StudentUserInfoVo>()
      let adminUserInfoList = new Array<AdminInfoResponse>()
      if (userIds) {
        const studentRes = await queryStudentUser.queryStudentListInSubject(userIds)
        if (studentRes.data.length) {
          studentUserInfoList = studentRes.data
        }
        adminUserInfoList = await this.getAdminUserByIds(userIds)
      }
      await this.fromDiff(res.data, studentUserInfoList, adminUserInfoList)
    }
  }

  /**
   * 查询退款单详情 （分销）
   */
  async queryDetailByFx() {
    const res = await TradeQueryFrontGatewayTradeQueryBackstage.getReturnOrderInDistributor(this.returnOrderNo)

    if (res?.status && res.status.isSuccess() && res?.data) {
      // 提取所有需要转换的学员id  一次性取出所以学员信息
      const userIds = new Array<string>()
      const data = res.data || new ReturnOrderResponse()
      if (data.subOrderInfo?.orderInfo?.buyer?.userId) {
        userIds.push(data.subOrderInfo?.orderInfo?.buyer?.userId)
      }
      if (data.approvalInfo?.approveUser?.userId) {
        userIds.push(data.approvalInfo?.approveUser?.userId)
      }
      if (data.basicData?.applyInfo?.applyUser?.userId) {
        userIds.push(data.basicData?.applyInfo?.applyUser?.userId)
      }
      if (data.confirmUser?.userId) {
        userIds.push(data.confirmUser?.userId)
      }
      if (data.basicData?.returnCloseReason?.cancelUser?.userId) {
        userIds.push(data.basicData?.returnCloseReason?.cancelUser?.userId)
      }
      const queryStudentUser = new QueryStudentList()

      // 这边因为有的用户是学员有的用户是管理员，而且区分不开，故两个都查
      let studentUserInfoList = new Array<StudentUserInfoVo>()
      let adminUserInfoList = new Array<AdminInfoResponse>()
      if (userIds) {
        const studentRes = await queryStudentUser.queryStudentListInSubject(userIds)
        if (studentRes.data.length) {
          studentUserInfoList = studentRes.data
        }
        adminUserInfoList = await this.getAdminUserByIds(userIds)
      }
      await this.fromDiff(res.data, studentUserInfoList, adminUserInfoList)
    }
  }
}
