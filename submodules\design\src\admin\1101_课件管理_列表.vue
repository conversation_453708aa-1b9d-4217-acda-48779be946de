<template>
  <el-main>
    <div class="f-p15">
      <div class="f-mb15">
        <el-button type="primary" icon="el-icon-plus">新建课件</el-button>
        <el-button type="primary" icon="el-icon-link">外链课件导入</el-button>
        <el-button type="primary" plain>课件分类管理</el-button>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
        <el-row :gutter="16" class="m-query is-border-bottom">
          <el-form :inline="true" label-width="auto">
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="课件名称">
                <el-input v-model="input" clearable placeholder="请输入课件名称" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="课件供应商">
                <el-select v-model="select" clearable filterable placeholder="请选择课件供应商">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="课件类型">
                <el-select v-model="select" clearable placeholder="请选择课件类型">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="课件分类">
                <el-cascader clearable filterable multiple :options="cascader" placeholder="请选择课件分类" />
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="课件状态">
                <el-select v-model="select" clearable placeholder="请选择课件状态">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="转换状态">
                <el-select v-model="select" clearable placeholder="请选择转换状态">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="媒体源">
                <el-select v-model="select" clearable placeholder="请选择媒体源">
                  <el-option value="选项1"></el-option>
                  <el-option value="选项2"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6">
              <el-form-item label="创建时间">
                <el-date-picker
                  v-model="form.date1"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="起始时间"
                  end-placeholder="结束时间"
                >
                </el-date-picker>
              </el-form-item>
            </el-col>
            <el-col :sm="12" :md="8" :xl="6" class="f-fr">
              <el-form-item class="f-tr">
                <el-button type="primary">查询</el-button>
                <el-button>重置</el-button>
                <!--<el-button type="text">展开<i class="el-icon-arrow-down el-icon&#45;&#45;right"></i></el-button>-->
                <el-button type="text">收起<i class="el-icon-arrow-up el-icon--right"></i></el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="课件名称" min-width="180" fixed="left">
            <template>课件名称课件名称</template>
          </el-table-column>
          <el-table-column label="课件供应商" min-width="180">
            <template>课件供应商</template>
          </el-table-column>
          <el-table-column label="课件分类" min-width="180">
            <template>课件分类课件分类</template>
          </el-table-column>
          <el-table-column label="状态" min-width="100">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-tag type="info">停用</el-tag>
              </div>
              <div v-else-if="scope.$index === 1">
                <el-tag type="info">停用</el-tag>
              </div>
              <div v-else>
                <el-tag type="success">启用</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="转换状态" min-width="120">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">
                <el-badge is-dot type="primary" class="badge-status">转换中</el-badge>
              </div>
              <div v-else-if="scope.$index === 1">
                <el-tooltip placement="top">
                  <div slot="content">
                    <p>如上一次修改包含替换媒体，可能是转码后获取的媒体时长小于原有媒体时长导致本次的转换失败。</p>
                    <p class="f-mt5">
                      如需更正请点击修改，重新替换媒体，时长要求大于等于原有媒体时长，提交后待系统重新解析。
                    </p>
                  </div>
                  <el-badge is-dot type="danger" class="badge-status">转换失败</el-badge>
                </el-tooltip>
              </div>
              <div v-else>
                <el-badge is-dot type="success" class="badge-status">转换成功</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="课件类型" min-width="120">
            <template>课件类型</template>
          </el-table-column>
          <el-table-column label="时长" min-width="120">
            <template>00:21:21</template>
          </el-table-column>
          <el-table-column label="创建人" min-width="120">
            <template>创建人</template>
          </el-table-column>
          <el-table-column label="创建时间" min-width="180">
            <template>2020-11-11 12:20:20</template>
          </el-table-column>
          <el-table-column label="是否被引用" min-width="120" align="center">
            <template>是</template>
          </el-table-column>
          <el-table-column label="操作" width="240" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini">详情</el-button>
              <el-button type="text" size="mini">修改</el-button>
              <el-button type="text" size="mini">删除</el-button>
              <el-button v-if="scope.$index === 0" type="text" size="mini" disabled>启用</el-button>
              <el-button v-else-if="scope.$index === 1" type="text" size="mini" disabled>启用</el-button>
              <el-button v-else type="text" size="mini">停用</el-button>
              <el-button type="text" size="mini">预览</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="currentPage4"
          :page-sizes="[100, 200, 300, 400]"
          :page-size="100"
          layout="total, sizes, prev, pager, next, jumper"
          :total="400"
        >
        </el-pagination>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
