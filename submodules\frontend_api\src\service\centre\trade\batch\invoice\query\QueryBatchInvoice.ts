import TradeQueryBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import InvoiceListResponse from '@api/service/management/trade/batch/invoice/query/vo/InvoiceListResponse'
import { InvoiceTypeEnum } from '../enum/InvoiceEnum'

import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
export default class QueryBatchInvoice {
  /**
   * 查询发票详情
   * @param invoiceId 发票ID
   * @param way 线上or线下
   */
  async queryInvoiceDetail(invoiceId: string, way: InvoiceTypeEnum) {
    if (way == InvoiceTypeEnum.ONLINE) {
      const result = await TradeQueryBackstage.getOnlineInvoiceInServicer(invoiceId)
      const data = InvoiceListResponse.from(result.data)
      return data
    } else {
      const result = await TradeQueryBackstage.getOfflineInvoiceInServicer(invoiceId)
      const data = OffLinePageInvoiceVo.from(result.data)
      return data
    }
  }
}
