<route-meta>
{
"isMenu": true,
"title": "导出任务查看",
"sort":6,
"icon": "icon-chakan"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="导出任务查看" actions="getTaskType">
    <!--  -->
    <div class="f-p15">
      <el-row :gutter="15" class="is-height">
        <el-col :span="7">
          <el-card shadow="never" class="m-card is-header-sticky f-mb15">
            <div slot="header" class="is-sticky">
              <span class="tit-txt">导出任务类型</span>
            </div>
            <div class="f-plr20 f-pb20">
              <el-row :gutter="5" class="m-query no-gutter f-pt15">
                <el-form :inline="true" label-width="auto">
                  <el-col :span="18">
                    <el-form-item>
                      <el-input v-model="taskType" clearable placeholder="请输入任务类型关键字" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="4">
                    <el-form-item v-if="$hasPermission('query')" desc="查询" actions="getTaskType">
                      <el-button type="primary" @click="getTaskType">查询</el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <div class="m-plan-list">
                <el-table
                  ref="exportTable"
                  stripe
                  :data="taskTypeList"
                  max-height="500px"
                  highlight-current-row
                  class="m-table is-body is-arrow"
                  @current-change="handleCurrentChange"
                  v-loading="uiConfig.typeLoading"
                >
                  <el-table-column>
                    <template slot-scope="scope">
                      <div>{{ scope.row.name }}</div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="17">
          <el-card shadow="never" class="m-card is-header-sticky f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">{{ currentTaskType }}</span>
            </div>
            <div class="f-plr20 f-pt20">
              <el-row :gutter="16" class="m-query is-border-bottom no-gutter">
                <el-form :inline="true" label-width="auto">
                  <el-col :sm="12" :xl="6">
                    <el-form-item label="执行状态">
                      <el-select v-model="pageQueryParam.jobState" clearable filterable placeholder="请选择执行状态">
                        <el-option
                          v-for="item in status"
                          :label="item.name"
                          :value="item.value"
                          :key="item.value"
                        ></el-option>
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" v-show="advanced" :xl="9">
                    <el-form-item label="执行时间">
                      <double-date-picker
                        :begin-create-time.sync="pageQueryParam.beginTime"
                        :end-create-time.sync="pageQueryParam.endTime"
                        begin-time-placeholder="请选择执行起始时间"
                        end-time-placeholder="请选择执行结束时间"
                      ></double-date-picker>
                    </el-form-item>
                  </el-col>
                  <el-col :sm="12" :xl="5" class="f-fr">
                    <el-form-item class="f-tr">
                      <template v-if="$hasPermission('query')" desc="查询" actions="doSearch">
                        <el-button type="primary" @click="page.currentChange(1)">查询</el-button></template
                      >
                      <el-button @click="resetCondition">重置</el-button>
                      <el-button type="text" v-if="!defaultShow" @click="showAdvanced">
                        {{ this.advanced ? '收起' : '展开' }}
                        <i :class="advanced ? 'el-icon-arrow-up' : 'el-icon-arrow-down'" />
                      </el-button>
                    </el-form-item>
                  </el-col>
                </el-form>
              </el-row>
              <!--表格-->
              <el-table stripe :data="pageData" max-height="500" class="m-table" v-loading="uiConfig.loading">
                <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
                <el-table-column label="任务名称" min-width="240" fixed="left">
                  <template slot-scope="scope">{{ scope.row.jobName || '-' }}</template>
                </el-table-column>
                <el-table-column label="操作人" min-width="140">
                  <template slot-scope="scope">{{ scope.row.operateUser || '-' }}</template>
                </el-table-column>
                <el-table-column label="任务处理时间" min-width="170">
                  <template slot-scope="scope">{{ scope.row.createTime || '-' }}</template>
                </el-table-column>
                <el-table-column label="任务结束时间" min-width="170">
                  <template slot-scope="scope">{{ scope.row.endTime || '-' }}</template>
                </el-table-column>
                <el-table-column label="任务执行状态" min-width="120">
                  <template slot-scope="scope">
                    <div v-if="taskStatusMapType[scope.row.jobState]">
                      <el-tag :type="taskStatusMapType[scope.row.jobState]">{{
                        taskStatusMapName[scope.row.jobState]
                      }}</el-tag>
                    </div>
                    <div v-else>-</div>
                  </template>
                </el-table-column>
                <el-table-column label="任务处理结果" min-width="120">
                  <template slot-scope="scope">
                    <div v-if="!scope.row.jobResult && taskStatusMapType[scope.row.jobState] != 'primary'">
                      <el-badge is-dot type="danger" class="badge-status">失败</el-badge>
                    </div>
                    <div v-if="!scope.row.jobResult && taskStatusMapType[scope.row.jobState] == 'primary'">
                      <el-badge is-dot type="primary" class="badge-status">未知</el-badge>
                    </div>
                    <div v-else-if="scope.row.jobResult">
                      <el-badge is-dot type="success" class="badge-status">成功</el-badge>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="140" align="center" fixed="right">
                  <template slot-scope="scope">
                    <template v-if="$hasPermission('location')" desc="下载导出数据" actions="location">
                      <el-button
                        type="text"
                        size="mini"
                        :disabled="taskStatusMapName[scope.row.jobState] == '执行中'"
                        @click="location(scope.row)"
                        >下载导出数据</el-button
                      ></template
                    >
                    <template v-if="$hasPermission('viewLog')" desc="查看日志" actions="viewLog">
                      <el-button
                        type="text"
                        size="mini"
                        :disabled="taskStatusMapName[scope.row.jobState] == '执行中'"
                        @click="viewLog(scope.row)"
                        >查看日志</el-button
                      ></template
                    >
                  </template>
                </el-table-column>
              </el-table>
              <!--分页-->
              <hb-pagination :page="page" v-bind="page"></hb-pagination>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import { UiPage } from '@hbfe/common'
  import JobRequestVo from '@api/service/management/async-task/query/vo/JobRequestVo'
  import AsyncTaskItemVo from '@api/service/management/async-task/query/vo/AsyncTaskItemVo'
  import { AsyncTaskJobStatusEnum } from '@api/service/common/enums/async-task/AsyncTaskJobStatusType'
  import AsyncTaskTypeVo from '@api/service/management/async-task/query/vo/AsyncTaskTypeVo'
  import Downloader from '@api/service/common/utils/Downloader'
  import UserModule from '@api/service/management/user/UserModule'

  import QueryExportAsyncTask from '@api/service/management/async-task/query/QueryExportAsyncTask'

  @Component({
    components: { DoubleDatePicker }
  })
  export default class extends Vue {
    status = [
      {
        name: '已执行',
        value: AsyncTaskJobStatusEnum.EXECUTED
      },
      {
        name: '执行中',
        value: AsyncTaskJobStatusEnum.EXECUTING
      },
      {
        name: '执行失败',
        value: AsyncTaskJobStatusEnum.FAIL
      }
    ]
    select = ''
    input = ''
    // 筛选默认展示
    defaultShow = document.documentElement.clientWidth > 1800 ? true : false
    // 筛选默认收起
    advanced = document.documentElement.clientWidth > 1800 ? true : false
    /**
     * 路径资源前缀
     */
    //resourcesPrefix: string
    pageData: Array<AsyncTaskItemVo> = new Array<AsyncTaskItemVo>()
    /**
     * 任务类型关键字
     */
    taskType = ''
    /**
     * 当前选中的任务类型
     */
    currentTaskType = ''
    /**
     * 任务类型列表数据
     */
    // taskTypeList: Array<AsyncTaskTypeVo> = new Array<AsyncTaskTypeVo>()
    taskTypeList = [{ id: 'exportChooseCourseStatistical', name: '选课统计' }]
    page: UiPage
    pageQueryParam: JobRequestVo = new JobRequestVo()

    uiConfig = {
      loading: false,
      typeLoading: false
    }

    taskStatusMapName = {
      [AsyncTaskJobStatusEnum.EXECUTING]: '执行中',
      [AsyncTaskJobStatusEnum.EXECUTED]: '已执行',
      [AsyncTaskJobStatusEnum.FAIL]: '执行失败'
    }

    taskStatusMapType = {
      [AsyncTaskJobStatusEnum.EXECUTING]: 'primary',
      [AsyncTaskJobStatusEnum.EXECUTED]: 'success',
      [AsyncTaskJobStatusEnum.FAIL]: 'danger'
    }

    queryTaskFactory = new QueryExportAsyncTask()

    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    async activated() {
      this.taskType = ''
      await this.getTaskType()
    }
    mounted() {
      window.onresize = () => {
        return this.getWindowWidth()
      }
    }
    getWindowWidth() {
      if (document.documentElement.clientWidth > 1800) {
        this.defaultShow = true
        this.advanced = true
      } else {
        this.defaultShow = false
      }
    }
    showAdvanced() {
      this.advanced = !this.advanced
    }
    async created() {
      //this.resourcesPrefix = '' //获取下载资源路径前缀
      //   当前登录用户是否为地区管理员
      const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
      if (isDistrictAdministrator) {
        this.taskTypeList = [
          { id: 'exportRegionLearningStatistical', name: '地区学习统计' },
          { id: 'exportRegionLearningDetailStatistical', name: '地区学习统计导出列表人员详情' },
          { id: 'exportStudentLearningStatistical', name: '学员学习明细' }
        ]
      }
      this.taskType = ''
      await this.getTaskType()
    }

    //获取导出任务类型
    async getTaskType() {
      this.uiConfig.typeLoading = true
      const res = await this.queryTaskFactory.queryAsyncTaskTypeList(this.taskType)
      if (res.status.isSuccess()) {
        if (res.data.length) {
          //   this.taskTypeList = Object.assign(new Array<AsyncTaskTypeVo>(), res.data)
          this.setCurrent(this.taskTypeList[0])
          if (this.$route.query.type) {
            const curGroup = this.taskTypeList.findIndex((item: AsyncTaskTypeVo) => {
              return item.id === this.$route.query.type
            })
            if (curGroup !== -1) {
              this.setCurrent(this.taskTypeList[curGroup])
            }
          }
        }
        await this.doSearch()
      }
      this.uiConfig.typeLoading = false
    }

    async handleCurrentChange(val: AsyncTaskTypeVo) {
      this.currentTaskType = val.name
      this.pageQueryParam = new JobRequestVo()
      this.pageQueryParam.group = val.id
      await this.doSearch()
    }

    setCurrent(row: any) {
      console.log(row)
      ;(this.$refs.exportTable as any).setCurrentRow(row)
    } //

    async doSearch() {
      this.uiConfig.loading = true
      const res = await this.queryTaskFactory.queryAsyncTaskList(this.page, this.pageQueryParam)
      if (res.status.isSuccess()) {
        this.pageData = Object.assign(new Array<AsyncTaskItemVo>(), res.data)
        console.log(this.pageData, 'this.pageData')
      }
      //处理切换页数后行数错位问题
      ;(this.$refs['exportTable'] as any)?.doLayout()
      this.uiConfig.loading = false
    }
    resetCondition() {
      this.page.pageNo = 1
      const group = this.pageQueryParam.group
      this.pageQueryParam = new JobRequestVo()
      this.pageQueryParam.group = group
      this.doSearch()
    }

    /**
     * 下载导出数据
     */
    location(item: AsyncTaskItemVo) {
      console.log(item, '032202020200202')
      if (item.exportFilePath) {
        const fileNamePathList = item.exportFilePath.split('/')
        const fileName = fileNamePathList[fileNamePathList.length - 1]
        const authorization = localStorage.getItem('admin.Access-Token')
        const downloader = new Downloader('/mfs' + item.exportFilePath, fileName, {
          ['Authorization']: authorization
        })
        downloader.download()
      } else {
        this.$message.error('文件不存在！')
      }
    }

    /**
     * 查看日志
     */
    viewLog(item: AsyncTaskItemVo) {
      if (item.jobResult) {
        this.$message.success('执行成功')
      } else {
        this.$message.error(item.message || '处理异常')
      }
    }
  }
</script>
