import { FxCheckOrderCodeEnum } from '@api/service/customer/trade/single/enum/FxCheckOrderCodeEnum'
import ScopeRangeItem from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/ScopeRangeItem'

export default class VerifyOrderResult {
  /**
   * 响应code
   */
  code: FxCheckOrderCodeEnum = undefined

  /**
   * 消息
   */
  message: string = undefined

  /**
   * 不符合的销售范围
   */
  scopeRangeList: Array<ScopeRangeItem> = new Array<ScopeRangeItem>()
}
