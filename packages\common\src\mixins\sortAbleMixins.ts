import sortable from 'sortablejs'
import { Component, Vue } from 'vue-property-decorator'

@Component
export default class extends Vue {
  // 已调用的页面为准 这边定义只声明
  // 可选课程
  option: Element = null
  // 必修
  compulsory: Element = null
  // 兴趣
  interest: Element = null
  createSortAble(elName: string, obj: any, cbName: string) {
    let times = 0
    const addSortAble = () => {
      const element = document.querySelector(elName)
      if (!element) {
        if (times <= 20) {
          times++
          setTimeout(() => addSortAble(), 100)
        } else {
          return
        }
        return
      }
      sortable.create(element as any, {
        onEnd({ from }) {
          // 执行回调方法
          obj[cbName] = from
        }
      })
    }

    this.$nextTick(() => {
      addSortAble()
    })
  }

  /**排序dom */
  sortDom(keyName: 'option' | 'compulsory' | 'interest', list: any[]) {
    // todo
    const dom = this[keyName]
    if (!dom) {
      return list
    }
    const arr = new Array<any>()
    const trs = dom.children
    const length = trs.length
    for (let i = 0; i < length; i++) {
      const tr = trs[i]
      const td = tr.children[0]
      // 这是获取elementUi的No
      const index = (td as any).innerText - 1
      arr[i] = list[index]
    }
    console.log(arr)
    return arr
  }
}
