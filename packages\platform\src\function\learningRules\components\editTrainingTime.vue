<template>
  <el-drawer title="编辑" :visible.sync="openDialog" size="700px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="form" label-width="auto" class="m-form f-mt30">
            <el-form-item label="年度：">{{ trainingTimeRange.year }}</el-form-item>
            <el-form-item label="设置年度培训时间：" required>
              <el-date-picker
                type="daterange"
                v-model="trainingTimeRange.time"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                placeholder="选择时间范围"
                value-format="yyyy-MM-dd"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button @click="openDialog = false">取消</el-button>
              <el-button type="primary" @click="sureEdit()">确认</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>
<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import BasicInfo, { TrainingTimeRange } from '@api/service/management/learning-rule/model/BasicInfo'
  import { cloneDeep } from 'lodash'

  @Component
  export default class extends Vue {
    // 接收基础信息
    @Prop({
      type: BasicInfo,
      default: () => new BasicInfo()
    })
    basicInfo: BasicInfo

    // 打开抽屉
    openDialog = false
    // 编辑培训时间区间对象
    trainingTimeRange = new TrainingTimeRange()
    // 存储外面传进来的对象
    initIndex: number = null

    // 初始化
    init(item: TrainingTimeRange, index: number) {
      // 获取索引，传给状态层使用修改功能
      this.initIndex = index
      // 深克隆
      this.trainingTimeRange = Object.assign(new TrainingTimeRange(), cloneDeep(item))
      this.openDialog = true
    }

    // 确认修改时间区间
    sureEdit() {
      this.basicInfo.addTimeRange(this.trainingTimeRange, this.initIndex)
      this.openDialog = false
    }
  }
</script>
