<route-params content="/:id"></route-params>
<route-meta>
  {
  "isMenu":true,
  "hideMenu": true,
  "onlyShowOnTab":true,
  "title": "个人订单详情"
  }
</route-meta>

<script lang="ts">
  import PersonalDetail from '@hbfe/jxjy-admin-trade/src/order/personal/detail.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    detail: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    queryRelevancyOrder: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    queryChangeClassDetail: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    refundOrder: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    refundDetail: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY],
    closeOrder: [WXGLY, FXS, GYS, NZFXS, NZFXSJCB, ZTGLY]
  })
  export default class extends PersonalDetail {}
</script>
