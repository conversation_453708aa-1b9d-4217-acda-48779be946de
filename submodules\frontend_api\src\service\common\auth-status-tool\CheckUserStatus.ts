import { EnterTypeEnums } from '@api/service/common/auth-status-tool/enums/EnterTypeEnums'
import { LoginStatusEnums } from '@api/service/common/auth-status-tool/enums/LoginStatusEnums'
import QueryUserInfo from '@api/service/customer/user/query/QueryUserInfo'
import SignCenterControl from '@api/service/customer/sign-center/SignCenterControl'
import QueryMyTrainClassCommodityList from '@api/service/customer/train-class/query/QueryMyTrainClassCommodityList'
import { Page, ResponseStatus } from '@hbfe/common'
import { SchemeRequest } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryForestage'
import { QueryClassStatue } from '@api/service/customer/train-class/query/QueryClassStatue'
import { StudentSchemeLearningRequest } from '@api/service/customer/train-class/query/vo/MyTrainFilter'
import QueryMyTrainClassDetail from '@api/service/customer/train-class/query/QueryMyTrainClassDetail'
import { TrainingModeEnum } from '@api/service/common/scheme/enum/TrainingMode'

/**
 * 检测用户状态工具类
 */
class CheckUserStatus {
  /**
   * 进入类型
   */
  entryType: EnterTypeEnums = null
  /**
   * 登录状态
   */
  loginStatus: LoginStatusEnums = null
  /**
   * 重定向地址
   */
  redirectUrl = ''
  /**
   * 方案参数资格id
   */
  qualificationId = ''
  /**
   * 期数参训资格ID
   */
  periodQualificationId = ''
  /**
   * 学习方式ID
   */
  learningId = ''
  /**
   * 方案开始时间
   */
  schemeBeginDate = ''
  /**
   * 学号
   */
  studentNo = ''

  /**
   * 检测是否完善
   */
  async checkIsPerfect() {
    await QueryUserInfo.getCurrenUserInfo()
    // await QueryUserInfo.getThirdCertificatesInfo()
    // await QueryUserInfo.getCurrentUserCertificatesList()
    const userInfo = QueryUserInfo.userInfo?.userInfo
    // 无姓名
    if (!userInfo?.userName) {
      return false
    } else if (!userInfo?.idCard) {
      // 无证件号
      return false
    } else if (!userInfo?.idCardType) {
      // 无证件类型
      return false
    } else if (!userInfo?.gender) {
      // 无性别
      return false
    } else if (!userInfo?.phone) {
      // 无手机号
      return false
    }
    return true
  }

  /**
   * 检测当前用户是否拥有已培训面授班级
   */
  async checkHasTrainingClass() {
    const data = await SignCenterControl.getStudentLastStudyQualification()
    this.qualificationId = data?.qualificationId
    return data
  }

  /**
   * 判断学员对应学习方式状态
   * @param schemeId
   * @param issueId
   * @param questionnaireId 进入问卷必传
   * @param learningId 进入问卷必传(问卷应用范围类型)
   */
  async checkUserLearningTypeStatus(
    schemeId: string,
    issueId: string,
    questionnaireId?: string,
    learningId?: string,
    uniqueKey?: string
  ) {
    const responseStatus = new ResponseStatus(200, '')
    // 获取方案列表
    const queryMyTrainClassCommodityList = new QueryMyTrainClassCommodityList()
    const page = new Page(1, 10)
    const request = new StudentSchemeLearningRequest()
    request.scheme = new SchemeRequest()
    request.scheme.schemeId = schemeId
    const listResponse = await queryMyTrainClassCommodityList.queryMyTrainClassList({
      page,
      request
    })
    if (!listResponse.data.currentPageData?.length) {
      responseStatus.code = 4001 // 无方案
      return responseStatus
    }
    const currentScheme = listResponse.data?.currentPageData[0]
    this.qualificationId = currentScheme?.qualificationId
    // 获取班级详情
    const queryMyTrainClassDetail = new QueryMyTrainClassDetail()
    this.studentNo = currentScheme?.studentNo
    queryMyTrainClassDetail.qualificationId = this.qualificationId
    queryMyTrainClassDetail.studentNo = this.studentNo
    // queryMyTrainClassDetail.trainingMode = currentScheme?.scheme?.skuProperty?.trainingWay
    //   ?.skuPropertyValueId as TrainingModeEnum
    queryMyTrainClassDetail.trainClassDetail.trainClassBaseInfo.id = schemeId
    await queryMyTrainClassDetail.queryTrainClassDetail()
    // 获取期别信息---签到使用
    const currentIssue = queryMyTrainClassDetail.queryIssueConfigDetailByIssueId(issueId)
    this.schemeBeginDate = currentIssue.trainingDateRange.startDate
    this.periodQualificationId = queryMyTrainClassDetail.issueLearningDetail.periodQualificationId
    // 判断配置
    const queryClassStatue = new QueryClassStatue()
    if ([EnterTypeEnums.classSignOut, EnterTypeEnums.classCheckIn, EnterTypeEnums.report].includes(this.entryType)) {
      this.learningId = currentIssue.relateTeachPlanLearning?.id
    }
    if (this.entryType === EnterTypeEnums.questionnaire) {
      if (learningId) {
        this.learningId = learningId
      } else {
        await queryMyTrainClassDetail.requestClassDetail()

        const questionnaireConfig = queryMyTrainClassDetail.trainClassDetail.learningTypeModel.issue.issueConfigList.find(
          item => item.id == queryMyTrainClassDetail.issueLearningDetail.periodId
        )
        const currentQuestionnaireConfig = questionnaireConfig.perQuestionnaireBackupMap.get(uniqueKey)
        console.log(currentQuestionnaireConfig, 'currentQuestionnaireConfig')
        this.learningId = currentQuestionnaireConfig?.id
      }
    }
    queryClassStatue.qualificationId = this.qualificationId
    queryClassStatue.learningId = this.learningId
    return queryClassStatue.judgeClass()
  }
}

export default new CheckUserStatus()
