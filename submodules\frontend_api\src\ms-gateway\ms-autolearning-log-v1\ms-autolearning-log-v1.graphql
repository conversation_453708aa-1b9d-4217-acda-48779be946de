"""独立部署的微服务,K8S服务名:ms-autolearning-v1"""
schema {
	query:Query
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(implementsInputs:[String],value:String!) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""查询自动学习编排日志"""
	queryAutoLearningArrangeLog(studentAutoLearningTaskResultId:String):[AutoLearningArrangeLogResponse]
	"""查询自动学习执行日志"""
	queryAutoLearningExecuteLog(studentAutoLearningTaskResultId:String):[AutoLearningExecuteLogResponse]
	"""查看详情-课程学习"""
	queryCourseLearningDetail(request:CourseLearningDetailRequest):CourseLearningDetailResponse
	"""查看详情-考试"""
	queryExamLearningDetail(request:ExamLearningDetailRequest):ExamLearningDetailResponse
}
"""课程学习详情请求
	<AUTHOR>
	@date 2025/3/20 16:21
"""
input CourseLearningDetailRequest @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.request.CourseLearningDetailRequest") {
	"""日志id"""
	logId:String!
	"""日志类型：1-编排日志，2-执行日志"""
	logType:Int!
}
"""查看日志详情请求
	<AUTHOR>
	@date 2025/3/20 9:50
"""
input ExamLearningDetailRequest @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.request.ExamLearningDetailRequest") {
	"""日志id"""
	logId:String!
	"""日志类型：1-编排日志，2-执行日志"""
	logType:Int!
}
"""编排日志返回值
	<AUTHOR>
	@date 2025/3/20 14:55
"""
type AutoLearningArrangeLogResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.AutoLearningArrangeLogResponse") {
	"""日志ID"""
	logId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""学员自动学习任务结果ID"""
	studentAutoLearningTaskResultId:String
	"""主任务ID"""
	mainTaskId:String
	"""学习方案ID"""
	learningSchemeId:String
	"""学号"""
	studentNo:String
	"""参训资格ID"""
	qualificationId:String
	"""信息"""
	message:String
	"""类型:0-导入，1-重启
		@see com.fjhb.ms.autolearning.v1.api.constant.AutoLearningLogTypes;
	"""
	type:Int
	"""编排次数(当type=1的时候才有值)"""
	arrangeNum:Int
	"""创建时间"""
	createTime:DateTime
	"""完成时间"""
	completeTime:DateTime
	"""是否允许重叠"""
	allowOverlap:Boolean!
}
"""执行日志返回值
	<AUTHOR>
	@date 2025/3/20 14:57
"""
type AutoLearningExecuteLogResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.AutoLearningExecuteLogResponse") {
	"""日志ID"""
	logId:String
	"""平台ID"""
	platformId:String
	"""平台版本ID"""
	platformVersionId:String
	"""项目ID"""
	projectId:String
	"""子项目ID"""
	subProjectId:String
	"""单位ID"""
	unitId:String
	"""服务商id"""
	servicerId:String
	"""学员自动学习任务结果ID"""
	studentAutoLearningTaskResultId:String
	"""主任务ID"""
	mainTaskId:String
	"""学习方案ID"""
	learningSchemeId:String
	"""学号"""
	studentNo:String
	"""参训资格ID"""
	qualificationId:String
	"""状态:0-失败，1-中断，2-完成
		@see com.fjhb.ms.autolearning.v1.kernel.consts.AutoLearningLogExecuteStatus
	"""
	status:Int
	"""信息"""
	message:String
	"""类型：0-导入，1-重启
		@see com.fjhb.ms.autolearning.v1.kernel.consts.AutoLearningLogTypes
	"""
	type:Int
	"""创建时间"""
	createTime:DateTime
}
"""课程学习详情返回值
	<AUTHOR>
	@date 2025/3/20 16:19
"""
type CourseLearningDetailResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.CourseLearningDetailResponse") {
	"""code"""
	code:String
	"""message"""
	message:String
	"""期望开始学习时间"""
	expectStartStudyTime:DateTime
	"""课程详情"""
	courseDetails:[CourseDetail]
}
"""课程信息返回值
	<AUTHOR>
	@date 2025/3/21 15:06
"""
type CourseLearningInfoResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.CourseLearningInfoResponse") {
	"""课程Id"""
	courseId:String
	"""学习开始时间"""
	studyStartTime:DateTime
	"""学习结束时间"""
	studyEndTime:DateTime
	"""完成进度-所有媒体资源进度相加(currentLearningProgress)"""
	completionProgress:Double
	"""学习完成情况
		@see com.fjhb.ms.autolearning.v1.kernel.domain.autolearninglog.consts.CompleteStatus
		1-未完成，2-已完成，3-终止(自主学习插入)，4-终止(培训时间缩短执行失败) 5-已终止(管理员中止)
	"""
	completeStatus:Int
}
"""课后测验信息返回值
	<AUTHOR>
	@date 2025/3/21 16:00
"""
type CourseQuizInfoResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.CourseQuizInfoResponse") {
	"""课程Id"""
	courseId:String
	"""测验开始时间"""
	answerStartTime:DateTime
	"""测验结束时间"""
	answerEndTime:DateTime
	"""测验分数"""
	quizScore:Int
	"""测验完成情况"""
	quizCompleteStatus:Int
}
"""班级考试详情返回值
	<AUTHOR>
	@date 2025/3/20 9:31
"""
type ExamLearningDetailResponse @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.ExamLearningDetailResponse") {
	"""code
		200-成功
		60001-未获取考试类型的子任务数据
	"""
	code:String
	"""message"""
	message:String
	"""考试场次名称"""
	examSessionName:String
	"""考试开始时间"""
	examStartTime:DateTime
	"""考试结束时间"""
	examEndTime:DateTime
	"""完成状态"""
	completeStatus:Int
	"""考试成绩"""
	examScore:Int
}
"""课程详情
	<AUTHOR>
	@date 2025/3/24 16:30
"""
type CourseDetail @type(value:"com.fjhb.ms.autolearning.v1.kernel.gateway.graphql.response.dto.CourseDetail") {
	"""课程信息"""
	courseInfo:CourseLearningInfoResponse
	"""课后测验信息"""
	courseQuizInfo:CourseQuizInfoResponse
}

scalar List
