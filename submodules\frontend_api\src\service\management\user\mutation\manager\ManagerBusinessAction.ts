import basicdata from '@api/ms-gateway/ms-basicdata-domain-gateway-v1'
import platformJxjyDistributor, {
  SaveStatusDistributorAdminRequest
} from '@api/platform-gateway/platform-jxjy-distributor-admin-v1'
import Context from '@api/service/common/context/Context'

/**
 * 操作管理员业务类
 */
class MutationManagerBusinessAction {
  /**
   * 账号id
   */
  private accountId = ''

  constructor(id: string) {
    this.accountId = id
  }

  /**
   * 停用
   */
  async doDeactivate() {
    const response = await basicdata.freezeAccount(this.accountId)
    return response
  }

  /**
   * 启用
   */
  async doEnable() {
    const response = await basicdata.resumeAccount(this.accountId)
    return response
  }

  /**
   * 启用——分销商管理员
   */
  async doEnableDistributor(roleIds?: string[]) {
    const request = new SaveStatusDistributorAdminRequest()
    request.accountId = this.accountId
    request.status = 1
    request.onlineSchoolId = Context.businessEnvironment.serviceToken?.tokenMeta?.servicerId
    if (roleIds?.length) {
      request.roleIds = roleIds
    }
    return platformJxjyDistributor.saveStatusDistributorAdmin(request)
  }

  /**
   * 停用——分销商管理员
   */
  async doDeactivateDistributor(roleIds?: string[]) {
    const request = new SaveStatusDistributorAdminRequest()
    request.accountId = this.accountId
    request.status = 0
    request.onlineSchoolId = Context.businessEnvironment.serviceToken?.tokenMeta?.servicerId
    if (roleIds?.length) {
      request.roleIds = roleIds
    }
    return platformJxjyDistributor.saveStatusDistributorAdmin(request)
  }
}
export default MutationManagerBusinessAction
