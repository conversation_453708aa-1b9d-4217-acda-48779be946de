import ExamLearning from '@api/service/common/models/myscheme/ExamLearning'

/**
 * 用户考试学习方式
 */
class UserExamLearning extends ExamLearning {
  /**
   * 最高考试成绩
   */
  highestExamScore: number
  /**
   * 最新的考试成绩
   */
  latestExamScore: number

  /**
   * 当前考核状态，-1未考核，0考核不通过，1考核通过
   备注：该值可能被考核计算策略影响
   */
  assessStatus: number
  /**
   * 考核通过时间
   */
  passedTime: Date

  /**
   * 是否有最高成绩
   */
  hasHighestExamScore() {
    return !!this.highestExamScore
  }

  /**
   * 是否有最新成绩
   */
  hasLatestExamScore() {
    return !!this.latestExamScore
  }

  /**
   * 考试是否合格
   */
  isPassExam() {
    return this.assessStatus == 1
  }
}

export default UserExamLearning
