/*
 * @Description: 发票工厂
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-03-25 14:27:32
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-24 11:59:03
 */

import { Page } from '@hbfe/common'
import QueryInvoice from '@api/service/customer/trade/single/invoice/query/QueryInvoice'
import MutationInvoice from './mutation/MutationInvoice'
import { InvoiceTypeEnum } from './enum/InvoiceEnum'
class InvoiceFactor {
  /**
   * 查询发票
   */
  get queryInvoice() {
    return (invoiceId: Array<string>, way?: InvoiceTypeEnum) => {
      return new QueryInvoice(invoiceId, way)
    }
  }
  /**
   * 业务发票
   */
  get mutationInvoice() {
    return new MutationInvoice()
  }
}
export default new InvoiceFactor()
