import { BatchOrderTradeStatusEnum } from '@api/service/management/trade/batch/order/enum/BatchOrderTradeStatus'
import { BatchOrderResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import BatchOrderDetailBuyerInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailBuyerInfoVo'
import DataResolve from '@api/service/common/utils/DataResolve'
import BatchOrderRemittanceVoucherInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderRemittanceVoucherInfoVo'
import BatchOrderUtils from '@api/service/management/trade/batch/order/query/utils/BatchOrderUtils'
import BatchOrderRefundInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderRefundInfoVo'
import BatchOrderDetailInvoiceInfoVo from '@api/service/management/trade/batch/order/query/vo/BatchOrderDetailInvoiceInfoVo'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { PaymentMethodEnum } from '@api/service/management/trade/batch/order/enum/PaymentMethod'

/**
 * @description 【集体报名订单列表】详情
 */
class BatchOrderListDetailVo extends BatchOrderResponse {
  /**
   * 批次号
   */
  batchOrderNo = ''

  /**
   * 购买人信息
   */
  buyerInfo: BatchOrderDetailBuyerInfoVo = new BatchOrderDetailBuyerInfoVo()

  /**
   *  缴费人次
   */
  payTimes = 0

  /**
   * 实付金额
   */
  payAmount = 0

  /**
   * 订单金额
   */
  orderAmount = 0

  /**
   * 交易成功时间
   */
  paymentCompleteTime = ''

  /**
   * 批次提交时间
   */
  applyTime = ''

  /**
   * 交易状态 1:待下单、2:下单中、3:待付款、4:支付中、5:开通中、6:交易成功、7:交易关闭中、8:交易关闭
   */
  orderStatus: BatchOrderTradeStatusEnum = null

  /**
   * 汇款凭证信息
   */
  remittanceVoucherInfoList: BatchOrderRemittanceVoucherInfoVo[] = []

  /**
   * 是否是线下收款
   */
  isOffLine = false

  /**
   * 退款信息
   */
  refundInfo: BatchOrderRefundInfoVo = new BatchOrderRefundInfoVo()

  /**
   * 批次单是否处于待发货
   */
  isBatchOrderWaitDelivery = false

  /**
   * 发票信息
   */
  invoiceInfoList: BatchOrderDetailInvoiceInfoVo[] = []

  /**
   * 是否有退款记录
   */
  hasRefundRecord: boolean = null

  /**
   * 付款单号
   */
  paymentOrderNo = ''

  /**
   * 是否已经申请发票
   */
  isInvoiceApplied: boolean
  /**
   * 销售渠道
   */
  saleChannel: SaleChannelEnum = null
  /**
   * 缴费方式
   */
  paymentMethod: PaymentMethodEnum = null
  /**
   * 是否可补要发票
   */
  get canGetInvoice() {
    return (
      !this.isInvoiceApplied &&
      this.orderStatus === BatchOrderTradeStatusEnum.Pay_Success &&
      !this.hasRefundRecord &&
      this.payAmount
    )
  }

  /**
   * 是否可退款
   */
  get canRefund() {
    return [BatchOrderTradeStatusEnum.Pay_Success].includes(this.orderStatus)
  }

  static async fromList(response: BatchOrderResponse): Promise<BatchOrderListDetailVo> {
    const detail = new BatchOrderListDetailVo()
    Object.assign(detail, response)
    detail.orderStatus = BatchOrderUtils.getBatchOrderStatus(response)
    detail.batchOrderNo = response.batchOrderNo ?? ''
    if (detail.orderStatus === BatchOrderTradeStatusEnum.Wait_Place_Order) {
      detail.payTimes = await BatchOrderUtils.getUploadSignUpTotalCount(detail.batchOrderNo)
      // detail.payAmount = await BatchOrderUtils.getUploadSignUpTotalAmountByBatchOrderNo(detail.batchOrderNo)
      detail.payAmount = 0
      detail.orderAmount = detail.payAmount
    } else {
      detail.payTimes = response.basicData?.orderForBatchCount ?? 0
      detail.payAmount = response.basicData?.amount ?? 0
      detail.orderAmount = detail.payAmount
    }
    detail.saleChannel = response.basicData?.saleChannel
    detail.buyerInfo.buyerId = response.creator?.userId ?? ''
    detail.paymentCompleteTime = response.basicData?.batchOrderStatusChangeTime?.completed ?? ''
    detail.applyTime = response.basicData?.batchOrderStatusChangeTime?.committing ?? ''
    detail.isOffLine = response.payInfo?.paymentOrderType === 2 ? true : false
    if (DataResolve.isWeightyArr(response.payInfo?.paymentVoucherList)) {
      detail.remittanceVoucherInfoList = BatchOrderUtils.getRemittanceVoucherInfoList(
        response.payInfo.paymentVoucherList
      )
    }
    // detail.refundInfo = await BatchOrderUtils.getRefundInfo(detail.batchOrderNo)
    detail.isBatchOrderWaitDelivery = response.basicData?.deliveryStatus === 0 ? true : false
    // 付款单号
    detail.paymentOrderNo = response.payInfo?.paymentOrderNo
    detail.paymentMethod = response.payInfo?.paymentOrderType
    return detail
  }
  static async from(response: BatchOrderResponse): Promise<BatchOrderListDetailVo> {
    const detail = new BatchOrderListDetailVo()
    Object.assign(detail, response)
    detail.orderStatus = BatchOrderUtils.getBatchOrderStatus(response)
    detail.batchOrderNo = response.batchOrderNo ?? ''
    if (detail.orderStatus === BatchOrderTradeStatusEnum.Wait_Place_Order) {
      detail.payTimes = await BatchOrderUtils.getUploadSignUpTotalCount(detail.batchOrderNo)
      detail.payAmount = 0
      detail.orderAmount = detail.payAmount
    } else {
      detail.payTimes = response.basicData?.orderForBatchCount ?? 0
      detail.payAmount = response.basicData?.amount ?? 0
      detail.orderAmount = detail.payAmount
    }
    detail.buyerInfo.buyerId = response.creator?.userId ?? ''
    detail.paymentCompleteTime = response.basicData?.batchOrderStatusChangeTime?.completed ?? ''
    detail.applyTime = response.basicData?.batchOrderStatusChangeTime?.committing ?? ''
    detail.isOffLine = response.payInfo?.paymentOrderType === 2 ? true : false
    if (DataResolve.isWeightyArr(response.payInfo?.paymentVoucherList)) {
      detail.remittanceVoucherInfoList = BatchOrderUtils.getRemittanceVoucherInfoList(
        response.payInfo.paymentVoucherList
      )
    }
    detail.refundInfo = await BatchOrderUtils.getRefundInfo(detail.batchOrderNo)
    detail.isBatchOrderWaitDelivery = response.basicData?.deliveryStatus === 0 ? true : false
    // 发票信息
    detail.invoiceInfoList = await BatchOrderUtils.getBatchOrderInvoiceInfoList(response)
    if (detail.orderStatus === BatchOrderTradeStatusEnum.Pay_Success) {
      detail.hasRefundRecord = await BatchOrderUtils.getBatchHasRefundRecord(detail.batchOrderNo)
    }
    // 付款单号
    detail.paymentOrderNo = response.payInfo?.paymentOrderNo
    return detail
  }

  /**
   * 更新退款信息
   */
  updateRefundInfo(refundInfo: BatchOrderRefundInfoVo) {
    this.refundInfo = refundInfo
  }
}

export default BatchOrderListDetailVo
