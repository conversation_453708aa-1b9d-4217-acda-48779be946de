import getBatchOrderInDistributor from './queries/getBatchOrderInDistributor.graphql'
import getBatchOrderInServicer from './queries/getBatchOrderInServicer.graphql'
import getBatchReturnOrderInDistributor from './queries/getBatchReturnOrderInDistributor.graphql'
import getBatchReturnOrderInServicer from './queries/getBatchReturnOrderInServicer.graphql'
import getCommodityReportSummaryInCourseSupplier from './queries/getCommodityReportSummaryInCourseSupplier.graphql'
import getCommodityReportSummaryInServicer from './queries/getCommodityReportSummaryInServicer.graphql'
import getCommoditySkuInServicer from './queries/getCommoditySkuInServicer.graphql'
import getCommoditySkuInServicerInCourseVendor from './queries/getCommoditySkuInServicerInCourseVendor.graphql'
import getDeliveryChannelInServicer from './queries/getDeliveryChannelInServicer.graphql'
import getDeliveryChannelListInServicer from './queries/getDeliveryChannelListInServicer.graphql'
import getDeliveryChannelListV2InServicer from './queries/getDeliveryChannelListV2InServicer.graphql'
import getExchangeOrderInDistributor from './queries/getExchangeOrderInDistributor.graphql'
import getExchangeOrderInServicer from './queries/getExchangeOrderInServicer.graphql'
import getInvoiceAutoBillPolicyInServicer from './queries/getInvoiceAutoBillPolicyInServicer.graphql'
import getOfflineInvoiceInServicer from './queries/getOfflineInvoiceInServicer.graphql'
import getOnlineInvoiceInServicer from './queries/getOnlineInvoiceInServicer.graphql'
import getOrderInDistributor from './queries/getOrderInDistributor.graphql'
import getOrderInServicer from './queries/getOrderInServicer.graphql'
import getPurchaseChannel from './queries/getPurchaseChannel.graphql'
import getReceiveAccountInServicer from './queries/getReceiveAccountInServicer.graphql'
import getReceiveAccountInSubProject from './queries/getReceiveAccountInSubProject.graphql'
import getReceiveAccountV2InServicer from './queries/getReceiveAccountV2InServicer.graphql'
import getReturnOrderInDistributor from './queries/getReturnOrderInDistributor.graphql'
import getReturnOrderInServicer from './queries/getReturnOrderInServicer.graphql'
import getTaxpayerInServicer from './queries/getTaxpayerInServicer.graphql'
import getTaxpayerV2InServicer from './queries/getTaxpayerV2InServicer.graphql'
import getTradeStatisticDateHistogramInServicer from './queries/getTradeStatisticDateHistogramInServicer.graphql'
import listBuyerAllCommodityInSerivicer from './queries/listBuyerAllCommodityInSerivicer.graphql'
import listBuyerValidCommodityInSerivicer from './queries/listBuyerValidCommodityInSerivicer.graphql'
import listCommodityOpenReportFormsBeSoldInServicer from './queries/listCommodityOpenReportFormsBeSoldInServicer.graphql'
import listCommoditySkuRegionOpenReportFormsInServicer from './queries/listCommoditySkuRegionOpenReportFormsInServicer.graphql'
import listMissExchangeOrderByPage from './queries/listMissExchangeOrderByPage.graphql'
import listMissOrderByPage from './queries/listMissOrderByPage.graphql'
import listMissReturnOrderByPage from './queries/listMissReturnOrderByPage.graphql'
import listOfflineInvoiceOperationRecord from './queries/listOfflineInvoiceOperationRecord.graphql'
import listOnlineInvoiceOperationRecord from './queries/listOnlineInvoiceOperationRecord.graphql'
import listReceiveAccountInServicer from './queries/listReceiveAccountInServicer.graphql'
import listReceiveAccountInSubProject from './queries/listReceiveAccountInSubProject.graphql'
import listReceiveAccountV2InServicer from './queries/listReceiveAccountV2InServicer.graphql'
import listRegionOpenReportFormsInServier from './queries/listRegionOpenReportFormsInServier.graphql'
import listReturnReasonInfoInSubProject from './queries/listReturnReasonInfoInSubProject.graphql'
import listTaxpayerInServicer from './queries/listTaxpayerInServicer.graphql'
import listTaxpayerV2InService from './queries/listTaxpayerV2InService.graphql'
import listUniqueRegionFromCommodityInServicer from './queries/listUniqueRegionFromCommodityInServicer.graphql'
import pageBatchOrderInDistributor from './queries/pageBatchOrderInDistributor.graphql'
import pageBatchOrderInServicer from './queries/pageBatchOrderInServicer.graphql'
import pageBatchOrderInTrainingChannel from './queries/pageBatchOrderInTrainingChannel.graphql'
import pageBatchReturnOrderInDistributor from './queries/pageBatchReturnOrderInDistributor.graphql'
import pageBatchReturnOrderInServicer from './queries/pageBatchReturnOrderInServicer.graphql'
import pageBatchReturnOrderInTrainingChannel from './queries/pageBatchReturnOrderInTrainingChannel.graphql'
import pageCommodityOpenReportFormsInCourseSupplier from './queries/pageCommodityOpenReportFormsInCourseSupplier.graphql'
import pageCommodityOpenReportFormsInServicer from './queries/pageCommodityOpenReportFormsInServicer.graphql'
import pageCommoditySkuInServicer from './queries/pageCommoditySkuInServicer.graphql'
import pageCommoditySkuInServicerInCourseVendor from './queries/pageCommoditySkuInServicerInCourseVendor.graphql'
import pageCommoditySkuInTrainingChannel from './queries/pageCommoditySkuInTrainingChannel.graphql'
import pageExchangeOrderInDistributor from './queries/pageExchangeOrderInDistributor.graphql'
import pageExchangeOrderInServicer from './queries/pageExchangeOrderInServicer.graphql'
import pageIssueCommoditySkuInServicer from './queries/pageIssueCommoditySkuInServicer.graphql'
import pageIssueLogInDistributor from './queries/pageIssueLogInDistributor.graphql'
import pageIssueLogInServicer from './queries/pageIssueLogInServicer.graphql'
import pageOfflineInvoiceInServicer from './queries/pageOfflineInvoiceInServicer.graphql'
import pageOfflineInvoiceInTrainingChannel from './queries/pageOfflineInvoiceInTrainingChannel.graphql'
import pageOnlineInvoiceInServicer from './queries/pageOnlineInvoiceInServicer.graphql'
import pageOnlineInvoiceInTrainingChannel from './queries/pageOnlineInvoiceInTrainingChannel.graphql'
import pageOrderInDistributor from './queries/pageOrderInDistributor.graphql'
import pageOrderInServicer from './queries/pageOrderInServicer.graphql'
import pageOrderInTrainingChannel from './queries/pageOrderInTrainingChannel.graphql'
import pageReceiveAccountInDistribution from './queries/pageReceiveAccountInDistribution.graphql'
import pageReceiveAccountInServicer from './queries/pageReceiveAccountInServicer.graphql'
import pageReceiveAccountInSubProject from './queries/pageReceiveAccountInSubProject.graphql'
import pageReturnOrderInDistributor from './queries/pageReturnOrderInDistributor.graphql'
import pageReturnOrderInServicer from './queries/pageReturnOrderInServicer.graphql'
import pageReturnOrderInTrainingChannel from './queries/pageReturnOrderInTrainingChannel.graphql'
import statisticBatchOrderInDistributor from './queries/statisticBatchOrderInDistributor.graphql'
import statisticBatchOrderInServicer from './queries/statisticBatchOrderInServicer.graphql'
import statisticBatchOrderInTrainingChannel from './queries/statisticBatchOrderInTrainingChannel.graphql'
import statisticBatchReturnOrderInDistributor from './queries/statisticBatchReturnOrderInDistributor.graphql'
import statisticBatchReturnOrderInServicer from './queries/statisticBatchReturnOrderInServicer.graphql'
import statisticBatchReturnOrderInTrainingChannel from './queries/statisticBatchReturnOrderInTrainingChannel.graphql'
import statisticOnlineInvoiceInServicer from './queries/statisticOnlineInvoiceInServicer.graphql'
import statisticOnlineInvoiceInTrainingChannel from './queries/statisticOnlineInvoiceInTrainingChannel.graphql'
import statisticOrderInDistributor from './queries/statisticOrderInDistributor.graphql'
import statisticOrderInServicer from './queries/statisticOrderInServicer.graphql'
import statisticOrderInTrainingChannel from './queries/statisticOrderInTrainingChannel.graphql'
import statisticReturnOrderInDistributor from './queries/statisticReturnOrderInDistributor.graphql'
import statisticReturnOrderInServicer from './queries/statisticReturnOrderInServicer.graphql'
import statisticReturnOrderInTrainingChannel from './queries/statisticReturnOrderInTrainingChannel.graphql'
import temporaryOrderUpdateOrderInfoInSubject from './queries/temporaryOrderUpdateOrderInfoInSubject.graphql'

export {
  getBatchOrderInDistributor,
  getBatchOrderInServicer,
  getBatchReturnOrderInDistributor,
  getBatchReturnOrderInServicer,
  getCommodityReportSummaryInCourseSupplier,
  getCommodityReportSummaryInServicer,
  getCommoditySkuInServicer,
  getCommoditySkuInServicerInCourseVendor,
  getDeliveryChannelInServicer,
  getDeliveryChannelListInServicer,
  getDeliveryChannelListV2InServicer,
  getExchangeOrderInDistributor,
  getExchangeOrderInServicer,
  getInvoiceAutoBillPolicyInServicer,
  getOfflineInvoiceInServicer,
  getOnlineInvoiceInServicer,
  getOrderInDistributor,
  getOrderInServicer,
  getPurchaseChannel,
  getReceiveAccountInServicer,
  getReceiveAccountInSubProject,
  getReceiveAccountV2InServicer,
  getReturnOrderInDistributor,
  getReturnOrderInServicer,
  getTaxpayerInServicer,
  getTaxpayerV2InServicer,
  getTradeStatisticDateHistogramInServicer,
  listBuyerAllCommodityInSerivicer,
  listBuyerValidCommodityInSerivicer,
  listCommodityOpenReportFormsBeSoldInServicer,
  listCommoditySkuRegionOpenReportFormsInServicer,
  listMissExchangeOrderByPage,
  listMissOrderByPage,
  listMissReturnOrderByPage,
  listOfflineInvoiceOperationRecord,
  listOnlineInvoiceOperationRecord,
  listReceiveAccountInServicer,
  listReceiveAccountInSubProject,
  listReceiveAccountV2InServicer,
  listRegionOpenReportFormsInServier,
  listReturnReasonInfoInSubProject,
  listTaxpayerInServicer,
  listTaxpayerV2InService,
  listUniqueRegionFromCommodityInServicer,
  pageBatchOrderInDistributor,
  pageBatchOrderInServicer,
  pageBatchOrderInTrainingChannel,
  pageBatchReturnOrderInDistributor,
  pageBatchReturnOrderInServicer,
  pageBatchReturnOrderInTrainingChannel,
  pageCommodityOpenReportFormsInCourseSupplier,
  pageCommodityOpenReportFormsInServicer,
  pageCommoditySkuInServicer,
  pageCommoditySkuInServicerInCourseVendor,
  pageCommoditySkuInTrainingChannel,
  pageExchangeOrderInDistributor,
  pageExchangeOrderInServicer,
  pageIssueCommoditySkuInServicer,
  pageIssueLogInDistributor,
  pageIssueLogInServicer,
  pageOfflineInvoiceInServicer,
  pageOfflineInvoiceInTrainingChannel,
  pageOnlineInvoiceInServicer,
  pageOnlineInvoiceInTrainingChannel,
  pageOrderInDistributor,
  pageOrderInServicer,
  pageOrderInTrainingChannel,
  pageReceiveAccountInDistribution,
  pageReceiveAccountInServicer,
  pageReceiveAccountInSubProject,
  pageReturnOrderInDistributor,
  pageReturnOrderInServicer,
  pageReturnOrderInTrainingChannel,
  statisticBatchOrderInDistributor,
  statisticBatchOrderInServicer,
  statisticBatchOrderInTrainingChannel,
  statisticBatchReturnOrderInDistributor,
  statisticBatchReturnOrderInServicer,
  statisticBatchReturnOrderInTrainingChannel,
  statisticOnlineInvoiceInServicer,
  statisticOnlineInvoiceInTrainingChannel,
  statisticOrderInDistributor,
  statisticOrderInServicer,
  statisticOrderInTrainingChannel,
  statisticReturnOrderInDistributor,
  statisticReturnOrderInServicer,
  statisticReturnOrderInTrainingChannel,
  temporaryOrderUpdateOrderInfoInSubject
}
