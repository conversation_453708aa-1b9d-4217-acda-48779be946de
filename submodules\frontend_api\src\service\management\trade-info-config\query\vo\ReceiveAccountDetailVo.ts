import { ReceiveAccountConfigResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { AccountTypeEunm } from '@api/service/common/enums/trade-configuration/AccountType'

class ReceiveAccountDetailVo {
  /**
   * 支付方式 1-线上 2-线下
   */
  accountType: AccountTypeEunm = AccountTypeEunm.ONLINE
  /**
   * 收款账号(开户号、商户号、支付宝账号)
   */
  accountNo = ''
  /**
   * 收款账号别名
   */
  accountName = ''
  /**
   * 纳税人识别号id
   */
  taxPayerId = ''
  /**
   * 纳税人名称
   */
  taxPayerName = ''
  /**
   * 退款方式  1-线上 2-线下
   */
  refundWay = -1
  /**
   * 付款扫码引导语
   */
  qrScanPrompt = ''

  from(res: ReceiveAccountConfigResponse) {
    return
  }
}
export default ReceiveAccountDetailVo
