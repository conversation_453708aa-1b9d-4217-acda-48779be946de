/**
 * sku
 */
class SkuVo<T = string> {
  // region properties
  /**
   * 是否隐藏
   */
  hidden = false
  /**
   * 用于有父子级别关系的
   */
  parentId = ''
  /**
   *sku属性值id，类型为string
   */
  skuPropertyValueId: T = '' as unknown as T
  /**
   *sku属性值名称，类型为string
   */
  skuPropertyName = ''
  // endregion
  // region methods

  /**
   * 构造函数
   * @param skuPropertyValueId sku属性值id
   * @param skuPropertyName sku属性值名称
   */
  constructor(skuPropertyValueId?: T, skuPropertyName?: string) {
    if (skuPropertyValueId || (typeof skuPropertyValueId === 'number' && skuPropertyValueId === 0)) {
      this.skuPropertyValueId = skuPropertyValueId
    }
    if (skuPropertyName) {
      this.skuPropertyName = skuPropertyName
    }
  }

  // endregion
}
export default SkuVo
