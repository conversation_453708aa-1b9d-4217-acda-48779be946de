"""独立部署的微服务,K8S服务名:ms-choosecourselearningscene-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	getSchemaName:String
}
type Mutation {
	"""申请选课
		@param studentLearningToken 学员学习凭证
		@return 选课凭证
	"""
	applyChooseCourse(studentLearningToken:String!):ChooseCourseTokenResponse
	"""申请课程一键学习token"""
	applyCourseImmediatelyLearning(studentLearningToken:String!,studentCourseId:String!,quizScore:Double):StudentCourseImmediatelyLearningTokenResponse
	"""申请课程学习
		@param studentLearningToken 学员学习凭证
		@param studentCourseId      学员课程编号
		@return 课程学习凭证
	"""
	applyCourseLearning(studentLearningToken:String!,studentCourseId:String!):StudentCourseLearningTokenResponse
	"""申请课件一键学习token"""
	applyCoursewareImmediatelyLearning(studentLearningToken:String!,studentCourseId:String!,coursewareId:String!):StudentCoursewareImmediatelyLearningTokenResponse
	invalidStudentCourse(studentReLearnToken:String!,studentCourseId:String!):Void
	pushUserCourse(request:PushCompulsoryCourseRequest):Void
	relearnStudentCourse(studentReLearnToken:String!,studentCourseId:String!):Void
	"""操作场景-测试接口"""
	sceneDelete(preCommand:ChooseCourseLearningScenePreRemoveCommand,commitCommand:ChooseCourseLearningSceneCommitRemoveCommand):NeedRecalculateProcessResult
	"""操作场景-测试接口"""
	sceneOperate(command:ChooseCourseLearningScenePreCreateCommand):NeedRecalculateProcessResult
	"""操作场景-测试接口"""
	sceneUpdate(preCommand:ChooseCourseLearningScenePreUpdateCommand,commitCommand:ChooseCourseLearningSceneCommitUpdateCommand):NeedRecalculateProcessResult
}
input LSContext @type(value:"com.fjhb.domain.learningscheme.support.resource.consts.LSContext") {
	lifeCycle:LifeCycle
}
"""选课学习场景提交删除命令
	<AUTHOR>
	@since 2023/7/3
"""
input ChooseCourseLearningSceneCommitRemoveCommand @type(value:"com.fjhb.ms.clscene.choose.course.v1.api.command.ChooseCourseLearningSceneCommitRemoveCommand") {
	"""选课学习场景，配置json体
		@see com.fjhb.ms.clscene.choose.course.v1.api.config.ChooseCourseLearningSceneConfig
	"""
	config:String!
	"""方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String!
	"""平台ID"""
	platformId:String!
	"""平台版本ID"""
	platformVersionId:String!
	"""项目ID"""
	projectId:String!
	"""子项目ID"""
	subProjectId:String!
	"""单位ID"""
	unitId:String!
	"""服务商ID"""
	servicerId:String!
	"""删除人id"""
	deleteUserId:String!
	"""删除时间"""
	deleteTime:DateTime!
	lsContext:LSContext
	currentTxId:String
}
"""选课学习场景提交更新命令
	<AUTHOR>
	@since 2023/7/3
"""
input ChooseCourseLearningSceneCommitUpdateCommand @type(value:"com.fjhb.ms.clscene.choose.course.v1.api.command.ChooseCourseLearningSceneCommitUpdateCommand") {
	"""对应资源的学习方案配置json体
		@see com.fjhb.ms.clscene.choose.course.v1.api.config.ChooseCourseLearningSceneConfig
	"""
	config:String!
	"""方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String!
	"""平台ID"""
	platformId:String!
	"""平台版本ID"""
	platformVersionId:String!
	"""项目ID"""
	projectId:String!
	"""子项目ID"""
	subProjectId:String!
	"""单位ID"""
	unitId:String!
	"""服务商ID"""
	servicerId:String!
	"""更新人id"""
	updateUserId:String!
	"""更新时间"""
	updateTime:DateTime!
	lsContext:LSContext
	currentTxId:String
}
"""选课学习场景预创建命令
	<AUTHOR>
	@since 2023/7/3
"""
input ChooseCourseLearningScenePreCreateCommand @type(value:"com.fjhb.ms.clscene.choose.course.v1.api.command.ChooseCourseLearningScenePreCreateCommand") {
	"""选课学习场景，配置json体
		@see com.fjhb.ms.clscene.choose.course.v1.api.config.ChooseCourseLearningSceneConfig
	"""
	config:String!
	"""方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String!
	"""平台ID"""
	platformId:String!
	"""平台版本ID"""
	platformVersionId:String!
	"""项目ID"""
	projectId:String!
	"""子项目ID"""
	subProjectId:String!
	"""单位ID"""
	unitId:String!
	"""服务商ID"""
	servicerId:String!
	"""创建人id"""
	createUserId:String!
	"""预创建时间"""
	preCreateTime:DateTime!
	lsContext:LSContext
	currentTxId:String
}
"""选课学习场景预删除命令
	<AUTHOR>
	@since 2023/7/3
"""
input ChooseCourseLearningScenePreRemoveCommand @type(value:"com.fjhb.ms.clscene.choose.course.v1.api.command.ChooseCourseLearningScenePreRemoveCommand") {
	"""选课学习场景，配置json体
		@see com.fjhb.ms.clscene.choose.course.v1.api.config.ChooseCourseLearningSceneConfig
	"""
	config:String!
	"""方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String!
	"""平台ID"""
	platformId:String!
	"""平台版本ID"""
	platformVersionId:String!
	"""项目ID"""
	projectId:String!
	"""子项目ID"""
	subProjectId:String!
	"""单位ID"""
	unitId:String!
	"""服务商ID"""
	servicerId:String!
	"""删除人id"""
	deleteUserId:String!
	"""预删除时间"""
	preDeleteTime:DateTime!
	lsContext:LSContext
	currentTxId:String
}
"""选课学习场景预更新命令
	<AUTHOR>
	@since 2023/7/3
"""
input ChooseCourseLearningScenePreUpdateCommand @type(value:"com.fjhb.ms.clscene.choose.course.v1.api.command.ChooseCourseLearningScenePreUpdateCommand") {
	"""对应资源的学习方案配置json体
		@see com.fjhb.ms.clscene.choose.course.v1.api.config.ChooseCourseLearningSceneConfig
	"""
	config:String!
	"""方案id"""
	schemeId:String!
	"""学习方式id"""
	learningId:String!
	"""平台ID"""
	platformId:String!
	"""平台版本ID"""
	platformVersionId:String!
	"""项目ID"""
	projectId:String!
	"""子项目ID"""
	subProjectId:String!
	"""单位ID"""
	unitId:String!
	"""服务商ID"""
	servicerId:String!
	"""更新人id"""
	updateUserId:String!
	"""预更新时间"""
	preUpdateTime:DateTime!
	lsContext:LSContext
	currentTxId:String
}
"""PushCompulsoryCourseRequest
	<AUTHOR>
	@since 2023/9/28
"""
input PushCompulsoryCourseRequest @type(value:"com.fjhb.ms.clscene.choose.v1.kernel.gateway.graphql.request.PushCompulsoryCourseRequest") {
	"""学员编号"""
	studentNo:String
	"""用户id"""
	userId:String
	"""场景id"""
	sceneId:String
	"""选课数据范围"""
	ranges:[ChooseCourseRange]
	"""推送时间"""
	pushDate:DateTime
	"""参训资格编号"""
	qualificationId:String
	microContext:String
}
"""选课范围"""
input ChooseCourseRange @type(value:"com.fjhb.ms.clscene.choose.v1.kernel.gateway.graphql.request.PushCompulsoryCourseRequest$ChooseCourseRange") {
	"""数据范围key"""
	key:String
	"""数据范围value"""
	value:String
}
enum LifeCycle @type(value:"com.fjhb.domain.learningscheme.support.resource.consts.LSContext$LifeCycle") {
	create
	update
	delete
}
type NeedRecalculateProcessResult @type(value:"com.fjhb.domain.learningscheme.support.resource.response.NeedRecalculateProcessResult") {
	learningId:String
	learningResourceId:String
	recalculateRequests:[RecalculateRequest]
	code:String
	message:String
	configJson:String
	resourceId:String
	asyncExecute:Boolean!
	resultType:Int!
}
type RecalculateRequest @type(value:"com.fjhb.domain.learningscheme.support.resource.response.RecalculateRequest") {
	dataType:String
	priority:Int!
	recalculate:Boolean
	updatedCode:String
	metadata:Map
}
"""选课凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type ChooseCourseTokenResponse @type(value:"com.fjhb.ms.clscene.choose.v1.kernel.gateway.graphql.response.ChooseCourseTokenResponse") {
	"""申请结果"""
	applyResult:TokenResponse
	"""选课凭证"""
	token:String
}
"""课程一键学习凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type StudentCourseImmediatelyLearningTokenResponse @type(value:"com.fjhb.ms.clscene.choose.v1.kernel.gateway.graphql.response.StudentCourseImmediatelyLearningTokenResponse") {
	"""申请结果
		200-成功
		500-内部异常
		501001-学员课程不存在
		501002-学员课程已失效
		501003-课后测验分数不可为空
	"""
	applyResult:TokenResponse
	"""课程学习凭证"""
	token:String
}
"""课程学习凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type StudentCourseLearningTokenResponse @type(value:"com.fjhb.ms.clscene.choose.v1.kernel.gateway.graphql.response.StudentCourseLearningTokenResponse") {
	"""申请结果"""
	applyResult:TokenResponse
	"""课程学习凭证"""
	token:String
	"""applyResult.code==L90001 有这个值
		已学习(时长/课时)
	"""
	timeLength:Double
	"""applyResult.code==L90001 有这个值
		学习规则类型1=时长(秒) 2=课时
	"""
	ruleType:Int
}
"""课件一键学习凭证响应
	<AUTHOR>
	@since 2022/1/20
"""
type StudentCoursewareImmediatelyLearningTokenResponse @type(value:"com.fjhb.ms.clscene.choose.v1.kernel.gateway.graphql.response.StudentCoursewareImmediatelyLearningTokenResponse") {
	"""申请结果
		200-成功
		500-内部异常
		501001-学员课程不存在
		501002-学员课程已失效
	"""
	applyResult:TokenResponse
	"""课程学习凭证"""
	token:String
}
"""凭证响应基类
	<AUTHOR>
	@since 2022/1/20
"""
type TokenResponse @type(value:"com.fjhb.ms.clscene.choose.v1.kernel.gateway.graphql.response.TokenResponse") {
	"""代码：
		200-成功
	"""
	code:String
	"""信息"""
	message:String
}

scalar List
