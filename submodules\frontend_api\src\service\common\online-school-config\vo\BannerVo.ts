import { BannerInfo } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import { TrainingInstitutionBannerSaveRequest } from '@api/ms-gateway/ms-servicer-v1'

/**
 * @description 轮播图
 */
class BannerVo {
  /**
   * id
   */
  id: string
  /**
   * 轮播图名称
   */
  name: string
  /**
   * 轮播图类型
   @see com.fjhb.btpx.platform.service.rollpicture.RollPictureTypeEnum
   */
  type: string
  /**
   * 轮播图地址
   */
  url: string
  /**
   * 链接地址
   */
  link: string
  /**
   * 轮播图描述
   */
  description: string
  /**
   * 轮播图状态 0-停用 1-启用
   */
  status: number
  /**
   * 轮播图排序
   */
  sort?: number

  /**
   * 创建时间
   */
  createdTime: string

  static from(response: BannerInfo) {
    const banner = new BannerVo()
    banner.id = response.id
    banner.name = response.name
    banner.url = response.path
    banner.link = response.link
    banner.sort = response.sort
    banner.createdTime = response.createdTime
    return banner
  }

  static to(data: BannerVo) {
    const request = new TrainingInstitutionBannerSaveRequest()
    request.bannerLink = data.link
    request.bannerPath = data.url
    request.sort = data.sort
    return request
  }
}

export default BannerVo
