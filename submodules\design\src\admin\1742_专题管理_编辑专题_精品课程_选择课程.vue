<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--选择课程-->
        <el-button @click="dialog4 = true" type="primary" class="f-mr20 f-mb20">选择课程</el-button>
        <el-drawer
          title="选择课程"
          :visible.sync="dialog4"
          :direction="direction"
          size="1000px"
          :append-to-body="true"
          custom-class="m-drawer m-table-auto"
        >
          <div class="drawer-bd">
            <el-row :gutter="16" class="m-query f-mt10">
              <el-form :inline="true" label-width="auto">
                <el-col :span="10">
                  <el-form-item label="分类名称：">
                    <el-cascader clearable :options="cascader" placeholder="请选择分类" />
                  </el-form-item>
                </el-col>
                <el-col :span="10">
                  <el-form-item label="课程名称">
                    <el-input v-model="input" clearable placeholder="请输入课程名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="4">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table stripe :data="tableData" class="m-table f-mt10">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="课程名称" min-width="300">
                <template slot-scope="scope">
                  <div v-if="scope.$index === 0">
                    <i class="is-tag f-mr5"></i>
                    课程包名称课程包名称课程包名称课程包名称
                  </div>
                  <div v-else-if="scope.$index === 1">
                    <i class="is-tag f-mr5"></i>
                    课程包名称课程包名称课程包名称课程包名称
                  </div>
                  <div v-else>课程包名称课程包名称课程包名称课程包名称</div>
                </template>
              </el-table-column>
              <el-table-column label="学时" min-width="120" align="center">
                <template>10</template>
              </el-table-column>
              <el-table-column label="查看" width="80" align="center" fixed="right">
                <template>
                  <el-button type="text" size="mini">详情</el-button>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center" fixed="right">
                <template>
                  <el-checkbox v-model="checked">选择</el-checkbox>
                </template>
              </el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </div>
          <div class="m-btn-bar drawer-ft">
            <el-button>取消</el-button>
            <el-badge :value="2" class="f-ml10">
              <el-button type="primary">确定</el-button>
            </el-badge>
          </div>
        </el-drawer>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        num: 100,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        checked: false,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        dialog2: false,
        dialog3: false,
        dialog4: true,
        dialog5: false,
        dialog6: false,
        dialog7: false,
        dialog8: false,
        dialog9: false,
        dialog10: false,
        dialog11: false,
        dialog12: false,
        dialog13: false,
        dialog14: false,
        dialog15: false,
        dialog16: false,
        dialog17: false,
        dialog18: false,
        dialog19: false,
        dialog20: false,
        dialog21: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      open3() {
        this.$message({
          message: '本次修改内容如涉及到考核重算，重算任务于程序后台自动执行，即将自动为您跳转到方案管理列表页。',
          type: 'warning',
          duration: 5000,
          customClass: 'm-message'
        })
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
