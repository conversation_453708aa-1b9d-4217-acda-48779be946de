import Vue from 'vue'
import App from './App.vue'
import ElementUi from 'element-ui'
import 'element-ui/packages/theme-chalk/lib/index.css'
import axios, { AxiosRequestConfig } from 'axios'
import router from './router'

Vue.use(ElementUi)
Vue.prototype.$http = axios
axios.interceptors.request.use((requestConfig: AxiosRequestConfig) => {
  requestConfig.headers['App-Authentication'] = `Basic ${process.env.VUE_APP_KEY}`
  return requestConfig
})
new Vue({
  components: {
    App
  },
  router,
  el: '#app',
  render() {
    return (
      <App />
    )
  }
})
