<route-meta>
  {
  "isMenu": true,
  "title": "个人报名发票",
  "sort": 1
  }
</route-meta>
<template>
  <jxgx-invoice></jxgx-invoice>
</template>
<script lang="ts">
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import ElectronicInvoiceAuto from '@hbfe/jxjy-admin-trade/src/diff/jxgx/invoice/personal/components/electronic-invoice-auto.vue'
  import Invoice from '@hbfe/jxjy-admin-trade/src/invoice/personal/index.vue'
  import ElectronicInvoiceOffline from '@hbfe/jxjy-admin-trade/src/diff/jxgx/invoice/personal/components/electronic-invoice-offline.vue'
  import SpecialInvoice from '@hbfe/jxjy-admin-trade/src/diff/jxgx/invoice/personal/components/special-invoice.vue'
  import InvoiceDistribution from '@hbfe/jxjy-admin-trade/src/diff/jxgx/invoice/personal/components/invoice-distribution.vue'
  import ElectronicSpecialInvoice from '@hbfe/jxjy-admin-trade/src/diff/jxgx/invoice/personal/components/electronic-special-invoice.vue'

  @Component({
    components: {
      ElectronicInvoiceAuto,
      ElectronicInvoiceOffline,
      SpecialInvoice,
      InvoiceDistribution,
      ElectronicSpecialInvoice
    }
  })
  class JxgxInvoice extends Invoice {}
  @Component({
    components: {
      JxgxInvoice
    }
  })
  export default class extends Vue {}
</script>
