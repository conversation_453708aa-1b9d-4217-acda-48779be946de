import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 问卷前置条件类型枚举
 * pass_online_course 完成线上课程考核才可作答
 * pass_issue_assess 完成培训期别考核才可作答
 * none 无
 */
export enum QuestionnairePreconditionTypeEnum {
  pass_online_course = 'pass_online_course',
  pass_issue_assess = 'pass_issue_assess',
  none = 'none'
}

/**
 * @description 问卷前置条件类型
 */
class QuestionnairePreconditionType extends AbstractEnum<QuestionnairePreconditionTypeEnum> {
  static enum = QuestionnairePreconditionTypeEnum

  constructor(status?: QuestionnairePreconditionTypeEnum) {
    super()
    this.current = status
    this.map.set(QuestionnairePreconditionTypeEnum.pass_online_course, '完成线上课程考核')
    this.map.set(QuestionnairePreconditionTypeEnum.pass_issue_assess, '完成培训期别课程')
    this.map.set(QuestionnairePreconditionTypeEnum.none, '无')
  }
}

export default new QuestionnairePreconditionType()
