"""独立部署的微服务,K8S服务名:ms-account-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @optionalLogin on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION | FIELD_DEFINITION | INPUT_FIELD_DEFINITION
"""用于表示Page的泛型返回类型,for对应的是Page返回的真实类型"""
directive @page(for:String!) on FIELD_DEFINITION
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""导出全部学员导入数据
		@param mainTaskId 任务id
		@return 学员导入数据
	"""
	exportAllStudentResult(mainTaskId:String!):ExportStudentImportResultResponse
	"""导出失败学员导入数据
		@param mainTaskId 任务id
		@return 失败学员导入数据
	"""
	exportErrorStudentResult(mainTaskId:String!):ExportStudentImportResultResponse
	"""分页查询学员导入任务数据
		@param page    分页信息
		@param request 查询条件
		@return 学员导入任务数据
	"""
	pageStudentImportTask(page:Page,request:PageStudentImportTaskRequest):StudentImportTaskResponsePage @page(for:"StudentImportTaskResponse")
}
type Mutation {
	"""批量导入学员
		@param request 批量导入学员请求信息
	"""
	batchImportStudent(request:BatchImportStudentRequest):Void
	"""绑定新手机号(学员端)
		@param request {输入的短信验证码、输入的新手机号、token(短信验证码校验成功生成的token)}
	"""
	bindPhone(request:BindPhoneRequest):Void
	"""绑定新手机号(管理端)
		@param request {输入的短信验证码、输入的新手机号、输入的图片验证码、token(图片验证码校验成功生成的token)}
	"""
	bindPhoneByAdmin(request:BindPhoneByAdminRequest):Void
	"""变更当前登录帐户的密码
		@param changeInfo 更新信息
	"""
	changePasswordByCurrent(changeInfo:CurrentAccountChangePasswordRequest):Void
	"""集体报名管理员身份认证
		@param request {真实姓名,登录账号(手机号码)}
		@return {身份认证结果代码和消息,accountId,认证ID,token(确认身份成功返回的token)}
	"""
	collectiveRegistrationAdminIdentify(request:IdentifyRequest):IdentifyResultResponse @optionalLogin
	createAdmin(request:CreateAdminRequest):Void @optionalLogin
	"""禁用当前帐户微信小程序通知"""
	disableNoticeForWebChatApplet:Void
	"""启用当前帐户微信小程序通知"""
	enableNoticeForWebChatApplet:Void
	"""冻结帐户
		@param accountId 【必填】帐户ID
	"""
	freezeAccount(accountId:String):Void
	"""立即重置密码
		@param resetInfo 重置密码信息
	"""
	immediateResetPassword(resetInfo:ImmediateResetPasswordRequest):Void
	"""加载所需基础验证数据
		@return {图片验证码,token}
	"""
	loadBasicValidationData:BasicValidationDataResponse @optionalLogin
	"""加载所需基础验证数据(已登录)
		@return {当前账户已绑定的数据脱敏的手机号,图片验证码,token}
	"""
	loadBasicValidationDataWithBindPhone:BasicValidationDataResponse
	"""加载忘记密码所需基础验证数据
		@param request {accountId,token(确认身份成功生成的token)}
		@return {当前账户已绑定的数据脱敏的手机号,图片验证码,token}
	"""
	loadRetrievePasswordBasicValidationData(request:LoadRetrievePasswordRequest):BasicValidationDataResponse @optionalLogin
	"""登录并绑定平台账号【微信】
		@param request {账户ID(证件号或手机号),密码,输入的图片验证码,token(图片验证码校验成功生成的),单点登录Token}
	"""
	loginAndBindOpenPlatform(request:BindPlatformAccountRequest):Void @optionalLogin
	"""平台管理端管理员身份认证
		@param request {真实姓名,登录账号(非手机号码)}
		@return {身份认证结果代码和消息,accountId,认证ID,token(确认身份成功返回的token)}
	"""
	platformManagementAdminIdentify(request:IdentifyRequest):IdentifyResultResponse @optionalLogin
	"""创建地区管理员信息
		@param createInfo 【必填】地区管理员信息
	"""
	registerAreaAdmin(createInfo:CreateAreaAdminCreateRequest):String
	"""注册集体报名账号[企业账户]
		@param createInfo {真实姓名,手机号码,输入的短信验证码,输入的图片验证码,登录密码,token(图片验证码校验成功生成的token)}
	"""
	registerCollective(createInfo:CreateCollectiveRequest):Void @optionalLogin
	"""注册学员账号
		@param createInfo 注册学员账号信息 {输入的短信验证码、输入手机号、输入的图片验证码、token(图片验证码校验成功生成的token)}
	"""
	registerStudent(createInfo:CreateStudentRequest):Void @optionalLogin
	"""创建管理员信息（企业个人）
		@param createInfo 【必填】管理员创建信息
	"""
	registerSubAdmin(createInfo:CreateSubAdminRequest):String
	"""重新设置账户的帐号认证方式的帐号"""
	resetAccountPwdAuthIdentity(resetInfo:AccountPwdAuthIdentityResetInfo):Void
	"""忘记密码-重置密码
		@param request {账户ID,认证ID,新密码,token(短信验证码校验通过生成的token)}
	"""
	resetPasswordWithToken(request:ResetPasswordRequest):Void @optionalLogin
	"""恢复冻结的帐户
		@param accountId 【必填】帐户ID
	"""
	resumeAccount(accountId:String):Void
	"""发送短信验证码(注册相关业务使用)
		@param request {账户ID或手机号,输入的图片验证码,校验图片验证码成功生成的token}
		@return {code,message}
	"""
	sendSmsCodeByRegister(request:SendSmsCodeRequest):SendSmsCodeResultResponse @optionalLogin
	"""发送短信验证码(更换手机相关业务使用)
		@param request {账户ID或手机号,输入的图片验证码,校验图片验证码成功生成的token}
		@return {code,message}
	"""
	sendSmsCodeByUpdatePhone(request:SendSmsCodeRequest):SendSmsCodeResultResponse @optionalLogin
	"""发送短信验证码(更换手机相关业务使用)
		@param request {账户ID或手机号,校验短信验证码成功生成的token}
		@return {code,message}
	"""
	sendSmsCodeByUpdatePhoneWithSmsValidToken(request:SendSmsCodeBasicRequest):SendSmsCodeResultResponse @optionalLogin
	"""发送短信验证码(修改密码相关业务使用)
		@param request {账户ID或手机号,输入的图片验证码,校验图片验证码成功生成的token}
		@return {code,message}
	"""
	sendSmsCodeByUpdatePwd(request:SendSmsCodeRequest):SendSmsCodeResultResponse @optionalLogin
	"""学员身份认证
		@param request {真实姓名,登录账号(证件号码/手机号码)}
		@return {身份认证结果代码和消息,accountId,认证ID,token(确认身份成功返回的token)}
	"""
	studentIdentify(request:IdentifyRequest):IdentifyResultResponse @optionalLogin
	"""解绑账户第三方绑定信息
		@param accountId
	"""
	unbind(accountId:String):Void
	"""解绑账户绑定的微信开放平台信息
		@param accountId 账户id
	"""
	unbindWechatOpenPlatform(accountId:String):Void
	"""修改管理员信息
		@param updateInfo 【必填】要更新的用户信息
	"""
	updateAdmin(updateInfo:AdministratorUpdateRequest):Void
	"""修改地区管理员信息
		@param updateInfo 【必填】要更新地区管理员信息
	"""
	updateAreaAdmin(updateInfo:AreaAdministratorUpdateRequest):Void
	"""更新学员账号信息（学员端）
		@param updateInfo 更新学员账号信息
	"""
	updateStudent(updateInfo:UpdateStudentRequest):Void
	"""更新学员账号信息（管理端）
		@param updateInfo 更新学员账号信息
	"""
	updateStudentSystem(updateInfo:UpdateStudentSystemRequest):Void
	"""更新用户信息
		@param updateInfo 【必填】要更新的用户信息
	"""
	updateUser(updateInfo:UserUpdateRequest):Void
	"""更新当前登录用户信息
		@param updateInfo 【必填】要更新的用户信息
	"""
	updateUserByCurrent(updateInfo:CurrentUserUpdateRequest):Void
	"""校验图片验证码
		@param request {captcha 输入的验证码,token (加载基础验证数据中生成的token)}
		@return {校验结果响应码,响应消息,token,refreshCaptcha刷新的图片验证码}
	"""
	validCaptcha(request:CaptchaValidRequest):CaptchaValidResultResponse @optionalLogin
	"""校验短信验证码(单独流程使用)
		1.校验Token(校验图片验证码成功生成的token)
		1.1 Token校验通过
		2.校验短信验证码
		2.1 短信验证码校验通过
		整体校验通过，返回成功code，生成短信校验成功的Token
		2.2 短信验证码校验失败(错误或过期)
		返回失败code和message，无Token返回
		1.2 Token校验失败(可能是Token过期或输入的图片验证码被修改)
		返回失败code和message，无Token返回
		@param request {账户ID,手机号,输入的图片验证码,短信验证码,String token(校验图片验证码成功生成的token)}
		@return {校验结果响应code,响应消息,token(短信校验成功生成)}
	"""
	validSmsCode(request:SmsCodeValidRequest):SmsCodeValidResultResponse @optionalLogin
}
input Page @type(value:"com.fjhb.commons.dao.page.Page") {
	pageNo:Int
	pageSize:Int
}
"""重新设置账户的帐号密码认证方式认证标识信息
	<AUTHOR>
"""
input AccountPwdAuthIdentityResetInfo @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.AccountPwdAuthIdentityResetInfo") {
	"""【必填】账户ID"""
	accountId:String
	"""【必填】认证标识类型 1用户名，2手机，3身份证，4电子邮箱
		@see com.fjhb.domain.basicdata.api.account.consts.AuthenticationIdentityTypes
	"""
	identityType:Int!
	"""【必填】认证标识：帐号"""
	identity:String
}
"""<AUTHOR>  2022/1/25 11:02"""
input AdministratorUpdateRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.AdministratorUpdateRequest") {
	"""账户id"""
	id:String
	"""姓名"""
	name:String
	"""性别"""
	gender:Int
	"""手机"""
	phone:String
	"""邮箱"""
	email:String
	"""启用状态"""
	status:Int!
	"""登录账户"""
	identity:String
	"""角色id集合"""
	roleIds:[String]
}
"""<AUTHOR>  2022/1/25 11:02"""
input AreaAdministratorUpdateRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.AreaAdministratorUpdateRequest") {
	"""用户id"""
	id:String
	"""姓名"""
	name:String
	"""手机"""
	phone:String
	"""管辖地区"""
	managementAreas:[String]
}
"""批量导入学员请求信息
	@author: zhengp 2022/2/22 9:08
"""
input BatchImportStudentRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.BatchImportStudentRequest") {
	"""文件路径"""
	filePath:String
	"""密码"""
	password:String
}
"""换绑手机号请求(管理端)
	<AUTHOR>
"""
input BindPhoneByAdminRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.BindPhoneByAdminRequest") {
	"""【必填】用户输入的新手机号"""
	phone:String
	"""【必填】用户输入的图片验证码"""
	captcha:String!
	"""【必填】用户输入的短信验证码"""
	smsCode:String!
	"""【必填】token(图片验证码校验通过生成)"""
	token:String!
}
"""换绑手机号请求(学员端)
	<AUTHOR>
"""
input BindPhoneRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.BindPhoneRequest") {
	"""【必填】用户输入的新手机号"""
	phone:String
	"""【必填】用户输入的短信验证码"""
	smsCode:String!
	"""【必填】token(短信验证码校验通过生成)"""
	token:String!
}
"""绑定平台账号【微信】的请求
	<AUTHOR>
"""
input BindPlatformAccountRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.BindPlatformAccountRequest") {
	"""【必填】认证标识(证件号或手机号)"""
	identity:String!
	"""【必填】密码"""
	password:String!
	"""【必填】用户输入的图片验证码"""
	captcha:String!
	"""【必填】token(图片验证码校验成功生成的)"""
	token:String!
	"""【必填】单点登录Token"""
	loginToken:String!
}
"""base64验证码校验请求
	<AUTHOR>
"""
input CaptchaValidRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CaptchaValidRequest") {
	"""【必填】用户输入的验证码"""
	captcha:String!
	"""【必填】token"""
	token:String!
}
"""<AUTHOR> 2022/9/9 9:06"""
input CreateAdminRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CreateAdminRequest") {
	"""角色列表"""
	roleIds:[String]
}
"""<AUTHOR>  2022/1/25 11:02"""
input CreateAreaAdminCreateRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CreateAreaAdminCreateRequest") {
	"""姓名"""
	name:String
	"""手机"""
	phone:String
	"""登录账户"""
	identity:String
	"""管辖地区"""
	managementAreas:[String]
}
"""@author: zhengp 2022/1/25 9:59"""
input CreateCollectiveRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CreateCollectiveRequest") {
	"""【必填】用户名称"""
	name:String!
	"""【必填】手机号"""
	phone:String!
	"""【必填】密码"""
	password:String!
	"""【必填】用户输入的图片验证码"""
	captcha:String!
	"""【必填】短信验证码"""
	smsCode:String!
	"""【必填】token(图片校验成功生成的token)"""
	token:String!
}
"""@author: zhengp 2022/1/25 9:59"""
input CreateStudentRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CreateStudentRequest") {
	"""【必填】证件号码"""
	idCard:String!
	"""【必填】密码"""
	password:String!
	"""【必填】短信验证码"""
	smsCode:String!
	"""【必填】用户输入的图片验证码"""
	captcha:String!
	"""【必填】图片验证码校验通过生成的token"""
	token:String!
	"""单点登录Token"""
	loginToken:String
	"""【必填】用户名称"""
	name:String
	"""【必填】手机号"""
	phone:String
	"""【必填】所属区域"""
	area:String
	"""工作单位"""
	companyName:String
	"""[必填]加密值"""
	encrypt:String
	"""行业信息"""
	userIndustryInfos:[CreateUserIndustryRequest]
}
"""<AUTHOR>  2022/1/25 11:02"""
input CreateSubAdminRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CreateSubAdminRequest") {
	"""姓名"""
	name:String
	"""性别  女: 0 男:1"""
	gender:Int
	"""手机"""
	phone:String
	"""邮箱"""
	email:String
	"""启用状态  正常: 1 禁用: 2"""
	status:Int!
	"""登录账户"""
	identity:String
	"""密码"""
	password:String
	"""角色id集合"""
	roleIds:[String]
}
"""@author: zhengp 2022/1/24 15:27"""
input CreateUserIndustryRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CreateUserIndustryRequest") {
	"""所属行业"""
	industryId:String
	"""一级专业类别"""
	firstProfessionalCategory:String
	"""二级专业类别"""
	secondProfessionalCategory:String
	"""职称等级"""
	professionalQualification:String
	"""证书信息"""
	certificateInfos:[CertificateInfo]
}
input CertificateAttachment @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CreateUserIndustryRequest$CertificateAttachment") {
	certificateUrl:String
	name:String
}
input CertificateInfo @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CreateUserIndustryRequest$CertificateInfo") {
	"""更新时必填"""
	certificateId:String
	"""证书编号"""
	certificateNo:String
	"""证书类别"""
	certificateCategory:String
	"""注册专业"""
	registerProfessional:String
	"""发证日期（起）"""
	releaseStartTime:DateTime
	"""证书有效期（止）"""
	certificateEndTime:DateTime
	"""证书附件"""
	certificateAttachments:[CertificateAttachment]
}
"""变更当前登录帐户的密码信息
	<AUTHOR>
"""
input CurrentAccountChangePasswordRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CurrentAccountChangePasswordRequest") {
	"""【必填】原始密码"""
	originalPassword:String
	"""【必填】新密码"""
	newPassword:String
}
"""当前登录用户的用户修改信息
	<AUTHOR>
"""
input CurrentUserUpdateRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.CurrentUserUpdateRequest") {
	"""身份证号"""
	idCard:String
	"""用户名称"""
	name:String
	"""用户昵称"""
	nickName:String
	"""手机号"""
	phone:String
	"""头像地址"""
	photo:String
	"""所属区域"""
	area:String
	"""联系地址"""
	address:String
	"""性别
		@see com.fjhb.domain.basicdata.api.user.consts.Genders
	"""
	gender:Int
	"""工作单位"""
	companyName:String
	"""所属人群"""
	peoples:String
	"""电子邮箱"""
	email:String
}
"""身份认证请求
	<AUTHOR>
"""
input IdentifyRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.IdentifyRequest") {
	"""【必填】姓名"""
	realName:String!
	"""【必填】登录账号"""
	identity:String!
}
"""立即重置密码
	<AUTHOR>
"""
input ImmediateResetPasswordRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.ImmediateResetPasswordRequest") {
	"""【必填】帐户ID"""
	accountId:String
	"""【必填】重置后的密码"""
	password:String
}
"""加载忘记密码所需基础验证数据的请求
	<AUTHOR>
"""
input LoadRetrievePasswordRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.LoadRetrievePasswordRequest") {
	"""账户ID"""
	accountId:String!
	"""token"""
	token:String!
}
"""分页查询学员导入任务数据请求信息
	@author: zhengp 2022/5/12 15:39
"""
input PageStudentImportTaskRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.PageStudentImportTaskRequest") {
	"""任务名称"""
	taskName:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""执行时间（起始）"""
	executeStartTime:DateTime
	"""执行时间（终止）"""
	executeEndTime:DateTime
}
"""重置密码请求
	<AUTHOR>
"""
input ResetPasswordRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.ResetPasswordRequest") {
	"""账户ID"""
	accountId:String!
	"""【必填】认证ID"""
	authId:String!
	"""【必填】新密码"""
	password:String!
	"""【必填】token"""
	token:String!
}
"""发送短信验证码基础请求
	<AUTHOR>
"""
input SendSmsCodeBasicRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.SendSmsCodeBasicRequest") {
	"""账户ID"""
	accountId:String
	"""手机号码(完整手机号，若无则提供账户ID)"""
	phone:String
	"""【必填】token"""
	token:String!
}
"""发送短信验证码请求
	<AUTHOR>
"""
input SendSmsCodeRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.SendSmsCodeRequest") {
	"""【必填】用户输入的图片验证码"""
	captcha:String!
	"""账户ID"""
	accountId:String
	"""手机号码(完整手机号，若无则提供账户ID)"""
	phone:String
	"""【必填】token"""
	token:String!
}
"""短信验证码校验请求
	<AUTHOR>
"""
input SmsCodeValidRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.SmsCodeValidRequest") {
	"""账户ID"""
	accountId:String
	"""手机号码(完整手机号，若无则提供账户ID)"""
	phone:String
	"""【必填】用户输入的图片验证码"""
	captcha:String!
	"""【必填】用户输入的短信验证码"""
	smsCode:String!
	"""【必填】token"""
	token:String!
}
"""@author: zhengp 2022/1/25 9:59"""
input UpdateStudentRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.UpdateStudentRequest") {
	"""短信验证码"""
	smsCode:String
	"""用户输入的图片验证码"""
	captcha:String
	"""图片验证码校验通过生成的token"""
	token:String
	"""【必填】默认为普通更新false，强制更新true则会携带短信验证码和token"""
	forcedUpdate:Boolean
	"""【必填】用户名称"""
	name:String
	"""【必填】手机号"""
	phone:String
	"""【必填】所属区域"""
	area:String
	"""工作单位"""
	companyName:String
	"""[必填]加密值"""
	encrypt:String
	"""行业信息"""
	userIndustryInfos:[CreateUserIndustryRequest]
}
"""@author: zhengp 2022/1/25 9:59"""
input UpdateStudentSystemRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.UpdateStudentSystemRequest") {
	"""【必填】账户id"""
	accountId:String
	"""【必填】证件号码"""
	idCard:String
	"""性别
		@see com.fjhb.domain.basicdata.api.user.consts.Genders
	"""
	gender:Int!
	"""【必填】用户名称"""
	name:String
	"""【必填】手机号"""
	phone:String
	"""【必填】所属区域"""
	area:String
	"""工作单位"""
	companyName:String
	"""[必填]加密值"""
	encrypt:String
	"""行业信息"""
	userIndustryInfos:[CreateUserIndustryRequest]
}
"""用户更新信息，不设置或者设置null表示字段不更新
	<AUTHOR>
"""
input UserUpdateRequest @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.request.UserUpdateRequest") {
	"""【必填】用户ID"""
	id:String
	"""身份证号"""
	idCard:String
	"""用户名称"""
	name:String
	"""用户昵称"""
	nickName:String
	"""手机号"""
	phone:String
	"""头像地址"""
	photo:String
	"""所属区域"""
	area:String
	"""联系地址"""
	address:String
	"""性别
		@see com.fjhb.domain.basicdata.api.user.consts.Genders
	"""
	gender:Int
	"""工作单位"""
	companyName:String
	"""所属人群"""
	peoples:String
	"""电子邮箱"""
	email:String
}
"""基础验证数据响应(用于返回当前账号已绑定的手机号、图片验证码等)
	<AUTHOR>
"""
type BasicValidationDataResponse @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.response.BasicValidationDataResponse") {
	"""当前账户已绑定的数据脱敏的手机号"""
	boundDesensitizationPhone:String
	"""图片验证码"""
	captcha:String
	"""token"""
	token:String
}
"""图片验证码校验结果响应
	<AUTHOR>
"""
type CaptchaValidResultResponse @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.response.CaptchaValidResultResponse") {
	"""token"""
	token:String
	"""新的验证码"""
	refreshCaptcha:String
	"""默认200成功
		其他为异常：
		40001:手机号已被使用
		40002:未绑定手机号码
		40003:帐号密码不匹配
		40004:已绑定其他微信帐号
		40005:姓名与帐号不匹配
		40006:身份认证失败
		40007:账户被禁用
		41000:图片验证码错误
		41001:图片验证码已过期
		41002:图片验证码校验未通过
		42000:短信验证码校验未通过
		42001:短信验证码已过期
		42002:短信验证码错误
		42003:校验短信验证码
		42004:校验短信验证码，输入的短信验证码不得为空
		43000:不支持的Token类型
		43001:Token已过期
		43002:Token解析异常
		43003:Token生成失败
		43004:单点登录Token不允许为空
		44000:获取用户微信信息异常
		44001:当前账户未绑定微信开放平台
		44002:当前账户未绑定微信开放平台，但存在微信开放平台类型的认证绑定信息，请联系管理员检查绑定信息的一致性
	"""
	code:String
	"""携带的消息"""
	msg:String
}
"""获取导入学员信息执行结果返回信息
	@author: zhengp 2022/5/9 14:52
"""
type ExportStudentImportResultResponse @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.response.ExportStudentImportResultResponse") {
	"""导入学员信息执行结果文件地址"""
	fileUrl:String
}
"""身份认证结果响应
	<AUTHOR>
"""
type IdentifyResultResponse @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.response.IdentifyResultResponse") {
	"""账户ID(身份认证通过才会返回)"""
	accountId:String
	"""认证ID(身份认证通过才会返回)"""
	authId:String
	"""token(身份认证通过才会生成)"""
	token:String
	"""默认200成功
		其他为异常：
		40001:手机号已被使用
		40002:未绑定手机号码
		40003:帐号密码不匹配
		40004:已绑定其他微信帐号
		40005:姓名与帐号不匹配
		40006:身份认证失败
		40007:账户被禁用
		41000:图片验证码错误
		41001:图片验证码已过期
		41002:图片验证码校验未通过
		42000:短信验证码校验未通过
		42001:短信验证码已过期
		42002:短信验证码错误
		42003:校验短信验证码
		42004:校验短信验证码，输入的短信验证码不得为空
		43000:不支持的Token类型
		43001:Token已过期
		43002:Token解析异常
		43003:Token生成失败
		43004:单点登录Token不允许为空
		44000:获取用户微信信息异常
		44001:当前账户未绑定微信开放平台
		44002:当前账户未绑定微信开放平台，但存在微信开放平台类型的认证绑定信息，请联系管理员检查绑定信息的一致性
	"""
	code:String
	"""携带的消息"""
	msg:String
}
"""发送短信验证码结果响应
	<AUTHOR>
"""
type SendSmsCodeResultResponse @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.response.SendSmsCodeResultResponse") {
	"""默认200成功
		其他为异常：
		40001:手机号已被使用
		40002:未绑定手机号码
		40003:帐号密码不匹配
		40004:已绑定其他微信帐号
		40005:姓名与帐号不匹配
		40006:身份认证失败
		40007:账户被禁用
		41000:图片验证码错误
		41001:图片验证码已过期
		41002:图片验证码校验未通过
		42000:短信验证码校验未通过
		42001:短信验证码已过期
		42002:短信验证码错误
		42003:校验短信验证码
		42004:校验短信验证码，输入的短信验证码不得为空
		43000:不支持的Token类型
		43001:Token已过期
		43002:Token解析异常
		43003:Token生成失败
		43004:单点登录Token不允许为空
		44000:获取用户微信信息异常
		44001:当前账户未绑定微信开放平台
		44002:当前账户未绑定微信开放平台，但存在微信开放平台类型的认证绑定信息，请联系管理员检查绑定信息的一致性
	"""
	code:String
	"""携带的消息"""
	msg:String
}
"""短信验证码校验结果响应
	<AUTHOR>
"""
type SmsCodeValidResultResponse @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.response.SmsCodeValidResultResponse") {
	"""token"""
	token:String
	"""默认200成功
		其他为异常：
		40001:手机号已被使用
		40002:未绑定手机号码
		40003:帐号密码不匹配
		40004:已绑定其他微信帐号
		40005:姓名与帐号不匹配
		40006:身份认证失败
		40007:账户被禁用
		41000:图片验证码错误
		41001:图片验证码已过期
		41002:图片验证码校验未通过
		42000:短信验证码校验未通过
		42001:短信验证码已过期
		42002:短信验证码错误
		42003:校验短信验证码
		42004:校验短信验证码，输入的短信验证码不得为空
		43000:不支持的Token类型
		43001:Token已过期
		43002:Token解析异常
		43003:Token生成失败
		43004:单点登录Token不允许为空
		44000:获取用户微信信息异常
		44001:当前账户未绑定微信开放平台
		44002:当前账户未绑定微信开放平台，但存在微信开放平台类型的认证绑定信息，请联系管理员检查绑定信息的一致性
	"""
	code:String
	"""携带的消息"""
	msg:String
}
"""学员导入任务数据信息
	@author: zhengp 2022/5/12 15:40
"""
type StudentImportTaskResponse @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.response.StudentImportTaskResponse") {
	"""任务编号"""
	id:String
	"""【必填】平台编号"""
	platformId:String
	"""【必填】平台版本编号"""
	platformVersionId:String
	"""【必填】项目编号"""
	projectId:String
	"""【必填】子项目编号"""
	subProjectId:String
	"""任务名称"""
	name:String
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	taskState:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	processResult:Int
	"""处理信息"""
	message:String
	"""处理时间"""
	executingTime:DateTime
	"""结束（完成）时间"""
	completedTime:DateTime
	"""各状态及执行结果对应数量集合
		总数：全部数量之和
		成功数：result = 1数量之和
		失败数：result = 2数量之和
	"""
	eachStateCounts:[EachStateCount]
}
"""各状态及执行结果对应数量"""
type EachStateCount @type(value:"com.fjhb.ms.account.v1.kernel.gateway.graphql.response.StudentImportTaskResponse$EachStateCount") {
	"""任务执行状态
		0-已创建 1-已就绪 2-执行中 3-已完成
		@see com.fjhb.batchtask.core.enums.TaskState
	"""
	state:Int
	"""执行结果
		0-未处理 1-成功 2-失败 3-就绪失败
		@see com.fjhb.batchtask.core.enums.ProcessResult
	"""
	result:Int
	"""数量"""
	count:Int!
}

scalar List
type StudentImportTaskResponsePage {pageSize: Int,pageNo: Int,totalPageSize: Int,totalSize: Int,currentPageData: [StudentImportTaskResponse]}
