import {
  DateScopeRequest,
  ExtendedInfoRequest,
  LearningRegisterRequest,
  SchemeRequest,
  StudentLearningRequest,
  StudentSchemeLearningRequest,
  UserPropertyRequest,
  UserRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'

/*
 * @Author: ZhuSong
 * @Date: 2022-11-07 11:25:22
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-01-05 11:50:07
 * @Description:
 */
export default class LearningArcjovesRequest {
  /**
   * 姓名
   */
  name: string = undefined
  /**
   * 证件号
   */
  idCard: string = undefined
  /**
   * 培训方案 -> ID
   */
  trainingId: string = undefined
  /**
   * 工作单位
   */
  unit: string = undefined
  /**
   * 订单号
   */
  order: string = undefined
  /**
   * 打印状态
   */
  status: boolean = undefined
  /**
   * 考核开始时间
   */
  startTime: string = undefined
  /**
   * 考核结束时间
   */
  endTime: string = undefined
  /**
   * 方案是否提供培训证明
   */
  openPrintTemplate: boolean = undefined
  /**
   * 销售渠道
   */
  saleChannel: SaleChannelEnum = null
  /**
   * 分销商id
   */
  distributorId = ''
  /**
   * 期别id
   */
  issueId = ''

  static to(vo: LearningArcjovesRequest) {
    const dto = new StudentSchemeLearningRequest()
    dto.scheme = new SchemeRequest()
    dto.scheme.schemeId = vo.trainingId
    dto.learningRegister = new LearningRegisterRequest()
    dto.learningRegister.orderNoList = vo.order ? [vo.order] : undefined
    dto.learningRegister.status = [1]
    dto.extendedInfo = new ExtendedInfoRequest()
    dto.extendedInfo.whetherToPrint = vo.status
    dto.studentLearning = new StudentLearningRequest()
    dto.studentLearning.trainingResultList = [1]
    dto.studentLearning.trainingResultTime = new DateScopeRequest()
    dto.studentLearning.trainingResultTime.begin = vo.startTime
    dto.studentLearning.trainingResultTime.end = vo.endTime
    dto.openPrintTemplate = vo.openPrintTemplate ? vo.openPrintTemplate : undefined
    dto.student = new UserRequest()
    dto.student.userProperty = new UserPropertyRequest()
    dto.student.userProperty.companyName = vo.unit ? vo.unit : undefined
    dto.learningRegister.saleChannels = typeof vo.saleChannel === 'number' ? [vo.saleChannel] : undefined
    dto.learningRegister.distributorId = vo.distributorId || undefined
    dto.issueId = vo.issueId || undefined
    return dto
  }
}
