import { PreExamQuestionParamDTO } from '@api/gateway/PreExam-default'

/**
 *
 *
 * @author: eleven
 * @date: 2020/4/10
 */
export class QuestionParam {
  /**
   * 题类
   */
  questionCategory?: string
  /**
   * 是否易错题
   */
  errorProne?: boolean
  topic: string
  libraryId: string
  questionType?: number
  enable?: number
  beginCreateTime: string
  endCreateTime: string
  tagIds: Array<string>

  public from() {
    const param = new PreExamQuestionParamDTO()
    param.tagIdSearchPattern = 2
    if (this.questionCategory) {
      param.questionCategory = this.questionCategory
    }
    param.errorProne = this.errorProne
    param.topic = this.topic
    if (this.beginCreateTime) {
      param.beginCreateTime = this.beginCreateTime + ' 00:00:00'
    }
    if (this.endCreateTime) {
      param.endCreateTime = this.endCreateTime + ' 23:59:59'
    }
    param.enable = -1
    if (this.enable !== undefined) {
      param.enable = this.enable
    }
    if (this.questionType) {
      param.questionTypes = new Array<number>()
      param.questionTypes.push(this.questionType)
    }
    if (this.libraryId) {
      param.librarys = new Array<string>()
      param.librarys.push(this.libraryId)
    }
    if (this.tagIds && this.tagIds.length) {
      param.tagIds = this.tagIds
    }
    return param
  }
}
