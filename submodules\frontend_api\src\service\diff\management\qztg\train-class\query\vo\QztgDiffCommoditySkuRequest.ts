import {
  CommoditySkuRequest,
  DoubleScopeRequest,
  SchemeRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

export default class QztgDiffCommoditySkuRequest extends CommoditySkuRequest {
  /**
   * 方案信息
   */
  schemeRequest: SchemeRequest = new SchemeRequest()

  /**
   * 学时筛选（大端）
   */
  maxPeriod: number = undefined

  /**
   * 学时筛选（小端）
   */
  minPeriod: number = undefined

  toRequest() {
    this.schemeRequest.period = new DoubleScopeRequest()
    this.schemeRequest.period.begin = this.minPeriod
    this.schemeRequest.period.end = this.maxPeriod
  }
}
