<route-meta>
  {
  "title": "销售渠道"
  }
  </route-meta>
<template>
  <div id="root">
    <el-select
      v-model="selected"
      :placeholder="placeholder"
      @clear="selected = undefined"
      class="el-input"
      filterable
      clearable
    >
      <el-option v-for="item in options" :label="item.label" :value="item.value" :key="item.value"></el-option>
    </el-select>
  </div>
</template>
<script lang="ts">
  import { Prop, Emit, Watch, Component, Vue } from 'vue-property-decorator'
  import SaleChannelTypeDiff from '@api/service/diff/management/fjzj/trade/enums/SaleChannelType'
  @Component({})
  export default class extends Vue {
    selected: number = null
    saleChannelTypeOption = SaleChannelTypeDiff.list()
    options = [
      {
        label: '华医网',
        value: 3
      }
    ]
    @Prop({
      type: String,
      default: '请选择销售渠道'
    })
    placeholder: string
    @Prop({
      type: Number
    })
    value: number
    @Watch('value', { immediate: true, deep: true })
    valueChange(val: number) {
      this.selected = val
    }

    @Emit('input')
    @Watch('selected', { immediate: true, deep: true })
    selectedChange() {
      return this.selected
    }
    created() {
      //
    }
    mounted() {
      //
    }
  }
</script>
