import MsImportOpen, { ImportOpenImportInfoForVerifyRequest } from '@api/ms-gateway/ms-importopen-v1'
import { Response, ResponseStatus } from '@hbfe/common'
import ImportOpenLearningParam from '@api/service/management/intelligence-learning/model/ImportOpenLearningParam'

abstract class ImportOpenLearningBasic {
  /**
   * 导入开班入参
   */
  params = new ImportOpenLearningParam()
  /**
   * 导入学员并开班
   */
  abstract doImportOpen(): Promise<Response<string>>
  /**
   * 获取导入开班并学习模板链接
   * @returns
   */
  async queryImportOpenTemplatePathByCategory(): Promise<Response<string>> {
    const result = new Response<string>()
    const request = new ImportOpenImportInfoForVerifyRequest()
    request.category = ImportOpenLearningParam.category
    const { status, data } = await MsImportOpen.queryImportOpenTemplatePathByCategory(request)
    if (status.isSuccess() && data) {
      result.status = status
      result.data = data
    } else {
      result.status = new ResponseStatus(500, '获取模板失败')
    }
    return result
  }
}
export default ImportOpenLearningBasic
