import { Page } from '@hbfe/common'
import QueryOrderListVo from '@api/service/management/trade/single/order/query/vo/QueryOrderListVo'
import OrderDetailVo from '@api/service/management/trade/single/order/query/vo/OrderDetailVo'
import OrderStatisticResponseVo from '@api/service/management/trade/single/order/query/vo/OrderStatisticResponseVo'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'
import UserModule from '@api/service/management/user/UserModule'
import BuyerInfoVo from '@api/service/management/trade/single/order/query/vo/BuyerInfoVo'
import StudentUserInfoVo from '@api/service/management/user/query/student/vo/StudentUserInfoVo'
import QueryTradeConfig from '@api/service/common/trade-config/query/QueryTradeConfig'
import { OrderStatisticResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'

export default abstract class QueryOrderBase {
  /**
   * 查询订单商品分页
   * @param {Page} page - 分页参数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  abstract queryOrderList(
    page: Page,
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderDetailVo[]>

  /**
   * 获取订单总金额、总数量
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  abstract queryOrderListStatistic(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponseVo>

  /**
   * 获取总数
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  abstract getOrderTotalCount(queryParams: QueryOrderListVo, isBusinessConsult: boolean): Promise<number>

  /**
   * 获取交易成功总金额
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  abstract getOrderTotalAmount(queryParams: QueryOrderListVo, isBusinessConsult: boolean): Promise<number>

  /**
   * 获取订单总金额、总数量 去学时
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  abstract queryOrderListStatisticRemoveTotalPeriod(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponseVo>

  /**
   * 获取交易成功总金额
   * @param {QueryOrderListVo} queryParams - 查询参数
   * @param {boolean} isBusinessConsult - 是否是业务咨询，必传
   */
  abstract getOrderTotalAmountRemoveTotalPeriod(
    queryParams: QueryOrderListVo,
    isBusinessConsult: boolean
  ): Promise<OrderStatisticResponse>

  /**
   * 填充买家相关信息
   */
  protected async fillBuyerInfo(arr: OrderDetailVo[]): Promise<OrderDetailVo[]> {
    const userIds =
      arr?.map(item => {
        return item.buyerInfo.userId
      }) || ([] as string[])
    if (!userIds.length) return arr
    const queryRemote: QueryStudentList = UserModule.queryUserFactory.queryStudentList
    const response = await queryRemote.queryStudentListInSubject(userIds)
    const result: Map<string, BuyerInfoVo> = new Map<string, BuyerInfoVo>()
    if (response.status?.isSuccess()) {
      response.data?.map((item: StudentUserInfoVo) => {
        const info: BuyerInfoVo = new BuyerInfoVo()
        info.userId = item.userId
        info.userName = item.userName
        info.userIdCard = item.idCard
        info.loginAccount = item.loginAccount
        info.userPhoneNumber = item.phone
        result.set(item.userId, info)
      })
    }
    arr.forEach((item: OrderDetailVo) => {
      item.buyerInfo = result.get(item.buyerInfo.userId) || new BuyerInfoVo()
    })
    const res = await this.fillReapplyInvoiceInfo(arr)
    return res
  }

  /**
   * （联合网校补要发票配置）填充是否支持补要发票信息
   */
  protected async fillReapplyInvoiceInfo(arr: OrderDetailVo[]): Promise<OrderDetailVo[]> {
    const queryRemote = new QueryTradeConfig()
    const response: boolean = await queryRemote.hasOpenInvoice()
    arr?.forEach((item: OrderDetailVo) => {
      item.enableReApplyInvoice = Boolean(item.enableReApplyInvoice && response)
    })
    return arr
  }

  /**
   * 数组是否有质量（是否不为空）
   */
  protected isWeightyArr<T>(arr: T[]): boolean {
    return Array.isArray(arr) && arr.length ? true : false
  }

  /**
   * 校验用户是否存在
   */
  protected async validateExistUser(queryParams: QueryOrderListVo): Promise<boolean> {
    const queryRemote: QueryStudentList = UserModule.queryUserFactory.queryStudentList
    queryRemote.queryStudentIdParams.idCard = queryParams.idCard || undefined
    queryRemote.queryStudentIdParams.userName = queryParams.userName || undefined
    queryRemote.queryStudentIdParams.loginAccount = queryParams.loginAccount || undefined
    const result = await queryRemote.queryStudentIdList()
    return Array.isArray(result.data) && result.data.length ? true : false
  }
}
