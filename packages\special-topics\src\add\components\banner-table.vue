<template>
  <div>
    <el-card shadow="never" class="m-card f-mb15">
      <div class="f-p15">
        <el-button type="primary" icon="el-icon-plus" class="f-mb15" @click="addRotation">添加轮播图</el-button>
        <!--表格-->
        <el-table stripe :data="tableData" ref="dragWebTable" max-height="500px" class="m-table">
          <el-table-column
            :key="flag"
            type="index"
            label="No."
            width="60"
            align="center"
            fixed="left"
          ></el-table-column>
          <el-table-column label="排序" min-width="80" align="center">
            <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
          </el-table-column>
          <el-table-column label="轮播图" min-width="450">
            <template slot-scope="scope">
              <el-image class="web-banner" :src="scope.row.url" :preview-src-list="[scope.row.url]" />
            </template>
          </el-table-column>
          <el-table-column label="链接地址" min-width="280">
            <template slot-scope="scope">{{ scope.row.link || '-' }}</template>
          </el-table-column>
          <el-table-column label="创建时间" min-width="170">
            <template slot-scope="scope">{{ scope.row.createdAt || '-' }}</template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="editRotation(scope.row, scope.$index)">修改</el-button>
              <el-popconfirm
                title="删除后轮播图需要重新添加，是否确认删除"
                confirm-button-text="确定"
                cancel-button-text="取消"
                icon="el-icon-info"
                icon-color="red"
                @confirm="deleteItem(scope.$index)"
              >
                <el-button slot="reference" type="text" size="mini">删除</el-button>
              </el-popconfirm>
            </template>
          </el-table-column>
        </el-table>
        <el-drawer :title="drawerTitle" :visible.sync="addRotationDialog" size="1000px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-form ref="form" label-width="auto" class="m-form f-mt20">
              <el-form-item label="门户轮播图：">
                <upload-image-link
                  ref="uploadRef"
                  v-model="pictureUrl"
                  :link="link"
                  @callback="callback"
                  :isEdit="isEdit"
                  :imgTypeTip="imageTips"
                >
                </upload-image-link>
              </el-form-item>

              <el-form-item class="m-btn-bar">
                <el-button @click="cancelSave">取消</el-button>
                <el-button type="primary" @click="save" :loading="saving">保存</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
      </div>
    </el-card>
  </div>
</template>

<script lang="ts">
  import { Component, Vue, Ref, Watch, Prop } from 'vue-property-decorator'
  import { ElTable } from 'element-ui/types/table'
  import Sortable from 'sortablejs'
  import TemplateItem from '@api/service/common/template-school/TemplateItem'
  import UploadImageLink from '@hbfe/jxjy-admin-platform/src/components/upload-image-link.vue'
  import { cloneDeep } from 'lodash'

  class CarouselImage {
    /**
     * ID
     */
    id = ''
    /**
     * 轮播图URL
     */
    url = ''
    /**
     * 排序
     */
    sort = 0
    /**
     * 链接
     */
    link = ''
    /**
     * 创建时间
     */
    createdAt = ''
  }

  @Component({
    components: { UploadImageLink }
  })
  export default class extends Vue {
    //TODO
    pictureUrl = ''
    link = ''
    isEdit = false
    drawerTitle = '添加轮播图'
    addRotationDialog = false
    editIndex = 0
    saving = false
    flag = 1
    // imageTips = ''
    TemplateModuleObj: TemplateItem
    saveParams: any[] = new Array<any>()
    // @Prop({})
    @Prop({
      required: true,
      default: () => {
        return new Array<CarouselImage>()
      }
    })
    value: Array<CarouselImage>
    tableData: Array<CarouselImage> = new Array<CarouselImage>()

    @Prop({
      type: Boolean,
      default: false
    })
    isWeb: boolean
    @Prop({
      type: Number,
      default: 0
    })
    webSizeWidth: number
    @Prop({
      type: Number,
      default: 0
    })
    WebSizeHight: number
    @Prop({
      type: Number,
      default: 0
    })
    H5SizeWidth: number
    @Prop({
      type: Number,
      default: 0
    })
    H5SizeHight: number
    @Ref('dragWebTable') dragWebTable: any
    @Ref('uploadRef') uploadRef: any
    @Watch('value', {
      deep: true,
      immediate: true
    })
    setTableData(val: Array<CarouselImage>) {
      if (val.length > 0) {
        this.tableData = cloneDeep(val)
      }
    }
    webSize = { width: '', hight: '' }
    H5Size = { width: '', hight: '' }
    get imageTips() {
      const imageTips = this.isWeb
        ? `建议选择高清大图，尺寸：宽度${this.webSizeWidth}PX，高度${this.WebSizeHight}PX`
        : `建议选择高清大图，尺寸：宽度${this.H5SizeWidth}PX，高度${this.H5SizeHight}PX`
      return imageTips
    }
    // 添加轮播图
    addRotation() {
      this.pictureUrl = ''
      this.link = ''
      if (this.isEdit) {
        this.uploadRef.reset()
      }
      this.drawerTitle = '添加轮播图'
      this.isEdit = false
      this.addRotationDialog = true
    }
    // 修改轮播图
    editRotation(item: CarouselImage, idx: number) {
      this.editIndex = idx
      const index = item.url.indexOf('/mfs/')
      this.pictureUrl = item.url.slice(index)
      this.drawerTitle = '修改轮播图'
      this.link = item.link
      this.isEdit = true
      this.addRotationDialog = true
    }
    // 删除轮播图
    async deleteItem(index: number) {
      this.tableData.splice(index, 1)
      this.saveParams.splice(index, 1)
      this.$emit('input', this.tableData)
    }
    callback(val: any) {
      this.link = val[0]?.address
      this.saveParams = val
    }
    cancelSave() {
      this.$confirm('提示', {
        message: '确定要放弃编辑吗？',
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.addRotationDialog = false
          // this.uploadRef.fileList = new Array<any>()
        })
        .catch(() => {
          console.log('点个der...')
        })
    }
    // 保存修改
    async save() {
      this.saving = true
      if (this.saveParams.length === 0) {
        this.$message.error('轮播图不能为空')
        this.saving = false
        return
      }
      this.saveParams.map((item, index) => {
        const i = new CarouselImage()
        i.sort = this.tableData.length + index + 1
        i.url = item.url
        i.link = item.address ? item.address : ''
        i.createdAt = this.timestampToTime()
        if (!this.isEdit) {
          this.tableData.push(i)
          console.log('轮播图添加', this.tableData)
        } else {
          i.sort = this.tableData[this.editIndex]?.sort
          this.tableData.splice(this.editIndex, 1, i)

          console.log('轮播图修改', this.tableData)
        }
      })
      this.addRotationDialog = false
      this.saving = false
      this.$emit('input', this.tableData)
    }
    dragElement(table: ElTable) {
      const el = table.$el.querySelector('.el-table__body-wrapper > table > tbody') as HTMLElement
      return new Sortable(el, {
        ghostClass: 'blue-background-class',
        handle: '.hb-iconfont',
        onEnd: ({ newIndex, oldIndex }) => {
          const curRow = this.tableData.splice(oldIndex, 1)[0]
          this.tableData.splice(newIndex, 0, curRow)
          let newArray = this.tableData.slice(0)
          this.tableData = []
          this.$nextTick(async () => {
            newArray = newArray.map((res, index) => {
              res.sort = index + 1
              return res
            })
            this.tableData = newArray
            this.flag += 1
          })
        }
      })
    }
    async mounted() {
      console.log(this.dragWebTable)
      this.dragElement(this.dragWebTable)
    }
    private timestampToTime() {
      const date = new Date() //时间戳为10位需*1000，时间戳为13位的话不需乘1000
      const Y = date.getFullYear() + '-'
      const M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-'
      const D = (date.getDate() < 10 ? '0' + date.getDate() : date.getDate()) + ' '
      const h = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':'
      const m = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':'
      const s = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
      return Y + M + D + h + m + s
    }
  }
</script>
