/*
 * @Author: dong<PERSON><PERSON>
 * @Date: 2023-07-24 16:43:45
 * @LastEditors: dongjuncheng
 * @LastEditTime: 2024-04-26 20:51:25
 * @Description:
 */
import { PurchaseChannelTypeEnum } from '@api/service/customer/trade/single/enum/PurchaseChannelTypeEnum'
import { QueryExchangeCommodity } from '@api/service/customer/trade/single/query/QueryExchangeCommodity'
import QueryMyOrderList from '@api/service/customer/trade/single/query/QueryMyOrderList'
import { QueryOrderPayStatue } from '@api/service/customer/trade/single/query/QueryOrderPayStatue'
import QueryPayOrderConfig from '@api/service/customer/trade/single/query/QueryPayOrderConfig'
import { QueryPayingStatue } from '@api/service/customer/trade/single/query/QueryPayingStatue'
import QueryPreparePayOrderConfig from '@api/service/customer/trade/single/query/QueryPreparePayOrderConfig'
import QueryCreateOrderConfig from '@api/service/customer/trade/single/query/query-create-order-config/QueryCreateOrderConfig'
import QueryOrderDetail from '@api/service/customer/trade/single/query/query-customer-user-order/query-order-detail/QueryOrderDetail'
/**
 * 培训班查询工厂类
 */
class QueryTradeSingleFactory {
  get queryCreateOrderConfig() {
    return (
      schemeId?: string,
      ids?: string[],
      purchaseChannelType = PurchaseChannelTypeEnum.PurchaseChannelTypeEnumUserBuy,
      portalCommoditySkuId?: string,
      isFx?: boolean
    ) => {
      return new QueryCreateOrderConfig(schemeId, ids, purchaseChannelType, portalCommoditySkuId, isFx)
    }
  }
  //获取支付初始化对象
  get queryPayOrderConfig() {
    return (orderNo: string, portalCommoditySkuId = '') => {
      return new QueryPayOrderConfig(orderNo, portalCommoditySkuId)
    }
  }
  //获取支付中初始化对象
  get queryPayingOrderConfig() {
    return (orderNo: string) => {
      return new QueryPreparePayOrderConfig(orderNo)
    }
  }
  //查询支付中的状态
  get queryPayingStatue() {
    return () => {
      return new QueryPayingStatue()
    }
  }
  //查询支付状态类
  get queryOrderPayStatue() {
    return new QueryOrderPayStatue()
  }
  // 获取我的订单列表查询类实例 多例
  get queryMyOrderList() {
    return new QueryMyOrderList()
  }
  // 获取换货单列表查询
  get queryExchangeOrderList() {
    return new QueryExchangeCommodity()
  }
  /**
   * 获取订单详情查询类
   */
  get queryOrderDetailMultiton() {
    return (orderNo: string) => {
      return new QueryOrderDetail(orderNo)
    }
  }
}
export default new QueryTradeSingleFactory()
