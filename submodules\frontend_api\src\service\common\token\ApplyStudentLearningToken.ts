import MsStudentLearningV1, { ApplyLearningTokenRequest } from '@api/ms-gateway/ms-studentlearning-v1'
import AbstractApplyToken from '@api/service/common/token/AbstractApplyToken'
import { ResponseStatus } from '@hbfe/common'

/**
 * 申请学员学习 token
 */
class ApplyStudentLearningToken extends AbstractApplyToken {
  // 参训资格 id
  private readonly qualificationId: string
  // 学习方式 id
  private readonly learningId: string

  /**
   * @param qualificationId 参训资格 id
   * @param learningId 学习方式 id
   */
  constructor(qualificationId: string, learningId: string) {
    super()
    this.qualificationId = qualificationId
    this.learningId = learningId
  }

  /**
   * 用学习方案换取学员 token
   */
  async apply(): Promise<ResponseStatus> {
    const requestStudentLearningTokenParams = new ApplyLearningTokenRequest()
    requestStudentLearningTokenParams.learningId = this.learningId
    requestStudentLearningTokenParams.qualificationId = this.qualificationId
    const result = await MsStudentLearningV1.applyStudentLearningToken(requestStudentLearningTokenParams)
    this.token = result.data
    let errorMsg = ''

    if (result.status.code != 200) {
      errorMsg = result.status.getMessage()
      result.status.code = result.status.errors[0].code
    }
    return new ResponseStatus(result.status.code, errorMsg)
  }

  /**
   * 用学习方案换取学员 token 会中断智能学习
   */
  async applyInterruptAutoStudy(): Promise<ResponseStatus> {
    const requestStudentLearningTokenParams = new ApplyLearningTokenRequest()
    requestStudentLearningTokenParams.learningId = this.learningId
    requestStudentLearningTokenParams.qualificationId = this.qualificationId
    const result = await MsStudentLearningV1.applyStudentLearningTokenInterruptAutoStudy(
      requestStudentLearningTokenParams
    )
    this.token = result.data
    let errorMsg = ''

    if (result.status.code != 200) {
      errorMsg = result.status.getMessage()
      result.status.code = result.status.errors[0].code
    }
    return new ResponseStatus(result.status.code, errorMsg)
  }
}

export default ApplyStudentLearningToken
