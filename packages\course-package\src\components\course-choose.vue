<template>
  <el-card class="flex-sub-2 ml20">
    <!-- <hb-search-wrapper :model="QueryCourseListParam" @reset="resetParam"> -->
    <!-- <el-form-item class="fl" label="课程名称：">
      <el-input placeholder="请输入课程名称" v-model="QueryCourseListParam.name"></el-input>
    </el-form-item>
    <el-button type="primary" @click="searchBase">查询</el-button>-->
    <el-row :gutter="16" class="m-query no-gutter">
      <el-form :inline="true" label-width="auto">
        <el-col :span="8">
          <el-form-item label="课程名称">
            <el-input v-model="queryCourseListParam.name" clearable placeholder="请输入课程名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item>
            <el-button type="primary" @click="searchBase">查询</el-button>
            <el-button type="primary" @click="chooseAll">全选</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <!-- <template slot="actions">
        <el-button type="primary" @click="searchBase">查询</el-button>
      </template> -->
    <!-- </hb-search-wrapper> -->

    <!-- <el-form :inline="true" class="mt50">
      <el-form-item class="fr">
        <span class="mr10">共有{{ totalSize }}门课程</span>
        <el-button type="primary" @click="chooseAll">全选</el-button>
      </el-form-item>
    </el-form> -->
    <el-alert type="warning" :closable="false" class="m-alert f-mb10"> 共有 {{ totalSize }}门课</el-alert>
    <el-table
      ref="tableData"
      :data="currentPageDataThis"
      class="m-table"
      max-height="500px"
      stripe
      v-loading="query.loading"
    >
      <el-table-column prop="index" type="index" label="No." width="60" align="center"></el-table-column>
      <el-table-column prop="name" label="课程名称" min-width="240"></el-table-column>
      <el-table-column prop="period" label="学时" min-width="80" align="center"></el-table-column>
      <el-table-column prop="evaluation" label="综合评分" min-width="100" align="center"></el-table-column>
      <el-table-column prop="createTime" label="创建时间" min-width="200"></el-table-column>

      <el-table-column label="操作" width="150" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button size="mini" @click="handleChoose(scope.row, !scope.row.reallyChoose)" type="text">
            {{ scope.row.reallyChoose ? '取消' : '选择' }}
          </el-button>
          <el-button size="mini" @click="previewCourse(scope.row.id)" type="text">预览</el-button>
        </template>
      </el-table-column>
    </el-table>

    <hb-pagination :page="page" v-bind="page"></hb-pagination>
  </el-card>
</template>
<script lang="ts">
  import { Component, Mixins, Prop, Watch } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import QueryCourseListParam from '@api/service/management/resource/course/query/vo/QueryCourseListParam'
  import ResourceModule from '@api/service/management/resource/ResourceModule'
  import CreateCoursePackageVo from '@api/service/management/resource/course-package/mutation/vo/CreateCoursePackageVo'
  import CourseInCoursePackage from '@api/service/management/resource/course-package/mutation/vo/CourseInCoursePackage'
  import { CourseStatusEnum } from '@api/service/common/enums/course/CourseStatus'
  import PreviewCourseMixins from '@hbfe/jxjy-admin-common/src/mixins/PreviewCourseMixins'

  class NewCourseListDetail extends CourseInCoursePackage {
    /**
     * 是否被选
     */
    reallyChoose = false
  }

  @Component
  export default class CourseChoose extends Mixins(PreviewCourseMixins) {
    loadPage = true
    chooseAllStatus = false
    //currentPageDataThis: any[] = new Array<NewBTPXCourse>()
    // page = new Page()
    queryCourseListParam: QueryCourseListParam = new QueryCourseListParam()
    currentPageDataThis: any = new Array<NewCourseListDetail>()
    chooseData = new Array<NewCourseListDetail>()
    page: UiPage
    query: Query = new Query()
    categoryId = ''
    totalSize = 0
    courseWareSupplierId: string[] = [] //课件供应商ID

    @Prop({
      type: Object,
      // required: true,
      default: () => new CreateCoursePackageVo()
    })
    createCoursePackage: CreateCoursePackageVo

    @Prop({
      type: String,
      default: ''
    })
    courseParam: string //查询用参数

    @Prop({
      type: String,
      default: ''
    })
    courseWareSupId: string //修改时当前供应商ID

    @Prop({
      type: Boolean,
      default: false
    })
    isModify: string

    @Prop({
      type: Number,
      default: 0
    })
    changeDataNum: number

    // @Watch('isModify', {
    //   immediate: true,
    //   deep: true
    // })
    // isModifyChange(val: boolean) {
    //   if (val) {
    //     this.doQueryPage()
    //   }
    // }

    @Watch('currentPageDataThis', { deep: true })
    changeCurrentPageDataThis(val: Array<NewCourseListDetail>) {
      // 判断是否全选
      const isAll = val.filter((item) => item.reallyChoose === true).length === this.page.pageSize
      this.chooseAllStatus = isAll
    }
    @Watch('createCoursePackage.addedList', {
      immediate: true,
      deep: true
    })
    createCoursePackageAddedListChange(val: any) {
      if (val) {
        this.stateValidation()
      }
    }

    @Watch('changeDataNum', {
      immediate: true,
      deep: true
    })
    changeDataChange(val: any) {
      if (val >= 2) {
        this.currentPageDataThis.forEach((item: NewCourseListDetail, index: number) => {
          if (this.createCoursePackage.addedList.length >= 1) {
            console.log(this.createCoursePackage.addedList, 'this.createCoursePackage.addedList')
            this.createCoursePackage.addedList.findIndex((itm) => {
              if (item.id !== itm.id) {
                this.currentPageDataThis[index].reallyChoose = false
              }
            })
          } else {
            this.currentPageDataThis[index].reallyChoose = false
          }
        })
      }
    }

    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }

    // 根据机构和工种的变化进行查询
    @Watch('courseParam', { immediate: true, deep: true })
    async courseParamChange(newValue: any) {
      if (newValue) {
        this.queryCourseListParam.categoryIdList = newValue ? [newValue] : undefined
        console.log(newValue, 'courseParam')
        this.page.pageNo = 1

        await this.doQueryPage()
      }
    }

    /**
     * 父组件调用这个组件来取消选择课程
     * @param courseId
     */
    propCancelChooseById(courseId: string) {
      this.changeCurrentPageDataThisStatus(courseId, false)
      this.changeChooseAllStatus()
    }

    /**
     * 改变课程中的是否选择状态
     * */
    changeCurrentPageDataThisStatus(courseId: string, reallyChoose: boolean) {
      const index = this.currentPageDataThis.findIndex(function (value: any) {
        return value.id === courseId
      })

      if (index !== -1) {
        this.currentPageDataThis[index].reallyChoose = reallyChoose
      }
    }

    // 是否全选
    changeChooseAllStatus() {
      this.chooseAllStatus = this.createCoursePackage.addedList.length >= this.currentPageDataThis.length
    }

    // 选择or取消
    handleChoose(item: NewCourseListDetail, reallyChoose: boolean) {
      if (reallyChoose) {
        if (!item.reallyChoose) {
          item.reallyChoose = reallyChoose
          // this.chooseData.push(item)
          // this.$emit('chooseData', this.chooseData)
          const i = new CourseInCoursePackage()
          i.id = item.id
          i.name = item.name
          i.period = item.period
          i.physicsPeriod = item.physicsPeriod
          i.sort = item.sort
          i.createTime = item.createTime
          i.providerName = item.providerName
          this.createCoursePackage.addCourse(i)
          this.$emit('addPeriod', item.period)
          // console.log(this.chooseData, ' this.chooseData')
        }
      }

      if (!reallyChoose) {
        item.reallyChoose = reallyChoose

        const index = this.createCoursePackage.addedList.findIndex(function (value) {
          return value.id === item.id
        })
        if (index !== -1) {
          // this.chooseData.splice(index, 1)
          // console.log(this.chooseData, ' this.chooseData')
          // this.$emit('chooseData', this.chooseData)
          const i = new CourseInCoursePackage()
          i.id = item.id
          i.name = item.name
          i.period = item.period
          i.physicsPeriod = item.physicsPeriod
          i.sort = item.sort
          i.createTime = item.createTime
          this.createCoursePackage.cancelChoose(i)
          this.$emit('reductionPeriod', item.period)
        }
      }
      // this.changeCurrentPageDataThisStatus(item.id, reallyChoose)
      this.changeChooseAllStatus()
    }

    async searchBase() {
      this.page.pageNo = 1
      await this.doQueryPage()
    }

    async doQueryPage(pageNo = 1) {
      this.query.loading = true
      if (!this.queryCourseListParam.categoryIdList) {
        return
      }
      try {
        this.queryCourseListParam.enable.current = CourseStatusEnum.ENABLE
        const results = await ResourceModule.courseFactory.queryCourse.queryCoursePage(
          this.page,
          this.queryCourseListParam
        )
        this.currentPageDataThis = new Array<NewCourseListDetail>()
        results?.forEach((item) => {
          const option = new NewCourseListDetail()
          Object.assign(option, item)
          option.reallyChoose = false
          option.physicsPeriod = item.period
          option.providerName = item.courseProviderName
          this.currentPageDataThis.push(option)
          ;(this.$refs['tableData'] as any)?.doLayout()
        })
        console.log(this.currentPageDataThis, 'currentPageDataThiscurrentPageDataThis')
        this.totalSize = this.page.totalSize
        setTimeout(() => {
          this.stateValidation()
        }, 100)
      } catch (e) {
        console.log(e)
      } finally {
        this.query.loading = false
      }
    }

    stateValidation() {
      if (this.currentPageDataThis.length > 0) {
        this.currentPageDataThis.forEach((item: NewCourseListDetail, index: number) => {
          this.createCoursePackage.addedList.findIndex((p) => {
            if (item.id == p.id) {
              this.currentPageDataThis[index].reallyChoose = true
            }
          })
        })
      }

      // if(this.createCoursePackage.addedList.length)
    }

    chooseAll() {
      const chooseAllStatus = this.chooseAllStatus
      this.currentPageDataThis.forEach((p: any) => {
        this.handleChoose(p, !chooseAllStatus)
      })
      this.chooseAllStatus = !chooseAllStatus
    }

    // 重置
    resetParam() {
      this.page.pageNo = 1
      // this.courseChooseParam.name = ''
      this.doQueryPage()
    }

    async created() {
      await this.doQueryPage()
    }
  }
</script>

<style lang="scss" scoped>
  .mt50 {
    margin-top: -50px;
  }

  /* ::v-deep.el-checkbox__input::after {
    content: '全选';
    display: inline-block;
    vertical-align: 2px;
    margin-left: 10px;
  } */
  ::v-deep .el-checkbox::after {
    content: ' 全选';
  }
</style>
