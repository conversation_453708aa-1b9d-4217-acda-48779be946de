<!--
 * @Author: lixin<PERSON> <EMAIL>
 * @Date: 2023-07-24 20:05:43
 * @LastEditors: lixinye <EMAIL>
 * @LastEditTime: 2023-07-25 11:46:30
 * @FilePath: \jxjyv2_frontend_web_admin\src\unit-share\network-school\statistic\statistics-report\__components__\user-info.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!-- 用户信息 -->
<template>
  <!-- 用户信息 -->
  <div class="f-p20" v-loading="loadStatus">
    <el-row class="no-gutter">
      <el-form :inline="true" label-width="100px" class="m-text-form f-mt10">
        <el-col :span="8">
          <el-form-item label="姓名：">{{ userDetail.userName || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="手机号：">{{ userDetail.phone || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8" v-if="judgeJXGX">
          <el-form-item label="登录账号：">{{ userDetail.loginAccount || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="身份证号：">{{ userDetail.idCard || '-' }}</el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="所在地区："><span v-text="formatRegion(userDetail.companyRegion)"></span> </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="工作单位：">{{ userDetail.companyName || '-' }}</el-form-item>
        </el-col>
      </el-form>
    </el-row>
  </div>
</template>

<script lang="ts">
  import { Component, Prop, Vue } from 'vue-property-decorator'
  import UserModule from '@api/service/management/user/UserModule'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
  import { RegionModel } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import systemContext from '@api/service/common/context/Context'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  @Component
  export default class extends Vue {
    queryStudentDetail = UserModule.queryUserFactory.queryStudentDetail

    // 加载状态
    loadStatus = false

    // 用户数据
    userDetail = new UserDetailVo()

    // 加载用户信息
    async loadData(userId: string) {
      this.loadStatus = true
      try {
        if (!userId) return new Error('用户id为空')
        const res = await this.queryStudentDetail(userId).queryDetail()
        console.log(res)
        this.userDetail = res.data
      } catch (error) {
        console.log(error)
      } finally {
        this.loadStatus = false
      }
    }
    get judgeJXGX() {
      return ConfigCenterModule.getFrontendApplication(frontendApplication.loginAccountServicerIds).includes(
        systemContext.servicerInfo.id
      )
    }
    // 格式化地区处理
    formatRegion(region: RegionModel) {
      let regionStr = ''
      if (region && region.provinceName) regionStr += region.provinceName
      if (region && region.cityName) regionStr += region.cityName
      if (!regionStr) regionStr = '-'
      return regionStr
    }
  }
</script>
