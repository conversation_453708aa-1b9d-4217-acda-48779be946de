import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ResponseStatus } from '@api/Response'
import { TrainingVoucherInfo } from '@api/service/common/training-voucher/model/TrainingVoucherInfo'
import { TrainingVoucherInfoUi } from '@api/service/common/training-voucher/model/TrainingVoucherInfoUi'
import trainingVoucherGateway, {
  Page,
  TrainingVoucherInfoResponse,
  UserTrainingVoucherRequest
} from '@api/gateway/PlatformTrainingVoucher'
import CommonModule from '@api/service/common/common/CommonModule'
import PlatformWorkTypeGateway, { SearchMatchingPattern, WorkTypeResponse } from '@api/gateway/PlatformWorkType'
import WorkTypeCategoryModule from '@api/service/customer/work-type/WorkTypeCategoryModule'
export interface TrainingVoucherState {
  //默认每页数量
  defaultSize: number
  //总数
  totalSize: number
  //总页数
  totalPageSize: number
  //分页数据
  dataList: Array<TrainingVoucherInfo>
  //用户列表数据
  dataListByUser: Array<TrainingVoucherInfo>
}

/**
 *
 * 用户培训券查询条件
 * @author: puxf
 * @date: 2021/2/23
 */
export class TrainingVoucherQueryParams {
  /**
   * 身份证号
   */
  identityNumber!: string
  /**
   * 用户培训券状态（可选），默认0
   <pre>
   0 - 不限
   1 - 可用
   2 - 激活
   3 - 已使用
   4 - 已结算（补贴）
   -5 - 已作废
   -2 - 过期
   </pre>
   */
  status: number
}

@Module({ namespaced: true, store, dynamic: true, name: 'TrainingVoucherModule' })
class TrainingVoucherModule extends VuexModule implements TrainingVoucherState {
  //默认每页数量
  defaultSize = 5
  //总数
  totalSize = 0
  //总页数
  totalPageSize = 0
  dataList = new Array<TrainingVoucherInfo>()
  dataListByUser = new Array<TrainingVoucherInfo>()

  /**
   * 获取分页列表数据
   * PS:这里的分页只是按照每页数去查询，直到没有数据。。
   * @param params
   */
  @Action
  async getPage(params: { page: Page; queryParam: TrainingVoucherQueryParams }): Promise<ResponseStatus> {
    const paramRemote = new UserTrainingVoucherRequest()
    Object.assign(paramRemote, params.queryParam)
    const response = await trainingVoucherGateway.findUserTrainingVoucherPageList({
      page: params.page,
      request: paramRemote
    })
    if (!response.status.isSuccess()) {
      return response.status
    }
    const workTypeList = Array<WorkTypeResponse>()
    if (response.data && response.data.length > 0) {
      const jobList = response.data.flatMap(d => d.jobScope)
      jobList.push(...response.data.map(d => d.job).filter(d => d))
      const jobScopeSet = new Set<string>(jobList)
      for (const jobScope of jobScopeSet) {
        const workTypeResponse = await PlatformWorkTypeGateway.getAllWorkTypeList({
          name: jobScope,
          matchingPattern: SearchMatchingPattern.EQUAL
        })
        if (workTypeResponse.status.isSuccess()) {
          workTypeList.push(...workTypeResponse.data)
        }
      }
    }
    this.SET_DATA_LIST({ list: response.data, workTypeList })
    return response.status
  }

  /**
   * 通过用户身份证号获取
   * @param identityNumber
   * @param status 用户培训券状态（可选），默认0
   <pre>
   0 - 不限
   1 - 可用
   2 - 激活
   3 - 已使用
   4 - 已结算（补贴）
   -5 - 已作废
   -2 - 过期
   </pre>
   */
  @Action
  async listByUser(params: { identityNumber: string; status?: number }): Promise<ResponseStatus> {
    //这里的分页只是按照没页数去查询，直到没有数据。。
    const page = new Page()
    page.pageNo = 1
    page.pageSize = 200
    const paramRemote = new UserTrainingVoucherRequest()
    paramRemote.identityNumber = params.identityNumber
    paramRemote.status = params.status || 0
    const response = await trainingVoucherGateway.findUserTrainingVoucherPageList({
      page: page,
      request: paramRemote
    })
    if (!response.status.isSuccess()) {
      return response.status
    }
    const workTypeList = Array<WorkTypeResponse>()
    if (response.data && response.data.length > 0) {
      const jobList = response.data.flatMap(d => d.jobScope)
      jobList.push(...response.data.map(d => d.job).filter(d => d))
      const jobScopeSet = new Set<string>(jobList)
      for (const jobScope of jobScopeSet) {
        const workTypeResponse = await PlatformWorkTypeGateway.getAllWorkTypeList({
          name: jobScope,
          matchingPattern: SearchMatchingPattern.EQUAL
        })
        if (workTypeResponse.status.isSuccess()) {
          workTypeList.push(...workTypeResponse.data)
        }
      }
    }
    this.SET_DATA_LIST_BY_USER({ list: response.data, workTypeList })
    return response.status
  }

  @Mutation
  private SET_DATA_LIST(param: { list: Array<TrainingVoucherInfoResponse>; workTypeList: Array<WorkTypeResponse> }) {
    this.dataList = new Array<TrainingVoucherInfo>()
    param.list.map(p => {
      const item = new TrainingVoucherInfo()
      Object.assign(item, p)
      if (item.jobScope && item.jobScope.length > 0) {
        item.workTypeScopeList = param.workTypeList.filter(w => item.jobScope.some(i => w.name === i))
      }
      if (item.job) {
        item.usableWorkType = param.workTypeList.find(w => item.job === w.name)
      }
      this.dataList.push(item)
    })
    this.SET_TOTAL_PAGE_SIZE(0) //接口未返回数据
    this.SET_TOTAL_SIZE(0) //接口未返回数据
  }

  @Mutation
  private SET_DATA_LIST_BY_USER(param: {
    list: Array<TrainingVoucherInfoResponse>
    workTypeList: Array<WorkTypeResponse>
  }) {
    this.dataListByUser = new Array<TrainingVoucherInfo>()
    param.list.map(p => {
      const item = new TrainingVoucherInfo()
      Object.assign(item, p)
      if (item.amount !== 0) {
        item.amount = item.amount / 100
      }
      if (item.nowAmount !== 0) {
        item.nowAmount = item.nowAmount / 100
      }
      if (item.jobScope && item.jobScope.length > 0) {
        item.workTypeScopeList = param.workTypeList.filter(w => item.jobScope.some(i => w.name === i))
      }
      if (item.job) {
        item.usableWorkType = param.workTypeList.find(w => item.job === w.name)
      }
      this.dataListByUser.push(item)
    })
  }

  @Mutation
  SET_TOTAL_SIZE(totalSize: number) {
    this.totalSize = totalSize
  }

  @Mutation
  SET_TOTAL_PAGE_SIZE(totalPageSize: number) {
    this.totalPageSize = totalPageSize
  }

  get getByIdentityNumber() {
    return (identityNumber: string): Array<TrainingVoucherInfo> => {
      return this.dataListByUser[identityNumber]
    }
  }

  /**
   * 获取学员未使用的培训券
   */
  get unUsedList() {
    const list = this.dataListByUser?.filter(el => {
      return el.couponStatus === 1 || el.couponStatus === 2
    })
    const unUsedList: TrainingVoucherInfoUi[] = new Array<TrainingVoucherInfoUi>()
    list.forEach(el => {
      const ticketUi = new TrainingVoucherInfoUi()
      Object.assign(ticketUi, el)
      unUsedList.push(ticketUi)
    })
    return unUsedList
  }

  /**
   * 获取学员已过期的培训券
   */
  get expiredList() {
    const list = this.dataListByUser?.filter(el => el.couponStatus === -2)
    const expiredList: TrainingVoucherInfoUi[] = new Array<TrainingVoucherInfoUi>()
    list.forEach(el => {
      const ticketUi = new TrainingVoucherInfoUi()
      Object.assign(ticketUi, el)
      ticketUi.reason = '培训券已失效'
      expiredList.push(ticketUi)
    })
    return expiredList
  }

  /**
   * 获取学员已过期的培训券
   */
  get expiredListByAdmin() {
    const list = this.dataListByUser?.filter(el => el.couponStatus === -2 || el.couponStatus === -5)
    const expiredList: TrainingVoucherInfoUi[] = new Array<TrainingVoucherInfoUi>()
    list.forEach(el => {
      const ticketUi = new TrainingVoucherInfoUi()
      Object.assign(ticketUi, el)

      ticketUi.reason = el.couponStatus === -2 ? '培训券已失效' : '培训券已作废'
      expiredList.push(ticketUi)
    })
    return expiredList
  }

  /**获取可用的培训券列表
   * 适用于listByUser筛选全部的情况
   * @param: 工种
   *  */
  get getOderList() {
    return (params: {
      workTypeName?: string
      personScope?: Array<string>
      price: number
      trainingCategoryId?: string
    }) => {
      // 全部培训券
      let initialList = this.dataListByUser
      // 过滤掉已使用(可用和不可用的都不包括已使用的

      initialList = initialList.filter(el => {
        if (el.couponStatus === 1 || el.couponStatus === 2 || el.couponStatus === -2) {
          return true
        }
        return false
      })
      // 接下来找出不可用的 过期/金额不够/工种不匹配
      const disabledList: TrainingVoucherInfoUi[] = []
      const usableList: TrainingVoucherInfoUi[] = []

      initialList.forEach(el => {
        console.log(
          el.job,
          params.workTypeName,
          params.trainingCategoryId,
          WorkTypeCategoryModule.OPERATION_TYPE_WORK,
          el.job !== params.workTypeName || params.trainingCategoryId !== WorkTypeCategoryModule.OPERATION_TYPE_WORK
        )
        const ticketUi = new TrainingVoucherInfoUi()
        Object.assign(ticketUi, el)
        ticketUi.expand = false
        // if (el.jobScope && el?.jobScope?.indexOf(params.workTypeName) === -1) {
        //   ticketUi.reason = '培训工种不匹配'
        //   disabledList.push(ticketUi)
        // } else
        if (el.nowAmount < params.price) {
          ticketUi.reason = '培训券金额不足'
          disabledList.push(ticketUi)
        }
        // 过期的情况
        else if (el.couponStatus === -2) {
          ticketUi.reason = '培训券已失效'
          disabledList.push(ticketUi)
        }

        // 找出工种不适配
        // 工种类别必须是操作技能型
        // 工种必须一样
        else if (
          el.job &&
          (el.job !== params.workTypeName || params.trainingCategoryId !== WorkTypeCategoryModule.OPERATION_TYPE_WORK)
        ) {
          ticketUi.reason = `与操作技能型工种线下课程工种(${el.job})不符`
          disabledList.push(ticketUi)
        }

        // 过滤适用人群 取交集  或者商品没有适用人群就是全部适用 同理培训券 所以取交集 如果没有交集就是不适用
        else if (el.personScope && el.personScope.length && params.personScope && params.personScope.length) {
          // 新增的需求 如果数组里面含特定关键字
          // 券
          if (el.personScope.findIndex(el => el === CommonModule.SUITABLE_ALL) > -1) {
            usableList.push(ticketUi)
            return
          }
          // 如果券都是''的   那么也是所有人可用
          if (el.personScope.every(x => x === '' || x === ' ')) {
            usableList.push(ticketUi)
            return
          }
          // 商品
          if (params.personScope.findIndex(el => el === CommonModule.SUITABLE_ALL) > -1) {
            usableList.push(ticketUi)
            return
          }
          const intersection = el.personScope.filter(val => {
            return params.personScope.indexOf(val) > -1
          })
          if (intersection && intersection.length === 0) {
            ticketUi.reason = '适用人群不匹配'
            disabledList.push(ticketUi)
          } else {
            // 可用的
            usableList.push(ticketUi)
          }
        }

        // 余下的过滤掉不可用的就是可用的
        else {
          usableList.push(ticketUi)
        }
      })

      return {
        usableList,
        disabledList
      }
    }
  }

  /**获取已失效 已作废的培训券 不包括已使用的 */
  get getDisabledList() {
    return this.dataListByUser?.filter(el => {
      // 去除掉已使用的
      if (el.couponStatus === 3 || el.couponStatus === 4 || el.couponStatus === 1) {
        return false
      }
      return true
    })
  }

  /**
   * 适用人群匹配判定，同时筛选是否符合培训班的适用人群
   * @param personScop 培训券适用人群
   * @param trainingScope 培训班适用人群
   * @returns true/false,符合/不符合
   */
  // isAllPerson(personScop: any, trainingScope: Array<string>): boolean {
  //   if (personScop) {
  //     if (Array.isArray(personScop)) {
  //       // 匹配“[]”格式判定
  //       if (personScop.length <= 0) return true
  //       //匹配“['',' ', ""," "]”
  //       if (personScop.every(x => x === '' || x === ' ')) {
  //         return true
  //       }
  //       //
  //       if (personScop.findIndex(el => el === CommonModule.SUITABLE_ALL) > -1) {
  //         return true
  //       }
  //       //
  //       if (trainingScope.find(t => personScop.includes(t))) {
  //         return true
  //       } else {
  //         return false
  //       }
  //     } else {
  //       throw new Error()
  //     }
  //   }
  //   //适用人群不存在时，判定为适用所有人群
  //   return true
  // }
}

export default getModule(TrainingVoucherModule)
