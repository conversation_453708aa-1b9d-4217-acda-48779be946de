import { DoubleScopeRequest } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import {
  CommoditySkuRequest1,
  DateScopeRequest,
  OrderBasicDataRequest,
  OrderPayInfoRequest,
  OrderRequest,
  OrderStatusChangeTimeRequest
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import SaleChannel, { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { OrderTransaction } from '@api/service/management/trade/single/order/query/enum/OrderTransactionStatus'
import UserModule from '@api/service/management/user/UserModule'
import QueryStudentList from '@api/service/management/user/query/student/QueryStudentList'

/**
 * @description 查询订单列表参数
 */
class QueryOrderListVo {
  /**
   * 销售渠道
0-自营 1-分销 不传则查全部
   */
  saleChannel: number = undefined
  /**
   * 收款账号
   */
  receiveAccountIdList: string[] = []

  /**
   * 订单号Id集合
   */
  orderNo = ''

  /**
   * 登录账号
   */
  loginAccount = ''
  /**
   * 证件号
   */
  idCard = ''

  /**
   * 姓名
   */
  userName = ''

  /**
   * 交易状态 1：等待付款 2：支付中 3：开通中 4：交易成功 5：交易关闭
   */
  orderStatus: OrderTransaction = null

  /**
   * 缴费渠道 1: web端 2：H5端 3：导入开通
   */
  terminalCode: number = null

  /**
   * 交易流水号
   */
  flowNo = ''

  /**
   * 培训方案Id集合
   */
  commoditySkuIdList: string[] = []

  /**
   * 订单创建时间
   */
  orderCreateTime: string[] = []

  /**
   * 交易成功时间
   */
  paymentCompleteTime: string[] = []

  /**
   * 买家Id集合
   */
  buyerId = ''

  /**
   * 是否剔除0元单
   */
  isRemoveZeroOrder = false

  /**
   * 是否包含集体订单
   */
  haveCollection = false

  /**
   * 专题名称
   */
  specialSubjectName = ''

  /**
   * 是否来源专题
   */
  isFromSpecialSubject: boolean = undefined

  /**
   * 销售渠道
   */
  saleSource: SaleChannelEnum = null

  /**
   * 分销商id
   */
  distributorId = ''

  /**
   * 推广门户id
   */
  promotionPortalId = ''

  /**
   * 是否非门户推广数据
   */
  isDistributionExcludePortal = false

  /**
   * 期别id
   */
  periodId: string = undefined

  /**
   * 商品品类
   */
  commodityCategory: string = undefined

  /**
   * 转换成微服务查询对象
   * @param {boolean} isBusinessConsult - 是否是业务咨询相关
   */
  async to(isBusinessConsult: boolean): Promise<OrderRequest> {
    const to = new OrderRequest()
    to.orderNoList = this.orderNo ? [this.orderNo] : undefined

    to.issueId = this.periodId ? [this.periodId] : []
    to.commodityCategory = [this.commodityCategory]
    if (this.saleSource || this.saleSource === SaleChannelEnum.self) {
      to.saleChannels = [this.saleSource]
    } else {
      to.saleChannels = [SaleChannelEnum.self, SaleChannelEnum.distribution, SaleChannelEnum.topic]
    }
    // if (typeof this.isFromSpecialSubject === 'boolean') {
    //   to.saleChannels = SaleChannel.getSpecialSubjectSaleChannel(this.isFromSpecialSubject)
    // } else {
    //   to.saleChannel = this.saleChannel
    // }
    to.saleChannelName = to.saleChannels.includes(SaleChannelEnum.topic) ? this.specialSubjectName : ''
    if (isBusinessConsult) {
      to.buyerIdList = this.buyerId ? [this.buyerId] : undefined
    } else {
      to.buyerIdList = await this.getBuyerIdList()
    }
    to.orderBasicData = new OrderBasicDataRequest()
    /** 订单状态 */
    if (this.orderStatus === OrderTransaction.Wait_Pay) {
      // 等待付款：订单状态正常&支付状态未支付
      to.orderBasicData.orderStatusList = [1]
      to.orderBasicData.orderPaymentStatusList = [0]
    } else if (this.orderStatus === OrderTransaction.Paying) {
      // 支付中：订单状态正常&支付状态支付中
      to.orderBasicData.orderStatusList = [1]
      to.orderBasicData.orderPaymentStatusList = [1]
    } else if (this.orderStatus === OrderTransaction.Opening) {
      // 开通中：订单状态正常&支付状态已支付
      to.orderBasicData.orderStatusList = [1]
      to.orderBasicData.orderPaymentStatusList = [2]
    } else if (this.orderStatus === OrderTransaction.Complete_Transaction) {
      // 交易成功：订单状态交易成功
      to.orderBasicData.orderStatusList = [2]
      to.orderBasicData.orderPaymentStatusList = undefined
    } else if (this.orderStatus === OrderTransaction.Close_Transaction) {
      // 交易关闭：订单状态交易关闭
      to.orderBasicData.orderStatusList = [3]
      to.orderBasicData.orderPaymentStatusList = undefined
    } else {
      // 不处理
      to.orderBasicData.orderStatusList = undefined
      to.orderBasicData.orderPaymentStatusList = undefined
    }
    to.deliveryCommodity = new CommoditySkuRequest1()
    to.deliveryCommodity.commoditySkuIdList = QueryOrderListVo.setArrValue(this.commoditySkuIdList)
    to.payInfo = new OrderPayInfoRequest()
    to.payInfo.receiveAccountIdList = QueryOrderListVo.setArrValue(this.receiveAccountIdList)
    to.payInfo.flowNoList = this.flowNo ? [this.flowNo] : undefined

    /** 缴费渠道 */
    if (this.terminalCode === 1) {
      // web端
      to.orderBasicData.terminalCodeList = ['Web']
      to.orderBasicData.channelTypesList = [1]
    } else if (this.terminalCode === 2) {
      // h5端
      to.orderBasicData.terminalCodeList = ['H5', 'WechatOfficial']
      to.orderBasicData.channelTypesList = [1]
    } else if (this.terminalCode === 3) {
      // 导入开通
      to.orderBasicData.terminalCodeList = undefined
      to.orderBasicData.channelTypesList = [3]
    } else {
      to.orderBasicData.terminalCodeList = undefined
      if (this.haveCollection) {
        to.orderBasicData.channelTypesList = [1, 2, 3]
      } else {
        to.orderBasicData.channelTypesList = [1, 3]
      }
    }

    to.orderBasicData.orderStatusChangeTime = new OrderStatusChangeTimeRequest()
    // 订单创建时间
    if (QueryOrderListVo.isWeightyArr(this.orderCreateTime)) {
      to.orderBasicData.orderStatusChangeTime.normalDateScope = new DateScopeRequest()
      to.orderBasicData.orderStatusChangeTime.normalDateScope.begin = this.orderCreateTime[0] || ''
      to.orderBasicData.orderStatusChangeTime.normalDateScope.end = this.orderCreateTime[1] || ''
    } else {
      to.orderBasicData.orderStatusChangeTime.normalDateScope = undefined
    }
    // 交易成功时间
    if (QueryOrderListVo.isWeightyArr(this.paymentCompleteTime)) {
      to.orderBasicData.orderStatusChangeTime.completedDatesScope = new DateScopeRequest()
      to.orderBasicData.orderStatusChangeTime.completedDatesScope.begin = this.paymentCompleteTime[0] || ''
      to.orderBasicData.orderStatusChangeTime.completedDatesScope.end = this.paymentCompleteTime[1] || ''
    } else {
      to.orderBasicData.orderStatusChangeTime.completedDatesScope = undefined
    }
    // 是否剔除0元单
    if (this.isRemoveZeroOrder) {
      to.orderBasicData.orderAmountScope = new DoubleScopeRequest()
      to.orderBasicData.orderAmountScope.begin = 0.01
    } else {
      to.orderBasicData.orderAmountScope = undefined
    }

    if (this.distributorId) {
      to.distributorId = this.distributorId
    }
    if (this.promotionPortalId && !this.isDistributionExcludePortal) {
      to.portalId = this.promotionPortalId
    }

    to.isDistributionExcludePortal = this.isDistributionExcludePortal
    return to
  }

  private static setArrValue<T>(arr: T[]): T[] | undefined {
    return Array.isArray(arr) && arr.length ? arr : undefined
  }

  static isWeightyArr<T>(arr: T[]): boolean {
    return Array.isArray(arr) && arr.length ? true : false
  }

  /**
   * 获取买家id集合
   */
  async getBuyerIdList(): Promise<string[] | undefined> {
    if (!this.idCard && !this.userName && !this.loginAccount) return undefined
    const queryRemote: QueryStudentList = UserModule.queryUserFactory.queryStudentList
    queryRemote.queryStudentIdParams.idCard = this.idCard || undefined
    queryRemote.queryStudentIdParams.userName = this.userName || undefined
    queryRemote.queryStudentIdParams.loginAccount = this.loginAccount || undefined
    const result = await queryRemote.queryStudentIdList()
    if (result.status?.isSuccess()) {
      return QueryOrderListVo.setArrValue(result.data)
    }
    return undefined
  }
}

export default QueryOrderListVo
