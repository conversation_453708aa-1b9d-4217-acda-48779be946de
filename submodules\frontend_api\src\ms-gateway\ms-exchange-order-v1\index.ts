import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-exchange-order-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-exchange-order-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 换货单同意申请请求
<AUTHOR>
 */
export class ExchangeOrderAgreeApplyRequest {
  exchangeOrderNo: string
  approveComment?: string
}

/**
 * 换货单取消换货申请请求
<AUTHOR>
 */
export class ExchangeOrderCancelApplyRequest {
  exchangeOrderNo: string
  cancelReason?: string
}

/**
 * 换货单拒绝申请请求
<AUTHOR>
 */
export class ExchangeOrderRejectApplyRequest {
  exchangeOrderNo: string
  approveComment?: string
}

export class ExchangeOrderRetryDeliveryRequest {
  exchangeOrderNo: string
}

export class ExchangeOrderRetryRecycleRequest {
  exchangeOrderNo: string
}

export class ExchangeOrderAgreeApplyResponse {
  code: string
  message: string
}

export class ExchangeOrderCancelApplyResponse {
  code: string
  message: string
}

export class ExchangeOrderRejectApplyResponse {
  code: string
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 返回所有可供填写的换货原因id和原因描述Map
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getExchangeApplyReason(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getExchangeApplyReason,
    operation?: string
  ): Promise<Response<Map<string, string>>> {
    return commonRequestApi<Map<string, string>>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 同意换货申请
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async agreeExchangeApply(
    request: ExchangeOrderAgreeApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.agreeExchangeApply,
    operation?: string
  ): Promise<Response<ExchangeOrderAgreeApplyResponse>> {
    return commonRequestApi<ExchangeOrderAgreeApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 拒绝换货申请
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async rejectExchangeApply(
    request: ExchangeOrderRejectApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.rejectExchangeApply,
    operation?: string
  ): Promise<Response<ExchangeOrderRejectApplyResponse>> {
    return commonRequestApi<ExchangeOrderRejectApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重试发货
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async retryDelivery(
    request: ExchangeOrderRetryDeliveryRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.retryDelivery,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 重试回收资源
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async retryRecycleResouce(
    request: ExchangeOrderRetryRecycleRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.retryRecycleResouce,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 取消换货申请
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async sellerCancelExchangeApply(
    request: ExchangeOrderCancelApplyRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.sellerCancelExchangeApply,
    operation?: string
  ): Promise<Response<ExchangeOrderCancelApplyResponse>> {
    return commonRequestApi<ExchangeOrderCancelApplyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
