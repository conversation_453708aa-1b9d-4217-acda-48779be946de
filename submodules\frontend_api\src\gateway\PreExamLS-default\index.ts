import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

export const SERVER_URL = '/web/gql/PreExamLS-default'

// 枚举

// 类

export class UpDownSettingsDto {
  upping: boolean
  upPlainTime?: string
  downPlainTime?: string
}

/**
 * 每日一练学习Token信息
 */
export class ApplyDailyPracticeLearningTokenRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
}

/**
 * 考试Token信息
 */
export class ApplyExamLearningTokenRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
}

/**
 * 考纲结构易错题学习Token
 */
export class ApplyExamOutlineErrorPronePracticeLearningTokenRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
  /**
   * 章节ID
   */
  chaptersId?: string
}

/**
 * 申请考纲模式的练习学习Token
 */
export class ApplyExamOutlinePracticeLearningTokenRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
  /**
   * 章节ID
   */
  chaptersId?: string
  /**
   * 题类 null全部，real真题，practice练习题，simulation模拟题
   */
  questionCategory?: string
}

/**
 * 题型方式易错题学习Token
 */
export class ApplyQuestionTypeErrorPronePracticeLearningTokenRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
  /**
   * 专业ID
   */
  professionId?: string
  /**
   * 题型,-1全部,1判断题,2单选题,3多选题,6案例题
   */
  questionType: number
}

export class ApplyRandomPracticeLearningTokenRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
  /**
   * 抽题模式,1:未答优先其次作答次数较少优先,2:未答,3:已答且次数较少优先
   */
  extractMode: number
  /**
   * 抽题总数
   */
  questionCount: number
  /**
   * 题型,-1全部,1判断题,2单选题,3多选题,6案例题
   */
  questionType: number
}

/**
 * 申请课程学习Token信息
 */
export class ApplySingleCourseLearningTokenRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数ID
   */
  issueId?: string
  /**
   * 课程包ID
   */
  packageId?: string
  /**
   * 课程ID
   */
  courseId?: string
}

/**
 * 课程学习方式设置信息
 */
export class CourseLearningSettingsRequest {
  /**
   * 是否启用课程学习方式
   */
  enabled: boolean
  /**
   * 课程包ID集合
   */
  packageIds?: Array<string>
  /**
   * 是否仅系统推送
   */
  systemPush: boolean
}

/**
 * 每日一练配置信息
 */
export class DailyPracticeSettingsRequest {
  /**
   * 是否启用每日一练
   */
  enabled: boolean
}

/**
 * 易错题练习配置
 */
export class ErrorPronePracticeSettingsRequest {
  /**
   * 是否启用易错题练习
   */
  enabled: boolean
}

/**
 * 考试学习方式设置信息
 */
export class ExamLearningSettingsRequest {
  /**
   * 是否启用考试学习方式
   */
  enabled: boolean
  /**
   * 考试名称
   */
  name?: string
  /**
   * 试卷ID
   */
  examPaperId?: string
  /**
   * 考试时长，单位分钟
   */
  examTimeLength: number
  /**
   * 考试次数 0表示不限制次数
   */
  examCount: number
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 是否开放试题解析
   */
  openResolvedExam: boolean
  /**
   * 最短提交时长
   */
  minSubmitTimeLength: number
}

export class PreExamLSCreateRequest {
  /**
   * 培训方案名称
   */
  name?: string
  /**
   * 考试类别ID
   */
  examTypeId?: string
  /**
   * 考试类别名称
   */
  examTypeName?: string
  /**
   * 专业ID
   */
  professionId?: string
  /**
   * 专业名称
   */
  professionName?: string
  /**
   * 年度
   */
  year: number
  /**
   * 封面图片地址
   */
  picture?: string
  /**
   * 课程学习方式设置信息
   */
  courseLearningSettings?: CourseLearningSettingsRequest
  /**
   * 考试学习方式设置信息
   */
  examLearningSettings?: ExamLearningSettingsRequest
  /**
   * 试题练习学习方式配置
   */
  questionPracticeLearningSettings?: QuestionPracticeLearningSettingsRequest
  /**
   * 每日一练配置
   */
  dailyPracticeSettings?: DailyPracticeSettingsRequest
  /**
   * 易错题配置
   */
  errorPronePracticeSettings?: ErrorPronePracticeSettingsRequest
}

/**
 * 考前学习方案期数创建信息
 */
export class PreExamLSIssueCreateRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 期数名称
   */
  title?: string
  /**
   * 培训开始时间
   */
  startTime?: string
  /**
   * 培训结束时间
   */
  endTime?: string
  /**
   * 线下考试时间
   */
  offlineExamTime?: string
  /**
   * 是否开放报名
   */
  openEnrolment: boolean
  /**
   * 排序
   */
  sort: number
  /**
   * 销售价格
   */
  price?: number
  /**
   * 上下架设置
   */
  upDownSettings?: UpDownSettingsRequest
}

/**
 * 考前学习方案期数更新信息
 */
export class PreExamLSIssueUpdateRequest {
  /**
   * 要更新期数的学习方案ID
   */
  schemeId?: string
  /**
   * 要更新的期数ID
   */
  issueId?: string
  /**
   * 期数名称
   */
  title?: string
  /**
   * 培训开始时间
   */
  startTime?: string
  /**
   * 培训结束时间
   */
  endTime?: string
  /**
   * 线下考试时间
   */
  offlineExamTime?: string
  /**
   * 排序
   */
  sort: number
  /**
   * 是否开放报名
   */
  openEnrolment: boolean
  /**
   * 销售价格
   */
  price?: number
  /**
   * 上下架设置
   */
  upDownSettings?: UpDownSettingsDto
}

export class PreExamLSUpdateRequest {
  /**
   * 学习方案ID
   */
  schemeId?: string
  /**
   * 培训方案名称
   */
  name?: string
  /**
   * 封面图片地址
   */
  picture?: string
  /**
   * 课程学习方式设置信息
   */
  courseLearningSettings?: CourseLearningSettingsRequest
  /**
   * 考试学习方式设置信息
   */
  examLearningSettings?: ExamLearningSettingsRequest
  /**
   * 试题练习学习方式配置
   */
  questionPracticeLearningSettings?: QuestionPracticeLearningSettingsRequest
  /**
   * 每日一练配置
   */
  dailyPracticeSettings?: DailyPracticeSettingsRequest
  /**
   * 易错题配置
   */
  errorPronePracticeSettings?: ErrorPronePracticeSettingsRequest
}

/**
 * 试题练习学习方式配置
 */
export class QuestionPracticeLearningSettingsRequest {
  /**
   * 是否启用试题练习
   */
  enabled: boolean
  /**
   * 抽题方式，1：指定考纲方式，2：按试题题型及抽题比例
   */
  fetchWay: number
  /**
   * 是否开放真题
   */
  openReal: boolean
  /**
   * 是否开放练习题
   */
  openPractice: boolean
  /**
   * 是否开放模拟题
   */
  openSimulation: boolean
  /**
   * 真题比率
   */
  realRatioValue: number
  /**
   * 练习题比率
   */
  practiceRatioValue: number
  /**
   * 模拟题比率
   */
  simulationRatioValue: number
}

/**
 * 商品SKU上下架设置
 */
export class UpDownSettingsRequest {
  /**
   * 是否上架
true: 如果商品SKU处于下架状态将进行立即上架
false: 如果商品SKU处于上架状态将进行立即下架
   */
  upping: boolean
  /**
   * 计划上架时间
如果 upping 为 true 该值无效
   */
  upPlainTime?: string
  /**
   * 计划下架时间
如果 upping 为 false 该值无效
   */
  downPlainTime?: string
}

/**
 * 课程学习方式信息
 */
export class CourseLearningResponse {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 是否启用课程学习方式
   */
  enabled: boolean
  /**
   * 课程包ID集合
   */
  packageIds: Array<string>
  /**
   * 是否仅系统推送
   */
  systemPush: boolean
}

/**
 * 每日一练信息
 */
export class DailyPracticeResponse {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 是否启用每日一练
   */
  enabled: boolean
}

/**
 * 易错题练习信息
 */
export class ErrorPronePracticeResponse {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 是否启用易错题练习
   */
  enabled: boolean
}

/**
 * 考试学习方式信息
 */
export class ExamLearningResponse {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 是否启用考试学习方式
   */
  enabled: boolean
  /**
   * 考试名称
   */
  name: string
  /**
   * 试卷ID
   */
  examPaperId: string
  /**
   * 考试时长，单位分钟
   */
  examTimeLength: number
  /**
   * 考试次数 0表示不限制次数
   */
  examCount: number
  /**
   * 及格分数
   */
  passScore: number
  /**
   * 是否开放试题解析
   */
  openResolvedExam: boolean
  /**
   * 最短提交时长
   */
  minSubmitTimeLength: number
}

/**
 * 期数和商品SKU信息
 */
export class IssueAndCommoditySkuResponse {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 期数ID
   */
  issueId: string
  /**
   * 商品SkuId
   */
  commoditySkuId: string
  /**
   * 标题
   */
  title: string
  /**
   * 开始时间
   */
  startTime: string
  /**
   * 结束时间
   */
  endTime: string
  /**
   * 线下考试时间
   */
  offlineExamTime: string
  /**
   * 是否开放报名
   */
  openEnrolment: boolean
  /**
   * 销售价格
   */
  price: number
  /**
   * 上下架设置
   */
  upDownSettings: UpDownSettingsResponse
}

/**
 * 考前学习方案信息
 */
export class PreExamLSResponse {
  /**
   * 学习方案ID
   */
  schemeId: string
  /**
   * 培训方案名称
   */
  name: string
  /**
   * 考试类别ID
   */
  examTypeId: string
  /**
   * 专业ID
   */
  professionId: string
  /**
   * 年度
   */
  year: number
  /**
   * 封面图片地址
   */
  picture: string
  /**
   * 课程学习方式信息
   */
  courseLearning: CourseLearningResponse
  /**
   * 考试学习方式信息
   */
  examLearning: ExamLearningResponse
  /**
   * 试题练习学习方式信息
   */
  questionPracticeLearning: QuestionPracticeLearningResponse
  /**
   * 每日一练信息
   */
  dailyPractice: DailyPracticeResponse
  /**
   * 易错题练习信息
   */
  errorPronePractice: ErrorPronePracticeResponse
  /**
   * 创建人ID
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
}

/**
 * 试题练习学习方式信息
 */
export class QuestionPracticeLearningResponse {
  /**
   * 学习方式ID
   */
  learningId: string
  /**
   * 是否启用试题练习
   */
  enabled: boolean
  /**
   * 抽题方式，1：指定考纲方式，2：按试题题型及抽题比例
   */
  fetchWay: number
  /**
   * 是否开放真题
   */
  openReal: boolean
  /**
   * 是否开放练习题
   */
  openPractice: boolean
  /**
   * 是否开放模拟题
   */
  openSimulation: boolean
  /**
   * 真题比率
   */
  realRatioValue: number
  /**
   * 练习题比率
   */
  practiceRatioValue: number
  /**
   * 模拟题比率
   */
  simulationRatioValue: number
}

/**
 * 商品SKU上下架设置
 */
export class UpDownSettingsResponse {
  /**
   * 是否上架
true: 如果商品SKU处于下架状态将进行立即上架
false: 如果商品SKU处于上架状态将进行立即下架
   */
  upping: boolean
  /**
   * 计划上架时间
如果 upping 为 true 该值无效
   */
  upPlainTime: string
  /**
   * 计划下架时间
如果 upping 为 false 该值无效
   */
  downPlainTime: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取指定的考前培训方案
   * @param schemeId 学习方案ID
   * @param query 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async findById(
    schemeId: string,
    query: DocumentNode = GraphqlImporter.findById,
    operation?: string
  ): Promise<Response<PreExamLSResponse>> {
    return commonRequestApi<PreExamLSResponse>(SERVER_URL, {
      query: query,
      variables: { schemeId },
      operation: operation
    })
  }

  /**   * 申请每日一练Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyDailyPracticeLearningToken(
    applyInfo: ApplyDailyPracticeLearningTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applyDailyPracticeLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**   * 申请考试Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyExamLearningToken(
    applyInfo: ApplyExamLearningTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applyExamLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**   * 申请考纲结构易错题Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyExamOutlineErrorPronePracticeLearningToken(
    applyInfo: ApplyExamOutlineErrorPronePracticeLearningTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applyExamOutlineErrorPronePracticeLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**   * 申请考纲模式试题练习Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyExamOutlinePracticeLearningToken(
    applyInfo: ApplyExamOutlinePracticeLearningTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applyExamOutlinePracticeLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**   * 申请试题方式易错题Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyQuestionTypeErrorPronePracticeLearningToken(
    applyInfo: ApplyQuestionTypeErrorPronePracticeLearningTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applyQuestionTypeErrorPronePracticeLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**   * 申请随机抽题模式试题练习Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyRandomPracticeLearningToken(
    applyInfo: ApplyRandomPracticeLearningTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applyRandomPracticeLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**   * 申请课程学习Token
   * @param applyInfo 申请信息
   * @param mutate 查询 graphql 语法文档
   * @param applyInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applySingleCourseLearningToken(
    applyInfo: ApplySingleCourseLearningTokenRequest,
    mutate: DocumentNode = GraphqlImporter.applySingleCourseLearningToken,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(SERVER_URL, {
      query: mutate,
      variables: { applyInfo },
      operation: operation
    })
  }

  /**   * 在指定的考前学习方案下新增期数
   * @param createInfo 期数创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createIssue(
    createInfo: PreExamLSIssueCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createIssue,
    operation?: string
  ): Promise<Response<IssueAndCommoditySkuResponse>> {
    return commonRequestApi<IssueAndCommoditySkuResponse>(SERVER_URL, {
      query: mutate,
      variables: { createInfo },
      operation: operation
    })
  }

  /**   * 创建一个考前学习方案
   * @param createInfo 创建信息
   * @param mutate 查询 graphql 语法文档
   * @param createInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async createLS(
    createInfo: PreExamLSCreateRequest,
    mutate: DocumentNode = GraphqlImporter.createLS,
    operation?: string
  ): Promise<Response<PreExamLSResponse>> {
    return commonRequestApi<PreExamLSResponse>(SERVER_URL, {
      query: mutate,
      variables: { createInfo },
      operation: operation
    })
  }

  /**   * 发布考前学习方案
   * @param schemeId 学习方案ID
   * @param mutate 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async publishLS(
    schemeId: string,
    mutate: DocumentNode = GraphqlImporter.publishLS,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { schemeId },
      operation: operation
    })
  }

  /**   * 删除指定期数
   * @param schemeId 学习方案ID
   * @param issueId 期数ID
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeIssue(
    params: { schemeId?: string; issueId?: string },
    mutate: DocumentNode = GraphqlImporter.removeIssue,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: params,
      operation: operation
    })
  }

  /**   * 删除学习方案
   * @param schemeId 学习方案ID
   * @param mutate 查询 graphql 语法文档
   * @param schemeId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async removeLS(
    schemeId: string,
    mutate: DocumentNode = GraphqlImporter.removeLS,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(SERVER_URL, {
      query: mutate,
      variables: { schemeId },
      operation: operation
    })
  }

  /**   * 更新考前学习方案指定的期数
   * @param updateInfo 期数更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateIssue(
    updateInfo: PreExamLSIssueUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateIssue,
    operation?: string
  ): Promise<Response<IssueAndCommoditySkuResponse>> {
    return commonRequestApi<IssueAndCommoditySkuResponse>(SERVER_URL, {
      query: mutate,
      variables: { updateInfo },
      operation: operation
    })
  }

  /**   * 更新一个考前学习方案信息
   * @param updateInfo 更新信息
   * @param mutate 查询 graphql 语法文档
   * @param updateInfo 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async updateLS(
    updateInfo: PreExamLSUpdateRequest,
    mutate: DocumentNode = GraphqlImporter.updateLS,
    operation?: string
  ): Promise<Response<PreExamLSResponse>> {
    return commonRequestApi<PreExamLSResponse>(SERVER_URL, {
      query: mutate,
      variables: { updateInfo },
      operation: operation
    })
  }
}

export default new DataGateway()
