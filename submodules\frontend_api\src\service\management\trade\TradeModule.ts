import store from '@/store'
import { getModule, Module, VuexModule } from 'vuex-module-decorators'
import SingleTradeBatchFactor from './single/SingleTradeBatchFactor'
import BatchTradeBatchFactor from './batch/BatchTradeBatchFactor'

/**
 * @description: 交易状态层
 */
@Module({ namespaced: true, dynamic: true, name: 'TradeModule', store })
class TradeModule extends VuexModule {
  // 【个人】
  singleTradeBatchFactor: SingleTradeBatchFactor = new SingleTradeBatchFactor()
  // 【集体】
  batchTradeBatchFactor: BatchTradeBatchFactor = new BatchTradeBatchFactor()
}

export default getModule(TradeModule)
