<!--
 * @Author: lixin<PERSON> <EMAIL>
 * @Date: 2024-03-20 15:51:59
 * @LastEditors: lixinye <EMAIL>
 * @LastEditTime: 2024-04-07 09:44:45
 * @FilePath: \jxjyv2_frontend_web_admin\src\unit-share\network-school\training\special-topics\manage\components\__components__\premium-course-components\no-classified.vue
 * @Description: 有分类表格
-->
<template>
  <div v-if="$hasPermission('addDrawer')" desc="查询精品课程列表" actions="doQuery">
    <el-drawer title="添加精品课程" :visible.sync="showDrawer" direction="rtl" size="1000px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-row :gutter="16" class="m-query f-mt10">
          <el-form :inline="true" label-width="auto">
            <el-col :span="10">
              <el-form-item label="课程名称">
                <el-input v-model="queryParam.name" clearable placeholder="请输入课程名称" />
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item label="课程分类">
                <biz-course-category
                  :filterable="true"
                  v-model="categoryIdList"
                  :showRootNode="false"
                  placeholder="全部"
                ></biz-course-category>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <template v-if="$hasPermission('query')" desc="查询精品课程列表" actions="doQuery">
                  <el-button
                    type="primary"
                    @click="page.currentChange(1)"
                    :loading="searchLoading"
                    :disabled="saveLoading"
                    >查询</el-button
                  >
                </template>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <!--表格-->
        <el-table stripe :data="tableData" max-height="500px" class="m-table f-mt10" v-loading="searchLoading">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="课程名称" min-width="300">
            <template slot-scope="{ row }">
              <div class="is-cache" v-if="isNewSelected(row)"></div>
              <span>{{ row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="学时" min-width="100" align="center">
            <template slot-scope="{ row }">{{ row.period }}</template>
          </el-table-column>
          <el-table-column label="操作" width="150" align="center" fixed="right">
            <template slot-scope="{ row }">
              <el-button type="text" size="mini" @click="toDetail(row)">详情</el-button>
              <span style="margin:0px 5px;color:#1f86f0">|</span>
              <el-button type="text" size="mini" @click="chooseOrNot(row)">{{
                hasSelected(row) ? '取消选择' : '选择'
              }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
        <div class="select-course-foot">
          <el-button @click="drawerOpration(false)">取消</el-button>
          <template v-if="$hasPermission('save')" desc="保存精品课程列表" actions="save">
            <el-button v-if="!allNewSelectedCount" @click="save" type="primary" :loading="saveLoading">保存</el-button>
            <el-badge v-else :value="allNewSelectedCount">
              <el-button @click="save" type="primary" :loading="saveLoading">保存</el-button>
            </el-badge>
          </template>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import { UiPage } from '@hbfe/common'
  import ThematicMangementCourseItem from '@api/service/management/thematic-management/model/ThematicMangementCourseItem'
  import {
    UpdatePremium,
    DrawerSearchParam as SearchParam,
    MyTools as ModelChanger
  } from '@hbfe/jxjy-admin-specialTopics/src//manage/components/components/premium-course-components/model/SavePremium'
  import ThematicMangementCourseList from '@api/service/management/thematic-management/ThematicMangementCourseList'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  @Component({
    components: {}
  })
  export default class extends Vue {
    // ========================= 变量 =========================

    /**
     * @description 是否展示弹窗
     * */
    showDrawer = false

    /**
     * @description 查询入参
     * */
    queryParam: SearchParam = new SearchParam()

    /**
     * @description 分页
     * */
    page: UiPage
    constructor() {
      super()
      this.page = new UiPage(this.doQuery, this.doQuery)
    }

    /**
     * @description 分类id列表
     * */
    categoryIdList: Array<string> = new Array<string>()

    /**
     * @description 查询加载
     * */
    searchLoading = false

    /**
     * @description 渲染数据
     * */
    tableData = new Array<ThematicMangementCourseItem>()

    /**
     * @description 新选中的数据
     * */
    selectNewData = new Array<ThematicMangementCourseItem>()

    /**
     * @description 被删除的数据
     * */
    delPrevData = new Array<ThematicMangementCourseItem>()

    /**
     * @description 打开弹窗时，已添加最大的数据索引
     * */
    addIndex = 0

    /**
     * @description 模型转换
     * */
    modelChanger = new ModelChanger()

    /**
     * @description 保存加载
     * */
    saveLoading = false

    /**
     * @description 列表查询类
     * */
    queryObj = new ThematicMangementCourseList()

    /**
     * @description 列表更新类
     * */
    updatetObj = new ThematicManagementItem()

    // ========================= 生命周期 =========================

    created() {
      //   this.doQuery()
    }

    // ========================= 计算属性 =========================

    /**
     * @description 判断是否选中
     * @return true 选中 false 未选中
     * */
    get hasSelected() {
      return (row: ThematicMangementCourseItem) => {
        // 查询回来的选中状态
        const defaultSelect = row.selected
        // 比对唯一标识（id），判断是否选中
        // 新选中的结果
        const selectResult =
          this.selectNewData.filter(item => {
            return row.courseId === item.courseId
          }).length > 0
        // 原先选中但是删除的结果
        const delResult =
          this.delPrevData.filter(item => {
            return row.courseId === item.courseId
          }).length > 0
        return (defaultSelect && !delResult) || (selectResult && !defaultSelect)
      }
    }

    /**
     * @description 判断是否为新选中
     * @return true 选中 false 未选中
     * */
    get isNewSelected() {
      return (row: ThematicMangementCourseItem) => {
        // 查询回来是选中状态
        const defaultSelect = row.selected
        // 比对唯一标识（id），判断是否选中
        const result =
          this.selectNewData.filter(item => {
            return row.courseId === item.courseId
          }).length > 0
        return !defaultSelect && result
      }
    }

    /**
     * @description 返回新选中长度
     * */
    get allNewSelectedCount() {
      return this.selectNewData.length
    }

    // ========================= 变量 =========================

    /**
     * @description 查询数据
     * */
    async doQuery() {
      try {
        this.searchLoading = true
        // 课程分类入参，取末级
        if (this.categoryIdList?.length) {
          this.queryParam.categoryIdList = [this.categoryIdList[this.categoryIdList.length - 1]]
        } else {
          this.queryParam.categoryIdList = []
        }
        console.log(this.$route, 'this.$route')

        this.queryObj.trainingChannelId = (this.$route.query.id as string) || (this.$route.params.id as string)
        this.tableData = await this.queryObj.queryCourseList(this.page, this.queryParam)
      } catch (error) {
        this.$message.error('查询列表请求失败！')
        console.log('查询列表请求失败！', error)
      } finally {
        this.searchLoading = false
      }
    }

    /**
     * @description 操作弹窗
     * @param status true 打开弹窗 false 关闭弹窗
     * */
    drawerOpration(status: boolean, index?: number) {
      if (status) {
        this.delPrevData = new Array<ThematicMangementCourseItem>()
        this.selectNewData = new Array<ThematicMangementCourseItem>()
        this.doQuery()
      }
      this.addIndex = index
      this.showDrawer = status
    }

    /**
     * @description 前往详情
     * */
    toDetail(row: ThematicMangementCourseItem) {
      this.showDrawer = false
      this.$router.push(`/resource/course/detail/${row.courseId}`)
    }

    /**
     * @description 选择/取消选择
     * */
    chooseOrNot(row: ThematicMangementCourseItem) {
      // 判断是否是选中状态
      const selected = this.hasSelected(row)
      // 是否是新选中
      const isNewSelect = this.isNewSelected(row)
      console.log(selected, '========selected')
      console.log(isNewSelect, '===============isNewSelect')
      // =====选中，做取消选中=====
      if (selected && isNewSelect) {
        // 是新选中 -> 从新选中列中删除
        this.selectNewData.map((item, index) => {
          if (item.courseId == row.courseId) {
            this.selectNewData.splice(index, 1)
          }
        })
        this.resetSort(this.selectNewData)
      }
      if (selected && !isNewSelect) {
        // 不是新选中 -> 加入删除列
        this.delPrevData.push(row)
      }
      // =====未选中，做选中=====
      if (!selected && row.selected) {
        // 选中原先选中的 -> 从删除列移除
        this.delPrevData.map((item, index) => {
          if (item.courseId == row.courseId) {
            this.delPrevData.splice(index, 1)
          }
        })
      }
      if (!selected && !row.selected) {
        // 选中原先未选中的 -> 加入新选择列
        this.selectNewData.push(row)
        this.resetSort(this.selectNewData)
      }
      console.log(this.addIndex, '============newsel==index')
      console.log(this.selectNewData, '============newsel')
    }

    /**
     * @description 保存
     * */
    async save() {
      // 保存
      if (!this.selectNewData.length && !this.delPrevData.length) return

      try {
        this.saveLoading = true
        const param = new UpdatePremium()
        param.addCourse = this.modelChanger.toSelectedCourse(this.selectNewData)
        param.deleteCourse = this.modelChanger.toDeleteSelectedCourse(this.delPrevData)
        // 无分类默认传-1
        param.courseCategoryId = '-1'
        this.updatetObj.topicID = (this.$route.query.id as string) || (this.$route.params.id as string)
        const res = await this.updatetObj.saveTrainingChannelCourse([param])
        if (res.isSuccess()) {
          this.$message.success('保存成功！')
          this.$emit('saved', true)
          this.drawerOpration(false)
        } else {
          throw new Error('保存失败！')
        }
      } catch (error) {
        this.$message.error('保存失败！')
        console.log('弹窗保存失败:func:saveCourse >>', error)
      } finally {
        this.saveLoading = false
      }
    }

    /**
     * @description 重置列表排序
     * */
    resetSort(list: Array<ThematicMangementCourseItem>) {
      list.map((item, i) => {
        item.sort = list.length + this.addIndex - i
      })
    }
  }
</script>

<style scoped>
  .is-cache {
    width: 8px;
    background: #e89820;
    height: 8px;
    border-radius: 50%;
    display: inline-flex;
    align-items: flex-start;
    justify-content: flex-start;
    margin-right: 5px;
  }

  .select-course-foot {
    text-align: center;
    position: absolute;
    bottom: 0;
    width: 100%;
    padding: 10px;
  }
</style>
