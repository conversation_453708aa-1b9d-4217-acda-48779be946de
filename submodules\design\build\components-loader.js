const loaderUtil = require('loader-utils')
const { transformPath, translation } = require('./utils')
const { sync } = require('glob')

function getContent(query) {
  const importContent = []
  let exportContent = ''
  let fileContent = ''
  sync('**/*.vue', {
    cwd: './src'
  }).forEach(file => {
    if (!new RegExp(`^${query.app}${query.sub !== 'undefined' ? '/' + query.sub : ''}`).test(file)) {
      return
    }
    let thePath = transformPath(file)
    thePath = translation(thePath)
    importContent.push(`import ${thePath} from '@/${file}'`)
    exportContent += `  ${thePath},\n`
  })
  if (!importContent.length) {
    fileContent = 'export default {}\n'
  } else {
    fileContent = `${importContent.join('\n')}\n\nexport default {\n${exportContent.replace(/,\n$/, '')}\n}\n`
  }
  return fileContent
}

module.exports = function(source) {
  const query = loaderUtil.parseQuery(this.resourceQuery)
  source = getContent(query)
  return source
}
