import InvoiceFactor from '@api/service/management/trade/batch/invoice/InvoiceFactor'
import CheckAccountFactor from './checkAccount/CheckAccountFactor'
import OrderFactor from './order/OrderFactor'
class TradeBatchFactor {
  /**
   * 集体 发票工厂
   */
  invoiceFactor = new InvoiceFactor()
  /**
   * 集体 订单工厂
   */
  orderFactor = new OrderFactor()

  /**
   * 集体 对账查询
   */
  checkAccountFactor = new CheckAccountFactor()
}
export default TradeBatchFactor
