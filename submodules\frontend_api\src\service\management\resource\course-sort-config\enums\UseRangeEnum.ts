import AbstractEnum from '@api/service/common/enums/AbstractEnum'
import { EffectiveScopeEnum } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'

class UseRangeEnum extends AbstractEnum<EffectiveScopeEnum> {
  static enum = EffectiveScopeEnum

  constructor(status?: EffectiveScopeEnum) {
    super()
    this.current = status
    this.map.set(EffectiveScopeEnum.PLATFORM, '全平台')
  }
}

export default UseRangeEnum
