<template>
  <el-main>
    <el-alert type="warning" show-icon :closable="false" class="m-alert f-ptb15">
      <div class="f-fl f-mr50">温馨提示：当前网校未开启web、h5访问。请完成网校配置后开启对外访问。</div>
      <div class="f-flex f-fl">
        <div class="f-mr50">
          <i class="f-vm f-c6">Web 访问</i>
          <el-switch v-model="switch1" class="f-ml10"></el-switch>
        </div>
        <div class="f-mr50">
          <i class="f-vm f-c6">H5 访问</i>
          <el-switch v-model="switch2" class="f-ml10"></el-switch>
        </div>
      </div>
    </el-alert>
    <!--顶部tab标签-->
    <el-tabs v-model="activeName" class="m-tab-top is-sticky">
      <el-tab-pane label="Web 端" name="first">
        <div class="f-p15">
          <el-tabs v-model="activeName2" type="card" class="m-tab-card">
            <div class="tab-right">
              <el-button type="primary" size="medium" class="f-mr15">
                <i class="hb-iconfont icon-complelearn f-mr5"></i>预览
              </el-button>
            </div>
            <el-tab-pane label="门户信息配置" name="first">
              <el-card shadow="never" class="m-card f-mb15">详见 0201_基础信息配置_门户信息配置.vue</el-card>
            </el-tab-pane>
            <el-tab-pane label="栏目设置" name="second">
              <el-card shadow="never" class="m-card f-mb15">
                <div class="m-web-column">
                  <div class="m-tit">
                    <span class="tit-txt">一级栏目</span>
                  </div>
                  <el-row :gutter="20">
                    <el-col :span="6">
                      <div class="item">
                        <div class="item-bd">
                          <div class="f-flex f-flex-sub f-align-start">
                            <span class="name">报名培训</span>
                          </div>
                        </div>
                        <div class="item-ft">
                          <el-button type="text">修改名称</el-button>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="item">
                        <div class="item-bd">
                          <div class="f-flex f-flex-sub f-align-start">
                            <span class="name">培训通知</span>
                            <el-tag type="danger" size="mini">资讯类别</el-tag>
                          </div>
                        </div>
                        <div class="item-ft">
                          <el-button type="text">修改名称</el-button>
                        </div>
                      </div>
                    </el-col>
                    <!--点击修改名称后-->
                    <el-col :span="6">
                      <div class="item">
                        <div class="item-bd">
                          <div class="f-flex f-flex-sub f-align-start">
                            <span class="name">政策法规</span>
                            <el-tag type="danger" size="mini">资讯类别</el-tag>
                            <el-input v-model="input1" size="medium" clearable class="ipt" />
                          </div>
                        </div>
                        <div class="item-ft">
                          <el-button type="text">取消</el-button>
                          <el-button type="text">保存</el-button>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="item">
                        <div class="item-bd">
                          <div class="f-flex f-flex-sub f-align-start">
                            <span class="name">精品课程</span>
                          </div>
                        </div>
                        <div class="item-ft">
                          <el-button type="text">修改名称</el-button>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="item">
                        <div class="item-bd">
                          <div class="f-flex f-flex-sub f-align-start">
                            <span class="name">帮助中心</span>
                            <el-tag type="danger" size="mini">资讯类别</el-tag>
                          </div>
                        </div>
                        <div class="item-ft">
                          <el-button type="text">收起二级栏目</el-button>
                          <el-button type="text">修改名称</el-button>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="item">
                        <div class="item-bd">
                          <div class="f-flex f-flex-sub f-align-start">
                            <span class="name">消息</span>
                            <el-tag type="danger" size="mini">资讯类别</el-tag>
                          </div>
                        </div>
                        <div class="item-ft">
                          <el-button type="text">修改名称</el-button>
                        </div>
                      </div>
                    </el-col>
                  </el-row>
                  <div class="m-sub-tit">
                    <span class="tit-txt">帮助中心二级栏目</span>
                    <a href="#" class="f-link f-cb f-ml10">[收起]</a>
                  </div>
                  <el-row :gutter="15">
                    <el-col :span="6">
                      <div class="item">
                        <div class="item-hd"><el-tag type="info" size="mini" class="f-mr5">父级</el-tag>帮助中心</div>
                        <div class="item-bd">
                          <div class="f-flex f-flex-sub f-align-start">
                            <span class="name">常见问题常见问题常见</span>
                          </div>
                        </div>
                        <div class="item-ft">
                          <el-button type="text">修改名称</el-button>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="item">
                        <div class="item-hd"><el-tag type="info" size="mini" class="f-mr5">父级</el-tag>帮助中心</div>
                        <div class="item-bd">
                          <div class="f-flex f-flex-sub f-align-start">
                            <span class="name">操作指南</span>
                          </div>
                        </div>
                        <div class="item-ft">
                          <el-button type="text">修改名称</el-button>
                        </div>
                      </div>
                    </el-col>
                    <!--点击修改名称后-->
                    <el-col :span="6">
                      <div class="item">
                        <div class="item-hd"><el-tag type="info" size="mini" class="f-mr5">父级</el-tag>帮助中心</div>
                        <div class="item-bd">
                          <div class="f-flex f-flex-sub f-align-start">
                            <span class="name">政策法规政策法规政策法规</span>
                            <el-input v-model="input1" size="medium" clearable class="ipt" />
                          </div>
                        </div>
                        <div class="item-ft">
                          <el-button type="text">取消</el-button>
                          <el-button type="text">保存</el-button>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="item">
                        <div class="item-hd"><el-tag type="info" size="mini" class="f-mr5">父级</el-tag>帮助中心</div>
                        <div class="item-bd">
                          <div class="f-flex f-flex-sub f-align-start">
                            <span class="name">自定义名称</span>
                          </div>
                        </div>
                        <div class="item-ft">
                          <el-button type="text">禁用</el-button>
                          <el-button type="text">修改名称</el-button>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <!--禁用样式-->
                      <div class="item disabled">
                        <div class="item-hd"><el-tag type="info" size="mini" class="f-mr5">父级</el-tag>帮助中心</div>
                        <div class="item-bd">
                          <div class="f-flex f-flex-sub f-align-start">
                            <span class="name">自定义名称</span>
                          </div>
                        </div>
                        <div class="item-ft">
                          <el-button type="text">启用</el-button>
                          <el-button type="text">修改名称</el-button>
                        </div>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <!--新增-->
                      <div class="item add">
                        新增二级栏目
                      </div>
                    </el-col>
                  </el-row>
                </div>
              </el-card>
            </el-tab-pane>
            <el-tab-pane label="轮播图设置" name="third">
              <el-card shadow="never" class="m-card f-mb15">详见 0203_基础信息配置_轮播图设置.vue</el-card>
            </el-tab-pane>
            <el-tab-pane label="风格设置" name="fourth">
              <el-card shadow="never" class="m-card f-mb15">详见 0205_基础信息配置_风格设置.vue</el-card>
            </el-tab-pane>
            <el-tab-pane label="网校SEO配置" name="fifth">
              <el-card shadow="never" class="m-card f-mb15">
                详见 0209_基础信息配置_网校SEO配置.vue
              </el-card>
            </el-tab-pane>
          </el-tabs>
        </div>
      </el-tab-pane>
      <el-tab-pane label="移动端" name="second">
        <div class="f-p15">详见 0207_基础信息配置_移动学习.vue</div>
      </el-tab-pane>
    </el-tabs>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        switch1: false,
        switch2: false,
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'second',
        input: '',
        input1: '政策法规',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
