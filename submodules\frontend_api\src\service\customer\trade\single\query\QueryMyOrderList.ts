import TradeQueryBackstage from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import {
  getOfflineInvoiceInServicer,
  getOnlineInvoiceInServicer
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage/graphql-importer'
import {
  CommoditySkuRequest1,
  default as MsTradeQueryFrontGatewayCourseLearningForestage,
  OfflineInvoiceResponse,
  OnlineInvoiceResponse,
  OrderBasicDataRequest,
  OrderFixQueryRequest,
  OrderRequest,
  OrderResponse,
  OrderSortField,
  OrderSortRequest,
  Page,
  ReturnOrderResponse,
  SortPolicy,
  default as TradeQueryForestage,
  SchemeResourceResponse,
  SubOrderResponse
} from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryForestage'
import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'
import { RewriteGraph } from '@api/service/common/utils/RewriteGraph'
import ThematicMap from '@api/service/customer/thematic-config/ThematicMap'
import { QueryOrderStatusEnum } from '@api/service/customer/trade/single/enum/QueryOrderStatusEnum'
import InvoiceListResponse from '@api/service/customer/trade/single/invoice/query/vo/InvoiceListResponse'
import MyOrderVo from '@api/service/customer/trade/single/query/vo/MyOrderVo'
import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
import { Page as CommonPage } from '@hbfe/common'
import { InvoiceStatusEnum } from '../invoice/enum/InvoiceEnum'
import GetOrderInMyselfgraphql from './graphql/getOrderInMyself.graphql'
import OrderInvoiceApplyInfoResponseVo from './query-customer-user-order/query-order-detail/vo/OrderInvoiceApplyInfoResponseVo'
import ReturnOrderResponseVo from './vo/ReturnOrderResponseVo'
import CommodityRefundStatus, {
  CommodityRefundStatusEnum,
  CommodityRefundStatusWithGoods
} from '@api/service/common/return-order/enums/CommodityRefundStatus'
export default class QueryMyOrderList {
  //
  totalSize = 0
  /**
   * 获取我的订单列表
   *
   */
  async queryMyOrderList(
    page: Page,
    orderStatus = QueryOrderStatusEnum.QueryOrderStatusEnumAll,
    saleChannelIds?: string[]
  ): Promise<Array<MyOrderVo>> {
    const request = new OrderRequest()
    request.orderBasicData = new OrderBasicDataRequest()
    if (orderStatus == QueryOrderStatusEnum.QueryOrderStatusEnumCompleted) {
      request.orderBasicData.orderStatusList = [2]
      request.orderBasicData.orderDeliveryStatusList = [1, 2]
    } else if (orderStatus == QueryOrderStatusEnum.QueryOrderStatusEnumInvalid) {
      request.orderBasicData.orderStatusList = [3]
    } else if (orderStatus == QueryOrderStatusEnum.QueryOrderStatusEnumWaitPay) {
      request.orderBasicData.orderPaymentStatusList = [0, 1]
      request.orderBasicData.orderStatusList = [1]
    }
    if (saleChannelIds && saleChannelIds.length) {
      request.saleChannelIds = saleChannelIds
      request.saleChannels = [SaleChannelEnum.topic]
    }
    const sort = new OrderSortRequest()
    sort.field = OrderSortField.ORDER_NORMAL_TIME
    sort.policy = SortPolicy.DESC
    // 过滤分销管理员导入订单（不展示）
    request.orderFixQuery = new OrderFixQueryRequest()
    request.orderFixQuery.excludeSaleChannels = [1]
    request.orderFixQuery.excludeChannelTypesList = [3]
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageOrderInMyself({
      page: page,
      request: request,
      sortRequest: [sort]
    })
    const onlineInvoice: string[] = []
    const offlineInvoice: string[] = []
    const subOrderItems: string[] = []
    const channelIds: string[] = []
    if (res.status.isSuccess()) {
      const datas = []
      for (const item of res.data.currentPageData) {
        const tmpItem = new MyOrderVo()
        Object.assign(tmpItem, item)
        tmpItem.changeStatue()
        if (
          tmpItem.invoiceApplyInfo &&
          tmpItem.invoiceApplyInfo.invoiceIdList &&
          tmpItem.invoiceApplyInfo.invoiceIdList.length
        ) {
          //判断线下发票还是线上发票
          if (tmpItem.invoiceApplyInfo.invoiceMethod === 1) {
            onlineInvoice.push(...tmpItem.invoiceApplyInfo.invoiceIdList)
          } else {
            offlineInvoice.push(...tmpItem.invoiceApplyInfo.invoiceIdList)
          }
        } else {
          if (tmpItem.invoiceApplyInfo) {
            ;(tmpItem.invoiceApplyInfo as OrderInvoiceApplyInfoResponseVo).invoiceStatus = InvoiceStatusEnum.NOTPTOOPEN
          }
        }
        tmpItem.subOrderItems.map(item => {
          if (item.subOrderNo) {
            subOrderItems.push(item.subOrderNo)
          }
        })
        // subOrderItems.push(tmpItem.subOrderItems[0].subOrderNo)
        datas.push(tmpItem)
        if (tmpItem.saleChannelId) {
          !channelIds.includes(tmpItem.saleChannelId) && channelIds.push(tmpItem.saleChannelId)
        }
      }
      const refundOrderRes = await TradeQueryForestage.pageReturnOrderInMyself({
        page: {
          pageNo: 1,
          pageSize: 200
        },
        request: {
          subOrderInfo: {
            subOrderNoList: subOrderItems
          }
        }
      })
      const refundOrderResMap = new Map<string, ReturnOrderResponse>()
      if (
        refundOrderRes.status.isSuccess() &&
        refundOrderRes.data.currentPageData &&
        refundOrderRes.data.currentPageData.length
      ) {
        refundOrderRes.data.currentPageData.forEach(item => {
          refundOrderResMap.set(item.subOrderInfo.subOrderNo, item)
        })
      }
      // 拿取全部线上发票
      const onlineRew = new RewriteGraph<OnlineInvoiceResponse, string>(
        TradeQueryBackstage._commonQuery,
        getOnlineInvoiceInServicer
      )
      await onlineRew.request(onlineInvoice)
      // 拿取全部线下发票
      const onfflineRew = new RewriteGraph<OfflineInvoiceResponse, string>(
        TradeQueryBackstage._commonQuery,
        getOfflineInvoiceInServicer
      )
      await onfflineRew.request(offlineInvoice)
      // 专题地区
      if (channelIds.length) {
        await ThematicMap.getThematicMap(channelIds)
      }
      datas.forEach(item => {
        if (item.saleChannelId) {
          item.specialArea = ThematicMap.map.get(item.saleChannelId)?.region?.[0] || ''
          item.specialType = ThematicMap.map.get(item.saleChannelId)?.type || []
          item.specialUnit = ThematicMap.map.get(item.saleChannelId)?.unitName || ''
        }
        let invoceList
        if (
          item.invoiceApplyInfo &&
          item.invoiceApplyInfo.invoiceIdList &&
          item.invoiceApplyInfo.invoiceIdList.length
        ) {
          if (item.invoiceApplyInfo.invoiceMethod === 1) {
            invoceList = item.invoiceApplyInfo.invoiceIdList.map(temp =>
              InvoiceListResponse.from(onlineRew.itemMap.get(temp))
            )
          } else {
            invoceList = item.invoiceApplyInfo.invoiceIdList.map(temp =>
              OffLinePageInvoiceVo.from(onfflineRew.itemMap.get(temp))
            )
          }
          item.addInvoiceStatic(invoceList)
        }
        if (
          refundOrderRes.status.isSuccess() &&
          refundOrderRes.data.currentPageData &&
          refundOrderRes.data.currentPageData.length
        ) {
          item.returnOrderDetailList = []
          item.subOrderItems.map(subitem => {
            const returnOrderDetail = refundOrderResMap.get(subitem.subOrderNo)
            if (returnOrderDetail) {
              const returnOrderVo = new ReturnOrderResponseVo()
              Object.assign(returnOrderVo, returnOrderDetail)
              returnOrderVo.changeStatus()
              item.returnOrderDetail = returnOrderVo
              item.returnOrderDetailList.push(returnOrderVo)
            } else {
              item.returnOrderDetail = null
            }
          })
        } else {
          item.returnOrderDetail = null
        }
      })
      this.totalSize = res.data.totalSize
      return datas
    }
    return []
  }
  /**
   * 个人-根据商品查询订单号 - 订单状态
   * @return 0 是未退款 1是订单数据未出来（提示内容：订单信息未完成，请稍候在进入） 2是在退款中 3 是已被换班 或者 在换班中
   */
  async queryOrderStatus(commoditySkuId: string) {
    const request = new OrderRequest()
    request.currentCommodity = new CommoditySkuRequest1()
    request.currentCommodity.commoditySkuIdList = [commoditySkuId]
    const sort = new OrderSortRequest()
    sort.field = OrderSortField.ORDER_NORMAL_TIME
    sort.policy = SortPolicy.DESC
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageOrderInMyself({
      page: {
        pageNo: 1,
        pageSize: 10
      },
      request: request,
      sortRequest: [sort]
    })
    let orderStatus
    const commodity = res?.data?.currentPageData[0]

    if (commodity) {
      const subOrder = commodity.subOrderItems.find(
        subOrder => subOrder.currentCommoditySku.commoditySkuId == commoditySkuId
      )

      if (subOrder) {
        const status = CommodityRefundStatus.transferSubOrderStatusToCurrent(
          subOrder.returnStatus,
          subOrder.refundSchedule,
          subOrder.returnSchedule
        )

        if (CommodityRefundStatusWithGoods.includes(status) || status === CommodityRefundStatusEnum.processing) {
          orderStatus = 2
        } else {
          orderStatus = 0
        }
        // orderStatus = subOrder.returnStatus === 0 ? 0 : 2

        if (orderStatus === 0 && ![0, 3].includes(subOrder.exchangeStatus)) {
          orderStatus = 3
        }
      } else {
        orderStatus = 1
      }
    } else {
      orderStatus = 1
    }

    return orderStatus
  }

  /**
   * 个人-根据商品查询订单号 - 订单状态
   * @return 0 是未退款 1是订单数据未出来（提示内容：订单信息未完成，请稍候在进入） 2是在退款中 3 是已被换班 或者 在换班中
   */
  async queryOrderStatusByOrderNo(orderNo: string, commoditySkuId?: string) {
    // const res = await MsTradeQueryFrontGatewayCourseLearningForestage.getOrderInMyself(orderNo)
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage._commonQuery<OrderResponse>(
      GetOrderInMyselfgraphql,
      {
        orderNo
      }
    )
    let orderStatus: number = null

    if (res.data && res.data.subOrderItems?.length) {
      let subOrder = new SubOrderResponse()
      if (commoditySkuId) {
        subOrder = res.data.subOrderItems.find(
          subOrder => subOrder.currentCommoditySku.commoditySkuId == commoditySkuId
        )
      } else {
        subOrder = res.data.subOrderItems[0]
      }
      if (subOrder) {
        const status = CommodityRefundStatus.transferSubOrderStatusToCurrent(
          subOrder.returnStatus,
          subOrder.refundSchedule,
          subOrder.returnSchedule
        )

        if (CommodityRefundStatusWithGoods.includes(status) || status === CommodityRefundStatusEnum.processing) {
          orderStatus = 2
        } else {
          orderStatus = 0
        }
        if (orderStatus === 0 && ![0, 3].includes(subOrder.exchangeStatus)) {
          orderStatus = 3
        }
      }
    }
    if (!orderStatus && orderStatus !== 0) {
      orderStatus = 1
    }
    return orderStatus
  }

  /**
   * 个人-根据商品以及方案查询订单号 - 订单状态
   * @param commoditySkuId 商品id
   * @param schemeId 方案id
   * @return 0 是未退款 1是订单数据未出来（提示内容：订单信息未完成，请稍候在进入） 2是在退款中 3 是已被换班 或者 在换班中
   */
  async queryOrderStatusByScheme(commoditySkuId: string, schemeId: string) {
    const request = new OrderRequest()
    request.currentCommodity = new CommoditySkuRequest1()
    request.currentCommodity.commoditySkuIdList = [commoditySkuId]
    const sort = new OrderSortRequest()
    sort.field = OrderSortField.ORDER_NORMAL_TIME
    sort.policy = SortPolicy.DESC
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageOrderInMyself({
      page: {
        pageNo: 1,
        pageSize: 10
      },
      request: request,
      sortRequest: [sort]
    })

    const commodity = res?.data?.currentPageData[0]
    let orderStatus

    if (commodity) {
      const subOrder = commodity.subOrderItems.find(subOrder => {
        const resource = subOrder?.currentCommoditySku?.resource as SchemeResourceResponse
        if (resource.schemeId && resource.schemeId == schemeId) {
          return true
        }
      })

      if (subOrder) {
        const status = CommodityRefundStatus.transferSubOrderStatusToCurrent(
          subOrder.returnStatus,
          subOrder.refundSchedule,
          subOrder.returnSchedule
        )

        if (CommodityRefundStatusWithGoods.includes(status) || status === CommodityRefundStatusEnum.processing) {
          orderStatus = 2
        } else {
          orderStatus = 0
        }

        if (orderStatus === 0 && ![0, 3].includes(subOrder.exchangeStatus)) {
          orderStatus = 3
        }
      } else {
        orderStatus = 1
      }
    } else {
      orderStatus = 1
    }

    return orderStatus
  }

  /**
   * 个人-根据商品查询订单信息
   *   * @return 0 是未退款 1是订单数据未出来（提示内容：订单信息未完成，请稍候在进入） 2是在退款中 3 是已被换班 或者 在换班中
   */
  async queryOrder(commoditySkuId: string) {
    const request = new OrderRequest()
    request.currentCommodity = new CommoditySkuRequest1()
    request.currentCommodity.commoditySkuIdList = [commoditySkuId]
    const sort = new OrderSortRequest()
    sort.field = OrderSortField.ORDER_NORMAL_TIME
    sort.policy = SortPolicy.DESC
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageOrderInMyself({
      page: {
        pageNo: 1,
        pageSize: 10
      },
      request: request,
      sortRequest: [sort]
    })

    return res.data.currentPageData[0]
  }

  /**
   * 根据条件查订单列表
   */
  async pageOrderInMyself(page: CommonPage = new CommonPage(), orderRequest: OrderRequest = new OrderRequest()) {
    const res = await MsTradeQueryFrontGatewayCourseLearningForestage.pageOrderInMyself({
      page,
      request: orderRequest
    })
    if (res.status.isSuccess()) {
      page.totalSize = res.data.totalSize
      page.totalPageSize = res.data.totalPageSize
      return res.data.currentPageData.map(item => {
        return Object.assign(new MyOrderVo(), item)
      })
    }
    return []
  }
}
