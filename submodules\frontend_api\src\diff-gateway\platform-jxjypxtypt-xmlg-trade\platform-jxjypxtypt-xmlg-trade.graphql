"""独立部署的差异化平台,K8S服务名:tomcat-jxjytyptcyh"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
directive @type(value:String!,implementsInputs:[String]) on ENUM | INPUT_OBJECT | INTERFACE | MUTATION | OBJECT | QUERY | SUBSCRIPTION
type Query {
	"""获取华医网组合方案的课程退款情况"""
	queryHymSchemeRefundInfo(orderNo:String!):QueryHymSchemeRefundInfoResponse
}
type Mutation {
	"""校验是否存在未处理完成的退货流程"""
	VerifyOrderAfterSale(request:VerifyOrderAfterSaleRequest):VerifyOrderAfterSaleResponse
	"""同意退货"""
	agreeOrderReturn(request:AgreeOrderReturnRequest):AgreeOrderReturnResponse
	"""批量同意退货"""
	agreeOrderReturnBatch(request:AgreeOrderReturnBatchRequest):AgreeOrderReturnBatchResponse
	"""申请退货"""
	applyOrderReturn(request:ApplyOrderReturnRequest):ApplyOrderReturnResponse
	"""取消退货"""
	cancelOrderReturn(request:CancelOrderReturnRequest):CancelOrderReturnResponse
	"""拒绝退货"""
	refuseOrderReturn(request:RefuseOrderReturnRequest):RefuseOrderReturnResponse
}
input SubOrderAfterInfo @type(value:"com.fjhb.domain.trade.api.orderAfterSale.entities.SubOrderAfterInfo") {
	subOrderNo:String
	quantity:Int!
	amount:BigDecimal
	amountSource:Int!
	properties:Map
}
"""退货单批量同意申请请求
	<AUTHOR>
"""
input AgreeOrderReturnBatchRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.request.AgreeOrderReturnBatchRequest") {
	"""订单和退货单信息列表"""
	orderReturnPairs:[OrderReturnPair]!
	"""审批意见"""
	approveComment:String
	"""是否自动同意退款"""
	autoAgreeReturn:Boolean!
}
"""订单和退货单对应关系"""
input OrderReturnPair @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.request.AgreeOrderReturnBatchRequest$OrderReturnPair") {
	"""订单号"""
	orderNo:String!
	"""退货单号"""
	returnOrderNo:String!
}
"""确认退货请求
	<AUTHOR>
	@since 2025-03-07
"""
input AgreeOrderReturnRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.request.AgreeOrderReturnRequest") {
	orderNo:String!
	"""退货单号"""
	returnOrderNo:String!
	"""审批意见"""
	approveComment:String
	"""是否自动同意退款"""
	autoAgreeReturn:Boolean!
}
"""申请退货
	<AUTHOR>
	@since 2025-03-07
"""
input ApplyOrderReturnRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.request.ApplyOrderReturnRequest") {
	orderNo:String!
	"""退货原因id"""
	reasonId:String
	"""退货原因描述"""
	description:String
	"""退货单类型
		- 仅退货             1
		- 退货且退款          3
		- 部分退货且部分退款   6
	"""
	returnOrderType:Int!
	"""退货商品信息"""
	returnInfo:SubOrderAfterInfo
}
"""取消退货请求
	<AUTHOR>
	@since 2025-03-07
"""
input CancelOrderReturnRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.request.CancelOrderReturnRequest") {
	orderNo:String!
	returnOrderNo:String!
	cancelReason:String
}
"""拒绝退货请求
	<AUTHOR>
	@since 2025-03-07
"""
input RefuseOrderReturnRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.request.RefuseOrderReturnRequest") {
	orderNo:String!
	returnOrderNo:String!
	"""审批意见"""
	approveComment:String
}
"""校验订单售后请求
"""
input VerifyOrderAfterSaleRequest @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.request.VerifyOrderAfterSaleRequest") {
	orderNo:String!
}
type ReturnInfo @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.appservice.XMLGOrderReturnAppService$ReturnInfo") {
	"""是否有退款"""
	refund:Boolean!
	"""是否已完成"""
	completed:Boolean!
}
type AgreeOrderReturnBatchResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.response.AgreeOrderReturnBatchResponse") {
	agreeOrderReturnResponseList:[AgreeOrderReturnResponse]
}
"""确认退货响应
	<AUTHOR>
	@since 2025-03-07
"""
type AgreeOrderReturnResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.response.AgreeOrderReturnResponse") {
	code:String
	message:String
	returnOrderNo:String
}
"""退货申请返回结果
	<AUTHOR>
	@since 2025-03-07
"""
type ApplyOrderReturnResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.response.ApplyOrderReturnResponse") {
	"""申请退货业务code"""
	code:String
	"""申请退货信息"""
	message:String
	data:ApplyOrderReturnData
}
type ApplyOrderReturnData @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.response.ApplyOrderReturnResponse$ApplyOrderReturnData") {
	"""生成的退货单号"""
	returnOrderNo:String
}
"""取消退货响应
	<AUTHOR>
	@since 2025-03-07
"""
type CancelOrderReturnResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.response.CancelOrderReturnResponse") {
	code:String
	message:String
}
"""<AUTHOR>
	@since 2025-03-13
"""
type QueryHymSchemeRefundInfoResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.response.QueryHymSchemeRefundInfoResponse") {
	code:String
	message:String
	orderNo:String
	"""公需课程退款信息"""
	publicCourse:ReturnInfo
	"""专业课程退款信息"""
	professionalCourse:ReturnInfo
}
"""拒绝退货响应
	<AUTHOR>
	@since 2025-03-07
"""
type RefuseOrderReturnResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.response.RefuseOrderReturnResponse") {
	code:String
	message:String
}
"""校验订单售后结果
"""
type VerifyOrderAfterSaleResponse @type(value:"com.fjhb.platform.jxjypxtypt.dif.xmlg.v1.kernel.geteway.response.VerifyOrderAfterSaleResponse") {
	"""校验订单售后业务code
		- 200 成功
		- -4000 失败
	"""
	code:String
	"""校验订单售后信息"""
	message:String
}

scalar List
