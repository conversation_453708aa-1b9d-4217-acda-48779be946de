const fs = require('fs')
const { readdirSync } = fs
const { slugify } = require('transliteration')
const { sync } = require('glob')
const path = require('path')

const translation = function (name) {
  return (
    slugify(name, {
      lowercase: false,
      separator: ' ',
      trim: true
    })
      // 替换空格
      .replace(/\s/g, '')
      // 将首字母变成大写
      .replace(/^[a-z]/, function (re) {
        return re.toUpperCase()
      })
  )
}
const transformPath = function (file) {
  return `${`${file.replace(/\.vue$/, '')}`.replace(/\/[a-zA-Z]/g, function (re) {
    return re.toUpperCase().replace(/\//, '')
  })}`
}
const getSubDir = function (_path) {
  return sync('*', {
    cwd: path.resolve(process.cwd(), _path)
  }).filter(dir => fs.statSync(path.resolve(process.cwd(), `${_path}/${dir}`)).isDirectory())
}

const getEntries = function (_path) {
  const array = []
  getSubDir(_path).forEach(dir => {
    if (dir === 'portal') {
      getEntries(`${_path}/${dir}`).forEach(sub => {
        array.push(`${dir}/${sub}`)
      })
    } else {
      array.push(dir)
    }
  })
  return array
}

const cwd = target => {
  return path.resolve(process.cwd(), target)
}

const walk = (_path, dirTreeList, sourcePath) => {
  readdirSync(_path).forEach(dir => {
    const myPath = _path.replace(sourcePath, '')
    const pinYin = translation(dir)
    const moduleName = `${transformPath(`${myPath}`)}${pinYin}`
    const obj = {
      children: [],
      appPath: myPath,
      expand: true,
      fileDir: `${myPath}/${dir}`,
      vueRouter: `/${`${myPath}/${dir.replace(/\.vue$/, '')}`.replace(/\//, '').replace(/\//g, '_')}`,
      moduleName: moduleName.replace(/\.vue$/, '').replace(/-/g, '')
    }
    obj.title = dir
    obj.label = dir
    obj.name = dir
    if (fs.statSync(`${_path}/${dir}`).isDirectory() && !['assets', 'styles', 'common'].includes(dir)) {
      obj.isDirectory = true
      walk(`${_path}/${dir}`, obj.children, sourcePath)
    }

    // vue文件才能看
    if (/\.vue$/.test(obj.name) || (fs.statSync(`${_path}/${dir}`).isDirectory() && !['assets', 'styles', 'common'].includes(dir))) {
      dirTreeList.push(obj)
    }
  })
}

module.exports = {
  translation,
  transformPath,
  getSubDir,
  getEntries,
  cwd,
  walk
}
