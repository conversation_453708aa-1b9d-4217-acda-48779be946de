export class SystemException extends Error {
  message = ''
  errorCode = ''

  constructor(message = '系统异常错误!', errorCode: string) {
    super(message)
    this.errorCode = errorCode
    this.message = message
    // ts历史遗留问题 继承基类失败
    // eslint-disable-line 
    Object.setPrototypeOf(this, SystemException.prototype)
  }
}

export class NetWorkException extends SystemException {
  constructor(message = '网络异常！', errorCode: string) {
    super(message, errorCode)
    this.errorCode = errorCode
    this.message = message
  }
}
