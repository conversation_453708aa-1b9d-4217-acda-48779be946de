<route-meta>
  {
  "isMenu": true,
  "title": "学员学习明细",
  "sort": 5,
  "icon": "icon-mingxi"
  }
  </route-meta>
<template>
  <el-main
    v-if="$hasPermission('query')"
    query
    desc="查询"
    actions="activated,search,created,@BizPortalSelect,@BizPortalDistributorSelect,@BizDistributorSelect,@BizDistributorIndustry,@BizDistributorYear"
  >
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
        <hb-search-wrapper @reset="resetQueryParam" class="m-query is-border-bottom">
          <el-form-item label="姓名">
            <el-input v-model="localSkuProperty.name" clearable placeholder="请输入姓名" />
          </el-form-item>
          <el-form-item label="登录账号" v-if="queryShowLoginAccount.isShowLoginAccount">
            <el-input v-model="localSkuProperty.loginAccount" clearable placeholder="请输入省平台ID" />
          </el-form-item>
          <el-form-item label="证件号">
            <el-input v-model="localSkuProperty.idCard" clearable placeholder="请输入证件号" />
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="localSkuProperty.phone" clearable placeholder="请输入手机号" />
          </el-form-item>
          <el-form-item label="工作单位所在地区" v-if="isHaveFxRole && isOpenFxServer">
            <biz-region-distribution-cascader
              v-model="localSkuProperty.regionList"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-region-distribution-cascader>
          </el-form-item>
          <el-form-item label="工作单位所在地区" v-if="!isHaveFxRole">
            <biz-national-region
              v-model="localSkuProperty.regionList"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-national-region>
          </el-form-item>
          <el-form-item label="工作单位">
            <el-input clearable placeholder="请输入工作单位" v-model="localSkuProperty.companyName" />
          </el-form-item>
          <el-form-item label="培训方案类型">
            <biz-scheme-type v-model="localSkuProperty.schemeTypeInfo"></biz-scheme-type>
          </el-form-item>

          <el-form-item label="培训方案名称">
            <el-input clearable placeholder="请输入培训方案名称" v-model="localSkuProperty.schemeName" />
          </el-form-item>
          <el-form-item label="期别名称" v-if="['second', 'third'].includes(activeName)">
            <biz-issue-auto-select v-model="localSkuProperty.issueName" @getIssueId="handleIssueId" />
          </el-form-item>

          <el-form-item label="行业" v-if="isHaveFxRole && isOpenFxServer">
            <biz-distributor-industry
              v-model="localSkuProperty.industry"
              @clearIndustrySelect="handleClearIndustrySelect"
              @industryPropertyId="handleIndustryPropertyId"
              @industryInfos="handleIndustryInfos"
            ></biz-distributor-industry>
          </el-form-item>
          <el-form-item label="行业" v-if="!isHaveFxRole">
            <biz-industry-select
              v-model="localSkuProperty.industry"
              @clearIndustrySelect="handleClearIndustrySelect"
              @industryPropertyId="handleIndustryPropertyId"
              @industryInfos="handleIndustryInfos"
            ></biz-industry-select>
          </el-form-item>

          <el-form-item label="年度" v-if="isHaveFxRole && isOpenFxServer">
            <biz-distributor-year
              v-model="localSkuProperty.year"
              placeholder="请选择年度"
              :multiple="true"
            ></biz-distributor-year>
          </el-form-item>

          <el-form-item label="年度" v-if="!isHaveFxRole">
            <biz-year-select
              v-model="localSkuProperty.year"
              placeholder="请选择年度"
              :multiple="true"
            ></biz-year-select>
          </el-form-item>
          <!-- <el-form-item label="技术等级">
              <biz-technical-grade-select v-model="localSkuProperty.technicalGrade"></biz-technical-grade-select>
            </el-form-item> -->

          <el-form-item label="地区" v-if="isHaveFxRole && isOpenFxServer">
            <biz-region-distribution-cascader
              v-model="localSkuProperty.region"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-region-distribution-cascader>
          </el-form-item>
          <el-form-item label="地区" v-if="!isHaveFxRole">
            <biz-national-region
              v-model="localSkuProperty.region"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-national-region>
          </el-form-item>
          <el-form-item
            label="科目类型"
            v-if="
              skuVisible.subjectType &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-accounttype-select
              v-model="localSkuProperty.subjectType"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            >
            </biz-accounttype-select>
          </el-form-item>

          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-major-cascader
              v-model="localSkuProperty.societyTrainingMajor"
              placeholder="请选择培训专业"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-training-category-select
              placeholder="请选择培训类别"
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              @updateTrainingCategory="handleUpdateTrainingCategory"
            />
          </el-form-item>

          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-major-select
              v-model="localSkuProperty.constructionTrainingMajor"
              placeholder="请选择培训专业"
              :industry-property-id="industryPropertyId"
              :training-category-id="trainingCategoryId"
            />
          </el-form-item>
          <!-- 工勤行业 -->
          <el-form-item
            label="技术等级"
            v-if="
              skuVisible.technicalGrade &&
              localSkuProperty.industry &&
              envConfig.workServiceId &&
              localSkuProperty.industry === envConfig.workServiceId
            "
          >
            <biz-technical-grade-select
              v-model="localSkuProperty.technicalGrade"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            ></biz-technical-grade-select>
          </el-form-item>
          <!-- 卫生行业 -->
          <el-form-item
            label="培训类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-training-category-select
              placeholder="请选择培训类别"
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训对象"
            v-if="
              skuVisible.trainingObject &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-training-object-select
              placeholder="请选择培训对象"
              v-model="localSkuProperty.trainingObject"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              @updateTrainingCategory="handleWSUpdateTrainingObject"
            />
          </el-form-item>
          <el-form-item
            label="岗位类别"
            v-if="
              skuVisible.positionCategory &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-obj-category-select
              placeholder="请选择岗位类别"
              v-model="localSkuProperty.positionCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              :training-object-id="trainingObjectId"
            />
          </el-form-item>
          <el-form-item
            label="学段"
            v-if="
              skuVisible.learningPhase &&
              localSkuProperty.industry &&
              envConfig.teacherIndustryId &&
              localSkuProperty.industry === envConfig.teacherIndustryId
            "
          >
            <biz-study-period
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              placeholder="请选择学段"
              v-model="localSkuProperty.learningPhase"
              @updateStudyPeriod="updateStudyPeriod"
            />
          </el-form-item>
          <el-form-item
            label="学科"
            v-if="
              skuVisible.discipline &&
              localSkuProperty.industry &&
              envConfig.teacherIndustryId &&
              localSkuProperty.industry === envConfig.teacherIndustryId
            "
          >
            <biz-subject
              :study-period-id="localSkuProperty.learningPhase"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              placeholder="请选择学科"
              v-model="localSkuProperty.discipline"
            />
          </el-form-item>
          <el-form-item label="报名学时">
            <el-input v-model="localSkuProperty.trainingResultPeriodBegin" class="input-num" type="number" />
            -
            <el-input v-model="localSkuProperty.trainingResultPeriodEnd" class="input-num" />
            <span class="f-ml5">学时</span>
          </el-form-item>
          <el-form-item label="要求学时">
            <el-input v-model="localSkuProperty.requirePeriodBegin" class="input-num" />
            -
            <el-input v-model="localSkuProperty.requirePeriodEnd" class="input-num" />
            <span class="f-ml5">学时</span>
          </el-form-item>
          <el-form-item label="获得学时">
            <el-input v-model="localSkuProperty.acquiredPeriodBegin" class="input-num" />
            -
            <el-input v-model="localSkuProperty.acquiredPeriodEnd" class="input-num" />
            <span class="f-ml5">学时</span>
          </el-form-item>
          <el-form-item label="培训结果">
            <el-select v-model="localSkuProperty.trainingResultList" clearable multiple filterable placeholder="请选择">
              <el-option label="未通过" :value="0">未通过</el-option>
              <el-option label="已通过" :value="1">已通过</el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="报名时间">
            <double-date-picker
              :begin-create-time.sync="localSkuProperty.registerTimeBegin"
              :end-create-time.sync="localSkuProperty.registerTimeEnd"
              beginTimePlaceholder="请选择报名成功时间"
              endTimePlaceholder="请选择报名成功时间"
              endDefaultTime="23:59:59"
            ></double-date-picker>
          </el-form-item>
          <el-form-item label="培训通过时间">
            <double-date-picker
              :begin-create-time.sync="localSkuProperty.trainingResultTimeBegin"
              :end-create-time.sync="localSkuProperty.trainingResultTimeEnd"
              beginTimePlaceholder="请选择报名成功时间"
              endTimePlaceholder="请选择报名成功时间"
              endDefaultTime="23:59:59"
            ></double-date-picker>
          </el-form-item>
          <el-form-item v-if="schoolConfigFlag" label="成果是否同步">
            <el-select v-model="localSkuProperty.syncResult" clearable filterable placeholder="请选择">
              <!-- <el-option :value="syncResultOptionVal.None" label="不同步"></el-option> -->
              <el-option :value="syncResultOptionVal.Unsynchronized" label="未同步"></el-option>
              <el-option :value="syncResultOptionVal.Waitsynchronized" label="待同步"></el-option>
              <el-option :value="syncResultOptionVal.Synchronized" label="已同步"></el-option>
              <el-option :value="syncResultOptionVal.SynchronizationFailure" label="同步失败"></el-option>
              <slot name="disSynchronized"></slot>
            </el-select>
          </el-form-item>
          <el-form-item label="专题">
            <biz-special-select v-model="saleChannels" @input="changeSaleChannels"></biz-special-select>
          </el-form-item>
          <el-form-item label="专题名称" v-if="saleChannels || saleChannels == null">
            <biz-special-name v-model="trainingChannelName"></biz-special-name>
          </el-form-item>
          <slot name="sale-channel" :localSkuProperty="localSkuProperty"></slot>
          <template v-if="!isHaveFxRole && isOpenFxServer && !isHaveZtRole">
            <el-form-item label="分销商">
              <!-- 解决切换tab导致分销商名称显示丢失问题 -->
              <biz-distributor-select
                v-model="filter.learningRegister.distributorId"
                :name="filter.learningRegister.distributorId ? '分销商' : ''"
              ></biz-distributor-select>
            </el-form-item>
          </template>
          <el-form-item label="推广门户简称" v-if="!isHaveFxRole && isOpenFxServer && !isHaveZtRole">
            <biz-portal-select
              :disabled="filter.notDistributionPortal"
              v-model="portalId"
              :name="filter.promoteThePortalAlias"
            ></biz-portal-select>
          </el-form-item>
          <el-form-item label="推广门户简称" v-if="isHaveFxRole && isOpenFxServer">
            <biz-portal-distributor-select
              :disabled="filter.notDistributionPortal"
              v-model="portalId"
              :name="filter.promoteThePortalAlias"
            ></biz-portal-distributor-select>
          </el-form-item>
          <slot name="more-filtration" :localSkuProperty="localSkuProperty"></slot>
          <el-form-item v-if="isOpenFxServer && !isHaveZtRole">
            <el-checkbox label="查看非门户推广数据" name="type" v-model="filter.notDistributionPortal"></el-checkbox>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="search">查询</el-button>
            <!--学习数据异常管理-->
            <template
              v-if="$hasPermission('exceptionManagement') && isShowErrorDataTab && !isHaveFxRole"
              query
              desc="学习数据异常管理"
              actions="@ExceptionManagement"
            >
              <el-button @click="doManagement" type="primary" class="f-mr20"
                >学习数据异常管理（{{ learnAnomalousCount + ruleAnomalousCount }}）</el-button
              >
            </template>
            <el-button v-if="$hasPermission('export')" query desc="导出" actions="doExport" @click="doExport"
              >导出列表数据</el-button
            >
            <slot name="exportLinkData"></slot>
            <slot name="export-docking-data"></slot>
          </template>
        </hb-search-wrapper>
        <!--操作栏-->
        <el-tabs v-model="activeName" type="card" class="m-tab-card" @tab-click="search">
          <el-tab-pane label="网授班" name="first">
            <!--表格-->
            <template
              v-if="$hasPermission('onlineClassTable')"
              desc="网授班列表"
              actions="@OnlineClassTable,@examDetail,@testDetail,@attendanceDetail"
            >
              <online-class-table
                :tableData="tableData"
                :loading="loading"
                :schoolConfigFlag="schoolConfigFlag"
                @examDetail="examDetail"
                @testDetail="testDetail"
                @getChildRef="getChildRef"
                @getStudentNoList="getStudentNoList"
                :page="page"
                @changeStavisible="changeStavisible"
                @doQueryPage="doQueryPage"
                :baseConfig="baseConfig"
              ></online-class-table>
            </template>
          </el-tab-pane>
          <el-tab-pane v-if="showOffline" label="面授班" name="third">
            <!--表格-->
            <template
              v-if="$hasPermission('faceClassTable')"
              desc="面授班列表"
              actions="@FaceClassTable,@examDetail,@testDetail,@attendanceDetail"
            >
              <face-class-table
                :tableData="tableData"
                :loading="loading"
                :schoolConfigFlag="schoolConfigFlag"
                @examDetail="examDetail"
                @testDetail="testDetail"
                @attendanceDetail="attendanceDetail"
                @getChildRef="getChildRef"
                :page="page"
                @changeStavisible="changeStavisible"
                @doQueryPage="doQueryPage"
                :baseConfig="baseConfig"
              ></face-class-table>
            </template>
          </el-tab-pane>
          <el-tab-pane v-if="showOffline" label="面网授班" name="second">
            <!--表格-->
            <template
              v-if="$hasPermission('mixedClassTable')"
              desc="混合班列表"
              actions="@MixedClassTable,@examDetail,@testDetail,@attendanceDetail"
            >
              <mixed-class-table
                :tableData="tableData"
                :loading="loading"
                :schoolConfigFlag="schoolConfigFlag"
                @examDetail="examDetail"
                @testDetail="testDetail"
                @attendanceDetail="attendanceDetail"
                @getChildRef="getChildRef"
                :page="page"
                @changeStavisible="changeStavisible"
                @doQueryPage="doQueryPage"
                :baseConfig="baseConfig"
              ></mixed-class-table>
            </template>
          </el-tab-pane>
        </el-tabs>
      </el-card>
      <exam-detail
        v-if="isExamDetail"
        :qualificationId="qualificationId"
        :value="isExamDetail"
        ref="examDetailRef"
        @isExamDialog="isExamDialog"
      ></exam-detail>
      <test-detail
        v-if="isTestDetail"
        :qualificationId="qualificationId"
        :value="isTestDetail"
        @isTextDialog="isTextDialog"
      ></test-detail>
      <attendance-detail
        v-if="isAttendanceDetail"
        :qualificationId="periodQualificationId"
        :period="period"
        :value="isAttendanceDetail"
        @isAttendanceDialog="isAttendanceDialog"
      ></attendance-detail>
      <!-- 导出任务查看 -->
      <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
        <div class="dialog-alert is-big">
          <i class="icon el-icon-success success"></i>
          <div class="txt">
            <p class="f-fb">导出成功，是否前往下载数据？</p>
            <p class="f-f13 f-mt5">下载入口：导出任务管理-学员学习明细</p>
          </div>
        </div>
        <div slot="footer">
          <el-button @click="exportSuccessVisible = false">暂 不</el-button>
          <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
        </div>
      </el-dialog>
      <el-drawer title="统计口径说明" :visible.sync="stavisible" size="900px" custom-class="m-drawer">
        <div class="drawer-bd">
          <el-alert type="warning" show-icon :closable="false" class="m-alert">
            注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
          </el-alert>
          <p class="f-mt20 f-mb10">
            <span class="f-fb f-f15">搜索条件说明</span>
            （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
          </p>
          <el-table stripe :data="searchConditions" border class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="字段" width="150">
              <template slot-scope="scope">
                {{ scope.row.field }}
              </template>
            </el-table-column>
            <el-table-column label="详细说明" min-width="300">
              <template slot-scope="scope">
                {{ scope.row.description }}
              </template>
            </el-table-column>
          </el-table>
          <p class="f-mt20 f-mb10">
            <span class="f-fb f-f15">列表字段及详细说明</span>
            （列表下的数据显示受搜索条件的约束，统计单位：人次）
          </p>
          <el-table stripe :data="listFields" border class="m-table">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="字段" width="150">
              <template slot-scope="scope">
                {{ scope.row.field }}
              </template>
            </el-table-column>
            <el-table-column label="详细说明" min-width="300">
              <template slot-scope="scope">
                {{ scope.row.description }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-drawer>
      <!-- 学习数据异常管理 -->

      <exception-management
        ref="exceptionManagementRef"
        :learnAnomalousCount="learnAnomalousCount"
        :ruleAnomalousCount="ruleAnomalousCount"
        :isShowLearningRulesTab="isShowLearningRulesTab"
        @getStatistics="getLearningAnomalousCount"
        :isShowSmartLearningTab="isShowSmartLearningTab"
      ></exception-management>
    </div>
  </el-main>
</template>

<script lang="ts">
  import { RegionModel } from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
  import {
    DataAnalysisRequest,
    DateScopeRequest,
    DoubleScopeRequest,
    LearningRegisterRequest,
    RegionRequest,
    SchemeRequest,
    SchemeSkuPropertyRequest,
    StudentLearningRequest,
    UserPropertyRequest,
    UserRequest
  } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
  import {
    RegionSkuPropertyRequest,
    RegionSkuPropertySearchRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import { StudentLearningStaticsVo } from '@api/service/management/statisticalReport/query/vo/StudentLearningStaticsVo'
  import {
    StudentSchemeLearningRequestVo,
    SyncResultEnmu
  } from '@api/service/management/statisticalReport/query/vo/StudentSchemeLearningRequestVo'
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'
  import { UiPage } from '@hbfe/common'
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src/double-date-picker/index.vue'
  import examDetail from '@hbfe/jxjy-admin-learningStatistic/src/__components__/exam-detail.vue'
  import ExceptionManagement from '@hbfe/jxjy-admin-learningStatistic/src/__components__/exception-management.vue'
  import testDetail from '@hbfe/jxjy-admin-learningStatistic/src/__components__/test-detail.vue'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-learningStatistic/src/models/index'
  import { cloneDeep, isBoolean } from 'lodash'
  import { Component, Ref, Vue } from 'vue-property-decorator'

  import CommonConfigCenter from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import SaleChannelType from '@api/service/common/enums/trade/SaleChannelType'
  import getServicerIsDocking from '@api/service/management/online-school-config/portal/query/QueryPortal'
  import LearnAnomalousList from '@api/service/management/statisticalReport/query/LearnAnomalousList'
  import { QueryStudentLearningManagerRegionList } from '@api/service/management/statisticalReport/query/QueryStudentLearningManagerRegionList'
  import UserModule from '@api/service/management/user/UserModule'
  import { isAxServicerId } from '@hbfe/jxjy-admin-common/src/util/differentiateServicerId'

  import Context from '@api/service/common/context/Context'
  import AhStudentTrainClassDetailVo from '@api/service/diff/management/anhui/train-class/StudentTrainClassDetailVo'
  import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
  import LearningRuleList from '@api/service/management/learning-rule/LearningRuleList'
  import QueryManagerDetail from '@api/service/management/user/query/manager/QueryManagerDetail'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import { isAhServicerId } from '@hbfe/jxjy-admin-common/src/util/differentiateServicerId'

  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import IntelligenceLearningModule from '@api/service/management/intelligence-learning/IntelligenceLearningModule'
  import IntelligenceAbnormalList from '@api/service/management/statisticalReport/query/IntelligenceAbnormalList'
  import QueryShowLoginAccount from '@api/service/management/user/query/student/QueryShowLoginAccount'
  import BaseConfig from '@hbfe-biz/biz-anticheat/dist/config/BaseConfig'
  import BizDistributorIndustry from '@hbfe/fx-manage/src/components/biz/biz-distributor-industry.vue'
  import BizDistributorYear from '@hbfe/fx-manage/src/components/biz/biz-distributor-year.vue'
  import BizPortalDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-distributor-select.vue'
  import BizRegionDistributionCascader from '@hbfe/jxjy-admin-components/src/biz/biz-region-distribution-cascader.vue'
  import attendanceDetail from '@hbfe/jxjy-admin-learningStatistic/src/__components__/attendance-detail.vue'
  import OnlineClassTable from '@hbfe/jxjy-admin-learningStatistic/src/__components__/online-class-table.vue'
  import MixedClassTable from '@hbfe/jxjy-admin-learningStatistic/src/__components__/mixed-class-table.vue'
  import FaceClassTable from '@hbfe/jxjy-admin-learningStatistic/src/__components__/face-class-table.vue'
  // import { HasSelectSchemeMode } from '@hbfe/jxjy-admin-components/src/models/HasSelectSchemeMode'
  import IssueConfigDetail from '@api/service/common/scheme/model/IssueConfigDetail'
  import BizLearningSchemeZtSelect from '@hbfe/jxjy-admin-components/src/biz/biz-learning-scheme-zt-select.vue'
  import QueryShowOffline from '@api/service/common/config/QueryShowOffline'

  @Component({
    methods: { isAxServicerId },
    components: {
      DoubleDatePicker,
      examDetail,
      testDetail,
      attendanceDetail,
      ExceptionManagement,
      BizPortalSelect,
      BizDistributorSelect,
      BizRegionDistributionCascader,
      BizPortalDistributorSelect,
      BizDistributorIndustry,
      BizDistributorYear,
      BizLearningSchemeZtSelect,
      OnlineClassTable,
      MixedClassTable,
      FaceClassTable
    }
  })
  export default class extends Vue {
    @Ref('schemeTable') schemeTableRef: any
    @Ref('exceptionManagementRef') exceptionManagementRef: any
    @Ref('examDetailRef') examDetailRef: examDetail
    userRequest: UserRequest = new UserRequest()
    learningRegisterRequest: LearningRegisterRequest = new LearningRegisterRequest()
    schemeRequest: SchemeRequest = new SchemeRequest()
    studentLearningRequest: StudentLearningRequest = new StudentLearningRequest()
    dataAnalysisRequest: DataAnalysisRequest = new DataAnalysisRequest()
    tableData: Array<StudentLearningStaticsVo> = new Array<StudentLearningStaticsVo>()
    isExamDetail = false
    isTestDetail = false
    loading = false
    stavisible = false
    /**
     * 监管配置
     */
    baseConfig = new BaseConfig()
    syncResultOptionVal = SyncResultEnmu
    qualificationId = ''
    // 查询阿波罗是否显示登录账号
    queryShowLoginAccount = QueryShowLoginAccount
    /**
     * 培训类别Id
     */
    trainingCategoryId = ''
    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''
    /**
     * 培训对象Id
     */
    trainingObjectId = ''
    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = new SchemeSkuProperty()
    /**
     * 培训方案类型
     */
    /**
     * 当前网校信息
     */
    envConfig = {
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 工勤行业Id
      workServiceId: '',
      // 职业卫生行业Id
      professionHealthIndustryId: '',
      // 教师行业id
      teacherIndustryId: ''
    }
    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: false,
      // 培训类别
      trainingCategory: false,
      // 岗位类别
      positionCategory: false,
      // 培训对象
      trainingObject: false,
      // 技术等级
      technicalGrade: false,
      // 学段
      learningPhase: false,
      // 科目
      discipline: false
    }
    page: UiPage
    filter: StudentSchemeLearningRequestVo = new StudentSchemeLearningRequestVo()
    regionType: Array<string> = new Array<string>()
    tableData4 = new Array(15).fill(0)
    tableData9 = new Array(17).fill(0)
    // 导出任务查看
    exportSuccessVisible = false
    // 网校对接与否
    schoolConfigFlag = true
    // 批量同步
    QueryStudentLearningManagerRegionListObj: QueryStudentLearningManagerRegionList =
      new QueryStudentLearningManagerRegionList()
    studentNoList: Array<string> = []
    // 异常管理模型
    learnAnomalousModule = new LearnAnomalousList()
    // 智能学习异常数量
    learnAnomalousCount = 0
    // 学习规则异常数量
    ruleAnomalousCount = 0
    /**
     * 是否专题
     */
    saleChannels: boolean = null
    /**
     * 专题名称
     */
    trainingChannelName = ''
    /**
     * 是否展示学习规则tab
     */
    isShowErrorDataTab = false
    /**
     * 是否展示学习规则tab
     */
    isShowLearningRulesTab = false
    /**
     * 是否展示学习规则tab
     */
    isShowSmartLearningTab = false
    /**
     * 推广门户id
     */
    portalId = ''

    /**
     * 路由跳转的方案iD
     */
    schemeId = ''
    activeName = 'first'
    /**
     * 期别参训资格id
     */
    periodQualificationId = ''
    /**
     * 期别id
     */
    period = ''
    isAttendanceDetail = false
    showOffline = !QueryShowOffline.getShowOfflineApolloConfig()
    // hasSelectSchemeMode = new Array<HasSelectSchemeMode>()
    searchConditions = [
      { field: '学员姓名', description: '查询指定姓名的学员' },
      { field: '学员帐号', description: '学员的登录帐号（证件号）' },
      { field: '手机号', description: '查看某个培训方案下各个单位的学习数据' },
      { field: '工作单位所在地区', description: '选择需查询的地区' },
      { field: '单位名称', description: '学员工作单位信息' },
      { field: '单位所在地区', description: '学员工作单位所在地区' },
      { field: '方案名称', description: '学员报名的培训方案名称信息' },
      { field: '方案类型', description: '创建培训方案时定义的类型属性，如培训班等' },
      { field: '方案属性', description: '选择不同的行业，可以查询不同的培训属性值。属性值为空不显示' },
      { field: '报名学时', description: '查看培训方案考核通过获得学时' },
      { field: '要求学时', description: '查看课程学习的考核要求学时' },
      { field: '获得学时', description: '查看学员当前培训方案获得的学时情况' },
      { field: '培训结果', description: '根据培训结果，筛选学员学习情况' },
      { field: '培训通过时间', description: '查看在某个开通时间内，各学员学习数据' },
      { field: '培训通过时间', description: '查看在某个培训通过时间内，各个学员的学习情况' }
    ]

    listFields = [
      { field: '学员姓名', description: '查询指定姓名的学员' },
      { field: '手机号', description: '学员手机号信息，为空显示-' },
      { field: '学员证件号', description: '学员的登录帐号' },
      { field: '单位名称', description: '学员工作单位信息，为空显示-' },
      { field: '单位所在地区', description: '学员工作单位所在地区' },
      { field: '方案名称', description: '学员报名的培训方案名称信息' },
      { field: '方案属性', description: '培训方案的属性值展示' },
      { field: '报名成功时间', description: '学员成功开通培训班的时间' },
      { field: '报名学时', description: '培训方案考核通过获得学时' },
      { field: '要求学时', description: '课程学习要求的考核学时' },
      { field: '已获得', description: '课程学习已获得的学时数统计，无课程学习显示：无需学习' },
      { field: '课程合格时间', description: '完成培训班要求的课程学时时间' },
      { field: '班级考试成绩最高分', description: '班级考试当前取得的最高分，班级未配置考试显示：无需考试' },
      { field: '培训结果', description: '学员的培训结果' },
      { field: '培训通过时间', description: '学员通过培训考核时间' },
      { field: '心得要求', description: '学习心得要求至少要参与的活动数' },
      { field: '已通过', description: '已参加且满足合格要求的活动数' },
      { field: '培训期别-结业测试', description: '学员报名期别是否已被认定合格，如无需结业测试，显示 -' },
      { field: '培训期别-考勤签到', description: '学员报名期别要求的考勤总次数及学员实际完成考勤的次数。' },
      { field: '培训期别-调研问卷', description: '学员报名期别的问卷要求提交份数及学员实际提交份数，无要求显示 -' },
      { field: '调研问卷', description: '学员报名班级的问卷要求提交份数及学员实际提交份数，无要求显示 -' }
    ]
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }

    /**
     * 判断当前用户是否拥有分销商角色类型
     */
    get isHaveFxRole() {
      return QueryManagerDetail.hasCategory(CategoryEnums.fxs)
    }
    /**
     * 判断当前网校是否开启分销服务
     */
    get isOpenFxServer() {
      return CapabilityServiceConfig.fxCapabilityEnable
    }
    /**
     * 判断当前用户是否专题管理员角色类型
     */
    get isHaveZtRole() {
      return QueryManagerDetail.hasCategory(CategoryEnums.ztgly)
    }
    /**
     * 有无增值服务
     */
    async queryAddService() {
      // 学习规则
      const leanringRuleObj = new LearningRuleList()
      const learningRule = await leanringRuleObj.queryRuleConfigByServicerId(Context.servicerInfo.id)
      this.isShowLearningRulesTab = learningRule.status

      // 智能学习
      const IntelligenceLearningModuleObj = new IntelligenceLearningModule()
      const smartLearning = await IntelligenceLearningModuleObj.doQueryServiceConfig()
      this.isShowSmartLearningTab = smartLearning == 1 ? true : false

      //学习数据异常管理
      this.isShowErrorDataTab = this.isShowLearningRulesTab || this.isShowSmartLearningTab
      return this.isShowErrorDataTab
    }

    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      this.schemeId = ''
      this.localSkuProperty = new SchemeSkuProperty()
      this.portalId = ''
      this.saleChannels = null
      this.trainingChannelName = ''
      this.filter.saleChannels = new Array<number>()

      this.filter.trainingChannelName = ''
      this.filter = new StudentSchemeLearningRequestVo()
      if (this.activeName == 'first') {
        this.filter.trainingType = 'trainingWay0001'
        this.schemeRequest.skuProperty.trainingWay = ['trainingWay0001']
      } else if (this.activeName == 'second') {
        this.filter.trainingType = 'trainingWay0002'
        this.schemeRequest.skuProperty.trainingWay = ['trainingWay0002']
      } else {
        this.filter.trainingType = 'trainingWay0003'
        this.schemeRequest.skuProperty.trainingWay = ['trainingWay0003']
      }
      this.loading = true
      const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
      if (isDistrictAdministrator) {
        this.tableData = await this.listRegionLearningReportFormsInServicerRegion()
      } else {
        if (this.isHaveFxRole) {
          this.tableData = await this.pageStudentSchemeLearningInDistributor()
        } else if (this.isHaveZtRole) {
          this.tableData = await this.listRegionLearningReportFormsInTrainingChannel()
        } else {
          this.tableData = await this.listRegionLearningReportFormsInServicer()
        }
      }
      if (this.activeName == 'first') {
        ;(this.$refs['schemeTable'] as any)?.doLayout()
      } else if (this.activeName == 'second') {
        ;(this.$refs['schemeMixedTable'] as any)?.doLayout()
      } else {
        ;(this.$refs['schemeFaceTable'] as any)?.doLayout()
      }
      this.loading = false
    }
    async created() {
      this.getLearningAnomalousCount()
      // await this.doQueryPage()
      this.schoolConfigFlag = await getServicerIsDocking.getServicerIsDocking()
      this.queryAddService()
      this.listFields = this.clearOffline(this.listFields)
    }

    clearOffline(list: Array<any>) {
      let temp = list
      if (!this.showOffline) {
        temp = list.filter(
          (description) => !(description.field.includes('培训期别') || description.description.includes('培训期别'))
        )
      }
      return temp
    }
    get showNeedPeriod() {
      return (row: any) => {
        if (
          !row.userAssessResult?.electiveRequirePeriod?.current &&
          row.userAssessResult?.electiveRequirePeriod?.current != 0
        ) {
          return false
        }
        if (
          !row.userAssessResult?.compulsoryRequirePeriod?.current &&
          row.userAssessResult?.compulsoryRequirePeriod?.current != 0
        ) {
          return false
        }
        return true
      }
    }

    get showGetPeriod() {
      return (row: any) => {
        if (
          !row.userAssessResult?.electiveRequirePeriod?.current &&
          row.userAssessResult?.electiveRequirePeriod?.current != 0
        ) {
          return false
        }
        if (
          !row.userAssessResult?.compulsoryRequirePeriod?.current &&
          row.userAssessResult?.compulsoryRequirePeriod?.current != 0
        ) {
          return false
        }
        return true
      }
    }
    // 避免精度缺失
    get numToFixed() {
      return (num: number) => {
        if (!num) return 0
        const numStr = num.toString()
        if (numStr.indexOf('.') != -1 && numStr.length - numStr.indexOf('.') > 3) {
          num = Number(num.toFixed(2))
        }
        return num
      }
    }
    // 选择是否来源专题
    changeSaleChannels() {
      this.filter.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(this.saleChannels)
      if (!this.saleChannels && this.saleChannels != null) {
        this.trainingChannelName = ''
      }
    }
    async activated() {
      this.baseConfig.queryDetail()
      if (this.$route.query.subjectName) {
        this.filter.trainingChannelName = this.$route.query.subjectName as string
        this.trainingChannelName = this.$route.query.subjectName as string
      } else {
        this.filter.trainingChannelName = this.trainingChannelName
      }
      if (this.$route.query.subjectType) {
        this.filter.saleChannels = JSON.parse(this.$route.query.subjectType as string)
        if (this.$route.query.subjectType == '[2]') {
          this.saleChannels = true
        } else if (this.$route.query.subjectType == '[0,1]') {
          this.saleChannels = false
        } else {
          this.saleChannels = null
        }
      } else {
        this.filter.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(this.saleChannels)
      }
      if (this.$route.query.schemeName) {
        // schemeName 未声明不是响应式，需要先声明或用this.$set赋值
        // this.$set(this.localSkuProperty, 'schemeName', this.$route.query.schemeName)
        this.localSkuProperty.schemeName = this.$route.query.schemeName as string
      }
      if (this.$route.query.year) {
        this.localSkuProperty.year = this.$route.query.year ? JSON.parse(this.$route.query.year as string) : []
      }
      if (this.$route.query.regionType) {
        const regionType = (this.$route.query.regionType as string)?.split('/')
        for (let i = 0; i < regionType.length; i++) {
          if (!regionType[i]) {
            regionType.splice(i, 1)
          }
        }
        this.localSkuProperty.regionList = regionType
      }
      if (this.$route.query.registerTimeBegin) {
        this.localSkuProperty.registerTimeBegin = this.$route.query.registerTimeBegin as string
      }
      if (this.$route.query.registerTimeEnd) {
        this.localSkuProperty.registerTimeEnd = this.$route.query.registerTimeEnd as string
      }
      if (this.$route.query.regionSkuPropertySearch) {
        this.localSkuProperty.region = [this.$route.query.regionSkuPropertySearch as string]
      }
      if (this.$route.query.schemeId) {
        this.schemeId = this.$route.query.schemeId as string
      }
      if (this.$route.query.periodId) {
        this.localSkuProperty.periodId = this.$route.query.periodId as string
        this.localSkuProperty.issueName = this.$route.query.periodName as string
      } else if (this.$route.query.schemeId) {
        this.schemeId = this.$route.query.schemeId as string
        this.localSkuProperty.schemeName = this.$route.query.schemeName as string
      }
      if (this.$route.query.trainingType == 'trainingWay0001' || this.$route.query.trainingMode == 'trainingWay0001') {
        this.activeName = 'first'
      } else if (
        this.$route.query.trainingType == 'trainingWay0002' ||
        this.$route.query.trainingMode == 'trainingWay0002'
      ) {
        this.activeName = 'second'
      } else if (
        this.$route.query.trainingType == 'trainingWay0003' ||
        this.$route.query.trainingMode == 'trainingWay0003'
      ) {
        this.activeName = 'third'
      }

      await this.doQueryPage()
    }

    async search() {
      this.page.pageNo = 1
      await this.doQueryPage()
    }
    doManagement() {
      this.exceptionManagementRef.init()
    }
    async doExport() {
      this.$confirm('是否确认导出数据?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(
        async () => {
          const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
          this.getLocalSkuProperty()
          let res
          if (isDistrictAdministrator) {
            res = await this.exportExcelRegion()
          } else {
            if (this.isHaveFxRole) {
              res = await this.exportStudentSchemeLearningExcelInSDistributor()
            } else if (this.isHaveZtRole) {
              res = await this.exportExcelTrainingChannel()
            } else {
              res = await this.exportExcel()
            }
          }

          if (res.status.code == 200 && res.data) {
            this.exportSuccessVisible = true
            // this.$message.success('导出成功')
          } else {
            this.$message.warning('导出失败')
          }
        },
        () => {
          // nothing
        }
      )
    }
    /**
     * 地区管理员导出
     */
    async exportExcelRegion() {
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningManagerRegionList()
        .exportExcel(this.filter)
    }
    /**
     * 分销管理员导出
     */
    async exportStudentSchemeLearningExcelInSDistributor() {
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningList()
        .exportStudentSchemeLearningExcelInSDistributor(this.filter)
    }
    /**
     * 专题管理员导出
     */
    async exportExcelTrainingChannel() {
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningList()
        .exportExcelTrainingChannel(this.filter)
    }
    /**
     * 超级管理员导出
     */
    async exportExcel() {
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningList()
        .exportExcel(this.filter)
    }

    getLocalSkuProperty() {
      this.userRequest = new UserRequest()
      this.userRequest.userProperty = new UserPropertyRequest()
      this.userRequest.userProperty.companyName = this.localSkuProperty.companyName
      this.userRequest.userProperty.regionList = new Array<RegionRequest>()
      let province
      let city
      let county
      if (this.localSkuProperty.regionList && this.localSkuProperty.regionList.length > 0) {
        if (this.localSkuProperty.regionList[0]) {
          province = this.localSkuProperty.regionList[0] as string
        }
        if (this.localSkuProperty.regionList[1]) {
          city = this.localSkuProperty.regionList[1] as string
        }
        if (this.localSkuProperty.regionList[2]) {
          county = this.localSkuProperty.regionList[2] as string
        }
        this.userRequest.userProperty.regionList.push({ province, city, county })
      } else {
        // province = '350000'
        // this.userRequest.userProperty.regionList.push({ province, city, county })
        this.userRequest.userProperty.regionList = []
      }
      this.learningRegisterRequest = new LearningRegisterRequest()
      this.learningRegisterRequest.registerTime = new DateScopeRequest()
      this.learningRegisterRequest.registerTime.begin = this.localSkuProperty.registerTimeBegin
      this.learningRegisterRequest.registerTime.end = this.localSkuProperty.registerTimeEnd
      this.schemeRequest = new SchemeRequest()
      this.schemeRequest.schemeName = this.localSkuProperty.schemeName
      // this.schemeRequest.schemeName = this.localSkuProperty.schemeName
      // this.schemeRequest.schemeId = this.$route.query.schemeId as string
      // if (this.hasSelectSchemeMode.length) {
      //   this.schemeRequest.schemeId = this.hasSelectSchemeMode[0].id
      // } else {
      this.schemeRequest.schemeId = this.schemeId
      // }
      if (this.localSkuProperty.schemeTypeInfo && this.localSkuProperty.schemeTypeInfo.length > 0) {
        this.schemeRequest.schemeType =
          this.localSkuProperty.schemeTypeInfo[this.localSkuProperty.schemeTypeInfo.length - 1]
      }
      this.schemeRequest.skuProperty = new SchemeSkuPropertyRequest()
      if (this.localSkuProperty.industry) {
        this.schemeRequest.skuProperty.industry = [this.localSkuProperty.industry]
      }
      if (this.localSkuProperty.subjectType) {
        this.schemeRequest.skuProperty.subjectType = [this.localSkuProperty.subjectType]
      }
      if (this.localSkuProperty.trainingCategory) {
        this.schemeRequest.skuProperty.trainingCategory = [this.localSkuProperty.trainingCategory]
      }
      if (this.localSkuProperty.positionCategory) {
        this.schemeRequest.skuProperty.positionCategory = [this.localSkuProperty.positionCategory]
      }
      if (this.localSkuProperty.trainingObject) {
        this.schemeRequest.skuProperty.trainingObject = [this.localSkuProperty.trainingObject]
      }
      if (this.localSkuProperty.constructionTrainingMajor) {
        this.schemeRequest.skuProperty.trainingProfessional = [this.localSkuProperty.constructionTrainingMajor]
      }
      if (this.localSkuProperty?.societyTrainingMajor?.length) {
        this.schemeRequest.skuProperty.trainingProfessional = this.localSkuProperty.societyTrainingMajor
      }
      if (this.localSkuProperty.technicalGrade) {
        this.schemeRequest.skuProperty.technicalGrade = [this.localSkuProperty.technicalGrade]
      }
      if (this.localSkuProperty.year) {
        this.schemeRequest.skuProperty.year = this.localSkuProperty.year
      }
      // 学科、学段转换
      if (this.localSkuProperty.learningPhase) {
        this.schemeRequest.skuProperty.learningPhase = [this.localSkuProperty.learningPhase]
      }
      if (this.localSkuProperty.discipline) {
        this.schemeRequest.skuProperty.discipline = [this.localSkuProperty.discipline]
      }
      if (this.localSkuProperty.region) {
        this.schemeRequest.skuProperty.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
        this.schemeRequest.skuProperty.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
        const localRegion = cloneDeep(this.localSkuProperty.region)
        if (Array.isArray(localRegion) && localRegion.length) {
          const option = new RegionSkuPropertyRequest()
          //物理地区改为业务地区
          option.province = localRegion ? localRegion[0] : undefined
          option.city = localRegion ? localRegion[1] : undefined
          option.county = localRegion ? localRegion[2] : undefined
          // option.province = '350000'
          // option.city = localRegion ? localRegion[0] : undefined
          // option.county = undefined
          this.schemeRequest.skuProperty.regionSkuPropertySearch.region.push(option)
          this.schemeRequest.skuProperty.regionSkuPropertySearch.regionSearchType = 1
        } else {
          this.schemeRequest.skuProperty.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
        }
      }
      if (this.activeName == 'first') {
        this.schemeRequest.skuProperty.trainingWay = ['trainingWay0001']
      } else if (this.activeName == 'second') {
        this.schemeRequest.skuProperty.trainingWay = ['trainingWay0002']
      } else {
        this.schemeRequest.skuProperty.trainingWay = ['trainingWay0003']
      }
      //   this.studentLearningRequest = new StudentLearningRequest()

      this.studentLearningRequest.trainingResultList = cloneDeep(this.localSkuProperty.trainingResultList)
      if (this.localSkuProperty.trainingResultList && this.localSkuProperty.trainingResultList.indexOf(0) > -1) {
        this.studentLearningRequest.trainingResultList.push(-1)
      }
      //   //学习统计参数处理开始
      //   if (this.$route.query.regionType) {
      //     const regionType = this.$route.query.regionType as string
      //     this.regionType = regionType.split('/')
      //     for (let i = 0; i < this.regionType.length; i++) {
      //       if (!this.regionType[i]) {
      //         this.regionType.splice(i, 1)
      //       }
      //     }
      //     this.userRequest.userProperty.regionList = [
      //       { province: this.regionType[0], city: this.regionType[1], county: this.regionType[2] }
      //     ]
      //   }
      //   if (this.$route.query.schemeName) {
      //     this.schemeRequest.schemeName = this.$route.query.schemeName as string
      //   }
      //   if (this.$route.query.year) {
      //     this.schemeRequest.skuProperty.year = this.$route.query.year as string[]
      //   }
      //   if (this.$route.query.registerTimeBegin) {
      //     this.learningRegisterRequest.registerTime.begin = this.$route.query.registerTimeBegin as string
      //   }
      //   if (this.$route.query.registerTimeEnd) {
      //     this.learningRegisterRequest.registerTime.end = this.$route.query.registerTimeEnd as string
      //   }
      //学习统计参数处理结束
      this.studentLearningRequest.trainingResultTime = new DateScopeRequest()
      this.studentLearningRequest.trainingResultTime.begin = this.localSkuProperty.trainingResultTimeBegin
      this.studentLearningRequest.trainingResultTime.end = this.localSkuProperty.trainingResultTimeEnd
      if (
        (this.studentLearningRequest.trainingResultTime.begin || this.studentLearningRequest.trainingResultTime.end) &&
        !this.studentLearningRequest.trainingResultList.length
      ) {
        this.studentLearningRequest.trainingResultList = [1]
      }
      this.dataAnalysisRequest = new DataAnalysisRequest()
      this.dataAnalysisRequest.trainingResultPeriod = new DoubleScopeRequest()
      this.dataAnalysisRequest.trainingResultPeriod.begin = this.localSkuProperty.trainingResultPeriodBegin
      this.dataAnalysisRequest.trainingResultPeriod.end = this.localSkuProperty.trainingResultPeriodEnd
      this.dataAnalysisRequest.requirePeriod = new DoubleScopeRequest()
      this.dataAnalysisRequest.requirePeriod.begin = this.localSkuProperty.requirePeriodBegin
      this.dataAnalysisRequest.requirePeriod.end = this.localSkuProperty.requirePeriodEnd
      this.dataAnalysisRequest.acquiredPeriod = new DoubleScopeRequest()
      this.dataAnalysisRequest.acquiredPeriod.begin = this.localSkuProperty.acquiredPeriodBegin
      this.dataAnalysisRequest.acquiredPeriod.end = this.localSkuProperty.acquiredPeriodEnd
      const distributorId = this.filter.learningRegister.distributorId
      this.filter.learningRegister.portalId = this.portalId
      const notDistributionPortal = this.filter.notDistributionPortal
      this.filter = new StudentSchemeLearningRequestVo()
      // 分销商id不置空，解决切换tab分销商筛选项显示丢失问题
      const temp = new StudentSchemeLearningRequestVo()
      temp.learningRegister = this.learningRegisterRequest
      temp.learningRegister.distributorId = distributorId
      this.filter = temp
      if (this.activeName == 'first') {
        this.filter.trainingType = 'trainingWay0001'
      } else if (this.activeName == 'second') {
        this.filter.trainingType = 'trainingWay0002'
      } else {
        this.filter.trainingType = 'trainingWay0003'
      }
      this.filter.dataAnalysis = this.dataAnalysisRequest
      this.filter.name = this.localSkuProperty.name
      this.filter.loginAccount = this.localSkuProperty.loginAccount
      this.localSkuProperty.name ? '' : delete this.filter.name

      this.filter.idCard = this.localSkuProperty.idCard
      this.localSkuProperty.idCard ? '' : delete this.filter.idCard
      this.filter.phone = this.localSkuProperty.phone
      this.localSkuProperty.phone ? '' : delete this.filter.phone
      this.filter.studentLearning = this.studentLearningRequest
      this.filter.issueId = this.localSkuProperty.periodId

      if (
        !this.studentLearningRequest.trainingResultList &&
        !(this.studentLearningRequest.trainingResultTime.begin || this.studentLearningRequest.trainingResultTime.end)
      ) {
        delete this.filter.studentLearning
      } else {
        this.studentLearningRequest.trainingResultList.length
          ? this.studentLearningRequest.trainingResultList
          : delete this.filter.studentLearning.trainingResultList
        this.studentLearningRequest.trainingResultTime.begin || this.studentLearningRequest.trainingResultTime.end
          ? this.studentLearningRequest
          : delete this.filter.studentLearning.trainingResultTime
      }
      if (JSON.stringify(this.filter.studentLearning) === '{}') {
        delete this.filter.studentLearning
      }
      this.filter.scheme = this.schemeRequest
      // this.filter.learningRegister = this.learningRegisterRequest
      // this.filter.learningRegister.distributorId = distributorId
      this.filter.notDistributionPortal = notDistributionPortal
      this.filter.learningRegister.portalId = this.portalId
      if (this.filter.notDistributionPortal) {
        this.filter.learningRegister.portalId = ''
      }
      this.filter.student = this.userRequest
      this.filter.syncResult = this.localSkuProperty.syncResult
      this.filter.trainingChannelName = this.trainingChannelName
      this.filter.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(this.saleChannels)
    }
    async doQueryPage() {
      this.loading = true
      this.getLocalSkuProperty()
      this.filter.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(this.saleChannels)
      this.filter.trainingChannelName = this.trainingChannelName
      if (this.activeName == 'first') {
        this.filter.trainingType = 'trainingWay0001'
      } else if (this.activeName == 'second') {
        this.filter.trainingType = 'trainingWay0002'
      } else {
        this.filter.trainingType = 'trainingWay0003'
      }
      const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
      if (isDistrictAdministrator) {
        this.tableData = await this.listRegionLearningReportFormsInServicerRegion()
      } else {
        if (this.isHaveFxRole) {
          this.tableData = await this.pageStudentSchemeLearningInDistributor()
        } else if (this.isHaveZtRole) {
          this.tableData = await this.listRegionLearningReportFormsInTrainingChannel()
        } else {
          this.tableData = await this.listRegionLearningReportFormsInServicer()
        }
      }
      console.log('🚀🐱‍🚀🐱‍👓 ~ doQueryPage ~ this.tableData:', this.tableData)
      if (this.activeName == 'first') {
        ;(this.$refs['schemeTable'] as any)?.doLayout()
      } else if (this.activeName == 'second') {
        ;(this.$refs['schemeMixedTable'] as any)?.doLayout()
      } else {
        ;(this.$refs['schemeFaceTable'] as any)?.doLayout()
      }

      this.loading = false
    }

    /**
     * 地区管理员查询
     */
    async listRegionLearningReportFormsInServicerRegion() {
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningManagerRegionList()
        .listRegionLearningReportFormsInServicer(this.page, this.filter)
    }
    /**
     * 分销管理员查询
     */
    async pageStudentSchemeLearningInDistributor() {
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningList()
        .pageStudentSchemeLearningInDistributor(this.page, this.filter)
    }
    /**
     * 专题管理员查询
     */
    async listRegionLearningReportFormsInTrainingChannel() {
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningList()
        .listRegionLearningReportFormsInTrainingChannel(this.page, this.filter)
    }
    /**
     * 网校管理员查询
     */
    async listRegionLearningReportFormsInServicer() {
      return await StaticticalReportManagerModule.queryStaticticalReportFactory
        .getQueryStudentLearningList()
        .listRegionLearningReportFormsInServicer(this.page, this.filter)
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.technicalGrade = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      // 学段、学科
      this.localSkuProperty.discipline = ''
      this.localSkuProperty.learningPhase = ''
    }
    /**
     * 学段变化清空学科
     */
    updateStudyPeriod() {
      this.localSkuProperty.discipline = ''
    }
    /**
     * 响应组件行业Id集合传参
     */
    handleIndustryInfos(values: any) {
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.professionHealthIndustryId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
    }
    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => (el.name = '科目类型'))
      const configTrainingCategory = configList.findIndex((el) => (el.name = '培训类别'))
      const configPositionCategory = configList.findIndex((el) => (el.code = 'POSITION_CATEGORY'))
      const configTrainingObject = configList.findIndex((el) => (el.code = 'TRAINNING_OBJECT'))
      const configTechnicalLevel = configList.findIndex((el) => (el.code = 'JOB_LEVEL'))
      // 修改
      const configTechnicalStudying = configList.findIndex((el) => (el.code = '学段'))
      const configTechnicalSubject = configList.findIndex((el) => (el.code = '学科'))
      this.skuVisible.subjectType = configSubjectType > -1 ? true : false
      this.skuVisible.trainingCategory = configTrainingCategory > -1 ? true : false
      this.skuVisible.positionCategory = configPositionCategory > -1 ? true : false
      this.skuVisible.trainingObject = configTrainingObject > -1 ? true : false
      this.skuVisible.technicalGrade = configTechnicalLevel > -1 ? true : false
      // 获取教师配置
      this.skuVisible.learningPhase = configTechnicalStudying > -1 ? true : false
      this.skuVisible.discipline = configTechnicalSubject > -1 ? true : false
    }
    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }
    // 卫生行业培训类别联动
    handleWSUpdateTrainingObject(value: string) {
      this.localSkuProperty.positionCategory = ''
      this.trainingObjectId = value
    }
    examDetail(qualificationId: string, row: StudentLearningStaticsVo) {
      this.qualificationId = qualificationId
      this.isExamDetail = true
      console.log(this.examDetailRef, 'this.examDetailRef')
      this.$nextTick(() => {
        this.examDetailRef.dataItem = row
      })
    }
    testDetail(qualificationId: string) {
      this.qualificationId = qualificationId
      this.isTestDetail = true
    }

    // 跳转考勤详情
    attendanceDetail(periodQualificationId: string, period: string) {
      this.periodQualificationId = periodQualificationId
      this.period = period
      this.isAttendanceDetail = true
    }

    isExamDialog(isShowDrawer: boolean) {
      this.isExamDetail = isShowDrawer
    }
    isTextDialog(isShowDrawer: boolean) {
      this.isTestDetail = isShowDrawer
    }
    /**
     * 导出任务下载
     */
    goDownloadPage() {
      this.exportSuccessVisible = false
      const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
      if (isDistrictAdministrator) {
        this.$router.push({
          path: '/statistic/export-task',
          query: { type: 'exportStudentLearningStatistical' }
        })
      } else {
        this.$router.push({
          path: '/training/task/exporttask',
          query: { type: 'exportStudentLearningStatistical' }
        })
        // // TODO LZH 面授导出跳转
        // if (this.activeName == 'first') {
        //   this.$router.push({
        //     path: '/training/task/exporttask',
        //     query: { type: 'exportOnlineStudyReport' }
        //   })
        // } else {
        //   this.$router.push({
        //     path: '/training/task/exporttask',
        //     query: { type: 'exportOnlineAndFaceStudyReport' }
        //   })
        // }
      }
    }
    /**
     * 转换单位地区
     */
    transfromRegion(region: RegionModel) {
      const temp = new Array<string>()
      if (region?.provinceName) {
        temp.push(region.provinceName)
      }
      if (region?.cityName) {
        temp.push(region.cityName)
      }
      if (region?.countyName) {
        temp.push(region.countyName)
      }
      return temp.join('-') || '-'
    }

    // 前往学习日志
    toStudyLog(row: StudentLearningStaticsVo) {
      // scope.row.studentDetail.userId, scope.row.trainClassBaseInfo.id
      const { userId } = row.studentDetail
      const schemeId = row.trainClassBaseInfo.id
      const { qualificationId, schemeType } = row.trainClassBaseInfo
      const { studentNo } = row
      this.$router.push({
        path: '/statistic/statistics-report/study-log',
        query: { userId, schemeId, qualificationId, schemeType: String(schemeType), studentNo }
      })
    }
    learningLog(row: StudentLearningStaticsVo) {
      const { userId } = row.studentDetail
      const { qualificationId } = row.trainClassBaseInfo
      const studentNo = row.studentNo
      const parentPage = 'studyDetail'
      this.$router.push({
        path: '/statistic/statistics-report/learning-log',
        query: { userId, qualificationId, studentNo, parentPage }
      })
    }
    // 获取数据异常管理数量
    async getLearningAnomalousCount() {
      let res
      if (this.isHaveZtRole) {
        res = await this.learnAnomalousModule.queryStatisticsTrainingChannel()
      } else {
        res = await this.learnAnomalousModule.queryStatistics()
      }
      // 智能学习异常数量 + 学习规则异常数量
      if (!this.isHaveZtRole) {
        const intelligenceAbnormalList = new IntelligenceAbnormalList()
        const page = new UiPage()
        await intelligenceAbnormalList.queryList(page)
        this.learnAnomalousCount = page.totalSize
      }
      this.ruleAnomalousCount = res.supplementStudyErrorCount
    }
    getChildRef(e: any, type: string) {
      if (type == 'online') {
        this.$refs['schemeTable'] = e
      } else if (type == 'mixed') {
        this.$refs['schemeMixedTable'] = e
      } else if (type == 'face') {
        this.$refs['schemeFaceTable'] = e
      }
    }
    getStudentNoList(e: string[]) {
      this.studentNoList = e
      return
    }
    // 考勤详情显隐
    isAttendanceDialog(isShowDrawer: boolean) {
      this.isAttendanceDetail = isShowDrawer
    }
    changeStavisible() {
      this.stavisible = true
    }
    handleIssueId(e: string) {
      this.localSkuProperty.periodId = e
    }
  }
</script>
