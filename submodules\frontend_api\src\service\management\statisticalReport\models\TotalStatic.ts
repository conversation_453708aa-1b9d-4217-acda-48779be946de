import {
  DistributionSellStatisticResponse,
  TradeStatisticResponse
} from '@api/platform-gateway/fxnl-query-front-gateway-backstage'

export default class TotalStatic {
  // 净开通
  netTurnOn = 0
  // 成交总额
  transactionTotal = 0

  static toTotalStatic(dto: DistributionSellStatisticResponse) {
    const vo = new TotalStatic()
    vo.netTurnOn = dto.netTradeSuccessCount
    vo.transactionTotal = dto.netTradeSuccessAmount
    return vo
  }

  static TradeStatisticResponseToTotalStatic(dto: TradeStatisticResponse) {
    const vo = new TotalStatic()
    vo.netTurnOn = dto?.netTradeSuccessCount || 0
    vo.transactionTotal = dto?.netTradeSuccessAmount || 0
    return vo
  }
}
