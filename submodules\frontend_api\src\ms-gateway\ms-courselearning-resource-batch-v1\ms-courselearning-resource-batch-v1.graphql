"""独立部署的微服务,K8S服务名:ms-courselearning-resource-v1"""
schema {
	query:Query
	mutation:Mutation
}
"""Long type"""
scalar Long
"""Short as Int"""
scalar Short
"""Byte as Int"""
scalar Byte
"""java.math.BigDecimal"""
scalar BigDecimal
"""java.math.BigInteger"""
scalar BigInteger
"""Char as Character"""
scalar Char
"""日期时间标量类型"""
scalar DateTime
scalar Void
scalar Map
scalar Double
type Query {
	getSchemaName:String
}
type Mutation {
	"""同步课程包下所有课程信息
		@param coursePackageId 课程包使用情况id集合
	"""
	syncAllCoursePackageUsed(coursePackageId:String):Void
}

scalar List
