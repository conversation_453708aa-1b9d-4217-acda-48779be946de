import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * 增值服务枚举
 */
export enum AddServiceEnum {
  /**
   * 学习规则
   */
  learningRule = '1',
  /**
   * 智能学习
   */
  intelligentlearning = '2',
  /**
   *分销服务
   */
  fxService = '3'
}

class AddService extends AbstractEnum<AddServiceEnum> {
  static enum = AddServiceEnum

  constructor(status?: AddServiceEnum) {
    super()
    this.current = status
    this.map.set(AddServiceEnum.learningRule, '学习规则')
    this.map.set(AddServiceEnum.intelligentlearning, '智能学习')
    this.map.set(AddServiceEnum.fxService, '分销服务')
  }
}

export default AddService
