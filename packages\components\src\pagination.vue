<template>
  <el-pagination
    class="f-mt15 f-tr"
    @size-change="pageSizeChange"
    @current-change="currentChange"
    :current-page="page.pageNo"
    :page-sizes="pageSizes"
    :page-size="page.pageSize"
    :total="totalSize"
    v-bind="page"
    background
    layout="total, sizes, prev, pager, next, jumper"
  >
  </el-pagination>
</template>

<script lang="ts">
  import { Component, Emit, Prop, Vue } from 'vue-property-decorator'
  import Page from '@hbfe/jxjy-admin-common/src/models/Page'

  @Component
  export default class extends Vue {
    @Prop({
      required: true,
      default: () => new Page()
    })
    page: Page
    @Prop({
      required: true,
      default: 0
    })
    totalSize: number
    @Prop({
      default: () => [5, 10, 15, 20, 25]
    })
    pageSizes: Array<number>

    @Emit('current-change')
    currentChange(val: number) {
      this.page.currentChange(val)
    }

    @Emit('size-change')
    pageSizeChange(val: number) {
      this.page.pageSizeChange(val)
    }
  }
</script>
