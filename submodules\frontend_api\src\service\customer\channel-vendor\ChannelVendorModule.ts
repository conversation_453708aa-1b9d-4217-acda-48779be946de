import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ResponseStatus } from '@api/Response'
import channelVendorGateway, {
  ApplyChannelVendorApplyParams,
  ServicerDetailDto
} from '@api/gateway/PlatformChannelVendor'

/*export interface IChannelVendorState {}*/

@Module({ namespaced: true, dynamic: true, name: 'CustomerChannelVendorModule', store })
class ChannelVendorModule extends VuexModule /*implements IChannelVendorState*/ {
  detailDto = new ServicerDetailDto()

  /**
   * 申请成为渠道商
   */
  @Action
  async apply(params: { captchaToken?: string; params?: ApplyChannelVendorApplyParams }): Promise<ResponseStatus> {
    const response = await channelVendorGateway.apply(params)
    return response.status
  }

  /**
   * 详情
   */
  @Action
  async detail(id: string): Promise<ResponseStatus> {
    const response = await channelVendorGateway.detail(id)
    if (response.status.isSuccess()) {
      this.SET_DETAIL_INFO(response.data)
    }
    return response.status
  }

  /**
   * 设置详情
   */
  @Mutation
  private SET_DETAIL_INFO(data: ServicerDetailDto) {
    this.detailDto = data
  }
}

export default getModule(ChannelVendorModule)
