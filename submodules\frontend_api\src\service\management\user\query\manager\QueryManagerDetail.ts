import { Response, ResponseStatus } from '@hbfe/common'
import BasicDataQueryBackstage, {
  AdminInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import QueryLoginStatus from './QueryLoginStatus'
import PlatformJxjypxtyptAccount, {
  ChangeUnitAuthorizeRequest,
  ChangeUnitAuthorizeResponse
} from '@api/platform-gateway/platform-jxjypxtypt-account-v1'
import DistributionUnitInformation from '@api/service/management/user/query/manager/vo/DistributionUnitInformation'
import { CategoryEnums } from '@api/service/management/authority/role/RoleBaseInfo'
import Context from '@api/service/common/context/Context'

/**
 * 查询管理员详情
 */
class QueryManagerDetail {
  /**
   * 查询管理员详情
   */
  adminInfo = new AdminInfoResponse()
  /**
   * 是否地区管理员
   */
  isRegionAdmin = false

  /**
   * 关联分销单位列表
   */
  distributionUnitInformationList: DistributionUnitInformation[] = []

  /**
   * 判断当前用户是否拥有当前角色类型
   * @param CategoryEnums
   */
  get hasCategory() {
    return (category: CategoryEnums) => {
      return this.adminInfo.roleList?.findIndex(res => res.roleCategory === category) >= 0
    }
  }

  /**
   * 获取当前帐号单位信息
   */
  get currentUnitInfo() {
    const currentUnitId = localStorage.getItem('currentUnitId')
    return this.distributionUnitInformationList.find(res => res.unitId === currentUnitId)
  }

  async queryManagerDetail(): Promise<ResponseStatus> {
    const response = await BasicDataQueryBackstage.getAdminInfoInMyself()
    if (!response.status.isSuccess()) {
      return response.status
    }
    this.adminInfo = response.data
    if (this.adminInfo.roleList.find(role => role.roleCategory === 320)) this.isRegionAdmin = true
    if (response.status.isSuccess()) {
      QueryLoginStatus.setLoginStatus(true)
      QueryLoginStatus.setShowDialog(false)
    }
    return response.status
  }

  /**
   * 查询人员与单位关系接口
   */
  async changeAuthorizationUnitInfoList() {
    const request = new ChangeUnitAuthorizeRequest()
    request.onlineSchoolId = Context.businessEnvironment.serviceToken?.tokenMeta?.servicerId
    if (!this.distributionUnitInformationList?.length) {
      const response = await PlatformJxjypxtyptAccount.changeAuthorizationUnitInfoList(request)
      if (response.data?.code != '200' || !response.status.isSuccess()) {
        console.error('获取关联关系报错', response)
        return response
      }
      this.distributionUnitInformationList = response.data.changeTokenInfos.map(res =>
        DistributionUnitInformation.toDistributionUnitInformation(res)
      )
      return response
    } else {
      const response = new Response<ChangeUnitAuthorizeResponse>()
      response.data = new ChangeUnitAuthorizeResponse()
      response.data.code = '200'
      response.status = new ResponseStatus(200, '请求成功')
      return response
    }
  }
}
export default new QueryManagerDetail()
