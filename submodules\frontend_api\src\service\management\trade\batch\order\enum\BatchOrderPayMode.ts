import AbstractEnum from '@api/service/common/enums/AbstractEnum'

/**
 * @description 【集体报名订单】下单结果-支付方式枚举
 */
export enum BatchOrderPayModeEnum {
  // 线上支付
  Online_Pay = 1,
  // 线下支付
  Offline_Pay
}

/**
 * @description 【集体报名订单】下单结果-支付方式
 */
class BatchOrderPayMode extends AbstractEnum<BatchOrderPayModeEnum> {
  static enum = BatchOrderPayModeEnum
  constructor(status?: BatchOrderPayModeEnum) {
    super()
    this.current = status
    this.map.set(BatchOrderPayModeEnum.Online_Pay, '线上支付')
    this.map.set(BatchOrderPayModeEnum.Offline_Pay, '线下支付')
  }
}

export default new BatchOrderPayMode()
