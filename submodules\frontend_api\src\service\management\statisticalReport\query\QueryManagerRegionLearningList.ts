import exportMsgateway from '@api/platform-gateway/diff-ms-data-export-front-gateway-DataExportBackstage'
import schemeLearningMsGateway, {
  LearningReportFormsRequest
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
import { RegionLearningStatisticsResponseVo } from '@api/service/management/statisticalReport/query/vo/RegionLearningStatisticsResponseVo'
import { cloneDeep } from 'lodash'
import { LearningStatistic } from '@api/service/management/statisticalReport/models/LearningStatistic'

/**
 * 【超管】地区学习统计列表
 * @description
 */
export default class QueryManagerRegionLearningList {
  /**
   * 【超管】合计
   */
  statisticsM = new LearningStatistic()
  /**
   * 【超管】暂无地区的学习统计
   */
  withoutStaticM = new LearningStatistic()
  /**
   * 【超管】地区学习统计列表
   */
  async listRegionLearningReportFormsInServicer(
    filter: LearningReportFormsRequest,
    vendorId = '',
    isPopularize = false
  ): Promise<Array<RegionLearningStatisticsResponseVo>> {
    try {
      console.log('filter参数=', filter)
      const res = await schemeLearningMsGateway.listRegionLearningReportFormsInServicerJXJY(filter)
      const tmpArr = []

      if (res.status.isSuccess() && res.data && res.data.length) {
        // 额外处理暂无地区的数据
        const findIdx = res.data.findIndex(el => el.region.province === '暂无地区')
        if (findIdx > -1) {
          this.withoutStaticM = cloneDeep(res.data[findIdx].learningStatistic || new LearningStatistic())
          res.data.splice(findIdx, 1)
        } else {
          this.withoutStaticM = new LearningStatistic()
        }

        for (const tmpArrElement of res.data) {
          const reportVo = new RegionLearningStatisticsResponseVo()
          Object.assign(reportVo, tmpArrElement)
          tmpArr.push(reportVo)
        }

        // await this.getStatistics(filter)
      }

      console.log('调用了listRegionLearningReportFormsInServicer方法，返回值=', tmpArr)
      return tmpArr
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryManagerRegionLearningList.ts所处方法，listRegionLearningReportFormsInServicer',
        e
      )
    }
  }

  /**
   * 导出
   */
  async exportExcel(param: LearningReportFormsRequest) {
    try {
      param.learningRegister.status = [1, 2]
      console.log('param参数=', param)
      const res = await exportMsgateway.exportRegionLearningReportFormsExcelInServicer(param)
      console.log('调用了exportExcel方法，返回值=', res)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryManagerRegionLearningList.ts所处方法，exportExcel',
        e
      )
    }
  }

  /**
   * 【超管】导出明细
   */
  async exportExcelDetail(param: LearningReportFormsRequest) {
    try {
      param.learningRegister.status = [1, 2]
      console.log('param参数=', param)
      const res = await exportMsgateway.exportRegionLearningReportFormsDetailExcelInServicer(param)
      console.log('调用了exportExcel方法，返回值=', res)
      return res
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryManagerRegionLearningList.ts所处方法，exportExcelDetail',
        e
      )
    }
  }

  /**
   * 【超管】获取报表统计合计数据
   */
  async getStatistics(filter: LearningReportFormsRequest) {
    try {
      console.log('filter参数=', filter)
      const res = await schemeLearningMsGateway.getRegionLearningReportSummeryInServicerJXJY(filter)

      if (res.status.isSuccess()) {
        this.statisticsM = res.data
      }
    } catch (e) {
      console.log(
        '报错了，所处位置/service/management/statisticalReport/query/QueryManagerRegionLearningList.ts所处方法，getStatistics',
        e
      )
    }
  }
}
