import { CreateStudentAndPlaceOrderResponse as CreateStudentAndPlaceOrder } from '@api/diff-gateway/platform-jxjypxtypt-fjzj-school'
export default class CreateStudentAndPlaceOrderResponse extends CreateStudentAndPlaceOrder {
  /**
   * 学员ID
   */
  userId = ''
  /**
   * 订单号
   */
  orderNo = ''
  /**
   * 子订单号列表
   */
  subOrderNoList: Array<string> = []
  /**
   * 登录token
   */
  token = ''
  /**
   * 参训资格id
   */
  qualificationId = ''
  /**
   * 状态码
   */
  code = ''
  /**
   * 状态信息
   */
  message = ''

  get subOrderNo() {
    return this.subOrderNoList?.[0] ?? ''
  }
}
