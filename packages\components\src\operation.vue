<template>
  <el-drawer :title="title" :visible.sync="show" size="800px" custom-class="m-drawer">
    <div class="drawer-bd">
      <el-row type="flex" justify="center">
        <el-col :span="18">
          <el-form ref="form" :model="detail" label-width="auto" class="m-form f-mt20">
            <el-form-item label="所属节点：" required>
              <components v-model="detail.parentId" :is="switchComponent" v-if="!isDetailMode"></components>
              <div v-else>
                <el-skeleton animated :rows="1" v-if="!detail.id" />
                {{ detail.parentName }}
              </div>
            </el-form-item>
            <el-form-item label="分类名称：" required>
              <el-input
                v-model="detail.name"
                clearable
                maxlength="30"
                show-word-limit
                v-if="!isDetailMode"
                placeholder="请输入分类名称，不超过30个字"
              />
              <div v-else>
                <el-skeleton animated :rows="1" v-if="!detail.id" />
                {{ detail.name }}
              </div>
            </el-form-item>
            <el-form-item class="m-btn-bar" v-if="!isDetailMode">
              <el-button @click="cancel">取消</el-button>
              <el-button type="primary" @click="doOperate" :loading="onOperating">保存</el-button>
              <el-checkbox v-model="createAnother" class="f-ml10">创建另外一个</el-checkbox>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
    </div>
  </el-drawer>
</template>

<script lang="ts">
  import { Component, Emit, Prop, Vue } from 'vue-property-decorator'

  class Detail {
    parentName: string
    id: string
    name: string
    parentId: string
  }

  export enum Mode {
    detail = 'detail',
    modify = 'modify',
    create = 'create'
  }

  export enum CategoryComponent {
    course = 'biz-course-category',
    courseware = 'biz-courseware-category'
  }

  @Component
  export default class Operation extends Vue {
    @Prop({
      type: String,
      default: CategoryComponent.courseware
    })
    switchComponent: CategoryComponent
    createAnother = false
    @Prop({
      type: String,
      default: ''
    })
    title: string

    @Prop({
      type: Function,
      default() {
        return async (id: string) => {
          return Promise.resolve(id)
        }
      }
    })
    loadDetail: (id: string) => Promise<Detail>

    parentId: Array<string> = new Array<string>()

    //新建、修改、详情弹窗显隐
    show = false
    detail: Detail = new Detail()
    onOperating = false
    // 默认展示详情
    mode = Mode.detail

    get isDetailMode() {
      return this.mode === Mode.detail
    }

    open(mode: Mode, id?: string) {
      this.mode = mode
      this.show = true
      this.doInit(id)
    }

    async doInit(id: string) {
      if (this.mode !== Mode.create) {
        this.detail = await this.loadDetail(id)
        this.parentId = [this.detail.parentId]
      } else {
        this.detail = new Detail()
        this.parentId = []
      }
    }

    close() {
      this.show = false
    }

    @Emit('cancel')
    cancel() {
      this.close()
    }

    doOperate() {
      switch (this.mode) {
        case Mode.create:
          this.doCreate()
          break
        case Mode.modify:
          this.doModify()
          break
      }
    }

    doCreate() {
      this.onOperating = true
      this.$emit('confirm')
      this.onOperating = false
    }

    doModify() {
      this.onOperating = true
      this.$emit('confirm')
      this.onOperating = false
    }
  }
</script>
