import CreateCoursewareCategoryDto from '@api/service/management/resource/courseware-category/mutation/dto/CreateCoursewareCategoryDto'
import { ResponseStatus } from '@hbfe/common'
import MsCourseResourceV1 from '@api/ms-gateway/ms-course-resource-v1'
import CoursewareCategoryDetail from '@api/service/management/resource/courseware-category/query/vo/CoursewareCategoryDetail'

class UpdateCoursewareCategoryDto extends CreateCoursewareCategoryDto {
  id: string

  async save(): Promise<ResponseStatus> {
    const { status } = await MsCourseResourceV1.updateCoursewareCategory(this)
    return new ResponseStatus(status.code, status.getMessage())
  }

  from(coursewareCategoryDetail: CoursewareCategoryDetail) {
    this.id = coursewareCategoryDetail.id
    this.name = coursewareCategoryDetail.name
    this.parentId = coursewareCategoryDetail.parentId
    this.sort = coursewareCategoryDetail.sort
  }
}

export default UpdateCoursewareCategoryDto
