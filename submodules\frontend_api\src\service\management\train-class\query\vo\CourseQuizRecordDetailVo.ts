import { CourseQuizAnswerPaperResponse } from '@api/ms-gateway/ms-exam-query-front-gateway-ExamQueryBackStage'

/**
 * @description 课程测验记录详情
 */
class CourseQuizRecordDetailVo extends CourseQuizAnswerPaperResponse {
  /**
   * 测验提交时间
   */
  quizSubmitTime = ''

  /**
   * 测验成绩 -1表示不是为分数评定方式
   */
  quizScore = 0

  /**
   * 测验是否合格
   */
  isQuizQualified = false

  static from(response: CourseQuizAnswerPaperResponse): CourseQuizRecordDetailVo {
    const detail = new CourseQuizRecordDetailVo()
    detail.quizSubmitTime = response.answerPaperTimeInfo?.handingTime ?? null
    detail.quizScore = response.answerPaperMarkInfo?.score ?? null
    const qualifiedScore = response.answerPaperBasicInfo?.qualifiedScore ?? null
    const score = response.answerPaperMarkInfo?.score ?? null
    if (qualifiedScore && score && score >= qualifiedScore) detail.isQuizQualified = true
    return detail
  }
}

export default CourseQuizRecordDetailVo
