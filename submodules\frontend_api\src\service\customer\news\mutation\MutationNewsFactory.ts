/*
 * @Description: 资讯业务工厂
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-01-24 19:06:28
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-02-17 20:09:42
 */
import MutationRecordNews from '@api/service/customer/news/mutation/MutationRecordNews'
class MutationNewsFactory {
  /**
   * 记录观看次数
   * @returns MutationRecordNews
   */
  doMutationRecordNews() {
    return new MutationRecordNews()
  }
}
export default MutationNewsFactory
