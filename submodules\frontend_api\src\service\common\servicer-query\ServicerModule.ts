import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { ResponseStatus } from '@api/Response'

import servicerGateway, { ServicerDto } from '@api/gateway/PlatformServicer'

export interface IServicerState {
  /**
   * 服务商信息列表
   */
  servicerList: Array<ServicerDto>
}

@Module({ namespaced: true, dynamic: true, name: 'CommonServicerModule', store })
class ServicerModule extends VuexModule implements IServicerState {
  servicerList = new Array<ServicerDto>()

  /**
   * 服务商Id列表获取服务商信息
   */
  @Action
  async servicerListByIds(idList: Array<string>): Promise<ResponseStatus> {
    const response = await servicerGateway.list(idList)
    if (response.status.isSuccess()) {
      this.SET_SERVICER_LIST(response.data)
    }
    return response.status
  }

  @Mutation
  private SET_SERVICER_LIST(list: Array<ServicerDto>) {
    this.servicerList = list
  }
}

export default getModule(ServicerModule)
