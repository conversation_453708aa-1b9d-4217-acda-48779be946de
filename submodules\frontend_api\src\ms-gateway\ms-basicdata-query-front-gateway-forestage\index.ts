import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-basicdata-query-front-gateway-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-basicdata-query-front-gateway-forestage'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举
export enum SortTypeEnum {
  ASC = 'ASC',
  DESC = 'DESC'
}
export enum BusinessDictionarySortFieldEnum {
  createdTime = 'createdTime',
  sort = 'sort'
}

// 类

export class Page {
  pageNo?: number
  pageSize?: number
}

/**
 * @Description 获取单个业务数据字典信息的查询
<AUTHOR>
@Date 2022/9/27 9:33
@Version 1.0
 */
export class BusinessDataDictionaryCodeRequest {
  /**
   * 字典编码
   */
  code?: number
  /**
   * 业务数据字典类型（必填项）
DEGREE(&quot;学位&quot;, &quot;DEGREE&quot;),
EDUCATION_BACKGROUND(&quot;学历&quot;, &quot;EDUCATION_BACKGROUND&quot;),
ENTERPRISE_ECONOMIC_TYPE(&quot;企业经济类型&quot;, &quot;ENTERPRISE_ECONOMIC_TYPE&quot;),
ENTERPRISE_TYPE(&quot;企业类型&quot;, &quot;ENTERPRISE_TYPE&quot;),
EXECUTIVES_UNIT_TYPE(&quot;主管单位类型&quot;, &quot;EXECUTIVES_UNIT_TYPE&quot;),
GENDER(&quot;性别&quot;, &quot;GENDER&quot;),
HOUSEHOLD_REGISTRATION_TYPE(&quot;户口性质&quot;, &quot;HOUSEHOLD_REGISTRATION_TYPE&quot;),
ID_CARD_TYPE(&quot;证件类型&quot;, &quot;ID_CARD_TYPE&quot;),
INDUSTRY_EXECUTIVES_TYPE(&quot;行业主管类型&quot;, &quot;INDUSTRY_EXECUTIVES_TYPE&quot;),
INDUSTRY_TYPE(&quot;行业类型&quot;, &quot;INDUSTRY_TYPE&quot;),
NATIONALITY(&quot;民族&quot;, &quot;NATIONALITY&quot;),
PERSON_TYPE(&quot;人员类型&quot;, &quot;PERSON_TYPE&quot;),
PERSON_TYPE_GROUP(&quot;人员类型分组&quot;, &quot;PERSON_TYPE_GROUP&quot;),
POLITICS_STATUS(&quot;政治面貌&quot;, &quot;POLITICS_STATUS&quot;),
USER_REGISTER_TYPE(&quot;注册方式&quot;, &quot;USER_REGISTER_TYPE&quot;),
USER_SOURCE_TYPE(&quot;注册来源&quot;, &quot;USER_SOURCE_TYPE&quot;);
@see BusinessDataDictionaryTypeEnum
   */
  businessDataDictionaryType?: string
}

/**
 * 业务数据字典查询
 */
export class BusinessDataDictionaryRequest {
  /**
   * 字典编码
   */
  code?: number
  /**
   * 字典编码扩展
   */
  codeExt?: string
  /**
   * 业务数据字典id集合
   */
  idList?: Array<string>
  /**
   * 父级业务数据字典id
   */
  parentId?: string
  /**
   * 业务数据字典类型（必填项）
   */
  businessDataDictionaryType?: string
  /**
   * 业务数据字典业务配置id
   */
  businessId?: string
  /**
   * 字典名称
   */
  name?: string
  /**
   * 字典名称 匹配方式
1:模糊查询   2:后缀查询   3:前缀查询 0:精确匹配  tips:未传参默认为精确匹配
(1:*name*   2:*name     3:name*)
   */
  nameMatchType?: number
  /**
   * 是否可用（0：禁用 1：启用）
   */
  available?: number
  /**
   * 排序规则
   */
  sortList?: Array<BusinessDictionarySortKParam>
}

export class BusinessDictionarySortKParam {
  /**
   * 排序字段
   */
  sortField?: BusinessDictionarySortFieldEnum
  /**
   * 排序类型
   */
  sortType?: SortTypeEnum
}

/**
 * 功能描述：业务数据字典信息
@Author： wtl
@Date： 2022年8月12日 17:25:23
 */
export class BusinessDataDictionaryResponse {
  /**
   * 字典id
   */
  id: string
  /**
   * 字典类型
   */
  type: string
  /**
   * 父级字典id
   */
  parentId: string
  /**
   * 字典编码
   */
  code: number
  /**
   * 字典编码扩展
   */
  codeExt: string
  /**
   * 字典名称
   */
  name: string
  /**
   * 是否可用（0：禁用 1：启用）
   */
  available: number
  /**
   * 排序
   */
  sort: number
  /**
   * 创建时间
   */
  createTime: string
}

export class BusinessDataDictionaryResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<BusinessDataDictionaryResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 功能描述：项目级-根据code和业务数据字典类型查询单个业务数据字典信息接口-明细接口
   * 描述：根据code和业务数据字典类型查询当前子项目下的业务数据字典信息
   * @param request :业务数据字典查询条件
   * @return : com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse
   * @date : 2022年8月12日 15:56:50
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getBusinessDataDictionaryInSubProject(
    request: BusinessDataDictionaryCodeRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getBusinessDataDictionaryInSubProject,
    operation?: string
  ): Promise<Response<BusinessDataDictionaryResponse>> {
    return commonRequestApi<BusinessDataDictionaryResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-根据id集合查询字典信息-列表接口
   * 描述：查询指定id的字典列表，默认按排序字段升序排
   * @param idList :字典id集合
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
   * @date : 2024年1月24日 09:35:49
   * @param query 查询 graphql 语法文档
   * @param idList 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessDataDictionaryByIdInSubProject(
    idList: Array<string>,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessDataDictionaryByIdInSubProject,
    operation?: string
  ): Promise<Response<Array<BusinessDataDictionaryResponse>>> {
    return commonRequestApi<Array<BusinessDataDictionaryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { idList },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询指定业务数据字典类型的字典信息-列表接口
   * 描述：查询指定业务数据字典类型的字典列表，默认按排序字段升序排
   * @param request :业务数据字典查询条件
   * @return : java.util.List<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
   * @date : 2022年8月12日 17:28:02
   * @param query 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async listBusinessDataDictionaryInSubProject(
    request: BusinessDataDictionaryRequest,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.listBusinessDataDictionaryInSubProject,
    operation?: string
  ): Promise<Response<Array<BusinessDataDictionaryResponse>>> {
    return commonRequestApi<Array<BusinessDataDictionaryResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 功能描述：项目级-查询指定业务数据字典类型的字典信息-分页列表接口
   * 描述：查询指定业务数据字典类型的字典分页列表，默认按排序字段升序排
   * @param page    :分页对象
   * @param request :业务数据字典查询条件
   * @return : com.fjhb.commons.dao.page.Page<com.fjhb.ms.basicdata.query.front.gateway.btglxt.graphql.response.dictionary.BusinessDataDictionaryResponse>
   * @date : 2022年8月12日 17:28:02
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async pageBusinessDataDictionaryInSubProject(
    params: { page?: Page; request?: BusinessDataDictionaryRequest },
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.pageBusinessDataDictionaryInSubProject,
    operation?: string
  ): Promise<Response<BusinessDataDictionaryResponsePage>> {
    return commonRequestApi<BusinessDataDictionaryResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }
}

export default new DataGateway()
