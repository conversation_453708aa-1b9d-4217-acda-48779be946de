import { OfflineInvoiceOperationResponse } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
import { OperationLogTypeEnum } from '@api/service/management/trade/batch/invoice/enum/OperationLogTypeEnum'
import QueryPortal from '@api/service/management/online-school-config/portal/query/QueryPortal'

export default class OperationLogItem extends OfflineInvoiceOperationResponse {
  /**
   * 综合描述
   */
  operationStr: string = undefined

  /**
   * 模型转化
   * @param dto
   */
  static from(dto: OfflineInvoiceOperationResponse) {
    const vo = new OperationLogItem()

    vo.operatorUserId = dto?.operatorUserId
    vo.operationType = dto?.operationType
    vo.operateTime = dto?.operateTime
    vo.operationMessage = dto?.operationMessage
    vo.operatorUserName = dto?.operatorUserName

    switch (dto?.operationType) {
      case OperationLogTypeEnum.ISSUE:
        vo.operationStr = `【${dto.operatorUserName}】在 【${dto?.operateTime}】 开票了【培训发票】。`
        break
      case OperationLogTypeEnum.CANCLE_FREE:
        vo.operationStr = `【${QueryPortal?.webPortalInfo?.title}】在 【${dto?.operateTime}】 时取消了发票冻结。`
        break
      case OperationLogTypeEnum.FREE:
        vo.operationStr = `【${QueryPortal?.webPortalInfo?.title}】在 【${dto?.operateTime}】 时冻结了发票。`
        break
      case OperationLogTypeEnum.UPDATE:
        vo.operationStr = `【${dto?.operatorUserName}】在【${dto?.operateTime}】修改了【培训发票】`
        break
      case OperationLogTypeEnum.NEGATE:
        vo.operationStr = `【${dto?.operatorUserName}】在 【${dto?.operateTime}】作废了【培训发票】。`
        break
    }

    return vo
  }
}
