import MsOrder from '@api/ms-gateway/ms-order-v1'
import TradeConfiguration from '@api/ms-gateway/ms-trade-configuration-v1'
import { ResponseStatus } from '@hbfe/common'
import BatchInvoiceParam from './vo/BatchInvoiceParam'

/**
 * 【集体管理员】集体发票-业务工厂
 */
class MutationBatchInvoice {
  /**
   * 申请开票
   * @param batchOrderNo 批次号
   * @returns
   */
  async applyInoice(batchOrderNo: string, batchInvoiceParam: BatchInvoiceParam) {
    const invoiceInfo = new BatchInvoiceParam()
    Object.assign(invoiceInfo, batchInvoiceParam)
    if (
      batchInvoiceParam.deliveryAddress.address &&
      batchInvoiceParam.deliveryAddress.consignee &&
      batchInvoiceParam.deliveryAddress.phone &&
      batchInvoiceParam.deliveryAddress.region
    ) {
      invoiceInfo.deliveryAddress = batchInvoiceParam.deliveryAddress
    } else {
      invoiceInfo.deliveryAddress = undefined
    }
    if (batchInvoiceParam.takePoint.pickupLocation && batchInvoiceParam.takePoint.pickupTime) {
      invoiceInfo.takePoint = batchInvoiceParam.takePoint
    } else {
      invoiceInfo.takePoint = undefined
    }
    const { status } = await MsOrder.batchApplyInvoice({
      batchOrderNo,
      invoiceInfo
    })
    return status
  }
}

export default MutationBatchInvoice
