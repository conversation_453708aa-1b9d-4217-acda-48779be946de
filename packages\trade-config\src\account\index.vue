<route-meta>
{
"isMenu": true,
"title": "收款账户管理",
"sort": 1,
"icon": "icon_guanli"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="doQueryPage">
    <div class="f-p15">
      <div class="f-mb15">
        <template
          v-if="$hasPermission('create')"
          desc="新建收款账户"
          actions="@hbfe/jxjy-admin-tradeConfig/src/account/create.vue"
        >
          <el-button type="primary" icon="el-icon-plus" @click="addAccount">新建收款账号</el-button>
        </template>
      </div>
      <el-card shadow="never" class="m-card f-mb15">
        <!--表格-->
        <el-table stripe ref="tableRef" :data="tableData" max-height="500px" class="m-table" v-loading="loading">
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="收款账号别名" min-width="200" fixed="left">
            <template slot-scope="scope">{{ scope.row.accountName }}</template>
          </el-table-column>
          <el-table-column label="支付方式" min-width="150" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.accountType === 1">
                <el-tag type="primary" size="small">线上</el-tag>
              </div>
              <div v-else>
                <el-tag type="warning" size="small">线下</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="退款方式" min-width="150" align="center">
            <template slot-scope="scope">
              <div v-if="scope.row.returnType === 1">
                <el-tag type="primary" size="small">线上退款</el-tag>
              </div>
              <div v-else>
                <el-tag type="warning" size="small">线下退款</el-tag>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="支付帐号类型" min-width="150">
            <template slot-scope="scope">{{ paymentChannelTypeTitle(scope.row.paymentChannelId) }}</template>
          </el-table-column>
          <el-table-column label="商户号" min-width="200">
            <template slot-scope="scope">{{ scope.row.accountNo }}</template>
          </el-table-column>
          <el-table-column label="帐号状态" min-width="150">
            <template slot-scope="scope">
              <div v-if="scope.row.status === 1">
                <el-badge is-dot type="success" class="badge-status">启用</el-badge>
              </div>
              <div v-if="scope.row.status === 0">
                <el-badge is-dot type="info" class="badge-status">停用</el-badge>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="190" align="center" fixed="right">
            <template slot-scope="scope">
              <template v-if="$hasPermission('disable')" desc="停用" actions="@WarningDialog">
                <el-button
                  type="text"
                  size="mini"
                  v-if="scope.row.status === 1"
                  :disabled="scope.row.paymentChannelId == 'THIRD_PARTY_PLATFORM_PAY'"
                  @click="blockUpClcik(1, scope.row)"
                  >停用</el-button
                >
              </template>
              <template v-if="$hasPermission('enable')" desc="启用" actions="@WarningDialog">
                <el-button type="text" size="mini" v-if="scope.row.status === 0" @click="blockUpClcik(2, scope.row)"
                  >启用</el-button
                >
              </template>
              <template
                v-if="$hasPermission('collectionDetail')"
                desc="账户-详细"
                actions="@hbfe/jxjy-admin-tradeConfig/src/account/detail.vue"
              >
                <el-button
                  type="text"
                  size="mini"
                  :disabled="scope.row.paymentChannelId == 'THIRD_PARTY_PLATFORM_PAY'"
                  @click="detail(scope.row)"
                  >详细</el-button
                >
              </template>
              <template
                v-if="$hasPermission('collectionEdit')"
                desc="账户-修改"
                actions="@hbfe/jxjy-admin-tradeConfig/src/account/modify.vue"
              >
                <el-button
                  type="text"
                  size="mini"
                  :disabled="scope.row.paymentChannelId == 'THIRD_PARTY_PLATFORM_PAY'"
                  @click="editAccount(scope.row)"
                  >修改</el-button
                >
              </template>
              <template v-if="$hasPermission('remove')" desc="删除" actions="@WarningDialog">
                <el-button
                  type="text"
                  size="mini"
                  :disabled="scope.row.paymentChannelId == 'THIRD_PARTY_PLATFORM_PAY'"
                  @click="blockUpClcik(3, scope.row)"
                  >删除</el-button
                >
              </template>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"> </hb-pagination>
      </el-card>
    </div>
    <WarningDialog
      v-if="isDialog"
      @dialogClick="dialogClick"
      :confirmText="confirmText"
      @doQueryPage="doQueryPage"
      :content="content"
      :requestType="requestType"
      :itemDetail="itemDetail"
    ></WarningDialog>
  </el-main>
</template>

<script lang="ts">
  import { Component, Vue } from 'vue-property-decorator'
  import WarningDialog from '@hbfe/jxjy-admin-tradeConfig/src/account/components/dialog.vue'
  import { UiPage } from '@hbfe/common'
  import TradeInfoConfigModule from '@api/service/management/trade-info-config/TradeInfoConfigModule'
  import ReceiveAccountVo from '@api/service/management/trade-info-config/query/vo/ReceiveAccountVo'
  import { ElTable } from 'element-ui/types/table'

  @Component({
    components: { WarningDialog }
  })
  export default class extends Vue {
    isDialog = false
    content = ''
    // 区分弹窗确定事件
    requestType = 0
    page: UiPage
    getQueryReceiveAccount = TradeInfoConfigModule.queryTradeInfoConfigFactory.getQueryReceiveAccount()
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    tableData: Array<ReceiveAccountVo> = []
    itemDetail = new ReceiveAccountVo()
    loading = false
    confirmText = '确定'
    async doQueryPage() {
      this.loading = true
      const res = await this.getQueryReceiveAccount.queryPage(this.page)
      ;(this.$refs['tableRef'] as ElTable).doLayout()
      console.log(res, 'res')
      this.tableData = res
      this.loading = false
    }

    async activated() {
      await this.doQueryPage()
    }

    editAccount(item: ReceiveAccountVo) {
      if (item.status == 1) return this.$message.warning('修改收款帐号，需将帐号暂停开放使用，才可修改！')
      if (item.paymentChannelId === 'NO_PAYMENT') return this.$message.warning('导入开通账号不可修改')
      this.$router.push('/basic-data/trade/account/modify/' + item.id)
    }
    addAccount() {
      this.$router.push('/basic-data/trade/account/create')
    }
    detail(item: ReceiveAccountVo) {
      if (item.paymentChannelId === 'NO_PAYMENT') return this.$message.warning('导入开通账号无法查看详情')
      this.$router.push('/basic-data/trade/account/detail/' + item.id)
    }
    // 弹窗显隐
    dialogClick() {
      this.isDialog = !this.isDialog
    }
    // 停用/启用事件
    blockUpClcik(requestType: number, item: ReceiveAccountVo) {
      this.itemDetail = item
      if (requestType == 1) {
        this.confirmText = '确定停用'
        this.content = '确定停用该收款帐号？'
      }
      if (requestType == 2) {
        this.confirmText = '确定启用'
        this.content = '确定启用该收款帐号？'
      }
      if (requestType == 3) {
        if (item.paymentChannelId === 'NO_PAYMENT') {
          return this.$message.warning('导入开通账号不能删除')
        }
        if (item.status != 0) {
          return this.$message.warning('帐号未停用不能删除')
        }
        this.content = '删除后数据不可恢复，确定要删除？'
        this.confirmText = '确定'
      }
      this.requestType = requestType
      this.dialogClick()
    }
    // 支付账号类型
    paymentChannelTypeTitle(paymentChannelId: string) {
      if (paymentChannelId.indexOf('WXPAY') != -1) {
        return '微信支付'
      }
      if (paymentChannelId.indexOf('ALIPAY') != -1) {
        return '支付宝支付'
      }
      if (paymentChannelId.indexOf('CIB_PAY') != -1) {
        return '兴业银行聚合支付'
      }
      if (paymentChannelId.indexOf('CCB_PAY') != -1) {
        return '建设银行聚合支付'
      }
      if (paymentChannelId.indexOf('SWIFT_PASS_PAY') != -1) {
        return '兴业银行聚合支付（威富通）'
      }
      if (paymentChannelId.indexOf('NEW_LAND_PAY') != -1) {
        return '新大陆聚合支付'
      }
      return '银行支付'
    }
  }
</script>

<style scoped></style>
