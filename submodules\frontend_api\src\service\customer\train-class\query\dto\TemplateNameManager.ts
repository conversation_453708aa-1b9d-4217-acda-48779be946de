import PlaceholderConstant from '@api/service/common/scheme/model/schemeDto/common/PlaceholderConstant'

/**
 * 模板常量类
 * @description 历史遗留原因，后续不再维护，统一更新在PlaceholderConstant
 */
class TemplateNameManager extends PlaceholderConstant {
  // region properties

  static readonly schemeID = 'schemeId.UUID$00000001'
  static readonly chooseCourseLearningID = 'learningId.UUID$00000001'
  static readonly chooseCourseLearningAssessSettingID = 'assessId.UUID$00000001'
  static readonly chooseCourseLearningAssessSettingNAME = 'Course_Assess_001'
  // endregion
  // region methods
  // endregion
}
export default TemplateNameManager
