import { NewsNoticeCondition } from '@api/gateway/PlatformNewNotice'

/**
 * 资讯搜索条件
 * @author: eleven
 * @date: 2020/4/19
 */
export class InfoContentParam {
  /**
   * 资讯标题
   */
  title: string
  /**
   * 资讯分类
   */
  categoryId: string
  /**
   * 展示施教机构
   */
  associateUnitId: string
  /**
   * 是否弹窗公告
   */
  pop: boolean
  /**
   * 资讯状态
   * -1、不查
   * 0、草稿
   * 1、发布

   */
  status: number

  /**
   * 转成远端参数
   */
  toRemote() {
    const remote = new NewsNoticeCondition()
    remote.title = this.title
    remote.associateUnitId = this.associateUnitId
    remote.categoryId = this.categoryId
    if (this.pop === undefined) {
      remote.type = -1
    } else {
      remote.type = this.pop ? 2 : 1
    }
    if (this.status === undefined) {
      remote.status = -1
    } else {
      remote.status = this.status
    }
    return remote
  }
}
