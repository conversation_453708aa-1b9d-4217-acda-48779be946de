<!--
 * @Description: 描述
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2024-09-04 15:42:34
 * @LastEditors: chenweinian <EMAIL>
 * @LastEditTime: 2025-01-06 10:13:25
-->
<route-meta>
{
"isMenu": true,
"openWhenInit": true,
"closeAble": false,
"title": "首页",
"sort":1,
"icon": "icon-shouye"
}
</route-meta>
<template>
  <div>
    <template v-if="$hasPermission('home')" query actions="@DefaultHome" desc="首页">
      <default-home />
    </template>
    <template v-if="$hasPermission('Home')" query actions="@Welcome" desc="课件供应商首页">
      <welcome></welcome>
    </template>
    <template v-if="$hasPermission('specialHome')" actions="@SpecialSubjectHome" desc="专题首页">
      <special-subject-home />
    </template>
  </div>
</template>

<script lang="ts">
  import { Component, Vue as VueDecorator } from 'vue-property-decorator'
  import DefaultHome from '@hbfe/jxjy-admin-basicSchoolHome/src/__summary-components__/default-home.vue'
  import Welcome from '@hbfe/jxjy-admin-components/src/welcome.vue'
  import SpecialSubjectHome from '@hbfe/jxjy-admin-basicSchoolHome/src/__summary-components__/special-subject-home.vue'
  // import UnitDialog from '@/unit-share/network-school/home/<USER>/unit-dialog.vue'

  @Component({
    components: {
      DefaultHome,
      Welcome,
      SpecialSubjectHome
    }
  })
  export default class extends VueDecorator {}
</script>
