import AnnexInfoDto from '@api/service/management/implement/models/AnnexInfoDto'
import MockUtil from '@api/service/common/utils/MockUtil'
import PeriodConfig from '@api/service/management/implement/models/PeriodConfig'
import { Page } from '@hbfe/common'
import MsSchemeLearningQuery, {
  IssueStudyConfigResponse
} from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'

/**
 * 学习资料配置
 */
export default class LearningMaterialsConfig {
  /**
   * 方案id
   */
  private schemeId: string = undefined

  /**
   * 统一配置
   */
  allAnnexInfoConfig: Array<AnnexInfoDto> = new Array<AnnexInfoDto>()

  /**
   * 期别配置列表
   */
  periodInfoList: Array<PeriodConfig> = new Array<PeriodConfig>()

  /**
   * @param schemeId 方案id
   * @param schemeConfig 方案信息（如果没传方案信息，在获取配置时会根据方案id查方案信心）
   */
  constructor(schemeId?: string) {
    if (schemeId) {
      this.schemeId = schemeId
    }
  }

  /**
   * 获取学习资料配置
   */
  async getLearningMaterialsConfig() {
    this.allAnnexInfoConfig = Array(10)
      .fill('')
      .map(() => {
        const item = new AnnexInfoDto()
        MockUtil(item)
        return item
      })
  }

  /**
   * 分页查询方案下期别配置项
   */
  async pagePeriodLearningMaterialsConfig(page: Page) {
    const res = await MsSchemeLearningQuery.pageIssueStudyConfigInServicer({ page, schemeId: this.schemeId })
    if (res?.data?.currentPageData?.length) {
      this.periodInfoList = res.data.currentPageData.map((item: IssueStudyConfigResponse) => {
        return PeriodConfig.from(item)
      })
    } else {
      this.periodInfoList = new Array<PeriodConfig>()
    }

    page.totalSize = res?.data?.totalSize
    page.totalPageSize = res?.data?.totalPageSize

    return
  }

  /**
   * 保存学习资料设置
   */
  async saveLearningMaterialsConfig() {
    // todo
  }
}
