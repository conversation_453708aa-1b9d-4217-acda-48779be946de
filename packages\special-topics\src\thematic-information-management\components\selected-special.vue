<template>
  <div>
    <!--选择专题-->
    <el-drawer title="选择专题" :visible.sync="showDrawer" size="1200px" custom-class="m-drawer">
      <!-- 有专题数据 -->
      <template v-if="!isNoData">
        <div class="drawer-bd">
          <el-row :gutter="16" class="m-query f-mt10">
            <el-form :inline="true" label-width="auto">
              <el-col :span="8">
                <el-form-item label="专题名称">
                  <el-input
                    v-model="thematicManagementList.queryParam.subjectName"
                    clearable
                    placeholder="请输入专题名称"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="专题类型">
                  <el-select
                    v-model="thematicManagementList.queryParam.subjectType"
                    clearable
                    placeholder="请选择专题类型"
                  >
                    <el-option :value="[1]" label="地区"></el-option>
                    <el-option :value="[2]" label="行业"></el-option>
                    <el-option :value="[3]" label="单位"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item>
                  <el-button type="primary" @click="page.currentChange(1)">查询</el-button>
                  <el-button @click="reset">重置</el-button>
                </el-form-item>
              </el-col>
            </el-form>
          </el-row>
          <el-table
            stripe
            :data="thematicManagementList.list"
            max-height="500px"
            class="m-table"
            v-loading="loading"
            ref="tableRef"
          >
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="专题名称" min-width="250">
              <template slot-scope="scope">{{ scope.row.basicInfo.subjectName }}</template>
            </el-table-column>
            <el-table-column label="专题类型" min-width="250">
              <template slot-scope="scope">
                <div v-if="hasRegion(scope.row.basicInfo.subjectType)">
                  <el-tag type="warning" size="mini" class="f-mt5">地区</el-tag>{{ scope.row.basicInfo.suiteArea }}
                </div>
                <div v-if="hasIndustry(scope.row.basicInfo.subjectType)">
                  <el-tag type="warning" size="mini" class="f-mt5">行业</el-tag>{{ scope.row.basicInfo.suiteIndustry }}
                </div>
                <div v-if="hasUnit(scope.row.basicInfo.subjectType)">
                  <el-tag type="warning" size="mini" class="f-mt5">单位</el-tag>{{ scope.row.basicInfo.unitName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" min-width="100">
              <template slot-scope="scope">
                <el-badge is-dot type="success" class="badge-status" v-if="scope.row.enable">启用</el-badge>
                <el-badge is-dot type="danger" class="badge-status" v-else>停用</el-badge>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="mini"
                  v-if="selectedIds.includes(scope.row.topicID)"
                  @click="cancelSelectThematic(scope.row)"
                  >取消选择</el-button
                >
                <el-button type="text" size="mini" v-else @click="selectThematic(scope.row)">选择</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!--分页-->
          <hb-pagination :page="page" v-bind="page"> </hb-pagination>
          <!-- 已选专题 -->
          <div class="m-tit">
            <span class="tit-txt">已选择的专题（{{ selectedList.length }} 个）</span>
          </div>
          <el-table stripe :data="selectedList" max-height="500px" class="m-table" ref="selectedTableRef">
            <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
            <el-table-column label="专题名称" min-width="250">
              <template slot-scope="scope">{{ scope.row.basicInfo.subjectName }}</template>
            </el-table-column>
            <el-table-column label="专题类型" min-width="250">
              <template slot-scope="scope">
                <div v-if="hasRegion(scope.row.basicInfo.subjectType)">
                  <el-tag type="warning" size="mini" class="f-mt5">地区</el-tag>{{ scope.row.basicInfo.suiteArea }}
                </div>
                <div v-if="hasIndustry(scope.row.basicInfo.subjectType)">
                  <el-tag type="warning" size="mini" class="f-mt5">行业</el-tag>{{ scope.row.basicInfo.suiteIndustry }}
                </div>
                <div v-if="hasUnit(scope.row.basicInfo.subjectType)">
                  <el-tag type="warning" size="mini" class="f-mt5">单位</el-tag>{{ scope.row.basicInfo.unitName }}
                </div>
              </template>
            </el-table-column>
            <el-table-column label="状态" min-width="100">
              <template slot-scope="scope">
                <el-badge is-dot type="success" class="badge-status" v-if="scope.row.enable">启用</el-badge>
                <el-badge is-dot type="danger" class="badge-status" v-else>停用</el-badge>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="100" align="center" fixed="right">
              <template slot-scope="scope">
                <el-button
                  type="text"
                  size="mini"
                  v-if="selectedIds.includes(scope.row.topicID)"
                  @click="cancelSelectThematic(scope.row)"
                  >取消选择</el-button
                >
                <el-button type="text" size="mini" v-else @click="selectThematic(scope.row)">选择</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="m-btn-bar drawer-ft">
          <el-button @click="showDrawer = false">取消</el-button>
          <el-button type="primary" @click="confirmSelect">确定</el-button>
        </div>
      </template>
      <!-- 空数据 -->
      <div class="drawer-bd" v-if="isNoData">
        <div class="m-no-date f-mt50">
          <img class="img" src="@design/admin/assets/images/no-data-normal.png" alt="" />
          <div class="date-bd">
            <p class="f-f15 f-c9">暂无数据</p>
            <div class="f-mt20">
              <el-button type="primary" icon="el-icon-plus" @click="newThematic">新建专题</el-button>
            </div>
          </div>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { SpecialSubjectInfo } from '@api/ms-gateway/ms-news-v1/index'
  import { SubjectType } from '@api/service/management/thematic-management/enum/SubjectType'
  import ThematicManagementQueryParam from '@api/service/management/thematic-management/model/ThematicManagementQueryParam'
  import ThematicManagementItem from '@api/service/management/thematic-management/ThematicManagementItem'
  import ThematicManagementItemBase from '@api/service/management/thematic-management/ThematicManagementItemBase'
  import ThematicManagementList from '@api/service/management/thematic-management/ThematicManagementList'
  import { UiPage } from '@hbfe/common'
  import { ElTable } from 'element-ui/types/table'
  import { Component, Prop, Ref, Vue } from 'vue-property-decorator'
  @Component({})
  export default class extends Vue {
    /**
     * 表格ref
     */
    @Ref('tableRef')
    tableRef: ElTable
    /**
     * 已选专题表格ref
     */
    @Ref('selectedTableRef')
    selectedTableRef: ElTable
    /**
     * 专题id
     */
    @Prop({
      type: Array,
      default: () => new Array<string>()
    })
    topicId: Array<string>
    constructor() {
      super()
      this.page = new UiPage(this.queryThematic, this.queryThematic)
    }
    /**
     * 抽屉显隐
     */
    showDrawer = false
    /**
     * 是否无专题数据
     */
    isNoData = false
    /**
     * 专题列表loading
     */
    loading = false
    /**
     * 分页
     */
    page = new UiPage()
    /**
     * 专题基础配置实例化
     */
    thematicManagementItemBase = new ThematicManagementItemBase()
    /**
     * 已选专题列表
     */
    selectedList = new Array<ThematicManagementItem>()
    /**
     * 专题类型枚举
     */
    subjectType = SubjectType
    /**
     * 专题实例化
     */
    thematicManagementList = new ThematicManagementList()
    /**
     * 已选专题列表id
     */
    get selectedIds() {
      return this.selectedList?.map((item) => item?.topicID) || []
    }
    // 判断是否是地区
    get hasRegion() {
      return (val: any) => {
        return val.includes(SubjectType.region)
      }
    }

    // 判断是否是行业
    get hasIndustry() {
      return (val: any) => {
        return val.includes(SubjectType.industry)
      }
    }

    // 判断是否是单位
    get hasUnit() {
      return (val: any) => {
        return val.includes(SubjectType.unit)
      }
    }
    /**
     * 已选专题
     */
    get selectedItemList() {
      const selectedItemList = this.selectedList?.map((item) => {
        const selectedItem = new SpecialSubjectInfo()
        selectedItem.specialSubjectId = item?.topicID
        selectedItem.specialSubjectName = item?.basicInfo?.subjectName
        return selectedItem
      })
      return selectedItemList
    }
    /**
     * 打开抽屉
     */
    async openDrawer() {
      this.thematicManagementList.queryParam = new ThematicManagementQueryParam()
      this.showDrawer = true
      this.selectedList = []
      if (this.topicId.length) {
        const selectedItem = await this.thematicManagementList.getInfoMapByIds(this.topicId)
        this.topicId.forEach((item) => {
          this.selectedList.push(selectedItem.get(item))
        })
      }
      this.selectedList = this.selectedList.filter((item) => item !== undefined)
      this.queryThematic()
    }
    /**
     * 新建专题
     */
    async newThematic() {
      await this.thematicManagementItemBase.getBaseConfig()
      if (this.thematicManagementItemBase.isOpen) {
        this.showDrawer = false
        this.$router.push('/training/special-topics/add')
      } else {
        return this.$confirm('网校专题功能暂未开启，请先完成专题基础配置，再进行具体专题页的发布', '系统提醒', {
          confirmButtonText: '立即前往',
          showCancelButton: false
        }).then(() => {
          this.showDrawer = false
          this.$router.push('/training/special-topics/manage')
        })
      }
    }
    /**
     * 查询专题
     */
    async queryThematic() {
      this.loading = true
      try {
        if (!this.thematicManagementList.queryParam.subjectType.length) {
          this.thematicManagementList.queryParam.subjectType = new Array<SubjectType>()
        }
        await this.thematicManagementList.queryList(this.page)
        if (
          !this.thematicManagementList.list.length &&
          !this.thematicManagementList.queryParam.subjectName &&
          !this.thematicManagementList.queryParam.subjectType
        ) {
          this.isNoData = true
        } else {
          this.isNoData = false
        }
      } catch (e) {
        console.log(e)
      } finally {
        this.loading = false
        this.tableRef.doLayout()
      }
    }
    /**
     * 重置查询
     */
    async reset() {
      this.page.pageNo = 1
      this.thematicManagementList.queryParam = new ThematicManagementQueryParam()
      await this.queryThematic()
    }
    /**
     * 选择专题
     */
    selectThematic(row: ThematicManagementItem) {
      this.selectedList.push(row)
      this.selectedTableRef.doLayout()
    }
    /**
     * 取消选择专题
     */
    cancelSelectThematic(row: ThematicManagementItem) {
      this.selectedList.splice(
        this.selectedList.findIndex((item) => item.topicID == row.topicID),
        1
      )
      this.selectedTableRef.doLayout()
    }
    /**
     * 确认选择
     */
    confirmSelect() {
      this.showDrawer = false
      this.$emit('selectSpecialSubject', this.selectedItemList)
    }
  }
</script>
