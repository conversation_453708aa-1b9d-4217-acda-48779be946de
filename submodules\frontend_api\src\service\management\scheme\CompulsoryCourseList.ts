import { Page } from '@hbfe/common'
import QueryCourse from '@api/service/management/resource/course/query/QueryCourse'
import CourseListDetail from '@api/service/management/resource/course/query/vo/CourseListDetail'
import CompulsoryCourseDetail from '@api/service/management/scheme/models/CompulsoryCourseDetail'
import OutlineCompulsoryCourseInfo from '@api/service/management/scheme/models/OutlineCompulsoryCourseInfo'
import CoursePackageCompulsoryCourseInfo from '@api/service/management/scheme/models/CoursePackageCompulsoryCourseInfo'
import CompulsoryCourseStatistic from '@api/service/management/scheme/models/CompulsoryCourseStatistic'

/**
 * @description
 */
class CompulsoryCourseList {
  /**
   * 资源课程列表
   * @private
   */
  private _resourceCourseList: CompulsoryCourseDetail[] = []

  /**
   * 构造函数
   * @param coursePackageInfo 课程包必修信息
   * @param outlineInfo 课程大纲必修信息
   */
  constructor(coursePackageInfo: CoursePackageCompulsoryCourseInfo[], outlineInfo: OutlineCompulsoryCourseInfo[]) {
    coursePackageInfo?.forEach(coursePackageCourse => {
      coursePackageCourse.compulsoryCourseIdList?.forEach(courseId => {
        this._resourceCourseList.push({
          courseId,
          resourceType: 1,
          resourceId: coursePackageCourse.coursePackageId
        } as CompulsoryCourseDetail)
      })
    })
    outlineInfo?.forEach(outlineInfo => {
      outlineInfo.compulsoryCourseIdList?.forEach(courseId => {
        this._resourceCourseList.push({
          courseId,
          resourceType: 2,
          resourceId: outlineInfo.outlineId
        } as CompulsoryCourseDetail)
      })
    })
  }

  /**
   * 分页查询必学课程列表
   * @param page 分页参数
   */
  async pageQueryCourseList(page: Page): Promise<CompulsoryCourseDetail[]> {
    const result = [] as CompulsoryCourseDetail[]
    page.totalSize = this._resourceCourseList.length
    page.totalPageSize = Math.ceil(page.totalSize / page.pageSize)
    const targetList = result.slice((page.pageNo - 1) * page.pageSize, page.pageNo * page.pageSize)
    if (targetList.length) {
      const summary = targetList.reduce((prev, cur) => {
        const target = prev.find(el => el.id === cur.resourceId)
        if (target) {
          if (!target.list.includes(cur.courseId)) target.list.push(cur.courseId)
        } else {
          prev.push({ id: cur.resourceId, resourceType: cur.resourceType, list: [cur.courseId] })
        }
        return prev
      }, [] as { id: string; resourceType: number; list: string[] }[])
      const coursePackageItems = summary.filter(el => el.resourceType === 1)
      const outlineItems = summary.filter(el => el.resourceType === 2)
      const queryCourse = new QueryCourse()
      // 批量查询课程包下课程
      if (coursePackageItems.length) {
        // // 课程id集合
        // const coursePackageCourseIds = coursePackageList.map(el => el.courseId)
        // // 课程包集合
        // const coursePackageIds = coursePackageList.map(el => el.resourceId)
        // const coursePackageCourseList: CourseListDetail[] = await queryCourse.queryCourseByIdList(
        //   coursePackageCourseIds
        // )
        // targetList.forEach(item => {
        //   const coursePackageCourse = coursePackageCourseList?.find(el => el.id === item.courseId)
        //   if (coursePackageCourse) {
        //     item.coursePhysicsPeriod = coursePackageCourse?.period
        //     item.courseName = coursePackageCourse?.name
        //   }
        // })
      }
      // 批量查询课程大纲下课程
      if (outlineItems.length) {
        //
      }
    }

    return result
  }

  /**
   * 查询必学课程统计信息
   */
  async queryCourseStatistic(): Promise<CompulsoryCourseStatistic[]> {
    // TODO LWF
    const result = [] as CompulsoryCourseStatistic[]
    const packages = this._resourceCourseList?.filter(el => el.resourceType === 1)
    const outlines = this._resourceCourseList?.filter(el => el.resourceType === 2)
    if (packages.length) {
      // 课程包
    }
    if (outlines.length) {
      // 课程大纲
    }
    return result
  }
}

export default CompulsoryCourseList
