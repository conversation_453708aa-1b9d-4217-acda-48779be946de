import LearningTypeBase from '@api/service/common/scheme/model/LearningTypeBase'
import Classification from '@api/service/common/scheme/model/Classification'

/**
 * @description 兴趣课学习方式
 */
class InterestCourseLearningType extends LearningTypeBase {
  /**
   * 分类+课程包id
   * @description 其中第一层是顶级分类，如果第一级无children则代表无分类
   */
  classification = new Classification()
}

export default InterestCourseLearningType
