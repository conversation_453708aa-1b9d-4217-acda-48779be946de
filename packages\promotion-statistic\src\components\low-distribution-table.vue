<template>
  <el-table ref="tableRef" :data="tableData" border class="m-table f-mt10">
    <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
    <el-table-column label="分销商" min-width="180" class-name="tree-cell" align="center">
      <template slot-scope="scope">
        {{ scope.row.distributorName }}
      </template>
    </el-table-column>
    <el-table-column label="开通" min-width="90" align="right">
      <template slot-scope="{ row }">{{ row.total.open }}</template>
    </el-table-column>
    <el-table-column label="退班" min-width="90" align="right">
      <template slot-scope="{ row }">{{ row.total.return }}</template>
    </el-table-column>
    <el-table-column label="净开通" min-width="90" align="right">
      <template slot-scope="{ row }">{{ row.total.netOpen }}</template>
    </el-table-column>
    <el-table-column label="分销总额" min-width="90" align="right">
      <template slot-scope="{ row }">{{ row.total.netAmount }}</template>
    </el-table-column>
  </el-table>
</template>
<script lang="ts">
  import { Component, Ref, Vue, Prop, Watch } from 'vue-property-decorator'
  // import ChildrenDistributorPromotionItem from '@api/service/management/statisticalReport/DistributorPromotionStatistics/model/ChildrenDistributorPromotionItem'

  @Component
  export default class extends Vue {
    @Prop({
      type: Array,
      default: () => new Array<any>()
      // default: () => new Array<ChildrenDistributorPromotionItem>()
    })
    tableData: Array<any>
    /**
     * 监听事件
     * @param val
     */
    @Watch('tableData', {
      immediate: true
    })
    valueChange(val: string) {
      if (val && val.length) {
        ;(this.$refs['tableRef'] as any)?.doLayout()
      }
    }
    created() {
      console.log(this.tableData, 'tableData')
    }
  }
</script>
