/*
 * @Description: 更新专票
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-22 09:30:44
 * @LastEditors: Do not edit
 * @LastEditTime: 2022-05-06 10:21:03
 */

import {
  DeliveryAddress,
  TakePoint,
  UpdateOfflineSpecialPaperInvoiceRequest
} from '@api/ms-gateway/ms-offlineinvoice-v1'
import { DeliveryWayEnum } from '../../enum/DeliveryInvoiceEnum'
import OffLinePageInvoiceResponseVo from '../../query/vo/OffLinePageInvoiceResponseVo'
export default class OffLineInvoiceUpdateVo extends OffLinePageInvoiceResponseVo {
  static to(item: OffLinePageInvoiceResponseVo) {
    const updateOfflineInvoiceRequest = new UpdateOfflineSpecialPaperInvoiceRequest()
    updateOfflineInvoiceRequest.offlineInvoiceId = item.invoiceId
    updateOfflineInvoiceRequest.invoiceType = null
    updateOfflineInvoiceRequest.invoiceCategory = item.invoiceCategory
    updateOfflineInvoiceRequest.title = item.title
    updateOfflineInvoiceRequest.taxpayerNo = item.taxpayerNo
    updateOfflineInvoiceRequest.address = item.address
    updateOfflineInvoiceRequest.phone = item.rePhone
    updateOfflineInvoiceRequest.bankName = item.bankName
    updateOfflineInvoiceRequest.account = item.account
    updateOfflineInvoiceRequest.businessLicensePath = item.businessLicenseUrl
    updateOfflineInvoiceRequest.accountOpeningLicensePath = item.permitUrl
    updateOfflineInvoiceRequest.shippingMethod = item.shippingMethod
    updateOfflineInvoiceRequest.remark = item.remark
    updateOfflineInvoiceRequest.invoiceVerifyStrategy = item.invoiceVerifyStrategy
    if (item.shippingMethod === DeliveryWayEnum.SELFFETCHED) {
      // 自取
      updateOfflineInvoiceRequest.takePoint = new TakePoint()
      updateOfflineInvoiceRequest.takePoint.pickupLocation = item.takePointPickupLocation
      updateOfflineInvoiceRequest.takePoint.pickupTime = item.takePointPickupTime
      updateOfflineInvoiceRequest.takePoint.remark = item.takePointRemark
    } else if (item.shippingMethod === DeliveryWayEnum.COURIER) {
      // 快递
      updateOfflineInvoiceRequest.deliveryAddress = new DeliveryAddress()
      updateOfflineInvoiceRequest.deliveryAddress.address = item.deliveryAddress
      updateOfflineInvoiceRequest.deliveryAddress.consignee = item.consignee
      updateOfflineInvoiceRequest.deliveryAddress.phone = item.deliveryphone
      updateOfflineInvoiceRequest.deliveryAddress.region = item.deliveryRegion
    }

    return updateOfflineInvoiceRequest
  }
}
