import { Component, Vue, Prop } from 'vue-property-decorator'
import Authentication from '@api/service/common/authentication/Authentication'
import DistributionUnitInformation from '@api/service/management/user/query/manager/vo/DistributionUnitInformation'

@Component
export default class extends Vue {
  $authentication: Authentication
  // 单位列表
  unitModel: DistributionUnitInformation[] = []
  isShow = false

  // 选择单位
  async selectUnit(unit: DistributionUnitInformation) {
    await this.getUnitToken(unit)
  }

  // 申请（业务域）身份凭证切换
  async getUnitToken(unit: DistributionUnitInformation) {
    // 用户信息身份认证
    try {
      const res = await this.$authentication.selectUnit(unit)
      if (res?.data.code !== 200) {
        this.$message.warning('身份凭证切换出错！')
        this.$authentication.ssoLogout()
        return
      } else {
        localStorage.setItem('currentUnitId', unit.unitId)
        // localStorage.setItem('unitModelName', unit.name)
        this.$message.success('登录成功')
      }
    } catch (e) {
      return this.$message.warning('单位列表请求失败')
    }
    location.hash = '/fx/distribution/promotion-gateway/check'
    location.reload()
  }

  close() {
    this.isShow = false
  }
}
