import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-examevaluation-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-examevaluation-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * <AUTHOR> create 2021/6/21 9:17
 */
export class AnswerPaperQuestionAnswerManualEvaluatedRequest {
  /**
   * 答卷ID
   */
  id: string
  /**
   * 阅卷人ID
   */
  markUserId: string
  /**
   * 试题评定列表
   */
  questionAnswerEvaluatedList: Array<QuestionAnswerEvaluated>
}

export class QuestionAnswerEvaluated {
  /**
   * 试题id
   */
  questionId?: string
  /**
   * 试题类型
   */
  questionType: number
  /**
   * 评定结果
@see QuestionEvaluateResults
   */
  evaluateResult: number
  /**
   * 得分，-1表示不为分数评定
   */
  score: number
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 人工阅卷
   * @param request
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async manualEvaluate(
    request: AnswerPaperQuestionAnswerManualEvaluatedRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.manualEvaluate,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
