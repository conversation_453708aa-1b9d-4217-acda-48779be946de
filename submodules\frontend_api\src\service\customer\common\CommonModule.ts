import YearSku from './models/YearSku'
import { TeachUnit } from './models/TeachUnit'
import platformTradeGateway from '@api/gateway/PlatformTrade'
// import { TeachUnitPageDTO } from '@api/gateway/PlatformBasicData'
import { ResponseStatus } from '../../../Response'
import { Action, getModule, Module, Mutation, VuexModule } from 'vuex-module-decorators'
import store from '@/store'
import { Role, RoleType, Secure } from '@api/Secure'

interface CommonState {
  yearList: Array<YearSku>
  unitList: Array<TeachUnit>
  inited: boolean
}

@Module({ namespaced: true, dynamic: true, name: 'CustomerCommonModule', store })
class CommonModule extends VuexModule implements CommonState {
  yearList = new Array<YearSku>()
  unitList = new Array<TeachUnit>()
  inited = false

  // 初始化通用的数据状态
  @Action
  async init(): Promise<ResponseStatus> {
    // 2021/10/27 调用已关闭接口 FJZYPXLM-2961
    // if (this.inited) {
    //   return new ResponseStatus(200)
    // }
    // let status = await this.getUnitList()
    // status = await this.getYearList()
    // if (status.isSuccess()) {
    //   this.setInited(true)
    // }
    return new ResponseStatus(200)
  }

  @Mutation
  private setInited(inited: boolean) {
    this.inited = inited
  }

  // 获取培训机构列表
  @Action
  @Role([RoleType.user])
  async getUnitList(): Promise<ResponseStatus> {
    // const response = await platformBasicDataGateway.findTeachUnitPage({
    //   page: {
    //     pageNo: 1,
    //     pageSize: -1
    //   },
    //   queryParam: {}
    // })
    // const data = response.data?.currentPageData
    // if (data) {
    //   this.setUnitList(data)
    // }
    // return response.status
    //todo 弃用该方法，弃用整个状态层，请改用机构服务商接口
    return new ResponseStatus(500)
  }

  @Mutation
  private setUnitList(data: any) {
    //todo 弃用该方法，弃用整个状态层，请改用机构服务商接口
    // const unitList = data.map(dto => TeachUnit.from(dto))
    // this.unitList = unitList
  }

  // 获取年度列表
  @Action
  @Role([RoleType.user])
  async getYearList(): Promise<ResponseStatus> {
    // const response = await platformTradeGateway.listYear()
    // const yearList = response.data?.map(dto => YearSku.from(dto))
    // this.setYearList(yearList)
    // return response.status
    // 2021/10/27 调用已关闭接口 FJZYPXLM-2961
    return new ResponseStatus(200)
  }

  @Mutation
  private setYearList(yearList: Array<YearSku>) {
    this.yearList = yearList
  }

  get getUnitById() {
    return (id: string): TeachUnit | undefined => {
      return this.unitList.find(unit => unit.id === id)
    }
  }

  get getYearSkuById() {
    return (id: string): YearSku | undefined => {
      return this.yearList.find(year => year.id === id)
    }
  }
}

export default getModule(CommonModule)
