import { MetaProperty } from '@api/ms-gateway/ms-collectivesign-v1'
import { ImportOpenQueryParamRequest, ImportOpenQueryRequest } from '@api/ms-gateway/ms-importopen-v1'

export default class PageImportResultParam {
  /**
   * 姓名
   */
  name?: string
  /**
   * 证件号
   */
  idCard?: string
  /**
   * 手机号
   */
  phone?: string
  /**
   * 培训方案名称
   */
  trainingSchemeName?: string
  /**
 * 期别名称
 */
  issueName?: string
  /**
   * 导入任务
   */
  importTask?: string
  /**
   * 开通状态 0 已开通 1 开通中 2 开通失败
   */
  // processState?: number
  /**
   * 开始时间
   */
  startTime?: string
  /**
   * 结束时间
   */
  endTime?: string
  /**
   * 智能选课/学习状态 1待处理、2进行中、3处理失败、4处理成功
   */
  // learningStatus?: number
  /**
   * 失败原因
   */
  reason?: string
  /**
   * 主任务类型 默认是导入开通类型（ADMIN_NORMAL_IMPORT），如果是导入开通并学习，需要填入（ADMIN_NORMAL_IMPORT_AND_LEARNING）
   */
  taskCategoryList?: Array<string>
  /**
   * 订单状态  1-开通中 2-已开通 3-开通失败 默认null
   */
  orderState?: number
  /**
   * 学习状态
   * 流程还没到学习-null
   * 待处理-0
   * 进行中-1
   * 处理失败-2
   * 处理成功-3
   */
  learningState?: number
  to() {
    const importOpenQueryRequest = new ImportOpenQueryRequest()
    const metaProperty = new Array<MetaProperty>()
    if (this.name) {
      metaProperty.push({ key: 'userInfo_name', value: this.name })
    }
    if (this.idCard) {
      metaProperty.push({ key: 'userInfo_idCard', value: this.idCard })
    }
    if (this.phone) {
      metaProperty.push({ key: 'userInfo_phone', value: this.phone })
    }
    if (this.trainingSchemeName) {
      metaProperty.push({ key: 'signUp_schemeName', value: this.trainingSchemeName })
    }
    if (this.issueName) {
      metaProperty.push({ key: 'signUp_issueName', value: this.issueName })
    }
    importOpenQueryRequest.metaPropertyList = metaProperty
    const query = new ImportOpenQueryParamRequest()
    query.mainTaskId = this.importTask
    query.importEndTime = this.endTime
    query.importStartTime = this.startTime
    query.taskCategoryList = this.taskCategoryList
    if (this.orderState == 3) {
      // 开通失败
      query.batchParallelQueryDtoList = [
        {
          parallelQueryDtoList: [
            {
              subTaskProcessResult: 2,
              existProperties: [
                { key: 'orderState', value: false },
                { key: 'learningState', value: false }
              ]
            },
            { orderState: 3 }
          ]
        }
      ]
    } else if (this.orderState == 1) {
      // 开通中
      query.batchParallelQueryDtoList = [
        {
          parallelQueryDtoList: [{ orderState: 1 }]
        }
      ]
    } else if (this.orderState == 2) {
      // 已开通
      query.batchParallelQueryDtoList = [
        {
          parallelQueryDtoList: [
            {
              subTaskProcessResult: 1,
              existProperties: [
                { key: 'orderState', value: false },
                { key: 'learningState', value: false }
              ]
            },
            { orderState: 2 }
          ]
        }
      ]
    }
    query.learningState = this.learningState
    // switch (this.processState) {
    //   case 1:
    //     query.subTaskState = 2
    //     break
    //   case 0:
    //     query.subTaskProcessResult = 1
    //     break
    //   case 2:
    //     query.subTaskProcessResult = 2
    //     break
    // }
    importOpenQueryRequest.queryParam = query
    return importOpenQueryRequest
  }
}
