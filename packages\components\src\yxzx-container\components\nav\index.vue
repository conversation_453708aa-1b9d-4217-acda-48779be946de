<template>
  <div class="bare-nav-wrapper">
    <div class="bare-nav-left">
      <el-button type="text" @click="goLeft">
        <i :size="18" class="el-icon-arrow-left" />
      </el-button>
    </div>
    <div class="bare-nav-right">
      <el-button type="text" @click="goRight">
        <i :size="18" class="el-icon-arrow-right" />
      </el-button>
    </div>
    <div class="bare-nav-operation">
      <el-dropdown trigger="click" size="mini" @command="systemCommand">
        <el-button type="text">
          <i :size="16" class="el-icon-more" />
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="closeCurrent">
            <i class="el-icon-circle-close" />
            关闭当前
          </el-dropdown-item>
          <el-dropdown-item command="closeOthers">
            <i class="el-icon-error" />
            关闭其他
          </el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
    </div>
    <div class="bare-nav-container" ref="navContainer">
      <div class="bare-nav-scroll-body" ref="navScrollBody" :style="scrollBodyStyle">
        <el-tag
          v-for="(nav, index) in fxNavList"
          v-bind:key="nav.path"
          :closable="true"
          @close="close(nav.path)"
          @click.native.prevent="routerGo(nav.activeRouter, index)"
          size="small"
        >
          <span :class="{ true: 'primary' }[isCurrent(nav)]" class="dot" />
          <span :class="{ true: 'currentText' }[isCurrent(nav)]">{{ nav.title }}</span>
        </el-tag>
      </div>
    </div>
  </div>
</template>

<style lang="less">
  @import 'index.less';
</style>

<script lang="ts">
  import { Component, Vue, Watch } from 'vue-property-decorator'
  import { Route } from 'vue-router'
  import RootModule from '@/store/RootModule'

  @Component
  export default class extends Vue {
    scrollBodyStyle = { left: '0px' }

    // @Prop({ type: Array, default: () => [] }) navList: Array<Route>

    @Watch('$store.state.Root.currentNavActiveIndex', { immediate: true })
    watchCurrentNavActiveIndex(val: number) {
      this.$nextTick(() => {
        const currentActiveEl = (this.$refs.navScrollBody as Node).childNodes[val]
        if (currentActiveEl) {
          const currentElWidth = (currentActiveEl as HTMLElement).offsetWidth
          const currentElLeft = (currentActiveEl as HTMLElement).offsetLeft
          const containerWidth = (this.$refs.navContainer as HTMLElement).offsetWidth
          if (currentElLeft + currentElWidth > containerWidth) {
            const left = currentElLeft + currentElWidth - containerWidth
            this.scrollBodyStyle.left = `-${left}px`
          }
          if (currentElLeft + currentElWidth < containerWidth) {
            this.scrollBodyStyle.left = '0px'
          }
        }
      })
    }

    goRight() {
      const navContainerWidth = (this.$refs.navContainer as HTMLElement).clientWidth
      const scrollBodyWidth = (this.$refs.navScrollBody as HTMLElement).clientWidth
      let left = Math.abs(parseInt(this.scrollBodyStyle.left)) + navContainerWidth
      if (left + navContainerWidth >= scrollBodyWidth) {
        left = scrollBodyWidth - navContainerWidth
        if (left < 0) {
          return
        }
      }
      this.scrollBodyStyle.left = `-${left}px`
    }

    goLeft() {
      const navContainerWidth = (this.$refs.navContainer as HTMLElement).clientWidth
      let left = Math.abs(parseInt(this.scrollBodyStyle.left)) - navContainerWidth
      if (left <= 0) {
        left = 0
      }
      this.scrollBodyStyle.left = `-${left}px`
    }

    isCurrent(nav: Route) {
      return RootModule.currentFxNav.activeRouter === nav.path || RootModule.currentFxNav.path === nav.path
    }

    routerGo(activeRouter: string, index: number) {
      // 如果点击的是当前的则不跳转
      if (this.$route.path === activeRouter) return
      const targetNav = RootModule.fxNavList[index]
      const query = targetNav.query
      this.$router.push({
        path: activeRouter,
        query
      })
      RootModule.setCurrentFxActivateIndex(index)
    }

    async close(path: string) {
      if (RootModule.closePageTip.get(path)) {
        return this.$confirm(RootModule.closePageTip.get(path), '提示').then(() => {
          RootModule.REMOVE_CLOSE_PAGE_TIP(path)
          localStorage.setItem('BatchNosetNull', 'true')
          this.close(path)
        })
      }
      await RootModule.doRemoveFxNav(path)
      if (RootModule.currentFxNav) {
        if (RootModule.currentFxNav.path) {
          if (RootModule.currentFxNav.path === this.$route.path) return
          await this.$router.push({
            path: RootModule.currentFxNav.path,
            query: RootModule.currentFxNav.query
          })
        } else {
          await this.$router.push('/welcome')
        }
      }
    }

    get fxNavList() {
      return RootModule.fxNavList
    }

    contextMenu() {
      // todo 打开右单击菜单
      console.log('')
      // this.showContextMenu = true
    }

    systemCommand(command: string) {
      ;({
        refresh: () => {
          // nothing
        },
        closeCurrent: async () => {
          const nav = RootModule.currentFxNav
          if (nav.closeAble) {
            await this.close(nav.path)
          } else {
            this.$message({
              message: '当前tab不可关闭',
              type: 'warning'
            })
          }
        },
        closeOthers: async () => {
          await RootModule.doCloseOtherFxNav()
        }
      }[command]())
    }
  }
</script>
