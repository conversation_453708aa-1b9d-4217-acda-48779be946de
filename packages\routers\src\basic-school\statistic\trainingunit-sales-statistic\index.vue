<route-meta>
  {
  "isMenu": true,
  "title": "培训单位销售统计",
  "sort": 7,
  "icon": "icon-ribaotongji"
  }
  </route-meta>
<script lang="ts">
  import TrainingunitSalesStatistic from '@hbfe/jxjy-admin-trainingunitSalesStatistic/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'

  @RoleTypeDecorator({
    sellQuery: [],
    sellBatchexport: [],
    details: []
  })
  export default class extends TrainingunitSalesStatistic {}
</script>
