import IdCardTypeVo from '@api/service/common/basic-data-dictionary/query/vo/IdCardTypeVo'
import BasicData, { BusinessDataDictionaryRequest } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
import { Response } from '@hbfe/common'
import IdRegisterCardTypeVo from '@api/service/common/basic-data-dictionary/query/vo/IdRegisterCardTypeVo'
import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'
import DictionaryTypeTypeVo from '@api/service/diff/common/gszj/basic-data-dictionary/query/vo/DictionaryTypeTypeVo'
import { DictionaryTypeEnum } from '@api/service/diff/common/gszj/basic-data-dictionary/query/enum/DictionaryType'

class QueryDictionaryType {
  /**
   * 字典缓存
   */
  dictionaryTypeMap = new Map<string, Array<DictionaryTypeTypeVo>>()

  /**
   * 查询字典
   */
  async queryDictionaryTypeList(type: DictionaryTypeEnum): Promise<DictionaryTypeTypeVo[]> {
    if (!this.dictionaryTypeMap.has(type)) {
      const request = new BusinessDataDictionaryRequest()
      request.businessDataDictionaryType = type
      const res = await BasicData.listBusinessDataDictionaryInSubProject(request)
      this.dictionaryTypeMap.set(type, res.data.map(DictionaryTypeTypeVo.from))
    }
    return this.dictionaryTypeMap.get(type)
  }
}
export default new QueryDictionaryType()
