<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">补学规则</el-breadcrumb-item>
      <el-breadcrumb-item>添加补学规则</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <el-card shadow="never" class="m-card is-header">
        <div slot="header" class="">
          <span class="tit-txt">基础设置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-pt30">
              <el-form-item label="适用行业：" required>
                <el-radio v-model="radio" label="1">建设行业</el-radio>
                <el-radio v-model="radio" label="2">人社行业</el-radio>
                <el-radio v-model="radio" label="3">职业卫生行业</el-radio>
              </el-form-item>
              <el-form-item label="适用范围：" required>
                <el-radio v-model="radio" label="1">
                  平台级别
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                    <i class="el-icon-info m-tooltip-icon f-c9"></i>
                    <div slot="content">
                      平台级别规则：对适用行业下所有培训方案都生效，学员完成学习后，若没有培训方案和地区规则时，平台级别规则生效。
                    </div>
                  </el-tooltip>
                </el-radio>
                <el-radio v-model="radio" label="2">
                  地区级别
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                    <i class="el-icon-info m-tooltip-icon f-c9"></i>
                    <div slot="content">
                      地区级别规则：指定地区的培训学习规则，学员完成学习后，若个人信息的地区在规则设置的地区内，则地区规则生效。
                    </div>
                  </el-tooltip>
                </el-radio>
                <el-radio v-model="radio" label="3">
                  培训方案级别
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip is-small">
                    <i class="el-icon-info m-tooltip-icon f-c9"></i>
                    <div slot="content">
                      培训方案级别：指定培训方案的培训学习规则。
                    </div>
                  </el-tooltip>
                </el-radio>
                <el-alert type="info" show-icon :closable="false" class="m-alert f-mt10">
                  <div class="lh20">
                    培训方案级别的优先级高于地区级别高于平台级别，地区级别的优先级高于平台级别。即：当一个学员合格后满足三个级别的规则时，优先按培训方案级别规则，其次按地区级别规则。
                  </div>
                </el-alert>
              </el-form-item>
              <el-form-item label="选择地区：" required>
                <el-button type="primary" plain>选择地区</el-button>
                <!--表格-->
                <el-table :data="tableData" max-height="500px" class="m-table f-mt15" border>
                  <el-table-column type="index" label="No." width="60"></el-table-column>
                  <el-table-column label="省份" min-width="100">
                    <template>福建省</template>
                  </el-table-column>
                  <el-table-column label="地市" min-width="100">
                    <template>福州市</template>
                  </el-table-column>
                  <el-table-column label="区县" min-width="240">
                    <template>
                      鼓楼区、马尾区、XX区
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="120" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">编辑</el-button>
                      <el-button type="text" size="mini">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
              <el-form-item label="选择培训方案：" required>
                <el-button type="primary" plain>选择培训方案</el-button>
                <!--表格-->
                <el-table :data="tableData" max-height="500px" class="m-table f-mt15" border>
                  <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
                  <el-table-column label="培训方案名称" min-width="220">
                    <template>任务名称任务名称任务名称</template>
                  </el-table-column>
                  <el-table-column label="方案属性" min-width="240">
                    <template>
                      <div>行业：建设行业</div>
                      <div>地区：福建省/福州市/鼓楼区</div>
                      <div>科目类型：科目类型1</div>
                      <div>培训年度：2023年</div>
                    </template>
                  </el-table-column>
                  <el-table-column label="操作" min-width="100" align="center" fixed="right">
                    <template>
                      <el-button type="text" size="mini">删除</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </el-form-item>
              <el-form-item label="指定年度政策学习时间：" required>
                <el-button type="primary" plain>添加年度</el-button>
                <el-alert type="info" show-icon :closable="false" class="m-alert f-mt10">
                  <div class="lh20">
                    设置培训年度学习时间：选择培训年度并设置学习开始时间和合格时间的时间区间。若学员开始时间或合格学习不在设置的学习区间内，则会按照规则生成学习和考试数据。定年度配置学习时间区间，未配置的培训年度则不会触发计算。
                  </div>
                </el-alert>
              </el-form-item>
              <el-form-item label="培训时间区间：" required>
                <div class="f-mb5">
                  <el-tag size="small" class="f-mr20 f-vm">年度：2024</el-tag>
                  <span class="f-mr30">2022-01-01 至 2022-12-31</span>
                  <a href="#" class="f-link el-icon-edit-outline f-mr10"></a>
                  <a href="#" class="f-link el-icon-delete f-mr10"></a>
                </div>
                <div class="f-mb5">
                  <el-tag size="small" class="f-mr20 f-vm">年度：2023</el-tag>
                  <span class="f-mr30">2022-01-01 至 2022-12-31</span>
                  <a href="#" class="f-link el-icon-edit-outline f-mr10"></a>
                  <a href="#" class="f-link el-icon-delete f-mr10"></a>
                </div>
                <div class="f-mb5">
                  <el-tag size="small" class="f-mr20 f-vm">年度：2022</el-tag>
                  <span class="f-mr30">2022-01-01 至 2022-12-31</span>
                  <a href="#" class="f-link el-icon-edit-outline f-mr10"></a>
                  <a href="#" class="f-link el-icon-delete f-mr10"></a>
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never" class="m-card is-header f-mt15">
        <div slot="header" class="">
          <span class="tit-txt">规则设置</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-alert type="warning" show-icon :closable="false" class="m-alert f-mt30">
              学员同一个天内报名多个培训班时：
            </el-alert>
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt10">
              <el-form-item label="是否需要错开开始学习日期：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="错开开始学习时间"></el-radio>
                  <el-radio label="不需要"></el-radio>
                </el-radio-group>
                <el-alert type="info" show-icon :closable="false" class="m-alert f-mt10">
                  <div class="lh20">
                    选择需要错开开通日期时，若学员在同一天内报名了多个培训班时，会将学员不同培训班的学习开始日期（月、日）错开，避免重复
                  </div>
                </el-alert>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <!--以下为更多设置内容-->
        <div class="f-pt20 f-pb40">
          <el-divider>
            <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
              <!--<el-button type="primary" plain>更多设置<i class="el-icon-plus el-icon&#45;&#45;right"></i></el-button>-->
              <!--展开后显示-->
              <el-button type="primary" plain>收起更多设置<i class="el-icon-minus el-icon--right"></i></el-button>
              <div slot="content">
                更多配置内可以设置课程学习规则、考试规则、特殊规则，系统默认设置一套学习规则，若需要调整配置，修改后保存生效，新合格的学员会按照保存的规则计算。
                <p class="f-co f-mt5">设置详细规则时会影响学习规则计算，请谨慎设置！</p>
              </div>
            </el-tooltip>
          </el-divider>
        </div>
        <!--课程学习规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">课程学习规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
              <el-form-item>
                <div slot="label">
                  <!--<span class="f-cr f-mr5">*</span>-->
                  <span class="f-vm"> 每天学习时间</span>
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <i class="el-icon-question m-tooltip-icon f-co f-mlr5"></i>
                    <div slot="content">
                      学习时间是指触发重新计算学习记录时，每门课程的学习时间会在设置的时间段内，时间段外的时间不会学习
                    </div>
                  </el-tooltip>
                  <span>：</span>
                </div>
                <div class="time-item form-l">
                  <div class="time">00:00:00 至 23:59:59</div>
                </div>
              </el-form-item>
              <el-form-item label="每天不学习时间：">
                <el-button type="primary" icon="el-icon-plus" plain>添加时间段</el-button>
                <div class="time-item form-l f-mt15">
                  <div class="time">00:00:00 至 23:59:59</div>
                  <i class="f-link f-cb el-icon-delete f-f18"></i>
                </div>
                <div class="time-item form-l f-mt15">
                  <div class="time">00:00:00 至 23:59:59</div>
                  <i class="f-link f-cb el-icon-delete f-f18"></i>
                </div>
              </el-form-item>
              <el-form-item label="首次开始学习时间：" required>
                开通班级的
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <el-input placeholder="" class="input-num f-mlr5" />
                  <div slot="content">
                    请输入正整数
                  </div>
                </el-tooltip>
                <i class="f-mlr5">~</i>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <el-input placeholder="" class="input-num f-mlr5" />
                  <div slot="content">
                    请输入正整数
                  </div>
                </el-tooltip>
                天内随机开始学习。
              </el-form-item>
              <el-form-item label="每天最多学习时长：" required>
                <el-radio-group v-model="form.resource">
                  <el-radio label="按课程学习学时"></el-radio>
                  <el-radio label="按课程物理时长"></el-radio>
                </el-radio-group>
                <div class="f-mt15">
                  每天课程学习最多
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <el-input placeholder="" class="input-num f-mlr5" />
                    <div slot="content">
                      请输入正整数
                    </div>
                  </el-tooltip>
                  学时且每次学习时长达到
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <el-input placeholder="" class="input-num f-mlr5" />
                    <div slot="content">
                      请输入正整数
                    </div>
                  </el-tooltip>
                  学时，随机休息 60~180 分钟。
                </div>
                <div class="f-mt15">
                  每天课程学习最多
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <el-input placeholder="" class="input-num f-mlr5" />
                    <div slot="content">
                      请输入正整数
                    </div>
                  </el-tooltip>
                  小时且每次学习时长达到
                  <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                    <el-input placeholder="" class="input-num f-mlr5" />
                    <div slot="content">
                      请输入正整数
                    </div>
                  </el-tooltip>
                  小时，随机休息 60~180 分钟。
                </div>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <!--测验规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">测验规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="f-mtb30 f-flex f-align-center">
              <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
              <span class="f-flex-sub"
                >课程学习结束后进入对应的课程测验，测验开始时间和结束时间随机间隔分钟数15-60分钟。</span
              >
            </div>
          </el-col>
        </el-row>

        <!--考试规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">考试规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <div class="f-mt30 f-flex f-align-center">
              <i class="el-icon-s-order f-f24 f-mr10 f-c9"></i>
              <span class="f-flex-sub"
                >课程学习结束后进入考试，考试开始时间和结束时间随机间隔分钟数，最少间隔总考试时长的三分之一的时间。</span
              >
            </div>
            <el-alert type="info" :closable="false" class="m-alert f-mt10 f-mb30">
              例：考试总时长60分钟，开始和结束时间至少间隔20分钟。
            </el-alert>
          </el-col>
        </el-row>

        <!--特殊规则-->
        <div class="m-sub-tit is-border-bottom">
          <span class="tit-txt">特殊规则</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit">
          <el-col :md="20" :lg="16" :xl="13">
            <el-form ref="form" :model="form" label-width="auto" class="m-form f-mt30">
              <el-form-item label=" " required>
                若重新计算的学习合格时间还是超过学习区间，则开通时间随机提前
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <el-input placeholder="" class="input-num f-mlr5" />
                  <div slot="content">
                    请输入正整数
                  </div>
                </el-tooltip>
                <i class="f-mlr5">～</i>
                <el-tooltip effect="dark" placement="top" popper-class="m-tooltip">
                  <el-input placeholder="" class="input-num f-mlr5" />
                  <div slot="content">
                    请输入正整数
                  </div>
                </el-tooltip>
                天。
              </el-form-item>
              <el-alert type="info" show-icon :closable="false" class="m-alert f-mb30">
                <div class="lh20">
                  若计算出来的学习合格时间还是超过学习区间，会根据配置的天数随机将学员的开通时间提前 X
                  天并且重新计算学习记录，若还是超过，会继续提前开始时间，直到符合学习时间区间。
                </div>
              </el-alert>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <div class="m-btn-bar f-tc is-sticky-1">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        checkList: ['开通年度与方案年度不一致'],
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
