import {
  RegionModel,
  StudentInfoResponse,
  StudentUserInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'

class StudentUserListVo extends StudentInfoResponse {
  /**
   * 单位所属地区
   */
  region = ''
  /**
   * 工作单位名称
   */
  companyName = ''
  /**
   * 用户id
   */
  userId = ''
  /**
   * 用户名称
   */
  userName = ''

  /**
   * 证件号
   */
  idCard = ''
  /**
   * 手机号
   */
  phone = ''

  from(data: StudentUserInfoResponse) {
    this.region = this.transfromRegion(data.region)
    this.companyName = data.companyName
    this.userName = data.userName
    this.idCard = data.idCard
    this.phone = data.phone
  }

  private transfromRegion(region: RegionModel): string {
    const temp = new Array<string>()
    if (region?.provinceName) {
      temp.push(region.provinceName)
    } else if (region?.cityName) {
      temp.push(region.cityName)
    } else if (region?.countyName) {
      temp.push(region.countyName)
    }
    return temp.join('-') || '-'
  }
}

export default StudentUserListVo
