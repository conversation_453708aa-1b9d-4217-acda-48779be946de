<template>
  <el-main>
    <!--面包屑-->
    <el-breadcrumb separator-class="el-icon-arrow-right">
      <el-button type="text" size="mini" class="return-btn">
        <i class="iconfont icon-lsh-return"></i>
      </el-button>
      <el-breadcrumb-item :to="{ path: '/' }">个人报名订单</el-breadcrumb-item>
      <el-breadcrumb-item>详情</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="f-p15">
      <!--交易成功-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>订单号：191024160130748942790239</p>
          <p class="state f-cg">交易成功</p>
          <!--<p class="state f-co">等待付款</p>-->
          <!--<p class="state f-cb">支付中</p>-->
          <!--<p class="state f-cb">开通中</p>-->
          <!--<p class="state f-c9">交易关闭</p>-->
        </div>
        <el-steps :active="4" align-center class="process">
          <el-step title="提交订单" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-myorder"></el-step>
          <el-step title="付款" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step
            title="班级开通"
            description="2021-02-02 15:23:23"
            icon="hb-iconfont icon-s-learningcenter"
          ></el-step>
          <el-step title="交易成功" description="2021-02-02 15:23:23" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
      </el-card>
      <!--订单信息-->
      <el-card shadow="never" class="m-card is-header m-order-info f-mb15">
        <div class="f-flex-sub f-plr20 f-pt10">
          <div class="m-tit">
            <span class="tit-txt">订单信息</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="24">
              <el-form-item label="订单类型：">个人订单</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="订单创建人：">林林一</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="创建人帐号：">linlin001</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="分销商：">带货王的下属</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="上级分销商：">带货王</el-form-item>
            </el-col>
          </el-form>
          <div class="m-tit">
            <span class="tit-txt">发票信息</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="24">
              <el-form-item label="发票类型：">增值税电子普通发票</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发票状态：">
                <el-tag type="success" size="small" class="f-mr5">已开票</el-tag>
                <el-tag type="warning" size="small" class="f-mr5">待开票</el-tag>
                <el-tag type="error" size="small" class="f-mr5">冻结中</el-tag>
                <el-tag type="info" size="small" class="f-mr5">已作废</el-tag>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发票号码：">035001900111</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="申请开票时间：">2021-12-12 12:12:12</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发票抬头：">单位 - 福建华博教育科技股份有限公司</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="统一社会信用代码：">25486698745856985</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户银行：">中国建设银行</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="开户帐号：">2548 6698 7458 5698 215</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="注册电话：">0591-87459632</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="注册地址：">福建省福州市鼓楼区工业路611号</el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label=" ">
                <el-button type="primary">下载发票</el-button>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label=" ">
                <el-alert type="info" show-icon :closable="false" class="m-alert">
                  发票查询地址：
                  <a href="https://inv-veri.chinatax.gov.cn/" target="_blank" class="f-link">
                    https://inv-veri.chinatax.gov.cn/
                  </a>
                </el-alert>
              </el-form-item>
            </el-col>
          </el-form>
          <!--未填写发票信息-->
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="12">
              <el-form-item label="是否需要发票：">否</el-form-item>
            </el-col>
          </el-form>
          <div class="m-tit">
            <span class="tit-txt">配送信息</span>
          </div>
          <el-form label-width="140px" class="m-text-form f-pt10 f-pb20">
            <el-col :span="12">
              <el-form-item label="配送方式：">邮寄</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="快递公司：">中国邮政</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收货地址：">福州市工业路 611号</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收件人：">林林一</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="手机号：">13003831001</el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="运单号：">
                435235235
                <el-tooltip class="item" effect="dark" placement="top" popper-class="m-tooltip">
                  <i class="el-icon-document-copy f-link-gray f-ml5 f-c9"></i>
                  <div slot="content">复制运单号并查询</div>
                </el-tooltip>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="发货时间：">2020-07-17 15:12:43</el-form-item>
            </el-col>
          </el-form>
        </div>
        <div class="right f-plr20 f-ptb10">
          <div class="m-tit">
            <span class="tit-txt">购买人信息</span>
          </div>
          <el-form label-width="120px" class="m-text-form is-column f-pt10 f-pb20">
            <el-form-item label="购买人：">林林一</el-form-item>
            <el-form-item label="帐号：">350702198801240811</el-form-item>
            <el-form-item label="手机号：">13003831002</el-form-item>
          </el-form>
          <div class="m-tit">
            <span class="tit-txt">支付信息</span>
          </div>
          <el-form label-width="120px" class="m-text-form is-column f-pt10 f-pb20">
            <el-form-item label="支付方式：">线上支付-web端-支付宝</el-form-item>
            <el-form-item label="交易号：">ZFBP2020032616553398574899791001211</el-form-item>
            <el-form-item label="付款时间：">2020-07-17 11:49:21</el-form-item>
            <el-form-item label="付款成功时间：">-</el-form-item>
          </el-form>
        </div>
      </el-card>
      <!--购买清单-->
      <el-card shadow="never" class="m-card is-header f-mb15">
        <div class="f-plr20 f-pt10">
          <div class="m-tit is-small">
            <span class="tit-txt">购买清单</span>
          </div>
          <el-table class="m-table is-header">
            <el-table-column label="物品名称" min-width="300"></el-table-column>
            <el-table-column label="学时" width="150" align="center"></el-table-column>
            <el-table-column label="数量" width="150" align="center"></el-table-column>
            <el-table-column label="实付金额(元)" width="150" align="right"></el-table-column>
            <el-table-column label="订单状态" width="120"></el-table-column>
            <el-table-column label="换班状态" width="120" align="center"></el-table-column>
            <el-table-column label="售后" width="150" align="center"></el-table-column>
          </el-table>
          <el-alert type="info" :closable="false" class="m-alert f-mt10">
            <div class="f-flex">
              <i class="iconfont icon-fangan f-cb f-mr5"></i>
              <div class="f-flex-sub">
                <span class="f-fb f-cb">培训方案：【培训班-选课规则】2021年福建专技公需课培训班</span>
                <el-tag class="f-ml10" size="small">2020年</el-tag>
                <el-tag class="f-ml10" size="small">专技行业</el-tag>
                <el-tag class="f-ml10" size="small">公需科目</el-tag>
              </div>
            </div>
          </el-alert>
          <el-table :data="tableData" max-height="500px" class="m-table is-body">
            <el-table-column min-width="300">
              <template slot-scope="scope">
                <div v-if="scope.$index === 0">
                  <div class="f-flex f-align-start">
                    <div class="f-flex-sub">
                      <p>这是一个培训班名称培训班名称</p>
                    </div>
                  </div>
                </div>
                <div v-else>
                  <div class="f-flex f-align-start">
                    <el-tag type="danger" size="small">华医网</el-tag>
                    <div class="f-flex-sub f-mt5 f-ml5">
                      <p>这是一个培训班名称培训班名称</p>
                    </div>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column width="150" align="center">
              <template>2</template>
            </el-table-column>
            <el-table-column width="150" align="center">
              <template>1</template>
            </el-table-column>
            <el-table-column width="150">
              <template
                ><span class="f-cr">200.00</span
                ><el-tag type="warning" size="mini" class="f-ml5">优惠申请</el-tag></template
              >
            </el-table-column>
            <el-table-column width="120">
              <template>
                <el-badge is-dot type="success" class="badge-status">交易成功</el-badge>
                <!--<el-badge is-dot type="primary" class="badge-status">支付中</el-badge>-->
                <!--<el-badge is-dot type="warning" class="badge-status">等待付款</el-badge>-->
                <!--<el-badge is-dot type="primary" class="badge-status">开通中</el-badge>-->
                <!--<el-badge is-dot type="info" class="badge-status">交易关闭</el-badge>-->
              </template>
            </el-table-column>
            <el-table-column width="120" align="center">
              <template>
                <!--<el-tag type="success">换班成功</el-tag>-->
                <el-tag type="warning">换班中</el-tag>
                <p class="f-mt5"><a href="#" class="f-link f-underline f-cb f-f12">换班详情</a></p>
              </template>
            </el-table-column>
            <el-table-column width="150" align="center">
              <template><el-button type="text" size="mini">退款</el-button></template>
            </el-table-column>
          </el-table>
          <div class="m-order-sum f-mtb30">
            <div class="item">
              共 <i class="f-cr">20</i> 学时，商品总额：
              <span class="price">¥ <i class="num">180.00</i></span>
            </div>
            <div class="sum-price">
              实付金额：<span class="price">¥ <i class="num">180.00</i></span>
            </div>
          </div>
          <div class="m-order-sum f-mtb30">
            <div class="item">
              共 <i class="f-cr">20</i> 学时，商品总额：
              <span class="price">¥ <i class="num">180.00</i></span>
            </div>
            <div class="item">
              实付金额：
              <span class="price">¥ <i class="num">180.00</i></span>
            </div>
            <div class="sum-price">
              已退款金额：<span class="price">¥ <i class="num">180.00</i></span>
            </div>
          </div>
        </div>
      </el-card>
      <!--其他状态-->
      <!--等待付款-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>订单号：191024160130748942790239</p>
          <p class="state f-co">等待付款</p>
        </div>
        <el-steps :active="1" align-center class="process">
          <el-step title="提交订单" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-myorder"></el-step>
          <el-step title="付款" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step title="班级开通" icon="hb-iconfont icon-s-learningcenter"></el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
      </el-card>
      <!--支付中-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>订单号：191024160130748942790239</p>
          <p class="state f-cb">支付中</p>
        </div>
        <el-steps :active="1" align-center class="process">
          <el-step title="提交订单" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-myorder"></el-step>
          <el-step title="付款" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step title="班级开通" icon="hb-iconfont icon-s-learningcenter"></el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
      </el-card>
      <!--开通中-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>订单号：191024160130748942790239</p>
          <p class="state f-cb">开通中</p>
        </div>
        <el-steps :active="3" align-center class="process">
          <el-step title="提交订单" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-myorder"></el-step>
          <el-step title="付款" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-pay"></el-step>
          <el-step
            title="班级开通"
            description="2021-02-02 15:23:23"
            icon="hb-iconfont icon-s-learningcenter"
          ></el-step>
          <el-step title="交易成功" icon="hb-iconfont icon-success"></el-step>
        </el-steps>
      </el-card>
      <!--交易关闭-->
      <el-card shadow="never" class="m-card is-header m-order-state">
        <div class="info">
          <p>订单号：191024160130748942790239</p>
          <p class="state f-c9">交易关闭</p>
        </div>
        <el-steps :active="2" align-center class="process">
          <el-step title="提交订单" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-myorder"></el-step>
          <el-step title="交易关闭" description="2021-02-02 15:23:23" icon="hb-iconfont icon-s-close"></el-step>
        </el-steps>
      </el-card>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{}, {}],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      arraySpanMethod({ rowIndex, columnIndex }) {
        if (rowIndex % 2 === 0) {
          if (columnIndex === 0) {
            return [1, 6]
          } else if (columnIndex === 1) {
            return [0, 0]
          }
        }
      },
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      }
    }
  }
</script>
