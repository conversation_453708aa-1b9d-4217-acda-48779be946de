<route-meta>
{
"isMenu": true,
"hideMenu": true,
"title": "分销商品销售统计",
"sort": 7,
"icon": "icon-mingxi"
}
</route-meta>

<script lang="ts">
  import CommodityStatistic from '@hbfe/jxjy-admin-commodityStatistic/src/index.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY, DQGLY } from '@/models/RoleTypes'
  // 原型删除暂不维护
  @RoleTypeDecorator({
    query: [WXGLY, DQGLY],
    export: [WXGLY, DQGLY],
    allSync: [WXGLY, DQGLY],
    testDetail: [WXGLY, DQGLY],
    examDetail: [WXGLY, DQGLY],
    toLog: [WXGLY, DQGLY],
    userInfo: [WXGLY, DQGLY],
    classInfo: [WXGLY, DQGLY],
    studyLog: [WXGLY, DQGLY],
    exceptionManagement: [WXGLY, DQGLY]
  })
  export default class extends CommodityStatistic {}
</script>
