/**
 * 用户登录状态
 */
class QueryLoginStatus {
  //
  isLogin = false
  showDialog = false
  isloginType = 'first'
  /**
   * 弹框文本
   */
  context = ''
  getLoginStatus() {
    return this.isLogin
  }
  /**
   * 设置登录状态
   * @param key 是否登录
   */
  setLoginStatus(key: boolean) {
    this.isLogin = key
  }
  setShowDialog(key: boolean, type?: string, context?: string) {
    this.showDialog = key
    this.isloginType = type
    this.context = context || ''
  }
}
export default new QueryLoginStatus()
