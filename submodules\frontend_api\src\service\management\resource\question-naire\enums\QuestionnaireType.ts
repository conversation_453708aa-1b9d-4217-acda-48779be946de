import AbstractEnum from '@api/service/common/enums/AbstractEnum'

export enum QuestionnaireTypeEnum {
  /**
   * 全部
   */
  all = 0,
  /**
   * 普通问卷
   */
  ordinary = 1,
  /**
   * 量表问卷
   */
  scale = 2
}
class QuestionnaireType extends AbstractEnum<QuestionnaireTypeEnum> {
  static enum = QuestionnaireTypeEnum

  constructor(status?: QuestionnaireTypeEnum) {
    super()
    this.current = status
    this.map.set(QuestionnaireTypeEnum.all, '全部')
    this.map.set(QuestionnaireTypeEnum.ordinary, '普通问卷')
    this.map.set(QuestionnaireTypeEnum.scale, '量表问卷')
  }
}
export default QuestionnaireType
