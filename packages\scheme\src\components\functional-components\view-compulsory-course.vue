<template>
  <div class="pure">
    <el-drawer
      title="已选必学课程"
      :visible.sync="visible"
      size="1200px"
      custom-class="m-drawer"
      :wrapper-closable="false"
      :close-on-press-escape="false"
    >
      <div class="drawer-bd">
        <el-row :gutter="16" class="m-query f-mt10">
          <el-form :inline="true" label-width="auto">
            <el-col :span="10">
              <el-form-item label="课程名称">
                <el-input v-model="searchCourseName" clearable placeholder="请输入课程名称" />
              </el-form-item>
            </el-col>
            <el-col :span="10" v-if="hasChildren">
              <el-form-item label="课程分类">
                <el-cascader
                  clearable
                  lazy
                  :options="resource.childOutlines"
                  :props="defaultProps"
                  v-model="nodePaths"
                  placeholder="请选择分类"
                ></el-cascader>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item>
                <el-button type="primary" @click="searchBase">查询</el-button>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <p class="f-mt15">
          一共 <i class="f-cr">{{ courseTotal }}</i> 门，<i class="f-cr">{{ coursePeriodTotal }}</i> 学时
        </p>
        <!--表格-->
        <el-table stripe :data="pageCourseList" max-height="500px" class="m-table f-mt10">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="课程名称" min-width="300">
            <template slot-scope="scope">{{ scope.row.name }}</template>
          </el-table-column>
          <el-table-column label="所属分类" min-width="240" v-if="hasChildren">
            <template slot-scope="scope">{{ getCourseCategory(scope.row) }}</template>
          </el-table-column>
          <el-table-column label="学时" min-width="140" align="center">
            <template slot-scope="scope">{{ scope.row.period }}</template>
          </el-table-column>
          <el-table-column label="操作" width="80" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" @click="viewCourse(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-pagination
          background
          class="f-mt15 f-tr"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="page.pageNo"
          :page-sizes="[5, 10, 15, 20, 25]"
          :page-size="page.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="page.totalSize"
        >
        </el-pagination>
      </div>
      <div class="m-btn-bar drawer-ft">
        <el-button @click="closeViewCompulsoryCourse">取消</el-button>
        <el-button type="primary" @click="closeViewCompulsoryCourse">确定</el-button>
      </div>
    </el-drawer>
  </div>
</template>

<script lang="ts">
  import { Component, Emit, Prop, Vue, Watch } from 'vue-property-decorator'
  import { cloneDeep } from 'lodash'
  import Classification from '@api/service/management/train-class/mutation/vo/Classification'
  import {
    SchemeCourseDetailInCoursePackage,
    CreateSchemeUtils
  } from '@hbfe/jxjy-admin-scheme/src/utils/CreateSchemeUtils'
  import UtilsClass from '@hbfe/jxjy-admin-common/src/util'
  import CalculatorObj from '@api/service/common/utils/CalculatorObj'
  import { CompulsoryCourseInfo } from '@api/service/management/train-class/mutation/vo/CompulsoryCourseInfo'
  import { CourseLoadModeEnum } from '@api/service/management/train-class/mutation/Enum/CourseLoadMode'
  import QueryCourseM from '@api/service/management/resource/course/query/QueryCourse'

  class Page {
    // 当前页数
    pageNo: number
    // 当前条数
    pageSize: number
    // 总数
    totalSize: number

    constructor() {
      this.pageNo = 1
      this.pageSize = 10
      this.totalSize = 0
    }
  }

  @Component
  export default class extends Vue {
    /**
     * 是否展示
     */
    @Prop({
      required: true,
      type: Boolean,
      default: false
    })
    value: boolean

    @Watch('value', {
      immediate: true,
      deep: true
    })
    valueChange(val: any) {
      this.visible = cloneDeep(val)
    }

    @Emit('input')
    @Watch('visible', {
      immediate: true,
      deep: true
    })
    visibleChange(val: number) {
      return val
    }

    /**
     * 数据来源
     */
    @Prop({
      type: Classification,
      default: () => new Classification()
    })
    resource: Classification

    /**
     * 是否有分类
     */
    @Prop({
      type: Boolean,
      default: false
    })
    hasChildren: boolean
    /**
     * 方案id
     */
    @Prop({
      type: String,
      default: ''
    })
    schemeId: string

    // 是否显示抽屉
    visible = false

    // 分页
    page: Page = new Page()
    // 课程名称
    searchCourseName = ''
    // 课程列表
    courseList: CompulsoryCourseInfo[] = [] as CompulsoryCourseInfo[]
    // 过滤课程列表
    filterCourseList: CompulsoryCourseInfo[] = [] as CompulsoryCourseInfo[]
    // 过滤课程列表 - 分页
    pageCourseList: CompulsoryCourseInfo[] = [] as CompulsoryCourseInfo[]
    // 节点路径
    nodePaths: string[] = [] as string[]
    // 查必学课程
    queryCourseM: QueryCourseM = new QueryCourseM()
    // 级联选择器默认配置
    defaultProps = {
      label: 'name',
      children: 'childOutlines',
      value: 'id',
      checkStrictly: true // 取消关联，可选任意级
    }

    /**
     * 课程数
     */
    get courseTotal() {
      return this.pageCourseList?.length || 0
    }

    /**
     * 课程学时总数
     */
    get coursePeriodTotal() {
      return (
        this.pageCourseList?.reduce((prev, cur) => {
          return CalculatorObj.add(cur.period, prev)
        }, 0) || 0
      )
    }

    /**
     * 数据初始化
     */
    async setData(isDetail = false) {
      const outlineTreeLeaves = CreateSchemeUtils.treeFindAllLeaves<Classification>([this.resource], 'childOutlines')
      const filterTree = outlineTreeLeaves.filter(
        (item) =>
          item.compulsoryCourseIdList?.length &&
          !item.compulsoryCourseInfoList?.length &&
          (item.courseLoadMode === CourseLoadModeEnum.BY_OUTLINE_ID || isDetail)
      )
      console.log(filterTree, outlineTreeLeaves, 'filterTrees')
      await Promise.all(
        filterTree?.map(async (item) => {
          item.compulsoryCourseInfoList = await this.queryCourseM.queryAllCourseListInSchemeByOutline(
            this.schemeId,
            item.idCopy,
            item.compulsoryCourseIdList
          )
        })
      )
      console.log(outlineTreeLeaves, 'outlineTreeLeaves')

      this.courseList = this.getInitCourseList(this.resource)
      this.searchCourseName = ''
      this.nodePaths = [] as string[]
      this.searchBase()
    }

    /**
     * 获取必学课程列表 - 有分类
     */
    getCourseListWithClassification(tree: Array<Classification>): CompulsoryCourseInfo[] {
      let result = [] as CompulsoryCourseInfo[]
      if (CreateSchemeUtils.isWeightyArray(tree)) {
        const outlineTreeLeaves = CreateSchemeUtils.treeFindAllLeaves<Classification>(tree, 'childOutlines')
        outlineTreeLeaves?.forEach((el: Classification) => {
          const subResult = this.getCourseListWithoutClassification(el)
          result = [...result, ...subResult]
        })
      }
      return result
    }

    /**
     * 获取必学课程列表 - 无分类
     */
    getCourseListWithoutClassification(resource: Classification) {
      const result = [] as CompulsoryCourseInfo[]
      const compulsoryCourseInfoList = resource.compulsoryCourseInfoList
      const compulsoryCourseIdList = resource.compulsoryCourseIdList
      if (
        resource.coursePackageId &&
        CreateSchemeUtils.isWeightyArray(compulsoryCourseInfoList) &&
        CreateSchemeUtils.isWeightyArray(compulsoryCourseIdList)
      ) {
        compulsoryCourseInfoList.forEach((subEl) => {
          const index = compulsoryCourseIdList.indexOf(subEl.id)
          if (index > -1) {
            subEl.courseCategoryInfo = this.outlineTreeFindPath((node: Classification) => {
              return node.coursePackageId === resource.coursePackageId
            }, 'name')
            result.push(subEl)
          }
        })
      }
      return result
    }

    /**
     * 获取课程列表 - 初始化
     */
    getInitCourseList(resource: Classification) {
      if (this.hasChildren) {
        // 有分类
        return this.getCourseListWithClassification(resource.childOutlines)
      } else {
        // 无分类
        return this.getCourseListWithoutClassification(resource)
      }
    }

    /**
     * 获取课程列表 - 查询中（有分类）
     */
    getSearchCourseList() {
      if (this.hasChildren) {
        // 有分类
        const nodePaths = this.nodePaths
        const nodePathsLength = this.nodePaths?.length || 0
        const targetNodeId = CreateSchemeUtils.isWeightyArray(nodePaths) ? nodePaths[nodePathsLength - 1] : ''
        if (targetNodeId) {
          const outline = CreateSchemeUtils.treeFind(
            this.resource.childOutlines,
            (node: Classification) => {
              return node.id === targetNodeId
            },
            'childOutlines'
          )
          const option = new Classification()
          if (!option.childOutlines) option.childOutlines = [] as Classification[]
          option.childOutlines.push(outline)

          console.log(outline, 'outline')
          return this.getCourseListWithClassification(option.childOutlines)
        } else {
          return this.getCourseListWithClassification(this.resource.childOutlines)
        }
      } else {
        // 无分类
        return this.getCourseListWithoutClassification(this.resource)
      }
    }

    /**
     * 查询课程列表 - 指定首页
     */
    searchBase() {
      this.page.pageNo = 1
      this.pageCourse()
    }

    /**
     * 查询课程列表
     */
    pageCourse() {
      this.courseList = this.getSearchCourseList()
      this.filterCourseList = [] as CompulsoryCourseInfo[]
      this.pageCourseList = [] as CompulsoryCourseInfo[]
      this.courseList.forEach((el: CompulsoryCourseInfo) => {
        if (el.name.includes(this.searchCourseName)) {
          this.filterCourseList.push(el)
        }
      })
      this.pageCourseList = this.pageBySize(this.filterCourseList, this.page.pageNo, this.page.pageSize)
      console.log('filterCourseList', this.filterCourseList)
      console.log('pageCourseList', this.pageCourseList)
      this.page.totalSize = this.filterCourseList.length
    }

    /**
     * 手动分页
     */
    pageBySize(arr: CompulsoryCourseInfo[], pageNo: number, pageSize: number) {
      const offset = (pageNo - 1) * pageSize
      const maxSize = pageNo * pageSize
      return maxSize >= arr.length ? arr.slice(offset, arr.length) : arr.slice(offset, offset + pageSize)
    }

    /**
     * 切换课程分页 - 每页条数
     */
    handleSizeChange(pageSize: number) {
      this.page.pageSize = pageSize
      this.pageCourse()
    }

    /**
     * 切换课程分页 - 第几页
     */
    handleCurrentChange(pageNo: number) {
      this.page.pageNo = pageNo
      this.pageCourse()
    }

    /**
     * 获取课程分类信息
     */
    getCourseCategory(row: SchemeCourseDetailInCoursePackage) {
      return row.courseCategoryInfo?.join(' > ') || ''
    }

    /**
     * 查看课程
     */
    viewCourse(item: SchemeCourseDetailInCoursePackage) {
      const targetUrl = `/resource/course/detail/${item.id}`
      UtilsClass.openUrl(targetUrl)
    }

    /**
     * 关闭弹窗
     */
    closeViewCompulsoryCourse() {
      this.visible = false
    }
    /**
     * 查找节点路径 - 课程大纲树通用
     */
    outlineTreeFindPath(func: any, key: string) {
      return CreateSchemeUtils.treeFindPath<Classification>(this.resource.childOutlines, func, key, 'childOutlines')
    }
  }
</script>

<style lang="scss" scoped>
  .pure {
    margin: 0;
    padding: 0;
  }
</style>
