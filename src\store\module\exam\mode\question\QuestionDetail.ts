/**
 *
 * 试题详情
 * @author: eleven
 * @date: 2020/4/13
 */
import { Tag } from '@/store/module/exam/mode/question/Tag'
import {
  ComprehensiveChildQuestionResponse,
  ComprehensiveResponse,
  JudgementResponse,
  MultipleChoiceResponse,
  SingleChoiceResponse
} from '@api/gateway/PreExam-default'
import { ExamUtil } from '@/store/module/exam/common/ExamUtil'
import { ExamConstant } from '@/store/module/exam/common/ExamConstant'

export class QuestionDetail {
  /**
   * 章节
   */
  chapters: Array<Tag>
  /**
   * 题类
   */
  questionCategory: string
  /**
   * 是否易错题
   */
  errorProne: boolean
  id: string
  applyTypes: Array<number>
  libraryId: string
  title: string
  judgement: JudgementResponse
  singleChoice: SingleChoiceResponse
  multipleChoice: MultipleChoiceResponse
  comprehensive: ComprehensiveResponse
  questionType: number
  mode: number
  difficulty: number
  description: string
  enabled: boolean
  /**
   * 创建人id
   */
  createUserId: string
  /**
   * 创建时间
   */
  createTime: string
  /**
   * 创建人
   */
  createUserName: string

  /**
   * 解析试题类型
   */
  resolverQuestionCategory() {
    return ExamUtil.resolverQuestionCategory(this.questionCategory)
  }

  /**
   * 解析试题题型
   * 子题也有需要解析
   */
  resolverQuestionType(questionType: number) {
    return ExamUtil.resolverQuestionType(questionType)
  }

  /**
   * 解析index为字母
   * @param index
   */
  resolverIndexToCharCode(index: number) {
    return ExamUtil.matchCharCode(index)
  }

  /**
   * 解析正确答案-详情回显
   * @param children
   */
  resolverCorrectAnswerIndexToCharCode(children: ComprehensiveChildQuestionResponse) {
    if (this.questionType === ExamConstant.QUESTION_TYPE_SINGLE) {
      return ExamUtil.matchCharCode(
        this.singleChoice.choiceItems.findIndex(p => p.id === this.singleChoice.correctAnswer)
      )
    } else if (this.questionType === ExamConstant.QUESTION_TYPE_MULTIPLE) {
      const arr = new Array<string>()
      arr.push(
        ...this.multipleChoice.correctAnswers.map(p => {
          return ExamUtil.matchCharCode(this.multipleChoice.choiceItems.findIndex(c => c.id === p))
        })
      )
      return arr.join('、')
    } else if (this.questionType === ExamConstant.QUESTION_TYPE_COMPREHENSIVE) {
      if (children.questionType === ExamConstant.QUESTION_TYPE_SINGLE) {
        return ExamUtil.matchCharCode(
          children.singleChoice.choiceItems.findIndex(p => p.id === children.singleChoice.correctAnswer)
        )
      } else if (children.questionType === ExamConstant.QUESTION_TYPE_MULTIPLE) {
        const arr = new Array<string>()
        arr.push(
          ...children.multipleChoice.correctAnswers.map(p => {
            return ExamUtil.matchCharCode(children.multipleChoice.choiceItems.findIndex(c => c.id === p))
          })
        )
        return arr.join('、')
      }
    }
  }
}
