/*
 * @Description: 用户信息
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-02-28 10:37:33
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-03-01 17:30:03
 */

// import CertificateQueryVo from '@api/service/customer/user/query/vo/CertificateQueryVo'
import {
  AccountResponse,
  OpenPlatformBindResponse,
  RegionModel,
  StudentInfoResponse,
  StudentUserInfoResponse
} from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
class UserInfoVo extends StudentInfoResponse {
  /**
   * 账户信息
   */
  accountInfo = new AccountResponse()
  /**
   * 学员用户信息
   */
  userInfo = new StudentUserInfoResponse()
  /**
   * 第三方绑定信息
   */
  openPlatformBind = new OpenPlatformBindResponse()
  constructor() {
    super()
    this.userInfo.region = new RegionModel()
  }
}
export default UserInfoVo
