import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'

const microServiceName = 'ms-questionnaire-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-questionnaire-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * @Author: chenzeyu
@CreateTime: 2024-12-06  14:12
@Description: TODO
 */
export class ApplyQuestionnaireVerifyResponse {
  /**
   * 30001 问卷已停用,无需作答
30002 问卷未开始
30003 问卷已结束
   */
  code: string
  message: string
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 申请问卷调查
   * @param mutate 查询 graphql 语法文档
   * @param studentToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyQuestionnaire(
    studentToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyQuestionnaire,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**
   * @param mutate 查询 graphql 语法文档
   * @param studentToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyQuestionnaireVerify(
    studentToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyQuestionnaireVerify,
    operation?: string
  ): Promise<Response<ApplyQuestionnaireVerifyResponse>> {
    return commonRequestApi<ApplyQuestionnaireVerifyResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
