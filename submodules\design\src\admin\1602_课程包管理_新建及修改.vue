<template>
  <el-main>
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="">
          <span class="tit-txt">基础信息</span>
        </div>
        <el-row type="flex" justify="center" class="width-limit f-p20">
          <el-col :md="14" :lg="13" :xl="10">
            <el-form ref="form" :model="form" label-width="140px" class="m-form">
              <el-form-item label="课程包名称：" required>
                <el-input v-model="form.name" clearable placeholder="请输入课程包名称" />
              </el-form-item>
              <el-form-item label="展示名称：">
                <el-input v-model="form.name" clearable placeholder="请输入展示名称" />
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-card>
      <el-row :gutter="15" class="is-height f-mb15">
        <el-col :md="8">
          <el-card shadow="never" class="m-card f-mb15">
            <div slot="header" class="">
              <span class="tit-txt">可选课程</span>
            </div>
            <el-row :gutter="16" class="m-query is-border-bottom">
              <el-form :inline="true" label-width="auto">
                <el-col :span="18">
                  <el-form-item label="课程分类">
                    <el-input v-model="input" clearable placeholder="请输入末级课程分类名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <!--表格-->
            <el-table
              :data="tableData1"
              row-key="id"
              lazy
              :load="load"
              :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
              :show-header="false"
              max-height="500px"
              class="m-table is-tree f-mb20"
            >
              <el-table-column label="分类名称" prop="field01"></el-table-column>
            </el-table>
          </el-card>
        </el-col>
        <el-col :md="16">
          <el-card shadow="never" class="m-card f-mb15">
            <el-row :gutter="16" class="m-query no-gutter">
              <el-form :inline="true" label-width="auto">
                <el-col :span="8">
                  <el-form-item label="课程名称">
                    <el-input v-model="input" clearable placeholder="请输入课程名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item>
                    <el-button type="primary">查询</el-button>
                  </el-form-item>
                </el-col>
              </el-form>
            </el-row>
            <el-alert type="warning" :closable="false" class="m-alert f-mb10">
              共有 0 门课
            </el-alert>
            <el-table stripe :data="tableData" max-height="500px" class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="课程名称" min-width="300">
                <template>课程名称课程名称课程名称课程名称课程名称课程名称</template>
              </el-table-column>
              <el-table-column label="学时" min-width="100" align="center">
                <template>13</template>
              </el-table-column>
              <el-table-column label="综合评分" min-width="100" align="center">
                <template>5.0</template>
              </el-table-column>
              <el-table-column label="创建时间" min-width="180">
                <template>2020-11-11 12:20:20</template>
              </el-table-column>
              <el-table-column type="selection" width="100" class-name="is-selection" fixed="right"></el-table-column>
            </el-table>
            <!--分页-->
            <el-pagination
              background
              class="f-mt15 f-tr"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="currentPage4"
              :page-sizes="[100, 200, 300, 400]"
              :page-size="100"
              layout="total, sizes, prev, pager, next, jumper"
              :total="400"
            >
            </el-pagination>
          </el-card>
        </el-col>
      </el-row>
      <el-card shadow="never" class="m-card f-mb15">
        <div slot="header" class="f-clear">
          <span class="tit-txt f-fl">已选待确认课程（共 5 门）</span>
          <el-tooltip class="item" effect="dark" placement="right" popper-class="m-tooltip">
            <i class="el-icon-info m-tooltip-icon f-c9"></i>
            <div slot="content">
              课程学习学时必须大于0，支持小数点后一位，如需调整课程的展示顺序，可长按具体课程拖拽至想要的位置。
            </div>
          </el-tooltip>
        </div>
        <el-table stripe :data="tableData" max-height="500px" class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="排序" width="70" align="center">
            <template><i class="hb-iconfont icon-drag f-f22 f-link-gray"></i></template>
          </el-table-column>
          <el-table-column label="课程名称" min-width="300">
            <template>课程名称课程名称课程名称课程名称课程名称课程名称</template>
          </el-table-column>
          <el-table-column label="物理学时" min-width="100" align="center">
            <template>13</template>
          </el-table-column>
          <el-table-column label="学习学时" min-width="100" align="center">
            <template>
              <el-input v-model="input1" class="f-input-num" />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="140" align="center" fixed="right">
            <template>
              <el-button type="text" size="mini">预览</el-button>
              <el-button type="text" size="mini">取消</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <div class="m-btn-bar f-mt10 f-tc is-sticky-1">
        <el-button>取消</el-button>
        <el-button type="primary">保存</el-button>
      </div>
    </div>
  </el-main>
</template>
<script>
  export default {
    data() {
      return {
        activeName: 'first',
        activeName1: 'first',
        activeName2: 'first',
        props: { multiple: true },
        radio: 3,
        input: '',
        input1: '1',
        select: '',
        cascader: [
          {
            value: 'zhinan',
            label: '指南',
            children: [
              {
                value: 'shejiyuanze',
                label: '设计原则',
                children: [
                  {
                    value: 'yizhi',
                    label: '一致'
                  },
                  {
                    value: 'fankui',
                    label: '反馈'
                  },
                  {
                    value: 'xiaolv',
                    label: '效率'
                  },
                  {
                    value: 'kekong',
                    label: '可控'
                  }
                ]
              },
              {
                value: 'daohang',
                label: '导航',
                children: [
                  {
                    value: 'cexiangdaohang',
                    label: '侧向导航'
                  },
                  {
                    value: 'dingbudaohang',
                    label: '顶部导航'
                  }
                ]
              }
            ]
          },
          {
            value: 'ziyuan',
            label: '资源',
            children: [
              {
                value: 'axure',
                label: 'Axure Components'
              },
              {
                value: 'sketch',
                label: 'Sketch Templates'
              },
              {
                value: 'jiaohu',
                label: '组件交互文档'
              }
            ]
          }
        ],
        value1: '',
        tableData: [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }],
        tableData1: [
          {
            id: 1,
            field01: '这里是分类名称',
            hasChildren: true
          },
          {
            id: 2,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 3,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 4,
            field01: '分类名称1',
            hasChildren: true
          },
          {
            id: 5,
            field01: '分类名称1',
            hasChildren: true
          }
        ],
        form: {
          name: '',
          region: '',
          date1: '',
          date2: '',
          delivery: false,
          type: [],
          resource: '',
          desc: ''
        },
        dialogImageUrl: '',
        dialogVisible: false,
        dialog1: false,
        fits: ['fill', 'contain', 'cover', 'none', 'scale-down']
      }
    },
    methods: {
      handleClick(tab, event) {
        console.log(tab, event)
      },
      handleNodeClick(data) {
        console.log(data)
      },
      handleRemove(file, fileList) {
        console.log(file, fileList)
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url
        this.dialogVisible = true
      },
      remoteMethod(query) {
        if (query !== '') {
          this.loading = true
          setTimeout(() => {
            this.loading = false
            this.options = this.list.filter(item => {
              return item.label.toLowerCase().indexOf(query.toLowerCase()) > -1
            })
          }, 200)
        } else {
          this.options = []
        }
      },
      handleSizeChange(val) {
        console.log(`每页 ${val} 条`)
      },
      handleCurrentChange(val) {
        console.log(`当前页: ${val}`)
      },
      load(tree, treeNode, resolve) {
        setTimeout(() => {
          resolve([
            {
              id: 11,
              field01: '分类名称1-1'
            },
            {
              id: 12,
              field01: '分类名称1-2'
            }
          ])
        }, 1000)
      }
    }
  }
</script>
