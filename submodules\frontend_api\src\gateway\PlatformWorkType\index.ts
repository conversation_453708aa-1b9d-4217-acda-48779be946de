import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import Response from '../../Response'
import { DocumentNode } from 'graphql'

// 请求地址路径
export const SERVER_URL = '/web/gql/PlatformWorkType'

// 是否微服务
const isMicroService = false

// 服务名称，未必等于 schema 名称
const schemaName = 'PlatformWorkType'

// 请求配置项
export const requestConfig = {
  isMicroService,
  schemaName,
  microServiceName: ''
}

// 枚举
export enum SearchMatchingPattern {
  RLIKE = 'RLIKE',
  LLIKE = 'LLIKE',
  EQUAL = 'EQUAL',
  LIKE = 'LIKE'
}

// 类

export class WorkTypeQueryDTO {
  /**
   * 工种名称模糊查询
   */
  name?: string
  /**
   * 工种所在的门户课程分类ID
   */
  portalCourseCategoryIds?: Array<string>
}

export class Page {
  pageNo?: number
  pageSize?: number
}

export class PortalCourseCategoryRelationWorkTypeResponse {
  portalCourseCategoryId: string
  workTypeList: Array<WorkTypeResponse>
}

export class WorkTypeCategoryRelationResponse {
  categoryId: string
  workTypeList: Array<WorkTypeResponse>
}

export class WorkTypeResponse {
  id: string
  name: string
  code: string
  sort: number
  creatorId: string
  createTime: string
}

export class WorkTypeResponsePage {
  pageSize: number
  pageNo: number
  totalPageSize: number
  totalSize: number
  currentPageData: Array<WorkTypeResponse>
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(SERVER_URL, {
      query: query,
      variables: params,
      operation: operation
    })
  }

  /**   * 获取所有工种信息
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getAllWorkTypeList(
    params: { name?: string; matchingPattern?: SearchMatchingPattern },
    query: DocumentNode = GraphqlImporter.getAllWorkTypeList,
    operation?: string
  ): Promise<Response<Array<WorkTypeResponse>>> {
    return commonRequestApi<Array<WorkTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取工种信息
   * @param query 查询 graphql 语法文档
   * @param id 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getWorkType(
    id: string,
    query: DocumentNode = GraphqlImporter.getWorkType,
    operation?: string
  ): Promise<Response<WorkTypeResponse>> {
    return commonRequestApi<WorkTypeResponse>(
      SERVER_URL,
      {
        query: query,
        variables: { id },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取工种分类下的所有工种
   * @param containsChildren 代表是否包含其子分类对应的工种,false的话就是该分类直接对应的工种
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getWorkTypeListByCategoryId(
    params: { categoryId?: string; containsChildren: boolean },
    query: DocumentNode = GraphqlImporter.getWorkTypeListByCategoryId,
    operation?: string
  ): Promise<Response<Array<WorkTypeResponse>>> {
    return commonRequestApi<Array<WorkTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据工种分类id集合批量获取对应的工种信息,该查询不会递归到子分类,就是以传入的当前分类id直接关联的工种信息
   * @param query 查询 graphql 语法文档
   * @param categoryIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getWorkTypeListByCategoryIds(
    categoryIds: Array<string>,
    query: DocumentNode = GraphqlImporter.getWorkTypeListByCategoryIds,
    operation?: string
  ): Promise<Response<Array<WorkTypeCategoryRelationResponse>>> {
    return commonRequestApi<Array<WorkTypeCategoryRelationResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { categoryIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据工种id集合批量获取工种信息
   * @param query 查询 graphql 语法文档
   * @param ids 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getWorkTypeListByIds(
    ids: Array<string>,
    query: DocumentNode = GraphqlImporter.getWorkTypeListByIds,
    operation?: string
  ): Promise<Response<Array<WorkTypeResponse>>> {
    return commonRequestApi<Array<WorkTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { ids },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取门户课程分类下的所有工种
   * @param containsChildren 代表是否包含其子分类对应的工种,false的话就是该分类直接对应的工种
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getWorkTypeListByPortalCourseCategoryId(
    params: { portalCourseCategoryId?: string; containsChildren: boolean },
    query: DocumentNode = GraphqlImporter.getWorkTypeListByPortalCourseCategoryId,
    operation?: string
  ): Promise<Response<Array<WorkTypeResponse>>> {
    return commonRequestApi<Array<WorkTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 根据门户课程分类id集合批量获取对应的工种信息,该查询不会递归到子分类,就是以传入的当前分类id直接关联的工种信息
   * @param query 查询 graphql 语法文档
   * @param portalCourseCategoryIds 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getWorkTypeListByPortalCourseCategoryIds(
    portalCourseCategoryIds: Array<string>,
    query: DocumentNode = GraphqlImporter.getWorkTypeListByPortalCourseCategoryIds,
    operation?: string
  ): Promise<Response<Array<PortalCourseCategoryRelationWorkTypeResponse>>> {
    return commonRequestApi<Array<PortalCourseCategoryRelationWorkTypeResponse>>(
      SERVER_URL,
      {
        query: query,
        variables: { portalCourseCategoryIds },
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 获取工种分页
   * @param query 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getWorkTypePage(
    params: { page?: Page; query?: WorkTypeQueryDTO },
    query: DocumentNode = GraphqlImporter.getWorkTypePage,
    operation?: string
  ): Promise<Response<WorkTypeResponsePage>> {
    return commonRequestApi<WorkTypeResponsePage>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 工种下移指定偏移量
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async moveDown(
    params: { id?: string; offset: number },
    mutate: DocumentNode = GraphqlImporter.moveDown,
    operation?: string
  ): Promise<Response<WorkTypeResponse>> {
    return commonRequestApi<WorkTypeResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**   * 工种上移指定偏移量
   * @param mutate 查询 graphql 语法文档
   * @param params 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async moveUp(
    params: { id?: string; offset: number },
    mutate: DocumentNode = GraphqlImporter.moveUp,
    operation?: string
  ): Promise<Response<WorkTypeResponse>> {
    return commonRequestApi<WorkTypeResponse>(
      SERVER_URL,
      {
        query: mutate,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }
}

export default new DataGateway()
