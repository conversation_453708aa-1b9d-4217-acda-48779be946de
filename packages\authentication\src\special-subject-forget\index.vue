<template>
  <div class="m-login-wrap">
    <div class="wrap-bd m-forget">
      <el-button type="primary" size="mini" plain @click="goLogin"
        ><i class="iconfont icon-lsh-return f-mr5"></i>返回登录</el-button
      >
      <!--第一步-->
      <el-steps :active="active" align-center class="m-steps f-ptb50 f-mt30">
        <el-step title="验证身份"></el-step>
        <el-step title="设置新密码"></el-step>
        <el-step title="完成"></el-step>
      </el-steps>
      <el-row type="flex" justify="center">
        <el-col :span="8">
          <el-form
            ref="checkUserRef"
            :model="checkUser"
            :rules="checkUserRules"
            label-width="auto"
            class="m-form f-mt30"
            v-if="checkStatus"
          >
            <el-form-item label="姓名：" prop="userName">
              <el-input v-model="checkUser.userName" clearable placeholder="请输入姓名" />
            </el-form-item>
            <el-form-item label="帐号：" prop="account">
              <el-input v-model="checkUser.account" clearable placeholder="请输入帐号/手机号" />
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button type="primary" @click="checkNext">下一步</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <el-row type="flex" justify="center" v-if="showCheckPhone">
        <el-col :span="10">
          <el-form
            ref="checkPhoneRef"
            :model="checkPhone"
            :rules="checkPhoneRules"
            label-width="auto"
            class="m-form f-mt30"
          >
            <el-form-item label=" " class="is-text">
              <el-alert type="warning" show-icon :closable="false" class="m-alert">
                重置密码需要短信验证您的安全手机
              </el-alert>
              <p class="f-mt15">
                点击获取验证码，短信将发送至安全手机 <span class="f-fb">{{ phoneNumber }}</span>
              </p>
            </el-form-item>
            <el-form-item label="图形验证码：" prop="captcha">
              <div class="f-flex">
                <el-input v-model="checkPhone.captcha" clearable placeholder="请输入图形验证码" class="f-flex-sub" />
                <div class="code">
                  <img :src="validateCodePic" @click="refreshValidateCodePic" title="看不清，点击刷新" />
                </div>
              </div>
            </el-form-item>
            <el-form-item label="短信校验码：" prop="smsCode">
              <div class="f-flex">
                <el-input v-model="checkPhone.smsCode" clearable placeholder="请输入短信校验码" class="f-flex-sub" />
                <div class="code">
                  <el-button type="primary" plain v-if="sending" :disabled="isCaptchaValid" @click="getPhoneCaptcha"
                    >获取短信验证码</el-button
                  >
                  <el-button type="info" plain disabled v-if="!sending">重新获取（{{ countTime }}s）</el-button>
                </div>
              </div>
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button type="primary" :disabled="isCaptchaValid" @click="nextTwo()">下一步</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <!--第二步-->

      <el-row type="flex" justify="center" v-if="active == 2">
        <el-col :span="11">
          <el-form
            ref="checkPassworRef"
            :model="checkPassword"
            :rules="checkPasswordRules"
            label-width="auto"
            class="m-form f-mt30"
          >
            <el-form-item label="密码：" prop="password">
              <el-input
                v-model="checkPassword.password"
                clearable
                show-password
                placeholder="请输入8~18位由数字、字母或符号组成的密码"
              />
              <password-strength :password="checkPassword.password"></password-strength>
              <!--密码安全判断-->
            </el-form-item>
            <el-form-item label="确认密码：" prop="againPassword">
              <el-input clearable show-password placeholder="请再次输入密码" v-model="checkPassword.againPassword" />
            </el-form-item>
            <el-form-item class="m-btn-bar">
              <el-button type="primary" @click="nextStep">下一步</el-button>
            </el-form-item>
          </el-form>
        </el-col>
      </el-row>
      <!--第三步-->

      <el-result icon="success" title="新密码设置成功，请前往登录！" v-if="active == 3">
        <template slot="extra">
          <el-button type="primary" size="medium" @click="goLogin">立即前往</el-button>
        </template>
      </el-result>
    </div>
  </div>
</template>

<script lang="ts">
  import AccountModule from '@api/service/common/account/AccountModule'
  import Authentication from '@api/service/common/authentication/Authentication'
  import { RoleTypeEnum } from '@api/service/common/authentication/interfaces/LoginParams'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
  import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
  import { BusinessTypeEnum } from '@hbfe-biz/biz-authentication/dist/enums/BusinessTypeEnum'
  import { CaptchaApplyRequest, SmsCodeApplyRequest } from '@hbfe-ms/ms-basicdata-domain-gateway'
  import {
    CurrentAccountChangePasswordCauseForgetRequest,
    ValidIdentityRequest
  } from '@hbfe-ms/ms-basicdata-domain-gateway/src'
  import passwordStrength from '@hbfe/jxjy-admin-authentication/src/forget/password-strength.vue'
  import { Component, Ref, Vue } from 'vue-property-decorator'

  @Component({
    components: {
      passwordStrength
    }
  })
  export default class extends Vue {
    @Ref('checkPassworRef') checkPassworRef: any
    @Ref('checkUserRef') checkUserRef: any
    @Ref('checkPhoneRef') checkPhoneRef: any
    $authentication: Authentication
    // 是否开启过分销增值能力服务
    isHadFxAbility = CapabilityServiceConfig.fxCapabilityEnable

    step = 1
    imageValidateCode = ''
    checkPhone = {
      captcha: '',
      smsCode: ''
    }
    checkUserRules = {
      account: {
        required: true,
        message: '请输入账号',
        trigger: 'blur'
      },
      userName: {
        required: true,
        message: '请输入姓名',
        trigger: 'blur'
      },
      type: {
        required: true,
        message: '请选择账号类型',
        trigger: 'change'
      }
    }

    checkUser = {
      account: '',
      userName: '',
      type: ''
    }
    phoneNumber = ''
    smsValidateCode = ''
    againPassword = ''
    // password = ''
    success = false
    showPassword = true
    hasPhoneNumber = true
    phoneNumbe = ''
    // 验证码发送
    sending = true
    checkPassword = {
      password: '',
      againPassword: ''
    }
    checkPhoneRules = {
      captcha: [
        {
          // 以大写小写字母或者数字组合的4位
          regexp: /^[a-zA-z0-9]{4}$/,
          validator: async (rule: any, value: any, callback: any) => {
            try {
              if (value && rule.regexp.test(value)) {
                const res = await this.$authentication.verify.msValidateCaptcha(value)
                console.log('这是图形验证码成功', res)
                if (res.status.code == 200 && res.data.code === 200) {
                  this.change(false)
                  callback()
                } else {
                  await this.refreshValidateCodePic()
                  this.change(true)
                  callback(new Error('请输入正确图形验证码'))
                }
              } else {
                this.change(true)
                callback(new Error('验证码为4位数'))
              }
            } catch (e) {
              console.log(e)
              callback(new Error('请输入正确图形验证码'))
              await this.refreshValidateCodePic()
              this.change(true)
            }
          },
          required: true,
          trigger: 'change'
        },
        { required: true, message: '请输入图形验证码', trigger: 'blur' }
      ],
      smsCode: {
        required: true,
        message: '请输入正确短信验证码',
        trigger: 'blur'
      }
    }
    checkPasswordRules = {
      password: [
        { required: true, message: '请输入8~18位由数字、字母或符号组成的密码', trigger: 'blur' },
        {
          message: '请输入8~18位由数字、字母或符号组成的密码',
          max: 18,
          min: 8,
          validator: this.validatePassword,
          trigger: 'blur'
        }
      ],
      againPassword: {
        required: true,
        max: 18,
        min: 8,
        validator: this.validateRepeatPassword,
        trigger: 'blur'
      }
    }
    // 预计倒计时时间
    count = 60
    // 发送后倒计时计时
    countTime = 60
    //图形验证码报错后请求次数
    imgIndex = 5
    checkStatus = true
    showCheckPhone = false
    account = ''
    validateCodePic = ''
    userName = ''
    AccountModule = AccountModule.mutationFactory.getMutationBizAccount()
    active = 1
    isCaptchaValid = true
    form = {
      name: '',
      name1: '周一至周五 (09:00 - 12:00 14:00 - 17:30)',
      region: '',
      date1: '',
      date2: '',
      delivery: false,

      resource: '',
      desc: ''
    }
    /**
     * 密码验证,必须数字和字母，特殊符号可有可无
     */
    validatePassword(rule: any, value: any, callback: any) {
      const reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{8,18}$')
      if (value === '') {
        callback(new Error('请输入密码'))
      } else {
        reg.test(value) ? callback() : callback(new Error('请输入8~18位由数字、字母或符号组成的密码'))
      }
    }
    /**
     * / 确定密码验证
     */
    validateRepeatPassword(rule: any, value: any, callback: any) {
      const reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{8,18}$')
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value !== this.checkPassword.password) {
        callback(new Error('两次输入密码不一致'))
      } else {
        reg.test(value) ? callback() : callback(new Error('请输入8~18位由数字、字母或符号组成的密码'))
      }
    }

    back() {
      this.$router.back()
    }
    // async created() {
    // }
    /**
     * 获取图片验证码
     */
    async refreshValidateCodePic() {
      try {
        const params = new CaptchaApplyRequest()
        params.token = this.$authentication.verify.validIdentityToken
        params.businessType = BusinessTypeEnum.forgot_password
        const res = await this.$authentication.verify.applyCaptcha(params)
        console.log(res, '这是获取图形验证码')
        if (res.status.code == 200 && res.data.captcha) {
          this.validateCodePic = `data:image/jpeg;base64,${res.data.captcha}`
          this.imgIndex = 5
        } else {
          this.catchImg(res)
        }
      } catch (e) {
        this.catchImg(e)
      }
    }
    async catchImg(e: any) {
      this.imgIndex--
      if (this.imgIndex > 0) {
        await this.refreshValidateCodePic()
      } else {
        console.log(e, '连接失败')
        this.$message.warning('连接失败，请刷新重试')
        this.imgIndex = 5
      }
    }
    change(type: boolean) {
      this.isCaptchaValid = type
    }
    async checkNext() {
      try {
        await this.checkUserRef.validate()
        const params = new ValidIdentityRequest()
        params.token = ConfigCenterModule.getFrontendApplication(frontendApplication.specialAdminForgotPasswordToken)
        params.name = this.checkUser.userName
        params.identity = this.checkUser.account
        params.businessType = BusinessTypeEnum.forgot_password
        // 如果是分销商走validIdentityForFxs接口
        const res =
          this.checkUser.type === RoleTypeEnum.DISTRIBUTOR
            ? await this.$authentication.verify.validIdentityForFxs(params)
            : await this.$authentication.verify.validIdentity(params)
        console.log(res, '身份验证')
        if (res.data.code !== 200) {
          console.error('身份验证失败', res)
          return this.errerMassage(res.data.code)
        }
        if (res.status.code == 200) {
          // const reslut = await this.$authentication.loadStudentRetrievePasswordBasicValidationData()
          // console.log(reslut)
          this.phoneNumber = res.data.boundDesensitizationPhone
          this.showCheckPhone = true
          this.checkStatus = false
          await this.refreshValidateCodePic()
        } else {
          this.$message.warning((res as any).message)
        }
      } catch (e) {
        console.log(e)
      }
    }
    // 报错信息
    errerMassage(code: number) {
      switch (code) {
        case 400:
          this.$message.warning('错误的请求')
          break
        case 408:
          this.$message.warning('验证码过期')
          break
        case 500:
          this.$message.warning('验证码不匹配')
          break
        case 501:
          this.$message.warning('手机号或账号不存在')
          break
        case 502:
          this.$message.warning('手机号或账号异常')
          break
        case 503:
          this.$message.warning('姓名和账号不匹配')
          break
        case 504:
          this.$message.warning('姓名不存在')
          break
        case 506:
          this.$message.warning('可用账户不唯一')
          break
        case 509:
          this.$message.warning('未绑定手机号')
          break
        default:
          break
      }
    }
    async nextTwo() {
      try {
        await this.checkPhoneRef.validateField('smsCode')
        if (!this.checkPhone.smsCode) return
        const smsCode = this.checkPhone.smsCode
        const res = await this.$authentication.verify.msValidSmsCode(smsCode, this.phoneNumber)

        if (res.data.code !== 200) {
          return this.errerMassage(res.data.code)
        }
        if (res.status.code == 200) {
          this.active = 2
          this.showCheckPhone = false
          this.showPassword = true
        } else {
          this.$message.warning(res.status.errors[0].message)
        }
      } catch (e) {
        console.log(e)
      }
    }
    async nextStep() {
      try {
        await this.checkPassworRef.validate()
        const params = new CurrentAccountChangePasswordCauseForgetRequest()
        params.token = this.$authentication.verify.shortMessageCaptchaToken
        params.password = this.checkPassword.password
        const res = await this.$authentication.account.forgetPassword(params)
        if (res.data.code !== 200) {
          return this.errerMassage(res.data.code)
        }
        if (res.status.code == 200) {
          this.active = 3
          this.showPassword = false
        } else {
          this.$message.warning(res.status.errors[0].message)
        }
      } catch (e) {
        console.log(e)
      }
    }
    // checkout(step: number) {
    //   const reg = new RegExp('^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z\\W]{6,18}$') // 验证密码的正则是否符合的正则
    //   if (!this.userName) {
    //     console.log('请输入姓名')
    //     return false
    //   } else if (!this.account) {
    //     console.log('请输入登录帐号')
    //     return false
    //   }
    //   if (step === 2) {
    //     if (!this.imageValidateCode || this.imageValidateCode.length !== 4) {
    //       console.log('请输入4位验证码')
    //       return false
    //     } else if (!this.smsValidateCode) {
    //       console.log('请输入验证码')
    //       return false
    //     }
    //   }
    //   if (step === 3) {
    //     if (!this.password || !reg.test(this.password)) {
    //       console.log('请输入6~18位由数字、字母或符号组成的密码')
    //       return false
    //     } else if (!this.againPassword) {
    //       console.log('请再次输入密码')
    //       return false
    //     } else if (this.againPassword !== this.password) {
    //       console.log('两次输入的密码不一致')
    //       return false
    //     }
    //   }
    //   return true
    // }

    // 发送手机短信验证码
    async getPhoneCaptcha() {
      this.sending = false
      const params = new SmsCodeApplyRequest()
      params.phone = this.phoneNumber
      params.token = this.$authentication.verify.captchaToken
      params.businessType = BusinessTypeEnum.forgot_password
      const res = await this.$authentication.verify.msSendSmsCode(params)
      if (res.data.code != 200) {
        this.sending = true
        await this.refreshValidateCodePic()
      }
      if (res.status.code == 200 && res.status.isSuccess()) {
        this.countDown()
        this.$message.success('短信验证码已发送至手机，请查收！')
      }
      if (res.data.code === 409) {
        this.$message.error('一个手机号一天只能接收4条短信，今日次数已用完，请隔天再试。')
      } else if (res.data.code === 410) {
        this.$message.error('发送验证码失败')
      } else if (res.data.code === 701) {
        this.$message.error('请重新验证图形验证码后，再次获取短信验证码')
      } else if (res.data.code === 515) {
        this.$message.error('不期望的手机号(与token元数据中的手机号不匹配或与上下文中登录账户的手机号不匹配)')
      } else if (res.data.code === 400) {
        this.$message.error('token无效')
      } else if (res.data.code === 509) {
        this.$message.error('未绑定手机号')
      } else if (res.data.code === 514) {
        this.$message.error('token中未携带手机号')
      }
    }
    // 验证码倒计时
    countDown() {
      let timer = 60
      const timers = setInterval(() => {
        if (timer === 0) {
          clearInterval(timers)
          this.sending = true
          this.countTime = this.count
        } else {
          timer--
          this.countTime = timer
        }
      }, 1000)
    }
    /**
     * 回到专题登录页
     */
    goLogin() {
      this.$router.push('/specialSubjectLogin')
    }
  }
</script>
