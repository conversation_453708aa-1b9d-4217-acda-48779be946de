import { DateRange } from '../enums/enums'
import moment from 'moment'

class AnswerRecordUtils {
  public static getStartTime(currentDate: Date, range: DateRange, customStartDate: Date, customEndDate: Date) {
    if (customEndDate != null && customEndDate === null) {
      console.log('!!')
    }
    switch (range) {
      case DateRange.TODAY:
        return undefined
      case DateRange.YESTERDAY:
        return undefined
        break
      case DateRange.WITHIN7DAY:
        return moment(currentDate.getTime())
          .add(-6, 'd')
          .format('YYYY-MM-DD 00:00:00')
        break
      case DateRange.WITHIN15DAY:
        return moment(currentDate.getTime())
          .add(-14, 'd')
          .format('YYYY-MM-DD 00:00:00')
        break
      case DateRange.WITHIN30DAY:
        return moment(currentDate.getTime())
          .add(-29, 'd')
          .format('YYYY-MM-DD 00:00:00')
        break
      case DateRange.CUSTOM:
        if (customStartDate) {
          return moment(customStartDate.getTime()).format('YYYY-MM-DD 00:00:00')
        }
        return undefined
        break
    }
  }

  public static getEndTime(currentDate: Date, range: DateRange, customStartDate: Date, customEndDate: Date) {
    switch (range) {
      case DateRange.TODAY:
        return moment(currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.YESTERDAY:
        return moment(currentDate.getTime())
          .add(-1, 'd')
          .format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.WITHIN7DAY:
        return moment(currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.WITHIN15DAY:
        return moment(currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.WITHIN30DAY:
        return moment(currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.CUSTOM:
        return moment(customEndDate?.getTime() || currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
        break
    }
  }

  public static getTrendQueryStartTime(
    currentDate: Date,
    range: DateRange,
    customStartDate: Date,
    customEndDate: Date
  ) {
    switch (range) {
      case DateRange.TODAY:
        // 趋势今天取15天前
        return moment(currentDate.getTime())
          .add(-14, 'd')
          .format('YYYY-MM-DD 00:00:00')
      case DateRange.YESTERDAY:
        return moment(currentDate.getTime())
          .add(-1, 'd')
          .format('YYYY-MM-DD 00:00:00')
        break
      case DateRange.WITHIN7DAY:
        return moment(currentDate.getTime())
          .add(-6, 'd')
          .format('YYYY-MM-DD 00:00:00')
        break
      case DateRange.WITHIN15DAY:
        return moment(currentDate.getTime())
          .add(-14, 'd')
          .format('YYYY-MM-DD 00:00:00')
        break
      case DateRange.WITHIN30DAY:
        return moment(currentDate.getTime())
          .add(-29, 'd')
          .format('YYYY-MM-DD 00:00:00')
        break
      case DateRange.CUSTOM:
        if (customStartDate) {
          const days = moment(customEndDate.getTime()).diff(
            moment(customStartDate?.getTime() || currentDate.getTime()),
            'days'
          )
          if (days > 29) {
            return moment(customEndDate.getTime())
              .add(-29, 'd')
              .format('YYYY-MM-DD 00:00:00')
          } else {
            return moment(customStartDate.getTime()).format('YYYY-MM-DD 00:00:00')
          }
        } else {
          return undefined
        }
        break
    }
  }

  public static getTrendQueryEndTime(currentDate: Date, range: DateRange, customStartDate: Date, customEndDate: Date) {
    switch (range) {
      case DateRange.TODAY:
        return moment(currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.YESTERDAY:
        return moment(currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.WITHIN7DAY:
        return moment(currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.WITHIN15DAY:
        return moment(currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.WITHIN30DAY:
        return moment(currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.CUSTOM:
        return moment(customEndDate?.getTime() || currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
        break
    }
  }

  public static getToCompareQueryEndTime(
    currentDate: Date,
    range: DateRange,
    customStartDate: Date,
    customEndDate: Date
  ) {
    let days
    switch (range) {
      case DateRange.TODAY:
        return moment(currentDate.getTime())
          .add(-6, 'd')
          .format('YYYY-MM-DD 23:59:59')
      case DateRange.YESTERDAY:
        return moment(currentDate.getTime()).format('YYYY-MM-DD 23:59:59')
      case DateRange.WITHIN7DAY:
        return moment(currentDate.getTime())
          .add(-6, 'd')
          .format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.WITHIN15DAY:
        return moment(currentDate.getTime())
          .add(-14, 'd')
          .format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.WITHIN30DAY:
        return moment(currentDate.getTime())
          .add(-29, 'd')
          .format('YYYY-MM-DD 23:59:59')
        break
      case DateRange.CUSTOM:
        days = moment(customEndDate.getTime()).diff(moment(customStartDate?.getTime() || currentDate.getTime()), 'days')
        console.log('自定义日期相差' + days + '天')
        if (days > 29) {
          return moment(customEndDate.getTime())
            .add(-29, 'd')
            .format('YYYY-MM-DD 23:59:59')
        } else {
          return moment(customStartDate.getTime()).format('YYYY-MM-DD 23:59:59')
        }
        break
    }
    return undefined
  }

  public static getTrendQueryTimes(currentDate: Date, range: DateRange, customStartDate: Date, customEndDate: Date) {
    let times
    switch (range) {
      case DateRange.TODAY:
        return 15
        break
      case DateRange.YESTERDAY:
        return 2
        break
      case DateRange.WITHIN7DAY:
        return 7
        break
      case DateRange.WITHIN15DAY:
        return 15
        break
      case DateRange.WITHIN30DAY:
        return 30
        break
      case DateRange.CUSTOM:
        if (customStartDate) {
          times = moment(customEndDate?.getTime() || currentDate.getTime()).diff(
            moment(customStartDate.getTime()),
            'days'
          )
        } else {
          return 30
        }

        return times > 30 ? 30 : times
        break
    }
    return -1
  }
}

export default AnswerRecordUtils
