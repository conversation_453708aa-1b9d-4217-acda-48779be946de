import * as GraphqlImporter from './graphql-importer'
import commonRequestApi from '../../request'
import { Response } from '@hbfe/common'
import { DocumentNode } from 'graphql'
import { UnAuthorize } from '@api/Secure'

const microServiceName = 'ms-examination-v1'
// 请求地址路径
export const SERVER_URL = `/gql/${microServiceName}`

// 是否微服务
const isMicroService = true

// 是否是差异化网关
const isDiffService = false

const msType = ''

// 服务名称，未必等于 schema 名称
const schemaName = 'ms-examination-v1'

// 请求配置项
export const requestConfig = {
  isMicroService,
  isDiffService,
  schemaName,
  microServiceName,
  msType
}

// 枚举

// 类

/**
 * 作废学员考试答卷请求
<AUTHOR>
 */
export class InvalidStudentExamAnswerPaperRequest {
  /**
   * 学员考试id
   */
  studentExamId?: string
  /**
   * 答卷id集合
   */
  answerPaperIds?: Array<string>
}

/**
 * 学员考试自动作答请求
<AUTHOR>
 */
export class StudentExaminationAutoAnswerRequest {
  /**
   * 学员学习token
   */
  studentToken: string
  /**
   * 合格分
   */
  qualifiedScore: number
}

class DataGateway {
  async _commonQuery<T>(query: DocumentNode, params?: unknown, operation?: string): Promise<Response<T>> {
    return commonRequestApi<T>(
      SERVER_URL,
      {
        query: query,
        variables: params,
        operation: operation
      },
      requestConfig
    )
  }

  /**
   * @param query 查询 graphql 语法文档
   * @param undefined 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  @UnAuthorize
  async getServiceTime(
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getServiceTime,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: undefined,
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: true
      })
    )
  }

  /**   * 学员考试id查询
   * @param token 作答token
   * @return
   * @param query 查询 graphql 语法文档
   * @param token 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async getSurplusExamDegree(
    token: string,
    serviceCapability = '',
    query: DocumentNode = GraphqlImporter.getSurplusExamDegree,
    operation?: string
  ): Promise<Response<number>> {
    return commonRequestApi<number>(
      SERVER_URL,
      {
        query: query,
        variables: { token },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 申请考试
   * @param studentToken 学员token
   * @return 考试token
   * @param mutate 查询 graphql 语法文档
   * @param studentToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async applyExam(
    studentToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.applyExam,
    operation?: string
  ): Promise<Response<string>> {
    return commonRequestApi<string>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 考试一键作答
   * @param request 学员考试自动作答请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async autoExam(
    request: StudentExaminationAutoAnswerRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.autoExam,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 删除考试
   * @param examId 考试id[必填]
   * @param mutate 查询 graphql 语法文档
   * @param examId 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async deleteExam(
    examId: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.deleteExam,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { examId },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 作废学员方案下全部答卷
   * @param studentReLearnToken 学员重学token
   * @param mutate 查询 graphql 语法文档
   * @param studentReLearnToken 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async invalidSchemeExamAnswerPapers(
    studentReLearnToken: string,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.invalidSchemeExamAnswerPapers,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { studentReLearnToken },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }

  /**   * 作废学员考试答卷
   * @param request 作废学员考试答卷请求
   * @param mutate 查询 graphql 语法文档
   * @param request 参数
   * @param operation graphql 语法文档需要调用的方法
   */
  async invalidStudentExamAnswerPaper(
    request: InvalidStudentExamAnswerPaperRequest,
    serviceCapability = '',
    mutate: DocumentNode = GraphqlImporter.invalidStudentExamAnswerPaper,
    operation?: string
  ): Promise<Response<void>> {
    return commonRequestApi<void>(
      SERVER_URL,
      {
        query: mutate,
        variables: { request },
        operation: operation
      },
      Object.assign(requestConfig, {
        returnTypeCryptoForGraphql: false,
        returnTypeNoCryptoForGraphql: false,
        serviceCapability: serviceCapability,
        isUnAuthorize: false
      })
    )
  }
}

export default new DataGateway()
