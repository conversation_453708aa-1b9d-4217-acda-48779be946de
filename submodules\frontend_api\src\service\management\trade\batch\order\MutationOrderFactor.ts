import MutationBatchOrderPay from './mutation/MutationBatchOrderPay'
import MutationBatchOrderRefund from './mutation/MutationBatchOrderRefund'
import MutationBatchOrderSignUp from './mutation/MutationBatchOrderSignUp'
import MutationBatchOrderExport from '@api/service/management/trade/batch/order/mutation/MutationBatchOrderExport'

class MutationOrderFactor {
  /**
   * 批次单导出业务对象
   */
  get mutationBatchOrderExport() {
    return new MutationBatchOrderExport()
  }

  /**
   * 【集体报名退款订单】 退款业务
   */
  get mutationBatchOrderRefund() {
    return new MutationBatchOrderRefund()
  }

  /**
   * 【集体报名】 报名业务
   */
  get mutationBatchOrderSignUp() {
    return new MutationBatchOrderSignUp()
  }

  /**
   * 【集体报名】 付款业务
   */
  get mutationBatchOrderPay() {
    return new MutationBatchOrderPay()
  }
}
export default MutationOrderFactor
