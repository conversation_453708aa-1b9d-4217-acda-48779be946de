import ReportConfig from '@api/service/management/implement/ReportConfig'
import AttendanceConfig from '@api/service/management/implement/AttendanceConfig'
import LearningMaterialsConfig from '@api/service/management/implement/LearningMaterialsConfig'
import TrainClassDetailClassVo from '@api/service/management/train-class/query/vo/TrainClassDetailClassVo'

export default class ImplementConfig {
  /**
   * 方案id
   */
  schemeId: string = undefined

  /**
   * 方案配置
   */
  schemeConfig: TrainClassDetailClassVo = new TrainClassDetailClassVo()

  /**
   * 报道配置
   */
  reportConfig: ReportConfig = new ReportConfig()

  /**
   * 考勤配置
   */
  attendanceConfig: AttendanceConfig = new AttendanceConfig()

  /**
   * 学习资料配置
   */
  learningMaterialsConfig: LearningMaterialsConfig = new LearningMaterialsConfig()

  /**
   * @param schemeId 方案id
   */
  constructor(schemeId: string) {
    this.schemeId = schemeId
    this.reportConfig = new ReportConfig(schemeId)
    this.attendanceConfig = new AttendanceConfig(schemeId)
    this.learningMaterialsConfig = new LearningMaterialsConfig(schemeId)
  }

  /**
   * 查询配置
   */
  async getConfig() {
    await this.reportConfig.getReportConfig()
    await this.attendanceConfig.getAttendanceConfig()
    await this.learningMaterialsConfig.getLearningMaterialsConfig()
  }
}
