<template>
  <el-card shadow="never" class="m-card f-mb15">
    <!--条件查询-->
    <el-row :gutter="16" class="m-query">
      <el-form :inline="true" label-width="auto">
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item label="订单号">
            <el-input v-model="pageQueryParam.orderNoList" clearable placeholder="请输入订单号" />
          </el-form-item>
        </el-col>
        <el-col :sm="12" :md="8" :xl="6">
          <el-form-item>
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <!--表格-->
    <el-table stripe :data="pageData" max-height="500px" class="m-table" v-loading="query.loading">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="订单号" min-width="220" fixed="left">
        <template slot-scope="scope">
          {{ scope.row.associationId }}
          <p><el-tag type="success" size="mini" v-if="scope.row.saleChannel == SaleChannelEnum.topic">专题</el-tag></p>
        </template>
      </el-table-column>
      <el-table-column label="退款状态" min-width="130">
        <template slot-scope="scope">
          <el-badge
            is-dot
            :type="refundStatusMapType[scope.row.orderReturnStatus]"
            class="badge-status"
            v-if="refundStatusMapName[scope.row.orderReturnStatus]"
          >
            {{ refundStatusMapName[scope.row.orderReturnStatus] }}</el-badge
          >
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="付款金额(元)" width="140" align="right">
        <template slot-scope="scope">
          {{ scope.row.payAmount || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="开票金额(元)" width="140" align="right">
        <template slot-scope="scope">
          {{ scope.row.blueTotalAmount || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="购买人信息" min-width="240">
        <template slot-scope="scope">
          <p>姓名：{{ scope.row.name || '-' }}</p>
          <p>证件号：{{ scope.row.idCard || '-' }}</p>
          <p>手机号：{{ scope.row.phone || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="发票抬头" prop="title" min-width="300">
        <template slot-scope="scope">【{{ invoiceTitleMapType[scope.row.titleType] }}】{{ scope.row.title }}</template>
      </el-table-column>
      <el-table-column label="统一社会信用代码" prop="taxpayerNo" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.taxpayerNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="申请开票时间" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.applyForDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票状态" min-width="130">
        <template slot-scope="scope">
          <el-badge
            is-dot
            :type="invoiceStatusMapType[scope.row.invoiceStatus]"
            class="badge-status"
            v-if="invoiceStatusMapName[scope.row.invoiceStatus]"
          >
            {{ invoiceStatusMapName[scope.row.invoiceStatus] }}
          </el-badge>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="开票时间" min-width="180">
        <template slot-scope="scope">
          {{ scope.row.invoiceDate || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="发票号" min-width="120">
        <template slot-scope="scope">
          {{ scope.row.invoiceNo || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="手机号 / 邮箱" min-width="220">
        <template slot-scope="scope">
          {{ scope.row.phone || '-' }} /
          <p>{{ scope.row.contactEmail || '-' }}</p>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="140" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            v-if="showModifyBtn(scope.row)"
            @click="editInvoicePopup(scope.row.invoiceId)"
            >修改发票信息</el-button
          >
          <el-button v-else type="text" disabled size="mini">修改发票信息</el-button>
        </template>
      </el-table-column>
    </el-table>
    <!--分页-->
    <hb-pagination :page="page" v-bind="page"></hb-pagination>

    <!-- 修改发票信息弹窗 -->
    <edit-electronic-special-invoice-dialog :dialog-ctrl.sync="editInvoiceVisible" :invoice-id="invoiceId">
    </edit-electronic-special-invoice-dialog>
  </el-card>
</template>
<script lang="ts">
  import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
  import TradeModule from '@api/service/management/trade/TradeModule'
  import { UiPage, Query } from '@hbfe/common'
  import invoiceInformation from '@hbfe/jxjy-admin-customerService/src/personal/components/invoice-information.vue'
  import QueryInvoice from '@api/service/management/trade/single/invoice/query/QueryInvoice'
  import { OnlineInvoiceSortRequest } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import {
    InvoiceStatusEnum,
    OrderReturnStatusEnum,
    TitleTypeEnum
  } from '@api/service/management/trade/single/invoice/enum/InvoiceEnum'
  import QueryOffLinePageInvoiceParam from '@api/service/management/trade/single/invoice/query/vo/QueryOffLinePageInvoiceParam'
  import OffLinePageInvoiceVo from '@api/service/management/trade/single/invoice/query/vo/OffLinePageInvoiceResponseVo'
  import UserDetailVo from '@api/service/management/user/query/student/vo/UserDetailVo'
  import EditElectronicSpecialInvoiceDialog from '@hbfe/jxjy-admin-trade/src/invoice/personal/components/edit-electronic-special-invoice-dialog.vue'
  import { SaleChannelEnum } from '@api/service/common/enums/trade/SaleChannelType'

  @Component({
    components: {
      //   EditOfflineInvoiceDialog,
      EditElectronicSpecialInvoiceDialog,
      invoiceInformation
    }
  })
  export default class extends Vue {
    //身份证
    idCard = ''
    // 页面分页控件
    page: UiPage
    // 分页查询
    query: Query = new Query()
    //接口请求
    queryInvoiceRemote: QueryInvoice = TradeModule.singleTradeBatchFactor.invoiceFactor.queryInvoice
    //接口查询参数
    pageQueryParam: QueryOffLinePageInvoiceParam = new QueryOffLinePageInvoiceParam()
    //创建时间进行排序
    sort: Array<OnlineInvoiceSortRequest> = new Array<OnlineInvoiceSortRequest>()
    //订单发票列表
    pageData: Array<OffLinePageInvoiceVo> = new Array<OffLinePageInvoiceVo>()
    //当前选中的发票id
    invoiceId = ''
    // 订单来源枚举
    SaleChannelEnum = SaleChannelEnum
    // 用户id
    userId = ''
    //发票状态
    blueInvoiceStatus = [
      {
        name: '请选择发票状态',
        value: null
      },
      {
        name: '待开票',
        value: InvoiceStatusEnum.NOTPTOOPEN
      },
      {
        name: '开票中',
        value: InvoiceStatusEnum.OPENING
      },
      {
        name: '开票成功',
        value: InvoiceStatusEnum.OPEMSUCCESS
      },
      {
        name: '开票失败',
        value: InvoiceStatusEnum.OPENERROR
      }
    ]

    //开票状态
    invoiceStatusMapName = {
      [InvoiceStatusEnum.NOTPTOOPEN]: '待开票',
      [InvoiceStatusEnum.OPENING]: '开票中',
      [InvoiceStatusEnum.OPENERROR]: '开票失败',
      [InvoiceStatusEnum.OPEMSUCCESS]: '开票成功'
    }

    invoiceStatusMapType = {
      [InvoiceStatusEnum.NOTPTOOPEN]: 'info',
      [InvoiceStatusEnum.OPENING]: 'primary',
      [InvoiceStatusEnum.OPENERROR]: 'danger',
      [InvoiceStatusEnum.OPEMSUCCESS]: 'success'
    }

    //退款状态
    refundStatusMapName = {
      [OrderReturnStatusEnum.DIDNOTRETURN]: '未退款',
      [OrderReturnStatusEnum.RETURNSING]: '退款中',
      [OrderReturnStatusEnum.RETURNSUCCESS]: '退款成功'
    }

    refundStatusMapType = {
      [OrderReturnStatusEnum.DIDNOTRETURN]: 'info',
      [OrderReturnStatusEnum.RETURNSING]: 'primary',
      [OrderReturnStatusEnum.RETURNSUCCESS]: 'success'
    }

    invoiceTitleMapType = {
      [TitleTypeEnum.PERSONAL]: '个人',
      [TitleTypeEnum.UNIT]: '单位'
    }
    /**
     * 打开-弹窗标识
     */
    // editInvoiceDialog = false
    editInvoiceVisible = false
    constructor() {
      super()
      this.page = new UiPage(this.doQueryPage, this.doQueryPage)
    }
    @Prop({
      type: UserDetailVo,
      default: new UserDetailVo()
    })
    userData!: UserDetailVo
    @Watch('userData', {
      deep: true,
      immediate: true
    })
    async userDataChange({ userName, idCard, phone, userId, loginAccount }: UserDetailVo) {
      this.pageQueryParam.userName = userName
      this.pageQueryParam.idCard = idCard
      this.pageQueryParam.phone = phone
      this.userId = userId
      this.pageQueryParam.loginAccount = loginAccount
      if (userName || idCard || phone) {
        await this.search()
      } else {
        this.pageData = []
      }
    }
    @Watch('idCard')
    watchValue(val?: string) {
      this.pageQueryParam.idCard = this.idCard
    }

    /**
     * 查询发票分页
     */
    async doQueryPage() {
      if (!this.userId) {
        return
      }
      this.query.loading = true
      try {
        this.pageData =
          await TradeModule.singleTradeBatchFactor.invoiceFactor.queryOffLineInvoice.offLinePageElectVatspecialplaInvoiceInServicer(
            this.page,
            this.pageQueryParam
          )
      } catch (e) {
        console.log(e, '加载发票列表失败')
      } finally {
        this.query.loading = false
      }
    }
    async search() {
      this.page.pageNo = 1
      await this.doQueryPage()
    }
    reset() {
      this.page.pageNo = 1
      this.pageQueryParam = new QueryOffLinePageInvoiceParam()
    }
    /**
     * 修改发票信息
     */
    async editInvoicePopup(id: string) {
      this.invoiceId = id
      //   this.editInvoiceDialog = true
      this.editInvoiceVisible = true
    }

    // 展示修改按钮
    showModifyBtn(item: OffLinePageInvoiceVo) {
      // 开票中和开票成功不展示
      if (item.invoiceStatus == 1 || item.invoiceStatus == 2) return false
      // 冻结不展示
      if (item.invoiceFreezeStatus) return false
      return true
    }
  }
</script>
