import CreateOrderParamsCommon from '@api/service/customer/trade/single/mutation/customer-order/vo/create-order/CreateOrderParams'
import { InvoiceInfoRequest } from '@api/ms-gateway/ms-order-v1'
export default class CreateOrderParams extends CreateOrderParamsCommon {
  /**
   * 身份证
   */
  idCard?: string

  /**
   * 渠道商编号
   */
  channelVendorId?: string

  /**
   * 发票信息
   */
  invoiceInfo?: InvoiceInfoRequest = undefined

  toGeneralCreateOrderRequest() {
    const createOrderParams = new CreateOrderParams()
    const data = super.toGeneralCreateOrderRequest()
    Object.assign(createOrderParams, data)
    createOrderParams.idCard = this.idCard
    createOrderParams.channelVendorId = this.channelVendorId
    createOrderParams.invoiceInfo = this.invoiceInfo
    console.log(createOrderParams, 'createOrderParams')
    return createOrderParams
  }
}
