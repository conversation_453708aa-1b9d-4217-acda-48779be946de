<template>
  <el-card
    shadow="never"
    class="m-card f-mb15"
    v-if="$hasPermission('PickUp')"
    desc="查询自取点列表"
    actions="doQueryPage"
  >
    <el-button
      v-if="$hasPermission('add')"
      desc="添加自取点"
      actions="@WarningDialog,save"
      type="primary"
      icon="el-icon-plus"
      class="f-mb20"
      @click="openDialog('add')"
      >添加自取点</el-button
    >
    <!--表格-->
    <el-table stripe :data="tableData" max-height="500px" class="m-table" v-loading="loading">
      <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
      <el-table-column label="自取点名称" min-width="140" fixed="left">
        <template slot-scope="scope">{{ scope.row.name }}</template>
      </el-table-column>
      <el-table-column label="领取地点" min-width="300">
        <template slot-scope="scope">{{ scope.row.address }}</template>
      </el-table-column>
      <el-table-column label="领取时间" min-width="120">
        <template slot-scope="scope">{{ scope.row.openTakeTime }}</template>
      </el-table-column>
      <el-table-column label="备注" min-width="250">
        <template slot-scope="scope">{{ scope.row.remark }}</template>
      </el-table-column>
      <el-table-column label="状态" min-width="100">
        <template slot-scope="scope">
          <div v-if="!scope.row.status">
            <el-badge is-dot type="info" class="badge-status">停用</el-badge>
          </div>
          <div v-else>
            <el-badge is-dot type="success" class="badge-status">可用</el-badge>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" align="center" fixed="right">
        <template slot-scope="scope">
          <el-button
            v-if="$hasPermission('collectionEdit')"
            desc="修改自取点"
            actions="@WarningDialog,openDialog,save"
            type="text"
            size="mini"
            @click="openDialog('edit', scope.row)"
            >修改</el-button
          >
          <template v-if="$hasPermission('disuse')" desc="停用自取点" actions="@WarningDialog,disable">
            <el-button type="text" size="mini" v-if="scope.row.status" @click="blockUpClcik(1, scope.row)">
              停用
            </el-button>
          </template>
          <template v-if="$hasPermission('enable')" desc="启用自取点" actions="@WarningDialog,enable">
            <el-button type="text" size="mini" v-if="!scope.row.status" @click="blockUpClcik(2, scope.row)">
              启用
            </el-button>
          </template>
          <el-button
            v-if="$hasPermission('remove')"
            desc="删除自取点"
            actions="@WarningDialog,detele"
            type="text"
            size="mini"
            @click="blockUpClcik(3, scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <el-drawer :title="title" :visible.sync="isShow" size="700px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-row type="flex" justify="center">
          <el-col :span="22">
            <el-form ref="formRef" :model="takePlace.vo" :rules="rules" label-width="auto" class="m-form f-mt20">
              <el-form-item label="自取点名称：" prop="name">
                <el-input
                  v-model="takePlace.vo.name"
                  clearable
                  placeholder="请输入自取点名称，仅做为管理员命名用，学员不可见"
                />
              </el-form-item>
              <el-form-item label="领取地点：" prop="address">
                <el-input v-model="takePlace.vo.address" clearable placeholder="请输入自取点地址，学员可见" />
              </el-form-item>
              <el-form-item label="领取时间：" prop="openTakeTime">
                <el-input
                  v-model="takePlace.vo.openTakeTime"
                  clearable
                  placeholder="请输入开放领取时间，供学员在此时段前往领取"
                />
              </el-form-item>
              <el-form-item label="备注：" prop="remark">
                <el-input type="textarea" :rows="6" v-model="takePlace.vo.remark" placeholder="请输入备注信息" />
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="isShow = false">取消</el-button>
                <el-button type="primary" @click="save">确定</el-button>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </div>
    </el-drawer>
    <warning-dialog
      ref="WarningDialog"
      @disable="disable"
      @enable="enable"
      @detele="detele"
      :content="content"
    ></warning-dialog>
  </el-card>
</template>

<script lang="ts">
  import CreateTakePlaceVo from '@api/service/management/online-school-config/distribution-channels-config/mutation/vo/CreateTakePlaceVo'
  import TakePlaceDetailVo from '@api/service/management/online-school-config/distribution-channels-config/query/vo/TakePlaceDetailVo'
  import OnlineSchoolConfigModule from '@api/service/management/online-school-config/OnlineSchoolConfigModule'
  import { Component, Vue, Ref } from 'vue-property-decorator'
  import WarningDialog from '@hbfe/jxjy-admin-distributionChannel/src/components/dialog.vue'
  import MutationCreateTakePlace from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationCreateTakePlace'
  import MutationUpdateTakePlace from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationUpdateTakePlace'
  import MutationTakePlace from '@api/service/management/online-school-config/distribution-channels-config/mutation/MutationTakePlace'
  import UpdateTakePlaceVo from '@api/service/management/online-school-config/distribution-channels-config/mutation/vo/UpdateTakePlaceVo'

  @Component({
    components: { WarningDialog }
  })
  export default class extends Vue {
    @Ref('formRef') formRef: any
    @Ref('WarningDialog') WarningDialog: WarningDialog
    isShow = false // 是否显示抽屉
    content = '' // 弹窗内容
    checkedItem = new TakePlaceDetailVo() // 选中的item
    takePlace: MutationCreateTakePlace | MutationUpdateTakePlace = new MutationCreateTakePlace()
    rules = {
      name: [{ required: true, message: '请输入自取点名称', trigger: 'blur' }],
      address: [{ required: true, message: '请输入领取地点', trigger: 'blur' }],
      openTakeTime: [{ required: true, message: '请输入自领取时间', trigger: 'blur' }],
      remark: [{ required: true, message: '请输入备注', trigger: 'blur' }]
    }
    title = ''
    tableData: any = []
    loading = false

    async doQueryPage() {
      try {
        this.loading = true
        this.tableData = await OnlineSchoolConfigModule.queryDistributionChannelsConfigFactory.queryTakePlace.search()
      } catch (e) {
        this.$message.error('请求数据列表失败！')
      } finally {
        this.loading = false
      }
    }

    async openDialog(type: string, item: any) {
      this.isShow = true
      if (type === 'add') {
        this.title = '添加自取点'
        const takePlace = this.takePlace as MutationCreateTakePlace
        takePlace.vo = new CreateTakePlaceVo()
      } else if (type === 'edit') {
        this.title = '修改自取点'
        this.takePlace = await OnlineSchoolConfigModule.mutationDistributionChannelsConfigFactory.updateTakePlace(
          item.id
        )
      }
    }

    // 新增，修改自取点
    save() {
      this.formRef.validate(async (valid: any) => {
        if (valid) {
          if (this.title === '添加自取点') {
            const takePlace = this.takePlace as MutationCreateTakePlace
            const res = await takePlace.doCreate()
            if (res.isSuccess()) {
              this.isShow = false
              this.$message.success('添加成功')
              await this.doQueryPage()
            } else {
              this.$message.warning('添加失败')
            }
          } else {
            // const takePlace = this.takePlace as MutationUpdateTakePlace
            // const res = await takePlace.doUpdate()
            const takePlace = new MutationUpdateTakePlace(this.takePlace.vo as UpdateTakePlaceVo)
            const res = await takePlace.doUpdate()
            if (res.isSuccess()) {
              this.isShow = false
              this.$message.success('修改成功')
              await this.doQueryPage()
            } else {
              this.$message.warning('修改失败')
            }
          }
        }
      })
    }

    // 停用/启用事件
    blockUpClcik(type: number, item: any) {
      console.log(item)
      this.checkedItem = item
      if (type == 1) {
        this.content = '确认要停用自取点吗？'
        this.WarningDialog.type = 1
      }
      if (type == 2) {
        this.content = '确定要启用自取点吗？'
        this.WarningDialog.type = 2
      }
      if (type == 3) {
        if (item.status) {
          return this.$message.warning('请先停用')
        }
        this.content = '确定要删除自取点吗？'
        this.WarningDialog.type = 3
      }
      this.WarningDialog.isShowDialog()
    }

    // 生命周期
    async created() {
      this.doQueryPage()
    }

    // 停用
    async disable() {
      const disableTake = new MutationTakePlace(this.checkedItem.id)
      const doDisable = await disableTake.doDisable()
      console.log(doDisable, 'doDisable')
      if (doDisable.isSuccess()) {
        this.$message.success('停用成功')
        this.doQueryPage()
      } else {
        this.$message.warning('请求失败')
      }
      this.WarningDialog.isShowDialog()
    }

    //启用
    async enable() {
      const enableTake = new MutationTakePlace(this.checkedItem.id)
      const doEnable = await enableTake.doEnable()
      console.log(doEnable, 'doEnable')
      if (doEnable.isSuccess()) {
        this.$message.success('启用成功')
        this.doQueryPage()
      } else {
        this.$message.success('请求失败')
      }
      this.WarningDialog.isShowDialog()
    }

    // 删除
    async detele(state: number) {
      const deleteTake = new MutationTakePlace(this.checkedItem.id)
      const doRemove = await deleteTake.doRemove()
      console.log(doRemove, 'doRemove')
      if (doRemove.isSuccess()) {
        this.$message.success('删除成功')
        this.doQueryPage()
      } else {
        this.$message.success('请求失败')
      }
      this.WarningDialog.isShowDialog()
    }
  }
</script>
