<route-meta>
{
"isMenu": true,
"title": "地区学习统计",
"sort": 4,
"icon": "icon-tongjibaobiao"
}
</route-meta>
<template>
  <el-main v-if="$hasPermission('query')" desc="查询" actions="doSearch,@BizPortalSelectBiz,@DistributorSelect">
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
        <hb-search-wrapper @reset="resetQueryParam" class="m-query is-border-bottom">
          <el-form-item label="单位所在地">
            <!-- <biz-region-cascader
              v-model="locationUnit"
              :check-strictly="true"
              placeholder="请选择工作单位所在地区"
            ></biz-region-cascader> -->
            <!-- <biz-register-region
              v-model="locationUnit"
              :check-strictly="true"
              ref="regionValueRef"
              placeholder="请选择工作单位所在地区"
            >
            </biz-register-region> -->
            <!-- <biz-national-region
              ref="regionValueRef"
              v-model="locationUnit"
              :check-strictly="true"
              placeholder="请选择工作单位所在地区"
            ></biz-national-region> -->
            <biz-region-admin-cascader
              v-model="locationUnit"
              :check-strictly="true"
              placeholder="请选择工作单位所在地区"
            ></biz-region-admin-cascader>
          </el-form-item>

          <el-form-item label="年度">
            <biz-year-select
              placeholder="请选择培训年度"
              v-model="localSkuProperty.year"
              :multiple="true"
            ></biz-year-select>
          </el-form-item>

          <el-form-item label="地区">
            <!-- <biz-region-cascader
              v-model="localSkuProperty.region"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-region-cascader> -->
            <biz-national-region
              ref="regionValueRef"
              v-model="localSkuProperty.region"
              :check-strictly="true"
              placeholder="请选择地区"
            ></biz-national-region>
          </el-form-item>
          <el-form-item label="培训形式">
            <biz-training-mode-select v-model="localSkuProperty.trainingForm"></biz-training-mode-select>
          </el-form-item>
          <el-form-item label="培训方案类型">
            <biz-scheme-type v-model="schemeTypeInfo"></biz-scheme-type>
          </el-form-item>

          <el-form-item label="培训方案名称">
            <el-input clearable placeholder="请输入培训方案名称" v-model="schemeName" />
          </el-form-item>

          <el-form-item label="行业">
            <biz-industry-select
              v-model="localSkuProperty.industry"
              @clearIndustrySelect="handleClearIndustrySelect"
              @industryPropertyId="handleIndustryPropertyId"
              @industryInfos="handleIndustryInfos"
            ></biz-industry-select>
          </el-form-item>

          <el-form-item
            label="科目类型"
            v-if="
              skuVisible.subjectType &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-accounttype-select
              v-model="localSkuProperty.subjectType"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            >
            </biz-accounttype-select>
          </el-form-item>

          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.societyIndustryId &&
              localSkuProperty.industry === envConfig.societyIndustryId
            "
          >
            <biz-major-cascader
              v-model="localSkuProperty.societyTrainingMajor"
              placeholder="请选择培训专业"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-training-category-select
              placeholder="请选择培训类别"
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              @updateTrainingCategory="handleUpdateTrainingCategory"
            />
          </el-form-item>
          <el-form-item
            label="培训专业"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.constructionIndustryId &&
              localSkuProperty.industry === envConfig.constructionIndustryId
            "
          >
            <biz-major-select
              v-model="localSkuProperty.constructionTrainingMajor"
              placeholder="请选择培训专业"
              :industry-property-id="industryPropertyId"
              :training-category-id="trainingCategoryId"
            />
          </el-form-item>
          <!-- 工勤行业 -->
          <el-form-item
            label="技术等级"
            v-if="
              skuVisible.technicalGrade &&
              localSkuProperty.industry &&
              envConfig.workServiceId &&
              localSkuProperty.industry === envConfig.workServiceId
            "
          >
            <biz-technical-grade-select
              v-model="localSkuProperty.technicalGrade"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            ></biz-technical-grade-select>
          </el-form-item>
          <!-- 卫生行业 -->
          <el-form-item
            label="培训类别"
            v-if="
              skuVisible.trainingCategory &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-training-category-select
              placeholder="请选择培训类别"
              v-model="localSkuProperty.trainingCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
            />
          </el-form-item>
          <el-form-item
            label="培训对象"
            v-if="
              skuVisible.trainingObject &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-training-object-select
              placeholder="请选择培训对象"
              v-model="localSkuProperty.trainingObject"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              @updateTrainingCategory="handleWSUpdateTrainingObject"
            />
          </el-form-item>
          <el-form-item
            label="岗位类别"
            v-if="
              skuVisible.positionCategory &&
              localSkuProperty.industry &&
              envConfig.professionHealthIndustryId &&
              localSkuProperty.industry === envConfig.professionHealthIndustryId
            "
          >
            <biz-obj-category-select
              placeholder="请选择岗位类别"
              v-model="localSkuProperty.positionCategory"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              :training-object-id="trainingObjectId"
            />
          </el-form-item>
          <el-form-item
            label="学段"
            v-if="
              skuVisible.learningPhase &&
              localSkuProperty.industry &&
              envConfig.teacherIndustryId &&
              localSkuProperty.industry === envConfig.teacherIndustryId
            "
          >
            <biz-study-period
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              placeholder="请选择学段"
              v-model="localSkuProperty.learningPhase"
              @updateStudyPeriod="updateStudyPeriod"
            />
          </el-form-item>
          <el-form-item
            label="学科"
            v-if="
              skuVisible.discipline &&
              localSkuProperty.industry &&
              envConfig.teacherIndustryId &&
              localSkuProperty.industry === envConfig.teacherIndustryId
            "
          >
            <biz-subject
              :study-period-id="localSkuProperty.learningPhase"
              :industry-property-id="industryPropertyId"
              :industryId="localSkuProperty.industry"
              placeholder="请选择学科"
              v-model="localSkuProperty.discipline"
            />
          </el-form-item>
          <el-form-item label="报名时间">
            <double-date-picker
              :begin-create-time.sync="learningReportFormsRequest.learningRegister.registerTime.begin"
              :end-create-time.sync="learningReportFormsRequest.learningRegister.registerTime.end"
              beginTimePlaceholder="请选择报名成功时间"
              endTimePlaceholder="请选择报名成功时间"
              endDefaultTime="23:59:59"
            ></double-date-picker>
          </el-form-item>
          <el-form-item label="专题">
            <biz-special-select v-model="saleChannels" @input="changeSaleChannels"></biz-special-select>
          </el-form-item>
          <el-form-item label="专题名称" v-if="saleChannels || saleChannels == null">
            <biz-special-name v-model="learningReportFormsRequest.trainingChannelName"></biz-special-name>
          </el-form-item>
          <slot name="sale-channel" :localSkuProperty="localSkuProperty"></slot>
          <el-form-item label="分销商" v-if="isOpenFxService">
            <biz-distributor-select
              v-model="learningReportFormsRequest.learningRegister.distributorId"
              :name="distributorName"
            ></biz-distributor-select>
          </el-form-item>
          <el-form-item label="推广门户简称" v-if="isOpenFxService">
            <biz-portal-select
              :disabled="learningReportFormsRequest.notDistributionPortal"
              v-model="portalId"
              :name="promoteThePortalAlias"
            ></biz-portal-select>
          </el-form-item>
          <el-form-item v-if="isOpenFxService">
            <el-checkbox
              label="查看非门户推广数据"
              name="type"
              v-model="learningReportFormsRequest.notDistributionPortal"
            ></el-checkbox>
          </el-form-item>
          <!-- <el-form-item label="分销推广">
            <el-select v-model="localSkuProperty.learningPhase" placeholder="请选择是否为分销推广订单">
              <el-option value="" label="全部"></el-option>
              <el-option value="1" label="是"></el-option>
              <el-option value="2" label="否"></el-option>
            </el-select>
          </el-form-item> -->
          <template slot="actions">
            <el-button type="primary" @click="doSearch()">查询</el-button>
            <!-- <el-button @click="exportListData">导出列表数据</el-button> -->
            <el-button @click="exportDialog = true">导出列表数据</el-button>
          </template>
        </hb-search-wrapper>

        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert f-clear">
            <div class="f-fr f-csp f-flex f-align-center" @click="dialog8 = true">
              <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
            </div>
          </el-alert>
        </div>
        <!--表格-->

        <el-table
          stripe
          :data="tableData2"
          row-key="id"
          lazy
          border
          v-loading="trainSchemeQuery.loading"
          element-loading-text="加载中..."
          :load="load"
          :tree-props="{ children: 'children', hasChildren: 'leaf' }"
          max-height="400px"
          class="m-table is-statistical f-mt10"
          ref="schemeTable"
          :row-style="setRowStyle"
        >
          <el-table-column type="index" label="No." width="120" align="center" fixed="left">
            <template slot-scope="{ row, $index }">
              <div>{{ getIndex(row, $index) }}</div>
            </template>
          </el-table-column>
          <el-table-column label="工作单位所在地区" prop="name" min-width="150" fixed="left"></el-table-column>
          <el-table-column label="净报名" prop="netRegisterCount" min-width="90" align="right">
            <template slot-scope="scope">{{ scope.row.learningStatistic.netRegisterCount }}</template>
          </el-table-column>
          <el-table-column label="课程学习" header-align="center">
            <el-table-column label="未学习" prop="courseLearningStatistic.waitStudyCount" min-width="90" align="right">
              <template slot-scope="scope"
                >{{ scope.row.learningStatistic.courseLearningStatistic.waitStudyCount }}
              </template>
            </el-table-column>
            <el-table-column label="学习中" prop="courseLearningStatistic.studyingCount" min-width="90" align="right">
              <template slot-scope="scope"
                >{{ scope.row.learningStatistic.courseLearningStatistic.studyingCount }}
              </template>
            </el-table-column>
            <el-table-column
              label="已学完"
              prop="courseLearningStatistic.studyFinishCount"
              min-width="90"
              align="right"
            >
              <template slot-scope="scope"
                >{{ scope.row.learningStatistic.courseLearningStatistic.studyFinishCount }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="班级考试">
            <el-table-column
              label="已考试"
              prop="examLearningStatistic.committedExamCount"
              min-width="90"
              align="right"
            >
              <template slot-scope="scope"
                >{{ scope.row.learningStatistic.examLearningStatistic.committedExamCount }}
              </template>
            </el-table-column>
          </el-table-column>
          <el-table-column label="调研问卷" align="center">
            <el-table-column label="已提交" prop="questionnaireSubmitCount" min-width="90" align="center">
              <template slot-scope="scope">{{ scope.row.learningStatistic.questionnaireSubmitCount || 0 }}</template>
            </el-table-column>
            <el-table-column label="未提交" prop="questionnaireUnSubmitCount" min-width="90" align="center">
              <template slot-scope="scope">{{ scope.row.learningStatistic.questionnaireUnSubmitCount || 0 }}</template>
            </el-table-column>
          </el-table-column>
          <el-table-column v-if="showOffline" label="培训期别" align="center">
            <el-table-column label="已合格" prop="issueQualifiedCount" min-width="90" align="center">
              <template slot-scope="scope">{{ scope.row.learningStatistic.issueQualifiedCount || 0 }}</template>
            </el-table-column>
            <el-table-column label="未合格" prop="issueUnQualifiedCount" min-width="90" align="center">
              <template slot-scope="scope">{{ scope.row.learningStatistic.issueUnQualifiedCount || 0 }}</template>
            </el-table-column>
          </el-table-column>

          <el-table-column label="已合格" prop="qualifiedCount" min-width="90" align="right">
            <template slot-scope="scope">{{ scope.row.learningStatistic.qualifiedCount }}</template>
          </el-table-column>
          <el-table-column label="合格率" prop="qualifiedRate" min-width="90" align="right">
            <template slot-scope="scope"
              >{{
                (
                  (scope.row.learningStatistic.qualifiedCount /
                    dealQualified(scope.row.learningStatistic.netRegisterCount)) *
                  100
                ).toFixed(2)
              }}%
            </template>
          </el-table-column>
          <el-table-column label="操作" prop="operation" width="140" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button v-if="!isSpecialRow(scope.row)" type="text" size="mini" @click="toCollectDetails(scope.row)">
                学员学习明细
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-drawer
          title="提示"
          v-if="$hasPermission('export')"
          desc="导出"
          actions="confirmExportSort"
          :visible.sync="exportDialog"
          size="600px"
          custom-class="m-drawer"
        >
          <div class="drawer-bd">
            <el-form ref="form" :model="form" class="m-form f-mt20">
              <el-form-item label="请选择导出方式：">
                <el-radio-group v-model="exportSort">
                  <el-radio label="导出数据列表" :value="1">导出数据列表</el-radio>
                  <el-radio label="导出列表人员详细" :value="2">导出列表人员详细</el-radio>
                </el-radio-group>
              </el-form-item>
              <el-form-item class="m-btn-bar">
                <el-button @click="exportDialog = false">取消</el-button>
                <el-button type="primary" @click="confirmExportSort(exportSort)">确定</el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-drawer>
        <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-地区学习统计</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
          </div>
        </el-dialog>
        <el-dialog title="提示" :visible.sync="exportDetailSuccessVisible" width="450px" class="m-dialog">
          <div class="dialog-alert is-big">
            <i class="icon el-icon-success success"></i>
            <div class="txt">
              <p class="f-fb">导出成功，是否前往下载数据？</p>
              <p class="f-f13 f-mt5">下载入口：导出任务管理-地区学习统计导出列表人员详情</p>
            </div>
          </div>
          <div slot="footer">
            <el-button @click="exportDetailSuccessVisible = false">暂 不</el-button>
            <el-button type="primary" @click="goDownloadDetailPage">前往下载</el-button>
          </div>
        </el-dialog>
        <el-drawer title="统计口径说明" :visible.sync="dialog8" size="900px" custom-class="m-drawer">
          <div class="drawer-bd">
            <el-alert type="warning" show-icon :closable="false" class="m-alert">
              注：统计报名查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
            </el-alert>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">搜索条件说明</span>
              （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
            </p>
            <el-table stripe :data="searchConditions" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  {{ scope.row.field }}
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  {{ scope.row.description }}
                </template>
              </el-table-column>
            </el-table>
            <p class="f-mt20 f-mb10">
              <span class="f-fb f-f15">列表字段及详细说明</span>
              （列表下的数据显示受搜索条件的约束，统计单位：人次）
            </p>
            <el-table stripe :data="listFields" border class="m-table">
              <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
              <el-table-column label="字段" width="150">
                <template slot-scope="scope">
                  {{ scope.row.field }}
                </template>
              </el-table-column>
              <el-table-column label="详细说明" min-width="300">
                <template slot-scope="scope">
                  {{ scope.row.description }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-drawer>
        <!--分页-->
        <!-- <hb-pagination class="f-mt15 f-tr" :page="trainSchemePage" v-bind="trainSchemePage"> </hb-pagination> -->
      </el-card>
    </div>
  </el-main>
</template>

<script lang="ts">
  import DoubleDatePicker from '@hbfe/jxjy-admin-components/src//double-date-picker/index.vue'
  import NewRegionTreeVo from '@hbfe/jxjy-admin-common/src/models/NewRegionTreeVo'
  import SchemeSkuProperty from '@hbfe/jxjy-admin-common/src/models/sku'
  import { RegionResponse } from '@api/ms-gateway/ms-basicdata-query-front-gateway-backstage'
  import {
    LearningRegisterRequest,
    LearningReportFormsRequest,
    RegionRequest,
    UserPropertyRequest,
    UserRequest
  } from '@api/ms-gateway/ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage'
  import {
    CommoditySkuSortRequest,
    DateScopeRequest,
    RegionSkuPropertyRequest,
    RegionSkuPropertySearchRequest,
    SchemeRequest,
    SkuPropertyRequest
  } from '@api/ms-gateway/ms-trade-query-front-gateway-TradeQueryBackstage'
  import BasicDataDictionaryModule from '@api/service/common/basic-data-dictionary/BasicDataDictionaryModule'
  import QueryBusinessRegion from '@api/service/common/basic-data-dictionary/query/QueryBusinessRegion'
  import QueryPhysicalRegion from '@api/service/common/basic-data-dictionary/query/QueryPhysicalRegion'
  import IndustryPropertyCategoryVo from '@api/service/common/basic-data-dictionary/query/vo/IndustryPropertyCategoryVo'
  import CapabilityServiceConfig from '@api/service/common/capability-service-config/CapabilityServiceConfig'
  import SaleChannelType from '@api/service/common/enums/trade/SaleChannelType'
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'
  import QueryManagerRegionLearningList from '@api/service/management/statisticalReport/query/QueryManagerRegionLearningList'
  import { RegionLearningStatisticsResponseVo } from '@api/service/management/statisticalReport/query/vo/RegionLearningStatisticsResponseVo'
  import QueryTrainClassCommodityList from '@api/service/management/train-class/query/QueryTrainClassCommodityList'
  import TrainClassCommodityVo from '@api/service/management/train-class/query/vo/TrainClassCommodityVo'
  import UserModule from '@api/service/management/user/UserModule'
  import { Query, UiPage } from '@hbfe/common'
  import BizDistributorSelect from '@hbfe/fx-manage/src/components/biz/biz-distributor-select.vue'
  import BizPortalSelect from '@hbfe/fx-manage/src/components/biz/biz-portal-select.vue'
  import { cloneDeep } from 'lodash'
  import { Component, Ref, Vue } from 'vue-property-decorator'
  import QueryShowOffline from '@api/service/common/config/QueryShowOffline'

  class NewLearningReportFormsRequest extends LearningReportFormsRequest {
    //是否来源于专题
    subjectType = ''
    //专题名称
    subjectName = ''
  }

  @Component({
    components: {
      DoubleDatePicker,
      BizPortalSelect,
      BizDistributorSelect
    }
  })
  export default class extends Vue {
    @Ref('regionValueRef') regionValueRef: any
    @Ref('schemeTable') schemeTableRef: any
    dialog8 = false
    searchConditions = [
      {
        field: '方案属性',
        description:
          '选择不同的行业，可以查询不同的培训属性值。如建设行业方案属性：地区、培训年度、科目类型、培训类别、培训专业。属性值为空，显示“-”'
      },
      { field: '培训方案', description: '创建培训方案时定义的类型属性，如培训班-选课规则等' },
      { field: '方案类型', description: '选择全部已发布的培训班' },
      { field: '报名成功时间', description: '查看在某个开通时间内，各地区学员学习数据' }
    ]

    listFields = [
      {
        field: '方案名称',
        description: '培训方案名称，默认显示全部已发布的方案'
      },
      {
        field: '方案属性',
        description: '培训方案的属性值展示'
      },
      { field: '净报名', description: '统计截止到当前时间扣除退班后的实际有效的报名人次，净报名=未学习+学习中+已学完' },
      { field: '未学习', description: '统计截止到当前时间净开通人数中，班级学习进度=0的人' },
      { field: '学习中', description: '统计截止到当前时间净开通人数中，0<班级学习获得学时<要求学时或考核标准值的人' },
      {
        field: '已学完',
        description: '统计截止到当前时间净开通人数中，班级学习进度=100%或班级学习进度>=考核标准值的人'
      },
      {
        field: '已考试',
        description:
          '统计截止到当前时间净开通人数中，班级学习进度已达考核值已参加过考试的人（有提交试卷成功就计一次考试）'
      },
      { field: '已合格', description: '统计截止到当前时间净开通人数中，已达到考核要求的人' },
      { field: '合格率', description: '统计截止到当前时间净开通人数中的合格率，合格率=已合格/净报名' },
      { field: '已提交', description: '统计截止到当前时间净报名人数中，已提交所有要求填写（纳入考核）的问卷的人数。' },
      { field: '未提交', description: '统计截止到当前时间净报名人数中，未提交所有要求填写（纳入考核）的问卷的人数。' },
      { field: '培训期别已合格', description: '统计截止到当前时间净报名人数中，已满足培训期别考核的人' },
      { field: '培训期别未合格', description: '统计截止到当前时间净报名人数中，不满足培训期别考核的人' }
    ]
    tableData = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }, { field101: '5' }]
    // 分页参数 - 培训方案
    trainSchemePage: UiPage
    // 分页加载参数 - 培训方案
    trainSchemeQuery: Query = new Query()
    //导出成功弹窗
    exportSuccessVisible = false
    exportDetailSuccessVisible = false
    exportSort = 0
    exportDialog = false

    queryTrainClassReportList: QueryManagerRegionLearningList =
      StaticticalReportManagerModule.queryStaticticalReportFactory.getQueryManagerRegionLearningList()
    regionLearningStatisticsResponseVo: Array<RegionLearningStatisticsResponseVo> =
      new Array<RegionLearningStatisticsResponseVo>()
    regionLearningStatisticsResponseVoChildren: Array<RegionLearningStatisticsResponseVo> =
      new Array<RegionLearningStatisticsResponseVo>()
    learningReportFormsRequest: LearningReportFormsRequest = new LearningReportFormsRequest()
    learningReportFormsRequestChidren: LearningReportFormsRequest = new LearningReportFormsRequest()
    exportQueryParam: LearningReportFormsRequest = new LearningReportFormsRequest()
    // provinces: Array<NewRegionVo> = new Array<NewRegionVo>()
    // expanArea: Array<NewRegionVo> = new Array<NewRegionVo>()
    provinces: Array<NewRegionTreeVo> = new Array<NewRegionTreeVo>() // 初始省级地区
    expanArea: Array<NewRegionTreeVo> = new Array<NewRegionTreeVo>() //懒加载拓展地区
    queryRegionManagerRegionLearningList =
      StaticticalReportManagerModule.queryStaticticalReportFactory.getQueryRegionManagerRegionLearningList() //地区管理员获取列表数据口
    queryManagerDetail = UserModule.queryUserFactory.queryManagerDetail // 用户信息
    distributorName = ''
    promoteThePortalAlias = ''
    form = {
      data1: ''
    }
    tableData1 = [
      {
        id: 1,
        field01: '福建省',
        hasChildren: true
      },
      {
        id: 2,
        field01: '江西省',
        hasChildren: true
      },
      {
        id: 3,
        field01: '河南省',
        hasChildren: true
      },
      {
        id: 4,
        field01: '广东省',
        hasChildren: true
      },
      {
        id: 5,
        field01: '湖南省',
        hasChildren: true
      }
    ]
    tableData2 = [] as any[]
    locationUnit: string[] = []
    // 培训方案名称
    schemeName = ''
    // 排序策略
    sortPolicy: Array<CommoditySkuSortRequest> = new Array<CommoditySkuSortRequest>()

    // 培训方案业务状态层入口
    schemeBusinessEntry: QueryTrainClassCommodityList = new QueryTrainClassCommodityList()
    /**
     * 培训方案类型
     */
    schemeTypeInfo: Array<string> = new Array<string>()
    // 培训方案列表
    trainSchemeList: Array<TrainClassCommodityVo> = new Array<TrainClassCommodityVo>()
    /**
     * 行业属性分类Id
     */
    industryPropertyId = ''

    /**
     * 培训类别Id
     */
    trainingCategoryId = ''
    /**
     * 培训对象Id
     */
    trainingObjectId = ''
    /**
     * 推广门户id
     */
    portalId = ''
    /**
     * 隐藏的sku属性
     */
    skuVisible = {
      // 科目类型
      subjectType: false,
      // 培训类别
      trainingCategory: false,
      // 岗位类别
      positionCategory: false,
      // 培训对象
      trainingObject: false,
      // 技术等级
      technicalGrade: false,
      // 学段
      learningPhase: false,
      // 学科
      discipline: false
    }
    /**
     * 当前网校信息
     */
    envConfig = {
      // 人社行业Id
      societyIndustryId: '',
      // 建设行业Id
      constructionIndustryId: '',
      // 工勤行业Id
      workServiceId: '',
      // 职业卫生行业Id
      professionHealthIndustryId: '',
      // 教师行业id
      teacherIndustryId: ''
    }
    /**
     * 本地sku
     */
    localSkuProperty: SchemeSkuProperty = new SchemeSkuProperty()
    tableData10 = [] as any[]
    areaOptions = [] as any[]
    maps = new Map()
    pid: string
    /**
     * 是否专题
     */
    saleChannels: boolean = null
    showOffline = !QueryShowOffline.getShowOfflineApolloConfig()

    /**
     * 是否开启分销服务
     */
    get isOpenFxService() {
      return CapabilityServiceConfig.fxCapabilityEnable
    }

    /**
     * 是否是地区管理员
     */
    get isDistrictAdministrator() {
      return UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
    }

    /**
     * 判断是否是暂无地区行/合计行
     */
    get isSpecialRow() {
      return (row: any) => {
        return row.id === '暂无地区' || row.id === '合计'
      }
    }

    /**
     * 是否是用户自定义选择地区
     * @description 代表统计展示的行数据地区数量
     */
    get isUserManualChooseRegion() {
      return this.locationUnit && this.locationUnit.length
    }

    /**
     * 获取列表序号
     * @param row 当前行的数据
     * @param index 当前行的索引下标（从0开始）
     */
    getIndex(row: any, index: number) {
      if (this.isSpecialRow(row)) {
        return row.id
      } else {
        return index + 1
      }
    }

    /**
     * 处理列表查询参数
     */
    getPageQueryParams() {
      this.getLocalSkuProperty()
      this.learningReportFormsRequest.scheme.schemeName = this.schemeName || undefined
      this.configureTrainSchemeQueryParam()
    }

    /**
     * 配置查询参数
     */
    configureTrainSchemeQueryParam() {
      // 处理培训方案类型
      if (!this.schemeTypeInfo.length) {
        this.learningReportFormsRequest.scheme.schemeType = undefined
      }
      const schemeType = this.schemeTypeInfo[this.schemeTypeInfo.length - 1]
      // if (schemeType === 'chooseCourseLearning' || schemeType === 'autonomousCourseLearning') {
      this.learningReportFormsRequest.scheme.schemeType = schemeType
      // } else {
      //   this.learningReportFormsRequest.scheme.schemeType = undefined
      // }
    }

    /**
     * 获取本地sku选项
     */
    getLocalSkuProperty() {
      // const skuProperties = cloneDeep(this.trainSchemeQueryParam.skuPropertyRequest)
      const skuProperties = cloneDeep(this.learningReportFormsRequest.scheme.skuProperty)
      skuProperties.year = !this.localSkuProperty.year ? ([] as string[]) : this.localSkuProperty.year
      skuProperties.regionSkuPropertySearch.region = new Array<RegionSkuPropertyRequest>()
      const localRegion = cloneDeep(this.localSkuProperty.region)
      if (Array.isArray(localRegion) && localRegion.length) {
        const option = new RegionSkuPropertyRequest()
        option.province = localRegion.length >= 1 ? localRegion[0] : undefined
        option.city = localRegion.length >= 2 ? localRegion[1] : undefined
        option.county = localRegion.length >= 3 ? localRegion[2] : undefined
        //物理地区改为业务地区
        // option.province = '320000'
        // option.city = localRegion ? localRegion[0] : undefined
        // option.county = undefined
        skuProperties.regionSkuPropertySearch.region.push(option)
        skuProperties.regionSkuPropertySearch.regionSearchType = 1
      } else {
        skuProperties.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      }
      skuProperties.industry = !this.localSkuProperty.industry ? ([] as string[]) : [this.localSkuProperty.industry]
      skuProperties.subjectType = !this.localSkuProperty.subjectType
        ? ([] as string[])
        : [this.localSkuProperty.subjectType]
      skuProperties.trainingCategory = !this.localSkuProperty.trainingCategory
        ? ([] as string[])
        : [this.localSkuProperty.trainingCategory]
      skuProperties.positionCategory = !this.localSkuProperty.positionCategory
        ? ([] as string[])
        : [this.localSkuProperty.positionCategory]
      skuProperties.trainingObject = !this.localSkuProperty.trainingObject
        ? ([] as string[])
        : [this.localSkuProperty.trainingObject]
      skuProperties.technicalGrade = !this.localSkuProperty.technicalGrade
        ? ([] as string[])
        : [this.localSkuProperty.technicalGrade]
      // 学科、学段转换
      skuProperties.learningPhase = !this.localSkuProperty.learningPhase
        ? ([] as string[])
        : [this.localSkuProperty.learningPhase]
      skuProperties.discipline = !this.localSkuProperty.discipline
        ? ([] as string[])
        : [this.localSkuProperty.discipline]
      skuProperties.trainingWay = !this.localSkuProperty.trainingForm ? [] : [this.localSkuProperty.trainingForm]
      skuProperties.trainingProfessional = this.getTrainingProfessional()
      // this.trainSchemeQueryParam.skuPropertyRequest = cloneDeep(skuProperties)
      this.learningReportFormsRequest.scheme.skuProperty = cloneDeep(skuProperties)
      this.learningReportFormsRequestChidren.scheme.skuProperty = cloneDeep(skuProperties)
    }

    /**
     * 获取培训专业查询参数
     */
    getTrainingProfessional(): string[] {
      if (!this.localSkuProperty.industry) {
        return [] as string[]
      }
      // 人设行业
      if (this.localSkuProperty.industry === this.envConfig.societyIndustryId) {
        const majorArr = this.localSkuProperty.societyTrainingMajor
        return majorArr && majorArr.length ? [majorArr[majorArr.length - 1]] : ([] as string[])
      }
      // 建设行业
      if (this.localSkuProperty.industry === this.envConfig.constructionIndustryId) {
        return this.localSkuProperty.constructionTrainingMajor
          ? [this.localSkuProperty.constructionTrainingMajor]
          : ([] as string[])
      }
      return [] as string[]
    }

    /**
     * 更新培训类别联动
     */
    handleUpdateTrainingCategory(value: string) {
      this.localSkuProperty.constructionTrainingMajor = ''
      this.trainingCategoryId = value
    }

    /**
     * 分页查询
     */
    async pageScheme() {
      // this.trainSchemeQuery.loading = true
      try {
        this.getPageQueryParams()
        if (this.learningReportFormsRequest.learningRegister.status) {
          delete this.learningReportFormsRequest.learningRegister.status
        }
        this.learningReportFormsRequestChidren.scheme = this.learningReportFormsRequest.scheme
        this.learningReportFormsRequestChidren.learningRegister = this.learningReportFormsRequest.learningRegister
        this.learningReportFormsRequestChidren.learningRegister.portalId = this.portalId
        if (this.learningReportFormsRequest.notDistributionPortal) {
          this.learningReportFormsRequestChidren.learningRegister.portalId = ''
        }
        // isDistrictAdministrator => 是否是地区管理员
        const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
        if (isDistrictAdministrator) {
          // 地区管理员
          this.regionLearningStatisticsResponseVo =
            await this.queryRegionManagerRegionLearningList.listRegionLearningReportFormsInServicer(
              this.learningReportFormsRequest
            )
          this.regionLearningStatisticsResponseVoChildren = []
        } else {
          // 超管
          this.resolveIsStatisticsNullArea(this.learningReportFormsRequest)
          this.regionLearningStatisticsResponseVo =
            await this.queryTrainClassReportList.listRegionLearningReportFormsInServicer(
              this.learningReportFormsRequest
            )
          this.regionLearningStatisticsResponseVoChildren = []
        }
      } catch (e) {
        console.log('获取培训方案分页列表失败！', e)
      } finally {
        // 解决懒加载区县数据不刷新
        if (this.maps.size) {
          // 获取节点
          const node = this.maps.get(this.pid)
          // 获取需要刷新节点
          const { tree, treeNode, resolve } = node
          // 重新加载子节点数据
          this.load(tree, treeNode, resolve)
        }
        //处理切换页数后行数错位问题
        ;(this.$refs['schemeTable'] as any)?.doLayout()
        // this.trainSchemeQuery.loading = false
        this.exportQueryParam = Object.assign(new LearningReportFormsRequest(), this.learningReportFormsRequest)
      }
    }

    async initialArea() {
      this.learningReportFormsRequest.student.userProperty.regionList = []
      const childrenAreaList: Array<NewRegionTreeVo> = []
      const childBuyerAreaPath: Array<RegionRequest> = []
      // const queryRegisterRegion = (await QueryPhysicalRegion.queryRegion()) as any[]
      const queryRegisterRegion = (await QueryBusinessRegion.getServiceOrIndustry(1)) as any[]
      // const res = await QueryBusinessRegion.queryBusinessRegion()
      if (queryRegisterRegion.length > 0) {
        // const res = QueryRegisterRegion.topRegionTree
        const arr = queryRegisterRegion.map((item) => {
          item.id = item.id.substr(0, 2) + '0000'
          return item.id
        })
        const area = arr.filter(function (item, index) {
          return arr.indexOf(item) === index
        })
        const areaList = QueryBusinessRegion.filterRegionTree(this.areaOptions, area)
        areaList.map((aitem) => {
          if (aitem.children?.length > 0) {
            // 不同省
            aitem.children.map(
              (item: {
                parentCode: string
                code: string
                regionPath: any
                children: any
                name: string
                id: string
                parentId: string
              }) => {
                if (item.children?.length > 0) {
                  // 省级
                  item.children.map((subItem: { name: string; id: string; parentId: string; regionPath: string }) => {
                    const subRegion = new NewRegionTreeVo()
                    subRegion.name = subItem.name
                    subRegion.id = subItem.id
                    subRegion.parentId = subItem.parentId
                    subRegion.leaf = false
                    subRegion.level = 1
                    // childrenAreaList.push(subRegion)
                    const codePathArr = subItem.regionPath.split('/')
                    const codePath = {
                      /**
                       * 地区：省
                       */
                      province: codePathArr[1],
                      /**
                       * 地区：市
                       */
                      city: codePathArr[2],
                      /**
                       * 地区：区
                       */
                      county: codePathArr[3]
                    }
                    childBuyerAreaPath.push(codePath)
                  })
                }
                // 没有children 省直
                const region = new NewRegionTreeVo()
                region.name = item.name
                region.id = item.id
                region.parentId = item.parentId
                region.leaf = true
                region.level = 0
                childrenAreaList.push(region)
                const codePath = item.regionPath.split('/')
                const region1 = {
                  /**
                   * 地区：省
                   */
                  province: codePath[1],
                  /**
                   * 地区：市
                   */
                  city: codePath[2]
                }
                this.learningReportFormsRequest.student.userProperty.regionList.push(region1)
                return region
              }
            )
          }
        })
      }
      this.learningReportFormsRequestChidren.student.userProperty.regionList = childBuyerAreaPath
      this.provinces = this.provinces.concat(childrenAreaList)
    }

    async created() {
      this.initQueryParam()
      this.areaOptions = await QueryBusinessRegion.getCountrywideRegion()
      await this.initialArea() //获取初始地区
      // await this.pageScheme() //调用查询列表口
      // await this.getSummaryInServicer()
      // this.dataMerge()
      await this.doSearch()
      // setTimeout(() => {
      //   const html: HTMLElement = document.getElementsByClassName('cell')[
      //     document.getElementsByClassName('cell').length - 1
      //   ] as HTMLElement
      //   html.innerHTML = '<div style="color: #1f86f0">学员学习明细</div>'
      //   html.onclick = () => {
      //     this.$router.push({
      //       path: '/statistic/learning-statistic'
      //     })
      //   }
      // }, 100)
      this.listFields = this.clearOffline(this.listFields)
    }

    clearOffline(list: Array<any>) {
      let temp = list
      if (!this.showOffline) {
        temp = list.filter(
          (description) => !(description.field.includes('培训期别') || description.description.includes('培训期别'))
        )
      }
      return temp
    }

    // 选择是否来源专题
    changeSaleChannels() {
      this.learningReportFormsRequest.saleChannels = SaleChannelType.getSpecialSubjectSaleChannel(this.saleChannels)
      console.log(this.saleChannels, 'saleChannels')
      if (!this.saleChannels && this.saleChannels != null) {
        this.learningReportFormsRequest.trainingChannelName = ''
      }
    }

    async doSearch() {
      this.$nextTick(async () => {
        try {
          this.trainSchemeQuery.loading = true
          await this.finalSearch()
          await this.getSummaryInServicer()
          this.loadSpecialRow()
        } catch (e) {
          console.log('加载统计列表失败：', e)
        } finally {
          this.trainSchemeQuery.loading = false
        }
      })
    }

    /**
     * 获取统计数据
     */
    async getSummaryInServicer() {
      if (this.isDistrictAdministrator) {
        // 地区管理员
        await this.queryRegionManagerRegionLearningList.getStatistics(this.learningReportFormsRequest)
      } else {
        this.resolveIsStatisticsNullArea(this.learningReportFormsRequest)
        // 超管
        await this.queryTrainClassReportList.getStatistics(this.learningReportFormsRequest)
      }
    }

    async finalSearch() {
      const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
      //处理单位所在地区后再进行查询
      this.learningReportFormsRequest.student.userProperty.regionList = []
      if (this.locationUnit.length > 1) {
        //由物理地区转为业务地区
        const region1 = {} as RegionRequest
        // region1.province = this.locationUnit[0]
        if (this.locationUnit.length === 2) {
          region1.province = this.locationUnit[0]
          region1.city = this.locationUnit[1]
        } else if (this.locationUnit.length === 3) {
          region1.province = this.locationUnit[0]
          region1.city = this.locationUnit[1]
          region1.county = this.locationUnit[2]
        }

        this.learningReportFormsRequest.student.userProperty.regionList.push(region1)
        await this.pageScheme() //调用查询列表口
        if (this.locationUnit.length != 1) {
          await this.dataMerge()
        }
      } else if (
        isDistrictAdministrator &&
        (this.queryManagerDetail.adminInfo.userInfo.manageRegionList[0].cityId ||
          this.queryManagerDetail.adminInfo.userInfo.manageRegionList[0].countyId)
      ) {
        this.learningReportFormsRequest.student.userProperty.regionList = [
          {
            city: this.queryManagerDetail.adminInfo.userInfo.manageRegionList[0].cityId
              ? this.queryManagerDetail.adminInfo.userInfo.manageRegionList[0].cityId
              : undefined,
            county: this.queryManagerDetail.adminInfo.userInfo.manageRegionList[0].countyId
              ? this.queryManagerDetail.adminInfo.userInfo.manageRegionList[0].countyId
              : undefined,
            province: this.queryManagerDetail.adminInfo.userInfo.manageRegionList[0].provinceId
          }
        ]

        await this.pageScheme() //调用查询列表口
        await this.dataMerge()
      } else {
        if (this.locationUnit.length === 1) {
          this.learningReportFormsRequest.student.userProperty.regionList = []
          const areaList = await QueryPhysicalRegion.queryLowerLevelRegion(this.locationUnit[0])
          areaList.map((item) => {
            const region1 = {} as RegionRequest
            region1.province = this.locationUnit[0]
            region1.city = item.code
            this.learningReportFormsRequest.student.userProperty.regionList.push(region1)
          })
        } else {
          await this.initialArea() //获取初始地区
        }
        await this.pageScheme() //调用查询列表口
        // this.learningReportFormsRequest.student.userProperty.regionList = []
        await this.dataMerge()
      }
    }

    /**
     * 数据合并
     *
     */
    async dataMerge() {
      this.tableData2 = []
      this.tableData10 = []
      // 去重
      const list = new Array<string>()
      this.provinces = this.provinces.filter((item) => {
        if (!list.includes(item.id)) {
          list.push(item.id)
          return item
        }
      })
      // 去重
      const list2: any[] = []
      this.regionLearningStatisticsResponseVo = this.regionLearningStatisticsResponseVo.filter((item) => {
        if (!list2.includes(item.region.city)) {
          list2.push(item.region.city)
          return item
        }
      })
      try {
        this.provinces.map((itm) => {
          this.regionLearningStatisticsResponseVo.map((item) => {
            let obj
            if (
              this.locationUnit.length <= 2 &&
              itm.id === item.region.city &&
              (!UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin ||
                (UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin &&
                  this.queryManagerDetail.adminInfo.userInfo.manageRegionList[0].countyId == ''))
            ) {
              obj = Object.assign(itm, item)
              this.tableData2.push(obj)
            } else if (itm.id === item.region.city) {
              //id相同时数据合并
              itm.leaf = true
              obj = Object.assign(item, itm)
              this.tableData2.push(obj)
            } else if (itm.id === item.region.county) {
              //id相同时数据合并
              itm.leaf = false
              obj = Object.assign(item, itm)
              this.tableData2.push(obj)
            }
          })
        })
        //单个省份 单独区县显示
        if (this.locationUnit.length == 3) {
          const obj = new NewRegionTreeVo()
          obj.id = this.locationUnit[2]
          obj.name = QueryBusinessRegion.getCountrywideRegionCodeDetail(this.locationUnit[2])?.name
          obj.leaf = false
          obj.parentId = this.locationUnit[0]
          Object.assign(obj, this.regionLearningStatisticsResponseVo[0])
          this.tableData2.push(obj)
        }
        const idList: Array<string> = []
        const indexList: Array<number> = []
        this.tableData2.map((item, index) => {
          if (
            (index < this.tableData2.length - 1 && item.parentId != this.tableData2[index + 1].parentId) ||
            index == this.tableData2.length - 1
          ) {
            idList.push(item.parentId)
            if (index != this.tableData2.length - 1) {
              indexList.push(index)
            }
          }
        })
        const area: any = await QueryBusinessRegion.queryRegionsNameByIds(idList)
        const provinceList: any = []
        for (const [key, value] of area) {
          // 不知道为什么会查出其他地区 需要过滤掉
          if (idList.includes(key)) {
            provinceList.push({ name: value, id: key })
          }
        }
        // 调整三级结构 添加省级
        this.tableData2 = provinceList.map((item: any, index: number) => {
          item.learningStatistic = {
            courseLearningStatistic: {
              studyFinishCount: 0,
              studyingCount: 0,
              waitStudyCount: 0
            },
            examLearningStatistic: {
              committedExamCount: 0,
              passedExamCount: 0
            },
            netRegisterCount: 0,
            qualifiedCount: 0,
            regionPeopleCount: 0,
            questionnaireSubmitCount: 0,
            questionnaireUnSubmitCount: 0,
            issueQualifiedCount: 0,
            issueUnQualifiedCount: 0
          }
          if (index === 0) {
            if (indexList.length > 0) {
              // 服务地区为多个省份 第一个省份
              item.children = this.tableData2.filter((sitem, sindex) => {
                return sindex <= indexList[0]
              })
            } else {
              // 单个省份 直接塞进二级结构
              item.children = this.tableData2
            }
          } else if (index === provinceList.length - 1) {
            // 服务地区为多个省份 中间省份
            item.children = this.tableData2.filter((sitem, sindex) => {
              return sindex > indexList[indexList.length - 1]
            })
          } else {
            // 服务地区为多个省份 最后一个省份
            item.children = this.tableData2.filter((sitem, sindex) => {
              return indexList[index - 1] < sindex && sindex <= indexList[index]
            })
          }
          item.children.map((mitem: any) => {
            item.learningStatistic.courseLearningStatistic.studyFinishCount +=
              mitem.learningStatistic.courseLearningStatistic.studyFinishCount || 0
            item.learningStatistic.courseLearningStatistic.studyingCount +=
              mitem.learningStatistic.courseLearningStatistic.studyingCount || 0
            item.learningStatistic.courseLearningStatistic.waitStudyCount +=
              mitem.learningStatistic.courseLearningStatistic.waitStudyCount || 0
            item.learningStatistic.examLearningStatistic.committedExamCount +=
              mitem.learningStatistic.examLearningStatistic.committedExamCount || 0
            item.learningStatistic.examLearningStatistic.passedExamCount +=
              mitem.learningStatistic.examLearningStatistic.passedExamCount || 0
            item.learningStatistic.netRegisterCount += mitem.learningStatistic.netRegisterCount || 0
            item.learningStatistic.qualifiedCount += mitem.learningStatistic.qualifiedCount || 0
            item.learningStatistic.regionPeopleCount += mitem.learningStatistic.regionPeopleCount || 0
            item.learningStatistic.questionnaireSubmitCount += mitem.learningStatistic.questionnaireSubmitCount || 0
            item.learningStatistic.questionnaireUnSubmitCount += mitem.learningStatistic.questionnaireUnSubmitCount || 0
            item.learningStatistic.issueQualifiedCount += mitem.learningStatistic.issueQualifiedCount || 0
            item.learningStatistic.issueUnQualifiedCount += mitem.learningStatistic.issueUnQualifiedCount || 0
          })
          return item
        })
        console.log(this.tableData2, 'tab2s')
        // this.provinces.map(itm => {
        //   this.regionLearningStatisticsResponseVoChildren.map(item => {
        //     if (itm.id === item.region.county) {
        //       //id相同时数据合并
        //       const obj = Object.assign(itm, item)
        //       this.tableData10.push(obj)
        //     }
        //   })
        // })
      } catch (err) {
        console.log(err)
      }
    }

    /**
     * 加载特殊行
     * @description 处理固定吸附在底部的暂无地区行、合计行数据
     */
    loadSpecialRow() {
      let statisticM, withoutStaticM
      if (this.isDistrictAdministrator) {
        // 地区管理员角色
        statisticM = this.queryRegionManagerRegionLearningList.statisticsM
        withoutStaticM = this.queryRegionManagerRegionLearningList.withoutStaticM
      } else {
        // 管理员角色
        statisticM = this.queryTrainClassReportList.statisticsM
        withoutStaticM = this.queryTrainClassReportList.withoutStaticM
      }
      if (this.locationUnit && this.locationUnit.length) {
        // 带单位所在地地区查询时，不需要展示暂无地区行数据，仅展示合计行数据
        this.tableData2.push({
          id: '合计',
          children: [] as any[],
          name: '',
          learningStatistic: statisticM
        })
      } else {
        // 暂无地区行
        const withoutStaticItem = {
          id: '暂无地区',
          children: [] as any[],
          name: '',
          learningStatistic: withoutStaticM
        }
        // 合计行
        const summaryStaticItem = {
          id: '合计',
          children: [] as any[],
          name: '',
          learningStatistic: statisticM
        }
        if (!this.isUserManualChooseRegion && !this.isDistrictAdministrator) {
          // 超管角色&用户没选择单位所在地-要使用暂无地区行、合计行数据
          this.tableData2.push(withoutStaticItem, summaryStaticItem)
        } else {
          // 地区管理员-要使用暂无地区行、合计行数据
          this.tableData2.push(summaryStaticItem)
        }
      }
    }

    resolveIsStatisticsNullArea(params: LearningReportFormsRequest) {
      if (this.isUserManualChooseRegion) {
        // 如果用户有手动指定展示地区行，则isStatisticsNullArea要传false（默认为true，代表要统计暂无地区的数据）
        params.isStatisticsNullArea = false
      } else {
        params.isStatisticsNullArea = true
      }
    }

    /**
     * 加载子节点数据
     * @param tree 节点树
     * @param treeNode 节点数据
     * @param resolve 要展开展示的子节点数据
     */
    async load(tree: any, treeNode: any, resolve: (arr: Array<NewRegionTreeVo>) => void) {
      /*
       * 事急从权先这么写了，这坨屎山，下次要改直接重构吧
       */
      const subRegionList = await QueryPhysicalRegion.queryLowerLevelRegion(tree.id)
      const subRegionRqList = new Array<RegionRequest>()
      subRegionList.map((item: RegionResponse) => {
        const regionRq = new RegionRequest()
        const codePathSplits = item.codePath.split('/')
        regionRq.province = codePathSplits?.length > 1 ? codePathSplits[1] : null
        regionRq.city = codePathSplits?.length > 2 ? codePathSplits[2] : null
        regionRq.county = codePathSplits?.length > 3 ? codePathSplits[3] : null
        subRegionRqList.push(regionRq)
      })
      const resolveList = new Array<NewRegionTreeVo>()
      if (subRegionRqList.length) {
        // 只有地区不为的时候才有查询的意义；空地区查询时接口也会报错，此时直接返回空数组
        const subRq = cloneDeep(this.learningReportFormsRequest) as LearningReportFormsRequest
        subRq.student.userProperty.regionList = subRegionRqList

        let res = [] as RegionLearningStatisticsResponseVo[]
        if (this.isDistrictAdministrator) {
          // 地区管理员
          res = await this.queryRegionManagerRegionLearningList.listRegionLearningReportFormsInServicer(subRq)
        } else {
          // 超管
          this.resolveIsStatisticsNullArea(subRq)
          res = await this.queryTrainClassReportList.listRegionLearningReportFormsInServicer(subRq)
        }

        res.map((item) => {
          const regionTreeVo = new NewRegionTreeVo()
          const findSubRegion = subRegionList.find((it) => it.code === item.region.county)
          if (findSubRegion) {
            if (this.locationUnit.length >= 3 && findSubRegion?.code != this.locationUnit[2]) {
              return
            }
            regionTreeVo.name = findSubRegion?.name
            regionTreeVo.id = findSubRegion?.code
            regionTreeVo.parentId = findSubRegion?.parentCode
            regionTreeVo.leaf = false
            regionTreeVo.level = findSubRegion?.level
            resolveList.push(Object.assign(regionTreeVo, item))
          }
        })
        this.pid = tree.pid
        this.maps.set(tree.pid, { tree, treeNode, resolve })
      }
      resolve(resolveList)
    }

    /**
     * 初始化查询参数
     */
    initQueryParam() {
      this.learningReportFormsRequest = new NewLearningReportFormsRequest()
      this.learningReportFormsRequest.learningRegister = new LearningRegisterRequest()
      this.learningReportFormsRequest.learningRegister.registerTime = new DateScopeRequest()
      this.learningReportFormsRequest.learningRegister.registerTime.begin = ''
      this.learningReportFormsRequest.learningRegister.registerTime.end = ''
      this.distributorName = ''
      this.promoteThePortalAlias = ''
      this.learningReportFormsRequest.scheme = new SchemeRequest()
      this.learningReportFormsRequest.scheme.schemeName = ''
      // this.tradeReportRequest.commoditySku.scheme = new SchemeRequest1()
      this.learningReportFormsRequest.scheme.schemeType = ''
      // this.tradeReportRequest.commoditySku.scheme.schemePeriodScope = new DoubleScopeRequest()

      this.learningReportFormsRequest.scheme.skuProperty = new SkuPropertyRequest()
      this.learningReportFormsRequest.scheme.skuProperty.year = new Array<string>()
      this.learningReportFormsRequest.scheme.skuProperty.regionSkuPropertySearch = new RegionSkuPropertySearchRequest()
      this.learningReportFormsRequest.scheme.skuProperty.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.learningReportFormsRequest.scheme.skuProperty.industry = new Array<string>()
      this.learningReportFormsRequest.scheme.skuProperty.subjectType = new Array<string>()
      this.learningReportFormsRequest.scheme.skuProperty.trainingCategory = new Array<string>()
      this.learningReportFormsRequest.scheme.skuProperty.trainingProfessional = new Array<string>()
      this.learningReportFormsRequest.student = new UserRequest()
      this.learningReportFormsRequest.student.userProperty = new UserPropertyRequest()
      this.learningReportFormsRequest.student.userProperty.regionList = new Array<RegionRequest>()

      this.learningReportFormsRequestChidren = new NewLearningReportFormsRequest()
      this.learningReportFormsRequestChidren.learningRegister = new LearningRegisterRequest()
      this.learningReportFormsRequestChidren.learningRegister.registerTime = new DateScopeRequest()
      this.learningReportFormsRequestChidren.learningRegister.registerTime.begin = ''
      this.learningReportFormsRequestChidren.learningRegister.registerTime.end = ''
      this.learningReportFormsRequestChidren.scheme = new SchemeRequest()
      this.learningReportFormsRequestChidren.scheme.schemeName = ''
      // this.tradeReportRequest.commoditySku.scheme = new SchemeRequest1()
      this.learningReportFormsRequestChidren.scheme.schemeType = ''
      // this.tradeReportRequest.commoditySku.scheme.schemePeriodScope = new DoubleScopeRequest()

      this.learningReportFormsRequestChidren.scheme.skuProperty = new SkuPropertyRequest()
      this.learningReportFormsRequestChidren.scheme.skuProperty.year = new Array<string>()
      this.learningReportFormsRequestChidren.scheme.skuProperty.regionSkuPropertySearch =
        new RegionSkuPropertySearchRequest()
      this.learningReportFormsRequestChidren.scheme.skuProperty.regionSkuPropertySearch.region =
        new Array<RegionSkuPropertyRequest>()
      this.learningReportFormsRequestChidren.scheme.skuProperty.industry = new Array<string>()
      this.learningReportFormsRequestChidren.scheme.skuProperty.subjectType = new Array<string>()
      this.learningReportFormsRequestChidren.scheme.skuProperty.trainingCategory = new Array<string>()
      this.learningReportFormsRequestChidren.scheme.skuProperty.trainingProfessional = new Array<string>()
      // 初始化添加 学科、学段置空
      // todo
      this.learningReportFormsRequestChidren.student = new UserRequest()
      this.learningReportFormsRequestChidren.student.userProperty = new UserPropertyRequest()
      this.learningReportFormsRequestChidren.student.userProperty.regionList = new Array<RegionRequest>()
      this.sortPolicy = new Array<CommoditySkuSortRequest>()
      this.schemeTypeInfo = new Array<string>()
      // this.queryTrainClassReportList.statisticsM.tradeCountSummaryInfo = new SubOrderStatisticDto()
    }

    /**
     * 重置查询条件
     */
    async resetQueryParam() {
      await this.initQueryParam()
      this.portalId = ''
      this.$set(this.learningReportFormsRequest, 'notDistributionPortal', false)
      this.saleChannels = null
      this.localSkuProperty = new SchemeSkuProperty()
      // this.onShelveStatus = null
      this.locationUnit = []
      this.schemeName = ''
      this.locationUnit = []
      this.regionValueRef.selctValue = []
      this.sortPolicy = new Array<CommoditySkuSortRequest>()
      this.learningReportFormsRequest.student.userProperty.regionList = []

      await this.doSearch()
    }

    constructor() {
      super()
      this.trainSchemePage = new UiPage(this.searchBase, this.searchBase)
    }

    /**
     * 加载第一页
     */
    async searchBase() {
      this.trainSchemePage.pageNo = 1

      this.tableData2 = []
      await this.initialArea() //获取初始地区
      await this.pageScheme() //调用查询列表口
      await this.dataMerge()
      this.loadSpecialRow()
    }

    /**
     * 组件联动：切换行业清空已选项
     */
    handleClearIndustrySelect() {
      // 清空已选项
      this.localSkuProperty.subjectType = ''
      this.localSkuProperty.trainingCategory = ''
      this.localSkuProperty.trainingObject = ''
      this.localSkuProperty.positionCategory = ''
      this.localSkuProperty.technicalGrade = ''
      this.localSkuProperty.societyTrainingMajor = [] as string[]
      this.localSkuProperty.constructionTrainingMajor = ''
      this.localSkuProperty.discipline = ''
      this.localSkuProperty.learningPhase = ''
    }

    /**
     * 学段变化清空学科
     */
    updateStudyPeriod() {
      this.localSkuProperty.discipline = ''
    }

    /**
     * 组件联动：industryPropertyId
     */
    async handleIndustryPropertyId(val: string) {
      this.industryPropertyId = val
      // 获取不同行业的配置项
      const skuQueryRemote = BasicDataDictionaryModule.queryBasicDataDictionaryFactory.queryIndustryPropertyCategory
      const configList: Array<IndustryPropertyCategoryVo> = await skuQueryRemote.getIndustryPropertyCategoryList(
        this.industryPropertyId
      )
      const configSubjectType = configList.findIndex((el) => (el.name = '科目类型'))
      const configTrainingCategory = configList.findIndex((el) => (el.name = '培训类别'))
      const configPositionCategory = configList.findIndex((el) => (el.code = 'POSITION_CATEGORY'))
      const configTrainingObject = configList.findIndex((el) => (el.code = 'TRAINNING_OBJECT'))
      const configTechnicalLevel = configList.findIndex((el) => (el.code = 'JOB_LEVEL'))
      // 修改
      const configTechnicalStudying = configList.findIndex((el) => (el.code = '学段'))
      const configTechnicalSubject = configList.findIndex((el) => (el.code = '学科'))
      this.skuVisible.subjectType = configSubjectType > -1 ? true : false
      this.skuVisible.trainingCategory = configTrainingCategory > -1 ? true : false
      this.skuVisible.positionCategory = configPositionCategory > -1 ? true : false
      this.skuVisible.trainingObject = configTrainingObject > -1 ? true : false
      this.skuVisible.technicalGrade = configTechnicalLevel > -1 ? true : false
      // 获取教师配置
      this.skuVisible.learningPhase = configTechnicalStudying > -1 ? true : false
      this.skuVisible.discipline = configTechnicalSubject > -1 ? true : false
    }

    // 卫生行业培训类别联动
    handleWSUpdateTrainingObject(value: string) {
      this.localSkuProperty.positionCategory = ''
      this.trainingObjectId = value
    }

    /**
     * 响应组件行业Id集合传参
     */
    handleIndustryInfos(values: any) {
      this.envConfig.societyIndustryId = values.societyIndustryId || ''
      this.envConfig.constructionIndustryId = values.constructionIndustryId || ''
      this.envConfig.workServiceId = values.workServiceId || ''
      this.envConfig.professionHealthIndustryId = values.professionHealthIndustryId || ''
      this.envConfig.teacherIndustryId = values.teacherIndustryId || ''
    }

    async confirmExportSort(name: string) {
      const region = this.learningReportFormsRequest.student?.userProperty?.regionList
        ? this.learningReportFormsRequest.student?.userProperty?.regionList[0]
        : undefined
      if (region && region.province && region.city && !region.county) {
        this.provinces
          .filter((item) => item.parentId === region.city)
          .map((ite) => {
            this.learningReportFormsRequest.student?.userProperty?.regionList.push({
              province: region.province,
              city: region.city,
              county: ite.id
            })
          })
      }
      if (name == '导出数据列表') {
        await this.exportListData()
      } else if (name == '导出列表人员详细') {
        await this.exportDetailListData()
      } else {
        this.$message.error('请选择导出方式')
        return
      }
    }

    goDownloadPage() {
      this.exportSuccessVisible = false
      const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
      if (isDistrictAdministrator) {
        this.$router.push({
          path: '/statistic/export-task',
          query: { type: 'exportRegionLearningStatistical' }
        })
      } else {
        this.$router.push({
          path: '/training/task/exporttask',
          query: { type: 'exportRegionLearningStatistical' }
        })
      }
    }

    goDownloadDetailPage() {
      this.exportDetailSuccessVisible = false
      const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
      if (isDistrictAdministrator) {
        this.$router.push({
          path: '/statistic/export-task',
          query: { type: 'exportRegionLearningDetailStatistical' }
        })
      } else {
        this.$router.push({
          path: '/training/task/exporttask',
          query: { type: 'exportRegionLearningDetailStatistical' }
        })
      }
    }

    async exportListData() {
      // isDistrictAdministrator => 是否是地区管理员
      const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
      this.exportQueryParam = Object.assign(new LearningReportFormsRequest(), this.learningReportFormsRequest)
      this.learningReportFormsRequest.learningRegister.portalId = this.portalId
      if (this.learningReportFormsRequest.notDistributionPortal) {
        this.learningReportFormsRequest.learningRegister.portalId = ''
      }
      if (this.isDistrictAdministrator) {
        // 地区管理员
      } else {
        // 超管
        this.resolveIsStatisticsNullArea(this.exportQueryParam)
      }
      try {
        const res = isDistrictAdministrator
          ? await this.queryRegionManagerRegionLearningList.exportExcel(this.exportQueryParam)
          : await this.queryTrainClassReportList.exportExcel(this.exportQueryParam)
        // const res = await this.queryTrainClassReportList.exportExcel(this.exportQueryParam)

        if (res.status.code == 200 && res.data) {
          //   this.$message.success('导出成功')
          this.exportDialog = false

          this.exportSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }

    async exportDetailListData() {
      const isDistrictAdministrator = UserModule.queryUserFactory.queryManagerDetail.isRegionAdmin
      if (this.isDistrictAdministrator) {
        // 地区管理员
      } else {
        // 超管
        this.resolveIsStatisticsNullArea(this.exportQueryParam)
      }

      try {
        this.exportQueryParam = Object.assign(new LearningReportFormsRequest(), this.learningReportFormsRequest)
        this.learningReportFormsRequest.learningRegister.portalId = this.portalId
        if (this.learningReportFormsRequest.notDistributionPortal) {
          this.learningReportFormsRequest.learningRegister.portalId = ''
        }
        const res = isDistrictAdministrator
          ? await this.queryRegionManagerRegionLearningList.exportExcelDetail(this.exportQueryParam)
          : await this.queryTrainClassReportList.exportExcelDetail(this.exportQueryParam)

        if (res.status.code == 200 && res.data) {
          //   this.$message.success('导出成功')
          this.exportDialog = false
          this.exportDetailSuccessVisible = true
        } else {
          this.$message.error('导出失败')
        }
      } catch (e) {
        console.log(e)
      } finally {
        //todo
      }
    }

    dealQualified(res: number) {
      if (res === 0) {
        return 1
      } else {
        return res
      }
    }

    toCollectDetails(item: RegionLearningStatisticsResponseVo) {
      if (!item.region) {
        // 省级跳转
        item.region = {
          province: item.id,
          city: '',
          county: ''
        }
      }
      const res =
        '/' +
        Object.values(item.region)
          .filter((region) => {
            return region
          })
          ?.join('/')

      this.$router.push({
        path: '/statistic/learning-statistic',
        query: {
          regionType: res,
          schemeName: this.learningReportFormsRequest.scheme.schemeName,
          year: JSON.stringify(this.learningReportFormsRequest.scheme.skuProperty.year) || '',
          registerTimeBegin: this.learningReportFormsRequest.learningRegister.registerTime.begin,
          registerTimeEnd: this.learningReportFormsRequest.learningRegister.registerTime.end,
          regionSkuPropertySearch: this.localSkuProperty.region?.[0],
          subjectName: this.learningReportFormsRequest.trainingChannelName,
          subjectType: JSON.stringify(this.learningReportFormsRequest.saleChannels)
        }
      })
    }

    /**
     * 设置行样式
     * @param params 列表行数据
     */
    setRowStyle(params: any) {
      const { row } = params
      return this.isSpecialRow(row)
        ? {
            position: 'sticky', // 吸附底部的关键样式
            bottom: row.id === '暂无地区' ? '46px' : '0px'
          }
        : {}
    }
  }
</script>
