import MsBasicdataQueryFrontGatewayBackstage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryBackstage'
import BasicDataQueryForestage from '@api/ms-gateway/ms-basicdata-query-front-gateway-BasicDataQueryForestage'
import ConfigCenterModule from '@api/service/common/config/ConfigCenterModule'
import { frontendApplication } from '@api/service/common/config/enums/ApolloConfigKeysEnum'
import Context from '@api/service/common/context/Context'
import LinkVo from '@api/service/common/online-school-config/vo/LinkVo'
import PortalVo from '@api/service/common/online-school-config/vo/PortalVo'
import { ResponseStatus } from '@hbfe/common'
import UnitConfig from '@api/service/management/online-school-config/portal/models/UnitConfig'
import ServicerSeriesV1Gateway from '@api/ms-gateway/ms-servicer-series-v1'

class QueryPortal {
  webAccess = true
  h5Access = true
  webPortalInfo = new PortalVo()
  h5PortalInfo = new PortalVo()
  /**
   * 单位设置
   */
  unitConfig: UnitConfig = new UnitConfig()

  async queryDetail(): Promise<ResponseStatus> {
    let response = new ResponseStatus(500, '')
    const serverId = Context.businessEnvironment.serviceToken.tokenMeta.servicerId
    const webRes = await MsBasicdataQueryFrontGatewayBackstage.getTrainingInstitutionPortalInfo({
      portalType: 1,
      servicerId: serverId
    })
    const h5Res = await MsBasicdataQueryFrontGatewayBackstage.getTrainingInstitutionPortalInfo({
      portalType: 2,
      servicerId: serverId
    })
    if (webRes.status.isSuccess()) {
      response = webRes.status
      this.webPortalInfo.from(webRes.data)
      this.webAccess = webRes.data.isPublished
      const linkRes = await MsBasicdataQueryFrontGatewayBackstage.getFriendLinkListByPortalType(1)
      if (linkRes.status.isSuccess()) {
        this.webPortalInfo.friendLinkList = linkRes.data.friendLinkInfos?.map(LinkVo.from)
      }
    }
    if (h5Res.status.isSuccess()) {
      response = h5Res.status
      this.h5PortalInfo.from(h5Res.data)
      this.h5Access = h5Res.data.isPublished
    }
    return response
  }
  /**
   * 获取网校网站 - 对应页面访问网站功能
   */
  async getPortalInfoInSubProject() {
    //
    const serviceId = Context.servicerInfo.id
    const response = await BasicDataQueryForestage.pagePortalInfoInSubProject({
      page: {
        pageNo: 1,
        pageSize: 10
      },
      request: {
        owner: {
          trainingInstitutionIdList: [serviceId]
        }
      }
    })
    const id = response.data.currentPageData[0].portalInfo.portalId
    const { data } = await BasicDataQueryForestage.getPortalInfoInSubProject(id)
    return data
  }
  /**
   * 获取网校是否对接公共服务平台
   */
  getServicerIsDocking() {
    const serviceId = Context.servicerInfo.id
    const dockServicerIds = ConfigCenterModule.getFrontendApplication(frontendApplication.dockServicerIds)
    return dockServicerIds?.includes(serviceId)
  }

  /**
   * 查询单位配置
   */
  async getUnitConfig() {
    const unitConfigRes = await ServicerSeriesV1Gateway.getDockingTycAndQcc()
    if (unitConfigRes.status?.isSuccess()) {
      this.unitConfig = UnitConfig.from(unitConfigRes.data)
    }

    return this.unitConfig
  }
}
export default new QueryPortal()
