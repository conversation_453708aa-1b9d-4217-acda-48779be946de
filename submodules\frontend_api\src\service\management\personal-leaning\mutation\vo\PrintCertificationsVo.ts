/*
 * @Author: <PERSON><PERSON><PERSON>
 * @Date: 2024-04-10 14:16:03
 * @LastEditors: z张仁榕
 * @LastEditTime: 2025-01-21 19:37:49
 * @Description:
 */
import {
  BatchPrintCertificatesRequest,
  StudentBatchPrintCertificatesRequest
} from '@api/platform-gateway/platform-certificate-v1'
import { FileTypesEnum } from '@api/service/common/enums/personal-leaning/FileTypes'
import { ServiceTypeEnum } from '@api/service/management/personal-leaning/mutation/enums/ServiceType'

class PrintCertificationsVo {
  /**
   * 学号集合
   */
  // studentNos?: Array<string> = []
  /**
   * 证书模板id 学员批量打印用
   */
  templateId?: string = ''
  /**
   * 文件类型 1-PDF   2-IMAGE
   */
  fileType: FileTypesEnum = null
  /**
   * 文件打印方式 1-连贯打印 2-单个打印
   */
  printType = 1
  /**
   * 是否合并打印（有连贯打印才有合并打印选项）1-是，0-否 字符串
   */
  isMerge = ''
  /**
   * 打印模板来源  (打印模板是读取方案配置，还是读取学员端选择模板)
   读方案 &#x3D; 1  读学员选择 &#x3D; 2
   */
  printSource = 1
  /**
   * 打印端口
   方案端 &#x3D; 1  学员端 &#x3D; 2
   */
  printPort = 1

  /**
   * 打印类型
   */
  serviceType: ServiceTypeEnum = null

  to() {
    const dto = new BatchPrintCertificatesRequest()
    // dto.studentNos = this.studentNos
    dto.fileType = this.fileType
    dto.printType = this.printType || undefined
    dto.merge = this.isMerge || undefined
    dto.printSource = this.printSource
    dto.printPort = 1
    dto.servicerType = this.serviceType
    return dto
  }

  /**
   * 学员批量打印入参
   */
  toStudent() {
    const dto = new StudentBatchPrintCertificatesRequest()
    // dto.studentNos = this.studentNos
    dto.fileType = this.fileType
    dto.printType = this.printType || undefined
    dto.merge = this.isMerge || undefined
    dto.templateId = this.templateId || undefined
    dto.printSource = this.printSource
    dto.printPort = 2
    dto.servicerType = this.serviceType || undefined
    return dto
  }
}

export default PrintCertificationsVo
