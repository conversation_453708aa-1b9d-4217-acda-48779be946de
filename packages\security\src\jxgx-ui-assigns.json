[{"name": "WXGLY", "meta": {"title": "网校管理员", "isMenu": true, "ownerGroup": ["WXGLY"], "roles": ["FXS", "GYS", "NZGYS", "NZFXS", "NZFXSJCB", "NZGYSJCB", "ZTGLY", "JTJFGLY", "WXGLY", "DQGLY"], "permissionMap": {"query": {"name": "查询(必选)", "ownerGroup": ["WXGLY.query"], "graphql": ["ms-config-v1.query.getCurrentFrontendConfig:{\"serviceName\":\"ms-config-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForServiceByDomainName:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.query.getDistributionService:{\"authorizationRequired\":false}", "ms-identity-authentication-v1.query.getAccessTokenValue:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.findIndustryPropertyByServiceIdAndPropertyIdAndPropertyType:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getAdminInfoInMyself:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjy-distributor-admin-v1.query.isOnlineSchoolContractExpired:{\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.isOnlineSchoolContractExpired:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.query.findFunctionalAuthorityByRoleIdsNew:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.query.findRoleByAccountId:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-autolearning-online-school-smart-learning-service-v1.query.queryOnlineSchoolSmartLearningServiceConfigByServicerId:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applyCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptchaForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCodeForFXS:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwd:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountPwdCaptcha:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateByAccountId:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyAuthenticateBySmsCode:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-account-v1.mutation.changeAuthorizationUnitInfoList:{\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.applySmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.validSmsCode:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentAccount:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.bindPhoneForCurrentUser:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.validCaptcha:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getOnlineSchoolConfig:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getSmsCodeAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-secure-config-v1.query.getFailedAuthConfig:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.forgetPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":false}", "ms-course-play-resource-v1.mutation.applyCoursePreview:{\"serviceName\":\"ms-course-play-resource-v1\",\"authorizationRequired\":false}", "platform-training-channel-v1.mutation.updateSaleSetting:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursePackageInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseInSchemeInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageCoursewareSupplierInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyChangeIdentityAuthentication:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticate:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyProxyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-identity-authentication-v1.mutation.applyReAuthenticateBasic:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryPayFlowStatus:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-payment-v1.mutation.getBatchPreParePayResult:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.queryQrScanPromptByPayFlowNo:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.deliveredOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":true}", "ms-assess-v1.mutation.batchRePushSchemeIndicator:{\"serviceName\":\"ms-assess-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.createJxjyCommonMenu:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.temporaryOrderUpdateOrderInfoInSubject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicercontract-v1.mutation.enableOSContract:{\"serviceName\":\"ms-servicercontract-v1\",\"authorizationRequired\":false}", "ms-servicer-v1.mutation.applyForService:{\"serviceName\":\"ms-servicer-v1\",\"authorizationRequired\":false}", "ms-identity-authentication-v1.mutation.applyThirdPartyIdentity:{\"serviceName\":\"ms-identity-authentication-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.tryComparePhoto:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.updateUserDatumCodePhoto:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}", "ms-learningscheme-v1.mutation.refreshConfigAndUpdate:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":false}", "ms-inspection-v1.mutation.executeAntiInspection:{\"serviceName\":\"ms-inspection-v1\",\"authorizationRequired\":false}", "platform-account-v1.mutation.compensatingStudentAccount:{\"serviceName\":\"platform-account-v1\",\"authorizationRequired\":false}", "platform-jxjypxtypt-distribution-service-v1.mutation.openDistributionService:{\"authorizationRequired\":false}", "ms-order-repair-data-v1.mutation.orderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-order-repair-data-v1.mutation.batchOrderMigration:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.distributionAuthBatchProduct:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.createPricePlan:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-distribution-repairdata-v1.mutation.pricePlanSetSaleChannel:{\"serviceName\":\"ms-distribution-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissReturnOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listMissExchangeOrderByPage:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.clearStudentLearningRule:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-teachingplan-studentattendance-sds-v1.mutation.oneClickClock:{\"serviceName\":\"ms-teachingplan-v1\",\"authorizationRequired\":false}"], "roles": ["FXS", "GYS", "NZGYS", "NZFXS", "NZFXSJCB", "NZGYSJCB", "ZTGLY", "JTJFGLY", "WXGLY", "DQGLY"], "ext": {"diffSchool": ["jxgx"]}}}, "sort": "2", "diffSchool": ["jxgx"]}, "specifier": "WXGLY", "path": "", "pathSegments": ["WXGLY"], "children": [{"name": "statistic", "specifier": "Statistic", "path": "/statistic", "pathSegments": ["WXGLY", "statistic"], "component": "@/packages/routers/src/basic-router/statistic.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "统计报表", "sort": 5, "icon": "icon-shu<PERSON>", "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.statistic"], "diffSchool": ["jxgx"]}, "children": [{"name": "statistic-learning-statistic-index", "specifier": "StatisticLearningStatisticIndex", "path": "learning-statistic", "pathSegments": ["WXGLY", "statistic", "learning-statistic"], "component": "@/packages/routers/src/basic-router/statistic/learning-statistic/index.vue", "meta": {"isMenu": true, "title": "学员学习明细", "sort": 5, "icon": "icon-mingxi", "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {"query": {"roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "name": "查询", "ownerGroup": ["WXGLY.statistic.learning-statistic.query"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegionInDistribution:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicerManageRegion:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInTrainingChannelV2:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInTrainingChannel:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicerV2:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiBasicConfigInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiModuleConfigurationInfoInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}", "fxnl-query-common-disposition-information.query.getIndustriesByServiceIdInDistributor:{\"authorizationRequired\":true}", "fxnl-query-common-disposition-information.query.getYearsByServiceIdInDistributor:{\"authorizationRequired\":true}"], "ext": {"diffSchool": ["jxgx"]}}, "exceptionManagement": {"name": "学习数据异常管理", "ownerGroup": ["WXGLY.statistic.learning-statistic.exceptionManagement"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "learnAnomalousList": {"name": "学习数据异常管理-学习规则", "ownerGroup": ["WXGLY.statistic.learning-statistic.learnAnomalousList"], "graphql": ["student-course-learning-query-back-gateway.query.pageLearningResultErrorInTrainingChannel:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.pageLearningResultErrorInServicer:{\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.reGenerateStudentTrainingResultSimulateInServicer:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "intelligentLearningList": {"name": "学习数据异常管理-智能学习", "ownerGroup": ["WXGLY.statistic.learning-statistic.intelligentLearningList"], "graphql": ["ms-autolearning-student-auto-learning-task-result-v1.query.queryByPage:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-autolearning-student-auto-learning-task-result-v1.query.findLastFailSubTaskByMainTaskIdList:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "export": {"name": "导出", "ownerGroup": ["WXGLY.statistic.learning-statistic.export"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInServicerManageRegion:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInDistributor:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInDistributor:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInDistributor:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInTrainingChannel:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInTrainingChannel:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportStudentSchemeLearningExcelInTrainingChannel:{\"authorizationRequired\":true}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportBlendedStudentSchemeLearningExcelInServicer:{\"authorizationRequired\":false}", "diff-ms-data-export-front-gateway-DataExportBackstage.query.exportFaceToFaceStudentSchemeLearningExcelInServicer:{\"authorizationRequired\":false}", "jxgx-data-export-gateway-backstage.query.exportStudentSchemeLearningExcelInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "onlineClassTable": {"name": "网授班列表", "ownerGroup": ["WXGLY.statistic.learning-statistic.onlineClassTable"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pagePlanItemAttendanceInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "allSync": {"name": "批量同步", "ownerGroup": ["WXGLY.statistic.learning-statistic.allSync"], "graphql": ["platform-jxjypxtypt-student-learning-backstage.query.rePushStudentTrainingResultToGatewayInServicerV2:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "examDetail": {"name": "考试详情", "ownerGroup": ["WXGLY.statistic.learning-statistic.examDetail"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "testDetail": {"name": "测验详情", "ownerGroup": ["WXGLY.statistic.learning-statistic.testDetail"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "toLog": {"name": "查阅监管日志", "ownerGroup": ["WXGLY.statistic.learning-statistic.toLog"], "graphql": [], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "userInfo": {"name": "用户信息组件", "ownerGroup": ["WXGLY.statistic.learning-statistic.userInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "classInfo": {"name": "培训班信息组件", "ownerGroup": ["WXGLY.statistic.learning-statistic.classInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigByRequestInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "studyLog": {"name": "学习日志组件", "ownerGroup": ["WXGLY.statistic.learning-statistic.studyLog"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.pageSupervisionCourseDetailsInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.listLearningSupervisionBehaviorInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "DQGLY", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "toLearningLog": {"name": "查阅学习日志", "ownerGroup": ["WXGLY.statistic.learning-statistic.toLearningLog"], "graphql": [], "roles": ["WXGLY", "DQGLY", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "queryInfo": {"name": "查询学习日志", "ownerGroup": ["WXGLY.statistic.learning-statistic.queryInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningLogsInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getLearningType:{\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "faceClassTable": {"name": "面授班列表", "ownerGroup": ["WXGLY.statistic.learning-statistic.faceClassTable"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pagePlanItemAttendanceInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "mixedClassTable": {"name": "混合班列表", "ownerGroup": ["WXGLY.statistic.learning-statistic.mixedClassTable"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCourseV2InServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pagePlanItemAttendanceInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "attendanceDetail": {"name": "考勤详情", "ownerGroup": ["WXGLY.statistic.learning-statistic.attendanceDetail"], "graphql": [], "roles": [], "ext": {"diffSchool": ["jxgx"]}}}, "ownerGroup": ["WXGLY.statistic.learning-statistic"], "diffSchool": ["jxgx"]}}]}, {"name": "training", "specifier": "Training", "path": "/training", "pathSegments": ["WXGLY", "training"], "component": "@/packages/routers/src/basic-router/training.vue", "meta": {"openWhenInit": false, "closeAble": false, "isMenu": true, "title": "培训管理", "sort": 4, "icon": "icon-peixun", "roles": ["WXGLY", "FXS", "GYS", "ZTGLY", "NZFXS", "NZFXSJCB"], "permissionMap": {}, "ownerGroup": ["WXGLY.training"], "diffSchool": ["jxgx"]}, "children": [{"name": "training-customer-service", "specifier": "TrainingCustomerService", "path": "customer-service", "pathSegments": ["WXGLY", "training", "customer-service"], "component": "@/packages/routers/src/basic-router/training/customer-service.vue", "meta": {"isMenu": true, "title": "客服管理", "sort": 6, "icon": "icon-kefu", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {}, "ownerGroup": ["WXGLY.training.customer-service"], "diffSchool": ["jxgx"]}, "children": [{"name": "training-customer-service-personal-index", "specifier": "TrainingCustomerServicePersonalIndex", "path": "personal", "pathSegments": ["WXGLY", "training", "customer-service", "personal"], "component": "@/packages/routers/src/basic-router/training/customer-service/personal/index.vue", "meta": {"isMenu": true, "title": "业务咨询", "sort": 1, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS"], "permissionMap": {"query": {"name": "查询", "ownerGroup": ["WXGLY.training.customer-service.personal.query"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiBasicConfigInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.getAntiModuleConfigurationInfoInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryNewestTaskResultByQualificationIds:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInformationInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "basicData": {"name": "学员信息", "ownerGroup": ["WXGLY.training.customer-service.personal.basicData"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getDockingTycAndQcc:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "editCompany": {"name": "编辑基础信息", "ownerGroup": ["WXGLY.training.customer-service.personal.editCompany"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "confirmResetPwd": {"name": "重置密码", "ownerGroup": ["WXGLY.training.customer-service.personal.confirmResetPwd"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.ResetPassword:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "editProfessionalCategoryName": {"name": "编辑人社专业类别", "ownerGroup": ["WXGLY.training.customer-service.personal.editProfessionalCategoryName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "editProfessionalQualification": {"name": "编辑人社职称等级", "ownerGroup": ["WXGLY.training.customer-service.personal.editProfessionalQualification"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "deleteAttachmentInfoItem": {"name": "删除行业证书", "ownerGroup": ["WXGLY.training.customer-service.personal.deleteAttachmentInfoItem"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.deleteCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "editPersonnelCategoryName": {"name": "编辑卫生人员类别", "ownerGroup": ["WXGLY.training.customer-service.personal.editPersonnelCategoryName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "editPositionCategoryName": {"name": "编辑卫生岗位类别", "ownerGroup": ["WXGLY.training.customer-service.personal.editPositionCategoryName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "editProfessionalLevelName": {"name": "编辑工勤技术等级", "ownerGroup": ["WXGLY.training.customer-service.personal.editProfessionalLevelName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "editJobCategoryIdName": {"name": "编辑工勤工种", "ownerGroup": ["WXGLY.training.customer-service.personal.editJobCategoryIdName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "editStudyperiodSubjectName": {"name": "编辑学段学科", "ownerGroup": ["WXGLY.training.customer-service.personal.editStudyperiodSubjectName"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getStudentRegisterFormConstraint:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentByAdmin:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "updateAndEditCertifition": {"name": "新增/编辑证书抽屉", "ownerGroup": ["WXGLY.training.customer-service.personal.updateAndEditCertifition"], "graphql": ["ms-basicdata-domain-gateway-v1.mutation.addStudentCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-domain-gateway-v1.mutation.updateStudentCertificateInfo:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "unbundle": {"name": "微信解绑", "ownerGroup": ["WXGLY.training.customer-service.personal.unbundle"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-domain-gateway-v1.mutation.unbindWeChatOpenPlatform:{\"serviceName\":\"ms-basicdata-domain-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "revisePhotoTime": {"name": "调整基准照次数", "ownerGroup": ["WXGLY.training.customer-service.personal.revisePhotoTime"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.changeAllowUpdateCount:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "deletePhoto": {"name": "删除基准照", "ownerGroup": ["WXGLY.training.customer-service.personal.deletePhoto"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.getUserReferencePhotoResponseInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-anticheat-v1.mutation.doDeleteUserDatum:{\"serviceName\":\"ms-anticheat-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "studyDontent": {"name": "学习内容", "ownerGroup": ["WXGLY.training.customer-service.personal.studyDontent"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryNewestTaskResultByQualificationIds:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInformationInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningExperienceParticipatedInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listLearningExperienceTopic:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-general-supervision-query-front-gateway-Forestage.query.getAntiBasicConfigDetailInScheme:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "studyQuery": {"name": "查询", "ownerGroup": ["WXGLY.training.customer-service.personal.studyQuery"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryNewestTaskResultByQualificationIds:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInformationInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "showIntelligentLearningTag": {"name": "是否展示智能学习标识", "ownerGroup": ["WXGLY.training.customer-service.personal.showIntelligentLearningTag"], "graphql": [], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}, "qualified": {"name": "一键合格", "ownerGroup": ["WXGLY.training.customer-service.personal.qualified"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-learningscheme-v1.mutation.oneKeyPass:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "learningSituation": {"name": "学习情况", "ownerGroup": ["WXGLY.training.customer-service.personal.learningSituation"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageStudentCourseInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareLearningRecordInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageCoursewareInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getStudentSchemeLearningDetailInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exam-query-front-gateway-ExamQueryBackStage.query.pageCourseQuizRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "studty": {"name": "一键学习", "ownerGroup": ["WXGLY.training.customer-service.personal.studty"], "graphql": ["ms-learningscheme-v1.mutation.applyAutoLearningTokenForManage:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.applyCourseImmediatelyLearning:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}", "ms-autonomouscourselearningscene-v1.mutation.applyCourseImmediatelyLearning:{\"serviceName\":\"ms-autonomouscourselearningscene-v1\",\"authorizationRequired\":true}", "ms-studentcourselearning-v1.mutation.commitImmediatelyCourseLearning:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.applyCoursewareImmediatelyLearning:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}", "ms-autonomouscourselearningscene-v1.mutation.applyCoursewareImmediatelyLearning:{\"serviceName\":\"ms-autonomouscourselearningscene-v1\",\"authorizationRequired\":true}", "ms-studentcourselearning-v1.mutation.commitImmediatelyCoursewareLearning:{\"serviceName\":\"ms-studentcourselearning-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "deleteCourse": {"name": "删除课程", "ownerGroup": ["WXGLY.training.customer-service.personal.deleteCourse"], "graphql": ["ms-learningscheme-v1.mutation.applyRelearnTokenForManage:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-autonomouscourselearningscene-v1.mutation.invalidStudentCourse:{\"serviceName\":\"ms-autonomouscourselearningscene-v1\",\"authorizationRequired\":true}", "ms-choosecourselearningscene-v1.mutation.invalidStudentCourse:{\"serviceName\":\"ms-choosecourselearningscene-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "toLog": {"name": "监管日志", "ownerGroup": ["WXGLY.training.customer-service.personal.toLog"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "userInfo": {"name": "用户信息组件", "ownerGroup": ["WXGLY.training.customer-service.personal.userInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "classInfo": {"name": "培训班信息组件", "ownerGroup": ["WXGLY.training.customer-service.personal.classInfo"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigByRequestInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "studyLog": {"name": "学习日志组件", "ownerGroup": ["WXGLY.training.customer-service.personal.studyLog"], "graphql": ["ms-general-supervision-query-front-gateway-Backstage.query.pageSupervisionCourseDetailsInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-general-supervision-query-front-gateway-Backstage.query.listLearningSupervisionBehaviorInServicer:{\"serviceName\":\"ms-general-supervision-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "attendanceDetailsDrawer": {"name": "考勤详情", "ownerGroup": ["WXGLY.training.customer-service.personal.attendanceDetailsDrawer"], "graphql": ["ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pagePlanItemAttendanceInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getMySchemeIssueConfigInMySelf:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "deleteExamRecord": {"name": "删除考试记录", "ownerGroup": ["WXGLY.training.customer-service.personal.deleteExamRecord"], "graphql": ["ms-exam-query-front-gateway-ExamQueryBackStage.query.pageExaminationRecordInSubProject:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-examination-v1.mutation.invalidStudentExamAnswerPaper:{\"serviceName\":\"ms-examination-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "deleteExperience": {"name": "删除学习心得", "ownerGroup": ["WXGLY.training.customer-service.personal.deleteExperience"], "graphql": ["ms-course-learning-query-front-gateway-CourseLearningBackstage.query.pageLearningExperienceParticipatedInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.listLearningExperienceTopic:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-knowledge-v1.mutation.removeStudentLearningExperience:{\"serviceName\":\"ms-knowledge-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "surveyQuestionnaire": {"name": "调研问卷", "ownerGroup": ["WXGLY.training.customer-service.personal.surveyQuestionnaire"], "graphql": ["ms-exam-query-front-gateway-QuestionnaireQueryBackStage.query.pageQuestionnaireSchemeInServicer:{\"serviceName\":\"ms-exam-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "queryExchangeClass": {"name": "查看换班信息", "ownerGroup": ["WXGLY.training.customer-service.personal.queryExchangeClass"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.pageProductPricingSchemeInSupplier:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "exchangeClass": {"name": "换班", "ownerGroup": ["WXGLY.training.customer-service.personal.exchangeClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listALLIndustryPropertyRootByCategory:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.getSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-order-v1.mutation.sellerApplyExchange:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "continueExchangeClass": {"name": "继续换班", "ownerGroup": ["WXGLY.training.customer-service.personal.continueExchangeClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryRecycleResouce:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryDelivery:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "queryMixExchangeClass": {"name": "查看面网授换班信息", "ownerGroup": ["WXGLY.training.customer-service.personal.queryMixExchangeClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "exchangeMixClass": {"name": "面网授班换班", "ownerGroup": ["WXGLY.training.customer-service.personal.exchangeMixClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "exchangeMixPeriod": {"name": "面网授班换期", "ownerGroup": ["WXGLY.training.customer-service.personal.exchangeMixPeriod"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-information-gateway.query.getCurrentTime:{\"authorizationRequired\":false}", "ms-learningscheme-enrollment-v1.mutation.changeIssue:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "viewExchangeMixDetail": {"name": "面网授班换班换期记录详情查看", "ownerGroup": ["WXGLY.training.customer-service.personal.viewExchangeMixDetail"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "continueExchangeMixTrainClass": {"name": "面网授班继续换班", "ownerGroup": ["WXGLY.training.customer-service.personal.continueExchangeMixTrainClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryRecycleResouce:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryDelivery:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "queryFaceExchangeClass": {"name": "查看面授换班信息", "ownerGroup": ["WXGLY.training.customer-service.personal.queryFaceExchangeClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "exchangeFaceClass": {"name": "面授班换班", "ownerGroup": ["WXGLY.training.customer-service.personal.exchangeFaceClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "exchangeFacePeriod": {"name": "面授班换期", "ownerGroup": ["WXGLY.training.customer-service.personal.exchangeFacePeriod"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-information-gateway.query.getCurrentTime:{\"authorizationRequired\":false}", "ms-learningscheme-enrollment-v1.mutation.changeIssue:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "viewExchangeFacexDetail": {"name": "面授班换班换期记录详情查看", "ownerGroup": ["WXGLY.training.customer-service.personal.viewExchangeFacexDetail"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "continueExchangeFaceTrainClass": {"name": "面授班继续换班", "ownerGroup": ["WXGLY.training.customer-service.personal.continueExchangeFaceTrainClass"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listBuyerAllCommodityInSerivicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryRecycleResouce:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}", "ms-exchange-order-v1.mutation.retryDelivery:{\"serviceName\":\"ms-exchange-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "orderInfo": {"name": "订单详情", "ownerGroup": ["WXGLY.training.customer-service.personal.orderInfo"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "detail": {"name": "详情", "ownerGroup": ["WXGLY.training.customer-service.personal.detail"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listReturnReasonInfoInSubProject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-order-v1.mutation.sellerApplyAfterSale:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "queryRelevancyOrder": {"name": "详情-查看关联换班订单", "ownerGroup": ["WXGLY.training.customer-service.personal.queryRelevancyOrder"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInInDistributor:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueLogInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueLogInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "queryChangeClassDetail": {"name": "详情-查看换班/换期详情", "ownerGroup": ["WXGLY.training.customer-service.personal.queryChangeClassDetail"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getExchangeOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getExchangeOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "refundOrder": {"name": "详情-退款操作", "ownerGroup": ["WXGLY.training.customer-service.personal.refundOrder"], "graphql": ["ms-order-v1.mutation.sellerApplyAfterSale:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listReturnReasonInfoInSubProject:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "fillToInvoice": {"name": "补要发票", "ownerGroup": ["WXGLY.training.customer-service.personal.fillToInvoice"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.query.queryShippingMethodsForSchool:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.applyInvoice:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "invoiceInfo": {"name": "发票信息", "ownerGroup": ["WXGLY.training.customer-service.personal.invoiceInfo"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.mutation.updateElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflinePaperInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceDeliveryInfoWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.deliveryInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.pickupInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "invoiceAutomatic": {"name": "电子发票", "ownerGroup": ["WXGLY.training.customer-service.personal.invoiceAutomatic"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.mutation.updateElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "invoiceOffline": {"name": "电子发票（线下开票）", "ownerGroup": ["WXGLY.training.customer-service.personal.invoiceOffline"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "electronicSpecialInvoice": {"name": "增值税电子专用发票（线下开票）", "ownerGroup": ["WXGLY.training.customer-service.personal.electronicSpecialInvoice"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineSpecialPaperElectronicInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "invoiceIncrement": {"name": "增值税专票", "ownerGroup": ["WXGLY.training.customer-service.personal.invoiceIncrement"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflinePaperInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "invoiceDistribution": {"name": "发票配送", "ownerGroup": ["WXGLY.training.customer-service.personal.invoiceDistribution"], "graphql": ["ms-offlineinvoice-v1.mutation.importOfflineInvoiceDeliveryInfoWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-offlineinvoice-v1.mutation.deliveryInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.pickupInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "refundInfo": {"name": "退款信息", "ownerGroup": ["WXGLY.training.customer-service.personal.refundInfo"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryByIdInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.listBusinessDictionaryAcrossTypeBySalveId:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "refundDetail": {"name": "退款详情详情", "ownerGroup": ["WXGLY.training.customer-service.personal.refundDetail"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "approve": {"name": "退款审批", "ownerGroup": ["WXGLY.training.customer-service.personal.approve"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.sellerCancelReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.rejectReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.agreeReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "retryRecycleRefund": {"name": "重新回收资源", "ownerGroup": ["WXGLY.training.customer-service.personal.retryRecycleRefund"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.retryRecycleResource:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "continueRefund": {"name": "继续退款", "ownerGroup": ["WXGLY.training.customer-service.personal.continueRefund"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.retryRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "confirmRefund": {"name": "确认退款", "ownerGroup": ["WXGLY.training.customer-service.personal.confirmRefund"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.confirmRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "studyRecords": {"name": "学习档案", "ownerGroup": ["WXGLY.training.customer-service.personal.studyRecords"], "graphql": ["ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-course-learning-query-front-gateway-CourseLearningBackstage.query.statisticsLearningExperienceParticipatedBySchemeIdInServicer:{\"serviceName\":\"ms-course-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "student-course-learning-query-back-gateway.query.getStudentTrainingResultSimulateResponseInServicer:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-student-auto-learning-task-result-v1.query.queryNewestTaskResultByQualificationIds:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageStudentSchemeLearningInformationInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-jxjypxtypt-jxgx-certificate.mutation.singlePrintCertificate:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "lookCourseCertification": {"name": "查阅学时证明，重新推送", "ownerGroup": ["WXGLY.training.customer-service.personal.lookCourseCertification"], "graphql": ["platform-certificate-v1.mutation.printCertificate:{\"authorizationRequired\":true}", "platform-jxjypxtypt-student-learning-backstage.query.rePushStudentTrainingResultToGatewayInServicerV2:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}}, "ownerGroup": ["WXGLY.training.customer-service.personal"], "diffSchool": ["jxgx"]}}]}, {"name": "training-trade", "specifier": "TrainingTrade", "path": "trade", "pathSegments": ["WXGLY", "training", "trade"], "component": "@/packages/routers/src/basic-router/training/trade.vue", "meta": {"isMenu": true, "title": "交易管理", "sort": 3, "icon": "icon-<PERSON><PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS", "ZTGLY", "NZFXS", "NZFXSJCB"], "permissionMap": {}, "ownerGroup": ["WXGLY.training.trade"], "diffSchool": ["jxgx"]}, "children": [{"name": "training-trade-invoice", "specifier": "TrainingTradeInvoice", "path": "invoice", "pathSegments": ["WXGLY", "training", "trade", "invoice"], "component": "@/packages/routers/src/basic-router/training/trade/invoice.vue", "meta": {"isMenu": true, "title": "发票管理", "sort": 3, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS", "ZTGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.training.trade.invoice"], "diffSchool": ["jxgx"]}, "children": [{"name": "training-trade-invoice-personal-index", "specifier": "TrainingTradeInvoicePersonalIndex", "path": "personal", "pathSegments": ["WXGLY", "training", "trade", "invoice", "personal"], "component": "@/packages/routers/src/basic-router/training/trade/invoice/personal/index.vue", "meta": {"isMenu": true, "title": "个人报名发票", "sort": 1, "roles": ["WXGLY", "FXS", "GYS", "ZTGLY"], "permissionMap": {"personalBill": {"name": "个人报名发票", "ownerGroup": ["WXGLY.training.trade.invoice.personal.personalBill"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "autoInvoice": {"name": "电子普通发票（自动开票）", "ownerGroup": ["WXGLY.training.trade.invoice.personal.autoInvoice"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getInvoiceAutoBillPolicyInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "autoInvoiceZt": {"name": "电子普通发票（专题）", "ownerGroup": ["WXGLY.training.trade.invoice.personal.autoInvoiceZt"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getInvoiceAutoBillPolicyInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "autoInvoiceExport": {"name": "电子普通发票（自动开票）-导出数据", "ownerGroup": ["WXGLY.training.trade.invoice.personal.autoInvoiceExport"], "graphql": ["jxjy-data-export-gateway-backstage.query.exportOnlineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "autoInvoiceExportZt": {"name": "电子普通发票（专题）-导出数据", "ownerGroup": ["WXGLY.training.trade.invoice.personal.autoInvoiceExportZt"], "graphql": ["jxjy-data-export-gateway-backstage.query.exportOnlineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "autoInvoiceBatch": {"name": "批量开票", "ownerGroup": ["WXGLY.training.trade.invoice.personal.autoInvoiceBatch"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.mutation.issueElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "reBatchOpenInvoice": {"name": "重新批量开通发票", "ownerGroup": ["WXGLY.training.trade.invoice.personal.reBatchOpenInvoice"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.mutation.retryIssueElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "autoInvoiceTime": {"name": "设置自动开票时间", "ownerGroup": ["WXGLY.training.trade.invoice.personal.autoInvoiceTime"], "graphql": ["ms-bill-v1.mutation.addOrUpdateElectronicInvoiceAutoConfig:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "editBill": {"name": "修改电子普通发票信息", "ownerGroup": ["WXGLY.training.trade.invoice.personal.editBill"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.getOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.mutation.updateElectronicInvoice:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "reOpenInvoice": {"name": "重新开通发票", "ownerGroup": ["WXGLY.training.trade.invoice.personal.reOpenInvoice"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "operationRecord": {"name": "操作记录", "ownerGroup": ["WXGLY.training.trade.invoice.personal.operationRecord"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.listOnlineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "redInvoice": {"name": "冲红发票", "ownerGroup": ["WXGLY.training.trade.invoice.personal.redInvoice"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOnlineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "redInvoiceZt": {"name": "冲红发票(专题)", "ownerGroup": ["WXGLY.training.trade.invoice.personal.redInvoiceZt"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfBlueTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-bill-v1.query.getInvoicingServiceLogOfRedTicket:{\"serviceName\":\"ms-bill-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOnlineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOnlineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOnlineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "personalBillZt": {"name": "个人报名发票(专题)", "ownerGroup": ["WXGLY.training.trade.invoice.personal.personalBillZt"], "graphql": [], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "autoBill": {"name": "自动开票列表", "ownerGroup": ["WXGLY.training.trade.invoice.personal.autoBill"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "autoBillZt": {"name": "自动开票列表(专题)", "ownerGroup": ["WXGLY.training.trade.invoice.personal.autoBillZt"], "graphql": [], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "invoiceOffline": {"name": "线下开票", "ownerGroup": ["WXGLY.training.trade.invoice.personal.invoiceOffline"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOnlineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOfflineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.resetInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.issueOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "invoiceOfflineZt": {"name": "线下开票(专题)", "ownerGroup": ["WXGLY.training.trade.invoice.personal.invoiceOfflineZt"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOnlineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOfflineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.resetInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.issueOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "electronicSpecialInvoice": {"name": "增值税电子专用发票(线下开票)", "ownerGroup": ["WXGLY.training.trade.invoice.personal.electronicSpecialInvoice"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOnlineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOfflineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.resetInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.issueOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineSpecialPaperElectronicInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "electronicSpecialInvoiceZt": {"name": "增值税电子专用发票(专题)", "ownerGroup": ["WXGLY.training.trade.invoice.personal.electronicSpecialInvoiceZt"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOnlineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOfflineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.resetInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.issueOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflineSpecialPaperElectronicInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "specialInvoice": {"name": "专用发票", "ownerGroup": ["WXGLY.training.trade.invoice.personal.specialInvoice"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOnlineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOfflineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.resetInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.issueOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflinePaperInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "specialInvoiceZt": {"name": "专用发票(专题)", "ownerGroup": ["WXGLY.training.trade.invoice.personal.specialInvoiceZt"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOnlineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.listOfflineInvoiceOperationRecord:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.importOfflineInvoiceWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.resetInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.issueOfflineInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listChildRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getDeliveryChannelListInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.updateOfflinePaperInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInTrainingChannelForJxjy:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportOfflineInvoiceInServicerForJxjy:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "invoiceDistribution": {"name": "发票配送", "ownerGroup": ["WXGLY.training.trade.invoice.personal.invoiceDistribution"], "graphql": ["ms-offlineinvoice-v1.mutation.importOfflineInvoiceDeliveryInfoWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.deliveryInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.pickupInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportInvoiceDeliveryInTrainingChannel:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "invoiceDistributionZt": {"name": "发票配送(专题)", "ownerGroup": ["WXGLY.training.trade.invoice.personal.invoiceDistributionZt"], "graphql": ["ms-offlineinvoice-v1.mutation.importOfflineInvoiceDeliveryInfoWithServiceId:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-backstage.query.listRegionByCodeInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOfflineInvoiceInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.deliveryInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "ms-offlineinvoice-v1.mutation.pickupInvoice:{\"serviceName\":\"ms-offlineinvoice-v1\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportInvoiceDeliveryInTrainingChannel:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"authorizationRequired\":true}", "jxgx-data-export-gateway-backstage.query.exportInvoiceDeliveryInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}}, "ownerGroup": ["WXGLY.training.trade.invoice.personal"], "diffSchool": ["jxgx"]}}]}, {"name": "training-trade-order", "specifier": "TrainingTradeOrder", "path": "order", "pathSegments": ["WXGLY", "training", "trade", "order"], "component": "@/packages/routers/src/basic-router/training/trade/order.vue", "meta": {"isMenu": true, "title": "订单管理", "sort": 1, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.training.trade.order"], "diffSchool": ["jxgx"]}, "children": [{"name": "training-trade-order-personal-index", "specifier": "TrainingTradeOrderPersonalIndex", "path": "personal", "pathSegments": ["WXGLY", "training", "trade", "order", "personal"], "component": "@/packages/routers/src/basic-router/training/trade/order/personal/index.vue", "meta": {"isMenu": true, "title": "个人报名订单", "sort": 1, "icon": "icon_guanli", "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {"query": {"roles": ["WXGLY", "FXS", "GYS"], "name": "查询", "ownerGroup": ["WXGLY.training.trade.order.personal.query"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInDistribution:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "ext": {"diffSchool": ["jxgx"]}}, "queryFx": {"name": "查询（分销）", "ownerGroup": ["WXGLY.training.trade.order.personal.queryFx"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.listMyDistributorCommoditySkuPropertyInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageMyDistributorCommodityInDistributor:{\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInDistribution:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "queryZt": {"name": "查询（专题）", "ownerGroup": ["WXGLY.training.trade.order.personal.queryZt"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "editInvoicePopup": {"name": "选择收款账号", "ownerGroup": ["WXGLY.training.trade.order.personal.editInvoicePopup"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInDistribution:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "search": {"name": "查询", "ownerGroup": ["WXGLY.training.trade.order.personal.search"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "searchFx": {"name": "查询（分销）", "ownerGroup": ["WXGLY.training.trade.order.personal.searchFx"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "searchZt": {"name": "查询（专题）", "ownerGroup": ["WXGLY.training.trade.order.personal.searchZt"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "export": {"name": "导出", "ownerGroup": ["WXGLY.training.trade.order.personal.export"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "exportFx": {"name": "导出（分销）", "ownerGroup": ["WXGLY.training.trade.order.personal.exportFx"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "exportZt": {"name": "导出（专题）", "ownerGroup": ["WXGLY.training.trade.order.personal.exportZt"], "graphql": ["jxjy-data-export-gateway-backstage.query.exportOrderExcelInTrainingChannel:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "detail": {"name": "详情（入口控制）", "ownerGroup": ["WXGLY.training.trade.order.personal.detail"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "closeOrder": {"name": "关闭订单", "ownerGroup": ["WXGLY.training.trade.order.personal.closeOrder"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getBatchOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-configuration-v1.mutation.preparePurchaseChannel:{\"serviceName\":\"ms-trade-configuration-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getPurchaseChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-payment-v1.query.prepareRepay:{\"serviceName\":\"ms-payment-v1\",\"authorizationRequired\":true}", "ms-order-v1.mutation.sellerCancelOrder:{\"serviceName\":\"ms-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}}, "ownerGroup": ["WXGLY.training.trade.order.personal"], "diffSchool": ["jxgx"]}}]}, {"name": "training-trade-reconciliation", "specifier": "TrainingTradeReconciliation", "path": "reconciliation", "pathSegments": ["WXGLY", "training", "trade", "reconciliation"], "component": "@/packages/routers/src/basic-router/training/trade/reconciliation.vue", "meta": {"isMenu": true, "title": "对账管理", "sort": 4, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.training.trade.reconciliation"], "diffSchool": ["jxgx"]}, "children": [{"name": "training-trade-reconciliation-personal-index", "specifier": "TrainingTradeReconciliationPersonalIndex", "path": "personal", "pathSegments": ["WXGLY", "training", "trade", "reconciliation", "personal"], "component": "@/packages/routers/src/basic-router/training/trade/reconciliation/personal/index.vue", "meta": {"isMenu": true, "title": "个人报名对账", "sort": 1, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {"query": {"name": "个人报名对账", "ownerGroup": ["WXGLY.training.trade.reconciliation.personal.query"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "orderReconciliation": {"name": "报名订单对账", "ownerGroup": ["WXGLY.training.trade.reconciliation.personal.orderReconciliation"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "orderReconciliationFx": {"name": "报名订单对账（分销）", "ownerGroup": ["WXGLY.training.trade.reconciliation.personal.orderReconciliationFx"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "orderReconciliationZt": {"name": "报名订单对账（专题）", "ownerGroup": ["WXGLY.training.trade.reconciliation.personal.orderReconciliationZt"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "export": {"name": "导出", "ownerGroup": ["WXGLY.training.trade.reconciliation.personal.export"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportReconciliationExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportReturnReconciliationExcelInServicer:{\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "exportFx": {"name": "导出（分销）", "ownerGroup": ["WXGLY.training.trade.reconciliation.personal.exportFx"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportReconciliationExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.exportReturnReconciliationExcelInDistributor:{\"authorizationRequired\":true}"], "roles": ["NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "exportZt": {"name": "导出（专题）", "ownerGroup": ["WXGLY.training.trade.reconciliation.personal.exportZt"], "graphql": ["jxjy-data-export-gateway-backstage.query.exportReconciliationExcelInTrainingChannel:{\"authorizationRequired\":true}", "jxjy-data-export-gateway-backstage.query.exportReturnReconciliationExcelInTrainingChannel:{\"authorizationRequired\":true}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "editInvoicePopup": {"name": "选择收款账号", "ownerGroup": ["WXGLY.training.trade.reconciliation.personal.editInvoicePopup"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInDistribution:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "refundReconciliation": {"name": "退款订单对账", "ownerGroup": ["WXGLY.training.trade.reconciliation.personal.refundReconciliation"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "refundReconciliationFx": {"name": "分销退款订单对账", "ownerGroup": ["WXGLY.training.trade.reconciliation.personal.refundReconciliationFx"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "refundReconciliationZt": {"name": "专题退款订单对账", "ownerGroup": ["WXGLY.training.trade.reconciliation.personal.refundReconciliationZt"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}}, "ownerGroup": ["WXGLY.training.trade.reconciliation.personal"], "diffSchool": ["jxgx"]}}]}, {"name": "training-trade-refund", "specifier": "TrainingTradeRefund", "path": "refund", "pathSegments": ["WXGLY", "training", "trade", "refund"], "component": "@/packages/routers/src/basic-router/training/trade/refund.vue", "meta": {"isMenu": true, "title": "退款管理", "sort": 2, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.training.trade.refund"], "diffSchool": ["jxgx"]}, "children": [{"name": "training-trade-refund-personal-index", "specifier": "TrainingTradeRefundPersonalIndex", "path": "personal", "pathSegments": ["WXGLY", "training", "trade", "refund", "personal"], "component": "@/packages/routers/src/basic-router/training/trade/refund/personal/index.vue", "meta": {"isMenu": true, "title": "个人报名退款订单", "sort": 1, "icon": "icon_men<PERSON><PERSON>xigua<PERSON><PERSON>", "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB", "ZTGLY"], "permissionMap": {"query": {"roles": ["WXGLY", "FXS", "GYS"], "name": "查询", "ownerGroup": ["WXGLY.training.trade.refund.personal.query"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeIssueConfigListInDistributor:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getDistributorPortalInfosInSupplier:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageDistributorInSupplier:{\"authorizationRequired\":true}"], "ext": {"diffSchool": ["jxgx"]}}, "queryfx": {"name": "查询分销", "ownerGroup": ["WXGLY.training.trade.refund.personal.queryfx"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.listMyDistributorCommoditySkuPropertyInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pageMyDistributorCommodityInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.pagePromotionPortalInfoInDistributor:{\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.getPortalPricingCount:{\"authorizationRequired\":true}"], "roles": ["NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "queryzt": {"name": "查询专题", "ownerGroup": ["WXGLY.training.trade.refund.personal.queryzt"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInTrainingChannel:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "batchAgreeRefund": {"name": "批量同意退款", "ownerGroup": ["WXGLY.training.trade.refund.personal.batchAgreeRefund"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.agreeReturnBatchApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "batchConfirmRefund": {"name": "批量确认退款", "ownerGroup": ["WXGLY.training.trade.refund.personal.batchConfirmRefund"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.confirmBatchRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "export": {"name": "导出", "ownerGroup": ["WXGLY.training.trade.refund.personal.export"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportReturnOrderExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS"], "ext": {"diffSchool": ["jxgx"]}}, "exportfx": {"name": "导出（分销）", "ownerGroup": ["WXGLY.training.trade.refund.personal.exportfx"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportReturnOrderExcelInDistributor:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "exportzt": {"name": "导出（专题）", "ownerGroup": ["WXGLY.training.trade.refund.personal.exportzt"], "graphql": ["jxjy-data-export-gateway-backstage.query.exportReturnOrderExcelInTrainingChannel:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "refundDetail": {"name": "退款详情(入口控制)", "ownerGroup": ["WXGLY.training.trade.refund.personal.refundDetail"], "graphql": [], "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB", "ZTGLY"], "ext": {"diffSchool": ["jxgx"]}}, "refundApproval": {"name": "退款审批", "ownerGroup": ["WXGLY.training.trade.refund.personal.refundApproval"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInDistributor:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.statisticReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReturnOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-return-order-v1.mutation.sellerCancelReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.rejectReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.agreeReturnApply:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}", "ms-return-order-v1.mutation.confirmRefund:{\"serviceName\":\"ms-return-order-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}, "editInvoicePopup": {"name": "选择收款账号", "ownerGroup": ["WXGLY.training.trade.refund.personal.editInvoicePopup"], "graphql": ["ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageReceiveAccountInDistribution:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "roles": ["WXGLY", "FXS", "GYS", "NZFXS", "NZFXSJCB"], "ext": {"diffSchool": ["jxgx"]}}}, "ownerGroup": ["WXGLY.training.trade.refund.personal"], "diffSchool": ["jxgx"]}}]}]}, {"name": "training-user", "specifier": "TrainingUser", "path": "user", "pathSegments": ["WXGLY", "training", "user"], "component": "@/packages/routers/src/basic-router/training/user.vue", "meta": {"isMenu": true, "title": "用户管理", "sort": 5, "icon": "icon-guan<PERSON><PERSON>", "roles": ["WXGLY"], "permissionMap": {}, "ownerGroup": ["WXGLY.training.user"], "diffSchool": ["jxgx"]}, "children": [{"name": "training-user-student-index", "specifier": "TrainingUserStudentIndex", "path": "student", "pathSegments": ["WXGLY", "training", "user", "student"], "component": "@/packages/routers/src/basic-router/training/user/student/index.vue", "meta": {"isMenu": true, "title": "学员管理", "sort": 1, "icon": "icon_guanli", "roles": ["WXGLY"], "permissionMap": {"query": {"roles": [], "name": "查询", "ownerGroup": ["WXGLY.training.user.student.query"], "graphql": ["ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}"], "ext": {"diffSchool": ["jxgx"]}}, "participantsManagement": {"name": "查询", "ownerGroup": ["WXGLY.training.user.student.participantsManagement"], "graphql": ["ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInServicer:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.getOrderInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}, "queryTask": {"name": "导出任务查看", "ownerGroup": ["WXGLY.training.user.student.queryTask"], "graphql": ["jxgx-data-export-gateway-backstage.query.exportStudentExcelInServicer:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageAdminInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.query.findTaskExecuteResponsePage:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "fxnl-data-export-gateway-backstage.query.pageExportTaskInfoInMyself:{\"authorizationRequired\":true}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}, "queryTaskList": {"name": "任务类型查询", "ownerGroup": ["WXGLY.training.user.student.queryTaskList"], "graphql": ["jxjy-data-export-gateway-backstage.query.listExportTaskGroupInfoInServicer:{\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "queryTaskListZt": {"name": "任务类型查询-专题管理员", "ownerGroup": ["WXGLY.training.user.student.queryTaskListZt"], "graphql": ["jxjy-data-export-gateway-backstage.query.listExportTaskGroupInfoInTrainingChannel:{\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "location": {"name": "下载导出数据", "ownerGroup": ["WXGLY.training.user.student.location"], "graphql": [], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "downErrorData": {"name": "下载失败数据", "ownerGroup": ["WXGLY.training.user.student.downErrorData"], "graphql": ["platform-certificate-v1.mutation.exportCertificateFailedData:{\"authorizationRequired\":true}"], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "viewLog": {"name": "查看日志", "ownerGroup": ["WXGLY.training.user.student.viewLog"], "graphql": [], "roles": [], "ext": {"diffSchool": ["jxgx"]}}, "detali": {"name": "详情", "ownerGroup": ["WXGLY.training.user.student.detali"], "graphql": [], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}}, "ownerGroup": ["WXGLY.training.user.student"], "diffSchool": ["jxgx"]}}]}, {"name": "training-batch-print-index", "specifier": "TrainingBatchPrintIndex", "path": "batch-print", "pathSegments": ["WXGLY", "training", "batch-print"], "component": "@/packages/routers/src/basic-router/training/batch-print/index.vue", "meta": {"isMenu": true, "title": "批量打印证明", "sort": 10, "icon": "icon-p<PERSON><PERSON><PERSON><PERSON>", "roles": ["WXGLY"], "permissionMap": {"query": {"roles": [], "name": "查询", "ownerGroup": ["WXGLY.training.batch-print.query"], "graphql": ["ms-servicer-series-v1.query.getYears:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listAllRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.getServiceOrIndustryRegion:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-servicer-series-v1.query.getIndustries:{\"serviceName\":\"ms-servicer-series-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listAllIndustryProperty:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyChildByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.listBusinessDataDictionaryInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listALLIndustryPropertyRootByCategoryV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-learningscheme-v1.query.learningSchemeProcessTransactionStepQuery:{\"serviceName\":\"ms-learningscheme-v1\",\"authorizationRequired\":true}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}", "fxnl-query-front-gateway-backstage.query.judgeCommodityAuthorized:{\"authorizationRequired\":true}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageSchemeConfigInServicer:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-autolearning-v1.mutation.validLearningSchemeExistAutoLearning:{\"serviceName\":\"ms-autolearning-v1\",\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryPropertyByOnlineSchoolV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-basicdata-query-front-gateway-backstage.query.getRegionInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-trade-query-front-gateway-TradeQueryBackstage.query.pageIssueCommoditySkuInServicer:{\"serviceName\":\"ms-trade-query-front-gateway-v1\",\"authorizationRequired\":true}"], "ext": {"diffSchool": ["jxgx"]}}, "batchPrintCertify": {"name": "查询批量打印证明", "ownerGroup": ["WXGLY.training.batch-print.batchPrintCertify"], "graphql": ["platform-jxjypxtypt-jxgx-certificate.mutation.singlePrintCertificate:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.chooseTemplatePrintCertificate:{\"authorizationRequired\":true}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}, "schemeQuery": {"name": "方案回调请求", "ownerGroup": ["WXGLY.training.batch-print.schemeQuery"], "graphql": ["ms-certificate-v1.query.findCertificateTemplate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":false}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}, "batchPrint": {"name": "批量打印", "ownerGroup": ["WXGLY.training.batch-print.batchPrint"], "graphql": [], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}, "importListPrinting": {"name": "导入名单打印", "ownerGroup": ["WXGLY.training.batch-print.importListPrinting"], "graphql": ["platform-certificate-v1.mutation.studentBatchPrintCertificates:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.batchPrintCertificates:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageCertificateTemplate:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.mutation.printCertificateTemplate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.query.findCertificateTemplate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":false}", "platform-certificate-v1.mutation.learnerImportBatchPrintCertificates:{\"authorizationRequired\":true}", "ms-certificate-v1.query.queryExportTaskResponsePage:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.checkImportPrintListTheUploadProgress:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.findUploadSuccessFailureQuantity:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.returnImportedData:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.batchImportStudentList:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.printTheListRemoveAllStudent:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.printTheListRemoveStudent:{\"authorizationRequired\":true}", "platform-certificate-v1.mutation.exportFailedData:{\"authorizationRequired\":true}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}, "preview": {"name": "打印", "ownerGroup": ["WXGLY.training.batch-print.preview"], "graphql": ["platform-jxjypxtypt-jxgx-certificate.mutation.singlePrintCertificate:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}, "downLoad": {"name": "下载", "ownerGroup": ["WXGLY.training.batch-print.downLoad"], "graphql": ["platform-jxjypxtypt-jxgx-certificate.mutation.singlePrintCertificate:{\"serviceName\":\"tomcat-jxjytyptcyh\",\"serviceType\":\"diff-platform\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.chooseTemplatePrintCertificate:{\"authorizationRequired\":true}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}, "studentBatchPrint": {"name": "学员批量打印证明", "ownerGroup": ["WXGLY.training.batch-print.studentBatchPrint"], "graphql": ["platform-certificate-v1.mutation.batchPrintCertificates:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.studentBatchPrintCertificates:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryForestage.query.listIndustryInfoV2:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":false}", "ms-scheme-learning-query-front-gateway-SchemeLearningQueryBackstage.query.pageCertificateTemplate:{\"serviceName\":\"ms-scheme-learning-query-front-gateway-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.mutation.printCertificateTemplate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":true}", "ms-certificate-v1.query.findCertificateTemplate:{\"serviceName\":\"ms-certificate-v1\",\"authorizationRequired\":false}", "platform-certificate-v1.mutation.learnerImportBatchPrintCertificates:{\"authorizationRequired\":true}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}, "axPrintType": {"name": "安溪批量打印证明", "ownerGroup": ["WXGLY.training.batch-print.axPrintType"], "graphql": ["platform-certificate-v1.mutation.studentBatchPrintCertificates:{\"authorizationRequired\":true}", "ms-basicdata-query-front-gateway-BasicDataQueryBackstage.query.pageStudentInfoInSubProject:{\"serviceName\":\"ms-basicdata-query-front-gateway-v1\",\"authorizationRequired\":true}", "platform-certificate-v1.mutation.batchPrintCertificates:{\"authorizationRequired\":true}"], "roles": ["WXGLY"], "ext": {"diffSchool": ["jxgx"]}}}, "ownerGroup": ["WXGLY.training.batch-print"], "diffSchool": ["jxgx"]}}]}]}]