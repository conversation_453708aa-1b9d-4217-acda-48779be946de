/**
 * 用户域查询培训证明
 */
class QueryCustomerTrainingCertificateList {
  /**
   * @description: 3. 查询培训证书
   * @param {LearningArchivesDto}
   * @return {*}
   */
  queryTrainingCertifications() {
    return
  }

  /**
   * @description: 1.获取培训证书是否生成
   * @param {string} issueId 期别id
   * @return {*}
   */
  private createdTrainingCertificateValidate(issueId: string): boolean {
    return true
  }

  /**
   * @description: 2.获取请求资源配置
   * @param {*}
   * @return {*}
   */
  private getTrainingCertificateConfig() {
    return
  }
}

export default QueryCustomerTrainingCertificateList
