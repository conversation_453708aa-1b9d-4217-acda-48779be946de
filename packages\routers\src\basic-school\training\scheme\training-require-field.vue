<route-meta>
{
"isMenu": true,
"title": "培训属性值管理",
"sort": 3
}
</route-meta>
<script lang="ts">
  import SchemeTrainingRequireField from '@hbfe/jxjy-admin-scheme/src/training-require-field.vue'
  import { RoleTypeDecorator } from '@/models/RoleTypeDecorator'
  import { WXGLY } from '@/models/RoleTypes'
  @RoleTypeDecorator({
    query: [WXGLY],
    modify: [WXGLY],
    queryList: [WXGLY]
  })
  export default class extends SchemeTrainingRequireField {}
</script>
