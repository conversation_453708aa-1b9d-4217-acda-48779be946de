import store from '@api/store'
import { VuexModule, Module, getModule } from 'vuex-module-decorators'
import QueryTrainingCertificateFactory from '@api/service/customer/personal-leaning/QueryTrainingCertificateFactory'
import MutationTrainingCertificateFactory from './MutationTrainingCertificateFactory'
@Module({ namespaced: true, dynamic: true, name: 'TrainingCertificateModule', store })
class TrainingCertificateModule extends VuexModule {
  /* 
    培训成果查询工厂
  */
  get queryTrainingCertificateFactory() {
    return QueryTrainingCertificateFactory
  }

  /* 
    培训成果业务工厂
  */
  get mutationTrainingCertificateFactory() {
    return MutationTrainingCertificateFactory
  }
}

export default getModule(TrainingCertificateModule)
