<route-meta>
  {
  "isMenu": true,
  "title": "培训单位销售统计",
  "sort": 7,
  "icon": "icon-ribaotongji"
  }
  </route-meta>
<template>
  <el-main v-if="$hasPermission('sellQuery')" desc="查询" actions="created,doSearch">
    <div class="f-p15">
      <el-card shadow="never" class="m-card f-mb15">
        <!--条件查询-->
        <!--屏幕分辨率 > 1680 的查询条件超过7个的，隐藏起来-->
        <!--屏幕分辨率 ≤ 1680 的查询条件超过5个的，隐藏起来-->
        <hb-search-wrapper @reset="resetPageQuery" class="m-query is-border-bottom">
          <el-form-item label="课件供应商">
            <biz-courseware-supplier v-model="provriderId" placeholder="全部"></biz-courseware-supplier>
          </el-form-item>
          <el-form-item label="查询时间">
            <el-date-picker
              v-model="pageQueryTime"
              type="datetimerange"
              value-format="yyyy-MM-dd hh:mm:ss"
              range-separator="至"
              start-placeholder="起始时间"
              end-placeholder="结束时间"
            >
            </el-date-picker>
          </el-form-item>
          <template slot="actions">
            <el-button type="primary" @click="doSearch">查询</el-button>
            <el-button
              @click="batchExport"
              v-if="$hasPermission('sellBatchexport')"
              desc="批量导出"
              actions="batchExport"
              >批量导出</el-button
            >
          </template>
        </hb-search-wrapper>
        <!--操作栏-->
        <div class="f-mt20">
          <el-alert type="warning" :closable="false" class="m-alert f-clear">
            <div class="f-c6 f-fl">
              搜索结果合计：累计选课人次：<span class="f-fb f-co">{{
                courseSalesStatisticsInfoTotal.sumChooseCourseCount
                  ? courseSalesStatisticsInfoTotal.sumChooseCourseCount
                  : 0
              }}</span
              >，累计退课人次：<span class="f-fb f-co">{{
                courseSalesStatisticsInfoTotal.totalWithdrawalCount
                  ? courseSalesStatisticsInfoTotal.totalWithdrawalCount
                  : 0
              }}</span
              >， 净选课人次：<span class="f-fb f-co">{{
                courseSalesStatisticsInfoTotal.totalChooseCourseCount
                  ? courseSalesStatisticsInfoTotal.totalChooseCourseCount
                  : 0
              }}</span>
            </div>
            <div class="f-fr f-ml20 f-c6">单位：人次</div>
            <div class="f-fr f-csp f-flex f-align-center" @click="tipdialog = true">
              <i class="el-icon-info f-f16 f-mr5"></i>统计口径说明
            </div>
          </el-alert>
        </div>
        <!--表格-->
        <el-table
          stripe
          :data="courseSalesStatisticsTableList"
          border
          max-height="500px"
          v-loading="query.loading"
          ref="courseSalesTable"
          class="m-table is-statistical f-mt10"
        >
          <el-table-column type="index" label="No." width="60" align="center" fixed="left"></el-table-column>
          <el-table-column label="课件供应商" prop="field01" min-width="200" fixed="left">
            <template slot-scope="scope">{{ scope.row.providerName }}</template>
          </el-table-column>
          <el-table-column label="累计选课人次" min-width="90" align="right">
            <template slot-scope="scope">{{ scope.row.sumChooseCourseCount }}</template>
          </el-table-column>
          <el-table-column label="累计退课人次" min-width="90" align="right">
            <template slot-scope="scope">{{ scope.row.totalWithdrawalCount }}</template>
          </el-table-column>
          <el-table-column label="净选课人次" min-width="90" align="right">
            <template slot-scope="scope">{{ scope.row.totalChooseCourseCount }}</template>
          </el-table-column>
          <el-table-column label="操作" width="120" align="center" fixed="right">
            <template slot-scope="scope">
              <el-button
                v-if="$hasPermission('details')"
                desc="查看详情"
                type="text"
                size="mini"
                @click="courseSelectionStatistic(scope.row.coursewareSupplierId)"
                >查看详情</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <hb-pagination :page="page" v-bind="page"></hb-pagination>
      </el-card>
    </div>
    <!-- 统计口径说明弹窗 -->
    <el-drawer title="统计口径说明" :visible.sync="tipdialog" size="900px" custom-class="m-drawer">
      <div class="drawer-bd">
        <el-alert type="warning" show-icon :closable="false" class="m-alert">
          注：统计开通查询的是以日为单位的数据的实时报表，查询截止日期可选范围为当前时间！
        </el-alert>
        <p class="f-mt20 f-mb10">
          <span class="f-fb f-f15">搜索条件说明</span>
          （以下各搜索条件若都填写相关数据，则查询的是满足这几项搜索条件的数据才会查出来，即是且的关系）
        </p>
        <el-table stripe :data="tableData" border class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="字段" width="150">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">课件供应商</div>
              <div v-else>查询时间</div>
            </template>
          </el-table-column>
          <el-table-column label="详细说明" min-width="300">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">选择已创建的课件供应商信息</div>
              <div v-else>查看在某个选课成功的时间内，培训单位的选课统计</div>
            </template>
          </el-table-column>
        </el-table>
        <p class="f-mt20 f-mb10">
          <span class="f-fb f-f15">列表字段及详细说明</span>
          （列表下的数据显示受搜索条件的约束，统计单位：人次）
        </p>
        <el-table stripe :data="tableData4" border class="m-table">
          <el-table-column type="index" label="No." width="60" align="center"></el-table-column>
          <el-table-column label="字段" width="150">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">课件供应商</div>
              <div v-else-if="scope.$index === 1">累计选课人次</div>
              <div v-else-if="scope.$index === 2">累计退课人次</div>
              <div v-else>净选课人次</div>
            </template>
          </el-table-column>
          <el-table-column label="详细说明" min-width="300">
            <template slot-scope="scope">
              <div v-if="scope.$index === 0">培训方案名称，默认显示全部已发布的方案</div>
              <div v-else-if="scope.$index === 1">在查询日期段内，选课成功的累积人次</div>
              <div v-else-if="scope.$index === 2">在查询日期段内，选课成功的累积人次</div>
              <div v-else>净选课=累积选课人次-累计退课人次</div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-drawer>
    <!-- 导出任务查看 -->
    <el-dialog title="提示" :visible.sync="exportSuccessVisible" width="450px" class="m-dialog">
      <div class="dialog-alert is-big">
        <i class="icon el-icon-success success"></i>
        <div class="txt">
          <p class="f-fb">导出成功，是否前往下载数据？</p>
          <p class="f-f13 f-mt5">下载入口：数据统计-导出任务查看，查询条件选择培训单位销售统计！</p>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="exportSuccessVisible = false">暂 不</el-button>
        <el-button type="primary" @click="goDownloadPage">前往下载</el-button>
      </div>
    </el-dialog>
  </el-main>
</template>
<script lang="ts">
  import { Component, Watch, Vue } from 'vue-property-decorator'
  import { UiPage, Query } from '@hbfe/common'
  import {
    CourseSalesStatisticsInfoTotal,
    CourseSalesStatisticsRequest
  } from '@api/ms-gateway/ms-course-learning-query-front-gateway-CourseLearningBackstage'
  import ChooseCourseStatisticDetail from '@api/service/management/statisticalReport/query/vo/ChooseCourseStatisticDetail'
  import StaticticalReportManagerModule from '@api/service/management/statisticalReport/StaticticalReportManagerModule'

  @Component
  export default class extends Vue {
    getQueryChooseCourseStatisticList = StaticticalReportManagerModule.queryStaticticalReportFactory.getQueryTrainingUnitSalesStatistics()

    // 分页
    page = new UiPage()

    query: Query = new Query()
    // 查询条件
    pageQueryParam = new CourseSalesStatisticsRequest()
    // * 培训单位销售总数统计
    courseSalesStatisticsInfoTotal = new CourseSalesStatisticsInfoTotal()
    // 课件供应商列表
    provriderId: Array<string> = new Array<string>()
    pageQueryTime = [] as any
    // 列表
    courseSalesStatisticsTableList: Array<ChooseCourseStatisticDetail> = new Array<ChooseCourseStatisticDetail>()

    // 说明弹窗
    tipdialog = false
    tableData = [{ field101: '1' }, { field101: '2' }]
    tableData4 = [{ field101: '1' }, { field101: '2' }, { field101: '3' }, { field101: '4' }]
    //导出任务弹窗
    exportSuccessVisible = false
    constructor() {
      super()
      this.page = new UiPage(this.doSearch, this.doSearch)
    }

    created() {
      this.doSearch()
    }

    async batchExport() {
      //
      const res = await this.getQueryChooseCourseStatisticList.exportTrainingUnitSalesStatisticsLait(
        this.pageQueryParam
      )
      if (res) {
        this.exportSuccessVisible = true
      }
    }

    @Watch('provriderId')
    changeProvriderId() {
      if (this.provriderId?.length) {
        this.pageQueryParam.supplierId = [this.provriderId[this.provriderId.length - 1]]
      } else {
        this.pageQueryParam.supplierId = []
      }
    }

    @Watch('pageQueryTime')
    changePageQueryTime() {
      if (this.pageQueryTime?.length) {
        // 开始时间
        this.pageQueryParam.chooseCourseDateStart = this.pageQueryTime[0]
        // 结束时间
        this.pageQueryParam.chooseCourseDateEnd = this.pageQueryTime[1]
      } else {
        // 开始时间
        this.pageQueryParam.chooseCourseDateStart = ''
        // 结束时间
        this.pageQueryParam.chooseCourseDateEnd = ''
      }
    }
    // 获取table
    async doSearch() {
      this.query.loading = true
      try {
        if (this.pageQueryTime?.length) {
          // 开始时间
          this.pageQueryParam.chooseCourseDateStart = this.pageQueryTime[0]
          // 结束时间
          this.pageQueryParam.chooseCourseDateEnd = this.pageQueryTime[1]
        } else {
          // 开始时间
          this.pageQueryParam.chooseCourseDateStart = ''
          // 结束时间
          this.pageQueryParam.chooseCourseDateEnd = ''
        }
        if (this.provriderId?.length) {
          this.pageQueryParam.supplierId = [this.provriderId[this.provriderId.length - 1]]
        } else {
          this.pageQueryParam.supplierId = []
        }
        this.courseSalesStatisticsTableList = await this.getQueryChooseCourseStatisticList.queryTrainingUnitSalesStatisticsLait(
          this.page,
          this.pageQueryParam
        )
        this.courseSalesStatisticsInfoTotal = this.getQueryChooseCourseStatisticList.courseSalesStatisticsInfoTotal
        //console.log(this.courseSalesStatisticsTableList, '11111111111111111111')
      } catch (e) {
        //console.log(e)
        // nothing
      } finally {
        //处理切换页数后行数错位问题
        ;(this.$refs['courseSalesTable'] as any)?.doLayout()
        this.query.loading = false
      }
    }
    /**
     * 跳转到选课统计
     */
    courseSelectionStatistic(id: any) {
      this.$router.push({
        path: '/statistic/course-selection-statistic',
        query: { coursewareSupplierId: id }
      })
    }
    /**
     * 重置查询条件
     */
    async resetPageQuery() {
      this.provriderId = []
      this.pageQueryTime = []
      await this.doSearch()
    }
    /**
     * 导出任务下载
     */
    goDownloadPage() {
      this.exportSuccessVisible = false
      this.$router.push({
        path: '/training/task/exporttask',
        query: { type: 'exportUnitSalesStatistical' }
      })
    }
  }
</script>
