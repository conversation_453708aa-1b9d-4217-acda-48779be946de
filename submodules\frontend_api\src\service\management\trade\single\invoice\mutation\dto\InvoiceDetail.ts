/*
 * @Description: 更新发票请求参数
 * @Version: feature/*******.0
 * @Autor: <PERSON>
 * @Date: 2022-04-01 14:31:28
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-13 09:52:24
 */

import { UpdateElectronicInvoiceRequest } from '@api/ms-gateway/ms-bill-v1'
import { TitleTypeEnum } from '../../enum/InvoiceEnum'

export default class InvoiceDetail {
  /**
   * 发票编号
   */
  invoiceId?: string
  /**
   * 发票抬头，null表示不更新
   */
  title?: string
  /**
     * 发票抬头类型，null表示不更新
  <pre>
  发票抬头类型  PERSONAL：个人  UNIT：企业
  </pre>
     */
  titleType?: TitleTypeEnum
  /**
   * 购买方纳税人识别号，null表示不更新
   */
  taxpayerNo?: string
  /**
   * 购买方地址，null表示不更新
   */
  address?: string
  /**
   * 购买方电话号码，null表示不更新
   */
  rePhone?: string
  /**
   * 购买方开户行名称，null表示不更新
   */
  bankName?: string
  /**
   * 购买方银行账户，null表示不更新
   */
  account?: string
  /**
   * 购买方电子邮箱，null表示不更新
   */
  email?: string
  /**
   * 发票票面备注，null表示不更新
   */
  remark?: string
  /**
   * 用户电话号码
   */
  phone?: string
  static to(invoiceDetail: InvoiceDetail) {
    const updateElectronicInvoiceRequest = new UpdateElectronicInvoiceRequest()
    updateElectronicInvoiceRequest.invoiceId = invoiceDetail.invoiceId
    updateElectronicInvoiceRequest.title = invoiceDetail.title
    updateElectronicInvoiceRequest.titleType = invoiceDetail.titleType
    updateElectronicInvoiceRequest.taxpayerNo = invoiceDetail.taxpayerNo
    updateElectronicInvoiceRequest.address = invoiceDetail.address
    updateElectronicInvoiceRequest.bankName = invoiceDetail.bankName
    updateElectronicInvoiceRequest.phone = invoiceDetail.rePhone
    updateElectronicInvoiceRequest.account = invoiceDetail.account
    updateElectronicInvoiceRequest.email = invoiceDetail.email
    updateElectronicInvoiceRequest.remark = invoiceDetail.remark
    return updateElectronicInvoiceRequest
  }
}
