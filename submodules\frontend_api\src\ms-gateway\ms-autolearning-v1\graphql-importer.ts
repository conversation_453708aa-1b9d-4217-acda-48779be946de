import batchTerminateAutoLearning from './mutates/batchTerminateAutoLearning.graphql'
import queryLastTimeExpectationStartStudyDate from './mutates/queryLastTimeExpectationStartStudyDate.graphql'
import rearrangementAutoLearning from './mutates/rearrangementAutoLearning.graphql'
import restartAutoLearningTask from './mutates/restartAutoLearningTask.graphql'
import updateOnlineSchoolSmartLearningServiceConfig from './mutates/updateOnlineSchoolSmartLearningServiceConfig.graphql'
import validLearningSchemeExistAutoLearning from './mutates/validLearningSchemeExistAutoLearning.graphql'

export {
  batchTerminateAutoLearning,
  queryLastTimeExpectationStartStudyDate,
  rearrangementAutoLearning,
  restartAutoLearningTask,
  updateOnlineSchoolSmartLearningServiceConfig,
  validLearningSchemeExistAutoLearning
}
