import { GetTrainSupervisionForAHZJRequest } from '@api/platform-gateway/platform-jxjypxtypt-ahzj-school'

export default class AntiCheatInitModel {
  /**
   * 课程id
   */
  courseId: string = undefined

  /**
   * 课程名称
   */
  courseName: string = undefined

  /**
   * 初始化播放时长
   */
  initPlayTime: number = undefined

  /**
   * 总课程时长
   */
  courseTimeLength: number = undefined

  /**
   * 用户id
   */
  userId: string = undefined

  /**
   * 订单号
   */
  orderId: string = undefined

  /**
   * 子订单号
   */
  subOrderNo: string = undefined

  /**
   * 进度百分比
   */
  schedule: number = undefined

  to() {
    const request = new GetTrainSupervisionForAHZJRequest()
    request.courseId = this.courseId
    request.courseName = this.courseName
    request.userId = this.userId
    request.orderId = this.orderId
    request.subOrderNo = this.subOrderNo
    return request
  }
}
